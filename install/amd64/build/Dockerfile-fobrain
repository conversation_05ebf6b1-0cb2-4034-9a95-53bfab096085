FROM --platform=linux/amd64 rockylinux:8

EXPOSE 8090
WORKDIR /data
VOLUME ["/data/storage"]
RUN sed -i 's|^mirrorlist=|#mirrorlist=|g' /etc/yum.repos.d/Rocky-AppStream.repo && \
    sed -i 's|^#baseurl=http://dl.rockylinux.org/$contentdir|baseurl=https://mirrors.aliyun.com/rockylinux|g' /etc/yum.repos.d/Rocky-AppStream.repo && \
    sed -i 's|^mirrorlist=|#mirrorlist=|g' /etc/yum.repos.d/Rocky-BaseOS.repo && \
    sed -i 's|^#baseurl=http://dl.rockylinux.org/$contentdir|baseurl=https://mirrors.aliyun.com/rockylinux|g' /etc/yum.repos.d/Rocky-BaseOS.repo
RUN dnf install -y epel-release && \
    dnf install -y \
      bash which xz tar gzip curl vim-minimal procps-ng net-tools iproute less \
      libstdc++ glibc openssl3 openssl3-libs && \
    dnf clean all

RUN curl -LO https://downloads.mysql.com/archives/get/p/23/file/mysql-8.0.37-linux-glibc2.28-x86_64.tar.xz && \
    tar -xf mysql-8.0.37-linux-glibc2.28-x86_64.tar.xz && \
    cp mysql-8.0.37-linux-glibc2.28-x86_64/bin/mysqlbinlog \
       mysql-8.0.37-linux-glibc2.28-x86_64/bin/mysql \
       mysql-8.0.37-linux-glibc2.28-x86_64/bin/mysqldump \
       mysql-8.0.37-linux-glibc2.28-x86_64/bin/mysqladmin /usr/local/bin/ && \
    chmod +x /usr/local/bin/{mysqlbinlog,mysql,mysqldump,mysqladmin} && \
    rm -rf mysql-8.0.37-linux-glibc2.28-x86_64*

RUN mysqlbinlog --version && mysql --version && mysqldump --version && mysqladmin --version

ENV LANG=en_US.UTF-8

COPY ./fobrain/cmd /usr/sbin/cmd
COPY ./fobrain/fobrain /usr/sbin/fobrain
COPY ./fobrain/fobrain.json /etc/fobrain/conf/config.json
COPY ./fobrain/version /etc/fobrain/conf/version

ENTRYPOINT ["/bin/bash", "-c", "/usr/sbin/fobrain"]
CMD []