{"common": {"env": "develope", "log_level": "info", "listen": ":8090", "network": "en0", "root_storage": "/data/fobrain/storage", "encryption_key": "5e884898da28047151d0e56f8dc6292773603d0d6aabbdd62a11ef721d1542d8", "binlog_base_path": "/data/fobrain/storage/data/mysql", "repo_base_path": "/usr/share/elasticsearch/data/snapshot"}, "elastic": {"address": "127.0.0.1", "port": 9200, "username": "", "password": "", "sniff": false}, "logger": {"output_console": true, "output_file": true, "file_name": "/data/fobrain/storage/logs/logs.log", "file_name_fobrain": "/data/fobrain/storage/logs/fobrain-service.log", "file_name_scheduler": "/data/fobrain/storage/logs/scheduler-logs.log", "file_name_sync": "/data/fobrain/storage/logs/sync-logs.log", "file_name_cascade": "/data/fobrain/storage/logs/cascade-logs.log", "file_name_backup": "/data/fobrain/storage/logs/backup-logs.log", "level": "warn", "max_size": 8, "max_age": 30, "local_time": true, "compress": true, "max_backups": 5}, "merge_logger": {"output_console": true, "output_file": true, "file_name_service": "/data/fobrain/storage/logs/merge_service.log", "file_name_device": "/data/fobrain/storage/logs/merge_device.log", "file_name_asset": "/data/fobrain/storage/logs/merge_asset.log", "file_name_vuln": "/data/fobrain/storage/logs/merge_vuln.log", "file_name_person": "/data/fobrain/storage/logs/merge_person.log", "level": "warn", "max_size": 10, "max_age": 30, "local_time": true, "compress": false, "max_backups": 20}, "mysql": {"address": "127.0.0.1", "port": 3306, "username": "root", "password": "Fobrain@@#13244%!", "database": "fobrain", "charset": "utf8mb4", "log-level": "debug", "slow-time": 15}, "queue": {"device_merge_queue": "device_merge_queue_dev", "device_merge_concurrent": 20, "asset_merge_queue": "asset_merge_queue_dev", "asset_merge_concurrent": 20, "vuln_merge_queue": "vuln_merge_queue_dev", "vuln_merge_concurrent": 10, "vuln_update_queue": "vuln_update_queue_dev", "vuln_update_concurrent": 10, "person_merge_queue": "person_merge_queue_dev", "person_merge_concurrent": 10, "del_msg_batch": 500, "end_delay": 180, "log_merge_cost": false}, "redis": {"address": "127.0.0.1", "port": 6379, "password": "Fobrain@7391%", "database": 0}, "consul": {"address": "127.0.0.1", "port": 8500, "prefix": "", "token": "5ef1336c-c13b-34d2-a91e-0bcd3c4c1240"}, "source_sync": {"aliyun_cloud_size": 50, "aliyun_cloud_time": 3, "changting_waf_size": 50, "changting_waf_time": 1, "mach_lake_time": 1, "qizhi_uaudithost_size": 50, "qizhi_uaudithost_time": 1, "weibu_size": 50, "weibu_time": 1}}