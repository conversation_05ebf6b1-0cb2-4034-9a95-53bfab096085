[supervisord]
nodaemon=true

[group:fobrain]
programs=nginx,update,fobrain

[program:nginx]
process_name = fobrain_nginx
numprocs=1
user=root
command=/usr/sbin/nginx
autostart=true
autorestart=true
startsecs=10
stdout_logfile=/data/storage/logs/supervisor/nginx_stdout.log
stderr_logfile=/data/storage/logs/supervisor/nginx_stderr.log
stdout_logfile_maxbytes=0
stderr_logfile_maxbytes=0
stdout_logfile_backups=0
stderr_logfile_backups=0
stopsignal=QUIT
stopasgroup=true

[program:update]
process_name = fobrain_update
numprocs=1
user=root
command=sh -c 'while true; do if pgrep -x "dockerUpdate" > /dev/null; then echo "dockerUpdate is running"; else /usr/sbin/dockerUpdate eth0 0 /data/storage/update; fi; sleep 3; done'
autostart=true
autorestart=true
startsecs=3
stdout_logfile=/data/storage/logs/supervisor/fobrain_update_stdout.log
stderr_logfile=/data/storage/logs/supervisor/fobrain_update_stderr.log
stdout_logfile_maxbytes=0
stderr_logfile_maxbytes=0
stdout_logfile_backups=0
stderr_logfile_backups=0
stopsignal=QUIT
stopasgroup=true

[program:fobrain]
process_name = fobrain
numprocs=1
user=root
command=/usr/sbin/fobrain -c /etc/fobrain/fobrain/fobrain.conf server
autostart=true
autorestart=true
startsecs=10
stdout_logfile=/data/storage/logs/supervisor/fobrain_stdout.log
stderr_logfile=/data/storage/logs/supervisor/fobrain_stderr.log
stdout_logfile_maxbytes=0
stderr_logfile_maxbytes=0
stdout_logfile_backups=0
stderr_logfile_backups=0
stopsignal=QUIT
stopasgroup=true