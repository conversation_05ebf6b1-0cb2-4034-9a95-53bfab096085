#!/bin/bash

# 定义颜色代码
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# 创建日志文件
LOG_FILE="upgrade_$(date +%Y%m%d_%H%M%S).log"

# 定义日志函数
log_message() {
    local level=$1
    local message=$2
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    # 根据日志级别选择颜色
    case $level in
        "INFO")  color=$GREEN ;;
        "WARN")  color=$YELLOW ;;
        "ERROR") color=$RED ;;
        "DEBUG") color=$BLUE ;;
        *)       color=$NC ;;
    esac
    
    # 输出到控制台（带颜色）
    echo -e "${color}[${timestamp}] [${level}] ${message}${NC}"
    # 输出到日志文件（不带颜色）
    echo "[${timestamp}] [${level}] ${message}" >> "${LOG_FILE}"
}

log_message "INFO" "===========开始执行 fileboy_script.sh==========="
log_message "INFO" "--进入fileboy_script.sh,目录为 $1"

if [[ "$1" == "flag-dir/upload_finish" ]]; then
    if [[ -f "./exec-dir/system_upgrade.sh" ]]; then
        log_message "INFO" "开始执行system_upgrade.sh $1"
        chmod +x exec-dir/system_upgrade.sh
        
        # 执行升级脚本
        if sh ./exec-dir/system_upgrade.sh >> "${LOG_FILE}" 2>&1; then
            log_message "INFO" "system_upgrade.sh执行成功 $1"
        else
            log_message "ERROR" "system_upgrade.sh执行失败 $1"
        fi
        
        sleep 2
        
        TIMESTAMP=$(date +"%Y%m%d%H%M%S")
        log_message "DEBUG" "重命名 pkg 文件夹 $1"
        if mv "./exec-dir" "./${TIMESTAMP}-exec-dir"; then
            log_message "INFO" "exec-dir文件夹重命名成功"
        else
            log_message "ERROR" "exec-dir文件夹重命名失败"
        fi
        
        log_message "INFO" "===========fileboy_script.sh执行完成==========="
    fi
else
    log_message "WARN" "--非预期目录，跳过 $1"
fi
sync