
!Software Version V600R007C20SPC300
!Last configuration was saved at 2024-05-22 07:27:29 UTC
#
sysname USG6300E
#
 l2tp domain suffix-separator @
#
ipv6
#
vlan batch 10 20 30 40 50 60 666
#
authentication-profile name portal_authen_default
#
 undo factory-configuration prohibit
#
undo telnet ipv6 server enable
#
clock timezone Beijing add 08:00:00
#
 firewall packet-filter basic-protocol enable
#
 update schedule location-sdb weekly Sun 02:23
#
 firewall defend action discard
#
 log type traffic enable
 log type syslog enable
 log type policy enable
 log type traffic-report enable
#
 undo dataflow enable
#
 undo sa force-detection enable
#
 configure disk type traffic-log 20
 configure disk type threat-log 3
 configure disk type bandwidth-log 10
 configure disk type bandwidth-report 4
#
 banner enable
#
 undo user-manage web-authentication enable
 user-manage web-authentication security port 8887
 undo privacy-statement english
 undo privacy-statement chinese
page-setting
 user-manage security version tlsv1.1 tlsv1.2
password-policy
 level high
#
 firewall blacklist enable
#
 undo firewall ipv6 statistics system enable
#
 nat64 enable
#
 firewall ids authentication type aes256
#
 web-manager security version tlsv1.1 tlsv1.2
 web-manager enable
 web-manager security enable
 web-manager timeout 60
 undo web-manager config-guide enable
#
firewall dataplane to manageplane application-apperceive default-action drop
#
dns proxy enable
#
dhcp enable
#
undo update schedule ips-sdb enable
update schedule ips-sdb daily 05:47
undo update schedule av-sdb enable
update schedule av-sdb daily 05:47
update schedule sa-sdb daily 05:47
update schedule ip-reputation daily 05:47
update schedule cnc daily 05:47
update schedule file-reputation daily 05:47
update schedule ext-url-sdb daily 05:47
#
 disk-usage alarm threshold 95 
#
ip vpn-instance default
 ipv4-family
#
ip address-set ************ type object
 address 0 ************ mask 32
#
ip address-set *********** type object
 address 0 *********** mask 23
#
ip address-set �칫����-172 type object
 address 0 *********** mask 23
 address 1 *********** mask 23
 address 2 *********** mask 23
 address 3 *********** mask 23
 address 4 *********** mask 23
 address 5 *********** mask 23
#
ip address-set *********** type object
 address 0 *********** mask 24
#
ip address-set **********96 type object
 address 0 **********96 mask 32
#
ip address-set ����**********/23 type object
 address 0 ********** mask *************
#
ip address-set ********** type object
 address 0 ********** mask 32
#
ip address-set *********** type object
 address 0 *********** mask 24
#
ip address-set ***********4 type object
 address 0 ***********4 mask 32
#
ip address-set *********** type object
 address 0 *********** mask 23
#
ip address-set *********** type object
 address 0 *********** mask 23
#
ip address-set ***********9 type object
 address 0 ***********9 mask 32
#
ip address-set *********** type object
 address 0 *********** mask 23
#
ip address-set ***********2 type object
 address 0 ***********2 mask 32
#
ip address-set *********** type object
 address 0 *********** mask 32
#
ip address-set ************* type object
 address 0 ************* mask 32
#
ip address-set ************ type object
 address 0 ************ mask 32
#
ip address-set *********** type object
 address 0 *********** mask 32
#
ip address-set *********** type object
 address 0 *********** mask 32
#
ip address-set *********** type object
 address 0 *********** mask 32
#
ip address-set ************ type object
 address 0 ************ mask 32
#
ip address-set **********9 type object
 address 0 **********9 mask 32
#
ip address-set ************ type object
 address 0 ************ mask 32
#
ip address-set **********1 type object
 address 0 **********1 mask 32
#
ip address-set *********** type object
 address 0 *********** mask 32
#
ip address-set *********** type object
 address 0 *********** mask 32
#
ip address-set *********** type object
 address 0 *********** mask 32
#
ip address-set ***********7 type object
 address 0 ***********7 mask 32
#
ip address-set ��ͨ20M type object
 address 0 ************* mask 32
#
ip address-set ************/24 type object
 address 0 ************ mask 24
#
ip address-set ************** type object
 address 0 ************** mask 32
#
ip address-set ************** type object
 address 0 ************** mask 32
#
ip address-set *********** type object
 address 0 *********** mask 32
#
ip address-set ************ type object
 address 0 ************ mask 32
#
ip address-set *********** type object
 address 0 *********** mask 32
#
ip address-set ************** type object
 address 0 ************** mask 32
#
ip address-set ************* type object
 address 0 ************* mask 32
#
ip address-set **********/16 type object
 address 0 ********** mask 16
#
ip address-set **********/16 type object
 address 0 ********** mask 16
#
ip address-set ************/24 type object
 address 0 ************ mask 24
#
ip address-set ************* type object
 address 0 ************* mask 32
#
ip address-set ************** type object
 address 0 ************** mask 32
#
ip address-set ************ type object
 address 0 ************ mask 32
#
ip address-set *************** type object
 address 0 *************** mask 32
#
ip address-set ************ type object
 address 0 ************ mask 32
#
ip address-set ************** type object
 address 0 ************** mask 32
#
ip address-set ***********0 type object
 address 0 ***********0 mask 32
#
ip address-set ************** type object
 address 0 ************** mask 32
#
ip address-set 106.37.174.62 type object
 address 0 106.37.174.62 mask 32
#
ip address-set 223.104.3.139 type object
 address 0 223.104.3.139 mask 32
#
ip address-set *************** type object
 address 0 *************** mask 32
#
ip address-set ************ type object
 address 0 ************ mask 32
#
ip address-set ************** type object
 address 0 ************** mask 32
#
ip address-set **********19 type object
 address 0 **********19 mask 32
#
ip address-set 117.136.38.34 type object
 address 0 117.136.38.34 mask 32
#
ip address-set **********87 type object
 address 0 **********87 mask 32
#
ip address-set 114.242.248.90 type object
 address 0 114.242.248.90 mask 32
#
ip address-set ************ type object
 address 0 ************ mask 32
 address 1 117.50.16.112 mask 32
 address 2 118.190.70.117 mask 32
#
ip address-set 106.75.9.98 type object
 address 0 106.75.9.98 mask 32
#
ip address-set 103.242.173.99 type object
 address 0 103.242.173.99 mask 32
#
ip address-set 106.75.109.221 type object
 address 0 106.75.109.221 mask 32
#
ip address-set *********** type object
 address 0 *********** mask 32
#
ip address-set 118.190.70.117 type object
 address 0 118.190.70.117 mask 32
#
ip address-set *********** type object
 address 0 *********** mask 32
#
ip address-set **********/16 type object
 address 0 ********** mask 16
#
ip address-set *********** type object
 address 0 *********** mask 32
#
ip address-set *************** type object
 address 0 *************** mask 32
#
ip address-set ************** type object
 address 0 ************** mask 32
#
ip address-set ************** type object
 address 0 ************** mask 32
#
ip address-set 122.97.179.22 type object
 address 0 122.97.179.22 mask 32
#
ip address-set ************** type object
 address 0 ************** mask 32
#
ip address-set ************* type object
 address 0 ************* mask 32
#
ip address-set ***********2 type object
 address 0 ***********2 mask 32
#
ip address-set ************* type object
 address 0 ************* mask 32
#
ip address-set *********** type object
 address 0 *********** mask 32
#
ip address-set *********** type object
 address 0 *********** mask 32
#
ip address-set ************** type object
 address 0 ************** mask 32
#
ip address-set **********97 type object
 address 0 **********97 mask 32
#
ip address-set ***********6 type object
 address 0 ***********6 mask 32
#
ip address-set ************* type object
 address 0 ************* mask 32
#
ip address-set 114.244.160.102 type object
 address 0 114.244.160.102 mask 32
#
ip address-set **********98 type object
 address 0 **********98 mask 32
#
ip address-set 10.10.11.164 type object
 address 0 10.10.11.164 mask 32
#
ip address-set 223.104.3.211 type object
 address 0 223.104.3.211 mask 32
#
ip address-set 122.115.232.111 type object
 address 0 122.115.232.111 mask 32
#
ip address-set 106.75.174.81 type object
 address 0 106.75.174.81 mask 32
#
ip address-set ************ type object
 address 0 ************ mask 32
#
ip address-set 192.168.100.0/24 type object
 address 0 192.168.100.0 mask 24
#
ip address-set **********6 type object
 address 0 **********6 mask 32
#
ip address-set 10.10.10.44 type object
 address 0 10.10.10.44 mask 32
#
ip address-set 10.10.9.0/24 type object
 address 0 10.10.9.0 mask 24
#
ip address-set 223.104.42.40 type object
 address 0 223.104.42.40 mask 32
#
ip address-set 10.10.11.61 type object
 address 0 10.10.11.61 mask 32
#
ip address-set 223.104.38.173 type object
 address 0 223.104.38.173 mask 32
#
ip address-set ***********6 type object
 address 0 ***********6 mask 32
#
ip address-set **********4 type object
 address 0 **********4 mask 32
#
ip address-set 10.10.10.22 type object
 address 0 10.10.10.22 mask 32
#
ip address-set **********78 type object
 address 0 **********78 mask 32
#
ip address-set 2408:8607:500::/64 type object
 address 0 2408:8607:500:: 64
#
ip address-set ************* type object
 address 0 ************* mask 32
#
ip address-set 117.136.38.167 type object
 address 0 117.136.38.167 mask 32
#
ip address-set **********4 type object
 description **********4
 address 0 **********4 mask 32
#
ip address-set 10.10.11.222 type object
 address 0 10.10.11.222 mask 32
#
ip address-set 10.10.10.30 type object
 address 0 10.10.10.30 mask 32
#
ip address-set 106.75.98.235 type object
 address 0 106.75.98.235 mask 32
#
ip address-set **********22 type object
 address 0 **********22 mask 32
#
ip address-set 10.10.11.182 type object
 address 0 10.10.11.182 mask 32
#
ip address-set ***********8 type object
 address 0 ***********8 mask 32
#
ip address-set ************ type object
 address 0 ************ mask 32
#
ip address-set ************ type object
 address 0 ************ mask 32
#
ip address-set *********** type object
 address 0 *********** mask 32
#
ip address-set ************ type object
 address 0 ************ mask 32
#
ip address-set **********9 type object
 address 0 **********9 mask 32
#
ip address-set 2408:8607:500:: type object
 address 0 2408:8607:500:: 64
#
ip address-set *********** type object
 address 0 *********** mask 32
#
ip address-set *********** type object
 address 0 *********** mask 32
#
ip address-set ***********3 type object
 address 0 ***********3 mask 32
#
ip address-set ************ type object
 address 0 ************ mask 32
#
ip address-set ************** type object
 address 0 ************** mask 32
#
ip address-set ************* type object
 address 0 ************* mask 32
#
ip address-set *************** type object
 address 0 *************** mask 32
#
ip address-set ************ type object
 address 0 ************ mask 32
#
ip address-set ************** type object
 address 0 ************** mask 32
#
ip address-set ************ type object
 address 0 ************ mask 32
#
ip address-set **********9 type object
 address 0 **********9 mask 32
#
ip address-set ***********2 type object
 address 0 ***********2 mask 32
#
ip address-set *********** type object
 address 0 *********** mask 24
#
ip address-set ************ type object
 address 0 ************ mask 32
#
ip address-set *************** type object
 address 0 *************** mask 32
#
ip address-set ************ type object
 address 0 ************ mask 32
#
ip address-set ************ type object
 address 0 ************ mask 32
#
ip address-set ac1f-6bd0-cedf type object
 address 0 ac1f-6bd0-cedf
#
ip address-set b405-5d4f-d5f7 type object
 address 0 b405-5d4f-d5f7
#
ip address-set ***********0 type object
 address 0 ***********0 mask 32
#
ip address-set *************** type object
 description �������Ű�
 address 0 *************** mask 32
#
ip address-set ************ type object
 address 0 ************ mask 32
#
ip address-set ************** type object
 address 0 ************** mask 32
#
ip address-set ************ type object
 address 0 ************ mask 32
#
ip address-set ***********1 type object
 address 0 ***********1 mask 32
#
ip address-set ************ type object
 address 0 ************ mask 32
#
ip address-set *************** type object
 address 0 *************** mask 32
#
ip address-set ************* type object
 address 0 ************* mask 32
#
ip address-set *********** type object
 address 0 *********** mask 32
#
ip address-set ************** type object
 address 0 ************** mask 32
#
ip address-set *********** type object
 address 0 *********** mask 32
#
ip address-set ************ type object
 address 0 ************ mask 32
#
ip address-set ************ type object
 address 0 ************ mask 32
#
ip address-set �ζ��� type object
 address 0 ************ mask 32
#
ip address-set ************ type object
 address 0 ************ mask 23
#
ip address-set ************ type object
 address 0 ************ mask 32
#
ip address-set ************/32 type object
 address 0 ************ mask 32
#
ip address-set ************/32 type object
 address 0 ************ mask 32
#
ip address-set ***********5/32 type object
 address 0 ***********5 mask 32
#
ip address-set *************/32 type object
 address 0 ************* mask 32
#
ip address-set ************** type object
 address 0 ************** mask 32
#
ip address-set ************ type object
 address 0 ************ mask 32
#
ip address-set ************ type object
 address 0 ************ mask 32
#
ip address-set ************ type object
 address 0 ************ mask 32
#
ip address-set ************ type object
 address 0 ************ mask 32
#
ip address-set ************ type object
 address 0 ************ mask 32
#
ip address-set ************ type object
 address 0 ************ mask 32
#
ip address-set *********** type object
 address 0 *********** mask 32
#
ip address-set 172.16.30.21 type object
 address 0 172.16.30.21 mask 32
#
ip address-set 172.16.60.67 type object
 address 0 172.16.60.67 mask 32
#
ip address-set ***********15 type object
 address 0 ***********15 mask 32
#
ip address-set 172.16.60.60 type object
 address 0 172.16.60.60 mask 32
#
ip address-set ***********7 type object
 address 0 ***********7 mask 32
#
ip address-set 172.16.30.27 type object
 address 0 172.16.30.27 mask 32
#
ip address-set 172.16.60.55 type object
 address 0 172.16.60.55 mask 32
#
ip address-set 172.16.60.37 type object
 address 0 172.16.60.37 mask 32
#
ip address-set 172.16.60.92 type object
 address 0 172.16.60.92 mask 32
#
ip address-set ***********2 type object
 address 0 ***********2 mask 32
#
ip address-set ************* type object
 address 0 ************* mask 32
#
ip address-set 172.16.50.21 type object
 address 0 172.16.50.21 mask 32
#
ip address-set 172.16.60.51 type object
 address 0 172.16.60.51 mask 32
#
ip address-set 10.10.11.142 type object
 address 0 10.10.11.142 mask 32
#
ip address-set 172.16.60.35 type object
 address 0 172.16.60.35 mask 32
#
ip address-set 172.16.30.52 type object
 address 0 172.16.30.52 mask 32
#
ip address-set ************** type object
 address 0 ************** mask 32
#
ip address-set 172.16.20.2 type object
 address 0 172.16.20.2 mask 32
#
ip address-set ***********6 type object
 address 0 ***********6 mask 32
#
ip address-set ***********2 type object
 address 0 ***********2 mask 32
#
ip address-set ***********9 type object
 address 0 ***********9 mask 32
#
ip address-set ***********2 type object
 address 0 ***********2 mask 32
#
ip address-set ***********03 type object
 address 0 ***********03 mask 32
#
ip address-set ************ type object
 address 0 ************ mask 32
#
ip address-set 172.16.20.26 type object
 address 0 172.16.20.26 mask 32
#
ip address-set 172.16.20.41 type object
 address 0 172.16.20.41 mask 32
#
ip address-set ***********07 type object
 address 0 ***********07 mask 32
#
ip address-set 172.16.30.30 type object
 address 0 172.16.30.30 mask 32
#
ip address-set ***********3 type object
 address 0 ***********3 mask 32
#
ip address-set 10.10.11.141 type object
 address 0 10.10.11.141 mask 32
#
ip address-set 172.16.50.23 type object
 address 0 172.16.50.23 mask 32
#
ip address-set ***********5 type object
 address 0 ***********5 mask 32
#
ip address-set ***********00 type object
 address 0 ***********00 mask 32
#
ip address-set 172.16.50.236 type object
 address 0 172.16.50.236 mask 32
#
ip address-set 172.16.50.22 type object
 address 0 172.16.50.22 mask 32
#
ip address-set ***********1 type object
 address 0 ***********1 mask 32
#
ip address-set ***********/24 type object
 address 0 *********** mask 24
#
ip address-set 172.16.60.46 type object
 address 0 172.16.60.46 mask 32
#
ip address-set ***********2 type object
 address 0 ***********2 mask 32
#
ip address-set ***********58 type object
 address 0 ***********58 mask 32
#
ip address-set 172.16.30.23 type object
 address 0 172.16.30.23 mask 32
#
ip address-set 172.16.60.73 type object
 address 0 172.16.60.73 mask 32
#
ip address-set 172.16.60.50 type object
 address 0 172.16.60.50 mask 32
#
ip address-set ***********20 type object
 address 0 ***********20 mask 32
#
ip address-set ***********14 type object
 address 0 ***********14 mask 32
#
ip address-set ***********5 type object
 address 0 ***********5 mask 32
#
ip address-set ***********25 type object
 address 0 ***********25 mask 32
#
ip address-set ************ type object
 address 0 ************ mask 32
#
ip address-set 172.16.20.68 type object
 address 0 172.16.20.68 mask 32
#
ip address-set ***********3 type object
 address 0 ***********3 mask 32
#
ip address-set ***********22 type object
 address 0 ***********22 mask 32
#
ip address-set 172.16.60.38 type object
 address 0 172.16.60.38 mask 32
#
ip address-set ����-�׿챨 type object
 address 0 118.178.199.177 mask 32
 address 1 106.15.0.0 mask 16
#
ip address-set 172.16.50.25 type object
 address 0 172.16.50.25 mask 32
#
ip address-set �������� type object
 address 0 183.203.38.13 mask 32
 address 1 103.208.15.138 mask 32
 address 2 222.246.133.66 mask 32
#
ip address-set *********** type object
 address 0 *********** mask 32
#
ip address-set ***********54 type object
 address 0 ***********54 mask 32
#
ip address-set 172.16.60.94 type object
 address 0 172.16.60.94 mask 32
#
ip address-set ***********32 type object
 address 0 ***********32 mask 32
#
ip address-set ***********36 type object
 address 0 ***********36 mask 32
#
ip address-set ***********37 type object
 address 0 ***********37 mask 32
#
ip address-set ************ type object
 address 0 ************ mask 32
#
ip address-set 223.104.3.159 type object
 address 0 223.104.3.159 mask 32
#
ip address-set 172.16.60.31 type object
 address 0 172.16.60.31 mask 32
#
ip address-set ***********7 type object
 address 0 ***********7 mask 32
#
ip address-set ************ type object
 address 0 ************ mask 32
#
ip address-set ***********2 type object
 address 0 ***********2 mask 32
#
ip address-set 172.16.20.23 type object
 address 0 172.16.20.23 mask 32
#
ip address-set ************ type object
 address 0 ************ mask 32
 address 1 ************ mask 32
 address 2 ************ mask 32
#
ip address-set VPN-test type object
 address 0 221.216.116.198 mask 32
#
ip address-set VPN�豸 type object
 address 0 **********2 mask 32
#
ip address-set 172.16.30.48 type object
 address 0 172.16.30.48 mask 32
#
ip address-set **********2 type object
 address 0 **********2 mask 32
#
ip address-set *************** type object
 address 0 *************** mask 32
#
ip address-set 2408:8607:500::0/64 type object
 address 0 2408:8607:500:: 64
#
ip address-set 10.10.11.195 type object
 address 0 10.10.11.195 mask 32
#
ip address-set 10.10.11.175 type object
 address 0 10.10.11.175 mask 32
#
ip address-set 10.10.11.33 type object
 address 0 10.10.11.33 mask 32
#
ip address-set ***********46 type object
 address 0 ***********46 mask 32
#
ip address-set 172.16.20.24 type object
 address 0 172.16.20.24 mask 32
#
ip address-set ************ type object
 address 0 ************ mask 32
#
ip address-set ************ type object
 address 0 ************ mask 32
#
ip address-set ************* type object
 address 0 ************* mask 32
#
ip address-set *********** type object
 address 0 *********** mask 32
#
ip address-set ************* type object
 address 0 ************* mask 32
#
ip address-set ********* type object
 address 0 ********* mask 32
#
ip address-set ************* type object
 address 0 ************* mask 32
#
ip address-set ************** type object
 address 0 ************** mask 32
#
ip address-set ************** type object
 address 0 ************** mask 32
#
ip address-set ********* type object
 address 0 ********* mask 32
#
ip address-set *********** type object
 address 0 *********** mask 32
#
ip address-set ************ type object
 address 0 ************ mask 32
#
ip address-set ************ type object
 address 0 ************ mask 32
#
ip address-set ������� type object
 address 0 ************ mask 32
 address 1 ************ mask 32
 address 2 ************** mask 32
#
ip address-set ********* type object
 address 0 ********* mask 32
#
ip address-set ����IP type object
 address 0 ************** mask 28
#
ip address-set **************/28 type object
 address 0 ************** mask 28
#
ip address-set ************** type object
 address 0 ************** mask 32
#
ip address-set *********** type object
 address 0 *********** mask 32
#
ip address-set ************ type object
 address 0 ************ mask 32
#
ip address-set ***********8 type object
 address 0 ***********8 mask 32
#
ip address-set ************ type object
 address 0 ************ mask 32
#
ip address-set ************ type object
 address 0 ************ mask 32
#
ip address-set *********** type object
 address 0 *********** mask 32
#
ip address-set *********** type object
 address 0 *********** mask 32
#
ip address-set ************ type object
 address 0 ************ mask 32
#
ip address-set *********** type object
 address 0 *********** mask 32
#
ip address-set ************ type object
 address 0 ************ mask 32
#
ip address-set ************ type object
 address 0 ************ mask 32
#
ip address-set acd6-1857-f588 type object
 address 0 acd6-1857-f588
 address 1 7a26-65f0-8c94
#
ip address-set Vlanif60 type object
 address 0 range *********** ***********54
 address 1 range *********** *************
 address 2 range ************* ***********54
 address 3 range ************* *************
#
ip address-set Vlanif10 type object
 address 0 range *********** ************
 address 1 range ************ ************
 address 2 range ************ ************
 address 3 range ************ ************
 address 4 range ************* *************
 address 5 range ************* *************
 address 6 ************ mask 32
 address 7 range ************* *************
 address 8 range ************* ***********54
 address 9 ************ mask 32
 address 10 ************* mask 32
 address 11 ************ mask 32
 address 12 ************* mask 32
 address 13 ************ mask 32
 address 14 ************ mask 32
 address 15 ************ mask 32
 address 16 ************ mask 32
 address 17 ************* mask 32
 address 18 ************ mask 32
#
ip address-set *********** type object
 address 0 *********** mask 32
#
ip address-set ************** type object
 address 0 ************** mask 32
#
ip address-set ************ type object
 address 0 ************ mask 32
#
ip address-set ********** type object
 address 0 ********** mask 32
#
ip address-set ***********41 type object
 address 0 ***********41 mask 32
#
ip address-set ONES�Ӱ�IP type object
 address 0 203.119.0.0 mask 16
 address 1 140.205.0.0 mask 16
 address 2 106.11.0.0 mask 16
 address 3 198.11.0.0 mask 16
 address 4 ************* mask 32
 address 5 ************ mask 32
#
ip address-set 172.17.0.2 type object
 address 0 172.17.0.2 mask 32
#
ip address-set 10.10.11.74 type object
 address 0 10.10.11.74 mask 32
#
ip address-set ***********1 type object
 address 0 ***********1 mask 32
#
ip address-set ONES type object
 address 0 123.121.150.35 mask 32
#
ip address-set ѹ������IP type object
 address 0 ********** mask 24
 address 1 16.16.16.0 mask 24
#
ip address-set ********** type object
 address 0 ********** mask 24
#
ip address-set ***********45 type object
 address 0 ***********45 mask 32
#
ip address-set ������ίDCC���� type object
 address 0 ********** mask 16
 address 1 ********** mask 16
#
ip address-set 219.142.69.76 type object
 address 0 219.142.69.76 mask 32
#
ip address-set 10.10.11.133 type object
 address 0 10.10.11.133 mask 32
#
ip address-set 172.16.10.31 type object
 address 0 172.16.10.31 mask 32
#
ip address-set ***********3 type object
 address 0 ***********3 mask 32
#
ip address-set 18.138.190.157 type object
 address 0 18.138.190.157 mask 32
#
ip address-set ************** type object
 address 0 ************** mask 32
#
ip address-set 172.16.40.108 type object
 address 0 172.16.40.108 mask 32
#
ip address-set 172.31.0.233 type object
 address 0 172.31.0.233 mask 32
#
ip address-set 172.16.40.27 type object
 address 0 172.16.40.27 mask 32
#
ip address-set ************ type object
 address 0 ************ mask 32
#
ip address-set ************** type object
 address 0 ************** mask 32
#
ip address-set һ������IP type object
 address 0 125.35.86.162 mask 32
 address 1 *************** mask 32
 address 2 120.52.152.8 mask 32
#
ip address-set Vlanif20 type object
 address 0 range 172.16.20.2 172.16.20.20
 address 1 range ***********14 ***********99
 address 2 range 172.16.20.202 ************4
 address 3 172.16.20.90 mask 32
 address 4 ***********00 mask 32
 address 5 172.16.20.95 mask 32
 address 6 ************* mask 32
 address 7 ***********09 mask 32
 address 8 172.16.20.201 mask 32
 address 9 172.16.20.91 mask 32
 address 10 range 172.16.20.92 172.16.20.93
 address 11 range ***********07 ***********08
 address 12 ***********05 mask 32
 address 13 172.16.20.49 mask 32
 address 14 ************ mask 32
 address 15 ***********13 mask 32
 address 16 172.16.20.200 mask 32
 address 17 172.16.20.94 mask 32
 address 18 ***********03 mask 32
 address 19 172.16.20.55 mask 32
 address 20 172.16.20.81 mask 32
 address 21 ***********04 mask 32
 address 22 172.16.20.82 mask 32
 address 23 172.16.20.69 mask 32
 address 24 ************ mask 32
 address 25 ***********8 mask 32
 address 26 ************ mask 32
 address 27 172.16.20.79 mask 32
 address 28 ***********12 mask 32
 address 29 172.16.20.54 mask 32
 address 30 ************ mask 32
#
ip address-set Vlanif30 type object
 address 0 range 172.16.30.2 172.16.30.20
 address 1 range 172.16.30.81 ************4
 address 2 172.16.30.72 mask 32
 address 3 172.16.30.70 mask 32
 address 4 172.16.30.55 mask 32
 address 5 172.16.30.71 mask 32
 address 6 172.16.30.45 mask 32
 address 7 172.16.30.46 mask 32
 address 8 172.16.30.23 mask 32
 address 9 172.16.30.73 mask 32
 address 10 172.16.30.47 mask 32
 address 11 172.16.30.65 mask 32
 address 12 172.16.30.57 mask 32
 address 13 172.16.30.61 mask 32
 address 14 172.16.30.54 mask 32
 address 15 ************ mask 32
 address 16 172.16.30.66 mask 32
 address 17 range 172.16.30.78 172.16.30.79
 address 18 172.16.30.24 mask 32
 address 19 172.16.30.22 mask 32
 address 20 172.16.30.52 mask 32
 address 21 172.16.30.68 mask 32
 address 22 172.16.30.56 mask 32
 address 23 172.16.30.77 mask 32
 address 24 172.16.30.48 mask 32
 address 25 172.16.30.58 mask 32
 address 26 172.16.30.44 mask 32
 address 27 ************ mask 32
 address 28 172.16.30.49 mask 32
 address 29 172.16.30.74 mask 32
 address 30 172.16.30.69 mask 32
 address 31 172.16.30.34 mask 32
 address 32 172.16.30.60 mask 32
 address 33 172.16.30.37 mask 32
 address 34 172.16.30.26 mask 32
#
ip address-set Vlanif40 type object
 address 0 range 172.16.40.2 172.16.40.14
 address 1 range 172.16.40.52 172.16.40.254
#
ip address-set Vlanif50 type object
 address 0 range 172.16.50.2 172.16.50.20
 address 1 range 172.16.50.58 172.16.50.254
 address 2 172.16.50.57 mask 32
 address 3 172.16.50.21 mask 32
 address 4 172.16.50.35 mask 32
 address 5 172.16.50.47 mask 32
 address 6 172.16.50.30 mask 32
 address 7 172.16.50.44 mask 32
 address 8 172.16.50.50 mask 32
 address 9 172.16.50.56 mask 32
 address 10 172.16.50.55 mask 32
 address 11 172.16.50.49 mask 32
 address 12 172.16.50.48 mask 32
#
ip address-set 172.16.50.34 type object
 address 0 172.16.50.34 mask 32
#
ip address-set 172.16.40.29 type object
 address 0 172.16.40.29 mask 32
#
ip address-set 172.16.50.44 type object
 address 0 172.16.50.44 mask 32
#
ip address-set OA type object
 address 0 123.58.250.18 mask 32
 address 1 120.52.152.66 mask 32
 address 2 ************* mask 32
#
ip address-set ����IP��ַ type object
 address 0 172.16.50.40 mask 32
 address 1 ************ mask 32
 address 2 172.16.50.25 mask 32
 address 4 ************ mask 32
 address 5 172.16.50.29 mask 32
 address 7 172.16.50.32 mask 32
 address 9 172.16.50.34 mask 32
 address 10 172.16.50.39 mask 32
 address 11 172.16.20.86 mask 32
 address 12 ************ mask 32
 address 13 ************ mask 32
 address 14 ************ mask 32
 address 15 172.16.10.75 mask 32
 address 16 172.16.10.66 mask 32
 address 17 172.16.10.67 mask 32
 address 18 ************ mask 32
 address 19 ************ mask 32
 address 21 172.16.10.77 mask 32
 address 22 172.16.50.21 mask 32
 address 23 172.16.10.36 mask 32
 address 27 ************ mask 32
 address 29 172.16.10.42 mask 32
 address 31 172.16.10.44 mask 32
 address 32 172.16.10.46 mask 32
 address 33 172.16.10.47 mask 32
 address 35 172.16.10.49 mask 32
 address 36 172.16.10.50 mask 32
 address 38 172.16.10.52 mask 32
 address 39 172.16.10.81 mask 32
 address 40 ************ mask 32
 address 41 ************ mask 32
 address 42 172.16.10.55 mask 32
 address 43 172.16.10.56 mask 32
 address 44 172.16.50.57 mask 32
 address 46 172.16.50.31 mask 32
 address 47 172.16.10.61 mask 32
 address 48 172.16.10.59 mask 32
 address 49 172.16.10.63 mask 32
 address 51 ************ mask 32
 address 52 172.16.50.45 mask 32
 address 53 172.16.50.47 mask 32
 address 55 172.16.10.88 mask 32
#
ip address-set ********** type object
 address 0 ********** mask 24
 address 1 range ********** ************
 address 2 range ************ ************
 address 3 range ********** **********1
 address 4 *********** mask 32
 address 5 range *********** **********54
 address 6 range ********** ***********
 address 7 range *********** ************
 address 8 range ************ **********54
#
ip address-set ************ type object
 address 0 ************ mask 32
#
ip address-set ************ type object
 address 0 ************ mask 32
#
ip address-set ************ type object
 address 0 ************ mask 32
#
ip address-set ************ type object
 address 0 ************ mask 32
#
ip address-set ************ type object
 address 0 ************ mask 32
#
ip address-set ************** type object
 address 0 ************** mask 32
#
ip address-set ************* type object
 address 0 ************* mask 32
#
ip address-set ************ type object
 address 0 ************ mask 32
#
ip address-set ************* type object
 address 0 ************* mask 32
#
ip address-set *********** type object
 address 0 *********** mask 32
#
ip address-set ����-PC type object
 address 0 ************ mask 32
 address 1 ************ mask 32
#
ip address-set ************** type object
 address 0 ************** mask 32
#
ip address-set ************ type object
 address 0 ************ mask 32
#
ip address-set ************** type object
 address 0 ************** mask 32
#
ip address-set ************* type object
 address 0 ************* mask 32
#
ip address-set ***********5 type object
 address 0 ***********5 mask 32
#
ip address-set ************ type object
 address 0 ************ mask 32
#
ip address-set vlanif25-0 type object
 address 0 ********** mask 32
 address 1 ********** mask 32
 address 2 range *********** ***********
 address 3 range *********** **********2
 address 4 **********5 mask 32
 address 5 *********** mask 32
 address 6 *********** mask 32
 address 7 *********** mask 32
 address 8 *********** mask 32
 address 9 *********** mask 32
 address 10 **********0 mask 32
 address 11 range **********7 **********9
 address 12 ************ mask 32
 address 13 ************ mask 32
 address 14 ************ mask 32
 address 15 range ************ ************
 address 16 ************ mask 32
 address 18 ************ mask 32
 address 19 range ************ ************
 address 20 range ************ ************
 address 21 range ************ ************
 address 22 range ***********4 ***********5
 address 23 range ************ ************
 address 24 range ************ ************
 address 25 ***********2 mask 32
 address 26 range ***********7 ***********0
 address 27 ***********6 mask 32
 address 28 range ***********8 ***********9
 address 29 ************ mask 32
#
ip address-set vlanif25-1 type object
 address 0 range *********** ***********
 address 1 range *********** ***********
 address 2 range *********** ***********
 address 3 *********** mask 32
 address 4 range *********** ***********
 address 5 range 10.10.11.56 10.10.11.60
 address 6 range *********** ***********
 address 7 range *********** 10.10.11.92
 address 8 10.10.11.92 mask 32
 address 9 range 10.10.11.106 ***********0
 address 10 range ***********3 ***********7
 address 11 range ************ ***********8
 address 12 10.10.11.132 mask 32
 address 13 10.10.11.134 mask 32
 address 14 range 10.10.11.137 ************
 address 15 range 10.10.11.141 10.10.11.143
 address 16 range 10.10.11.146 ***********0
 address 17 range ***********4 ***********9
 address 18 range ************ ************
 address 19 10.10.11.172 mask 32
 address 20 10.10.11.176 mask 32
 address 21 range 10.10.11.178 10.10.11.182
 address 22 range 10.10.11.184 10.10.11.196
 address 23 range ***********2 ************
 address 24 range 10.10.11.213 10.10.11.217
 address 25 range 10.10.11.221 10.10.11.222
 address 26 range 10.10.11.247 10.10.11.254
 address 27 range 10.10.11.95 10.10.11.103
 address 28 10.10.11.243 mask 32
#
ip address-set FOGA type object
 address 0 16.16.16.0 mask 24
#
ip address-set 172.16.20.71 type object
 address 0 172.16.20.71 mask 32
#
ip address-set ��ǧ�� type object
 address 0 172.16.20.48 mask 32
#
ip address-set ������ type object
 address 0 172.16.50.21 mask 32
#
ip address-set �������ES��ַ type object
 address 0 ************ mask 32
 address 1 ************ mask 32
 address 2 172.16.40.37 mask 32
 address 3 ************ mask 32
 address 4 ************ mask 32
 address 5 172.16.40.20 mask 32
 address 6 ************ mask 32
 address 7 ************* mask 32
 address 8 ************ mask 32
 address 9 ***********0 mask 32
 address 10 **********83 mask 32
 address 11 **********7 mask 32
 address 12 *********** mask 32
 address 13 10.10.11.94 mask 32
 address 14 10.10.11.102 mask 32
 address 15 172.16.20.200 mask 32
 address 16 ************ mask 32
 address 17 *********** mask 32
 address 18 ************ mask 32
 address 19 ***********2 mask 32
 address 20 *********** mask 32
 address 21 10.10.11.104 mask 32
 address 22 ************ mask 32
 address 23 ************ mask 32
 address 24 ************ mask 32
 address 25 10.10.11.66 mask 32
 address 26 ************ mask 32
 address 27 ************ mask 32
 address 28 ************ mask 32
 address 29 ***********7 mask 32
 address 30 ***********8 mask 32
 address 31 ***********9 mask 32
 address 32 *********** mask 32
 address 33 ***********1 mask 32
 address 34 172.16.40.34 mask 32
#
ip address-set ����ES��ַ type object
 address 0 120.52.152.2 mask 32
 address 1 ************ mask 32
 address 2 ************* mask 32
 address 3 120.52.152.13 mask 32
 address 4 120.52.152.14 mask 32
 address 5 123.58.250.20 mask 32
 address 6 218.76.14.23 mask 32
#
ip address-set ����� type object
 address 0 172.16.20.40 mask 32
#
ip address-set ���� type object
 address 0 ************ mask 32
 address 1 ************ mask 32
 address 2 ************ mask 32
 address 3 172.16.40.20 mask 32
#
ip address-set ����.TV type object
 address 0 ************* mask 32
#
ip address-set ���� type object
 address 0 172.16.30.60 mask 32
#
ip address-set GOBY-���� type object
 address 0 123.121.150.40 mask 32
#
ip address-set ************** type object
 address 0 ************** mask 32
 address 1 8.218.5.61 mask 32
#
ip address-set 150.34�Ӱ�IP type object
 address 0 ************* mask 32
 address 1 ************** mask 28
 address 2 39.155.237.0 mask 24
 address 3 114.242.127.32 mask 32
 address 4 111.192.102.196 mask 32
 address 5 111.194.223.57 mask 32
 address 6 121.29.98.179 mask 32
#
ip address-set 150.34 type object
 address 0 123.121.150.34 mask 32
#
ip address-set ��΢��ά��Ա type object
 address 0 ************ mask 32
 address 1 ************ mask 32
 address 2 ***********7 mask 32
 address 3 *********** mask 32
 address 4 ************ mask 32
 address 5 ************ mask 32
#
ip address-set ��΢IP��ַ type object
 address 0 123.58.250.18 mask 32
#
ip address-set 172.16.36.144 type object
 address 0 ************44 mask 32
#
ip address-set gust-vlan type object
 address 0 ********** mask 23
 address 1 ************ mask 32
 address 2 ************ mask 32
#
ip address-set ���Ʒ� type object
 address 0 ************* mask 32
#
ip address-set 172.16.20.21 type object
 address 0 172.16.20.21 mask 32
#
ip address-set ©���ӿ����� type object
 address 0 43.228.36.94 mask 32
 address 1 111.202.168.76 mask 32
#
ip address-set *********** type object
 address 0 *********** mask 32
#
ip address-set ���Ͽ� type object
 address 0 ************ mask 32
 address 1 ***********5 mask 32
#
ip address-set openfalcon�Ӱ�IP type object
 address 0 42.240.129.0 mask 24
 address 1 42.240.133.0 mask 24
 address 2 152.32.234.44 mask 32
 address 3 range 120.52.152.1 120.52.152.24
 address 4 range 123.58.236.73 123.58.236.78
 address 5 range 123.58.250.17 123.58.250.27
 address 6 103.14.35.210 mask 32
 address 7 52.77.73.79 mask 32
 address 8 18.138.190.157 mask 32
 address 9 3.1.108.37 mask 32
 address 10 13.214.139.139 mask 32
 address 11 13.214.129.117 mask 32
 address 12 ************* mask 32
 address 13 13.209.106.72 mask 32
 address 14 3.35.126.8 mask 32
 address 15 ************** mask 32
 address 16 18.138.102.0 mask 32
 address 17 39.104.93.208 mask 32
 address 18 39.104.85.115 mask 32
 address 19 101.200.132.197 mask 32
 address 20 39.104.105.217 mask 32
 address 21 ************ mask 32
 address 22 118.190.70.117 mask 32
 address 23 152.32.226.228 mask 32
 address 24 182.92.124.45 mask 32
 address 25 8.210.236.39 mask 32
 address 26 47.104.150.39 mask 32
 address 27 47.242.255.187 mask 32
 address 28 47.242.47.211 mask 32
 address 29 ********** mask 16
 address 30 ********** mask 16
 address 31 ********** mask 16
 address 32 113.31.0.0 mask 16
 address 33 152.32.172.182 mask 32
 address 34 180.101.56.56 mask 32
 address 35 117.50.36.163 mask 32
 address 36 211.91.254.224 mask 30
 address 37 185.233.19.0 mask 25
 address 38 152.32.234.20 mask 32
 address 39 119.167.222.135 mask 32
 address 40 185.233.19.128 mask 25
 address 41 113.249.159.56 mask 32
#
ip address-set ************47 type object
 address 0 ************47 mask 32
#
ip address-set ������������ type object
 address 0 ************* mask 32
#
ip address-set ES��ַA type object
 address 0 211.91.254.228 mask 32
#
ip address-set ��������ES��ַA type object
 address 0 ************ mask 32
 address 1 10.10.11.94 mask 32
 address 2 ***********7 mask 32
 address 3 ************ mask 32
 address 4 ************ mask 32
 address 5 *********** mask 32
 address 6 ************ mask 32
 address 7 ************ mask 32
#
ip address-set ������ type object
 address 0 172.16.20.24 mask 32
 address 1 ***********9 mask 32
#
ip address-set ���� type object
 address 0 172.16.20.27 mask 32
#
ip address-set �³��� type object
 address 0 172.16.20.48 mask 32
 address 1 ************ mask 32
#
ip address-set ������ type object
 address 0 ***********9 mask 32
#
ip address-set ������ type object
 address 0 172.16.30.68 mask 32
#
ip address-set ������ӳ��Ӱ�IP type object
 address 0 ************ mask 32
 address 1 ************* mask 32
 address 2 ************* mask 32
#
ip address-set OA���Է���IP type object
 address 0 ************* mask 32
 address 1 ************* mask 32
#
ip address-set FOBrain��FOEYE type object
 address 0 ************* mask 32
#
ip address-set FOFA�� type object
 address 0 ************ mask 32
 address 1 ************ mask 32
 address 2 ************ mask 32
 address 3 ************ mask 32
 address 4 ************ mask 32
 address 5 ************ mask 32
 address 6 ************ mask 32
 address 7 ************ mask 32
 address 8 ************ mask 32
 address 9 *********** mask 32
 address 10 ************ mask 32
 address 11 ************ mask 32
 address 12 ************ mask 32
 address 13 *********** mask 32
 address 14 ************ mask 32
 address 15 ***********1 mask 32
 address 16 ************ mask 32
 address 17 ************ mask 32
 address 18 ************ mask 32
 address 19 ************ mask 32
 address 20 ************ mask 32
 address 21 ************ mask 32
#
ip address-set ������ֹ���ʹ���IP type object
 address 0 ************** mask 32
 address 1 ************** mask 32
 address 2 ************** mask 32
 address 3 ************** mask 32
 address 4 ************* mask 32
 address 5 ************** mask 32
 address 6 ************* mask 32
 address 7 *************** mask 32
 address 8 ************* mask 32
 address 9 ************* mask 32
 address 10 ************* mask 32
 address 11 ************ mask 32
#
ip address-set ���� type object
 address 0 ************ mask 32
#
ip address-set black-ip type object
 address 0 *********** mask 32
 address 1 *********** mask 32
 address 2 *********** mask 32
 address 3 *********** mask 32
 address 4 ************* mask 32
 address 5 ************** mask 32
 address 6 ************** mask 32
 address 7 ************* mask 32
 address 8 ************* mask 32
 address 9 ************** mask 32
 address 10 ************* mask 32
 address 11 ************* mask 32
 address 12 ************* mask 32
 address 13 ************* mask 32
 address 14 ************* mask 32
 address 15 ************* mask 32
 address 16 ************** mask 32
 address 17 ************* mask 32
 address 18 ************* mask 32
 address 19 ************** mask 32
 address 20 ************** mask 32
 address 21 ************* mask 32
 address 22 *********** mask 32
 address 23 ************ mask 32
 address 24 *********** mask 32
 address 25 *********** mask 32
 address 26 ************ mask 32
 address 27 ************ mask 32
 address 28 ************ mask 32
 address 29 ************ mask 32
 address 30 ************** mask 32
 address 31 ************** mask 32
 address 32 ************ mask 32
 address 33 ************ mask 32
 address 34 ************ mask 32
 address 35 ************ mask 32
 address 36 ************* mask 32
 address 37 ************* mask 32
 address 38 ************ mask 32
 address 39 ************* mask 32
 address 40 ************ mask 32
 address 41 *********** mask 32
 address 42 ************ mask 32
 address 43 *********** mask 32
 address 44 ************* mask 32
 address 45 *********** mask 32
 address 46 ************ mask 32
 address 47 *********** mask 32
 address 48 ************ mask 32
 address 49 ************* mask 32
 address 50 ************ mask 32
 address 51 ************** mask 32
 address 52 ************** mask 32
 address 53 ************** mask 32
 address 54 ********** mask 32
 address 55 ********** mask 32
 address 56 ********** mask 32
 address 57 *********** mask 32
 address 58 ************** mask 32
 address 59 5.255.88.185 mask 32
 address 60 52.253.113.181 mask 32
 address 61 52.231.52.226 mask 32
 address 62 52.231.14.124 mask 32
 address 63 52.203.216.120 mask 32
 address 64 5.188.34.235 mask 32
 address 65 5.188.34.218 mask 32
 address 66 5.188.34.137 mask 32
 address 67 5.188.228.53 mask 32
 address 68 5.188.228.150 mask 32
 address 69 5.188.108.29 mask 32
 address 70 51.79.248.8 mask 32
 address 71 51.79.237.113 mask 32
 address 72 47.92.91.195 mask 32
 address 73 47.75.177.15 mask 32
 address 74 47.243.7.66 mask 32
 address 75 47.243.49.249 mask 32
 address 76 47.122.43.174 mask 32
 address 77 46.8.198.214 mask 32
 address 78 46.249.49.21 mask 32
 address 79 45.91.83.58 mask 32
 address 80 45.87.43.60 mask 32
 address 81 45.87.43.56 mask 32
 address 82 45.87.43.55 mask 32
 address 83 45.79.122.225 mask 32
 address 84 45.77.251.116 mask 32
 address 85 45.77.250.209 mask 32
 address 86 45.77.246.42 mask 32
 address 87 45.77.171.10 mask 32
 address 88 45.77.170.15 mask 32
 address 89 45.77.169.18 mask 32
 address 90 45.77.133.4 mask 32
 address 91 45.76.55.101 mask 32
 address 92 45.76.48.18 mask 32
 address 93 45.76.206.67 mask 32
 address 94 45.76.206.153 mask 32
 address 95 45.76.202.190 mask 32
 address 96 45.76.153.100 mask 32
 address 97 45.76.148.165 mask 32
 address 98 45.67.230.119 mask 32
 address 99 45.63.55.5 mask 32
 address 100 45.63.123.209 mask 32
 address 101 45.32.57.227 mask 32
 address 102 45.32.49.144 mask 32
 address 103 45.32.45.110 mask 32
 address 104 45.32.232.8 mask 32
 address 105 45.32.149.253 mask 32
 address 106 45.32.121.191 mask 32
 address 107 45.32.120.178 mask 32
 address 108 45.32.115.142 mask 32
 address 109 45.32.11.27 mask 32
 address 110 45.32.111.181 mask 32
 address 111 45.32.106.240 mask 32
 address 112 45.207.53.204 mask 32
 address 113 45.207.50.104 mask 32
 address 114 45.207.50.102 mask 32
 address 115 45.204.12.29 mask 32
 address 116 45.195.67.64 mask 32
 address 117 45.144.138.121 mask 32
 address 118 45.144.138.120 mask 32
 address 119 45.144.138.119 mask 32
 address 120 45.14.224.18 mask 32
 address 121 45.142.166.15 mask 32
 address 122 45.14.106.178 mask 32
 address 123 45.14.106.162 mask 32
 address 124 45.14.106.159 mask 32
 address 125 45.136.244.166 mask 32
 address 126 45.133.194.150 mask 32
 address 127 45.116.166.133 mask 32
 address 128 45.116.165.25 mask 32
 address 129 45.116.161.95 mask 32
 address 130 45.116.13.241 mask 32
 address 131 45.10.58.93 mask 32
 address 132 43.255.28.190 mask 32
 address 133 43.248.186.112 mask 32
 address 134 43.242.34.30 mask 32
 address 135 43.242.34.23 mask 32
 address 136 43.240.13.178 mask 32
 address 137 43.230.161.83 mask 32
 address 138 43.230.161.71 mask 32
 address 139 43.230.161.70 mask 32
 address 140 43.228.86.56 mask 32
 address 141 43.226.23.89 mask 32
 address 142 43.226.23.88 mask 32
 address 143 43.226.23.84 mask 32
 address 144 43.224.34.35 mask 32
 address 145 43.154.29.157 mask 32
 address 146 43.131.69.98 mask 32
 address 147 43.128.18.202 mask 32
 address 148 42.51.222.242 mask 32
 address 149 42.51.222.109 mask 32
 address 150 42.193.189.161 mask 32
 address 151 39.104.91.125 mask 32
 address 152 39.104.20.55 mask 32
 address 153 39.101.138.98 mask 32
 address 154 37.247.53.76 mask 32
 address 155 36.106.178.161 mask 32
 address 156 35.240.220.175 mask 32
 address 157 35.220.214.142 mask 32
 address 158 35.220.176.90 mask 32
 address 159 34.96.224.146 mask 32
 address 160 34.96.188.202 mask 32
 address 161 34.92.30.54 mask 32
 address 162 34.92.250.61 mask 32
 address 163 34.125.128.32 mask 32
 address 164 27.124.45.116 mask 32
 address 165 27.124.45.115 mask 32
 address 166 27.124.45.114 mask 32
 address 167 27.124.40.245 mask 32
 address 168 27.124.40.234 mask 32
 address 169 27.124.40.219 mask 32
 address 170 27.124.40.199 mask 32
 address 171 27.124.40.172 mask 32
 address 172 27.124.40.126 mask 32
 address 173 27.124.40.125 mask 32
 address 174 27.102.101.71 mask 32
 address 175 2.56.213.86 mask 32
 address 176 23.95.13.180 mask 32
 address 177 23.254.228.142 mask 32
 address 178 23.254.225.184 mask 32
 address 179 23.234.242.246 mask 32
 address 180 23.225.177.190 mask 32
 address 181 23.225.161.125 mask 32
 address 182 23.106.123.157 mask 32
 address 183 223.252.173.139 mask 32
 address 184 217.69.3.107 mask 32
 address 185 217.12.199.70 mask 32
 address 186 216.118.229.142 mask 32
 address 187 216.118.229.141 mask 32
 address 188 216.118.229.140 mask 32
 address 189 216.118.229.139 mask 32
 address 190 216.118.229.138 mask 32
 address 191 213.156.142.77 mask 32
 address 192 213.156.137.235 mask 32
 address 193 210.56.59.201 mask 32
 address 194 210.56.59.200 mask 32
 address 195 210.56.59.197 mask 32
 address 196 209.250.248.20 mask 32
 address 197 209.250.237.26 mask 32
 address 198 209.250.224.145 mask 32
 address 199 208.92.93.26 mask 32
 address 200 207.246.100.190 mask 32
 address 201 207.148.97.160 mask 32
 address 202 207.148.72.166 mask 32
 address 203 207.148.125.242 mask 32
 address 204 207.148.110.215 mask 32
 address 205 207.148.110.155 mask 32
 address 206 207.148.103.108 mask 32
 address 207 205.189.160.45 mask 32
 address 208 205.189.160.106 mask 32
 address 209 203.86.236.174 mask 32
 address 210 203.86.236.142 mask 32
 address 211 203.86.234.16 mask 32
 address 212 203.196.8.201 mask 32
 address 213 203.12.200.61 mask 32
 address 214 203.12.200.14 mask 32
 address 215 202.81.235.65 mask 32
 address 216 202.81.235.63 mask 32
 address 217 202.81.235.18 mask 32
 address 218 202.81.235.13 mask 32
 address 219 20.2.80.0 mask 32
 address 220 202.61.136.158 mask 32
 address 221 20.255.61.254 mask 32
 address 222 20.255.49.253 mask 32
 address 223 20.2.232.107 mask 32
 address 224 202.182.123.105 mask 32
 address 225 202.182.120.156 mask 32
 address 226 202.182.115.238 mask 32
 address 227 202.182.114.156 mask 32
 address 228 202.182.109.41 mask 32
 address 229 202.182.109.173 mask 32
 address 230 202.182.108.24 mask 32
 address 231 202.182.105.169 mask 32
 address 232 20.205.36.24 mask 32
 address 233 20.205.129.255 mask 32
 address 234 20.194.5.189 mask 32
 address 235 20.194.32.166 mask 32
 address 236 198.13.56.122 mask 32
 address 237 198.13.46.248 mask 32
 address 238 195.2.74.8 mask 32
 address 239 195.123.213.78 mask 32
 address 240 194.49.69.218 mask 32
 address 241 194.49.68.134 mask 32
 address 242 194.113.194.107 mask 32
 address 243 193.37.59.246 mask 32
 address 244 193.22.152.56 mask 32
 address 245 193.203.215.21 mask 32
 address 246 193.19.118.162 mask 32
 address 247 192.252.180.100 mask 32
 address 248 192.248.188.199 mask 32
 address 249 *************** mask 32
 address 250 *************** mask 32
 address 251 *************** mask 32
 address 252 *************** mask 32
 address 253 *************** mask 32
 address 254 *************** mask 32
 address 255 *************** mask 32
 address 256 *************** mask 32
 address 257 *************** mask 32
 address 258 ************** mask 32
 address 259 *************** mask 32
 address 260 *************** mask 32
 address 261 ************* mask 32
 address 262 ************** mask 32
 address 263 ************** mask 32
 address 264 ************* mask 32
 address 265 ************** mask 32
 address 266 *************** mask 32
 address 267 ************* mask 32
 address 268 ************** mask 32
 address 269 ************* mask 32
 address 270 ************** mask 32
 address 271 ************** mask 32
 address 272 ************** mask 32
 address 273 ************** mask 32
 address 274 ************** mask 32
 address 275 *************** mask 32
 address 276 ************* mask 32
 address 277 ************** mask 32
 address 278 ************* mask 32
 address 279 ************ mask 32
 address 280 ************ mask 32
 address 281 ************** mask 32
 address 282 *************** mask 32
 address 283 ************** mask 32
 address 284 *********** mask 32
 address 285 ************** mask 32
 address 286 ************* mask 32
 address 287 ************* mask 32
 address 288 ************* mask 32
 address 289 ************** mask 32
 address 290 ************** mask 32
 address 291 ************* mask 32
 address 292 ************* mask 32
 address 293 ************* mask 32
 address 294 *********** mask 32
 address 295 ************ mask 32
 address 296 ************* mask 32
 address 297 ************* mask 32
 address 298 ************** mask 32
 address 299 ************* mask 32
 address 300 ************** mask 32
 address 301 ************* mask 32
 address 302 ************** mask 32
 address 303 ************ mask 32
 address 304 ************** mask 32
 address 305 ************* mask 32
 address 306 ************ mask 32
 address 307 ************ mask 32
 address 308 ************ mask 32
 address 309 ************* mask 32
 address 310 ************** mask 32
 address 311 ************** mask 32
 address 312 ************* mask 32
 address 313 ************** mask 32
 address 314 ************* mask 32
 address 315 ************** mask 32
 address 316 ************* mask 32
 address 317 ************* mask 32
 address 318 ************** mask 32
 address 319 ************** mask 32
 address 320 *************** mask 32
 address 321 *************** mask 32
 address 322 *************** mask 32
 address 323 ************* mask 32
 address 324 *************** mask 32
 address 325 *************** mask 32
 address 326 *************** mask 32
 address 327 ************ mask 32
 address 328 ************* mask 32
 address 329 ************** mask 32
 address 330 ************** mask 32
 address 331 ************** mask 32
 address 332 ************** mask 32
 address 333 ************** mask 32
 address 334 *************** mask 32
 address 335 *************** mask 32
 address 336 ************* mask 32
 address 337 ************* mask 32
 address 338 ************** mask 32
 address 339 ************** mask 32
 address 340 *************** mask 32
 address 341 *************** mask 32
 address 342 *************** mask 32
 address 343 *************** mask 32
 address 344 *************** mask 32
 address 345 ************ mask 32
 address 346 ************** mask 32
 address 347 ************** mask 32
 address 348 ************ mask 32
 address 349 159.65.188.162 mask 32
 address 350 159.65.158.28 mask 32
 address 351 158.247.237.215 mask 32
 address 352 158.247.222.240 mask 32
 address 353 158.247.222.2 mask 32
 address 354 158.247.221.162 mask 32
 address 355 158.247.195.79 mask 32
 address 356 158.247.193.17 mask 32
 address 357 156.253.14.159 mask 32
 address 358 156.247.11.57 mask 32
 address 359 156.247.10.118 mask 32
 address 360 156.245.12.79 mask 32
 address 361 156.245.12.173 mask 32
 address 362 156.245.12.150 mask 32
 address 363 156.240.108.251 mask 32
 address 364 156.240.108.20 mask 32
 address 365 156.240.108.103 mask 32
 address 366 156.240.106.99 mask 32
 address 367 156.240.106.69 mask 32
 address 368 156.240.106.27 mask 32
 address 369 156.234.95.85 mask 32
 address 370 156.234.95.111 mask 32
 address 371 156.234.95.110 mask 32
 address 372 156.226.17.107 mask 32
 address 373 155.94.146.87 mask 32
 address 374 155.235.243.157 mask 32
 address 375 154.88.14.57 mask 32
 address 376 154.88.14.24 mask 32
 address 377 154.39.66.248 mask 32
 address 378 154.39.66.122 mask 32
 address 379 154.39.66.103 mask 32
 address 380 154.38.111.223 mask 32
 address 381 154.31.174.84 mask 32
 address 382 154.31.172.86 mask 32
 address 383 154.223.167.41 mask 32
 address 384 154.215.197.141 mask 32
 address 385 154.215.197.140 mask 32
 address 386 154.215.123.202 mask 32
 address 387 154.209.88.225 mask 32
 address 388 154.209.72.197 mask 32
 address 389 154.208.77.176 mask 32
 address 390 154.208.76.252 mask 32
 address 391 154.204.41.113 mask 32
 address 392 154.202.56.139 mask 32
 address 393 154.202.56.132 mask 32
 address 394 154.197.27.194 mask 32
 address 395 152.32.247.153 mask 32
 address 396 152.32.225.186 mask 32
 address 397 152.32.219.4 mask 32
 address 398 152.32.219.135 mask 32
 address 399 152.32.211.67 mask 32
 address 400 152.32.155.96 mask 32
 address 401 152.32.153.134 mask 32
 address 402 150.129.219.222 mask 32
 address 403 14.98.68.67 mask 32
 address 404 149.28.26.169 mask 32
 address 405 149.28.22.56 mask 32
 address 406 149.28.16.125 mask 32
 address 407 149.28.153.106 mask 32
 address 408 149.28.152.166 mask 32
 address 409 149.28.139.84 mask 32
 address 410 149.28.130.206 mask 32
 address 411 146.196.54.229 mask 32
 address 412 146.190.210.36 mask 32
 address 413 146.190.18.156 mask 32
 address 414 145.239.175.116 mask 32
 address 415 144.48.6.15 mask 32
 address 416 14.1.98.191 mask 32
 address 417 14.192.67.218 mask 32
 address 418 14.192.67.187 mask 32
 address 419 141.164.62.171 mask 32
 address 420 141.164.59.80 mask 32
 address 421 141.164.59.58 mask 32
 address 422 141.164.52.9 mask 32
 address 423 141.164.49.114 mask 32
 address 424 141.164.46.44 mask 32
 address 425 141.164.43.177 mask 32
 address 426 141.164.42.13 mask 32
 address 427 141.164.34.96 mask 32
 address 428 140.82.38.177 mask 32
 address 429 140.82.36.67 mask 32
 address 430 139.59.113.146 mask 32
 address 431 139.5.200.6 mask 32
 address 432 139.5.200.4 mask 32
 address 433 139.5.200.3 mask 32
 address 434 139.180.223.123 mask 32
 address 435 139.180.214.192 mask 32
 address 436 139.180.191.109 mask 32
 address 437 139.180.172.42 mask 32
 address 438 139.180.146.47 mask 32
 address 439 139.180.145.170 mask 32
 address 440 139.162.42.56 mask 32
 address 441 139.162.19.20 mask 32
 address 442 139.162.125.72 mask 32
 address 443 137.220.244.3 mask 32
 address 444 136.244.103.29 mask 32
 address 445 134.209.158.208 mask 32
 address 446 134.209.149.2 mask 32
 address 447 134.195.208.174 mask 32
 address 448 134.122.139.54 mask 32
 address 449 134.122.139.28 mask 32
 address 450 13.229.93.133 mask 32
 address 451 13.213.157.52 mask 32
 address 452 130.255.189.26 mask 32
 address 453 129.226.36.211 mask 32
 address 454 128.1.44.238 mask 32
 address 455 128.14.239.146 mask 32
 address 456 128.14.233.129 mask 32
 address 457 125.17.92.85 mask 32
 address 458 125.124.165.128 mask 32
 address 459 123.176.96.157 mask 32
 address 460 123.108.250.122 mask 32
 address 461 122.254.96.201 mask 32
 address 462 122.254.92.156 mask 32
 address 463 122.10.84.179 mask 32
 address 464 122.10.45.217 mask 32
 address 465 122.10.45.207 mask 32
 address 466 121.0.111.51 mask 32
 address 467 120.26.44.124 mask 32
 address 468 119.91.38.253 mask 32
 address 469 119.76.173.247 mask 32
 address 470 119.29.182.35 mask 32
 address 471 119.29.161.16 mask 32
 address 472 119.160.233.178 mask 32
 address 473 119.160.233.169 mask 32
 address 474 119.13.92.72 mask 32
 address 475 118.194.239.178 mask 32
 address 476 118.194.233.106 mask 32
 address 477 118.193.72.107 mask 32
 address 478 118.190.216.82 mask 32
 address 479 118.163.100.221 mask 32
 address 480 118.107.45.33 mask 32
 address 481 118.107.45.31 mask 32
 address 482 118.107.45.21 mask 32
 address 483 117.254.110.52 mask 32
 address 484 117.18.14.22 mask 32
 address 485 117.18.14.21 mask 32
 address 486 117.18.14.20 mask 32
 address 487 116.213.39.157 mask 32
 address 488 116.213.39.155 mask 32
 address 489 116.212.126.129 mask 32
 address 490 116.206.92.83 mask 32
 address 491 114.29.254.94 mask 32
 address 492 114.29.254.201 mask 32
 address 493 114.29.254.17 mask 32
 address 494 114.29.254.126 mask 32
 address 495 114.29.253.4 mask 32
 address 496 114.29.253.126 mask 32
 address 497 112.28.248.5 mask 32
 address 498 112.28.248.2 mask 32
 address 499 112.213.109.47 mask 32
 address 500 112.213.109.35 mask 32
 address 501 112.213.109.32 mask 32
 address 502 112.196.204.151 mask 32
 address 503 112.196.204.141 mask 32
 address 504 112.121.187.182 mask 32
 address 505 112.121.187.181 mask 32
 address 506 112.121.187.180 mask 32
 address 507 112.121.187.179 mask 32
 address 508 112.121.187.178 mask 32
 address 509 112.121.176.133 mask 32
 address 510 111.92.84.121 mask 32
 address 511 111.73.46.30 mask 32
 address 512 111.73.46.103 mask 32
 address 513 111.73.46.102 mask 32
 address 514 111.67.193.215 mask 32
 address 515 110.45.132.219 mask 32
 address 516 109.94.209.44 mask 32
 address 517 109.248.19.107 mask 32
 address 518 109.248.19.102 mask 32
 address 519 109.248.19.101 mask 32
 address 520 108.61.174.133 mask 32
 address 521 108.61.166.255 mask 32
 address 522 108.61.163.91 mask 32
 address 523 108.61.117.16 mask 32
 address 524 108.160.143.43 mask 32
 address 525 108.160.143.31 mask 32
 address 526 108.160.142.248 mask 32
 address 527 108.160.139.106 mask 32
 address 528 107.191.60.208 mask 32
 address 529 107.155.55.15 mask 32
 address 530 107.150.124.43 mask 32
 address 531 106.55.60.126 mask 32
 address 532 106.240.232.162 mask 32
 address 533 104.233.172.12 mask 32
 address 534 104.233.163.30 mask 32
 address 535 104.233.162.7 mask 32
 address 536 ************** mask 32
 address 537 ************** mask 32
 address 538 ************** mask 32
 address 539 ************* mask 32
 address 540 ************* mask 32
 address 541 ************** mask 32
 address 542 ************* mask 32
 address 543 ************* mask 32
 address 544 ************ mask 32
 address 545 ************* mask 32
 address 546 ************* mask 32
 address 547 ************** mask 32
 address 548 ************* mask 32
 address 549 ************* mask 32
 address 550 ************ mask 32
 address 551 ************ mask 32
 address 552 ************* mask 32
 address 553 ************* mask 32
 address 554 ************** mask 32
 address 555 ************** mask 32
 address 556 ************** mask 32
 address 557 ************** mask 32
 address 558 ************* mask 32
 address 559 ************** mask 32
 address 560 *************** mask 32
 address 561 *************** mask 32
 address 562 *************** mask 32
 address 563 *************** mask 32
 address 564 ************** mask 32
 address 565 *************** mask 32
 address 566 ************* mask 32
 address 567 *************** mask 32
 address 568 *************** mask 32
 address 569 *************** mask 32
 address 570 ************** mask 32
 address 571 *************** mask 32
 address 572 ************** mask 32
 address 573 *************** mask 32
 address 574 ************** mask 32
 address 575 ************* mask 32
 address 576 ************* mask 32
 address 577 *************** mask 32
 address 578 *************** mask 32
 address 579 ************** mask 32
 address 580 ************* mask 32
 address 581 ************* mask 32
 address 582 ************** mask 32
 address 583 ************** mask 32
 address 584 ************** mask 32
 address 585 *************** mask 32
 address 586 ************* mask 32
 address 587 ************** mask 32
 address 588 ************** mask 32
 address 589 ************** mask 32
 address 590 ************** mask 32
 address 591 ************** mask 32
 address 592 *************** mask 32
 address 593 ************** mask 32
 address 594 *************** mask 32
 address 595 ************** mask 32
 address 596 ************** mask 32
 address 597 ************ mask 32
#
ip address-set ��Ⱦ������IP��ַ type object
 address 0 ************ mask 32
#
ip address-set Ѧ��ΰ type object
 address 0 ************ mask 32
#
ip address-set ������IP��ַ type object
 address 0 *********** mask 32
 address 1 ************ mask 32
 address 3 ***********7 mask 32
#
ip address-set ������ type object
 address 0 ***********05 mask 32
#
ip address-set ��� type object
 address 0 ************ mask 32
 address 1 ************ mask 32
#
ip address-set black-IP2 type object
 address 0 ************* mask 32
 address 1 *********** mask 32
 address 2 ************* mask 32
 address 3 ************ mask 32
 address 4 ************** mask 32
#
ip address-set ������ type object
 address 0 ************ mask 32
#
ip address-set ����ƽ̨������ type object
 address 0 ************* mask 32
 address 1 ************* mask 32
 address 2 ************* mask 32
 address 3 ************* mask 32
#
ip address-set �������ƽ̨ type object
 address 0 ************ mask 32
#
ip address-set ����IP��ַ type object
 address 0 ***********2 mask 32
 address 1 ************ mask 32
#
ip address-set ���� type object
 address 0 ************ mask 32
#
ip address-set �������� type object
 address 0 ***********0 mask 32
#
ip address-set GOBY�г� type object
 address 0 **********8 mask 32
#
ip address-set 10.21����ԴIP type object
 address 0 ************* mask 32
 address 1 ************** mask 32
 address 2 range ************** **************
#
ip address-set ************** type object
 address 0 ************** mask 32
#
ip address-set ������ type object
 address 0 ************ mask 32
#
ip address-set ���� type object
 address 0 ***********12 mask 32
#
ip address-set ������ȫ���� type object
 address 0 ************ mask 32
#
ip address-set �ټ��� type object
 address 0 ***********02 mask 32
#
ip address-set ��־���Ӱ� type object
 address 0 ************** mask 32
 address 1 ************* mask 32
#
ip address-set 150�Ӱ�IP type object
 address 0 ************ mask 32
#
ip address-set ����� type object
 address 0 ***********10 mask 32
#
ip address-set ���ų� type object
 address 0 172.16.10.52 mask 32
#
ip address-set ���� type object
 address 0 172.16.20.76 mask 32
#
ip address-set ***********3 type object
 address 0 ***********3 mask 32
#
ip address-set N01ԴIP�Ӱ� type object
 address 0 106.75.60.212 mask 32
#
ip address-set **********63 type object
 address 0 **********63 mask 32
#
ip address-set ����� type object
 address 0 172.16.20.44 mask 32
#
ip address-set ����� type object
 address 0 ************ mask 32
#
ip address-set �ܳɷ� type object
 address 0 172.16.20.84 mask 32
#
ip address-set ************ type object
 address 0 ************ mask 24
#
ip address-set �ػ����� type object
 address 0 172.16.10.55 mask 32
 address 1 172.16.10.66 mask 32
 address 2 172.16.10.43 mask 32
 address 3 172.16.40.30 mask 32
 address 4 172.16.10.84 mask 32
#
ip address-set falocn type group
 address 0 107.155.55.69 mask 32
 address 1 113.31.111.115 mask 32
 address 2 117.50.5.37 mask 32
 address 3 117.50.5.94 mask 32
 address 4 120.52.152.23 mask 32
 address 5 152.32.217.232 mask 32
 address 6 152.32.217.253 mask 32
 address 7 47.74.6.210 mask 32
 address 8 18.138.190.157 mask 32
 address 9 39.104.105.217 mask 32
 address 10 128.1.134.5 mask 32
 address 11 ************ mask 27
 address 12 range 123.58.250.18 123.58.250.30
 address 13 128.1.136.34 mask 32
 address 14 ************** mask 32
 address 15 103.255.179.9 mask 32
 address 16 ********** mask 16
 address 17 ************* mask 32
 address 18 118.190.70.117 mask 32
 address 19 ************ mask 32
 address 20 113.31.112.16 mask 32
 address 21 ********** mask 16
 address 22 120.132.65.20 mask 32
 address 23 *************** mask 32
 address 24 42.240.135.203 mask 32
 address 25 154.89.5.64 mask 27
 address 26 183.201.193.63 mask 32
 address 27 211.91.254.228 mask 32
 address 28 13.209.106.72 mask 32
 address 29 154.212.141.128 mask 25
#
ip address-set fofa type group
 address 0 123.115.66.0 mask 24
 address 1 123.122.92.0 mask 24
 address 2 125.33.117.0 mask 24
#
ip address-set lg-gc type group
 address 0 ************** mask 32
 address 1 122.115.232.0 mask 24
#
ip address-set ɨ�豨��IP type group
 address 0 range 10.10.11.0 ***********1
 address 1 **********18 mask 32
 address 2 range ***********3 10.10.11.254
 address 3 range ***********4 ***********6
 address 4 ***********2 mask 32
 address 5 ************ mask 32
 address 6 range ***********2 ************
 address 7 **********9 mask 32
 address 8 **********83 mask 32
 address 9 range 10.10.10.227 ************
 address 10 ***********6 mask 32
 address 11 10.10.10.229 mask 32
 address 12 **********4 mask 32
 address 13 **********6 mask 32
 address 14 **********82 mask 32
 address 15 **********60 mask 32
 address 16 **********61 mask 32
 address 17 **********7 mask 32
 address 18 **********3 mask 32
 address 19 **********5 mask 32
 address 20 range *********** **********1
 address 21 **********24 mask 32
#
ip address-set ���������� type group
 address 0 ************ mask 24
 address 1 117.50.16.112 mask 32
 address 2 106.75.70.197 mask 32
 address 3 106.75.96.169 mask 32
 address 4 117.50.110.203 mask 32
 address 5 106.75.60.62 mask 32
 address 6 106.75.57.188 mask 32
 address 7 106.75.57.211 mask 32
 address 8 106.75.118.15 mask 32
 address 9 117.50.9.2 mask 32
 address 10 117.50.65.19 mask 32
 address 11 ************** mask 32
 address 12 18.138.190.157 mask 32
 address 13 123.58.250.18 mask 32
 address 14 106.75.106.8 mask 32
 address 15 106.0.0.0 mask 8
 address 16 117.0.0.0 mask 8
 address 17 123.0.0.0 mask 8
 address 18 120.0.0.0 mask 8
 address 19 211.0.0.0 mask 8
 address 20 175.0.0.0 mask 8
 address 21 52.0.0.0 mask 8
 address 22 43.0.0.0 mask 8
 address 23 8.0.0.0 mask 8
 address 24 118.0.0.0 mask 8
 address 25 39.104.0.0 mask 16
 address 26 81.71.151.201 mask 32
 address 27 8.218.5.61 mask 32
 address 28 114.242.143.148 mask 32
 address 29 123.121.150.34 mask 32
 address 30 117.50.62.154 mask 32
 address 31 123.58.250.19 mask 32
#
ip address-set ����ӳ�� type group
 address 0 124.64.18.217 mask 32
 address 1 111.207.138.200 mask 32
 address 2 111.207.138.192 mask 32
 address 3 114.255.132.146 mask 32
#
ip address-set �ۺ��� type group
 address 0 ***********7 mask 32
 address 1 172.16.10.80 mask 32
 address 2 ***********12 mask 32
 address 3 ***********4 mask 32
 address 4 ************* mask 32
 address 5 ***********9 mask 32
 address 6 ***********9 mask 32
 address 7 ***********9 mask 32
 address 8 ***********16 mask 32
 address 9 **********6 mask 32
 address 10 ************ mask 32
 address 11 ************ mask 32
 address 12 172.16.10.31 mask 32
 address 13 ***********5 mask 32
 address 14 172.16.10.38 mask 32
#
ip address-set FOFAELK type group
 description FOFAELK
 address 0 106.75.92.55 mask 32
 address 1 211.91.254.228 mask 32
 address 2 ************** mask 32
 address 3 120.52.152.18 mask 32
#
ip address-set ��������� type group
 address 0 154.89.5.0 mask 24
 address 1 52.77.73.79 mask 32
 address 2 ************* mask 32
 address 3 106.75.98.235 mask 32
 address 4 ************** mask 32
 address 5 152.32.0.0 mask 16
 address 6 8.218.5.61 mask 32
 address 7 128.1.40.89 mask 32
 address 8 128.14.230.49 mask 32
 address 9 165.154.0.0 mask 16
 address 10 101.36.0.0 mask 16
 address 11 211.95.50.0 mask 24
 address 12 128.1.0.0 mask 16
 address 13 45.249.244.0 mask 24
 address 14 103.14.0.0 mask 16
 address 15 107.150.97.144 mask 32
 address 16 117.161.174.234 mask 32
 address 17 134.122.0.0 mask 16
 address 18 14.128.0.0 mask 16
 address 19 103.96.0.0 mask 16
 address 20 154.212.0.0 mask 16
#
ip service-set restconf type object 512
 service 0 protocol tcp source-port 0 to 65535 destination-port 8447
#
ip service-set ��΢��ά�˿� type object 513
 service 0 protocol tcp source-port 0 to 65535 destination-port 22703
 service 1 protocol tcp source-port 0 to 65535 destination-port 9081
 service 2 protocol tcp source-port 0 to 65535 destination-port 8091
#
 time-range worktime
  period-range 08:00:00 to 18:00:00 working-day   
#
acl number 2000
 rule 5 permit source ************ 0 
 rule 10 permit source ***********7 0 
 rule 20 permit source ************ 0 
 rule 30 permit source ************ 0 
 rule 35 permit source *********** 0 
 rule 45 permit source ************* 0 
 rule 50 deny 
acl number 2001
 rule 5 permit source ************ 0 
 rule 10 deny 
acl number 2002
 rule 5 permit source ************ 0 
 rule 10 deny 
acl number 2003
 rule 5 permit source ************ 0 
 rule 10 deny 
acl number 2004
 rule 5 permit source ************ 0 
 rule 15 permit source ************* 0 
 rule 20 deny 
#
acl number 3000
 description "Acl for Quintuple Packet Capture"
 rule 0 permit ip destination ************* 0 
acl number 3001
 description "Acl for Quintuple Packet Statistics"
 rule 0 permit ip source *********** 0 destination **********6 0 
acl number 3002
 description "Acl for Quintuple Packet Capture"
 rule 2 permit ip destination ************ 0 
acl number 3003
 description "Acl for Quintuple Packet Capture"
 rule 0 permit ip 
acl number 3004
 description "Acl for Quintuple Packet Capture"
 rule 0 permit ip destination ************* 0 
 rule 1 permit ip source ************* 0 
acl number 3005
 description "Acl for Quintuple Packet Capture"
 rule 0 permit ip destination *********** 0 
acl number 3006
 description "Acl for Quintuple Packet Capture"
 rule 0 permit tcp destination ************* 0 destination-port eq 11035 
acl number 3007
 description "Acl for Quintuple Packet Capture"
 rule 0 permit ip source **********9 0 
acl number 3008
 description "Acl for Quintuple Packet Capture"
 rule 0 permit ip destination ************ 0 
 rule 1 permit ip source ************ 0 
acl number 3333
 rule 5 permit ip source ************** 0 
 rule 10 permit ip source ************ 0 destination ************** 0 
 rule 15 permit ip source ************** 0 destination ************ 0 
 rule 20 permit tcp destination ************ 0 destination-port eq www 
 rule 25 permit tcp source ************ 0 source-port eq www 
#
acl ipv6 number 3333
 rule 5 permit icmpv6 
#
ike proposal default
 encryption-algorithm aes-256 aes-192 aes-128 
 dh group14 
 authentication-algorithm sha2-512 sha2-384 sha2-256 
 authentication-method pre-share
 integrity-algorithm hmac-sha2-256 
 prf hmac-sha2-256 
#
web-auth-server default
 port 50100
#
portal-access-profile name default
#
dhcpv6 pool GigabitEthernet0_0_0
 prefix-delegation 2408:8607:500::/64 64 life-time 86400 86400
 dns-server 2408:8000:1010:2::8
 dns-server 2408:8000:1010:1::8
#
aaa
 authentication-scheme admin_ad
 authentication-scheme admin_ad_local
 authentication-scheme admin_hwtacacs
 authentication-scheme admin_hwtacacs_local
 authentication-scheme admin_ldap
 authentication-scheme admin_ldap_local
 authentication-scheme admin_local
 authentication-scheme admin_radius
 authentication-scheme admin_radius_local
 authentication-scheme default
 authorization-scheme default
 accounting-scheme default
 service-scheme webServerScheme1631091292347
 domain default
  service-scheme webServerScheme1631091292347
  service-type internetaccess l2tp
  internet-access mode password
  reference user current-domain
 manager-user audit-admin 
  password cipher $1a$1DYO4:CGT<$p=|5!!oP.Xb(wJBVbx@RHAK6Im[nH)f:{~=^5P/D$
  service-type web terminal 
  level 15 
  authentication-scheme admin_local 

 manager-user admin 
  password cipher $1a$j-)rVz5EcS$@VnZ7];A"'';Kk0>:_wS~UBFCxO"_N7u5rFhk(yO$
  service-type web 
  level 15 
  acl-number 2000 
  authentication-scheme admin_local 

 manager-user restapi 
  password cipher $1a$xSwuY7Q4{~$}$y3.56fpY(+e"J84{AD-D%7DyKx.C%AExKrER.H$
  service-type api 
  level 15 
  acl-number 2004 
  authentication-scheme admin_local 

 role system-admin
 role device-admin
 role device-admin(monitor)
 role audit-admin
 role rest�Ӻ�
  dashboard none
  monitor none
  policy read-write shield
  policy none security nat traffic-policy aspf proxy-policy decryption flow-probe quota-policy slb
  object none
  network none
  system none
 bind manager-user audit-admin role audit-admin
 bind manager-user admin role system-admin
#
ntp-service server disable
ntp-service ipv6 server disable
ntp-service unicast-server ************** source-interface WAN0/0/0
#
interface Vlanif666
 description =====wifi=====
 ip address ************ *************
 alias Vlanif666
 service-manage ping permit
 dhcp server ip-range ************ **************
 dhcp select interface
 dhcp server gateway-list ************ 
 dhcp server static-bind ip-address ************* mac-address d49e-3b03-7c7b 
 dhcp server dns-list *************** ******* 
#
interface MEth0/0/0
 undo shutdown
 ip binding vpn-instance default
 ip address ***********0 *************
 service-manage http permit
 service-manage https permit
 service-manage ping permit
#
l2tp-group default-lns
#
interface GigabitEthernet0/0/0
 undo shutdown
 ipv6 enable
 ip address ********** *************
 ipv6 address 2408:8607:500::1/64
 alias ѹ������
 service-manage ping permit
 dhcp server mask ************* 
 dhcp server ip-range ********** **********54
 dhcp select interface
 dhcpv6 server GigabitEthernet0_0_0
 dhcp server gateway-list ********** 
 dhcp server dns-list ********** 
#
interface GigabitEthernet0/0/1
 undo shutdown
 service-manage ping permit
 nat64 enable
#
interface GigabitEthernet0/0/2
 undo shutdown
 undo service-manage enable
#
interface GigabitEthernet0/0/3
 undo shutdown
#
interface GigabitEthernet0/0/4
 undo shutdown
 undo service-manage enable
#
interface GigabitEthernet0/0/5
 undo shutdown
 ip address ************** ***************
 alias ����ʹ��
 service-manage ping permit
#
interface GigabitEthernet0/0/6
 undo shutdown
#
interface GigabitEthernet0/0/7
 portswitch
 undo shutdown
 port link-type trunk
 port trunk allow-pass vlan 666
 alias bmh_gust
#
interface WAN0/0/0
 undo shutdown
 ipv6 enable
 ip address ************* ***************
 ipv6 address 2408:860B:A0A::6/126
 undo ipv6 nd ra halt
 alias ��ͨ100M
 gateway *************
 service-manage ping permit
 redirect-reverse next-hop *************
#
interface WAN0/0/1
 undo shutdown
 ip address ************ ***************
 alias ����150M
 gateway ************
 service-manage ping permit
 redirect-reverse next-hop ************
#
interface XGigabitEthernet0/0/0
 undo shutdown
 ip address ********* *************
 alias �����ӿ�
 service-manage http permit
 service-manage https permit
 service-manage ping permit
 service-manage telnet permit
#
interface XGigabitEthernet0/0/1
 undo shutdown
 ip address ********** *************
 alias FOGA
 service-manage ping permit
#
interface Virtual-if0
#
interface NULL0
#
firewall zone local
 set priority 100
#
firewall zone trust
 set priority 85
 add interface GigabitEthernet0/0/0
 add interface MEth0/0/0
 add interface XGigabitEthernet0/0/0
 add interface XGigabitEthernet0/0/1
#
firewall zone untrust
 set priority 5
 add interface GigabitEthernet0/0/5
 add interface WAN0/0/0
 add interface WAN0/0/1
#
firewall zone dmz
 set priority 50
 add interface GigabitEthernet0/0/7
 add interface Vlanif666
#
api
 security server-certificate default_local.cer
 security version tlsv1.1 tlsv1.2
#
undo icmp name timestamp-request receive
undo icmp name timestamp-reply receive
undo icmp type 17 code 0 receive
undo icmp type 18 code 0 receive
#
ip route-static 0.0.0.0 0.0.0.0 WAN0/0/0 *************
ip route-static ********** ************* XGigabitEthernet0/0/0 *********
ip route-static *********** ************* *********
ip route-static *********** ************* *********
ip route-static *********** ************* *********
ip route-static *********** ************* *********
ip route-static *********** ************* *********
ip route-static *********** ************* *********
ip route-static *********** ************* *********
ip route-static *********** ************* *********
ip route-static ********** ************* *********
#
ipv6 route-static :: 0 WAN0/0/0 2408:860B:A0A::5
#
undo ssh server compatible-ssh1x enable
stelnet server enable
ssh authentication-type default password
ssh user admin
ssh user admin authentication-type password
ssh user admin service-type all
ssh user admin sftp-directory hda1:
ssh user audit-admin
ssh user audit-admin authentication-type password
ssh user audit-admin service-type all
ssh user audit-admin sftp-directory hda1:
ssh server cipher aes256_ctr aes128_ctr
ssh server hmac sha2_256 sha1
ssh client cipher aes256_ctr aes128_ctr
ssh client hmac sha2_256 sha1
ssh server dh-exchange min-len 2048
#
firewall detect ftp
#
 firewall mac-binding ************ a82b-cd00-b827
 firewall mac-binding ************* a82b-cd00-b826
#
 v-gateway public ssl version tlsv12
 v-gateway public ssl public-key algorithm rsa 
 v-gateway public ssl ciphersuit custom aes256-sha aes128-sha
 v-gateway ssl-renegotiation-attack defend enable
 v-gateway ssl weak-encryption enable
#
 nat server 1���ȱ� protocol tcp global ************* 12443 inside **********06 443 no-reverse nat-disable
 nat server 4-proxy zone untrust protocol tcp global ************* 5681 inside *********** 5681 no-reverse nat-disable
 nat server 21 protocol tcp global ************* 6029 inside *********** 6029 no-reverse
 nat server baolei-VPN protocol udp global ************* 1194 inside *********** 1194
 nat server 7-d01-i zone untrust protocol tcp global ************* 44443 inside ***********2 443 no-reverse nat-disable
 nat server 8 zone untrust protocol tcp global ************* 60088 inside *********** www no-reverse nat-disable
 nat server 12-lz-gc zone untrust protocol tcp global ************* 13000 inside **********97 3000 no-reverse nat-disable
 nat server 13-������ zone untrust protocol tcp global ************* 35767 inside ************ www no-reverse nat-disable
 nat server J���� protocol tcp global ************* 11033 inside ************ www no-reverse nat-disable
 nat server wangyu-faxianzhe1 protocol tcp global ************* 18080 inside **********63 www no-reverse nat-disable
 nat server fofa������ʾ protocol tcp global ************* 39280 inside ***********0 8000 no-reverse
 nat server 17-K01API zone untrust protocol tcp global ************* 11300 inside ***********2 3000 no-reverse
 nat server 19 protocol tcp global ************* 50081 inside ***********0 www no-reverse
 nat server 22 protocol tcp global ************* 6030 inside *********** 6030 no-reverse
 nat server hauge-VPN zone untrust protocol tcp global ************* 60011 inside ************ 60011 no-reverse
 nat server 24 protocol tcp global ************* 29999 inside *********** 443 no-reverse
 nat server J����-2 protocol tcp global ************* 11034 inside ************ 8080 no-reverse nat-disable
 nat server 27-lz-gc zone untrust protocol tcp global ************* 11443 inside ***********6 443 no-reverse nat-disable
 nat server 28-lz-gc zone untrust protocol tcp global ************* 18081 inside *********** 8081 no-reverse nat-disable
 nat server 30-fofa zone untrust protocol tcp global ************* 11080 inside *********** www no-reverse nat-disable
 nat server D��������-sentry protocol tcp global ************* 11112 inside *********** 9000 no-reverse
 nat server d�����о�Ժ protocol tcp global ************* 11113 inside 10.10.11.133 8080 no-reverse nat-disable
 nat server FOBrain��FOEYE protocol tcp global ************* 65480 inside ***********3 www no-reverse
 nat server FOBrain��FOEYE-2 protocol tcp global ************* 65443 inside ***********3 443 no-reverse
 nat server FOBrain��FOEYE-3 protocol tcp global ************* 62201 inside ***********3 2201 no-reverse
 nat server FOEYE����ƽ̨ protocol tcp global ************* 9965 inside *********** 443 no-reverse
 nat server wangyuan-10.21 protocol tcp global ************* 38085 inside *********** www no-reverse
 nat server 1������ȫ����Э��ƽ̨ protocol tcp global ************* 8808 inside *********** 8808 no-reverse
#
 undo hardware fast-forwarding enable
#
user-interface con 0
 authentication-mode aaa
user-interface vty 0 4
 authentication-mode aaa
user-interface vty 16 20
#
pki realm default
#
profile type url-filter name ��ֹtodesk
 add blacklist url *.todesk.com.*
 add blacklist host ***************
 category pre-defined subcategory-id 155 action block
 category pre-defined subcategory-id 157 action block
 category pre-defined subcategory-id 158 action block
 category pre-defined subcategory-id 231 action block
 category pre-defined subcategory-id 232 action block
 category pre-defined subcategory-id 159 action block
 category pre-defined subcategory-id 254 action block
 category pre-defined subcategory-id 160 action block
 category pre-defined subcategory-id 237 action block
 category pre-defined subcategory-id 239 action block
 default action block
 https-filter enable
#
sa
#
 domain-set name todesk 
  add domain *.todesk.com 
 domain-set name www.iconfont.cn 
  add domain www.iconfont.cn 
 domain-set name ��ֹ���ʵ���վ 
  add domain transact.netsarang.com 
  add domain update.netsarang.com 
  add domain www.netsarang.com 
  add domain www.netsarang.co.kr 
  add domain sales.netsarang.com 
 domain-set name fofapro.com 
  add domain fofapro.com 
  add domain foradar.baimaohui.net 
  add domain search-es71-3z22frtvfjbc3tkkeixxldu23u.cn-northwest-1.es.amazonaws.com.cn 
  add domain www.citicbank.com 
  add domain sslvpn.faw.cn 
  add domain www.jianyu360.cn 
  add domain www.zhipin.com 
  add domain wsnb.scjgj.beijing.gov.cn 
  add domain harbor.fofa.info 
 domain-set name ����©���� 
  add domain www.cnnvd.org.cn 
 domain-set name black-url 
  add domain googleupdate.luckfafa.com 
  add domain wpsupdate.luckfafa.com 
  add domain www.luckfafa.com 
  add domain 023vjr.com 
  add domain 58nc.com 
  add domain vm.nccuedu.site 
  add domain sogouwudiniubi.com 
  add domain sogoubaba.club 
  add domain quliaoba.com 
  add domain quliao666.com 
  add domain kazinfom.online 
  add domain help.mfadropbox.top 
  add domain ayx5222.net 
  add domain aaat.top 
 domain-set name black-url2 
  add domain freedns.afraid.org 
  add domain xred.mooo.com 
  add domain mooo.com 
  add domain mobaxterm.info 
  add domain xumming.net 
  add domain www.supbrowser.com 
  add domain abc.masktable.com 
  add domain www.masktable.com 
  add domain *.xumming.net 
  add domain *.mobaxterm.info 
  add domain *.supbrowser.com 
  add domain *.masktable.com 
  add domain *.hi4089.com 
  add domain macdn.cloudcache.org 
  add domain macache.globalacceleration.net 
  add domain xinggedafanzei.com 
  add domain qlxytj.xyz 
  add domain agentclub.shop 
  add domain agentclub.vip 
 domain-set name �������� 
  add domain endpoit.ztna-dingtalk.com 
 domain-set name ������ 
  add domain https120-52-152-67-86wdfsk5rpq8u4.ztna-dingtalk.com 
 domain-set name ������-������· 
  add domain a.ti.qianxin.com 
#
location
#
nat address-group ********* 0
 mode pat
 section 0 ********* *********
#
multi-interface
 mode proportion-of-weight
#
right-manager server-group
#
IoT
#
network-scan
 target-ip 1 *********** mask 32
 target-port 80
 network-scan timeout per-asset 300
 network-scan timeout entire-scan 23
 conflict-resolve override
#
ztna
#
device-classification
 device-group pc
 device-group mobile-terminal
 device-group undefined-group
#
user-manage single-sign-on ad
user-manage single-sign-on tsm
user-manage single-sign-on radius
user-manage auto-sync online-user
user-manage server-sync tsm
#
security-policy
 default action permit
 rule name ��ֹgust��������
  source-zone dmz
  destination-zone trust
  source-address address-set ************
  action deny
 rule name ����16.0����10.113
  source-zone trust
  source-address address-set FOGA
  destination-address **********13 mask ***************
  action permit
 rule name ����16.107����10.231
  source-zone trust
  source-address **********07 mask ***************
  destination-address ***********1 mask ***************
  service protocol tcp source-port 0 to 65535 destination-port 31080
  action permit
 rule name ����16.108����10.163
  source-zone trust
  source-address **********08 mask ***************
  destination-address **********63 mask ***************
  service protocol tcp source-port 0 to 65535 destination-port 9200
  action permit
 rule name ����16.101����11.218
  source-zone trust
  source-address **********01 mask ***************
  destination-address ************ mask ***************
  service protocol tcp source-port 0 to 65535 destination-port 32000
  action permit
 rule name ����16.101����11.130
  source-zone trust
  source-address **********01 mask ***************
  destination-address ************ mask ***************
  service protocol tcp source-port 0 to 65535 destination-port 2181
  service protocol tcp source-port 0 to 65535 destination-port 9092
  action permit
 rule name ����16.101����12.35
  source-zone trust
  source-address **********01 mask ***************
  destination-address *********** mask ***************
  service protocol tcp source-port 0 to 65535 destination-port 9000
  service protocol tcp source-port 0 to 65535 destination-port 9001
  action permit
 rule name ��ֹFOGA��������
  source-zone trust
  destination-zone dmz
  destination-zone trust
  source-address address-set FOGA
  action deny
 rule name ��ֹ��������
  destination-address address-set black-IP2
  destination-address address-set black-ip
  destination-address address-set ������ֹ���ʹ���IP
  destination-address domain-set black-url
  destination-address domain-set black-url2
  destination-address domain-set ��ֹ���ʵ���վ
  action deny
 rule name ipv6
  source-zone trust
  destination-zone untrust
  source-address address-set 2408:8607:500::
  action permit
 rule name ipv6�ܾ�
  source-zone untrust
  destination-zone trust
  destination-address address-set 2408:8607:500::
  action deny
 rule name δ��Ȩip�Ӻ�
  source-zone trust
  source-address address-set **********
  source-address address-set Vlanif10
  source-address address-set Vlanif20
  source-address address-set Vlanif30
  source-address address-set Vlanif40
  source-address address-set Vlanif50
  source-address address-set Vlanif60
  source-address address-set vlanif25-0
  source-address address-set vlanif25-1
  action deny
 rule name ����ES���Ʒ���-����A
  source-address address-set ��������ES��ַA
  destination-address address-set ES��ַA
  action permit
 rule name ����foradarES���Ʒ���-����
  source-address ***********02 mask ***************
  source-address ***********06 mask ***************
  source-address address-set ************
  destination-address ************** mask ***************
  action permit
 rule name ����IP����ONES
  source-zone untrust
  destination-zone untrust
  source-address address-set ONES�Ӱ�IP
  destination-address address-set ONES
  action permit
 rule name �ܾ�����ONES
  source-zone untrust
  destination-zone untrust
  destination-address address-set ONES
  action deny
 rule name ����ES���Ʒ���-����ELK
  source-address address-set FOFA��
  destination-address address-set FOFAELK
  action permit
 rule name �������foproes
  source-address address-set ************
  destination-address ************* mask ***************
  action permit
 rule name ����ES���Ʒ���-����
  source-address address-set �������ES��ַ
  destination-address address-set ����ES��ַ
  action permit
 rule name ����ES���Ʒ���-�ܾ�
  destination-address address-set ����ES��ַ
  action deny
 rule name ����ELK���Ʒ���-�ܾ�
  destination-address address-set FOFAELK
  action deny
 rule name ������ʷ�΢��ά�˿�
  destination-zone untrust
  source-address address-set ��΢��ά��Ա
  destination-address address-set ��΢IP��ַ
  service ��΢��ά�˿�
  action permit
 rule name ��ֹ���ʷ�΢��ά�˿�
  destination-zone untrust
  destination-address address-set ��΢IP��ַ
  service ��΢��ά�˿�
  action deny
 rule name old_vpn
  source-zone untrust
  destination-zone trust
  destination-address address-set ************
  action permit
 rule name baolei-VPN
  destination-zone trust
  destination-address address-set ***********
  action permit
 rule name FOBrain��FOEYE-3
  description ������ӳ�䣨FOBrain��FOEYE-3������
  destination-zone trust
  source-address address-set FOBrain��FOEYE
  destination-address address-set ***********3
  action permit
 rule name ����todesk
  destination-zone untrust
  source-address address-set �ػ�����
  source-address address-set �³���
  source-address address-set ������
  source-address address-set ������
  source-address address-set �����
  source-address address-set �ټ���
  source-address address-set �ۺ���
  destination-address domain-set todesk
  action permit
 rule name ��ֹtodesk
  destination-zone untrust
  destination-address domain-set todesk
  action deny
 rule name ����Զ�̹���
  destination-zone untrust
  source-address address-set �ػ�����
  source-address address-set ������
  source-address address-set ������
  source-address address-set �ټ���
  source-address address-set �ۺ���
  application app FeiQ_Remote
  application app QQ_RemoteDesktop
  application app SunLogin_Remote
  application app TeamViewer
  action permit
 rule name ��ֹԶ�̹���
  destination-zone untrust
  application app FeiQ_Remote
  application app QQ_RemoteDesktop
  application app SunLogin_Remote
  application app TeamViewer
  action deny
 rule name FOEYE����ƽ̨
  description ������ӳ�䣨FOEYE����ƽ̨������
  source-address address-set ����ƽ̨������
  destination-address *********** mask ***************
  action permit
 rule name 10.21ӳ��
  description ������ӳ��
  source-address address-set 10.21����ԴIP
  destination-address address-set ***********
  action permit
 rule name 19
  description ������ӳ�䣨19������
  destination-zone trust
  source-address address-set 150�Ӱ�IP
  destination-address ***********0 mask ***************
  action permit
 rule name 1������ȫ����Э��ƽ̨
  description ������ӳ�䣨1������ȫ����Э��ƽ̨������
  destination-zone trust
  source-address address-set ������ȫ����
  destination-address *********** mask ***************
  action permit
 rule name ��������ӳ��
  destination-zone trust
  source-address address-set ������������
  destination-address address-set ��������
  action permit
 rule name �����������
  source-zone untrust
  destination-zone trust
  source-address address-set openfalcon�Ӱ�IP
  destination-address address-set ***********
  action permit
 rule name һ��-K01API-����
  destination-zone trust
  source-address address-set һ������IP
  destination-address address-set ***********2
  action permit
 rule name ����IP����GOBY
  source-zone untrust
  destination-zone untrust
  source-address address-set **************
  source-address address-set *************
  destination-address address-set GOBY-����
  action permit
 rule name ����IP����150.34
  source-zone untrust
  destination-zone untrust
  source-address address-set 150.34�Ӱ�IP
  destination-address address-set 150.34
  action permit
 rule name ����IP����150.36
  source-zone untrust
  destination-zone untrust
  destination-address address-set **************
  service protocol tcp source-port 0 to 65535 destination-port 9092
  action permit
 rule name ��ֹ��������ONES
  source-zone untrust
  destination-zone untrust
  destination-address address-set ONES
  action deny
 rule name ��ֹ��������150.34
  source-zone untrust
  destination-zone untrust
  destination-address address-set 150.34
  action deny
 rule name gust����
  source-zone dmz
  destination-zone untrust
  action permit
 rule name 8021
  description lztest
  source-address address-set **************
  destination-address address-set ************
  action permit
 rule name 8070
  description fofascan
  source-address address-set ***************
  destination-address address-set ************
  service http
  action permit
 rule name 8080
  description fofascan
  source-address address-set **************
  destination-address address-set ************
  service http
  action permit
 rule name 8090
  description nosec
  source-address address-set ***************
  destination-address address-set **********19
  action permit
 rule name 8490
  description nosec-es
  source-address address-set ************
  destination-address address-set **********19
  action permit
 rule name 8690
  description nosec-fofapai
  source-address address-set ************
  destination-address address-set ***********
  service http
  action permit
 rule name 8790
  description nosec-gitlib
  source-address address-set ************
  source-address address-set **************
  destination-address address-set ***********
  service https
  action permit
 rule name 8808
  description openfalcon
  source-address address-set **********/16
  destination-address address-set ***********
  action permit
 rule name 8809
  description openfalcon
  source-address address-set ***************
  destination-address address-set ***********
  action permit
 rule name 8810
  description openfalcon
  source-address address-set **************
  destination-address address-set ***********
  action permit
 rule name 8811
  description openfalcon
  source-address address-set **************
  destination-address address-set ***********
  action permit
 rule name 8813
  description openfalcon
  source-address address-set falocn
  destination-address address-set ***********
  action permit
 rule name 8892
  description d01-i
  source-address address-set **************
  destination-address address-set ***********2
  service https
  action permit
 rule name 8981
  description k01api
  source-address address-set ***************
  source-address address-set *************
  source-address address-set **************
  source-address address-set *************
  destination-address address-set ***********2
  action permit
 rule name 8982
  description k01api
  source-address address-set ************
  source-address address-set **************
  destination-address address-set ***********2
  action permit
 rule name 8989
  description fofa
  source-address address-set fofa
  destination-address address-set ***********
  service http
  action permit
 rule name 8993
  description d01-i
  source-address address-set *************
  destination-address address-set ***********2
  service https
  action permit
 rule name 8994
  description lg-gc
  source-address address-set lg-gc
  destination-address address-set ***********
  action permit
 rule name 8996
  description lz-gc
  source-address address-set **************
  destination-address address-set **********97
  action permit
 rule name 8997
  description lz-gc
  source-address address-set **************
  destination-address address-set ***********6
  service https
  action permit
 rule name 8998
  description d01-i
  source-address address-set *************
  destination-address address-set ***********2
  service https
  action permit
 rule name J����
  description ������ӳ�䣨J����������
  source-zone untrust
  destination-zone trust
  source-address address-set ����ӳ��
  destination-address address-set ************
  action permit
 rule name J����-2
  description ������ӳ�䣨J����������
  source-zone untrust
  destination-zone trust
  source-address address-set **************
  source-address address-set ����ӳ��
  destination-address address-set ************
  action permit
 rule name �Ӻ�
  source-zone untrust
  destination-zone trust
  action deny
 rule name restconf
  source-zone trust
  destination-zone local
  source-address address-set ************
  destination-address address-set *********
  service restconf
  action permit
 rule name OA����ӳ��
  description ������ӳ�䣨OA����ӳ�䣩����
  disable
  destination-zone trust
  source-address address-set OA���Է���IP
  destination-address ************ mask ***************
  service protocol tcp source-port 0 to 65535 destination-port 8090
  action permit
 rule name ����-ESXi
  disable
  source-zone dmz
  source-zone local
  source-zone trust
  source-zone untrust
  destination-zone untrust
  destination-address address-set **************
  action permit
 rule name ��ʱ����-ӳ��
  description ������ӳ�䣨8��ʱ���ԣ�����
  disable
  source-zone untrust
  destination-zone dmz
  destination-address address-set ************
  action permit
 rule name ��ʱ����
  disable
  source-zone trust
  destination-zone dmz
  destination-address address-set ************
  action deny
 rule name ��ʱ����-��ֹ
  disable
  source-zone dmz
  destination-zone trust
  source-address address-set ************
  action deny
 rule name ������ʾ
  description ������ӳ�䣨1������ȫ����Э��ƽ̨������
  disable
  destination-zone trust
  source-address ************* mask ***************
  destination-address ***********2 mask ***************
  action permit
 rule name FOGA1
  description ������ӳ�䣨FOGA1������
  disable
  destination-zone trust
  source-address address-set ������������
  destination-address *********** mask ***************
  action permit
 rule name ���ȱ�ӳ��
  disable
  destination-zone trust
  source-address ************ mask ***************
  source-address ************** mask ***************
  source-address ************* mask ***************
  source-address ************* mask ***************
  destination-address **********06 mask ***************
  action permit
 rule name ������1��ӳ��
  description ������ӳ�䣨wangyu������
  disable
  destination-zone trust
  source-address address-set ������ӳ��Ӱ�IP
  destination-address address-set **********63
  action permit
 rule name 100
  disable
  source-zone trust
  destination-zone trust
  source-address address-set ����**********/23
  destination-address address-set ����**********/23
  action permit
 rule name 8022
  description ������
  disable
  source-address address-set ***************
  destination-address address-set ************
  service http
  action permit
 rule name 8030
  description fofadapin
  disable
  source-address address-set **************
  destination-address address-set ***********0
  action permit
 rule name wangyu|�˿�ӳ��
  description ������ӳ�䣨wangyu������
  disable
  source-zone untrust
  destination-zone trust
  source-address address-set ************
  source-address address-set **************
  source-address address-set **************
  source-address address-set **************
  source-address address-set **************
  source-address address-set *************
  source-address address-set *************
  destination-address address-set **********1
  action permit
 group name bmh from 8021 to J����-2
 group name C11-new
#
auth-policy
#
traffic-policy
 profile HTTPS��������
  bandwidth maximum-bandwidth whole upstream 30000
  bandwidth guaranteed-bandwidth whole upstream 10000
  bandwidth maximum-bandwidth whole downstream 30000
  bandwidth guaranteed-bandwidth whole downstream 10000
  bandwidth maximum-bandwidth per-ip upstream 30000
  bandwidth guaranteed-bandwidth per-ip upstream 10000
  bandwidth maximum-bandwidth per-ip downstream 30000
  bandwidth guaranteed-bandwidth per-ip downstream 10000
 profile ������������
  bandwidth maximum-bandwidth whole upstream 20000
  bandwidth guaranteed-bandwidth whole upstream 20000
  bandwidth maximum-bandwidth whole downstream 20000
  bandwidth guaranteed-bandwidth whole downstream 20000
  bandwidth maximum-bandwidth per-ip upstream 20000
  bandwidth guaranteed-bandwidth per-ip upstream 20000
  bandwidth maximum-bandwidth per-ip downstream 20000
  bandwidth guaranteed-bandwidth per-ip downstream 20000
 profile ��������
  bandwidth maximum-bandwidth whole upstream 30000
  bandwidth guaranteed-bandwidth whole upstream 30000
  bandwidth maximum-bandwidth whole downstream 30000
  bandwidth guaranteed-bandwidth whole downstream 30000
  bandwidth maximum-bandwidth per-ip upstream 30000
  bandwidth guaranteed-bandwidth per-ip upstream 30000
  bandwidth maximum-bandwidth per-ip downstream 30000
  bandwidth guaranteed-bandwidth per-ip downstream 30000
 profile ǿ������
  bandwidth maximum-bandwidth whole upstream 10000
  bandwidth guaranteed-bandwidth whole upstream 5000
  bandwidth maximum-bandwidth whole downstream 10000
  bandwidth guaranteed-bandwidth whole downstream 5000
  bandwidth maximum-bandwidth per-ip upstream 10000
  bandwidth guaranteed-bandwidth per-ip upstream 5000
  bandwidth maximum-bandwidth per-ip downstream 10000
  bandwidth guaranteed-bandwidth per-ip downstream 5000
 rule name ������
  disable
  source-address address-set ������IP��ַ
  action no-qos
 rule name ǿ������
  ingress-interface XGigabitEthernet0/0/0
  source-address address-set ************
  source-address address-set ����IP��ַ
  action qos profile ǿ������
 rule name ��������
  ingress-interface Vlanif666
  action qos profile ��������
 rule name HTTPS����
  ingress-interface XGigabitEthernet0/0/0
  service http
  service https
  action qos profile HTTPS��������
 rule name ��������
  ingress-interface XGigabitEthernet0/0/0
  application app 163disk
  application app BaiDu_disk
  application app Netease_DiskWeb
  application app QQDownLoad
  application app Thunder
  application app WindowsUpdate
  application app XLKC
  application app XunLeiKanKan
  application label Network-Storage
  application label P2P-Based
  application software Netease_Disk
  action qos profile ������������
#
policy-based-route
 rule name ������������ų���
  ingress-interface XGigabitEthernet0/0/0
  destination-address address-set ���������
  destination-address domain-set ������-������·
  action pbr egress-interface WAN0/0/1 next-hop ************
 rule name gust����
  ingress-interface Vlanif666
  action pbr egress-interface WAN0/0/1 next-hop ************
 rule name ipv6·��
  ingress-interface GigabitEthernet0/0/0
  source-address address-set 2408:8607:500::/64
  action pbr egress-interface WAN0/0/0 next-hop 2408:860B:A0A::5
 rule name ��������������
  ingress-interface GigabitEthernet0/0/0
  ingress-interface XGigabitEthernet0/0/0
  destination-address address-set OA
  destination-address address-set ONES
  destination-address address-set ����������
  destination-address domain-set fofapro.com
  action pbr egress-interface WAN0/0/0 next-hop *************
 rule name https��������
  ingress-interface XGigabitEthernet0/0/1
  destination-address address-set **********
  action pbr egress-interface GigabitEthernet0/0/0 next-hop **********
 rule name https��������16��
  ingress-interface GigabitEthernet0/0/0
  ingress-interface WAN0/0/0
  ingress-interface WAN0/0/1
  ingress-interface XGigabitEthernet0/0/0
  destination-address address-set FOGA
  action pbr egress-interface XGigabitEthernet0/0/1 next-hop **********
 rule name ������ѹ��
  ingress-interface XGigabitEthernet0/0/0
  destination-address address-set **********
  action pbr egress-interface GigabitEthernet0/0/0 next-hop **********
 rule name ��������
  ingress-interface XGigabitEthernet0/0/0
  application app AntsP2P
  application app Microsoft_Store
  application app Vuze_P2P
  application app WindowsUpdate
  application category Entertainment
  application category Entertainment sub-category Peercasting
  application category General_Internet
  application category General_Internet sub-category FileShare_P2P
  application label Bandwidth-Consuming
  application label P2P-Based
  application label Productivity-Loss
  application label Social-Applications
  application software AliTalk
  application software BaiDuHi
  application software BaiDu_Disk
  application software BuGuNiao
  application software CTDisk
  application software CaiFuTong
  application software CaiYun
  application software Chanjet
  application software CinemaNow
  application software DaZhiHui
  application software DoShow
  application software DouBan
  application software DuDu
  application software Facebook
  application software FeiQ
  application software Fetion
  application software Fxiaoke
  application software GangGuKuaiChe
  application software GleasyDisk
  application software Good.gd
  application software KaiXin
  application software NBA
  application software Netease_Disk
  application software POBO
  application software PSBC-PaymentSystem
  application software PocketDisk
  application software QQ
  application software QianLongStock
  application software QiannaoDisk
  application software RenRen
  application software Socks
  application software Sonork_EIM
  application software SpeedShare
  application software Steam
  application software TangRenJie
  application software TianYiCloud
  application software TongDaXin
  application software UCYun
  application software WangYicc
  application software WeCall
  application software WeChatWork
  application software WeiKeDisk
  application software WeiYun
  application software WeiboDesktop
  application software WenHuaCaiJing
  application software WoCloud
  application software XiaoNeiTong
  application software YY
  application software YahooMsg
  application software YiMuHeWangPan
  application software YouPeng
  application software Ys168
  action pbr egress-interface WAN0/0/1 next-hop ************
 rule name ɨ����-�칫
  ingress-interface GigabitEthernet0/0/0
  source-address address-set **********
  destination-address address-set ***********
  destination-address address-set ***********
  destination-address address-set ***********
  destination-address address-set ***********
  destination-address address-set ����**********/23
  action pbr egress-interface XGigabitEthernet0/0/0 next-hop *********
 rule name FOGA��·
  ingress-interface XGigabitEthernet0/0/1
  destination-address address-set ***********
  destination-address address-set ***********
  destination-address address-set ***********
  destination-address address-set ***********
  destination-address address-set ����**********/23
  action pbr egress-interface XGigabitEthernet0/0/0 next-hop *********
 rule name ѹ�����Ի�·
  ingress-interface XGigabitEthernet0/0/0
  source-address address-set ɨ�豨��IP
  destination-address address-set **********
  action pbr egress-interface GigabitEthernet0/0/0 next-hop **********
 rule name ������������
  ingress-interface GigabitEthernet0/0/0
  ingress-interface XGigabitEthernet0/0/0
  ingress-interface XGigabitEthernet0/0/1
  source-address address-set gust-vlan
  source-address address-set ɨ�豨��IP
  source-address address-set ѹ������IP
  action pbr egress-interface WAN0/0/1 next-hop ************
 rule name ѹ��������������
  ingress-interface GigabitEthernet0/0/0
  ingress-interface XGigabitEthernet0/0/1
  action pbr egress-interface WAN0/0/1 next-hop ************
 rule name https�������ų���
  disable
  ingress-interface XGigabitEthernet0/0/0
  service http
  service https
  action pbr egress-interface WAN0/0/1 next-hop ************
 rule name FOGA����
  disable
  ingress-interface XGigabitEthernet0/0/1
  action pbr egress-interface WAN0/0/1 next-hop ***********
 rule name ����-�׿챨
  disable
  ingress-interface XGigabitEthernet0/0/0
  destination-address address-set ����-�׿챨
  action pbr egress-interface WAN0/0/1 next-hop ***********
 rule name 20-server
  disable
  ingress-interface GigabitEthernet0/0/2
  source-address address-set ***********
  destination-address address-set ����**********/23
  action pbr egress-interface GigabitEthernet0/0/3 next-hop *********
 rule name server-�칫
  disable
  ingress-interface XGigabitEthernet0/0/0
  source-address address-set ����**********/23
  destination-address address-set ***********
  action pbr egress-interface GigabitEthernet0/0/1 next-hop ***********
 rule name server-��3
  disable
  ingress-interface XGigabitEthernet0/0/0
  source-address address-set ����**********/23
  destination-address address-set ***********
  action pbr egress-interface GigabitEthernet0/0/2 next-hop ***********
 rule name server-��2
  disable
  ingress-interface XGigabitEthernet0/0/0
  source-address address-set ����**********/23
  destination-address address-set ***********
  action pbr egress-interface GigabitEthernet0/0/4 next-hop ***********
 rule name server-��4
  disable
  ingress-interface XGigabitEthernet0/0/0
  source-address address-set ����**********/23
  destination-address address-set ***********
  action pbr egress-interface GigabitEthernet0/0/6 next-hop ***********
 rule name ����ɨ��ip����***********
  disable
  ingress-interface GigabitEthernet0/0/0
  source-address address-set ɨ�豨��IP
  destination-address address-set ***********/24
  action pbr egress-interface GigabitEthernet0/0/4 next-hop ***********
 rule name ����ɨ��ip����***********
  disable
  ingress-interface GigabitEthernet0/0/0
  source-address address-set ɨ�豨��IP
  destination-address address-set ***********
  action pbr egress-interface GigabitEthernet0/0/3 next-hop ***********
 rule name ����ɨ��ip����***********
  disable
  ingress-interface GigabitEthernet0/0/0
  source-address address-set ************
  source-address address-set ɨ�豨��IP
  destination-address address-set ***********
  action pbr egress-interface GigabitEthernet0/0/2 next-hop ***********
 rule name ����ɨ��ip����**********/23
  disable
  ingress-interface GigabitEthernet0/0/0
  source-address address-set ɨ�豨��IP
  destination-address address-set ����**********/23
  action pbr egress-interface GigabitEthernet0/0/0 next-hop **********
 rule name ����ɨ��ip����***********
  disable
  ingress-interface GigabitEthernet0/0/0
  source-address address-set ************
  source-address address-set ɨ�豨��IP
  destination-address address-set ***********
  action pbr egress-interface GigabitEthernet0/0/1 next-hop ***********
 rule name ����-���������ã�
  disable
  ingress-interface XGigabitEthernet0/0/0
  destination-address address-set **************/28
  action pbr egress-interface GigabitEthernet0/0/5 next-hop **************
 rule name ����ͨר��
  disable
  ingress-interface XGigabitEthernet0/0/0
  source-address address-set ************
  source-address address-set ************
  source-address address-set ************
  source-address address-set ************
  action pbr egress-interface WAN0/0/0 next-hop *************
 rule name �ļ������л�
  disable
  ingress-interface XGigabitEthernet0/0/0
  application app 163disk
  application app 51gugu
  application app BaiDu_disk
  application app Ctdisk
  application app DingTalk_Live
  application app KaiXin_MicroAPP_NetworkStorage
  application app KooWo
  application app NS_115Disk_Download
  application app NS_115Disk_Upload
  application app NS_CTDisk_Download
  application app NS_CTDisk_Upload
  application app NS_GleasyDisk_Download
  application app NS_GleasyDisk_Upload
  application app NS_QianNaoDisk_Upload
  application app NS_Qiannaodisk
  application app NS_Qiannaodisk_Download
  application app NS_Rapidgator_Download
  application app NS_Rapidgator_Upload
  application app NS_RayFile_Download
  application app NS_RayFile_Upload
  application app NS_TianYiCloud_Download
  application app NS_TianYiCloud_Upload
  application app NS_Turbobit_Download
  application app NS_Turbobit_Upload
  application app NS_Uploades_Download
  application app NS_Uploades_Upload
  application app NS_Uyun
  application app NS_Uyun_Download
  application app NS_Uyun_Upload
  application app NS_WeiKeDisk
  application app NS_WeiKeDisk_Download
  application app NS_WeiKeDisk_Upload
  application app NS_WeiYun_Download
  application app NS_WeiYun_Upload
  application app NS_YiMuHeWangPan_Download
  application app NS_YiMuHeWangPan_Upload
  application app NS_Ys168
  application app NS_Ys168_Download
  application app NS_Ys168_Upload
  application app Netease_DiskWeb
  application app QQMusic
  application app Uploadstation
  application app WangYiMusic
  application app WeChatWork
  application app WeChatWork_FileTransfer
  application app WebMail_DingTalk_Mail
  application app WeiXin_FileTransfer
  application app WindowsUpdate
  application category General_Internet sub-category FileShare_P2P
  application label Network-Storage
  application software 115Disk
  application software 126Disk
  application software 163Disk
  application software CTDisk
  application software Good.gd
  application software Netease_Disk
  application software PocketDisk
  application software QiannaoDisk
  application software WeiKeDisk
  application software YiMuHeWangPan
  application software Ys168
  action pbr egress-interface WAN0/0/1 next-hop ***********
#
nat-policy
 rule name ����
  source-zone trust
  destination-zone untrust
  action source-nat easy-ip
 rule name guest
  source-zone dmz
  destination-zone untrust
  source-address address-set ************
  action source-nat easy-ip
#
proxy-policy
#
quota-policy
#
pcp-policy
#
dns-transparent-policy
 dns transparent-proxy enable
 dns server bind interface WAN0/0/0 preferred ************ alternate *************** 
 dns server bind interface WAN0/0/1 preferred ************** alternate ************ 
 dns server bind interface XGigabitEthernet0/0/0 preferred *************** alternate ******* 
 dns server bind interface Vlanif666 preferred *************** alternate ******* 
 dns server bind interface GigabitEthernet0/0/5 preferred *************** alternate ******* 
 dns server bind interface GigabitEthernet0/0/0 preferred *************** alternate ******* 
 dns server bind interface XGigabitEthernet0/0/1 preferred *************** alternate ******* 
 mode based-on-multi-interface
#
rightm-policy
#
decryption-policy
#
flow-probe-policy
#
mac-access-profile name mac_access_profile
#
return  