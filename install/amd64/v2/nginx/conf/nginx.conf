worker_processes  1;

events {
    worker_connections  1024;
}

http {
    include       mime.types;
    default_type  application/octet-stream;
    sendfile        on;
    keepalive_timeout  65;
    #server_tag off;
    #server_info off;
    #server_tokens off;
    access_log  /var/log/nginx/fobrain.access.log;
    error_log   /var/log/nginx/fobrain.error.log;

    # WebSocket长链接
    map $http_upgrade $connection_upgrade {
        default upgrade;
        ''      close;
    }

    # 80端口重定向到443
    server {
        listen 80;
        server_name _;
        error_page 301 /_use_https_;
        location = /_use_https_ {
            internal;
            return 200 "Please use HTTPS";
        }
        location / {
            return 301 https://$host$request_uri;
        }
    }
    server {
        #侦听443端口
        listen       443  ssl http2;
        index index.php index.html index.htm;
        #设定本虚拟主机的访问日志
        server_name  _;
        root /data/dist;
        #这个选项使服务器在 sendfile 时可以提前准备 HTTP 首部，能够达到优化吞吐的效果。
        tcp_nopush on;
        # 开启 sendfile 选项，使用内核的 FD 文件传输功能，这个比在用户态用 read() + write() 的方式更加高效。
        sendfile on;
        # 允许服务器在客户端停止发送应答之后关闭连接，以便释放连接相应的 socket 内存开销。
        reset_timedout_connection on;
        # 缓存高频操作文件的FDs（文件描述符/文件句柄）
        open_file_cache max=200000 inactive=20s;
        open_file_cache_valid 30s;
        open_file_cache_min_uses 2;
        open_file_cache_errors on;
        #Gzip
        gzip on;
        gzip_vary      on;
        gzip_disable "MSIE [1-6]\.";
        gzip_proxied any;
        gzip_min_length 1;
        gzip_comp_level 4;
        gzip_types text/plain text/css application/json application/x-javascript text/xml application/xml application/xml+rss text/javascript image/svg+xml image/png image/x-icon application/javascript application/octet-stream;
        gzip_buffers 4 16k;
        #SSL 处理
        ssl_certificate "/data/certs/fobrain.pem";
        ssl_certificate_key "/data/certs/fobrain.key";
        ssl_session_cache shared:SSL:1m;
        ssl_session_timeout  10m;
        ssl_prefer_server_ciphers on;
        ssl_protocols TLSv1.2;
        ssl_ciphers "ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA:ECDHE-RSA-AES128-SHA:DHE-RSA-AES256-SHA256:DHE-RSA-AES128-SHA256:DHE-RSA-AES256-SHA:DHE-RSA-AES128-SHA:ECDHE-RSA-DES-CBC3-SHA:EDH-RSA-DES-CBC3-SHA:AES256-GCM-SHA384:AES128-GCM-SHA256:AES256-SHA256:AES128-SHA256:AES256-SHA:AES128-SHA:DES-CBC3-SHA:HIGH:!aNULL:!eNULL:!EXPORT:!DES:!MD5:!PSK:!RC4";
        #日志
        access_log  /var/log/nginx/fobrain.access.log;
        error_log   /var/log/nginx/fobrain.error.log;
        #文件上传大小配置
        client_max_body_size     5000m;
        client_header_timeout    10m;
        client_body_timeout      10m;
        proxy_connect_timeout     60s;
        proxy_read_timeout      10m;
        proxy_send_timeout      10m;
        # Header处理
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";
        add_header X-Frame-Options sameorigin;
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS, DELETE, PUT';
        add_header Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';
        #OPTIONS处理
        if ($request_method = 'OPTIONS') {
            return 204;
        }
        #Foradar前端地址
        location / {
            root /data/dist;
            try_files $uri $uri/ /index.html;
        }
        #WebScoket
        location /websocket {
               proxy_send_timeout 300s;
               proxy_read_timeout 300s;
               proxy_http_version 1.1;
               proxy_set_header X-Real-IP $remote_addr;
               proxy_set_header X-Real-PORT $remote_port;
               proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
               proxy_set_header Host $http_host;
               proxy_set_header Scheme $scheme;
               proxy_set_header Server-Protocol $server_protocol;
               proxy_set_header Server-Name $server_name;
               proxy_set_header Server-Addr $server_addr;
               proxy_set_header Server-Port $server_port;
               proxy_set_header Upgrade $http_upgrade;
               proxy_set_header Connection $connection_upgrade;
               proxy_pass http://127.0.0.1:8090/websocket;
        }
        #Foradar API
        location /api {
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Real-PORT $remote_port;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header Host $http_host;
		    proxy_pass http://127.0.0.1:8090/api/v1;
        }
        # 单点登录
        location /oauth2_callback {
            proxy_pass         http://127.0.0.1:8090/oauth2_callback;
            proxy_set_header   Host $host;
            proxy_set_header   X-Real-IP $remote_addr;
            proxy_set_header   X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header   X-Forwarded-Proto $scheme;
        }
        location /oauth2_login {
            proxy_pass         http://127.0.0.1:8090/oauth2_login;
            proxy_set_header   Host $host;
            proxy_set_header   X-Real-IP $remote_addr;
            proxy_set_header   X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header   X-Forwarded-Proto $scheme;
        }
        location /4a_login {
            proxy_pass         http://127.0.0.1:8090/4a_login;
            proxy_set_header   Host $host;
            proxy_set_header   X-Real-IP $remote_addr;
            proxy_set_header   X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header   X-Forwarded-Proto $scheme;
        }

        location /sign_out {
            proxy_pass         http://127.0.0.1:8090/sign_out;
            proxy_set_header   Host $host;
            proxy_set_header   X-Real-IP $remote_addr;
            proxy_set_header   X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header   X-Forwarded-Proto $scheme;
        }
        # 兼容兼容Foeye/D01
        location /foeyeApi/auth {
            proxy_pass         http://127.0.0.1:8090/foeyeApi/auth;
            proxy_set_header   Host $host;
            proxy_set_header   X-Real-IP $remote_addr;
            proxy_set_header   X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header   X-Forwarded-Proto $scheme;
        }
        location /foeyeApi/auth/sftpPasswd {
            proxy_pass         http://127.0.0.1:8090/foeyeApi/auth/sftpPasswd;
            proxy_set_header   Host $host;
            proxy_set_header   X-Real-IP $remote_addr;
            proxy_set_header   X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header   X-Forwarded-Proto $scheme;
        }
        # 文件请求
        location ~ /storage {
        	#允许跨域请求的域，*代表所有
        	add_header 'Access-Control-Allow-Origin' *;
        	#允许带上cookie请求
        	add_header 'Access-Control-Allow-Credentials' 'true';
        	#允许请求的方法，比如 GET/POST/PUT/DELETE
        	add_header 'Access-Control-Allow-Methods' *;
        	#允许请求的header
        	add_header 'Access-Control-Allow-Headers' *;
        	root   /data/fobrain;
        }
    }
}
