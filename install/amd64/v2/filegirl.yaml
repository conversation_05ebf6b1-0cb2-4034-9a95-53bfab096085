# 主配置
core:
    version: 1

# 监控配置
monitor:
    # 监听根目录下所有子目录
    includeDirs:
        - flag-dir,*

    # 监听所有文件类型
    types:
        - .*

    # 监听创建事件
    events:
        - create

# 命令
command:
    # 当检测到 pkg 文件夹被创建时，执行监控
    exec:
      - echo "!!!yaml 检测到upgrade文件夹下有create事件"  {{file}}
      - echo "!!!yaml 开始执行fileboy_script.sh ----" {{file}}
      - ./fileboy_script.sh {{file}} 
      - echo "!!!yaml fileboy_script.sh执行结束 ----" {{file}}

    # 文件变更后命令在2秒后才会执行，减少冗余和重复任务的执行
    delayMillSecond: 2000

# 通知器
notifier:
    # 不启用远程通知
    callUrl: ""

# 特殊指令
instruction:
    - ignore-warn