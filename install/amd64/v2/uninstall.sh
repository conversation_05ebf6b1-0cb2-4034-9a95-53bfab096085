#!/bin/bash

# 检查是否有管理员权限
if [ "$(id -u)" -ne 0 ]; then
  echo "请使用 root 用户或具有管理员权限的用户执行此脚本。"
  exit 1
fi

# 定义卸载日志文件
current_dir="$PWD"
log_file=${current_dir}/uninstall_`date +%s`.log

echo "开始卸载程序..." | tee -a ${log_file}

# 停止并删除所有Docker容器
echo "停止并删除Docker容器..." | tee -a ${log_file}
cd /data/fobrain/
if [ -f "docker-compose.yaml" ]; then
    docker-compose down -v 2>&1 | tee -a ${log_file}
fi

# 停止并禁用系统服务
echo "停止并禁用系统服务..." | tee -a ${log_file}
systemctl stop nginx 2>&1 | tee -a ${log_file}
systemctl disable nginx 2>&1 | tee -a ${log_file}
systemctl stop docker 2>&1 | tee -a ${log_file}
systemctl disable docker 2>&1 | tee -a ${log_file}
systemctl stop fileboy 2>&1 | tee -a ${log_file}
systemctl disable fileboy 2>&1 | tee -a ${log_file}

# 删除系统服务文件
echo "删除系统服务文件..." | tee -a ${log_file}
rm -f /etc/systemd/system/docker.service 2>&1 | tee -a ${log_file}
rm -f /etc/systemd/system/fileboy.service 2>&1 | tee -a ${log_file}
systemctl daemon-reload 2>&1 | tee -a ${log_file}

# 删除Docker相关文件
echo "删除Docker相关文件..." | tee -a ${log_file}
rm -rf /usr/bin/docker* 2>&1 | tee -a ${log_file}
rm -f /usr/local/bin/docker-compose 2>&1 | tee -a ${log_file}

# 删除fileboy
echo "删除fileboy..." | tee -a ${log_file}
rm -f /usr/local/bin/fileboy 2>&1 | tee -a ${log_file}

# 删除所有创建的目录
echo "删除所有创建的目录..." | tee -a ${log_file}
directories=(
    "/data/dist"
    "/data/certs"
    "/data/fobrain"
    "/data/storage"
    "/etc/fobrain"
    "/data/docker"
)

for dir in "${directories[@]}"; do
    if [ -d "$dir" ]; then
        echo "删除目录 $dir ..." | tee -a ${log_file}
        rm -rf "$dir" 2>&1 | tee -a ${log_file}
    fi
done

# 删除Docker数据目录
echo "删除Docker数据目录..." | tee -a ${log_file}
rm -rf /var/lib/docker 2>&1 | tee -a ${log_file}

# 删除Nginx配置文件
echo "删除Nginx配置文件..." | tee -a ${log_file}
rm -rf /etc/nginx 2>&1 | tee -a ${log_file}

echo "卸载完成。" | tee -a ${log_file}
echo "注意：某些系统文件可能需要手动清理。" | tee -a ${log_file}