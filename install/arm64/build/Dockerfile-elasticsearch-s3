# Elasticsearch with S3 repository plugin for testing
FROM docker.eustis.top/library/elasticsearch:7.17.26

# 安装repository-s3插件以支持云存储测试
RUN elasticsearch-plugin install --batch repository-s3

# 确保插件安装成功
RUN elasticsearch-plugin list | grep repository-s3


# arm64本地构建 docker build -f Dockerfile-es-s3 -t es-arm64:7.17.26 .

# 导出 docker save -o es-arm64_7.17.26.tar es-arm64:7.17.26
# 发送到服务器（示例用 scp） scp es-arm64_7.17.26.tar user@your-server:/path
# 服务器上加载 docker load -i es-arm64_7.17.26.tar
# docker-compose.yml image: es-arm64:7.17.26
