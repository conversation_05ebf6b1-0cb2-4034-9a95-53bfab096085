FROM --platform=linux/arm64 rockylinux:8

# 替换默认源为阿里云，提升稳定性和速度
RUN sed -i 's|^mirrorlist=|#mirrorlist=|g' /etc/yum.repos.d/Rocky-AppStream.repo && \
    sed -i 's|^#baseurl=http://dl.rockylinux.org/$contentdir|baseurl=https://mirrors.aliyun.com/rockylinux|g' /etc/yum.repos.d/Rocky-AppStream.repo && \
    sed -i 's|^mirrorlist=|#mirrorlist=|g' /etc/yum.repos.d/Rocky-BaseOS.repo && \
    sed -i 's|^#baseurl=http://dl.rockylinux.org/$contentdir|baseurl=https://mirrors.aliyun.com/rockylinux|g' /etc/yum.repos.d/Rocky-BaseOS.repo

# 安装基础工具与 OpenSSL3 支持
RUN dnf install -y epel-release && \
    dnf install -y \
      bash which xz tar gzip curl vim-minimal procps-ng net-tools iproute less \
      libstdc++ glibc openssl3 openssl3-libs && \
    dnf clean all

# 下载并解压 ARM64 对应架构的 MySQL 工具（版本需兼容 glibc 2.28）
RUN curl -LO https://downloads.mysql.com/archives/get/p/23/file/mysql-8.0.37-linux-glibc2.28-aarch64.tar.xz && \
    tar -xf mysql-8.0.37-linux-glibc2.28-aarch64.tar.xz && \
    cp mysql-8.0.37-linux-glibc2.28-aarch64/bin/mysqlbinlog \
       mysql-8.0.37-linux-glibc2.28-aarch64/bin/mysql \
       mysql-8.0.37-linux-glibc2.28-aarch64/bin/mysqldump \
       mysql-8.0.37-linux-glibc2.28-aarch64/bin/mysqladmin /usr/local/bin/ && \
    chmod +x /usr/local/bin/{mysqlbinlog,mysql,mysqldump,mysqladmin} && \
    rm -rf mysql-8.0.37-linux-glibc2.28-aarch64*

# 验证 MySQL 工具链是否安装成功
RUN mysqlbinlog --version && mysql --version && mysqldump --version && mysqladmin --version

ENV LANG=en_US.UTF-8