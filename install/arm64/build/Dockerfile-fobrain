FROM --platform=linux/arm64 rockylinux:8

EXPOSE 8090
WORKDIR /data
VOLUME ["/data/storage"]

# 复制 fobrain 二进制与配置文件
COPY ./fobrain/cmd /usr/sbin/cmd
COPY ./fobrain/fobrain /usr/sbin/fobrain
COPY ./fobrain/fobrain.json /etc/fobrain/conf/config.json
COPY ./fobrain/version /etc/fobrain/conf/version

ENTRYPOINT ["/bin/bash", "-c", "/usr/sbin/fobrain"]
CMD []

###############################################################################
# 本地arm64架构构建
# 构建镜像
#docker build -f Dockerfile-fobrain-base -t rockylinux:8-arm64 .

# 保存镜像到 tar 文件
#docker save rockylinux:8-arm64 -o rockylinux-8-arm64.tar

#服务器加载
# docker load -i rockylinux-8-arm64.tar
#加载后显示 Loaded image: rockylinux:8-arm64

# dockerfile使用FROM rockylinux:8-arm64