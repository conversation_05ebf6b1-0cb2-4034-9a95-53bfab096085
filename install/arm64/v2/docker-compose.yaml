# version: "3.3" docker compose 2.0以后不用加version，加上会影响系统升级程序
services:
  mysql:
    privileged: true
    image: mysql:8.0.38
    extra_hosts:
      - "host.docker.internal:host-gateway"
    container_name: mysql
    network_mode: "host"
    restart: always
    ports:
      - "3306:3306"
    command:
      --explicit_defaults_for_timestamp=true
      --lower_case_table_names=1
      --default-authentication-plugin=mysql_native_password
      --bind-address=0.0.0.0
    environment:
      - LANG=C.UTF-8
      - TZ=Asia/Shanghai
      - MYSQL_ROOT_PASSWORD=Fobrain@@#13244%!
      - MYSQL_DATABASE=fobrain
      - MYSQL_ROOT_HOST=%
    volumes:
      - /data/fobrain/storage/data/mysql:/var/lib/mysql
      - /data/fobrain/storage/logs/mysql:/logs
      # - /data/fobrain/storage/conf/mysql/conf.d:/etc/mysql/conf.d
      # - /etc/resolv.conf:/etc/resolv.conf
    ulimits:
      nproc: 165536
      nofile:
        soft: 165536
        hard: 165536
      memlock:
        soft: -1
        hard: -1
  redis:
    privileged: true
    restart: always
    image: redis:6.2.1
    extra_hosts:
      - "host.docker.internal:host-gateway"
    container_name: redis
    network_mode: "host"
    ports:
      - "6379:6379"
    command: >
      --ignore-warnings ARM64-COW-BUG
      --requirepass "Fobrain@7391%"
      --bind 0.0.0.0
    environment:
      - LANG=C.UTF-8
      - TZ=Asia/Shanghai
      - LANG=en_US.UTF-8
    volumes:
      - /data/fobrain/storage/data/redis:/data
      - /data/fobrain/storage/logs/redis:/logs
      # - /etc/resolv.conf:/etc/resolv.conf
    ulimits:
      nproc: 165536
      nofile:
        soft: 165536
        hard: 165536
      memlock:
        soft: -1
        hard: -1
    healthcheck:
      test: ["CMD-SHELL", "redis-cli --no-auth-warning -a \"Fobrain@7391%\" ping | grep PONG"]
      interval: 30s
      timeout: 10s
      retries: 3
  es:
    privileged: true
    restart: always
    image: es-arm64:7.17.26
    extra_hosts:
      - "host.docker.internal:host-gateway"
    container_name: es
    network_mode: "host"
    ports:
      - "9200:9200"
      - "9300:9300"
    environment:
      - LANG=C.UTF-8
      - ES_JAVA_OPTS=-Xms16g -Xmx32g
      - cluster.name=elasticsearch
      - discovery.type=single-node
      - bootstrap.memory_lock=true
      - ingest.geoip.downloader.enabled=false
      - TZ=Asia/Shanghai
      - "network.host=127.0.0.1"
      - "path.repo=/usr/share/elasticsearch/data/snapshot"
    ulimits:
      nproc: 165536
      nofile:
        soft: 165536
        hard: 165536
      memlock:
        soft: -1
        hard: -1
    # healthcheck:
    #   test: ["CMD", "curl", "-f", "http://localhost:9200"]
    #   interval: 30s
    #   timeout: 10s
    #   retries: 5
    volumes:
      - /data/fobrain/storage/logs/es:/usr/share/elasticsearch/logs
      - /data/fobrain/storage/data/es:/usr/share/elasticsearch/data
      - /data/fobrain/storage/backup:/usr/share/elasticsearch/data/snapshot
      # - /etc/resolv.conf:/etc/resolv.conf
  consul:
    privileged: true
    restart: always
    image: consul:latest
    extra_hosts:
      - "host.docker.internal:host-gateway"
    container_name: consul
    network_mode: "host"
    ports:
      - "8500:8500"
    command: agent -dev -client=0.0.0.0
    volumes:
      - /data/fobrain/storage/consul_data:/consul/data
  fobrain:
    # privileged 表示是否使用特权模式，特权模式下的容器可以访问主机上的所有设备，包括网络设备。
    privileged: true
    container_name: fobrain
    network_mode: "host"
    restart: always
    image: fobrain-arm64:latest
    stop_grace_period: 60s #等待60s退出
    extra_hosts:
      - "host.docker.internal:host-gateway"
      - "127.0.0.1:host-gateway"
    volumes:
      - /data/fobrain/storage:/data/fobrain/storage
      # - /etc/resolv.conf:/etc/resolv.conf
      - /data/fobrain:/data/fobrain
      - /etc/fobrain/conf/config.json:/etc/fobrain/conf/config.json
    environment:
      LANG: C.UTF-8
      TZ: Asia/Shanghai
    entrypoint: sh -c '/usr/sbin/fobrain'
    depends_on:
      - mysql
      - es
      - redis
    deploy:
      replicas: 1
      labels:
        bmh.foradar.service.type: code
      restart_policy:
        condition: any
        delay: 5s
        window: 120s
      update_config:
        parallelism: 1
        delay: 10s
        failure_action: continue
        monitor: 60s
        max_failure_ratio: 0.3
    ports:
      - "8090:8090"
    ulimits:
      nproc: 165536
      nofile:
        soft: 165536
        hard: 165536
      memlock:
        soft: -1
        hard: -1
    # healthcheck:
    #   test: ["CMD-SHELL", "curl --fail http://localhost:443 || exit 1"]
    #   interval: 30s
    #   timeout: 10s
    #   retries: 5
  merge-service:
    privileged: true
    # container_name: fobrain-merge-service
    network_mode: "host"
    restart: always
    image: merge-service-arm64:latest
    stop_grace_period: 60s #等待60s退出
    # extra_hosts 表示额外的主机名映射，用于在容器内部访问外部主机。
    extra_hosts:
      - "host.docker.internal:host-gateway"
      - "127.0.0.1:host-gateway"
    volumes:
      - /data/fobrain/storage:/data/fobrain/storage
      # - /etc/resolv.conf:/etc/resolv.conf
      - /data/fobrain:/data/fobrain
      - /etc/fobrain/conf/config.json:/etc/fobrain/conf/config.json
    environment:
      LANG: C.UTF-8
      TZ: Asia/Shanghai
    entrypoint: ["/bin/sh", "-c", "/usr/sbin/mergeservice"]
    depends_on:
      - mysql
      - es
      - redis
    deploy:
      replicas: 1
      labels:
        bmh.foradar.service.type: code
      restart_policy:
        condition: any
        delay: 5s
        window: 120s
      update_config:
        parallelism: 1
        delay: 10s
        failure_action: continue
        monitor: 60s
        max_failure_ratio: 0.3
    ulimits:
      nproc: 165536
      nofile:
        soft: 165536
        hard: 165536
      memlock:
        soft: -1
        hard: -1
  nginx:
    privileged: true
    image: nginx:latest
    network_mode: "host"
    container_name: nginx
    restart: always
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - /data/dist:/data/dist
      - /data/storage/logs/nginx:/var/log/nginx
      - /data/certs:/data/certs
      - /etc/nginx:/etc/nginx
      - /data/fobrain:/data/fobrain
    environment:
      - NGINX_HOST=localhost
      - NGINX_PORT=80