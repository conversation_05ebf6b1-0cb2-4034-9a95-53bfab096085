#!/bin/bash
# 当前安装目录
current_dir="$PWD"
# 安装目录
install_dir="/data/fobrain-arm64"
# Docker数据保存目录
docker_save_dir="/data/docker/data-root"
#网卡名称
eth_name=""
#网卡IP
ip_addr=""
#安装日志文件
log_file=${current_dir}/install_`date +%s`.log
#获取用户输入配置
function getUserInputSetting() {
    #获取网卡列表
    ethMenus=()
    eths=(`ifconfig | grep ^[a-z] | awk -F: '{print $1}'|grep -v docker0|grep -v lo`)
    for ((i=0;i<${#eths[@]};i++)) do
        ipv4=`ifconfig ${eths[$i]}|grep inet|grep -v inet6 |awk '{print $2}'`
        if [[ "$ipv4" != "" ]];then
          ethMenus+=("网卡名:${eths[i]},IP地址:${ipv4}")
          ethMenus+=("")
        fi
    done
    #选择安装网卡
    select_use_eht=$(whiptail --title "FOBrain安装" --cancel-button "取消" --menu "请选择使用网卡:" 15 70 5 "${ethMenus[@]}" 3>&1 1>&2 2>&3- 0>&-)
    if [ "$select_use_eht" == "" ]; then
      exit 0;
    else
      eth_name=`echo $select_use_eht |awk -F',' '{print $1}'|awk -F':' '{print $2}'`
      ip_addr=`echo $select_use_eht |awk -F',' '{print $2}'|awk -F':' '{print $2}'`
    fi
    # FOBrain安装目录处理
    FOBRAIN_INPUT_INSTALL_DIR=$(whiptail --title "FOBrain安装" --cancel-button "取消" --inputbox "请输入您的安装目录:" 10 60 ${install_dir} 3>&1 1>&2 2>&3)
    if [ $? == 0 ]; then
        install_dir=${FOBRAIN_INPUT_INSTALL_DIR}
        mkdir -p ${install_dir}
    else
      exit 0;
    fi
    # DOCKER安装目录处理
    DOCKER_INPUT_INSTALL_DIR=$(whiptail --title "FOBrain安装" --cancel-button "取消" --inputbox "请输入您的Docker数据保存目录:" 10 60 ${docker_save_dir} 3>&1 1>&2 2>&3)
    if [ $? == 0 ]; then
        docker_save_dir=${DOCKER_INPUT_INSTALL_DIR}
        mkdir -p ${docker_save_dir} && mkdir -p /etc/docker/
    else
      exit 0;
    fi
    #防火墙配置
    if (whiptail --title "FOBrain安装" --yes-button "启用&添加策略" --cancel-button "取消" --no-button "忽略(不做任何处理)"  --yesno "是否使用Firewalld防火墙?" 10 60) then
        enable_firewalld=1
    fi
}
# 安装数据存储服务
function installStorageService() {
    cd ${current_dir}
    #获取机器内存大小&计算ES内存
    total_mem=$(free -g | awk '/^Mem:/ {print $2}')
    half_mem=$(( total_mem / 2 ))
    sed -i "s#ES_JAVA_OPTS=-Xms15g -Xmx15g#ES_JAVA_OPTS=-Xms${half_mem}g -Xmx${half_mem}g#g" ${install_dir}/docker-compose.yaml
    echo -e "XXX\n15\n加载Es镜像文件... \nXXX" 2>&1 | tee -a ${log_file}
    docker load -i images/es 2>&1 | tee -a ${log_file}
    echo -e "XXX\n20\n加载Redis镜像文件... \nXXX" 2>&1 | tee -a ${log_file}
    docker load -i images/redis 2>&1 | tee -a ${log_file}
    echo -e "XXX\n25\n加载MySql镜像文件... \nXXX" 2>&1 | tee -a ${log_file}
    docker load -i images/mysql 2>&1 | tee -a ${log_file}
    echo -e "XXX\n35\n加载FOBrain镜像文件... \nXXX" 2>&1 | tee -a ${log_file}
    docker load -i images/fobrain 2>&1 | tee -a ${log_file}
}
# CopyFOBrain系统文件
function copyFOBrainToTargetDir() {
    echo -e "XXX\n6\n安装系统依赖环境... \nXXX" 2>&1 | tee -a ${log_file}
    #Copy安装文件
    /bin/cp -rf fobrain/fobrain/* ${install_dir}  2>&1 | tee -a ${log_file}
    echo -e "XXX\n6.3\n安装系统依赖环境... \nXXX" 2>&1 | tee -a ${log_file}
    #设置docker-compose中的安装目录
    sudo sed -i "s#/data/fobrain#${install_dir}#g" ${install_dir}/docker-compose.yaml 2>&1 | tee -a ${log_file}
    echo -e "XXX\n6.6\n安装系统依赖环境... \nXXX" 2>&1 | tee -a ${log_file}
    # 设置目录权限
    chmod 777 ${install_dir}/storage -Rf 2>&1 | tee -a ${log_file}
    echo -e "XXX\n6.8\n安装系统依赖环境... \nXXX" 2>&1 | tee -a ${log_file}
}
# 安装docker环境
function installDockerTools() {
    echo -e "XXX\n5\n安装Docker环境... \nXXX" 2>&1 | tee -a ${log_file}
    #安装docker和docker-compose
    /bin/cp -rf docker/bin/* /usr/bin/ 2>&1 | tee -a ${log_file}
    /bin/cp -rf docker/docker.service /usr/lib/systemd/system/ 2>&1 | tee -a ${log_file}
    if [ -f docker/containerd.service ];then /bin/cp -rf docker/containerd.service /usr/lib/systemd/system/ 2>&1 | tee -a ${log_file}; fi
    if [ -f docker/docker.socket ];then /bin/cp -rf docker/docker.socket /usr/lib/systemd/system/ 2>&1 | tee -a ${log_file}; fi
    #设置执行权限
    chmod 755 /usr/bin/docker* 2>&1 | tee -a ${log_file}
    chmod 755 /usr/bin/runc 2>&1 | tee -a ${log_file}
    chmod 755 /usr/bin/ctr 2>&1 | tee -a ${log_file}
    chmod 755 /usr/bin/containerd* 2>&1 | tee -a ${log_file}
# 写入Docker配置
cat >/etc/docker/daemon.json <<EOF
{
    "ipv6": true,
    "fixed-cidr-v6": "2001:db8:1::/64",
    "data-root": "${docker_save_dir}"
}
EOF
    cat /etc/docker/daemon.json 2>&1 | tee -a ${log_file}
    #启动Docker
    systemctl enable docker  2>&1 | tee -a ${log_file}
    systemctl start docker  2>&1 | tee -a ${log_file}
    sleep 10;
    timeout=180  # 设置等待时间,3分钟，单位为秒,
    start_time=$(date +%s)  # 获取当前时间戳
    #等待docker启动
    while [ "$(systemctl is-active docker)" != "active" ]; do
      sleep 3;
      #在启动中时,等待启动完成
      if [ "$(systemctl is-active docker)" != "activating" ];then
        systemctl start docker  2>&1 | tee -a ${log_file}
      fi
      #检查是否超时,超时自动退出
      current_time=$(date +%s)
      elapsed_time=$((current_time - start_time))
      if [ $elapsed_time -ge $timeout ]; then
          echo -e "XXX\n100\n安装失败... \nXXX" 2>&1 | tee -a ${log_file}
          whiptail --title "FOBrain安装" --msgbox "FOBrain安装异常,Docker启动超时,请检查安装日志排查异常原因" 10 60
          exit 1;
      fi
    done
}
# 初始化系统信息
function initSystemInfo() {
    echo -e "XXX\n2\n系统配置初始化... \nXXX" 2>&1 | tee -a ${log_file}
    #禁止网卡修改/etc/resolv.conf,并删除IPV6异常配置
    sed -i "/%/d" /etc/resolv.conf 2>&1 | tee -a ${log_file}
    chattr -i /etc/resolv.conf 2>&1 | tee -a ${log_file}
    #临时设置ulimit
    ulimit -HSn 165536 2>&1 | tee -a ${log_file}
    ulimit -HSu 102400 2>&1 | tee -a ${log_file}
    #永久设置,重启生效,Centos7
    if [ -f /etc/security/limits.d/20-nproc.conf ];then
        if [ `cat /etc/security/limits.d/20-nproc.conf|grep "* hard nproc 102400" |wc -l` == 0 ];then
              echo -e "* hard nproc 102400\n* soft nproc 102400\n* hard nofile 165536\n* soft nofile 165536" >>/etc/security/limits.d/20-nproc.conf 2>&1 | tee -a ${log_file}
        fi
    else
        echo -e "* hard nproc 102400\n* soft nproc 102400\n* hard nofile 165536\n* soft nofile 165536" >>/etc/security/limits.d/20-nproc.conf 2>&1 | tee -a ${log_file}
    fi
}

# 配置系统防火墙
function settingSystemFirewalld() {
  if [ $enable_firewalld == 1 ];then
    echo -e "XXX\n70\n配置系统防火墙... \nXXX" 2>&1 | tee -a ${log_file}
    # 未启动防火墙时,启动防火墙
    if [ `systemctl is-active firewalld|grep inactive |wc -l` == 1 ]; then
      systemctl enable firewalld 2>&1 | tee -a ${log_file}
      systemctl start firewalld 2>&1 | tee -a ${log_file}
      # 等待访问获取启动完成
      sleep 3;
    fi
    # 没有IPV4转发时添加转发,存在时修改为1
    if [[ $(cat /etc/sysctl.conf | grep "net.ipv4.ip_forward" | wc -l) != 1 ]]; then
      echo 'net.ipv4.ip_forward = 1' >>/etc/sysctl.conf 2>&1 | tee -a ${log_file}
      sysctl net.ipv4.ip_forward=1 2>&1 | tee -a ${log_file}
    else
      sed -i "/^net.ipv4.ip_forward*/cnet.ipv4.ip_forward = 1" /etc/sysctl.conf 2>&1 | tee -a ${log_file}
    fi
    # 修改vm.overcommit_memory
    if [[ $(cat /etc/sysctl.conf | grep "vm.overcommit_memory" | wc -l) != 1 ]]; then
      echo 'vm.overcommit_memory = 1' >>/etc/sysctl.conf 2>&1 | tee -a ${log_file}
      sysctl vm.overcommit_memory=1 2>&1 | tee -a ${log_file}
    else
      sed -i "/^vm.overcommit_memory*/cvm.overcommit_memory = 1" /etc/sysctl.conf 2>&1 | tee -a ${log_file}
    fi
    # 修改net.core.somaxconn
    if [[ $(cat /etc/sysctl.conf | grep "net.core.somaxconn" | wc -l) != 1 ]]; then
      echo 'net.core.somaxconn = 1024' >>/etc/sysctl.conf 2>&1 | tee -a ${log_file}
      sysctl net.core.somaxconn=1024 2>&1 | tee -a ${log_file}
      echo 1024 >/proc/sys/net/core/somaxconn 2>&1 | tee -a ${log_file}
    else
      sed -i "/^net.core.somaxconn*/cnet.core.somaxconn = 1024" /etc/sysctl.conf 2>&1 | tee -a ${log_file}
    fi
    echo 1024 >/proc/sys/net/core/somaxconn 2>&1 | tee -a ${log_file}
    # 设置防火墙转发生效
    sysctl -p 2>&1 | tee -a ${log_file}
    echo -e "XXX\n75\n开启系统访问端口... \nXXX" 2>&1 | tee -a ${log_file}
    # 业务节点,开启80/443端口
    firewall-cmd --zone=public --add-port=80/tcp --permanent 2>&1 | tee -a ${log_file}
    firewall-cmd --zone=public --add-port=443/tcp  --permanent 2>&1 | tee -a ${log_file}
    # 添加允许ip地址伪装
    firewall-cmd --add-masquerade --permanent 2>&1 | tee -a ${log_file}
    # 端口管理交给firewall
    firewall-cmd --zone=trusted --remove-interface=docker0 --permanent 2>&1 | tee -a ${log_file}
    #重启防火墙
    sudo firewall-cmd --reload 2>&1 | tee -a ${log_file}
  fi
}

# 安装FOBrain Service
function installServiceByFOBrain() {
    echo -e "XXX\n90\n安装系统系统项... \nXXX" 2>&1 | tee -a ${log_file}
    # 启动项目
    /bin/cp -rf ${current_dir}/fobrain/fobrain/fobrain.service /usr/lib/systemd/system/ 2>&1 | tee -a ${log_file}
    chmod 644 /usr/lib/systemd/system/fobrain.service 2>&1 | tee -a ${log_file}
    #设置fobrain.service中的安装目录
    sudo sed -i "s#/data/fobrain#${install_dir}#g" /usr/lib/systemd/system/fobrain.service 2>&1 | tee -a ${log_file}
    #删除安装目录中的service
    unlink ${install_dir}/fobrain.service 2>&1 | tee -a ${log_file}
    #启动FORadar
    echo -e "XXX\n95\n启动系统... \nXXX" 2>&1 | tee -a ${log_file}
    systemctl daemon-reload 2>&1 | tee -a ${log_file}
    systemctl enable fobrain 2>&1 | tee -a ${log_file}
    systemctl start fobrain 2>&1 | tee -a ${log_file}
    #等待系统启动完成
    timeout=180  # 设置等待时间,3分钟，单位为秒,
    start_time=$(date +%s)  # 获取当前时间戳
    while [ `curl -m 3 -s https://127.0.0.1 -k |grep "html"|wc -l` -eq 0 ]; do
      #系统启动完成,退出循环
      if [ `curl -m 3 -s https://127.0.0.1 -k |grep "html"|wc -l` -eq 1 ];then
        break;
      fi
      #每次循环,等待3秒
      echo "等待系统启动完成...." 2>&1 | tee -a ${log_file}
      sleep 3;
      #检查是否超时,超时自动退出
      current_time=$(date +%s)
      elapsed_time=$((current_time - start_time))
      if [ $elapsed_time -ge $timeout ]; then
          whiptail --title "FOBrain安装" --msgbox "FOBrain安装异常,系统启动超时,请检查安装日志排查异常原因" 10 60
          exit 1;
      fi
    done;
}
#-----------------------------------------------主程序开始
#获取用户选择配置
# shellcheck disable=SC2218
getUserInputSetting
# 开始安装
{
    #初始化系统基础信息
    initSystemInfo
    #安装Docker环境
    installDockerTools
    #安装FORadar依赖环境
    copyFOBrainToTargetDir
    #安装数据存储服务
    installStorageService
    # 配置系统防火墙
    settingSystemFirewalld
    # 安装系统启动项
    installServiceByFOBrain
    echo -e "XXX\n100\n安装完成... \nXXX" 2>&1 | tee -a ${log_file}
} |whiptail --title "FOBrain安装" --gauge "FOBrain安装中........." 6 60 0
if [ $? -eq 0 ]; then
    whiptail --title "FORadar2.0安装" --msgbox "            恭喜您FOBrain安装完成!
                系统访问地址为: https://${ip_addr}
                系统按照路径为: ${install_dir}
                容器存储目录为: ${docker_save_dir}
                感谢您的使用~~~" 15 60
else
    # 安装异常
    whiptail --title "FOBrain安装" --msgbox "FOBrain安装异常,请检查安装日志排查异常原因" 10 60
fi