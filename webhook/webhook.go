package webhook

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"fobrain/webhook/dao"
	"fobrain/webhook/dingtalk"
	"io"
	"net/http"
	"sync"
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/panjf2000/ants"
	"github.com/pkg/errors"
	"go-micro.dev/v4/logger"
)

type Webhook struct {
	quitConsumerChan chan struct{}
	quitDbChan       chan struct{}
	insertDb<PERSON>han     chan *insertDbRecord
	insertDbCount    int64
	antsPool         *ants.PoolWithFunc
	wg               sync.WaitGroup
}

func NewWebhook(conf *Config) *Webhook {
	w := &Webhook{
		quitConsumerChan: make(chan struct{}),
		quitDbChan:       make(chan struct{}),
		insertDbChan:     make(chan *insertDbRecord),
		insertDbCount:    10,
		wg:               sync.WaitGroup{},
	}
	pool, _ := ants.NewPoolWithFunc(20, func(i interface{}) {
		defer w.wg.Done()
		w.send(i)
	})
	w.antsPool = pool

	redisCli = initRedis(conf.Redis)
	dao.InitMysql(conf.Mysql)

	return w
}

func (w *Webhook) Start() {
	logger.Info("[Webhook] webhook starting")
	w.insertDb()
	w.consumer()
	logger.Info("[Webhook] webhook started")
}

func (w *Webhook) Stop() {
	logger.Info("[Webhook] webhook stopping")
	// 关闭消息接受通道
	w.quitConsumerChan <- struct{}{}
	// 等待所有结果写入数据库
	<-w.quitDbChan
	// 等待线程池释放
	logger.Info("[Webhook] wait release ants pool")
	w.antsPool.Release()
	logger.Info("[Webhook] webhook stoped")
}

const webhookRedisKey = "fobrain:webhook:eventQueue"

type Sender interface {
	SendMsg() error
}

type SenderMsg struct {
	UrlConf    dao.WebhookEventUrlConfigModel
	Info       []byte
	RetryCount int64
}

func (w *Webhook) consumer() {
	logger.Info("[Webhook] run consumer goroutine")
	go func() {
		for {
			r, err := redisCli.BRPop(context.TODO(), time.Second*5, webhookRedisKey).Result()
			if err == redis.Nil {
				if err == redis.Nil {
					// 通过 BRPop 超时控制是否退出
					select {
					case <-w.quitConsumerChan:
						time.Sleep(time.Second * 2)
						// 关闭 chan 通知 run 停止
						close(w.insertDbChan)
						return
					default:
						continue
					}
				}
			}
			if len(r) < 2 { // 检查 r 的长度
				logger.Warn("接收到的消息格式不正确,丢弃:", r)
				continue
			}
			w.wg.Add(1)
			_ = w.antsPool.Invoke([]byte(r[1]))
		}
	}()
}

var sendFuncMap = map[int]func(msg *SenderMsg) error{
	dao.AuthTypeNoAuth:   commonSend,
	dao.AuthTypeToken:    commonSend,
	dao.AuthTypeDingtalk: dingtalkSend,

	dao.AuthTypeZhongyiFeishu: zhongyiFeishuSend,
}

func GetAuthType() []int {
	var t = make([]int, 0)
	for k := range sendFuncMap {
		t = append(t, k)
	}
	return t
}

func (w *Webhook) send(i interface{}) error {
	// 处理消息
	data := i.([]byte)
	var msg SenderMsg
	if err := json.Unmarshal(data, &msg); err != nil {
		fmt.Printf("消息反序列化失败: %v\n", err)
		return nil
	}

	fn, ok := sendFuncMap[msg.UrlConf.AuthType]
	if !ok {
		logger.Errorf("不支持的SDK类型:%d, msg:%+v\n", msg.UrlConf.AuthType, msg)
		return nil
	}
	var infoMao map[string]interface{}
	if err := json.Unmarshal(msg.Info, &infoMao); err != nil {
		logger.Errorf("消息反序列化失败: %v\n", err)
		return nil
	}
	event, _ := infoMao["event"].(string)
	businessid, _ := infoMao["business_id"].(string)
	msguuid, _ := infoMao["msg_uuid"].(string)
	var result = &insertDbRecord{
		WebhookConfigId: msg.UrlConf.Id,
		Status:          1,
		Event:           event,
		BusinessId:      businessid,
		MsgUuid:         msguuid,
		MsgInfo:         string(msg.Info),
	}
	if err := fn(&msg); err != nil {
		logger.Errorf("发送失败:%d, msg:%+v\n", msg.UrlConf.AuthType, msg)
		result.Status = 0
		result.ErrMsg = err.Error()
	}

	w.insertDbChan <- result
	return nil
}

type insertDbRecord struct {
	WebhookConfigId uint64
	MsgInfo         string
	Status          int64
	ErrMsg          string
	Event           string
	BusinessId      string
	MsgUuid         string
}

func (w *Webhook) insertDb() {
	logger.Info("[Webhook] run inserting goroutine")
	result := make([]*insertDbRecord, 0, w.insertDbCount)

	go func() {
		t := time.NewTicker(time.Second * 3)
		defer t.Stop()
		for {
			select {
			case <-t.C:
				if len(result) > 0 {
					_ = w.SaveLog(result)
					result = result[:0]
				}
			case r, ok := <-w.insertDbChan:
				if ok {
					result = append(result, r)
					if len(result) >= int(w.insertDbCount) {
						_ = w.SaveLog(result)
						result = result[:0]
						time.Sleep(time.Second * 3)
					}
				} else {
					if len(result) > 0 {
						_ = w.SaveLog(result)
						result = result[:0]
					}
					w.quitDbChan <- struct{}{}
					return
				}

			}
		}
	}()
}

func (w *Webhook) SaveLog(data []*insertDbRecord) error {
	for _, v := range data {
		err := dao.NewWebhookEventSendResultModel().Create(&dao.WebhookEventSendResultModel{
			WebhookConfigId: v.WebhookConfigId,
			MsgInfo:         v.MsgInfo,
			Status:          v.Status,
			ErrMsg:          v.ErrMsg,
			Event:           v.Event,
			BusinessId:      v.BusinessId,
			MsgUuid:         v.MsgUuid,
		})
		if err != nil {
			logger.Error("SaveLog failed", *v)
		}
	}
	return nil
}

func dingtalkSend(msg *SenderMsg) error {
	dingCli, err := dingtalk.GetDingTalkClient(msg.UrlConf.ConfigMd5Hash)
	if err != nil {
		dingtalk.InitDingRobot(msg.UrlConf.ConfigMd5Hash, msg.UrlConf.Host, msg.UrlConf.TokenValue, msg.UrlConf.Secret, false)
		dingCli, err = dingtalk.GetDingTalkClient(msg.UrlConf.ConfigMd5Hash)
		if err != nil {
			return errors.WithMessage(err, "获取dingtalk client 失败"+msg.UrlConf.ConfigMd5Hash)
		}
	}

	err = dingCli.SendMsg(string(msg.Info))
	if err != nil {
		return errors.WithMessage(err, "钉钉消息发送失败")
	}
	return nil
}

func commonSend(m *SenderMsg) error {
	// 构建请求
	req, err := http.NewRequest("POST", m.UrlConf.Host, bytes.NewReader(m.Info))
	if err != nil {
		return err
	}

	// 设置 Token
	if m.UrlConf.URLTokenName != "" {
		q := req.URL.Query()
		q.Add(m.UrlConf.URLTokenName, m.UrlConf.TokenValue)
		req.URL.RawQuery = q.Encode()
	}

	if m.UrlConf.HeaderTokenName != "" {
		req.Header.Add(m.UrlConf.HeaderTokenName, m.UrlConf.TokenValue)
	}

	// 发送请求
	client := &http.Client{Timeout: 3 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	// 检查响应
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("请求失败，状态码:%d,响应体:%s", resp.StatusCode, body)
	}
	return nil
}
