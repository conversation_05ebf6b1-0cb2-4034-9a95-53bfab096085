package webhook

import (
	"fmt"
	"fobrain/webhook/dao"
	"time"

	"github.com/go-redis/redis/v8"
)

var redisCli *redis.Client

type Config struct {
	Redis RedisConfig
	Mysql dao.MysqlConfig
}
type RedisConfig struct {
	Address            string `json:"address"`
	Port               uint16 `json:"port"`
	Password           string `json:"password"`
	Database           int    `json:"database"`
	MergeRuleCacheTime int    `json:"merge_rule_cache_time"` // 融合规则缓存时间,单位分钟
}

func initRedis(conf RedisConfig) *redis.Client {
	rc := redis.NewClient(&redis.Options{
		Addr:     fmt.Sprintf("%s:%d", conf.Address, conf.Port), // redis服务ip:port
		Password: conf.Password,                                 // redis的认证密码
		DB:       conf.Database,                                 // 连接的database库
		// 连接池大小会根据 CPU 数量自动调整
		// PoolSize:    30,                                            // 连接池
		MinIdleConns: 10,               // 最小空闲连接数
		PoolTimeout:  10 * time.Second, // 连接池最大等待时间
		IdleTimeout:  5 * time.Minute,  // 空闲连接最大保持时间
	})

	return rc
}
