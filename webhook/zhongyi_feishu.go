package webhook

import (
	"encoding/json"
	"fmt"
	license2 "fobrain/fobrain/app/repository/settings/license"
	"fobrain/fobrain/app/services/node/zhongyi_feishu"
	"fobrain/fobrain/common/license"
	"fobrain/models/mysql/user_staff"
	"fobrain/pkg/cfg"
	"fobrain/pkg/utils"
	"fobrain/webhook/dao"
	"github.com/google/uuid"
	"github.com/pkg/errors"
	"go-micro.dev/v4/logger"
)

func init() {
	sources, err := license.GetLicense().GetDataSources(license2.CheckDevModel(cfg.GetInstance().Common.Env))
	if err != nil {
		logger.Errorf("webhook zhongyi feishu get license data sources error: %v", err)
		return
	}
	if utils.InArray("zhongyi_feishu", sources) || utils.InArray("all", sources) {
		sendFuncMap[dao.AuthTypeZhongyiFeishu] = zhongyiFeishuSend
	}
}

type ZhongyiFeishuSendRequest struct {
	MsgType    string `json:"msg_type,omitempty"`
	Content    string `json:"content,omitempty"`
	ReceiverId string `json:"receive_id,omitempty"`
	Uuid       string `json:"uuid,omitempty"`
}

type ZhongyiFeishuSendResp struct {
	Code    interface{} `json:"code"`    // 返回，非200失败
	Msg     string      `json:"message"` //错误信息
	Details interface{} `json:"details"`
	Data    interface{} `json:"outData"` //返回消息集合
}

// 中移香港飞书简版卡片示例
type FeishuSimpleCard struct {
	Config struct {
		UpdateMulti bool `json:"update_multi"`
	} `json:"config"`
	I18NElements struct {
		ZhCn []I18NElements `json:"zh_cn"`
	} `json:"i18n_elements"`
	I18NHeader struct {
	} `json:"i18n_header"`
}

type I18NElements struct {
	Tag       string `json:"tag"`
	Content   string `json:"content"`
	TextAlign string `json:"text_align"`
	TextSize  string `json:"text_size"`
}

/*
	{
	    "config": {
	        "update_multi": true
	    },
	    "i18n_elements": {
	        "zh_cn": [
	            {
	                "tag": "markdown",
	                "content": "[有一个漏洞派发给您，请登录系统查看](https://open.feishu.cn/document/server-docs/im-v1/message-reaction/emojis-introduce)",
	                "text_align": "left",
	                "text_size": "normal"
	            }
	        ]
	    },
	    "i18n_header": {}
	}
*/
func zhongyiFeishuSend(msg *SenderMsg) error {
	var info VulnerabilityMsg
	err := json.Unmarshal(msg.Info, &info)
	if err != nil {
		return err
	}
	logger.Infof("-----zhongyiFeishuSend SenderMsg:event%+v\n ", info.Event)

	if err != nil {
		return errors.WithMessage(err, "zhongyiFeishuSend序列化cardMsg失败")
	}

	msguuid := info.MsgUuid
	if msguuid == "" {
		msguuid = uuid.New().String()
	}
	cli := zhongyi_feishu.NewZhongyiFeishuSdk(msg.UrlConf.SysKey, msg.UrlConf.Secret, msg.UrlConf.Host)
	url := fmt.Sprintf("%s/bcoc/feishu/message/v1/auth_sendMessages?receive_id_type=user_id", msg.UrlConf.Host)

	var users []ReceiverUser

	for _, d := range info.Details {
		users = append(users, d.ReceiverUsers...)
	}

	for _, user := range users {

		var cardMsg = FeishuSimpleCard{
			I18NHeader: struct{}{},
			Config: struct {
				UpdateMulti bool `json:"update_multi"`
			}{UpdateMulti: true},

			I18NElements: struct {
				ZhCn []I18NElements `json:"zh_cn"`
			}{
				ZhCn: []I18NElements{
					{
						Tag:       "markdown",
						Content:   fmt.Sprintf("[%s](%s)", info.Title, msg.UrlConf.JumpLink),
						TextAlign: "left",
						TextSize:  "normal",
					},
				},
			},
		}

		if user.Fid == "" || !user_staff.NewUserRoleModel().ExistByStaffId(user.FidHash) {
			cardMsg.I18NElements.ZhCn = append(cardMsg.I18NElements.ZhCn, I18NElements{
				Tag:       "markdown",
				Content:   "暂无资产安全管理平台账号，请联系相关负责人注册",
				TextAlign: "left",
				TextSize:  "normal",
			})
		}

		cardbyte, err := json.Marshal(cardMsg)

		var reqBody = ZhongyiFeishuSendRequest{
			Uuid:       msguuid,
			MsgType:    "interactive",
			Content:    string(cardbyte),
			ReceiverId: user.SsoId,
		}
		var resp ZhongyiFeishuSendResp
		err = cli.BcocHttpPost(url, reqBody, &resp)
		if err != nil {
			logger.Errorf("zhongyiFeishuSend err:%v,收到响应信息:%+v", err, resp)
		}
		logger.Debugf("zhongyiFeishuSend收到响应信息:%+v", resp)
	}

	return nil
}
