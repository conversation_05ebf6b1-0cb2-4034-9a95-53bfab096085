package webhook

import (
	"encoding/json"
	"fobrain/webhook/dao"
	"fobrain/webhook/dingtalk"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/go-redis/redis/v8"
	"github.com/panjf2000/ants"
	"github.com/stretchr/testify/assert"
	"go-micro.dev/v4/logger"
)

func TestInitRedis(t *testing.T) {
	conf := RedisConfig{
		Address:  "localhost",
		Port:     6379,
		Password: "",
		Database: 0,
	}

	patches := gomonkey.ApplyFunc(redis.NewClient, func(opt *redis.Options) *redis.Client {
		client := &redis.Client{}
		return client
	})
	defer patches.Reset()

	// 调用 initRedis
	_ = initRedis(conf)

}
func TestNewWebhook(t *testing.T) {
	conf := &Config{
		Redis: RedisConfig{
			Address:  "localhost",
			Port:     6379,
			Password: "",
			Database: 0,
		},
	}
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyFuncReturn(dao.InitMysql).Reset()
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyMethodReturn(dao.NewWebhookEventUrlConfigModel(), "List", []*dao.WebhookEventUrlConfigModel{}, int64(0), nil).Reset()
	webhook := NewWebhook(conf)

	// 确保 insertDbCount 的类型匹配
	webhook.insertDbCount = int64(10) // 确保这里是 int64 类型

	// 验证 webhook 对象是否正确初始化
	assert.NotNil(t, webhook)
	assert.NotNil(t, webhook.quitConsumerChan)
	assert.NotNil(t, webhook.quitDbChan)
	assert.NotNil(t, webhook.insertDbChan)
	assert.Equal(t, int64(10), webhook.insertDbCount)
	assert.NotNil(t, webhook.antsPool)
}

func TestWebhook_Start(t *testing.T) {
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyFuncReturn(dao.InitMysql).Reset()
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyFuncReturn(dao.InitMysql).Reset()
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyMethodReturn(dao.NewWebhookEventUrlConfigModel(), "List", []*dao.WebhookEventUrlConfigModel{}, int64(0), nil).Reset()

	// 创建 Webhook 实例
	w := NewWebhook(&Config{})

	// 使用 gomonkey 模拟 logger.Info
	patches := gomonkey.ApplyFunc(logger.Info, func(args ...interface{}) {
		// 模拟 logger.Info 的行为
	})

	defer patches.Reset()

	// 调用 Start 方法
	w.Start()

}

func TestWebhook_SaveLog(t *testing.T) {
	// 创建一个 Webhook 实例
	w := &Webhook{}

	// 测试数据
	data := []*insertDbRecord{
		{},
		{},
	}

	patches := gomonkey.ApplyMethodReturn(&dao.WebhookEventSendResultModel{}, "Create", nil)
	defer patches.Reset()

	err := w.SaveLog(data)

	assert.NoError(t, err)

}

func TestWebhook_dingtalkSend(t *testing.T) {
	// 创建一个 Webhook 实例

	// 创建一个 SenderMsg 实例
	msg := &SenderMsg{
		UrlConf: dao.WebhookEventUrlConfigModel{
			ConfigMd5Hash: "test_md5",
		},
		Info: []byte("测试用例"),
	}

	// 模拟 dingtalk.GetDingTalkClient 方法
	patches := gomonkey.ApplyFuncReturn(dingtalk.GetDingTalkClient, &dingtalk.DingRobot{}, nil)
	defer patches.Reset()

	// 模拟 SendMsg 方法
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyMethodReturn(&dingtalk.DingRobot{}, "SendMsg", nil).Reset()

	// 调用 dingtalkSend 方法
	err := dingtalkSend(msg)

	// 断言没有错误
	assert.NoError(t, err)
}

func TestWebhook_send(t *testing.T) {
	// 创建一个 Webhook 实例
	w := &Webhook{
		insertDbChan: make(chan *insertDbRecord, 1), // 使用缓冲通道
	}

	// 测试数据
	msg := SenderMsg{
		UrlConf: dao.WebhookEventUrlConfigModel{
			AuthType: dao.AuthTypeDingtalk,
		},
		Info: []byte(`{"event":"vul_distribute","business_id":"123","msg_uuid":"testMsgUuid"}`),
	}

	// 序列化消息
	data, err := json.Marshal(msg)
	if err != nil {
		t.Fatalf("消息序列化失败: %v", err)
	}

	// 测试成功的情况
	err = w.send(data)
	assert.NoError(t, err)

	// 检查 insertDbChan 是否接收到数据
	select {
	case result := <-w.insertDbChan:
		assert.NotNil(t, result)
	default:
		t.Error("insertDbChan 没有接收到数据")
	}

	// 测试反序列化失败的情况
	err = w.send([]byte("invalid data"))
	assert.NoError(t, err) // 反序列化失败时不应返回错误

	// 测试不支持的 AuthType
	msg.UrlConf.AuthType = 999
	data, err = json.Marshal(msg)
	if err != nil {
		t.Fatalf("消息序列化失败: %v", err)
	}
	err = w.send(data)
	assert.NoError(t, err) // 不支持的 AuthType 也不应返回错误
}

func TestWebhook_insertDb(t *testing.T) {
	// 创建一个 Webhook 实例
	w := &Webhook{
		insertDbChan:  make(chan *insertDbRecord, 1), // 使用缓冲通道
		quitDbChan:    make(chan struct{}),
		insertDbCount: 2, // 设置插入数据库的计数
	}

	// 模拟 SaveLog 方法
	patches := gomonkey.ApplyMethodReturn(w, "SaveLog", nil)
	defer patches.Reset()

	// 启动 insertDb 方法
	go w.insertDb()

	// 发送测试数据到 insertDbChan
	for i := 0; i < 3; i++ {
		w.insertDbChan <- &insertDbRecord{}
	}

	// 等待一段时间以确保 goroutine 处理完
	time.Sleep(time.Second * 5)

	// 关闭 insertDbChan
	close(w.insertDbChan)

	// 等待 goroutine 完成
	<-w.quitDbChan

}

func TestWebhook_Stop(t *testing.T) {
	w := &Webhook{
		quitConsumerChan: make(chan struct{}),
		quitDbChan:       make(chan struct{}),
		insertDbChan:     make(chan *insertDbRecord),
		insertDbCount:    10,
	}
	pool, _ := ants.NewPoolWithFunc(20, func(i interface{}) {
		defer w.wg.Done()
		w.send(i)
	})
	w.antsPool = pool
	//gomonkey.ApplyMethodReturn(&ants.PoolWithFunc{}, "Release")
	go func() {
		<-w.quitConsumerChan
		w.quitDbChan <- struct{}{}
	}()
	w.Stop()
}

func TestCommonSend(t *testing.T) {
	// 创建一个模拟的 SenderMsg
	m := &SenderMsg{
		UrlConf: dao.WebhookEventUrlConfigModel{
			Host:            "http://localhost:8080/test",
			URLTokenName:    "token",
			TokenValue:      "test-token",
			HeaderTokenName: "Authorization",
		},
		Info: []byte("测试信息"),
	}

	// 创建一个模拟的 HTTP 服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// 检查请求的 Token
		if r.URL.Query().Get("token") != "test-token" {
			t.Errorf("期望 token 为 'test-token', 但得到 %s", r.URL.Query().Get("token"))
		}
		if r.Header.Get("Authorization") != "test-token" {
			t.Errorf("期望 Authorization 为 'test-token', 但得到 %s", r.Header.Get("Authorization"))
		}
		w.WriteHeader(http.StatusOK)
	}))
	defer server.Close()

	// 更新 SenderMsg 的 Host 为模拟服务器的地址
	m.UrlConf.Host = server.URL

	// 调用 commonSend 函数
	err := commonSend(m)
	if err != nil {
		t.Errorf("期望没有错误，但得到错误: %v", err)
	}
}
func TestGetAuthType(t *testing.T) {
	expected := []int{dao.AuthTypeNoAuth, dao.AuthTypeToken, dao.AuthTypeDingtalk} // 根据 sendFuncMap 的键值更新预期结果
	result := GetAuthType()

	// 使用 map 来检查是否包含所有预期的值
	expectedSet := make(map[int]struct{})
	for _, v := range expected {
		expectedSet[v] = struct{}{}
	}

	for _, v := range result {
		delete(expectedSet, v) // 从预期集合中删除已找到的值
	}

	// 如果 expectedSet 仍然有值，说明有缺失的预期值
	if len(expectedSet) > 0 {
		t.Errorf("缺失的预期值: %v", expectedSet)
	}

}
