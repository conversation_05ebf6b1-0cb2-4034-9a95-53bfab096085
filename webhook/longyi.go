//go:build longji

package webhook

import (
	"bytes"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"fobrain/webhook/dao"
	"io/ioutil"
	"net"
	"net/http"
	"sort"
	"strings"
	"time"

	"github.com/pkg/errors"
	"go-micro.dev/v4/logger"
	"gorm.io/gorm"
)

func init() {
	sendFuncMap[dao.AuthTypeLongyi] = longyiSend

}

func longyiSend(m *SenderMsg) error {
	var info VulnerabilityMsg
	err := json.Unmarshal(m.Info, &info)
	if err != nil {
		return err
	}
	logger.Infof("-----longyiSend SenderMsg:event%+v\n ", info.Event)
	if info.Event == dao.EventVulDistribute || info.Event == dao.EventVulTransfer {
		return longyiMMCTodoAdd(m.UrlConf, info)
	}

	if info.Event == dao.EventVulErrorReport || info.Event == dao.EventVulCantRepaired || info.Event == dao.EventVulWaitRetest {
		queryOpts := []dao.HandleFunc{
			func(db *gorm.DB) {
				db.Where(fmt.Sprintf("event in ('%s','%s')", dao.EventVulDistribute, dao.EventVulTransfer)).Where("business_id = ?", info.BusinessId).Order("ID desc")
			},
		}
		sendResult, err := dao.NewWebhookEventSendResultModel().First(queryOpts...)
		if err != nil {
			return err
		}
		return longyiMMCTodoDone(m.UrlConf, sendResult.MsgUuid)
	}
	return errors.New("不支持的事件类型")
}

// docSubject	是	String	文档主题
// todoType	是	Integer	类型 1--待办；2–待阅
// docUrl	是	String	文档链接 json方式 格式示例：{"APP":"移动端跳转链接","PC":"pc端跳转链接"}
// systemCode	是	String	系统标识
// docTime	是	String	文档时间 支持两种格式：yyyy-MM-dd HH:mm:ss 或 直接使用时间戳 例："docTime":"2021-12-11 09:40:00" 或："docTime":"1639186800000"
// docAuth	是	String	文档作者(中文名)
// receiverIds	是	List<Map<String,String>>	待办接收人员
//
//	添加待办
func longyiMMCTodoAdd(urlConf dao.WebhookEventUrlConfigModel, info VulnerabilityMsg) error {
	now := time.Now()
	var receiverIds = make([]map[string]string, 0)
	for _, d := range info.Details {
		for _, r := range d.ReceiverThirdUsernames {
			receiverIds = append(receiverIds, map[string]string{
				"loginName": r,
			})
		}
	}

	timestamp := fmt.Sprintf("%d", now.UnixMilli())
	fobrainUrl := `https://caasm.longi.com/oauth2_login?redirect_uri=%2F%23%2Floophole%2FloopholeManage`
	docUrl := `{"PC":"` + fobrainUrl + `"}`
	body := map[string]interface{}{
		"docSubject":  info.Title,
		"todoType":    1,
		"docUrl":      docUrl,
		"systemCode":  "CAASM",
		"docTime":     timestamp,
		"docAuth":     info.OpName,
		"receiverIds": receiverIds,
		"taskId":      info.MsgUuid, // 消息唯一关联值
	}
	respBody, err := HTTPRequestBodyNotSign(http.MethodPost, urlConf.Host+"/mmc/v5/open/todo/add", map[string]string{
		"sysKey":    urlConf.SysKey,
		"timestamp": timestamp,
	}, body, urlConf.Secret)
	if err != nil {
		return errors.WithMessage(err, respBody)
	}
	return nil
}

func getHostIP() (string, error) {
	// 获取本机所有网络接口
	interfaces, err := net.Interfaces()
	if err != nil {
		return "", err
	}

	for _, iface := range interfaces {
		// 过滤掉未激活的接口和回环接口
		if iface.Flags&net.FlagUp == 0 || iface.Flags&net.FlagLoopback != 0 {
			continue
		}

		// 获取接口的地址
		addrs, err := iface.Addrs()
		if err != nil {
			return "", err
		}

		for _, addr := range addrs {
			// 只处理 IPv4 地址
			if ipNet, ok := addr.(*net.IPNet); ok && ipNet.IP.To4() != nil {
				return ipNet.IP.String(), nil
			}
		}
	}
	return "", fmt.Errorf("未找到有效的宿主机 IP 地址")
}

// generateSignWithoutBody 生成不含 body 的 sign
func generateSignWithoutBody(params map[string]string, secret string) string {
	// Step 1: Sort URL parameters by key
	keys := make([]string, 0, len(params))
	for k := range params {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	// Step 2: Concatenate sorted parameters
	var paramStr strings.Builder
	for _, key := range keys {
		paramStr.WriteString(key)
		paramStr.WriteString(params[key])
	}

	// Step 3: Add secret at both ends and compute MD5
	signStr := secret + paramStr.String() + secret
	return md5Hash(signStr)
}

// generateSignWithBody 生成含 body 的 sign
func generateSignWithBody(params map[string]string, body map[string]interface{}, secret string) string {
	// Step 1: Sort URL parameters by key
	keys := make([]string, 0, len(params))
	for k := range params {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	// Step 2: Concatenate sorted URL parameters
	var paramStr strings.Builder
	for _, key := range keys {
		paramStr.WriteString(key)
		paramStr.WriteString(params[key])
	}

	// Step 3: Process and sort body JSON
	sortedBodyStr := sortBodyJSON(body)

	// Step 4: Concatenate all with secret and compute MD5
	signStr := secret + paramStr.String() + "#" + sortedBodyStr + secret
	return md5Hash(signStr)
}

// sortBodyJSON 递归排序 JSON body 并返回排序后的字符串
func sortBodyJSON(body map[string]interface{}) string {
	// 深度排序并编码为 JSON 字符串
	sortedBody := deepSortMap(body)
	bodyJSON, err := json.Marshal(sortedBody)
	if err != nil {
		fmt.Println("Error marshaling JSON:", err)
		return ""
	}
	return string(bodyJSON)
}

// deepSortMap 递归排序 JSON 中的 map 类型
func deepSortMap(data map[string]interface{}) map[string]interface{} {
	sortedData := make(map[string]interface{}, len(data))
	keys := make([]string, 0, len(data))
	for k := range data {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	for _, k := range keys {
		value := data[k]
		switch v := value.(type) {
		case map[string]interface{}:
			sortedData[k] = deepSortMap(v)
		case []interface{}:
			sortedData[k] = deepSortArray(v)
		default:
			sortedData[k] = v
		}
	}
	return sortedData
}

// deepSortArray 递归排序 JSON 中的 array 类型
func deepSortArray(array []interface{}) []interface{} {
	for i, v := range array {
		switch value := v.(type) {
		case map[string]interface{}:
			array[i] = deepSortMap(value)
		case []interface{}:
			array[i] = deepSortArray(value)
		}
	}
	return array
}

// md5Hash 计算字符串的 MD5 并返回十六进制字符串
func md5Hash(text string) string {
	hash := md5.New()
	hash.Write([]byte(text))
	return hex.EncodeToString(hash.Sum(nil))
}

func HTTPRequestBodyNotSign(method, url string, params map[string]string, body map[string]interface{}, secret string) (string, error) {
	sign := generateSignWithoutBody(params, secret)

	urlWithSign := fmt.Sprintf("%s?sysKey=%s&timestamp=%s&sign=%s", url, params["sysKey"], params["timestamp"], sign)

	logger.Infof("-----longyiSend HTTPRequestBodyNotSign: urlWithSign %+v, body:%+v\n ", urlWithSign, body)
	var req *http.Request
	var err error
	if method == "GET" {
		req, err = http.NewRequest("GET", urlWithSign, nil)
	} else if method == "POST" {
		bodyBytes, err := json.Marshal(body)
		if err != nil {
			return "", err
		}
		req, err = http.NewRequest("POST", urlWithSign, bytes.NewBuffer(bodyBytes))
		req.Header.Set("Content-Type", "application/json")
	}

	if err != nil {
		return "", err
	}

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		logger.Infof("-----longyiSend HTTPRequestBodyNotSign failed, err:%v,urlWithSign:%+v, body:%+v\n ", err, urlWithSign, body)
		return "", err
	}
	defer resp.Body.Close()

	respBody, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}
	logger.Infof("-----longyiSend HTTPRequestBodyNotSign success, urlWithSign:%+v, body:%+v,respBody:%s\n ", urlWithSign, body, string(respBody))
	return string(respBody), nil
}

// todoType	是	Integer	待办类型 (1-待办 2-待阅)
// systemCode	是	String	系统标识
// nodeId	否	String	节点id
// taskId	否	String	任务id
// flowId	否	String	流程id
// receiverIds	否	List<Map<String,String>>	转已办的人员，不指定则把对应待办所有人员都转已办
// docUrl	否	String	文档链接(格式参考添加待办参数中docUrl说明），如果传了，会进行更新；如转已办后不需要变更跳转地址，请不要传此参数
// /mmc/v5/open/todo/done 待办转已办
func longyiMMCTodoDone(urlConf dao.WebhookEventUrlConfigModel, msgUuid string) error {
	now := time.Now()
	timestamp := fmt.Sprintf("%d", now.UnixMilli())
	body := map[string]interface{}{
		"todoType":   1,
		"systemCode": "CAASM",
		"taskId":     msgUuid,
	}
	_, err := HTTPRequestBodyNotSign(http.MethodPost, urlConf.Host+"/mmc/v5/open/todo/done", map[string]string{
		"sysKey":    urlConf.SysKey,
		"timestamp": timestamp,
	}, body, urlConf.Secret)
	return err
}
