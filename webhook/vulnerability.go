package webhook

import (
	"context"
	"encoding/json"
	"fmt"
	"fobrain/webhook/dao"
	"strings"

	"github.com/google/uuid"
	"github.com/pkg/errors"
	"go-micro.dev/v4/logger"
	"gorm.io/gorm"
)

type VulnerabilityMsg struct {
	Event      string          `json:"event"`
	Title      string          `json:"title"`
	OpName     string          `json:"op_name"`
	BusinessId string          `json:"business_id"`
	MsgUuid    string          `json:"msg_uuid"`
	Details    []VulMsgDetails `json:"details"`
}

type ReceiverUser struct {
	SsoId   string `json:"sso_id"`
	Id      string `json:"id"`
	Fid     string `json:"fid"`
	FidHash string `json:"fid_hash"`
}

type VulMsgDetails struct {
	VulnerabilityId        string         `json:"vulnerability_id"`         // 漏洞id
	VulnerabilityName      string         `json:"vulnerability_name"`       // 漏洞名称
	SeverityLevel          string         `json:"severity_level"`           // 漏洞等级
	IPAddress              string         `json:"ip_address"`               // 修复的IP地址
	RepairPriority         string         `json:"repair_priority"`          // 漏洞修复优先级
	DispatchTime           string         `json:"dispatch_time"`            // 漏洞派发时间
	RepairDeadline         string         `json:"repair_deadline"`          // 漏洞修复期限
	RecheckTime            string         `json:"recheck_time"`             // 漏洞复测时间
	ReceiverPersons        []string       `json:"receiver_persons"`         // 接收人列表
	ReceiverThirdUsernames []string       `json:"receiver_third_usernames"` // 接收人第三方用户名
	ReceiverThirdUserId    []string       `json:"receiver_third_userid"`    //接收人第三方用户id
	ReceiverUsers          []ReceiverUser `json:"receiver_users"`           //接收人第三方用户id

	// 以下暂不支持
	AssetsList   []string `json:"assets_list"`
	BusinessList []string `json:"business_list"`
	FofaQuery    string   `json:"fofa_query"`
	FofaUrl      string   `json:"fofa_url"`
	FobrainUrl   string   `json:"fobrain_url"`
}

func (info *VulnerabilityMsg) ToSimpleString() string {
	msg := fmt.Sprintf("标题:%s", info.Title)
	if len(info.Details) > 0 {
		var infoList []string
		for _, v := range info.Details {
			var details []string
			if v.VulnerabilityId != "" {
				details = append(details, fmt.Sprintf("漏洞ID: %s", v.VulnerabilityId))
			}
			if v.VulnerabilityName != "" {
				details = append(details, fmt.Sprintf("漏洞名称: %s", v.VulnerabilityName))
			}
			if v.RepairDeadline != "" {
				details = append(details, fmt.Sprintf("修复期限: %s", v.RepairDeadline))
			}
			t := " " + strings.Join(details, ", ")
			infoList = append(infoList, t)
		}

		msg += ";详情:" + strings.Join(infoList, ";")
	}
	return msg
}
func (info *VulnerabilityMsg) ToString() string {
	eventName := dao.EventNameMap[info.Event]
	msg := fmt.Sprintf("事件: %s; 内容：%s", eventName, info.Title)
	if len(info.Details) > 0 {
		var infoList []string
		for _, v := range info.Details {
			var details []string
			if v.VulnerabilityId != "" {
				details = append(details, fmt.Sprintf("漏洞ID: %s", v.VulnerabilityId))
			}
			if v.VulnerabilityName != "" {
				details = append(details, fmt.Sprintf("漏洞名称: %s", v.VulnerabilityName))
			}
			if v.SeverityLevel != "" {
				details = append(details, fmt.Sprintf("漏洞等级: %s", v.SeverityLevel))
			}
			if v.IPAddress != "" {
				details = append(details, fmt.Sprintf("修复IP地址: %s", v.IPAddress))
			}
			if v.RepairPriority != "" {
				details = append(details, fmt.Sprintf("漏洞修复优先级: %s", v.RepairPriority))
			}
			if v.DispatchTime != "" {
				details = append(details, fmt.Sprintf("派发时间: %s", v.DispatchTime))
			}
			if v.RepairDeadline != "" {
				details = append(details, fmt.Sprintf("修复期限: %s", v.RepairDeadline))
			}
			if v.RecheckTime != "" {
				details = append(details, fmt.Sprintf("复测时间: %s", v.RecheckTime))
			}
			t := " " + strings.Join(details, ", ")
			infoList = append(infoList, t)
		}

		msg += ";详情:" + strings.Join(infoList, ";")
	}
	return msg
}

func (msg *VulnerabilityMsg) SendMsg() error {
	list, _, err := dao.NewWebhookEventUrlConfigModel().List(0, 0,
		func(tx *gorm.DB) {
			tx.Where("event_subscriptions like ?", "%"+msg.Event+"%")
		})
	if err != nil {
		logger.Errorf("VulnerabilityMsg SendMsg  List err:%v\n", err)
		return err
	}
	msg.MsgUuid = strings.ReplaceAll(uuid.New().String(), "-", "")
	info, err := json.Marshal(msg)
	if err != nil {
		return errors.WithMessage(err, "VulnerabilityMsg SendMsg  json marshal")
	}
	for _, v := range list {
		var senderMsg = SenderMsg{
			UrlConf:    *v,
			Info:       info,
			RetryCount: 0,
		}
		data, err := json.Marshal(senderMsg)
		if err != nil {
			logger.Errorf("VulnerabilityMsg SendMsg  failed to Marshal data, event_name:%s, md5hash:%s, title:%s, err:%v\n",
				msg.Event, v.ConfigMd5Hash, msg.Title, err)
			continue
		}
		err = redisCli.LPush(context.TODO(), webhookRedisKey, data).Err()
		if err != nil {
			logger.Errorf("VulnerabilityMsg SendMsg failed to push data to redis,  event_name:%s, md5hash:%s, title:%s, err:%v\n",
				msg.Event, v.ConfigMd5Hash, msg.Title, err)
			continue
		}
	}
	return nil
}
