package dingtalk

import (
	"fmt"
)

const (
	DingMsgTypeText string = "text" // text类型消息
)

// TextMsg text类型消息
type DingMsg struct {
	MsgType string      `json:"msgtype"` // 必填。消息类型，此时固定为：text。
	Text    TextContent `json:"text"`    // 是。消息内容
	At      *MsgAt      `json:"at"`      // 可选。
}

// TextContent
type TextContent struct {
	Content string `json:"content"`
}

// MsgAt @信息
type MsgAt struct {
	AtMobiles []string `json:"atMobiles"` // 可选。被@人的手机号（在content里添加@人的手机号）
	IsAtAll   bool     `json:"isAtAll"`   // 可选。是否@所有人
}

// MsgResponse 消息响应结果
type DingMsgResponse struct {
	ErrCode int    `json:"errcode"` // 错误码
	ErrMsg  string `json:"errmsg"`  // 错误信息
}

func (p DingMsgResponse) Success() bool {
	return p.ErrCode == 0
}

func (p DingMsgResponse) Error() string {
	return fmt.Sprintf("code:%d;msg:%s", p.ErrCode, p.ErrMsg)
}
