package dingtalk

import (
	"bytes"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"sync"
	"time"

	"github.com/pkg/errors"
)

var rwmux sync.RWMutex
var robotList = map[string]*DingRobot{}

// Robot 钉钉机器人
type DingRobot struct {
	webhook    string       // Webhook地址
	secret     string       // 密钥。机器人安全设置页面，加签一栏下面显示的SEC开头的字符串。不为空则进行加签
	httpClient *http.Client // http客户端
	isTest     bool
}

func InitDingRobot(robotName, url, accessToken, Secret string, isTest bool) {
	t := &DingRobot{
		webhook: url + "?access_token=" + accessToken,
		secret:  Secret,
		httpClient: &http.Client{
			Timeout: 3 * time.Second,
		},
		isTest: isTest,
	}
	rwmux.Lock()
	defer rwmux.Unlock()
	robotList[robotName] = t
}

func GetDingTalkClient(name string) (*DingRobot, error) {
	rwmux.RLock()
	defer rwmux.RUnlock()
	if r, ok := robotList[name]; ok {
		return r, nil
	}
	return nil, errors.New("no robot " + name)
}

func (dr *DingRobot) SendMsg(msg string) error {
	t := DingMsg{
		MsgType: DingMsgTypeText,
		Text: TextContent{
			Content: msg,
		},
	}
	data, err := json.Marshal(t)
	if err != nil {
		return errors.WithStack(err)
	}
	url, err := dr.genRequestURL()
	if err != nil {
		return err
	}

	return dr.send(url, data)
}

// https://ding-doc.dingtalk.com/doc#/serverapi2/qf2nxq/26eaddd5
func (dr *DingRobot) genRequestURL() (string, error) {
	timestamp := fmt.Sprintf("%d", time.Now().UnixNano()/1e6)
	signStr, err := dr.sign(timestamp)
	if err != nil {
		return "", err
	}
	return fmt.Sprintf("%s&timestamp=%s&sign=%s", dr.webhook, timestamp, signStr), nil
}

// sign 加签。timestamp，当前时间戳。单位：毫秒
// https://ding-doc.dingtalk.com/doc#/serverapi2/qf2nxq/26eaddd5
func (dr *DingRobot) sign(timestamp string) (string, error) {
	toSignStr := timestamp + "\n" + dr.secret

	h := hmac.New(sha256.New, []byte(dr.secret))
	if _, err := io.WriteString(h, toSignStr); err != nil {
		return "", errors.WithStack(err)
	}
	encodeStr := base64.StdEncoding.EncodeToString(h.Sum(nil))

	return url.QueryEscape(encodeStr), nil
}

func (dr *DingRobot) send(reqURL string, buff []byte) error {
	if dr.isTest {
		return nil
	}
	resp, err := dr.httpClient.Post(reqURL, "application/json;charset=utf-8", bytes.NewReader(buff))
	if err != nil {
		return errors.WithStack(err)
	}
	if resp.StatusCode != http.StatusOK {
		return errors.New(resp.Status)
	}
	defer resp.Body.Close()
	b, err := io.ReadAll(resp.Body)
	if err != nil {
		return errors.WithStack(err)
	}

	var r DingMsgResponse
	if err := json.Unmarshal(b, &r); err != nil {
		return errors.WithStack(err)
	}
	if !r.Success() {
		return r
	}

	return nil
}
