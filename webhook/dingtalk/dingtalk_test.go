package dingtalk

import (
	"testing"
)

func TestInitDingRobot(t *testing.T) {
	InitDingRobot("AccountApplyRobot", "", "", "", true)
	InitDingRobot("sdds", "", "", "", true)
}

func TestGetDingTalkClient(t *testing.T) {
	InitDingRobot("AccountApplyRobot", "", "", "", true)
	cli, err := GetDingTalkClient("AccountApplyRobot")
	if err != nil {
		t.Fatal(err)
	}
	if cli == nil {
		t.Fatal("cli is nil")
	}
}
func TestSendMsg(t *testing.T) {
	type args struct {
		msg string
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "test dingding msg 1",
			args: args{
				msg: "hello robot",
			},
			wantErr: false,
		},
	}
	InitDingRobot("AccountApplyRobot", "", "", "", true)
	cli, err := GetDingTalkClient("AccountApplyRobot")
	if err != nil {
		t.Fatal(err)
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := cli.SendMsg(tt.args.msg); (err != nil) != tt.wantErr {
				t.Errorf("SendMsg() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestDingRobot_genRequestURL(t *testing.T) {
	type fields struct {
		url     string
		webhook string
		secret  string
	}
	tests := []struct {
		name    string
		fields  fields
		wantErr bool
	}{
		{
			name: "test 1",
			fields: fields{
				url:     "",
				webhook: "",
				secret:  "",
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {

			InitDingRobot("AccountApplyRobot", tt.fields.url, tt.fields.webhook, tt.fields.secret, true)
			dr, err := GetDingTalkClient("AccountApplyRobot")
			if err != nil {
				t.Fatal(err)
			}
			got, err := dr.genRequestURL()
			if (err != nil) != tt.wantErr {
				t.Errorf("DingRobot.genRequestURL() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			t.Log(got)
		})
	}
}
