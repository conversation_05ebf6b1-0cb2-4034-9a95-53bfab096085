package webhook

import (
	"testing"
)

func TestVulnerabilityMsg_ToString(t *testing.T) {
	tests := []struct {
		name     string
		msg      VulnerabilityMsg
		expected string
	}{
		{
			name: "测试正常情况",
			msg: VulnerabilityMsg{
				Event: "test_event",
				Title: "测试标题",
				Details: []VulMsgDetails{
					{
						VulnerabilityId:   "123",
						VulnerabilityName: "测试漏洞",
						SeverityLevel:     "高",
						IPAddress:         "***********",
						RepairPriority:    "高",
						DispatchTime:      "2023-01-01",
						RepairDeadline:    "2023-01-10",
						RecheckTime:       "2023-01-11",
					},
				},
			},
			expected: "事件: ; 内容：测试标题;详情: 漏洞ID: 123, 漏洞名称: 测试漏洞, 漏洞等级: 高, 修复IP地址: ***********, 漏洞修复优先级: 高, 派发时间: 2023-01-01, 修复期限: 2023-01-10, 复测时间: 2023-01-11",
		},
		// 可以添加更多测试用例
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.msg.ToString()
			if result != tt.expected {
				t.Errorf("期望: %s, 得到: %s", tt.expected, result)
			}
		})
	}
}

func TestVulnerabilityMsg_ToSimpleString(t *testing.T) {
	tests := []struct {
		name     string
		msg      VulnerabilityMsg
		expected string
	}{
		{
			name: "测试正常情况",
			msg: VulnerabilityMsg{
				Title: "测试标题",
				Details: []VulMsgDetails{
					{
						VulnerabilityId:   "123",
						VulnerabilityName: "测试漏洞",
						RepairDeadline:    "2023-01-10",
					},
				},
			},
			expected: "标题:测试标题;详情: 漏洞ID: 123, 漏洞名称: 测试漏洞, 修复期限: 2023-01-10",
		},
		{
			name: "测试无详情情况",
			msg: VulnerabilityMsg{
				Title:   "测试标题",
				Details: []VulMsgDetails{},
			},
			expected: "标题:测试标题",
		},
		// 可以添加更多测试用例
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.msg.ToSimpleString()
			if result != tt.expected {
				t.Errorf("期望: %s, 得到: %s", tt.expected, result)
			}
		})
	}
}

func TestVulnerabilityMsg_SendMsg(t *testing.T) {
	type fields struct {
		Event   string
		Title   string
		OpName  string
		Details []VulMsgDetails
	}
	tests := []struct {
		name    string
		fields  fields
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			msg := &VulnerabilityMsg{
				Event:   tt.fields.Event,
				Title:   tt.fields.Title,
				OpName:  tt.fields.OpName,
				Details: tt.fields.Details,
			}
			if err := msg.SendMsg(); (err != nil) != tt.wantErr {
				t.Errorf("VulnerabilityMsg.SendMsg() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
