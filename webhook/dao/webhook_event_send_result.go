package dao

import "time"

type WebhookEventSendResultModel struct {
	BaseModel
	WebhookConfigId uint64 `gorm:"column:webhook_config_id"`
	MsgInfo         string `gorm:"column:msg_info"`
	Status          int64  `gorm:"column:status"`
	ErrMsg          string `gorm:"column:err_msg"`
	Event           string `gorm:"column:event"`
	BusinessId      string `gorm:"column:business_id"`
	MsgUuid         string `gorm:"column:msg_uuid"`
}

func (m *WebhookEventSendResultModel) TableName() string {
	return "webhook_event_send_result"
}

func NewWebhookEventSendResultModel() *WebhookEventSendResultModel {
	return &WebhookEventSendResultModel{BaseModel: BaseModel{}}
}

func (m *WebhookEventSendResultModel) Create(param *WebhookEventSendResultModel) error {
	param.CreatedAt = time.Now()
	param.UpdatedAt = time.Now()
	return getGormDB().Create(param).Error
}

func (t *WebhookEventSendResultModel) First(opts ...HandleFunc) (*WebhookEventSendResultModel, error) {
	query := getGormDB().Table(t.TableName())
	for _, opt := range opts {
		opt(query)
	}
	var r WebhookEventSendResultModel
	err := query.First(&r).Error
	return &r, err
}
