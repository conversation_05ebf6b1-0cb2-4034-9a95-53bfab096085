package dao

import (
	"fmt"
	"fobrain/pkg/cfg"
	"fobrain/pkg/utils/common_logs"
	"log"
	"strings"
	"time"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"

	driverMysql "gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"gorm.io/gorm/schema"
)

const logPrefix = "SQL "

var gormDB *gorm.DB

type MysqlConfig struct {
	Address  string `json:"address"`
	Port     int    `json:"port"`
	UserName string `json:"username"`
	Password string `json:"password"`
	Database string `json:"database"`
	CharSet  string `json:"charset"`
	// MaxIdleConns int    `json:"max-idle-conns"`
	// MaxOpenConns int    `json:"max-open-conns"`
	// LogMode      string `json:"log-mode"`
	LogLevel string `json:"log-level"` // 2345=error,01=warning,-1=debug
	SlowTime int64  `json:"slow-time"` // unit: second
}

// InitMysql 初始化数据库并产生数据库全局变量
func InitMysql(con MysqlConfig) {
	dnsString := getDNS(con)
	db, err := gorm.Open(driverMysql.New(driverMysql.Config{
		DSN:                       dnsString,
		DefaultStringSize:         256,   // string 类型字段的默认长度                                                                            // string 类型字段的默认长度
		DisableDatetimePrecision:  true,  // 禁用 datetime 精度，MySQL 5.6 之前的数据库不支持
		DontSupportRenameIndex:    true,  // 重命名索引时采用删除并新建的方式，MySQL 5.7 之前的数据库和 MariaDB 不支持重命名索引
		DontSupportRenameColumn:   true,  // 用 `change` 重命名列，MySQL 8 之前的数据库和 MariaDB 不支持重命名列
		SkipInitializeWithVersion: false, //根据版本自动配置                                                                            // 根据当前 MySQL 版本自动配置
	}), &gorm.Config{
		PrepareStmt:                              true,                // open sql preparestmt
		Logger:                                   getMysqlLogger(con), // 日志
		DisableForeignKeyConstraintWhenMigrating: true,                // 外键约束
		//SkipDefaultTransaction: true, // 禁用默认事务（提高运行速度）
		NamingStrategy: schema.NamingStrategy{
			// 使用单数表名，启用该选项，此时，`User` 的表名应该是 `user`
			SingularTable: true,
		},
	})
	if err != nil {
		panic(fmt.Sprintf("gorm.Open err:%v \n", err))
	}
	sqlDB, _ := db.DB()
	// Enable Logger, show detailed log
	sqlDB.SetMaxIdleConns(50)                  // 设置空闲连接池中连接的最大数量
	sqlDB.SetMaxOpenConns(10)                  // 设置打开数据库连接的最大数量。
	sqlDB.SetConnMaxLifetime(10 * time.Second) // 设置了连接可复用的最大时间。
	gormDB = db
}
func getGormDB() *gorm.DB {
	return gormDB
}

func getMysqlLogger(con MysqlConfig) logger.Interface {
	logMode := logger.Warn
	logLevel, err := zapcore.ParseLevel(con.LogLevel)
	if err != nil {
		panic(err)
	}
	switch logLevel {
	case zapcore.ErrorLevel, zapcore.FatalLevel, zapcore.PanicLevel, zapcore.DPanicLevel:
		logMode = logger.Error
	case zapcore.WarnLevel, zapcore.InfoLevel:
		logMode = logger.Warn
	case zapcore.DebugLevel:
		logMode = logger.Info
	}
	newLogger := logger.New(
		log.New(&logWriter{logger: common_logs.InitLogger(cfg.LoadLogger()).Logger}, logPrefix, 0), // io writer
		logger.Config{
			SlowThreshold:             time.Second * time.Duration(con.SlowTime), // Slow SQL threshold
			LogLevel:                  logMode,                                   // gorm日志模式：silent 可选 Silent，Error，Warn，Info
			Colorful:                  false,
			IgnoreRecordNotFoundError: true,
		},
	)
	return newLogger
}

func getDNS(opt MysqlConfig) string {
	return fmt.Sprintf("%s:%s@(%s:%d)/%s?charset=%s&parseTime=True&loc=Local",
		opt.UserName,
		opt.Password,
		opt.Address,
		opt.Port,
		opt.Database,
		opt.CharSet,
	)
}

type BaseModel struct {
	Id        uint64    `gorm:"primaryKey;autoIncrement;comment:id" json:"id"`
	CreatedAt time.Time `gorm:"autoCreateTime;comment:创建时间" json:"created_at"`
	UpdatedAt time.Time `gorm:"autoUpdateTime;comment:更新时间" json:"updated_at"`
}

// logWriter is an io.Writer that writes to zap.SugaredLogger.
type logWriter struct {
	logger *zap.Logger
}

// Write implements io.Writer.
func (w *logWriter) Write(p []byte) (n int, err error) {
	arr := strings.Split(string(p), "\n")
	if len(arr) >= 1 {
		paths := strings.Split(arr[0], "/")
		if len(paths) >= 2 {
			arr[0] = logPrefix + strings.Join(paths[len(paths)-2:], "/")
		}
	}
	w.logger.WithOptions(zap.WithCaller(false)).Info(strings.Join(arr, " "))
	return len(p), nil
}
