package dao

import (
	"testing"

	"github.com/agiledragon/gomonkey/v2"
	"gorm.io/gorm"
)

func TestGetGormDB(t *testing.T) {

	patches := gomonkey.ApplyGlobalVar(&gormDB, &gorm.DB{})
	defer patches.Reset()

	db := getGormDB()

	// 断言
	if db == nil {
		t.Fatal("expected gormDB to be non-nil")
	}
}
func TestGetDNS(t *testing.T) {
	tests := []struct {
		name     string
		config   MysqlConfig
		expected string
	}{
		{
			name: "Test with valid config",
			config: MysqlConfig{
				UserName: "user",
				Password: "pass",
				Address:  "localhost",
				Port:     3306,
				Database: "testdb",
				CharSet:  "utf8",
			},
			expected: "user:pass@(localhost:3306)/testdb?charset=utf8&parseTime=True&loc=Local",
		},
		// 可以添加更多测试用例
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := getDNS(tt.config)
			if result != tt.expected {
				t.<PERSON><PERSON>("getDNS() = %v, want %v", result, tt.expected)
			}
		})
	}
}
