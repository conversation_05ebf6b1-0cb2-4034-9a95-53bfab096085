package dao

import (
	"errors"
	"fobrain/pkg/utils"
	"reflect"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"gorm.io/gorm"
)

func TestWebhookEventSendResult_TableName(t *testing.T) {
	t.Run("正常", func(t *testing.T) {
		if NewWebhookEventSendResultModel().TableName() != "webhook_event_send_result" {
			t.Error("wrong table name")
		}
	})
}
func TestWebhookEventSendResult_Create(t *testing.T) {
	sql := "INSERT INTO `webhook_event_send_result` (`created_at`,`updated_at`,`webhook_config_id`,`msg_info`,`status`,`err_msg`,`event`,`business_id`,`msg_uuid`) VALUES (?,?,?,?,?,?,?,?,?)"
	t.Run("正常", func(t *testing.T) {
		mockDb := InitSqlMock()
		mockDb.ExpectBegin()
		mockDb.ExpectExec(sql).
			WillReturnResult(sqlmock.NewResult(1, 1))
		mockDb.ExpectCommit()
		gormDB = mockDb.MockGorm
		err := NewWebhookEventSendResultModel().Create(&WebhookEventSendResultModel{
			WebhookConfigId: 123,
			MsgInfo:         "test",
			Status:          1,
			ErrMsg:          "test",
			Event:           "test",
			BusinessId:      "test",
			MsgUuid:         "test",
		})

		if err != nil {
			t.Fatal(err)
		}
	})
	t.Run("应该是错误", func(t *testing.T) {
		mockDb := InitSqlMock()
		mockDb.ExpectBegin()
		mockDb.ExpectExec(sql).
			WillReturnError(errors.New("模拟错误"))
		mockDb.ExpectRollback()
		mockDb.ExpectCommit()
		gormDB = mockDb.MockGorm
		err := NewWebhookEventSendResultModel().Create(&WebhookEventSendResultModel{})
		if err == nil {
			t.Fatal("Create应该是err")
		}
	})
}

var WebhookEventSendResultFieldsArray = []string{"id", "created_at", "updated_at", "webhook_config_id", "msg_info", "status", "err_msg", "event", "business_id", "msg_uuid"}

func TestWebhookEventSendResultModel_First(t *testing.T) {
	sql := "SELECT * FROM `webhook_event_send_result` WHERE id = ? ORDER BY `webhook_event_send_result`.`id` LIMIT 1"

	t.Run("预期error", func(t *testing.T) {
		mockDb := InitSqlMock()
		var opts []HandleFunc
		opts = append(opts, func(tx *gorm.DB) {
			tx.Where("id = ?", 1)
		})
		gormDB = mockDb.MockGorm
		mockDb.ExpectQuery(sql).
			WillReturnError(sqlmock.ErrCancelled)
		if _, err := NewWebhookEventSendResultModel().First(opts...); err == nil {
			t.Errorf("First() = nil, want not nil")
		}
	})
	t.Run("正常", func(t *testing.T) {
		timeParse, err := time.Parse(utils.DateTimeLayout, "2024-10-12 16:32:31")
		if err != nil {
			t.Fatal(err)
		}
		want := &WebhookEventSendResultModel{
			BaseModel: BaseModel{
				Id:        1,
				CreatedAt: timeParse,
				UpdatedAt: timeParse,
			},
			WebhookConfigId: 123,
			MsgInfo:         "test",
			Status:          1,
			ErrMsg:          "test",
			Event:           "test",
			BusinessId:      "test",
			MsgUuid:         "test",
		}
		mockDb := InitSqlMock()
		var opts []HandleFunc
		opts = append(opts, func(tx *gorm.DB) {
			tx.Where("id = ?", 1)
		})
		gormDB = mockDb.MockGorm
		mockDb.ExpectQuery(sql).
			WillReturnRows(mockDb.NewRows(WebhookEventSendResultFieldsArray).
				AddRow(1, timeParse, timeParse, 123, "test", 1, "test", "test", "test", "test"))
		got, err := NewWebhookEventSendResultModel().First(opts...)
		if err != nil {
			t.Errorf("First() != nil, want nil,err:%v", err)
		}
		if !reflect.DeepEqual(got, want) {
			t.Errorf("First() = %+v, want %+v", got, want)
		}
	})
}
