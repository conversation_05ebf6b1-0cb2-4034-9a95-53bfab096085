package dao

import (
	"errors"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
)

func TestIsPageAll(t *testing.T) {
	tests := []struct {
		page, size int
		expected   bool
	}{
		{0, 0, true},  // 测试页面和大小均为0
		{1, 0, false}, // 测试页面为1，大小为0
		{0, 1, false}, // 测试页面为0，大小为1
		{1, 1, false}, // 测试页面和大小均不为0
	}

	for _, test := range tests {
		result := IsPageAll(test.page, test.size)
		if result != test.expected {
			t.Errorf("IsPageAll(%d, %d) = %v; expected %v", test.page, test.size, result, test.expected)
		}
	}
}

func TestWebhookEventUrlConfigModel_TableName(t *testing.T) {
	t.Run("正常", func(t *testing.T) {
		if NewWebhookEventUrlConfigModel().TableName() != "webhook_event_url_config" {
			t.Error("wrong table name")
		}
	})
}

func TestWebhookEventUrlConfigModel_NewWebhookEventUrlConfigModel(t *testing.T) {
	t.Run("正常", func(t *testing.T) {
		if NewWebhookEventUrlConfigModel() == nil {
			t.Fatal("NewWebhookEventUrlConfigModel() returns nil")
		}
	})
}
func TestPageLimit(t *testing.T) {
	r := PageLimit(1, 10)
	if r == nil {
		t.Fatal("不应该为nil")
	}
}

func TestWebhookEventUrlConfigModel_Create(t *testing.T) {
	sql := "INSERT INTO `webhook_event_url_config` (`created_at`,`updated_at`,`auth_type`,`host`,`token_value`,`url_token_name`,`header_token_name`,`secret`,`sys_key`,`event_subscriptions`,`config_md5_hash`,`jump_link`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?)"
	t.Run("正常", func(t *testing.T) {
		mockDb := InitSqlMock()
		mockDb.ExpectBegin()
		mockDb.ExpectExec(sql).
			WillReturnResult(sqlmock.NewResult(1, 1))
		mockDb.ExpectCommit()
		gormDB = mockDb.MockGorm
		err := NewWebhookEventUrlConfigModel().Create(&WebhookEventUrlConfigModel{
			AuthType:           123,
			Host:               "test",
			TokenValue:         "test",
			URLTokenName:       "test",
			HeaderTokenName:    "test",
			Secret:             "test",
			SysKey:             "test",
			EventSubscriptions: "test",
			ConfigMd5Hash:      "test",
		})

		if err != nil {
			t.Fatal(err)
		}
	})
	t.Run("应该是错误", func(t *testing.T) {
		mockDb := InitSqlMock()
		mockDb.ExpectBegin()
		mockDb.ExpectExec(sql).
			WillReturnError(errors.New("模拟错误"))
		mockDb.ExpectRollback()
		mockDb.ExpectCommit()
		gormDB = mockDb.MockGorm
		err := NewWebhookEventSendResultModel().Create(&WebhookEventSendResultModel{})
		if err == nil {
			t.Fatal("Create应该是err")
		}
	})
}

func TestWebhookEventUrlConfigModel_Delete(t *testing.T) {
	t.Run("正常", func(t *testing.T) {
		mockDb := InitSqlMock()
		defer mockDb.Close()
		mockDb.ExpectBegin()
		mockDb.ExpectExec("DELETE FROM `webhook_event_url_config` WHERE id in (?,?)").
			WithArgs(1, 2).
			WillReturnResult(sqlmock.NewResult(0, 2))
		mockDb.ExpectCommit()
		gormDB = mockDb.MockGorm
		err := NewWebhookEventUrlConfigModel().Delete([]int64{1, 2})
		if err != nil {
			t.Fatal(err)
		}

	})
	t.Run("应该是错误", func(t *testing.T) {
		mockDb := InitSqlMock()
		mockDb.ExpectBegin()
		mockDb.ExpectExec("DELETE FROM `webhook_event_url_config` WHERE id in (?)").
			WithArgs(1).
			WillReturnError(sqlmock.ErrCancelled)
		mockDb.ExpectRollback()
		mockDb.ExpectCommit()
		gormDB = mockDb.MockGorm
		err := NewWebhookEventUrlConfigModel().Delete([]int64{1})
		if err == nil {
			t.Fatal("应该是err")
		}
	})
}

func TestWebhookEventUrlConfigModel_List(t *testing.T) {
	t.Run("error", func(t *testing.T) {
		mockDb := InitSqlMock()
		defer mockDb.Close()
		gormDB = mockDb.MockGorm
		mockDb.ExpectQuery("SELECT count(*) FROM `aliyun_avd`").
			WillReturnRows(mockDb.NewRows([]string{"count"}).AddRow(1))
		mockDb.ExpectQuery("SELECT * FROM `webhook_event_url_config` LIMIT 15").
			WillReturnError(sqlmock.ErrCancelled)

		_, _, err := NewWebhookEventUrlConfigModel().List(1, 15)
		if err == nil {
			t.Fatal("应该是err")
		}

	})
	t.Run("空列表", func(t *testing.T) {
		mockDb := InitSqlMock()
		defer mockDb.Close()
		gormDB = mockDb.MockGorm
		mockDb.ExpectQuery("SELECT count(*) FROM `webhook_event_url_config`").
			WillReturnRows(mockDb.NewRows([]string{"count"}).AddRow(0))
		mockDb.ExpectQuery("SELECT * FROM `webhook_event_url_config` LIMIT 15").
			WillReturnRows(sqlmock.NewRows([]string{}))

		got, _, err := NewWebhookEventUrlConfigModel().List(1, 15)
		if err != nil {
			t.Fatal(err)
		}
		if len(got) != 0 {
			t.Errorf("WebhookEventUrlConfigModel.List() = %v, want %v", got, []WebhookEventUrlConfigModel{})
		}
	})
}
