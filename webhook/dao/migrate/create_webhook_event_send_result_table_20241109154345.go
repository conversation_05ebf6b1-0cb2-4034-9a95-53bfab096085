package migrate

import (
	"git.gobies.org/caasm/fobrain-components/go-migrate/pkg/interfaces"
	"git.gobies.org/caasm/fobrain-components/go-migrate/pkg/lib/mysql"
)

type WebhookEventSendResult20241109154345Table struct{}

func CreateWebhookEventSendResult20241109154345Table() interfaces.Migration {
	return &WebhookEventSendResult20241109154345Table{}
}

func (t *WebhookEventSendResult20241109154345Table) Up() error {
	return mysql.Schema.Create("webhook_event_send_result", func(table interfaces.Blueprint) {
		table.Id("id", 22)
		table.Timestamps()
		table.Integer("webhook_config_id", 22).Comment("认证类型")
		table.Integer("status", 11).Comment("发送状态")
		table.Text("msg_info").Comment("事件消息")
		table.Text("err_msg").Comment("错误信息")
		table.TableComment("webhook配置表")
	})
}

func (t *WebhookEventSendResult20241109154345Table) Down() error {
	return mysql.Schema.DropIfExists("webhook_event_send_result")
}
