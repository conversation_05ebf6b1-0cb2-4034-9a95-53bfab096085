package migrate

import (
	"git.gobies.org/caasm/fobrain-components/go-migrate/pkg/interfaces"
	"git.gobies.org/caasm/fobrain-components/go-migrate/pkg/lib/mysql"
)

type AlterWebhookEventSendResultTable20241120171813 struct{}

func CreateAlterWebhookEventSendResultTable20241120171813() interfaces.Migration {
	return &AlterWebhookEventSendResultTable20241120171813{}
}

func (t *AlterWebhookEventSendResultTable20241120171813) Up() error {
	return mysql.Schema.Table("webhook_event_send_result", func(table interfaces.Blueprint) {
		table.String("event", 256).Comment("事件")
		table.String("business_id", 256).Comment("业务ID")
		table.String("msg_uuid", 256).Comment("消息UUID")
		table.Index("business_id").IndexName("idx_business_id")
		table.Index("msg_uuid").IndexName("idx_msg_uuid")
	})

}

func (t *AlterWebhookEventSendResultTable20241120171813) Down() error {
	return mysql.Schema.Table("webhook_event_send_result", func(table interfaces.Blueprint) {
		table.DropColumn("event")
		table.DropColumn("business_id")
		table.DropColumn("msg_uuid")
		table.DropIndex("idx_business_id")
		table.DropIndex("idx_msg_uuid")
	})
}
