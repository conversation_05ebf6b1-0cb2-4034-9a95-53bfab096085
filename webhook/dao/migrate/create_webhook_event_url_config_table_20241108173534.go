package migrate

import (
	"git.gobies.org/caasm/fobrain-components/go-migrate/pkg/interfaces"
	"git.gobies.org/caasm/fobrain-components/go-migrate/pkg/lib/mysql"
)

type WebhookEventUrlConfig20241108173534Table struct{}

func CreateWebhookEventUrlConfig20241108173534Table() interfaces.Migration {
	return &WebhookEventUrlConfig20241108173534Table{}
}

func (t *WebhookEventUrlConfig20241108173534Table) Up() error {
	return mysql.Schema.Create("webhook_event_url_config", func(table interfaces.Blueprint) {
		table.Id("id", 22)
		// 新增字段
		table.Integer("auth_type", 11).Comment("认证类型")
		table.String("host", 256).Comment("主机")
		table.String("token_value", 256).Comment("令牌值")
		table.String("url_token_name", 256).Comment("URL令牌名称")
		table.String("header_token_name", 256).Comment("头部令牌名称")
		table.String("secret", 256).Comment("密钥")
		table.String("sys_key", 256).Comment("密钥")
		table.Text("event_subscriptions").Comment("事件订阅")
		table.String("config_md5_hash", 64).Comment("配置的MD5哈希值")
		table.Timestamps()
		// 添加索引
		table.Unique("config_md5_hash")
		table.Index("host")
		table.TableComment("webhook配置表")
	})
}

func (t *WebhookEventUrlConfig20241108173534Table) Down() error {
	return mysql.Schema.DropIfExists("webhook_event_url_config")
}
