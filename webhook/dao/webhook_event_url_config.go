package dao

import (
	"errors"
	"strings"
	"time"

	"gorm.io/gorm"
)

type WebhookEventUrlConfigModel struct {
	BaseModel
	AuthType           int    `gorm:"column:auth_type" json:"auth_type"`
	Host               string `gorm:"column:host" json:"host"`
	TokenValue         string `gorm:"column:token_value" json:"token_value"`
	URLTokenName       string `gorm:"column:url_token_name" json:"url_token_name"`
	HeaderTokenName    string `gorm:"column:header_token_name" json:"header_token_name"`
	Secret             string `gorm:"column:secret" json:"secret"`
	SysKey             string `gorm:"column:sys_key" json:"sys_key"`
	EventSubscriptions string `gorm:"column:event_subscriptions" json:"event_subscriptions"`
	ConfigMd5Hash      string `gorm:"column:config_md5_hash" json:"config_md5_hash"`
	JumpLink           string `gorm:"column:jump_link" json:"jump_link"`
}

const (
	AuthTypeNoAuth        = iota
	AuthTypeToken         // 单token
	AuthTypeDingtalk      // 钉钉
	AuthTypeLongyi        // 隆易鉴权
	AuthTypeZhongyiFeishu // 中移香港飞书

	EventVulDistribute   = "vul_distribute"
	EventVulTransfer     = "vul_transfer"
	EventVulRetest       = "vul_retest"
	EventVulTimeout      = "vul_timeout"
	EventVulErrorReport  = "vul_error_report"
	EventVulCantRepaired = "vul_cant_repaired"
	EventVulRepaired     = "vul_repaired"
	EventVulWaitRetest   = "vul_wait_retest"
)

var EventNameMap = map[string]string{
	EventVulDistribute:   "漏洞派发",
	EventVulRetest:       "漏洞复测",
	EventVulTransfer:     "漏洞转交",
	EventVulTimeout:      "漏洞处理超时",
	EventVulErrorReport:  "误报",
	EventVulCantRepaired: "无法修复",
	EventVulRepaired:     "复测通过",
	EventVulWaitRetest:   "修复完成",
}

func (m *WebhookEventUrlConfigModel) TableName() string {
	return "webhook_event_url_config"
}

func NewWebhookEventUrlConfigModel() *WebhookEventUrlConfigModel {
	return &WebhookEventUrlConfigModel{BaseModel: BaseModel{}}
}

func IsPageAll(page, size int) bool {
	return page == 0 && size == 0
}

type HandleFunc func(tx *gorm.DB)

func PageLimit(page, size int) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		offset := (page - 1) * size
		if offset <= 0 {
			offset = 0
		}
		return db.Offset(offset).Limit(size)
	}
}
func (m *WebhookEventUrlConfigModel) List(page, size int, opts ...HandleFunc) ([]*WebhookEventUrlConfigModel, int64, error) {
	query := getGormDB().Table(m.TableName())
	for _, opt := range opts {
		opt(query)
	}

	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}
	if !IsPageAll(page, size) {
		query.Scopes(PageLimit(page, size))
	}

	var list []*WebhookEventUrlConfigModel
	if err := query.Find(&list).Error; err != nil {
		return nil, 0, err
	}

	return list, total, nil
}
func (m *WebhookEventUrlConfigModel) Create(param *WebhookEventUrlConfigModel) error {
	param.CreatedAt = time.Now()
	param.UpdatedAt = time.Now()
	err := getGormDB().Create(param).Error
	if err != nil {
		if strings.Contains(err.Error(), "Duplicate entry") {
			return errors.New("该配置已存在")
		}
		return err
	}
	return nil
}

func (m *WebhookEventUrlConfigModel) Delete(ids []int64) error {
	if len(ids) == 0 {
		return getGormDB().Where("id >0").Delete(&WebhookEventUrlConfigModel{}).Error
	}
	return getGormDB().Where("id in (?)", ids).Delete(&WebhookEventUrlConfigModel{}).Error
}
