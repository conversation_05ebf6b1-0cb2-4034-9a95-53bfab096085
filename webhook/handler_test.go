package webhook

import (
	"net/http"
	"net/http/httptest"
	"reflect"
	"strings"
	"testing"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func createTestRequest(body string, method string, url string) (*http.Request, *httptest.ResponseRecorder) {
	req := httptest.NewRequest(method, url, strings.NewReader(body))
	req.Header.Set("Content-Type", "application/json")
	return req, httptest.NewRecorder()
}

func TestSendWebhookMsg_SenderAddError(t *testing.T) {
	gin.SetMode(gin.TestMode)

	// 创建一个新的 Gin 路由
	router := gin.Default()
	router.POST("/webhook", SendWebhookMsg)
	// Mock the SendMsg method
	patches := gomonkey.ApplyMethod(reflect.TypeOf(&VulnerabilityMsg{}), "SendMsg", func() error {
		return nil
	})
	defer patches.Reset()

	// 创建一个测试请求
	reqBody := `{"id":1,"send_url":"http://example.com/send","sdk_type":0,"event":"漏洞上报","name":"测试事件"}`
	req := httptest.NewRequest(http.MethodPost, "/webhook", strings.NewReader(reqBody))
	req.Header.Set("Content-Type", "application/json")

	// 创建一个响应记录器
	w := httptest.NewRecorder()

	// 执行请求
	router.ServeHTTP(w, req)

	// 断言响应状态码
	assert.Equal(t, http.StatusOK, w.Code)
	assert.JSONEq(t, `{"code":0,"message":"success","data":""}`, w.Body.String())
}
