package webhook

import (
	"fmt"
	"fobrain/fobrain/common/request"
	"fobrain/pkg/utils"
	"fobrain/webhook/dao"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"go-micro.dev/v4/logger"
	"gorm.io/gorm"
)

var responseOk = gin.H{
	"code":    0,
	"message": "success",
	"data":    "",
}

func SendWebhookMsg(ctx *gin.Context) {
	param := &VulnerabilityMsg{}
	if err := ctx.ShouldBind(param); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    http.StatusBadRequest,
			"message": err.Error(),
			"data":    "",
		})
		return
	}
	_ = param.SendMsg()
	ctx.JSON(http.StatusOK, responseOk)
}

type EventSubscriptionParams struct {
	Id                 uint64   `json:"id"`
	CreatedAt          string   `json:"created_at"`
	UpdatedAt          string   `json:"updated_at"`
	AuthType           int      `json:"auth_type"`
	Host               string   `json:"host"`
	TokenValue         string   `json:"token_value"`
	URLTokenName       string   `json:"url_token_name"`
	HeaderTokenName    string   `json:"header_token_name"`
	Secret             string   `json:"secret"`
	SysKey             string   `json:"sys_key"`
	EventSubscriptions []string `json:"event_subscriptions"`
	JumpLink           string   `json:"jump_link"`
}

func EventSubscription(ctx *gin.Context) error {
	var sub EventSubscriptionParams
	if err := ctx.ShouldBindJSON(&sub); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    http.StatusBadRequest,
			"message": err.Error(),
			"data":    "",
		})
		return nil
	}
	if !strings.Contains(sub.Host, "http://") && !strings.Contains(sub.Host, "https://") {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    http.StatusBadRequest,
			"message": "host格式不正确,请填写以http://或https://开头的地址",
			"data":    "",
		})
		return nil
	}
	record := dao.WebhookEventUrlConfigModel{
		AuthType:           sub.AuthType,
		Host:               sub.Host,
		TokenValue:         sub.TokenValue,
		URLTokenName:       sub.URLTokenName,
		HeaderTokenName:    sub.HeaderTokenName,
		Secret:             sub.Secret,
		SysKey:             sub.SysKey,
		EventSubscriptions: strings.Join(sub.EventSubscriptions, ","),
		ConfigMd5Hash:      utils.Md5Hash(fmt.Sprint(sub.AuthType) + sub.Host + sub.TokenValue + sub.HeaderTokenName + sub.URLTokenName + sub.Secret),
		JumpLink:           sub.JumpLink,
	}
	if record.Secret == "" && record.TokenValue != "" && record.AuthType == dao.AuthTypeLongyi {
		record.Secret = record.TokenValue
	}
	if err := dao.NewWebhookEventUrlConfigModel().Create(&record); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    http.StatusBadRequest,
			"message": err.Error(),
			"data":    "",
		})
		return nil
	}
	ctx.JSON(http.StatusOK, responseOk)
	return nil
}

func EventSubscriptionDelete(ctx *gin.Context) error {
	params, err := request.Validate(ctx, &struct {
		Ids []int64 `json:"ids" uri:"ids" form:"ids" validate:"required,dive,number" zh:"主键id"`
	}{})

	if err != nil {
		logger.Errorf("[ERROR] EventSubscriptionDelete error: %v\n", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    http.StatusBadRequest,
			"message": err.Error(),
			"data":    "",
		})
		return nil
	}
	err = dao.NewWebhookEventUrlConfigModel().Delete(params.Ids)
	if err != nil {
		logger.Errorf("[ERROR] EventSubscriptionDelete exec error: %v\n", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    http.StatusInternalServerError,
			"message": "删除失败" + err.Error(),
			"data":    "",
		})
		return nil
	}
	ctx.JSON(http.StatusOK, responseOk)
	return nil
}

func EventSubscriptionList(ctx *gin.Context) error {
	page := cast.ToInt(ctx.Query("page"))
	pageSize := cast.ToInt(ctx.Query("per_page"))
	id := cast.ToInt(ctx.Query("id"))
	keyword := ctx.Query("keyword")
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}
	var opts []dao.HandleFunc
	if keyword != "" {
		opts = append(opts, func(tx *gorm.DB) {
			tx.Where("host like ?", "%"+keyword+"%")
		})
	}
	if id > 0 {
		opts = append(opts, func(tx *gorm.DB) {
			tx.Where("id = ?", id)
		})
	}
	list, total, err := dao.NewWebhookEventUrlConfigModel().List(page, pageSize, opts...)
	if err != nil {
		logger.Errorf("[ERROR] EventSubscriptionList error: %v\n", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    http.StatusInternalServerError,
			"message": err.Error(),
			"data":    "",
		})
		return nil
	}
	var items = make([]EventSubscriptionParams, 0, len(list))
	for _, v := range list {
		items = append(items, EventSubscriptionParams{
			Id:                 v.Id,
			CreatedAt:          v.CreatedAt.Format(utils.DateTimeLayout),
			UpdatedAt:          v.UpdatedAt.Format(utils.DateTimeLayout),
			AuthType:           v.AuthType,
			Host:               v.Host,
			TokenValue:         v.TokenValue,
			URLTokenName:       v.URLTokenName,
			HeaderTokenName:    v.HeaderTokenName,
			Secret:             v.Secret,
			SysKey:             v.SysKey,
			EventSubscriptions: strings.Split(v.EventSubscriptions, ","),
			JumpLink:           v.JumpLink,
		})
	}
	ctx.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "success",
		"data": map[string]interface{}{
			"page":     page,
			"per_page": pageSize,
			"total":    total,
			"items":    items,
		},
	})
	return nil
}

func GetAuthTypeHandle(ctx *gin.Context) error {
	ctx.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "success",
		"data": map[string]interface{}{
			"auth_type": GetAuthType(),
		},
	})
	return nil
}
