//go:build longji

package webhook

import (
	"encoding/json"
	"fmt"
	"fobrain/webhook/dao"
	"testing"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/stretchr/testify/assert"
)

func TestLongyiSend(t *testing.T) {
	// 准备测试数据
	m := &SenderMsg{
		UrlConf: dao.WebhookEventUrlConfigModel{
			Host:   "http://localhost:8080",
			SysKey: "testSysKey",
			Secret: "testSecret",
		},
		Info: json.RawMessage(`{
			"Details": [
				{
					"ReceiverPersons": ["user1", "user2"]
				}
			],
			"OpName": "Operator"
		}`),
	}
	var info VulnerabilityMsg
	err := json.Unmarshal(m.Info, &info)
	if err != nil {
		t.Fatal(err)
	}

	// 模拟 getHostIP 函数
	patches := gomonkey.ApplyFunc(getHostIP, func() (string, error) {
		return "127.0.0.1", nil
	})
	defer patches.Reset()

	// 模拟 HTTPRequest 函数
	gomonkey.ApplyFunc(HTTPRequestBodyNotSign, func(method, url string, params map[string]string, body map[string]interface{}, secret string) (string, error) {
		return `{"status": "success"}`, nil
	})

	// 执行被测试的函数
	err = longyiMMCTodoAdd(m.UrlConf, info)

	// 断言期望的结果
	assert.NoError(t, err)
}

func TestGetHostIP(t *testing.T) {
	ip, err := getHostIP()
	if err != nil {
		t.Errorf("获取 IP 地址时出错: %v", err)
	}
	if ip == "" {
		t.Error("返回的 IP 地址为空")
	}
	t.Log(ip)
}

func TestGetSign(t *testing.T) {
	params := map[string]string{
		"timestamp": "1629250988980",
		"sysKey":    "demo_sys",
	}
	body := map[string]interface{}{
		"test": "123",
		"array": []interface{}{
			map[string]interface{}{"test": "123", "sour": "124123", "abc": 12312},
			map[string]interface{}{"bce": 12312, "abd": 1234},
		},
		"object": map[string]interface{}{"aaaa": "sadfa", "hasdfas": 123412},
	}
	secret := "123abc"

	signWithoutBody := generateSignWithoutBody(params, secret)
	signWithBody := generateSignWithBody(params, body, secret)

	fmt.Printf("Sign without body:%s\n是否一样:%v\n", signWithoutBody, signWithoutBody == "2380c9fae08230fcffcc5273415e0725")
	fmt.Printf("Sign with body:%s\n是否一样:%v\n", signWithBody, signWithBody == "58438f8caed1a421a11a95896f1f8b65")
	if signWithoutBody != "2380c9fae08230fcffcc5273415e0725" {
		t.Fatal("无body的签名生成方式不对")
	}
	if signWithBody != "58438f8caed1a421a11a95896f1f8b65" {
		t.Fatal("有body的签名生成方式不对")
	}
}

func TestMd5Hash(t *testing.T) {
	tests := []struct {
		input    string
		expected string
	}{
		{"hello", "5d41402abc4b2a76b9719d911017c592"},
	}

	for _, test := range tests {
		result := md5Hash(test.input)
		if result != test.expected {
			t.Errorf("md5Hash(%s) = %s; want %s", test.input, result, test.expected)
		}
	}
}

func TestHTTPRequest(t *testing.T) {
	// 测试用例
	tests := []struct {
		method   string
		url      string
		params   map[string]string
		body     map[string]interface{}
		secret   string
		expected string // 预期响应
	}{
		{"GET", "http://example.com", map[string]string{"sysKey": "testKey", "timestamp": "1234567890"}, nil, "secret", "expectedResponse"},                                     // 示例
		{"POST", "http://example.com", map[string]string{"sysKey": "testKey", "timestamp": "1234567890"}, map[string]interface{}{"key": "value"}, "secret", "expectedResponse"}, // 示例
	}

	for _, test := range tests {
		result, err := HTTPRequestBodyNotSign(test.method, test.url, test.params, test.body, test.secret)
		if err != nil {
			t.Errorf("HTTPRequest(%s, %s) returned an error: %v", test.method, test.url, err)
		}
		t.Log(result)
	}
}

func Test_longyiSend(t *testing.T) {
	t.Run("vul_distribute", func(t *testing.T) {
		patches := gomonkey.ApplyFuncReturn(HTTPRequestBodyNotSign, `{"status": "success"}`, nil)
		defer patches.Reset()
		info := VulnerabilityMsg{
			Event:      "vul_distribute",
			Title:      "『漏洞修复提醒』您好，2024-11-20 15:39:13有１个漏洞派发给您，请登录系统进行查看",
			OpName:     "超级管理员",
			BusinessId: "a1b63f4794714b9a9cbc8f01c7e419ea",
			MsgUuid:    "testMsgUuid",
			Details: []VulMsgDetails{
				{
					VulnerabilityId:   "a1b63f4794714b9a9cbc8f01c7e419ea",
					VulnerabilityName: "亿赛通电子文档安全管理系统 MailMessageLogServices 接口远程代码执行漏洞",
					RepairDeadline:    "2024-11-16 13:43:09",
					ReceiverPersons:   []string{"wangwei325"},
				},
			},
		}

		err := longyiMMCTodoAdd(dao.WebhookEventUrlConfigModel{
			AuthType: 3,
			Host:     "https://emap-test.longi.com:31443",
			SysKey:   "CAASM",
			Secret:   "40698259b48373847b230bded858a7f3",
		}, info)
		if err != nil {
			t.Fatal(err)
			t.Log("failed")
		}
	})
	t.Run("vul_retest", func(t *testing.T) {
		patches := gomonkey.ApplyFuncReturn(HTTPRequestBodyNotSign, `{"status": "success"}`, nil)
		defer patches.Reset()
		err := longyiMMCTodoDone(dao.WebhookEventUrlConfigModel{
			AuthType: 3,
			Host:     "https://emap-test.longi.com:31443",
			SysKey:   "CAASM",
			Secret:   "40698259b48373847b230bded858a7f3",
		}, "testMsgUuid")
		if err != nil {
			t.Fatal(err)
		}
	})
}
