#!/bin/bash

git_branch=$1
release_version=$2
pack_docker_compose=$3

release_greek_alphabet_version='beta'
if [ -z "$git_branch" ]; then
  echo "git branch is empty"
  exit 1
fi

git checkout .

git checkout $git_branch
git status

git_branch_current=$(git rev-parse --abbrev-ref HEAD) # 查询当前分支
echo "git_branch_current: $git_branch_current"

if [ "$git_branch" != "$git_branch_current" ]; then
  echo "git branch is not $git_branch"
  exit 1
fi

git pull

echo "--clean upgrade package folder and fobrain-upgrade-v$release_version-$release_greek_alphabet_version-arm64.tar.gz"
rm -rf fobrain-upgrade-v$release_version-$release_greek_alphabet_version-arm64.tar.gz
rm -rf upgrade-package-arm64
mkdir -p upgrade-package-arm64
mkdir -p upgrade-package-arm64/build-images
mkdir -p upgrade-package-arm64/build-images/fobrain

echo "--go mod tidy"
go mod tidy

echo "change arch to arm64"
go env -w GOARCH=arm64

# 从git中获取版本号
code_version=""
if [[ -z $code_version ]]; then
    if [ -d ".git" ]; then
        # 获取git分支名
        branch=`git symbolic-ref HEAD | cut -b 12-`
        # 获取git commit id
        commitId=`git rev-parse HEAD`
        # 获取git commit id前8位
        # commitId=${commitId:0:8}
        code_version="$branch-$commitId"
    else
        code_version="unknown"
    fi
fi

echo "--building fobrain"
cd fobrain && go build -ldflags "-X main.Version=$code_version -X main.BuildTime=$(date +%Y-%m-%d_%H:%M:%S)" -o fobrain-linux-arm64 && cd ..

echo "--building mergeService"
cd mergeService && go build -o merge-linux-arm64 && cd ..

echo "--building cmd"
cd cmd && go build -o cmd-linux-arm64 && cd ..

echo "--moving files"
mv fobrain/fobrain-linux-arm64 ./upgrade-package-arm64/fobrain.server
mv mergeService/merge-linux-arm64 ./upgrade-package-arm64/merge-service.server
mv cmd/cmd-linux-arm64 ./upgrade-package-arm64/cmd-migrate
echo "--copy version"
cp version ./upgrade-package-arm64/version

echo "--copy system_upgrade.sh"
cp ./upgrade/system_upgrade_arm64.sh ./upgrade-package-arm64/

echo "--copy dist.tar.gz"
cp /data/dist.tar.gz ./upgrade-package-arm64/

echo "--copy config.json"
cp ./install/amd64/v2/config.json ./upgrade-package-arm64/

echo "--copy public"
cp -r ./storage/app/public ./upgrade-package-arm64/

echo "--开始构建镜像--"
cp ./install/amd64/v2/config.json ./upgrade-package-arm64/build-images/fobrain/fobrain.json
cp version ./upgrade-package-arm64/build-images/fobrain/
cp ./upgrade-package-arm64/fobrain.server ./upgrade-package-arm64/build-images/fobrain/fobrain
cp ./upgrade-package-arm64/merge-service.server ./upgrade-package-arm64/build-images/fobrain/merge-service
cp ./upgrade-package-arm64/cmd-migrate ./upgrade-package-arm64/build-images/fobrain/cmd

cp ./install/arm64/build/Dockerfile-fobrain ./upgrade-package-arm64/build-images/
cp ./install/arm64/build/Dockerfile-elasticsearch-s3 ./upgrade-package-arm64/build-images

cd ./upgrade-package-arm64/build-images
# 构建fobrain镜像
echo "--构建fobrain镜像--"
docker build -t fobrain:latest -f Dockerfile-fobrain .
docker save fobrain:latest -o fobrain.tar

# 构建elasticsearch镜像
echo "--构建elasticsearch镜像--"
#docker build -t es-arm64:7.17.26 -f Dockerfile-elasticsearch-s3 .
docker save es-arm64:7.17.26 -o elasticsearch.tar

cd ../../

echo "--构建fobrain镜像完成--"

if [ "$pack_docker_compose" == "true" ]; then
  echo "--copy docker-compose.yaml"
  cp ./install/arm64/v2/docker-compose.yaml ./upgrade-package-arm64/
fi

echo "-------相关文件 已放入upgrade-package-arm64"

cd upgrade-package-arm64
tar -cvzf fobrain-upgrade-v$release_version-$release_greek_alphabet_version-arm64.tar.gz ./*

mv fobrain-upgrade-v$release_version-$release_greek_alphabet_version-arm64.tar.gz ../
cd ../
rm -rf upgrade-package-arm64

echo "--打包结束, 路径:$(pwd)/fobrain-upgrade-v$release_version-$release_greek_alphabet_version-arm64.tar.gz"

echo "change arch to amd64"
go env -w GOARCH=amd64

echo "--end--"