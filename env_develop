####配置文件样例#####
ENV=development
LOG_LEVEL=debug
LISTEN=0.0.0.0:8090
# 数据库配置
# DB_HOST=************
# DB_PORT=3306
# DB_USER=root
# DB_PASSWORD=BMH@mysql2022
# DB_DATABASE=fobrain
# DB_CHARSET=utf8mb4

DB_HOST=127.0.0.1
DB_PORT=3306
DB_USER=root
DB_PASSWORD=
DB_DATABASE=fobrain
DB_CHARSET=utf8mb4

#Redis配置

REDIS_PASSWORD=
REDIS_HOST=127.0.0.1
REDIS_PORT=6379


# ES配置
ELASTIC_HOST=127.0.0.1
ELASTIC_PORT=9200
ELASTIC_SNIFF=false
LOG_FILE=/Users/<USER>/program/go/src/xinan/FOBrain/fobrain/storage/log/fobrain.log
PPROF_ENABLE=false
# 文件保存目录
STORAGE=/Users/<USER>/program/go/src/xinan/FOBrain/fobrain/storage


FTPD_STORAGE=/Users/<USER>/program/go/src/xinan/FOBrain/fobrain/storage/ftp/