package frame

import (
	"fobrain/models/elastic"
	dbmodel "fobrain/models/mysql/strategy"
	"fobrain/pkg/utils"
	"slices"
	"sort"
	"strings"
)

// FieldValue 字段值及其来源信息
type FieldValue[T any] struct {
	elastic.FieldValInfo
	Values []T // 字段值
}

// GetSourceByPriority 获取可信源及其优先级信息
// 返回值：优先级顺序，可信源信息(key=优先级，value=源 ID 集合)
func GetSourceByPriority(s *dbmodel.Strategy) ([]uint64, map[uint64][]string) {
	if s == nil || len(s.SourcePriority) == 0 {
		return []uint64{}, nil
	}
	// 根据不可信标识排除
	// 可信源集合，key=优先级，value=源 ID 集合
	sourceList := make(map[uint64][]string, 0)
	// 可信源的优先级
	sourcePriorityMap := make(map[uint64]struct{})
	for sid, pri := range s.SourcePriority {
		if len(s.UntrustedSource) > 0 && slices.Contains(s.UntrustedSource, sid) {
			// 不可信名单中匹配到了，说明源不可信
			continue
		}

		// 添加到可信源列表中
		sourceList[pri] = append(sourceList[pri], sid)
		// 收集所有可信源的优先级
		sourcePriorityMap[pri] = struct{}{}
	}
	if len(sourceList) == 0 {
		return []uint64{}, nil
	}
	// 将map中的key转换为slice
	sourcePriority := make([]uint64, 0, len(sourcePriorityMap))
	for pri := range sourcePriorityMap {
		sourcePriority = append(sourcePriority, pri)
	}
	// 按照优先级排序
	sort.Slice(sourcePriority, func(i, j int) bool {
		return sourcePriority[i] < sourcePriority[j]
	})
	return sourcePriority, sourceList
}

// 预处理融合策略
func PreProcessStrategy(strategies []*dbmodel.Strategy) map[string]*dbmodel.Strategy {
	strategyMap := make(map[string]*dbmodel.Strategy)
	for _, strategy := range strategies {
		fieldName := strings.ToLower(strategy.FieldName)
		strategyMap[fieldName] = strategy
	}
	return strategyMap
}

// StrFieldCheck 字符串字段检查
func StrFieldCheck[T ~string](field T) (T, bool) {
	if field == "" {
		return "", false
	}
	return field, true
}

// IntFieldCheck 整数字段检查
func IntFieldCheck[T ~int](field T) (T, bool) {
	if field == 0 {
		return 0, false
	}
	return field, true
}

// StrFieldResultHandler 字符串字段结果处理
func StrFieldResultHandler[T ~string](field *T, result []T) error {
	if len(result) == 0 {
		return nil
	}
	*field = result[0]
	return nil
}

// StrSliceFieldResultHandler 字符串切片字段结果处理
func StrSliceFieldResultHandler[T ~string](field *[]T, result []T) error {
	if len(result) == 0 {
		*field = []T{}
		return nil
	}
	if len(*field) == 0 {
		*field = []T{}
	}
	*field = append(*field, result...)
	*field = utils.ListDistinctNonZero(*field)
	return nil
}

// SliceStrSliceFieldResultHandler 字符串切片字段结果处理
func SliceStrSliceFieldResultHandler[T ~string](field *[]T, result [][]T) error {
	if len(result) == 0 {
		*field = []T{}
		return nil
	}
	if len(*field) == 0 {
		*field = []T{}
	}
	for _, v := range result {
		*field = append(*field, v...)
	}
	*field = utils.ListDistinctNonZero[T](*field)
	return nil
}

// IntFieldResultHandler 整数字段结果处理
func IntFieldResultHandler[T ~int](field *T, result []T) error {
	if len(result) == 0 {
		return nil
	}
	*field = result[0]
	return nil
}

// IntSliceFieldResultHandler 整数切片字段结果处理
func IntSliceFieldResultHandler[T ~int](field *[]T, result []T) error {
	if len(result) == 0 {
		*field = []T{}
		return nil
	}
	if len(*field) == 0 {
		*field = []T{}
	}
	*field = append(*field, result...)
	*field = utils.ListDistinctNonZero(*field)
	return nil
}
