package frame

import (
	"errors"
	logs "fobrain/mergeService/utils/log"
	es_model "fobrain/models/elastic"
	"fobrain/models/elastic/device"
	"fobrain/models/mysql/strategy"
	"fobrain/pkg/utils"
	"slices"
	"strings"
	"sync"
)

// FieldHandlerForDevice 字段处理器接口
type FieldHandlerForDevice[T any] interface {
	// GetAllData 返回是否需要获取所有数据源的数据
	// 返回true时会调用getAllFieldValForDevice获取所有数据
	// 返回false时会按照优先级策略获取数据
	GetAllData() bool

	// Getter 获取字段值
	// sourcePriority: 数据源优先级列表
	// sourceList: 每个优先级对应的数据源ID列表
	// allData: 所有数据源的数据
	// 返回值：
	// *FieldValue[any]: 字段值及其来源信息
	// error: 错误信息
	Getter(sourcePriority []uint64, sourceList map[uint64][]string, allData map[string]*device.ProcessDevice) (*FieldValue[T], error)

	// ResultHandler 保存字段结果
	ResultHandler(device *device.Device, result []T) error
}

// GenericFieldHandlerForDevice 字段处理器实现
type GenericFieldHandlerForDevice[T any] struct {
	fieldName     string
	getAllData    bool
	checkFunc     func(dataSource *device.ProcessDevice) (T, bool)
	resultHandler func(device *device.Device, result []T) error
}

var (
	fieldHandlerMapForDevice map[string]interface{}
	handlerMutexForDevice    sync.RWMutex // 添加读写锁
	onceForDevice            sync.Once
)

func init() {
	onceForDevice.Do(func() {
		fieldHandlerMapForDevice = make(map[string]interface{})
	})
}

// GetAllFieldHandlerForDevice 获取所有字段处理器
func GetAllFieldHandlerForDevice() map[string]interface{} {
	return fieldHandlerMapForDevice
}

// GetFieldHandlerForDevice 获取字段处理器
func GetFieldHandlerForDevice[T any](fieldName string) (FieldHandlerForDevice[T], bool) {
	fieldName = strings.ToLower(fieldName)
	fieldHandler, ok := fieldHandlerMapForDevice[fieldName]
	if !ok {
		return nil, false
	}
	h, ok := fieldHandler.(FieldHandlerForDevice[T])
	return h, ok
}

// RegisterFieldHandlerForDevice 注册字段处理器
// fieldName 字段名
// getAllData 是否获取所有数据
// checkFunc 字段值获取函数，返回值为字段值和是否获取成功
// resultHandler 字段结果处理函数，返回值为错误信息
func RegisterFieldHandlerForDevice[T any](fieldName string, getAllData bool, checkFunc func(dataSource *device.ProcessDevice) (T, bool), resultHandler func(device *device.Device, result []T) error) error {
	handlerMutexForDevice.Lock()
	defer handlerMutexForDevice.Unlock()
	if fieldName == "" {
		return errors.New("field name cannot be empty")
	}
	if checkFunc == nil {
		return errors.New("checkFunc cannot be nil")
	}
	if resultHandler == nil {
		return errors.New("resultHandler cannot be nil")
	}
	fieldName = strings.ToLower(fieldName)
	if _, ok := GetFieldHandlerForDevice[T](fieldName); ok {
		return errors.New("field handler for " + fieldName + " already exists")
	}
	fieldHandlerMapForDevice[fieldName] = &GenericFieldHandlerForDevice[T]{
		fieldName:     fieldName,
		getAllData:    getAllData,
		checkFunc:     checkFunc,
		resultHandler: resultHandler,
	}
	return nil
}

func (h *GenericFieldHandlerForDevice[T]) Getter(sourcePriority []uint64, sourceList map[uint64][]string, allData map[string]*device.ProcessDevice) (*FieldValue[T], error) {
	if h.getAllData {
		return getAllFieldValForDevice[T](allData, h.checkFunc)
	}
	return getFieldValByStrategyForDevice[T](sourcePriority, sourceList, allData, h.checkFunc)
}

func (h *GenericFieldHandlerForDevice[T]) ResultHandler(device *device.Device, result []T) error {
	return h.resultHandler(device, result)
}

func (h *GenericFieldHandlerForDevice[T]) GetAllData() bool {
	return h.getAllData
}

// getFieldValByStrategyForDevice 根据指定的策略获取字段值
// sourcePriority 优先级顺序
// sourceList 源节点列表,key为优先级,value为源节点id列表
// allData 所有数据,key为源节点id,value为源节点数据
// checkFunc 字段值获取函数，返回值为字段值和是否获取成功
// 返回值为字段值列表、源节点id列表、节点id列表、任务数据id列表、处理id列表、错误信息
func getFieldValByStrategyForDevice[T any](sourcePriority []uint64, sourceList map[uint64][]string, allData map[string]*device.ProcessDevice, checkFunc func(dataSource *device.ProcessDevice) (T, bool)) (*FieldValue[T], error) {
	result := make([]T, 0)
	sourceIds := make([]uint64, 0)
	nodeIds := make([]uint64, 0)
	taskDataIds := make([]string, 0)
	processIds := make([]string, 0)

	// 按照策略规则进行融合
	// 遍历优先级顺序
	for _, p := range sourcePriority {
		// 当前优先级是否存在非空值的标识
		hasNonEmptyValue := false
		// 遍历每个源节点的最新数据，按照策略规则进行融合
		for id, sd := range allData {
			// 如果当前源在当前优先级中,就可以计算当前源的值
			if slices.Contains(sourceList[p], id) {
				// 计算当前源的值
				r, ok := checkFunc(sd)
				if ok {
					result = append(result, r)
					hasNonEmptyValue = true
					sourceIds = append(sourceIds, sd.GetSource())
					nodeIds = append(nodeIds, sd.GetNode())
					taskDataIds = append(taskDataIds, sd.GetTaskDataId())
					processIds = append(processIds, sd.GetId())
				}
				// 继续寻找下一个符合条件的源
				continue
			}
		}
		// 如果当前优先级存在非空值，则跳出优先级循环
		if hasNonEmptyValue {
			break
		}
	}
	return &FieldValue[T]{
		Values: result,
		FieldValInfo: es_model.FieldValInfo{
			SourceIds:   sourceIds,
			NodeIds:     nodeIds,
			TaskDataIds: taskDataIds,
			ProcessIds:  processIds,
		},
	}, nil
}

// getAllFieldValForDevice 获取所有源提供的字段值
// allData 所有数据,key为源节点id,value为源节点数据
// fieldName 字段名,区分大小写
// checkFunc 字段值获取函数，返回值为字段值和是否获取成功
func getAllFieldValForDevice[T any](allData map[string]*device.ProcessDevice, checkFunc func(dataSource *device.ProcessDevice) (T, bool)) (*FieldValue[T], error) {
	result := make([]T, 0)
	sourceIds := make([]uint64, 0)
	nodeIds := make([]uint64, 0)
	taskDataIds := make([]string, 0)
	processIds := make([]string, 0)

	// 遍历所有数据
	for _, sd := range allData {
		// 计算当前源的值
		r, ok := checkFunc(sd)
		if ok {
			result = append(result, r)
			sourceIds = append(sourceIds, sd.GetSource())
			nodeIds = append(nodeIds, sd.GetNode())
			taskDataIds = append(taskDataIds, sd.GetTaskDataId())
			processIds = append(processIds, sd.GetId())
		}
	}

	return &FieldValue[T]{
		Values: result,
		FieldValInfo: es_model.FieldValInfo{
			SourceIds:   sourceIds,
			NodeIds:     nodeIds,
			TaskDataIds: taskDataIds,
			ProcessIds:  processIds,
		},
	}, nil
}

func DeviceFieldProcess(d *device.Device, strategies []*strategy.Strategy, latestData map[string]*device.ProcessDevice) []*es_model.FieldValInfo {
	// 预处理融合策略
	strategyMap := PreProcessStrategy(strategies)
	// 获取所有字段及对应的处理器
	fieldHandlerMap := GetAllFieldHandlerForDevice()
	// 字段采信信息
	fieldValInfoList := make([]*es_model.FieldValInfo, 0)
	// 遍历字段
	for fieldName, fieldHandler := range fieldHandlerMap {
		logs.GetLogger("d").Debugf("开始处理字段. fieldName: %s", fieldName)
		func(fieldName string, fieldHandler interface{}) {
			defer func() {
				if r := recover(); r != nil {
					logs.GetLogger("d").Errorf("处理字段失败. fieldName: %s, err: %v", fieldName, r)
				}
				logs.GetLogger("d").Debugf("结束处理字段. fieldName: %s", fieldName)
			}()
			fieldValInfo := &es_model.FieldValInfo{}
			switch fh := fieldHandler.(type) {
			case FieldHandlerForDevice[string]:
				fieldValInfo = processFieldHandlerDevice[string](fh, fieldName, d, strategyMap, latestData)
			case FieldHandlerForDevice[[]string]:
				fieldValInfo = processFieldHandlerDevice[[]string](fh, fieldName, d, strategyMap, latestData)
			case FieldHandlerForDevice[int]:
				fieldValInfo = processFieldHandlerDevice[int](fh, fieldName, d, strategyMap, latestData)
			case FieldHandlerForDevice[int64]:
				fieldValInfo = processFieldHandlerDevice[int64](fh, fieldName, d, strategyMap, latestData)
			case FieldHandlerForDevice[uint64]:
				fieldValInfo = processFieldHandlerDevice[uint64](fh, fieldName, d, strategyMap, latestData)
			case FieldHandlerForDevice[[]int]:
				fieldValInfo = processFieldHandlerDevice[[]int](fh, fieldName, d, strategyMap, latestData)
			case FieldHandlerForDevice[[]int64]:
				fieldValInfo = processFieldHandlerDevice[[]int64](fh, fieldName, d, strategyMap, latestData)
			case FieldHandlerForDevice[[]uint64]:
				fieldValInfo = processFieldHandlerDevice[[]uint64](fh, fieldName, d, strategyMap, latestData)
			case FieldHandlerForDevice[float64]:
				fieldValInfo = processFieldHandlerDevice[float64](fh, fieldName, d, strategyMap, latestData)
			case FieldHandlerForDevice[[]*device.NetworkCardInfo]:
				fieldValInfo = processFieldHandlerDevice[[]*device.NetworkCardInfo](fh, fieldName, d, strategyMap, latestData)
			case FieldHandlerForDevice[map[string]string]:
				fieldValInfo = processFieldHandlerDevice[map[string]string](fh, fieldName, d, strategyMap, latestData)
			default:
				logs.GetLogger("device").Errorf("不支持的字段处理器类型. fieldName: %s, type: %T", fieldName, fh)
			}
			if fieldValInfo != nil {
				fieldValInfo.FieldName = fieldName
				fieldValInfoList = append(fieldValInfoList, fieldValInfo)
			}
		}(fieldName, fieldHandler)

	}
	d.SourceIds = utils.ListDistinctNonZero(d.SourceIds)
	d.NodeIds = utils.ListDistinctNonZero(d.NodeIds)
	d.TaskDataIds = utils.ListDistinctNonZero(d.TaskDataIds)
	d.ProcessIds = utils.ListDistinctNonZero(d.ProcessIds)
	return fieldValInfoList
}
func processFieldHandlerDevice[T any](fieldHandler FieldHandlerForDevice[T], fieldName string, device *device.Device, strategyMap map[string]*strategy.Strategy, latestData map[string]*device.ProcessDevice) *es_model.FieldValInfo {
	if fieldHandler.GetAllData() {
		// 处理字段
		fieldVal, err := fieldHandler.Getter(nil, nil, latestData)
		if err != nil {
			logs.GetLogger("device").Errorf("计算字段值失败. fieldName: %s, err: %v", fieldName, err)
			return nil
		}
		// 保存字段结果
		err = fieldHandler.ResultHandler(device, fieldVal.Values)
		if err != nil {
			logs.GetLogger("device").Errorf("保存字段结果失败. fieldName: %s, err: %v", fieldName, err)
			return nil
		}
		// 获取所有源的字段值，不再保存采信的sourceIds、nodeIds、taskDataIds、processIds
		return &fieldVal.FieldValInfo
	} else {
		if st, ok := strategyMap[fieldName]; ok {
			sourcePriority, sourceList := GetSourceByPriority(st)
			fieldVal, err := fieldHandler.Getter(sourcePriority, sourceList, latestData)
			if err != nil {
				logs.GetLogger("device").Errorf("计算字段值失败. fieldName: %s, err: %v", fieldName, err)
				return nil
			}
			// 保存字段结果
			err = fieldHandler.ResultHandler(device, fieldVal.Values)
			if err != nil {
				logs.GetLogger("device").Errorf("保存字段结果失败. fieldName: %s, err: %v", fieldName, err)
				return nil
			}
			device.SourceIds = append(device.SourceIds, fieldVal.SourceIds...)
			device.NodeIds = append(device.NodeIds, fieldVal.NodeIds...)
			device.TaskDataIds = append(device.TaskDataIds, fieldVal.TaskDataIds...)
			device.ProcessIds = append(device.ProcessIds, fieldVal.ProcessIds...)
			return &fieldVal.FieldValInfo
		}
	}
	return nil
}
