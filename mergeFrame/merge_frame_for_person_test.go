package frame

import (
	"errors"
	"fobrain/models/elastic/staff"
	"fobrain/models/mysql/strategy"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestGetFieldValByStrategyForPerson(t *testing.T) {
	tests := []struct {
		name           string
		sourcePriority []uint64
		sourceList     map[uint64][]string
		allData        map[string]*staff.ProcessStaff
		wantValues     []string
		wantSourceIds  []uint64
		wantNodeIds    []uint64
		wantTaskIds    []string
		wantProcessIds []string
		wantErr        bool
	}{
		{
			name:           "空数据测试",
			sourcePriority: []uint64{},
			sourceList:     map[uint64][]string{},
			allData:        map[string]*staff.ProcessStaff{},
			wantValues:     []string{},
			wantSourceIds:  []uint64{},
			wantNodeIds:    []uint64{},
			wantTaskIds:    []string{},
			wantProcessIds: []string{},
			wantErr:        false,
		},
		{
			name:           "单一优先级数据获取",
			sourcePriority: []uint64{1},
			sourceList: map[uint64][]string{
				1: {"source1", "source2"},
			},
			allData: map[string]*staff.ProcessStaff{
				"source1": {
					Name:       "test1",
					Source:     1,
					Node:       1,
					TaskDataId: "task1",
					Id:         "process1",
				},
				"source2": {
					Name:       "test2",
					Source:     2,
					Node:       2,
					TaskDataId: "task2",
					Id:         "process2",
				},
			},
			wantValues:     []string{"test1", "test2"},
			wantSourceIds:  []uint64{1, 2},
			wantNodeIds:    []uint64{1, 2},
			wantTaskIds:    []string{"task1", "task2"},
			wantProcessIds: []string{"process1", "process2"},
			wantErr:        false,
		},
		{
			name:           "多优先级数据获取",
			sourcePriority: []uint64{1, 2},
			sourceList: map[uint64][]string{
				1: {"source1"},
				2: {"source2"},
			},
			allData: map[string]*staff.ProcessStaff{
				"source1": {
					Name:       "test1",
					Source:     1,
					Node:       1,
					TaskDataId: "task1",
					Id:         "process1",
				},
				"source2": {
					Name:       "test2",
					Source:     2,
					Node:       2,
					TaskDataId: "task2",
					Id:         "process2",
				},
			},
			wantValues:     []string{"test1"},
			wantSourceIds:  []uint64{1},
			wantNodeIds:    []uint64{1},
			wantTaskIds:    []string{"task1"},
			wantProcessIds: []string{"process1"},
			wantErr:        false,
		},
	}

	checkFunc := func(dataSource *staff.ProcessStaff) (string, bool) {
		if dataSource.Name == "" {
			return "", false
		}
		return dataSource.Name, true
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := getFieldValByStrategyForPerson(
				tt.sourcePriority,
				tt.sourceList,
				tt.allData,
				checkFunc,
			)

			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.ElementsMatch(t, tt.wantValues, result.Values)
				assert.ElementsMatch(t, tt.wantSourceIds, result.SourceIds)
				assert.ElementsMatch(t, tt.wantNodeIds, result.NodeIds)
				assert.ElementsMatch(t, tt.wantTaskIds, result.TaskDataIds)
				assert.ElementsMatch(t, tt.wantProcessIds, result.ProcessIds)
			}
		})
	}
}

func TestGetAllFieldValForPerson(t *testing.T) {
	tests := []struct {
		name           string
		allData        map[string]*staff.ProcessStaff
		wantValues     []string
		wantSourceIds  []uint64
		wantNodeIds    []uint64
		wantTaskIds    []string
		wantProcessIds []string
		wantErr        bool
	}{
		{
			name:           "空数据测试",
			allData:        map[string]*staff.ProcessStaff{},
			wantValues:     []string{},
			wantSourceIds:  []uint64{},
			wantNodeIds:    []uint64{},
			wantTaskIds:    []string{},
			wantProcessIds: []string{},
			wantErr:        false,
		},
		{
			name: "多数据源测试",
			allData: map[string]*staff.ProcessStaff{
				"source1": {
					Name:       "test1",
					Source:     1,
					Node:       1,
					TaskDataId: "task1",
					Id:         "process1",
				},
				"source2": {
					Name:       "test2",
					Source:     2,
					Node:       2,
					TaskDataId: "task2",
					Id:         "process2",
				},
			},
			wantValues:     []string{"test1", "test2"},
			wantSourceIds:  []uint64{1, 2},
			wantNodeIds:    []uint64{1, 2},
			wantTaskIds:    []string{"task1", "task2"},
			wantProcessIds: []string{"process1", "process2"},
			wantErr:        false,
		},
	}

	checkFunc := func(dataSource *staff.ProcessStaff) (string, bool) {
		if dataSource.Name == "" {
			return "", false
		}
		return dataSource.Name, true
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := getAllFieldValForPerson(
				tt.allData,
				checkFunc,
			)

			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.ElementsMatch(t, tt.wantValues, result.Values)
				assert.ElementsMatch(t, tt.wantSourceIds, result.SourceIds)
				assert.ElementsMatch(t, tt.wantNodeIds, result.NodeIds)
				assert.ElementsMatch(t, tt.wantTaskIds, result.TaskDataIds)
				assert.ElementsMatch(t, tt.wantProcessIds, result.ProcessIds)
			}
		})
	}
}

func TestRegisterFieldHandlerForPerson(t *testing.T) {
	// 清空 fieldHandlerMapForPerson 以确保测试的独立性
	fieldHandlerMapForPerson = make(map[string]interface{})

	fieldName := "testField"
	getAllData := true
	checkFunc := func(dataSource *staff.ProcessStaff) (string, bool) {
		return "testValue", true
	}
	resultHandler := func(person *staff.Staff, result []string) error {
		if len(result) == 0 {
			return errors.New("no result")
		}
		return nil
	}

	// 注册字段处理器
	err := RegisterFieldHandlerForPerson[string](fieldName, getAllData, checkFunc, resultHandler)
	assert.NoError(t, err)

	// 尝试再次注册相同的字段处理器，应该返回错误
	err = RegisterFieldHandlerForPerson[string](fieldName, getAllData, checkFunc, resultHandler)
	assert.Error(t, err)
	assert.Equal(t, "field handler for testfield already exists", err.Error())

	// 验证字段处理器是否正确注册
	handler, exists := GetFieldHandlerForPerson[string](fieldName)
	assert.True(t, exists)
	assert.NotNil(t, handler)
	assert.Equal(t, getAllData, handler.GetAllData())
}

func TestPersonFieldProcess(t *testing.T) {
	// 清理已注册的处理器,避免影响其他测试
	fieldHandlerMapForPerson = make(map[string]interface{})

	// 准备测试数据 - getAllData=true
	person := &staff.Staff{
		Id:          "test_person",
		SourceIds:   []uint64{},
		NodeIds:     []uint64{},
		TaskDataIds: []string{},
		ProcessIds:  []string{},
	}

	// 1. 测试 getAllData=true 的场景
	err := RegisterFieldHandlerForPerson(
		"test_field",
		true,
		func(dataSource *staff.ProcessStaff) (string, bool) {
			return "test_value", true
		},
		func(person *staff.Staff, result []string) error {
			person.Id = result[0]
			return nil
		},
	)
	assert.NoError(t, err)

	strategies := []*strategy.Strategy{}
	latestData := map[string]*staff.ProcessStaff{
		"source1": {
			Id:     "process1",
			Source: 1,
			Node:   1,
		},
	}

	PersonFieldProcess(person, strategies, latestData)

	assert.Equal(t, "test_value", person.Id)
	assert.Empty(t, person.SourceIds)
	assert.Empty(t, person.NodeIds)
	assert.Empty(t, person.TaskDataIds)
	assert.Empty(t, person.ProcessIds)

	// 清理处理器
	fieldHandlerMapForPerson = make(map[string]interface{})

	// 2. 测试 getAllData=false 的场景
	err = RegisterFieldHandlerForPerson[string](
		"test_field_2",
		false,
		func(dataSource *staff.ProcessStaff) (string, bool) {
			return "test_value_2", true
		},
		func(person *staff.Staff, result []string) error {
			person.Id = result[0]
			return nil
		},
	)
	assert.NoError(t, err)

	strategies = []*strategy.Strategy{
		{
			FieldName:      "test_field_2",
			SourcePriority: strategy.SourcePriority{"1": uint64(1)},
		},
	}
	latestData = map[string]*staff.ProcessStaff{
		"1": {
			Id:         "process1",
			Source:     1,
			Node:       1,
			TaskDataId: "task1",
		},
	}

	person.Id = "test_person" // 重置数据
	person.SourceIds = []uint64{}
	person.NodeIds = []uint64{}
	person.TaskDataIds = []string{}
	person.ProcessIds = []string{}

	PersonFieldProcess(person, strategies, latestData)

	assert.Equal(t, "test_value_2", person.Id)
	assert.Equal(t, []uint64{1}, person.SourceIds)
	assert.Equal(t, []uint64{1}, person.NodeIds)
	assert.Equal(t, []string{"task1"}, person.TaskDataIds)
	assert.Equal(t, []string{"process1"}, person.ProcessIds)

	// 清理处理器
	fieldHandlerMapForPerson = make(map[string]interface{})

	// 3. 测试处理器返回错误的场景
	err = RegisterFieldHandlerForPerson[string](
		"test_field_3",
		false,
		func(dataSource *staff.ProcessStaff) (string, bool) {
			return "", false // 返回错误
		},
		func(person *staff.Staff, result []string) error {
			return nil
		},
	)
	assert.NoError(t, err)

	strategies = []*strategy.Strategy{
		{
			FieldName:      "test_field_3",
			SourcePriority: strategy.SourcePriority{"1": uint64(1)},
		},
	}

	person.Id = "test_person" // 重置数据
	person.SourceIds = []uint64{}
	person.NodeIds = []uint64{}
	person.TaskDataIds = []string{}
	person.ProcessIds = []string{}

	PersonFieldProcess(person, strategies, latestData)

	assert.Equal(t, "test_person", person.Id)
	assert.Empty(t, person.SourceIds)
	assert.Empty(t, person.NodeIds)
	assert.Empty(t, person.TaskDataIds)
	assert.Empty(t, person.ProcessIds)

	// 清理处理器
	fieldHandlerMapForPerson = make(map[string]interface{})

	// 4. 测试字段没有对应策略的场景
	err = RegisterFieldHandlerForPerson[string](
		"test_field_4",
		false,
		func(dataSource *staff.ProcessStaff) (string, bool) {
			return "test_value_4", true
		},
		func(person *staff.Staff, result []string) error {
			person.Id = result[0]
			return nil
		},
	)
	assert.NoError(t, err)

	strategies = []*strategy.Strategy{} // 空策略

	person.Id = "test_person" // 重置数据
	person.SourceIds = []uint64{}
	person.NodeIds = []uint64{}
	person.TaskDataIds = []string{}
	person.ProcessIds = []string{}

	PersonFieldProcess(person, strategies, latestData)

	assert.Equal(t, "test_person", person.Id)
	assert.Empty(t, person.SourceIds)
	assert.Empty(t, person.NodeIds)
	assert.Empty(t, person.TaskDataIds)
	assert.Empty(t, person.ProcessIds)

	// 清理处理器
	fieldHandlerMapForPerson = make(map[string]interface{})

	// 5. 测试 panic 恢复
	err = RegisterFieldHandlerForPerson[string](
		"test_field_5",
		false,
		func(dataSource *staff.ProcessStaff) (string, bool) {
			panic("test panic")
		},
		func(person *staff.Staff, result []string) error {
			return nil
		},
	)
	assert.NoError(t, err)

	strategies = []*strategy.Strategy{
		{
			FieldName:      "test_field_5",
			SourcePriority: strategy.SourcePriority{"1": uint64(1)},
		},
	}

	person.Id = "test_person" // 重置数据
	person.SourceIds = []uint64{}
	person.NodeIds = []uint64{}
	person.TaskDataIds = []string{}
	person.ProcessIds = []string{}

	assert.NotPanics(t, func() {
		PersonFieldProcess(person, strategies, latestData)
	})

	assert.Equal(t, "test_person", person.Id)
	assert.Empty(t, person.SourceIds)
	assert.Empty(t, person.NodeIds)
	assert.Empty(t, person.TaskDataIds)
	assert.Empty(t, person.ProcessIds)
}
