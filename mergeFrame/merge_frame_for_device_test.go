package frame

import (
	"errors"
	"fobrain/models/elastic/device"
	"fobrain/models/mysql/strategy"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestGetFieldValByStrategyForDevice(t *testing.T) {
	allData := map[string]*device.ProcessDevice{
		"1": {Source: 1, Node: 1, TaskDataId: "task1", Id: "id1", HostName: ""},
		"2": {Source: 2, Node: 2, TaskDataId: "task2", Id: "id2", HostName: "host2"},
	}

	sourcePriority := []uint64{1, 2}
	sourceList := map[uint64][]string{
		1: {"1"},
		2: {"2"},
	}

	checkFunc := func(dataSource *device.ProcessDevice) (string, bool) {
		if dataSource.HostName != "" {
			return dataSource.HostName, true
		}
		return "", false
	}

	result, err := getFieldValByStrategyForDevice(sourcePriority, sourceList, allData, checkFunc)

	assert.NoError(t, err)
	assert.Equal(t, []string{"host2"}, result.Values)
	assert.Equal(t, []uint64{2}, result.SourceIds)
	assert.Equal(t, []uint64{2}, result.NodeIds)
	assert.Equal(t, []string{"task2"}, result.TaskDataIds)
	assert.Equal(t, []string{"id2"}, result.ProcessIds)
}

func TestGetAllFieldValForDevice(t *testing.T) {
	allData := map[string]*device.ProcessDevice{
		"1": {Source: 1, Node: 1, TaskDataId: "task1", Id: "id1", HostName: "host1"},
		"2": {Source: 2, Node: 2, TaskDataId: "task2", Id: "id2", HostName: "host2"},
	}

	checkFunc := func(dataSource *device.ProcessDevice) (string, bool) {
		return dataSource.HostName, true
	}

	result, err := getAllFieldValForDevice(allData, checkFunc)

	assert.NoError(t, err)
	assert.ElementsMatch(t, []string{"host1", "host2"}, result.Values)
	assert.ElementsMatch(t, []uint64{1, 2}, result.SourceIds)
	assert.ElementsMatch(t, []uint64{1, 2}, result.NodeIds)
	assert.ElementsMatch(t, []string{"task1", "task2"}, result.TaskDataIds)
	assert.ElementsMatch(t, []string{"id1", "id2"}, result.ProcessIds)
}

func TestRegisterFieldHandlerForDevice(t *testing.T) {
	// 清空 fieldHandlerMapForDevice 以确保测试的独立性
	fieldHandlerMapForDevice = make(map[string]interface{})

	fieldName := "testField"
	checkFunc := func(dataSource *device.ProcessDevice) (string, bool) {
		return "testValue", true
	}
	resultHandler := func(device *device.Device, result []string) error {
		if len(result) == 0 {
			return errors.New("no result")
		}
		return nil
	}

	// 注册字段处理器
	err := RegisterFieldHandlerForDevice[string](fieldName, true, checkFunc, resultHandler)
	assert.NoError(t, err)

	// 尝试再次注册相同的字段处理器，应该返回错误
	err = RegisterFieldHandlerForDevice[string](fieldName, true, checkFunc, resultHandler)
	assert.Error(t, err)
	assert.Equal(t, "field handler for testfield already exists", err.Error())

	// 验证字段处理器是否正确注册
	handler, exists := GetFieldHandlerForDevice[string](fieldName)
	assert.True(t, exists)
	assert.NotNil(t, handler)
	assert.Equal(t, true, handler.GetAllData())
}

func TestGenericFieldHandlerForDevice_Getter(t *testing.T) {
	// 准备测试数据
	allData := map[string]*device.ProcessDevice{
		"1": {Source: 1, Node: 1, TaskDataId: "task1", Id: "id1", HostName: "host1"},
		"2": {Source: 2, Node: 2, TaskDataId: "task2", Id: "id2", HostName: "host2"},
	}

	sourcePriority := []uint64{1, 2}
	sourceList := map[uint64][]string{
		1: {"1"},
		2: {"2"},
	}

	checkFunc := func(dataSource *device.ProcessDevice) (string, bool) {
		if dataSource.HostName != "" {
			return dataSource.HostName, true
		}
		return "", false
	}

	// 创建字段处理器，getAllData 为 false
	handler := &GenericFieldHandlerForDevice[string]{
		fieldName:  "hostName",
		getAllData: false,
		checkFunc:  checkFunc,
	}

	// 测试 getAllData 为 false 的情况
	result, err := handler.Getter(sourcePriority, sourceList, allData)

	assert.NoError(t, err)
	assert.Equal(t, []string{"host1"}, result.Values)
	assert.Equal(t, []uint64{1}, result.SourceIds)
	assert.Equal(t, []uint64{1}, result.NodeIds)
	assert.Equal(t, []string{"task1"}, result.TaskDataIds)
	assert.Equal(t, []string{"id1"}, result.ProcessIds)

	// 测试 getAllData 为 true 的情况
	handler.getAllData = true
	result, err = handler.Getter(nil, nil, allData)

	assert.NoError(t, err)
	assert.ElementsMatch(t, []string{"host1", "host2"}, result.Values)
	assert.ElementsMatch(t, []uint64{1, 2}, result.SourceIds)
	assert.ElementsMatch(t, []uint64{1, 2}, result.NodeIds)
	assert.ElementsMatch(t, []string{"task1", "task2"}, result.TaskDataIds)
	assert.ElementsMatch(t, []string{"id1", "id2"}, result.ProcessIds)
}

func TestGetFieldHandlerForDevice(t *testing.T) {
	// 清空 fieldHandlerMapForDevice 以确保测试的独立性
	fieldHandlerMapForDevice = make(map[string]interface{})

	fieldName := "testField"
	getAllData := true
	checkFunc := func(dataSource *device.ProcessDevice) (string, bool) {
		return "testValue", true
	}
	resultHandler := func(device *device.Device, result []string) error {
		if len(result) == 0 {
			return errors.New("no result")
		}
		return nil
	}

	// 注册字段处理器
	err := RegisterFieldHandlerForDevice(fieldName, getAllData, checkFunc, resultHandler)
	assert.NoError(t, err)

	// 测试获取已注册的字段处理器
	handler, exists := GetFieldHandlerForDevice[string](fieldName)
	assert.True(t, exists)
	assert.NotNil(t, handler)
	assert.Equal(t, getAllData, handler.GetAllData())

	// 测试获取未注册的字段处理器
	_, exists = GetFieldHandlerForDevice[string]("nonExistentField")
	assert.False(t, exists)
}

func TestDeviceFieldProcess(t *testing.T) {
	// 清理已注册的处理器,避免影响其他测试
	fieldHandlerMapForDevice = make(map[string]interface{})

	// 准备测试数据
	dev := &device.Device{
		Id:          "test_device",
		SourceIds:   []uint64{},
		NodeIds:     []uint64{},
		TaskDataIds: []string{},
		ProcessIds:  []string{},
	}

	// 1. 测试 getAllData=true 的场景
	err := RegisterFieldHandlerForDevice(
		"test_field",
		true,
		func(dataSource *device.ProcessDevice) (string, bool) {
			return "test_value", true
		},
		func(device *device.Device, result []string) error {
			device.Id = result[0]
			return nil
		},
	)
	assert.NoError(t, err)

	strategies := []*strategy.Strategy{}
	latestData := map[string]*device.ProcessDevice{
		"source1": {
			Id:     "process1",
			Source: 1,
			Node:   1,
		},
	}

	DeviceFieldProcess(dev, strategies, latestData)

	assert.Equal(t, "test_value", dev.Id)
	assert.Empty(t, dev.SourceIds)
	assert.Empty(t, dev.NodeIds)
	assert.Empty(t, dev.TaskDataIds)
	assert.Empty(t, dev.ProcessIds)

	// 清理处理器
	fieldHandlerMapForDevice = make(map[string]interface{})

	// 2. 测试 getAllData=false 的场景
	err = RegisterFieldHandlerForDevice(
		"test_field_2",
		false,
		func(dataSource *device.ProcessDevice) (string, bool) {
			return "test_value_2", true
		},
		func(device *device.Device, result []string) error {
			device.Id = result[0]
			return nil
		},
	)
	assert.NoError(t, err)

	strategies = []*strategy.Strategy{
		{
			FieldName:      "test_field_2",
			SourcePriority: strategy.SourcePriority{"1": uint64(1)},
		},
	}
	latestData = map[string]*device.ProcessDevice{
		"1": {
			Id:         "process1",
			Source:     1,
			Node:       1,
			TaskDataId: "task1",
		},
	}

	dev.Id = "test_device" // 重置数据
	dev.SourceIds = []uint64{}
	dev.NodeIds = []uint64{}
	dev.TaskDataIds = []string{}
	dev.ProcessIds = []string{}

	DeviceFieldProcess(dev, strategies, latestData)

	assert.Equal(t, "test_value_2", dev.Id)
	assert.Equal(t, []uint64{1}, dev.SourceIds)
	assert.Equal(t, []uint64{1}, dev.NodeIds)
	assert.Equal(t, []string{"task1"}, dev.TaskDataIds)
	assert.Equal(t, []string{"process1"}, dev.ProcessIds)

	// 清理处理器
	fieldHandlerMapForDevice = make(map[string]interface{})

	// 3. 测试处理器返回错误的场景
	err = RegisterFieldHandlerForDevice(
		"test_field_3",
		false,
		func(dataSource *device.ProcessDevice) (string, bool) {
			return "", false // 返回错误
		},
		func(device *device.Device, result []string) error {
			return nil
		},
	)
	assert.NoError(t, err)

	strategies = []*strategy.Strategy{
		{
			FieldName:      "test_field_3",
			SourcePriority: strategy.SourcePriority{"1": uint64(1)},
		},
	}

	dev.Id = "test_device" // 重置数据
	dev.SourceIds = []uint64{}
	dev.NodeIds = []uint64{}
	dev.TaskDataIds = []string{}
	dev.ProcessIds = []string{}

	DeviceFieldProcess(dev, strategies, latestData)

	assert.Equal(t, "test_device", dev.Id)
	assert.Empty(t, dev.SourceIds)
	assert.Empty(t, dev.NodeIds)
	assert.Empty(t, dev.TaskDataIds)
	assert.Empty(t, dev.ProcessIds)

	// 清理处理器
	fieldHandlerMapForDevice = make(map[string]interface{})

	// 4. 测试字段没有对应策略的场景
	err = RegisterFieldHandlerForDevice(
		"test_field_4",
		false,
		func(dataSource *device.ProcessDevice) (string, bool) {
			return "test_value_4", true
		},
		func(device *device.Device, result []string) error {
			device.Id = result[0]
			return nil
		},
	)
	assert.NoError(t, err)

	strategies = []*strategy.Strategy{} // 空策略

	dev.Id = "test_device" // 重置数据
	dev.SourceIds = []uint64{}
	dev.NodeIds = []uint64{}
	dev.TaskDataIds = []string{}
	dev.ProcessIds = []string{}

	DeviceFieldProcess(dev, strategies, latestData)

	assert.Equal(t, "test_device", dev.Id)
	assert.Empty(t, dev.SourceIds)
	assert.Empty(t, dev.NodeIds)
	assert.Empty(t, dev.TaskDataIds)
	assert.Empty(t, dev.ProcessIds)

	// 清理处理器
	fieldHandlerMapForDevice = make(map[string]interface{})

	// 5. 测试 panic 恢复
	err = RegisterFieldHandlerForDevice(
		"test_field_5",
		false,
		func(dataSource *device.ProcessDevice) (string, bool) {
			panic("test panic")
		},
		func(device *device.Device, result []string) error {
			return nil
		},
	)
	assert.NoError(t, err)

	strategies = []*strategy.Strategy{
		{
			FieldName:      "test_field_5",
			SourcePriority: strategy.SourcePriority{"1": uint64(1)},
		},
	}

	dev.Id = "test_device" // 重置数据
	dev.SourceIds = []uint64{}
	dev.NodeIds = []uint64{}
	dev.TaskDataIds = []string{}
	dev.ProcessIds = []string{}

	assert.NotPanics(t, func() {
		DeviceFieldProcess(dev, strategies, latestData)
	})

	assert.Equal(t, "test_device", dev.Id)
	assert.Empty(t, dev.SourceIds)
	assert.Empty(t, dev.NodeIds)
	assert.Empty(t, dev.TaskDataIds)
	assert.Empty(t, dev.ProcessIds)
}
