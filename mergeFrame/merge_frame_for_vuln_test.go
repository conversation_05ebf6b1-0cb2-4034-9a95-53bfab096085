package frame

import (
	"errors"
	"fobrain/models/elastic/poc"
	"fobrain/models/mysql/strategy"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestGetFieldValByStrategyForVuln(t *testing.T) {
	tests := []struct {
		name           string
		sourcePriority []uint64
		sourceList     map[uint64][]string
		allData        map[string]*poc.ProcessPoc
		wantValues     []string
		wantSourceIds  []uint64
		wantNodeIds    []uint64
		wantTaskIds    []string
		wantProcessIds []string
		wantErr        bool
	}{
		{
			name:           "空数据测试",
			sourcePriority: []uint64{},
			sourceList:     map[uint64][]string{},
			allData:        map[string]*poc.ProcessPoc{},
			wantValues:     []string{},
			wantSourceIds:  []uint64{},
			wantNodeIds:    []uint64{},
			wantTaskIds:    []string{},
			wantProcessIds: []string{},
			wantErr:        false,
		},
		{
			name:           "单一优先级数据获取",
			sourcePriority: []uint64{1},
			sourceList: map[uint64][]string{
				1: {"source1", "source2"},
			},
			allData: map[string]*poc.ProcessPoc{
				"source1": {
					Name:       "test1",
					Source:     1,
					Node:       1,
					TaskDataId: "task1",
					Id:         "process1",
				},
				"source2": {
					Name:       "test2",
					Source:     2,
					Node:       2,
					TaskDataId: "task2",
					Id:         "process2",
				},
			},
			wantValues:     []string{"test1", "test2"},
			wantSourceIds:  []uint64{1, 2},
			wantNodeIds:    []uint64{1, 2},
			wantTaskIds:    []string{"task1", "task2"},
			wantProcessIds: []string{"process1", "process2"},
			wantErr:        false,
		},
		{
			name:           "多优先级数据获取",
			sourcePriority: []uint64{1, 2},
			sourceList: map[uint64][]string{
				1: {"source1"},
				2: {"source2"},
			},
			allData: map[string]*poc.ProcessPoc{
				"source1": {
					Name:       "test1",
					Source:     1,
					Node:       1,
					TaskDataId: "task1",
					Id:         "process1",
				},
				"source2": {
					Name:       "test2",
					Source:     2,
					Node:       2,
					TaskDataId: "task2",
					Id:         "process2",
				},
			},
			wantValues:     []string{"test1"},
			wantSourceIds:  []uint64{1},
			wantNodeIds:    []uint64{1},
			wantTaskIds:    []string{"task1"},
			wantProcessIds: []string{"process1"},
			wantErr:        false,
		},
	}

	checkFunc := func(dataSource *poc.ProcessPoc) (string, bool) {
		if dataSource.Name == "" {
			return "", false
		}
		return dataSource.Name, true
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := getFieldValByStrategyForVuln[string](
				tt.sourcePriority,
				tt.sourceList,
				tt.allData,
				checkFunc,
			)

			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.ElementsMatch(t, tt.wantValues, result.Values)
				assert.ElementsMatch(t, tt.wantSourceIds, result.SourceIds)
				assert.ElementsMatch(t, tt.wantNodeIds, result.NodeIds)
				assert.ElementsMatch(t, tt.wantTaskIds, result.TaskDataIds)
				assert.ElementsMatch(t, tt.wantProcessIds, result.ProcessIds)
			}
		})
	}
}

func TestGetAllFieldValForVuln(t *testing.T) {
	tests := []struct {
		name           string
		allData        map[string]*poc.ProcessPoc
		wantValues     []string
		wantSourceIds  []uint64
		wantNodeIds    []uint64
		wantTaskIds    []string
		wantProcessIds []string
		wantErr        bool
	}{
		{
			name:           "空数据测试",
			allData:        map[string]*poc.ProcessPoc{},
			wantValues:     []string{},
			wantSourceIds:  []uint64{},
			wantNodeIds:    []uint64{},
			wantTaskIds:    []string{},
			wantProcessIds: []string{},
			wantErr:        false,
		},
		{
			name: "多数据源测试",
			allData: map[string]*poc.ProcessPoc{
				"source1": {
					Name:       "test1",
					Source:     1,
					Node:       1,
					TaskDataId: "task1",
					Id:         "process1",
				},
				"source2": {
					Name:       "test2",
					Source:     2,
					Node:       2,
					TaskDataId: "task2",
					Id:         "process2",
				},
			},
			wantValues:     []string{"test1", "test2"},
			wantSourceIds:  []uint64{1, 2},
			wantNodeIds:    []uint64{1, 2},
			wantTaskIds:    []string{"task1", "task2"},
			wantProcessIds: []string{"process1", "process2"},
			wantErr:        false,
		},
	}

	checkFunc := func(dataSource *poc.ProcessPoc) (string, bool) {
		if dataSource.Name == "" {
			return "", false
		}
		return dataSource.Name, true
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := getAllFieldValForVuln(
				tt.allData,
				checkFunc,
			)

			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.ElementsMatch(t, tt.wantValues, result.Values)
				assert.ElementsMatch(t, tt.wantSourceIds, result.SourceIds)
				assert.ElementsMatch(t, tt.wantNodeIds, result.NodeIds)
				assert.ElementsMatch(t, tt.wantTaskIds, result.TaskDataIds)
				assert.ElementsMatch(t, tt.wantProcessIds, result.ProcessIds)
			}
		})
	}
}

func TestRegisterFieldHandlerForVuln(t *testing.T) {
	// 清空 fieldHandlerMapForVuln 以确保测试的独立性
	fieldHandlerMapForVuln = make(map[string]interface{})

	fieldName := "testField"
	getAllData := true
	checkFunc := func(dataSource *poc.ProcessPoc) (string, bool) {
		return "testValue", true
	}
	resultHandler := func(poc *poc.Poc, result []string) error {
		if len(result) == 0 {
			return errors.New("no result")
		}
		return nil
	}

	// 注册字段处理器
	err := RegisterFieldHandlerForVuln(fieldName, getAllData, checkFunc, resultHandler)
	assert.NoError(t, err)

	// 尝试再次注册相同的字段处理器，应该返回错误
	err = RegisterFieldHandlerForVuln(fieldName, getAllData, checkFunc, resultHandler)
	assert.Error(t, err)
	assert.Equal(t, "field handler for testfield already exists", err.Error())

	// 验证字段处理器是否正确注册
	handler, exists := GetFieldHandlerForVuln[string](fieldName)
	assert.True(t, exists)
	assert.NotNil(t, handler)
	assert.Equal(t, getAllData, handler.GetAllData())
}

func TestVulnFieldProcess(t *testing.T) {
	// 清理已注册的处理器,避免影响其他测试
	fieldHandlerMapForVuln = make(map[string]interface{})

	// 准备测试数据
	vuln := &poc.Poc{
		Id:          "test_vuln",
		SourceIds:   []uint64{},
		NodeIds:     []uint64{},
		TaskDataIds: []string{},
		ProcessIds:  []string{},
	}

	// 1. 测试 getAllData=true 的场景
	err := RegisterFieldHandlerForVuln[string](
		"test_field",
		true,
		func(dataSource *poc.ProcessPoc) (string, bool) {
			return "test_value", true
		},
		func(poc *poc.Poc, result []string) error {
			poc.Id = result[0]
			return nil
		},
	)
	assert.NoError(t, err)

	strategies := []*strategy.Strategy{}
	latestData := map[string]*poc.ProcessPoc{
		"source1": {
			Id:     "process1",
			Source: 1,
			Node:   1,
		},
	}

	VulnFieldProcess(vuln, strategies, latestData)

	assert.Equal(t, "test_value", vuln.Id)
	assert.Empty(t, vuln.SourceIds)
	assert.Empty(t, vuln.NodeIds)
	assert.Empty(t, vuln.TaskDataIds)
	assert.Empty(t, vuln.ProcessIds)

	// 清理处理器
	fieldHandlerMapForVuln = make(map[string]interface{})

	// 2. 测试 getAllData=false 的场景
	err = RegisterFieldHandlerForVuln(
		"test_field_2",
		false,
		func(dataSource *poc.ProcessPoc) (string, bool) {
			return "test_value_2", true
		},
		func(poc *poc.Poc, result []string) error {
			poc.Id = result[0]
			return nil
		},
	)
	assert.NoError(t, err)

	strategies = []*strategy.Strategy{
		{
			FieldName:      "test_field_2",
			SourcePriority: strategy.SourcePriority{"1": uint64(1)},
		},
	}
	latestData = map[string]*poc.ProcessPoc{
		"1": {
			Id:         "process1",
			Source:     1,
			Node:       1,
			TaskDataId: "task1",
		},
	}

	vuln.Id = "test_vuln" // 重置数据
	vuln.SourceIds = []uint64{}
	vuln.NodeIds = []uint64{}
	vuln.TaskDataIds = []string{}
	vuln.ProcessIds = []string{}

	VulnFieldProcess(vuln, strategies, latestData)

	assert.Equal(t, "test_value_2", vuln.Id)
	assert.Equal(t, []uint64{1}, vuln.SourceIds)
	assert.Equal(t, []uint64{1}, vuln.NodeIds)
	assert.Equal(t, []string{"task1"}, vuln.TaskDataIds)
	assert.Equal(t, []string{"process1"}, vuln.ProcessIds)

	// 清理处理器
	fieldHandlerMapForVuln = make(map[string]interface{})

	// 3. 测试处理器返回错误的场景
	err = RegisterFieldHandlerForVuln(
		"test_field_3",
		false,
		func(dataSource *poc.ProcessPoc) (string, bool) {
			return "", false // 返回错误
		},
		func(poc *poc.Poc, result []string) error {
			return nil
		},
	)
	assert.NoError(t, err)

	strategies = []*strategy.Strategy{
		{
			FieldName:      "test_field_3",
			SourcePriority: strategy.SourcePriority{"1": uint64(1)},
		},
	}

	vuln.Id = "test_vuln" // 重置数据
	vuln.SourceIds = []uint64{}
	vuln.NodeIds = []uint64{}
	vuln.TaskDataIds = []string{}
	vuln.ProcessIds = []string{}

	VulnFieldProcess(vuln, strategies, latestData)

	assert.Equal(t, "test_vuln", vuln.Id)
	assert.Empty(t, vuln.SourceIds)
	assert.Empty(t, vuln.NodeIds)
	assert.Empty(t, vuln.TaskDataIds)
	assert.Empty(t, vuln.ProcessIds)

	// 清理处理器
	fieldHandlerMapForVuln = make(map[string]interface{})

	// 4. 测试字段没有对应策略的场景
	err = RegisterFieldHandlerForVuln(
		"test_field_4",
		false,
		func(dataSource *poc.ProcessPoc) (string, bool) {
			return "test_value_4", true
		},
		func(poc *poc.Poc, result []string) error {
			poc.Id = result[0]
			return nil
		},
	)
	assert.NoError(t, err)

	strategies = []*strategy.Strategy{} // 空策略

	vuln.Id = "test_vuln" // 重置数据
	vuln.SourceIds = []uint64{}
	vuln.NodeIds = []uint64{}
	vuln.TaskDataIds = []string{}
	vuln.ProcessIds = []string{}

	VulnFieldProcess(vuln, strategies, latestData)

	assert.Equal(t, "test_vuln", vuln.Id)
	assert.Empty(t, vuln.SourceIds)
	assert.Empty(t, vuln.NodeIds)
	assert.Empty(t, vuln.TaskDataIds)
	assert.Empty(t, vuln.ProcessIds)

	// 清理处理器
	fieldHandlerMapForVuln = make(map[string]interface{})

	// 5. 测试 panic 恢复
	err = RegisterFieldHandlerForVuln(
		"test_field_5",
		false,
		func(dataSource *poc.ProcessPoc) (string, bool) {
			panic("test panic")
		},
		func(poc *poc.Poc, result []string) error {
			return nil
		},
	)
	assert.NoError(t, err)

	strategies = []*strategy.Strategy{
		{
			FieldName:      "test_field_5",
			SourcePriority: strategy.SourcePriority{"1": uint64(1)},
		},
	}

	vuln.Id = "test_vuln" // 重置数据
	vuln.SourceIds = []uint64{}
	vuln.NodeIds = []uint64{}
	vuln.TaskDataIds = []string{}
	vuln.ProcessIds = []string{}

	assert.NotPanics(t, func() {
		VulnFieldProcess(vuln, strategies, latestData)
	})

	assert.Equal(t, "test_vuln", vuln.Id)
	assert.Empty(t, vuln.SourceIds)
	assert.Empty(t, vuln.NodeIds)
	assert.Empty(t, vuln.TaskDataIds)
	assert.Empty(t, vuln.ProcessIds)
}
