package frame

import (
	"errors"
	"fobrain/fobrain/common/localtime"
	logs "fobrain/mergeService/utils/log"
	es_model "fobrain/models/elastic"
	"fobrain/models/elastic/staff"
	dbmodel "fobrain/models/mysql/strategy"
	"fobrain/pkg/utils"
	"slices"
	"strings"
	"sync"
)

// FieldHandlerForPerson 字段处理器接口
type FieldHandlerForPerson[T any] interface {
	// GetAllData 返回是否需要获取所有数据源的数据
	// 返回true时会调用getAllFieldValForPerson获取所有数据
	// 返回false时会按照优先级策略获取数据
	GetAllData() bool

	// Getter 获取字段值
	// sourcePriority: 数据源优先级列表
	// sourceList: 每个优先级对应的数据源ID列表
	// allData: 所有数据源的数据
	// 返回值：
	// *FieldValue[T]: 字段值及其来源信息
	// error: 错误信息
	Getter(sourcePriority []uint64, sourceList map[uint64][]string, allData map[string]*staff.ProcessStaff) (*FieldValue[T], error)

	// ResultHandler 保存字段结果
	ResultHandler(person *staff.Staff, result []T) error
}

// GenericFieldHandlerForPerson 字段处理器实现
type GenericFieldHandlerForPerson[T any] struct {
	fieldName     string
	getAllData    bool
	checkFunc     func(dataSource *staff.ProcessStaff) (T, bool)
	resultHandler func(person *staff.Staff, result []T) error
}

var (
	fieldHandlerMapForPerson map[string]interface{}
	handlerMutexForPerson    sync.RWMutex
	onceForPerson            sync.Once
)

func init() {
	onceForPerson.Do(func() {
		fieldHandlerMapForPerson = make(map[string]interface{})
	})
}

// GetAllFieldHandlerForPerson 获取所有字段处理器
func GetAllFieldHandlerForPerson() map[string]interface{} {
	return fieldHandlerMapForPerson
}

// GetFieldHandlerForPerson 获取字段处理器
func GetFieldHandlerForPerson[T any](fieldName string) (FieldHandlerForPerson[T], bool) {
	fieldName = strings.ToLower(fieldName)
	fieldHandler, ok := fieldHandlerMapForPerson[fieldName]
	if !ok {
		return nil, false
	}
	handler, ok := fieldHandler.(FieldHandlerForPerson[T])
	return handler, ok
}

// RegisterFieldHandlerForPerson 注册字段处理器
func RegisterFieldHandlerForPerson[T any](fieldName string, getAllData bool, checkFunc func(dataSource *staff.ProcessStaff) (T, bool), resultHandler func(person *staff.Staff, result []T) error) error {
	handlerMutexForPerson.Lock()
	defer handlerMutexForPerson.Unlock()
	if fieldName == "" {
		return errors.New("field name cannot be empty")
	}
	if checkFunc == nil {
		return errors.New("checkFunc cannot be nil")
	}
	if resultHandler == nil {
		return errors.New("resultHandler cannot be nil")
	}
	fieldName = strings.ToLower(fieldName)
	if _, ok := GetFieldHandlerForPerson[T](fieldName); ok {
		return errors.New("field handler for " + fieldName + " already exists")
	}
	fieldHandlerMapForPerson[fieldName] = &GenericFieldHandlerForPerson[T]{
		fieldName:     fieldName,
		getAllData:    getAllData,
		checkFunc:     checkFunc,
		resultHandler: resultHandler,
	}
	return nil
}

func (h *GenericFieldHandlerForPerson[T]) Getter(sourcePriority []uint64, sourceList map[uint64][]string, allData map[string]*staff.ProcessStaff) (*FieldValue[T], error) {
	if h.getAllData {
		return getAllFieldValForPerson[T](allData, h.checkFunc)
	}
	return getFieldValByStrategyForPerson[T](sourcePriority, sourceList, allData, h.checkFunc)
}

func (h *GenericFieldHandlerForPerson[T]) ResultHandler(person *staff.Staff, result []T) error {
	return h.resultHandler(person, result)
}

func (h *GenericFieldHandlerForPerson[T]) GetAllData() bool {
	return h.getAllData
}

// getFieldValByStrategyForPerson 根据指定的策略获取字段值
// sourcePriority 优先级顺序
// sourceList 源节点列表,key为优先级,value为源节点id列表
// allData 所有数据,key为源节点id,value为源节点数据
// checkFunc 字段值获取函数，返回值为字段值和是否获取成功
// 返回值为字段值列表、源节点id列表、节点id列表、任务数据id列表、处理id列表、错误信息
func getFieldValByStrategyForPerson[T any](sourcePriority []uint64, sourceList map[uint64][]string, allData map[string]*staff.ProcessStaff, checkFunc func(dataSource *staff.ProcessStaff) (T, bool)) (*FieldValue[T], error) {
	result := make([]T, 0)
	sourceIds := make([]uint64, 0)
	nodeIds := make([]uint64, 0)
	taskDataIds := make([]string, 0)
	processIds := make([]string, 0)

	// 按照策略规则进行融合
	// 遍历优先级顺序
	for _, p := range sourcePriority {
		// 当前优先级是否存在非空值的标识
		hasNonEmptyValue := false
		// 遍历每个源节点的最新数据，按照策略规则进行融合
		for id, sd := range allData {
			// 如果当前源在当前优先级中,就可以计算当前源的值
			if slices.Contains(sourceList[p], id) {
				// 计算当前源的值
				r, ok := checkFunc(sd)
				if ok {
					result = append(result, r)
					hasNonEmptyValue = true
					sourceIds = append(sourceIds, sd.GetSource())
					nodeIds = append(nodeIds, sd.GetNode())
					taskDataIds = append(taskDataIds, sd.GetTaskDataId())
					processIds = append(processIds, sd.GetId())
				}
				// 继续寻找下一个符合条件的源
				continue
			}
		}
		// 如果当前优先级存在非空值，则跳出优先级循环
		if hasNonEmptyValue {
			break
		}
	}
	return &FieldValue[T]{
		Values: result,
		FieldValInfo: es_model.FieldValInfo{
			SourceIds:   sourceIds,
			NodeIds:     nodeIds,
			TaskDataIds: taskDataIds,
			ProcessIds:  processIds,
		},
	}, nil
}

// getAllFieldValForPerson 获取所有源提供的字段值
// allData 所有数据,key为源节点id,value为源节点数据
// fieldName 字段名,区分大小写
// checkFunc 字段值获取函数，返回值为字段值和是否获取成功
func getAllFieldValForPerson[T any](allData map[string]*staff.ProcessStaff, checkFunc func(dataSource *staff.ProcessStaff) (T, bool)) (*FieldValue[T], error) {
	result := make([]T, 0)
	sourceIds := make([]uint64, 0)
	nodeIds := make([]uint64, 0)
	taskDataIds := make([]string, 0)
	processIds := make([]string, 0)

	// 遍历所有数据
	for _, sd := range allData {
		// 计算当前源的值
		r, ok := checkFunc(sd)
		if ok {
			result = append(result, r)
			sourceIds = append(sourceIds, sd.GetSource())
			nodeIds = append(nodeIds, sd.GetNode())
			taskDataIds = append(taskDataIds, sd.GetTaskDataId())
			processIds = append(processIds, sd.GetId())
		}
	}

	return &FieldValue[T]{
		Values: result,
		FieldValInfo: es_model.FieldValInfo{
			SourceIds:   sourceIds,
			NodeIds:     nodeIds,
			TaskDataIds: taskDataIds,
			ProcessIds:  processIds,
		},
	}, nil
}

// PersonFieldProcess 人员融合字段处理
// person 人员
// strategies 融合策略
// latestData 最新数据
func PersonFieldProcess(person *staff.Staff, strategies []*dbmodel.Strategy, latestData map[string]*staff.ProcessStaff) []*es_model.FieldValInfo {
	// 预处理融合策略
	strategyMap := PreProcessStrategy(strategies)
	// 获取所有字段及对应的处理器
	fieldHandlerMap := GetAllFieldHandlerForPerson()
	// 字段采信信息
	fieldValInfoList := make([]*es_model.FieldValInfo, 0)
	// 遍历字段
	for fieldName, fieldHandler := range fieldHandlerMap {
		logs.GetLogger("person").Debugf("开始处理字段. fieldName: %s", fieldName)
		func(fieldName string, fieldHandler interface{}) {
			defer func() {
				if r := recover(); r != nil {
					logs.GetLogger("person").Errorf("处理字段失败. fieldName: %s, err: %v", fieldName, r)
				}
				logs.GetLogger("person").Debugf("结束处理字段. fieldName: %s", fieldName)
			}()
			fieldValInfo := &es_model.FieldValInfo{}
			switch fieldHandler.(type) {
			case FieldHandlerForPerson[string]:
				fh := fieldHandler.(FieldHandlerForPerson[string])
				fieldValInfo = processFieldHandlerPerson[string](fh, fieldName, person, strategyMap, latestData)
			case FieldHandlerForPerson[map[string]string]:
				fh := fieldHandler.(FieldHandlerForPerson[map[string]string])
				fieldValInfo = processFieldHandlerPerson[map[string]string](fh, fieldName, person, strategyMap, latestData)
			case FieldHandlerForPerson[[]string]:
				fh := fieldHandler.(FieldHandlerForPerson[[]string])
				fieldValInfo = processFieldHandlerPerson[[]string](fh, fieldName, person, strategyMap, latestData)
			case FieldHandlerForPerson[int]:
				fh := fieldHandler.(FieldHandlerForPerson[int])
				fieldValInfo = processFieldHandlerPerson[int](fh, fieldName, person, strategyMap, latestData)
			case FieldHandlerForPerson[[]int]:
				fh := fieldHandler.(FieldHandlerForPerson[[]int])
				fieldValInfo = processFieldHandlerPerson[[]int](fh, fieldName, person, strategyMap, latestData)
			case FieldHandlerForPerson[int64]:
				fh := fieldHandler.(FieldHandlerForPerson[int64])
				fieldValInfo = processFieldHandlerPerson[int64](fh, fieldName, person, strategyMap, latestData)
			case FieldHandlerForPerson[uint64]:
				fh := fieldHandler.(FieldHandlerForPerson[uint64])
				fieldValInfo = processFieldHandlerPerson[uint64](fh, fieldName, person, strategyMap, latestData)
			case FieldHandlerForPerson[[]uint64]:
				fh := fieldHandler.(FieldHandlerForPerson[[]uint64])
				fieldValInfo = processFieldHandlerPerson[[]uint64](fh, fieldName, person, strategyMap, latestData)
			case FieldHandlerForPerson[*localtime.Time]:
				fh := fieldHandler.(FieldHandlerForPerson[*localtime.Time])
				fieldValInfo = processFieldHandlerPerson[*localtime.Time](fh, fieldName, person, strategyMap, latestData)
			default:
				logs.GetLogger("person").Errorf("未知的字段处理器类型. fieldName: %s", fieldName)
			}
			if fieldValInfo != nil {
				fieldValInfo.FieldName = fieldName
				fieldValInfoList = append(fieldValInfoList, fieldValInfo)
			}
		}(fieldName, fieldHandler)
	}

	person.SourceIds = utils.ListDistinctNonZero(person.SourceIds)
	person.NodeIds = utils.ListDistinctNonZero(person.NodeIds)
	person.TaskDataIds = utils.ListDistinctNonZero(person.TaskDataIds)
	person.ProcessIds = utils.ListDistinctNonZero(person.ProcessIds)
	return fieldValInfoList
}

func processFieldHandlerPerson[T any](fh FieldHandlerForPerson[T], fieldName string, person *staff.Staff, strategyMap map[string]*dbmodel.Strategy, latestData map[string]*staff.ProcessStaff) *es_model.FieldValInfo {
	if fh.GetAllData() {
		// 处理字段
		fieldVal, err := fh.Getter(nil, nil, latestData)
		if err != nil {
			logs.GetLogger("person").Errorf("计算字段值失败. fieldName: %s, err: %v", fieldName, err)
			return nil
		}
		// 保存字段结果
		err = fh.ResultHandler(person, fieldVal.Values)
		if err != nil {
			logs.GetLogger("person").Errorf("保存字段结果失败. fieldName: %s, err: %v", fieldName, err)
			return nil
		}
		// 获取所有源的字段值，不再保存采信的sourceIds、nodeIds、taskDataIds、processIds
		return &fieldVal.FieldValInfo
	} else {
		if strategy, ok := strategyMap[fieldName]; ok {
			sourcePriority, sourceList := GetSourceByPriority(strategy)
			fieldVal, err := fh.Getter(sourcePriority, sourceList, latestData)
			if err != nil {
				logs.GetLogger("person").Errorf("计算字段值失败. fieldName: %s, err: %v", fieldName, err)
				return nil
			}
			// 保存字段结果
			err = fh.ResultHandler(person, fieldVal.Values)
			if err != nil {
				logs.GetLogger("person").Errorf("保存字段结果失败. fieldName: %s, err: %v", fieldName, err)
				return nil
			}
			person.SourceIds = append(person.SourceIds, fieldVal.SourceIds...)
			person.NodeIds = append(person.NodeIds, fieldVal.NodeIds...)
			person.TaskDataIds = append(person.TaskDataIds, fieldVal.TaskDataIds...)
			person.ProcessIds = append(person.ProcessIds, fieldVal.ProcessIds...)
			return &fieldVal.FieldValInfo
		}
	}
	return nil
}
