package frame

import (
	"fobrain/models/elastic/assets"
	"fobrain/models/mysql/strategy"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestRegisterAndGetFieldHandler(t *testing.T) {
	// 清理测试环境
	fieldHandlerMapForAsset = make(map[string]interface{})

	tests := []struct {
		name       string
		fieldName  string
		wantErr    bool
		errMessage string
	}{
		{
			name:      "注册新的处理器",
			fieldName: "test_field",
			wantErr:   false,
		},
		{
			name:       "重复注册处理器",
			fieldName:  "test_field",
			wantErr:    true,
			errMessage: "field handler for test_field already exists",
		},
	}

	checkFunc := func(dataSource *assets.ProcessAssets) (string, bool) {
		return "test", true
	}
	resultHandler := func(asset *assets.Assets, result []string) error {
		return nil
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := RegisterFieldHandlerForAsset[string](tt.fieldName, false, checkFunc, resultHandler)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Equal(t, tt.errMessage, err.Error())
			} else {
				assert.NoError(t, err)
				// 验证是否成功注册
				handler, exists := GetFieldHandlerForAsset[string](tt.fieldName)
				assert.True(t, exists)
				assert.NotNil(t, handler)
			}
		})
	}
}

func TestGetFieldValByStrategyForAsset(t *testing.T) {
	tests := []struct {
		name           string
		sourcePriority []uint64
		sourceList     map[uint64][]string
		allData        map[string]*assets.ProcessAssets
		wantValues     []string
		wantSourceIds  []uint64
		wantNodeIds    []uint64
		wantTaskIds    []string
		wantProcessIds []string
		wantErr        bool
	}{
		{
			name:           "空数据测试",
			sourcePriority: []uint64{},
			sourceList:     map[uint64][]string{},
			allData:        map[string]*assets.ProcessAssets{},
			wantValues:     []string{},
			wantSourceIds:  []uint64{},
			wantNodeIds:    []uint64{},
			wantTaskIds:    []string{},
			wantProcessIds: []string{},
			wantErr:        false,
		},
		{
			name:           "单一优先级数据获取",
			sourcePriority: []uint64{1},
			sourceList: map[uint64][]string{
				1: {"source1", "source2"},
			},
			allData: map[string]*assets.ProcessAssets{
				"source1": {
					HostName:   "test1",
					Source:     1,
					Node:       1,
					TaskDataId: "task1",
					Id:         "process1",
				},
				"source2": {
					HostName:   "test2",
					Source:     2,
					Node:       2,
					TaskDataId: "task2",
					Id:         "process2",
				},
			},
			wantValues:     []string{"test1", "test2"},
			wantSourceIds:  []uint64{1, 2},
			wantNodeIds:    []uint64{1, 2},
			wantTaskIds:    []string{"task1", "task2"},
			wantProcessIds: []string{"process1", "process2"},
			wantErr:        false,
		},
		{
			name:           "多优先级数据获取",
			sourcePriority: []uint64{1, 2},
			sourceList: map[uint64][]string{
				1: {"source1"},
				2: {"source2"},
			},
			allData: map[string]*assets.ProcessAssets{
				"source1": {
					HostName:   "test1",
					Source:     1,
					Node:       1,
					TaskDataId: "task1",
					Id:         "process1",
				},
				"source2": {
					HostName:   "test2",
					Source:     2,
					Node:       2,
					TaskDataId: "task2",
					Id:         "process2",
				},
			},
			wantValues:     []string{"test1"},
			wantSourceIds:  []uint64{1},
			wantNodeIds:    []uint64{1},
			wantTaskIds:    []string{"task1"},
			wantProcessIds: []string{"process1"},
			wantErr:        false,
		},
		{
			name:           "数据源不存在测试",
			sourcePriority: []uint64{1},
			sourceList: map[uint64][]string{
				1: {"non_existent_source"},
			},
			allData: map[string]*assets.ProcessAssets{
				"source1": {
					HostName:   "test1",
					Source:     1,
					Node:       1,
					TaskDataId: "task1",
					Id:         "process1",
				},
			},
			wantValues:     []string{},
			wantSourceIds:  []uint64{},
			wantNodeIds:    []uint64{},
			wantTaskIds:    []string{},
			wantProcessIds: []string{},
			wantErr:        false,
		},
		{
			name:           "空值过滤测试",
			sourcePriority: []uint64{1},
			sourceList: map[uint64][]string{
				1: {"source1", "source2"},
			},
			allData: map[string]*assets.ProcessAssets{
				"source1": {
					HostName:   "",
					Source:     1,
					Node:       1,
					TaskDataId: "task1",
					Id:         "process1",
				},
				"source2": {
					HostName:   "test2",
					Source:     2,
					Node:       2,
					TaskDataId: "task2",
					Id:         "process2",
				},
			},
			wantValues:     []string{"test2"},
			wantSourceIds:  []uint64{2},
			wantNodeIds:    []uint64{2},
			wantTaskIds:    []string{"task2"},
			wantProcessIds: []string{"process2"},
			wantErr:        false,
		},
	}

	checkFunc := func(dataSource *assets.ProcessAssets) (string, bool) {
		if dataSource.HostName == "" {
			return "", false
		}
		return dataSource.HostName, true
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := getFieldValByStrategyForAsset(
				tt.sourcePriority,
				tt.sourceList,
				tt.allData,
				checkFunc,
			)

			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.ElementsMatch(t, tt.wantValues, result.Values)
				assert.ElementsMatch(t, tt.wantSourceIds, result.SourceIds)
				assert.ElementsMatch(t, tt.wantNodeIds, result.NodeIds)
				assert.ElementsMatch(t, tt.wantTaskIds, result.TaskDataIds)
				assert.ElementsMatch(t, tt.wantProcessIds, result.ProcessIds)
			}
		})
	}
}

func TestGetFieldHandlerForAsset(t *testing.T) {
	// 清理已注册的处理器
	fieldHandlerMapForAsset = make(map[string]interface{})

	// 注册一个测试用的处理器
	checkFunc := func(dataSource *assets.ProcessAssets) (string, bool) {
		return "test", true
	}
	resultHandler := func(asset *assets.Assets, result []string) error {
		return nil
	}
	err := RegisterFieldHandlerForAsset[string]("test_field", false, checkFunc, resultHandler)
	assert.NoError(t, err)

	tests := []struct {
		name      string
		fieldName string
		wantOk    bool
	}{
		{
			name:      "获取已注册的处理器",
			fieldName: "test_field",
			wantOk:    true,
		},
		{
			name:      "获取不存在的处理器",
			fieldName: "non_existent_field",
			wantOk:    false,
		},
		{
			name:      "字段名大小写不敏感测试",
			fieldName: "TEST_FIELD",
			wantOk:    true,
		},
		{
			name:      "字段名大小写混合测试",
			fieldName: "TeSt_FiElD",
			wantOk:    true,
		},
		{
			name:      "空字段名测试",
			fieldName: "",
			wantOk:    false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			handler, ok := GetFieldHandlerForAsset[string](tt.fieldName)
			assert.Equal(t, tt.wantOk, ok)
			if tt.wantOk {
				assert.NotNil(t, handler)
			} else {
				assert.Nil(t, handler)
			}
		})
	}
}

func TestGetAllFieldValForAsset(t *testing.T) {
	tests := []struct {
		name           string
		allData        map[string]*assets.ProcessAssets
		wantValues     []string
		wantSourceIds  []uint64
		wantNodeIds    []uint64
		wantTaskIds    []string
		wantProcessIds []string
		wantErr        bool
	}{
		{
			name:           "空数据测试",
			allData:        map[string]*assets.ProcessAssets{},
			wantValues:     []string{},
			wantSourceIds:  []uint64{},
			wantNodeIds:    []uint64{},
			wantTaskIds:    []string{},
			wantProcessIds: []string{},
			wantErr:        false,
		},
		{
			name: "多数据源测试",
			allData: map[string]*assets.ProcessAssets{
				"source1": {
					HostName:   "test1",
					Source:     1,
					Node:       1,
					TaskDataId: "task1",
					Id:         "process1",
				},
				"source2": {
					HostName:   "test2",
					Source:     2,
					Node:       2,
					TaskDataId: "task2",
					Id:         "process2",
				},
			},
			wantValues:     []string{"test1", "test2"},
			wantSourceIds:  []uint64{1, 2},
			wantNodeIds:    []uint64{1, 2},
			wantTaskIds:    []string{"task1", "task2"},
			wantProcessIds: []string{"process1", "process2"},
			wantErr:        false,
		},
		{
			name: "数据过滤测试",
			allData: map[string]*assets.ProcessAssets{
				"source1": {
					HostName:   "",
					Source:     1,
					Node:       1,
					TaskDataId: "task1",
					Id:         "process1",
				},
				"source2": {
					HostName:   "test2",
					Source:     2,
					Node:       2,
					TaskDataId: "task2",
					Id:         "process2",
				},
			},
			wantValues:     []string{"test2"},
			wantSourceIds:  []uint64{2},
			wantNodeIds:    []uint64{2},
			wantTaskIds:    []string{"task2"},
			wantProcessIds: []string{"process2"},
			wantErr:        false,
		},
		{
			name: "重复数据测试",
			allData: map[string]*assets.ProcessAssets{
				"source1": {
					HostName:   "test1",
					Source:     1,
					Node:       1,
					TaskDataId: "task1",
					Id:         "process1",
				},
				"source2": {
					HostName:   "test1",
					Source:     1,
					Node:       1,
					TaskDataId: "task1",
					Id:         "process1",
				},
			},
			wantValues:     []string{"test1", "test1"},
			wantSourceIds:  []uint64{1, 1},
			wantNodeIds:    []uint64{1, 1},
			wantTaskIds:    []string{"task1", "task1"},
			wantProcessIds: []string{"process1", "process1"},
			wantErr:        false,
		},
	}

	checkFunc := func(dataSource *assets.ProcessAssets) (string, bool) {
		if dataSource.HostName == "" {
			return "", false
		}
		return dataSource.HostName, true
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := getAllFieldValForAsset(
				tt.allData,
				checkFunc,
			)

			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.ElementsMatch(t, tt.wantValues, result.Values)
				assert.ElementsMatch(t, tt.wantSourceIds, result.SourceIds)
				assert.ElementsMatch(t, tt.wantNodeIds, result.NodeIds)
				assert.ElementsMatch(t, tt.wantTaskIds, result.TaskDataIds)
				assert.ElementsMatch(t, tt.wantProcessIds, result.ProcessIds)
			}
		})
	}
}

func TestAssetFieldProcess(t *testing.T) {
	// 清理已注册的处理器
	fieldHandlerMapForAsset = make(map[string]interface{})

	// 注册测试用的字段处理器
	err := RegisterFieldHandlerForAsset[string]("all_data_field", true,
		func(dataSource *assets.ProcessAssets) (string, bool) {
			return dataSource.HostName, true
		},
		func(asset *assets.Assets, result []string) error {
			hostname := result[0]
			asset.HostName = []string{
				hostname,
			}
			return nil
		})
	assert.NoError(t, err)

	err = RegisterFieldHandlerForAsset[string]("hostname", false,
		func(dataSource *assets.ProcessAssets) (string, bool) {
			return dataSource.HostName, true
		},
		func(asset *assets.Assets, result []string) error {
			hostname := result[0]
			asset.HostName = []string{
				hostname,
			}
			return nil
		})
	assert.NoError(t, err)

	tests := []struct {
		name       string
		asset      *assets.Assets
		strategies []*strategy.Strategy
		latestData map[string]*assets.ProcessAssets
		want       *assets.Assets
	}{
		{
			name:       "空策略测试",
			asset:      &assets.Assets{},
			strategies: []*strategy.Strategy{},
			latestData: map[string]*assets.ProcessAssets{
				"source1": {
					Source:     1,
					Node:       1,
					TaskDataId: "task1",
					Id:         "process1",
					HostName:   "test_value1",
				},
			},
			want: &assets.Assets{
				HostName: []string{"test_value1"},
			},
		},
		{
			name:  "完整处理流程测试",
			asset: &assets.Assets{},
			strategies: []*strategy.Strategy{
				{
					FieldName: "hostname",
					SourcePriority: map[string]uint64{
						"source1": 1,
						"source2": 2,
					},
				},
			},
			latestData: map[string]*assets.ProcessAssets{
				"source1": {
					Source:     1,
					Node:       1,
					TaskDataId: "task1",
					Id:         "process1",
					HostName:   "test_value1",
				},
				"source2": {
					Source:     2,
					Node:       2,
					TaskDataId: "task2",
					Id:         "process2",
					HostName:   "test_value2",
				},
			},
			want: &assets.Assets{
				HostName:    []string{"test_value1"},
				SourceIds:   []uint64{1},
				NodeIds:     []uint64{1},
				TaskDataIds: []string{"task1"},
				ProcessIds:  []string{"process1"},
			},
		},
		{
			name:  "重复数据去重测试",
			asset: &assets.Assets{},
			strategies: []*strategy.Strategy{
				{
					FieldName: "hostname",
					SourcePriority: map[string]uint64{
						"source1": 1,
					},
				},
			},
			latestData: map[string]*assets.ProcessAssets{
				"source1": {
					Source:     1,
					Node:       1,
					TaskDataId: "task1",
					Id:         "process1",
					HostName:   "test_value1",
				},
				"source2": {
					Source:     1,
					Node:       1,
					TaskDataId: "task1",
					Id:         "process1",
					HostName:   "test_value1",
				},
			},
			want: &assets.Assets{
				HostName:    []string{"test_value1"},
				SourceIds:   []uint64{1},
				NodeIds:     []uint64{1},
				TaskDataIds: []string{"task1"},
				ProcessIds:  []string{"process1"},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			AssetFieldProcess(tt.asset, tt.strategies, tt.latestData)
			assert.Equal(t, tt.want.HostName, tt.asset.HostName)
			assert.ElementsMatch(t, tt.want.SourceIds, tt.asset.SourceIds)
			assert.ElementsMatch(t, tt.want.NodeIds, tt.asset.NodeIds)
			assert.ElementsMatch(t, tt.want.TaskDataIds, tt.asset.TaskDataIds)
			assert.ElementsMatch(t, tt.want.ProcessIds, tt.asset.ProcessIds)
		})
	}
}
