package frame

import (
	"errors"
	logs "fobrain/mergeService/utils/log"
	es_model "fobrain/models/elastic"
	"fobrain/models/elastic/poc"
	dbmodel "fobrain/models/mysql/strategy"
	"fobrain/pkg/utils"
	"slices"
	"strings"
	"sync"
)

// FieldHandlerForVuln 字段处理器接口
type FieldHandlerForVuln[T any] interface {
	// GetAllData 返回是否需要获取所有数据源的数据
	// 返回true时会调用getAllFieldValForVuln获取所有数据
	// 返回false时会按照优先级策略获取数据
	GetAllData() bool

	// Getter 获取字段值
	// sourcePriority: 数据源优先级列表
	// sourceList: 每个优先级对应的数据源ID列表
	// allData: 所有数据源的数据
	// 返回值：
	// *FieldValue[T]: 字段值及其来源信息
	// error: 错误信息
	Getter(sourcePriority []uint64, sourceList map[uint64][]string, allData map[string]*poc.ProcessPoc) (*FieldValue[T], error)

	// ResultHandler 保存字段结果
	ResultHandler(poc *poc.Poc, result []T) error
}

// GenericFieldHandlerForVuln 字段处理器实现
type GenericFieldHandlerForVuln[T any] struct {
	fieldName     string
	getAllData    bool
	checkFunc     func(dataSource *poc.ProcessPoc) (T, bool)
	resultHandler func(poc *poc.Poc, result []T) error
}

var (
	fieldHandlerMapForVuln map[string]interface{}
	handlerMutexForVuln    sync.RWMutex
	onceForVuln            sync.Once
)

func init() {
	onceForVuln.Do(func() {
		fieldHandlerMapForVuln = make(map[string]interface{})
	})
}

// GetAllFieldHandlerForVuln 获取所有字段处理器
func GetAllFieldHandlerForVuln() map[string]interface{} {
	return fieldHandlerMapForVuln
}

// GetFieldHandlerForVuln 获取字段处理器
func GetFieldHandlerForVuln[T any](fieldName string) (FieldHandlerForVuln[T], bool) {
	fieldName = strings.ToLower(fieldName)
	if handler, ok := fieldHandlerMapForVuln[fieldName]; ok {
		if typedHandler, ok := handler.(FieldHandlerForVuln[T]); ok {
			return typedHandler, true
		}
	}
	return nil, false
}

// RegisterFieldHandlerForVuln 注册字段处理器
func RegisterFieldHandlerForVuln[T any](fieldName string, getAllData bool, checkFunc func(dataSource *poc.ProcessPoc) (T, bool), resultHandler func(poc *poc.Poc, result []T) error) error {
	handlerMutexForVuln.Lock()
	defer handlerMutexForVuln.Unlock()
	if fieldName == "" {
		return errors.New("field name cannot be empty")
	}
	if checkFunc == nil {
		return errors.New("checkFunc cannot be nil")
	}
	if resultHandler == nil {
		return errors.New("resultHandler cannot be nil")
	}
	fieldName = strings.ToLower(fieldName)
	if _, ok := GetFieldHandlerForVuln[T](fieldName); ok {
		return errors.New("field handler for " + fieldName + " already exists")
	}
	fieldHandlerMapForVuln[fieldName] = &GenericFieldHandlerForVuln[T]{
		fieldName:     fieldName,
		getAllData:    getAllData,
		checkFunc:     checkFunc,
		resultHandler: resultHandler,
	}
	return nil
}

func (h *GenericFieldHandlerForVuln[T]) Getter(sourcePriority []uint64, sourceList map[uint64][]string, allData map[string]*poc.ProcessPoc) (*FieldValue[T], error) {
	if h.getAllData {
		return getAllFieldValForVuln[T](allData, h.checkFunc)
	}
	return getFieldValByStrategyForVuln[T](sourcePriority, sourceList, allData, h.checkFunc)
}

func (h *GenericFieldHandlerForVuln[T]) ResultHandler(poc *poc.Poc, result []T) error {
	return h.resultHandler(poc, result)
}

func (h *GenericFieldHandlerForVuln[T]) GetAllData() bool {
	return h.getAllData
}

// getFieldValByStrategyForVuln 根据指定的策略获取字段值
// sourcePriority 优先级顺序
// sourceList 源节点列表,key为优先级,value为源节点id列表
// allData 所有数据,key为源节点id,value为源节点数据
// checkFunc 字段值获取函数，返回值为字段值和是否获取成功
// 返回值为字段值列表、源节点id列表、节点id列表、任务数据id列表、处理id列表、错误信息
func getFieldValByStrategyForVuln[T any](sourcePriority []uint64, sourceList map[uint64][]string, allData map[string]*poc.ProcessPoc, checkFunc func(dataSource *poc.ProcessPoc) (T, bool)) (*FieldValue[T], error) {
	result := make([]T, 0)
	sourceIds := make([]uint64, 0)
	nodeIds := make([]uint64, 0)
	taskDataIds := make([]string, 0)
	processIds := make([]string, 0)

	// 按照策略规则进行融合
	// 遍历优先级顺序
	for _, p := range sourcePriority {
		// 当前优先级是否存在非空值的标识
		hasNonEmptyValue := false
		// 遍历每个源节点的最新数据，按照策略规则进行融合
		for id, sd := range allData {
			// 如果当前源在当前优先级中,就可以计算当前源的值
			if slices.Contains(sourceList[p], id) {
				// 计算当前源的值
				r, ok := checkFunc(sd)
				if ok {
					result = append(result, r)
					hasNonEmptyValue = true
					sourceIds = append(sourceIds, sd.GetSource())
					nodeIds = append(nodeIds, sd.GetNode())
					taskDataIds = append(taskDataIds, sd.GetTaskDataId())
					processIds = append(processIds, sd.GetId())
				}
				// 继续寻找下一个符合条件的源
				continue
			}
		}
		// 如果当前优先级存在非空值，则跳出优先级循环
		if hasNonEmptyValue {
			break
		}
	}
	return &FieldValue[T]{
		Values: result,
		FieldValInfo: es_model.FieldValInfo{
			SourceIds:   sourceIds,
			NodeIds:     nodeIds,
			TaskDataIds: taskDataIds,
			ProcessIds:  processIds,
		},
	}, nil
}

// getAllFieldValForVuln 获取所有源提供的字段值
// allData 所有数据,key为源节点id,value为源节点数据
// fieldName 字段名,区分大小写
// checkFunc 字段值获取函数，返回值为字段值和是否获取成功
func getAllFieldValForVuln[T any](allData map[string]*poc.ProcessPoc, checkFunc func(dataSource *poc.ProcessPoc) (T, bool)) (*FieldValue[T], error) {
	var result []T
	var sourceIds []uint64
	var nodeIds []uint64
	var taskDataIds []string
	var processIds []string

	for _, data := range allData {
		if val, ok := checkFunc(data); ok {
			result = append(result, val)
			sourceIds = append(sourceIds, data.GetSource())
			nodeIds = append(nodeIds, data.GetNode())
			taskDataIds = append(taskDataIds, data.GetTaskDataId())
			processIds = append(processIds, data.GetId())
		}
	}
	return &FieldValue[T]{
		Values: result,
		FieldValInfo: es_model.FieldValInfo{
			SourceIds:   sourceIds,
			NodeIds:     nodeIds,
			TaskDataIds: taskDataIds,
			ProcessIds:  processIds,
		},
	}, nil
}

// VulnFieldProcess 漏洞融合字段处理
// vuln 漏洞
// strategies 融合策略
// latestData 最新数据
func VulnFieldProcess(vuln *poc.Poc, strategies []*dbmodel.Strategy, latestData map[string]*poc.ProcessPoc) []*es_model.FieldValInfo {
	// 预处理融合策略
	strategyMap := PreProcessStrategy(strategies)
	// 获取所有字段及对应的处理器
	fieldHandlerMap := GetAllFieldHandlerForVuln()
	// 字段采信信息
	fieldValInfoList := make([]*es_model.FieldValInfo, 0)
	// 遍历字段
	for fieldName, fieldHandler := range fieldHandlerMap {
		logs.GetLogger("vuln").Debugf("开始处理字段. fieldName: %s", fieldName)
		func(fieldName string, fieldHandler interface{}) {
			defer func() {
				if r := recover(); r != nil {
					logs.GetLogger("vuln").Errorf("处理字段失败. fieldName: %s, err: %v", fieldName, r)
				}
				logs.GetLogger("vuln").Debugf("结束处理字段. fieldName: %s", fieldName)
			}()
			fieldValInfo := &es_model.FieldValInfo{}
			switch fh := fieldHandler.(type) {
			case FieldHandlerForVuln[string]:
				fieldValInfo = processFieldHandlerVuln[string](fh, fieldName, vuln, strategyMap, latestData)
			case FieldHandlerForVuln[[]string]:
				fieldValInfo = processFieldHandlerVuln[[]string](fh, fieldName, vuln, strategyMap, latestData)
			case FieldHandlerForVuln[int]:
				fieldValInfo = processFieldHandlerVuln[int](fh, fieldName, vuln, strategyMap, latestData)
			case FieldHandlerForVuln[[]int]:
				fieldValInfo = processFieldHandlerVuln[[]int](fh, fieldName, vuln, strategyMap, latestData)
			case FieldHandlerForVuln[[]int64]:
				fieldValInfo = processFieldHandlerVuln[[]int64](fh, fieldName, vuln, strategyMap, latestData)
			case FieldHandlerForVuln[[]int32]:
				fieldValInfo = processFieldHandlerVuln[[]int32](fh, fieldName, vuln, strategyMap, latestData)
			case FieldHandlerForVuln[[]int16]:
				fieldValInfo = processFieldHandlerVuln[[]int16](fh, fieldName, vuln, strategyMap, latestData)
			case FieldHandlerForVuln[[]int8]:
				fieldValInfo = processFieldHandlerVuln[[]int8](fh, fieldName, vuln, strategyMap, latestData)
			case FieldHandlerForVuln[[]uint64]:
				fieldValInfo = processFieldHandlerVuln[[]uint64](fh, fieldName, vuln, strategyMap, latestData)
			case FieldHandlerForVuln[[]uint32]:
				fieldValInfo = processFieldHandlerVuln[[]uint32](fh, fieldName, vuln, strategyMap, latestData)
			case FieldHandlerForVuln[map[string]any]:
				fieldValInfo = processFieldHandlerVuln[map[string]any](fh, fieldName, vuln, strategyMap, latestData)
			case FieldHandlerForVuln[map[string]string]:
				fieldValInfo = processFieldHandlerVuln[map[string]string](fh, fieldName, vuln, strategyMap, latestData)
			case FieldHandlerForVuln[uint64]:
				fieldValInfo = processFieldHandlerVuln[uint64](fh, fieldName, vuln, strategyMap, latestData)
			default:
				logs.GetLogger("vuln").Errorf("未知的字段处理器类型. fieldName: %s", fieldName)
				return
			}
			if fieldValInfo != nil {
				fieldValInfo.FieldName = fieldName
				fieldValInfoList = append(fieldValInfoList, fieldValInfo)
			}
		}(fieldName, fieldHandler)
	}
	vuln.SourceIds = utils.ListDistinctNonZero(vuln.SourceIds)
	vuln.NodeIds = utils.ListDistinctNonZero(vuln.NodeIds)
	vuln.TaskDataIds = utils.ListDistinctNonZero(vuln.TaskDataIds)
	vuln.ProcessIds = utils.ListDistinctNonZero(vuln.ProcessIds)
	return fieldValInfoList
}

func processFieldHandlerVuln[T any](fh FieldHandlerForVuln[T], fieldName string, vuln *poc.Poc, strategyMap map[string]*dbmodel.Strategy, latestData map[string]*poc.ProcessPoc) *es_model.FieldValInfo {
	if fh.GetAllData() {
		// 处理字段
		fieldVal, err := fh.Getter(nil, nil, latestData)
		if err != nil {
			logs.GetLogger("vuln").Errorf("计算字段值失败. fieldName: %s, err: %v", fieldName, err)
			return nil
		}
		// 保存字段结果
		err = fh.ResultHandler(vuln, fieldVal.Values)
		if err != nil {
			logs.GetLogger("vuln").Errorf("保存字段结果失败. fieldName: %s, err: %v", fieldName, err)
			return nil
		}
		// 获取所有源的字段值，不再保存采信的sourceIds、nodeIds、taskDataIds、processIds
		return &fieldVal.FieldValInfo
	} else {
		if st, ok := strategyMap[fieldName]; ok {
			sourcePriority, sourceList := GetSourceByPriority(st)
			fieldVal, err := fh.Getter(sourcePriority, sourceList, latestData)
			if err != nil {
				logs.GetLogger("vuln").Errorf("计算字段值失败. fieldName: %s, err: %v", fieldName, err)
				return nil
			}
			// 保存字段结果
			err = fh.ResultHandler(vuln, fieldVal.Values)
			if err != nil {
				logs.GetLogger("vuln").Errorf("保存字段结果失败. fieldName: %s, err: %v", fieldName, err)
				return nil
			}
			vuln.SourceIds = append(vuln.SourceIds, fieldVal.SourceIds...)
			vuln.NodeIds = append(vuln.NodeIds, fieldVal.NodeIds...)
			vuln.TaskDataIds = append(vuln.TaskDataIds, fieldVal.TaskDataIds...)
			vuln.ProcessIds = append(vuln.ProcessIds, fieldVal.ProcessIds...)
			return &fieldVal.FieldValInfo
		}
	}
	return nil
}
