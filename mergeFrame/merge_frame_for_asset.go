package frame

import (
	"errors"
	"fobrain/fobrain/common/localtime"
	logs "fobrain/mergeService/utils/log"
	es_model "fobrain/models/elastic"
	"fobrain/models/elastic/assets"
	"fobrain/models/mysql/strategy"
	"fobrain/pkg/utils"
	"slices"
	"strings"
	"sync"
)

type FieldHandlerForAsset[T any] interface {
	// GetAllData 返回是否需要获取所有数据源的数据
	// 返回true时会调用getAllFieldValForAsset获取所有数据
	// 返回false时会按照优先级策略获取数据
	GetAllData() bool

	// Getter 获取字段值
	// sourcePriority: 数据源优先级列表
	// sourceList: 每个优先级对应的数据源ID列表
	// allData: 所有数据源的数据
	// 返回值：
	// *FieldValue[T]: 字段值及其来源信息
	// error: 错误信息
	Getter(sourcePriority []uint64, sourceList map[uint64][]string, allData map[string]*assets.ProcessAssets) (*FieldValue[T], error)

	// ResultHandler 保存字段结果
	ResultHandler(asset *assets.Assets, result []T) error
}

type GenericFieldHandlerForAsset[T any] struct {
	fieldName     string
	getAllData    bool
	checkFunc     func(dataSource *assets.ProcessAssets) (T, bool)
	resultHandler func(asset *assets.Assets, result []T) error
}
type Business struct {
	Business *assets.Business
	Source   uint64
	Node     uint64
}

func (h *GenericFieldHandlerForAsset[T]) Getter(sourcePriority []uint64, sourceList map[uint64][]string, allData map[string]*assets.ProcessAssets) (*FieldValue[T], error) {
	if h.getAllData {
		return getAllFieldValForAsset(allData, h.checkFunc)
	}
	return getFieldValByStrategyForAsset(sourcePriority, sourceList, allData, h.checkFunc)
}

func (h *GenericFieldHandlerForAsset[T]) ResultHandler(asset *assets.Assets, result []T) error {
	return h.resultHandler(asset, result)
}

func (h *GenericFieldHandlerForAsset[T]) GetAllData() bool {
	return h.getAllData
}

var (
	fieldHandlerMapForAsset map[string]interface{}
	handlerMutexForAsset    sync.RWMutex
	onceForAsset            sync.Once
)

func init() {
	onceForAsset.Do(func() {
		fieldHandlerMapForAsset = make(map[string]interface{})
	})
}

// GetAllFieldHandlerForAsset 获取所有字段处理器
func GetAllFieldHandlerForAsset() map[string]interface{} {
	return fieldHandlerMapForAsset
}

// GetFieldHandlerForAsset 获取字段处理器
func GetFieldHandlerForAsset[T any](fieldName string) (FieldHandlerForAsset[T], bool) {
	fieldName = strings.ToLower(fieldName)
	fieldHandler, ok := fieldHandlerMapForAsset[fieldName]
	if !ok {
		return nil, false
	}
	handler, ok := fieldHandler.(FieldHandlerForAsset[T])
	return handler, ok
}

// RegisterFieldHandlerForAsset 注册字段处理器
func RegisterFieldHandlerForAsset[T any](fieldName string, getAllData bool, checkFunc func(dataSource *assets.ProcessAssets) (T, bool), resultHandler func(asset *assets.Assets, result []T) error) error {
	handlerMutexForAsset.Lock()
	defer handlerMutexForAsset.Unlock()
	if fieldName == "" {
		return errors.New("field name cannot be empty")
	}
	if checkFunc == nil {
		return errors.New("checkFunc cannot be nil")
	}
	if resultHandler == nil {
		return errors.New("resultHandler cannot be nil")
	}
	fieldName = strings.ToLower(fieldName)
	if _, ok := fieldHandlerMapForAsset[fieldName]; ok {
		return errors.New("field handler for " + fieldName + " already exists")
	}
	fieldHandlerMapForAsset[fieldName] = &GenericFieldHandlerForAsset[T]{
		fieldName:     fieldName,
		getAllData:    getAllData,
		checkFunc:     checkFunc,
		resultHandler: resultHandler,
	}
	return nil
}

// getFieldValByStrategyForAsset 获取字段值
// sourcePriority 优先级顺序
// sourceList 源节点列表,key为优先级,value为源节点id列表
// allData 所有数据,key为源节点id,value为源节点数据
// fieldName 字段名,区分大小写
// checkFunc 字段值获取函数，返回值为字段值和是否获取成功
func getFieldValByStrategyForAsset[T any](sourcePriority []uint64, sourceList map[uint64][]string, allData map[string]*assets.ProcessAssets, checkFunc func(dataSource *assets.ProcessAssets) (T, bool)) (*FieldValue[T], error) {
	result := make([]T, 0)
	sourceIds := make([]uint64, 0)
	nodeIds := make([]uint64, 0)
	taskDataIds := make([]string, 0)
	processIds := make([]string, 0)

	// 按照策略规则进行融合
	// 遍历优先级顺序
	for _, p := range sourcePriority {
		// 当前优先级是否存在非空值的标识
		hasNonEmptyValue := false
		// 遍历每个源节点的最新数据，按照策略规则进行融合
		for id, sd := range allData {
			// 如果当前源在当前优先级中,就可以计算当前源的值
			if slices.Contains(sourceList[p], id) {
				// 计算当前源的值
				r, ok := checkFunc(sd)
				if ok {
					result = append(result, r)
					hasNonEmptyValue = true
					sourceIds = append(sourceIds, sd.GetSource())
					nodeIds = append(nodeIds, sd.GetNode())
					taskDataIds = append(taskDataIds, sd.GetTaskDataId())
					processIds = append(processIds, sd.GetId())
				}
				// 继续寻找下一个符合条件的源
				continue
			}
		}
		// 如果当前优先级存在非空值，则跳出优先级循环
		if hasNonEmptyValue {
			break
		}
	}
	return &FieldValue[T]{
		FieldValInfo: es_model.FieldValInfo{
			SourceIds:   sourceIds,
			NodeIds:     nodeIds,
			TaskDataIds: taskDataIds,
			ProcessIds:  processIds,
		},
		Values: result,
	}, nil
}

// getAllFieldValForDevice 获取所有源提供的字段值
// allData 所有数据,key为源节点id,value为源节点数据
// fieldName 字段名,区分大小写
// checkFunc 字段值获取函数，返回值为字段值和是否获取成功
func getAllFieldValForAsset[T any](allData map[string]*assets.ProcessAssets, checkFunc func(dataSource *assets.ProcessAssets) (T, bool)) (*FieldValue[T], error) {
	result := make([]T, 0)
	sourceIds := make([]uint64, 0)
	nodeIds := make([]uint64, 0)
	taskDataIds := make([]string, 0)
	processIds := make([]string, 0)

	// 遍历所有数据
	for _, sd := range allData {
		// 计算当前源的值
		r, ok := checkFunc(sd)
		if ok {
			result = append(result, r)
			sourceIds = append(sourceIds, sd.GetSource())
			nodeIds = append(nodeIds, sd.GetNode())
			taskDataIds = append(taskDataIds, sd.GetTaskDataId())
			processIds = append(processIds, sd.GetId())
		}
	}

	return &FieldValue[T]{
		FieldValInfo: es_model.FieldValInfo{
			SourceIds:   sourceIds,
			NodeIds:     nodeIds,
			TaskDataIds: taskDataIds,
			ProcessIds:  processIds,
		},
		Values: result,
	}, nil
}

// AssetFieldProcess 资产融合字段处理
// asset 资产
// strategies 融合策略
// latestData 最新数据
// 返回值：
// 字段采信信息 -> 源节点id列表、节点id列表、taskDataId列表、processId列表
func AssetFieldProcess(asset *assets.Assets, strategies []*strategy.Strategy, latestData map[string]*assets.ProcessAssets) []*es_model.FieldValInfo {
	// 预处理融合策略
	preProcessStrategies := PreProcessStrategy(strategies)
	// 获取所有字段及对应的处理器
	fieldHandlerMap := GetAllFieldHandlerForAsset()
	// 字段采信信息
	fieldValInfoList := make([]*es_model.FieldValInfo, 0)
	// 遍历字段处理器
	for fieldName, fieldHandler := range fieldHandlerMap {
		logs.GetLogger("asset").Debugf("开始处理字段. fieldName: %s", fieldName)
		func(fieldName string, fieldHandler interface{}) {
			defer func() {
				if r := recover(); r != nil {
					logs.GetLogger("asset").Errorf("处理字段失败. fieldName: %s, err: %v", fieldName, r)
				}
				logs.GetLogger("asset").Debugf("结束处理字段. fieldName: %s", fieldName)
			}()
			fieldValInfo := &es_model.FieldValInfo{}
			switch fh := fieldHandler.(type) {
			case FieldHandlerForAsset[string]:
				fieldValInfo = processFieldHandlerForAsset(fh, fieldName, asset, preProcessStrategies, latestData)
			case FieldHandlerForAsset[[]string]:
				fieldValInfo = processFieldHandlerForAsset(fh, fieldName, asset, preProcessStrategies, latestData)
			case FieldHandlerForAsset[int]:
				fieldValInfo = processFieldHandlerForAsset(fh, fieldName, asset, preProcessStrategies, latestData)
			case FieldHandlerForAsset[int64]:
				fieldValInfo = processFieldHandlerForAsset(fh, fieldName, asset, preProcessStrategies, latestData)
			case FieldHandlerForAsset[uint64]:
				fieldValInfo = processFieldHandlerForAsset(fh, fieldName, asset, preProcessStrategies, latestData)
			case FieldHandlerForAsset[[]int]:
				fieldValInfo = processFieldHandlerForAsset(fh, fieldName, asset, preProcessStrategies, latestData)
			case FieldHandlerForAsset[[]int64]:
				fieldValInfo = processFieldHandlerForAsset(fh, fieldName, asset, preProcessStrategies, latestData)
			case FieldHandlerForAsset[[]uint64]:
				fieldValInfo = processFieldHandlerForAsset(fh, fieldName, asset, preProcessStrategies, latestData)
			case FieldHandlerForAsset[*Business]:
				fieldValInfo = processFieldHandlerForAsset(fh, fieldName, asset, preProcessStrategies, latestData)
			case FieldHandlerForAsset[map[string]string]:
				fieldValInfo = processFieldHandlerForAsset(fh, fieldName, asset, preProcessStrategies, latestData)
			case FieldHandlerForAsset[map[string]interface{}]:
				fieldValInfo = processFieldHandlerForAsset(fh, fieldName, asset, preProcessStrategies, latestData)
			case FieldHandlerForAsset[*localtime.Time]:
				fieldValInfo = processFieldHandlerForAsset(fh, fieldName, asset, preProcessStrategies, latestData)
			case FieldHandlerForAsset[[]*assets.PortInfo]:
				fieldValInfo = processFieldHandlerForAsset(fh, fieldName, asset, preProcessStrategies, latestData)
			case FieldHandlerForAsset[[]*assets.RuleInfo]:
				fieldValInfo = processFieldHandlerForAsset(fh, fieldName, asset, preProcessStrategies, latestData)
			case FieldHandlerForAsset[*assets.PersonWithMapping]:
				fieldValInfo = processFieldHandlerForAsset(fh, fieldName, asset, preProcessStrategies, latestData)
			case FieldHandlerForAsset[[]*assets.JarPackageInfo]:
				fieldValInfo = processFieldHandlerForAsset(fh, fieldName, asset, preProcessStrategies, latestData)
			case FieldHandlerForAsset[[]*assets.BusinessInfo]:
				fieldValInfo = processFieldHandlerForAsset(fh, fieldName, asset, preProcessStrategies, latestData)
			default:
				logs.GetLogger("asset").Errorf("未知的字段处理器类型. fieldName: %s", fieldName)
			}
			if fieldValInfo != nil {
				fieldValInfo.FieldName = fieldName
				fieldValInfoList = append(fieldValInfoList, fieldValInfo)
			}
		}(fieldName, fieldHandler)
	}

	asset.SourceIds = utils.ListDistinctNonZero(asset.SourceIds)
	asset.NodeIds = utils.ListDistinctNonZero(asset.NodeIds)
	asset.TaskDataIds = utils.ListDistinctNonZero(asset.TaskDataIds)
	asset.ProcessIds = utils.ListDistinctNonZero(asset.ProcessIds)
	return fieldValInfoList
}

func processFieldHandlerForAsset[T any](handler FieldHandlerForAsset[T], fieldName string, asset *assets.Assets, preProcessStrategies map[string]*strategy.Strategy, latestData map[string]*assets.ProcessAssets) *es_model.FieldValInfo {
	if handler.GetAllData() {
		// 获取所有数据
		fieldVal, err := handler.Getter(nil, nil, latestData)
		if err != nil {
			logs.GetLogger("asset").Errorf("获取字段值失败. fieldName: %s, err: %v", fieldName, err)
			return nil
		}
		// 保存字段结果
		err = handler.ResultHandler(asset, fieldVal.Values)
		if err != nil {
			logs.GetLogger("asset").Errorf("保存字段结果失败. fieldName: %s, err: %v", fieldName, err)
			return nil
		}
		return &fieldVal.FieldValInfo
	} else {
		if strategy, ok := preProcessStrategies[fieldName]; ok {
			sourcePriority, sourceList := GetSourceByPriority(strategy)
			// 按照策略获取数据
			fieldVal, err := handler.Getter(sourcePriority, sourceList, latestData)
			if err != nil {
				logs.GetLogger("asset").Errorf("获取字段值失败. fieldName: %s, err: %v", fieldName, err)
				return nil
			}
			// 保存字段结果
			err = handler.ResultHandler(asset, fieldVal.Values)
			if err != nil {
				logs.GetLogger("asset").Errorf("保存字段结果失败. fieldName: %s, err: %v", fieldName, err)
				return nil
			}
			asset.SourceIds = append(asset.SourceIds, fieldVal.FieldValInfo.SourceIds...)
			asset.NodeIds = append(asset.NodeIds, fieldVal.FieldValInfo.NodeIds...)
			asset.TaskDataIds = append(asset.TaskDataIds, fieldVal.FieldValInfo.TaskDataIds...)
			asset.ProcessIds = append(asset.ProcessIds, fieldVal.FieldValInfo.ProcessIds...)
			return &fieldVal.FieldValInfo
		}
	}
	return nil
}
