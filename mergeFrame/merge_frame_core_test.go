package frame

import (
	dbmodel "fobrain/models/mysql/strategy"
	"reflect"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestGetSourceByPriority(t *testing.T) {
	tests := []struct {
		name     string
		strategy *dbmodel.Strategy
		wantPri  []uint64
		wantSrc  map[uint64][]string
	}{
		{
			name:     "空策略",
			strategy: nil,
			wantPri:  []uint64{},
			wantSrc:  nil,
		},
		{
			name: "空优先级列表",
			strategy: &dbmodel.Strategy{
				SourcePriority: map[string]uint64{},
			},
			wantPri: []uint64{},
			wantSrc: nil,
		},
		{
			name: "包含不可信源",
			strategy: &dbmodel.Strategy{
				SourcePriority: map[string]uint64{
					"source1": 1,
					"source2": 2,
					"source3": 3,
				},
				UntrustedSource: []string{"source2"},
			},
			wantPri: []uint64{1, 3},
			wantSrc: map[uint64][]string{
				1: {"source1"},
				3: {"source3"},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotPri, gotSrc := GetSourceByPriority(tt.strategy)

			// 检查优先级列表
			if !reflect.DeepEqual(gotPri, tt.wantPri) {
				t.Errorf("GetSourceByPriority() gotPri = %v, want %v", gotPri, tt.wantPri)
			}

			// 检查源映射
			if !reflect.DeepEqual(gotSrc, tt.wantSrc) {
				t.Errorf("GetSourceByPriority() gotSrc = %v, want %v", gotSrc, tt.wantSrc)
			}
		})
	}
}

func Test_strFieldCheck(t *testing.T) {
	tests := []struct {
		name     string
		fieldVal string
		want     string
		wantBool bool
	}{
		{
			name:     "空字符串",
			fieldVal: "",
			want:     "",
			wantBool: false,
		},
		{
			name:     "非空字符串",
			fieldVal: "test",
			want:     "test",
			wantBool: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, gotBool := StrFieldCheck(tt.fieldVal)
			assert.Equal(t, tt.want, got)
			assert.Equal(t, tt.wantBool, gotBool)
		})
	}
}

func Test_intFieldCheck(t *testing.T) {
	tests := []struct {
		name     string
		fieldVal int
		want     int
		wantBool bool
	}{
		{
			name:     "零值",
			fieldVal: 0,
			want:     0,
			wantBool: false,
		},
		{
			name:     "正数",
			fieldVal: 42,
			want:     42,
			wantBool: true,
		},
		{
			name:     "负数",
			fieldVal: -1,
			want:     -1,
			wantBool: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, gotBool := IntFieldCheck(tt.fieldVal)
			assert.Equal(t, tt.want, got)
			assert.Equal(t, tt.wantBool, gotBool)
		})
	}
}

func Test_strFieldResultHandler(t *testing.T) {
	tests := []struct {
		name      string
		field     []string
		result    [][]string
		wantField []string
		wantError bool
	}{
		{
			name:      "空结果",
			field:     []string{"old"},
			result:    [][]string{},
			wantField: []string{},
			wantError: false,
		},
		{
			name:      "有效结果",
			field:     []string{},
			result:    [][]string{{"test1", "test2", "test1"}},
			wantField: []string{"test1", "test2"},
			wantError: false,
		},
		{
			name:      "合并已有值",
			field:     []string{"existing"},
			result:    [][]string{{"new", "existing"}},
			wantField: []string{"existing", "new"},
			wantError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			field := tt.field
			err := SliceStrSliceFieldResultHandler[string](&field, tt.result)
			if tt.wantError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.ElementsMatch(t, tt.wantField, field)
			}
		})
	}
}

func Test_intFieldResultHandler(t *testing.T) {
	tests := []struct {
		name      string
		field     []int
		result    []int
		wantField []int
		wantError bool
	}{
		{
			name:      "空结果",
			field:     []int{1},
			result:    []int{},
			wantField: []int{},
			wantError: false,
		},
		{
			name:      "有效结果",
			field:     []int{},
			result:    []int{1, 2, 1},
			wantField: []int{1, 2},
			wantError: false,
		},
		{
			name:      "合并已有值",
			field:     []int{1},
			result:    []int{2, 1},
			wantField: []int{1, 2},
			wantError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			field := tt.field
			err := IntSliceFieldResultHandler(&field, tt.result)
			if tt.wantError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.ElementsMatch(t, tt.wantField, field)
			}
		})
	}
}

func TestPreProcessStrategy(t *testing.T) {
	tests := []struct {
		name       string
		strategies []*dbmodel.Strategy
		want       map[string]*dbmodel.Strategy
	}{
		{
			name:       "空输入测试",
			strategies: []*dbmodel.Strategy{},
			want:       map[string]*dbmodel.Strategy{},
		},
		{
			name: "单个策略测试",
			strategies: []*dbmodel.Strategy{
				{
					FieldName: "Name",
				},
			},
			want: map[string]*dbmodel.Strategy{
				"name": {
					FieldName: "Name",
				},
			},
		},
		{
			name: "多个策略测试",
			strategies: []*dbmodel.Strategy{
				{
					FieldName: "Name",
				},
				{
					FieldName: "Age",
				},
			},
			want: map[string]*dbmodel.Strategy{
				"name": {
					FieldName: "Name",
				},
				"age": {
					FieldName: "Age",
				},
			},
		},
		{
			name: "字段名大小写测试",
			strategies: []*dbmodel.Strategy{
				{
					FieldName: "NAME",
				},
				{
					FieldName: "age",
				},
				{
					FieldName: "PhOnE",
				},
			},
			want: map[string]*dbmodel.Strategy{
				"name": {
					FieldName: "NAME",
				},
				"age": {
					FieldName: "age",
				},
				"phone": {
					FieldName: "PhOnE",
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := PreProcessStrategy(tt.strategies)
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("PreProcessStrategy() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_strSliceFieldResultHandler(t *testing.T) {
	tests := []struct {
		name      string
		field     []string
		result    [][]string
		wantField []string
		wantError bool
	}{
		{
			name:      "空结果测试",
			field:     []string{"old"},
			result:    [][]string{},
			wantField: []string{},
			wantError: false,
		},
		{
			name:      "空字段测试",
			field:     []string{},
			result:    [][]string{[]string{"test1", "test2"}},
			wantField: []string{"test1", "test2"},
			wantError: false,
		},
		{
			name:      "合并已有值测试",
			field:     []string{"existing"},
			result:    [][]string{[]string{"new", "existing"}},
			wantField: []string{"existing", "new"},
			wantError: false,
		},
		{
			name:      "去重测试",
			field:     []string{"value1"},
			result:    [][]string{[]string{"value1", "value2", "value1", "value2"}},
			wantField: []string{"value1", "value2"},
			wantError: false,
		},
		{
			name:      "多个切片合并测试",
			field:     []string{},
			result:    [][]string{[]string{"a", "b"}, []string{"c", "d"}},
			wantField: []string{"a", "b", "c", "d"},
			wantError: false,
		},
		{
			name:      "空字符串过滤测试",
			field:     []string{},
			result:    [][]string{[]string{"", "valid", ""}},
			wantField: []string{"valid"},
			wantError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			field := tt.field
			err := SliceStrSliceFieldResultHandler(&field, tt.result)
			if tt.wantError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.ElementsMatch(t, tt.wantField, field)
			}
		})
	}
}
