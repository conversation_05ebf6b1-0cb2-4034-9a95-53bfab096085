# Mac安装  curl -sSfL https://raw.githubusercontent.com/cosmtrek/air/master/install.sh | sh -s -- -b $(go env GOPATH)/bin
#使用 air server
# 表示当前路径
root="./"
# 临时文件目录
tmp_dir="tmp"

[build]
# 编译的命令
cmd="go build -o tmp/fobrain ."
# 最终可执行文件的名字
bin="fobrain"
# 执行程序的命令
full_bin="tmp/fobrain"
# 监听哪些扩展的文件
include_ext=["go", "yaml", "tpl", "html"]
# 忽略文件扩展名或目录
exclude_dir=["storage", "js"]
# 编译延迟 防止构建频繁
delay=1000
# 构建错误时 停止运行旧的二进制文件
stop_on_error=true
# air 的日志名
log="./storage/log/air_errors.log"
# 显示日志时间
time=true
[color]
main="magenta"
watcher="cyan"
build="yellow"
runner="green"

[misc]
# 退出时删除tmp目录
clean_on_exit=true
