#!/bin/sh

version=""

# 从VERSION文件中获取版本号
# if [ -f "VERSION" ]; then
#     version=`cat VERSION`
# fi

# 如果VERSION文件中没有版本号，则从git中获取版本号
if [[ -z $version ]]; then
    if [ -d ".git" ]; then
        # 获取git分支名
        branch=`git symbolic-ref HEAD | cut -b 12-`
        # 获取git commit id
        commitId=`git rev-parse HEAD`
        # 获取git commit id前8位
        # commitId=${commitId:0:8}
        version="$branch-$commitId"
    else
        version="unknown"
    fi
fi

# 构建版本
go build -ldflags "-X main.Version=$version -X main.BuildTime=$(date +%Y-%m-%d_%H:%M:%S)" ./fobrain/main.go

# 输出版本号
echo $version