#!/bin/bash

git_branch=$1
release_version=$2
pack_docker_compose=$3

release_greek_alphabet_version='beta'
if [ -z "$git_branch" ]; then
  echo "git branch is empty"
  exit 1
fi

git checkout .

git checkout $git_branch
git status

git_branch_current=$(git rev-parse --abbrev-ref HEAD) # 查询当前分支
echo "git_branch_current: $git_branch_current"

if [ "$git_branch" != "$git_branch_current" ]; then
  echo "git branch is not $git_branch"
  exit 1
fi

git pull

echo "--clean upgrade package folder and fobrain-upgrade-v$release_version-$release_greek_alphabet_version.tar.gz"
rm -rf fobrain-upgrade-v$release_version-$release_greek_alphabet_version.tar.gz
rm -rf upgrade-package
mkdir -p upgrade-package
mkdir -p upgrade-package/build-images
mkdir -p upgrade-package/build-images/fobrain

echo "--go mod tidy"
go mod tidy

# 从git中获取版本号
code_version=""
if [[ -z $code_version ]]; then
    if [ -d ".git" ]; then
        # 获取git分支名
        branch=`git symbolic-ref HEAD | cut -b 12-`
        # 获取git commit id
        commitId=`git rev-parse HEAD`
        # 获取git commit id前8位
        # commitId=${commitId:0:8}
        code_version="$branch-$commitId"
    else
        code_version="unknown"
    fi
fi

echo "--building fobrain"
cd fobrain && go build -ldflags "-X main.Version=$code_version -X main.BuildTime=$(date +%Y-%m-%d_%H:%M:%S)" -o fobrain-linux && cd ..

echo "--building mergeService"
cd mergeService && go build -o merge-linux && cd ..

echo "--building cmd"
cd cmd && go build -o cmd-linux && cd ..

echo "--moving files"
mv fobrain/fobrain-linux ./upgrade-package/fobrain.server
mv mergeService/merge-linux ./upgrade-package/merge-service.server
mv cmd/cmd-linux ./upgrade-package/cmd-migrate
echo "--copy version"
cp version ./upgrade-package/version

echo "--copy system_upgrade.sh"
cp ./upgrade/system_upgrade.sh ./upgrade-package/

echo "--copy dist.tar.gz"
cp /data/dist.tar.gz ./upgrade-package/

echo "--copy config.json"
cp ./install/amd64/v2/config.json ./upgrade-package/

echo "--copy public"
cp -r ./storage/app/public ./upgrade-package/

if [ "$pack_docker_compose" == "true" ]; then
  echo "--copy docker-compose.yaml"
  cp ./install/amd64/v2/docker-compose.yaml ./upgrade-package/
fi

echo "--开始构建镜像--"
cp ./install/amd64/v2/config.json ./upgrade-package/build-images/fobrain/fobrain.json
cp version ./upgrade-package/build-images/fobrain/
cp ./upgrade-package/fobrain.server ./upgrade-package/build-images/fobrain/fobrain
cp ./upgrade-package/merge-service.server ./upgrade-package/build-images/fobrain/merge-service
cp ./upgrade-package/cmd-migrate ./upgrade-package/build-images/fobrain/cmd

cp ./install/amd64/build/Dockerfile-fobrain ./upgrade-package/build-images/
cp ./install/amd64/build/Dockerfile-elasticsearch-s3 ./upgrade-package/build-images

cd ./upgrade-package/build-images
# 构建fobrain镜像
echo "--构建fobrain镜像--"
docker build -t fobrain:latest -f Dockerfile-fobrain .
docker save fobrain:latest -o fobrain.tar

# 构建elasticsearch镜像
echo "--构建elasticsearch镜像--"
docker build -t es-amd64:7.17.26 -f Dockerfile-elasticsearch-s3 .
docker save es-amd64:7.17.26 -o elasticsearch.tar

cd ../../

echo "--构建fobrain镜像完成--"

echo "-------相关文件 已放入upgrade-package"
cd upgrade-package
tar -cvzf fobrain-upgrade-v$release_version-$release_greek_alphabet_version.tar.gz ./*

mv fobrain-upgrade-v$release_version-$release_greek_alphabet_version.tar.gz ../
cd ../
rm -rf upgrade-package

echo "--打包结束, 路径:$(pwd)/fobrain-upgrade-v$release_version-$release_greek_alphabet_version.tar.gz"

echo "--end--"