#!/bin/bash

echo "----------开始执行升级子程序------------------"
function handleESDir() {
    # 判断宿主机 /data/fobrain/storage/data/es目录跟/usr/share/elasticsearch/data目录是否数据一致
      if [ -z "$(ls -A /data/fobrain/storage/data/es)" ] || [ -z "$(docker inspect -f '{{ range .Mounts }}{{ if eq .Source "/data/fobrain/storage/data/es" }}{{ .Destination }}{{ end }}{{ end }}' es)" ]; then
        echo "[Info] 宿主机 elasticsearch数据目录为空或者数据不一致"
        # 拷贝es容器数据到该目录
        docker cp es:/usr/share/elasticsearch/data/. /data/fobrain/storage/data/es
        # 判断是否拷贝成功
        if [ -z "$(ls -A /data/fobrain/storage/data/es)" ]; then
          echo "[Error] 拷贝elasticsearch数据失败" >&2
          update_progress 40 true "拷贝elasticsearch数据失败" false
          rollback
          exit 1
        fi
      else
        # 检查目录是否存在，不存在则创建
        if [ ! -d "/data/fobrain/storage/data/containerEs" ]; then
          mkdir -p /data/fobrain/storage/data/containerEs
        fi
        # 拷贝es容器数据到该目录
        docker cp es:/usr/share/elasticsearch/data/. /data/fobrain/storage/data/containerEs
        # 判断是否拷贝成功
        if [ -z "$(ls -A /data/fobrain/storage/data/es)" ]; then
          echo "[Error] 拷贝elasticsearch数据失败" >&2
          update_progress 40 true "拷贝elasticsearch数据失败" false
          rollback
          exit 1
        fi
        echo "[Info] 宿主机 elasticsearch数据目录不为空"
      fi
      #停止并删除旧es容器
      docker stop es
      docker rm -f es
      echo "[Info] 重新启动 elasticsearch--"
      cd $fobrainDir
      if ! docker-compose up -d --no-deps es; then
        echo "[Error] 重新启动 elasticsearch失败" >&2
        update_progress 40 true "重新启动 elasticsearch失败" false
        rollback
        exit 1
      fi
      echo "[Info] 重新启动 elasticsearch 成功"
}

function deleteOldIndex() {
  echo "[Info] 清理旧设备索引及数据表"
  ES_HOST='http://127.0.0.1:9200'
  INDEX_NAME="device_merge_record,device_record,device"

  # 删除索引
  echo "[Info] 正在删除索引 $INDEX_NAME ..."
  DELETE_RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" -X DELETE "$ES_HOST/$INDEX_NAME")

  # 检查删除操作是否成功
  if [ "$DELETE_RESPONSE" -eq 200 ]; then
    echo "[Info] 索引 $INDEX_NAME 删除成功！"
  elif [ "$DELETE_RESPONSE" -eq 404 ]; then
    echo "索引 $INDEX_NAME 不存在，无需删除。"
  else
    echo "索引 $INDEX_NAME 删除失败，HTTP 状态码: $DELETE_RESPONSE"
    exit 1
  fi

  # 检查索引是否真的不存在
  echo "[Info] 正在检查索引 $INDEX_NAME 是否存在..."
  INDEX_EXIST=$(curl -s -o /dev/null -w "%{http_code}" -X GET "$ES_HOST/_cat/indices/$INDEX_NAME?v")

  if [ "$INDEX_EXIST" -eq 404 ]; then
    echo "[Info] 确认索引 $INDEX_NAME 已不存在。"
  else
    echo "警告：索引 $INDEX_NAME 仍然存在！"
    exit 1
  fi

  # 删除 MySQL 数据
  # MySQL 配置
  MYSQL_USER="root"
  MYSQL_DATABASE="fobrain"
  MYSQL_CONTAINER_NAME="mysql"  # 替换为你的 MySQL 容器名称
  # 设置 MySQL 密码
  MYSQL_PWD="Fobrain@@#13244%!"
  echo "[Info] 清理 MySQL 数据库 $MYSQL_DATABASE 表 $MYSQL_TABLE 中满足条件的数据..."

  # 根据密码是否为空调整命令
  if [ -z "$MYSQL_PWD" ]; then
    DELETE_MYSQL_DATA=$(docker exec -i $MYSQL_CONTAINER_NAME mysql -u "$MYSQL_USER" -D "$MYSQL_DATABASE" -e "DELETE FROM migrations WHERE migration='*migrations.MergeDeviceBlacklist20241018185022Table'" 2>&1)
  else
    DELETE_MYSQL_DATA=$(docker exec -i $MYSQL_CONTAINER_NAME mysql -u "$MYSQL_USER" -p"$MYSQL_PWD" -D "$MYSQL_DATABASE" -e "DELETE FROM migrations WHERE migration='*migrations.MergeDeviceBlacklist20241018185022Table'" 2>&1)
  fi

  if [ $? -eq 0 ]; then
    echo "[Info] MySQL 数据删除成功！"
  else
    echo "MySQL 数据删除失败，错误信息: $DELETE_MYSQL_DATA"
    exit 1
  fi

  # 删除 MySQL 表
  echo "[Info] 正在删除 MySQL 数据库 $MYSQL_DATABASE 中的表 $MYSQL_TABLE ..."
  # 根据密码是否为空调整命令
  if [ -z "$MYSQL_PWD" ]; then
    DROP_TABLE_RESULT=$(docker exec -i $MYSQL_CONTAINER_NAME mysql -u "$MYSQL_USER" -D "$MYSQL_DATABASE" -e "DROP TABLE merge_device_blacklist;" 2>&1)
  else
    DROP_TABLE_RESULT=$(docker exec -i $MYSQL_CONTAINER_NAME mysql -u "$MYSQL_USER" -p"$MYSQL_PWD" -D "$MYSQL_DATABASE" -e "DROP TABLE merge_device_blacklist;" 2>&1)
  fi

  if [ $? -eq 0 ]; then
    echo "[Info] MySQL 表 $MYSQL_TABLE 删除成功！"
  else
    echo "MySQL 表 $MYSQL_TABLE 删除失败，错误信息: $DROP_TABLE_RESULT"
    exit 1
  fi

  # 清空豁免记录表 MySQL 表
  echo "[Info] 正在清空豁免记录表 workbench_asset_exempt_record ..."
  # 根据密码是否为空调整命令
  if [ -z "$MYSQL_PWD" ]; then
    CLEAR_TABLE_RESULT=$(docker exec -i $MYSQL_CONTAINER_NAME mysql -u "$MYSQL_USER" -D "$MYSQL_DATABASE" -e "DELETE from workbench_asset_exempt_record where id >0;" 2>&1)
  else
    CLEAR_TABLE_RESULT=$(docker exec -i $MYSQL_CONTAINER_NAME mysql -u "$MYSQL_USER" -p"$MYSQL_PWD" -D "$MYSQL_DATABASE" -e "DELETE from workbench_asset_exempt_record where id >0;" 2>&1)
  fi

  if [ $? -eq 0 ]; then
    echo "[Info] MySQL 表 workbench_asset_exempt_record 清空成功！"
  else
    echo "MySQL 表 workbench_asset_exempt_record 清空失败，错误信息: $CLEAR_TABLE_RESULT"
    exit 1
  fi

  echo "所有操作完成！"
}
function addRepoConfigToEs() {
    echo "[Info] 开始配置 Elasticsearch 仓库目录..."

    # 容器名称
    CONTAINER_NAME="es"
    # 仓库路径
    REPO_PATH="/usr/share/elasticsearch/data/snapshot"
    # 配置文件路径
    CONFIG_PATH="/usr/share/elasticsearch/config/elasticsearch.yml"

    # 检查容器是否存在
    if ! docker ps -a --format '{{.Names}}' | grep -q "^$CONTAINER_NAME$"; then
        echo "[Error] Elasticsearch 容器不存在"
        exit 1
    fi

    # 检查容器是否运行
    if ! docker ps --format '{{.Names}}' | grep -q "^$CONTAINER_NAME$"; then
        echo "[Error] Elasticsearch 容器未运行"
        exit 1
    fi

    # 检查配置文件是否存在
    if ! docker exec "$CONTAINER_NAME" test -f "$CONFIG_PATH"; then
        echo "[Error] 配置文件不存在: $CONFIG_PATH"
        exit 1
    fi

    # 创建仓库目录并设置权限
    echo "[Info] 创建仓库目录并设置权限..."
    if ! docker exec "$CONTAINER_NAME" sh -c "mkdir -p $REPO_PATH && \
        chown -R elasticsearch:elasticsearch $REPO_PATH && \
        chmod 755 $REPO_PATH"; then
        echo "[Error] 创建目录或设置权限失败"
        exit 1
    fi

    # 检查目录是否创建成功
    if ! docker exec "$CONTAINER_NAME" test -d "$REPO_PATH"; then
        echo "[Error] 仓库目录创建失败"
        exit 1
    fi

    # 添加或更新配置
    echo "[Info] 更新 Elasticsearch 配置..."
    CONFIG_LINE="path.repo: [\"$REPO_PATH\"]"
    if ! docker exec "$CONTAINER_NAME" grep -q "path.repo" "$CONFIG_PATH"; then
        # 如果配置不存在，则追加
        if ! docker exec "$CONTAINER_NAME" sh -c "echo '$CONFIG_LINE' >> $CONFIG_PATH"; then
            echo "[Error] 添加配置失败"
            exit 1
        fi
    else
        # 如果配置存在，则更新
        if ! docker exec "$CONTAINER_NAME" sed -i "s|path.repo:.*|$CONFIG_LINE|g" "$CONFIG_PATH"; then
            echo "[Error] 更新配置失败"
            exit 1
        fi
    fi

    # 验证配置
    echo "[Info] 验证配置..."
    if ! docker exec "$CONTAINER_NAME" grep "path.repo" "$CONFIG_PATH"; then
        echo "[Error] 配置验证失败"
        exit 1
    fi

    # 检查目录权限
    echo "[Info] 检查目录权限..."
    PERMS=$(docker exec "$CONTAINER_NAME" ls -ld "$REPO_PATH" | awk '{print $1,$3,$4}')
    echo "[Info] 目录权限: $PERMS"

    # 重启 ES 容器使配置生效
    echo "[Info] 重启 Elasticsearch 容器..."
    if ! docker restart "$CONTAINER_NAME"; then
        echo "[Error] 重启容器失败"
        exit 1
    fi

    # 等待 ES 启动
    echo "[Info] 等待 Elasticsearch 启动..."
    sleep 30

    echo "[Info] Elasticsearch 仓库目录配置完成"
}

function addMysqlBinFileToContainer(){
  cd $current_dir
  echo "[Info] 开始处理 MySQL 二进制文件..."
  # 容器名称
  CONTAINER_NAME="fobrain"
  # 容器内的目标路径
  CONTAINER_CONFIG_PATH="/usr/local/bin"

  # 检查容器是否运行
  if  ! docker ps --format '{{.Names}}' | grep -q "^$CONTAINER_NAME$" ; then
    echo "[Error] MySQL 容器未运行"
    exit 1
  fi

  # 检查源文件是否存在
  if [ ! -f "./mysql" ]; then
    echo "[Error] 本地 mysql 二进制文件不存在"
    exit 1
  fi

  if [ ! -f "./mysqldump" ]; then
    echo "[Error] 本地 mysqldump 二进制文件不存在"
    exit 1
  fi

  # 复制 mysql 二进制文件
  if ! docker exec "$CONTAINER_NAME" test -f "$CONTAINER_CONFIG_PATH/mysql"; then
    echo "[Info] 复制 mysql 二进制文件到容器"
    if ! docker cp ./mysql "$CONTAINER_NAME:$CONTAINER_CONFIG_PATH/"; then
      echo "[Error] mysql 二进制文件复制失败"
      exit 1
    fi
  else
    echo "[Info] mysql 二进制文件已存在"
  fi

  # 复制 mysqldump 二进制文件
  if ! docker exec "$CONTAINER_NAME" test -f "$CONTAINER_CONFIG_PATH/mysqldump"; then
    echo "[Info] 复制 mysqldump 二进制文件到容器"
    if ! docker cp ./mysqldump "$CONTAINER_NAME:$CONTAINER_CONFIG_PATH/"; then
      echo "[Error] mysqldump 二进制文件复制失败"
      exit 1
    fi
  else
    echo "[Info] mysqldump 二进制文件已存在"
  fi

  # 设置执行权限
  echo "[Info] 设置执行权限"
  if ! docker exec "$CONTAINER_NAME" chmod +x "$CONTAINER_CONFIG_PATH/mysql"; then
    echo "[Error] 设置 mysql 执行权限失败"
    exit 1
  fi

  if ! docker exec "$CONTAINER_NAME" chmod +x "$CONTAINER_CONFIG_PATH/mysqldump"; then
    echo "[Error] 设置 mysqldump 执行权限失败"
    exit 1
  fi

  echo "[Info] MySQL 二进制文件处理完成"
}
function checkKernelParams() {
  # 必须以 root 权限运行
  [ "$(id -u)" -ne 0 ] && { echo "必须以 root 权限运行此脚本"; exit 1; }

  SYSCTL_CONF="/etc/sysctl.conf"
  PARAM_FILE="/tmp/desired_params.txt"

  # 备份当前 sysctl.conf
  cp "$SYSCTL_CONF" "${SYSCTL_CONF}.bak"

  # 将期望的参数写入临时文件 注意此处here doc 前面不能有空格
  cat <<'EOF' > "$PARAM_FILE"
net.ipv4.tcp_fin_timeout 30
net.ipv4.tcp_tw_reuse 1
net.ipv4.ip_local_port_range 1024 65000
net.ipv4.tcp_max_tw_buckets 5000
net.ipv4.tcp_synack_retries 2
net.ipv4.tcp_syn_retries 2
net.nf_conntrack_max 2621440
fs.file-max 2097152
EOF

  updated=0

  # 从临时文件中逐行读取（避免管道导致的子 shell 问题）
  while IFS=' ' read -r key rest; do
      # rest 为期望的值（可能包含空格，比如 ip_local_port_range）
      desired_value="$rest"
      if [ "$key" = "net.ipv4.ip_local_port_range" ]; then
          # 对于此参数，标准化空格
          desired_value=$(echo "$desired_value" | tr -s ' ')
      fi

      # 获取当前值，并标准化空格（防止格式不一致）
      current_value=$(sysctl -n "$key" 2>/dev/null | tr -s ' ')
      if [ $? -ne 0 ]; then
          echo "参数 $key 不存在，跳过。"
          continue
      fi

      if [ "$current_value" != "$desired_value" ]; then
          echo "更新 $key: 当前值 ($current_value) -> 期望值 ($desired_value)"
          sysctl -w "$key=$desired_value"
          updated=1
      else
          echo "$key 已经设置为 $desired_value"
      fi

      # 更新 /etc/sysctl.conf：
      # 先删除该参数的已有行（格式为 key=...，注意等号两侧不允许有空格）
      sed -i "/^$key[[:space:]]*=/d" "$SYSCTL_CONF"
      # 追加新的设置（确保格式为 key=value）
      echo "$key=$desired_value" >> "$SYSCTL_CONF"
  done < "$PARAM_FILE"

  if [ $updated -eq 1 ]; then
      echo "更新 /etc/sysctl.conf 后执行 sysctl -p 输出："
      sysctl -p
  else
      echo "所有内核参数已是期望值，无需更新。"
  fi

  rm -f "$PARAM_FILE"
}

function handleNginx() {
  echo "[Info] 复制nginx配置文件"
  mkdir -p /etc/nginx
  cp nginx.conf /etc/nginx/
  echo "[Info] 文件复制完成"
  echo "[Info] 重载nginx配置"
  if ! docker exec nginx nginx -s reload; then
    echo "nginx配置重载失败,休眠重试"
    sleep 10
    if ! docker exec nginx nginx -s reload; then
        echo "nginx配置重载重试失败,注意要手动验证docker exec nginx nginx -s reload"
        return
    fi
  fi
  echo "[Info] nginx配置重载完成"
}

########程序开始##########

#function handleDaemon() {
#  echo "[Info] 重启docker 先stop容器"
#  docker-compose stop
#  sleep 5
#  echo "[Info] 重启docker 再down容器"
#  docker-compose down
#  echo "[Info] 所有容器已删除"
#  echo "[Info] 重启docker 然后stop docker"
#  systemctl stop docker
#  echo "[Info] 复制docker配置文件"
#  mkdir -p /etc/docker
#  cp daemon.json /etc/docker/
#  echo "[Info] 文件复制完成，start docker"
#  systemctl start docker
#
#  echo "[Info] docker重启完成"
#  echo "[Info] 启动新容器"
#  cd "$fobrainDir"
#  docker-compose up -d --no-deps nginx mysql redis es consul
#  echo "等待30秒"
#  sleep 30
#  docker-compose up -d --no-deps fobrain merge-service
#  echo "[Info] 容器启动完成"
#  cd "$current_dir"
#}

# 重载docker配置
#if [ -e "daemon.json" ]; then
#  cd "$current_dir"
#  handleDaemon
#fi

#checkKernelParams

# 清理旧设备索引及数据表
#deleteOldIndex
# 数据备份
addRepoConfigToEs

addMysqlBinFileToContainer

if [ -e  "docker-compose.yaml" ]; then
   echo "处理docker-compose.yam"
   rm -rf /data/fobrain/docker-compose.yaml
   cp docker-compose.yaml /data/fobrain/docker-compose.yaml
fi

#amd的es镜像替换
if [ -e "elasticsearch-7.10.1.tar"  ]; then
  cd $current_dir
  echo "---开始load elasticsearch-7.10.1.tar"
  docker load -i elasticsearch-7.10.1.tar
  echo "---load elasticsearch-7.10.1.tar success"
fi

#arm的es镜像替换
if [ -e "elasticsearch-7.17.26.tar"  ]; then
  cd $current_dir
  echo "---开始load elasticsearch-7.17.26.tar"
  docker load -i elasticsearch-7.17.26.tar
  echo "---load elasticsearch-7.17.26.tar success"
fi

# 处理nginx配置
if [ -e "nginx.conf" ]; then
  cd "$current_dir"
  handleNginx
fi

# 查找名为 consul 的容器 ID
container_id=$(docker ps -q -f "name=consul")

# 如果容器存在，则去掉consul,上面的docker-compose.yaml已经去掉了consul依赖
if [ -n "$container_id" ]; then
  echo "停掉consul"
  cd $fobrainDir && docker-compose down  consul
fi

# 重载docker配置
#if [ -e "daemon.json" ]; then
#  cd "$current_dir"
#  handleDaemon
#fi