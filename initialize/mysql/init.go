package mysql

import (
	"database/sql"
	"errors"
	"fmt"
	"log"
	"reflect"
	"strings"
	"sync"
	"time"

	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/pkg/utils/common_logs"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"golang.org/x/exp/constraints"
	driverMysql "gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"gorm.io/gorm/schema"

	"fobrain/fobrain/common/localtime"
	"fobrain/pkg/cfg"
	"fobrain/pkg/utils"
)

type (
	BaseModel struct {
		*gorm.DB  `gorm:"-" json:"-"`
		Id        uint64         `gorm:"primaryKey;autoIncrement;comment:id" json:"id"`
		CreatedAt localtime.Time `gorm:"autoCreateTime;comment:创建时间" json:"created_at"`
		UpdatedAt localtime.Time `gorm:"autoUpdateTime;comment:更新时间" json:"updated_at"`
	}

	SoftDeleteModel struct {
		*gorm.DB  `gorm:"-" json:"-"`
		Id        uint64         `gorm:"primaryKey;autoIncrement;comment:id" json:"id"`
		CreatedAt localtime.Time `gorm:"column:created_at;comment:创建时间" json:"created_at"`
		UpdatedAt localtime.Time `gorm:"column:updated_at;comment:更新时间" json:"updated_at"`
		DeletedAt gorm.DeletedAt `gorm:"column:deleted_at;comment:删除事件" json:"deleted_at"`
	}

	HandleFunc func(tx *gorm.DB)

	// logWriter is an io.Writer that writes to zap.SugaredLogger.
	logWriter struct {
		logger *zap.Logger
	}
)

const logPrefix = "SQL "

const ProactiveTaskIdAdd = 1000000000

var once sync.Once

var singleInstance *gorm.DB

// List 查询列表
func List[T any](page, size int, opts ...HandleFunc) ([]T, int64, error) {
	indexName := reflect.ValueOf(new(T)).MethodByName("TableName").Call([]reflect.Value{})[0].String()
	query := GetDbClient().Table(indexName)
	for _, opt := range opts {
		opt(query)
	}
	var total int64
	if !IsPageAll(page, size) {
		if err := query.Count(&total).Error; err != nil {
			return nil, 0, err
		}
		query.Scopes(PageLimit(page, size))
	}
	var list = make([]T, 0)
	if err := query.Find(&list).Error; err != nil {
		return nil, 0, err
	}
	return list, total, nil
}

// Delete 删除数据
func Delete[T any](opts ...HandleFunc) error {
	model := new(T)
	indexName := reflect.ValueOf(new(T)).MethodByName("TableName").Call([]reflect.Value{})[0].String()
	query := GetDbClient().Table(indexName)
	for _, opt := range opts {
		opt(query)
	}
	return query.Delete(model).Error
}

// First 查询一条数据
func First[T any](opts ...HandleFunc) (T, error) {
	indexName := reflect.ValueOf(new(T)).MethodByName("TableName").Call([]reflect.Value{})[0].String()
	query := GetDbClient().Table(indexName)
	for _, opt := range opts {
		opt(query)
	}
	var item T
	err := query.First(&item).Error
	if err != nil {
		return item, err
	}
	return item, nil
}

// Write implements io.Writer.
func (w *logWriter) Write(p []byte) (n int, err error) {
	arr := strings.Split(string(p), "\n")
	if len(arr) >= 1 {
		paths := strings.Split(arr[0], "/")
		if len(paths) >= 2 {
			arr[0] = logPrefix + strings.Join(paths[len(paths)-2:], "/")
		}
	}
	w.logger.WithOptions(zap.WithCaller(false)).Info(strings.Join(arr, " "))
	return len(p), nil
}

func GetDbClient() *gorm.DB {
	// 如果是执行单元测试，则不走单例
	if testcommon.IsTest() {
		mysqlMock := testcommon.GetMysqlMock()
		singleInstance = mysqlMock.MockGorm
		return singleInstance
	}

	if singleInstance == nil {
		once.Do(func() { singleInstance = initMysql(cfg.LoadMysql()) })
	}
	return singleInstance
}

// ClearMysqlMock
// 用于单元测试中，来清理每个 case 执行后对 singleInstance 值的回收操作。
func ClearMysqlMock() {
	singleInstance = nil
}

// InitMysql 初始化数据库并产生数据库全局变量
func initMysql(con cfg.MySql) *gorm.DB {
	dnsString := getDNS(con)
	wait := time.Second * 8
	var err error
	var db *gorm.DB
	for i := 0; i < 5; i++ {
		db, err = gorm.Open(driverMysql.New(driverMysql.Config{
			DSN:                       dnsString,
			DefaultStringSize:         256,   // string 类型字段的默认长度                                                                            // string 类型字段的默认长度
			DisableDatetimePrecision:  true,  // 禁用 datetime 精度，MySQL 5.6 之前的数据库不支持
			DontSupportRenameIndex:    true,  // 重命名索引时采用删除并新建的方式，MySQL 5.7 之前的数据库和 MariaDB 不支持重命名索引
			DontSupportRenameColumn:   true,  // 用 `change` 重命名列，MySQL 8 之前的数据库和 MariaDB 不支持重命名列
			SkipInitializeWithVersion: false, //根据版本自动配置                                                                            // 根据当前 MySQL 版本自动配置
		}), &gorm.Config{
			PrepareStmt:                              true,                // open sql preparestmt
			Logger:                                   getMysqlLogger(con), // 日志
			DisableForeignKeyConstraintWhenMigrating: true,                // 外键约束
			//SkipDefaultTransaction: true, // 禁用默认事务（提高运行速度）
			NamingStrategy: schema.NamingStrategy{
				// 使用单数表名，启用该选项，此时，`User` 的表名应该是 `user`
				SingularTable: true,
			},
		})
		if err == nil {
			break
		}

		time.Sleep(wait)
		wait *= 2
		continue
	}
	if err != nil {
		panic(fmt.Sprintf("gorm.Open err:%v \n", err))
	}

	sqlDB, _ := db.DB()
	// Enable Logger, show detailed log
	sqlDB.SetMaxIdleConns(50)                  // 设置空闲连接池中连接的最大数量
	sqlDB.SetMaxOpenConns(10)                  // 设置打开数据库连接的最大数量。
	sqlDB.SetConnMaxLifetime(10 * time.Second) // 设置了连接可复用的最大时间。
	return db
}

func getMysqlLogger(con cfg.MySql) logger.Interface {
	logMode := logger.Warn
	logLevel, err := zapcore.ParseLevel(con.LogLevel)
	if err != nil {
		panic(err)
	}
	switch logLevel {
	case zapcore.ErrorLevel, zapcore.FatalLevel, zapcore.PanicLevel, zapcore.DPanicLevel:
		logMode = logger.Error
	case zapcore.WarnLevel, zapcore.InfoLevel:
		logMode = logger.Warn
	case zapcore.DebugLevel:
		logMode = logger.Info
	}
	newLogger := logger.New(
		log.New(&logWriter{logger: common_logs.InitLogger(cfg.LoadLogger()).Logger}, logPrefix, 0), // io writer
		logger.Config{
			SlowThreshold:             time.Second * time.Duration(con.SlowTime), // Slow SQL threshold
			LogLevel:                  logMode,                                   // gorm日志模式：silent 可选 Silent，Error，Warn，Info
			Colorful:                  false,
			IgnoreRecordNotFoundError: true,
		},
	)
	return newLogger
}

func getDNS(opt cfg.MySql) string {
	return fmt.Sprintf("%s:%s@(%s:%d)/%s?charset=%s&parseTime=True&loc=Local",
		opt.UserName,
		opt.Password,
		opt.Address,
		opt.Port,
		opt.Database,
		opt.CharSet,
	)
}

func fieldProcess(f string) string {
	f = strings.ReplaceAll(f, "`", "")
	l := strings.Split(f, ".")
	for i := range l {
		l[i] = "`" + l[i] + "`"
	}
	return strings.Join(l, ".")
}

func IsNotFound(err error) bool {
	return errors.Is(err, gorm.ErrRecordNotFound)
}

func PageProcess(page, size int) (int, int) {
	page = utils.If(page <= 0, 0, page)
	size = utils.If(size <= 0, 0, size)

	return page, size
}

func PageLimit(page, size int) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		offset := (page - 1) * size
		offset = utils.If(offset <= 0, 0, offset)
		return db.Offset(offset).Limit(size)
	}
}

func IsPageAll(page, size int) bool {
	return page == 0 && size == 0
}

func WithSelect(columns ...string) HandleFunc {
	cls := make([]string, 0, len(columns))
	for _, v := range columns {
		cls = append(cls, strings.Split(v, ",")...)
	}
	return func(tx *gorm.DB) {
		tx.Select(cls)
	}
}

func WithValuesIn[T any](column string, values []T) HandleFunc {
	return func(tx *gorm.DB) {
		if len(values) > 0 {
			column = fieldProcess(column)
			tx.Where(column+" IN (?)", values)
		}
	}
}

func WithValueNotIn[T any](column string, values []T) HandleFunc {
	return func(tx *gorm.DB) {
		if len(values) > 0 {
			column = fieldProcess(column)
			tx.Where(column+" NOT IN (?)", values)
		}
	}
}

func WithId[T constraints.Integer | ~string](value T) HandleFunc {
	return func(tx *gorm.DB) {
		tx.Where("`id` = ?", value)
	}
}

func WithColumnValue(column string, value any) HandleFunc {
	return func(tx *gorm.DB) {
		column = fieldProcess(column)
		tx.Where(column+" = ?", value)
	}
}

func WithColumnNotValue(column string, value any) HandleFunc {
	return func(tx *gorm.DB) {
		column = fieldProcess(column)
		tx.Where(column+" != ?", value)
	}
}

func WithColumnNull(column string) HandleFunc {
	return func(tx *gorm.DB) {
		column = fieldProcess(column)
		tx.Where(column + " IS NULL")
	}
}

func WithColumnNotNull(column string) HandleFunc {
	return func(tx *gorm.DB) {
		column = fieldProcess(column)
		tx.Where(column + " IS NOT NULL")
	}
}

func WithWhere(conditions string, value ...any) HandleFunc {
	return func(tx *gorm.DB) {
		if len(conditions) > 0 {
			tx.Where(conditions, value...)
		}
	}
}

func WithOrWhere(conditions string, value ...any) HandleFunc {
	return func(tx *gorm.DB) {
		if len(conditions) > 0 {
			tx.Or(conditions, value...)
		}
	}
}

func WithBetweenAt(column string, values []string) HandleFunc {
	values = utils.ListDistinctNonZero(values)
	return func(tx *gorm.DB) {
		column = fieldProcess(column)
		if len(values) == 1 {
			tx.Where(column+" >= ?", values[0])
		}
		if len(values) == 2 {
			tx.Where(fmt.Sprintf(`%s >= ? and %s <= ?`, column, column), values[0], values[1])
		}
	}
}

func WithBetween(column string, begin, end any) HandleFunc {
	return func(tx *gorm.DB) {
		column = fieldProcess(column)
		tx.Where(column+" BETWEEN ? AND ?", begin, end)
	}
}

func WithLike(column, value string) HandleFunc {
	return func(tx *gorm.DB) {
		if value != "" {
			column = fieldProcess(column)
			tx.Where(column+" LIKE ?", "%"+value+"%")
		}
	}
}

func WithLRLike(column, value string) HandleFunc {
	return func(tx *gorm.DB) {
		if value != "" {
			column = fieldProcess(column)
			tx.Where(column+" LIKE ?", "%"+value+"%")
		}
	}
}

func WithOrder(order string) HandleFunc {
	return func(tx *gorm.DB) {
		if len(order) > 0 {
			tx.Order(order)
		}
	}
}

// WithGT Greater Than (column > value)
func WithGT[T comparable](column string, value T) HandleFunc {
	return func(tx *gorm.DB) {
		tx.Where(column+" > ?", value)
	}
}

// WithLT Greater Than (column < value)
func WithLT[T comparable](column string, value T) HandleFunc {
	return func(tx *gorm.DB) {
		tx.Where(column+" < ?", value)
	}
}

func SqlString(v *sql.NullString) string {
	if v == nil {
		return ""
	}
	return v.String
}
