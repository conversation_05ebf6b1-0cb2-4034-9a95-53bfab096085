package mysql

import (
	testcommon "fobrain/fobrain/tests/common_test"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"
)

func TestBuildQuery(t *testing.T) {
	//初始化的gorm数据库连接
	mockDB := testcommon.GetMysqlMock()
	defer mockDB.Close()
	t.Run("TestBuildQuery", func(t *testing.T) {
		var db = mockDB.MockGorm
		db = db.Session(&gorm.Session{DryRun: true, SkipDefaultTransaction: true, NewDB: true})
		qb := buildComplexQuery(123, "/api", "GET")
		queryDB := BuildQuery(db, qb)

		var menus []interface{}
		queryDB = queryDB.Find(&menus)
		t.Log(queryDB.Statement.SQL.String(), queryDB.Statement.Vars)
		err := queryDB.Error
		assert.Nil(t, err)
		assert.NotNil(t, queryDB)
	})
	t.Run("TestBuildQueryWithJoins", func(t *testing.T) {
		var db = mockDB.MockGorm
		BuildQuery(db, &QueryBuilder{
			Table: "menus",
			Joins: []JoinCondition{
				{
					Table: "roles",
					On:    "menus.id = roles.menu_id",
					Type:  InnerJoin,
				},
			},
			Select: []string{"id", "name"},
			Where: []Condition{RawCondition{
				Query: "id=1",
			}, NotInCondition{Field: "id", Values: []interface{}{1, 2, 3}}},
		})
	})
}

type MenuTest struct {
	BaseDSL[MenuTest]
	BaseTimestampDSL
	Name     string `gorm:"type:varchar(255);comment:菜单名称" json:"name"`
	Path     string `gorm:"type:varchar(255);comment:菜单路径" json:"path"`
	Method   string `gorm:"type:varchar(255);comment:菜单方法" json:"method"`
	ParentId uint64 `gorm:"type:bigint;comment:父级菜单id" json:"parent_id"`
}

func (m *MenuTest) TableName() string {
	return "menus"
}
func TestFindByID(t *testing.T) {
	mockDB := testcommon.GetMysqlMock()
	defer mockDB.Close()
	// SELECT * FROM `menus` WHERE `id` = ? ORDER BY `menus`.`id` LIMIT 1
	mockDB.ExpectQuery("SELECT * FROM `menus` WHERE `id` = ? ORDER BY `menus`.`id` LIMIT 1").
		WithArgs(1).
		WillReturnRows(mockDB.NewRows([]string{"id", "name", "path", "method", "parent_id"}).
			AddRow(1, "菜单1", "/api/menu1", "GET", 0))
	d := NewDSL[MenuTest]()
	_, err := d.FindByID(1)
	assert.NoError(t, err)
}
func TestOne(t *testing.T) {
	mockDB := testcommon.GetMysqlMock()
	defer mockDB.Close()
	// SELECT * FROM `menus` WHERE `id` = ? LIMIT 1
	mockDB.ExpectQuery("SELECT * FROM `menus` WHERE `id` = ? LIMIT 1").
		WithArgs(1).
		WillReturnRows(mockDB.NewRows([]string{"id", "name", "path", "method", "parent_id"}).
			AddRow(1, "菜单1", "/api/menu1", "GET", 0))
	d := NewDSL[MenuTest]()
	_, err := d.One(&QueryBuilder{Where: []Condition{CompareCond{Field: "id", Operator: "=", Value: 1}}})
	assert.NoError(t, err)
}
func TestQuery(t *testing.T) {
	mockDB := testcommon.GetMysqlMock()
	defer mockDB.Close()
	// SELECT * FROM `menus` WHERE `id` = ?
	mockDB.ExpectQuery("SELECT * FROM `menus` WHERE `id` = ?").
		WithArgs(1).
		WillReturnRows(mockDB.NewRows([]string{"id", "name", "path", "method", "parent_id"}).
			AddRow(1, "菜单1", "/api/menu1", "GET", 0))
	d := NewDSL[MenuTest]()
	_, err := d.Query(&QueryBuilder{Where: []Condition{CompareCond{Field: "id", Operator: "=", Value: 1}}})
	assert.NoError(t, err)
}
func TestTotal(t *testing.T) {
	mockDB := testcommon.GetMysqlMock()
	defer mockDB.Close()
	// SELECT count(*) FROM `menus` WHERE `id` = ?
	mockDB.ExpectQuery("SELECT count(*) FROM `menus` WHERE `id` = ?").
		WithArgs(1).
		WillReturnRows(mockDB.NewRows([]string{"count(*)"}).
			AddRow(1))
	d := NewDSL[MenuTest](mockDB.MockGorm)
	_, err := d.Total(&QueryBuilder{Where: []Condition{CompareCond{Field: "id", Operator: "=", Value: 1}}})
	assert.NoError(t, err)
}
func TestUpdateById(t *testing.T) {
	mockDB := testcommon.GetMysqlMock()
	defer mockDB.Close()
	mockDB.ExpectBegin()
	// UPDATE `menus` SET `name`=? WHERE `id` = ?
	mockDB.ExpectExec("UPDATE `menus` SET `name`=?,`updated_at`=? WHERE `id` = ?").
		WithArgs("菜单1", sqlmock.AnyArg(), 1).WillReturnResult(sqlmock.NewResult(1, 1))
	mockDB.ExpectCommit()
	d := NewDSL[MenuTest](mockDB.MockGorm)
	_, err := d.UpdateByID(1, map[string]interface{}{
		"name": "菜单1",
	})
	assert.NoError(t, err)
}
func TestUpdate(t *testing.T) {
	mockDB := testcommon.GetMysqlMock()
	defer mockDB.Close()
	mockDB.ExpectBegin()
	// UPDATE `menus` SET `name`=? WHERE `id` = ?
	mockDB.ExpectExec("UPDATE `menus` SET `name`=?,`updated_at`=? WHERE `id` = ?").
		WithArgs("菜单1", sqlmock.AnyArg(), 1).WillReturnResult(sqlmock.NewResult(1, 1))
	mockDB.ExpectCommit()
	d := NewDSL[MenuTest](mockDB.MockGorm)
	_, err := d.Update(&QueryBuilder{Where: []Condition{CompareCond{Field: "id", Operator: "=", Value: 1}}}, map[string]interface{}{
		"name": "菜单1",
	})
	assert.NoError(t, err)
}
func TestSoftDelete(t *testing.T) {
	mockDB := testcommon.GetMysqlMock()
	defer mockDB.Close()
	mockDB.ExpectBegin()
	// UPDATE `menus` SET `deleted_at`=? WHERE `id` = ?
	mockDB.ExpectExec("UPDATE `menus` SET `deleted_at`=?,`updated_at`=? WHERE `id` = ?").
		WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), 1).WillReturnResult(sqlmock.NewResult(1, 1))
	mockDB.ExpectCommit()
	d := NewDSL[MenuTest](mockDB.MockGorm)
	_, err := d.SoftDelete(&QueryBuilder{Where: []Condition{CompareCond{Field: "id", Operator: "=", Value: 1}}})
	assert.NoError(t, err)
}
func TestRawDelete(t *testing.T) {
	mockDB := testcommon.GetMysqlMock()
	defer mockDB.Close()
	mockDB.ExpectBegin()
	// DELETE FROM `menus` WHERE `id` = ?
	mockDB.ExpectExec("DELETE FROM `menus` WHERE `id` = ?").
		WithArgs(1).WillReturnResult(sqlmock.NewResult(1, 1))
	mockDB.ExpectCommit()
	d := NewDSL[MenuTest](mockDB.MockGorm)
	_, err := d.DangerousDelete(&QueryBuilder{Where: []Condition{CompareCond{Field: "id", Operator: "=", Value: 1}}})
	assert.NoError(t, err)
}
func TestCreate(t *testing.T) {
	mockDB := testcommon.GetMysqlMock()
	defer mockDB.Close()
	mockDB.ExpectBegin()
	// DELETE FROM `menus` WHERE `id` = ?
	mockDB.ExpectExec("INSERT INTO `menus` (`created_at`,`updated_at`,`name`,`path`,`method`,`parent_id`,`id`) VALUES (?,?,?,?,?,?,?)").
		WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), "菜单1", "/api/menu1", "GET", 0, 1).WillReturnResult(sqlmock.NewResult(1, 1))
	mockDB.ExpectCommit()
	d := NewDSL[MenuTest](mockDB.MockGorm)
	_, err := d.Create(MenuTest{
		BaseDSL: BaseDSL[MenuTest]{
			Id: 1,
		},
		Name:     "菜单1",
		Path:     "/api/menu1",
		Method:   "GET",
		ParentId: 0,
	})
	assert.NoError(t, err)
}
func TestBatchCreate(t *testing.T) {
	mockDB := testcommon.GetMysqlMock()
	defer mockDB.Close()
	mockDB.ExpectBegin()
	// DELETE FROM `menus` WHERE `id` = ?
	mockDB.ExpectExec("INSERT INTO `menus` (`created_at`,`updated_at`,`name`,`path`,`method`,`parent_id`,`id`) VALUES (?,?,?,?,?,?,?)").
		WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), "菜单1", "/api/menu1", "GET", 0, 1).WillReturnResult(sqlmock.NewResult(1, 1))
	mockDB.ExpectCommit()
	d := NewDSL[MenuTest](mockDB.MockGorm)
	_, err := d.BatchCreate([]MenuTest{{
		BaseDSL: BaseDSL[MenuTest]{
			Id: 1,
		},
		Name:     "菜单1",
		Path:     "/api/menu1",
		Method:   "GET",
		ParentId: 0,
	}})
	assert.NoError(t, err)
}
func TestPage(t *testing.T) {
	mockDB := testcommon.GetMysqlMock()
	defer mockDB.Close()

	mockDB.ExpectQuery("SELECT count(*) FROM `menus` WHERE `id` = ?").
		WithArgs(1).
		WillReturnRows(mockDB.NewRows([]string{"count(*)"}).
			AddRow(1))
	// SELECT * FROM `menus` WHERE `id` = ? LIMIT 1
	mockDB.ExpectQuery("SELECT * FROM `menus` WHERE `id` = ? LIMIT 10").
		WithArgs(1).
		WillReturnRows(mockDB.NewRows([]string{"id", "name", "path", "method", "parent_id"}).
			AddRow(1, "菜单1", "/api/menu1", "GET", 0))

	d := NewDSL[MenuTest](mockDB.MockGorm)
	_, _, err := d.Page(&QueryBuilder{Limit: 10, Offset: 0, Where: []Condition{CompareCond{Field: "id", Operator: "=", Value: 1}}})
	assert.NoError(t, err)
}
func buildComplexQuery(userID uint, path, method string) *QueryBuilder {
	// 构建最内层子查询：SELECT `user_id` FROM `users_roles` WHERE `user_id` =?
	subQueryInner := &QueryBuilder{
		Table: "users_roles",
		Where: []Condition{
			CompareCond{"user_id", "=", userID},
		},
		Select: []string{"user_id"},
	}

	// 构建中层子查询：SELECT `menu_id` FROM `roles_menus` WHERE `role_id` IN (...)
	subQueryMiddle := &QueryBuilder{
		Table: "roles_menus",
		Where: []Condition{
			InCondition{
				Field:  "role_id",
				Values: subQueryInner,
			},
		},
		Select: []string{"menu_id"},
	}

	// 构建主查询
	return &QueryBuilder{
		Table: "menus",
		Where: []Condition{
			InCondition{
				Field:  "id",
				Values: subQueryMiddle,
			},
			CompareCond{"path", "=", path},
			CompareCond{"method", "=", method},
			OrConditions{
				Conditions: []Condition{
					CompareCond{"state", "=", 1},
					CompareCond{"state", "=", 2},
				},
			},
		},
		OrderBy: []string{"id DESC"},
		Limit:   10, // 示例分页参数
		Offset:  0,  // 示例分页参数
	}
}
