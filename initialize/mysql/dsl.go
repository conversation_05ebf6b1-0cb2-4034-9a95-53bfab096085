package mysql

import (
	"fmt"
	"fobrain/fobrain/common/localtime"
	"reflect"
	"strings"

	"gorm.io/gorm"
)

// buildSubQuery 构造子语句
func buildSubQuery(tx *gorm.DB, qb *QueryBuilder) *gorm.DB {
	db := tx.Session(&gorm.Session{DryRun: true, SkipDefaultTransaction: true, NewDB: true})
	return BuildQuery(db, qb)
}

// processConditions 处理where条件列表
func processConditions(tx *gorm.DB, conds []Condition) *gorm.DB {
	for _, cond := range conds {
		tx = processSingleCondition(tx, cond)
	}
	return tx
}

// processSingleCondition 处理单个条件
func processSingleCondition(tx *gorm.DB, cond Condition) *gorm.DB {
	switch c := cond.(type) {
	case RawCondition:
		return tx.Where(c.Query, c.Args...)
	case CompareCond:
		return tx.Where(fmt.Sprintf("`%s` %s ?", c.<PERSON>, c.Operator), c.Value)
	case BetweenCondition:
		return tx.Where(fmt.Sprintf("`%s` BETWEEN ? AND ?", c.<PERSON>), c.Start, c.End)
	case InCondition:
		switch values := c.Values.(type) {
		case []interface{}, []int, []int8, []uint, []uint64, []uint8, []uint16, []uint32, []int16, []int32, []int64, []string:
			return tx.Where(fmt.Sprintf("`%s` IN (?)", c.Field), values)
		case *QueryBuilder:
			subTx := buildSubQuery(tx, values)
			stmt := subTx.Find(nil).Statement
			query := stmt.SQL.String()
			return tx.Where(fmt.Sprintf("`%s` IN (%s)", c.Field, query), stmt.Vars...)
		}
	case NotInCondition:
		switch values := c.Values.(type) {
		case []interface{}, []int, []int8, []uint, []uint64, []uint8, []uint16, []uint32, []int16, []int32, []int64, []string:
			return tx.Where(fmt.Sprintf("`%s` NOT IN (?)", c.Field), values)
		case *QueryBuilder:
			subTx := buildSubQuery(tx, values)
			stmt := subTx.Find(nil).Statement
			query := stmt.SQL.String()
			return tx.Where(fmt.Sprintf("`%s` NOT IN (%s)", c.Field, query), stmt.Vars...)
		}
	case ExistsCondition:
		subTx := buildSubQuery(tx, c.SubQuery)
		stmt := subTx.Select("1").Limit(1).Find(nil).Statement
		query := stmt.SQL.String()
		if c.Not {
			return tx.Where(fmt.Sprintf("NOT EXISTS (%s)", query), stmt.Vars...)
		}
		return tx.Where(fmt.Sprintf("EXISTS (%s)", query), stmt.Vars...)
	case SubQueryCondition:
		subTx := buildSubQuery(tx, c.Query)
		stmt := subTx.Find(nil).Statement
		query := stmt.SQL.String()
		return tx.Where(fmt.Sprintf("`%s` %s (%s)", c.Field, c.Operator, query), stmt.Vars...)
	case OrConditions:
		var sqlFragments []string
		var allArgs []interface{}
		for _, orCond := range c.Conditions {
			subTx := tx.Session(&gorm.Session{DryRun: true, SkipDefaultTransaction: true, NewDB: true})
			subTx = processSingleCondition(subTx, orCond)
			for _, s := range subTx.Statement.Clauses {
				s.Name = "" // 处理掉WHERE前缀
				s.Build(subTx.Statement)
			}
			stmt := subTx.Statement
			sql, args := stmt.SQL.String(), stmt.Vars
			sqlFragments = append(sqlFragments, sql)
			allArgs = append(allArgs, args...)
		}
		joinedSQL := strings.Join(sqlFragments, " OR ")
		return tx.Where(joinedSQL, allArgs...)
	}
	return tx
}

// BuildQuery 构造查询语句
func BuildQuery(db *gorm.DB, qb *QueryBuilder) *gorm.DB {
	tx := db
	if qb == nil {
		return tx
	}
	if len(qb.Table) > 0 {
		tx = tx.Table(qb.Table)
	}

	// 处理 SELECT
	if len(qb.Select) > 0 {
		tx = tx.Select(qb.Select)
	}

	// 处理JOIN
	for _, join := range qb.Joins {
		joinClause := fmt.Sprintf("%s JOIN", join.Type)
		var args = join.Args
		if args == nil {
			args = make([]interface{}, 0)
		}
		switch t := join.Table.(type) {
		case string:
			if len(join.Alias) > 0 {
				joinClause += fmt.Sprintf(" `%s` AS %s", t, join.Alias)
			} else {
				joinClause += fmt.Sprintf(" `%s`", t)
			}
		case *QueryBuilder:
			subTx := BuildQuery(tx.Session(&gorm.Session{DryRun: true, SkipDefaultTransaction: true, NewDB: true}), t)
			joinClause += " (" + subTx.Statement.SQL.String() + ") " + join.Alias
		}
		switch on := join.On.(type) {
		case string:
			joinClause += " ON " + on
		case *QueryBuilder:
			subTx := BuildQuery(tx.Session(&gorm.Session{DryRun: true, SkipDefaultTransaction: true, NewDB: true}), on)
			joinClause += " ON (" + subTx.Statement.SQL.String() + ")"
			args = append(args, subTx.Statement.Vars...)
		}
		tx = tx.Joins(joinClause, args...)
	}

	tx = processConditions(tx, qb.Where)

	// 处理 ORDER BY
	if len(qb.OrderBy) > 0 {
		tx = tx.Order(strings.Join(qb.OrderBy, ", "))
	}

	// 处理 GROUP BY
	if len(qb.GroupBy) > 0 {
		tx = tx.Group(strings.Join(qb.GroupBy, ", "))
	}

	// 处理分页
	if qb.Limit > 0 {
		tx = tx.Limit(qb.Limit)
	}
	if qb.Offset > 0 {
		tx = tx.Offset(qb.Offset)
	}

	return tx
}

func (d *BaseDSL[T]) Query(qb *QueryBuilder) ([]T, error) {
	db := d.GetDbClient()
	q := BuildQuery(db, qb)
	var result []T
	if qb == nil || len(qb.Table) == 0 {
		q.Model(&result)
	}
	if err := q.Find(&result).Error; err != nil {
		return nil, err
	}
	return result, nil
}
func (d *BaseDSL[T]) One(qb *QueryBuilder) (T, error) {
	qb.Limit = 1
	db := d.GetDbClient()
	q := BuildQuery(db, qb)
	var result T
	if qb == nil || len(qb.Table) == 0 {
		q.Model(&result)
	}
	if err := q.Take(&result).Error; err != nil {
		return result, err
	}
	return result, nil
}
func (d *BaseDSL[T]) FindByID(id uint64) (T, error) {
	db := d.GetDbClient()
	var result T
	if err := db.Model(&result).Where("`id` = ?", id).First(&result).Error; err != nil {
		return result, err
	}
	return result, nil
}

// Total 查询总数
func (d *BaseDSL[T]) Total(qb *QueryBuilder) (int64, error) {
	db := d.GetDbClient()
	qb.Limit = 0
	qb.Offset = 0
	q := BuildQuery(db, qb)
	if len(qb.Table) == 0 {
		var t T
		q = q.Model(&t)
	}
	var count int64
	if err := q.Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}

// Page 分页查询
func (d *BaseDSL[T]) Page(qb *QueryBuilder) ([]T, int64, error) {
	// limit the page size, large page size may cause performance issues
	if qb.Limit <= 0 || qb.Limit > 10000 {
		qb.Limit = 10
	}
	db := d.GetDbClient()
	q := BuildQuery(db, qb)
	var result []T
	if len(qb.Table) == 0 {
		var t T
		q = q.Model(&t)
	}
	count, err := d.Total(qb)
	if err != nil {
		return nil, 0, err
	}
	if err = q.Find(&result).Error; err != nil {
		return nil, 0, err
	}
	return result, count, nil
}
func (d *BaseDSL[T]) UpdateByID(id uint64, data interface{}) (int64, error) {
	db := d.GetDbClient()
	var t T
	result := db.Model(&t).Where("`id` = ?", id).Updates(data)
	if result.Error != nil {
		return 0, result.Error
	}
	return result.RowsAffected, nil
}
func (d *BaseDSL[T]) Update(qb *QueryBuilder, data interface{}) (int64, error) {
	db := d.GetDbClient()
	q := BuildQuery(db, qb)
	if qb == nil || len(qb.Table) == 0 {
		q = q.Model(new(T))
	}
	result := q.Updates(data)
	if result.Error != nil {
		return 0, result.Error
	}
	return result.RowsAffected, nil
}
func (d *BaseDSL[T]) SoftDelete(qb *QueryBuilder) (int64, error) {
	return d.Update(qb, map[string]interface{}{
		"deleted_at": localtime.Now(),
	})
}
func (d *BaseDSL[T]) DangerousDelete(qb *QueryBuilder) (int64, error) {
	if qb == nil || len(qb.Where) == 0 {
		return 0, fmt.Errorf("delete without where clause is dangerous")
	}
	db := d.GetDbClient()
	q := BuildQuery(db, qb)
	if len(qb.Table) == 0 {
		q.Model(new(T))
	}
	result := q.Delete(new(T))
	if result.Error != nil {
		return 0, result.Error
	}
	return result.RowsAffected, nil
}
func (d *BaseDSL[T]) Create(data T) (T, error) {
	db := d.GetDbClient()
	var result *gorm.DB
	typ := reflect.TypeOf(data)
	if typ.Kind() != reflect.Pointer {
		result = db.Create(&data)
	} else {
		result = db.Create(data)
	}
	if result.Error != nil {
		return data, result.Error
	}
	return data, nil
}

func (d *BaseDSL[T]) BatchCreate(data []T) ([]T, error) {
	db := d.GetDbClient()
	var result *gorm.DB
	typ := reflect.TypeOf(data)
	if typ.Kind() != reflect.Pointer {
		result = db.Create(&data)
	} else {
		result = db.Create(data)
	}
	if result.Error != nil {
		return data, result.Error
	}
	return data, nil
}
func (d *BaseDSL[T]) GetDbClient() *gorm.DB {
	if d.DB != nil {
		return d.DB
	} else {
		return GetDbClient()
	}
}

func NewDSL[T any](args ...any) *BaseDSL[T] {
	dsl := &BaseDSL[T]{}
	if len(args) > 0 {
		for _, arg := range args {
			switch t := arg.(type) {
			case *gorm.DB:
				dsl.DB = t
			}
		}
	}
	return dsl
}
