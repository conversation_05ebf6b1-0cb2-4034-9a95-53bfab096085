package engine

import (
	"fobrain/fobrain/common/middleware"
	"fobrain/fobrain/config"
	"sync"

	"github.com/gin-gonic/gin"
)

var once sync.Once

var engine *gin.Engine

func GetEngine() *gin.Engine {
	if engine == nil {
		once.Do(func() { engine = initEngine() })
	}
	return engine
}

func initEngine() *gin.Engine {
	// 设置运行模式
	if config.Get().Env == "production" {
		gin.SetMode(gin.ReleaseMode)
	} else if config.Get().Env == "testing" {
		gin.SetMode(gin.TestMode)
	} else {
		gin.SetMode(gin.DebugMode)
	}
	ginEngine := gin.New()
	// 开启http2
	ginEngine.UseH2C = true
	ginEngine.MaxMultipartMemory = config.Get().UploadMaxSize * 1024 * 1024
	// 404 处理
	//ginEngine.NoRoute(func(c *gin.Context) { _ = response.Fail(c) })
	// 加载中间件
	middleware.InitMiddleware(ginEngine)
	return ginEngine
}
