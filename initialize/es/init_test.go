package es

import (
	"fobrain/fobrain/common/license"
	testcommon "fobrain/fobrain/tests/common_test"
	"testing"

	"github.com/agiledragon/gomonkey/v2"

	"github.com/olivere/elastic/v7"
	"github.com/stretchr/testify/assert"
)

type MockBaseModel struct {
	Id   string `json:"id"`
	Name string `json:"name"`
}

func (m *MockBaseModel) IndexName() string {
	return "test_index"
}

func TestGetById_HappyPath(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	mockServer.Register("/test_index/_doc/1", &elastic.GetResult{
		Source: []byte(`{"id": "1", "name": "test"}`),
		Found:  true,
	})

	// Call the function
	result, err := GetById[MockBaseModel]("1")

	// Assert the result
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, "test", result.Name)
}

func TestGetById_NoResults(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	mockServer.Register("/test_index/_doc/1", &elastic.GetResult{
		Found: false,
	})

	// Call the function
	result, err := GetById[MockBaseModel]("1")

	// Assert the result
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "record not found")
	assert.Nil(t, result)
}

func TestGetById_Error(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	mockServer.Register("/test_index/_doc/1", &elastic.GetResult{
		Error: &elastic.ErrorDetails{
			Type: "search_phase_execution_exception",
		},
	})

	// Call the function
	result, err := GetById[MockBaseModel]("1")

	// Assert the result
	assert.Error(t, err)
	assert.Nil(t, result)
}

func TestIsLicenseAssetLimitReached(t *testing.T) {
	getCountMock := gomonkey.ApplyFunc(GetCount, func(indexName string, query elastic.Query) (int64, error) {
		return 1, nil
	})
	getLicenseMock := gomonkey.ApplyFunc(license.GetLicense().GetLicenseAssetCount, func() (int, error) {
		return 1000, nil
	})
	defer getCountMock.Reset()
	defer getLicenseMock.Reset()
	result, err := IsLicenseAssetLimitReached("asset", true)
	if err != nil {
		t.Fatal(err)
	}
	assert.NotNil(t, result)
}

func TestIsLicenseProcessAssetLimitReached(t *testing.T) {
	getCountMock := gomonkey.ApplyFunc(GetCount, func(indexName string, query elastic.Query) (int64, error) {
		return 1, nil
	})
	getLicenseMock := gomonkey.ApplyFunc(license.GetLicense().GetLicenseAssetCount, func() (int, error) {
		return 1000, nil
	})
	getLicenseAssetsNumMock := gomonkey.ApplyFunc(license.GetLicense().GetDataSources, func() ([]string, error) {
		return []string{"foeye", "foradar-saas"}, nil
	})
	defer getCountMock.Reset()
	defer getLicenseMock.Reset()
	defer getLicenseAssetsNumMock.Reset()
	result, err := IsLicenseProcessAssetLimitReached("process_asset", true)
	if err != nil {
		t.Fatal(err)
	}
	assert.NotNil(t, result)
}

func TestTmpSortByWrongDate(t *testing.T) {
	assert.NotPanics(t, func() {
		got := TmpSortByWrongDate("updated_at", false)
		assert.NotNil(t, got)
	})
}

func TestGetCount(t *testing.T) {
	mockEs := testcommon.NewMockServer()
	defer mockEs.Close()

	// 模拟 Count API 的响应，返回 count 为 5
	mockEs.Register("/asset/_count", map[string]interface{}{
		"count": 5,
		"_shards": map[string]interface{}{
			"total":      1,
			"successful": 1,
			"skipped":    0,
			"failed":     0,
		},
	})

	query := elastic.NewBoolQuery().Must(elastic.NewTermQuery("field", "value"))
	count, err := GetCount("asset", query)

	assert.NoError(t, err)
	assert.Equal(t, int64(5), count)
}
