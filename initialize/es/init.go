package es

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"fobrain/fobrain/common/license"
	testcommon "fobrain/fobrain/tests/common_test"
	"io"
	"net/http"
	"reflect"
	"sync"
	"time"

	errors2 "github.com/pkg/errors"

	"github.com/olivere/elastic/v7"

	"fobrain/fobrain/common/localtime"
	"fobrain/pkg/cfg"
)

var (
	once           sync.Once
	singleInstance *elastic.Client
)

type (
	// baseModelInterface 基础模型接口
	baseModelInterface interface {
		GetClient() *SafeClient
		IndexName() string
	}
	// BaseModel 基础模型
	BaseModel struct {
		baseModelInterface
	}
)

func (b *BaseModel) GetClient() *SafeClient {
	return GetEsClient()
}
func (b *BaseModel) GobEncode() ([]byte, error) {
	// 返回一个空的 byte slice，告诉 gob 我自己什么也不保存
	return []byte{}, nil
}

func (b *BaseModel) GobDecode(data []byte) error {
	// data 肯定为空，也不做任何事
	return nil
}

// Delete 删除
func Delete[T any](query elastic.Query) error {
	indexName := reflect.ValueOf(new(T)).MethodByName("IndexName").Call([]reflect.Value{})[0].String()
	if query == nil {
		return errors.New("query is nil")
	}
	_, err := GetEsClient().DeleteByQuery().Index(indexName).
		Query(query).Slices("auto").Pretty(true).Refresh("true").Do(context.TODO())
	if err != nil {
		return err
	}
	return nil
}

// First 查询单个
func First[T any](query *elastic.BoolQuery, sorts []elastic.Sorter, fields ...string) (*T, error) {
	indexName := reflect.ValueOf(new(T)).MethodByName("IndexName").Call([]reflect.Value{})[0].String()
	var data *T
	search := GetEsClient().Search(indexName)
	if len(fields) != 0 {
		search.FetchSourceContext(elastic.NewFetchSourceContext(true).Include(fields...))
	}
	if len(sorts) != 0 {
		search = search.SortBy(sorts...)
	}
	result, err := search.Size(1).Query(query).Do(context.TODO())
	if err == io.EOF {
		return nil, nil
	}
	if err != nil {
		return nil, err
	}
	// 检查是否有结果
	if result.TotalHits() > 0 {
		// 索引中的第一个文档
		data = ParseHitsValue[T](result.Hits.Hits)[0]
	}
	return data, nil
}

func GetById[T any](id string) (*T, error) {
	indexName := reflect.ValueOf(new(T)).MethodByName("IndexName").Call([]reflect.Value{})[0].String()
	search, err := GetEsClient().Get().Index(indexName).Id(id).Do(context.TODO())
	if err != nil {
		return nil, err
	}
	if search.Found {
		var record T
		err = json.Unmarshal(search.Source, &record)
		if err != nil {
			return nil, err
		}
		return &record, nil
	}
	return nil, fmt.Errorf("record not found")
}

func GetByIdSafe[T any](id string) (*T, error) {
	indexName := reflect.ValueOf(new(T)).MethodByName("IndexName").Call([]reflect.Value{})[0].String()
	if id == "" {
		return nil, fmt.Errorf("id is empty")
	}
	result, err := GetEsClient().Search().Index(indexName).Query(elastic.NewTermQuery("id", id)).Do(context.TODO())
	if err != nil {
		return nil, err
	}
	var data *T
	// 检查是否有结果
	if result.TotalHits() > 0 {
		// 索引中的第一个文档
		data = ParseHitsValue[T](result.Hits.Hits)[0]
		return data, nil
	}
	return nil, fmt.Errorf("data not found")
}

// CreateOrUpdate 单个插入或更新
func CreateOrUpdate[T any](l T) error {
	return InsertOrUpdate[T]([]T{l})
}

// InsertOrUpdate 批量插入或更新
func InsertOrUpdate[T any](l []T) error {
	bulk := GetEsClient().Bulk()
	indexName := reflect.ValueOf(new(T)).MethodByName("IndexName").Call([]reflect.Value{})[0].String()
	for i := range l {
		if reflect.ValueOf(l[i]).FieldByName("CreatedAt").IsNil() {
			reflect.ValueOf(&l[i]).Elem().FieldByName("CreatedAt").Set(reflect.ValueOf(localtime.NewLocalTime(time.Now())))
		}
		reflect.ValueOf(&l[i]).Elem().FieldByName("UpdatedAt").Set(reflect.ValueOf(localtime.NewLocalTime(time.Now())))
		doc := elastic.NewBulkUpdateRequest().Index(indexName).
			Id(reflect.ValueOf(l[i]).FieldByName("Id").String()).Doc(l[i]).DocAsUpsert(true)
		bulk.Add(doc)
	}
	if bulk.NumberOfActions() <= 0 {
		return nil
	}
	re, err := bulk.Refresh("true").Do(context.TODO())
	if err != nil {
		return err
	}
	// 每个item是一个map[string]*BulkResponseItem，因此我们需要迭代这个map
	if re.Errors {
		for i, item := range re.Items {
			for op, res := range item {
				if res.Error != nil {
					// 如果有错误，打印出错误信息
					return errors.New(fmt.Sprintf("Item %d, operation %s: error %s: %s\n", i, op, res.Error.Type, res.Error.Reason))
				}
			}
		}
	}
	return nil
}

func UpdateRefreshInterval(ctx context.Context, indexName string, interval string) error {
	body := fmt.Sprintf(`{
		"index":{
			"refresh_interval": "%s"
		}
	}`, interval)

	// Put settings
	putRes, err := GetEsClient().IndexPutSettings().Index(indexName).BodyString(body).Do(context.Background())
	if err != nil {
		return errors2.Wrapf(err, "UpdateRefreshInterval")
	}
	if putRes == nil {
		return errors.New("UpdateRefreshInterval: putRes is nil")
	}
	if !putRes.Acknowledged {
		return errors.New("UpdateRefreshInterval: put settings ack failed")
	}
	return nil
}

// BulkUpdate 批量更新
func BulkUpdate[T any](l []T) error {
	bulk := GetEsClient().Bulk()
	indexName := reflect.ValueOf(new(T)).MethodByName("IndexName").Call([]reflect.Value{})[0].String()

	for i := range l {
		// 设置更新时间
		reflect.ValueOf(&l[i]).Elem().FieldByName("UpdatedAt").Set(reflect.ValueOf(localtime.NewLocalTime(time.Now())))

		// 创建更新请求
		doc := elastic.NewBulkUpdateRequest().Index(indexName).
			Id(reflect.ValueOf(l[i]).FieldByName("Ids").String()).Doc(l[i])

		// 添加到批量请求
		bulk.Add(doc)
	}

	// 如果没有批量操作，返回 nil
	if bulk.NumberOfActions() <= 0 {
		return nil
	}

	// 执行批量请求
	re, err := bulk.Refresh("true").Do(context.TODO())
	if err != nil {
		return err
	}

	// 处理响应中的错误
	if re.Errors {
		for i, item := range re.Items {
			for op, res := range item {
				if res.Error != nil {
					return errors.New(fmt.Sprintf("Item %d, operation %s: error %s: %s\n", i, op, res.Error.Type, res.Error.Reason))
				}
			}
		}
	}

	return nil
}

func GetCountByMultiIndexAndQuery(indices []string, query *elastic.BoolQuery) (bool, error) {
	total, err := GetEsClient().Count(indices...).Query(query).Do(context.TODO())
	if err != nil {
		return false, err
	}
	return total > 0, nil
}

func GetCount(index string, query *elastic.BoolQuery) (int64, error) {
	total, err := GetEsClient().Count(index).Query(query).Do(context.TODO())
	if err != nil {
		return 0, err
	}
	return total, nil
}

// IsLicenseAssetLimitReached 是否达到license结果表资产上限
func IsLicenseAssetLimitReached(indexName string, devModel bool) (bool, error) {
	if devModel {
		return false, nil
	}
	licenseAssetCount, err := license.GetLicense().GetLicenseAssetCount()
	if err != nil {
		fmt.Println("IsLicenseAssetLimitReached", err)
		return true, err
	}
	boolQuery := elastic.NewBoolQuery()
	boolQuery = boolQuery.MustNot(elastic.NewExistsQuery("deleted_at"))
	boolQuery = boolQuery.MustNot(elastic.NewExistsQuery("purged_at"))
	assetCount, err := GetCount(indexName, boolQuery)
	if err != nil {
		fmt.Println("IsLicenseAssetLimitReached", err)
		return true, err
	}
	if assetCount >= int64(licenseAssetCount) {
		return true, nil
	}
	return false, nil
}

// IsLicenseProcessAssetLimitReached 是否达到license过程表资产上限
func IsLicenseProcessAssetLimitReached(indexName string, devModel bool) (bool, error) {
	if devModel {
		return false, nil
	}
	licenseAssetCount, err := license.GetLicense().GetLicenseAssetCount()
	if err != nil {
		return true, err
	}
	boolQuery := elastic.NewBoolQuery()
	boolQuery = boolQuery.MustNot(elastic.NewExistsQuery("deleted_at"))
	boolQuery = boolQuery.MustNot(elastic.NewExistsQuery("purged_at"))
	assetCount, err := GetCount(indexName, boolQuery)
	if err != nil {
		return true, err
	}
	licenseSources, err := license.GetLicense().GetDataSources(devModel)
	if err != nil {
		return true, err
	}
	if assetCount >= int64(licenseAssetCount*len(licenseSources)) {
		return true, nil
	}
	return false, nil
}

// List 查询列表
func List[T any](page, prePage int, query elastic.Query, sorts []elastic.Sorter, fields ...string) (int64, []*T, error) {
	indexName := reflect.ValueOf(new(T)).MethodByName("IndexName").Call([]reflect.Value{})[0].String()
	search := GetEsClient().Search(indexName)
	if len(fields) != 0 {
		search.FetchSourceContext(elastic.NewFetchSourceContext(true).Include(fields...))
	}
	if len(sorts) != 0 {
		search = search.SortBy(sorts...)
	}
	result, err := search.TrackTotalHits(true).From(GetFrom(page, prePage)).Size(GetSize(prePage)).Query(query).Do(context.TODO())
	if err == io.EOF {
		return 0, nil, nil
	}
	if err != nil {
		return 0, nil, err
	}
	list := ParseHitsValue[T](result.Hits.Hits)
	if err == io.EOF {
		return result.TotalHits(), list, nil
	}
	return result.TotalHits(), list, err
}

// All 查询所有数据（使用 search_after 替代 Scroll API） 请注意排序必须有唯一的字段，否则会丢数据
func All[T any](prePage int, query elastic.Query, sorts []elastic.Sorter, fields ...string) ([]*T, error) {
	indexName := reflect.ValueOf(new(T)).MethodByName("IndexName").Call([]reflect.Value{})[0].String()

	if len(sorts) == 0 {
		sorts = []elastic.Sorter{elastic.NewFieldSort("updated_at").Desc(), elastic.NewFieldSort("created_at").Desc(), elastic.NewFieldSort("id").Desc()}
		//return nil, errors.New("sorts must be provided when using search_after")
	}

	var lastSortValues []interface{}
	var list []*T

	for {
		// 每次循环新建 SearchService
		search := GetEsClient().Search().Index(indexName)
		search = search.SortBy(sorts...) // 必须重新设置排序
		if len(fields) != 0 {
			search = search.FetchSourceContext(elastic.NewFetchSourceContext(true).Include(fields...))
		}

		// 设置 search_after 参数
		if len(lastSortValues) > 0 {
			search = search.SearchAfter(lastSortValues...)
		}

		// 执行查询
		result, err := search.Size(prePage).Query(query).Do(context.TODO())
		if err != nil {
			return nil, err
		}

		hits := result.Hits.Hits
		if len(hits) == 0 {
			break
		}
		// 添加当前批次数据
		list = append(list, ParseHitsValue[T](hits)...)
		if len(hits) < prePage {
			break
		}
		// 更新 search_after 参数为最后一个文档的排序值
		lastSortValues = hits[len(hits)-1].Sort // 需确保 SortValues 存在

	}

	return list, nil
}

func GetEsClient() *SafeClient {
	if testcommon.IsTest() {
		singleInstance = testcommon.GetElasticClient()
		return NewSafeClient(singleInstance)
	}

	if singleInstance == nil {
		once.Do(func() { singleInstance = initES(cfg.LoadElastic()) })
	}
	return NewSafeClient(singleInstance)
}

func initES(conf cfg.ElasticSearch) *elastic.Client {
	host := fmt.Sprintf("http://%s:%d", conf.Address, conf.Port)
	var esClient *elastic.Client
	var err error
	wait := time.Second * 8
	for i := 0; i < 5; i++ {
		esClient, err = elastic.NewClient(
			elastic.SetURL(host),
			elastic.SetBasicAuth(conf.UserName, conf.Password), // 账号密码
			elastic.SetGzip(true),
			elastic.SetSniff(conf.Sniff),
			elastic.SetInfoLog(NewInfoLogger()),
			elastic.SetErrorLog(NewErrLogger()),
			elastic.SetMaxRetries(2),
			elastic.SetHttpClient(&http.Client{
				Transport: &Transport{}, // use fasthttp
				Timeout:   time.Second * 10,
			}),
		)
		if err != nil {
			time.Sleep(wait)
			wait *= 2
			continue
		}
		_, _, err = esClient.Ping(host).Do(context.Background())
		if err != nil {
			time.Sleep(wait)
			wait *= 2
			continue
		}
		break
	}
	if err != nil {
		panic(err)
	}
	return esClient
}

func ParseHitsValue[T any](l []*elastic.SearchHit) []*T {
	var list = make([]*T, 0, len(l))
	for i := range l {
		if l[i] == nil || l[i].Source == nil {
			continue
		}
		var record T
		err := json.Unmarshal(l[i].Source, &record)
		if err != nil {
			continue
		}
		list = append(list, &record)
	}
	return list
}

func GetSize(size int) int {
	if size <= 0 {
		size = 1
	}
	return size
}

func GetFrom(page, size int) int {
	if page <= 0 {
		page = 1
	}
	if size <= 0 {
		size = 10
	}
	offset := (page - 1) * size
	if offset < 0 {
		offset = 0
	}
	return offset
}

// TmpSortByWrongDate 临时方案，处理时间倒序排列
func TmpSortByWrongDate(field string, desc bool) *elastic.ScriptSort {
	q := elastic.NewScriptSort(
		elastic.NewScript("ZonedDateTime.parse(params['_source']['"+
			field+
			"'], DateTimeFormatter.ofPattern('yyyy-MM-dd HH:mm:ss').withZone(ZoneId.of('UTC'))).toInstant().toEpochMilli()"),
		"number",
	)
	if desc {
		return q.Desc()
	}
	return q.Asc()
}
