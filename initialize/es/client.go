package es

import (
	"context"
	"sync"

	"github.com/olivere/elastic/v7"
)

type SafeClient struct {
	*elastic.Client
}

func NewSafeClient(client *elastic.Client) *SafeClient {
	return &SafeClient{
		Client: client,
	}
}
func (s *SafeClient) Bulk() *SafeBulkService {
	return NewSafeBulkService(s.Client)
}

// SafeBulkService 在文件开头添加线程安全的 BulkService 包装器
// bulk service是非协程安全的，因融合服务已经改造支持协程安全，所以这里不再需要线程安全，后续使用的人需要注意，或者重新开启锁
type SafeBulkService struct {
	bulkService *elastic.BulkService
	mu          sync.Mutex
}

func NewSafeBulkService(client *elastic.Client) *SafeBulkService {
	return &SafeBulkService{
		bulkService: client.Bulk(),
	}
}

// Add 添加请求
func (s *SafeBulkService) Add(requests ...elastic.BulkableRequest) *SafeBulkService {
	s.mu.Lock()
	defer s.mu.Unlock()
	s.bulkService = s.bulkService.Add(requests...)
	return s
}

// Refresh 刷新
func (s *SafeBulkService) Refresh(refresh string) *SafeBulkService {
	s.bulkService = s.bulkService.Refresh(refresh)
	return s
}

// Reset 重置
func (s *SafeBulkService) Reset() *SafeBulkService {
	s.mu.Lock()
	defer s.mu.Unlock()
	s.bulkService.Reset()
	return s
}

// Do 执行
func (s *SafeBulkService) Do(ctx context.Context) (*elastic.BulkResponse, error) {
	s.mu.Lock()
	defer s.mu.Unlock()
	return s.bulkService.Do(ctx)
}
func (s *SafeBulkService) Index(index string) *SafeBulkService {
	s.bulkService = s.bulkService.Index(index)
	return s
}

// Type 设置类型
func (s *SafeBulkService) Type(typ string) *SafeBulkService {
	s.bulkService = s.bulkService.Type(typ)
	return s
}

// Timeout 设置超时
func (s *SafeBulkService) Timeout(timeout string) *SafeBulkService {
	s.bulkService = s.bulkService.Timeout(timeout)
	return s
}

// Pretty 设置是否漂亮打印
func (s *SafeBulkService) Pretty(pretty bool) *SafeBulkService {
	s.bulkService = s.bulkService.Pretty(pretty)
	return s
}

// Routing 设置路由
func (s *SafeBulkService) Routing(routing string) *SafeBulkService {
	s.bulkService = s.bulkService.Routing(routing)
	return s
}

// Pipeline 设置管道
func (s *SafeBulkService) Pipeline(pipeline string) *SafeBulkService {
	s.bulkService = s.bulkService.Pipeline(pipeline)
	return s
}

// WaitForActiveShards 设置等待活跃分片
func (s *SafeBulkService) WaitForActiveShards(waitForActiveShards string) *SafeBulkService {
	s.bulkService = s.bulkService.WaitForActiveShards(waitForActiveShards)
	return s
}

// NumberOfActions 获取请求数量
func (s *SafeBulkService) NumberOfActions() int {
	s.mu.Lock()
	defer s.mu.Unlock()
	return s.bulkService.NumberOfActions()
}

// EstimatedSizeInBytes 获取请求大小
func (s *SafeBulkService) EstimatedSizeInBytes() int64 {
	s.mu.Lock()
	defer s.mu.Unlock()
	return s.bulkService.EstimatedSizeInBytes()
}
