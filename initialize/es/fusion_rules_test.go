package es

import (
	"fmt"
	"fobrain/fobrain/common/localtime"
	"testing"
	"time"
)

func TestNewFusionRules(t *testing.T) {
	res := NewFusionRules().AppendFusionRule("ip", "unique", 1, SourceVal{Value: "127.0.0.1", CreatedAt: localtime.NewLocalTime(time.Now()), UpdatedAt: localtime.NewLocalTime(time.Now())})
	for _, v := range *res {
		fmt.Println(v)
	}
	fmt.Println(fmt.Sprintf("%+v", res.GetByFusionRuleFiled("ip")))
}

func TestGetFieldNodes(t *testing.T) {
	rules := NewFusionRules()
	fmt.Println(rules.GetFieldNodes(""))
}

func TestGetFieldRaw(t *testing.T) {
	rules := NewFusionRules()
	fmt.Println(rules.GetFieldRaw(""))
}
