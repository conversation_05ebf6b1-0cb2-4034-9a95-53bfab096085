package es

import (
	"fobrain/fobrain/common/localtime"
	"fobrain/pkg/utils"
)

type (
	// FusionRule 融合规则信息
	FusionRule struct {
		Field string            `json:"field"` // 融合字段
		Rule  string            `json:"rule"`  // 融合规则
		Raw   map[int]SourceVal `json:"raw"`   // 原始数据
	}
	// SourceVal 来源值信息
	SourceVal struct {
		CreatedAt *localtime.Time `json:"created_at"`
		UpdatedAt *localtime.Time `json:"updated_at"`
		Value     any             `json:"value"`
	}
	FusionRules []*FusionRule
)

func NewFusionRules() *FusionRules {
	rules := make(FusionRules, 0)
	return &rules
}

// GetByFusionRuleFiled 根据指定字段提取融合规则
func (f *FusionRules) GetByFusionRuleFiled(field string) *FusionRule {
	maps := utils.ListToMapFunc(*f, func(v *FusionRule) (string, bool) {
		if v == nil {
			return "", false
		}
		return v.Field, true
	})
	return maps[field]
}

// AppendFusionRule 追加融合规则
func (f *FusionRules) AppendFusionRule(field string, rule string, node int, value SourceVal) *FusionRules {
	find := false
	for _, v := range *f {
		// 找到,覆盖数据
		if v.Field == field {
			v.Raw[node] = value
			v.Rule = rule
			find = true
			break
		}
	}
	// 未找到,追加数据
	if !find {
		*f = append(*f, &FusionRule{Field: field, Rule: rule, Raw: map[int]SourceVal{node: value}})
	}
	return f
}

// GetFieldNodes 获取字段数据节点列表
func (f *FusionRules) GetFieldNodes(field string) []int {
	nodes := make([]int, 0)
	for _, v := range *f {
		// 找到,覆盖数据
		if v.Field == field {
			for node, _ := range v.Raw {
				nodes = append(nodes, node)
			}
		}
	}
	return nodes
}

// GetFieldRaw 获取字段原始数据
func (f *FusionRules) GetFieldRaw(field string) map[int]SourceVal {
	for _, v := range *f {
		if v.Field == field {
			return v.Raw
		}
	}
	return map[int]SourceVal{}
}
