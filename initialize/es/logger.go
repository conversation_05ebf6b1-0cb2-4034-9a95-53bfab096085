package es

import (
	"fmt"
	"fobrain/fobrain/logs"
	"fobrain/pkg/cfg"

	"github.com/olivere/elastic/v7"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"gorm.io/gorm/logger"
)

const logPrefix = "ES "

type (
	InfoLogger struct {
		elastic.Logger
	}
	ErrLogger struct {
		elastic.Logger
	}
)

func NewInfoLogger() *InfoLogger {
	return &InfoLogger{}
}
func NewErrLogger() *ErrLogger {
	return &ErrLogger{}
}

func getLogLevel() logger.LogLevel {
	logMode := logger.Warn
	logLevel, err := zapcore.ParseLevel(cfg.LoadLogger().Level)
	if err != nil {
		panic(err)
	}
	switch logLevel {
	case zapcore.ErrorLevel, zapcore.FatalLevel, zapcore.PanicLevel, zapcore.DPanicLevel:
		logMode = logger.Error
	case zapcore.WarnLevel, zapcore.InfoLevel:
		logMode = logger.Warn
	case zapcore.DebugLevel:
		logMode = logger.Info
	default:

	}
	return logMode
}

func (l *InfoLogger) Printf(format string, v ...interface{}) {
	level := getLogLevel()
	// 实际info，getLogLevel给转成了Warn
	if level == logger.Info || level == logger.Warn {
		// 日志太多了，Info级别的就不打印了
		// logs.GetLogger().WithOptions(zap.AddCallerSkip(5)).Info(logPrefix + fmt.Sprintf(format, v...))
	}
}
func (l *ErrLogger) Printf(format string, v ...interface{}) {
	level := getLogLevel()
	if level == logger.Info || level == logger.Error || level == logger.Warn {
		logs.GetLogger().WithOptions(zap.AddCallerSkip(1)).Error(logPrefix + fmt.Sprintf(format, v...))
	}
}
