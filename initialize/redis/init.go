package redis

import (
	"context"
	"fmt"
	"sync"
	"time"

	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/pkg/cfg"

	goRedis "github.com/go-redis/redis/v8"
)

var once sync.Once

var singleInstance *goRedis.Client

func GetRedisClient() *goRedis.Client {
	if testcommon.IsTest() {
		singleInstance = testcommon.GetRedisClient()
		return singleInstance
	}

	if singleInstance == nil {
		once.Do(func() { singleInstance = initRedis(cfg.LoadRedis()) })
	}
	return singleInstance
}

func initRedis(conf cfg.Redis) *goRedis.Client {
	rc := goRedis.NewClient(&goRedis.Options{
		Addr:     fmt.Sprintf("%s:%d", conf.Address, conf.Port), // redis服务ip:port
		Password: conf.Password,                                 // redis的认证密码
		DB:       conf.Database,                                 // 连接的database库
		// 连接池大小会根据 CPU 数量自动调整
		// PoolSize:    30,                                            // 连接池
		MinIdleConns: 10,               // 最小空闲连接数
		PoolTimeout:  10 * time.Second, // 连接池最大等待时间
		IdleTimeout:  5 * time.Minute,  // 空闲连接最大保持时间
	})
	fmt.Println("redis address: ", rc.Options().Addr)
	fmt.Println("redis pool size: ", rc.Options().PoolSize)
	wait := time.Second * 8
	var err error
	for i := 0; i < 5; i++ {
		_, err = rc.Ping(context.Background()).Result()
		if err == nil {
			break
		}
		time.Sleep(wait)
		wait *= 2
		continue

	}
	if err != nil {
		panic(err)
	}

	return rc
}

// Close 关闭 Redis 连接
func Close() {
	if singleInstance != nil {
		singleInstance.Close()
		singleInstance = nil
	}
}
