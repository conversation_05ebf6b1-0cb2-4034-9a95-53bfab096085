#!/bin/bash
echo "--install amd64 pack start--"

git_branch=$1
release_version=$2
echo "git_branch: $git_branch"

git checkout .
git checkout $git_branch
echo "-- git pull -- "1
git pull

echo "--clean install package folder and fobrain-install-v$release_version-amd64.tar.gz"
rm -rf install-fobrain
rm -rf build-images
rm -rf fobrain-install-v$release_version-amd64.tar.gz

mkdir install-fobrain
mkdir build-images
mkdir build-images/fobrain

echo "--复制Dockerfile和用到的version文件"
cp ./install/amd64/v2/config.json ./build-images/fobrain/fobrain.json
cp version ./build-images/fobrain/
cp ./install/amd64/build/Dockerfile-fobrain ./build-images/
cp ./install/amd64/build/Dockerfile-mergeService ./build-images/
cp ./install/amd64/build/Dockerfile-elasticsearch-s3 ./build-images/

#cp ./install/amd64/v2/mysql ./build-images/fobrain/mysql
#cp ./install/amd64/v2/mysqldump ./build-images/fobrain/mysqldump
#cp ./install/amd64/build/Dockerfile-fobrain-adapter ./build-images/

echo "--building cmd"
cd cmd && go build -o cmd-linux && cd ..
cp cmd/cmd-linux ./build-images/fobrain/cmd
mv cmd/cmd-linux ./install-fobrain/cmd-migrate

# 从git中获取版本号
code_version=""
if [[ -z $code_version ]]; then
    if [ -d ".git" ]; then
        # 获取git分支名
        branch=`git symbolic-ref HEAD | cut -b 12-`
        # 获取git commit id
        commitId=`git rev-parse HEAD`
        # 获取git commit id前8位
        # commitId=${commitId:0:8}
        code_version="$branch-$commitId"
    else
        code_version="unknown"
    fi
fi

echo "--building fobrain"
cd fobrain && go build -ldflags "-X main.Version=$code_version -X main.BuildTime=$(date +%Y-%m-%d_%H:%M:%S)" -o fobrain-linux && cd ..
mv fobrain/fobrain-linux ./build-images/fobrain/fobrain

echo "--building mergeService"
cd mergeService && go build -o merge-linux && cd ..
mv mergeService/merge-linux ./build-images/fobrain/mergeservice

#echo "--building adapter"
#cd adapter && go build -o adapter-linux && cd ..
#mv adapter/adapter ./build-images/fobrain/adapter

cd build-images && echo "进入构建目录" && pwd

echo "---构建fobrain--"
docker build -f Dockerfile-fobrain -t fobrain:latest .

echo "---构建elasticsearch-7.17.26--"
docker build -f Dockerfile-elasticsearch-s3 -t es-amd64:7.17.26 .

echo "---构建merge-service--"
docker build -f Dockerfile-mergeService -t merge-service:latest .

#echo "---构建fobrain-adapter--"
#docker build -f Dockerfile-fobrain-adapter -t fobrain-adapter:latest .

echo "--构建完毕--"
cd ..

echo "save fobrain and merge-service image"
cd install-fobrain && docker save -o fobrain.tar fobrain:latest && docker save -o es-amd64.tar es-amd64:7.17.26 && docker save -o merge-service.tar merge-service:latest && cd ..

echo "copy version"
cp version ./install-fobrain/

echo "copy install files"
cp -r ./install/amd64/v2/* ./install-fobrain/
cp -r ./storage/app/public ./install-fobrain/
echo "copy dist.tar.gz"
cp /data/dist.tar.gz ./install-fobrain/
#consul.tar docker-26.1.4.tgz elasticsearch-7.10.0.tar mysql-8.0.38.tar nginx-1.20.1-1.el7.ngx.x86_64.rpm redis-6.2.1.tar
echo "copy install-tools/*"
cp -r /data/install-tools/* ./install-fobrain/


echo "-------相关文件 已放入install-fobrain"
echo "-------开始打包"
tar -cvzf fobrain-install-v$release_version-amd64.tar.gz install-fobrain/
echo "-------打包结束, 路径:$(pwd)/fobrain-install-v$release_version-amd64.tar.gz"
echo "-------清理"
rm -rf build-images
rm -rf install-fobrain
echo "-------清理结束"

echo "--all end--"
