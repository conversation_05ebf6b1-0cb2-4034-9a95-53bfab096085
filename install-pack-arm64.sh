#!/bin/bash
echo "--install arm64 pack start--"
git_branch=$1
release_version=$2
echo "git_branch: $git_branch"

git checkout .
git checkout $git_branch
echo "-- git pull -- "
git pull

echo "--clean install package folder and fobrain-install-v$release_version-arm64.tar.gz"

rm -rf install-fobrain
rm -rf build-images-arm64
rm -rf fobrain-install-v$release_version-arm64.tar.gz

mkdir install-fobrain-arm64
mkdir build-images-arm64
mkdir build-images-arm64/fobrain

echo "--复制Dockerfile和用到的version文件"
cp ./install/amd64/v2/config.json ./build-images-arm64/fobrain/fobrain.json
cp version ./build-images-arm64/fobrain/
cp ./install/arm64/build/Dockerfile-fobrain ./build-images-arm64/
cp ./install/arm64/build/Dockerfile-mergeService ./build-images-arm64/
cp ./install/arm64/build/Dockerfile-elasticsearch-s3 ./build-images-arm64/
# cp ./install/arm64/v2/mysql ./build-images-arm64/fobrain/mysql
# cp ./install/arm64/v2/mysqldump ./build-images-arm64/fobrain/mysqldump

echo "change arch to arm64"
go env -w GOARCH=arm64

echo "--building cmd"
cd cmd && go build -o cmd-linux-arm64 && cd ..
cp cmd/cmd-linux-arm64 ./build-images-arm64/fobrain/cmd
mv cmd/cmd-linux-arm64 ./install-fobrain-arm64/cmd-migrate

# 从git中获取版本号
code_version=""
if [[ -z $code_version ]]; then
    if [ -d ".git" ]; then
        # 获取git分支名
        branch=`git symbolic-ref HEAD | cut -b 12-`
        # 获取git commit id
        commitId=`git rev-parse HEAD`
        # 获取git commit id前8位
        # commitId=${commitId:0:8}
        code_version="$branch-$commitId"
    else
        code_version="unknown"
    fi
fi

echo "--building fobrain"
cd fobrain && go build -ldflags "-X main.Version=$code_version -X main.BuildTime=$(date +%Y-%m-%d_%H:%M:%S)" -o fobrain-linux-arm64 && cd ..
mv fobrain/fobrain-linux-arm64 ./build-images-arm64/fobrain/fobrain

echo "--building mergeService"
cd mergeService && go build -o merge-linux-arm64 && cd ..
mv mergeService/merge-linux-arm64 ./build-images-arm64/fobrain/mergeservice

cd build-images-arm64 && echo "进入构建目录" && pwd

echo "---构建fobrain--"
docker build -f Dockerfile-fobrain -t fobrain-arm64:latest .

#echo "---构建elasticsearch-7.17.26--"
#docker build -f Dockerfile-elasticsearch-s3 -t es-arm64:7.17.26 .

echo "---构建merge-service--"
docker build -f Dockerfile-mergeService -t merge-service-arm64:latest .

echo "--构建完毕--"
cd ..

echo "save fobrain and merge-service image"
cd install-fobrain-arm64 && docker save -o fobrain-arm64.tar fobrain-arm64:latest && docker save -o es-arm64.tar es-arm64:7.17.26 && docker save -o merge-service-arm64.tar merge-service-arm64:latest && cd ..

echo "copy version"
cp version ./install-fobrain-arm64/

echo "copy install files"
cp -r ./install/amd64/v2/* ./install-fobrain-arm64/
cp -r ./install/arm64/v2/* ./install-fobrain-arm64/
cp -r ./storage/app/public ./install-fobrain-arm64/
echo "copy dist.tar.gz"
cp /data/dist.tar.gz ./install-fobrain-arm64/
#consul.tar docker-26.1.4.tgz elasticsearch-7.17.26.tar mysql-8.0.38.tar nginx-1.20.1-1.el7.ngx.x86_64.rpm redis-6.2.1.tar
echo "copy install-tools-arm64/*"
cp -r /data/install-tools-arm64/* ./install-fobrain-arm64/

echo "-------相关文件 已放入install-fobrain"
echo "-------开始打包"
tar -cvzf fobrain-install-v$release_version-arm64.tar.gz install-fobrain-arm64/
echo "-------打包结束, 路径:$(pwd)/fobrain-install-v$release_version-arm64.tar.gz"
echo "-------清理"
rm -rf build-images-arm64
rm -rf install-fobrain-arm64
echo "-------清理结束"

echo "change arch to amd64"
go env -w GOARCH=amd64

echo "--all end--"
