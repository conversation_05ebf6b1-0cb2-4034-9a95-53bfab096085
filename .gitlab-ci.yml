# 定义 stages
stages:
  - lint-test
  - build-replace-deploy
  - automated_test
  - automated_test_239
  - build-in-149
  - deploy-by-binary
  - build
  - deploy
  #- pack

variables:
  AUTO_DEVOPS_DISABLE_FETCH: "true"
  GIT_STRATEGY: none
  FOBRAIN_BUILD_DIR: "/data/fobrain-build/fobrain"
  FOBRAIN_LOCAL_DIR: "/data/fobrain-build/local"
  GOROOT: "/usr/local/go"
  GORELEASER_CURRENT_TAG: "0.0.1-alpha.1"
  GORELEASER_PREVIOUS_TAG: "0.0.1-alpha.1"
  DOCKER_REGISTER_ADDR: "harbor.fofa.info"
  CURRENT_BRANCH: $CI_COMMIT_BRANCH
  SERVER_IP: ""

lint+test:
  stage: lint-test
  coverage: '/total:\s+\(statements\)\s+(\d+.\d+\%)/'
  artifacts:
    when: always
    reports:
      junit: report.xml
  tags:
    - fb2.0-11.144
  script:
    - echo "当前服务器 IP 地址：" $(hostname -I)
    - cd $FOBRAIN_BUILD_DIR && git checkout .
    - 'if [[ -n "$CI_MERGE_REQUEST_SOURCE_BRANCH_NAME" ]]; then'
    - '  BRANCH=$CI_MERGE_REQUEST_SOURCE_BRANCH_NAME'
    - '  CURRENT_BRANCH=$CI_MERGE_REQUEST_SOURCE_BRANCH_NAME'
    - 'else'
    - '  BRANCH=$CI_COMMIT_BRANCH'
    - '  CURRENT_BRANCH=$CI_COMMIT_BRANCH'
    - 'fi'
    - 'echo !!!!当前分支: $CURRENT_BRANCH'
    - 'cd $FOBRAIN_BUILD_DIR'
    - 'EXIST=$(git branch --list "$BRANCH")'
    - 'if [ -z "$EXIST" ]; then'
    - '  echo "no branch and fetch"'
    - '  git fetch origin $BRANCH:$BRANCH'
    - 'elif [ "$EXIST" == "* develop" ]; then'
    - '  echo "branch exists and is develop"'
    - 'elif [ "$EXIST" == "develop" ]; then'
    - '  echo "branch exists and target is develop"'
    - 'else'
    - '  echo "branch exists"'
    - '  git checkout main'
    - '  git branch -D $BRANCH'
    - '  git fetch origin $BRANCH:$BRANCH'
    - 'fi'
    - cd $FOBRAIN_BUILD_DIR && git reset --hard HEAD && git checkout $BRANCH && git checkout . && git branch --set-upstream-to=origin/$BRANCH && git pull
    - echo "最近的 git log："
    - git log -n 5 --oneline
    - cd $FOBRAIN_BUILD_DIR && go mod tidy && go mod vendor
    - echo "最新代码初始化完成"
    - echo "开始测试"
    - go test -failfast -gcflags=all=-l $(go list ./... | grep -v fobrain/module/backup | grep -v fobrain/module/zip | grep -v fobrain/mergeService/proto | grep -v fobrain/fobrain/app/response | grep -v fobrain/fobrain/databases | grep -v fobrain/fobrain/tests | grep -v fobrain/cmd | grep -v fobrain/fobrain/routes | grep -v fobrain/fobrain/common/middleware | grep -v fobrain/mergeService/model/test | grep -v fobrain/mergeService/model/field_tagger/real_test | grep -v fobrain/adapter/router | grep -v fobrain/pkg/utils) -parallel 20 -p 4 -coverprofile=coverage.out -v
    - go tool cover -func=coverage.out && unlink coverage.out
    - gotestsum --junitfile report.xml --format testname -- $(go list ./... | grep -v fobrain/module/backup | grep -v fobrain/module/zip | grep -v fobrain/mergeService/proto | grep -v fobrain/fobrain/app/response | grep -v fobrain/fobrain/databases | grep -v fobrain/fobrain/tests | grep -v fobrain/cmd | grep -v fobrain/fobrain/routes | grep -v fobrain/fobrain/common/middleware | grep -v fobrain/mergeService/model/test | grep -v fobrain/mergeService/model/field_tagger/real_test | grep -v fobrain/adapter/router | grep -v fobrain/pkg/utils) -parallel 20 -p 4 > /dev/null && /bin/cp -rf report.xml  $CI_PROJECT_DIR/report.xml
    - cd $FOBRAIN_BUILD_DIR && cat report.xml
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event" &&
        ($CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "develop" || $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "testing")
      when: always
    - if: $CI_COMMIT_BRANCH == "develop" || $CI_COMMIT_BRANCH =~ /^testing.*$/
      when: always
    - when: never
  allow_failure: false
  retry:
    max: 1
  resource_group: ${CI_MERGE_REQUEST_IID:-"default-group"} # 自动引用合并请求 ID 作为资源组

build-replace-deploy-local:
  stage: build-replace-deploy
  tags:
    - fb2.0-11.144
  script:
    - echo "当前服务器 IP 地址：" $(hostname -I)
    - SERVER_IP=$(hostname -I | awk '{print $1}')
    - cd $FOBRAIN_BUILD_DIR && git checkout .
    - 'if [[ -n "$CI_MERGE_REQUEST_SOURCE_BRANCH_NAME" ]]; then'
    - '  BRANCH=$CI_MERGE_REQUEST_SOURCE_BRANCH_NAME'
    - '  CURRENT_BRANCH=$CI_MERGE_REQUEST_SOURCE_BRANCH_NAME'
    - 'else'
    - '  BRANCH=$CI_COMMIT_BRANCH'
    - '  CURRENT_BRANCH=$CI_COMMIT_BRANCH'
    - 'fi'
    - 'echo !!!!当前分支: $CURRENT_BRANCH'
    - 'cd $FOBRAIN_BUILD_DIR'
    - 'EXIST=$(git branch --list "$BRANCH")'
    - 'if [ -z "$EXIST" ]; then'
    - '  echo "no branch and fetch"'
    - '  git fetch origin $BRANCH:$BRANCH'
    - 'elif [ "$EXIST" == "* develop" ]; then'
    - '  echo "branch exists and is develop"'
    - 'elif [ "$EXIST" == "develop" ]; then'
    - '  echo "branch exists and target is develop"'
    - 'else'
    - '  echo "branch exists"'
    - '  git checkout main'
    - '  git branch -D $BRANCH'
    - '  git fetch origin $BRANCH:$BRANCH'
    - 'fi'
    - cd $FOBRAIN_BUILD_DIR && git reset --hard HEAD && git checkout $BRANCH && git checkout . && git branch --set-upstream-to=origin/$BRANCH && git pull
    - echo "最近的 git log："
    - git log -n 5 --oneline
    - cd $FOBRAIN_BUILD_DIR && go mod tidy && go mod vendor
    - echo "最新代码初始化完成"
    - echo "处理config.json"
    - cd $FOBRAIN_BUILD_DIR && rm /etc/fobrain/conf/config.json && cp install/amd64/v2/config.json /etc/fobrain/conf/
    - echo "开始迁移"
    - cd $FOBRAIN_BUILD_DIR && make migrate-run Component=all
    - echo "迁移完成"
    - echo $CURRENT_BRANCH
    - echo "开始构建"
    - export CODE_VERSION=$BRANCH-$(git rev-parse HEAD | cut -c 1-8)
    - echo "CODE_VERSION:" $CODE_VERSION
    - cd $FOBRAIN_BUILD_DIR/fobrain && go build -ldflags "-X main.Version=$CODE_VERSION -X main.BuildTime=$(date +%Y-%m-%d_%H:%M:%S)" -o $FOBRAIN_BUILD_DIR/bin/fobrain-linux-amd64
    - cd $FOBRAIN_BUILD_DIR/mergeService && go build -o $FOBRAIN_BUILD_DIR/bin/merge-linux-amd64
    - cd $FOBRAIN_BUILD_DIR/cmd && go build -o $FOBRAIN_BUILD_DIR/bin/cmd-linux-amd64
    # - cd $FOBRAIN_BUILD_DIR/adapter && go build -o $FOBRAIN_BUILD_DIR/bin/adapter-linux-amd64
    - echo "二进制镜像构建完成"
    - echo "开始替换"
    - docker cp $FOBRAIN_BUILD_DIR/bin/fobrain-linux-amd64 fobrain:/usr/sbin/fobrain
    - docker cp $FOBRAIN_BUILD_DIR/bin/merge-linux-amd64 fobrain-merge-service-1:/usr/sbin/mergeservice
    - docker cp $FOBRAIN_BUILD_DIR/bin/cmd-linux-amd64 fobrain:/usr/sbin/cmd
    # - docker cp $FOBRAIN_BUILD_DIR/bin/adapter-linux-amd64 fobrain:/usr/sbin/adapter
    - echo "替换完成"
    - echo "开始重启"
    - docker restart fobrain
    - docker restart fobrain-merge-service-1
    - echo "重启完成"
    - echo "SERVER_IP=$SERVER_IP" >> build.env
    - |
      if [[ "$SERVER_IP" == "************" ]]; then
        echo "TRIGGER_E2E_239=true" >> build.env
        echo "TRIGGER_E2E_229=false" >> build.env
      elif [[ "$SERVER_IP" == "************" ]]; then
        echo "TRIGGER_E2E_239=false" >> build.env
        echo "TRIGGER_E2E_229=true" >> build.env
      else
        echo "TRIGGER_E2E_239=false" >> build.env
        echo "TRIGGER_E2E_229=false" >> build.env
      fi
    - cat build.env
  artifacts:
    when: always
    reports:
      junit: report.xml
      dotenv: build.env
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event" &&
        ($CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "develop" || $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "testing")
      when: always
    - if: $CI_COMMIT_BRANCH == "develop" || $CI_COMMIT_BRANCH =~ /^testing.*$/
      when: always
    - when: never
  allow_failure: true
  retry:
    max: 1
  resource_group: ${CI_MERGE_REQUEST_IID:-"default-group"} # 自动引用合并请求 ID 作为资源组

# E2E自动化测试 - 针对149服务器
e2e_auto_test_239:
  stage: automated_test
  variables:
    NODE_ENV: "test"
    # 要测试的服务器IP列表
    TEST_SERVERS: "************"
  image: cypress_e2e_test:v1.2

  script:
    - |
      # 将变量转换为数组
      SERVER_IPS=($TEST_SERVERS)
      TOTAL_EXIT_CODE=0
      echo "TRIGGER_E2E_229: $TRIGGER_E2E_229"
      echo "TRIGGER_E2E_239: $TRIGGER_E2E_239"
      # 脚本中判定Trigger E2E 239
      if [ "$TRIGGER_E2E_239" != "true" ]; then
        echo "Trigger E2E 239 不为true，跳过测试"
        exit 0
      fi
      for SERVER_IP in "${SERVER_IPS[@]}"; do
        echo "测试服务器: $SERVER_IP"

        # 使用固定工作目录
        WORK_DIR="/data/cypress_ui_automation/fobrain_2.0_automated"

        # 清理进程和宿主机旧文件
        CONTAINER_NAME="cypress_e2e"
        sudo docker rm -f $CONTAINER_NAME 2>/dev/null || true
        sudo pkill -f "Xvfb" 2>/dev/null || true

        # 清理宿主机上的旧截图和视频文件，确保及时删除
        sudo rm -rf $WORK_DIR/cypress/screenshots/* 2>/dev/null || true
        sudo rm -rf $WORK_DIR/cypress/videos/* 2>/dev/null || true

        # 执行测试
        EXIT_CODE=0
        sudo docker run --rm --network=host \
          -e NODE_ENV="$NODE_ENV" \
          -e SERVER_IP="$SERVER_IP" \
          -e ELECTRON_DISABLE_SECURITY_WARNINGS=true \
          -v $WORK_DIR:/e2e \
          -w /e2e \
          --entrypoint /bin/bash \
          --name "$CONTAINER_NAME" \
          cypress_e2e_test:v1.2 \
          -c "Xvfb >/dev/null 2>&1 & sleep 3; timeout 1800 npm run run-tests" || EXIT_CODE=$?

        # 收集截图和视频文件（无论成功失败都创建文件夹，避免artifacts警告）
        mkdir -p ./cypress_screenshots_${SERVER_IP//\./_} ./cypress_videos_${SERVER_IP//\./_}

        if [ "${EXIT_CODE:-0}" -ne 0 ]; then
          echo "测试失败，收集截图和视频文件"
          if [ -d "$WORK_DIR/cypress/screenshots" ]; then
            sudo cp -r $WORK_DIR/cypress/screenshots/* ./cypress_screenshots_${SERVER_IP//\./_}/ 2>/dev/null || true
            sudo chown -R $(whoami):$(whoami) ./cypress_screenshots_${SERVER_IP//\./_} 2>/dev/null || true
          fi
          if [ -d "$WORK_DIR/cypress/videos" ]; then
            sudo cp -r $WORK_DIR/cypress/videos/* ./cypress_videos_${SERVER_IP//\./_}/ 2>/dev/null || true
            sudo chown -R $(whoami):$(whoami) ./cypress_videos_${SERVER_IP//\./_} 2>/dev/null || true
          fi
        else
          echo "测试成功，文件夹为空（符合预期）"
        fi

        # 记录结果
        if [ "${EXIT_CODE:-0}" -ne 0 ]; then
          echo "服务器 $SERVER_IP 测试失败"
          TOTAL_EXIT_CODE=1
        else
          echo "服务器 $SERVER_IP 测试成功"
        fi
      done

      # 最终结果
      if [ "$TOTAL_EXIT_CODE" -ne 0 ]; then
        exit 1
      fi
  dependencies:
    - build-replace-deploy-local
  tags:
    - fb2.0-11.239
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event" &&
        ($CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "develop" || $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "testing")
      when: always
    - if: ($CI_COMMIT_BRANCH == "develop" || $CI_COMMIT_BRANCH =~ /^testing.*$/)
      when: always
    - when: never
  allow_failure: true
  retry:
    max: 1
  artifacts:
    when: always
    paths:
      - cypress_screenshots_*
      - cypress_videos_*
    expire_in: 1 week

# E2E自动化测试 - 针对171服务器
e2e_auto_test_229:
  stage: automated_test
  variables:
    NODE_ENV: "test"
    # 要测试的服务器IP列表
    TEST_SERVERS: "************"
  image: cypress_e2e_test:v1.2

  script:
    - |
      # 将变量转换为数组
      SERVER_IPS=($TEST_SERVERS)
      TOTAL_EXIT_CODE=0
      # 脚本中判定Trigger E2E 229
      echo "TRIGGER_E2E_229: $TRIGGER_E2E_229"
      echo "TRIGGER_E2E_239: $TRIGGER_E2E_239"
      if [ "$TRIGGER_E2E_229" != "true" ]; then
        echo "Trigger E2E 229 不为true，跳过测试"
        exit 0
      fi
      for SERVER_IP in "${SERVER_IPS[@]}"; do
        echo "测试服务器: $SERVER_IP"
    
        # 使用固定工作目录
        WORK_DIR="/data/cypress_ui_automation/fobrain_2.0_automated"

        # 清理进程和宿主机旧文件
        CONTAINER_NAME="cypress_e2e"
        sudo docker rm -f $CONTAINER_NAME 2>/dev/null || true
        sudo pkill -f "Xvfb" 2>/dev/null || true

        # 清理宿主机上的旧截图和视频文件，确保及时删除
        sudo rm -rf $WORK_DIR/cypress/screenshots/* 2>/dev/null || true
        sudo rm -rf $WORK_DIR/cypress/videos/* 2>/dev/null || true

        # 执行测试
        EXIT_CODE=0
        sudo docker run --rm --network=host \
          -e NODE_ENV="$NODE_ENV" \
          -e SERVER_IP="$SERVER_IP" \
          -e ELECTRON_DISABLE_SECURITY_WARNINGS=true \
          -v $WORK_DIR:/e2e \
          -w /e2e \
          --entrypoint /bin/bash \
          --name "$CONTAINER_NAME" \
          cypress_e2e_test:v1.2 \
          -c "Xvfb >/dev/null 2>&1 & sleep 3; timeout 1800 npm run run-tests" || EXIT_CODE=$?

        # 收集截图和视频文件（无论成功失败都创建文件夹，避免artifacts警告）
        mkdir -p ./cypress_screenshots_${SERVER_IP//\./_} ./cypress_videos_${SERVER_IP//\./_}

        if [ "${EXIT_CODE:-0}" -ne 0 ]; then
          echo "测试失败，收集截图和视频文件"
          if [ -d "$WORK_DIR/cypress/screenshots" ]; then
            sudo cp -r $WORK_DIR/cypress/screenshots/* ./cypress_screenshots_${SERVER_IP//\./_}/ 2>/dev/null || true
            sudo chown -R $(whoami):$(whoami) ./cypress_screenshots_${SERVER_IP//\./_} 2>/dev/null || true
          fi
          if [ -d "$WORK_DIR/cypress/videos" ]; then
            sudo cp -r $WORK_DIR/cypress/videos/* ./cypress_videos_${SERVER_IP//\./_}/ 2>/dev/null || true
            sudo chown -R $(whoami):$(whoami) ./cypress_videos_${SERVER_IP//\./_} 2>/dev/null || true
          fi
        else
          echo "测试成功，文件夹为空（符合预期）"
        fi

        # 记录结果
        if [ "${EXIT_CODE:-0}" -ne 0 ]; then
          echo "服务器 $SERVER_IP 测试失败"
          TOTAL_EXIT_CODE=1
        else
          echo "服务器 $SERVER_IP 测试成功"
        fi
      done

      # 最终结果
      if [ "$TOTAL_EXIT_CODE" -ne 0 ]; then
        exit 1
      fi
  dependencies:
    - build-replace-deploy-local
  tags:
    - e2e_11.229
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event" &&
        ($CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "develop" || $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "testing")
      when: always
    - if: ($CI_COMMIT_BRANCH == "develop" || $CI_COMMIT_BRANCH =~ /^testing.*$/)
      when: always
    - when: never
  allow_failure: true
  retry:
    max: 1
  artifacts:
    when: always
    paths:
      - cypress_screenshots_*
      - cypress_videos_*
    expire_in: 1 week

build-in-149-branch:
  stage: build-in-149
  when: manual
  tags:
    - build-image-149
  script:
    - echo "当前服务器 IP 地址：" $(hostname -I)
    - echo "目标分支：develop"
    - cd $FOBRAIN_BUILD_DIR && git checkout .
    - git checkout develop
    - git pull
    - echo "最近的 git log："
    - git log -n 5 --oneline
    - cd $FOBRAIN_BUILD_DIR && go mod tidy && go mod vendor
    - echo "最新代码初始化完成"
    - echo "开始构建"
    - export CODE_VERSION=$BRANCH-$(git rev-parse HEAD | cut -c 1-8)
    - echo "CODE_VERSION:" $CODE_VERSION
    - echo "创建临时目录"
    - export TMP_BUILD_DIR="/tmp/fobrain_build"
    - rm -rf $TMP_BUILD_DIR
    - mkdir -p $TMP_BUILD_DIR
    - echo "构建目录：$TMP_BUILD_DIR"
    - echo "开始编译 Fobrain"
    - cd $FOBRAIN_BUILD_DIR/fobrain && go build -ldflags "-X main.Version=$CODE_VERSION -X main.BuildTime=$(date +%Y-%m-%d_%H:%M:%S)" -o $TMP_BUILD_DIR/fobrain-linux-amd64
    - echo "开始编译 mergeService"
    - cd $FOBRAIN_BUILD_DIR/mergeService && go build -o $TMP_BUILD_DIR/merge-linux-amd64
    - echo "开始编译 cmd"
    - cd $FOBRAIN_BUILD_DIR/cmd && go build -o $TMP_BUILD_DIR/cmd-linux-amd64
    - echo "开始编译 adapter"
    - cd $FOBRAIN_BUILD_DIR/adapter && go build -o $TMP_BUILD_DIR/adapter-linux-amd64
    - echo "所有二进制文件构建完成，保存在：$TMP_BUILD_DIR"
    - ls -la $TMP_BUILD_DIR
    - echo "构建信息文件"
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event" &&
        ($CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "develop" || $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "testing")
      when: always
    - if: $CI_COMMIT_BRANCH == "develop" || $CI_COMMIT_BRANCH =~ /^testing.*$/
      when: always
    - when: never
  allow_failure: true

deploy-149-to-129:
  stage: deploy-by-binary
  when: manual
  tags:
    - "10.129"  # 在129服务器上运行
  script:
    - echo "当前服务器 IP 地址：" $(hostname -I)
    - echo "测试SSH连接"
    - ssh -o ConnectTimeout=10 root@************ "echo 'SSH连接到149服务器成功'" || (echo "SSH连接失败，请检查免密配置" && exit 1)
    - echo "开始从149服务器拷贝二进制文件"
    - echo "149服务器构建目录：/tmp/fobrain_build"
    - echo "创建临时目录"
    - export TEMP_DIR="/tmp/deploy_from_149_$(date +%Y%m%d_%H%M%S)"
    - mkdir -p $TEMP_DIR
    - echo "临时目录：$TEMP_DIR"
    - echo "从149服务器拷贝文件到129服务器"
    - echo "检查149服务器上的文件是否存在"
    - ssh root@************ "ls -la /tmp/fobrain_build/" || (echo "构建目录不存在或无法访问" && exit 1)
    - echo "开始拷贝二进制文件"
    - echo "使用SSH+tar方式传输文件，避免scp命令依赖问题"
    - ssh -o ConnectTimeout=30 root@************ "cd /tmp/fobrain_build && tar czf - fobrain-linux-amd64 merge-linux-amd64 cmd-linux-amd64 adapter-linux-amd64 build_info.txt 2>/dev/null || tar czf - fobrain-linux-amd64 merge-linux-amd64 cmd-linux-amd64 adapter-linux-amd64" | tar xzf - -C $TEMP_DIR/ || (echo "文件传输失败" && exit 1)
    - echo "验证传输的文件"
    - ls -la $TEMP_DIR
    - echo "验证二进制文件可执行性"
    - chmod +x $TEMP_DIR/fobrain-linux-amd64
    - chmod +x $TEMP_DIR/merge-linux-amd64
    - chmod +x $TEMP_DIR/cmd-linux-amd64
    - chmod +x $TEMP_DIR/adapter-linux-amd64
    - echo "二进制文件准备完成"
    - echo "开始替换"
    - docker cp $TEMP_DIR/fobrain-linux-amd64 fobrain:/usr/sbin/fobrain
    - docker cp $TEMP_DIR/merge-linux-amd64 fobrain-merge-service-1:/usr/sbin/mergeservice
    - docker cp $TEMP_DIR/cmd-linux-amd64 fobrain:/usr/sbin/cmd
    - docker cp $TEMP_DIR/adapter-linux-amd64 fobrain:/usr/sbin/adapter
    - echo "替换完成"
    - echo "开始执行migrate"
    - sh $TEMP_DIR/cmd-linux-amd64 migrate run
    - echo "开始重启"
    - docker restart fobrain
    - docker restart fobrain-merge-service-1
    - echo "重启完成"
    - echo "清理临时文件"
    - rm -rf $TEMP_DIR
    - echo "部署完成"
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event" &&
        ($CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "develop" || $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "testing")
      when: always
    - if: $CI_COMMIT_BRANCH == "develop" || $CI_COMMIT_BRANCH =~ /^testing.*$/
      when: always
    - when: never
  allow_failure: true

build-image:
  stage: build
  when: manual
  script:
    - cd $FOBRAIN_BUILD_DIR
    - git checkout .
    - git checkout $CI_COMMIT_BRANCH
    - git branch --set-upstream-to=origin/$CI_COMMIT_BRANCH
    - git pull
    - export CODE_VERSION=$CI_COMMIT_BRANCH-$(git rev-parse HEAD | cut -c 1-8)
    - echo $CODE_VERSION
    - cd $FOBRAIN_BUILD_DIR/fobrain && go build -ldflags "-X main.Version=$CODE_VERSION -X main.BuildTime=$(date +%Y-%m-%d_%H:%M:%S)" -o $FOBRAIN_BUILD_DIR/bin/fobrain_linux_amd64_$CODE_VERSION
    - cd $FOBRAIN_BUILD_DIR/mergeService && go build -o $FOBRAIN_BUILD_DIR/bin/mergeService_linux_amd64_$CODE_VERSION
    - cd $FOBRAIN_BUILD_DIR/cmd && go build -o $FOBRAIN_BUILD_DIR/bin/cmd_linux_amd64_$CODE_VERSION
    - cd $FOBRAIN_BUILD_DIR/adapter && go build -o $FOBRAIN_BUILD_DIR/bin/adapter_linux_amd64_$CODE_VERSION
    - echo "二进制镜像构建完成"
    - cp $FOBRAIN_BUILD_DIR/bin/cmd_linux_amd64_$CODE_VERSION $FOBRAIN_BUILD_DIR/install/amd64/build/fobrain/cmd
    - cp $FOBRAIN_BUILD_DIR/bin/fobrain_linux_amd64_$CODE_VERSION $FOBRAIN_BUILD_DIR/install/amd64/build/fobrain/fobrain
    - cp $FOBRAIN_BUILD_DIR/bin/mergeService_linux_amd64_$CODE_VERSION $FOBRAIN_BUILD_DIR/install/amd64/build/fobrain/mergeservice
    - cp $FOBRAIN_BUILD_DIR/bin/adapter_linux_amd64_$CODE_VERSION $FOBRAIN_BUILD_DIR/install/amd64/build/fobrain/adapter
    - cp $FOBRAIN_BUILD_DIR/version $FOBRAIN_BUILD_DIR/install/amd64/build/fobrain/version
    - cd $FOBRAIN_BUILD_DIR/install/amd64/build && docker build -f Dockerfile-fobrain -t fobrain:latest .
    - cd $FOBRAIN_BUILD_DIR/install/amd64/build && docker build -f Dockerfile-mergeService -t merge-service:latest .
    - cd $FOBRAIN_BUILD_DIR/install/amd64/build && docker build -f Dockerfile-fobrain-adapter -t fobrain-adapter:latest .
    - docker tag fobrain:latest $DOCKER_REGISTER_ADDR/fobrain/fobrain:latest
    - docker tag merge-service:latest $DOCKER_REGISTER_ADDR/fobrain/merge-service:latest
    - docker push $DOCKER_REGISTER_ADDR/fobrain/fobrain:latest
    - docker push $DOCKER_REGISTER_ADDR/fobrain/merge-service:latest
    - git checkout .
  environment:
    name: $CI_COMMIT_BRANCH
  tags:
    - build-image-149
  only:
    - testing
    - develop
  allow_failure: true

build-image-arm64:
  stage: build
  when: manual
  script:
    - cd $FOBRAIN_BUILD_DIR
    - git checkout .
    - git checkout $CI_COMMIT_BRANCH
    - git pull
    - goreleaser --snapshot --skip=validate --skip=publish --skip=sign --clean
    - cp $FOBRAIN_BUILD_DIR/bin/cmd_linux_arm64_0.0.1 $FOBRAIN_BUILD_DIR/install/arm64/build/fobrain/cmd
    - cp $FOBRAIN_BUILD_DIR/bin/fobrain_linux_arm64_0.0.1 $FOBRAIN_BUILD_DIR/install/arm64/build/fobrain/fobrain
    - cp $FOBRAIN_BUILD_DIR/bin/mergeService_linux_arm64_0.0.1 $FOBRAIN_BUILD_DIR/install/arm64/build/fobrain/mergeservice
    - cp $FOBRAIN_BUILD_DIR/version $FOBRAIN_BUILD_DIR/install/arm64/build/fobrain/version
    - cd $FOBRAIN_BUILD_DIR/install/arm64/build && docker build -f Dockerfile-fobrain -t fobrain-arm64:latest .
    - cd $FOBRAIN_BUILD_DIR/install/arm64/build && docker build -f Dockerfile-mergeService -t merge-service-arm64:latest .
    - docker tag fobrain-arm64:latest $DOCKER_REGISTER_ADDR/fobrain/fobrain-arm64:latest
    - docker tag merge-service-arm64:latest $DOCKER_REGISTER_ADDR/fobrain/merge-service-arm64:latest
    - docker push $DOCKER_REGISTER_ADDR/fobrain/fobrain-arm64:latest
    - docker push $DOCKER_REGISTER_ADDR/fobrain/merge-service-arm64:latest
    - git checkout .
  environment:
    name: $CI_COMMIT_BRANCH
  tags:
    - build-image-149
  only:
    - testing
    - develop
  allow_failure: true

# Merge 后 部署到 10.129
deploy-10.129-for-develop:
  stage: deploy
  when: manual
  script:
    - docker pull $DOCKER_REGISTER_ADDR/fobrain/fobrain:latest
    - docker tag $DOCKER_REGISTER_ADDR/fobrain/fobrain:latest fobrain:latest
    - docker pull $DOCKER_REGISTER_ADDR/fobrain/merge-service:latest
    - docker tag $DOCKER_REGISTER_ADDR/fobrain/merge-service:latest merge-service:latest
    - cd $FOBRAIN_BUILD_DIR/install/amd64/v2 && docker-compose down fobrain merge-service && docker-compose up -d --no-deps fobrain merge-service
  environment:
    name: $CI_COMMIT_BRANCH
  tags:
    - "10.129"
  only:
    - develop
  allow_failure: true

# Merge 后 部署到 10.149
deploy-10.149-for-develop:
  stage: deploy
  when: manual
  script:
    - docker pull $DOCKER_REGISTER_ADDR/fobrain/fobrain:latest
    - docker tag $DOCKER_REGISTER_ADDR/fobrain/fobrain:latest fobrain:latest
    - docker pull $DOCKER_REGISTER_ADDR/fobrain/merge-service:latest
    - docker tag $DOCKER_REGISTER_ADDR/fobrain/merge-service:latest merge-service:latest
    - cd $FOBRAIN_BUILD_DIR/install/amd64/v2 && docker-compose down fobrain merge-service && docker-compose up -d --no-deps fobrain merge-service
  environment:
    name: $CI_COMMIT_BRANCH
  tags:
    - build-image-149
  only:
    - develop
  allow_failure: true

e2e_automated_test-11.239:
  stage: automated_test_239
  when: manual
  image: cypress_e2e_test:v1.2
  script:
    - echo "当前服务器 IP 地址：" $(hostname -I)
    - sudo docker run --rm --network=host -v /data/cypress_ui_automation/fobrain_2.0_automated:/e2e -w /e2e --entrypoint /bin/bash cypress_e2e_test:v1.2 -c "npm run run-tests"
    - sudo docker ps -a | grep "Exited" | awk '{print $1}' | xargs docker rm
  environment:
    name: $CI_COMMIT_BRANCH
  tags:
    - fb2.0-11.239
  allow_failure: true
  only:
    - testing

# 打安装包
#install-pack-149:
#  stage: pack
#  when: manual
#  script:
#    - cd $FOBRAIN_BUILD_DIR
#    - git checkout .
#    - git checkout $CI_COMMIT_BRANCH
#    - git pull
#    - sh install-pack.sh $CI_COMMIT_BRANCH
#  environment:
#    name: $CI_COMMIT_BRANCH
#  tags:
#    - fb2.0-11.144
#  only:
#    - testing
#    - develop
#
#install-pack-arm64-149:
#  stage: pack
#  when: manual
#  script:
#    - cd $FOBRAIN_BUILD_DIR
#    - git checkout .
#    - git checkout $CI_COMMIT_BRANCH
#    - git pull
#    - sh install-pack-arm64.sh $CI_COMMIT_BRANCH
#  environment:
#    name: $CI_COMMIT_BRANCH
#  tags:
#    - fb2.0-11.144
#  only:
#    - testing
#    - develop
#
##打升级包
#upgrade-pack-149:
#  stage: pack
#  when: manual
#  script:
#    - cd $FOBRAIN_BUILD_DIR
#    - git checkout .
#    - git checkout $CI_COMMIT_BRANCH
#    - git pull
#    - sh upgrade-pack.sh $CI_COMMIT_BRANCH
#  environment:
#    name: $CI_COMMIT_BRANCH
#  tags:
#    - fb2.0-11.144
#  only:
#    - testing
#    - develop
#
#upgrade-pack-arm64-149:
#  stage: pack
#  when: manual
#  script:
#    - cd $FOBRAIN_BUILD_DIR
#    - git checkout .
#    - git checkout $CI_COMMIT_BRANCH
#    - git pull
#    - sh upgrade-pack-arm64.sh $CI_COMMIT_BRANCH
#  environment:
#    name: $CI_COMMIT_BRANCH
#  tags:
#    - fb2.0-11.144
#  only:
#    - testing
#    - develop