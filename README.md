[![testing pipeline status](https://git.gobies.org/caasm/fobrain/badges/testing/pipeline.svg?job=lint)](https://git.gobies.net/caasm/fobrain/-/commits/testing)
[![testing coverage report](https://git.gobies.org/caasm/fobrain/badges/testing/coverage.svg)](https://git.gobies.net/caasm/fobrain/-/commits/testing)
[![develop pipeline status](https://git.gobies.org/caasm/fobrain/badges/develop/pipeline.svg?job=lint)](https://git.gobies.net/caasm/fobrain/-/commits/develop)
[![develop coverage report](https://git.gobies.org/caasm/fobrain/badges/develop/coverage.svg)](https://git.gobies.net/caasm/fobrain/-/commits/develop)

# fobrain

### 代码目录结构说明

```
.
├── bin # 编译结果目录
├── cmd #命令行工具
│   ├── databases # 创建表和索引的脚本
│   ├── migrate # 数据迁移功能
├── fobrain # 主服务
│   ├── app # app
│   │   ├── controller # 控制器
│   │   ├── crontab # 定时服务
│   │   ├── repository # 仓储层
│   │   ├── request # 请求模型定义
│   │   ├── response # 响应模型定义
│   │   ├── services # 服务层
│   ├── common # 通用层
│   │   ├── auth # 接口验证相关
│   │   ├── captcha # 验证码相关
│   │   ├── constant # 常量定义
│   │   ├── license # 授权相关
│   │   ├── localtime # 时间封装
│   │   ├── middleware # 中间件
│   │   ├── request # 请求相关帮助类
│   │   ├── response # 响应相关帮助类
│   │   ├── validate # 验证相关帮助类
│   │   ├── wrapper # gin请求包装器
│   ├── config # 配置信息
│   ├── logs # 主服务日志文件夹
│   ├── main.go
│   ├── proto # 主服务rpc接口文件夹
│   ├── routes # 路由定义文件夹
│   └── tests # 单测文件夹
├── initialize # 全局初始化目录
│   ├── engine # gin初始化封装
│   ├── es # es功能封装
│   ├── mysql # mysql功能封装
│   └── redis # redis功能封装
├── install # 安装包生成目录
│   ├── amd64 # amd64 安装包结构
│   │   ├── build # 业务镜像build 目录
│   │   │   ├── fobrain # 配置存放目录
│   │   │   ├── nginx # nginx配置目录
│   │   └── fobrain # 最终安装包目录结构模板
│   │   └── v2 # 安装程序相关功能文件夹（app/public在storage文件夹下）
│   └── arm64 # arm64 安装包结构
├── mergeService # 融合微服务
│   ├── handler # 微服务接口
│   ├── main.go
│   ├── model # 融合相关模型
│   ├── proto # 融合服务rpc接口
│   └── utils # 融合工具类文件夹
├── models # 全局通用模型
│   ├── elastic # es索引模型映射
│   ├── mysql # 数据库表模型映射
│   └── redis # redis数据映射，包括key管理
├── pkg # 全局帮助类
│   ├── cfg # 配置相关帮助类
│   ├── distributedLock # 分布式锁帮助类
│   ├── fortest # 单测帮助类
│   ├── queue # 队列帮助类
│   ├── tracker # 追踪器帮助类
│   └── utils # 通用帮助类
├── storage # 存储文件夹
│   ├── app # 前端需要资源
│   └── log # 日志文件夹
└── upgrade # 升级程序相关文件夹
```

# fobrain架构说明及当前问题说明
当前有资产、漏洞、设备、人员（部门）、业务系统这五种主要的数据流转，包含同步、融合、展示等环节；

## 安装部署情况
fobrain使用容器部署，现有fobrain、adapter、merge-service、redis、es、mysql、nginx、consul（计划去掉）容器。fobrain通过rpc调用merge-service服务的接口
以资产为例： fobrain当前支持了多种类型数据源接入，包含扫描器、流量平台、堡垒机、云平台等。
这些数据源的数据被拉取过来后经过“数据清洗”进入过程表，然后经融合服务做策略处理和数据融合等操作，写入结果表

次要的数据
IP映射

## 融合拆分方案
### 背景
融合是fobrain的核心逻辑，代码较为复杂，但是基于资产、设备、漏洞、人员等业务拆分后，流程相似度较高，并且每个流程都会处理大量字段，在可预见的未来，还会在不同业务流程中添加字段。

基于上述，把融合核心流程拆分为独立框架，解决代码重复率高和扩展性差的问题。

### 整体思路
框架提供计算依据和计算方法，所有字段（包括未来需要新增的字段）可以基于该框架快速计算，所有字段计算完成形成最终结构，由框架调用另外的方法存储。

所以融合框架负责的是字段计算，基础数据的提供和结果入库需要单独拆分，由数据源提供方法查询基础数据，由结果表提供存储方法。

目前的所有字段根据上述框架改造。

### 预期
第一阶段：
融合框架拆分为独立包

第二阶段：
存储转移为外部依赖

第三阶段：
原始数据转移为外部依赖

## 数据源同步
##### 数据来源：
 扫描器、HIDS、CMDB、云平台、堡垒机、防火墙、LDAP等类型产品、人工导入
##### 当前情况：
数据流程：原始数据->过程表（数据清洗）-> 数据融合(资产、漏洞、设备、人员)->结果表

1. 大部分数据源即承担了数据拉取的功能也承担了任务管理的功能，没有拆出同步框架
2. 在开发期间拆了一版同步框架出来，做了任务管理和数据拉取的拆分，但还是存在耦合，同时也存在功能重复等问题

##### 目标方案：
fobrain拆完只保留框架，负责对接展示层、同步任务、数据写入功能，而拆到组件库的数据源更确切的是作为SDK角色，负责请求数据、解析返回值、数据映射到过程表等功能。
1. 每个数据源在组件库都能独立跑起来，可以不写入库，可以写入文件或打印关键信息。
2. 如果要接入一个新的数据源，只需要在组件库内完成SDK开发，将该数据源注册到fobrain框架内，即完成数据源的接入。不需要改动fobrain的数据源框架，如果展示层或同步任务需要，则在DataSourceFunc接口内新增一个方法提供能力
3. 进度：新的框架已有

## 融合框架

## 数据展示及其他
以业务系统为例：使用事件注册到融合服务，融合过程中通过事件更新业务系统及业务系统相关数据、优先级计算等，所有关于业务系统的变更都通过事件来触发。
后续人员、部门等资产属性变更，也使用相同的方式处理（未实现）。
## 当前问题瓶颈
1. 各类关系的维护不好，其中一个关系修改后，为了数据准确，会走全量融合，触发全量融合的场景很多
2. 基础服务封装度不够，过于分散和随意，主要是es和mysql，应该封装好通用的灵活的查询方法，减少重复性代码
3. 刚开始做的时候未严格按照规范，中间很多人参与开发，代码各部分耦合较高，风格各异
4. 业务模型关系复杂，比如人员部门变更引发业务系统变更和IP资产变更，资产变更引发工作台统计信息变更，其中既有**多层关系**变化，也有**一对多**的关系变化。
5. 性能瓶颈。当前大量依赖ES，包括大量查询、大量写入，而且业务分散，没有统一管理底层读写。
6. 代码重复冗余，数据同步和融合部分，大量代码重复，需要提取优化。
7. 扩展性差，同步新接数据源，融合新增字段等核心逻辑没有抽离，导致扩展性差，无法快速实现业务扩展。
8. 新接入的业务改动代码多，而且位置分散。比如IP资产新增一个字段，需要处理数据同步、数据融合、策略管理、字段计算、查询展示、人工校准、数据导出、关联关系、索引更新等
9.  push转pull的适配器

### 程序执行帮助

#### 环境搭建

1. 建议使用docker运行程序，以下步骤基于本地docker
2. 程序依赖：consul、redis、mysql、es
3. 可以通过手动启动，也可以进入`install/amd64/fobrain`目录，使用 `docker-compose up -d`命令启动
4. 依赖环境启动完成后，把`consul配置示例`中的内容保存为json文件并导入consul。注意各组件的账号密码修改。
5. 重启 `fobrain`和`merge-service`服务。（因为第一次启动consul缺少配置，fobrain和merge-service可能启动失败）
6. 使用cmd下的migrate命令初始化数据库和es索引。也可以使用项目根目录下的make命令封装`make migrate run`

#### consul配置示例

```json
[
	{
		"key": "fobrain/",
		"flags": 0,
		"value": ""
	},
	{
		"key": "fobrain/common",
		"flags": 0,
		"value": "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
	},
	{
		"key": "fobrain/elastic",
		"flags": 0,
		"value": "************************************************************************************************************************************"
	},
	{
		"key": "fobrain/logger",
		"flags": 0,
		"value": "ewogICAgIm91dHB1dF9jb25zb2xlIjogdHJ1ZSwKICAgICJvdXRwdXRfZmlsZSI6IHRydWUsCiAgICAiZmlsZV9uYW1lIjogImxvZ3MvbG9ncy5sb2ciLAogIAkiZmlsZV9uYW1lX3NjaGVkdWxlciI6Ii9kYXRhL2ZvYnJhaW4vc3RvcmFnZS9sb2dzL3NjaGVkdWxlci1sb2dzLmxvZyIsCiAgICAibGV2ZWwiOiAiaW5mbyIsCiAgICAibWF4X3NpemUiOiA4LAogICAgIm1heF9hZ2UiOiAzMCwKICAgICJsb2NhbF90aW1lIjogdHJ1ZSwKICAgICJjb21wcmVzcyI6IHRydWUsCiAgICAibWF4X2JhY2t1cHMiOiA1Cn0="
	},
	{
		"key": "fobrain/merge_logger",
		"flags": 0,
		"value": "ewogICAgIm91dHB1dF9jb25zb2xlIjogdHJ1ZSwKICAgICJvdXRwdXRfZmlsZSI6IHRydWUsCiAgICAiZmlsZV9uYW1lX3NlcnZpY2UiOiAiL2RhdGEvZm9icmFpbi9zdG9yYWdlL2xvZ3MvbWVyZ2Vfc2VydmljZS5sb2ciLAogICJmaWxlX25hbWVfZGV2aWNlIjogIi9kYXRhL2ZvYnJhaW4vc3RvcmFnZS9sb2dzL21lcmdlX2RldmljZS5sb2ciLAogICAgImZpbGVfbmFtZV9hc3NldCI6ICIvZGF0YS9mb2JyYWluL3N0b3JhZ2UvbG9ncy9tZXJnZV9hc3NldC5sb2ciLAogICJmaWxlX25hbWVfdnVsbiI6ICIvZGF0YS9mb2JyYWluL3N0b3JhZ2UvbG9ncy9tZXJnZV92dWxuLmxvZyIsCiAgImZpbGVfbmFtZV9wZXJzb24iOiAiL2RhdGEvZm9icmFpbi9zdG9yYWdlL2xvZ3MvbWVyZ2VfcGVyc29uLmxvZyIsCiAgICAibGV2ZWwiOiAiaW5mbyIsCiAgICAibWF4X3NpemUiOiAxMCwKICAgICJtYXhfYWdlIjogMzAsCiAgICAibG9jYWxfdGltZSI6IHRydWUsCiAgICAiY29tcHJlc3MiOiBmYWxzZSwKICAgICJtYXhfYmFja3VwcyI6IDIwCiAgfQ=="
	},
	{
		"key": "fobrain/mysql",
		"flags": 0,
		"value": "****************************************************************************************************************************************************************************************************************************************************************************"
	},
	{
		"key": "fobrain/queue",
		"flags": 0,
		"value": "eyAKICAiZGV2aWNlX21lcmdlX3F1ZXVlIjoiYXNzZXRfbWVyZ2VfcXVldWVfZGV2IiwKICAiZGV2aWNlX21lcmdlX2NvbmN1cnJlbnQiOjIwLAogICJhc3NldF9tZXJnZV9xdWV1ZSI6ImFzc2V0X21lcmdlX3F1ZXVlX2RldiIsCiAgImFzc2V0X21lcmdlX2NvbmN1cnJlbnQiOjIwLAogICJ2dWxuX21lcmdlX3F1ZXVlIjoidnVsbl9tZXJnZV9xdWV1ZV9kZXYiLAogICJ2dWxuX21lcmdlX2NvbmN1cnJlbnQiOjUsCiAgInZ1bG5fdXBkYXRlX3F1ZXVlIjoidnVsbl91cGRhdGVfcXVldWVfZGV2IiwKICAidnVsbl91cGRhdGVfY29uY3VycmVudCI6NSwKICAicGVyc29uX21lcmdlX3F1ZXVlIjoicGVyc29uX21lcmdlX3F1ZXVlX2RldiIsCiAgInBlcnNvbl9tZXJnZV9jb25jdXJyZW50Ijo1LAogICJkZWxfbXNnX2JhdGNoIjo1MDAsCiAgImVuZF9kZWxheSI6MTgwCn0="
	},
	{
		"key": "fobrain/redis",
		"flags": 0,
		"value": "****************************************************************************************************************************************"
	}
]

```



### 程序打包

#### FOBrain初始化安装
使用`install-pack.sh`脚本进行自动打包，`sh install-pack.sh $branch`, branch不写会默认打包develop分支的代码，可写tag
1. 该程序会复制服务器下的`install/amd64/v2`目录下的所有文件和`storage/app/public`文件夹到新建的`install-fobrain`目录
2. 并且会拉取`harbor.fofa.info`的最新编译出的Fobrain和MergeService的docker镜像
3. `cd cmd`编译cmd程序
4. 复制`/data/install-tools/`目录下所有文件到`install-fobrain`目录
5. 复制`/data/dist.tar.gz`到`install-fobrain`目录
6. 将`install-fobrain`文件夹打包为`.tar.gz`格式的压缩包，`fobrain-install-$branch.tar.gz`
fobrain2.0的镜像初始化为2.0.1，关于安装程序的使用请参考install.sh和ones内的文档

#### FOBrain升级
系统升级的详细说明及架构见（FOBrain系统升级.png）
1. 使用`upgrade-pack.sh`脚本进行自动打包，`sh upgrade-pack.sh $branch`, branch不写会默认打包develop分支的代码，可写tag
2. 该程序会编译fobrain、cmd、mergeService的代码，放到新建的upgrade-package文件夹
3. 会复制当前目录的`version-$branch`文件为`upgrade-package/version`
4. 会复制`./upgrade/system_upgrade.sh，/data/dist.tar.gz，./storage/app/public`到upgrade-package文件夹
5. 将上面的文件和文件夹打包为`.tar.gz`格式的压缩包，`fobrain-upgrade-$branch.tar.gz`
关于升级程序的使用请参考system_upgrade.sh和ones内的文档,对于version文件没发一次版都要新生成一个指定版本号的version文件


> CI/CD参考: .gitlab-ci.yml

> 镜像生成信息参考: install/amd64/build/Dockerfile

> 安装包生成信息参考: .gitlab-ci.yml 中的 build-local-amd64 部分

> 完整服务维护管理参考: install/amd64/fobrain/docker-compose.yaml

> 本地化安装信息参考: install/amd64/fobrain/install.sh

```shell
# 编译为可执行文件后运行下面的命令启动
# 服务启动
./fobrain -c ./fobrain.conf server run
# 迁移表
./fobrain -c ./fobrain.conf migrate run
```

```shell
# 本地热加载
# Mac安装 
curl -sSfL https://raw.githubusercontent.com/cosmtrek/air/master/install.sh | sh -s -- -b $(go env GOPATH)/bin
# 会自动编译二进制文件到 tmp目录，并执行 ./tmp/fobrain server run
# 修改代码保存后自动重启
# 服务启动
air server run
```

## 漏洞派发OA
### 背景
隆基项目需要将漏洞派发到OA系统，实质是配置webhook，然后通过fobrain各节点调用webhook sdk推送数据。

### 特性
- [ ] 内部
  - [x] 前后端交互
    - [x] webhook新建
      - [x] 鉴权方式选择（token）
      - [x] 鉴权方式列表及说明
      - [ ] 更复杂的场景定制开发（现在不做）
      - [x] 鉴权方式配置
      - [x] host
      - [ ] 是否需要代理
      - [ ] 代理配置
      - [x] 订阅的事件（事件名称+说明）
      - [ ] 备注
      - [ ] 记录创建日志
      - [ ] 后端返回可用事件列表
      - [x] 去重
    - [ ] webhook更新
      - [ ] 修改创建时填写的信息和选择的信息
      - [ ] 记录更新日志
      - [ ] 单个更新
      - [ ] 后端返回可用事件列表
    - [x] webhook删除
      - [x] 批量删除
      - [x] 全选删除
      - [ ] 记录删除日志
    - [x] webhook列表
      - [x] 分页
      - [ ] 筛选
      - [ ] 排序
      - [x] 搜索-host搜索
    - [ ] 事件发送结果看板
  - [x] 事件支持，fobrain各节点提供触发支持
    - [x] 漏洞事件-下发漏洞时发送一个
    - [ ] 漏洞事件-修复完成待复测时发送
    - [x] 漏洞事件-转交漏洞的时候发送
    - [ ] 数据源接入过程中各节点触发事件
    - [ ] 融合过程中各节点触发事件
    - [ ] 提取过程中各节点触发事件
    - [ ] 漏扫过程中各节点触发事件
    - [ ] 数据源异常时发送一个
    - [ ] 数据源恢复时发送一个等等
  - [x] 后端处理任务
    - [x] 通过MQ接收事件（不用channel避免升级等情况丢失消息，且channel无法持久化），用redis队列,队列不关心事件内容
    - [x] 起一个协程专门消费事件
    - [x] 根据webhook配置表调用对应的sdk，再起协程发送
    - [x] 起一个insertDB协程专门做日志入库
    - [x] 记录操作日志,打日志的方式
    - [ ] 高并发怎么支持
    - [ ] 支持通过方法给fobrain内部代码调用
    - [ ] 支持通过api给fobrain外部或前端调用
  - [x] 后端涉及的表
    - [x] webhook配置表
    - [ ] webhook配置表的日志表
    - [x] 事件发送结果日志表
  - [x] 后端高可用
    - [ ] 链路追踪
    - [x] 平滑退出各协程
    - [x] 发送失败时放回MQ并记录次数，设置相应的延时时间
    - [ ] 超时设置
    - [ ] 专门的死信队列
  - [ ] 后端监控告警
    - [ ] Prometheus接入统计，统计产生了多少事件，成功了多少，失败了多少，丢失了多少
    - [ ] 服务监控告警，fobrain挂掉告警
- [ ] 外部
  - [x] 客户的鉴权方式
  - [ ] 数据加密
  - [ ] 配置代理
  - [x] http post方式，通过body传递数据，以json格式，单条发送
    - [x] 漏洞id
    - [x] 漏洞名称
    - [x] 漏洞等级
    - [x] 修复的IP地址
    - [x] 漏洞修复优先级
    - [x] 漏洞派发时间
    - [x] 漏洞修复期限
    - [x] 漏洞复测时间
    - [x] 漏洞分配人
    - [ ] fofa查询语句
    - [ ] fofa跳转链接及短链
    - [ ] 漏洞详情跳转链接及短链
    - [ ] 漏洞状态
    - [ ] 漏洞涉及的业务系统列表
    - [ ] 漏洞涉及的资产列表
    - [ ] 漏洞负责人
    - [ ] 漏洞负责人邮箱
    - [ ] 漏洞负责人电话
    - [ ] 漏洞负责人微信
    - [ ] 漏洞负责人钉钉
    - [ ] 抄送领导，选择抄送几级领导
    - [ ] 支持选择要不要抄送
    - [ ] 事件消息发送给谁，怎么确定
	
### 架构
通过redis队列做事件消息的异步处理，各事件节点通过调用统一的Push方法放入队列，Push方法通过查询配置表获取对应的webhook配置，放入队列后返回，不阻塞。redis队列以json字符串传递调戏，fobrain启动时会起一个协程专门消费队列，根据消息内的事件和webhook配置调用对应的sdk起协程发送。


### es 增加新的字段
1. 新增的es索引文件字段在 [es_add_field](cmd/databases/elastic/es_add_field) 目录下
2. 增加类似mapping 内容文件，使用init函数注册
3. 文件内容如下 
   ```go
       func init() {
        RegisterMapping("xxxx", `
         {
             "index_name": "xxxx",
             "properties": {
                 "a_fieds": {
                     "type": "keyword"
                 },
                 "b_fied": {
                     "type": "keyword"
                 }
             }
         }
         `)
        }
      
4. 执行命令 go run main.go migrate run 命令实现迁移

### 存储目录
1、fobrain自身的数据存储，如文件上传，图片上传，系统升级，文件模板等使用的是/data目录
2、mysql使用的是/data目录,nginx的日志使用的是/data,redis和es也配置了使用/data目录
3、docker使用	/var/lib/docker目录已在安装程序中软链到/data目录

### 关于config.json说明
2.1.8版本后fobrain开始依赖config.json配置，去掉了从consul的配置中心读配置，去掉后有三个地方的config.json需要保持一致
构建amd镜像install/amd64/build/fobrain/fobrain.json
构建arm镜像install/arm64/build/fobrain/fobrain.json
打安装包和升级包依赖install/amd64/v2/config.json
在修改配置时这三个文件要一块改并保持一致，同时对于已经安装的fobrain要改配置，需要通过升级的方式替换
fobrain默认读取/etc/fobrain/conf/config.json
### version说明
文件内容是对一个json做的base64编码，json示例:
```json
{"product_model": "fobrain-2.2.9","release_version": "2.2.9","release_date": "2024-12-02","release_desc": "1、新增华为云; 2、新增CMDB域名和F5数据的接入; 3、新增华为8000E-X8防火墙、Fortinet防火墙配置文件的解析; 4、新增资产图谱功能模块; 5、新增部门维度数据展示","version_dependencies": ["2.0.1","2.1.8"]}
```

编码后是
```txt
eyJwcm9kdWN0X21vZGVsIjogImZvYnJhaW4tMi4yLjkiLCJyZWxlYXNlX3ZlcnNpb24iOiAiMi4yLjkiLCJyZWxlYXNlX2RhdGUiOiAiMjAyNC0xMi0wMiIsInJlbGVhc2VfZGVzYyI6ICIx44CB5paw5aKe5Y2O5Li65LqROyAy44CB5paw5aKeQ01EQuWfn+WQjeWSjEY15pWw5o2u55qE5o6l5YWlOyAz44CB5paw5aKe5Y2O5Li6ODAwMEUtWDjpmLLngavlopnjgIFGb3J0aW5ldOmYsueBq+WimemFjee9ruaWh+S7tueahOino+aekDsgNOOAgeaWsOWinui1hOS6p+WbvuiwseWKn+iDveaooeWdlzsgNeOAgeaWsOWinumDqOmXqOe7tOW6puaVsOaNruWxleekuiIsInZlcnNpb25fZGVwZW5kZW5jaWVzIjogWyIyLjAuMSIsIjIuMS44Il19
```

说明:
- release_version要升级的fobrain版本号
- release_date发版时间
- release_desc发版描述
- version_dependencies版本依赖，升级前先把必升版本升了

fobrain默认读取/etc/fobrain/conf/version文件，需要读写权限
