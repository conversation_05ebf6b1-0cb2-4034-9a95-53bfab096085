# 使用migrate执行数据库迁移
.PHONY: migrate-run
migrate-run:
	cd cmd && go run main.go migrate run -c $(Component)

# 使用migrate创建表
.PHONY: migrate-new
migrate-new:
	cd cmd && go run main.go migrate new $(TableName)

# 使用migrate执行数据库回滚
.PHONY: migrate-rollback
migrate-rollback:
	cd cmd && go run main.go migrate rollback

# 安装protoc-gen-go和protoc-gen-micro
.PHONY: proto-install
proto-install:
	@go get -u google.golang.org/protobuf/proto
	@go install github.com/golang/protobuf/protoc-gen-go@latest
	@go install github.com/asim/go-micro/cmd/protoc-gen-micro/v4@latest
	@go install github.com/favadi/protoc-go-inject-tag@latest

# 编译proto文件
.PHONY: proto
proto:
	@cd mergeService && protoc --proto_path=./proto --micro_out=. --go_out=. proto/*.proto && cd proto && protoc-go-inject-tag -input="*.pb.go" -remove_tag_comment

.PHONY: go-test
go-test:
	go test -failfast -gcflags=all=-l $(go list ./... | grep -v fobrain/module/backup | grep -v fobrain/module/zip | grep -v fobrain/mergeService/proto | grep -v fobrain/fobrain/app/response | grep -v fobrain/fobrain/databases | grep -v fobrain/fobrain/tests | grep -v fobrain/cmd | grep -v fobrain/fobrain/routes | grep -v fobrain/fobrain/common/middleware | grep -v fobrain/mergeService/model/test | grep -v fobrain/mergeService/model/field_tagger/real_test | grep -v fobrain/adapter/router | grep -v fobrain/pkg/utils/common_logs | grep -v fobrain/mergeService/event/event_handler/log_handler) -parallel 20 -p 4 -coverprofile=coverage.out -v

.PHONY: go-test-sum
go-test-sum:
	gotestsum --format testname -- \
	$$(go list ./... | grep -v fobrain/mergeService/proto | grep -v fobrain/fobrain/app/response | grep -v fobrain/fobrain/databases | grep -v fobrain/fobrain/tests | grep -v fobrain/cmd | grep -v fobrain/fobrain/routes | grep -v fobrain/fobrain/common/middleware | grep -v fobrain/mergeService/model/test | grep -v fobrain/mergeService/model/field_tagger/real_test | grep -v fobrain/adapter/router | grep -v fobrain/pkg/utils/common_logs | grep -v fobrain/mergeService/event/event_handler/log_handler) \
	-failfast \
	-gcflags=all=-l \
	-parallel 10 \
	-p 1 \
	-coverprofile=coverage.out \
	-v | tee source.out

.PHONY: cul_percentage
cul_percentage:
	awk '{ sub(/%/, "", $$3); print }' coverage.txt | sort -k3,3n > sorted_coverage.txt

.PHONY: func_coverage
func_coverage:
	go tool cover -func=coverage.out > coverage.txt
	make cul_percentage
	{ \
	  echo "| Function | Statements | Coverage | 负责人 |"; \
	  echo "|----------|------------|----------|----------|"; \
	  awk '{ printf("| %s | %s | %s%% |  |\n", $$1, $$2, $$3) }' sorted_coverage.txt; \
	} > final_sorted_coverage.md

# 代码复杂度
.PHONY: gocyclo
gocyclo:
	gocyclo -over $(num) -ignore ".pb.go|_test.go" .

# 跨平台编译fobrain
.PHONY: build-fobrain-linux
build-fobrain-linux:
	cd fobrain && GOOS=linux GOARCH=amd64 CGO_ENABLED=0 go build -ldflags="-X 'main.BuildTime=$(shell date +%Y-%m-%d_%H:%M:%S)' -X 'main.Version=$(shell git symbolic-ref HEAD | cut -b 12-)-$(shell git rev-parse HEAD)'" -o fobrain main.go

