.idea
.cursor
*.log
/bin
!/bin/.gitignore
dist/
tmp/
.vscode
quick_lint.sh
vendor
go.sum
.license
coverage.out
.DS_Store
source.out
coverage.txt
sorted_coverage.txt
final_sorted_coverage.md
cmd/cmd
fobrain/fobrain
mergeService/mergeService
*.tar
*.gz
/data
/dist
server
*self*
*Self*
upgrade/fswatch-1.17.1/*
upload_finish
upload_process
upgrade/storage
fobrain/app/services/system_configs/upgrade/version
fobrain/upgrade/version
*.xml
install/amd64/build/supervisor/supervisor/
*.html
upgrade/conf
consul*.json
upgrade/version
fobrain/app/services/system_configs/upgrade/upgrade_process
config.json
storage/app/ip_mapping
storage/app/backup_data
storage/app/backup_data_zip
forlocaltest
upgrade/upgrade_process
*-linux
go_build*
*.jpeg
*.jpg
fobrain/app/controller/storage/threat_history
migrate-cmd
.envrc.sh
adapter/adapter
adapter_linux_amd64
storage/nginx
storage/fobrain
storage/app/files
storage/logs
config-129.json
test.*
local-*
/storage/backup_data/
/storage/backup/*
/storage/backup_data_zip/
/storage/restore_data/
/storage/app/poc_upload/
storage/app/public/upgrade

# windsurf rules
.windsurfrules

config.*.json
.cunzhi-memory
.trae