package external_api

import (
	"fobrain/fobrain/app/controller/external_api"
	"fobrain/fobrain/common/wrapper"

	"github.com/gin-gonic/gin"
)

// RegisterExternalApi 注册外部 API
// @Summary 此处注册接口并不规范，仅兼容 1.0 平台与部分节点对接
func RegisterExternalApi(r *gin.Engine) {
	externalApi := r.Group("")
	{
		externalApi.POST("/foeyeApi/auth", wrapper.Fn(external_api.FoeyeAuth))
		externalApi.GET("/foeyeApi/auth/sftpPasswd", wrapper.Fn(external_api.FoeyeAuthSftpPasswd))
	}
}
