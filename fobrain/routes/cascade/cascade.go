package cascade

import (
	"fobrain/fobrain/common/wrapper"

	controller "fobrain/fobrain/app/controller/cascade"

	"github.com/gin-gonic/gin"
)

func RouterGroupRegisterByCascade(r *gin.RouterGroup) {
	cascade := r.Group("/cascade")
	{
		cascade.GET("/pull_data", wrapper.Fn(controller.PullCascadeData))
		cascade.GET("/asset_list", wrapper.Fn(controller.ListCascadeAsset))
		cascade.DELETE("/asset_list", wrapper.Fn(controller.DeleteCascadeAsset))

		cascade.GET("/vuln_list", wrapper.Fn(controller.ListCascadeVuln))
		cascade.DELETE("/vuln_list", wrapper.Fn(controller.DeleteCascadeVuln))

		cascade.GET("/record", wrapper.Fn(controller.ListCascadeSyncRecord))
		cascade.GET("/record/detail/asset_list", wrapper.Fn(controller.ListCascadeTaskAsset))
		cascade.GET("/record/detail/vul_list", wrapper.Fn(controller.ListCascadeTaskVuln))
	}
}
