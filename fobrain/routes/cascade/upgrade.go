package cascade

import (
	"fobrain/fobrain/app/controller/cascade"
	"fobrain/fobrain/common/wrapper"

	"github.com/gin-gonic/gin"
)

func RouterGroupRegisterByUpgrade(r *gin.RouterGroup) {
	upgrade := r.Group("/upgrade")
	{
		upgrade.GET("", wrapper.Fn(cascade.ListUpgradePackage))
		upgrade.POST("/upload", wrapper.Fn(cascade.CreateUpgradePackage))
		upgrade.DELETE("", wrapper.Fn(cascade.DeleteUpgradePackage))
		upgrade.POST("/distribute", wrapper.Fn(cascade.UpgradePackageDistribute))
		upgrade.POST("/sub_platform", wrapper.Fn(cascade.UpgradeSubPlatform))
		upgrade.POST("/upgrade_probe", wrapper.Fn(cascade.UpgradeProbe)) // 上级升级自身的探针
		upgrade.POST("/sub_platform/verify", wrapper.Fn(cascade.UpgradePackageSubPlatformVerify))
		upgrade.GET("/sub_platform", wrapper.Fn(cascade.ListSubPlatformUpgradeRecord))
		upgrade.GET("/sub_platform/new", wrapper.Fn(cascade.ListSubPlatformUnUpgradeRecord))
		upgrade.GET("/sub_platform/download", wrapper.Fn(cascade.DownloadUpgradePackage))
		upgrade.GET("/sub_platform/detail", wrapper.Fn(cascade.ListUpgradeRecordDetail))
	}
}
