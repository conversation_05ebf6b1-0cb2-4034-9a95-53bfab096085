package webhook

import (
	"fobrain/fobrain/common/wrapper"
	"fobrain/webhook"

	"github.com/gin-gonic/gin"
)

func RouterGroupRegisterBySystem(group *gin.RouterGroup) {
	r := group.Group("/webhook")
	{
		// webhook
		r.GET("/list", wrapper.Fn(webhook.EventSubscriptionList))
		r.POST("", wrapper.Fn(webhook.EventSubscription))
		r.DELETE("", wrapper.Fn(webhook.EventSubscriptionDelete))
		r.GET("/auth/type", wrapper.Fn(webhook.GetAuthTypeHandle))
	}
}
