package ip_ranges

import (
	"github.com/gin-gonic/gin"

	"fobrain/fobrain/app/controller/ip_ranges"
	"fobrain/fobrain/common/wrapper"
)

func RouterGroupRegisterByIpRanges(group *gin.RouterGroup) {
	// IP段管理
	r := group.Group("/ip_ranges")
	{
		r.POST("", wrapper.Fn(ip_ranges.Create))
		r.PUT("", wrapper.Fn(ip_ranges.Update))
		r.GET("/:id", wrapper.Fn(ip_ranges.First))
		r.DELETE("", wrapper.Fn(ip_ranges.DeleteByIds))
		r.GET("", wrapper.Fn(ip_ranges.List))
		r.GET("/merge", wrapper.Fn(ip_ranges.MergeUpdate))
		r.GET("/export", wrapper.Fn(ip_ranges.Export))
		r.POST("/import", wrapper.Fn(ip_ranges.IpRangesImport))
	}
}
