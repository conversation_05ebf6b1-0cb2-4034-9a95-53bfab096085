package compliance_monitor

import (
	"github.com/gin-gonic/gin"

	"fobrain/fobrain/app/controller/compliance_monitor"
	"fobrain/fobrain/common/wrapper"
)

func RouterGroupRegisterByComplianceMonitor(r *gin.RouterGroup) {
	settings := r.Group("/compliance_monitors")
	{
		// 合规监测列表
		settings.GET("", wrapper.Fn(compliance_monitor.Index))
		// 合规监测创建
		settings.POST("", wrapper.Fn(compliance_monitor.Store))
		// 合规监测编辑回填
		settings.GET("/:id/edit", wrapper.Fn(compliance_monitor.Edit))
		// 合规监测编辑
		settings.PUT("", wrapper.Fn(compliance_monitor.Update))
		// 合规监测启用禁用设置
		settings.PUT("/set_status", wrapper.Fn(compliance_monitor.SetStatus))
		// 立即执行（周期任务） or 再次执行 （已完成合规监测任务）
		settings.GET("/:id/start", wrapper.Fn(compliance_monitor.Exec))
		// 合规监测详情
		settings.GET("/:id", wrapper.Fn(compliance_monitor.Show))
		// 合规监测删除
		settings.DELETE("", wrapper.Fn(compliance_monitor.Destroy))
		settings.GET("/risk_ports_list", wrapper.Fn(compliance_monitor.RiskPortsList))
	}
}
