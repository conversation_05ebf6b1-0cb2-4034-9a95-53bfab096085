package merge_record

import (
	"fobrain/fobrain/app/controller/merge_record"
	"fobrain/fobrain/common/wrapper"

	"github.com/gin-gonic/gin"
)

func RouterGroupRegisterByMergeRecord(r *gin.RouterGroup) {
	group := r.Group("/merge_record")
	group.GET("/list", wrapper.Fn(merge_record.List))
	group.GET("/detail/:id", wrapper.Fn(merge_record.Detail))
	group.GET("/exceptions", wrapper.Fn(merge_record.ListExceptions))
}
