package file

import (
	"github.com/gin-gonic/gin"

	"fobrain/fobrain/app/controller/file"
	"fobrain/fobrain/common/wrapper"
)

func RouterGroupRegisterByFile(r *gin.RouterGroup) {
	// file管理
	authGroup := r.Group("/file")
	{
		// 上传图标
		authGroup.POST("/upload/icon", wrapper.Fn(file.UploadIcon))
		authGroup.GET("/logs/list", wrapper.Fn(file.ListLogFiles))
		authGroup.POST("/logs/download", wrapper.Fn(file.DownloadLogFiles))
	}
}
