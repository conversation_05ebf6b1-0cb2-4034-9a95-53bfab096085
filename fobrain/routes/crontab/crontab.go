package crontab

import (
	"fobrain/fobrain/app/controller/crontab"
	"fobrain/fobrain/common/wrapper"

	"github.com/gin-gonic/gin"
)

func RouterGroupRegisterByCrontab(r *gin.RouterGroup) {
	crontabs := r.Group("/crontab")
	{
		// 列表
		crontabs.GET("", wrapper.Fn(crontab.List))
		// 添加
		crontabs.POST("", wrapper.Fn(crontab.Add))
		// 更新
		crontabs.POST("/:id", wrapper.Fn(crontab.Update))
		// 删除
		crontabs.DELETE("", wrapper.Fn(crontab.Delete))
		// 执行记录列表
		crontabs.GET("/:id/history", wrapper.Fn(crontab.HistoryList))
		// 执行记录清除
		crontabs.DELETE("/:id/history", wrapper.Fn(crontab.HistoryClear))
	}
}
