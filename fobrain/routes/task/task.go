package task

import (
	"github.com/gin-gonic/gin"

	"fobrain/fobrain/app/controller/task/foeye_proactive_scanning"

	"fobrain/fobrain/app/controller/task"
	"fobrain/fobrain/common/wrapper"
)

func RouterGroupRegisterByTask(r *gin.RouterGroup) {
	settings := r.Group("/task")
	{
		// 同步节点数据
		settings.GET("/sync", wrapper.Fn(task.Sync))
		settings.GET("/sync/all", wrapper.Fn(task.SyncAll))
		settings.POST("/sync/file", wrapper.Fn(task.SyncFile))
		settings.POST("/sync/data", wrapper.Fn(task.SyncData))
		settings.GET("/child_list", wrapper.Fn(task.ChildList))
		settings.GET("/list", wrapper.Fn(task.List))
	}

	proactiveScan := r.Group("/task_center/proactive_scan/foeye")
	{
		proactiveScan.POST("", wrapper.Fn(foeye_proactive_scanning.Create))
		proactiveScan.GET("/scan_tool", wrapper.Fn(foeye_proactive_scanning.ScanTool))
		proactiveScan.GET("/scan_node", wrapper.Fn(foeye_proactive_scanning.ScanNode))
		proactiveScan.GET("/once_list", wrapper.Fn(foeye_proactive_scanning.ListOnce))
		proactiveScan.GET("/scheduled_list", wrapper.Fn(foeye_proactive_scanning.GetScheduledTasksList))
		proactiveScan.DELETE("", wrapper.Fn(foeye_proactive_scanning.DelTask))
		proactiveScan.GET("/ports", wrapper.Fn(foeye_proactive_scanning.PortGroupList))
		proactiveScan.GET("", wrapper.Fn(foeye_proactive_scanning.GetTaskDetail))
		proactiveScan.POST("/retry", wrapper.Fn(foeye_proactive_scanning.RetryTask))
		proactiveScan.PUT("/update", wrapper.Fn(foeye_proactive_scanning.UpdateTask))
		proactiveScan.POST("/start", wrapper.Fn(foeye_proactive_scanning.StartTask))
		proactiveScan.PUT("/is_open", wrapper.Fn(foeye_proactive_scanning.IsOpenTask))
		proactiveScan.GET("/asset_result", wrapper.Fn(foeye_proactive_scanning.Asset))
		proactiveScan.GET("/threat_result", wrapper.Fn(foeye_proactive_scanning.Threat))
		proactiveScan.GET("/data_export", wrapper.Fn(foeye_proactive_scanning.ExportAsset))
		proactiveScan.GET("/export_task_bulletin", wrapper.Fn(foeye_proactive_scanning.ExportTaskBulletin))
	}

	// 后续会将上面有 foeye 的 URL 去除
	proactiveScans := r.Group("/task_center/proactive_scan")
	{
		proactiveScans.GET("/scan_types", wrapper.Fn(foeye_proactive_scanning.ScanTypes))
		// 绿盟漏扫，漏洞模板列表
		proactiveScans.GET("/nsfocus/templates", wrapper.Fn(task.VulTemplateList))
	}
}
