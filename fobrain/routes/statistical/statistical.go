package statistical

import (
	statistical2 "fobrain/fobrain/app/controller/statistical"
	"fobrain/fobrain/app/controller/user_access"
	"fobrain/fobrain/common/wrapper"
	"github.com/gin-gonic/gin"
)

func RouterGroupRegisterByStatistical(r *gin.RouterGroup) {
	statistical := r.Group("/statistical")
	{
		statistical.GET("/list", wrapper.Fn(statistical2.TreeList))
		statistical.POST("/info", wrapper.Fn(statistical2.Count))
		statistical.GET("/asset", wrapper.Fn(statistical2.Assets))
		statistical.PUT("/rule_info/level/config", wrapper.Fn(user_access.UpdateUserRuleInfoConfig))
	}

}
