package workbench

import (
	"fobrain/fobrain/app/controller/workbench_ctl"
	"fobrain/fobrain/app/controller/workbench_ctl/data_board_config"
	"fobrain/fobrain/common/wrapper"

	"github.com/gin-gonic/gin"
)

func RouterGroupRegisterByWorkbench(group *gin.RouterGroup) {
	r := group.Group("/workbench")
	{
		r.GET("/head/stats", wrapper.Fn(workbench_ctl.WorkbenchHeadStats))
		r.GET("/alarm/list", wrapper.Fn(workbench_ctl.NotifyAlarmList))
		r.GET("/alarm/single", wrapper.Fn(workbench_ctl.NotifyAlarmSingle))
		r.POST("/alarm/add", wrapper.Fn(workbench_ctl.NotifyAlarmAdd))
		r.GET("/alarm/remind/list", wrapper.Fn(workbench_ctl.NotifyAlarmRemindList))
		r.PUT("/alarm/remind/read", wrapper.Fn(workbench_ctl.ReadNotifyAlarmRemind))
		r.GET("/probe/overview", wrapper.Fn(workbench_ctl.ProbeOverview))
		r.GET("/probe/asset/contribute", wrapper.Fn(workbench_ctl.ProbeAssetContributionTop))
		r.GET("/asset/security/coverage", wrapper.Fn(workbench_ctl.AssetSecurityCoverage))
		r.GET("/weekly/task/overview", wrapper.Fn(workbench_ctl.WeeklyTaskOverview))
		r.GET("/asset/result", wrapper.Fn(workbench_ctl.AssetsResult))
		r.GET("/data/process/overview", wrapper.Fn(workbench_ctl.DataProcessOverview))
		r.GET("/vulnerability/overview", wrapper.Fn(workbench_ctl.VulnerabilityOverview))
		r.GET("/asset/exempt", wrapper.Fn(workbench_ctl.AssetExemptList))
		r.POST("/asset/exempt", wrapper.Fn(workbench_ctl.AssetExemptSave))
		r.DELETE("/asset/exempt", wrapper.Fn(workbench_ctl.AssetExemptDelete))

		// 资产全景统计
		r.GET("/asset/panorama", wrapper.Fn(workbench_ctl.AssetPanorama))

		// 看板配置
		r.GET("/custom_board", wrapper.Fn(data_board_config.DataBoardConfigList))
		r.POST("/custom_board", wrapper.Fn(data_board_config.DataBoardConfig))
		r.POST("/board_data", wrapper.Fn(data_board_config.GetBoardData))

		// 衡量指标配置
		r.GET("/data_interpretation", wrapper.Fn(workbench_ctl.DataInterpretation))
		r.PUT("/data_interpretation", wrapper.Fn(workbench_ctl.DataInterpretationUpdate))
		r.GET("/floating/box/data", wrapper.Fn(workbench_ctl.FloatingBoxData))
	}
}

/*

// 有延迟的
数据概览 定时15min 查es和mysql
资产安全覆盖度 缓存2秒查es 计算百分比
数据处理概览 关心每天的量，每小时更新一次 查es

// 实时统计查询的
通知告警列表 实时查mysql
通知告警详情 实时查mysql
通知告警弹窗列表 实时查mysql
通知告警弹窗已读 实时查mysql

探针情况概览 实时查mysql

探针资产贡献度top5 实时查es 计算百分比

近一周上报任务概率 实时查mysql

资产处理结果 实时查es

漏洞概览 实时查es

资产豁免配置列表 		实时查mysql
资产豁免配置新增单条	实时mysql
资产豁免配置删除单条	实时mysql
*/
