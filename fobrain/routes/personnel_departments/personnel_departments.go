package personnel_departments

import (
	"github.com/gin-gonic/gin"

	"fobrain/fobrain/app/controller/personnel_departments"
	"fobrain/fobrain/common/wrapper"
)

func RouterGroupRegisterByPersonnelDepartments(group *gin.RouterGroup) {
	// IP段管理
	r := group.Group("/personnel_departments")
	{
		r.DELETE("", wrapper.Fn(personnel_departments.DeleteByIds))
		r.POST("", wrapper.Fn(personnel_departments.Create))
		r.GET("/:id", wrapper.Fn(personnel_departments.First))
		r.PUT("", wrapper.Fn(personnel_departments.Update))
		r.GET("", wrapper.Fn(personnel_departments.List))
		r.GET("/all/tree/list", wrapper.Fn(personnel_departments.AllTreeList))
		r.POST("/department_import", wrapper.Fn(personnel_departments.Import))
		r.GET("/check/del", wrapper.Fn(personnel_departments.CheckDel))
	}
}
