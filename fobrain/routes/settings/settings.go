package settings

import (
	"fobrain/fobrain/app/controller/settings/email"
	"fobrain/fobrain/app/controller/settings/license"
	"fobrain/fobrain/app/controller/settings/network_areas"
	"fobrain/fobrain/app/controller/settings/sso"
	"fobrain/fobrain/app/controller/system_configs/cascade_api_token"
	"fobrain/fobrain/app/controller/system_configs/config"
	"fobrain/fobrain/app/controller/system_configs/systemInfo"

	"github.com/gin-gonic/gin"

	"fobrain/fobrain/app/controller/system_configs/intranet_ips"
	"fobrain/fobrain/app/controller/system_configs/networks"
	peoplepgid "fobrain/fobrain/app/controller/system_configs/people_pgid"
	"fobrain/fobrain/common/wrapper"
)

func RouterGroupRegisterBySettings(r *gin.RouterGroup) {
	settings := r.Group("/settings")
	{
		// 网络区域管理
		settings.GET("/network_areas", wrapper.Fn(network_areas.List))
		settings.DELETE("/network_areas", wrapper.Fn(network_areas.Delete))
		settings.POST("/network_areas", wrapper.Fn(network_areas.Create))
		settings.GET("/network_areas/isp", wrapper.Fn(network_areas.IspList))
		settings.GET("/network_areas/geo", wrapper.Fn(network_areas.GeoList))

		// license 系统激活
		settings.POST("/license", wrapper.Fn(license.Activation))
		settings.GET("/retrieve_system_usage", wrapper.Fn(systemInfo.RetrieveSystemUsage))
		settings.POST("/allow_add_node", wrapper.Fn(license.AllowAddSource))
		//内网网段列表
		settings.GET("/intranet_ips", wrapper.Fn(intranet_ips.IntranetIpList))

		//内网网段列表修改
		settings.PUT("/intranet_ips", wrapper.Fn(intranet_ips.IntranetIpListUpdate))

		//内网网段列表
		settings.GET("/networks", wrapper.Fn(networks.GetNetworksSettings))

		//内网网段列表
		settings.PUT("/networks", wrapper.Fn(networks.UpdateNetworkSettings))

		//内网网段列表
		settings.POST("/ping", wrapper.Fn(networks.Ping))

		// host配置
		settings.GET("host_config", wrapper.Fn(networks.GetHostConfig))

		//host配置修改
		settings.PUT("host_config", wrapper.Fn(networks.UpdateHostConfig))

		settingsEmail := r.Group("/settings/email")
		{
			settingsEmail.GET("", wrapper.Fn(email.Infos))
			settingsEmail.POST("", wrapper.Fn(email.Save))
			settingsEmail.GET("test", wrapper.Fn(email.TestMail))
		}

		// 级联api token
		settings.GET("cascade_api_token", wrapper.Fn(cascade_api_token.GetCascadeApiToken))
		settings.GET("cascade_api_token/refresh", wrapper.Fn(cascade_api_token.RefreshCascadeApiToken))

		settingsSSO := r.Group("/settings/sso")
		{
			settingsSSO.GET("", wrapper.Fn(sso.GetDetails))
			settingsSSO.GET("/sso_server", wrapper.Fn(sso.ServerList))
			settingsSSO.PUT("", wrapper.Fn(sso.ChangedDetails))
		}

		settingsPeoplePgid := r.Group("/settings/people_pgid")
		{
			settingsPeoplePgid.GET("", wrapper.Fn(peoplepgid.GetPeoplePgid))
			settingsPeoplePgid.POST("/:value", wrapper.Fn(peoplepgid.SetPeoplePgid))
		}

		// 通用配置
		settingsConfig := r.Group("/settings/config")
		{
			// 获取系统配置
			settingsConfig.GET("", wrapper.Fn(config.GetSystemConfig))
			// 创建或更新系统配置
			settingsConfig.POST("", wrapper.Fn(config.CreateOrUpdateSystemConfig))
		}
	}
}
