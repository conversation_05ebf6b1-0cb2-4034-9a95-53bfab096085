package routes

import (
	cascade2 "fobrain/fobrain/app/controller/cascade"
	"fobrain/fobrain/app/controller/settings/license"
	"fobrain/fobrain/app/controller/system_configs/backup"
	"fobrain/fobrain/app/controller/system_configs/systemInfo"
	"fobrain/fobrain/routes/asset_center"
	"fobrain/fobrain/routes/asset_graph"
	"fobrain/fobrain/routes/asset_mapping"
	"fobrain/fobrain/routes/cascade"
	"fobrain/fobrain/routes/compliance_monitor"
	"fobrain/fobrain/routes/compliance_monitor_task"
	"fobrain/fobrain/routes/custom_field"
	"fobrain/fobrain/routes/custom_tags"
	"fobrain/fobrain/routes/external_api"
	"fobrain/fobrain/routes/field_tag_rules"
	"fobrain/fobrain/routes/ip_ranges"
	"fobrain/fobrain/routes/logs"
	"fobrain/fobrain/routes/merge_record"
	"fobrain/fobrain/routes/net_mapping"
	"fobrain/fobrain/routes/personnel_departments"
	"fobrain/fobrain/routes/report"
	"fobrain/fobrain/routes/statistical"
	"fobrain/fobrain/routes/strategy"
	"fobrain/fobrain/routes/system"
	"fobrain/fobrain/routes/threat_center"
	"fobrain/fobrain/routes/user_access"
	fobrainWebhook "fobrain/fobrain/routes/webhook"
	"fobrain/fobrain/routes/workbench"
	"fobrain/models/mysql/system_configs"
	"fobrain/pkg/oauth"
	"fobrain/webhook"

	"fobrain/fobrain/app/controller/auth"
	"fobrain/fobrain/app/controller/file"
	backup_v2 "fobrain/fobrain/app/controller/system_configs"
	"fobrain/fobrain/app/websockt/foeye"
	"fobrain/fobrain/common/wrapper"
	"fobrain/fobrain/common/wsocket"
	routerAuth "fobrain/fobrain/routes/auth"
	"fobrain/fobrain/routes/crontab"
	"fobrain/fobrain/routes/custom_column"
	rfile "fobrain/fobrain/routes/file"
	"fobrain/fobrain/routes/ip_mapping"
	"fobrain/fobrain/routes/settings"
	"fobrain/fobrain/routes/sources"
	"fobrain/fobrain/routes/task"
	"fobrain/fobrain/routes/user"
	"fobrain/fobrain/routes/vulnerability"

	"github.com/gin-gonic/gin"
)

// InitNoAuthRouter 初始化无权限管理路由
func InitNoAuthRouter(v1 *gin.RouterGroup, r *gin.Engine) {
	// WebSocket
	r.GET("/websocket", wrapper.Fn(wsocket.GetWSocket().WebSocketHandle))
	r.GET("/oauth2_login", wrapper.Fn(auth.Oauth2Login))
	oauth.BindToRouterGroup(r.Group(""), oauth.SSOCfg(system_configs.SSOConfigs()), oauth.HandleSSOUserInfo)

	// 心跳
	v1.GET("/heartbeat", func(c *gin.Context) { c.String(200, "okokok") })
	// 验证码
	v1.GET("/captcha", wrapper.Fn(auth.Captcha))
	// 下载指定文件
	v1.GET("/template/:template", wrapper.Fn(file.File))
	// 下载Storage文件
	v1.GET("/download/import_file/:template", wrapper.Fn(file.Storage))
	// 图片展示
	v1.GET("/storage/icon/:template", wrapper.Fn(file.ImageIcon))
	v1.GET("/storage/logo/:template", wrapper.Fn(file.ImageLogo))

	// 下载模板文件
	v1.GET("/download/template/:id", wrapper.Fn(file.Template))
	// 权限管理
	routerAuth.RouterGroupRegisterByAuth(v1)
	v1.GET("/license", wrapper.Fn(license.Expire))
	// test
	v1.GET("/hi", wrapper.Fn(auth.Hi))
	v1.GET("/system/info/conf", wrapper.Fn(systemInfo.ListSystemInfoConfig))
	v1.POST("/send/msg", webhook.SendWebhookMsg)
	v1.GET("/version", wrapper.Fn(auth.Version))

	// 获取隐藏菜单
	v1.GET("/system/info/hidden_menu", wrapper.Fn(systemInfo.HiddenMenu))
	// 获取系统菜单
	v1.GET("/system/info/menus", wrapper.Fn(systemInfo.Menus))
	// 上级升级包下载接口
	v1.POST("/upgrade/download", wrapper.Fn(cascade2.UpgradePackage))
	// 备份恢复进度接口
	v1.GET("/system/backup/process", wrapper.Fn(backup.GetProcess))

	v1.GET("/system/backup/v2/lock/status", wrapper.Fn(backup_v2.GetLockStatus))
	// 初始化不需要权限的外部接口
	external_api.RegisterExternalApi(r)
}

// InitAuthRouter 初始化权限管理路由
func InitAuthRouter(v1 *gin.RouterGroup) {
	// 系统配置
	settings.RouterGroupRegisterBySettings(v1)
	// 用户信息
	user.RouterGroupRegisterByUser(v1)
	// 定时任务管理
	crontab.RouterGroupRegisterByCrontab(v1)
	// 同步任务管理
	task.RouterGroupRegisterByTask(v1)
	// 数据源管理
	sources.RouterGroupRegisterBySources(v1)
	// 节点管理
	sources.RouterGroupRegisterByNodes(v1)
	// 节点数据管理
	sources.RouterGroupRegisterByNodeData(v1)
	// 资产中心
	asset_center.RouterGroupRegisterByAssetCenter(v1)
	// 漏洞中心
	threat_center.RouterGroupRegisterByThreatCenter(v1)
	// 策略管理
	strategy.RouterGroupRegisterByStrategy(v1)
	// 内外网映射关系管理
	ip_mapping.RouterGroupRegisterByIpMapping(v1)
	net_mapping.RouterGroupRegisterByNetMapping(v1)
	// file管理
	rfile.RouterGroupRegisterByFile(v1)

	// 漏洞知识库
	vulnerability.RouterGroupRegisterByVulnerability(v1)

	// 系统信息/系统管理
	system.RouterGroupRegisterBySystem(v1)
	fobrainWebhook.RouterGroupRegisterBySystem(v1)
	// 审计日志
	logs.RouterGroupRegisterByAuditLogs(v1)
	logs.RouterGroupRegisterBySystemLogs(v1)

	// 合规监测
	compliance_monitor.RouterGroupRegisterByComplianceMonitor(v1)
	// 合规监测任务
	compliance_monitor_task.RouterGroupRegisterByComplianceMonitorTask(v1)

	// 工作台
	workbench.RouterGroupRegisterByWorkbench(v1)
	logs.RouterGroupRegisterByQcLogsSetting(v1)

	// 报告管理
	report.RouterGroupReportRegister(v1)
	// ip段管理
	ip_ranges.RouterGroupRegisterByIpRanges(v1)
	// 用户/权限管理
	user_access.RouterGroupRegisterByUserAccess(v1)
	// 人员部门
	personnel_departments.RouterGroupRegisterByPersonnelDepartments(v1)

	// 级联数据
	cascade.RouterGroupRegisterByCascade(v1)

	// 自定义字段
	custom_field.RouterGroupRegisterByCustomField(v1)

	//人行资产映射
	asset_mapping.RouterGroupRegisterByAssetMapping(v1)

	// 级联更新
	cascade.RouterGroupRegisterByUpgrade(v1)

	// 字段标签规则
	field_tag_rules.RouterGroupRegisterByFieldTagRules(v1)
	// 自定义标签
	custom_tags.RouterGroupRegisterByCustomTags(v1)
	//统计分析
	statistical.RouterGroupRegisterByStatistical(v1)
	// 资产图谱
	asset_graph.RouterGroupRegisterByAssetGraph(v1)
	// 融合记录
	merge_record.RouterGroupRegisterByMergeRecord(v1)
	// 自定义列
	custom_column.RegisterCustomColumnRoutes(v1)
}

// InitWebsocketRouter 初始化websocket路由
func InitWebsocketRouter(w *wsocket.WScoket) {
	// 分组解析设置
	w.SetGroupParseHandler("foeye", foeye.Parse)
	// foeye 注册节点
	w.Register("foeye", "register", foeye.Register)
}
