package monitor

import (
	"fobrain/fobrain/app/controller/monitor"
	"fobrain/fobrain/common/wrapper"
	"github.com/gin-gonic/gin"
)

func RouterGroupRegisterByMonitor(r *gin.RouterGroup) {
	// 任务
	monitorRule := r.Group("/monitor")
	{
		monitorRule.GET("", wrapper.Fn(monitor.List))             // 任务列表
		monitorRule.DELETE("/delete", wrapper.Fn(monitor.Delete)) // 批量删除任务
		monitorRule.PUT("/update", wrapper.Fn(monitor.Update))    // 编辑任务
		monitorRule.POST("/create", wrapper.Fn(monitor.Insert))   // 新建任务
		monitorRule.GET("/rules", wrapper.Fn(monitor.RuleList))   // 查看当前任务的规则
	}
	// 资产
	monitorAsset := r.Group("/monitor_asset")
	{
		monitorAsset.GET("", wrapper.Fn(monitor.ListAsset))                   // 当前任务对应的资产列表
		monitorAsset.DELETE("/delete_asset", wrapper.Fn(monitor.DeleteAsset)) // 批量删除资产
		monitorAsset.GET("/query_asset", wrapper.Fn(monitor.ExecuteSelect))   // 根据规则来查询资产(立即执行)
		monitorAsset.POST("/export_asset", wrapper.Fn(monitor.ExportAsset))   // 批量导出资产
		monitorAsset.POST("/switch", wrapper.Fn(monitor.Switch))              // 周期任务开关
	}
}
