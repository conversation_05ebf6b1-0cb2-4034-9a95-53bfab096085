package asset_center

import (
	"github.com/gin-gonic/gin"

	"fobrain/fobrain/app/controller/asset_center/business_strategy"

	"fobrain/fobrain/app/controller/asset_center"
	"fobrain/fobrain/app/controller/asset_center/business_system"
	deviceasset "fobrain/fobrain/app/controller/asset_center/device_asset"
	"fobrain/fobrain/app/controller/asset_center/external_ip_asset"
	"fobrain/fobrain/app/controller/asset_center/internal_asset"
	"fobrain/fobrain/app/controller/asset_center/personnel_ledger"
	"fobrain/fobrain/app/controller/asset_center/search_condition"
	"fobrain/fobrain/common/wrapper"
)

// RouterGroupRegisterByAssetCenter
// 资产中心
func RouterGroupRegisterByAssetCenter(r *gin.RouterGroup) {
	// 人员台账
	personnelLedger := r.Group("/personnel_ledger")
	{
		personnelLedger.GET("", wrapper.Fn(personnel_ledger.List))
		personnelLedger.DELETE("", wrapper.Fn(personnel_ledger.DeleteByIds))
		personnelLedger.GET("/department", wrapper.Fn(personnel_ledger.DepartmentList))
		personnelLedger.PUT("/department", wrapper.Fn(personnel_ledger.DepartmentUpdate))
		personnelLedger.DELETE("/department", wrapper.Fn(personnel_ledger.DepartmentDelete))
		// 人工校准
		personnelLedger.POST("/manual_calibration", wrapper.Fn(personnel_ledger.ManualCalibration))
		personnelLedger.GET("/position", wrapper.Fn(personnel_ledger.Position))
	}

	// 内网资产
	internalAsset := r.Group("/internal_asset")
	{
		internalAsset.GET("", wrapper.Fn(internal_asset.List))
		internalAsset.DELETE("", wrapper.Fn(internal_asset.DeleteByIds))
		internalAsset.GET("/:id", wrapper.Fn(internal_asset.Show))
		internalAsset.GET("/recycle_bin", wrapper.Fn(internal_asset.RecycleBin))
		internalAsset.POST("/reduction", wrapper.Fn(internal_asset.Reduction))
		internalAsset.POST("/purge", wrapper.Fn(internal_asset.PurgeByIds))
		internalAsset.GET("/export", wrapper.Fn(internal_asset.Export))
		internalAsset.GET("/recycle_bin_export", wrapper.Fn(internal_asset.RecycleBinExport))
		internalAsset.POST("/manual_calibration", wrapper.Fn(internal_asset.ManualCalibration))
		internalAsset.GET("/ip_base_info", wrapper.Fn(internal_asset.IpBaseInfo))
		internalAsset.GET("/ip_admin_info", wrapper.Fn(internal_asset.IpAdminInfo))
		internalAsset.GET("/ip_port_info", wrapper.Fn(internal_asset.IpPortInfo))
		internalAsset.GET("/ip_host_info", wrapper.Fn(internal_asset.IpHostInfo))
		internalAsset.GET("/ip_security_info", wrapper.Fn(internal_asset.IpSecurityInfo))
		internalAsset.GET("/ip_traceability_base_info", wrapper.Fn(internal_asset.IpTraceabilityBaseInfo))
		internalAsset.GET("/ip_traceability_process_info", wrapper.Fn(internal_asset.IpTraceabilityProcessInfo))
		internalAsset.POST("/ip_traceability_detail_info", wrapper.Fn(internal_asset.IpTraceabilityDetailInfo))
	}
	asset := r.Group("/asset")
	{
		asset.GET("", wrapper.Fn(asset_center.List))
		asset.GET("/rule_infos", wrapper.Fn(asset_center.IpRuleInfos))
	}
	// 外网IP资产
	externalIpAsset := r.Group("/external_ip_asset")
	{
		externalIpAsset.GET("", wrapper.Fn(external_ip_asset.List))
		externalIpAsset.DELETE("", wrapper.Fn(external_ip_asset.DeleteByIds))
		externalIpAsset.GET("/:id", wrapper.Fn(external_ip_asset.Show))
		externalIpAsset.GET("/recycle_bin", wrapper.Fn(external_ip_asset.RecycleBin))
		externalIpAsset.POST("/reduction", wrapper.Fn(external_ip_asset.Reduction))
		externalIpAsset.POST("/purge", wrapper.Fn(external_ip_asset.PurgeByIds))
		externalIpAsset.GET("/export", wrapper.Fn(external_ip_asset.Export))
		externalIpAsset.GET("/recycle_bin_export", wrapper.Fn(external_ip_asset.RecycleBinExport))

		// 手动校准，使用内网资产的接口
		externalIpAsset.POST("/manual_calibration", wrapper.Fn(internal_asset.ManualCalibration))
		// 注意：以下溯源相关接口使用内网资产的接口
		externalIpAsset.GET("/ip_base_info", wrapper.Fn(internal_asset.IpBaseInfo))
		externalIpAsset.GET("/ip_admin_info", wrapper.Fn(internal_asset.IpAdminInfo))
		externalIpAsset.GET("/ip_port_info", wrapper.Fn(internal_asset.IpPortInfo))
		externalIpAsset.GET("/ip_host_info", wrapper.Fn(internal_asset.IpHostInfo))
		externalIpAsset.GET("/ip_security_info", wrapper.Fn(internal_asset.IpSecurityInfo))
		externalIpAsset.GET("/ip_traceability_base_info", wrapper.Fn(internal_asset.IpTraceabilityBaseInfo))
		externalIpAsset.GET("/ip_traceability_process_info", wrapper.Fn(internal_asset.IpTraceabilityProcessInfo))
		externalIpAsset.POST("/ip_traceability_detail_info", wrapper.Fn(internal_asset.IpTraceabilityDetailInfo))
	}

	// 设备管理
	deviceGroup := r.Group("/device")
	{
		deviceGroup.GET("/:id", wrapper.Fn(deviceasset.Show))

		// 列表
		deviceGroup.GET("", wrapper.Fn(deviceasset.List))
		// 删除
		deviceGroup.DELETE("", wrapper.Fn(deviceasset.DeleteByIds))
		// 回收站列表
		deviceGroup.GET("/recycle_bin", wrapper.Fn(deviceasset.RecycleBin))
		// 回收站彻底删除
		deviceGroup.POST("/purge", wrapper.Fn(deviceasset.PurgeByIds))
		// 设备列表导出
		deviceGroup.GET("/export", wrapper.Fn(deviceasset.Export))
		// 设备漏洞列表
		deviceGroup.GET("/threat_center", wrapper.Fn(deviceasset.RelatedPocList))
		// 设备关联漏洞导出
		deviceGroup.GET("/export_related_poc", wrapper.Fn(deviceasset.ExportRelatedPoc))

		// 手动校准
		deviceGroup.POST("/manual_calibration", wrapper.Fn(deviceasset.ManualCalibration))
		// 设备画像-基本信息
		deviceGroup.GET("/device_base_info", wrapper.Fn(deviceasset.DeviceBaseInfo))
		// 设备画像-关联 IP
		deviceGroup.GET("/device_related_ip_info", wrapper.Fn(deviceasset.DeviceRelatedIpInfo))
		// 设备画像-关联网卡
		deviceGroup.GET("/network_card", wrapper.Fn(deviceasset.DeviceRelatedEthInfo))
		// 设备画像-主机信息
		deviceGroup.GET("/device_host_info", wrapper.Fn(deviceasset.DeviceHostInfo))
		// 设备画像-溯源基础信息
		deviceGroup.GET("/device_traceability_base_info", wrapper.Fn(deviceasset.DeviceTraceabilityBaseInfo))
		// 设备画像-溯源流程
		deviceGroup.GET("/device_traceability_process_info", wrapper.Fn(deviceasset.DeviceTraceabilityProcessInfo))
		// 设备画像-溯源详情
		deviceGroup.POST("/device_traceability_detail_info", wrapper.Fn(deviceasset.DeviceTraceabilityDetailInfo))
	}

	// 业务系统
	businessSystemGroup := r.Group("/business")
	{
		//添加
		businessSystemGroup.POST("", wrapper.Fn(business_system.Add))
		//更新
		businessSystemGroup.PUT("", wrapper.Fn(business_system.Update))
		//业务系统列表
		businessSystemGroup.GET("", wrapper.Fn(business_system.List))
		//获取业务系统
		businessSystemGroup.GET("/:id", wrapper.Fn(business_system.Detail))
		//回收站列表
		businessSystemGroup.GET("/recycle_bin", wrapper.Fn(business_system.RecycleBin))
		//软删除
		businessSystemGroup.DELETE("", wrapper.Fn(business_system.Delete))
		//彻底删除
		businessSystemGroup.DELETE("/purge", wrapper.Fn(business_system.Purge))
		//回收站还原
		businessSystemGroup.POST("/reduction", wrapper.Fn(business_system.Reduction))
		//导出
		businessSystemGroup.GET("/export", wrapper.Fn(business_system.Export))
		businessSystemGroup.GET("/recycle_bin_export", wrapper.Fn(business_system.RecycleBinExport))
		businessSystemGroup.POST("/import", wrapper.Fn(business_system.Import))
		businessSystemGroup.PUT("/batch", wrapper.Fn(business_system.Batch))
		//可信、待确认 移入黑名单
		businessSystemGroup.DELETE("/blacklist", wrapper.Fn(business_system.SetBlacklist))
		//移除黑名单 (移入待确认)
		businessSystemGroup.DELETE("/unconfirmed_list", wrapper.Fn(business_system.UnconfirmedList))
		//批量确认
		businessSystemGroup.POST("/set_credible", wrapper.Fn(business_system.SetCredible))
		//业务系统部门统计排行
		businessSystemGroup.GET("/department_list", wrapper.Fn(business_system.DepartmentList))
	}
	businessStrategyGroup := r.Group("/business_strategy")
	{
		businessStrategyGroup.GET("", wrapper.Fn(business_strategy.List))
		businessStrategyGroup.PUT("", wrapper.Fn(business_strategy.UpdateBatch))
	}

	// 高级筛选搜索条件
	searchCondition := r.Group("/search_condition")
	{
		searchCondition.GET("/fuzzy_search", wrapper.Fn(asset_center.IpLikeList))
		searchCondition.GET("/list", wrapper.Fn(search_condition.SearchCondition))
	}
}
