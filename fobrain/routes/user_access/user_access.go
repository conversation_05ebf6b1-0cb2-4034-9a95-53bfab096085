package user_access

import (
	"fobrain/fobrain/app/controller/user_access"
	"fobrain/fobrain/common/wrapper"
	"github.com/gin-gonic/gin"
)

func RouterGroupRegisterByUserAccess(group *gin.RouterGroup) {
	r := group.Group("/user_access")
	{
		// 创建用户
		r.POST("/create_user", wrapper.Fn(user_access.CreateUser))
		// 用户列表
		r.GET("/user_list", wrapper.Fn(user_access.UserList))
		// 删除用户
		r.DELETE("/delete_user", wrapper.Fn(user_access.DeleteByIds))
		// 修改用户
		r.PUT("/update_user", wrapper.Fn(user_access.UpdateUser))
		// 批量修改用户状态
		r.POST("/user_switch", wrapper.Fn(user_access.BatchSwitchUsersStatus))
		// 角色列表
		r.GET("/roles", wrapper.Fn(user_access.Roles))
		// 角色添加、修改、删除
		r.POST("/roles", wrapper.Fn(user_access.RolesAdd))
		r.PUT("/roles", wrapper.Fn(user_access.RolesUpdate))
		r.DELETE("/roles", wrapper.Fn(user_access.RolesDelete))
		// 人员台账列表 - 滚动查询
		r.GET("/staff_list", wrapper.Fn(user_access.Staff))
		// 所有系统菜单
		r.GET("/all_menus_tree", wrapper.Fn(user_access.MenusTree))
		// 获取当前用户菜单树,用户所在角色拥有的菜单树合集，如果是超管，直接返回全部
		r.GET("/my_menus_tree", wrapper.Fn(user_access.MyMenusTree))
		// 获取指定角色的菜单树，编辑和添加角色时用
		r.GET("/get_role_menus", wrapper.Fn(user_access.RoleMenus))
		// 数据权限枚举map
		r.GET("/data_permission_levels", wrapper.Fn(user_access.GetDataPermissionLevelMap))
	}
}
