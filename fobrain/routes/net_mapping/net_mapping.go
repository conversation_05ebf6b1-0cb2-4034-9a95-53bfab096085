package net_mapping

import (
	"fobrain/fobrain/app/controller/net_mapping"
	"fobrain/fobrain/common/wrapper"
	"github.com/gin-gonic/gin"
)

func RouterGroupRegisterByNetMapping(r *gin.RouterGroup) {
	group := r.Group("/net_mapping")
	// 手动创建
	group.POST("/create", wrapper.Fn(net_mapping.Create))
	// 导入
	group.POST("/import", wrapper.Fn(net_mapping.Import))
	// 导入验证
	group.POST("/import/confirm", wrapper.Fn(net_mapping.ImportConfirm))
	// 下载模板文件
	group.GET("/download", wrapper.Fn(net_mapping.DownloadTemplate))
	// 删除
	group.DELETE("/delete", wrapper.Fn(net_mapping.Delete))
	// 列表
	group.GET("/list", wrapper.Fn(net_mapping.List))
	// 审计列表
	group.GET("/audit_list", wrapper.Fn(net_mapping.ListAuditLog))
	// 审计详情
	group.GET("/audit_detail", wrapper.Fn(net_mapping.ListAuditData))
	// 检查报警
	group.GET("/check_alarm", wrapper.Fn(net_mapping.CheckAlarm))

	// 映射区域
	group.GET("/area", wrapper.Fn(net_mapping.ListArea))
	group.PUT("/area", wrapper.Fn(net_mapping.UpdateAreaList))

	// 网络映射拓扑
	group.GET("/gplot", wrapper.Fn(net_mapping.GetMappingGplot))
	group.GET("/gplot/asset", wrapper.Fn(net_mapping.GetMappingAssets))
	group.GET("/gplot/base_info", wrapper.Fn(net_mapping.GetMappingBaseInfo))
	group.GET("/gplot/business", wrapper.Fn(net_mapping.GetMappingByBusiness))
}
