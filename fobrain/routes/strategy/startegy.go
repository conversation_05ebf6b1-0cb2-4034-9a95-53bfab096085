package strategy

import (
	"fobrain/fobrain/app/controller/strategy"
	"fobrain/fobrain/common/wrapper"

	"github.com/gin-gonic/gin"
)

func RouterGroupRegisterByStrategy(r *gin.RouterGroup) {
	group := r.Group("/strategy")
	group.GET("/device", wrapper.Fn(strategy.GetDeviceConfig))
	group.PUT("/device", wrapper.Fn(strategy.SetDeviceConfig))

	group.GET("/list", wrapper.Fn(strategy.List))
	group.GET("/detail/:id", wrapper.Fn(strategy.GetDetail))
	group.GET("/detailByName", wrapper.Fn(strategy.GetDetailByName))
	group.POST("/create", wrapper.Fn(strategy.Create))
	group.POST("/triggerMerge", wrapper.Fn(strategy.TriggerMerge))

	group.GET("/poc_settings", wrapper.Fn(strategy.AllSettings))
	group.POST("/poc_settings_update", wrapper.Fn(strategy.SettingsUpdate))
	group.GET("/poc_manul_update", wrapper.Fn(strategy.ManualUpdate))
	group.POST("/poc_default", wrapper.Fn(strategy.SetDefault))
}
