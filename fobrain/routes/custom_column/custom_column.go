package custom_column

import (
	"fobrain/fobrain/app/controller/custom_column"
	"fobrain/fobrain/common/wrapper"

	"github.com/gin-gonic/gin"
)

// RegisterCustomColumnRoutes 注册自定义列相关路由
func RegisterCustomColumnRoutes(r *gin.RouterGroup) {
	group := r.Group("/custom_columns")
	{
		group.GET("", wrapper.Fn(custom_column.CustomFieldMetaList))        // 列表
		group.POST("", wrapper.Fn(custom_column.CustomFieldMetaCreate))     // 新增
		group.PUT("/:id", wrapper.Fn(custom_column.CustomFieldMetaUpdate))  // 修改
		group.DELETE("", wrapper.Fn(custom_column.CustomFieldMetaDelete))   // 删除（支持批量/条件）
		group.GET("/field_list", wrapper.Fn(custom_column.CustomFieldList)) // 列表，给前端做动态表头
	}
}
