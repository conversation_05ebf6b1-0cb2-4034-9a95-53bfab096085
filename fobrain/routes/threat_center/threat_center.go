package threat_center

import (
	"fobrain/fobrain/app/controller/poc_auto_distribute_config"
	"fobrain/fobrain/app/controller/threat_center"
	"fobrain/fobrain/common/wrapper"

	"github.com/gin-gonic/gin"
)

func RouterGroupRegisterByThreatCenter(r *gin.RouterGroup) {
	// 漏洞中心
	threatCenter := r.Group("/threat_center")
	{
		threatCenter.GET("", wrapper.Fn(threat_center.List))
		threatCenter.GET("/:id", wrapper.Fn(threat_center.Show))
		// 删除到回收站
		threatCenter.POST("/delete_to_recycle_bin", wrapper.Fn(threat_center.DeleteToRecycleBin))
		// 删除回收站数据
		threatCenter.POST("/delete_recycle_bin", wrapper.Fn(threat_center.DeleteRecycleBin))
		threatCenter.GET("/export", wrapper.Fn(threat_center.Export))
		threatCenter.GET("/get_node", wrapper.Fn(threat_center.GetNodeInfo))

		threatCenter.GET("/history", wrapper.Fn(threat_center.GetHistory))

		// 人工派发
		threatCenter.POST("/distribute", wrapper.Fn(threat_center.DistributeBatch))
		// 批量操作
		threatCenter.POST("/status_operate", wrapper.Fn(threat_center.StatusOperationBatch))
		threatCenter.POST("/retest", wrapper.Fn(threat_center.ReTest))
		threatCenter.POST("/retest_manual", wrapper.Fn(threat_center.ReTestManual))
		// 人工校准
		threatCenter.POST("/manual_calibration", wrapper.Fn(threat_center.ManualCalibration))
		threatCenter.GET("/status", wrapper.Fn(threat_center.Status))

		//手动添加
		threatCenter.POST("", wrapper.Fn(threat_center.Create))

		//获取自动派发的配置
		threatCenter.GET("/auto_dispatch", wrapper.Fn(poc_auto_distribute_config.Info))
		threatCenter.POST("/auto_dispatch", wrapper.Fn(poc_auto_distribute_config.Create))

		// 漏洞附件信息
		threatCenter.GET("files", wrapper.Fn(threat_center.Accessory)) // 附件列表
		threatCenter.POST("files", wrapper.Fn(threat_center.Upload))   // 上传附件
		threatCenter.DELETE("files", wrapper.Fn(threat_center.Delete)) // 删除附件
		threatCenter.GET("/download/files/:id", wrapper.Fn(threat_center.DownloadFile))

	}
	relevanceGroup := r.Group("/threat_center/relevance")
	{
		relevanceGroup.GET("/list", wrapper.Fn(threat_center.VulRelevanceList))
		relevanceGroup.GET("/ip_list", wrapper.Fn(threat_center.VulRelevanceIPList))
		relevanceGroup.GET("/business_list", wrapper.Fn(threat_center.VulRelevanceBusinessList))
		relevanceGroup.GET("/list/export", wrapper.Fn(threat_center.VulRelevanceExport))
		relevanceGroup.GET("/ip_list/export", wrapper.Fn(threat_center.VulRelevanceIPListExport))
		relevanceGroup.GET("/business_list/export", wrapper.Fn(threat_center.VulRelevanceBusinessListExport))
	}
}
