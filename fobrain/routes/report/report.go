package report

import (
	"fobrain/fobrain/app/controller/report"
	"fobrain/fobrain/common/wrapper"
	"github.com/gin-gonic/gin"
)

func RouterGroupReportRegister(r *gin.RouterGroup) {
	// 报告管理
	reportManager := r.Group("/report")
	{
		reportManager.GET("", wrapper.Fn(report.List))                  // 报告列表
		reportManager.GET("/template", wrapper.Fn(report.TemplateList)) // 报告模板列表
		reportManager.POST("/template", wrapper.Fn(report.CreateTemplate))
	}
}
