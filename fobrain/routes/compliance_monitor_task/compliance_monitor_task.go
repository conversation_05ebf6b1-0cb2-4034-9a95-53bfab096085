package compliance_monitor_task

import (
	"github.com/gin-gonic/gin"

	"fobrain/fobrain/app/controller/compliance_monitor_task"
	"fobrain/fobrain/common/wrapper"
)

func RouterGroupRegisterByComplianceMonitorTask(r *gin.RouterGroup) {
	settings := r.Group("/compliance_monitor_tasks")
	{
		// 合规监测任务列表
		settings.GET("", wrapper.Fn(compliance_monitor_task.Index))
		// 合规监测任务记录详情
		settings.GET("/records", wrapper.Fn(compliance_monitor_task.Records))
		// 合规监测任务记录删除
		settings.DELETE("/records", wrapper.Fn(compliance_monitor_task.RecordsDestroy))
		// 合规监测任务记录导出
		settings.GET("/records/export", wrapper.Fn(compliance_monitor_task.RecordsExport))
		// 合规监测任务删除
		settings.DELETE("", wrapper.Fn(compliance_monitor_task.Destroy))
		// 合规监测任务执行
		settings.GET("/:id/exec", wrapper.Fn(compliance_monitor_task.Exec))

		// 人工派发
		settings.POST("/distribute", wrapper.Fn(compliance_monitor_task.ComplianceRisksDistributeBatch))
		// 批量操作
		settings.POST("/status_operate", wrapper.Fn(compliance_monitor_task.ComplianceRisksStatusOperation))
		settings.POST("/retest", wrapper.Fn(compliance_monitor_task.ComplianceRisksReTest))
		// 检查违规资产是否存在
		settings.GET("/check_asset/:asset_id", wrapper.Fn(compliance_monitor_task.CheckAsset))
		// 查询合规历史表
		settings.GET("/history", wrapper.Fn(compliance_monitor_task.RecordsHistory))
	}
}
