package user

import (
	"fobrain/fobrain/app/controller/auth"
	"fobrain/fobrain/common/wrapper"

	"github.com/gin-gonic/gin"
)

func RouterGroupRegisterByUser(group *gin.RouterGroup) {
	r := group.Group("/user")
	{
		// current user
		r.GET("", wrapper.Fn(auth.UserInfo))
		// 角色列表
		r.GET("/gen-api-token", wrapper.Fn(auth.GenApiToken))
		// 退出登录
		r.GET("/login_out", wrapper.Fn(auth.LoginOut))
		// 修改密码
		r.PUT("/change-password", wrapper.Fn(auth.ChangePwd))
		// 忽略备份提醒
		r.POST("/ignore-backup-reminder", wrapper.Fn(auth.IgnoreBackupReminder))
	}
}
