package logs

import (
	"github.com/gin-gonic/gin"

	"fobrain/fobrain/app/controller/logs"
	"fobrain/fobrain/common/wrapper"
)

func RouterGroupRegisterByAuditLogs(group *gin.RouterGroup) {
	r := group.Group("/audit_logs")
	{
		// 用户日志
		r.GET("", wrapper.Fn(logs.List))
		r.GET("/users", wrapper.Fn(logs.Users))
		r.GET("/ips", wrapper.Fn(logs.Ips))
		r.GET("/export", wrapper.Fn(logs.Export))
		r.DELETE("", wrapper.Fn(logs.DeleteByIds))
	}
}
