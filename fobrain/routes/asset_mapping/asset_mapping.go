package asset_mapping

import (
	"github.com/gin-gonic/gin"

	"fobrain/fobrain/app/controller/asset_mapping"
	"fobrain/fobrain/common/wrapper"
)

func RouterGroupRegisterByAssetMapping(r *gin.RouterGroup) {
	settings := r.Group("/asset_mappings")
	{
		// 资产映射列表
		settings.GET("", wrapper.Fn(asset_mapping.Index))
		// 资产映射创建
		settings.POST("", wrapper.Fn(asset_mapping.Store))
		// 资产映射编辑
		settings.PUT("", wrapper.Fn(asset_mapping.Update))
		// 资产映射删除
		settings.DELETE("", wrapper.Fn(asset_mapping.Destroy))
		// 资产映射创建 - 文件导入
		settings.POST("/file", wrapper.Fn(asset_mapping.StoreFile))
	}
}
