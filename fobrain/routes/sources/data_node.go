package sources

import (
	"github.com/gin-gonic/gin"

	"fobrain/fobrain/app/controller/data_sources/nodes"
	"fobrain/fobrain/app/controller/data_sources/nodes/aliyun_cloud"
	"fobrain/fobrain/app/controller/data_sources/nodes/qizhi_uaudithost"
	"fobrain/fobrain/common/wrapper"
)

func RouterGroupRegisterByNodes(r *gin.RouterGroup) {
	dataNode := r.Group("/data")
	{
		// 数据源列表
		dataNode.GET("/nodes", wrapper.Fn(nodes.List))
		dataNode.GET("/node/list", wrapper.Fn(nodes.Item))
		dataNode.DELETE("/nodes/:id", wrapper.Fn(nodes.DeleteNode))
		dataNode.DELETE("/clear/nodes/:id", wrapper.Fn(nodes.ClearNode))
		dataNode.GET("/auth_node/:id", wrapper.Fn(nodes.GetNode))

		// 文件导入
		dataNode.GET("/node/file_import/field", wrapper.Fn(nodes.GetFileImportField)) // 获取人员字段列表
		// 日志易
		dataNode.GET("/node/rizhiyi/field", wrapper.Fn(nodes.GetRizhiyiField)) // 获取日志易字段列表
		// X-ray
		dataNode.POST("/node/x_ray", wrapper.Fn(nodes.Xray))
		dataNode.POST("/node/x_ray/test", wrapper.Fn(nodes.XrayTest))

		// D01
		dataNode.GET("/node/d01/:id/check_status", wrapper.Fn(nodes.CheckD01Status))
		dataNode.POST("/node/d01", wrapper.Fn(nodes.D01))
		dataNode.POST("/node/d01/test", wrapper.Fn(nodes.D01Test))
		// huawei_hk_cloud
		dataNode.POST("/node/huawei_hk_cloud", wrapper.Fn(nodes.HuaweiHkCloud))
		dataNode.POST("/node/huawei_hk_cloud/test", wrapper.Fn(nodes.HuaweiHkCloudTest))
		dataNode.PUT("/node/huawei_hk_cloud", wrapper.Fn(nodes.UpdateHuaweiHkCloud))
		dataNode.GET("/node/huawei_hk_cloud/field", wrapper.Fn(nodes.GetField))
		dataNode.GET("/node/huawei_hk_cloud/:id/check_status", wrapper.Fn(nodes.CheckHuaweiHkCloudStatus))

		// Foeye
		dataNode.POST("/node/foeye", wrapper.Fn(nodes.FoEye))
		dataNode.POST("/node/foeye/test", wrapper.Fn(nodes.FoEyeTest))
		dataNode.GET("/node/foeye/:id/check_status", wrapper.Fn(nodes.CheckFoEyeStatus))
		// Foradar_saas
		dataNode.POST("/node/foradar_saas", wrapper.Fn(nodes.FoRadar))
		dataNode.POST("/node/foradar_saas/test", wrapper.Fn(nodes.FoRadarTest))
		dataNode.GET("/node/foradar_saas/:id/check_status", wrapper.Fn(nodes.CheckFoRadarStatus))
		dataNode.POST("/node/bk_cmdb", wrapper.Fn(nodes.BkCmdb))
		dataNode.POST("/node/bk_cmdb/test", wrapper.Fn(nodes.BkCmdbTest))
		dataNode.GET("/node/bk_cmdb/:id/check_status", wrapper.Fn(nodes.CheckBkCmdbStatus))
		dataNode.GET("/node/bk_cmdb/field", wrapper.Fn(nodes.GetBKCmdbPeopleListFields))
		dataNode.POST("/node/jw_cmdb", wrapper.Fn(nodes.BkCmdb))
		dataNode.POST("/node/jw_cmdb/test", wrapper.Fn(nodes.BkCmdbTest))
		//dataNode.POST("/node/suyi_cmdb", wrapper.Fn(nodes.SuyiCmdb))
		dataNode.POST("/node/qty", wrapper.Fn(nodes.QtCloud))
		dataNode.POST("/node/qty/test", wrapper.Fn(nodes.QtCloudTest))
		dataNode.GET("/node/qty/field", wrapper.Fn(nodes.GetQtyField))
		dataNode.GET("/node/qty/:id/check_status", wrapper.Fn(nodes.CheckQtCloudStatus))
		dataNode.POST("/node/dingtalk", wrapper.Fn(nodes.DingTalk))
		dataNode.POST("/node/dingtalk/test", wrapper.Fn(nodes.DingTalkTest))
		dataNode.GET("/node/dingtalk/:id/check_status", wrapper.Fn(nodes.CheckDingTalkStatus))
		dataNode.GET("/node/dingtalk/field", wrapper.Fn(nodes.GetDingTalkFields))

		// NodeLogs
		dataNode.GET("/node/logs", wrapper.Fn(nodes.GetLogs))

		//nodeUpdate
		dataNode.PUT("/node/foeye", wrapper.Fn(nodes.UpdateFoeye))
		dataNode.PUT("/node/foradar_saas", wrapper.Fn(nodes.UpdateForadar))
		dataNode.PUT("/node/bk_cmdb", wrapper.Fn(nodes.UpdateBkCmdb))
		dataNode.PUT("/node/jw_cmdb", wrapper.Fn(nodes.UpdateBkCmdb))
		//dataNode.PUT("/node/suyi_cmdb", wrapper.Fn(nodes.SuyiCmdb))
		dataNode.PUT("/node/qty", wrapper.Fn(nodes.UpdateQtCloud))
		dataNode.PUT("/node/dingtalk", wrapper.Fn(nodes.UpdateDingTalk))
		dataNode.PUT("/node/d01", wrapper.Fn(nodes.UpdateD01))
		dataNode.PUT("/node/x_ray", wrapper.Fn(nodes.UpdateXray))

		//添加自定义数据源节点
		dataNode.POST("/nodes", wrapper.Fn(nodes.CustomDataSourceNodeAdd))
		//更新自定义数据源节点
		dataNode.PUT("/nodes", wrapper.Fn(nodes.CustomDataSourceNodeUpdate))

		// 阿里云云盾
		dataNode.POST("/node/aliyun/cloud", wrapper.Fn(aliyun_cloud.AliYunCloud))
		dataNode.POST("/node/aliyun/cloud/test", wrapper.Fn(aliyun_cloud.AliYunCloudTest))
		dataNode.GET("/node/aliyun/cloud/:id/check_status", wrapper.Fn(aliyun_cloud.CheckAliYunCloudStatus))
		dataNode.PUT("/node/aliyun/cloud", wrapper.Fn(aliyun_cloud.UpdateAliYunCloud))

		// 齐治堡垒机
		dataNode.POST("/node/qizhi/uaudithost", wrapper.Fn(qizhi_uaudithost.QiZhiUAuditHost))
		dataNode.POST("/node/qizhi/uaudithost/test", wrapper.Fn(qizhi_uaudithost.QiZhiUAuditHostTest))
		dataNode.GET("/node/qizhi/uaudithost/:id/check_status", wrapper.Fn(qizhi_uaudithost.CheckQiZhiUAuditHostStatus))
		dataNode.PUT("/node/qizhi/uaudithost", wrapper.Fn(qizhi_uaudithost.UpdateQiZhiUAuditHost))

		dataNode.PUT("/node/weibu", wrapper.Fn(nodes.UpdateWeiBu))
		dataNode.GET("/node/weibu/:id/check_status", wrapper.Fn(nodes.CheckWeiBuStatus))
		dataNode.POST("/node/weibu", wrapper.Fn(nodes.WeiBuNode))
		dataNode.POST("/node/weibu/test", wrapper.Fn(nodes.WeiBuNodeTest))

		dataNode.PUT("/node/longi_waf", wrapper.Fn(nodes.UpdateChangTingWaf))
		dataNode.GET("/node/longi_waf/:id/check_status", wrapper.Fn(nodes.CheckChangTingWafStatus))
		dataNode.POST("/node/longi_waf", wrapper.Fn(nodes.ChangTingWafNode))
		dataNode.POST("/node/longi_waf/test", wrapper.Fn(nodes.ChangTingWafNodeTest))

		dataNode.PUT("/node/mach_lake", wrapper.Fn(nodes.MachLakeUpdates))
		dataNode.GET("/node/mach_lake/:id/check_status", wrapper.Fn(nodes.MachLakeStatus))
		dataNode.POST("/node/mach_lake", wrapper.Fn(nodes.MachLakeNode))
		dataNode.POST("/node/mach_lake/test", wrapper.Fn(nodes.MachLakeNodeTest))

		// 中移香港
		dataNode.POST("/node/zhongyi/feishu", wrapper.Fn(nodes.ZhongyiFeishu))
		dataNode.POST("/node/zhongyi/feishu/test", wrapper.Fn(nodes.ZhongyiFeishuTest))
		dataNode.GET("/node/zhongyi/feishu/:id/check_status", wrapper.Fn(nodes.CheckZhongyiFeishuStatus))
		dataNode.PUT("/node/zhongyi/feishu", wrapper.Fn(nodes.UpdateZhongyiFeishu))
		dataNode.GET("/node/zhongyi/feishu/field", wrapper.Fn(nodes.GetZhongyiFeishuPeopleListFields))

		// 节点添加
		dataNode.PUT("/node/frame", wrapper.Fn(nodes.Update))
		dataNode.GET("/node/frame/:id/check_status", wrapper.Fn(nodes.CheckStatus))
		dataNode.POST("/node/frame", wrapper.Fn(nodes.Add))
		dataNode.POST("/node/frame/test", wrapper.Fn(nodes.Test))

		// 绿盟漏扫
		dataNode.POST("/node/rsas/sync_vuln_templates", wrapper.Fn(nodes.SyncVulnTemplates))

		// 腾讯UD
		dataNode.GET("/node/tencent_ud/field", wrapper.Fn(nodes.GetUDFields))

		// 奇安信堡垒机
		dataNode.GET("/node/qianxin_pam/field", wrapper.Fn(nodes.GetPamFields))
	}
}
