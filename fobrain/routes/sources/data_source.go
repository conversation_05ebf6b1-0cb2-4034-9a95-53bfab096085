package sources

import (
	"github.com/gin-gonic/gin"

	"fobrain/fobrain/app/controller/data_sources/sources"
	"fobrain/fobrain/common/wrapper"
)

func RouterGroupRegisterBySources(r *gin.RouterGroup) {
	dataSources := r.Group("/data")
	{
		// 数据源列表
		dataSources.GET("/source", wrapper.Fn(sources.List))

		//添加数据源
		dataSources.POST("/source", wrapper.Fn(sources.Add))

		// 展示用户设置
		dataSources.GET("/had_node", wrapper.Fn(sources.HadNode))
		// 数据源类型列表
		dataSources.GET("/source_types", wrapper.Fn(sources.SourceTypes))

		dataSources.GET("/sources", wrapper.Fn(sources.Item))
		// 数据源列表
		dataSources.GET("/source/nodes", wrapper.Fn(sources.NodeList))
	}
}
