package ip_mapping

import (
	"fobrain/fobrain/app/controller/ip_mapping"
	"fobrain/fobrain/common/wrapper"

	"github.com/gin-gonic/gin"
)

func RouterGroupRegisterByIpMapping(r *gin.RouterGroup) {
	group := r.Group("/ip_mapping")
	// 手动创建
	group.POST("/create", wrapper.Fn(ip_mapping.Create))
	// 导入
	group.POST("/import", wrapper.Fn(ip_mapping.Import))
	// 导入验证
	group.POST("/import/confirm", wrapper.Fn(ip_mapping.ImportConfirm))
	// 下载模板文件
	group.GET("/download", wrapper.Fn(ip_mapping.DownloadTemplate))
	// 删除
	group.DELETE("/delete", wrapper.Fn(ip_mapping.Delete))
	// 列表
	group.GET("/list", wrapper.Fn(ip_mapping.List))
	// 审计列表
	group.GET("/audit_list", wrapper.Fn(ip_mapping.AuditList))
	// 审计详情
	group.GET("/audit_detail", wrapper.Fn(ip_mapping.AuditDetail))
	// 检查报警
	group.GET("/check_alarm", wrapper.Fn(ip_mapping.CheckIpMappingAlarm))
	// 报警列表
	group.GET("/alarm_list", wrapper.Fn(ip_mapping.AlarmList))
	// 导出报警信息
	group.POST("/export_alarm_list", wrapper.Fn(ip_mapping.ExportAlarmList))

	// 自定义字段
	group.GET("/custom_fields", wrapper.Fn(ip_mapping.ListCustomFields))
	group.PUT("/custom_fields", wrapper.Fn(ip_mapping.CreateOrUpdateCustomFields))
	group.GET("/custom_fields/detail", wrapper.Fn(ip_mapping.ListCustomFieldsDetail))
	group.GET("/custom_fields/combination", wrapper.Fn(ip_mapping.ListCustomFieldCombination))
	// 统计——自定义字段
	group.GET("/statistic_custom_fields", wrapper.Fn(ip_mapping.ListStatisticCustomFields))
	group.GET("/statistic_custom_fields/detail", wrapper.Fn(ip_mapping.ListStatisticCustomFieldsDetail))

	// 拓扑图
	group.GET("/gplot", wrapper.Fn(ip_mapping.GetMappingGplot))
	group.GET("/gplot/asset", wrapper.Fn(ip_mapping.GetMappingAssets))
	group.GET("/gplot/base_info", wrapper.Fn(ip_mapping.GetMappingBaseInfo))
}
