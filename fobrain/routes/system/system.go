package system

import (
	"fobrain/fobrain/app/controller/system_configs"
	"fobrain/fobrain/app/controller/system_configs/backup"
	"fobrain/fobrain/app/controller/system_configs/black_white"
	"fobrain/fobrain/app/controller/system_configs/ntp"
	"fobrain/fobrain/app/controller/system_configs/scan_ban"
	"fobrain/fobrain/app/controller/system_configs/systemInfo"
	"fobrain/fobrain/app/controller/system_configs/upgrade"
	"fobrain/fobrain/app/controller/workbench_ctl"
	"fobrain/fobrain/common/wrapper"

	"github.com/gin-gonic/gin"
)

func RouterGroupRegisterBySystem(group *gin.RouterGroup) {
	r := group.Group("/system")
	{
		// 系统升级
		r.GET("/upgrade/info", wrapper.Fn(upgrade.SystemUpgradePageInfo))
		r.POST("/upgrade/upload", wrapper.Fn(upgrade.SystemUpgradeUploadFile))
		r.GET("/upgrade/process", wrapper.Fn(upgrade.SystemUpgradeProgress))
		r.GET("/upgrade/manual/unlock", wrapper.Fn(upgrade.UpgradeManualUnlock))
		r.POST("/upgrade/gen/version", wrapper.Fn(upgrade.SystemGenVersionFile))

		// 禁扫配置
		r.POST("/scan/ban", wrapper.Fn(scan_ban.SaveScanBanConfig))
		r.GET("/scan/ban", wrapper.Fn(scan_ban.ReadScanBanConfig))

		// 黑白名单配置
		r.GET("/black/white/conf", wrapper.Fn(black_white.ListBlackWhite))
		r.POST("/black/white/conf", wrapper.Fn(black_white.AddBlackWhite))
		r.DELETE("/black/white/conf", wrapper.Fn(black_white.DeleteBlackWhite))
		r.GET("/black/white/judge", wrapper.Fn(black_white.BlackWhiteJudge))
		r.POST("/black/white/import", wrapper.Fn(black_white.BlackWhiteImport))
		r.POST("/black/white/export", wrapper.Fn(black_white.BlackWhiteExport))

		// 系统信息
		r.POST("/info/conf", wrapper.Fn(systemInfo.SaveSystemInfoConfig))
		r.POST("/info/init_data", wrapper.Fn(systemInfo.ClearData))

		//备份恢复 (旧版本)
		r.POST("/backup/export", wrapper.Fn(backup.Backup))
		r.GET("/backup/export", wrapper.Fn(backup.Download))
		r.POST("/backup/upload", wrapper.Fn(backup.Restore))
		r.GET("/backup/checkout", wrapper.Fn(backup.Checkout))

		// 新版备份恢复 (UniBackup SDK v2)
		backupV2 := r.Group("/backup/v2")
		{
			// 配置管理
			backupV2.GET("/config", wrapper.Fn(system_configs.GetBackupConfig))
			backupV2.PUT("/config", wrapper.Fn(system_configs.UpdateBackupConfig))
			backupV2.POST("/config/test-cloud-storage", wrapper.Fn(system_configs.TestCloudStorageConfig))

			// 手动备份
			backupV2.POST("/manual/full", wrapper.Fn(system_configs.ManualFullBackup))
			backupV2.POST("/manual/incr", wrapper.Fn(system_configs.ManualIncrBackup))

			// 备份列表和管理
			backupV2.GET("/list", wrapper.Fn(system_configs.ListBackups))
			backupV2.DELETE("/:id", wrapper.Fn(system_configs.DeleteBackup))
			backupV2.GET("/:id/deletion-info", wrapper.Fn(system_configs.GetBackupDeletionInfo))

			// 恢复备份
			backupV2.POST("/restore", wrapper.Fn(system_configs.RestoreBackup))

			// 任务状态查询
			backupV2.GET("/task/:id", wrapper.Fn(system_configs.GetTaskStatus))

			// 锁状态管理
			// backupV2.GET("/lock/status", wrapper.Fn(system_configs.GetLockStatus))
			backupV2.POST("/lock/force-unlock", wrapper.Fn(system_configs.ForceUnlock))
		}

		// WAF覆盖度计算维度配置
		r.GET("/waf/coverage/conf", wrapper.Fn(workbench_ctl.WAFCoverageConfInfo))
		r.POST("/waf/coverage/conf", wrapper.Fn(workbench_ctl.WAFCoverageConfSave))
	}
	ntpRoute := group.Group("/ntp")
	{
		ntpRoute.POST("", wrapper.Fn(ntp.UpdateNtpConfig))
		ntpRoute.GET("", wrapper.Fn(ntp.GetNtpConfig))
	}
}
