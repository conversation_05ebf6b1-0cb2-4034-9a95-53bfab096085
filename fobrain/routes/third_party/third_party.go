package third_party

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"

	"fobrain/fobrain/app/controller/auth"
	"fobrain/fobrain/app/controller/third_party/account_4a"
	"fobrain/fobrain/common/wrapper"
	"fobrain/models/mysql/system_configs"
)

// RouterGroupRegisterByThirdParty
//	@Summary: 第三方对接路由注册
func RouterGroupRegisterByThirdParty(r *gin.RouterGroup) {
	r.Use(Authorization)
	api := r.Group("/api/v1")
	api.POST("/add_account", account_4a.AccountAdd)
	api.POST("/find_account", account_4a.AccountFind)

	r.GET("/4a_login", wrapper.Fn(auth.FourALogin))
}

func Authorization(c *gin.Context) {
	token, err := system_configs.NewSystemConfigs().GetConfig("cascade_api_token")
	if err != nil {
		fmt.Println("获取api token失败", err)
		c.Abort()
	}

	requestToken := c.Request.Header.Get("access_token")
	if requestToken == "" {
		// 按照行业标准化走，统一使用 access_token 来作为三方 token 的参数名称
		requestToken = c.Query("access_token")
	}

	if requestToken != token {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "非法请求！"})
		c.Abort()
		return
	}
}
