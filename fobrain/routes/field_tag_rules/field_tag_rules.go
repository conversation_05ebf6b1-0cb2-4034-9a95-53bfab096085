package field_tag_rules

import (
	"github.com/gin-gonic/gin"

	"fobrain/fobrain/app/controller/field_tag_rules"
	"fobrain/fobrain/common/wrapper"
)

func RouterGroupRegisterByFieldTagRules(group *gin.RouterGroup) {
	// IP段管理
	r := group.Group("/tag_rules")
	{
		r.POST("", wrapper.Fn(field_tag_rules.Create))
		r.PUT("", wrapper.Fn(field_tag_rules.Update))
		r.GET("", wrapper.Fn(field_tag_rules.List))
		r.DELETE("", wrapper.Fn(field_tag_rules.DeleteByIds))
	}
	rr := group.Group("/tag_fields")
	{
		rr.GET("", wrapper.Fn(field_tag_rules.FieldOrSetFiledList))
	}
}
