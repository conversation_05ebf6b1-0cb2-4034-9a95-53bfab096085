package config

import (
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestLoadConfigByFile(t *testing.T) {
	// 创建一个临时的 .env 文件
	envContent := `
APP_NAME=TestApp
ENV=production
LISTEN=127.0.0.1:8080
STORAGE=/tmp/test_storage
UPLOAD_MAX_SIZE=200
LICENSE_PATH=/tmp/.license
IS_CLOSE_CODE=true
JWT_EXPIRE=60
JWT_KEY=test_jwt_key
PPROF_ENABLE=false
LOG_SYSLOG=test_syslog
LOG_FILE=/tmp/test.log
LOG_LEVEL=debug
LOG_MAX_SIZE=100
LOG_MAX_AGE=7
LOG_MAX_BACKUPS=3
`
	envFile := "/tmp/test.env"
	err := os.WriteFile(envFile, []byte(envContent), 0644)
	assert.Nil(t, err)
	defer os.Remove(envFile)

	// 加载配置
	err = LoadConfigByFile(envFile)
	assert.Nil(t, err)

	// 获取配置
	config := Get()

	// 断言配置项
	assert.Equal(t, "TestApp", config.SysName)
	assert.Equal(t, "production", config.Env)
	assert.Equal(t, "127.0.0.1:8080", config.Listen)
	assert.Equal(t, "/tmp/test_storage", config.Storage)
	assert.Equal(t, int64(200), config.UploadMaxSize)
	assert.Equal(t, "/tmp/.license", config.LicensePath)
	assert.Equal(t, true, config.IsCloseCode)
	assert.Equal(t, uint(60), config.Jwt.Expire)
	assert.Equal(t, "test_jwt_key", config.Jwt.Key)
	assert.Equal(t, false, config.Log.PprofEnable)
	assert.Equal(t, "test_syslog", config.Log.Syslog)
	assert.Equal(t, "/tmp/test.log", config.Log.File)
	assert.Equal(t, "debug", config.Log.Level)
	assert.Equal(t, 100, config.Log.MaxSize)
	assert.Equal(t, 7, config.Log.MaxAge)
	assert.Equal(t, 3, config.Log.MaxBackups)
}

func TestSetVersion(t *testing.T) {
	initConfig()
	SetVersion("1.0.0")

	config := Get()
	assert.Equal(t, "1.0.0", config.Version)
}

func TestSetBuildTime(t *testing.T) {
	initConfig()
	SetBuildTime("2024-01-01T00:00:00Z")

	config := Get()
	assert.Equal(t, "2024-01-01T00:00:00Z", config.BuildTime)
}
