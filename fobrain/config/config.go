package config

import (
	"github.com/caarlos0/env/v10"
	"github.com/joho/godotenv"
)

type (
	Config struct {
		SysName       string `json:"app_name" env:"APP_NAME" envDefault:"FOBrain"`                        // 产品名称
		Copyright     string `json:"copyright" env:"COPYRIGHT" envDefault:"北京华顺信安信息技术有限公司"`               // 产品名称
		Version       string `json:"version"  env:"VERSION" envDefault:"2.0.0"`                           // 版本
		BuildTime     string `json:"build_time"`                                                          // 编译时间
		Env           string `json:"env" env:"ENV" envDefault:"development"`                              // 运行环境
		Listen        string `json:"listen" env:"LISTEN" envDefault:"0.0.0.0:8100"`                       // 监听信息
		Storage       string `json:"storage" env:"STORAGE" envDefault:"/data/storage/files"`              // 文件保存目录
		UploadMaxSize int64  `json:"upload_max_size" env:"UPLOAD_MAX_SIZE" envDefault:"100"`              // 上传文件大小控制(MB)
		LicensePath   string `json:"license_path" env:"LICENSE_PATH" envDefault:"/data/storage/.license"` // license文件路径
		IsCloseCode   bool   `json:"is_close_code" env:"IS_CLOSE_CODE" envDefault:"false"`                // 是否关闭验证吗 true 是 false false
		Log           Log    `json:"log"`                                                                 // Log
		Jwt           Jwt    `json:"jwt"`                                                                 // Jwt
	}

	Jwt struct {
		Expire uint   `json:"expire" env:"JWT_EXPIRE" envDefault:"30"`            // Token过期时间(Minute)
		Key    string `json:"key" env:"JWT_KEY" envDefault:"fobrain_jwt_key_666"` // Token加密key
	}

	Log struct {
		PprofEnable bool   `json:"enable_pprof" env:"PPROF_ENABLE" envDefault:"true"`         // pprof 监控
		Syslog      string `json:"syslog" env:"LOG_SYSLOG" envDefault:""`                     // syslog服务器
		File        string `json:"template" env:"LOG_FILE" envDefault:"/var/tmp/fobrain.log"` // 日志文件
		Level       string `json:"level" env:"LOG_LEVEL" envDefault:"info"`                   // 日志级别
		MaxSize     int    `json:"max_size" env:"LOG_MAX_SIZE" envDefault:"512"`              // 最大MB
		MaxAge      int    `json:"max_age" env:"LOG_MAX_AGE" envDefault:"30"`                 // 保留天数
		MaxBackups  int    `json:"max_backups" env:"LOG_MAX_BACKUPS" envDefault:"10"`         // 最多保留10个备份
	}
)

var _config *Config

func LoadConfigByFile(path string) error {
	// 初始化配置
	initConfig()
	if err := godotenv.Load(path); err != nil {
		return err
	}
	if err := env.Parse(_config); err != nil {
		return err
	}
	return nil
}

func initConfig() {
	if _config == nil {
		_config = &Config{}
	}
}

func Get() *Config {
	return _config
}

func SetVersion(version string) {
	initConfig()
	_config.Version = version
}

func SetBuildTime(date string) {
	initConfig()
	_config.BuildTime = date
}
