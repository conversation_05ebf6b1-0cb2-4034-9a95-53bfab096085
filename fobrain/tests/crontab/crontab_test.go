package crontab

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"gorm.io/gorm"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"

	testcommon "fobrain/fobrain/tests/common_test"
	mockblackwhite "fobrain/fobrain/tests/mock_black_white"

	"fobrain/fobrain/tests"
	"fobrain/models/mysql/crontab"
	"fobrain/pkg/utils"
)

func TestCrontabList(t *testing.T) {
	mockDb := testcommon.InitSqlMock()
	defer mockDb.Close()

	mockDb.Mock.ExpectBegin()
	mockDb.ExpectQuery("SELECT * FROM `users` WHERE `id` = ? ORDER BY `users`.`id` LIMIT 1").
		WillReturnRows(sqlmock.NewRows([]string{"id", "name", "key", "status", "remark"}).AddRow(1, "admin", "admin", 1, "超级管理员"))

	mockDb.ExpectQuery("SELECT count(*) FROM `crontab`").
		WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(1))

	mockDb.ExpectQuery("SELECT * FROM `menus` WHERE `path` = ? AND `method` = ? ORDER BY `menus`.`id` LIMIT 1").
		WithArgs("/api/v1/crontab", "GET").
		WillReturnRows(sqlmock.NewRows([]string{"id", "title"}).AddRow(1, "admin"))

	mockDb.ExpectQuery("SELECT * FROM `crontab` ORDER BY created_at DESC LIMIT 13").
		WillReturnRows(sqlmock.NewRows([]string{"id", "user_id", "name"}).AddRow(1, 1, "abel"))

	mockDb.ExpectExec("INSERT INTO `audit_logs` (`created_at`,`updated_at`,`title`,`user_id`,`path`,`type`,`result`,`ip`) VALUES (?,?,?,?,?,?,?,?)").
		WithArgs(time.Now(), time.Now(), "title", 1, "path", 1, "result", "ip").
		WillReturnResult(sqlmock.NewResult(1, 1)) // Assuming one row affected with ID 1

	mockDb.Mock.ExpectCommit()

	r := tests.GetGinEngine()
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/crontab?per_page=13&page=1", nil)
	req.Header.Set("Authorization", tests.GetUserToken(1))
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusBadRequest, w.Code)
}

func TestCrontabHistoryList(t *testing.T) {
	r := tests.GetGinEngine()
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/crontab/1/history?per_page=13&page=1", nil)
	req.Header.Set("Authorization", tests.GetUserToken(1))
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusBadRequest, w.Code)
}

func TestCrontabHistoryClear(t *testing.T) {
	r := tests.GetGinEngine()
	w := httptest.NewRecorder()
	req := httptest.NewRequest("DELETE", "/api/v1/crontab/2/history", strings.NewReader(""))
	req.Header.Set("Authorization", tests.GetUserToken(1))
	req.Header.Set("Content-Type", "application/json")
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusBadRequest, w.Code)
}

//func TestCUD(t *testing.T) {
//	// 添加
//	add(t)
//	// 更新
//	update(t)
//	// 删除
//	delete(t)
//}

func TestAdd(t *testing.T) {

	r := tests.GetGinEngine()
	w := httptest.NewRecorder()
	val, _ := json.Marshal(map[string]any{
		"name": "TestCrontabAdd", "status": 1, "type": 1, "spec": "59 59 23 * * *", "method": "ClearExpiredTokens", "params": "",
	})
	req := httptest.NewRequest("POST", "/api/v1/crontab", strings.NewReader(string(val)))
	req.Header.Set("Authorization", tests.GetUserToken(1))
	req.Header.Set("Content-Type", "application/json")
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusBadRequest, w.Code)
}

func TestDelete(t *testing.T) {
	mockblackwhite.CommonBlackWhiteTest()

	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()
	mockDb.ExpectQuery("SELECT * FROM `users` WHERE `id` = ? ORDER BY `users`.`id` LIMIT 1").
		WillReturnRows(sqlmock.NewRows([]string{"id", "name", "key", "status", "remark"}).AddRow(1, "admin", "admin", 1, "超级管理员"))

	mockDb.ExpectQuery("SELECT * FROM `roles` WHERE `id` in (SELECT `role_id` FROM `users_roles` WHERE `user_id`=?)").
		WithArgs(1).
		WillReturnRows(sqlmock.NewRows([]string{"id", "name", "key", "status", "remark"}).AddRow(1, "admin", "admin", 1, "超级管理员"))

	mockDb.ExpectQuery("SELECT * FROM `menus` WHERE `id` IN (SELECT `menu_id` FROM `roles_menus` WHERE `role_id` IN (SELECT `user_id` FROM `users_roles` WHERE `user_id`=?)) AND `path` = ? and `method` = ?").
		WillReturnError(gorm.ErrRecordNotFound)

	r := tests.GetGinEngine()
	w := httptest.NewRecorder()
	crontabs := make([]crontab.CrontabModel, 0)
	_ = crontab.NewCrontabModel().Where("name = ?", "TestCrontabAdd").Select("id").Find(&crontabs).Error
	ids := utils.ListColumn(crontabs, func(t crontab.CrontabModel) uint64 { return t.Id })
	val, _ := json.Marshal(map[string]any{"ids": ids})
	req := httptest.NewRequest("DELETE", "/api/v1/crontab", strings.NewReader(string(val)))
	req.Header.Set("Authorization", tests.GetUserToken(1))
	req.Header.Set("Content-Type", "application/json")
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)
}
