package task

import (
	"net/http"
	"net/http/httptest"
	"reflect"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"gorm.io/gorm"

	testcommon "fobrain/fobrain/tests/common_test"

	"github.com/agiledragon/gomonkey/v2"

	auth2 "fobrain/fobrain/common/auth"
	"fobrain/fobrain/common/localtime"
	mockblackwhite "fobrain/fobrain/tests/mock_black_white"
	"fobrain/initialize/mysql"
	"fobrain/models/mysql/token"
	"fobrain/models/mysql/user"

	"github.com/stretchr/testify/assert"

	"fobrain/fobrain/tests"
)

func TestSync(t *testing.T) {

	r := tests.GetGinEngine()
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/task/sync", nil)
	req.Header.Set("Authorization", tests.GetUserToken(1))
	req.Header.Set("Content-Type", "application/json")
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusBadRequest, w.Code)
}
func TestChildList(t *testing.T) {
	mockblackwhite.CommonBlackWhiteTest()

	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()
	mockDb.ExpectQuery("SELECT * FROM `users` WHERE `id` = ? ORDER BY `users`.`id` LIMIT 1").
		WillReturnRows(sqlmock.NewRows([]string{"id", "account", "password", "username", "status"}).AddRow(1, "admin", "admin", "admin", 1))

	mockDb.ExpectQuery("SELECT * FROM `roles` WHERE `id` in (SELECT `role_id` FROM `users_roles` WHERE `user_id`=?)").
		WithArgs(1).
		WillReturnRows(sqlmock.NewRows([]string{"id", "name", "key", "status", "remark"}).AddRow(1, "admin", "admin", 1, "超级管理员"))

	mockDb.ExpectQuery("SELECT * FROM `menus` WHERE `id` IN (SELECT `menu_id` FROM `roles_menus` WHERE `role_id` IN (SELECT `user_id` FROM `users_roles` WHERE `user_id`=?)) AND `path` = ? and `method` = ?").
		WillReturnError(gorm.ErrRecordNotFound)

	r := tests.GetGinEngine()
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/task/child_list?per_page=13&page=1", nil)
	req.Header.Set("Authorization", tests.GetUserToken(1))
	req.Header.Set("Content-Type", "application/json")
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)
}
func TestList(t *testing.T) {
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyFunc(auth2.GenerateToken, func(userInfo user.User) (string, *time.Time, error) {
		timeEx := time.Now().Add(10 * time.Second)
		return "D50EGMF8ICPVPA3MZIKB8A7020E7E8754103A7D6B32F1127E6E9", &timeEx, nil
	}).Reset()

	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyMethod(reflect.TypeOf(&token.Token{}), "First", func(*token.Token, ...mysql.HandleFunc) (token.Token, error) {
		return token.Token{
			UserId:    1,
			Token:     "mocked_token_string",
			ExpiredAt: localtime.Time(time.Now().Add(15 * time.Minute)),
		}, nil
	}).Reset()

	r := tests.GetGinEngine()
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/task/list?per_page=13&page=1", nil)
	req.Header.Set("Authorization", tests.GetUserToken(1))
	req.Header.Set("Content-Type", "application/json")
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusBadRequest, w.Code)
}
func TestFoeyeSync(t *testing.T) {
	//r := tests.GetGinEngine()
	//w := httptest.NewRecorder()
	//req := httptest.NewRequest("GET", "/api/v1/task/sync?node_id=1&type=0", nil)
	//req.Header.Set("Authorization", tests.GetUserToken(1))
	//req.Header.Set("Content-Type", "application/json")
	//r.ServeHTTP(w, req)
	//assert.Equal(t, http.StatusOK, w.Code)
	//time.Sleep(1 * 30 * time.Second)
}

func TestForadarSync(t *testing.T) {
	//r := tests.GetGinEngine()
	//w := httptest.NewRecorder()
	//req := httptest.NewRequest("GET", "/api/v1/task/sync?node_id=2&type=0", nil)
	//req.Header.Set("Authorization", tests.GetUserToken(1))
	//req.Header.Set("Content-Type", "application/json")
	//r.ServeHTTP(w, req)
	//assert.Equal(t, http.StatusOK, w.Code)
	//time.Sleep(1 * 60 * time.Second)
}

func TestQTCloudSync(t *testing.T) {
	//r := tests.GetGinEngine()
	//w := httptest.NewRecorder()
	//req := httptest.NewRequest("GET", "/api/v1/task/sync?node_id=3&type=0", nil)
	//req.Header.Set("Authorization", tests.GetUserToken(1))
	//req.Header.Set("Content-Type", "application/json")
	//r.ServeHTTP(w, req)
	//assert.Equal(t, http.StatusOK, w.Code)
	//time.Sleep(1 * 60 * time.Second)
}

func TestDingTalkSync(t *testing.T) {
	//r := tests.GetGinEngine()
	//w := httptest.NewRecorder()
	//req := httptest.NewRequest("GET", "/api/v1/task/sync?node_id=4&type=0", nil)
	//req.Header.Set("Authorization", tests.GetUserToken(1))
	//req.Header.Set("Content-Type", "application/json")
	//r.ServeHTTP(w, req)
	//assert.Equal(t, http.StatusOK, w.Code)
	//time.Sleep(2 * 60 * time.Second)
}

func TestBKCmdbSync(t *testing.T) {
	//r := tests.GetGinEngine()
	//w := httptest.NewRecorder()
	//req := httptest.NewRequest("GET", "/api/v1/task/sync?node_id=5&type=0", nil)
	//req.Header.Set("Authorization", tests.GetUserToken(1))
	//req.Header.Set("Content-Type", "application/json")
	//r.ServeHTTP(w, req)
	//assert.Equal(t, http.StatusOK, w.Code)
	//time.Sleep(1 * 60 * time.Second)
}
