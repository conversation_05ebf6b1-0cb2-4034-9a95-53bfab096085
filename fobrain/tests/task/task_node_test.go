package task

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"

	testcommon "fobrain/fobrain/tests/common_test"
	mockblackwhite "fobrain/fobrain/tests/mock_black_white"

	"fobrain/fobrain/tests"
)

func TestTaskChildList(t *testing.T) {
	mockblackwhite.CommonBlackWhiteTest()

	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()
	mockDb.ExpectQuery("SELECT * FROM `roles` WHERE `id` in (SELECT `role_id` FROM `users_roles` WHERE `user_id`=?)").
		WillReturnRows(sqlmock.NewRows([]string{"id", "name", "key", "status", "remark"}).AddRow(1, "超级管理员", "admin", 1, "超级管理员"))

	mockDb.ExpectQuery("SELECT `ip` FROM `black_white_config` WHERE strategy_type = ?").
		WillReturnError(gorm.ErrRecordNotFound)

	mockDb.ExpectQuery("SELECT `ip` FROM `black_white_config` WHERE strategy_type = ?").
		WillReturnError(gorm.ErrRecordNotFound)

	mockDb.ExpectQuery("SELECT * FROM `users` WHERE `id` = ? ORDER BY `users`.`id` LIMIT 1").
		WillReturnRows(sqlmock.NewRows([]string{"id", "account", "password", "username", "status"}).AddRow(1, "admin", "admin", "admin", 1))

	mockDb.ExpectQuery("SELECT * FROM `menus` WHERE `id` IN (SELECT `menu_id` FROM `roles_menus` WHERE `role_id` IN (SELECT `user_id` FROM `users_roles` WHERE `user_id`=?)) AND `path` = ? and `method` = ?").
		WillReturnError(gorm.ErrRecordNotFound)

	r := tests.GetGinEngine()
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/task/child_list?page=1&per_page=20&keyword=&node_id=2", nil)
	req.Header.Set("Authorization", tests.GetUserToken(1))
	req.Header.Set("Content-Type", "application/json")
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)
	//time.Sleep(1 * 30 * time.Second)
}
func TestTaskList(t *testing.T) {
	mockblackwhite.CommonBlackWhiteTest()

	r := tests.GetGinEngine()
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/task/list?page=1&per_page=20", nil)
	req.Header.Set("Authorization", tests.GetUserToken(1))
	req.Header.Set("Content-Type", "application/json")
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusBadRequest, w.Code)
	//time.Sleep(1 * 30 * time.Second)
}
