package settings

import (
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"fobrain/pkg/fortest"

	mockblackwhite "fobrain/fobrain/tests/mock_black_white"

	"github.com/stretchr/testify/assert"

	"fobrain/fobrain/tests"
)

func TestLicense(t *testing.T) {
	mockblackwhite.CommonBlackWhiteTest()

	r := tests.GetGinEngine()
	w := httptest.NewRecorder()
	req := httptest.NewRequest(http.MethodPost, "/api/v1/settings/license", nil)
	req.Header.Set("Authorization", tests.GetUserToken(1))
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusBadRequest, w.Code)
}

func TestIntranetIps(t *testing.T) {
	fortest.Init()
	mockblackwhite.CommonBlackWhiteTest()

	r := tests.GetGinEngine()
	w := httptest.NewRecorder()
	req := httptest.NewRequest(http.MethodGet, "/api/v1/settings/intranet_ips", nil)
	req.Header.Set("Authorization", tests.GetUserToken(1))
	r.ServeHTTP(w, req)
	fmt.Println(w.Body)
	assert.Equal(t, http.StatusBadRequest, w.Code)
}

func TestIntranetIpsUpdate(t *testing.T) {
	mockblackwhite.CommonBlackWhiteTest()

	r := tests.GetGinEngine()
	w := httptest.NewRecorder()
	req := httptest.NewRequest(http.MethodPut, "/api/v1/settings/intranet_ips", nil)
	req.Header.Set("Authorization", tests.GetUserToken(1))
	r.ServeHTTP(w, req)
	fmt.Println(w.Body)
	assert.Equal(t, http.StatusBadRequest, w.Code)
}

func TestNetworksUpdate(t *testing.T) {
	mockblackwhite.CommonBlackWhiteTest()

	r := tests.GetGinEngine()
	w := httptest.NewRecorder()
	req := httptest.NewRequest(http.MethodPut, "/api/v1/settings/networks", nil)
	req.Header.Set("Authorization", tests.GetUserToken(1))
	r.ServeHTTP(w, req)
	fmt.Println(w.Body)
	assert.Equal(t, http.StatusBadRequest, w.Code)
}

func TestNetworks(t *testing.T) {
	mockblackwhite.CommonBlackWhiteTest()

	r := tests.GetGinEngine()
	w := httptest.NewRecorder()
	req := httptest.NewRequest(http.MethodGet, "/api/v1/settings/networks", nil)
	req.Header.Set("Authorization", tests.GetUserToken(1))
	r.ServeHTTP(w, req)
	fmt.Println(w.Body)
	assert.Equal(t, http.StatusBadRequest, w.Code)
}

func TestPing(t *testing.T) {
	mockblackwhite.CommonBlackWhiteTest()

	r := tests.GetGinEngine()
	w := httptest.NewRecorder()
	req := httptest.NewRequest(http.MethodPost, "/api/v1/settings/ping", nil)
	req.Header.Set("Authorization", tests.GetUserToken(1))
	r.ServeHTTP(w, req)
	fmt.Println(w.Body)
	assert.Equal(t, http.StatusBadRequest, w.Code)
}
