package websocket

import (
	"fobrain/fobrain/tests"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/gorilla/websocket"
	"github.com/stretchr/testify/assert"
)

func TestWebsocket(t *testing.T) {
	r := tests.GetGinEngine()
	srv := httptest.NewServer(r)
	defer srv.Close()
	dialer := websocket.Dialer{}
	conn, _, err := dialer.Dial("ws"+strings.TrimPrefix(srv.URL, "http")+"/websocket", nil)
	if err != nil {
		t.Errorf("Failed to open websocket: %v", err)
	}
	err = conn.WriteMessage(websocket.TextMessage, []byte(`{"cmd":"register","data":{"mid":"xxxx","key":"kkkkkkk"}}`))
	if err != nil {
		t.Errorf("Failed to write message: %v", err)
	}
	_, msg, err := conn.ReadMessage()
	if err != nil {
		t.Errorf("Failed to read message: %v", err)
	}
	assert.Contains(t, string(msg), "success")
}
