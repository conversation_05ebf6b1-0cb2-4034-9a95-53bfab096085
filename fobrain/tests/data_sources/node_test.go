package data_sources

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"gorm.io/gorm"

	testcommon "fobrain/fobrain/tests/common_test"

	"github.com/stretchr/testify/assert"

	mockblackwhite "fobrain/fobrain/tests/mock_black_white"

	"fobrain/fobrain/tests"
)

func TestNodes(t *testing.T) {
	mockblackwhite.CommonBlackWhiteTest()

	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()
	mockDb.ExpectQuery("SELECT * FROM `users` WHERE `id` = ? ORDER BY `users`.`id` LIMIT 1").
		WillReturnRows(sqlmock.NewRows([]string{"id", "name", "key", "status", "remark"}).AddRow(1, "admin", "admin", 1, "超级管理员"))

	mockDb.ExpectQuery("SELECT * FROM `roles` WHERE `id` in (SELECT `role_id` FROM `users_roles` WHERE `user_id`=?)").
		WithArgs(1).
		WillReturnRows(sqlmock.NewRows([]string{"id", "name", "key", "status", "remark"}).AddRow(1, "admin", "admin", 1, "超级管理员"))

	mockDb.ExpectQuery("SELECT * FROM `menus` WHERE `id` IN (SELECT `menu_id` FROM `roles_menus` WHERE `role_id` IN (SELECT `user_id` FROM `users_roles` WHERE `user_id`=?)) AND `path` = ? and `method` = ?").
		WillReturnError(gorm.ErrRecordNotFound)

	r := tests.GetGinEngine()
	w := httptest.NewRecorder()

	req := httptest.NewRequest(http.MethodGet, "/api/v1/data/nodes?page=1&per_page=10&source_id=1&get_delete=1", nil)
	req.Header.Set("Authorization", tests.GetUserToken(1))
	r.ServeHTTP(w, req)
	t.Log(w.Body.String())
	assert.Equal(t, http.StatusOK, w.Code)
}

func TestNodeList(t *testing.T) {
	r := tests.GetGinEngine()
	w := httptest.NewRecorder()

	req := httptest.NewRequest(http.MethodGet, "/api/v1/data/node/list?page=1&per_page=10", nil)
	req.Header.Set("Authorization", tests.GetUserToken(1))
	r.ServeHTTP(w, req)
	t.Log(w.Body.String())
	assert.Equal(t, http.StatusBadRequest, w.Code)
}

func TestDelNode(t *testing.T) {
	r := tests.GetGinEngine()
	w := httptest.NewRecorder()

	req := httptest.NewRequest(http.MethodDelete, "/api/v1/data/nodes/1", nil)
	req.Header.Set("Authorization", tests.GetUserToken(1))
	r.ServeHTTP(w, req)
	t.Log(w.Body.String())
	assert.Equal(t, http.StatusBadRequest, w.Code)
}

func TestClearNode(t *testing.T) {

	r := tests.GetGinEngine()
	w := httptest.NewRecorder()

	req := httptest.NewRequest(http.MethodDelete, "/api/v1/data/clear/nodes/nil", nil)
	req.Header.Set("Authorization", tests.GetUserToken(1))
	r.ServeHTTP(w, req)
	t.Log(w.Body.String())
	assert.Equal(t, http.StatusBadRequest, w.Code)
}

func TestAuthNode(t *testing.T) {

	r := tests.GetGinEngine()
	w := httptest.NewRecorder()

	req := httptest.NewRequest(http.MethodGet, "/api/v1/data/auth_node/1", nil)
	req.Header.Set("Authorization", tests.GetUserToken(1))
	r.ServeHTTP(w, req)
	t.Log(w.Body.String())
	assert.Equal(t, http.StatusBadRequest, w.Code)
}

func TestNodeFoeye(t *testing.T) {

	r := tests.GetGinEngine()
	w := httptest.NewRecorder()

	req := httptest.NewRequest(http.MethodPost, "/api/v1/data/node/foeye", nil)
	req.Header.Set("Authorization", tests.GetUserToken(1))
	r.ServeHTTP(w, req)
	t.Log(w.Body.String())
	assert.Equal(t, http.StatusBadRequest, w.Code)
}

func TestNodeForadar(t *testing.T) {

	r := tests.GetGinEngine()
	w := httptest.NewRecorder()

	req := httptest.NewRequest(http.MethodPost, "/api/v1/data/node/foradar_saas", nil)
	req.Header.Set("Authorization", tests.GetUserToken(1))
	r.ServeHTTP(w, req)
	t.Log(w.Body.String())
	assert.Equal(t, http.StatusBadRequest, w.Code)
}

func TestNodeBKCmdb(t *testing.T) {

	r := tests.GetGinEngine()
	w := httptest.NewRecorder()

	req := httptest.NewRequest(http.MethodPost, "/api/v1/data/node/bk_cmdb", nil)
	req.Header.Set("Authorization", tests.GetUserToken(1))
	r.ServeHTTP(w, req)
	t.Log(w.Body.String())
	assert.Equal(t, http.StatusBadRequest, w.Code)
}

func TestNodeJWCmdb(t *testing.T) {

	r := tests.GetGinEngine()
	w := httptest.NewRecorder()

	req := httptest.NewRequest(http.MethodPost, "/api/v1/data/node/jw_cmdb", nil)
	req.Header.Set("Authorization", tests.GetUserToken(1))
	r.ServeHTTP(w, req)
	t.Log(w.Body.String())
	assert.Equal(t, http.StatusBadRequest, w.Code)
}
func TestNodeQty(t *testing.T) {

	r := tests.GetGinEngine()
	w := httptest.NewRecorder()

	req := httptest.NewRequest(http.MethodPost, "/api/v1/data/node/qty", nil)
	req.Header.Set("Authorization", tests.GetUserToken(1))
	r.ServeHTTP(w, req)
	t.Log(w.Body.String())
	assert.Equal(t, http.StatusBadRequest, w.Code)
}
func TestNodeDingtalk(t *testing.T) {

	r := tests.GetGinEngine()
	w := httptest.NewRecorder()

	req := httptest.NewRequest(http.MethodPost, "/api/v1/data/node/dingtalk", nil)
	req.Header.Set("Authorization", tests.GetUserToken(1))
	r.ServeHTTP(w, req)
	t.Log(w.Body.String())
	assert.Equal(t, http.StatusBadRequest, w.Code)
}

func TestNodeFoeyeUpdate(t *testing.T) {

	r := tests.GetGinEngine()
	w := httptest.NewRecorder()

	req := httptest.NewRequest(http.MethodPut, "/api/v1/data/node/foeye", nil)
	req.Header.Set("Authorization", tests.GetUserToken(1))
	r.ServeHTTP(w, req)
	t.Log(w.Body.String())
	assert.Equal(t, http.StatusBadRequest, w.Code)
}

func TestNodeForadarUpdate(t *testing.T) {

	r := tests.GetGinEngine()
	w := httptest.NewRecorder()

	req := httptest.NewRequest(http.MethodPut, "/api/v1/data/node/foradar_saas", nil)
	req.Header.Set("Authorization", tests.GetUserToken(1))
	r.ServeHTTP(w, req)
	t.Log(w.Body.String())
	assert.Equal(t, http.StatusBadRequest, w.Code)
}

func TestNodeBKCmdbUpdate(t *testing.T) {

	r := tests.GetGinEngine()
	w := httptest.NewRecorder()

	req := httptest.NewRequest(http.MethodPut, "/api/v1/data/node/bk_cmdb", nil)
	req.Header.Set("Authorization", tests.GetUserToken(1))
	r.ServeHTTP(w, req)
	t.Log(w.Body.String())
	assert.Equal(t, http.StatusBadRequest, w.Code)
}

func TestNodeJWCmdbUpdate(t *testing.T) {

	r := tests.GetGinEngine()
	w := httptest.NewRecorder()

	req := httptest.NewRequest(http.MethodPut, "/api/v1/data/node/jw_cmdb", nil)
	req.Header.Set("Authorization", tests.GetUserToken(1))
	r.ServeHTTP(w, req)
	t.Log(w.Body.String())
	assert.Equal(t, http.StatusBadRequest, w.Code)
}
func TestNodeQtyUpdate(t *testing.T) {

	r := tests.GetGinEngine()
	w := httptest.NewRecorder()

	req := httptest.NewRequest(http.MethodPut, "/api/v1/data/node/qty", nil)
	req.Header.Set("Authorization", tests.GetUserToken(1))
	r.ServeHTTP(w, req)
	t.Log(w.Body.String())
	assert.Equal(t, http.StatusBadRequest, w.Code)
}
func TestNodeDingtalkUpdate(t *testing.T) {

	r := tests.GetGinEngine()
	w := httptest.NewRecorder()

	req := httptest.NewRequest(http.MethodPut, "/api/v1/data/node/dingtalk", nil)
	req.Header.Set("Authorization", tests.GetUserToken(1))
	r.ServeHTTP(w, req)
	t.Log(w.Body.String())
	assert.Equal(t, http.StatusBadRequest, w.Code)
}

func TestNodeData(t *testing.T) {

	r := tests.GetGinEngine()
	w := httptest.NewRecorder()

	req := httptest.NewRequest(http.MethodGet, "/api/v1/node/data?page=1&per_page=10&node_id=1&sync_type=1&keyword=wsman", nil)
	req.Header.Set("Authorization", tests.GetUserToken(1))
	r.ServeHTTP(w, req)
	t.Log(w.Body.String())
	assert.Equal(t, http.StatusBadRequest, w.Code)
}

func TestNodeGetDataFoeyeAsset(t *testing.T) {
	//r := tests.GetGinEngine()
	//w := httptest.NewRecorder()
	//
	//req := httptest.NewRequest(http.MethodGet, "/api/v1/node/data?page=1&per_page=10&node_id=1&sync_type=1&keyword=wsman", nil)
	//req.Header.Set("Authorization", tests.GetUserToken(1))
	//r.ServeHTTP(w, req)
	//t.Log(w.Body.String())
	//assert.Equal(t, http.StatusOK, w.Code)
}

func TestNodeGetDataFoeyeThreat(t *testing.T) {
	//r := tests.GetGinEngine()
	//w := httptest.NewRecorder()
	//
	//req := httptest.NewRequest(http.MethodGet, "/api/v1/node/data?page=1&per_page=10&node_id=1&sync_type=2&keyword=", nil)
	//req.Header.Set("Authorization", tests.GetUserToken(1))
	//r.ServeHTTP(w, req)
	//t.Log(w.Body.String())
	//assert.Equal(t, http.StatusOK, w.Code)
}

func TestNodeGetDataForadarAsset(t *testing.T) {
	//r := tests.GetGinEngine()
	//w := httptest.NewRecorder()
	//
	//req := httptest.NewRequest(http.MethodGet, "/api/v1/node/data?page=1&per_page=10&node_id=2&sync_type=1&keyword=117.50.123.29", nil)
	//req.Header.Set("Authorization", tests.GetUserToken(1))
	//r.ServeHTTP(w, req)
	//t.Log(w.Body.String())
	//assert.Equal(t, http.StatusOK, w.Code)
}

func TestNodeGetDataForadarThreat(t *testing.T) {
	//r := tests.GetGinEngine()
	//w := httptest.NewRecorder()
	//
	//req := httptest.NewRequest(http.MethodGet, "/api/v1/node/data?page=1&per_page=10&node_id=2&sync_type=2&keyword=1.1.1.1", nil)
	//req.Header.Set("Authorization", tests.GetUserToken(1))
	//r.ServeHTTP(w, req)
	//t.Log(w.Body.String())
	//assert.Equal(t, http.StatusOK, w.Code)
}

func TestNodeGetDataQTCloudAsset(t *testing.T) {
	//r := tests.GetGinEngine()
	//w := httptest.NewRecorder()
	//
	//req := httptest.NewRequest(http.MethodGet, "/api/v1/node/data?page=1&per_page=10&node_id=3&sync_type=1&keyword=", nil)
	//req.Header.Set("Authorization", tests.GetUserToken(1))
	//r.ServeHTTP(w, req)
	//t.Log(w.Body.String())
	//assert.Equal(t, http.StatusOK, w.Code)
}

func TestNodeGetDataQTCloudThreat(t *testing.T) {
	//r := tests.GetGinEngine()
	//w := httptest.NewRecorder()
	//
	//req := httptest.NewRequest(http.MethodGet, "/api/v1/node/data?page=1&per_page=10&node_id=3&sync_type=2&keyword=", nil)
	//req.Header.Set("Authorization", tests.GetUserToken(1))
	//r.ServeHTTP(w, req)
	//t.Log(w.Body.String())
	//assert.Equal(t, http.StatusOK, w.Code)
}

func TestNodeGetDataQTCloudPeople(t *testing.T) {
	//r := tests.GetGinEngine()
	//w := httptest.NewRecorder()
	//
	//req := httptest.NewRequest(http.MethodGet, "/api/v1/node/data?page=1&per_page=10&node_id=3&sync_type=3&keyword=", nil)
	//req.Header.Set("Authorization", tests.GetUserToken(1))
	//r.ServeHTTP(w, req)
	//t.Log(w.Body.String())
	//assert.Equal(t, http.StatusOK, w.Code)
}

func TestNodeGetDataDingTalkPeople(t *testing.T) {
	//r := tests.GetGinEngine()
	//w := httptest.NewRecorder()
	//
	//req := httptest.NewRequest(http.MethodGet, "/api/v1/node/data?page=1&per_page=10&node_id=4&sync_type=3&keyword=", nil)
	//req.Header.Set("Authorization", tests.GetUserToken(1))
	//r.ServeHTTP(w, req)
	//t.Log(w.Body.String())
	//assert.Equal(t, http.StatusOK, w.Code)
}

func TestNodeGetDataBkCmdbAsset(t *testing.T) {
	//r := tests.GetGinEngine()
	//w := httptest.NewRecorder()
	//
	//req := httptest.NewRequest(http.MethodGet, "/api/v1/node/data?page=1&per_page=10&node_id=5&sync_type=1&keyword=", nil)
	//req.Header.Set("Authorization", tests.GetUserToken(1))
	//r.ServeHTTP(w, req)
	//t.Log(w.Body.String())
	//assert.Equal(t, http.StatusOK, w.Code)
}

func TestNodeGetDataBkCmdbPeople(t *testing.T) {
	//r := tests.GetGinEngine()
	//w := httptest.NewRecorder()
	//
	//req := httptest.NewRequest(http.MethodGet, "/api/v1/node/data?page=1&per_page=10&node_id=5&sync_type=3&keyword=", nil)
	//req.Header.Set("Authorization", tests.GetUserToken(1))
	//r.ServeHTTP(w, req)
	//t.Log(w.Body.String())
	//assert.Equal(t, http.StatusOK, w.Code)
}
