package auth

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"

	"fobrain/fobrain/app/controller/auth"
	"fobrain/fobrain/tests"
)

func TestCaptcha(t *testing.T) {
	r := tests.GetGinEngine()
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/captcha", nil)
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusBadRequest, w.Code)
}

func TestLogin(t *testing.T) {
	r := tests.GetGinEngine()
	w := httptest.NewRecorder()
	val, _ := json.Marshal(auth.LoginRequest{Account: "admin", Password: "xxx", CaptchaId: "xxx", CaptchaCode: "xxx"})
	req := httptest.NewRequest("POST", "/api/v1/auth/login", strings.NewReader(string(val)))
	req.Header.Set("Content-Type", "application/json")
	r.Serve<PERSON>TP(w, req)
	assert.Equal(t, http.StatusBadRequest, w.Code)
}

func TestGenApiToken(t *testing.T) {
	r := tests.GetGinEngine()
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/user/gen-api-token", strings.NewReader(""))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", tests.GetUserToken(1))
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusBadRequest, w.Code)
}
