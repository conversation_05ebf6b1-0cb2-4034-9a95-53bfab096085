package networks

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"gorm.io/gorm"

	"github.com/stretchr/testify/assert"

	testcommon "fobrain/fobrain/tests/common_test"
	mockblackwhite "fobrain/fobrain/tests/mock_black_white"

	"fobrain/fobrain/tests"
)

func TestGetNetworksSettings(t *testing.T) {

	r := tests.GetGinEngine()
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/settings/networks", nil)
	req.Header.Set("Authorization", tests.GetUserToken(1))
	r.ServeHTTP(w, req)
	t.Log(w.Body.String())
	assert.Equal(t, http.StatusBadRequest, w.Code)
}

func TestSetMultiConfig(t *testing.T) {

	r := tests.GetGinEngine()
	w := httptest.NewRecorder()
	data := map[string]interface{}{
		"network_work_addr":         "10.10.10.164",
		"network_work_mask":         "255.255.254.0",
		"network_work_gateway_addr": "10.10.10.1",
		"network_work_dns_master":   "114.114.114.114",
		"network_work_dns_slave":    "192.168.1.1",
	}

	jsonData, _ := json.Marshal(data)

	req := httptest.NewRequest("PUT", "/api/v1/settings/networks", strings.NewReader(string(jsonData)))
	req.Header.Set("Authorization", tests.GetUserToken(1))
	req.Header.Set("Content-Type", "application/json")
	r.ServeHTTP(w, req)

	assert.Equal(t, http.StatusBadRequest, w.Code)
}

func TestPing(t *testing.T) {
	mockblackwhite.CommonBlackWhiteTest()

	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()
	mockDb.ExpectQuery("SELECT * FROM `users` WHERE `id` = ? ORDER BY `users`.`id` LIMIT 1").
		WillReturnRows(sqlmock.NewRows([]string{"id", "name", "key", "status", "remark"}).AddRow(1, "admin", "admin", 1, "超级管理员"))

	mockDb.ExpectQuery("SELECT * FROM `roles` WHERE `id` in (SELECT `role_id` FROM `users_roles` WHERE `user_id`=?)").
		WithArgs(1).
		WillReturnRows(sqlmock.NewRows([]string{"id", "name", "key", "status", "remark"}).AddRow(1, "admin", "admin", 1, "超级管理员"))

	mockDb.ExpectQuery("SELECT * FROM `menus` WHERE `id` IN (SELECT `menu_id` FROM `roles_menus` WHERE `role_id` IN (SELECT `user_id` FROM `users_roles` WHERE `user_id`=?)) AND `path` = ? and `method` = ?").
		WillReturnError(gorm.ErrRecordNotFound)

	r := tests.GetGinEngine()
	w := httptest.NewRecorder()
	data := map[string]interface{}{
		"type": "ping",
		"addr": "127.0.0.1",
	}

	jsonData, _ := json.Marshal(data)

	req := httptest.NewRequest("POST", "/api/v1/settings/ping", strings.NewReader(string(jsonData)))
	req.Header.Set("Authorization", tests.GetUserToken(1))
	req.Header.Set("Content-Type", "application/json")
	r.ServeHTTP(w, req)
	t.Log(w.Body)
	assert.Equal(t, http.StatusOK, w.Code)
}
