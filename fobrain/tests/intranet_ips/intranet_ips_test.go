package intranet_ips

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"

	testcommon "fobrain/fobrain/tests/common_test"

	"fobrain/fobrain/tests"
)

func TestGetIntranetIpList(t *testing.T) {
	mockDb := testcommon.InitSqlMock()
	defer mockDb.Close()

	mockDb.ExpectQuery("SELECT * FROM `users` WHERE `id` = ? ORDER BY `users`.`id` LIMIT 1").
		WillReturnRows(sqlmock.NewRows([]string{"id", "name", "key", "status", "remark"}).AddRow(1, "admin", "admin", 1, "超级管理员"))

	r := tests.GetGinEngine()
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/settings/intranet_ips", nil)
	req.Header.Set("Authorization", tests.GetUserToken(1))
	r.ServeHTTP(w, req)
	t.Log(w.Body.String())
	assert.Equal(t, http.StatusBadRequest, w.Code)
}
func TestUpdateIntranetIpList(t *testing.T) {

	r := tests.GetGinEngine()
	w := httptest.NewRecorder()

	data := map[string]interface{}{
		"other_config_intranet_ips": []string{
			"***********",
			"************",
		},
	}

	jsonData, _ := json.Marshal(data)

	req := httptest.NewRequest("PUT", "/api/v1/settings/intranet_ips", strings.NewReader(string(jsonData)))
	req.Header.Set("Authorization", tests.GetUserToken(1))
	req.Header.Set("Content-Type", "application/json")
	r.ServeHTTP(w, req)

	assert.Equal(t, http.StatusBadRequest, w.Code)
}
