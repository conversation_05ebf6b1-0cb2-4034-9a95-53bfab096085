package testcommon

import (
	"github.com/go-redis/redis/v8"
	"sync"
)

var redisClient *redis.Client

var lock = &sync.Mutex{}

func SetRedisClient(client *redis.Client) {
	// 添加锁，避免并发时SetRedisClient和GetRedisClient同时执行
	lock.Lock()
	defer lock.Unlock()
	redisClient = client
}

func GetRedisClient() *redis.Client {
	// 添加锁，避免并发时SetRedisClient和GetRedisClient同时执行
	lock.Lock()
	defer lock.Unlock()
	return redisClient
}
