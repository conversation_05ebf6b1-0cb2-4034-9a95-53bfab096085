package mockblackwhite

import (
	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/models/mysql/system_configs"

	"github.com/DATA-DOG/go-sqlmock"
)

func CommonBlackWhiteTest() {
	mockDb := testcommon.InitSqlMock()
	mockDb.ExpectQuery("SELECT `ip` FROM `black_white_config` WHERE strategy_type = ?").
		WithArgs(system_configs.WhiteStrategyType).
		WillReturnRows(
			sqlmock.NewRows([]string{"ip"}),
		)
	mockDb.ExpectQuery("SELECT `ip` FROM `black_white_config` WHERE strategy_type = ?").
		WithArgs(system_configs.BlackStrategyType).
		WillReturnRows(
			sqlmock.NewRows([]string{"ip"}).
				AddRow("***********-*************").
				AddRow("***********/24").
				AddRow("*************"),
		)
}
