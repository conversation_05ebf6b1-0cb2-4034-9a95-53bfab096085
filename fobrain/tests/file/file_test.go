package file

import (
	"testing"
)

func TestFileDownload(t *testing.T) {
	//r := tests.GetGinEngine()
	//w := httptest.NewRecorder()
	//url := utils.GenFileDownloadUrl(filepath.Join(config.Get().Storage, ".gitignore"), false)
	//req := httptest.NewRequest("GET", url, nil)
	//r.ServeHTTP(w, req)
	//assert.Equal(t, http.StatusOK, w.Code)
	//assert.Contains(t, w.Body.String(), ".idea")
}

func TestStorageDownload(t *testing.T) {
	//r := tests.GetGinEngine()
	//w := httptest.NewRecorder()
	//url := utils.GenStorageDownloadUrl(".gitignore", false)
	//req := httptest.NewRequest("GET", url, nil)
	//r.ServeHTTP(w, req)
	//assert.Equal(t, http.StatusOK, w.Code)
	//assert.Contains(t, w.Body.String(), ".idea")
}
