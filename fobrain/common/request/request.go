package request

import (
	"bytes"
	"errors"
	"fobrain/models/mysql/role"
	"io"
	"mime/multipart"
	"net/http"
	"path/filepath"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"github.com/spf13/cast"

	"fobrain/fobrain/common/validate"
	"fobrain/fobrain/config"
	"fobrain/initialize/mysql"
	"fobrain/models/mysql/user"
	"fobrain/pkg/utils"
)

type (
	// PageRequest 分页请求
	PageRequest struct {
		Page    int `json:"page" form:"page" uri:"page" validate:"required,number,min=1" zh:"页数"`
		PerPage int `json:"per_page" form:"per_page" uri:"per_page" validate:"required,number,min=1" zh:"条数"`
	}
	// MultiUploadInfo 批量上传信息
	MultiUploadInfo struct {
		FullPath string
		AbsPath  string
	}
)

var (
	// AllowTypesByImages 图片类型
	AllowTypesByImages = []string{FileTypeJpg, FileTypePng, FileTypeGif, FileTypeSvg}
	// AllowTypesByDoc 文档类型
	AllowTypesByDoc = []string{FileTypeDoc, FileTypeDocx}
	// AllowTypesByXsl excel类型
	AllowTypesByXsl = []string{FileTypeXls, FileTypeXlsx, FileTypeCsv}
)

const (
	FileTypeCsv  = "text/csv"
	FileTypeXls  = "application/vnd.ms-excel"
	FileTypeXlsx = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
	FileTypeJpg  = "image/jpeg"
	FileTypePng  = "image/png"
	FileTypeGif  = "image/gif"
	FileTypeSvg  = "image/svg+xml"
	FileTypeDoc  = "application/msword"
	FileTypeDocx = "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
)

// Validate 请求参数校验
func Validate[T any](ctx *gin.Context, request *T) (*T, error) {
	if err := ShouldBind(ctx, request); err != nil {
		return nil, err
	}
	// 继续执行验证逻辑
	if ok, msg := validate.Validator(request); !ok {
		return nil, errors.New(msg)
	}
	return request, nil
}

// ShouldBind 解析请求参数
func ShouldBind[T any](ctx *gin.Context, request *T) error {
	// 解析path路径参数
	if strings.Contains(ctx.FullPath(), "/:") {
		if err := ctx.ShouldBindUri(request); err != nil {
			return err
		}
	}
	// 解析Get请求
	if ctx.Request.Method == http.MethodGet {
		if err := ctx.ShouldBindQuery(request); err != nil {
			return err
		}
	} else if ctx.Request.Method == http.MethodDelete {
		// 处理DELETE请求，优先绑定查询参数
		if err := ctx.ShouldBindQuery(request); err != nil {
			return err
		}
		// 如果是JSON格式，尝试解析请求体
		if ctx.ContentType() == binding.MIMEJSON {
			fullEmptyJsonByEmptyBody(ctx)
			if err := ctx.ShouldBindJSON(request); err != nil {
				return err
			}
		}
	} else if ctx.ContentType() == binding.MIMEJSON {
		fullEmptyJsonByEmptyBody(ctx)
		// 解析其他参数
		if err := ctx.ShouldBindJSON(request); err != nil {
			return err
		}
	} else {
		// 解析其他参数
		if err := ctx.ShouldBind(request); err != nil {
			return err
		}
	}
	return nil
}

// GetUserId 获取用户ID
func GetUserId(c *gin.Context) uint64 {
	if userId, ok := c.Get("user_id"); !ok {
		return 0
	} else {
		return cast.ToUint64(userId)
	}
}

// GetTokenType 获取Token类型
func GetTokenType(c *gin.Context) int {
	if tokenType, ok := c.Get("token_type"); !ok {
		return 0
	} else {
		return cast.ToInt(tokenType)
	}
}

// GetUserInfo 获取用户信息
func GetUserInfo(c *gin.Context) *user.User {
	userId, ok := c.Get("user_id")
	// fmt.Println(userId, ok)
	if !ok {
		id := c.GetHeader("id")
		if id == "" {
			return nil
		}
		userId = cast.ToUint64(id)
	}
	users, err := user.NewUserModel().First(mysql.WithId(userId.(uint64)))
	if err != nil {
		return nil
	}
	r := role.NewRoleModel().GetUserRole(users.Id)
	users.Role = user.Role{Id: r.Id, Name: r.Name}
	users.Super = users.IsSuperAdmin(userId.(uint64))
	return &users
}

// Upload 单文件上传
func Upload(c *gin.Context, key string, allowTypes []string) (string, string, error) {
	file, _ := c.FormFile(key)
	if fileType, err := GetFileType(file); err != nil {
		return "", "", err
	} else if !utils.ListContains(allowTypes, fileType) {
		return "", "", errors.New("不被允许上传的文件类型:" + fileType)
	}
	// 生成保存路径
	path := filepath.Join("uploads", time.Now().Format("2006/01/02"), file.Filename)
	// 完成路径
	dst := filepath.Join(config.Get().Storage, path)
	// 保存文件
	if err := c.SaveUploadedFile(file, dst); err != nil {
		return "", "", err
	}
	return dst, path, nil
}

// UploadMulti 批量上传
func UploadMulti(c *gin.Context, key string, allowTypes []string) ([]*MultiUploadInfo, error) {
	form, _ := c.MultipartForm()
	fileInfos := make([]*MultiUploadInfo, 0)
	files := form.File[key]
	for i := 0; i < len(files); i++ {
		file := files[i]
		if fileType, err := GetFileType(file); err != nil {
			return fileInfos, err
		} else if !utils.ListContains(allowTypes, fileType) {
			return fileInfos, errors.New("不被允许上传的文件类型:" + fileType)
		}
		// 生成保存路径
		path := filepath.Join("uploads", time.Now().Format("2006/01/02"), file.Filename)
		// 完成路径
		dst := filepath.Join(config.Get().Storage, path)
		// 保存文件
		if err := c.SaveUploadedFile(file, dst); err != nil {
			return fileInfos, err
		}
		fileInfos = append(fileInfos, &MultiUploadInfo{FullPath: dst, AbsPath: path})
	}
	return fileInfos, nil
}

// GetFileType 获取文件夹类型
func GetFileType(file *multipart.FileHeader) (string, error) {
	// 打开文件
	src, err := file.Open()
	if err != nil {
		return "", err
	}
	defer src.Close()
	// 读取文件开头的几个字节
	buffer := make([]byte, 512)
	_, err = src.Read(buffer)
	if err != nil && err != io.EOF {
		return "", err
	}
	// 检查文件类型
	fileType := http.DetectContentType(buffer)
	return fileType, nil
}

func fullEmptyJsonByEmptyBody(c *gin.Context) {
	bodyBytes, err := io.ReadAll(c.Request.Body)
	if err != nil {
		return
	}
	// Check if the body is empty
	if len(bodyBytes) == 0 {
		bodyBytes = []byte("{}")
	}
	c.Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))
}
