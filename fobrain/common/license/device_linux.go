package license

/*
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/socket.h>
#include <sys/ioctl.h>
#include <net/if.h>
#include <unistd.h>

#include <errno.h>
#include <fcntl.h>
#include <linux/hdreg.h>


char * mac_serial(char *iface){
    int fd;
    struct ifreq ifr;
    unsigned char *mac = NULL;
    char *rtn=malloc(21);

    errno=0;
    memset(&ifr, 0, sizeof(ifr));

    fd = socket(AF_INET, SOCK_DGRAM, 0);

    ifr.ifr_addr.sa_family = AF_INET;
    strncpy(ifr.ifr_name , iface , IFNAMSIZ-1);

    if (0 == ioctl(fd, SIOCGIFHWADDR, &ifr)) {
        mac = (unsigned char *)ifr.ifr_hwaddr.sa_data;

        sprintf(rtn,"%.2X:%.2X:%.2X:%.2X:%.2X:%.2X" , mac[0], mac[1], mac[2], mac[3], mac[4], mac[5]);
    }

    close(fd);

    return rtn;
}

char * disk_serial(char *devname)
{
    static struct hd_driveid hd;
    int fd;
    char *rtn=malloc(21);
    errno=0;

    if ((fd = open(devname, O_RDONLY|O_NONBLOCK)) < 0) { //can't open
        errno=2;
        return NULL;
    }

    if (!ioctl(fd, HDIO_GET_IDENTITY, &hd)) {
        sprintf(rtn,"%.20s",hd.serial_no);
        return rtn;
    } else if (errno == -ENOMSG) {
        errno=3;
        return NULL;
    } else { //HDIO_GET_IDENTITY
        errno=4;
        return NULL;
    }
}
*/
import "C"

import (
	"fmt"
	"net"
	"strings"
	"unsafe"
)

/**
 * 删除字符串空格
 *
 */
func deleteExtraSpace(s string) string {
	str := strings.Replace(s, " ", "", -1)
	return str
}

/**
 * 获取mac地址
 *
 */
func getMacAddr(ethName string) string {
	// 指定网卡名称
	//inter, _ := net.InterfaceByName(ethName)
	//macAddr := inter.HardwareAddr.String()

	networks, err := net.Interfaces()
	if err != nil {
		fmt.Println(err)
		return ""
	}
	var macAddr = ""
	for i := 0; i < len(networks); i++ {
		if networks[i].HardwareAddr != nil {
			macAddr = networks[i].HardwareAddr.String()
			break
		}
	}
	return strings.ToUpper(macAddr)
}

/**
 * 获取mac，硬盘序列号
 *
 */
func getMacAndDisk(ethName string, diskName string) (string, string) {
	//手动获取网卡
	//mac,err0 := C.mac_serial(C.CString(ethName))
	//defer C.free(unsafe.Pointer(mac))
	//if err0 != nil{
	//	return "", ""
	//}

	//自动获取网卡地址
	mac := getMacAddr(ethName)
	//获取硬盘
	diskNum, err := C.disk_serial(C.CString(diskName))
	defer C.free(unsafe.Pointer(diskNum))
	if err != nil {
		return mac, strings.Replace(mac, ":", "", -1)
	} else {
		return mac, deleteExtraSpace(C.GoString(diskNum))
	}
}

/*
 * 获取硬盘序列号
 */
func getDisk(diskName string, ethName string) string {
	//获取硬盘
	diskNum, err := C.disk_serial(C.CString(diskName))
	//defer C.free(unsafe.Pointer(diskNum))
	if err != nil {
		mac := getMacAddr(ethName)
		return strings.Replace(mac, ":", "", -1)
	}
	return deleteExtraSpace(C.GoString(diskNum))
}
