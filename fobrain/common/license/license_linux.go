package license

import (
	_ "embed"
	"errors"
	"fmt"
	"github.com/google/martian/log"
	"os"
	"os/exec"
	"runtime"
	"strings"

	"fobrain/pkg/cfg"

	"github.com/spf13/cast"

	"fobrain/pkg/utils"
)

var (
	//go:embed binary/license_amd64
	licenseBinaryAmd64 []byte
	//go:embed binary/license_arm64
	licenseBinaryArm64 []byte
)

// Activation 激活
func (l *License) Activation(code string) (bool, error) {
	defer func() {
		if err := recover(); err != nil {
			log.Infof("Activation panic: %v\n", err)
		}
	}()

	expireCode, err := l.CheckExpireCode(code)
	if err != nil {
		return false, err
	}
	if !expireCode {
		return false, fmt.Errorf("激活码无效")
	}
	err = utils.CreateFileIfNotExist(cfg.LoadCommon().LicensePath)
	if err != nil {
		return false, err
	}
	if err = os.WriteFile(cfg.LoadCommon().LicensePath, []byte(code), 0644); err != nil {
		return false, err
	}
	result, err := l.runBinary("-m", "activationSuccess", "-f", cfg.LoadCommon().LicensePath)
	if err != nil {
		return false, err
	}
	return cast.ToInt(result) == 1, nil
}

// IsProductExpired 检测产品是否已过期
func (l *License) IsProductExpired() (bool, error) {
	result, err := l.runBinary("-m", "checkExpire", "-f", cfg.LoadCommon().LicensePath)
	if err != nil {
		return false, err
	}
	return cast.ToInt(result) == 0, nil
}

// IsServiceExpired 检测服务是否已过期
func (l *License) IsServiceExpired() (bool, error) {
	result, err := l.runBinary("-m", "checkServerTime", "-f", cfg.LoadCommon().LicensePath)
	if err != nil {
		return false, err
	}
	return cast.ToInt(result) == 0, nil
}

// GetProductModels 获取产品授权模块
func (l *License) GetProductModels() ([]string, error) {
	models := make([]string, 0)
	result, err := l.runBinary("-m", "getProductModels", "-f", cfg.LoadCommon().LicensePath)
	if err != nil {
		return models, err
	}
	return strings.Split(cast.ToString(result), ","), nil
}

// GetDataSources 获取数据源
func (l *License) GetDataSources(devModel bool) ([]string, error) {
	if devModel {
		return []string{"all"}, nil
	}
	models := make([]string, 0)
	result, err := l.runBinary("-m", "getDataSources", "-f", cfg.LoadCommon().LicensePath)
	if err != nil {
		return models, err
	}
	return strings.Split(cast.ToString(result), ","), nil
}

// CheckExpireCode 检测延期码
func (l *License) CheckExpireCode(code string) (bool, error) {
	result, err := l.runBinary("-m", "checkCode", "-code", code)
	if err != nil {
		return false, err
	}
	return cast.ToInt(result) == 1, nil
}

// GetProductExpireDate 获取产品有效期
func (l *License) GetProductExpireDate() (string, error) {
	result, err := l.runBinary("-m", "getExpireDate", "-f", cfg.LoadCommon().LicensePath)
	if err != nil {
		return "", err
	}
	return cast.ToString(result), nil
}

// GetServerExpireDate 获取产品有效期
func (l *License) GetServerExpireDate() (string, error) {
	result, err := l.runBinary("-m", "getServerDate", "-f", cfg.LoadCommon().LicensePath)
	if err != nil {
		return "", err
	}
	return cast.ToString(result), nil
}

// GetLicenseAssetCount 获取资产数量管理上限
func (l *License) GetLicenseAssetCount() (int, error) {
	if !l.HasLicenseFile() {
		return 0, nil
	}
	result, err := l.runBinary("-m", "getLicenseAssetCount", "-f", cfg.LoadCommon().LicensePath)
	if err != nil {
		return 0, err
	}
	return cast.ToInt(result), nil
}

// GetMacAddr 获取MAC地址
func (l *License) GetMacAddr() (string, error) {
	result, err := l.runBinary("-m", "getMacAddr")
	if err != nil {
		return "", err
	}
	return cast.ToString(result), nil
}

// GetDiskSn 获取磁盘序列号
func (l *License) GetDiskSn() (string, error) {
	result, err := l.runBinary("-m", "getDisk")
	if err != nil {
		return "", err
	}
	return cast.ToString(result), nil
}

// GetMacAndDisk 序列号信息 mac地址 ， 硬盘序列
func (l *License) GetMacAndDisk() (string, string, error) {
	result, err := l.runBinary("-m", "getMacAndDisk")
	if err != nil {
		return "", "", err
	}
	arr := strings.Split(cast.ToString(result), ",")
	if len(arr) == 1 {
		return arr[0], "", nil
	}
	if len(arr) == 2 {
		return arr[0], arr[1], nil
	}
	return "", "", errors.New("GetMacAndDisk error")
}

// Activation 激活
func (l *License) runBinary(args ...string) (any, error) {
	defer func() {
		if err := recover(); err != nil {
			log.Infof("runBinary panic: %v\n", err)
		}
	}()

	var data []byte
	var err error
	var tmpFile *os.File
	if !utils.FileExists(l.tmpBinaryPath) {
		if runtime.GOARCH == "arm64" || runtime.GOARCH == "arm" {
			data = licenseBinaryArm64
		} else {
			data = licenseBinaryAmd64
		}
		// 在临时目录创建一个临时文件
		tmpFile, err = os.CreateTemp("", utils.GenerateRandomString(10))
		if err != nil {
			return false, err
		}
		// 将二进制数据写入临时文件
		if _, err = tmpFile.Write(data); err != nil {
			return false, err
		}
		// 关闭临时文件，确保其他程序可以执行
		if err = tmpFile.Close(); err != nil {
			return false, err
		}
		// 赋予临时文件执行权限
		if err = os.Chmod(tmpFile.Name(), 0700); err != nil {
			return false, err
		}
		l.tmpBinaryPath = tmpFile.Name()
	}
	cmd := exec.Command(l.tmpBinaryPath, args...)
	output, err := cmd.CombinedOutput()
	log.Infof("runBinary l.tmpBinaryPath info: %v\n", l.tmpBinaryPath)
	log.Infof("runBinary args info: %v\n", args)
	log.Infof("runBinary output info: %v\n", output)
	log.Infof("runBinary err info: %v\n", err)
	if err != nil {
		if strings.Contains(err.Error(), "exec format error") {
			return false, errors.New("platform not supported")
		}
		return false, err
	}
	return string(output), nil
}

// ClearTmpBinary 清除临时执行文件
func (l *License) ClearTmpBinary() {
	_ = os.Remove(l.tmpBinaryPath)
	l.tmpBinaryPath = ""
}
