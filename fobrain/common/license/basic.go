package license

import (
	"encoding/json"
	"fobrain/fobrain/app/services/system_configs/upgrade"
	"fobrain/models/mysql/system_configs"
	"fobrain/pkg/cfg"
	"fobrain/pkg/utils"
	"log"
	"strings"
	"sync"
)

var (
	once       sync.Once
	license    *License
	encryptKey = "pio!lM)_5-x!k_ek4jCaaSms83r2Q^Zo"
)

const (
	NotActive      = 1 //未激活
	IsActive       = 2 //已激活
	TestExpired    = 3 //测试过期
	ServiceExpired = 4 //服务过期
)

// License 授权信息
type License struct {
	tmpBinaryPath     string
	Model             string `json:"model"`
	VersionType       string `json:"version_type"`
	Version           string `json:"version"`
	DeviceId          string `json:"device_id"`
	Mac               string `json:"mac"`
	LicenseAssetCount int    `json:"license_asset_count"`
	ExpireData        string `json:"expire_data"`
	ServerData        string `json:"server_data"`
	ActivationState   int8   `json:"activation_state"`
	Copyright         string `json:"copyright"`
}

// GetLicense 获取license及信息
func GetLicense() *License {
	if license == nil {
		once.Do(func() {
			license = &License{
				Model:             cfg.LoadCommon().SysName + ":" + cfg.LoadCommon().Version,
				VersionType:       "试用版",
				Version:           "",
				LicenseAssetCount: 0,
				ExpireData:        "License未激活",
				ServerData:        "",
				ActivationState:   NotActive, // 1未激活 2 已激活 3测试已过期 4运维服务已过期
				Copyright:         cfg.LoadCommon().Copyright,
			}
		})
	}
	data, err := system_configs.NewSystemConfigs().GetConfig(system_configs.SystemInfoConfigKey)
	if err == nil {
		var conf system_configs.SystemInfoConfig
		err = json.Unmarshal([]byte(data), &conf)
		if err == nil {
			license.Model = conf.ProductModel
			license.Copyright = conf.Copyright
		}
	}
	return license
}

// HasLicenseFile 系统有license文件 代表已激活过
func (l *License) HasLicenseFile() bool {
	if utils.FileExists(cfg.LoadCommon().LicensePath) {
		return true
	}
	return false
}

// GetExpireInfo 获取激活信息
func (l *License) GetExpireInfo() error {
	defer func() {
		if err := recover(); err != nil {
			log.Printf("GetExpireInfo panic: %v\n", err)
		}
	}()

	info, err := upgrade.GetVersionInfo(upgrade.VersionFilePath, true)
	if err != nil {
		l.Version = "2.0.0"
	} else {
		l.Version = info.ReleaseVersion
	}
	hasExpireFile := l.HasLicenseFile()
	if hasExpireFile {
		l.ActivationState = IsActive
		isExpired, err := l.IsProductExpired()
		if err != nil {
			return err
		}
		if isExpired {
			l.ActivationState = TestExpired
		}
		expireData, err := l.GetProductExpireDate()
		if err != nil {
			return err
		}
		l.ExpireData = expireData
		if 0 == strings.Compare(expireData, "0-0-0") {
			l.ExpireData = "无限期"
			l.VersionType = "正式版"
		}
		IsServiceExpired, err := l.IsServiceExpired()
		log.Printf("GetExpireInfo IsServiceExpired info:%v", IsServiceExpired)
		log.Printf("GetExpireInfo IsServiceExpired err info:%v", err)
		if err != nil {
			return err
		}
		if IsServiceExpired {
			l.ActivationState = ServiceExpired
		}
		serverData, err := l.GetServerExpireDate()
		log.Printf("GetExpireInfo serverData info:%v", IsServiceExpired)
		log.Printf("GetExpireInfo serverData err info:%v", err)
		if err != nil {
			return err
		}
		l.ServerData = serverData
		l.LicenseAssetCount, err = l.GetLicenseAssetCount()
		if err != nil {
			return err
		}
	}
	mac, disk, err := l.GetMacAndDisk()
	if err != nil {
		//return err
	}
	l.DeviceId = disk
	l.Mac = mac
	return nil
}
