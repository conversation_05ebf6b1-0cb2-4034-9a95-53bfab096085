package license

import (
	"fmt"
	"syscall"
	"unsafe"
)

// 定义Windows API函数
var (
	modkernel32 = syscall.NewLazyDLL("kernel32.dll")
	modiphlpapi = syscall.NewLazyDLL("iphlpapi.dll")

	procGetVolumeInformation = modkernel32.NewProc("GetVolumeInformationW")
	procGetAdaptersAddresses = modiphlpapi.NewProc("GetAdaptersAddresses")
)

// 定义IP_ADAPTER_UNICAST_ADDRESS结构体
type IP_ADAPTER_UNICAST_ADDRESS struct {
	Length  uint32
	Flags   uint32
	Next    *IP_ADAPTER_UNICAST_ADDRESS
	Address syscall.Sockaddr
}

// 定义缺失的Windows API结构体
type IP_ADAPTER_ANYCAST_ADDRESS struct {
	Length uint32
	Flags  uint32
	Next   *IP_ADAPTER_ANYCAST_ADDRESS
}

type IP_ADAPTER_MULTICAST_ADDRESS struct {
	Length uint32
	Flags  uint32
	Next   *IP_ADAPTER_MULTICAST_ADDRESS
}

type IP_ADAPTER_DNS_SERVER_ADDRESS struct {
	Length  uint32
	Flags   uint32
	Next    *IP_ADAPTER_DNS_SERVER_ADDRESS
	Address syscall.Sockaddr
}

type IP_ADAPTER_PREFIX struct {
	Length       uint32
	Flags        uint32
	Next         *IP_ADAPTER_PREFIX
	Address      syscall.Sockaddr
	PrefixLength uint32
}

// 定义Windows API结构体
type IP_ADAPTER_ADDRESSES struct {
	Length                uint32
	IfIndex               uint32
	Next                  *IP_ADAPTER_ADDRESSES
	AdapterName           *byte
	FirstUnicastAddress   *IP_ADAPTER_UNICAST_ADDRESS
	FirstAnycastAddress   *IP_ADAPTER_ANYCAST_ADDRESS
	FirstMulticastAddress *IP_ADAPTER_MULTICAST_ADDRESS
	FirstDnsServerAddress *IP_ADAPTER_DNS_SERVER_ADDRESS
	DnsSuffix             *uint16
	Description           *uint16
	FriendlyName          *uint16
	PhysicalAddress       [syscall.MAX_ADAPTER_ADDRESS_LENGTH]byte
	PhysicalAddressLength uint32
	Flags                 uint32
	Mtu                   uint32
	IfType                uint32
	OperStatus            uint32
	Ipv6IfIndex           uint32
	ZoneIndices           [16]uint32
	FirstPrefix           *IP_ADAPTER_PREFIX
}

// GetVolumeInformation 获取硬盘序列号
func GetVolumeInformation(rootPathName string) (string, error) {
	var volumeNameBuffer = make([]uint16, 256)
	var volumeSerialNumber uint32
	var maximumComponentLength uint32
	var fileSystemFlags uint32
	var fileSystemNameBuffer = make([]uint16, 256)

	rootPathNamePtr, err := syscall.UTF16PtrFromString(rootPathName)
	if err != nil {
		return "", err
	}

	ret, _, _ := procGetVolumeInformation.Call(
		uintptr(unsafe.Pointer(rootPathNamePtr)),
		uintptr(unsafe.Pointer(&volumeNameBuffer[0])),
		uintptr(len(volumeNameBuffer)),
		uintptr(unsafe.Pointer(&volumeSerialNumber)),
		uintptr(unsafe.Pointer(&maximumComponentLength)),
		uintptr(unsafe.Pointer(&fileSystemFlags)),
		uintptr(unsafe.Pointer(&fileSystemNameBuffer[0])),
		uintptr(len(fileSystemNameBuffer)),
	)

	if ret == 0 {
		return "", fmt.Errorf("failed to get volume information")
	}

	return fmt.Sprintf("%X", volumeSerialNumber), nil
}

// GetMacAddr 获取MAC地址
func GetMacAddr() (string, error) {
	var adapterAddresses *IP_ADAPTER_ADDRESSES
	var bufferSize uint32 = 15000

	// 第一次调用获取所需缓冲区大小
	_, _, _ = procGetAdaptersAddresses.Call(
		uintptr(syscall.AF_UNSPEC),
		uintptr(0),
		uintptr(0),
		uintptr(unsafe.Pointer(adapterAddresses)),
		uintptr(unsafe.Pointer(&bufferSize)),
	)

	// 分配缓冲区
	adapterAddresses = (*IP_ADAPTER_ADDRESSES)(unsafe.Pointer(&make([]byte, bufferSize)[0]))

	// 第二次调用获取适配器信息
	ret, _, _ := procGetAdaptersAddresses.Call(
		uintptr(syscall.AF_UNSPEC),
		uintptr(0),
		uintptr(0),
		uintptr(unsafe.Pointer(adapterAddresses)),
		uintptr(unsafe.Pointer(&bufferSize)),
	)

	if ret != 0 {
		return "", fmt.Errorf("failed to get adapters addresses")
	}

	// 遍历适配器列表，获取第一个非空的MAC地址
	for adapter := adapterAddresses; adapter != nil; adapter = adapter.Next {
		if adapter.PhysicalAddressLength > 0 {
			mac := adapter.PhysicalAddress[:adapter.PhysicalAddressLength]
			return fmt.Sprintf("%02X:%02X:%02X:%02X:%02X:%02X", mac[0], mac[1], mac[2], mac[3], mac[4], mac[5]), nil
		}
	}

	return "", fmt.Errorf("no MAC address found")
}

/**
 * 获取mac地址
 *
 */
func getMacAddr(ethName string) string {
	mac, err := GetMacAddr()
	if err != nil {
		return ""
	}
	return mac
}

/**
 * 获取mac，硬盘序列号
 *
 */
func getMacAndDisk(ethName string, diskName string) (string, string) {
	mac := getMacAddr(ethName)
	disk, err := GetVolumeInformation(diskName)
	if err != nil {
		return mac, ""
	}
	return mac, disk
}

/*
 * 获取硬盘序列号
 */
func getDisk(diskName string, ethName string) string {
	disk, err := GetVolumeInformation(diskName)
	if err != nil {
		return ""
	}
	return disk
}
