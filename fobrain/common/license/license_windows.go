package license

import (
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"crypto/md5"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"github.com/spf13/cast"
	"io/ioutil"
	"os"
	"os/user"
	"path/filepath"
	"strings"
	"time"

	"fobrain/pkg/cfg"
	"fobrain/pkg/utils"
	"github.com/google/martian/log"
)

// 时间转换模板
var dateLayout = "2006-01-02"

// ActivationDateLayout 激活时间转换模版
var ActivationDateLayout = "2006-01-02 15:04:05"

// 时区设置
var dateLocation = "Asia/Shanghai"

// Expire 有效期数据
type expireData struct {
	ExpireDate        string
	ServerDate        string
	ActivationTime    string
	CurrentTime       string
	EncryptionNumber  string
	Mac               string
	DiskNum           string
	LicenseAssetCount string
	DataSources       string
	ProductModels     string
}

/**
 * 创建有效期文件
 *
 */
func CreateExpire(expireDate string, expirePath string, mac string, diskNum string, serverDate string, licenseAssetCount string, dataSources string, productModels string) {
	f, err := os.Create(expirePath)
	if err != nil {
		return
	}
	expire := expireData{}
	loc, _ := time.LoadLocation(dateLocation)
	expire.CurrentTime = time.Now().In(loc).Format(dateLayout)
	expire.EncryptionNumber = getEncryptionNumber(mac, diskNum, expireDate, serverDate)
	expire.ExpireDate = expireDate
	expire.ServerDate = serverDate
	expire.ActivationTime = ""
	expire.Mac = mac
	expire.DiskNum = diskNum
	expire.LicenseAssetCount = licenseAssetCount
	expire.DataSources = dataSources
	expire.ProductModels = productModels
	jsonString, _ := json.Marshal(expire)
	encryptCode := AesEncrypt(string(jsonString), encryptKey)
	f.WriteString(encryptCode)
}

/**
 * 获取软件编码
 *
 */
func getEncryptionNumber(mac string, diskNum string, expireDate string, serverDate string) string {
	h := md5.New()
	h.Write([]byte(mac + diskNum + encryptKey + expireDate + serverDate))
	md5Str := hex.EncodeToString(h.Sum(nil))
	content := md5Str[0:16]
	return strings.ToUpper(content)
}

/**
 * 激活成功
 *
 */
func activationSuccess(eth string, disk string, expirePath string, encryptKey string) int {
	Expire := readExpireData(expirePath, encryptKey)
	mac, diskNum := getMacAndDisk(eth, disk)

	if Expire.ActivationTime != "" {
		return 0
	}

	// MAC
	if Expire.Mac != mac {
		return 0
	}

	// 硬盘
	if Expire.DiskNum != diskNum {
		return 0
	}

	if Expire.ExpireDate == "" {
		return 0
	}

	if Expire.ServerDate == "" {
		return 0
	}

	if Expire.EncryptionNumber != getEncryptionNumber(mac, diskNum, Expire.ExpireDate, Expire.ServerDate) {
		return 0
	}

	loc, _ := time.LoadLocation(dateLocation)
	Expire.ActivationTime = time.Now().In(loc).Format(ActivationDateLayout)
	jsonString, _ := json.Marshal(Expire)
	encryptCode := AesEncrypt(string(jsonString), encryptKey)
	ioutil.WriteFile(expirePath, []byte(encryptCode), 0777)
	return 1
}

// GetProductModels 获取产品授权模块
func (l *License) GetProductModels() ([]string, error) {
	return strings.Split(getProductModels("", "", cfg.LoadCommon().LicensePath, encryptKey), ","), nil
}

// IsProductExpired 检测产品是否已过期
func (l *License) IsProductExpired() (bool, error) {
	return checkExpire("", "", cfg.LoadCommon().LicensePath, encryptKey) == 0, nil
}

// GetProductExpireDate 获取产品有效期
func (l *License) GetProductExpireDate() (string, error) {
	return getExpireDate("", "", cfg.LoadCommon().LicensePath, encryptKey), nil
}

// GetServerExpireDate 获取产品有效期
func (l *License) GetServerExpireDate() (string, error) {
	return getServerDate("", "", cfg.LoadCommon().LicensePath, encryptKey), nil
}

// GetDataSources 获取数据源
func (l *License) GetDataSources(devModel bool) ([]string, error) {
	if devModel {
		return []string{"all"}, nil
	}
	return strings.Split(getDataSources("", "", cfg.LoadCommon().LicensePath, encryptKey), ","), nil
}

// GetLicenseAssetCount 获取资产数量管理上限
func (l *License) GetLicenseAssetCount() (int, error) {
	if !l.HasLicenseFile() {
		return 0, nil
	}
	return cast.ToInt(getLicenseAssetCount("", "", cfg.LoadCommon().LicensePath, encryptKey)), nil
}

// IsServiceExpired 检测服务是否已过期
func (l *License) IsServiceExpired() (bool, error) {
	return checkServerTime("", "", cfg.LoadCommon().LicensePath, encryptKey) == 0, nil
}

// GetMacAddr 获取MAC地址
func (l *License) GetMacAddr() (string, error) {
	return getMacAddr(""), nil
}

// GetDiskSn 获取磁盘序列号
func (l *License) GetDiskSn() (string, error) {
	return getDisk("C:", ""), nil
}

// GetMacAndDisk 序列号信息 mac地址 ， 硬盘序列
func (l *License) GetMacAndDisk() (string, string, error) {
	mac, disk := getMacAndDisk("", "C:")
	return mac, disk, nil
}

// ClearTmpBinary 清除临时执行文件
func (l *License) ClearTmpBinary() {
	_ = os.Remove(l.tmpBinaryPath)
	l.tmpBinaryPath = ""
}

/**
 * 获取到期时间
 *
 */
func getExpireDate(eth string, disk string, expirePath string, encryptKey string) string {
	Expire := readExpireData(expirePath, encryptKey)
	mac, diskNum := getMacAndDisk(eth, disk)

	if Expire.ActivationTime == "" {
		return ""
	}
	// MAC
	if Expire.Mac != mac {
		return ""
	}

	// 硬盘
	if Expire.DiskNum != diskNum {
		return ""
	}

	if Expire.ExpireDate == "" {
		return ""
	}

	if Expire.ServerDate == "" {
		return ""
	}

	if Expire.EncryptionNumber != getEncryptionNumber(mac, diskNum, Expire.ExpireDate, Expire.ServerDate) {
		return ""
	}

	if Expire.ExpireDate == "0-0-0" {
		return "0-0-0"
	}
	return Expire.ExpireDate
}

/**
 * 获取服务到期时间
 *
 */
func getServerDate(eth string, disk string, expirePath string, encryptKey string) string {
	Expire := readExpireData(expirePath, encryptKey)
	mac, diskNum := getMacAndDisk(eth, disk)

	if Expire.ActivationTime == "" {
		return ""
	}
	// MAC
	if Expire.Mac != mac {
		return ""
	}

	// 硬盘
	if Expire.DiskNum != diskNum {
		return ""
	}

	if Expire.ExpireDate == "" {
		return ""
	}

	if Expire.ServerDate == "" {
		return ""
	}

	if Expire.EncryptionNumber != getEncryptionNumber(mac, diskNum, Expire.ExpireDate, Expire.ServerDate) {
		return ""
	}

	if Expire.ServerDate == "0-0-0" {
		return "0-0-0"
	}
	return Expire.ServerDate
}

/**
 * 获取资产数量管理上限
 *
 */
func getLicenseAssetCount(eth string, disk string, expirePath string, encryptKey string) string {
	Expire := readExpireData(expirePath, encryptKey)
	mac, diskNum := getMacAndDisk(eth, disk)

	if Expire.ActivationTime == "" {
		return ""
	}
	// MAC
	if Expire.Mac != mac {
		return ""
	}

	// 硬盘
	if Expire.DiskNum != diskNum {
		return ""
	}

	if Expire.ExpireDate == "" {
		return ""
	}

	if Expire.ServerDate == "" {
		return ""
	}

	if Expire.EncryptionNumber != getEncryptionNumber(mac, diskNum, Expire.ExpireDate, Expire.ServerDate) {
		return ""
	}

	return Expire.LicenseAssetCount
}

/**
 * 获取数据源
 *
 */
func getDataSources(eth string, disk string, expirePath string, encryptKey string) string {
	Expire := readExpireData(expirePath, encryptKey)
	mac, diskNum := getMacAndDisk(eth, disk)

	if Expire.ActivationTime == "" {
		return ""
	}

	// MAC
	if Expire.Mac != mac {
		return ""
	}

	// 硬盘
	if Expire.DiskNum != diskNum {
		return ""
	}

	if Expire.ExpireDate == "" {
		return ""
	}

	if Expire.ServerDate == "" {
		return ""
	}

	if Expire.EncryptionNumber != getEncryptionNumber(mac, diskNum, Expire.ExpireDate, Expire.ServerDate) {
		return ""
	}

	return Expire.DataSources
}

/**
 * 获取 产品模块
 *
 */
func getProductModels(eth string, disk string, expirePath string, encryptKey string) string {
	Expire := readExpireData(expirePath, encryptKey)
	mac, diskNum := getMacAndDisk(eth, disk)

	if Expire.ActivationTime == "" {
		return ""
	}
	// MAC
	if Expire.Mac != mac {
		return ""
	}
	// 硬盘
	if Expire.DiskNum != diskNum {
		return ""
	}
	if Expire.ExpireDate == "" {
		return ""
	}
	if Expire.ServerDate == "" {
		return ""
	}
	if Expire.EncryptionNumber != getEncryptionNumber(mac, diskNum, Expire.ExpireDate, Expire.ServerDate) {
		return ""
	}
	return Expire.ProductModels
}

/*
验证 license 码
*/
func checkCode(eth string, disk string, code string, encryptKey string) int {

	if code == "" {
		return 0
	}
	var Expire expireData

	decoded := AesDecrypt(code, encryptKey)

	jsonerr := json.Unmarshal([]byte(string(decoded)), &Expire)
	if jsonerr != nil {
		return 0
	}
	mac, diskNum := getMacAndDisk(eth, disk)
	// MAC
	if Expire.Mac != mac {
		return 0
	}

	// 硬盘
	if Expire.DiskNum != diskNum {
		return 0
	}

	if Expire.ExpireDate == "" {
		return 0
	}

	if Expire.ServerDate == "" {
		return 0
	}

	if Expire.CurrentTime == "" {
		return 0
	}

	if Expire.EncryptionNumber == "" {
		return 0
	}

	if Expire.EncryptionNumber != getEncryptionNumber(mac, diskNum, Expire.ExpireDate, Expire.ServerDate) {
		return 0
	}

	return 1
}

/**
* 验证是否过期
*
 */
func checkExpire(eth string, disk string, expirePath string, encryptKey string) int {
	Expire := readExpireData(expirePath, encryptKey)
	mac, diskNum := getMacAndDisk(eth, disk)
	//没有激活时间
	if Expire.ActivationTime == "" {
		return 0
	}

	// MAC
	if Expire.Mac != mac {
		return 0
	}

	// 硬盘
	if Expire.DiskNum != diskNum {
		return 0
	}

	if Expire.EncryptionNumber != getEncryptionNumber(mac, diskNum, Expire.ExpireDate, Expire.ServerDate) {
		return 0
	}

	//无限期
	if Expire.ExpireDate == "0-0-0" {
		return 1
	}

	loc, _ := time.LoadLocation(dateLocation)
	//activationTime, _ := time.ParseInLocation(ActivationDateLayout, Expire.ActivationTime, loc)
	expireTime, _ := time.Parse(dateLayout, Expire.ExpireDate)
	nowTime := time.Now().In(loc).Unix()
	if nowTime > expireTime.Unix() {
		return 0
	}
	FileChangeTime := getFileChangeTime(expirePath)
	//if activationTime.Unix() != FileChangeTime {
	//	return 0
	//}
	if FileChangeTime > nowTime {
		return 0
	}
	return 1
}

/**
* 验证是否服务时间过期
*
 */
func checkServerTime(eth string, disk string, expirePath string, encryptKey string) int {
	Expire := readExpireData(expirePath, encryptKey)
	mac, diskNum := getMacAndDisk(eth, disk)
	//没有激活时间
	if Expire.ActivationTime == "" {
		return 0
	}

	// MAC
	if Expire.Mac != mac {
		return 0
	}

	// 硬盘
	if Expire.DiskNum != diskNum {
		return 0
	}

	if Expire.EncryptionNumber != getEncryptionNumber(mac, diskNum, Expire.ExpireDate, Expire.ServerDate) {
		return 0
	}

	//无限期
	if Expire.ServerDate == "0-0-0" {
		return 1
	}

	loc, _ := time.LoadLocation(dateLocation)
	//activationTime, _ := time.ParseInLocation(ActivationDateLayout, Expire.ActivationTime, loc)
	serverTime, _ := time.Parse(dateLayout, Expire.ServerDate)
	nowTime := time.Now().In(loc).Unix()
	if nowTime > serverTime.Unix() {
		return 0
	}
	FileChangeTime := getFileChangeTime(expirePath)
	//if activationTime.Unix() != FileChangeTime {
	//	return 0
	//}
	if FileChangeTime > nowTime {
		return 0
	}
	return 1
}

/**
* 获取文件创建时间
*
 */
func getFileChangeTime(filepath string) int64 {
	f, err := os.Open(filepath)
	if err != nil {
		return 0
	}
	defer f.Close()

	fi, errs := f.Stat()
	if errs != nil {
		return 0
	}
	loc, _ := time.LoadLocation(dateLocation)
	return fi.ModTime().In(loc).Unix()
}

/**
 * 读取文件内容
 *
 */
func readExpireData(expirePath string, encryptKey string) expireData {
	var Expire expireData
	data, err := ioutil.ReadFile(expirePath)
	if err != nil {
		return Expire
	}
	decoded := AesDecrypt(string(data), encryptKey)

	jsonerr := json.Unmarshal([]byte(string(decoded)), &Expire)

	if jsonerr != nil {
		return Expire
	}
	return Expire
}

/**
 * 加密
 *
 */
func AesEncrypt(orig string, key string) string {
	// 转成字节数组
	origData := []byte(orig)
	k := []byte(key)
	// 分组秘钥
	// NewCipher该函数限制了输入k的长度必须为16, 24或者32
	block, _ := aes.NewCipher(k)
	// 获取秘钥块的长度
	blockSize := block.BlockSize()
	// 补全码
	origData = PKCS7Padding(origData, blockSize)
	// 加密模式
	blockMode := cipher.NewCBCEncrypter(block, k[:blockSize])
	// 创建数组
	cryted := make([]byte, len(origData))
	// 加密
	blockMode.CryptBlocks(cryted, origData)
	return base64.StdEncoding.EncodeToString(cryted)
}

// AesDecrypt 解密
func AesDecrypt(cryted string, key string) string {
	// 转成字节数组
	crytedByte, _ := base64.StdEncoding.DecodeString(cryted)
	k := []byte(key)
	// 分组秘钥
	block, _ := aes.NewCipher(k)
	// 获取秘钥块的长度
	blockSize := block.BlockSize()
	// 加密模式
	blockMode := cipher.NewCBCDecrypter(block, k[:blockSize])
	// 创建数组
	orig := make([]byte, len(crytedByte))
	// 解密
	blockMode.CryptBlocks(orig, crytedByte)
	// 去补全码
	orig = PKCS7UnPadding(orig)
	return string(orig)
}

// PKCS7Padding /**
func PKCS7Padding(ciphertext []byte, blocksize int) []byte {
	padding := blocksize - len(ciphertext)%blocksize
	padtext := bytes.Repeat([]byte{byte(padding)}, padding)
	return append(ciphertext, padtext...)
}

// PKCS7UnPadding /**
func PKCS7UnPadding(origData []byte) []byte {
	if len(origData) == 0 {
		return origData
	}
	length := len(origData)
	unpadding := int(origData[length-1])
	return origData[:(length - unpadding)]
}

func (l *License) CheckExpireCode(code string) (bool, error) {
	// 实现检查激活码是否有效的逻辑
	return checkCode("", "", code, encryptKey) == 1, nil
}

func (l *License) Activation(code string) (bool, error) {
	defer func() {
		if err := recover(); err != nil {
			log.Infof("Activation panic: %v\n", err)
		}
	}()

	expireCode, err := l.CheckExpireCode(code)
	if err != nil {
		return false, err
	}
	if !expireCode {
		return false, fmt.Errorf("激活码无效")
	}

	// 获取当前用户
	currentUser, err := user.Current()
	if err != nil {
		return false, fmt.Errorf("获取当前用户失败: %v", err)
	}

	// 获取主目录
	homeDir := currentUser.HomeDir
	cfg.GetInstance().Common.LicensePath = filepath.Join(homeDir, ".fobrain.license")

	err = utils.CreateFileIfNotExist(cfg.LoadCommon().LicensePath)
	if err != nil {
		return false, err
	}
	if err = os.WriteFile(cfg.LoadCommon().LicensePath, []byte(code), 0644); err != nil {
		return false, err
	}
	return activationSuccess("", "", cfg.LoadCommon().LicensePath, encryptKey) == 1, nil
}
