package license

import (
	"fmt"
	"github.com/google/martian/log"
	"os"
	"os/user"
	"path/filepath"
	"strings"

	"fobrain/pkg/cfg"

	"github.com/spf13/cast"

	"fobrain/pkg/utils"
)

// Activation 激活
func (l *License) Activation(code string) (bool, error) {
	defer func() {
		if err := recover(); err != nil {
			log.Infof("Activation panic: %v\n", err)
		}
	}()

	expireCode, err := l.CheckExpireCode(code)
	if err != nil {
		return false, err
	}
	if !expireCode {
		return false, fmt.Errorf("激活码无效")
	}

	// 获取当前用户
	currentUser, err := user.Current()
	if err != nil {
		return false, fmt.Errorf("获取当前用户失败: %v", err)
	}

	// 获取主目录
	homeDir := currentUser.HomeDir
	cfg.GetInstance().Common.LicensePath = filepath.Join(homeDir, ".fobrain.license")

	err = utils.CreateFileIfNotExist(cfg.LoadCommon().LicensePath)
	if err != nil {
		return false, err
	}
	if err = os.WriteFile(cfg.LoadCommon().LicensePath, []byte(code), 0644); err != nil {
		return false, err
	}
	return activationSuccess("", "", cfg.LoadCommon().LicensePath, encryptKey) == 1, nil
}

// IsProductExpired 检测产品是否已过期
func (l *License) IsProductExpired() (bool, error) {
	return checkExpire("", "", cfg.LoadCommon().LicensePath, encryptKey) == 0, nil
}

// IsServiceExpired 检测服务是否已过期
func (l *License) IsServiceExpired() (bool, error) {
	return checkServerTime("", "", cfg.LoadCommon().LicensePath, encryptKey) == 0, nil
}

// GetProductModels 获取产品授权模块
func (l *License) GetProductModels() ([]string, error) {
	return strings.Split(getProductModels("", "", cfg.LoadCommon().LicensePath, encryptKey), ","), nil
}

// GetDataSources 获取数据源
func (l *License) GetDataSources(devModel bool) ([]string, error) {
	if devModel {
		return []string{"all"}, nil
	}
	return strings.Split(getDataSources("", "", cfg.LoadCommon().LicensePath, encryptKey), ","), nil
}

// CheckExpireCode 检测延期码
func (l *License) CheckExpireCode(code string) (bool, error) {
	return checkCode("", "", code, encryptKey) == 1, nil
}

// GetProductExpireDate 获取产品有效期
func (l *License) GetProductExpireDate() (string, error) {
	return getExpireDate("", "", cfg.LoadCommon().LicensePath, encryptKey), nil
}

// GetServerExpireDate 获取产品有效期
func (l *License) GetServerExpireDate() (string, error) {
	return getServerDate("", "", cfg.LoadCommon().LicensePath, encryptKey), nil
}

// GetLicenseAssetCount 获取资产数量管理上限
func (l *License) GetLicenseAssetCount() (int, error) {
	if !l.HasLicenseFile() {
		return 0, nil
	}
	return cast.ToInt(getLicenseAssetCount("", "", cfg.LoadCommon().LicensePath, encryptKey)), nil
}

// GetMacAddr 获取MAC地址
func (l *License) GetMacAddr() (string, error) {
	return getMacAddr(""), nil
}

// GetDiskSn 获取磁盘序列号
func (l *License) GetDiskSn() (string, error) {
	return getDisk("", ""), nil
}

// GetMacAndDisk 序列号信息 mac地址 ， 硬盘序列
func (l *License) GetMacAndDisk() (string, string, error) {
	a, b := getMacAndDisk("", "")
	return a, b, nil
}

// ClearTmpBinary 清除临时执行文件
func (l *License) ClearTmpBinary() {
	_ = os.Remove(l.tmpBinaryPath)
	l.tmpBinaryPath = ""
}
