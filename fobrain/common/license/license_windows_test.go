package license

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"fobrain/fobrain/config"
)

func TestActivation(t *testing.T) {
	_ = config.LoadConfigByFile("../../.env")
	_, err := GetLicense().Activation("123456")
	if err != nil {
		//assert.Equal(t, err.Error(), "platform not supported")
	}
}

func TestIsServiceExpired(t *testing.T) {
	_ = config.LoadConfigByFile("../../.env")
	_, err := GetLicense().IsServiceExpired()
	if err != nil {
		//	assert.Equal(t, err.Error(), "platform not supported")
	}
}

func TestIsProductExpired(t *testing.T) {
	_ = config.LoadConfigByFile("../../.env")
	_, err := GetLicense().IsProductExpired()
	if err != nil {
		//	assert.Equal(t, err.Error(), "platform not supported")
	}
}

func TestGetProductModels(t *testing.T) {
	_ = config.LoadConfigByFile("../../.env")
	_, err := GetLicense().GetProductModels()
	if err != nil {
		//assert.Equal(t, err.Error(), "platform not supported")
	}
}

func TestGetDataSources(t *testing.T) {
	_ = config.LoadConfigByFile("../../.env")
	_, err := GetLicense().GetDataSources(true)
	if err != nil {
		//assert.Equal(t, err.Error(), "platform not supported")
	}
}

func TestGetProductExpireDate(t *testing.T) {
	_ = config.LoadConfigByFile("../../.env")
	_, err := GetLicense().GetProductExpireDate()
	if err != nil {
		//assert.Equal(t, err.Error(), "platform not supported")
	}
}

func TestGetServerExpireDate(t *testing.T) {
	_ = config.LoadConfigByFile("../../.env")
	_, err := GetLicense().GetServerExpireDate()
	if err != nil {
		//assert.Equal(t, err.Error(), "platform not supported")
	}
}

func TestGetLicenseAssetCount(t *testing.T) {
	_ = config.LoadConfigByFile("../../.env")
	_, err := GetLicense().GetLicenseAssetCount()
	if err != nil {
		//assert.Equal(t, err.Error(), "platform not supported")
	}
}

func TestGetMacAddr(t *testing.T) {
	_ = config.LoadConfigByFile("../../.env")
	_, err := GetLicense().GetMacAddr()
	if err != nil {
		assert.Equal(t, err.Error(), "platform not supported")
	}
}

func TestGetDiskSn(t *testing.T) {
	_ = config.LoadConfigByFile("../../.env")
	_, err := GetLicense().GetDiskSn()
	if err != nil {
		assert.Equal(t, err.Error(), "platform not supported")
	}
}

func TestGetMacAndDisk(t *testing.T) {
	_ = config.LoadConfigByFile("../../.env")
	_, _, err := GetLicense().GetMacAndDisk()
	if err != nil {
		assert.Equal(t, err.Error(), "platform not supported")
	}
}

func TestCheckExpireCode(t *testing.T) {
	_ = config.LoadConfigByFile("../../.env")
	_, err := GetLicense().CheckExpireCode("123456")
	if err != nil {
		//assert.Equal(t, err.Error(), "platform not supported")
	}
}

func TestClearTmpBinary(t *testing.T) {
	_ = config.LoadConfigByFile("../../.env")
	GetLicense().ClearTmpBinary()
}
