package auth

import (
	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/models/mysql/role"
	"github.com/DATA-DOG/go-sqlmock"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestRbacService_GetRoleMenus(t *testing.T) {
	mockDb := testcommon.InitSqlMock()
	defer mockDb.Close()

	mockDb.ExpectQuery("SELECT * FROM `roles` ORDER BY `roles`.`id` LIMIT 1").
		WillReturnRows(sqlmock.NewRows([]string{"id", "name", "key", "status", "remark"}).AddRow(1, "admin", "admin", 1, "超级管理员"))

	mockDb.ExpectQuery("SELECT * FROM `menus` WHERE `id` IN (SELECT `menu_id` FROM `roles_menus` WHERE `role_id` = ?)").
		WithArgs(1).
		WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).AddRow(1, "Menu 1"))

	role, err := role.NewRoleModel().First()
	if err != nil {
		t.Error(err)
	}
	_, err = GetRbacService().GetRoleMenus(role.Id)
	if err != nil {
		t.Error(err)
	}
}
func TestRbacService_GetUserMenus(t *testing.T) {
	mockDb := testcommon.InitSqlMock()
	defer mockDb.Close()

	mockDb.ExpectQuery("SELECT * FROM `menus` WHERE `id` IN (SELECT `menu_id` FROM `roles_menus` WHERE `role_id` IN (SELECT `user_id` FROM `users_roles` WHERE `user_id`=?))").
		WithArgs(1).
		WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).AddRow(1, "Menu 1"))

	_, err := GetRbacService().GetUserMenus(1)
	if err != nil {
		t.Error(err)
	}
}
func TestRbacService_GetUserRoles(t *testing.T) {
	mockDb := testcommon.InitSqlMock()
	defer mockDb.Close()

	mockDb.ExpectQuery("SELECT * FROM `roles` WHERE `id` in (SELECT `role_id` FROM `users_roles` WHERE `user_id`=?)").
		WithArgs(1).
		WillReturnRows(sqlmock.NewRows([]string{"id", "name", "key", "status", "remark"}).AddRow(1, "admin", "admin", 1, "超级管理员"))

	_, err := GetRbacService().GetUserRoles(1)
	if err != nil {
		t.Error(err)
	}
}
func TestRbacService_IsSuperAdmin(t *testing.T) {
	mockDb := testcommon.InitSqlMock()
	defer mockDb.Close()

	mockDb.ExpectQuery("SELECT * FROM `roles` WHERE `id` in (SELECT `role_id` FROM `users_roles` WHERE `user_id`=?)").
		WithArgs(1).
		WillReturnRows(sqlmock.NewRows([]string{"id", "name", "key", "status", "remark"}).AddRow(1, "admin", "admin", 1, "超级管理员"))

	ok := GetRbacService().IsSuperAdmin(1)
	assert.Equal(t, true, ok)
}
