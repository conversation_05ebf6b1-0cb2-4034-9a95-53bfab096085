package auth

import (
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func TestIsSuperManage(t *testing.T) {
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/internal_asset?page=1&per_page=10", nil)
	// 创建一个Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Set("staff_ids", []string{})
	c.Set("is_super_manage", true)
	c.Request = req

	isSuper := IsSuperManage(c)
	assert.Equal(t, isSuper, true)
}

func TestCurrentStaffIds(t *testing.T) {
	t.Run("exist", func(t *testing.T) {
		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/v1/internal_asset?page=1&per_page=10", nil)
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Set("staff_ids", []string{"1", "2"})
		c.Set("is_super_manage", true)
		c.Request = req

		staffIds := CurrentStaffIds(c)
		assert.Equal(t, staffIds, []string{"1", "2"})
	})

	t.Run("not exist", func(t *testing.T) {
		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/v1/internal_asset?page=1&per_page=10", nil)
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		staffIds := CurrentStaffIds(c)
		assert.Equal(t, staffIds, []string{})
	})
}
