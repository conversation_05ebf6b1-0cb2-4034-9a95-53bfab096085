package auth

import (
	_ "embed"
	"fobrain/models/mysql/menu"
	"fobrain/models/mysql/role"
	"fobrain/models/mysql/user"
	"sync"
)

var (
	once           sync.Once
	singleInstance *RbacService
)

type RbacService struct {
}

func GetRbacService() *RbacService {
	once.Do(func() {
		if singleInstance == nil {
			singleInstance = &RbacService{}
		}
	})
	return singleInstance
}

// GetUserMenus 获取用户所有菜单
func (r *RbacService) GetUserMenus(userId uint64) ([]menu.Menu, error) {
	return menu.NewMenuModel().GetUserMenus(userId)
}

// GetRoleMenus 获取角色所有菜单权限
func (r *RbacService) GetRoleMenus(roleId uint64) ([]menu.Menu, error) {
	return menu.NewMenuModel().GetRoleMenus(roleId)
}

// GetUserRoles 获取用户所有角色
func (r *RbacService) GetUserRoles(userId uint64) ([]role.Role, error) {
	return role.NewRoleModel().GetUserRoles(userId)
}

// IsSuperAdmin 是否是超管用户
func (r *RbacService) IsSuperAdmin(userId uint64) bool {
	return user.NewUserModel().IsSuperAdmin(userId)
}

// CheckAuth 检查用户权限
func (r *RbacService) CheckAuth(userId uint64, url, method string) (bool, error) {
	if user.NewUserModel().IsSuperAdmin(userId) {
		return true, nil
	}
	return menu.NewMenuModel().CheckUserHaveMenu(userId, url, method)
}

func (r *RbacService) GetUserInfo(userId uint64) (*user.User, error) {
	return user.NewUserModel().GetUserInfo(userId)
}
