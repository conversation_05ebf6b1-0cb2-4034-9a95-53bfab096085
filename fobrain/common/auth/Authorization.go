package auth

import (
	"errors"
	license2 "fobrain/fobrain/app/repository/settings/license"
	"github.com/gin-gonic/gin"
	"time"

	"fobrain/fobrain/common/localtime"
	"fobrain/initialize/mysql"
	"fobrain/models/mysql/token"
	"fobrain/models/mysql/user"
	"fobrain/pkg/cfg"
	"fobrain/pkg/utils"

	"github.com/golang-jwt/jwt/v5"
	"github.com/spf13/cast"
)

type Claims struct {
	jwt.RegisteredClaims // 标准Claims结构体，可设置8个标准字段
	User                 *user.User
}

// GenerateToken 登录成功后调用，传入UserInfo结构体
func GenerateToken(userInfo user.User) (string, *time.Time, error) {
	// token 过期时间写死 15 分钟过期
	expiredAt := time.Now().Add(15 * time.Minute)
	isDev := license2.CheckDevModel(cfg.LoadCommon().Env)
	// 本地开发时，token 1天过期
	if cfg.LoadCommon().Local && isDev {
		expiredAt = time.Now().Add(time.Hour * 24)
	}
	tokenStr := utils.GenerateRandomString(20) + utils.Get32MD5Encode(cfg.LoadJwt().Key+cast.ToString(time.Now().UnixMicro()))
	err := token.NewTokenModel().Create(&token.Token{
		UserId:    userInfo.Id,
		ExpiredAt: localtime.Time(expiredAt),
		Type:      token.TokenTypeNormal,
		Token:     tokenStr,
	})
	if err != nil {
		return "", nil, err
	}
	return tokenStr, &expiredAt, nil
}

// GenerateApiToken 传入UserInfo结构体
func GenerateApiToken(userInfo *user.User, ref bool) (string, *localtime.Time, error) {
	// 不刷新时查询,旧Token
	if !ref {
		tokenInfo, err := token.NewTokenModel().First(
			mysql.WithWhere("user_id", userInfo.Id),
			mysql.WithWhere("type", token.TokenTypeApi),
		)
		if err != nil && !mysql.IsNotFound(err) {
			return "", nil, err
		}
		if !mysql.IsNotFound(err) {
			return tokenInfo.Token, &tokenInfo.ExpiredAt, nil
		}
	}
	// 删除旧Token
	if err := token.NewTokenModel().Del(mysql.WithWhere("user_id", userInfo.Id), mysql.WithWhere("type", token.TokenTypeApi)); err != nil {
		return "", nil, err
	}

	// 生成新的Token
	expiredAt := localtime.Time(time.Now().AddDate(999, 0, 0))

	tokenStr := utils.GenerateRandomString(20) + utils.Get32MD5Encode(cfg.LoadJwt().Key+cast.ToString(time.Now().UnixMicro()))
	err := token.NewTokenModel().Create(&token.Token{
		UserId: userInfo.Id, ExpiredAt: expiredAt,
		Type: token.TokenTypeApi, Token: tokenStr,
	})

	if err != nil {
		return "", nil, err
	}
	return tokenStr, &expiredAt, nil
}

func CheckToken(tokenString string) (uint64, int, error) {
	if tokenString == "" {
		return 0, 0, errors.New("unauthorized")
	}
	// 查找Token
	tokenInfo, err := token.NewTokenModel().First(mysql.WithWhere("token = ?", tokenString))
	if err != nil {
		if mysql.IsNotFound(err) {
			return 0, 0, errors.New("unauthorized")
		} else {
			return 0, 0, err
		}
	}
	// 检查Token有效期
	if time.Now().After(time.Time(tokenInfo.ExpiredAt)) {
		return 0, 0, errors.New("unauthorized")
	}
	// 普通token时,更新Token有效期
	if tokenInfo.Type == token.TokenTypeNormal {
		// token 过期时间写死 15 分钟过期
		expiredAt := time.Now().Add(15 * time.Minute)
		isDev := license2.CheckDevModel(cfg.LoadCommon().Env)
		// 本地开发时，token 1天过期
		if cfg.LoadCommon().Local && isDev {
			expiredAt = time.Now().Add(time.Hour * 24)
		}
		_ = token.NewTokenModel().Where("id", tokenInfo.Id).UpdateColumn("expired_at", expiredAt).Error
	}
	return tokenInfo.UserId, tokenInfo.Type, nil
}

// IsSuperManage
// @Summary 供接口层判断当前登陆用户是否是管理员
func IsSuperManage(ctx *gin.Context) bool {
	isSuper, ok := ctx.Get("is_super_manage")
	if ok {
		return isSuper.(bool)
	}
	return false
}

func CurrentStaffIds(ctx *gin.Context) []string {
	staffIds, ok := ctx.Get("staff_ids")
	if !ok {
		return []string{}
	}

	return staffIds.([]string)
}
