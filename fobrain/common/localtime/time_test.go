package localtime

import (
	"errors"
	"testing"
	"time"

	"github.com/agiledragon/gomonkey/v2"
	. "github.com/smartystreets/goconvey/convey"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
)

type TimeSuite struct {
	suite.Suite
}

func TestTimeSuite(t *testing.T) {
	s := &TimeSuite{}
	suite.Run(t, s)
}

func (s *TimeSuite) Test_common_MarshalJSONFunc() {
	<PERSON><PERSON>("Test_common_MarshalJSONFunc", s.T(), func() {
		<PERSON><PERSON>("MarshalJSONFunc PASS", func() {

			_, err := NewLocalTime(time.Now()).MarshalJSON()
			So(err, ShouldBeNil)
		})
	})
}

func (s *TimeSuite) Test_common_UnmarshalJSONFunc() {
	<PERSON>vey("Test_common_UnmarshalJSONFunc", s.T(), func() {
		<PERSON><PERSON>("MarshalJSONFunc PASS", func() {

			data, err := NewLocalTime(time.Now()).MarshalJSON()
			So(err, ShouldBeNil)

			err = NewLocalTime(time.Now()).UnmarshalJSON(data)
			So(err, ShouldBeNil)
		})

		Convey("MarshalJSONFunc Error", func() {

			data, err := NewLocalTime(time.Now()).MarshalJSON()
			So(err, ShouldBeNil)

			time.Sleep(time.Second)
			defer gomonkey.ApplyFuncReturn(time.ParseInLocation, nil, errors.New("ParseInLocation Error")).Reset()

			err = NewLocalTime(time.Now()).UnmarshalJSON(data)
			So(err, ShouldBeError)
		})
	})
}

func (s *TimeSuite) Test_common_ValueFunc() {
	Convey("Test_common_ValueFunc", s.T(), func() {
		Convey("ValueFunc PASS", func() {
			_, err := NewLocalTime(time.Now()).Value()
			So(err, ShouldBeNil)
		})
	})
}

func (s *TimeSuite) Test_common_ScanFunc() {
	Convey("Test_common_ScanFunc", s.T(), func() {
		Convey("ValueFunc PASS", func() {
			err := NewLocalTime(time.Now()).Scan(time.Now())
			So(err, ShouldBeNil)
		})

		Convey("ValueFunc ERROR", func() {
			err := NewLocalTime(time.Now()).Scan(23)
			So(err, ShouldBeError)
		})
	})
}

func TestUnix(t *testing.T) {
	tests := []struct {
		name     string
		input    Time
		expected int64
	}{

		{
			name:     "当前时间",
			input:    Time(time.Now()),  // 当前时间
			expected: time.Now().Unix(), // 期望值为当前时间的 Unix 时间戳
		},
		{
			name:     "特定时间",
			input:    Time(time.Date(2024, 11, 16, 13, 47, 43, 0, time.UTC)), // 特定时间
			expected: time.Date(2024, 11, 16, 13, 47, 43, 0, time.UTC).Unix(),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.input.Unix()
			if result != tt.expected {
				t.Errorf("期望 %v, 但得到 %v", tt.expected, result)
			}
		})
	}
}

func TestMarshalJSON(t *testing.T) {
	tests := []struct {
		name     string
		time     Time
		expected string
	}{
		{
			name:     "Zero time",
			time:     Time(time.Time{}),
			expected: `""`,
		},
		{
			name:     "Non-zero time",
			time:     Time(time.Date(2023, 10, 1, 12, 0, 0, 0, time.UTC)),
			expected: `"2023-10-01 12:00:00"`,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := tt.time.MarshalJSON()
			assert.NoError(t, err)
			assert.Equal(t, tt.expected, string(result))
		})
	}
}

func TestUnmarshalJSON(t *testing.T) {
	tests := []struct {
		name     string
		data     string
		expected Time
	}{
		{
			name:     "Empty string",
			data:     `""`,
			expected: Time(time.Time{}),
		},
		{
			name:     "Valid time string",
			data:     `"2023-10-01 12:00:00"`,
			expected: Time(time.Date(2023, 10, 1, 12, 0, 0, 0, time.Local)),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var result Time
			err := result.UnmarshalJSON([]byte(tt.data))
			assert.NoError(t, err)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestTime_Sub(t *testing.T) {
	t1 := Time(time.Date(2024, 11, 16, 13, 47, 43, 0, time.UTC))
	t2 := Time(time.Date(2024, 12, 16, 13, 47, 43, 0, time.UTC))
	duration := t2.Sub(&t1)
	assert.Equal(t, duration.String(), "720h0m0s")
	duration = t1.Sub(&t2)
	assert.Equal(t, duration.String(), "-720h0m0s")
}
