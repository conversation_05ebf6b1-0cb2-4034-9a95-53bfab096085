package localtime

import (
	"database/sql/driver"
	"fmt"
	"time"
)

// Time 全局定义
type Time time.Time

const timeFormat = "2006-01-02 15:04:05"
const DateFormat = "2006-01-02"
const TimeFormat = "2006-01-02 15:04:05"
const TimeNotSecondFormat = "2006-01-02 15:04"
const DateTimeLayout = "20060102150405"
const YearFormat = "2006"

// MarshalJSON json
func (t Time) MarshalJSON() ([]byte, error) {
	lt := time.Time(t)
	if lt.Is<PERSON>ero() {
		return []byte(`""`), nil // 返回空字符串
	}
	b := make([]byte, 0, len(timeFormat)+2)
	b = append(b, '"')
	b = time.Time(t).AppendFormat(b, timeFormat)
	b = append(b, '"')
	return b, nil
}

// UnmarshalJSON 解析json
func (t *Time) UnmarshalJSON(data []byte) (err error) {
	if string(data) == `""` {
		*t = Time(time.Time{})
		return nil
	}
	now, err := time.ParseInLocation(`"`+timeFormat+`"`, string(data), time.Local)
	*t = Time(now)
	return
}

func (t Time) After(t2 Time) bool {
	return time.Time(t).After(time.Time(t2))
}

func (t *Time) Sub(t2 *Time) time.Duration {
	if t == nil || t2 == nil {
		return 0
	}
	return time.Time(*t).Sub(time.Time(*t2))
}

// String 字符串
func (t *Time) String() string {
	if t == nil {
		return ""
	}
	return time.Time(*t).Format(timeFormat)
}

func (t *Time) Format(layout string) string {
	if t == nil {
		return ""
	}
	return time.Time(*t).Format(layout)
}

func (t *Time) Time() time.Time {
	if t == nil {
		return time.Time{}
	}
	return t.local()
}

// DateString 字符串
func (t *Time) DateString() string {
	if t == nil {
		return ""
	}
	return time.Time(*t).Format(DateFormat)
}

// local 获取时间
func (t Time) local() time.Time {
	return time.Time(t)
}

func NewLocalTime(t time.Time) *Time {
	ts := Time(t)
	return &ts
}

func Now() *Time {
	return NewLocalTime(time.Now())
}

// Value 获取时间
func (t Time) Value() (driver.Value, error) {
	var zeroTime time.Time
	var ti = time.Time(t)
	if ti.UnixNano() == zeroTime.UnixNano() {
		return nil, nil
	}
	return ti, nil
}

// Scan 获取时间
func (t *Time) Scan(v interface{}) error {
	switch v.(type) {
	case time.Time:
		*t = Time(v.(time.Time))
		return nil
	case string:
		*t = Parse(TimeFormat, v.(string))
		return nil
	case []byte:
		*t = Parse(TimeFormat, string(v.([]byte)))
		return nil
	default:
		return fmt.Errorf("can not convert %v to timestamp", v)
	}
}

func Parse(layout, value string) Time {
	t, err := time.Parse(layout, value)
	if err != nil {
		return Time{}
	}
	return Time(t)
}

func (t Time) Unix() int64 {
	return time.Time(t).Unix()
}
