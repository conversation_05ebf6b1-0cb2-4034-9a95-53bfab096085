package middleware

import (
	"github.com/gin-gonic/gin"
	"net/http"
	"time"
)

func NoCache(c *gin.Context) {
	c.<PERSON><PERSON>("Cache-Control", "no-cache, no-store, max-age=0, must-revalidate, value")
	c.<PERSON><PERSON>("Expires", "Thu, 01 Jan 1970 00:00:00 GMT")
	c.<PERSON>("Last-Modified", time.Now().UTC().Format(http.TimeFormat))
	c.Next()
}

func Options(c *gin.Context) {
	if c.Request.Method != "OPTIONS" {
		c.Next()
	} else {
		c.<PERSON><PERSON>("Access-Control-Allow-Origin", "*")
		c.<PERSON>("Access-Control-Allow-Methods", "GET,POST,PUT,PATCH,DELETE,OPTIONS")
		c.<PERSON>("Access-Control-Allow-Headers", "Authorization, Range, Origin, Content-Type, Accept, Content-Length, Content-Type")
		c.<PERSON><PERSON>("Allow", "HEAD,GET,POST,PUT,PATCH,DELETE,OPTIONS")
		c.<PERSON>("Content-Type", "application/json")
		c.AbortWithStatus(200)
	}
}
