package middleware

import (
	"fmt"
	"fobrain/fobrain/app/controller/system_configs/backup"
	backupService "fobrain/fobrain/app/services/backup"
	"path/filepath"
	"slices"
	"time"

	"fobrain/fobrain/routes/third_party"
	"fobrain/pkg/utils"

	"github.com/didip/tollbooth"
	"github.com/didip/tollbooth/limiter"
	"github.com/gin-contrib/gzip"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"

	"fobrain/fobrain/app/controller/system_configs/upgrade"
	"fobrain/fobrain/app/crontab"
	license2 "fobrain/fobrain/app/repository/settings/license"
	cascade_service "fobrain/fobrain/app/services/cascade"
	"fobrain/fobrain/common/license"
	"fobrain/fobrain/common/response"
	"fobrain/fobrain/common/wsocket"
	"fobrain/fobrain/logs"
	"fobrain/fobrain/routes"
	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/initialize/mysql"
	"fobrain/models/mysql/system_configs"
	"fobrain/module/cascade/cascade"
	node_model "fobrain/module/cascade/models/data_capture_nodes"
	"fobrain/module/cascade/sdk"
	"fobrain/module/cascade/service/data_capture_nodes"
	"fobrain/pkg/cfg"
)

func InitMiddleware(r *gin.Engine) {
	// 不使用缓存,跨域处理,错误处理
	r.Use(NoCache, Options, GinLogger, gzip.Gzip(gzip.DefaultCompression), gin.CustomRecovery(func(c *gin.Context, err any) {
		_ = response.FailWithDetailed(c, 400, nil, cast.ToString(err))
		c.Abort()
		return
	}))
	// 请求频率限制,每秒钟最多允许 10 个请求
	r.Use(LimitHandler(tollbooth.NewLimiter(10, &limiter.ExpirableOptions{
		DefaultExpirationTTL: time.Second,
	})))

	// 初始化第三方调用路由
	third_party.RouterGroupRegisterByThirdParty(r.Group(""))

	// 初始化路由
	v1Group := r.Group("/api/v1")
	noAuthGroupRouter := v1Group.Group("")
	authGroupRouter := v1Group.Group("")
	// 初始化无权限路由
	routes.InitNoAuthRouter(noAuthGroupRouter, r)
	// 初始化权限校验路由
	authGroupRouter.Use(upgrade.UpgradeMiddleware())
	authGroupRouter.Use(backup.Middleware())        // 旧版备份中间件
	authGroupRouter.Use(backupService.Middleware()) // 新版备份中间件
	authGroupRouter.Use(Authorization)
	routes.InitAuthRouter(authGroupRouter)
	// 初始化websocket路由
	routes.InitWebsocketRouter(wsocket.GetWSocket())

	// 加载静态资源
	if utils.FileExists(filepath.Join("storage", "app", "public")) {
		r.StaticFS("/storage", gin.Dir("storage", false))
	}

	// 获取产品授权模块
	license := license.GetLicense()
	models, err := license.GetProductModels()
	if err != nil {
		fmt.Println("获取产品授权模块失败", err)
	}
	fmt.Println("产品授权模块：", models)
	// 授权存在级联模块 或者 是dev模式 或者 是测试环境
	// cascade_admin 是级联上级模块，cascade是级联下级模块
	if slices.Contains(models, "enable_cascade") || slices.Contains(models, "enable_cascade-admin") || license2.CheckDevModel(cfg.LoadCommon().Env) || testcommon.IsTest() {
		fmt.Println("授权存在级联模块 或者 是dev模式 或者 是测试环境")
		// 注册级联模块
		db := mysql.GetDbClient()
		// 初始化级联路由
		cascadeGroup := v1Group.Group("/cascade")
		// 获取api token
		token, err := system_configs.NewSystemConfigs().GetConfig("cascade_api_token")
		if err != nil {
			fmt.Println("获取级联api token失败", err)
		}
		cascade.RegisterModule(cascadeGroup, nil, db, map[string]string{"token": token})
		// 保存级联下载地址
		err = saveCascadeDownloadUrl()
		if err != nil {
			fmt.Println("保存级联下载地址失败", err)
		}
		// 注册命令
		cascade_service.RegisterCmdHandler()
		// 注册节点创建事件
		sdk.RegisterNodeCreateEventHandler(func(node *node_model.DataCaptureNodes, connectStatus int) {
			if connectStatus == data_capture_nodes.ConnectSuccess {
				crontab.AddCascadeNodeTask(node)
			}
			logs.GetLogger().Info(fmt.Sprintf("节点创建事件 %s", node.Name))
		})
		// 注册节点更新事件
		sdk.RegisterNodeUpdateEventHandler(func(node *node_model.DataCaptureNodes, connectStatus int) {
			if connectStatus == data_capture_nodes.ConnectSuccess || connectStatus == data_capture_nodes.NoUpdate {
				crontab.UpdateCascadeNodeTask(node)
			} else {
				crontab.DeleteCascadeNodeTask(node.Id)
			}
			logs.GetLogger().Info(fmt.Sprintf("节点更新事件 %s", node.Name))
		})
		// 注册节点删除事件
		sdk.RegisterNodeDeleteEventHandler(func(nodeId uint64) {
			crontab.DeleteCascadeNodeTask(nodeId)
			logs.GetLogger().Info(fmt.Sprintf("节点删除事件 %d", nodeId))
		})
	}
}

// saveCascadeDownloadUrl 保存级联下载地址
func saveCascadeDownloadUrl() error {
	url := cfg.LoadCommon().DownloadUrl
	if url == "" {
		return nil
	}
	return system_configs.NewSystemConfigs().UpdateConfig("cascade_download_url", url)
}
