package middleware

import (
	license2 "fobrain/fobrain/app/repository/settings/license"
	"fobrain/fobrain/common/license"
	"net/http"

	blackwhite "fobrain/fobrain/app/services/system_configs/black_white"
	jwtAuth "fobrain/fobrain/common/auth"
	"fobrain/fobrain/common/response"
	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/models/mysql/user_staff"
	"fobrain/pkg/cfg"

	"github.com/gin-gonic/gin"
	"go-micro.dev/v4/logger"
)

func Authorization(c *gin.Context) {
	auth := c.Request.Header.Get("Authorization")
	if len(auth) == 0 { // 登录TOKEN获取失败
		c.Abort()
		_ = response.FailWithCodeMessage(c, 401, "Unauthorized")
		return
	}
	// 检查Token,获取用户id
	userId, tokenType, err := jwtAuth.CheckToken(auth)
	if err != nil { // Token 解析失败
		c.Abort()
		_ = response.FailWithCodeMessage(c, 401, err.Error())
		return
	}
	info, err := jwtAuth.GetRbacService().GetUserInfo(userId)
	if err != nil {
		c.Abort()
		_ = response.FailWithCodeMessage(c, 400, err.Error())
		return
	}
	if !license2.CheckDevModel(cfg.LoadCommon().Env) && !testcommon.IsTest() && c.FullPath() != "/api/v1/settings/license" {
		isExpired, err := license.GetLicense().IsProductExpired()
		if err != nil {
			c.Abort()
			_ = response.FailWithCodeMessage(c, 451, err.Error())
			return
		}
		if isExpired {
			if !info.IsSuperAdmin(userId) {
				c.Abort()
				_ = response.FailWithCodeMessage(c, 451, "产品测试已到期，请联系管理员激活产品")
				return
			} else {
				if c.FullPath() == "/api/v1/user" {
					c.Set("message", "产品测试已到期，请激活产品！")
				} else {
					c.Abort()
					_ = response.FailWithCodeMessage(c, 451, "产品测试已到期，请激活产品！")
					return
				}
			}
		}
	}
	clientIP := c.ClientIP()
	if clientIP != "" {
		isAllowed, err := blackwhite.NewBlackWhiteService().Judge(clientIP)
		if err != nil {
			logger.Errorf("[ERROR] Authorization ip (%s) blackwhite error: %v\n", clientIP, err)
			c.Abort()
			_ = response.FailWithCodeMessage(c, http.StatusBadRequest, "IP Unknown")
			return
		}
		if !isAllowed {
			c.Abort()
			logger.Infof("[INFO] Authorization IP Forbidden (%s) blackwhite\n", clientIP)
			_ = response.FailWithCodeMessage(c, http.StatusForbidden, "IP Forbidden")
			return
		}
	}

	if cfg.GetInstance().Common.CloseAuthToken == "true" || testcommon.IsTest() {
		c.Set("user_id", userId)
		c.Set("token_type", 1)
		c.Set("is_super_manage", true)
		c.Set("staff_ids", []string{"4ef89fb5cc8a8e03a18e0a48233754ff", "e40487520f21243daa65f93764aec0ca"})
		c.Next()
		return
	}

	// 校验用户权限
	//if ok, cErr := jwtAuth.GetRbacService().CheckAuth(userId, c.FullPath(), c.Request.Method); cErr != nil {
	//	c.Abort()
	//	_ = response.FailWithCodeMessage(c, 400, cErr.Error())
	//	return
	//} else if !ok {
	//	c.Abort()
	//	_ = response.FailWithCodeMessage(c, 403, "Forbidden")
	//	return
	//}
	c.Set("user_id", userId)
	c.Set("token_type", tokenType)

	// 是否管理员
	c.Set("is_super_manage", info.IsSuperAdmin(userId))

	// 人员台账ID

	c.Set("staff_ids", user_staff.NewUserRoleModel().Get(userId))

	c.Next()
}
