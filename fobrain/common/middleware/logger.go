package middleware

import (
	"fmt"
	"fobrain/fobrain/logs"
	"time"

	"github.com/gin-gonic/gin"
)

// GinLogger 接收gin框架默认的日志
func GinLogger(c *gin.Context) {
	start := time.Now()
	c.Next()
	cost := time.Since(start)
	logs.GetLogger().WithOutCaller().Info(fmt.Sprintf(
		"%s %s %s %d %v %s %s %s",
		c.ClientIP(),
		c.Request.Method,
		c.Request.URL.Path,
		c.Writer.Status(),
		cost,
		c.Request.UserAgent(),
		c.Request.URL.RawQuery,
		c.Errors.ByType(gin.ErrorTypePrivate).String(),
	))
}
