package wsocket

import (
	"errors"
	"fobrain/fobrain/logs"
	"strings"
	"sync"

	"github.com/gorilla/websocket"
)

// WsConn websocket连接
type (
	WsConn struct {
		inChan    chan []byte
		outChan   chan []byte
		closeChan chan []byte
		isClose   bool // 通道closeChan是否已经关闭
		mutex     sync.Mutex
		conn      *websocket.Conn
		ip        string
	}
)

// InitWebSocket 初始化Websocket
func InitWebSocket(conn *websocket.Conn, ip string) (ws *WsConn, err error) {
	ws = &WsConn{
		inChan:    make(chan []byte, 1024),
		outChan:   make(chan []byte, 1024),
		closeChan: make(chan []byte, 1024),
		conn:      conn,
		ip:        ip,
	}
	go ws.readMsgLoop()
	go ws.writeMsgLoop()
	logs.GetLogger().Info("WS ", ip+" ", "websocket链接建立成功")
	return
}

// InChanRead 读取inChan的数据
func (conn *WsConn) InChanRead() (data []byte, err error) {
	select {
	case data = <-conn.inChan:
	case <-conn.closeChan:
		err = errors.New(conn.ip + " connection is closed")
	}
	return
}

// InChanWrite inChan写入数据
func (conn *WsConn) InChanWrite(data []byte) (err error) {
	select {
	case conn.inChan <- data:
	case <-conn.closeChan:
		err = errors.New(conn.ip + "connection is closed")
	}
	return
}

// OutChanRead 读取inChan的数据
func (conn *WsConn) OutChanRead() (data []byte, err error) {
	select {
	case data = <-conn.outChan:
	case <-conn.closeChan:
		err = errors.New(conn.ip + "connection is closed")
	}
	return
}

// OutChanWrite inChan写入数据
func (conn *WsConn) OutChanWrite(data []byte) (err error) {
	select {
	case conn.outChan <- data:
	case <-conn.closeChan:
		err = errors.New(conn.ip + "connection is closed")
	}
	return
}

// CloseConn 关闭WebSocket连接
func (conn *WsConn) CloseConn() {
	// 关闭closeChan以控制inChan/outChan策略,仅此一次
	conn.mutex.Lock()
	if !conn.isClose {
		close(conn.closeChan)
		conn.isClose = true
	}
	conn.mutex.Unlock()
	//关闭WebSocket的连接,conn.Close()是并发安全可以多次关闭
	_ = conn.conn.Close()
}

// readMsgLoop 读取客户端发送的数据写入到inChan
func (conn *WsConn) readMsgLoop() {
	defer func() {
		conn.CloseConn()
	}()
	for {
		// 确定数据结构
		var (
			data []byte
			err  error
		)
		// 接受数据
		if _, data, err = conn.conn.ReadMessage(); err != nil {
			return
		}
		logs.GetLogger().Info("WS ", conn.ip+" ", "读取数据 ", strings.Replace(
			strings.Replace(string(data), "\n", "", -1),
			" ", "", -1))
		// 解析数据
		for group, parseFunc := range parseHandlers {
			cmd, params, pErr := parseFunc(string(data))
			if pErr != nil {
				logs.GetLogger().Info("WS ", conn.ip+" ", "解析数据 ", group, pErr.Error())
				continue
			}
			handle, ok := getHandlers(group, cmd)
			if !ok {
				logs.GetLogger().Warn("WS ", conn.ip+" ", "路由匹配失败 ", group, cmd)
				continue
			}
			if err = handle(conn, params); err != nil {
				logs.GetLogger().Warn("WS ", conn.ip+" ", "请求处理失败 ", group, cmd, err.Error())
				continue
			}
		}
	}
}

// writeMsgLoop 读取outChan的数据响应给客户端
func (conn *WsConn) writeMsgLoop() {
	defer func() {
		conn.CloseConn()
	}()
	for {
		var (
			data []byte
			err  error
		)
		// 读取数据
		if data, err = conn.OutChanRead(); err != nil {
			return
		}
		logs.GetLogger().Info("WS ", conn.ip+" ", "发送数据 ", string(data))
		// 发送数据
		if err = conn.conn.WriteMessage(websocket.TextMessage, data); err != nil {
			logs.GetLogger().Warn("WS ", conn.ip+" ", "发送数据失败 ", string(data))
			return
		}
	}
}
