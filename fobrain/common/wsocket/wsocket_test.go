package wsocket

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

// Mock WebSocket connection
type MockWsConn struct {
	WriteData []byte
	ReadData  []byte
}

func (m *MockWsConn) InChanWrite(data []byte) error {
	m.WriteData = data
	return nil
}

func (m *MockWsConn) InChanRead() ([]byte, error) {
	return m.ReadData, nil
}

func (m *MockWsConn) Close() error {
	return nil
}

func TestGetWSocket(t *testing.T) {
	ws1 := GetWSocket()
	ws2 := GetWSocket()
	assert.Equal(t, ws1, ws2)
}

func TestSendMessage(t *testing.T) {
	ws := GetWSocket()

	err := ws.SendMessage("nonexistent", "test message")
	assert.NotNil(t, err)
	assert.Equal(t, "client not found", err.Error())
}

func TestSetGroupParseHandler(t *testing.T) {
	ws := GetWSocket()
	parseFn := func(msg string) (string, map[string]any, error) {
		return "cmd", map[string]any{"key": "value"}, nil
	}
	ws.SetGroupParseHandler("group1", parseFn)
	assert.NotNil(t, parseHandlers["group1"])
}

func TestRegister(t *testing.T) {
	ws := GetWSocket()
	handlerFn := func(conn *WsConn, data map[string]any) error {
		return nil
	}
	ws.Register("group1", "cmd1", handlerFn)
	assert.NotNil(t, handlers["group1"]["cmd1"])
}

func TestGetHandlers(t *testing.T) {
	handlerFn := func(conn *WsConn, data map[string]any) error {
		return nil
	}
	GetWSocket().Register("group1", "cmd1", handlerFn)

	h, ok := getHandlers("group1", "cmd1")
	assert.True(t, ok)
	assert.NotNil(t, h)

	h, ok = getHandlers("group1", "nonexistent")
	assert.False(t, ok)
	assert.Nil(t, h)
}
