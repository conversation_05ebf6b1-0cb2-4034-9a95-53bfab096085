package wsocket

import (
	"errors"
	"fobrain/fobrain/logs"
	"net/http"
	"sync"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
)

type (
	// HandlerFu 处理函数
	HandlerFu func(conn *WsConn, data map[string]any) error
	// ParseFu 解析命令函数
	ParseFu func(msg string) (string, map[string]any, error)
	// WScoket websocket实例
	WScoket struct {
		clients map[string]*WsConn
	}
)

var (
	upgrade = &websocket.Upgrader{
		// 允许跨域
		CheckOrigin: func(r *http.Request) bool {
			return true
		},
	}
	once    sync.Once
	wsocket *WScoket
	// routes 处理函数
	handlers = make(map[string]map[string]HandlerFu)
	//	参数解析
	parseHandlers   = make(map[string]ParseFu)
	handlersRWMutex sync.RWMutex
)

// GetWSocket 获取websocket实例
func GetWSocket() *WScoket {
	if wsocket == nil {
		once.Do(func() {
			wsocket = &WScoket{
				clients: make(map[string]*WsConn),
			}
		})
	}
	return wsocket
}

// SendMessage 推送Ws消息
func (w *WScoket) SendMessage(key string, data string) error {
	conn, ok := w.clients[key]
	if ok {
		if err := conn.InChanWrite([]byte(data)); err != nil {
			return err
		}
	} else {
		return errors.New("client not found")
	}
	return nil
}

// WebSocketHandle websocket 请求处理
func (w *WScoket) WebSocketHandle(c *gin.Context) error {
	var (
		err  error
		conn *websocket.Conn
		ws   *WsConn
	)
	ip := c.Request.Header.Get("X-Real-IP")
	if ip == "" {
		ip = c.Request.Header.Get("X-Forwarded-For")
	}
	if ip == "" {
		ip = c.Request.RemoteAddr
	}
	if conn, err = upgrade.Upgrade(c.Writer, c.Request, nil); err != nil {
		return err
	}
	defer func() {
		_ = conn.Close()
	}()
	if ws, err = InitWebSocket(conn, ip); err != nil {
		return err
	}
	// 使得inChan和outChan耦合起来
	for {
		var data []byte
		if data, err = ws.InChanRead(); err != nil {
			logs.GetLogger().Info("WS ", err.Error())
			return nil
		}
		if err = ws.OutChanWrite(data); err != nil {
			logs.GetLogger().Infoln("WS ", err.Error())
			return nil
		}
	}
}

// SetGroupParseHandler 设置组解析函数
func (w *WScoket) SetGroupParseHandler(group string, value ParseFu) {
	parseHandlers[group] = value
	return
}

// Register 注册Ws路由
func (w *WScoket) Register(group, key string, value HandlerFu) {
	handlersRWMutex.Lock()
	defer handlersRWMutex.Unlock()
	if handlers[group] == nil {
		handlers[group] = make(map[string]HandlerFu)
	}
	handlers[group][key] = value
	return
}

// getHandlers 获取处理方法
func getHandlers(group, key string) (value HandlerFu, ok bool) {
	handlersRWMutex.RLock()
	defer handlersRWMutex.RUnlock()
	value, ok = handlers[group][key]
	return
}
