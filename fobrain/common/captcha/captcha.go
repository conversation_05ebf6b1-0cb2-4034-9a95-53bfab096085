package captcha

import (
	"bytes"
	"context"
	"embed"
	"encoding/base64"
	"fmt"
	"fobrain/pkg/cfg"
	"image/png"
	"io/ioutil"
	"os"
	"strings"
	"time"

	"github.com/afocus/captcha"
	"github.com/google/uuid"

	"fobrain/initialize/redis"
)

var capt *captcha.Captcha

// var FontPath = "/data/fobrain/storage/app/fonts/RitaSmith.ttf"

//go:embed assets/font/RitaSmith.ttf
var FontPath embed.FS

// GetFontFilePath returns the path to a temporary file containing the embedded font data
// @description: 通过 embed 将字体文件嵌入到二进制文件中
func GetFontFilePath() string {
	data, _ := FontPath.ReadFile("assets/font/RitaSmith.ttf")
	tempFile, _ := ioutil.TempFile("", "RitaSmith-*.ttf")

	if _, err := tempFile.Write(data); err != nil {
		tempFile.Close()
		os.Remove(tempFile.Name())
		return ""
	}

	if err := tempFile.Close(); err != nil {
		return ""
	}

	return tempFile.Name()
}

// Generate 生成验证码
func Generate() (id, b64s string, err error) {
	capt = captcha.New()
	// 可以设置多个字体 或使用cap.AddFont("xx.ttf")追加
	capt.SetFont(GetFontFilePath())
	// 设置验证码大小
	capt.SetSize(128, 64)
	// 设置干扰强度
	capt.SetDisturbance(captcha.NORMAL)
	// 设置前景色 可以多个 随机替换文字颜色 默认黑色
	// capt.SetFrontColor(color.RGBA{255, 255, 255, 255})
	// 设置背景色 可以多个 随机替换背景色 默认白色
	// cap.SetBkgColor(color.RGBA{255, 0, 0, 255}, color.RGBA{0, 0, 255, 255}, color.RGBA{0, 153, 0, 255})
	capt.SetFont(GetFontFilePath())
	img, imageText := capt.Create(4, captcha.CLEAR)
	code := strings.ToLower(imageText)
	id = uuid.New().String()

	client := redis.GetRedisClient()
	client.Set(context.Background(), id, code, 60*time.Second)

	// 生成图片
	emptyBuff := bytes.NewBuffer(nil)
	_ = png.Encode(emptyBuff, img)
	return id, fmt.Sprintf(
		"data:image/png;base64,%s",
		base64.StdEncoding.EncodeToString(emptyBuff.Bytes()),
	), nil
}

// Verify 验证验证码
func Verify(id, answer string) bool {
	if cfg.GetInstance().Common.CloseCaptcha == "true" {
		return true
	}

	// 验证id和answer是否为空
	if id == "" || answer == "" {
		return false
	}
	client := redis.GetRedisClient()
	defer client.Del(context.Background(), id)

	val, err := client.Get(context.Background(), id).Result()
	if err != nil {
		return false
	}

	return strings.Trim(val, "\"") == strings.ToLower(answer)
}
