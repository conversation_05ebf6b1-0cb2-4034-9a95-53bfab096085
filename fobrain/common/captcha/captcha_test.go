package captcha

import (
	"testing"

	"github.com/alicebob/miniredis/v2"
	redis2 "github.com/go-redis/redis/v8"
	. "github.com/smartystreets/goconvey/convey"
	"github.com/stretchr/testify/suite"

	"fobrain/fobrain/config"
	testcommon "fobrain/fobrain/tests/common_test"
)

type CaptchaSuite struct {
	suite.Suite
}

func TestCaptchaSuite(t *testing.T) {
	s := &CaptchaSuite{}
	_ = config.LoadConfigByFile("../../.env")
	r := miniredis.RunT(t)
	defer r.Close()
	client := redis2.NewClient(&redis2.Options{Addr: r.Addr()})
	testcommon.SetRedisClient(client)
	suite.Run(t, s)
}

func (s *CaptchaSuite) Test_common_GenerateFunc() {
	Convey("Test_common_GenerateFunc", s.T(), func() {
		Convey("GenerateFunc PASS", func() {
			_, _, err := Generate()
			So(err, ShouldBeNil)
		})
	})
}

func (s *CaptchaSuite) Test_common_VerifyFunc() {
	//Convey("Test_common_VerifyFunc", s.T(), func() {
	//
	//	Convey("VerifyFunc ERROR", func() {
	//		id, _, _ := Generate()
	//		flag := Verify(id, "answer")
	//		So(flag, ShouldEqual, false)
	//	})
	//
	//})
}
