package response

import (
	"errors"
	"fmt"
	"net/http"
	"os"
	"path/filepath"

	"github.com/gin-gonic/gin"

	"fobrain/pkg/utils"
)

const (
	ERROR      = 400
	SUCCESS    = 0
	MsgSuccess = "Success"
	MsgFail    = "Fail"
)

type (
	Response struct {
		Code    int    `json:"code"`
		Message string `json:"message"`
		Data    any    `json:"data"`
	}

	PageResult struct {
		Total   int64 `json:"total"`
		Page    int   `json:"page"`
		PerPage int   `json:"per_page"`
		Items   any   `json:"items"`
	}

	PageCustomFieldResult struct {
		Total            int64               `json:"total"`
		Page             int                 `json:"page"`
		PerPage          int                 `json:"per_page"`
		Items            any                 `json:"items"`
		ListFields       []map[string]string `json:"list_fields"`
		ListDetailFields []map[string]string `json:"list_detail_fields"`
	}
)

func Result(code int, data interface{}, msg string, c *gin.Context) error {
	if msg == "EOF" {
		msg = "参数错误"
	}
	if code == SUCCESS {
		c.JSON(http.StatusOK, Response{code, msg, data})
	} else {
		c.JSON(code, Response{code, msg, data})
	}
	return nil
}

func Ok(c *gin.Context) error {
	fmt.Println()
	return Result(SUCCESS, map[string]interface{}{}, MsgSuccess, c)
}

func OkWithImage(c *gin.Context, path string) error {
	ext := filepath.Ext(path)
	var contentType string
	switch ext {
	case ".png":
		contentType = "image/png"
	case ".jpg", ".jpeg":
		contentType = "image/jpeg"
	case ".svg":
		contentType = "text/xml; charset=utf-8"
	case ".ico":
		contentType = "image/x-icon"
	default:
		c.JSON(http.StatusBadRequest, gin.H{"error": "Unsupported file format"})
		return errors.New("unsupported file format")
	}
	c.Header("Content-Type", contentType)
	c.Header("Content-Description", "File Transfer")
	c.Header("Content-Transfer-Encoding", "binary")
	file, err := utils.ReadFile(path)
	if err != nil {
		return err
	}
	_, err = c.Writer.Write(file)
	if err != nil {
		return err
	}
	c.Status(http.StatusOK)
	return nil
}

func OkWithFile(c *gin.Context, path string, delete bool) error {
	// 如果请求包含 Range 头，说明是分片下载
	if c.GetHeader("Range") != "" {
		// 分片下载
		if err := utils.DownloadFileByRange(c, path); err != nil {
			return FailWithCodeMessage(c, 400, err.Error())
		}
	} else {
		// 下载完成文件
		if err := utils.DownloadFileByFull(c, path); err != nil {
			return FailWithCodeMessage(c, 400, err.Error())
		}
	}
	// 删除文件
	if delete {
		if err := utils.FileExists(path); err == true {
			_ = os.Remove(path)
		}
	}
	return nil
}

func OkWithPageData(c *gin.Context, total int64, page, perPage int, items any) error {
	return OkWithData(c, PageResult{Items: items, Total: total, Page: page, PerPage: perPage})
}

func OkWithPageCustomData(c *gin.Context, total int64, page, perPage int, items any, listFields []map[string]string, listDetailFields []map[string]string) error {
	return OkWithData(c, PageCustomFieldResult{Items: items, ListFields: listFields, ListDetailFields: listDetailFields, Total: total, Page: page, PerPage: perPage})
}

func OkWithMessage(c *gin.Context, message string) error {
	return Result(SUCCESS, map[string]interface{}{}, message, c)
}

func OkWithData(c *gin.Context, data interface{}) error {
	return Result(SUCCESS, data, MsgSuccess, c)
}

func OkWithDetailed(data interface{}, message string, c *gin.Context) error {
	return Result(SUCCESS, data, message, c)
}

func Fail(c *gin.Context) error {
	return Result(ERROR, map[string]interface{}{}, MsgFail, c)
}

func FailWithMessage(c *gin.Context, message string) error {
	return Result(ERROR, map[string]interface{}{}, message, c)
}

func FailWithCodeMessage(c *gin.Context, code int, message string) error {
	return Result(code, map[string]interface{}{}, message, c)
}

func FailWithDetailed(c *gin.Context, code int, data interface{}, message string) error {
	return Result(code, data, message, c)
}
