package wrapper

import (
	"bytes"
	"fmt"
	"fobrain/fobrain/common/request"
	"fobrain/fobrain/common/response"
	"fobrain/fobrain/logs"
	"fobrain/initialize/mysql"
	"fobrain/models/mysql/audit_logs"
	"fobrain/models/mysql/permission"
	"io"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

type HandlerFuncWrapper func(ctx *gin.Context) error

type bodyWriter struct {
	gin.ResponseWriter
	body *bytes.Buffer
}

func (w *bodyWriter) Write(b []byte) (int, error) {
	w.body.Write(b) // 缓存响应数据
	return w.ResponseWriter.Write(b)
}

// Fn 请求处理函数返回错误处理
func Fn(wrapper HandlerFuncWrapper) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		result := "success"
		userId := request.GetUserId(ctx)

		// ---------- 记录请求参数 ----------
		var requestData string
		// 读取请求体内容
		bodyBytes, _ := io.ReadAll(ctx.Request.Body)
		ctx.Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes)) // 恢复请求体
		requestData = string(bodyBytes)

		// 若是 GET/DELETE，附加 URL 查询参数 包括 ？参数跟 /参数
		if ctx.Request.Method == http.MethodGet || ctx.Request.Method == http.MethodDelete {
			queryStr := ctx.Request.URL.RawQuery
			if queryStr != "" {
				if requestData != "" {
					requestData += " | "
				}
				requestData += "Query: " + queryStr
			}
		}

		// 记录路径参数，适用于所有HTTP方法
		if len(ctx.Params) > 0 {
			pathParams := make([]string, 0, len(ctx.Params))
			for _, param := range ctx.Params {
				pathParams = append(pathParams, param.Key+": "+param.Value)
			}

			pathParamsStr := strings.Join(pathParams, ", ")
			if pathParamsStr != "" {
				if requestData != "" && requestData != "{}" {
					requestData += " | "
				} else if requestData == "{}" {
					requestData = ""
				}

				requestData += "Path Params: " + pathParamsStr
			}
		}

		// 若 POST 且请求体为空，尝试解析表单参数
		if len(bodyBytes) == 0 && ctx.Request.Method == http.MethodPost {
			ctx.Request.ParseForm()
			requestData = ctx.Request.PostForm.Encode()
		}

		// ---------- 执行业务逻辑 ----------
		// 创建自定义响应写入器
		rw := &bodyWriter{body: bytes.NewBufferString(""), ResponseWriter: ctx.Writer}
		ctx.Writer = rw

		if err := wrapper(ctx); err != nil {
			logs.GetLogger().Error(fmt.Sprintf("请求处理失败：%s, %+v", ctx.FullPath(), err))
			_ = response.FailWithMessage(ctx, err.Error())
			result = err.Error()
		}

		// 检查状态码，如果不是200，更新result
		if rw.ResponseWriter.Status() != http.StatusOK {
			result = fmt.Sprintf("请求失败，状态码: %d；相应内容： %s", rw.ResponseWriter.Status(), rw.body.String())
		}

		// ---------- 写入审计日志 ----------
		m, _ := permission.NewMenusModel().One(&mysql.QueryBuilder{
			Where: []mysql.Condition{
				mysql.CompareCond{Field: "path", Operator: "=", Value: staticRoutePrefix(ctx.FullPath())},
				mysql.CompareCond{Field: "method", Operator: "=", Value: ctx.Request.Method},
				mysql.CompareCond{Field: "type", Operator: "=", Value: 4},
			},
		})
		//  用户未登录 记录登录日志，用户登录后，记录所有操作日志
		if userId == 0 && !strings.HasPrefix(ctx.FullPath(), "/api/v1/auth/login") {
			return
		}
		//记录登录跟鉴权日志
		if strings.HasPrefix(ctx.FullPath(), "/api/v1/auth/login") {
			m = permission.Menus{
				Name:  "登录",
				Title: "鉴权操作",
			}
		}

		if strings.HasPrefix(ctx.FullPath(), "/api/v1/user/login_out") {
			m = permission.Menus{
				Name:  "退出",
				Title: "鉴权操作",
			}
		}

		//if m.Name == "" {
		//	return
		//}
		if err := audit_logs.NewAuditLogModel().Create(&audit_logs.AuditLog{
			UserId:      cast.ToUint64(userId),
			Title:       m.Title,
			Operation:   m.Name,
			RequestData: requestData,
			Type:        request.GetTokenType(ctx),
			Path:        staticRoutePrefix(ctx.FullPath()),
			Result:      result,
			Ip:          ctx.ClientIP(),
		}); err != nil {
			logs.GetLogger().Warn("审计日志写入失败:", err.Error())
		}

		// ---------- 最终日志记录 ----------
		// logs.GetLogger().Info(fmt.Sprintf(
		// 	"接口日志 => Path: %s, Method: %s, 请求参数: [%s], 响应参数: [%s]",
		// 	ctx.FullPath(), ctx.Request.Method, requestData, rw.body.String()))
	}
}

func staticRoutePrefix(fullPath string) string {
	segments := strings.Split(fullPath, "/")
	var result []string
	for _, seg := range segments {
		if seg == "" || strings.HasPrefix(seg, ":") {
			continue // 忽略 :id 等动态部分
		}
		result = append(result, seg)
	}
	return "/" + strings.Join(result, "/")
}
