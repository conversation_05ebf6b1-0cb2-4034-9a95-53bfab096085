package validate

import (
	"testing"

	"github.com/go-playground/validator/v10"
)

func TestIsPortValid(t *testing.T) {
	validate := validator.New()
	validate.RegisterValidation("port", isPortValid)

	tests := []struct {
		name  string
		port  interface{}
		valid bool
	}{
		{"Valid port as string", "8080", true},
		{"Valid port as int", 8080, true},
		{"Valid port as uint", uint(8080), true},
		{"Invalid port (too low) as string", "0", false},
		{"Invalid port (too low) as int", 0, false},
		{"Invalid port (too low) as uint", uint(0), false},
		{"Invalid port (too high) as string", "65536", false},
		{"Invalid port (too high) as int", 65536, false},
		{"Invalid port (too high) as uint", uint(65536), false},
		{"Invalid port (non-numeric string)", "invalid", false},
		{"Invalid port (negative int)", -8080, false},
		{"Invalid port (negative string)", "-8080", false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validate.Var(tt.port, "port")
			if tt.valid && err != nil {
				t.Errorf("Expected port %v to be valid, but got error: %v", tt.port, err)
			} else if !tt.valid && err == nil {
				t.Errorf("Expected port %v to be invalid, but got no error", tt.port)
			}
		})
	}
}
