package validate

import (
	"reflect"
	"strconv"

	unTrans "github.com/go-playground/universal-translator"
	"github.com/go-playground/validator/v10"
)

// isPortValid checks if the given port is valid or not.
//
// A valid port is an integer between 1 and 65535 (inclusive).
func isPortValid(fl validator.FieldLevel) bool {
	var port int64
	switch fl.Field().Kind() {
	case reflect.String:
		port, _ = strconv.ParseInt(fl.Field().String(), 10, 64)
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		port = fl.Field().Int()
	case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
		port = int64(fl.Field().Uint())
	}
	return port >= 1 && port <= 65535
}

func registerPortValidatingTrans(validate *validator.Validate, trans *unTrans.Translator) {
	_ = validate.RegisterTranslation("port", *trans, func(ut unTrans.Translator) error {
		return ut.Add("port", "{0}必须是一个有效值", true) // see universal-translator for details
	}, func(ut unTrans.Translator, fe validator.FieldError) string {
		t, _ := ut.T("port", fe.Field())
		return t
	})
}
