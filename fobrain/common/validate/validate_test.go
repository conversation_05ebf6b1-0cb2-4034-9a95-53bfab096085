package validate

import (
	"github.com/go-playground/validator/v10"
	"testing"

	"github.com/stretchr/testify/assert"
)

type TestStruct struct {
	Port      string `validate:"port"`
	Db        string `validate:"db"`
	Domain    string `validate:"domain"`
	IPSegment string `validate:"ipSegment"`
	MAC       string `validate:"mac"`
	Phone     string `validate:"phone"`
	LowerCase string `validate:"lowercaseUnderscore"`
}

func TestValidator(t *testing.T) {
	tests := []struct {
		name     string
		input    TestStruct
		expected bool
	}{
		{
			name: "Invalid domain",
			input: TestStruct{
				Port:      "8080",
				Db:        "valid_db_name",
				Domain:    "invalid_domain",
				IPSegment: "***********/24",
				MAC:       "01:23:45:67:89:AB",
				Phone:     "13800138000",
			},
			expected: false,
		},
		{
			name: "Invalid IP segment",
			input: TestStruct{
				Port:      "8080",
				Db:        "valid_db_name",
				Domain:    "example.com",
				IPSegment: "***********-300",
				MAC:       "01:23:45:67:89:AB",
				Phone:     "13800138000",
			},
			expected: false,
		},
		{
			name: "Invalid MAC",
			input: TestStruct{
				Port:      "8080",
				Db:        "valid_db_name",
				Domain:    "example.com",
				IPSegment: "***********/24",
				MAC:       "invalid_mac",
				Phone:     "13800138000",
			},
			expected: false,
		},
		{
			name: "Invalid phone",
			input: TestStruct{
				Port:      "8080",
				Db:        "valid_db_name",
				Domain:    "example.com",
				IPSegment: "***********/24",
				MAC:       "01:23:45:67:89:AB",
				Phone:     "invalid_phone",
			},
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ok, _ := Validator(tt.input)
			assert.Equal(t, tt.expected, ok)
		})
	}
}

func TestIsValidIPRange(t *testing.T) {
	tests := []struct {
		name      string
		ipSegment string
		expected  bool
	}{
		{
			name:      "Valid IP range with full IPs",
			ipSegment: "***********-***********0",
			expected:  true,
		},
		{
			name:      "Valid IP range with shorthand",
			ipSegment: "***********-10",
			expected:  true,
		},
		{
			name:      "Invalid IP range with invalid start IP",
			ipSegment: "InvalidIP-***********0",
			expected:  false,
		},
		{
			name:      "Invalid IP range with invalid end IP",
			ipSegment: "***********-InvalidIP",
			expected:  false,
		},
		{
			name:      "Invalid IP range with missing end IP",
			ipSegment: "***********-",
			expected:  false,
		},
		{
			name:      "Invalid format without dash",
			ipSegment: "***********",
			expected:  false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := isValidIPRange(tt.ipSegment)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestCompleteIPRange(t *testing.T) {
	tests := []struct {
		name      string
		startIP   string
		endIPPart string
		expected  [2]string
		expectErr bool
	}{
		{
			name:      "Valid shorthand IP range",
			startIP:   "***********",
			endIPPart: "10",
			expected:  [2]string{"***********", "***********0"},
			expectErr: false,
		},
		{
			name:      "Valid full IP range",
			startIP:   "***********",
			endIPPart: "***********0",
			expected:  [2]string{"***********", "***********0"},
			expectErr: false,
		},
		{
			name:      "Invalid IP range with different lengths",
			startIP:   "***********",
			endIPPart: "192.168.10",
			expected:  [2]string{},
			expectErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := completeIPRange(tt.startIP, tt.endIPPart)
			if tt.expectErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expected, result)
			}
		})
	}
}

type testStruct struct {
	IPs string `validate:"custom_ips"`
}

func TestValidateIPs(t *testing.T) {
	validate := validator.New()
	validate.RegisterValidation("custom_ips", validateIPs)

	tests := []struct {
		name     string
		input    string
		expected bool
	}{
		{"Valid Single IP", "***********", true},
		{"Valid Multiple IPs", "***********, ********", true},
		{"Invalid IP", "192.168.1.256", false},
		{"Valid & Invalid IPs", "***********, 300.300.300.300", false},
		{"Empty String", "", false},
		{"Spaces and Valid IPs", "  127.0.0.1  ,  *******  ", true},
		{"Only Comma", ",", false},
		{"Trailing Comma", "***********,", false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ts := testStruct{IPs: tt.input}
			err := validate.Struct(ts)
			result := err == nil
			assert.Equal(t, tt.expected, result)
		})
	}
}

// TestLowercaseUnderscore 测试 lowercaseUnderscore 验证函数
func TestLowercaseUnderscore(t *testing.T) {
	validate := validator.New()
	validate.RegisterValidation("lowercaseUnderscore", lowercaseUnderscore)

	type testStruct struct {
		FieldName string `validate:"lowercaseUnderscore"`
	}

	tests := []struct {
		name     string
		input    string
		expected bool
	}{
		{
			name:     "有效的单个小写单词",
			input:    "field",
			expected: true,
		},
		{
			name:     "有效的下划线分隔小写单词",
			input:    "field_name",
			expected: true,
		},
		{
			name:     "有效的多个下划线分隔小写单词",
			input:    "custom_field_name",
			expected: true,
		},
		{
			name:     "有效的长字段名",
			input:    "very_long_custom_field_name_with_many_words",
			expected: true,
		},
		{
			name:     "无效的包含大写字母",
			input:    "Field",
			expected: false,
		},
		{
			name:     "无效的包含大写字母和下划线",
			input:    "field_Name",
			expected: false,
		},
		{
			name:     "无效的包含数字",
			input:    "field1",
			expected: false,
		},
		{
			name:     "无效的包含数字和下划线",
			input:    "field_1_name",
			expected: false,
		},
		{
			name:     "无效的包含特殊字符",
			input:    "field-name",
			expected: false,
		},
		{
			name:     "无效的包含空格",
			input:    "field name",
			expected: false,
		},
		{
			name:     "无效的以下划线开头",
			input:    "_field",
			expected: false,
		},
		{
			name:     "无效的以下划线结尾",
			input:    "field_",
			expected: false,
		},
		{
			name:     "无效的连续下划线",
			input:    "field__name",
			expected: false,
		},
		{
			name:     "无效的空字符串",
			input:    "",
			expected: false,
		},
		{
			name:     "无效的只有下划线",
			input:    "_",
			expected: false,
		},
		{
			name:     "无效的只有下划线组合",
			input:    "___",
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ts := testStruct{FieldName: tt.input}
			err := validate.Struct(ts)
			result := err == nil
			assert.Equal(t, tt.expected, result, "输入: %s, 期望: %v, 实际: %v", tt.input, tt.expected, result)
		})
	}
}
