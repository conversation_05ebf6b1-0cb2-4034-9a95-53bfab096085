package validate

import (
	"fmt"
	"net"
	"reflect"
	"regexp"
	"strings"
	"sync"

	"fobrain/fobrain/logs"

	zhCN "github.com/go-playground/locales/zh_<PERSON>_CN"
	unTrans "github.com/go-playground/universal-translator"
	"github.com/go-playground/validator/v10"
	zhTrans "github.com/go-playground/validator/v10/translations/zh"
	"github.com/nyaruka/phonenumbers"
)

var (
	once     sync.Once
	validate *validator.Validate
	trans    unTrans.Translator
)

func initInstance() {
	validate = validator.New()
	uni := unTrans.New(zhCN.New())
	trans, _ = uni.GetTranslator("zh_Hans_CN")
	err := zhTrans.RegisterDefaultTranslations(validate, trans)
	if err != nil {
		logs.GetLogger().Warnf("register RegisterDefaultTranslations failed, err: %v", err)
	}
	// 注册用户自定义
	registerCustomValidation(validate, &trans)
}

func getValidateInstance() (*validator.Validate, unTrans.Translator) {
	if validate == nil || trans == nil {
		once.Do(func() { initInstance() })
	}
	return validate, trans
}

func Validator(data any) (bool, string) {
	check, tr := getValidateInstance()
	err := check.Struct(data)
	if err != nil {
		for _, v := range err.(validator.ValidationErrors) {
			return false, v.Translate(tr)
		}
	}
	return true, ""
}

func getZhTag(fld reflect.StructField) string { return fld.Tag.Get("zh") }

func registerCustomValidation(validate *validator.Validate, trans *unTrans.Translator) {
	// 注册获取zh tag值
	validate.RegisterTagNameFunc(getZhTag)

	// 注册检验端口有效性
	if err := validate.RegisterValidation("port", isPortValid); err != nil {
		logs.GetLogger().Warnf("register validation func: port failed: %v", err)
	}
	// 注册数据校验
	if err := validate.RegisterValidation("db", isDbValid); err != nil {
		logs.GetLogger().Warnf("register validation func: db failed: %v", err)
	}

	// 注册域名校验
	if err := validate.RegisterValidation("domain", validateDomain); err != nil {
		logs.GetLogger().Warnf("register validation func: domain failed: %v", err)
	}
	// 注册IP段校验，支持格式如下：
	//**********/24
	//**********-100
	//**********-**********00
	if err := validate.RegisterValidation("ipSegment", isValidIpSegment); err != nil {
		logs.GetLogger().Warnf("register validation func: ipSegment failed: %v", err)
	}
	// 注册mac校验
	if err := validate.RegisterValidation("mac", validateMAC); err != nil {
		logs.GetLogger().Warnf("register validation func: validateMAC failed: %v", err)
	}
	// 注册手机号校验
	if err := validate.RegisterValidation("phone", validatePhoneOrLandline); err != nil {
		logs.GetLogger().Warnf("register validation func: validatePhoneOrLandline failed: %v", err)
	}
	// 注册检查字符串是否为全小写字母并且用下划线隔开
	if err := validate.RegisterValidation("lowercaseUnderscore", lowercaseUnderscore); err != nil {
		logs.GetLogger().Warnf("register validation func: lowercaseUnderscore failed: %v", err)
	}

	if err := validate.RegisterValidation("ips", validateIPs); err != nil {
		logs.GetLogger().Warnf("register validation func: validateIPs failed: %v", err)
	}

	registerPortValidatingTrans(validate, trans)
	registerDbValidatingTrans(validate, trans)
}

// 自定义验证函数，验证逗号分隔的多个 IP 地址
func validateIPs(fl validator.FieldLevel) bool {
	ipList := fl.Field().String()
	ips := strings.Split(ipList, ",")

	for _, ip := range ips {
		ip = strings.TrimSpace(ip)
		if net.ParseIP(ip) == nil {
			return false
		}
	}
	return true
}

// 自定义校验函数，校验域名
func validateDomain(fl validator.FieldLevel) bool {
	// 使用正则表达式来验证域名格式
	domain := fl.Field().String()
	re := regexp.MustCompile(`^([a-zA-Z0-9]+(-[a-zA-Z0-9]+)*\.)+[a-zA-Z]{2,}$`)
	return re.MatchString(domain)
}

// 自定义校验函数，校验 IP 段
func isValidIpSegment(fl validator.FieldLevel) bool {
	ipSegment := fl.Field().String()
	return isValidCIDR(ipSegment) || isValidIPRange(ipSegment)
}

// 校验 CIDR 表示法
func isValidCIDR(ipSegment string) bool {
	_, _, err := net.ParseCIDR(ipSegment)
	return err == nil
}

// 校验 IP 范围表示法
func isValidIPRange(ipSegment string) bool {
	if strings.Contains(ipSegment, "-") {
		parts := strings.Split(ipSegment, "-")
		if len(parts) == 2 {
			startIP := net.ParseIP(parts[0])
			if startIP == nil {
				return false
			}
			endIP := net.ParseIP(parts[1])
			if endIP == nil {
				// 如果 endIP 不是一个完整的 IP 地址，尝试补全
				if ipRange, err := completeIPRange(parts[0], parts[1]); err == nil {
					return net.ParseIP(ipRange[1]) != nil
				}
				return false
			}
			return true
		}
	}
	return false
}

// 补全 IP 范围表示法中的简写形式
func completeIPRange(startIP, endIPPart string) ([2]string, error) {
	startIPParts := strings.Split(startIP, ".")
	endIPParts := strings.Split(endIPPart, ".")

	if len(endIPParts) == 1 {
		// endIP 是一个数字，需要补全
		startIPParts[3] = endIPPart
		return [2]string{startIP, strings.Join(startIPParts, ".")}, nil
	}

	if len(startIPParts) == len(endIPParts) {
		return [2]string{startIP, endIPPart}, nil
	}
	return [2]string{}, fmt.Errorf("invalid IP range")
}

func validateMAC(fl validator.FieldLevel) bool {
	// 定义 MAC 地址的正则表达式
	macRegex := regexp.MustCompile(`^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$`)

	// 检查 MAC 地址是否匹配正则表达式
	mac := fl.Field().String()
	if !macRegex.MatchString(mac) {
		return false
	}

	return true
}

// validatePhoneOrLandline 自定义手机号或座机号验证器
func validatePhoneOrLandline(fl validator.FieldLevel) bool {
	phone := fl.Field().String()
	// 解析电话号码
	num, err := phonenumbers.Parse(phone, "CN") // 假设输入的电话号码是中国号码
	if err != nil {
		return false
	}

	// 验证电话号码是否有效
	if !phonenumbers.IsValidNumber(num) {
		return false
	}
	return true
}

// lowercaseUnderscore 检查字符串是否为全小写字母并且用下划线隔开
func lowercaseUnderscore(fl validator.FieldLevel) bool {
	// 正则表达式：匹配全小写字母，单词间用下划线隔开
	regex := `^[a-z]+(_[a-z]+)*$`
	matched, _ := regexp.MatchString(regex, fl.Field().String())
	return matched
}
