package validate

import (
	"fmt"
	"fobrain/initialize/mysql"
	"fobrain/pkg/utils"
	"net/url"
	"reflect"
	"strings"

	unTrans "github.com/go-playground/universal-translator"
	"github.com/go-playground/validator/v10"
	"gorm.io/gorm"
)

func isDbValid(fl validator.FieldLevel) bool {
	info, err := url.Parse(fl.Param())
	if err != nil {
		return false
	}
	rule := info.Query().Get("_rule")
	var count int64
	query := getDbQuery(info.Path, info.Query(), fl.Field().Interface())
	if qErr := query.Count(&count).Error; qErr != nil {
		return false
	}
	switch rule {
	case "exists":
		if count > 0 {
			return true
		}
	case "not_exists":
		if count == 0 {
			return true
		}
	default:
		return false
	}
	return false
}
func getDbQuery(table string, values url.Values, requestVal any) *gorm.DB {
	query := mysql.GetDbClient().Table(table)
	for k, v := range values {
		v = utils.ListDistinctNonZero(v)
		if strings.HasPrefix(k, "_") {
			continue
		}
		if len(v) == 0 {
			if strings.Contains(reflect.ValueOf(requestVal).Type().String(), "[]") {
				query.Where(fmt.Sprintf("%s in (?)", k), requestVal)
			} else {
				query.Where(fmt.Sprintf("%s = ?", k), requestVal)
			}
		} else if len(v) == 1 {
			query.Where(fmt.Sprintf("%s = ?", k), v[0])
		} else {
			if strings.Contains(reflect.ValueOf(v).Type().String(), "[]") {
				query.Where(fmt.Sprintf("%s in (?)", k), v)
			} else {
				query.Where(fmt.Sprintf("%s = ?", k), v)
			}
		}
	}
	return query
}

func registerDbValidatingTrans(validate *validator.Validate, trans *unTrans.Translator) {
	_ = validate.RegisterTranslation("db", *trans, func(ut unTrans.Translator) error {
		return ut.Add("db", "{0}", true)
	}, func(ut unTrans.Translator, fe validator.FieldError) string {
		info, err := url.Parse(fe.Param())
		if err != nil {
			return "规则校验异常"
		}
		// 自定义提示
		if eMsg := info.Query().Get("_err"); eMsg != "" {
			return eMsg
		}
		rule := info.Query().Get("_rule")
		field, _ := ut.T("db", fe.Field())
		switch rule {
		case "exists":
			return fmt.Sprintf("%s不存在", field)
		case "not_exists":
			return fmt.Sprintf("%s已存在", field)
		default:
			return fmt.Sprintf("%s规则校验异常", field)
		}
	})
}
