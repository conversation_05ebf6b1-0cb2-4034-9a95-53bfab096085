package logs

import (
	"fobrain/pkg/cfg"
	"fobrain/pkg/utils/common_logs"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestGetLogger(t *testing.T) {
	patch := gomonkey.ApplyFuncReturn(common_logs.InitLogger, &common_logs.Logger{})
	logger := GetLogger()
	patch.Reset()
	assert.NotNil(t, logger)
}

func TestGetCrontabLogger(t *testing.T) {
	cfgLogger := cfg.Logger{}
	patches := []*gomonkey.Patches{
		gomonkey.ApplyFuncReturn(cfg.LoadLogger, cfgLogger),
		gomonkey.ApplyFuncReturn(common_logs.InitLogger, &common_logs.Logger{}),
	}
	logger := GetCrontabLogger()
	for _, patch := range patches {
		patch.Reset()
	}
	assert.NotNil(t, logger)
}
