package logs

import (
	"fobrain/pkg/cfg"
	"fobrain/pkg/utils/common_logs"
	"sync"
)

var (
	// Logger 日志
	once sync.Once
	Log  *common_logs.Logger

	syncOnce sync.Once
	LogSync  *common_logs.Logger

	onceCrontab sync.Once
	LogCrontab  *common_logs.Logger

	onceBackup sync.Once
	LogBackup  *common_logs.Logger
)

func GetLogger() *common_logs.Logger {
	if Log == nil {
		once.Do(func() {
			Log = common_logs.InitLogger(cfg.LoadLogger())
		})
	}
	return Log
}

func GetSyncLogger() *common_logs.Logger {
	syncOnce.Do(func() {
		LogSync = common_logs.InitLogger(cfg.LoadLoggerByType("sync"))
	})
	return LogSync
}

func GetCrontabLogger() *common_logs.Logger {
	if LogCrontab == nil {
		onceCrontab.Do(func() {
			conf := cfg.LoadLogger()
			// 定时任务关闭控制台输出
			conf.OutPutConsole = false
			LogCrontab = common_logs.InitLogger(conf)
		})
	}

	return LogCrontab
}

func GetBackupLogger() *common_logs.Logger {
	if LogBackup == nil {
		onceBackup.Do(func() {
			LogBackup = common_logs.InitLogger(cfg.LoadLoggerByType("backup"))
		})
	}
	return LogBackup
}
