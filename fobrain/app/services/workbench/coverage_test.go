package workbench

import (
	"fobrain/fobrain/app/repository/asset"
	"fobrain/fobrain/common/localtime"
	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/initialize/es"
	"fobrain/initialize/mysql"
	"fobrain/models/mysql/workbench"
	"fobrain/pkg/utils"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/olivere/elastic/v7"
	"github.com/pkg/errors"
	"github.com/stretchr/testify/assert"
)

var AssetSecurityCoverageFieldsArray = []string{"id", "created_at", "updated_at", "dimensions", "metrics_rate", "is_show", "total_count", "effective_count", "exempt_count", "date_hour"}
var AssetExemptRecordFieldsArray = []string{"id", "created_at", "updated_at", "dimensions_id", "exempt_condition", "exempt_type", "op_id", "remark"}

func TestWorkbenchService_AssetSecurityCoverage(t *testing.T) {
	t.Run("正常", func(t *testing.T) {
		// 设置 Mock ES
		mockEs := testcommon.NewMockServer()
		defer mockEs.Close()

		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()

		sql := "SELECT * FROM `workbench_asset_security_coverage`"
		mockDb.ExpectQuery("SELECT count(*) FROM `workbench_asset_security_coverage`").
			WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(2))

		mockDb.ExpectQuery(sql).
			WillReturnRows(mockDb.NewRows(AssetSecurityCoverageFieldsArray).
				AddRow(1, localtime.Parse(utils.DateTimeLayout, "2024-10-12 16:32:31"), localtime.Parse(utils.DateTimeLayout, "2024-10-12 16:32:31"),
					0, 3224, true, 3435, 2323, 123, "15").
				AddRow(2, localtime.Parse(utils.DateTimeLayout, "2024-10-11 16:32:31"), localtime.Parse(utils.DateTimeLayout, "2024-10-11 16:32:31"),
					1, 13224, true, 13435, 12323, 1123, "14"))

		mockDb.ExpectQuery("SELECT count(*) FROM `workbench_asset_exempt_record`").
			WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(2))
		mockDb.ExpectQuery("SELECT * FROM `workbench_asset_exempt_record`").
			WillReturnRows(mockDb.NewRows(AssetExemptRecordFieldsArray).
				AddRow(1, localtime.Parse(utils.DateTimeLayout, "2024-10-12 16:32:31"), localtime.Parse(utils.DateTimeLayout, "2024-10-12 16:32:31"),
					0, "10.10.111.111", 0, 0, "").
				AddRow(2, localtime.Parse(utils.DateTimeLayout, "2024-10-11 16:32:31"), localtime.Parse(utils.DateTimeLayout, "2024-10-11 16:32:31"),
					1, "keyword='wps'", 1, 0, ""))

		patches := gomonkey.ApplyFuncReturn(getSourceTypeMap, map[uint64][]uint64{
			1: {5},
			2: {3},
			3: {11},
			4: {1},
			5: {10},
		}, nil)
		patches.ApplyFuncReturn(es.GetCount, int64(22), nil)
		defer patches.Reset()

		_, err := NewWorkbenchService().AssetSecurityCoverage(false)
		assert.NotNil(t, err)
	})
}

func TestWorkbenchService_AssetStatisticalCoverage(t *testing.T) {
	t.Run("正常", func(t *testing.T) {
		time.Sleep(time.Second)
		defer gomonkey.ApplyFuncReturn(getSourceTypeMap, map[uint64][]uint64{
			1: {5},
			2: {3},
			3: {11},
			4: {1},
			5: {10},
		}, nil).Reset()
		time.Sleep(time.Second)
		defer gomonkey.ApplyFuncReturn(es.GetCount, int64(22), nil).Reset()
		got, err := NewWorkbenchService().AssetStatisticalCoverage(1, nil)
		if err != nil {
			t.Fatalf("advancedFilterQuery() error = %v", err)
		}
		t.Log(got)
		got, err = NewWorkbenchService().AssetStatisticalCoverage(6, nil)
		if err != nil {
			t.Fatalf("advancedFilterQuery() error = %v", err)
		}
		t.Log(got)
	})
}

func Test_advancedFilterQuery1(t *testing.T) {
	t.Run("正常", func(t *testing.T) {
		records := []*workbench.AssetExemptRecord{
			{
				BaseModel: mysql.BaseModel{
					Id: 1,
				},
				DimensionsId:    1,
				ExemptCondition: "10.22.23.11",
				ExemptType:      1,
				NetworkType:     1,
			},
		}
		time.Sleep(time.Second)
		defer gomonkey.ApplyFuncReturn(es.GetCount, int64(22), nil).Reset()
		got, err := advancedFilterQuery(records)
		if err != nil {
			t.Fatalf("advancedFilterQuery() error = %v", err)
		}
		if got != 22 {
			t.Fatalf("advancedFilterQuery:%v, want %v", got, 22)
		}
	})
}

// func Test_getSourceTypeMap(t *testing.T) {
// 	t.Run("正常", func(t *testing.T) {
// 		mockDb := testcommon.InitSqlMock()
// 		mockDb.ExpectQuery("SELECT * FROM `data_source_types` WHERE `name` IN (?,?,?,?,?)").
// 			WithArgs("CMDB", "堡垒机", "防火墙", "主机安全", "资产扫描").
// 			WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).
// 				AddRow(2, "资产扫描").AddRow(3, "CMDB").AddRow(4, "主机安全").AddRow(7, "堡垒机").AddRow(8, "防火墙"))

// 		mockDb.ExpectQuery("SELECT * FROM `data_source_type_map` WHERE source_type_id IN (?,?,?,?,?)").
// 			WithArgs(2, 3, 4, 7, 8).
// 			WillReturnRows(sqlmock.NewRows([]string{"source_id", "source_type_id"}).
// 				AddRow(1, 2).AddRow(5, 3).AddRow(3, 4).AddRow(11, 7).AddRow(10, 8))
// 		got, err := getSourceTypeMap()
// 		if err != nil {
// 			t.Fatalf("getSourceTypeMap() error = %v", err)
// 		}

// 		t.Log(got)
// 	})
// }

func Test_getSourceTypeMap(t *testing.T) {
	mockDb := testcommon.InitSqlMock()
	defer mockDb.Close()
	mockDb.ExpectQuery("SELECT * FROM `data_source_types` WHERE `name` IN (?,?,?,?,?)").
		WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg()).
		WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).
			AddRow(3, "CMDB").
			AddRow(7, "堡垒机").
			AddRow(8, "防火墙").
			AddRow(4, "主机安全").
			AddRow(2, "资产扫描"))

	mockDb.ExpectQuery("SELECT * FROM `data_source_type_map` WHERE source_type_id IN (?,?,?,?,?)").
		WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg()).
		WillReturnRows(sqlmock.NewRows([]string{"source_type_id", "source_id"}).
			AddRow(3, 5).
			AddRow(7, 11).
			AddRow(8, 9).
			AddRow(4, 3).
			AddRow(2, 1))

	expect := map[uint64][]uint64{
		1: {5},
		2: {3},
		3: {11},
		4: {1},
		5: {9},
	}
	got, err := getSourceTypeMap()
	assert.Nil(t, err)
	assert.Equal(t, expect, got)
	t.Log(got)
}

// Test_calcWAF 测试WAF计算函数
func Test_calcWAF(t *testing.T) {
	s := NewWorkbenchService()

	// 设置Mock ES
	mockEs := testcommon.NewMockServer()
	defer mockEs.Close()

	t.Run("正常计算WAF覆盖度", func(t *testing.T) {
		// Mock ES计数查询
		mockEs.Register("/asset/_count", map[string]interface{}{
			"count": 30, // WAF覆盖的资产数量
		})

		// Mock数据库连接
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()

		// Mock系统配置查询
		mockDb.ExpectQuery("SELECT.*system_configs.*").
			WillReturnRows(mockDb.NewRows([]string{"value"}).
				AddRow(`{"id": 1}`))

		// Mock网络映射查询
		mockDb.ExpectQuery("SELECT.*net_mapping.*").
			WillReturnRows(mockDb.NewRows([]string{"from_ip", "to_ip"}).
				AddRow("***********", "********").
				AddRow("***********", "********"))

		// 创建Mock ES搜索结果
		searchResult := &elastic.SearchResult{
			Hits: &elastic.SearchHits{
				Hits: []*elastic.SearchHit{},
			},
		}

		result := s.calcWAF([]interface{}{1, 2}, searchResult, 100)

		// 验证结果是个有效的百分比
		assert.GreaterOrEqual(t, result, int64(0))
		assert.LessOrEqual(t, result, int64(10000))
	})

	t.Run("配置不存在的情况", func(t *testing.T) {
		// Mock数据库连接
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()

		// Mock系统配置查询返回空
		mockDb.ExpectQuery("SELECT.*system_configs.*").
			WillReturnError(errors.New("config not found"))

		searchResult := &elastic.SearchResult{
			Hits: &elastic.SearchHits{
				Hits: []*elastic.SearchHit{},
			},
		}

		result := s.calcWAF([]interface{}{1, 2}, searchResult, 100)

		// 配置不存在应该返回0
		assert.Equal(t, int64(0), result)
	})
}

// Test_refreshIndex 测试索引刷新函数
func Test_refreshIndex(t *testing.T) {
	// 设置Mock ES
	mockEs := testcommon.NewMockServer()
	defer mockEs.Close()

	t.Run("正常刷新索引", func(t *testing.T) {
		// Mock ES刷新操作
		mockEs.Register("/process_asset,process_poc,poc,asset,device,compliance_monitor_task_records/_refresh", map[string]interface{}{
			"_shards": map[string]interface{}{
				"total": 5, "successful": 5, "failed": 0,
			},
		})

		// 调用函数，确保不会panic
		assert.NotPanics(t, func() {
			refreshIndex()
		})
	})

	t.Run("刷新索引失败", func(t *testing.T) {
		// 不注册路由，导致404错误
		mockEs2 := testcommon.NewMockServer()
		defer mockEs2.Close()

		// 即使失败也不应该panic
		assert.NotPanics(t, func() {
			refreshIndex()
		})
	})
}

// TestAdvancedFilterQuery_AdditionalCases 测试高级筛选查询的额外情况
func TestAdvancedFilterQuery_AdditionalCases(t *testing.T) {
	// 设置Mock ES
	mockEs := testcommon.NewMockServer()
	defer mockEs.Close()

	t.Run("单个资产筛选", func(t *testing.T) {
		// Mock ES计数查询
		mockEs.Register("/asset/_count", map[string]interface{}{
			"count": 5, // 匹配的资产数量
		})

		records := []*workbench.AssetExemptRecord{
			{
				ExemptType:      workbench.ExemptTypeSingleAsset,
				ExemptCondition: "***********",
				NetworkType:     1,
			},
			{
				ExemptType:      workbench.ExemptTypeSingleAsset,
				ExemptCondition: "***********",
				NetworkType:     1,
			},
		}

		count, err := advancedFilterQuery(records)

		assert.NoError(t, err)
		assert.Equal(t, int64(5), count)
	})

	t.Run("高级筛选条件", func(t *testing.T) {
		// Mock ES计数查询
		mockEs.Register("/asset/_count", map[string]interface{}{
			"count": 3, // 匹配的资产数量
		})

		// 构建高级筛选条件JSON
		advancedCondition := `{"keyword":"test","ids":[]}`

		records := []*workbench.AssetExemptRecord{
			{
				ExemptType:      workbench.ExemptTypeAdvanced,
				ExemptCondition: advancedCondition,
				NetworkType:     1,
			},
		}

		// Mock CreateBoolQuery函数
		patches := gomonkey.ApplyFunc(asset.CreateBoolQuery, func(keyword string, networkType int, recycleBin int, ids []string, params interface{}) (*elastic.BoolQuery, error) {
			return elastic.NewBoolQuery().Must(elastic.NewTermQuery("ip", "***********")), nil
		})
		defer patches.Reset()

		count, err := advancedFilterQuery(records)

		assert.NoError(t, err)
		assert.Equal(t, int64(3), count)
	})

	t.Run("空记录列表", func(t *testing.T) {
		// Mock ES计数查询
		mockEs.Register("/asset/_count", map[string]interface{}{
			"count": 0,
		})

		records := []*workbench.AssetExemptRecord{}

		count, err := advancedFilterQuery(records)

		assert.NoError(t, err)
		assert.Equal(t, int64(0), count)
	})
}
