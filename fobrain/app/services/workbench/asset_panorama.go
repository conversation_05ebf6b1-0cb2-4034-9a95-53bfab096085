package workbench

import (
	"fmt"
	"fobrain/fobrain/app/repository/asset"
	"fobrain/models/elastic/assets"
	"fobrain/models/mysql/network_areas"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/olivere/elastic/v7"
)

type Service struct {
}

func NewService() *Service {
	return &Service{}
}

// AssetPanoramaResponse 资产全景响应结构
type AssetPanoramaResponse struct {
	Zones []*ZoneData `json:"zones"`
}

// ZoneData 区域数据
type ZoneData struct {
	Id       string        `json:"id"`        // 区域ID
	AreaName string        `json:"area_name"` // 区域名称
	Systems  []*SystemData `json:"systems"`   // 业务系统列表
	Stats    *StatsData    `json:"stats"`     // 统计数据
}

// SystemData 业务系统数据
type SystemData struct {
	SystemName string `json:"system_name"` // 业务系统名称
	IpCount    int64  `json:"ip_count"`    // IP数量
}

// StatsData 统计数据
type StatsData struct {
	OrphanAssets int64  `json:"orphanAssets"` // 无主资产数量
	OrphanRate   string `json:"orphanRate"`   // 无主资产占比
}

func (s *Service) AssetPanorama(ctx *gin.Context) (any, error) {
	// 第一个查询：获取每个区域的业务系统及资产数量
	config := asset.AssetTopConfig{
		Field:           "area",
		Size:            1000,
		NestedPath:      "", // 恢复为空，因为area是根级别字段
		NestedSubField:  "business.system",
		IsDualNested:    true, // 恢复为true
		Keyword:         "",
		AdditionQueries: []elastic.Query{},
		Must:            false,
		IncludeMissing:  false,
		IndexName:       assets.NewAssets().IndexName(),
	}
	businessData, err := asset.AssetTopProcess(config)
	if err != nil {
		return nil, err
	}

	// 第二个查询：获取每个区域无主资产
	ownershipData := make(map[string]float64)
	for _, item := range businessData {
		areaId := item.FieldValue
		// 使用公共函数获取业务无主资产查询条件
		boolQuery := elastic.NewBoolQuery()
		// 将字符串areaId转换为整数
		areaIdInt, err := strconv.Atoi(areaId)
		if err != nil {
			return nil, fmt.Errorf("区域ID转换失败: %v", err)
		}
		boolQuery.Must(elastic.NewTermQuery("area", areaIdInt)) // area是数字类型

		count, _, err := NewWorkbenchService().OwnerlessAssetPercentage("business", boolQuery)
		if err != nil {
			return nil, err
		}
		noBusinessPersonCount := 0
		noMatchBusinessPersonCount := 0
		numerator := 0
		for _, v := range count {
			if v.FieldValue == "noBusinessPerson" {
				noBusinessPersonCount = int(v.FieldCount)
				numerator += int(v.InternalExternalSumCount)
			}
			if v.FieldValue == "noMatchBusinessPerson" {
				noMatchBusinessPersonCount = int(v.FieldCount)
				numerator += int(v.InternalExternalSumCount)
			}

		}
		//计算无主资产
		ownershipData[areaId] = float64(noBusinessPersonCount + noMatchBusinessPersonCount)
		ownershipData[areaId+"_count"] = float64(numerator)
	}

	// 组合数据
	result := s.combineAssetPanoramaData(businessData, ownershipData)

	return result, nil
}

// combineAssetPanoramaData 组合两个查询的数据
func (s *Service) combineAssetPanoramaData(businessData []*asset.AssetTopResult, areaOwnershipMap map[string]float64) *AssetPanoramaResponse {
	// 获取所有区域信息
	allAreas := network_areas.AllNetworkArea()

	// 构建区域有主资产映射

	var zones []*ZoneData

	// 处理业务系统数据
	for _, item := range businessData {
		areaId := item.FieldValue
		// 获取区域名称
		areaName := allAreas[parseAreaIdToUint64(areaId)]
		if areaName == "" {
			areaName = fmt.Sprintf("区域%s", areaId)
		}
		// 构建业务系统列表
		var systems []*SystemData
		for _, child := range item.Children {
			systemName := child.FieldValue
			if systemName == "" {
				systemName = "无业务系统"
			}
			systems = append(systems, &SystemData{
				SystemName: systemName,
				IpCount:    child.FieldCount,
			})
		}

		zoneData := &ZoneData{
			Id:       areaId,
			AreaName: areaName,
			Systems:  systems,
			Stats: &StatsData{
				OrphanAssets: int64(areaOwnershipMap[areaId+"_count"]),
				OrphanRate:   fmt.Sprintf("%.2f%%", areaOwnershipMap[areaId]/100),
			},
		}

		zones = append(zones, zoneData)
	}

	return &AssetPanoramaResponse{Zones: zones}
}

// parseAreaIdToUint64 将区域ID字符串转换为uint64
func parseAreaIdToUint64(areaIdStr string) uint64 {
	if areaIdStr == "" {
		return 0
	}
	areaId, err := strconv.ParseUint(areaIdStr, 10, 64)
	if err != nil {
		return 0
	}
	return areaId
}
