package workbench

import (
	"context"
	"encoding/json"
	"fmt"
	"fobrain/fobrain/app/repository/asset"
	"fobrain/fobrain/app/request/asset_center"
	"fobrain/fobrain/common/request"
	"fobrain/initialize/es"
	"fobrain/initialize/mysql"
	"fobrain/models/elastic/assets"
	"fobrain/models/elastic/compliance_monitor"
	esmodel "fobrain/models/elastic/device"
	"fobrain/models/elastic/poc"
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/net_mapping"
	"fobrain/models/mysql/system_configs"
	"fobrain/models/mysql/workbench"
	"sync"
	"time"

	"git.gobies.org/caasm/fobrain-components/utils"

	"github.com/olivere/elastic/v7"
	"github.com/pkg/errors"
	"go-micro.dev/v4/logger"
)

type AssetSecurityCoverageInfo struct {
	CMDBRegistrationRate        int64 `json:"cmdb_registration_rate"`        // CMDB登记率
	HostSecurityCoverageRate    int64 `json:"host_security_coverage_rate"`   // 主机安全覆盖率
	HoneypotCoverageRate        int64 `json:"honeypot_coverage_rate"`        // 蜜罐机覆盖率
	ProactiveDetectionRate      int64 `json:"proactive_detection_rate"`      // 主动探测覆盖率
	WAFCoverageRate             int64 `json:"waf_coverage_rate"`             // WAF覆盖率
	BusinessSystemOwnerRate     int64 `json:"business_system_owner_rate"`    // 业务系统负责人覆盖率
	OperationResponsibilityRate int64 `json:"operation_responsibility_rate"` // 运维负责人覆盖率
}

type AssetSecurityCoverageItem struct {
	ID   uint64 `json:"id"`
	Rate int64  `json:"rate"`
}

// 资产安全覆盖度
func (s *WorkbenchService) AssetSecurityCoverage(refresh bool) ([]*AssetSecurityCoverageItem, error) {
	coverList, _, err := workbench.NewAssetSecurityCoverageModel().List(0, 0, mysql.WithWhere("is_show = 1"))
	if err != nil {
		return nil, err
	}
	// 判断是否需要更新，1s
	var resultMap = make(map[uint64]*AssetSecurityCoverageItem)
	var updateIds = make([]uint64, 0, len(coverList))
	for _, v := range coverList {
		resultMap[v.Id] = &AssetSecurityCoverageItem{
			ID:   v.Id,
			Rate: v.MetricsRate,
		}
		if time.Now().After(time.Time(v.UpdatedAt).Add(time.Second*1)) || refresh {
			updateIds = append(updateIds, v.Id)
		}
	}
	refreshIndex()

	sourceTypeMap, err := getSourceTypeMap()
	if err != nil {
		return nil, err
	}

	allquery := elastic.NewBoolQuery().MustNot(elastic.NewExistsQuery("deleted_at"), elastic.NewExistsQuery("purged_at"))
	assetAllCount, derr := es.GetCount(assets.NewAssets().IndexName(), allquery)
	if derr != nil {
		return nil, derr
	}

	wafAllCount, werr := es.GetCount(assets.NewAssets().IndexName(), allquery.Must(elastic.NewTermQuery("network_type", asset.NetworkTypeExternal)))
	if werr != nil {
		return nil, werr
	}

	var sourceIds = make([]interface{}, 0)
	for _, id := range updateIds {
		var calcExempt = true
		subQuery := elastic.NewBoolQuery().MustNot(elastic.NewExistsQuery("deleted_at"), elastic.NewExistsQuery("purged_at"))
		switch id {
		case workbench.CmdbDimensionsID, workbench.BastionHostDimensionsID,
			workbench.HostSecurityDimensionsID, workbench.ActiveDetectDimensionsID:
			{
				if val, ok := sourceTypeMap[id]; ok {
					var ids []interface{}
					for _, id := range val {
						ids = append(ids, id)
					}
					subQuery.Must(elastic.NewTermsQuery("all_source_ids", ids...))
				} else {
					continue
				}
			}
		case workbench.WafDimensionsID:
			{
				if val, ok := sourceTypeMap[id]; ok {
					for _, id := range val {
						sourceIds = append(sourceIds, id)
					}
				} else {
					continue
				}
			}
		case workbench.BusinessDimensionsID:
			{
				subQuery.Must(elastic.NewNestedQuery(
					"business.person_base", // 嵌套路径
					elastic.NewBoolQuery().Must(
						elastic.NewExistsQuery("business.person_base.name"), // 确保 business.owner_id 存在
					).MustNot(
						elastic.NewTermQuery("business.person_base.name", ""), // 确保 business.owner_id 不为空
					),
				))
			}
		case workbench.OperDimensionsID:
			{
				subQuery.Must(elastic.NewExistsQuery("oper_staff_ids")).MustNot(elastic.NewTermQuery("oper_staff_ids", ""))
			}
		default:
			calcExempt = false
			logger.Warn("资产安全覆盖度-未配置的维度计算方法", id)
		}

		exemptCondition := elastic.NewNestedQuery("exempt_record_ids",
			elastic.NewBoolQuery().
				Must(elastic.NewMatchQuery("exempt_record_ids.key", id)).
				Must(elastic.NewScriptQuery(elastic.NewScript(`
					doc['exempt_record_ids.values'].length > 0
				`))),
		)

		if calcExempt {
			subQuery.MustNot(exemptCondition)
		}

		var molecular int64
		if id != workbench.WafDimensionsID {
			molecular, err = es.GetCount(assets.NewAssets().IndexName(), subQuery)
			if err != nil {
				logger.Warn("资产安全覆盖度-统计分子数量失败", id, err)
				continue
			}
		}

		var exemptCount int64
		var searchResult *elastic.SearchResult
		if calcExempt {
			//构建豁免查询
			query := elastic.NewBoolQuery().
				//Must(elastic.NewExistsQuery("exempt_record_ids")). // 排除 exempt_record_ids 为 null 的文档
				Must(exemptCondition)

			// 执行查询
			searchResult, err = es.GetEsClient().Search().
				Index(assets.NewAssets().IndexName()).
				Query(query).
				Do(context.Background())

			if err != nil {
				return nil, err
			}
			exemptCount = searchResult.TotalHits()
		}

		var rate int64
		if id == workbench.WafDimensionsID {
			rate = s.calcWAF(sourceIds, searchResult, wafAllCount-exemptCount)
		} else {
			rate = utils.CalculatePercentageRate(molecular, assetAllCount-exemptCount)
		}

		logger.Debugf("----id:%d,desc:%s,molecular:%d,assetAllCount:%d,exemptCount:%d,rate:%d\n", id, workbench.DimensionsMap[int64(id)], molecular, assetAllCount, exemptCount, rate)
		if rate > 10000 {
			rate = 10000
		}
		if rate < 0 {
			rate = 0
		}
		if _, ok := resultMap[id]; ok {
			resultMap[id].Rate = rate
		}
		workbench.NewAssetSecurityCoverageModel().Save(int64(id), true, map[string]interface{}{
			"total_count":     assetAllCount,
			"effective_count": molecular,
			"exempt_count":    exemptCount,
			"metrics_rate":    rate,
		})
	}

	var result = make([]*AssetSecurityCoverageItem, 0, len(coverList))
	for _, v := range resultMap {
		result = append(result, v)
	}

	return result, nil
}

// waf上报的都是内网
func (s *WorkbenchService) calcWAF(sourceIds []interface{}, exemptSearchResult *elastic.SearchResult, denominator int64) int64 {
	conf, _ := system_configs.NewSystemConfigs().GetWAFCoverageConf()
	if conf == nil {
		return 0
	}

	netMapping, err := net_mapping.NewNetMappingModel().ListByOpt(mysql.WithWhere("from_area = ? OR to_area = ?", conf.Id, conf.Id))
	if err != nil || len(netMapping) == 0 {
		return 0
	}
	var exemptIpMap = make(map[string]struct{})
	for _, hit := range exemptSearchResult.Hits.Hits {
		asset := assets.Assets{}
		json.Unmarshal(hit.Source, &asset)
		if asset.Ip != "" {
			exemptIpMap[asset.Ip] = struct{}{}
		}
	}

	var mappingIps []interface{}
	for _, v := range netMapping {
		if _, ok := exemptIpMap[v.FromIp]; ok {
			continue
		}
		if _, ok := exemptIpMap[v.ToIp]; ok {
			continue
		}
		mappingIps = append(mappingIps, v.FromIp, v.ToIp)
	}

	query := assets.NewAssets().GenQueryNoDeletedAndPurged().Must(
		elastic.NewTermsQuery("ip", mappingIps...),
		elastic.NewTermsQuery("all_source_ids", sourceIds...),
		//elastic.NewTermQuery("network_type", asset.NetworkTypeInternal),
	)
	molecular, err := es.GetCount(assets.NewAssets().IndexName(), query)
	if err != nil {
		return 0
	}

	return utils.CalculatePercentageRate(molecular, denominator)
}

func (s *WorkbenchService) AssetStatisticalCoverage(coverageId uint64, additionQueries ...elastic.Query) (*AssetSecurityCoverageItem, error) {
	sourceTypeMap, err := getSourceTypeMap()
	if err != nil {
		return nil, err
	}
	allquery := elastic.NewBoolQuery().MustNot(elastic.NewExistsQuery("deleted_at"), elastic.NewExistsQuery("purged_at"))
	for _, q := range additionQueries {
		allquery = allquery.Must(q)
	}
	assetAllCount, err := es.GetCount(assets.NewAssets().IndexName(), allquery)
	if err != nil {
		return nil, err
	}

	subQuery := elastic.NewBoolQuery().MustNot(elastic.NewExistsQuery("deleted_at"), elastic.NewExistsQuery("purged_at"))
	for _, q := range additionQueries {
		subQuery = subQuery.Must(q)
	}
	switch coverageId {
	case workbench.CmdbDimensionsID, workbench.BastionHostDimensionsID,
		workbench.WafDimensionsID, workbench.HostSecurityDimensionsID,
		workbench.ActiveDetectDimensionsID:
		{
			if val, ok := sourceTypeMap[coverageId]; ok {
				var ids []interface{}
				for _, id := range val {
					ids = append(ids, id)
				}
				subQuery.Must(elastic.NewTermsQuery("source_ids", ids...))
			}
		}
	case workbench.BusinessDimensionsID:
		{
			subQuery.Must(elastic.NewNestedQuery(
				"business.person_base", // 嵌套路径
				elastic.NewBoolQuery().Must(
					elastic.NewExistsQuery("business.person_base.name"), // 确保 business.owner_id 存在
				).MustNot(
					elastic.NewTermQuery("business.person_base.name", ""), // 确保 business.owner_id 不为空
				),
			))
		}
	case workbench.OperDimensionsID:
		{
			subQuery.Must(elastic.NewExistsQuery("oper"))
		}
	default:
		logger.Warn("资产安全覆盖度-未配置的维度计算方法", coverageId)
	}

	molecular, err := es.GetCount(assets.NewAssets().IndexName(), subQuery)
	if err != nil {
		logger.Warn("资产安全覆盖度-统计分子数量失败", coverageId, err)
	}

	rate := utils.CalculatePercentageRate(molecular, assetAllCount)
	if rate > 10000 {
		rate = 10000
	}
	if rate < 0 {
		rate = 0
	}
	return &AssetSecurityCoverageItem{
		ID:   coverageId,
		Rate: rate,
	}, nil
}

func advancedFilterQuery(records []*workbench.AssetExemptRecord) (int64, error) {
	var singleArray = make([]interface{}, 0, len(records))
	combinedQuery := elastic.NewBoolQuery()
	for _, v := range records {
		if v.ExemptType == workbench.ExemptTypeSingleAsset {
			singleArray = append(singleArray, v.ExemptCondition)
			continue
		}
		var req asset_center.AssetRequest
		if err := json.Unmarshal([]byte(v.ExemptCondition), &req); err != nil {
			logger.Errorf("NetworkTypeCoverage解析高级筛选条件失败,err:%v,param:%v", err, v.ExemptCondition)
			continue
		}
		params := &asset_center.InternalAssetRequest{
			PageRequest: request.PageRequest{
				Page:    1,
				PerPage: 20,
			},
			AssetRequest: req,
		}
		advancedQuery, err := asset.CreateBoolQuery(req.Keyword, int(v.NetworkType), asset.NotIsRecycleBin, req.Ids, params)
		if err != nil {
			return 0, err
		}

		combinedQuery.Should(advancedQuery)
	}
	combinedQuery.Should(elastic.NewBoolQuery().Must(elastic.NewTermsQuery("ip", singleArray...)))

	advancedCount, err := es.GetCount(assets.NewAssets().IndexName(), combinedQuery)
	if err != nil {
		return 0, err
	}
	return advancedCount, nil
}

func getSourceTypeMap() (map[uint64][]uint64, error) {
	var resultMap = make(map[uint64][]uint64)
	// 获取数据源类型列表
	dataSourceTypeList, err := data_source.NewSourceTypeModel().All(mysql.WithValuesIn("name", []string{"CMDB", "堡垒机", "防火墙", "主机安全", "资产扫描"}))
	if err != nil {
		return resultMap, errors.WithMessage(err, "查询数据源类型错误")
	}
	// 收集数据源类型ID列表
	dataSourceTypeIdList := make([]uint64, 0)
	for _, sourceType := range dataSourceTypeList {
		dataSourceTypeIdList = append(dataSourceTypeIdList, sourceType.Id)
	}
	// 获取数据源类型映射表，得到数据源类型ID与数据源ID的映射关系
	dataSourceMapList, err := data_source.NewSourceTypeMapModel().GetByTypeId(dataSourceTypeIdList)
	if err != nil {
		return resultMap, errors.WithMessage(err, "查询数据源类型映射表错误")
	}
	// 将数据源类型ID与数据源ID的映射关系转换为map
	dataSourceMapWithTypeId := make(map[uint64][]uint64)
	for _, typeMap := range dataSourceMapList {
		if _, ok := dataSourceMapWithTypeId[typeMap.SourceTypeId]; !ok {
			dataSourceMapWithTypeId[typeMap.SourceTypeId] = make([]uint64, 0)
		}
		dataSourceMapWithTypeId[typeMap.SourceTypeId] = append(dataSourceMapWithTypeId[typeMap.SourceTypeId], typeMap.SourceId)
	}
	// 遍历数据源类型列表，判断对应数据源类型的数据源，是否包含当前数据的数据源ID
	for _, sourceType := range dataSourceTypeList {
		switch sourceType.Name {
		case "CMDB":
			val, ok := dataSourceMapWithTypeId[sourceType.Id]
			if ok {
				resultMap[workbench.CmdbDimensionsID] = val
			}
		case "堡垒机":
			val, ok := dataSourceMapWithTypeId[sourceType.Id]
			if ok {
				resultMap[workbench.BastionHostDimensionsID] = val
			}
		case "防火墙":
			val, ok := dataSourceMapWithTypeId[sourceType.Id]
			if ok {
				resultMap[workbench.WafDimensionsID] = val
			}
		case "主机安全":
			val, ok := dataSourceMapWithTypeId[sourceType.Id]
			if ok {
				resultMap[workbench.HostSecurityDimensionsID] = val
			}
		case "资产扫描":
			val, ok := dataSourceMapWithTypeId[sourceType.Id]
			if ok {
				resultMap[workbench.ActiveDetectDimensionsID] = val
			}
		}
	}
	return resultMap, nil
}

var refreshIndexArray = []string{
	assets.NewProcessAssetsModel().IndexName(),
	poc.NewProcessPocModel().IndexName(),
	poc.NewPoc().IndexName(),
	assets.NewAssets().IndexName(),
	esmodel.NewDeviceModel().IndexName(),
	compliance_monitor.NewComplianceMonitorTaskRecords().IndexName(),
}
var (
	lastRefreshTime time.Time
	refreshMutex    sync.Mutex
)

// 更新asset、poc、device等索引
func refreshIndex() error {
	refreshMutex.Lock()
	defer refreshMutex.Unlock()
	now := time.Now()
	if now.Sub(lastRefreshTime) < time.Second {
		// 如果不足1秒，跳过执行
		logger.Infof("RefreshIndex skipped, called too frequently")
		return nil
	}

	ctx := context.Background()
	_, err := es.GetEsClient().Refresh(refreshIndexArray...).Do(ctx)
	if err != nil {
		return fmt.Errorf("failed to refresh index [%v]: %v", refreshIndexArray, err)
	}
	lastRefreshTime = now

	return nil
}
