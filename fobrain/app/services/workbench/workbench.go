package workbench

import (
	"encoding/json"
	"fmt"
	"fobrain/fobrain/common/request"
	filtrate "fobrain/models/elastic"
	"fobrain/models/elastic/business_system"
	"sort"
	"strings"
	"sync"
	"time"

	"fobrain/fobrain/app/repository/asset"
	"fobrain/fobrain/app/request/asset_center"
	"fobrain/fobrain/common/localtime"
	"fobrain/initialize/es"
	"fobrain/initialize/mysql"
	"fobrain/models/elastic/assets"
	"fobrain/models/elastic/compliance_monitor"
	esmodel "fobrain/models/elastic/device"
	"fobrain/models/elastic/poc"
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/data_sync_child_task"
	"fobrain/models/mysql/proactive_task_node_relations"
	"fobrain/models/mysql/user_staff"
	"fobrain/models/mysql/workbench"
	"fobrain/pkg/utils"

	"github.com/olivere/elastic/v7"
	"github.com/pkg/errors"
	"go-micro.dev/v4/logger"
)

type WorkbenchService struct{}

var workbenchOnce sync.Once

func NewWorkbenchService() *WorkbenchService {
	return &WorkbenchService{}
}

var isTest = false

func init() {
	if isTest {
		return
	}
	workbenchOnce.Do(func() {
		NewWorkbenchService().workbenchTicker()
	})
}

type TickerTaskModel struct {
	fn       func()
	interval time.Duration
}

func (s *WorkbenchService) workbenchTicker() {
	// 定义任务列表和执行间隔
	tasks := []TickerTaskModel{
		{s.tickerExecDataProcessOverview, 10 * time.Second},
		{s.tickerRecordNoRepairedVuln, 10 * time.Second},
		{s.tickerRecordAssetCount, 0}, // 最后一个任务不需要延迟
	}

	// 立即执行首次任务
	go s.executeTasks(tasks)

	// 创建定时器
	ticker := time.NewTicker(1 * time.Hour)
	go func() {
		for {
			select {
			case <-ticker.C:
				go s.executeTasks(tasks)
			}
		}
	}()

}

// executeTasks 按顺序执行任务，带有指定的间隔
func (s *WorkbenchService) executeTasks(tasks []TickerTaskModel) {
	for i, task := range tasks {
		if i > 0 {
			time.Sleep(tasks[i-1].interval) // 使用前一个任务的间隔
		}
		go task.fn()
	}
}

type HeadStatsInfo struct {
	asset.AssetsIPStatsInfo
	poc.PocStatsInfo
	asset.RecycleAssetsIPStatsInfo
	ProbeNormalCount   int64     `json:"probe_normal_count"`
	ProbeAbnormalCount int64     `json:"probe_abnormal_count"`
	DeviceCount        int64     `json:"device_count"`
	BusinessCount      int64     `json:"business_count"`
	ComplianceCount    int64     `json:"compliance_count"`
	UpdateTime         time.Time `json:"-"`
	UpdateTimeFormat   string    `json:"update_time"`
	RecycleDeviceCount int64     `json:"recycle_device_count"`

	BusinessStatusCount map[int64]int64 `json:"business_status_count"`
	DomainStatsInfo
}

type DomainStatsInfo struct {
	InternalDomainCount int64 `json:"internal_domain_count"`
	ExternalDomainCount int64 `json:"external_domain_count"`
}

var rwmux sync.RWMutex
var statsCache = &HeadStatsInfo{
	UpdateTime: time.Now().Add(-3 * time.Second),
}

func (s *WorkbenchService) readCache(refresh bool) *HeadStatsInfo {
	rwmux.RLock()
	defer rwmux.RUnlock()
	if !refresh && statsCache != nil {
		if time.Now().Sub(statsCache.UpdateTime) < time.Second {
			return statsCache
		}
	}
	return nil
}

// 数据概览
func (s *WorkbenchService) HeadStats(refresh bool) (*HeadStatsInfo, error) {
	t := s.readCache(refresh)
	if t != nil {
		return t, nil
	}
	var result = HeadStatsInfo{
		UpdateTime:       time.Now(),
		UpdateTimeFormat: time.Now().Format(utils.DateTimeLayout),
	}
	refreshIndex()
	var err error
	var wg sync.WaitGroup
	var mux sync.Mutex
	wg.Add(9)

	go func() {
		// 探针
		defer wg.Done()
		var perr error
		normal, nerr := data_source.NewNodeModel().Total(mysql.WithWhere("status = ?", data_source.StatusNormal))
		if err != nil {
			perr = errors.WithMessagef(perr, "{get normal node count failed,err:%v}", nerr)
		}
		abnormal, aberr := data_source.NewNodeModel().Total(mysql.WithWhere("status != ?", data_source.StatusNormal))
		if err != nil {
			perr = errors.WithMessagef(perr, "{get abnormal node count failed,err:%v}", aberr)
		}
		mux.Lock()
		defer mux.Unlock()
		if perr != nil {
			err = errors.WithMessage(err, perr.Error())
		}
		result.ProbeNormalCount = normal
		result.ProbeAbnormalCount = abnormal
	}()
	go func() {
		// ip
		defer wg.Done()
		ipResult, iperr := asset.IPStats()

		mux.Lock()
		defer mux.Unlock()
		if iperr != nil {
			err = errors.WithMessagef(err, "{got ip count failed,err:%v}", iperr)
		}
		if ipResult != nil {
			result.AssetsIPStatsInfo = *ipResult
		}
	}()
	go func() {
		// 实体设备
		defer wg.Done()
		query := elastic.NewBoolQuery().MustNot(elastic.NewExistsQuery("deleted_at"))
		count, derr := es.GetCount(esmodel.NewDeviceModel().IndexName(), query)

		mux.Lock()
		defer mux.Unlock()
		if derr != nil {
			err = errors.WithMessagef(err, "{DeviceCount failed,err:%v}", derr)
		}
		result.DeviceCount = count
	}()
	go func() {
		defer wg.Done()
		// 业务系统
		mux.Lock()
		defer mux.Unlock()

		bsStatusMap, bserr := business_system.NewBusinessSystems().StatisticsStatus()
		if err != nil {
			err = errors.WithMessagef(err, "{DeviceCount failed,err:%v}", bserr)
		}
		var tcount int64
		for _, v := range bsStatusMap {
			tcount += v
		}
		result.BusinessStatusCount = bsStatusMap
		result.BusinessCount = tcount
	}()
	go func() {
		defer wg.Done()
		// 漏洞
		info, pocerr := poc.PocStats()
		mux.Lock()
		defer mux.Unlock()
		if pocerr != nil {
			err = errors.WithMessagef(err, "{PocStatsInfo failed,err:%v}", pocerr)
		}
		if info != nil {
			result.PocStatsInfo = *info
		}
	}()
	go func() {
		defer wg.Done()
		// 合规风险
		count, merr := es.GetCount(compliance_monitor.NewComplianceMonitorTaskRecords().IndexName(), elastic.NewBoolQuery())
		mux.Lock()
		defer mux.Unlock()
		if merr != nil {
			err = errors.WithMessagef(err, "{ComplianceCount failed,err:%v}", merr)
		}
		result.ComplianceCount = count
	}()
	// 回收站中的 IP 数据
	go func() {
		defer wg.Done()
		ipResult, iperr := asset.RecycleIpStats()

		mux.Lock()
		defer mux.Unlock()
		if iperr != nil {
			err = errors.WithMessagef(err, "{got recycle ip count failed,err:%v}", iperr)
		}
		if ipResult != nil {
			result.RecycleAssetsIPStatsInfo = *ipResult
		}
	}()

	// 回收站中的实体设备
	go func() {
		defer wg.Done()
		query := elastic.NewBoolQuery().Must(elastic.NewExistsQuery("deleted_at"))
		count, derr := es.GetCount(esmodel.NewDeviceModel().IndexName(), query)

		mux.Lock()
		defer mux.Unlock()
		if derr != nil {
			err = errors.WithMessagef(err, "{RecycleDeviceCount failed,err:%v}", derr)
		}
		result.RecycleDeviceCount = count
	}()

	go func() {
		defer wg.Done()
		mux.Lock()
		defer mux.Unlock()
		internalMap, _, err := asset.DomainStat(asset.NetworkTypeInternal)
		if err != nil {
			err = errors.WithMessagef(err, "{DomainStat NetworkTypeInternal failed,err:%v}", err)
		}
		externalMap, _, err := asset.DomainStat(asset.NetworkTypeExternal)
		if err != nil {
			err = errors.WithMessagef(err, "{DomainStat NetworkTypeExternal failed,err:%v}", err)
		}
		result.InternalDomainCount = int64(len(internalMap))
		result.ExternalDomainCount = int64(len(externalMap))

	}()

	wg.Wait()
	if err != nil {
		rwmux.RLock()
		defer rwmux.RUnlock()
		return statsCache, nil
	}
	rwmux.Lock()
	defer rwmux.Unlock()
	statsCache = &result
	return &result, nil
}

// 通知告警
func (s *WorkbenchService) AlarmList(page, size, msgType int, userId uint64) ([]*workbench.NotifyAlarmCenter, int64, error) {
	handleFunc := []mysql.HandleFunc{
		mysql.WithOrder("created_at DESC"),
	}
	if msgType == 3 {
		staffIds := user_staff.NewUserRoleModel().Get(userId)
		if len(staffIds) > 0 {
			handleFunc = append(handleFunc, mysql.WithWhere("staff_id in (?)", staffIds))
		} else {
			handleFunc = append(handleFunc, mysql.WithWhere("msg_type = ?", 99))
		}
	}

	if msgType != 99 { //全部不加条件
		handleFunc = append(handleFunc, mysql.WithWhere("msg_type = ?", msgType))
	}
	return workbench.NewNotifyAlarmCenter().GetList(page, size, handleFunc...)
}

func (s *WorkbenchService) AlarmSingle(id int) (*workbench.NotifyAlarmCenter, error) {
	return workbench.NewNotifyAlarmCenter().First(mysql.WithWhere("id = ?", id))
}

func (s *WorkbenchService) AlarmCreate(item *workbench.NotifyAlarmCenter) error {
	return workbench.NewNotifyAlarmCenter().Create(item)
}

func (s *WorkbenchService) AlarmRemindList(staffIds []string) ([]*workbench.NotifyAlarmCenter, int64, error) {
	handleFunc := []mysql.HandleFunc{
		mysql.WithOrder("created_at DESC"),
		mysql.WithWhere("msg_type = ? AND `read` = ?", workbench.MsgTypeRemind, false),
	}
	handleFunc = append(handleFunc, mysql.WithWhere("staff_id in (?)", staffIds))
	return workbench.NewNotifyAlarmCenter().GetAll(handleFunc...)
}

func (s *WorkbenchService) ReadAlarmRemind(userId uint64, ids []int) error {
	if len(ids) <= 0 {
		staffIds := user_staff.NewUserRoleModel().Get(userId)
		err := workbench.NewNotifyAlarmCenter().UpdateByStaffIds(staffIds, &workbench.NotifyAlarmCenter{Read: true})
		if err != nil {
			return err
		}
		return nil
	}

	errCount := 0
	for _, id := range ids {
		remind, err := workbench.NewNotifyAlarmCenter().First(mysql.WithWhere("id = ?", id))
		if err != nil || remind.MsgType != workbench.MsgTypeRemind {
			errCount++
			continue
		}
		remind.Read = true
		err = workbench.NewNotifyAlarmCenter().Update(remind)
		if err != nil {
			errCount++
		}
	}
	if errCount > 0 {
		return errors.New(fmt.Sprintf("更新%d条记录,%d条失败", len(ids), errCount))
	}
	return nil
}

type ProbeDetailInfo struct {
	NormalCount   int64                       `json:"normal_count"`
	AbnormalCount int64                       `json:"abnormal_count"`
	AbnormalList  []*data_source.NodeOverview `json:"abnormal_list"`
	UpdateTime    time.Time                   `json:"-"`
}

// 探针情况概览
func (s *WorkbenchService) ProbeOverview() (*ProbeDetailInfo, error) {
	normal, nerr := data_source.NewNodeModel().Total(mysql.WithWhere("status = ?", data_source.StatusNormal))
	if nerr != nil {
		return nil, errors.WithMessage(nerr, "get normal node count failed")
	}
	abnormalList, aberr := data_source.NewNodeModel().OverviewList(mysql.WithWhere("data_nodes.status != ?", data_source.StatusNormal))
	if aberr != nil {
		return nil, errors.WithMessage(aberr, "get abnormal node list failed")
	}
	sort.Slice(abnormalList, func(i, j int) bool {
		return abnormalList[i].ID < abnormalList[j].ID
	})
	result := &ProbeDetailInfo{
		NormalCount:   normal,
		AbnormalCount: int64(len(abnormalList)),
		AbnormalList:  abnormalList,
	}
	return result, nil
}

type ProbeAssetContributionInfo struct {
	Total      int64                          `json:"total"`     // 总资产数量
	ItemList   []*asset.ProbeContributionItem `json:"item_list"` // 探针列表
	UpdateTime time.Time                      `json:"-"`
}

// 探针资产贡献度
func (s *WorkbenchService) ProbeAssetContributionTop() (*ProbeAssetContributionInfo, error) {
	refreshIndex()
	total, err := es.GetCount(assets.NewAssets().IndexName(), assets.NewAssets().GenQueryNoDeletedAndPurged())
	if err != nil {
		return nil, err
	}

	list, err := asset.ProbeAssetContributionTop()
	if err != nil {
		return &ProbeAssetContributionInfo{
			Total:    total,
			ItemList: []*asset.ProbeContributionItem{},
		}, err
	}

	var info = &ProbeAssetContributionInfo{
		Total:    total,
		ItemList: list,
	}

	return info, nil
}

type WeeklyTaskOverview struct {
	SyncOverview []utils.TaskOverviewStats `json:"sync_overview"`
	ScanOverview []utils.TaskOverviewStats `json:"scan_overview"`
	AbnormalList []AbnormalTaskList        `json:"abnormal_list"`
	UpdateTime   time.Time                 `json:"-"`
}

type AbnormalTaskList struct {
	TaskType  string `json:"task_type"`
	NodeName  string `json:"node_name"`
	DataType  string `json:"data_type"`
	StartTime string `json:"start_time"`
}

// 近一周上报任务概率
func (s *WorkbenchService) WeeklyTaskOverview() (*WeeklyTaskOverview, error) {
	// 同步任务
	syncOverview, err := data_sync_child_task.NewDataSyncChildTaskModel().TaskOverview()
	if err != nil {
		return nil, errors.WithMessage(err, "统计同步任务失败")
	}
	syncAbnormalList, _, err := data_sync_child_task.NewDataSyncChildTaskModel().List(0, 0,
		mysql.WithSelect("source_id", "type", "start_at"),
		mysql.WithWhere("status = 4 and DATE(start_at) BETWEEN DATE_SUB(CURDATE(), INTERVAL 7 DAY) AND CURDATE()"))
	if err != nil {
		return nil, errors.WithMessage(err, "查找同步任务列表失败")
	}

	scanOverview, err := proactive_task_node_relations.NewProactiveTaskNodeRelations().TaskOverview()
	if err != nil {
		return nil, errors.WithMessage(err, "统计同步任务失败")
	}

	scanAbnormalList, _, err := proactive_task_node_relations.NewProactiveTaskNodeRelations().List(0, 0,
		mysql.WithSelect("source_id", "start_at"),
		mysql.WithWhere("state = 6 and DATE(start_at) BETWEEN DATE_SUB(CURDATE(), INTERVAL 7 DAY) AND CURDATE()"))
	if err != nil {
		return nil, errors.WithMessage(err, "查找扫描任务列表失败")
	}

	sourceList, _, err := data_source.NewSourceModel().ListOpt(0, 0)
	if err != nil {
		return nil, errors.WithMessage(err, "查找data_source失败")
	}

	var sourceIdMap = make(map[uint64]string)
	for _, source := range sourceList {
		sourceIdMap[source.Id] = source.Name
	}
	result := &WeeklyTaskOverview{
		ScanOverview: []utils.TaskOverviewStats{},
		SyncOverview: []utils.TaskOverviewStats{},
		AbnormalList: []AbnormalTaskList{},
	}

	if syncOverview != nil {
		result.SyncOverview = syncOverview
	}
	if scanOverview != nil {
		result.ScanOverview = scanOverview
	}
	for _, v := range syncAbnormalList {
		result.AbnormalList = append(result.AbnormalList, AbnormalTaskList{
			TaskType:  "同步",
			NodeName:  sourceIdMap[v.SourceId],
			DataType:  data_sync_child_task.DataSyncChildTaskTypeMap[v.Type],
			StartTime: v.StartAt.String(),
		})
	}
	for _, v := range scanAbnormalList {
		result.AbnormalList = append(result.AbnormalList, AbnormalTaskList{
			TaskType:  "扫描",
			NodeName:  sourceIdMap[uint64(v.SourceId)],
			DataType:  "默认",
			StartTime: v.StartAt.String(),
		})
	}
	return result, nil
}

type AssetsResult struct {
	asset.AssetsIPStatsInfo
	asset.RecycleAssetsIPStatsInfo
	InternalFusedCount          int64 `json:"internal_fused_count"`
	InternalUnfusedCount        int64 `json:"internal_unfused_count"`
	ExternalFusedCount          int64 `json:"external_fused_count"`
	ExternalUnfusedCount        int64 `json:"external_unfused_count"`
	RecycleInternalFusedCount   int64 `json:"recycle_internal_fused_count"`
	RecycleInternalUnfusedCount int64 `json:"recycle_internal_unfused_count"`
	RecycleExternalFusedCount   int64 `json:"recycle_external_fused_count"`
	RecycleExternalUnfusedCount int64 `json:"recycle_external_unfused_count"`
}

// 资产处理结果
func (s *WorkbenchService) AssetsResult() (*AssetsResult, error) {
	refreshIndex()
	ipResult, err := asset.IPStats()
	if err != nil {
		return nil, err
	}

	recycleIpResult, err := asset.RecycleIpStats()
	if err != nil {
		return nil, err
	}

	internalUnfusedCount, err := asset.AssetUnfused(asset.NetworkTypeInternal)
	if err != nil {
		return nil, err
	}
	ExternalUnfusedCount, err := asset.AssetUnfused(asset.NetworkTypeExternal)
	if err != nil {
		return nil, err
	}

	recycleInternalUnfusedCount, err := asset.RecycleAssetUnfused(asset.NetworkTypeInternal)
	if err != nil {
		return nil, err
	}
	recycleExternalUnfusedCount, err := asset.RecycleAssetUnfused(asset.NetworkTypeExternal)
	if err != nil {
		return nil, err
	}

	result := &AssetsResult{
		AssetsIPStatsInfo:           *ipResult,
		RecycleAssetsIPStatsInfo:    *recycleIpResult,
		InternalUnfusedCount:        internalUnfusedCount,
		InternalFusedCount:          ipResult.InternalIPCount - internalUnfusedCount,
		ExternalUnfusedCount:        ExternalUnfusedCount,
		ExternalFusedCount:          ipResult.ExternalIPCount - ExternalUnfusedCount,
		RecycleInternalUnfusedCount: recycleInternalUnfusedCount,
		RecycleInternalFusedCount:   recycleIpResult.RecycleInternalIPCount - recycleInternalUnfusedCount,
		RecycleExternalUnfusedCount: recycleExternalUnfusedCount,
		RecycleExternalFusedCount:   recycleIpResult.RecycleExternalIPCount - recycleExternalUnfusedCount,
	}

	return result, nil
}
func (s *WorkbenchService) tickerExecDataProcessOverview() {
	r, _ := workbench.NewDataProcessOverview().First(mysql.WithWhere("date_hour = ?", time.Now().Format(utils.TimeDateHourLayout)))
	if r.Id > 0 {
		return
	}

	nowData, err := s.GetCurrentDataProcessOverview()
	if err != nil {
		logger.Warn("GetCurrentDataProcessOverview failed ", err)
		return
	}
	_ = workbench.NewDataProcessOverview().Create(nowData)
}

var AssetBeforeFusionIndex = []string{
	assets.NewProcessAssetsModel().IndexName(),
	/*以下是原始表 资产是以_assets的索引,数据是以增量的方式加入，不会删除和修改
	bk_cmdb.NewBKCmdbAssetsModel().IndexName(),
	//data_import.NewDataImportAssetsModel().IndexName(),
	file_import.NewFileImportAssetsModel().IndexName(),
	foeye.NewFoeyeAssetsModel().IndexName(),
	foradar.NewForadarAssetsModel().IndexName(),
	qt_cloud.NewQTCloudAssetsModel().IndexName(),
	// 云洞、微步、waf开发中
	*/
}
var PocBeforeFusionIndex = []string{
	poc.NewProcessPocModel().IndexName(),
	/*以下是原始表 漏洞是以_threats结尾的索引,数据是以增量的方式加入，不会删除和修改
	//data_import.NewDataImportThreatsModel().IndexName(),
	file_import.NewFileImportThreatsModel().IndexName(),
	foeye.NewFoeyeThreatsModel().IndexName(),
	foradar.NewForadarThreatsModel().IndexName(),
	qt_cloud.NewQTCloudThreatsModel().IndexName(),
	// 云洞、微步、waf开发中
	*/
}

func (s *WorkbenchService) GetCurrentDataProcessOverview() (*workbench.DataProcessOverview, error) {
	var wg sync.WaitGroup
	var pocTotal, assetTotal, deviceCount int64
	var pocErr, assetErr, deviceErr error
	query := elastic.NewBoolQuery().MustNot(elastic.NewExistsQuery("deleted_at"),
		elastic.NewExistsQuery("purged_at"))
	wg.Add(1)
	go func() {
		defer wg.Done()
		deviceCount, deviceErr = es.GetCount(esmodel.NewDeviceModel().IndexName(), query)
	}()
	if deviceErr != nil {
		return nil, errors.WithMessagef(deviceErr, "{DeviceCount failed,err:%v}", deviceErr)
	}

	assetQuery := elastic.NewBoolQuery().
		MustNot(elastic.NewExistsQuery("deleted_at"),
			elastic.NewExistsQuery("purged_at"))
	wg.Add(1)
	go func() {
		defer wg.Done()
		assetTotal, assetErr = es.GetCount(assets.NewAssets().IndexName(), assetQuery)
	}()
	if assetErr != nil {
		return nil, assetErr
	}

	pocQuery := elastic.NewBoolQuery()
	wg.Add(1)
	go func() {
		defer wg.Done()
		pocTotal, pocErr = es.GetCount(poc.NewPoc().IndexName(), pocQuery)
	}()

	if pocErr != nil {
		return nil, pocErr
	}
	var assetOriginCount, pocOriginCount int64
	var mux sync.Mutex

	for _, index := range AssetBeforeFusionIndex {
		wg.Add(1)
		go func(esIndex string) {
			defer wg.Done()
			count, err := es.GetCount(esIndex, elastic.NewBoolQuery())
			if err != nil {
				logger.Errorf("%s GetCount failed,err:%v", esIndex, err)
				return
			}
			if count > 0 {
				mux.Lock()
				defer mux.Unlock()
				assetOriginCount += count
			}
		}(index)
	}

	for _, index := range PocBeforeFusionIndex {
		wg.Add(1)
		go func(esIndex string) {
			defer wg.Done()
			count, err := es.GetCount(esIndex, elastic.NewBoolQuery())
			if err != nil {
				logger.Errorf("%s GetCount failed,err:%v", esIndex, err)
				return
			}
			if count > 0 {
				mux.Lock()
				defer mux.Unlock()
				pocOriginCount += count
			}
		}(index)
	}

	wg.Wait()
	var result = workbench.DataProcessOverview{
		AssetBeforeFusionCount: assetOriginCount,
		PocBeforeFusionCount:   pocOriginCount,
		AssetFusionCount:       assetTotal,
		PocFusionCount:         pocTotal,
		DeviceCount:            deviceCount,
		DateHour:               time.Now().Format(utils.TimeDateHourLayout),
	}

	return &result, nil
}

// 数据处理概览
func (s *WorkbenchService) DataProcessOverview(refresh bool) ([]*workbench.DataProcessOverview, error) {
	now := time.Now()
	result, _, err := workbench.NewDataProcessOverview().List(0, 0,
		mysql.WithWhere("HOUR(created_at) = ?", now.Format("15")),
		mysql.WithWhere("created_at > ?",
			utils.ZeroOfDay(time.Now().AddDate(0, -1, 0)).Format(utils.DateTimeLayout)))
	if err != nil {
		return result, err
	}

	var hasTodayHour bool
	zero := utils.ZeroOfDay(now)
	var todayIndex int
	for i, v := range result {
		if zero.After(time.Time(v.CreatedAt)) {
			continue
		}
		if v.CreatedAt.Format(utils.TimeDateHourLayout) == now.Format(utils.TimeDateHourLayout) {
			hasTodayHour = true
			todayIndex = i
		}
	}

	if hasTodayHour && !refresh {
		return result, nil
	}

	if hasTodayHour && refresh {
		result = append(result[:todayIndex], result[todayIndex+1:]...)
	}

	nowData, err := s.GetCurrentDataProcessOverview()
	if err != nil {
		return result, err
	}

	result = append(result, nowData)
	_ = workbench.NewDataProcessOverview().Create(nowData)

	return result, nil
}

// 漏洞概览
func (s *WorkbenchService) VulnerabilityOverview() (*poc.PocOverviewInfo, error) {
	refreshIndex()
	pocOverview, err := poc.PocOverview()
	if err != nil {
		return nil, err
	}
	recyclePocOverview, err := poc.RecyclePocOverview()
	if err != nil {
		return nil, err
	}
	pocOverview.RecyclePocOverviewInfo = *recyclePocOverview
	return pocOverview, nil
}

type AssetExemptListRequest struct {
	Page       int    `json:"page"`
	Size       int    `json:"per_page"`
	Dimensions int64  `json:"dimensions_id"`
	Keywords   string `json:"keywords"`
}

func (s *WorkbenchService) AssetExemptList(req *AssetExemptListRequest) ([]*workbench.AssetExemptRecord, int64, error) {
	var opts []mysql.HandleFunc
	if req.Dimensions > 0 {
		opts = append(opts, mysql.WithWhere("dimensions = ?", req.Dimensions))
	}
	opts = append(opts, mysql.WithOrder("id desc"))
	record, total, err := workbench.NewWorkbenchAssetExemptRecordModel().List(req.Page, req.Size, opts...)
	if err != nil {
		return record, total, err
	}
	for _, v := range record {
		if v.ExemptType == workbench.ExemptTypeAdvanced && v.ExemptCondition != "" {
			var condition []string
			err = json.Unmarshal([]byte(v.ExemptCondition), &condition)
			if err != nil {
				continue
			}
			v.ExemptCondition = strings.Join(condition, ";")
		}
		if v.ExemptType == workbench.ExemptTypeSingleAsset && v.NetworkType == 3 {
			deviceQuery := elastic.NewBoolQuery().Must(elastic.NewTermQuery("id", v.ExemptCondition))
			d, _ := esmodel.NewDeviceModel().First(deviceQuery)
			if d != nil {
				v.ExemptCondition = d.Fid
			}
		}
		v.ExemptConditionHumanArray = strings.Split(v.ExemptConditionHuman, exemptConditionHumanJoinSep)
		v.ExemptConditionHuman = ""
	}

	return record, total, nil
}

type AssetExemptRateSaveRequest struct {
	DimensionsId         int64                      `json:"dimensions_id"`    // 统计维度
	IsShow               bool                       `json:"is_show"`          //是否在工作台资产安全覆盖度中展示
	NetworkType          int64                      `json:"asset_type"`       //类型0-内网资产，2-外网资产，3-实体设备
	AdvancedFilters      *asset_center.AssetRequest `json:"advanced_filters"` // 高级筛选条件
	AssetArray           []string                   `json:"asset_array"`      // 资产数组
	OpId                 int64                      `json:"op_id"`
	Remark               string                     `json:"remark"`
	SearchCondition      []string                   `json:"search_condition" form:"search_condition" uri:"search_condition"  zh:"搜索条件"`
	ExemptConditionHuman []string                   `json:"exempt_condition_human_array" form:"column:exempt_condition_human_array"`
}

var AssetExemptForbbidenErr = errors.New("不支持同时选择多条资产及高级筛选条件")

const exemptConditionHumanJoinSep = "##"

func (s *WorkbenchService) AssetExemptRateSave(req *AssetExemptRateSaveRequest) error {
	now := time.Now()
	if req.DimensionsId < 1 {
		return nil
	}
	showFn := func() error {
		return workbench.NewAssetSecurityCoverageModel().Save(req.DimensionsId, req.IsShow, map[string]interface{}{
			"is_show": req.IsShow,
		})
	}

	// 只保存是否显示
	if len(req.AssetArray) == 0 && len(req.SearchCondition) == 0 {
		return showFn()
	}
	if len(req.AssetArray) > 0 && len(req.SearchCondition) != 0 {
		return AssetExemptForbbidenErr
	}
	if err := showFn(); err != nil {
		return err
	}

	var itemSlice = make([]*workbench.AssetExemptRecord, 0)
	if len(req.AssetArray) > 0 {
		for _, v := range req.AssetArray {
			itemSlice = append(itemSlice, &workbench.AssetExemptRecord{
				DimensionsId:         req.DimensionsId,
				ExemptCondition:      v,
				ExemptConditionHash:  utils.Md5Hash(v),
				ExemptType:           workbench.ExemptTypeSingleAsset,
				NetworkType:          req.NetworkType,
				OpId:                 req.OpId,
				Remark:               req.Remark,
				ExemptConditionHuman: strings.Join(req.ExemptConditionHuman, exemptConditionHumanJoinSep),
				BaseModel: mysql.BaseModel{
					CreatedAt: localtime.Time(now),
					UpdatedAt: localtime.Time(now),
				},
			})
		}
		if err := workbench.NewWorkbenchAssetExemptRecordModel().Create(itemSlice); err != nil {
			return err
		}
		return s.UpdateAssetExemptRecordIdsSingle(req.DimensionsId, itemSlice)
	}
	if len(req.SearchCondition) == 0 {
		return nil
	}

	data, err := json.Marshal(req.SearchCondition)
	if err != nil {
		return errors.WithMessage(err, "传参错误")
	}

	advancedRecord := &workbench.AssetExemptRecord{
		DimensionsId:         req.DimensionsId,
		ExemptCondition:      string(data),
		ExemptConditionHash:  utils.Md5Hash(data),
		ExemptType:           workbench.ExemptTypeAdvanced,
		NetworkType:          req.NetworkType,
		OpId:                 req.OpId,
		Remark:               req.Remark,
		ExemptConditionHuman: strings.Join(req.ExemptConditionHuman, exemptConditionHumanJoinSep),
		BaseModel: mysql.BaseModel{
			CreatedAt: localtime.Time(now),
			UpdatedAt: localtime.Time(now),
		},
	}

	if err := workbench.NewWorkbenchAssetExemptRecordModel().Create([]*workbench.AssetExemptRecord{advancedRecord}); err != nil {
		return err
	}

	return s.UpdateAssetExemptRecordIdsAdvanced(req.DimensionsId, advancedRecord, req.SearchCondition)
}

func (s *WorkbenchService) UpdateAssetExemptRecordIdsSingle(dimensionsId int64, itemSlice []*workbench.AssetExemptRecord) error {
	for _, v := range itemSlice {
		if v.ExemptCondition == "" {
			continue
		}
		if v.Id == 0 {
			continue
		}
		query := elastic.NewBoolQuery()
		// 判断是否为实体设备的条件
		if v.NetworkType == 3 {
			deviceQuery := elastic.NewBoolQuery().Must(elastic.NewTermQuery("id", v.ExemptCondition))
			d, err := esmodel.NewDeviceModel().First(deviceQuery)
			if err != nil {
				return err
			}
			itemAllIps := d.GetAllIps()

			var ipArray = make([]interface{}, 0)
			for _, v := range utils.ListDistinctNonZero(itemAllIps) {
				ipArray = append(ipArray, v)
			}
			query.Must(elastic.NewTermsQuery("ip", ipArray...))
		} else {
			query.Must(elastic.NewTermQuery("ip", v.ExemptCondition))
		}
		docNum, err := assets.NewAssets().AddValueToExemptRecordIds(dimensionsId, []int64{int64(v.Id)}, query)
		if err != nil {
			return err
		}
		logger.Info("UpdateAssetExemptRecordIdsAdvanced AddValueToExemptRecordIds doc num:", docNum)
	}
	return nil
}

func (s *WorkbenchService) UpdateAssetExemptRecordIdsAdvanced(dimensionsId int64, r *workbench.AssetExemptRecord, searchCondition []string) error {
	if r.ExemptCondition == "" {
		return nil
	}
	query := elastic.NewBoolQuery()
	// 判断是否为实体设备的条件
	if r.NetworkType == 3 {
		deviceQuery := elastic.NewBoolQuery()
		conditions, err := filtrate.ParseQueryConditions(searchCondition)
		if err != nil {
			return errors.WithMessage(err, "豁免配置解析查询条件失败")
		}
		for _, condition := range conditions {
			deviceQuery = filtrate.BuildBoolQuery(condition.Field, condition.OperationTypeString, condition.LogicalConnective, condition.Value, deviceQuery)
		}
		count, err := es.GetCount(esmodel.NewDeviceModel().IndexName(), deviceQuery)
		if err != nil {
			return err
		}
		if count == 0 {
			return nil
		}
		allDevice, err := es.All[esmodel.Device](1000, deviceQuery, nil, "id", "private_ip", "public_ip", "ip")
		if err != nil {
			return errors.WithMessage(err, "豁免高级筛选条件查询设备失败")
		}
		var allIps []string
		for _, v := range allDevice {
			allIps = append(allIps, v.GetAllIps()...)
		}
		var ipArray = make([]interface{}, 0)
		for _, v := range utils.ListDistinctNonZero(allIps) {
			ipArray = append(ipArray, v)
		}
		query.Must(elastic.NewTermsQuery("ip", ipArray...))

	} else {
		params := &asset_center.InternalAssetRequest{
			PageRequest: request.PageRequest{
				Page:    1,
				PerPage: 20,
			},
			AssetRequest: asset_center.AssetRequest{
				SearchCondition: searchCondition,
			},
		}
		advancedQuery, err := asset.CreateBoolQuery("", int(r.NetworkType), asset.NotIsRecycleBin, []string{}, params)
		if err != nil {
			return err
		}
		query.Should(advancedQuery)
	}

	docNum, err := assets.NewAssets().AddValueToExemptRecordIds(dimensionsId, []int64{int64(r.Id)}, query)
	if err != nil {
		return err
	}
	logger.Info("UpdateAssetExemptRecordIdsAdvanced AddValueToExemptRecordIds doc num:", docNum)
	return nil
}

func (s *WorkbenchService) AssetExemptDelete(ids []int64) error {
	var opts []mysql.HandleFunc
	if len(ids) > 0 {
		opts = append(opts, mysql.WithWhere("id in (?)", ids))
	}
	record, _, err := workbench.NewWorkbenchAssetExemptRecordModel().List(0, 0, opts...)
	if err != nil {
		return err
	}

	var updateMap = make(map[int64][]int64)
	for _, v := range record {
		id := v.DimensionsId
		if _, ok := updateMap[id]; !ok {
			updateMap[id] = make([]int64, 0)
		}
		updateMap[id] = append(updateMap[id], int64(v.Id))
	}
	for dimensionsId, ids := range updateMap {
		docNum, err := assets.NewAssets().DeleteValueFromExemptRecordIdsScript(dimensionsId, ids)
		if err != nil {
			return err
		}
		logger.Infof("UpdateAssetExemptRecordIdsAdvanced Delete doc num:%d,dimensionsId:%d,ids:%d\n", docNum, dimensionsId, ids)
	}
	return workbench.NewWorkbenchAssetExemptRecordModel().Delete(ids)
}

func (s *WorkbenchService) AssetExemptAdvancedFilterToString(networkType int64, params *asset_center.AssetRequest) string {
	var result []string
	if params.Keyword != "" {
		result = append(result, fmt.Sprintf("keyword = %v", params.Keyword))
	}
	if networkType != 0 {
		result = append(result, fmt.Sprintf("networkType = %v", networkType))
	}

	if params != nil && params.OperationTypeString != "" {
		if len(params.SourceIds) > 0 {
			result = append(result, fmt.Sprintf("all_source_ids %s %v", params.OperationTypeString, params.SourceIds))
		}
		if len(params.Ips) > 0 {
			result = append(result, fmt.Sprintf("ip %s %v", params.OperationTypeString, params.Ips))
		}
		if len(params.Ports) > 0 {
			result = append(result, fmt.Sprintf("ports.port %s %v", params.OperationTypeString, params.Ports))
		}
		if len(params.Protocols) > 0 {
			result = append(result, fmt.Sprintf("ports.protocol %s %v", params.OperationTypeString, params.Protocols))
		}
		if len(params.Component) > 0 {
			result = append(result, fmt.Sprintf("product %s %v", params.OperationTypeString, params.Component))
		}
		if len(params.AssetsStatus) > 0 {
			result = append(result, fmt.Sprintf("status %s %v", params.OperationTypeString, params.AssetsStatus))
		}
		if len(params.IpTypes) > 0 {
			result = append(result, fmt.Sprintf("ip_type %s %v", params.OperationTypeString, params.IpTypes))
		}
		if len(params.MacAddress) > 0 {
			result = append(result, fmt.Sprintf("mac %s %v", params.OperationTypeString, params.MacAddress))
		}
		if len(params.Domain) > 0 {
			result = append(result, fmt.Sprintf("ports.domain %s %v", params.OperationTypeString, params.Domain))
		}
		if len(params.Area) > 0 {
			result = append(result, fmt.Sprintf("area %s %v", params.OperationTypeString, params.Area))
		}
		if len(params.BusinessApp) > 0 {
			result = append(result, fmt.Sprintf("business.system %s %v", params.OperationTypeString, params.BusinessApp))
		}
		if len(params.BusinessAppPrincipal) > 0 {
			result = append(result, fmt.Sprintf("business.owner %s %v", params.OperationTypeString, params.BusinessAppPrincipal))
		}
		if len(params.OperatorPrincipal) > 0 {
			result = append(result, fmt.Sprintf("oper %s %v", params.OperationTypeString, params.OperatorPrincipal))
		}
	}

	return strings.Join(result, ";")
}
