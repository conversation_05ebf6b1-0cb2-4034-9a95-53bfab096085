package workbench

import (
	"fobrain/models/elastic/assets"
	"testing"
	"time"

	"fobrain/fobrain/app/repository/asset"
	"fobrain/fobrain/app/request/asset_center"
	"fobrain/fobrain/common/localtime"
	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/initialize/mysql"
	"fobrain/models/elastic/poc"
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/workbench"
	"fobrain/pkg/utils"

	"reflect"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/pkg/errors"
	"github.com/stretchr/testify/assert"
)

// 初始化mock server
var mockServer *testcommon.MockServer

func TestMain(m *testing.M) {
	isTest = true
	mockServer = testcommon.NewMockServer()
	defer mockServer.Close()
	m.Run()
}

// TestWorkbenchService_HeadStats 测试工作台头部统计信息
func TestWorkbenchService_HeadStats(t *testing.T) {
	s := NewWorkbenchService()

	// 设置Mock ES
	mockEs := testcommon.NewMockServer()
	defer mockEs.Close()

	// Mock数据库连接
	mockDb := testcommon.InitSqlMock()
	defer mockDb.Close()

	t.Run("正常获取头部统计信息", func(t *testing.T) {
		// Mock Redis缓存查询 - 使用 nil 客户端
		testcommon.SetRedisClient(nil)

		// Mock ES刷新操作
		mockEs.Register("/process_asset,process_poc,poc,asset,device,compliance_monitor_task_records/_refresh", map[string]interface{}{
			"_shards": map[string]interface{}{
				"total": 5, "successful": 5, "failed": 0,
			},
		})

		// Mock各种统计查询
		mockEs.Register("/asset/_count", map[string]interface{}{
			"count": 1000,
		})
		mockEs.Register("/device/_count", map[string]interface{}{
			"count": 200,
		})
		mockEs.Register("/poc/_count", map[string]interface{}{
			"count": 50,
		})

		// Mock数据源查询
		mockDb.ExpectQuery("SELECT.*data_sources.*").
			WillReturnRows(mockDb.NewRows([]string{"id", "name", "status"}).
				AddRow(1, "探针1", 1).
				AddRow(2, "探针2", 2))

		// Mock业务系统查询
		mockDb.ExpectQuery("SELECT.*business_system.*").
			WillReturnRows(mockDb.NewRows([]string{"id", "name"}).
				AddRow(1, "系统1").
				AddRow(2, "系统2"))

		result, err := s.HeadStats(false)

		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.GreaterOrEqual(t, result.ProbeNormalCount, int64(0))
		assert.GreaterOrEqual(t, result.DeviceCount, int64(0))
		assert.GreaterOrEqual(t, result.BusinessCount, int64(0))
	})

	t.Run("强制刷新统计信息", func(t *testing.T) {
		// Mock类似的设置，但这次传入refresh=true
		testcommon.SetRedisClient(nil)

		mockEs.Register("/process_asset,process_poc,poc,asset,device,compliance_monitor_task_records/_refresh", map[string]interface{}{
			"_shards": map[string]interface{}{
				"total": 5, "successful": 5, "failed": 0,
			},
		})

		mockEs.Register("/asset/_count", map[string]interface{}{
			"count": 1000,
		})
		mockEs.Register("/device/_count", map[string]interface{}{
			"count": 200,
		})
		mockEs.Register("/poc/_count", map[string]interface{}{
			"count": 50,
		})

		mockDb.ExpectQuery("SELECT.*data_sources.*").
			WillReturnRows(mockDb.NewRows([]string{"id", "name", "status"}).
				AddRow(1, "探针1", 1))

		mockDb.ExpectQuery("SELECT.*business_system.*").
			WillReturnRows(mockDb.NewRows([]string{"id", "name"}).
				AddRow(1, "系统1"))

		result, err := s.HeadStats(true)

		assert.NoError(t, err)
		assert.NotNil(t, result)
	})
}

// TestWorkbenchService_ProbeOverview 测试探针概览
func TestWorkbenchService_ProbeOverview(t *testing.T) {
	s := NewWorkbenchService()

	// Mock数据库连接
	mockDb := testcommon.InitSqlMock()
	defer mockDb.Close()

	t.Run("OverviewList查询失败", func(t *testing.T) {
		patches := gomonkey.ApplyMethod(reflect.TypeOf(&data_source.Node{}), "OverviewList", func(_ *data_source.Node, opts ...mysql.HandleFunc) ([]*data_source.NodeOverview, error) {
			return nil, errors.New("数据源查询失败")
		})
		defer patches.Reset()

		result, err := s.ProbeOverview()

		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Contains(t, err.Error(), "数据源查询失败")
	})

	t.Run("空探针列表", func(t *testing.T) {
		patches := gomonkey.ApplyMethod(reflect.TypeOf(&data_source.Node{}), "OverviewList", func(_ *data_source.Node, opts ...mysql.HandleFunc) ([]*data_source.NodeOverview, error) {
			return []*data_source.NodeOverview{}, nil
		})
		defer patches.Reset()

		result, err := s.ProbeOverview()

		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, int64(0), result.NormalCount)
		assert.Equal(t, int64(0), result.AbnormalCount)
		assert.Len(t, result.AbnormalList, 0)
	})
}

// TestWorkbenchService_WeeklyTaskOverview 测试周任务概览
func TestWorkbenchService_WeeklyTaskOverview(t *testing.T) {
	s := NewWorkbenchService()

	t.Run("查询失败处理", func(t *testing.T) {
		// Mock数据库错误
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()

		mockDb.ExpectQuery("SELECT.*").WillReturnError(errors.New("数据库连接失败"))

		result, err := s.WeeklyTaskOverview()

		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Contains(t, err.Error(), "统计同步任务失败")
	})
}

// TestWorkbenchService_AssetExemptList 测试资产豁免列表
func TestWorkbenchService_AssetExemptList(t *testing.T) {
	s := NewWorkbenchService()

	// Mock数据库连接
	mockDb := testcommon.InitSqlMock()
	defer mockDb.Close()

	t.Run("数据库查询失败", func(t *testing.T) {
		mockDb.ExpectQuery("SELECT count.*workbench_asset_exempt_record.*").
			WillReturnError(errors.New("数据库连接失败"))

		req := &AssetExemptListRequest{
			Page: 1,
			Size: 10,
		}

		result, total, err := s.AssetExemptList(req)

		assert.Error(t, err)
		assert.Equal(t, int64(0), total)
		assert.Nil(t, result)
	})
}

func TestWorkbenchService_AssetsResult(t *testing.T) {
	s := NewWorkbenchService()

	// 设置Mock ES
	mockEs := testcommon.NewMockServer()
	defer mockEs.Close()

	t.Run("正常获取资产统计", func(t *testing.T) {
		// Mock各种ES查询的返回
		mockEs.Register("/asset/_refresh", map[string]interface{}{
			"_shards": map[string]interface{}{
				"total": 1, "successful": 1, "failed": 0,
			},
		})

		// Mock asset.IPStats()返回
		patches := gomonkey.ApplyFunc(asset.IPStats, func() (*asset.AssetsIPStatsInfo, error) {
			return &asset.AssetsIPStatsInfo{
				InternalIPCount: 100,
				ExternalIPCount: 50,
			}, nil
		})
		defer patches.Reset()

		// Mock asset.RecycleIpStats()返回
		patches.ApplyFunc(asset.RecycleIpStats, func() (*asset.RecycleAssetsIPStatsInfo, error) {
			return &asset.RecycleAssetsIPStatsInfo{
				RecycleInternalIPCount: 10,
				RecycleExternalIPCount: 5,
			}, nil
		})

		// Mock asset.AssetUnfused()返回
		patches.ApplyFunc(asset.AssetUnfused, func(networkType int) (int64, error) {
			if networkType == asset.NetworkTypeInternal {
				return 20, nil // 内网未融合数量
			}
			return 10, nil // 外网未融合数量
		})

		// Mock asset.RecycleAssetUnfused()返回
		patches.ApplyFunc(asset.RecycleAssetUnfused, func(networkType int) (int64, error) {
			if networkType == asset.NetworkTypeInternal {
				return 2, nil // 回收站内网未融合数量
			}
			return 1, nil // 回收站外网未融合数量
		})

		result, err := s.AssetsResult()

		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, int64(100), result.InternalIPCount)
		assert.Equal(t, int64(50), result.ExternalIPCount)
		assert.Equal(t, int64(80), result.InternalFusedCount)   // 100 - 20
		assert.Equal(t, int64(20), result.InternalUnfusedCount) // 未融合数量
		assert.Equal(t, int64(40), result.ExternalFusedCount)   // 50 - 10
		assert.Equal(t, int64(10), result.ExternalUnfusedCount) // 未融合数量
	})

	t.Run("IPStats失败", func(t *testing.T) {
		time.Sleep(time.Second)
		patches := gomonkey.ApplyFunc(asset.IPStats, func() (*asset.AssetsIPStatsInfo, error) {
			return nil, errors.New("IP统计失败")
		})
		defer patches.Reset()

		result, err := s.AssetsResult()

		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Contains(t, err.Error(), "IP统计失败")
	})
}

func TestWorkbenchService_AlarmCreate(t *testing.T) {
	t.Run("正常", func(t *testing.T) {
		time.Sleep(time.Second)
		defer gomonkey.ApplyMethodReturn(&workbench.NotifyAlarmCenter{}, "Create", nil).Reset()
		err := NewWorkbenchService().AlarmCreate(&workbench.NotifyAlarmCenter{})
		assert.Nil(t, err)
	})
}

func TestWorkbenchService_AlarmSingle(t *testing.T) {
	t.Run("正常", func(t *testing.T) {
		p := gomonkey.ApplyMethodFunc(workbench.NewNotifyAlarmCenter(), "First", func(opts ...mysql.HandleFunc) (*workbench.NotifyAlarmCenter, error) {

			return &workbench.NotifyAlarmCenter{
				Remark: "----",
			}, nil
		})
		r, err := NewWorkbenchService().AlarmSingle(1)
		p.Reset()
		if err != nil {
			t.Fatalf("AlarmList() error = %v\n", err)
		}
		t.Log(r)
	})
}

func TestWorkbenchService_AlarmList(t *testing.T) {
	t.Run("正常", func(t *testing.T) {
		mockDb := testcommon.GetMysqlMock()
		defer mockDb.Close()

		mockDb.ExpectQuery("SELECT count(*) FROM `notify_alarm_center` WHERE id IN (SELECT MAX(id) FROM `notify_alarm_center` WHERE created_at >= ? GROUP BY `msg_content`) AND msg_type = ?").
			WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg()).
			WillReturnRows(mockDb.NewRows([]string{"count"}).AddRow(1))

		mockDb.ExpectQuery("SELECT * FROM `notify_alarm_center` WHERE id IN (SELECT MAX(id) FROM `notify_alarm_center` WHERE created_at >= ? GROUP BY `msg_content`) AND msg_type = ? ORDER BY created_at DESC LIMIT 10").
			WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg()).
			WillReturnRows(mockDb.NewRows([]string{"id"}).AddRow(1))

		list, _, err := NewWorkbenchService().AlarmList(1, 10, 1, 1)

		assert.Nil(t, err)
		t.Log(len(list))
	})
}

func TestWorkbenchService_AlarmRemindList(t *testing.T) {
	patch := gomonkey.ApplyMethodReturn(&workbench.NotifyAlarmCenter{}, "GetAll", nil, int64(1), nil)
	_, count, err := NewWorkbenchService().AlarmRemindList([]string{"1"})
	patch.Reset()
	assert.Equal(t, count, int64(1))
	assert.Nil(t, err)
}

func TestWorkbenchService_ReadAlarmRemind(t *testing.T) {
	patches := []*gomonkey.Patches{
		gomonkey.ApplyMethodReturn(&workbench.NotifyAlarmCenter{}, "First", &workbench.NotifyAlarmCenter{
			MsgType: workbench.MsgTypeRemind,
		}, nil),
		gomonkey.ApplyMethodReturn(&workbench.NotifyAlarmCenter{}, "Update", nil),
	}
	err := NewWorkbenchService().ReadAlarmRemind(1, []int{1, 2, 3})
	for _, patch := range patches {
		patch.Reset()
	}
	assert.Nil(t, err)
}

func TestWorkbenchService_ReadAlarmRemindFail(t *testing.T) {
	time.Sleep(time.Millisecond * 300)
	patches := []*gomonkey.Patches{
		gomonkey.ApplyMethodReturn(&workbench.NotifyAlarmCenter{}, "First", &workbench.NotifyAlarmCenter{
			MsgType: workbench.MsgTypeRisk,
		}, nil),
		gomonkey.ApplyMethodReturn(&workbench.NotifyAlarmCenter{}, "Update", nil),
	}
	err := NewWorkbenchService().ReadAlarmRemind(1, []int{1, 2, 3})
	for _, patch := range patches {
		patch.Reset()
	}
	assert.NotNil(t, err)
}

func TestWorkbenchService_AssetExemptAdvancedFilterToString(t *testing.T) {
	type args struct {
		networkType int64
		params      *asset_center.AssetRequest
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "test1",
			args: args{
				networkType: 0,
				params: &asset_center.AssetRequest{
					Keyword: "asd",
				},
			},
			want: "keyword = asd",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &WorkbenchService{}
			assert.Equalf(t, tt.want, s.AssetExemptAdvancedFilterToString(tt.args.networkType, tt.args.params), "AssetExemptAdvancedFilterToString(%v, %v)", tt.args.networkType, tt.args.params)
		})
	}
}

// TestWorkbenchService_VulnerabilityOverview 测试漏洞概览
func TestWorkbenchService_VulnerabilityOverview(t *testing.T) {
	s := NewWorkbenchService()

	// 设置Mock ES
	mockEs := testcommon.NewMockServer()
	defer mockEs.Close()

	t.Run("正常获取漏洞概览", func(t *testing.T) {
		time.Sleep(time.Second)
		// Mock refreshIndex操作
		mockEs.Register("/process_asset,process_poc,poc,asset,device,compliance_monitor_task_records/_refresh", map[string]interface{}{
			"_shards": map[string]interface{}{
				"total": 1, "successful": 1, "failed": 0,
			},
		})

		// Mock poc.PocOverview()返回
		patches := gomonkey.ApplyFunc(poc.PocOverview, func() (*poc.PocOverviewInfo, error) {
			return &poc.PocOverviewInfo{}, nil
		})
		defer patches.Reset()

		// Mock poc.RecyclePocOverview()返回
		patches.ApplyFunc(poc.RecyclePocOverview, func() (*poc.RecyclePocOverviewInfo, error) {
			return &poc.RecyclePocOverviewInfo{}, nil
		})

		result, err := s.VulnerabilityOverview()

		assert.NoError(t, err)
		assert.NotNil(t, result)
	})

	t.Run("漏洞概览查询失败", func(t *testing.T) {
		time.Sleep(time.Second)
		patches := gomonkey.ApplyFunc(poc.PocOverview, func() (*poc.PocOverviewInfo, error) {
			return nil, errors.New("漏洞查询失败")
		})
		defer patches.Reset()

		result, err := s.VulnerabilityOverview()

		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Contains(t, err.Error(), "漏洞查询失败")
	})
}

func TestWorkbenchService_AssetExemptDelete(t *testing.T) {
	t.Run("正常", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()

		sql := "SELECT * FROM `workbench_asset_exempt_record`"
		mockDb.ExpectQuery("SELECT count(*) FROM `workbench_asset_exempt_record`").
			WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(2))
		mockDb.ExpectQuery(sql).
			WillReturnRows(mockDb.NewRows(AssetExemptRecordFieldsArray).
				AddRow(1, localtime.Parse(utils.DateTimeLayout, "2024-10-12 16:32:31"), localtime.Parse(utils.DateTimeLayout, "2024-10-12 16:32:31"),
					0, "*************", 0, 0, "").
				AddRow(2, localtime.Parse(utils.DateTimeLayout, "2024-10-11 16:32:31"), localtime.Parse(utils.DateTimeLayout, "2024-10-11 16:32:31"),
					1, "keyword='wps'", 1, 0, ""))

		mockDb.ExpectBegin()
		mockDb.ExpectExec("DELETE FROM `workbench_asset_exempt_record` WHERE id in (?)").
			WithArgs(int64(32)).
			WillReturnResult(sqlmock.NewResult(1, 1))

		mockDb.ExpectCommit()
		gomonkey.ApplyMethodReturn(&assets.Assets{}, "DeleteValueFromExemptRecordIdsScript", int64(1), nil)
		err := NewWorkbenchService().AssetExemptDelete([]int64{32})
		assert.Nil(t, err)
	})
}

func TestAssetExemptRateSave(t *testing.T) {
	var s WorkbenchService

	// 正常用例
	t.Run("正常保存显示设置", func(t *testing.T) {
		req := &AssetExemptRateSaveRequest{
			DimensionsId: 2,
			IsShow:       true,
			AssetArray:   []string{},
			OpId:         1001,
			Remark:       "test remark",
		}

		patch := gomonkey.ApplyMethodReturn(workbench.NewAssetSecurityCoverageModel(), "Save", nil)
		defer patch.Reset()

		err := s.AssetExemptRateSave(req)
		assert.Nil(t, err)
	})

	// 没有 AssetArray 和 AdvancedFilters
	t.Run("无资产数组和高级筛选条件", func(t *testing.T) {
		req := &AssetExemptRateSaveRequest{
			DimensionsId:    123,
			IsShow:          true,
			AssetArray:      []string{},
			AdvancedFilters: nil,
			OpId:            1001,
			Remark:          "test remark",
		}

		patch := gomonkey.ApplyMethodReturn(workbench.NewAssetSecurityCoverageModel(), "Save", nil)
		defer patch.Reset()

		err := s.AssetExemptRateSave(req)
		assert.Nil(t, err)
	})

	// 同时提供 AssetArray 和 AdvancedFilters
	t.Run("同时提供资产数组和高级筛选条件", func(t *testing.T) {
		req := &AssetExemptRateSaveRequest{
			DimensionsId:    123,
			IsShow:          true,
			AssetArray:      []string{"asset1"},
			AdvancedFilters: &asset_center.AssetRequest{},
			OpId:            1001,
			Remark:          "test remark",
			SearchCondition: []string{""},
		}

		err := s.AssetExemptRateSave(req)
		assert.NotNil(t, err)
		assert.Equal(t, "不支持同时选择多条资产及高级筛选条件", err.Error())
	})
	/*
		// 传参错误
		t.Run("传参错误", func(t *testing.T) {
			req := &AssetExemptRateSaveRequest{
				DimensionsId:    123,
				IsShow:          true,
				AdvancedFilters: &asset_center.AssetRequest{},
				OpId:            1001,
				Remark:          "test remark",
			}

			patch1 := gomonkey.ApplyMethodReturn(workbench.NewAssetSecurityCoverageModel(), "Save", nil)
			defer patch1.Reset()
			// Mock json.Marshal 失败
			patch := gomonkey.ApplyFuncReturn(json.Marshal, nil, errors.New("json marshal error"))

			defer patch.Reset()

			err := s.AssetExemptRateSave(req)
			assert.NotNil(t, err)
			assert.Equal(t, "传参错误", err.Error())
		})

		// 仅保存资产数组
		t.Run("仅保存资产数组", func(t *testing.T) {
			req := &AssetExemptRateSaveRequest{
				DimensionsId: 123,
				IsShow:       true,
				AssetArray:   []string{"asset1", "asset2"},
				OpId:         1001,
				Remark:       "test remark",
			}

			// Mock Save
			patch1 := gomonkey.ApplyMethodReturn(workbench.NewAssetSecurityCoverageModel(), "Save", nil)
			defer patch1.Reset()

			patch2 := gomonkey.ApplyMethodReturn(workbench.NewAssetSecurityCoverageModel(), "Create", nil)
			defer patch2.Reset()

			err := s.AssetExemptRateSave(req)
			assert.Nil(t, err)
		})

		// 仅保存高级筛选条件
		t.Run("仅保存高级筛选条件", func(t *testing.T) {
			req := &AssetExemptRateSaveRequest{
				DimensionsId:    123,
				IsShow:          true,
				AdvancedFilters: &asset_center.AssetRequest{Name: "example"},
				OpId:            1001,
				Remark:          "test remark",
			}

			// Mock json.Marshal
			patch3 := gomonkey.ApplyFunc(json.Marshal, func(v interface{}) ([]byte, error) {
				return []byte(`{"FilterName":"example"}`), nil
			})
			defer patch3.Reset()

			patch4 := gomonkey.ApplyFunc(workbench.NewAssetSecurityCoverageModel().Save, func(dimensionsId int64, isShow bool, data map[string]interface{}) error {
				return nil
			})
			defer patch4.Reset()

			patch5 := gomonkey.ApplyFunc(workbench.NewWorkbenchAssetExemptRecordModel().Create, func(records []*workbench.AssetExemptRecord) error {
				return nil
			})
			defer patch5.Reset()

			err := s.AssetExemptRateSave(req)
			assert.Nil(t, err)
		})
	*/
}


