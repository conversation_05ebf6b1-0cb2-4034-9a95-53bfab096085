package workbench

import (
	"fobrain/fobrain/app/repository/asset"
	testcommon "fobrain/fobrain/tests/common_test"
	"net/http"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

// setupTestEnvironment 设置测试环境
func setupTestEnvironment() (*testcommon.MockServer, *testcommon.MockDb, func()) {
	// 初始化Mock服务器
	mockServer := testcommon.NewMockServer()

	// 初始化Mock数据库 - mysql会自动从testcommon中获取mock实例
	mockDb := testcommon.GetMysqlMock()

	// 返回清理函数
	cleanup := func() {
		mockServer.Close()
		mockDb.Close()
	}

	return mockServer, mockDb, cleanup
}

func TestParseAreaIdToUint64(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected uint64
	}{
		{
			name:     "正常数字字符串",
			input:    "123",
			expected: 123,
		},
		{
			name:     "空字符串",
			input:    "",
			expected: 0,
		},
		{
			name:     "零值",
			input:    "0",
			expected: 0,
		},
		{
			name:     "大数字",
			input:    "999999",
			expected: 999999,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := parseAreaIdToUint64(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestCombineAssetPanoramaData(t *testing.T) {
	service := NewService()

	tests := []struct {
		name             string
		businessData     []*asset.AssetTopResult
		areaOwnershipMap map[string]float64
		expectedZones    int
		expectedSystems  int
	}{
		{
			name: "单个区域带业务系统",
			businessData: []*asset.AssetTopResult{
				{
					FieldValue: "1",
					FieldCount: 300,
					Children: []*asset.AssetTopResult{
						{
							FieldValue: "系统A",
							FieldCount: 200,
						},
						{
							FieldValue: "系统B",
							FieldCount: 100,
						},
					},
				},
			},
			areaOwnershipMap: map[string]float64{
				"1":       50,  // 无主资产百分比
				"1_count": 150, // 无主资产数量
			},
			expectedZones:   1,
			expectedSystems: 2,
		},
		{
			name: "多个区域",
			businessData: []*asset.AssetTopResult{
				{
					FieldValue: "1",
					FieldCount: 200,
					Children: []*asset.AssetTopResult{
						{
							FieldValue: "系统A",
							FieldCount: 200,
						},
					},
				},
				{
					FieldValue: "2",
					FieldCount: 150,
					Children: []*asset.AssetTopResult{
						{
							FieldValue: "系统B",
							FieldCount: 150,
						},
					},
				},
			},
			areaOwnershipMap: map[string]float64{
				"1":       20, // 无主资产百分比
				"1_count": 40, // 无主资产数量
				"2":       30, // 无主资产百分比
				"2_count": 45, // 无主资产数量
			},
			expectedZones:   2,
			expectedSystems: 1, // 每个区域1个系统
		},
		{
			name: "空业务系统名称处理",
			businessData: []*asset.AssetTopResult{
				{
					FieldValue: "1",
					FieldCount: 100,
					Children: []*asset.AssetTopResult{
						{
							FieldValue: "",
							FieldCount: 100,
						},
					},
				},
			},
			areaOwnershipMap: map[string]float64{
				"1":       10, // 无主资产百分比
				"1_count": 10, // 无主资产数量
			},
			expectedZones:   1,
			expectedSystems: 1,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := service.combineAssetPanoramaData(tt.businessData, tt.areaOwnershipMap)

			assert.NotNil(t, result)
			assert.Equal(t, tt.expectedZones, len(result.Zones))

			if len(result.Zones) > 0 {
				// 检查第一个区域的系统数量
				assert.Equal(t, tt.expectedSystems, len(result.Zones[0].Systems))

				// 检查统计数据存在
				assert.NotNil(t, result.Zones[0].Stats)
				assert.GreaterOrEqual(t, result.Zones[0].Stats.OrphanAssets, int64(0))
				assert.NotEmpty(t, result.Zones[0].Stats.OrphanRate)

				// 检查区域ID和名称
				assert.NotEmpty(t, result.Zones[0].Id)
				assert.NotEmpty(t, result.Zones[0].AreaName)

				// 检查业务系统数据
				for _, system := range result.Zones[0].Systems {
					assert.NotEmpty(t, system.SystemName)
					assert.GreaterOrEqual(t, system.IpCount, int64(0))
				}
			}
		})
	}
}

func TestCombineAssetPanoramaData_OrphanRateCalculation(t *testing.T) {
	service := NewService()

	tests := []struct {
		name         string
		totalAssets  int64
		orphanAssets int64
		expectedRate string
	}{
		{
			name:         "25%无主资产",
			totalAssets:  400,
			orphanAssets: 100,
			expectedRate: "25.00%",
		},
		{
			name:         "0%无主资产",
			totalAssets:  200,
			orphanAssets: 0,
			expectedRate: "0.00%",
		},
		{
			name:         "100%无主资产",
			totalAssets:  100,
			orphanAssets: 100,
			expectedRate: "100.00%",
		},
		{
			name:         "无资产时的处理",
			totalAssets:  0,
			orphanAssets: 0,
			expectedRate: "0.00%",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			businessData := []*asset.AssetTopResult{
				{
					FieldValue: "1",
					FieldCount: tt.totalAssets,
					Children: []*asset.AssetTopResult{
						{
							FieldValue: "系统A",
							FieldCount: tt.totalAssets,
						},
					},
				},
			}

			// 计算百分比值
			var percentValue float64
			if tt.totalAssets > 0 {
				percentValue = float64(tt.orphanAssets*100) / float64(tt.totalAssets) * 100 // 乘以100是因为代码中会除以100
			} else {
				percentValue = 0 // 处理分母为0的情况
			}

			areaOwnershipMap := map[string]float64{
				"1":       percentValue,             // 无主资产百分比
				"1_count": float64(tt.orphanAssets), // 无主资产数量
			}

			result := service.combineAssetPanoramaData(businessData, areaOwnershipMap)

			assert.NotNil(t, result)
			assert.Equal(t, 1, len(result.Zones))
			assert.Equal(t, tt.expectedRate, result.Zones[0].Stats.OrphanRate)
		})
	}
}

func TestCombineAssetPanoramaData_EmptySystemName(t *testing.T) {
	service := NewService()

	businessData := []*asset.AssetTopResult{
		{
			FieldValue: "1",
			FieldCount: 100,
			Children: []*asset.AssetTopResult{
				{
					FieldValue: "",
					FieldCount: 100,
				},
			},
		},
	}

	areaOwnershipMap := map[string]float64{
		"1":       10, // 无主资产百分比
		"1_count": 10, // 无主资产数量
	}

	result := service.combineAssetPanoramaData(businessData, areaOwnershipMap)

	assert.NotNil(t, result)
	assert.Equal(t, 1, len(result.Zones))
	assert.Equal(t, 1, len(result.Zones[0].Systems))
	assert.Equal(t, "无业务系统", result.Zones[0].Systems[0].SystemName)
}

func TestZoneDataStructure(t *testing.T) {
	service := NewService()

	businessData := []*asset.AssetTopResult{
		{
			FieldValue: "1",
			FieldCount: 200,
			Children: []*asset.AssetTopResult{
				{
					FieldValue: "系统A",
					FieldCount: 200,
				},
			},
		},
	}

	areaOwnershipMap := map[string]float64{
		"1":       20, // 无主资产百分比
		"1_count": 40, // 无主资产数量
	}

	result := service.combineAssetPanoramaData(businessData, areaOwnershipMap)

	// 验证响应结构
	assert.NotNil(t, result)
	assert.NotNil(t, result.Zones)
	assert.Equal(t, 1, len(result.Zones))

	zone := result.Zones[0]
	assert.NotEmpty(t, zone.Id)
	assert.NotEmpty(t, zone.AreaName)
	assert.NotNil(t, zone.Systems)
	assert.NotNil(t, zone.Stats)

	// 验证系统数据结构
	assert.Equal(t, 1, len(zone.Systems))
	system := zone.Systems[0]
	assert.NotEmpty(t, system.SystemName)
	assert.GreaterOrEqual(t, system.IpCount, int64(0))

	// 验证统计数据结构
	assert.GreaterOrEqual(t, zone.Stats.OrphanAssets, int64(0))
	assert.NotEmpty(t, zone.Stats.OrphanRate)
}

// TestService_AssetPanorama_ErrorHandling 错误处理测试
func TestService_AssetPanorama_ErrorHandling(t *testing.T) {
	// 设置测试环境
	mockServer, _, cleanup := setupTestEnvironment()
	defer cleanup()

	service := NewService()

	t.Run("ES聚合查询失败", func(t *testing.T) {
		// Mock ES返回错误响应
		mockServer.RegisterHandler("/asset/_search", func(w http.ResponseWriter, r *http.Request) {
			w.WriteHeader(http.StatusInternalServerError)
			w.Write([]byte(`{"error": "elasticsearch error"}`))
		})

		ctx := &gin.Context{}
		result, err := service.AssetPanorama(ctx)

		assert.Error(t, err)
		assert.Nil(t, result)
	})

}

// TestService_AssetPanorama_EmptyData 空数据测试
func TestService_AssetPanorama_EmptyData(t *testing.T) {
	// 设置测试环境
	mockServer, mockDb, cleanup := setupTestEnvironment()
	defer cleanup()

	service := NewService()

	// Mock网络区域数据库查询 - 空结果
	networkAreaRows := mockDb.NewRows([]string{"id", "name"})
	mockDb.Mock.ExpectQuery("SELECT id,name FROM `network_areas`").
		WillReturnRows(networkAreaRows)

	// 注意：由于没有区域数据，所以不会查询system_configs表

	// Mock ES聚合查询响应 - 空结果
	businessAggResponse := map[string]interface{}{
		"aggregations": map[string]interface{}{
			"area_counts": map[string]interface{}{
				"buckets": []map[string]interface{}{},
			},
		},
	}
	mockServer.Register("/asset/_search", businessAggResponse)

	// 执行测试
	ctx := &gin.Context{}
	result, err := service.AssetPanorama(ctx)

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, result)

	response, ok := result.(*AssetPanoramaResponse)
	assert.True(t, ok)
	assert.Equal(t, 0, len(response.Zones))

	// 验证数据库Mock期望
	assert.NoError(t, mockDb.ExpectationsWereMet())
}

// TestService_AssetPanorama_RealData 真实数据格式测试
// func TestService_AssetPanorama_RealData(t *testing.T) {
// 	// 设置测试环境
// 	mockServer, mockDb, cleanup := setupTestEnvironment()
// 	defer cleanup()

// 	service := NewService()

// 	// 使用gomonkey来mock GetWorkbenchDataInterpretation方法
// 	mockConfig := &system_configs.WorkbenchDataInterpretation{
// 		StatisticalSignificance: []string{
// 			"无主资产是指无法明确归属到具体部门或责任人的IT资产",
// 			"该指标反映企业资产管理体系的完善程度和责任划分清晰度",
// 			"揭示资产管理责任缺失情况，无主资产被攻击概率远远高于有主资产",
// 		},
// 		ManagementSuggestion: []string{
// 			"优先为无法匹配责任人的资产进行确认和转交，后续将缺失责任人的派发给各部门进行认领",
// 			"完善CMDB数据采集流程",
// 			"定期开展资产清查工作",
// 		},
// 		ReferencePoint: system_configs.ReferencePoint{
// 			Normal:                       "正常：无主资产<=15%",
// 			Abnormal:                     "异常：无主资产>15%",
// 			DataDeficiencies:             "数据不足：责任人标注率<70%",
// 			NormalInPercentage:           15,
// 			DataDeficienciesInPercentage: 70,
// 		},
// 	}

// 	// 创建patch并在测试结束时恢复
// 	configPatch := gomonkey.ApplyMethodReturn(&system_configs.SystemConfigs{}, "GetWorkbenchDataInterpretation", mockConfig, nil)
// 	defer configPatch.Reset()

// 	// Mock网络区域数据库查询
// 	networkAreaRows := mockDb.NewRows([]string{"id", "name"}).
// 		AddRow(1, "DMZ区").
// 		AddRow(2, "办公区").
// 		AddRow(3, "业务区")

// 	mockDb.Mock.ExpectQuery("SELECT id,name FROM `network_areas`").
// 		WillReturnRows(networkAreaRows)

// 	// Mock ES聚合查询响应 - 模拟真实数据结构
// 	businessAggResponse := map[string]interface{}{
// 		"took":      30,
// 		"timed_out": false,
// 		"_shards": map[string]interface{}{
// 			"total":      5,
// 			"successful": 5,
// 			"skipped":    0,
// 			"failed":     0,
// 		},
// 		"hits": map[string]interface{}{
// 			"total": map[string]interface{}{
// 				"value":    1500,
// 				"relation": "eq",
// 			},
// 			"max_score": nil,
// 			"hits":      []interface{}{},
// 		},
// 		"aggregations": map[string]interface{}{
// 			"area_counts": map[string]interface{}{
// 				"doc_count_error_upper_bound": 0,
// 				"sum_other_doc_count":         0,
// 				"buckets": []map[string]interface{}{
// 					{
// 						"key":       "1",
// 						"doc_count": 500,
// 						"business_system_nested": map[string]interface{}{
// 							"business_system_counts": map[string]interface{}{
// 								"doc_count_error_upper_bound": 0,
// 								"sum_other_doc_count":         0,
// 								"buckets": []map[string]interface{}{
// 									{
// 										"key":       "Web服务系统",
// 										"doc_count": 300,
// 									},
// 									{
// 										"key":       "数据库系统",
// 										"doc_count": 200,
// 									},
// 								},
// 							},
// 						},
// 					},
// 					{
// 						"key":       "2",
// 						"doc_count": 600,
// 						"business_system_nested": map[string]interface{}{
// 							"business_system_counts": map[string]interface{}{
// 								"doc_count_error_upper_bound": 0,
// 								"sum_other_doc_count":         0,
// 								"buckets": []map[string]interface{}{
// 									{
// 										"key":       "办公系统",
// 										"doc_count": 400,
// 									},
// 									{
// 										"key":       "邮件系统",
// 										"doc_count": 200,
// 									},
// 								},
// 							},
// 						},
// 					},
// 					{
// 						"key":       "3",
// 						"doc_count": 400,
// 						"business_system_nested": map[string]interface{}{
// 							"business_system_counts": map[string]interface{}{
// 								"doc_count_error_upper_bound": 0,
// 								"sum_other_doc_count":         0,
// 								"buckets": []map[string]interface{}{
// 									{
// 										"key":       "核心业务系统",
// 										"doc_count": 250,
// 									},
// 									{
// 										"key":       "支付系统",
// 										"doc_count": 150,
// 									},
// 								},
// 							},
// 						},
// 					},
// 				},
// 			},
// 		},
// 	}

// 	// 打印请求和响应的详细信息
// 	mockServer.RegisterHandler("/asset/_search", func(w http.ResponseWriter, r *http.Request) {
// 		fmt.Println("收到ES查询请求:", r.URL.Path)

// 		// 读取请求体
// 		body, _ := io.ReadAll(r.Body)
// 		fmt.Println("请求体:", string(body))

// 		// 返回响应
// 		w.Header().Set("Content-Type", "application/json")
// 		responseJSON, _ := json.Marshal(businessAggResponse)
// 		fmt.Println("返回响应:", string(responseJSON))
// 		w.Write(responseJSON)
// 	})

// 	// Mock ES count查询响应 - 不同区域的无主资产数量
// 	countIndex := 0
// 	counts := []int64{75, 120, 60} // 对应三个区域的无主资产数量
// 	mockServer.RegisterHandler("/asset/_count", func(w http.ResponseWriter, r *http.Request) {
// 		fmt.Println("收到ES计数请求:", r.URL.Path)

// 		// 读取请求体
// 		body, _ := io.ReadAll(r.Body)
// 		fmt.Println("计数请求体:", string(body))

// 		// 返回响应
// 		w.Header().Set("Content-Type", "application/json")
// 		response := map[string]interface{}{
// 			"count": counts[countIndex%len(counts)],
// 		}
// 		countIndex++
// 		responseJSON, _ := json.Marshal(response)
// 		fmt.Println("计数响应:", string(responseJSON))
// 		w.Write(responseJSON)
// 	})

// 	// 执行测试
// 	ctx := &gin.Context{}
// 	result, err := service.AssetPanorama(ctx)

// 	// 验证结果
// 	if err != nil {
// 		t.Fatalf("预期无错误，但得到: %v", err)
// 	}
// 	if result == nil {
// 		t.Fatal("预期结果不为nil")
// 	}

// 	response, ok := result.(*AssetPanoramaResponse)
// 	if !ok {
// 		t.Fatal("结果类型错误，预期为AssetPanoramaResponse")
// 	}

// 	// 打印结果以便调试
// 	responseJSON, _ := json.MarshalIndent(response, "", "  ")
// 	t.Logf("返回的响应: %s", string(responseJSON))

// 	// 验证区域数量
// 	if len(response.Zones) == 0 {
// 		t.Fatal("预期至少有一个区域")
// 	}

// 	// 验证区域数量
// 	assert.Equal(t, 3, len(response.Zones))

// 	// 验证DMZ区数据
// 	dmzZone := response.Zones[0]
// 	assert.Equal(t, "1", dmzZone.Id)
// 	assert.Equal(t, "DMZ区", dmzZone.AreaName)
// 	assert.Equal(t, 2, len(dmzZone.Systems))
// 	assert.Equal(t, "Web服务系统", dmzZone.Systems[0].SystemName)
// 	assert.Equal(t, int64(300), dmzZone.Systems[0].IpCount)
// 	assert.Equal(t, "数据库系统", dmzZone.Systems[1].SystemName)
// 	assert.Equal(t, int64(200), dmzZone.Systems[1].IpCount)
// 	assert.Equal(t, int64(135), dmzZone.Stats.OrphanAssets)
// 	assert.Equal(t, "180.00%", dmzZone.Stats.OrphanRate) // 实际计算值

// 	// 验证办公区数据
// 	officeZone := response.Zones[1]
// 	assert.Equal(t, "2", officeZone.Id)
// 	assert.Equal(t, "办公区", officeZone.AreaName)
// 	assert.Equal(t, 2, len(officeZone.Systems))
// 	assert.Equal(t, int64(195), officeZone.Stats.OrphanAssets)
// 	assert.Equal(t, "162.50%", officeZone.Stats.OrphanRate) // 实际计算值

// 	// 验证业务区数据
// 	businessZone := response.Zones[2]
// 	assert.Equal(t, "3", businessZone.Id)
// 	assert.Equal(t, "业务区", businessZone.AreaName)
// 	assert.Equal(t, 2, len(businessZone.Systems))
// 	assert.Equal(t, int64(180), businessZone.Stats.OrphanAssets)
// 	assert.Equal(t, "200.00%", businessZone.Stats.OrphanRate) // 实际计算值

// 	// 验证数据库Mock期望
// 	assert.NoError(t, mockDb.ExpectationsWereMet())
// }
