package workbench

import (
	"context"
	"errors"
	"fmt"
	"fobrain/fobrain/app/repository/asset"
	"fobrain/initialize/es"
	"fobrain/initialize/mysql"
	"fobrain/models/elastic/assets"
	"fobrain/models/elastic/business_system"
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/network_areas"
	"fobrain/models/mysql/system_configs"
	"strconv"
	"strings"

	"github.com/olivere/elastic/v7"
	"go-micro.dev/v4/logger"
)

type FloatingBoxDataFunc func(string) (any, error)

var FloatingBoxDataFuncMap = map[string]FloatingBoxDataFunc{
	system_configs.OwnerlessAssetsProportion:    OwnerlessAssetFloatingBoxData,
	system_configs.ProbeAssetContributionDegree: ProbeAssetContributionFloatingBoxData,
	system_configs.InternetVulnerabilityTop10:   InternetVulnerabilityTop10FloatingBoxData,
	system_configs.BusinessAssetStatistics:      BusinessAssetStatisticsFloatingBoxData,
	system_configs.BusinessDepartmentAssetCount: BusinessDepartmentAssetCountFloatingBoxData,
	system_configs.BusinessVulTop5:              BusinessVulTop5FloatingBoxData,
}

func (s *WorkbenchService) GetFloatingBoxData(key, label string) (any, error) {
	fn, ok := FloatingBoxDataFuncMap[key]
	if !ok {
		return nil, errors.New("不支持的类型")
	}
	return fn(label)
}

// ProbeBoxDetailData 探针详细数据结构
type ProbeBoxDetailData struct {
	IPSegment      string `json:"ip_segment"`      // IP段
	BusinessSystem string `json:"business_system"` // 关联业务系统
}

// ProbeAssetContributionFloatingBoxData 探针资产贡献度悬浮框数据
func ProbeAssetContributionFloatingBoxData(sourceIDStr string) (any, error) {
	// 从缓存获取探针覆盖信息
	coverage, err := GetProbeCoverageFromCache(context.Background(), sourceIDStr)
	if err != nil {
		return nil, fmt.Errorf("获取探针覆盖信息失败: %v", err)
	}

	var contentData []BoxData

	// 添加覆盖的IP段
	for _, detail := range coverage.CoveredRanges {
		contentData = append(contentData, BoxData{
			Label:    detail.IPSegment,      // IP段
			Value:    detail.BusinessSystem, // 关联的业务系统
			Children: []BoxData{},
			Tag:      "covered",
		})
	}

	// 添加缺失的IP段
	for _, detail := range coverage.UncoveredRanges {
		contentData = append(contentData, BoxData{
			Label:    detail.IPSegment,      // IP段
			Value:    detail.BusinessSystem, // 关联的业务系统
			Children: []BoxData{},
			Tag:      "missed",
		})
	}

	boxDatas := BoxDatas{
		ContentData: contentData,
	}

	return boxDatas, nil
}

// getSourceIdByName 根据探针名称获取探针ID
func getSourceIdByName(name string) (uint64, error) {
	sources, _, err := data_source.AllSources()
	if err != nil {
		return 0, err
	}

	for _, source := range sources {
		if source.Name == name {
			return source.Id, nil
		}
	}

	return 0, fmt.Errorf("未找到名称为 %s 的探针", name)
}

// getProbeAssetDetails 获取探针资产详情（基于IP段配置的缓存系统）
// sourceId: 探针ID字符串
// isCovered: true-获取覆盖的IP段，false-获取缺失的IP段
func getProbeAssetDetails(sourceId string, isCovered bool) ([]ProbeBoxDetailData, error) {
	// 使用新的基于IP段配置的缓存系统
	cacheData, err := GetProbeCoverageFromCache(context.Background(), sourceId)
	if err != nil {
		return nil, fmt.Errorf("获取探针覆盖数据失败: %v", err)
	}

	if isCovered {
		return cacheData.CoveredRanges, nil
	} else {
		return cacheData.UncoveredRanges, nil
	}
}

// formatProbeAssetDetails 将探针资产详情格式化为字符串
func formatProbeAssetDetails(details []ProbeBoxDetailData) string {
	if len(details) == 0 {
		return ""
	}

	var result strings.Builder
	for i, detail := range details {
		if i > 0 {
			result.WriteString("\n")
		}
		result.WriteString(fmt.Sprintf("IP段: %s, 业务系统: %s", detail.IPSegment, detail.BusinessSystem))
	}
	return result.String()
}

func InternetVulnerabilityTop10FloatingBoxData(label string) (any, error) {
	query := assets.NewAssets().GenQueryNoDeletedAndPurged().Must(elastic.NewTermQuery("ip", label))
	assetInfo, err := es.First[assets.Assets](query, nil)
	if err != nil {
		return nil, err
	}

	if assetInfo == nil {
		return &BoxDatas{
			ContentData: []BoxData{},
		}, nil
	}

	networkarea, err := network_areas.NewNetworkAreaModel().First(mysql.WithId(uint64(assetInfo.Area)))
	if err != nil {
		logger.Warnf("get network area by id(%d) error: %v", assetInfo.Area, err)
	}

	boxDatas := BoxDatas{
		ContentData: []BoxData{
			{
				Label: "所属区域：",
				Value: networkarea.Name,
			},
			{
				Label: "关联业务系统",
				Value: assetInfo.BusinessSystems(),
			},
			{
				Label: "业务系统负责人",
				Value: assetInfo.BusinessOwners(),
			},
			{
				Label: "业务部门",
				Value: assetInfo.BusinessDepartments(),
			},
			{
				Label: "运维人员",
				Value: strings.Join(assetInfo.OperStringSlice(), ","),
			},
			{
				Label: "运维部门",
				Value: assetInfo.OperDepartments(),
			},
			{
				Label: "主机名",
				Value: strings.Join(assetInfo.HostName, ","),
			},
		},
	}
	return boxDatas, nil
}

// DepartmentVulnRetestPassRateFloatingBoxData 业务系统悬浮框数据
func BusinessVulTop5FloatingBoxData(businessName string) (any, error) {
	query := elastic.NewBoolQuery()
	query.Must(elastic.NewTermQuery("business_name", businessName))
	business, err := es.First[business_system.BusinessSystems](query, nil)
	if err != nil {
		return nil, err
	}
	if business == nil {
		return nil, fmt.Errorf("business system %s not found", businessName)
	}
	boxDatas := BoxDatas{
		ContentData: []BoxData{},
	}
	children := []BoxData{}
	children = append(children, BoxData{
		Label: "业务系统负责人",
		Value: func() string {
			if business.PersonBase == nil {
				return "无"
			}
			personNames := []string{}
			for _, person := range business.PersonBase {
				if person.Name == "" || person.Name == "null" {
					continue
				}
				personNames = append(personNames, person.Name)
			}
			return strings.Join(personNames, ",")
		}(),
		Children: []BoxData{},
		Tag:      "",
	})
	children = append(children, BoxData{
		Label: "业务部门",
		Value: func() string {
			if business.DepartmentBase == nil {
				return "无"
			}
			departmentNames := []string{}
			for _, department := range business.DepartmentBase {
				if department.Name == "" || department.Name == "null" {
					continue
				}
				departmentNames = append(departmentNames, department.Name)
			}
			return strings.Join(departmentNames, ",")
		}(),
		Children: []BoxData{},
		Tag:      "",
	})
	children = append(children, BoxData{
		Label: "运行状态",
		Value: func() string {
			if business.AssetsAttribute == (business_system.AssetsAttribute{}) || business.AssetsAttribute.RunningState == nil {
				return ""
			}
			if *business.AssetsAttribute.RunningState == 1 {
				return "运行中"
			}
			return "已下线"
		}(),
		Children: []BoxData{},
		Tag:      "",
	})
	boxDatas.ContentData = append(boxDatas.ContentData, BoxData{
		Label:    "业务系统",
		Value:    business.BusinessName,
		Children: children,
	})
	return boxDatas, nil
}

// 根据参数查询业务系统关联资产数据
func BusinessAssetStatisticsFloatingBoxData(label string) (any, error) {
	// 0: 可信, 1: 待确认, 2: 黑名单, 3: 缺失业务系统
	//1可信 2可疑 3不可信
	businessType := 0
	switch label {
	case "可信":
		businessType = 1
	case "待确认":
		businessType = 2
	case "黑名单":
		businessType = 3
	case "缺失业务系统":
		businessType = 0
	}
	additionQueries := []elastic.Query{}
	if businessType != 0 {
		//nested查询
		additionQueries = append(additionQueries, elastic.NewBoolQuery().Must(elastic.NewNestedQuery("business", elastic.NewTermQuery("business.business_trusted_state", businessType))))
	} else {
		return BoxDatas{}, nil
	}
	// 组合高级查询参数
	config := asset.AssetTopConfig{
		Field:           "business.system",
		Size:            5,
		NestedPath:      "business",
		NestedSubField:  "",
		IsDualNested:    false,
		Keyword:         "",
		AdditionQueries: additionQueries,
		Must:            false,
		IncludeMissing:  false,
		IndexName:       assets.NewAssets().IndexName(),
	}

	// 只有在非缺失业务系统的情况下才添加聚合过滤条件
	if businessType != 0 {
		config.AggFilters = []elastic.Query{
			elastic.NewTermQuery("business.business_trusted_state", businessType),
		}
	}

	return processBusinessAssetData(config)
}

// 根据参数查询业务部门资产数量
func BusinessDepartmentAssetCountFloatingBoxData(label string) (any, error) {
	additionQueries := []elastic.Query{}

	// 只查询子部门的资产
	// 使用父部门匹配查询，这样只会返回子部门的数据
	query := elastic.NewNestedQuery("business.department_base.parents",
		elastic.NewTermQuery("business.department_base.parents.name.keyword", label))
	additionQueries = append(additionQueries, query)

	// 组合高级查询参数
	config := asset.AssetTopConfig{
		Field:           "business.department_base.name.keyword",
		Size:            1000, // 获取足够多的结果以确保不遗漏
		NestedPath:      "business.department_base",
		NestedSubField:  "",
		IsDualNested:    false,
		Keyword:         "",
		AdditionQueries: additionQueries,
		Must:            false,
		IncludeMissing:  false,
		IndexName:       assets.NewAssets().IndexName(),
	}

	// 获取数据并处理
	boxDatas, err := processBusinessAssetData(config)
	if err != nil {
		return BoxDatas{}, err
	}

	// 创建部门层级映射，用于累加子部门数据
	deptMap := make(map[string]int64)

	// 收集所有部门的直接资产数量
	for _, data := range boxDatas.ContentData {
		count, _ := strconv.ParseInt(data.Value, 10, 64)
		deptMap[data.Label] = count
	}

	// 创建直接子部门映射，用于累加其子部门的资产
	directChildrenMap := make(map[string]int64)
	labelPrefix := label + "/"

	// 第一步：找出所有直接子部门
	for deptPath, count := range deptMap {
		if strings.HasPrefix(deptPath, labelPrefix) {
			parts := strings.Split(strings.TrimPrefix(deptPath, labelPrefix), "/")
			if len(parts) == 1 { // 直接子部门
				directChildrenMap[deptPath] = count
			}
		}
	}

	// 第二步：为每个直接子部门累加它们子部门的资产
	for deptPath, count := range deptMap {
		if strings.HasPrefix(deptPath, labelPrefix) {
			parts := strings.Split(strings.TrimPrefix(deptPath, labelPrefix), "/")
			if len(parts) > 1 { // 孙子部门及更深层级
				// 构建直接子部门的路径
				directChildPath := label + "/" + parts[0]
				if _, exists := directChildrenMap[directChildPath]; exists {
					// 将这个部门的资产数量加到对应的直接子部门上
					directChildrenMap[directChildPath] += count
				}
			}
		}
	}

	// 构建最终结果
	result := BoxDatas{
		ContentData: make([]BoxData, 0, len(directChildrenMap)),
	}

	// 只添加直接子部门（包含其子部门的资产）
	for deptPath, totalCount := range directChildrenMap {
		// 获取相对路径的最后一段作为部门名称
		parts := strings.Split(deptPath, "/")
		deptName := parts[len(parts)-1]

		result.ContentData = append(result.ContentData, BoxData{
			Label:    deptName, // 只使用部门名称，不包含父级路径
			Value:    strconv.FormatInt(totalCount, 10),
			Children: []BoxData{},
			Tag:      "",
		})
	}

	return result, nil
}

func processBusinessAssetData(config asset.AssetTopConfig) (BoxDatas, error) {
	assetData, err := asset.AssetTopProcess(config)
	if err != nil {
		return BoxDatas{}, err
	}
	boxDatas := BoxDatas{
		ContentData: []BoxData{}, // 初始化为空slice而不是nil
	}
	for _, result := range assetData {
		boxDatas.ContentData = append(boxDatas.ContentData, BoxData{
			Label:    result.FieldValue,
			Value:    strconv.FormatInt(result.FieldCount, 10),
			Children: []BoxData{},
			Tag:      "",
		})
	}
	return boxDatas, nil
}
