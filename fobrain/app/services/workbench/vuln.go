package workbench

import (
	"context"
	"errors"
	"fmt"
	"fobrain/fobrain/app/repository/asset"
	log "fobrain/fobrain/logs"
	"fobrain/initialize/es"
	"fobrain/initialize/mysql"
	"fobrain/models/elastic/assets"
	"fobrain/models/elastic/poc"
	"fobrain/models/mysql/workbench"
	"sort"
	"strings"
	"time"

	"git.gobies.org/caasm/fobrain-components/utils"
	"github.com/olivere/elastic/v7"
)

// 漏洞未整改占比情况
func (s *WorkbenchService) tickerRecordNoRepairedVuln() {
	dataHour := time.Now().Format(utils.TimeDateHourLayout)
	r, _ := workbench.NewWorkbenchStatisticsByHour().First(
		mysql.WithWhere("category = ?", workbench.WorkbenchCategoryNoRepairedVulnerability),
		mysql.WithWhere("date_hour = ?", dataHour),
	)
	if r.Id > 0 {
		return
	}

	// 已处理：包括复测通过、误报、无法修复
	query := elastic.NewBoolQuery().MustNot(
		elastic.NewExistsQuery("deleted_at"),
		elastic.NewExistsQuery("purged_at"),
		elastic.NewTermsQuery("status", 30, 40, 41),
	)
	count, err := es.GetCount(poc.NewPoc().IndexName(), query)
	if err != nil {
		log.GetLogger().Warn("tickerRecordNoRepairedVuln query Molecular failed ", err)
		return
	}
	all, err := es.GetCount(poc.NewPoc().IndexName(), assets.NewAssets().GenQueryNoDeletedAndPurged())
	if err != nil {
		log.GetLogger().Warn("tickerRecordNoRepairedVuln query Denominator failed ", err)
		return
	}

	err = workbench.NewWorkbenchStatisticsByHour().Create(&workbench.WorkbenchStatisticsByHour{
		Category:    workbench.WorkbenchCategoryNoRepairedVulnerability,
		Molecular:   count,
		Denominator: all,
		Percentage:  utils.CalculatePercentageRate(count, all),
		DateHour:    dataHour,
	})
	if err != nil {
		log.GetLogger().Warn("tickerRecordNoRepairedVuln create record failed ", err)
	}
}

func (s *WorkbenchService) NoRepairedVulnPercentage(period string) ([]*asset.AssetTopResult, error) {
	return s.QueryWorkbenchStatistics(workbench.WorkbenchCategoryNoRepairedVulnerability, period)
}

func (s *WorkbenchService) QueryWorkbenchStatistics(category int64, period string) ([]*asset.AssetTopResult, error) {
	records, err := workbench.NewWorkbenchStatisticsByHour().Query(category, period)
	if err != nil {
		return nil, err
	}
	var result = make([]*asset.AssetTopResult, 0, len(records))
	for _, record := range records {
		result = append(result, &asset.AssetTopResult{
			FieldValue: record.PeriodLabel,
			FieldCount: record.Percentage,
		})
	}
	return result, nil
}

func (s *WorkbenchService) PocVulnRepairedInfo() ([]*asset.AssetTopResult, string, error) {
	query := elastic.NewBoolQuery().MustNot(elastic.NewExistsQuery("deleted_at"))
	query = query.Must(elastic.NewTermQuery("is_poc", 1))
	all, err := es.GetCount(poc.NewPoc().IndexName(), query)
	if err != nil {
		return nil, "", err
	}
	query = query.Must(elastic.NewTermsQuery("status", poc.PocStatusOfRepaired))
	repaired, err := es.GetCount(poc.NewPoc().IndexName(), query)
	if err != nil {
		return nil, "", err
	}

	result := []*asset.AssetTopResult{
		{
			FieldValue: "repaired",
			FieldCount: utils.CalculatePercentageRate(repaired, all),
		},
		{
			FieldValue: "norepaired",
			FieldCount: utils.CalculatePercentageRate((all - repaired), all),
		},
	}

	return result, fmt.Sprintf("全部POC漏洞数量%d个", all), nil

}
func (s *WorkbenchService) PocVulTypeTop5() ([]*asset.AssetTopResult, error) {
	allResult, err := asset.AssetTopProcess(asset.AssetTopConfig{
		Field:     "vulType",
		IndexName: poc.NewPoc().IndexName(),
		Size:      100000,
	})
	if err != nil {
		return nil, err
	}
	var vulTypeCountMap = make(map[string]int64, len(allResult))
	for _, r := range allResult {
		arr := strings.Split(r.FieldValue, " ")
		for _, v := range utils.ListDistinctNonZero(arr) {
			vulTypeCountMap[v] += r.FieldCount
		}
	}
	type kv struct {
		Key   string
		Value int64
	}
	var sorted []kv
	for k, v := range vulTypeCountMap {
		sorted = append(sorted, kv{k, v})
	}

	sort.Slice(sorted, func(i, j int) bool {
		return sorted[i].Value > sorted[j].Value
	})
	var result = make([]*asset.AssetTopResult, 0, 5)
	for _, v := range sorted {
		result = append(result, &asset.AssetTopResult{
			FieldValue: v.Key,
			FieldCount: v.Value,
		})
		if len(result) >= 5 {
			break
		}
	}
	return result, nil
}

func (s *WorkbenchService) PocIPTop10() ([]*asset.AssetTopResult, error) {
	agg := elastic.NewTermsAggregation().Field("ip.keyword").Size(10000).OrderByCountDesc()
	searchResult, err := es.GetEsClient().Search().
		Index(poc.NewPoc().IndexName()).
		Size(0).
		Aggregation("top_ips", agg).
		Do(context.TODO())
	if err != nil {
		return nil, err
	}
	termsAgg, found := searchResult.Aggregations.Terms("top_ips")
	if !found {
		return nil, errors.New("Aggregation top_ips not found")
	}
	var result = make([]*asset.AssetTopResult, 0, 10)
	for _, bucket := range termsAgg.Buckets {
		result = append(result, &asset.AssetTopResult{
			FieldValue: fmt.Sprint(bucket.Key),
			FieldCount: bucket.DocCount,
		})
	}
	sort.Slice(result, func(i, j int) bool {
		return result[i].FieldCount > result[j].FieldCount
	})
	if len(result) >= 10 {
		result = result[:9]
	}
	for _, r := range result {
		boolQuery := elastic.NewBoolQuery().Must(elastic.NewTermsQuery("ip", r.FieldValue)).MustNot(elastic.NewExistsQuery("deleted_at"))
		count, _ := es.GetCount(poc.NewPoc().IndexName(), boolQuery)
		if count > 0 {
			r.FieldCount = count
		}
	}
	return result, nil
}
