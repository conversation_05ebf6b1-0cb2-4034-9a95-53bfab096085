package workbench

import (
	"context"
	"fmt"
	"fobrain/fobrain/app/repository/asset"
	"fobrain/initialize/es"
	"fobrain/models/elastic/assets"
	"fobrain/models/elastic/business_system"
	"fobrain/models/elastic/compliance_monitor"
	"fobrain/models/elastic/poc"
	"fobrain/models/mysql/system_configs"
	"fobrain/models/mysql/workbench"
	"strings"

	"git.gobies.org/caasm/fobrain-components/utils"
	"github.com/olivere/elastic/v7"
)

// AssetComponentProportionDataInterpretation 资产组件占比数据解析
func AssetComponentProportionDataInterpretation(results []*asset.AssetTopResult) (*system_configs.DataInterpretationResult, error) {
	noData := &system_configs.DataInterpretationResult{
		TagsStatus:     system_configs.DataInterpretationTagsStatusDataDeficiencies,
		AnalysisResult: "暂无组件数据",
	}
	if len(results) == 0 {
		return noData, nil
	}

	// 获取配置
	conf, err := system_configs.NewSystemConfigs().GetWorkbenchDataInterpretation(system_configs.AssetComponentProportion)
	if err != nil {
		return nil, err
	}

	// 获取总资产数量
	totalAssets, err := es.GetCount(assets.NewAssets().IndexName(), assets.NewAssets().GenQueryNoDeletedAndPurged())
	if err != nil {
		return nil, fmt.Errorf("获取总资产数量失败: %v", err)
	}

	if totalAssets == 0 {
		return noData, nil
	}

	// 首先判断数据不足：组件识别率<80%
	// 计算有组件信息的资产数量
	hasComponentQuery := assets.NewAssets().GenQueryNoDeletedAndPurged().Must(
		elastic.NewNestedQuery("rule_infos",
			elastic.NewBoolQuery().Must(
				elastic.NewExistsQuery("rule_infos.product"),
			).MustNot(
				elastic.NewTermQuery("rule_infos.product", ""),
			),
		),
	)

	assetsWithComponents, err := es.GetCount(assets.NewAssets().IndexName(), hasComponentQuery)
	if err != nil {
		return nil, fmt.Errorf("获取有组件信息的资产数量失败: %v", err)
	}

	componentRecognitionRate := utils.CalculatePercentageRate(assetsWithComponents, totalAssets)

	if componentRecognitionRate < conf.GetDataDeficienciesInPercentage() {
		return &system_configs.DataInterpretationResult{
			TagsStatus:     system_configs.DataInterpretationTagsStatusDataDeficiencies,
			AnalysisResult: fmt.Sprintf("数据不足：当前组件识别率<%d%%，数据暂时不具备分析价值", conf.ReferencePoint.DataDeficienciesInPercentage),
		}, nil
	}

	// 计算TOP3组件占比：查询包含TOP3组件中任意一个的资产数量
	var top3Components []string
	for i, result := range results {
		if i >= 3 { // 只取前3个
			break
		}
		if result.FieldValue != "" {
			top3Components = append(top3Components, result.FieldValue)
		}
	}

	var top3AssetsCount int64 = 0
	if len(top3Components) > 0 {
		// 构建查询条件：包含TOP3组件中任意一个的资产
		componentQuery := assets.NewAssets().GenQueryNoDeletedAndPurged()

		// 添加组件匹配条件（假设组件字段是rule_infos.name）
		// 这里需要根据实际的组件字段进行调整
		var componentQueries []elastic.Query
		for _, component := range top3Components {
			componentQueries = append(componentQueries,
				elastic.NewNestedQuery("rule_infos",
					elastic.NewTermQuery("rule_infos.name", component)))
		}

		if len(componentQueries) > 0 {
			componentQuery.Should(componentQueries...)
			componentQuery.MinimumShouldMatch("1")
		}

		top3AssetsCount, err = es.GetCount(assets.NewAssets().IndexName(), componentQuery)
		if err != nil {
			return nil, fmt.Errorf("获取TOP3组件关联资产数量失败: %v", err)
		}
	}

	// 计算TOP3组件占比
	top3Percentage := utils.CalculatePercentageRate(top3AssetsCount, totalAssets)

	// 检查两高一弱组件
	var highRiskPorts []compliance_monitor.PortEntry
	highRiskPorts = append(highRiskPorts, compliance_monitor.IntranetHighRiskPorts...)
	highRiskPorts = append(highRiskPorts, compliance_monitor.InternetHighRiskPorts...)

	var ruleInfosList []string
	for _, t := range highRiskPorts {
		if t.RuleInfos != "" {
			ruleInfosList = append(ruleInfosList, t.RuleInfos)
		}
	}
	distinctRuleInfos := utils.ListDistinct(ruleInfosList)

	var termQueries []elastic.Query
	for _, ruleInfo := range distinctRuleInfos {
		termQueries = append(termQueries,
			elastic.NewTermQuery("rule_infos.product", ruleInfo).CaseInsensitive(true),
		)
	}

	var productQuery elastic.Query
	if len(termQueries) == 1 {
		productQuery = termQueries[0]
	} else if len(termQueries) > 1 {
		productQuery = elastic.NewBoolQuery().Should(termQueries...).MinimumNumberShouldMatch(1)
	} else {
		productQuery = elastic.NewMatchAllQuery()
	}

	ruleInfoQuery := assets.NewAssets().GenQueryNoDeletedAndPurged().Must(
		elastic.NewNestedQuery("rule_infos", productQuery),
	)
	highRiskAssetsCount, err := es.GetCount(assets.NewAssets().IndexName(), ruleInfoQuery)
	if err != nil {
		return nil, fmt.Errorf("获取两高一弱组件关联资产数量失败: %v", err)
	}
	// 判断异常情况
	if top3Percentage > conf.GetNormalInPercentage() {
		return &system_configs.DataInterpretationResult{
			TagsStatus:     system_configs.DataInterpretationTagsStatusAbnormal,
			AnalysisResult: fmt.Sprintf("异常：TOP3组件总资产>%d%%，需要重点对高占比组件实施更严格的安全管控", conf.ReferencePoint.NormalInPercentage),
		}, nil
	}

	if highRiskAssetsCount > 0 {
		return &system_configs.DataInterpretationResult{
			TagsStatus:     system_configs.DataInterpretationTagsStatusAbnormal,
			AnalysisResult: "异常：含有两高一弱的组件，需要尽快采取相关措施保证安全性",
		}, nil
	}

	// 正常情况
	return &system_configs.DataInterpretationResult{
		TagsStatus:     system_configs.DataInterpretationTagsStatusNormal,
		AnalysisResult: fmt.Sprintf("正常：TOP3组件<=%d%%且无两高一弱的组件，组件具备多元化特征", conf.ReferencePoint.NormalInPercentage),
	}, nil
}

// ProbeAssetContributionDataInterpretation 探针资产数据贡献度解析
// 根据核心探针的覆盖度情况进行分析判断
// 核心探针包括：主动探测+被动流量+终端设备+CMDB或线下台账
// 正常：具备核心探针数据，且核心探针数据单个覆盖度>=70%
// 异常：缺少核心探针或者存在核心探针数据单个覆盖度<70%
// 覆盖度计算逻辑：（独有+共有）/总资产数
func ProbeAssetContributionDataInterpretation(results []*asset.ProbeContributionItem) (*system_configs.DataInterpretationResult, error) {
	var noData = &system_configs.DataInterpretationResult{
		TagsStatus:     system_configs.DataInterpretationTagsStatusDataDeficiencies,
		AnalysisResult: "暂无探针数据，无法进行分析",
	}
	// 空数据检查
	if len(results) == 0 {
		return noData, nil
	}

	// 获取配置
	config, err := system_configs.NewSystemConfigs().GetWorkbenchDataInterpretation(system_configs.ProbeAssetContributionDegree)
	if err != nil {
		return nil, fmt.Errorf("获取配置失败: %v", err)
	}

	// 获取总资产数量
	totalAssets, err := es.GetCount(assets.NewAssets().IndexName(), assets.NewAssets().GenQueryNoDeletedAndPurged())
	if err != nil {
		return nil, fmt.Errorf("获取总资产数量失败: %v", err)
	}

	if totalAssets == 0 {
		return noData, nil
	}

	// 获取核心探针类型映射
	coreProbeTypeMap, err := getCoreProbeTypeMapFromSourceTypeMap()
	if err != nil {
		return nil, fmt.Errorf("获取核心探针类型映射失败: %v", err)
	}

	// 分析探针数据，检查核心探针覆盖情况
	coreProbeCategories := map[string]bool{
		"主动探测": false,
		"被动流量": false,
		"终端设备": false,
		"CMDB": false,
	}

	lowCoverageProbes := make([]string, 0)

	for _, probe := range results {
		// 计算覆盖度百分比：（独有+共有）/总资产数
		coverageCount := probe.UniqueCount + probe.ShareCount
		coveragePercentage := utils.CalculatePercentageRate(coverageCount, totalAssets)

		// 检查是否为核心探针
		if probeTypes, exists := coreProbeTypeMap[probe.USourceId]; exists {
			for _, probeType := range probeTypes {
				switch probeType {
				case "资产扫描":
					coreProbeCategories["主动探测"] = true
					if coveragePercentage < config.GetNormalInPercentage() {
						lowCoverageProbes = append(lowCoverageProbes, fmt.Sprintf("%s(主动探测)覆盖度%d%%", probe.ProbeType, coveragePercentage))
					}
				case "防火墙":
					coreProbeCategories["被动流量"] = true
					if coveragePercentage < config.GetNormalInPercentage() {
						lowCoverageProbes = append(lowCoverageProbes, fmt.Sprintf("%s(被动流量)覆盖度%d%%", probe.ProbeType, coveragePercentage))
					}
				case "主机安全":
					coreProbeCategories["终端设备"] = true
					if coveragePercentage < config.GetNormalInPercentage() {
						lowCoverageProbes = append(lowCoverageProbes, fmt.Sprintf("%s(终端设备)覆盖度%d%%", probe.ProbeType, coveragePercentage))
					}
				case "CMDB", "堡垒机":
					coreProbeCategories["CMDB"] = true
					if coveragePercentage < config.GetNormalInPercentage() {
						lowCoverageProbes = append(lowCoverageProbes, fmt.Sprintf("%s(CMDB)覆盖度%d%%", probe.ProbeType, coveragePercentage))
					}
				}
			}
		}
	}

	// 检查缺失的核心探针类型
	missingCoreProbes := make([]string, 0)
	for category, exists := range coreProbeCategories {
		if !exists {
			missingCoreProbes = append(missingCoreProbes, category)
		}
	}

	// 判断结果
	if len(missingCoreProbes) > 0 {
		return &system_configs.DataInterpretationResult{
			TagsStatus:     system_configs.DataInterpretationTagsStatusAbnormal,
			AnalysisResult: fmt.Sprintf("异常：缺少核心探针类型(%s)，请尽快完成探针数据的补充，以形成全面的台账资产", strings.Join(missingCoreProbes, "、")),
		}, nil
	}

	if len(lowCoverageProbes) > 0 {
		return &system_configs.DataInterpretationResult{
			TagsStatus:     system_configs.DataInterpretationTagsStatusAbnormal,
			AnalysisResult: fmt.Sprintf("异常：核心探针类型全部具备，但是存在核心探针数据覆盖度<%d%%的情况(%s)，需要提升探针数据覆盖度", config.ReferencePoint.NormalInPercentage, strings.Join(lowCoverageProbes, "、")),
		}, nil
	}

	// 正常情况
	return &system_configs.DataInterpretationResult{
		TagsStatus:     system_configs.DataInterpretationTagsStatusNormal,
		AnalysisResult: fmt.Sprintf("正常：当前核心探针全部具备且覆盖度均大于%d%%，整体数据采集状态良好，具备形成全面、准备资产台账的基础条件", config.ReferencePoint.NormalInPercentage),
	}, nil
}

// getCoreProbeTypeMapFromSourceTypeMap 基于getSourceTypeMap封装，获取核心探针类型映射
// 返回 sourceId -> []probeType 的映射关系
func getCoreProbeTypeMapFromSourceTypeMap() (map[uint64][]string, error) {
	// 复用coverage.go中的getSourceTypeMap方法
	sourceTypeMap, err := getSourceTypeMap()
	if err != nil {
		return nil, err
	}

	// 维度ID到探针类型名称的映射
	dimensionToTypeName := map[uint64]string{
		workbench.CmdbDimensionsID:         "CMDB",
		workbench.BastionHostDimensionsID:  "堡垒机",
		workbench.WafDimensionsID:          "防火墙",
		workbench.HostSecurityDimensionsID: "主机安全",
		workbench.ActiveDetectDimensionsID: "资产扫描",
	}

	// 构建 sourceId -> []probeType 的映射
	result := make(map[uint64][]string)
	for dimensionId, sourceIds := range sourceTypeMap {
		if typeName, exists := dimensionToTypeName[dimensionId]; exists {
			for _, sourceId := range sourceIds {
				if _, ok := result[sourceId]; !ok {
					result[sourceId] = make([]string, 0)
				}
				result[sourceId] = append(result[sourceId], typeName)
			}
		}
	}

	return result, nil
}

// 互联网漏洞TOP10解析
func InternetVulnerabilityTop10DataInterpretation(results []*asset.AssetTopResult) (*system_configs.DataInterpretationResult, error) {
	conf, err := system_configs.NewSystemConfigs().GetWorkbenchDataInterpretation(system_configs.InternetVulnerabilityTop10)
	if err != nil {
		return nil, err
	}

	externalCount, err := es.GetCount(assets.NewAssets().IndexName(), assets.NewAssets().GenQueryNoDeletedAndPurged().Must(elastic.NewTermQuery("network_type", assets.NetworkTypeExternal)))
	if err != nil {
		return nil, err
	}
	// 数据不足：无互联网漏洞TOP10数据
	if externalCount == 0 {
		return &system_configs.DataInterpretationResult{
			TagsStatus:     system_configs.DataInterpretationTagsStatusDataDeficiencies,
			AnalysisResult: "数据不足：无互联网资产，数据暂时不具备分析价值",
		}, nil
	}

	// 基于results中的TOP10互联网资产进行漏洞分析
	// 提取TOP10资产的IP地址列表（假设FieldValue是IP地址）
	var top10IPs []interface{}
	for _, result := range results {
		if result.FieldValue != "" {
			top10IPs = append(top10IPs, result.FieldValue)
		}
	}

	var highRiskCount int64 = 0
	var mediumRiskCount int64 = 0

	if len(top10IPs) > 0 {
		// 查询TOP10互联网资产关联的高危漏洞
		highRiskVulnQuery := elastic.NewBoolQuery().
			Must(elastic.NewTermsQuery("ip", top10IPs...)). // TOP10资产IP
			Must(elastic.NewTermsQuery("level", 3, 4)).     // 高危漏洞
			MustNot(elastic.NewExistsQuery("deleted_at"))   // 未删除

		// 这里需要根据实际的漏洞索引名称进行调整
		// 暂时使用资产索引，实际应该查询漏洞索引
		highRiskCount, err = es.GetCount(poc.NewPoc().IndexName(), highRiskVulnQuery) // 需要替换为实际的漏洞索引名
		if err != nil {
			return nil, fmt.Errorf("查询高危漏洞失败: %v", err)
		}

		// 查询TOP10互联网资产关联的中危漏洞
		mediumRiskVulnQuery := elastic.NewBoolQuery().
			Must(elastic.NewTermsQuery("ip", top10IPs...)). // TOP10资产IP
			Must(elastic.NewTermQuery("level", 2)).         // 中危漏洞
			MustNot(elastic.NewExistsQuery("deleted_at"))   // 未删除

		mediumRiskCount, err = es.GetCount(poc.NewPoc().IndexName(), mediumRiskVulnQuery) // 需要替换为实际的漏洞索引名
		if err != nil {
			return nil, fmt.Errorf("查询中危漏洞失败: %v", err)
		}
	}

	// 判断逻辑
	// 异常：TOP10互联网资产存在高危漏洞或者中危>=3个情况
	if highRiskCount > 0 {
		return &system_configs.DataInterpretationResult{
			TagsStatus:     system_configs.DataInterpretationTagsStatusAbnormal,
			AnalysisResult: fmt.Sprintf("异常：TOP10互联网资产存在%d个高危漏洞，互联网漏洞被利用概率远大于内网，需要重点关注", highRiskCount),
		}, nil
	}

	if mediumRiskCount >= conf.ReferencePoint.NormalInPercentage {
		return &system_configs.DataInterpretationResult{
			TagsStatus:     system_configs.DataInterpretationTagsStatusAbnormal,
			AnalysisResult: fmt.Sprintf("异常：TOP10互联网资产存在%d个中危漏洞(>=%d个)，互联网漏洞被利用概率远大于内网，需要重点关注", mediumRiskCount, conf.ReferencePoint.NormalInPercentage),
		}, nil
	}

	// 正常：全部TOP10互联网资产无高危漏洞且中危<3个
	return &system_configs.DataInterpretationResult{
		TagsStatus:     system_configs.DataInterpretationTagsStatusNormal,
		AnalysisResult: fmt.Sprintf("正常：全部TOP10互联网资产无高危漏洞且中危<%d个，TOP10互联网资产安全状态良好", conf.ReferencePoint.NormalInPercentage),
	}, nil
}

// BusinessVulTop5DataInterpretation 业务系统漏洞TOP5数据解读
// 传入的是业务系统漏洞TOP5数据 数据结构为：FieldValue: 业务系统名称 FieldCount: 漏洞数量
func BusinessVulTop5DataInterpretation(results []*asset.AssetTopResult) (*system_configs.DataInterpretationResult, error) {
	// 获取配置
	conf, err := system_configs.NewSystemConfigs().GetWorkbenchDataInterpretation(system_configs.BusinessVulTop5)
	if err != nil {
		return nil, err
	}

	// 1. 首先检查业务系统覆盖率（数据不足判断）
	// 查询所有有业务系统的资产数量
	businessQuery := elastic.NewBoolQuery().
		MustNot(
			elastic.NewExistsQuery("deleted_at"),
			elastic.NewExistsQuery("purged_at"),
		).
		Must(
			elastic.NewNestedQuery(
				"business",
				elastic.NewBoolQuery().Must(
					elastic.NewExistsQuery("business.system"),
				).MustNot(
					elastic.NewTermQuery("business.system", ""),
				),
			),
		)

	businessAssetCount, err := es.GetCount(assets.NewAssets().IndexName(), businessQuery)
	if err != nil {
		return nil, err
	}

	// 查询所有资产数量
	allAssetQuery := elastic.NewBoolQuery().MustNot(
		elastic.NewExistsQuery("deleted_at"),
		elastic.NewExistsQuery("purged_at"),
	)
	allAssetCount, err := es.GetCount(assets.NewAssets().IndexName(), allAssetQuery)
	if err != nil {
		return nil, err
	}

	// 计算业务系统覆盖率
	businessCoverageRate := utils.CalculatePercentageRate(businessAssetCount, allAssetCount)

	// 数据不足：业务系统覆盖率<60%
	if businessCoverageRate < conf.GetDataDeficienciesInPercentage() {
		return &system_configs.DataInterpretationResult{
			TagsStatus:     system_configs.DataInterpretationTagsStatusDataDeficiencies,
			AnalysisResult: fmt.Sprintf("当前业务系统覆盖率<%d%%，数据暂时不具备分析价值", conf.ReferencePoint.DataDeficienciesInPercentage),
		}, nil
	}

	// 2. 检查TOP业务系统中的高危和中危漏洞情况（使用聚合查询优化性能）
	if len(results) == 0 {
		// 空结果直接返回正常状态
		return &system_configs.DataInterpretationResult{
			TagsStatus:     system_configs.DataInterpretationTagsStatusNormal,
			AnalysisResult: fmt.Sprintf("全部TOP5业务系统无高危漏洞且中危<%d个，当前业务具备一定应对风险的能力，但是针对可对外访问的业务还需尽快完成漏洞修复", conf.ReferencePoint.NormalInPercentage),
		}, nil
	}

	// 提取业务系统名称列表
	businessNames := make([]interface{}, 0, len(results))
	for _, system := range results {
		if system.FieldValue != "" {
			businessNames = append(businessNames, system.FieldValue)
		}
	}

	if len(businessNames) == 0 {
		return &system_configs.DataInterpretationResult{
			TagsStatus:     system_configs.DataInterpretationTagsStatusNormal,
			AnalysisResult: fmt.Sprintf("全部TOP5业务系统无高危漏洞且中危<%d个，当前业务具备一定应对风险的能力，但是针对可对外访问的业务还需尽快完成漏洞修复", conf.ReferencePoint.NormalInPercentage),
		}, nil
	}

	// 使用聚合查询一次性获取所有业务系统的漏洞等级统计
	// 按业务系统名称聚合，然后按漏洞等级子聚合
	businessAgg := elastic.NewTermsAggregation().
		Field("business_name_tmp").
		Size(len(businessNames)).
		SubAggregation("level_stats",
			elastic.NewTermsAggregation().
				Field("level").
				Size(10), // 漏洞等级数量有限，10个足够
		)

	// 构建查询条件
	query := elastic.NewBoolQuery().
		Must(
			elastic.NewTermsQuery("business_name_tmp", businessNames...),
		).
		MustNot(
			elastic.NewExistsQuery("deleted_at"),
			elastic.NewExistsQuery("purged_at"),
		)

	// 执行聚合查询
	searchResult, err := es.GetEsClient().Search().
		Index(poc.NewPoc().IndexName()).
		Query(query).
		Aggregation("business_vuln_stats", businessAgg).
		Size(0). // 不需要返回具体文档，只要聚合结果
		Do(context.Background())
	if err != nil {
		return nil, fmt.Errorf("聚合查询业务系统漏洞统计失败: %v", err)
	}

	// 解析聚合结果，检查是否有异常
	hasHighRiskVuln := false
	hasMediumRiskGe3 := false

	if businessStatsAgg, found := searchResult.Aggregations.Terms("business_vuln_stats"); found {
		for _, businessBucket := range businessStatsAgg.Buckets {
			if levelStatsAgg, found := businessBucket.Terms("level_stats"); found {
				for _, levelBucket := range levelStatsAgg.Buckets {
					level := levelBucket.Key
					count := levelBucket.DocCount

					// 检查高危漏洞（Level = 3）
					if level == float64(3) && count > 0 {
						hasHighRiskVuln = true
						break
					}

					// 检查中危漏洞（Level = 2）
					if level == float64(2) && count >= 3 {
						hasMediumRiskGe3 = true
						break
					}
				}
			}

			// 如果已经发现异常，可以提前退出
			if hasHighRiskVuln || hasMediumRiskGe3 {
				break
			}
		}
	}

	// 3. 根据判断结果返回相应状态
	// 异常：TOP5业务系统存在高危漏洞或者中危>=3个情况
	if hasHighRiskVuln || hasMediumRiskGe3 {
		return &system_configs.DataInterpretationResult{
			TagsStatus:     system_configs.DataInterpretationTagsStatusAbnormal,
			AnalysisResult: fmt.Sprintf("TOP5业务系统存在高危漏洞或者中危>=%d个情况，需要对该业务系统关联资产进行重点整改", conf.ReferencePoint.NormalInPercentage),
		}, nil
	}

	// 正常：全部TOP5业务系统无高危漏洞且中危<3个
	return &system_configs.DataInterpretationResult{
		TagsStatus:     system_configs.DataInterpretationTagsStatusNormal,
		AnalysisResult: fmt.Sprintf("全部TOP5业务系统无高危漏洞且中危<%d个，当前业务具备一定应对风险的能力，但是针对可对外访问的业务还需尽快完成漏洞修复", conf.ReferencePoint.NormalInPercentage),
	}, nil
}

// 传入的数据是部门聚合数据，FieldValue是部门名称，FieldCount是资产数量，如果FieldValue为空，则表示该资产没有部门归属
// 正常：重要业务部门资产占比>=80%， （重要部门，指的是业务系统重要性在1级或2级的部门）
// 异常：非技术部门资产占比>75%或存在幽灵部门，（非技术部门，指的是业务部门名称不包含技术、研发、IT、信息、开发、测试、运维、安全等关键词的部门｜幽灵部门指的是业务部门不在部门表中的部门）
// 数据不足：部门标注完整度<20%或未标注部门重要程度 （部门标注完整度指的是有部门的资产占比<20% 或者所有部门都没有属于重要及以上的业务系统）
func BusinessDepartmentAssetCountDataInterpretation(results []*asset.AssetTopResult) (*system_configs.DataInterpretationResult, error) {
	// 获取配置
	conf, err := system_configs.NewSystemConfigs().GetWorkbenchDataInterpretation(system_configs.BusinessDepartmentAssetCount)
	if err != nil {
		return nil, err
	}

	// 计算总资产数量
	totalAssetCount := int64(0)
	for _, result := range results {
		totalAssetCount += result.FieldCount
	}

	// 获取所有资产数量（用于计算部门标注率）
	allAssetCount, err := es.GetCount(assets.NewAssets().IndexName(), elastic.NewBoolQuery().MustNot(
		elastic.NewExistsQuery("deleted_at"),
		elastic.NewExistsQuery("purged_at"),
	))
	if err != nil {
		return nil, err
	}

	// 计算部门标注完整度
	departmentCoverageRate := utils.CalculatePercentageRate(totalAssetCount, allAssetCount)

	// 数据不足：部门标注完整度<配置值
	// calcRate返回万分比，需要转换为百分比比较
	if departmentCoverageRate < conf.GetDataDeficienciesInPercentage() {
		return &system_configs.DataInterpretationResult{
			TagsStatus:     system_configs.DataInterpretationTagsStatusDataDeficiencies,
			AnalysisResult: fmt.Sprintf("部门标注完整度<%d%%，数据暂不具备分析价值", conf.ReferencePoint.DataDeficienciesInPercentage),
		}, nil
	}

	// 检查是否有重要业务系统存在（重要程度为1级或2级）
	query := elastic.NewBoolQuery()
	query.Must(elastic.NewTermsQuery("assets_attribute.important_types", 1, 2))
	query.MustNot(
		elastic.NewExistsQuery("deleted_at"),
		elastic.NewExistsQuery("purged_at"),
	)

	// 获取重要业务系统（重要程度为1级或2级）的部门列表
	importantBusinessSystems, _ := business_system.NewBusinessSystems().FindAllByQuery(
		context.Background(),
		query,
		[]string{"id", "business_name", "department_base"},
	)

	// 数据不足：未标注部门重要程度（没有重要及以上的业务系统）
	if len(importantBusinessSystems) == 0 {
		return &system_configs.DataInterpretationResult{
			TagsStatus:     system_configs.DataInterpretationTagsStatusDataDeficiencies,
			AnalysisResult: "未标注部门重要程度，数据暂不具备分析价值",
		}, nil
	}
	// 构建重要业务部门映射表
	importantDepartments := make(map[string]bool)
	for _, bs := range importantBusinessSystems {
		for _, dept := range bs.DepartmentBase {
			if dept != nil && dept.Name != "" {
				importantDepartments[dept.Name] = true
			}
		}
	}

	// 计算重要业务部门的资产数量
	importantDeptCount := int64(0)
	nonImportantDeptCount := int64(0)
	unknownDeptCount := int64(0)

	for _, result := range results {
		if result.FieldValue == "" {
			unknownDeptCount += result.FieldCount
			continue
		}

		if importantDepartments[result.FieldValue] {
			importantDeptCount += result.FieldCount
		} else {
			nonImportantDeptCount += result.FieldCount
		}
	}

	// 计算重要业务部门资产占比
	importantDeptRate := utils.CalculatePercentageRate(importantDeptCount, totalAssetCount)

	// 正常：重要业务部门资产占比>=配置值（默认80%）
	// calcRate返回万分比，需要转换为百分比比较
	if importantDeptRate >= conf.GetNormalInPercentage() {
		return &system_configs.DataInterpretationResult{
			TagsStatus:     system_configs.DataInterpretationTagsStatusNormal,
			AnalysisResult: fmt.Sprintf("重要业务部门资产占比>=%d%%，部门资产分布合理", conf.ReferencePoint.NormalInPercentage),
		}, nil
	}

	// 其他情况为正常
	return &system_configs.DataInterpretationResult{
		TagsStatus:     system_configs.DataInterpretationTagsStatusNormal,
		AnalysisResult: fmt.Sprintf("重要业务部门资产占比<%d%%，建议加强重要部门的资产管理", conf.ReferencePoint.NormalInPercentage),
	}, nil
}

// 正常：待确认业务系统关联资产<=总资产10%，
// 异常：待确认业务系统关联资产>总资产10%，或者存在3个以上重要业务系统为待确认
// 数据不足：有业务系统的占比<60%
func BusinessAssetStatisticsDataInterpretation(results []*asset.AssetTopResult) (*system_configs.DataInterpretationResult, error) {
	//待确认资产数量
	pendingAssetCount := 0
	//有业务系统的资产数量（可信+待确认+黑名单）
	businessAssetCount := 0
	// 总资产数量
	totalAssetCount := 0
	for _, result := range results {
		totalAssetCount += int(result.FieldCount)
		if result.FieldValue == "2" {
			pendingAssetCount = int(result.FieldCount)
		}
		// 有业务系统的资产：可信(1) + 待确认(2) + 黑名单(3)，不包括缺失业务系统("")
		if result.FieldValue != "" {
			businessAssetCount += int(result.FieldCount)
		}
	}

	conf, err := system_configs.NewSystemConfigs().GetWorkbenchDataInterpretation(system_configs.BusinessAssetStatistics)
	if err != nil {
		return nil, err
	}

	if totalAssetCount == 0 {
		return &system_configs.DataInterpretationResult{
			TagsStatus:     system_configs.DataInterpretationTagsStatusDataDeficiencies,
			AnalysisResult: fmt.Sprintf("数据不足：当前资产关联到业务系统比例<%d%%，数据暂不具备分析价值", conf.ReferencePoint.DataDeficienciesInPercentage),
		}, nil
	}
	// 数据不足：有业务系统的占比<60%
	relationRate := utils.CalculatePercentageRate(int64(businessAssetCount), int64(totalAssetCount))
	if relationRate < conf.GetDataDeficienciesInPercentage() {
		return &system_configs.DataInterpretationResult{
			TagsStatus:     system_configs.DataInterpretationTagsStatusDataDeficiencies,
			AnalysisResult: fmt.Sprintf("资产关联率<%d%%，数据暂不具备分析价值", conf.ReferencePoint.DataDeficienciesInPercentage),
		}, nil
	}

	// 存在3个以上重要业务系统为待确认
	//查询待确认重要业务系统数量
	query := elastic.NewBoolQuery()
	query.Must(elastic.NewTermQuery("status", 2))
	query.Must(elastic.NewTermsQuery("assets_attribute.important_types", 1, 2))
	query.MustNot(
		elastic.NewExistsQuery("deleted_at"),
		elastic.NewExistsQuery("purged_at"),
	)
	count, err := es.GetCount(business_system.NewBusinessSystems().IndexName(), query)
	if err != nil {
		return nil, err
	}
	if count > 3 {
		return &system_configs.DataInterpretationResult{
			TagsStatus:     system_configs.DataInterpretationTagsStatusAbnormal,
			AnalysisResult: "存在3个以上重要业务系统为待确认，需要重点关注重要业务系统的状态，尽快完成确权",
		}, nil
	}

	// 计算待确认资产占比
	pendingRate := utils.CalculatePercentageRate(int64(pendingAssetCount), int64(totalAssetCount))

	//正常：待确认系统关联资产<=总资产10%
	if pendingRate <= conf.GetNormalInPercentage() {
		return &system_configs.DataInterpretationResult{
			TagsStatus:     system_configs.DataInterpretationTagsStatusNormal,
			AnalysisResult: fmt.Sprintf("待确认系统关联资产<=%d%%，业务管理状态正常", conf.ReferencePoint.NormalInPercentage),
		}, nil
	}

	// 异常：待确认系统关联资产>总资产10%
	if pendingRate > conf.GetNormalInPercentage() {
		return &system_configs.DataInterpretationResult{
			TagsStatus:     system_configs.DataInterpretationTagsStatusAbnormal,
			AnalysisResult: fmt.Sprintf("待确认系统关联资产>%d%%，需要进行业务系统的确权", conf.ReferencePoint.NormalInPercentage),
		}, nil
	}
	return &system_configs.DataInterpretationResult{
		TagsStatus:     system_configs.DataInterpretationTagsStatusNormal,
		AnalysisResult: fmt.Sprintf("待确认系统关联资产<=%d%%，业务管理状态正常", conf.ReferencePoint.NormalInPercentage),
	}, nil
}
