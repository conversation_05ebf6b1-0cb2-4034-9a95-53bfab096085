package data_board

import (
	"testing"

	"fobrain/fobrain/app/repository/asset"
	"fobrain/models/mysql/system_configs"

	"github.com/stretchr/testify/assert"
)

func TestPieTransFormData_WithPercentage(t *testing.T) {
	tests := []struct {
		name           string
		assetData      []*asset.AssetTopResult
		searchConfig   *searchConfig
		expectedResult bool // 是否期望有百分比数据
	}{
		{
			name: "启用占比计算",
			assetData: []*asset.AssetTopResult{
				{
					FieldValue:      "Apache",
					FieldCount:      100,
					FieldPercentage: 1000, // 10.00%
				},
				{
					FieldValue:      "Nginx",
					FieldCount:      200,
					FieldPercentage: 2000, // 20.00%
				},
			},
			searchConfig: &searchConfig{
				param:            system_configs.AssetComponentProportion,
				enablePercentage: true,
			},
			expectedResult: true,
		},
		{
			name: "未启用占比计算",
			assetData: []*asset.AssetTopResult{
				{
					FieldValue:      "Apache",
					FieldCount:      100,
					FieldPercentage: 1000,
				},
			},
			searchConfig: &searchConfig{
				param:            system_configs.AssetComponentProportion,
				enablePercentage: false,
			},
			expectedResult: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := pieTransFormData(tt.assetData, tt.searchConfig, "", "测试标题", nil)
			assert.NoError(t, err)

			pieChartData, ok := result.(*PieChartData)
			assert.True(t, ok, "返回结果应该是PieChartData类型")

			for i, data := range pieChartData.ChartData.Data {
				if tt.expectedResult {
					assert.Equal(t, tt.assetData[i].FieldPercentage, data.Percentage, "应该包含百分比数据")
				} else {
					assert.Equal(t, int64(0), data.Percentage, "不应该包含百分比数据")
				}
			}
		})
	}
}

func TestHandleDescOnly_WithPercentage(t *testing.T) {
	tests := []struct {
		name           string
		assetData      []*asset.AssetTopResult
		searchConfig   *searchConfig
		expectedResult bool // 是否期望有百分比数据
	}{
		{
			name: "启用占比计算_单序列柱状图",
			assetData: []*asset.AssetTopResult{
				{
					FieldValue:      "web",
					FieldCount:      100,
					FieldPercentage: 1000, // 10.00%
				},
				{
					FieldValue:      "database",
					FieldCount:      200,
					FieldPercentage: 2000, // 20.00%
				},
			},
			searchConfig: &searchConfig{
				desc: []*KeyValuePair{
					{Key: "web", Value: "Web服务"},
					{Key: "database", Value: "数据库"},
				},
				enablePercentage: true,
			},
			expectedResult: true,
		},
		{
			name: "未启用占比计算_单序列柱状图",
			assetData: []*asset.AssetTopResult{
				{
					FieldValue:      "web",
					FieldCount:      100,
					FieldPercentage: 1000,
				},
			},
			searchConfig: &searchConfig{
				desc: []*KeyValuePair{
					{Key: "web", Value: "Web服务"},
				},
				enablePercentage: false,
			},
			expectedResult: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := handleDescOnly(tt.assetData, tt.searchConfig)
			assert.NoError(t, err)

			if tt.expectedResult {
				assert.NotEmpty(t, result.ChartData.SeriesData[0].Percentages, "应该包含百分比数据")
				for i, percentage := range result.ChartData.SeriesData[0].Percentages {
					if i < len(tt.assetData) {
						assert.Equal(t, tt.assetData[i].FieldPercentage, percentage, "百分比数据应该匹配")
					}
				}
			} else {
				assert.Empty(t, result.ChartData.SeriesData[0].Percentages, "不应该包含百分比数据")
			}
		})
	}
}

func TestHandleNoDescNoXDesc_WithPercentage(t *testing.T) {
	tests := []struct {
		name           string
		assetData      []*asset.AssetTopResult
		searchConfig   *searchConfig
		expectedResult bool // 是否期望有百分比数据
	}{
		{
			name: "启用占比计算_默认处理",
			assetData: []*asset.AssetTopResult{
				{
					FieldValue:      "Apache",
					FieldCount:      100,
					FieldPercentage: 1000, // 10.00%
				},
				{
					FieldValue:      "Nginx",
					FieldCount:      150,
					FieldPercentage: 1500, // 15.00%
				},
			},
			searchConfig: &searchConfig{
				enablePercentage: true,
			},
			expectedResult: true,
		},
		{
			name: "未启用占比计算_默认处理",
			assetData: []*asset.AssetTopResult{
				{
					FieldValue:      "Apache",
					FieldCount:      100,
					FieldPercentage: 1000,
				},
			},
			searchConfig: &searchConfig{
				enablePercentage: false,
			},
			expectedResult: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := handleNoDescNoXDesc(tt.assetData, tt.searchConfig)
			assert.NoError(t, err)

			if tt.expectedResult {
				assert.NotEmpty(t, result.ChartData.SeriesData[0].Percentages, "应该包含百分比数据")
				// 验证百分比数据的正确性
				for i, data := range tt.assetData {
					if i < len(result.ChartData.SeriesData[0].Percentages) {
						assert.Equal(t, data.FieldPercentage, result.ChartData.SeriesData[0].Percentages[i], "百分比数据应该匹配")
					}
				}
			} else {
				assert.Empty(t, result.ChartData.SeriesData[0].Percentages, "不应该包含百分比数据")
			}
		})
	}
}

func TestInitSeries_WithPercentage(t *testing.T) {
	xDesc := []*KeyValuePair{
		{Key: "type1", Value: "类型1", Sort: 1},
		{Key: "type2", Value: "类型2", Sort: 2},
	}
	xLen := 3

	series, mapping := initSeries(xDesc, xLen)

	// 验证系列数量
	assert.Equal(t, 2, len(series), "应该创建2个系列")

	// 验证每个系列的结构
	for _, s := range series {
		assert.Equal(t, xLen, len(s.Data), "数据数组长度应该正确")
		assert.Equal(t, xLen, len(s.Percentages), "百分比数组长度应该正确")
		assert.NotEmpty(t, s.Name, "系列名称不应该为空")
	}

	// 验证映射关系
	assert.Equal(t, 2, len(mapping), "映射关系数量应该正确")
}

func TestPercentageConfiguration(t *testing.T) {
	tests := []struct {
		name                   string
		enablePercentage       bool
		percentageBasedOnTotal bool
		description            string
	}{
		{
			name:                   "启用占比_基于总数",
			enablePercentage:       true,
			percentageBasedOnTotal: true,
			description:            "应该启用占比计算并基于总资产数",
		},
		{
			name:                   "启用占比_基于TOP数据",
			enablePercentage:       true,
			percentageBasedOnTotal: false,
			description:            "应该启用占比计算并基于TOP数据总和",
		},
		{
			name:                   "禁用占比",
			enablePercentage:       false,
			percentageBasedOnTotal: false,
			description:            "应该禁用占比计算",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			config := &searchConfig{
				enablePercentage:       tt.enablePercentage,
				percentageBasedOnTotal: tt.percentageBasedOnTotal,
			}

			assert.Equal(t, tt.enablePercentage, config.enablePercentage, "enablePercentage配置应该正确")
			assert.Equal(t, tt.percentageBasedOnTotal, config.percentageBasedOnTotal, "percentageBasedOnTotal配置应该正确")
		})
	}
}

// 测试资产组件占比的配置
func TestAssetComponentProportionConfig(t *testing.T) {
	// 验证资产组件占比的配置是否正确设置
	config, exists := SearchConfigs[system_configs.AssetComponentProportion]
	assert.True(t, exists, "资产组件占比配置应该存在")
	assert.True(t, config.enablePercentage, "资产组件占比应该启用占比计算")
	assert.True(t, config.percentageBasedOnTotal, "资产组件占比应该基于总数计算")
}

// 基准测试：占比计算性能
func BenchmarkPieTransFormData_WithPercentage(b *testing.B) {
	assetData := make([]*asset.AssetTopResult, 10)
	for i := 0; i < 10; i++ {
		assetData[i] = &asset.AssetTopResult{
			FieldValue:      "component" + string(rune(i)),
			FieldCount:      int64(100 + i*50),
			FieldPercentage: int64(1000 + i*500),
		}
	}

	searchConfig := &searchConfig{
		param:            system_configs.AssetComponentProportion,
		enablePercentage: true,
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := pieTransFormData(assetData, searchConfig, "", "测试标题", nil)
		if err != nil {
			b.Fatal(err)
		}
	}
}
