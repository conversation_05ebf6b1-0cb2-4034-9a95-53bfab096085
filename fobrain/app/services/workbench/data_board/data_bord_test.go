package data_board

import (
	"testing"

	"fobrain/fobrain/app/repository/asset"
	"fobrain/fobrain/app/services/workbench"
	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/models/mysql/system_configs"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/stretchr/testify/assert"
)

// 测试 GetDataProcessor
func TestService_GetDataProcessor(t *testing.T) {
	s := NewService()

	// 支持的类型
	processor, err := s.GetDataProcessor("pie")
	assert.NoError(t, err)
	assert.NotNil(t, processor)

	// 不支持的类型
	processor, err = s.GetDataProcessor("unknown")
	assert.Error(t, err)
	assert.Nil(t, processor)
}

// 测试 transformData 逻辑
func Test_transformData(t *testing.T) {
	// mock 资产数据
	mockAssets := []*asset.AssetTopResult{
		{FieldValue: "A", FieldCount: 10},
		{FieldValue: "B", FieldCount: 20},
	}
	// mock 查询配置
	mockQuery := &searchConfig{
		field: "name",
	}
	// patch 依赖（如有外部依赖可补充）
	// 这里只做简单调用
	res, err := transformData(mockAssets, mockQuery, "1", "", nil)
	assert.NoError(t, err)
	assert.NotNil(t, res)
}

// 测试 transformData 四种分支
func Test_transformData_all_cases(t *testing.T) {
	// 1. desc + xDesc
	mockAssets := []*asset.AssetTopResult{
		{
			FieldValue: "可信", FieldCount: 5,
			Children: []*asset.AssetTopResult{
				{FieldValue: "内网资产", FieldCount: 2},
				{FieldValue: "互联网资产", FieldCount: 3},
			},
		},
		{
			FieldValue: "可疑", FieldCount: 8,
			Children: []*asset.AssetTopResult{
				{FieldValue: "内网资产", FieldCount: 1},
				{FieldValue: "互联网资产", FieldCount: 7},
			},
		},
	}
	desc := []*KeyValuePair{{Key: "可信", Value: "可信", Sort: 1}, {Key: "可疑", Value: "可疑", Sort: 2}}
	xDesc := []*KeyValuePair{{Key: "内网资产", Value: "内网资产", Sort: 1}, {Key: "互联网资产", Value: "互联网资产", Sort: 2}}
	cfg := &searchConfig{desc: desc, xDesc: xDesc}
	res, err := transformData(mockAssets, cfg, "1", "", nil)
	assert.NoError(t, err)
	assert.NotNil(t, res)
	// xLabels 顺序: ["可信", "可疑"]
	// seriesList 顺序: ["内网资产", "互联网资产"]
	assert.Equal(t, 2, len(res.(*ResChatData).ChartData.SeriesData))
	assert.Equal(t, "内网资产", res.(*ResChatData).ChartData.SeriesData[0].Name)
	assert.Equal(t, []int64{2, 1}, res.(*ResChatData).ChartData.SeriesData[0].Data)
	assert.Equal(t, "互联网资产", res.(*ResChatData).ChartData.SeriesData[1].Name)
	assert.Equal(t, []int64{3, 7}, res.(*ResChatData).ChartData.SeriesData[1].Data)

	// 2. desc 无 xDesc
	mockAssets2 := []*asset.AssetTopResult{
		{FieldValue: "可信", FieldCount: 10},
		{FieldValue: "黑名单", FieldCount: 20},
	}
	desc2 := []*KeyValuePair{{Key: "可信", Value: "可信", Sort: 1}, {Key: "黑名单", Value: "黑名单", Sort: 2}}
	cfg2 := &searchConfig{desc: desc2}
	res2, err := transformData(mockAssets2, cfg2, "1", "", nil)
	assert.NoError(t, err)
	assert.NotNil(t, res2)
	assert.Equal(t, 1, len(res2.(*ResChatData).ChartData.SeriesData))
	assert.Equal(t, "数量", res2.(*ResChatData).ChartData.SeriesData[0].Name)
	assert.Equal(t, []int64{10, 20}, res2.(*ResChatData).ChartData.SeriesData[0].Data)

	// 3. 无 desc 无 xDesc
	mockAssets3 := []*asset.AssetTopResult{{FieldValue: "A", FieldCount: 10}, {FieldValue: "B", FieldCount: 20}}
	cfg3 := &searchConfig{}
	res3, err := transformData(mockAssets3, cfg3, "1", "", nil)
	assert.NoError(t, err)
	assert.NotNil(t, res3)
	assert.Equal(t, 1, len(res3.(*ResChatData).ChartData.SeriesData))
	assert.Equal(t, "数量", res3.(*ResChatData).ChartData.SeriesData[0].Name)
	assert.Equal(t, []int64{10, 20}, res3.(*ResChatData).ChartData.SeriesData[0].Data)

	// 4. 无 desc 有 xDesc
	mockAssets4 := []*asset.AssetTopResult{
		{
			FieldValue: "A", FieldCount: 0,
			Children: []*asset.AssetTopResult{
				{FieldValue: "X", FieldCount: 1},
				{FieldValue: "Y", FieldCount: 2},
			},
		},
		{
			FieldValue: "B", FieldCount: 0,
			Children: []*asset.AssetTopResult{
				{FieldValue: "X", FieldCount: 3},
				{FieldValue: "Y", FieldCount: 4},
			},
		},
	}
	xDesc4 := []*KeyValuePair{{Key: "X", Value: "X", Sort: 1}, {Key: "Y", Value: "Y", Sort: 2}}
	cfg4 := &searchConfig{xDesc: xDesc4}
	res4, err := transformData(mockAssets4, cfg4, "1", "", nil)
	assert.NoError(t, err)
	assert.NotNil(t, res4)
	// xLabels: ["A", "B"]，seriesList: ["X", "Y"]
	assert.Equal(t, 2, len(res4.(*ResChatData).ChartData.SeriesData))
	assert.Equal(t, "X", res4.(*ResChatData).ChartData.SeriesData[0].Name)
	assert.Equal(t, []int64{1, 3}, res4.(*ResChatData).ChartData.SeriesData[0].Data)
	assert.Equal(t, "Y", res4.(*ResChatData).ChartData.SeriesData[1].Name)
	assert.Equal(t, []int64{2, 4}, res4.(*ResChatData).ChartData.SeriesData[1].Data)
}

// 测试 GetConfigList
func TestService_GetConfigList(t *testing.T) {
	s := NewService()
	list, err := s.GetConfigList("data_board_assets_config")
	assert.NoError(t, err)
	assert.NotNil(t, list)
}

// 测试 GetDataByRequest
func TestService_GetDataByRequest(t *testing.T) {
	s := NewService()
	// patch asset.AssetTopProcess，避免真实依赖
	patches := gomonkey.ApplyFunc(asset.AssetTopProcess, func(_ asset.AssetTopConfig) ([]*asset.AssetTopResult, error) {
		return []*asset.AssetTopResult{{FieldValue: "A", FieldCount: 10}}, nil
	})
	defer patches.Reset()
	params := &RequestParams{
		Params: []*condition{{Key: "business_status_distribution", Type: "pie"}},
	}
	res, err := s.GetDataByRequest(params)
	assert.NoError(t, err)
	assert.NotNil(t, res)
}

// 测试 GetDataByRequest，mock mysql 依赖
func TestService_GetDataByRequest_WithSqlMock(t *testing.T) {
	s := NewService()
	mockDb := testcommon.InitSqlMock()
	defer mockDb.Close()

	// patch asset.AssetTopProcess，避免真实依赖
	patches := gomonkey.ApplyFunc(asset.AssetTopProcess, func(_ asset.AssetTopConfig) ([]*asset.AssetTopResult, error) {
		return []*asset.AssetTopResult{{FieldValue: "A", FieldCount: 10}}, nil
	})
	defer patches.Reset()
	params := &RequestParams{
		Params: []*condition{{Key: "business_status_distribution", Type: "pie"}},
	}
	res, err := s.GetDataByRequest(params)
	assert.NoError(t, err)
	assert.NotNil(t, res)
	assert.NoError(t, mockDb.ExpectationsWereMet())
}

// 测试 getSearchConfigByRequest
func TestService_getSearchConfigByRequest(t *testing.T) {
	s := NewService()
	// 支持的 key
	cfg, err := s.getSearchConfigByRequest("ownerless_assets_statistics")
	assert.NoError(t, err)
	assert.NotNil(t, cfg)
	assert.Equal(t, "ownerless_assets_statistics", cfg.param)

	// 不支持的 key
	cfg, err = s.getSearchConfigByRequest("not_exist_key")
	assert.Error(t, err)
	assert.Nil(t, cfg)
}

// 测试 processPieOrTopData
func TestService_processPieOrTopData(t *testing.T) {
	s := NewService()
	// patch asset.AssetTopProcess，避免真实依赖
	patches := gomonkey.ApplyFunc(asset.AssetTopProcess, func(_ asset.AssetTopConfig) ([]*asset.AssetTopResult, error) {
		return []*asset.AssetTopResult{{FieldValue: "A", FieldCount: 10}}, nil
	})
	defer patches.Reset()

	cfg := &searchConfig{
		field:        "name",
		size:         10,
		nestedPath:   "",
		isDualNested: false,
	}
	res, err := s.processPieOrTopData(cfg, nil)
	assert.NoError(t, err)
	assert.NotNil(t, res)
	assert.Equal(t, 1, len(res))
	assert.Equal(t, "A", res[0].FieldValue)
	assert.Equal(t, int64(10), res[0].FieldCount)
}

// 测试 GetDataByRequest，覆盖 source_sort_top5、rule_infos_statistics
func TestService_GetDataByRequest_allKeys(t *testing.T) {
	s := NewService()

	// Mock ES 连接，避免空指针错误
	mockEs := testcommon.NewMockServer()
	// 注册 /asset/_count 路径的 mock 返回，避免 404
	mockEs.Register("/asset/_count", map[string]interface{}{
		"count": 100,
		"_shards": map[string]interface{}{
			"total": 1, "successful": 1, "skipped": 0, "failed": 0,
		},
	})
	defer mockEs.Close()

	// Mock 数据库连接以处理可能的查询
	mockDb := testcommon.InitSqlMock()
	defer mockDb.Close()

	// 为可能的查询添加期望
	mockDb.ExpectQuery("SELECT value FROM `system_configs` WHERE `key` = ?").
		WithArgs(sqlmock.AnyArg()).
		WillReturnRows(sqlmock.NewRows([]string{"value"}).AddRow("{}"))

	// 为第二个查询添加期望
	mockDb.ExpectQuery("SELECT value FROM `system_configs` WHERE `key` = ?").
		WithArgs(sqlmock.AnyArg()).
		WillReturnRows(sqlmock.NewRows([]string{"value"}).AddRow("{}"))

	patchAssetTopProcess := gomonkey.ApplyFunc(asset.AssetTopProcess, func(config asset.AssetTopConfig) ([]*asset.AssetTopResult, error) {
		return []*asset.AssetTopResult{{FieldValue: "A", FieldCount: 10}}, nil
	})
	defer patchAssetTopProcess.Reset()

	// Mock ProbeAssetContributionTop 方法来避免ES查询错误
	patchProbeAssetContribution := gomonkey.ApplyFunc(asset.ProbeAssetContributionTop, func() ([]*asset.ProbeContributionItem, error) {
		return []*asset.ProbeContributionItem{
			{SourceId: "1", SubAllCount: 100},
		}, nil
	})
	defer patchProbeAssetContribution.Reset()

	// Mock 数据解读函数
	patchProbeDataInterpretation := gomonkey.ApplyFunc(workbench.ProbeAssetContributionDataInterpretation,
		func(results []*asset.ProbeContributionItem) (*system_configs.DataInterpretationResult, error) {
			return &system_configs.DataInterpretationResult{
				TagsStatus:     1,
				AnalysisResult: "测试结果",
			}, nil
		})
	defer patchProbeDataInterpretation.Reset()

	// 测试两个key
	keys := []string{system_configs.ProbeAssetContributionDegree, system_configs.AssetComponentProportion}
	for _, key := range keys {
		params := &RequestParams{
			Params: []*condition{{Key: key, Type: "pie"}},
		}
		res, err := s.GetDataByRequest(params)
		assert.NoError(t, err, key)
		assert.NotNil(t, res, key)
	}
}

// TestModifyChatData 测试 modifyChatData 函数
func TestModifyChatData(t *testing.T) {
	tests := []struct {
		name        string
		data        *ResChatData
		cfg         *searchConfig
		expectError bool
		validate    func(t *testing.T, result *ResChatData)
	}{
		{
			name: "横向排行数据-size小于100",
			data: &ResChatData{
				ChartData: &ChartData{
					XAxisLabels: []string{"A", "B", "C"},
					YAxisLabels: []string{},
					SeriesData:  []*seriesData{{Name: "数量", Data: []int64{1, 2, 3}}},
				},
			},
			cfg: &searchConfig{param: "test_top_data", size: 5},
			expectError: false,
			validate: func(t *testing.T, result *ResChatData) {
				// size < 100 时，XAxisLabels 会移动到 YAxisLabels
				assert.Empty(t, result.ChartData.XAxisLabels, "XAxisLabels应该为空")
				assert.Equal(t, []string{"A", "B", "C"}, result.ChartData.YAxisLabels, "YAxisLabels应该包含原来的XAxisLabels")
			},
		},
		{
			name: "普通数据-size大于100",
			data: &ResChatData{
				ChartData: &ChartData{
					XAxisLabels: []string{"X", "Y"},
					YAxisLabels: []string{"Series1"},
					SeriesData:  []*seriesData{{Name: "数量", Data: []int64{10, 20}}},
				},
			},
			cfg: &searchConfig{param: "normal_data", size: 200},
			expectError: false,
			validate: func(t *testing.T, result *ResChatData) {
				assert.Equal(t, []string{"X", "Y"}, result.ChartData.XAxisLabels, "XAxisLabels应该保持不变")
				assert.Equal(t, []string{"Series1"}, result.ChartData.YAxisLabels, "YAxisLabels应该保持不变")
			},
		},
		{
			name: "空数据",
			data: &ResChatData{
				ChartData: &ChartData{
					XAxisLabels: []string{},
					YAxisLabels: []string{},
					SeriesData:  []*seriesData{},
				},
			},
			cfg: &searchConfig{param: "empty_data", size: 200},
			expectError: false,
			validate: func(t *testing.T, result *ResChatData) {
				assert.Empty(t, result.ChartData.XAxisLabels, "XAxisLabels应该为空")
				assert.Empty(t, result.ChartData.YAxisLabels, "YAxisLabels应该为空")
				assert.Empty(t, result.ChartData.SeriesData, "SeriesData应该为空")
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := modifyChatData(tt.data, tt.cfg)

			if tt.expectError {
				assert.Error(t, err, "应该返回错误")
			} else {
				assert.NoError(t, err, "不应该返回错误")
				assert.NotNil(t, result, "结果不应该为nil")
				tt.validate(t, result)
			}
		})
	}
}

// 测试 ownerless_assets_proportion 字段key转换
func TestService_GetDataByRequest_ownerless_assets_proportion_key_convert(t *testing.T) {
	// mock ES，防止未初始化导致 panic
	mockEs := testcommon.NewMockServer()
	// 注册 /asset/_count 路径的 mock 返回，避免 404
	mockEs.Register("/asset/_count", map[string]interface{}{
		"count": 10,
		"_shards": map[string]interface{}{
			"total": 1, "successful": 1, "skipped": 0, "failed": 0,
		},
	})
	defer mockEs.Close()
	s := NewService()
	patchTransform := gomonkey.ApplyFunc(transformData, func(assetData []*asset.AssetTopResult, queryFields *searchConfig, flag string, title string, interpretation *system_configs.DataInterpretationResult) (any, error) {
		return &ResChatData{Key: queryFields.param, ChartData: &ChartData{XAxisLabels: []string{"有归属资产", "无法匹配负责人", "缺失负责人"}, SeriesData: []*seriesData{{Name: "数量", Data: []int64{5, 3, 2}}}}}, nil
	})
	defer patchTransform.Reset()
	mockDb := testcommon.InitSqlMock()
	defer mockDb.Close()
	mockDb.ExpectQuery("SELECT value FROM `system_configs` WHERE `key` = ?").
		WithArgs(system_configs.OwnerlessAssetsProportion).
		WillReturnRows(sqlmock.NewRows([]string{"value"}).AddRow("{\"reference_point\":{\"normal_in_percentage\":10,\"abnormal_in_percentage\":20,\"data_deficiencies_in_percentage\":30}}"))
	params := &RequestParams{
		Params: []*condition{{Key: "ownerless_assets_proportion", Type: "pie"}},
	}
	res, err := s.GetDataByRequest(params)
	assert.NoError(t, err)
	assert.NotNil(t, res)
}

func TestSortAndReorderLabels(t *testing.T) {
	tests := []struct {
		name           string
		series         []*seriesData
		xLabels        []string
		desc           bool
		expectedLabels []string
		expectedSeries []*seriesData
	}{
		{
			name: "降序排序",
			series: []*seriesData{
				{Data: []int64{1, 2, 3}},
				{Data: []int64{4, 5, 6}},
				{Data: []int64{7, 8, 9}},
			},
			xLabels: []string{"A", "B", "C"},
			desc:    true,
			expectedLabels: []string{"C", "B", "A"},
			expectedSeries: []*seriesData{
				{Data: []int64{3, 2, 1}},
				{Data: []int64{6, 5, 4}},
				{Data: []int64{9, 8, 7}},
			},
		},
		{
			name: "升序排序",
			series: []*seriesData{
				{Data: []int64{3, 2, 1}},
				{Data: []int64{6, 5, 4}},
			},
			xLabels: []string{"C", "B", "A"},
			desc:    false,
			expectedLabels: []string{"A", "B", "C"},
			expectedSeries: []*seriesData{
				{Data: []int64{1, 2, 3}},
				{Data: []int64{4, 5, 6}},
			},
		},
		{
			name: "单个数据点",
			series: []*seriesData{
				{Data: []int64{5}},
			},
			xLabels: []string{"X"},
			desc:    true,
			expectedLabels: []string{"X"},
			expectedSeries: []*seriesData{
				{Data: []int64{5}},
			},
		},
		{
			name: "空数据",
			series: []*seriesData{
				{Data: []int64{}},
			},
			xLabels: []string{},
			desc:    true,
			expectedLabels: []string{},
			expectedSeries: []*seriesData{
				{Data: []int64{}},
			},
		},
		{
			name: "相同值排序",
			series: []*seriesData{
				{Data: []int64{5, 5, 5}},
			},
			xLabels: []string{"A", "B", "C"},
			desc:    true,
			expectedLabels: []string{"A", "B", "C"}, // 相同值时保持原顺序
			expectedSeries: []*seriesData{
				{Data: []int64{5, 5, 5}},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 复制输入数据以避免修改原始测试数据
			series := make([]*seriesData, len(tt.series))
			for i, s := range tt.series {
				series[i] = &seriesData{
					Data: make([]int64, len(s.Data)),
				}
				copy(series[i].Data, s.Data)
			}
			xLabels := make([]string, len(tt.xLabels))
			copy(xLabels, tt.xLabels)

			result := sortAndReorderLabels(series, xLabels, tt.desc)

			assert.Equal(t, tt.expectedLabels, result, "标签排序结果不符合预期")

			for i, expectedSeries := range tt.expectedSeries {
				assert.Equal(t, expectedSeries.Data, series[i].Data, "系列数据排序结果不符合预期")
			}
		})
	}
}

// TestSortKVList 测试 sortKVList 函数
func TestSortKVList(t *testing.T) {
	tests := []struct {
		name     string
		input    []*KeyValuePair
		expected []kvWithSort
	}{
		{
			name: "正常排序",
			input: []*KeyValuePair{
				{Key: "key3", Value: "value3", Sort: 3},
				{Key: "key1", Value: "value1", Sort: 1},
				{Key: "key2", Value: "value2", Sort: 2},
			},
			expected: []kvWithSort{
				{Key: "key1", Value: "value1", Sort: 1},
				{Key: "key2", Value: "value2", Sort: 2},
				{Key: "key3", Value: "value3", Sort: 3},
			},
		},
		{
			name:     "空列表",
			input:    []*KeyValuePair{},
			expected: []kvWithSort{},
		},
		{
			name: "单个元素",
			input: []*KeyValuePair{
				{Key: "key1", Value: "value1", Sort: 1},
			},
			expected: []kvWithSort{
				{Key: "key1", Value: "value1", Sort: 1},
			},
		},
		{
			name: "相同排序值",
			input: []*KeyValuePair{
				{Key: "key2", Value: "value2", Sort: 1},
				{Key: "key1", Value: "value1", Sort: 1},
			},
			expected: []kvWithSort{
				{Key: "key2", Value: "value2", Sort: 1},
				{Key: "key1", Value: "value1", Sort: 1},
			},
		},
		{
			name: "负数排序",
			input: []*KeyValuePair{
				{Key: "key1", Value: "value1", Sort: -1},
				{Key: "key2", Value: "value2", Sort: 0},
				{Key: "key3", Value: "value3", Sort: 1},
			},
			expected: []kvWithSort{
				{Key: "key1", Value: "value1", Sort: -1},
				{Key: "key2", Value: "value2", Sort: 0},
				{Key: "key3", Value: "value3", Sort: 1},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := sortKVList(tt.input)
			assert.Equal(t, len(tt.expected), len(result), "结果长度不符合预期")
			for i, expected := range tt.expected {
				assert.Equal(t, expected.Key, result[i].Key, "Key不匹配")
				assert.Equal(t, expected.Value, result[i].Value, "Value不匹配")
				assert.Equal(t, expected.Sort, result[i].Sort, "Sort不匹配")
			}
		})
	}
}

// TestBuildLabelMap 测试 buildLabelMap 函数
func TestBuildLabelMap(t *testing.T) {
	tests := []struct {
		name           string
		input          []kvWithSort
		expectedLabels []string
		expectedMap    map[string]int
	}{
		{
			name: "正常构建",
			input: []kvWithSort{
				{Key: "key1", Value: "label1", Sort: 1},
				{Key: "key2", Value: "label2", Sort: 2},
				{Key: "key3", Value: "label3", Sort: 3},
			},
			expectedLabels: []string{"label1", "label2", "label3"},
			expectedMap:    map[string]int{"key1": 0, "key2": 1, "key3": 2},
		},
		{
			name:           "空输入",
			input:          []kvWithSort{},
			expectedLabels: []string{},
			expectedMap:    map[string]int{},
		},
		{
			name: "单个元素",
			input: []kvWithSort{
				{Key: "key1", Value: "label1", Sort: 1},
			},
			expectedLabels: []string{"label1"},
			expectedMap:    map[string]int{"key1": 0},
		},
		{
			name: "重复键值",
			input: []kvWithSort{
				{Key: "key1", Value: "label1", Sort: 1},
				{Key: "key1", Value: "label2", Sort: 2},
			},
			expectedLabels: []string{"label1", "label2"},
			expectedMap:    map[string]int{"key1": 1}, // 后面的会覆盖前面的
		},
		{
			name: "特殊字符",
			input: []kvWithSort{
				{Key: "key@1", Value: "标签1", Sort: 1},
				{Key: "key-2", Value: "Label 2", Sort: 2},
			},
			expectedLabels: []string{"标签1", "Label 2"},
			expectedMap:    map[string]int{"key@1": 0, "key-2": 1},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			labels, mapping := buildLabelMap(tt.input)

			assert.Equal(t, tt.expectedLabels, labels, "标签数组不符合预期")
			assert.Equal(t, len(tt.expectedMap), len(mapping), "映射长度不符合预期")

			for key, expectedIndex := range tt.expectedMap {
				actualIndex, exists := mapping[key]
				assert.True(t, exists, "键 %s 应该存在于映射中", key)
				assert.Equal(t, expectedIndex, actualIndex, "键 %s 的索引不符合预期", key)
			}
		})
	}
}

// TestNewService 测试 NewService 函数
func TestNewService(t *testing.T) {
	service := NewService()
	assert.NotNil(t, service, "NewService应该返回非nil的服务实例")
	assert.IsType(t, &Service{}, service, "返回的应该是Service类型")
}

// TestInitSeries 测试 initSeries 函数
func TestInitSeries(t *testing.T) {
	tests := []struct {
		name     string
		xDesc    []*KeyValuePair
		xLen     int
		validate func(t *testing.T, series []*seriesData, mapping map[string]int)
	}{
		{
			name: "正常初始化",
			xDesc: []*KeyValuePair{
				{Key: "key1", Value: "value1", Sort: 1},
				{Key: "key2", Value: "value2", Sort: 2},
			},
			xLen: 3,
			validate: func(t *testing.T, series []*seriesData, mapping map[string]int) {
				assert.Equal(t, 2, len(series), "系列数量应该等于xDesc长度")
				assert.Equal(t, 2, len(mapping), "映射数量应该等于xDesc长度")

				for _, s := range series {
					assert.Equal(t, 3, len(s.Data), "每个系列的数据长度应该等于xLen")
					for _, data := range s.Data {
						assert.Equal(t, int64(0), data, "初始数据应该为0")
					}
				}

				assert.Equal(t, 0, mapping["key1"], "key1应该映射到索引0")
				assert.Equal(t, 1, mapping["key2"], "key2应该映射到索引1")
			},
		},
		{
			name:  "空xDesc",
			xDesc: []*KeyValuePair{},
			xLen:  5,
			validate: func(t *testing.T, series []*seriesData, mapping map[string]int) {
				assert.Equal(t, 0, len(series), "空xDesc应该返回空系列")
				assert.Equal(t, 0, len(mapping), "空xDesc应该返回空映射")
			},
		},
		{
			name: "单个元素",
			xDesc: []*KeyValuePair{
				{Key: "single", Value: "单个", Sort: 1},
			},
			xLen: 1,
			validate: func(t *testing.T, series []*seriesData, mapping map[string]int) {
				assert.Equal(t, 1, len(series), "单个元素应该返回一个系列")
				assert.Equal(t, 1, len(mapping), "单个元素应该返回一个映射")
				assert.Equal(t, "单个", series[0].Name, "系列名称应该正确")
				assert.Equal(t, 1, len(series[0].Data), "数据长度应该正确")
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			series, mapping := initSeries(tt.xDesc, tt.xLen)
			tt.validate(t, series, mapping)
		})
	}
}

// TestMakeResData 测试 makeResData 函数
func TestMakeResData(t *testing.T) {
	tests := []struct {
		name     string
		param    string
		xLabels  []string
		series   []*seriesData
		validate func(t *testing.T, result *ResChatData)
	}{
		{
			name:    "正常数据",
			param:   "test_param",
			xLabels: []string{"A", "B", "C"},
			series: []*seriesData{
				{Name: "系列1", Data: []int64{1, 2, 3}},
				{Name: "系列2", Data: []int64{4, 5, 6}},
			},
			validate: func(t *testing.T, result *ResChatData) {
				assert.Equal(t, "test_param", result.Key, "Key应该正确")
				assert.Equal(t, []string{"A", "B", "C"}, result.ChartData.XAxisLabels, "XAxisLabels应该正确")
				assert.Empty(t, result.ChartData.YAxisLabels, "YAxisLabels应该为空")
				assert.Equal(t, 2, len(result.ChartData.SeriesData), "SeriesData长度应该正确")
			},
		},
		{
			name:    "空数据",
			param:   "",
			xLabels: []string{},
			series:  []*seriesData{},
			validate: func(t *testing.T, result *ResChatData) {
				assert.Equal(t, "", result.Key, "Key应该为空")
				assert.Empty(t, result.ChartData.XAxisLabels, "XAxisLabels应该为空")
				assert.Empty(t, result.ChartData.YAxisLabels, "YAxisLabels应该为空")
				assert.Empty(t, result.ChartData.SeriesData, "SeriesData应该为空")
			},
		},
		{
			name:    "单系列数据",
			param:   "single_series",
			xLabels: []string{"X"},
			series: []*seriesData{
				{Name: "唯一系列", Data: []int64{100}},
			},
			validate: func(t *testing.T, result *ResChatData) {
				assert.Equal(t, "single_series", result.Key, "Key应该正确")
				assert.Equal(t, []string{"X"}, result.ChartData.XAxisLabels, "XAxisLabels应该正确")
				assert.Equal(t, 1, len(result.ChartData.SeriesData), "应该有一个系列")
				assert.Equal(t, "唯一系列", result.ChartData.SeriesData[0].Name, "系列名称应该正确")
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := makeResData(tt.param, tt.xLabels, tt.series)
			assert.NotNil(t, result, "结果不应该为nil")
			tt.validate(t, result)
		})
	}
}

// TestSortSeriesByValue 测试 sortSeriesByValue 函数
func TestSortSeriesByValue(t *testing.T) {
	tests := []struct {
		name     string
		series   []*seriesData
		desc     bool
		expected []int
	}{
		{
			name: "降序排序-按位置总和",
			series: []*seriesData{
				{Data: []int64{1, 5}}, // 位置0总和: 1+3=4, 位置1总和: 5+6=11
				{Data: []int64{3, 6}},
			},
			desc:     true,
			expected: []int{1, 0}, // 按位置总和降序: 位置1(11), 位置0(4)
		},
		{
			name: "升序排序-按位置总和",
			series: []*seriesData{
				{Data: []int64{1, 5}}, // 位置0总和: 1+3=4, 位置1总和: 5+6=11
				{Data: []int64{3, 6}},
			},
			desc:     false,
			expected: []int{0, 1}, // 按位置总和升序: 位置0(4), 位置1(11)
		},
		{
			name:     "空系列",
			series:   []*seriesData{},
			desc:     true,
			expected: nil,
		},
		{
			name: "单个系列-多个位置",
			series: []*seriesData{
				{Data: []int64{10, 20}}, // 位置0总和: 10, 位置1总和: 20
			},
			desc:     true,
			expected: []int{1, 0}, // 按位置总和降序: 位置1(20), 位置0(10)
		},
		{
			name: "相同位置总和",
			series: []*seriesData{
				{Data: []int64{5, 5}}, // 位置0总和: 5+5=10, 位置1总和: 5+5=10
				{Data: []int64{5, 5}},
			},
			desc:     true,
			expected: []int{0, 1}, // 相同总和时保持原顺序
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := sortSeriesByValue(tt.series, tt.desc)
			assert.Equal(t, tt.expected, result, "sortSeriesByValue返回值不符合预期")
		})
	}
}
