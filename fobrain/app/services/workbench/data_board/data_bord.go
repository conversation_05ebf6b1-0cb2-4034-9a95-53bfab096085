package data_board

import (
	"errors"
	"fobrain/fobrain/app/repository/asset"
	"fobrain/fobrain/app/services/poc"
	"fobrain/fobrain/app/services/workbench"
	"fobrain/initialize/es"
	filtrate "fobrain/models/elastic"
	"fobrain/models/elastic/assets"
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/system_configs"
	"math"
	"sort"
	"strconv"
	"strings"

	"github.com/olivere/elastic/v7"
	"gorm.io/gorm/utils"
)

// 看板统计服务
// Service 统计分析服务
// 整体逻辑描述
// 1. 获取看板配置
// 2. 根据配置获取数据
// 3. 返回数据

type Service struct {
}

// NewService 创建统计分析服务
func NewService() *Service {
	return &Service{}
}

// DataProcessorType 数据处理函数类型
type DataProcessorType func(asset []*asset.AssetTopResult, queryFields *searchConfig, flag, title string, interpretation *system_configs.DataInterpretationResult) (any, error)

// GetDataProcessor 根据图表类型获取对应的数据处理函数
// 参数 chartType: pie 饼图|bar|柱状图|line|折线图|chart|图表
// 返回对应的处理函数
func (s *Service) GetDataProcessor(chartType string) (DataProcessorType, error) {
	// 根据图表类型返回对应的处理函数
	switch chartType {
	case "bar", "line":
		// 返回饼图数据处理函数
		return transformData, nil
	case "pie":
		return pieTransFormData, nil
	case "progress":
		return progressTransFormData, nil
	default:
		// 默认返回饼图处理函数
		return nil, errors.New("不支持的类型:" + chartType)
	}
}

const (
	FlagTypePercentage = "percentage" // 百分比计算类型
	FlagTypeDepartment = "department" // 部门类型计算
)

type (
	DataBoardConfigList struct {
		Title           string                 `json:"title"`            // 标题
		Key             string                 `json:"key"`              // 字段
		Type            string                 `json:"type"`             // 类型 pie 饼图|bar|柱状图|line|折线图|chart|图表
		SearchCondition *SearchCondition       `json:"search_condition"` // 查询选项
		FlagCondition   *SearchCondition       `json:"flag_condition"`   // 标签选项
		Children        []*DataBoardConfigList `json:"children"`         // 子项
	}

	SearchCondition struct {
		SearchField string     `json:"field"`
		Options     []*Options `json:"options"`
	}
	Options struct {
		Label string `json:"label"`
		Value string `json:"value"`
	}

	searchConfig struct {
		keyword        string            // 关键词，用于过滤查询
		field          string            // 主要查询/聚合字段
		subField       string            // 子查询/聚合字段，用于双层聚合
		value          string            // 字段值，用于精确匹配
		nestedPath     string            // 嵌套路径，用于嵌套查询
		isDualNested   bool              // 子查询是否嵌套查询
		size           int               // 返回结果数量限制
		name           string            // 字段显示名称
		param          string            // 参数名称，用于前端标识
		desc           []*KeyValuePair   // 字段值描述映射，用于转换显示值
		xDesc          []*KeyValuePair   // x轴字段值描述映射，用于转换显示值
		yDesc          map[string]string // y轴字段值描述映射，用于转换显示值
		includeMissing bool              // 是否包含缺失字段的文档
		invertAxis     bool              // 是否倒置X轴和系列数据
		indexName      string            // 索引名称
		sortByValue    bool              // 是否按数值大小排序
		sortDesc       bool              // 排序方向，true为降序，false为升序
		mustNotEmpty   bool              //不能为空
		dataType       int               // 0-百分比 1-数量
		pieParams      *PieParams        // 数据标识类型及百分比计算配置
		searchType     int               // 搜索类型 0-字符串 1-数字

		enablePercentage       bool // 是否启用占比计算
		percentageBasedOnTotal bool // 占比计算是否基于总数（true: 基于总资产数，false: 基于TOP数据总和）
	}

	PieParams struct {
		flagType    string   // 标识类型 百分比计算类型 FlagTypePercentage 部门类型
		searchField string   // 搜索字段
		numerator   []string // 分子
		denominator []string // 分母 all代表全部
	}
	RequestParams struct {
		Params []*condition `json:"params" binding:"required"`
	}
	// Request 请求参数
	condition struct {
		Key             string   `json:"key" form:"key" uri:"key" binding:"required"`    // 字段
		Type            string   `json:"type" form:"type" uri:"type" binding:"required"` // 类型 pie 饼图|bar|柱状图|line|折线图|chart|图表
		SearchCondition []string `json:"search_condition" form:"search_condition" uri:"search_condition"  zh:"搜索条件"`
		Flag            string   `json:"flag" form:"flag" uri:"flag" binding:"required"` // 数据标识
	}
	ResChatData struct {
		ChartData *ChartData `json:"chart_data"`
		Key       string     `json:"key"`
		system_configs.DataInterpretationResult
	}
	// ChartData 图表数据
	ChartData struct {
		DataType    int           `json:"data_type"`
		XAxisLabels []string      `json:"x_axis_labels"`
		YAxisLabels []string      `json:"y_axis_labels"`
		SeriesData  []*seriesData `json:"series_data"`
	}
	PieChartData struct {
		ChartData *PieDatas `json:"chart_data"`
		Key       string    `json:"key"`
		system_configs.DataInterpretationResult
	}
	PieDatas struct {
		Title string     `json:"title"`
		Data  []*PieData `json:"data"`
	}

	ProgressData struct {
		Title   string  `json:"title"`
		Percent float64 `json:"percent"`
	}

	ProgressChartData struct {
		ChartData *ProgressData `json:"chart_data"`
		Key       string        `json:"key"`
		system_configs.DataInterpretationResult
	}
	PieData struct {
		Name       string      `json:"name"`
		Value      interface{} `json:"value"`
		Percentage int64       `json:"percentage,omitempty"` // 占比，以百分比显示
	}
	seriesData struct {
		Name        string  `json:"name"`
		Data        []int64 `json:"data"`
		Percentages []int64 `json:"percentages,omitempty"` // 占比数据，以百分比显示
	}
	KeyValuePair struct {
		Key   string // 键
		Value string // 值
		Sort  int    // 排序值
	}
)

// 定义一个带排序字段的键值结构体
type kvWithSort struct {
	Key   string
	Value string
	Sort  int
}

func pieTransFormData(assetData []*asset.AssetTopResult, queryFields *searchConfig, flag, title string, interpretation *system_configs.DataInterpretationResult) (any, error) {
	result := make([]*PieData, 0)
	for _, asset := range assetData {
		name := asset.FieldValue
		for _, desc := range queryFields.desc {
			if desc.Key == asset.FieldValue {
				name = desc.Value
				break
			}
		}
		pieData := &PieData{
			Name:  name,
			Value: asset.FieldCount,
		}
		// 配置化的百分比信息处理
		if queryFields.enablePercentage {
			pieData.Percentage = asset.FieldPercentage
		}
		result = append(result, pieData)
	}

	chatData := &PieChartData{
		ChartData: &PieDatas{
			Title: title,
			Data:  result,
		},
		Key: queryFields.param,
	}
	if interpretation != nil {
		chatData.DataInterpretationResult = *interpretation
	}
	return chatData, nil
}

func progressTransFormData(assetData []*asset.AssetTopResult, queryFields *searchConfig, flag, title string, interpretation *system_configs.DataInterpretationResult) (any, error) {
	if queryFields.pieParams == nil {
		return nil, errors.New("progressParams is nil")
	}
	switch queryFields.pieParams.flagType {
	case FlagTypePercentage:
		//根据配置计算百分比
		numerator := queryFields.pieParams.numerator
		denominator := queryFields.pieParams.denominator
		numeratorMap := int64(0)
		denominatorMap := int64(0)
		for _, asset := range assetData {
			//获取分子
			if utils.Contains(numerator, asset.FieldValue) {
				numeratorMap += asset.FieldCount
			}
			//获取分母
			if utils.Contains(denominator, "all") {
				denominatorMap += asset.FieldCount
			} else {
				if utils.Contains(denominator, asset.FieldValue) {
					denominatorMap += asset.FieldCount
				}
			}
		}
		//计算百分比
		chatData := &ProgressChartData{
			ChartData: &ProgressData{
				Title:   queryFields.name,
				Percent: math.Round(float64(numeratorMap)/float64(denominatorMap)*10000) / 100,
			},
			Key: queryFields.param,
		}
		if interpretation != nil {
			chatData.DataInterpretationResult = *interpretation
		}
		return chatData, nil
	default:
		return nil, errors.New("progressParams is nil")
	}
}

func modifyChatDataFefore(assetData []*asset.AssetTopResult, queryFields *searchConfig, flag string) []*asset.AssetTopResult {
	switch queryFields.param {
	case system_configs.BusinessDepartmentAssetCount, "department_vul_statistics", "department_nofixed_vul_statistics", "department_business_system_num", "department_vul_top5":
		result := make([]*asset.AssetTopResult, 0)
		// 根据部门层级筛选数据
		if flagInt, err := strconv.Atoi(flag); err == nil && flagInt >= 1 && flagInt <= 3 {
			// flag=1 或 2 时，进行部门聚合统计
			if flagInt == 1 || flagInt == 2 {
				// 用 map 聚合部门数据，key 为前 N 级部门名
				deptCountMap := make(map[string]*asset.AssetTopResult)
				// 先聚合主部门数据
				for _, d := range assetData {
					parts := strings.Split(d.FieldValue, "/")
					if len(parts) >= flagInt {
						dept := strings.Join(parts[:flagInt], "/")
						if _, ok := deptCountMap[dept]; !ok {
							deptCountMap[dept] = &asset.AssetTopResult{
								FieldValue: dept,
								FieldCount: 0,
							}
						}
						deptCountMap[dept].FieldCount += d.FieldCount
						// 对Children进行分组累加（按FieldValue）
						if len(d.Children) > 0 {
							if deptCountMap[dept].Children == nil {
								deptCountMap[dept].Children = make([]*asset.AssetTopResult, 0)
							}
							childMap := make(map[string]*asset.AssetTopResult)
							// 先将已有的Children纳入map
							for _, c := range deptCountMap[dept].Children {
								childMap[c.FieldValue] = c
							}
							for _, child := range d.Children {
								if aggChild, ok := childMap[child.FieldValue]; ok {
									aggChild.FieldCount += child.FieldCount
								} else {
									childMap[child.FieldValue] = &asset.AssetTopResult{
										FieldValue: child.FieldValue,
										FieldCount: child.FieldCount,
									}
								}
							}
							// 重新赋值Children，保证去重聚合
							deptCountMap[dept].Children = make([]*asset.AssetTopResult, 0, len(childMap))
							for _, v := range childMap {
								deptCountMap[dept].Children = append(deptCountMap[dept].Children, v)
							}
						}
					}
				}
				result = make([]*asset.AssetTopResult, 0, len(deptCountMap))
				for _, v := range deptCountMap {
					result = append(result, v)
				}
				assetData = result
			}
			// flagInt==3 时，只要三级部门数据
			if flagInt == 3 {
				result = make([]*asset.AssetTopResult, 0)
				for _, d := range assetData {
					parts := strings.Split(d.FieldValue, "/")
					if len(parts) == 3 {
						result = append(result, d)
					}
				}
				assetData = result
			}
		}
		if queryFields.param == "department_vul_statistics" {
			// 根据漏洞状态计算已修复/未修复数量
			// issue:3554
			// 未处理：包括新增(0)、复现(1)、待修复(10)、待修复-转交(11)、待复测(17)、复测中(14)、复测未通过(15)、延时(12)、超时未修复(13)
			// 已处理：包括复测通过(30)、误报(40)、无法修复(41)
			for i := range assetData {
				as := assetData[i]
				// 统计已修复和未修复的数量，注意FieldCount为int64类型
				var fixedCount int64 = 0
				var unfixedCount int64 = 0
				if len(as.Children) == 0 {
					as.Children = []*asset.AssetTopResult{
						{FieldValue: "已修复", FieldCount: fixedCount},
						{FieldValue: "未修复", FieldCount: unfixedCount},
					}
				}
				for _, vulStatus := range as.Children {
					// "30", "40", "41"为已修复状态，其余为未修复
					if utils.Contains([]string{"30", "40", "41"}, vulStatus.FieldValue) {
						fixedCount += vulStatus.FieldCount
					} else {
						unfixedCount += vulStatus.FieldCount
					}
				}
				// 重组 Children，顺序固定，类型一致
				as.Children = []*asset.AssetTopResult{
					{FieldValue: "已修复", FieldCount: fixedCount},
					{FieldValue: "未修复", FieldCount: unfixedCount},
				}
			}
		}
	}
	// 补充未派发的数据
	if queryFields.param == "department_nofixed_vul_statistics" {
		notDispatchCount, err := poc.GetNotDispatchCount()
		if err != nil {
			return assetData
		}
		assetData = append(assetData, &asset.AssetTopResult{
			FieldValue: "未派发",
			FieldCount: notDispatchCount,
		})
	} else if queryFields.param == "department_vul_statistics" {
		notDispatchCount, err := poc.GetNotDispatchCount()
		if err != nil {
			return assetData
		}
		assetData = append(assetData, &asset.AssetTopResult{
			FieldValue: "未派发",
			FieldCount: notDispatchCount,
			Children: []*asset.AssetTopResult{
				{FieldValue: "已修复", FieldCount: 0},
				{FieldValue: "未修复", FieldCount: notDispatchCount},
			},
		})
	}
	return assetData
}

// 主方法：将原始资产数据转换为图表结构
func transformData(assetData []*asset.AssetTopResult, queryFields *searchConfig, flag, title string, interpretation *system_configs.DataInterpretationResult) (any, error) {
	hasDesc := queryFields.desc != nil && len(queryFields.desc) > 0    // 是否配置了 desc
	hasXDesc := queryFields.xDesc != nil && len(queryFields.xDesc) > 0 // 是否配置了 xDesc

	// 移除对 sortAssetDataByValue 的调用，因为数据默认已经排序好了
	// 我们将直接使用 sortSeriesByValue 方法，它能够根据子数据的位置计算总和进行排序

	//前置处理
	result := modifyChatDataFefore(assetData, queryFields, flag)
	var chatData = &ResChatData{}
	var err error
	switch {
	// 情况 1：同时配置了 desc 和 xDesc，对应多序列柱状图
	case hasDesc && hasXDesc:
		chatData, err = handleDescAndXDesc(result, queryFields)
	// 情况 2：仅有 desc，对应单序列柱状图
	case hasDesc:
		chatData, err = handleDescOnly(result, queryFields)
	// 情况 3：仅有 xDesc（如来源统计 Top5 场景）
	case hasXDesc:
		chatData, err = handleXDescOnly(result, queryFields)
	// 情况 4：既无 desc 又无 xDesc，使用原始 FieldValue 构造
	default:
		chatData, err = handleNoDescNoXDesc(result, queryFields)
	}
	if interpretation != nil {
		chatData.DataInterpretationResult = *interpretation
	}
	return chatData, err
}

// sortSeriesByValue 根据系列数据的总和对系列进行排序，并返回排序后的位置索引映射关系
// 参数 desc: true为降序（从大到小），false为升序（从小到大）
// 返回值: 排序后的位置索引数组，用于同步调整标签顺序
func sortSeriesByValue(series []*seriesData, desc bool) []int {
	// 获取所有系列的数据长度（假设所有系列数据长度相同）
	if len(series) == 0 {
		return nil
	}

	// 计算每个位置的数据总和
	dataLen := len(series[0].Data)

	// 计算每个位置的总和
	positionSums := make([]int64, dataLen)
	seriesPositionValues := make(map[int][]int64, len(series))

	// 首先计算每个位置的总和
	for i, s := range series {
		seriesPositionValues[i] = make([]int64, dataLen)
		for j, v := range s.Data {
			if j < dataLen {
				positionSums[j] += v
				seriesPositionValues[i][j] = v
			}
		}
	}

	// 创建位置索引数组，用于排序
	positionIndices := make([]int, dataLen)
	for i := range positionIndices {
		positionIndices[i] = i
	}

	// 根据位置总和对位置索引进行排序
	if desc {
		// 降序排序
		sort.Slice(positionIndices, func(i, j int) bool {
			return positionSums[positionIndices[i]] > positionSums[positionIndices[j]]
		})
	} else {
		// 升序排序
		sort.Slice(positionIndices, func(i, j int) bool {
			return positionSums[positionIndices[i]] < positionSums[positionIndices[j]]
		})
	}

	// 根据排序后的位置索引重新排列每个系列的数据
	for _, s := range series {
		originalData := make([]int64, dataLen)
		copy(originalData, s.Data)
		for newIdx, oldIdx := range positionIndices {
			s.Data[newIdx] = originalData[oldIdx]
		}
	}

	// 返回排序后的位置索引，用于调整标签顺序
	return positionIndices
}

// 工具方法：对 KeyValuePair 列表进行按 Sort 排序
func sortKVList(list []*KeyValuePair) []kvWithSort {
	sorted := make([]kvWithSort, len(list))
	for i, kv := range list {
		sorted[i] = kvWithSort{Key: kv.Key, Value: kv.Value, Sort: kv.Sort}
	}
	sort.Slice(sorted, func(i, j int) bool { return sorted[i].Sort < sorted[j].Sort })
	return sorted
}

// 工具方法：将排序后的 kv 列表生成标签数组和映射
func buildLabelMap(kvs []kvWithSort) ([]string, map[string]int) {
	labels := make([]string, len(kvs))
	mapping := make(map[string]int)
	for i, kv := range kvs {
		labels[i] = kv.Value
		mapping[kv.Key] = i
	}
	return labels, mapping
}

// 工具方法：初始化系列（Y轴数据结构）
func initSeries(xDesc []*KeyValuePair, xLen int) ([]*seriesData, map[string]int) {
	sorted := sortKVList(xDesc)
	series := make([]*seriesData, len(sorted))
	mapping := make(map[string]int)
	for i, kv := range sorted {
		series[i] = &seriesData{
			Name:        kv.Value,
			Data:        make([]int64, xLen),
			Percentages: make([]int64, xLen),
		}
		mapping[kv.Key] = i
	}
	return series, mapping
}

// 处理情况1：有 desc 有 xDesc（多序列柱状图）
func handleDescAndXDesc(assetData []*asset.AssetTopResult, queryFields *searchConfig) (*ResChatData, error) {
	descSorted := sortKVList(queryFields.desc)
	xLabels, xMap := buildLabelMap(descSorted)
	series, seriesMap := initSeries(queryFields.xDesc, len(xLabels))

	for _, top := range assetData {
		xIdx, ok := xMap[top.FieldValue]
		if !ok {
			continue
		}
		for _, child := range top.Children {
			if sIdx, ok := seriesMap[child.FieldValue]; ok {
				series[sIdx].Data[xIdx] = child.FieldCount
			}
		}
	}

	// 如果需要按值排序，对系列进行排序
	if queryFields.sortByValue {
		xLabels = sortAndReorderLabels(series, xLabels, queryFields.sortDesc)
	}

	return modifyChatData(makeResData(queryFields.param, xLabels, series), queryFields)
}

// 处理情况2：仅有 desc（单序列柱状图）
func handleDescOnly(assetData []*asset.AssetTopResult, queryFields *searchConfig) (*ResChatData, error) {
	descSorted := sortKVList(queryFields.desc)
	xLabels, xMap := buildLabelMap(descSorted)

	data := make([]int64, len(xLabels))
	percentages := make([]int64, len(xLabels))

	for _, top := range assetData {
		if xIdx, ok := xMap[top.FieldValue]; ok {
			data[xIdx] = top.FieldCount
			percentages[xIdx] = top.FieldPercentage
		}
	}

	// 创建单个系列
	seriesItem := &seriesData{
		Name: "数量",
		Data: data,
	}
	// 配置化的百分比信息处理
	if queryFields.enablePercentage {
		seriesItem.Percentages = percentages
	}
	series := []*seriesData{seriesItem}

	// 对于单序列柱状图，如果需要按值排序，使用与多系列相同的排序逻辑
	if queryFields.sortByValue {
		xLabels = sortAndReorderLabels(series, xLabels, queryFields.sortDesc)
	}

	return modifyChatData(makeResData(queryFields.param, xLabels, series), queryFields)
}

// 处理情况3：仅有 xDesc，使用原始 FieldValue 作为 x 轴
func handleXDescOnly(assetData []*asset.AssetTopResult, queryFields *searchConfig) (*ResChatData, error) {
	// 收集 x 轴标签（FieldValue）
	labelSet := make(map[string]struct{})
	for _, top := range assetData {
		labelSet[top.FieldValue] = struct{}{}
	}
	keys := make([]string, 0, len(labelSet))
	for k := range labelSet {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	xLabels := keys
	xMap := make(map[string]int)
	for i, k := range xLabels {
		xMap[k] = i
	}

	series, seriesMap := initSeries(queryFields.xDesc, len(xLabels))

	for _, top := range assetData {
		xIdx := xMap[top.FieldValue]
		for _, child := range top.Children {
			if sIdx, ok := seriesMap[child.FieldValue]; ok {
				series[sIdx].Data[xIdx] = child.FieldCount
			}
		}
	}

	// 如果需要按值排序，对系列进行排序
	if queryFields.sortByValue {
		xLabels = sortAndReorderLabels(series, xLabels, queryFields.sortDesc)
	}

	return modifyChatData(makeResData(queryFields.param, xLabels, series), queryFields)
}

// 处理情况4：既无 desc 又无 xDesc（回退默认处理）
func handleNoDescNoXDesc(assetData []*asset.AssetTopResult, queryFields *searchConfig) (*ResChatData, error) {
	labelOrder := []string{}
	labelSeen := make(map[string]bool)
	fieldDataMap := make(map[string]int64)
	isMultiSeries := false

	// 收集标签和数据
	for _, top := range assetData {
		if !labelSeen[top.FieldValue] {
			labelOrder = append(labelOrder, top.FieldValue)
			labelSeen[top.FieldValue] = true
		}
		fieldDataMap[top.FieldValue] = top.FieldCount
		if len(top.Children) > 0 {
			isMultiSeries = true
		}
	}

	xLabels := labelOrder
	xMap := make(map[string]int)
	for i, k := range labelOrder {
		xMap[k] = i
	}

	if isMultiSeries {
		seriesMap := make(map[string]*seriesData)
		for _, top := range assetData {
			xIdx := xMap[top.FieldValue]
			for _, child := range top.Children {
				if _, ok := seriesMap[child.FieldValue]; !ok {
					seriesMap[child.FieldValue] = &seriesData{
						Name: child.FieldValue,
						Data: make([]int64, len(xLabels)),
					}
				}
				seriesMap[child.FieldValue].Data[xIdx] = child.FieldCount
			}
		}

		var series []*seriesData
		for _, ser := range seriesMap {
			series = append(series, ser)
		}

		// 如果需要按值排序，对系列进行排序
		if queryFields.sortByValue {
			// 获取排序后的位置索引，用于同步调整标签顺序
			positionIndices := sortSeriesByValue(series, queryFields.sortDesc)

			// 根据排序后的位置索引重新排列标签
			if len(positionIndices) > 0 {
				newLabels := make([]string, len(xLabels))
				for newIdx, oldIdx := range positionIndices {
					newLabels[newIdx] = xLabels[oldIdx]
				}
				xLabels = newLabels
			}

			// 遍历排序后的结果，如果label为"未派发"，则把数据移到最后面(包括label和对应data)
			for i, label := range xLabels {
				if label == "未派发" {
					xLabels = append(xLabels[:i], xLabels[i+1:]...)
					xLabels = append(xLabels, "未派发")
					// 将series中对应位置的数据移到最后面
					for _, s := range series {
						notDispatchData := s.Data[i]
						s.Data = append(s.Data[:i], s.Data[i+1:]...)
						s.Data = append(s.Data, notDispatchData)
					}
					break
				}
			}
		}

		return modifyChatData(makeResData(queryFields.param, xLabels, series), queryFields)
	}

	// 单序列情况
	data := make([]int64, len(xLabels))
	percentages := make([]int64, len(xLabels))
	for _, top := range assetData {
		xIdx := xMap[top.FieldValue]
		data[xIdx] = top.FieldCount
		percentages[xIdx] = top.FieldPercentage
	}

	// 创建单个系列
	seriesItem := &seriesData{
		Name: "数量",
		Data: data,
	}
	// 配置化的百分比信息处理
	if queryFields.enablePercentage {
		seriesItem.Percentages = percentages
	}
	series := []*seriesData{seriesItem}

	// 如果需要按值排序，对系列进行排序
	if queryFields.sortByValue {
		xLabels = sortAndReorderLabels(series, xLabels, queryFields.sortDesc)
	}

	return modifyChatData(makeResData(queryFields.param, xLabels, series), queryFields)
}

// sortSeriesByValue 根据系列数据的总和对系列进行排序，并返回排序后的位置索引映射关系
// 参数 desc: true为降序（从大到小），false为升序（从小到大）
// 返回值: 排序后的位置索引数组，用于同步调整标签顺序
func sortAndReorderLabels(series []*seriesData, xLabels []string, desc bool) []string {
	// 获取所有系列的数据长度（假设所有系列数据长度相同）
	if len(series) == 0 {
		return nil
	}

	// 计算每个位置的数据总和
	dataLen := len(series[0].Data)

	// 计算每个位置的总和
	positionSums := make([]int64, dataLen)
	seriesPositionValues := make(map[int][]int64, len(series))

	// 首先计算每个位置的总和
	for i, s := range series {
		seriesPositionValues[i] = make([]int64, dataLen)
		for j, v := range s.Data {
			if j < dataLen {
				positionSums[j] += v
				seriesPositionValues[i][j] = v
			}
		}
	}

	// 创建位置索引数组，用于排序
	positionIndices := make([]int, dataLen)
	for i := range positionIndices {
		positionIndices[i] = i
	}

	// 根据位置总和对位置索引进行排序
	if desc {
		// 降序排序
		sort.Slice(positionIndices, func(i, j int) bool {
			return positionSums[positionIndices[i]] > positionSums[positionIndices[j]]
		})
	} else {
		// 升序排序
		sort.Slice(positionIndices, func(i, j int) bool {
			return positionSums[positionIndices[i]] < positionSums[positionIndices[j]]
		})
	}

	// 根据排序后的位置索引重新排列每个系列的数据
	for _, s := range series {
		originalData := make([]int64, dataLen)
		copy(originalData, s.Data)
		for newIdx, oldIdx := range positionIndices {
			s.Data[newIdx] = originalData[oldIdx]
		}
	}
	// 根据排序后的位置索引重新排列标签
	newLabels := make([]string, dataLen)
	for newIdx, oldIdx := range positionIndices {
		newLabels[newIdx] = xLabels[oldIdx]
	}
	// 遍历排序后的结果，如果label为"未派发"，则把数据移到最后面(包括label和对应data)
	for i, label := range newLabels {
		if label == "未派发" {
			newLabels = append(newLabels[:i], newLabels[i+1:]...)
			newLabels = append(newLabels, "未派发")
			// 将series中对应位置的数据移到最后面
			notDispatchData := series[0].Data[i]
			series[0].Data = append(series[0].Data[:i], series[0].Data[i+1:]...)
			series[0].Data = append(series[0].Data, notDispatchData)
			break
		}
	}
	return newLabels
}

// 构造最终响应结构
func makeResData(param string, xLabels []string, series []*seriesData) *ResChatData {
	return &ResChatData{
		Key: param,
		ChartData: &ChartData{
			XAxisLabels: xLabels,
			YAxisLabels: []string{},
			SeriesData:  series,
		},
	}
}

// modifyChatData 后处理图表数据
// modifyChatData 后处理图表数据，用于根据 param 进行定制化转换，如字段替换、单位换算、百分比转换等
func modifyChatData(result *ResChatData, searchConfig *searchConfig) (*ResChatData, error) {
	switch searchConfig.param {
	case system_configs.ProbeAssetContributionDegree:
		// ✅ 特殊处理：将来源 ID 替换为来源名称
		sources, _, _ := data_source.AllSources()
		if len(sources) == 0 {
			return result, nil
		}
		sourceNameMap := make(map[string]string, len(sources))
		for _, s := range sources {
			sourceNameMap[strconv.FormatUint(s.Id, 10)] = s.Name
		}
		newLabels := make([]string, len(result.ChartData.XAxisLabels))
		for i, label := range result.ChartData.XAxisLabels {
			if name, ok := sourceNameMap[label]; ok {
				newLabels[i] = name
			} else {
				newLabels[i] = label
			}
		}
		result.ChartData.XAxisLabels = newLabels
	case "ownerless_assets_proportion-nouse":
		// ✅ 将每个 series 中的 data 转换为百分比
		for _, series := range result.ChartData.SeriesData {
			var total int64
			for _, value := range series.Data {
				total += value
			}
			if total == 0 {
				// 避免除以 0，所有项设为 0
				for i := range series.Data {
					series.Data[i] = 0
				}
				continue
			}
			for i, value := range series.Data {
				// 保留两位小数（放大 10000 后四舍五入再缩回）
				percentage := float64(value) / float64(total) * 100
				series.Data[i] = int64(math.Round(percentage))
			}
			// 可以根据需要更改名称加" (%)"等
			series.Name += "（百分比）"
		}
	case "unremediated_vul_ratio":
		for _, series := range result.ChartData.SeriesData {
			series.Name = "百分比"
		}

	default:
		// 默认不处理
	}

	result.ChartData.DataType = searchConfig.dataType
	// 如果 size 小于 100，标识是TOP数据，要横向展示排行，将 X 轴标签移到 Y 轴
	if searchConfig.size < 100 {
		result.ChartData.YAxisLabels = result.ChartData.XAxisLabels
		result.ChartData.XAxisLabels = make([]string, 0)
	}
	return result, nil
}

// getSearchConfigByRequest 根据看板字段获取查询配置
// 参数 databoardKey: 看板字段key
// 返回 查询配置
func (s *Service) getSearchConfigByRequest(databoardKey string) (*searchConfig, error) {
	// 根据看板字段获取查询配置
	// 获取配置
	config, ok := SearchConfigs[databoardKey]
	if !ok {
		return nil, errors.New("不支持的类型")
	}

	return config, nil
}

// GetConfigList 获取看板配置列表
// 返回 看板配置列表
// GetConfigList 获取看板配置列表
// 根据不同的key返回静态配置，配置内容已迁移到config.go，便于维护和复用
func (s *Service) GetConfigList(key string) ([]*DataBoardConfigList, error) {
	switch key {
	case "data_board_assets_config":
		return AssetsConfigList, nil
	case "data_board_vul_config":
		return VulConfigList, nil
	case "data_board_operation_config":
		return OperationConfigList, nil
	}
	return nil, errors.New("不支持的类型")
}

// 数据解读函数映射
var DataInterpretationFuncMap = map[string]func(results []*asset.AssetTopResult) (*system_configs.DataInterpretationResult, error){
	system_configs.BusinessAssetStatistics:      workbench.BusinessAssetStatisticsDataInterpretation,
	system_configs.AssetComponentProportion:     workbench.AssetComponentProportionDataInterpretation,
	system_configs.InternetVulnerabilityTop10:   workbench.InternetVulnerabilityTop10DataInterpretation,
	system_configs.BusinessDepartmentAssetCount: workbench.BusinessDepartmentAssetCountDataInterpretation,
	system_configs.BusinessVulTop5:              workbench.BusinessVulTop5DataInterpretation,
}

// GetDataByRequest 根据请求获取数据
// 参数 param: 请求参数
// 返回 数据
func (s *Service) GetDataByRequest(param *RequestParams) ([]any, error) {
	// 根据看板字段获取查询配置
	result := make([]any, 0)
	for _, param := range param.Params {
		// 根据查询配置获取数据
		searchConfig, err := s.getSearchConfigByRequest(param.Key)
		if err != nil {
			return nil, err
		}
		// 添加搜索条件
		query, err := s.buildSearchConditionQuery(param.SearchCondition, elastic.NewBoolQuery())
		if err != nil {
			return nil, err
		}
		//配置筛选条件
		if searchConfig.pieParams != nil && searchConfig.pieParams.searchField != "" && param.Flag != "" {
			var flag interface{}
			if searchConfig.searchType == 1 {
				flag, err = strconv.Atoi(param.Flag)
				if err != nil {
					return nil, err
				}
			} else {
				flag = param.Flag
			}
			query = query.Must(elastic.NewTermsQuery(searchConfig.pieParams.searchField, flag))
		}

		// 将构建的查询条件添加到附加查询中
		additionQueries := []elastic.Query{query}
		var data []*asset.AssetTopResult
		var title string
		var interpretation *system_configs.DataInterpretationResult

		switch param.Key {
		case system_configs.OwnerlessAssetsProportion:
			// 处理无主资产占比数据
			data, interpretation, err = workbench.NewWorkbenchService().OwnerlessAssetPercentage(param.Flag, query)
			if err != nil {
				return nil, err
			}
			// {Key: "hasBusinessPerson", Value: "有归属资产", Sort: 1},
			// {Key: "hasOper", Value: "有归属资产", Sort: 2},
			// {Key: "noBusinessPerson", Value: "缺失负责人", Sort: 3},
			// {Key: "noMatchBusinessPerson", Value: "无法匹配负责人", Sort: 4},
			// {Key: "noMatchOper", Value: "无法匹配责任人", Sort: 5},
			// {Key: "noOper", Value: "缺失责任人", Sort: 6},
			// 修改返回key
			for _, v := range data {
				if v.FieldValue == "hasBusinessPerson" || v.FieldValue == "hasOper" {
					v.FieldValue = "has_oper"
				}
				if v.FieldValue == "noBusinessPerson" || v.FieldValue == "noOper" {
					v.FieldValue = "no_oper"
				}
				if v.FieldValue == "noMatchBusinessPerson" || v.FieldValue == "noMatchOper" {
					v.FieldValue = "no_match_oper"
				}
			}
		case "ownerless_assets_statistics":
			// 处理无主资产统计
			data, err = workbench.NewWorkbenchService().OwnerlessAssetInfo()
		case "unremediated_vul_ratio":
			data, err = workbench.NewWorkbenchService().NoRepairedVulnPercentage(param.Flag)
		case "vul_poc_repaired_info":
			data, title, err = workbench.NewWorkbenchService().PocVulnRepairedInfo()

		case system_configs.InternetVulnerabilityTop10:
			additionQueries = append(additionQueries, elastic.NewTermQuery("network_type", assets.NetworkTypeExternal))
			// 处理柱状图数据
			data, err = s.processPieOrTopData(searchConfig, additionQueries)
		case "week_asset_count":
			data, err = workbench.NewWorkbenchService().WeeklyAssetCount()
		case "vul_type_top5":
			data, err = workbench.NewWorkbenchService().PocVulTypeTop5()
		case "ip_vul_top10":
			data, err = workbench.NewWorkbenchService().PocIPTop10()
		case system_configs.ProbeAssetContributionDegree:
			// 处理探针资产数据贡献度
			probeData, err := asset.ProbeAssetContributionTop()
			if err != nil {
				return nil, err
			}
			// 调用探针数据解析函数
			interpretation, err = workbench.ProbeAssetContributionDataInterpretation(probeData)
			if err != nil {
				return nil, err
			}
			// 将探针数据转换为AssetTopResult格式以便后续处理
			data = make([]*asset.AssetTopResult, len(probeData))
			for i, probe := range probeData {
				data[i] = &asset.AssetTopResult{
					FieldValue: probe.SourceId,
					FieldCount: probe.SubAllCount,
				}
			}
		default:
			if param.Key == "personnel_assets_top5" && param.Flag == "business" {
				searchConfig.nestedPath = "business.person_base"
				searchConfig.field = "business.person_base.name"
			} else if param.Key == "personnel_assets_top5" && param.Flag == "oper" {
				searchConfig.nestedPath = "oper_info"
				searchConfig.field = "oper_info.name"
			} else if param.Key == "department_nofixed_vul_statistics" {
				// issue:3554
				// 未处理：包括新增(0)、复现(1)、待修复(10)、待修复-转交(11)、待复测(17)、复测中(14)、复测未通过(15)、延时(12)、超时未修复(13)，已处理：包括复测通过(30)、误报(40)、无法修复(41)
				// 此处查询未处理状态的漏洞
				additionQueries = append(additionQueries, elastic.NewTermsQuery("status", "0", "1", "10", "11", "17", "14", "15", "12", "13"))
			} else if param.Key == "department_vul_not_fixed_top10" {
				// 查询超时未修复的漏洞
				additionQueries = append(additionQueries, elastic.NewTermsQuery("status", "13"))
			}
			// 处理柱状图数据
			data, err = s.processPieOrTopData(searchConfig, additionQueries)
			if err != nil {
				return nil, err
			}

			// 配置化的占比计算处理
			if searchConfig.enablePercentage {
				var totalAssets int64 = 0

				query := assets.NewAssets().GenQueryNoDeletedAndPurged()
				// 根据配置决定占比计算方式
				if searchConfig.percentageBasedOnTotal {
					indexName := searchConfig.indexName
					if indexName == "" {
						indexName = assets.NewAssets().IndexName()
					}
					// 获取总资产数量用于正确计算占比
					totalAssets, err = es.GetCount(indexName, query)
					if err != nil {
						return nil, err
					}
				}
				// 如果不基于总数，则传入0，函数内部会使用TOP数据总和
				asset.CalculatePercentages(data, totalAssets)
			}
		}
		if err != nil {
			return nil, err
		}
		//根据类型获取数据
		if interpretationFunc, ok := DataInterpretationFuncMap[param.Key]; ok {
			interpretation, err = interpretationFunc(data)
			if err != nil {
				return nil, err
			}
		}
		//获取处理器
		processFun, err := s.GetDataProcessor(param.Type)
		if err != nil {
			return nil, err
		}
		//根据类型处理数据
		chatData, err := processFun(data, searchConfig, param.Flag, title, interpretation)
		if err != nil {
			return nil, err
		}
		result = append(result, chatData)
	}
	return result, nil
}

// buildSearchConditionQuery 根据搜索条件构建查询
// 参数 searchCondition: 搜索条件
// 参数 baseQuery: 基础查询
// 返回 构建后的查询
func (s *Service) buildSearchConditionQuery(searchCondition []string, baseQuery *elastic.BoolQuery) (*elastic.BoolQuery, error) {
	// 解析搜索条件
	conditions, err := filtrate.ParseQueryConditions(searchCondition)
	if err != nil {
		return nil, err
	}

	// 遍历条件并构建查询
	for _, condition := range conditions {
		baseQuery = filtrate.BuildBoolQuery(
			condition.Field,
			condition.OperationTypeString,
			condition.LogicalConnective,
			condition.Value,
			baseQuery,
		)
	}

	return baseQuery, nil
}

// processPieOrTopData 处理饼图或排行榜数据
// 参数 queryFields: 查询配置
// 参数 additionQueries: 高级查询参数
// 返回 资产数据
func (s *Service) processPieOrTopData(queryFields *searchConfig, additionQueries []elastic.Query) ([]*asset.AssetTopResult, error) {
	// 组合高级查询参数
	config := asset.AssetTopConfig{
		Field:           queryFields.field,
		Size:            queryFields.size,
		NestedPath:      queryFields.nestedPath,
		NestedSubField:  queryFields.subField,
		IsDualNested:    queryFields.isDualNested,
		Keyword:         queryFields.keyword,
		AdditionQueries: additionQueries,
		Must:            queryFields.mustNotEmpty,
		IncludeMissing:  queryFields.includeMissing, // 包含缺失字段的文档，即没有business.business_trusted_state字段的资产
	}
	if queryFields.indexName != "" {
		config.IndexName = queryFields.indexName
	}
	assetData, err := asset.AssetTopProcess(config)
	if err != nil {
		return nil, err
	}

	return assetData, nil
}
