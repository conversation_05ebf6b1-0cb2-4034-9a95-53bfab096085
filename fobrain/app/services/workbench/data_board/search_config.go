package data_board

import (
	"fobrain/models/elastic/business_system"
	"fobrain/models/elastic/poc"
	"fobrain/models/mysql/system_configs"
)

// 所有静态的searchConfig配置结构体

var SearchConfigs = map[string]*searchConfig{
	"unremediated_vul_ratio": {
		keyword:      "",
		field:        "",
		subField:     "",
		value:        "",
		nestedPath:   "",
		isDualNested: false,
		size:         10000,
		name:         "未修复漏洞占比",
		param:        "unremediated_vul_ratio",
	},
	"department_vul_statistics": {
		keyword:      "",
		field:        "person_department.name.keyword",
		subField:     "status",
		value:        "",
		nestedPath:   "person_department",
		isDualNested: false,
		indexName:    poc.NewPoc().IndexName(),
		size:         10000,
		name:         "部门维度漏洞数量统计",
		param:        "department_vul_statistics",
		sortByValue:  true,
		sortDesc:     true,
	},
	system_configs.AssetComponentProportion: {
		keyword:                "",
		field:                  "rule_infos.product",
		subField:               "",
		value:                  "",
		nestedPath:             "rule_infos",
		isDualNested:           false,
		size:                   10,
		name:                   "资产组件占比",
		param:                  system_configs.AssetComponentProportion,
		enablePercentage:       true, // 启用占比计算
		percentageBasedOnTotal: true, // 基于总资产数计算占比
	},
	system_configs.ProbeAssetContributionDegree: {
		keyword:      "",
		field:        "source_ids",
		subField:     "",
		value:        "",
		nestedPath:   "",
		isDualNested: false,
		size:         5,
		name:         "探针资产数据源贡献度",
		param:        system_configs.ProbeAssetContributionDegree,
	},
	"ownerless_assets_statistics": {
		keyword:      "",
		field:        "",
		subField:     "",
		value:        "",
		nestedPath:   "",
		isDualNested: false,
		size:         10000,
		name:         "无主资产统计",
		param:        "ownerless_assets_statistics",
		desc: []*KeyValuePair{
			{Key: "noBusinessPerson", Value: "缺失业务系统负责人", Sort: 1},
			{Key: "noMatchBusinessPerson", Value: "业务系统负责人无法匹配", Sort: 2},
			{Key: "noMatchOper", Value: "运维责任人无法匹配", Sort: 3},
			{Key: "noOper", Value: "缺失运维责任人", Sort: 4},
		},
	},
	system_configs.OwnerlessAssetsProportion: {
		keyword:      "",
		field:        "",
		subField:     "",
		value:        "",
		nestedPath:   "",
		isDualNested: false,
		size:         10000,
		name:         "无主资产占比",
		param:        system_configs.OwnerlessAssetsProportion,
		desc: []*KeyValuePair{
			{Key: "has_oper", Value: "有归属资产", Sort: 1},
			{Key: "no_oper", Value: "缺失负责人", Sort: 2},
			{Key: "no_match_oper", Value: "无法匹配负责人", Sort: 3},
		},
	},
	system_configs.BusinessDepartmentAssetCount: {
		keyword:      "",
		field:        "business_department.name.keyword",
		subField:     "network_type",
		value:        "",
		nestedPath:   "business_department",
		isDualNested: false,
		size:         10000,
		name:         "各部门资产数量",
		param:        system_configs.BusinessDepartmentAssetCount,
		xDesc: []*KeyValuePair{
			{Key: "1", Value: "内网资产", Sort: 1},
			{Key: "2", Value: "互联网资产", Sort: 2},
		},
	},
	"personnel_assets_top5": {
		keyword:      "",
		field:        "oper_info.name",
		subField:     "network_type",
		value:        "",
		nestedPath:   "oper_info",
		isDualNested: false,
		size:         5,
		name:         "人员管理资产top5",
		param:        "personnel_assets_top5",
		xDesc: []*KeyValuePair{
			{Key: "1", Value: "内网资产", Sort: 1},
			{Key: "2", Value: "互联网资产", Sort: 2},
		},
		sortByValue:  true,
		sortDesc:     true,
		mustNotEmpty: true,
	},
	"business_status_distribution": {
		keyword:        "",
		field:          "status",
		subField:       "",
		value:          "",
		nestedPath:     "",
		isDualNested:   false,
		size:           10000,
		name:           "业务系统状态分布",
		param:          "business_status_distribution",
		includeMissing: false,
		indexName:      business_system.NewBusinessSystems().IndexName(),
		desc: []*KeyValuePair{
			{Key: "1", Value: "可信", Sort: 1},
			{Key: "2", Value: "待确认", Sort: 2},
			{Key: "3", Value: "黑名单", Sort: 3},
		},
	},
	system_configs.BusinessAssetStatistics: {
		// 对于嵌套字段和普通字段的组合聚合
		keyword:        "",
		field:          "business.business_trusted_state",
		subField:       "network_type",
		value:          "",
		nestedPath:     "business",
		isDualNested:   false,
		size:           10000,
		name:           "业务系统关联资产统计",
		param:          system_configs.BusinessAssetStatistics,
		includeMissing: true,
		desc: []*KeyValuePair{
			{Key: "", Value: "缺失业务系统", Sort: 3},
			{Key: "1", Value: "可信", Sort: 0},
			{Key: "2", Value: "待确认", Sort: 1},
			{Key: "3", Value: "黑名单", Sort: 2},
		},
		xDesc: []*KeyValuePair{
			{Key: "1", Value: "内网资产", Sort: 1},
			{Key: "2", Value: "互联网资产", Sort: 2},
		},
	},
	"vul_type_top5": {
		keyword:      "",
		field:        "vulType",
		indexName:    poc.NewPoc().IndexName(),
		subField:     "",
		value:        "",
		nestedPath:   "",
		isDualNested: false,
		size:         5,
		name:         "漏洞类型数量TOP5",
		param:        "vul_type_top5",
		sortByValue:  true,
		sortDesc:     true,
		mustNotEmpty: true,
	},
	"vul_poc_repaired_info": {
		keyword:      "",
		field:        "",
		indexName:    poc.NewPoc().IndexName(),
		subField:     "",
		value:        "",
		nestedPath:   "",
		isDualNested: false,
		size:         10000,
		name:         "POC漏洞修复情况",
		param:        "vul_poc_repaired_info",
		desc: []*KeyValuePair{
			{Key: "repaired", Value: "已修复", Sort: 1},
			{Key: "norepaired", Value: "未修复", Sort: 2},
		},
	},
	"ip_vul_top10": {
		keyword:      "",
		field:        "ip",
		subField:     "",
		value:        "",
		nestedPath:   "",
		isDualNested: false,
		size:         10,
		name:         "IP关联漏洞TOP10",
		param:        "ip_vul_top10",
		indexName:    poc.NewPoc().IndexName(),
		sortByValue:  true,
		sortDesc:     true,
		mustNotEmpty: true,
	},
	system_configs.InternetVulnerabilityTop10: {
		keyword:      "",
		field:        "ip",
		subField:     "",
		value:        "",
		nestedPath:   "",
		isDualNested: false,
		size:         10,
		name:         "互联网漏洞暴露TOP10",
		param:        system_configs.InternetVulnerabilityTop10,
		indexName:    poc.NewPoc().IndexName(),
		sortByValue:  true,
		sortDesc:     true,
		mustNotEmpty: true,
	},
	"department_vul_top5": {
		keyword:      "",
		field:        "business_department.name.keyword",
		subField:     "",
		value:        "",
		nestedPath:   "business_department",
		isDualNested: false,
		size:         5,
		name:         "部门漏洞TOP5",
		param:        "department_vul_top5",
		indexName:    poc.NewPoc().IndexName(),
		sortByValue:  true,
		sortDesc:     true,
		mustNotEmpty: true,
	},
	system_configs.BusinessVulTop5: {
		keyword:      "",
		field:        "business_name_tmp",
		subField:     "",
		value:        "",
		nestedPath:   "",
		isDualNested: false,
		size:         5,
		name:         "业务系统漏洞TOP5",
		param:        system_configs.BusinessVulTop5,
		indexName:    poc.NewPoc().IndexName(),
		sortByValue:  true,
		sortDesc:     true,
		mustNotEmpty: true,
	},
	"week_asset_count": {
		isDualNested: false,
		size:         1000,
		name:         "近一周资产变化",
		param:        "week_asset_count",
		dataType:     1,
		xDesc: []*KeyValuePair{
			{Key: "internal", Value: "内网", Sort: 1},
			{Key: "external", Value: "互联网", Sort: 2},
		},
	},
	"department_nofixed_vul_statistics": {
		keyword:      "",
		field:        "person_department.name.keyword",
		subField:     "",
		value:        "",
		nestedPath:   "person_department",
		isDualNested: false,
		indexName:    poc.NewPoc().IndexName(),
		size:         10000,
		name:         "未整改漏洞部门分布",
		param:        "department_nofixed_vul_statistics",
		sortByValue:  true,
		sortDesc:     true,
		mustNotEmpty: false,
	},
	"department_vul_not_fixed_top10": {
		keyword:      "",
		field:        "person_department.name.keyword",
		subField:     "",
		value:        "",
		nestedPath:   "person_department",
		isDualNested: false,
		indexName:    poc.NewPoc().IndexName(),
		size:         10000,
		name:         "漏洞超时未修复排名TOP10",
		param:        "department_vul_not_fixed_top10",
		mustNotEmpty: true,
	},
	"vul_fix_rate": {
		keyword:      "",
		field:        "status",
		subField:     "",
		value:        "",
		nestedPath:   "",
		isDualNested: false,
		indexName:    poc.NewPoc().IndexName(),
		size:         10,
		name:         "漏洞修复率",
		param:        "vul_fix_rate",
		searchType:   1,
		// 已处理：包括复测通过(30)、误报(40)、无法修复(41)
		pieParams:    &PieParams{flagType: FlagTypePercentage, searchField: "level", numerator: []string{"30", "40", "41"}, denominator: []string{"all"}},
		mustNotEmpty: false,
	},
	"department_business_system_num": {
		keyword:        "",
		field:          "department_base.name.keyword",
		subField:       "status",
		value:          "",
		nestedPath:     "department_base",
		isDualNested:   false,
		size:           10000,
		name:           "各部门业务系统数量",
		param:          "department_business_system_num",
		includeMissing: false,
		indexName:      business_system.NewBusinessSystems().IndexName(),
		xDesc: []*KeyValuePair{
			{Key: "1", Value: "可信", Sort: 1},
			{Key: "2", Value: "待确认", Sort: 2},
		},
	},
}
