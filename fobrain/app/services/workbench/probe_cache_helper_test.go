package workbench

import (
	"context"
	"testing"
	"time"

	"fobrain/initialize/mysql"
	"fobrain/models/mysql/ip_ranges"

	"github.com/stretchr/testify/assert"
)

// TestFindMatchingIPRanges 测试查找IP匹配的IP段
func TestFindMatchingIPRanges(t *testing.T) {
	// 准备测试数据
	ipRanges := []*ip_ranges.IpRanges{
		{BaseModel: mysql.BaseModel{Id: 1}, IpRange: "10.0.0.0/24"},
		{BaseModel: mysql.BaseModel{Id: 2}, IpRange: "***********-***********00"},
		{BaseModel: mysql.BaseModel{Id: 3}, IpRange: "**********"},
		{BaseModel: mysql.BaseModel{Id: 4}, IpRange: "**********-50"},
	}

	tests := []struct {
		name        string
		assetIP     string
		expectedIds []uint64
		description string
	}{
		{
			name:        "匹配CIDR格式",
			assetIP:     "*********",
			expectedIds: []uint64{1},
			description: "IP在CIDR范围内",
		},
		{
			name:        "匹配范围格式_完整IP",
			assetIP:     "************",
			expectedIds: []uint64{2},
			description: "IP在完整IP范围内",
		},
		{
			name:        "匹配单个IP",
			assetIP:     "**********",
			expectedIds: []uint64{3},
			description: "IP完全匹配单个IP",
		},
		{
			name:        "匹配简化范围格式",
			assetIP:     "***********",
			expectedIds: []uint64{4},
			description: "IP在简化范围格式内",
		},
		{
			name:        "无匹配",
			assetIP:     "*******",
			expectedIds: []uint64{},
			description: "IP不在任何范围内",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := findMatchingIPRanges(tt.assetIP, ipRanges)

			var resultIds []uint64
			for _, r := range result {
				resultIds = append(resultIds, r.Id)
			}

			assert.ElementsMatch(t, tt.expectedIds, resultIds, tt.description)
		})
	}
}

// TestIsIPInRange 测试判断IP是否在指定范围内
func TestIsIPInRange(t *testing.T) {
	tests := []struct {
		name        string
		ip          string
		ipRange     string
		expected    bool
		description string
	}{
		{
			name:        "CIDR格式_在范围内",
			ip:          "*********",
			ipRange:     "10.0.0.0/24",
			expected:    true,
			description: "IP在CIDR网段内",
		},
		{
			name:        "CIDR格式_不在范围内",
			ip:          "*********",
			ipRange:     "10.0.0.0/24",
			expected:    false,
			description: "IP不在CIDR网段内",
		},
		{
			name:        "范围格式_在范围内",
			ip:          "************",
			ipRange:     "***********-***********00",
			expected:    true,
			description: "IP在完整范围内",
		},
		{
			name:        "简化范围格式_在范围内",
			ip:          "***********",
			ipRange:     "**********-50",
			expected:    true,
			description: "IP在简化范围内",
		},
		{
			name:        "单个IP_匹配",
			ip:          "**********",
			ipRange:     "**********",
			expected:    true,
			description: "IP完全匹配",
		},
		{
			name:        "单个IP_不匹配",
			ip:          "**********",
			ipRange:     "**********",
			expected:    false,
			description: "IP不匹配",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := isIPInRange(tt.ip, tt.ipRange)
			assert.Equal(t, tt.expected, result, tt.description)
		})
	}
}

// TestIsIPInRangeFormat 测试范围格式IP判断
func TestIsIPInRangeFormat(t *testing.T) {
	tests := []struct {
		name        string
		ip          string
		rangeStr    string
		expected    bool
		description string
	}{
		{
			name:        "完整范围格式_在范围内",
			ip:          "************",
			rangeStr:    "***********-***********00",
			expected:    true,
			description: "IP在完整IP范围内",
		},
		{
			name:        "简化范围格式_在范围内",
			ip:          "***********",
			rangeStr:    "**********-50",
			expected:    true,
			description: "IP在简化范围内",
		},
		{
			name:        "简化范围格式_边界值_起始",
			ip:          "**********",
			rangeStr:    "**********-50",
			expected:    true,
			description: "IP等于起始值",
		},
		{
			name:        "简化范围格式_边界值_结束",
			ip:          "***********",
			rangeStr:    "**********-50",
			expected:    true,
			description: "IP等于结束值",
		},
		{
			name:        "简化范围格式_超出范围",
			ip:          "***********",
			rangeStr:    "**********-50",
			expected:    false,
			description: "IP超出范围",
		},
		{
			name:        "无效范围格式",
			ip:          "***********",
			rangeStr:    "**********",
			expected:    false,
			description: "范围格式无效（缺少-）",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := isIPInRangeFormat(tt.ip, tt.rangeStr)
			assert.Equal(t, tt.expected, result, tt.description)
		})
	}
}

// TestIpToInt 测试IP地址转换为整数
func TestIpToInt(t *testing.T) {
	tests := []struct {
		name        string
		ip          string
		expected    uint32
		description string
	}{
		{
			name:        "正常IP地址",
			ip:          "***********",
			expected:    3232235777,
			description: "标准IPv4地址转换",
		},
		{
			name:        "全零IP",
			ip:          "0.0.0.0",
			expected:    0,
			description: "全零IP地址",
		},
		{
			name:        "最大IP",
			ip:          "***************",
			expected:    4294967295,
			description: "最大IPv4地址",
		},
		{
			name:        "无效IP格式_缺少段",
			ip:          "192.168.1",
			expected:    0,
			description: "IP段数不足",
		},
		{
			name:        "无效IP格式_超出范围",
			ip:          "256.168.1.1",
			expected:    0,
			description: "IP段超出0-255范围",
		},
		{
			name:        "无效IP格式_非数字",
			ip:          "192.168.a.1",
			expected:    0,
			description: "IP段包含非数字字符",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := ipToInt(tt.ip)
			assert.Equal(t, tt.expected, result, tt.description)
		})
	}
}

// TestGenerateProbeCoverageData 测试生成探针覆盖数据
func TestGenerateProbeCoverageData(t *testing.T) {
	// 准备测试数据
	ipRanges := []*ip_ranges.IpRanges{
		{BaseModel: mysql.BaseModel{Id: 1}, IpRange: "10.0.0.0/24"},
		{BaseModel: mysql.BaseModel{Id: 2}, IpRange: "***********/24"},
		{BaseModel: mysql.BaseModel{Id: 3}, IpRange: "**********/16"},
	}

	ipRangeInfoMap := map[uint64]*IPRangeInfo{
		1: {
			IPRangeId:       1,
			IPRange:         "10.0.0.0/24",
			CoveredProbes:   []uint64{123, 456},
			BusinessSystems: []string{"系统A", "系统B"},
			AssetCount:      10,
		},
		2: {
			IPRangeId:       2,
			IPRange:         "***********/24",
			CoveredProbes:   []uint64{456, 789},
			BusinessSystems: []string{"系统C"},
			AssetCount:      5,
		},
		3: {
			IPRangeId:       3,
			IPRange:         "**********/16",
			CoveredProbes:   []uint64{999},
			BusinessSystems: []string{},
			AssetCount:      0,
		},
	}

	tests := []struct {
		name                 string
		probeId              uint64
		expectedCoveredCount int
		expectedUncovered    int
		description          string
	}{
		{
			name:                 "探针覆盖部分IP段",
			probeId:              123,
			expectedCoveredCount: 1,
			expectedUncovered:    2,
			description:          "探针123只覆盖第一个IP段",
		},
		{
			name:                 "探针覆盖多个IP段",
			probeId:              456,
			expectedCoveredCount: 2,
			expectedUncovered:    1,
			description:          "探针456覆盖前两个IP段",
		},
		{
			name:                 "探针不覆盖任何IP段",
			probeId:              888,
			expectedCoveredCount: 0,
			expectedUncovered:    3,
			description:          "探针888不存在于任何IP段中",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := generateProbeCoverageData(tt.probeId, ipRangeInfoMap, ipRanges)

			assert.NotNil(t, result)
			assert.Equal(t, tt.probeId, result.SourceId)
			assert.Len(t, result.CoveredRanges, tt.expectedCoveredCount)
			assert.Len(t, result.UncoveredRanges, tt.expectedUncovered)
			assert.NotEmpty(t, result.UpdatedAt)

			// 验证时间格式
			_, err := time.Parse(time.RFC3339, result.UpdatedAt)
			assert.NoError(t, err, "时间格式应该符合RFC3339标准")
		})
	}
}

// TestGetProbeCoverageFromCache 测试从缓存获取探针覆盖数据（需要Redis）
func TestGetProbeCoverageFromCache(t *testing.T) {
	ctx := context.Background()

	tests := []struct {
		name        string
		sourceId    string
		expectError bool
		description string
	}{
		{
			name:        "缓存不存在",
			sourceId:    "999999",
			expectError: true,
			description: "使用不存在的sourceId，应该返回错误",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := GetProbeCoverageFromCache(ctx, tt.sourceId)

			if tt.expectError {
				assert.Error(t, err)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
			}
		})
	}
}

// TestRefreshAllProbeCacheAsync 测试异步刷新所有探针缓存
func TestRefreshAllProbeCacheAsync(t *testing.T) {
	// 这个函数启动一个goroutine，难以直接测试，只测试它不会panic
	assert.NotPanics(t, func() {
		RefreshAllProbeCacheAsync()
	}, "异步刷新函数不应该panic")

	// 给一点时间让goroutine启动
	time.Sleep(10 * time.Millisecond)
}

// BenchmarkIpToInt IP转换为整数的性能测试
func BenchmarkIpToInt(b *testing.B) {
	testIPs := []string{
		"***********",
		"********",
		"**********",
		"***************",
		"0.0.0.0",
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		for _, ip := range testIPs {
			ipToInt(ip)
		}
	}
}

// BenchmarkIsIPInRange IP范围判断的性能测试
func BenchmarkIsIPInRange(b *testing.B) {
	testCases := []struct {
		ip      string
		ipRange string
	}{
		{"*********", "10.0.0.0/24"},
		{"************", "***********-***********00"},
		{"**********", "**********"},
		{"***********", "**********-50"},
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		for _, tc := range testCases {
			isIPInRange(tc.ip, tc.ipRange)
		}
	}
}

// TestSaveIPRangeInfoToRedis 测试保存IP段信息到Redis（集成测试）
func TestSaveIPRangeInfoToRedis(t *testing.T) {
	ctx := context.Background()

	ipRangeInfoMap := map[uint64]*IPRangeInfo{
		1: {
			IPRangeId:       1,
			IPRange:         "10.0.0.0/24",
			CoveredProbes:   []uint64{123},
			BusinessSystems: []string{"测试系统"},
			AssetCount:      5,
		},
	}

	// 注意：这个测试需要实际的Redis连接
	err := saveIPRangeInfoToRedis(ctx, ipRangeInfoMap)

	// 如果有Redis连接，测试应该成功；如果没有Redis连接，会返回错误
	if err != nil {
		t.Logf("Redis连接失败（可能是测试环境问题）: %v", err)
	}
}

// TestGenerateAndSaveAllProbeCoverageData 测试批量生成和保存探针数据（集成测试）
func TestGenerateAndSaveAllProbeCoverageData(t *testing.T) {
	ctx := context.Background()

	allProbes := map[uint64]bool{
		123: true,
		456: true,
	}

	ipRangeInfoMap := map[uint64]*IPRangeInfo{
		1: {
			IPRangeId:       1,
			IPRange:         "10.0.0.0/24",
			CoveredProbes:   []uint64{123},
			BusinessSystems: []string{"系统A"},
			AssetCount:      5,
		},
	}

	ipRanges := []*ip_ranges.IpRanges{
		{BaseModel: mysql.BaseModel{Id: 1}, IpRange: "10.0.0.0/24"},
	}

	// 注意：这个测试需要Redis连接
	err := generateAndSaveAllProbeCoverageData(ctx, allProbes, ipRangeInfoMap, ipRanges)

	if err != nil {
		t.Logf("生成和保存数据失败（可能是Redis连接问题）: %v", err)
	}
}

// TestGetAllIPRangesFromDB 测试从数据库获取所有IP段配置（集成测试）
func TestGetAllIPRangesFromDB(t *testing.T) {
	// 注意：这个测试需要实际的数据库连接
	result, err := getAllIPRangesFromDB()

	if err != nil {
		t.Logf("数据库连接失败（可能是测试环境问题）: %v", err)
		return
	}

	// 验证返回结果不为nil
	assert.NotNil(t, result)
	t.Logf("获取到 %d 个IP段配置", len(result))
}

// 辅助函数测试
func TestIPValidationHelpers(t *testing.T) {
	t.Run("测试各种边界情况", func(t *testing.T) {
		// 测试空字符串
		assert.False(t, isIPInRange("", "10.0.0.0/24"))
		assert.False(t, isIPInRange("********", ""))

		// 测试格式错误的CIDR
		assert.False(t, isIPInRange("********", "10.0.0.0/"))
		assert.False(t, isIPInRange("********", "10.0.0.0/33"))

		// 测试无效IP
		assert.Equal(t, uint32(0), ipToInt(""))
		assert.Equal(t, uint32(0), ipToInt("abc.def.ghi.jkl"))
		assert.Equal(t, uint32(0), ipToInt("300.300.300.300"))
	})
}

// 并发安全测试
func TestConcurrentAccess(t *testing.T) {
	t.Run("并发调用ipToInt", func(t *testing.T) {
		const numGoroutines = 10
		const numIterations = 100

		ch := make(chan bool, numGoroutines)

		for i := 0; i < numGoroutines; i++ {
			go func() {
				for j := 0; j < numIterations; j++ {
					result := ipToInt("***********")
					assert.Equal(t, uint32(3232235777), result)
				}
				ch <- true
			}()
		}

		// 等待所有goroutine完成
		for i := 0; i < numGoroutines; i++ {
			<-ch
		}
	})
}

// 测试数据结构完整性
func TestDataStructureIntegrity(t *testing.T) {
	t.Run("测试ProbeCacheData结构完整性", func(t *testing.T) {
		data := &ProbeCacheData{
			SourceId: 123,
			CoveredRanges: []ProbeBoxDetailData{
				{
					IPSegment:      "10.0.0.0/24",
					BusinessSystem: "测试系统",
				},
			},
			UncoveredRanges: []ProbeBoxDetailData{
				{
					IPSegment:      "***********/24",
					BusinessSystem: "生产系统",
				},
			},
			UpdatedAt: time.Now().Format(time.RFC3339),
		}

		assert.Equal(t, uint64(123), data.SourceId)
		assert.Len(t, data.CoveredRanges, 1)
		assert.Len(t, data.UncoveredRanges, 1)
		assert.NotEmpty(t, data.UpdatedAt)

		// 验证时间格式
		_, err := time.Parse(time.RFC3339, data.UpdatedAt)
		assert.NoError(t, err)
	})

	t.Run("测试IPRangeInfo结构完整性", func(t *testing.T) {
		info := &IPRangeInfo{
			IPRangeId:       1,
			IPRange:         "10.0.0.0/24",
			CoveredProbes:   []uint64{123, 456},
			BusinessSystems: []string{"系统A", "系统B"},
			AssetCount:      10,
		}

		assert.Equal(t, uint64(1), info.IPRangeId)
		assert.Equal(t, "10.0.0.0/24", info.IPRange)
		assert.Len(t, info.CoveredProbes, 2)
		assert.Len(t, info.BusinessSystems, 2)
		assert.Equal(t, 10, info.AssetCount)
	})
}

// 边界值测试
func TestBoundaryValues(t *testing.T) {
	t.Run("测试IP转换边界值", func(t *testing.T) {
		// 测试最小值
		assert.Equal(t, uint32(0), ipToInt("0.0.0.0"))

		// 测试最大值
		assert.Equal(t, uint32(4294967295), ipToInt("***************"))

		// 测试边界上的值
		assert.Equal(t, uint32(1), ipToInt("*******"))
		assert.Equal(t, uint32(256), ipToInt("*******"))
		assert.Equal(t, uint32(65536), ipToInt("*******"))
		assert.Equal(t, uint32(16777216), ipToInt("*******"))
	})

	t.Run("测试范围判断边界值", func(t *testing.T) {
		// CIDR边界测试
		assert.True(t, isIPInRange("10.0.0.0", "10.0.0.0/24"))   // 网络地址
		assert.True(t, isIPInRange("**********", "10.0.0.0/24")) // 广播地址
		assert.False(t, isIPInRange("1*******", "10.0.0.0/24"))  // 超出范围

		// 范围格式边界测试
		assert.True(t, isIPInRange("***********", "***********-***********00"))    // 起始值
		assert.True(t, isIPInRange("***********00", "***********-***********00"))  // 结束值
		assert.False(t, isIPInRange("***********", "***********-***********00"))   // 小于起始值
		assert.False(t, isIPInRange("***********01", "***********-***********00")) // 大于结束值
	})
}
