package workbench

import (
	"context"
	"fmt"
	"fobrain/fobrain/app/repository/asset"
	log "fobrain/fobrain/logs"
	"fobrain/initialize/es"
	"fobrain/models/elastic/assets"
	"fobrain/models/elastic/staff"
	"fobrain/models/mysql/system_configs"
	"strconv"
	"sync"

	"git.gobies.org/caasm/fobrain-components/utils"

	"github.com/olivere/elastic/v7"
	"github.com/spf13/cast"
)

const (
	// 缺失业务系统负责人
	noBusinessPerson = "noBusinessPerson"
	// 业务系统负责人无法匹配，若有多个业务系统负责人，那全都不匹配才算，多个人当中有一个匹配，就不算无法匹配
	noMatchBusinessPerson = "noMatchBusinessPerson"
	// 缺失运维人员
	noOper = "noOper"
	// 运维人员无法匹配
	noMatchOper = "noMatchOper"
	// 有业务系统负责人且能匹配台账
	hasBusinessPerson = "hasBusinessPerson"
	// 有运维人员且能匹配到人员台账
	hasOper = "hasOper"
)

type BoxData struct {
	Label    string    `json:"label"`
	Value    string    `json:"value"`
	Children []BoxData `json:"children"`
	Tag      string    `json:"tag"`
}

type BoxDatas struct {
	ContentData []BoxData `json:"content_data"`
}

type OwnerlessAssetQuery struct {
	label string
	query *elastic.BoolQuery
}

var hasBusinessPersonQueryCondition = OwnerlessAssetQuery{
	// 有业务系统负责人且能匹配台账
	hasBusinessPerson,
	assets.NewAssets().GenQueryNoDeletedAndPurged().MustNot(
		elastic.NewTermQuery("business_staff_ids", ""),
	).Must(
		elastic.NewExistsQuery("business_staff_ids"),
	),
}

var hasOperQueryCondition = OwnerlessAssetQuery{
	// 有运维人员且能匹配到人员台账
	hasOper,
	assets.NewAssets().GenQueryNoDeletedAndPurged().MustNot(
		elastic.NewTermQuery("oper_staff_ids", ""),
	).Must(
		elastic.NewExistsQuery("oper_staff_ids"),
	),
}

var noMatchBusinessPersonCondition = OwnerlessAssetQuery{
	// 业务系统负责人无法匹配
	noMatchBusinessPerson,
	assets.NewAssets().GenQueryNoDeletedAndPurged().Must(
		elastic.NewNestedQuery("business.person_base.find_info",
			elastic.NewBoolQuery().Must(
				elastic.NewExistsQuery("business.person_base.find_info.source_value"),
			),
		),
		elastic.NewNestedQuery("business.person_base",
			elastic.NewBoolQuery().Must(
				elastic.NewTermQuery("business.person_base.id", ""),
			),
		),
		elastic.NewBoolQuery().Should(
			elastic.NewBoolQuery().MustNot(elastic.NewExistsQuery("business_staff_ids")),
			elastic.NewTermQuery("business_staff_ids", ""),
		).MinimumNumberShouldMatch(1),
	),
}

var noMatchOperCondition = OwnerlessAssetQuery{
	// 运维人员无法匹配
	noMatchOper,
	assets.NewAssets().GenQueryNoDeletedAndPurged().Must(
		elastic.NewNestedQuery("oper_info",
			elastic.NewBoolQuery().Must(
				elastic.NewExistsQuery("oper_info.id"),
			),
		),
		elastic.NewBoolQuery().Should(
			elastic.NewBoolQuery().MustNot(elastic.NewExistsQuery("oper_staff_ids")),
			elastic.NewTermQuery("oper_staff_ids", ""),
		).MinimumNumberShouldMatch(1),
	),
}
var ownerlessAssetQueries = []OwnerlessAssetQuery{
	{
		// 缺失业务系统负责人
		noBusinessPerson,
		assets.NewAssets().GenQueryNoDeletedAndPurged().MustNot(
			elastic.NewBoolQuery().Must(
				elastic.NewExistsQuery("business_staff_ids"),
				elastic.NewBoolQuery().MustNot(elastic.NewTermQuery("business_staff_ids", "")),
			),
			elastic.NewNestedQuery("business.person_base", elastic.NewMatchAllQuery()),
		),
	},
	noMatchBusinessPersonCondition,
	{
		// 缺失运维人员
		noOper,
		assets.NewAssets().GenQueryNoDeletedAndPurged().MustNot(
			elastic.NewBoolQuery().Must(
				elastic.NewExistsQuery("oper_staff_ids"),
				elastic.NewBoolQuery().MustNot(elastic.NewTermQuery("oper_staff_ids", "")),
			),
			elastic.NewNestedQuery("oper_info", elastic.NewMatchAllQuery()),
		),
	},
	noMatchOperCondition,
}

func (s *WorkbenchService) OwnerlessAssetInfo() ([]*asset.AssetTopResult, error) {
	index := assets.NewAssets().IndexName()
	var results = make([]*asset.AssetTopResult, 0, 16)
	var wg sync.WaitGroup
	var mux sync.Mutex
	for _, q := range ownerlessAssetQueries {
		wg.Add(1)
		go func() {
			defer wg.Done()
			count, err := es.GetCount(index, q.query)
			if err != nil {
				log.GetLogger().Errorf("Error getting count of assets:%s, err: %v\n", q.label, err)
				return
			}

			defer mux.Unlock()
			mux.Lock()
			results = append(results, &asset.AssetTopResult{
				FieldValue: q.label,
				FieldCount: count,
			})
		}()
	}
	wg.Wait()

	return results, nil
}

var businessConditionMap = map[string]struct{}{
	noBusinessPerson:      {},
	noMatchBusinessPerson: {},
	hasBusinessPerson:     {},
}
var operConditionMap = map[string]struct{}{
	noOper:      {},
	noMatchOper: {},
	hasOper:     {},
}

func (s *WorkbenchService) OwnerlessAssetPercentage(condition string, query *elastic.BoolQuery) ([]*asset.AssetTopResult, *system_configs.DataInterpretationResult, error) {
	index := assets.NewAssets().IndexName()
	query1 := assets.NewAssets().GenQueryNoDeletedAndPurged()
	if query != nil {
		query1 = query1.Must(query)
	}
	assetAllCount, derr := es.GetCount(index, query1)
	if derr != nil {
		return nil, nil, derr
	}
	var results = make([]*asset.AssetTopResult, 0, 16)
	var queries = make([]OwnerlessAssetQuery, 0, 6)
	queries = append(queries, ownerlessAssetQueries...)
	queries = append(queries, hasBusinessPersonQueryCondition, hasOperQueryCondition)

	var wg sync.WaitGroup
	var mux sync.Mutex
	for _, q := range queries {
		if condition == "business" {
			if _, ok := businessConditionMap[q.label]; !ok {
				continue
			}
		} else if condition == "oper" {
			if _, ok := operConditionMap[q.label]; !ok {
				continue
			}
		}
		wg.Add(1)
		go func() {
			defer wg.Done()
			var tmpQuery = *q.query
			actualQuery := &tmpQuery
			if query != nil {
				actualQuery = actualQuery.Must(query)
			}
			count, err := es.GetCount(index, actualQuery)
			if err != nil {
				log.GetLogger().Errorf("Error getting count of assets:%s, err: %v\n", q.label, err)
				return
			}
			rate := utils.CalculatePercentageRate(count, assetAllCount)
			if rate > 10000 {
				rate = 10000
			}
			if rate < 0 {
				rate = 0
			}
			defer mux.Unlock()
			mux.Lock()
			results = append(results, &asset.AssetTopResult{
				FieldValue:               q.label,
				FieldCount:               rate,
				InternalExternalSumCount: count,
			})
		}()
	}
	wg.Wait()
	interpretation, err := s.OwnerlessAssetDataInterpretation(condition, results)
	if err != nil {
		return nil, nil, err
	}
	return results, interpretation, nil
}

// 获取配置的指标，然后判断：无主资产<=配置的指标为正常，大于时异常，责任人标注率<配置的指标时为数据不足
func (s *WorkbenchService) OwnerlessAssetDataInterpretation(condition string, results []*asset.AssetTopResult) (*system_configs.DataInterpretationResult, error) {
	var match int64
	for _, result := range results {
		if condition == "business" && result.FieldValue == hasBusinessPerson {
			match = result.FieldCount
		} else if condition == "oper" && result.FieldValue == hasOper {
			match = result.FieldCount
		}
	}
	conf, err := system_configs.NewSystemConfigs().GetWorkbenchDataInterpretation(system_configs.OwnerlessAssetsProportion)
	if err != nil {
		return nil, err
	}
	var tagsStatus int64
	var analysisResult string
	if match < conf.GetDataDeficienciesInPercentage() {
		tagsStatus = system_configs.DataInterpretationTagsStatusDataDeficiencies
		analysisResult = fmt.Sprintf("责任人标注率<%d%%，数据暂不具备分析价值", conf.ReferencePoint.DataDeficienciesInPercentage)
	} else if (10000 - match) <= conf.GetNormalInPercentage() {
		tagsStatus = system_configs.DataInterpretationTagsStatusNormal
		analysisResult = fmt.Sprintf("无主资产占比<=%d%%，基本管理体系健全，但仍有改进空间", conf.ReferencePoint.NormalInPercentage)
	} else if (10000 - match) > conf.GetNormalInPercentage() {
		tagsStatus = system_configs.DataInterpretationTagsStatusAbnormal
		analysisResult = fmt.Sprintf("无主资产占比>%d%%，可能会影响后续风险排查的进度，为了达到更好的管理水平，可将涉及到的资产尽快完成确权", conf.ReferencePoint.NormalInPercentage)
	}
	return &system_configs.DataInterpretationResult{
		TagsStatus:     tagsStatus,
		AnalysisResult: analysisResult,
	}, nil
}
func OwnerlessAssetFloatingBoxData(label string) (any, error) {
	var query *elastic.BoolQuery
	var agg *elastic.TermsAggregation
	if label == hasBusinessPerson {
		// 有业务系统负责人且能匹配台账 top5
		query = hasBusinessPersonQueryCondition.query
		agg = elastic.NewTermsAggregation().Field("business_staff_ids").Size(10)

	} else if label == hasOper {
		// 有运维人员且能匹配到人员台账 top5
		query = hasOperQueryCondition.query
		agg = elastic.NewTermsAggregation().Field("oper_staff_ids").Size(10)
	} else {
		// 无效标签，返回空数据
		return BoxDatas{}, nil
	}

	searchResult, err := es.GetEsClient().Search().
		Index(assets.NewAssets().IndexName()).
		Query(query).
		Aggregation("staff_agg", agg).
		Size(0).
		Do(context.TODO())

	if err != nil {
		return nil, fmt.Errorf("search business person failed: %v", err)
	}

	staffAgg, found := searchResult.Aggregations.Terms("staff_agg")
	if !found {
		return nil, fmt.Errorf("staff_agg aggregation not found")
	}

	boxDatas := BoxDatas{}
	for _, bucket := range staffAgg.Buckets {
		personInfo, err := staff.NewStaff().GetById(context.Background(), cast.ToString(bucket.Key))
		if err != nil || personInfo == nil {
			continue
		}

		boxDatas.ContentData = append(boxDatas.ContentData, BoxData{
			Label:    personInfo.Name,
			Value:    strconv.FormatInt(bucket.DocCount, 10),
			Children: []BoxData{},
			Tag:      personInfo.StatusDesc(),
		})
	}
	if len(boxDatas.ContentData) > 5 {
		boxDatas.ContentData = boxDatas.ContentData[:5]
	}
	return boxDatas, nil

}
