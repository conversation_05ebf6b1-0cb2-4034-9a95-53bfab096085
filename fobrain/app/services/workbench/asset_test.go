package workbench

import (
	testcommon "fobrain/fobrain/tests/common_test"
	"testing"

	"git.gobies.org/caasm/fobrain-components/utils"
	"git.gobies.org/caasm/fobrain-components/utils/localtime"
	"github.com/stretchr/testify/assert"
)

func TestWorkbenchService_tickerRecordAssetCount(t *testing.T) {
	mockEs := testcommon.NewMockServer()
	defer mockEs.Close()

	mockEs.Register("/asset/_count", map[string]interface{}{
		"count": 5,
		"_shards": map[string]interface{}{
			"total":      1,
			"successful": 1,
			"skipped":    0,
			"failed":     0,
		},
	})

	NewWorkbenchService().tickerRecordAssetCount()

}

func TestWorkbenchService_WeeklyAssetCount(t *testing.T) {
	// 获取Mock数据库实例
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()

	// 使用正则表达式匹配SQL查询，直接使用Mock.ExpectQuery避免QuoteMeta
	sqlPattern := `SELECT .*, DATE_FORMAT\(created_at, '%Y-%m-%d'\) AS period_label FROM .* ROW_NUMBER\(\) OVER .* WHERE category = \? .* WHERE rn = 1`

	mockDb.Mock.ExpectQuery(sqlPattern).
		WillReturnRows(mockDb.NewRows([]string{"id", "created_at", "updated_at", "business_count", "business_second", "percentage", "date_hour", "period_label"}).
			AddRow(1, localtime.Parse(utils.DateTimeLayout, "2024-10-12 16:32:31"), localtime.Parse(utils.DateTimeLayout, "2024-10-12 16:32:31"), 100, 200, 5000, "2025-05-01 13", "2024-10-12"))

	got, err := NewWorkbenchService().WeeklyAssetCount()
	assert.Nil(t, err)
	assert.Equal(t, got[0].Children[0].FieldValue, "internal")
	assert.Equal(t, got[0].Children[0].FieldCount, int64(100))

	// 验证数据库Mock期望
	assert.NoError(t, mockDb.ExpectationsWereMet())
}
