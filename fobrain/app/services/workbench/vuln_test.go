package workbench

import (
	"errors"
	"fobrain/fobrain/app/repository/asset"
	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/models/mysql/workbench"
	"testing"
	"time"

	"git.gobies.org/caasm/fobrain-components/utils"
	"github.com/DATA-DOG/go-sqlmock"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/stretchr/testify/assert"
)

// TestWorkbenchService_PocVulnRepairedInfo 测试POC漏洞修复信息
func TestWorkbenchService_PocVulnRepairedInfo(t *testing.T) {
	s := NewWorkbenchService()

	// 设置Mock ES
	mockEs := testcommon.NewMockServer()
	defer mockEs.Close()

	t.Run("ES查询失败", func(t *testing.T) {
		// 不注册任何路由，导致404错误
		mockEs2 := testcommon.NewMockServer()
		defer mockEs2.Close()

		result, description, err := s.PocVulnRepairedInfo()

		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Empty(t, description)
	})
}

// TestWorkbenchService_PocVulTypeTop5 测试POC漏洞类型Top5
func TestWorkbenchService_PocVulTypeTop5(t *testing.T) {
	s := NewWorkbenchService()

	t.Run("正常获取漏洞类型Top5", func(t *testing.T) {
		time.Sleep(time.Second)
		patches := gomonkey.ApplyFunc(asset.AssetTopProcess, func(config asset.AssetTopConfig) ([]*asset.AssetTopResult, error) {
			return []*asset.AssetTopResult{
				{FieldValue: "SQL注入", FieldCount: 100},
				{FieldValue: "XSS", FieldCount: 80},
				{FieldValue: "命令注入", FieldCount: 60},
				{FieldValue: "文件包含", FieldCount: 40},
				{FieldValue: "目录遍历", FieldCount: 20},
			}, nil
		})
		defer patches.Reset()

		result, err := s.PocVulTypeTop5()

		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Len(t, result, 5)
		assert.Equal(t, "SQL注入", result[0].FieldValue)
		assert.Equal(t, int64(100), result[0].FieldCount)
	})

	t.Run("AssetTopProcess失败", func(t *testing.T) {
		time.Sleep(time.Second)
		patches := gomonkey.ApplyFunc(asset.AssetTopProcess, func(config asset.AssetTopConfig) ([]*asset.AssetTopResult, error) {
			return nil, errors.New("ES查询失败")
		})
		defer patches.Reset()

		result, err := s.PocVulTypeTop5()

		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Contains(t, err.Error(), "ES查询失败")
	})

	t.Run("空数据", func(t *testing.T) {
		time.Sleep(time.Second)
		patches := gomonkey.ApplyFunc(asset.AssetTopProcess, func(config asset.AssetTopConfig) ([]*asset.AssetTopResult, error) {
			return []*asset.AssetTopResult{}, nil
		})
		defer patches.Reset()

		result, err := s.PocVulTypeTop5()

		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Len(t, result, 0)
	})
}

// TestWorkbenchService_PocIPTop10 测试POC IP Top10
func TestWorkbenchService_PocIPTop10(t *testing.T) {
	s := NewWorkbenchService()

	// 设置Mock ES
	mockEs := testcommon.NewMockServer()
	defer mockEs.Close()

	t.Run("正常获取IP Top10", func(t *testing.T) {
		time.Sleep(time.Second)
		// Mock ES聚合查询
		mockEs.Register("/poc/_search", map[string]interface{}{
			"took": 5,
			"aggregations": map[string]interface{}{
				"top_ips": map[string]interface{}{
					"buckets": []map[string]interface{}{
						{"key": "***********", "doc_count": 50},
						{"key": "***********", "doc_count": 30},
						{"key": "***********", "doc_count": 20},
						{"key": "***********", "doc_count": 15},
						{"key": "***********", "doc_count": 10},
					},
				},
			},
		})

		// Mock 每个IP的具体计数查询
		mockEs.Register("/poc/_count", map[string]interface{}{
			"count": 50,
		})

		result, err := s.PocIPTop10()

		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.LessOrEqual(t, len(result), 10) // 最多10个

		if len(result) > 0 {
			assert.Equal(t, "***********", result[0].FieldValue)
			// 验证结果按数量排序
			if len(result) > 1 {
				assert.GreaterOrEqual(t, result[0].FieldCount, result[1].FieldCount)
			}
		}
	})

	t.Run("ES聚合查询失败", func(t *testing.T) {
		time.Sleep(time.Second)
		// 不注册任何路由，导致404错误
		mockEs2 := testcommon.NewMockServer()
		defer mockEs2.Close()

		result, err := s.PocIPTop10()

		assert.Error(t, err)
		assert.Nil(t, result)
	})

}

// TestWorkbenchService_tickerRecordNoRepairedVuln 测试定时记录未修复漏洞
func TestWorkbenchService_tickerRecordNoRepairedVuln(t *testing.T) {
	s := NewWorkbenchService()

	// 设置Mock ES
	mockEs := testcommon.NewMockServer()
	defer mockEs.Close()

	// Mock数据库连接
	mockDb := testcommon.InitSqlMock()
	defer mockDb.Close()

	t.Run("记录不存在-创建新记录", func(t *testing.T) {
		// Mock查询现有记录
		mockDb.ExpectQuery("SELECT.*workbench_statistics_by_hour.*").
			WillReturnRows(mockDb.NewRows([]string{"id"}).AddRow(0)) // id为0表示不存在

		// Mock ES计数查询 - 未修复漏洞
		mockEs.Register("/poc/_count", map[string]interface{}{
			"count": 80, // 未修复漏洞数量
		})

		// Mock ES计数查询 - 全部漏洞
		mockEs.Register("/poc/_count", map[string]interface{}{
			"count": 100, // 全部漏洞数量
		})

		// Mock创建记录
		mockDb.ExpectBegin()
		mockDb.ExpectExec("INSERT INTO.*workbench_statistics_by_hour.*").
			WillReturnResult(sqlmock.NewResult(1, 1))
		mockDb.ExpectCommit()

		// 调用函数
		assert.NotPanics(t, func() {
			s.tickerRecordNoRepairedVuln()
		})
	})

	t.Run("记录已存在-跳过创建", func(t *testing.T) {
		// Mock查询现有记录 - 返回已存在的记录
		mockDb.ExpectQuery("SELECT.*workbench_statistics_by_hour.*").
			WillReturnRows(mockDb.NewRows([]string{"id", "category", "date_hour"}).
				AddRow(1, workbench.WorkbenchCategoryNoRepairedVulnerability, time.Now().Format(utils.TimeDateHourLayout)))

		// 不应该有创建操作
		// 调用函数
		assert.NotPanics(t, func() {
			s.tickerRecordNoRepairedVuln()
		})
	})
}

// TestWorkbenchService_PocIPTop10_EmptyResults 测试空结果的情况
func TestWorkbenchService_PocIPTop10_EmptyResults(t *testing.T) {
	s := NewWorkbenchService()

	// 设置Mock ES
	mockEs := testcommon.NewMockServer()
	defer mockEs.Close()

	// Mock ES返回空聚合结果
	mockEs.Register("/poc/_search", map[string]interface{}{
		"took":      5,
		"timed_out": false,
		"hits": map[string]interface{}{
			"total": map[string]interface{}{"value": 0},
		},
		"aggregations": map[string]interface{}{
			"top_ips": map[string]interface{}{
				"buckets": []map[string]interface{}{},
			},
		},
	})

	result, err := s.PocIPTop10()

	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Len(t, result, 0)
}
