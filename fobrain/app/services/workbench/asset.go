package workbench

import (
	"fobrain/fobrain/app/repository/asset"
	log "fobrain/fobrain/logs"
	"fobrain/initialize/es"
	"fobrain/initialize/mysql"
	"fobrain/models/elastic/assets"
	"fobrain/models/mysql/workbench"
	"time"

	"git.gobies.org/caasm/fobrain-components/utils"
	"github.com/olivere/elastic/v7"
)

func (s *WorkbenchService) tickerRecordAssetCount() {
	dataHour := time.Now().Format(utils.TimeDateHourLayout)
	r, _ := workbench.NewWorkbenchStatisticsByHour().First(
		mysql.WithWhere("category = ?", workbench.WorkbenchCategoryWeeklyAssetCount),
		mysql.WithWhere("date_hour = ?", dataHour),
	)
	if r.Id > 0 {
		return
	}

	fn := func(networkType int) (int64, error) {
		query := assets.NewAssets().GenQueryNoDeletedAndPurged().Must(
			elastic.NewTermQuery("network_type", networkType),
		)
		return es.GetCount(assets.NewAssets().IndexName(), query)
	}

	internalCount, err := fn(assets.NetworkTypeInternal)
	if err != nil {
		log.GetLogger().Warnf("tickerRecordAssetCount 查询内网资产数据失败,err:%v\n ", err)
		return
	}
	externalCount, err := fn(assets.NetworkTypeExternal)
	if err != nil {
		log.GetLogger().Warnf("tickerRecordAssetCount 查询外网资产数据失败,err:%v\n ", err)
		return
	}

	err = workbench.NewWorkbenchStatisticsByHour().Create(&workbench.WorkbenchStatisticsByHour{
		Category:            workbench.WorkbenchCategoryWeeklyAssetCount,
		BusinessCount:       internalCount,
		BusinessSecondCount: externalCount,
		DateHour:            dataHour,
	})
	if err != nil {
		log.GetLogger().Warn("tickerRecordAssetCount create record failed ", err)
	}
}

func (s *WorkbenchService) WeeklyAssetCount() ([]*asset.AssetTopResult, error) {
	records, err := workbench.NewWorkbenchStatisticsByHour().Query(workbench.WorkbenchCategoryWeeklyAssetCount, "week")
	if err != nil {
		return nil, err
	}
	var result = make([]*asset.AssetTopResult, 0, 7)
	for _, r := range records {
		result = append(result, &asset.AssetTopResult{
			FieldValue:    r.PeriodLabel,
			FieldCount:    0,
			InternalCount: 0,
			ExternalCount: 0,
			Children: []*asset.AssetTopResult{
				{FieldValue: "internal", FieldCount: r.BusinessCount},
				{FieldValue: "external", FieldCount: r.BusinessSecondCount},
			},
		})
	}
	return result, nil
}
