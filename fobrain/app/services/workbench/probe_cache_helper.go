package workbench

import (
	"context"
	"encoding/json"
	"fmt"
	"net"
	"strconv"
	"strings"
	"time"

	"fobrain/fobrain/logs"
	"fobrain/initialize/es"
	"fobrain/initialize/redis"
	"fobrain/models/elastic/assets"
	"fobrain/models/mysql/ip_ranges"

	"git.gobies.org/caasm/fobrain-components/utils"

	"github.com/olivere/elastic/v7"
	"github.com/pkg/errors"
)

// ProbeCacheData 探针缓存数据
type ProbeCacheData struct {
	SourceId        uint64               `json:"source_id"`
	CoveredRanges   []ProbeBoxDetailData `json:"covered_ranges"`
	UncoveredRanges []ProbeBoxDetailData `json:"uncovered_ranges"`
	UpdatedAt       string               `json:"updated_at"`
}

// IPRangeInfo IP段信息
type IPRangeInfo struct {
	IPRangeId       uint64         `json:"ip_range_id"`
	IPRange         string         `json:"ip_range"`
	CoveredProbes   []uint64       `json:"covered_probes"`   // 覆盖该IP段的探针列表
	BusinessSystems []string       `json:"business_systems"` // 该IP段关联的业务系统
	AssetCount      int            `json:"asset_count"`      // 该IP段内的资产数量
	ProbeCount      map[uint64]int `json:"probe_count"`      // 探针分别覆盖该ip段的数量
}

const (
	ProbeCacheKeyPrefix  = "probe_coverage:"
	IPRangeInfoKey       = "ip_range_info_map"
	AllProbesKey         = "all_probes_list"
	CacheUpdateLock      = "ip_range_cache_update_lock"
	ProbeCacheExpiration = 24 * time.Hour
	LockTimeout          = 50 * time.Minute
)

// GetProbeCoverageFromCache 从缓存获取探针覆盖数据
func GetProbeCoverageFromCache(ctx context.Context, sourceId string) (*ProbeCacheData, error) {
	redisClient := redis.GetRedisClient()

	// 检查缓存是否存在
	cacheKey := fmt.Sprintf("%s%s", ProbeCacheKeyPrefix, sourceId)
	data, err := redisClient.Get(ctx, cacheKey).Result()
	if err != nil {
		// 缓存不存在，触发全量重建,并且直接返回报错
		logs.GetLogger().Info("探针覆盖缓存不存在，触发重建", sourceId)
		go RefreshIPRangeProbeMapping(ctx)
		return nil, errors.WithMessage(err, "探针覆盖缓存不存在，触发重建，稍后再试")
	}

	var cacheData ProbeCacheData
	if err := json.Unmarshal([]byte(data), &cacheData); err != nil {
		return nil, fmt.Errorf("解析缓存数据失败: %v", err)
	}

	return &cacheData, nil
}

// RefreshIPRangeProbeMapping 刷新IP段与探针的映射关系（异步任务调用）
func RefreshIPRangeProbeMapping(ctx context.Context) error {
	redisClient := redis.GetRedisClient()

	// 获取分布式锁
	acquired, err := redisClient.SetNX(ctx, CacheUpdateLock, "1", LockTimeout).Result()
	if err != nil {
		return fmt.Errorf("获取锁失败: %v", err)
	}
	if !acquired {
		logs.GetLogger().Info("IP段探针映射更新任务正在进行中，跳过")
		return nil
	}
	defer redisClient.Del(ctx, CacheUpdateLock)

	logs.GetLogger().Info("开始刷新IP段与探针映射关系")

	// 1. 获取所有IP段配置
	ipRanges, err := getAllIPRangesFromDB()
	if err != nil {
		return fmt.Errorf("获取IP段配置失败: %v", err)
	}

	// 2. 初始化IP段信息映射
	ipRangeInfoMap := make(map[uint64]*IPRangeInfo)
	for _, ipRange := range ipRanges {
		ipRangeInfoMap[ipRange.Id] = &IPRangeInfo{
			IPRangeId:       ipRange.Id,
			IPRange:         ipRange.IpRange,
			CoveredProbes:   make([]uint64, 0),
			BusinessSystems: make([]string, 0),
			AssetCount:      0,
			ProbeCount:      make(map[uint64]int),
		}
	}

	// 3. 扫描所有资产，建立映射关系
	allProbes := make(map[uint64]bool)
	if err := scanAllAssetsAndBuildMapping(ctx, ipRanges, ipRangeInfoMap, allProbes); err != nil {
		return fmt.Errorf("扫描资产建立映射失败: %v", err)
	}

	// 4. 存储IP段信息到Redis
	if err := saveIPRangeInfoToRedis(ctx, ipRangeInfoMap); err != nil {
		return fmt.Errorf("存储IP段信息失败: %v", err)
	}

	// 5. 为每个探针生成覆盖数据并存储
	if err := generateAndSaveAllProbeCoverageData(ctx, allProbes, ipRangeInfoMap, ipRanges); err != nil {
		return fmt.Errorf("生成探针覆盖数据失败: %v", err)
	}

	logs.GetLogger().Infof("IP段与探针映射关系刷新完成，共处理 %d 个探针", len(allProbes))
	return nil
}

// scanAllAssetsAndBuildMapping 扫描所有资产并建立映射关系
func scanAllAssetsAndBuildMapping(ctx context.Context, ipRanges []*ip_ranges.IpRanges, ipRangeInfoMap map[uint64]*IPRangeInfo, allProbes map[uint64]bool) error {
	logs.GetLogger().Info("开始扫描所有资产建立映射关系")

	// 构建查询条件
	query := assets.NewAssets().GenQueryNoDeletedAndPurged()

	// 定义需要的字段
	fields := []string{"ip", "source_ids", "business"}

	// 使用分页方式获取所有数据
	page := 1
	perPage := 1000

	for {
		// 使用es.List方法分页获取数据
		total, assetsList, err := es.List[assets.Assets](page, perPage, query,
			[]elastic.Sorter{elastic.NewFieldSort("updated_at").Desc()},
			fields...)

		if err != nil {
			return fmt.Errorf("查询资产失败: %v", err)
		}

		// 处理当前批次的资产
		for _, asset := range assetsList {
			// 跳过没有IP的资产
			if asset.Ip == "" {
				continue
			}

			// 获取资产的探针列表
			for _, probeId := range asset.SourceIds {
				allProbes[probeId] = true
			}

			// 获取资产的业务系统
			businessSystems := strings.Split(asset.BusinessSystems(), ",")

			// 判断该IP属于哪些IP段
			matchedIPRanges := findMatchingIPRanges(asset.Ip, ipRanges)

			// 更新IP段信息
			for _, ipRange := range matchedIPRanges {
				info := ipRangeInfoMap[ipRange.Id]
				if info == nil {
					continue
				}

				// 添加探针ID（去重）
				for _, probeId := range asset.SourceIds {
					if !utils.ListContains(info.CoveredProbes, probeId) {
						info.CoveredProbes = append(info.CoveredProbes, probeId)
					}
					info.ProbeCount[probeId]++
				}

				// 添加业务系统（去重）
				for _, business := range businessSystems {
					if business != "" && !utils.ListContains(info.BusinessSystems, business) {
						info.BusinessSystems = append(info.BusinessSystems, business)
					}
				}

				// 增加资产计数
				info.AssetCount++
			}
		}

		logs.GetLogger().Infof("已处理 %d 条资产，当前页 %d", len(assetsList), page)

		// 判断是否已处理完所有数据
		if int64((page)*perPage) >= total || len(assetsList) == 0 {
			break
		}

		// 处理下一页
		page++
	}

	return nil
}

// findMatchingIPRanges 查找IP匹配的IP段
func findMatchingIPRanges(assetIP string, ipRanges []*ip_ranges.IpRanges) []*ip_ranges.IpRanges {
	var matchedRanges []*ip_ranges.IpRanges

	for _, ipRange := range ipRanges {
		if isIPInRange(assetIP, ipRange.IpRange) {
			matchedRanges = append(matchedRanges, ipRange)
		}
	}

	return matchedRanges
}

// isIPInRange 判断IP是否在指定范围内
func isIPInRange(ip, ipRange string) bool {
	if strings.Contains(ipRange, "/") {
		// CIDR格式：**********/24
		_, ipNet, err := net.ParseCIDR(ipRange)
		if err != nil {
			return false
		}
		targetIP := net.ParseIP(ip)
		return ipNet.Contains(targetIP)

	} else if strings.Contains(ipRange, "-") {
		// 范围格式：**********-100
		return isIPInRangeFormat(ip, ipRange)
	} else {
		// 单个IP
		return ip == ipRange
	}
}

// isIPInRangeFormat 判断IP是否在范围格式中（如：**********-100）
func isIPInRangeFormat(ip, rangeStr string) bool {
	parts := strings.Split(rangeStr, "-")
	if len(parts) != 2 {
		return false
	}

	startIP := strings.TrimSpace(parts[0])
	endPart := strings.TrimSpace(parts[1])

	var endIP string
	if strings.Contains(endPart, ".") {
		endIP = endPart
	} else {
		// 简化格式：**********-100
		startParts := strings.Split(startIP, ".")
		if len(startParts) != 4 {
			return false
		}
		endIP = fmt.Sprintf("%s.%s.%s.%s", startParts[0], startParts[1], startParts[2], endPart)
	}

	// 转换为数值比较
	targetIPNum := ipToInt(ip)
	startIPNum := ipToInt(startIP)
	endIPNum := ipToInt(endIP)

	return targetIPNum >= startIPNum && targetIPNum <= endIPNum
}

// ipToInt 将IP地址转换为整数
func ipToInt(ip string) uint32 {
	parts := strings.Split(ip, ".")
	if len(parts) != 4 {
		return 0
	}

	var result uint32
	for i, part := range parts {
		num, err := strconv.Atoi(part)
		if err != nil || num < 0 || num > 255 {
			return 0
		}
		result += uint32(num) << (8 * (3 - i))
	}
	return result
}

// saveIPRangeInfoToRedis 保存IP段信息到Redis
func saveIPRangeInfoToRedis(ctx context.Context, ipRangeInfoMap map[uint64]*IPRangeInfo) error {
	redisClient := redis.GetRedisClient()

	data, err := json.Marshal(ipRangeInfoMap)
	if err != nil {
		return fmt.Errorf("序列化IP段信息失败: %v", err)
	}

	return redisClient.Set(ctx, IPRangeInfoKey, data, ProbeCacheExpiration).Err()
}

// generateAndSaveAllProbeCoverageData 生成并保存所有探针的覆盖数据
func generateAndSaveAllProbeCoverageData(ctx context.Context, allProbes map[uint64]bool, ipRangeInfoMap map[uint64]*IPRangeInfo, ipRanges []*ip_ranges.IpRanges) error {
	redisClient := redis.GetRedisClient()

	for probeId := range allProbes {
		// 生成该探针的覆盖数据
		coverageData := generateProbeCoverageData(probeId, ipRangeInfoMap, ipRanges)

		// 存储到Redis
		cacheKey := fmt.Sprintf("%s%d", ProbeCacheKeyPrefix, probeId)
		cacheData, err := json.Marshal(coverageData)
		if err != nil {
			logs.GetLogger().Errorf("序列化探针 %d 数据失败: %v", probeId, err)
			continue
		}

		if err := redisClient.Set(ctx, cacheKey, cacheData, ProbeCacheExpiration).Err(); err != nil {
			logs.GetLogger().Errorf("存储探针 %d 数据失败: %v", probeId, err)
		}
	}

	return nil
}

// generateProbeCoverageData 生成探针覆盖数据
func generateProbeCoverageData(probeId uint64, ipRangeInfoMap map[uint64]*IPRangeInfo, ipRanges []*ip_ranges.IpRanges) *ProbeCacheData {
	var coveredRanges []ProbeBoxDetailData
	var uncoveredRanges []ProbeBoxDetailData

	for _, ipRange := range ipRanges {
		info := ipRangeInfoMap[ipRange.Id]
		if info == nil {
			continue
		}

		// 检查该探针是否覆盖此IP段
		isCovered := utils.ListContains(info.CoveredProbes, probeId)

		// 格式化业务系统
		businessSystemStr := "未关联业务系统"
		if len(info.BusinessSystems) > 0 {
			businessSystemStr = strings.Join(info.BusinessSystems, "、")
		}

		boxData := ProbeBoxDetailData{
			IPSegment:      info.IPRange,
			BusinessSystem: businessSystemStr,
		}

		if isCovered {
			coveredRanges = append(coveredRanges, boxData)
		} else {
			uncoveredRanges = append(uncoveredRanges, boxData)
		}
	}

	return &ProbeCacheData{
		SourceId:        probeId,
		CoveredRanges:   coveredRanges,
		UncoveredRanges: uncoveredRanges,
		UpdatedAt:       time.Now().Format(time.RFC3339),
	}
}

// getAllIPRangesFromDB 从数据库获取所有IP段配置
func getAllIPRangesFromDB() ([]*ip_ranges.IpRanges, error) {
	model := ip_ranges.NewIpRangesModel()
	ranges, _, err := model.List(0, 0)
	if err != nil {
		return nil, err
	}
	return ranges, nil
}

// RefreshAllProbeCacheAsync 异步刷新所有探针缓存（IP段更新时调用）
func RefreshAllProbeCacheAsync() {
	go func() {
		ctx := context.Background()
		if err := RefreshIPRangeProbeMapping(ctx); err != nil {
			logs.GetLogger().Errorf("异步刷新探针缓存失败: %v", err)
		}
	}()
}
