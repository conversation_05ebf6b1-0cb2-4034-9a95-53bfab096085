package cascade_service

import (
	"fobrain/fobrain/common/localtime"
	"fobrain/models/elastic/assets"
	"fobrain/models/elastic/poc"
)

type GetAssetListRequest struct {
	TaskId      string `json:"task_id"`
	NetworkType int    `json:"network_type"`
}

type GetVulnListRequest struct {
	TaskId string `json:"task_id"`
}

type SaveAsset struct {
	TaskId  string           `json:"task_id"`
	Page    int              `json:"page"`
	PerPage int              `json:"per_page"`
	Total   int              `json:"total"`
	Data    []*assets.Assets `json:"data"`
}

type SavePoc struct {
	TaskId  string     `json:"task_id"`
	Page    int        `json:"page"`
	PerPage int        `json:"per_page"`
	Total   int        `json:"total"`
	Data    []*poc.Poc `json:"data"`
}

type NewUpgradeRequest struct {
	PackageId   uint64 `json:"package_id"`
	PackageType int    `json:"package_type"`
	PackageName string `json:"package_name"`
	Version     string `json:"version"`
	PackageHash string `json:"package_hash"`
	DownloadUrl string `json:"download_url"`
	TaskType    int    `json:"task_type"`
	NodeId      int    `json:"node_id"`
}

type GenerateDownloadTokenRequest struct {
	NodeId         int             `json:"node_id"`
	RecordId       uint64          `json:"record_id"`
	PackageId      uint64          `json:"package_id"`
	PackageType    int             `json:"package_type"`
	PackageName    string          `json:"package_name"`
	Version        string          `json:"version"`
	ExpirationTime *localtime.Time `json:"expiration_time"`
}

type GetDownloadTokenRequest struct {
	RecordId uint64 `json:"record_id"`
	Token    string `json:"token"`
}
