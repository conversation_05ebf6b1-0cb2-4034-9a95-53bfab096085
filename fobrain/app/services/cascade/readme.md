### 升级包下发、下载流程
``` mermaid
sequenceDiagram
    participant 上级节点
    participant Redis
    participant 下级节点
    participant 文件服务器(上级节点)
    
    %% 创建升级记录
    Note over 上级节点: 下发升级包
    上级节点->>下级节点: TriggerNewUpgrade(nodeId, request)
    Note over 下级节点: HandleNewUpgrade(nodeId, data)
    下级节点->>下级节点: 创建升级记录(CascadeUpgradeRecord)
    下级节点->>下级节点: 存储包ID、包名、版本号、哈希值、下载URL等
    
    %% 生成下载令牌
     Note over 下级节点: 触发下载
    下级节点->>上级节点: TriggerGenerateDownloadToken(request)
    Note over 上级节点: HandleGenerateDownloadToken(nodeId, data)
    上级节点->>上级节点: 生成随机Token(16位)
    上级节点->>Redis: 存储Token与升级信息(有效期15分钟)
    
    %% 发送下载令牌
    上级节点->>下级节点: TriggerSendDownloadToken(nodeId, request)
    Note over 上级节点: 传递RecordId和Token信息
    
    %% 使用令牌下载文件
    下级节点->>下级节点: HandleGetDownloadToken(nodeId, data)
    下级节点->>下级节点: 查询升级记录信息(根据RecordId)
    下级节点->>文件服务器(上级节点): 发送POST请求(URL=downloadUrl, body={"token": token})
    Note over 下级节点,文件服务器(上级节点): 使用TLS传输但禁用证书验证
    
    文件服务器(上级节点)-->>下级节点: 返回文件数据流
    下级节点->>下级节点: 检查存储目录是否存在
    下级节点->>下级节点: 如不存在则创建目录(/app/public/upgrade/)
    下级节点->>下级节点: 创建文件并保存
    
    %% 更新下载状态
    下级节点->>下级节点: 更新升级记录状态为已下载
    下级节点->>下级节点: 记录下载时间和文件路径
    Note over 下级节点: 文件下载成功
```