package cascade_service

import (
	"fobrain/fobrain/common/localtime"
	"fobrain/fobrain/logs"
	"fobrain/models/mysql/cascade_sync_record"
	"fobrain/module/cascade/sdk"
	"time"

	"github.com/spf13/cast"
)

// TriggerGetAssetList 触发获取资产列表
func TriggerGetAssetList(nodeId int) error {
	logs.GetLogger().Info("下发获取资产列表命令")
	// 创建任务
	cascadeSyncRecord := &cascade_sync_record.CascadeSyncRecord{
		StartTime:    localtime.NewLocalTime(time.Now()),
		EndTime:      nil,
		NodeId:       uint64(nodeId),
		Tag:          cascade_sync_record.AssetTag,
		SuccessCount: 0,
		FailCount:    0,
		Status:       cascade_sync_record.Waiting,
	}
	err := cascade_sync_record.NewCascadeSyncRecord().Create(cascadeSyncRecord)
	if err != nil {
		logs.GetLogger().Errorf("创建节点同步记录失败:%v", err)
		return err
	}
	// 下发命令
	err = sdk.SendCmdToServer(nodeId, CmdGetAssetList, &GetAssetListRequest{
		TaskId:      cast.ToString(cascadeSyncRecord.Id),
		NetworkType: 1,
	})
	if err != nil {
		// 更新同步任务记录
		err2 := cascade_sync_record.NewCascadeSyncRecord().UpdateByRaw(cascadeSyncRecord.Id, 0, false, 0)
		if err2 != nil {
			logs.GetLogger().Errorf("更新同步任务记录失败:%v", err2)
		}
		logs.GetLogger().Errorf("下发获取资产列表命令失败:%v", err)
		return err
	}
	return nil
}

// TriggerGetVulnList 触发获取漏洞列表
func TriggerGetVulnList(nodeId int) error {
	logs.GetLogger().Info("下发获取漏洞列表命令")
	// 创建任务
	cascadeSyncRecord := &cascade_sync_record.CascadeSyncRecord{
		StartTime:    localtime.NewLocalTime(time.Now()),
		EndTime:      nil,
		NodeId:       uint64(nodeId),
		Tag:          cascade_sync_record.VulTag,
		SuccessCount: 0,
		FailCount:    0,
		Status:       cascade_sync_record.Waiting,
		Total:        0,
	}
	err := cascade_sync_record.NewCascadeSyncRecord().Create(cascadeSyncRecord)
	if err != nil {
		logs.GetLogger().Errorf("创建节点同步记录失败:%v", err)
		return err
	}
	// 下发命令
	err = sdk.SendCmdToServer(nodeId, CmdGetVulnList, &GetVulnListRequest{
		TaskId: cast.ToString(cascadeSyncRecord.Id),
	})
	if err != nil {
		// 更新同步任务记录
		err2 := cascade_sync_record.NewCascadeSyncRecord().UpdateByRaw(cascadeSyncRecord.Id, 0, false, 0)
		if err2 != nil {
			logs.GetLogger().Errorf("更新同步任务记录失败:%v", err2)
		}
		logs.GetLogger().Errorf("下发获取漏洞列表命令失败:%v", err)
		return err
	}
	return nil
}

// TriggerNewUpgrade 触发创建升级记录
func TriggerNewUpgrade(nodeId int, request *NewUpgradeRequest) {
	logs.GetLogger().Info("下发创建升级记录命令")
	err := sdk.SendCmdToServer(nodeId, CmdNewUpgrade, &NewUpgradeRequest{
		PackageId:   request.PackageId,
		PackageName: request.PackageName,
		PackageType: request.PackageType,
		Version:     request.Version,
		PackageHash: request.PackageHash,
		DownloadUrl: request.DownloadUrl,
		TaskType:    request.TaskType,
		NodeId:      request.NodeId,
	})
	if err != nil {
		logs.GetLogger().Errorf("下发创建升级记录命令失败:%v", err)
	}
}

// TriggerGenerateDownloadToken 触发生成api token
func TriggerGenerateDownloadToken(request *GenerateDownloadTokenRequest) {
	logs.GetLogger().Info("下发生成下载token命令")
	err := sdk.SendCmdToClient(CmdGenerateDownloadToken, &GenerateDownloadTokenRequest{
		RecordId:    request.RecordId,
		NodeId:      request.NodeId,
		PackageId:   request.PackageId,
		PackageName: request.PackageName,
		PackageType: request.PackageType,
		Version:     request.Version,
	})
	if err != nil {
		logs.GetLogger().Errorf("下发生成下载token命令失败:%v", err)
	}
}

// TriggerSendDownloadToken 触发发送api token
func TriggerSendDownloadToken(nodeId int, request *GetDownloadTokenRequest) {
	logs.GetLogger().Info("下发获取下载token命令")
	err := sdk.SendCmdToServer(nodeId, CMDGetDownloadToken, &GetDownloadTokenRequest{
		RecordId: request.RecordId,
		Token:    request.Token,
	})
	if err != nil {
		logs.GetLogger().Errorf("下发获取下载token命令失败%v", err)
	}
}
