package cascade_service

import (
	request "fobrain/fobrain/app/request/cascade"
	testcommon "fobrain/fobrain/tests/common_test"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/alicebob/miniredis/v2"
	goRedis "github.com/go-redis/redis/v8"
	"github.com/olivere/elastic/v7"
	"github.com/stretchr/testify/assert"
)

func TestListCascadeAsset(t *testing.T) {
	mockDB := testcommon.InitSqlMock()
	defer mockDB.Close()
	mockDB.ExpectQuery("SELECT * FROM `cascade_sync_records` WHERE `node_id` = ? AND status = ? AND tag = ? ORDER BY created_at DESC,`cascade_sync_records`.`id` LIMIT 1").
		WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg()).WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow("1"))
	mockDB.ExpectQuery("SELECT `name` FROM `network_areas` WHERE id = ?").
		WithArgs(sqlmock.AnyArg()).WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow("1"))
	mockDB.ExpectQuery("SELECT * FROM `data_sources` WHERE id in (?)").
		WithArgs(sqlmock.AnyArg()).WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))
	mockDB.ExpectQuery("SELECT name FROM `data_nodes` WHERE id IN (?)").
		WithArgs(sqlmock.AnyArg()).WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow("1"))

	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()
	mockServer.Register("cascade_asset/_count", elastic.CountResponse{
		Count:           1,
		TerminatedEarly: false,
		Shards: &elastic.ShardsInfo{
			Total:      1,
			Successful: 1,
			Skipped:    0,
			Failed:     0,
		},
	})
	mockServer.Register("cascade_asset/_search", []*elastic.SearchHit{
		{
			Id: "1",
		},
	})

	params := &request.ListCascadeRequest{NodeId: 1}
	err, _, _ := ListCascadeAsset(params)
	assert.Nil(t, err)
}

func TestListCascadeVuln(t *testing.T) {
	mockDB := testcommon.InitSqlMock()
	defer mockDB.Close()
	mockDB.ExpectQuery("SELECT * FROM `cascade_sync_records` WHERE `node_id` = ? AND status = ? AND tag = ? ORDER BY created_at DESC,`cascade_sync_records`.`id` LIMIT 1").
		WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg()).WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow("1"))
	mockDB.ExpectQuery("SELECT * FROM `data_sources` WHERE id in (?)").
		WithArgs(sqlmock.AnyArg()).WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))
	mockDB.ExpectQuery("SELECT name FROM `data_nodes` WHERE id IN (?)").
		WithArgs(sqlmock.AnyArg()).WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow("1"))
	mockDB.ExpectQuery("SELECT `name` FROM `network_areas` WHERE id = ?").
		WithArgs(sqlmock.AnyArg()).WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow("1"))

	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()
	mockServer.Register("cascade_poc/_count", elastic.CountResponse{
		Count:           1,
		TerminatedEarly: false,
		Shards: &elastic.ShardsInfo{
			Total:      1,
			Successful: 1,
			Skipped:    0,
			Failed:     0,
		},
	})
	mockServer.Register("cascade_poc/_search", []*elastic.SearchHit{
		{
			Id: "1",
		},
	})

	params := &request.ListCascadeRequest{NodeId: 1}
	err, _, _ := ListCascadeVuln(params)
	assert.Nil(t, err)
}

func TestDeleteCascadeAsset(t *testing.T) {
	s, err := miniredis.Run()
	if err != nil {
		panic(err)
	}
	defer s.Close()

	cli := goRedis.NewClient(&goRedis.Options{
		Addr: s.Addr(),
	})
	testcommon.SetRedisClient(cli)
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()
	mockServer.Register("/cascade_asset/_delete_by_query", &elastic.BulkIndexByScrollResponse{
		Deleted: 1,
	})
	params := &request.DeleteCascadeAssetRequest{Ids: []string{"asdfghjkl"}}
	err = DeleteCascadeAsset(params)
	assert.Nil(t, err)
}

func TestDeleteCascadeVuln(t *testing.T) {
	s, err := miniredis.Run()
	if err != nil {
		panic(err)
	}
	defer s.Close()

	cli := goRedis.NewClient(&goRedis.Options{
		Addr: s.Addr(),
	})
	testcommon.SetRedisClient(cli)
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()
	mockServer.Register("/cascade_poc/_delete_by_query", &elastic.BulkIndexByScrollResponse{
		Deleted: 1,
	})
	params := &request.DeleteCascadeVulnRequest{Ids: []string{"asdfghjkl"}}
	err = DeleteCascadeVuln(params)
	assert.Nil(t, err)
}

// TestSearchSort 测试 searchSort 函数
func TestSearchSort(t *testing.T) {
	tests := []struct {
		name      string
		fieldKey  string
		fieldSort string
		expected  map[string]bool
	}{
		{
			name:      "指定排序字段-正序",
			fieldKey:  "name",
			fieldSort: "ascend",
			expected:  map[string]bool{"name": true},
		},
		{
			name:      "指定排序字段-倒序",
			fieldKey:  "name",
			fieldSort: "descend",
			expected:  map[string]bool{"name": false},
		},
		{
			name:      "指定排序字段-其他值",
			fieldKey:  "name",
			fieldSort: "other",
			expected:  map[string]bool{"name": false},
		},
		{
			name:      "空字段名",
			fieldKey:  "",
			fieldSort: "ascend",
			expected:  map[string]bool{"updated_at": false, "created_at": false},
		},
		{
			name:      "空字段名和空排序",
			fieldKey:  "",
			fieldSort: "",
			expected:  map[string]bool{"updated_at": false, "created_at": false},
		},
		{
			name:      "特殊字段名",
			fieldKey:  "updated_at",
			fieldSort: "ascend",
			expected:  map[string]bool{"updated_at": true},
		},
		{
			name:      "特殊字段名-倒序",
			fieldKey:  "created_at",
			fieldSort: "descend",
			expected:  map[string]bool{"created_at": false},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := searchSort(tt.fieldKey, tt.fieldSort)
			assert.Equal(t, len(tt.expected), len(result), "返回的map长度不符合预期")
			for key, expectedValue := range tt.expected {
				actualValue, exists := result[key]
				assert.True(t, exists, "键 %s 应该存在", key)
				assert.Equal(t, expectedValue, actualValue, "键 %s 的值不符合预期", key)
			}
		})
	}
}
