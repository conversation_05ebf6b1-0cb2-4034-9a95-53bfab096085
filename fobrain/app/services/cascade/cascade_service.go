package cascade_service

import (
	"encoding/json"
	"fobrain/fobrain/logs"
	"fobrain/module/cascade/sdk"
)

const (
	CmdGetAssetList = "get_asset_list"
	CmdGetVulnList  = "get_vuln_list"
	CmdSaveAsset    = "save_asset"
	CmdSaveVuln     = "save_vuln"
)

const (
	CmdNewUpgrade            = "new_upgrade"
	CmdGenerateDownloadToken = "Get_download_token"
	CMDGetDownloadToken      = "get_download_token"
)

// 注册解析命令
func RegisterCmdHandler() {
	// 获取资产列表
	sdk.RegisterCmdHandler(CmdGetAssetList, HandleGetAssetList)

	// 获取漏洞列表
	sdk.RegisterCmdHandler(CmdGetVulnList, HandleGetVulnList)

	// 保存资产
	sdk.RegisterCmdHandler(CmdSaveAsset, HandleSaveAsset)

	// 保存漏洞
	sdk.RegisterCmdHandler(CmdSaveVuln, HandleSaveVuln)

	// 创建更新记录
	sdk.RegisterCmdHandler(CmdNewUpgrade, HandleNewUpgrade)

	// 生成ApiToken
	sdk.RegisterCmdHandler(CmdGenerateDownloadToken, HandleGenerateDownloadToken)

	// 获取APIToken
	sdk.RegisterCmdHandler(CMDGetDownloadToken, HandleGetDownloadToken)
}

// 解析tag内容
func ParseTag(tag string) map[string]bool {
	tagMap := make(map[string]bool)
	err := json.Unmarshal([]byte(tag), &tagMap)
	if err != nil {
		logs.GetLogger().Errorf("获取节点tag，解析Json失败:%s", err.Error())
		return nil
	}
	return tagMap
}
