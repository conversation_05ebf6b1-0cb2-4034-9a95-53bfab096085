package cascade_service

import (
	"context"
	"errors"
	"fmt"
	request "fobrain/fobrain/app/request/cascade"
	"fobrain/initialize/es"
	cascade_model "fobrain/models/elastic/cascade"
	"fobrain/models/mysql/cascade_sync_record"
	redis_helper "fobrain/models/redis"
	distributedlock "fobrain/pkg/distributedLock"
	"fobrain/pkg/utils"

	"fobrain/module/cascade/sdk"

	"github.com/olivere/elastic/v7"
)

func ListCascadeAsset(params *request.ListCascadeRequest) ([]any, int64, error) {
	// 查询所有成功的同步记录
	records, err := cascade_sync_record.NewCascadeSyncRecord().LastSuccessfulRecord(uint64(params.NodeId), cascade_sync_record.AssetTag)
	if err != nil {
		return nil, 0, err
	}
	if len(records) == 0 {
		return []any{}, 0, nil
	}
	// 获取所有记录id
	recordIds := make([]string, 0, len(records))
	for _, record := range records {
		recordIds = append(recordIds, fmt.Sprintf("%d", record.Id))
	}
	query := elastic.NewBoolQuery().Must(elastic.NewTermsQueryFromStrings("record_id", recordIds...))
	if params.Keyword != "" {
		query.Must(elastic.NewMatchQuery("ip.keyword", params.Keyword))
	} else {
		query.Must(elastic.NewMatchAllQuery())
	}
	if params.NodeId > 0 {
		query.Must(elastic.NewTermQuery("cascade_id", params.NodeId))
	}

	count, err := es.GetCount(cascade_model.NewCascadeAsset().IndexName(), query)
	if err != nil {
		return []any{}, int64(0), errors.New("级联资产统计失败")
	}

	if count > 0 {
		searchService := es.GetEsClient().Search(cascade_model.NewCascadeAsset().IndexName()).From(es.GetFrom(params.Page, params.PerPage)).
			Size(es.GetSize(params.PerPage)).
			Query(query)
		sortList := searchSort(params.Field, params.Order)
		for key, value := range sortList {
			if key != "updated_at" && key != "created_at" {
				searchService = searchService.Sort(key, value)
			}
		}
		for key, value := range sortList {
			if key == "updated_at" || key == "created_at" {
				searchService = searchService.SortBy(
					es.TmpSortByWrongDate(key, !value),
				)
			}
		}
		res, err := searchService.Do(context.Background())
		if err != nil {
			return []any{}, 0, err
		}
		// 获取所有节点信息
		allNodes, err := sdk.GetAllNodes()
		if err != nil {
			return []any{}, 0, err
		}
		mapNodeInfo := make(map[uint64]string)
		for _, node := range allNodes {
			mapNodeInfo[node.Id] = node.Name
		}
		hits := res.Hits.Hits
		list := make([]any, 0, len(hits))
		for _, hit := range hits {
			asset := utils.ParseSampleHash(hit, &cascade_model.CascadeAsset{})
			cascadeName := ""
			// int转uint64
			if cascadeId, ok := asset["cascade_id"].(int); ok {
				if nodeName, ok := mapNodeInfo[uint64(cascadeId)]; ok {
					cascadeName = nodeName
				}
			}
			// 补充节点名称
			asset["cascade_name"] = cascadeName
			list = append(list, asset)
		}
		return list, count, nil
	}
	return []any{}, 0, nil
}

// searchSort 设置排序条件
func searchSort(fieldKey string, fieldSort string) map[string]bool {
	sortList := make(map[string]bool)
	if fieldKey != "" {
		sortList[fieldKey] = fieldSort == "ascend"
		return sortList
	}
	sortList["updated_at"] = false
	sortList["created_at"] = false
	return sortList
}

func ListCascadeVuln(params *request.ListCascadeRequest) ([]any, int64, error) {
	// 查询所有成功的同步记录
	records, err := cascade_sync_record.NewCascadeSyncRecord().LastSuccessfulRecord(uint64(params.NodeId), cascade_sync_record.VulTag)
	if err != nil {
		return nil, 0, err
	}
	if len(records) == 0 {
		return []any{}, 0, nil
	}
	recordIds := make([]string, 0, len(records))
	for _, record := range records {
		recordIds = append(recordIds, fmt.Sprintf("%d", record.Id))
	}
	query := elastic.NewBoolQuery().Must(elastic.NewTermsQueryFromStrings("record_id", recordIds...))
	if params.Keyword != "" {
		query = cascade_model.NewCascadePoc().NewKeywordQuery(params.Keyword, query)
	} else {
		query.Must(elastic.NewMatchAllQuery())
	}
	if params.NodeId > 0 {
		query.Must(elastic.NewTermQuery("cascade_id", params.NodeId))
	}

	count, err := es.GetCount(cascade_model.NewCascadePoc().IndexName(), query)
	if err != nil {
		return []any{}, 0, errors.New("级联漏洞统计失败")
	}

	if count > 0 {
		searchService := es.GetEsClient().Search(cascade_model.NewCascadePoc().IndexName()).From(es.GetFrom(params.Page, params.PerPage)).
			Size(es.GetSize(params.PerPage)).
			Query(query)
		sortList := searchSort(params.Field, params.Order)
		for key, value := range sortList {
			if key != "updated_at" && key != "created_at" {
				searchService = searchService.Sort(key, value)
			}
		}
		for key, value := range sortList {
			if key == "updated_at" || key == "created_at" {
				searchService = searchService.SortBy(
					es.TmpSortByWrongDate(key, !value),
				)
			}
		}
		res, err := searchService.Do(context.Background())
		if err != nil {
			return []any{}, 0, err
		}
		// 获取所有节点信息
		allNodes, err := sdk.GetAllNodes()
		if err != nil {
			return []any{}, 0, err
		}
		mapNodeInfo := make(map[uint64]string)
		for _, node := range allNodes {
			mapNodeInfo[node.Id] = node.Name
		}
		hits := res.Hits.Hits
		list := make([]any, 0, len(hits))
		for _, hit := range hits {
			asset := utils.ParseSampleHash(hit, &cascade_model.CascadePoc{})
			cascadeName := ""
			// int转uint64
			if cascadeId, ok := asset["cascade_id"].(int); ok {
				if nodeName, ok := mapNodeInfo[uint64(cascadeId)]; ok {
					cascadeName = nodeName
				}
			}
			// 补充节点名称
			asset["cascade_name"] = cascadeName
			list = append(list, asset)
		}
		return list, count, nil
	}
	return []any{}, 0, nil
}

func DeleteCascadeAsset(params *request.DeleteCascadeAssetRequest) error {
	lockKey := redis_helper.GetCascadeAssetLockKey()
	ok := distributedlock.Lock(lockKey, "DeleteCascadeAsset", 600)
	if !ok {
		return errors.New("数据处理中，无法删除数据")
	}
	defer distributedlock.Unlock(lockKey, "DeleteCascadeAsset")
	query := elastic.NewBoolQuery()
	if params.NodeId > 0 {
		query = query.Must(elastic.NewTermQuery("cascade_id", params.NodeId))
	}
	if len(params.Ids) > 0 {
		query = query.Must(elastic.NewTermsQueryFromStrings("id", params.Ids...))
	} else if params.Keyword != "" {
		query = query.Must(elastic.NewMatchQuery("ip.keyword", params.Keyword))
	} else {
		query = query.Must(elastic.NewMatchAllQuery())
	}

	_, err := es.GetEsClient().DeleteByQuery(cascade_model.NewCascadeAsset().IndexName()).
		Query(query).
		Refresh("true").
		ScrollSize(200).
		Do(context.TODO())
	return err
}

func DeleteCascadeVuln(params *request.DeleteCascadeVulnRequest) error {
	lockKey := redis_helper.GetCascadeVulnLockKey()
	ok := distributedlock.Lock(lockKey, "DeleteCascadeVuln", 600)
	if !ok {
		return errors.New("数据处理中，无法删除数据")
	}
	defer distributedlock.Unlock(lockKey, "DeleteCascadeVuln")
	query := elastic.NewBoolQuery()
	if params.NodeId > 0 {
		query = query.Must(elastic.NewTermQuery("cascade_id", params.NodeId))
	}
	if len(params.Ids) > 0 {
		query = query.Must(elastic.NewTermsQueryFromStrings("id", params.Ids...))
	} else if params.Keyword != "" {
		query = cascade_model.NewCascadePoc().NewKeywordQuery(params.Keyword, query)
	} else {
		query = query.Must(elastic.NewMatchAllQuery())
	}

	_, err := es.GetEsClient().DeleteByQuery(cascade_model.NewCascadePoc().IndexName()).
		Query(query).
		Refresh("true").
		ScrollSize(200).
		Do(context.TODO())
	return err
}
