package cascade_service

import (
	"bytes"
	"context"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"fobrain/models/mysql/cascade_sync_record"
	distributedlock "fobrain/pkg/distributedLock"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"time"

	"github.com/spf13/cast"
	"go-micro.dev/v4/logger"

	"fobrain/fobrain/logs"
	"fobrain/models/elastic/poc"
	"fobrain/models/mysql/cascade_upgrade"

	"fobrain/fobrain/common/localtime"
	"fobrain/initialize/es"
	"fobrain/initialize/mysql"
	"fobrain/initialize/redis"
	"fobrain/models/elastic/assets"
	cascade_model "fobrain/models/elastic/cascade"
	"fobrain/models/mysql/data_source"
	redis_helper "fobrain/models/redis"
	"fobrain/module/cascade/sdk"
	"fobrain/pkg/cfg"
	"fobrain/pkg/utils"

	"github.com/olivere/elastic/v7"
)

// HandleGetAssetList 处理获取资产列表命令
func HandleGetAssetList(nodeId int, data []byte) {
	logger := logs.GetLogger()
	logger.Info("开始处理命令：get_asset_list")
	defer func() {
		if reErr := recover(); reErr != nil {
			logs.GetCrontabLogger().Infof("处理获取资产列表命令 > FAIL, 返回结果: %v", reErr)
			return
		}
	}()

	// 解析命令参数
	var params GetAssetListRequest
	if err := json.Unmarshal(data, &params); err != nil {
		// todo 返回错误信息
		logger.Errorf("解析资产列表命令参数失败: %v", err)
		return
	}

	// todo 创建被拉取任务

	query := elastic.NewBoolQuery()
	if params.NetworkType != 0 {
		query.Must(elastic.NewTermQuery("network_type", params.NetworkType))
	} else {
		query.Must(elastic.NewMatchAllQuery())
	}
	// 查询资产列表数量
	client := es.GetEsClient()
	total, err := client.Count(assets.NewAssets().IndexName()).Query(query).Do(context.Background())
	if err != nil {
		// todo 返回错误信息
		logger.Errorf("统计资产数据量 Error: %v", err)
		return
	}
	logger.Infof("统计资产数据量: %d", total)

	page := 1
	perPage := 500

	scroll := client.Scroll().Index(assets.NewAssets().IndexName()).
		Query(query).
		Sort("created_at", false).
		Size(perPage).
		Scroll("1m") // 滚动查询
	defer scroll.Clear(context.Background())
	for {
		logger.Infof("------ 开始处理资产数据,Page: %d,perPage:%d ------", page, perPage)
		// 初始化返回数据
		result := &SaveAsset{
			TaskId: params.TaskId,
			Total:  int(total),
			Data:   nil,
		}
		result.Page = page
		result.PerPage = perPage
		result.Data = make([]*assets.Assets, 0, perPage)
		res, esErr := scroll.Do(context.TODO())
		if esErr == io.EOF {
			logger.Errorf("查询资产数据为空 error:%v", esErr)
			// 发送命令到客户端
			err = sdk.SendCmdToClient(CmdSaveAsset, result)
			if err != nil {
				// todo 返回错误信息
				logger.Errorf("发送资产数据 SendCmdToClient: %v", err)
				return
			}
			break
		}
		if res != nil {
			if res.Hits.TotalHits.Value == 0 || len(res.Hits.Hits) == 0 {
				logger.Errorf("查询资产数据统计为空 error:%v", esErr)
				// 发送命令到客户端
				err = sdk.SendCmdToClient(CmdSaveAsset, result)
				if err != nil {
					// todo 返回错误信息
					logger.Errorf("发送资产数据 SendCmdToClient: %v", err)
					return
				}
				break
			}

			paList := es.ParseHitsValue[assets.Assets](res.Hits.Hits)
			if len(paList) > 0 {
				// 如果存在数据，则返回数据
				result.Data = append(result.Data, paList...)
				logger.Infof("------ 开始处理资产数据，放入到结构体中,Page: %d,perPage:%d,Count,%d ------", page, perPage, len(paList))
				// 发送命令到客户端
				err = sdk.SendCmdToClient(CmdSaveAsset, result)
				if err != nil {
					// todo 返回错误信息
					logger.Errorf("发送资产数据 SendCmdToClient: %v", err)
					return
				}
			}
		}
		time.Sleep(time.Second * 1)
		page++
	}
}

// HandleSaveAsset 函数用于处理保存资产信息的命令。
//
// 参数：
// nodeId int：节点ID，表示资产所属的节点。
// data []byte：包含资产信息的JSON数据。
//
// 功能：
// 1. 记录开始处理命令的日志信息。
// 2. 解析传入的JSON数据，将其反序列化为SaveAsset结构体实例。
// 3. 如果数据解析失败，则记录错误信息并返回。
// 4. 检查解析后的资产数据，如果总条数大于0且数据不为空，则进行资产保存操作。
// 5. 创建一个Elasticsearch批量索引器，并设置索引名称为CascadeAsset的索引名。
// 6. 遍历资产数据，为每个资产创建一个CascadeAsset实例，并设置相关字段。
// 7. 将每个CascadeAsset实例添加到批量索引请求中。
// 8. 执行批量索引请求，如果请求失败，则记录错误信息并返回。
// 9. 如果请求成功，记录成功保存的资产数量（包括新创建和更新的资产）。
func HandleSaveAsset(nodeId int, data []byte) {
	defer func() {
		if reErr := recover(); reErr != nil {
			logs.GetCrontabLogger().Infof("处理保存资产命令 > FAIL, 返回结果: %v", reErr)
			return
		}
	}()
	logs.GetLogger().Info("开始处理命令：save_asset")
	// 解析命令参数
	var params SaveAsset
	if err := json.Unmarshal(data, &params); err != nil {
		// todo 返回错误信息
		logger.Errorf("解析资产列表命令参数失败: %v", err)
		return
	}

	logger.Infof("------开始处理资产保存数据 Total: %d,Page:%d--------", params.Total, params.Page)
	cascadeSyncRecord, err := cascade_sync_record.NewCascadeSyncRecord().First(mysql.WithWhere("`id` = ?", cast.ToUint64(params.TaskId)))
	if err != nil {
		logs.GetLogger().Errorf("节点同步记录获取失败:%v", err)
	}
	// 保存资产
	if params.Total > 0 {
		if len(params.Data) > 0 {
			bulkIndexer := es.GetEsClient().Bulk().Index(cascade_model.NewCascadeAsset().IndexName())
			for _, asset := range params.Data {
				cascadeAsset := &cascade_model.CascadeAsset{
					CascadeId:      nodeId,
					RecordId:       cascadeSyncRecord.Id,
					SyncCreateTime: localtime.NewLocalTime(time.Now()),
					SyncUpdateTime: localtime.NewLocalTime(time.Now()),
					Assets:         *asset,
				}
				bulkIndexer.Add(elastic.NewBulkIndexRequest().Id(fmt.Sprintf("%s_%d", asset.Id, cascadeSyncRecord.Id)).Doc(cascadeAsset))
			}
			taskBulkIndexer := es.GetEsClient().Bulk().Index(cascade_model.NewCascadeTaskAsset().IndexName())
			for _, asset := range params.Data {
				cascadeTaskAsset := &cascade_model.CascadeTaskAsset{
					CascadeId:      nodeId,
					RecordId:       cascadeSyncRecord.Id,
					SyncCreateTime: localtime.NewLocalTime(time.Now()),
					SyncUpdateTime: localtime.NewLocalTime(time.Now()),
					Assets:         *asset,
				}
				taskBulkIndexer.Add(elastic.NewBulkIndexRequest().Id(fmt.Sprintf("%s_%d", asset.Id, cascadeSyncRecord.Id)).Doc(cascadeTaskAsset))
			}
			resp1, err1 := bulkIndexer.Do(context.Background())
			resp2, err2 := taskBulkIndexer.Do(context.Background())

			if err1 != nil || err2 != nil {
				err := cascade_sync_record.NewCascadeSyncRecord().UpdateByRaw(cascadeSyncRecord.Id, len(params.Data), false, params.Total)
				if err != nil {
					logs.GetLogger().Errorf("节点同步记录更新失败:%v", err)
				}
				// todo 返回错误信息
				logs.GetLogger().Errorf("保存资产失败:%v", err)
				return
			}
			logs.GetLogger().Info("保存资产成功，创建成功", resp1.Created(), ".更新成功", resp1.Updated())
			logs.GetLogger().Info("保存资产成功，创建成功", resp2.Created(), ".更新成功", resp2.Updated())
			err = cascade_sync_record.NewCascadeSyncRecord().UpdateByRaw(cascadeSyncRecord.Id, len(params.Data), true, params.Total)
			if err != nil {
				logs.GetLogger().Errorf("节点同步记录更新失败:%v", err)
			}
			cascadeSyncRecord, err = cascade_sync_record.NewCascadeSyncRecord().First(mysql.WithWhere("`id` = ?", cast.ToUint64(params.TaskId)))
			if err != nil || cascadeSyncRecord.FailCount+cascadeSyncRecord.SuccessCount == cascadeSyncRecord.Total {
				lockKey := redis_helper.GetCascadeAssetLockKey()
				distributedlock.Unlock(lockKey, "PullCascadeAsset")
				logs.GetLogger().Infof("节点同步完成")
			}
		}
	} else {
		logs.GetLogger().Info("资产数据为空，不进行同步")
		err := cascade_sync_record.NewCascadeSyncRecord().UpdateByRaw(cascadeSyncRecord.Id, 0, true, params.Total)
		if err != nil {
			logs.GetLogger().Errorf("节点同步记录更新失败:%v", err)
		}
		lockKey := redis_helper.GetCascadeAssetLockKey()
		distributedlock.Unlock(lockKey, "PullCascadeAsset")
		logs.GetLogger().Infof("节点同步完成")
	}
}

// HandleGetVulnList 处理获取漏洞列表命令
func HandleGetVulnList(nodeId int, data []byte) {
	logger := logs.GetLogger()
	logger.Info("开始处理命令：get_vuln_list")
	defer func() {
		if reErr := recover(); reErr != nil {
			logs.GetCrontabLogger().Infof("处理获取漏洞列表命令 > FAIL, 返回结果: %v", reErr)
			return
		}
	}()

	// 解析命令参数
	var params GetVulnListRequest
	if err := json.Unmarshal(data, &params); err != nil {
		// todo 返回错误信息
		logger.Errorf("获取漏洞列表命令参数解析失败:%v", err)

		return
	}

	// 查询资产列表数量
	total, err := es.GetEsClient().Count(poc.NewPoc().IndexName()).Do(context.Background())
	if err != nil {
		// todo 返回错误信息
		logger.Errorf("获取漏洞数据列表解析失败:%v", err)
		return
	}
	logger.Infof("统计漏洞数据量: %d", total)

	// 初始化返回数据
	page := 1
	perPage := 500
	client := es.GetEsClient()
	scroll := client.Scroll().Index(poc.NewPoc().IndexName()).
		Query(elastic.NewMatchAllQuery()).
		Sort("created_at", false).
		Size(perPage).
		Scroll("1m") // 滚动查询
	defer scroll.Clear(context.Background())
	for {
		logger.Infof("------ 开始处理漏洞数据,Page: %d,perPage:%d ------", page, perPage)
		result := &SavePoc{
			TaskId: params.TaskId,
			Total:  int(total),
			Data:   nil,
		}
		result.Page = page
		result.PerPage = perPage
		result.Data = make([]*poc.Poc, 0, perPage)
		res, esErr := scroll.Do(context.TODO())
		if esErr == io.EOF {
			logger.Errorf("查询POC为空 error:%v", esErr)
			// 发送命令到客户端
			err = sdk.SendCmdToClient(CmdSaveVuln, result)
			if err != nil {
				// todo 返回错误信息
				logger.Errorf("发送命令到客户端 error:%v", err)
				return
			}
			break
		}
		if res != nil {
			if res.Hits.TotalHits.Value == 0 || len(res.Hits.Hits) == 0 {
				logger.Errorf("查询POC 统计 为空 error:%v", esErr)
				// 发送命令到客户端
				err = sdk.SendCmdToClient(CmdSaveVuln, result)
				if err != nil {
					// todo 返回错误信息
					logger.Errorf("发送命令到客户端 error:%v", err)
					return
				}
				break
			}
			paList := es.ParseHitsValue[poc.Poc](res.Hits.Hits)
			if len(paList) > 0 {
				// 如果存在数据，则返回数据
				result.Data = append(result.Data, paList...)
				logger.Infof("------ 开始处理漏洞数据，放入到结构体中,Page: %d,perPage:%d,Count,%d ------", page, perPage, len(paList))
				// 发送命令到客户端
				err = sdk.SendCmdToClient(CmdSaveVuln, result)
				if err != nil {
					// todo 返回错误信息
					logger.Errorf("发送命令到客户端 error:%v", err)
					return
				}
			}
		}
		time.Sleep(time.Second * 1)
		page++
	}
}

func HandleSaveVuln(nodeId int, data []byte) {
	defer func() {
		if reErr := recover(); reErr != nil {
			logs.GetCrontabLogger().Infof("处理保存漏洞命令 > FAIL, 返回结果: %v", reErr)
			return
		}
	}()
	logs.GetLogger().Info("开始处理命令：save_vuln")

	// 解析命令参数
	var params SavePoc
	if err := json.Unmarshal(data, &params); err != nil {
		// todo 返回错误信息
		return
	}

	cascadeSyncRecord, err := cascade_sync_record.NewCascadeSyncRecord().First(mysql.WithWhere("`id` = ?", cast.ToUint64(params.TaskId)))
	if err != nil {
		logs.GetLogger().Errorf("节点同步记录获取失败:%v", err)
	}
	if cascadeSyncRecord == nil || cascadeSyncRecord.Id == 0 {
		logs.GetLogger().Errorf("节点同步记录不存在")
		return
	}
	logger.Infof("------开始处理漏洞保存数据 Total: %d,Page:%d--------", params.Total, params.Page)

	// 保存资产
	if params.Total > 0 {
		if len(params.Data) > 0 {
			bulkIndexer := es.GetEsClient().Bulk().Index(cascade_model.NewCascadePoc().IndexName())
			for _, poc := range params.Data {
				cascadePoc := &cascade_model.CascadePoc{
					CascadeId:      nodeId,
					RecordId:       cascadeSyncRecord.Id,
					SyncCreateTime: localtime.NewLocalTime(time.Now()),
					SyncUpdateTime: localtime.NewLocalTime(time.Now()),
					Poc:            *poc,
				}
				bulkIndexer.Add(elastic.NewBulkIndexRequest().Id(fmt.Sprintf("%s_%d", poc.Id, cascadeSyncRecord.Id)).Doc(cascadePoc))

			}
			taskBulkIndexer := es.GetEsClient().Bulk().Index(cascade_model.NewCascadeTaskPoc().IndexName())
			for _, poc := range params.Data {
				cascadeTaskPoc := &cascade_model.CascadeTaskPoc{
					CascadeId:      nodeId,
					RecordId:       cascadeSyncRecord.Id,
					SyncCreateTime: localtime.NewLocalTime(time.Now()),
					SyncUpdateTime: localtime.NewLocalTime(time.Now()),
					Poc:            *poc,
				}
				taskBulkIndexer.Add(elastic.NewBulkIndexRequest().Id(fmt.Sprintf("%s_%d", poc.Id, cascadeSyncRecord.Id)).Doc(cascadeTaskPoc))
			}
			resp1, err1 := bulkIndexer.Do(context.Background())
			resp2, err2 := taskBulkIndexer.Do(context.Background())
			if err1 != nil || err2 != nil {
				err = cascade_sync_record.NewCascadeSyncRecord().UpdateByRaw(cascadeSyncRecord.Id, len(params.Data), false, params.Total)
				if err != nil {
					logs.GetLogger().Errorf("节点同步记录更新失败:%v", err)
				}
				// todo 返回错误信息
				logs.GetLogger().Errorf("保存资产失败:%v", err)
				return
			}
			logs.GetLogger().Info("保存漏洞成功，创建成功", resp1.Created(), ".更新成功", resp1.Updated())
			logs.GetLogger().Info("保存漏洞记录成功，创建成功", resp2.Created(), ".更新成功", resp2.Updated())
			err = cascade_sync_record.NewCascadeSyncRecord().UpdateByRaw(cascadeSyncRecord.Id, len(params.Data), true, params.Total)
			if err != nil {
				logs.GetLogger().Errorf("节点同步记录更新失败:%v", err)
			}
		}
	} else {
		err = cascade_sync_record.NewCascadeSyncRecord().UpdateByRaw(cascadeSyncRecord.Id, 0, true, params.Total)
		if err != nil {
			logs.GetLogger().Errorf("节点同步记录更新失败:%v", err)
		}
	}
	cascadeSyncRecord, err = cascade_sync_record.NewCascadeSyncRecord().First(mysql.WithWhere("`id` = ?", cast.ToUint64(params.TaskId)))
	if err != nil || cascadeSyncRecord.FailCount+cascadeSyncRecord.SuccessCount == cascadeSyncRecord.Total {
		lockKey := redis_helper.GetCascadeVulnLockKey()
		distributedlock.Unlock(lockKey, "PullCascadeVuln")
		logs.GetLogger().Info("节点同步记录更新完成")
	}
}

// HandleNewUpgrade 处理创建更新记录命令
func HandleNewUpgrade(nodeID int, data []byte) {
	logs.GetLogger().Info("开始处理命令：new_upgrade")

	var params NewUpgradeRequest
	if err := json.Unmarshal(data, &params); err != nil {
		// todo 返回错误信息
		logger.Errorf("创建更新记录命令参数解析失败:%v", err)
		return
	}

	// 查询是否已有记录
	cascadeUpgradeRecord, err := cascade_upgrade.NewCascadeUpgradeRecordModel().First(
		mysql.WithWhere("version = ?", params.Version),
		mysql.WithWhere("package_name = ?", params.PackageName),
	)
	if err != nil && (err.Error() != "record not found" || cascadeUpgradeRecord.Id > 0) {
		return
	}

	storagePath := cfg.LoadCommon().StoragePath
	dst := filepath.Join(storagePath, "/app/public/upgrade/", params.PackageName)

	// 创建记录
	upgradeRecord := &cascade_upgrade.CascadeUpgradeRecord{
		PackageName:    params.PackageName,
		PackageId:      params.PackageId,
		PackageType:    params.PackageType,
		Version:        params.Version,
		DownloadStatus: cascade_upgrade.WaitDownload,
		DownloadTime:   nil,
		VerifyStatus:   cascade_upgrade.NotVerify,
		UpgradeStatus:  cascade_upgrade.WaitUpgrade,
		UpgradeTime:    nil,
		PackageHash:    params.PackageHash,
		PackagePath:    dst,
		DownloadUrl:    params.DownloadUrl,
		TaskType:       params.TaskType,
		NodeId:         params.NodeId,
	}

	err = cascade_upgrade.NewCascadeUpgradeRecordModel().Create(upgradeRecord)
	if err != nil {
		// todo 返回错误信息
		return
	}

	// 是否需要保存升级详情记录
	needSaveDetail := false
	nodes := make([]*data_source.Node, 0)
	if upgradeRecord.TaskType == cascade_upgrade.TaskTypeD01 {
		// 获取对应数据源节点，保存升级详情记录
		needSaveDetail = true
		nodes, _, err = data_source.NewNodeModel().Items(0, 0, mysql.WithWhere("source = ?", "d01"))
		if err != nil {
			logs.GetLogger().Errorf("获取数据源节点失败:%v", err)
			return
		}
	} else if upgradeRecord.TaskType == cascade_upgrade.TaskTypeXRay {
		// 获取对应数据源节点，保存升级详情记录
		needSaveDetail = true
		nodes, _, err = data_source.NewNodeModel().Items(0, 0, mysql.WithWhere("source = ?", "x_ray"))
		if err != nil {
			logs.GetLogger().Errorf("获取数据源节点失败:%v", err)
			return
		}
	}
	if needSaveDetail {
		// 保存升级详情记录
		if len(nodes) > 0 {
			for _, node := range nodes {
				cascade_upgrade.NewCascadeUpgradeRecordDetailsModel().Create(&cascade_upgrade.CascadeUpgradeRecordDetails{
					RecordId:       upgradeRecord.Id,
					ThirdPartyId:   node.Id,
					ThirdPartyType: node.Source,
					UploadStatus:   1,
					CheckStatus:    1,
					UpgradeStatus:  1,
				})
			}
		} else {
			logs.GetLogger().Errorf("没有对应数据源节点")
		}
	}
	logs.GetLogger().Info("更新记录创建成功")
}

// HandleGenerateDownloadToken 处理生成下载token命令
func HandleGenerateDownloadToken(nodeId int, data []byte) {
	logs.GetLogger().Info("开始处理命令：download_token")
	var params GenerateDownloadTokenRequest
	if err := json.Unmarshal(data, &params); err != nil {
		return
	}
	params.ExpirationTime = localtime.NewLocalTime(time.Now().Add(15 * time.Minute))
	token, err := utils.GenerateRandomToken(16)
	if err != nil {
		logs.GetLogger().Errorf("Token 生成失败:%v", err)
		return
	}
	client := redis.GetRedisClient()
	if client == nil {
		logs.GetLogger().Errorf("Redis 连接失败:%v", err)
		return
	}
	ctx := context.Background()
	redisKey := redis_helper.CascadeUpgradeTokenKey(token)
	redisValue, _ := json.Marshal(params)
	redis.GetRedisClient().Set(ctx, redisKey, string(redisValue), time.Minute*15)
	// 告诉下级获取token
	TriggerSendDownloadToken(params.NodeId, &GetDownloadTokenRequest{
		RecordId: params.RecordId,
		Token:    token,
	})
}

// HandleGetDownloadToken 获取Token 下载文件
func HandleGetDownloadToken(nodeId int, data []byte) {
	var params GetDownloadTokenRequest
	if err := json.Unmarshal(data, &params); err != nil {
		return
	}
	token := params.Token

	// 查询升级包记录，获取下载地址
	record, err := cascade_upgrade.NewCascadeUpgradeRecordModel().First(mysql.WithWhere("id = ?", params.RecordId))
	if err != nil {
		msg := fmt.Sprintf("升级记录查询失败:%v", err)
		updateRecordFail(fmt.Sprintf("%d", record.Id), msg)
		return
	}

	// 下载文件
	resp, err := downloadFile(token, record.DownloadUrl)
	if err != nil {
		msg := fmt.Sprintf("下载文件失败:%v", err)
		updateRecordFail(fmt.Sprintf("%d", record.Id), msg)
		return
	}
	defer resp.Close()

	// 保存文件
	dst, err := saveFile(resp, record.PackageName)
	if err != nil {
		msg := fmt.Sprintf("保存文件失败:%v", err)
		updateRecordFail(fmt.Sprintf("%d", record.Id), msg)
		return
	}

	// 更新记录
	record.DownloadStatus = cascade_upgrade.Downloaded
	record.DownloadTime = localtime.NewLocalTime(time.Now())
	record.PackagePath = dst
	record.DownloadErr = ""
	err = cascade_upgrade.NewCascadeUpgradeRecordModel().Update(record)
	if err != nil {
		msg := fmt.Sprintf("更新记录更新失败:%v", err)
		updateRecordFail(fmt.Sprintf("%d", record.Id), msg)
		return
	}

	logs.GetLogger().Infof("文件已成功下载到: %s\n", dst)
}

// 下载文件，注意：调用方需要负责关闭返回的ReadCloser
func downloadFile(token, url string) (io.ReadCloser, error) {
	// 生成下载参数
	requestBody := map[string]string{
		"token": token,
	}
	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		logs.GetLogger().Errorf("转换JSON失败:%v", err)
		return nil, err
	}
	// 发送下载请求
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		logs.GetLogger().Errorf("创建请求失败:%v", err)
		return nil, err
	}
	req.Header.Set("Content-Type", "application/json")

	// 创建一个自定义的Transport，并禁用证书验证
	tr := &http.Transport{
		TLSClientConfig: &tls.Config{
			InsecureSkipVerify: true,
		},
	}
	client := &http.Client{Transport: tr}
	resp, err := client.Do(req)
	if err != nil {
		logs.GetLogger().Errorf("发送请求失败:%v", err)
		return nil, err
	}
	if resp.StatusCode != http.StatusOK {
		logs.GetLogger().Errorf("请求失败，状态码: %d", resp.StatusCode)
		return nil, fmt.Errorf("请求失败，状态码: %d", resp.StatusCode)
	}
	// 返回读取器，调用方负责关闭
	return resp.Body, nil
}

// 保存文件
func saveFile(resp io.ReadCloser, packageName string) (string, error) {
	// 检查路径
	storagePath := filepath.Join(cfg.LoadCommon().StoragePath, "/app/public/upgrade/")
	if _, err := os.Stat(storagePath); os.IsNotExist(err) {
		err = os.MkdirAll(storagePath, os.ModePerm)
		if err != nil {
			logs.GetLogger().Errorf("创建目录失败:%v\n", err)
			return "", err
		}
	} else if err != nil {
		logs.GetLogger().Errorf("检查目录时发生错误:%v", err)
	}
	// 完成路径
	dst := filepath.Join(storagePath, packageName)
	file, err := os.Create(dst)
	if err != nil {
		logs.GetLogger().Errorf("创建文件失败:%v", err)
		return "", err
	}
	defer file.Close()
	// 保存文件
	_, err = io.Copy(file, resp)
	if err != nil {
		logs.GetLogger().Errorf("保存文件失败:%v", err)
		return "", err
	}
	return dst, nil
}

// 更新记录
func updateRecordFail(recordId string, err string) error {
	return cascade_upgrade.NewCascadeUpgradeRecordModel().UpdateColumns(recordId, map[string]interface{}{
		"download_status": cascade_upgrade.DownloadFail,
		"download_err":    err,
	})
}
