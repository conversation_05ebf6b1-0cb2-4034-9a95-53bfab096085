package task

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"fobrain/models/elastic/device"
	"fobrain/models/elastic/staff"
	"fobrain/pkg/utils/handle_es_bulk_error"
	"sync"
	"time"

	node2 "fobrain/fobrain/app/services/node"

	"github.com/olivere/elastic/v7"

	"fobrain/fobrain/common/localtime"
	"fobrain/fobrain/logs"
	"fobrain/initialize/es"
	"fobrain/initialize/mysql"
	assetses "fobrain/models/elastic/assets"
	"fobrain/models/elastic/poc"
	"fobrain/models/elastic/source/aliyun_cloud"
	"fobrain/models/elastic/source/bk_cmdb"
	"fobrain/models/elastic/source/changting_waf"
	"fobrain/models/elastic/source/d01"
	"fobrain/models/elastic/source/data_import"
	"fobrain/models/elastic/source/dingtalk"
	"fobrain/models/elastic/source/file_import"
	"fobrain/models/elastic/source/foeye"
	"fobrain/models/elastic/source/foradar"
	"fobrain/models/elastic/source/mach_lake"
	qizhi_uaudithost2 "fobrain/models/elastic/source/qizhi_uaudithost"
	"fobrain/models/elastic/source/qt_cloud"
	"fobrain/models/elastic/source/weibu"
	xray "fobrain/models/elastic/source/x-ray"
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/data_sync_child_task"
	"fobrain/models/mysql/data_sync_task"
	"fobrain/pkg/utils"
)

// LogLevel 定义日志级别
type LogLevel string

const (
	LogLevelInfo  LogLevel = "INFO"
	LogLevelWarn  LogLevel = "WARN"
	LogLevelError LogLevel = "ERROR"
)

type SyncTask struct {
	Node            *data_source.Node
	Client          node2.Client
	Task            *data_sync_task.DataSyncTask
	ChildTasks      map[int]*data_sync_child_task.DataSyncChildTask
	AssetChildTasks *data_sync_child_task.DataSyncChildTask
	VulChildTasks   *data_sync_child_task.DataSyncChildTask
	StaffTasks      *data_sync_child_task.DataSyncChildTask
	mu              sync.Mutex
}

// NewSyncTask 创建同步任务对象
func NewSyncTask() *SyncTask {
	return &SyncTask{
		Node:   &data_source.Node{},
		Client: node2.NewDefaultClient(),
		Task:   &data_sync_task.DataSyncTask{},
		ChildTasks: map[int]*data_sync_child_task.DataSyncChildTask{
			data_sync_task.SyncAsset:  {}, //资产类型
			data_sync_task.SyncThreat: {}, //漏洞类型
			data_sync_task.SyncPeople: {}, //人员类型
		},
	}
}

// initTask 初始化主任务及子任务
func (st *SyncTask) initTask(node *data_source.Node, dataSource data_source.Source, syncTypes []int, source int) error {

	st.Task.SourceId = node.SourceId
	st.Task.NodeId = node.Id
	st.Task.Source = source
	st.Task.StartAt = localtime.Time(time.Now())

	//创建任务信息
	err := data_sync_task.NewDataSyncTaskModel().Create(st.Task)
	if err != nil {
		return err
	}

	//创建子任务
	// 数据源存在数据资产数据且在本次同步的类型当中创建任务
	if dataSource.HasAssetData && utils.InArray(data_sync_task.SyncAsset, syncTypes) {
		st.ChildTasks[data_sync_task.SyncAsset].SourceId = node.SourceId
		st.ChildTasks[data_sync_task.SyncAsset].NodeId = node.Id
		st.ChildTasks[data_sync_task.SyncAsset].TaskId = st.Task.Id
		st.ChildTasks[data_sync_task.SyncAsset].Type = data_sync_task.SyncAsset
		err = data_sync_child_task.NewDataSyncChildTaskModel().Create(st.ChildTasks[data_sync_task.SyncAsset])
		if err != nil {
			return err
		}
	}

	if dataSource.HasVulData && utils.InArray(data_sync_task.SyncThreat, syncTypes) {
		st.ChildTasks[data_sync_task.SyncThreat].SourceId = node.SourceId
		st.ChildTasks[data_sync_task.SyncThreat].NodeId = node.Id
		st.ChildTasks[data_sync_task.SyncThreat].TaskId = st.Task.Id
		st.ChildTasks[data_sync_task.SyncThreat].Type = data_sync_task.SyncThreat
		err = data_sync_child_task.NewDataSyncChildTaskModel().Create(st.ChildTasks[data_sync_task.SyncThreat])
		if err != nil {
			return err
		}
	}

	if dataSource.HasPersonnelData && utils.InArray(data_sync_task.SyncPeople, syncTypes) {
		st.ChildTasks[data_sync_task.SyncPeople].SourceId = node.SourceId
		st.ChildTasks[data_sync_task.SyncPeople].NodeId = node.Id
		st.ChildTasks[data_sync_task.SyncPeople].TaskId = st.Task.Id
		st.ChildTasks[data_sync_task.SyncPeople].Type = data_sync_task.SyncPeople
		err = data_sync_child_task.NewDataSyncChildTaskModel().Create(st.ChildTasks[data_sync_task.SyncPeople])
		if err != nil {
			return err
		}
	}

	return nil
}

// Dispatch 下发任务 source任务来源 1定时 2手动
func (st *SyncTask) Dispatch(nodeId uint64, syncType []int, source int, nodeInterface node2.SourceNode) {
	st.WriteLog(fmt.Sprintf("init node task nodeId:%d,syncType:%d,source:%d", nodeId, syncType, source))

	//获取数据源信息
	dataSource, err := data_source.NewSourceModel().First(mysql.WithWhere("id", st.Node.SourceId))
	if err != nil {
		st.WriteLog(fmt.Sprintf("sync data err : %+v", err))
	}

	//初始化任务
	err = st.initTask(st.Node, dataSource, syncType, source)
	if err != nil {
		st.WriteLog(fmt.Sprintf("sync data err : %+v", err))
	}

	//更新主任务状态为进行中
	err = data_sync_task.NewDataSyncTaskModel().UpdateStatusById(st.Task.Id, data_sync_task.StatusDoing)
	if err != nil {
		st.WriteLog(fmt.Sprintf("sync data err : %+v", err))
	}

	st.WriteLog(fmt.Sprintf("dispath sourceId:%d sync task", st.Task.SourceId))

	//存在资产任务
	if st.ChildTasks[data_sync_task.SyncAsset] != nil && st.ChildTasks[data_sync_task.SyncAsset].Id > 0 {
		//调用节点对应源下发源同步任务
		err = st.ProcessTask(nodeInterface)
		if err != nil {
			st.WriteLog(fmt.Sprintf("sync data ProcessTask err : %+v", err))
		}
	}
	//存在漏洞任务
	if st.ChildTasks[data_sync_task.SyncThreat] != nil && st.ChildTasks[data_sync_task.SyncThreat].Id > 0 {
		//调用节点对应源下发源同步任务
		err = st.ProcessVulTask(nodeInterface)
		if err != nil {
			st.WriteLog(fmt.Sprintf("sync data ProcessVulTask err : %+v", err))
		}
	}

	//存在人员任务
	if st.ChildTasks[data_sync_task.SyncPeople] != nil && st.ChildTasks[data_sync_task.SyncPeople].Id > 0 {
		//调用节点对应源下发源同步任务
		err = st.ProcessStaffTask(nodeInterface)
		if err != nil {
			st.WriteLog(fmt.Sprintf("sync data ProcessStaffTask err : %+v", err))
		}
	}

	st.WriteLog(fmt.Sprintf("sync data success ; node task nodeId:%d,syncType:%d,source:%d", nodeId, syncType, source))

}

func (st *SyncTask) ProcessTask(cli node2.SourceNode) (err error) { // 使用命名返回值
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("recover node task err: %v", r) // 修改返回值
		}
	}()

	// 检查 cli 是否为空
	if cli == nil {
		return errors.New("can not find ProcessTask source node")
	}

	// 设置任务状态为进行中
	err = st.SetStatus(data_sync_task.SyncAsset, data_sync_child_task.StatusDoing, "")
	if err != nil {
		st.WriteLog(fmt.Sprintf("ProcessTask SetStatus err: %+v", err.Error()))
		return err
	}

	// 执行获取资产的操作
	err = cli.GetAssets(context.Background(), st, nil)
	if err != nil {
		// 设置任务状态为失败
		setStatusErr := st.SetStatus(data_sync_task.SyncAsset, data_sync_child_task.StatusFail, err.Error())
		if setStatusErr != nil {
			st.WriteLog(fmt.Sprintf("ProcessTask SetStatus err: %+v", setStatusErr.Error()))
		}
		return err
	}

	// 设置任务状态为成功
	err = st.SetStatus(data_sync_task.SyncAsset, data_sync_child_task.StatusSuccess, "")
	if err != nil {
		st.WriteLog(fmt.Sprintf("SyncHuaweiHkCloud SetStatus err: %+v", err.Error()))
		return err
	}

	return nil
}

func (st *SyncTask) GetTaskId(syncType int) (uint64, uint64) {
	return st.Task.Id, st.ChildTasks[syncType].Id
}

func (st *SyncTask) ProcessStaffTask(cli node2.SourceNode) (err error) { // 使用命名返回值
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("recover node task err: %v", r) // 修改返回值
		}
	}()

	// 检查 cli 是否为空
	if cli == nil {
		return errors.New("can not find ProcessStaffTask source node")
	}

	// 设置任务状态为进行中
	err = st.SetStatus(data_sync_task.SyncPeople, data_sync_child_task.StatusDoing, "")
	if err != nil {
		st.WriteLog(fmt.Sprintf("ProcessStaffTask SetStatus err: %+v", err.Error()))
		return err
	}

	// 执行获取资产的操作
	err = cli.GetPerson(context.Background(), st, nil)
	if err != nil {
		// 设置任务状态为失败
		setStatusErr := st.SetStatus(data_sync_task.SyncPeople, data_sync_child_task.StatusFail, err.Error())
		if setStatusErr != nil {
			st.WriteLog(fmt.Sprintf("ProcessStaffTask SetStatus err: %+v", setStatusErr.Error()))
		}
		return err
	}

	// 设置任务状态为成功
	err = st.SetStatus(data_sync_task.SyncPeople, data_sync_child_task.StatusSuccess, "")
	if err != nil {
		st.WriteLog(fmt.Sprintf("ProcessStaffTask SetStatus err: %+v", err.Error()))
		return err
	}

	return nil
}

func (st *SyncTask) ProcessVulTask(cli node2.SourceNode) (err error) { // 使用命名返回值
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("recover node task err: %v", r) // 修改返回值
		}
	}()

	// 检查 cli 是否为空
	if cli == nil {
		return errors.New("can not find ProcessVulTask source node")
	}

	// 设置任务状态为进行中
	err = st.SetStatus(data_sync_task.SyncThreat, data_sync_child_task.StatusDoing, "")
	if err != nil {
		st.WriteLog(fmt.Sprintf("ProcessVulTask SetStatus err: %+v", err.Error()))
		return err
	}

	// 执行获取资产的操作
	err = cli.GetVul(context.Background(), st, nil)
	if err != nil {
		// 设置任务状态为失败
		setStatusErr := st.SetStatus(data_sync_task.SyncThreat, data_sync_child_task.StatusFail, err.Error())
		if setStatusErr != nil {
			st.WriteLog(fmt.Sprintf("ProcessVulTask SetStatus err: %+v", setStatusErr.Error()))
		}
		return err
	}

	// 设置任务状态为成功
	err = st.SetStatus(data_sync_task.SyncThreat, data_sync_child_task.StatusSuccess, "")
	if err != nil {
		st.WriteLog(fmt.Sprintf("ProcessVulTask SetStatus err: %+v", err.Error()))
		return err
	}

	return nil
}

func (st *SyncTask) GetParamByKey(key string) any {
	return st.Client.GetConfig(key)
}

// BatchUpdateSourceAsset 批量更新资产数据-仅数据源资产入库
func (st *SyncTask) BatchUpdateSourceAsset(sourceData []map[string]interface{}) error {
	if len(sourceData) == 0 {
		return nil
	}
	st.mu.Lock()
	defer st.mu.Unlock()
	//获取对应源名称
	sourceAssetTaskName := st.getSourceIndexModel(st.Task.SourceId, data_sync_task.SyncAsset)

	if sourceAssetTaskName == "" {
		return errors.New("BatchUpdateAsset 数据源索引不存在")
	}

	assetTaskBulkRequest := es.GetEsClient().Bulk()
	for _, sourceItem := range sourceData {

		sourceItemId, _ := sourceItem["id"].(string)
		//写入源数据任务库
		sourceItem["id"] = fmt.Sprintf("%d_%s", st.Task.Id, sourceItem["id"]) //过程表ID同数据源任务ID一样

		//追加数据
		sourceItem = st.appendAttribute(data_sync_task.SyncAsset, sourceItem)

		//写入资产任务库
		assetTaskReq := elastic.NewBulkIndexRequest().Index(sourceAssetTaskName).Id(sourceItemId).Doc(sourceItem)
		assetTaskBulkRequest = assetTaskBulkRequest.Add(assetTaskReq)
	}

	assetTaskBulkReq, err := assetTaskBulkRequest.Do(context.Background())
	if err != nil || assetTaskBulkReq.Errors {
		errString := handle_es_bulk_error.HandleBulkResp(sourceAssetTaskName, err, assetTaskBulkReq)
		st.WriteLog("BatchUpdateAsset " + errString)
	}

	if err != nil {
		return err
	}
	return nil
}

func (st *SyncTask) BatchUpdateProcessDevice(processDeviceData []map[string]interface{}) error {
	if len(processDeviceData) == 0 {
		return nil
	}
	processDevice := device.NewProcessDeviceModel()
	assetProcessDeviceBulkRequest := es.GetEsClient().Bulk()
	for i := 0; i < len(processDeviceData); i++ {
		d := processDeviceData[i]
		d["source"] = st.Node.SourceId
		d["node"] = st.Node.Id
		d["area"] = st.Node.AreaId
		d["task_id"] = st.Task.Id
		d["child_task_id"] = st.ChildTasks[data_sync_task.SyncAsset].Id
		processDeviceReq := elastic.NewBulkIndexRequest().Index(processDevice.IndexName()).Id(d["id"].(string)).Doc(d)
		assetProcessDeviceBulkRequest = assetProcessDeviceBulkRequest.Add(processDeviceReq)
	}

	processDeviceResp, err := assetProcessDeviceBulkRequest.Refresh("true").Do(context.Background())
	if err != nil || processDeviceResp.Errors {
		errString := handle_es_bulk_error.HandleBulkResp(processDevice.IndexName(), err, processDeviceResp)
		st.WriteLog("BatchUpdateDevice " + errString)
	}
	return err
}

// BatchUpdateAsset 批量更新资产数据
func (st *SyncTask) BatchUpdateAsset(sourceData, processData, processDeviceData []map[string]interface{}) error {
	st.mu.Lock()
	defer st.mu.Unlock()
	//获取对应源名称
	sourceAssetTaskName := st.getSourceIndexModel(st.Task.SourceId, data_sync_task.SyncAsset)

	if sourceAssetTaskName == "" {
		return errors.New("BatchUpdateAsset 数据源索引不存在")
	}

	assetTaskBulkRequest := es.GetEsClient().Bulk()
	assetProcessBulkRequest := es.GetEsClient().Bulk()
	processKeyExists := map[string]bool{}
	for i, sourceItem := range sourceData {
		processItem := processData[i]
		//写入源数据任务库
		sourceItem["id"] = processItem["id"] //过程表ID同数据源任务ID一样
		//检测IP是否正确
		ip, ok := processItem["ip"].(string)
		if !ok || ip == "" || !utils.IsIPv4(ip) {
			js, _ := json.Marshal(processItem)
			st.WriteLog(fmt.Sprintf("node %d data format err, data: %s", st.Node.Id, string(js)))
			continue
		}
		//TODO 记录重复数据 后续确定处理方案
		if _, exist := processKeyExists[processItem["id"].(string)]; exist {
			js, _ := json.Marshal(processItem)
			st.WriteLog(fmt.Sprintf("node %d data repeat, data: %s", st.Node.Id, string(js)))
			continue
		}

		processKeyExists[processItem["id"].(string)] = true

		//追加数据
		sourceItem = st.appendAttribute(data_sync_task.SyncAsset, sourceItem)
		processItem = st.appendAttribute(data_sync_task.SyncAsset, processItem)
		if st.Client.GetConfig("person_field") != "" && st.Client.GetConfig("person_field") != nil {
			processItem["person_field"] = st.Client.GetConfig("person_field").(string)
		}
		//写入资产任务库
		assetTaskReq := elastic.NewBulkIndexRequest().Index(sourceAssetTaskName).Id(sourceItem["id"].(string)).Doc(sourceItem)
		assetTaskBulkRequest = assetTaskBulkRequest.Add(assetTaskReq)

		//写入过程库
		assetProcess := assetses.NewProcessAssetsModel()
		assetProcessReq := elastic.NewBulkIndexRequest().Index(assetProcess.IndexName()).Id(processItem["id"].(string)).Doc(processItem)
		assetProcessBulkRequest = assetProcessBulkRequest.Add(assetProcessReq)
	}
	if len(processData) > 0 {
		assetTaskBulkReq, err := assetTaskBulkRequest.Do(context.Background())
		if err != nil || assetTaskBulkReq.Errors {
			errString := handle_es_bulk_error.HandleBulkResp(sourceAssetTaskName, err, assetTaskBulkReq)
			st.WriteLog("BatchUpdateAsset " + errString)
		}

		assetProcessBulkReq, err := assetProcessBulkRequest.Refresh("true").Do(context.Background())
		if err != nil || assetProcessBulkReq.Errors {
			for i, item := range assetProcessBulkReq.Items {
				for op, res := range item {
					if res.Error != nil {
						// 如果有错误，打印出错误信息
						err = fmt.Errorf("BatchUpdateAsset assetProcessBulkReq Item %d, operation %s: error %s: %s\n", i, op, res.Error.Type, res.Error.Reason)
						//记录日志
						st.WriteLog(err.Error())
					}
				}
			}
			errString := handle_es_bulk_error.HandleBulkResp(assetses.NewProcessAssetsModel().IndexName(), err, assetProcessBulkReq)
			st.WriteLog("BatchUpdateAsset " + errString)
		}
		if err != nil {
			return err
		}
	}

	if len(processDeviceData) > 0 {
		return st.BatchUpdateProcessDevice(processDeviceData)
	}
	return nil
}

// BatchUpdateVul 批量更新漏洞数据
func (st *SyncTask) BatchUpdateVul(sourceData, processData []map[string]interface{}) error {
	st.mu.Lock()
	defer st.mu.Unlock()

	//获取对应源名称
	sourceVulTaskName := st.getSourceIndexModel(st.Task.SourceId, data_sync_task.SyncThreat)
	if sourceVulTaskName == "" {
		return errors.New("BatchUpdateVul 数据源索引不存在")
	}

	vulTaskBulkRequest := es.GetEsClient().Bulk()
	vulProcessBulkRequest := es.GetEsClient().Bulk()

	for i, sourceItem := range sourceData {
		//写入源数据任务库
		sourceItem["id"] = processData[i]["id"] //过程表ID同数据源任务ID一样
		//写入资产任务库

		//追加数据
		sourceItem = st.appendAttribute(data_sync_task.SyncThreat, sourceItem)
		processData[i] = st.appendAttribute(data_sync_task.SyncThreat, processData[i])

		vulTaskReq := elastic.NewBulkIndexRequest().Index(sourceVulTaskName).Id(sourceItem["id"].(string)).Doc(sourceItem)
		vulTaskBulkRequest = vulTaskBulkRequest.Add(vulTaskReq)

		//写入过程库
		vulProcess := poc.NewProcessPocModel()
		vulProcessReq := elastic.NewBulkIndexRequest().Index(vulProcess.IndexName()).Id(processData[i]["id"].(string)).Doc(processData[i])
		vulProcessBulkRequest = vulProcessBulkRequest.Add(vulProcessReq)
	}
	if len(sourceData) > 0 {
		vulTaskBulkReq, err := vulTaskBulkRequest.Do(context.Background())
		if err != nil || vulTaskBulkReq.Errors {
			errString := handle_es_bulk_error.HandleBulkResp(sourceVulTaskName, err, vulTaskBulkReq)
			st.WriteLog("BatchUpdateVul " + errString)
		}

		vulProcessBulkReq, err := vulProcessBulkRequest.Refresh("true").Do(context.Background())
		if err != nil || vulProcessBulkReq.Errors {
			errString := handle_es_bulk_error.HandleBulkResp(poc.NewProcessPocModel().IndexName(), err, vulProcessBulkReq)
			st.WriteLog("BatchUpdateVul " + errString)
		}

		if err != nil {
			return err
		}
	}

	return nil
}

// BatchUpdateStaff 批量更新人员数据
func (st *SyncTask) BatchUpdateStaff(sourceData, processData []map[string]interface{}) error {
	st.mu.Lock()
	defer st.mu.Unlock()

	//获取对应源名称
	sourceStaffTaskName := st.getSourceIndexModel(st.Task.SourceId, data_sync_task.SyncPeople)
	if sourceStaffTaskName == "" {
		return errors.New("BatchUpdateStaff 数据源索引不存在")
	}

	staffTaskBulkRequest := es.GetEsClient().Bulk()
	staffProcessBulkRequest := es.GetEsClient().Bulk()

	for i, sourceItem := range sourceData {
		//写入源数据任务库
		sourceItem["id"] = processData[i]["id"] //过程表ID同数据源任务ID一样
		//写入资产任务库
		//追加数据
		sourceItem = st.appendAttribute(data_sync_task.SyncPeople, sourceItem)
		processData[i] = st.appendAttribute(data_sync_task.SyncPeople, processData[i])
		staffTaskReq := elastic.NewBulkIndexRequest().Index(sourceStaffTaskName).Id(sourceItem["id"].(string)).Doc(sourceItem)
		staffTaskBulkRequest = staffTaskBulkRequest.Add(staffTaskReq)

		//写入过程库
		staffProcess := staff.NewProcessStaffModel()
		staffProcessReq := elastic.NewBulkIndexRequest().Index(staffProcess.IndexName()).Id(processData[i]["id"].(string)).Doc(processData[i])
		staffProcessBulkRequest = staffProcessBulkRequest.Add(staffProcessReq)
	}
	if len(sourceData) > 0 {
		staffTaskBulkReq, err := staffTaskBulkRequest.Do(context.Background())
		if err != nil || staffTaskBulkReq.Errors {
			errString := handle_es_bulk_error.HandleBulkResp(sourceStaffTaskName, err, staffTaskBulkReq)
			st.WriteLog("BatchUpdateStaff " + errString)
		}

		staffProcessBulkReq, err := staffProcessBulkRequest.Refresh("true").Do(context.Background())
		if err != nil || staffProcessBulkReq.Errors {
			errString := handle_es_bulk_error.HandleBulkResp(staff.NewProcessStaffModel().IndexName(), err, staffProcessBulkReq)
			st.WriteLog("BatchUpdateStaff " + errString)
		}

		if err != nil {
			return err
		}

	}

	return nil
}

// UpdateSyncDataTotal 更新子任务总数
func (st *SyncTask) UpdateSyncDataTotal(syncType int, total int) error {
	//记录日志
	st.WriteLog(fmt.Sprintf("UpdateSyncDataTotal total:%d ", total))

	err := data_sync_child_task.NewDataSyncChildTaskModel().UpdateSyncDataTotal(st.ChildTasks[syncType].Id, total)
	if err != nil {
		return err
	}
	return nil
}

// UpdateSyncDataSuccessTotal 更新子任务成功总数
func (st *SyncTask) UpdateSyncDataSuccessTotal(syncType int, num int) error {
	//记录日志
	st.WriteLog(fmt.Sprintf("UpdateSyncDataSuccessTotal num:%d ", num))

	err := data_sync_child_task.NewDataSyncChildTaskModel().UpdateSyncDataSuccessTotal(st.ChildTasks[syncType].Id, num)
	if err != nil {
		return err
	}
	return nil
}

// SetStatus 更新子任务和主任务的状态（用于更新任务为进行中，或者已完成任务的更新）
// @param syncType 同步类型 data_sync_task.SyncAsset//资产类型 data_sync_task.SyncThreat//漏洞类型 data_sync_task.SyncPeople//人员类型
// @param status 任务状态 data_sync_child_task.StatusDoing data_sync_child_task.StatusSuccess data_sync_child_task.StatusFail
// @param msg 任务失败错误消息
func (st *SyncTask) SetStatus(syncType int, status int, msg string) error {
	//st.mu.Lock()
	//defer st.mu.Unlock()

	//记录日志
	st.WriteLog(fmt.Sprintf("SetStatus status:%d msg:%s syncType:%d", status, msg, syncType))

	if status != data_sync_child_task.StatusFail {
		err := data_sync_child_task.NewDataSyncChildTaskModel().UpdateStatusById(st.ChildTasks[syncType].Id, status)
		if err != nil {
			return err
		}
		return nil
	}

	//更新主任务状态为失败
	err := data_sync_task.NewDataSyncTaskModel().UpdateFailById(st.Task.Id, msg)
	if err != nil {
		return err
	}

	//更新子任务状态为失败
	childTaskErr := data_sync_child_task.NewDataSyncChildTaskModel().UpdateFailById(st.ChildTasks[syncType].Id, msg)
	if childTaskErr != nil {
		return err
	}
	return nil
}

// SetProcess 更新任务进度
func (st *SyncTask) SetProcess(syncType int, process int) error {
	st.mu.Lock()
	defer st.mu.Unlock()

	//todo 任务暂时不支持设置进度
	return nil
}

// WriteLog 写入带有日志级别的日志
func (st *SyncTask) WriteLog(msg string) {
	//st.mu.Lock()
	//defer st.mu.Unlock()

	logEntry := fmt.Sprintf("SyncTask WriteLog msg:%s params:%+v", msg, st)
	logs.GetSyncLogger().Infof(logEntry)
	//switch level {
	//case LogLevelInfo:
	//	logs.GetSyncLogger().Infof(logEntry)
	//case LogLevelWarn:
	//	logs.GetSyncLogger().Warnf(logEntry)
	//case LogLevelError:
	//	logs.GetSyncLogger().Errorf(logEntry)
	//}
}

// GetUUID 根据 id 获取 sourceId、processId（同任务ID为同一ID）
// 资产id为IP
// 漏洞id为漏洞id
// 人员id为漏洞id hash := md5.Sum([]byte("name@mobile") id := hex.EncodeToString(hash[:])
func (st *SyncTask) GetUUID(id string) (sourceId string, processId string) {
	st.mu.Lock()
	defer st.mu.Unlock()

	return fmt.Sprintf("%d_%d_%s", st.Node.Id, st.Node.AreaId, id), fmt.Sprintf("%d_%d_%d_%s", st.Task.Id, st.Node.Id, st.Node.AreaId, id)
}

/*
资产同步后会有source、sourceTask、process_asset、process_device
漏洞同步后会有source、sourceTask、process_poc
人员同步后会有source、sourceTask、process_staff
1、所有的数据都由sourceDatas生成，是个[]map[string]any类型
2、转换时生成过程索引数据，应该由固定对象做约束
3、sourceData会生成一个sourceTaskData
4、结果表会根据task_data_id查到原始任务表，过程表由查到原始表，原始表根据查到sourceTask表
*/
func (st *SyncTask) appendAttribute(syncType int, item map[string]interface{}) map[string]interface{} {
	switch syncType {
	case data_sync_task.SyncAsset:
		item["asset_task_id"] = item["id"]
	case data_sync_task.SyncThreat:
		item["poc_task_id"] = item["id"]
	case data_sync_task.SyncPeople:
		item["staff_task_id"] = item["id"]
	default:
		return nil
	}

	item["source"] = st.Node.SourceId
	item["source_id"] = st.Node.SourceId
	item["node_id"] = st.Node.Id
	item["node"] = st.Node.Id
	var areaId uint64
	if _, ok := item["ip"].(string); ok && item["ip"].(string) != "" {
		areaId, _ = st.Node.GetAreaByIp(item["ip"].(string))
	} else {
		areaId = st.Node.AreaId
	}
	item["area_id"] = areaId
	item["area"] = areaId
	item["task_id"] = st.Task.Id
	if syncType != data_sync_task.SyncPeople {
		networkType, err := st.Node.GetNetworkType()
		if err != nil {
			return nil
		}
		item["network_type"] = networkType
	}
	item["child_task_id"] = st.ChildTasks[syncType].Id
	item["sync_created_at"] = localtime.NewLocalTime(time.Now())
	item["sync_updated_at"] = localtime.NewLocalTime(time.Now())
	return item
}
func (st *SyncTask) getSourceIndexModel(sourceId uint64, syncType int) string {
	switch sourceId {
	case data_source.FoeyeSourceId:
		version, ok := st.Client.GetConfig("version").(string)
		if ok && version == "v2" {
			if syncType == data_sync_task.SyncAsset {
				return foeye.FoeyeV2SourceTaskAssetsIndex
			} else if syncType == data_sync_task.SyncThreat {
				return foeye.FoeyeV2SourceTaskThreatsIndex
			}
		}
		if syncType == data_sync_task.SyncAsset {
			return foeye.NewFoeyeTaskAssetsModel().IndexName()
		} else if syncType == data_sync_task.SyncThreat {
			return foeye.NewFoeyeTaskThreatsModel().IndexName()
		}
	case data_source.D01SourceId:
		version, ok := st.Client.GetConfig("version").(string)
		if ok && version == "v2" {
			if syncType == data_sync_task.SyncAsset {
				return d01.D01V2SourceTaskAssetsIndex
			} else if syncType == data_sync_task.SyncThreat {
				return d01.D01V2SourceTaskThreatsIndex
			}
		}
		if syncType == data_sync_task.SyncAsset {
			return d01.NewTaskAssetsModel().IndexName()

		} else if syncType == data_sync_task.SyncThreat {
			return d01.NewTaskThreatsModel().IndexName()
		}
	case data_source.ForadarSourceId:
		if syncType == data_sync_task.SyncAsset {
			return foradar.NewForadarTaskAssetsModel().IndexName()
		} else if syncType == data_sync_task.SyncThreat {
			return foradar.NewForadarTaskThreatsModel().IndexName()
		}
	case data_source.QTCloudSourceId:
		if syncType == data_sync_task.SyncAsset {
			return qt_cloud.NewQTCloudTaskAssetsModel().IndexName()

		} else if syncType == data_sync_task.SyncThreat {
			return qt_cloud.NewQTCloudTaskThreatsModel().IndexName()
		} else if syncType == data_sync_task.SyncPeople {
			return qt_cloud.NewQTCloudTaskLinuxEmployeesModel().IndexName()
		}
	case data_source.DingTalkSourceId:
		if syncType == data_sync_task.SyncPeople {
			return dingtalk.NewDingtalkTaskEmployeesModel().IndexName()
		}
	case data_source.BKCmdbSourceId:
		if syncType == data_sync_task.SyncAsset {
			return bk_cmdb.NewBKCmdbTaskAssetsModel().IndexName()

		} else if syncType == data_sync_task.SyncPeople {
			return bk_cmdb.NewBKCmdbTaskEmployeesModel().IndexName()
		}
	case data_source.FileImportSourceId:
		if syncType == data_sync_task.SyncAsset {
			return file_import.NewFileImportTaskAssetsModel().IndexName()

		} else if syncType == data_sync_task.SyncThreat {
			return file_import.NewFileImportTaskThreatsModel().IndexName()
		} else if syncType == data_sync_task.SyncPeople {
			return file_import.NewFileImportTaskPeoplesModel().IndexName()
		}
	case data_source.QiZhiUAuditHostSourceId:
		if syncType == data_sync_task.SyncAsset {
			return qizhi_uaudithost2.NewQiZhiUAuditHostTaskAssetsModel().IndexName()
		}
	case data_source.AliYunCloudSourceId:
		if syncType == data_sync_task.SyncThreat {
			return aliyun_cloud.NewAliYunCloudTaskThreatsModel().IndexName()
		}
	case data_source.WeiBuSourceId:
		if syncType == data_sync_task.SyncAsset {
			return weibu.NewWeibuTaskAssetsModel().IndexName()
		} else if syncType == data_sync_task.SyncThreat {
			return weibu.NewWeibuTaskThreatsModel().IndexName()
		}
	case data_source.ChangTingWAFSourceId:
		if syncType == data_sync_task.SyncAsset {
			return changting_waf.NewChangtingWafTaskAssetsModel().IndexName()
		}
	case data_source.MachLakeSourceId:
		if syncType == data_sync_task.SyncAsset {
			return mach_lake.NewMachLakeTaskAssetsModel().IndexName()
		}
	case data_source.XRaySourceId:
		if syncType == data_sync_task.SyncThreat {
			return xray.NewTaskThreatsModel().IndexName()
		} else if syncType == data_sync_task.SyncAsset {
			return xray.NewTaskAssetsModel().IndexName()
		}
	default:
		source, err := data_source.NewSourceModel().First(mysql.WithWhere("id = ?", st.Task.SourceId))
		if err == nil {
			if syncType == data_sync_task.SyncAsset {
				return data_import.NewDataImportTaskAssetsModel().IndexName(source.EnName)
			} else if syncType == data_sync_task.SyncThreat {
				return data_import.NewDataImportTaskThreatsModel().IndexName(source.EnName)
			} else if syncType == data_sync_task.SyncPeople {
				return data_import.NewDataImportTaskPeoplesModel().IndexName(source.EnName)
			}
		}
	}

	return ""
}
