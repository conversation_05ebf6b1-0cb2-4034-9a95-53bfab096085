package task

import (
	"context"
	"encoding/json"
	"fmt"
	"fobrain/dataSourceFrame/data_sync_frame"
	node2 "fobrain/fobrain/app/services/node"
	nodeBkCmdbCustom "fobrain/fobrain/app/services/node/bk_cmdb"
	"fobrain/fobrain/app/services/node/huawei_hk_cloud"
	"fobrain/fobrain/app/services/node/zhongyi_feishu"
	"fobrain/fobrain/app/services/sync/qt_cloud"
	"time"

	"fobrain/fobrain/app/services/sync/x_ray"

	"fobrain/fobrain/app/services/sync/bk_cmdb_custom_module_property"

	esDataImportMapping "fobrain/cmd/databases/elastic/data_import"
	"fobrain/fobrain/app/services/sync/aliyun_cloud"
	"fobrain/fobrain/app/services/sync/bk_cmdb"
	"fobrain/fobrain/app/services/sync/changting_waf"
	"fobrain/fobrain/app/services/sync/d01"
	"fobrain/fobrain/app/services/sync/data_import"
	"fobrain/fobrain/app/services/sync/dingtalk"
	"fobrain/fobrain/app/services/sync/file_import"
	"fobrain/fobrain/app/services/sync/foeye"
	"fobrain/fobrain/app/services/sync/foradar"
	"fobrain/fobrain/app/services/sync/mach_lake"
	"fobrain/fobrain/app/services/sync/qizhi_uaudithost"
	"fobrain/fobrain/app/services/sync/weibu"
	"fobrain/fobrain/common/localtime"
	"fobrain/fobrain/logs"
	"fobrain/initialize/es"
	"fobrain/initialize/mysql"
	esDataImport "fobrain/models/elastic/source/data_import"
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/data_sync_child_task"
	"fobrain/models/mysql/data_sync_task"
	"fobrain/pkg/utils"
)

type Task struct {
	Task       *data_sync_task.DataSyncTask
	ChildTasks map[int]*data_sync_child_task.DataSyncChildTask
}

// NewSyncDataTask 创建同步任务对象
func NewSyncDataTask() *Task {
	return &Task{
		Task: &data_sync_task.DataSyncTask{},
		ChildTasks: map[int]*data_sync_child_task.DataSyncChildTask{
			1: {}, //资产类型
			2: {}, //漏洞类型
			3: {}, //人员类型
		},
	}
}

// initTask 初始化主任务及子任务
func (t *Task) initTask(node *data_source.Node, dataSource data_source.Source, syncTypes []int, source int, fileName string) error {

	t.Task.SourceId = node.SourceId
	t.Task.NodeId = node.Id
	t.Task.Source = source
	t.Task.File = fileName
	t.Task.StartAt = localtime.Time(time.Now())

	//创建任务信息
	err := data_sync_task.NewDataSyncTaskModel().Create(t.Task)
	if err != nil {
		return err
	}

	//创建子任务
	// 数据源存在数据资产数据且在本次同步的类型当中创建任务
	if dataSource.HasAssetData && utils.InArray(data_sync_task.SyncAsset, syncTypes) {
		t.ChildTasks[data_sync_task.SyncAsset].SourceId = node.SourceId
		t.ChildTasks[data_sync_task.SyncAsset].NodeId = node.Id
		t.ChildTasks[data_sync_task.SyncAsset].TaskId = t.Task.Id
		t.ChildTasks[data_sync_task.SyncAsset].Type = data_sync_task.SyncAsset
		err = data_sync_child_task.NewDataSyncChildTaskModel().Create(t.ChildTasks[data_sync_task.SyncAsset])
		if err != nil {
			return err
		}
	}

	if dataSource.HasVulData && utils.InArray(data_sync_task.SyncThreat, syncTypes) {
		t.ChildTasks[data_sync_task.SyncThreat].SourceId = node.SourceId
		t.ChildTasks[data_sync_task.SyncThreat].NodeId = node.Id
		t.ChildTasks[data_sync_task.SyncThreat].TaskId = t.Task.Id
		t.ChildTasks[data_sync_task.SyncThreat].Type = data_sync_task.SyncThreat
		err = data_sync_child_task.NewDataSyncChildTaskModel().Create(t.ChildTasks[data_sync_task.SyncThreat])
		if err != nil {
			return err
		}
	}

	if dataSource.HasPersonnelData && utils.InArray(data_sync_task.SyncPeople, syncTypes) {
		t.ChildTasks[data_sync_task.SyncPeople].SourceId = node.SourceId
		t.ChildTasks[data_sync_task.SyncPeople].NodeId = node.Id
		t.ChildTasks[data_sync_task.SyncPeople].TaskId = t.Task.Id
		t.ChildTasks[data_sync_task.SyncPeople].Type = data_sync_task.SyncPeople
		err = data_sync_child_task.NewDataSyncChildTaskModel().Create(t.ChildTasks[data_sync_task.SyncPeople])
		if err != nil {
			return err
		}
	}

	return nil
}

// Dispatch 下发任务 source任务来源 1定时 2手动
func (t *Task) Dispatch(nodeId uint64, syncType []int, source int, rows, fileName string) error {
	var DataSourceRegister = map[uint64]node2.SourceNode{
		data_source.HuaweiHkCloudSourceId:        huawei_hk_cloud.New(),
		data_source.BKCmdbCustomDomainSourceId:   nodeBkCmdbCustom.NewBkCmdbCustom(),
		data_source.BKCmdbCustomBusinessSourceId: nodeBkCmdbCustom.NewBkCmdbCustomBusiness(),
		data_source.BKCmdbCustomF5VsSourceId:     nodeBkCmdbCustom.NewBkCmdbCustomF5Vs(),
		data_source.BKCmdbCustomF5PoolSourceId:   nodeBkCmdbCustom.NewBkCmdbCustomF5Pool(),
		data_source.ZhongyiFeishuSourceId:        zhongyi_feishu.NewZhongyiFeishuTask(),
		//data_source.QTCloudSourceId:              qt_cloud.NewQingTengSync(), //err = qt_cloud.SyncQTCloud(node, string(marshal))
	}
	logs.GetLogger().Infof("init node task nodeId:%d,syncType:%d,source:%d", nodeId, syncType, source)
	//获取节点信息
	node, err := data_source.NewNodeModel().First(mysql.WithWhere("id", nodeId))
	//临时跳转，后期逐步替换
	if err != nil {
		return err
	}

	st := NewSyncTask()
	st.Node = node
	if err = st.Client.SetNode(nodeId); err != nil {
		return err
	}

	version, _ := st.Client.GetConfig("version").(string)
	// 数据源同步新框架，后面逐步迁移到新框架
	newFrameSdk := data_sync_frame.NewDataSourceSDK(node.SourceId, version)
	if newFrameSdk != nil {
		go data_sync_frame.NewDataSyncTask().Dispatch(context.TODO(), nodeId, syncType, int64(source), fileName)
		return nil
	}

	nodeInterface, ok := DataSourceRegister[node.SourceId]
	if ok {
		go st.Dispatch(nodeId, syncType, source, nodeInterface)
		return nil
	}

	//获取数据源信息
	dataSource, err := data_source.NewSourceModel().First(mysql.WithWhere("id", node.SourceId))
	if err != nil {
		return err
	}

	//初始化任务
	err = t.initTask(node, dataSource, syncType, source, fileName)
	if err != nil {
		return err
	}

	//更新主任务状态为进行中
	err = data_sync_task.NewDataSyncTaskModel().UpdateStatusById(t.Task.Id, data_sync_task.StatusDoing)
	if err != nil {
		return err
	}

	marshal, err := json.Marshal(t)
	if err != nil {
		return err
	}

	logs.GetLogger().Infof("dispath sourceId:%d sync task", t.Task.SourceId)

	go func() {
		//调用节点对应源下发源同步任务
		switch t.Task.SourceId {
		case data_source.FoeyeSourceId:
			err = foeye.SyncFoeye(node, string(marshal))
		case data_source.D01SourceId:
			err = d01.Sync(node, string(marshal))
		case data_source.ForadarSourceId:
			err = foradar.SyncForadar(node, string(marshal))
		case data_source.QTCloudSourceId:
			err = qt_cloud.SyncQTCloud(node, string(marshal))
		case data_source.DingTalkSourceId:
			err = dingtalk.SyncDingTalk(node, string(marshal))
		case data_source.BKCmdbSourceId:
			err = bk_cmdb.SyncBKCmdb(node, string(marshal))
		case data_source.FileImportSourceId:
			err = file_import.SyncFileImport(node, string(marshal), rows)
		case data_source.AliYunCloudSourceId:
			err = aliyun_cloud.SyncAliYunCloud(node, string(marshal))
		case data_source.QiZhiUAuditHostSourceId:
			err = qizhi_uaudithost.SyncQiZhiUAuditHost(node, string(marshal))
		case data_source.WeiBuSourceId:
			err = weibu.SyncWeiBu(node, string(marshal))
		case data_source.ChangTingWAFSourceId:
			err = changting_waf.SyncChangtingWaf(node, string(marshal))
		case data_source.MachLakeSourceId:
			err = mach_lake.SyncMachLake(node, string(marshal))
		case data_source.XRaySourceId:
			err = x_ray.Sync(node, string(marshal))
		case data_source.BKCmdbCustomVmMachineSourceId:
			err = bk_cmdb_custom_module_property.SyncCmdbCustomVmMachine(node, string(marshal))
		case data_source.BKCmdbCustomCloudEcsSourceId:
			err = bk_cmdb_custom_module_property.SyncCmdbCustomEcs(node, string(marshal))
		default:
			//检测数据源索引是否创建，没有创建则创建索引
			err = checkDataSourceIndexAndCreate(dataSource)
			// 默认为接口同步数据
			err = data_import.SyncApiData(node, string(marshal), rows)
		}

		if err != nil {
			logs.GetLogger().Errorf("Task Dispatch err:%s", err.Error())
			//更新任务状态为失败
			errUpdate := data_sync_task.NewDataSyncTaskModel().UpdateFailById(t.Task.Id, err.Error())
			if errUpdate != nil {
				logs.GetLogger().Errorf("Task Dispatch UpdateFailById err:%s", errUpdate)
			}
		}
	}()
	return nil
}

func checkDataSourceIndexAndCreate(source data_source.Source) error {
	client := es.GetEsClient()

	if source.HasAssetData {
		assetsIndexName := esDataImport.NewDataImportAssetsModel().IndexName(source.EnName)
		// 检查索引是否存在
		exists, err := client.IndexExists(assetsIndexName).Do(context.Background())
		if err != nil {
			return err
		}
		if !exists {
			// 如果索引不存在，创建索引
			createIndex, err := client.CreateIndex(assetsIndexName).BodyString(esDataImportMapping.GetDataImportAssetsMapping()).Do(context.Background())
			if err != nil {
				return err
			}
			if !createIndex.Acknowledged {
				return fmt.Errorf("创建索引 %s 失败", assetsIndexName)
			}
		}
		assetsTaskIndexName := esDataImport.NewDataImportTaskAssetsModel().IndexName(source.EnName)
		// 检查任务索引是否存在
		tExists, err := client.IndexExists(assetsTaskIndexName).Do(context.Background())
		if err != nil {
			return err
		}
		if !tExists {
			// 如果索引不存在，创建索引
			createIndex, err := client.CreateIndex(assetsTaskIndexName).BodyString(esDataImportMapping.GetDataImportTaskAssetsMapping()).Do(context.Background())
			if err != nil {
				return err
			}
			if !createIndex.Acknowledged {
				return fmt.Errorf("创建索引 %s 失败", assetsTaskIndexName)
			}
		}
	}

	if source.HasVulData {
		threatsIndexName := esDataImport.NewDataImportThreatsModel().IndexName(source.EnName)
		// 检查索引是否存在
		exists, err := client.IndexExists(threatsIndexName).Do(context.Background())
		if err != nil {
			return err
		}
		if !exists {
			// 如果索引不存在，创建索引
			createIndex, err := client.CreateIndex(threatsIndexName).BodyString(esDataImportMapping.GetDataImportThreatsMapping()).Do(context.Background())
			if err != nil {
				return err
			}
			if !createIndex.Acknowledged {
				return fmt.Errorf("创建索引 %s 失败", threatsIndexName)
			}
		}
		threatsTaskIndexName := esDataImport.NewDataImportTaskThreatsModel().IndexName(source.EnName)
		// 检查任务索引是否存在
		tExists, err := client.IndexExists(threatsTaskIndexName).Do(context.Background())
		if err != nil {
			return err
		}
		if !tExists {
			// 如果索引不存在，创建索引
			createIndex, err := client.CreateIndex(threatsTaskIndexName).BodyString(esDataImportMapping.GetDataImportTaskThreatsMapping()).Do(context.Background())
			if err != nil {
				return err
			}
			if !createIndex.Acknowledged {
				return fmt.Errorf("创建索引 %s 失败", threatsTaskIndexName)
			}
		}
	}

	if source.HasPersonnelData {
		peoplesIndexName := esDataImport.NewDataImportPeoplesModel().IndexName(source.EnName)
		// 检查索引是否存在
		exists, err := client.IndexExists(peoplesIndexName).Do(context.Background())
		if err != nil {
			return err
		}
		if !exists {
			// 如果索引不存在，创建索引
			createIndex, err := client.CreateIndex(peoplesIndexName).BodyString(esDataImportMapping.GetDataImportPeoplesMapping()).Do(context.Background())
			if err != nil {
				return err
			}
			if !createIndex.Acknowledged {
				return fmt.Errorf("创建索引 %s 失败", peoplesIndexName)
			}
		}
		peoplesTaskIndexName := esDataImport.NewDataImportTaskPeoplesModel().IndexName(source.EnName)
		// 检查索引是否存在
		tExists, err := client.IndexExists(peoplesTaskIndexName).Do(context.Background())
		if err != nil {
			return err
		}
		if !tExists {
			// 如果索引不存在，创建索引
			createIndex, err := client.CreateIndex(peoplesTaskIndexName).BodyString(esDataImportMapping.GetDataImportTaskPeoplesMapping()).Do(context.Background())
			if err != nil {
				return err
			}
			if !createIndex.Acknowledged {
				return fmt.Errorf("创建索引 %s 失败", peoplesTaskIndexName)
			}
		}
	}
	return nil
}

// CleanRunningTask 清理执行中任务标记为失败
func CleanRunningTask() {
	logs.GetLogger().Info("开始清理执行中的任务并标记为失败")

	// 1. 清理执行中的同步任务 (data_sync_task)
	cleanRunningDataSyncTasks()

	logs.GetLogger().Info("清理执行中任务完成")
}

// Shutdown 优雅关闭任务服务
func Shutdown() {
	logs.GetLogger().Info("开始关闭任务服务...")

	// 清理执行中的任务
	CleanRunningTask()

	logs.GetLogger().Info("任务服务已关闭")
}

// cleanRunningDataSyncTasks 清理执行中的同步任务
func cleanRunningDataSyncTasks() {
	// 获取所有执行中的主任务
	runningTasks, _, err := data_sync_task.NewDataSyncTaskModel().List(
		1, -1, // 获取所有数据
		mysql.WithWhere("status = ?", data_sync_task.StatusDoing),
	)
	if err != nil {
		logs.GetLogger().Errorf("获取执行中的同步任务失败: %v", err)
		return
	}

	if len(runningTasks) == 0 {
		logs.GetLogger().Info("没有发现执行中的同步任务")
		return
	}

	logs.GetLogger().Infof("发现 %d 个执行中的同步任务，开始清理", len(runningTasks))

	// 更新主任务状态为失败
	var taskIds []uint64
	for _, task := range runningTasks {
		taskIds = append(taskIds, task.Id)
	}

	err = data_sync_task.NewDataSyncTaskModel().UpdateFailByIds(taskIds, "系统重启时自动清理执行中任务")
	if err != nil {
		logs.GetLogger().Errorf("批量更新同步主任务状态为失败时出错: %v", err)
		return
	}

	// 获取并更新对应的子任务状态为失败
	runningChildTasks, _, err := data_sync_child_task.NewDataSyncChildTaskModel().List(
		1, -1, // 获取所有数据
		mysql.WithWhere("task_id IN ?", taskIds),
		mysql.WithWhere("status = ?", data_sync_child_task.StatusDoing),
	)
	if err != nil {
		logs.GetLogger().Errorf("获取执行中的同步子任务失败: %v", err)
		return
	}

	if len(runningChildTasks) > 0 {
		var childTaskIds []uint64
		for _, childTask := range runningChildTasks {
			childTaskIds = append(childTaskIds, childTask.Id)
		}

		err = data_sync_child_task.NewDataSyncChildTaskModel().UpdateFailByIds(childTaskIds, "系统重启时自动清理执行中任务")
		if err != nil {
			logs.GetLogger().Errorf("批量更新同步子任务状态为失败时出错: %v", err)
		} else {
			logs.GetLogger().Infof("成功清理 %d 个同步主任务和 %d 个子任务", len(taskIds), len(childTaskIds))
		}
	} else {
		logs.GetLogger().Infof("成功清理 %d 个同步主任务", len(taskIds))
	}
}
