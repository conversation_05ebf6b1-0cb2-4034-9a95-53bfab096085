package task

import (
	"errors"
	"fobrain/initialize/es"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/olivere/elastic/v7"
	. "github.com/smartystreets/goconvey/convey"

	"fobrain/fobrain/app/services/node"
	"fobrain/fobrain/app/services/node/huawei_hk_cloud"
	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/initialize/mysql"
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/data_sync_child_task"
	"fobrain/models/mysql/data_sync_task"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestProcessTask(t *testing.T) {
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()

	mockDb.ExpectQuery("SELECT * FROM `data_sync_child_tasks` WHERE `id` = ? ORDER BY `data_sync_child_tasks`.`id` LIMIT 1").
		WithArgs(0).
		WillReturnRows(mockDb.NewRows([]string{}).AddRow())

	mockDb.Mock.ExpectBegin()
	mockDb.ExpectExec("UPDATE `data_sync_child_tasks` SET `updated_at`=?,`status`=?,`start_at`=? WHERE `id` = ? AND `id` = ? ORDER BY `data_sync_child_tasks`.`id` LIMIT 1").
		WillReturnResult(sqlmock.NewResult(1, 1))
	mockDb.Mock.ExpectCommit()

	st := NewSyncTask()
	_ = st.Client.SetConfig(map[string]any{
		"account":  "zhang",
		"password": "123456",
		"ip":       "127.0.0.1",
		"protocol": "http",
	})

	cli := huawei_hk_cloud.New()

	time.Sleep(time.Second)
	patches := gomonkey.ApplyMethodReturn(cli, "GetAssets", nil)

	mockDb.ExpectQuery("SELECT * FROM `data_sync_child_tasks` WHERE `id` = ? ORDER BY `data_sync_child_tasks`.`id` LIMIT 1").
		WithArgs(0).
		WillReturnRows(mockDb.NewRows([]string{}).AddRow())

	mockDb.Mock.ExpectBegin()
	mockDb.ExpectExec("UPDATE `data_sync_child_tasks` SET `updated_at`=?,`sync_data_total`=? WHERE `id` = ? AND `id` = ? ORDER BY `data_sync_child_tasks`.`id` LIMIT 1").
		WillReturnResult(sqlmock.NewResult(1, 1))
	mockDb.Mock.ExpectCommit()

	patches.ApplyMethodReturn(st, "UpdateSyncDataTotal", nil).Reset()
	patches.ApplyMethodReturn(st, "BatchUpdateAsset", nil).Reset()
	patches.ApplyMethodReturn(st, "UpdateSyncDataSuccessTotal", nil).Reset()
	patches.ApplyMethodReturn(st, "SetStatus", nil).Reset()

	err := st.ProcessTask(cli)
	assert.NotNil(t, err)
}

func TestInitTask(t *testing.T) {

	Convey("Test_NewSyncDataTask", t, func() {
		time.Sleep(time.Second)
		Convey("DataSyncTaskCreateError", func() {
			time.Sleep(time.Second)
			patch := gomonkey.ApplyMethodReturn(data_sync_task.NewDataSyncTaskModel(), "Create", errors.New("DataSyncTaskCreateError"))
			err := NewSyncDataTask().initTask(&data_source.Node{}, data_source.Source{}, []int{1, 2, 3}, 1, "")
			patch.Reset()
			So(err, ShouldBeError)
		})

		Convey("DataSyncChildTaskCreateError", func() {
			time.Sleep(time.Second)
			patches := []*gomonkey.Patches{
				gomonkey.ApplyMethodReturn(data_sync_task.NewDataSyncTaskModel(), "Create", nil),
				gomonkey.ApplyMethodReturn(data_sync_child_task.NewDataSyncChildTaskModel(), "Create", errors.New("DataSyncChildTaskCreateError")),
			}
			err := NewSyncDataTask().initTask(&data_source.Node{}, data_source.Source{
				HasAssetData:     true,
				HasVulData:       true,
				HasPersonnelData: true,
			}, []int{1, 2, 3}, 1, "")
			for _, patch := range patches {
				patch.Reset()
			}
			So(err, ShouldBeError)
		})

		Convey("Pass", func() {
			time.Sleep(time.Second)
			patches := []*gomonkey.Patches{
				gomonkey.ApplyMethodReturn(data_sync_task.NewDataSyncTaskModel(), "Create", nil),
				gomonkey.ApplyMethodReturn(data_sync_child_task.NewDataSyncChildTaskModel(), "Create", nil),
			}
			err := NewSyncDataTask().initTask(&data_source.Node{}, data_source.Source{
				HasAssetData:     true,
				HasVulData:       true,
				HasPersonnelData: true,
			}, []int{1, 2, 3}, 1, "")
			for _, patch := range patches {
				patch.Reset()
			}
			So(err, ShouldBeNil)
		})

	})
}

func TestSyncTask_Dispatch(t *testing.T) {
	patches := []*gomonkey.Patches{
		gomonkey.ApplyMethodReturn(&node.Client{}, "SetNode", nil),
		gomonkey.ApplyMethodReturn(&SyncTask{}, "WriteLog"),
		gomonkey.ApplyMethodReturn(&SyncTask{}, "ProcessTask", nil),
	}

	mockDb := testcommon.InitSqlMock()

	mockDb.ExpectQuery("SELECT * FROM `data_nodes` WHERE `id` = ?").
		WithArgs(1).
		WillReturnRows(sqlmock.NewRows([]string{"id", "source_id"}).AddRow(uint64(1), uint64(1)))

	mockDb.ExpectQuery("SELECT * FROM `data_sources` WHERE `id` = ? ORDER BY `data_sources`.`id` LIMIT 1").
		WithArgs(1).
		WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(uint64(1)))

	mockDb.ExpectBegin()
	mockDb.ExpectExec("INSERT INTO `data_sync_tasks` (`created_at`,`updated_at`,`source_id`,`node_id`,`status`,`source`,`start_at`,`end_at`,`message`) VALUES (?,?,?,?,?,?,?,?,?)").
		WillReturnResult(sqlmock.NewResult(1, 1))
	mockDb.ExpectCommit()

	mockDb.ExpectBegin()
	mockDb.ExpectExec("UPDATE `data_sync_tasks` SET `updated_at`=?,`status`=? WHERE `id` = ?").
		WillReturnResult(sqlmock.NewResult(1, 1))
	mockDb.ExpectCommit()

	st := NewSyncTask()
	st.Dispatch(uint64(1), []int{1}, 1, nil)
	for _, patch := range patches {
		patch.Reset()
	}
	mockDb.Close()
	//assert.Nil(t, err)

	patch := gomonkey.ApplyMethodReturn(&node.Client{}, "SetNode", errors.New("err"))
	st = NewSyncTask()
	st.Dispatch(uint64(1), []int{1}, 1, nil)
	patch.Reset()
	//assert.NotNil(t, err)
}

func TestGetParamByKey(t *testing.T) {
	patch := gomonkey.ApplyMethodReturn(&node.Client{}, "GetConfig", nil)
	res := NewSyncTask().GetParamByKey("")
	patch.Reset()
	assert.Nil(t, res)
}

func TestSyncTask_BatchUpdateSourceAsset(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()

		mockDb.ExpectQuery("SELECT * FROM `data_sources` WHERE id = ? ORDER BY `data_sources`.`id` LIMIT 1").
			WithArgs("network_type").
			WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(uint64(100)))

		time.Sleep(time.Second)
		defer gomonkey.ApplyMethodReturn(&SyncTask{}, "WriteLog").Reset()
		time.Sleep(time.Second)
		defer gomonkey.ApplyMethodReturn(&es.SafeBulkService{}, "Do", &elastic.BulkResponse{
			Items: []map[string]*elastic.BulkResponseItem{
				{
					"test": &elastic.BulkResponseItem{
						Error: &elastic.ErrorDetails{Type: "err", Reason: ""},
					},
				},
			},
		}, nil).Reset()

		st := NewSyncTask()
		st.Task.SourceId = 100
		err := st.BatchUpdateSourceAsset([]map[string]interface{}{
			{"id": "1"},
		})
		assert.Equal(t, err.Error(), "BatchUpdateAsset 数据源索引不存在")
	})
	t.Run("error", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()

		mockDb.ExpectQuery("SELECT * FROM `data_sources` WHERE id = ? ORDER BY `data_sources`.`id` LIMIT 1").
			WithArgs("network_type").
			WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(uint64(100)))

		time.Sleep(time.Second)
		defer gomonkey.ApplyMethodReturn(&SyncTask{}, "WriteLog").Reset()
		time.Sleep(time.Second)
		defer gomonkey.ApplyMethodReturn(&es.SafeBulkService{}, "Do", &elastic.BulkResponse{
			Items: []map[string]*elastic.BulkResponseItem{
				{
					"test": &elastic.BulkResponseItem{
						Error: &elastic.ErrorDetails{Type: "err", Reason: ""},
					},
				},
			},
		}, errors.New("err")).Reset()

		st := NewSyncTask()
		st.Task.SourceId = 100
		err := st.BatchUpdateSourceAsset([]map[string]interface{}{
			{"id": "1"},
		})
		assert.Equal(t, err.Error(), "BatchUpdateAsset 数据源索引不存在")
	})
	t.Run("error", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()

		mockDb.ExpectQuery("SELECT * FROM `data_sources` WHERE id = ? ORDER BY `data_sources`.`id` LIMIT 1").
			WithArgs(uint64(100)).
			WillReturnError(errors.New("err"))
		st := NewSyncTask()
		st.Task.SourceId = 100
		err := st.BatchUpdateSourceAsset([]map[string]interface{}{
			{"id": "1"},
		})
		assert.Equal(t, err.Error(), "BatchUpdateAsset 数据源索引不存在")
	})
}

func TestSyncTask_BatchUpdateAsset(t *testing.T) {
	mockDb := testcommon.InitSqlMock()

	mockDb.ExpectQuery("SELECT * FROM `data_sources` WHERE id = ? ORDER BY `data_sources`.`id` LIMIT 1").
		WithArgs(uint64(100)).
		WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(uint64(100)))

	patches := []*gomonkey.Patches{
		gomonkey.ApplyMethodReturn(&SyncTask{}, "WriteLog"),
		gomonkey.ApplyMethodReturn(&es.SafeBulkService{}, "Do", &elastic.BulkResponse{
			Items: []map[string]*elastic.BulkResponseItem{
				{
					"test": &elastic.BulkResponseItem{
						Error: &elastic.ErrorDetails{Type: "err", Reason: ""},
					},
				},
			},
		}, nil),
	}

	st := NewSyncTask()
	st.Task.SourceId = 100
	err := st.BatchUpdateAsset([]map[string]interface{}{
		{"id": "1"},
	}, []map[string]interface{}{
		{"id": "1"},
	}, []map[string]interface{}{})
	for _, patch := range patches {
		patch.Reset()
	}
	mockDb.Close()
	assert.Nil(t, err)

	mockDb = testcommon.InitSqlMock()
	defer mockDb.Close()

	mockDb.ExpectQuery("SELECT * FROM `data_sources` WHERE id = ? ORDER BY `data_sources`.`id` LIMIT 1").
		WithArgs(uint64(100)).
		WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(uint64(100)))

	patches = []*gomonkey.Patches{
		gomonkey.ApplyMethodReturn(&SyncTask{}, "WriteLog"),
		gomonkey.ApplyMethodReturn(&es.SafeBulkService{}, "Do", &elastic.BulkResponse{
			Items: []map[string]*elastic.BulkResponseItem{
				{
					"test": &elastic.BulkResponseItem{
						Error: &elastic.ErrorDetails{Type: "err", Reason: ""},
					},
				},
			},
		}, errors.New("err")),
	}

	st = NewSyncTask()
	st.Task.SourceId = 100
	err = st.BatchUpdateAsset([]map[string]interface{}{
		{"id": "1"},
	}, []map[string]interface{}{
		{"id": "1"},
	}, []map[string]interface{}{})
	for _, patch := range patches {
		patch.Reset()
	}
	mockDb.Close()
	assert.NotNil(t, err)

	mockDb = testcommon.InitSqlMock()
	defer mockDb.Close()

	mockDb.ExpectQuery("SELECT * FROM `data_sources` WHERE id = ? ORDER BY `data_sources`.`id` LIMIT 1").
		WithArgs(uint64(100)).
		WillReturnError(errors.New("err"))
	st = NewSyncTask()
	st.Task.SourceId = 100
	err = st.BatchUpdateAsset([]map[string]interface{}{
		{"id": "1"},
	}, []map[string]interface{}{
		{"id": "1"},
	}, []map[string]interface{}{})
	assert.NotNil(t, err)
}

func TestSyncTask_BatchUpdateVul(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()

		mockDb.ExpectQuery("SELECT * FROM `data_sources` WHERE id = ? ORDER BY `data_sources`.`id` LIMIT 1").
			WithArgs("network_type").
			WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(uint64(100)))

		time.Sleep(time.Second)
		defer gomonkey.ApplyMethodReturn(&SyncTask{}, "WriteLog").Reset()
		time.Sleep(time.Second)
		defer gomonkey.ApplyMethodReturn(&es.SafeBulkService{}, "Do", &elastic.BulkResponse{
			Items: []map[string]*elastic.BulkResponseItem{
				{
					"test": &elastic.BulkResponseItem{
						Error: &elastic.ErrorDetails{Type: "err", Reason: ""},
					},
				},
			},
		}, nil).Reset()

		st := NewSyncTask()
		st.Task.SourceId = 100
		err := st.BatchUpdateVul([]map[string]interface{}{
			{"id": "1"},
		}, []map[string]interface{}{
			{"id": "1"},
		})
		assert.Equal(t, err.Error(), "BatchUpdateVul 数据源索引不存在")
	})
	t.Run("error", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()

		mockDb.ExpectQuery("SELECT * FROM `data_sources` WHERE id = ? ORDER BY `data_sources`.`id` LIMIT 1").
			WithArgs("network_type").
			WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(uint64(100)))

		time.Sleep(time.Second)
		defer gomonkey.ApplyMethodReturn(&SyncTask{}, "WriteLog").Reset()
		time.Sleep(time.Second)
		defer gomonkey.ApplyMethodReturn(&es.SafeBulkService{}, "Do", &elastic.BulkResponse{
			Items: []map[string]*elastic.BulkResponseItem{
				{
					"test": &elastic.BulkResponseItem{
						Error: &elastic.ErrorDetails{Type: "err", Reason: ""},
					},
				},
			},
		}, errors.New("err")).Reset()

		st := NewSyncTask()
		st.Task.SourceId = 100
		err := st.BatchUpdateVul([]map[string]interface{}{
			{"id": "1"},
		}, []map[string]interface{}{
			{"id": "1"},
		})
		assert.NotNil(t, err)
	})
	t.Run("error", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()

		mockDb.ExpectQuery("SELECT * FROM `data_sources` WHERE id = ? ORDER BY `data_sources`.`id` LIMIT 1").
			WithArgs("network_type").
			WillReturnError(errors.New("err"))
		st := NewSyncTask()
		st.Task.SourceId = 100
		err := st.BatchUpdateVul([]map[string]interface{}{
			{"id": "1"},
		}, []map[string]interface{}{
			{"id": "1"},
		})
		assert.NotNil(t, err)
	})
}

func TestSyncTask_BatchUpdateStaff(t *testing.T) {
	mockDb := testcommon.InitSqlMock()

	mockDb.ExpectQuery("SELECT * FROM `data_sources` WHERE id = ? ORDER BY `data_sources`.`id` LIMIT 1").
		WithArgs("network_type").
		WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(uint64(100)))

	patches := []*gomonkey.Patches{
		gomonkey.ApplyMethodReturn(&SyncTask{}, "WriteLog"),
		gomonkey.ApplyMethodReturn(&es.SafeBulkService{}, "Do", &elastic.BulkResponse{
			Items: []map[string]*elastic.BulkResponseItem{
				{
					"test": &elastic.BulkResponseItem{
						Error: &elastic.ErrorDetails{Type: "err", Reason: ""},
					},
				},
			},
		}, nil),
	}

	st := NewSyncTask()
	st.Task.SourceId = 100
	err := st.BatchUpdateStaff([]map[string]interface{}{
		{"id": "1"},
	}, []map[string]interface{}{
		{"id": "1"},
	})
	for _, patch := range patches {
		patch.Reset()
	}
	mockDb.Close()
	assert.Equal(t, err.Error(), "BatchUpdateStaff 数据源索引不存在")

	mockDb = testcommon.InitSqlMock()

	mockDb.ExpectQuery("SELECT * FROM `data_sources` WHERE id = ? ORDER BY `data_sources`.`id` LIMIT 1").
		WithArgs("network_type").
		WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(uint64(100)))

	patches = []*gomonkey.Patches{
		gomonkey.ApplyMethodReturn(&SyncTask{}, "WriteLog"),
		gomonkey.ApplyMethodReturn(&es.SafeBulkService{}, "Do", &elastic.BulkResponse{
			Items: []map[string]*elastic.BulkResponseItem{
				{
					"test": &elastic.BulkResponseItem{
						Error: &elastic.ErrorDetails{Type: "err", Reason: ""},
					},
				},
			},
		}, errors.New("err")),
	}

	st = NewSyncTask()
	st.Task.SourceId = 100
	err = st.BatchUpdateStaff([]map[string]interface{}{
		{"id": "1"},
	}, []map[string]interface{}{
		{"id": "1"},
	})
	for _, patch := range patches {
		patch.Reset()
	}
	mockDb.Close()
	assert.NotNil(t, err)

	mockDb = testcommon.InitSqlMock()
	defer mockDb.Close()

	mockDb.ExpectQuery("SELECT * FROM `data_sources` WHERE id = ? ORDER BY `data_sources`.`id` LIMIT 1").
		WithArgs(uint64(100)).
		WillReturnError(errors.New("err"))
	st = NewSyncTask()
	st.Task.SourceId = 100
	err = st.BatchUpdateStaff([]map[string]interface{}{
		{"id": "1"},
	}, []map[string]interface{}{
		{"id": "1"},
	})
	for _, patch := range patches {
		patch.Reset()
	}
	mockDb.Close()
	assert.NotNil(t, err)
}

func TestSyncTask_UpdateSyncDataTotalal(t *testing.T) {
	patch := gomonkey.ApplyMethodReturn(&data_sync_child_task.DataSyncChildTask{}, "UpdateSyncDataTotal", nil)
	time.Sleep(time.Millisecond * 300)
	err := NewSyncTask().UpdateSyncDataTotal(1, 1)
	patch.Reset()
	assert.Nil(t, err)

	patch = gomonkey.ApplyMethodReturn(&data_sync_child_task.DataSyncChildTask{}, "UpdateSyncDataTotal", errors.New("err"))
	time.Sleep(time.Millisecond * 300)
	err = NewSyncTask().UpdateSyncDataTotal(1, 1)
	patch.Reset()
	assert.NotNil(t, err)
}

func TestSyncTask_UpdateSyncDataSuccessTotal(t *testing.T) {
	time.Sleep(time.Millisecond * 300)
	patch := gomonkey.ApplyMethodReturn(&data_sync_child_task.DataSyncChildTask{}, "UpdateSyncDataSuccessTotal", nil)
	err := NewSyncTask().UpdateSyncDataSuccessTotal(1, 1)
	patch.Reset()
	assert.Nil(t, err)
	time.Sleep(time.Millisecond * 300)
	patch = gomonkey.ApplyMethodReturn(&data_sync_child_task.DataSyncChildTask{}, "UpdateSyncDataSuccessTotal", errors.New("err"))
	err = NewSyncTask().UpdateSyncDataSuccessTotal(1, 1)
	patch.Reset()
	assert.NotNil(t, err)
}

func TestSyncTask_SetStatus(t *testing.T) {
	mockDb := testcommon.InitSqlMock()
	defer mockDb.Close()

	st := NewSyncTask()
	st.ChildTasks = map[int]*data_sync_child_task.DataSyncChildTask{
		1: {
			BaseModel: mysql.BaseModel{
				Id: 1,
			},
		},
	}

	// 首先 mock SELECT 查询
	mockDb.ExpectQuery("SELECT * FROM `data_sync_child_tasks` WHERE `id` = ? ORDER BY `data_sync_child_tasks`.`id` LIMIT 1").
		WithArgs(1). // 修改这里，匹配实际传入的参数
		WillReturnRows(sqlmock.NewRows([]string{"id", "status"}).
			AddRow(1, 1))

	// 然后 mock UPDATE 语句
	mockDb.ExpectExec("UPDATE `data_sync_child_tasks` SET `status` = ? WHERE `id` = ?").
		WithArgs(1, 1).
		WillReturnResult(sqlmock.NewResult(0, 1))

	err := st.SetStatus(1, 1, "")
	assert.Nil(t, err)
}

func TestSetStatus(t *testing.T) {
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()
	//t.Run("SetStatus doing", func(t *testing.T) {
	//	mockDb.Mock.ExpectBegin()
	//	mockDb.ExpectExec("DELETE FROM `audit_logs` WHERE id in (?)").
	//		WithArgs(1).
	//		WillReturnResult(sqlmock.NewResult(0, 1))
	//	mockDb.Mock.ExpectCommit()
	//
	//	st := &SyncTask{
	//		Node: &data_source.Node{
	//			SoftDeleteModel: mysql.SoftDeleteModel{
	//				Ids: 1,
	//			},
	//			AreaId:   1,
	//			SourceId: 1,
	//		},
	//		Task: &data_sync_task.DataSyncTask{},
	//		ChildTasks: map[int]*data_sync_child_task.DataSyncChildTask{
	//			data_sync_task.SyncAsset: {
	//				BaseModel: mysql.BaseModel{
	//					Ids: 1,
	//				},
	//			}, //资产类型
	//			data_sync_task.SyncThreat: {
	//				BaseModel: mysql.BaseModel{
	//					Ids: 1,
	//				},
	//			}, //漏洞类型
	//			data_sync_task.SyncPeople: {
	//				BaseModel: mysql.BaseModel{
	//					Ids: 1,
	//				},
	//			}, //人员类型
	//		},
	//	}
	//	err := st.SetStatus(data_sync_task.SyncAsset, 2, "")
	//	assert.NoError(t, err)
	//})
	//
	//t.Run("SetStatus fail", func(t *testing.T) {
	//	mockDb.Mock.ExpectBegin()
	//	mockDb.ExpectExec("DELETE FROM `audit_logs` WHERE id > 0").
	//		WillReturnResult(sqlmock.NewResult(0, 1))
	//	mockDb.Mock.ExpectCommit()
	//
	//	st := &SyncTask{
	//		Node: &data_source.Node{
	//			SoftDeleteModel: mysql.SoftDeleteModel{
	//				Ids: 1,
	//			},
	//			AreaId:   1,
	//			SourceId: 1,
	//		},
	//		Task: &data_sync_task.DataSyncTask{},
	//		ChildTasks: map[int]*data_sync_child_task.DataSyncChildTask{
	//			data_sync_task.SyncAsset:  {}, //资产类型
	//			data_sync_task.SyncThreat: {}, //漏洞类型
	//			data_sync_task.SyncPeople: {}, //人员类型
	//		},
	//	}
	//	err := st.SetStatus(data_sync_task.SyncAsset, 2, "")
	//	assert.NoError(t, err)
	//})
}

func TestGetUUID(t *testing.T) {
	tests := []struct {
		name         string
		id           string
		expectedSrc  string
		expectedProc string
	}{
		{
			name:         "Normal case",
			id:           "test-id",
			expectedSrc:  "1_1_test-id",
			expectedProc: "0_1_1_test-id",
		},
		{
			name:         "Empty ID",
			id:           "",
			expectedSrc:  "1_1_",
			expectedProc: "0_1_1_",
		},
		{
			name:         "Special characters",
			id:           "abc@123",
			expectedSrc:  "1_1_abc@123",
			expectedProc: "0_1_1_abc@123",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			st := &SyncTask{
				Node: &data_source.Node{
					SoftDeleteModel: mysql.SoftDeleteModel{
						Id: 1,
					},
					AreaId: 1,
				},
				Task: &data_sync_task.DataSyncTask{},
				ChildTasks: map[int]*data_sync_child_task.DataSyncChildTask{
					data_sync_task.SyncAsset:  {}, //资产类型
					data_sync_task.SyncThreat: {}, //漏洞类型
					data_sync_task.SyncPeople: {}, //人员类型
				},
			}

			srcId, procId := st.GetUUID(tt.id)
			assert.Equal(t, tt.expectedSrc, srcId)
			assert.Equal(t, tt.expectedProc, procId)
		})
	}

}

func TestAppendAttribute(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		st := NewSyncTask()
		res := st.appendAttribute(data_sync_task.SyncPeople, make(map[string]interface{}))
		assert.NotNil(t, res)
	})
}

func TestGetSourceIndexModel(t *testing.T) {
	tests := []struct {
		name         string
		sourceId     uint64
		syncType     int
		expectedIdx2 string
	}{
		{
			name:         "Foeye Source Asset Sync",
			sourceId:     data_source.FoeyeSourceId,
			syncType:     data_sync_task.SyncAsset,
			expectedIdx2: "foeye_task_assets",
		},
		{
			name:         "Foeye Source Threat Sync",
			sourceId:     data_source.FoeyeSourceId,
			syncType:     data_sync_task.SyncThreat,
			expectedIdx2: "foeye_task_threats",
		},
		{
			name:         "Default Case",
			sourceId:     9999, // 未定义的 SourceId
			syncType:     data_sync_task.SyncAsset,
			expectedIdx2: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			idx1 := NewSyncTask().getSourceIndexModel(tt.sourceId, tt.syncType)
			assert.Equal(t, tt.expectedIdx2, idx1)
		})
	}
}

func TestSyncTask_GetTaskId(t *testing.T) {
	st := &SyncTask{
		Task: &data_sync_task.DataSyncTask{
			BaseModel: mysql.BaseModel{
				Id: 1,
			},
		},
		ChildTasks: map[int]*data_sync_child_task.DataSyncChildTask{
			data_sync_task.SyncAsset:  {},
			data_sync_task.SyncThreat: {},
			data_sync_task.SyncPeople: {
				BaseModel: mysql.BaseModel{
					Id: 2,
				},
			},
		},
	}

	got, got1 := st.GetTaskId(data_sync_task.SyncPeople)
	assert.Equal(t, uint64(2), got1)
	assert.Equal(t, uint64(1), got)
}

func TestSyncTask_BatchUpdateProcessDevice(t *testing.T) {
	patches := []*gomonkey.Patches{
		gomonkey.ApplyMethodReturn(&SyncTask{}, "WriteLog"),
		gomonkey.ApplyMethodReturn(&es.SafeBulkService{}, "Do", &elastic.BulkResponse{
			Items: []map[string]*elastic.BulkResponseItem{
				{
					"test": &elastic.BulkResponseItem{
						Error: &elastic.ErrorDetails{Type: "err", Reason: ""},
					},
				},
			},
		}, nil),
	}

	st := NewSyncTask()
	st.Task.SourceId = 100
	err := st.BatchUpdateProcessDevice([]map[string]interface{}{
		{"id": "1"},
	})
	for _, patch := range patches {
		patch.Reset()
	}
	assert.Nil(t, err)

}
