package task

import (
	"encoding/json"
	"errors"
	"testing"
	"time"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/olivere/elastic/v7"
	. "github.com/smartystreets/goconvey/convey"

	esDataImportMapping "fobrain/cmd/databases/elastic/data_import"
	"fobrain/initialize/es"
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/data_sync_child_task"
	"fobrain/models/mysql/data_sync_task"
	"fobrain/models/mysql/proactive_task_node_relations"
	"fobrain/models/mysql/task"

	"github.com/stretchr/testify/assert"
)

func TestNewSyncDataTask(t *testing.T) {

	<PERSON>vey("Test_NewSyncDataTask", t, func() {

		Convey("PASS", func() {
			err := NewSyncDataTask()
			So(err, ShouldNotBeEmpty)
		})

	})
}

func TestDispatch(t *testing.T) {

	<PERSON>vey("Test_Dispatch", t, func() {

		<PERSON><PERSON>("NodeFirstError", func() {
			time.Sleep(time.Millisecond * 300)
			defer gomonkey.ApplyMethodReturn(data_source.NewNodeModel(), "First", nil, errors.New("NodeFirstError")).Reset()
			err := NewSyncDataTask().Dispatch(0, []int{1, 2, 3}, 1, "", "")
			So(err, ShouldBeError)
		})

		Convey("SourceFirstError", func() {
			time.Sleep(time.Millisecond * 300)
			defer gomonkey.ApplyMethodReturn(data_source.NewNodeModel(), "First", &data_source.Node{
				SourceId: 1,
			}, nil).Reset()
			time.Sleep(time.Millisecond * 300)
			defer gomonkey.ApplyMethodReturn(data_source.NewSourceModel(), "First", nil, errors.New("SourceFirstError")).Reset()
			err := NewSyncDataTask().Dispatch(0, []int{1, 2, 3}, 1, "", "")
			So(err, ShouldBeError)
		})

		Convey("UpdateStatusByIdError", func() {
			time.Sleep(time.Millisecond * 300)
			defer gomonkey.ApplyMethodReturn(data_source.NewNodeModel(), "First", &data_source.Node{
				SourceId: 1,
			}, nil).Reset()
			time.Sleep(time.Millisecond * 300)
			defer gomonkey.ApplyMethodReturn(data_source.NewSourceModel(), "First", data_source.Source{}, nil).Reset()
			time.Sleep(time.Millisecond * 300)
			defer gomonkey.ApplyMethodReturn(data_sync_task.NewDataSyncTaskModel(), "UpdateStatusById", errors.New("UpdateStatusByIdError")).Reset()
			err := NewSyncDataTask().Dispatch(0, []int{1, 2, 3}, 1, "", "")
			So(err, ShouldBeError)
		})

		Convey("MarshalError", func() {
			time.Sleep(time.Millisecond * 300)
			defer gomonkey.ApplyMethodReturn(data_source.NewNodeModel(), "First", &data_source.Node{
				SourceId: 1,
			}, nil).Reset()
			time.Sleep(time.Millisecond * 300)
			defer gomonkey.ApplyMethodReturn(data_source.NewSourceModel(), "First", data_source.Source{}, nil).Reset()
			time.Sleep(time.Millisecond * 300)
			defer gomonkey.ApplyMethodReturn(data_sync_task.NewDataSyncTaskModel(), "UpdateStatusById", errors.New("nil")).Reset()
			time.Sleep(time.Millisecond * 300)
			defer gomonkey.ApplyFuncReturn(json.Marshal, nil, errors.New("MarshalError")).Reset()
			err := NewSyncDataTask().Dispatch(0, []int{1, 2, 3}, 1, "", "")
			So(err, ShouldBeError)
		})

	})
}

func TestCheckDataSourceIndexAndCreate(t *testing.T) {

	Convey("Test_CheckDataSourceIndexAndCreate", t, func() {

		Convey("AssetsIndexExistsError", func() {
			time.Sleep(time.Millisecond * 300)
			defer gomonkey.ApplyMethodReturn(es.GetEsClient().IndexExists("custom_assets"), "Do", false, errors.New("AssetsIndexExistsError")).Reset()
			err := checkDataSourceIndexAndCreate(data_source.Source{
				EnName:           "custom",
				HasAssetData:     true,
				HasVulData:       true,
				HasPersonnelData: true,
			})
			So(err, ShouldBeError)
		})
		Convey("CreateAssetsIndexError", func() {
			time.Sleep(time.Millisecond * 300)
			defer gomonkey.ApplyMethodReturn(es.GetEsClient().IndexExists("custom_assets"), "Do", false, nil).Reset()
			time.Sleep(time.Millisecond * 300)
			defer gomonkey.ApplyMethodReturn(es.GetEsClient().CreateIndex("custom_assets").BodyString(esDataImportMapping.GetDataImportAssetsMapping()), "Do", nil, errors.New("CreateAssetsIndexError")).Reset()
			err := checkDataSourceIndexAndCreate(data_source.Source{
				EnName:           "custom",
				HasAssetData:     true,
				HasVulData:       true,
				HasPersonnelData: true,
			})
			So(err, ShouldBeError)
		})
		Convey("CreateAssetsIndexAcknowledgedError", func() {
			time.Sleep(time.Millisecond * 300)
			defer gomonkey.ApplyMethodReturn(es.GetEsClient().IndexExists("custom_assets"), "Do", false, nil).Reset()
			time.Sleep(time.Millisecond * 300)
			defer gomonkey.ApplyMethodReturn(es.GetEsClient().CreateIndex("custom_assets").BodyString(esDataImportMapping.GetDataImportAssetsMapping()), "Do", &elastic.IndicesCreateResult{
				Acknowledged: false,
			}, nil).Reset()
			err := checkDataSourceIndexAndCreate(data_source.Source{
				EnName:           "custom",
				HasAssetData:     true,
				HasVulData:       true,
				HasPersonnelData: true,
			})
			So(err, ShouldBeError)
		})
		Convey("TaskAssetsIndexExistsError", func() {
			time.Sleep(time.Millisecond * 300)
			defer gomonkey.ApplyMethodReturn(es.GetEsClient().IndexExists(), "Do", false, nil).Reset()
			time.Sleep(time.Millisecond * 300)
			defer gomonkey.ApplyMethodReturn(es.GetEsClient().CreateIndex("custom_task_assets").BodyString(esDataImportMapping.GetDataImportAssetsMapping()), "Do", &elastic.IndicesCreateResult{
				Acknowledged: true,
			}, nil).Reset()
			err := checkDataSourceIndexAndCreate(data_source.Source{
				EnName:           "custom",
				HasAssetData:     true,
				HasVulData:       true,
				HasPersonnelData: true,
			})
			So(err, ShouldBeNil)
		})

		Convey("VulIndexExistsError", func() {
			time.Sleep(time.Millisecond * 300)
			defer gomonkey.ApplyMethodReturn(es.GetEsClient().IndexExists("custom_assets"), "Do", false, errors.New("AssetsIndexExistsError")).Reset()
			err := checkDataSourceIndexAndCreate(data_source.Source{
				EnName:           "custom",
				HasAssetData:     false,
				HasVulData:       true,
				HasPersonnelData: true,
			})
			So(err, ShouldBeError)
		})
		Convey("CreateVulIndexError", func() {
			time.Sleep(time.Millisecond * 300)
			defer gomonkey.ApplyMethodReturn(es.GetEsClient().IndexExists("custom_assets"), "Do", false, nil).Reset()
			time.Sleep(time.Millisecond * 300)
			defer gomonkey.ApplyMethodReturn(es.GetEsClient().CreateIndex("custom_assets").BodyString(esDataImportMapping.GetDataImportAssetsMapping()), "Do", nil, errors.New("CreateAssetsIndexError")).Reset()
			err := checkDataSourceIndexAndCreate(data_source.Source{
				EnName:           "custom",
				HasAssetData:     false,
				HasVulData:       true,
				HasPersonnelData: true,
			})
			So(err, ShouldBeError)
		})
		Convey("CreateVulIndexAcknowledgedError", func() {
			time.Sleep(time.Millisecond * 300)
			defer gomonkey.ApplyMethodReturn(es.GetEsClient().IndexExists("custom_assets"), "Do", false, nil).Reset()
			time.Sleep(time.Millisecond * 300)
			defer gomonkey.ApplyMethodReturn(es.GetEsClient().CreateIndex("custom_assets").BodyString(esDataImportMapping.GetDataImportAssetsMapping()), "Do", &elastic.IndicesCreateResult{
				Acknowledged: false,
			}, nil).Reset()
			err := checkDataSourceIndexAndCreate(data_source.Source{
				EnName:           "custom",
				HasAssetData:     false,
				HasVulData:       true,
				HasPersonnelData: true,
			})
			So(err, ShouldBeError)
		})
		Convey("TaskVulIndexExistsError", func() {
			time.Sleep(time.Millisecond * 300)
			defer gomonkey.ApplyMethodReturn(es.GetEsClient().IndexExists(), "Do", false, nil).Reset()
			time.Sleep(time.Millisecond * 300)
			defer gomonkey.ApplyMethodReturn(es.GetEsClient().CreateIndex("custom_task_assets").BodyString(esDataImportMapping.GetDataImportAssetsMapping()), "Do", &elastic.IndicesCreateResult{
				Acknowledged: true,
			}, nil).Reset()
			err := checkDataSourceIndexAndCreate(data_source.Source{
				EnName:           "custom",
				HasAssetData:     false,
				HasVulData:       true,
				HasPersonnelData: true,
			})
			So(err, ShouldBeNil)
		})

		Convey("PersonIndexExistsError", func() {
			time.Sleep(time.Millisecond * 300)
			defer gomonkey.ApplyMethodReturn(es.GetEsClient().IndexExists("custom_assets"), "Do", false, errors.New("AssetsIndexExistsError")).Reset()
			err := checkDataSourceIndexAndCreate(data_source.Source{
				EnName:           "custom",
				HasAssetData:     false,
				HasVulData:       false,
				HasPersonnelData: true,
			})
			So(err, ShouldBeError)
		})
		Convey("CreatePersonIndexError", func() {
			time.Sleep(time.Millisecond * 300)
			defer gomonkey.ApplyMethodReturn(es.GetEsClient().IndexExists("custom_assets"), "Do", false, nil).Reset()
			time.Sleep(time.Millisecond * 300)
			defer gomonkey.ApplyMethodReturn(es.GetEsClient().CreateIndex("custom_assets").BodyString(esDataImportMapping.GetDataImportAssetsMapping()), "Do", nil, errors.New("CreateAssetsIndexError")).Reset()
			err := checkDataSourceIndexAndCreate(data_source.Source{
				EnName:           "custom",
				HasAssetData:     false,
				HasVulData:       false,
				HasPersonnelData: true,
			})
			So(err, ShouldBeError)
		})
		Convey("CreatePersonIndexAcknowledgedError", func() {
			time.Sleep(time.Millisecond * 300)
			defer gomonkey.ApplyMethodReturn(es.GetEsClient().IndexExists("custom_assets"), "Do", false, nil).Reset()
			time.Sleep(time.Millisecond * 300)
			defer gomonkey.ApplyMethodReturn(es.GetEsClient().CreateIndex("custom_assets").BodyString(esDataImportMapping.GetDataImportAssetsMapping()), "Do", &elastic.IndicesCreateResult{
				Acknowledged: false,
			}, nil).Reset()
			err := checkDataSourceIndexAndCreate(data_source.Source{
				EnName:           "custom",
				HasAssetData:     false,
				HasVulData:       false,
				HasPersonnelData: true,
			})
			So(err, ShouldBeError)
		})
		Convey("TaskPersonIndexExistsError", func() {
			time.Sleep(time.Millisecond * 300)
			defer gomonkey.ApplyMethodReturn(es.GetEsClient().IndexExists(), "Do", false, nil).Reset()
			time.Sleep(time.Millisecond * 300)
			defer gomonkey.ApplyMethodReturn(es.GetEsClient().CreateIndex("custom_task_assets").BodyString(esDataImportMapping.GetDataImportAssetsMapping()), "Do", &elastic.IndicesCreateResult{
				Acknowledged: true,
			}, nil).Reset()
			err := checkDataSourceIndexAndCreate(data_source.Source{
				EnName:           "custom",
				HasAssetData:     false,
				HasVulData:       false,
				HasPersonnelData: true,
			})
			So(err, ShouldBeNil)
		})
	})
}

func TestCleanRunningTask(t *testing.T) {
	// 这个测试用例验证cleanRunningTask方法的基本功能
	// 注意：这是一个集成测试，需要数据库连接

	// 测试主要验证函数调用不会崩溃
	// 实际的数据库操作测试需要在有数据库环境下进行

	// 基本的函数调用测试
	t.Run("cleanRunningTask should execute without panic", func(t *testing.T) {
		assert.NotPanics(t, func() {
			CleanRunningTask()
		})
	})

	t.Run("cleanRunningDataSyncTasks should execute without panic", func(t *testing.T) {
		assert.NotPanics(t, func() {
			cleanRunningDataSyncTasks()
		})
	})
}

// TestCleanRunningTaskIntegration 集成测试 - 需要数据库环境
func TestCleanRunningTaskIntegration(t *testing.T) {
	if testing.Short() {
		t.Skip("跳过集成测试")
	}

	// 这里可以添加更详细的集成测试
	// 包括创建测试数据，执行清理，验证结果等
	t.Run("integration test placeholder", func(t *testing.T) {
		// TODO: 添加实际的集成测试逻辑
		// 1. 创建一些执行中的任务数据
		// 2. 调用cleanRunningTask
		// 3. 验证任务状态被正确更新为失败

		// 当前只是占位测试
		assert.True(t, true)
	})
}

// TestTaskStatusConstants 测试状态常量的正确性
func TestTaskStatusConstants(t *testing.T) {
	t.Run("data sync task status constants", func(t *testing.T) {
		assert.Equal(t, 2, data_sync_task.StatusDoing)
		assert.Equal(t, 4, data_sync_task.StatusFail)
	})

	t.Run("data sync child task status constants", func(t *testing.T) {
		assert.Equal(t, 2, data_sync_child_task.StatusDoing)
		assert.Equal(t, 4, data_sync_child_task.StatusFail)
	})

	t.Run("proactive task status constants", func(t *testing.T) {
		assert.Equal(t, 4, task.SyncStatusSyncFailed)
		assert.Equal(t, 4, proactive_task_node_relations.SyncFailStatus)
	})
}
