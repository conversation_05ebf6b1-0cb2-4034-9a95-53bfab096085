package system_info

import (
	"slices"
)

func GetMenusByLicense(models []string, isDevModel bool, isTest bool) ([]string, error) {
	menus := []string{}

	// 开发环境或测试环境
	if isDevModel || isTest {
		// 开启上级菜单
		// menus = append(menus, "enable_AssetCentralizedControl", "enable_UpgradePlatform", "enable_DataCollection", "enable_DataCenter", "enable_DataAppear")
		// 不隐藏任何模块
		return menus, nil
	}

	// 授权存在级联下级模块
	if slices.Contains(models, "enable_cascade") {
		// 授权仅存在级联下级模块
		if !slices.Contains(models, "enable_cascade-admin") {
			// 开启下级菜单
			menus = append(menus, "enable_AssetCentralizedControl", "enable_SubUpgradePlatform")
			// 隐藏上级菜单
			menus = append(menus, "disable_UpgradePlatform", "disable_DataCollection", "disable_DataCenter", "disable_DataAppear")
		} else {
			// 授权同时存在级联上级模块和下级模块
			// 同时开启上下级菜单
			menus = append(menus, "enable_AssetCentralizedControl", "enable_UpgradePlatform", "enable_SubUpgradePlatform", "enable_DataCenter", "enable_DataCollection", "enable_DataAppear")
		}
	} else {
		if slices.Contains(models, "enable_cascade-admin") {
			// 授权仅存在级联上级模块
			// 开启上级菜单
			menus = append(menus, "enable_AssetCentralizedControl", "enable_UpgradePlatform", "enable_DataCollection", "enable_DataCenter", "enable_DataAppear")
			// 隐藏下级菜单
			menus = append(menus, "disable_SubUpgradePlatform")
		} else {
			// 授权不存在级联模块
			// 关闭所有级联菜单
			menus = append(menus, "disable_AssetCentralizedControl", "disable_UpgradePlatform", "disable_SubUpgradePlatform", "disable_DataCenter", "disable_DataCollection", "disable_DataAppear")
		}
	}
	// 授权存在人行业务系统
	if slices.Contains(models, "enable_renhang_businessSystem") {
		// 开启 人行业务系统菜单
		menus = append(menus, "enable_BusinessSystem")
		menus = append(menus, "enable_BusinessPortrait")
		// 隐藏 标品业务系统菜单
		menus = append(menus, "disable_Business")
		menus = append(menus, "disable_BusinessAssets")
	} else {
		// 隐藏 人行业务系统菜单
		menus = append(menus, "disable_BusinessSystem")
		menus = append(menus, "disable_BusinessPortrait")
	}

	menus = append(menus, models...)

	return menus, nil
}
