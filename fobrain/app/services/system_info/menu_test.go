package system_info

import (
	"slices"
	"testing"
)

func TestGetMenusByLicense(t *testing.T) {
	tests := []struct {
		name       string
		models     []string
		isDevModel bool
		isTest     bool
		expected   []string
	}{
		{
			name:       "开发环境",
			models:     []string{},
			isDevModel: true,
			isTest:     false,
			expected:   []string{},
		},
		{
			name:       "测试环境",
			models:     []string{},
			isDevModel: false,
			isTest:     true,
			expected:   []string{},
		},
		{
			name:       "仅级联下级模块",
			models:     []string{"enable_cascade"},
			isDevModel: false,
			isTest:     false,
			expected:   []string{"enable_AssetCentralizedControl", "enable_SubUpgradePlatform", "disable_UpgradePlatform", "disable_DataCollection", "disable_DataCenter", "disable_DataAppear"},
		},
		{
			name:       "级联上下级模块",
			models:     []string{"enable_cascade", "enable_cascade-admin"},
			isDevModel: false,
			isTest:     false,
			expected:   []string{"enable_AssetCentralizedControl", "enable_UpgradePlatform", "enable_SubUpgradePlatform", "enable_DataCollection", "enable_DataCenter", "enable_DataAppear"},
		},
		{
			name:       "仅级联上级模块",
			models:     []string{"enable_cascade-admin"},
			isDevModel: false,
			isTest:     false,
			expected:   []string{"disable_SubUpgradePlatform", "enable_AssetCentralizedControl", "enable_UpgradePlatform", "enable_DataCollection", "enable_DataCenter", "enable_DataAppear"},
		},
		{
			name:       "无级联模块",
			models:     []string{},
			isDevModel: false,
			isTest:     false,
			expected:   []string{"disable_AssetCentralizedControl", "disable_UpgradePlatform", "disable_SubUpgradePlatform", "disable_DataCenter", "disable_DataCollection", "disable_DataAppear"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := GetMenusByLicense(tt.models, tt.isDevModel, tt.isTest)
			if err != nil {
				t.Fatalf("GetMenusByLicense() error = %v", err)
			}
			for _, contain := range tt.expected {
				if !slices.Contains(result, contain) {
					t.Errorf("GetMenusByLicense() = %v, expectedContain %v", result, contain)
				}
			}
		})
	}
}
