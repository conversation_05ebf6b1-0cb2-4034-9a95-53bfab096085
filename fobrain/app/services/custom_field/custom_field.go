package custom_field

import (
	"errors"
	"fmt"
	"fobrain/initialize/mysql"
	"fobrain/models/mysql/column"
	"fobrain/models/mysql/custom_field"
	"fobrain/pkg/utils"
	"time"
)

func UpdateOrCreate(customField *custom_field.CustomField) error {
	// 检查是否有重复名称
	_, err := custom_field.NewCustomFieldModel().First(mysql.WithWhere("`display_name` = ? AND `table` = ?", customField.DisplayName, customField.Table))
	if err == nil {
		return errors.New("该名称已存在")
	}
	// 有Id，更新
	if customField.Id > 0 {
		// 获取旧字段
		oldField, err := custom_field.NewCustomFieldModel().First(mysql.WithWhere("`id` = ?", customField.Id))
		if err != nil {
			return err
		}
		if oldField.IsSystemField {
			return errors.New("不允许更新系统字段")
		}
		err = custom_field.NewCustomFieldModel().Update(&custom_field.CustomField{
			BaseModel: mysql.BaseModel{
				Id: customField.Id,
			},
			DisplayName: customField.DisplayName,
			// 不能更新字段名
			FieldName: oldField.FieldName,
			FieldType: customField.FieldType,
			// 不能更新表
			Table:         oldField.Table,
			Sort:          customField.Sort,
			IsSystemField: false,
		})
		// 在表中创建或更新列
		err = column.CreateOrUpdateColumn(customField.Table, customField.FieldName, "VARCHAR(255) DEFAULT ''")
		if err != nil {
			return err
		}
	} else {
		// 创建字段
		customField.FieldName = generateTimestampedString("custom_field")
		err = custom_field.NewCustomFieldModel().Create(customField)
		if err != nil {
			return err
		}
		// 在表中创建或更新列
		err = column.CreateOrUpdateColumn(customField.Table, customField.FieldName, "VARCHAR(255) DEFAULT ''")
		if err != nil {
			return err
		}
	}
	return nil
}

// GenerateTimestampedString 按时间戳生成列名
func generateTimestampedString(prefix string) string {
	timestamp := time.Now().Unix()
	return fmt.Sprintf("%s_%d_", prefix, timestamp) + utils.RandString(6)
}
