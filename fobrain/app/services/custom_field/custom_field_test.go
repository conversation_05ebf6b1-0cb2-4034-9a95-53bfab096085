package custom_field

import (
	"fobrain/models/mysql/column"
	"fobrain/models/mysql/custom_field"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/pkg/errors"
	"github.com/stretchr/testify/assert"
	"regexp"
	"strings"
	"testing"
	"time"
)

func TestUpdateOrCreate(t *testing.T) {
	patches := []*gomonkey.Patches{
		gomonkey.ApplyMethodReturn(&custom_field.CustomField{}, "First", &custom_field.CustomField{
			DisplayName:   "",
			FieldName:     "",
			FieldType:     "",
			Table:         "",
			Sort:          0,
			IsSystemField: false,
		}, errors.New("err")),
		gomonkey.ApplyMethodReturn(&custom_field.CustomField{}, "Create", nil),
		gomonkey.ApplyMethodReturn(&custom_field.CustomField{}, "Update", nil),
		gomonkey.ApplyFuncReturn(column.CreateOrUpdateColumn, nil),
	}
	err := UpdateOrCreate(&custom_field.CustomField{
		DisplayName:   "",
		FieldName:     "",
		FieldType:     "",
		Table:         "",
		Sort:          0,
		IsSystemField: false,
	})
	for _, patch := range patches {
		patch.Reset()
	}
	assert.Nil(t, err)
}

// TestGenerateTimestampedString 测试 generateTimestampedString 函数
func TestGenerateTimestampedString(t *testing.T) {
	tests := []struct {
		name   string
		prefix string
	}{
		{
			name:   "测试custom_field前缀",
			prefix: "custom_field",
		},
		{
			name:   "测试test前缀",
			prefix: "test",
		},
		{
			name:   "测试空前缀",
			prefix: "",
		},
		{
			name:   "测试带下划线的前缀",
			prefix: "my_custom_field",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := generateTimestampedString(tt.prefix)

			// 验证结果不为空
			assert.NotEmpty(t, result, "生成的字符串不应该为空")

			// 验证格式：prefix_timestamp_randomstring
			expectedPattern := ""
			if tt.prefix != "" {
				expectedPattern = "^" + regexp.QuoteMeta(tt.prefix) + "_\\d+_[a-zA-Z0-9]{6}$"
			} else {
				expectedPattern = "^_\\d+_[a-zA-Z0-9]{6}$"
			}

			matched, err := regexp.MatchString(expectedPattern, result)
			assert.NoError(t, err, "正则表达式匹配不应该出错")
			assert.True(t, matched, "生成的字符串格式应该符合预期: %s", result)

			// 验证前缀
			if tt.prefix != "" {
				assert.True(t, strings.HasPrefix(result, tt.prefix+"_"), "结果应该以前缀开头")
			} else {
				assert.True(t, strings.HasPrefix(result, "_"), "空前缀时结果应该以下划线开头")
			}

			// 验证时间戳在合理范围内
			parts := strings.Split(result, "_")
			assert.True(t, len(parts) >= 3, "结果应该至少包含3个部分")

			// 验证随机字符串长度为6
			randomPart := parts[len(parts)-1]
			assert.Equal(t, 6, len(randomPart), "随机字符串长度应该为6")

			// 验证随机字符串只包含字母和数字
			matched, err = regexp.MatchString("^[a-zA-Z0-9]{6}$", randomPart)
			assert.NoError(t, err, "正则表达式匹配不应该出错")
			assert.True(t, matched, "随机字符串应该只包含字母和数字")
		})
	}

	// 测试多次调用生成不同的字符串
	t.Run("多次调用生成不同字符串", func(t *testing.T) {
		result1 := generateTimestampedString("test")
		time.Sleep(time.Millisecond * 10) // 确保时间戳可能不同
		result2 := generateTimestampedString("test")
		result3 := generateTimestampedString("test")

		// 由于包含随机字符串，即使时间戳相同，结果也应该不同
		assert.NotEqual(t, result1, result2, "两次调用应该生成不同的字符串")
		assert.NotEqual(t, result1, result3, "两次调用应该生成不同的字符串")
		assert.NotEqual(t, result2, result3, "两次调用应该生成不同的字符串")
	})
}
