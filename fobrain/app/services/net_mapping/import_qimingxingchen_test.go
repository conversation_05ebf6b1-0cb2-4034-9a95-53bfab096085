package net_mapping

import (
	"bytes"
	"fobrain/models/mysql/net_mapping"
	"sync"
	"testing"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
)

// TestImportNetMappingByQiMingXingChen 测试从启明星辰配置文件导入网络映射数据
func TestImportNetMappingByQiMingXingChen(t *testing.T) {
	// 模拟启明星辰配置文件内容
	mockContent := `vs vs1 address *********** protocol tcp port 80 type http 1
	default-pool pool1
member ip ******** port 8080 pool pool1
member ip ******** port 8080 pool pool1
vs vs2 address *********** protocol tcp port 443 type https 1
	default-pool pool2
member ip ******** port 8443 pool pool2
member ip ******** port 8443 pool pool2`

	// 创建模拟文件
	mockFile := bytes.NewReader([]byte(mockContent))
	batchNo := uuid.New().String()
	fileType := "启明星辰负载均衡"
	fromArea := uint64(1)
	toArea := uint64(2)

	// 使用 gomonkey 打桩 SaveData 函数
	patches := []*gomonkey.Patches{
		gomonkey.ApplyFunc(SaveData, func(ch chan *net_mapping.NetMapping, wg *sync.WaitGroup, successCount *int64) {
			defer wg.Done()
			count := 0
			for range ch {
				count++
			}
			*successCount = int64(count)
		}),
	}

	// 调用被测试函数
	successCount, msg, err := ImportNetMappingByQiMingXingChen(mockFile, batchNo, fileType, fromArea, toArea)

	// 重置 gomonkey 打桩
	for _, patch := range patches {
		patch.Reset()
	}

	// 断言测试结果
	assert.Nil(t, err)
	assert.Equal(t, int64(4), successCount)
	assert.Equal(t, "导入成功，成功解析4条数据，新增4条数据", msg)
}

// TestImportNetMappingByQingMingXingChenConfirm 测试启明星辰配置文件导入确认
func TestImportNetMappingByQingMingXingChenConfirm(t *testing.T) {
	// 模拟启明星辰配置文件内容
	mockContent := `vs vs1 address *********** protocol tcp port 80 type http 1
	default-pool pool1
member ip ******** port 8080 pool pool1
member ip ******** port 8080 pool pool1
vs vs2 address *********** protocol tcp port 443 type https 1
	default-pool pool2
member ip ******** port 8443 pool pool2
member ip ******** port 8443 pool pool2`

	fromArea := uint64(1)
	toArea := uint64(2)

	// 调用被测试函数
	netMappings, err := ImportNetMappingByQingMingXingChenConfirm(mockContent, fromArea, toArea)

	// 断言测试结果
	assert.Nil(t, err)
	assert.Equal(t, 4, len(netMappings))

	assert.ElementsMatch(t, netMappings, []*net_mapping.NetMapping{
		{
			FromArea: fromArea,
			FromIp:   "***********",
			FromPort: "80",
			ToArea:   toArea,
			ToIp:     "********",
			ToPort:   "8080",
		},
		{
			FromArea: fromArea,
			FromIp:   "***********",
			FromPort: "80",
			ToArea:   toArea,
			ToIp:     "********",
			ToPort:   "8080",
		},
		{
			FromArea: fromArea,
			FromIp:   "***********",
			FromPort: "443",
			ToArea:   toArea,
			ToIp:     "********",
			ToPort:   "8443",
		},
		{
			FromArea: fromArea,
			FromIp:   "***********",
			FromPort: "443",
			ToArea:   toArea,
			ToIp:     "********",
			ToPort:   "8443",
		},
	})

	// // 验证第一个映射关系
	// assert.Equal(t, "***********", netMappings[0].FromIp)
	// assert.Equal(t, "80", netMappings[0].FromPort)
	// assert.Equal(t, "********", netMappings[0].ToIp)
	// assert.Equal(t, "8080", netMappings[0].ToPort)

	// // 验证第二个映射关系
	// assert.Equal(t, "***********", netMappings[1].FromIp)
	// assert.Equal(t, "80", netMappings[1].FromPort)
	// assert.Equal(t, "********", netMappings[1].ToIp)
	// assert.Equal(t, "8080", netMappings[1].ToPort)
}

// TestPrepareQimingXingChenData 测试准备启明星辰数据
func TestPrepareQimingXingChenData(t *testing.T) {
	// 模拟启明星辰配置文件内容
	mockContent := `vs vs1 address *********** protocol tcp port 80 type http 1
	default-pool pool1
member ip ******** port 8080 pool pool1
member ip ******** port 8080 pool pool1`

	// 创建模拟文件
	mockFile := bytes.NewReader([]byte(mockContent))
	batchNo := uuid.New().String()
	fileType := "启明星辰负载均衡"
	fromArea := uint64(1)
	toArea := uint64(2)

	// 创建通道和计数器
	netMappingChan := make(chan *net_mapping.NetMapping, 10)

	// 启动一个 goroutine 来消费通道中的数据
	var receivedCount int64
	var wg sync.WaitGroup
	wg.Add(1)
	go func() {
		defer wg.Done()
		for range netMappingChan {
			receivedCount++
		}
	}()

	// 调用被测试函数
	totalCount := PrepareQimingXingChenData(netMappingChan, mockFile, batchNo, fileType, fromArea, toArea)

	// 关闭通道并等待消费完成
	close(netMappingChan)
	wg.Wait()

	// 断言测试结果
	assert.Equal(t, int64(2), totalCount)
	assert.Equal(t, int64(2), receivedCount)
}
