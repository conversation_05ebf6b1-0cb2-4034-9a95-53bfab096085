package net_mapping

import (
	"fobrain/models/mysql/net_mapping"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"sync"
	"testing"
)

func TestImportNetMappingByHuaweiConfig(t *testing.T) {
	lines := []string{
		"nat server fofa大屏演示 protocol tcp global ************* 39280 inside ************ 8000 no-reverse",
		"nat server 17-K01API zone untrust protocol tcp global ************* 11300 inside ************ 3000 no-reverse",
		"aaa server 17-K01API zone untrust protocol tcp global ************* 11300 inside ************ 3000 no-reverse",
		"nat server 17-K01API zone untrust protocol tcp global ************* 113a0 inside ************ 3000 no-reverse",
		"nat server 17-K01API zone untrust protocol tcp global ************* 1130 inside ************ 3000 no-reverse",
	}
	patches := []*gomonkey.Patches{
		gomonkey.ApplyFunc(SaveData, func(ch chan *net_mapping.NetMapping, wg *sync.WaitGroup, successCount *int64) {
			defer wg.Done()
			count := 0
			for range ch {
				count++
			}
			*successCount = int64(count)
		}),
	}
	successCount, msg, err := ImportNetMappingByHuaweiConfig(uuid.New().String(), "华为防火墙配置文件", lines, uint64(1), uint64(2))
	for _, patch := range patches {
		patch.Reset()
	}
	assert.Equal(t, successCount, int64(3))
	assert.Equal(t, msg, "导入成功，成功解析3条数据，新增3条数据")
	assert.Nil(t, err)
}

func TestImportNetMappingByHuaweiConfigConfirm(t *testing.T) {
	lines := []string{
		"nat server fofa大屏演示 protocol tcp global ************* 39280 inside ************ 8000 no-reverse",
		"nat server 17-K01API zone untrust protocol tcp global ************* 11300 inside ************ 3000 no-reverse",
		"aaa server 17-K01API zone untrust protocol tcp global ************* 11300 inside ************ 3000 no-reverse",
		"nat server 17-K01API zone untrust protocol tcp global ************* 113a0 inside ************ 3000 no-reverse",
		"nat server 17-K01API zone untrust protocol tcp global ************* 1130 inside ************ 3000 no-reverse",
	}
	netMappings, err := ImportNetMappingByHuaweiConfigConfirm(lines, uint64(1), uint64(2))
	assert.Nil(t, err)
	assert.Equal(t, netMappings[0].FromIp, "*************")
	assert.Equal(t, netMappings[0].ToIp, "************")
}
