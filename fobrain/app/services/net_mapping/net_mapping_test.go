package net_mapping

import (
	request "fobrain/fobrain/app/request/net_mapping"
	"fobrain/fobrain/logs"
	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/models/mysql/net_mapping"
	"fobrain/pkg/utils/common_logs"
	"os"
	"sync"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/gin-gonic/gin"
	"github.com/likexian/gokit/assert"
	"github.com/olivere/elastic/v7"
	"go.uber.org/zap"
)

func TestSaveData(t *testing.T) {
	patches := []*gomonkey.Patches{
		gomonkey.ApplyFunc(logs.GetLogger, func() *common_logs.Logger {
			return &common_logs.Logger{
				Logger: &zap.Logger{},
				Err:    nil,
			}
		}),
		gomonkey.ApplyMethodReturn(&net_mapping.NetMapping{}, "CreateBatch", int64(1), nil),
		gomonkey.ApplyMethodReturn(&net_mapping.NetMappingAuditData{}, "CreateBatch", nil),
	}
	successCount := int64(0)
	wg := &sync.WaitGroup{}
	wg.Add(1)
	netMappingChan := make(chan *net_mapping.NetMapping, 1100)
	for i := 0; i < 1100; i++ {
		netMappingChan <- &net_mapping.NetMapping{}
	}
	close(netMappingChan)
	SaveData(netMappingChan, wg, &successCount)
	for _, patch := range patches {
		patch.Reset()
	}
}

func TestCreate(t *testing.T) {
	mockEs := testcommon.NewMockServer()
	mockEs.Register("/asset/_search", []*elastic.SearchHit{})

	defer mockEs.Close()
	params := &request.CreateNetMappingRequest{
		FromArea: 1,
		FromIp:   "***************",
		ToArea:   2,
		ToIp:     "***************",
	}
	mockDb := testcommon.InitSqlMock()
	mockDb.ExpectBegin()
	mockDb.ExpectExec("INSERT INTO `net_mappings` (`created_at`,`updated_at`,`data_source`,`batch_no`,`from_area`,`from_ip`,`from_port`,`from_domain`,`from_url`,`from_protocol`,`from_exist_alarm`,`to_area`,`to_ip`,`to_port`,`to_domain`,`to_url`,`to_protocol`,`to_exist_alarm`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)").
		WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(),
			sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg()).
		WillReturnResult(sqlmock.NewResult(1, 1))
	mockDb.ExpectCommit()
	err := Create(params)
	mockDb.Close()
	assert.Nil(t, err)
}

func TestDelete(t *testing.T) {
	params := &request.DeleteNetMappingRequest{
		Ids: []uint64{1},
	}
	mockDb := testcommon.InitSqlMock()

	mockDb.ExpectQuery("SELECT * FROM `net_mappings` WHERE id IN (?)").
		WithArgs(sqlmock.AnyArg()).
		WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))

	mockDb.ExpectBegin()
	mockDb.ExpectExec("DELETE FROM `net_mappings` WHERE `net_mappings`.`id` = ?").
		WithArgs(sqlmock.AnyArg()).
		WillReturnResult(sqlmock.NewResult(1, 1))
	mockDb.ExpectCommit()

	mockDb.ExpectBegin()
	mockDb.ExpectExec("INSERT INTO `net_mapping_audit_logs`").
		WillReturnResult(sqlmock.NewResult(1, 1))
	mockDb.ExpectCommit()

	mockDb.ExpectBegin()
	mockDb.ExpectExec("INSERT INTO `net_mapping_audit_datas`").
		WillReturnResult(sqlmock.NewResult(1, 1))
	mockDb.ExpectCommit()

	_, err := Delete(&gin.Context{}, params)
	mockDb.Close()
	assert.Nil(t, err)
}

func TestList(t *testing.T) {
	params := &request.ListNetMappingRequest{
		Keyword: "***************",
	}
	params.Page = 1
	params.PerPage = 10
	mockDb := testcommon.InitSqlMock()
	mockDb.ExpectQuery("SELECT * FROM `net_mapping_areas`").
		WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))
	mockDb.ExpectQuery("SELECT * FROM `net_mapping_areas` WHERE `name` = ? ORDER BY `net_mapping_areas`.`id` LIMIT 1").
		WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))
	mockDb.ExpectQuery("SELECT count(*) FROM `net_mappings`").
		WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(1))
	mockDb.ExpectQuery("SELECT * FROM `net_mappings` WHERE from_ip LIKE ? OR data_source LIKE ? OR  from_port LIKE ? OR from_domain LIKE ? OR from_url LIKE ? OR from_protocol LIKE ? OR to_ip LIKE ? OR to_port LIKE ? OR to_domain LIKE ? OR to_url LIKE ? OR to_protocol LIKE ? OR from_area = ? OR to_area = ? ORDER BY created_at DESC LIMIT 10").
		WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(),
			sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg()).
		WillReturnRows(sqlmock.NewRows([]string{"id"}).
			AddRow(1))
	res, total, err := List(params)
	assert.Nil(t, err)
	assert.Equal(t, total, int64(1))
	assert.NotNil(t, res)
}

func TestCheckAlarm(t *testing.T) {
	// 初始化 mock ES 客户端
	mockEs := testcommon.NewMockServer()
	mockEs.Register("/asset/_search", []*elastic.SearchHit{})
	defer mockEs.Close()

	mockDb := testcommon.InitSqlMock()
	mockDb.ExpectQuery("SELECT count(*) FROM `net_mappings`").
		WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(1))
	mockDb.ExpectQuery("SELECT * FROM `net_mappings` ORDER BY created_at DESC LIMIT 100").
		WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))
	mockDb.ExpectBegin()
	mockDb.ExpectExec("UPDATE `net_mappings` SET `batch_no`=?,`data_source`=?,`from_area`=?,`from_domain`=?,`from_exist_alarm`=?,`from_ip`=?,`from_port`=?,`from_protocol`=?,`from_url`=?,`to_area`=?,`to_domain`=?,`to_exist_alarm`=?,`to_ip`=?,`to_port`=?,`to_protocol`=?,`to_url`=? WHERE id = ?").
		WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg()).
		WillReturnResult(sqlmock.NewResult(1, 1))
	mockDb.ExpectCommit()
	err := CheckAlarm()
	assert.Nil(t, err)
}

// TestCheckIp 测试 checkIp 函数
func TestCheckIp(t *testing.T) {
	tests := []struct {
		name     string
		ips      []string
		expected bool
	}{
		{
			name:     "单个有效IP",
			ips:      []string{"***********"},
			expected: true,
		},
		{
			name:     "多个有效IP",
			ips:      []string{"***********", "********", "**********"},
			expected: true,
		},
		{
			name:     "包含无效IP",
			ips:      []string{"***********", "invalid_ip"},
			expected: false,
		},
		{
			name:     "单个无效IP",
			ips:      []string{"invalid_ip"},
			expected: false,
		},
		{
			name:     "空IP列表",
			ips:      []string{},
			expected: true,
		},
		{
			name:     "IPv6地址",
			ips:      []string{"2001:db8::1"},
			expected: true,
		},
		{
			name:     "混合IPv4和IPv6",
			ips:      []string{"***********", "2001:db8::1"},
			expected: true,
		},
		{
			name:     "边界IP地址",
			ips:      []string{"0.0.0.0", "***************"},
			expected: true,
		},
		{
			name:     "超出范围的IP",
			ips:      []string{"256.256.256.256"},
			expected: false,
		},
		{
			name:     "格式错误的IP",
			ips:      []string{"192.168.1"},
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := checkIp(tt.ips...)
			assert.Equal(t, result, tt.expected)
		})
	}
}

// TestCheckPort 测试 checkPort 函数
func TestCheckPort(t *testing.T) {
	tests := []struct {
		name     string
		ports    []string
		expected bool
	}{
		{
			name:     "单个有效端口",
			ports:    []string{"80"},
			expected: true,
		},
		{
			name:     "多个有效端口",
			ports:    []string{"80", "443", "8080"},
			expected: true,
		},
		{
			name:     "包含无效端口",
			ports:    []string{"80", "invalid_port"},
			expected: false,
		},
		{
			name:     "单个无效端口",
			ports:    []string{"invalid_port"},
			expected: false,
		},
		{
			name:     "空端口列表",
			ports:    []string{},
			expected: true,
		},
		{
			name:     "边界端口",
			ports:    []string{"1", "65535"},
			expected: true,
		},
		{
			name:     "超出范围的端口",
			ports:    []string{"65536"},
			expected: false,
		},
		{
			name:     "负数端口",
			ports:    []string{"-1"},
			expected: false,
		},
		{
			name:     "零端口",
			ports:    []string{"0"},
			expected: false,
		},
		{
			name:     "常用端口",
			ports:    []string{"22", "80", "443", "3306", "5432"},
			expected: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := checkPort(tt.ports...)
			assert.Equal(t, result, tt.expected)
		})
	}
}

// TestCreateTemplateFile 测试 CreateTemplateFile 函数
func TestCreateTemplateFile(t *testing.T) {
	tests := []struct {
		name     string
		params   *request.DownloadTemplateRequest
		validate func(t *testing.T, result string, err error)
	}{
		{
			name: "正常创建模板文件",
			params: &request.DownloadTemplateRequest{
				FromArea: "内网",
				ToArea:   "外网",
			},
			validate: func(t *testing.T, result string, err error) {
				assert.Nil(t, err)
				assert.NotEqual(t, "", result)

				// 清理生成的文件
				defer func() {
					if _, statErr := os.Stat(result); statErr == nil {
						os.Remove(result)
					}
				}()
			},
		},
		{
			name: "不同区域名称",
			params: &request.DownloadTemplateRequest{
				FromArea: "DMZ",
				ToArea:   "Internet",
			},
			validate: func(t *testing.T, result string, err error) {
				assert.Nil(t, err)
				assert.NotEqual(t, "", result)

				// 清理生成的文件
				defer func() {
					if _, statErr := os.Stat(result); statErr == nil {
						os.Remove(result)
					}
				}()
			},
		},
		{
			name: "空区域名称",
			params: &request.DownloadTemplateRequest{
				FromArea: "",
				ToArea:   "",
			},
			validate: func(t *testing.T, result string, err error) {
				assert.Nil(t, err)
				assert.NotEqual(t, "", result)

				// 清理生成的文件
				defer func() {
					if _, statErr := os.Stat(result); statErr == nil {
						os.Remove(result)
					}
				}()
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := CreateTemplateFile(tt.params)
			tt.validate(t, result, err)
		})
	}
}
