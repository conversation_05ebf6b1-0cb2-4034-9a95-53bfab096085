package net_mapping

import (
	"fmt"
	"fobrain/models/mysql/net_mapping"
	"regexp"
	"strings"
	"sync"
)

func ImportNetMappingByHuaweiFWConfig(batchNo string, fileType string, lines []string, fromArea, toArea uint64) (int64, string, error) {
	successCount := int64(0)
	totalCount := int64(0)
	wg := &sync.WaitGroup{}
	netMappingChan := make(chan *net_mapping.NetMapping, 100)

	wg.Add(1)
	go SaveData(netMappingChan, wg, &successCount)

	for _, line := range lines {
		if !strings.HasPrefix(line, "nat server") {
			continue
		}
		keyType, matches := getHuaWeiValueByRegexp(line)
		if keyType == "IncludePort" && len(matches) == 5 {
			netMapping := &net_mapping.NetMapping{
				DataSource: fileType,
				BatchNo:    batchNo,
				FromArea:   fromArea,
				FromIp:     matches[1],
				FromPort:   matches[2],
				ToArea:     toArea,
				ToIp:       matches[3],
				ToPort:     matches[4],
			}
			if !checkIp(matches[1], matches[3]) || !checkPort(matches[2], matches[4]) {
				continue
			}
			netMappingChan <- netMapping
			totalCount++
		} else if keyType == "NotIncludePort" && len(matches) == 3 {
			netMapping := &net_mapping.NetMapping{
				DataSource: fileType,
				BatchNo:    batchNo,
				FromArea:   fromArea,
				FromIp:     matches[1],
				ToArea:     toArea,
				ToIp:       matches[2],
			}
			if !checkIp(matches[1], matches[2]) {
				continue
			}
			netMappingChan <- netMapping
			totalCount++
		}
	}

	close(netMappingChan)
	wg.Wait()
	return successCount, fmt.Sprintf("导入成功，成功解析%d条数据，新增%d条数据", totalCount, successCount), nil
}

func ImportNetMappingByHuaweiFWConfigConfirm(lines []string, fromArea, toArea uint64) ([]*net_mapping.NetMapping, error) {
	netMappings := make([]*net_mapping.NetMapping, 0)
	for _, line := range lines {
		if !strings.HasPrefix(line, "nat server") {
			continue
		}
		keyType, matches := getHuaWeiValueByRegexp(line)
		if keyType == "IncludePort" && len(matches) == 5 {
			netMapping := &net_mapping.NetMapping{
				FromArea: fromArea,
				FromIp:   matches[1],
				FromPort: matches[2],
				ToArea:   toArea,
				ToIp:     matches[3],
				ToPort:   matches[4],
			}
			if !checkIp(matches[1], matches[3]) || !checkPort(matches[2], matches[4]) {
				continue
			}
			netMappings = append(netMappings, netMapping)
			if len(netMappings) > 4 {
				break
			}
		} else if keyType == "NotIncludePort" && len(matches) == 3 {
			netMapping := &net_mapping.NetMapping{
				FromArea: fromArea,
				FromIp:   matches[1],
				ToArea:   toArea,
				ToIp:     matches[2],
			}
			if !checkIp(matches[1], matches[2]) {
				continue
			}
			netMappings = append(netMappings, netMapping)
			if len(netMappings) > 4 {
				break
			}
		}
	}
	return netMappings, nil
}

func getHuaWeiValueByRegexp(line string) (string, []string) {
	if regexp.MustCompile(`\bnat-disable\b`).MatchString(line) {
		return "", nil
	}
	expressions := map[string]*regexp.Regexp{
		"IncludePort":    regexp.MustCompile(`global\s+([\d.]+)\s+(\d+)\s+inside\s+([\d.]+)\s+(\d+)`),
		"NotIncludePort": regexp.MustCompile(`global\s+([\d.]+)\s+inside\s+([\d.]+)`),
	}
	for name, expr := range expressions {
		matches := expr.FindStringSubmatch(line)
		if matches != nil {
			return name, matches
		}
	}
	return "", nil
}
