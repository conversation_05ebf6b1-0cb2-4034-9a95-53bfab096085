package net_mapping

import (
	"context"
	"encoding/json"
	"errors"
	request "fobrain/fobrain/app/request/net_mapping"
	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/models/elastic/assets"
	"fobrain/models/mysql/net_mapping"
	"github.com/DATA-DOG/go-sqlmock"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/olivere/elastic/v7"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestMappingGplot_Include(t *testing.T) {
	netMapping1 := &net_mapping.NetMapping{
		FromIp:   "************",
		FromPort: "8080",
		ToIp:     "***********",
		ToPort:   "8080",
	}
	netMapping2 := &net_mapping.NetMapping{
		FromIp:   "************",
		FromPort: "8080",
		ToIp:     "***********",
		ToPort:   "8080",
	}
	netMapping3 := &net_mapping.NetMapping{
		ToIp:     "************",
		ToPort:   "8080",
		FromIp:   "***********",
		FromPort: "8080",
	}
	mappingGplot := MappingGplot([]*net_mapping.NetMapping{netMapping1})
	includeMapping := mappingGplot.Include(netMapping1)
	noIncludeMapping := mappingGplot.Include(netMapping2)
	reMapping := mappingGplot.Include(netMapping3)
	assert.Equal(t, includeMapping, true)
	assert.Equal(t, noIncludeMapping, false)
	assert.Equal(t, reMapping, true)
}

func TestMappingGplot_Add(t *testing.T) {
	netMapping1 := &net_mapping.NetMapping{
		FromIp:   "************",
		FromPort: "8080",
		ToIp:     "***********",
		ToPort:   "8080",
	}
	netMapping2 := &net_mapping.NetMapping{
		FromIp:   "************",
		FromPort: "8080",
		ToIp:     "***********",
		ToPort:   "8080",
	}
	netMapping3 := &net_mapping.NetMapping{
		ToIp:     "************",
		ToPort:   "8080",
		FromIp:   "***********",
		FromPort: "8080",
	}
	mappingGplot := MappingGplot([]*net_mapping.NetMapping{netMapping1})
	addSuccess := mappingGplot.Add(netMapping2)
	addFail := mappingGplot.Add(netMapping3)
	assert.Equal(t, addSuccess, true)
	assert.Equal(t, addFail, false)
}

func TestRecordNodeMsg(t *testing.T) {
	// 初始化数据库mock
	mockDb := testcommon.InitSqlMock()
	defer mockDb.Close()
	mockDb.ExpectQuery("SELECT * FROM `net_mapping_areas`").
		WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).AddRow(1, "test").AddRow(2, "test2"))

	// 初始化Elasticsearch mock
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	// Mock asset search查询 - getIPs函数会调用这个
	mockServer.Register("asset/_search", elastic.SearchResult{
		TookInMillis: 10,
		TimedOut:     false,
		Shards: &elastic.ShardsInfo{
			Total:      5,
			Successful: 5,
			Skipped:    0,
			Failed:     0,
		},
		Hits: &elastic.SearchHits{
			TotalHits: &elastic.TotalHits{
				Value:    100,
				Relation: "eq",
			},
		},
		Aggregations: elastic.Aggregations{
			"ip_agg": json.RawMessage(`{
				"doc_count_error_upper_bound": 0,
				"sum_other_doc_count": 0,
				"buckets": [
					{"key": "************", "doc_count": 10, "poc_sum": {"value": 5}},
					{"key": "***********", "doc_count": 15, "poc_sum": {"value": 3}}
				]
			}`),
		},
	})

	mapping := &net_mapping.NetMapping{
		FromArea: uint64(1),
		FromIp:   "************",
		FromPort: "8080",
		ToArea:   uint64(2),
		ToIp:     "***********",
		ToPort:   "8080",
	}
	list := make([]map[string]string, 0)
	savedIp := make([]string, 0)
	searchType := "ip_port"

	list, savedIp = recordNodeMsg(mapping, list, savedIp, searchType)

	assert.Equal(t, len(list), 2)
	assert.Equal(t, list[0]["name"], "************:8080")
	assert.Equal(t, list[0]["ip_kind"], "test")
	assert.Equal(t, list[0]["vuln_count"], "5")
	assert.Equal(t, savedIp[0], "************:8080")
	assert.Equal(t, list[1]["name"], "***********:8080")
	assert.Equal(t, list[1]["ip_kind"], "test2")
	assert.Equal(t, list[1]["vuln_count"], "3")
	assert.Equal(t, savedIp[1], "***********:8080")
}
func TestRecordNodeMsgErr(t *testing.T) {
	// 初始化数据库mock
	mockDb := testcommon.InitSqlMock()
	defer mockDb.Close()
	mockDb.ExpectQuery("SELECT * FROM `net_mapping_areas`").
		WillReturnError(errors.New("err"))

	// 初始化Elasticsearch mock（虽然不会被调用到，但为了保险起见）
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	mapping := &net_mapping.NetMapping{
		FromArea: uint64(1),
		FromIp:   "************",
		FromPort: "8080",
		ToArea:   uint64(2),
		ToIp:     "***********",
		ToPort:   "8080",
	}
	list := make([]map[string]string, 0)
	savedIp := make([]string, 0)
	searchType := "ip_port"

	list, savedIp = recordNodeMsg(mapping, list, savedIp, searchType)

	assert.Nil(t, list)
	assert.Nil(t, savedIp)
}
func TestGetRootMappingListByIp(t *testing.T) {
	keyword := "***********"
	size := 1
	searchType := "ip"
	mockDb := testcommon.InitSqlMock()
	mockDb.ExpectQuery("SELECT * FROM `net_mappings` WHERE from_ip = ? OR to_ip = ? ORDER BY created_at DESC").
		WithArgs(keyword, keyword).
		WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1).AddRow(2))
	_, err := getRootMappingList(keyword, size, searchType)
	mockDb.Close()
	assert.Nil(t, err)
}
func TestGetRootMappingListByIpport(t *testing.T) {
	keyword := "***********:8080"
	size := 1
	searchType := "ip_port"
	mockDb := testcommon.InitSqlMock()
	mockDb.ExpectQuery("SELECT * FROM `net_mappings` WHERE (from_ip = ? AND from_port = ?) OR (to_ip = ? AND to_port = ?) ORDER BY created_at DESC").
		WithArgs("***********", "8080", "***********", "8080").
		WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1).AddRow(2))
	_, err := getRootMappingList(keyword, size, searchType)
	mockDb.Close()
	assert.Nil(t, err)
}
func TestGetRootMappingListDomain(t *testing.T) {
	keyword := "***********"
	size := 1
	searchType := "domain"
	mockDb := testcommon.InitSqlMock()
	mockDb.ExpectQuery("SELECT * FROM `net_mappings` WHERE from_domain = ? OR to_domain = ? ORDER BY created_at DESC").
		WithArgs(keyword, keyword).
		WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1).AddRow(2))
	_, err := getRootMappingList(keyword, size, searchType)
	mockDb.Close()
	assert.Nil(t, err)
}
func TestBuildMappingGplot(t *testing.T) {
	mockDb := testcommon.InitSqlMock()

	mockDb.ExpectQuery("SELECT * FROM `net_mappings` WHERE from_ip = ? OR to_ip = ? ORDER BY created_at DESC").
		WithArgs("************", "************").
		WillReturnRows(sqlmock.NewRows([]string{"from_ip", "from_port", "to_ip", "to_port"}).
			AddRow("************", "8080", "***********", ""))

	mockDb.ExpectQuery("SELECT * FROM `net_mappings` WHERE (from_ip = ? AND from_port = ?) OR (to_ip = ? AND to_port = ?) ORDER BY created_at DESC").
		WithArgs("************", "8080", "************", "8080").
		WillReturnRows(sqlmock.NewRows([]string{"from_ip", "from_port", "to_ip", "to_port"}).
			AddRow("************", "8080", "***********", ""))

	mockDb.ExpectQuery("SELECT * FROM `net_mapping_areas`").
		WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).AddRow(1, "test"))

	mockDb.ExpectQuery("SELECT * FROM `net_mappings` WHERE from_ip = ? OR to_ip = ? ORDER BY created_at DESC").
		WithArgs("***********", "***********").
		WillReturnRows(sqlmock.NewRows([]string{"from_ip", "from_port", "to_ip", "to_port"}).
			AddRow("************", "8080", "***********", ""))

	mappingGplot, list, err := buildMappingGplot("************", 10, "ip")
	mockDb.Close()
	assert.Nil(t, err)
	assert.NotNil(t, mappingGplot)
	assert.NotNil(t, list)
}
func TestGetMappingGplot(t *testing.T) {
	params := &request.GetMappingGplotRequest{
		Size:       10,
		Keyword:    "************:8080",
		SearchType: "ip_port",
	}
	mockDb := testcommon.InitSqlMock()
	mockDb.ExpectQuery("SELECT * FROM `net_mapping_areas`").
		WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).AddRow(1, "test"))

	mockDb.ExpectQuery("SELECT * FROM `net_mappings` WHERE (from_ip = ? AND from_port = ?) OR (to_ip = ? AND to_port = ?) ORDER BY created_at DESC").
		WithArgs("************", "8080", "************", "8080").
		WillReturnRows(sqlmock.NewRows([]string{"from_ip", "from_port", "to_ip", "to_port"}).
			AddRow("************", "8080", "***********", ""))

	mockDb.ExpectQuery("SELECT * FROM `net_mappings` WHERE (from_ip = ? AND from_port = ?) OR (to_ip = ? AND to_port = ?) ORDER BY created_at DESC").
		WithArgs("************", "8080", "************", "8080").
		WillReturnRows(sqlmock.NewRows([]string{"from_ip", "from_port", "to_ip", "to_port"}).
			AddRow("************", "8080", "***********", ""))

	mockDb.ExpectQuery("SELECT * FROM `net_mapping_areas`").
		WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).AddRow(1, "test"))

	mockDb.ExpectQuery("net_mappings` WHERE from_ip = ? OR to_ip = ? ORDER BY created_at DESC").
		WithArgs("***********", "***********").
		WillReturnRows(sqlmock.NewRows([]string{"from_ip", "from_port", "to_ip", "to_port"}).
			AddRow("************", "8080", "***********", ""))

	res, err := GetMappingGplot(params)
	mockDb.Close()
	assert.Nil(t, err)
	assert.NotNil(t, res)
}
func TestGetMappingAssets(t *testing.T) {
	params := &request.GetMappingAssetsRequest{
		Size:    0,
		Keyword: "**************",
	}
	netMapping1 := &net_mapping.NetMapping{
		FromIp:   "************",
		FromPort: "8080",
		ToIp:     "***********",
		ToPort:   "8080",
	}
	netMapping2 := &net_mapping.NetMapping{
		FromIp:   "************",
		FromPort: "8080",
		ToIp:     "***********",
		ToPort:   "8080",
	}
	mappingGplot := MappingGplot([]*net_mapping.NetMapping{netMapping1, netMapping2})

	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()
	mockServer.Register("asset/_search", []*elastic.SearchHit{
		{
			Id:     "1",
			Source: []byte(`{"ip":"***********", "updated_at":"2023-04-25 12:34:56", "created_at":"2023-04-25 12:34:56"}`),
		},
	})

	// 使用gomonkey来mock HandleNetMapping函数，避免复杂的数据库mock
	patchBuildMapping := gomonkey.ApplyFuncReturn(buildMappingGplot, mappingGplot, nil, nil)
	defer patchBuildMapping.Reset()

	res1, total1, err1 := GetMappingAssets(params)
	params.Keyword = "**************:8080"
	res2, total2, err2 := GetMappingAssets(params)

	assert.Nil(t, err1)
	assert.NotNil(t, res1)
	assert.Equal(t, int64(1), total1)

	assert.Nil(t, err2)
	assert.NotNil(t, res2)
	assert.Equal(t, int64(1), total2)
}
func TestGetAssetBaseInfo(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()
	mockServer.Register("asset/_search", []*elastic.SearchHit{
		{
			Id:     "1",
			Source: []byte(`{"ip":"***********", "updated_at":"2023-04-25 12:34:56", "created_at":"2023-04-25 12:34:56"}`),
		},
	})
	res1, err1 := getAssetBaseInfo("*************:80")
	assert.Nil(t, err1)
	assert.NotNil(t, res1)

	res2, err2 := getAssetBaseInfo("************")
	assert.Nil(t, err2)
	assert.NotNil(t, res2)
}
func TestGetAssetInfo(t *testing.T) {
	params := &request.GetMappingBaseInfoRequest{
		Keyword: "**************:8080",
	}
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()
	mockServer.Register("asset/_search", []*elastic.SearchHit{
		{
			Id:     "1",
			Source: []byte(`{"ip":"***********", "updated_at":"2023-04-25 12:34:56", "created_at":"2023-04-25 12:34:56","business":[{"system":"a", "owner": "a", "department":[]}],"ports":[{"port":80}],"oper":["a"],"oper_department":[{}]}`),
		},
	})
	mockServer.Register("business_systems/_search", []*elastic.SearchHit{})
	mockDb := testcommon.InitSqlMock()
	defer mockDb.Close()
	mockDb.ExpectQuery("SELECT `name` FROM `network_areas` WHERE id = ?").
		WithArgs(sqlmock.AnyArg()).
		WillReturnRows(sqlmock.NewRows([]string{"id"}).
			AddRow(1))
	mockDb.ExpectQuery("SELECT * FROM `data_sources` WHERE id in (?)").
		WithArgs(sqlmock.AnyArg()).
		WillReturnRows(sqlmock.NewRows([]string{"id"}).
			AddRow(1))
	res, err := GetMappingBaseInfo(params)
	assert.Nil(t, err)
	assert.NotNil(t, res)
}

// TestGetBusinessSystemIPs_EmptyBusinessName 测试空业务系统名称
func TestGetBusinessSystemIPs_EmptyBusinessName(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	mockResponse := elastic.SearchResult{
		TookInMillis: 5,
		TimedOut:     false,
		Shards: &elastic.ShardsInfo{
			Total:      5,
			Successful: 5,
			Skipped:    0,
			Failed:     0,
		},
		Hits: &elastic.SearchHits{
			TotalHits: &elastic.TotalHits{
				Value:    0,
				Relation: "eq",
			},
		},
		Aggregations: elastic.Aggregations{},
	}
	expectedIPs := map[string]int64{}

	// 注册mock响应
	mockServer.Register(assets.NewAssets().IndexName()+"/_search", mockResponse)

	// 调用被测试的函数
	result, err := getIPs("", Business)

	// 验证结果
	assert.NoError(t, err, "空业务系统名称应该返回空列表")
	assert.Equal(t, expectedIPs, result, "空业务系统名称应该返回空列表")
}

// TestGetBusinessSystemIPs_NormalCase 测试正常获取业务系统IP列表
func TestGetBusinessSystemIPs_NormalCase(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	mockResponse := elastic.SearchResult{
		TookInMillis: 10,
		TimedOut:     false,
		Shards: &elastic.ShardsInfo{
			Total:      5,
			Successful: 5,
			Skipped:    0,
			Failed:     0,
		},
		Hits: &elastic.SearchHits{
			TotalHits: &elastic.TotalHits{
				Value:    100,
				Relation: "eq",
			},
		},
		Aggregations: elastic.Aggregations{
			"ip_agg": json.RawMessage(`{
				"doc_count_error_upper_bound": 0,
				"sum_other_doc_count": 0,
				"buckets": [
					{"key": "***********", "doc_count": 10, "poc_sum": {"value": 5}},
					{"key": "***********", "doc_count": 15, "poc_sum": {"value": 3}},
					{"key": "********", "doc_count": 5, "poc_sum": {"value": 0}}
				]
			}`),
		},
	}
	expectedIPs := map[string]int64{"***********": 5, "***********": 3, "********": 0}

	// 注册mock响应
	mockServer.Register(assets.NewAssets().IndexName()+"/_search", mockResponse)

	// 调用被测试的函数
	result, err := getIPs("test-business", Business)

	// 验证结果
	assert.NoError(t, err, "应该正确返回业务系统的IP列表")
	assert.Equal(t, expectedIPs, result, "应该正确返回业务系统的IP列表")
}

// TestGetBusinessSystemIPs_EmptyBuckets 测试聚合结果为空buckets
func TestGetBusinessSystemIPs_EmptyBuckets(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	mockResponse := elastic.SearchResult{
		TookInMillis: 5,
		TimedOut:     false,
		Shards: &elastic.ShardsInfo{
			Total:      5,
			Successful: 5,
			Skipped:    0,
			Failed:     0,
		},
		Hits: &elastic.SearchHits{
			TotalHits: &elastic.TotalHits{
				Value:    0,
				Relation: "eq",
			},
		},
		Aggregations: elastic.Aggregations{
			"ip_agg": json.RawMessage(`{
				"doc_count_error_upper_bound": 0,
				"sum_other_doc_count": 0,
				"buckets": []
			}`),
		},
	}
	expectedIPs := map[string]int64{}

	// 注册mock响应
	mockServer.Register(assets.NewAssets().IndexName()+"/_search", mockResponse)

	// 调用被测试的函数
	result, err := getIPs("empty-buckets-business", Business)

	// 验证结果
	assert.NoError(t, err, "当聚合结果buckets为空时，应该返回空的IP列表")
	assert.Equal(t, expectedIPs, result, "当聚合结果buckets为空时，应该返回空的IP列表")
}

// TestGetBusinessSystemIPs_NoAggregation 测试无聚合结果
func TestGetBusinessSystemIPs_NoAggregation(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	mockResponse := elastic.SearchResult{
		TookInMillis: 5,
		TimedOut:     false,
		Shards: &elastic.ShardsInfo{
			Total:      5,
			Successful: 5,
			Skipped:    0,
			Failed:     0,
		},
		Hits: &elastic.SearchHits{
			TotalHits: &elastic.TotalHits{
				Value:    0,
				Relation: "eq",
			},
		},
		Aggregations: elastic.Aggregations{},
	}
	expectedIPs := map[string]int64{}

	// 注册mock响应
	mockServer.Register(assets.NewAssets().IndexName()+"/_search", mockResponse)

	// 调用被测试的函数
	result, err := getIPs("empty-business", Business)

	// 验证结果
	assert.NoError(t, err, "当没有聚合结果时，应该返回空的IP列表")
	assert.Equal(t, expectedIPs, result, "当没有聚合结果时，应该返回空的IP列表")
}

// TestGetBusinessSystemIPs_ElasticsearchError 测试Elasticsearch查询错误
func TestGetBusinessSystemIPs_ElasticsearchError(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	mockResponse := &elastic.Error{
		Details: &elastic.ErrorDetails{
			Type:   "search_phase_execution_exception",
			Reason: "all shards failed",
		},
		Status: 500,
	}

	// 注册mock响应
	mockServer.Register(assets.NewAssets().IndexName()+"/_search", mockResponse)

	// 调用被测试的函数
	result, err := getIPs("error-business", Business)

	// 验证结果
	assert.Error(t, err, "当Elasticsearch查询失败时，应该返回错误")
	assert.Nil(t, result, "当Elasticsearch查询失败时，应该返回错误")
}

// TestBuildBaseQuery_IP 测试IP查询
func TestBuildBaseQuery_IP(t *testing.T) {
	// 调用被测试的函数
	query, err := buildBaseQuery("***********", Ip)

	// 验证结果
	assert.NoError(t, err, "应该正确构建IP查询")
	assert.NotNil(t, query, "应该正确构建IP查询")
}

// TestBuildBaseQuery_IpPort 测试IP:端口查询
func TestBuildBaseQuery_IpPort(t *testing.T) {
	// 调用被测试的函数
	query, err := buildBaseQuery("***********:8080", IpPort)

	// 验证结果
	assert.NoError(t, err, "应该正确构建IP:端口查询")
	assert.NotNil(t, query, "应该正确构建IP:端口查询")
}

// TestBuildBaseQuery_Domain 测试域名查询
func TestBuildBaseQuery_Domain(t *testing.T) {
	// 调用被测试的函数
	query, err := buildBaseQuery("example.com", Domain)

	// 验证结果
	assert.NoError(t, err, "应该正确构建域名查询")
	assert.NotNil(t, query, "应该正确构建域名查询")
}

// TestBuildBaseQuery_Business 测试业务系统查询
func TestBuildBaseQuery_Business(t *testing.T) {
	// 调用被测试的函数
	query, err := buildBaseQuery("test-business", Business)

	// 验证结果
	assert.NoError(t, err, "应该正确构建业务系统查询")
	assert.NotNil(t, query, "应该正确构建业务系统查询")
}

// TestBuildBaseQuery_IpPortFormatError 测试IP:端口格式错误
func TestBuildBaseQuery_IpPortFormatError(t *testing.T) {
	// 调用被测试的函数
	query, err := buildBaseQuery("***********", IpPort)

	// 验证结果
	assert.Error(t, err, "IP:端口格式错误时应该返回错误")
	assert.Nil(t, query, "IP:端口格式错误时应该返回错误")
}

// TestBuildBaseQuery_UnknownType 测试未知查询类型
func TestBuildBaseQuery_UnknownType(t *testing.T) {
	// 调用被测试的函数
	query, err := buildBaseQuery("test", "unknown")

	// 验证结果
	assert.Error(t, err, "未知查询类型应该返回错误")
	assert.Nil(t, query, "未知查询类型应该返回错误")
}

// TestGetMappingByBusiness_Normal 测试正常获取业务系统拓扑图
func TestGetMappingByBusiness_Normal(t *testing.T) {
	// 初始化mock
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()
	mockDb := testcommon.InitSqlMock()
	defer mockDb.Close()

	// Mock Elasticsearch response for getIPs
	mockServer.Register(assets.NewAssets().IndexName()+"/_search", elastic.SearchResult{
		TookInMillis: 10,
		TimedOut:     false,
		Shards: &elastic.ShardsInfo{
			Total:      5,
			Successful: 5,
			Skipped:    0,
			Failed:     0,
		},
		Hits: &elastic.SearchHits{
			TotalHits: &elastic.TotalHits{
				Value:    100,
				Relation: "eq",
			},
		},
		Aggregations: elastic.Aggregations{
			"ip_agg": json.RawMessage(`{
				"doc_count_error_upper_bound": 0,
				"sum_other_doc_count": 0,
				"buckets": [
					{"key": "***********", "doc_count": 10, "poc_sum": {"value": 5}},
					{"key": "***********", "doc_count": 15, "poc_sum": {"value": 3}}
				]
			}`),
		},
	})

	// Mock database queries for buildMappingGplotFromIPs
	// 第一次查询 ***********
	mockDb.ExpectQuery("SELECT * FROM `net_mappings` WHERE from_ip = ? OR to_ip = ? ORDER BY created_at DESC").
		WithArgs("***********", "***********").
		WillReturnRows(sqlmock.NewRows([]string{"id", "from_ip", "from_port", "to_ip", "to_port", "from_area", "to_area"}).
			AddRow(1, "***********", "", "***********", "", 1, 2))

	// 第二次查询 ***********
	mockDb.ExpectQuery("SELECT * FROM `net_mappings` WHERE from_ip = ? OR to_ip = ? ORDER BY created_at DESC").
		WithArgs("***********", "***********").
		WillReturnRows(sqlmock.NewRows([]string{"id", "from_ip", "from_port", "to_ip", "to_port", "from_area", "to_area"}))

	// Mock area mapping - 这个查询会被调用多次，所以我们需要设置多次期望
	mockDb.ExpectQuery("SELECT * FROM `net_mapping_areas`").
		WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).
			AddRow(1, "内网区域").
			AddRow(2, "DMZ区域"))

	mockDb.ExpectQuery("SELECT * FROM `net_mapping_areas`").
		WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).
			AddRow(1, "内网区域").
			AddRow(2, "DMZ区域"))

	// 构建请求参数
	params := &request.GetMappingByBusinessRequest{
		BusinessName: "test-business",
		Size:         10,
	}

	// 调用被测试的函数
	result, err := GetMappingByBusiness(params)

	// 验证结果
	assert.NoError(t, err, "应该正确构建业务系统拓扑图")
	assert.NotNil(t, result, "应该正确构建业务系统拓扑图")
	if result != nil {
		assert.Equal(t, 2, len(result.Links), "连接数应该匹配: 应该正确构建业务系统拓扑图")
		assert.Equal(t, 3, len(result.Data), "节点数应该匹配: 应该正确构建业务系统拓扑图")
	}
}

// TestGetMappingByBusiness_EmptyAssets 测试业务系统无资产
func TestGetMappingByBusiness_EmptyAssets(t *testing.T) {
	// 初始化mock
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()
	mockDb := testcommon.InitSqlMock()
	defer mockDb.Close()

	// Mock empty Elasticsearch response
	mockServer.Register(assets.NewAssets().IndexName()+"/_search", elastic.SearchResult{
		TookInMillis: 5,
		TimedOut:     false,
		Shards: &elastic.ShardsInfo{
			Total:      5,
			Successful: 5,
			Skipped:    0,
			Failed:     0,
		},
		Hits: &elastic.SearchHits{
			TotalHits: &elastic.TotalHits{
				Value:    0,
				Relation: "eq",
			},
		},
		Aggregations: elastic.Aggregations{},
	})

	// 构建请求参数
	params := &request.GetMappingByBusinessRequest{
		BusinessName: "empty-business",
		Size:         10,
	}

	// 调用被测试的函数
	result, err := GetMappingByBusiness(params)

	// 验证结果
	assert.NoError(t, err, "当业务系统无资产时，应该返回空的拓扑图")
	assert.NotNil(t, result, "当业务系统无资产时，应该返回空的拓扑图")
	if result != nil {
		assert.Equal(t, 0, len(result.Links), "连接数应该匹配: 当业务系统无资产时，应该返回空的拓扑图")
		assert.Equal(t, 0, len(result.Data), "节点数应该匹配: 当业务系统无资产时，应该返回空的拓扑图")
	}
}

// TestRecordNode_NewNodes 测试记录新节点
func TestRecordNode_NewNodes(t *testing.T) {
	mapping := &net_mapping.NetMapping{
		FromIp: "***********",
		ToIp:   "***********",
	}
	initialList := []map[string]string{}
	initialSaved := map[string]struct{}{}
	expectedList := []map[string]string{
		{"ip": "***********"},
		{"ip": "***********"},
	}
	expectedSaved := map[string]struct{}{
		"***********": {},
		"***********": {},
	}

	// 调用被测试的函数
	resultList, resultSaved := recordNode(mapping, initialList, initialSaved)

	// 验证结果
	assert.Equal(t, expectedList, resultList, "应该记录新的源IP和目标IP节点")
	assert.Equal(t, expectedSaved, resultSaved, "应该记录新的源IP和目标IP节点")
}

// TestRecordNode_SkipExisting 测试跳过已存在的节点
func TestRecordNode_SkipExisting(t *testing.T) {
	mapping := &net_mapping.NetMapping{
		FromIp: "***********",
		ToIp:   "***********",
	}
	initialList := []map[string]string{
		{"ip": "***********"},
	}
	initialSaved := map[string]struct{}{
		"***********": {},
	}
	expectedList := []map[string]string{
		{"ip": "***********"},
		{"ip": "***********"},
	}
	expectedSaved := map[string]struct{}{
		"***********": {},
		"***********": {},
	}

	// 调用被测试的函数
	resultList, resultSaved := recordNode(mapping, initialList, initialSaved)

	// 验证结果
	assert.Equal(t, expectedList, resultList, "应该跳过已存在的节点，只添加新节点")
	assert.Equal(t, expectedSaved, resultSaved, "应该跳过已存在的节点，只添加新节点")
}

// TestRecordNode_SameSourceAndTarget 测试相同源IP和目标IP
func TestRecordNode_SameSourceAndTarget(t *testing.T) {
	mapping := &net_mapping.NetMapping{
		FromIp: "***********",
		ToIp:   "***********",
	}
	initialList := []map[string]string{}
	initialSaved := map[string]struct{}{}
	expectedList := []map[string]string{
		{"ip": "***********"},
	}
	expectedSaved := map[string]struct{}{
		"***********": {},
	}

	// 调用被测试的函数
	resultList, resultSaved := recordNode(mapping, initialList, initialSaved)

	// 验证结果
	assert.Equal(t, expectedList, resultList, "当源IP和目标IP相同时，应该只记录一次")
	assert.Equal(t, expectedSaved, resultSaved, "当源IP和目标IP相同时，应该只记录一次")
}

// TestGetNetMap_IPWithoutPort 测试获取IP映射（无端口）
func TestGetNetMap_IPWithoutPort(t *testing.T) {
	// 初始化数据库mock
	mockDb := testcommon.InitSqlMock()
	defer mockDb.Close()

	mockDb.ExpectQuery("SELECT * FROM `net_mappings` WHERE from_ip = ? OR to_ip = ? ORDER BY created_at DESC").
		WithArgs("***********", "***********").
		WillReturnRows(sqlmock.NewRows([]string{"id", "from_ip", "to_ip", "from_port", "to_port"}).
			AddRow(1, "***********", "***********", "", "").
			AddRow(2, "***********", "***********", "", ""))

	// 调用被测试的函数
	result, err := GetNetMap("***********", "")

	// 验证结果
	assert.NoError(t, err, "应该正确查询IP的网络映射关系")
	assert.NotNil(t, result, "应该正确查询IP的网络映射关系")
	assert.Equal(t, 2, len(result), "应该正确查询IP的网络映射关系")
}

// TestGetNetMap_IPWithPort 测试获取IP:端口映射
func TestGetNetMap_IPWithPort(t *testing.T) {
	// 初始化数据库mock
	mockDb := testcommon.InitSqlMock()
	defer mockDb.Close()

	mockDb.ExpectQuery("SELECT * FROM `net_mappings` WHERE (from_ip = ? AND from_port = ?) OR (to_ip = ? AND to_port = ?) ORDER BY created_at DESC").
		WithArgs("***********", "8080", "***********", "8080").
		WillReturnRows(sqlmock.NewRows([]string{"id", "from_ip", "from_port", "to_ip", "to_port"}).
			AddRow(1, "***********", "8080", "***********", "80").
			AddRow(2, "***********", "443", "***********", "8080"))

	// 调用被测试的函数
	result, err := GetNetMap("***********", "8080")

	// 验证结果
	assert.NoError(t, err, "应该正确查询IP:端口的网络映射关系")
	assert.NotNil(t, result, "应该正确查询IP:端口的网络映射关系")
	assert.Equal(t, 2, len(result), "应该正确查询IP:端口的网络映射关系")
}

// TestGetNetMap_NoResults 测试查询无结果
func TestGetNetMap_NoResults(t *testing.T) {
	// 初始化数据库mock
	mockDb := testcommon.InitSqlMock()
	defer mockDb.Close()

	mockDb.ExpectQuery("SELECT * FROM `net_mappings` WHERE from_ip = ? OR to_ip = ? ORDER BY created_at DESC").
		WithArgs("***********00", "***********00").
		WillReturnRows(sqlmock.NewRows([]string{"id", "from_ip", "to_ip", "from_port", "to_port"}))

	// 调用被测试的函数
	result, err := GetNetMap("***********00", "")

	// 验证结果
	assert.NoError(t, err, "当没有匹配的映射关系时，应该返回空列表")
	assert.NotNil(t, result, "当没有匹配的映射关系时，应该返回空列表")
	assert.Equal(t, 0, len(result), "当没有匹配的映射关系时，应该返回空列表")
}

// TestGetNetMap_DatabaseError 测试数据库查询错误
func TestGetNetMap_DatabaseError(t *testing.T) {
	// 初始化数据库mock
	mockDb := testcommon.InitSqlMock()
	defer mockDb.Close()

	mockDb.Mock.ExpectQuery("SELECT \\* FROM `net_mappings` WHERE from_ip = \\? OR to_ip = \\? ORDER BY created_at DESC").
		WithArgs("***********", "***********").
		WillReturnError(errors.New("database connection failed"))

	// 调用被测试的函数
	result, err := GetNetMap("***********", "")

	// 验证结果
	assert.Error(t, err, "当数据库查询失败时，应该返回错误")
	assert.Nil(t, result, "当数据库查询失败时，应该返回错误")
}

// TestBuildMappingGplotFromIPs_EmptyList 测试空IP列表
func TestBuildMappingGplotFromIPs_EmptyList(t *testing.T) {
	// 初始化数据库mock
	mockDb := testcommon.InitSqlMock()
	defer mockDb.Close()

	// 调用被测试的函数
	mappingGplot, nodeList, err := buildMappingGplotFromIPs(context.Background(), []string{}, 100)

	// 验证结果
	assert.NoError(t, err, "空IP列表应该返回空的拓扑图")
	assert.NotNil(t, mappingGplot, "空IP列表应该返回空的拓扑图")
	assert.NotNil(t, nodeList, "空IP列表应该返回空的拓扑图")
	assert.Equal(t, 0, len(mappingGplot), "边数应该匹配: 空IP列表应该返回空的拓扑图")
	assert.Equal(t, 0, len(nodeList), "节点数应该匹配: 空IP列表应该返回空的拓扑图")
}

// TestBuildMappingGplotFromIPs_SingleIPNoMapping 测试单个IP无映射
func TestBuildMappingGplotFromIPs_SingleIPNoMapping(t *testing.T) {
	// 初始化数据库mock
	mockDb := testcommon.InitSqlMock()
	defer mockDb.Close()

	mockDb.ExpectQuery("SELECT * FROM `net_mappings` WHERE from_ip = ? OR to_ip = ? ORDER BY created_at DESC").
		WithArgs("***********", "***********").
		WillReturnRows(sqlmock.NewRows([]string{"id", "from_ip", "from_port", "to_ip", "to_port"}))

	// 调用被测试的函数
	mappingGplot, nodeList, err := buildMappingGplotFromIPs(context.Background(), []string{"***********"}, 10)

	// 验证结果
	assert.NoError(t, err, "单个IP无映射关系时应该返回空结果")
	assert.NotNil(t, mappingGplot, "单个IP无映射关系时应该返回空结果")
	assert.NotNil(t, nodeList, "单个IP无映射关系时应该返回空结果")
	assert.Equal(t, 0, len(mappingGplot), "边数应该匹配: 单个IP无映射关系时应该返回空结果")
	assert.Equal(t, 0, len(nodeList), "节点数应该匹配: 单个IP无映射关系时应该返回空结果")
}

// TestBuildMappingGplotFromIPs_SingleIPWithMapping 测试单个IP有映射
func TestBuildMappingGplotFromIPs_SingleIPWithMapping(t *testing.T) {
	// 初始化数据库mock
	mockDb := testcommon.InitSqlMock()
	defer mockDb.Close()

	// 第一次查询 ***********
	mockDb.ExpectQuery("SELECT * FROM `net_mappings` WHERE from_ip = ? OR to_ip = ? ORDER BY created_at DESC").
		WithArgs("***********", "***********").
		WillReturnRows(sqlmock.NewRows([]string{"id", "from_ip", "from_port", "to_ip", "to_port"}).
			AddRow(1, "***********", "", "***********", ""))

	// 第二次查询 ***********（BFS扩展）
	mockDb.ExpectQuery("SELECT * FROM `net_mappings` WHERE from_ip = ? OR to_ip = ? ORDER BY created_at DESC").
		WithArgs("***********", "***********").
		WillReturnRows(sqlmock.NewRows([]string{"id", "from_ip", "from_port", "to_ip", "to_port"}))

	// 调用被测试的函数
	mappingGplot, nodeList, err := buildMappingGplotFromIPs(context.Background(), []string{"***********"}, 10)

	// 验证结果
	assert.NoError(t, err, "单个IP有映射关系时应该正确构建拓扑图")
	assert.NotNil(t, mappingGplot, "单个IP有映射关系时应该正确构建拓扑图")
	assert.NotNil(t, nodeList, "单个IP有映射关系时应该正确构建拓扑图")
	assert.Equal(t, 1, len(mappingGplot), "边数应该匹配: 单个IP有映射关系时应该正确构建拓扑图")
	assert.Equal(t, 2, len(nodeList), "节点数应该匹配: 单个IP有映射关系时应该正确构建拓扑图")
}

// TestBuildMappingGplotFromIPs_DatabaseError 测试数据库查询错误
func TestBuildMappingGplotFromIPs_DatabaseError(t *testing.T) {
	// 初始化数据库mock
	mockDb := testcommon.InitSqlMock()
	defer mockDb.Close()

	mockDb.Mock.ExpectQuery("SELECT \\* FROM `net_mappings` WHERE from_ip = \\? OR to_ip = \\? ORDER BY created_at DESC").
		WithArgs("***********", "***********").
		WillReturnError(errors.New("database connection failed"))

	// 调用被测试的函数
	_, _, err := buildMappingGplotFromIPs(context.Background(), []string{"***********"}, 10)

	// 验证结果
	assert.Error(t, err, "当数据库查询失败时，应该返回错误")
}

// TestBuildMappingGplotFromIPs_MaxEdgesLimit 测试达到最大边数限制
func TestBuildMappingGplotFromIPs_MaxEdgesLimit(t *testing.T) {
	// 初始化数据库mock
	mockDb := testcommon.InitSqlMock()
	defer mockDb.Close()

	mockDb.ExpectQuery("SELECT * FROM `net_mappings` WHERE from_ip = ? OR to_ip = ? ORDER BY created_at DESC").
		WithArgs("***********", "***********").
		WillReturnRows(sqlmock.NewRows([]string{"id", "from_ip", "from_port", "to_ip", "to_port"}).
			AddRow(1, "***********", "", "***********", "").
			AddRow(2, "***********", "", "***********", ""))

	// 调用被测试的函数
	mappingGplot, nodeList, err := buildMappingGplotFromIPs(context.Background(), []string{"***********"}, 1)

	// 验证结果
	assert.NoError(t, err, "应该正确处理最大边数限制")
	assert.NotNil(t, mappingGplot, "应该正确处理最大边数限制")
	assert.NotNil(t, nodeList, "应该正确处理最大边数限制")
	assert.Equal(t, 1, len(mappingGplot), "边数应该匹配: 应该正确处理最大边数限制")
	assert.Equal(t, 2, len(nodeList), "节点数应该匹配: 应该正确处理最大边数限制")
}

// TestBuildMappingGplotFromIPs_ComplexTopology 测试复杂网络拓扑
func TestBuildMappingGplotFromIPs_ComplexTopology(t *testing.T) {
	// 初始化数据库mock
	mockDb := testcommon.InitSqlMock()
	defer mockDb.Close()

	// 第一次查询 ***********（无端口）
	mockDb.ExpectQuery("SELECT * FROM `net_mappings` WHERE from_ip = ? OR to_ip = ? ORDER BY created_at DESC").
		WithArgs("***********", "***********").
		WillReturnRows(sqlmock.NewRows([]string{"id", "from_ip", "from_port", "to_ip", "to_port"}).
			AddRow(1, "***********", "80", "***********", "8080").
			AddRow(2, "***********", "443", "***********", "443"))

	// 第二次查询 ***********:80（带端口）
	mockDb.ExpectQuery("SELECT * FROM `net_mappings` WHERE (from_ip = ? AND from_port = ?) OR (to_ip = ? AND to_port = ?) ORDER BY created_at DESC").
		WithArgs("***********", "80", "***********", "80").
		WillReturnRows(sqlmock.NewRows([]string{"id", "from_ip", "from_port", "to_ip", "to_port"}))

	// 第三次查询 ***********:8080（带端口）
	mockDb.ExpectQuery("SELECT * FROM `net_mappings` WHERE (from_ip = ? AND from_port = ?) OR (to_ip = ? AND to_port = ?) ORDER BY created_at DESC").
		WithArgs("***********", "8080", "***********", "8080").
		WillReturnRows(sqlmock.NewRows([]string{"id", "from_ip", "from_port", "to_ip", "to_port"}))

	// 第四次查询 ***********:443（带端口）
	mockDb.ExpectQuery("SELECT * FROM `net_mappings` WHERE (from_ip = ? AND from_port = ?) OR (to_ip = ? AND to_port = ?) ORDER BY created_at DESC").
		WithArgs("***********", "443", "***********", "443").
		WillReturnRows(sqlmock.NewRows([]string{"id", "from_ip", "from_port", "to_ip", "to_port"}))

	// 第五次查询 ***********:443（带端口，来自第二条映射关系的目标）
	mockDb.ExpectQuery("SELECT * FROM `net_mappings` WHERE (from_ip = ? AND from_port = ?) OR (to_ip = ? AND to_port = ?) ORDER BY created_at DESC").
		WithArgs("***********", "443", "***********", "443").
		WillReturnRows(sqlmock.NewRows([]string{"id", "from_ip", "from_port", "to_ip", "to_port"}))

	// 调用被测试的函数
	mappingGplot, nodeList, err := buildMappingGplotFromIPs(context.Background(), []string{"***********"}, 5)

	// 验证结果
	assert.NoError(t, err, "应该正确构建复杂的网络拓扑图")
	assert.NotNil(t, mappingGplot, "应该正确构建复杂的网络拓扑图")
	assert.NotNil(t, nodeList, "应该正确构建复杂的网络拓扑图")
	assert.Equal(t, 2, len(mappingGplot), "边数应该匹配: 应该正确构建复杂的网络拓扑图")
	assert.Equal(t, 3, len(nodeList), "节点数应该匹配: 应该正确构建复杂的网络拓扑图")
}

// TestKeyOf_IPWithoutPort 测试IP无端口
func TestKeyOf_IPWithoutPort(t *testing.T) {
	result := keyOf("***********", "")
	assert.Equal(t, "***********", result, "keyOf函数应该正确生成键值")
}

// TestKeyOf_IPWithPort 测试IP有端口
func TestKeyOf_IPWithPort(t *testing.T) {
	result := keyOf("***********", "8080")
	assert.Equal(t, "***********:8080", result, "keyOf函数应该正确生成键值")
}

// TestKeyOf_EmptyIPWithoutPort 测试空IP无端口
func TestKeyOf_EmptyIPWithoutPort(t *testing.T) {
	result := keyOf("", "")
	assert.Equal(t, "", result, "keyOf函数应该正确生成键值")
}

// TestKeyOf_EmptyIPWithPort 测试空IP有端口
func TestKeyOf_EmptyIPWithPort(t *testing.T) {
	result := keyOf("", "8080")
	assert.Equal(t, ":8080", result, "keyOf函数应该正确生成键值")
}
