package net_mapping

import (
	"fmt"
	"fobrain/models/mysql/net_mapping"
	"io"
	"sync"
)

func ImportNetMappingByQiMingXingChen(file io.Reader, batchNo string, fileType string, fromArea, toArea uint64) (int64, string, error) {
	successCount := int64(0)
	wg := &sync.WaitGroup{}
	netMappingChan := make(chan *net_mapping.NetMapping, 0)

	wg.Add(1)
	go SaveData(netMappingChan, wg, &successCount)

	totalCount := PrepareQimingXingChenData(netMappingChan, file, batchNo, fileType, fromArea, toArea)

	close(netMappingChan)
	wg.Wait()
	return successCount, fmt.Sprintf("导入成功，成功解析%d条数据，新增%d条数据", totalCount, successCount), nil
}

func PrepareQimingXingChenData(netMappingChan chan *net_mapping.NetMapping, file io.Reader, batchNo string, fileType string, fromArea, toArea uint64) int64 {
	totalCount := int64(0)
	content, _ := io.ReadAll(file)
	netMappingList, err := ImportNetMappingByQingMingXingChenConfirm(string(content), fromArea, toArea)
	if err != nil {
		return totalCount
	}
	for _, netMapping := range netMappingList {
		netMapping.BatchNo = batchNo
		netMapping.DataSource = fileType
		netMappingChan <- netMapping
		totalCount++
	}
	return totalCount
}
