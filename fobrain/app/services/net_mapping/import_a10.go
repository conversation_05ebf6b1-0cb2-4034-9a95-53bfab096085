package net_mapping

import (
	"bufio"
	"fmt"
	"fobrain/models/mysql/net_mapping"
	"github.com/spf13/cast"
	"io"
	"regexp"
	"strconv"
	"strings"
	"sync"
)

// A10数据源

type ServiceGroupMember struct {
	Ip       string
	Port     int
	Protocol string
}

type HostSwitching struct {
	Domain string
	Pool   string
}

// PrepareA10Data 生成A10数据
func PrepareA10Data(batchNo string, fileType string, datas map[string][]string, netMappingChan chan *net_mapping.NetMapping, fromArea, toArea uint64) int64 {
	totalCount := int64(0)
	serviceGroupMap := make(map[string][]*ServiceGroupMember)
	templateHttpMap := make(map[string][]*HostSwitching)
	for key, value := range datas {
		keyType, matches := getA10ValueByRegexp(key)
		if keyType == "SlbServiceGroup" {
			slbServiceGroup := matches[1]
			for _, line := range value {
				lineType, memberMatches := getA10ValueByRegexp(line)
				if lineType == "Member" {
					member := &ServiceGroupMember{
						Ip:       memberMatches[1],
						Port:     cast.ToInt(memberMatches[2]),
						Protocol: matches[2],
					}
					serviceGroupMap[slbServiceGroup] = append(serviceGroupMap[slbServiceGroup], member)
				}
			}
		}
		if keyType == "SlbTemplateHttpHostSwitching" || keyType == "SlbTemplateHttpHost" {
			templateHttp := matches[1]
			for _, line := range value {
				lineType, hostSwitchingMatches := getA10ValueByRegexp(line)
				if lineType == "HostSwitching" {
					hostSwitching := &HostSwitching{
						Domain: hostSwitchingMatches[1],
						Pool:   hostSwitchingMatches[2],
					}
					templateHttpMap[templateHttp] = append(templateHttpMap[templateHttp], hostSwitching)
				}
			}
		}
	}
	for key, value := range datas {
		keyType, matches := getA10ValueByRegexp(key)
		if keyType == "FromIp" {
			fromIp := matches[1]
			publicPort := make([]string, 0)
			for _, line := range value {
				lineType, subMatches := getA10ValueByRegexp(line)
				if lineType == "FromPort" {
					publicPort = subMatches
					continue
				}
				if lineType == "ServiceGroup" {
					serviceGroup := subMatches[1]
					if serviceGroupMembers, ok := serviceGroupMap[serviceGroup]; ok {
						for _, serviceGroupMember := range serviceGroupMembers {
							netMapping := &net_mapping.NetMapping{
								DataSource:   fileType,
								BatchNo:      batchNo,
								FromArea:     fromArea,
								FromIp:       fromIp,
								FromPort:     publicPort[1],
								FromProtocol: publicPort[2],
								ToArea:       toArea,
								ToIp:         serviceGroupMember.Ip,
								ToPort:       cast.ToString(serviceGroupMember.Port),
								ToProtocol:   serviceGroupMember.Protocol,
							}
							if !checkIp(fromIp, serviceGroupMember.Ip) || !checkPort(publicPort[1], strconv.Itoa(serviceGroupMember.Port)) {
								continue
							}
							netMappingChan <- netMapping
							totalCount++
						}
					}
				}
				if lineType == "TemplateHttpHostSwitching" || lineType == "TemplateHttpHost" {
					templateHttpMapKey := "http-hostswitching"
					if lineType == "TemplateHttpHost" {
						templateHttpMapKey = "http_host"
					}
					for _, hostSwitching := range templateHttpMap[templateHttpMapKey] {
						serviceGroup := hostSwitching.Pool
						if serviceGroupMembers, ok := serviceGroupMap[serviceGroup]; ok {
							for _, serviceGroupMember := range serviceGroupMembers {
								netMapping := &net_mapping.NetMapping{
									DataSource:   fileType,
									BatchNo:      batchNo,
									FromArea:     fromArea,
									FromIp:       fromIp,
									FromPort:     publicPort[1],
									FromDomain:   hostSwitching.Domain,
									FromProtocol: publicPort[2],
									ToArea:       toArea,
									ToIp:         serviceGroupMember.Ip,
									ToPort:       cast.ToString(serviceGroupMember.Port),
									ToProtocol:   serviceGroupMember.Protocol,
								}
								if !checkIp(fromIp, serviceGroupMember.Ip) || !checkPort(publicPort[1], strconv.Itoa(serviceGroupMember.Port)) {
									continue
								}
								netMappingChan <- netMapping
								totalCount++
							}
						}
					}
				}
			}
		}
	}
	return totalCount
}

// ImportNetMappingByA10 导入IP映射-A10数据源
func ImportNetMappingByA10(batchNo string, fileType string, datas map[string][]string, fromArea, toArea uint64) (int64, string, error) {
	successCount := int64(0)
	wg := &sync.WaitGroup{}
	netMappingChan := make(chan *net_mapping.NetMapping, 100)

	wg.Add(1)
	go SaveData(netMappingChan, wg, &successCount)

	totalCount := PrepareA10Data(batchNo, fileType, datas, netMappingChan, fromArea, toArea)

	close(netMappingChan)
	wg.Wait()
	return successCount, fmt.Sprintf("导入成功，成功解析%d条数据，新增%d条数据", totalCount, successCount), nil
}

// ImportNetMappingByA10Confirm 导入IP映射-A10数据源-确认
func ImportNetMappingByA10Confirm(datas map[string][]string, fromArea, toArea uint64) ([]*net_mapping.NetMapping, error) {
	netMappings := make([]*net_mapping.NetMapping, 0)
	serviceGroupMap := make(map[string][]*ServiceGroupMember)
	templateHttpMap := make(map[string][]*HostSwitching)
	for key, value := range datas {
		keyType, matches := getA10ValueByRegexp(key)
		if keyType == "SlbServiceGroup" {
			slbServiceGroup := matches[1]
			for _, line := range value {
				lineType, memberMatches := getA10ValueByRegexp(line)
				if lineType == "Member" {
					member := &ServiceGroupMember{
						Ip:       memberMatches[1],
						Port:     cast.ToInt(memberMatches[2]),
						Protocol: matches[2],
					}
					serviceGroupMap[slbServiceGroup] = append(serviceGroupMap[slbServiceGroup], member)
				}
			}
		}
		if keyType == "SlbTemplateHttpHostSwitching" || keyType == "SlbTemplateHttpHost" {
			templateHttp := matches[1]
			for _, line := range value {
				lineType, hostSwitchingMatches := getA10ValueByRegexp(line)
				if lineType == "HostSwitching" {
					hostSwitching := &HostSwitching{
						Domain: hostSwitchingMatches[1],
						Pool:   hostSwitchingMatches[2],
					}
					templateHttpMap[templateHttp] = append(templateHttpMap[templateHttp], hostSwitching)
				}
			}
		}
	}
	for key, value := range datas {
		keyType, matches := getA10ValueByRegexp(key)
		if keyType == "FromIp" {
			fromIp := matches[1]
			publicPort := make([]string, 0)
			for _, line := range value {
				if len(netMappings) > 4 {
					break
				}
				lineType, subMatches := getA10ValueByRegexp(line)
				if lineType == "FromPort" {
					publicPort = subMatches
					continue
				}
				if lineType == "ServiceGroup" {
					serviceGroup := subMatches[1]
					if serviceGroupMembers, ok := serviceGroupMap[serviceGroup]; ok {
						for _, serviceGroupMember := range serviceGroupMembers {
							if len(netMappings) > 4 {
								break
							}
							netMapping := &net_mapping.NetMapping{
								FromArea:     fromArea,
								FromIp:       fromIp,
								FromPort:     publicPort[1],
								FromProtocol: publicPort[2],
								ToArea:       toArea,
								ToIp:         serviceGroupMember.Ip,
								ToPort:       cast.ToString(serviceGroupMember.Port),
								ToProtocol:   serviceGroupMember.Protocol,
							}
							if !checkIp(fromIp, serviceGroupMember.Ip) || !checkPort(publicPort[1], strconv.Itoa(serviceGroupMember.Port)) {
								continue
							}
							netMappings = append(netMappings, netMapping)
						}
					}
				}
				if lineType == "TemplateHttpHostSwitching" || lineType == "TemplateHttpHost" {
					templateHttpMapKey := "http-hostswitching"
					if lineType == "TemplateHttpHost" {
						templateHttpMapKey = "http_host"
					}
					for _, hostSwitching := range templateHttpMap[templateHttpMapKey] {
						if len(netMappings) > 4 {
							break
						}
						serviceGroup := hostSwitching.Pool
						if serviceGroupMembers, ok := serviceGroupMap[serviceGroup]; ok {
							for _, serviceGroupMember := range serviceGroupMembers {
								if len(netMappings) > 4 {
									break
								}
								netMapping := &net_mapping.NetMapping{
									FromArea:     fromArea,
									FromIp:       fromIp,
									FromPort:     publicPort[1],
									FromDomain:   hostSwitching.Domain,
									FromProtocol: publicPort[2],
									ToArea:       toArea,
									ToIp:         serviceGroupMember.Ip,
									ToPort:       cast.ToString(serviceGroupMember.Port),
									ToProtocol:   serviceGroupMember.Protocol,
								}
								if !checkIp(fromIp, serviceGroupMember.Ip) || !checkPort(publicPort[1], strconv.Itoa(serviceGroupMember.Port)) {
									continue
								}
								netMappings = append(netMappings, netMapping)
							}
						}
					}

				}
			}
		}
	}
	return netMappings, nil
}

// BuildA10Data 初步处理A10数据
func BuildA10Data(f io.Reader) map[string][]string {
	// 打开文件
	scanner := bufio.NewScanner(f)
	datas := make(map[string][]string)
	key := ""
	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())
		if strings.HasPrefix(line, "slb virtual-server vip_") ||
			strings.HasPrefix(line, "slb template http http-hostswitching") ||
			strings.HasPrefix(line, "slb template http http_host") ||
			strings.HasPrefix(line, "slb service-group") {
			key = line
		}
		if ((strings.HasPrefix(line, "port") ||
			strings.HasPrefix(line, "service-group")) ||
			strings.HasPrefix(line, "template http http_host") ||
			strings.HasPrefix(line, "template http http-hostswitching") ||
			strings.HasPrefix(line, "host-switching") ||
			strings.HasPrefix(line, "member")) && key != "" {
			datas[key] = append(datas[key], line)
		}
		if strings.HasPrefix(line, "!") {
			key = ""
		}
	}
	return datas
}

func getA10ValueByRegexp(line string) (string, []string) {
	expressions := map[string]*regexp.Regexp{
		"FromIp":                       regexp.MustCompile(`slb\s+virtual-server\s+vip_([\d.]+)\s+([\d.]+)`),
		"FromPort":                     regexp.MustCompile(`port\s+(\d+)\s+(\w+)`),
		"ServiceGroup":                 regexp.MustCompile(`^service-group\s+(.*)$`),
		"SlbTemplateHttpHostSwitching": regexp.MustCompile(`^slb\s+template\s+http\s+(http-hostswitching)$`),
		"TemplateHttpHostSwitching":    regexp.MustCompile(`^template\s+http\s+http-hostswitching$`),
		"SlbTemplateHttpHost":          regexp.MustCompile(`^slb\s+template\s+http\s+(http_host)$`),
		"TemplateHttpHost":             regexp.MustCompile(`^template\s+http\s+http_host$`),
		"HostSwitching":                regexp.MustCompile(`^host-switching\s+.*\s+([a-zA-Z0-9][a-zA-Z0-9\-\.]+\.[a-zA-Z]{2,})\s+service-group\s+(pool_[^_]+_\d+_\w+)$`),
		"SlbServiceGroup":              regexp.MustCompile(`^slb service-group\s+(.*)\s+(\w+)$`),
		"Member":                       regexp.MustCompile(`member\s+([\d.]+)\s+(\d+)`),
	}

	for name, expr := range expressions {
		matches := expr.FindStringSubmatch(line)
		if matches != nil {
			return name, matches
		}
	}
	return "", nil
}
