package net_mapping

import (
	"context"
	"errors"
	"fmt"
	assetRepository "fobrain/fobrain/app/repository/asset"
	request "fobrain/fobrain/app/request/net_mapping"
	response "fobrain/fobrain/app/response/net_mapping"
	"fobrain/initialize/es"
	"fobrain/initialize/mysql"
	"fobrain/models/elastic/assets"
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/net_mapping"
	"fobrain/models/mysql/network_areas"
	"fobrain/pkg/utils"
	pgidservice "fobrain/services/people_pgid"
	"strconv"
	"strings"

	"github.com/olivere/elastic/v7"
	"github.com/spf13/cast"
)

const (
	Business = "business"
	Ip       = "ip"
	IpPort   = "ip_port"
	Domain   = "domain"
)

type MappingGplot []*net_mapping.NetMapping

func (m *MappingGplot) Include(mapping *net_mapping.NetMapping) bool {
	for _, item := range *m {
		if mapping.FromIp == item.FromIp && mapping.ToIp == item.ToIp &&
			mapping.FromPort == item.FromPort && mapping.ToPort == item.ToPort {
			return true
		}
		if mapping.FromIp == item.ToIp && mapping.ToIp == item.FromIp &&
			mapping.FromPort == item.ToPort && mapping.ToPort == item.FromPort {
			return true
		}
	}
	return false
}

func (m *MappingGplot) Add(mapping *net_mapping.NetMapping) bool {
	if m.Include(mapping) {
		return false
	}
	*m = append(*m, mapping)
	return true
}

func GetMappingGplot(params *request.GetMappingGplotRequest) (map[string]interface{}, error) {

	areaIdNameMap, err := net_mapping.NewNetMappingAreaModel().GetIdNameMap()
	if err != nil {
		return nil, err
	}
	mappingGplot, ipList, err := buildMappingGplot(params.Keyword, params.Size, params.SearchType)
	if err != nil {
		return nil, err
	}
	noPort := make([]string, 0)
	if params.SearchType == "ip_port" {
		// 保存不带端口的IP
		for _, item := range ipList {
			parts := strings.Split(item["name"], ":")
			if len(parts) != 2 {
				ip := item["name"]
				noPort = append(noPort, ip)
			}
		}
		// 清除带端口的数据
		for _, name := range noPort {
			for ind, item := range ipList {
				parts := strings.Split(item["name"], ":")
				if len(parts) == 2 {
					if parts[0] == name {
						ipList = append(ipList[:ind], ipList[ind+1:]...)
					}
				}
			}
		}
		for _, mapping := range mappingGplot {
			if mapping.FromPort != "" && !utils.InArray(mapping.FromIp, noPort) {
				mapping.FromIp = mapping.FromIp + ":" + mapping.FromPort
			}
			if mapping.ToPort != "" && !utils.InArray(mapping.ToIp, noPort) {
				mapping.ToIp = mapping.ToIp + ":" + mapping.ToPort
			}
		}
	}

	var res []*response.NetMappingGplotResponse

	for _, mapping := range mappingGplot {
		res = append(res, &response.NetMappingGplotResponse{
			SourceName:     areaIdNameMap[mapping.FromArea],
			Source:         mapping.FromIp,
			SourcePort:     mapping.FromPort,
			SourceDomain:   mapping.FromDomain,
			SourceUrl:      mapping.FromUrl,
			SourceProtocol: mapping.FromProtocol,
			TargetName:     areaIdNameMap[mapping.ToArea],
			Target:         mapping.ToIp,
			TargetPort:     mapping.ToPort,
			TargetDomain:   mapping.ToDomain,
			TargetUrl:      mapping.ToUrl,
			TargetProtocol: mapping.ToProtocol,
		})
	}

	return map[string]interface{}{
		"links": res,
		"data":  ipList,
	}, nil
}
func buildMappingGplot(keyword string, size int, searchType string) (MappingGplot, []map[string]string, error) {
	// 第一步：根据关键词获取初始的映射关系列表
	rootMappingList, err := getRootMappingList(keyword, size, searchType)
	if err != nil {
		return nil, nil, err
	}
	// 初始化图
	mappingGplot := MappingGplot{}
	// 遍历过的数据和遍历中的数据
	lookedMappingList := make([][2]string, 0)
	lookingMappingList := make([][2]string, 0)

	// 前端构图所需的list数据
	var list = make([]map[string]string, 0)
	var savedIp = make([]string, 0)

	for _, mapping := range rootMappingList {
		lookingMappingList = append(lookingMappingList, [2]string{mapping.FromIp, mapping.FromPort})
		lookingMappingList = append(lookingMappingList, [2]string{mapping.ToIp, mapping.ToPort})
	}

	for len(lookingMappingList) > 0 {
		// 取一条数据
		lookingMapping := lookingMappingList[0]
		lookingMappingList = lookingMappingList[1:]

		if utils.InArray[[2]string](lookingMapping, lookedMappingList) {
			continue
		}
		lookedMappingList = append(lookedMappingList, lookingMapping)

		var netMappingList = make([]*net_mapping.NetMapping, 0)
		if lookingMapping[1] == "" {
			netMappingList, err = net_mapping.NewNetMappingModel().ListByOpt(
				mysql.WithWhere("from_ip = ?", lookingMapping[0]),
				mysql.WithOrWhere("to_ip = ?", lookingMapping[0]),
			)
		} else {
			netMappingList, err = net_mapping.NewNetMappingModel().ListByOpt(
				mysql.WithWhere("from_ip = ? AND from_port = ?", lookingMapping[0], lookingMapping[1]),
				mysql.WithOrWhere("to_ip = ? AND to_port = ?", lookingMapping[0], lookingMapping[1]),
			)
		}
		if err != nil {
			return nil, nil, err
		}

		for _, mapping := range netMappingList {
			if mappingGplot.Add(mapping) {
				list, savedIp = recordNodeMsg(mapping, list, savedIp, searchType)

				mappingSlice := [2]string{mapping.FromIp, mapping.FromPort}
				if !utils.InArray[[2]string](mappingSlice, lookingMappingList) && !utils.InArray[[2]string](mappingSlice, lookedMappingList) {
					lookingMappingList = append(lookingMappingList, mappingSlice)
				}
				mappingSlice = [2]string{mapping.ToIp, mapping.ToPort}
				if !utils.InArray[[2]string](mappingSlice, lookingMappingList) && !utils.InArray[[2]string](mappingSlice, lookedMappingList) {
					lookingMappingList = append(lookingMappingList, mappingSlice)
				}
			}
		}
	}
	return mappingGplot, list, nil
}

// getRootMappingList 根据关键词和搜索类型获取初始的映射关系列表
func getRootMappingList(keyword string, size int, searchType string) ([]*net_mapping.NetMapping, error) {
	var rootMappingList []*net_mapping.NetMapping
	var err error
	parts := strings.Split(keyword, ":")

	if len(parts) == 2 && searchType == "ip_port" {
		if !utils.IsValidIP(parts[0]) && !utils.IsValidatePort(parts[1]) {
			return nil, errors.New("参数错误，请输入正确的IP:端口")
		}
		rootMappingList, err = net_mapping.NewNetMappingModel().ListByOpt(
			mysql.WithWhere("from_ip = ? AND from_port = ?", parts[0], parts[1]),
			mysql.WithOrWhere("to_ip = ? AND to_port = ?", parts[0], parts[1]),
		)
	} else if searchType == "ip" {
		if !utils.IsValidIP(keyword) {
			return nil, errors.New("参数错误，请输入正确的IP")
		}
		rootMappingList, err = net_mapping.NewNetMappingModel().ListByOpt(
			mysql.WithWhere("from_ip = ?", keyword),
			mysql.WithOrWhere("to_ip = ?", keyword),
		)
	} else if searchType == "domain" {
		rootMappingList, err = net_mapping.NewNetMappingModel().ListByOpt(
			mysql.WithWhere("from_domain = ?", keyword),
			mysql.WithOrWhere("to_domain = ?", keyword),
		)
	} else {
		return nil, errors.New("输入错误")
	}
	if err != nil {
		return nil, err
	}
	if len(rootMappingList) > size && size > 0 {
		rootMappingList = rootMappingList[:size]
	}
	return rootMappingList, nil
}

// recordNodeMsg 记录映射关系中的节点信息，用于前端拓扑图显示
func recordNodeMsg(mapping *net_mapping.NetMapping, list []map[string]string, savedIp []string, searchType string) ([]map[string]string, []string) {
	areaIdNameMap, err := net_mapping.NewNetMappingAreaModel().GetIdNameMap()
	if err != nil {
		return nil, nil
	}
	fromIpName := mapping.FromIp
	if searchType == "ip_port" {
		if mapping.FromPort != "" {
			fromIpName = mapping.FromIp + ":" + mapping.FromPort
		}
	}
	if !utils.InArray(fromIpName, savedIp) {
		fromMap := map[string]string{
			"name":    fromIpName,
			"ip_kind": areaIdNameMap[mapping.FromArea],
		}
		savedIp = append(savedIp, fromIpName)
		// 非ip_port
		t := Ip
		if fromIpName != mapping.FromIp {
			t = IpPort
		}
		res, _ := getIPs(fromIpName, t)

		fromMap["vuln_count"] = strconv.FormatInt(res[mapping.FromIp], 10)

		list = append(list, fromMap)
	}

	toIpName := mapping.ToIp
	if searchType == "ip_port" {
		if mapping.ToPort != "" {
			toIpName = mapping.ToIp + ":" + mapping.ToPort
		}
	}
	if !utils.InArray(toIpName, savedIp) {
		toMap := map[string]string{
			"name":    toIpName,
			"ip_kind": areaIdNameMap[mapping.ToArea],
		}
		savedIp = append(savedIp, toIpName)
		// 非ip_port
		t := Ip
		if toIpName != mapping.ToIp {
			t = IpPort
		}
		res, _ := getIPs(toIpName, t)

		toMap["vuln_count"] = strconv.FormatInt(res[mapping.ToIp], 10)
		list = append(list, toMap)
	}
	return list, savedIp
}

// GetMappingAssets 获取网络映射关系中涉及的资产信息
func GetMappingAssets(params *request.GetMappingAssetsRequest) (any, int64, error) {
	var gplot MappingGplot
	var err error

	parts := strings.Split(params.Keyword, ":")
	if len(parts) == 2 {
		if !utils.IsValidIP(parts[0]) && !utils.IsValidatePort(parts[1]) {
			return nil, 0, errors.New("参数错误，请输入IP:端口")
		}
		gplot, _, err = buildMappingGplot(params.Keyword, params.Size, "ip_port")
	} else if utils.IsValidIP(params.Keyword) {
		gplot, _, err = buildMappingGplot(params.Keyword, params.Size, "ip")
	} else {
		// 获取业务系统下的所有IP
		res, err := getIPs(params.Keyword, Business)
		if err != nil {
			return nil, 0, err
		}
		ips := make([]string, 0, len(res))
		for i := range res {
			ips = append(ips, i)
		}
		gplot, _, err = buildMappingGplotFromIPs(context.Background(), ips, params.Size)
	}

	if err != nil || len(gplot) == 0 {
		return nil, 0, err
	}

	assetQuery := elastic.NewBoolQuery()
	queryList := make([]elastic.Query, 0)
	for _, mapping := range gplot {
		q := elastic.NewBoolQuery()
		q.Must(elastic.NewTermsQuery("ip", mapping.FromIp))
		if mapping.FromPort != "" && mapping.FromPort != "0" {
			q.Must(elastic.NewTermsQuery("ports.port", mapping.FromPort))
		}
		p := elastic.NewBoolQuery()
		p.Must(elastic.NewTermsQuery("ip", mapping.ToIp))
		if mapping.ToPort != "" && mapping.ToPort != "0" {
			p.Must(elastic.NewTermsQuery("ports.port", mapping.ToPort))
		}
		queryList = append(queryList, q)
		queryList = append(queryList, p)
	}
	assetQuery.Should(queryList...).MinimumShouldMatch("1")
	assetServe := es.GetEsClient().Search().Index(assets.NewAssets().IndexName()).
		From(es.GetFrom(params.Page, params.PerPage)).Size(es.GetSize(params.PerPage)).
		Query(assetQuery).TrackTotalHits(true)
	if len(params.Field) > 0 {
		assetServe.Sort(params.Field, params.Order == "ascend")
	}
	searchResult, err := assetServe.Do(context.Background())
	if err != nil {
		return nil, 0, err
	}
	list, err := assetRepository.ParseAssets(searchResult.Hits.Hits)
	if err != nil {
		return nil, 0, err
	}
	assetRepository.HandleNetMapping(list)
	return list, searchResult.TotalHits(), nil
}

// GetMappingBaseInfo 获取网络映射关系中资产的基础信息
func GetMappingBaseInfo(params *request.GetMappingBaseInfoRequest) ([]*MappingBaseInfoResponse, error) {
	assets, err := getAssetBaseInfo(params.Keyword)
	if err != nil || assets == nil {
		return nil, errors.New("参数错误")
	}

	var result = make([]*MappingBaseInfoResponse, 0, len(assets))
	for _, asset := range assets {
		for _, oper := range asset.OperInfo {
			oper.Pgid, _ = pgidservice.GetPgidById(oper.Id)
		}
		for _, b := range asset.Business {
			for _, pb := range b.PersonBase {
				pb.Pgid, _ = pgidservice.GetPgidById(pb.Id)
			}
		}
		networkArea := network_areas.NetworkAreaName(cast.ToUint64(asset.Area))
		source := data_source.NewSourceModel().SourceNames(asset.SourceIds)
		ports := make([]string, 0)
		for _, port := range asset.Ports {
			ports = append(ports, cast.ToString(port.Port))
		}
		result = append(result, &MappingBaseInfoResponse{
			Id:     asset.Id,
			Ip:     asset.Ip,
			AreaId: asset.Area,
			Area: map[string]interface{}{
				"id":   asset.Area,
				"name": networkArea,
			},
			SourceId:       asset.SourceIds,
			Source:         source,
			Hostname:       asset.HostName,
			Os:             asset.Os,
			Mac:            asset.Mac,
			Sn:             asset.Sn,
			Product:        asset.Product,
			CreateAt:       asset.CreatedAt.Format("2006-01-02 15:04:05"),
			UpdateAt:       asset.UpdatedAt.Format("2006-01-02 15:04:05"),
			BusinessSystem: asset.Business,
			Oper:           asset.OperInfo,
			Ports:          ports,
			RuleInfos:      asset.RuleInfos,
		})
	}

	return result, nil
}

// MappingBaseInfoResponse 网络映射基础信息响应结构
type MappingBaseInfoResponse struct {
	Id             string             `json:"id"`
	Ip             string             `json:"ip"`
	AreaId         int                `json:"area_id"`
	Area           any                `json:"area"`
	SourceId       []uint64           `json:"source_id"`
	Source         any                `json:"source"`
	Hostname       []string           `json:"hostname"`
	Os             []string           `json:"os"`
	Mac            []string           `json:"mac"`
	Sn             []string           `json:"sn"`
	Product        []string           `json:"product"`
	CreateAt       string             `json:"create_at"`
	UpdateAt       string             `json:"update_at"`
	BusinessSystem any                `json:"business_system"`
	Oper           any                `json:"oper"`
	Ports          []string           `json:"ports"`
	RuleInfos      []*assets.RuleInfo `json:"rule_infos"` //组件信息
}

// getAssetBaseInfo 根据关键词从Elasticsearch中查询资产基础信息
func getAssetBaseInfo(keyword string) ([]*assets.Assets, error) {
	assetQuery := elastic.NewBoolQuery()
	parts := strings.Split(keyword, ":")
	if len(parts) == 2 {
		assetQuery = assetQuery.Must(elastic.NewTermQuery("ip", parts[0])).Must(elastic.NewTermQuery("ports.port", parts[1]))
	} else if utils.IsValidIP(keyword) {
		assetQuery = assetQuery.Must(elastic.NewTermQuery("ip", keyword))
	} else {
		return nil, nil
	}

	searchResult, err := es.GetEsClient().Search().Index(assets.NewAssets().IndexName()).
		Query(assetQuery).Do(context.Background())
	if err != nil {
		return nil, err
	}
	if searchResult.Hits.TotalHits.Value == 0 {
		return nil, errors.New("无资产数据")
	}
	var result = make([]*assets.Assets, 0, len(searchResult.Hits.Hits))
	for _, hit := range searchResult.Hits.Hits {
		item, err := assets.ConvertToAssetsModel(hit)
		if err != nil {
			return nil, err
		}
		result = append(result, item)
	}

	return result, err
}

// GetMappingByBusiness 根据业务系统获取拓扑图
func GetMappingByBusiness(params *request.GetMappingByBusinessRequest) (*response.BusinessMappingGplotResponse, error) {

	res, err := getIPs(params.BusinessName, Business)
	// 检查查询结果
	if err != nil {
		return nil, fmt.Errorf("查询业务系统资产失败: %v", err)
	}

	if len(res) == 0 {
		return &response.BusinessMappingGplotResponse{
			Links: []response.NetMappingGplotResponse{},
			Data:  []response.BusinessNodeData{},
		}, nil
	}

	ips := make([]string, 0, len(res))
	for i := range res {
		ips = append(ips, i)
	}

	// IP列表构建网络映射拓扑图
	mappingGplot, ipList, err := buildMappingGplotFromIPs(context.Background(), ips, params.Size)
	if err != nil {
		return nil, fmt.Errorf("构建网络拓扑图失败: %v", err)
	}

	// 获取区域映射信息
	areaIdNameMap, err := net_mapping.NewNetMappingAreaModel().GetIdNameMap()
	if err != nil {
		return nil, fmt.Errorf("获取区域映射失败: %v", err)
	}

	var links []response.NetMappingGplotResponse
	// 所有没有from的节点需要连接上业务系统
	// 记录所有ToIp 不在这个map中则是入度为0的节点
	toIpMap := make(map[string]struct{})
	// 记录所有节点信息 用于后续构建业务系统的节点
	allMap := make(map[string]response.NetMappingGplotResponse)
	for _, mapping := range mappingGplot {
		links = append(links, response.NetMappingGplotResponse{
			// 源节点
			Source:         mapping.FromIp,
			SourceName:     areaIdNameMap[mapping.FromArea],
			SourcePort:     mapping.FromPort,
			SourceDomain:   mapping.FromDomain,
			SourceUrl:      mapping.FromUrl,
			SourceProtocol: mapping.FromProtocol,
			// 目标节点
			Target:         mapping.ToIp,
			TargetName:     areaIdNameMap[mapping.ToArea],
			TargetPort:     mapping.ToPort,
			TargetDomain:   mapping.ToDomain,
			TargetUrl:      mapping.ToUrl,
			TargetProtocol: mapping.ToProtocol,
		})

		toIpMap[mapping.ToIp] = struct{}{}
		allMap[mapping.FromIp] = links[len(links)-1]
		allMap[mapping.ToIp] = links[len(links)-1]
	}

	// 构建业务系统 -> 入度为0的节点
	for _, node := range ipList {
		ip := node["ip"]
		if _, ok := toIpMap[ip]; ok {
			continue
		}
		links = append(links, response.NetMappingGplotResponse{
			Source:         params.BusinessName,
			SourceName:     "业务系统",
			Target:         ip,
			TargetName:     allMap[ip].SourceName,
			TargetPort:     allMap[ip].SourcePort,
			TargetDomain:   allMap[ip].SourceDomain,
			TargetUrl:      allMap[ip].SourceUrl,
			TargetProtocol: allMap[ip].SourceProtocol,
		})
	}

	// 构建返回
	nodeData := make([]response.BusinessNodeData, 0, len(links)+1)
	// 业务系统节点
	nodeData = append(nodeData, response.BusinessNodeData{
		Name:   params.BusinessName,
		IpKind: "业务系统",
	})
	for _, node := range ipList {
		ip := node["ip"]
		t := allMap[ip].SourceName
		if ip == allMap[ip].Target {
			t = allMap[ip].TargetName
		}
		nodeData = append(nodeData, response.BusinessNodeData{
			Name:      ip,
			IpKind:    t,
			VulnCount: res[ip],
		})

	}

	return &response.BusinessMappingGplotResponse{
		Links: links,
		Data:  nodeData,
	}, nil

}

// getIPs 从资产索引中获取指定类型的IP列表
func getIPs(keyword, queryType string) (map[string]int64, error) {
	// 查业务系统
	q, err := buildBaseQuery(keyword, queryType)
	if err != nil {
		return nil, err
	}
	// 构建嵌套查询
	boolQuery := elastic.NewBoolQuery()
	boolQuery.Must(q)
	// 去掉无用的数据
	boolQuery.MustNot(elastic.NewExistsQuery("deleted_at"))
	boolQuery.MustNot(elastic.NewExistsQuery("purged_at"))

	// 聚合获取IP列表
	ipAgg := elastic.NewTermsAggregation().Field("ip").Size(10000).
		SubAggregation("poc_sum", elastic.NewSumAggregation().Field("poc_num"))

	searchResult, err := es.GetEsClient().Search().
		Index(assets.NewAssets().IndexName()).
		Query(boolQuery).
		Size(0).
		Aggregation("ip_agg", ipAgg).
		Do(context.Background())

	if err != nil {
		return nil, fmt.Errorf("查询资产索引失败: %v", err)
	}
	ips := make(map[string]int64)
	// 提取ip和漏洞数量
	if agg, found := searchResult.Aggregations.Terms("ip_agg"); found {
		for _, bucket := range agg.Buckets {
			ip := bucket.Key.(string)
			pocSum, _ := bucket.Aggregations.Sum("poc_sum")
			count := int64(0)
			if pocSum != nil && pocSum.Value != nil {
				count = int64(*pocSum.Value)
			}
			ips[ip] = count

		}
	}

	return ips, nil
}

// buildBaseQuery 根据查询类型构建基础查询
func buildBaseQuery(keyword string, queryType string) (elastic.Query, error) {
	switch queryType {
	case Ip:
		return elastic.NewTermQuery("ip", keyword), nil

	case IpPort:
		parts := strings.Split(keyword, ":")
		if len(parts) != 2 {
			return nil, errors.New("IP:Port 格式错误")
		}
		ip := parts[0]
		port, _ := strconv.Atoi(parts[1])

		innerQuery := elastic.NewBoolQuery().
			Must(
				elastic.NewTermQuery("ip", ip),
				elastic.NewTermQuery("ports.port", port),
			)
		return innerQuery, nil

	case Domain:
		return elastic.NewNestedQuery("ports",
			elastic.NewTermQuery("ports.domain", keyword),
		), nil

	case Business:
		return elastic.NewNestedQuery("business",
			elastic.NewTermQuery("business.system", keyword),
		), nil

	default:
		return nil, errors.New("未知的查询类型")
	}
}

// bfsNode BFS节点
type bfsNode struct {
	IP   string
	Port string
}

func keyOf(ip, port string) string {
	if port == "" {
		return ip
	}
	return ip + ":" + port
}

// buildMappingGplotFromIPs 通过ip列表构图
func buildMappingGplotFromIPs(ctx context.Context, ips []string, maxEdges int) (MappingGplot, []map[string]string, error) {
	if len(ips) == 0 {
		return MappingGplot{}, make([]map[string]string, 0), nil
	}
	// 最大边
	if maxEdges <= 0 {
		maxEdges = 1000
	}

	mg := MappingGplot{}

	// 队列
	queue := make([]bfsNode, 0, len(ips)*2)
	head := 0

	// 已出队
	out := make(map[string]struct{}, len(ips)*4)
	// 入队
	vis := make(map[string]struct{}, len(ips)*4)

	// 节点
	nodeList := make([]map[string]string, 0, 128)
	savedIP := make(map[string]struct{}, 128)

	// 缓存查询结果
	cache := make(map[string][]*net_mapping.NetMapping, 32)

	// 入队
	for _, ip := range ips {
		k := keyOf(ip, "")
		// 跳过已入队的节点
		if _, ok := vis[k]; ok {
			continue
		}
		queue = append(queue, bfsNode{IP: ip})
		vis[k] = struct{}{}
	}

	edges := 0

	for head < len(queue) && edges < maxEdges {

		// 取bfs头节点
		n := queue[head]
		head++

		k := keyOf(n.IP, n.Port)
		// 跳过已处理的节点
		if _, done := out[k]; done {
			continue
		}
		// 标记
		out[k] = struct{}{}

		// 是否缓存
		netMappings, ok := cache[k]
		if !ok {
			nm, err := GetNetMap(n.IP, n.Port)
			if err != nil {
				return MappingGplot{}, nil, err
			}
			netMappings = nm
			// 缓存
			cache[k] = nm
		}

		// 处理映射
		for _, m := range netMappings {
			if edges >= maxEdges {
				break
			}

			if !mg.Add(m) {
				continue // 已存在边
			}
			edges++

			// 节点信息
			nodeList, savedIP = recordNode(m, nodeList, savedIP)

			// 入队继续
			if edges < maxEdges {
				// from
				fk := keyOf(m.FromIp, m.FromPort)
				if _, ok := out[fk]; !ok {
					if _, eq := vis[fk]; !eq {
						queue = append(queue, bfsNode{IP: m.FromIp, Port: m.FromPort})
						vis[fk] = struct{}{}
					}
				}
				// to
				tk := keyOf(m.ToIp, m.ToPort)
				if _, ok := out[tk]; !ok {
					if _, eq := vis[tk]; !eq {
						queue = append(queue, bfsNode{IP: m.ToIp, Port: m.ToPort})
						vis[tk] = struct{}{}
					}
				}
			}
		}
	}

	return mg, nodeList, nil
}

// recordNode 记录节点信息
func recordNode(m *net_mapping.NetMapping, list []map[string]string, saved map[string]struct{}) ([]map[string]string, map[string]struct{}) {
	// from
	if _, ok := saved[m.FromIp]; !ok {
		list = append(list, map[string]string{"ip": m.FromIp})
		saved[m.FromIp] = struct{}{}
	}
	if _, ok := saved[m.ToIp]; !ok {
		list = append(list, map[string]string{"ip": m.ToIp})
		saved[m.ToIp] = struct{}{}
	}
	return list, saved
}

// GetNetMap 获取映射
func GetNetMap(ip, port string) (nm []*net_mapping.NetMapping, err error) {
	if port == "" {

		nm, err = net_mapping.NewNetMappingModel().ListByOpt(
			mysql.WithWhere("from_ip = ?", ip),
			mysql.WithOrWhere("to_ip = ?", ip),
		)
		return
	}

	nm, err = net_mapping.NewNetMappingModel().ListByOpt(
		mysql.WithWhere("from_ip = ? AND from_port = ?", ip, port),
		mysql.WithOrWhere("to_ip = ? AND to_port = ?", ip, port),
	)
	return
}
