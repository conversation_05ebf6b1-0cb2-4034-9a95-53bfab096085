package net_mapping

import (
	"bufio"
	"errors"
	"fmt"
	request "fobrain/fobrain/app/request/net_mapping"
	response "fobrain/fobrain/app/response/net_mapping"
	"fobrain/fobrain/logs"
	"fobrain/initialize/es"
	"fobrain/initialize/mysql"
	esmodel "fobrain/models/elastic/assets"
	"fobrain/models/mysql/net_mapping"
	"fobrain/pkg/cfg"
	"fobrain/pkg/utils"
	"io"
	"path/filepath"
	"regexp"
	"strings"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/olivere/elastic/v7"
	"github.com/spf13/cast"
	"github.com/xuri/excelize/v2"
	"golang.org/x/net/context"
)

func Create(params *request.CreateNetMappingRequest) error {
	if !checkIp(params.FromIp, params.ToIp) {
		return errors.New("IP格式不正确")
	}
	netMapping := &net_mapping.NetMapping{
		DataSource:   "人工录入",
		BatchNo:      uuid.New().String(),
		FromArea:     params.FromArea,
		FromIp:       params.FromIp,
		FromPort:     cast.ToString(params.FromPort),
		FromDomain:   params.FromDomain,
		FromUrl:      params.FromUrl,
		FromProtocol: params.FromProtocol,
		ToArea:       params.ToArea,
		ToIp:         params.ToIp,
		ToPort:       cast.ToString(params.ToPort),
		ToDomain:     params.ToDomain,
		ToUrl:        params.ToUrl,
		ToProtocol:   params.ToProtocol,
	}
	err := markNetMappingListAlarm([]*net_mapping.NetMapping{netMapping}, nil)
	if err != nil {
		return err
	}
	return net_mapping.NewNetMappingModel().Create(netMapping)
}

func Import(c *gin.Context, params *request.ImportNetMappingRequest) (string, error) {
	fromArea, err := net_mapping.NewNetMappingAreaModel().First(mysql.WithWhere("`name` = ?", params.FromMapping))
	if err != nil || fromArea == nil {
		return "查询区域1失败", errors.New("查询区域1失败")
	}
	toArea, err := net_mapping.NewNetMappingAreaModel().First(mysql.WithWhere("`name` = ?", params.ToMapping))
	if err != nil || toArea == nil {
		return "查询区域2失败", errors.New("查询区域2失败")
	}

	file, err := c.FormFile("file")
	if err != nil {
		return "文件打开错误", err
	}
	if file.Size > 10*1024*1024 {
		return "内外网关系映射文件文件大小不能大于10M", errors.New("内外网关系映射文件文件大小不能大于10M")
	}
	// 本地文件名
	localFileName := fmt.Sprintf("%s-%s", time.Now().Format("20060102150405"), file.Filename)
	// 将文件保存到服务器本地
	storagePath := cfg.LoadCommon().StoragePath
	if err := c.SaveUploadedFile(file, filepath.Join(storagePath, "/app/ip_mapping/", localFileName)); err != nil {
		return "保存文件失败", err
	}
	f, err := file.Open()
	if err != nil {
		return "打开文件失败", err
	}
	defer f.Close()
	// 记录审计数据
	successCount := int64(0)
	msg := ""
	batchNo := uuid.New().String()
	auditLog := &net_mapping.NetMappingAuditLog{
		BatchNo:         batchNo,
		FileName:        file.Filename,
		LocalFileName:   localFileName,
		OperationStatus: "成功",
		Operation:       "导入",
	}

	if params.FileType == 1 {
		auditLog.FileType = "模板文件"
		fileContent, err := excelize.OpenReader(f)
		if err != nil {
			return "打开文件失败", err
		}
		//读取Sheet1的数据
		rows, err := fileContent.GetRows("Sheet1", excelize.Options{RawCellValue: true})
		if err != nil {
			return "读取表格失败", err
		}
		for index, _ := range rows {
			// 补全空值，确保每行有 10 列
			for len(rows[index]) < 10 {
				rows[index] = append(rows[index], "")
			}
		}
		if len(rows) == 0 {
			auditLog.OperationStatus = "失败"
			auditLog.FailReason = "文件内容不能为空"
			err := net_mapping.NewNetMappingAuditLogModel().Create(c, auditLog)
			if err != nil {
				return "创建审计记录失败", err
			}
			return "文件内容不能为空", errors.New("文件内容不能为空")
		}
		if len(rows) > 300*1000 {
			auditLog.OperationStatus = "失败"
			auditLog.FailReason = "单次文件同步数量不能大于30w数据"
			err := net_mapping.NewNetMappingAuditLogModel().Create(c, auditLog)
			if err != nil {
				return "创建审计记录失败", err
			}
			return "单次文件同步数量不能大于30w数据", errors.New("单次文件同步数量不能大于30w数据")
		}
		successCount, msg, err = ImportNetMappingByTemplate(batchNo, "模板文件", rows, fromArea.Id, toArea.Id)
	} else if params.FileType == 2 {
		auditLog.FileType = "华为防火墙配置文件"
		// 打开文件
		scanner := bufio.NewScanner(f)
		lines := make([]string, 0)
		for scanner.Scan() {
			line := strings.TrimSpace(scanner.Text())
			if strings.HasPrefix(line, "nat server") {
				lines = append(lines, line)
			}
		}
		if len(lines) == 0 {
			auditLog.OperationStatus = "失败"
			auditLog.FailReason = "不能解析到符合格式的数据"
			err := net_mapping.NewNetMappingAuditLogModel().Create(c, auditLog)
			if err != nil {
				return "创建审计记录失败", err
			}
			return "不能解析到符合格式的数据", errors.New("不能解析到符合格式的数据")
		}
		successCount, msg, err = ImportNetMappingByHuaweiConfig(batchNo, "华为防火墙配置文件", lines, fromArea.Id, toArea.Id)
	} else if params.FileType == 3 {
		auditLog.FileType = "A10数据源"
		datas := BuildA10Data(f)
		successCount, msg, err = ImportNetMappingByA10(batchNo, "A10数据源", datas, fromArea.Id, toArea.Id)
	} else if params.FileType == 4 {
		auditLog.FileType = "Fortinet防火墙数据源"
		content, _ := io.ReadAll(f)
		successCount, msg, err = ImportNetMappingByFortinet(batchNo, "Fortinet防火墙数据源", string(content), fromArea.Id, toArea.Id)
	} else if params.FileType == 5 {
		auditLog.FileType = "华为防火墙8000E-X8配置文件"
		// 打开文件
		scanner := bufio.NewScanner(f)
		lines := make([]string, 0)
		for scanner.Scan() {
			line := strings.TrimSpace(scanner.Text())
			if strings.HasPrefix(line, "nat server") {
				lines = append(lines, line)
			}
		}
		if len(lines) == 0 {
			auditLog.OperationStatus = "失败"
			auditLog.FailReason = "不能解析到符合格式的数据"
			err := net_mapping.NewNetMappingAuditLogModel().Create(c, auditLog)
			if err != nil {
				return "创建审计记录失败", err
			}
			return "不能解析到符合格式的数据", errors.New("不能解析到符合格式的数据")
		}
		successCount, msg, err = ImportNetMappingByHuaweiFWConfig(batchNo, "华为防火墙8000E", lines, fromArea.Id, toArea.Id)
	} else if params.FileType == 6 {
		auditLog.FileType = "启明星辰负载均衡配置文件"
		successCount, msg, err = ImportNetMappingByQiMingXingChen(f, batchNo, "启明星辰负载均衡", fromArea.Id, toArea.Id)
	} else if params.FileType == 7 {
		auditLog.FileType = "Ctrix负载均衡配置文件"
		successCount, msg, err = ImportNetMappingByCtrix(f, batchNo, "Ctrix负载均衡配置文件", fromArea.Id, toArea.Id)
	} else if params.FileType == 8 {
		auditLog.FileType = "天融信防火墙映射文件"
		successCount, msg, err = ImportNetMappingByTopsecFW(f, batchNo, "天融信防火墙映射文件", fromArea.Id, toArea.Id)
	} else if params.FileType == 9 {
		auditLog.FileType = "深信服应用负载JSON文件"
		successCount, msg, err = ImportNetMappingByTopsecLoadJSON(f, batchNo, "深信服应用负载JSON文件", fromArea.Id, toArea.Id)
	}
	// 失败也有可能存在保存成功的数据
	auditLog.EffectCount = int(successCount)
	if err != nil {
		auditLog.OperationStatus = "失败"
		auditLog.FailReason = msg + err.Error()
	} else {
		auditLog.OperationStatus = "成功"
	}
	err = net_mapping.NewNetMappingAuditLogModel().Create(c, auditLog)
	if err != nil {
		return "创建审计记录失败", err
	}
	return msg, nil
}

func ImportConfirm(c *gin.Context, params *request.ImportNetMappingConfirmRequest) (*response.ImportNetMappingConfirmResponse, error) {
	file, err := c.FormFile("file")
	if err != nil {
		return nil, err
	}
	if file.Size > 10*1024*1024 {
		return nil, errors.New("内外网关系映射文件文件大小不能大于10M")
	}
	// 本地文件名
	localFileName := fmt.Sprintf("%s-%s", time.Now().Format("20060102150405"), file.Filename)
	// 将文件保存到服务器本地
	storagePath := cfg.LoadCommon().StoragePath
	if err := c.SaveUploadedFile(file, filepath.Join(storagePath, "/app/ip_mapping/", localFileName)); err != nil {
		return nil, err
	}
	f, err := file.Open()
	if err != nil {
		return nil, err
	}
	defer f.Close()

	parts := strings.Split(params.Fields, ",")
	if len(parts) != 2 {
		return nil, errors.New("参数错误")
	}

	fromArea, err := net_mapping.NewNetMappingAreaModel().First(mysql.WithWhere("`name` = ?", parts[0]))
	if err != nil || fromArea == nil {
		return nil, errors.New("查询区域1失败")
	}
	toArea, err := net_mapping.NewNetMappingAreaModel().First(mysql.WithWhere("`name` = ?", parts[1]))
	if err != nil || toArea == nil {
		return nil, errors.New("查询区域2失败")
	}

	internetFields := []map[string]string{
		{
			"type":     "ip",
			"label":    fromArea.Name + "IP",
			"show_key": "from_ip",
		},
		{
			"type":     "port",
			"label":    fromArea.Name + "端口",
			"show_key": "from_port",
		},
		{
			"type":     "domain",
			"label":    fromArea.Name + "域名",
			"show_key": "from_domain",
		},
		{
			"type":     "protocol",
			"label":    fromArea.Name + "协议",
			"show_key": "from_protocol",
		},
		{
			"type":     "url",
			"label":    fromArea.Name + "URL",
			"show_key": "from_url",
		},
	}

	intranetFields := []map[string]string{
		{
			"type":     "ip",
			"label":    toArea.Name + "IP",
			"show_key": "to_ip",
		},
		{
			"type":     "port",
			"label":    toArea.Name + "端口",
			"show_key": "to_port",
		},
		{
			"type":     "domain",
			"label":    toArea.Name + "域名",
			"show_key": "to_domain",
		},
		{
			"type":     "protocol",
			"label":    toArea.Name + "协议",
			"show_key": "to_protocol",
		},
		{
			"type":     "url",
			"label":    toArea.Name + "URL",
			"show_key": "to_url",
		},
	}

	var netMappingList []*net_mapping.NetMapping

	if params.FileType == 1 {
		fileContent, err := excelize.OpenReader(f)
		if err != nil {
			return nil, err
		}
		//读取Sheet1的数据
		rows, err := fileContent.GetRows("Sheet1", excelize.Options{RawCellValue: true})
		if err != nil {
			return nil, err
		}
		// GetRows默认会去除末尾的空列，补全空列
		for index, _ := range rows {
			// 补全空值，确保每行有 10 列
			for len(rows[index]) < 10 {
				rows[index] = append(rows[index], "")
			}
		}
		netMappingList, err = ImportNetMappingByTemplateConfirm(rows, fromArea.Id, toArea.Id)
	} else if params.FileType == 2 {
		// 打开文件
		scanner := bufio.NewScanner(f)
		lines := make([]string, 0)
		for scanner.Scan() {
			line := strings.TrimSpace(scanner.Text())
			if strings.HasPrefix(line, "nat server") {
				lines = append(lines, line)
			}
		}
		if len(lines) == 0 {
			return nil, errors.New("不能解析到符合格式的数据")
		}
		netMappingList, err = ImportNetMappingByHuaweiConfigConfirm(lines, fromArea.Id, toArea.Id)
	} else if params.FileType == 3 {
		datas := BuildA10Data(f)
		netMappingList, err = ImportNetMappingByA10Confirm(datas, fromArea.Id, toArea.Id)
	} else if params.FileType == 4 {
		content, _ := io.ReadAll(f)
		netMappingList, err = ImportNetMappingByFortinetConfirm(string(content), fromArea.Id, toArea.Id)
	} else if params.FileType == 5 {
		// 打开文件
		scanner := bufio.NewScanner(f)
		lines := make([]string, 0)
		for scanner.Scan() {
			line := strings.TrimSpace(scanner.Text())
			if strings.HasPrefix(line, "nat server") {
				lines = append(lines, line)
			}
		}
		if len(lines) == 0 {
			return nil, errors.New("不能解析到符合格式的数据")
		}
		netMappingList, err = ImportNetMappingByHuaweiFWConfigConfirm(lines, fromArea.Id, toArea.Id)
	} else if params.FileType == 6 {
		content, _ := io.ReadAll(f)
		netMappingList, err = ImportNetMappingByQingMingXingChenConfirm(string(content), fromArea.Id, toArea.Id)
	}
	if len(netMappingList) == 0 {
		return nil, errors.New("解析不到数据")
	}
	res := &response.ImportNetMappingConfirmResponse{
		Custom1Fields: internetFields,
		Custom2Fields: intranetFields,
		Items:         netMappingList,
		FieldsName: map[string]string{
			"custom1_fields": fromArea.Name,
			"custom2_fields": toArea.Name,
		},
	}
	return res, nil
}

func ImportNetMappingByQingMingXingChenConfirm(content string, fromArea, toArea uint64) ([]*net_mapping.NetMapping, error) {
	// 创建结果列表
	netMappings := make([]*net_mapping.NetMapping, 0)

	// 按行分割配置文件内容
	lines := strings.Split(content, "\n")

	// 定义正则表达式来匹配 vs 配置行
	vsRegex := regexp.MustCompile(`vs\s+([^\s]+)\s+address\s+([\d.]+)\s+protocol\s+([^\s]+)\s+port\s+(\d+)\s+type\s+([^\s]+)\s+(\d+)`)

	// 定义正则表达式来匹配 default-pool 配置行
	poolRegex := regexp.MustCompile(`\s+default-pool\s+([^\s]+)`)

	// 定义正则表达式来匹配 member 配置行
	memberRegex := regexp.MustCompile(`member\s+ip\s+([\d.]+)\s+port\s+(\d+)\s+pool\s+([^\s]+)`)

	// 存储 vs 名称与其外网 IP 和端口的映射
	vsMap := make(map[string]struct {
		Name     string
		IP       string
		Port     string
		Protocol string
		PoolName string
	})

	// 存储 pool 名称与其内网 IP 和端口的映射
	poolMap := make(map[string][]struct {
		IP   string
		Port string
	})

	// 第一遍扫描：提取所有 vs 配置和 pool 名称
	var currentVS string
	for _, line := range lines {
		// 匹配 vs 行
		vsMatches := vsRegex.FindStringSubmatch(line)
		if len(vsMatches) > 6 {
			currentVS = vsMatches[1]
			vsMap[currentVS] = struct {
				Name     string
				IP       string
				Port     string
				Protocol string
				PoolName string
			}{
				Name:     vsMatches[1],
				IP:       vsMatches[2],
				Protocol: vsMatches[3],
				Port:     vsMatches[4],
			}
			continue
		}

		// 匹配 default-pool 行
		if currentVS != "" {
			poolMatches := poolRegex.FindStringSubmatch(line)
			if len(poolMatches) > 1 {
				vs := vsMap[currentVS]
				vs.PoolName = poolMatches[1]
				vsMap[currentVS] = vs
			}
		}
	}

	// 第二遍扫描：提取所有 member 配置
	for _, line := range lines {
		memberMatches := memberRegex.FindStringSubmatch(line)
		if len(memberMatches) > 3 {
			poolName := memberMatches[3]
			poolMap[poolName] = append(poolMap[poolName], struct {
				IP   string
				Port string
			}{
				IP:   memberMatches[1],
				Port: memberMatches[2],
			})
		}
	}

	// 第三遍处理：关联 vs 和 member，生成映射关系
	for _, vs := range vsMap {
		if vs.PoolName == "" {
			continue
		}

		members, ok := poolMap[vs.PoolName]
		if !ok || len(members) == 0 {
			continue
		}

		// 最多取前5个成员作为预览
		count := 0
		for _, member := range members {
			if count >= 5 {
				break
			}
			if len(vs.IP) > 16 || len(member.IP) > 16 {
				continue
			}
			// 创建网络映射对象
			netMapping := &net_mapping.NetMapping{
				FromArea: fromArea,
				FromIp:   vs.IP,
				FromPort: vs.Port,
				ToArea:   toArea,
				ToIp:     member.IP,
				ToPort:   member.Port,
			}

			// 添加到结果列表
			netMappings = append(netMappings, netMapping)
			count++
		}
	}

	return netMappings, nil
}

func Delete(c *gin.Context, params *request.DeleteNetMappingRequest) (string, error) {
	deletedDatas, err := net_mapping.NewNetMappingModel().Delete(c, params.Ids, params.Keyword)
	if err != nil {
		return "数据删除失败", err
	}
	batchNo := uuid.New().String()
	netMappingAuditLog := &net_mapping.NetMappingAuditLog{
		BatchNo:         batchNo,
		Operation:       "删除",
		OperationStatus: "成功",
		EffectCount:     len(deletedDatas),
	}
	err = net_mapping.NewNetMappingAuditLogModel().Create(c, netMappingAuditLog)
	if err != nil {
		return "删除数据成功，保存审计记录失败", err
	}
	if len(deletedDatas) > 0 {
		netMappingAuditDataList := make([]*net_mapping.NetMappingAuditData, 0)
		for _, data := range deletedDatas {
			netMappingAuditData := &net_mapping.NetMappingAuditData{
				DataId:       data.Id,
				BatchNo:      batchNo,
				DataSource:   data.DataSource,
				FromArea:     data.FromArea,
				FromIp:       data.FromIp,
				FromPort:     data.FromPort,
				FromDomain:   data.FromDomain,
				FromUrl:      data.FromUrl,
				FromProtocol: data.FromProtocol,
				ToArea:       data.ToArea,
				ToIp:         data.ToIp,
				ToPort:       data.ToPort,
				ToDomain:     data.ToDomain,
				ToUrl:        data.ToUrl,
				ToProtocol:   data.ToProtocol,
			}
			netMappingAuditDataList = append(netMappingAuditDataList, netMappingAuditData)
		}
		err := net_mapping.NewNetMappingAuditDataModel().CreateBatch(c, netMappingAuditDataList)
		if err != nil {
			return "数据删除成功,保存审计数据失败", err
		}
	}
	return fmt.Sprintf("删除成功,删除数量:%d", len(deletedDatas)), nil
}

func List(params *request.ListNetMappingRequest) ([]*response.ListNetMappingResponse, int64, error) {
	areaIdNameMap, err := net_mapping.NewNetMappingAreaModel().GetIdNameMap()
	if err != nil {
		return nil, 0, err
	}
	data, total, err := net_mapping.NewNetMappingModel().ListByPage(params.Page, params.PerPage, params.Keyword)
	if err != nil {
		return nil, 0, err
	}
	var res []*response.ListNetMappingResponse
	for _, item := range data {
		t := &response.ListNetMappingResponse{
			Id:           item.Id,
			DataSource:   item.DataSource,
			FromArea:     areaIdNameMap[item.FromArea],
			FromIp:       item.FromIp,
			FromPort:     item.FromPort,
			FromDomain:   item.FromDomain,
			FromUrl:      item.FromUrl,
			FromProtocol: item.FromProtocol,
			ToArea:       areaIdNameMap[item.ToArea],
			ToIp:         item.ToIp,
			ToPort:       item.ToPort,
			ToDomain:     item.ToDomain,
			ToUrl:        item.ToUrl,
			ToProtocol:   item.ToProtocol,
		}
		if item.FromExistAlarm == 1 {
			t.FromExistAlarm = true
		}
		if item.ToExistAlarm == 1 {
			t.ToExistAlarm = true
		}
		res = append(res, t)
	}
	return res, total, nil
}

func CreateTemplateFile(params *request.DownloadTemplateRequest) (string, error) {
	filePath := time.Now().Format("20060102150405") + "内外网映射关系模板.xlsx"
	filePath, err := utils.WriterExcel(filePath, []string{
		params.FromArea + "IP",
		params.FromArea + "端口",
		params.FromArea + "域名",
		params.FromArea + "URL",
		params.FromArea + "协议",
		params.ToArea + "IP",
		params.ToArea + "端口",
		params.ToArea + "域名",
		params.ToArea + "URL",
		params.ToArea + "协议",
	}, [][]interface{}{})
	if err != nil {
		return "", err
	}
	return filePath, nil
}

func ListAuditData(c *gin.Context, params *request.ListAuditDataRequest) ([]*response.ListNetMappingResponse, int64, error) {
	areaIdNameMap, err := net_mapping.NewNetMappingAreaModel().GetIdNameMap()
	if err != nil {
		return nil, 0, err
	}
	data, total, err := net_mapping.NewNetMappingAuditDataModel().List(c, params.BatchNo, params.Page, params.PerPage)
	if err != nil {
		return nil, 0, err
	}
	var res []*response.ListNetMappingResponse
	for _, item := range data {
		res = append(res, &response.ListNetMappingResponse{
			Id:           item.Id,
			DataSource:   item.DataSource,
			FromArea:     areaIdNameMap[item.FromArea],
			FromIp:       item.FromIp,
			FromPort:     item.FromPort,
			FromDomain:   item.FromDomain,
			FromUrl:      item.FromUrl,
			FromProtocol: item.FromProtocol,
			ToArea:       areaIdNameMap[item.ToArea],
			ToIp:         item.ToIp,
			ToPort:       item.ToPort,
			ToDomain:     item.ToDomain,
			ToUrl:        item.ToUrl,
			ToProtocol:   item.ToProtocol,
		})
	}
	return res, total, nil
}

// CheckAlarm 检查告警数据
func CheckAlarm() error {
	page := 1
	perPage := 100
	handleCount := int64(0)
	wg := &sync.WaitGroup{}
	var globalErr error
	for {
		netMappingList, total, err := net_mapping.NewNetMappingModel().ListByPage(page, perPage, "")
		if err != nil {
			return err
		}

		handleCount += int64(len(netMappingList))
		page++
		wg.Add(1)

		go func(globalErr error) {
			err := markNetMappingListAlarm(netMappingList, wg)
			if err != nil {
				globalErr = err
			}
			err = net_mapping.NewNetMappingModel().UpdateBatch(netMappingList)
			if err != nil {
				globalErr = err
			}
		}(globalErr)

		if handleCount >= total {
			break
		}

		if len(netMappingList) < perPage {
			break
		}
	}
	wg.Wait()
	return globalErr
}
func markNetMappingListAlarm(netMappingList []*net_mapping.NetMapping, wg *sync.WaitGroup) error {
	if wg != nil {
		defer wg.Done()
	}
	assetQuery := elastic.NewBoolQuery()
	subQueryList := make([]elastic.Query, 0)
	for _, netMapping := range netMappingList {
		fromQuery := elastic.NewBoolQuery()
		fromQuery.Must(elastic.NewTermsQuery("ip", netMapping.FromIp))
		if netMapping.FromPort != "" {
			fromQuery.Must(elastic.NewTermsQuery("ports.port", netMapping.FromPort))
		}
		toQuery := elastic.NewBoolQuery()
		toQuery.Must(elastic.NewTermsQuery("ip", netMapping.ToIp))
		if netMapping.ToPort != "" {
			toQuery.Must(elastic.NewTermsQuery("ports.port", netMapping.ToPort))
		}
		subQueryList = append(subQueryList, fromQuery, toQuery)
	}
	assetQuery.Should(subQueryList...).MinimumShouldMatch("1")
	assetList, err := es.All[esmodel.Assets](100, assetQuery, nil, "id", "ip", "ports.port", "ports.domain")
	if err != nil {
		return err
	}
	for _, netMapping := range netMappingList {
		markAlarm(netMapping, assetList)
	}
	return nil
}
func markAlarm(netMapping *net_mapping.NetMapping, assetList []*esmodel.Assets) {
	netMapping.FromExistAlarm = 0
	netMapping.ToExistAlarm = 0
	for _, asset := range assetList {
		if asset.Ip == netMapping.FromIp {
			for _, assetPort := range asset.Ports {
				if cast.ToString(assetPort.Port) == netMapping.FromPort {
					netMapping.FromExistAlarm = 1
					if assetPort.Domain != "" {
						if netMapping.FromDomain != "" {
							if !utils.InArray(assetPort.Domain, netMapping.GetFromDomainList()) {
								netMapping.FromDomain = fmt.Sprintf("%s,%s", netMapping.FromDomain, assetPort.Domain)
							}
						} else {
							netMapping.FromDomain = assetPort.Domain
						}
					}
				}
			}
		}
		if asset.Ip == netMapping.ToIp {
			for _, assetPort := range asset.Ports {
				if cast.ToString(assetPort.Port) == netMapping.ToPort {
					netMapping.ToExistAlarm = 1
					if assetPort.Domain != "" {
						if netMapping.ToDomain != "" {
							if !utils.InArray(assetPort.Domain, netMapping.GetToDomainList()) {
								netMapping.ToDomain = fmt.Sprintf("%s,%s", netMapping.ToDomain, assetPort.Domain)
							}
						} else {
							netMapping.ToDomain = assetPort.Domain
						}
					}
				}
			}
		}
	}
}

// SaveData 保存映射数据和审计数据
func SaveData(ch chan *net_mapping.NetMapping, wg *sync.WaitGroup, successCount *int64) {
	defer wg.Done()
	logger := logs.GetLogger()
	netMappingList := make([]*net_mapping.NetMapping, 0)
	auditDataList := make([]*net_mapping.NetMappingAuditData, 0)
	count := 0
	for netMapping := range ch {
		count++
		// fmt.Println("-------------", count)
		// 添加映射数据
		netMappingList = append(netMappingList, netMapping)
		auditDataList = append(auditDataList, &net_mapping.NetMappingAuditData{
			BatchNo:      netMapping.BatchNo,
			DataSource:   netMapping.DataSource,
			FromArea:     netMapping.FromArea,
			FromIp:       netMapping.FromIp,
			FromPort:     netMapping.FromPort,
			FromDomain:   netMapping.FromDomain,
			FromUrl:      netMapping.FromUrl,
			FromProtocol: netMapping.FromProtocol,
			ToArea:       netMapping.ToArea,
			ToIp:         netMapping.ToIp,
			ToPort:       netMapping.ToPort,
			ToDomain:     netMapping.ToDomain,
			ToUrl:        netMapping.ToUrl,
			ToProtocol:   netMapping.ToProtocol,
		})
		if len(netMappingList) >= 1000 || len(auditDataList) >= 1000 {
			rowsAffected, err := net_mapping.NewNetMappingModel().CreateBatch(netMappingList)
			if err != nil {
				logger.Error("保存映射关系失败,err:%v", err)
			}
			err = net_mapping.NewNetMappingAuditDataModel().CreateBatch(context.Background(), auditDataList)
			if err != nil {
				logger.Error("保存审计数据失败,err:%v", err)
			}
			*successCount += rowsAffected
			netMappingList = make([]*net_mapping.NetMapping, 0)
			auditDataList = make([]*net_mapping.NetMappingAuditData, 0)
		}
	}
	if len(netMappingList) > 0 || len(auditDataList) > 0 {
		fmt.Println("-------len(netMappingList), len(auditDataList)------", len(netMappingList), len(auditDataList))
		rowsAffected, err := net_mapping.NewNetMappingModel().CreateBatch(netMappingList)
		if err != nil {
			logger.Error("保存映射关系失败,err:%v", err)
		}
		err = net_mapping.NewNetMappingAuditDataModel().CreateBatch(context.Background(), auditDataList)
		if err != nil {
			logger.Error("保存审计数据失败,err:%v", err)
		}
		*successCount += rowsAffected
	}
}

// 检查数据格式
func checkIp(ips ...string) bool {
	for _, ip := range ips {
		if !utils.IsValidIP(ip) {
			return false
		}
	}
	return true
}
func checkPort(ports ...string) bool {
	for _, port := range ports {
		if !utils.IsValidatePort(port) {
			return false
		}
	}
	return true
}
