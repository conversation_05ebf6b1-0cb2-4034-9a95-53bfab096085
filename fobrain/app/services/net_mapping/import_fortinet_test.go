package net_mapping

import (
	"fobrain/models/mysql/net_mapping"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"regexp"
	"sync"
	"testing"
)

func TestImportNetMappingByFortinet(t *testing.T) {
	patches := []*gomonkey.Patches{
		gomonkey.ApplyFuncReturn(parseFortinetConfig, map[string]interface{}{
			"policies": []Policy{
				{
					SrcAddr:  "test",
					PoolName: "**************",
				},
			},
			"addresses": map[string]string{
				"test": "************",
			},
		}, nil),
		gomonkey.ApplyFunc(SaveData, func(ch chan *net_mapping.NetMapping, wg *sync.WaitGroup, successCount *int64) {
			defer wg.Done()
			count := 0
			for range ch {
				count++
			}
			*successCount = int64(count)
		}),
	}
	successCount, msg, err := ImportNetMappingByFortinet(uuid.New().String(), "华为防火墙配置文件", "", uint64(1), uint64(2))
	for _, patch := range patches {
		patch.Reset()
	}
	assert.Equal(t, successCount, int64(1))
	assert.Equal(t, msg, "导入成功，成功解析1条数据，新增1条数据")
	assert.Nil(t, err)
}

func TestImportNetMappingByFortinetConfirm(t *testing.T) {
	patches := []*gomonkey.Patches{
		gomonkey.ApplyFuncReturn(parseFortinetConfig, map[string]interface{}{
			"policies": []Policy{
				{
					SrcAddr:  "test",
					PoolName: "**************",
				},
			},
			"addresses": map[string]string{
				"test": "************",
			},
		}, nil),
	}
	netMapping, err := ImportNetMappingByFortinetConfirm("", uint64(1), uint64(2))
	for _, patch := range patches {
		patch.Reset()
	}
	assert.Equal(t, netMapping[0].FromIp, "************")
	assert.Nil(t, err)
}

func TestParseFortinetConfig(t *testing.T) {
	patches := []*gomonkey.Patches{
		gomonkey.ApplyMethodReturn(&regexp.Regexp{}, "FindAllStringSubmatch", [][]string{
			{"test", "test"},
		}),
		gomonkey.ApplyMethodReturn(&regexp.Regexp{}, "FindAllString", []string{"test"}),
		gomonkey.ApplyMethodReturn(&regexp.Regexp{}, "MatchString", true),
	}
	config := ""
	_, err := parseFortinetConfig(config)
	for _, patch := range patches {
		patch.Reset()
	}
	assert.Nil(t, err)
}
