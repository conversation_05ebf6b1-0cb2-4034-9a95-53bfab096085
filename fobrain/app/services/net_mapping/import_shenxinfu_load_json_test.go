package net_mapping

import (
	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/models/mysql/net_mapping"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/olivere/elastic/v7"
	"strings"
	"sync"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestImportNetMappingByTopsecLoadJSON(t *testing.T) {
	// 初始化 mock ES 客户端
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	// 注册空的搜索响应，避免 ES 查询报错
	mockServer.Register("/.*/_search", []*elastic.SearchHit{})

	// 使用gomonkey模拟SaveData函数，避免实际数据库操作
	patch := gomonkey.ApplyFunc(SaveData, func(ch chan *net_mapping.NetMapping, wg *sync.WaitGroup, successCount *int64) {
		defer wg.Done()
		count := 0
		for range ch {
			count++
		}
		*successCount = int64(count)
	})
	defer patch.Reset()

	// 模拟JSON数据，包含IP和域名节点
	jsonData := `{
		"/slb/virtual-service": [
			{
				"name": "vs-OA_60-1081",
				"description": "测试服务",
				"state": "ENABLE",
				"service": "HTTP",
				"vips": ["*************"],
				"vports": ["1081"],
				"pool": "OA_1801-POOL"
			}
		],
		"/slb/pool": [
			{
				"name": "OA_1801-POOL",
				"description": "测试池",
				"nodes": [
					{
						"name": "node1",
						"description": "IP节点",
						"address": "*************",
						"port": 1081,
						"state": "ENABLE",
						"type": "ADDRESS"
					},
					{
						"name": "node2",
						"description": "域名节点",
						"address": "cnbg-sf02.lims.cnbg.com.cn",
						"port": 1081,
						"state": "ENABLE",
						"type": "DOMAIN"
					}
				]
			}
		]
	}`

	file := strings.NewReader(jsonData)
	batchNo := "test-batch"
	fileType := "深信服负载JSON文件"
	fromArea := uint64(1)
	toArea := uint64(2)

	successCount, msg, err := ImportNetMappingByTopsecLoadJSON(file, batchNo, fileType, fromArea, toArea)

	assert.Nil(t, err)
	assert.Equal(t, int64(2), successCount) // 应该创建2个映射（1个IP节点 + 1个域名节点）
	assert.Contains(t, msg, "成功解析2条数据")
}

func TestImportNetMappingByTopsecLoadJSONWithDomainVIP(t *testing.T) {
	// 初始化 mock ES 客户端
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	// 注册空的搜索响应，避免 ES 查询报错
	mockServer.Register("/.*/_search", []*elastic.SearchHit{})

	// 使用gomonkey模拟SaveData函数，避免实际数据库操作
	patch := gomonkey.ApplyFunc(SaveData, func(ch chan *net_mapping.NetMapping, wg *sync.WaitGroup, successCount *int64) {
		defer wg.Done()
		count := 0
		for range ch {
			count++
		}
		*successCount = int64(count)
	})
	defer patch.Reset()

	// 测试VIP也是域名的情况
	jsonData := `{
		"/slb/virtual-service": [
			{
				"name": "vs-domain-test",
				"description": "域名VIP测试",
				"state": "ENABLE",
				"service": "HTTPS",
				"vips": ["api.example.com"],
				"vports": ["443"],
				"pool": "DOMAIN_POOL"
			}
		],
		"/slb/pool": [
			{
				"name": "DOMAIN_POOL",
				"description": "域名池",
				"nodes": [
					{
						"name": "backend1",
						"description": "后端域名",
						"address": "backend.internal.com",
						"port": 8080,
						"state": "ENABLE",
						"type": "DOMAIN"
					}
				]
			}
		]
	}`

	file := strings.NewReader(jsonData)
	batchNo := "test-domain-batch"
	fileType := "深信服负载JSON文件"
	fromArea := uint64(1)
	toArea := uint64(2)

	successCount, msg, err := ImportNetMappingByTopsecLoadJSON(file, batchNo, fileType, fromArea, toArea)

	assert.Nil(t, err)
	assert.Equal(t, int64(1), successCount) // 应该创建1个映射（域名到域名）
	assert.Contains(t, msg, "成功解析1条数据")
}

func TestExtractProtocolFromService(t *testing.T) {
	assert.Equal(t, "HTTP", extractProtocolFromService("HTTP"))
	assert.Equal(t, "HTTPS", extractProtocolFromService("SSL-OFFLOAD-HTTPS"))
	assert.Equal(t, "TCP", extractProtocolFromService("TCP-FORWARD"))
	assert.Equal(t, "UDP", extractProtocolFromService("UDP-FORWARD"))
	assert.Equal(t, "TCP", extractProtocolFromService("UNKNOWN"))
}

func TestIsDomainName(t *testing.T) {
	// 有效域名
	assert.True(t, isDomainName("cnbg-sf02.lims.cnbg.com.cn"))
	assert.True(t, isDomainName("www.example.com"))
	assert.True(t, isDomainName("api.test-server.org"))
	assert.True(t, isDomainName("sub.domain.example.co.uk"))

	// 无效域名
	assert.False(t, isDomainName(""))                 // 空字符串
	assert.False(t, isDomainName(".example.com"))     // 以点开头
	assert.False(t, isDomainName("example.com."))     // 以点结尾
	assert.False(t, isDomainName("localhost"))        // 没有点
	assert.False(t, isDomainName("example..com"))     // 连续的点
	assert.False(t, isDomainName("example.com/path")) // 包含路径
}
