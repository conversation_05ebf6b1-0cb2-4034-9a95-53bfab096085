package net_mapping

import (
	"fmt"
	"fobrain/models/mysql/net_mapping"
	"regexp"
	"strings"
	"sync"
)

// ImportNetMappingByHuaweiConfig 导入IP映射-华为防火墙配置文件
func ImportNetMappingByHuaweiConfig(batchNo string, fileType string, lines []string, fromArea, toArea uint64) (int64, string, error) {
	// 定义正则表达式来匹配所需的信息
	regex := `global\s+([\d.]+)\s+(\d+)\s+inside\s+([\d.]+)\s+(\w+)`
	re := regexp.MustCompile(regex)

	successCount := int64(0)
	totalCount := int64(0)
	wg := &sync.WaitGroup{}
	netMappingChan := make(chan *net_mapping.NetMapping, 100)

	wg.Add(1)
	go SaveData(netMappingChan, wg, &successCount)

	for _, line := range lines {
		if !strings.HasPrefix(line, "nat server") {
			continue
		}
		matches := re.FindStringSubmatch(line)
		if len(matches) < 5 {
			continue
		}
		netMapping := &net_mapping.NetMapping{
			DataSource: fileType,
			BatchNo:    batchNo,
			FromArea:   fromArea,
			FromIp:     matches[1],
			FromPort:   matches[2],
			ToArea:     toArea,
			ToIp:       matches[3],
			ToPort:     matches[4],
		}
		if !checkIp(matches[1], matches[3]) || !checkPort(matches[2], matches[4]) {
			continue
		}
		netMappingChan <- netMapping
		totalCount++
	}

	close(netMappingChan)
	wg.Wait()
	return successCount, fmt.Sprintf("导入成功，成功解析%d条数据，新增%d条数据", totalCount, successCount), nil
}

// ImportNetMappingByHuaweiConfigConfirm 导入IP映射-华为防火墙配置文件确认
func ImportNetMappingByHuaweiConfigConfirm(lines []string, fromArea, toArea uint64) ([]*net_mapping.NetMapping, error) {
	regex := `global\s+([\d.]+)\s+(\d+)\s+inside\s+([\d.]+)\s+(\w+)`
	re := regexp.MustCompile(regex)
	netMappings := make([]*net_mapping.NetMapping, 0)
	for _, line := range lines {
		if !strings.HasPrefix(line, "nat server") {
			continue
		}
		matches := re.FindStringSubmatch(line)
		if len(matches) < 5 {
			continue
		}
		netMapping := &net_mapping.NetMapping{
			FromArea: fromArea,
			FromIp:   matches[1],
			FromPort: matches[2],
			ToArea:   toArea,
			ToIp:     matches[3],
			ToPort:   matches[4],
		}
		netMappings = append(netMappings, netMapping)
		if !checkIp(matches[1], matches[3]) || !checkPort(matches[2], matches[4]) {
			continue
		}
		if len(netMappings) > 4 {
			break
		}
	}
	return netMappings, nil
}
