package net_mapping

import (
	"encoding/json"
	"fmt"
	"fobrain/models/mysql/net_mapping"
	"io"
	"strconv"
	"strings"
	"sync"
)

// VirtualService 虚拟服务结构体
type VirtualService struct {
	Name        string   `json:"name"`
	Description string   `json:"description"`
	State       string   `json:"state"`
	Service     string   `json:"service"`
	Vips        []string `json:"vips"`
	Vports      []string `json:"vports"`
	Pool        string   `json:"pool"`
}

// PoolNode 池节点结构体
type PoolNode struct {
	Name             string `json:"name"`
	Description      string `json:"description"`
	Address          string `json:"address"`
	Port             int    `json:"port"`
	State            string `json:"state"`
	Type             string `json:"type"`              // ADDRESS 或 DOMAIN
	AssociatedDomain string `json:"associated_domain"` // 关联域名
}

// Pool 池结构体
type Pool struct {
	Name        string     `json:"name"`
	Description string     `json:"description"`
	Nodes       []PoolNode `json:"nodes"`
}

// LoadBalancerConfig 负载均衡配置结构体
type LoadBalancerConfig struct {
	VirtualServices []VirtualService `json:"/slb/virtual-service"`
	Pools           []Pool           `json:"/slb/pool"`
}

// ImportNetMappingByTopsecLoadJSON 解析JSON格式的深信服负载
func ImportNetMappingByTopsecLoadJSON(file io.Reader, batchNo string, fileType string, fromArea, toArea uint64) (int64, string, error) {
	successCount := int64(0)
	totalCount := int64(0)
	wg := &sync.WaitGroup{}
	netMappingChan := make(chan *net_mapping.NetMapping, 100)

	wg.Add(1)
	go SaveData(netMappingChan, wg, &successCount)

	// 读取JSON数据
	data, err := io.ReadAll(file)
	if err != nil {
		return 0, "", fmt.Errorf("读取文件失败: %v", err)
	}

	// 解析JSON
	var config LoadBalancerConfig
	if err := json.Unmarshal(data, &config); err != nil {
		return 0, "", fmt.Errorf("解析JSON失败: %v", err)
	}

	// 创建池名称到池对象的映射
	poolMap := make(map[string]*Pool)
	for i := range config.Pools {
		poolMap[config.Pools[i].Name] = &config.Pools[i]
	}

	// 处理虚拟服务配置
	for _, vs := range config.VirtualServices {
		// 跳过禁用的服务
		if vs.State != "ENABLE" {
			continue
		}

		// 提取协议信息
		protocol := extractProtocolFromService(vs.Service)

		// 查找对应的池
		pool, poolExists := poolMap[vs.Pool]

		// 处理每个VIP和端口组合
		for _, vip := range vs.Vips {
			for _, vport := range vs.Vports {
				// 验证VIP格式（可能是IP或域名）
				var fromIp, fromDomain string
				var isValidVip bool

				if checkIp(vip) {
					// VIP是IP地址
					fromIp = vip
					fromDomain = ""
					isValidVip = true
				} else if isDomainName(vip) {
					// VIP是域名
					fromIp = ""
					fromDomain = vip
					isValidVip = true
				} else {
					isValidVip = false
				}

				// 验证端口格式
				if !isValidVip || !checkPort(vport) {
					continue
				}

				// 如果有对应的池，创建到每个后端节点的映射
				if poolExists && pool != nil {
					for _, node := range pool.Nodes {
						// 处理不同类型的节点
						var toIp, toDomain string
						var isValidAddress bool

						if node.Type == "DOMAIN" {
							// 域名类型节点
							toDomain = node.Address
							toIp = "" // 域名节点不设置IP
							isValidAddress = isDomainName(node.Address)
						} else {
							// IP地址类型节点（默认）
							toIp = node.Address
							toDomain = "" // IP节点不设置域名
							isValidAddress = checkIp(node.Address)
						}

						// 验证地址格式
						if !isValidAddress {
							continue
						}

						// 创建网络映射对象：VIP -> 真实服务器/域名
						mapping := &net_mapping.NetMapping{
							DataSource:   fileType,
							BatchNo:      batchNo,
							FromArea:     fromArea,
							FromIp:       fromIp,     // 虚拟IP地址，如 *************
							FromDomain:   fromDomain, // 虚拟域名（如果VIP是域名）
							FromPort:     vport,      // 虚拟端口，如 1081
							FromProtocol: protocol,
							ToArea:       toArea,
							ToIp:         toIp,                    // 真实服务器IP地址，如 *************
							ToDomain:     toDomain,                // 真实服务器域名，如 cnbg-sf02.lims.cnbg.com.cn
							ToPort:       strconv.Itoa(node.Port), // 真实服务器端口，如 1081
							ToProtocol:   protocol,
						}
						markNetMappingListAlarm([]*net_mapping.NetMapping{mapping}, nil)
						netMappingChan <- mapping
						totalCount++
					}
				}
			}
		}
	}

	close(netMappingChan)
	wg.Wait()
	return successCount, fmt.Sprintf("导入成功，成功解析%d条数据，新增%d条数据", totalCount, successCount), nil
}

// extractProtocolFromService 从服务类型中提取协议
func extractProtocolFromService(service string) string {
	service = strings.ToUpper(service)
	switch {
	case strings.Contains(service, "HTTPS"):
		return "HTTPS"
	case strings.Contains(service, "SSL"):
		return "HTTPS"
	case strings.Contains(service, "HTTP"):
		return "HTTP"
	case strings.Contains(service, "TCP"):
		return "TCP"
	case strings.Contains(service, "UDP"):
		return "UDP"
	default:
		return "TCP" // 默认协议
	}
}

// isDomainName 验证是否为有效的域名
func isDomainName(domain string) bool {
	if domain == "" {
		return false
	}

	// 基本的域名格式验证
	// 域名长度不能超过253个字符
	if len(domain) > 253 {
		return false
	}

	// 域名不能以点开头或结尾
	if strings.HasPrefix(domain, ".") || strings.HasSuffix(domain, ".") {
		return false
	}

	// 域名必须包含至少一个点
	if !strings.Contains(domain, ".") {
		return false
	}

	// 检查是否包含连续的点
	if strings.Contains(domain, "..") {
		return false
	}

	// 检查是否包含路径或其他非域名字符
	if strings.Contains(domain, "/") || strings.Contains(domain, "?") ||
		strings.Contains(domain, "#") || strings.Contains(domain, ":") {
		return false
	}

	// 简单的字符验证：只允许字母、数字、点和连字符
	for _, char := range domain {
		if !((char >= 'a' && char <= 'z') ||
			(char >= 'A' && char <= 'Z') ||
			(char >= '0' && char <= '9') ||
			char == '.' || char == '-') {
			return false
		}
	}

	return true
}
