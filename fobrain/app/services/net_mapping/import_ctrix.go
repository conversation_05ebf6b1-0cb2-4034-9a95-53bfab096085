package net_mapping

import (
	"bufio"
	"fmt"
	"fobrain/models/mysql/net_mapping"
	"io"
	"regexp"
	"strings"
	"sync"
)

func ImportNetMappingByCtrix(file io.Reader, batchNo string, fileType string, fromArea, toArea uint64) (int64, string, error) {
	successCount := int64(0)
	wg := &sync.WaitGroup{}
	netMappingChan := make(chan *net_mapping.NetMapping, 0)

	wg.Add(1)
	go SaveData(netMappingChan, wg, &successCount)

	totalCount := PrepareCtrixData(netMappingChan, file, batchNo, fileType, fromArea, toArea)

	close(netMappingChan)
	wg.Wait()
	return successCount, fmt.Sprintf("导入成功，成功解析%d条数据，新增%d条数据", totalCount, successCount), nil
}

type Endpoint struct {
	IP       string
	Port     string
	Protocol string
}

func PrepareCtrixData(netMapping<PERSON>han chan *net_mapping.NetMapping, file io.Reader, batchNo string, fileType string, fromArea, toArea uint64) int64 {
	totalCount := int64(0)
	serviceMap := make(map[string]Endpoint)
	vserverMap := make(map[string]Endpoint)
	bindings := make(map[string][]string)

	// 正则
	serviceRegex := regexp.MustCompile(`add service (\S+) (\S+) (\S+) (\d+)`)
	vserverRegex := regexp.MustCompile(`add lb vserver (\S+) (\S+) (\S+) (\d+)`)
	bindRegex := regexp.MustCompile(`bind lb vserver (\S+) (\S+)`)

	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		line := scanner.Text()

		if strings.HasPrefix(line, "add service") {
			matches := serviceRegex.FindStringSubmatch(line)
			if len(matches) == 5 {
				serviceMap[matches[1]] = Endpoint{IP: matches[2], Protocol: matches[3], Port: matches[4]}
			}
		} else if strings.HasPrefix(line, "add lb vserver") {
			matches := vserverRegex.FindStringSubmatch(line)
			if len(matches) == 5 {
				vserverMap[matches[1]] = Endpoint{IP: matches[3], Protocol: matches[2], Port: matches[4]}
			}
		} else if strings.HasPrefix(line, "bind lb vserver") {
			matches := bindRegex.FindStringSubmatch(line)
			if len(matches) == 3 {
				bindings[matches[1]] = append(bindings[matches[1]], matches[2])
			}
		}
	}

	for vserver, services := range bindings {
		from := vserverMap[vserver]
		for _, service := range services {
			to := serviceMap[service]
			if len(from.IP) > 16 || len(to.IP) > 16 {
				continue
			}
			mapping := &net_mapping.NetMapping{
				DataSource:     fileType,
				BatchNo:        batchNo,
				FromArea:       fromArea,
				FromIp:         from.IP,
				FromPort:       from.Port,
				FromDomain:     "",
				FromUrl:        "",
				FromProtocol:   from.Protocol,
				FromExistAlarm: 0,
				ToArea:         toArea,
				ToIp:           to.IP,
				ToPort:         to.Port,
				ToDomain:       "",
				ToUrl:          "",
				ToProtocol:     to.Protocol,
				ToExistAlarm:   0,
			}
			markNetMappingListAlarm([]*net_mapping.NetMapping{mapping}, nil)
			netMappingChan <- mapping
			totalCount++

		}
	}
	return totalCount
}
