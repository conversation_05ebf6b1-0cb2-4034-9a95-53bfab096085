package net_mapping

import (
	"fmt"
	"fobrain/models/mysql/net_mapping"
	"sync"
)

// ImportNetMappingByTemplate 导入IP映射-模板文件
func ImportNetMappingByTemplate(batchNo string, fileType string, rows [][]string, fromArea, toArea uint64) (int64, string, error) {
	successCount := int64(0)
	totalCount := int64(0)
	wg := &sync.WaitGroup{}
	netMappingChan := make(chan *net_mapping.NetMapping, 100)

	wg.Add(1)
	go SaveData(netMappingChan, wg, &successCount)

	for i, row := range rows {
		if i < 1 {
			continue
		}
		if len(row) < 10 {
			continue
		}
		netMapping := &net_mapping.NetMapping{
			DataSource:   fileType,
			BatchNo:      batchNo,
			FromArea:     fromArea,
			FromIp:       row[0],
			FromPort:     row[1],
			FromDomain:   row[2],
			FromUrl:      row[3],
			FromProtocol: row[4],
			ToArea:       toArea,
			ToIp:         row[5],
			ToPort:       row[6],
			ToDomain:     row[7],
			ToUrl:        row[8],
			ToProtocol:   row[9],
		}
		if !checkIp(row[0], row[5]) {
			continue
		}
		netMappingChan <- netMapping
		totalCount++
	}
	// 关闭通道
	close(netMappingChan)
	wg.Wait()
	return successCount, fmt.Sprintf("导入成功，成功解析%d条数据，新增%d条数据", totalCount, successCount), nil
}

// ImportNetMappingByTemplateConfirm 导入IP映射-模板文件-确认
func ImportNetMappingByTemplateConfirm(rows [][]string, fromArea, toArea uint64) ([]*net_mapping.NetMapping, error) {
	netMappings := make([]*net_mapping.NetMapping, 0)
	for i, row := range rows {
		if i < 1 {
			continue
		}
		if len(row) < 10 {
			continue
		}

		netMapping := &net_mapping.NetMapping{
			FromArea:     fromArea,
			FromIp:       row[0],
			FromPort:     row[1],
			FromDomain:   row[2],
			FromUrl:      row[3],
			FromProtocol: row[4],
			ToArea:       toArea,
			ToIp:         row[5],
			ToPort:       row[6],
			ToDomain:     row[7],
			ToUrl:        row[8],
			ToProtocol:   row[9],
		}
		if !checkIp(row[0], row[5]) {
			continue
		}
		netMappings = append(netMappings, netMapping)
		if len(netMappings) > 4 {
			break
		}
	}
	return netMappings, nil
}
