package net_mapping

import (
	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/models/mysql/net_mapping"
	"github.com/olivere/elastic/v7"
	"github.com/stretchr/testify/assert"
	"strings"
	"sync"
	"testing"
)

func TestPrepareCtrixData(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	// 注册空的搜索响应，避免 ES 查询报错
	mockServer.Register("/.*/_search", []*elastic.SearchHit{})

	input := `
add service SVC1 ******** HTTP 80
add service SVC2 ******** HTTP 8080
add lb vserver VS1 SSL ******* 443
bind lb vserver VS1 SVC1
bind lb vserver VS1 SVC2
`

	file := strings.NewReader(input)
	batchNo := "test-batch"
	fileType := "test-source"
	fromArea := uint64(100)
	toArea := uint64(200)

	mappingChan := make(chan *net_mapping.NetMapping, 10)
	var results []*net_mapping.NetMapping

	var wg sync.WaitGroup
	wg.Add(1)

	// 消费 channel 里的内容
	go func() {
		defer wg.Done()
		for mapping := range mappingChan {
			results = append(results, mapping)
		}
	}()

	count := PrepareCtrixData(mappingChan, file, batchNo, fileType, fromArea, toArea)
	close(mappingChan)

	wg.Wait() // 等待所有数据消费完毕

	assert.Equal(t, int64(2), count)
	assert.Len(t, results, 2)

	expectedFromIP := "*******"
	expectedFromPort := "443"
	expectedFromProtocol := "SSL"

	// 检查每条映射是否符合预期
	for _, m := range results {
		assert.Equal(t, fileType, m.DataSource)
		assert.Equal(t, batchNo, m.BatchNo)
		assert.Equal(t, fromArea, m.FromArea)
		assert.Equal(t, toArea, m.ToArea)
		assert.Equal(t, expectedFromIP, m.FromIp)
		assert.Equal(t, expectedFromPort, m.FromPort)
		assert.Equal(t, expectedFromProtocol, m.FromProtocol)
		assert.Contains(t, []string{"********", "********"}, m.ToIp)
		assert.Contains(t, []string{"80", "8080"}, m.ToPort)
		assert.Equal(t, "HTTP", m.ToProtocol)
	}
}
