package net_mapping

import (
	"fobrain/models/mysql/net_mapping"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"sync"
	"testing"
)

func TestImportNetMappingByTemplate(t *testing.T) {
	rows := [][]string{{"公网IP", "公网端口", "公网协议", "公网域名", "公网URL", "内网IP", "内网端口", "内网协议", "内网域名", "内网URL"},
		{"***********", "80", "", "", "", "***********", "8880", "", "", ""},
	}
	patches := []*gomonkey.Patches{
		gomonkey.ApplyFunc(SaveData, func(ch chan *net_mapping.NetMapping, wg *sync.WaitGroup, successCount *int64) {
			defer wg.Done()
			count := 0
			for range ch {
				count++
			}
			*successCount = int64(count)
		}),
	}
	successCount, msg, err := ImportNetMappingByTemplate(uuid.New().String(), "", rows, uint64(1), uint64(2))
	for _, patch := range patches {
		patch.Reset()
	}
	assert.Equal(t, successCount, int64(1))
	assert.Equal(t, msg, "导入成功，成功解析1条数据，新增1条数据")
	assert.Nil(t, err)
}

func TestImportIpMappingByTemplateConfirm(t *testing.T) {
	rows := [][]string{{"公网IP", "公网端口", "公网协议", "公网域名", "公网URL", "内网IP", "内网端口", "内网协议", "内网域名", "内网URL"},
		{"***********", "80", "", "", "", "***********", "8880", "", "", ""},
	}
	netMappings, err := ImportNetMappingByTemplateConfirm(rows, uint64(1), uint64(2))
	assert.Nil(t, err)
	assert.Equal(t, netMappings[0].FromIp, "***********")
	assert.Equal(t, netMappings[0].ToIp, "***********")
}
