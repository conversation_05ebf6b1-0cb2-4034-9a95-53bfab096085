package net_mapping

import (
	"bufio"
	"fmt"
	"fobrain/models/mysql/net_mapping"
	"io"
	"regexp"
	"strings"
	"sync"
)

// 天融信防火墙映射
func ImportNetMappingByTopsecFW(file io.Reader, batchNo string, fileType string, fromArea, toArea uint64) (int64, string, error) {
	successCount := int64(0)
	totalCount := int64(0)
	wg := &sync.WaitGroup{}
	netMappingChan := make(chan *net_mapping.NetMapping, 100)

	wg.Add(1)
	go SaveData(netMappingChan, wg, &successCount)

	scanner := bufio.NewScanner(file)
	// 正则匹配天融信防火墙的nat policy add行
	// 支持格式如：
	// nat policy add name xxx srcarea 'xxx ' orig_dst 'ip ' orig_service 'port ' trans_dst ip trans_service port
	// nat policy add name xxx srcarea 'xxx ' orig_dst 'ip ' orig_service 'protoPort ' trans_dst ip trans_service protoPort
	// 兼容部分字段缺失或无协议
	reg := regexp.MustCompile(`nat policy add name [^\s]+ srcarea '[^']+' orig_dst '([^']+)' orig_service '([^']+)' trans_dst\s*([\d.]+)\s*[^\s]* trans_service\s*([^\s]+)`) // 主要字段

	for scanner.Scan() {
		line := scanner.Text()
		if !strings.HasPrefix(line, "nat policy add") {
			continue
		}
		matches := reg.FindStringSubmatch(line)
		if len(matches) == 5 {
			fromIp := strings.TrimSpace(strings.Split(matches[1], " ")[0])
			// 去除fromIp中的中文和空格，只保留IP部分
			fromIp = extractPureIP(fromIp)
			fromPort := strings.TrimSpace(matches[2])
			var fromProtocol string
			fromProtocol, fromPort = extractProtocolAndPort(fromPort)
			toIp := strings.TrimSpace(matches[3])
			// 去除toIp中的中文和空格，只保留IP部分
			toIp = extractPureIP(toIp)
			toPort := strings.TrimSpace(matches[4])
			var toProtocol string
			toProtocol, toPort = extractProtocolAndPort(toPort)
			if !checkIp(fromIp, toIp) || (fromPort != "" && !checkPort(fromPort)) || (toPort != "" && !checkPort(toPort)) {
				continue
			}
			mapping := &net_mapping.NetMapping{
				DataSource:   fileType,
				BatchNo:      batchNo,
				FromArea:     fromArea,
				FromIp:       fromIp,
				FromPort:     fromPort,
				FromProtocol: fromProtocol,
				ToArea:       toArea,
				ToIp:         toIp,
				ToPort:       toPort,
				ToProtocol:   toProtocol,
			}
			markNetMappingListAlarm([]*net_mapping.NetMapping{mapping}, nil)
			netMappingChan <- mapping
			totalCount++
		}
	}

	close(netMappingChan)
	wg.Wait()
	return successCount, fmt.Sprintf("导入成功，成功解析%d条数据，新增%d条数据", totalCount, successCount), nil
}

// extractPureIP 只保留字符串中的IP部分
func extractPureIP(s string) string {
	reg := regexp.MustCompile(`(\d+\.\d+\.\d+\.\d+)`)
	match := reg.FindStringSubmatch(s)
	if len(match) > 1 {
		return match[1]
	}
	return s
}

// extractProtocolAndPort 提取协议（如TCP/UDP）和纯端口号
func extractProtocolAndPort(s string) (protocol, port string) {
	s = strings.ToUpper(strings.TrimSpace(s))
	// 用正则提取协议和端口，协议为前缀字母，端口为后跟数字，末尾非数字自动去除
	reg := regexp.MustCompile(`^([A-Z]+)?(\d+)`)
	matches := reg.FindStringSubmatch(s)
	if len(matches) == 3 {
		return matches[1], matches[2]
	}
	return "", s
}
