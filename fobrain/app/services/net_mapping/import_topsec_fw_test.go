package net_mapping

import (
	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/models/mysql/net_mapping"
	"github.com/olivere/elastic/v7"
	"strings"
	"sync"
	"testing"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/stretchr/testify/assert"
)

func TestImportNetMappingByTopsecFW(t *testing.T) {
	// 初始化 mock ES 客户端
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	// 注册空的搜索响应，避免 ES 查询报错
	mockServer.Register("/.*/_search", []*elastic.SearchHit{})

	patch := gomonkey.ApplyFunc(SaveData, func(ch chan *net_mapping.NetMapping, wg *sync.WaitGroup, successCount *int64) {
		defer wg.Done()
		count := 0
		for range ch {
			count++
		}
		*successCount = int64(count)
	})
	defer patch.Reset()

	input := `
nat policy add name 测试1 srcarea '电信外网 ' orig_dst '************外网地址 ' orig_service '8086 ' trans_dst *********** DMZ trans_service 8086
nat policy add name 测试2 srcarea '电信外网 ' orig_dst '************公网IP ' orig_service 'TCP81 ' trans_dst ***********  trans_service TCP81
nat policy add name 测试3 srcarea '电信外网 ' orig_dst '************ ' orig_service 'UDP53 ' trans_dst ************ trans_service UDP53
nat policy add name 测试4 srcarea '电信外网 ' orig_dst '************外网 ' orig_service '8080端口 ' trans_dst ************ trans_service 8080
nat policy add name 测试5 srcarea '电信外网 ' orig_dst '************ ' orig_service '443 ' trans_dst ************ trans_service 443
nat policy add name 测试6 srcarea '电信外网 ' orig_dst 'notanip ' orig_service 'abc ' trans_dst notanip trans_service abc

invalid line here
`
	file := strings.NewReader(input)
	batchNo := "test-batch"
	fileType := "天融信防火墙"
	fromArea := uint64(1)
	toArea := uint64(2)

	successCount, msg, err := ImportNetMappingByTopsecFW(file, batchNo, fileType, fromArea, toArea)
	assert.Nil(t, err)
	assert.Equal(t, int64(5), successCount)
	assert.Contains(t, msg, "成功解析5条数据")
}

func Test_extractPureIP(t *testing.T) {
	assert.Equal(t, "************", extractPureIP("************外网地址"))
	assert.Equal(t, "***********", extractPureIP("*********** DMZ"))
	assert.Equal(t, "********", extractPureIP("********"))
	assert.Equal(t, "notanip", extractPureIP("notanip"))
}

func Test_extractProtocolAndPort(t *testing.T) {
	proto, port := extractProtocolAndPort("TCP81")
	assert.Equal(t, "TCP", proto)
	assert.Equal(t, "81", port)
	proto, port = extractProtocolAndPort("UDP53")
	assert.Equal(t, "UDP", proto)
	assert.Equal(t, "53", port)
	proto, port = extractProtocolAndPort("8080")
	assert.Equal(t, "", proto)
	assert.Equal(t, "8080", port)
}
