package net_mapping

import (
	"errors"
	request "fobrain/fobrain/app/request/net_mapping_area"
	response "fobrain/fobrain/app/response/net_mapping"
	"fobrain/initialize/mysql"
	"fobrain/models/mysql/net_mapping"
)

func ListArea() []*response.ListAreaResponse {
	var res []*response.ListAreaResponse
	netMappingAreaList, err := net_mapping.NewNetMappingAreaModel().List()
	if err != nil {
		return nil
	}
	var lastArea *net_mapping.NetMappingArea
	for _, netMappingArea := range netMappingAreaList {
		if netMappingArea.Id == 2 {
			lastArea = netMappingArea
			continue
		}
		res = append(res, &response.ListAreaResponse{
			Id:   netMappingArea.Id,
			Name: netMappingArea.Name,
		})
	}
	if lastArea != nil && lastArea.Id == 2 {
		res = append(res, &response.ListAreaResponse{
			Id:   lastArea.Id,
			Name: lastArea.Name,
		})
	}
	return res
}

func UpdateAreaList(params *request.UpdateAreaListRequest) error {
	var newAreaList []struct {
		Id   uint64 `json:"id"`
		Name string `json:"name"`
	}

	for _, field := range params.Fields {
		if field.Id > 0 && field.Name == "" {
			if field.Id == 1 || field.Id == 2 {
				return errors.New("系统字段不允许删除")
			}
			err := net_mapping.NewNetMappingAreaModel().Delete(
				&net_mapping.NetMappingArea{BaseModel: mysql.BaseModel{Id: field.Id}})
			if err != nil {
				return err
			}
		} else {
			newAreaList = append(newAreaList, field)
		}
	}

	if len(newAreaList) > 10 {
		return errors.New("自定义字段数必须小于10")
	}

	for _, newArea := range newAreaList {
		if newArea.Id > 0 {
			err := net_mapping.NewNetMappingAreaModel().Update(
				&net_mapping.NetMappingArea{
					BaseModel: mysql.BaseModel{Id: newArea.Id},
					Name:      newArea.Name,
				})
			if err != nil {
				return err
			}
		} else if newArea.Name != "" {
			err := net_mapping.NewNetMappingAreaModel().Create(
				&net_mapping.NetMappingArea{
					Name: newArea.Name,
				})
			if err != nil {
				return err
			}
		}
	}
	return nil
}
