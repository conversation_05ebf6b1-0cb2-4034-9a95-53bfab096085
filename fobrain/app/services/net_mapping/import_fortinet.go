package net_mapping

import (
	"fmt"
	"fobrain/models/mysql/net_mapping"
	"regexp"
	"strings"
	"sync"
)

type Policy struct {
	SrcAddr  string `json:"src_addr"`
	PoolName string `json:"pool_name"`
}

// ImportNetMappingByFortinet 导入Fortinet防火墙配置文件
func ImportNetMappingByFortinet(batchNo string, fileType string, content string, fromArea, toArea uint64) (int64, string, error) {
	successCount := int64(0)
	totalCount := int64(0)
	wg := &sync.WaitGroup{}
	netMappingChan := make(chan *net_mapping.NetMapping, 100)

	wg.Add(1)
	go SaveData(netMappingChan, wg, &successCount)

	data, err := parseFortinetConfig(content)
	if err != nil {
		return 0, "", err
	}

	policies := data["policies"].([]Policy)
	addresses := data["addresses"].(map[string]string)

	for _, policy := range policies {
		srcAddr := policy.SrcAddr
		PoolName := policy.PoolName

		if _, ok := addresses[srcAddr]; !ok {
			continue
		}
		netMapping := &net_mapping.NetMapping{
			DataSource: fileType,
			BatchNo:    batchNo,
			FromArea:   fromArea,
			FromIp:     addresses[srcAddr],
			ToArea:     toArea,
			ToIp:       PoolName,
		}
		if !checkIp(addresses[srcAddr], PoolName) {
			continue
		}
		netMappingChan <- netMapping
		totalCount++
	}
	close(netMappingChan)
	wg.Wait()
	return successCount, fmt.Sprintf("导入成功，成功解析%d条数据，新增%d条数据", totalCount, successCount), nil
}

// ImportNetMappingByFortinetConfirm 导入Fortinet防火墙配置文件确认
func ImportNetMappingByFortinetConfirm(content string, fromArea, toArea uint64) ([]*net_mapping.NetMapping, error) {
	netMappings := make([]*net_mapping.NetMapping, 0)
	data, err := parseFortinetConfig(content)
	if err != nil {
		return netMappings, err
	}

	policies := data["policies"].([]Policy)
	addresses := data["addresses"].(map[string]string)

	for _, policy := range policies {
		srcAddr := policy.SrcAddr
		PoolName := policy.PoolName

		if _, ok := addresses[srcAddr]; !ok {
			continue
		}
		netMapping := &net_mapping.NetMapping{
			FromArea: fromArea,
			FromIp:   addresses[srcAddr],
			ToArea:   toArea,
			ToIp:     PoolName,
		}
		if !checkIp(addresses[srcAddr], PoolName) {
			continue
		}
		netMappings = append(netMappings, netMapping)
		if len(netMappings) > 4 {
			break
		}
	}
	return netMappings, nil
}

func parseFortinetConfig(config string) (map[string]interface{}, error) {
	policies := []Policy{}
	addresses := make(map[string]string)
	srcaddrs := []string{}

	// 解析 config firewall policy 部分
	policyPattern := regexp.MustCompile(`(?s)config firewall policy(.*?)end`)
	policyMatches := policyPattern.FindAllStringSubmatch(config, -1)

	for _, match := range policyMatches {
		if len(match) < 2 {
			continue
		}
		block := match[1]

		// 匹配 edit 到 next 之间的内容
		entryPattern := regexp.MustCompile(`(?s)edit.*?next`)
		entries := entryPattern.FindAllString(block, -1)

		for _, entry := range entries {
			// 检查是否有 set nat enable 且没有 set status disable
			hasNatEnable := regexp.MustCompile(`set nat enable`).MatchString(entry)
			hasStatusDisable := regexp.MustCompile(`set status disable`).MatchString(entry)

			if hasNatEnable && !hasStatusDisable {
				// 提取 srcaddr 和 poolname
				srcaddrPattern := regexp.MustCompile(`set srcaddr "([^"]*)"`)
				poolnamePattern := regexp.MustCompile(`set poolname "([^"]*)"`)

				srcaddrMatch := srcaddrPattern.FindStringSubmatch(entry)
				poolnameMatch := poolnamePattern.FindStringSubmatch(entry)

				if len(srcaddrMatch) > 1 && len(poolnameMatch) > 1 {
					srcaddrs = append(srcaddrs, srcaddrMatch[1])
					policies = append(policies, Policy{
						SrcAddr:  srcaddrMatch[1],
						PoolName: strings.ReplaceAll(poolnameMatch[1], "/32", ""),
					})
				}
			}
		}
	}

	// 解析 config firewall address 部分
	addressPattern := regexp.MustCompile(`(?s)config firewall address\s+((?:edit.*?next\s*)*)end\s*`)
	addressMatches := addressPattern.FindAllStringSubmatch(config, -1)

	for _, match := range addressMatches {
		if len(match) < 2 {
			continue
		}
		block := match[1]

		// 过滤掉包含 "address6" 的块
		if strings.Contains(block, "address6") {
			continue
		}

		// 匹配 edit 到 next 之间的内容
		entryPattern := regexp.MustCompile(`(?s)edit.*?next`)
		entries := entryPattern.FindAllString(block, -1)

		for _, entry := range entries {
			// 检查是否存在 set subnet
			if regexp.MustCompile(`set subnet`).MatchString(entry) {
				// 提取 edit 和 subnet
				editPattern := regexp.MustCompile(`edit "([^"]*)"`)
				subnetPattern := regexp.MustCompile(`set subnet (\d{1,3}(?:\.\d{1,3}){3})`)

				editMatch := editPattern.FindStringSubmatch(entry)
				subnetMatch := subnetPattern.FindStringSubmatch(entry)

				if len(editMatch) > 1 && len(subnetMatch) > 1 {
					// 检查是否在srcaddrs中
					found := false
					for _, addr := range srcaddrs {
						if addr == editMatch[1] {
							addresses[editMatch[1]] = subnetMatch[1]
							found = true
							break
						}
					}

					// 如果没找到，检查comment
					if !found && regexp.MustCompile(`set comment`).MatchString(entry) {
						commentPattern := regexp.MustCompile(`set comment "([^"]*)"`)
						commentMatch := commentPattern.FindStringSubmatch(entry)
						if len(commentMatch) > 1 {
							for _, addr := range srcaddrs {
								if addr == commentMatch[1] {
									addresses[commentMatch[1]] = subnetMatch[1]
									break
								}
							}
						}
					}
				}
			}
		}
	}
	result := map[string]interface{}{
		"policies":  policies,
		"addresses": addresses,
	}

	return result, nil
}
