package net_mapping

import (
	"fobrain/models/mysql/net_mapping"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"sync"
	"testing"
)

func TestImportNetMappingByHuaweiFWConfig(t *testing.T) {
	patches := []*gomonkey.Patches{
		gomonkey.ApplyFunc(SaveData, func(ch chan *net_mapping.NetMapping, wg *sync.WaitGroup, successCount *int64) {
			defer wg.Done()
			count := 0
			for range ch {
				count++
			}
			*successCount = int64(count)
		}),
	}
	lines := []string{
		"nat server fofa大屏演示 protocol tcp global ************* inside ************ no-reverse",
		"nat server 17-K01API zone untrust protocol tcp global ************* 11300 inside ************ 3000 no-reverse",
		"aaa server 17-K01API zone untrust protocol tcp global ************* 11300 inside ************ 3000 no-reverse",
		"nat server 17-K01API zone untrust protocol tcp global ************* 113a0 inside ************ 3000 no-reverse",
		"nat server 17-K01API zone untrust protocol tcp global ************* 1130110 inside ************ 3000 nat-disable",
	}
	successCount, msg, err := ImportNetMappingByHuaweiFWConfig(uuid.New().String(), "华为防火墙8000E-X8配置文件", lines, uint64(1), uint64(2))
	for _, patch := range patches {
		patch.Reset()
	}
	assert.Equal(t, successCount, int64(2))
	assert.Equal(t, msg, "导入成功，成功解析2条数据，新增2条数据")
	assert.Nil(t, err)
}

func TestImportIpMappingByHuaweiFWConfigConfirm(t *testing.T) {
	lines := []string{
		"nat server fofa大屏演示 protocol tcp global ************* inside ************ no-reverse",
		"nat server 17-K01API zone untrust protocol tcp global ************* 11300 inside ************ 3000 no-reverse",
		"aaa server 17-K01API zone untrust protocol tcp global ************* 11300 inside ************ 3000 no-reverse",
		"nat server 17-K01API zone untrust protocol tcp global ************* 113a0 inside ************ 3000 no-reverse",
		"nat server 17-K01API zone untrust protocol tcp global ************* 1130110 inside ************ 3000 nat-disable",
	}
	netMappings, err := ImportNetMappingByHuaweiFWConfigConfirm(lines, uint64(1), uint64(2))
	assert.Nil(t, err)
	assert.Equal(t, netMappings[0].FromIp, "*************")
	assert.Equal(t, netMappings[0].ToIp, "************")
}
