package net_mapping

import (
	"errors"
	request "fobrain/fobrain/app/request/net_mapping_area"
	testcommon "fobrain/fobrain/tests/common_test"
	"github.com/DATA-DOG/go-sqlmock"
	"github.com/spf13/cast"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestListArea(t *testing.T) {
	mockDb := testcommon.InitSqlMock()
	mockDb.ExpectQuery("SELECT * FROM `net_mapping_areas`").
		WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))
	res1 := ListArea()
	mockDb.ExpectQuery("SELECT * FROM `net_mapping_areas`").
		WillReturnError(errors.New("err"))
	res2 := ListArea()
	mockDb.Close()
	assert.Equal(t, len(res1), 1)
	assert.Equal(t, len(res2), 0)
}

func TestUpdateAreaList(t *testing.T) {
	params := &request.UpdateAreaListRequest{
		Fields: []struct {
			Id   uint64 `json:"id"`
			Name string `json:"name"`
		}{
			{Id: 4, Name: "test1"},
			{Id: 5, Name: ""},
			{Name: "test3"},
		},
	}
	mockDb := testcommon.InitSqlMock()

	mockDb.ExpectBegin()
	mockDb.ExpectExec("DELETE FROM `net_mapping_areas` WHERE `net_mapping_areas`.`id` = ?").
		WithArgs(5).
		WillReturnResult(sqlmock.NewResult(1, 1))
	mockDb.ExpectCommit()

	mockDb.ExpectBegin()
	mockDb.ExpectExec("net_mapping_areas` SET `updated_at`=?,`name`=? WHERE `id` = ?").
		WithArgs(sqlmock.AnyArg(), "test1", 4).
		WillReturnResult(sqlmock.NewResult(1, 1))
	mockDb.ExpectCommit()

	mockDb.ExpectBegin()
	mockDb.ExpectExec("INSERT INTO `net_mapping_areas`").
		WillReturnResult(sqlmock.NewResult(1, 1))
	mockDb.ExpectCommit()

	err := UpdateAreaList(params)
	mockDb.Close()
	assert.Nil(t, err)
}

func TestUpdateAreaList10(t *testing.T) {
	params := &request.UpdateAreaListRequest{Fields: make([]struct {
		Id   uint64 `json:"id"`
		Name string `json:"name"`
	}, 0)}
	for i := 0; i < 11; i++ {
		params.Fields = append(params.Fields, struct {
			Id   uint64 `json:"id"`
			Name string `json:"name"`
		}{Name: cast.ToString(i)})
	}
	err := UpdateAreaList(params)
	assert.Equal(t, err.Error(), "自定义字段数必须小于10")
}
