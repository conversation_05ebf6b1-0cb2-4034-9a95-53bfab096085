package rule_infos_level

import (
	"fobrain/pkg/cfg"
)

var englishToChinese = map[int]string{
	5: "business",
	4: "support",
	3: "service",
	2: "system",
	1: "hard",
}

var chineseToSort = map[int]string{
	5: "业务层",
	4: "支撑层",
	3: "服务层",
	2: "系统层",
	1: "硬件层",
}

func GetRuleInfoLevel(tag string) string {
	value, exists := cfg.RuleInfosLevelMapping[tag]
	if exists {
		return chineseToSort[value]
	}
	return ""
}
