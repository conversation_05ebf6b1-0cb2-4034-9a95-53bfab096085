package cascade_upgrade_service

import (
	"bytes"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"fobrain/fobrain/app/services/node/x_ray"
	"fobrain/fobrain/logs"
	"fobrain/models/mysql/data_source"
	"io"
	"mime/multipart"
	"net/http"
	"os"
	"path/filepath"

	"github.com/spf13/cast"
)

const (
	UriTemplateForXrayUploadFile       = "%s://%s/api/v2/upload_file/"
	UriTemplateForUploadUpgradePackage = "%s://%s/api/v2/upload_upgrade_package/"
	UriTemplateForCheckUpgradePackage  = "%s://%s/api/v2/package/check/"
	UriTemplateForUpgradeEngine        = "%s://%s/api/v2/engine/upgrade/"
	UriTemplateForGetEngineList        = "%s://%s/api/v2/engine/filter/"
	UriTemplateForUpgradeVulnLibrary   = "%s://%s/api/v2/vuln_library/upgrade/"
	UriTemplateForExecutionFilter      = "%s://%s/api/v2/execution/filter/"
)

// getXrayClient 获取Xray客户端
func getXrayClient(nodeId uint64) (*x_ray.XRay, error) {
	cli := x_ray.New()
	err := cli.SetNode(nodeId)
	if err != nil {
		return nil, err
	}

	config, err := data_source.NewNodeConfigModel().GetNodeConfig(nodeId)
	if err != nil {
		return nil, err
	}

	err = cli.SetConfig(config)
	if err != nil {
		return nil, err
	}

	return cli, nil
}

// XrayUploadFile 上传文件
func XrayUploadFile(cli *x_ray.XRay, filePath, fileName string) (string, error) {
	logger := logs.GetLogger()
	payload := &bytes.Buffer{}
	// 创建一个缓冲区和multipart写入器
	writer := multipart.NewWriter(payload)
	// 添加其他表单字段
	err := writer.WriteField("file_name", fileName)
	if err != nil {
		return "", fmt.Errorf("failed to write form field: %v", err)
	}

	// 打开文件
	file, err := os.Open(filePath)
	if err != nil {
		return "", fmt.Errorf("failed to open file: %v", err)
	}
	defer file.Close()
	part, err := writer.CreateFormFile("file", filepath.Base(filePath))
	if err != nil {
		return "", fmt.Errorf("failed to create form file: %v", err)
	}
	// 将文件内容复制到表单字段中
	_, err = io.Copy(part, file)
	if err != nil {
		return "", fmt.Errorf("failed to copy file content: %v", err)
	}
	// 关闭写入器以结束表单内容
	err = writer.Close()
	if err != nil {
		return "", fmt.Errorf("failed to close writer: %v", err)
	}

	// 拼接请求地址
	apiUri := fmt.Sprintf(UriTemplateForXrayUploadFile, cast.ToString(cli.GetConfig("protocol")), cast.ToString(cli.GetConfig("ip")))

	// 创建 HTTP 请求
	req, err := http.NewRequest("POST", apiUri, payload)
	if err != nil {
		return "", fmt.Errorf("failed to create xray HTTP request: %v", err)
	}

	// 设置请求头信息
	req.Header.Add("Content-Type", writer.FormDataContentType())
	req.Header.Add("token", cli.GetToken())

	// 创建新的http客户端
	client := &http.Client{}
	// 忽略证书验证
	client.Transport = &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}

	resp, err := client.Do(req)
	if err != nil {
		return "", err
	}
	if resp.StatusCode != 200 {
		// 413:文件大小超过限制，人行真的遇到了这个问题（2025-4-24）
		if resp.StatusCode == 413 {
			return "", fmt.Errorf("x-ray上传升级包失败,文件大小超过限制")
		}
		return "", fmt.Errorf("x-ray上传升级包失败,状态码: %d", resp.StatusCode)
	}
	// 读取响应体
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("读取响应体失败: %v", err)
	}
	logger.Infof("上传文件响应: %s", string(respBody))
	defer resp.Body.Close()

	// 定义响应结构
	type Response struct {
		Err  string `json:"err"`
		Msg  string `json:"msg"`
		Data struct {
			Uuid string `json:"uuid"`
		} `json:"data"`
	}

	// 解析JSON响应
	var response Response
	if err := json.Unmarshal(respBody, &response); err != nil {
		return "", fmt.Errorf("解析JSON响应失败: %v", err)
	}
	logger.Infof("上传文件响应: %s", string(respBody))
	defer resp.Body.Close()

	// 如果升级操作失败,返回错误
	if response.Err != "" || response.Data.Uuid == "" {
		return "", fmt.Errorf("上传文件失败,%s", response.Msg)
	}

	return response.Data.Uuid, nil
}

// UploadUpgradePackage 上传升级包
// module:升级包类型类型包含：ENGINE（引擎），VULN_LIBRARY（漏洞库）
// 返回升级包ID
func UploadUpgradePackage(cli *x_ray.XRay, fileUuid string, module string) (int, error) {
	logger := logs.GetLogger()
	if module != "ENGINE" && module != "VULN_LIBRARY" {
		return 0, fmt.Errorf("invalid module: %s", module)
	}

	params := map[string]string{
		"file_uuid": fileUuid,
		"module":    module,
	}

	apiUri := fmt.Sprintf(UriTemplateForUploadUpgradePackage,
		cast.ToString(cli.GetConfig("protocol")),
		cast.ToString(cli.GetConfig("ip")))

	resp, err := doJsonRequest(cli, apiUri, params)
	if err != nil {
		return 0, err
	}

	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return 0, fmt.Errorf("读取响应体失败: %v", err)
	}
	logger.Infof("上传升级包响应: %s", string(body))
	defer resp.Body.Close()

	// 定义响应结构
	type Response struct {
		Err  string `json:"err"`
		Msg  string `json:"msg"`
		Data struct {
			PackageId int `json:"package_id"`
		} `json:"data"`
	}

	// 解析JSON响应
	var response Response
	if err := json.Unmarshal(body, &response); err != nil {
		return 0, fmt.Errorf("解析JSON响应失败: %v", err)
	}
	if response.Err != "" || response.Data.PackageId == 0 {
		return 0, fmt.Errorf("上传升级包失败: %s", response.Msg)
	}

	return response.Data.PackageId, nil
}

// CheckUpgradePackage 校验升级包
// module:升级包类型类型包含：ENGINE（引擎），VULN_LIBRARY（漏洞库）,当module为ENGINE时,engine_ids为必填
func CheckUpgradePackage(cli *x_ray.XRay, packageId int, module string, engineIds []string) (bool, error) {
	logger := logs.GetLogger()
	if module != "ENGINE" && module != "VULN_LIBRARY" {
		return false, fmt.Errorf("invalid module: %s", module)
	}

	params := map[string]interface{}{
		"package_id": packageId,
		"module":     module,
	}

	// 当module为ENGINE时,engine_ids为必填
	if module == "ENGINE" {
		params["engine_ids"] = engineIds
	}

	apiUri := fmt.Sprintf(UriTemplateForCheckUpgradePackage,
		cast.ToString(cli.GetConfig("protocol")),
		cast.ToString(cli.GetConfig("ip")))

	resp, err := doJsonRequest(cli, apiUri, params)
	if err != nil {
		return false, err
	}
	if resp.StatusCode != 200 {
		return false, fmt.Errorf("x-ray校验升级包失败,状态码: %d", resp.StatusCode)
	}
	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return false, fmt.Errorf("读取响应体失败: %v", err)
	}
	logger.Infof("x-ray校验升级包响应: %s", string(body))
	defer resp.Body.Close()

	// 定义响应结构
	type Response struct {
		Err  string `json:"err"`
		Msg  string `json:"msg"`
		Data struct {
			Status     bool                         `json:"status"`
			FailedList []PackageFailedCheckResponse `json:"failed_list"`
		} `json:"data"`
	}

	// 解析JSON响应
	var response Response
	if err := json.Unmarshal(body, &response); err != nil {
		logger.Errorf("x-ray校验解析JSON响应失败: %s", string(body))
		return false, fmt.Errorf("解析JSON响应失败: %v", err)
	}

	// 如果升级操作失败,返回错误
	if !response.Data.Status {
		if len(response.Data.FailedList) > 0 {
			logger.Errorf("x-ray校验未通过: %s", string(body))
			return false, fmt.Errorf("校验未通过,error: %v", response.Data.FailedList[0].Message)
		}
		logger.Errorf("x-ray校验未通过: %s", string(body))
		return false, fmt.Errorf("校验未通过")
	}

	return true, nil
}

// PackageFailedCheckResponse 校验升级包的响应
type PackageFailedCheckResponse struct {
	ExecutorId string `json:"executor_id"` // 引擎节点的ID 或漏洞库的ID
	Message    string `json:"message"`     // 校验失败原因
}

// UpgradeVulnLibrary 漏洞库升级
// packageId: 漏洞库的升级包ID
func UpgradeVulnLibrary(cli *x_ray.XRay, packageId int) (bool, error) {
	logger := logs.GetLogger()
	// 先校验升级包
	ok, err := CheckUpgradePackage(cli, packageId, "VULN_LIBRARY", nil)
	if err != nil {
		return false, fmt.Errorf("升级包校验失败: %v", err)
	}
	if !ok {
		return false, fmt.Errorf("升级包校验未通过")
	}

	params := map[string]interface{}{
		"package_id": packageId,
	}

	apiUri := fmt.Sprintf(UriTemplateForUpgradeVulnLibrary,
		cast.ToString(cli.GetConfig("protocol")),
		cast.ToString(cli.GetConfig("ip")))
	resp, err := doJsonRequest(cli, apiUri, params)
	if err != nil {
		return false, err
	}

	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return false, fmt.Errorf("读取响应体失败: %v", err)
	}
	logger.Infof("漏洞库升级响应: %s", string(body))
	defer resp.Body.Close()

	// 定义响应结构
	type Response struct {
		Err  string `json:"err"`
		Msg  string `json:"msg"`
		Data struct {
			Success bool `json:"success"`
		} `json:"data"`
	}

	// 解析JSON响应
	var response Response
	if err := json.Unmarshal(body, &response); err != nil {
		return false, fmt.Errorf("解析JSON响应失败: %v", err)
	}

	// 如果升级操作失败,返回错误
	if !response.Data.Success {
		return false, fmt.Errorf("升级操作失败")
	}

	return true, nil
}

// UpgradeEngine 引擎升级
// packageId: 引擎节点的升级包ID
// engineIds: 引擎节点的ID列表
func UpgradeEngine(cli *x_ray.XRay, packageId int, engineIds []string) (bool, error) {
	logger := logs.GetLogger()
	params := map[string]interface{}{
		"package_id": packageId,
		"engine_ids": engineIds,
	}

	apiUri := fmt.Sprintf(UriTemplateForUpgradeEngine,
		cast.ToString(cli.GetConfig("protocol")),
		cast.ToString(cli.GetConfig("ip")))
	resp, err := doJsonRequest(cli, apiUri, params)
	if err != nil {
		return false, err
	}

	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return false, fmt.Errorf("读取响应体失败: %v", err)
	}
	defer resp.Body.Close()

	// 定义响应结构
	type Response struct {
		Err  string `json:"err"`
		Msg  string `json:"msg"`
		Data struct {
			Success bool `json:"success"`
		} `json:"data"`
	}

	// 解析JSON响应
	var response Response
	if err := json.Unmarshal(body, &response); err != nil {
		return false, fmt.Errorf("解析JSON响应失败: %v", err)
	}
	logger.Infof("引擎升级响应: %s", string(body))
	defer resp.Body.Close()

	// 如果升级操作失败,返回错误
	if !response.Data.Success {
		return false, fmt.Errorf("升级操作失败")
	}

	return true, nil
}

// GetEngineList 获取引擎节点列表
// limit: 结果的数量限制
// offset: 结果的偏移量
// 返回引擎节点的ID列表
func GetEngineList(cli *x_ray.XRay, limit int, offset int) ([]string, error) {
	logger := logs.GetLogger()
	params := map[string]interface{}{
		"limit":  limit,
		"offset": offset,
	}

	apiUri := fmt.Sprintf(UriTemplateForGetEngineList,
		cast.ToString(cli.GetConfig("protocol")),
		cast.ToString(cli.GetConfig("ip")))

	resp, err := doJsonRequest(cli, apiUri, params)
	if err != nil {
		return nil, err
	}
	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应体失败: %v", err)
	}
	defer resp.Body.Close()

	// 定义响应结构
	type Engine struct {
		Id string `json:"id"`
	}
	type Response struct {
		Err  string `json:"err"`
		Msg  string `json:"msg"`
		Data struct {
			Content []Engine `json:"content"`
		} `json:"data"`
	}

	// 解析JSON响应
	var response Response
	if err := json.Unmarshal(body, &response); err != nil {
		return nil, fmt.Errorf("解析JSON响应失败: %v", err)
	}
	logger.Infof("获取引擎节点列表响应: %s", string(body))
	defer resp.Body.Close()

	// 提取所有引擎ID
	engineIds := make([]string, 0, len(response.Data.Content))
	for _, engine := range response.Data.Content {
		engineIds = append(engineIds, engine.Id)
	}

	return engineIds, nil
}

// GetExecutionList 获取执行状态列表
// limit: 结果的数量限制
// offset: 结果的偏移量
// modules: 升级包类型列表,包含：ENGINE（引擎），VULN_LIBRARY（漏洞库）
func GetExecutionList(cli *x_ray.XRay, limit int, offset int, modules []string) (*ExecutionListResponse, error) {
	logger := logs.GetLogger()
	params := map[string]interface{}{
		"limit":   limit,
		"offset":  offset,
		"modules": modules,
	}

	apiUri := fmt.Sprintf(UriTemplateForExecutionFilter,
		cast.ToString(cli.GetConfig("protocol")),
		cast.ToString(cli.GetConfig("ip")))

	resp, err := doJsonRequest(cli, apiUri, params)
	if err != nil {
		return nil, err
	}

	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应体失败: %v", err)
	}
	defer resp.Body.Close()

	// 解析JSON响应
	var response ExecutionListResponse
	if err := json.Unmarshal(body, &response); err != nil {
		return nil, fmt.Errorf("解析JSON响应失败: %v", err)
	}
	logger.Infof("获取执行状态列表响应: %s", string(body))
	return &response, nil
}

// ExecutionListResponse 执行状态列表响应
type ExecutionListResponse struct {
	Count  int                `json:"count"`  // 执行状态的总数
	Result []UpgradeExecution `json:"result"` // 限制下执行状态的详情列表
}

// UpgradeExecution 升级执行状态
type UpgradeExecution struct {
	ID       int    `json:"id"`       // 升级进程ID
	State    string `json:"state"`    // 升级状态,可能的值:[UNKNOWN, PENDING, RUNNING, SUCCEEDED, FAILED, TO_CANCEL, CANCELING, CANCELED, TO_CLEAN, CLEANING, CLEANED]
	Progress int    `json:"progress"` // 升级进度（0-100）
}

// doJsonRequest 执行JSON请求
func doJsonRequest(cli *x_ray.XRay, apiUri string, params interface{}) (*http.Response, error) {
	// 将参数编码为JSON
	jsonData, err := json.Marshal(params)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal params: %v", err)
	}

	// 创建HTTP请求
	req, err := http.NewRequest("POST", apiUri, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("failed to create HTTP request: %v", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("token", cli.GetToken())

	// 创建新的http客户端
	client := &http.Client{}
	// 忽略证书验证
	client.Transport = &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	return client.Do(req)
}
