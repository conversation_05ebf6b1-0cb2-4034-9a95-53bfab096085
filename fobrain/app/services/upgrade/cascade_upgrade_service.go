package cascade_upgrade_service

import (
	"encoding/json"
	"fmt"
	"fobrain/pkg/scheduler"
	"io"
	"os"
	"path/filepath"
	"time"

	"fobrain/fobrain/app/services/system_configs/upgrade"
	"fobrain/fobrain/common/localtime"
	"fobrain/fobrain/logs"
	"fobrain/initialize/mysql"
	"fobrain/models/mysql/cascade_upgrade"
	"fobrain/models/mysql/data_source"
	"fobrain/module/cascade/sdk"
	"fobrain/pkg/utils"

	"github.com/pkg/errors"
)

// x-ray升级记录附加信息
type XrayUpgradeDetailAdditionalInfo struct {
	FileId     string   `json:"file_id"`
	PackageId  int      `json:"package_id"`
	Module     string   `json:"module"`
	EngineList []string `json:"engine_list"`
}

// 下发升级包
func CreateUpgradePackage(model *cascade_upgrade.CascadeUpgradePackage) error {
	model.DistributeStatus = 1
	model.DistributeSuccessCount = 0
	model.DistributeTotalCount = 0
	// 查询所有节点
	nodes, err := sdk.GetAllNodes()
	if err != nil {
		return fmt.Errorf("获取下级节点失败: %v", err)
	}
	model.DistributeTotalCount = len(nodes)
	// 保存升级包记录
	err = cascade_upgrade.NewCascadeUpgradePackageModel().Create(model)
	if err != nil {
		return fmt.Errorf("保存升级包记录失败: %v", err)
	}
	if len(nodes) == 0 {
		logs.GetLogger().Info("下发升级包，下级节点不存在，跳过下发")
		return nil
	}
	// 保存下发记录
	recordModel := cascade_upgrade.NewCascadeUpgradeDistributeRecordModel()
	records := make([]*cascade_upgrade.CascadeUpgradeDistributeRecord, 0)
	for _, node := range nodes {
		records = append(records, &cascade_upgrade.CascadeUpgradeDistributeRecord{
			PackageId:      model.Id,
			NodeId:         node.Id,
			Status:         1,
			DistributeTime: localtime.NewLocalTime(time.Now()),
			Times:          0,
		})
	}
	err = recordModel.BatchCreate(records)
	if err != nil {
		return fmt.Errorf("保存下发记录失败: %v", err)
	}
	return nil
}

// 升级包校验
func UpgradePackageSubPlatformVerify(id string, detailId string) error {
	updateMap := make(map[string]interface{})
	updateMap["verify_status"] = cascade_upgrade.VerifySuccess
	updateMap["verify_err_msg"] = ""

	verifyErr := upgradePackageVerify(id, detailId)
	if verifyErr != nil {
		updateMap["verify_status"] = cascade_upgrade.VerifyFailed
		updateMap["verify_err_msg"] = verifyErr.Error()
	}
	if updateErr := cascade_upgrade.NewCascadeUpgradeRecordModel().UpdateColumns(id, updateMap); updateErr != nil {
		if verifyErr != nil {
			return errors.WithMessage(updateErr, verifyErr.Error()+"且更新升级记录失败")
		}
		return errors.WithMessage(updateErr, "更新升级记录失败")
	}
	if verifyErr != nil {
		return errors.WithMessage(verifyErr, "升级包校验失败")
	}
	return nil
}

var verifyFuncMap = map[int]func(*cascade_upgrade.CascadeUpgradeRecord, string) error{
	cascade_upgrade.PackageTypeFobrain:    VerifyFobrain,
	cascade_upgrade.PackageTypeXRayVuln:   VerifyXRay,
	cascade_upgrade.PackageTypeXRayEngine: VerifyXRay,
	cascade_upgrade.PackageTypeD01:        VerifyD01,
}

func upgradePackageVerify(id string, detailId string) error {
	upgradeRecord, err := cascade_upgrade.NewCascadeUpgradeRecordModel().First(mysql.WithWhere("id = ?", id))
	if err != nil {
		return errors.WithMessage(err, "获取升级记录失败")
	}
	hash, err := utils.GetFileSHA256(upgradeRecord.PackagePath)
	if err != nil {
		return errors.WithMessage(err, "获取升级包哈希值失败")
	}
	if hash != upgradeRecord.PackageHash {
		return fmt.Errorf("升级包完整性校验不通过")
	}

	verifyFunc, exists := verifyFuncMap[upgradeRecord.PackageType]
	if !exists {
		return fmt.Errorf("升级包类型错误")
	}
	return verifyFunc(upgradeRecord, detailId)
}

// 校验fobrain
func VerifyFobrain(upgradeRecord *cascade_upgrade.CascadeUpgradeRecord, detailId string) error {
	storagePath := filepath.Dir(upgradeRecord.PackagePath)
	fileName := upgradeRecord.PackageName
	if fileName == "" {
		// 从包路径中获取文件名
		fileName = filepath.Base(upgradeRecord.PackagePath)
	}
	fileExt := filepath.Ext(fileName)
	_, _, err := upgrade.UpgradeVersionVerify(storagePath, fileName, fileExt)
	if err != nil {
		return errors.WithMessage(err, "校验fobrain升级包版本失败")
	}
	return nil
}

// 校验x-ray
func VerifyXRay(upgradeRecord *cascade_upgrade.CascadeUpgradeRecord, detailId string) error {
	logger := logs.GetLogger()
	detailsModel := cascade_upgrade.NewCascadeUpgradeRecordDetailsModel()
	if detailId != "" {
		detail, err := detailsModel.First(mysql.WithWhere("id =?", detailId))
		if err != nil {
			return errors.WithMessage(err, "获取升级记录详情失败")
		}
		if detail == nil {
			return errors.New("升级记录详情不存在")
		}
		uploadStatus, uploadMsg, verifyStatus, verifyMsg, additionalInfo, err := verifyXray(detail.ThirdPartyId, upgradeRecord.PackagePath, upgradeRecord.PackageName, upgradeRecord.PackageType)
		if err != nil {
			logger.Errorf("校验x-ray(node_id:%d)升级包失败: %v", detail.ThirdPartyId, err)
			return errors.WithMessage(err, "校验x-ray升级包失败")
		}
		// 上传状态
		if uploadStatus {
			// 上传成功
			detail.UploadStatus = 2
			detail.LastUploadTime = localtime.NewLocalTime(time.Now())
			// 上传成功记录校验成功
			if verifyStatus {
				detail.CheckStatus = 2
				detail.LastCheckTime = localtime.NewLocalTime(time.Now())
				jsonStr, err := json.Marshal(additionalInfo)
				if err != nil {
					logger.Errorf("x-ray(node_id:%d)升级包校验记录附加信息序列化失败: %v", detail.ThirdPartyId, err)
					return errors.WithMessage(err, "x-ray升级包校验记录附加信息序列化失败")
				}
				detail.AdditionalInfo = string(jsonStr)
			} else {
				detail.CheckStatus = 3
				detail.CheckErrMsg = verifyMsg
				detail.LastCheckTime = localtime.NewLocalTime(time.Now())
			}
		} else {
			// 上传失败
			detail.UploadStatus = 3
			detail.UploadErrMsg = uploadMsg
			detail.LastUploadTime = localtime.NewLocalTime(time.Now())
		}
		err = detailsModel.CreateOrUpdate(detail)
		if err != nil {
			logger.Errorf("保存x-ray(node_id:%d)升级包校验记录失败: %v", detail.ThirdPartyId, err)
			return errors.WithMessage(err, "保存x-ray升级包校验记录失败")
		}
	} else {
		// 获取x-ray节点
		dataSourceNodes, err := data_source.NewNodeModel().All(mysql.WithWhere("source = ?", "x_ray"), mysql.WithWhere("status = ?", 3))
		if err != nil {
			return errors.WithMessage(err, "获取x-ray节点失败")
		}
		for _, node := range dataSourceNodes {
			// 获取该节点的升级记录详情
			detail, err := detailsModel.First(mysql.WithWhere("record_id = ? AND third_party_id = ?", upgradeRecord.Id, node.Id))
			if err != nil {
				logger.Errorf("获取x-ray(node_id:%d,node_name:%s)升级包校验记录失败: %v", node.Id, node.Name, err)
				continue
			}
			if detail == nil {
				detail = &cascade_upgrade.CascadeUpgradeRecordDetails{
					RecordId:       upgradeRecord.Id,
					ThirdPartyId:   node.Id,
					ThirdPartyType: "x-ray",
				}
			}
			uploadStatus, uploadMsg, verifyStatus, verifyMsg, additionalInfo, err := verifyXray(node.Id, upgradeRecord.PackagePath, upgradeRecord.PackageName, upgradeRecord.PackageType)
			if err != nil {
				logger.Errorf("校验x-ray(node_id:%d,node_name:%s)升级包失败: %v", node.Id, node.Name, err)
				continue
			}
			// 上传状态
			if uploadStatus {
				// 上传成功
				detail.UploadStatus = 2
				detail.LastUploadTime = localtime.NewLocalTime(time.Now())
				// 上传成功记录校验成功
				if verifyStatus {
					detail.CheckStatus = 2
					detail.LastCheckTime = localtime.NewLocalTime(time.Now())
					jsonStr, err := json.Marshal(additionalInfo)
					if err != nil {
						logger.Errorf("x-ray(node_id:%d,node_name:%s)升级包校验记录附加信息序列化失败: %v", node.Id, node.Name, err)
						continue
					}
					detail.AdditionalInfo = string(jsonStr)
				} else {
					detail.CheckStatus = 3
					detail.CheckErrMsg = verifyMsg
					detail.LastCheckTime = localtime.NewLocalTime(time.Now())
				}
			} else {
				// 上传失败
				detail.UploadStatus = 3
				detail.UploadErrMsg = uploadMsg
				detail.LastUploadTime = localtime.NewLocalTime(time.Now())
			}
			err = detailsModel.CreateOrUpdate(detail)
			if err != nil {
				logger.Errorf("保存x-ray(node_id:%d,node_name:%s)升级包校验记录失败: %v", node.Id, node.Name, err)
				continue
			}
		}
	}
	return nil
}

// 校验x-ray
// 返回值：上传状态，上传信息，校验状态，校验信息，附加信息，错误
func verifyXray(nodeId uint64, filePath string, fileName string, packageType int) (uploadStatus bool, uploadMsg string, verifyStatus bool, verifyMsg string, additionalInfo *XrayUpgradeDetailAdditionalInfo, err error) {
	additionalInfo = &XrayUpgradeDetailAdditionalInfo{}
	// 获取x-ray节点
	cli, err := getXrayClient(nodeId)
	if err != nil {
		return false, "", false, "", additionalInfo, errors.WithMessage(err, "获取x-ray客户端失败")
	}
	// 上传文件
	fileId, err := XrayUploadFile(cli, filePath, fileName)
	if err != nil {
		return false, err.Error(), false, "", additionalInfo, nil
	}
	additionalInfo.FileId = fileId
	// 上传升级包
	module := ""
	engineList := []string{"00000000000000000000000000000001"}
	if packageType == cascade_upgrade.PackageTypeXRayVuln {
		module = "VULN_LIBRARY"
	} else if packageType == cascade_upgrade.PackageTypeXRayEngine {
		module = "ENGINE"
		// todo 获取引擎列表
		// engineList, err = GetEngineList(cli, 0, 0)
		// if err != nil {
		// 	return errors.WithMessage(err, "获取引擎列表失败")
		// }
	}
	additionalInfo.Module = module
	additionalInfo.EngineList = engineList
	packageId, err := UploadUpgradePackage(cli, fileId, module)
	if err != nil {
		return false, err.Error(), false, "", additionalInfo, nil
	}
	additionalInfo.PackageId = packageId
	// 校验升级包
	success, err := CheckUpgradePackage(cli, packageId, module, engineList)
	if err != nil {
		return true, "", false, err.Error(), additionalInfo, nil
	}
	if !success {
		return true, "", false, "", additionalInfo, nil
	}
	return true, "", true, "", additionalInfo, nil
}

// 校验d01
func VerifyD01(upgradeRecord *cascade_upgrade.CascadeUpgradeRecord, detailId string) error {
	return errors.New("d01规则库升级包暂不支持校验")
}

// 升级下级平台
func UpgradeSubPlatform(id string, detailId string) error {
	upgradeRecord, err := cascade_upgrade.NewCascadeUpgradeRecordModel().First(mysql.WithWhere("id = ?", id))
	if err != nil {
		return errors.WithMessage(err, "获取升级记录失败")
	}
	if upgradeRecord.UpgradeStatus == cascade_upgrade.Upgrading {
		return errors.New("升级中")
	}
	if upgradeRecord.UpgradeStatus == cascade_upgrade.Upgraded {
		return errors.New("升级完成")
	}
	if upgradeRecord.PackageType == cascade_upgrade.PackageTypeFobrain {
		return UpgradeFobrain(upgradeRecord)
	} else if upgradeRecord.PackageType == cascade_upgrade.PackageTypeXRayVuln || upgradeRecord.PackageType == cascade_upgrade.PackageTypeXRayEngine {
		_, successCount, lastErrMsg, err := UpgradeXRay(fmt.Sprintf("%d", upgradeRecord.Id), detailId)
		if err != nil {
			if detailId != "" {
				return errors.New("升级x-ray失败. 原因:" + lastErrMsg)
			}
			return errors.New("升级x-ray失败. 原因:" + err.Error())
		}
		if detailId != "" {
			return errors.New("升级x-ray失败. 原因:" + lastErrMsg)
		}
		if successCount == 0 {
			return errors.New("升级x-ray失败")
		}
	} else if upgradeRecord.PackageType == cascade_upgrade.PackageTypeD01 {
		_, successCount, lastErrMsg, err := UpgradeD01(upgradeRecord, detailId)
		if err != nil {
			if detailId != "" {
				return errors.New("升级D01失败. 原因:" + lastErrMsg)
			}
			return errors.New("升级D01失败. 原因:" + err.Error())
		}
		if detailId != "" {
			return errors.New("升级D01失败. 原因:" + lastErrMsg)
		}
		if successCount == 0 {
			return errors.New("升级D01失败")
		}
	}
	return nil
}

// 上级触发探针升级
// 参数：packageId 升级包id， detailId 升级详情id,可以为空
func UpgradeProbe(packageId uint64, detailId string) error {
	// 查询升级包信息
	upgradePackage, err := cascade_upgrade.NewCascadeUpgradePackageModel().First(mysql.WithWhere("id = ?", packageId))
	if err != nil {
		return errors.WithMessage(err, "获取升级包信息失败")
	}
	// 上级触发升级只允许触发D01和x-ray升级包
	if upgradePackage.TaskType != cascade_upgrade.TaskTypeD01 && upgradePackage.TaskType != cascade_upgrade.TaskTypeXRay {
		return errors.New("升级包类型错误")
	}
	// 查询是否已有升级记录
	cascadeUpgradeRecord, err := cascade_upgrade.NewCascadeUpgradeRecordModel().First(
		mysql.WithWhere("version = ?", upgradePackage.Version),
		mysql.WithWhere("package_name = ?", upgradePackage.PackageName),
	)
	if err != nil && (err.Error() != "record not found" || cascadeUpgradeRecord.Id > 0) {
		return errors.WithMessage(err, "获取升级记录失败")
	}
	// 是否需要保存升级详情记录
	needSaveDetail := false
	nodes := make([]*data_source.Node, 0)
	if upgradePackage.TaskType == cascade_upgrade.TaskTypeD01 {
		// 获取对应数据源节点，保存升级详情记录
		needSaveDetail = true
		d01nodes, nodeCount, err := data_source.NewNodeModel().Items(0, 0, mysql.WithWhere("source = ?", "d01"))
		if err != nil {
			logs.GetLogger().Errorf("获取数据源节点失败:%v", err)
			return errors.New("获取数据源节点失败")
		}
		if nodeCount == 0 {
			return errors.New("数据源节点不存在")
		}
		nodes = append(nodes, d01nodes...)
	} else if upgradePackage.TaskType == cascade_upgrade.TaskTypeXRay {
		// 获取对应数据源节点，保存升级详情记录
		needSaveDetail = true
		xRayNodes, nodeCount, err := data_source.NewNodeModel().Items(0, 0, mysql.WithWhere("source = ?", "x_ray"))
		if err != nil {
			logs.GetLogger().Errorf("获取数据源节点失败:%v", err)
			return errors.New("获取数据源节点失败")
		}
		if nodeCount == 0 {
			return errors.New("数据源节点不存在")
		}
		nodes = append(nodes, xRayNodes...)
	}
	// 创建升级记录
	upgradeRecord := &cascade_upgrade.CascadeUpgradeRecord{
		PackageName:    upgradePackage.PackageName,
		PackageId:      upgradePackage.Id,
		PackageType:    upgradePackage.PackageType,
		Version:        upgradePackage.Version,
		DownloadStatus: cascade_upgrade.WaitDownload,
		DownloadTime:   nil,
		VerifyStatus:   cascade_upgrade.NotVerify,
		UpgradeStatus:  cascade_upgrade.WaitUpgrade,
		UpgradeTime:    nil,
		PackageHash:    upgradePackage.PackageHash,
		PackagePath:    upgradePackage.PackagePath,
		DownloadUrl:    "",
		TaskType:       upgradePackage.TaskType,
		NodeId:         0,
	}
	err = cascade_upgrade.NewCascadeUpgradeRecordModel().Create(upgradeRecord)
	if err != nil {
		return errors.WithMessage(err, "创建升级记录失败")
	}

	if needSaveDetail {
		// 保存升级详情记录
		if len(nodes) > 0 {
			for _, node := range nodes {
				cascade_upgrade.NewCascadeUpgradeRecordDetailsModel().Create(&cascade_upgrade.CascadeUpgradeRecordDetails{
					RecordId:       upgradeRecord.Id,
					ThirdPartyId:   node.Id,
					ThirdPartyType: node.Source,
					UploadStatus:   1,
					CheckStatus:    1,
					UpgradeStatus:  1,
				})
			}
		} else {
			logs.GetLogger().Errorf("没有对应数据源节点")
		}
	}

	// 开始验证升级包
	// 只有xray需要验证
	if upgradePackage.TaskType == cascade_upgrade.TaskTypeXRay {
		err = VerifyXRay(upgradeRecord, detailId)
		if err != nil {
			return errors.WithMessage(err, "升级包校验失败")
		}
	}

	// 开始升级
	err = UpgradeSubPlatform(fmt.Sprintf("%d", upgradeRecord.Id), detailId)
	if err != nil {
		return errors.WithMessage(err, "升级失败")
	}
	return nil
}

// 升级fobrain
func UpgradeFobrain(upgradeRecord *cascade_upgrade.CascadeUpgradeRecord) error {
	if upgradeRecord.VerifyStatus != cascade_upgrade.VerifySuccess {
		if upgradeRecord.VerifyStatus != cascade_upgrade.VerifyFailed {
			return errors.New("升级包校验未通过")
		}
		verifyErr := upgradePackageVerify(fmt.Sprintf("%d", upgradeRecord.Id), "")
		if verifyErr != nil {
			return errors.WithMessage(verifyErr, "升级包校验失败请重新校验")
		}
	}
	savePath := filepath.Join(upgrade.UpgradeStoragePath, "exec-dir")
	if !utils.IsDirExist(savePath) {
		if mkdirErr := utils.Mkdir(savePath); mkdirErr != nil {
			return errors.WithMessage(mkdirErr, "创建exec-dir文件的目录失败")
		}
	}

	srcFile, err := os.Open(upgradeRecord.PackagePath)
	if err != nil {
		return errors.WithMessage(err, "打开源文件失败")
	}
	defer srcFile.Close()
	fileName := upgradeRecord.PackageName
	destFile, err := os.Create(filepath.Join(savePath, fileName))
	if err != nil {
		return errors.WithMessage(err, "创建目标文件失败")
	}
	defer destFile.Close()

	_, err = io.Copy(destFile, srcFile)
	if err != nil {
		return errors.WithMessage(err, "复制升级包失败")
	}
	// 关闭文件
	err = destFile.Sync()
	if err != nil {
		return errors.WithMessage(err, "同步目标文件失败")
	}
	fileExt := filepath.Ext(fileName)

	updateMap := make(map[string]interface{})
	updateMap["upgrade_status"] = cascade_upgrade.Upgrading
	upgradeErr := upgrade.SystemUpgradeExec(savePath, fileName, fileExt, fmt.Sprintf("%d", upgradeRecord.Id))
	if upgradeErr != nil {
		updateMap["upgrade_err_msg"] = upgradeErr.Error()
		updateMap["upgrade_status"] = cascade_upgrade.UpgradeFailed
		cascade_upgrade.NewCascadeUpgradeRecordModel().UpdateColumns(fmt.Sprintf("%d", upgradeRecord.Id), updateMap)
		return errors.WithMessage(upgradeErr, "升级失败")
	}
	updateMap["upgrade_status"] = cascade_upgrade.Upgraded
	cascade_upgrade.NewCascadeUpgradeRecordModel().UpdateColumns(fmt.Sprintf("%d", upgradeRecord.Id), updateMap)
	// 升级中
	return nil
}

// 升级x-ray
func UpgradeXRay(recordId string, detailId string) (totalCount int, successCount int, lastErrMsg string, err error) {
	detailsModel := cascade_upgrade.NewCascadeUpgradeRecordDetailsModel()
	query := make([]mysql.HandleFunc, 0)
	query = append(query, mysql.WithWhere("record_id =?", recordId))
	query = append(query, mysql.WithWhere("third_party_type =?", "x_ray"))
	query = append(query, mysql.WithWhere("check_status =?", cascade_upgrade.VerifySuccess))
	if detailId != "" {
		query = append(query, mysql.WithWhere("third_party_id =?", detailId))
	}
	// 获取校验成功的升级记录详情
	detailList, _, err := detailsModel.List(0, 0, query...)
	if err != nil {
		return 0, 0, "", errors.WithMessage(err, "获取升级记录详情失败")
	}
	if len(detailList) == 0 {
		return 0, 0, "", errors.New("没有可升级的x-ray节点")
	}

	lastErrMsg = ""
	successCount = 0
	for _, detail := range detailList {
		// 解析附加信息
		additionalInfo := &XrayUpgradeDetailAdditionalInfo{}
		err = json.Unmarshal([]byte(detail.AdditionalInfo), additionalInfo)
		if err != nil {
			lastErrMsg = fmt.Sprintf("解析附加信息失败: %v", err)
			detailsModel.UpdateColumns(detail.Id, map[string]interface{}{
				"upgrade_status":    3,
				"upgrade_err_msg":   fmt.Sprintf("解析附加信息失败: %v", err),
				"last_upgrade_time": localtime.NewLocalTime(time.Now()),
			})
			continue
		}
		xrayClient, err := getXrayClient(detail.ThirdPartyId)
		if err != nil {
			lastErrMsg = fmt.Sprintf("获取x-ray客户端失败: %v", err)
			detailsModel.UpdateColumns(detail.Id, map[string]interface{}{
				"upgrade_status":    3,
				"upgrade_err_msg":   fmt.Sprintf("获取x-ray客户端失败: %v", err),
				"last_upgrade_time": localtime.NewLocalTime(time.Now()),
			})
			continue
		}
		if additionalInfo.Module == "VULN_LIBRARY" {
			ok, err := UpgradeVulnLibrary(xrayClient, additionalInfo.PackageId)
			if err != nil {
				lastErrMsg = fmt.Sprintf("升级漏洞库失败: %v", err)
				detailsModel.UpdateColumns(detail.Id, map[string]interface{}{
					"upgrade_status":    3,
					"upgrade_err_msg":   fmt.Sprintf("升级漏洞库失败: %v", err),
					"last_upgrade_time": localtime.NewLocalTime(time.Now()),
				})
				continue
			}
			if !ok {
				lastErrMsg = fmt.Sprintf("升级漏洞库失败")
				detailsModel.UpdateColumns(detail.Id, map[string]interface{}{
					"upgrade_status":    3,
					"upgrade_err_msg":   "升级漏洞库失败",
					"last_upgrade_time": localtime.NewLocalTime(time.Now()),
				})
				continue
			} else {
				detailsModel.UpdateColumns(detail.Id, map[string]interface{}{
					"upgrade_status":    2,
					"upgrade_err_msg":   "",
					"last_upgrade_time": localtime.NewLocalTime(time.Now()),
				})
			}
		} else if additionalInfo.Module == "ENGINE" {
			ok, err := UpgradeEngine(xrayClient, additionalInfo.PackageId, additionalInfo.EngineList)
			if err != nil {
				lastErrMsg = fmt.Sprintf("升级引擎失败: %v", err)
				detailsModel.UpdateColumns(detail.Id, map[string]interface{}{
					"upgrade_status":    3,
					"upgrade_err_msg":   fmt.Sprintf("升级引擎失败: %v", err),
					"last_upgrade_time": localtime.NewLocalTime(time.Now()),
				})
				continue
			}
			if !ok {
				lastErrMsg = fmt.Sprintf("升级引擎失败")
				detailsModel.UpdateColumns(detail.Id, map[string]interface{}{
					"upgrade_status":    3,
					"upgrade_err_msg":   "升级引擎失败",
					"last_upgrade_time": localtime.NewLocalTime(time.Now()),
				})
				continue
			} else {
				detailsModel.UpdateColumns(detail.Id, map[string]interface{}{
					"upgrade_status":    2,
					"upgrade_err_msg":   "",
					"last_upgrade_time": localtime.NewLocalTime(time.Now()),
				})
			}
		}
		successCount++
	}
	if len(detailList) > 0 && successCount == 0 {
		return len(detailList), 0, lastErrMsg, errors.New("升级失败")
	}
	return len(detailList), successCount, lastErrMsg, nil
}

func UpgradeD01(record *cascade_upgrade.CascadeUpgradeRecord, detailId string) (totalCount int, successCount int, lastErrMsg string, err error) {
	detailsModel := cascade_upgrade.NewCascadeUpgradeRecordDetailsModel()
	query := make([]mysql.HandleFunc, 0)
	query = append(query, mysql.WithWhere("record_id =?", record.Id))
	query = append(query, mysql.WithWhere("third_party_type =?", "d01"))
	if detailId != "" {
		query = append(query, mysql.WithWhere("third_party_id =?", detailId))
	}
	// 获取校验成功的升级记录详情
	detailList, _, err := detailsModel.List(0, 0, query...)
	if err != nil {
		return 0, 0, "", errors.WithMessage(err, "获取升级记录详情失败")
	}
	if len(detailList) == 0 {
		return 0, 0, "", errors.New("没有可升级的d01节点")
	}

	lastErrMsg = ""
	successCount = 0
	for _, detail := range detailList {
		d01Client, err := getD01Client(detail.ThirdPartyId)
		if err != nil {
			lastErrMsg = fmt.Sprintf("获取d01客户端失败: %v", err)
			detailsModel.UpdateColumns(detail.Id, map[string]interface{}{
				"upgrade_status":    3,
				"upgrade_err_msg":   fmt.Sprintf("获取d01客户端失败: %v", err),
				"last_upgrade_time": localtime.NewLocalTime(time.Now()),
			})
			continue
		}
		err = CheckAndUpgradeD01(d01Client, record.PackagePath, record.PackageName)
		if err != nil {
			lastErrMsg = fmt.Sprintf("升级d01失败: %v", err)
			detailsModel.UpdateColumns(detail.Id, map[string]interface{}{
				"upgrade_status":    3,
				"upgrade_err_msg":   fmt.Sprintf("升级d01失败: %v", err),
				"last_upgrade_time": localtime.NewLocalTime(time.Now()),
			})
			continue
		} else {
			detailsModel.UpdateColumns(detail.Id, map[string]interface{}{
				"upgrade_status":    4, // 升级中
				"upgrade_err_msg":   "",
				"last_upgrade_time": localtime.NewLocalTime(time.Now()),
			})

			// 开启定时任务，每10秒执行一次，获取升级进度
			schedulerTaskName := fmt.Sprintf("d01_upgrade_process_%d", detail.Id)
			scheduler.Start(schedulerTaskName, 10*time.Second, false, func() {
				status, err := GetD01UpgradeProgress(d01Client)
				if err != nil {
					logs.GetLogger().Errorf("获取d01升级进度失败: %v", err)
					detailsModel.UpdateColumns(detail.Id, map[string]interface{}{
						"upgrade_status":    5, // 查询异常，判定为未知
						"upgrade_err_msg":   "获取d01升级进度失败",
						"last_upgrade_time": localtime.NewLocalTime(time.Now()),
					})
					scheduler.Stop(schedulerTaskName)
				}
				logs.GetLogger().Infof("d01升级进度: %v", status)
				if status != "升级中" {
					detailsModel.UpdateColumns(detail.Id, map[string]interface{}{
						"upgrade_status": func() int {
							if status == "升级成功" {
								return 2
							} else if status == "升级失败" {
								return 3
							} else if status == "未知" {
								return 5
							}
							return 5
						}(),
						"upgrade_err_msg":   "",
						"last_upgrade_time": localtime.NewLocalTime(time.Now()),
					})
					scheduler.Stop(schedulerTaskName)
				}
			})
		}
		successCount++
	}
	if len(detailList) > 0 && successCount == 0 {
		return len(detailList), 0, lastErrMsg, errors.New("升级失败")
	}
	return len(detailList), successCount, lastErrMsg, nil
}
