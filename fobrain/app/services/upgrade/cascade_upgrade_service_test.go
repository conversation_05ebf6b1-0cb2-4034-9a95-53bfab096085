package cascade_upgrade_service

import (
	"fmt"
	"os"
	"os/exec"
	"testing"
	"time"

	"fobrain/fobrain/app/services/node/d01"
	"fobrain/fobrain/app/services/node/x_ray"
	"fobrain/fobrain/app/services/system_configs/upgrade"
	"fobrain/initialize/mysql"
	"fobrain/models/mysql/cascade_upgrade"
	"fobrain/models/mysql/data_source"
	"fobrain/pkg/utils"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/pkg/errors"
	"github.com/stretchr/testify/assert"

	testcommon "fobrain/fobrain/tests/common_test"
)

// func TestCreateUpgradePackage(t *testing.T) {
// 	getAllNodesPatches := gomonkey.ApplyFuncReturn(sdk.GetAllNodes, []*models.DataCaptureNodes{
// 		{
// 			BaseModel: mysql.BaseModel{
// 				Ids: 1,
// 			},
// 		},
// 		{
// 			BaseModel: mysql.BaseModel{
// 				Ids: 2,
// 			},
// 		},
// 	}, nil)
// 	defer getAllNodesPatches.Reset()
// 	model := &cascade_upgrade.CascadeUpgradePackage{
// 		PackageType: cascade_upgrade.PackageTypeFobrain,
// 	}

// 	// 设置模拟行为
// 	createPatches := gomonkey.ApplyMethodReturn(model, "Create", nil)
// 	batchCreatePatches := gomonkey.ApplyMethodReturn(&cascade_upgrade.CascadeUpgradeDistributeRecord{}, "BatchCreate", nil)
// 	defer createPatches.Reset()
// 	defer batchCreatePatches.Reset()

// 	// 调用被测试函数
// 	err := CreateUpgradePackage(model)

// 	// 验证结果
// 	assert.NoError(t, err)
// 	assert.Equal(t, 2, model.DistributeTotalCount)
// }

func TestUpgradePackageSubPlatformVerify(t *testing.T) {
	t.Run("成功", func(t *testing.T) {
		verifyPatches := gomonkey.ApplyFuncReturn(upgradePackageVerify, nil)

		originalModel := cascade_upgrade.NewCascadeUpgradeRecordModel()
		patch := gomonkey.ApplyMethodReturn(originalModel, "UpdateColumns", nil)
		time.Sleep(time.Second)
		err := UpgradePackageSubPlatformVerify("1", "")
		patch.Reset()
		verifyPatches.Reset()
		assert.NoError(t, err)
	})

	t.Run("校验失败", func(t *testing.T) {
		verifyPatches := gomonkey.ApplyFuncReturn(upgradePackageVerify, errors.New("校验失败"))

		time.Sleep(time.Second)
		err := UpgradePackageSubPlatformVerify("1", "")
		verifyPatches.Reset()
		assert.Error(t, err)
	})

	t.Run("更新失败", func(t *testing.T) {
		verifyPatches := gomonkey.ApplyFuncReturn(upgradePackageVerify, nil)
		time.Sleep(time.Second)
		err := UpgradePackageSubPlatformVerify("1", "")
		verifyPatches.Reset()
		assert.Error(t, err)
	})
}

func TestUpgradePackageVerify(t *testing.T) {
	t.Run("验证成功", func(t *testing.T) {
		mockDb := testcommon.GetMysqlMock()
		defer mockDb.Close()

		// 创建测试文件
		testFile := "test_verify_package.tar.gz"
		file, err := os.Create(testFile)
		if err != nil {
			t.Fatal("创建测试文件失败:", err)
		}
		file.WriteString("test content for verification")
		file.Close()
		defer os.Remove(testFile)

		// 获取真实文件的哈希值
		realHash, err := utils.GetFileSHA256(testFile)
		if err != nil {
			t.Fatal("获取文件哈希失败:", err)
		}

		// Mock数据库查询
		mockDb.ExpectQuery("").
			WillReturnRows(mockDb.NewRows([]string{"id", "package_path", "package_hash", "package_type", "package_name"}).
				AddRow(1, testFile, realHash, cascade_upgrade.PackageTypeFobrain, "test_package.tar.gz"))

		patches := gomonkey.NewPatches()
		defer patches.Reset()

		// 模拟 VerifyFobrain 函数
		patches.ApplyFuncReturn(VerifyFobrain, nil)

		time.Sleep(time.Second)
		err = upgradePackageVerify("1", "")
		t.Log(err)
		assert.NoError(t, err)
	})

	t.Run("验证失败-哈希不匹配", func(t *testing.T) {
		mockDb := testcommon.GetMysqlMock()
		defer mockDb.Close()

		// 创建测试文件
		testFile := "test_verify_package2.tar.gz"
		file, err := os.Create(testFile)
		if err != nil {
			t.Fatal("创建测试文件失败:", err)
		}
		file.WriteString("test content for verification")
		file.Close()
		defer os.Remove(testFile)

		// Mock数据库查询，使用错误的哈希值
		mockDb.ExpectQuery("").
			WillReturnRows(mockDb.NewRows([]string{"id", "package_path", "package_hash", "package_type", "package_name"}).
				AddRow(1, testFile, "wrong_hash_value", cascade_upgrade.PackageTypeFobrain, "test_package.tar.gz"))

		time.Sleep(time.Second)
		err = upgradePackageVerify("1", "")
		t.Log(err)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "升级包完整性校验不通过")
	})

	t.Run("验证失败-未知包类型", func(t *testing.T) {
		mockDb := testcommon.GetMysqlMock()
		defer mockDb.Close()

		// 创建测试文件
		testFile := "test_verify_package3.tar.gz"
		file, err := os.Create(testFile)
		if err != nil {
			t.Fatal("创建测试文件失败:", err)
		}
		file.WriteString("test content for verification")
		file.Close()
		defer os.Remove(testFile)

		// 获取真实文件的哈希值
		realHash, err := utils.GetFileSHA256(testFile)
		if err != nil {
			t.Fatal("获取文件哈希失败:", err)
		}

		// Mock数据库查询，使用未知的包类型
		mockDb.ExpectQuery("").
			WillReturnRows(mockDb.NewRows([]string{"id", "package_path", "package_hash", "package_type", "package_name"}).
				AddRow(1, testFile, realHash, 999, "test_package.tar.gz")) // 未知包类型

		time.Sleep(time.Second)
		err = upgradePackageVerify("1", "")
		t.Log(err)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "升级包类型错误")
	})
}
func TestVerifyFobrain(t *testing.T) {
	versionPatches := gomonkey.ApplyGlobalVar(&upgrade.VersionFilePath, "./version")
	defer versionPatches.Reset()

	patches := gomonkey.ApplyGlobalVar(&upgrade.UpgradeStoragePath, "./")
	defer patches.Reset()

	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyFuncReturn(upgrade.CheckArchitecture, true).Reset()

	info := &upgrade.VersionInfo{
		ProductModel:   "fobrain-00001",
		ReleaseVersion: "2.0.0",
		ReleaseDate:    "2024-09-06",
		ReleaseDesc:    "全新升级Fobrain带给您丝滑体验",
		VersionDependencies: []string{
			"1.0.0",
			"1.9.0",
		},
	}
	lowerInfo := &upgrade.VersionInfo{
		ProductModel:        "fobrain-00000",
		ReleaseVersion:      "1.9.0",
		ReleaseDate:         "2024-08-06",
		ReleaseDesc:         "全新升级Fobrain带给您丝滑体验",
		VersionDependencies: []string{},
	}

	err := upgrade.GenVersionFile("./version", info)
	if err != nil {
		t.Fatal(err)
	}
	// 创建文件
	exampleFileName := "example.txt"
	file, err := os.Create(exampleFileName)
	if err != nil {
		t.Fatal("Error creating file:", err)
	}
	defer file.Close()

	// 写入内容到文件
	_, err = file.WriteString("Hello, World!\n")
	if err != nil {
		t.Fatal("Error writing to file:", err)
	}
	// 要创建的 .tar.gz 文件名
	tarGzFileName := "example.tar.gz"
	// 准备 tar 命令参数
	os.Mkdir("./tmp", 0755)
	defer os.RemoveAll("./tmp")
	execArgs := []string{"-cvzf", fmt.Sprintf("./tmp/%s", tarGzFileName), exampleFileName, "version"}
	//  执行 tar 命令
	cmd := exec.Command("tar", execArgs...)
	// 运行命令并捕获输出
	output, err := cmd.CombinedOutput()
	if err != nil {
		t.Fatalf("Error executing tar command: %v, Command output: %s\n", err, output)
	}
	os.Remove(exampleFileName)
	os.Remove("./version")

	upgrade.GenVersionFile(upgrade.VersionFilePath, lowerInfo)
	defer os.Remove(upgrade.VersionFilePath)
	defer os.RemoveAll(time.Now().Format(utils.DateMinuteLayout))

	upgradeRecord := &cascade_upgrade.CascadeUpgradeRecord{
		PackagePath: "./tmp/example.tar.gz",
		PackageName: "example.tar.gz",
	}

	err = VerifyFobrain(upgradeRecord, "")
	assert.NoError(t, err, "VerifyFobrain应该不返回错误")
	os.Remove(upgrade.VersionFilePath)
	os.RemoveAll(time.Now().Format(utils.DateMinuteLayout))
}

// 测试VerifyD01函数
func TestVerifyD01(t *testing.T) {
	upgradeRecord := &cascade_upgrade.CascadeUpgradeRecord{}
	err := VerifyD01(upgradeRecord, "")
	assert.Error(t, err, "VerifyD01应该返回错误")
	assert.Equal(t, "d01规则库升级包暂不支持校验", err.Error())
}

// 测试VerifyXRay函数
func TestVerifyXRay(t *testing.T) {
	patches := gomonkey.NewPatches()

	patches.ApplyMethodReturn(data_source.NewNodeModel(), "All", []*data_source.Node{
		{
			SoftDeleteModel: mysql.SoftDeleteModel{
				Id: 1,
			},
			Source: "x_ray",
			Status: 3,
		},
	}, nil)

	detailsModel := cascade_upgrade.NewCascadeUpgradeRecordDetailsModel()
	patches.ApplyMethodReturn(detailsModel, "First", nil, nil)
	patches.ApplyMethodReturn(detailsModel, "CreateOrUpdate", nil)

	patches.ApplyFuncReturn(verifyXray, true, "", true, "", nil, nil)

	err := VerifyXRay(&cascade_upgrade.CascadeUpgradeRecord{}, "")
	assert.NoError(t, err, "VerifyXRay应该不返回错误")
	patches.Reset()
}

func TestVerifyXray(t *testing.T) {
	time.Sleep(time.Second)
	nodeId := uint64(1)
	filePath := "test/path"
	fileName := "testfile"
	packageType := cascade_upgrade.PackageTypeXRayVuln

	patches := gomonkey.NewPatches()

	patches.ApplyFuncReturn(upgrade.CheckArchitecture, true)

	patches.ApplyFuncReturn(getXrayClient, &x_ray.XRay{}, nil)
	patches.ApplyFuncReturn(XrayUploadFile, "fileId123", nil)
	patches.ApplyFuncReturn(UploadUpgradePackage, 1, nil)
	patches.ApplyFuncReturn(CheckUpgradePackage, true, nil)

	// 执行测试
	uploadStatus, uploadMsg, verifyStatus, verifyMsg, additionalInfo, err := verifyXray(nodeId, filePath, fileName, packageType)

	// 验证结果
	assert.True(t, uploadStatus)
	assert.Empty(t, uploadMsg)
	assert.True(t, verifyStatus)
	assert.Empty(t, verifyMsg)
	assert.NotNil(t, additionalInfo)
	assert.NoError(t, err)
	patches.Reset()
}

func TestUpgradeSubPlatform_Success(t *testing.T) {
	patches := gomonkey.NewPatches()
	defer patches.Reset()

	patches.ApplyFuncReturn(cascade_upgrade.NewCascadeUpgradeRecordModel, &cascade_upgrade.CascadeUpgradeRecord{})

	// 模拟 First 方法的行为
	patches.ApplyMethodReturn(&cascade_upgrade.CascadeUpgradeRecord{}, "First", &cascade_upgrade.CascadeUpgradeRecord{
		PackagePath:    "./tmp/example.tar.gz",
		PackageName:    "example.tar.gz",
		PackageType:    cascade_upgrade.PackageTypeFobrain,
		UpgradeStatus:  cascade_upgrade.WaitUpgrade,
		VerifyStatus:   cascade_upgrade.VerifySuccess,
		DownloadStatus: int64(cascade_upgrade.Downloaded),
	}, nil)
	patches.ApplyMethodReturn(&cascade_upgrade.CascadeUpgradeRecord{}, "UpdateColumns", nil)

	versionPatches := gomonkey.ApplyGlobalVar(&upgrade.VersionFilePath, "./version")
	defer versionPatches.Reset()

	storagePatches := gomonkey.ApplyGlobalVar(&upgrade.UpgradeStoragePath, "./")
	defer storagePatches.Reset()

	info := &upgrade.VersionInfo{
		ProductModel:   "fobrain-00001",
		ReleaseVersion: "2.0.0",
		ReleaseDate:    "2024-09-06",
		ReleaseDesc:    "全新升级Fobrain带给您丝滑体验",
		VersionDependencies: []string{
			"1.0.0",
			"1.9.0",
		},
	}
	lowerInfo := &upgrade.VersionInfo{
		ProductModel:        "fobrain-00000",
		ReleaseVersion:      "1.9.0",
		ReleaseDate:         "2024-08-06",
		ReleaseDesc:         "全新升级Fobrain带给您丝滑体验",
		VersionDependencies: []string{},
	}

	err := upgrade.GenVersionFile("./version", info)
	if err != nil {
		t.Fatal(err)
	}
	// 创建文件
	exampleFileName := "example.txt"
	file, err := os.Create(exampleFileName)
	if err != nil {
		t.Fatal("Error creating file:", err)
	}
	defer file.Close()

	// 写入内容到文件
	_, err = file.WriteString("Hello, World!\n")
	if err != nil {
		t.Fatal("Error writing to file:", err)
	}
	// 要创建的 .tar.gz 文件名
	tarGzFileName := "example.tar.gz"
	// 准备 tar 命令参数
	os.Mkdir("./tmp", 0755)
	defer os.RemoveAll("./tmp")
	execArgs := []string{"-cvzf", fmt.Sprintf("./tmp/%s", tarGzFileName), exampleFileName, "version"}
	//  执行 tar 命令
	cmd := exec.Command("tar", execArgs...)
	// 运行命令并捕获输出
	output, err := cmd.CombinedOutput()
	if err != nil {
		t.Fatalf("Error executing tar command: %v, Command output: %s\n", err, output)
	}
	os.Remove(exampleFileName)
	os.Remove("./version")

	upgrade.GenVersionFile(upgrade.VersionFilePath, lowerInfo)
	defer os.Remove(upgrade.VersionFilePath)
	defer os.RemoveAll(time.Now().Format(utils.DateMinuteLayout))

	patches.ApplyFuncReturn(upgrade.SystemUpgradeExec, nil)
	err = UpgradeSubPlatform("1", "")
	assert.NoError(t, err, "UpgradeSubPlatform应该不返回错误")
	os.Remove(upgrade.VersionFilePath)
	os.RemoveAll(time.Now().Format(utils.DateMinuteLayout))
	os.RemoveAll("./exec-dir")
}

func TestUpgradePackageSubPlatformVerify_Fobrain(t *testing.T) {
	t.Run("成功", func(t *testing.T) {
		patches := gomonkey.NewPatches()
		defer patches.Reset()

		// 模拟 NewCascadeUpgradeRecordModel
		patches.ApplyFunc(cascade_upgrade.NewCascadeUpgradeRecordModel, func() *cascade_upgrade.CascadeUpgradeRecord {
			return &cascade_upgrade.CascadeUpgradeRecord{}
		})

		// 测试成功场景
		patches.ApplyFunc(upgradePackageVerify, func(id string) error {
			return nil // 模拟成功
		})
		patches.ApplyMethod(&cascade_upgrade.CascadeUpgradeRecord{}, "UpdateColumns", func(_ *cascade_upgrade.CascadeUpgradeRecord, id string, updateMap map[string]interface{}) error {
			return nil // 模拟成功更新
		})

		err := UpgradePackageSubPlatformVerify("1", "")
		assert.NoError(t, err, "UpgradePackageSubPlatformVerify 应该不返回错误")

	})
}

func TestUpgradeXRay(t *testing.T) {
	// Mock data
	detail_vuln := &cascade_upgrade.CascadeUpgradeRecordDetails{
		BaseModel: mysql.BaseModel{
			Id: 1,
		},
		AdditionalInfo: `{"Module":"VULN_LIBRARY","PackageId":"123"}`,
	}
	detail_engine := &cascade_upgrade.CascadeUpgradeRecordDetails{
		BaseModel: mysql.BaseModel{
			Id: 2,
		},
		AdditionalInfo: `{"Module":"ENGINE","PackageId":"123","EngineList":["1","2"]}`,
	}
	detailList := []*cascade_upgrade.CascadeUpgradeRecordDetails{detail_vuln, detail_engine}

	// Mock expectations
	patches := gomonkey.NewPatches()
	defer patches.Reset()

	patches.ApplyFuncReturn(getXrayClient, &x_ray.XRay{}, nil)
	detailsModel := &cascade_upgrade.CascadeUpgradeRecordDetails{}
	patches.ApplyMethodReturn(detailsModel, "List", detailList, int64(1), nil)
	patches.ApplyMethodReturn(detailsModel, "UpdateColumns", nil)
	patches.ApplyFuncReturn(UpgradeVulnLibrary, true, nil)
	patches.ApplyFuncReturn(UpgradeEngine, true, nil)

	// Call the function
	totalCount, successCount, lastErrMsg, err := UpgradeXRay("1", "")

	// Assertions
	assert.NoError(t, err)
	assert.Equal(t, 2, totalCount)
	assert.Equal(t, 2, successCount)
	assert.Empty(t, lastErrMsg)
}

func TestUpgradeD01(t *testing.T) {
	// Mock data
	detail := &cascade_upgrade.CascadeUpgradeRecordDetails{
		BaseModel: mysql.BaseModel{
			Id: 1,
		},
		ThirdPartyId: 123,
	}
	detailList := []*cascade_upgrade.CascadeUpgradeRecordDetails{detail}

	patches := gomonkey.NewPatches()

	patches.ApplyFuncReturn(getD01Client, &d01.D01{}, nil)
	detailsModel := &cascade_upgrade.CascadeUpgradeRecordDetails{}
	patches.ApplyMethodReturn(detailsModel, "List", detailList, int64(1), nil)
	patches.ApplyMethodReturn(detailsModel, "UpdateColumns", nil)
	patches.ApplyFuncReturn(CheckAndUpgradeD01, nil)

	// Create a mock record
	record := &cascade_upgrade.CascadeUpgradeRecord{
		BaseModel: mysql.BaseModel{
			Id: 1,
		},
		PackagePath: "path/to/package",
		PackageName: "packageName",
	}

	time.Sleep(time.Second)
	// Call the function
	totalCount, successCount, lastErrMsg, err := UpgradeD01(record, "")
	patches.Reset()
	// Assertions
	assert.NoError(t, err)
	assert.Equal(t, 1, totalCount)
	assert.Equal(t, 1, successCount)
	assert.Empty(t, lastErrMsg)
}

func TestUpgradeProbe(t *testing.T) {
	patches := gomonkey.NewPatches()
	defer patches.Reset()

	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()

	mockDb.ExpectQuery("SELECT * FROM `cascade_upgrade_packages` WHERE id = ? AND is_deleted = ? ORDER BY `cascade_upgrade_packages`.`id` LIMIT 1").WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg()).
		WillReturnRows(sqlmock.NewRows([]string{"id", "task_type"}).AddRow(1, cascade_upgrade.TaskTypeD01))

	mockDb.ExpectQuery("SELECT * FROM `cascade_upgrade_records` WHERE version = ? AND package_name = ? ORDER BY `cascade_upgrade_records`.`id` LIMIT 1").WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg()).
		WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))

	patches.ApplyMethodReturn(data_source.NewNodeModel(), "Items", []*data_source.Node{
		{
			SoftDeleteModel: mysql.SoftDeleteModel{
				Id: 1,
			},
			Source: "d01",
			Status: 3,
		},
	}, int64(1), nil)

	patches.ApplyMethodReturn(cascade_upgrade.NewCascadeUpgradeRecordModel(), "Create", nil)
	patches.ApplyMethodReturn(cascade_upgrade.NewCascadeUpgradeRecordDetailsModel(), "Create", nil)
	patches.ApplyFuncReturn(UpgradeSubPlatform, nil)
	assert.NoError(t, UpgradeProbe(1, ""))

}
