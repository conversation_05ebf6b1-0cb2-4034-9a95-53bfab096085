package cascade_upgrade_service

import (
	"encoding/json"
	"fobrain/fobrain/app/services/node/x_ray"
	"fobrain/models/mysql/data_source"
	"net/http"
	"net/http/httptest"
	"os"
	"reflect"
	"testing"
	"time"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/pkg/errors"
	"github.com/stretchr/testify/assert"
)

func TestGetXrayClient(t *testing.T) {
	nodeId := uint64(12345)

	// 创建一个 XRay 实例
	xrayInstance := &x_ray.XRay{}

	// 使用 gomonkey 模拟 x_ray.New() 函数
	patches := gomonkey.ApplyFunc(x_ray.New, func() *x_ray.XRay {
		return xrayInstance
	})
	defer patches.Reset()

	// 模拟 SetNode 方法
	patches.ApplyMethod(reflect.TypeOf(xrayInstance), "SetNode", func(_ *x_ray.XRay, _ uint64) error {
		return nil
	})

	// 模拟 GetNodeConfig 方法
	patches.ApplyMethod(reflect.TypeOf(&data_source.DataNodeConfig{}), "GetNodeConfig", func(_ *data_source.DataNodeConfig, _ uint64) (map[string]interface{}, error) {
		return map[string]interface{}{"protocol": "http", "ip": "127.0.0.1"}, nil
	})

	// 模拟 SetConfig 方法
	patches.ApplyMethod(reflect.TypeOf(xrayInstance), "SetConfig", func(_ *x_ray.XRay, _ map[string]interface{}) error {
		return nil
	})

	// 调用被测试的函数
	client, err := getXrayClient(nodeId)

	// 断言
	assert.NoError(t, err)
	assert.NotNil(t, client)
}

func TestGetXrayClient_SetNodeError(t *testing.T) {
	nodeId := uint64(12345)

	// 创建一个 XRay 实例
	xrayInstance := &x_ray.XRay{}

	time.Sleep(time.Second)
	// 使用 gomonkey 模拟 x_ray.New() 函数
	patches := gomonkey.ApplyFunc(x_ray.New, func() *x_ray.XRay {
		return xrayInstance
	})

	// 模拟 SetNode 方法返回错误
	patches.ApplyMethod(reflect.TypeOf(xrayInstance), "SetNode", func(_ *x_ray.XRay, _ uint64) error {
		return errors.New("set node error")
	})

	// 调用被测试的函数
	client, err := getXrayClient(nodeId)
	patches.Reset()
	// 断言
	assert.Error(t, err)
	assert.Nil(t, client)
}

func TestXrayUploadFile(t *testing.T) {
	// 创建一个临时文件用于测试
	tempFile, err := os.CreateTemp("", "testfile")
	assert.NoError(t, err)
	defer os.Remove(tempFile.Name())

	// 写入一些内容到临时文件
	_, err = tempFile.WriteString("test content")
	assert.NoError(t, err)
	tempFile.Close()

	// 创建一个测试HTTP服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// 检查请求方法和路径
		assert.Equal(t, "POST", r.Method)
		assert.Contains(t, r.URL.Path, "/api/v2/upload_file/")

		// 模拟响应
		response := map[string]interface{}{
			"err": "",
			"msg": "success",
			"data": map[string]string{
				"uuid": "12345",
			},
		}
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(response)
	}))
	defer server.Close()

	// 模拟XRay客户端
	cli := &x_ray.XRay{}
	cli.SetConfig(map[string]interface{}{
		"protocol": "http",
		"ip":       server.Listener.Addr().String(),
	})
	getToken := gomonkey.ApplyMethodReturn(cli, "GetToken", "mock-token")
	defer getToken.Reset()

	// 调用XrayUploadFile函数
	uuid, err := XrayUploadFile(cli, tempFile.Name(), "testfile")
	assert.NoError(t, err)
	assert.Equal(t, "12345", uuid)
}

func TestDoJsonRequest(t *testing.T) {
	// 创建一个模拟的 HTTP 服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		assert.Equal(t, "application/json", r.Header.Get("Content-Type"))
		assert.Equal(t, "mock-token", r.Header.Get("token"))

		var params map[string]string
		err := json.NewDecoder(r.Body).Decode(&params)
		assert.NoError(t, err)
		assert.Equal(t, "value", params["key"])

		w.WriteHeader(http.StatusOK)
		w.Write([]byte(`{"err": "", "msg": "success"}`))
	}))
	defer server.Close()

	// 模拟XRay客户端
	cli := &x_ray.XRay{}
	cli.SetConfig(map[string]interface{}{
		"protocol": "http",
		"ip":       server.Listener.Addr().String(),
	})
	getToken := gomonkey.ApplyMethodReturn(cli, "GetToken", "mock-token")
	defer getToken.Reset()
	params := map[string]string{"key": "value"}

	resp, err := doJsonRequest(cli, server.URL+"/api/v2/upload_file/", params)

	assert.NoError(t, err)
	assert.NotNil(t, resp)

	defer resp.Body.Close()
	var response map[string]interface{}
	err = json.NewDecoder(resp.Body).Decode(&response)

	assert.NoError(t, err)
	assert.Equal(t, "success", response["msg"])
}

func TestUploadUpgradePackage(t *testing.T) {
	// 创建一个模拟的 HTTP 服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		assert.Equal(t, "application/json", r.Header.Get("Content-Type"))
		assert.Equal(t, "mock-token", r.Header.Get("token"))

		var params map[string]string
		err := json.NewDecoder(r.Body).Decode(&params)
		assert.NoError(t, err)
		assert.Equal(t, "mock-uuid", params["file_uuid"])
		assert.Equal(t, "ENGINE", params["module"])

		w.WriteHeader(http.StatusOK)
		w.Write([]byte(`{"err": "", "msg": "success", "data": {"package_id": 123}}`))
	}))
	defer server.Close()

	// 模拟XRay客户端
	cli := &x_ray.XRay{}
	cli.SetConfig(map[string]interface{}{
		"protocol": "http",
		"ip":       server.Listener.Addr().String(),
	})
	getToken := gomonkey.ApplyMethodReturn(cli, "GetToken", "mock-token")
	defer getToken.Reset()

	packageId, err := UploadUpgradePackage(cli, "mock-uuid", "ENGINE")

	assert.NoError(t, err)
	assert.Equal(t, 123, packageId)
}

func TestCheckUpgradePackage(t *testing.T) {
	// 创建一个模拟的 HTTP 服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		assert.Equal(t, "application/json", r.Header.Get("Content-Type"))
		assert.Equal(t, "mock-token", r.Header.Get("token"))

		var params map[string]interface{}
		err := json.NewDecoder(r.Body).Decode(&params)
		assert.NoError(t, err)
		assert.Equal(t, float64(123), params["package_id"])
		assert.Equal(t, "ENGINE", params["module"])
		assert.ElementsMatch(t, []interface{}{"engine1", "engine2"}, params["engine_ids"])

		w.WriteHeader(http.StatusOK)
		w.Write([]byte(`{"err": "", "msg": "success", "data": {"status": true}}`))
	}))
	defer server.Close()

	// 模拟XRay客户端
	cli := &x_ray.XRay{}
	cli.SetConfig(map[string]interface{}{
		"protocol": "http",
		"ip":       server.Listener.Addr().String(),
	})
	getToken := gomonkey.ApplyMethodReturn(cli, "GetToken", "mock-token")
	defer getToken.Reset()

	status, err := CheckUpgradePackage(cli, 123, "ENGINE", []string{"engine1", "engine2"})

	assert.NoError(t, err)
	assert.True(t, status)
}

func TestUpgradeVulnLibrary(t *testing.T) {
	// 创建一个模拟的 HTTP 服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		assert.Equal(t, "application/json", r.Header.Get("Content-Type"))
		assert.Equal(t, "mock-token", r.Header.Get("token"))

		var params map[string]interface{}
		err := json.NewDecoder(r.Body).Decode(&params)
		assert.NoError(t, err)
		assert.Equal(t, float64(123), params["package_id"])

		w.WriteHeader(http.StatusOK)
		w.Write([]byte(`{"err": "", "msg": "success", "data": {"success": true}}`))
	}))
	defer server.Close()

	// 模拟XRay客户端
	cli := &x_ray.XRay{}
	cli.SetConfig(map[string]interface{}{
		"protocol": "http",
		"ip":       server.Listener.Addr().String(),
	})
	getToken := gomonkey.ApplyMethodReturn(cli, "GetToken", "mock-token")

	patches := gomonkey.ApplyFuncReturn(CheckUpgradePackage, true, nil)
	time.Sleep(time.Second)
	success, err := UpgradeVulnLibrary(cli, 123)
	patches.Reset()
	getToken.Reset()
	assert.NoError(t, err)
	assert.True(t, success)
}

func TestUpgradeEngine(t *testing.T) {
	// 创建一个模拟的 HTTP 服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		assert.Equal(t, "application/json", r.Header.Get("Content-Type"))
		assert.Equal(t, "mock-token", r.Header.Get("token"))

		var params map[string]interface{}
		err := json.NewDecoder(r.Body).Decode(&params)
		assert.NoError(t, err)
		assert.Equal(t, float64(123), params["package_id"])
		assert.ElementsMatch(t, []interface{}{"engine1", "engine2"}, params["engine_ids"])

		w.WriteHeader(http.StatusOK)
		w.Write([]byte(`{"err": "", "msg": "success", "data": {"success": true}}`))
	}))
	defer server.Close()

	// 模拟XRay客户端
	cli := &x_ray.XRay{}
	cli.SetConfig(map[string]interface{}{
		"protocol": "http",
		"ip":       server.Listener.Addr().String(),
	})
	getToken := gomonkey.ApplyMethodReturn(cli, "GetToken", "mock-token")
	defer getToken.Reset()

	success, err := UpgradeEngine(cli, 123, []string{"engine1", "engine2"})

	assert.NoError(t, err)
	assert.True(t, success)
}

func TestGetEngineList(t *testing.T) {
	// 创建一个模拟的 HTTP 服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		assert.Equal(t, "application/json", r.Header.Get("Content-Type"))
		assert.Equal(t, "mock-token", r.Header.Get("token"))

		var params map[string]interface{}
		err := json.NewDecoder(r.Body).Decode(&params)
		assert.NoError(t, err)
		assert.Equal(t, float64(10), params["limit"])
		assert.Equal(t, float64(0), params["offset"])

		w.WriteHeader(http.StatusOK)
		w.Write([]byte(`{"err": "", "msg": "success", "data": {"content": [{"id": "engine1"}, {"id": "engine2"}]}}`))
	}))
	defer server.Close()

	// 模拟XRay客户端
	cli := &x_ray.XRay{}
	cli.SetConfig(map[string]interface{}{
		"protocol": "http",
		"ip":       server.Listener.Addr().String(),
	})
	getToken := gomonkey.ApplyMethodReturn(cli, "GetToken", "mock-token")
	defer getToken.Reset()

	engineIds, err := GetEngineList(cli, 10, 0)

	assert.NoError(t, err)
	assert.ElementsMatch(t, []string{"engine1", "engine2"}, engineIds)
}

func TestGetExecutionList(t *testing.T) {
	// 创建一个模拟的 HTTP 服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		assert.Equal(t, "application/json", r.Header.Get("Content-Type"))
		assert.Equal(t, "mock-token", r.Header.Get("token"))

		var params map[string]interface{}
		err := json.NewDecoder(r.Body).Decode(&params)
		assert.NoError(t, err)
		assert.Equal(t, float64(10), params["limit"])
		assert.Equal(t, float64(0), params["offset"])
		assert.ElementsMatch(t, []interface{}{"ENGINE", "VULN_LIBRARY"}, params["modules"])

		w.WriteHeader(http.StatusOK)
		w.Write([]byte(`{"count": 2, "result": [{"id": 1, "state": "SUCCEEDED", "progress": 100}, {"id": 2, "state": "RUNNING", "progress": 50}]}`))
	}))
	defer server.Close()

	// 模拟XRay客户端
	cli := &x_ray.XRay{}
	cli.SetConfig(map[string]interface{}{
		"protocol": "http",
		"ip":       server.Listener.Addr().String(),
	})
	getToken := gomonkey.ApplyMethodReturn(cli, "GetToken", "mock-token")
	defer getToken.Reset()

	response, err := GetExecutionList(cli, 10, 0, []string{"ENGINE", "VULN_LIBRARY"})

	assert.NoError(t, err)
	assert.NotNil(t, response)
	assert.Equal(t, 2, response.Count)
	assert.Len(t, response.Result, 2)
	assert.Equal(t, 1, response.Result[0].ID)
	assert.Equal(t, "SUCCEEDED", response.Result[0].State)
	assert.Equal(t, 100, response.Result[0].Progress)
	assert.Equal(t, 2, response.Result[1].ID)
	assert.Equal(t, "RUNNING", response.Result[1].State)
	assert.Equal(t, 50, response.Result[1].Progress)
}
