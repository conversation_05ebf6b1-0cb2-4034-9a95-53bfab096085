package cascade_upgrade_service

import (
	"bytes"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"os"
	"path/filepath"

	"fobrain/fobrain/app/services/node/d01"
	"fobrain/fobrain/logs"
	"fobrain/models/mysql/data_source"

	"github.com/spf13/cast"
)

const (
	UriTemplateForD01Upgrade = "%s://%s/v3/systems/upgrade_upload"
)

func getD01Client(nodeId uint64) (*d01.D01, error) {
	cli := d01.NewD01()
	err := cli.SetNode(nodeId)
	if err != nil {
		return nil, err
	}

	config, err := data_source.NewNodeConfigModel().GetNodeConfig(nodeId)
	if err != nil {
		return nil, err
	}

	err = cli.SetConfig(config)
	if err != nil {
		return nil, err
	}

	return cli, nil
}

func CheckAndUpgradeD01(cli *d01.D01, filePath, fileName string) error {
	logger := logs.GetLogger()
	payload := &bytes.Buffer{}
	// 创建一个缓冲区和multipart写入器
	writer := multipart.NewWriter(payload)
	// 添加其他表单字段
	err := writer.WriteField("file_name", fileName)
	if err != nil {
		return fmt.Errorf("failed to write form field: %v", err)
	}

	// 打开文件
	file, err := os.Open(filePath)
	if err != nil {
		return fmt.Errorf("failed to open file: %v", err)
	}
	defer file.Close()
	part, err := writer.CreateFormFile("file", filepath.Base(filePath))
	if err != nil {
		return fmt.Errorf("failed to create form file: %v", err)
	}
	// 将文件内容复制到表单字段中
	_, err = io.Copy(part, file)
	if err != nil {
		return fmt.Errorf("failed to copy file content: %v", err)
	}
	// 关闭写入器以结束表单内容
	err = writer.Close()
	if err != nil {
		return fmt.Errorf("failed to close writer: %v", err)
	}

	// 拼接请求地址
	apiUri := fmt.Sprintf(UriTemplateForD01Upgrade, cast.ToString(cli.GetConfig("protocol")), cast.ToString(cli.GetConfig("ip")))

	// 创建 HTTP 请求
	req, err := http.NewRequest("POST", apiUri, payload)
	if err != nil {
		return fmt.Errorf("创建D01升级请求失败: %v", err)
	}

	// 设置请求头信息
	req.Header.Add("Content-Type", writer.FormDataContentType())
	req.Header.Add("Api-Token", cli.GetApiKey())

	// 跳过 TLS 证书验证
	tr := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	client := &http.Client{Transport: tr}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("执行D01升级请求失败: %v", err)
	}
	defer resp.Body.Close()
	logger.Infof("D01升级响应: %v", resp)

	// 如果升级操作失败,返回错误
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("上传文件失败")
	}

	return nil
}

func GetD01UpgradeProgress(cli *d01.D01) (string, error) {
	logger := logs.GetLogger()
	apiUri := fmt.Sprintf(UriTemplateForD01Upgrade, cast.ToString(cli.GetConfig("protocol")), cast.ToString(cli.GetConfig("ip")))
	// 创建 HTTP 请求
	req, err := http.NewRequest("GET", apiUri, nil)
	if err != nil {
		return "", fmt.Errorf("创建D01升级请求失败: %v", err)
	}
	// 设置请求头信息
	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Api-Token", cli.GetApiKey())

	// 跳过 TLS 证书验证
	tr := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	client := &http.Client{Transport: tr}
	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("执行D01升级进度查询请求失败: %v", err)
	}
	defer resp.Body.Close()
	logger.Infof("D01升级进度查询响应: %v", resp)

	// 定义响应结构
	type Response struct {
		Code    int    `json:"code"`
		Message string `json:"message"`
		Data    struct {
			Data struct {
				Progress string `json:"progress"`
				Status   string `json:"state"`
				Message  string `json:"message"`
				Success  bool   `json:"success"`
			} `json:"data"`
		} `json:"data"`
	}

	// 发送请求并解析响应
	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("读取响应体失败: %v", err)
	}
	defer resp.Body.Close()
	// 解析JSON响应
	var response Response
	if err := json.Unmarshal(body, &response); err != nil {
		return "", fmt.Errorf("解析JSON响应失败: %v", err)
	}
	logger.Infof("D01升级进度查询响应: %s", string(body))

	if response.Code != 200 {
		return "", fmt.Errorf("查询D01升级进度失败: %s", response.Message)
	} else {
		if response.Data.Data.Status == "1" {
			return "升级中", nil
		} else if response.Data.Data.Status == "2" {
			return "升级成功", nil
		} else if response.Data.Data.Status == "3" {
			return "升级失败", nil
		} else {
			return "未知", nil
		}
	}
}
