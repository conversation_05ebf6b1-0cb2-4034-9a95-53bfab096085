package cascade_upgrade_service

import (
	"bytes"
	"io"
	"net/http"
	"net/http/httptest"
	"os"
	"path/filepath"
	"reflect"
	"testing"
	"time"

	"fobrain/fobrain/app/services/node/d01"
	"fobrain/models/mysql/data_source"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/pkg/errors"
	"github.com/stretchr/testify/assert"
)

func TestGetD01Client(t *testing.T) {
	nodeId := uint64(123)

	d01Instance := &d01.D01{}
	patches := gomonkey.ApplyFunc(d01.NewD01, func() *d01.D01 {
		return d01Instance
	})

	// 设置模拟行为
	patches.ApplyMethod(reflect.TypeOf(d01Instance), "SetNode", func(_ *d01.D01, _ uint64) error {
		return nil
	})
	patches.ApplyMethod(reflect.TypeOf(d01Instance), "SetConfig", func(_ *d01.D01, _ map[string]interface{}) error {
		return nil
	})

	// 模拟 GetNodeConfig 方法
	patches.ApplyMethod(reflect.TypeOf(&data_source.DataNodeConfig{}), "GetNodeConfig", func(_ *data_source.DataNodeConfig, _ uint64) (map[string]interface{}, error) {
		return map[string]interface{}{"protocol": "http", "ip": "127.0.0.1"}, nil
	})

	time.Sleep(time.Second)
	// 调用被测试函数
	client, err := getD01Client(nodeId)
	patches.Reset()
	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, client)
}

func TestGetD01Client_SetNodeError(t *testing.T) {
	nodeId := uint64(123)
	d01Instance := &d01.D01{}
	patches := gomonkey.ApplyFunc(d01.NewD01, func() *d01.D01 {
		return d01Instance
	})

	// 设置模拟行为
	patches.ApplyMethod(reflect.TypeOf(d01Instance), "SetNode", func(_ *d01.D01, _ uint64) error {
		return errors.New("set node error")
	})
	time.Sleep(time.Second)
	client, err := getD01Client(nodeId)
	patches.Reset()
	assert.Error(t, err)
	assert.Nil(t, client)
}

func TestGetD01Client_GetNodeConfigError(t *testing.T) {
	d01Instance := &d01.D01{}
	patches := gomonkey.ApplyFunc(d01.NewD01, func() *d01.D01 {
		return d01Instance
	})

	// 模拟 GetNodeConfig 方法返回错误
	patches.ApplyMethod(reflect.TypeOf(&data_source.DataNodeConfig{}), "GetNodeConfig", func(_ *data_source.DataNodeConfig, _ uint64) (map[string]interface{}, error) {
		return nil, errors.New("get config error")
	})

	nodeId := uint64(123)
	time.Sleep(time.Second)
	client, err := getD01Client(nodeId)
	patches.Reset()
	assert.Error(t, err)
	assert.Nil(t, client)
}

func TestCheckAndUpgradeD01(t *testing.T) {
	// 创建一个模拟的 HTTP 服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		assert.Equal(t, "multipart/form-data", r.Header.Get("Content-Type")[:19])

		// 解析multipart表单
		err := r.ParseMultipartForm(10 << 20) // 10 MB
		assert.NoError(t, err)

		// 验证文件字段
		file, _, err := r.FormFile("file")
		assert.NoError(t, err)
		defer file.Close()

		buf := new(bytes.Buffer)
		_, err = io.Copy(buf, file)
		assert.NoError(t, err)
		assert.Equal(t, "test content", buf.String())

		w.WriteHeader(http.StatusOK)
	}))
	defer server.Close()

	// 创建一个临时文件
	tmpFile, err := os.CreateTemp("", "testfile")
	assert.NoError(t, err)
	defer os.Remove(tmpFile.Name())

	_, err = tmpFile.WriteString("test content")
	assert.NoError(t, err)
	tmpFile.Close()

	// 创建一个 D01 实例
	cli := &d01.D01{}
	cli.SetConfig(map[string]interface{}{
		"protocol": "http",
		"ip":       server.Listener.Addr().String(),
	})

	err = CheckAndUpgradeD01(cli, tmpFile.Name(), filepath.Base(tmpFile.Name()))

	assert.NoError(t, err)
}

func TestGetD01UpgradeProgress(t *testing.T) {
	// 创建一个模拟的 HTTP 服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		assert.Equal(t, "application/json", r.Header.Get("Content-Type"))
		// 构造模拟的响应

		w.WriteHeader(http.StatusOK)
		w.Write([]byte(`{"code":200,"message":"操作成功","data":{"data":{"success":true,"state":"2","progress":"100","message":"","need_update_history_data":false}}}`))
	}))
	defer server.Close()

	// 创建一个 D01 实例
	cli := &d01.D01{}
	cli.SetConfig(map[string]interface{}{
		"protocol": "http",
		"ip":       server.Listener.Addr().String(),
	})
	status, err := GetD01UpgradeProgress(cli)
	assert.NoError(t, err)
	assert.Equal(t, "升级成功", status)
}
