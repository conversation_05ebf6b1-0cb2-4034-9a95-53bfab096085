package intranet_ips

import (
	"context"
	"fobrain/initialize/redis"
	"fobrain/models/mysql/system_configs"
	"fobrain/pkg/utils"
	"time"
)

var (
	key = "other_config_intranet_ips"
)

func IntranetIPList() (list []string, err error) {
	data, err := redis.GetRedisClient().Get(context.Background(), key).Result()
	if err == nil && len(data) > 0 {
		// 如果数据存在于 Redis 中，则解析为ip数组切片并返回
		return utils.ParseIPString(data)
	}
	// 如果数据不存在于 Redis 中，则从数据库中获取数据
	data, err = system_configs.NewSystemConfigs().GetConfig(key)
	if err != nil {
		return nil, err
	}

	err = redis.GetRedisClient().Set(context.Background(), key, data, 0).Err()
	if err != nil {
		return nil, err
	}

	return utils.ParseIPString(data)
}

func IntranetIPListUpdate(list []string) error {
	value, err := utils.IPListToString(list)
	if err != nil {
		return err
	}
	// 更新数据库中的数据
	err = system_configs.NewSystemConfigs().UpdateConfig(key, value)
	if err != nil {
		return err
	}

	// 将数据转换为字节并存储到 Redis 中
	err = redis.GetRedisClient().Set(context.Background(), key, value, time.Minute).Err()
	if err != nil {
		return err
	}

	return nil
}
