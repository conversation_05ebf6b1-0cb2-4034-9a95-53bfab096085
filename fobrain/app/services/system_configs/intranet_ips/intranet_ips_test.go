package intranet_ips

import (
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/alicebob/miniredis/v2"
	redis2 "github.com/go-redis/redis/v8"
	"github.com/stretchr/testify/assert"

	testcommon "fobrain/fobrain/tests/common_test"
)

func TestIntranetIPList(t *testing.T) {
	s := miniredis.RunT(t)
	defer s.Close()

	client := redis2.NewClient(&redis2.Options{Addr: s.Addr()})
	testcommon.SetRedisClient(client)

	list, err := IntranetIPList()
	t.Log(list, err)
}

func TestIntranetIPListUpdate(t *testing.T) {
	list := []string{"***********", "***********"}
	mockDb := testcommon.InitSqlMock()
	defer mockDb.Close()
	mockDb.ExpectQuery("SELECT * FROM `system_configs` WHERE `key` = ? ORDER BY `system_configs`.`id` LIMIT 1").
		WithArgs("other_config_intranet_ips").
		WillReturnRows(sqlmock.NewRows([]string{"id", "key", "value"}).AddRow(1, "other_config_intranet_ips", "IP_ADDRESS"))

	mockDb.ExpectBegin()
	mockDb.ExpectExec("UPDATE `system_configs`").
		WillReturnResult(sqlmock.NewResult(1, 1))
	mockDb.ExpectCommit()

	s := miniredis.RunT(t)
	defer s.Close()

	client := redis2.NewClient(&redis2.Options{Addr: s.Addr()})
	testcommon.SetRedisClient(client)

	err := IntranetIPListUpdate(list)
	assert.NoError(t, err)
}
