package upgrade

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"fobrain/fobrain/common/localtime"
	"fobrain/models/mysql/cascade_upgrade"
	"fobrain/models/mysql/system_configs"
	"fobrain/models/mysql/system_logs"
	"fobrain/pkg/utils"
	"os"
	"os/exec"
	"path/filepath"
	"runtime"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/pkg/errors"
	"go-micro.dev/v4/logger"
)

const (
	uploadFinishFile         = "upload_finish"
	upgradeProcessFile       = "upgrade_process"
	defaultProcess     int64 = 2
)

var (
	VersionFilePath    = "/etc/fobrain/conf/version"
	UpgradeStoragePath = "/data/fobrain/storage/upgrade/"
)

type VersionInfo struct {
	ProductModel        string   `json:"product_model"`
	ReleaseVersion      string   `json:"release_version"`
	ReleaseDate         string   `json:"release_date"`
	ReleaseDesc         string   `json:"release_desc"`
	VersionDependencies []string `json:"version_dependencies"`
}

func init() {
	if runtime.GOOS == "darwin" {
		VersionFilePath = "./version"
		UpgradeStoragePath = "./storage/upgrade/"
	}
}

func CheckArchitecture(filename string) bool {
	// 获取当前cpu架构
	arch := runtime.GOARCH
	logger.Infof("当前 CPU 架构: %s\n", arch)
	if strings.Contains(strings.ToLower(arch), "arm") && !strings.Contains(strings.ToLower(filename), "arm") {
		return false
	}
	if !strings.Contains(strings.ToLower(arch), "arm") && strings.Contains(strings.ToLower(filename), "arm") {
		return false
	}
	return true
}

func UpgradeVersionVerify(storagePath, fileName, fileExt string) (*VersionInfoResult, *VersionInfoResult, error) {
	if storagePath == "" || fileName == "" {
		return nil, nil, fmt.Errorf("无效的存储路径或文件名: storagePath=%s, fileName=%s", storagePath, fileName)
	}
	if !CheckArchitecture(fileName) {
		return nil, nil, fmt.Errorf("升级包不适用当前架构:%s", runtime.GOARCH)
	}
	// 执行 tar 命令
	tarCmd := exec.Command("bash", "-c", fmt.Sprintf("cd %s && tar -xvzf %s", storagePath, fileName))
	// 运行命令并捕获输出
	output, err := tarCmd.CombinedOutput()
	if err != nil {
		logger.Errorf("Error executing tar command: %v, Command output: %s\n", err, output)
		return nil, nil, errors.WithMessagef(err, "解压升级包失败,file:%s", fileName)
	}

	// 校验必升版本
	nowVersion, err := GetVersionInfo(VersionFilePath, true)
	if err != nil {
		return nil, nil, err
	}
	latestVersion, err := GetVersionInfo(filepath.Join(storagePath, "version"), true)
	if err != nil {
		return nil, nil, err
	}
	isLower, err := isVersionLower(nowVersion.ReleaseVersion, latestVersion.ReleaseVersion)
	if err != nil {
		return nil, nil, fmt.Errorf("判断升级包版本失败,nowVersion:%s, latest:%s, err:%v", nowVersion.ReleaseVersion, latestVersion.ReleaseVersion, err)
	}
	if !isLower {
		return nowVersion, latestVersion, utils.UpgradeNoNeed
	}

	for _, v := range latestVersion.VersionDependencies {
		isLower, err := isVersionLower(nowVersion.ReleaseVersion, v)
		if err != nil {
			return nowVersion, latestVersion, fmt.Errorf("判断必升版本失败,nowVersion:%s, dependencies:%s, err:%v", nowVersion.ReleaseVersion, v, err)
		}
		if isLower {
			return nowVersion, latestVersion, &VersionTooLowError{
				CurrentVersion:  nowVersion.ReleaseVersion,
				RequiredVersion: v,
			}
		}
	}
	return nowVersion, latestVersion, nil
}

func SystemUpgradeExec(storagePath, fileName, fileExt, cascadeListId string) error {
	nowVersion, latestVersion, err := UpgradeVersionVerify(storagePath, fileName, fileExt)
	if err != nil {
		return err
	}
	processFile := filepath.Join(UpgradeStoragePath, upgradeProcessFile)
	os.Remove(processFile)
	pFile, err := os.OpenFile(processFile, os.O_CREATE|os.O_APPEND|os.O_RDWR, 0666)
	if err == nil {
		data, _ := json.Marshal(UpgradeProcessInfo{
			Version: latestVersion.ReleaseVersion,
			Process: defaultProcess,
			Finish:  false,
			Failed:  false,
			Msg:     "",

			CreateTime:    time.Now().Format(utils.DateTimeLayout),
			CascadeListId: cascadeListId,
		})
		pFile.WriteString(string(data))
	}

	time.Sleep(time.Millisecond * 200)
	finishPath := filepath.Join(UpgradeStoragePath, "flag-dir")
	if !utils.IsDirExist(finishPath) {
		if mkdirErr := utils.Mkdir(finishPath); mkdirErr != nil {
			return errors.WithMessage(err, "创建finish文件的目录失败")
		}
	}
	finishFile := filepath.Join(finishPath, uploadFinishFile)
	os.Remove(finishFile)
	file, err := os.Create(finishFile)
	if err != nil {
		logger.Error("SystemUpgradeExec 无法创建文件uploadFinishFile", err)
		return utils.UpgradeFailed
	}
	defer file.Close()
	system_configs.NewSystemUpgradeLog().CreateUpgradeLog(&system_configs.SystemUpgradeLog{
		FromVersion:  nowVersion.ReleaseVersion,
		ToVersion:    latestVersion.ReleaseVersion,
		Status:       system_configs.SystemUpgradeLogStatusUpgrading,
		ReleaseDesc:  latestVersion.ReleaseDesc,
		ErrorMessage: "",
	})
	logger.Info("上传及校验通过开始执行升级操作", storagePath, fileName)
	return nil
}

type UpgradeProcessInfo struct {
	Version string `json:"version"`
	Process int64  `json:"process"`
	Finish  bool   `json:"finish"`
	Failed  bool   `json:"failed"`
	Msg     string `json:"msg"`

	CreateTime    string `json:"create_time"`
	CascadeListId string `json:"cascade_list_id,omitempty"`
}

func (info *UpgradeProcessInfo) IsSuccessful() bool {
	if info == nil {
		return false
	}
	return info.Finish && !info.Failed
}
func (info *UpgradeProcessInfo) IsTimeout() bool {
	if info == nil || info.CreateTime == "" {
		return false
	}
	createTime := localtime.Parse(utils.DateTimeLayout, info.CreateTime)
	return localtime.NewLocalTime(time.Now()).Unix()-createTime.Unix() > 20*60
}

func (info *UpgradeProcessInfo) GetProcess() int64 {
	if info == nil {
		return 0
	}

	if info.Failed {
		return 0
	}

	if info.Finish || info.Process > 100 {
		return 100
	}

	if info.Process < 0 {
		return 0
	}

	return info.Process
}

func GetUpgradeProcess() (*UpgradeProcessInfo, error) {
	file := filepath.Join(UpgradeStoragePath, upgradeProcessFile)
	data, err := os.ReadFile(file)
	if err != nil {
		return nil, errors.WithMessagef(err, "打开upgradeProcessFile文件失败,file:%s", file)
	}

	var info UpgradeProcessInfo
	err = json.Unmarshal(data, &info)
	if err != nil {
		return nil, errors.WithMessagef(err, "升级进度文件内容unmarshal失败,file:%s", file)
	}

	// 添加调试日志
	logger.Infof("GetUpgradeProcess: info.Finish=%v, info.Failed=%v, info.IsSuccessful()=%v",
		info.Finish, info.Failed, info.IsSuccessful())

	if info.IsSuccessful() {
		// 检查数据库中是否已经有该版本的成功记录，防止重复记录
		existingLog, err := system_configs.NewSystemUpgradeLog().GetLatestUpgradeLog()
		hasSuccessRecord := false
		if err == nil && existingLog != nil && existingLog.ToVersion == info.Version && existingLog.Status == system_configs.SystemUpgradeLogStatusSuccess {
			hasSuccessRecord = true
		}

		if !hasSuccessRecord {
			logger.Infof("执行升级成功的系统日志记录: version=%s, cascadeListId=%s", info.Version, info.CascadeListId)

			updateMap := make(map[string]interface{})
			updateMap["upgrade_status"] = cascade_upgrade.Upgraded
			updateMap["upgrade_time"] = time.Now().Format(utils.DateTimeLayout)
			cascade_upgrade.NewCascadeUpgradeRecordModel().UpdateColumns(info.CascadeListId, updateMap)

			system_configs.NewSystemUpgradeLog().UpdateUpgradeStatus(info.Version, system_configs.SystemUpgradeLogStatusSuccess, "")
			system_logs.NewSystemLogModel().Create("系统升级", info.Version+"升级成功")

			logger.Infof("升级成功系统日志记录完成: version=%s", info.Version)
		} else {
			logger.Infof("升级成功但该版本已有成功记录，跳过重复日志: version=%s", info.Version)
		}
	}

	if !info.Finish && !info.Failed && info.IsTimeout() {
		info.Process = 100
		info.Failed = true
		info.Msg = "升级超时"
	}
	if info.Failed {
		// 检查是否已经记录过失败日志，防止重复记录
		existingLog, err := system_configs.NewSystemUpgradeLog().GetLatestUpgradeLog()
		hasFailedRecord := false
		if err == nil && existingLog != nil && existingLog.ToVersion == info.Version && existingLog.Status == system_configs.SystemUpgradeLogStatusFailed {
			hasFailedRecord = true
		}

		if !hasFailedRecord {
			updateMap := make(map[string]interface{})
			updateMap["upgrade_status"] = cascade_upgrade.UpgradeFailed
			updateMap["upgrade_err_msg"] = info.Msg
			cascade_upgrade.NewCascadeUpgradeRecordModel().UpdateColumns(info.CascadeListId, updateMap)

			system_configs.NewSystemUpgradeLog().UpdateUpgradeStatus(info.Version, system_configs.SystemUpgradeLogStatusFailed, info.Msg)
			system_logs.NewSystemLogModel().Create("系统升级", info.Version+"升级失败")

			logger.Errorf("系统升级失败: 版本%s升级失败, 错误信息: %s, 级联任务ID: %s", info.Version, info.Msg, info.CascadeListId)
		} else {
			logger.Infof("升级失败但该版本已有失败记录，跳过重复日志: version=%s", info.Version)
		}
	}
	return &info, nil
}

type VersionInfoResult struct {
	VersionInfo
	UpgradeLog []*system_configs.UpgradeLogItem `json:"upgrade_log"`
	IsLocked   bool                             `json:"is_locked"`
}

func GetVersionInfo(filePath string, isUpload bool) (*VersionInfoResult, error) {
	encryptedData, err := os.ReadFile(filePath)
	if err != nil {
		return nil, errors.WithMessagef(err, "打开version文件失败,file:%s", filePath)
	}
	jsonData, err := base64.StdEncoding.DecodeString(string(encryptedData))
	if err != nil {
		return nil, errors.WithMessagef(err, "version文件内容解密失败,file:%s", filePath)
	}
	var version VersionInfo
	err = json.Unmarshal(jsonData, &version)
	if err != nil {
		return nil, errors.WithMessagef(err, "version文件内容unmarshal失败,file:%s", filePath)
	}
	if isUpload {
		return &VersionInfoResult{version, []*system_configs.UpgradeLogItem{}, false}, nil
	}
	records, err := system_configs.NewSystemUpgradeLog().GetUpgradeLogs()
	if err != nil {
		return &VersionInfoResult{version, []*system_configs.UpgradeLogItem{}, false}, nil
	}
	sort.Slice(records, func(i, j int) bool { return records[i].Id > records[j].Id })

	return &VersionInfoResult{version, system_configs.ConvertToUpgradeLogItems(records), false}, nil
}

func GenVersionFile(filePath string, info *VersionInfo) error {
	jsonData, err := json.Marshal(info)
	if err != nil {
		return err
	}
	encryptedData := base64.StdEncoding.EncodeToString(jsonData)
	return os.WriteFile(filePath, []byte(encryptedData), 0666)
}

type parseVersionResult struct {
	Major int
	Minor int
	Patch int
}

// 将版本号字符串转为三个整数字段
func parseVersion(version string) (parseVersionResult, error) {
	parts := strings.Split(version, ".")
	if len(parts) != 3 {
		return parseVersionResult{}, fmt.Errorf("无效的版本号格式: %s", version)
	}

	major, err := strconv.Atoi(parts[0])
	if err != nil {
		return parseVersionResult{}, fmt.Errorf("无效的主版本号: %s", parts[0])
	}

	minor, err := strconv.Atoi(parts[1])
	if err != nil {
		return parseVersionResult{}, fmt.Errorf("无效的次版本号: %s", parts[1])
	}

	patch, err := strconv.Atoi(parts[2])
	if err != nil {
		return parseVersionResult{}, fmt.Errorf("无效的补丁版本号: %s", parts[2])
	}

	return parseVersionResult{
		Major: major,
		Minor: minor,
		Patch: patch,
	}, nil
}

// 比较版本号，判断是否低于依赖版本号
func isVersionLower(currentVersion, latestVersion string) (bool, error) {
	current, err := parseVersion(currentVersion)
	if err != nil {
		return false, err
	}

	dependency, err := parseVersion(latestVersion)
	if err != nil {
		return false, err
	}

	// 按顺序比较主版本号、次版本号、补丁版本号
	if current.Major < dependency.Major {
		return true, nil
	}
	if current.Major == dependency.Major && current.Minor < dependency.Minor {
		return true, nil
	}
	if current.Major == dependency.Major && current.Minor == dependency.Minor && current.Patch < dependency.Patch {
		return true, nil
	}

	return false, nil
}

// 首先，定义一个新的错误类型
type VersionTooLowError struct {
	CurrentVersion  string
	RequiredVersion string
}

func (e *VersionTooLowError) Error() string {
	return fmt.Sprintf("当前版本过低,需要先升级前置版本%s", e.RequiredVersion)
}
