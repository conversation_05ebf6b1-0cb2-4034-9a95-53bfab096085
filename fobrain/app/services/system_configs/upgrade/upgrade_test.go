package upgrade

import (
	"encoding/json"
	"fmt"
	"fobrain/fobrain/common/localtime"
	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/pkg/fortest"
	"fobrain/pkg/utils"
	"os"
	"os/exec"
	"path/filepath"
	"reflect"
	"runtime"
	"strings"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/agiledragon/gomonkey/v2"
)

func TestSystemUpgradeExec(t *testing.T) {
	time.Sleep(time.Second)
	patches := gomonkey.ApplyGlobalVar(&VersionFilePath, "./version")
	patches.ApplyGlobalVar(&UpgradeStoragePath, "./")
	patches.ApplyFunc(CheckArchitecture, func(string) bool {
		return true
	})

	info := &VersionInfo{
		ProductModel:   "fobrain-00001",
		ReleaseVersion: "2.0.0",
		ReleaseDate:    "2024-09-06",
		ReleaseDesc:    "全新升级Fobrain带给您丝滑体验",
		VersionDependencies: []string{
			"1.0.0",
			"1.9.0",
		},
	}
	lowerInfo := &VersionInfo{
		ProductModel:        "fobrain-00000",
		ReleaseVersion:      "1.9.0",
		ReleaseDate:         "2024-08-06",
		ReleaseDesc:         "全新升级Fobrain带给您丝滑体验",
		VersionDependencies: []string{},
	}

	time.Sleep(time.Second)
	err := GenVersionFile("./version", info)
	if err != nil {
		t.Fatal(err)
	}
	// 创建文件
	exampleFileName := "example.txt"
	file, err := os.Create(exampleFileName)
	if err != nil {
		t.Fatal("Error creating file:", err)
	}
	defer file.Close()

	// 写入内容到文件
	_, err = file.WriteString("Hello, World!\n")
	if err != nil {
		t.Fatal("Error writing to file:", err)
	}
	// 要创建的 .tar.gz 文件名
	tarGzFileName := "example.tar.gz"
	// 准备 tar 命令参数
	os.Mkdir("./tmp", 0755)
	defer os.RemoveAll("./tmp")
	execArgs := []string{"-cvzf", fmt.Sprintf("./tmp/%s", tarGzFileName), exampleFileName, "version"}
	//  执行 tar 命令
	cmd := exec.Command("tar", execArgs...)
	// 运行命令并捕获输出
	output, err := cmd.CombinedOutput()
	if err != nil {
		t.Fatalf("Error executing tar command: %v, Command output: %s\n", err, output)
	}
	os.Remove(exampleFileName)
	os.Remove("./version")

	GenVersionFile(VersionFilePath, lowerInfo)
	defer os.Remove(VersionFilePath)
	defer os.RemoveAll(time.Now().Format(utils.DateMinuteLayout))

	type args struct {
		filePath string
		fileName string
		fileExt  string
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "test1",
			args: args{
				filePath: "./tmp/",
				fileName: tarGzFileName,
				fileExt:  ".gz",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := SystemUpgradeExec(tt.args.filePath, tt.args.fileName, tt.args.fileExt, "")
			if err != nil {
				t.Fatal("失败", err)
			}
			_, err = os.Stat(fmt.Sprintf("./tmp/%s", exampleFileName))
			if os.IsNotExist(err) {
				t.Errorf("file %s not exist\n", exampleFileName)
			} else {
				t.Logf("file %s exist\n", exampleFileName)
			}
		})
	}

	patches.Reset()
	os.Remove(exampleFileName)
	os.Remove(upgradeProcessFile)
}

func TestGenVersionFile(t *testing.T) {
	fortest.Init()
	dir, err := os.Getwd()
	if err != nil {
		t.Fatal(err)
	}
	t.Log("dir:", dir)
	tmpVersion := filepath.Join(dir, "version")
	t.Log("tmpVersion:", tmpVersion)
	type args struct {
		filePath string
		info     *VersionInfo
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "test",
			args: args{
				filePath: tmpVersion,
				info: &VersionInfo{
					ProductModel:   "fobrain-2.1.3",
					ReleaseVersion: "2.1.3",
					ReleaseDate:    "2024-11-15",
					ReleaseDesc:    "全新升级Fobrain带给您丝滑体验",
					VersionDependencies: []string{
						"2.0.1",
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := GenVersionFile(tt.args.filePath, tt.args.info); (err != nil) != tt.wantErr {
				t.Errorf("GenVersionFile() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
	os.Remove(VersionFilePath)
}

func TestGetVersionInfo(t *testing.T) {
	fortest.Init()
	dir, err := os.Getwd()
	if err != nil {
		t.Fatal(err)
	}
	mockDb := testcommon.InitSqlMock()
	defer mockDb.Close()
	mockDb.ExpectQuery("SELECT * FROM `system_upgrade_log`").
		WillReturnRows(
			sqlmock.NewRows(
				[]string{"id", "from_version", "to_version", "status", "release_desc", "error_message", "created_at", "updated_at"}).
				AddRow(1, "1.0.0", "2.0.0", "success", "desc", "err_msg", localtime.NewLocalTime(time.Now()), localtime.NewLocalTime(time.Now())),
		)
	t.Log("dir:", dir)
	info := &VersionInfo{
		ProductModel:   "fobrain-00001",
		ReleaseVersion: "2.0.0",
		ReleaseDate:    "2024-09-06",
		ReleaseDesc:    "全新升级Fobrain带给您丝滑体验",
		VersionDependencies: []string{
			"1.0.0",
			"1.9.0",
		},
	}
	tmpVersion := filepath.Join(dir, "version")
	t.Log("tmpVersion:", tmpVersion)
	GenVersionFile(tmpVersion, info)

	tests := []struct {
		name    string
		want    *VersionInfo
		wantErr bool
	}{
		{
			name:    "test",
			want:    info,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := GetVersionInfo(tmpVersion, false)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetVersionInfo() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got.ReleaseVersion != tt.want.ReleaseVersion {
				t.Errorf("GetVersionInfo() = %v, want %v", got, tt.want)
			}

			t.Log(got)
		})
	}
	os.Remove(tmpVersion)
}

func Test_isVersionLower(t *testing.T) {
	fortest.Init()
	type args struct {
		currentVersion    string
		dependencyVersion string
	}
	tests := []struct {
		name    string
		args    args
		want    bool
		wantErr bool
	}{
		{
			name: "test true",
			args: args{
				currentVersion:    "1.4.7",
				dependencyVersion: "2.0.1",
			},
			want:    true,
			wantErr: false,
		},
		{
			name: "test false",
			args: args{
				currentVersion:    "2.4.7",
				dependencyVersion: "2.0.1",
			},
			want:    false,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := isVersionLower(tt.args.currentVersion, tt.args.dependencyVersion)
			if (err != nil) != tt.wantErr {
				t.Errorf("isVersionLower() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("isVersionLower() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_parseVersion(t *testing.T) {
	fortest.Init()
	type args struct {
		version string
	}
	tests := []struct {
		name    string
		args    args
		want    parseVersionResult
		wantErr bool
	}{
		{
			name: "test",
			args: args{
				version: "1.2.3",
			},
			want: parseVersionResult{
				Major: 1,
				Minor: 2,
				Patch: 3,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := parseVersion(tt.args.version)
			if (err != nil) != tt.wantErr {
				t.Errorf("parseVersion() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("parseVersion() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestGenProcessInfo(t *testing.T) {
	fortest.Init()
	data, err := json.Marshal(UpgradeProcessInfo{
		Version: "2.0.0",
		Process: 10,
		Finish:  false,
		Failed:  false,
		Msg:     "",
	})
	if err != nil {
		t.Fatal(err)
	}
	t.Log(string(data))
}

func TestGetUpgradeProcess(t *testing.T) {
	patches := gomonkey.ApplyGlobalVar(&UpgradeStoragePath, "./")
	defer patches.Reset()
	processFile := filepath.Join(UpgradeStoragePath, upgradeProcessFile)
	os.Remove(processFile)
	pFile, err := os.OpenFile(processFile, os.O_CREATE|os.O_APPEND|os.O_RDWR, 0666)
	if err == nil {
		data, _ := json.Marshal(UpgradeProcessInfo{
			Version:       "3.2.1",
			Process:       defaultProcess,
			Finish:        false,
			Failed:        false,
			Msg:           "",
			CascadeListId: "123",
		})
		pFile.WriteString(string(data))
	}
	tests := []struct {
		name    string
		wantErr bool
	}{
		{
			name:    "test",
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := GetUpgradeProcess()
			if (err != nil) != tt.wantErr {
				t.Errorf("GetUpgradeProcess() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			t.Log(got)
		})
	}
	os.Remove(processFile)
}

func TestUpgradeProcessInfo_IsSuccessful(t *testing.T) {
	type fields struct {
		Version string
		Process int64
		Finish  bool
		Failed  bool
		Msg     string
	}
	tests := []struct {
		name   string
		fields fields
		want   bool
	}{
		{
			name: "成功完成升级",
			fields: fields{
				Version: "1.0.0",
				Process: 100,
				Finish:  true,
				Failed:  false,
				Msg:     "升级成功",
			},
			want: true,
		},
		{
			name: "升级失败",
			fields: fields{
				Version: "1.0.0",
				Process: 50,
				Finish:  true,
				Failed:  true,
				Msg:     "升级失败",
			},
			want: false,
		},
		{
			name: "升级进行中",
			fields: fields{
				Version: "1.0.0",
				Process: 50,
				Finish:  false,
				Failed:  false,
				Msg:     "升级中",
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			info := &UpgradeProcessInfo{
				Version: tt.fields.Version,
				Process: tt.fields.Process,
				Finish:  tt.fields.Finish,
				Failed:  tt.fields.Failed,
				Msg:     tt.fields.Msg,
			}
			if got := info.IsSuccessful(); got != tt.want {
				t.Errorf("UpgradeProcessInfo.IsSuccessful() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestUpgradeProcessInfo_GetProcess(t *testing.T) {
	type fields struct {
		Version string
		Process int64
		Finish  bool
		Failed  bool
		Msg     string
	}
	tests := []struct {
		name   string
		fields fields
		want   int64
	}{
		{
			name: "正常进度",
			fields: fields{
				Version: "1.0.0",
				Process: 50,
				Finish:  false,
				Failed:  false,
				Msg:     "升级中",
			},
			want: 50,
		},
		{
			name: "完成状态",
			fields: fields{
				Version: "1.0.0",
				Process: 80,
				Finish:  true,
				Failed:  false,
				Msg:     "升级完成",
			},
			want: 100,
		},
		{
			name: "失败状态",
			fields: fields{
				Version: "1.0.0",
				Process: 30,
				Finish:  true,
				Failed:  true,
				Msg:     "升级失败",
			},
			want: 0,
		},
		{
			name: "进度超过100",
			fields: fields{
				Version: "1.0.0",
				Process: 120,
				Finish:  false,
				Failed:  false,
				Msg:     "升级中",
			},
			want: 100,
		},
		{
			name: "进度为负数",
			fields: fields{
				Version: "1.0.0",
				Process: -10,
				Finish:  false,
				Failed:  false,
				Msg:     "升级中",
			},
			want: 0,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			info := &UpgradeProcessInfo{
				Version: tt.fields.Version,
				Process: tt.fields.Process,
				Finish:  tt.fields.Finish,
				Failed:  tt.fields.Failed,
				Msg:     tt.fields.Msg,
			}
			if got := info.GetProcess(); got != tt.want {
				t.Errorf("UpgradeProcessInfo.GetProcess() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestIsTimeout(t *testing.T) {
	tests := []struct {
		name     string
		info     *UpgradeProcessInfo
		expected bool
	}{
		{
			name: "未超时",
			info: &UpgradeProcessInfo{
				CreateTime: localtime.NewLocalTime(time.Now().Add(-10 * time.Minute)).String(),
			},
			expected: false,
		},

		{
			name: "无创建时间",
			info: &UpgradeProcessInfo{
				CreateTime: "",
			},
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.info.IsTimeout()
			if result != tt.expected {
				t.Errorf("期望 %v, 但得到 %v", tt.expected, result)
			}
		})
	}
}

func TestUpgradeVersionVerify(t *testing.T) {
	patches := gomonkey.ApplyGlobalVar(&UpgradeStoragePath, "./")
	patches.ApplyGlobalVar(&VersionFilePath, "./version")
	patches.ApplyFuncReturn(CheckArchitecture, true)
	info := &VersionInfo{
		ProductModel:   "fobrain-00001",
		ReleaseVersion: "2.0.0",
		ReleaseDate:    "2024-09-06",
		ReleaseDesc:    "全新升级Fobrain带给您丝滑体验",
		VersionDependencies: []string{
			"1.0.0",
			"1.9.0",
		},
	}
	lowerInfo := &VersionInfo{
		ProductModel:        "fobrain-00000",
		ReleaseVersion:      "1.9.0",
		ReleaseDate:         "2024-08-06",
		ReleaseDesc:         "全新升级Fobrain带给您丝滑体验",
		VersionDependencies: []string{},
	}

	err := GenVersionFile("./version", info)
	if err != nil {
		t.Fatal(err)
	}
	// 创建文件
	exampleFileName := "example.txt"
	file, err := os.Create(exampleFileName)
	if err != nil {
		t.Fatal("Error creating file:", err)
	}
	defer file.Close()

	// 写入内容到文件
	_, err = file.WriteString("Hello, World!\n")
	if err != nil {
		t.Fatal("Error writing to file:", err)
	}
	// 要创建的 .tar.gz 文件名
	tarGzFileName := "example.tar.gz"
	// 准备 tar 命令参数
	os.Mkdir("./tmp", 0755)
	defer os.RemoveAll("./tmp")
	execArgs := []string{"-cvzf", fmt.Sprintf("./tmp/%s", tarGzFileName), exampleFileName, "version"}
	//  执行 tar 命令
	cmd := exec.Command("tar", execArgs...)
	// 运行命令并捕获输出
	output, err := cmd.CombinedOutput()
	if err != nil {
		t.Fatalf("Error executing tar command: %v, Command output: %s\n", err, output)
	}
	os.Remove(exampleFileName)
	os.Remove("./version")

	GenVersionFile(VersionFilePath, lowerInfo)
	defer os.Remove(VersionFilePath)
	defer os.RemoveAll(time.Now().Format(utils.DateMinuteLayout))

	type args struct {
		filePath string
		fileName string
		fileExt  string
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "test1",
			args: args{
				filePath: "./tmp/",
				fileName: tarGzFileName,
				fileExt:  ".gz",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			nowVersion, latestVersion, err := UpgradeVersionVerify(tt.args.filePath, tt.args.fileName, tt.args.fileExt)
			if err != nil {
				t.Fatal("失败", err)
			}
			t.Log(nowVersion)
			t.Log(latestVersion)
			_, err = os.Stat(fmt.Sprintf("./tmp/%s", exampleFileName))
			if os.IsNotExist(err) {
				t.Errorf("file %s not exist\n", exampleFileName)
			} else {
				t.Logf("file %s exist\n", exampleFileName)
			}
		})
	}
	patches.Reset()
	os.Remove(exampleFileName)
	os.Remove(upgradeProcessFile)
}

func TestVersionTooLowError_Error(t *testing.T) {
	err := &VersionTooLowError{CurrentVersion: "1.0.0", RequiredVersion: "2.0.0"}
	expectedMessage := "当前版本过低,需要先升级前置版本2.0.0"

	if err.Error() != expectedMessage {
		t.Errorf("期望错误信息: %s, 实际错误信息: %s", expectedMessage, err.Error())
	}
}

func TestCheckArchitecture(t *testing.T) {
	arch := runtime.GOARCH
	if strings.Contains(strings.ToLower(arch), "arm") {
		if CheckArchitecture("fobrain-upgrade-v2.0.8-beta.tar.gz") {
			t.Fatal("arm不符合预期")
		}
		if CheckArchitecture("fobrain-upgrade-v2.0.8-arm64.tar.gz") {
			t.Log("arm符合预期")
		}
	} else {
		if CheckArchitecture("fobrain-upgrade-v2.0.8-beta.tar.gz") {
			t.Log("符合预期")
		}
		if CheckArchitecture("fobrain-upgrade-v2.0.8-arm64.tar.gz") {
			t.Fatal("不符合预期")
		}
	}
}
