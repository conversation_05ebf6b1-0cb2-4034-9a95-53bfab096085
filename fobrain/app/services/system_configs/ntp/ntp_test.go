package ntp

import (
	"errors"
	"fobrain/fobrain/app/request/system_config"
	"fobrain/models/mysql/system_configs"
	"fobrain/pkg/utils"
	"os"
	"os/exec"
	"reflect"
	"strconv"
	"testing"
	"time"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/stretchr/testify/assert"
)

// TestUpdateOrCreateNTPConfig 测试 UpdateOrCreateNTPConfig 函数
func TestUpdateOrCreateNTPConfig(t *testing.T) {
	type args struct {
		req *system_config.NtpConfigReq
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
		mocks   func() *gomonkey.Patches
	}{
		{
			name: "成功更新或创建NTP配置",
			args: args{
				req: &system_config.NtpConfigReq{
					NtpServer:   "ntp.example.com",
					NtpTimeZone: "Asia/Shanghai",
					OnlySync:    "false",
				},
			},
			wantErr: false,
			mocks: func() *gomonkey.Patches {
				patches := gomonkey.NewPatches()
				patches.ApplyFunc(utils.StructToMap, func(data interface{}, tagName string) (map[string]interface{}, error) {
					return map[string]interface{}{"server": "ntp.example.com", "time_zone": "Asia/Shanghai"}, nil
				})
				patches.ApplyFunc(SyncNTP, func(ntpServer, ntpTimezone string) error {
					return nil
				})
				patches.ApplyMethod(reflect.TypeOf(&system_configs.SystemConfigs{}), "SetMultiConfig", func(_ *system_configs.SystemConfigs, configs map[string]interface{}) error {
					return nil
				})
				return patches
			},
		},
		{
			name: "StructToMap 发生错误",
			args: args{
				req: &system_config.NtpConfigReq{},
			},
			wantErr: true,
			mocks: func() *gomonkey.Patches {
				patches := gomonkey.NewPatches()
				patches.ApplyFunc(utils.StructToMap, func(data interface{}, tagName string) (map[string]interface{}, error) {
					return nil, errors.New("StructToMap error")
				})
				return patches
			},
		},
		{
			name: "SyncNTP 发生错误",
			args: args{
				req: &system_config.NtpConfigReq{
					NtpServer:   "ntp.example.com",
					NtpTimeZone: "Asia/Shanghai",
				},
			},
			wantErr: true,
			mocks: func() *gomonkey.Patches {
				patches := gomonkey.NewPatches()
				patches.ApplyFunc(utils.StructToMap, func(data interface{}, tagName string) (map[string]interface{}, error) {
					return map[string]interface{}{"server": "ntp.example.com", "time_zone": "Asia/Shanghai"}, nil
				})
				patches.ApplyFunc(SyncNTP, func(ntpServer, ntpTimezone string) error {
					return errors.New("SyncNTP error")
				})
				return patches
			},
		},

		{
			name: "OnlySync 为 true 但同步失败",
			args: args{
				req: &system_config.NtpConfigReq{
					NtpServer:   "ntp.example.com",
					NtpTimeZone: "Asia/Shanghai",
					OnlySync:    "true",
				},
			},
			wantErr: true,
			mocks: func() *gomonkey.Patches {
				patches := gomonkey.NewPatches()
				patches.ApplyFunc(SyncNTP, func(ntpServer, ntpTimezone string) error {
					return errors.New("SyncNTP error")
				})
				return patches
			},
		},
		{
			name: "参数验证失败 - NTP服务器为空",
			args: args{
				req: &system_config.NtpConfigReq{
					NtpServer:   "",
					NtpTimeZone: "Asia/Shanghai",
					OnlySync:    "false",
				},
			},
			wantErr: true,
			mocks: func() *gomonkey.Patches {
				return gomonkey.NewPatches()
			},
		},
		{
			name: "SetMultiConfig 发生错误",
			args: args{
				req: &system_config.NtpConfigReq{
					NtpServer:   "ntp.example.com",
					NtpTimeZone: "Asia/Shanghai",
					OnlySync:    "false",
				},
			},
			wantErr: true,
			mocks: func() *gomonkey.Patches {
				patches := gomonkey.NewPatches()
				patches.ApplyFunc(utils.StructToMap, func(data interface{}, tagName string) (map[string]interface{}, error) {
					return map[string]interface{}{"server": "ntp.example.com", "time_zone": "Asia/Shanghai"}, nil
				})
				patches.ApplyFunc(SyncNTP, func(ntpServer, ntpTimezone string) error {
					return nil
				})
				patches.ApplyMethod(reflect.TypeOf(&system_configs.SystemConfigs{}), "SetMultiConfig", func(_ *system_configs.SystemConfigs, configs map[string]interface{}) error {
					return errors.New("SetMultiConfig error")
				})
				return patches
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			patches := tt.mocks()
			defer patches.Reset()
			if err := UpdateOrCreateNTPConfig(tt.args.req); (err != nil) != tt.wantErr {
				t.Errorf("UpdateOrCreateNTPConfig() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

// TestSyncNTPOnLinux 测试 syncNTPOnLinux 函数
func TestSyncNTPOnLinux(t *testing.T) {
	tests := []struct {
		name      string
		ntpServer string
		timeZone  string
		wantErr   bool
		mocks     func() *gomonkey.Patches
	}{
		{
			name:      "成功同步NTP",
			ntpServer: "ntp1.aliyun.com",
			timeZone:  "Asia/Shanghai",
			wantErr:   false,
			mocks: func() *gomonkey.Patches {
				patches := gomonkey.NewPatches()
				patches.ApplyFunc(getNTPTime, func(ntpServer string) (time.Time, error) {
					return time.Now(), nil
				})
				patches.ApplyFunc(setSystemTime, func(newTime time.Time) error {
					return nil
				})
				patches.ApplyFunc(setTimeZone, func(timeZone string) error {
					return nil
				})
				return patches
			},
		},
		{
			name:      "获取NTP时间失败",
			ntpServer: "invalid.ntp.server",
			timeZone:  "Asia/Shanghai",
			wantErr:   true,
			mocks: func() *gomonkey.Patches {
				patches := gomonkey.NewPatches()
				patches.ApplyFunc(getNTPTime, func(ntpServer string) (time.Time, error) {
					return time.Time{}, errors.New("failed to get NTP time")
				})
				return patches
			},
		},
		{
			name:      "设置系统时间失败",
			ntpServer: "ntp1.aliyun.com",
			timeZone:  "Asia/Shanghai",
			wantErr:   true,
			mocks: func() *gomonkey.Patches {
				patches := gomonkey.NewPatches()
				patches.ApplyFunc(getNTPTime, func(ntpServer string) (time.Time, error) {
					return time.Now(), nil
				})
				patches.ApplyFunc(setSystemTime, func(newTime time.Time) error {
					return errors.New("setSystemTime error")
				})
				return patches
			},
		},
		{
			name:      "设置时区失败",
			ntpServer: "ntp1.aliyun.com",
			timeZone:  "Invalid/TimeZone",
			wantErr:   true,
			mocks: func() *gomonkey.Patches {
				patches := gomonkey.NewPatches()
				patches.ApplyFunc(getNTPTime, func(ntpServer string) (time.Time, error) {
					return time.Now(), nil
				})
				patches.ApplyFunc(setSystemTime, func(newTime time.Time) error {
					return nil
				})
				patches.ApplyFunc(setTimeZone, func(timeZone string) error {
					return errors.New("setTimeZone error")
				})
				return patches
			},
		},
		{
			name:      "NTP服务器为空",
			ntpServer: "",
			timeZone:  "Asia/Shanghai",
			wantErr:   true,
			mocks: func() *gomonkey.Patches {
				return gomonkey.NewPatches()
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			patches := tt.mocks()
			defer patches.Reset()

			err := syncNTPOnLinux(tt.ntpServer, tt.timeZone)
			if (err != nil) != tt.wantErr {
				t.Errorf("syncNTPOnLinux() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

// TestGetNTPTime 测试 getNTPTime 函数
func TestGetNTPTime(t *testing.T) {
	tests := []struct {
		name      string
		ntpServer string
		want      time.Time
		wantErr   bool
		mocks     func() *gomonkey.Patches
	}{
		{
			name:      "成功获取NTP时间",
			ntpServer: "ntp1.aliyun.com",
			wantErr:   false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := getNTPTime(tt.ntpServer)
			if (err != nil) != tt.wantErr {
				t.Errorf("getNTPTime() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			assert.NotNil(t, got)
		})
	}
}

// TestSetSystemTime 测试 setSystemTime 函数
func TestSetSystemTime(t *testing.T) {
	tests := []struct {
		name    string
		newTime time.Time
		wantErr bool
		mocks   func() *gomonkey.Patches
	}{
		{
			name:    "成功设置系统时间",
			newTime: time.Date(2023, 1, 1, 10, 0, 0, 0, time.UTC),
			wantErr: false,
			mocks: func() *gomonkey.Patches {
				patches := gomonkey.NewPatches()
				// Mock exec.Command
				patches.ApplyFunc(exec.Command, func(name string, args ...string) *exec.Cmd {
					cmd := &exec.Cmd{}
					return cmd
				})
				// Mock cmd.Run
				patches.ApplyMethod(reflect.TypeOf(&exec.Cmd{}), "Run", func(_ *exec.Cmd) error {
					return nil
				})
				return patches
			},
		},
		{
			name:    "设置系统时间失败",
			newTime: time.Date(2023, 1, 1, 10, 0, 0, 0, time.UTC),
			wantErr: true,
			mocks: func() *gomonkey.Patches {
				patches := gomonkey.NewPatches()
				// Mock exec.Command
				patches.ApplyFunc(exec.Command, func(name string, args ...string) *exec.Cmd {
					cmd := &exec.Cmd{}
					return cmd
				})
				// Mock cmd.Run with error
				patches.ApplyMethod(reflect.TypeOf(&exec.Cmd{}), "Run", func(_ *exec.Cmd) error {
					return errors.New("cmd.Run error")
				})
				return patches
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			patches := tt.mocks()
			defer patches.Reset()

			err := setSystemTime(tt.newTime)
			if (err != nil) != tt.wantErr {
				t.Errorf("setSystemTime() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

// TestSetTimeZone 测试 setTimeZone 函数
func TestSetTimeZone(t *testing.T) {
	tests := []struct {
		name     string
		timeZone string
		wantErr  bool
		mocks    func() *gomonkey.Patches
	}{
		{
			name:     "成功设置时区",
			timeZone: "Asia/Shanghai",
			wantErr:  false,
			mocks: func() *gomonkey.Patches {
				patches := gomonkey.NewPatches()
				patches.ApplyFunc(os.Remove, func(name string) error {
					return nil
				})
				patches.ApplyFunc(os.Symlink, func(oldname, newname string) error {
					return nil
				})
				patches.ApplyFunc(os.Stat, func(name string) (os.FileInfo, error) {
					return nil, nil
				})
				return patches
			},
		},
		{
			name:     "时区文件不存在",
			timeZone: "Invalid/TimeZone",
			wantErr:  true,
			mocks: func() *gomonkey.Patches {
				patches := gomonkey.NewPatches()
				patches.ApplyFunc(os.Stat, func(name string) (os.FileInfo, error) {
					return nil, os.ErrNotExist
				})
				return patches
			},
		},
		{
			name:     "删除已有时区链接失败",
			timeZone: "Asia/Shanghai",
			wantErr:  true,
			mocks: func() *gomonkey.Patches {
				patches := gomonkey.NewPatches()
				patches.ApplyFunc(os.Stat, func(name string) (os.FileInfo, error) {
					return nil, nil
				})
				patches.ApplyFunc(os.Remove, func(name string) error {
					return errors.New("remove error")
				})
				return patches
			},
		},
		{
			name:     "创建时区链接失败",
			timeZone: "Asia/Shanghai",
			wantErr:  true,
			mocks: func() *gomonkey.Patches {
				patches := gomonkey.NewPatches()
				patches.ApplyFunc(os.Stat, func(name string) (os.FileInfo, error) {
					return nil, nil
				})
				patches.ApplyFunc(os.Remove, func(name string) error {
					return nil
				})
				patches.ApplyFunc(os.Symlink, func(oldname, newname string) error {
					return errors.New("symlink error")
				})
				return patches
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			patches := tt.mocks()
			defer patches.Reset()

			err := setTimeZone(tt.timeZone)
			if (err != nil) != tt.wantErr {
				t.Errorf("setTimeZone() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

// TestGetNTPConfig 测试 GetNTPConfig 函数
func TestGetNTPConfig(t *testing.T) {
	mockTime := time.Date(2023, 1, 1, 10, 0, 0, 0, time.UTC)
	mockTimeStr := strconv.FormatInt(mockTime.Unix(), 10)

	tests := []struct {
		name    string
		want    map[string]string
		wantErr bool
		mocks   func() *gomonkey.Patches
	}{
		{
			name: "成功获取NTP配置",
			want: map[string]string{
				"ntp_enable": "false",
				"server":     "ntp.example.com",
				"time_zone":  "Asia/Shanghai",
				"time":       mockTimeStr,
			},
			wantErr: false,
			mocks: func() *gomonkey.Patches {
				patches := gomonkey.NewPatches()
				patches.ApplyFunc(time.Now, func() time.Time {
					return mockTime
				})
				patches.ApplyMethod(reflect.TypeOf(&system_configs.SystemConfigs{}), "GetMultiConfig", func(_ *system_configs.SystemConfigs, keys []string) (map[string]string, error) {
					return map[string]string{
						"server":    "ntp.example.com",
						"time_zone": "Asia/Shanghai",
					}, nil
				})
				return patches
			},
		},
	}

	for _, tt := range tests {
		tt := tt // 创建一个新的变量来避免闭包问题
		t.Run(tt.name, func(t *testing.T) {
			patches := tt.mocks()
			defer patches.Reset()

			got, err := GetNTPConfig()
			if (err != nil) != tt.wantErr {
				t.Errorf("GetNTPConfig() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			// 检查时间戳是否正确
			if got != nil {
				assert.Equal(t, mockTimeStr, got["time"], "时间戳不匹配")
				// 删除时间戳后比较其他字段
				delete(got, "time")
				delete(tt.want, "time")
			}
			assert.Equal(t, tt.want, got, "GetNTPConfig() got = %v, want %v", got, tt.want)
		})
	}
}
