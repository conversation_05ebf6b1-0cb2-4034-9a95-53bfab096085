package ntp

import (
	"errors"
	"fmt"
	"fobrain/fobrain/app/repository/settings/license"
	"fobrain/fobrain/app/request/system_config"
	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/models/mysql/system_configs"
	"fobrain/pkg/cfg"
	"fobrain/pkg/utils"
	"os"
	"os/exec"
	"runtime"
	"strconv"
	"time"

	"github.com/beevik/ntp"
	"go-micro.dev/v4/logger"
)

// NTP配置参数结构体
type NTPOptions struct {
	// 最大重试次数
	MaxRetries int
	// 重试间隔（秒）
	RetryInterval int
	// 超时时间（秒）
	Timeout int
}

// 默认NTP配置
var DefaultNTPOptions = NTPOptions{
	MaxRetries:    3,
	RetryInterval: 2,
	Timeout:       5,
}

// UpdateOrCreateNTPConfig 更新或创建 NTP 配置
//
// 参数：
// - req：NTP 配置请求对象
//
// 返回值：
//   - error：如果发生错误，返回错误对象；否则返回 nil
//     如果成功，返回 nil
//
// 注意：
// - 该函数用于更新或创建 NTP 配置。
// - 如果 NTP 配置已存在，则更新配置；否则创建新的 NTP 配置。
// - 该函数返回一个错误对象，如果发生错误，返回错误对象；否则返回 nil。
// - 该函数使用了 NTP 配置请求对象来更新或创建 NTP 配置。
// - 该函数使用了 NTP 配置请求对象的 Key 和 Value 字段来更新或创建 NTP 配置。
func UpdateOrCreateNTPConfig(req *system_config.NtpConfigReq) error {
	//实现更新或创建 NTP 配置的逻辑
	ntpConfig, err := utils.StructToMap(req, "json")
	if err != nil {
		return err
	}
	//同步NTP配置
	err = SyncNTP(req.NtpServer, req.NtpTimeZone)
	if err != nil {
		// 处理错误
		return err
	}
	if req.OnlySync == "true" {
		//只同步时间，不存配置
		return nil
	}
	delete(ntpConfig, "only_sync")
	ntpConfig["ntp_interval"] = strconv.FormatInt(int64(req.NtpInterval), 10)
	//更新NTP更新时间
	ntpConfig["ntp_update_time"] = time.Now().Format("2006-01-02 15:04:05")
	// 更新或创建 NTP 配置
	err = system_configs.NewSystemConfigs().SetMultiConfig(ntpConfig)
	if err != nil {
		// 处理错误
		return err
	}
	return nil
}

// SyncNTP 同步 NTP
//
// 参数：
// - ntpServer：NTP 服务器地址
// - ntpTimezone：NTP 时区
//
// 返回值：
//   - error：如果发生错误，返回错误对象；否则返回 nil
//     如果成功，返回 nil
//
// 注意：
// - 该函数用于同步 NTP。
// - 该函数使用了 NTP 服务器地址和 NTP 时区来同步 NTP。
// - 该函数返回一个错误对象，如果发生错误，返回错误对象；否则返回 nil。
// - 该函数使用了 NTP 服务器地址和 NTP 时区来同步 NTP。
// - 如果 NTP 服务器地址或 NTP 时区无效，返回错误对象。
// - 如果 NTP 同步失败，返回错误对象。
// - 如果 NTP 同步成功，返回 nil。
func SyncNTP(ntpServer, ntpTimezone string) error {
	isLocal := cfg.LoadCommon().Local

	isDev := license.CheckDevModel(cfg.LoadCommon().Env)
	isTest := testcommon.IsTest()
	// 开发环境不设置NTP 直接返回成功
	if isDev && isLocal && !isTest {
		logger.Infof("开发环境不设置NTP 直接返回成功")
		return nil
	}
	switch runtime.GOOS {
	case "linux":
		return syncNTPOnLinux(ntpServer, ntpTimezone)
	default:
		return errors.New("不支持的操作系统: " + runtime.GOOS)
	}
}

// syncNTPOnLinux 同步 NTP
//
// 参数：
// - ntpServer：NTP 服务器地址
// - ntpTimezone：NTP 时区
//
// 返回值：
//   - error：如果发生错误，返回错误对象；否则返回 nil
//     如果成功，返回 nil
//
// 注意：
// - 该函数用于同步 NTP。
// - 该函数使用了 NTP 服务器地址和 NTP 时区来同步 NTP。
// - 该函数返回一个错误对象，如果发生错误，返回错误对象；否则返回 nil。
// - 该函数使用了 NTP 服务器地址和 NTP 时区来同步 NTP。
// - 如果 NTP 服务器地址或 NTP 时区无效，返回错误对象。
// - 如果 NTP 同步失败，返回错误对象。
// - 如果 NTP 同步成功，返回 nil。
func syncNTPOnLinux(ntpServer, ntpTimezone string) error {
	// 使用 NTP 库获取 NTP 时间
	ntpTime, err := getNTPTime(ntpServer)
	if err != nil {
		return err
	}

	// 根据时区转换时间
	if ntpTimezone != "" {
		loc, err := time.LoadLocation(ntpTimezone)
		if err != nil {
			return fmt.Errorf("加载时区失败: %v", err)
		}
		ntpTime = ntpTime.In(loc)
	} else {
		ntpTime = ntpTime.Local()
	}

	// 更新系统时间
	err = setSystemTime(ntpTime)
	if err != nil {
		return err
	}

	// 设置时区
	err = setTimeZone(ntpTimezone)
	if err != nil {
		return err
	}

	return nil
}

// 获取 NTP 时间，使用默认参数
func getNTPTime(server string) (time.Time, error) {
	return getNTPTimeWithOptions(server, DefaultNTPOptions)
}

// 获取 NTP 时间，带自定义配置参数
func getNTPTimeWithOptions(server string, options NTPOptions) (time.Time, error) {
	var lastErr error
	// 设置NTP查询选项
	ntpOptions := ntp.QueryOptions{
		Timeout: time.Duration(options.Timeout) * time.Second,
	}

	// 重试逻辑
	for i := 0; i < options.MaxRetries; i++ {
		// 使用ntp库的Time函数获取时间，带超时设置
		ntpResponse, err := ntp.QueryWithOptions(server, ntpOptions)
		if err == nil {
			return ntpResponse.Time, nil
		}

		// 记录错误并重试
		lastErr = err
		if i < options.MaxRetries-1 {
			time.Sleep(time.Duration(options.RetryInterval) * time.Second)
			// 增加超时时间，指数退避
			ntpOptions.Timeout += time.Duration(options.Timeout) * time.Second
		}
	}

	// 所有重试都失败
	return time.Time{}, fmt.Errorf("无法从NTP服务器获取时间，已重试%d次: %v", options.MaxRetries, lastErr)
}

// 设置系统时间
func setSystemTime(newTime time.Time) error {
	// 检查文件是否存在
	// if _, err := os.Stat("/usr/bin/date"); os.IsNotExist(err) {
	// 	return fmt.Errorf("date命令不存在: %v", err)
	// }

	// 使用 date 命令来更新系统时间（格式化为可接受的格式）
	dateCommand := fmt.Sprintf("date -s \"%s\"", newTime.Format("2006-01-02 15:04:05"))
	cmd := exec.Command("sh", "-c", dateCommand)
	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("更新系统时间失败: %v, 输出: %s", err, string(output))
	}

	// 如果是Linux系统，还需要将时间写入硬件时钟
	if runtime.GOOS == "linux" {
		hwClockCmd := exec.Command("hwclock", "--systohc")
		if output, err := hwClockCmd.CombinedOutput(); err != nil {
			// 仅记录错误，不中断流程
			fmt.Printf("写入硬件时钟失败: %v, 输出: %s\n", err, string(output))
		}
	}

	return nil
}

// 设置时区
func setTimeZone(timeZone string) error {
	// 检查时区文件是否存在
	timezoneFilePath := fmt.Sprintf("/usr/share/zoneinfo/%s", timeZone)
	if _, err := os.Stat(timezoneFilePath); os.IsNotExist(err) {
		return fmt.Errorf("时区文件不存在: %s", timezoneFilePath)
	}

	// 如果/etc/localtime已存在，先删除
	if _, err := os.Stat("/etc/localtime"); err == nil {
		if err := os.Remove("/etc/localtime"); err != nil {
			return fmt.Errorf("删除旧的时区文件失败: %v", err)
		}
	}

	// 创建新的符号链接
	if err := os.Symlink(timezoneFilePath, "/etc/localtime"); err != nil {
		return fmt.Errorf("设置时区失败: %v", err)
	}

	// 如果存在timezone文件，也更新它
	if _, err := os.Stat("/etc/timezone"); err == nil {
		if err := os.WriteFile("/etc/timezone", []byte(timeZone+"\n"), 0644); err != nil {
			// 仅记录错误，不中断流程
			fmt.Printf("更新/etc/timezone文件失败: %v\n", err)
		}
	}

	return nil
}

// GetNTPConfig 获取 NTP 配置
//
// 返回值：
//   - map[string]string：NTP 配置的键值对
//   - error：如果发生错误，返回错误对象；否则返回 nil
//     如果成功，返回 NTP 配置的键值对
//
// 注意：
// - 该函数用于获取 NTP 配置。
// - 该函数返回一个 NTP 配置的键值对和一个错误对象。
// - 如果成功，返回 NTP 配置的键值对；否则返回错误对象。
// - 该函数使用了 NTP 配置请求对象来获取 NTP 配置。
// - 该函数使用了 NTP 配置请求对象的 Key 字段来获取 NTP 配置。
func GetNTPConfig(keys ...string) (map[string]string, error) {
	//获取NTP配置Key
	ntpConfig := &system_config.NtpConfigReq{}
	//获取NTP配置Key
	ntpConfigJson, err := utils.StructToMap(ntpConfig, "json")
	if err != nil {
		return nil, err
	}
	ntpConfigKeys := make([]string, 0)
	for key := range ntpConfigJson {
		ntpConfigKeys = append(ntpConfigKeys, key)
	}
	if len(keys) > 0 {
		ntpConfigKeys = append(ntpConfigKeys, keys...)
	}
	// 实现获取 NTP 配置的逻辑
	configs, err := system_configs.NewSystemConfigs().GetMultiConfig(ntpConfigKeys)
	if err != nil {
		return nil, err
	}
	if configs["ntp_enable"] == "" {
		configs["ntp_enable"] = "false"
	}
	configs["time"] = strconv.FormatInt(time.Now().Unix(), 10)
	return configs, nil
}
