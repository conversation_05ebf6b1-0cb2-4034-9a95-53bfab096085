package device_strategy

import (
	"context"
	"encoding/json"
	"errors"
	"fobrain/fobrain/app/request/system_config"
	"fobrain/initialize/redis"
	"fobrain/models/mysql/system_configs"
	"fobrain/pkg/utils"
	"strings"
	"time"

	redis2 "github.com/go-redis/redis/v8"
	"github.com/spf13/cast"
	"go-micro.dev/v4/logger"
)

const deviceDhcpConfig = "cache:device:strategies:dhcp_config"

// IsNotDhcpIp 检查IP是否能提取设备
func IsNotDhcpIp(ip string) bool {
	config, err := GetDeviceStrategy()
	if err != nil {
		return false
	}
	if !config.HasDhcp {
		return true
	}
	isDhcpIp, err := utils.ContainsIP(ip, config.Ip)
	if err != nil {
		return false
	}
	if isDhcpIp {
		return false
	}
	return true
}

func DeleteCache() {
	redis.GetRedisClient().Del(context.Background(), deviceDhcpConfig)
}

func GetDeviceStrategy() (*system_config.DeviceConfigReq, error) {
	strategiesCache, err := redis.GetRedisClient().Get(context.Background(), deviceDhcpConfig).Result()
	if err != nil {
		//缓存未命中
		if errors.Is(err, redis2.Nil) {
			logger.Debug("【device strategy dhcp】缓存设备提取 DHCP 配置")
			data, err := system_configs.NewSystemConfigs().GetMultiConfig([]string{"has_dhcp", "dhcp_ips"})
			if err != nil {
				return nil, err
			}
			ips := make([]string, 0)
			if data["dhcp_ips"] != "" {
				ips = strings.Split(data["dhcp_ips"], ",")
			}
			deviceConfig := &system_config.DeviceConfigReq{
				Ip:      ips,
				HasDhcp: cast.ToBool(data["has_dhcp"]),
			}
			deviceConfigJson, err := json.Marshal(deviceConfig)
			if err != nil {
				logger.Errorf("【device strategy dhcp】缓存设备提取 DHCP 配置 err:%s", err.Error())
				return nil, err
			}
			err = redis.GetRedisClient().Set(context.Background(), deviceDhcpConfig, deviceConfigJson, time.Hour*24).Err()
			if err != nil {
				logger.Errorf("【device strategy dhcp】缓存设备提取 DHCP 配置缓存失败 %s", err.Error())
				return nil, err
			}
			return deviceConfig, nil
		}
		return nil, err
	}
	logger.Debugf("【device strategy dhcp】命中设备提取 DHCP 配置缓存")
	// 缓存命中
	s := &system_config.DeviceConfigReq{}
	err = json.Unmarshal([]byte(strategiesCache), &s)
	if err != nil {
		return nil, err
	}
	return s, nil
}
