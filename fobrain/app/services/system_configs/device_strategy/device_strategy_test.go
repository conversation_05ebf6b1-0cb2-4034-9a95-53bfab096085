package device_strategy

import (
	"context"
	"fobrain/fobrain/app/request/system_config"
	testcommon "fobrain/fobrain/tests/common_test"
	"github.com/DATA-DOG/go-sqlmock"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/alicebob/miniredis/v2"
	"github.com/go-redis/redis/v8"
	"github.com/stretchr/testify/assert"
	"testing"
	"time"
)

func TestGetDeviceStrategy(t *testing.T) {
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()

	mockDb.ExpectQuery("SELECT * FROM `system_configs` WHERE `key` IN (?,?)").
		WithArgs("has_dhcp", "dhcp_ips").
		WillReturnRows(sqlmock.NewRows([]string{"key", "value"}).
			AddRow("has_dhcp", "true").
			AddRow("dhcp_ips", "**********/24"))
	// Setup miniredis
	s, err := miniredis.Run()
	if err != nil {
		t.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
	}
	defer s.Close()

	cli := redis.NewClient(&redis.Options{
		Addr: s.Addr(),
	})
	testcommon.SetRedisClient(cli)
	// 模拟 Redis 未命中情况
	mockRedisGet := gomonkey.ApplyMethodFunc(testcommon.GetRedisClient(), "Get", func(ctx context.Context, key string) *redis.StringCmd {
		cmd := redis.NewStringCmd(ctx)
		cmd.SetErr(redis.Nil) // 模拟 key 不存在
		return cmd
	})
	defer mockRedisGet.Reset()
	dhcpConfig := &system_config.DeviceConfigReq{
		HasDhcp: true,
		Ip:      []string{"**********/24"},
	}
	// 模拟 Redis 设置操作
	mockRedisSet := gomonkey.ApplyMethodFunc(testcommon.GetRedisClient(), "Set", func(ctx context.Context, key string, value interface{}, expiration time.Duration) *redis.StatusCmd {
		cmd := redis.NewStatusCmd(ctx)
		return cmd
	})
	defer mockRedisSet.Reset()

	// 调用被测试方法
	result, err := GetDeviceStrategy()
	assert.NoError(t, err)
	assert.Equal(t, dhcpConfig, result)
}
