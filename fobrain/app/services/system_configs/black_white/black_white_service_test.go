package blackwhite

import (
	"errors"
	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/models/mysql/system_configs"
	"github.com/stretchr/testify/assert"
	"os"
	"reflect"
	"testing"
	"time"

	"github.com/agiledragon/gomonkey/v2"

	"github.com/DATA-DOG/go-sqlmock"
)

func TestBlackWhiteService_List(t *testing.T) {
	t.Run("正常", func(t *testing.T) {
		want := &BlackWhiteConfListResponse{
			White: []BlackWhiteConfItem{
				{ID: 1, IP: "***********-*************", CreatedAt: "2024-09-20 16:32:14"},
				{ID: 2, IP: "***********/24", CreatedAt: "2024-09-20 16:32:14"},
				{ID: 3, IP: "*************", CreatedAt: "2024-09-20 16:32:14"},
			},
			Black: []BlackWhiteConfItem{
				{ID: 4, IP: "***********-*************", CreatedAt: "2024-09-20 16:32:14"},
				{ID: 5, IP: "***********/24", CreatedAt: "2024-09-20 16:32:14"},
				{ID: 6, IP: "*************", CreatedAt: "2024-09-20 16:32:14"},
			},
		}
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()
		mockDb.ExpectQuery("SELECT id,ip,strategy_type,created_at FROM `black_white_config` ORDER BY id DESC").
			WillReturnRows(
				sqlmock.NewRows([]string{"id", "ip", "strategy_type", "created_at"}).
					AddRow(1, "***********-*************", "white", "2024-09-20 16:32:14").
					AddRow(2, "***********/24", "white", "2024-09-20 16:32:14").
					AddRow(3, "*************", "white", "2024-09-20 16:32:14").
					AddRow(4, "***********-*************", "black", "2024-09-20 16:32:14").
					AddRow(5, "***********/24", "black", "2024-09-20 16:32:14").
					AddRow(6, "*************", "black", "2024-09-20 16:32:14"),
			)
		got, err := NewBlackWhiteService().List()
		if err != nil {
			t.Fatal(err)
		}
		if !reflect.DeepEqual(got, want) {
			t.Errorf("BlackWhiteService.List() = %v, want %v", got, want)
		}
	})
	t.Run("error", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()
		mockDb.ExpectQuery("SELECT id,ip,strategy_type,created_at FROM `black_white_config` ORDER BY id DESC").
			WillReturnError(sqlmock.ErrCancelled)

		_, err := NewBlackWhiteService().List()
		if err == nil {
			t.Fatal("应该是err")
		}

	})
	t.Run("空列表", func(t *testing.T) {
		want := &BlackWhiteConfListResponse{
			Black: []BlackWhiteConfItem{},
			White: []BlackWhiteConfItem{},
		}
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()
		mockDb.ExpectQuery("SELECT id,ip,strategy_type,created_at FROM `black_white_config` ORDER BY id DESC").
			WillReturnRows(sqlmock.NewRows([]string{"id", "ip", "strategy_type", "created_at"}))

		got, err := NewBlackWhiteService().List()
		if err != nil {
			t.Fatal(err)
		}
		if !reflect.DeepEqual(got, want) {
			t.Errorf("BlackWhiteService.List() = %v, want %v", got, want)
		}
	})
}

func TestBlackWhiteService_Delete(t *testing.T) {
	t.Run("正常", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()
		mockDb.ExpectBegin()
		mockDb.ExpectExec("DELETE FROM `black_white_config` WHERE id in (?,?)").
			WithArgs(1, 2).
			WillReturnResult(sqlmock.NewResult(0, 2))
		mockDb.ExpectCommit()
		err := NewBlackWhiteService().Delete([]int64{1, 2})
		if err != nil {
			t.Fatal(err)
		}

	})
	t.Run("应该是错误", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		//defer mockDb.Close()
		mockDb.ExpectBegin()
		mockDb.ExpectExec("DELETE FROM `black_white_config` WHERE id in (?)").
			WithArgs(1).
			WillReturnError(sqlmock.ErrCancelled)
		mockDb.ExpectRollback()
		mockDb.ExpectCommit()

		err := NewBlackWhiteService().Delete([]int64{1})
		if err == nil {
			t.Fatal("应该是err")
		}
	})
}

func TestBlackWhiteService_Save(t *testing.T) {
	t.Run("正常", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		mockDb.ExpectBegin()
		mockDb.ExpectExec("INSERT INTO `black_white_config` (`created_at`,`updated_at`,`ip`,`strategy_type`) VALUES (?,?,?,?)").
			WillReturnResult(sqlmock.NewResult(1, 1))
		mockDb.ExpectCommit()

		err := NewBlackWhiteService().Save(&BlackWhiteConfAddRequest{
			StrategyType: system_configs.WhiteStrategyType,
			IPList:       []string{"*************"},
		})
		if err != nil {
			t.Fatal(err)
		}

	})
	t.Run("应该是错误", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		//defer mockDb.Close()
		mockDb.ExpectBegin()
		mockDb.ExpectExec("INSERT INTO `black_white_config` (`created_at`,`updated_at`,`ip`,`strategy_type`) VALUES (?,?,?,?)").
			WillReturnError(errors.New("模拟错误"))
		mockDb.ExpectRollback()
		mockDb.ExpectCommit()

		err := NewBlackWhiteService().Save(&BlackWhiteConfAddRequest{})
		if err == nil {
			t.Fatal("应该是err")
		}
	})
}

func TestBlackWhiteService_Judge(t *testing.T) {
	mockDb := testcommon.InitSqlMock()
	defer mockDb.Close()
	t.Run("在白名单", func(t *testing.T) {
		//mockDb.ExpectQuery("SELECT `ip` FROM `black_white_config` WHERE strategy_type = ?").
		//	WithArgs(system_configs.WhiteStrategyType).
		//	WillReturnRows(
		//		sqlmock.NewRows([]string{"ip"}).
		//			AddRow("***********-*************").
		//			AddRow("***********/24").
		//			AddRow("*************"),
		//	)
		//
		//mockDb.ExpectQuery("SELECT `ip` FROM `black_white_config` WHERE strategy_type = ?").
		//	WithArgs(system_configs.BlackStrategyType).
		//	WillReturnRows(
		//		sqlmock.NewRows([]string{"ip"}).
		//			AddRow("***********-*************").
		//			AddRow("***********/24").
		//			AddRow("*************"),
		//	)
		time.Sleep(time.Second)
		defer gomonkey.ApplyMethodReturn(&system_configs.BlackWhiteConfig{}, "IsIPAllowed", true, nil).Reset()
		allowed, err := NewBlackWhiteService().Judge("*************")
		if err != nil {
			t.Fatal(err)
		}
		if allowed != true {
			t.Errorf("BlackWhiteService.List() = %v, want true", allowed)
		}
	})
	t.Run("在黑名单", func(t *testing.T) {
		//mockDb.ExpectQuery("SELECT `ip` FROM `black_white_config` WHERE strategy_type = ?").
		//	WithArgs(system_configs.WhiteStrategyType).
		//	WillReturnRows(
		//		sqlmock.NewRows([]string{"ip"}),
		//	)
		//mockDb.ExpectQuery("SELECT `ip` FROM `black_white_config` WHERE strategy_type = ?").
		//	WithArgs(system_configs.BlackStrategyType).
		//	WillReturnRows(
		//		sqlmock.NewRows([]string{"ip"}).
		//			AddRow("***********-*************").
		//			AddRow("***********/24").
		//			AddRow("*************"),
		//	)
		time.Sleep(time.Second)
		defer gomonkey.ApplyMethodReturn(&system_configs.BlackWhiteConfig{}, "IsIPAllowed", false, nil).Reset()
		allowed, err := NewBlackWhiteService().Judge("*************")
		t.Log(allowed)
		if err != nil {
			t.Fatal(err)
		}
		if allowed != false {
			t.Errorf("BlackWhiteService.List() = %v, want:false", allowed)
		}
	})
}

func TestBlackWhiteService_Import(t *testing.T) {
	mockDb := testcommon.InitSqlMock()
	mockDb.ExpectBegin()
	mockDb.ExpectExec("INSERT INTO `black_white_config` (`created_at`,`updated_at`,`ip`,`strategy_type`) VALUES (?,?,?,?)").
		WillReturnResult(sqlmock.NewResult(2, 2))
	mockDb.ExpectCommit()
	err := NewBlackWhiteService().Import([][]string{
		{"黑名单", "***********"},
		{"白名单", "127.0.0.1"},
	})
	assert.Nil(t, err)
}

func TestBlackWhiteService_Export(t *testing.T) {
	mockDb := testcommon.InitSqlMock()
	defer mockDb.Close()
	mockDb.ExpectQuery("SELECT * FROM `black_white_config` WHERE strategy_type = ?").
		WillReturnRows(
			sqlmock.NewRows([]string{"id", "ip", "strategy_type", "created_at"}).
				AddRow(1, "***********-*************", "black", "2024-09-20 16:32:14").
				AddRow(2, "***********/24", "black", "2024-09-20 16:32:14").
				AddRow(3, "*************", "black", "2024-09-20 16:32:14").
				AddRow(4, "***********-*************", "black", "2024-09-20 16:32:14").
				AddRow(5, "***********/24", "black", "2024-09-20 16:32:14").
				AddRow(6, "*************", "black", "2024-09-20 16:32:14"),
		)
	path, err := NewBlackWhiteService().Export("black", []uint64{})
	assert.Nil(t, err)
	assert.NotEmpty(t, path)
	os.Remove(path)
}
