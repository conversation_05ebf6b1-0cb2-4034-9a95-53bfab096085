package blackwhite

import (
	"errors"
	"fmt"
	"fobrain/initialize/mysql"
	"fobrain/models/mysql/system_configs"
	"git.gobies.org/caasm/fobrain-components/utils"
	"regexp"
	"strings"
	"time"
)

type BlackWhiteService struct{}

func NewBlackWhiteService() *BlackWhiteService {
	return &BlackWhiteService{}
}

type BlackWhiteConfItem struct {
	ID        uint64 `json:"id"`
	IP        string `json:"ip"`
	CreatedAt string `json:"created_at"`
}

type BlackWhiteConfListResponse struct {
	Black []BlackWhiteConfItem `json:"black_list"`
	White []BlackWhiteConfItem `json:"white_list"`
}

func (s *BlackWhiteService) List() (*BlackWhiteConfListResponse, error) {
	list, err := system_configs.NewBlackWhiteList().GetList()
	if err != nil {
		return nil, err
	}
	var resp = BlackWhiteConfListResponse{
		Black: []BlackWhiteConfItem{},
		White: []BlackWhiteConfItem{},
	}
	for _, v := range list {
		if v.StrategyType == system_configs.BlackStrategyType {
			resp.Black = append(resp.Black, BlackWhiteConfItem{
				ID:        v.Id,
				IP:        v.IP,
				CreatedAt: v.CreatedAt.String(),
			})
		}
		if v.StrategyType == system_configs.WhiteStrategyType {
			resp.White = append(resp.White, BlackWhiteConfItem{
				ID:        v.Id,
				IP:        v.IP,
				CreatedAt: v.CreatedAt.String(),
			})
		}
	}
	return &resp, nil
}

type BlackWhiteConfAddRequest struct {
	StrategyType string   `json:"strategy_type"`
	IPList       []string `json:"ip_list"`
}

func (s *BlackWhiteService) Save(req *BlackWhiteConfAddRequest) error {
	if req.StrategyType != system_configs.BlackStrategyType && req.StrategyType != system_configs.WhiteStrategyType {
		return errors.New("不支持的类型")
	}

	var insertList = make([]*system_configs.BlackWhiteConfig, 0, len(req.IPList))
	for _, v := range req.IPList {
		t := strings.TrimSpace(v)
		if t == "" {
			continue
		}
		if !system_configs.NewBlackWhiteList().IsValidIPRange(t) {
			return fmt.Errorf("不支持的IP格式:%s", t)
		}
		insertList = append(insertList, &system_configs.BlackWhiteConfig{
			IP:           t,
			StrategyType: req.StrategyType,
		})
	}
	return system_configs.NewBlackWhiteList().BatchCreate(insertList)
}

func (s *BlackWhiteService) Delete(ids []int64) error {
	return system_configs.NewBlackWhiteList().DeleteBlackWhiteItem(ids)
}
func (s *BlackWhiteService) Judge(ip string) (bool, error) {
	return system_configs.NewBlackWhiteList().IsIPAllowed(ip)
}

func (s *BlackWhiteService) Import(rows [][]string) error {
	var insertList = make([]*system_configs.BlackWhiteConfig, 0)
	for i, row := range rows {
		if i == 0 {
			continue
		}
		if len(row) < 2 {
			continue
		}

		ip := strings.TrimSpace(row[1])
		// 处理类型
		var strategyType string
		switch row[0] {
		case "黑名单":
			strategyType = system_configs.BlackStrategyType
		case "白名单":
			strategyType = system_configs.WhiteStrategyType
		default:
			return fmt.Errorf("第%d行数据类型错误，必须是'黑名单'或'白名单'", i+1)
		}
		if !utils.IsValidIP(ip) {
			return fmt.Errorf("第%d行IP地址无效: %s", i+1, ip)
		}

		// 添加到批量保存列表
		insertList = append(insertList, &system_configs.BlackWhiteConfig{
			IP:           ip,
			StrategyType: strategyType,
		})
	}

	err := system_configs.NewBlackWhiteList().BatchCreate(insertList)
	if err != nil && strings.Contains(err.Error(), "Duplicate entry") {
		re := regexp.MustCompile(`\b(?:\d{1,3}\.){3}\d{1,3}\b`)
		return fmt.Errorf("列表已有 %v", strings.Join(re.FindAllString(insertList[0].IP, -1), " "))
	}
	if err != nil {
		return err
	}
	return nil
}
func (s *BlackWhiteService) Export(category string, ids []uint64) (string, error) {
	if category != system_configs.WhiteStrategyType && category != system_configs.BlackStrategyType {
		return "", fmt.Errorf("分类错误:%s", category)
	}
	var query = []mysql.HandleFunc{
		mysql.WithWhere("strategy_type = ?", category),
	}
	if len(ids) > 0 {
		query = append(query, mysql.WithWhere("id in (?)", ids))
	}
	list, _, err := mysql.List[system_configs.BlackWhiteConfig](0, 0, query...)
	if err != nil {
		return "", err
	}
	var cateName = "黑名单"
	if category == system_configs.WhiteStrategyType {
		cateName = "白名单"
	}
	var dataList = make([][]interface{}, 0)
	for _, v := range list {
		dataList = append(dataList, []interface{}{
			cateName,
			v.IP,
		})
	}
	filePath := time.Now().Format("20060102150405") + "黑白名单列表.xlsx"
	utils.WriterExcel(filePath,
		[]string{
			"黑白名单类型",
			"IP地址",
		},
		dataList,
	)
	return filePath, nil
}
