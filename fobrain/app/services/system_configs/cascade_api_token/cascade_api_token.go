package cascade_api_token

import (
	"fobrain/fobrain/logs"
	"fobrain/models/mysql/system_configs"
	"fobrain/pkg/utils"
)

func CheckCascadeApiToken() {
	key := "cascade_api_token"
	value, err := system_configs.NewSystemConfigs().GetConfig(key)
	if err != nil || value == "" {
		logs.GetLogger().Error("级联Token获取失败")
		token, err := utils.GenerateRandomToken(16)
		if err != nil {
			logs.GetLogger().Error(err)
			return
		}
		err = system_configs.NewSystemConfigs().UpdateConfig(key, token)
		if err != nil {
			logs.GetLogger().Error("级联Token更新失败")
		}
		// 数据库中没有记录key值也不会报错，更新后再次触发查询
		value, err = system_configs.NewSystemConfigs().GetConfig(key)
		if err != nil || value == "" {
			err = system_configs.NewSystemConfigs().CreateConfig(key, token)
			if err != nil {
				logs.GetLogger().Error("级联Token创建失败")
			}
		}
	}
}
