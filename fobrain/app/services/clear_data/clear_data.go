package clear_data

import (
	"context"
	"fmt"
	"fobrain/initialize/es"
	"fobrain/initialize/mysql"
	"fobrain/pkg/cfg"
	"os/exec"
	"strings"
	"sync"
	"time"

	"go-micro.dev/v4/logger"
)

// WorkerPool 工作池配置
type WorkerPool struct {
	Workers   int
	BatchSize int
}

// NewDefaultWorkerPool 创建默认工作池配置
func NewDefaultWorkerPool() *WorkerPool {
	return &WorkerPool{
		Workers:   10, // 默认5个工作协程
		BatchSize: 10, // 每批处理10个
	}
}

// ClearData 清除所有ES索引和MySQL表数据
// 警告：此操作不可逆，请谨慎使用
func ClearData() error {
	var wg sync.WaitGroup
	errCh := make(chan error, 2)

	// 并行执行 ES 和 MySQL 的清理
	wg.Add(2)
	go func() {
		defer wg.Done()
		if err := clearESIndices(); err != nil {
			errCh <- fmt.Errorf("clear ES indices failed: %v", err)
		}
	}()

	go func() {
		defer wg.Done()
		if err := clearMySQLTables(); err != nil {
			errCh <- fmt.Errorf("clear MySQL tables failed: %v", err)
		}
	}()

	// 等待所有操作完成
	wg.Wait()
	close(errCh)

	// 收集错误
	var errs []error
	for err := range errCh {
		errs = append(errs, err)
	}
	if len(errs) > 0 {
		return fmt.Errorf("clear data failed: %v", errs)
	}

	logger.Info("Successfully cleared all data")

	// 重新初始化数据库和索引
	var cmd *exec.Cmd
	if cfg.LoadCommon().Local {
		// 获取项目根目录（从环境变量或配置中）
		rootDir := cfg.GetInstance().Common.StoragePath // 假设配置中有 RootDir
		rootDir = strings.ReplaceAll(rootDir, "storage", "")
		fmt.Println("rootDir", rootDir)
		// 使用绝对路径执行命令
		cmd = exec.Command("sh", "-c", fmt.Sprintf("cd %s && make migrate-run Component=all", rootDir))
	} else {
		cmd = exec.Command("cmd", "migrate", "run")
	}

	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("failed to run migrations: %v, output: %s", err, string(output))
	}
	logger.Info("Successfully reinitialized database and indices")

	return nil
}

// ClearDataWithTimeout 带超时的数据清除
func ClearDataWithTimeout(timeout time.Duration) error {
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	done := make(chan error, 1)
	go func() {
		done <- ClearData()
	}()

	select {
	case err := <-done:
		return err
	case <-ctx.Done():
		return fmt.Errorf("operation timed out after %v", timeout)
	}
}

// clearESIndices 清除所有 ES 索引
func clearESIndices() error {
	pool := NewDefaultWorkerPool()
	client := es.GetEsClient()

	// 获取所有索引
	indices, err := client.IndexNames()
	if err != nil {
		return fmt.Errorf("get ES indices failed: %v", err)
	}

	// 使用工作池并发删除索引
	ch := make(chan []string, (len(indices)+pool.BatchSize-1)/pool.BatchSize)
	var wg sync.WaitGroup
	errCh := make(chan error, len(indices))

	// 启动工作协程
	for i := 0; i < pool.Workers; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			for batch := range ch {
				for _, index := range batch {
					if isProtectedIndex(index) {
						logger.Infof("Skipping protected index: %s", index)
						continue
					}
					if _, err := client.DeleteIndex(index).Do(context.Background()); err != nil {
						errCh <- fmt.Errorf("failed to delete index %s: %v", index, err)
					} else {
						logger.Infof("Successfully deleted ES index: %s", index)
					}
				}
			}
		}()
	}

	// 分批发送任务
	for i := 0; i < len(indices); i += pool.BatchSize {
		end := i + pool.BatchSize
		if end > len(indices) {
			end = len(indices)
		}
		ch <- indices[i:end]
	}
	close(ch)

	// 等待所有工作完成并处理错误
	wg.Wait()
	close(errCh)

	return handleErrors(errCh)
}

// clearMySQLTables 删除所有 MySQL 表
func clearMySQLTables() error {
	pool := NewDefaultWorkerPool()
	db := mysql.GetDbClient()

	// 禁用外键检查
	if err := db.Exec("SET FOREIGN_KEY_CHECKS = 0").Error; err != nil {
		return fmt.Errorf("disable foreign key checks failed: %v", err)
	}
	defer db.Exec("SET FOREIGN_KEY_CHECKS = 1")

	// 获取所有表名
	var tables []string
	if err := db.Raw("SHOW TABLES").Scan(&tables).Error; err != nil {
		return fmt.Errorf("get MySQL tables failed: %v", err)
	}

	// 使用工作池并发删除表
	ch := make(chan []string, (len(tables)+pool.BatchSize-1)/pool.BatchSize)
	var wg sync.WaitGroup
	errCh := make(chan error, len(tables))

	// 启动工作协程
	for i := 0; i < pool.Workers; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			for batch := range ch {
				for _, table := range batch {
					if isProtectedTable(table) {
						logger.Infof("Skipping protected table: %s", table)
						continue
					}
					if err := db.Exec(fmt.Sprintf("DROP TABLE IF EXISTS `%s`", table)).Error; err != nil {
						errCh <- fmt.Errorf("failed to drop table %s: %v", table, err)
					} else {
						logger.Infof("Successfully dropped table: %s", table)
					}
				}
			}
		}()
	}

	// 分批发送任务
	for i := 0; i < len(tables); i += pool.BatchSize {
		end := i + pool.BatchSize
		if end > len(tables) {
			end = len(tables)
		}
		ch <- tables[i:end]
	}
	close(ch)

	// 等待所有工作完成并处理错误
	wg.Wait()
	close(errCh)

	return handleErrors(errCh)
}

// isProtectedIndex 检查是否是受保护的索引
func isProtectedIndex(index string) bool {
	// 添加需要保护的索引名称
	protectedIndices := []string{
		// 添加其他需要保护的索引
	}

	for _, protected := range protectedIndices {
		if index == protected {
			return true
		}
	}
	return false
}

// isProtectedTable 检查是否是受保护的表
func isProtectedTable(table string) bool {
	// 添加需要保护的表名称
	protectedTables := []string{
		// 添加其他需要保护的表
	}

	for _, protected := range protectedTables {
		if table == protected {
			return true
		}
	}
	return false
}

// handleErrors 处理错误通道中的错误
func handleErrors(errCh chan error) error {
	var errs []error
	for err := range errCh {
		errs = append(errs, err)
	}
	if len(errs) > 0 {
		return fmt.Errorf("operation failed with %d errors: %v", len(errs), errs)
	}
	return nil
}
