package clear_data

import (
	"fmt"
	testcommon "fobrain/fobrain/tests/common_test"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/olivere/elastic/v7"
	"github.com/stretchr/testify/assert"
)

func TestClearESIndices(t *testing.T) {
	// 设置 mock ES server
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	// 模拟 _all/_settings 响应
	mockServer.Register("_all/_settings", map[string]interface{}{
		"test_index1": map[string]interface{}{
			"settings": map[string]interface{}{
				"index": map[string]interface{}{
					"number_of_shards": "1",
				},
			},
		},
		"test_index2": map[string]interface{}{
			"settings": map[string]interface{}{
				"index": map[string]interface{}{
					"number_of_shards": "1",
				},
			},
		},
	})

	// 模拟 ES 索引列表响应
	mockServer.Register("_cat/indices?format=json", []map[string]interface{}{
		{"index": "test_index1", "health": "green", "status": "open"},
		{"index": "test_index2", "health": "green", "status": "open"},
	})

	// 模拟删除索引的响应
	mockServer.Register("test_index1", elastic.IndicesDeleteResponse{Acknowledged: true})
	mockServer.Register("test_index2", elastic.IndicesDeleteResponse{Acknowledged: true})

	// 执行测试
	err := clearESIndices()
	assert.NoError(t, err)
}

func TestClearMySQLTables(t *testing.T) {
	// 设置 mock MySQL
	mockDB := testcommon.InitSqlMock()
	defer mockDB.Close()

	// 设置期望
	mockDB.ExpectExec("SET FOREIGN_KEY_CHECKS = 0").WillReturnResult(sqlmock.NewResult(0, 0))

	rows := sqlmock.NewRows([]string{"Tables_in_database"}).
		AddRow("cascade_sync_records").
		AddRow("data_nodes")
	mockDB.ExpectQuery("SHOW TABLES").WillReturnRows(rows)

	mockDB.ExpectExec("DROP TABLE IF EXISTS `cascade_sync_records`").WillReturnResult(sqlmock.NewResult(0, 0))
	mockDB.ExpectExec("DROP TABLE IF EXISTS `data_nodes`").WillReturnResult(sqlmock.NewResult(0, 0))

	mockDB.ExpectExec("SET FOREIGN_KEY_CHECKS = 1").WillReturnResult(sqlmock.NewResult(0, 0))

	// 执行测试
	err := clearMySQLTables()
	assert.NoError(t, err)

	// 验证所有 SQL 期望都被执行
	assert.NoError(t, mockDB.ExpectationsWereMet())
}

func TestClearMySQLTablesWithError(t *testing.T) {
	// 设置 mock MySQL
	mockDB := testcommon.InitSqlMock()
	defer mockDB.Close()

	// 模拟外键检查禁用失败
	mockDB.ExpectExec("SET FOREIGN_KEY_CHECKS = 0").WillReturnError(fmt.Errorf("database error"))

	// 执行测试
	err := clearMySQLTables()
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "disable foreign key checks failed")
}

func TestClearESIndicesWithError(t *testing.T) {
	// 设置 mock ES server
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	// 模拟获取索引列表失败，返回一个错误响应
	mockServer.Register("_cat/indices?format=json", map[string]interface{}{
		"error": map[string]interface{}{
			"type":   "index_not_found_exception",
			"reason": "failed to get indices",
		},
		"status": 404,
	})

	// 执行测试
	err := clearESIndices()
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "get ES indices failed")
}
