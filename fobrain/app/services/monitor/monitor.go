package monitor

import (
	"fobrain/fobrain/logs"
	"fobrain/initialize/mysql"
	"fobrain/models/mysql/monitor_asset"
	"time"
)

// 新建规则
func InitMonitor(req *monitor_asset.CreateReq) error {
	errCreate := monitor_asset.NewMonModel().Create(req)
	if errCreate != nil {
		logs.GetLogger().Errorf("insert monitor database err:%s", errCreate)
		return errCreate
	}
	return nil
}

// 查询单次任务数据
func QueryMonitor(req *monitor_asset.ListReq) (any, int64, error) {
	// 构造查询条件
	var opts []mysql.HandleFunc
	opts = append(opts, mysql.WithWhere("period", "0"))
	if req.Name != "" {
		opts = append(opts, mysql.WithWhere("name", req.Name))
	}
	// 列表查询
	tasks, total, errQuery := monitor_asset.NewMonModel().List(req.Page, req.PerPage, opts...)
	if errQuery != nil {
		logs.GetLogger().Errorf("query monitor database err:%s", errQuery)
		return nil, 0, errQuery
	}

	listResp := make([]*monitor_asset.ListResp, 0)
	// 整理数据
	for _, task := range tasks {
		var resp monitor_asset.ListResp
		taskInfo, err := monitor_asset.NewMonTaskModel().QueryAssetById(int(task.Id))
		if err != nil && !mysql.IsNotFound(err) {
			return nil, 0, err
		} else if mysql.IsNotFound(err) {
			resp.Elapsed = 0
			resp.EndAt = time.Time{}.Format(monitor_asset.TimeFormat)
		} else {
			endTime := taskInfo.CreatedAt.Add(time.Duration(taskInfo.Elapsed) * time.Second)
			resp.Elapsed = int(taskInfo.Elapsed)
			resp.EndAt = endTime.Format(monitor_asset.TimeFormat)
		}
		resp.Id = task.Id
		resp.Name = task.Name
		resp.Desc = task.Desc
		resp.Event = task.Event
		resp.Status = task.Status
		resp.StartAt = task.LastTime.Format(monitor_asset.TimeFormat)
		resp.LastAbnormalAsset = task.LastAbnormalAsset
		resp.AdminName = task.AdminName

		listResp = append(listResp, &resp)
	}
	return listResp, total, nil
}

// 查询周期任务数据
func QueryPeriodMonitor(req *monitor_asset.ListReq) (any, int64, error) {
	// 构造查询条件
	var opts []mysql.HandleFunc
	opts = append(opts, mysql.WithWhere("period", "1"))
	if req.Name != "" {
		opts = append(opts, mysql.WithWhere("name", req.Name))
	}
	// 列表查询
	tasks, total, errQuery := monitor_asset.NewMonModel().List(req.Page, req.PerPage, opts...)
	if errQuery != nil {
		logs.GetLogger().Errorf("query period monitor database err:%s", errQuery)
		return nil, 0, errQuery
	}

	ListPeriodResp := make([]*monitor_asset.ListPeriodResp, 0)
	// 整理数据
	for _, task := range tasks {
		resp := &monitor_asset.ListPeriodResp{
			Id:                task.Id,
			Name:              task.Name,
			Desc:              task.Desc,
			Event:             task.Event,
			LastTime:          task.LastTime.Format(monitor_asset.TimeFormat),
			LastAbnormalAsset: task.LastAbnormalAsset,
			Time:              task.Time,
			AdminName:         task.AdminName,
		}
		ListPeriodResp = append(ListPeriodResp, resp)
	}
	return ListPeriodResp, total, nil
}

// 删除数据
func DeleteMonitor(ids []int) error {
	errDelete := monitor_asset.NewMonModel().DeleteMonAssetById(ids)
	if errDelete != nil {
		logs.GetLogger().Errorf("delete monitor database err:%s", errDelete)
		return errDelete
	}
	return nil
}

// 更新数据
func UpdateMonitor(req *monitor_asset.UpdateReq) error {
	// 执行更新操作
	errUpdate := monitor_asset.NewMonModel().UpdateMonAssetById(req)
	if errUpdate != nil {
		logs.GetLogger().Errorf("update monitor database err:%s", errUpdate)
		return errUpdate
	}
	return nil
}

// 查询规则
func RuleListMonitor(req *monitor_asset.IDReq) (any, error) {
	rules, mon, errList := monitor_asset.NewMonModel().RuleListById(req.Id)
	if errList != nil {
		logs.GetLogger().Errorf("query rule err:%s", errList)
		return nil, errList
	}
	list := &monitor_asset.ParamResp{
		Name:      mon.Name,
		Desc:      mon.Desc,
		Rule:      rules,
		AssetType: mon.AssetType,
		Event:     mon.Event,
		Date:      mon.Date,
		Time:      mon.Time,
		Notices:   mon.Notices,
	}
	return list, nil
}

// 周期任务开关
func SwitchMonitor(req *monitor_asset.SwitchReq) error {
	errSwitch := monitor_asset.NewMonModel().TaskSet(req.Id, req.Flag, 0)
	if errSwitch != nil {
		logs.GetLogger().Errorf("switch task err:%s", errSwitch)
		return errSwitch
	}
	return nil
}
