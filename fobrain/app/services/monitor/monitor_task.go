package monitor

import (
	"encoding/json"
	"errors"
	"fmt"
	"fobrain/fobrain/logs"
	"fobrain/initialize/mysql"
	"fobrain/models/elastic/assets"
	"fobrain/models/mysql/monitor_asset"
	"fobrain/pkg/utils"
	"strings"
	"time"
)

// 查看资产列表
func QueryAsset(TaskID int) (any, int64, error) {
	var opts []mysql.HandleFunc
	if TaskID > 0 {
		// 获取当前任务上次的执行时间
		latest, err := monitor_asset.NewMonTaskModel().LatestTine(TaskID)
		if err != nil {
			return nil, 0, err
		}
		opts = append(opts, mysql.WithWhere("created_at", latest))
		opts = append(opts, mysql.WithWhere("task_id", TaskID))
	}

	assetList, total, errList := monitor_asset.NewMonTaskModel().ListAsset(0, 0, opts...)
	if errList != nil {
		logs.GetLogger().Errorf("list monitor database err:%s", errList)
		return nil, 0, errList
	}
	return assetList, total, nil
}

func Details(req *monitor_asset.AssetListRequest) (any, int, error) {
	var (
		pagelist []*assets.MonitorAsset
		size     = req.PerPage
		cur      = req.Cursor
	)
	if cur == -1 {
		return nil, 0, nil
	}

	// 查询所有数据
	list, _, err := QueryAsset(req.TaskID)
	if err != nil {
		return nil, 0, err
	}
	count := monitor_asset.NewMonTaskModel().CountAsset(list.([]*assets.MonitorAsset))
	paramList, err := RuleListMonitor(&monitor_asset.IDReq{
		Id: req.TaskID,
	})
	if err != nil {
		return nil, 0, err
	}

	// 基于游标进行分页
	length := len(list.([]*assets.MonitorAsset))
	if cur+size >= length && cur < length {
		pagelist = list.([]*assets.MonitorAsset)[cur:length]
		cur = -1
	} else if cur+size < length {
		pagelist = list.([]*assets.MonitorAsset)[cur : cur+size]
		cur = list.([]*assets.MonitorAsset)[len(list.([]*assets.MonitorAsset))-1].Id
	} else {
		return nil, 0, nil
	}

	detailslist := &monitor_asset.DetailResp{
		AssetList: pagelist,
		Count:     count,
		Param:     paramList.(*monitor_asset.ParamResp),
		Cursor:    cur,
	}
	return detailslist, len(pagelist), nil
}

// 根据id删除对应资产记录
func DeleteAsset(id int) error {
	errDelete := monitor_asset.NewMonTaskModel().DeleteAssetById(id)
	if errDelete != nil {
		logs.GetLogger().Errorf("delete  monitor database err:%s", errDelete)
		return errDelete
	}
	return nil
}

// 导出资产记录
func ExportExcel(exp *monitor_asset.AssetExport) (string, error) {
	var filePath string
	var datum [][]interface{}
	var header []string
	var total int

	// 获取待导出的数据
	for _, assetId := range exp.Ids {
		var opts []mysql.HandleFunc
		// id >= 0时表示导出选中id的数据; id= -1表示导出全部
		if assetId >= 0 {
			opts = append(opts, mysql.WithWhere("id", assetId))
		} else {
			// id < 0时表示导出本次任务的全部数据
			latest, err := monitor_asset.NewMonTaskModel().LatestTine(exp.TaskID)
			if err != nil {
				return "", err
			}
			opts = append(opts, mysql.WithWhere("created_at", latest))
		}
		opts = append(opts, mysql.WithWhere("task_id", exp.TaskID))
		assetList, _, errList := monitor_asset.NewMonTaskModel().ListAsset(0, 0, opts...)
		if errList != nil {
			logs.GetLogger().Errorf("list asset database err:%s", errList)
		}
		total += len(assetList)

		// 设置表头
		header = []string{
			"IP地址",
			"端口",
			"协议",
			"组件信息",
			"所属设备",
			"业务系统",
			"业务系统负责人",
			"运维负责人",
		}
		// 存储查询出的数据
		for _, asset := range assetList {
			datum = append(datum, []interface{}{
				asset.Ip,
				string(asset.Port)[1 : len(string(asset.Port))-1],
				StringDeal(asset.Protocol),
				StringDeal(asset.Product),
				asset.Device,
				StringDeal(asset.Business),
				StringDeal(asset.BusinessOwner),
				StringDeal(asset.OperationsOwner),
			})
		}
	}

	if len(datum) == 0 {
		return "", errors.New("导出的数据不存在")
	}

	// 生成文件路径
	filePath = time.Now().Format("20060102150405") + "-资产详情.xlsx"
	time1 := time.Now()
	logs.GetLogger().Info("开始导出数据")
	if _, err := utils.WriterExcel(filePath, header, datum); err != nil {
		logs.GetLogger().Errorf("导出数据失败", err)
		return "", err
	}
	str := fmt.Sprintf("成功导出%d条数据，共耗时%f秒", total, time.Since(time1).Seconds())

	return str, nil
}

// 输出字符串格式化处理
func StringDeal(data json.RawMessage) string {
	// 去掉两头的[]以及所有的"
	str := string(data)[1 : len(string(data))-1]
	newStr := strings.ReplaceAll(str, "\"", "")
	return newStr
}
