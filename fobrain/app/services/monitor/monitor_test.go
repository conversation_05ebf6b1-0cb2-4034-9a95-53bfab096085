package monitor

import (
	"encoding/json"
	"fobrain/fobrain/common/request"
	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/models/mysql/monitor_asset"
	"github.com/stretchr/testify/assert"
	"testing"
	"time"
)

func TestQueryMonitor(t *testing.T) {
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()
	createdAtTime, _ := time.Parse("2006-01-02 15:04:05", "2024-08-29 12:00:00")

	mockDb.ExpectQuery("SELECT count(*) FROM `monitors`").
		WillReturnRows(mockDb.NewRows([]string{"count"}).AddRow(1))

	mockDb.ExpectQuery("SELECT * FROM `monitors` WHERE `period` = ? AND `name` = ? AND deleted_at IS NULL AND `monitors`.`deleted_at` IS NULL ORDER BY status asc, id asc LIMIT 20").
		WithArgs("0", "111aaa").
		WillReturnRows(mockDb.NewRows([]string{"id", "name", "desc", "event", "status", "admin_name", "last_time"}).
			AddRow(1, "111aaa", "111", "fast", 0, "admin", createdAtTime))

	mockDb.ExpectQuery("SELECT `created_at` FROM `monitor_task_ips` WHERE task_id = ? AND `monitor_task_ips`.`deleted_at` IS NULL ORDER BY created_at DESC,`monitor_task_ips`.`id` LIMIT 1").
		WithArgs(1).
		WillReturnRows(mockDb.NewRows([]string{"created_at"}).AddRow(createdAtTime))

	mockDb.ExpectQuery("SELECT SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) AS last_normal_asset, SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END) AS last_abnormal_asset, COUNT(*) AS last_asset, MAX(elapsed) AS elapsed FROM `monitor_task_ips` WHERE task_id = ? AND `monitor_task_ips`.`deleted_at` IS NULL AND (task_id = ? AND created_at = ?) ORDER BY created_at DESC,`monitor_task_ips`.`id` LIMIT 1").
		WithArgs(1, 1, createdAtTime).
		WillReturnRows(mockDb.NewRows([]string{"last_normal_asset", "last_abnormal_asset", "last_asset", "elapsed"}).AddRow(5, 0, 5, 1.64))

	// 构造函数调用的参数
	req := &monitor_asset.ListReq{
		PageRequest: request.PageRequest{
			Page:    1,
			PerPage: 20,
		},
		Name:   "111aaa",
		Period: "0",
	}

	// 调用函数
	result, total, err := QueryMonitor(req)

	assert.NoError(t, err)
	assert.Equal(t, int64(1), total)
	assert.Len(t, result, 1)

	resp := result.([]*monitor_asset.ListResp)[0]
	assert.Equal(t, int64(1), int64(resp.Id))
	assert.Equal(t, "111aaa", resp.Name)
	assert.Equal(t, "111", resp.Desc)
	assert.Equal(t, "fast", resp.Event)
	assert.Equal(t, 0, resp.Status)
	assert.Equal(t, "admin", resp.AdminName)
	assert.Equal(t, "2024-08-29 12:00:00", resp.StartAt)
	assert.Equal(t, "2024-08-29 12:00:01", resp.EndAt)
	assert.Equal(t, 1, resp.Elapsed)
	assert.Equal(t, 0, int(resp.LastAbnormalAsset))
}

func TestQueryPeriodMonitor(t *testing.T) {
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()
	createdAtTime, _ := time.Parse("2006-01-02 15:04:05", "2024-08-29 12:00:00")

	mockDb.ExpectQuery("SELECT count(*) FROM `monitors`").
		WillReturnRows(mockDb.NewRows([]string{"count"}).AddRow(1))

	mockDb.ExpectQuery("SELECT * FROM `monitors` WHERE `period` = ? AND `name` = ? AND deleted_at IS NULL AND `monitors`.`deleted_at` IS NULL ORDER BY status asc, id asc LIMIT 20").
		WithArgs("1", "222aaa").
		WillReturnRows(mockDb.NewRows([]string{"id", "name", "desc", "event", "time", "admin_name", "last_time"}).
			AddRow(2, "222aaa", "222", "day", "12:00", "admin", createdAtTime))

	mockDb.ExpectQuery("SELECT `created_at` FROM `monitor_task_ips` WHERE task_id = ? AND `monitor_task_ips`.`deleted_at` IS NULL ORDER BY created_at DESC,`monitor_task_ips`.`id` LIMIT 1").
		WithArgs(2).
		WillReturnRows(mockDb.NewRows([]string{"created_at"}).AddRow(createdAtTime))

	mockDb.ExpectQuery("SELECT SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) AS last_normal_asset, SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END) AS last_abnormal_asset, COUNT(*) AS last_asset, MAX(elapsed) AS elapsed FROM `monitor_task_ips` WHERE task_id = ? AND `monitor_task_ips`.`deleted_at` IS NULL AND (task_id = ? AND created_at = ?) ORDER BY created_at DESC,`monitor_task_ips`.`id` LIMIT 1").
		WithArgs(2, 2, createdAtTime).
		WillReturnRows(mockDb.NewRows([]string{"last_normal_asset", "last_abnormal_asset", "last_asset", "elapsed"}).AddRow(5, 0, 5, 1.64))

	// 构造函数调用的参数
	req := &monitor_asset.ListReq{
		PageRequest: request.PageRequest{
			Page:    1,
			PerPage: 20,
		},
		Name:   "222aaa",
		Period: "1",
	}

	// 调用函数
	result, total, err := QueryPeriodMonitor(req)

	assert.NoError(t, err)
	assert.Equal(t, int64(1), total)
	assert.Len(t, result, 1)

	resp := result.([]*monitor_asset.ListPeriodResp)[0]
	assert.Equal(t, "222aaa", resp.Name)
	assert.Equal(t, "222", resp.Desc)
	assert.Equal(t, "day", resp.Event)
	assert.Equal(t, "12:00", resp.Time)
	assert.Equal(t, "2024-08-29 12:00:00", resp.LastTime)
	assert.Equal(t, "admin", resp.AdminName)
	assert.Equal(t, 0, int(resp.LastAbnormalAsset))
}

func TestRuleListMonitor(t *testing.T) {
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()

	mockDb.ExpectQuery("SELECT * FROM `monitors` WHERE `monitors`.`id` = ? AND `monitors`.`deleted_at` IS NULL ORDER BY `monitors`.`id` LIMIT 1").
		WithArgs(1).
		WillReturnRows(mockDb.NewRows([]string{"name", "desc", "rule", "asset_type", "event", "date", "time", "notices"}).
			AddRow("111", "111", "[{\"id\":\"1\",\"name\":\"port\",\"op\":\"in\",\"value\":\"111\",\"logic\":\"\"}]", 0, "week", 8, "10:00", "{\"is_notice\":false,\"notice_obj\":[]}"))

	req := &monitor_asset.IDReq{
		Id: 1,
	}

	// 调用服务层函数
	result, err := RuleListMonitor(req)

	// 断言结果和错误
	assert.NoError(t, err)

	expectedResult := &monitor_asset.ParamResp{
		Name:      "111",
		Desc:      "111",
		Rule:      []*monitor_asset.Rule{{Id: "1", Name: "port", Op: "in", Value: "111", Logic: ""}},
		AssetType: 0,
		Event:     "week",
		Date:      "8",
		Time:      "10:00",
		Notices:   `{"is_notice":false,"notice_obj":[]}`,
	}

	assert.Equal(t, expectedResult, result)
}

// TestStringDeal 测试 StringDeal 函数
func TestStringDeal(t *testing.T) {
	tests := []struct {
		name     string
		input    json.RawMessage
		expected string
	}{
		{
			name:     "正常JSON数组字符串",
			input:    json.RawMessage(`["value1","value2","value3"]`),
			expected: "value1,value2,value3",
		},
		{
			name:     "单个值的JSON数组",
			input:    json.RawMessage(`["single_value"]`),
			expected: "single_value",
		},
		{
			name:     "空JSON数组",
			input:    json.RawMessage(`[]`),
			expected: "",
		},
		{
			name:     "包含特殊字符的JSON数组",
			input:    json.RawMessage(`["value with spaces","value-with-dashes","value_with_underscores"]`),
			expected: "value with spaces,value-with-dashes,value_with_underscores",
		},
		{
			name:     "包含数字的JSON数组",
			input:    json.RawMessage(`["80","443","8080"]`),
			expected: "80,443,8080",
		},
		{
			name:     "混合内容的JSON数组",
			input:    json.RawMessage(`["http","https","tcp","80"]`),
			expected: "http,https,tcp,80",
		},
		{
			name:     "包含中文的JSON数组",
			input:    json.RawMessage(`["业务系统","测试系统"]`),
			expected: "业务系统,测试系统",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := StringDeal(tt.input)
			assert.Equal(t, tt.expected, result, "StringDeal函数返回值不符合预期")
		})
	}
}
