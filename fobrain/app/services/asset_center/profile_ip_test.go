package assetcenter

import (
	"encoding/json"
	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/initialize/mysql"
	"fobrain/models/elastic/assets"
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/network_areas"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/olivere/elastic/v7"
	"github.com/stretchr/testify/assert"
)

func TestMerge_IpBaseInfo(t *testing.T) {
	asset := &assets.Assets{
		Id:                "1",
		Ip:                "***********",
		IpType:            1,
		Status:            1,
		IsDeviceExtracted: 1,
		Area:              1,
		AllSourceIds:      []uint64{1, 2},
		HostName:          []string{"host1"},
		Os:                []string{"Linux"},
		Mac:               []string{"00:11:22:33:44:55"},
		EthName:           []string{"eth0"},
		RuleInfos:         []*assets.RuleInfo{{Product: "product1"}},
		Ports:             []*assets.PortInfo{{Domain: "example.com"}},
	}
	assetJson, _ := json.Marshal(asset)

	// Mock es.GetById
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	mockServer.Register("asset/_doc/1", &elastic.GetResult{
		Id:     "1",
		Source: assetJson,
		Found:  true,
	})

	mockServer.Register("device/_search", []*elastic.SearchHit{
		{
			Id:     "1",
			Source: []byte(`{"id": "1", "sn": ["sn1"], "mac": ["00:11:22:33:44:55"], "hostname": ["host1"]}`),
		},
	})
	mockServer.RegisterEmptyScrollHandler()

	// 模拟 network_areas.NewNetworkAreaModel().First
	areaMock := gomonkey.ApplyMethodReturn(network_areas.NewNetworkAreaModel(), "First", network_areas.NetworkArea{
		BaseModel: mysql.BaseModel{Id: 1},
		Name:      "Area1",
	}, nil)
	defer areaMock.Reset()

	// 模拟 data_source.NewSourceModel().SourceNames
	sourceMock := gomonkey.ApplyMethodReturn(data_source.NewSourceModel(), "SourceNames", []*data_source.Source{
		{BaseModel: mysql.BaseModel{Id: 1}, Name: "Source1", Icon: "icon1"},
		{BaseModel: mysql.BaseModel{Id: 2}, Name: "Source2", Icon: "icon2"},
	})
	defer sourceMock.Reset()

	mockDb := testcommon.InitSqlMock()
	mockDb.ExpectQuery("SELECT count(*) FROM `net_mappings` WHERE from_area LIKE ? OR data_source LIKE ? OR from_ip LIKE ? OR from_port LIKE ? OR from_domain LIKE ? OR from_url LIKE ? OR from_protocol LIKE ? OR to_area LIKE ? OR to_ip LIKE ? OR to_port LIKE ? OR to_domain LIKE ? OR to_url LIKE ? OR to_protocol LIKE ?").
		WithArgs("%***********%", "%***********%", "%***********%", "%***********%", "%***********%", "%***********%", "%***********%", "%***********%", "%***********%", "%***********%", "%***********%", "%***********%", "%***********%").
		WillReturnRows(sqlmock.NewRows([]string{"count(*)"}).AddRow(0))
	defer mockDb.Close()

	rsp, err := GetIpBaseInfo("1")

	assert.NoError(t, err)
	assert.Equal(t, "1", rsp.Id)
	assert.Equal(t, "***********", rsp.Ip)
	assert.Equal(t, "IPv4", rsp.IpType)
	assert.Equal(t, int32(1), rsp.Status)
	assert.Equal(t, uint64(1), rsp.AreaId)
	assert.Equal(t, "Area1", rsp.Area.Name)
	assert.Equal(t, uint64(1), rsp.SourceId[0])
	assert.Equal(t, "Source1", rsp.Source[0].Name)
	assert.Equal(t, "Source2", rsp.Source[1].Name)
	assert.Equal(t, "host1", rsp.Hostname[0])
	assert.Equal(t, "Linux", rsp.Os[0])
	assert.Equal(t, "00:11:22:33:44:55", rsp.Mac[0])
	assert.Equal(t, "eth0", rsp.EthName[0])
	assert.Equal(t, "product1", rsp.Product[0])
	assert.Equal(t, "example.com", rsp.Domain[0])
	assert.Len(t, rsp.Device, 1)
	assert.Equal(t, "1", rsp.Device[0].Id)
}
