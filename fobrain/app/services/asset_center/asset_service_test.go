package assetcenter

import (
	"encoding/json"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/olivere/elastic/v7"
	"github.com/stretchr/testify/assert"

	testcommon "fobrain/fobrain/tests/common_test"
)

func TestGetAssetsIpByBusinessIds(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	mockServer.Register("/asset/_search", elastic.SearchResult{
		ScrollId: "scroll-id-2",
		Hits: &elastic.SearchHits{
			TotalHits: &elastic.TotalHits{
				Value:    2,
				Relation: "eq",
			},
			Hits: []*elastic.SearchHit{
				{
					Id:     "1",
					Source: json.RawMessage(`{"business": [{"system": "业务系统", "system_id": "1", "owner": "张三", "owner_id": "1", "department": ["部门负责人"], "source": "rule"}]}`),
				},
				{
					Id:     "2",
					Source: json.RawMessage(`{"business": [{"system": "业务系统", "system_id": "1", "owner": "张三", "owner_id": "1", "department": ["部门负责人"], "source": "rule"}], "network_type": 1}`),
				},
			},
		},
	})
	mockServer.RegisterEmptyScrollHandler()

	_ = GetAssetsIpByBusinessIds([]string{"1"})
}

func TestGetAssetsIpByBusinessId(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	mockServer.Register("/asset/_search", elastic.SearchResult{
		ScrollId: "scroll-id-2",
		Hits: &elastic.SearchHits{
			TotalHits: &elastic.TotalHits{
				Value:    2,
				Relation: "eq",
			},
			Hits: []*elastic.SearchHit{
				{
					Id:     "1",
					Source: json.RawMessage(`{"business": [{"system": "业务系统", "system_id": "1", "owner": "张三", "owner_id": "1", "department": ["部门负责人"], "source": "rule"}]}`),
				},
				{
					Id:     "2",
					Source: json.RawMessage(`{"business": [{"system": "业务系统", "system_id": "1", "owner": "张三", "owner_id": "1", "department": ["部门负责人"], "source": "rule"}], "ip_type": 1}`),
				},
			},
		},
	})
	mockServer.RegisterEmptyScrollHandler()

	_, _ = GetAssetsIpByBusinessId("1")
}

func TestPurgeAsset(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	mockDb := testcommon.GetMysqlMock()
	defer mockServer.Close()
	defer mockDb.Close()
	mockDb.ExpectQuery("SELECT count(*) FROM `merge_records` WHERE `task_status` = ?").
		WithArgs("执行中").WillReturnRows(mockDb.NewRows([]string{"count"}).AddRow(0))
	mockDb.ExpectBegin()
	mockDb.ExpectExec("DELETE FROM `merge_asset_blacklist` WHERE count = ? AND source_id = ? AND ip = ? AND area = ?").
		WithArgs(1, 1, "127.0.0.1", "1").
		WillReturnResult(sqlmock.NewResult(1, 1))
	mockDb.ExpectCommit()

	mockServer.Register("/asset/_search", elastic.SearchResult{
		ScrollId: "scroll-id-2",
		Hits: &elastic.SearchHits{
			TotalHits: &elastic.TotalHits{
				Value:    2,
				Relation: "eq",
			},
			Hits: []*elastic.SearchHit{
				{
					Id:     "1",
					Source: json.RawMessage(`{"source_ids": [1], "ip": "127.0.0.1", "area": 1}`),
				},
				{
					Id:     "2",
					Source: json.RawMessage(`{"source_ids": [2, 3], "ip": "127.0.0.1", "area": 1}`),
				},
			},
		},
	})
	mockServer.RegisterEmptyScrollHandler()
	mockServer.Register("/asset/_delete_by_query", &elastic.BulkIndexByScrollResponse{
		Deleted: 1,
	})
	mockServer.Register("/process_asset/_delete_by_query", &elastic.BulkIndexByScrollResponse{
		Deleted: 1,
	})
	mockServer.Register("/asset_record/_delete_by_query", &elastic.BulkIndexByScrollResponse{
		Deleted: 1,
	})
	mockServer.Register("/asset_merge_record/_delete_by_query", &elastic.BulkIndexByScrollResponse{
		Deleted: 1,
	})

	err := PurgeAsset(nil, []string{"1", "2"}, 1, "", []string{})
	assert.Nil(t, err)
}

func TestAddAssetToBlacklist(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	mockDb := testcommon.GetMysqlMock()
	defer mockServer.Close()
	defer mockDb.Close()

	mockServer.Register("/asset/_search", elastic.SearchResult{
		ScrollId: "scroll-id-2",
		Hits: &elastic.SearchHits{
			TotalHits: &elastic.TotalHits{
				Value:    2,
				Relation: "eq",
			},
			Hits: []*elastic.SearchHit{
				{
					Id:     "1",
					Source: json.RawMessage(`{"source_ids": [1], "ip": "127.0.0.1", "area": 1}`),
				},
				{
					Id:     "2",
					Source: json.RawMessage(`{"source_ids": [2, 3], "ip": "127.0.0.1", "area": 1}`),
				},
			},
		},
	})
	mockServer.RegisterEmptyScrollHandler()

	mockDb.ExpectBegin()
	mockDb.ExpectExec("INSERT INTO `merge_asset_blacklist` (`source_id`,`ip`,`area`,`count`,`network_type`) values (?,?,?,?,?) ON DUPLICATE KEY UPDATE `count` = `count` + 1").
		WillReturnResult(sqlmock.NewResult(1, 1))
	mockDb.ExpectCommit()

}

func TestPurgeAsset_Real(t *testing.T) {
	// testcommon.SetTestEnv(false)
	// cfg.InitLoadCfg()
	// // es插入数据
	// bulkSercice := es.GetEsClient().Bulk().Refresh("true")
	// assetIndexName := esmodel.NewAssets().IndexName()
	// processIndexName := esmodel.NewProcessAssetsModel().IndexName()
	// recordIndexName := esmodel.NewAssetRecord().IndexName()
	// blacklistColl := make([]*dbmodel.MergeAssetBlacklist, 0, 5000)
	// ids := make([]string, 0, 5000)
	// for i := 0; i < 5000; i++ {
	// 	id := uuid.New().String()
	// 	ids = append(ids, id)
	// 	bulkSercice.Add(elastic.NewBulkCreateRequest().Index(assetIndexName).Ids(id).Doc(map[string]interface{}{
	// 		"id":           id,
	// 		"ip":           "*******",
	// 		"area":         i,
	// 		"source_ids":   []uint64{1},
	// 		"network_type": 1,
	// 		"deleted_at":   time.Now().Format(time.DateTime),
	// 	}))
	// }
	// _, err := bulkSercice.Do(context.Background())
	// if err != nil {
	// 	t.Fatalf("PurgeAsset failed, err: %v\n", err)
	// }

	// bulkSercice.Reset()
	// // 插入processIndexName数据
	// for i := 0; i < 5000; i++ {
	// 	id := uuid.New().String()
	// 	bulkSercice.Add(elastic.NewBulkCreateRequest().Index(processIndexName).Ids(id).Doc(map[string]interface{}{
	// 		"id":           id,
	// 		"ip":           "*******",
	// 		"area":         i,
	// 		"source_ids":   []uint64{1},
	// 		"network_type": 1,
	// 	}))
	// }
	// _, err = bulkSercice.Do(context.Background())
	// if err != nil {
	// 	t.Fatalf("PurgeAsset failed, err: %v\n", err)
	// }
	// bulkSercice.Reset()
	// // 插入recordIndexName数据
	// for i := 0; i < 5000; i++ {
	// 	id := uuid.New().String()
	// 	bulkSercice.Add(elastic.NewBulkCreateRequest().Index(recordIndexName).Ids(id).Doc(map[string]interface{}{
	// 		"id":           id,
	// 		"ip":           "*******",
	// 		"area":         i,
	// 		"source_ids":   []uint64{1},
	// 		"network_type": 1,
	// 	}))
	// 	// 批量插入数据库黑名单表
	// 	blacklistColl = append(blacklistColl, &dbmodel.MergeAssetBlacklist{
	// 		Ip:          "*******",
	// 		Area:        fmt.Sprintf("%d", i),
	// 		SourceId:    1,
	// 		NetworkType: 1,
	// 	})
	// }
	// // 批量插入数据库黑名单表
	// err = dbmodel.NewMergeAssetBlacklistModel().BatchInsert(blacklistColl)
	// if err != nil {
	// 	t.Fatalf("PurgeAsset failed, err: %v\n", err)
	// }

	// // 彻底删除资产
	// err = PurgeAsset(nil, 1)
	// if err != nil {
	// 	t.Fatalf("PurgeAsset failed, err: %v\n", err)
	// }
	// // 检查es数据是否被删除
	// // 获取一个随机id
	// id := ids[rand.Intn(len(ids))]
	// query := elastic.NewBoolQuery().Must(elastic.NewTermQuery("id", id))
	// searchResult, err := es.GetEsClient().Search().Index(assetIndexName).Query(query).Do(context.Background())
	// if err != nil {
	// 	t.Fatalf("PurgeAsset failed, err: %v\n", err)
	// }
	// if searchResult.Hits.TotalHits.Value > 0 {
	// 	t.Fatalf("PurgeAsset failed, data not deleted\n")
	// }
	// // 检查数据库黑名单表数据是否被删除
	// data, err := dbmodel.NewMergeAssetBlacklistModel().First(1, "*******", "1")
	// if err != nil {
	// 	t.Fatalf("PurgeAsset failed, err: %v\n", err)
	// }
	// if data != nil {
	// 	t.Fatalf("PurgeAsset failed, data not deleted\n")
	// }
}
