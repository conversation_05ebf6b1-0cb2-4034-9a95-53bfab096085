package assetcenter

import (
	"fobrain/fobrain/logs"
	"fobrain/initialize/es"
	"fobrain/initialize/mysql"
	"fobrain/models/elastic/assets"
	"fobrain/models/elastic/device"
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/net_mapping"
	"fobrain/models/mysql/network_areas"
	"sync"

	"github.com/olivere/elastic/v7"
	"github.com/spf13/cast"
)

// IpBaseInfoResponse IP基础信息响应结构
type IpBaseInfoResponse struct {
	Id          string                         `json:"id"`
	Ip          string                         `json:"ip"`
	AreaId      uint64                         `json:"area_id"`
	Area        *AreaDto                       `json:"area"`
	SourceId    []uint64                       `json:"source_id"`
	Source      []*SourceDto                   `json:"source"`
	Hostname    []string                       `json:"hostname"`
	Os          []string                       `json:"os"`
	Mac         []string                       `json:"mac"`
	Sn          []string                       `json:"sn"`
	PublicIp    []string                       `json:"public_ip"`
	Device      []*DeviceItemDto               `json:"device"`
	Status      int32                          `json:"status"`
	IpType      string                         `json:"ip_type"`
	Domain      []string                       `json:"domain"`
	EthName     []string                       `json:"eth_name"`
	Product     []string                       `json:"product"`
	IpMappings  map[string]*PortMappingListDto `json:"ip_mappings"`
	Fid         string                         `json:"fid"`
	NetMappings any                            `json:"net_mappings"`
}

type AreaDto struct {
	Id   uint64 `json:"id"`
	Name string `json:"name"`
}

type SourceDto struct {
	Id   uint64 `json:"id"`
	Name string `json:"name"`
	Icon string `json:"icon"`
}

// DeviceItem 设备信息
type DeviceItemDto struct {
	Id       string   `json:"id"`
	Sn       []string `json:"sn"`
	Hostname []string `json:"hostname"`
	Mac      []string `json:"mac"`
	Fid      string   `json:"fid"`
}

type PortMappingListDto struct {
	Mappings []*PortMappingItemDto `json:"mappings"`
}

// PortMapping 端口映射信息
type PortMappingItemDto struct {
	PublicIp    string `json:"public_ip"`
	PublicPort  uint64 `json:"public_port"`
	PrivateIp   string `json:"private_ip"`
	PrivatePort uint64 `json:"private_port"`
}

// RuleInfo 规则信息
type RuleInfoDto struct {
	Product   string `json:"product"`
	FirstTag  string `json:"first_tag"`
	SecondTag string `json:"second_tag"`
	Level     string `json:"level"`
}

// GetIpBaseInfo 获取IP基础信息
func GetIpBaseInfo(id string) (*IpBaseInfoResponse, error) {
	logger := logs.GetLogger()
	rsp := &IpBaseInfoResponse{}

	// 从ES中获取IP资产信息
	asset, err := es.GetById[assets.Assets](id)
	if err != nil {
		return nil, err
	}

	wg := &sync.WaitGroup{}
	wg.Add(4)
	// 设备信息
	go func() {
		defer wg.Done()
		// 关联设备，需要动态查询
		rsp.Device = make([]*DeviceItemDto, 0)
		deviceQuery := elastic.NewBoolQuery().
			Must(elastic.NewTermQuery("area", asset.Area)).
			MustNot(elastic.NewExistsQuery("deleted_at")).
			Should(elastic.NewTermQuery("private_ip", asset.Ip), elastic.NewTermQuery("public_ip", asset.Ip)).MinimumNumberShouldMatch(1)
		sortList := []elastic.Sorter{
			elastic.NewFieldSort("updated_at").Desc(),
			elastic.NewFieldSort("created_at").Desc(),
			elastic.NewFieldSort("id").Desc(),
		}
		deviceList, err := es.All[device.Device](500, deviceQuery, sortList, "id", "sn", "mac", "hostname")
		if err != nil {
			logger.Warnf("get device by ip(%s) and area(%s) error: %v", asset.Ip, asset.Area, err)
		}
		for _, device := range deviceList {
			item := &DeviceItemDto{
				Id:       device.Id,
				Sn:       device.Sn,
				Mac:      device.Mac,
				Hostname: device.HostName,
			}
			rsp.Device = append(rsp.Device, item)
		}
	}()

	// 获取区域信息
	go func() {
		defer wg.Done()
		networkarea, err := network_areas.NewNetworkAreaModel().First(mysql.WithId(asset.Area))
		if err != nil {
			logger.Warnf("get network area by id(%d) error: %v", asset.Area, err)
		}
		rsp.Area = &AreaDto{
			Id:   networkarea.Id,
			Name: networkarea.Name,
		}
	}()

	// 获取数据源信息
	rsp.SourceId = asset.AllSourceIds
	go func() {
		defer wg.Done()
		if len(asset.AllSourceIds) > 0 {
			sources := data_source.NewSourceModel().SourceNames(asset.AllSourceIds)
			if len(sources) > 0 {
				rsp.Source = make([]*SourceDto, 0)
				for _, source := range sources {
					rsp.Source = append(rsp.Source, &SourceDto{
						Id:   source.Id,
						Name: source.Name,
						Icon: source.Icon,
					})
				}
			}
		}
	}()

	//内外网映射关系
	go func() {
		defer wg.Done()

		// 根据ip搜索映射关系
		data, _, err := net_mapping.NewNetMappingModel().ListByPage(1, 100, asset.Ip)
		if err != nil {
			logger.Warnf("get IpMappingStatistics List error: %v", err)
		}

		rsp.IpMappings = func() map[string]*PortMappingListDto {
			portMappingList := make(map[string]*PortMappingListDto)
			for _, datum := range data {
				if portMappingList[datum.FromIp] == nil {
					portMappingList[datum.FromIp] = &PortMappingListDto{
						Mappings: []*PortMappingItemDto{},
					}
				}
				portMappingList[datum.FromIp] = &PortMappingListDto{
					Mappings: append(portMappingList[datum.FromIp].Mappings, &PortMappingItemDto{
						PublicIp:    datum.FromIp,
						PublicPort:  cast.ToUint64(datum.FromPort),
						PrivateIp:   datum.ToIp,
						PrivatePort: cast.ToUint64(datum.ToPort),
					}),
				}
				if portMappingList[datum.ToIp] == nil {
					portMappingList[datum.ToIp] = &PortMappingListDto{
						Mappings: []*PortMappingItemDto{},
					}
				}
				portMappingList[datum.ToIp] = &PortMappingListDto{
					Mappings: append(portMappingList[datum.ToIp].Mappings, &PortMappingItemDto{
						PublicIp:    datum.FromIp,
						PublicPort:  cast.ToUint64(datum.FromPort),
						PrivateIp:   datum.ToIp,
						PrivatePort: cast.ToUint64(datum.ToPort),
					}),
				}
			}
			return portMappingList
		}()
	}()

	rsp.Id = asset.Id
	rsp.Ip = asset.Ip
	rsp.IpType = func() string {
		switch asset.IpType {
		case 1:
			return "IPv4"
		case 2:
			return "IPv6"
		default:
			return "unknown"
		}
	}()
	rsp.Status = int32(asset.Status)
	// 区域信息
	rsp.AreaId = uint64(asset.Area)
	rsp.Hostname = func() []string {
		if len(asset.HostName) > 0 {
			return asset.HostName
		}
		return []string{}
	}()
	rsp.Os = asset.Os
	rsp.Mac = asset.Mac
	rsp.EthName = asset.EthName
	var products []string
	for _, item := range asset.RuleInfos {
		products = append(products, item.Product)
	}
	rsp.Product = products
	rsp.Sn = asset.Sn
	rsp.PublicIp = make([]string, 0) // 内网资产使用，暂时预留
	rsp.Domain = make([]string, 0)
	if len(asset.Ports) > 0 {
		for _, port := range asset.Ports {
			rsp.Domain = append(rsp.Domain, port.Domain)
		}
	}

	wg.Wait()

	return rsp, nil
}
