package assetcenter

import (
	"context"
	"errors"
	"fmt"
	"fobrain/initialize/mysql"
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/merge"

	"github.com/gin-gonic/gin"
	"github.com/olivere/elastic/v7"

	"fobrain/fobrain/app/repository/asset"
	"fobrain/fobrain/app/services/permission"
	"fobrain/fobrain/logs"
	"fobrain/initialize/es"
	filtrate "fobrain/models/elastic"
	"fobrain/models/elastic/assets"
	"fobrain/models/elastic/business_system"
)

type AssetsIps struct {
	PublicIps  []string
	PrivateIps []string
}

func GetAssetsIpByBusinessIds(businessIds []string) map[string]*AssetsIps {
	query := elastic.NewNestedQuery("business",
		elastic.NewBoolQuery().Must(
			elastic.NewTermsQueryFromStrings("business.system_id", businessIds...),
		),
	)

	// 从 ES 获取所有业务资产
	allBusinessAsset, _ := assets.NewAssets().FindAllByQuery(context.Background(), nil, query)

	// 初始化 businessIps 映射表
	businessIps := make(map[string]*AssetsIps)

	// 临时存储 IP 去重，避免重复添加
	type ipSet struct {
		Public  map[string]struct{}
		Private map[string]struct{}
	}

	ipCache := make(map[string]*ipSet)

	// 遍历所有业务资产并填充 IP 映射
	for _, a := range allBusinessAsset {
		for _, b := range a.Business {
			if b == nil {
				continue
			}
			// 初始化 AssetsIps 和 ipSet
			if _, exists := businessIps[b.SystemId]; !exists {
				businessIps[b.SystemId] = &AssetsIps{}
				ipCache[b.SystemId] = &ipSet{
					Public:  make(map[string]struct{}),
					Private: make(map[string]struct{}),
				}
			}

			// 获取 IP 去重的缓存引用
			cache := ipCache[b.SystemId]

			// 根据网络类型分类 IP，添加前检查是否已存在
			if a.NetworkType == asset.NetworkTypeInternal {
				if _, found := cache.Private[a.Ip]; !found {
					cache.Private[a.Ip] = struct{}{}
					businessIps[b.SystemId].PrivateIps = append(businessIps[b.SystemId].PrivateIps, a.Ip)
				}
			} else {
				if _, found := cache.Public[a.Ip]; !found {
					cache.Public[a.Ip] = struct{}{}
					businessIps[b.SystemId].PublicIps = append(businessIps[b.SystemId].PublicIps, a.Ip)
				}
			}
		}
	}

	return businessIps
}

func GetAssetsIpByBusinessId(businessId string) ([]string, []string) {
	query := elastic.NewBoolQuery()
	query.Must(elastic.NewTermQuery("business.system_id", businessId))
	allBusinessAsset, _ := assets.NewAssets().FindAllByQuery(context.Background(), query, nil)
	publicIps := make([]string, 0)
	privateIps := make([]string, 0)
	for _, a := range allBusinessAsset {
		if a.IpType == asset.NetworkTypeInternal {
			privateIps = append(privateIps, a.Ip)
		} else {
			publicIps = append(publicIps, a.Ip)
		}
	}
	return publicIps, privateIps
}

// PurgeAsset 彻底删除资产
// networkType 1: 内网, 2: 外网
func PurgeAsset(c *gin.Context, ids []string, networkType int, keyword string, searchCondition []string) error {
	count, err := merge.NewMergeRecordsModel().Count(mysql.WithColumnValue("task_status", merge.MergeRecordsStatusRunning))
	if err != nil {
		return err
	}
	if count > 0 {
		return errors.New("存在融合任务, 请稍后删除")
	}
	log := logs.GetLogger()
	query := elastic.NewBoolQuery().Must(elastic.NewExistsQuery("deleted_at"))
	if networkType > 0 {
		query.Must(elastic.NewTermQuery("network_type", networkType))
	}
	sourceType := data_source.SourceTypeInternetAsset
	if networkType == assets.NetworkTypeInternal {
		sourceType = data_source.SourceTypeIntranetAsset
	}
	permissionQuery, err := permission.NewPermissionService().ProcessElasticsearchQuery(c, sourceType)
	if err != nil {
		return err
	}
	if permissionQuery != nil {
		query = query.Must(permissionQuery)
	}
	if len(ids) > 0 {
		query.Must(elastic.NewTermsQueryFromStrings("id", ids...))
	} else {
		if len(searchCondition) > 0 {
			conditions, err := filtrate.ParseQueryConditions(searchCondition)
			if err != nil {
				log.Errorf("assets CreateBoolQuery 解析查询条件失败 %s", err.Error())
				return err
			}

			for _, condition := range conditions {
				query = filtrate.BuildBoolQuery(condition.Field, condition.OperationTypeString, condition.LogicalConnective, condition.Value, query)
			}
		}
		if keyword != "" {
			query = assets.NewAssets().NewKeywordQuery(keyword, query)
		}
	}

	// 查询源+ip+area信息，为后面删除过程表准备条件
	existAssets, err := es.All[assets.Assets](1000, query, nil, "id", "all_process_ids")
	if err != nil {
		log.Errorf("彻底删除资产，查询资产信息失败: %v", err)
		return err
	}
	if len(existAssets) == 0 {
		log.Warn("彻底删除资产，没有找到需要删除的资产信息")
		return nil
	}

	// 获取所有资产的id
	assetIds := make([]string, 0)
	// 获取所有资产的process_id
	processIds := make([]string, 0)
	for _, existAsset := range existAssets {
		assetIds = append(assetIds, existAsset.Id)
		processIds = append(processIds, existAsset.AllProcessIds...)
	}
	scrollSize := 1000
	// 获取资产关联业务系统ID聚合
	businessIds := GetAssetsBusinessIdsByQuery(query)

	// 删除资产历史记录
	resp, err := es.GetEsClient().DeleteByQuery(assets.NewAssetRecord().IndexName()).Query(elastic.NewTermsQueryFromStrings("asset_id", assetIds...)).ScrollSize(scrollSize).Do(context.Background())
	if err != nil {
		log.Errorf("彻底删除资产，删除资产历史记录失败: %v", err)
		return err
	}
	log.Infof("彻底删除资产，删除资产历史记录成功: %v", resp)

	// 删除资产融合记录
	resp, err = es.GetEsClient().DeleteByQuery(assets.NewMergeRecordsModel().IndexName()).Query(elastic.NewTermsQueryFromStrings("asset_id", assetIds...)).ScrollSize(scrollSize).Do(context.Background())
	if err != nil {
		log.Errorf("彻底删除资产，删除资产融合记录失败: %v", err)
		return err
	}
	log.Infof("彻底删除资产，删除资产融合记录成功: %v", resp)

	// 删除过程表
	resp, err = es.GetEsClient().DeleteByQuery(assets.NewProcessAssetsModel().IndexName()).Refresh("true").Query(elastic.NewTermsQueryFromStrings("id", processIds...)).ScrollSize(scrollSize).Do(context.Background())
	if err != nil {
		log.Errorf("彻底删除资产，删除过程表失败: %v", err)
		return err
	}
	log.Infof("彻底删除资产，删除过程表成功: %v", resp)

	// 删除资产数据
	resp, err = es.GetEsClient().DeleteByQuery(assets.NewAssets().IndexName()).ScrollSize(scrollSize).Refresh("true").Query(query).Do(context.Background())
	if err != nil {
		log.Errorf("彻底删除资产，删除资产数据失败: %v", err)
		return err
	}
	log.Infof("彻底删除资产，删除资产数据成功: %v", resp)

	// 刷新业务系统数据
	if len(businessIds) > 0 {
		// 刷新业务系统
		err = business_system.NewBusinessSystems().RefreshBusiness(context.Background(), businessIds, []string{"ip"})
		if err != nil {
			return err
		}
	}
	return nil
}

// GetAssetsBusinessIdsByQuery
// 获取资产关联业务系统ID去重聚合 业务系统是 nested类型
func GetAssetsBusinessIdsByQuery(query *elastic.BoolQuery) []string {
	// 执行ES查询，添加嵌套聚合和子聚合
	// 先创建一个嵌套聚合，然后在其中添加一个子聚合来获取system_id
	// 使用 .keyword 后缀来指定使用关键字类型版本的字段，而不是文本类型
	nestedAgg := elastic.NewNestedAggregation().Path("business")
	systemIdAgg := elastic.NewTermsAggregation().Field("business.system_id.keyword").Size(10000)
	nestedAgg.SubAggregation("system_ids", systemIdAgg)

	resp, err := es.GetEsClient().Search(assets.NewAssets().IndexName()).
		Query(query).
		Aggregation("business_nested", nestedAgg).
		Do(context.Background())

	if err != nil {
		logs.GetLogger().Errorf("获取资产关联业务系统ID聚合失败: %v", err)
		return []string{}
	}

	// 获取嵌套聚合结果
	nestedResult, found := resp.Aggregations.Nested("business_nested")
	if !found || nestedResult == nil {
		logs.GetLogger().Errorf("未找到业务系统ID嵌套聚合结果")
		return []string{}
	}

	// 从嵌套聚合中获取子聚合结果
	agg, found := nestedResult.Aggregations.Terms("system_ids")
	if !found || agg == nil {
		logs.GetLogger().Errorf("未找到业务系统ID子聚合结果")
		return []string{}
	}

	// 提取所有业务系统ID
	result := make([]string, 0, len(agg.Buckets))
	for _, bucket := range agg.Buckets {
		// 将业务系统ID转换为字符串并添加到结果中
		if systemId, ok := bucket.Key.(string); ok && systemId != "" {
			result = append(result, systemId)
		} else {
			// 如果不是字符串类型，尝试使用fmt.Sprint转换
			systemId := fmt.Sprint(bucket.Key)
			if systemId != "" {
				result = append(result, systemId)
			}
		}
	}

	return result
}
