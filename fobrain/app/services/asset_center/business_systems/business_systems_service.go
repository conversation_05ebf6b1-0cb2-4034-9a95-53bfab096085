package business_systems

import (
	"context"
	"encoding/json"
	"fmt"
	pb "fobrain/mergeService/proto"
	"fobrain/models/elastic/poc"
	"fobrain/models/elastic/staff"
	"io"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/pkg/errors"

	"go-micro.dev/v4/logger"

	filtrate "fobrain/models/elastic"
	"fobrain/models/mysql/custom_column"
	field_tag_rules2 "fobrain/models/mysql/field_tag_rules"

	"github.com/google/uuid"
	"github.com/olivere/elastic/v7"

	"fobrain/fobrain/app/services/sync/file_import"
	"fobrain/fobrain/app/services/task"
	"fobrain/fobrain/logs"
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/data_sync_task"
	pgidservice "fobrain/services/people_pgid"
	strategy "fobrain/services/strategy_business"

	"fobrain/fobrain/app/request/asset_center"
	"fobrain/fobrain/app/request/statistical"
	"fobrain/fobrain/common/localtime"
	"fobrain/initialize/es"
	"fobrain/models/elastic/assets"
	business_system2 "fobrain/models/elastic/business_system"
	"fobrain/pkg/utils"
)

type ServiceBusinessSystems struct {
	Id              string   `json:"id"`            //id
	TagId           uint64   `json:"tag_id"`        //id
	Fid             string   `json:"fid"`           //唯一标记 未融合先使用 mid+BusinessName
	BusinessName    string   `json:"business_name"` //业务系统名称
	Address         string   `json:"address"`       //业务系统地址
	Reliability     int      `json:"reliability"`   //可信状态 1可信 2可疑 3不可信
	Status          int      `json:"status"`        //可信状态 1可信 2可疑 3不可信
	From            int      `json:"from"`          //数据来源 1资产IP导入 2数据源 3业务信息配置人工添加 4业务信息配置人工导入
	FromMark        int      `json:"from_mark"`     //来源标记 用于来源更小粒度描述
	IntranetIps     []string `json:"intranet_ips" zh:"内网"`
	InternetIps     []string `json:"internet_ips" zh:"互联网"`
	ContinuityLevel int      `json:"continuity_level" zh:"连续性级别"` // 1一级 2二级 3三级 4四级 5五级
	UseMark         string   `json:"use_mark" form:"use_mark" validate:"omitempty" zh:"规划用途"`

	AssetsAttribute AssetsAttribute `json:"assets_attribute"` //资产属性
	SystemVersion   string          `json:"system_version"`   //系统版本

	CreatedAt      *localtime.Time          `json:"created_at"`      // 创建时间
	DeletedAt      *localtime.Time          `json:"deleted_at"`      // 创建时间
	UpdatedAt      *localtime.Time          `json:"updated_at"`      // 更新时间
	Business       *Business                `json:"business"`        //业务系统归属
	PersonBase     []*assets.PersonBase     `json:"person_base"`     // 业务系统负责人信息
	DepartmentBase []*assets.DepartmentBase `json:"department_base"` // 业务系统部门信息
	CustomFields   map[string]interface{}   `json:"custom_fields"`   //自定义字段
	PocCount       []PocInfo                `json:"poc_count"`
}

// Business 业务归属结构体
type Business struct {
	BusinessOper         string `json:"business_oper"`           //业务系统责任人id
	BusinessOperNotFound string `json:"business_oper_not_found"` //业务系统人员导入未找到
	BusinessOperName     string `json:"business_oper_name"`      //业务系统责任人
	//BusinessDepartment     []string `json:"business_department"`      //业务系统部门id
	BusinessDepartmentName []string `json:"business_department_name"` //业务系统部门名称
}

// AssetsAttribute 资产属性结构体
type AssetsAttribute struct {
	IsGj           *int `json:"is_gj"`           //是否关基设施：''-空/ 1-是/ 2-否
	IsXc           *int `json:"is_xc"`           //是否信创：''- 空/ 1-是/ 2- 否
	PurchaseType   *int `json:"purchase_type"`   //采购类型：''-空/ 1-自研/ 2-外包/ 3-第三方采购
	ImportantTypes *int `json:"important_types"` //重要性 ''-空/ 1-非常重要/ 2- 重要/ 3- 一般
	InsuranceLevel *int `json:"insurance_level"` //等保级别: ''- 空/ 1-一级/2- 二级/3- 三级
	OperatingEnv   *int `json:"operating_env"`   //运行环境 1-生产环境/2-开发环境
	RunningState   *int `json:"running_state"`   //运行状态 1-运行中/ 0-已下线
}
type PocInfo struct {
	Level string `json:"level"`
	Count int    `json:"count"`
}

// 定义一个通用的map类型，用于存储value到label的映射
type attrMap map[int]string

// 运行状态映射
var runningStateData = attrMap{
	1: "运行中",
	0: "已下线",
}

// 是否映射
var gJData = attrMap{
	1: "是",
	2: "否",
}

var xCData = attrMap{
	1: "是",
	2: "否",
}

// 采购类型映射
var purchaseTypeData = attrMap{
	1: "自研",
	2: "外包",
	3: "第三方采购",
}

// 重要性类型映射
var importantTypes = attrMap{
	1: "非常重要",
	2: "重要",
	3: "一般",
}

// 保险等级映射
var insuranceLevel = attrMap{
	1: "一级等保",
	2: "二级等保",
	3: "三级等保",
	4: "四级等保",
	5: "五级等保",
}

// 运行环境映射
var operatingEnv = attrMap{
	1: "生产环境",
	2: "开发环境",
}

// UUIDStr 生成不带连字符的 UUID
func UUIDStr() string {
	u := uuid.New()
	return strings.ReplaceAll(u.String(), "-", "")
}

type BusinessSystemsService struct {
}

func (s *BusinessSystemsService) GetDepartments(keyword string) ([]map[string]interface{}, error) {
	//聚合业务系统部门列表
	departments, err := business_system2.NewBusinessSystems().DepartmentList(context.Background(), keyword)
	if err != nil {
		return nil, err
	}
	var root statistical.TreeResponse
	for _, d := range departments {
		root.AddPath(d.DepartmentName, d.Count, func(n *statistical.TreeResponse) {
			n.QueryField = "department_base.name.keyword"
			n.QueryKey = "id"
		})
	}
	result := make([]map[string]interface{}, 0)
	result = append(result, map[string]interface{}{
		"value":     root.Children,
		"type":      "tree",
		"type_name": "组织架构",
	})
	return result, nil
}

func NewService() *BusinessSystemsService {
	return &BusinessSystemsService{}
}

// BatchUpdate 批量更新
func (s *BusinessSystemsService) BatchUpdate(params *asset_center.BusinessSystemsSearchRequest, userId uint64) error {
	_ = staff.NewStaff().CacheAllStaff(true)
	query, _ := s.getQuery(params, false)
	businessReq := params.BusinessSystemsRequest
	err := business_system2.NewBusinessSystems().BatchUpdate(context.Background(), query, businessReq)
	if err != nil {
		return err
	}
	// 触发资产更新
	err = strategy.NewStrategy().ReMerge(params.Ids, "system_id")
	if err != nil {
		return err
	}
	return nil
}

// Add 添加或更新
func (s *BusinessSystemsService) Add(params *asset_center.BusinessSystemsRequest, id string, userId uint64) error {
	_ = staff.NewStaff().CacheAllStaff(true)
	//查询业务名称系统是否重复  无唯一值 无法验证
	boolQuery := elastic.NewBoolQuery()
	boolQuery.Must(elastic.NewTermQuery("business_name", params.BusinessName))
	if id != "" {
		boolQuery.MustNot(elastic.NewTermQuery("id", id))
	}
	oldBusinessSystem, err := es.First[business_system2.BusinessSystems](boolQuery, nil, "business_name")
	if err != nil {
		return err
	}
	if oldBusinessSystem != nil {
		return fmt.Errorf("业务系统名称已存在")
	}

	//数据转换
	businessSystem, err := s.convertData(params, userId)
	if err != nil {
		return err
	}
	st := strategy.NewStrategy()
	_ = business_system2.NewBusinessSystems().CacheAllBusinessSystems(context.Background())
	_, err = st.UpSet(params.BusinessName, business_system2.SourceStatusHandleAndImport, businessSystem)
	if err != nil {
		return err
	}
	st.FlushResult()

	// 触发资产更新
	err = strategy.NewStrategy().ReMerge([]string{params.BusinessName}, "system")
	if err != nil {
		return err
	}
	return nil
}

func (s *BusinessSystemsService) convertData(params *asset_center.BusinessSystemsRequest, userId uint64) (*strategy.SourceBusiness, error) {
	businessSystem := &strategy.SourceBusiness{}
	businessSystem.Address = params.Address
	businessSystem.PersonIds = params.PersonIds
	businessSystem.ContinuityLevel = params.ContinuityLevel
	businessSystem.DepartmentsIds = params.DepartmentsIds
	businessSystem.UseMark = params.UseMark
	businessSystem.Source = 0
	businessSystem.Node = 0
	businessSystem.UserId = userId

	businessSystem.IsGj = params.AssetsAttribute.IsGj
	businessSystem.IsXc = params.AssetsAttribute.IsXc
	businessSystem.PurchaseType = params.AssetsAttribute.PurchaseType
	businessSystem.ImportantTypes = params.AssetsAttribute.ImportantTypes
	businessSystem.InsuranceLevel = params.AssetsAttribute.InsuranceLevel
	businessSystem.OperatingEnv = params.AssetsAttribute.OperatingEnv
	businessSystem.RunningState = params.AssetsAttribute.RunningState
	businessSystem.SystemVersion = params.SystemVersion
	businessSystem.StaffField = "name"
	businessSystem.CustomFields = params.CustomFields
	return businessSystem, nil
}

func (s *BusinessSystemsService) ParseParams(business file_import.BusinessSystems, id uint64) *strategy.SourceBusiness {
	// 获取并验证 IP 地址
	continuityLevel := 0
	if business.ContinuityLevel != "" {
		continuityLevel = *parseIntPointer(business.ContinuityLevel)
	}
	return &strategy.SourceBusiness{
		Address:         business.Address,
		ContinuityLevel: continuityLevel,
		PersonNames:     strings.Split(business.PersonNames, ","),
		DepartmentNames: strings.Split(business.DepartmentNames, ","),
		Source:          business_system2.SourceStatusHandleAndImport,
		Node:            0,
		State:           business_system2.TrustStatusYes,
		IsGj:            parseIntPointer(business.IsGj),
		IsXc:            parseIntPointer(business.IsXc),
		PurchaseType:    parseIntPointer(business.PurchaseType),
		ImportantTypes:  parseIntPointer(business.ImportantTypes),
		InsuranceLevel:  parseIntPointer(business.InsuranceLevel),
		OperatingEnv:    parseIntPointer(business.OperatingEnv),
		RunningState:    parseIntPointer(business.RunningState),
		SystemVersion:   business.SystemVersion,
		UseMark:         business.UseMark,
		CustomFields:    business.CustomFields,
	}
}

// BatchUpdateOrCreate 导入
func (s *BusinessSystemsService) BatchUpdateOrCreate(rows []file_import.BusinessSystems, userId uint64, staffField string) error {
	if len(rows) == 0 { // 空数据处理
		return nil
	}
	businessNames := make([]string, 0, len(rows))
	// 使用通道收集每个批次的处理结果
	dataChan := make(chan []*file_import.AssetValidate, len(rows))
	st := strategy.NewStrategy()
	var wg sync.WaitGroup
	chunkSize := 100 // 批次大小，调整为较大的值以减少 Goroutine 数量
	_ = business_system2.NewBusinessSystems().CacheAllBusinessSystems(context.Background())
	for i := 0; i < len(rows); i += chunkSize {
		end := i + chunkSize
		if end > len(rows) {
			end = len(rows)
		}

		wg.Add(1)
		go func(start, end int) {
			defer wg.Done()
			var localData []*file_import.AssetValidate
			for index := start; index < end; index++ {
				business := rows[index]

				sourceBusiness := s.ParseParams(business, userId)
				sourceBusiness.StaffField = staffField
				// 更新或创建业务系统数据
				_, err := st.UpSet(business.BusinessName, business_system2.SourceStatusHandleAndImport, sourceBusiness)
				if err != nil {
					// 限制错误日志的输出频率，避免过多的日志I/O导致性能下降
					if index%100 == 0 { // 每100条输出一次错误
						logger.Errorf("[update business] 入库失败，business Info %+v", sourceBusiness)
					}
				}
				// 优化：提取公共逻辑，避免代码重复
				ipGroups := []struct {
					ips         string
					networkType int
				}{
					{business.IntranetIps, assets.NetworkTypeInternal},
					{business.InternetIps, assets.NetworkTypeExternal},
				}

				// 处理所有IP组
				for _, group := range ipGroups {
					distinctIps := utils.ListDistinct(strings.Split(group.ips, ","))
					for _, ip := range distinctIps {
						ip = strings.TrimSpace(ip) // 优化：去除空格
						if ip == "" {
							continue
						}
						localData = append(localData, &file_import.AssetValidate{
							Ip:             ip,
							BusinessSystem: business.BusinessName,
							NetworkType:    strconv.Itoa(group.networkType),
						})

					}
				}

				// 无论是否有IP，都要添加业务名称
				businessNames = append(businessNames, business.BusinessName)
			}

			// 将每个批次的数据发送到通道
			dataChan <- localData
		}(i, end)
	}

	// 等待所有 Goroutine 完成
	wg.Wait()
	st.FlushResult()
	close(dataChan)

	// 合并所有数据
	var data []*file_import.AssetValidate
	for localData := range dataChan {
		data = append(data, localData...)
	}

	// 如果没有数据需要同步，直接返回
	if len(data) == 0 {
		err := strategy.NewStrategy().ReMerge(businessNames, "system")
		if err != nil {
			return err
		}
		return nil
	}

	// 将数据序列化为 JSON
	marshal, err := json.Marshal(data)
	if err != nil {
		logs.GetLogger().Infof("BatchUpdateOrCreate Marshal ip err:%+v", err)
		return err
	}

	// 派发同步任务
	err = task.NewSyncDataTask().Dispatch(data_source.FileImportNodeId, []int{data_sync_task.SyncAsset}, data_sync_task.SourceHandle, string(marshal), "")
	if err != nil {
		logs.GetLogger().Infof("BatchUpdateOrCreate Dispatch err:%+v", err)
		return err
	}

	return nil
}

// 辅助方法：解析字符串为指针
func parseIntPointer(value string) *int {
	if value == "" {
		return nil
	}
	val, _ := strconv.Atoi(value)
	return &val
}

// Update 更新业务系统
func (s *BusinessSystemsService) Update(params *asset_center.BusinessSystemsUpdateRequest, userId uint64) error {
	id := params.Id
	return s.Add(&params.BusinessSystemsRequest, id, userId)
}

// Delete 删除业务系统
func (s *BusinessSystemsService) Delete(params *asset_center.BusinessSystemsSearchRequest) error {
	//删除业务系统
	query, _ := s.getQuery(params, false)
	data, _ := business_system2.NewBusinessSystems().FindAllByQuery(context.Background(), query, []string{"id"})
	ids := make([]string, 0)
	for _, item := range data {
		ids = append(ids, item.Id)
	}
	//分组删除业务系统，每500个一组
	chunkSize := 500
	for i := 0; i < len(ids); i += chunkSize {
		end := i + chunkSize
		if end > len(ids) {
			end = len(ids)
		}
		chunkIds := ids[i:end]
		err := business_system2.NewBusinessSystems().DeleteByIds(chunkIds, "")
		if err != nil {
			return err
		}
	}
	//删除资产
	err := strategy.NewStrategy().ReMerge(ids, "system_id")
	if err != nil {
		return err
	}
	return nil
}

// UpdateDeletedAt 软删除状态修改
func (s *BusinessSystemsService) UpdateDeletedAt(ids []string, keyword string, isDeleted bool) error {
	return business_system2.NewBusinessSystems().UpdateDeletedAt(ids, keyword, isDeleted)
}

// getQuery 组装查询条件
func (s *BusinessSystemsService) getQuery(params *asset_center.BusinessSystemsSearchRequest, isDeleted bool) (elastic.Query, []elastic.Sorter) {
	query := elastic.NewBoolQuery()
	existsQuery := elastic.NewExistsQuery("deleted_at")
	if isDeleted {
		// 查询 deleted_at 字段存在且不为空字符串
		query.Must(existsQuery)
	} else {
		// 查询 deleted_at 字段为 null 或空字符串
		query.MustNot(existsQuery)
	}
	params.Ids = utils.CompactStrings(params.Ids)
	if params.Status > 0 {
		query.Must(elastic.NewTermQuery("status", params.Status))
	}
	if len(params.Ids) > 0 {
		query.Must(elastic.NewTermsQueryFromStrings("fid", params.Ids...))
	}
	if len(params.Keyword) > 0 {
		query.Must(business_system2.NewBusinessSystems().NewKeywordQuery(params.Keyword))
	}
	sortList := []elastic.Sorter{
		elastic.NewFieldSort("updated_at").Desc(),
		elastic.NewFieldSort("created_at").Desc(),
		elastic.NewFieldSort("id").Desc(),
	}

	if len(params.SearchCondition) > 0 {
		conditions, err := filtrate.ParseQueryConditions(params.SearchCondition)
		if err != nil {
			logger.Errorf("assets CreateBoolQuery 解析查询条件失败 %s", err.Error())
			return query, sortList
		}

		for _, condition := range conditions {
			query = filtrate.BuildBoolQuery(condition.Field, condition.OperationTypeString, condition.LogicalConnective, condition.Value, query)
		}
	}

	// 打印生成的查询语句
	source, err := query.Source()
	if err != nil {
		logger.Errorf("生成查询语句时发生错误: %s", err.Error())
	}

	queryJSON, _ := json.MarshalIndent(source, "", "  ") // 格式化输出
	logger.Debugf("生成的查询语句: %s", string(queryJSON))

	return query, sortList
}

// GetBusinessSystem 获取业务系统
func (s *BusinessSystemsService) GetBusinessSystem(params *asset_center.BusinessSystemsSearchRequest, isDeleted bool) (int64, []*ServiceBusinessSystems, error) {
	query, sortList := s.getQuery(params, isDeleted)
	total, data, err := es.List[business_system2.BusinessSystems](params.Page, params.PerPage, query, sortList)
	if err != nil {
		return 0, nil, err
	}

	// 查询指定包含的ID，返回结果中必须包含这些数据，除非查不到，主要为前端回显使用
	includeIds := utils.ListDistinctNonZero(params.IncludeIds)
	allHits := data
	if len(includeIds) > 0 {
		idsQuery := elastic.NewBoolQuery().Must(elastic.NewTermsQueryFromStrings("id", includeIds...))
		hits, err := es.GetEsClient().Search(business_system2.NewBusinessSystems().IndexName()).Query(idsQuery).Do(context.TODO())
		if err != nil {
			logs.GetLogger().Infof("GetBusinessSystem 查询指定包含的ID,ids:%+v err:%+v", includeIds, err)
		} else {
			allHits = append(allHits, es.ParseHitsValue[business_system2.BusinessSystems](hits.Hits.Hits)...)
		}
	}

	if len(allHits) == 0 {
		return 0, nil, nil
	}
	items, err := s.ProcessPersonnelAndDepartment(allHits)
	if err != nil {
		return 0, nil, err
	}
	// 查询poc索引 聚合漏洞等级和数量
	p, err := s.GetPocInfo(params.Keyword)
	if err != nil {
		return 0, nil, err
	}
	for _, item := range items {
		item.PocCount = p
	}
	return total, items, nil
}

// GetById 获取业务系统
func (s *BusinessSystemsService) GetById(id string) (*ServiceBusinessSystems, error) {
	query := elastic.NewBoolQuery()
	query.Must(elastic.NewTermQuery("id", id))
	item, err := es.First[business_system2.BusinessSystems](query, nil)
	if err != nil {
		return nil, err
	}
	r, err := s.ProcessPersonnelAndDepartment([]*business_system2.BusinessSystems{item})
	if err != nil {
		return nil, err
	}
	return r[0], nil
}

// ProcessPersonnelAndDepartment 处理部门跟人员
func (s *BusinessSystemsService) ProcessPersonnelAndDepartment(items []*business_system2.BusinessSystems) ([]*ServiceBusinessSystems, error) {
	serviceBusinessSystems := make([]*ServiceBusinessSystems, 0)
	for _, item := range items {
		customFields := make(map[string]interface{})
		if item.CustomFields != nil {
			customFields = item.CustomFields
		}
		serviceBusinessSystems = append(serviceBusinessSystems, &ServiceBusinessSystems{
			Id:              item.Id,
			TagId:           item.TagId,
			Fid:             item.Fid,
			BusinessName:    item.BusinessName,
			Address:         item.Address,
			Status:          item.Status,
			Reliability:     item.Status,
			From:            item.From,
			FromMark:        item.FromMark,
			IntranetIps:     item.IntranetIps,
			InternetIps:     item.InternetIps,
			ContinuityLevel: item.ContinuityLevel,
			UseMark:         item.UseMark,
			AssetsAttribute: AssetsAttribute{
				IsGj:           item.AssetsAttribute.IsGj,
				IsXc:           item.AssetsAttribute.IsXc,
				PurchaseType:   item.AssetsAttribute.PurchaseType,
				ImportantTypes: item.AssetsAttribute.ImportantTypes,
				InsuranceLevel: item.AssetsAttribute.InsuranceLevel,
				OperatingEnv:   item.AssetsAttribute.OperatingEnv,
				RunningState:   item.AssetsAttribute.RunningState,
			},
			SystemVersion:  item.SystemVersion,
			CreatedAt:      item.CreatedAt,
			DeletedAt:      item.DeletedAt,
			UpdatedAt:      item.UpdatedAt,
			PersonBase:     item.PersonBase,
			DepartmentBase: item.DepartmentBase,
			CustomFields:   customFields,
		})
		for _, person := range item.PersonBase {
			if person.Id != "" {
				person.Pgid, _ = pgidservice.GetPgidById(person.Id)
			}
		}
	}
	return serviceBusinessSystems, nil
}

// GetPocInfo 获取POC信息 (漏洞等级和数量)
func (s *BusinessSystemsService) GetPocInfo(name string) ([]PocInfo, error) {
	if name == "" {
		return nil, nil
	}
	q := elastic.NewBoolQuery().Must(
		elastic.NewTermQuery("business_name_tmp", name),
	).MustNot(
		elastic.NewExistsQuery("deleted_at"),
	)

	levelAgg := elastic.NewTermsAggregation().Field("level").Size(10)

	res, err := es.GetEsClient().Search().
		Index(poc.NewPoc().IndexName()).
		Query(q).
		Size(0).
		Aggregation("level_aggs", levelAgg).
		Do(context.Background())
	if err != nil {
		return nil, err
	}

	agg, found := res.Aggregations.Terms("level_aggs")
	if !found {
		return nil, nil
	}

	out := make([]PocInfo, len(agg.Buckets))
	for i, b := range agg.Buckets {
		out[i].Level = fmt.Sprint(b.Key)
		out[i].Count = int(b.DocCount)
	}
	return out, nil
}

func (s *BusinessSystemsService) Export(params *asset_center.BusinessSystemsSearchRequest, isDeleted bool) (string, error) {
	// 获取custom字段
	customColumnMeta, err := custom_column.NewCustomFieldMetaModel().GetByModuleType(custom_column.CustomFieldMetaModuleTypeBusinessSystem, false)
	if err != nil {
		return "", err
	}
	customKeys := make([]string, 0)
	customNames := make([]string, 0)
	for _, meta := range customColumnMeta {
		customKeys = append(customKeys, meta.FieldKey)
		customNames = append(customNames, meta.DisplayName)
	}
	// 获取数据
	dataList, err := s.GetExportData(params, isDeleted, customKeys)
	if err != nil {
		return "", err
	}

	// 生成 Excel 文件
	filePath := time.Now().Format("20060102150405") + "业务系统列表.xlsx"
	headers := []string{
		"数据来源",
		"业务系统名称",
		"业务系统地址",
		"业务系统负责人及部门",
		"关联内网IP",
		"关联互联网IP",
		"是否关基设施",
		"是否信创",
		"运行环境",
		"采购类型",
		"重要性",
		"运行状态",
		"等保级别",
		"系统版本",
		"连续性级别",
		"规划用途",
		"首次上报时间",
		"最后上报时间",
	}
	headers = append(headers, customNames...)
	_, err = utils.WriterExcel(filePath, headers, dataList)
	if err != nil {
		return "", err
	}

	return filePath, nil
}

func (s *BusinessSystemsService) GetExportData(params *asset_center.BusinessSystemsSearchRequest, isDeleted bool, customKeys []string) ([][]interface{}, error) {
	query, _ := s.getQuery(params, isDeleted)

	client := es.GetEsClient()                                                                                                             //初始化 Elasticsearch 客户端
	scroll := client.Scroll().Index(business_system2.NewBusinessSystems().IndexName()).Query(query).Sort("updated_at", false).Scroll("1m") // 滚动查询
	//Scroll 方法初始化滚动查询，设置索引名、查询条件、排序规则（按 updated_at 降序）和滚动窗口（1分钟）
	defer scroll.Clear(context.Background())

	datum := make([][]interface{}, 0)
	//循环处理滚动查询的每一个响应，直到没有更多数据或达到结束条件（如 io.EOF）
	for {
		result, err := scroll.Do(context.TODO())

		if err == io.EOF {
			break
		}

		if result.Hits.TotalHits.Value == 0 {
			break
		}

		if len(result.Hits.Hits) == 0 {
			break
		}
		data := es.ParseHitsValue[business_system2.BusinessSystems](result.Hits.Hits)
		list, err := s.ProcessPersonnelAndDepartment(data)
		if err != nil {
			return nil, err
		}
		for _, business := range list {
			perAndDep := ""
			if business.Business != nil {
				perAndDep += business.Business.BusinessOperName + " "
				perAndDep += strings.Join(business.Business.BusinessDepartmentName, ",") + " "
			}
			for _, per := range business.PersonBase {
				perAndDep += per.Name + " "
				for _, dep := range per.Department {
					perAndDep += dep.Name + " "
				}
			}
			attr := s.GetAttr(&business.AssetsAttribute, false)
			item := []interface{}{
				business_system2.SourceStatusMap[business.From],
				business.BusinessName,
				business.Address,
				perAndDep,
				strings.Join(business.IntranetIps, ","),
				strings.Join(business.InternetIps, ","),
				attr["gj"],
				attr["is_xc"],
				attr["env"],
				attr["purchase_tp"],
				attr["important_tp"],
				attr["running_st"],
				attr["insuranceL"],
				business.SystemVersion,
				business_system2.ContinuityLevel[business.ContinuityLevel],
				business.UseMark,
				business.CreatedAt,
				business.UpdatedAt,
			}
			for _, customKey := range customKeys {
				if value, ok := business.CustomFields[customKey]; ok {
					item = append(item, value)
				} else {
					item = append(item, "")
				}
			}
			datum = append(datum, item)
		}

		//scroll = client.Scroll().ScrollId(result.ScrollId).Scroll("1m")
	}

	return datum, nil
}

func (s *BusinessSystemsService) GetAttr(business *AssetsAttribute, isStatistical bool) map[string]string {
	// 初始化 map
	attr := make(map[string]string)
	attr["gj"] = ""
	if business.IsGj != nil {
		if _, ok := gJData[*business.IsGj]; ok {
			attr["gj"] = gJData[*business.IsGj]
		}
	}
	attr["is_xc"] = ""
	if business.IsXc != nil {
		if _, ok := xCData[*business.IsXc]; ok {
			attr["is_xc"] = xCData[*business.IsXc]
		}
	}
	attr["env"] = ""
	if business.OperatingEnv != nil {
		if _, ok := operatingEnv[*business.OperatingEnv]; ok {
			attr["env"] = operatingEnv[*business.OperatingEnv]
		}
	}
	attr["purchase_tp"] = ""
	if business.PurchaseType != nil {
		if _, ok := purchaseTypeData[*business.PurchaseType]; ok {
			attr["purchase_tp"] = purchaseTypeData[*business.PurchaseType]
		}
	}
	attr["important_tp"] = ""
	if business.ImportantTypes != nil {
		if _, ok := importantTypes[*business.ImportantTypes]; ok {
			attr["important_tp"] = importantTypes[*business.ImportantTypes]
		}
	}
	attr["running_st"] = ""
	if business.RunningState != nil {
		if _, ok := runningStateData[*business.RunningState]; ok {
			attr["running_st"] = runningStateData[*business.RunningState]
		}
	}
	attr["insuranceL"] = ""
	if business.InsuranceLevel != nil {
		if _, ok := insuranceLevel[*business.InsuranceLevel]; ok {
			attr["insuranceL"] = insuranceLevel[*business.InsuranceLevel]
		}
	}
	if isStatistical {
		if attr["gj"] == "是" {
			attr["gj"] = "关基设施"
		} else {
			attr["gj"] = ""
		}
		if attr["is_xc"] == "是" {
			attr["is_xc"] = "信创系统"
		} else {
			attr["is_xc"] = ""
		}
	}
	return attr
}

// SetStatus 设置业务系统状态
func (s *BusinessSystemsService) SetStatus(ids []string, keyword string, opStatus int, searchCondition []string, status int) (*asset_center.BusinessSystemCredibleResponse, error) {
	// 在确认前获取人员可信状态
	credibleResponse, err := business_system2.NewBusinessSystems().CredibleList(ids, keyword, opStatus)
	if err != nil {
		return nil, err
	}
	// 如果状态变更为不可信，则删除标签规则
	var businesIds []string
	if status == business_system2.TrustStatusNo {
		query := elastic.NewBoolQuery()
		//query.Must(elastic.NewTermQuery("status", opStatus))
		if len(ids) > 0 {
			query.Must(elastic.NewTermsQueryFromStrings("fid", ids...))
		}
		if len(keyword) > 0 {
			query.Must(business_system2.NewBusinessSystems().NewKeywordQuery(keyword))
		}
		if len(searchCondition) > 0 {
			conditions, err := filtrate.ParseQueryConditions(searchCondition)
			if err != nil {
				logger.Errorf("assetsAll CreateBoolQuery 解析查询条件失败 %s", err.Error())
				//return err
				return nil, err
			}

			for _, condition := range conditions {
				query = filtrate.BuildBoolQuery(condition.Field, condition.OperationTypeString, condition.LogicalConnective, condition.Value, query)
			}
		}
		// 根据query查询tag_id
		businesses, _ := business_system2.NewBusinessSystems().FindAllByQuery(context.Background(), query, []string{"id"})
		for _, business := range businesses {
			businesIds = append(businesIds, business.Id)
		}
	}

	//err := business_system2.NewBusinessSystems().SetStatus(ids, keyword, opStatus, searchCondition, status)
	err = business_system2.NewBusinessSystems().SetStatus(ids, keyword, opStatus, searchCondition, status)
	if err != nil {
		//return err
		return nil, err
	}
	// 如果状态变更为不可信，则删除标签规则
	if status == business_system2.TrustStatusNo && len(businesIds) > 0 {
		err := field_tag_rules2.NewFieldTagRuleConfig().DeleteBusinessByBusinessIds(businesIds)
		if err != nil {
			//return err
			return nil, err
		}
	}

	// 触发资产重新融合 - 传递完整的查询参数
	err = s.triggerMergeForAssets(ids, keyword, status, searchCondition)
	if err != nil {
		return nil, errors.New("设置业务系统状态成功，触发资产融合失败")
	}
	//return nil
	return credibleResponse, nil
}

func (s *BusinessSystemsService) triggerMergeForAssets(businesIds []string, keyword string, opStatus int, searchCondition []string) error {
	// 1. 构建与SetStatus相同的查询条件，获取所有符合条件的业务系统真实ID
	boolQuery := elastic.NewBoolQuery()
	boolQuery.Must(elastic.NewTermQuery("status", opStatus))

	if len(businesIds) > 0 {
		boolQuery.Must(elastic.NewTermsQueryFromStrings("fid", businesIds...))
	}
	if len(keyword) > 0 {
		boolQuery.Must(business_system2.NewBusinessSystems().NewKeywordQuery(keyword))
	}
	if len(searchCondition) > 0 {
		conditions, err := filtrate.ParseQueryConditions(searchCondition)
		if err != nil {
			logs.GetLogger().Warnf("解析查询条件失败. err:%s", err.Error())
			return err
		}
		for _, condition := range conditions {
			boolQuery = filtrate.BuildBoolQuery(condition.Field, condition.OperationTypeString, condition.LogicalConnective, condition.Value, boolQuery)
		}
	}

	// 2. 获取所有符合条件的业务系统真实ID
	businesses, _ := business_system2.NewBusinessSystems().FindAllByQuery(context.Background(), boolQuery, []string{"id"})

	if len(businesses) == 0 {
		logs.GetLogger().Infof("未找到符合条件的业务系统，跳过资产融合触发")
		return nil // 没有符合条件的业务系统，不需要触发融合
	}

	// 3. 提取真实的业务系统ID列表
	realBusinessIds := make([]string, 0, len(businesses))
	for _, business := range businesses {
		realBusinessIds = append(realBusinessIds, business.Id)
	}

	logs.GetLogger().Infof("找到 %d 个符合条件的业务系统，准备触发资产融合", len(realBusinessIds))

	// 4. 使用真实业务系统ID查询受影响的资产
	nestedQuery := elastic.NewNestedQuery(
		"business", // 嵌套字段路径
		elastic.NewTermsQueryFromStrings("business.system_id", realBusinessIds...), // 查询条件
	)
	q := elastic.NewBoolQuery().Must(nestedQuery)
	assetsAll, err := es.All[assets.Assets](1000, q, nil, "id")
	if err != nil {
		logs.GetLogger().Warnf("查询关联资产失败. err:%s", err.Error())
		return err
	}

	assetIds := make([]string, 0, len(assetsAll))
	for _, asset := range assetsAll {
		assetIds = append(assetIds, asset.Id)
	}

	if len(assetIds) > 0 {
		logs.GetLogger().Infof("找到 %d 个关联资产，开始触发融合", len(assetIds))
		// 触发资产重新融合，部分融合
		taskId := fmt.Sprintf("%d", time.Now().Unix())
		result, err := pb.GetProtoClient().TriggerMergeForAsset(context.Background(), &pb.TriggerMergeForAssetRequest{
			TriggerMergeBaseRequest: &pb.TriggerMergeBaseRequest{
				SourceId:     0,
				NodeId:       0,
				TriggerEvent: "业务系统更新",
				TaskId:       taskId,
				ChildTaskId:  taskId,
			},
			AssetIds: assetIds,
			Fields:   []string{"business"},
		}, pb.ClientWithAddress)
		if err != nil {
			logs.GetLogger().Warnf("触发资产融合失败. err:%s", err.Error())
			return err
		}
		if !result.Success {
			return errors.New("触发资产融合失败")
		}
		logs.GetLogger().Infof("成功触发资产融合，任务ID: %s", taskId)
	} else {
		logs.GetLogger().Infof("未找到关联资产，跳过融合触发")
	}
	return nil
}

func (s *BusinessSystemsService) AggCountByDepartment(ctx context.Context) (map[string]int64, error) {
	// 创建嵌套聚合查询
	nestedAgg := elastic.NewNestedAggregation().Path("department_base")

	// 在嵌套聚合中添加部门ID的聚合
	departmentAgg := elastic.NewTermsAggregation().
		Field("department_base.id").
		Size(10000) // 设置足够大的size以获取所有部门

	// 将部门ID聚合添加到嵌套聚合中
	nestedAgg.SubAggregation("department_counts", departmentAgg)

	// 执行查询
	result, err := es.GetEsClient().Search().
		Index(business_system2.NewBusinessSystems().IndexName()).
		Aggregation("departments", nestedAgg).
		Size(0). // 不需要返回具体文档
		Do(ctx)

	if err != nil {
		return nil, err
	}

	// 获取嵌套聚合结果
	nestedResult, found := result.Aggregations.Nested("departments")
	if !found {
		return make(map[string]int64), nil
	}

	// 获取部门计数聚合结果
	departmentCounts, found := nestedResult.Terms("department_counts")
	if !found {
		return make(map[string]int64), nil
	}

	// 构建返回结果
	counts := make(map[string]int64)
	for _, bucket := range departmentCounts.Buckets {
		departmentId := bucket.Key.(float64) // ES 返回的数字默认是 float64
		counts[strconv.FormatUint(uint64(departmentId), 10)] = bucket.DocCount
	}

	return counts, nil
}
