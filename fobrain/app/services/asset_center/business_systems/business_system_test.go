package business_systems

import (
	"context"
	"encoding/json"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/olivere/elastic/v7"
	"github.com/stretchr/testify/assert"

	"fobrain/fobrain/app/repository/field_tag_rules"
	"fobrain/fobrain/app/request/asset_center"
	"fobrain/fobrain/app/services/sync/file_import"
	"fobrain/fobrain/app/services/task"
	"fobrain/fobrain/common/localtime"
	"fobrain/fobrain/common/request"
	testcommon "fobrain/fobrain/tests/common_test"
	pb "fobrain/mergeService/proto"
	business_system2 "fobrain/models/elastic/business_system"
	"fobrain/models/elastic/poc"
	pgidservice "fobrain/services/people_pgid"
	strategy "fobrain/services/strategy_business"
	"regexp"
	"strings"
)

// 测试函数
func TestGetAttr(t *testing.T) {
	service := &BusinessSystemsService{}

	tests := []struct {
		name          string
		input         *AssetsAttribute
		isStatistical bool
		expected      map[string]string
	}{
		{
			name: "Test case with all attributes set",
			input: &AssetsAttribute{
				IsGj:           ptrInt(1),
				IsXc:           ptrInt(1),
				OperatingEnv:   ptrInt(1),
				PurchaseType:   ptrInt(1),
				ImportantTypes: ptrInt(1),
				RunningState:   ptrInt(1),
				InsuranceLevel: ptrInt(1),
			},
			isStatistical: false,
			expected: map[string]string{
				"gj":           "是",
				"is_xc":        "是",
				"env":          "生产环境",
				"purchase_tp":  "自研",
				"important_tp": "非常重要",
				"running_st":   "运行中",
				"insuranceL":   "一级等保",
			},
		},
		{
			name: "Test case with statistical flag",
			input: &AssetsAttribute{
				IsGj:           ptrInt(1),
				IsXc:           ptrInt(2),
				OperatingEnv:   ptrInt(2),
				PurchaseType:   ptrInt(2),
				ImportantTypes: ptrInt(2),
				RunningState:   ptrInt(0),
				InsuranceLevel: ptrInt(3),
			},
			isStatistical: true,
			expected: map[string]string{
				"gj":           "关基设施", // The statistical flag changes "是" to "关基设施"
				"is_xc":        "",     // The statistical flag changes "是" to "信创系统"
				"env":          "开发环境",
				"purchase_tp":  "外包",
				"important_tp": "重要",
				"running_st":   "已下线",
				"insuranceL":   "三级等保",
			},
		},
		{
			name: "Test case with missing attributes",
			input: &AssetsAttribute{
				IsGj: nil, // Missing IsGj
			},
			isStatistical: false,
			expected: map[string]string{
				"gj": "",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := service.GetAttr(tt.input, tt.isStatistical)
			// Verify all expected fields, not just "gj"
			for key, expectedValue := range tt.expected {
				assert.Equal(t, expectedValue, result[key], "Mismatch in field: %s", key)
			}
		})
	}
}

// Helper function to create pointer to int
func ptrInt(i int) *int {
	return &i
}

func TestBatchUpdateOrCreate(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	defer gomonkey.ApplyMethodReturn(pb.GetProtoClient(), "TriggerMergeForAsset", &pb.TriggerMergeResponse{
		Success: true,
	}, nil).Reset()

	mockServer.Register("_bulk", &elastic.BulkIndexByScrollResponse{
		Updated: 1,
	})
	mockServer.Register("/business_systems/_update_by_query", &elastic.BulkIndexByScrollResponse{
		Updated: 1,
	})
	mockServer.Register("/business_systems/_search", []*elastic.SearchHit{
		{
			Id:     "1",
			Source: []byte(`{"business_name":"example"}`),
		},
	})

	// 添加对/asset/_search路径的模拟
	mockServer.Register("/asset/_search", []*elastic.SearchHit{
		{
			Id:     "1",
			Source: []byte(`{"id":"1","business_name":"example"}`),
		},
	})
	// 添加对/_search/scroll路径的模拟
	mockServer.Register("/_search/scroll", &elastic.SearchResult{
		ScrollId: "scroll-id-1",
		Hits: &elastic.SearchHits{
			Hits: []*elastic.SearchHit{},
		},
	})
	// 创建一个测试请求和响应
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyFuncReturn(field_tag_rules.CreateOrUpdate, uint64(1), nil).Reset()
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyMethodReturn(strategy.NewStrategy(), "UpSet", nil, nil).Reset()
	// 示例输入数据
	rows := `[
		{
			"name": "System A",
			"business_name": "Business A",
			"user_id": "1",
			"user_name": "User A",
			"running_state": "1",
			"operating_env": "2",
			"purchase_type": "3",
			"important_types": "4",
			"insurance_level": "5",
			"is_gj": "0",
			"is_xc": "1",
			"ips": "***********,***********"
		}
	]`
	userId := uint64(123)
	additionalParam := "some_additional_param"
	client := pb.GetProtoClient() // 假设这是一个结构体实例
	patches := gomonkey.ApplyMethodReturn(client, "ManualMerge", nil, nil)
	defer patches.Reset()
	mockServer.Register("_bulk", &elastic.BulkIndexByScrollResponse{
		Updated: 1,
	})
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()

	// 修正参数匹配问题
	mockDb.ExpectQuery("SELECT * FROM `data_nodes` WHERE `id` = ? AND `data_nodes`.`deleted_at` IS NULL ORDER BY `data_nodes`.`id` LIMIT 1").
		WithArgs(int64(1)).
		WillReturnRows(mockDb.NewRows([]string{"id", "name"}).AddRow(1, "Node A"))

	mockDb.ExpectQuery("SELECT * FROM `data_node_configs` WHERE `node_id` = ?").
		WithArgs(int64(1)).
		WillReturnRows(mockDb.NewRows([]string{"id", "node_id"}).AddRow(1, 1))

	// 修改为匹配实际传递的参数 0
	mockDb.ExpectQuery("SELECT * FROM `data_sources` WHERE `id` = ? ORDER BY `data_sources`.`id` LIMIT 1").
		WithArgs(int64(0)). // 改为 0 以匹配实际业务代码
		WillReturnRows(mockDb.NewRows([]string{"id", "name"}).AddRow(1, "Source A"))
	// 添加事务Begin和Commit的mock
	mockDb.ExpectBegin()
	// 添加对data_sync_tasks表插入的mock
	mockDb.ExpectExec("INSERT INTO `data_sync_tasks` (`created_at`,`updated_at`,`source_id`,`node_id`,`status`,`source`,`file`,`start_at`,`end_at`,`message`) VALUES (?,?,?,?,?,?,?,?,?,?)").
		WithArgs(
			sqlmock.AnyArg(), // created_at
			sqlmock.AnyArg(), // updated_at
			0,                // source_id
			1,                // node_id
			0,                // status
			2,                // source
			"",               // file
			sqlmock.AnyArg(), // start_at
			nil,              // end_at
			"",               // message
		).
		WillReturnResult(sqlmock.NewResult(1, 1))
	mockDb.ExpectCommit()
	defer gomonkey.ApplyMethodReturn(task.NewSyncDataTask(), "Dispatch", nil).Reset()
	// 创建 BusinessSystemsService 实例
	service := &BusinessSystemsService{}
	data := make([]file_import.BusinessSystems, 0)
	err := json.Unmarshal([]byte(rows), &data)
	if err != nil {
		t.Fatalf("Failed to unmarshal rows: %v", err)
	}
	// 调用 BatchUpdateOrCreate 方法
	err = service.BatchUpdateOrCreate(data, userId, additionalParam)

	assert.Nil(t, err)
}

func TestSetStatus(t *testing.T) {

	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()
	mockServer.Register("/business_systems/_update_by_query", &elastic.BulkIndexByScrollResponse{
		Updated: 1,
	})
	mockServer.Register("/business_systems/_count", elastic.CountResponse{
		Count:           1,
		TerminatedEarly: false,
		Shards: &elastic.ShardsInfo{
			Total:      5,
			Successful: 5,
			Skipped:    0,
			Failed:     0,
		},
	})
	// 创建 BusinessSystemsService 实例
	service := &BusinessSystemsService{}

	mockServer.Register("asset/_search", []*elastic.SearchHit{
		{
			Id:     "asset1",
			Source: []byte(`{"id":"asset1","ip":"***********","area":1,"all_process_ids":["process1","process2"]}`),
		},
	})
	mockServer.RegisterEmptyScrollHandler()

	defer gomonkey.ApplyMethodReturn(pb.GetProtoClient(), "TriggerMergeForAsset", &pb.TriggerMergeResponse{
		Success: true,
	}, nil).Reset()

	// 调用 updateOrCreateTagRules 方法
	//err := service.SetStatus([]string{}, "", 1, []string{}, 2)
	_, err := service.SetStatus([]string{}, "", 1, []string{}, 2)

	// 验证返回的 tagId 是否是我们模拟的值
	assert.Nil(t, err)
}

func TestParseIntPointer(t *testing.T) {
	tests := []struct {
		input    string
		expected *int
	}{
		{"", nil},
		{"123", func() *int { i := 123; return &i }()},
		{"abc", func() *int { i := 0; return &i }()}, // 如果转换失败，可以返回nil（假设函数不处理错误）
	}

	for _, test := range tests {
		t.Run(test.input, func(t *testing.T) {
			result := parseIntPointer(test.input)
			assert.Equal(t, test.expected, result)
		})
	}
}

func TestDelete(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	t.Run("Success", func(t *testing.T) {
		// 模拟删除成功
		mockServer.Register("/business_systems/_delete_by_query", &elastic.BulkIndexByScrollResponse{
			Updated: 1,
		})

		service := &BusinessSystemsService{}
		err := service.Delete(&asset_center.BusinessSystemsSearchRequest{
			Ids: []string{"test-id"},
		})
		assert.Nil(t, err)
	})
}

func TestUpdateDeletedAt(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	t.Run("Success", func(t *testing.T) {
		// 模拟更新成功
		mockServer.Register("/business_systems/_update_by_query", &elastic.BulkIndexByScrollResponse{
			Updated: 1,
		})

		service := &BusinessSystemsService{}
		err := service.UpdateDeletedAt([]string{"test-id"}, "", true)
		assert.Nil(t, err)
	})
}

func TestGetQuery(t *testing.T) {
	service := NewService()

	tests := []struct {
		name      string
		params    *asset_center.BusinessSystemsSearchRequest
		isDeleted bool
		wantQuery elastic.Query
		wantSort  []elastic.Sorter
	}{
		{
			name: "Basic query",
			params: &asset_center.BusinessSystemsSearchRequest{
				BusinessSystemsRequest: &asset_center.BusinessSystemsRequest{
					PageRequest: &request.PageRequest{
						Page:    1,
						PerPage: 10,
					},
				},
			},
			isDeleted: false,
			wantQuery: elastic.NewBoolQuery().MustNot(elastic.NewExistsQuery("deleted_at")),
			wantSort: []elastic.Sorter{
				elastic.NewFieldSort("updated_at").Desc(),
				elastic.NewFieldSort("created_at").Desc(),
				elastic.NewFieldSort("id").Desc(),
			},
		},
		{
			name: "Query with deleted items",
			params: &asset_center.BusinessSystemsSearchRequest{
				BusinessSystemsRequest: &asset_center.BusinessSystemsRequest{
					PageRequest: &request.PageRequest{
						Page:    1,
						PerPage: 10,
					},
				},
			},
			isDeleted: true,
			wantQuery: elastic.NewBoolQuery().Must(elastic.NewExistsQuery("deleted_at")),
			wantSort: []elastic.Sorter{
				elastic.NewFieldSort("updated_at").Desc(),
				elastic.NewFieldSort("created_at").Desc(),
				elastic.NewFieldSort("id").Desc(),
			},
		},
		{
			name: "Query with status filter",
			params: &asset_center.BusinessSystemsSearchRequest{
				BusinessSystemsRequest: &asset_center.BusinessSystemsRequest{
					PageRequest: &request.PageRequest{
						Page:    1,
						PerPage: 10,
					},
				},
				Status: 1,
			},
			isDeleted: false,
			wantQuery: elastic.NewBoolQuery().
				MustNot(elastic.NewExistsQuery("deleted_at")).
				Must(elastic.NewTermQuery("status", 1)),
			wantSort: []elastic.Sorter{
				elastic.NewFieldSort("updated_at").Desc(),
				elastic.NewFieldSort("created_at").Desc(),
				elastic.NewFieldSort("id").Desc(),
			},
		},
		{
			name: "Query with IDs filter",
			params: &asset_center.BusinessSystemsSearchRequest{
				BusinessSystemsRequest: &asset_center.BusinessSystemsRequest{
					PageRequest: &request.PageRequest{
						Page:    1,
						PerPage: 10,
					},
				},
				Ids: []string{"id1", "id2"},
			},
			isDeleted: false,
			wantQuery: elastic.NewBoolQuery().
				MustNot(elastic.NewExistsQuery("deleted_at")).
				Must(elastic.NewTermsQueryFromStrings("fid", "id1", "id2")),
			wantSort: []elastic.Sorter{
				elastic.NewFieldSort("updated_at").Desc(),
				elastic.NewFieldSort("created_at").Desc(),
				elastic.NewFieldSort("id").Desc(),
			},
		},
		{
			name: "Query with keyword search",
			params: &asset_center.BusinessSystemsSearchRequest{
				BusinessSystemsRequest: &asset_center.BusinessSystemsRequest{
					PageRequest: &request.PageRequest{
						Page:    1,
						PerPage: 10,
					},
				},
				Keyword: "test",
			},
			isDeleted: false,
			wantQuery: elastic.NewBoolQuery().
				MustNot(elastic.NewExistsQuery("deleted_at")).
				Must(business_system2.NewBusinessSystems().NewKeywordQuery("test")),
			wantSort: []elastic.Sorter{
				elastic.NewFieldSort("updated_at").Desc(),
				elastic.NewFieldSort("created_at").Desc(),
				elastic.NewFieldSort("id").Desc(),
			},
		},
		{
			name: "Query with search conditions",
			params: &asset_center.BusinessSystemsSearchRequest{
				BusinessSystemsRequest: &asset_center.BusinessSystemsRequest{
					PageRequest: &request.PageRequest{
						Page:    1,
						PerPage: 10,
					},
				},
				SearchCondition: []string{"field1==value1"},
			},
			isDeleted: false,
			wantQuery: elastic.NewBoolQuery().
				MustNot(elastic.NewExistsQuery("deleted_at")),
			wantSort: []elastic.Sorter{
				elastic.NewFieldSort("updated_at").Desc(),
				elastic.NewFieldSort("created_at").Desc(),
				elastic.NewFieldSort("id").Desc(),
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotQuery, gotSort := service.getQuery(tt.params, tt.isDeleted)
			assert.Equal(t, tt.wantQuery, gotQuery)
			assert.Equal(t, tt.wantSort, gotSort)
		})
	}
}

func TestProcessPersonnelAndDepartment(t *testing.T) {
	service := NewService()

	// 添加 mock for GetPgidById
	patches := gomonkey.ApplyFuncReturn(pgidservice.GetPgidById, "mocked_pgid", nil)
	defer patches.Reset()

	tests := []struct {
		name     string
		input    []*business_system2.BusinessSystems
		expected []*ServiceBusinessSystems
	}{
		{
			name:     "Empty input",
			input:    []*business_system2.BusinessSystems{},
			expected: []*ServiceBusinessSystems{},
		},
		{
			name: "Single business system with no personnel or department",
			input: []*business_system2.BusinessSystems{
				{
					Id:           "1",
					TagId:        0,
					Fid:          "fid1",
					BusinessName: "Test System",
					Address:      "Test Address",
					Status:       1,
					From:         1,
					FromMark:     0,
					IntranetIps:  []string{"***********"},
					InternetIps:  []string{"********"},
					AssetsAttribute: business_system2.AssetsAttribute{
						IsGj:           ptrInt(1),
						IsXc:           ptrInt(1),
						PurchaseType:   ptrInt(1),
						ImportantTypes: ptrInt(1),
						InsuranceLevel: ptrInt(1),
						OperatingEnv:   ptrInt(1),
						RunningState:   ptrInt(1),
					},
					SystemVersion: "1.0",
					CreatedAt:     localtime.NewLocalTime(time.Now()),
					UpdatedAt:     localtime.NewLocalTime(time.Now()),
				},
			},
			expected: []*ServiceBusinessSystems{
				{
					Id:           "1",
					TagId:        0,
					Fid:          "fid1",
					BusinessName: "Test System",
					Address:      "Test Address",
					Status:       1,
					Reliability:  1,
					From:         1,
					FromMark:     0,
					IntranetIps:  []string{"***********"},
					InternetIps:  []string{"********"},
					AssetsAttribute: AssetsAttribute{
						IsGj:           ptrInt(1),
						IsXc:           ptrInt(1),
						PurchaseType:   ptrInt(1),
						ImportantTypes: ptrInt(1),
						InsuranceLevel: ptrInt(1),
						OperatingEnv:   ptrInt(1),
						RunningState:   ptrInt(1),
					},
					SystemVersion: "1.0",
				},
			},
		},
	}
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()
	mockServer.Register("/person_base/_search", []*elastic.SearchHit{
		{
			Id:     "1",
			Source: []byte(`{"id":"1","name":"Test Person"}`),
		},
	})
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := service.ProcessPersonnelAndDepartment(tt.input)
			assert.NoError(t, err)
			assert.Equal(t, len(tt.expected), len(result))
			for i, expected := range tt.expected {
				actual := result[i]
				assert.Equal(t, expected.Id, actual.Id)
				assert.Equal(t, expected.TagId, actual.TagId)
				assert.Equal(t, expected.Fid, actual.Fid)
				assert.Equal(t, expected.BusinessName, actual.BusinessName)
				assert.Equal(t, expected.Address, actual.Address)
				assert.Equal(t, expected.Status, actual.Status)
				assert.Equal(t, expected.Reliability, actual.Reliability)
				assert.Equal(t, expected.From, actual.From)
				assert.Equal(t, expected.FromMark, actual.FromMark)
				assert.Equal(t, expected.IntranetIps, actual.IntranetIps)
				assert.Equal(t, expected.InternetIps, actual.InternetIps)
				assert.Equal(t, expected.SystemVersion, actual.SystemVersion)

				// Compare AssetsAttribute
				assert.Equal(t, expected.AssetsAttribute.IsGj, actual.AssetsAttribute.IsGj)
				assert.Equal(t, expected.AssetsAttribute.IsXc, actual.AssetsAttribute.IsXc)
				assert.Equal(t, expected.AssetsAttribute.PurchaseType, actual.AssetsAttribute.PurchaseType)
				assert.Equal(t, expected.AssetsAttribute.ImportantTypes, actual.AssetsAttribute.ImportantTypes)
				assert.Equal(t, expected.AssetsAttribute.InsuranceLevel, actual.AssetsAttribute.InsuranceLevel)
				assert.Equal(t, expected.AssetsAttribute.OperatingEnv, actual.AssetsAttribute.OperatingEnv)
				assert.Equal(t, expected.AssetsAttribute.RunningState, actual.AssetsAttribute.RunningState)

				// Compare PersonBase if exists
				if len(expected.PersonBase) > 0 {
					assert.Equal(t, len(expected.PersonBase), len(actual.PersonBase))
					for j, expectedPerson := range expected.PersonBase {
						actualPerson := actual.PersonBase[j]
						assert.Equal(t, expectedPerson.Id, actualPerson.Id)
						assert.Equal(t, expectedPerson.Fid, actualPerson.Fid)
						assert.Equal(t, expectedPerson.Name, actualPerson.Name)
						assert.Equal(t, len(expectedPerson.FindInfo), len(actualPerson.FindInfo))
						assert.Equal(t, len(expectedPerson.Department), len(actualPerson.Department))
					}
				}
			}
		})
	}
}

func TestConvertData(t *testing.T) {
	service := NewService()

	tests := []struct {
		name     string
		params   *asset_center.BusinessSystemsRequest
		userId   uint64
		expected *strategy.SourceBusiness
		wantErr  bool
	}{
		{
			name: "Valid input with all fields",
			params: &asset_center.BusinessSystemsRequest{
				BusinessName:    "Test System",
				Address:         "Test Address",
				PersonIds:       []string{"person1", "person2"},
				DepartmentsIds:  []string{"dept1", "dept2"},
				ContinuityLevel: 1,
				UseMark:         "Test Use Mark",
				AssetsAttribute: asset_center.AssetsAttribute{
					IsGj:           ptrInt(1),
					IsXc:           ptrInt(1),
					PurchaseType:   ptrInt(1),
					ImportantTypes: ptrInt(1),
					InsuranceLevel: ptrInt(1),
					OperatingEnv:   ptrInt(1),
					RunningState:   ptrInt(1),
				},
			},
			userId: 123,
			expected: &strategy.SourceBusiness{
				Address:         "Test Address",
				PersonIds:       []string{"person1", "person2"},
				DepartmentsIds:  []string{"dept1", "dept2"},
				ContinuityLevel: 1,
				UseMark:         "Test Use Mark",
				Source:          0,
				Node:            0,
				UserId:          123,
				IsGj:            ptrInt(1),
				IsXc:            ptrInt(1),
				PurchaseType:    ptrInt(1),
				ImportantTypes:  ptrInt(1),
				InsuranceLevel:  ptrInt(1),
			},
			wantErr: false,
		},
		{
			name:   "Empty input",
			params: &asset_center.BusinessSystemsRequest{},
			userId: 123,
			expected: &strategy.SourceBusiness{
				Source: 0,
				Node:   0,
				UserId: 123,
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := service.convertData(tt.params, tt.userId)
			if tt.wantErr {
				assert.Error(t, err)
				return
			}
			assert.NoError(t, err)
			assert.Equal(t, tt.expected.Address, got.Address)
			assert.Equal(t, tt.expected.PersonIds, got.PersonIds)
			assert.Equal(t, tt.expected.DepartmentsIds, got.DepartmentsIds)
			assert.Equal(t, tt.expected.ContinuityLevel, got.ContinuityLevel)
			assert.Equal(t, tt.expected.UseMark, got.UseMark)
			assert.Equal(t, tt.expected.Source, got.Source)
			assert.Equal(t, tt.expected.Node, got.Node)
			assert.Equal(t, tt.expected.UserId, got.UserId)
			assert.Equal(t, tt.expected.IsGj, got.IsGj)
			assert.Equal(t, tt.expected.IsXc, got.IsXc)
			assert.Equal(t, tt.expected.PurchaseType, got.PurchaseType)
			assert.Equal(t, tt.expected.ImportantTypes, got.ImportantTypes)
			assert.Equal(t, tt.expected.InsuranceLevel, got.InsuranceLevel)
		})
	}
}

func TestAggCountByDepartment(t *testing.T) {
	service := NewService()
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	// 修正后的模拟响应结构，匹配嵌套聚合的实际结构
	mockServer.Register("/business_systems/_search", elastic.SearchResult{
		TookInMillis: 1,
		TimedOut:     false,
		Hits: &elastic.SearchHits{
			TotalHits: &elastic.TotalHits{
				Value:    2,
				Relation: "eq",
			},
			MaxScore: nil,
			Hits:     make([]*elastic.SearchHit, 0),
		},
		Aggregations: elastic.Aggregations{
			"departments": json.RawMessage(`{
				"doc_count": 30,
				"department_counts": {
					"doc_count_error_upper_bound": 0,
					"sum_other_doc_count": 0,
					"buckets": [
						{
							"key": 1,
							"doc_count": 10
						},
						{
							"key": 2,
							"doc_count": 20
						}
					]
				}
			}`),
		},
	})

	tests := []struct {
		name     string
		expected map[string]int64
		wantErr  bool
	}{
		{
			name: "Valid input",
			expected: map[string]int64{
				"1": 10,
				"2": 20,
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := service.AggCountByDepartment(context.Background())
			if tt.wantErr {
				assert.Error(t, err)
				return
			}
			assert.NoError(t, err)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestBusinessSystemsService_GetDepartments(t *testing.T) {
	esMock := testcommon.NewMockServer()
	defer esMock.Close()

	service := NewService()

	// 定义返回结果的结构体
	type DepartmentResponse struct {
		Value []struct {
			Id         string `json:"id"`
			Name       string `json:"name"`
			QueryKey   string `json:"query_key"`
			QueryField string `json:"query_field"`
			AssetNum   int64  `json:"asset_num"`
		} `json:"value"`
		Type     string `json:"type"`
		TypeName string `json:"type_name"`
	}

	tests := []struct {
		name    string
		keyword string
		esMock  func()
		want    DepartmentResponse
		wantErr bool
	}{
		{
			name: "查询所有部门",
			esMock: func() {
				esMock.Register("/business_systems/_search", elastic.SearchResult{
					Aggregations: elastic.Aggregations{
						"nested_departments": json.RawMessage(`{
							"doc_count": 4,
							"department_names": {
								"doc_count_error_upper_bound": 0,
								"sum_other_doc_count": 0,
								"buckets": [
									{
										"key": "测试部门1",
										"doc_count": 2
									},
									{
										"key": "测试部门2",
										"doc_count": 1
									},
									{
										"key": "测试部门3",
										"doc_count": 1
									}
								]
							}
						}`),
					},
				})
			},
			keyword: "",
			want: DepartmentResponse{
				Value: []struct {
					Id         string `json:"id"`
					Name       string `json:"name"`
					QueryKey   string `json:"query_key"`
					QueryField string `json:"query_field"`
					AssetNum   int64  `json:"asset_num"`
				}{
					{Name: "测试部门1", QueryKey: "id", QueryField: "department_base.name.keyword", AssetNum: 2},
					{Name: "测试部门2", QueryKey: "id", QueryField: "department_base.name.keyword", AssetNum: 1},
					{Name: "测试部门3", QueryKey: "id", QueryField: "department_base.name.keyword", AssetNum: 1},
				},
				Type:     "tree",
				TypeName: "组织架构",
			},
			wantErr: false,
		},
		{
			name: "按关键字查询部门",
			esMock: func() {
				esMock.Register("/business_systems/_search", elastic.SearchResult{
					Aggregations: elastic.Aggregations{
						"nested_departments": json.RawMessage(`{
							"doc_count": 2,
							"department_names": {
								"doc_count_error_upper_bound": 0,
								"sum_other_doc_count": 0,
								"buckets": [
									{
										"key": "测试部门1",
										"doc_count": 2
									}
								]
							}
						}`),
					},
				})
			},
			keyword: "部门1",
			want: DepartmentResponse{
				Value: []struct {
					Id         string `json:"id"`
					Name       string `json:"name"`
					QueryKey   string `json:"query_key"`
					QueryField string `json:"query_field"`
					AssetNum   int64  `json:"asset_num"`
				}{
					{Name: "测试部门1", QueryKey: "id", QueryField: "department_base.name.keyword", AssetNum: 2},
				},
				Type:     "tree",
				TypeName: "组织架构",
			},
			wantErr: false,
		},
		{
			name: "查询不存在的部门",
			esMock: func() {
				esMock.Register("/business_systems/_search", elastic.SearchResult{
					Aggregations: elastic.Aggregations{
						"nested_departments": json.RawMessage(`{
							"doc_count": 0,
							"department_names": {
								"doc_count_error_upper_bound": 0,
								"sum_other_doc_count": 0,
								"buckets": []
							}
						}`),
					},
				})
			},
			keyword: "不存在的部门",
			want: DepartmentResponse{
				Value: []struct {
					Id         string `json:"id"`
					Name       string `json:"name"`
					QueryKey   string `json:"query_key"`
					QueryField string `json:"query_field"`
					AssetNum   int64  `json:"asset_num"`
				}{},
				Type:     "tree",
				TypeName: "组织架构",
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.esMock != nil {
				tt.esMock()
			}

			result, err := service.GetDepartments(tt.keyword)
			if tt.wantErr {
				assert.Error(t, err)
				return
			}
			assert.NoError(t, err)

			// 将结果转换为结构化的响应
			var response DepartmentResponse
			responseBytes, err := json.Marshal(result[0])
			assert.NoError(t, err)
			err = json.Unmarshal(responseBytes, &response)
			assert.NoError(t, err)

			// 比较结果
			assert.Equal(t, tt.want.Type, response.Type)
			assert.Equal(t, tt.want.TypeName, response.TypeName)
			assert.Equal(t, len(tt.want.Value), len(response.Value))

			// 比较部门列表
			for i, wantDept := range tt.want.Value {
				assert.Equal(t, wantDept.Name, response.Value[i].Name)
				assert.Equal(t, wantDept.QueryKey, response.Value[i].QueryKey)
				assert.Equal(t, wantDept.QueryField, response.Value[i].QueryField)
				assert.Equal(t, wantDept.AssetNum, response.Value[i].AssetNum)
			}
		})
	}
}

// TestGetPocInfo 测试 GetPocInfo 函数
func TestGetPocInfo(t *testing.T) {
	// 初始化 mock server
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	service := NewService()

	tests := []struct {
		name           string
		businessName   string
		mockResponse   interface{}
		expectedResult []PocInfo
		expectedError  bool
		description    string
	}{
		{
			name:           "空业务系统名称",
			businessName:   "",
			mockResponse:   nil,
			expectedResult: nil,
			expectedError:  false,
			description:    "当业务系统名称为空时，应该返回 nil, nil",
		},
		{
			name:         "正常查询有聚合结果",
			businessName: "test-business",
			mockResponse: elastic.SearchResult{
				TookInMillis: 10,
				TimedOut:     false,
				Shards: &elastic.ShardsInfo{
					Total:      5,
					Successful: 5,
					Skipped:    0,
					Failed:     0,
				},
				Hits: &elastic.SearchHits{
					TotalHits: &elastic.TotalHits{
						Value:    100,
						Relation: "eq",
					},
				},
				Aggregations: elastic.Aggregations{
					"level_aggs": json.RawMessage(`{
						"doc_count_error_upper_bound": 0,
						"sum_other_doc_count": 0,
						"buckets": [
							{"key": "high", "doc_count": 15},
							{"key": "medium", "doc_count": 30},
							{"key": "low", "doc_count": 55}
						]
					}`),
				},
			},
			expectedResult: []PocInfo{
				{Level: "high", Count: 15},
				{Level: "medium", Count: 30},
				{Level: "low", Count: 55},
			},
			expectedError: false,
			description:   "正常查询应该返回正确的 PocInfo 列表",
		},
		{
			name:         "查询无聚合结果",
			businessName: "empty-business",
			mockResponse: elastic.SearchResult{
				TookInMillis: 5,
				TimedOut:     false,
				Shards: &elastic.ShardsInfo{
					Total:      5,
					Successful: 5,
					Skipped:    0,
					Failed:     0,
				},
				Hits: &elastic.SearchHits{
					TotalHits: &elastic.TotalHits{
						Value:    0,
						Relation: "eq",
					},
				},
				Aggregations: elastic.Aggregations{},
			},
			expectedResult: nil,
			expectedError:  false,
			description:    "当没有聚合结果时，应该返回 nil, nil",
		},
		{
			name:         "聚合结果为空buckets",
			businessName: "no-buckets-business",
			mockResponse: elastic.SearchResult{
				TookInMillis: 8,
				TimedOut:     false,
				Shards: &elastic.ShardsInfo{
					Total:      5,
					Successful: 5,
					Skipped:    0,
					Failed:     0,
				},
				Hits: &elastic.SearchHits{
					TotalHits: &elastic.TotalHits{
						Value:    0,
						Relation: "eq",
					},
				},
				Aggregations: elastic.Aggregations{
					"level_aggs": json.RawMessage(`{
						"doc_count_error_upper_bound": 0,
						"sum_other_doc_count": 0,
						"buckets": []
					}`),
				},
			},
			expectedResult: []PocInfo{},
			expectedError:  false,
			description:    "当聚合结果buckets为空时，应该返回空的 PocInfo 列表",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 设置 mock 响应
			if tt.mockResponse != nil {
				// 注册 POC 索引的搜索响应
				mockServer.Register(poc.NewPoc().IndexName()+"/_search", tt.mockResponse)
			}

			// 调用被测试的函数
			result, err := service.GetPocInfo(tt.businessName)

			// 验证错误
			if tt.expectedError {
				assert.Error(t, err, tt.description)
			} else {
				assert.NoError(t, err, tt.description)
			}

			// 验证结果
			if tt.expectedResult == nil {
				assert.Nil(t, result, tt.description)
			} else {
				assert.NotNil(t, result, tt.description)
				assert.Equal(t, len(tt.expectedResult), len(result), tt.description)

				for i, expected := range tt.expectedResult {
					assert.Equal(t, expected.Level, result[i].Level, "Level should match at index %d", i)
					assert.Equal(t, expected.Count, result[i].Count, "Count should match at index %d", i)
				}
			}
		})
	}

	// 测试 Elasticsearch 查询错误的情况
	t.Run("Elasticsearch查询错误", func(t *testing.T) {
		// 创建一个新的 mock server 来模拟错误
		errorMockServer := testcommon.NewMockServer()
		defer errorMockServer.Close()

		// 注册一个返回错误的响应
		errorMockServer.Register(poc.NewPoc().IndexName()+"/_search", &elastic.Error{
			Details: &elastic.ErrorDetails{
				Type:   "search_phase_execution_exception",
				Reason: "all shards failed",
			},
			Status: 500,
		})

		service := NewService()
		result, err := service.GetPocInfo("error-business")

		assert.Error(t, err, "应该返回 Elasticsearch 查询错误")
		assert.Nil(t, result, "错误情况下结果应该为 nil")
	})
}

// TestUUIDStr 测试 UUIDStr 函数
func TestUUIDStr(t *testing.T) {
	tests := []struct {
		name        string
		description string
	}{
		{
			name:        "生成UUID字符串",
			description: "应该生成不带连字符的32位UUID字符串",
		},
		{
			name:        "多次调用生成不同UUID",
			description: "多次调用应该生成不同的UUID",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := UUIDStr()

			// 验证结果不为空
			assert.NotEmpty(t, result, tt.description)

			// 验证长度为32位（UUID去掉4个连字符后的长度）
			assert.Equal(t, 32, len(result), "UUID字符串长度应该为32位")

			// 验证不包含连字符
			assert.NotContains(t, result, "-", "UUID字符串不应该包含连字符")

			// 验证只包含十六进制字符
			matched, err := regexp.MatchString("^[0-9a-f]{32}$", result)
			assert.NoError(t, err, "正则表达式匹配不应该出错")
			assert.True(t, matched, "UUID字符串应该只包含小写十六进制字符")

			// 验证不包含大写字母
			assert.Equal(t, strings.ToLower(result), result, "UUID字符串应该全部为小写")
		})
	}

	// 测试多次调用生成不同的UUID
	t.Run("多次调用生成不同UUID", func(t *testing.T) {
		uuid1 := UUIDStr()
		uuid2 := UUIDStr()
		uuid3 := UUIDStr()

		// 验证三个UUID都不相同
		assert.NotEqual(t, uuid1, uuid2, "两次调用应该生成不同的UUID")
		assert.NotEqual(t, uuid1, uuid3, "两次调用应该生成不同的UUID")
		assert.NotEqual(t, uuid2, uuid3, "两次调用应该生成不同的UUID")
	})
}
