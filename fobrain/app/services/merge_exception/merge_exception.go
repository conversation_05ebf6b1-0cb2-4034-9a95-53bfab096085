package merge_exception

import (
	"errors"
	"fobrain/initialize/mysql"
	"fobrain/models/mysql/merge"
	"strconv"
	"strings"
)

// ListExceptions 获取融合异常列表
// page: 页码
// perPage: 每页条数
// mergeType: 融合类型
// recordId: 融合记录ID
// status: 状态,可选值: FAILED, DISCARDED
// keyword: 关键字
func ListExceptions(page, perPage int, mergeType, recordId string, status string, keyword string) (results []*merge.MergeExceptions, total int64, err error) {
	if perPage <= 0 {
		perPage = 10
	}
	if page <= 0 {
		page = 1
	}
	if mergeType == "" {
		err = errors.New("融合类型不能为空")
		return
	}
	if recordId == "" {
		err = errors.New("融合记录ID不能为空")
		return
	}
	if strings.Contains(mergeType, "asset") {
		mergeType = merge.BusinessModuleAssetMerge
	} else if strings.Contains(mergeType, "person") {
		mergeType = merge.BusinessModulePersonMerge
	} else if strings.Contains(mergeType, "vuln") {
		mergeType = merge.BusinessModuleVulnMerge
	} else if strings.Contains(mergeType, "device") {
		mergeType = merge.BusinessModuleDeviceMerge
	} else {
		err = errors.New("无效的融合类型")
		return
	}

	if status != "" {
		if status != merge.StatusFailed && status != merge.StatusDiscarded {
			err = errors.New("无效的状态")
			return
		}
	}

	// 构建查询选项
	opts := make([]mysql.HandleFunc, 0)
	// 设置业务模块
	opts = append(opts, mysql.WithColumnValue("business_module", mergeType))
	// 设置融合记录ID
	rId, convErr := strconv.ParseUint(recordId, 10, 64)
	if convErr != nil {
		err = errors.New("无效的融合记录ID格式")
		return
	}
	opts = append(opts, mysql.WithColumnValue("merge_record_id", rId))
	// 设置状态
	if status != "" {
		opts = append(opts, mysql.WithColumnValue("status", status))
	}
	// 设置关键字
	if keyword != "" {
		keywordLike := "%" + keyword + "%"
		opts = append(opts, mysql.WithWhere("error_message LIKE ? OR payload LIKE ?", keywordLike, keywordLike))
	}
	// 设置排序
	sortKey := map[string]string{"created_at": "ASC"}
	// 执行查询
	results, total, err = merge.NewMergeExceptionsModel().List(page, perPage, sortKey, opts...)
	return
}
