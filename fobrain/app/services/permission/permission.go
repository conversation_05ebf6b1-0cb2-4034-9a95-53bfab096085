package permission

import (
	"crypto/md5"
	"crypto/sha1"
	"encoding/json"
	"fmt"
	license2 "fobrain/fobrain/app/repository/settings/license"
	"fobrain/fobrain/app/repository/user_access"
	requestPermission "fobrain/fobrain/app/request/permission"
	responsePermission "fobrain/fobrain/app/response/permission"
	"fobrain/fobrain/app/services/system_info"
	"fobrain/fobrain/common/license"
	"fobrain/fobrain/common/localtime"
	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/initialize/mysql"
	"fobrain/models/elastic/staff"
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/permission"
	"fobrain/pkg/cfg"
	"fobrain/pkg/utils"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/olivere/elastic/v7"
	"github.com/pkg/errors"
	"go-micro.dev/v4/logger"
	"gorm.io/gorm"
)

// NewPermissionService 创建权限服务
// 权限服务，处理用户数据权限，系统用户添加，修改，删除，角色的增加，修改，删除，查询
// 由于菜单和研发相关，菜单暂不提供添加，修改等功能，菜单由migrate脚本初始化数据写入，当有程序升级有菜单变动时也通过脚本更新
// 注意菜单数据变化时，需要同步更新roles_menus, users_api等表的数据
func NewPermissionService() *Service {
	return &Service{}
}

// GetMenusTree 获取菜单树
func (s *Service) GetMenusTree(ctx *gin.Context) ([]responsePermission.Menus, error) {
	all, err := permission.NewMenusModel().Query(&mysql.QueryBuilder{Where: []mysql.Condition{}, OrderBy: []string{"sort asc"}})
	if err != nil {
		return nil, errors.Wrap(err, "permission service func GetMenusTree query menus err")
	}
	disabledMenus, enableMenus, err := s.GetLicenseMenus(ctx)
	if err != nil {
		return nil, errors.Wrap(err, "permission service func GetMenusTree query license_menus err")
	}
	// 递归组装成树
	var trees = s.getChildrenMenus(all, 0, disabledMenus, enableMenus)
	return trees, nil
}

// GetMenusTreeForRole 获取菜单树,添加角色、编辑角色选择菜单用
func (s *Service) GetMenusTreeForRole(ctx *gin.Context, roleId uint64) (*responsePermission.MenusTreeForRole, error) {
	// 从menus表中取出所有除type==4的菜单数据，递归组装成树，递归时需要判断模块是否在授权范围内
	checkedMenus := make(map[uint64]struct{})
	var conditions = []mysql.Condition{mysql.CompareCond{Field: "type", Operator: "!=", Value: 4}}
	if roleId > 0 {
		role, err := permission.NewRolesModel().FindByID(roleId)
		if err != nil {
			return nil, errors.Wrap(err, "permission service func GetMenusTreeForRole query roles err")
		}
		if role.Key == SuperRoleKey {
			menus, err := permission.NewMenusModel().Query(&mysql.QueryBuilder{Where: []mysql.Condition{}})
			if err != nil {
				return nil, errors.Wrap(err, "permission service func GetMenusTreeForRole query menus err")
			}
			checkedMenus = utils.ListToSetFunc[uint64, permission.Menus](menus, func(item permission.Menus) (key uint64, ok bool) {
				return item.Id, true
			})
		} else {
			// 非超管不可选系统管理
			conditions = append(conditions, mysql.CompareCond{Field: "name", Operator: "!=", Value: "Setting"})
			roleMenus, err := permission.NewRolesMenusModel().Query(&mysql.QueryBuilder{Where: []mysql.Condition{mysql.CompareCond{Field: "role_id", Operator: "=", Value: roleId}}})
			if err != nil {
				return nil, errors.Wrap(err, "permission service func GetMenusTreeForRole query role_menus err")
			}
			checkedMenus = utils.ListToSetFunc[uint64, permission.RolesMenus](roleMenus, func(item permission.RolesMenus) (key uint64, ok bool) {
				return item.MenuId, true
			})
		}
	} else {
		// 非超管不可选系统管理
		conditions = append(conditions, mysql.CompareCond{Field: "name", Operator: "!=", Value: "Setting"})
	}
	disabledMenus, enableMenus, err := s.GetLicenseMenus(ctx)
	if err != nil {
		return nil, errors.Wrap(err, "permission service func GetMenusTreeForRole query license_menus err")
	}
	all, err := permission.NewMenusModel().Query(&mysql.QueryBuilder{Where: conditions, OrderBy: []string{"sort asc"}})
	if err != nil {
		return nil, errors.Wrap(err, "permission service func GetMenusTree query menus err")
	}
	// 递归组装成树
	var trees = make([]responsePermission.MenuItemBase, 0)
	var allCheckedMenus = make([]uint64, 0)
	for _, v := range all {
		if _, ok := disabledMenus[v.Name]; ok {
			continue
		}
		if v.ParentId == 0 {
			var item = responsePermission.MenuItemBase{
				Id:   v.Id,
				Key:  v.Name,
				Name: v.Title,
			}
			if _, ok := checkedMenus[item.Id]; ok {
				allCheckedMenus = append(allCheckedMenus, item.Id)
			}
			var childChecked []uint64
			item.Children, childChecked = s.getChildrenMenusForRole(all, item.Id, checkedMenus, disabledMenus, enableMenus)
			allCheckedMenus = utils.ListMerge[uint64](allCheckedMenus, childChecked)
			item.Auth, item.Checked = s.getChildrenButtonForRole(all, item.Id, checkedMenus, disabledMenus)
			trees = append(trees, item)
		}
	}
	return &responsePermission.MenusTreeForRole{
		Checked: allCheckedMenus,
		Menus:   trees,
	}, nil
}
func (s *Service) getChildrenMenus(all []permission.Menus, parentId uint64, disabledMenus, enableMenus map[string]struct{}) []responsePermission.Menus {
	var children = make([]responsePermission.Menus, 0)
	for k := range all {
		if _, ok := disabledMenus[all[k].Name]; ok {
			continue
		}
		// 人行特殊需求，资产映射如果不在授权内，则不展示
		if _, ok := enableMenus[all[k].Name]; !ok && all[k].Name == "AssetRelevance" {
			continue
		}
		if all[k].ParentId == parentId {
			var item = responsePermission.Menus{
				Id:        all[k].Id,
				Path:      all[k].Path,
				Component: all[k].Component,
				Name:      all[k].Name,
				Redirect:  all[k].Redirect,
				Children:  nil,
				Meta: responsePermission.MenuMeta{
					Sort:  &all[k].Sort,
					Title: all[k].Title,
					Icon:  all[k].Icon,
				},
			}
			// 前端特殊需求，默认值的字段，不要
			if len(all[k].CurrentActiveMenu) > 0 {
				item.Meta.CurrentActiveMenu = &responsePermission.CurrentActiveMenu{CurrentActiveMenu: &all[k].CurrentActiveMenu}
			}
			if len(all[k].RealPath) > 0 {
				item.Meta.RealPath = &responsePermission.RealPath{RealPath: &all[k].RealPath}
			}
			if all[k].NoSideBar > 0 {
				item.Meta.NoSideBar = &responsePermission.NoSideBar{NoSideBar: utils.ToPointer[bool](true)}
			}
			if all[k].DynamicLevel > 0 {
				item.Meta.DynamicLevel = &responsePermission.DynamicLevel{DynamicLevel: utils.ToPointer[int](all[k].DynamicLevel)}
			}
			if all[k].HideMenu > 0 {
				item.Meta.HideMenu = &responsePermission.HideMenu{HideMenu: utils.ToPointer[bool](true)}
			}
			if all[k].IgnoreKeepAlive > 0 {
				item.Meta.IgnoreKeepAlive = &responsePermission.IgnoreKeepAlive{IgnoreKeepAlive: utils.ToPointer[bool](true)}
			}

			item.Children = s.getChildrenMenus(all, item.Id, disabledMenus, enableMenus)
			children = append(children, item)
		}
	}
	return children
}
func (s *Service) getChildrenMenusForRole(all []permission.Menus, parentId uint64, checked map[uint64]struct{}, disabledMenus, enableMenus map[string]struct{}) ([]responsePermission.MenuItemBase, []uint64) {
	var children = make([]responsePermission.MenuItemBase, 0)
	var checkedMenus = make([]uint64, 0)
	for k := range all {
		if _, ok := disabledMenus[all[k].Name]; ok {
			continue
		}
		// 人行特殊需求，资产映射如果不在授权内，则不展示
		if _, ok := enableMenus[all[k].Name]; !ok && all[k].Name == "AssetRelevance" {
			continue
		}
		if all[k].ParentId == parentId && all[k].Type != 3 {
			var item = responsePermission.MenuItemBase{
				Id:   all[k].Id,
				Key:  all[k].Name,
				Name: all[k].Title,
			}
			if _, ok := checked[all[k].Id]; ok {
				checkedMenus = append(checkedMenus, all[k].Id)
			}
			var childChecked []uint64
			item.Children, childChecked = s.getChildrenMenusForRole(all, item.Id, checked, disabledMenus, enableMenus)
			checkedMenus = utils.ListMerge[uint64](checkedMenus, childChecked)
			item.Auth, item.Checked = s.getChildrenButtonForRole(all, item.Id, checked, disabledMenus)
			children = append(children, item)
		}
	}
	return children, checkedMenus
}
func (s *Service) getChildrenButtonForRole(all []permission.Menus, parentId uint64, checked map[uint64]struct{}, disabledMenus map[string]struct{}) ([]responsePermission.MenuItemBase, []uint64) {
	var children = make([]responsePermission.MenuItemBase, 0)
	var checkedMenus = make([]uint64, 0)
	for k := range all {
		if _, ok := disabledMenus[all[k].Name]; ok {
			continue
		}
		if all[k].ParentId == parentId && all[k].Type == 3 {
			var item = responsePermission.MenuItemBase{
				Id:   all[k].Id,
				Key:  all[k].Name,
				Name: all[k].Title,
			}
			if all[k].ButtonType != "" {
				item.ButtonType = &responsePermission.ButtonType{ButtonType: utils.ToPointer[string](all[k].ButtonType)}
			}
			if _, ok := checked[all[k].Id]; ok {
				checkedMenus = append(checkedMenus, all[k].Id)
			}
			item.Auth, item.Checked = s.getChildrenButtonForRole(all, item.Id, checked, disabledMenus)
			children = append(children, item)
		}
	}
	return children, checkedMenus
}

// GetUserMenus 获取用户有权限的菜单列表
func (s *Service) GetUserMenus(ctx *gin.Context, userId uint64) (*responsePermission.MenusTreeForUser, error) {
	// 获取用户有权限的菜单列表
	// 从users_roles表拿到用户所在角色，再通过角色id从role_menus表拿到菜单id，再从menus表拿到菜单数据，递归组装成树，超管默认拥有所有菜单权限，直接返回GetMenusTree数据即可
	// 从menus表中取出所有除type==4的菜单数据，递归组装成树，递归时需要判断模块是否在授权范围内
	disabledMenus, enableMenus, err := s.GetLicenseMenus(ctx)
	if err != nil {
		return nil, errors.Wrap(err, "permission service func GetUserMenus query license_menus err")
	}
	isSuper, err := s.IsSuperUser(ctx, userId)
	if err != nil {
		return nil, errors.Wrap(err, "permission service func GetUserMenus query users_roles err")
	}
	// 超级管理员组的直接返回所有授权的菜单按钮
	if isSuper {
		// 所有菜单
		all, err := permission.NewMenusModel().Query(&mysql.QueryBuilder{Where: []mysql.Condition{mysql.InCondition{Field: "type", Values: []int8{1, 2}}}, OrderBy: []string{"sort asc"}})
		if err != nil {
			return nil, errors.Wrap(err, "permission service func GetMenusTree query menus err")
		}
		// 递归组装成树
		var trees = s.getChildrenMenus(all, 0, disabledMenus, enableMenus)
		// 所有按钮
		allButtons, err := permission.NewMenusModel().Query(&mysql.QueryBuilder{Where: []mysql.Condition{mysql.CompareCond{Field: "type", Operator: "=", Value: 3}, mysql.NotInCondition{Field: "name", Values: disabledMenus}}})
		if err != nil {
			return nil, errors.Wrap(err, "permission service func GetUserMenus query menus err")
		}
		allButtonsName := utils.ListColumn[string, permission.Menus](allButtons, func(item permission.Menus) (key string) {
			return item.Name
		})
		return &responsePermission.MenusTreeForUser{Menus: trees, Buttons: allButtonsName}, nil
	}
	// 用户所在的所有角色
	allRoles, err := permission.NewUsersRolesModel().Query(&mysql.QueryBuilder{Where: []mysql.Condition{mysql.CompareCond{Field: "user_id", Operator: "=", Value: userId}}})
	rolesId := utils.ListColumn[uint64, permission.UsersRoles](allRoles, func(item permission.UsersRoles) (key uint64) {
		return item.RoleId
	})
	// 用户没有角色
	if len(rolesId) == 0 {
		return nil, errors.New("permission service func GetUserMenus user not have roles")
	}
	// 用户所在角色拥有的菜单
	allRolesMenus, err := permission.NewRolesMenusModel().Query(&mysql.QueryBuilder{Where: []mysql.Condition{mysql.InCondition{Field: "role_id", Values: rolesId}}})
	if err != nil {
		return nil, errors.Wrap(err, "permission service func GetUserMenus query roles_menus err")
	}
	// 遍历得到所有的ID
	allMenusId := utils.ListColumn[uint64, permission.RolesMenus](allRolesMenus, func(item permission.RolesMenus) (key uint64) {
		return item.MenuId
	})
	// 查询类型为菜单的
	all, err := permission.NewMenusModel().Query(&mysql.QueryBuilder{Where: []mysql.Condition{mysql.InCondition{Field: "id", Values: allMenusId}, mysql.InCondition{Field: "type", Values: []int8{1, 2}}}, OrderBy: []string{"sort asc"}})
	if err != nil {
		return nil, errors.Wrap(err, "permission service func GetUserMenus query menus err")
	}
	// 递归组装成树
	var trees = s.getChildrenMenus(all, 0, disabledMenus, enableMenus)
	// 查询类型为按钮的
	allButtons, err := permission.NewMenusModel().Query(&mysql.QueryBuilder{Where: []mysql.Condition{mysql.InCondition{Field: "id", Values: allMenusId}, mysql.InCondition{Field: "type", Values: []int8{3}}}})
	if err != nil {
		return nil, errors.Wrap(err, "permission service func GetUserMenus query menus err")
	}
	allButtonsName := utils.ListColumn[string, permission.Menus](allButtons, func(item permission.Menus) (key string) {
		return item.Name
	})
	return &responsePermission.MenusTreeForUser{Menus: trees, Buttons: allButtonsName}, nil
}

func (s *Service) IsSuperUser(ctx *gin.Context, userId uint64) (bool, error) {
	// 判断用户是否是超管
	// 从users_roles表拿到用户所在角色，再通过角色id从role表拿到角色数据，判断角色是否是超管角色
	roles, err := permission.NewUsersRolesModel().Query(&mysql.QueryBuilder{
		Where: []mysql.Condition{mysql.CompareCond{Field: "user_id", Operator: "=", Value: userId}},
	})
	if err != nil {
		return false, errors.Wrap(err, "permission service func IsSuperUser query users_roles err")
	}
	if len(roles) > 0 {
		roleIds := utils.ListColumn[uint64, permission.UsersRoles](roles, func(item permission.UsersRoles) uint64 {
			return item.RoleId
		})
		total, err := permission.NewRolesModel().Total(&mysql.QueryBuilder{
			Where: []mysql.Condition{mysql.InCondition{Field: "id", Values: roleIds}, mysql.CompareCond{Field: "key", Operator: "=", Value: SuperRoleKey}},
		})
		if err != nil {
			return false, errors.Wrap(err, "permission service func IsSuperUser query roles err")
		}
		if total > 0 {
			return true, nil
		}
	}
	return false, nil
}

// GetRoles 获取角色列表, 分页查询
func (s *Service) GetRoles(ctx *gin.Context, request *requestPermission.GetRoleListRequest) ([]permission.Roles, int64, error) {
	qb := &mysql.QueryBuilder{
		Limit:  request.PerPage,
		Offset: (request.Page - 1) * request.PerPage,
	}
	w := make([]mysql.Condition, 0)
	if len(request.Keyword) > 0 {
		w = append(w, mysql.CompareCond{Field: "name", Operator: "like", Value: "%" + request.Keyword + "%"})
	}
	qb.Where = w
	roles, total, err := permission.NewRolesModel().Page(qb)
	if err != nil {
		return nil, 0, errors.Wrap(err, "permission service call GetRoles err")
	}
	if total == 0 {
		return roles, total, nil
	}
	rolesId := utils.ListColumn[uint64, permission.Roles](roles, func(item permission.Roles) uint64 {
		return item.Id
	})
	// 统计角色下的用户数量
	// 定义统计用的结构体
	type Count struct {
		RoleId uint64 `gorm:"column:role_id"`
		Count  int64  `gorm:"column:count"`
	}
	// 查询用户数量
	counts, err := mysql.NewDSL[Count]().Query(&mysql.QueryBuilder{
		Table:   new(permission.UsersRoles).TableName(),
		GroupBy: []string{"role_id"},
		Select:  []string{"role_id", "count(role_id) as count"},
		Where: []mysql.Condition{
			mysql.InCondition{Field: "role_id", Values: rolesId},
		},
	})
	if err != nil {
		return nil, 0, errors.Wrap(err, "permission service count role users err")
	}
	// 统计结果转换为map
	mapCounts := utils.ListToMapFunc[uint64, Count](counts, func(item Count) (uint64, bool) {
		return item.RoleId, true
	})
	for i := range roles {
		if count, ok := mapCounts[roles[i].Id]; ok {
			roles[i].TotalUsers = count.Count
		}
	}
	return roles, total, nil
}

// GetUsers 获取用户列表，分页查询
func (s *Service) GetUsers(ctx *gin.Context, request *requestPermission.GetUsersListRequest) ([]responsePermission.Users, int64, error) {
	qb := &mysql.QueryBuilder{
		Limit:  request.PerPage,
		Offset: (request.Page - 1) * request.PerPage,
	}
	w := make([]mysql.Condition, 0)
	if len(request.Keyword) > 0 {
		w = append(w, mysql.OrConditions{Conditions: []mysql.Condition{mysql.CompareCond{Field: "username", Operator: "like", Value: "%" + request.Keyword + "%"}, mysql.CompareCond{Field: "account", Operator: "like", Value: "%" + request.Keyword + "%"}}})
	}
	qb.Where = w
	users, total, err := permission.NewUsersModel().Page(qb)
	if err != nil {
		return nil, 0, errors.Wrap(err, "permission service call GetUsers err")
	}
	if total == 0 {
		return nil, total, nil
	}
	var usersID []uint64
	for _, v := range users {
		usersID = append(usersID, v.Id)
	}
	// 批量查询用户所属角色
	usersRoles, err := s.getUsersRoles(ctx, usersID)
	if err != nil {
		return nil, 0, errors.Wrap(err, "permission service call getUsersRoles err")
	}
	// 批量查询用户关联的人员台账
	staffs, err := s.getUsersStaffs(ctx, usersID)
	if err != nil {
		return nil, 0, errors.Wrap(err, "permission service call getUsersStaffs err")
	}
	var usersResponse []responsePermission.Users
	for i := range users {
		user := responsePermission.Users{
			PermissionDesc:        make([]string, 0),
			PrivatePermissionDesc: make([]string, 0),
			PublicPermissionDesc:  make([]string, 0),
		}
		user.Id = users[i].Id
		user.Username = users[i].Username
		user.Account = users[i].Account
		user.Status = users[i].Status
		user.CreatedAt = users[i].CreatedAt
		user.UpdatedAt = users[i].UpdatedAt
		user.DataPermission = users[i].DataPermission
		user.RuleInfo = users[i].RuleInfo
		if roles, ok := usersRoles[users[i].Id]; ok {
			user.Role = roles
			for _, r := range user.Role {
				if r.Key == SuperRoleKey {
					user.DataPermission = permission.DataPermission{
						PrivateAndPublicSame: true,
						NoMaster:             true,
						Private: permission.DataPermissionItem{
							DataLevel: []int8{int8(permission.DataPermissionLevelAll)},
						},
						Public: permission.DataPermissionItem{
							DataLevel: []int8{int8(permission.DataPermissionLevelAll)},
						},
					}
				}
			}
		}
		user.GetPermissionDesc()
		if st, ok := staffs[users[i].Id]; ok {
			user.Staffs = st
			user.StaffIds = utils.ListColumn[string, responsePermission.StaffBaseInfo](st, func(item responsePermission.StaffBaseInfo) string {
				return item.Fid
			})
		}
		usersResponse = append(usersResponse, user)
	}
	return usersResponse, total, nil
}

// getUsersStaffs 根据用户ID集合，批量查询用户关联的员工列表，先用users_staffs表查出关联的staff_id集合，再通过ES查询员工信息
func (s *Service) getUsersStaffs(ctx *gin.Context, usersId []uint64) (map[uint64][]responsePermission.StaffBaseInfo, error) {
	staffs := make(map[uint64][]responsePermission.StaffBaseInfo)
	usersStaffs, err := permission.NewUsersStaffsModel().Query(&mysql.QueryBuilder{
		Where: []mysql.Condition{mysql.InCondition{Field: "user_id", Values: usersId}},
	})
	if err != nil {
		return nil, errors.Wrap(err, "permission service func getUsersStaffs query users_staffs err")
	}
	var staffIds []string
	for _, v := range usersStaffs {
		staffIds = append(staffIds, v.StaffId)
	}
	staffsES, err := staff.NewStaff().GetByFIds(ctx, staffIds)
	if err != nil {
		return nil, errors.Wrap(err, "permission service func getUsersStaffs query staffs err")
	}
	mapStaffES := utils.ListToMapFunc[string, *staff.Staff](staffsES, func(item *staff.Staff) (string, bool) {
		return item.FidHash, true
	})
	for _, v := range usersStaffs {
		if _, ok := staffs[v.UserId]; !ok {
			staffs[v.UserId] = make([]responsePermission.StaffBaseInfo, 0)
		}
		if st, ok := mapStaffES[v.StaffId]; ok {
			staffBaseInfo := responsePermission.StaffBaseInfo{
				Fid:        st.FidHash,
				Name:       st.Name,
				Department: st.Department,
			}
			if len(st.Mobile) >= 10 {
				staffBaseInfo.Mobile = st.Mobile[:3] + "****" + st.Mobile[7:]
			}
			staffs[v.UserId] = append(staffs[v.UserId], staffBaseInfo)
		}
	}
	return staffs, nil
}

// getUsersRoles 根据用户ID集合，批量查询用户所属的角色集合
func (s *Service) getUsersRoles(ctx *gin.Context, usersId []uint64) (map[uint64][]permission.Roles, error) {
	usersRoles, err := permission.NewUsersRolesModel().Query(&mysql.QueryBuilder{
		Where: []mysql.Condition{mysql.InCondition{Field: "user_id", Values: usersId}},
	})
	if err != nil {
		return nil, errors.Wrap(err, "permission service func getUsersRoles query user's roles err")
	}
	var rolesId []uint64
	for _, v := range usersRoles {
		rolesId = append(rolesId, v.RoleId)
	}
	roles, err := permission.NewRolesModel().Query(&mysql.QueryBuilder{
		Where:  []mysql.Condition{mysql.InCondition{Field: "id", Values: rolesId}},
		Select: []string{"id", "name", "key", "sys"},
	})
	if err != nil {
		return nil, errors.Wrap(err, "permission service func getUsersRoles query roles err")
	}
	mapRoles := utils.ListToMapFunc[uint64, permission.Roles](roles, func(item permission.Roles) (uint64, bool) {
		return item.Id, true
	})
	var mapUsersRoles = make(map[uint64][]permission.Roles)
	for _, v := range usersRoles {
		if _, ok := mapUsersRoles[v.UserId]; !ok {
			mapUsersRoles[v.UserId] = make([]permission.Roles, 0)
		}
		if role, ok := mapRoles[v.RoleId]; ok {
			mapUsersRoles[v.UserId] = append(mapUsersRoles[v.UserId], role)
		}
	}
	return mapUsersRoles, nil
}

// ProcessElasticsearchQuery 处理Elasticsearch查询,数据权限查询处理
// 给每个资产es的index，都加上五个字段：运维负责人、业务系统负责人、运维部门、业务系统部门、漏洞修复人部门，均存切片，即ID集合
// 通过查询用户表users，拿到data_permission值，遍历获取用户有权限的所有部门ids，组装查询条件，部门ID分两部分，一部分是额外部门ID集合，一部分是通过staff_id去查人员台账，查部门ID
// 用户自己关联staff_id，根据权限设置去查业务系统负责人和运维负责人两个字段，组装查询条件
// 用户有无主数据查询权限，构建查询，即个字段都是空的数据
// 整体返回或条件查询集，即取并集：用户自己拥有的数据、用户所在部门及子部门的数据、无主数据、用户额外拥有的其他部门的数据
// 漏洞需要额外增加派发的漏洞
// 漏洞和设备不区分内外网，当用户内外网权限不一致时，取内外网权限的并集查询
func (s *Service) ProcessElasticsearchQuery(ctx *gin.Context, queryType int) (elastic.Query, error) {
	if ctx == nil {
		return nil, nil
	}
	//测试使用
	isSuperManage := ctx.GetBool("is_super_manage")
	if isSuperManage {
		return nil, nil
	}
	// 获取用户ID
	userId := ctx.GetUint64("user_id")
	if userId == 0 {
		return nil, nil
	}
	// 判断是否是超级用户
	isSuper, err := s.IsSuperUser(ctx, userId)
	if err != nil {
		return nil, err
	}
	if isSuper {
		return nil, nil
	}
	//获取用户权限
	query, err := s.GetPermissionsQuery(ctx, userId, queryType)
	if err != nil {
		return nil, err
	}
	if query == nil {
		return nil, nil
	}
	return query, nil
}
func (s *Service) EncryptedPassword(password string) string {
	return fmt.Sprintf("%x", md5.Sum([]byte(fmt.Sprintf("%x", sha1.Sum([]byte(password))))))
}

// GetPermissionsQuery 获取用户权限查询条件
func (s *Service) GetPermissionsQuery(ctx *gin.Context, userId uint64, queryType int) (*elastic.BoolQuery, error) {
	// 获取用户数据权限
	user, err := permission.NewUsersModel().FindByID(userId)
	if err != nil {
		return nil, errors.Wrap(err, "permission service func GetPermissions query users err")
	}
	permissions := user.DataPermission

	// 获取数据权限级别和部门ID
	dataLevel, departmentIds := s.getPermissionLevelAndDepts(permissions, queryType)

	// 如果有全部数据权限，则不进行权限过滤
	if utils.ListContains(dataLevel, int8(permission.DataPermissionLevelAll)) {
		return nil, nil
	}

	// 构建权限查询条件
	query := elastic.NewBoolQuery()

	// 获取用户关联的员工信息
	staffFids := ctx.GetStringSlice("staff_ids")
	staffDepartments, staffIds, err := s.getStaffDepartments(ctx, staffFids)
	if err != nil {
		return nil, errors.Wrap(err, "permission service func GetPermissionsQuery getStaffDepartments err")
	}

	// 根据查询类型构建不同的权限条件
	if err := s.buildQueryByType(query, queryType, dataLevel, staffIds, staffDepartments, staffFids, departmentIds); err != nil && permissions.NoMaster == false {
		return nil, err
	}

	// 添加无主数据查询条件 - 修改这里
	if permissions.NoMaster {
		s.addNoMasterQuery(query)
	}

	return query, nil
}

// getPermissionLevelAndDepts 获取权限级别和部门ID
func (s *Service) getPermissionLevelAndDepts(permissions permission.DataPermission, queryType int) ([]int8, []uint64) {
	dataLevel := make([]int8, 0)
	departmentIds := make([]uint64, 0)

	switch queryType {
	case data_source.SourceTypeInternetAsset:
		dataLevel = permissions.Public.DataLevel
		departmentIds = permissions.Public.ExtraDepartments
	case data_source.SourceTypeIntranetAsset:
		dataLevel = permissions.Private.DataLevel
		departmentIds = permissions.Private.ExtraDepartments
	default:
		dataLevel = utils.ListUnion(permissions.Public.DataLevel, permissions.Private.DataLevel)
		departmentIds = utils.ListUnion(permissions.Public.ExtraDepartments, permissions.Private.ExtraDepartments)
	}

	return dataLevel, departmentIds
}

// buildQueryByType 根据查询类型构建查询条件
func (s *Service) buildQueryByType(query *elastic.BoolQuery, queryType int, dataLevel []int8,
	staffIds []string, staffDepartments []uint64, staffFids []string, extraDepartmentIds []uint64) error {

	// 添加额外部门权限
	var hasExtraDepartments bool
	if utils.ListContains(dataLevel, int8(permission.DataPermissionLevelExtraDepartments)) {
		s.addExtraDepartmentsQuery(query, extraDepartmentIds)
		hasExtraDepartments = true
	}

	switch queryType {
	case data_source.SourceTypeInternetAsset, data_source.SourceTypeIntranetAsset, data_source.SourceTypeDevice:
		err := s.buildAssetQuery(query, dataLevel, staffIds, staffDepartments)
		if err != nil && !hasExtraDepartments {
			return err
		}
	case data_source.SourceTypePoc:
		err := s.buildPocQuery(query, dataLevel, staffIds, staffDepartments, staffFids)
		if err != nil && !hasExtraDepartments {
			return err
		}
	}

	return nil
}

// buildAssetQuery 构建资产相关查询
func (s *Service) buildAssetQuery(query *elastic.BoolQuery, dataLevel []int8, staffIds []string, staffDepartments []uint64) error {
	if len(utils.ListIntersect(dataLevel, []int8{int8(permission.DataPermissionLevelOperationManager), int8(permission.DataPermissionLevelBusinessManager), int8(permission.DataPermissionLevelOperationDepartment), int8(permission.DataPermissionLevelBusinessDepartment)})) == 0 {
		return errors.New("无数据权限！")
	}
	if utils.ListContains(dataLevel, int8(permission.DataPermissionLevelOperationManager)) {
		query.Should(elastic.NewTermsQueryFromStrings("oper_staff_ids", staffIds...))
	}
	if utils.ListContains(dataLevel, int8(permission.DataPermissionLevelBusinessManager)) {
		query.Should(elastic.NewTermsQueryFromStrings("business_staff_ids", staffIds...))
	}
	if utils.ListContains(dataLevel, int8(permission.DataPermissionLevelOperationDepartment)) {
		query.Should(elastic.NewTermsQuery("oper_department_ids", utils.ListToInterfaceSlice(staffDepartments)...))
	}
	if utils.ListContains(dataLevel, int8(permission.DataPermissionLevelBusinessDepartment)) {
		query.Should(elastic.NewTermsQuery("business_department_ids", utils.ListToInterfaceSlice(staffDepartments)...))
	}
	return nil
}

// buildPocQuery 构建POC相关查询
func (s *Service) buildPocQuery(query *elastic.BoolQuery, dataLevel []int8, staffIds []string,
	staffDepartments []uint64, staffFids []string) error {
	if len(utils.ListIntersect(dataLevel, []int8{int8(permission.DataPermissionLevelOperationManagerAsset), int8(permission.DataPermissionLevelBusinessManagerAsset), int8(permission.DataPermissionLevelOperationDepartmentAsset), int8(permission.DataPermissionLevelBusinessDepartmentAsset), int8(permission.DataPermissionLevelAssigned), int8(permission.DataPermissionLevelRepairDepartmentVuln)})) == 0 {
		return errors.New("无数据权限！")
	}
	if utils.ListContains(dataLevel, int8(permission.DataPermissionLevelOperationManagerAsset)) {
		query.Should(elastic.NewTermsQueryFromStrings("oper_staff_ids", staffIds...))
	}
	if utils.ListContains(dataLevel, int8(permission.DataPermissionLevelBusinessManagerAsset)) {
		query.Should(elastic.NewTermsQueryFromStrings("business_staff_ids", staffIds...))
	}
	if utils.ListContains(dataLevel, int8(permission.DataPermissionLevelOperationDepartmentAsset)) {
		query.Should(elastic.NewTermsQuery("oper_department_ids", utils.ListToInterfaceSlice(staffDepartments)...))
	}
	if utils.ListContains(dataLevel, int8(permission.DataPermissionLevelBusinessDepartmentAsset)) {
		query.Should(elastic.NewTermsQuery("business_department_ids", utils.ListToInterfaceSlice(staffDepartments)...))
	}
	if utils.ListContains(dataLevel, int8(permission.DataPermissionLevelAssigned)) {
		query.Should(elastic.NewTermsQueryFromStrings("person_limit_hash", staffFids...))
	}
	if utils.ListContains(dataLevel, int8(permission.DataPermissionLevelRepairDepartmentVuln)) {
		query.Should(elastic.NewTermsQuery("repair_department_ids", utils.ListToInterfaceSlice(staffDepartments)...))
	}
	return nil
}

// addExtraDepartmentsQuery 添加额外部门查询条件
func (s *Service) addExtraDepartmentsQuery(query *elastic.BoolQuery, extraDepartmentIds []uint64) {
	query.Should(elastic.NewTermsQuery("business_department_ids", utils.ListToInterfaceSlice(extraDepartmentIds)...))
	query.Should(elastic.NewTermsQuery("oper_department_ids", utils.ListToInterfaceSlice(extraDepartmentIds)...))
}

// addNoMasterQuery 添加无主数据查询条件
func (s *Service) addNoMasterQuery(query *elastic.BoolQuery) {

	// 情况1: 字段不存在
	query.Should(
		elastic.NewBoolQuery().MustNot(
			elastic.NewExistsQuery("oper_staff_ids"),
			elastic.NewExistsQuery("business_staff_ids"),
			elastic.NewExistsQuery("oper_department_ids"),
			elastic.NewExistsQuery("business_department_ids"),
		),
	)
}

// getStaffDepartments 获取人员部门ids
func (s *Service) getStaffDepartments(ctx *gin.Context, staffFids []string) ([]uint64, []string, error) {
	staffDepartments := make([]uint64, 0)
	staffs, err := staff.NewStaff().GetByFIds(ctx, staffFids)
	staffIds := make([]string, 0)
	if err != nil {
		return nil, nil, errors.Wrap(err, "permission service func getStaffDepartments query staffs err")
	}
	for _, v := range staffs {
		for _, departmentId := range v.DepartmentsIds {
			departmentIdUint64, err := strconv.ParseUint(departmentId, 10, 64)
			if err != nil {
				return nil, nil, errors.Wrap(err, "permission service func getStaffDepartments parse departmentId err")
			}
			staffDepartments = append(staffDepartments, departmentIdUint64)
		}
		staffIds = append(staffIds, v.Id)
	}
	return staffDepartments, staffIds, nil
}

// AddUser 添加用户
// 判断选择的roles都是存在的，状态正常的
// 往users表插入数据，往users_roles表插入数据
func (s *Service) AddUser(ctx *gin.Context, request requestPermission.AddUserRequest) (*permission.Users, error) {
	exist, err := permission.NewUsersModel().Total(&mysql.QueryBuilder{Where: []mysql.Condition{mysql.CompareCond{Field: "account", Operator: "=", Value: request.Account}}})
	if err != nil {
		return nil, errors.Wrap(err, "permission service func AddUser query users err")
	}
	if exist > 0 {
		return nil, errors.New("账号已存在！")
	}
	totalRoles, err := permission.NewRolesModel().Total(&mysql.QueryBuilder{Where: []mysql.Condition{mysql.InCondition{Field: "id", Values: request.Roles}}})
	if err != nil {
		return nil, errors.Wrap(err, "permission service func AddUser query roles err")
	}
	if totalRoles != int64(len(request.Roles)) {
		return nil, errors.New("角色不存在！")
	}
	if !user_access.CheckPassword(request.Password) {
		return nil, errors.New("密码必须包含大小写字母、数字、特殊符号，长度在8到20位！")
	}
	resUser := permission.Users{}
	dataPermission := request.DataPermission
	if utils.ListContains(request.Roles, 4) {
		dataPermission = *&permission.DataPermission{}
	}
	err = mysql.GetDbClient().Transaction(func(tx *gorm.DB) error {
		userModel := &permission.Users{
			Account:        request.Account,
			Username:       request.Username,
			Password:       s.EncryptedPassword(request.Password),
			Status:         1,
			RuleInfo:       `{"range": 0, "rule_info": ["业务层", "支撑层", "服务层", "系统层", "硬件层"]}`,
			DataPermission: dataPermission,
			//BaseTimestampDSL: mysql.BaseTimestampDSL{
			//	CreatedAt: localtime.Time{},
			//	UpdatedAt: localtime.Time{},
			//},
		}
		if len(request.Username) == 0 {
			userModel.Username = request.Account
		}
		// 创建用户
		resUser, err = permission.NewUsersModel(tx).Create(*userModel)
		if err != nil {
			return err
		}
		if len(request.Roles) > 0 {
			var userRoleModels []permission.UsersRoles
			for _, v := range request.Roles {
				userRoleModels = append(userRoleModels, permission.UsersRoles{
					UserId: resUser.Id,
					RoleId: v,
				})
			}
			if _, err = permission.NewUsersRolesModel(tx).BatchCreate(userRoleModels); err != nil {
				return err
			}
		}
		if len(request.StaffId) > 0 {
			// 删除已有的关联
			_, err = permission.NewUsersStaffsModel(tx).DangerousDelete(&mysql.QueryBuilder{Where: []mysql.Condition{mysql.InCondition{Field: "staff_id", Values: request.StaffId}}})
			if err != nil {
				return errors.Wrap(err, "permission service func AddUser delete users_staffs err")
			}
			var userStaffModels []permission.UsersStaffs
			for _, v := range request.StaffId {
				userStaffModels = append(userStaffModels, permission.UsersStaffs{
					UserId:  resUser.Id,
					StaffId: v,
				})
			}
			if _, err = permission.NewUsersStaffsModel(tx).BatchCreate(userStaffModels); err != nil {
				return err
			}
		}
		return nil
	})
	if err != nil {
		return nil, errors.Wrap(err, "permission service func AddUser create users err")
	}
	return &resUser, nil
}

// UpdateUser 更新用户
func (s *Service) UpdateUser(ctx *gin.Context, request requestPermission.UpdateUserRequest) (*permission.Users, error) {
	exist, err := permission.NewUsersModel().Total(&mysql.QueryBuilder{Where: []mysql.Condition{mysql.CompareCond{Field: "id", Operator: "!=", Value: request.Id}, mysql.CompareCond{Field: "account", Operator: "=", Value: request.Account}}})
	if err != nil {
		return nil, errors.Wrap(err, "permission service func UpdateUser query users err")
	}
	if exist > 0 {
		return nil, errors.New("账号已存在！")
	}
	totalRoles, err := permission.NewRolesModel().Total(&mysql.QueryBuilder{Where: []mysql.Condition{mysql.InCondition{Field: "id", Values: request.Roles}}})
	if err != nil {
		return nil, errors.Wrap(err, "permission service func AddUser query roles err")
	}
	if totalRoles != int64(len(request.Roles)) {
		return nil, errors.New("角色不存在！")
	}
	resUser, err := permission.NewUsersModel().FindByID(request.Id)
	if err != nil {
		return nil, errors.Wrap(err, "permission service func UpdateUser query users err")
	}
	dataPermission := request.DataPermission
	if utils.ListContains(request.Roles, 4) {
		dataPermission = *&permission.DataPermission{}
	}
	err = mysql.GetDbClient().Transaction(func(tx *gorm.DB) error {
		userModel := &permission.Users{
			Account:        request.Account,
			Username:       request.Username,
			Status:         request.Status,
			RuleInfo:       `{"range": 0, "rule_info": ["业务层", "支撑层", "服务层", "系统层", "硬件层"]}`,
			DataPermission: dataPermission,
		}
		if len(request.Password) > 0 && request.Password != resUser.Password {
			if !user_access.CheckPassword(request.Password) {
				return errors.New("密码必须包含大小写字母、数字、特殊符号，长度在8到20位！")
			}
			userModel.Password = s.EncryptedPassword(request.Password)
			userModel.PasswordUpdatedAt = localtime.Now()
		} else {
			userModel.Password = resUser.Password
			userModel.PasswordUpdatedAt = resUser.PasswordUpdatedAt
		}
		if len(request.Username) == 0 {
			userModel.Username = request.Account
		}
		dataMap, err := utils.StructToMap(userModel, "json")
		if err != nil {
			return err
		}
		dataMap["data_permission"], err = json.Marshal(dataMap["data_permission"])
		if err != nil {
			return err
		}
		_, err = permission.NewUsersModel(tx).UpdateByID(request.Id, dataMap)
		if err != nil {
			return err
		}
		_, err = permission.NewUsersRolesModel(tx).DangerousDelete(&mysql.QueryBuilder{Where: []mysql.Condition{mysql.CompareCond{Field: "user_id", Operator: "=", Value: request.Id}}})
		if err != nil {
			return err
		}
		if len(request.Roles) > 0 {
			var userRoleModels []permission.UsersRoles
			for _, v := range request.Roles {
				userRoleModels = append(userRoleModels, permission.UsersRoles{
					UserId: resUser.Id,
					RoleId: v,
				})
			}
			if _, err = permission.NewUsersRolesModel(tx).BatchCreate(userRoleModels); err != nil {
				return err
			}
		}
		_, err = permission.NewUsersStaffsModel(tx).DangerousDelete(&mysql.QueryBuilder{Where: []mysql.Condition{mysql.CompareCond{Field: "user_id", Operator: "=", Value: request.Id}}})
		if len(request.StaffId) > 0 {
			// 删除已有的关联
			_, err = permission.NewUsersStaffsModel(tx).DangerousDelete(&mysql.QueryBuilder{Where: []mysql.Condition{mysql.InCondition{Field: "staff_id", Values: request.StaffId}}})
			if err != nil {
				return errors.Wrap(err, "permission service func AddUser delete users_staffs err")
			}
			var userStaffModels []permission.UsersStaffs
			for _, v := range request.StaffId {
				userStaffModels = append(userStaffModels, permission.UsersStaffs{
					UserId:  resUser.Id,
					StaffId: v,
				})
			}
			if _, err = permission.NewUsersStaffsModel(tx).BatchCreate(userStaffModels); err != nil {
				return err
			}
		}
		return nil
	})
	if err != nil {
		return nil, errors.Wrap(err, "permission service func UpdateUser update users err")
	}
	return &resUser, nil
}

// DeleteUser 删除用户
// 需要同步删除users_roles表，users_api表数据
func (s *Service) DeleteUser(ctx *gin.Context, usersId []uint64) error {
	_, err := permission.NewUsersRolesModel().DangerousDelete(&mysql.QueryBuilder{
		Where: []mysql.Condition{mysql.InCondition{Field: "user_id", Values: usersId}},
	})
	if err != nil {
		return errors.Wrap(err, "permission service func DeleteUser delete users_roles err")
	}
	_, err = permission.NewUsersStaffsModel().DangerousDelete(&mysql.QueryBuilder{
		Where: []mysql.Condition{mysql.InCondition{Field: "user_id", Values: usersId}},
	})
	if err != nil {
		return errors.Wrap(err, "permission service func DeleteUser delete users_staffs err")
	}
	_, err = permission.NewUsersModel().DangerousDelete(&mysql.QueryBuilder{
		Where: []mysql.Condition{mysql.InCondition{Field: "id", Values: usersId}},
	})
	if err != nil {
		return errors.Wrap(err, "permission service func DeleteUser delete users err")
	}
	return nil
}

// AddRole 添加角色
// 往roles表插入数据，往role_menus表插入数据
func (s *Service) AddRole(ctx *gin.Context, request requestPermission.AddRoleRequest) (*permission.Roles, error) {
	dsl := permission.NewRolesModel()
	dslRoleMenu := permission.NewRolesMenusModel()
	// 判断key和name重复
	total, err := dsl.Total(&mysql.QueryBuilder{
		Where: []mysql.Condition{mysql.OrConditions{Conditions: []mysql.Condition{mysql.CompareCond{Field: "name", Operator: "=", Value: request.Name}}}},
	})
	if err != nil {
		return nil, errors.Wrap(err, "permission service func AddRole count exist roles err")
	}
	if total > 0 {
		return nil, errors.New("角色名称已存在")
	}
	// 插入角色数据，插入角色关联的菜单数据
	role, err := dsl.Create(permission.Roles{
		Name:   request.Name,
		Key:    request.Key,
		Sys:    0,
		Status: 1,
		Remark: request.Remark,
	})
	if err != nil {
		return nil, errors.Wrap(err, "permission service func AddRole create roles err")
	}
	var rolesMenus = make([]permission.RolesMenus, 0)
	for _, v := range request.Menus {
		rolesMenus = append(rolesMenus, permission.RolesMenus{
			RoleId: role.Id,
			MenuId: v,
		})
	}
	_, err = dslRoleMenu.BatchCreate(rolesMenus)
	return &role, nil
}

// UpdateRole 更新角色
// 判断key,name重复，id不是当前的
// 更新roles表，删除roles_menus表全部数据，重新插入数据
// 更新users_api表，删除users_api表全部数据，重新插入数据
func (s *Service) UpdateRole(ctx *gin.Context, request requestPermission.UpdateRoleRequest) (*permission.Roles, error) {
	// 判断key,name重复，id不是当前的
	dsl := permission.NewRolesModel()
	dslRoleMenu := permission.NewRolesMenusModel()
	total, err := dsl.Total(&mysql.QueryBuilder{
		Where: []mysql.Condition{mysql.CompareCond{Field: "id", Operator: "!=", Value: request.Id}, mysql.CompareCond{Field: "name", Operator: "=", Value: request.Name}},
	})
	if err != nil {
		return nil, errors.Wrap(err, "permission service func UpdateRole count exist roles err")
	}
	if total > 0 {
		return nil, errors.New("角色名称已存在")
	}
	role, err := dsl.FindByID(request.Id)
	if err != nil {
		return nil, errors.Wrap(err, "permission service func UpdateRole find roles err")
	}
	if role.Sys == 1 {
		return nil, errors.New("系统角色不允许修改")
	}
	var up = permission.Roles{
		Name:   request.Name,
		Key:    request.Key,
		Sys:    0,
		Remark: request.Remark,
	}
	if nil != request.Status {
		up.Status = *request.Status
	}
	_, err = dsl.UpdateByID(request.Id, up)
	if err != nil {
		return nil, errors.Wrap(err, "permission service func UpdateRole update roles err")
	}
	if _, err := dslRoleMenu.DangerousDelete(&mysql.QueryBuilder{Where: []mysql.Condition{mysql.CompareCond{Field: "role_id", Operator: "=", Value: request.Id}}}); err != nil {
		return nil, errors.Wrap(err, "permission service func UpdateRole delete roles_menus err")
	}
	var rolesMenus = make([]permission.RolesMenus, 0)
	for _, v := range request.Menus {
		rolesMenus = append(rolesMenus, permission.RolesMenus{
			RoleId: request.Id,
			MenuId: v,
		})
	}
	_, err = dslRoleMenu.BatchCreate(rolesMenus)
	if nil != err {
		return nil, errors.Wrap(err, "permission service func UpdateRole batch create roles_menus err")
	}
	totalUsers, err := permission.NewUsersRolesModel().Total(&mysql.QueryBuilder{Where: []mysql.Condition{mysql.CompareCond{Field: "role_id", Operator: "=", Value: request.Id}}})
	if err != nil {
		return nil, errors.Wrap(err, "permission service func UpdateRole count users_roles err")
	}
	role.TotalUsers = totalUsers
	role.Name = request.Name
	role.Remark = request.Remark
	role.Key = request.Key
	return &role, nil
}

// DeleteRole 删除角色
// 需要加限制条件sys!=1, 仅可删除非系统内置角色，即用户自定义角色
// 需要判断角色下是否有用户关联，即users_roles表，如果有关联，则不允许删除，提示用户需要先调用用户所属角色
func (s *Service) DeleteRole(ctx *gin.Context, id []uint64) error {
	dsl := permission.NewRolesModel()
	dslRoleMenu := permission.NewRolesMenusModel()
	dslRoleUser := permission.NewUsersRolesModel()
	totalUsers, err := dslRoleUser.Total(&mysql.QueryBuilder{Where: []mysql.Condition{mysql.InCondition{Field: "role_id", Values: id}}})
	if err != nil {
		return errors.Wrap(err, "permission service func DeleteRole count users_roles err")
	}
	if totalUsers > 0 {
		return errors.New("角色下有用户关联，不允许删除")
	}
	roles, err := dsl.Query(&mysql.QueryBuilder{Where: []mysql.Condition{mysql.InCondition{Field: "id", Values: id}}})
	if err != nil {
		return errors.Wrap(err, "permission service func DeleteRole find roles err")
	}
	for _, v := range roles {
		if v.Sys == 1 {
			return errors.New("系统内置角色不允许删除")
		}
	}
	if _, err := dsl.DangerousDelete(&mysql.QueryBuilder{Where: []mysql.Condition{mysql.InCondition{Field: "id", Values: id}}}); err != nil {
		return errors.Wrap(err, "permission service func DeleteRole delete roles err")
	}
	if _, err := dslRoleMenu.DangerousDelete(&mysql.QueryBuilder{Where: []mysql.Condition{mysql.InCondition{Field: "role_id", Values: id}}}); err != nil {
		return errors.Wrap(err, "permission service func DeleteRole delete roles_menus err")
	}
	return nil
}

// BatchSwitchUsersStatus 批量启用或禁用用户
// status: 1启用 2禁用
func (s *Service) BatchSwitchUsersStatus(ctx *gin.Context, userIds []uint64, status int) error {
	_, err := permission.NewUsersModel().Update(&mysql.QueryBuilder{Where: []mysql.Condition{mysql.InCondition{Field: "id", Values: userIds}}}, map[string]interface{}{"status": status})
	return err
}

// GetUserApis 查询用户拥有权限的API集合
// 用于中间件限制接口权限，202502228版本未实现，前端人力不足，未提供接口关联关系
func (s *Service) GetUserApis(ctx *gin.Context, userId uint64) ([]string, error) {
	dsl := permission.NewMenusModel()
	all, err := dsl.Query(&mysql.QueryBuilder{
		Where: []mysql.Condition{
			mysql.CompareCond{
				Field:    "type",
				Operator: "=",
				Value:    4,
			},
			mysql.InCondition{
				Field: "id",
				// 子查询，查询用户拥有权限的所有菜单ID
				Values: &mysql.QueryBuilder{
					Table:  new(permission.RolesMenus).TableName(),
					Select: []string{"menu_id"},
					Where: []mysql.Condition{
						mysql.InCondition{
							Field: "role_id",
							// 子查询，查询用户所有的角色ID
							Values: &mysql.QueryBuilder{
								Table:  new(permission.UsersRoles).TableName(),
								Select: []string{"role_id"},
								Where: []mysql.Condition{
									mysql.CompareCond{
										Field:    "user_id",
										Operator: "=",
										Value:    userId,
									},
								},
							},
						},
					},
				},
			},
		},
	})
	if err != nil {
		return nil, errors.Wrap(err, "permission service func GetUserApis query roles err")
	}
	return utils.ListColumn[string, permission.Menus](all, func(menus permission.Menus) string {
		return menus.Path
	}), nil
}

// GetLicenseMenus 获取产品授权模块隐藏菜单
func (s *Service) GetLicenseMenus(ctx *gin.Context) (map[string]struct{}, map[string]struct{}, error) {
	// 获取产品授权模块
	isLocal := cfg.LoadCommon().Local

	isTest := testcommon.IsTest()
	isDev := license2.CheckDevModel(cfg.LoadCommon().Env)
	// // 测试环境或开发环境不获取产品授权模块
	if (isTest || isDev) && isLocal {
		return map[string]struct{}{}, map[string]struct{}{"AssetRelevance": {}}, nil
	}
	l := license.GetLicense()
	models, err := l.GetProductModels()
	if err != nil {
		logger.Errorf("获取产品授权模块失败: %v\n", err)
		return nil, nil, err
	}
	logger.Infof("产品授权模块: %v\n", models)
	// local 具有最高优先级
	if isLocal {
		isDev = false
		isTest = false
	}
	// 根据产品授权模块和环境获取隐藏菜单
	menuByLicense, err := system_info.GetMenusByLicense(models, isDev, isTest)
	if err != nil {
		logger.Errorf("获取隐藏菜单失败: %v\n", err)
		return nil, nil, err
	}
	var disable = make(map[string]struct{})
	var enable = make(map[string]struct{})
	for _, v := range menuByLicense {
		if strings.Contains(v, "disable_") {
			disable[strings.ReplaceAll(v, "disable_", "")] = struct{}{}
		}
		if strings.Contains(v, "enable_") {
			enable[strings.ReplaceAll(v, "enable_", "")] = struct{}{}
		}
	}
	return disable, enable, nil
}
