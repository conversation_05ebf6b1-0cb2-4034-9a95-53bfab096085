package permission

import (
	"errors"
	requestPermission "fobrain/fobrain/app/request/permission"
	"fobrain/fobrain/common/request"
	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/permission"
	"fobrain/pkg/utils"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"github.com/agiledragon/gomonkey/v2"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/gin-gonic/gin"
	"github.com/olivere/elastic/v7"
	"github.com/stretchr/testify/assert"
)

func TestService_GetUserApis(t *testing.T) {
	mockDb := testcommon.InitSqlMock()
	defer mockDb.Close()
	//SELECT * FROM `menus` WHERE `type` = ? AND `id` IN (SELECT menu_id FROM `roles_menus` WHERE `role_id` IN (SELECT role_id FROM `users_roles` WHERE `user_id` = ?))
	mockDb.ExpectQuery("SELECT * FROM `menus` WHERE `type` = ? AND `id` IN (SELECT menu_id FROM `roles_menus` WHERE `role_id` IN (SELECT role_id FROM `users_roles` WHERE `user_id` = ?))").
		WithArgs(4, 1).
		WillReturnRows(mockDb.NewRows([]string{"id", "parent_id", "title", "path", "icon", "sort", "type", "status"}).
			AddRow(1, 0, "系统管理", "/system", "el-icon-setting", 1, 1, 1))
	s := NewPermissionService()
	apis, err := s.GetUserApis(nil, 1)
	if err != nil {
		t.Error(err)
	}
	assert.NotEmpty(t, apis)
}

func TestService_GetMenusTreeForRole(t *testing.T) {
	//testcommon.SetTestEnv(false)
	mockDb := testcommon.InitSqlMock()
	defer mockDb.Close()
	// 2025-02-25 19:03:05.005 INFO SQL mysql/dsl.go:162 [3.774ms] [rows:1] SELECT * FROM `roles_menus` WHERE `role_id` = 1
	// 2025-02-25 19:03:05.088 INFO SQL mysql/dsl.go:162 [2.533ms] [rows:184] SELECT * FROM `menus` WHERE `type` != 4
	mockDb.ExpectQuery("SELECT * FROM `roles` WHERE `id` = ?").
		WithArgs(1).
		WillReturnRows(mockDb.NewRows([]string{"id", "name", "key"}).
			AddRow(1, "二级管理员", "second"))
	mockDb.ExpectQuery("SELECT * FROM `roles_menus` WHERE `role_id` = ?").
		WithArgs(1).
		WillReturnRows(mockDb.NewRows([]string{"id", "role_id", "menu_id"}).
			AddRow(1, 1, 1))
	mockDb.ExpectQuery("SELECT * FROM `menus` WHERE `type` != ? AND `name` != ? ORDER BY sort asc").
		WithArgs(4, "Setting").
		WillReturnRows(mockDb.NewRows([]string{"id", "parent_id", "name", "path", "component", "icon", "sort", "type", "status", "created_at", "updated_at"}).
			AddRow(1, 0, "Dashboard", "/dashboard", "dashboard", "dashboard", 1, 1, 1, "2023-02-01 00:00:00.000", "2023-02-01 00:00:00.000").
			AddRow(2, 0, "Documentation", "/docs", "docs", "documentation", 2, 1, 1, "2023-02-01 00:00:00.000", "2023-02-01 00:00:00.000").
			AddRow(3, 0, "Example", "/example", "example", "example", 3, 1, 1, "2023-02-01 00:00:00.000", "2023-02-01 00:00:00.000").
			AddRow(4, 0, "Tools", "/tools", "tools", "tools", 4, 1, 1, "2023-02-01 00:00:00.000", "2023-02-01 00:00:00.000"))
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)

	s := NewPermissionService()
	p := gomonkey.ApplyMethodReturn(s, "GetLicenseMenus", map[string]struct{}{}, map[string]struct{}{}, nil)
	_, err := s.GetMenusTreeForRole(c, 1)
	p.Reset()
	assert.NoError(t, err)
}

func TestService_GetMenusTree(t *testing.T) {
	//SELECT * FROM `menus` ORDER BY sort asc
	mockDb := testcommon.InitSqlMock()
	defer mockDb.Close()
	mockDb.ExpectQuery("SELECT * FROM `menus` ORDER BY sort asc").
		WillReturnRows(mockDb.NewRows([]string{"id", "parent_id", "name", "path", "component", "icon", "sort", "type", "status", "created_at", "updated_at"}).
			AddRow(1, 0, "Dashboard", "/dashboard", "dashboard", "dashboard", 1, 1, 1, "2023-02-01 00:00:00.000", "2023-02-01 00:00:00.000"))

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)

	s := NewPermissionService()
	p := gomonkey.ApplyMethodReturn(s, "GetLicenseMenus", map[string]struct{}{}, map[string]struct{}{}, nil)
	_, err := s.GetMenusTree(c)
	p.Reset()
	assert.NoError(t, err)
}

func TestService_GetUserMenus(t *testing.T) {
	mockDb := testcommon.InitSqlMock()
	defer mockDb.Close()

	// 2025-02-25 18:39:05.352 INFO SQL mysql/dsl.go:162 [0.684ms] [rows:1] SELECT * FROM `users_roles` WHERE `user_id` = 1
	// 2025-02-25 18:39:05.356 INFO SQL mysql/dsl.go:197 [3.861ms] [rows:1] SELECT count(*) FROM `roles` WHERE `id` IN (1) AND `key` = 'admin'
	// 2025-02-25 18:39:05.358 INFO SQL mysql/dsl.go:162 [2.400ms] [rows:66] SELECT * FROM `menus` WHERE `type` IN (1,2)
	// 2025-02-25 18:39:05.360 INFO SQL mysql/dsl.go:162 [1.680ms] [rows:118] SELECT * FROM `menus` WHERE `type` = 3
	mockDb.ExpectQuery("SELECT * FROM `users_roles` WHERE `user_id` = ?").
		WithArgs(1).
		WillReturnRows(mockDb.NewRows([]string{"id", "user_id", "role_id"}).
			AddRow(1, 1, 1))
	mockDb.ExpectQuery("SELECT count(*) FROM `roles` WHERE `id` IN (?) AND `key` = ?").
		WithArgs(1, "admin").
		WillReturnRows(mockDb.NewRows([]string{"count"}).
			AddRow(1))
	mockDb.ExpectQuery("SELECT * FROM `menus` WHERE `type` IN (?,?)").
		WithArgs(1, 2).
		WillReturnRows(mockDb.NewRows([]string{"id", "parent_id", "title", "path", "icon", "type", "sort", "status"}).
			AddRow(1, 0, "系统管理", "/system", "el-icon-setting", 1, 1, 1).
			AddRow(2, 1, "用户管理", "/system/user", "el-icon-user", 2, 1, 1).
			AddRow(3, 1, "角色管理", "/system/role", "el-icon-user-solid", 3, 1, 1).
			AddRow(4, 1, "菜单管理", "/system/menu", "el-icon-menu", 4, 1, 1))
	mockDb.ExpectQuery("SELECT * FROM `menus` WHERE `type` = ?").
		WithArgs(3).
		WillReturnRows(mockDb.NewRows([]string{"id", "parent_id", "title", "path", "icon", "type", "sort", "status"}).
			AddRow(5, 4, "添加", "/system", "el-icon-setting", 3, 1, 1).
			AddRow(6, 4, "编辑", "/system/user", "el-icon-user", 3, 1, 1))
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Set("user_id", uint64(1))
	req := httptest.NewRequest("GET", "/api/v1/user_access/my_menus_tree", nil)
	req.Header.Set("Content-Type", "application/json")
	c.Request = req
	s := NewPermissionService()
	p := gomonkey.ApplyMethodReturn(s, "GetLicenseMenus", map[string]struct{}{}, map[string]struct{}{}, nil)
	_, err := s.GetUserMenus(c, 1)
	assert.NoError(t, err)
	p.Reset()
	// 2025-02-25 18:39:05.352 INFO SQL mysql/dsl.go:162 [0.684ms] [rows:1] SELECT * FROM `users_roles` WHERE `user_id` = 1
	// 2025-02-25 18:39:05.356 INFO SQL mysql/dsl.go:197 [3.861ms] [rows:1] SELECT count(*) FROM `roles` WHERE `id` IN (1) AND `key` = 'admin'
	// 2025-02-25 18:39:05.358 INFO SQL mysql/dsl.go:162 [2.400ms] [rows:66] SELECT * FROM `menus` WHERE `type` IN (1,2)
	// 2025-02-25 18:39:05.360 INFO SQL mysql/dsl.go:162 [1.680ms] [rows:118] SELECT * FROM `menus` WHERE `type` = 3
	mockDb.ExpectQuery("SELECT * FROM `users_roles` WHERE `user_id` = ?").
		WithArgs(1).
		WillReturnRows(mockDb.NewRows([]string{"id", "user_id", "role_id"}).
			AddRow(1, 1, 1))
	mockDb.ExpectQuery("SELECT count(*) FROM `roles` WHERE `id` IN (?) AND `key` = ?").
		WithArgs(1, "admin").
		WillReturnRows(mockDb.NewRows([]string{"count"}).
			AddRow(0))
	mockDb.ExpectQuery("SELECT * FROM `users_roles` WHERE `user_id` = ?").
		WithArgs(1).
		WillReturnRows(mockDb.NewRows([]string{"id", "user_id", "role_id"}).
			AddRow(1, 1, 1))
	// SELECT * FROM `roles_menus` WHERE `role_id` IN (?)
	mockDb.ExpectQuery("SELECT * FROM `roles_menus` WHERE `role_id` IN (?)").
		WithArgs(1).
		WillReturnRows(mockDb.NewRows([]string{"id", "role_id", "menu_id"}).
			AddRow(1, 1, 1).AddRow(2, 1, 2).AddRow(3, 1, 3).AddRow(4, 1, 4))
	// SELECT * FROM `menus` WHERE `id` IN (1,2,3,4) AND `type` IN (1,2)
	mockDb.ExpectQuery("SELECT * FROM `menus` WHERE `id` IN (?,?,?,?) AND `type` IN (?,?)").
		WithArgs(1, 2, 3, 4, 1, 2).
		WillReturnRows(mockDb.NewRows([]string{"id", "parent_id", "title", "path", "icon", "type", "sort", "status"}).
			AddRow(1, 0, "系统管理", "/system", "el-icon-setting", 1, 1, 1).
			AddRow(2, 1, "用户管理", "/system/user", "el-icon-user", 2, 1, 1).
			AddRow(4, 1, "菜单管理", "/system/menu", "el-icon-menu", 2, 1, 1))
	// SELECT * FROM `menus` WHERE `id` IN (1,2,3,4) AND `type` IN (3)
	mockDb.ExpectQuery("SELECT * FROM `menus` WHERE `id` IN (?,?,?,?) AND `type` IN (?)").
		WithArgs(1, 2, 3, 4, 3).
		WillReturnRows(mockDb.NewRows([]string{"id", "parent_id", "title", "path", "icon", "type", "sort", "status"}).
			AddRow(3, 1, "角色管理", "/system/role", "el-icon-user-solid", 3, 1, 1))

	w = httptest.NewRecorder()
	c, _ = gin.CreateTestContext(w)
	c.Set("user_id", uint64(1))
	req = httptest.NewRequest("GET", "/api/v1/user_access/my_menus_tree", nil)
	req.Header.Set("Content-Type", "application/json")
	c.Request = req
	s = NewPermissionService()
	p = gomonkey.ApplyMethodReturn(s, "GetLicenseMenus", map[string]struct{}{}, map[string]struct{}{}, nil)
	_, err = s.GetUserMenus(c, 1)
	p.Reset()
	assert.NoError(t, err)
}

func TestService_GetUserRoles(t *testing.T) {
	mockDb := testcommon.InitSqlMock()
	defer mockDb.Close()

	// SELECT * FROM `users_roles` WHERE `user_id` = ?
	mockDb.ExpectQuery("SELECT * FROM `users_roles` WHERE `user_id` IN (?,?)").
		WithArgs(1, 2).
		WillReturnRows(mockDb.NewRows([]string{"id", "user_id", "role_id"}).
			AddRow(1, 1, 2))
	// SELECT `id`,`name`,`key`,`sys` FROM `roles` WHERE `id` IN (?)
	mockDb.ExpectQuery("SELECT `id`,`name`,`key`,`sys` FROM `roles` WHERE `id` IN (?)").
		WithArgs(2).
		WillReturnRows(mockDb.NewRows([]string{"id", "name", "key", "sys"}).
			AddRow(2, "admin", "admin", 1))
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Set("user_id", uint64(1))
	s := NewPermissionService()
	_, err := s.getUsersRoles(c, []uint64{1, 2})
	assert.NoError(t, err)
}
func TestService_DeleteRole(t *testing.T) {
	mockDb := testcommon.InitSqlMock()
	defer mockDb.Close()
	// SELECT count(*) FROM `users_roles` WHERE `role_id` IN (?,?)
	mockDb.ExpectQuery("SELECT count(*) FROM `users_roles` WHERE `role_id` IN (?,?)").
		WithArgs(1, 2).
		WillReturnRows(mockDb.NewRows([]string{"count"}).
			AddRow(0))
	// SELECT * FROM `roles` WHERE `id` IN (?,?)
	mockDb.ExpectQuery("SELECT * FROM `roles` WHERE `id` IN (?,?)").
		WithArgs(1, 2).
		WillReturnRows(mockDb.NewRows([]string{"id", "name", "key", "sys"}).
			AddRow(1, "admin", "admin", 0).
			AddRow(2, "normal", "normal", 0))
	mockDb.ExpectBegin()
	//  DELETE FROM `roles` WHERE `id` IN (1,2)
	mockDb.ExpectExec("DELETE FROM `roles` WHERE `id` IN (?,?)").
		WithArgs(1, 2).
		WillReturnResult(sqlmock.NewResult(1, 2))
	mockDb.ExpectCommit()
	mockDb.ExpectBegin()
	// DELETE FROM `roles_menus` WHERE `role_id` IN (?,?)
	mockDb.ExpectExec("DELETE FROM `roles_menus` WHERE `role_id` IN (?,?)").
		WithArgs(1, 2).
		WillReturnResult(sqlmock.NewResult(1, 2))
	mockDb.ExpectCommit()

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Set("user_id", uint64(1))
	s := NewPermissionService()
	err := s.DeleteRole(c, []uint64{1, 2})
	assert.NoError(t, err)
}
func TestService_UpdateRole(t *testing.T) {
	mockDb := testcommon.InitSqlMock()
	defer mockDb.Close()

	mockDb.ExpectQuery("SELECT count(*) FROM `roles` WHERE `id` != ? AND `name` = ?").
		WithArgs(3, "自动测试3").
		WillReturnRows(mockDb.NewRows([]string{"count"}).
			AddRow(0))
	mockDb.ExpectQuery("SELECT * FROM `roles` WHERE `id` = ? ORDER BY `roles`.`id` LIMIT 1").
		WithArgs(3).
		WillReturnRows(mockDb.NewRows([]string{"id", "name", "key", "status", "remark", "sys"}).
			AddRow(3, "自动测试3", "autotest", 1, "单测3", 0))
	mockDb.ExpectBegin()
	mockDb.ExpectExec("UPDATE `roles` SET `updated_at`=?,`name`=?,`key`=?,`status`=?,`remark`=? WHERE `id` = ?").
		WithArgs(sqlmock.AnyArg(), "自动测试3", "autotest", 1, "单测3", 3).
		WillReturnResult(sqlmock.NewResult(1, 1))
	mockDb.ExpectCommit()
	mockDb.ExpectBegin()
	mockDb.ExpectExec("DELETE FROM `roles_menus` WHERE `role_id` = ?").
		WithArgs(3).
		WillReturnResult(sqlmock.NewResult(1, 1))
	mockDb.ExpectCommit()
	mockDb.ExpectBegin()
	mockDb.ExpectExec("INSERT INTO `roles_menus` (`created_at`,`updated_at`,`role_id`,`menu_id`) VALUES (?,?,?,?)").
		WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), 3, 2).
		WillReturnResult(sqlmock.NewResult(1, 1))
	mockDb.ExpectCommit()
	mockDb.ExpectQuery("SELECT count(*) FROM `users_roles` WHERE `role_id` = ?").
		WithArgs(3).
		WillReturnRows(mockDb.NewRows([]string{"count"}).
			AddRow(0))

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Set("user_id", uint64(1))
	s := NewPermissionService()
	_, err := s.UpdateRole(c, requestPermission.UpdateRoleRequest{
		Id:     3,
		Status: utils.ToPointer[int](1),
		AddRoleRequest: requestPermission.AddRoleRequest{
			Name:   "自动测试3",
			Key:    "autotest",
			Menus:  []uint64{2},
			Remark: "单测3",
		},
	})
	assert.NoError(t, err)
}
func TestService_DataPermission(t *testing.T) {
	dataPermission := permission.DataPermission{
		PrivateAndPublicSame: false,
		Public: permission.DataPermissionItem{
			DataLevel:        []int8{2, 4, 6, 7, 10, 11},
			ExtraDepartments: []uint64{1, 2},
		},
		Private: permission.DataPermissionItem{
			DataLevel:        []int8{3, 5, 8, 9, 10, 11},
			ExtraDepartments: []uint64{1, 2},
		},
	}
	t.Log(utils.AnyToStr(dataPermission))
}
func TestService_GetPermissionsQuery(t *testing.T) {
	t.Run("SourceTypeAsset", func(t *testing.T) {
		mockServer := testcommon.NewMockServer()
		defer mockServer.Close()
		mockServer.Register("staff/_search", []*elastic.SearchHit{
			{
				Id:     "1",
				Source: []byte(`{"name":"example"}`),
			},
		})
		// SELECT * FROM `users_roles` WHERE `user_id` = ?
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()
		mockDb.ExpectQuery("SELECT * FROM `users_roles` WHERE `user_id` = ?").
			WithArgs(1).
			WillReturnRows(mockDb.NewRows([]string{"id", "user_id", "role_id"}).
				AddRow(1, 1, 2))
		// SELECT count(*) FROM `roles` WHERE `id` IN (?) AND `key` = ?
		mockDb.ExpectQuery("SELECT count(*) FROM `roles` WHERE `id` IN (?) AND `key` = ?").
			WithArgs(2, "admin").
			WillReturnRows(mockDb.NewRows([]string{"count"}).
				AddRow(0))
		// SELECT * FROM `users` WHERE `id` = ? ORDER BY `users`.`id` LIMIT 1
		mockDb.ExpectQuery("SELECT * FROM `users` WHERE `id` = ? ORDER BY `users`.`id` LIMIT 1").
			WithArgs(1).
			WillReturnRows(mockDb.NewRows([]string{"id", "account", "username", "password", "status", "created_at", "updated_at", "data_permission"}).
				AddRow(1, "admin", "<EMAIL>", "admin", 1, time.Now(), time.Now(), `{"private_and_public_same":false,"no_master":true,"private":{"data_level":[3,5,8,9,10,11],"extra_departments":[1,2]},"public":{"data_level":[2,4,6,7,10,11],"extra_departments":[1,2]}}`))
		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)
		c.Set("user_id", uint64(1))
		c.Set("staff_ids", []string{"4ef89fb5cc8a8e03a18e0a48233754ff", "e40487520f21243daa65f93764aec0ca"})
		s := NewPermissionService()
		_, err := s.ProcessElasticsearchQuery(c, data_source.SourceTypeAsset)
		assert.NoError(t, err)
	})
	t.Run("SourceTypeAsset", func(t *testing.T) {
		mockServer := testcommon.NewMockServer()
		defer mockServer.Close()
		mockServer.Register("staff/_search", []*elastic.SearchHit{
			{
				Id:     "1",
				Source: []byte(`{"name":"example"}`),
			},
		})
		// SELECT * FROM `users_roles` WHERE `user_id` = ?
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()
		mockDb.ExpectQuery("SELECT * FROM `users_roles` WHERE `user_id` = ?").
			WithArgs(1).
			WillReturnRows(mockDb.NewRows([]string{"id", "user_id", "role_id"}).
				AddRow(1, 1, 2))
		// SELECT count(*) FROM `roles` WHERE `id` IN (?) AND `key` = ?
		mockDb.ExpectQuery("SELECT count(*) FROM `roles` WHERE `id` IN (?) AND `key` = ?").
			WithArgs(2, "admin").
			WillReturnRows(mockDb.NewRows([]string{"count"}).
				AddRow(0))
		// SELECT * FROM `users` WHERE `id` = ? ORDER BY `users`.`id` LIMIT 1
		mockDb.ExpectQuery("SELECT * FROM `users` WHERE `id` = ? ORDER BY `users`.`id` LIMIT 1").
			WithArgs(1).
			WillReturnRows(mockDb.NewRows([]string{"id", "account", "username", "password", "status", "created_at", "updated_at", "data_permission"}).
				AddRow(1, "admin", "<EMAIL>", "admin", 1, time.Now(), time.Now(), `{"private_and_public_same":false,"no_master":false,"private":{"data_level":[3,5,8,9,10,11],"extra_departments":[1,2]},"public":{"data_level":[2,4,6,7,10,11],"extra_departments":[1,2]}}`))
		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)
		c.Set("user_id", uint64(1))
		c.Set("staff_ids", []string{"4ef89fb5cc8a8e03a18e0a48233754ff", "e40487520f21243daa65f93764aec0ca"})
		s := NewPermissionService()
		_, err := s.ProcessElasticsearchQuery(c, data_source.SourceTypeDevice)
		assert.NoError(t, err)
	})
	t.Run("SourceTypeAsset", func(t *testing.T) {
		mockServer := testcommon.NewMockServer()
		defer mockServer.Close()
		mockServer.Register("staff/_search", []*elastic.SearchHit{
			{
				Id:     "1",
				Source: []byte(`{"name":"example"}`),
			},
		})
		// SELECT * FROM `users_roles` WHERE `user_id` = ?
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()
		mockDb.ExpectQuery("SELECT * FROM `users_roles` WHERE `user_id` = ?").
			WithArgs(1).
			WillReturnRows(mockDb.NewRows([]string{"id", "user_id", "role_id"}).
				AddRow(1, 1, 2))
		// SELECT count(*) FROM `roles` WHERE `id` IN (?) AND `key` = ?
		mockDb.ExpectQuery("SELECT count(*) FROM `roles` WHERE `id` IN (?) AND `key` = ?").
			WithArgs(2, "admin").
			WillReturnRows(mockDb.NewRows([]string{"count"}).
				AddRow(0))
		// SELECT * FROM `users` WHERE `id` = ? ORDER BY `users`.`id` LIMIT 1
		mockDb.ExpectQuery("SELECT * FROM `users` WHERE `id` = ? ORDER BY `users`.`id` LIMIT 1").
			WithArgs(1).
			WillReturnRows(mockDb.NewRows([]string{"id", "account", "username", "password", "status", "created_at", "updated_at", "data_permission"}).
				AddRow(1, "admin", "<EMAIL>", "admin", 1, time.Now(), time.Now(), `{"private_and_public_same":false,"no_master":false,"private":{"data_level":[3,5,8,9,10,11],"extra_departments":[1,2]},"public":{"data_level":[2,4,6,7,10,11],"extra_departments":[1,2]}}`))
		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)
		c.Set("user_id", uint64(1))
		c.Set("staff_ids", []string{"4ef89fb5cc8a8e03a18e0a48233754ff", "e40487520f21243daa65f93764aec0ca"})
		s := NewPermissionService()
		_, err := s.ProcessElasticsearchQuery(c, data_source.SourceTypePoc)
		assert.NoError(t, err)
	})

	t.Run("SourceTypeAsset", func(t *testing.T) {
		mockServer := testcommon.NewMockServer()
		defer mockServer.Close()
		mockServer.Register("staff/_search", []*elastic.SearchHit{
			{
				Id:     "1",
				Source: []byte(`{"name":"example"}`),
			},
		})
		// SELECT * FROM `users_roles` WHERE `user_id` = ?
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()
		mockDb.ExpectQuery("SELECT * FROM `users_roles` WHERE `user_id` = ?").
			WithArgs(1).
			WillReturnRows(mockDb.NewRows([]string{"id", "user_id", "role_id"}).
				AddRow(1, 1, 2))
		// SELECT count(*) FROM `roles` WHERE `id` IN (?) AND `key` = ?
		mockDb.ExpectQuery("SELECT count(*) FROM `roles` WHERE `id` IN (?) AND `key` = ?").
			WithArgs(2, "admin").
			WillReturnRows(mockDb.NewRows([]string{"count"}).
				AddRow(0))
		// SELECT * FROM `users` WHERE `id` = ? ORDER BY `users`.`id` LIMIT 1
		mockDb.ExpectQuery("SELECT * FROM `users` WHERE `id` = ? ORDER BY `users`.`id` LIMIT 1").
			WithArgs(1).
			WillReturnRows(mockDb.NewRows([]string{"id", "account", "username", "password", "status", "created_at", "updated_at", "data_permission"}).
				AddRow(1, "admin", "<EMAIL>", "admin", 1, time.Now(), time.Now(), `{"private_and_public_same":false,"no_master":false,"private":{"data_level":[3,5,8,9,10,11],"extra_departments":[1,2]},"public":{"data_level":[2,4,6,7,10,11],"extra_departments":[1,2]}}`))
		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)
		c.Set("user_id", uint64(1))
		c.Set("staff_ids", []string{"4ef89fb5cc8a8e03a18e0a48233754ff", "e40487520f21243daa65f93764aec0ca"})
		s := NewPermissionService()
		_, err := s.ProcessElasticsearchQuery(c, data_source.SourceTypeInternetAsset)
		assert.NoError(t, err)
	})

	t.Run("SourceTypeAsset", func(t *testing.T) {
		mockServer := testcommon.NewMockServer()
		defer mockServer.Close()
		mockServer.Register("staff/_search", []*elastic.SearchHit{
			{
				Id:     "1",
				Source: []byte(`{"name":"example"}`),
			},
		})
		// SELECT * FROM `users_roles` WHERE `user_id` = ?
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()
		mockDb.ExpectQuery("SELECT * FROM `users_roles` WHERE `user_id` = ?").
			WithArgs(1).
			WillReturnRows(mockDb.NewRows([]string{"id", "user_id", "role_id"}).
				AddRow(1, 1, 2))
		// SELECT count(*) FROM `roles` WHERE `id` IN (?) AND `key` = ?
		mockDb.ExpectQuery("SELECT count(*) FROM `roles` WHERE `id` IN (?) AND `key` = ?").
			WithArgs(2, "admin").
			WillReturnRows(mockDb.NewRows([]string{"count"}).
				AddRow(0))
		// SELECT * FROM `users` WHERE `id` = ? ORDER BY `users`.`id` LIMIT 1
		mockDb.ExpectQuery("SELECT * FROM `users` WHERE `id` = ? ORDER BY `users`.`id` LIMIT 1").
			WithArgs(1).
			WillReturnRows(mockDb.NewRows([]string{"id", "account", "username", "password", "status", "created_at", "updated_at", "data_permission"}).
				AddRow(1, "admin", "<EMAIL>", "admin", 1, time.Now(), time.Now(), `{"private_and_public_same":false,"no_master":false,"private":{"data_level":[3,5,8,9,10,11],"extra_departments":[1,2]},"public":{"data_level":[2,4,6,7,10,11],"extra_departments":[1,2]}}`))
		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)
		c.Set("user_id", uint64(1))
		c.Set("staff_ids", []string{"4ef89fb5cc8a8e03a18e0a48233754ff", "e40487520f21243daa65f93764aec0ca"})
		s := NewPermissionService()
		_, err := s.ProcessElasticsearchQuery(c, data_source.SourceTypeIntranetAsset)
		assert.NoError(t, err)
	})

}

func TestService_CreateUser(t *testing.T) {
	mockDb := testcommon.InitSqlMock()
	defer mockDb.Close()
	mockDb.ExpectQuery("SELECT count(*) FROM `users` WHERE `account` = ?").
		WithArgs("example").WillReturnRows(mockDb.NewRows([]string{"count"}).
		AddRow(0))

	mockDb.ExpectQuery("SELECT count(*) FROM `roles` WHERE `id` IN (?)").
		WithArgs(1).WillReturnRows(mockDb.NewRows([]string{"count"}).
		AddRow(1))

	mockDb.ExpectBegin()
	mockDb.ExpectExec("INSERT INTO `users` (`created_at`,`updated_at`,`username`,`account`,`password`,`password_updated_at`,`status`,`rule_info`,`data_permission`) VALUES (?,?,?,?,?,?,?,?,?)").
		WillReturnResult(sqlmock.NewResult(1, 1))
	mockDb.ExpectExec("INSERT INTO `users_roles` (`created_at`,`updated_at`,`user_id`,`role_id`) VALUES (?,?,?,?)").
		WillReturnResult(sqlmock.NewResult(1, 1))

	// DELETE FROM `users_staffs` WHERE `user_id` IN (?,?)
	mockDb.ExpectExec("DELETE FROM `users_staffs` WHERE `staff_id` IN (?)").WithArgs("example").
		WillReturnResult(sqlmock.NewResult(1, 2))

	mockDb.ExpectExec("INSERT INTO `users_staffs` (`created_at`,`updated_at`,`user_id`,`staff_id`) VALUES (?,?,?,?)").
		WillReturnResult(sqlmock.NewResult(1, 1))
	mockDb.ExpectCommit()

	req := httptest.NewRequest("POST", "/api/v1/user_access/create_user", strings.NewReader(`{"account":"example", "username":"example", "password":"Flower.123", "roles":[1], "staff_id":["example"]}`))
	req.Header.Set("Content-Type", "application/json")

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Set("user_id", uint64(1))
	s := NewPermissionService()
	_, err := s.AddUser(c, requestPermission.AddUserRequest{
		Account:  "example",
		Username: "example",
		Password: "Flower.123",
		Roles:    []uint64{1},
		StaffId:  []string{"example"},
	})
	assert.NoError(t, err)
}

func TestService_UpdateUser(t *testing.T) {
	mockDb := testcommon.InitSqlMock()
	defer mockDb.Close()
	mockDb.ExpectQuery("SELECT count(*) FROM `users` WHERE `id` != ? AND `account` = ?").WithArgs(26, "newAccout5").
		WillReturnRows(mockDb.NewRows([]string{"count"}).AddRow(0))
	mockDb.ExpectQuery("SELECT count(*) FROM `roles` WHERE `id` IN (?)").WithArgs(1).
		WillReturnRows(mockDb.NewRows([]string{"count"}).AddRow(1))

	mockDb.ExpectQuery("SELECT * FROM `users` WHERE `id` = ? ORDER BY `users`.`id` LIMIT 1").WithArgs(26).
		WillReturnRows(sqlmock.NewRows([]string{"id", "account", "username", "password", "role_id", "status"}).
			AddRow(26, "abel", "abel", "abel", 1, 1))

	mockDb.ExpectBegin()
	mockDb.ExpectExec("UPDATE `users` SET").WillReturnResult(sqlmock.NewResult(1, 1))
	mockDb.ExpectExec("DELETE FROM `users_roles` WHERE `user_id` = ?").WithArgs(26).
		WillReturnResult(sqlmock.NewResult(1, 1))
	mockDb.ExpectExec("INSERT INTO `users_roles` (`created_at`,`updated_at`,`user_id`,`role_id`) VALUES (?,?,?,?)").
		WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), 26, 1). // 这里使用 AnyArg() 忽略时间戳
		WillReturnResult(sqlmock.NewResult(1, 1))
	mockDb.ExpectExec("DELETE FROM `users_staffs` WHERE `user_id` = ?").WithArgs(26).
		WillReturnResult(sqlmock.NewResult(1, 1))

	// DELETE FROM `users_staffs` WHERE `user_id` IN (?,?)
	mockDb.ExpectExec("DELETE FROM `users_staffs` WHERE `staff_id` IN (?,?)").WithArgs("new1", "new2").
		WillReturnResult(sqlmock.NewResult(1, 2))

	mockDb.ExpectExec("INSERT INTO `users_staffs` (`created_at`,`updated_at`,`user_id`,`staff_id`) VALUES (?,?,?,?)").
		WillReturnResult(sqlmock.NewResult(1, 1))
	mockDb.ExpectCommit()

	req := httptest.NewRequest("POST", "/api/v1/user_access/update_user", strings.NewReader(`{"account":"example", "username":"example", "password":"Flower.123", "roles":[1], "staff_id":["example"]}`))
	req.Header.Set("Content-Type", "application/json")

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Set("user_id", uint64(1))
	s := NewPermissionService()
	_, err := s.UpdateUser(c, requestPermission.UpdateUserRequest{
		Account:  "newAccout5",
		Username: "newAccout3",
		Password: "",
		Roles:    []uint64{1},
		StaffId:  []string{"new1", "new2"},
		Id:       26,
		Status:   1,
	})
	assert.NoError(t, err)
}
func TestService_DeleteUser(t *testing.T) {
	mockDb := testcommon.InitSqlMock()
	defer mockDb.Close()

	mockDb.ExpectBegin()
	// DELETE FROM `users_roles` WHERE `user_id` IN (?,?)
	mockDb.ExpectExec("DELETE FROM `users_roles` WHERE `user_id` IN (?,?)").WithArgs(1, 2).
		WillReturnResult(sqlmock.NewResult(1, 2))
	mockDb.ExpectCommit()
	mockDb.ExpectBegin()
	// DELETE FROM `users_staffs` WHERE `user_id` IN (?,?)
	mockDb.ExpectExec("DELETE FROM `users_staffs` WHERE `user_id` IN (?,?)").WithArgs(1, 2).
		WillReturnResult(sqlmock.NewResult(1, 2))
	mockDb.ExpectCommit()
	mockDb.ExpectBegin()
	// DELETE FROM `users` WHERE `id` IN (?,?)
	mockDb.ExpectExec("DELETE FROM `users` WHERE `id` IN (?,?)").WithArgs(1, 2).
		WillReturnResult(sqlmock.NewResult(1, 2))
	mockDb.ExpectCommit()

	req := httptest.NewRequest("POST", "/api/v1/user_access/update_user", strings.NewReader(`{"account":"example", "username":"example", "password":"Flower.123", "roles":[1], "staff_id":["example"]}`))
	req.Header.Set("Content-Type", "application/json")

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Set("user_id", uint64(1))
	s := NewPermissionService()
	err := s.DeleteUser(c, []uint64{1, 2})
	assert.NoError(t, err)
}
func TestService_AddRole(t *testing.T) {
	//testcommon.SetTestEnv(false)
	mockDb := testcommon.InitSqlMock()
	defer mockDb.Close()
	// 2025-02-25 19:33:38.080 INFO SQL mysql/dsl.go:197 [0.776ms] [rows:1] SELECT count(*) FROM `roles` WHERE `key` = 'autotest' OR `name` = '自动测试1'
	//2025-02-25 19:33:38.087 INFO SQL mysql/dsl.go:270 [6.695ms] [rows:1] INSERT INTO `roles` (`created_at`,`updated_at`,`name`,`key`,`status`,`remark`,`sys`) VALUES ('2025-02-25 19:33:38.081','2025-02-25 19:33:38.081','自动测试1','autotest',1,'单测',0)
	//2025-02-25 19:33:38.095 INFO SQL mysql/dsl.go:285 [7.919ms] [rows:15] INSERT INTO `roles_menus` (`created_at`,`updated_at`,`role_id`,`menu_id`) VALUES ('2025-02-25 19:33:38.088','2025-02-25 19:33:38.088',3,1),('2025-02-25 19:33:38.088','2025-02-25 19:33:38.088',3,2),('2025-02-25 19:33:38.088','2025-02-25 19:33:38.088',3,3),('2025-02-25 19:33:38.088','2025-02-25 19:33:38.088',3,4),('2025-02-25 19:33:38.088','2025-02-25 19:33:38.088',3,5),('2025-02-25 19:33:38.088','2025-02-25 19:33:38.088',3,6),('2025-02-25 19:33:38.088','2025-02-25 19:33:38.088',3,7),('2025-02-25 19:33:38.088','2025-02-25 19:33:38.088',3,8),('2025-02-25 19:33:38.088','2025-02-25 19:33:38.088',3,9),('2025-02-25 19:33:38.088','2025-02-25 19:33:38.088',3,10),('2025-02-25 19:33:38.088','2025-02-25 19:33:38.088',3,11),('2025-02-25 19:33:38.088','2025-02-25 19:33:38.088',3,12),('2025-02-25 19:33:38.088','2025-02-25 19:33:38.088',3,13),('2025-02-25 19:33:38.088','2025-02-25 19:33:38.088',3,14),('2025-02-25 19:33:38.088','2025-02-25 19:33:38.088',3,15)
	mockDb.ExpectQuery("SELECT count(*) FROM `roles` WHERE `name` = ?").
		WithArgs("自动测试1").
		WillReturnRows(mockDb.NewRows([]string{"count"}).
			AddRow(0))
	mockDb.ExpectBegin()
	mockDb.ExpectExec("INSERT INTO `roles` (`created_at`,`updated_at`,`name`,`key`,`status`,`remark`,`sys`) VALUES (?,?,?,?,?,?,?)").
		WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), "自动测试1", "autotest", 1, "单测", 0).
		WillReturnResult(sqlmock.NewResult(3, 1))
	mockDb.ExpectCommit()
	mockDb.ExpectBegin()
	mockDb.ExpectExec("INSERT INTO `roles_menus` (`created_at`,`updated_at`,`role_id`,`menu_id`) VALUES (?,?,?,?)").
		WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), 3, 1).
		WillReturnResult(sqlmock.NewResult(1, 1))
	mockDb.ExpectCommit()
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Set("user_id", uint64(1))

	data := `{
			 "name": "自动测试1",
   			 "key": "autotest",
   			 "remark": "单测",
   			 "menus":[1]
		}`

	req := httptest.NewRequest("POST", "/api/v1/user_access/roles", strings.NewReader(data))
	req.Header.Set("Content-Type", "application/json")
	c.Request = req
	s := NewPermissionService()
	_, err := s.AddRole(c, requestPermission.AddRoleRequest{
		Key:    "autotest",
		Name:   "自动测试1",
		Remark: "单测",
		Menus:  []uint64{1},
	})
	assert.NoError(t, err)
}
func TestService_GetUsers(t *testing.T) {
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)

	t.Run("Success", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()

		mockServer := testcommon.NewMockServer()
		defer mockServer.Close()
		mockServer.Register("staff/_search", []*elastic.SearchHit{
			{
				Id:     "1",
				Source: []byte(`{"name":"example"}`),
			},
		})

		mockDb.ExpectQuery("SELECT count(*) FROM `users`").
			WillReturnRows(mockDb.NewRows([]string{"count"}).AddRow(1))
		mockDb.ExpectQuery("SELECT * FROM `users` WHERE `username` like ? OR `account` like ? LIMIT 10").WithArgs("%haha%", "%haha%").
			WillReturnRows(mockDb.NewRows([]string{"id", "account", "password", "username", "status"}).
				AddRow(1, "test_account", "hashed_password", "haha", 1))

		mockDb.ExpectQuery("SELECT * FROM `users_roles` WHERE `user_id` IN (?)").
			WithArgs(1).
			WillReturnRows(mockDb.NewRows([]string{"id", "user_id", "role_id"}).AddRow(1, 1, 1))
		// SELECT `id`,`name`,`key`,`sys` FROM `roles` WHERE `id` IN (?)
		mockDb.ExpectQuery("SELECT `id`,`name`,`key`,`sys` FROM `roles` WHERE `id` IN (?)").
			WithArgs(1).
			WillReturnRows(mockDb.NewRows([]string{"id", "name", "key", "sys"}).
				AddRow(1, "admin", "admin", 1))
		//
		mockDb.ExpectQuery("SELECT * FROM `users_staffs` WHERE `user_id` IN (?)").
			WithArgs(1).
			WillReturnRows(mockDb.NewRows([]string{"id", "user_id", "staff_id"}))

		req := httptest.NewRequest("GET", "/api/v1/user_access/user_list?page=1&per_page=10&username=haha", nil)
		c.Request = req
		s := NewPermissionService()
		_, _, err := s.GetUsers(c, &requestPermission.GetUsersListRequest{
			Keyword: "haha",
			PageRequest: request.PageRequest{
				Page:    1,
				PerPage: 10,
			},
		})
		assert.NoError(t, err)
	})

	t.Run("Param Err", func(t *testing.T) {
		req := httptest.NewRequest("GET", "/api/v1/user_access/user_list?page=1&per_pageErr=10&username=haha", nil)
		c.Request = req
		s := NewPermissionService()
		_, _, err := s.GetUsers(c, &requestPermission.GetUsersListRequest{
			Keyword: "haha",
			PageRequest: request.PageRequest{
				Page:    -1,
				PerPage: -10,
			},
		})
		assert.Error(t, err)
	})
}
func TestService_GetRoles(t *testing.T) {
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	t.Run("param err ", func(t *testing.T) {
		req := httptest.NewRequest("GET", "/api/v1/user_access/roles", strings.NewReader(`{}`))
		// 创建一个Gin上下文
		c.Request = req
		s := NewPermissionService()
		_, _, err := s.GetRoles(c, &requestPermission.GetRoleListRequest{})
		assert.Error(t, err)
	})
	t.Run("list Err", func(t *testing.T) {
		mockDb := testcommon.GetMysqlMock()
		defer mockDb.Close()
		mockDb.ExpectQuery("SELECT count(*) FROM `roles`").WillReturnError(errors.New("list err"))

		req := httptest.NewRequest("GET", "/api/v1/user_access/roles?page=1&per_page=10", nil)
		// 创建一个Gin上下文
		c.Request = req
		s := NewPermissionService()
		_, _, err := s.GetRoles(c, &requestPermission.GetRoleListRequest{
			PageRequest: request.PageRequest{
				Page:    1,
				PerPage: 10,
			},
		})
		assert.Contains(t, err.Error(), "list err")
	})

	t.Run("Success", func(t *testing.T) {
		mockDb := testcommon.GetMysqlMock()
		defer mockDb.Close()
		mockDb.ExpectQuery("SELECT count(*) FROM `roles`").WillReturnRows(sqlmock.NewRows([]string{"count"}).
			AddRow(1))

		mockDb.ExpectQuery("SELECT * FROM `roles` LIMIT 10").WillReturnRows(sqlmock.NewRows([]string{"id", "name", "key", "status", "remark"}).
			AddRow(1, "1", "1", 1, "1"))
		// SELECT `role_id`,count(role_id) as count FROM `users_roles` WHERE `role_id` IN (1) GROUP BY `role_id`
		mockDb.ExpectQuery("SELECT `role_id`,count(role_id) as count FROM `users_roles` WHERE `role_id` IN (?) GROUP BY `role_id`").
			WithArgs(1).
			WillReturnRows(sqlmock.NewRows([]string{"role_id", "count"}).
				AddRow(1, 1))

		req := httptest.NewRequest("GET", "/api/v1/user_access/roles?page=1&per_page=10", nil)
		c.Request = req
		s := NewPermissionService()
		_, _, err := s.GetRoles(c, &requestPermission.GetRoleListRequest{
			PageRequest: request.PageRequest{
				Page:    1,
				PerPage: 10,
			},
		})
		assert.NoError(t, err)
	})
}
func TestService_BatchSwitchUsersStatus(t *testing.T) {
	mockDb := testcommon.InitSqlMock()
	defer mockDb.Close()

	mockDb.ExpectBegin()
	mockDb.ExpectExec("UPDATE `users` SET `status`=?,`updated_at`=? WHERE `id` IN (?,?,?)").
		WithArgs(1, sqlmock.AnyArg(), 1, 2, 3).
		WillReturnResult(sqlmock.NewResult(0, 3))
	mockDb.ExpectCommit()

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Set("user_id", uint64(1))

	data := `{
			 "ids":[1,2,3],
			 "type":1
		}`

	req := httptest.NewRequest("POST", "/api/v1/user_access/user_switch", strings.NewReader(data))
	req.Header.Set("Content-Type", "application/json")
	c.Request = req
	s := NewPermissionService()
	err := s.BatchSwitchUsersStatus(c, []uint64{1, 2, 3}, 1)
	assert.NoError(t, err)
}
