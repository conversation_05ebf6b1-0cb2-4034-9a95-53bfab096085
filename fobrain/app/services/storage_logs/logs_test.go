package storage_logs

import (
	"fobrain/pkg/cfg"
	"os"
	"path/filepath"
	"testing"
)

func TestListLogFiles(t *testing.T) {
	logsPath := filepath.Join(cfg.LoadCommon().StoragePath, "logs")
	upgradePath := filepath.Join(cfg.LoadCommon().StoragePath, "upgrade")

	os.MkdirAll(logsPath, 0755)
	os.MkdirAll(upgradePath, 0755)

	os.WriteFile(filepath.Join(logsPath, "a.log"), []byte("log1"), 0644)
	os.WriteFile(filepath.Join(upgradePath, "b.log"), []byte("log2"), 0644)

	result, err := ListLogFiles()
	if err != nil {
		t.Fatalf("ListLogFiles error: %v", err)
	}

	if len(result) == 0 {
		t.Fatalf("Unexpected result: %+v", result)
	}
}

func TestDownloadLogFiles(t *testing.T) {
	sample := filepath.Join(cfg.<PERSON><PERSON><PERSON><PERSON>mon().StoragePath, "sample.log")
	os.WriteFile(sample, []byte("log data"), 0644)
	defer os.Remove(sample)
	zipPath, err := DownloadLogFiles([]string{"sample.log"})
	if err != nil {
		t.Fatalf("DownloadLogFiles failed: %v", err)
	}

	if _, err := os.Stat(zipPath); os.IsNotExist(err) {
		t.Fatal("zip file not created")
	}
	os.Remove(zipPath)
}

func TestFormatSize(t *testing.T) {
	tests := []struct {
		input    int64
		expected string
	}{
		{0, "0 B"},
		{500, "500 B"},
		{1024, "1.00 KB"},
		{1536, "1.50 KB"},
		{1048576, "1.00 MB"},
		{1572864, "1.50 MB"},
		{1073741824, "1.00 GB"},
		{1610612736, "1.50 GB"},
	}

	for _, test := range tests {
		result := formatSize(test.input)
		if result != test.expected {
			t.Errorf("formatSize(%d) = %s; want %s", test.input, result, test.expected)
		}
	}
}
