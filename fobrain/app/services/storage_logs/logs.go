package storage_logs

import (
	"fmt"
	"fobrain/pkg/cfg"
	"git.gobies.org/caasm/fobrain-components/utils"
	"os"
	"path/filepath"
	"strings"
)

type LogFileList struct {
	LogType  string `json:"log_type"`
	LogName  string `json:"log_name"`
	FileSize string `json:"file_size"`
}

func formatSize(size int64) string {
	const (
		_          = iota
		KB float64 = 1 << (10 * iota)
		MB
		GB
	)

	sz := float64(size)
	switch {
	case sz >= GB:
		return fmt.Sprintf("%.2f GB", sz/GB)
	case sz >= MB:
		return fmt.Sprintf("%.2f MB", sz/MB)
	case sz >= KB:
		return fmt.Sprintf("%.2f KB", sz/KB)
	default:
		return fmt.Sprintf("%d B", size)
	}
}

func ListLogFiles() ([]*LogFileList, error) {
	logsPath := filepath.Join(cfg.LoadCommon().StoragePath, "logs")
	upgradePath := filepath.Join(cfg.LoadCommon().StoragePath, "upgrade")
	logEntry, err := os.ReadDir(logsPath)
	if err != nil {
		return nil, err
	}
	upgradeEntry, err := os.ReadDir(upgradePath)
	if err != nil {
		return nil, err
	}
	var result = make([]*LogFileList, 0)
	for _, entry := range logEntry {
		name := entry.Name()
		if !entry.IsDir() && (strings.HasSuffix(name, ".log") || strings.HasSuffix(name, ".log.gz")) {
			result = append(result, &LogFileList{
				LogType: "普通日志",
				LogName: name,
			})
		}

	}
	for _, entry := range upgradeEntry {
		if entry.IsDir() {
			continue
		}
		if strings.HasSuffix(entry.Name(), ".log") {
			fileSize := ""
			info, err := os.Stat(entry.Name())
			if err == nil {
				size := info.Size()
				fileSize = formatSize(size)
			}

			result = append(result, &LogFileList{
				LogType:  "升级日志",
				LogName:  entry.Name(),
				FileSize: fileSize,
			})
		}
	}

	return result, nil
}

func DownloadLogFiles(names []string) (string, error) {
	zipName := filepath.Join(cfg.LoadCommon().StoragePath, "download_log_files.zip")
	localFiles := make([]string, 0, len(names))
	for _, name := range names {
		if strings.Contains(name, "..") || strings.ContainsAny(name, `/\`) {
			return "", fmt.Errorf("invalid file name: %s", name)
		}
		localPath := filepath.Join(cfg.LoadCommon().StoragePath, "/logs", filepath.Clean(name))
		upgradePath := filepath.Join(cfg.LoadCommon().StoragePath, "/upgrade", filepath.Clean(name))
		if utils.FileExists(localPath) {
			localFiles = append(localFiles, localPath)
		} else if utils.FileExists(upgradePath) {
			localFiles = append(localFiles, upgradePath)
		}
	}
	_, err := utils.CreateZip(zipName, localFiles)
	return zipName, err
}
