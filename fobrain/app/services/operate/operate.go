package operate

type Operate interface {
	UpdateSyncDataTotal(syncType int, total int) error
	UpdateSyncDataSuccessTotal(syncType int, num int) error
	SetStatus(syncType int, status int, msg string) error
	SetProcess(syncType int, process int) error
	BatchUpdateAsset(sourceData, processData, processDeviceData []map[string]interface{}) error
	BatchUpdateSourceAsset(sourceData []map[string]interface{}) error
	BatchUpdateVul(sourceData, processData []map[string]interface{}) error
	BatchUpdateStaff(sourceData, processData []map[string]interface{}) error
	WriteLog(msg string)
	GetUUID(id string) (string, string)
	GetParamByKey(key string) any
	GetTaskId(int) (uint64, uint64)
}
