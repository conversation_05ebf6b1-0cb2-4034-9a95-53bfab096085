package business_strategy

import (
	"context"
	"encoding/json"
	"errors"
	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/initialize/mysql"
	businessStrategy "fobrain/models/mysql/business_strategies"
	"github.com/DATA-DOG/go-sqlmock"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/alicebob/miniredis/v2"
	"github.com/go-redis/redis/v8"
	"github.com/stretchr/testify/assert"
	"testing"
	"time"
)

func TestGetValid(t *testing.T) {
	// Setup miniredis
	s, err := miniredis.Run()
	if err != nil {
		t.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
	}
	defer s.Close()

	cli := redis.NewClient(&redis.Options{
		Addr: s.Addr(),
	})
	testcommon.SetRedisClient(cli)
	// 模拟 Redis 未命中情况
	mockRedisGet := gomonkey.ApplyMethodFunc(testcommon.GetRedisClient(), "Get", func(ctx context.Context, key string) *redis.StringCmd {
		cmd := redis.NewStringCmd(ctx)
		cmd.SetErr(redis.Nil) // 模拟 key 不存在
		return cmd
	})
	defer mockRedisGet.Reset()

	mockDB := gomonkey.ApplyMethodFunc(businessStrategy.NewStrategiesModel(), "Get", func(handlers ...mysql.HandleFunc) ([]*businessStrategy.Strategies, error) {
		return []*businessStrategy.Strategies{
			{BaseModel: mysql.BaseModel{Id: 1}, Weight: 10},
			{BaseModel: mysql.BaseModel{Id: 2}, Weight: 20},
		}, nil
	})
	defer mockDB.Reset()

	// 模拟 Redis 设置操作
	mockRedisSet := gomonkey.ApplyMethodFunc(testcommon.GetRedisClient(), "Set", func(ctx context.Context, key string, value interface{}, expiration time.Duration) *redis.StatusCmd {
		cmd := redis.NewStatusCmd(ctx)
		return cmd
	})
	defer mockRedisSet.Reset()

	// 创建服务
	service := &StrategyService{}

	// 调用被测试方法
	result, err := service.GetValid()
	assert.NoError(t, err)
	assert.Equal(t, []int{1, 2}, result)
}

func TestGetValid_CacheHit(t *testing.T) {
	// Setup miniredis
	s, err := miniredis.Run()
	if err != nil {
		t.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
	}
	defer s.Close()

	cli := redis.NewClient(&redis.Options{
		Addr: s.Addr(),
	})
	testcommon.SetRedisClient(cli)

	// 创建 mock 数据库
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()

	// 期待的返回数据
	mockRows := sqlmock.NewRows([]string{"id", "data_source_name", "weight"}).
		AddRow(1, "data1", 10).
		AddRow(2, "data2", 20).
		AddRow(3, "data3", 30)

	// 模拟查询操作
	mockDb.ExpectQuery("SELECT * FROM `business_strategies`").
		WillReturnRows(mockRows)

	// 模拟 Redis 命中情况
	cacheData := []int{1, 2, 3}
	cacheDataJSON, _ := json.Marshal(cacheData)

	mockRedisGet := gomonkey.ApplyMethodFunc(testcommon.GetRedisClient(), "Get", func(ctx context.Context, key string) *redis.StringCmd {
		cmd := redis.NewStringCmd(ctx)
		cmd.SetVal(string(cacheDataJSON)) // 模拟缓存数据
		return cmd
	})
	defer mockRedisGet.Reset()

	// 创建服务
	service := &StrategyService{}

	// 调用被测试方法
	result, err := service.GetValid()
	assert.NoError(t, err)
	assert.Equal(t, []int{1, 2, 3}, result)
}

func TestGetValid_DBError(t *testing.T) {
	// Setup miniredis
	s, err := miniredis.Run()
	if err != nil {
		t.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
	}
	defer s.Close()

	cli := redis.NewClient(&redis.Options{
		Addr: s.Addr(),
	})
	testcommon.SetRedisClient(cli)

	// 期待的返回数据
	mockRows := sqlmock.NewRows([]string{"id", "data_source_name", "weight"}).
		AddRow(1, "data1", 10).
		AddRow(2, "data2", 20).
		AddRow(3, "data3", 30)

	// 创建 mock 数据库
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()

	// 模拟查询操作
	mockDb.ExpectQuery("SELECT * FROM `business_strategies`").
		WillReturnRows(mockRows)

	// 模拟 Redis 未命中情况
	mockRedisGet := gomonkey.ApplyMethodFunc(testcommon.GetRedisClient(), "Get", func(ctx context.Context, key string) *redis.StringCmd {
		cmd := redis.NewStringCmd(ctx)
		cmd.SetErr(redis.Nil) // 模拟 key 不存在
		return cmd
	})
	defer mockRedisGet.Reset()

	// 模拟数据库查询出错
	mockDB := gomonkey.ApplyMethodFunc(businessStrategy.NewStrategiesModel(), "Get", func(handlers ...mysql.HandleFunc) ([]*businessStrategy.Strategies, error) {
		return nil, errors.New("database error")
	})
	defer mockDB.Reset()

	// 创建服务
	service := &StrategyService{}

	// 调用被测试方法
	result, err := service.GetValid()
	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "database error")
}

func TestGetValid_RedisUnmarshalError(t *testing.T) {
	s, err := miniredis.Run()
	if err != nil {
		t.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
	}
	defer s.Close()

	cli := redis.NewClient(&redis.Options{
		Addr: s.Addr(),
	})
	testcommon.SetRedisClient(cli)
	// 模拟 Redis 返回无法解析的数据
	mockRedisGet := gomonkey.ApplyMethodFunc(testcommon.GetRedisClient(), "Get", func(ctx context.Context, key string) *redis.StringCmd {
		cmd := redis.NewStringCmd(ctx)
		cmd.SetVal("invalid json") // 模拟无效的缓存数据
		return cmd
	})
	defer mockRedisGet.Reset()

	// 创建服务
	service := &StrategyService{}

	// 调用被测试方法
	result, err := service.GetValid()
	assert.Error(t, err)
	assert.Nil(t, result)
}
