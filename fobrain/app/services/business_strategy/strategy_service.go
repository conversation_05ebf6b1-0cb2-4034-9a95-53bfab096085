package business_strategy

import (
	"context"
	"encoding/json"
	"errors"
	"fobrain/fobrain/logs"
	"fobrain/initialize/mysql"
	"fobrain/initialize/redis"
	businessStrategy "fobrain/models/mysql/business_strategies"
	redis_helper "fobrain/models/redis"
	"time"

	redis2 "github.com/go-redis/redis/v8"
)

type StrategyService struct{}

func NewService() *StrategyService {
	return &StrategyService{}
}

const (
	RedisKeyBusinessRefresh = "cache:business:refresh"
)

// GetValid 获取有效策略，根据权重 由高到低
func (st *StrategyService) GetValid() ([]int, error) {
	logger := logs.GetLogger()
	strategiesCache, err := redis.GetRedisClient().Get(context.Background(), redis_helper.BusinessStrategiesKey()).Result()
	if err != nil {
		//缓存未命中
		if errors.Is(err, redis2.Nil) {
			logger.Debug("【business strategy】缓存业务系统 更新策略")
			handlers := make([]mysql.HandleFunc, 0)
			//根据权重排序
			handlers = append(handlers, mysql.WithOrder("weight ASC"))
			//去除 无效（不可信）源
			handlers = append(handlers, mysql.WithWhere("weight != ?", 99))
			strategies, err := businessStrategy.NewStrategiesModel().Get(handlers...)
			if err != nil {
				logger.Errorf("【business strategy】查询业务系统策略 %s", err.Error())
				return nil, err
			}
			var strategiesData []int
			for _, strategy := range strategies {
				strategiesData = append(strategiesData, int(strategy.Id))
			}
			strategiesDataJson, err := json.Marshal(strategiesData)
			if err != nil {
				logger.Errorf("【business strategy】martshal strategy err:%s", err.Error())
				return nil, err
			}
			err = redis.GetRedisClient().Set(context.Background(), redis_helper.BusinessStrategiesKey(), strategiesDataJson, time.Hour*24).Err()
			if err != nil {
				logger.Errorf("【business strategy】缓存业务系统策略失败 %s", err.Error())
				return nil, err
			}
			return strategiesData, nil
		}
		return nil, err
	}
	logger.Debugf("【business strategy】命中业务系统更新策略缓存")
	// 缓存命中
	var valid []int
	err = json.Unmarshal([]byte(strategiesCache), &valid)
	if err != nil {
		return nil, err
	}
	return valid, nil
}
