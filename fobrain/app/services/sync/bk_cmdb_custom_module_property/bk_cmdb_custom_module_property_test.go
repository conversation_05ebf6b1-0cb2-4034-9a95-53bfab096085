package bk_cmdb_custom_module_property

import (
	"errors"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/olivere/elastic/v7"
	"github.com/stretchr/testify/assert"
	"github.com/tidwall/gjson"

	"fobrain/fobrain/app/services/node/bk_cmdb"
	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/initialize/mysql"
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/data_sync_child_task"
	"fobrain/models/mysql/data_sync_task"
)

func TestSyncCmdbCustomVmMachine(t *testing.T) {
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()
	cli := bk_cmdb.NewBkCmdb()
	taskInfo := `{"Task":{"id":559,"created_at":"2024-11-20 17:45:28","updated_at":"2024-11-20 17:45:28","source_id":3,"node_id":4,"status":0,"source":2,"start_at":"2024-11-20 17:45:28","end_at":"","message":""},"ChildTasks":{"1":{"id":785,"created_at":"2024-11-20 17:45:28","updated_at":"2024-11-20 17:45:28","source_id":3,"node_id":41,"task_id":559,"status":0,"type":1,"sync_data_total":0,"sync_data_success_total":0,"message":"","start_at":"","end_at":""},"2":{"id":786,"created_at":"2024-11-20 17:45:28","updated_at":"2024-11-20 17:45:28","source_id":3,"node_id":41,"task_id":559,"status":0,"type":0,"sync_data_total":0,"sync_data_success_total":0,"message":"","start_at":"","end_at":""},"3":{"id":0,"created_at":"","updated_at":"","source_id":0,"node_id":0,"task_id":0,"status":0,"type":0,"sync_data_total":0,"sync_data_success_total":0,"message":"","start_at":"","end_at":""}}}`
	node := data_source.Node{
		SoftDeleteModel: mysql.SoftDeleteModel{
			Id: 41,
		},
		SourceId:       3,
		AreaId:         1,
		Name:           "qt_cloud",
		Source:         "",
		Types:          "qt_cloud",
		Status:         3,
		Use:            1,
		LastTaskAt:     nil,
		LastTaskStatus: 0,
		LastSyncResult: "",
		Configs:        nil,
	}
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyMethodReturn(cli, "SetNode", nil).Reset()
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyMethodReturn(data_sync_child_task.NewDataSyncChildTaskModel(), "UpdateStatusById", nil).Reset()
	mockDb.ExpectQuery("SELECT * FROM `data_node_configs` WHERE `node_id` = ?").
		WithArgs(41).
		WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))

	err := SyncCmdbCustomVmMachine(&node, taskInfo)
	assert.Nil(t, err)
}

func TestSyncCmdbCustomEcs(t *testing.T) {
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()
	cli := bk_cmdb.NewBkCmdb()
	taskInfo := `{"Task":{"id":559,"created_at":"2024-11-20 17:45:28","updated_at":"2024-11-20 17:45:28","source_id":3,"node_id":4,"status":0,"source":2,"start_at":"2024-11-20 17:45:28","end_at":"","message":""},"ChildTasks":{"1":{"id":785,"created_at":"2024-11-20 17:45:28","updated_at":"2024-11-20 17:45:28","source_id":3,"node_id":41,"task_id":559,"status":0,"type":1,"sync_data_total":0,"sync_data_success_total":0,"message":"","start_at":"","end_at":""},"2":{"id":786,"created_at":"2024-11-20 17:45:28","updated_at":"2024-11-20 17:45:28","source_id":3,"node_id":41,"task_id":559,"status":0,"type":0,"sync_data_total":0,"sync_data_success_total":0,"message":"","start_at":"","end_at":""},"3":{"id":0,"created_at":"","updated_at":"","source_id":0,"node_id":0,"task_id":0,"status":0,"type":0,"sync_data_total":0,"sync_data_success_total":0,"message":"","start_at":"","end_at":""}}}`
	node := data_source.Node{
		SoftDeleteModel: mysql.SoftDeleteModel{
			Id: 41,
		},
		SourceId:       3,
		AreaId:         1,
		Name:           "qt_cloud",
		Source:         "",
		Types:          "qt_cloud",
		Status:         3,
		Use:            1,
		LastTaskAt:     nil,
		LastTaskStatus: 0,
		LastSyncResult: "",
		Configs:        nil,
	}
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyMethodReturn(cli, "SetNode", nil).Reset()
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyMethodReturn(data_sync_child_task.NewDataSyncChildTaskModel(), "UpdateStatusById", nil).Reset()
	mockDb.ExpectQuery("SELECT * FROM `data_node_configs` WHERE `node_id` = ?").
		WithArgs(41).
		WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))

	err := SyncCmdbCustomEcs(&node, taskInfo)
	assert.Nil(t, err)
}

func TestGetDiskNum(t *testing.T) {
	str := "Hard disk 1:1243.5126953125GB"
	num := getDiskNum(str)
	assert.Equal(t, 1243, num)
}

func TestCalculateTotalPages(t *testing.T) {
	t.Run("calculateTotalPages 0", func(t *testing.T) {
		totalPages := calculateTotalPages(0, 10)
		assert.Equal(t, 0, totalPages)
	})
	t.Run("calculateTotalPages 6", func(t *testing.T) {
		totalPages := calculateTotalPages(11, 2)
		assert.Equal(t, 6, totalPages)
	})
}

func TestErrDispose(t *testing.T) {
	err := errors.New("err")
	req := elastic.BulkResponse{
		Took:   1,
		Errors: false,
		Items:  nil,
	}
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyMethodReturn(data_sync_task.NewDataSyncTaskModel(), "UpdateFailById", nil).Reset()
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyMethodReturn(data_sync_child_task.NewDataSyncChildTaskModel(), "UpdateFailById", nil).Reset()
	dispose := errDispose(err, &req, "execVmMachineData", 1, 2)
	assert.Equal(t, false, dispose)
}

func TestGetAssetsModule(t *testing.T) {
	cli := bk_cmdb.NewBkCmdb()
	str := `{"code":0,"permission":null,"result":true,"request_id":"091a38b2d1134627a0c9114477fd11bf","message":"success","data":{"count":1,"info":[{"OrgName":"xxx","ecs_cpu":8,"bk_obj_id":"ecs","InsName":"worker-","create_time":"2024-10-25T10:01:15.026+08:00","biz":"CIT系统","bk_inst_name":"i-ni601hnth0vyyj7ue36u","bk_supplier_account":"0","ecs_memory":32768,"bk_inst_id":59074,"last_time":"2024-11-17T23:02:16.56+08:00","PrivateIpAddress":"127.0.0.1"}]}}`
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyMethodReturn(cli, "GetAssetsModule", str, nil).Reset()
	list, total, err := getAssetsModule(1, 10, cli, "vm_machine")

	assert.Nil(t, err)
	assert.Equal(t, int64(1), total)
	assert.NotNil(t, list)
}

func TestSyncVmMachine(t *testing.T) {
	cli := bk_cmdb.NewBkCmdb()
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()
	mockDb.ExpectQuery("SELECT * FROM `data_node_configs` WHERE node_id = ? AND `key` = ? ORDER BY `data_node_configs`.`id` LIMIT 1").
		WithArgs(int64(41), "network_type").
		WillReturnRows(mockDb.NewRows([]string{"id", "value"}).AddRow(1, 2))

	taskInfo := `{"Task":{"id":559,"created_at":"2024-11-20 17:45:28","updated_at":"2024-11-20 17:45:28","source_id":3,"node_id":4,"status":0,"source":2,"start_at":"2024-11-20 17:45:28","end_at":"","message":""},"ChildTasks":{"1":{"id":785,"created_at":"2024-11-20 17:45:28","updated_at":"2024-11-20 17:45:28","source_id":3,"node_id":41,"task_id":559,"status":0,"type":1,"sync_data_total":0,"sync_data_success_total":0,"message":"","start_at":"","end_at":""},"2":{"id":786,"created_at":"2024-11-20 17:45:28","updated_at":"2024-11-20 17:45:28","source_id":3,"node_id":41,"task_id":559,"status":0,"type":0,"sync_data_total":0,"sync_data_success_total":0,"message":"","start_at":"","end_at":""},"3":{"id":0,"created_at":"","updated_at":"","source_id":0,"node_id":0,"task_id":0,"status":0,"type":0,"sync_data_total":0,"sync_data_success_total":0,"message":"","start_at":"","end_at":""}}}`
	node := data_source.Node{
		SoftDeleteModel: mysql.SoftDeleteModel{
			Id: 41,
		},
		SourceId:       3,
		AreaId:         1,
		Name:           "qt_cloud",
		Source:         "",
		Types:          "qt_cloud",
		Status:         3,
		Use:            1,
		LastTaskAt:     nil,
		LastTaskStatus: 0,
		LastSyncResult: "",
		Configs:        nil,
	}

	str := `{"code":0,"permission":null,"result":true,"request_id":"bba9cc3c3a1e4849937c924f0b6b479a","message":"success","data":{"count":201,"info":[{"is_vm":"1","ip_addr":"127.0.0.1","cpu_num":16,"cluster":"OLD-11","create_time":"2023-09-11T14:23:31.981+08:00","disk":"Hard disk 1:1243.5126953125GB","last_time":"2024-10-18T00:27:17.672+08:00","bk_inst_name":"Tdddb","vmtools":"11","uuid":"422c98dad2ee9ab","hostname":"SERdd.com","power_state":"1","memory":"16.0GB","bk_inst_id":40903,"last_backup_time":"na","vm_name":"TDO","bk_obj_id":"vm_machine","mac":"00:50:56:acdsfsdfsdfa;00:50:56:ac:26:3b","boot_time":"None","vcenter":"xa-vcsa6n.com","instance_uuid":"502cc0de-8497-544a-0acf-811ad9ca1ef9","datacenter":"LongiGroup-DataCenter","sn":"VMware-42dfdfdsfadf 87 ad 2e e9 ab","bk_supplier_account":"0","backup_policy":"na:na"}]}}`
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyMethodReturn(cli, "GetAssetsModule", str, nil).Reset()

	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyMethodReturn(data_sync_child_task.NewDataSyncChildTaskModel(), "UpdateSyncDataTotal", nil).Reset()
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyFuncReturn(execVmMachineData, true).Reset()

	SyncVmMachine(0, taskInfo, cli, &node)
}

func TestSynCloudEcs(t *testing.T) {
	cli := bk_cmdb.NewBkCmdb()
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()
	mockDb.ExpectQuery("SELECT * FROM `data_node_configs` WHERE node_id = ? AND `key` = ? ORDER BY `data_node_configs`.`id` LIMIT 1").
		WithArgs(int64(41), "network_type").
		WillReturnRows(mockDb.NewRows([]string{"id", "value"}).AddRow(1, 2))

	taskInfo := `{"Task":{"id":559,"created_at":"2024-11-20 17:45:28","updated_at":"2024-11-20 17:45:28","source_id":3,"node_id":4,"status":0,"source":2,"start_at":"2024-11-20 17:45:28","end_at":"","message":""},"ChildTasks":{"1":{"id":785,"created_at":"2024-11-20 17:45:28","updated_at":"2024-11-20 17:45:28","source_id":3,"node_id":41,"task_id":559,"status":0,"type":1,"sync_data_total":0,"sync_data_success_total":0,"message":"","start_at":"","end_at":""},"2":{"id":786,"created_at":"2024-11-20 17:45:28","updated_at":"2024-11-20 17:45:28","source_id":3,"node_id":41,"task_id":559,"status":0,"type":0,"sync_data_total":0,"sync_data_success_total":0,"message":"","start_at":"","end_at":""},"3":{"id":0,"created_at":"","updated_at":"","source_id":0,"node_id":0,"task_id":0,"status":0,"type":0,"sync_data_total":0,"sync_data_success_total":0,"message":"","start_at":"","end_at":""}}}`
	node := data_source.Node{
		SoftDeleteModel: mysql.SoftDeleteModel{
			Id: 41,
		},
		SourceId:       3,
		AreaId:         1,
		Name:           "qt_cloud",
		Source:         "",
		Types:          "qt_cloud",
		Status:         3,
		Use:            1,
		LastTaskAt:     nil,
		LastTaskStatus: 0,
		LastSyncResult: "",
		Configs:        nil,
	}

	str := `{"code":0,"permission":null,"result":true,"request_id":"091a38b2d1134627a0c9114477fd11bf","message":"success","data":{"count":201,"info":[{"OrgName":"xxx","ecs_cpu":8,"bk_obj_id":"ecs","InsName":"worker-","create_time":"2024-10-25T10:01:15.026+08:00","biz":"CIT系统","bk_inst_name":"i-ni601hnth0vyyj7ue36u","bk_supplier_account":"0","ecs_memory":32768,"bk_inst_id":59074,"last_time":"2024-11-17T23:02:16.56+08:00","PrivateIpAddress":"127.0.0.1"}]}}`
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyMethodReturn(cli, "GetAssetsModule", str, nil).Reset()

	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyMethodReturn(data_sync_child_task.NewDataSyncChildTaskModel(), "UpdateSyncDataTotal", nil).Reset()
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyFuncReturn(execEcsData, true).Reset()

	SynCloudEcs(0, taskInfo, cli, &node)
}

func TestExecEcsData(t *testing.T) {
	node := data_source.Node{
		SoftDeleteModel: mysql.SoftDeleteModel{
			Id: 41,
		},
		SourceId:       3,
		AreaId:         1,
		Name:           "qt_cloud",
		Source:         "",
		Types:          "qt_cloud",
		Status:         3,
		Use:            1,
		LastTaskAt:     nil,
		LastTaskStatus: 0,
		LastSyncResult: "",
		Configs:        nil,
	}
	result := gjson.Result{
		Type: 5,
		Raw:  `[{"OrgName":"dfafa","ecs_cpu":8,"bk_obj_id":"cloud_ecs","InsName":"woreee","create_time":"2024-10-25T10:01:15.026+08:00","biz":"-所统","bk_inst_name":"i","bk_supplier_account":"0","ecs_memory":32768,"bk_inst_id":59074,"last_time":"2024-11-17T23:02:16.56+08:00","PrivateIpAddress":"127.0.0.1"}]`,
	}
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyFuncReturn(errDispose, true).Reset()
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()
	mockServer.Register("_bulk", &elastic.BulkIndexByScrollResponse{Updated: 1})
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyMethodReturn(data_sync_child_task.NewDataSyncChildTaskModel(), "UpdateSyncDataSuccessTotal", nil).Reset()
	flag := execEcsData(1, 2, 1, 20, "execEcsData", result, &node)
	assert.Equal(t, true, flag)
}

func TestExecVmMachineData(t *testing.T) {
	node := data_source.Node{
		SoftDeleteModel: mysql.SoftDeleteModel{
			Id: 41,
		},
		SourceId:       3,
		AreaId:         1,
		Name:           "qt_cloud",
		Source:         "",
		Types:          "qt_cloud",
		Status:         3,
		Use:            1,
		LastTaskAt:     nil,
		LastTaskStatus: 0,
		LastSyncResult: "",
		Configs:        nil,
	}
	result := gjson.Result{
		Type: 5,
		Raw:  `[{"is_vm":"1","ip_addr":"127.0.0.1","cpu_num":16,"cluster":"xxxx","create_time":"2023-09-11T14:23:31.981+08:00","disk":"Hard disk 1:1243.5126953125GB","last_time":"2024-10-18T00:27:17.672+08:00","bk_inst_name":"TDOA-老OA","vmtools":"toolsOk","uuid":"422c98e0-07edfsdfsdb","hostname":"SER.com","power_state":"poweredOn","memory":"16.0GB","bk_inst_id":40903,"last_backup_time":"na","vm_name":"老OA","bk_obj_id":"vm_machine","mac":"00:50:56:dfdsfds:3b","boot_time":"None","vcenter":"xa.com","instance_uuid":"502cc0de-8497-544a-0acf-811ad9ca1ef9","datacenter":"LongiGsdfasfaCenter","sn":"VMware-42 2c dfadfaf ab","bk_supplier_account":"0","backup_policy":"na:na"}]`,
	}
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyFuncReturn(errDispose, true).Reset()
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()
	mockServer.Register("_bulk", &elastic.BulkIndexByScrollResponse{Updated: 1})
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyMethodReturn(data_sync_child_task.NewDataSyncChildTaskModel(), "UpdateSyncDataSuccessTotal", nil).Reset()
	flag := execVmMachineData(1, 2, 1, "name", 20, "execVmMachineData", result, &node)
	assert.Equal(t, true, flag)
}
