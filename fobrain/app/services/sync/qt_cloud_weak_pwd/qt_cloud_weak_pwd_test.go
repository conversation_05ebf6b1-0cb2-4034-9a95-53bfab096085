package qt_cloud_weak_pwd

import (
	"errors"
	"sync"
	"testing"
	"time"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/olivere/elastic/v7"
	"github.com/stretchr/testify/assert"
	"github.com/tidwall/gjson"

	"fobrain/fobrain/app/services/node/qt_cloud"
	"fobrain/fobrain/common/localtime"
	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/initialize/mysql"
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/data_sync_child_task"
	"fobrain/models/mysql/data_sync_task"
)

func TestSyncWeakPwd(t *testing.T) {
	t.Run("syncWeakPwd Error", func(t *testing.T) {
		cli := qt_cloud.NewQTCloud()
		node := data_source.Node{
			SoftDeleteModel: mysql.SoftDeleteModel{
				Id: 41,
			},
			SourceId:       3,
			AreaId:         1,
			Name:           "qt_cloud",
			Source:         "",
			Types:          "qt_cloud",
			Status:         3,
			Use:            1,
			LastTaskAt:     nil,
			LastTaskStatus: 0,
			LastSyncResult: "",
			Configs:        nil,
		}
		taskInfo := `{"Task":{"id":559,"created_at":"2024-11-20 17:45:28","updated_at":"2024-11-20 17:45:28","source_id":3,"node_id":41,"status":0,"source":2,"start_at":"2024-11-20 17:45:28","end_at":"","message":""},"ChildTasks":{"1":{"id":785,"created_at":"2024-11-20 17:45:28","updated_at":"2024-11-20 17:45:28","source_id":3,"node_id":41,"task_id":559,"status":0,"type":1,"sync_data_total":0,"sync_data_success_total":0,"message":"","start_at":"","end_at":""},"2":{"id":786,"created_at":"2024-11-20 17:45:28","updated_at":"2024-11-20 17:45:28","source_id":3,"node_id":41,"task_id":559,"status":0,"type":2,"sync_data_total":0,"sync_data_success_total":0,"message":"","start_at":"","end_at":""},"3":{"id":0,"created_at":"","updated_at":"","source_id":0,"node_id":0,"task_id":0,"status":0,"type":0,"sync_data_total":0,"sync_data_success_total":0,"message":"","start_at":"","end_at":""}}}`
		time.Sleep(time.Second)
		defer gomonkey.ApplyMethodReturn(cli, "GetWeakPwd", nil, errors.New("errr")).Reset()
		wg := &sync.WaitGroup{}
		wg.Add(1)
		SyncWeakPwd(0, cli, taskInfo, "linux", &node, wg)
	})

	t.Run("syncWeakPwd All", func(t *testing.T) {
		cli := qt_cloud.NewQTCloud()
		node := data_source.Node{
			SoftDeleteModel: mysql.SoftDeleteModel{
				Id: 41,
			},
			SourceId:       3,
			AreaId:         1,
			Name:           "qt_cloud",
			Source:         "",
			Types:          "qt_cloud",
			Status:         3,
			Use:            1,
			LastTaskAt:     nil,
			LastTaskStatus: 0,
			LastSyncResult: "",
			Configs:        nil,
		}
		taskInfo := `{"Task":{"id":559,"created_at":"2024-11-20 17:45:28","updated_at":"2024-11-20 17:45:28","source_id":3,"node_id":41,"status":0,"source":2,"start_at":"2024-11-20 17:45:28","end_at":"","message":""},"ChildTasks":{"1":{"id":785,"created_at":"2024-11-20 17:45:28","updated_at":"2024-11-20 17:45:28","source_id":3,"node_id":41,"task_id":559,"status":0,"type":1,"sync_data_total":0,"sync_data_success_total":0,"message":"","start_at":"","end_at":""},"2":{"id":786,"created_at":"2024-11-20 17:45:28","updated_at":"2024-11-20 17:45:28","source_id":3,"node_id":41,"task_id":559,"status":0,"type":2,"sync_data_total":0,"sync_data_success_total":0,"message":"","start_at":"","end_at":""},"3":{"id":0,"created_at":"","updated_at":"","source_id":0,"node_id":0,"task_id":0,"status":0,"type":0,"sync_data_total":0,"sync_data_success_total":0,"message":"","start_at":"","end_at":""}}}`
		weakInfo := `{"rows":[{"agentId":"dfsfsaf","displayIp":"127.0.0.1","connectionIp":"127.0.0.1","externalIp":null,"internalIp":"127.0.0.1","bizGroupId":33,"bizGroup":"邮箱","remark":null,"hostTagList":null,"hostname":"db-server","id":"3dfafafafa","vulId":"qtcddddafasdfaf","whiteRuleEffect":false,"updatedTime":"2024-11-19 04:32:34"}],"total":403,"charts":{}}`
		time.Sleep(time.Second)
		defer gomonkey.ApplyMethodReturn(cli, "GetWeakPwd", weakInfo, nil).Reset()
		time.Sleep(time.Second)
		defer gomonkey.ApplyMethodReturn(data_sync_task.NewDataSyncTaskModel(), "UpdateFailById", nil).Reset()
		time.Sleep(1 * time.Second)
		time.Sleep(time.Second)
		defer gomonkey.ApplyMethodReturn(data_sync_child_task.NewDataSyncChildTaskModel(), "UpdateFailById", nil).Reset()
		time.Sleep(time.Second)
		defer gomonkey.ApplyMethodReturn(data_sync_child_task.NewDataSyncChildTaskModel(), "UpdateSyncDataTotal", nil).Reset()
		time.Sleep(1 * time.Second)

		time.Sleep(time.Second)
		defer gomonkey.ApplyFuncReturn(execWeakPwd, true).Reset()
		// time.Sleep(time.Second)
		// defer gomonkey.ApplyMethodReturn(data_sync_child_task.NewDataSyncChildTaskModel(), "UpdateStatusById", nil).Reset()
		// client := redis.GetRedisClient()
		// rq := &queue.RedisQueue{Client: client}
		// time.Sleep(time.Second)
		// defer gomonkey.ApplyMethodReturn(rq, "Push", 1).Reset()
		wg := &sync.WaitGroup{}
		wg.Add(1)
		SyncWeakPwd(0, cli, taskInfo, "linux", &node, wg)
		wg.Wait()
	})
}

func TestUpdateChildTask(t *testing.T) {
	err := errors.New("err")
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyMethodReturn(data_sync_task.NewDataSyncTaskModel(), "UpdateFailById", nil).Reset()
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyMethodReturn(data_sync_child_task.NewDataSyncChildTaskModel(), "UpdateFailById", nil).Reset()
	flag := updateChildTask(1, 2, "execWeakPwd", err)
	assert.Equal(t, false, flag)
}

func TestTimestampToDate(t *testing.T) {
	tests := []struct {
		name      string
		timestamp string
		validate  func(t *testing.T, result *localtime.Time)
	}{
		{
			name:      "正常时间戳",
			timestamp: "1700361154000", // 毫秒时间戳
			validate: func(t *testing.T, result *localtime.Time) {
				assert.NotNil(t, result, "结果不应该为nil")
				// 验证时间戳转换是否正确
			},
		},
		{
			name:      "空时间戳",
			timestamp: "",
			validate: func(t *testing.T, result *localtime.Time) {
				assert.NotNil(t, result, "结果不应该为nil")
				// 空时间戳应该返回当前时间
			},
		},
		{
			name:      "无效时间戳",
			timestamp: "invalid-timestamp",
			validate: func(t *testing.T, result *localtime.Time) {
				assert.NotNil(t, result, "结果不应该为nil")
				// 无效时间戳应该返回当前时间
			},
		},
		{
			name:      "零时间戳",
			timestamp: "0",
			validate: func(t *testing.T, result *localtime.Time) {
				assert.NotNil(t, result, "结果不应该为nil")
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := timestampToDate(tt.timestamp)
			tt.validate(t, result)
		})
	}
}

func TestCalculateTotalPages(t *testing.T) {
	tests := []struct {
		name       string
		totalCount int
		pageSize   int
		expected   int
	}{
		{
			name:       "总数为0",
			totalCount: 0,
			pageSize:   10,
			expected:   0,
		},
		{
			name:       "总数小于页大小",
			totalCount: 5,
			pageSize:   10,
			expected:   1,
		},
		{
			name:       "总数等于页大小",
			totalCount: 10,
			pageSize:   10,
			expected:   1,
		},
		{
			name:       "总数大于页大小-整除",
			totalCount: 20,
			pageSize:   10,
			expected:   2,
		},
		{
			name:       "总数大于页大小-不整除",
			totalCount: 11,
			pageSize:   2,
			expected:   6,
		},
		{
			name:       "大数据量测试",
			totalCount: 1000,
			pageSize:   50,
			expected:   20,
		},
		{
			name:       "大数据量测试-不整除",
			totalCount: 1001,
			pageSize:   50,
			expected:   21,
		},
		{
			name:       "页大小为1",
			totalCount: 5,
			pageSize:   1,
			expected:   5,
		},
		{
			name:       "单页大数据",
			totalCount: 100,
			pageSize:   200,
			expected:   1,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := calculateTotalPages(tt.totalCount, tt.pageSize)
			assert.Equal(t, tt.expected, result, "calculateTotalPages函数返回值不符合预期")
		})
	}
}

func TestErrDispose(t *testing.T) {
	err := errors.New("err")
	req := elastic.BulkResponse{
		Took:   1,
		Errors: false,
		Items:  nil,
	}
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyMethodReturn(data_sync_task.NewDataSyncTaskModel(), "UpdateFailById", nil).Reset()
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyMethodReturn(data_sync_child_task.NewDataSyncChildTaskModel(), "UpdateFailById", nil).Reset()
	dispose := errDispose(err, &req, "weakPwd", "err", 1, 2)
	assert.Equal(t, false, dispose)
}

func TestExecWeakPwd(t *testing.T) {
	cli := qt_cloud.NewQTCloud()
	node := data_source.Node{
		SoftDeleteModel: mysql.SoftDeleteModel{
			Id: 41,
		},
		SourceId:       3,
		AreaId:         1,
		Name:           "qt_cloud",
		Source:         "",
		Types:          "qt_cloud",
		Status:         3,
		Use:            1,
		LastTaskAt:     nil,
		LastTaskStatus: 0,
		LastSyncResult: "",
		Configs:        nil,
	}
	result := gjson.Result{
		Type: 5,
		Raw:  "[{\"agentId\":\"dddd\",\"displayIp\":\"127.0.0.1\",\"connectionIp\":\"127.0.0.1\",\"externalIp\":null,\"internalIp\":\"127.0.0.1\",\"bizGroupId\":1,\"bizGroup\":\"xxxxx\",\"remark\":null,\"hostTagList\":null,\"hostname\":\"db-server\",\"id\":\"1111\",\"vulId\":\"111\",\"whiteRuleEffect\":false,\"updatedTime\":\"2024-11-19 04:32:34\"}]",
	}

	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()
	mockServer.Register("_bulk", &elastic.BulkIndexByScrollResponse{
		Updated: 1,
	})
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyMethodReturn(data_sync_child_task.NewDataSyncChildTaskModel(), "UpdateSyncDataSuccessTotal", nil).Reset()
	getWeakPwdDetails := `{"vulId":"11","vulName":"11","apps":["11"],"desc":"11/删除重要数据并向企业寻求勒索等。","restartOpts":1,"publicDate":"2017-01-12 00:00:00","firstCheckTime":"2024-09-13 04:33:31","app":"1","uname":"1","accountStatus":2,"weakType":4,"version":"5.5.68-1.el7","binPath":"/usr/libexec/mysqld","password":"1@123","unChangePwdDays":null,"accountLoginType":null,"interactiveLoginType":null,"pwdStatus":null,"bindIp":"0.0.0.0","pid":"14955","port":"3308","root":null,"remote":null,"loginShell":null,"uhost":"%"}`
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyMethodReturn(cli, "GetWeakPwdDetails", getWeakPwdDetails, nil).Reset()

	flag := execWeakPwd(1, 2, "linux", result, cli, &node)
	assert.Equal(t, true, flag)
}

func TestIsHasPocOrExp(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected int
	}{
		{
			name:     "字符串true",
			input:    "true",
			expected: 1,
		},
		{
			name:     "字符串false",
			input:    "false",
			expected: 0,
		},
		{
			name:     "大写TRUE",
			input:    "TRUE",
			expected: 1, // 只有"false"返回0，其他都返回1
		},
		{
			name:     "大写FALSE",
			input:    "FALSE",
			expected: 1, // 区分大小写，只有小写"false"返回0
		},
		{
			name:     "空字符串",
			input:    "",
			expected: 1, // 默认返回1
		},
		{
			name:     "其他字符串",
			input:    "yes",
			expected: 1, // 默认返回1
		},
		{
			name:     "数字字符串",
			input:    "1",
			expected: 1, // 默认返回1
		},
		{
			name:     "包含空格的true",
			input:    " true ",
			expected: 1, // 默认返回1
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := isHasPocOrExp(tt.input)
			assert.Equal(t, tt.expected, result, "isHasPocOrExp函数返回值不符合预期")
		})
	}
}


