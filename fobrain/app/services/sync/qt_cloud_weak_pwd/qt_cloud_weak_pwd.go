package qt_cloud_weak_pwd

import (
	"context"
	"fmt"
	"fobrain/models/elastic/poc"
	qtcloudes "fobrain/models/elastic/source/qt_cloud"
	"fobrain/pkg/utils"
	"fobrain/pkg/utils/handle_es_bulk_error"
	"strconv"
	"sync"
	"time"

	"github.com/tidwall/sjson"

	"github.com/olivere/elastic/v7"
	"github.com/tidwall/gjson"
	"go-micro.dev/v4/logger"

	"fobrain/fobrain/app/services/node/qt_cloud"
	"fobrain/fobrain/common/constant"
	"fobrain/fobrain/common/localtime"
	"fobrain/fobrain/logs"
	"fobrain/initialize/es"
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/data_sync_child_task"
	"fobrain/models/mysql/data_sync_task"
)

// syncWeakPwd 函数用于同步l漏洞弱密码数据
//
// 参数：
// page: int类型，表示分页的页码
// size: int类型，表示每页显示的记录数
// cli: *qt_cloud.QTCloud类型，表示QTCloud客户端
// taskInfo: data_sync_task.DataSyncTask类型，表示数据同步任务信息
// node: node.Node类型，表示节点信息
//
// 返回值：
// 无返回值
func SyncWeakPwd(page int, cli *qt_cloud.QTCloud, taskInfo, syncAssetType string, node *data_source.Node, gwg *sync.WaitGroup) {
	taskId := uint64(gjson.Get(taskInfo, "Task.id").Int())
	taskChildId := uint64(gjson.Get(taskInfo, fmt.Sprintf("ChildTasks.%d.id", data_sync_task.SyncThreat)).Int())
	logs.GetSyncLogger().Infof("SyncQTCloud syncThreats start taskId:%d,taskChildId:%d,", taskId, taskChildId)
	//获取资产
	weakPwd, err := cli.GetWeakPwd(page, constant.DefaultSize*4, syncAssetType)
	if err != nil {
		logs.GetSyncLogger().Errorf("QTCloudWakPwd syncWeakPwd GetWeakPwd err:%s", err)

		//更新主任务状态为失败
		taskErr := data_sync_task.NewDataSyncTaskModel().UpdateFailById(taskId, err.Error())
		if taskErr != nil {
			logs.GetSyncLogger().Errorf("QTCloudWakPwd syncWeakPwd GetWeakPwd UpdateFailById Task err:%s", taskErr.Error())
		}

		//更新子任务状态为失败
		childTaskErr := data_sync_child_task.NewDataSyncChildTaskModel().UpdateFailById(taskChildId, err.Error())
		if childTaskErr != nil {
			logs.GetSyncLogger().Errorf("QTCloudWakPwd syncWeakPwd GetWeakPwd  UpdateFailById childTask err:%s", childTaskErr.Error())
		}
		gwg.Done()
		return
	}
	total := gjson.Get(weakPwd, "total").Int() //数据总量
	result := gjson.Get(weakPwd, "rows")
	logger.Infof("QTCloudWakPwd syncWeakPwd GetWeakPwd total:%d", total)
	if total < 1 {
		gwg.Done()
		return
	}

	//子任务更新需要同步的数据总量
	if page == constant.DefaultPageZero { //仅第一次同步
		err = data_sync_child_task.NewDataSyncChildTaskModel().UpdateSyncDataTotal(taskChildId, int(total))
		if err != nil {
			logs.GetSyncLogger().Errorf("QTCloudWakPwd syncWeakPwd UpdateSyncDataTotal err:%s", err.Error())
		}
		flag := execWeakPwd(taskId, taskChildId, syncAssetType, result, cli, node)
		if !flag {
			gwg.Done()
			return
		}
	}
	pages := calculateTotalPages(int(total), constant.DefaultSize*4)
	for i := 2; i <= pages; i++ {
		logger.Infof("QTCloudWakPwd syncWeakPwd GetWeakPwd total:%d,Page:%d 页", total, i-1)
		//获取资产
		weakPwd, err = cli.GetWeakPwd(i-1, constant.DefaultSize*4, syncAssetType)
		if err != nil {
			gwg.Done()
			return
		}
		result = gjson.Get(weakPwd, "rows")
		flag := execWeakPwd(taskId, taskChildId, syncAssetType, result, cli, node)
		if !flag {
			gwg.Done()
			return
		}
		time.Sleep(1 * time.Second)
	}
	logs.GetSyncLogger().Infof("QTCloudWakPwd syncWeakPwd end taskInfo:%d,taskChildId:%d", taskId, taskChildId)

	gwg.Done()
}

// execWeakPwd 函数用于执行弱密码相关的任务
//
// 参数：
// taskId: uint64类型，表示任务的唯一标识符
// taskChildId: uint64类型，表示子任务的唯一标识符
// result: gjson.Result类型，表示任务执行结果
// rq: *queue.RedisQueue类型，表示Redis队列对象
//
// 返回值：
// 无返回值
func execWeakPwd(taskId, taskChildId uint64, syncAssetType string, result gjson.Result, cli *qt_cloud.QTCloud, node *data_source.Node) bool {
	defer func() {
		if reErr := recover(); reErr != nil {
			logs.GetCrontabLogger().Infof("QTCloudWakPwd syncWeakPwd execWeakPwd taskId:%v,taskChildId:%v,weakPwdData:%s > FAIL, 返回结果: %v", taskId, taskChildId, result, reErr)
		}
	}()

	if result.Exists() && len(result.Array()) > 0 {
		// weakBulkRequest := es.GetEsClient().Bulk()
		weakTaskBulkRequest := es.GetEsClient().Bulk()
		weakProcessBulkRequest := es.GetEsClient().Bulk()
		var num = 0
		var ch = make(chan struct{}, 5)
		var wg sync.WaitGroup
		result.ForEach(func(key, value gjson.Result) bool {
			ch <- struct{}{}
			wg.Add(1)
			logs.GetSyncLogger().Debugf("QTCloudWakPwd syncWeakPwd execWeakPwd value info:%s", value)
			go func(key, value gjson.Result) {
				defer func() {
					<-ch
					wg.Done()
					if reErr := recover(); reErr != nil {
						logs.GetSyncLogger().Errorf("QTCloudWakPwd syncWeakPwd execWeakPwd taskId:%v,taskChildId:%v,weakPwdData:%s > FAIL, 返回结果: %v", taskId, taskChildId, value, reErr)
					}
				}()
				//根据id弱口令详情
				id := gjson.Get(value.String(), "id").String()
				details, err := cli.GetWeakPwdDetails(syncAssetType, id)
				if err != nil {
					updateChildTask(taskId, taskChildId, "execWeakPwd", err)
					return
				}
				details, _ = sjson.Set(details, "category", []int{100})

				ip := gjson.Get(value.String(), "connectionIp").String() // 主机ip
				vulId := gjson.Get(value.String(), "vulId").String()
				agentId := gjson.Get(value.String(), "agentId").String()
				firstCheckTime := gjson.Get(details, "firstCheckTime").String()
				// 青藤那个确认好了，connectionIp + vulId + agentId+ firstCheckTime
				uniquekey := utils.Md5Hash(fmt.Sprintf("%s_%s_%s_%s", ip, vulId, agentId, firstCheckTime))

				areaId := node.AreaId
				if ip != "" {
					// 获取区域
					areaId, err = node.GetAreaByIp(ip)
					if err != nil {
						logs.GetSyncLogger().Errorf("QTCloud syncThreats GetAreaByIp failed,ip:%s,nodeId:%d,err:%s", ip, node.Id, err)
						r := data_sync_child_task.NewDataSyncChildTaskFailRecord()
						r.Create(&data_sync_child_task.DataSyncChildTaskFailRecord{
							ChildTaskId:  taskChildId,
							DataType:     "ip",
							DataContent:  ip,
							FailedReason: err.Error(),
						})
					}
				}

				data, _ := sjson.Set(value.String(), "node_id", node.Id)
				data, _ = sjson.Set(data, "area_id", areaId)
				data, _ = sjson.Set(data, "details", details)
				data, _ = sjson.Set(data, "original_id", uniquekey)

				//写入漏洞库
				// threat := qtcloudes.NewQTCloudTaskThreatsModel()
				threatId := fmt.Sprintf("%d_%d_%s", node.Id, areaId, id)
				// threatReq := elastic.NewBulkIndexRequest().Index(threat.IndexName()).Id(threatId).Doc(data)
				// weakBulkRequest = weakBulkRequest.Add(threatReq)

				//写入任务库
				threatTask := qtcloudes.NewQTCloudTaskThreatsModel()
				threatTaskId := fmt.Sprintf("%d_%s", taskId, threatId)
				data, _ = sjson.Set(data, "task_id", taskId)
				data, _ = sjson.Set(data, "child_task_id", taskChildId)
				assetTaskReq := elastic.NewBulkIndexRequest().Index(threatTask.IndexName()).Id(threatTaskId).Doc(data)
				weakTaskBulkRequest = weakTaskBulkRequest.Add(assetTaskReq)

				//写入过程库
				threatProcess := poc.NewProcessPocModel()
				threatProcess.Id = threatId
				threatProcess.Url = uniquekey
				threatProcess.TaskId = taskId
				threatProcess.ChildTaskId = taskChildId
				threatProcess.OriginalId = uniquekey
				threatProcess.TaskDataId = threatTaskId
				threatProcess.Area = areaId
				threatProcess.Node = node.Id
				threatProcess.Source = node.SourceId
				threatProcess.IsPoc = poc.NoPoc
				//execute_type为1返回否，如果execute_type为2返回是，execute_type不为1或2，返回未知
				dataValue := gjson.Get(details, "data").String()
				executeType := gjson.Get(dataValue, "execute_type").Int()
				if executeType == 2 {
					threatProcess.IsPoc = poc.YesPoc
				} else if executeType != 1 {
					threatProcess.IsPoc = poc.UnknownPoc
				}
				threatProcess.Ip = ip
				threatProcess.VulType = "弱口令"
				threatProcess.Name = gjson.Get(details, "vulName").String() // 风险名（仅linux）
				threatProcess.Describe = gjson.Get(details, "desc").String()
				pidResult := gjson.Get(details, "pid")
				if !pidResult.Exists() {
					threatProcess.Details = ""
				} else {

					binPath := gjson.Get(details, "binPath").String()
					// 弱口令取pid以及和path相关的所有字段，通过换行分割
					threatProcess.Details = fmt.Sprintf("%s %d\n%s %s", "PID:", pidResult.Int(), "binPath:", binPath)

				}
				threatProcess.CreatedAt = timestampToDate(firstCheckTime)
				threatProcess.UpdatedAt = timestampToDate(gjson.Get(value.String(), "updatedTime").String())
				//漏洞等级（低危 0 中危 1 高危 2 严重 3 未知 4严重）
				threatProcess.Level = 4 // TODO: 弱口令中无风险等级，默认为严重

				hasPoc := 0
				if gjson.Get(details, "hasPoc").Exists() {
					if gjson.Get(details, "hasPoc").Bool() {
						hasPoc = 1
					} else {
						hasPoc = 2
					}
				}
				threatProcess.HasPoc = hasPoc
				hasExp := 0
				if gjson.Get(details, "hasExp").Exists() {
					if gjson.Get(details, "hasExp").Bool() {
						hasExp = 1
					} else {
						hasExp = 2
					}
				}
				threatProcess.HasExp = hasExp

				threatProcessReq := elastic.NewBulkIndexRequest().Index(threatProcess.IndexName()).Id(threatId).Doc(threatProcess)
				weakProcessBulkRequest = weakProcessBulkRequest.Add(threatProcessReq)
				num = num + 1
			}(key, value)

			return true
		})
		wg.Wait()
		if num > 0 {
			// 过程数据执行
			weakProcessBulkReq, err := weakProcessBulkRequest.Refresh("true").Do(context.Background())
			dispose := errDispose(err, weakProcessBulkReq, "execWeakPwd", "weakProcessBulkRequest", taskId, taskChildId)
			if !dispose {
				return false
			}

			// // 漏洞库执行
			// weakBulkReq, err := weakBulkRequest.Refresh("true").Do(context.Background())
			// dispose = errDispose(err, weakBulkReq, "execWeakPwd", "weakBulkRequest", taskId, taskChildId)
			// if !dispose {
			// 	return false
			// }

			// 任务库执行
			weakTaskBulkReq, err := weakTaskBulkRequest.Refresh("true").Do(context.Background())
			dispose = errDispose(err, weakTaskBulkReq, "execWeakPwd", "weakTaskBulkRequest", taskId, taskChildId)
			if !dispose {
				return false
			}

			if err == nil {
				weakTaskBulkReqNum := len(weakTaskBulkReq.Items)
				updateSyncDataSuccess(taskChildId, weakTaskBulkReqNum)
			}
		}
	}
	return true
}

// updateSyncDataSuccess 函数用于更新同步数据成功的任务信息，并将任务标识和弱密码ID列表发送到队列中。
//
// 参数：
//
//	taskChildId uint64：子任务ID
//	weakTaskBulkReqNum int：弱密码批量请求数
//
// 功能：
//  1. 调用data_sync_child_task.NewDataSyncChildTaskModel().UpdateSyncDataSuccessTotal方法，更新子任务同步数据成功的总数。
//  2. 如果更新失败，则记录错误日志。
func updateSyncDataSuccess(taskChildId uint64, weakTaskBulkReqNum int) {
	err := data_sync_child_task.NewDataSyncChildTaskModel().UpdateSyncDataSuccessTotal(taskChildId, weakTaskBulkReqNum)
	if err != nil {
		logs.GetSyncLogger().Errorf("QTCloudWakPwd syncThreats UpdateSyncDataSuccessTotal err:%s", err.Error())
	}
}

// errDispose 错误处理
func errDispose(err error, req *elastic.BulkResponse, methodName, errStr string, taskId, taskChildId uint64) bool {
	if err != nil || req.Errors {
		errString := handle_es_bulk_error.HandleBulkResp("qt_cloud_weak_pwd", err, req)
		logs.GetSyncLogger().Errorf("QTCloudWakPwd %s errDispose %s.Do err:%s", methodName, errStr, errString)
		//更新主任务状态为失败
		taskErr := data_sync_task.NewDataSyncTaskModel().UpdateFailById(taskId, errString)
		if taskErr != nil {
			logs.GetSyncLogger().Errorf("QTCloudWakPwd %s errDispose %s.Do task UpdateFailById err:%s", methodName, errStr, taskErr)
		}
		//更新子任务状态为失败
		childTaskErr := data_sync_child_task.NewDataSyncChildTaskModel().UpdateFailById(taskChildId, errString)
		if childTaskErr != nil {
			logs.GetSyncLogger().Errorf("QTCloudWakPwd %s errDispose %s.Do childTask UpdateFailById err:%s", methodName, errStr, childTaskErr.Error())
		}
		return false
	}
	return true
}

func isHasPocOrExp(hasPoc string) int {
	hasPocNum := 1
	if hasPoc == "false" {
		hasPocNum = 0
	}
	return hasPocNum
}

// updateChildTask 函数用于更新子任务的状态，并在子任务失败时更新主任务的状态。
//
// 参数：
//
//	taskId: uint64类型，表示主任务的唯一标识符。
//	taskChildId: uint64类型，表示子任务的唯一标识符。
//	methodName: string类型，表示触发更新操作的方法名称。
//	err: error类型，表示子任务执行过程中发生的错误。
//
// 返回值：
//
//	bool类型，总是返回false，表示更新操作的结果（在当前实现中，更新操作总是执行，无返回值实际意义）。
func updateChildTask(taskId, taskChildId uint64, methodName string, err error) bool {
	logs.GetSyncLogger().Errorf("QTCloudWakPwd %s updateChildTask err:%s", methodName, err)

	//更新主任务状态为失败
	taskErr := data_sync_task.NewDataSyncTaskModel().UpdateFailById(taskId, err.Error())
	if taskErr != nil {
		logs.GetSyncLogger().Errorf("QTCloudWakPwd updateChildTask %s UpdateFailById Task err:%s", methodName, taskErr.Error())
	}

	//更新子任务状态为失败
	childTaskErr := data_sync_child_task.NewDataSyncChildTaskModel().UpdateFailById(taskChildId, err.Error())
	if childTaskErr != nil {
		logs.GetSyncLogger().Errorf("QTCloudWakPwd updateChildTask %s UpdateFailById childTask err:%s", methodName, childTaskErr.Error())
	}
	return false
}

func timestampToDate(timestamp string) *localtime.Time {
	if timestamp != "" {
		i64, _ := strconv.ParseInt(timestamp, 10, 64)
		t := time.UnixMilli(i64)
		tm, _ := time.Parse(localtime.TimeFormat, t.Format("2006-01-02 15:04:05"))
		return localtime.NewLocalTime(tm)
	}
	return localtime.NewLocalTime(time.Now())
}

func calculateTotalPages(totalCount int, pageSize int) int {
	if totalCount == 0 {
		return 0
	}
	pages := totalCount / pageSize
	if totalCount%pageSize != 0 {
		pages++
	}
	return pages
}
