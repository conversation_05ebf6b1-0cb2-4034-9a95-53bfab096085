package ip_mapping

import (
	testcommon "fobrain/fobrain/tests/common_test"
	logs "fobrain/mergeService/utils/log"
	esmodel "fobrain/models/elastic/assets"
	"fobrain/models/mysql/ip_mapping"
	"fobrain/pkg/utils"
	"fobrain/pkg/utils/common_logs"
	"github.com/DATA-DOG/go-sqlmock"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/olivere/elastic/v7"
	"github.com/stretchr/testify/assert"
	"go.uber.org/zap"
	"net/http/httptest"
	"sync"
	"testing"
)

func TestSaveData(t *testing.T) {
	patches := []*gomonkey.Patches{
		gomonkey.ApplyFunc(logs.GetLogger, func() *common_logs.Logger {
			return &common_logs.Logger{
				Logger: &zap.Logger{},
				Err:    nil,
			}
		}),
		gomonkey.ApplyMethodReturn(&ip_mapping.IpMapping{}, "CreateBatch", int64(1), nil),
		gomonkey.ApplyMethodReturn(&ip_mapping.IpMappingAuditData{}, "CreateBatch", nil),
	}
	successCount := int64(0)
	wg := &sync.WaitGroup{}
	wg.Add(1)
	ipMappingChan := make(chan map[string]interface{}, 1100)
	for i := 0; i < 1100; i++ {
		ipMappingChan <- map[string]interface{}{}
	}
	close(ipMappingChan)
	saveData(ipMappingChan, wg, &successCount)
	for _, patch := range patches {
		patch.Reset()
	}
}

func TestGetIpMappingForAlarm(t *testing.T) {
	patches := []*gomonkey.Patches{
		gomonkey.ApplyFunc(logs.GetLogger, func() *common_logs.Logger {
			return &common_logs.Logger{
				Logger: &zap.Logger{},
				Err:    nil,
			}
		}),
		gomonkey.ApplyMethodReturn(&ip_mapping.IpMapping{}, "List", []map[string]interface{}{}, int64(1), nil),
		gomonkey.ApplyFunc(checkIpMappingAsset, func(batchNo string, ipMappingList []map[string]interface{}, wg *sync.WaitGroup) {
			defer wg.Done()
		}),
		gomonkey.ApplyMethodReturn(&ip_mapping.IpMappingStatistics{}, "DeleteByBatchNo", nil),
	}

	err := GetIpMappingForAlarm(uuid.New().String())
	for _, patch := range patches {
		patch.Reset()
	}
	assert.Nil(t, err)
}

func TestCheckIpMappingAsset(t *testing.T) {
	patches := []*gomonkey.Patches{
		gomonkey.ApplyFunc(logs.GetLogger, func() *common_logs.Logger {
			return &common_logs.Logger{
				Logger: &zap.Logger{},
				Err:    nil,
			}
		}),
		gomonkey.ApplyFunc(markAlarmData, func(ipList []string, ipMappingMap map[string][]uint64) (map[uint64]struct{}, map[string]string, error) {
			alarmData := make(map[uint64]struct{})
			alarmData[uint64(1)] = struct{}{}
			domainMap := make(map[string]string)
			return alarmData, domainMap, nil
		}),
		gomonkey.ApplyMethodReturn(&ip_mapping.IpMappingStatistics{}, "CreateBatch", nil),
	}
	wg := &sync.WaitGroup{}
	wg.Add(1)
	checkIpMappingAsset(uuid.New().String(), []map[string]interface{}{}, wg)
	for _, patch := range patches {
		patch.Reset()
	}
}

func TestMarkAlarmData(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()
	mockServer.Register("/asset/_search", []*elastic.SearchHit{
		{
			Id:     "1",
			Source: []byte(`{"id":"1","Ip":"127.0.0.1","name":"example","Ports":"[80,]"}`),
		},
	})
	mockServer.RegisterEmptyScrollHandler()
	ipList := []string{"127.0.0.1", "*********", "*********"}
	ipMappingMap := make(map[string][]uint64)
	ipMappingMap["127.0.0.1:80"] = []uint64{1, 2, 4}
	_, _, err := markAlarmData(ipList, ipMappingMap)
	assert.Nil(t, err)
}

func TestExportAlarmList1(t *testing.T) {
	patches := []*gomonkey.Patches{
		gomonkey.ApplyMethodReturn(&ip_mapping.IpMappingStatisticsAudit{}, "Last", nil, nil),
		gomonkey.ApplyMethodReturn(&ip_mapping.IpMappingStatistics{}, "List", int64(0), nil, nil),
		gomonkey.ApplyMethodReturn(&ip_mapping.IpMappingStatistics{}, "Query", nil, nil),
		gomonkey.ApplyFunc(createExcel, func(datum [][]interface{}) string {
			return "path"
		}),
	}
	c, _ := gin.CreateTestContext(httptest.NewRecorder())
	path, err := ExportAlarmList(c, []uint64{1, 2, 3}, "", make(map[string]any))
	for _, patch := range patches {
		patch.Reset()
	}
	assert.Nil(t, err)
	assert.NotNil(t, path)
}

func TestCreateTemplateFile(t *testing.T) {
	patch := gomonkey.ApplyFuncReturn(utils.WriterExcel, nil, nil)
	path := CreateTemplateFile("from", "to")
	assert.Contains(t, path, "内外网映射关系模板.xlsx")
	patch.Reset()
}

func TestExportAlarmList2(t *testing.T) {
	patches := []*gomonkey.Patches{
		gomonkey.ApplyMethodReturn(&ip_mapping.IpMappingStatisticsAudit{}, "Last", nil, nil),
		gomonkey.ApplyMethodReturn(&ip_mapping.IpMappingStatistics{}, "List", int64(0), nil, nil),
		gomonkey.ApplyMethodReturn(&ip_mapping.IpMappingStatistics{}, "Query", []map[string]interface{}{
			{
				"public_ip":           "127.0.0.1",
				"public_port":         "80",
				"private_ip":          "*********",
				"private_port":        "81",
				"public_exist_alarm":  0,
				"private_exist_alarm": 0,
			},
			{
				"public_ip":           "127.0.0.1",
				"public_port":         "80",
				"private_ip":          "*********",
				"private_port":        "81",
				"public_exist_alarm":  1,
				"private_exist_alarm": 1,
			},
		}, nil),
		gomonkey.ApplyFunc(createExcel, func(datum [][]interface{}) string {
			return "path"
		}),
	}
	c, _ := gin.CreateTestContext(httptest.NewRecorder())
	path, err := ExportAlarmList(c, []uint64{}, "a", make(map[string]any))
	for _, patch := range patches {
		patch.Reset()
	}
	assert.Nil(t, err)
	assert.NotNil(t, path)
}

func TestExportAlarmList3(t *testing.T) {
	patches := []*gomonkey.Patches{
		gomonkey.ApplyMethodReturn(&ip_mapping.IpMappingStatisticsAudit{}, "Last", nil, nil),
		gomonkey.ApplyMethodReturn(&ip_mapping.IpMappingStatistics{}, "List", int64(1), nil, nil),
		gomonkey.ApplyMethodReturn(&ip_mapping.IpMappingStatistics{}, "Query", nil, nil),
		gomonkey.ApplyFunc(createExcel, func(datum [][]interface{}) string {
			return "path"
		}),
	}
	c, _ := gin.CreateTestContext(httptest.NewRecorder())
	path, err := ExportAlarmList(c, []uint64{}, "", make(map[string]any))
	for _, patch := range patches {
		patch.Reset()
	}
	assert.Nil(t, err)
	assert.NotNil(t, path)
}

func TestCreateExcel(t *testing.T) {
	patch := gomonkey.ApplyFuncReturn(utils.WriterExcel, "path", nil)
	path := createExcel([][]interface{}{})
	patch.Reset()
	assert.NotNil(t, path)
}

func TestIsExistAlarmData(t *testing.T) {
	res, _ := isExistAlarmData("127.0.0.1", 80, []*esmodel.Assets{&esmodel.Assets{
		Ip:    "127.0.0.1",
		Ports: []*esmodel.PortInfo{&esmodel.PortInfo{Port: 80, Protocol: "tcp"}}}})
	assert.True(t, res)
}

func TestGetFieldName(t *testing.T) {
	mockDb := testcommon.InitSqlMock()
	mockDb.ExpectQuery("SELECT * FROM `custom_fields` WHERE `display_name` = ? AND `table` = ? ORDER BY `custom_fields`.`id` LIMIT 1").
		WithArgs("testIP", "ip_mapping").
		WillReturnRows(mockDb.NewRows([]string{"field_name"}).AddRow("test"))
	mockDb.ExpectQuery("SELECT * FROM `custom_fields` WHERE `display_name` = ? AND `table` = ? ORDER BY `custom_fields`.`id` LIMIT 1").
		WithArgs("test端口", "ip_mapping").
		WillReturnRows(mockDb.NewRows([]string{"field_name"}).AddRow("test"))
	mockDb.ExpectQuery("SELECT * FROM `custom_fields` WHERE `display_name` = ? AND `table` = ? ORDER BY `custom_fields`.`id` LIMIT 1").
		WithArgs("test域名", "ip_mapping").
		WillReturnRows(mockDb.NewRows([]string{"field_name"}).AddRow("test"))
	mockDb.ExpectQuery("SELECT * FROM `custom_fields` WHERE `display_name` = ? AND `table` = ? ORDER BY `custom_fields`.`id` LIMIT 1").
		WithArgs("test协议", "ip_mapping").
		WillReturnRows(mockDb.NewRows([]string{"field_name"}).AddRow("test"))
	fieldMap, ok := GetFieldName("test")
	assert.NotNil(t, fieldMap)
	assert.True(t, ok)
	mockDb.Close()
}

func TestFindMappingByIpPort(t *testing.T) {
	mockDb := testcommon.InitSqlMock()
	mockDb.ExpectQuery("SELECT * FROM `ip_mapping` WHERE `ip` = ? AND `port` = ?").
		WithArgs("127.0.0.1", "80").
		WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))
	res, err := FindMappingByIpPort("127.0.0.1", "80", []map[string]string{
		{"ip": "ip", "port": "port"},
	})
	assert.Nil(t, err)
	assert.NotNil(t, res)
}

// TestCheckIp 测试 checkIp 函数
func TestCheckIp(t *testing.T) {
	tests := []struct {
		name     string
		ips      []string
		expected bool
	}{
		{
			name:     "单个有效IP",
			ips:      []string{"***********"},
			expected: true,
		},
		{
			name:     "多个有效IP",
			ips:      []string{"***********", "********", "**********"},
			expected: true,
		},
		{
			name:     "包含无效IP",
			ips:      []string{"***********", "invalid_ip"},
			expected: false,
		},
		{
			name:     "单个无效IP",
			ips:      []string{"invalid_ip"},
			expected: false,
		},
		{
			name:     "空IP列表",
			ips:      []string{},
			expected: true,
		},
		{
			name:     "IPv6地址",
			ips:      []string{"2001:db8::1"},
			expected: true,
		},
		{
			name:     "混合IPv4和IPv6",
			ips:      []string{"***********", "2001:db8::1"},
			expected: true,
		},
		{
			name:     "边界IP地址",
			ips:      []string{"0.0.0.0", "***************"},
			expected: true,
		},
		{
			name:     "超出范围的IP",
			ips:      []string{"256.256.256.256"},
			expected: false,
		},
		{
			name:     "格式错误的IP",
			ips:      []string{"192.168.1"},
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := checkIp(tt.ips...)
			assert.Equal(t, tt.expected, result, "checkIp函数返回值不符合预期")
		})
	}
}




