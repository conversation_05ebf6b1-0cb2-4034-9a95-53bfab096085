package ip_mapping

import (
	"github.com/agiledragon/gomonkey/v2"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"sync"
	"testing"
)

func TestImportIpMappingByHuaweiFWConfig(t *testing.T) {
	patches := []*gomonkey.Patches{
		gomonkey.ApplyFunc(saveData, func(ch chan map[string]interface{}, wg *sync.WaitGroup, successCount *int64) {
			defer wg.Done()
			count := 0
			for range ch {
				count++
			}
			*successCount = int64(count)
		}),
		gomonkey.ApplyFuncReturn(GetFieldName, map[string]string{"ip": "ip", "port": "port", "domain": "domain", "protocol": "protocol"}, true),
	}
	lines := []string{
		"nat server fofa大屏演示 protocol tcp global ************* inside ************ no-reverse",
		"nat server 17-K01API zone untrust protocol tcp global ************* 11300 inside ************ 3000 no-reverse",
		"aaa server 17-K01API zone untrust protocol tcp global ************* 11300 inside ************ 3000 no-reverse",
		"nat server 17-K01API zone untrust protocol tcp global ************* 113a0 inside ************ 3000 no-reverse",
		"nat server 17-K01API zone untrust protocol tcp global ************* 1130110 inside ************ 3000 nat-disable",
	}
	successCount, msg, err := ImportIpMappingByHuaweiFWConfig(uuid.New().String(), "华为防火墙8000E-X8配置文件", lines, "", "")
	for _, patch := range patches {
		patch.Reset()
	}
	assert.Equal(t, successCount, int64(2))
	assert.Equal(t, msg, "导入成功，成功解析2条数据，新增2条数据")
	assert.Nil(t, err)
}

func TestImportIpMappingByHuaweiFWConfigConfirm(t *testing.T) {
	fromMap := map[string]string{"ip": "fromIp", "port": "fromPort", "domain": "fromDomain", "protocol": "fromProtocol"}
	toMap := map[string]string{"ip": "toIp", "port": "toPort", "domain": "toDomain", "protocol": "toProtocol"}
	lines := []string{
		"nat server fofa大屏演示 protocol tcp global ************* inside ************ no-reverse",
		"nat server 17-K01API zone untrust protocol tcp global ************* 11300 inside ************ 3000 no-reverse",
		"aaa server 17-K01API zone untrust protocol tcp global ************* 11300 inside ************ 3000 no-reverse",
		"nat server 17-K01API zone untrust protocol tcp global ************* 113a0 inside ************ 3000 no-reverse",
		"nat server 17-K01API zone untrust protocol tcp global ************* 1130110 inside ************ 3000 nat-disable",
	}
	ipMappings, err := ImportIpMappingByHuaweiFWConfigConfirm(lines, fromMap, toMap)
	assert.Nil(t, err)
	assert.Equal(t, ipMappings[0]["fromIp"], "*************")
	assert.Equal(t, ipMappings[0]["toIp"], "************")
}
