package ip_mapping

import (
	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/models/mysql/column"
	"fobrain/models/mysql/custom_field"
	"github.com/DATA-DOG/go-sqlmock"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestListCustomFieldGroups(t *testing.T) {
	mockDb := testcommon.GetMysqlMock()

	mockDb.ExpectQuery("SELECT * FROM `custom_field_groups` WHERE `table` = ?").
		WithArgs("test").WillReturnRows(sqlmock.NewRows([]string{"id"}).
		AddRow(1))

	_, err := ListCustomFieldGroups("test")
	mockDb.Close()
	assert.Nil(t, err)
}

func TestListCustomFields(t *testing.T) {
	mockDb := testcommon.GetMysqlMock()

	mockDb.ExpectQuery("SELECT * FROM `custom_field_groups` WHERE `table` = ?").
		WithArgs("test").WillReturnRows(sqlmock.NewRows([]string{"id"}).
		AddRow(1))

	_, err := ListCustomFields("test")
	mockDb.Close()
	assert.Nil(t, err)
}

func TestCreateCustomFieldGroup(t *testing.T) {
	patches := []*gomonkey.Patches{
		gomonkey.ApplyMethodReturn(&custom_field.CustomField{}, "Create", nil),
		gomonkey.ApplyFuncReturn(column.CreateOrUpdateColumn, nil),
		gomonkey.ApplyMethodReturn(&custom_field.CustomFieldGroup{}, "Create", nil),
	}
	err := CreateCustomFieldGroup("test", 1)
	for _, patch := range patches {
		patch.Reset()
	}
	assert.Nil(t, err)
}

func TestUpdateCustomFieldGroup(t *testing.T) {
	patches := []*gomonkey.Patches{
		gomonkey.ApplyMethodReturn(&custom_field.CustomFieldGroup{}, "First", &custom_field.CustomFieldGroup{
			Name:   "test",
			Table:  "test",
			Fields: "[\"custom_field_1733990531_MMS8j4\",\"custom_field_1733990531_mTdRwc\",\"custom_field_1733990531_RXJhkW\",\"custom_field_1733990531_vpVChv\"]",
		}, nil),
		gomonkey.ApplyMethodReturn(&custom_field.CustomField{}, "First", &custom_field.CustomField{
			DisplayName:   "",
			FieldName:     "",
			FieldType:     "",
			Table:         "",
			Sort:          0,
			IsSystemField: false,
		}, nil),
		gomonkey.ApplyMethodReturn(&custom_field.CustomField{}, "Update", nil),
		gomonkey.ApplyMethodReturn(&custom_field.CustomFieldGroup{}, "Update", nil),
	}

	err := UpdateCustomFieldGroup(uint64(1), "test", 1)
	for _, patch := range patches {
		patch.Reset()
	}
	assert.Nil(t, err)
}

func TestDeleteCustomFieldGroup(t *testing.T) {
	patches := []*gomonkey.Patches{
		gomonkey.ApplyMethodReturn(&custom_field.CustomFieldGroup{}, "First", &custom_field.CustomFieldGroup{
			Name:   "test",
			Table:  "test",
			Fields: "[\"custom_field_1733990531_MMS8j4\",\"custom_field_1733990531_mTdRwc\",\"custom_field_1733990531_RXJhkW\",\"custom_field_1733990531_vpVChv\"]",
		}, nil),
		gomonkey.ApplyMethodReturn(&custom_field.CustomField{}, "First", &custom_field.CustomField{
			DisplayName:   "",
			FieldName:     "",
			FieldType:     "",
			Table:         "",
			Sort:          0,
			IsSystemField: false,
		}, nil),
		gomonkey.ApplyMethodReturn(&custom_field.CustomField{}, "Delete", nil),
		gomonkey.ApplyMethodReturn(&custom_field.CustomFieldGroup{}, "Delete", nil),
	}
	err := DeleteCustomFieldGroup(uint64(1))
	for _, patch := range patches {
		patch.Reset()
	}
	assert.Nil(t, err)
}
