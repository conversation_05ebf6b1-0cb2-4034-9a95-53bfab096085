package ip_mapping

import (
	"errors"
	"fmt"
	"regexp"
	"strings"
	"sync"
)

// ImportIpMappingByHuaweiConfig 导入IP映射-华为防火墙配置文件
func ImportIpMappingByHuaweiConfig(batchNo string, fileType string, lines []string, fromMapping string, toMapping string) (int64, string, error) {
	fromMap, ok := GetFieldName(fromMapping)
	if !ok {
		return 0, "", errors.New("导入映射文件失败，字段不存在")
	}
	toMap, ok := GetFieldName(toMapping)
	if !ok {
		return 0, "", errors.New("导入映射文件失败，字段不存在")
	}
	// 定义正则表达式来匹配所需的信息
	regex := `global\s+([\d.]+)\s+(\d+)\s+inside\s+([\d.]+)\s+(\w+)`
	re := regexp.MustCompile(regex)

	successCount := int64(0)
	totalCount := int64(0)
	wg := &sync.WaitGroup{}
	ipMappingChan := make(chan map[string]interface{}, 100)

	wg.Add(1)
	go saveData(ipMappingChan, wg, &successCount)

	for _, line := range lines {
		if !strings.HasPrefix(line, "nat server") {
			continue
		}
		matches := re.FindStringSubmatch(line)
		if len(matches) < 5 {
			continue
		}
		ipMapping := map[string]interface{}{
			"batch_no":          batchNo,
			"data_source":       fileType,
			fromMap["ip"]:       matches[1],
			fromMap["port"]:     matches[2],
			fromMap["domain"]:   "",
			fromMap["protocol"]: "",
			toMap["ip"]:         matches[3],
			toMap["port"]:       matches[4],
			toMap["domain"]:     "",
			toMap["protocol"]:   "",
		}
		if !checkIp(matches[1], matches[3]) || !checkPort(matches[2], matches[4]) {
			continue
		}
		ipMappingChan <- ipMapping
		totalCount++
	}

	close(ipMappingChan)
	wg.Wait()
	return successCount, fmt.Sprintf("导入成功，成功解析%d条数据，新增%d条数据", totalCount, successCount), nil
}

// ImportIpMappingByHuaweiConfigConfirm 导入IP映射-华为防火墙配置文件确认
func ImportIpMappingByHuaweiConfigConfirm(lines []string, fromMap, toMap map[string]string) ([]map[string]interface{}, error) {
	regex := `global\s+([\d.]+)\s+(\d+)\s+inside\s+([\d.]+)\s+(\w+)`
	re := regexp.MustCompile(regex)
	ipMappings := make([]map[string]interface{}, 0)
	for _, line := range lines {
		if !strings.HasPrefix(line, "nat server") {
			continue
		}
		matches := re.FindStringSubmatch(line)
		if len(matches) < 5 {
			continue
		}
		ipMapping := map[string]interface{}{
			fromMap["ip"]:       matches[1],
			fromMap["port"]:     matches[2],
			fromMap["domain"]:   "",
			fromMap["protocol"]: "",
			toMap["ip"]:         matches[3],
			toMap["port"]:       matches[4],
			toMap["domain"]:     "",
			toMap["protocol"]:   "",
		}
		ipMappings = append(ipMappings, ipMapping)
		if !checkIp(matches[1], matches[3]) || !checkPort(matches[2], matches[4]) {
			continue
		}
		if len(ipMappings) > 4 {
			break
		}
	}
	return ipMappings, nil
}

func getHuaWeiValueByRegexp(line string) (string, []string) {
	if regexp.MustCompile(`\bnat-disable\b`).MatchString(line) {
		return "", nil
	}
	expressions := map[string]*regexp.Regexp{
		"IncludePort":    regexp.MustCompile(`global\s+([\d.]+)\s+(\d+)\s+inside\s+([\d.]+)\s+(\d+)`),
		"NotIncludePort": regexp.MustCompile(`global\s+([\d.]+)\s+inside\s+([\d.]+)`),
	}
	for name, expr := range expressions {
		matches := expr.FindStringSubmatch(line)
		if matches != nil {
			return name, matches
		}
	}
	return "", nil
}
