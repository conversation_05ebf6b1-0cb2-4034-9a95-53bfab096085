package ip_mapping

import (
	"errors"
	"fmt"
	"regexp"
	"strings"
	"sync"
)

type Policy struct {
	SrcAddr  string `json:"src_addr"`
	PoolName string `json:"pool_name"`
}

// ImportIpMappingByFortinet 导入Fortinet防火墙配置文件
func ImportIpMappingByFortinet(batchNo string, fileType string, content string, fromMapping string, toMapping string) (int64, string, error) {
	fromMap, ok := GetFieldName(fromMapping)
	if !ok {
		return 0, "", errors.New("导入映射文件失败，字段不存在")
	}
	toMap, ok := GetFieldName(toMapping)
	if !ok {
		return 0, "", errors.New("导入映射文件失败，字段不存在")
	}

	successCount := int64(0)
	totalCount := int64(0)
	wg := &sync.WaitGroup{}
	ipMappingChan := make(chan map[string]interface{}, 100)

	wg.Add(1)
	go saveData(ipMappingChan, wg, &successCount)

	data, err := parseFortinetConfig(content)
	if err != nil {
		return 0, "", err
	}

	policies := data["policies"].([]Policy)
	addresses := data["addresses"].(map[string]string)

	for _, policy := range policies {
		srcAddr := policy.SrcAddr
		PoolName := policy.PoolName

		if _, ok := addresses[srcAddr]; !ok {
			continue
		}
		ipMapping := map[string]interface{}{
			"data_source":       fileType,
			"batch_no":          batchNo,
			fromMap["ip"]:       addresses[srcAddr],
			fromMap["port"]:     "",
			fromMap["domain"]:   "",
			fromMap["protocol"]: "",
			toMap["ip"]:         PoolName,
			toMap["port"]:       "",
			toMap["domain"]:     "",
			toMap["protocol"]:   "",
		}
		if !checkIp(addresses[srcAddr], PoolName) {
			continue
		}
		ipMappingChan <- ipMapping
		totalCount++
	}

	close(ipMappingChan)
	wg.Wait()
	return successCount, fmt.Sprintf("导入成功，成功解析%d条数据，新增%d条数据", totalCount, successCount), nil
}

// ImportIpMappingByFortinetConfirm 导入Fortinet防火墙配置文件
func ImportIpMappingByFortinetConfirm(content string, fromMap, toMap map[string]string) ([]map[string]interface{}, error) {
	ipMappings := make([]map[string]interface{}, 0)
	data, err := parseFortinetConfig(content)
	if err != nil {
		return ipMappings, err
	}

	policies := data["policies"].([]Policy)
	addresses := data["addresses"].(map[string]string)

	for _, policy := range policies {
		srcAddr := policy.SrcAddr
		PoolName := policy.PoolName

		if _, ok := addresses[srcAddr]; !ok {
			continue
		}
		ipMapping := map[string]interface{}{
			fromMap["ip"]:       addresses[srcAddr],
			fromMap["port"]:     "",
			fromMap["domain"]:   "",
			fromMap["protocol"]: "",
			toMap["ip"]:         PoolName,
			toMap["port"]:       "",
			toMap["domain"]:     "",
			toMap["protocol"]:   "",
		}
		if !checkIp(addresses[srcAddr], PoolName) {
			continue
		}
		ipMappings = append(ipMappings, ipMapping)
		if len(ipMappings) > 4 {
			break
		}
	}
	return ipMappings, nil
}

func parseFortinetConfig(config string) (map[string]interface{}, error) {
	policies := []Policy{}
	addresses := make(map[string]string)
	srcaddrs := []string{}

	// 解析 config firewall policy 部分
	policyPattern := regexp.MustCompile(`(?s)config firewall policy(.*?)end`)
	policyMatches := policyPattern.FindAllStringSubmatch(config, -1)

	for _, match := range policyMatches {
		if len(match) < 2 {
			continue
		}
		block := match[1]

		// 匹配 edit 到 next 之间的内容
		entryPattern := regexp.MustCompile(`(?s)edit.*?next`)
		entries := entryPattern.FindAllString(block, -1)

		for _, entry := range entries {
			// 检查是否有 set nat enable 且没有 set status disable
			hasNatEnable := regexp.MustCompile(`set nat enable`).MatchString(entry)
			hasStatusDisable := regexp.MustCompile(`set status disable`).MatchString(entry)

			if hasNatEnable && !hasStatusDisable {
				// 提取 srcaddr 和 poolname
				srcaddrPattern := regexp.MustCompile(`set srcaddr "([^"]*)"`)
				poolnamePattern := regexp.MustCompile(`set poolname "([^"]*)"`)

				srcaddrMatch := srcaddrPattern.FindStringSubmatch(entry)
				poolnameMatch := poolnamePattern.FindStringSubmatch(entry)

				if len(srcaddrMatch) > 1 && len(poolnameMatch) > 1 {
					srcaddrs = append(srcaddrs, srcaddrMatch[1])
					policies = append(policies, Policy{
						SrcAddr:  srcaddrMatch[1],
						PoolName: strings.ReplaceAll(poolnameMatch[1], "/32", ""),
					})
				}
			}
		}
	}

	// 解析 config firewall address 部分
	addressPattern := regexp.MustCompile(`(?s)config firewall address\s+((?:edit.*?next\s*)*)end\s*`)
	addressMatches := addressPattern.FindAllStringSubmatch(config, -1)

	for _, match := range addressMatches {
		if len(match) < 2 {
			continue
		}
		block := match[1]

		// 过滤掉包含 "address6" 的块
		if strings.Contains(block, "address6") {
			continue
		}

		// 匹配 edit 到 next 之间的内容
		entryPattern := regexp.MustCompile(`(?s)edit.*?next`)
		entries := entryPattern.FindAllString(block, -1)

		for _, entry := range entries {
			// 检查是否存在 set subnet
			if regexp.MustCompile(`set subnet`).MatchString(entry) {
				// 提取 edit 和 subnet
				editPattern := regexp.MustCompile(`edit "([^"]*)"`)
				subnetPattern := regexp.MustCompile(`set subnet (\d{1,3}(?:\.\d{1,3}){3})`)

				editMatch := editPattern.FindStringSubmatch(entry)
				subnetMatch := subnetPattern.FindStringSubmatch(entry)

				if len(editMatch) > 1 && len(subnetMatch) > 1 {
					// 检查是否在srcaddrs中
					found := false
					for _, addr := range srcaddrs {
						if addr == editMatch[1] {
							addresses[editMatch[1]] = subnetMatch[1]
							found = true
							break
						}
					}

					// 如果没找到，检查comment
					if !found && regexp.MustCompile(`set comment`).MatchString(entry) {
						commentPattern := regexp.MustCompile(`set comment "([^"]*)"`)
						commentMatch := commentPattern.FindStringSubmatch(entry)
						if len(commentMatch) > 1 {
							for _, addr := range srcaddrs {
								if addr == commentMatch[1] {
									addresses[commentMatch[1]] = subnetMatch[1]
									break
								}
							}
						}
					}
				}
			}
		}
	}
	result := map[string]interface{}{
		"policies":  policies,
		"addresses": addresses,
	}

	return result, nil
}
