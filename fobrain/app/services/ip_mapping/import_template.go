package ip_mapping

import (
	"errors"
	"fmt"
	"sync"
)

// ImportIpMappingByTemplate 导入IP映射-模板文件
func ImportIpMappingByTemplate(batchNo string, fileType string, rows [][]string, fromMapping string, toMapping string) (int64, string, error) {
	fromMap, ok := GetFieldName(fromMapping)
	if !ok {
		return 0, "", errors.New("导入映射文件失败，字段不存在")
	}
	toMap, ok := GetFieldName(toMapping)
	if !ok {
		return 0, "", errors.New("导入映射文件失败，字段不存在")
	}

	successCount := int64(0)
	totalCount := int64(0)
	wg := &sync.WaitGroup{}
	ipMappingChan := make(chan map[string]interface{}, 100)

	wg.Add(1)
	go saveData(ipMappingChan, wg, &successCount)

	for i, row := range rows {
		if i < 1 {
			continue
		}
		if len(row) < 4 {
			continue
		}

		// 创建映射关系
		ipMapping := map[string]interface{}{
			"batch_no":          batchNo,
			"data_source":       fileType,
			fromMap["ip"]:       row[0],
			fromMap["port"]:     row[1],
			fromMap["domain"]:   "",
			fromMap["protocol"]: "",
			toMap["ip"]:         row[4],
			toMap["port"]:       row[5],
			toMap["domain"]:     "",
			toMap["protocol"]:   "",
		}
		if !checkIp(row[0], row[4]) {
			continue
		}
		ipMappingChan <- ipMapping
		totalCount++
	}
	// 关闭通道
	close(ipMappingChan)
	wg.Wait()
	return successCount, fmt.Sprintf("导入成功，成功解析%d条数据，新增%d条数据", totalCount, successCount), nil
}

// ImportIpMappingByTemplateConfirm 导入IP映射-模板文件-确认
func ImportIpMappingByTemplateConfirm(rows [][]string, fromMap, toMap map[string]string) ([]map[string]interface{}, error) {
	ipMappings := make([]map[string]interface{}, 0)
	for i, row := range rows {
		if i < 1 {
			continue
		}
		if len(row) < 4 {
			continue
		}

		ipMapping := map[string]interface{}{
			fromMap["ip"]:       row[0],
			fromMap["port"]:     row[1],
			fromMap["domain"]:   "",
			fromMap["protocol"]: "",
			toMap["ip"]:         row[4],
			toMap["port"]:       row[5],
			toMap["domain"]:     "",
			toMap["protocol"]:   "",
		}
		if !checkIp(row[0], row[4]) {
			continue
		}
		ipMappings = append(ipMappings, ipMapping)
		if len(ipMappings) > 4 {
			break
		}
	}
	return ipMappings, nil
}
