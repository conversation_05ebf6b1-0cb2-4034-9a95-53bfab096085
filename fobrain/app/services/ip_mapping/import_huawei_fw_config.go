package ip_mapping

import (
	"errors"
	"fmt"
	"strings"
	"sync"
)

func ImportIpMappingByHuaweiFWConfig(batchNo string, fileType string, lines []string, fromMapping string, toMapping string) (int64, string, error) {
	fromMap, ok := GetFieldName(fromMapping)
	if !ok {
		return 0, "", errors.New("导入映射文件失败，字段不存在")
	}
	toMap, ok := GetFieldName(toMapping)
	if !ok {
		return 0, "", errors.New("导入映射文件失败，字段不存在")
	}

	successCount := int64(0)
	totalCount := int64(0)
	wg := &sync.WaitGroup{}
	ipMappingChan := make(chan map[string]interface{}, 100)

	wg.Add(1)
	go saveData(ipMappingChan, wg, &successCount)

	for _, line := range lines {
		if !strings.HasPrefix(line, "nat server") {
			continue
		}
		var ipMapping map[string]interface{}
		keyType, matches := getHuaWeiValueByRegexp(line)
		if keyType == "IncludePort" && len(matches) == 5 {
			ipMapping = map[string]interface{}{
				"data_source":       fileType,
				"batch_no":          batchNo,
				fromMap["ip"]:       matches[1],
				fromMap["port"]:     matches[2],
				fromMap["domain"]:   "",
				fromMap["protocol"]: "",
				toMap["ip"]:         matches[3],
				toMap["port"]:       matches[4],
				toMap["domain"]:     "",
				toMap["protocol"]:   "",
			}
			if !checkIp(matches[1], matches[3]) || !checkPort(matches[2], matches[4]) {
				continue
			}
			ipMappingChan <- ipMapping
			totalCount++
		} else if keyType == "NotIncludePort" && len(matches) == 3 {
			ipMapping = map[string]interface{}{
				"data_source":       fileType,
				"batch_no":          batchNo,
				fromMap["ip"]:       matches[1],
				fromMap["port"]:     "",
				fromMap["domain"]:   "",
				fromMap["protocol"]: "",
				toMap["ip"]:         matches[2],
				toMap["port"]:       "",
				toMap["domain"]:     "",
				toMap["protocol"]:   "",
			}
			if !checkIp(matches[1], matches[2]) {
				continue
			}
			ipMappingChan <- ipMapping
			totalCount++
		}
	}

	close(ipMappingChan)
	wg.Wait()
	return successCount, fmt.Sprintf("导入成功，成功解析%d条数据，新增%d条数据", totalCount, successCount), nil
}

func ImportIpMappingByHuaweiFWConfigConfirm(lines []string, fromMap, toMap map[string]string) ([]map[string]interface{}, error) {
	ipMappings := make([]map[string]interface{}, 0)
	for _, line := range lines {
		if !strings.HasPrefix(line, "nat server") {
			continue
		}
		var ipMapping map[string]interface{}
		keyType, matches := getHuaWeiValueByRegexp(line)
		if keyType == "IncludePort" && len(matches) == 5 {
			ipMapping = map[string]interface{}{
				fromMap["ip"]:       matches[1],
				fromMap["port"]:     matches[2],
				fromMap["domain"]:   "",
				fromMap["protocol"]: "",
				toMap["ip"]:         matches[3],
				toMap["port"]:       matches[4],
				toMap["domain"]:     "",
				toMap["protocol"]:   "",
			}
			if !checkIp(matches[1], matches[3]) || !checkPort(matches[2], matches[4]) {
				continue
			}
			ipMappings = append(ipMappings, ipMapping)
			if len(ipMappings) > 4 {
				break
			}
		} else if keyType == "NotIncludePort" && len(matches) == 3 {
			ipMapping = map[string]interface{}{
				fromMap["ip"]:       matches[1],
				fromMap["port"]:     "",
				fromMap["domain"]:   "",
				fromMap["protocol"]: "",
				toMap["ip"]:         matches[2],
				toMap["port"]:       "",
				toMap["domain"]:     "",
				toMap["protocol"]:   "",
			}
			if !checkIp(matches[1], matches[2]) {
				continue
			}
			ipMappings = append(ipMappings, ipMapping)
			if len(ipMappings) > 4 {
				break
			}
		}
	}
	return ipMappings, nil
}
