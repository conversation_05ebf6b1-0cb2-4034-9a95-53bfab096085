package ip_mapping

import (
	"github.com/agiledragon/gomonkey/v2"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"sync"
	"testing"
)

func TestImportIpMappingByTemplate(t *testing.T) {
	rows := [][]string{{"公网IP", "公网端口", "公网协议", "公网域名", "内网IP", "内网端口", "内网协议", "内网域名"},
		{"***********", "80", "", "", "***********", "8880", "", ""},
	}
	patches := []*gomonkey.Patches{
		gomonkey.ApplyFunc(saveData, func(ch chan map[string]interface{}, wg *sync.WaitGroup, successCount *int64) {
			defer wg.Done()
			count := 0
			for range ch {
				count++
			}
			*successCount = int64(count)
		}),
		gomonkey.ApplyFuncReturn(GetFieldName, map[string]string{"ip": "ip", "port": "port", "domain": "domain", "protocol": "protocol"}, true),
	}
	successCount, msg, err := ImportIpMappingByTemplate(uuid.New().String(), "", rows, "", "")
	for _, patch := range patches {
		patch.Reset()
	}
	assert.Equal(t, successCount, int64(1))
	assert.Equal(t, msg, "导入成功，成功解析1条数据，新增1条数据")
	assert.Nil(t, err)
}

func TestImportIpMappingByTemplateConfirm(t *testing.T) {
	rows := [][]string{{"公网IP", "公网端口", "公网协议", "公网域名", "内网IP", "内网端口", "内网协议", "内网域名"},
		{"***********", "80", "", "", "***********", "8880", "", ""},
	}
	fromMap := map[string]string{"ip": "fromIp", "port": "fromPort", "domain": "fromDomain", "protocol": "fromProtocol"}
	toMap := map[string]string{"ip": "toIp", "port": "toPort", "domain": "toDomain", "protocol": "toProtocol"}
	ipMappings, err := ImportIpMappingByTemplateConfirm(rows, fromMap, toMap)
	assert.Nil(t, err)
	assert.Equal(t, ipMappings[0]["fromIp"], "***********")
	assert.Equal(t, ipMappings[0]["toIp"], "***********")
}
