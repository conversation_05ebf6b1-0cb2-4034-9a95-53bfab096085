package ip_mapping

import (
	"bufio"
	"errors"
	"fmt"
	"github.com/spf13/cast"
	"io"
	"regexp"
	"strconv"
	"strings"
	"sync"
)

// A10数据源

type ServiceGroupMember struct {
	Ip       string
	Port     int
	Protocol string
}

type HostSwitching struct {
	Domain string
	Pool   string
}

// PrepareA10Data 生成A10数据
func PrepareA10Data(batchNo string, fileType string, datas map[string][]string, ipMappingChan chan map[string]interface{}, fromMap, toMap map[string]string) int64 {
	totalCount := int64(0)
	serviceGroupMap := make(map[string][]*ServiceGroupMember)
	templateHttpMap := make(map[string][]*HostSwitching)
	for key, value := range datas {
		keyType, matches := getA10ValueByRegexp(key)
		if keyType == "SlbServiceGroup" {
			slbServiceGroup := matches[1]
			for _, line := range value {
				lineType, memberMatches := getA10ValueByRegexp(line)
				if lineType == "Member" {
					member := &ServiceGroupMember{
						Ip:       memberMatches[1],
						Port:     cast.ToInt(memberMatches[2]),
						Protocol: matches[2],
					}
					serviceGroupMap[slbServiceGroup] = append(serviceGroupMap[slbServiceGroup], member)
				}
			}
		}
		if keyType == "SlbTemplateHttpHostSwitching" || keyType == "SlbTemplateHttpHost" {
			templateHttp := matches[1]
			for _, line := range value {
				lineType, hostSwitchingMatches := getA10ValueByRegexp(line)
				if lineType == "HostSwitching" {
					hostSwitching := &HostSwitching{
						Domain: hostSwitchingMatches[1],
						Pool:   hostSwitchingMatches[2],
					}
					templateHttpMap[templateHttp] = append(templateHttpMap[templateHttp], hostSwitching)
				}
			}
		}
	}
	for key, value := range datas {
		keyType, matches := getA10ValueByRegexp(key)
		if keyType == "PublicIp" {
			publicIp := matches[1]
			publicPort := make([]string, 0)
			for _, line := range value {
				lineType, subMatches := getA10ValueByRegexp(line)
				if lineType == "PublicPort" {
					publicPort = subMatches
					continue
				}
				if lineType == "ServiceGroup" {
					serviceGroup := subMatches[1]
					if serviceGroupMembers, ok := serviceGroupMap[serviceGroup]; ok {
						for _, serviceGroupMember := range serviceGroupMembers {
							ipMapping := map[string]interface{}{
								"batch_no":          batchNo,
								"data_source":       fileType,
								fromMap["ip"]:       publicIp,
								fromMap["port"]:     publicPort[1],
								fromMap["domain"]:   "",
								fromMap["protocol"]: publicPort[2],
								toMap["ip"]:         serviceGroupMember.Ip,
								toMap["port"]:       cast.ToString(serviceGroupMember.Port),
								toMap["domain"]:     "",
								toMap["protocol"]:   serviceGroupMember.Protocol,
							}
							if !checkIp(publicIp, serviceGroupMember.Ip) || !checkPort(publicPort[1], strconv.Itoa(serviceGroupMember.Port)) {
								continue
							}
							ipMappingChan <- ipMapping
							totalCount++
						}
					}
				}
				if lineType == "TemplateHttpHostSwitching" || lineType == "TemplateHttpHost" {
					templateHttpMapKey := "http-hostswitching"
					if lineType == "TemplateHttpHost" {
						templateHttpMapKey = "http_host"
					}
					for _, hostSwitching := range templateHttpMap[templateHttpMapKey] {
						serviceGroup := hostSwitching.Pool
						if serviceGroupMembers, ok := serviceGroupMap[serviceGroup]; ok {
							for _, serviceGroupMember := range serviceGroupMembers {
								ipMapping := map[string]interface{}{
									"batch_no":          batchNo,
									"data_source":       fileType,
									fromMap["ip"]:       publicIp,
									fromMap["port"]:     publicPort[1],
									fromMap["domain"]:   hostSwitching.Domain,
									fromMap["protocol"]: publicPort[2],
									toMap["ip"]:         serviceGroupMember.Ip,
									toMap["port"]:       cast.ToString(serviceGroupMember.Port),
									toMap["domain"]:     "",
									toMap["protocol"]:   serviceGroupMember.Protocol,
								}
								if !checkIp(publicIp, serviceGroupMember.Ip) || !checkPort(publicPort[1], strconv.Itoa(serviceGroupMember.Port)) {
									continue
								}
								ipMappingChan <- ipMapping
								totalCount++
							}
						}
					}
				}
			}
		}
	}
	return totalCount
}

// ImportIpMappingByA10 导入IP映射-A10数据源
func ImportIpMappingByA10(batchNo string, fileType string, datas map[string][]string, fromMapping string, toMapping string) (int64, string, error) {
	fromMap, ok := GetFieldName(fromMapping)
	if !ok {
		return 0, "", errors.New("导入映射文件失败，字段不存在")
	}
	toMap, ok := GetFieldName(toMapping)
	if !ok {
		return 0, "", errors.New("导入映射文件失败，字段不存在")
	}

	successCount := int64(0)
	wg := &sync.WaitGroup{}
	ipMappingChan := make(chan map[string]interface{}, 100)

	wg.Add(1)
	go saveData(ipMappingChan, wg, &successCount)

	totalCount := PrepareA10Data(batchNo, fileType, datas, ipMappingChan, fromMap, toMap)

	close(ipMappingChan)
	wg.Wait()
	return successCount, fmt.Sprintf("导入成功，成功解析%d条数据，新增%d条数据", totalCount, successCount), nil
}

// ImportIpMappingByA10Confirm 导入IP映射-A10数据源-确认
func ImportIpMappingByA10Confirm(datas map[string][]string, fromMap, toMap map[string]string) ([]map[string]interface{}, error) {
	ipMappings := make([]map[string]interface{}, 0)
	serviceGroupMap := make(map[string][]*ServiceGroupMember)
	templateHttpMap := make(map[string][]*HostSwitching)
	for key, value := range datas {
		keyType, matches := getA10ValueByRegexp(key)
		if keyType == "SlbServiceGroup" {
			slbServiceGroup := matches[1]
			for _, line := range value {
				lineType, memberMatches := getA10ValueByRegexp(line)
				if lineType == "Member" {
					member := &ServiceGroupMember{
						Ip:       memberMatches[1],
						Port:     cast.ToInt(memberMatches[2]),
						Protocol: matches[2],
					}
					serviceGroupMap[slbServiceGroup] = append(serviceGroupMap[slbServiceGroup], member)
				}
			}
		}
		if keyType == "SlbTemplateHttpHostSwitching" || keyType == "SlbTemplateHttpHost" {
			templateHttp := matches[1]
			for _, line := range value {
				lineType, hostSwitchingMatches := getA10ValueByRegexp(line)
				if lineType == "HostSwitching" {
					hostSwitching := &HostSwitching{
						Domain: hostSwitchingMatches[1],
						Pool:   hostSwitchingMatches[2],
					}
					templateHttpMap[templateHttp] = append(templateHttpMap[templateHttp], hostSwitching)
				}
			}
		}
	}
	for key, value := range datas {
		keyType, matches := getA10ValueByRegexp(key)
		if keyType == "PublicIp" {
			publicIp := matches[1]
			publicPort := make([]string, 0)
			for _, line := range value {
				lineType, subMatches := getA10ValueByRegexp(line)
				if lineType == "PublicPort" {
					publicPort = subMatches
					continue
				}
				if lineType == "ServiceGroup" {
					serviceGroup := subMatches[1]
					if serviceGroupMembers, ok := serviceGroupMap[serviceGroup]; ok {
						for _, serviceGroupMember := range serviceGroupMembers {
							ipMapping := map[string]interface{}{
								fromMap["ip"]:       publicIp,
								fromMap["port"]:     publicPort[1],
								fromMap["domain"]:   "",
								fromMap["protocol"]: publicPort[2],
								toMap["ip"]:         serviceGroupMember.Ip,
								toMap["port"]:       cast.ToString(serviceGroupMember.Port),
								toMap["domain"]:     "",
								toMap["protocol"]:   serviceGroupMember.Protocol,
							}
							if !checkIp(publicIp, serviceGroupMember.Ip) || !checkPort(publicPort[1], strconv.Itoa(serviceGroupMember.Port)) {
								continue
							}
							ipMappings = append(ipMappings, ipMapping)
							if len(ipMappings) > 4 {
								break
							}
						}
					}
					if len(ipMappings) > 4 {
						break
					}
				}
				if lineType == "TemplateHttpHostSwitching" || lineType == "TemplateHttpHost" {
					templateHttpMapKey := "http-hostswitching"
					if lineType == "TemplateHttpHost" {
						templateHttpMapKey = "http_host"
					}
					for _, hostSwitching := range templateHttpMap[templateHttpMapKey] {
						serviceGroup := hostSwitching.Pool
						if serviceGroupMembers, ok := serviceGroupMap[serviceGroup]; ok {
							for _, serviceGroupMember := range serviceGroupMembers {
								ipMapping := map[string]interface{}{
									fromMap["ip"]:       publicIp,
									fromMap["port"]:     publicPort[1],
									fromMap["domain"]:   hostSwitching.Domain,
									fromMap["protocol"]: publicPort[2],
									toMap["ip"]:         serviceGroupMember.Ip,
									toMap["port"]:       cast.ToString(serviceGroupMember.Port),
									toMap["domain"]:     "",
									toMap["protocol"]:   serviceGroupMember.Protocol,
								}
								if !checkIp(publicIp, serviceGroupMember.Ip) || !checkPort(publicPort[1], strconv.Itoa(serviceGroupMember.Port)) {
									continue
								}
								ipMappings = append(ipMappings, ipMapping)
								if len(ipMappings) > 4 {
									break
								}
							}
						}
						if len(ipMappings) > 4 {
							break
						}
					}

				}
				if len(ipMappings) > 4 {
					break
				}
			}
		}
	}
	return ipMappings, nil
}

// BuildA10Data 初步处理A10数据
func BuildA10Data(f io.Reader) map[string][]string {
	// 打开文件
	scanner := bufio.NewScanner(f)
	datas := make(map[string][]string)
	key := ""
	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())
		if strings.HasPrefix(line, "slb virtual-server vip_") ||
			strings.HasPrefix(line, "slb template http http-hostswitching") ||
			strings.HasPrefix(line, "slb template http http_host") ||
			strings.HasPrefix(line, "slb service-group") {
			key = line
		}
		if ((strings.HasPrefix(line, "port") ||
			strings.HasPrefix(line, "service-group")) ||
			strings.HasPrefix(line, "template http http_host") ||
			strings.HasPrefix(line, "template http http-hostswitching") ||
			strings.HasPrefix(line, "host-switching") ||
			strings.HasPrefix(line, "member")) && key != "" {
			datas[key] = append(datas[key], line)
		}
		if strings.HasPrefix(line, "!") {
			key = ""
		}
	}
	return datas
}

func getA10ValueByRegexp(line string) (string, []string) {
	expressions := map[string]*regexp.Regexp{
		"PublicIp":                     regexp.MustCompile(`slb\s+virtual-server\s+vip_([\d.]+)\s+([\d.]+)`),
		"PublicPort":                   regexp.MustCompile(`port\s+(\d+)\s+(\w+)`),
		"ServiceGroup":                 regexp.MustCompile(`^service-group\s+(.*)$`),
		"SlbTemplateHttpHostSwitching": regexp.MustCompile(`^slb\s+template\s+http\s+(http-hostswitching)$`),
		"TemplateHttpHostSwitching":    regexp.MustCompile(`^template\s+http\s+http-hostswitching$`),
		"SlbTemplateHttpHost":          regexp.MustCompile(`^slb\s+template\s+http\s+(http_host)$`),
		"TemplateHttpHost":             regexp.MustCompile(`^template\s+http\s+http_host$`),
		"HostSwitching":                regexp.MustCompile(`^host-switching\s+.*\s+([a-zA-Z0-9][a-zA-Z0-9\-\.]+\.[a-zA-Z]{2,})\s+service-group\s+(pool_[^_]+_\d+_\w+)$`),
		"SlbServiceGroup":              regexp.MustCompile(`^slb service-group\s+(.*)\s+(\w+)$`),
		"Member":                       regexp.MustCompile(`member\s+([\d.]+)\s+(\d+)`),
	}

	for name, expr := range expressions {
		matches := expr.FindStringSubmatch(line)
		if matches != nil {
			return name, matches
		}
	}
	return "", nil
}
