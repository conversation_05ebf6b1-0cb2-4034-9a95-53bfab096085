package ip_mapping

import (
	"github.com/agiledragon/gomonkey/v2"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"strings"
	"sync"
	"testing"
)

var A10StringData = BuildA10Data(strings.NewReader(`
slb virtual-server vip_************** **************
  port 26 tcp
    access-list 10 source-nat-pool sant_NeiWang_to_Fabu_100.200.1.1
    service-group pool_***********_26_tcp
    use-rcv-hop-for-resp
  port 80 http
    access-list 10 source-nat-pool sant_NeiWang_to_Fabu_100.200.1.1
    use-rcv-hop-for-resp
    template http http_host
  port 81 tcp
    access-list 10 source-nat-pool sant_NeiWang_to_Fabu_100.200.1.1
    service-group pool_***********_81_tcp
    use-rcv-hop-for-resp
  port 143 tcp
    access-list 10 source-nat-pool sant_NeiWang_to_Fabu_100.200.1.1
    service-group pool_mail_143_tcp
    use-rcv-hop-for-resp
  port 443 https
    access-list 10 source-nat-pool sant_NeiWang_to_Fabu_100.200.1.1
    use-rcv-hop-for-resp
    template persist cookie cookie-group-10min
    template http http-hostswitching
    template server-ssl server-ssl-templatel
    template client-ssl client-ssl-template
!
slb service-group pool_***********_26_tcp tcp
  health-check tcp_halfopen
  member *********** 26
!
slb template http http_host
  host-switching contains private-oss.longi.com service-group pool_*************_80_tcp
  host-switching contains private-challenger.longi.com service-group pool_*************_80_tcp
!
slb service-group pool_***********_81_tcp tcp
  health-check tcp_halfopen
  member *********** 81
!
slb service-group pool_mail_143_tcp tcp
  health-check tcp_143
  member *********** 143
  member *********** 143
  member *********** 143
!
slb template http http-hostswitching
  host-switching contains mail.longi.com service-group pool_mail_443_tcp
  host-switching contains email.longi.com service-group pool_mail_443_tcp
  host-switching contains rdcp.longi.com service-group pool_***********_443_tcp
  host-switching contains tmsuat.longi.com service-group pool_**************_443_tcp
!
slb service-group pool_mail_443_tcp tcp
  health-check tcp_443
  member *********** 443
  member *********** 443
  member *********** 443
!
slb service-group pool_*************_80_tcp tcp
  health-check tcp_halfopen
  member ************* 80
!
slb service-group pool_*************_80_tcp tcp
  health-check tcp_halfopen
  member ************* 80
!
slb service-group pool_***********_443_tcp tcp
  health-check tcp_halfopen
  member *********** 443
!
slb service-group pool_**************_443_tcp tcp
  health-check tcp_halfopen
  member ************** 443
!`))

func TestImportIpMappingByA10(t *testing.T) {
	protocolExist := false
	patches := []*gomonkey.Patches{
		gomonkey.ApplyFunc(saveData, func(ch chan map[string]interface{}, wg *sync.WaitGroup, successCount *int64) {
			defer wg.Done()
			count := 0
			for ipMapping := range ch {
				if ipMapping["protocol"] != "" {
					protocolExist = true
				}
				count++
			}
			*successCount = int64(count)
		}),
		gomonkey.ApplyFuncReturn(GetFieldName, map[string]string{"ip": "ip", "port": "port", "domain": "domain", "protocol": "protocol"}, true),
	}
	successCount, msg, err := ImportIpMappingByA10(uuid.New().String(), "A10数据源", A10StringData, "", "")
	for _, patch := range patches {
		patch.Reset()
	}
	assert.True(t, protocolExist)
	assert.Equal(t, successCount, int64(15))
	assert.Equal(t, msg, "导入成功，成功解析15条数据，新增15条数据")
	assert.Nil(t, err)
}

func TestImportIpMappingByA10Confirm(t *testing.T) {
	fromMap := map[string]string{"ip": "fromIp", "port": "fromPort", "domain": "fromDomain", "protocol": "fromProtocol"}
	toMap := map[string]string{"ip": "toIp", "port": "toPort", "domain": "toDomain", "protocol": "toProtocol"}
	ipMappings, err := ImportIpMappingByA10Confirm(A10StringData, fromMap, toMap)
	assert.Equal(t, ipMappings[0]["fromIp"], "**************")
	assert.Nil(t, err)
}

func TestPrepareA10Data(t *testing.T) {
	fromMap := map[string]string{"ip": "fromIp", "port": "fromPort", "domain": "fromDomain", "protocol": "fromProtocol"}
	toMap := map[string]string{"ip": "toIp", "port": "toPort", "domain": "toDomain", "protocol": "toProtocol"}
	ipMappingChan := make(chan map[string]interface{}, 100)
	totalCount := PrepareA10Data("", "A10数据源", A10StringData, ipMappingChan, fromMap, toMap)
	assert.True(t, totalCount > 0)
}

func TestBuildA10Data(t *testing.T) {
	_, ok := A10StringData["slb virtual-server vip_************** **************"]
	assert.True(t, ok)
}
