package ip_mapping

import (
	"context"
	"fmt"
	"fobrain/models/mysql/custom_field"
	"strings"
	"sync"
	"time"

	"fobrain/fobrain/logs"
	"fobrain/initialize/es"
	"fobrain/initialize/mysql"
	"fobrain/models/mysql/ip_mapping"
	"fobrain/pkg/utils"

	esmodel "fobrain/models/elastic/assets"

	"github.com/olivere/elastic/v7"
	"github.com/spf13/cast"
	"go-micro.dev/v4/logger"
)

// 保存映射数据
func saveData(ch chan map[string]interface{}, wg *sync.WaitGroup, successCount *int64) {
	defer wg.Done()
	logger := logs.GetLogger()
	ipMappingList := make([]map[string]interface{}, 0)
	auditDataList := make([]*ip_mapping.IpMappingAuditData, 0)
	for ipMapping := range ch {
		// 添加映射关系
		ipMappingList = append(ipMappingList, ipMapping)
		// 创建审计数据
		_, okPublic := ipMapping["public_ip"]
		_, okPrivate := ipMapping["private_ip"]
		if okPublic && okPrivate {
			auditDataList = append(auditDataList, &ip_mapping.IpMappingAuditData{
				BatchNo:       cast.ToString(ipMapping["batch_no"]),
				PublicDomain:  cast.ToString(ipMapping["public_domain"]),
				PublicIp:      cast.ToString(ipMapping["public_ip"]),
				PublicPort:    cast.ToUint32(ipMapping["public_port"]),
				PrivateDomain: cast.ToString(ipMapping["private_domain"]),
				PrivateIp:     cast.ToString(ipMapping["private_ip"]),
				PrivatePort:   cast.ToUint32(ipMapping["private_port"]),
			})
		}
		// 每 1000 条保存一次数据
		if len(ipMappingList) >= 1000 {
			// 保存映射关系
			rowsAffected, err := ip_mapping.NewIpMappingModel().CreateBatch(context.Background(), ipMappingList)
			if err != nil {
				logger.Error("保存映射关系失败,err:%v", err)
			}
			// 保存审计数据
			err = ip_mapping.NewIpMappingAuditDataModel().CreateBatch(context.Background(), auditDataList)
			if err != nil {
				logger.Error("保存审计数据失败,err:%v", err)
			}
			*successCount += rowsAffected
			ipMappingList = make([]map[string]interface{}, 0)
			auditDataList = make([]*ip_mapping.IpMappingAuditData, 0)
		}
	}
	if len(ipMappingList) > 0 {
		rowsAffected, err := ip_mapping.NewIpMappingModel().CreateBatch(context.Background(), ipMappingList)
		if err != nil {
			logger.Error("保存映射关系失败,err:%v", err)
		}
		err = ip_mapping.NewIpMappingAuditDataModel().CreateBatch(context.Background(), auditDataList)
		if err != nil {
			logger.Error("保存审计数据失败,err:%v", err)
		}
		*successCount += rowsAffected
	}
}

// GetIpMappingForAlarm 获取内外网映射关系，用于标记报警
func GetIpMappingForAlarm(batchNo string) error {
	ctx := context.Background()
	logger := logs.GetLogger()
	page := 1
	perPage := 100
	handleCount := int64(0)
	wg := &sync.WaitGroup{}
	var globalErr error
	for {
		data, count, err := ip_mapping.NewIpMappingModel().List(page, perPage, "")
		if err != nil {
			logger.Errorf("获取内外网映射关系失败,page:%d,perPage:%d,err:%v", page, perPage, err)
			globalErr = err
			break
		}
		handleCount += int64(len(data))
		page++

		wg.Add(1)
		// 检查IP映射关系是否存在资产
		go checkIpMappingAsset(batchNo, data, wg)

		if handleCount >= count {
			break
		}
		if len(data) < perPage {
			break // 如果获取的数据少于每页的数量，说明已经是最后一页
		}
	}
	wg.Wait()

	if globalErr == nil {
		// 删除旧批次的统计数据
		err := ip_mapping.NewIpMappingStatisticsModel().DeleteByBatchNo(ctx, batchNo)
		if err != nil {
			logger.Errorf("删除旧批次的统计数据失败,err:%v", err)
			globalErr = err
		}
	}

	return globalErr
}

// 检查IP映射关系是否存在资产
func checkIpMappingAsset(batchNo string, ipMappingList []map[string]interface{}, wg *sync.WaitGroup) {
	defer wg.Done()
	// 收集所有ip+port
	// 根据ip+port查询是否存在资产
	// 如果不存在资产，则报警
	logger := logs.GetLogger()

	// key=ip:port, value=ipMappingId, 公网IP映射关系,保存ipMappingId为了更新报警数据
	ipMappingPublicMap := make(map[string][]uint64)
	// key=ip:port, value=ipMappingId, 内网IP映射关系,保存ipMappingId为了更新报警数据
	ipMappingPrivateMap := make(map[string][]uint64)
	// 公网IP列表，方便查询已有资产
	publicIpList := make([]string, 0)
	// 内网IP列表，方便查询已有资产
	privateIpList := make([]string, 0)
	for _, ipMapping := range ipMappingList {
		publicKey := fmt.Sprintf("%s:%s", ipMapping["public_ip"], ipMapping["public_port"])
		privateKey := fmt.Sprintf("%s:%s", ipMapping["private_ip"], ipMapping["private_port"])
		ipMappingPublicMap[publicKey] = append(ipMappingPublicMap[publicKey], ipMapping["id"].(uint64))
		ipMappingPrivateMap[privateKey] = append(ipMappingPrivateMap[privateKey], ipMapping["id"].(uint64))
		publicIpList = append(publicIpList, cast.ToString(ipMapping["public_ip"]))
		privateIpList = append(privateIpList, cast.ToString(ipMapping["private_ip"]))
	}
	publicIpList = utils.ListDistinct(publicIpList)
	privateIpList = utils.ListDistinct(privateIpList)

	// 标记公网 IP 的报警数据
	publicAlarmIds, publicDomainMap, err := markAlarmData(publicIpList, ipMappingPublicMap)
	if err != nil {
		logger.Errorf("标记公网 IP 报警数据失败,err:%v", err)
	}
	privateAlarmIds, privateDomainMap, err := markAlarmData(privateIpList, ipMappingPrivateMap)
	if err != nil {
		logger.Errorf("标记内网 IP 报警数据失败,err:%v", err)
	}

	// 所有映射关系数据，包括报警标记的
	allAlarmData := make([]map[string]interface{}, 0)
	// 遍历传入的映射关系数据，标记报警数据
	for _, ipMapping := range ipMappingList {
		id := cast.ToUint64(ipMapping["id"])
		alarmData := ipMapping
		delete(alarmData, "id")
		alarmData["batch_no"] = batchNo
		alarmData["private_exist_alarm"] = false
		alarmData["public_exist_alarm"] = false
		// 标记公网 IP 的报警数据
		if _, ok := publicAlarmIds[id]; ok {
			alarmData["public_exist_alarm"] = true
		} else {
			alarmData["public_domain"] = publicDomainMap[fmt.Sprintf("%s:%s", ipMapping["public_ip"], ipMapping["public_port"])]
		}
		// 标记内网 IP 的报警数据
		if _, ok := privateAlarmIds[id]; ok {
			alarmData["private_exist_alarm"] = true
		} else {
			alarmData["private_domain"] = privateDomainMap[fmt.Sprintf("%s:%s", ipMapping["private_ip"], ipMapping["private_port"])]
		}
		allAlarmData = append(allAlarmData, alarmData)
	}
	if len(allAlarmData) == 0 {
		logs.GetLogger().Infof("没有报警数据,batchNo:%s", batchNo)
		return
	}
	// 保存报警数据
	err = ip_mapping.NewIpMappingStatisticsModel().CreateBatch(context.Background(), allAlarmData)
	if err != nil {
		logger.Errorf("保存报警数据失败,err:%v", err)
	}
}

// 标记报警数据，根据传入的 ipList 查询资产，如果不存在资产，则对 ipMappingMap 中的数据标记报警数据
// 返回需要报警数据的ID，以及资产的域名
func markAlarmData(ipList []string, ipMappingMap map[string][]uint64) (map[uint64]struct{}, map[string]string, error) {
	if len(ipList) == 0 {
		return nil, nil, nil
	}
	// 按照ip查询资产，内存中判断端口
	assets, err := es.All[esmodel.Assets](100, elastic.NewTermsQueryFromStrings("ip", ipList...), nil, "id", "ip", "ports.port", "ports.domain")
	if err != nil {
		return nil, nil, err
	}
	alarmData := make(map[uint64]struct{})
	// key=ip:port, value=domain
	domainMap := make(map[string]string)
	// 遍历映射关系，判断是否存在资产
	for ipPort, ipMappingIds := range ipMappingMap {
		ip := strings.Split(ipPort, ":")[0]
		portStr := strings.Split(ipPort, ":")[1]
		port := cast.ToInt(portStr)
		// 判断是否存在资产
		existAsset, domain := isExistAlarmData(ip, port, assets)
		if !existAsset {
			// 不存在资产
			for _, id := range ipMappingIds {
				alarmData[id] = struct{}{}
			}
		} else {
			// 存在资产
			domainMap[ipPort] = domain
		}
	}
	return alarmData, domainMap, nil
}

// isExistAlarmData 判断资产是否存在
// 返回是否存在资产，以及资产的域名
func isExistAlarmData(ip string, port int, assets []*esmodel.Assets) (bool, string) {
	// 存在标识
	existAsset := false
	domain := ""
	// 遍历已有资产
	for _, asset := range assets {
		if asset.Ip == ip {
			// 遍历资产端口，判断是否存在
			for _, assetPort := range asset.Ports {
				if assetPort.Port == port {
					existAsset = true
					domain = assetPort.Domain
					break
				}
			}
		}
	}
	return existAsset, domain
}

// ExportAlarmList 导出报警信息
func ExportAlarmList(ctx context.Context, ids []uint64, keyword string, params map[string]any) (string, error) {
	exportData := make([]map[string]interface{}, 0)
	// 获取最后批次数据
	audit, err := ip_mapping.NewIpMappingStatisticsAuditModel().Last(ctx)
	if err != nil {
		return "", err
	}
	if audit == nil {
		//return "", fmt.Errorf("没有报警信息")
		audit = &ip_mapping.IpMappingStatisticsAudit{
			BatchNo: "",
		}
	}
	// 获取数据
	if len(ids) == 0 && keyword == "" {
		// 获取所有数据
		page := 1
		perPage := 1000
		handleCount := int64(0)
		for {
			count, data, err := ip_mapping.NewIpMappingStatisticsModel().List(ctx, page, perPage, keyword, audit.BatchNo, params)
			if err != nil {
				logger.Errorf("获取内外网映射关系失败,page:%d,perPage:%d,err:%v", page, perPage, err)
				break
			}
			if count == 0 {
				logger.Infof("获取内外网映射关系为空,page:%d,perPage:%d", page, perPage)
				break
			}
			handleCount += int64(len(data))
			page++

			exportData = append(exportData, data...)

			if handleCount >= count {
				break
			}
			if len(data) < perPage {
				break // 如果获取的数据少于每页的数量，说明已经是最后一页
			}
		}
	} else if len(ids) > 0 {
		// 根据ID获取数据
		exportData, err = ip_mapping.NewIpMappingStatisticsModel().Query(mysql.WithValuesIn("id", ids))
		if err != nil {
			return "", err
		}
	} else if keyword != "" {
		// 根据关键词获取数据
		exportData, err = ip_mapping.NewIpMappingStatisticsModel().Query(mysql.WithWhere("public_ip = ? OR private_ip = ? OR public_domain like ? OR private_domain like ?", keyword, keyword, "%"+keyword+"%", "%"+keyword+"%"))
		if err != nil {
			return "", err
		}
	}
	data := make([][]interface{}, 0)
	for _, item := range exportData {
		data = append(data, []interface{}{
			cast.ToString(item["data_source"]),
			cast.ToString(item["public_ip"]),
			cast.ToString(item["public_port"]),
			cast.ToString(item["public_domain"]),
			func() string {
				if cast.ToBool(item["public_exist_alarm"]) {
					return "是"
				}
				return "否"
			}(),
			cast.ToString(item["private_ip"]),
			cast.ToString(item["private_port"]),
			cast.ToString(item["private_domain"]),
			func() string {
				if cast.ToBool(item["private_exist_alarm"]) {
					return "是"
				}
				return "否"
			}(),
			item["updated_at"],
		})
	}
	filePath := createExcel(data)
	// 导出数据
	return filePath, nil
}

// CreateTemplateFile 生成自定义映射模板文件
func CreateTemplateFile(fromMapping string, toMapping string) string {
	filePath := time.Now().Format("20060102150405") + "内外网映射关系模板.xlsx"
	utils.WriterExcel(filePath, []string{
		fromMapping + "IP",
		fromMapping + "端口",
		fromMapping + "域名",
		fromMapping + "协议",
		toMapping + "IP",
		toMapping + "端口",
		toMapping + "域名",
		toMapping + "协议",
	}, [][]interface{}{})
	return filePath
}

// GetFieldName 获取需要的字段类型
func GetFieldName(mapping string) (map[string]string, bool) {
	ip := mapping + "IP"
	port := mapping + "端口"
	domain := mapping + "域名"
	protocol := mapping + "协议"

	fields := make([]string, 0)
	for _, name := range []string{ip, port, domain, protocol} {
		customField, err := custom_field.NewCustomFieldModel().First(
			mysql.WithWhere("`display_name` = ?", name),
			mysql.WithWhere("`table` = ?", ip_mapping.NewIpMappingModel().TableName()))
		if err != nil || customField.FieldName == "" {
			return nil, false
		}
		fields = append(fields, customField.FieldName)
	}

	fieldsMap := make(map[string]string)
	fieldsMap["ip"] = fields[0]
	fieldsMap["port"] = fields[1]
	fieldsMap["domain"] = fields[2]
	fieldsMap["protocol"] = fields[3]

	return fieldsMap, true
}

// FindMappingByIpPort 根据IP端口查询记录
func FindMappingByIpPort(ip, port string, IpPortMapList []map[string]string) ([]map[string]interface{}, error) {
	handleFuncs := make([]mysql.HandleFunc, 0)
	for _, IpPortMap := range IpPortMapList {
		handleFuncs = append(handleFuncs,
			mysql.WithOrWhere(fmt.Sprintf("`%s` = ? AND `%s` = ?", IpPortMap["ip"], IpPortMap["port"]), ip, port),
		)
	}
	return ip_mapping.NewIpMappingModel().ListByOpt(handleFuncs...)
}

func FindMappingByDomain(domain string, domainList []string) ([]map[string]interface{}, error) {
	handleFuncs := make([]mysql.HandleFunc, 0)
	for _, domainField := range domainList {
		handleFuncs = append(handleFuncs,
			mysql.WithOrWhere(fmt.Sprintf("`%s` = ?", domainField), domain),
		)
	}
	return ip_mapping.NewIpMappingModel().ListByOpt(handleFuncs...)
}

// 生成 Excel 文件
func createExcel(datum [][]interface{}) string {
	filePath := time.Now().Format("20060102150405") + "内外网映射关系列表.xlsx"
	utils.WriterExcel(filePath,
		[]string{
			"数据源",
			"互联网IP",
			"互联网端口",
			"互联网域名",
			"互联网报警",
			"内网IP",
			"内网端口",
			"内网域名",
			"内网报警",
			"更新时间",
		},
		datum,
	)
	return filePath
}

// 检查数据格式
func checkIp(ips ...string) bool {
	for _, ip := range ips {
		if !utils.IsValidIP(ip) {
			return false
		}
	}
	return true
}
func checkPort(ports ...string) bool {
	for _, port := range ports {
		if !utils.IsValidatePort(port) {
			return false
		}
	}
	return true
}
