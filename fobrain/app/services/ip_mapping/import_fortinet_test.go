package ip_mapping

import (
	"github.com/agiledragon/gomonkey/v2"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"regexp"
	"sync"
	"testing"
)

func TestImportIpMappingByFortinet(t *testing.T) {
	patches := []*gomonkey.Patches{
		gomonkey.ApplyFuncReturn(parseFortinetConfig, map[string]interface{}{
			"policies": []Policy{
				{
					SrcAddr:  "test",
					PoolName: "**************",
				},
			},
			"addresses": map[string]string{
				"test": "************",
			},
		}, nil),
		gomonkey.ApplyFunc(saveData, func(ch chan map[string]interface{}, wg *sync.WaitGroup, successCount *int64) {
			defer wg.Done()
			count := 0
			for range ch {
				count++
			}
			*successCount = int64(count)
		}),
		gomonkey.ApplyFuncReturn(GetFieldName, map[string]string{"ip": "ip", "port": "port", "domain": "domain", "protocol": "protocol"}, true),
	}
	successCount, msg, err := ImportIpMappingByFortinet(uuid.New().String(), "华为防火墙配置文件", "", "", "")
	for _, patch := range patches {
		patch.Reset()
	}
	assert.Equal(t, successCount, int64(1))
	assert.Equal(t, msg, "导入成功，成功解析1条数据，新增1条数据")
	assert.Nil(t, err)
}

func TestImportIpMappingByFortinetConfirm(t *testing.T) {
	patches := []*gomonkey.Patches{
		gomonkey.ApplyFuncReturn(parseFortinetConfig, map[string]interface{}{
			"policies": []Policy{
				{
					SrcAddr:  "test",
					PoolName: "**************",
				},
			},
			"addresses": map[string]string{
				"test": "************",
			},
		}, nil),
	}
	fromMap := map[string]string{"ip": "fromIp", "port": "fromPort", "domain": "fromDomain", "protocol": "fromProtocol"}
	toMap := map[string]string{"ip": "toIp", "port": "toPort", "domain": "toDomain", "protocol": "toProtocol"}
	ipMapping, err := ImportIpMappingByFortinetConfirm("", fromMap, toMap)
	for _, patch := range patches {
		patch.Reset()
	}
	assert.Equal(t, ipMapping[0]["fromIp"], "************")
	assert.Nil(t, err)
}

func TestParseFortinetConfig(t *testing.T) {
	patches := []*gomonkey.Patches{
		gomonkey.ApplyMethodReturn(&regexp.Regexp{}, "FindAllStringSubmatch", [][]string{
			{"test", "test"},
		}),
		gomonkey.ApplyMethodReturn(&regexp.Regexp{}, "FindAllString", []string{"test"}),
		gomonkey.ApplyMethodReturn(&regexp.Regexp{}, "MatchString", true),
	}
	config := ""
	_, err := parseFortinetConfig(config)
	for _, patch := range patches {
		patch.Reset()
	}
	assert.Nil(t, err)
}
