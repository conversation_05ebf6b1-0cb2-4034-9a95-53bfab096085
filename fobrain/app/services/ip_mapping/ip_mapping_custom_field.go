package ip_mapping

import (
	"encoding/json"
	"fmt"
	"fobrain/fobrain/app/response/ip_mapping"
	"fobrain/initialize/mysql"
	"fobrain/models/mysql/column"
	"fobrain/models/mysql/custom_field"
	ip_mapping2 "fobrain/models/mysql/ip_mapping"
	"fobrain/pkg/utils"
	"sort"
	"strings"
	"time"
)

// ListCustomFieldGroups 获取自定义字段列表组
func ListCustomFieldGroups(table string) ([]custom_field.CustomFieldGroup, error) {
	customFieldGroupList, err := custom_field.NewCustomFieldGroupModel().ListByTable(table)
	if err != nil {
		return nil, err
	}
	return customFieldGroupList, nil
}

// ListCustomFields 获取自定义字段列表
func ListCustomFields(table string) (ip_mapping.ListCustomFieldsDetailResponse, error) {
	var listCustomFieldsDetailResponse ip_mapping.ListCustomFieldsDetailResponse
	customFieldGroupList, err := custom_field.NewCustomFieldGroupModel().ListByTable(table)
	if err != nil {
		return listCustomFieldsDetailResponse, err
	}

	for _, customFieldGroup := range customFieldGroupList {
		fieldsNameList := make([]string, 0)
		err = json.Unmarshal([]byte(customFieldGroup.Fields), &fieldsNameList)

		fieldMap := make(map[string]interface{})

		for _, fieldName := range fieldsNameList {
			customField, err := custom_field.NewCustomFieldModel().First(mysql.WithWhere("`field_name` = ?", fieldName))
			if err != nil {
				return listCustomFieldsDetailResponse, err
			}

			fieldMap[customField.DisplayName] = customField.FieldName
		}

		listCustomFieldsDetailResponse.Fields = append(listCustomFieldsDetailResponse.Fields, fieldMap)
	}

	return listCustomFieldsDetailResponse, nil
}

// ListCustomFieldCombination 获取自定义列组合
func ListCustomFieldCombination(table string) (ip_mapping.ListCustomFieldCombinationResponse, error) {
	var listCustomFieldCombinationResponse ip_mapping.ListCustomFieldCombinationResponse
	listCustomFieldCombination, err := custom_field.NewCustomFieldGroupModel().ListByTable(table)
	if err != nil {
		return listCustomFieldCombinationResponse, err
	}
	sortCustomFieldCombination := custom_field.CustomFieldGroupSort(listCustomFieldCombination)
	sort.Sort(sortCustomFieldCombination)

	i := 0
	step := 1
	for step < sortCustomFieldCombination.Len() {
		for i+step < sortCustomFieldCombination.Len() {
			listCustomFieldCombinationResponse.FieldCombinations = append(listCustomFieldCombinationResponse.FieldCombinations, []string{
				sortCustomFieldCombination[i].Name,
				sortCustomFieldCombination[i+step].Name,
			})
			i++
		}
		i = 0
		step++
	}
	return listCustomFieldCombinationResponse, nil
}

// CreateCustomFieldGroup 创建自定义列
func CreateCustomFieldGroup(name string, sort int) error {
	ipName := name + "IP"
	portName := name + "端口"
	domainName := name + "域名"
	protocolName := name + "协议"

	displayNameList := []string{
		ipName,
		portName,
		domainName,
		protocolName,
	}

	fieldNameList := make([]string, 0)
	customFieldList := make([]*custom_field.CustomField, 0)
	// 创建映射自定义字段
	for _, displayName := range displayNameList {
		customField := &custom_field.CustomField{
			DisplayName:   displayName,
			FieldName:     generateTimestampedString("custom_field"),
			FieldType:     "string",
			Table:         "ip_mapping",
			Sort:          2,
			IsSystemField: false,
		}

		err := custom_field.NewCustomFieldModel().Create(customField)
		if err != nil {
			return err
		}

		if displayName == domainName {
			err = column.CreateOrUpdateColumn(customField.Table, customField.FieldName, "VARCHAR(255) DEFAULT ''")
		} else {
			err = column.CreateOrUpdateColumn(customField.Table, customField.FieldName, "VARCHAR(16) DEFAULT ''")
		}

		if err != nil {
			return err
		}

		fieldNameList = append(fieldNameList, customField.FieldName)
		customFieldList = append(customFieldList, customField)
	}

	fields, err := json.Marshal(fieldNameList)

	if err != nil {
		return err
	}

	customFieldGroup := &custom_field.CustomFieldGroup{
		Name:   name,
		Table:  "ip_mapping",
		Fields: string(fields),
		Sort:   2,
	}

	err = custom_field.NewCustomFieldGroupModel().Create(customFieldGroup)
	if err != nil {
		return err
	}

	// 创建统计自定义字段
	// 统计自定义字段的列名与映射自定义字段相同
	statisticFieldNameList := make([]string, 0)
	for _, customField := range customFieldList {
		statisticCustomField := &custom_field.CustomField{
			DisplayName:   customField.DisplayName,
			FieldName:     customField.FieldName,
			FieldType:     "string",
			Table:         "ip_mapping_statistics",
			Sort:          2,
			IsSystemField: false,
		}

		err := custom_field.NewCustomFieldModel().Create(statisticCustomField)
		if err != nil {
			return err
		}

		if customField.DisplayName == domainName {
			err = column.CreateOrUpdateColumn("ip_mapping_statistics", customField.FieldName, "VARCHAR(255) DEFAULT ''")
		} else {
			err = column.CreateOrUpdateColumn("ip_mapping_statistics", customField.FieldName, "VARCHAR(16) DEFAULT ''")
		}

		if err != nil {
			return err
		}

		statisticFieldNameList = append(statisticFieldNameList, customField.FieldName)
	}

	statisticFields, err := json.Marshal(statisticFieldNameList)

	if err != nil {
		return err
	}

	statisticCustomFieldGroup := &custom_field.CustomFieldGroup{
		Name:   name,
		Table:  "ip_mapping_statistics",
		Fields: string(statisticFields),
		Sort:   2,
	}

	err = custom_field.NewCustomFieldGroupModel().Create(statisticCustomFieldGroup)
	if err != nil {
		return err
	}

	return nil
}

// UpdateCustomFieldGroup 更新自定义列
func UpdateCustomFieldGroup(id uint64, name string, sort int) error {
	customFieldGroup, err := custom_field.NewCustomFieldGroupModel().First(
		mysql.WithWhere("`id` = ?", id),
		mysql.WithWhere("`table` = ?", "ip_mapping"),
	)

	if err != nil {
		return err
	}

	statisticCustomFieldGroup, err := custom_field.NewCustomFieldGroupModel().First(
		mysql.WithWhere("`name` = ?", customFieldGroup.Name),
		mysql.WithWhere("`table` = ?", "ip_mapping_statistics"),
	)
	if err != nil {
		return err
	}

	for _, group := range []*custom_field.CustomFieldGroup{customFieldGroup, statisticCustomFieldGroup} {
		fieldsNameList := make([]string, 0)
		err = json.Unmarshal([]byte(group.Fields), &fieldsNameList)
		if err != nil {
			return err
		}

		for _, fieldName := range fieldsNameList {
			customField, err := custom_field.NewCustomFieldModel().First(
				mysql.WithWhere("`field_name` = ?", fieldName),
				mysql.WithWhere("`table` = ?", group.Table),
			)
			if err != nil {
				return err
			}

			customField.DisplayName = strings.Replace(customField.DisplayName, group.Name, name, 1)
			err = custom_field.NewCustomFieldModel().Update(customField)
			if err != nil {
				return err
			}
		}

		group.Name = name
		err = custom_field.NewCustomFieldGroupModel().Update(group)
		if err != nil {
			return err
		}
	}
	return nil
}

// DeleteCustomFieldGroup 删除自定义列
func DeleteCustomFieldGroup(id uint64) error {
	customFieldGroup, err := custom_field.NewCustomFieldGroupModel().First(
		mysql.WithWhere("`id` = ?", id),
		mysql.WithWhere("`table` = ?", "ip_mapping"),
	)
	if err != nil {
		return err
	}
	statisticCustomFieldGroup, err := custom_field.NewCustomFieldGroupModel().First(
		mysql.WithWhere("`name` = ?", customFieldGroup.Name),
		mysql.WithWhere("`table` = ?", "ip_mapping_statistics"),
	)
	if err != nil {
		return err
	}

	for _, group := range []*custom_field.CustomFieldGroup{customFieldGroup, statisticCustomFieldGroup} {
		fieldsNameList := make([]string, 0)
		err = json.Unmarshal([]byte(group.Fields), &fieldsNameList)
		if err != nil {
			return err
		}
		for _, fieldName := range fieldsNameList {
			err = custom_field.NewCustomFieldModel().Delete(
				mysql.WithWhere("`field_name` = ?", fieldName),
			)
			if err != nil {
				return err
			}
		}

		err = custom_field.NewCustomFieldGroupModel().Delete(group.Id)
		if err != nil {
			return err
		}
	}

	return nil
}

// GenerateTimestampedString 按时间戳生成列名
func generateTimestampedString(prefix string) string {
	timestamp := time.Now().Unix()
	return fmt.Sprintf("%s_%d_", prefix, timestamp) + utils.RandString(6)
}

func GetFieldToNameMap() (map[string]string, error) {
	fieldToNameMap := make(map[string]string)
	customFields, err := custom_field.NewCustomFieldModel().
		ListByTable(ip_mapping2.NewIpMappingModel().TableName())
	if err != nil {
		return nil, err
	}
	for _, customField := range customFields {
		fieldToNameMap[customField.FieldName] = customField.DisplayName
	}
	return fieldToNameMap, nil
}

func GetIpPortFieldMap() ([]map[string]string, error) {
	var fieldMapList = make([]map[string]string, 0)
	customFieldGroupList, err := custom_field.NewCustomFieldGroupModel().ListByTable(ip_mapping2.NewIpMappingModel().TableName())
	if err != nil {
		return nil, err
	}
	for _, customFieldGroup := range customFieldGroupList {
		fieldsNameList := make([]string, 0)
		err = json.Unmarshal([]byte(customFieldGroup.Fields), &fieldsNameList)
		fieldMap := make(map[string]string)
		for _, fieldName := range fieldsNameList {
			customField, err := custom_field.NewCustomFieldModel().First(
				mysql.WithWhere("`field_name` = ?", fieldName),
				mysql.WithWhere("`table` = ?", ip_mapping2.NewIpMappingModel().TableName()),
			)
			if err != nil {
				return nil, err
			}
			runes := []rune(customField.DisplayName)
			fType := string(runes[len(runes)-2:])
			if fType == "IP" {
				fieldMap["ip"] = customField.FieldName
			}
			if fType == "端口" {
				fieldMap["port"] = customField.FieldName
			}
		}
		fieldMapList = append(fieldMapList, fieldMap)
	}
	return fieldMapList, nil
}

func GetDomainFieldList() ([]string, error) {
	var domainList = make([]string, 0)
	customFields, err := custom_field.NewCustomFieldModel().
		ListByTable(ip_mapping2.NewIpMappingModel().TableName())
	if err != nil {
		return nil, err
	}
	for _, customField := range customFields {
		runes := []rune(customField.DisplayName)
		fType := string(runes[len(runes)-2:])
		if fType == "域名" {
			domainList = append(domainList, customField.FieldName)
		}

	}
	return domainList, nil
}
