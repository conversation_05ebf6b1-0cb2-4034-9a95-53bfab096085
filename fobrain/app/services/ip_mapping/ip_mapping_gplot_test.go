package ip_mapping

import (
	assetses "fobrain/models/elastic/assets"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestMappingGplot_Include(t *testing.T) {
	m1 := MappingRelation{
		SourceIP:   "127.0.0.1",
		SourcePort: "80",
		TargetIP:   "*********",
		TargetPort: "08",
	}
	m2 := MappingRelation{
		SourceIP:   "127.0.0.1",
		SourcePort: "08",
		TargetIP:   "*********",
		TargetPort: "80",
	}
	g := MappingGplot([]MappingRelation{m1})

	ans1 := g.Include(m1)
	assert.Equal(t, ans1, true)
	ans2 := g.Include(m2)
	assert.Equal(t, ans2, false)
}

func TestMappingGplot_Add(t *testing.T) {
	m1 := MappingRelation{
		SourceIP:   "127.0.0.1",
		SourcePort: "80",
		TargetIP:   "*********",
		TargetPort: "08",
	}
	m2 := MappingRelation{
		SourceIP:   "127.0.0.1",
		SourcePort: "08",
		TargetIP:   "*********",
		TargetPort: "80",
	}
	g := MappingGplot([]MappingRelation{m1})

	ans1 := g.Add(m1)
	assert.Equal(t, ans1, false)
	ans2 := g.Add(m2)
	assert.Equal(t, ans2, true)
}

func TestParseMappingRelation(t *testing.T) {
	mapping := map[string]interface{}{
		"public_ip":        "127.0.0.1",
		"public_port":      "80",
		"public_domain":    "",
		"public_protocol":  "",
		"private_ip":       "*********",
		"private_port":     "08",
		"private_domain":   "",
		"private_protocol": "",
	}
	fieldToNameMap := map[string]string{
		"public_ip":        "公网IP",
		"public_port":      "公网端口",
		"public_domain":    "公网域名",
		"public_protocol":  "公网协议",
		"private_ip":       "内网IP",
		"private_port":     "内网端口",
		"private_domain":   "内网域名",
		"private_protocol": "内网协议",
	}
	mappingRelation := ParseMappingRelation(mapping, fieldToNameMap)
	assert.Contains(t, []string{"127.0.0.1", "*********"}, mappingRelation.TargetIP)
}

func TestGetStaffDepartmentByName(t *testing.T) {
	userId := "1"
	name := "lqk"
	departmentList := []*assetses.DepartmentBase{
		{UserId: "1", UserName: "lqk"},
		nil,
	}
	departmentResult1 := getStaffDepartmentByName("2", name, departmentList)
	departmentResult2 := getStaffDepartmentByName(userId, "haha", departmentList)
	assert.Equal(t, len(append(departmentResult2, departmentResult1...)), 2)
}
