package ip_mapping

import (
	"context"
	"errors"
	"fmt"
	ip_mapping2 "fobrain/fobrain/app/request/ip_mapping"
	ip_mapping3 "fobrain/fobrain/app/response/ip_mapping"
	"fobrain/initialize/es"
	pb "fobrain/mergeService/proto"
	"fobrain/models/elastic/assets"
	"fobrain/models/elastic/business_system"
	"fobrain/models/elastic/poc"
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/ip_mapping"
	"fobrain/models/mysql/network_areas"
	"fobrain/pkg/utils"
	"strings"

	"github.com/olivere/elastic/v7"
	"github.com/spf13/cast"
	"go-micro.dev/v4/logger"
)

type MappingRelation struct {
	SourceName     string
	SourceIP       string
	SourcePort     string
	SourceDomain   string
	SourceProtocol string
	TargetName     string
	TargetIP       string
	TargetPort     string
	TargetDomain   string
	TargetProtocol string
}

type MappingGplot []MappingRelation

func (m *MappingGplot) Include(mapping MappingRelation) bool {
	for i, _ := range *m {
		if mapping.SourceIP == (*m)[i].SourceIP && mapping.SourcePort == (*m)[i].SourcePort &&
			mapping.TargetIP == (*m)[i].TargetIP && mapping.TargetPort == (*m)[i].TargetPort {
			return true
		}
		if mapping.SourceIP == (*m)[i].TargetIP && mapping.SourcePort == (*m)[i].TargetPort &&
			mapping.TargetIP == (*m)[i].SourceIP && mapping.TargetPort == (*m)[i].SourcePort {
			return true
		}
	}
	return false
}

func (m *MappingGplot) Add(mapping MappingRelation) bool {
	if m.Include(mapping) {
		return false
	}
	*m = append(*m, mapping)
	return true
}

func ParseMappingRelation(mapping map[string]interface{}, fieldToNameMap map[string]string) *MappingRelation {
	mappingRelation := &MappingRelation{}
	names := make(map[string]map[string]string)
	keys := make([]string, 0)
	for key, value := range mapping {
		fieldName := fieldToNameMap[key]
		if fieldName == "" || len(fieldName) < 3 {
			continue
		}
		runes := []rune(fieldName)
		// fName + fType = fieldName (公网域名)
		fName := string(runes[:len(runes)-2])
		fType := string(runes[len(runes)-2:])
		if !utils.InArray[string](fType, []string{"IP", "域名", "端口", "协议"}) {
			continue
		}
		if _, ok := names[fName]; ok {
			names[fName][fType] = cast.ToString(value)
		} else {
			names[fName] = map[string]string{fType: cast.ToString(value)}
		}
	}
	for key, _ := range names {
		if len(names[key]["IP"]) > 0 {
			keys = append(keys, key)
		}
	}
	if len(keys) < 2 {
		return nil
	}
	nameMap1 := names[keys[0]]
	mappingRelation.SourceName = keys[0]
	mappingRelation.SourceIP = cast.ToString(nameMap1["IP"])
	mappingRelation.SourcePort = cast.ToString(nameMap1["端口"])
	mappingRelation.SourceDomain = cast.ToString(nameMap1["域名"])
	mappingRelation.SourceProtocol = cast.ToString(nameMap1["协议"])
	nameMap2 := names[keys[1]]
	mappingRelation.TargetName = keys[1]
	mappingRelation.TargetIP = cast.ToString(nameMap2["IP"])
	mappingRelation.TargetPort = cast.ToString(nameMap2["端口"])
	mappingRelation.TargetDomain = cast.ToString(nameMap2["域名"])
	mappingRelation.TargetProtocol = cast.ToString(nameMap2["协议"])
	return mappingRelation
}

func BuildMappingGplotByIpPort(keyword string, size int, searchType string) (MappingGplot, []map[string]string, error) {
	ipPortFieldMap, err := GetIpPortFieldMap()
	if err != nil {
		return nil, nil, err
	}
	fieldToNameMap, err := GetFieldToNameMap()
	if err != nil {
		return nil, nil, err
	}
	domainFieldList, err := GetDomainFieldList()
	if err != nil {
		return nil, nil, err
	}
	var rootMappings []map[string]interface{}
	parts := strings.Split(keyword, ":")
	if len(parts) == 2 && searchType == "ip_port" {
		if !utils.IsValidIP(parts[0]) && !utils.IsValidatePort(parts[1]) {
			return nil, nil, errors.New("参数错误，请输入正确的IP:端口")
		}
		rootMappings, err = FindMappingByIpPort(parts[0], parts[1], ipPortFieldMap)
	} else if utils.IsValidIP(keyword) && searchType == "ip" {
		rootMappings, err = ip_mapping.NewIpMappingModel().ListAll(keyword)
	} else if searchType == "domain" {
		rootMappings, err = FindMappingByDomain(keyword, domainFieldList)
	} else {
		errMsg := ""
		if searchType == "ip" {
			errMsg = "参数错误，请输入正确的IP"
		} else if searchType == "ip_port" {
			errMsg = "参数错误，请输入正确的IP:端口"
		} else {
			errMsg = "参数错误"
		}
		return nil, nil, errors.New(errMsg)
	}
	if len(rootMappings) > size && size > 0 {
		rootMappings = rootMappings[:size]
	}
	if err != nil {
		return nil, nil, err
	}
	// 初始化图
	mappingGplot := MappingGplot{}
	// 遍历过的数据和遍历中的数据
	lookedMappingList := make([][2]string, 0)
	lookingMappingList := make([][2]string, 0)
	// 前端构图所需的list数据
	var list = make([]map[string]string, 0)
	var savedIp = make([]string, 0)
	for _, mapping := range rootMappings {
		mappingRelation := ParseMappingRelation(mapping, fieldToNameMap)
		if mappingRelation == nil {
			return nil, nil, errors.New("查询失败")
		}
		lookingMappingList = append(lookingMappingList, [2]string{mappingRelation.SourceIP, mappingRelation.SourcePort})
		lookingMappingList = append(lookingMappingList, [2]string{mappingRelation.TargetIP, mappingRelation.TargetPort})
	}

	for len(lookingMappingList) > 0 {
		lookingMapping := lookingMappingList[0]
		lookingMappingList = lookingMappingList[1:]

		if utils.InArray[[2]string](lookingMapping, lookedMappingList) {
			continue
		}

		lookedMappingList = append(lookedMappingList, lookingMapping)
		ipMappingList, err := FindMappingByIpPort(lookingMapping[0], lookingMapping[1], ipPortFieldMap)
		if lookingMapping[1] == "" {
			ipMappingList, err = ip_mapping.NewIpMappingModel().ListAll(lookingMapping[0])
		}
		if err != nil {
			return nil, nil, err
		}
		for _, ipMapping := range ipMappingList {
			mappingRelation := ParseMappingRelation(ipMapping, fieldToNameMap)
			if mappingRelation == nil {
				continue
			}
			if !mappingGplot.Include(*mappingRelation) {
				mappingGplot = append(mappingGplot, *mappingRelation)

				sourceIpKey := mappingRelation.SourceIP
				if searchType == "ip_port" {
					if mappingRelation.SourcePort != "" {
						sourceIpKey = sourceIpKey + ":" + mappingRelation.SourcePort
					}
				}
				if !utils.InArray[string](sourceIpKey, savedIp) {
					sourceMap := map[string]string{
						"name":    sourceIpKey,
						"ip_kind": mappingRelation.SourceName,
					}
					savedIp = append(savedIp, sourceIpKey)
					list = append(list, sourceMap)
				}

				targetIpKey := mappingRelation.TargetIP
				if searchType == "ip_port" {
					if mappingRelation.TargetPort != "" {
						targetIpKey = targetIpKey + ":" + mappingRelation.TargetPort
					}
				}
				if !utils.InArray[string](targetIpKey, savedIp) {
					targetMap := map[string]string{
						"name":    targetIpKey,
						"ip_kind": mappingRelation.TargetName,
					}
					savedIp = append(savedIp, targetIpKey)
					list = append(list, targetMap)
				}

				mapping := [2]string{mappingRelation.SourceIP, mappingRelation.SourcePort}
				if !utils.InArray[[2]string](mapping, lookingMappingList) && !utils.InArray[[2]string](mapping, lookedMappingList) {
					lookingMappingList = append(lookingMappingList, mapping)
				}
				mapping = [2]string{mappingRelation.TargetIP, mappingRelation.TargetPort}
				if !utils.InArray[[2]string](mapping, lookingMappingList) && !utils.InArray[[2]string](mapping, lookedMappingList) {
					lookingMappingList = append(lookingMappingList, mapping)
				}
			}
		}
	}
	return mappingGplot, list, nil
}

func GetMappingGplotAssets(keyword string, page, perPage int) (any, int64, error) {
	parts := strings.Split(keyword, ":")
	var gplot MappingGplot
	var err error
	if len(parts) == 2 {
		if !utils.IsValidIP(parts[0]) && !utils.IsValidatePort(parts[1]) {
			return nil, 0, errors.New("参数错误，请输入IP:端口")
		}
		gplot, _, err = BuildMappingGplotByIpPort(keyword, 0, "ip_port")
	} else if utils.IsValidIP(keyword) {
		gplot, _, err = BuildMappingGplotByIpPort(keyword, 0, "ip")
	} else {
		return nil, 0, errors.New("参数错误，请输入正确IP")
	}
	if err != nil {
		return nil, 0, err
	}
	if len(gplot) == 0 {
		return nil, 0, nil
	}
	assetQuery := elastic.NewBoolQuery()
	querys := make([]elastic.Query, 0)
	for _, relation := range gplot {
		q := elastic.NewBoolQuery()
		q.Must(elastic.NewTermsQuery("ip", relation.SourceIP))
		if relation.SourcePort != "" {
			q.Must(elastic.NewTermsQuery("ports.port", relation.SourcePort))
		}
		p := elastic.NewBoolQuery()
		p.Must(elastic.NewTermsQuery("ip", relation.TargetIP))
		if relation.TargetPort != "" {
			p.Must(elastic.NewTermsQuery("ports.port", relation.TargetPort))
		}
		querys = append(querys, q)
		querys = append(querys, p)
	}
	assetQuery.Should(querys...).MinimumShouldMatch("1")
	searchResult, err := es.GetEsClient().Search().Index(assets.NewAssets().IndexName()).
		From(es.GetFrom(page, perPage)).Size(es.GetSize(perPage)).
		Query(assetQuery).TrackTotalHits(true).Do(context.Background())
	if err != nil {
		return nil, 0, err
	}
	assetList := make([]*assets.Assets, 0)
	for _, hit := range searchResult.Hits.Hits {
		item, err := assets.ConvertToAssetsModel(hit)
		if err != nil {
			continue
		}
		assetList = append(assetList, item)
	}
	res, err := parseAssets(searchResult.Hits.Hits)
	if err != nil {
		return nil, 0, err
	}
	return res, searchResult.TotalHits(), nil
}

func GetMappingGplotAssetBaseInfo(keyword string) (*assets.Assets, error) {
	assetQuery := elastic.NewBoolQuery()
	parts := strings.Split(keyword, ":")
	if len(parts) == 2 {
		assetQuery = assetQuery.Must(elastic.NewTermQuery("ip", parts[0])).Must(elastic.NewTermQuery("ports.port", parts[1]))
	} else if utils.IsValidIP(keyword) {
		assetQuery = assetQuery.Must(elastic.NewTermQuery("ip", keyword))
	} else {
		return nil, nil
	}

	searchResult, err := es.GetEsClient().Search().Index(assets.NewAssets().IndexName()).
		Query(assetQuery).Do(context.Background())
	if err != nil {
		return nil, err
	}
	if searchResult.Hits.TotalHits.Value == 0 {
		return nil, errors.New("无资产数据")
	}
	item, err := assets.ConvertToAssetsModel(searchResult.Hits.Hits[0])
	return item, err
}

func GetMappingGplotResponse(params *ip_mapping2.GetMappingGplotRequest) (map[string]interface{}, error) {
	mappingGplot, ipList, err := BuildMappingGplotByIpPort(params.Keyword, params.Size, params.SearchType)
	if err != nil {
		return nil, err
	}
	noPort := make([]string, 0)
	if params.SearchType == "ip_port" {
		for _, item := range ipList {
			parts := strings.Split(item["name"], ":")
			if len(parts) != 2 {
				ip := item["name"]
				noPort = append(noPort, ip)
			}
		}
		for _, name := range noPort {
			for ind, item := range ipList {
				parts := strings.Split(item["name"], ":")
				if len(parts) == 2 {
					if parts[0] == name {
						ipList = append(ipList[:ind], ipList[ind+1:]...)
					}
				}
			}
		}
	}

	var getMappingGplotResponse = make([]ip_mapping3.GetMappingGplotResponse, 0)
	for _, mappingRelation := range mappingGplot {
		res := ip_mapping3.GetMappingGplotResponse{
			Source:         mappingRelation.SourceIP,
			Target:         mappingRelation.TargetIP,
			SourceName:     mappingRelation.SourceName,
			TargetName:     mappingRelation.TargetName,
			SourcePort:     mappingRelation.SourcePort,
			TargetPort:     mappingRelation.TargetPort,
			SourceDomain:   mappingRelation.SourceDomain,
			TargetDomain:   mappingRelation.TargetDomain,
			SourceProtocol: mappingRelation.SourceProtocol,
			TargetProtocol: mappingRelation.TargetProtocol,
		}
		if params.SearchType == "ip_port" {
			if res.TargetPort != "" {
				if !utils.InArray(res.Target, noPort) {
					res.Target = res.Target + ":" + res.TargetPort
				}
			}
			if res.SourcePort != "" {
				if !utils.InArray(res.Source, noPort) {
					res.Source = res.Source + ":" + res.SourcePort
				}
			}
		}
		getMappingGplotResponse = append(getMappingGplotResponse, res)

	}

	return map[string]interface{}{
		"links": getMappingGplotResponse,
		"data":  ipList,
	}, nil
}

func GetMappingBaseInfoResponse(params *ip_mapping2.GetMappingBaseInfoRequest) *ip_mapping3.GetMappingBaseInfoResponse {
	asset, err := GetMappingGplotAssetBaseInfo(params.Keyword)
	if err != nil {
		return nil
	}
	networkArea := network_areas.NetworkAreaName(cast.ToUint64(asset.Area))
	source := data_source.NewSourceModel().SourceNames(asset.SourceIds)
	businessSystem := make([]map[string]interface{}, 0)
	for _, business := range asset.Business {
		if business == nil {
			continue
		}
		businessSystem = append(businessSystem, map[string]interface{}{
			"business_name":       business.System,
			"business_oper":       business.Owner,
			"business_department": business.Department,
		})
	}
	ports := make([]string, 0)
	for _, port := range asset.Ports {
		ports = append(ports, cast.ToString(port.Port))
	}
	oper := make([]any, 0)
	for _, operInfo := range asset.OperInfo {
		oper = append(oper, map[string]interface{}{
			"oper_name":       operInfo.Name,
			"oper_department": asset.OperDepartment,
		})
	}

	return &ip_mapping3.GetMappingBaseInfoResponse{
		Id:     asset.Id,
		Ip:     asset.Ip,
		AreaId: asset.Area,
		Area: map[string]interface{}{
			"id":   asset.Area,
			"name": networkArea,
		},
		SourceId:       asset.SourceIds,
		Source:         source,
		Hostname:       asset.HostName,
		Os:             asset.Os,
		Mac:            asset.Mac,
		Sn:             asset.Sn,
		Product:        asset.Product,
		CreateAt:       asset.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdateAt:       asset.UpdatedAt.Format("2006-01-02 15:04:05"),
		BusinessSystem: businessSystem,
		Oper:           oper,
		Ports:          ports,
	}
}

func parseAssets(hits []*elastic.SearchHit) ([]any, error) {
	assetIpBugNums, err := poc.PocIpNums([]interface{}{})
	if err != nil {
		logger.Errorf("get PocIpNums error: %v", err)
	}

	list := make([]any, 0, len(hits))
	for _, hit := range hits {
		asset := utils.ParseSampleHash(hit, &assets.Assets{})
		data, _, err := ip_mapping.NewIpMappingModel().List(1, 100, fmt.Sprintf("%s", asset["ip"]))
		if err != nil {
			logger.Warnf("get IpMappingStatistics List error: %v", err)
		}

		//关联关系
		asset["ip_mappings"] = func() map[string]*pb.PortMappingList {
			portMappingList := make(map[string]*pb.PortMappingList)

			switch asset["network_type"] {
			case "内网":
				for _, datum := range data {
					if portMappingList[cast.ToString(datum["PublicIp"])] == nil {
						portMappingList[cast.ToString(datum["PublicIp"])] = &pb.PortMappingList{
							Mappings: []*pb.PortMapping{},
						}
					}
					portMappingList[cast.ToString(datum["PublicIp"])] = &pb.PortMappingList{
						Mappings: append(portMappingList[cast.ToString(datum["PublicIp"])].Mappings, &pb.PortMapping{
							PublicIp:    cast.ToString(datum["PublicIp"]),
							PublicPort:  cast.ToUint64(datum["PublicPort"]),
							PrivateIp:   cast.ToString(datum["PrivateIp"]),
							PrivatePort: cast.ToUint64(datum["PrivatePort"]),
						}),
					}
				}
			case "互联网":
				for _, datum := range data {
					if portMappingList[cast.ToString(datum["PrivateIp"])] == nil {
						portMappingList[cast.ToString(datum["PrivateIp"])] = &pb.PortMappingList{
							Mappings: []*pb.PortMapping{},
						}
					}
					portMappingList[datum["PrivateIp"].(string)] = &pb.PortMappingList{
						Mappings: append(portMappingList[datum["PrivateIp"].(string)].Mappings, &pb.PortMapping{
							PublicIp:    cast.ToString(datum["PublicIp"]),
							PublicPort:  cast.ToUint64(datum["PublicPort"]),
							PrivateIp:   cast.ToString(datum["PrivateIp"]),
							PrivatePort: cast.ToUint64(datum["PrivatePort"]),
						}),
					}
				}
			}

			return portMappingList
		}()

		// 运维人员部门
		operDepartment := make([]*pb.OperDepartment, 0)
		operDepartmentList := make([]*assets.DepartmentBase, 0)
		if asset["oper_department"] != nil {
			operDepartmentList = asset["oper_department"].([]*assets.DepartmentBase)
		}
		if len(asset["oper"].([]string)) > 0 {
			for _, oper := range asset["oper"].([]string) {
				operDepartment = append(operDepartment, &pb.OperDepartment{
					Oper:       oper,
					Department: getStaffDepartmentByName("", oper, operDepartmentList),
				})
			}
		}
		asset["oper_department"] = operDepartment

		// 业务系统人员部门
		business := make([]*assets.Business, 0)
		businessDepartmentList := make([]*assets.DepartmentBase, 0)
		if asset["business_department"] != nil {
			businessDepartmentList = asset["business_department"].([]*assets.DepartmentBase)
		}
		if len(asset["business"].([]*assets.Business)) > 0 {
			for _, value := range asset["business"].([]*assets.Business) {
				if value.OwnerId != "" || value.Owner != "" {
					value.Department = getStaffDepartmentByName(value.OwnerId, value.Owner, businessDepartmentList)
				}

				//增加业务系统可信状态展示到内外网资产
				businessInfo, ok := value.BusinessInfo.(map[string]interface{})
				if ok && len(businessInfo["business_name"].(string)) > 0 { //存在业务系统
					businessInfo["reliability"] = 0 //默认为0

					logger.Errorf("parseAssets business_name to es.First err:%+v", businessInfo["business_name"].(string))
					boolQuery := elastic.NewBoolQuery()
					boolQuery.Must(elastic.NewTermQuery("business_name", businessInfo["business_name"].(string)))

					businessSystem, err := es.First[business_system.BusinessSystems](boolQuery, nil, "status")
					if err != nil {
						logger.Errorf("parseAssets Failed to es.First err:%+v", err)
					}

					if businessSystem != nil { //业务系统存在资产业务系统 取其可信状态
						businessInfo["reliability"] = businessSystem.Status
					}

					value.BusinessInfo = businessInfo //回填
				}

				business = append(business, value)
			}
		}
		var bugNum int64 = 0
		if val, exists := assetIpBugNums[asset["ip"].(string)]; exists {
			bugNum = val
		}
		asset["business"] = business
		asset["bug_num"] = bugNum
		list = append(list, asset)
	}
	return list, nil
}

// getStaffDepartmentByName  获取用户部门
func getStaffDepartmentByName(userId string, name string, departmentList []*assets.DepartmentBase) []string {
	departmentResult := make([]string, 0)
	if len(departmentList) > 0 {
		for _, sf := range departmentList {
			if sf == nil {
				continue
			}
			if sf.UserId == userId {
				departmentResult = append(departmentResult, sf.Name)
			} else if sf.UserName == name {
				departmentResult = append(departmentResult, sf.Name)
			}
		}
	}
	return departmentResult
}
