package compliance_center

import (
	"errors"
	"fobrain/fobrain/app/repository/compliance_history"
	"fobrain/fobrain/app/repository/threat"
	"fobrain/fobrain/app/repository/threat_history"
	"fobrain/fobrain/app/services/threat_center"
	"fobrain/models/elastic/assets"
	"fobrain/models/elastic/compliance_monitor"
	"fobrain/models/elastic/poc"
	"fobrain/models/elastic/staff"
	"fobrain/models/mysql/threat_tasks"
	"fobrain/models/mysql/user"
	"fobrain/models/mysql/user_staff"
	"fobrain/pkg/utils"
	"github.com/gin-gonic/gin"
	"github.com/olivere/elastic/v7"
	"github.com/spf13/cast"
	"go-micro.dev/v4/logger"
)

// DistributeMutil 批量派发、转交合规检测问题
func DistributeMutil(user *user.User, staff *staff.Staff, ctx *gin.Context, complianceHistorys *compliance_history.SomeComplianceHistory, complianceObjs []map[string]interface{}, ccStaff *staff.Staff) error {
	for _, complianceObj := range complianceObjs {
		flowStatus := cast.ToInt(complianceObj["flow_status"])
		err := checkDistribute(complianceHistorys.Status, flowStatus)
		if err != nil {
			return err
		}
		// 检查合规检测状态
		ok, msg := threat_history.CheckStatus(flowStatus, complianceHistorys.Status)
		if !ok {
			return errors.New(msg)
		}

		// 构造单个合规检测的OneComplianceHistory对象，用于记录
		id := cast.ToString(complianceObj["id"])
		oneComplianceHistory := &compliance_history.OneComplianceHistory{
			RecordId:      id,
			ToStaffId:     complianceHistorys.ToStaffId,
			ToStaffName:   complianceHistorys.ToStaffName,
			LimitDate:     complianceHistorys.LimitDate,
			SendNotice:    complianceHistorys.SendNotice,
			TimeoutNotice: complianceHistorys.TimeoutNotice,
			Descrition:    complianceHistorys.Descrition,
			ToCc:          complianceHistorys.ToCc,
			Status:        complianceHistorys.Status,
			ExecNow:       complianceHistorys.ExecNow,
			OriginalId:    complianceHistorys.OriginalId,
			OperationType: complianceHistorys.OperationType,
		}
		// 创建操作记录
		content := threat_history.GetOperateContent(user.Account, complianceHistorys.Status, "合规风险", staff.Name)
		err = compliance_history.CreateHistory(ctx, user, oneComplianceHistory, staff, flowStatus, content)
		if err != nil {
			return err
		}
		// 发送站内消息通知
		err = compliance_history.SendWebhookMsg(oneComplianceHistory, staff, user, []map[string]interface{}{complianceObj})
		if err != nil {
			return err
		}
		// 设置修复时间要求,修改状态时需要发送到队列中
		complianceObj["limit_date"] = complianceHistorys.LimitDate
		// 修改状态
		err = compliance_history.UpdateComplianceStatus(oneComplianceHistory, complianceObj, staff, ccStaff)
		if err != nil {
			return err
		}
	}
	// 发送邮件通知
	err := compliance_history.SendNotice(complianceHistorys, staff)
	if err != nil {
		return err
	}
	// 超时通知，添加定时任务
	if complianceHistorys.TimeoutNotice != 0 {
		err := compliance_history.CreateComplianceTask(threat_tasks.TaskTypeComplianceTimeoutNotice, user, complianceHistorys, staff)
		if err != nil {
			return err
		}
	}

	return nil
}

// StatusOperation 批量延时、催促(无状态修改)、误报、无法修复、修复完成
func StatusOperation(user *user.User, complianceObj map[string]interface{}, ctx *gin.Context, oneComplianceHistory *compliance_history.OneComplianceHistory) error {
	flowStatus := cast.ToInt(complianceObj["flow_status"])
	// 检查操作状态
	err := threat_center.CheckStatusOperation(oneComplianceHistory.Status, flowStatus)
	if err != nil {
		return err
	}
	// 检查合规检测状态
	ok, msg := threat_history.CheckStatus(flowStatus, oneComplianceHistory.Status)
	if !ok {
		return errors.New(msg)
	}
	// 催促 发送站内消息通知
	if oneComplianceHistory.Status == poc.PocOperateOfUrge {
		err = compliance_history.SendLocalNotice(complianceObj, oneComplianceHistory.Descrition)
		if err != nil {
			return err
		}
		return nil
	}
	content := threat_history.GetOperateContent(user.Account, oneComplianceHistory.Status, "合规风险")
	// 创建操作记录
	err = compliance_history.CreateHistory(ctx, user, oneComplianceHistory, nil, complianceObj["flow_status"].(int), content)
	if err != nil {
		return err
	}
	staffs := user_staff.NewUserRoleModel().Get(user.Id)
	if len(staffs) > 0 {
		staff, err := threat_history.GetStaff(staffs[0])
		if err == nil && staff != nil {
			// 发送webhook消息
			compliance_history.SendWebhookMsg(oneComplianceHistory, staff, user, []map[string]interface{}{complianceObj})
		} else {
			logger.Warn("获取合规风险修复负责人信息失败,合规风险消息webhook发送失败", user.Id)
		}
	} else {
		logger.Warn("未获取到users_staffs,合规风险消息webhook发送失败", user.Id)
	}

	// 修改状态
	err = compliance_history.UpdateComplianceStatus(oneComplianceHistory, complianceObj, nil, nil)
	if err != nil {
		return err
	}

	return nil
}

func checkDistribute(operationStatus int, complianceStatus int) error {
	//检查合规检测是否符合操作类型
	if operationStatus != poc.PocStatusOfBeRepair && operationStatus != poc.PocStatusOfForward {
		return errors.New("不支持的操作类型")
	}

	//校验操作类型 派发仅支持：
	if operationStatus == poc.PocStatusOfBeRepair {
		//操作的是派发不在符合的状态范围内，返回失败
		if !utils.InArray(complianceStatus, []int{poc.PocStatusOfNew, poc.PocStatusOfStillExist, poc.PocStatusOfErrorReport, poc.PocStatusOfCantRepaired}) {
			return errors.New("合规检测状态不符合操作类型")
		}
	}

	//校验操作类型 转交仅支持：
	if operationStatus == poc.PocStatusOfForward {
		//操作的是派发不在符合的状态范围内，返回失败
		if !utils.InArray(complianceStatus, []int{poc.PocStatusOfBeRepair, poc.PocStatusOfForward, poc.PocStatusOfDelay, poc.PocStatusOfTimeout, poc.PocStatusOfNoRepair, poc.PocOperateOfUrge}) {
			return errors.New("合规检测状态不符合操作类型")
		}
	}
	return nil
}

// GetComplianceRecords 获取合规检测记录
func GetComplianceRecords(ctx *gin.Context, ids []string, paramList map[string]interface{}) ([]interface{}, error) {
	// 查询合规检测ES记录
	return threat.ShowMultiple(ctx, ids, paramList, compliance_monitor.NewComplianceMonitorTaskRecords())
}

// CheckAsset 检查违规资产是否存在
func CheckAsset(ctx *gin.Context, assetId string) bool {
	query := elastic.NewBoolQuery()
	query = query.Must(elastic.NewTermQuery("id", assetId))
	return assets.NewAssets().Exists(ctx, query)
}
