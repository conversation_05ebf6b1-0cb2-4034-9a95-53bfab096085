package compliance_center

import (
	"encoding/json"
	"fobrain/fobrain/app/repository/compliance_history"
	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/models/elastic/compliance_monitor"
	esmodel_staff "fobrain/models/elastic/staff"
	"fobrain/pkg/queue"
	"github.com/alicebob/miniredis/v2"
	"github.com/go-redis/redis/v8"
	"github.com/olivere/elastic/v7"
	"testing"

	"github.com/stretchr/testify/assert"
)

// TestNewComplianceRiskUpdateStatus 测试构造函数
func TestNewComplianceRiskUpdateStatus(t *testing.T) {
	instance := NewComplianceRiskUpdateStatus()

	assert.NotNil(t, instance)
	assert.NotNil(t, instance.clog)
	assert.Equal(t, "compliance_update_queue", instance.groupName)
	assert.Equal(t, "fobrain:compliance:update", instance.qName)
}

// TestGetInfoFromMsg_Success 测试成功解析消息
func TestGetInfoFromMsg_Success(t *testing.T) {
	tests := []struct {
		name      string
		msg       queue.QueueMsg
		expected  *ComplianceUpdateMsg
		shouldErr bool
	}{
		{
			name: "完整消息解析",
			msg: queue.QueueMsg{
				Values: map[string]interface{}{
					"id":           "test_id_123",
					"staff_fid":    "staff_001",
					"cc_staff_fid": "cc_staff_001",
					"flow_status":  1,
					"limit_date":   "2024-12-31",
				},
			},
			expected: &ComplianceUpdateMsg{
				Id:         "test_id_123",
				StaffFid:   "staff_001",
				CCStaffFid: "cc_staff_001",
				FlowStatus: 1,
				LimitDate:  "2024-12-31",
			},
			shouldErr: false,
		},
		{
			name: "仅包含ID和状态",
			msg: queue.QueueMsg{
				Values: map[string]interface{}{
					"id":          "test_id_456",
					"flow_status": 2,
				},
			},
			expected: &ComplianceUpdateMsg{
				Id:         "test_id_456",
				StaffFid:   "",
				CCStaffFid: "",
				FlowStatus: 2,
				LimitDate:  "",
			},
			shouldErr: false,
		},
		{
			name: "仅包含ID和人员信息",
			msg: queue.QueueMsg{
				Values: map[string]interface{}{
					"id":        "test_id_789",
					"staff_fid": "staff_002",
				},
			},
			expected: &ComplianceUpdateMsg{
				Id:         "test_id_789",
				StaffFid:   "staff_002",
				CCStaffFid: "",
				FlowStatus: 0,
				LimitDate:  "",
			},
			shouldErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := getInfoFromMsg(tt.msg)

			if tt.shouldErr {
				assert.Error(t, err)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Equal(t, tt.expected.Id, result.Id)
				assert.Equal(t, tt.expected.StaffFid, result.StaffFid)
				assert.Equal(t, tt.expected.CCStaffFid, result.CCStaffFid)
				assert.Equal(t, tt.expected.FlowStatus, result.FlowStatus)
				assert.Equal(t, tt.expected.LimitDate, result.LimitDate)
			}
		})
	}
}

// TestGetInfoFromMsg_Errors 测试错误情况
func TestGetInfoFromMsg_Errors(t *testing.T) {
	tests := []struct {
		name        string
		msg         queue.QueueMsg
		expectedErr string
	}{
		{
			name: "空Values",
			msg: queue.QueueMsg{
				Values: map[string]interface{}{},
			},
			expectedErr: "合规风险更新消息中未包含搜索条件",
		},
		{
			name: "缺少ID",
			msg: queue.QueueMsg{
				Values: map[string]interface{}{
					"staff_fid":   "staff_001",
					"flow_status": 1,
				},
			},
			expectedErr: "合规风险更新消息中未包含id",
		},
		{
			name: "ID为空字符串",
			msg: queue.QueueMsg{
				Values: map[string]interface{}{
					"id":          "",
					"flow_status": 1,
				},
			},
			expectedErr: "合规风险更新消息中id为空",
		},
		{
			name: "ID为空格",
			msg: queue.QueueMsg{
				Values: map[string]interface{}{
					"id":          "   ",
					"flow_status": 1,
				},
			},
			expectedErr: "合规风险更新消息中id为空",
		},
		{
			name: "既无人员信息也无状态信息",
			msg: queue.QueueMsg{
				Values: map[string]interface{}{
					"id": "test_id",
				},
			},
			expectedErr: "合规风险更新消息中未包含人员信息或状态信息",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := getInfoFromMsg(tt.msg)
			assert.Error(t, err)
			assert.Nil(t, result)
			assert.Contains(t, err.Error(), tt.expectedErr)
		})
	}
}

// TestGetInfoFromMsg_TypeConversions 测试类型转换
func TestGetInfoFromMsg_TypeConversions(t *testing.T) {
	tests := []struct {
		name     string
		msg      queue.QueueMsg
		expected *ComplianceUpdateMsg
	}{
		{
			name: "状态为字符串数字",
			msg: queue.QueueMsg{
				Values: map[string]interface{}{
					"id":          "test_id",
					"flow_status": "5",
				},
			},
			expected: &ComplianceUpdateMsg{
				Id:         "test_id",
				FlowStatus: 5,
			},
		},
		{
			name: "ID为数字",
			msg: queue.QueueMsg{
				Values: map[string]interface{}{
					"id":          123,
					"flow_status": 1,
				},
			},
			expected: &ComplianceUpdateMsg{
				Id:         "123",
				FlowStatus: 1,
			},
		},
		{
			name: "人员FID为数字",
			msg: queue.QueueMsg{
				Values: map[string]interface{}{
					"id":        "test_id",
					"staff_fid": 456,
				},
			},
			expected: &ComplianceUpdateMsg{
				Id:       "test_id",
				StaffFid: "456",
			},
		},
		{
			name: "限制日期为数字",
			msg: queue.QueueMsg{
				Values: map[string]interface{}{
					"id":          "test_id",
					"flow_status": 1,
					"limit_date":  20241231,
				},
			},
			expected: &ComplianceUpdateMsg{
				Id:         "test_id",
				FlowStatus: 1,
				LimitDate:  "20241231",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := getInfoFromMsg(tt.msg)
			assert.NoError(t, err)
			assert.NotNil(t, result)
			assert.Equal(t, tt.expected.Id, result.Id)
			assert.Equal(t, tt.expected.StaffFid, result.StaffFid)
			assert.Equal(t, tt.expected.CCStaffFid, result.CCStaffFid)
			assert.Equal(t, tt.expected.FlowStatus, result.FlowStatus)
			assert.Equal(t, tt.expected.LimitDate, result.LimitDate)
		})
	}
}

// TestGetInfoFromMsg_EdgeCases 测试边界情况
func TestGetInfoFromMsg_EdgeCases(t *testing.T) {
	tests := []struct {
		name      string
		msg       queue.QueueMsg
		expected  *ComplianceUpdateMsg
		shouldErr bool
	}{
		{
			name: "ID包含特殊字符",
			msg: queue.QueueMsg{
				Values: map[string]interface{}{
					"id":          "test-id_123@special",
					"flow_status": 1,
				},
			},
			expected: &ComplianceUpdateMsg{
				Id:         "test-id_123@special",
				FlowStatus: 1,
			},
			shouldErr: false,
		},
		{
			name: "人员FID为空字符串",
			msg: queue.QueueMsg{
				Values: map[string]interface{}{
					"id":          "test_id",
					"staff_fid":   "",
					"flow_status": 1,
				},
			},
			expected: &ComplianceUpdateMsg{
				Id:         "test_id",
				StaffFid:   "",
				FlowStatus: 1,
			},
			shouldErr: false,
		},
		{
			name: "抄送人员FID包含冒号",
			msg: queue.QueueMsg{
				Values: map[string]interface{}{
					"id":           "test_id",
					"cc_staff_fid": "张三:13800138000",
					"flow_status":  1,
				},
			},
			expected: &ComplianceUpdateMsg{
				Id:         "test_id",
				CCStaffFid: "张三:13800138000",
				FlowStatus: 1,
			},
			shouldErr: false,
		},
		{
			name: "limit_date为空字符串",
			msg: queue.QueueMsg{
				Values: map[string]interface{}{
					"id":          "test_id",
					"flow_status": 1,
					"limit_date":  "",
				},
			},
			expected: &ComplianceUpdateMsg{
				Id:         "test_id",
				FlowStatus: 1,
				LimitDate:  "",
			},
			shouldErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := getInfoFromMsg(tt.msg)

			if tt.shouldErr {
				assert.Error(t, err)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Equal(t, tt.expected.Id, result.Id)
				assert.Equal(t, tt.expected.StaffFid, result.StaffFid)
				assert.Equal(t, tt.expected.CCStaffFid, result.CCStaffFid)
				assert.Equal(t, tt.expected.FlowStatus, result.FlowStatus)
				assert.Equal(t, tt.expected.LimitDate, result.LimitDate)
			}
		})
	}
}

// TestGetInfoFromMsg_NilValues 测试nil Values的情况
func TestGetInfoFromMsg_NilValues(t *testing.T) {
	msg := queue.QueueMsg{
		Values: nil,
	}

	result, err := getInfoFromMsg(msg)
	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "合规风险更新消息中未包含搜索条件")
}

// TestComplianceRiskUpdateStatus_failedHandle 测试失败处理方法
func TestComplianceRiskUpdateStatus_failedHandle(t *testing.T) {
	instance := NewComplianceRiskUpdateStatus()

	// 创建测试消息
	msg := queue.QueueMsg{
		ID: "test_msg_id",
		Values: map[string]interface{}{
			"test": "value",
		},
	}

	// 测试failedHandle方法会panic（因为queue.Ack的依赖问题）
	assert.Panics(t, func() {
		instance.failedHandle("test error message", msg)
	})
}

// TestComplianceUpdateMsg_Struct 测试ComplianceUpdateMsg结构体
func TestComplianceUpdateMsg_Struct(t *testing.T) {
	msg := &ComplianceUpdateMsg{
		Id:         "test_id",
		StaffFid:   "staff_001",
		CCStaffFid: "cc_staff_001",
		FlowStatus: 1,
		LimitDate:  "2024-12-31",
	}

	assert.Equal(t, "test_id", msg.Id)
	assert.Equal(t, "staff_001", msg.StaffFid)
	assert.Equal(t, "cc_staff_001", msg.CCStaffFid)
	assert.Equal(t, 1, msg.FlowStatus)
	assert.Equal(t, "2024-12-31", msg.LimitDate)
}

// TestComplianceUpdateMsg_DefaultValues 测试默认值
func TestComplianceUpdateMsg_DefaultValues(t *testing.T) {
	msg := &ComplianceUpdateMsg{}

	assert.Empty(t, msg.Id)
	assert.Empty(t, msg.StaffFid)
	assert.Empty(t, msg.CCStaffFid)
	assert.Zero(t, msg.FlowStatus)
	assert.Empty(t, msg.LimitDate)
}

// 基准测试
func BenchmarkGetInfoFromMsg(b *testing.B) {
	msg := queue.QueueMsg{
		Values: map[string]interface{}{
			"id":           "test_id_123",
			"staff_fid":    "staff_001",
			"cc_staff_fid": "cc_staff_001",
			"flow_status":  1,
			"limit_date":   "2024-12-31",
		},
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		getInfoFromMsg(msg)
	}
}

func BenchmarkNewComplianceRiskUpdateStatus(b *testing.B) {
	for i := 0; i < b.N; i++ {
		NewComplianceRiskUpdateStatus()
	}
}

// 表格驱动测试：消息解析的完整场景
func TestGetInfoFromMsg_CompleteScenarios(t *testing.T) {
	scenarios := []struct {
		name        string
		values      map[string]interface{}
		expectError bool
		errorMsg    string
		checkResult func(*testing.T, *ComplianceUpdateMsg)
	}{
		{
			name: "正常业务流程-派发",
			values: map[string]interface{}{
				"id":          "compliance_001",
				"staff_fid":   "staff_张三_001",
				"flow_status": 2,
			},
			expectError: false,
			checkResult: func(t *testing.T, msg *ComplianceUpdateMsg) {
				assert.Equal(t, "compliance_001", msg.Id)
				assert.Equal(t, "staff_张三_001", msg.StaffFid)
				assert.Equal(t, 2, msg.FlowStatus)
			},
		},
		{
			name: "正常业务流程-状态更新",
			values: map[string]interface{}{
				"id":          "compliance_002",
				"flow_status": 5,
			},
			expectError: false,
			checkResult: func(t *testing.T, msg *ComplianceUpdateMsg) {
				assert.Equal(t, "compliance_002", msg.Id)
				assert.Equal(t, 5, msg.FlowStatus)
				assert.Empty(t, msg.StaffFid)
			},
		},
		{
			name: "抄送场景",
			values: map[string]interface{}{
				"id":           "compliance_003",
				"cc_staff_fid": "李四:13900139000",
				"flow_status":  3,
			},
			expectError: false,
			checkResult: func(t *testing.T, msg *ComplianceUpdateMsg) {
				assert.Equal(t, "compliance_003", msg.Id)
				assert.Equal(t, "李四:13900139000", msg.CCStaffFid)
				assert.Equal(t, 3, msg.FlowStatus)
			},
		},
		{
			name: "设置期限",
			values: map[string]interface{}{
				"id":         "compliance_004",
				"staff_fid":  "staff_004",
				"limit_date": "2024-12-31 23:59:59",
			},
			expectError: false,
			checkResult: func(t *testing.T, msg *ComplianceUpdateMsg) {
				assert.Equal(t, "compliance_004", msg.Id)
				assert.Equal(t, "staff_004", msg.StaffFid)
				assert.Equal(t, "2024-12-31 23:59:59", msg.LimitDate)
			},
		},
	}

	for _, scenario := range scenarios {
		t.Run(scenario.name, func(t *testing.T) {
			msg := queue.QueueMsg{Values: scenario.values}
			result, err := getInfoFromMsg(msg)
			if scenario.expectError {
				assert.Error(t, err)
				if scenario.errorMsg != "" {
					assert.Contains(t, err.Error(), scenario.errorMsg)
				}
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				if scenario.checkResult != nil {
					scenario.checkResult(t, result)
				}
			}
		})
	}
}

func TestComplianceRiskUpdateStatus_Consumer(t *testing.T) {
	s, err := miniredis.Run()
	if err != nil {
		t.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
	}
	defer s.Close()

	cli := redis.NewClient(&redis.Options{
		Addr: s.Addr(),
	})
	testcommon.SetRedisClient(cli)

	rq := &queue.RedisQueue{Client: cli}
	count := rq.Push(compliance_history.ComplianceUpdateQueueName, []map[string]interface{}{
		{
			"id":           "test_id",
			"staff_fid":    "1232",
			"cc_staff_fid": "432g",
			"flow_status":  1,
			"limit_date":   20241231,
		},
	})
	assert.Equal(t, 1, count)
	NewComplianceRiskUpdateStatus().Consumer()

}

func Test_handleComplianceStatus(t *testing.T) {

	mockEs := testcommon.NewMockServer()
	defer mockEs.Close()
	person := esmodel_staff.Staff{
		Id:   "1232",
		Name: "张三",
	}
	docJson, _ := json.Marshal(person)

	// 模拟 staff 的 _search 返回（只含 docvalue_fields）
	mockEs.Register("/staff/_search", elastic.SearchResult{
		TookInMillis: 1,
		TimedOut:     false,
		Shards: &elastic.ShardsInfo{
			Total:      1,
			Successful: 1,
			Skipped:    0,
			Failed:     0,
		},
		Hits: &elastic.SearchHits{
			TotalHits: &elastic.TotalHits{
				Value:    1,
				Relation: "eq",
			},
			Hits: []*elastic.SearchHit{
				{
					Source: docJson,
				},
			},
		},
	})

	errMsg := handleComplianceStatus(queue.QueueMsg{
		ID: "2gvw",
		Values: map[string]interface{}{
			"id":           "test_id",
			"staff_fid":    "1232",
			"cc_staff_fid": "432g",
			"flow_status":  1,
			"limit_date":   20241231,
		},
	}, &ComplianceUpdateMsg{
		Id:         "test_id",
		StaffFid:   "1232",
		CCStaffFid: "432g",
		FlowStatus: 1,
		LimitDate:  "20241231",
	}, &compliance_monitor.ComplianceMonitorTaskRecords{})
	assert.Empty(t, errMsg)
}
