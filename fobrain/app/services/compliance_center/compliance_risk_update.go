package compliance_center

import (
	"context"
	"fmt"
	"fobrain/fobrain/app/repository/compliance_history"
	"fobrain/initialize/es"
	logs "fobrain/mergeService/utils/log"
	"fobrain/models/elastic/assets"
	"fobrain/models/elastic/compliance_monitor"
	"fobrain/models/elastic/helper"
	esmodel_poc "fobrain/models/elastic/poc"
	esmodel_staff "fobrain/models/elastic/staff"
	"fobrain/pkg/queue"
	"fobrain/pkg/utils/common_logs"
	"strconv"
	"strings"
	"time"

	"git.gobies.org/caasm/fobrain-components/utils"
	"git.gobies.org/caasm/fobrain-components/utils/localtime"
	"github.com/olivere/elastic/v7"
	"github.com/spf13/cast"
)

type ComplianceRiskUpdateStatus struct {
	clog      *common_logs.Logger
	groupName string
	qName     string
}

func NewComplianceRiskUpdateStatus() *ComplianceRiskUpdateStatus {
	return &ComplianceRiskUpdateStatus{
		clog:      logs.GetLogger("compliance_update_queue"),
		groupName: "compliance_update_queue",
		qName:     compliance_history.ComplianceUpdateQueueName,
	}
}

func (c *ComplianceRiskUpdateStatus) failedHandle(errMsg string, m queue.QueueMsg) {
	c.clog.Warnf(errMsg)
	// 确认消息已处理
	queue.NewQueue(queue.QueueType_Redis).Ack(c.qName, c.groupName, m.ID)
}
func (c *ComplianceRiskUpdateStatus) Consumer() {
	clog := c.clog
	maxConcurrency := 5
	msgChan, consumerName, err := queue.NewQueue(queue.QueueType_Redis).
		Subscribe(c.qName, c.groupName, maxConcurrency)
	clog.Info("订阅合规风险更新消息. qName: ", c.qName, " consumerName: ", consumerName)
	if err != nil {
		clog.Error("Failed to subscribe to compliance risk update")
		return
	}

	sem := make(chan struct{}, maxConcurrency)
	go func(sem chan struct{}) {
		for msg := range msgChan {
			sem <- struct{}{}
			go func(m queue.QueueMsg) {
				defer func() {
					if r := recover(); r != nil {
						clog.Errorf("合规风险更新消息处理失败. msg: %v, err: %v", m, r)
					}
					// 释放一个处理能力
					clog.Debug("释放一个处理能力")
					<-sem
				}()

				clog.Infof("收到合规风险更新消息. msg: %v", m)
				parsedMsg, err := getInfoFromMsg(m)
				if err != nil {
					c.failedHandle(fmt.Sprintf("从消息中获取合规风险id、人员信息、状态信息失败. msg: %v, err: %v", m, err), m)
					return
				}
				complianceRecord, err := es.GetById[compliance_monitor.ComplianceMonitorTaskRecords](parsedMsg.Id)
				if err != nil {
					c.failedHandle(fmt.Sprintf("根据id查询合规风险失败. msg: %v, id: %s, err: %v", m, parsedMsg.Id, err), m)
					return
				}
				if complianceRecord == nil {
					c.failedHandle(fmt.Sprintf("根据id查询合规风险为空. msg: %v, id: %v", m, parsedMsg.Id), m)
					return
				}
				errMsg := handleComplianceStatus(m, parsedMsg, complianceRecord)
				if errMsg != "" {
					c.failedHandle(errMsg, m)
					return
				}
				// 更新
				updateResp, err := es.GetEsClient().Update().
					Index(complianceRecord.IndexName()).
					Id(complianceRecord.Id).
					Doc(complianceRecord).
					Do(context.TODO())
				if err != nil {
					c.failedHandle(fmt.Sprintf("更新合规风险记录失败. msg: %v, err: %v", m, err), m)
					return
				}
				if updateResp == nil || updateResp.Result != "updated" {
					c.failedHandle(fmt.Sprintf("更新合规风险记录失败. unexpected update result: %+v\n", updateResp), m)
					return
				}
			}(msg)
		}
	}(sem)
}

func handleComplianceStatus(m queue.QueueMsg, parsedMsg *ComplianceUpdateMsg, complianceRecord *compliance_monitor.ComplianceMonitorTaskRecords) string {
	now := time.Now()
	if parsedMsg.StaffFid != "" {
		person, err := es.First[esmodel_staff.Staff](elastic.NewBoolQuery().Must(elastic.NewTermQuery("fid", parsedMsg.StaffFid)), nil)
		if err != nil {

			return fmt.Sprintf("根据fid查询人员失败. msg: %v, fid: %v, err: %v", m, parsedMsg.StaffFid, err)
		}
		if person == nil {
			return fmt.Sprintf("根据fid查询人员为空. msg: %v, fid: %v", m, parsedMsg.StaffFid)
		}
		complianceRecord.RepairDepartmentIds = make([]uint64, 0)

		// 获取人员部门
		var departments []*assets.DepartmentBase
		for _, departmentId := range person.DepartmentsIds {
			departmentIdInt, err := strconv.ParseUint(departmentId, 10, 64)
			if err != nil {

				return fmt.Sprintf("获取人员部门失败. msg: %v, fid: %v, err: %v", m, parsedMsg.StaffFid, err)
			}
			department := helper.GetDepartment(departmentIdInt, person.Name, person.Id, "", "")
			if department == nil {

				return fmt.Sprintf("获取人员部门失败. msg: %v, fid: %v", m, parsedMsg.StaffFid)
			}
			//人员部门id
			complianceRecord.RepairDepartmentIds = append(complianceRecord.RepairDepartmentIds, department.Id)
			//人员部门父级id
			for _, d := range department.Parents {
				complianceRecord.RepairDepartmentIds = append(complianceRecord.RepairDepartmentIds, d.Id)
			}
			departments = append(departments, department)
		}
		complianceRecord.RepairDepartmentIds = utils.ListDistinctNonZero(complianceRecord.RepairDepartmentIds)
		complianceRecord.PersonInfo = make([]*assets.PersonBase, 0)
		complianceRecord.PersonDepartment = make([]*assets.DepartmentBase, 0)
		pb := assets.NewPersonBase()
		pb.Id = person.Id
		pb.Name = person.Name
		pb.Fid = parsedMsg.StaffFid
		pb.Department = departments
		complianceRecord.PersonInfo = append(complianceRecord.PersonInfo, pb)
		complianceRecord.PersonDepartment = append(complianceRecord.PersonDepartment, departments...)
		// 人员信息更新，当前人员
		//complianceRecord.Person = map[string]string{
		//	"fid":    parsedMsg.StaffFid,
		//	"name":   person.Name,
		//	"mobile": person.Mobile,
		//}
		// 更新历史人员
		complianceRecord.PersonLimit = append(complianceRecord.PersonLimit, parsedMsg.StaffFid)
		complianceRecord.PersonLimit = utils.ListDistinctNonZero(complianceRecord.PersonLimit)
		complianceRecord.PersonLimitHash = append(complianceRecord.PersonLimitHash, utils.Md5Hash(parsedMsg.StaffFid))
		complianceRecord.PersonLimitHash = utils.ListDistinctNonZero(complianceRecord.PersonLimitHash)
	}
	if parsedMsg.CCStaffFid != "" {
		ccperson, err := es.First[esmodel_staff.Staff](elastic.NewBoolQuery().Must(elastic.NewTermQuery("fid", parsedMsg.CCStaffFid)), nil)
		if err != nil {

			return fmt.Sprintf("根据fid查询抄送人员失败. msg: %v, fid: %v, err: %v", m, parsedMsg.StaffFid, err)
		}
		if ccperson == nil {
			return fmt.Sprintf("根据fid查询抄送人员为空. msg: %v, fid: %v", m, parsedMsg.StaffFid)
		}
		// 人员信息更新，当前人员
		complianceRecord.CcPerson = map[string]string{
			"fid":    parsedMsg.CCStaffFid,
			"name":   ccperson.Name,
			"mobile": ccperson.Mobile,
		}
		// 更新历史人员
		complianceRecord.PersonLimit = append(complianceRecord.PersonLimit, parsedMsg.CCStaffFid)
		complianceRecord.PersonLimit = utils.ListDistinctNonZero(complianceRecord.PersonLimit)
		complianceRecord.PersonLimitHash = append(complianceRecord.PersonLimitHash, utils.Md5Hash(parsedMsg.CCStaffFid))
		complianceRecord.PersonLimitHash = utils.ListDistinctNonZero(complianceRecord.PersonLimitHash)
	}
	if parsedMsg.FlowStatus > 0 {
		complianceRecord.FlowStatus = parsedMsg.FlowStatus
		complianceRecord.FlowStatusChangeTime = localtime.NewLocalTime(now)
	}
	if parsedMsg.LimitDate != "" {
		var limitDate time.Time
		var parseSuccess = true
		limitDate, err := utils.ParseTime(parsedMsg.LimitDate)
		if err != nil {
			limitDate, err = utils.PraseStringTime(parsedMsg.LimitDate, utils.DateLayout)
			if err != nil {
				limitDate, err = utils.PraseStringTime(parsedMsg.LimitDate, utils.TimeDateHourLayout)
				if err != nil {
					parseSuccess = false
				}
			}
		}
		if parseSuccess {
			complianceRecord.LimitDate = localtime.NewLocalTime(limitDate)
		}
		if complianceRecord.Status == esmodel_poc.PocStatusOfRepaired || complianceRecord.Status == esmodel_poc.PocStatusOfErrorReport || complianceRecord.Status == esmodel_poc.PocStatusOfCantRepaired {
			// 漏洞状态为待重测，更新重测时间
			complianceRecord.ProcessedTime = localtime.NewLocalTime(now)
		}
		complianceRecord.UpdatedAt = localtime.NewLocalTime(now)
	}
	return ""
}

type ComplianceUpdateMsg struct {
	Id         string
	StaffFid   string
	CCStaffFid string
	FlowStatus int
	LimitDate  string
}

func getInfoFromMsg(m queue.QueueMsg) (*ComplianceUpdateMsg, error) {
	if len(m.Values) == 0 {
		return nil, fmt.Errorf("合规风险更新消息中未包含搜索条件. msg: %v", m)
	}
	var msg ComplianceUpdateMsg
	// 获取人员信息
	staffFid, ok := m.Values["staff_fid"]
	if ok {
		msg.StaffFid = cast.ToString(staffFid)
	}
	// 获取抄送人员信息
	ccStaffFid, ok := m.Values["cc_staff_fid"]
	if ok {
		msg.CCStaffFid = cast.ToString(ccStaffFid)
	}
	// 获取状态信息
	status, ok := m.Values["flow_status"]
	if ok {
		msg.FlowStatus = cast.ToInt(status)
		if msg.FlowStatus <= 0 {
			fmt.Errorf("合规风险更新消息中状态信息不符预期. msg: %v", m)
		}
	}
	// 如果人员信息和状态信息都为空，则不进行更新
	if staffFid == nil && status == nil {
		return nil, fmt.Errorf("合规风险更新消息中未包含人员信息或状态信息. msg: %v", m)
	}
	// 获取漏洞id
	id, ok := m.Values["id"]
	if !ok {
		return nil, fmt.Errorf("合规风险更新消息中未包含id. msg: %v", m)
	}
	msg.Id = strings.TrimSpace(cast.ToString(id))
	// 验证合规风险ID是否为空或无效
	if msg.Id == "" {
		return nil, fmt.Errorf("合规风险更新消息中id为空. msg: %v", m)
	}
	// 获取修复超期时间
	limitDateVal, ok := m.Values["limit_date"]
	if ok {
		msg.LimitDate = cast.ToString(limitDateVal)
	}
	return &msg, nil
}
