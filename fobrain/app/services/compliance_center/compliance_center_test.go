package compliance_center

import (
	"fobrain/fobrain/app/repository/compliance_history"
	"fobrain/models/elastic/assets"
	"fobrain/models/elastic/compliance_monitor"
	"fobrain/models/elastic/poc"
	"fobrain/models/elastic/staff"
	"fobrain/models/mysql/user"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/olivere/elastic/v7"
	"strings"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

// TestCheckDistribute 测试分发检查逻辑
func TestCheckDistribute_Success(t *testing.T) {
	tests := []struct {
		name             string
		operationStatus  int
		complianceStatus int
		expectError      bool
		errorMessage     string
	}{
		{
			name:             "valid distribute operation with new status",
			operationStatus:  poc.PocStatusOfBeRepair,
			complianceStatus: poc.PocStatusOfNew,
			expectError:      false,
		},
		{
			name:             "valid distribute operation with still exist status",
			operationStatus:  poc.PocStatusOfBeRepair,
			complianceStatus: poc.PocStatusOfStillExist,
			expectError:      false,
		},
		{
			name:             "valid distribute operation with error report status",
			operationStatus:  poc.PocStatusOfBeRepair,
			complianceStatus: poc.PocStatusOfErrorReport,
			expectError:      false,
		},
		{
			name:             "valid distribute operation with cant repaired status",
			operationStatus:  poc.PocStatusOfBeRepair,
			complianceStatus: poc.PocStatusOfCantRepaired,
			expectError:      false,
		},
		{
			name:             "valid forward operation with be repair status",
			operationStatus:  poc.PocStatusOfForward,
			complianceStatus: poc.PocStatusOfBeRepair,
			expectError:      false,
		},
		{
			name:             "valid forward operation with forward status",
			operationStatus:  poc.PocStatusOfForward,
			complianceStatus: poc.PocStatusOfForward,
			expectError:      false,
		},
		{
			name:             "valid forward operation with delay status",
			operationStatus:  poc.PocStatusOfForward,
			complianceStatus: poc.PocStatusOfDelay,
			expectError:      false,
		},
		{
			name:             "valid forward operation with timeout status",
			operationStatus:  poc.PocStatusOfForward,
			complianceStatus: poc.PocStatusOfTimeout,
			expectError:      false,
		},
		{
			name:             "valid forward operation with no repair status",
			operationStatus:  poc.PocStatusOfForward,
			complianceStatus: poc.PocStatusOfNoRepair,
			expectError:      false,
		},
		{
			name:             "valid forward operation with urge status",
			operationStatus:  poc.PocStatusOfForward,
			complianceStatus: poc.PocOperateOfUrge,
			expectError:      false,
		},
		{
			name:             "invalid operation type",
			operationStatus:  999,
			complianceStatus: poc.PocStatusOfNew,
			expectError:      true,
			errorMessage:     "不支持的操作类型",
		},
		{
			name:             "distribute with invalid compliance status",
			operationStatus:  poc.PocStatusOfBeRepair,
			complianceStatus: poc.PocStatusOfRepaired,
			expectError:      true,
			errorMessage:     "合规检测状态不符合操作类型",
		},
		{
			name:             "forward with invalid compliance status",
			operationStatus:  poc.PocStatusOfForward,
			complianceStatus: poc.PocStatusOfNew,
			expectError:      true,
			errorMessage:     "合规检测状态不符合操作类型",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := checkDistribute(tt.operationStatus, tt.complianceStatus)
			if tt.expectError {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errorMessage)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// TestCheckDistribute_DistributeValidStatuses 测试分发操作的所有有效状态
func TestCheckDistribute_DistributeValidStatuses(t *testing.T) {
	validStatuses := []int{
		poc.PocStatusOfNew,
		poc.PocStatusOfStillExist,
		poc.PocStatusOfErrorReport,
		poc.PocStatusOfCantRepaired,
	}

	for _, status := range validStatuses {
		t.Run("distribute_valid_status", func(t *testing.T) {
			err := checkDistribute(poc.PocStatusOfBeRepair, status)
			assert.NoError(t, err)
		})
	}
}

// TestCheckDistribute_ForwardValidStatuses 测试转交操作的所有有效状态
func TestCheckDistribute_ForwardValidStatuses(t *testing.T) {
	validStatuses := []int{
		poc.PocStatusOfBeRepair,
		poc.PocStatusOfForward,
		poc.PocStatusOfDelay,
		poc.PocStatusOfTimeout,
		poc.PocStatusOfNoRepair,
		poc.PocOperateOfUrge,
	}

	for _, status := range validStatuses {
		t.Run("forward_valid_status", func(t *testing.T) {
			err := checkDistribute(poc.PocStatusOfForward, status)
			assert.NoError(t, err)
		})
	}
}

// TestCheckDistribute_InvalidOperationTypes 测试无效的操作类型
func TestCheckDistribute_InvalidOperationTypes(t *testing.T) {
	invalidOperationTypes := []int{0, -1, 999, 100}

	for _, opType := range invalidOperationTypes {
		t.Run("invalid_operation_type", func(t *testing.T) {
			err := checkDistribute(opType, poc.PocStatusOfNew)
			assert.Error(t, err)
			assert.Contains(t, err.Error(), "不支持的操作类型")
		})
	}
}

// TestCheckDistribute_DistributeInvalidStatuses 测试分发操作的无效状态
func TestCheckDistribute_DistributeInvalidStatuses(t *testing.T) {
	invalidStatuses := []int{
		poc.PocStatusOfRepaired,
		poc.PocStatusOfForward,
		poc.PocStatusOfDelay,
		poc.PocStatusOfTimeout,
		999,
	}

	for _, status := range invalidStatuses {
		t.Run("distribute_invalid_status", func(t *testing.T) {
			err := checkDistribute(poc.PocStatusOfBeRepair, status)
			assert.Error(t, err)
			assert.Contains(t, err.Error(), "合规检测状态不符合操作类型")
		})
	}
}

// TestCheckDistribute_ForwardInvalidStatuses 测试转交操作的无效状态
func TestCheckDistribute_ForwardInvalidStatuses(t *testing.T) {
	invalidStatuses := []int{
		poc.PocStatusOfNew,
		poc.PocStatusOfStillExist,
		poc.PocStatusOfErrorReport,
		poc.PocStatusOfRepaired,
		999,
	}

	for _, status := range invalidStatuses {
		t.Run("forward_invalid_status", func(t *testing.T) {
			err := checkDistribute(poc.PocStatusOfForward, status)
			assert.Error(t, err)
			assert.Contains(t, err.Error(), "合规检测状态不符合操作类型")
		})
	}
}

// TestGetComplianceRecords_BasicFunctionality 测试获取合规记录的基础功能
func TestGetComplianceRecords_BasicFunctionality(t *testing.T) {
	gin.SetMode(gin.TestMode)
	ctx, _ := gin.CreateTestContext(nil)

	// 测试输入验证
	ids := []string{"id1", "id2", "id3"}
	paramList := map[string]interface{}{
		"param1": "value1",
		"param2": 123,
	}

	// 创建模型实例来验证参数
	model := compliance_monitor.NewComplianceMonitorTaskRecords()
	assert.NotNil(t, model)

	// 验证参数类型
	assert.IsType(t, []string{}, ids)
	assert.IsType(t, map[string]interface{}{}, paramList)

	// 由于实际函数会因为依赖问题panic，我们测试预期会发生panic
	assert.Panics(t, func() {
		GetComplianceRecords(ctx, ids, paramList)
	})
}

// TestGetComplianceRecords_EmptyInputs 测试空输入的情况
func TestGetComplianceRecords_EmptyInputs(t *testing.T) {
	gin.SetMode(gin.TestMode)
	ctx, _ := gin.CreateTestContext(nil)

	// 测试空ID列表
	emptyIds := []string{}
	emptyParams := map[string]interface{}{}

	assert.Panics(t, func() {
		GetComplianceRecords(ctx, emptyIds, emptyParams)
	})

	// 测试nil参数
	assert.Panics(t, func() {
		GetComplianceRecords(ctx, []string{"id1"}, nil)
	})
}

// TestGetComplianceRecords_LargeInputs 测试大量输入数据
func TestGetComplianceRecords_LargeInputs(t *testing.T) {
	gin.SetMode(gin.TestMode)
	ctx, _ := gin.CreateTestContext(nil)

	// 创建大量ID
	largeIds := make([]string, 1000)
	for i := 0; i < 1000; i++ {
		largeIds[i] = "id_" + string(rune(i))
	}

	largeParams := map[string]interface{}{
		"limit":  1000,
		"offset": 0,
		"sort":   "created_at",
	}

	assert.Panics(t, func() {
		GetComplianceRecords(ctx, largeIds, largeParams)
	})
}

// 基准测试
func BenchmarkCheckDistribute(b *testing.B) {
	for i := 0; i < b.N; i++ {
		checkDistribute(poc.PocStatusOfBeRepair, poc.PocStatusOfNew)
	}
}

func BenchmarkCheckDistribute_InvalidType(b *testing.B) {
	for i := 0; i < b.N; i++ {
		checkDistribute(999, poc.PocStatusOfNew)
	}
}

func BenchmarkGetComplianceRecords(b *testing.B) {
	gin.SetMode(gin.TestMode)
	ctx, _ := gin.CreateTestContext(nil)
	ids := []string{"id1", "id2", "id3"}
	paramList := map[string]interface{}{"test": "value"}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		GetComplianceRecords(ctx, ids, paramList)
	}
}

// 表格驱动测试：状态组合测试
func TestCheckDistribute_StatusCombinations(t *testing.T) {
	type testCase struct {
		operationType    int
		complianceStatus int
		shouldPass       bool
	}

	testCases := []testCase{
		// 分发操作的测试用例
		{poc.PocStatusOfBeRepair, poc.PocStatusOfNew, true},
		{poc.PocStatusOfBeRepair, poc.PocStatusOfStillExist, true},
		{poc.PocStatusOfBeRepair, poc.PocStatusOfErrorReport, true},
		{poc.PocStatusOfBeRepair, poc.PocStatusOfCantRepaired, true},
		{poc.PocStatusOfBeRepair, poc.PocStatusOfRepaired, false},
		{poc.PocStatusOfBeRepair, poc.PocStatusOfForward, false},

		// 转交操作的测试用例
		{poc.PocStatusOfForward, poc.PocStatusOfBeRepair, true},
		{poc.PocStatusOfForward, poc.PocStatusOfForward, true},
		{poc.PocStatusOfForward, poc.PocStatusOfDelay, true},
		{poc.PocStatusOfForward, poc.PocStatusOfTimeout, true},
		{poc.PocStatusOfForward, poc.PocStatusOfNoRepair, true},
		{poc.PocOperateOfUrge, poc.PocStatusOfNew, false},

		// 无效操作类型
		{999, poc.PocStatusOfNew, false},
		{0, poc.PocStatusOfNew, false},
		{-1, poc.PocStatusOfNew, false},
	}

	for i, tc := range testCases {
		t.Run("combination_"+string(rune(i)), func(t *testing.T) {
			err := checkDistribute(tc.operationType, tc.complianceStatus)
			if tc.shouldPass {
				assert.NoError(t, err)
			} else {
				assert.Error(t, err)
			}
		})
	}
}

func TestDistributeMutil(t *testing.T) {
	testUser := &user.User{}
	testStaff := &staff.Staff{}
	ccStaff := &staff.Staff{}
	history := &compliance_history.SomeComplianceHistory{
		Status: poc.PocStatusOfBeRepair,
	}
	complianceObjs := []map[string]interface{}{
		{
			"id":          "o9whv9qewhv90ehjvqe0ovbe",
			"flow_status": poc.PocStatusOfNew,
		},
	}
	var patches = []*gomonkey.Patches{
		gomonkey.ApplyFuncReturn(compliance_history.CreateHistory, nil),
		gomonkey.ApplyFuncReturn(compliance_history.SendWebhookMsg, nil),
		gomonkey.ApplyFuncReturn(compliance_history.UpdateComplianceStatus, nil),
		gomonkey.ApplyFuncReturn(compliance_history.SendNotice, nil),
	}

	defer func() {
		for _, f := range patches {
			f.Reset()
		}
	}()
	err := DistributeMutil(testUser, testStaff, &gin.Context{}, history, complianceObjs, ccStaff)
	assert.NoError(t, err)
}

// TestCheckAsset 测试资产检查功能
func TestCheckAsset(t *testing.T) {
	gin.SetMode(gin.TestMode)
	ctx, _ := gin.CreateTestContext(nil)

	tests := []struct {
		name         string
		assetId      string
		mockExists   bool
		expectResult bool
	}{
		{
			name:         "asset exists",
			assetId:      "test-asset-id-1",
			mockExists:   true,
			expectResult: true,
		},
		{
			name:         "asset does not exist",
			assetId:      "test-asset-id-2",
			mockExists:   false,
			expectResult: false,
		},
		{
			name:         "empty asset id",
			assetId:      "",
			mockExists:   false,
			expectResult: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Mock the assets.NewAssets().Exists method
			patches := gomonkey.ApplyMethodReturn(&assets.Assets{}, "Exists", tt.mockExists)
			defer patches.Reset()

			result := CheckAsset(ctx, tt.assetId)
			assert.Equal(t, tt.expectResult, result)
		})
	}
}

// TestCheckAsset_QueryConstruction 测试查询构造逻辑
func TestCheckAsset_QueryConstruction(t *testing.T) {
	gin.SetMode(gin.TestMode)
	ctx, _ := gin.CreateTestContext(nil)
	assetId := "test-asset-123"

	// Mock Exists method to capture the query parameter
	var capturedQuery elastic.Query
	patches := gomonkey.ApplyMethod(&assets.Assets{}, "Exists", func(_ *assets.Assets, _ interface{}, query elastic.Query) bool {
		capturedQuery = query
		return true
	})
	defer patches.Reset()

	CheckAsset(ctx, assetId)

	// Verify that a BoolQuery was created
	assert.NotNil(t, capturedQuery)

	// The query should be a BoolQuery with a TermQuery for "id" field
	boolQuery, ok := capturedQuery.(*elastic.BoolQuery)
	assert.True(t, ok, "Expected BoolQuery")
	assert.NotNil(t, boolQuery)
}

// TestCheckAsset_EdgeCases 测试边界情况
func TestCheckAsset_EdgeCases(t *testing.T) {
	gin.SetMode(gin.TestMode)
	ctx, _ := gin.CreateTestContext(nil)

	edgeCases := []struct {
		name     string
		assetId  string
		expected bool
	}{
		{"very long asset id", strings.Repeat("a", 1000), false},
		{"asset id with special characters", "asset-123!@#$%^&*()", false},
		{"numeric asset id", "12345", false},
		{"uuid format", "550e8400-e29b-41d4-a716-************", false},
	}

	for _, tc := range edgeCases {
		t.Run(tc.name, func(t *testing.T) {
			patches := gomonkey.ApplyMethodReturn(&assets.Assets{}, "Exists", tc.expected)
			defer patches.Reset()

			result := CheckAsset(ctx, tc.assetId)
			assert.Equal(t, tc.expected, result)
		})
	}
}
