package threat_center

import (
	"errors"
	"testing"
	"time"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"

	"fobrain/fobrain/app/repository/threat"
	"fobrain/fobrain/app/repository/threat_history"
	"fobrain/fobrain/common/request"
	"fobrain/models/elastic/poc"
	"fobrain/models/elastic/staff"
	"fobrain/models/mysql/user"
	"fobrain/models/mysql/user_staff"
	pgidservice "fobrain/services/people_pgid"
)

func TestDistributeMutil(t *testing.T) {
	patches := []*gomonkey.Patches{
		gomonkey.ApplyFuncReturn(threat.Show, map[string]interface{}{
			"statusCode": 40,
		}, nil),
		gomonkey.ApplyFuncReturn(threat_history.CheckStatus, true, "1"),
		gomonkey.ApplyFuncReturn(threat_history.GetStaff, &staff.Staff{}, nil),
		gomonkey.ApplyFuncReturn(request.GetUserInfo, &user.User{}),
		gomonkey.ApplyFuncReturn(pgidservice.GetPgidById, "test_pgid", nil),
		gomonkey.ApplyFuncReturn(threat_history.GetOperateContent, "测试操作内容"),
		gomonkey.ApplyFuncReturn(threat_history.CreateHistory, nil),
		gomonkey.ApplyFuncReturn(threat_history.SendNotice, nil),
		gomonkey.ApplyFuncReturn(threat_history.SendWebhookMsg, nil),
		gomonkey.ApplyFuncReturn(threat_history.UpdateThreatStatus, nil),
		gomonkey.ApplyFuncReturn(threat_history.CreateThreatTask, nil),
	}
	time.Sleep(time.Second)

	err := DistributeMutil(nil, nil, &gin.Context{}, &threat_history.SomeThreatHistory{Status: 40}, []map[string]interface{}{map[string]interface{}{"statusCode": 40}}, nil)
	for _, patch := range patches {
		patch.Reset()
	}
	assert.Equal(t, err.Error(), "不支持的操作类型")

	patch := gomonkey.ApplyFuncReturn(threat.Show, map[string]interface{}{
		"statusCode": 40,
	}, errors.New("err"))
	err = DistributeMutil(nil, nil, &gin.Context{}, &threat_history.SomeThreatHistory{Status: 40}, []map[string]interface{}{map[string]interface{}{"statusCode": 40}}, nil)
	patch.Reset()
	assert.NotNil(t, err)

	patches = []*gomonkey.Patches{
		gomonkey.ApplyFuncReturn(threat.Show, map[string]interface{}{
			"statusCode": 40,
		}, nil),
		gomonkey.ApplyFuncReturn(threat_history.CheckStatus, true, "1"),
		gomonkey.ApplyFuncReturn(pgidservice.GetPgidById, "test_pgid", nil),
		gomonkey.ApplyFuncReturn(threat_history.GetStaff, &staff.Staff{}, errors.New("err")),
	}
	err = DistributeMutil(nil, nil, &gin.Context{}, &threat_history.SomeThreatHistory{Status: 40}, []map[string]interface{}{map[string]interface{}{"statusCode": 40}}, nil)
	for _, patch := range patches {
		patch.Reset()
	}
	assert.NotNil(t, err)

	patches = []*gomonkey.Patches{
		gomonkey.ApplyFuncReturn(threat.Show, map[string]interface{}{
			"statusCode": 40,
		}, nil),
		gomonkey.ApplyFuncReturn(threat_history.CheckStatus, true, "1"),
		gomonkey.ApplyFuncReturn(threat_history.GetStaff, &staff.Staff{}, nil),
		gomonkey.ApplyFuncReturn(request.GetUserInfo, &user.User{}),
		gomonkey.ApplyFuncReturn(pgidservice.GetPgidById, "test_pgid", nil),
		gomonkey.ApplyFuncReturn(threat_history.GetOperateContent, "测试操作内容"),
		gomonkey.ApplyFuncReturn(threat_history.CreateHistory, errors.New("err")),
	}
	err = DistributeMutil(nil, nil, &gin.Context{}, &threat_history.SomeThreatHistory{Status: 40}, []map[string]interface{}{map[string]interface{}{"statusCode": 40}}, nil)
	for _, patch := range patches {
		patch.Reset()
	}
	assert.NotNil(t, err)
}

func TestStatusOperation(t *testing.T) {
	patches := []*gomonkey.Patches{
		gomonkey.ApplyFuncReturn(threat.Show, map[string]interface{}{
			"statusCode": 40,
		}, nil),
		gomonkey.ApplyFuncReturn(threat_history.CheckStatus, true, "1"),
		gomonkey.ApplyFuncReturn(request.GetUserInfo, &user.User{}),
		gomonkey.ApplyFuncReturn(threat_history.SendLocalNotice, nil),
		gomonkey.ApplyFuncReturn(threat_history.GetOperateContent, "测试操作内容"),
		gomonkey.ApplyFuncReturn(threat_history.CreateHistory, nil),
		gomonkey.ApplyFuncReturn(threat_history.UpdateThreatStatus, nil),
	}

	// Mock user_staff.NewUserRoleModel().Get() 方法
	userStaffPatch := gomonkey.ApplyMethod(&user_staff.UserStaff{}, "Get", func(_ *user_staff.UserStaff, userId uint64) []string {
		return []string{"staff_id_1"}
	})

	// Mock threat_history.GetStaff
	getStaffPatch := gomonkey.ApplyFuncReturn(threat_history.GetStaff, &staff.Staff{Id: "staff_id_1"}, nil)

	// Mock threat_history.SendWebhookMsg
	sendWebhookPatch := gomonkey.ApplyFuncReturn(threat_history.SendWebhookMsg, nil)

	err := StatusOperation(&user.User{}, map[string]interface{}{
		"statusCode": 40,
	}, &gin.Context{}, &threat_history.OneThreatHistory{Status: 40})
	for _, patch := range patches {
		patch.Reset()
	}
	userStaffPatch.Reset()
	getStaffPatch.Reset()
	sendWebhookPatch.Reset()
	assert.Equal(t, err.Error(), "漏洞状态不符合操作类型")

	patches = []*gomonkey.Patches{
		gomonkey.ApplyFuncReturn(threat.Show, map[string]interface{}{
			"statusCode": 40,
		}, nil),
		gomonkey.ApplyFuncReturn(threat_history.CheckStatus, true, "1"),
		gomonkey.ApplyFuncReturn(request.GetUserInfo, &user.User{}),
		gomonkey.ApplyFuncReturn(threat_history.SendLocalNotice, nil),
		gomonkey.ApplyFuncReturn(threat_history.GetOperateContent, "测试操作内容"),
		gomonkey.ApplyFuncReturn(threat_history.CreateHistory, nil),
		gomonkey.ApplyFuncReturn(threat_history.UpdateThreatStatus, nil),
	}

	// Mock user_staff for second test case
	userStaffPatch2 := gomonkey.ApplyMethod(&user_staff.UserStaff{}, "Get", func(_ *user_staff.UserStaff, userId uint64) []string {
		return []string{"staff_id_1"}
	})
	getStaffPatch2 := gomonkey.ApplyFuncReturn(threat_history.GetStaff, &staff.Staff{Id: "staff_id_1"}, nil)
	sendWebhookPatch2 := gomonkey.ApplyFuncReturn(threat_history.SendWebhookMsg, nil)

	err = StatusOperation(&user.User{}, map[string]interface{}{
		"statusCode": 40,
	}, &gin.Context{}, &threat_history.OneThreatHistory{Status: poc.PocOperateOfUrge})
	for _, patch := range patches {
		patch.Reset()
	}
	userStaffPatch2.Reset()
	getStaffPatch2.Reset()
	sendWebhookPatch2.Reset()
	assert.Equal(t, err.Error(), "漏洞状态不符合操作类型")
}

// TestCheckDistribute 测试函数
func TestCheckDistribute(t *testing.T) {
	tests := []struct {
		name             string
		operationStatus  int
		pocStatus        int
		expectedErrorMsg string
	}{
		// 不支持的操作类型
		{"UnsupportedOperation", 99, poc.PocStatusOfNew, "不支持的操作类型"},

		// 派发操作符合的状态
		{"DispatchNew", poc.PocStatusOfBeRepair, poc.PocStatusOfNew, ""},
		{"DispatchStillExist", poc.PocStatusOfBeRepair, poc.PocStatusOfStillExist, ""},
		{"DispatchErrorReport", poc.PocStatusOfBeRepair, poc.PocStatusOfErrorReport, ""},
		{"DispatchCantRepaired", poc.PocStatusOfBeRepair, poc.PocStatusOfCantRepaired, ""},

		// 派发操作不符合的状态
		{"DispatchInvalidStatus", poc.PocStatusOfBeRepair, poc.PocStatusOfRepaired, "漏洞状态不符合操作类型"},

		// 转交操作符合的状态
		{"ForwardBeRepair", poc.PocStatusOfForward, poc.PocStatusOfBeRepair, ""},
		{"ForwardDelay", poc.PocStatusOfForward, poc.PocStatusOfDelay, ""},
		{"ForwardTimeout", poc.PocStatusOfForward, poc.PocStatusOfTimeout, ""},
		{"ForwardNoRepair", poc.PocStatusOfForward, poc.PocStatusOfNoRepair, ""},
		{"ForwardUrge", poc.PocStatusOfForward, poc.PocOperateOfUrge, ""},

		// 转交操作不符合的状态
		{"ForwardInvalidStatus", poc.PocStatusOfForward, poc.PocStatusOfRepaired, "漏洞状态不符合操作类型"},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			err := checkDistribute(test.operationStatus, test.pocStatus)
			if test.expectedErrorMsg == "" {
				assert.NoError(t, err)
			} else {
				assert.EqualError(t, err, test.expectedErrorMsg)
			}
		})
	}
}

// TestCheckStatusOperation 测试函数
func TestCheckStatusOperation(t *testing.T) {

	tests := []struct {
		name             string
		operationStatus  int
		pocStatus        int
		expectedErrorMsg string
	}{
		// 不支持的操作类型
		{"UnsupportedOperation", 99, poc.PocStatusOfNew, "漏洞不支持的操作类型"},

		// 延时操作符合的状态
		{"DelayBeRepair", poc.PocStatusOfDelay, poc.PocStatusOfBeRepair, ""},
		{"DelayForward", poc.PocStatusOfDelay, poc.PocStatusOfForward, ""},
		{"DelayUrge", poc.PocStatusOfDelay, poc.PocOperateOfUrge, ""},

		// 延时操作不符合的状态
		{"DelayInvalidStatus", poc.PocStatusOfDelay, poc.PocStatusOfRepaired, "漏洞状态不符合操作类型"},

		// 催促操作符合的状态
		{"UrgeBeRepair", poc.PocOperateOfUrge, poc.PocStatusOfBeRepair, ""},
		{"UrgeForward", poc.PocOperateOfUrge, poc.PocStatusOfForward, ""},

		// 催促操作不符合的状态
		{"UrgeInvalidStatus", poc.PocOperateOfUrge, poc.PocStatusOfRepaired, "漏洞状态不符合操作类型"},

		// 误报操作符合的状态
		{"ErrorReportNew", poc.PocStatusOfErrorReport, poc.PocStatusOfNew, ""},
		{"ErrorReportStillExist", poc.PocStatusOfErrorReport, poc.PocStatusOfStillExist, ""},

		// 误报操作不符合的状态
		{"ErrorReportInvalidStatus", poc.PocStatusOfErrorReport, poc.PocStatusOfRepaired, "漏洞状态不符合操作类型"},

		// 无法修复操作符合的状态
		{"CantRepairBeRepair", poc.PocStatusOfCantRepaired, poc.PocStatusOfBeRepair, ""},
		{"CantRepairForward", poc.PocStatusOfCantRepaired, poc.PocStatusOfForward, ""},

		// 无法修复操作不符合的状态
		{"CantRepairInvalidStatus", poc.PocStatusOfCantRepaired, poc.PocStatusOfRepaired, "漏洞状态不符合操作类型"},

		// 修复完成操作符合的状态
		{"WaitRetestStillExist", poc.PocStatusOfWaitRetest, poc.PocStatusOfStillExist, ""},

		// 修复完成操作不符合的状态
		{"WaitRetestInvalidStatus", poc.PocStatusOfWaitRetest, poc.PocStatusOfRepaired, "漏洞状态不符合操作类型"},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			err := CheckStatusOperation(test.operationStatus, test.pocStatus)
			if test.expectedErrorMsg == "" {
				assert.NoError(t, err)
			} else {
				assert.EqualError(t, errors.New("漏洞"+err.Error()), test.expectedErrorMsg)
			}
		})
	}
}
