package threat_center

import (
	"errors"
	pgidservice "fobrain/services/people_pgid"
	"github.com/spf13/cast"
	"slices"

	"fobrain/models/elastic/staff"
	"fobrain/models/mysql/user"
	"fobrain/models/mysql/user_staff"

	"github.com/gin-gonic/gin"
	"go-micro.dev/v4/logger"

	"fobrain/fobrain/app/repository/threat_history"
	"fobrain/models/elastic/poc"
	"fobrain/models/mysql/threat_tasks"
	"fobrain/pkg/utils"
)

// func Distribute(user *user.User, staff *staff.Staff, pocObj map[string]interface{}, ctx *gin.Context, oneThreatHistory *threat_history.OneThreatHistory) error {
// 	err := checkDistribute(oneThreatHistory.Status, pocObj["statusCode"].(int))
// 	if err != nil {
// 		return err
// 	}
// 	// 检查漏洞状态
// 	ok, msg := threat_history.CheckStatus(pocObj, oneThreatHistory.Status)
// 	if !ok {
// 		return errors.New(msg)
// 	}
// 	// 创建操作记录
// 	content := threat_history.GetOperateContent(user.Account, oneThreatHistory.Status)
// 	err = threat_history.CreateHistory(ctx, user, oneThreatHistory, staff, pocObj["statusCode"].(int), content)
// 	if err != nil {
// 		return err
// 	}
// 	// 发送邮件通知
// 	someThreatHistory := &threat_history.SomeThreatHistory{
// 		PocIds:            []string{oneThreatHistory.PocId},
// 		Descrition:        oneThreatHistory.Descrition,
// 		ToCc:              oneThreatHistory.ToCc,
// 		Status:            oneThreatHistory.Status,
// 		SendNotice:        oneThreatHistory.SendNotice,
// 		ToStaffId:         oneThreatHistory.ToStaffId,
// 		ToStaffName:       oneThreatHistory.ToStaffName,
// 		LimitDate:         oneThreatHistory.LimitDate,
// 		TimeoutNotice:     oneThreatHistory.TimeoutNotice,
// 		ExecNow:           oneThreatHistory.ExecNow,
// 		OriginalId:        oneThreatHistory.OriginalId,
// 		OperationType:     oneThreatHistory.OperationType,
// 		TimeoutReceiverId: oneThreatHistory.TimeoutReceiverId,
// 		TimeoutFrequency:  oneThreatHistory.TimeoutFrequency,
// 	}
// 	err = threat_history.SendNotice(ctx, someThreatHistory, staff)
// 	if err != nil {
// 		return err
// 	}
// 	err = threat_history.SendWebhookMsg(oneThreatHistory, staff, user, []map[string]interface{}{pocObj})
// 	if err != nil {
// 		return err
// 	}
// 	// 修改状态
// 	err = threat_history.UpdateThreatStatus(oneThreatHistory, pocObj, staff)
// 	if err != nil {
// 		return err
// 	}
// 	if oneThreatHistory.TimeoutNotice != 0 {
// 		// 超时通知，添加定时任务
// 		someThreatHistory := &threat_history.SomeThreatHistory{
// 			PocIds:            []string{oneThreatHistory.PocId},
// 			Descrition:        oneThreatHistory.Descrition,
// 			ToCc:              oneThreatHistory.ToCc,
// 			Status:            oneThreatHistory.Status,
// 			SendNotice:        oneThreatHistory.SendNotice,
// 			ToStaffId:         oneThreatHistory.ToStaffId,
// 			ToStaffName:       oneThreatHistory.ToStaffName,
// 			LimitDate:         oneThreatHistory.LimitDate,
// 			TimeoutNotice:     oneThreatHistory.TimeoutNotice,
// 			ExecNow:           oneThreatHistory.ExecNow,
// 			OriginalId:        oneThreatHistory.OriginalId,
// 			OperationType:     oneThreatHistory.OperationType,
// 			TimeoutReceiverId: oneThreatHistory.TimeoutReceiverId,
// 			TimeoutFrequency:  oneThreatHistory.TimeoutFrequency,
// 		}
// 		err = threat_history.CreateThreatTask(threat_tasks.TaskTypeTimeoutNotice, user, someThreatHistory, staff)
// 		if err != nil {
// 			return err
// 		}
// 	}

// 	return nil
// }

func DistributeMutil(user *user.User, staff *staff.Staff, ctx *gin.Context, threathHistorys *threat_history.SomeThreatHistory, pocObjs []map[string]interface{}, ccStaff *staff.Staff) error {
	for _, pocObj := range pocObjs {
		statusCode := cast.ToInt(pocObj["statusCode"])
		err := checkDistribute(threathHistorys.Status, statusCode)
		if err != nil {
			return err
		}
		// 检查漏洞状态

		ok, msg := threat_history.CheckStatus(statusCode, threathHistorys.Status)
		if !ok {
			return errors.New(msg)
		}

		// 构造单个漏洞的OneThreatHistory对象，用于记录
		id := cast.ToString(pocObj["id"])
		oneThreatHistory := &threat_history.OneThreatHistory{
			PocId:         id,
			ToStaffId:     threathHistorys.ToStaffId,
			ToStaffName:   threathHistorys.ToStaffName,
			LimitDate:     threathHistorys.LimitDate,
			SendNotice:    threathHistorys.SendNotice,
			TimeoutNotice: threathHistorys.TimeoutNotice,
			Descrition:    threathHistorys.Descrition,
			ToCc:          threathHistorys.ToCc,
			Status:        threathHistorys.Status,
			ExecNow:       threathHistorys.ExecNow,
			OriginalId:    threathHistorys.OriginalId,
			OperationType: threathHistorys.OperationType,
		}
		// 创建操作记录
		pgid, _ := pgidservice.GetPgidById(staff.Id)
		content := threat_history.GetOperateContent(user.Account, threathHistorys.Status, "漏洞", pgid)
		err = threat_history.CreateHistory(ctx, user, oneThreatHistory, staff, pocObj["statusCode"].(int), content)
		if err != nil {
			return err
		}
		// 发送站内消息通知
		err = threat_history.SendWebhookMsg(oneThreatHistory, staff, user, []map[string]interface{}{pocObj})
		if err != nil {
			return err
		}
		// 设置修复时间要求,修改状态时需要发送到队列中
		pocObj["limit_date"] = threathHistorys.LimitDate
		// 修改状态
		err = threat_history.UpdateThreatStatus(oneThreatHistory, pocObj, staff, ccStaff)
		if err != nil {
			return err
		}
	}
	// 发送邮件通知
	err := threat_history.SendNotice(ctx, threathHistorys, staff)
	if err != nil {
		return err
	}
	// 超时通知，添加定时任务
	if threathHistorys.TimeoutNotice != 0 {
		err := threat_history.CreateThreatTask(threat_tasks.TaskTypeTimeoutNotice, user, threathHistorys, staff)
		if err != nil {
			return err
		}
	}

	return nil
}

// StatusOperation 批量延时、催促(无状态修改)、误报、无法修复、修复完成
func StatusOperation(user *user.User, pocObj map[string]interface{}, ctx *gin.Context, oneThreatHistory *threat_history.OneThreatHistory) error {
	// 检查操作状态
	statusCode := cast.ToInt(pocObj["statusCode"])
	err := CheckStatusOperation(oneThreatHistory.Status, statusCode)
	if err != nil {
		return errors.New("漏洞" + err.Error())
	}
	// 检查漏洞状态
	ok, msg := threat_history.CheckStatus(statusCode, oneThreatHistory.Status)
	if !ok {
		return errors.New(msg)
	}
	// 催促 发送站内消息通知
	if oneThreatHistory.Status == poc.PocOperateOfUrge {
		err = threat_history.SendLocalNotice(pocObj)
		if err != nil {
			return err
		}
		return nil
	}
	content := threat_history.GetOperateContent(user.Account, oneThreatHistory.Status, "漏洞")
	// 创建操作记录
	err = threat_history.CreateHistory(ctx, user, oneThreatHistory, nil, pocObj["statusCode"].(int), content)
	if err != nil {
		return err
	}
	staffs := user_staff.NewUserRoleModel().Get(user.Id)
	if len(staffs) > 0 {
		staff, err := threat_history.GetStaff(staffs[0])
		if err == nil && staff != nil {
			threat_history.SendWebhookMsg(oneThreatHistory, staff, user, []map[string]interface{}{pocObj})
		} else {
			logger.Warn("获取漏洞修复负责人信息失败,漏洞消息webhook发送失败", user.Id)
		}
	} else {
		logger.Warn("未获取到users_staffs,漏洞消息webhook发送失败", user.Id)
	}
	// 修改状态
	err = threat_history.UpdateThreatStatus(oneThreatHistory, pocObj, nil, nil)
	if err != nil {
		return err
	}

	return nil
}
func CheckStatusOperation(operationStatus int, pocStatus int) error {
	// 支持的操作类型
	allowedStatus := []int{
		poc.PocStatusOfDelay,        // 12,延时
		poc.PocOperateOfUrge,        // 16,催促
		poc.PocStatusOfErrorReport,  // 40,误报
		poc.PocStatusOfCantRepaired, // 41,无法修复
		poc.PocStatusOfWaitRetest,   // 17,待复测
	}
	//检查漏洞是否符合操作类型
	if !slices.Contains(allowedStatus, operationStatus) {
		return errors.New("不支持的操作类型")
	}

	notInLineErr := errors.New("状态不符合操作类型")
	//校验操作类型 延时仅支持：
	if operationStatus == poc.PocStatusOfDelay {
		//操作的是派发不在符合的状态范围内，返回失败
		if !utils.InArray(pocStatus, []int{poc.PocStatusOfBeRepair, poc.PocStatusOfForward, poc.PocStatusOfDelay, poc.PocStatusOfTimeout, poc.PocStatusOfNoRepair, poc.PocOperateOfUrge}) {
			return notInLineErr
		}
	}
	//催促仅支持：
	if operationStatus == poc.PocOperateOfUrge {
		//操作的是派发不在符合的状态范围内，返回失败
		if !utils.InArray(pocStatus, []int{poc.PocStatusOfBeRepair, poc.PocStatusOfForward, poc.PocStatusOfDelay, poc.PocStatusOfTimeout, poc.PocStatusOfNoRepair, poc.PocOperateOfUrge}) {
			return notInLineErr
		}
	}
	//误报仅支持：
	if operationStatus == poc.PocStatusOfErrorReport {
		//操作的是派发不在符合的状态范围内，返回失败
		if !utils.InArray(pocStatus, []int{poc.PocStatusOfBeRepair, poc.PocStatusOfForward, poc.PocStatusOfDelay, poc.PocStatusOfTimeout, poc.PocStatusOfNoRepair, poc.PocOperateOfUrge, poc.PocStatusOfNew, poc.PocStatusOfStillExist}) {
			return notInLineErr
		}
	}

	//无法修复仅支持：
	if operationStatus == poc.PocStatusOfCantRepaired {
		//操作的是派发不在符合的状态范围内，返回失败
		if !utils.InArray(pocStatus, []int{poc.PocStatusOfBeRepair, poc.PocStatusOfForward, poc.PocStatusOfDelay, poc.PocStatusOfTimeout, poc.PocStatusOfNoRepair, poc.PocOperateOfUrge, poc.PocStatusOfNew, poc.PocStatusOfStillExist}) {
			return notInLineErr
		}
	}
	//修复完成仅支持：
	if operationStatus == poc.PocStatusOfWaitRetest {
		//操作的是派发不在符合的状态范围内，返回失败
		if !utils.InArray(pocStatus, []int{poc.PocStatusOfBeRepair, poc.PocStatusOfForward, poc.PocStatusOfDelay, poc.PocStatusOfTimeout, poc.PocStatusOfNoRepair, poc.PocOperateOfUrge, poc.PocStatusOfStillExist}) {
			return notInLineErr
		}
	}
	return nil
}
func checkDistribute(operationStatus int, pocStatus int) error {
	//检查漏洞是否符合操作类型
	if operationStatus != poc.PocStatusOfBeRepair && operationStatus != poc.PocStatusOfForward {
		return errors.New("不支持的操作类型")
	}

	//校验操作类型 派发仅支持：
	if operationStatus == poc.PocStatusOfBeRepair {
		//操作的是派发不在符合的状态范围内，返回失败
		if !utils.InArray(pocStatus, []int{poc.PocStatusOfNew, poc.PocStatusOfStillExist, poc.PocStatusOfErrorReport, poc.PocStatusOfCantRepaired}) {
			return errors.New("漏洞状态不符合操作类型")
		}
	}

	//校验操作类型 转交仅支持：
	if operationStatus == poc.PocStatusOfForward {
		//操作的是派发不在符合的状态范围内，返回失败
		if !utils.InArray(pocStatus, []int{poc.PocStatusOfBeRepair, poc.PocStatusOfForward, poc.PocStatusOfDelay, poc.PocStatusOfTimeout, poc.PocStatusOfNoRepair, poc.PocOperateOfUrge}) {
			return errors.New("漏洞状态不符合操作类型")
		}
	}
	return nil
}
