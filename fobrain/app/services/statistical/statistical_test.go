package statistical

import (
	"encoding/json"
	"fobrain/fobrain/app/repository/asset"
	"fobrain/fobrain/app/request/statistical"
	testcommon "fobrain/fobrain/tests/common_test"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/olivere/elastic/v7"
	"github.com/stretchr/testify/assert"
)

// 初始化mock server
var mockServer *testcommon.MockServer

func TestMain(m *testing.M) {
	mockServer = testcommon.NewMockServer()
	defer mockServer.Close()
	m.Run()
}

// TestGetList 测试 GetList 方法
func TestGetList(t *testing.T) {
	service := &Service{
		fieldHandlers: map[string]func(keyword string) *searchField{
			"business": func(keyword string) *searchField {
				return &searchField{
					keyword:    keyword,
					field:      "business.system",
					nestedPath: "business",
					size:       10000,
					name:       "业务系统",
				}
			},
		},
		assembler: map[string]func([]*asset.AssetTopResult) (treeOrList, []*statistical.TreeResponse, error){
			"business": func(data []*asset.AssetTopResult) (treeOrList, []*statistical.TreeResponse, error) {
				var response []*statistical.TreeResponse
				for _, d := range data {
					response = append(response, &statistical.TreeResponse{
						Name:       d.FieldValue,
						AssetNum:   d.FieldCount,
						QueryKey:   "name",
						QueryField: "business_app",
					})
				}
				return List, response, nil
			},
		},
	}
	mockServer.Register("asset/_search", []*elastic.SearchHit{
		{
			Id:     "1",
			Source: []byte(`{"ip":"***********", "updated_at":"2023-04-25 12:34:56", "created_at":"2023-04-25 12:34:56"}`),
		},
	})
	// 创建一个参数请求
	params := &statistical.TreeRequest{
		Type:    []string{"business"},
		Keyword: "",
	}

	// 调用 GetList 方法
	result, err := service.GetList(params)
	// 验证没有错误
	assert.NoError(t, err)

	// 验证返回的结果是否符合预期
	assert.Equal(t, result[0]["type_name"], "业务系统")
}

// TestGetCountByFields 测试 GetCountByFields 方法
func TestGetCountByFields(t *testing.T) {
	service := &Service{
		fieldHandlers: map[string]func(keyword string) *searchField{
			"business": func(keyword string) *searchField {
				return &searchField{
					keyword:    keyword,
					field:      "business.system",
					nestedPath: "business",
					size:       10000,
					name:       "业务系统",
				}
			},
			"ip_type": func(keyword string) *searchField {
				return &searchField{
					keyword:    keyword,
					field:      "ip_type",
					nestedPath: "",
					value:      "",
					size:       5,
					name:       "关联资产状态",
					desc: map[string]string{
						"1": "在线",
						"2": "离线",
					},
				}
			},
			"cmdb_coverage": func(keyword string) *searchField {
				return &searchField{
					keyword:    keyword,
					field:      "1",
					nestedPath: "",
					size:       0,
					name:       "CMDB覆盖率",
				}
			},
			"business_oper_num": func(keyword string) *searchField {
				return &searchField{
					keyword:    keyword,
					field:      "business.owner",
					nestedPath: "business",
					size:       10000,
					name:       "业务系统负责人",
				}
			},
			//"business_tag": func(keyword string) *searchField {
			//	return &searchField{
			//		name: "系统基本属性",
			//	}
			//},
		},
	}
	mock := testcommon.GetMysqlMock()
	defer mock.Close()
	// 模拟查询 data_source.AllSources
	mock.ExpectQuery("SELECT * FROM `data_source_types` WHERE `name` IN (?,?,?,?,?)").WillReturnRows(
		sqlmock.NewRows([]string{"id", "name"}).
			AddRow(1, "CMDB").
			AddRow(2, "堡垒机").
			AddRow(3, "防火墙").
			AddRow(4, "主机安全").
			AddRow(5, "资产扫描"),
	)

	// 模拟查询 data_source_type_map
	mock.ExpectQuery("SELECT * FROM `data_source_type_map` WHERE source_type_id IN (?,?,?,?,?)").
		WithArgs(1, 2, 3, 4, 5).
		WillReturnRows(
			sqlmock.NewRows([]string{"source_type_id", "map_field"}).
				AddRow(1, "Map1").
				AddRow(2, "Map2").
				AddRow(3, "Map3").
				AddRow(4, "Map4").
				AddRow(5, "Map5"),
		)
	mockServer.Register("asset/_search", []*elastic.SearchHit{
		{
			Id: "1",
			Source: []byte(`{
    "id": "ed177d019de243f188c6c7fedc38933d",
    "fid": "*************:1",
    "fid_hash": "bf083d24ee60fc4ecd34f4b1600ca09c",
    "area": 1,
    "process_ids": [
        "435_586_19_1_*************"
    ],
    "source_ids": [
        2
    ],
    "node_ids": [
        19
    ],
    "asset_task_ids": [
        "435_19_1_*************"
    ],
    "all_source_ids": [
        2
    ],
    "all_node_ids": [
        19
    ],
    "all_asset_task_ids": [
        "435_19_1_*************"
    ],
    "all_process_ids": [
        "435_586_19_1_*************"
    ],
    "ip": "*************",
    "ip_type": 1,
    "ip_segment": null,
    "ip_segment_source": {
        "2-19": ""
    },
    "hostname": null,
    "hostname_source": {
        "2-19": ""
    },
    "eth_name": null,
    "eth_name_source": {
        "2-19": ""
    },
    "os": null,
    "os_source": {
        "2-19": ""
    },
    "kernel": null,
    "kernel_source": {
        "2-19": ""
    },
    "model": null,
    "model_source": {
        "2-19": ""
    },
    "maker": null,
    "maker_source": {
        "2-19": ""
    },
    "sn": null,
    "sn_source": {
        "2-19": ""
    },
    "mac": null,
    "mac_source": {
        "2-19": ""
    },
    "product": [
        "NGINX",
        "digicert-Cert",
        "GeoTrust-Cert"
    ],
    "product_source": {
        "2-19": "NGINX, digicert-Cert, GeoTrust-Cert"
    },
    "business": [
            {
                "system": "test_app",
                "system_id": "",
                "owner": "",
                "owner_id": "",
                "source": "original",
                "business_info": null,
                "addition": ""
            }
        ],
    "business_source": {
        "2-19": [
            {
                "system": "",
                "system_id": "",
                "owner": "",
                "owner_id": "",
                "source": "original",
                "business_info": null,
                "addition": ""
            }
        ]
    },
    "business_system_source": {},
    "business_owner_source": {},
    "oper": null,
    "oper_source": {
        "2-19": ""
    },
    "machine_room": null,
    "machine_room_source": {
        "2-19": ""
    },
    "status": 1,
    "status_source": {
        "2-19": 1
    },
    "ports": [
        {
            "port": 5222,
            "protocol": "xmpp",
            "status": 0,
            "url": "*************:5222",
            "domain": "",
            "title": ""
        },
        {
            "port": 80,
            "protocol": "http",
            "status": 0,
            "url": "http://man.js.sgcc.com.cn",
            "domain": "man.js.sgcc.com.cn",
            "title": ""
        },
        {
            "port": 10443,
            "protocol": "https",
            "status": 0,
            "url": "https://man.js.sgcc.com.cn:10443",
            "domain": "man.js.sgcc.com.cn",
            "title": ""
        },
        {
            "port": 10443,
            "protocol": "https",
            "status": 0,
            "url": "https://mam.js.sgcc.com.cn:10443",
            "domain": "mam.js.sgcc.com.cn",
            "title": ""
        },
        {
            "port": 80,
            "protocol": "http",
            "status": 0,
            "url": "http://mam.js.sgcc.com.cn",
            "domain": "mam.js.sgcc.com.cn",
            "title": ""
        },
        {
            "port": 10443,
            "protocol": "https",
            "status": 0,
            "url": "https://mam.js.sgcc.cn:10443",
            "domain": "mam.js.sgcc.cn",
            "title": ""
        },
        {
            "port": 443,
            "protocol": "https",
            "status": 0,
            "url": "https://*************",
            "domain": "",
            "title": ""
        },
        {
            "port": 10443,
            "protocol": "https",
            "status": 0,
            "url": "https://mamjs.sgcc.com.cn:10443",
            "domain": "mamjs.sgcc.com.cn",
            "title": ""
        },
        {
            "port": 10443,
            "protocol": "https",
            "status": 0,
            "url": "https://*************:10443",
            "domain": "",
            "title": ""
        },
        {
            "port": 80,
            "protocol": "http",
            "status": 0,
            "url": "http://*************",
            "domain": "",
            "title": ""
        }
    ],
    "ports_source": {
        "2-19": [
            {
                "port": 5222,
                "protocol": "xmpp",
                "status": 0,
                "url": "*************:5222",
                "domain": "",
                "title": ""
            },
            {
                "port": 80,
                "protocol": "http",
                "status": 0,
                "url": "http://man.js.sgcc.com.cn",
                "domain": "man.js.sgcc.com.cn",
                "title": ""
            },
            {
                "port": 10443,
                "protocol": "https",
                "status": 0,
                "url": "https://man.js.sgcc.com.cn:10443",
                "domain": "man.js.sgcc.com.cn",
                "title": ""
            },
            {
                "port": 10443,
                "protocol": "https",
                "status": 0,
                "url": "https://mam.js.sgcc.com.cn:10443",
                "domain": "mam.js.sgcc.com.cn",
                "title": ""
            },
            {
                "port": 80,
                "protocol": "http",
                "status": 0,
                "url": "http://mam.js.sgcc.com.cn",
                "domain": "mam.js.sgcc.com.cn",
                "title": ""
            },
            {
                "port": 10443,
                "protocol": "https",
                "status": 0,
                "url": "https://mam.js.sgcc.cn:10443",
                "domain": "mam.js.sgcc.cn",
                "title": ""
            },
            {
                "port": 443,
                "protocol": "https",
                "status": 0,
                "url": "https://*************",
                "domain": "",
                "title": ""
            },
            {
                "port": 10443,
                "protocol": "https",
                "status": 0,
                "url": "https://mamjs.sgcc.com.cn:10443",
                "domain": "mamjs.sgcc.com.cn",
                "title": ""
            },
            {
                "port": 10443,
                "protocol": "https",
                "status": 0,
                "url": "https://*************:10443",
                "domain": "",
                "title": ""
            },
            {
                "port": 80,
                "protocol": "http",
                "status": 0,
                "url": "http://*************",
                "domain": "",
                "title": ""
            }
        ]
    },
    "memory_size": null,
    "memory_size_source": {
        "2-19": ""
    },
    "memory_usage_rate": null,
    "memory_usage_rate_source": {
        "2-19": ""
    },
    "cpu_maker": null,
    "cpu_maker_source": {
        "2-19": ""
    },
    "cpu_brand": null,
    "cpu_brand_source": {
        "2-19": ""
    },
    "cpu_count": null,
    "cpu_count_source": {
        "2-19": 0
    },
    "disk_count": null,
    "disk_count_source": {
        "2-19": 0
    },
    "disk_size": null,
    "disk_size_source": {
        "2-19": 0
    },
    "disk_usage_rate": null,
    "disk_usage_rate_source": {
        "2-19": ""
    },
    "load_average": null,
    "load_average_source": {
        "2-19": ""
    },
    "network_type": 2,
    "fusion_rules": null,
    "deleted_at": null,
    "purged_at": null,
    "created_at": "2024-11-20 14:27:22",
    "updated_at": "2024-11-20 14:27:22",
    "is_device_extracted": 1,
    "merge_count": 1,
    "person_limit": null,
    "person_limit_hash": null,
    "tag": null
}`),
		},
	})
	mockServer.Register("/asset/_count", elastic.CountResponse{
		Count:           1,
		TerminatedEarly: false,
		Shards: &elastic.ShardsInfo{
			Total:      1,
			Successful: 1,
			Skipped:    0,
			Failed:     0,
		},
	})
	// 创建统计请求参数
	params := &statistical.CountRequest{
		Params: []*statistical.CountParams{
			{
				Field: "business",
				Type:  "top",
			},
			{
				Field: "ip_type",
				Type:  "pie",
			},
			{
				Field: "cmdb_coverage",
				Type:  "coverage",
			},
			//{
			//	Field: "business_oper_num",
			//	Type:  "total",
			//},
			//{
			//	Field: "business_tag",
			//	Type:  "tag",
			//},
		},
		BusinessApp: "test_app",
	}
	mockServer.Register("asset/_search", []*elastic.SearchHit{
		{
			Id:     "1",
			Source: []byte(`{"ip":"***********", "updated_at":"2023-04-25 12:34:56", "created_at":"2023-04-25 12:34:56"}`),
		},
	})
	// 调用 GetCountByFields 方法
	result, err := service.GetCountByFields(params)

	// 验证没有错误
	assert.NoError(t, err)

	// 验证返回的结果
	assert.NotNil(t, result)
}

// TestAssembleBusinessResponse 测试业务系统结果组装方法
func TestAssembleBusinessResponse(t *testing.T) {
	// 创建服务实例
	service := NewService()

	// 准备测试数据
	testData := []*asset.AssetTopResult{
		{
			FieldValue: "测试业务系统1",
			FieldCount: 10,
			Children: []*asset.AssetTopResult{
				{
					FieldValue: "子系统1",
					FieldCount: 5,
				},
				{
					FieldValue: "子系统2",
					FieldCount: 5,
				},
			},
		},
		{
			FieldValue: "测试业务系统2",
			FieldCount: 5,
			Children:   nil,
		},
	}

	// 调用被测试方法
	resultType, resultData, err := service.assembleBusinessResponse(testData)

	// 验证结果
	assert.NoError(t, err, "应该没有错误")
	assert.Equal(t, List, resultType, "结果类型应该是列表结构")
	assert.Equal(t, 2, len(resultData), "应该有两个顶级节点")

	// 验证第一个业务系统的数据
	assert.Equal(t, "测试业务系统1", resultData[0].Name, "第一个业务系统名称不匹配")
	assert.Equal(t, int64(10), resultData[0].AssetNum, "第一个业务系统资产数量不匹配")
	assert.Equal(t, "business.system", resultData[0].QueryField, "第一个业务系统查询字段不匹配")
	assert.Equal(t, "name", resultData[0].QueryKey, "第一个业务系统查询键不匹配")

	// 验证第二个业务系统的数据
	assert.Equal(t, "测试业务系统2", resultData[1].Name, "第二个业务系统名称不匹配")
	assert.Equal(t, int64(5), resultData[1].AssetNum, "第二个业务系统资产数量不匹配")
}

// TestAssembleDepartmentResponse 测试部门结果组装方法
func TestAssembleDepartmentResponse(t *testing.T) {
	// 创建服务实例
	service := NewService()

	// 准备测试数据 - 使用路径格式的部门数据
	testData := []*asset.AssetTopResult{
		{
			FieldValue: "总部/技术部/研发中心",
			FieldCount: 15,
		},
		{
			FieldValue: "总部/技术部/测试中心",
			FieldCount: 10,
		},
		{
			FieldValue: "总部/市场部",
			FieldCount: 5,
		},
	}

	// 调用被测试方法
	resultType, resultData, err := service.assembleDepartmentResponse(testData)

	// 验证结果
	assert.NoError(t, err, "应该没有错误")
	assert.Equal(t, Tree, resultType, "结果类型应该是树形结构")

	// 验证顶级节点
	assert.Equal(t, 1, len(resultData), "应该只有一个顶级节点")
	assert.Equal(t, "总部", resultData[0].Name, "顶级部门名称不匹配")

	// 验证二级部门
	assert.Equal(t, 2, len(resultData[0].Children), "总部下应该有两个部门")

	// 验证技术部及其子部门
	techDept := findChildByName(resultData[0].Children, "技术部")
	assert.NotNil(t, techDept, "应该存在技术部")
	assert.Equal(t, 2, len(techDept.Children), "技术部下应该有两个中心")

	// 验证研发中心
	rdCenter := findChildByName(techDept.Children, "研发中心")
	assert.NotNil(t, rdCenter, "应该存在研发中心")
	assert.Equal(t, int64(15), rdCenter.AssetNum, "研发中心资产数量不匹配")
	assert.Equal(t, "business_department.name.keyword", rdCenter.QueryField, "查询字段不匹配")

	// 验证市场部
	marketDept := findChildByName(resultData[0].Children, "市场部")
	assert.NotNil(t, marketDept, "应该存在市场部")
	assert.Equal(t, int64(5), marketDept.AssetNum, "市场部资产数量不匹配")
}

// findChildByName 辅助函数，根据名称查找子节点
func findChildByName(children []*statistical.TreeResponse, name string) *statistical.TreeResponse {
	for _, child := range children {
		if child.Name == name {
			return child
		}
	}
	return nil
}

// TestAssembleRuleInfosResponse 测试组件类型结果组装方法
func TestAssembleRuleInfosResponse(t *testing.T) {
	// 创建服务实例
	service := NewService()

	// 准备测试数据 - 一级和二级标签
	testData := []*asset.AssetTopResult{
		{
			FieldValue: "操作系统",
			FieldCount: 20,
			Children: []*asset.AssetTopResult{
				{
					FieldValue: "Linux",
					FieldCount: 12,
				},
				{
					FieldValue: "Windows",
					FieldCount: 8,
				},
			},
		},
		{
			FieldValue: "数据库",
			FieldCount: 15,
			Children: []*asset.AssetTopResult{
				{
					FieldValue: "MySQL",
					FieldCount: 8,
				},
				{
					FieldValue: "MongoDB",
					FieldCount: 4,
				},
				{
					FieldValue: "Redis",
					FieldCount: 3,
				},
			},
		},
	}

	// 调用被测试方法
	resultType, resultData, err := service.assembleRuleInfosResponse(testData)

	// 验证结果
	assert.NoError(t, err, "应该没有错误")
	assert.Equal(t, Tree, resultType, "结果类型应该是树形结构")
	assert.Equal(t, 2, len(resultData), "应该有两个顶级节点")

	// 验证操作系统节点
	osNode := resultData[0]
	assert.Equal(t, "操作系统", osNode.Name, "第一个节点名称不匹配")
	assert.Equal(t, "操作系统", osNode.Id, "第一个节点ID不匹配")
	assert.Equal(t, int64(20), osNode.AssetNum, "第一个节点资产数量不匹配")
	assert.Equal(t, "rule_infos.first_tag", osNode.QueryField, "第一个节点查询字段不匹配")
	assert.Equal(t, 2, len(osNode.Children), "操作系统下应该有两个子节点")

	// 验证Linux子节点
	linuxNode := osNode.Children[0]
	assert.Equal(t, "Linux", linuxNode.Name, "Linux节点名称不匹配")
	assert.Equal(t, int64(12), linuxNode.AssetNum, "Linux节点资产数量不匹配")
	assert.Equal(t, "rule_infos.second_tag", linuxNode.QueryField, "Linux节点查询字段不匹配")

	// 验证数据库节点
	dbNode := resultData[1]
	assert.Equal(t, "数据库", dbNode.Name, "第二个节点名称不匹配")
	assert.Equal(t, int64(15), dbNode.AssetNum, "第二个节点资产数量不匹配")
	assert.Equal(t, 3, len(dbNode.Children), "数据库下应该有三个子节点")

	// 验证MySQL子节点
	mysqlNode := dbNode.Children[0]
	assert.Equal(t, "MySQL", mysqlNode.Name, "MySQL节点名称不匹配")
	assert.Equal(t, int64(8), mysqlNode.AssetNum, "MySQL节点资产数量不匹配")
}

// TestBuildMustExistsQuery 测试构建必须存在的字段查询方法
func TestBuildMustExistsQuery(t *testing.T) {
	// 创建服务实例
	service := NewService()

	// 测试用例1：测试嵌套字段
	t.Run("嵌套字段查询", func(t *testing.T) {
		// 准备测试数据
		mustAttr := []string{"business", "department"}

		// 调用被测试方法
		query, err := service.buildMustExistsQuery(mustAttr)

		// 验证结果
		assert.NoError(t, err, "应该没有错误")
		assert.NotNil(t, query, "查询不应为空")

		// 验证查询结构
		// 直接检查类型是否为 BoolQuery
		assert.NotNil(t, query, "查询不应为空")

		// 注：由于elastic查询结构的内部字段不易直接访问，
		// 这里主要验证查询对象是否正确创建，而不验证具体的查询条件
	})

	// 测试用例2：测试普通字段
	t.Run("普通字段查询", func(t *testing.T) {
		// 准备测试数据
		mustAttr := []string{"ip"}

		// 调用被测试方法
		query, err := service.buildMustExistsQuery(mustAttr)

		// 验证结果
		assert.NoError(t, err, "应该没有错误")
		assert.NotNil(t, query, "查询不应为空")
	})

	// 测试用例3：测试空输入
	t.Run("空输入", func(t *testing.T) {
		// 准备测试数据
		var mustAttr []string

		// 调用被测试方法
		query, err := service.buildMustExistsQuery(mustAttr)

		// 验证结果
		assert.NoError(t, err, "应该没有错误")
		assert.NotNil(t, query, "查询不应为空")
	})
}

// TestBuildSearchConditionQuery 测试根据搜索条件构建查询方法
func TestBuildSearchConditionQuery(t *testing.T) {
	// 创建服务实例
	service := NewService()

	// 测试用例1：有效的搜索条件
	t.Run("有效搜索条件", func(t *testing.T) {
		// 准备测试数据 - 使用JSON格式
		searchCondition := []string{`{"field":"ip","operation_type_string":"=","condition":"and","ip":"********"}`}
		baseQuery := elastic.NewBoolQuery()

		// 调用被测试方法
		resultQuery, err := service.buildSearchConditionQuery(searchCondition, baseQuery)

		// 验证结果
		assert.NoError(t, err, "应该没有错误")
		assert.NotNil(t, resultQuery, "查询不应为空")
	})

	// 测试用例2：多个搜索条件
	t.Run("多个搜索条件", func(t *testing.T) {
		// 准备测试数据 - 使用JSON格式
		searchCondition := []string{
			`{"field":"ip","operation_type_string":"=","condition":"and","ip":"********"}`,
			`{"field":"port","operation_type_string":"=","condition":"and","port":["80"]}`,
		}
		baseQuery := elastic.NewBoolQuery()

		// 调用被测试方法
		resultQuery, err := service.buildSearchConditionQuery(searchCondition, baseQuery)

		// 验证结果
		assert.NoError(t, err, "应该没有错误")
		assert.NotNil(t, resultQuery, "查询不应为空")
	})

	// 测试用例4：空搜索条件
	t.Run("空搜索条件", func(t *testing.T) {
		// 准备测试数据
		var searchCondition []string
		baseQuery := elastic.NewBoolQuery()

		// 调用被测试方法
		resultQuery, err := service.buildSearchConditionQuery(searchCondition, baseQuery)

		// 验证结果
		assert.NoError(t, err, "应该没有错误")
		assert.NotNil(t, resultQuery, "查询不应为空")
		assert.Equal(t, baseQuery, resultQuery, "应该返回原始查询")
	})
}

// TestProcessPieOrTopData 测试处理饼图或排行榜数据方法
func TestProcessPieOrTopData(t *testing.T) {
	// 创建服务实例
	service := NewService()

	// 准备测试数据
	field := "business"
	queryFields := &searchField{
		keyword:    "",
		field:      "business.system",
		nestedPath: "business",
		size:       10,
		name:       "业务系统",
		param:      "business",
	}
	params := &statistical.CountRequest{
		Params: []*statistical.CountParams{
			{
				Field: "business",
				Type:  "pie",
			},
		},
	}
	additionQueries := []elastic.Query{elastic.NewMatchAllQuery()}

	// 注册mock响应 - 包含聚合结果
	// 创建模拟的聚合结果
	// 使用 gomonkey 模拟 parseAggregationResult 方法
	patch := gomonkey.ApplyFunc(asset.AssetTopProcess, func(config asset.AssetTopConfig) ([]*asset.AssetTopResult, error) {
		// 返回模拟的聚合结果
		return []*asset.AssetTopResult{
			{
				FieldValue: "测试系统",
				FieldCount: 10,
			},
			{
				FieldValue: "其他系统",
				FieldCount: 5,
			},
		}, nil
	})
	defer patch.Reset()

	// 测试用例1：饼图模式
	t.Run("饼图模式", func(t *testing.T) {
		// 调用被测试方法
		result, err := service.processPieOrTopData(field, "pie", queryFields, params, additionQueries)

		// 验证结果
		assert.NoError(t, err, "应该没有错误")
		assert.NotNil(t, result, "结果不应为空")
	})

	// 测试用例2：排行榜模式
	t.Run("排行榜模式", func(t *testing.T) {
		// 保存原始大小
		originalSize := queryFields.size

		// 调用被测试方法
		result, err := service.processPieOrTopData(field, "top", queryFields, params, additionQueries)

		// 验证结果
		assert.NoError(t, err, "应该没有错误")
		assert.NotNil(t, result, "结果不应为空")

		// 验证排行榜模式下的大小限制
		assert.Equal(t, 5, queryFields.size, "排行榜模式应限制大小为5")

		// 恢复原始大小
		queryFields.size = originalSize
	})
}

// TestGetFieldHandler 测试获取字段处理器方法
func TestGetFieldHandler(t *testing.T) {
	// 创建服务实例
	service := NewService()

	// 测试用例1：获取已注册的字段处理器
	t.Run("获取已注册的字段处理器", func(t *testing.T) {
		// 准备测试数据
		fieldName := "business"
		keyword := "test"

		// 调用被测试方法
		handler, exists := service.fieldHandlers[fieldName]
		assert.True(t, exists, "字段处理器应该存在")

		// 使用处理器
		result := handler(keyword)

		// 验证结果
		assert.NotNil(t, result, "处理器结果不应为空")
		assert.Equal(t, "业务系统", result.name, "名称应该匹配")
		assert.Equal(t, "business.system", result.field, "字段应该匹配")
		assert.Equal(t, "business", result.nestedPath, "嵌套路径应该匹配")
		assert.Equal(t, keyword, result.keyword, "关键词应该匹配")
	})

	// 测试用例2：获取不存在的字段处理器
	t.Run("获取不存在的字段处理器", func(t *testing.T) {
		// 准备测试数据
		fieldName := "non_existent_field"

		// 调用被测试方法
		_, exists := service.fieldHandlers[fieldName]

		// 验证结果
		assert.False(t, exists, "字段处理器不应存在")
	})

	// 测试用例3：测试多个字段处理器
	t.Run("测试多个字段处理器", func(t *testing.T) {
		// 准备测试数据
		fieldNames := []string{"business", "department", "ip_type", "network_type"}

		// 验证所有字段处理器都已注册
		for _, name := range fieldNames {
			_, exists := service.fieldHandlers[name]
			assert.True(t, exists, "字段处理器 %s 应该存在", name)
		}
	})
}

// TestRegisterFieldHandler 测试注册字段处理器方法
func TestRegisterFieldHandler(t *testing.T) {
	// 创建服务实例
	service := &Service{
		fieldHandlers: make(map[string]func(keyword string) *searchField),
		assembler:     make(map[string]func([]*asset.AssetTopResult) (treeOrList, []*statistical.TreeResponse, error)),
	}

	// 测试用例：注册新的字段处理器
	t.Run("注册新的字段处理器", func(t *testing.T) {
		// 准备测试数据
		fieldName := "test_field"
		handler := func(keyword string) *searchField {
			return &searchField{
				keyword:    keyword,
				field:      "test.field",
				nestedPath: "test",
				size:       100,
				name:       "测试字段",
			}
		}

		// 调用被测试方法
		service.registerFieldHandler(fieldName, handler)

		// 验证结果
		registeredHandler, exists := service.fieldHandlers[fieldName]
		assert.True(t, exists, "字段处理器应该已注册")

		// 使用注册的处理器
		result := registeredHandler("test_keyword")
		assert.Equal(t, "测试字段", result.name, "名称应该匹配")
		assert.Equal(t, "test.field", result.field, "字段应该匹配")
		assert.Equal(t, "test", result.nestedPath, "嵌套路径应该匹配")
		assert.Equal(t, "test_keyword", result.keyword, "关键词应该匹配")
	})
}

// TestRegisterAssembler 测试注册结果组装器方法
func TestRegisterAssembler(t *testing.T) {
	// 创建服务实例
	service := &Service{
		fieldHandlers: make(map[string]func(keyword string) *searchField),
		assembler:     make(map[string]func([]*asset.AssetTopResult) (treeOrList, []*statistical.TreeResponse, error)),
	}

	// 测试用例：注册新的结果组装器
	t.Run("注册新的结果组装器", func(t *testing.T) {
		// 准备测试数据
		fieldName := "test_field"
		assembler := func(data []*asset.AssetTopResult) (treeOrList, []*statistical.TreeResponse, error) {
			var response []*statistical.TreeResponse
			for _, d := range data {
				response = append(response, &statistical.TreeResponse{
					Name:       d.FieldValue,
					AssetNum:   d.FieldCount,
					QueryKey:   "test_key",
					QueryField: "test_field",
				})
			}
			return List, response, nil
		}

		// 调用被测试方法
		service.registerAssembler(fieldName, assembler)

		// 验证结果
		registeredAssembler, exists := service.assembler[fieldName]
		assert.True(t, exists, "结果组装器应该已注册")

		// 使用注册的组装器
		testData := []*asset.AssetTopResult{
			{
				FieldValue: "测试值",
				FieldCount: 10,
			},
		}
		resultType, resultData, err := registeredAssembler(testData)

		// 验证结果
		assert.NoError(t, err, "应该没有错误")
		assert.Equal(t, List, resultType, "结果类型应该是列表")
		assert.Equal(t, 1, len(resultData), "应该有一个结果")
		assert.Equal(t, "测试值", resultData[0].Name, "名称应该匹配")
		assert.Equal(t, int64(10), resultData[0].AssetNum, "资产数量应该匹配")
		assert.Equal(t, "test_key", resultData[0].QueryKey, "查询键应该匹配")
		assert.Equal(t, "test_field", resultData[0].QueryField, "查询字段应该匹配")
	})
}

// TestProcessTagData 测试处理标签数据方法
func TestProcessTagData(t *testing.T) {
	// 创建服务实例
	service := NewService()
	// 设置 mock 服务器
	// 创建 mock ES 服务器
	mockES := testcommon.NewMockServer()
	defer mockES.Close()

	// 测试用例2：没有业务系统名称的情况
	t.Run("没有业务系统名称", func(t *testing.T) {
		// 准备测试数据
		businessApp := ""

		// 调用被测试方法
		result, err := service.processTagData(businessApp)

		// 验证结果
		assert.NoError(t, err, "应该没有错误")
		assert.NotNil(t, result, "结果不应为空")
		assert.Equal(t, 0, len(result), "结果应该是空数组")
	})
}

// TestProcessCountData 测试处理计数数据方法
func TestProcessCountData(t *testing.T) {
	// 创建服务实例
	service := NewService()
	// 设置 mock 服务器
	// 创建 mock ES 服务器
	mockES := testcommon.NewMockServer()
	defer mockES.Close()
	// 设置 mock 服务器
	mockES.Register("/asset/_search", elastic.SearchResult{
		ScrollId: "scroll-id-2",
		Hits: &elastic.SearchHits{
			TotalHits: &elastic.TotalHits{
				Value:    2,
				Relation: "eq",
			},
			Hits: []*elastic.SearchHit{
				{
					Id:     "1",
					Source: json.RawMessage(`{"business": [{"system": "业务系统", "system_id": "1", "owner": "张三", "owner_id": "1", "department": ["部门负责人"], "source": "rule"}]}`),
				},
				{
					Id:     "2",
					Source: json.RawMessage(`{"business": [{"system": "业务系统", "system_id": "1", "owner": "张三", "owner_id": "1", "department": ["部门负责人"], "source": "rule"}], "network_type": 1}`),
				},
			},
		},
	})

	// 测试用例：处理普通计数数据
	t.Run("处理普通计数数据", func(t *testing.T) {
		// 准备测试数据
		queryFields := &searchField{
			keyword:    "",
			field:      "deveice_num",
			nestedPath: "",
			size:       0,
			name:       "设备资产",
		}
		additionQueries := []elastic.Query{elastic.NewBoolQuery()}

		// 调用被测试方法
		result, err := service.processCountData(queryFields, additionQueries)

		// 验证结果
		assert.NoError(t, err, "应该没有错误")
		assert.NotNil(t, result, "结果不应为空")
	})
}
