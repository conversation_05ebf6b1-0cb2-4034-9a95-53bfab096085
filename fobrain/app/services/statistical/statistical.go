package statistical

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"strings"

	"github.com/olivere/elastic/v7"

	"fobrain/fobrain/app/repository/asset"
	"fobrain/fobrain/app/request/statistical"
	"fobrain/fobrain/app/services/asset_center/business_systems"
	"fobrain/fobrain/app/services/workbench"
	filtrate "fobrain/models/elastic"
	business_system2 "fobrain/models/elastic/business_system"
	"fobrain/models/elastic/poc"
	"fobrain/models/mysql/data_source"
	workbench_model "fobrain/models/mysql/workbench"
)

// treeOrList 定义返回结果的类型，可以是树形或列表形式
type treeOrList string

// 定义返回结果类型常量
const (
	Tree treeOrList = "tree" // 树形结构
	List treeOrList = "list" // 列表结构
)

// Service 统计分析服务
type Service struct {
	// 字段处理器映射，用于处理不同类型的字段查询
	fieldHandlers map[string]func(keyword string) *searchField
	// 结果组装器映射，用于将查询结果组装成指定格式
	assembler map[string]func([]*asset.AssetTopResult) (treeOrList, []*statistical.TreeResponse, error)
}

// searchField 定义了统计分析查询的字段信息
// 包含了查询字段、嵌套路径、大小限制等元数据
type searchField struct {
	keyword      string            // 关键词，用于过滤查询
	field        string            // 主要查询字段
	subField     string            // 子查询字段，用于双层聚合
	value        string            // 字段值，用于精确匹配
	nestedPath   string            // 嵌套路径，用于嵌套查询
	isDualNested bool              // 是否为双层嵌套查询
	size         int               // 返回结果数量限制
	name         string            // 字段显示名称
	param        string            // 参数名称，用于前端标识
	desc         map[string]string // 字段值描述映射，用于转换显示值
	indexName    string            // 索引名称，用于查询
}

// NewService 创建统计分析服务
// 初始化服务并注册各种字段处理器和结果组装器
func NewService() *Service {
	// 初始化服务实例
	s := &Service{
		// 字段处理器映射，用于处理不同类型的字段查询
		fieldHandlers: make(map[string]func(keyword string) *searchField),
		// 结果组装器映射，用于将查询结果组装成指定格式
		assembler: make(map[string]func([]*asset.AssetTopResult) (treeOrList, []*statistical.TreeResponse, error)),
	}

	// 按功能分组注册各类字段处理器
	s.registerBusinessHandlers()  // 注册业务系统相关处理器
	s.registerNetworkHandlers()   // 注册网络类型相关处理器
	s.registerCoverageHandlers()  // 注册覆盖率相关处理器
	s.registerSecurityHandlers()  // 注册安全相关处理器
	s.registerComponentHandlers() // 注册组件相关处理器

	// 注册结果组装器
	// 注册业务系统结果组装器
	s.registerAssembler("business", s.assembleBusinessResponse)
	// 注册部门结果组装器
	s.registerAssembler("department", s.assembleDepartmentResponse)
	// 注册组件类型结果组装器
	s.registerAssembler("rule_infos", s.assembleRuleInfosResponse)

	return s
}

// registerBusinessHandlers 注册业务系统相关的字段处理器
func (s *Service) registerBusinessHandlers() {
	// 注册业务系统字段处理器
	s.registerFieldHandler("business", func(keyword string) *searchField {
		return &searchField{
			keyword:    keyword,           // 关键词过滤
			field:      "business.system", // 查询字段
			nestedPath: "business",        // 嵌套路径
			size:       10000,             // 返回结果数量限制
			name:       "业务系统",            // 显示名称
			param:      "business",        // 参数名称
		}
	})

	// 注册业务系统负责人字段处理器
	s.registerFieldHandler("business_oper_num", func(keyword string) *searchField {
		return &searchField{
			keyword:    keyword,
			field:      "business.person_base.name", // 业务系统负责人名称字段
			nestedPath: "business.person_base",      // 嵌套路径
			size:       10000,
			param:      "business_oper_num",
			name:       "业务系统负责人",
		}
	})

	// 注册部门字段处理器
	s.registerFieldHandler("department", func(keyword string) *searchField {
		return &searchField{
			keyword:      keyword,
			field:        "business_department.name.keyword", // 部门名称字段
			nestedPath:   "business_department",              // 嵌套路径
			subField:     "business_department.user_name.keyword",
			isDualNested: true,
			size:         1000,
			name:         "组织架构",
			param:        "department",
		}
	})

	// 注册业务系统标签字段处理器
	s.registerFieldHandler("business_tag", func(keyword string) *searchField {
		return &searchField{
			name:  "系统基本属性",
			param: "business_tag",
		}
	})
}

// registerNetworkHandlers 注册网络类型相关的字段处理器
func (s *Service) registerNetworkHandlers() {
	// 注册内网资产字段处理器
	s.registerFieldHandler("internal_asset_num", func(keyword string) *searchField {
		return &searchField{
			keyword:    keyword,
			field:      "network_type", // 网络类型字段
			nestedPath: "",
			value:      strconv.Itoa(asset.NetworkTypeInternal), // 内网类型值
			size:       3,
			name:       "内网资产",
			param:      "internal_asset_num",
		}
	})

	// 注册外网资产字段处理器
	s.registerFieldHandler("external_asset_num", func(keyword string) *searchField {
		return &searchField{
			keyword:    keyword,
			field:      "network_type", // 网络类型字段
			nestedPath: "",
			value:      strconv.Itoa(asset.NetworkTypeExternal), // 外网类型值
			size:       3,
			param:      "external_asset_num",
			name:       "外网资产",
		}
	})

	// 注册网络类型统计字段处理器
	s.registerFieldHandler("network_type", func(keyword string) *searchField {
		return &searchField{
			keyword:    keyword,
			field:      "network_type",
			nestedPath: "",
			value:      "",
			size:       5,
			name:       "内外网统计",
			param:      "network_type",
			// 网络类型描述映射
			desc: map[string]string{
				"1": "内网资产",
				"2": "互联网资产",
			},
		}
	})

	// 注册 IP 类型字段处理器
	s.registerFieldHandler("ip_type", func(keyword string) *searchField {
		return &searchField{
			keyword:    keyword,
			field:      "status", // 状态字段
			nestedPath: "",
			value:      "",
			size:       5,
			name:       "关联资产状态",
			param:      "ip_type",
			// 状态描述映射
			desc: map[string]string{
				"1": "在线",
				"2": "离线",
			},
		}
	})

	// 注册数据源字段处理器
	s.registerFieldHandler("source_ids", func(keyword string) *searchField {
		// 获取所有数据源
		sources, _, _ := data_source.AllSources()
		// 创建数据源 ID 到名称的映射
		var sourceNameMap = make(map[string]string, len(sources))
		for _, s := range sources {
			sourceNameMap[strconv.FormatUint(s.Id, 10)] = s.Name
		}

		return &searchField{
			keyword:    keyword,
			field:      "source_ids", // 数据源 ID 字段
			nestedPath: "",
			value:      "",
			size:       5,
			param:      "source_ids",
			name:       "数据源贡献度",
			desc:       sourceNameMap, // 数据源 ID 到名称的映射
		}
	})
}

// registerCoverageHandlers 注册覆盖率相关的字段处理器
func (s *Service) registerCoverageHandlers() {
	// 注册 CMDB 覆盖率字段处理器
	s.registerFieldHandler("cmdb_coverage", func(keyword string) *searchField {
		return &searchField{
			keyword:    keyword,
			field:      strconv.Itoa(workbench_model.CmdbDimensionsID), // CMDB 维度 ID
			nestedPath: "",
			size:       0,
			name:       "CMDB覆盖率",
			param:      "cmdb_coverage",
		}
	})

	// 注册业务系统负责人覆盖率字段处理器
	s.registerFieldHandler("oper_coverage", func(keyword string) *searchField {
		return &searchField{
			keyword:    keyword,
			field:      strconv.Itoa(workbench_model.BusinessDimensionsID), // 业务维度 ID
			nestedPath: "",
			size:       0,
			name:       "业务系统负责人覆盖率",
			param:      "oper_coverage",
		}
	})

	// 注册主机安全覆盖率字段处理器
	s.registerFieldHandler("host_coverage", func(keyword string) *searchField {
		return &searchField{
			keyword:    keyword,
			field:      strconv.Itoa(workbench_model.HostSecurityDimensionsID), // 主机安全维度 ID
			nestedPath: "",
			size:       0,
			name:       "主机安全覆盖率",
			param:      "host_coverage",
		}
	})

	// 注册堡垒机覆盖率字段处理器
	s.registerFieldHandler("uaudithost_coverage", func(keyword string) *searchField {
		return &searchField{
			keyword:    keyword,
			field:      strconv.Itoa(workbench_model.BastionHostDimensionsID), // 堡垒机维度 ID
			nestedPath: "",
			size:       0,
			name:       "堡垒机覆盖率",
			param:      "uaudithost_coverage",
		}
	})

	// 注册主动探测覆盖率字段处理器
	s.registerFieldHandler("detect_coverage", func(keyword string) *searchField {
		return &searchField{
			keyword:    keyword,
			field:      strconv.Itoa(workbench_model.ActiveDetectDimensionsID), // 主动探测维度 ID
			nestedPath: "",
			size:       0,
			name:       "主动探测覆盖率",
			param:      "detect_coverage",
		}
	})

	// 注册 WAF 覆盖率字段处理器
	s.registerFieldHandler("waf_coverage", func(keyword string) *searchField {
		return &searchField{
			keyword:    keyword,
			field:      strconv.Itoa(workbench_model.WafDimensionsID), // WAF 维度 ID
			nestedPath: "",
			size:       0,
			name:       "WAF覆盖率",
			param:      "waf_coverage",
		}
	})

	// 注册运维人员覆盖率字段处理器
	s.registerFieldHandler("devops_coverage", func(keyword string) *searchField {
		return &searchField{
			keyword:    keyword,
			field:      strconv.Itoa(workbench_model.OperDimensionsID), // 运维维度 ID
			nestedPath: "",
			size:       0,
			name:       "运维人员覆盖率",
			param:      "devops_coverage",
		}
	})
}

// registerSecurityHandlers 注册安全相关的字段处理器
func (s *Service) registerSecurityHandlers() {
	// 注册漏洞类型字段处理器
	s.registerFieldHandler("poc_type", func(keyword string) *searchField {
		return &searchField{
			keyword:   keyword,
			field:     "level", // 业务系统字段
			size:      0,
			name:      "漏洞风险等级分布",
			param:     "poc_type",
			indexName: poc.NewPoc().IndexName(),
		}
	})

	// 注册漏洞数量字段处理器
	s.registerFieldHandler("poc_num", func(keyword string) *searchField {
		return &searchField{
			keyword:   keyword,
			field:     "business_name_tmp", // 业务系统字段
			size:      0,
			name:      "漏洞数量",
			param:     "poc_num",
			indexName: poc.NewPoc().IndexName(),
		}
	})
}

// registerComponentHandlers 注册组件相关的字段处理器
func (s *Service) registerComponentHandlers() {
	// 注册设备资产字段处理器
	s.registerFieldHandler("deveice_num", func(keyword string) *searchField {
		return &searchField{
			keyword:    keyword,
			field:      "deveice_num", // 设备资产字段
			nestedPath: "",
			size:       0,
			name:       "设备资产",
			param:      "deveice_num",
		}
	})

	// 注册组件类型字段处理器
	s.registerFieldHandler("rule_infos", func(keyword string) *searchField {
		return &searchField{
			keyword:      keyword,
			field:        "rule_infos.first_tag",  // 一级标签字段
			subField:     "rule_infos.second_tag", // 二级标签字段
			nestedPath:   "rule_infos",            // 嵌套路径
			isDualNested: true,                    // 双层嵌套
			size:         1000,
			name:         "组件类型",
			param:        "rule_infos",
		}
	})
}

// registerFieldHandler 注册字段处理器
func (s *Service) registerFieldHandler(field string, handler func(keyword string) *searchField) {
	s.fieldHandlers[field] = handler
}

// registerAssembler 注册组装逻辑
func (s *Service) registerAssembler(field string, assembler func([]*asset.AssetTopResult) (treeOrList, []*statistical.TreeResponse, error)) {
	s.assembler[field] = assembler
}

// GetList 获取统计列表
func (s *Service) GetList(params *statistical.TreeRequest) ([]map[string]interface{}, error) {
	result := make([]map[string]interface{}, 0)
	for _, field := range params.Type {
		// 获取分组统计参数
		handler, exists := s.fieldHandlers[field]
		if !exists {
			return nil, fmt.Errorf("查询条件不存在: %s", params.Type)
		}
		queryFields := handler(params.Keyword)
		// 查询数据
		config := asset.AssetTopConfig{
			Field:          queryFields.field,
			NestedSubField: queryFields.subField,
			Size:           queryFields.size,
			NestedPath:     queryFields.nestedPath,
			IsDualNested:   queryFields.isDualNested,
			Keyword:        queryFields.keyword,
			Must:           true,
		}
		assetData, err := asset.AssetTopProcess(config)

		if err != nil {
			return nil, err
		}
		// 根据字段组装返回值
		assembler, exists := s.assembler[field]
		if !exists {
			return nil, errors.New("未注册的组装逻辑")
		}
		treeOrList, responses, err := assembler(assetData)
		if err != nil {
			return nil, err
		}
		result = append(result, map[string]interface{}{
			"value":     responses,
			"type":      treeOrList,
			"type_name": queryFields.name,
		})
	}
	return result, nil
}

// GetCountByFields 根据指定字段获取统计数据
// 支持多种统计模式：饼图、排行榜、标签、覆盖率和普通统计
func (s *Service) GetCountByFields(params *statistical.CountRequest) ([]map[string]interface{}, error) {
	var req []map[string]interface{}

	// 构建基础查询条件
	query, err := s.buildMustExistsQuery(params.MustAttr)
	if err != nil {
		return nil, err
	}

	// 添加搜索条件
	query, err = s.buildSearchConditionQuery(params.SearchCondition, query)
	if err != nil {
		return nil, err
	}

	// 将构建的查询条件添加到附加查询中
	additionQueries := []elastic.Query{query}

	// 处理每个请求参数
	for _, param := range params.Params {
		field := param.Field
		t := param.Type

		// 获取字段处理器
		handler, exists := s.fieldHandlers[field]
		if !exists {
			return nil, fmt.Errorf("查询条件不存在: %s", field)
		}

		// 获取查询字段信息
		queryFields := handler("")
		if queryFields.param == "poc_num" || queryFields.param == "poc_type" {
			additionQueries = []elastic.Query{elastic.NewExistsQuery(queryFields.field)}
			query, err = s.buildPocSearchConditionQuery(params.SearchCondition)
			if err != nil {
				return nil, err
			}
			additionQueries = append(additionQueries, query)
		}

		// 根据类型处理数据
		var v interface{}
		var err error

		switch {
		// 饼图/排行模式
		case t == "pie" || t == "top" || queryFields.field == "business_oper_num":
			v, err = s.processPieOrTopData(field, t, queryFields, params, additionQueries)

		// 标签模式
		case t == "tag":
			v, err = s.processTagData(params.BusinessApp)

		// 覆盖率模式
		case t == "coverage":
			v, err = s.processCoverageData(queryFields, additionQueries)

		// 默认统计模式
		default:
			v, err = s.processCountData(queryFields, additionQueries)
		}

		if err != nil {
			return nil, err
		}

		// 构建结果
		req = append(req, map[string]interface{}{
			"value": v,
			"name":  queryFields.name,
			"type":  t,
			"field": queryFields.param,
		})
	}

	return req, nil
}

// buildMustExistsQuery 构建必须存在的字段查询
// 对于嵌套字段，使用 NestedQuery 进行查询
func (s *Service) buildMustExistsQuery(mustAttr []string) (*elastic.BoolQuery, error) {
	query := elastic.NewBoolQuery()

	// 定义嵌套字段映射
	m := map[string][]string{
		"business":   {"business", "business.system"},
		"department": {"business_department", "business_department.name.keyword"},
		"rule_infos": {"rule_infos", "rule_infos.second_tag"},
	}

	// 遍历必须存在的字段
	for _, attr := range mustAttr {
		if v, ok := m[attr]; ok {
			// 对于嵌套字段，创建嵌套查询
			nestedBoolQuery := elastic.NewBoolQuery().Must(
				elastic.NewExistsQuery(v[1]),
			)

			// 将 BoolQuery 放入 NestedQuery 中
			nestedQuery := elastic.NewNestedQuery(
				v[0],
				nestedBoolQuery,
			)

			query = query.Must(nestedQuery)
		} else {
			// 对于普通字段，直接使用 ExistsQuery
			query = query.Must(elastic.NewExistsQuery(attr))
		}
	}

	return query, nil
}

// buildSearchConditionQuery 根据搜索条件构建查询
func (s *Service) buildSearchConditionQuery(searchCondition []string, baseQuery *elastic.BoolQuery) (*elastic.BoolQuery, error) {
	// 解析搜索条件
	conditions, err := filtrate.ParseQueryConditions(searchCondition)
	if err != nil {
		return nil, err
	}

	// 遍历条件并构建查询
	for _, condition := range conditions {
		baseQuery = filtrate.BuildBoolQuery(
			condition.Field,
			condition.OperationTypeString,
			condition.LogicalConnective,
			condition.Value,
			baseQuery,
		)
	}

	return baseQuery, nil
}

func (s *Service) buildPocSearchConditionQuery(searchCondition []string) (*elastic.BoolQuery, error) {
	query := elastic.NewBoolQuery()
	// 解析搜索条件
	conditions, err := filtrate.ParseQueryConditions(searchCondition)
	if err != nil {
		return nil, err
	}

	// 遍历条件并构建查询
	for _, condition := range conditions {
		var field string
		if condition.Field == "business.system" {
			field = "business_name_tmp"
		} else {
			field = condition.Field
		}
		query = filtrate.BuildBoolQuery(
			field,
			condition.OperationTypeString,
			condition.LogicalConnective,
			condition.Value,
			query,
		)
	}

	return query, nil
}

// processPieOrTopData 处理饼图或排行榜数据
func (s *Service) processPieOrTopData(field string, t string, queryFields *searchField, params *statistical.CountRequest, additionQueries []elastic.Query) (interface{}, error) {
	// 如果是排行榜模式，限制返回前5条
	if t == "top" {
		queryFields.size = 5
	}

	config := asset.AssetTopConfig{
		Field:           queryFields.field,
		Size:            queryFields.size,
		NestedPath:      queryFields.nestedPath,
		Keyword:         queryFields.keyword,
		AdditionQueries: additionQueries,
		Must:            true,
		IndexName:       queryFields.indexName,
	}
	assetData, err := asset.AssetTopProcess(config)

	if err != nil {
		return nil, err
	}

	// 特殊处理业务系统负责人总数
	if queryFields.field == "business_oper_num" && t == "total" {
		return len(assetData), nil
	}

	// 构建饼图数据
	var pie []map[string]interface{}
	for _, as := range assetData {
		desc := as.FieldValue

		// 如果有描述映射，则转换显示值
		if queryFields.desc != nil {
			ds, ok := queryFields.desc[desc]
			if ok {
				desc = ds
			}
			if desc == "0" {
				desc = "人工校准"
			}
		}

		// 添加到饼图数据中
		pie = append(pie, map[string]interface{}{
			"name":  desc,
			"value": as.FieldCount,
		})
	}

	return pie, nil
}

// processTagData 处理标签数据
func (s *Service) processTagData(businessApp string) ([]string, error) {
	// 如果没有指定业务系统，返回空列表
	if businessApp == "" {
		return make([]string, 0), nil
	}

	// 获取业务系统信息
	businessInfo, err := business_system2.NewBusinessSystems().GetByName(context.Background(), businessApp)
	if err != nil {
		return nil, err
	}

	if businessInfo == nil {
		return make([]string, 0), nil
	}

	// 获取业务系统属性
	businessServer := business_systems.BusinessSystemsService{}
	attr := businessServer.GetAttr(&business_systems.AssetsAttribute{
		IsGj:           businessInfo.AssetsAttribute.IsXc,
		IsXc:           businessInfo.AssetsAttribute.IsXc,
		PurchaseType:   businessInfo.AssetsAttribute.PurchaseType,
		ImportantTypes: businessInfo.AssetsAttribute.ImportantTypes,
		InsuranceLevel: businessInfo.AssetsAttribute.InsuranceLevel,
		OperatingEnv:   businessInfo.AssetsAttribute.OperatingEnv,
		RunningState:   businessInfo.AssetsAttribute.RunningState,
	}, true)

	// 遍历 map，将非空值添加到切片中
	var val []string
	for _, value := range attr {
		if value != "" {
			val = append(val, value)
		}
	}

	return val, nil
}

// processCoverageData 处理覆盖率数据
func (s *Service) processCoverageData(queryFields *searchField, additionQueries []elastic.Query) (float64, error) {
	// 创建工作台服务
	coverage := workbench.NewWorkbenchService()

	// 将字段转换为 uint64
	cover, err := strconv.ParseUint(queryFields.field, 10, 64)
	if err != nil {
		fmt.Println("转换错误:", err)
		return 0, err
	}

	// 获取资产统计覆盖率
	statisticalCoverage, err := coverage.AssetStatisticalCoverage(cover, additionQueries...)
	if err != nil {
		return 0, err
	}

	// 返回百分比值 (将 int64 转换为 float64)
	return float64(statisticalCoverage.Rate) / 100.0, nil
}

// processCountData 处理普通统计数据
func (s *Service) processCountData(queryFields *searchField, additionQueries []elastic.Query) (int64, error) {
	// 确定查询类型
	queryType := "exists"
	if queryFields.value != "" {
		queryType = "match"
	}

	// 根据参数类型选择不同的查询方法
	var count int64
	var err error
	if queryFields.param == "poc_num" {
		// 漏洞数量查询
		count, err = asset.SearchAssetsPocCount(queryType, queryFields.field, queryFields.value,
			queryFields.nestedPath, additionQueries...)
	} else {
		// 普通资产数量查询
		count, err = asset.SearchAssetsCount(queryType, queryFields.field, queryFields.value,
			queryFields.nestedPath, additionQueries...)
	}

	return count, err
}

// assembleBusinessResponse 组装业务系统的返回值
func (s *Service) assembleBusinessResponse(data []*asset.AssetTopResult) (treeOrList, []*statistical.TreeResponse, error) {
	var response []*statistical.TreeResponse
	for _, d := range data {
		response = append(response, &statistical.TreeResponse{
			Name:       d.FieldValue,
			AssetNum:   d.FieldCount,
			QueryKey:   "name",
			QueryField: "business.system",
		})
	}
	return List, response, nil
}

// assembleDepartmentResponse 组装部门的返回值
func (s *Service) assembleDepartmentResponse(data []*asset.AssetTopResult) (treeOrList, []*statistical.TreeResponse, error) {
	nodeMap := make(map[string]*statistical.TreeResponse)
	var root []*statistical.TreeResponse

	for _, d := range data {
		// 分割部门路径
		parts := strings.Split(d.FieldValue, "/")
		var currentPath []string
		var parentNode *statistical.TreeResponse

		// 为每一级部门路径创建或找到节点
		for _, part := range parts {
			if part == "" {
				continue
			}

			currentPath = append(currentPath, part)
			pathKey := strings.Join(currentPath, "/")

			node, exists := nodeMap[pathKey]
			if !exists {
				// 创建新的部门节点
				node = &statistical.TreeResponse{
					Id:         pathKey,
					Name:       part,
					AssetNum:   d.FieldCount,
					QueryField: "business_department.name.keyword",
					QueryKey:   "id",
				}
				nodeMap[pathKey] = node

				if parentNode == nil {
					// 根级部门
					root = append(root, node)
				} else {
					// 子级部门
					parentNode.Children = append(parentNode.Children, node)
				}
			} else {
				// 累加已存在部门的资产数量
				node.AssetNum += d.FieldCount
			}

			parentNode = node
		}

		// 为最后一级部门添加用户子节点
		if parentNode != nil && len(d.Children) > 0 {
			for _, child := range d.Children {
				userNode := &statistical.TreeResponse{
					Id:         child.FieldValue,
					Name:       child.FieldValue,
					AssetNum:   child.FieldCount,
					QueryField: "business_department.user_name.keyword",
					QueryKey:   "id",
				}
				parentNode.Children = append(parentNode.Children, userNode)
			}
		}
	}

	return Tree, root, nil
}

// assembleBusinessResponse 组装业务系统的返回值
func (s *Service) assembleRuleInfosResponse(data []*asset.AssetTopResult) (treeOrList, []*statistical.TreeResponse, error) {
	var root []*statistical.TreeResponse
	for _, d := range data {
		data := &statistical.TreeResponse{
			Id:         d.FieldValue,
			Name:       d.FieldValue,
			QueryField: "rule_infos.first_tag",
			AssetNum:   d.FieldCount,
			QueryKey:   "id",
		}
		var child []*statistical.TreeResponse
		for _, v := range d.Children {
			child = append(child, &statistical.TreeResponse{
				Id:         v.FieldValue,
				Name:       v.FieldValue,
				AssetNum:   v.FieldCount,
				QueryField: "rule_infos.second_tag",
				QueryKey:   "id",
			})
		}
		data.Children = child
		root = append(root, data)
	}
	return Tree, root, nil
}
