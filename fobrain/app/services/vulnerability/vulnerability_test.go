package vulnerability_services

import (
	"archive/zip"
	"bytes"
	"crypto/sha256"
	"encoding/hex"
	"fobrain/models/mysql/vulnerability"
	"io/ioutil"
	"os"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestWriteFile(t *testing.T) {
	// 创建临时目录
	tmpDir, err := ioutil.TempDir("", "testwritefile")
	assert.NoError(t, err)
	defer os.RemoveAll(tmpDir) // 测试完成后清理

	// 创建模拟的 zip 文件内容
	content := []byte("this is some test data")
	zbuf := new(bytes.Buffer)
	zw := zip.NewWriter(zbuf)
	fw, err := zw.Create("testfile")
	assert.NoError(t, err)
	_, err = fw.Write(content)
	assert.NoError(t, err)
	zw.Close()

	// 读取 zip 文件
	zr, err := zip.NewReader(bytes.NewReader(zbuf.Bytes()), int64(zbuf.Len()))
	assert.NoError(t, err)
	testFile := zr.File[0]

	// 文件路径
	filePath := tmpDir + "/testfile.txt"

	// 调用 WriteFile 函数
	hashString, err := WriteFile(testFile, filePath)
	assert.NoError(t, err, "WriteFile should not produce an error")

	// 读取文件内容
	writtenContent, err := ioutil.ReadFile(filePath)
	assert.NoError(t, err, "Should be able to read the written file")
	assert.Equal(t, content, writtenContent, "The content written should match the original content")

	// 验证哈希值
	expectedHash := sha256.Sum256(content)
	expectedHashString := hex.EncodeToString(expectedHash[:])
	assert.Equal(t, expectedHashString, hashString, "The hash of the written content should match the expected hash")
}

func TestCalculateFileHash(t *testing.T) {
	// 创建一个临时文件
	tmpFile, err := ioutil.TempFile("", "example")
	assert.NoError(t, err, "Creating temp file should not produce an error")
	defer os.Remove(tmpFile.Name()) // 清理临时文件

	// 写入测试数据到临时文件
	testData := []byte("hello world")
	_, err = tmpFile.Write(testData)
	assert.NoError(t, err, "Writing to temp file should not produce an error")
	tmpFile.Close() // 关闭文件以确保可以重新打开用于读取

	// 计算文件的哈希值
	calculatedHash, err := CalculateFileHash(tmpFile.Name())
	assert.NoError(t, err, "CalculateFileHash should not produce an error")

	// 手动计算测试数据的哈希值以验证
	hasher := sha256.New()
	hasher.Write(testData)
	expectedHash := hex.EncodeToString(hasher.Sum(nil))

	// 断言计算的哈希值是否正确
	assert.Equal(t, expectedHash, calculatedHash, "Calculated hash should match the expected hash")
}

func TestCNVDfile(t *testing.T) {
	// 模拟输入数据
	vulns := Vulnerabilities{
		Vulnerabilities: []Vulnerability{
			{
				Number:        "CNVD-2021-00001",
				Title:         "Test Vulnerability",
				Severity:      "High",
				IsEvent:       "Type 1",
				SubmitTime:    "2021-01-01",
				OpenTime:      "2021-01-02",
				ReferenceLink: "http://example.com",
				FormalWay:     "Patch",
				Description:   "Sample description of the vulnerability",
				PatchName:     "SamplePatch",
				Products: Products{
					Product: []string{"Product1", "Product2"},
				},
				CVEs: CVEs{
					CVE: []CVE{
						{CVENumber: "CVE-2021-0001"},
					},
				},
			},
		},
	}

	var cnvdRecords []vulnerability.VulnerabilityCnvdSource
	var successRecord int

	// 调用函数
	CNVDfile("2021-Batch", vulns, &cnvdRecords, &successRecord)

	// 验证结果
	assert.Equal(t, 1, len(cnvdRecords), "Should have one record in the slice")
	assert.Equal(t, 1, successRecord, "Should count one successful record")

	expectedProductString := strings.Join([]string{"Product1", "Product2"}, ",")
	assert.Equal(t, expectedProductString, cnvdRecords[0].AffectedSoftware, "The products should be correctly joined and recorded")
	assert.Equal(t, "CNVD-2021-00001", cnvdRecords[0].CNVDID)
	assert.Equal(t, "CVE-2021-0001", cnvdRecords[0].CVEID)
	assert.Equal(t, "Test Vulnerability", cnvdRecords[0].Name)
}
