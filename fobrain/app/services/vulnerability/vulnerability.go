package vulnerability_services

import (
	"archive/zip"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"encoding/xml"
	"fmt"
	"fobrain/initialize/mysql"
	"fobrain/models/mysql/vulnerability"
	"io"
	"io/ioutil"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"gorm.io/gorm"
)

// CNVD文件参数
type (
	Vulnerabilities struct {
		XMLName         xml.Name        `xml:"vulnerabilitys"`
		Vulnerabilities []Vulnerability `xml:"vulnerability"`
	}
	Vulnerability struct {
		Number        string   `xml:"number"`        // CNVD编号
		CVEs          CVEs     `xml:"cves"`          // CVE信息
		Title         string   `xml:"title"`         // 名称
		Severity      string   `xml:"serverity"`     // 危害级别
		IsEvent       string   `xml:"isEvent"`       // 漏洞类型
		SubmitTime    string   `xml:"submitTime"`    // 报送时间
		OpenTime      string   `xml:"openTime"`      // 公开日期
		ReferenceLink string   `xml:"referenceLink"` // 参考链接
		FormalWay     string   `xml:"formalWay"`     // 解决方案
		Description   string   `xml:"description"`   // 漏洞描述
		PatchName     string   `xml:"patchName"`     // 厂商补丁
		Products      Products `xml:"products"`      // 影响产品
	}
	CVEs struct {
		CVE []CVE `xml:"cve"`
	}
	CVE struct {
		CVENumber string `xml:"cveNumber"`
		CVEUrl    string `xml:"cveUrl"`
	}
	Products struct {
		Product []string `xml:"product"`
	}
)

// CNNVD 文件参数
type (
	CNNVD struct {
		XMLName xml.Name `xml:"cnnvd"`
		Entries []Entry  `xml:"entry"`
	}
	Entry struct {
		Name         string  `xml:"name"`
		CNNVDID      string  `xml:"vuln-id"`
		Published    string  `xml:"published"`
		Modified     string  `xml:"modified"`
		Severity     string  `xml:"severity"`
		VulnType     string  `xml:"vuln-type"`
		VulnDescript string  `xml:"vuln-descript"`
		OtherID      OtherID `xml:"other-id"`
	}
	OtherID struct {
		CVEID string `xml:"cve-id"`
	}
)

// CVE文件参数
type CVERecord struct {
	DataType    string `json:"dataType"`
	CveMetadata struct {
		CveId         string `json:"cveId"`
		DatePublished string `json:"datePublished"`
		DateReserved  string `json:"dateReserved"`
		DateUpdated   string `json:"dateUpdated"`
	} `json:"cveMetadata"`
	Containers struct {
		Cna struct {
			Title        string `json:"title"`
			Descriptions []struct {
				Value string `json:"value"`
			} `json:"descriptions"`
			References []struct {
				URL  string `json:"url"`
				Name string `json:"name"`
			} `json:"references"`
			Metrics []struct {
				CvssV3_0 struct {
					BaseSeverity string `json:"baseSeverity"`
				} `json:"cvssV3_0"`
			} `json:"metrics"`
		} `json:"cna"`
	} `json:"containers"`
}

var wg sync.WaitGroup
var mu sync.Mutex // 声明一个全局的锁，保证多个 goroutine 间的同步

func Unzip(src, destination, batch string, uploader uint64) error {
	db := mysql.GetDbClient()
	var wg sync.WaitGroup
	var fileType, hash string
	recordsChan := make(chan []vulnerability.VulnerabilityUploadRecord, 10) // 使用一个带缓冲的 channel
	uploadrecords := []vulnerability.VulnerabilityUploadRecord{}
	// 限制最大并发数
	maxConcurrent := 100
	sem := make(chan struct{}, maxConcurrent)
	filesAdded := 0 // 计数器，记录写入的文件数

	r, err := zip.OpenReader(src)
	if err != nil {
		return err
	}
	defer r.Close()

	os.MkdirAll(destination, 0755)
	// 文件夹名字、文件类型
	validDirs := map[string]string{
		"CVE":   ".json",
		"CNVD":  ".xml",
		"CNNVD": ".xml",
	}

	wg.Add(1)
	go vulnerability.BatchInsertWorker(recordsChan, &wg)

	for _, f := range r.File {
		// 格式化和解析目录
		cleanPath := strings.Trim(f.Name, "/")
		parts := strings.Split(cleanPath, "/")

		// 检查文件夹和文件
		if len(parts) > 0 {
			topDir := parts[0]
			switch topDir {
			case "CVE":
				fileType = "CVE"
			case "CNVD":
				fileType = "CNVD"
			case "CNNVD":
				fileType = "CNNVD"
			}
			requiredExt := validDirs[topDir]
			if fileType == "" || !strings.HasSuffix(f.Name, requiredExt) {
				// 跳过不符合目录或文件后缀要求的文件
				continue
			}

			// 检查是否为目录，创建文件夹
			if f.FileInfo().IsDir() {
				filePath := filepath.Join(destination, cleanPath) //生成该目录在目标位置的完整路径
				os.MkdirAll(filePath, os.ModePerm)                //创建这个目录以及所有必要的上级目录
				continue
			}
		}

		// 批量插入记录表
		if len(uploadrecords) >= 100 {
			recordsChan <- uploadrecords // 将记录发送到 channel
			uploadrecords = []vulnerability.VulnerabilityUploadRecord{}
		}

		// 限制并发数量
		sem <- struct{}{}
		wg.Add(1)
		// go processAndRecordFile(f, destination, cleanPath, fileType, batch, uploader, &wg, sem, &uploadrecords, &filesAdded)
		go func(f *zip.File, cleanPath string, fileType string) {
			defer wg.Done()
			defer func() { <-sem }() // 处理完成后释放信号

			// 多线程安全地写入文件
			filePath := filepath.Join(destination, cleanPath)
			if err := os.MkdirAll(filepath.Dir(filePath), os.ModePerm); err != nil {
				fmt.Printf("Error creating directory: %v\n", err)
			}
			// 文件写入到磁盘并计算文件哈希值
			if hash, err = WriteFile(f, filePath); err != nil {
				fmt.Printf("Error writing file: %v\n", err)
			}

			// 文件记录参数
			model := vulnerability.VulnerabilityUploadRecord{
				Batch:    batch,
				Uploader: uploader,
				Time:     time.Now(),
				FileType: fileType,
				FileHash: hash,
			}
			mu.Lock() // 锁定 uploadrecords 切片
			uploadrecords = append(uploadrecords, model)
			mu.Unlock()
			filesAdded++
		}(f, cleanPath, fileType)
	}

	// 将剩余数据发送到 channel
	if len(uploadrecords) > 0 {
		recordsChan <- uploadrecords
	}
	close(recordsChan) // 关闭 channel，通知 worker 停止接收
	wg.Wait()          // 等待所有的文件处理完毕

	// 当上传文件因为文件夹格式或者文件后缀等原因导致没有一条数据记录到记录表时，记录该次上传
	if filesAdded == 0 {
		if err := vulnerability.BatchInsertWorker2(db, batch, uploader); err != nil {
			return err
		}
	}
	return nil
}

// processAndRecordFile 处理单个文件的写入和记录
func processAndRecordFile(f *zip.File, destination, cleanPath, fileType, batch string, uploader uint64, wg *sync.WaitGroup, sem chan struct{}, uploadrecords *[]vulnerability.VulnerabilityUploadRecord, filesAdded *int) {
	defer wg.Done()
	defer func() { <-sem }() // 完成后释放信号

	filePath := filepath.Join(destination, cleanPath)
	if err := os.MkdirAll(filepath.Dir(filePath), os.ModePerm); err != nil {
		fmt.Printf("Error creating directory: %v\n", err)
		return
	}

	hash, err := WriteFile(f, filePath)
	if err != nil {
		fmt.Printf("Error writing file: %v\n", err)
		return
	}

	record := vulnerability.VulnerabilityUploadRecord{
		Batch:    batch,
		Uploader: uploader,
		Time:     time.Now(),
		FileType: fileType,
		FileHash: hash,
	}

	mu.Lock() // 锁定 uploadrecords 切片
	*uploadrecords = append(*uploadrecords, record)
	mu.Unlock()
	(*filesAdded)++
}

// 将从ZIP文件中读取的文件内容写入到磁盘上的指定路径并计算哈希值
func WriteFile(f *zip.File, filePath string) (string, error) {
	// 打开目标文件以供写入
	outFile, err := os.OpenFile(filePath, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, f.Mode())
	if err != nil {
		return "", err
	}
	defer outFile.Close()

	// 打开ZIP文件条目以供读取
	rc, err := f.Open()
	if err != nil {
		return "", err
	}
	defer rc.Close()

	// 创建哈希计算器
	hash := sha256.New()

	// 将文件内容复制到目标文件，同时计算哈希值
	multiWriter := io.MultiWriter(outFile, hash)
	_, err = io.Copy(multiWriter, rc)
	if err != nil {
		return "", err
	}

	// 计算并输出哈希值
	hashSum := hash.Sum(nil)
	hashString := hex.EncodeToString(hashSum)
	return hashString, nil
}

// 计算文件的SHA256哈希值
func CalculateFileHash(filePath string) (string, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return "", err
	}
	defer file.Close()

	hash := sha256.New()
	if _, err := io.Copy(hash, file); err != nil {
		return "", err
	}

	return hex.EncodeToString(hash.Sum(nil)), nil
}

// 文件数据添加到原始表中
func Writetosource(destination, batch string) error {
	var wg sync.WaitGroup
	maxConcurrent := 100
	ch := make(chan struct{}, maxConcurrent)
	hashBatch := []string{}
	totalRecords := make(map[string]int)
	successRecords := make(map[string]int)
	cvesources := []vulnerability.VulnerabilityCveSource{}

	recordsChan := make(chan []vulnerability.VulnerabilityCveSource, 10)
	wg.Add(1)
	go vulnerability.BatchInsertCVE(recordsChan, &wg) // 批量插入CVE原始表

	// 遍历文件
	err := filepath.Walk(destination, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err // 发生错误，终止遍历
		}

		if !info.IsDir() {
			relPath, err := filepath.Rel(destination, path) // 相对路径
			if err != nil {
				return err
			}
			path = strings.ReplaceAll(relPath, "\\", "/") // 将路径中的反斜杠替换为正斜杠，统一路径分隔符
			parts := strings.Split(path, "/")
			fullPath := filepath.Join(destination, path) // 绝对路径

			// 计算文件哈希值
			hash, err := CalculateFileHash(fullPath)
			if err != nil {
				return err
			}

			mu.Lock()
			// 根据文件哈希批量更新文件数量
			if len(hashBatch) >= 100 {
				vulnerability.BatchUpdateCountsByBatchAndHash(batch, hashBatch, totalRecords, successRecords)
				hashBatch = []string{}
			}
			mu.Unlock()

			// 批量插入CVE原始表
			if len(cvesources) >= 100 {
				recordsChan <- cvesources
				cvesources = []vulnerability.VulnerabilityCveSource{}
			}
			wg.Add(1)
			ch <- struct{}{}
			go func(fullPath, batch, hash string) {
				defer wg.Done()
				defer func() { <-ch }() // 处理完成后释放信号
				if len(parts) > 0 && parts[0] == "CNVD" && filepath.Ext(fullPath) == ".xml" {
					processCNVD(fullPath, batch, hash, totalRecords, successRecords, &hashBatch)
				} else if len(parts) > 0 && parts[0] == "CNNVD" && filepath.Ext(fullPath) == ".xml" {
					processCNNVD(fullPath, batch, hash, totalRecords, successRecords, &hashBatch)
				} else if len(parts) > 0 && parts[0] == "CVE" && filepath.Ext(fullPath) == ".json" {
					processCVE(fullPath, batch, hash, totalRecords, successRecords, &cvesources, &hashBatch)
				}
			}(fullPath, batch, hash)
		}
		return nil
	})
	if err != nil {
		fmt.Println("Error:", err)
	}
	// 遍历完后，剩余的数据
	if len(hashBatch) > 0 {
		vulnerability.BatchUpdateCountsByBatchAndHash(batch, hashBatch, totalRecords, successRecords) // 更新记录表文件数据数量
	}
	if len(cvesources) > 0 {
		recordsChan <- cvesources // 批量插入CVE原始表
	}
	close(recordsChan)
	wg.Wait()

	return nil
}

// 处理CNVD文件，数据插入到CNVD原始表中
func processCNVD(fullPath, batch, hash string, totalRecords, successRecords map[string]int, hashBatch *[]string) {
	db := mysql.GetDbClient()
	var successRecord int

	// 读取CNVD文件
	data, err := ioutil.ReadFile(fullPath)
	if err != nil {
		fmt.Printf("Error reading file %s: %v\n", fullPath, err)
	}

	// 解析CNVD的xml文件
	var vulnerabilitiesCnvddata Vulnerabilities
	err = xml.Unmarshal(data, &vulnerabilitiesCnvddata)
	if err != nil {
		fmt.Printf("Error unmarshalling XML: %v\n", err)
	}

	// 文件数据处理
	var cnvdRecords []vulnerability.VulnerabilityCnvdSource                // 用于批量插入的切片
	CNVDfile(batch, vulnerabilitiesCnvddata, &cnvdRecords, &successRecord) // 处理CNVD的XML文件，将数据记录到cnvdRecords

	mu.Lock()
	*hashBatch = append(*hashBatch, hash)                             // 文件哈希值批量数据
	totalRecords[hash] = len(vulnerabilitiesCnvddata.Vulnerabilities) // 数据总数
	successRecords[hash] = successRecord                              // 成功数量
	mu.Unlock()

	// 批量插入CNVD原始表
	if err := db.CreateInBatches(cnvdRecords, 100).Error; err != nil {
		fmt.Printf("Error inserting CNVD records: %v\n", err)
	}
}

// 处理CNVD的XML文件，将数据记录到cnvdRecords
func CNVDfile(batch string, vulnerabilitiesCnvddata Vulnerabilities, cnvdRecords *[]vulnerability.VulnerabilityCnvdSource, successRecord *int) {
	for _, vulnerabilitydata := range vulnerabilitiesCnvddata.Vulnerabilities {
		var cveid string
		for _, cve := range vulnerabilitydata.CVEs.CVE {
			cveid = cve.CVENumber
		}
		product := strings.Join(vulnerabilitydata.Products.Product, ",")

		model := vulnerability.VulnerabilityCnvdSource{
			Batch:            batch,
			CNVDID:           vulnerabilitydata.Number,
			CVEID:            cveid,
			DisclosureAt:     vulnerabilitydata.OpenTime,
			Name:             vulnerabilitydata.Title,
			Type:             vulnerabilitydata.IsEvent,
			Level:            vulnerabilitydata.Severity,
			Reference:        vulnerabilitydata.ReferenceLink,
			Describe:         vulnerabilitydata.Description,
			Suggestion:       vulnerabilitydata.FormalWay,
			Patch:            vulnerabilitydata.PatchName,
			SubmitTime:       vulnerabilitydata.SubmitTime,
			AffectedSoftware: product,
		}

		*cnvdRecords = append(*cnvdRecords, model) // 将每个 model 加入到批量插入的切片中
		*successRecord++
	}
}

// 处理CNNVD文件，数据插入到CNNVD原始表中
func processCNNVD(fullPath, batch, hash string, totalRecords, successRecords map[string]int, hashBatch *[]string) {
	db := mysql.GetDbClient()
	var successRecord int

	// 读取CNNVD的XML文件
	data, err := ioutil.ReadFile(fullPath)
	if err != nil {
		fmt.Printf("Error reading file %s: %v\n", fullPath, err)
	}

	// 解析CNNVD的XML文件
	var vulnerabilitiesCnnvddata CNNVD
	err = xml.Unmarshal(data, &vulnerabilitiesCnnvddata)
	if err != nil {
		fmt.Printf("Error unmarshalling XML: %v\n", err)
	}

	// 保存XML文件数据
	var cnnvdRecords []vulnerability.VulnerabilityCnnvdSource                 // 用于批量插入的切片
	CNNVDfile(batch, vulnerabilitiesCnnvddata, &cnnvdRecords, &successRecord) // 处理CNVD的XML文件，将数据记录到cnnvdRecords

	mu.Lock()
	*hashBatch = append(*hashBatch, hash)                      // 文件哈希值批量数据
	totalRecords[hash] = len(vulnerabilitiesCnnvddata.Entries) // 数据总数
	successRecords[hash] = successRecord                       // 成功数量
	mu.Unlock()

	// 批量插入数据到CNNVD原始表
	if err := db.CreateInBatches(cnnvdRecords, 100).Error; err != nil {
		fmt.Printf("Error inserting CNVD records: %v\n", err)
	}
}

// 处理CNVD的XML文件，将数据记录到cnnvdRecords
func CNNVDfile(batch string, vulnerabilitiesCnnvddata CNNVD, cnnvdRecords *[]vulnerability.VulnerabilityCnnvdSource, successRecord *int) {
	// 遍历XML文件中数据
	for _, entry := range vulnerabilitiesCnnvddata.Entries {
		// 提取CVE编号
		cveid := entry.OtherID.CVEID
		if cveid == "null" {
			cveid = ""
		}
		// 一条CNNVD数据
		model := vulnerability.VulnerabilityCnnvdSource{
			Batch:       batch,
			Name:        entry.Name,
			CNNVDID:     entry.CNNVDID,
			CVEID:       cveid,
			CreatedTime: entry.Published,
			UpdatedTime: entry.Modified,
			Level:       entry.Severity,
			Type:        entry.VulnType,
			Describe:    entry.VulnDescript,
		}

		*cnnvdRecords = append(*cnnvdRecords, model) // 将每个 model 加入到批量插入的切片中
		*successRecord++                             // 成功数量
	}
}

// 处理 CVE 文件的逻辑
func processCVE(fullPath, batch, hash string, totalRecords, successRecords map[string]int, cvesources *[]vulnerability.VulnerabilityCveSource, hashBatch *[]string) {
	// 读取 JSON 文件
	data, err := ioutil.ReadFile(fullPath)
	if err != nil {
		fmt.Printf("Error reading file %s: %v\n", fullPath, err)
	}

	// 解析CVE的JSON文件
	var vulnerabilitiesCvedata CVERecord
	err = json.Unmarshal(data, &vulnerabilitiesCvedata)
	if err != nil {
		fmt.Printf("Error unmarshalling JSON\n")
	}

	// 保存JSON文件数据，将数据记录到model
	model := CVEfile(batch, vulnerabilitiesCvedata)

	mu.Lock()
	*hashBatch = append(*hashBatch, hash)    // 文件哈希值批量数据
	totalRecords[hash] = 1                   // 数据总数
	successRecords[hash] = 1                 // 成功数量
	*cvesources = append(*cvesources, model) // 添加cve数据到cvesources
	mu.Unlock()
}

// 处理CVE的json文件，将数据记录到model
func CVEfile(batch string, vulnerabilitiesCvedata CVERecord) vulnerability.VulnerabilityCveSource {
	// 参考链接字段
	var referenceLinks []string
	for _, ref := range vulnerabilitiesCvedata.Containers.Cna.References {
		referenceLinks = append(referenceLinks, ref.URL)
	}
	references := strings.Join(referenceLinks, ", ")

	var describe, level string
	// 判断是否有该字段
	if len(vulnerabilitiesCvedata.Containers.Cna.Descriptions) > 0 {
		describe = vulnerabilitiesCvedata.Containers.Cna.Descriptions[0].Value
	}
	if len(vulnerabilitiesCvedata.Containers.Cna.Metrics) > 0 {
		level = vulnerabilitiesCvedata.Containers.Cna.Metrics[0].CvssV3_0.BaseSeverity
	}
	// 一条json数据
	model := vulnerability.VulnerabilityCveSource{
		Batch:        batch,
		Type:         vulnerabilitiesCvedata.DataType,
		CveId:        vulnerabilitiesCvedata.CveMetadata.CveId,
		DisclosureAt: vulnerabilitiesCvedata.CveMetadata.DatePublished,
		SubmitTime:   vulnerabilitiesCvedata.CveMetadata.DateReserved,
		UpdatedTime:  vulnerabilitiesCvedata.CveMetadata.DateUpdated,
		Name:         vulnerabilitiesCvedata.Containers.Cna.Title,
		Describe:     describe,
		Level:        level,
		Reference:    references,
	}
	return model
}

// 整合数据
func VulnerabilityIntegrationall(batch string) {
	db := mysql.GetDbClient()
	processedCVEIDs := make(map[string]struct{})              // 记录使用过的CVE编号
	VulnerabilityIntegrationCVE(db, batch, processedCVEIDs)   // 遍历CVE原始表
	VulnerabilityIntegrationCNVD(db, batch, processedCVEIDs)  // 遍历CNVD原始表
	VulnerabilityIntegrationCNNVD(db, batch, processedCVEIDs) // 遍历CNNVD原始表
}

// 遍历CVE原始表
func VulnerabilityIntegrationCVE(db *gorm.DB, batch string, processedCVEIDs map[string]struct{}) error {
	offset := 0
	batchSize := 100 // 定义每次查询的记录数
	for {
		// CVE原始表批量数据
		var cveRecords []vulnerability.VulnerabilityCveSource
		err := db.Where("batch = ?", batch).Offset(offset).Limit(batchSize).Find(&cveRecords).Error
		if err != nil {
			return fmt.Errorf("failed to fetch CVE records: %v", err)
		}

		if len(cveRecords) == 0 {
			break // 如果没有数据，则结束循环
		}
		// 找到CNVD和CNNVD原始表中与CVE匹配数据并进行整合,并记录已处理的 CVE ID
		VulnerabilityFindCVE(db, cveRecords, processedCVEIDs, batch)
		offset += batchSize
	}
	return nil
}

// 遍历CNVD原始表
func VulnerabilityIntegrationCNVD(db *gorm.DB, batch string, processedCVEIDs map[string]struct{}) error {
	offset := 0
	batchSize := 100 // 定义每次查询的记录数

	for {
		// CVE原始表批量数据
		var cnvdRecords []vulnerability.VulnerabilityCnvdSource
		err := db.Where("batch = ?", batch).Offset(offset).Limit(batchSize).Find(&cnvdRecords).Error
		if err != nil {
			return fmt.Errorf("failed to fetch CVE records: %v", err)
		}

		if len(cnvdRecords) == 0 {
			break // 如果没有数据，则结束循环
		}
		// 找到CNVD和CNNVD原始表中与CVE匹配数据并进行整合,并记录已处理的 CVE ID
		VulnerabilityFindCNVD(db, cnvdRecords, processedCVEIDs, batch)
		offset += batchSize
	}
	return nil
}

// 遍历CNNVD原始表
func VulnerabilityIntegrationCNNVD(db *gorm.DB, batch string, processedCVEIDs map[string]struct{}) error {
	offset := 0
	batchSize := 100 // 定义每次查询的记录数

	for {
		// CVE原始表批量数据
		var cnnvdRecords []vulnerability.VulnerabilityCnnvdSource
		err := db.Where("batch = ?", batch).Offset(offset).Limit(batchSize).Find(&cnnvdRecords).Error
		if err != nil {
			return fmt.Errorf("failed to fetch CVE records: %v", err)
		}

		if len(cnnvdRecords) == 0 {
			break // 如果没有数据，则结束循环
		}
		// 找到CNVD和CNNVD原始表中与CVE匹配数据并进行整合,并记录已处理的 CVE ID
		VulnerabilityFindCNNVD(db, cnnvdRecords, processedCVEIDs, batch)
		offset += batchSize
	}

	return nil
}

// 由CVE原始表数据找CNVD和CNNVD原始表数据
func VulnerabilityFindCVE(db *gorm.DB, cveBatch []vulnerability.VulnerabilityCveSource, processedCVEIDs map[string]struct{}, batch string) {
	cveIDs := make([]string, len(cveBatch))
	for i, cve := range cveBatch {
		cveIDs[i] = cve.CveId
		processedCVEIDs[cve.CveId] = struct{}{} // 记录使用的CVE编号
	}

	// CNVD和CNNVD满足CVE编号的数据
	cnvdMap := make(map[string]vulnerability.VulnerabilityCnvdSource)
	cnnvdMap := make(map[string]vulnerability.VulnerabilityCnnvdSource)

	wg.Add(2)
	// CNVD原始表查询CVE编号，取最大批次
	go func() {
		defer wg.Done()
		vulnerability.VulnerabilityCNVDinCVE(cveIDs, cnvdMap)
	}()

	// CNNVD原始表查询CVE编号，取最大批次
	go func() {
		defer wg.Done()
		vulnerability.VulnerabilityCNNVDinCVE(cveIDs, cnnvdMap)
	}()
	wg.Wait() // 等待所有的goroutine完成

	// 整合数据
	var batchResults []vulnerability.VulnerabilityIntegration
	for _, cve := range cveBatch {
		hasCve := true
		cnvd, hasCnvd := cnvdMap[cve.CveId]
		cnnvd, hasCnnvd := cnnvdMap[cve.CveId]
		// 整合规则
		result := integrationRule(cve, cnvd, cnnvd, hasCnvd, hasCnnvd, hasCve, batch)
		// 将结果加入批量插入的切片
		batchResults = append(batchResults, result)
	}
	// 批量插入
	if err := db.CreateInBatches(batchResults, 100).Error; err != nil {
		fmt.Printf("failed to insert batch: %v", err)
	}
}

// 由CNVD原始表数据找CVE和CNNVD原始表数据
func VulnerabilityFindCNVD(db *gorm.DB, cnvdBatch []vulnerability.VulnerabilityCnvdSource, processedCVEIDs map[string]struct{}, batch string) {
	cnvdIDs := make([]string, len(cnvdBatch)) // 记录cnvd的CVE编号
	for i, cnvd := range cnvdBatch {
		if cnvd.CVEID != "" {
			cnvdIDs[i] = cnvd.CVEID
		}
	}

	// CNVD和CNNVD满足CVE编号的数据
	cveMap := make(map[string]vulnerability.VulnerabilityCveSource)
	cnnvdMap := make(map[string]vulnerability.VulnerabilityCnnvdSource)

	wg.Add(2)
	// CVE原始表查询CVE编号
	go func() {
		defer wg.Done()
		vulnerability.VulnerabilityCVEinCVE(cnvdIDs, cveMap)
	}()

	// CNNVD原始表查询CVE编号
	go func() {
		defer wg.Done()
		vulnerability.VulnerabilityCNNVDinCVE(cnvdIDs, cnnvdMap)
	}()
	wg.Wait() // 等待所有的goroutine完成

	// 整合数据
	var batchResults []vulnerability.VulnerabilityIntegration
	for _, cnvd := range cnvdBatch {
		var result vulnerability.VulnerabilityIntegration
		if _, processed := processedCVEIDs[cnvd.CVEID]; processed {
			// *cnvdnum++
			continue
		} else if cnvd.CVEID != "" { // 如果 CNVD 的 CVEID 未被处理，则整合到结果表
			hasCnvd := true
			cve, hasCve := cveMap[cnvd.CVEID]
			cnnvd, hasCnnvd := cnnvdMap[cnvd.CVEID]
			processedCVEIDs[cnvd.CVEID] = struct{}{} // 记录使用的CVE编号
			// 整合规则
			result = integrationRule(cve, cnvd, cnnvd, hasCnvd, hasCnnvd, hasCve, batch)
		} else if cnvd.CVEID == "" {
			result = vulnerability.VulnerabilityIntegration{
				Batch:            batch,
				CNVDID:           cnvd.CNVDID,
				CVEID:            "",
				CNNVDID:          "",
				BatchInfo:        fmt.Sprintf("CVE批次: , CNVD批次: %s, CNNVD批次: ", cnvd.Batch),
				DisclosureAt:     cnvd.DisclosureAt,
				Name:             cnvd.Name,
				Type:             cnvd.Type,
				Level:            cnvd.Level,
				Reference:        cnvd.Reference,
				AffectedSoftware: cnvd.AffectedSoftware,
				Describe:         cnvd.Describe,
				Suggestion:       cnvd.Suggestion,
				Patch:            cnvd.Patch,
				Verification:     cnvd.Verification,
				SubmitTime:       cnvd.SubmitTime,
				CreatedTime:      cnvd.CreatedTime,
				UpdatedTime:      cnvd.UpdatedTime,
			}
		}
		// 将结果加入批量插入的切片
		batchResults = append(batchResults, result)
	}

	// 批量插入
	if err := db.CreateInBatches(batchResults, 100).Error; err != nil {
		fmt.Printf("failed to insert batch: %v", err)
	}
}

// 由CNNVD原始表数据找CVE和CNNVD原始表数据
func VulnerabilityFindCNNVD(db *gorm.DB, cnnvdBatch []vulnerability.VulnerabilityCnnvdSource, processedCVEIDs map[string]struct{}, batch string) {
	cnnvdIDs := make([]string, len(cnnvdBatch)) // 记录cnvd的CVE编号
	for i, cnnvd := range cnnvdBatch {
		if cnnvd.CVEID != "" {
			cnnvdIDs[i] = cnnvd.CVEID
		}
	}

	// CNVD和CNNVD满足CVE编号的数据
	cveMap := make(map[string]vulnerability.VulnerabilityCveSource)
	cnvdMap := make(map[string]vulnerability.VulnerabilityCnvdSource)

	wg.Add(2) // 为两个goroutine增加计数

	// CVE原始表查询CVE编号
	go func() {
		defer wg.Done() // 完成后减少WaitGroup的计数
		vulnerability.VulnerabilityCVEinCVE(cnnvdIDs, cveMap)
	}()

	// CNVD原始表查询CVE编号
	go func() {
		defer wg.Done() // 完成后减少WaitGroup的计数
		vulnerability.VulnerabilityCNVDinCVE(cnnvdIDs, cnvdMap)
	}()

	wg.Wait() // 等待所有的goroutine完成

	// 整合数据
	var batchResults []vulnerability.VulnerabilityIntegration
	for _, cnnvd := range cnnvdBatch {
		var result vulnerability.VulnerabilityIntegration
		if _, processed := processedCVEIDs[cnnvd.CVEID]; !processed && cnnvd.CVEID != "" { // 如果 CNVD 的 CVEID 未被处理，则整合到结果表
			hasCnnvd := true
			cve, hasCve := cveMap[cnnvd.CVEID]
			cnvd, hasCnvd := cnvdMap[cnnvd.CVEID]
			processedCVEIDs[cnnvd.CVEID] = struct{}{} // 记录使用的CVE编号
			// 整合规则
			result = integrationRule(cve, cnvd, cnnvd, hasCnvd, hasCnnvd, hasCve, batch)
		} else if cnnvd.CVEID == "" {
			result = vulnerability.VulnerabilityIntegration{
				Batch:            batch,
				CNVDID:           "",
				CVEID:            "",
				CNNVDID:          cnnvd.CNNVDID,
				BatchInfo:        fmt.Sprintf("CVE批次: , CNVD批次: , CNNVD批次: %s", cnnvd.Batch),
				DisclosureAt:     cnnvd.DisclosureAt,
				Name:             cnnvd.Name,
				Type:             cnnvd.Type,
				Level:            cnnvd.Level,
				Reference:        cnnvd.Reference,
				AffectedSoftware: cnnvd.AffectedSoftware,
				Describe:         cnnvd.Describe,
				Suggestion:       cnnvd.Suggestion,
				Patch:            cnnvd.Patch,
				Verification:     cnnvd.Verification,
				SubmitTime:       cnnvd.SubmitTime,
				CreatedTime:      cnnvd.CreatedTime,
				UpdatedTime:      cnnvd.UpdatedTime,
			}
		} else {
			continue
		}
		// 将结果加入批量插入的切片
		batchResults = append(batchResults, result)
	}

	// 批量插入
	if err := db.CreateInBatches(batchResults, 100).Error; err != nil {
		fmt.Printf("failed to insert batch: %v", err)
	}
}

// 整合规则
func integrationRule(cve vulnerability.VulnerabilityCveSource, cnvd vulnerability.VulnerabilityCnvdSource, cnnvd vulnerability.VulnerabilityCnnvdSource, hasCnvd, hasCnnvd, hasCve bool, batch string) vulnerability.VulnerabilityIntegration {
	var result vulnerability.VulnerabilityIntegration

	// 如果有 CNVD 数据
	if hasCnvd {
		result = vulnerability.VulnerabilityIntegration{
			Batch:            batch,
			CNVDID:           cnvd.CNVDID,
			CVEID:            cve.CveId,
			CNNVDID:          cnnvd.CNNVDID,
			BatchInfo:        fmt.Sprintf("CVE批次: %s, CNVD批次: %s, CNNVD批次: %s", cve.Batch, cnvd.Batch, cnnvd.Batch),
			DisclosureAt:     cnvd.DisclosureAt,
			Name:             cnvd.Name,
			Type:             cnvd.Type,
			Level:            cnvd.Level,
			Reference:        cnvd.Reference,
			AffectedSoftware: cnvd.AffectedSoftware,
			Describe:         cnvd.Describe,
			Suggestion:       cnvd.Suggestion,
			Patch:            cnvd.Patch,
			Verification:     cnvd.Verification,
			SubmitTime:       cnvd.SubmitTime,
			CreatedTime:      cnvd.CreatedTime,
			UpdatedTime:      cnvd.UpdatedTime,
		}
	} else if hasCnnvd { // 如果没有 CNVD 数据，但有 CNNVD 数据
		result = vulnerability.VulnerabilityIntegration{
			Batch:            batch,
			CNVDID:           cnvd.CNVDID,
			CVEID:            cve.CveId,
			CNNVDID:          cnnvd.CNNVDID,
			BatchInfo:        fmt.Sprintf("CVE批次: %s, CNVD批次: %s, CNNVD批次: %s", cve.Batch, cnvd.Batch, cnnvd.Batch),
			DisclosureAt:     cnnvd.DisclosureAt,
			Name:             cnnvd.Name,
			Type:             cnnvd.Type,
			Level:            cnnvd.Level,
			Reference:        cnnvd.Reference,
			AffectedSoftware: cnnvd.AffectedSoftware,
			Describe:         cnnvd.Describe,
			Suggestion:       cnnvd.Suggestion,
			Patch:            cnnvd.Patch,
			Verification:     cnnvd.Verification,
			SubmitTime:       cnnvd.SubmitTime,
			CreatedTime:      cnnvd.CreatedTime,
			UpdatedTime:      cnnvd.UpdatedTime,
		}
	} else if hasCve { // 如果没有 CNVD 和 CNNVD 数据
		result = vulnerability.VulnerabilityIntegration{
			Batch:            batch,
			CNVDID:           cnvd.CNVDID,
			CNNVDID:          cnnvd.CNNVDID,
			CVEID:            cve.CveId,
			BatchInfo:        fmt.Sprintf("CVE批次: %s, CNVD批次: %s, CNNVD批次: %s", cve.Batch, cnvd.Batch, cnnvd.Batch),
			DisclosureAt:     cve.DisclosureAt,
			Name:             cve.Name,
			Type:             cve.Type,
			Level:            cve.Level,
			Reference:        cve.Reference,
			AffectedSoftware: cve.AffectedSoftware,
			Describe:         cve.Describe,
			Suggestion:       cve.Suggestion,
			Patch:            cve.Patch,
			SubmitTime:       cve.SubmitTime,
			CreatedTime:      cve.CreatedTime,
			UpdatedTime:      cve.UpdatedTime,
		}
	}
	return result
}
