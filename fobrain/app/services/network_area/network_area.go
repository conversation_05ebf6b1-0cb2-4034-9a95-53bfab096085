package network_area

import (
	"context"
	"encoding/json"
	"fobrain/initialize/redis"
	"fobrain/models/mysql/network_areas"
	redis_helper "fobrain/models/redis"
	"time"

	go_redis "github.com/go-redis/redis/v8"
)

// GetNetWorkAreaList 获取网络区域列表, 优先从缓存中获取, 缓存过期时间1小时
func GetNetWorkAreaList() ([]*network_areas.NetworkArea, error) {
	// 从缓存中获取
	networkAreaListCache, err := redis.GetRedisClient().Get(context.Background(), redis_helper.NetworkAreaListKey()).Result()
	if err != nil && err != go_redis.Nil {
		return nil, err
	}
	if err == nil {
		// 反序列化
		var networkAreaList []*network_areas.NetworkArea
		err = json.Unmarshal([]byte(networkAreaListCache), &networkAreaList)
		if err != nil {
			return nil, err
		}
		return networkAreaList, nil
	}
	networkAreaList, _, err := network_areas.AllNetworkAreas()
	if err != nil {
		return nil, err
	}
	// 写入缓存
	redis.GetRedisClient().Set(context.Background(), redis_helper.NetworkAreaListKey(), networkAreaList, time.Hour*1)
	return networkAreaList, nil
}

// GetNetWorkAreaNameById 根据id获取网络区域名称
func GetNetWorkAreaNameById(id uint64) string {
	networkAreaList, err := GetNetWorkAreaList()
	if err != nil {
		return ""
	}
	for _, networkArea := range networkAreaList {
		if networkArea.Id == id {
			return networkArea.Name
		}
	}
	return ""
}
