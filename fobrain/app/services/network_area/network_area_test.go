package network_area

import (
	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/models/mysql/network_areas"
	redis_helper "fobrain/models/redis"
	. "github.com/agiledragon/gomonkey/v2"
	"github.com/alicebob/miniredis/v2"
	"github.com/go-redis/redis/v8"
	. "github.com/smartystreets/goconvey/convey"

	"encoding/json"
	"testing"
)

func TestGetNetWorkAreaList(t *testing.T) {
	Convey("TestGetNetWorkAreaList", t, func() {
		Convey("Success", func() {
			s, err := miniredis.Run()
			if err != nil {
				t.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
			}
			defer s.Close()

			client := redis.NewClient(&redis.Options{
				Addr: s.Addr(),
			})
			testcommon.SetRedisClient(client)

			defer ApplyFuncReturn(network_areas.AllNetworkAreas, nil, int64(0), nil).Reset()

			_, _ = GetNetWorkAreaList()
		})

		<PERSON>vey("Error is nil", func() {
			s, err := miniredis.Run()
			if err != nil {
				t.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
			}
			defer s.Close()

			client := redis.NewClient(&redis.Options{
				Addr: s.Addr(),
			})
			testcommon.SetRedisClient(client)

			var networkAreaList []*network_areas.NetworkArea
			networkAreaListByte, _ := json.Marshal(networkAreaList)
			_ = s.Set(redis_helper.NetworkAreaListKey(), string(networkAreaListByte))

			_, _ = GetNetWorkAreaList()
		})
	})
}
