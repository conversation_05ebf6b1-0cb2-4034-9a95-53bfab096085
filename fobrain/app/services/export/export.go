package export

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/olivere/elastic/v7"

	"fobrain/fobrain/app/services/export/handlers"
	"fobrain/fobrain/app/services/permission"
	"fobrain/initialize/es"
	"fobrain/models/mysql/data_source"
	"fobrain/pkg/utils"
)

type Service struct {
	shardSize   int // 分片大小，默认10000
	concurrency int // 并发数，默认10
}

func NewService() *Service {
	return &Service{
		shardSize:   10000,
		concurrency: 25,
	}
}

// WithConcurrency 设置并发数
func (s *Service) WithConcurrency(concurrency int) *Service {
	s.concurrency = concurrency
	return s
}

// Export 统一导出数据
func (s *Service) Export(ctx *gin.Context, handler Handler, params interface{}) (string, error) {
	// 构建查询
	query, err := s.buildFinalQuery(ctx, handler, params)
	if err != nil {
		return "", err
	}

	// 估算数据量
	count, err := es.GetCount(handler.IndexName(), query)
	if err != nil {
		return "", err
	}

	if count == 0 {
		return "", fmt.Errorf("导出的数据不存在")
	}

	// 统一使用分片导出逻辑
	return s.exportUnified(ctx, handler, query, params)
}

// buildFinalQuery 构建最终查询（包含权限）
func (s *Service) buildFinalQuery(ctx *gin.Context, handler Handler, params interface{}) (*elastic.BoolQuery, error) {
	query, err := handler.BuildQuery(ctx, params)
	if err != nil {
		return nil, err
	}

	// 添加权限查询
	if permissionType := handler.PermissionType(); permissionType > 0 {
		staffIds, _ := ctx.Get("staff_ids")
		isSuperManage, _ := ctx.Get("is_super_manage")

		if !isSuperManage.(bool) && staffIds != nil {
			permissionQuery, err := permission.NewPermissionService().ProcessElasticsearchQuery(ctx, permissionType)
			if err != nil {
				return nil, err
			}
			if permissionQuery != nil {
				query = query.Must(permissionQuery)
			}
		}
	}

	return query, nil
}

// exportUnified 统一导出逻辑
func (s *Service) exportUnified(ctx *gin.Context, handler Handler, query *elastic.BoolQuery, params interface{}) (string, error) {
	// 1. 创建临时目录
	tempDir, err := os.MkdirTemp("", "export_"+time.Now().Format("20060102150405"))
	if err != nil {
		return "", err
	}
	defer os.RemoveAll(tempDir) // 自动清理临时目录

	// 2. 预加载数据源信息（无论是否需要，都提前加载避免重复查询）
	allSources, err := s.getAllDataSources()
	if err != nil {
		return "", err
	}

	// 3. 分片处理数据
	err = s.processAllShards(tempDir, handler, query, params, allSources)
	if err != nil {
		return "", err // 自动清理临时目录
	}

	// 4. 检查生成的文件数量并返回
	return s.handleGeneratedFiles(tempDir, handler.FilePrefix())
}

// handleGeneratedFiles 处理生成的文件
func (s *Service) handleGeneratedFiles(tempDir string, filePrefix string) (string, error) {
	files, err := os.ReadDir(tempDir)
	if err != nil {
		return "", err
	}

	if len(files) == 0 {
		return "", fmt.Errorf("没有生成任何文件")
	}

	if len(files) == 1 {
		// 单文件：移动到根目录，保持原文件名格式
		return s.moveSingleFile(tempDir, files[0].Name(), filePrefix)
	} else {
		// 多文件：打包成ZIP
		return s.createZip(tempDir, filePrefix)
	}
}

// moveSingleFile 移动单个文件到根目录
func (s *Service) moveSingleFile(tempDir string, fileName string, filePrefix string) (string, error) {
	// 使用原来的命名格式
	newFileName := fmt.Sprintf("%s-%s.xlsx",
		time.Now().Format("20060102150405"),
		filePrefix)

	srcPath := filepath.Join(tempDir, fileName)
	dstPath := newFileName

	// 移动文件

	err := os.Rename(srcPath, dstPath)
	if err != nil { // 任何平台都可能失败
		if copyErr := copyAndRemove(srcPath, dstPath); copyErr != nil {
			return "", fmt.Errorf("rename failed (%v) and copy failed too (%w)", err, copyErr)
		}
	}

	return newFileName, nil
}
func copyAndRemove(src, dst string) error {
	// 1. 复制文件
	if err := copyFile(src, dst); err != nil {
		return err
	}
	// 2. 删除源文件
	return os.Remove(src)
}

func copyFile(src, dst string) error {
	srcFile, err := os.Open(src)
	if err != nil {
		return err
	}
	defer srcFile.Close()

	dstFile, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer dstFile.Close()

	_, err = io.Copy(dstFile, srcFile)
	return err
}

// processAllShards 分片处理所有数据（优化版：协程内完整处理）
func (s *Service) processAllShards(tempDir string, handler Handler, query *elastic.BoolQuery, params interface{}, allSources map[uint64]string) error {
	client := es.GetEsClient()
	sorts := []elastic.Sorter{
		elastic.NewFieldSort("updated_at").Desc(),
		elastic.NewFieldSort("id").Desc(),
	}

	// 协程控制
	semaphore := make(chan struct{}, s.concurrency)
	var wg sync.WaitGroup
	errChan := make(chan error, 1)
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	var lastSortValues []interface{}
	fileIndex := 0

	for {
		// 检查是否被取消
		select {
		case <-ctx.Done():
			break
		default:
		}

		// 查询数据
		search := client.Search().Index(handler.IndexName()).
			Query(query).
			SortBy(sorts...).
			Size(s.shardSize)

		if len(lastSortValues) > 0 {
			search = search.SearchAfter(lastSortValues...)
		}

		result, err := search.Do(ctx)
		if err != nil {
			return err
		}

		if len(result.Hits.Hits) == 0 {
			break // 没有更多数据
		}

		// 获取最后一个排序值，用于下一次查询
		lastHit := result.Hits.Hits[len(result.Hits.Hits)-1]
		lastSortValues = lastHit.Sort

		// 启动协程处理这批数据
		wg.Add(1)
		semaphore <- struct{}{} // 获取信号量

		go func(hits []*elastic.SearchHit, index int) {
			defer wg.Done()
			defer func() { <-semaphore }() // 释放信号量

			// 处理一批数据并生成文件
			err := s.processHitsAndGenerateFile(
				tempDir, hits, index, handler, params, allSources)

			// 如果出错，发送到错误通道并取消上下文
			if err != nil {
				select {
				case errChan <- err:
					cancel() // 取消所有其他协程
				default:
					// 已经有错误了，忽略
				}
			}
		}(result.Hits.Hits, fileIndex)

		fileIndex++
	}

	// 等待所有协程完成
	wg.Wait()

	// 检查错误
	select {
	case err := <-errChan:
		return err
	default:
		return nil
	}
}

// needDataSourceColumn 根据处理器类型判断是否需要添加数据源列
// 资产类型的处理器需要添加数据源列，其他类型不需要
// 这是为了在导出资产相关数据时，能够显示每个资产来自哪个数据源
func (s *Service) needDataSourceColumn(handler Handler) bool {
	// 根据处理器类型判断
	switch handler.(type) {
	case *handlers.AssetHandler, *handlers.DeviceHandler, *handlers.PocHandler:
		return true
	default:
		return false
	}
}

// processHitsAndGenerateFile 处理ES搜索结果并生成Excel文件
// 优化了数据源列处理，直接在生成Excel时添加数据源列，避免了重复的文件I/O操作
func (s *Service) processHitsAndGenerateFile(
	tempDir string,
	hits []*elastic.SearchHit,
	index int,
	handler Handler,
	params interface{},
	allSources map[uint64]string) error {

	// 1. 处理数据
	data := make([][]interface{}, 0, len(hits))

	// 判断是否需要数据源列
	needDataSourceColumn := s.needDataSourceColumn(handler)
	var sourceNames []string
	if needDataSourceColumn {
		sourceNames = make([]string, 0, len(hits))
	}

	for _, hit := range hits {
		row, err := handler.ParseRow(hit, params)
		if err != nil {
			continue // 如果解析失败，跳过这条记录
		}

		// 只有在解析成功后才添加数据和数据源信息
		data = append(data, row)

		// 提取数据源信息（如果需要）
		if needDataSourceColumn {
			sourceName := s.extractSourceNameFromHit(hit, allSources)
			sourceNames = append(sourceNames, sourceName)
		}
	}

	if len(data) == 0 {
		return nil // 没有有效数据，跳过文件生成
	}

	// 2. 准备表头和数据（包含数据源列）
	var headers []string
	var finalData [][]interface{}

	if needDataSourceColumn {
		// 添加数据源列作为第一列
		headers = append([]string{"数据源"}, handler.Headers(params)...)

		// 为每行数据添加数据源信息
		finalData = make([][]interface{}, len(data))
		for i, row := range data {
			finalData[i] = append([]interface{}{sourceNames[i]}, row...)
		}
	} else {
		headers = handler.Headers(params)
		finalData = data
	}

	// 3. 一次性生成完整的Excel文件
	fileName := fmt.Sprintf("%s_part_%03d.xlsx", handler.FilePrefix(), index+1)
	filePath := filepath.Join(tempDir, fileName)

	_, err := utils.WriterExcel(filePath, headers, finalData)
	if err != nil {
		return err
	}

	return nil
}

// getAllDataSources 获取所有数据源信息（一次性查询MySQL）
func (s *Service) getAllDataSources() (map[uint64]string, error) {
	sources, _, err := data_source.AllSources()
	if err != nil {
		return nil, err
	}

	sourceMap := make(map[uint64]string)
	for _, source := range sources {
		sourceMap[source.Id] = source.Name
	}
	return sourceMap, nil
}

// extractSourceNameFromHit 从ES hit中提取数据源名称
func (s *Service) extractSourceNameFromHit(hit *elastic.SearchHit, allSources map[uint64]string) string {
	// 解析资产数据获取数据源信息
	assetData := make(map[string]interface{})
	if err := json.Unmarshal(hit.Source, &assetData); err != nil {
		return ""
	}

	// 获取 AllSourceIds
	sourceIdsInterface, ok := assetData["all_source_ids"]
	if !ok {
		return ""
	}

	sourceIdsList, ok := sourceIdsInterface.([]interface{})
	if !ok {
		return ""
	}

	// 预分配切片容量
	allSourceIds := make([]uint64, 0, len(sourceIdsList))
	for _, id := range sourceIdsList {
		if idFloat, ok := id.(float64); ok {
			allSourceIds = append(allSourceIds, uint64(idFloat))
		}
	}

	if len(allSourceIds) == 0 {
		return ""
	}

	// 使用预加载的数据源信息进行匹配
	names := make([]string, 0, len(allSourceIds))
	for _, sourceId := range allSourceIds {
		if sourceName, exists := allSources[sourceId]; exists {
			names = append(names, sourceName)
		}
	}
	return strings.Join(names, ",")
}

// createZip 创建压缩文件
func (s *Service) createZip(tempDir, filePrefix string) (string, error) {
	files, err := filepath.Glob(filepath.Join(tempDir, "*.xlsx"))
	if err != nil {
		return "", err
	}

	if len(files) == 0 {
		return "", fmt.Errorf("没有文件需要压缩")
	}

	zipFileName := fmt.Sprintf("%s-%s.zip",
		time.Now().Format("20060102150405"),
		filePrefix)

	return utils.CreateZip(zipFileName, files)
}
