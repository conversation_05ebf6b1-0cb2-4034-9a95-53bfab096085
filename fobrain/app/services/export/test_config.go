package export

import (
	"fmt"
	"os"
	"testing"

	"github.com/gin-gonic/gin"
)

// TestConfig 测试配置
type TestConfig struct {
	// 测试数据库配置
	TestDBHost     string
	TestDBPort     string
	TestDBName     string
	TestDBUser     string
	TestDBPassword string

	// 测试Elasticsearch配置
	TestESHost string
	TestESPort string

	// 测试文件路径
	TestTempDir   string
	TestOutputDir string
	TestDataDir   string
}

// GetTestConfig 获取测试配置
func GetTestConfig() *TestConfig {
	return &TestConfig{
		TestDBHost:     getEnvOrDefault("TEST_DB_HOST", "localhost"),
		TestDBPort:     getEnvOrDefault("TEST_DB_PORT", "3306"),
		TestDBName:     getEnvOrDefault("TEST_DB_NAME", "fobrain_test"),
		TestDBUser:     getEnvOrDefault("TEST_DB_USER", "root"),
		TestDBPassword: getEnvOrDefault("TEST_DB_PASSWORD", ""),
		TestESHost:     getEnvOrDefault("TEST_ES_HOST", "localhost"),
		TestESPort:     getEnvOrDefault("TEST_ES_PORT", "9200"),
		TestTempDir:    getEnvOrDefault("TEST_TEMP_DIR", "/tmp/export_test"),
		TestOutputDir:  getEnvOrDefault("TEST_OUTPUT_DIR", "/tmp/export_output"),
		TestDataDir:    getEnvOrDefault("TEST_DATA_DIR", "./testdata"),
	}
}

// getEnvOrDefault 获取环境变量或默认值
func getEnvOrDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// SetupTestEnvironment 设置测试环境
func SetupTestEnvironment(t *testing.T) {
	// 设置Gin为测试模式
	gin.SetMode(gin.TestMode)

	// 设置测试环境变量
	os.Setenv("GO_ENV", "test")
	os.Setenv("GIN_MODE", "test")

	// 创建测试目录
	config := GetTestConfig()
	os.MkdirAll(config.TestTempDir, 0755)
	os.MkdirAll(config.TestOutputDir, 0755)
	os.MkdirAll(config.TestDataDir, 0755)
}

// CleanupTestEnvironment 清理测试环境
func CleanupTestEnvironment(t *testing.T) {
	config := GetTestConfig()

	// 清理测试目录
	os.RemoveAll(config.TestTempDir)
	os.RemoveAll(config.TestOutputDir)
}

// CreateTestData 创建测试数据
func CreateTestData() map[string]interface{} {
	return map[string]interface{}{
		"assets": []map[string]interface{}{
			{
				"id":       "asset-1",
				"ip":       "*************",
				"hostname": []string{"test-host-1"},
				"status":   1,
				"area":     1,
			},
			{
				"id":       "asset-2",
				"ip":       "*************",
				"hostname": []string{"test-host-2"},
				"status":   1,
				"area":     2,
			},
		},
		"devices": []map[string]interface{}{
			{
				"id":         "device-1",
				"fid":        "device-fid-1",
				"hostname":   []string{"device-host-1"},
				"private_ip": []string{"*************"},
				"public_ip":  []string{"*******"},
			},
			{
				"id":         "device-2",
				"fid":        "device-fid-2",
				"hostname":   []string{"device-host-2"},
				"private_ip": []string{"*************"},
				"public_ip":  []string{},
			},
		},
		"pocs": []map[string]interface{}{
			{
				"id":       "poc-1",
				"name":     "测试漏洞1",
				"ip":       "*************",
				"port":     80,
				"level":    3,
				"status":   1,
				"risk_num": 85,
			},
			{
				"id":       "poc-2",
				"name":     "测试漏洞2",
				"ip":       "*************",
				"port":     443,
				"level":    2,
				"status":   2,
				"risk_num": 65,
			},
		},
	}
}

// MockElasticsearchResponse 模拟Elasticsearch响应
type MockElasticsearchResponse struct {
	Hits struct {
		Total struct {
			Value int64 `json:"value"`
		} `json:"total"`
		Hits []struct {
			Source map[string]interface{} `json:"_source"`
			Sort   []interface{}          `json:"sort"`
		} `json:"hits"`
	} `json:"hits"`
}

// CreateMockESResponse 创建模拟的ES响应
func CreateMockESResponse(data []map[string]interface{}) *MockElasticsearchResponse {
	response := &MockElasticsearchResponse{}
	response.Hits.Total.Value = int64(len(data))

	for i, item := range data {
		hit := struct {
			Source map[string]interface{} `json:"_source"`
			Sort   []interface{}          `json:"sort"`
		}{
			Source: item,
			Sort:   []interface{}{i}, // 简单的排序值
		}
		response.Hits.Hits = append(response.Hits.Hits, hit)
	}

	return response
}

// TestDataSize 测试数据大小配置
type TestDataSize struct {
	Small  int // 小数据集（< 1000条）
	Medium int // 中等数据集（1000-10000条）
	Large  int // 大数据集（> 10000条）
}

// GetTestDataSizes 获取测试数据大小配置
func GetTestDataSizes() *TestDataSize {
	return &TestDataSize{
		Small:  100,
		Medium: 5000,
		Large:  15000,
	}
}

// GenerateTestAssets 生成测试资产数据
func GenerateTestAssets(count int) []map[string]interface{} {
	assets := make([]map[string]interface{}, count)
	for i := 0; i < count; i++ {
		assets[i] = map[string]interface{}{
			"id":       fmt.Sprintf("asset-%d", i+1),
			"ip":       fmt.Sprintf("192.168.%d.%d", (i/254)+1, (i%254)+1),
			"hostname": []string{fmt.Sprintf("host-%d", i+1)},
			"status":   1,
			"area":     (i % 5) + 1,
			"ports": []map[string]interface{}{
				{
					"port":     80,
					"protocol": "tcp",
					"products": []map[string]interface{}{
						{
							"name":    "nginx",
							"version": "1.18.0",
						},
					},
				},
			},
		}
	}
	return assets
}

// GenerateTestDevices 生成测试设备数据
func GenerateTestDevices(count int) []map[string]interface{} {
	devices := make([]map[string]interface{}, count)
	for i := 0; i < count; i++ {
		devices[i] = map[string]interface{}{
			"id":         fmt.Sprintf("device-%d", i+1),
			"fid":        fmt.Sprintf("device-fid-%d", i+1),
			"hostname":   []string{fmt.Sprintf("device-host-%d", i+1)},
			"private_ip": []string{fmt.Sprintf("192.168.%d.%d", (i/254)+1, (i%254)+1)},
			"public_ip":  []string{},
			"sn":         []string{fmt.Sprintf("SN%06d", i+1)},
			"mac":        []string{fmt.Sprintf("00:11:22:33:%02x:%02x", i/256, i%256)},
			"tags":       []string{"tag1", "tag2"},
			"poc_num":    i % 10,
		}
	}
	return devices
}

// GenerateTestPocs 生成测试漏洞数据
func GenerateTestPocs(count int) []map[string]interface{} {
	pocs := make([]map[string]interface{}, count)
	vulTypes := []string{"SQL注入", "XSS", "CSRF", "文件上传", "命令执行"}
	levels := []int{1, 2, 3, 4} // 低危、中危、高危、严重

	for i := 0; i < count; i++ {
		pocs[i] = map[string]interface{}{
			"id":          fmt.Sprintf("poc-%d", i+1),
			"name":        fmt.Sprintf("测试漏洞-%d", i+1),
			"ip":          fmt.Sprintf("192.168.%d.%d", (i/254)+1, (i%254)+1),
			"port":        80 + (i % 1000),
			"hostname":    []string{fmt.Sprintf("vuln-host-%d", i+1)},
			"area":        (i % 5) + 1,
			"vul_type":    vulTypes[i%len(vulTypes)],
			"level":       levels[i%len(levels)],
			"status":      (i % 5) + 1,
			"risk_num":    (i % 100) + 1,
			"details":     fmt.Sprintf("漏洞详情描述-%d", i+1),
			"suggestions": fmt.Sprintf("修复建议-%d", i+1),
		}
	}
	return pocs
}
