package export

import (
	"github.com/gin-gonic/gin"
	"github.com/olivere/elastic/v7"
)

// Handler 导出处理器接口
type Handler interface {
	// 获取索引名称
	IndexName() string

	// 构建查询条件
	BuildQuery(ctx *gin.Context, params interface{}) (*elastic.BoolQuery, error)

	// 获取表头
	Headers(params interface{}) []string

	// 解析单条数据
	ParseRow(hit *elastic.SearchHit, params interface{}) ([]interface{}, error)

	// 获取文件名前缀
	FilePrefix() string

	// 获取权限类型（0表示无权限控制）
	PermissionType() int
}
