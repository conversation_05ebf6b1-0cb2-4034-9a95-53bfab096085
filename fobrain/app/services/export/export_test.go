package export

import (
	"encoding/json"
	"github.com/stretchr/testify/require"
	"net/http"
	"net/http/httptest"
	"os"
	"path/filepath"
	"strings"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/olivere/elastic/v7"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"fobrain/fobrain/app/services/export/handlers"
	testcommon "fobrain/fobrain/tests/common_test"
)

// MockHandler 模拟导出处理器
type MockHandler struct {
	mock.Mock
}

func (m *MockHandler) IndexName() string {
	args := m.Called()
	return args.String(0)
}

func (m *MockHandler) BuildQuery(ctx *gin.Context, params interface{}) (*elastic.BoolQuery, error) {
	args := m.Called(ctx, params)
	return args.Get(0).(*elastic.BoolQuery), args.Error(1)
}

func (m *MockHandler) Headers(params interface{}) []string {
	args := m.Called(params)
	return args.Get(0).([]string)
}

func (m *MockHandler) ParseRow(hit *elastic.SearchHit, params interface{}) ([]interface{}, error) {
	args := m.Called(hit, params)
	return args.Get(0).([]interface{}), args.Error(1)
}

func (m *MockHandler) FilePrefix() string {
	args := m.Called()
	return args.String(0)
}

func (m *MockHandler) PermissionType() int {
	args := m.Called()
	return args.Int(0)
}

func TestNewService(t *testing.T) {
	service := NewService()

	assert.NotNil(t, service)
	assert.Equal(t, 10000, service.shardSize)
	assert.Equal(t, 25, service.concurrency)
}

func TestService_WithConcurrency(t *testing.T) {
	service := NewService()

	// 测试设置并发数
	newService := service.WithConcurrency(20)

	assert.Equal(t, 20, newService.concurrency)
	assert.Equal(t, 10000, newService.shardSize) // 其他配置不变
}

func TestService_buildFinalQuery(t *testing.T) {
	service := NewService()
	mockHandler := new(MockHandler)

	// 创建测试上下文
	gin.SetMode(gin.TestMode)
	w := httptest.NewRecorder()
	ctx, _ := gin.CreateTestContext(w)

	// 设置上下文值
	ctx.Set("staff_ids", []string{"staff1", "staff2"})
	ctx.Set("is_super_manage", false)

	// 模拟处理器返回
	baseQuery := elastic.NewBoolQuery().Must(elastic.NewMatchAllQuery())
	mockHandler.On("BuildQuery", ctx, mock.Anything).Return(baseQuery, nil)
	mockHandler.On("PermissionType").Return(1) // 有权限控制

	params := struct{}{}

	query, err := service.buildFinalQuery(ctx, mockHandler, params)

	assert.NoError(t, err)
	assert.NotNil(t, query)
	mockHandler.AssertExpectations(t)
}

func TestService_buildFinalQuery_NoPermission(t *testing.T) {
	service := NewService()
	mockHandler := new(MockHandler)

	// 创建测试上下文
	gin.SetMode(gin.TestMode)
	w := httptest.NewRecorder()
	ctx, _ := gin.CreateTestContext(w)

	// 设置上下文值 - 超级管理员
	ctx.Set("staff_ids", []string{"staff1"})
	ctx.Set("is_super_manage", true)

	// 模拟处理器返回
	baseQuery := elastic.NewBoolQuery().Must(elastic.NewMatchAllQuery())
	mockHandler.On("BuildQuery", ctx, mock.Anything).Return(baseQuery, nil)
	mockHandler.On("PermissionType").Return(1) // 有权限控制，但是超级管理员

	params := struct{}{}

	query, err := service.buildFinalQuery(ctx, mockHandler, params)

	assert.NoError(t, err)
	assert.NotNil(t, query)
	mockHandler.AssertExpectations(t)
}

func TestService_buildFinalQuery_NoPermissionType(t *testing.T) {
	service := NewService()
	mockHandler := new(MockHandler)

	// 创建测试上下文
	gin.SetMode(gin.TestMode)
	w := httptest.NewRecorder()
	ctx, _ := gin.CreateTestContext(w)

	// 模拟处理器返回
	baseQuery := elastic.NewBoolQuery().Must(elastic.NewMatchAllQuery())
	mockHandler.On("BuildQuery", ctx, mock.Anything).Return(baseQuery, nil)
	mockHandler.On("PermissionType").Return(0) // 无权限控制

	params := struct{}{}

	query, err := service.buildFinalQuery(ctx, mockHandler, params)

	assert.NoError(t, err)
	assert.NotNil(t, query)
	mockHandler.AssertExpectations(t)
}

func TestService_createZip(t *testing.T) {
	service := NewService()

	tests := []struct {
		name        string
		tempDir     string
		filePrefix  string
		expectError bool
	}{
		{
			name:        "有效的压缩",
			tempDir:     "/tmp/test_export",
			filePrefix:  "test_files",
			expectError: false,
		},
		{
			name:        "空目录",
			tempDir:     "/tmp/empty_dir",
			filePrefix:  "empty",
			expectError: true, // 没有文件需要压缩
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 注意：这个测试需要实际的文件系统操作
			// 在实际项目中，可能需要使用文件系统mock或临时目录
			zipPath, err := service.createZip(tt.tempDir, tt.filePrefix)

			if tt.expectError {
				assert.Error(t, err)
				assert.Empty(t, zipPath)
			} else {
				// 由于依赖实际文件系统，这里只检查方法调用不panic
				// 实际测试中需要创建临时文件
				assert.NotPanics(t, func() {
					service.createZip(tt.tempDir, tt.filePrefix)
				})
			}
		})
	}
}

func TestService_getAllDataSources(t *testing.T) {
	// 设置数据库mock
	mockDB := testcommon.GetMysqlMock()

	// 设置数据源查询的mock期望
	mockDB.Mock.ExpectQuery("SELECT \\* FROM `data_sources`").
		WillReturnRows(mockDB.Mock.NewRows([]string{"id", "name", "type", "status"}).
			AddRow(1, "测试数据源1", 1, 1).
			AddRow(2, "测试数据源2", 2, 1))

	service := NewService()

	// 执行测试
	sources, err := service.getAllDataSources()

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, sources)
}

func TestService_extractSourceNameFromHit(t *testing.T) {
	service := NewService()

	// 创建测试数据
	allSources := map[uint64]string{
		1: "数据源1",
		2: "数据源2",
		3: "数据源3",
	}

	// 测试用例1：正常情况
	hitData := map[string]interface{}{
		"all_source_ids": []interface{}{1, 2},
	}
	hitJSON, _ := json.Marshal(hitData)
	hit := &elastic.SearchHit{Source: hitJSON}

	result := service.extractSourceNameFromHit(hit, allSources)
	assert.Contains(t, result, "数据源1")
	assert.Contains(t, result, "数据源2")

	// 测试用例2：空的source_ids
	hitData2 := map[string]interface{}{
		"all_source_ids": []interface{}{},
	}
	hitJSON2, _ := json.Marshal(hitData2)
	hit2 := &elastic.SearchHit{Source: hitJSON2}

	result2 := service.extractSourceNameFromHit(hit2, allSources)
	assert.Equal(t, "", result2)

	// 测试用例3：不存在的source_id
	hitData3 := map[string]interface{}{
		"all_source_ids": []interface{}{999},
	}
	hitJSON3, _ := json.Marshal(hitData3)
	hit3 := &elastic.SearchHit{Source: hitJSON3}

	result3 := service.extractSourceNameFromHit(hit3, allSources)
	assert.Equal(t, "", result3)
}

func TestService_handleGeneratedFiles(t *testing.T) {
	service := NewService()

	// 测试用例：空目录
	assert.NotPanics(t, func() {
		service.handleGeneratedFiles("/tmp/empty_dir", "test_prefix")
	})
}

func TestService_needDataSourceColumn(t *testing.T) {
	service := NewService()

	// 测试AssetHandler
	assetHandler := &handlers.AssetHandler{}
	assert.True(t, service.needDataSourceColumn(assetHandler))

	// 测试DeviceHandler
	deviceHandler := &handlers.DeviceHandler{}
	assert.True(t, service.needDataSourceColumn(deviceHandler))

	// 测试其他类型的Handler
	mockHandler := new(MockHandler)
	assert.False(t, service.needDataSourceColumn(mockHandler))
}

// 为测试创建的包装Service
type testService struct {
	Service
	mockNeedDataSourceColumn func(handler Handler) bool
}

// 重写needDataSourceColumn方法用于测试
func (s *testService) needDataSourceColumn(handler Handler) bool {
	if s.mockNeedDataSourceColumn != nil {
		return s.mockNeedDataSourceColumn(handler)
	}
	return s.Service.needDataSourceColumn(handler)
}

func TestService_processHitsAndGenerateFile(t *testing.T) {
	// 使用包装的testService
	service := &testService{
		Service: *NewService(),
	}

	// 创建临时目录
	tempDir := t.TempDir()

	// 测试数据
	allSources := map[uint64]string{
		1: "数据源1",
		2: "数据源2",
	}

	// 创建测试数据
	hit1 := &elastic.SearchHit{
		Source: []byte(`{"all_source_ids": [1], "field1": "value1"}`),
	}
	hit2 := &elastic.SearchHit{
		Source: []byte(`{"all_source_ids": [2], "field1": "value2"}`),
	}
	hits := []*elastic.SearchHit{hit1, hit2}

	// 测试用例1：需要数据源列的处理器（使用mock）
	t.Run("需要数据源列的处理器", func(t *testing.T) {
		// 使用mock对象代替AssetHandler
		mockAssetHandler := new(MockHandler)

		// 设置期望
		mockAssetHandler.On("Headers", mock.Anything).Return([]string{"字段1"})
		mockAssetHandler.On("ParseRow", mock.Anything, mock.Anything).Return([]interface{}{"测试数据"}, nil)
		mockAssetHandler.On("FilePrefix").Return("asset")

		// 设置mock行为
		service.mockNeedDataSourceColumn = func(handler Handler) bool {
			return true // 强制返回true，模拟AssetHandler的行为
		}

		// 执行方法
		err := service.processHitsAndGenerateFile(tempDir, hits, 0, mockAssetHandler, nil, allSources)

		// 验证结果
		assert.NoError(t, err)
		mockAssetHandler.AssertExpectations(t)
	})

	// 测试用例2：不需要数据源列的处理器
	t.Run("不需要数据源列的处理器", func(t *testing.T) {
		mockHandler := new(MockHandler)

		// 设置期望
		mockHandler.On("Headers", mock.Anything).Return([]string{"字段1"})
		mockHandler.On("ParseRow", mock.Anything, mock.Anything).Return([]interface{}{"测试数据"}, nil)
		mockHandler.On("FilePrefix").Return("test")

		// 设置mock行为
		service.mockNeedDataSourceColumn = func(handler Handler) bool {
			return false // 强制返回false
		}

		// 执行方法
		err := service.processHitsAndGenerateFile(tempDir, hits, 0, mockHandler, nil, allSources)

		// 验证结果
		assert.NoError(t, err)
		mockHandler.AssertExpectations(t)
	})

	// 测试用例3：处理空数据
	t.Run("处理空数据", func(t *testing.T) {
		mockHandler := new(MockHandler)

		// 设置期望
		mockHandler.On("ParseRow", mock.Anything, mock.Anything).Return([]interface{}{}, assert.AnError)

		// 执行方法
		err := service.processHitsAndGenerateFile(tempDir, hits, 0, mockHandler, nil, allSources)

		// 验证结果
		assert.NoError(t, err) // 空数据应该不返回错误，只是跳过文件生成
		mockHandler.AssertExpectations(t)
	})
}

func TestService_exportUnified(t *testing.T) {
	// 设置数据库mock
	mockDB := testcommon.GetMysqlMock()

	// 设置数据源查询的mock期望
	mockDB.Mock.ExpectQuery("SELECT \\* FROM `data_sources`").
		WillReturnRows(mockDB.Mock.NewRows([]string{"id", "name", "type", "status"}).
			AddRow(1, "测试数据源1", 1, 1).
			AddRow(2, "测试数据源2", 2, 1))

	service := NewService()

	// 创建测试上下文
	gin.SetMode(gin.TestMode)
	w := httptest.NewRecorder()
	ctx, _ := gin.CreateTestContext(w)

	// 创建mock处理器
	mockHandler := new(MockHandler)
	mockHandler.On("IndexName").Return("test_index")
	mockHandler.On("FilePrefix").Return("test_prefix")
	mockHandler.On("Headers", mock.Anything).Return([]string{"header1"})
	mockHandler.On("ParseRow", mock.Anything, mock.Anything).Return([]interface{}{"data1"}, nil)

	// 创建mock查询
	query := elastic.NewBoolQuery()

	// 设置mock ES服务器
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	// 使用计数器跟踪调用次数
	callCount := 0

	// 注册自定义处理器
	mockServer.RegisterHandler("/test_index/_search", func(w http.ResponseWriter, r *http.Request) {
		callCount++
		w.Header().Set("Content-Type", "application/json")

		if callCount == 1 {
			// 第一次查询，返回有数据的响应
			response := elastic.SearchResult{
				Hits: &elastic.SearchHits{
					TotalHits: &elastic.TotalHits{
						Value:    1,
						Relation: "eq",
					},
					Hits: []*elastic.SearchHit{
						{
							Index: "test_index",
							Id:    "1",
							Source: []byte(`{
								"all_source_ids": [1],
								"field1": "value1"
							}`),
							Sort: []interface{}{1, 2},
						},
					},
				},
			}
			json.NewEncoder(w).Encode(response)
		} else {
			// 后续查询，返回空结果
			response := elastic.SearchResult{
				Hits: &elastic.SearchHits{
					TotalHits: &elastic.TotalHits{
						Value:    0,
						Relation: "eq",
					},
					Hits: []*elastic.SearchHit{},
				},
			}
			json.NewEncoder(w).Encode(response)
		}
	})

	// 设置测试环境
	testcommon.SetTestEnv(true)
	defer testcommon.SetTestEnv(false)

	// 执行测试
	params := struct{}{}
	path, err := service.exportUnified(ctx, mockHandler, query, params)

	// 验证结果
	assert.NoError(t, err)
	assert.NotEmpty(t, path)
	mockHandler.AssertExpectations(t)

	// 清理测试文件
	if path != "" {
		_ = os.Remove(path)
	}
}

// TestService_moveSingleFile 测试移动单个文件的方法
func TestService_moveSingleFile(t *testing.T) {
	service := NewService()

	t.Run("成功移动文件", func(t *testing.T) {
		// 创建临时目录和测试文件
		tempDir := t.TempDir()
		testFileName := "test_file.xlsx"
		testFilePath := filepath.Join(tempDir, testFileName)

		// 创建测试文件
		testContent := []byte("test content for move")
		err := os.WriteFile(testFilePath, testContent, 0644)
		require.NoError(t, err)

		// 测试移动文件
		filePrefix := "test_prefix"
		newPath, err := service.moveSingleFile(tempDir, testFileName, filePrefix)

		// 验证结果
		assert.NoError(t, err)
		assert.NotEmpty(t, newPath)
		assert.True(t, strings.HasSuffix(newPath, "-test_prefix.xlsx"))

		// 验证文件内容
		if err == nil {
			content, readErr := os.ReadFile(newPath)
			if readErr == nil {
				assert.Equal(t, testContent, content)
			}
			// 清理测试文件
			_ = os.Remove(newPath)
		}
	})

	t.Run("Rename失败但copyAndRemove成功", func(t *testing.T) {
		// 创建临时目录和测试文件
		tempDir := t.TempDir()
		testFileName := "test_file.xlsx"
		testFilePath := filepath.Join(tempDir, testFileName)

		// 创建测试文件
		testContent := []byte("test content for copy")
		err := os.WriteFile(testFilePath, testContent, 0644)
		require.NoError(t, err)

		// 创建一个目标路径，使其在某些情况下可能导致rename失败
		// 例如跨文件系统移动文件
		filePrefix := "test_copy_prefix"

		newPath, err := service.moveSingleFile(tempDir, testFileName, filePrefix)

		// 验证结果
		assert.NoError(t, err)
		assert.NotEmpty(t, newPath)
		assert.True(t, strings.HasSuffix(newPath, "-test_copy_prefix.xlsx"))

		// 清理测试文件
		if err == nil {
			_ = os.Remove(newPath)
		}
	})

	t.Run("源文件不存在", func(t *testing.T) {
		tempDir := t.TempDir()
		nonExistentFile := "non_existent.xlsx"
		filePrefix := "test_prefix"

		newPath, err := service.moveSingleFile(tempDir, nonExistentFile, filePrefix)

		// 验证结果
		assert.Error(t, err)
		assert.Empty(t, newPath)
		assert.Contains(t, err.Error(), "rename failed")
	})

	t.Run("目标路径无效", func(t *testing.T) {
		// 创建临时目录和测试文件
		tempDir := t.TempDir()
		testFileName := "test_file.xlsx"
		testFilePath := filepath.Join(tempDir, testFileName)

		// 创建测试文件
		err := os.WriteFile(testFilePath, []byte("test"), 0644)
		require.NoError(t, err)

		// 使用包含无效字符的前缀
		filePrefix := "test/invalid:prefix"

		newPath, err := service.moveSingleFile(tempDir, testFileName, filePrefix)

		if err != nil {
			assert.Empty(t, newPath)
			assert.Contains(t, err.Error(), "rename failed")
		} else {
			// 如果成功了，清理文件
			_ = os.Remove(newPath)
		}
	})
}

// TestCopyAndRemove 测试复制并删除文件的方法
func TestCopyAndRemove(t *testing.T) {
	t.Run("成功复制并删除", func(t *testing.T) {
		// 创建临时目录
		tempDir := t.TempDir()

		// 创建源文件
		srcPath := filepath.Join(tempDir, "source.txt")
		testContent := []byte("test content for copy and remove")
		err := os.WriteFile(srcPath, testContent, 0644)
		require.NoError(t, err)

		// 设置目标路径
		dstPath := filepath.Join(tempDir, "destination.txt")

		// 执行复制并删除
		err = copyAndRemove(srcPath, dstPath)

		// 验证结果
		assert.NoError(t, err)

		// 验证目标文件存在且内容正确
		dstContent, err := os.ReadFile(dstPath)
		assert.NoError(t, err)
		assert.Equal(t, testContent, dstContent)

		// 验证源文件已被删除
		_, err = os.Stat(srcPath)
		assert.True(t, os.IsNotExist(err))
	})

	t.Run("复制失败", func(t *testing.T) {
		// 使用不存在的源文件
		srcPath := "/non/existent/source.txt"
		dstPath := "/tmp/destination.txt"

		err := copyAndRemove(srcPath, dstPath)

		// 验证结果
		assert.Error(t, err)
		// 错误应该来自copyFile
		assert.True(t, os.IsNotExist(err), "应该是文件不存在错误，实际错误: %v", err)
	})

	t.Run("复制成功但删除失败", func(t *testing.T) {
		// 创建临时目录
		tempDir := t.TempDir()

		// 创建源文件
		srcPath := filepath.Join(tempDir, "source.txt")
		testContent := []byte("test content")
		err := os.WriteFile(srcPath, testContent, 0644)
		require.NoError(t, err)

		// 设置目标路径
		dstPath := filepath.Join(tempDir, "destination.txt")

		// 先将源文件设为只读，在某些系统上可能导致删除失败
		err = os.Chmod(srcPath, 0444)
		if err != nil {
			t.Skip("无法设置文件权限，跳过此测试")
		}

		// 执行复制并删除
		err = copyAndRemove(srcPath, dstPath)

		if err != nil {
			// 如果删除失败，验证目标文件仍然存在
			_, statErr := os.Stat(dstPath)
			assert.NoError(t, statErr, "目标文件应该存在，因为复制成功了")
		}

		// 清理：恢复权限并删除文件
		_ = os.Chmod(srcPath, 0644)
		_ = os.Remove(srcPath)
		_ = os.Remove(dstPath)
	})
}

// TestCopyFile 测试复制文件的方法
func TestCopyFile(t *testing.T) {
	t.Run("成功复制文件", func(t *testing.T) {
		// 创建临时目录
		tempDir := t.TempDir()

		// 创建源文件
		srcPath := filepath.Join(tempDir, "source.txt")
		testContent := []byte("test content for copy file")
		err := os.WriteFile(srcPath, testContent, 0644)
		require.NoError(t, err)

		// 设置目标路径
		dstPath := filepath.Join(tempDir, "destination.txt")

		// 执行复制
		err = copyFile(srcPath, dstPath)

		// 验证结果
		assert.NoError(t, err)

		// 验证目标文件存在且内容正确
		dstContent, err := os.ReadFile(dstPath)
		assert.NoError(t, err)
		assert.Equal(t, testContent, dstContent)

		// 验证源文件仍然存在
		srcContent, err := os.ReadFile(srcPath)
		assert.NoError(t, err)
		assert.Equal(t, testContent, srcContent)
	})

	t.Run("源文件不存在", func(t *testing.T) {
		srcPath := "/non/existent/source.txt"
		dstPath := "/tmp/destination.txt"

		err := copyFile(srcPath, dstPath)

		// 验证结果
		assert.Error(t, err)
		// 错误应该来自os.Open
		assert.True(t, os.IsNotExist(err) || strings.Contains(err.Error(), "cannot find"))
	})

	t.Run("目标路径无效", func(t *testing.T) {
		// 创建临时目录和源文件
		tempDir := t.TempDir()
		srcPath := filepath.Join(tempDir, "source.txt")
		err := os.WriteFile(srcPath, []byte("test"), 0644)
		require.NoError(t, err)

		// 使用无效的目标路径（不存在的目录）
		dstPath := "/non/existent/directory/destination.txt"

		err = copyFile(srcPath, dstPath)

		// 验证结果
		assert.Error(t, err)
		// 错误应该来自os.Create
		assert.True(t, os.IsNotExist(err) || strings.Contains(err.Error(), "cannot find") || strings.Contains(err.Error(), "no such file"))
	})

	t.Run("复制大文件", func(t *testing.T) {
		// 创建临时目录
		tempDir := t.TempDir()

		// 创建较大的源文件
		srcPath := filepath.Join(tempDir, "large_source.txt")
		srcFile, err := os.Create(srcPath)
		require.NoError(t, err)

		// 写入大量数据
		largeContent := strings.Repeat("This is a test line for large file copying.\n", 1000)
		_, err = srcFile.WriteString(largeContent)
		require.NoError(t, err)
		srcFile.Close()

		// 设置目标路径
		dstPath := filepath.Join(tempDir, "large_destination.txt")

		// 执行复制
		err = copyFile(srcPath, dstPath)

		// 验证结果
		assert.NoError(t, err)

		// 验证文件大小
		srcInfo, err := os.Stat(srcPath)
		assert.NoError(t, err)
		dstInfo, err := os.Stat(dstPath)
		assert.NoError(t, err)
		assert.Equal(t, srcInfo.Size(), dstInfo.Size())

		// 验证内容（读取部分内容进行比较）
		srcContent, err := os.ReadFile(srcPath)
		assert.NoError(t, err)
		dstContent, err := os.ReadFile(dstPath)
		assert.NoError(t, err)
		assert.Equal(t, srcContent, dstContent)
	})

	t.Run("复制空文件", func(t *testing.T) {
		// 创建临时目录
		tempDir := t.TempDir()

		// 创建空的源文件
		srcPath := filepath.Join(tempDir, "empty_source.txt")
		err := os.WriteFile(srcPath, []byte{}, 0644)
		require.NoError(t, err)

		// 设置目标路径
		dstPath := filepath.Join(tempDir, "empty_destination.txt")

		// 执行复制
		err = copyFile(srcPath, dstPath)

		// 验证结果
		assert.NoError(t, err)

		// 验证目标文件存在且为空
		dstContent, err := os.ReadFile(dstPath)
		assert.NoError(t, err)
		assert.Empty(t, dstContent)
	})
}

func TestService_processAllShards(t *testing.T) {
	// 使用包装的testService
	service := &testService{
		Service: *NewService(),
	}

	// 创建临时目录
	tempDir := t.TempDir()

	// 创建mock处理器
	mockHandler := new(MockHandler)
	mockHandler.On("IndexName").Return("test_index")
	mockHandler.On("Headers", mock.Anything).Return([]string{"header1"})
	mockHandler.On("FilePrefix").Return("test_prefix")
	mockHandler.On("ParseRow", mock.Anything, mock.Anything).Return([]interface{}{"test_data"}, nil)

	// 创建mock查询
	query := elastic.NewBoolQuery()

	// 测试数据源
	allSources := map[uint64]string{
		1: "数据源1",
		2: "数据源2",
	}

	// 设置mock ES服务器
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	// 使用计数器跟踪调用次数
	callCount := 0

	// 注册自定义处理器
	mockServer.RegisterHandler("/test_index/_search", func(w http.ResponseWriter, r *http.Request) {
		callCount++
		w.Header().Set("Content-Type", "application/json")

		if callCount == 1 {
			// 第一次查询，返回有数据的响应
			response := elastic.SearchResult{
				Hits: &elastic.SearchHits{
					TotalHits: &elastic.TotalHits{
						Value:    1,
						Relation: "eq",
					},
					Hits: []*elastic.SearchHit{
						{
							Index: "test_index",
							Id:    "1",
							Source: []byte(`{
								"all_source_ids": [1],
								"field1": "value1"
							}`),
							Sort: []interface{}{1, 2},
						},
					},
				},
			}
			json.NewEncoder(w).Encode(response)
		} else {
			// 后续查询，返回空结果
			response := elastic.SearchResult{
				Hits: &elastic.SearchHits{
					TotalHits: &elastic.TotalHits{
						Value:    0,
						Relation: "eq",
					},
					Hits: []*elastic.SearchHit{},
				},
			}
			json.NewEncoder(w).Encode(response)
		}
	})

	// 设置测试环境
	testcommon.SetTestEnv(true)
	defer testcommon.SetTestEnv(false)

	// 执行测试
	params := struct{}{}
	err := service.processAllShards(tempDir, mockHandler, query, params, allSources)

	// 验证结果
	assert.NoError(t, err)
	mockHandler.AssertExpectations(t)
}

// 集成测试示例（需要实际的依赖）
func TestService_Export_Integration(t *testing.T) {
	// 跳过集成测试，除非在集成测试环境中
	t.Skip("Integration test - requires actual dependencies")

	service := NewService()
	mockHandler := new(MockHandler)

	// 创建测试上下文
	gin.SetMode(gin.TestMode)
	w := httptest.NewRecorder()
	ctx, _ := gin.CreateTestContext(w)

	// 设置上下文值
	ctx.Set("staff_ids", []string{"staff1"})
	ctx.Set("is_super_manage", true)

	// 模拟处理器返回
	mockHandler.On("BuildQuery", ctx, mock.Anything).Return(elastic.NewBoolQuery(), nil)
	mockHandler.On("PermissionType").Return(0)
	mockHandler.On("IndexName").Return("test_index")
	mockHandler.On("Headers", mock.Anything).Return([]string{"header1"})
	mockHandler.On("FilePrefix").Return("test")
	mockHandler.On("ParseRow", mock.Anything, mock.Anything).Return([]interface{}{"data"}, nil)

	params := struct{}{}

	path, err := service.Export(ctx, mockHandler, params)

	assert.NoError(t, err)
	assert.NotEmpty(t, path)
	mockHandler.AssertExpectations(t)
}

// 基准测试
func BenchmarkService_buildFinalQuery(b *testing.B) {
	service := NewService()
	mockHandler := new(MockHandler)

	gin.SetMode(gin.TestMode)
	w := httptest.NewRecorder()
	ctx, _ := gin.CreateTestContext(w)
	ctx.Set("staff_ids", []string{"staff1"})
	ctx.Set("is_super_manage", true)

	baseQuery := elastic.NewBoolQuery().Must(elastic.NewMatchAllQuery())
	mockHandler.On("BuildQuery", ctx, mock.Anything).Return(baseQuery, nil)
	mockHandler.On("PermissionType").Return(0)

	params := struct{}{}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = service.buildFinalQuery(ctx, mockHandler, params)
	}
}
