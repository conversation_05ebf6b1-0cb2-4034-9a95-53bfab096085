package handlers

import (
	"encoding/json"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/olivere/elastic/v7"
	"github.com/stretchr/testify/assert"

	"fobrain/fobrain/app/request/asset_center"
	"fobrain/fobrain/common/localtime"
)

func TestBusinessSystemHandler_IndexName(t *testing.T) {
	handler := NewBusinessSystemHandler(false)
	indexName := handler.IndexName()
	assert.Contains(t, indexName, "business")
}

func TestBusinessSystemHandler_FilePrefix(t *testing.T) {
	handler := NewBusinessSystemHandler(false)
	filePrefix := handler.FilePrefix()
	assert.Equal(t, "业务系统列表", filePrefix)
}

func TestBusinessSystemHandler_PermissionType(t *testing.T) {
	handler := NewBusinessSystemHandler(false)
	permissionType := handler.PermissionType()
	assert.Equal(t, 0, permissionType) // 业务系统导出无权限控制
}

func TestBusinessSystemHandler_Headers(t *testing.T) {
	// 设置数据库mock
	helper := NewTestHelper(t)
	defer helper.Close()
	helper.MockCustomFieldQuery("业务系统", 1)

	handler := NewBusinessSystemHandler(false)
	params := &asset_center.BusinessSystemsSearchRequest{}

	headers := handler.Headers(params)

	expectedHeaders := []string{
		"数据来源",
		"业务系统名称",
		"业务系统地址",
		"业务系统负责人及部门",
		"关联内网IP",
		"关联互联网IP",
		"是否关基设施",
		"是否信创",
		"运行环境",
		"采购类型",
		"重要性",
		"运行状态",
		"等保级别",
		"系统版本",
		"连续性级别",
		"规划用途",
		"首次上报时间",
		"最后上报时间",
	}

	// 检查基本表头
	for i, expected := range expectedHeaders {
		if i < len(headers) {
			assert.Equal(t, expected, headers[i])
		}
	}
}

func TestBusinessSystemHandler_ParseRow(t *testing.T) {
	// 跳过这个测试，因为它依赖于数据库连接
	t.Skip("Skipping test that requires database connection")

	handler := NewBusinessSystemHandler(false)

	// 创建测试业务系统数据
	now := localtime.NewLocalTime(time.Now())
	businessData := map[string]interface{}{
		"name":       "测试业务系统",
		"address":    "http://test.example.com",
		"created_at": now,
		"updated_at": now,
		"person_base": []map[string]interface{}{
			{
				"name": "负责人1",
				"department": []map[string]interface{}{
					{
						"name": "测试部门",
					},
				},
			},
		},
	}

	// 序列化为JSON
	businessJSON, err := json.Marshal(businessData)
	assert.NoError(t, err)

	// 创建模拟的搜索结果
	hit := &elastic.SearchHit{
		Source: businessJSON,
	}

	params := &asset_center.BusinessSystemsSearchRequest{}

	// 调用ParseRow方法
	row, err := handler.ParseRow(hit, params)

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, row)
	assert.Greater(t, len(row), 0)

	// 验证业务系统名称
	assert.Equal(t, "测试业务系统", row[1]) // 业务系统名称在第2列
}

func TestBusinessSystemHandler_BuildQuery(t *testing.T) {
	handler := NewBusinessSystemHandler(false)

	// 创建模拟的gin上下文
	gin.SetMode(gin.TestMode)
	w := httptest.NewRecorder()
	ctx, _ := gin.CreateTestContext(w)

	params := &asset_center.BusinessSystemsSearchRequest{
		Ids: []string{"id1", "id2"},
	}

	// 调用BuildQuery方法
	query, err := handler.BuildQuery(ctx, params)

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, query)
}
