package handlers

import (
	"encoding/json"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/olivere/elastic/v7"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"fobrain/fobrain/common/localtime"
	"fobrain/models/elastic/assets"
	"fobrain/models/elastic/device"
)

// MockDeviceHandler 用于测试的设备处理器
type MockDeviceHandler struct {
	mock.Mock
}

func TestDeviceHandler_IndexName(t *testing.T) {
	handler := NewDeviceHandler()
	indexName := handler.IndexName()
	assert.Contains(t, indexName, "device")
}

func TestDeviceHandler_FilePrefix(t *testing.T) {
	handler := NewDeviceHandler()
	filePrefix := handler.FilePrefix()
	assert.Equal(t, "设备列表", filePrefix)
}

func TestDeviceHandler_PermissionType(t *testing.T) {
	handler := NewDeviceHandler()
	permissionType := handler.PermissionType()
	assert.Equal(t, 3, permissionType) // data_source.SourceTypeDevice
}

func TestDeviceHandler_Headers(t *testing.T) {
	// 设置数据库mock
	helper := NewTestHelper(t)
	defer helper.Close()
	helper.MockCustomFieldQuery("设备", 1)

	handler := NewDeviceHandler()
	params := &DeviceIdsRequest{}

	headers := handler.Headers(params)

	expectedHeaders := []string{
		"唯一值",
		"设备名称",
		"SN",
		"MAC地址",
		"标签",
		"漏洞数量",
		"操作系统",
		"机房",
		"关联内网IP",
		"关联互联网IP",
		"所属区域",
		"业务系统",
		"业务系统负责人",
		"业务部门",
		"运维负责人",
		"运维部门",
		"首次上报时间",
		"最后上报时间",
	}

	// 检查基础表头
	for i, expected := range expectedHeaders {
		assert.Equal(t, expected, headers[i])
	}

	// 表头数量应该至少包含基础字段
	assert.GreaterOrEqual(t, len(headers), len(expectedHeaders))
}

func TestDeviceHandler_BuildQuery(t *testing.T) {
	handler := NewDeviceHandler()
	
	// 创建测试上下文
	gin.SetMode(gin.TestMode)
	w := httptest.NewRecorder()
	ctx, _ := gin.CreateTestContext(w)
	
	tests := []struct {
		name     string
		params   *DeviceIdsRequest
		expected string
	}{
		{
			name: "空参数查询",
			params: &DeviceIdsRequest{
				Ids:     []string{},
				Keyword: "",
			},
			expected: "match_all",
		},
		{
			name: "ID查询",
			params: &DeviceIdsRequest{
				Ids:     []string{"device1", "device2"},
				Keyword: "",
			},
			expected: "terms",
		},
		{
			name: "关键字查询",
			params: &DeviceIdsRequest{
				Ids:     []string{},
				Keyword: "test",
			},
			expected: "match_all", // 会被关键字查询修改
		},
	}
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			query, err := handler.BuildQuery(ctx, tt.params)
			assert.NoError(t, err)
			assert.NotNil(t, query)
			
			// 验证查询类型
			queryMap, err := query.Source()
			assert.NoError(t, err)
			assert.NotNil(t, queryMap)
		})
	}
}

func TestDeviceHandler_ParseRow(t *testing.T) {
	// 跳过这个测试，因为它依赖于数据库连接
	t.Skip("Skipping test that requires database connection")

	handler := NewDeviceHandler()

	// 创建测试设备数据
	now := localtime.NewLocalTime(time.Now())
	deviceData := device.Device{
		Fid:         "test-device-fid",
		HostName:    []string{"test-host"},
		Sn:          []string{"SN123456"},
		Mac:         []string{"00:11:22:33:44:55"},
		Tags:        []string{"tag1", "tag2"},
		PocNum:      5,
		Os:          []string{"Linux"},
		MachineRoom: []string{"Room-A"},
		PrivateIp:   []string{"*************"},
		PublicIp:    []string{"*******"},
		Area:        []int{1, 2},
		AllSourceIds: []uint64{1, 2},
		CreatedAt:   now,
		UpdatedAt:   now,
		CustomFields: map[string]string{
			"custom_field1": "value1",
		},
		Opers: []*device.OperInfo{
			{
				OperInfo: &assets.PersonBase{
					Id:   "oper1",
					Name: "运维人员1",
				},
				OperDepartment: &assets.DepartmentBase{
					Id:   1,
					Name: "运维部门1",
				},
			},
		},
		Business: []*device.Business{
			{
				Business: &assets.Business{
					System: "业务系统1",
					PersonBase: []*assets.PersonBase{
						{
							Id:   "person1",
							Name: "负责人1",
						},
					},
					DepartmentBase: []*assets.DepartmentBase{
						{
							Id:   1,
							Name: "业务部门1",
						},
					},
				},
			},
		},
	}
	
	// 序列化为JSON
	deviceJSON, err := json.Marshal(deviceData)
	assert.NoError(t, err)
	
	// 创建模拟的搜索结果
	hit := &elastic.SearchHit{
		Source: deviceJSON,
	}
	
	params := &DeviceIdsRequest{}
	
	// 执行解析
	result, err := handler.ParseRow(hit, params)
	assert.NoError(t, err)
	assert.NotNil(t, result)
	
	// 验证结果长度（18个基础字段 + 自定义字段）
	assert.GreaterOrEqual(t, len(result), 18)

	// 验证具体字段值
	assert.Equal(t, "test-device-fid", result[0])                    // 唯一值
	assert.Equal(t, "test-host", result[1])                          // 设备名称
	assert.Equal(t, "SN123456", result[2])                          // SN
	assert.Equal(t, "00:11:22:33:44:55", result[3])                 // MAC地址
	assert.Equal(t, "tag1, tag2", result[4])                        // 标签
	assert.Equal(t, int64(5), result[5])                            // 漏洞数量
	assert.Equal(t, "Linux", result[6])                             // 操作系统
	assert.Equal(t, "Room-A", result[7])                            // 机房
	assert.Equal(t, "*************", result[8])                    // 关联内网IP
	assert.Equal(t, "*******", result[9])                         // 关联互联网IP
}

func TestDeviceHandler_ParseRow_EmptyData(t *testing.T) {
	// 跳过这个测试，因为它依赖于数据库连接
	t.Skip("Skipping test that requires database connection")

	handler := NewDeviceHandler()
	
	// 创建空设备数据
	deviceData := device.Device{
		Fid:          "",
		HostName:     []string{},
		Sn:           []string{},
		Mac:          []string{},
		Tags:         []string{},
		PocNum:       0,
		Os:           []string{},
		MachineRoom:  []string{},
		PrivateIp:    []string{},
		PublicIp:     []string{},
		Area:         []int{},
		AllSourceIds: []uint64{},
		Opers:        []*device.OperInfo{},
		Business:     []*device.Business{},
		CustomFields: map[string]string{},
	}
	
	deviceJSON, err := json.Marshal(deviceData)
	assert.NoError(t, err)
	
	hit := &elastic.SearchHit{
		Source: deviceJSON,
	}
	
	params := &DeviceIdsRequest{}
	
	result, err := handler.ParseRow(hit, params)
	assert.NoError(t, err)
	assert.NotNil(t, result)
	
	// 验证空值处理
	assert.Equal(t, "", result[0])  // 唯一值
	assert.Equal(t, "", result[1])  // 设备名称
	assert.Equal(t, "", result[2])  // SN
	assert.Equal(t, "", result[3])  // MAC地址
	assert.Equal(t, "", result[4])  // 标签
	assert.Equal(t, int64(0), result[5])  // 漏洞数量
}

func TestDeviceHandler_ParseRow_InvalidJSON(t *testing.T) {
	handler := NewDeviceHandler()
	
	// 创建无效的JSON数据
	hit := &elastic.SearchHit{
		Source: []byte(`{"invalid": json}`),
	}
	
	params := &DeviceIdsRequest{}
	
	result, err := handler.ParseRow(hit, params)
	assert.Error(t, err)
	assert.Nil(t, result)
}

// 基准测试
func BenchmarkDeviceHandler_ParseRow(b *testing.B) {
	handler := NewDeviceHandler()
	
	deviceData := device.Device{
		Fid:         "test-device-fid",
		HostName:    []string{"test-host"},
		Sn:          []string{"SN123456"},
		Mac:         []string{"00:11:22:33:44:55"},
		Tags:        []string{"tag1", "tag2"},
		PocNum:      5,
		Os:          []string{"Linux"},
		MachineRoom: []string{"Room-A"},
		PrivateIp:   []string{"*************"},
		PublicIp:    []string{"*******"},
		Area:        []int{1, 2},
		AllSourceIds: []uint64{1, 2},
		CustomFields: map[string]string{
			"custom_field1": "value1",
		},
	}
	
	deviceJSON, _ := json.Marshal(deviceData)
	hit := &elastic.SearchHit{Source: deviceJSON}
	params := &DeviceIdsRequest{}
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = handler.ParseRow(hit, params)
	}
}
