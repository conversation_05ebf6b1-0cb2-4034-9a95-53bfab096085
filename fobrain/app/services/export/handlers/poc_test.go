package handlers

import (
	"encoding/json"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/olivere/elastic/v7"
	"github.com/stretchr/testify/assert"

	"fobrain/fobrain/common/localtime"
	"fobrain/models/elastic/assets"
	"fobrain/models/elastic/poc"
)

func TestPocHandler_IndexName(t *testing.T) {
	handler := NewPocHandler("")
	indexName := handler.IndexName()
	assert.Contains(t, indexName, "poc")
}

func TestPocHandler_FilePrefix(t *testing.T) {
	handler := NewPocHandler("")
	filePrefix := handler.FilePrefix()
	assert.Equal(t, "漏洞列表", filePrefix)
}

func TestPocHandler_PermissionType(t *testing.T) {
	handler := NewPocHandler("")
	permissionType := handler.PermissionType()
	assert.Equal(t, 2, permissionType) // data_source.SourceTypePoc
}

func TestPocHandler_Headers(t *testing.T) {
	// 设置数据库mock
	helper := NewTestHelper(t)
	defer helper.Close()
	helper.MockCustomFieldQuery("漏洞", 1)

	handler := NewPocHandler("")
	params := &ThreatIdsRequest{}

	headers := handler.Headers(params)

	expectedHeaders := []string{
		"漏洞名称",
		"IP地址",
		"端口",
		"主机名",
		"所属区域",
		"漏洞地址",
		"漏洞类型",
		"漏洞等级",
		"漏洞状态",
		"漏洞超期时间",
		"风险值",
		"业务系统",
		"业务系统负责人",
		"业务部门",
		"修复负责人",
		"抄送人",
		"运维人员",
		"运维部门",
		"漏洞详情",
		"修复建议",
		"修复优先级",
		"CVE编号",
		"CNVD编号",
		"CNNVD编号",
		"是否PoC漏洞",
		"是否存在PoC",
		"是否存在EXP",
		"数据源更新时间",
		"首次上报时间",
		"最后上报时间",
		"描述",
	}

	// 检查基础表头
	for i, expected := range expectedHeaders {
		assert.Equal(t, expected, headers[i])
	}

	// 表头数量应该至少包含基础字段
	assert.GreaterOrEqual(t, len(headers), len(expectedHeaders))
}

func TestPocHandler_BuildQuery(t *testing.T) {
	handler := NewPocHandler("")

	// 创建测试上下文
	gin.SetMode(gin.TestMode)
	w := httptest.NewRecorder()
	ctx, _ := gin.CreateTestContext(w)

	tests := []struct {
		name     string
		params   *ThreatIdsRequest
		expected string
	}{
		{
			name: "空参数查询",
			params: &ThreatIdsRequest{
				Ids:     []string{},
				Keyword: "",
			},
			expected: "match_all",
		},
		{
			name: "ID查询",
			params: &ThreatIdsRequest{
				Ids:     []string{"poc1", "poc2"},
				Keyword: "",
			},
			expected: "terms",
		},
		{
			name: "设备触发的关键字查询",
			params: &ThreatIdsRequest{
				Ids:     []string{"poc1"},
				Keyword: "test",
			},
			expected: "terms", // 会被关键字查询修改
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.name == "设备触发的关键字查询" {
				handler = NewPocHandler("device")
			}

			query, err := handler.BuildQuery(ctx, tt.params)
			assert.NoError(t, err)
			assert.NotNil(t, query)

			// 验证查询类型
			queryMap, err := query.Source()
			assert.NoError(t, err)
			assert.NotNil(t, queryMap)
		})
	}
}

func TestPocHandler_ParseRow(t *testing.T) {
	// 跳过这个测试，因为它依赖于数据库和ES连接
	t.Skip("Skipping test that requires database and ES connection")

	handler := NewPocHandler("")

	// 创建测试漏洞数据
	now := localtime.NewLocalTime(time.Now())
	limitDate := localtime.NewLocalTime(time.Now())
	pocData := poc.Poc{
		Id:             "poc-test-id",
		Name:           "测试漏洞",
		Ip:             "*************",
		Port:           80,
		HostName:       []string{"test-host"},
		Area:           1,
		Url:            "http://test.com/vuln",
		VulType:        "SQL注入",
		Level:          3, // 高危
		Status:         1, // 新增
		RiskNum:        85,
		Details:        "漏洞详情描述",
		Suggestions:    "修复建议",
		RepairPriority: "高",
		Cve:            "CVE-2023-1234",
		Cnvd:           "CNVD-2023-1234",
		Cnnvd:          "CNNVD-2023-1234",
		HasExp:         1, // 存在EXP
		HasPoc:         1, // 存在PoC
		IsPoc:          1, // 是PoC漏洞
		Describe:       "漏洞描述",
		LastResponseAt: now,
		CreatedAt:      now,
		UpdatedAt:      now,
		LimitDate:      limitDate,
		AllSourceIds:   []uint64{1, 2},
		CustomFields: map[string]string{
			"custom_field1": "value1",
		},
		Person: map[string]string{
			"name":   "修复负责人",
			"mobile": "***********",
		},
		CcPerson: map[string]string{
			"fid": "cc-person-fid",
		},
		Business: []*assets.Business{
			{
				System: "测试业务系统",
				PersonBase: []*assets.PersonBase{
					{
						Id:   "person1",
						Name: "业务负责人",
					},
				},
				DepartmentBase: []*assets.DepartmentBase{
					{
						Id:   1,
						Name: "业务部门",
					},
				},
			},
		},
		OperInfo: []*assets.PersonBase{
			{
				Id:   "oper1",
				Name: "运维人员",
			},
		},
		OperDepartment: []*assets.DepartmentBase{
			{
				Id:   1,
				Name: "运维部门",
			},
		},
	}

	// 序列化为JSON
	pocJSON, err := json.Marshal(pocData)
	assert.NoError(t, err)

	// 创建模拟的搜索结果
	hit := &elastic.SearchHit{
		Source: pocJSON,
	}

	params := &ThreatIdsRequest{}

	// 执行解析
	result, err := handler.ParseRow(hit, params)
	assert.NoError(t, err)
	assert.NotNil(t, result)

	// 验证结果长度（32个基础字段 + 自定义字段）
	assert.GreaterOrEqual(t, len(result), 32)

	// 验证具体字段值
	assert.Contains(t, result[0].(string), "")         // 数据源（需要mock数据源查询）
	assert.Equal(t, "测试漏洞", result[1])                 // 漏洞名称
	assert.Equal(t, "*************", result[2])        // IP地址
	assert.Equal(t, "80", result[3])                   // 端口
	assert.Equal(t, "test-host", result[4])            // 主机名
	assert.Equal(t, "http://test.com/vuln", result[6]) // 漏洞地址
	assert.Equal(t, "SQL注入", result[7])                // 漏洞类型
	assert.Equal(t, int64(85), result[11])             // 风险值
	assert.Equal(t, "修复负责人-***********", result[15])   // 修复负责人
	assert.Equal(t, "漏洞详情描述", result[19])              // 漏洞详情
	assert.Equal(t, "修复建议", result[20])                // 修复建议
	assert.Equal(t, "高", result[21])                   // 修复优先级
	assert.Equal(t, "CVE-2023-1234", result[22])       // CVE编号
	assert.Equal(t, "CNVD-2023-1234", result[23])      // CNVD编号
	assert.Equal(t, "CNNVD-2023-1234", result[24])     // CNNVD编号
	assert.Equal(t, "漏洞描述", result[31])                // 描述
}

func TestPocHandler_ParseRow_EmptyPort(t *testing.T) {
	// 跳过这个测试，因为它依赖于数据库连接
	t.Skip("Skipping test that requires database connection")

	handler := NewPocHandler("")

	// 创建端口为0的漏洞数据
	pocData := poc.Poc{
		Id:   "poc-test-id",
		Name: "测试漏洞",
		Ip:   "*************",
		Port: 0, // 端口为0
	}

	pocJSON, err := json.Marshal(pocData)
	assert.NoError(t, err)

	hit := &elastic.SearchHit{
		Source: pocJSON,
	}

	params := &ThreatIdsRequest{}

	result, err := handler.ParseRow(hit, params)
	assert.NoError(t, err)
	assert.NotNil(t, result)

	// 验证端口为空字符串
	assert.Equal(t, "", result[3]) // 端口
}

func TestPocHandler_ParseRow_NoLimitDate(t *testing.T) {
	// 跳过这个测试，因为它依赖于数据库连接
	t.Skip("Skipping test that requires database connection")

	handler := NewPocHandler("")

	// 创建没有限期日期的漏洞数据
	pocData := poc.Poc{
		Id:        "poc-test-id",
		Name:      "测试漏洞",
		Ip:        "*************",
		LimitDate: nil, // 没有限期日期
	}

	pocJSON, err := json.Marshal(pocData)
	assert.NoError(t, err)

	hit := &elastic.SearchHit{
		Source: pocJSON,
	}

	params := &ThreatIdsRequest{}

	result, err := handler.ParseRow(hit, params)
	assert.NoError(t, err)
	assert.NotNil(t, result)

	// 验证漏洞超期时间为空字符串
	assert.Equal(t, "", result[10]) // 漏洞超期时间
}

func TestPocHandler_ParseRow_InvalidJSON(t *testing.T) {
	handler := NewPocHandler("")

	// 创建无效的JSON数据
	hit := &elastic.SearchHit{
		Source: []byte(`{"invalid": json}`),
	}

	params := &ThreatIdsRequest{}

	result, err := handler.ParseRow(hit, params)
	assert.Error(t, err)
	assert.Nil(t, result)
}

func TestPocHandler_Trigger(t *testing.T) {
	tests := []struct {
		name     string
		trigger  string
		expected string
	}{
		{
			name:     "普通触发",
			trigger:  "",
			expected: "",
		},
		{
			name:     "设备触发",
			trigger:  "device",
			expected: "device",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			handler := NewPocHandler(tt.trigger)
			assert.Equal(t, tt.expected, handler.trigger)
		})
	}
}

// 基准测试
func BenchmarkPocHandler_ParseRow(b *testing.B) {
	handler := NewPocHandler("")

	pocData := poc.Poc{
		Id:       "poc-test-id",
		Name:     "测试漏洞",
		Ip:       "*************",
		Port:     80,
		HostName: []string{"test-host"},
		Area:     1,
		VulType:  "SQL注入",
		Level:    3,
		Status:   1,
		RiskNum:  85,
	}

	pocJSON, _ := json.Marshal(pocData)
	hit := &elastic.SearchHit{Source: pocJSON}
	params := &ThreatIdsRequest{}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = handler.ParseRow(hit, params)
	}
}
