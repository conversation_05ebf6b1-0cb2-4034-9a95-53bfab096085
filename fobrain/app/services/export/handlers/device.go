package handlers

import (
	"encoding/json"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/olivere/elastic/v7"

	"fobrain/fobrain/app/services/network_area"
	filtrate "fobrain/models/elastic"
	"fobrain/models/elastic/device"
	"fobrain/models/mysql/custom_column"
	"fobrain/models/mysql/data_source"
	"fobrain/pkg/utils"
)

// DeviceIdsRequest 设备导出请求参数
type DeviceIdsRequest struct {
	Ids                 []string `json:"ids" form:"ids" uri:"ids" validate:"" zh:"实体设备IDS"`
	Keyword             string   `json:"keyword" form:"keyword" uri:"keyword" validate:"omitempty,max=100" zh:"模糊搜索"`
	SourceIds           []uint64 `json:"source_ids" form:"source_ids" uri:"source_ids" zh:"数据源ids"`
	DeviceNames         []string `json:"device_names" form:"device_names" uri:"device_names" zh:"设备名称"`
	DeviceTypes         []string `json:"device_types" form:"device_types" uri:"device_types" zh:"设备类型"`
	Sn                  []string `json:"sn" form:"sn" uri:"sn" zh:"sn"`
	Mac                 []string `json:"mac" form:"mac" uri:"mac" zh:"mac"`
	Os                  []string `json:"os" form:"os" uri:"os" zh:"os"`
	MachineRoom         []string `json:"machine_room" form:"machine_room" uri:"machine_room" zh:"机房"`
	PrivateIp           []string `json:"private_ip" form:"private_ip" uri:"private_ip" zh:"内网IP"`
	PublicIp            []string `json:"public_ip" form:"public_ip" uri:"public_ip" zh:"互联网IP"`
	Area                []uint64 `json:"area" form:"area" uri:"area" zh:"所属区域"`
	Oper                []string `json:"oper" form:"oper" uri:"oper" zh:"运维负责人"`
	CreatedAt           []string `json:"created_at" form:"created_at" uri:"created_at" zh:"首次上报时间"`
	UpdatedAt           []string `json:"updated_at" form:"updated_at" uri:"updated_at" zh:"最后上报时间"`
	OperationTypeString string   `json:"operation_type_string" form:"operation_type_string" uri:"operation_type_string"  validate:"omitempty,oneof=in not_in == !== null not_null = !="  zh:"操作类型"`
	SearchCondition     []string `json:"search_condition" form:"search_condition" uri:"search_condition"  zh:"搜索条件"`
}

type DeviceHandler struct{}

func NewDeviceHandler() *DeviceHandler {
	return &DeviceHandler{}
}

func (h *DeviceHandler) IndexName() string {
	return device.NewDeviceModel().IndexName()
}

func (h *DeviceHandler) BuildQuery(ctx *gin.Context, params interface{}) (*elastic.BoolQuery, error) {
	p := params.(*DeviceIdsRequest)

	boolQuery := elastic.NewBoolQuery()

	// ID查询
	if len(utils.CompactStrings(p.Ids)) > 0 {
		boolQuery = boolQuery.Must(elastic.NewTermsQueryFromStrings("id", p.Ids...))
	} else {
		boolQuery = boolQuery.Must(elastic.NewMatchAllQuery())
	}

	// 关键字查询
	if p.Keyword != "" {
		boolQuery = device.NewDeviceModel().NewKeywordQuery(p.Keyword, boolQuery)
	}

	// 高级筛选
	if len(p.SearchCondition) > 0 && p.SearchCondition[0] != "" {
		conditions, err := filtrate.ParseQueryConditions(p.SearchCondition)
		if err != nil {
			return nil, err
		}
		for _, condition := range conditions {
			boolQuery = filtrate.BuildBoolQuery(condition.Field, condition.OperationTypeString, condition.LogicalConnective, condition.Value, boolQuery)
		}
	}

	return boolQuery, nil
}

func (h *DeviceHandler) Headers(params interface{}) []string {
	header := []string{
		"唯一值",
		"设备名称",
		"SN",
		"MAC地址",
		"标签",
		"漏洞数量",
		"操作系统",
		"机房",
		"关联内网IP",
		"关联互联网IP",
		"所属区域",
		"业务系统",
		"业务系统负责人",
		"业务部门",
		"运维负责人",
		"运维部门",
		"首次上报时间",
		"最后上报时间",
	}

	// 添加自定义字段名称
	customFields, err := custom_column.NewCustomFieldMetaModel().GetByModuleType(custom_column.CustomFieldMetaModuleTypeDevice, false)
	if err == nil {
		for _, customField := range customFields {
			header = append(header, customField.DisplayName)
		}
	}

	return header
}

func (h *DeviceHandler) ParseRow(hit *elastic.SearchHit, params interface{}) ([]interface{}, error) {
	deviceData := device.Device{}
	if err := json.Unmarshal(hit.Source, &deviceData); err != nil {
		return nil, err
	}

	// 获取区域
	networkAreas, _ := network_area.GetNetWorkAreaList()
	deviceArea := make([]string, 0)
	for _, networkAreaId := range deviceData.Area {
		for _, networkArea := range networkAreas {
			if networkAreaId == int(networkArea.Id) {
				deviceArea = append(deviceArea, networkArea.Name)
				break
			}
		}
	}

	// 处理业务系统和运维信息
	var businessName, businessPerson, businessDepartment, operDepartment, opers []string

	for _, o := range deviceData.Opers {
		if o.OperInfo != nil {
			opers = append(opers, o.OperInfo.Name)
		}
		if o.OperDepartment != nil {
			operDepartment = append(operDepartment, o.OperDepartment.Name)
		}
	}

	for _, business := range deviceData.Business {
		if business == nil || business.Business == nil {
			continue
		}
		b := business.Business
		businessName = append(businessName, b.System)
		for _, v := range b.PersonBase {
			if v.Name != "" {
				businessPerson = append(businessPerson, v.Name)
			}
			if v.FindInfo != nil {
				for _, t := range v.FindInfo {
					businessPerson = append(businessPerson, t.SourceValue)
				}
			}

		}
		for _, v := range b.DepartmentBase {
			businessDepartment = append(businessDepartment, v.Name)
		}
	}

	data := []interface{}{
		deviceData.Fid,
		strings.Join(deviceData.HostName, ", "),
		strings.Join(deviceData.Sn, ", "),
		strings.Join(deviceData.Mac, ", "),
		strings.Join(deviceData.Tags, ", "),
		deviceData.PocNum,
		strings.Join(deviceData.Os, ", "),
		strings.Join(deviceData.MachineRoom, ", "),
		strings.Join(deviceData.PrivateIp, ", "),
		strings.Join(deviceData.PublicIp, ", "),
		strings.Join(deviceArea, ", "),
		strings.Join(utils.ListDistinct(businessName), ", "),
		strings.Join(utils.ListDistinct(businessPerson), ", "),
		strings.Join(utils.ListDistinct(businessDepartment), ", "),
		strings.Join(utils.ListDistinct(opers), ", "),
		strings.Join(utils.ListDistinct(operDepartment), ", "),
		deviceData.CreatedAt,
		deviceData.UpdatedAt,
	}

	// 添加自定义字段值
	customFields, err := custom_column.NewCustomFieldMetaModel().GetByModuleType(custom_column.CustomFieldMetaModuleTypeDevice, false)
	if err == nil {
		for _, customField := range customFields {
			if value, ok := deviceData.CustomFields[customField.FieldKey]; ok {
				data = append(data, value)
			} else {
				data = append(data, "")
			}
		}
	}

	return data, nil
}

func (h *DeviceHandler) FilePrefix() string {
	return "设备列表"
}

func (h *DeviceHandler) PermissionType() int {
	return data_source.SourceTypeDevice
}
