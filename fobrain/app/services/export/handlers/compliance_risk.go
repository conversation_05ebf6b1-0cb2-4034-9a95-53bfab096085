package handlers

import (
	"encoding/json"
	"fobrain/fobrain/app/services/permission"
	"fobrain/initialize/mysql"
	"fobrain/models/elastic/compliance_monitor"
	mysqlComplianceMonitor "fobrain/models/mysql/compliance_monitor"
	"fobrain/models/mysql/data_source"
	pgidservice "fobrain/services/people_pgid"
	"github.com/gin-gonic/gin"
	"github.com/olivere/elastic/v7"
	"strconv"
	"strings"
)

type ComplianceRiskHandler struct {
}

func NewComplianceRiskHandler() *ComplianceRiskHandler {
	return &ComplianceRiskHandler{}
}

func (cr *ComplianceRiskHandler) IndexName() string {
	return compliance_monitor.NewComplianceMonitorTaskRecords().IndexName()
}

func (cr *ComplianceRiskHandler) BuildQuery(ctx *gin.Context, params interface{}) (*elastic.BoolQuery, error) {
	boolQuery := elastic.NewBoolQuery()

	permissionQuery, err := permission.NewPermissionService().ProcessElasticsearchQuery(ctx, data_source.SourceTypeAsset)
	if err != nil {
		return nil, err
	}
	if permissionQuery != nil {
		boolQuery = boolQuery.Must(permissionQuery)
	}

	boolQuery = boolQuery.MustNot(elastic.NewExistsQuery("deleted_at"))

	return boolQuery, nil
}

func (cr *ComplianceRiskHandler) Headers(params interface{}) []string {
	header := []string{
		"IP地址",
		"命中规则",
		"端口",
		"协议",
		"组件信息",
		"关联实体设备",
		"流转状态",
		"修复负责人",
		"抄送人",
		"修复超期时间",
		"业务系统",
		"业务系统负责人",
		"业务部门",
		"运维人员",
		"运维部门",
	}

	return header
}

func (cr *ComplianceRiskHandler) ParseRow(hit *elastic.SearchHit, params interface{}) ([]interface{}, error) {
	complianceSource := compliance_monitor.ComplianceMonitorTaskRecords{}
	if err := json.Unmarshal(hit.Source, &complianceSource); err != nil {
		return nil, err
	}

	// 修复负责人
	var repair = "-"
	if complianceSource.PersonInfo != nil && len(complianceSource.PersonInfo) > 0 {
		var names []string
		for _, person := range complianceSource.PersonInfo {
			if person.Name != "" {
				names = append(names, person.Name)
			}
		}
		if len(names) > 0 {
			repair = strings.Join(names, ",")
		}
	}

	// 设备
	var rules []string
	if complianceSource.RuleInfos != nil {
		for _, rule := range complianceSource.RuleInfos {
			if rule.Product != "" {
				rules = append(rules, rule.Product)
			}
		}
	}
	ruleStr := strings.Join(rules, ",")

	// 端口
	var ports []string
	if complianceSource.Ports != nil {
		for _, port := range complianceSource.Ports {
			if port.Port > 0 {
				ports = append(ports, strconv.Itoa(port.Port))
			}
		}
	}
	portStr := strings.Join(ports, ",")

	// 协议
	var protocols []string
	if complianceSource.Ports != nil {
		for _, port := range complianceSource.Ports {
			if port.Protocol != "" {
				protocols = append(protocols, port.Protocol)
			}
		}
	}
	protocolStr := strings.Join(protocols, ",")

	// 抄送人
	var ccPerson string
	if complianceSource.CcPerson != nil {
		if fid, ok := complianceSource.CcPerson["fid"]; ok {
			pgid, err := pgidservice.GetPgidByFid(fid)
			if err == nil {
				ccPerson = pgid
			}
		}
	}

	// 业务系统
	var businessNames []string
	if complianceSource.Business != nil {
		for _, business := range complianceSource.Business {
			if business.System != "" {
				businessNames = append(businessNames, business.System)
			}
		}
	}
	businessStr := strings.Join(businessNames, ",")

	// 业务系统负责人
	var businessOwners []string
	if complianceSource.Business != nil {
		for _, business := range complianceSource.Business {
			if business.PersonBase != nil {
				for _, person := range business.PersonBase {
					if person.Name != "" {
						businessOwners = append(businessOwners, person.Name)
					}
				}
			}
		}
	}
	businessOwnerStr := strings.Join(businessOwners, ",")

	// 业务部门
	var businessDepts []string
	if complianceSource.BusinessDepartment != nil {
		for _, dept := range complianceSource.BusinessDepartment {
			if dept.Name != "" {
				businessDepts = append(businessDepts, dept.Name)
			}
		}
	}
	businessDeptStr := strings.Join(businessDepts, ",")

	// 运维人员
	var operNames []string
	if complianceSource.OperInfo != nil {
		for _, oper := range complianceSource.OperInfo {
			if oper.Name != "" {
				operNames = append(operNames, oper.Name)
			}
		}
	}
	operStr := strings.Join(operNames, ",")

	// 运维部门
	var operDepts []string
	if complianceSource.OperDepartment != nil {
		for _, dept := range complianceSource.OperDepartment {
			if dept.Name != "" {
				operDepts = append(operDepts, dept.Name)
			}
		}
	}
	operDeptStr := strings.Join(operDepts, ",")

	// 组件信息
	var components []string
	if complianceSource.RuleInfos != nil {
		for _, rule := range complianceSource.RuleInfos {
			if rule.Product != "" {
				components = append(components, rule.Product)
			}
		}
	}
	componentStr := strings.Join(components, ",")
	monitorItem, err := mysqlComplianceMonitor.NewComplianceMonitorModel().First(mysql.WithWhere("id = ?", complianceSource.ComplianceMonitorId))
	if err != nil {
		return nil, err
	}

	data := []interface{}{
		complianceSource.Ip, // IP地址
		monitorItem.Name,
		portStr,      // 端口
		protocolStr,  // 协议
		componentStr, // 组件信息
		ruleStr,      // 设备
		cr.getFlowStatusDesc(complianceSource.FlowStatus), // 流转状态
		repair,   // 修复负责人
		ccPerson, // 抄送人
		func() string {
			if complianceSource.LimitDate != nil {
				return complianceSource.LimitDate.DateString()
			}
			return ""
		}(),
		businessStr,      // 业务系统
		businessOwnerStr, // 业务系统负责人
		businessDeptStr,  // 业务部门
		operStr,          // 运维人员
		operDeptStr,      // 运维部门
	}

	return data, nil
}

// getFlowStatusDesc 获取流转状态描述
func (cr *ComplianceRiskHandler) getFlowStatusDesc(status int) string {
	// 合规风险流转状态
	statusMap := map[int]string{
		0:  "新增",
		1:  "复现",
		10: "待修复",
		11: "待修复",
		12: "延时",
		13: "超时",
		14: "复测中",
		15: "复测未通过",
		16: "催促",
		17: "待复测",
		30: "复测通过",
		40: "误报",
		41: "无法修复",
	}

	if desc, ok := statusMap[status]; ok {
		return desc
	}
	return "未知"
}

func (cr *ComplianceRiskHandler) FilePrefix() string {
	return "合规风险列表"
}

func (cr *ComplianceRiskHandler) PermissionType() int {
	return data_source.SourceTypeAsset
}
