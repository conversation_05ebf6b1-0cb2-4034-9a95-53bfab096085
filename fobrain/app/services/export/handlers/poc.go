package handlers

import (
	"encoding/json"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/olivere/elastic/v7"

	"fobrain/fobrain/app/repository/threat"
	"fobrain/fobrain/app/services/network_area"
	"fobrain/models/elastic/poc"
	"fobrain/models/mysql/custom_column"
	"fobrain/models/mysql/data_source"
	"fobrain/pkg/utils"
	pgidservice "fobrain/services/people_pgid"
)

// CommonThreatRequest 威胁请求参数
type CommonThreatRequest struct {
	SourceIds           []uint64 `json:"source_ids" form:"source_ids" uri:"source_ids" zh:"数据源"`
	ThreatName          []string `json:"threat_name" form:"threat_name" uri:"threat_name" zh:"漏洞名称"`
	Ips                 []string `json:"ips" form:"ips" uri:"ips"  zh:"IP地址"`
	Ports               []uint64 `json:"ports" form:"ports" uri:"ports"  zh:"端口"`
	Area                []uint64 `json:"area" form:"area" uri:"area"  zh:"所属区域"`
	ThreatUrl           []string `json:"threat_url" form:"threat_url" uri:"threat_url" zh:"漏洞地址"`
	ThreatType          []string `json:"threat_type" form:"threat_type" uri:"threat_type" zh:"漏洞类型"`
	ThreatLevel         []uint64 `json:"threat_level" form:"threat_level" uri:"threat_level" zh:"漏洞等级 低危：1 中危：2 高危：3 严重：4 未知：5-【策略】"`
	ThreatStatus        []uint64 `json:"threat_status" form:"threat_status" uri:"threat_status" zh:"漏洞状态-【唯一】漏洞流转状态：1新增 2复现 3修复 4未修复 5误报"`
	Risk                []uint64 `json:"risk" form:"risk" uri:"risk" zh:"风险值"`
	ThreatRepairPerson  []string `json:"threat_repair_person" form:"threat_repair_person" uri:"threat_repair_person" zh:"漏洞修复负责人"`
	RepairPriority      []string `json:"repair_priority" form:"repair_priority" uri:"repair_priority" zh:"修复优先级"`
	FirstTime           []string `json:"first_time" form:"first_time" uri:"first_time"  zh:"首次上报时间"`
	FinallyTime         []string `json:"finally_time" form:"finally_time" uri:"finally_time"  zh:"最后上报时间"`
	Field               string   `json:"field" form:"field" uri:"field"  zh:"排序字段"`
	Order               string   `json:"order" form:"order" uri:"order"  zh:"排序方式"`
	OperationTypeString string   `json:"operation_type_string" form:"operation_type_string" uri:"operation_type_string" validate:"omitempty,oneof=in not_in == !== null not_null = !="  zh:"操作类型"`
	DataRange           int      `json:"data_range" form:"data_range" uri:"data_range"  zh:"数据范围 1未处理 2已处理 3回收站"`
	SearchCondition     []string `json:"search_condition" form:"search_condition" uri:"search_condition"  zh:"搜索条件"`
}

// ThreatIdsRequest 威胁导出请求参数
type ThreatIdsRequest struct {
	Ids     []string `json:"ids" form:"ids" uri:"ids" validate:"" zh:"漏洞IDS"`
	Keyword string   `json:"keyword" form:"keyword" uri:"keyword" zh:"模糊搜索"`
	CommonThreatRequest
}

type PocHandler struct {
	trigger string
}

func NewPocHandler(trigger string) *PocHandler {
	return &PocHandler{trigger: trigger}
}

func (h *PocHandler) IndexName() string {
	return poc.NewPoc().IndexName()
}

func (h *PocHandler) BuildQuery(ctx *gin.Context, params interface{}) (*elastic.BoolQuery, error) {
	p := params.(*ThreatIdsRequest)

	boolQuery := elastic.NewBoolQuery()

	// ID查询
	if len(utils.CompactStrings(p.Ids)) > 0 {
		boolQuery = boolQuery.Must(elastic.NewTermsQueryFromStrings("id", p.Ids...))
		// 如果是设备触发，则添加关键字查询
		if h.trigger == "device" && p.Keyword != "" {
			boolQuery = poc.NewPoc().NewKeywordQuery(p.Keyword, boolQuery)
		}
	} else if p.Keyword != "" {
		// 没有IDs但有关键字时，使用关键字查询
		boolQuery = poc.NewPoc().NewKeywordQuery(p.Keyword, boolQuery)
	} else {
		boolQuery = boolQuery.Must(elastic.NewMatchAllQuery())
	}

	// 添加IP查询条件（必须在BuildBoolQueryForThreat之前处理）
	ips := utils.ListDistinctNonZero(p.Ips)
	if len(ips) > 0 {
		boolQuery = boolQuery.Must(elastic.NewTermsQueryFromStrings("ip", ips...))
	}

	// 添加其他查询条件
	paramList, err := utils.StructToMap(p.CommonThreatRequest, "json")
	if err != nil {
		return nil, err
	}

	// 设置操作类型为等于（与原实现保持一致）
	paramList["operation_type_string"] = "=="

	boolQuery, err = threat.BuildBoolQueryForThreat(paramList, boolQuery)
	if err != nil {
		return nil, err
	}

	return boolQuery, nil
}

func (h *PocHandler) Headers(params interface{}) []string {
	header := []string{
		"漏洞名称",
		"IP地址",
		"端口",
		"主机名",
		"所属区域",
		"漏洞地址",
		"漏洞类型",
		"漏洞等级",
		"漏洞状态",
		"漏洞超期时间",
		"风险值",
		"业务系统",
		"业务系统负责人",
		"业务部门",
		"修复负责人",
		"抄送人",
		"运维人员",
		"运维部门",
		"漏洞详情",
		"修复建议",
		"修复优先级",
		"CVE编号",
		"CNVD编号",
		"CNNVD编号",
		"是否PoC漏洞",
		"是否存在PoC",
		"是否存在EXP",
		"数据源更新时间",
		"首次上报时间",
		"最后上报时间",
		"描述",
	}

	// 添加自定义字段名称
	customFields, err := custom_column.NewCustomFieldMetaModel().GetByModuleType(custom_column.CustomFieldMetaModuleTypeVuln, false)
	if err == nil {
		for _, customField := range customFields {
			header = append(header, customField.DisplayName)
		}
	}

	return header
}

func (h *PocHandler) ParseRow(hit *elastic.SearchHit, params interface{}) ([]interface{}, error) {
	pocSource := poc.Poc{}
	if err := json.Unmarshal(hit.Source, &pocSource); err != nil {
		return nil, err
	}

	// 处理修复负责人
	var repair = "-"
	if pocSource.Person != nil {
		if name, ok := pocSource.Person["name"]; ok {
			repair = name
		}
		if mobile, ok := pocSource.Person["mobile"]; ok {
			repair += "-" + mobile
		}
	}

	// 处理端口
	port := ""
	if pocSource.Port > 0 {
		port = strconv.Itoa(pocSource.Port)
	}

	// 处理抄送人
	var ccPerson string
	if pocSource.CcPerson != nil {
		if fid, ok := pocSource.CcPerson["fid"]; ok {
			pgid, err := pgidservice.GetPgidByFid(fid)
			if err == nil {
				ccPerson = pgid
			}
		}
	}

	data := []interface{}{
		pocSource.Name,
		pocSource.Ip,
		port,
		strings.Join(pocSource.HostName, ","),
		network_area.GetNetWorkAreaNameById(pocSource.Area),
		poc.CheckUrl(pocSource.Url),
		pocSource.VulType,
		pocSource.LevelDesc(),
		pocSource.StatusDesc(),
		func() string {
			if pocSource.LimitDate != nil {
				return pocSource.LimitDate.Format("2006-01-02")
			}
			return ""
		}(),
		pocSource.RiskNum,
		pocSource.BusinessSystems(),
		pocSource.BusinessOwners(),
		pocSource.BusinessDepartments(),
		repair,
		ccPerson,
		pocSource.OperInfos(),
		pocSource.OperInfoDepartment(),
		pocSource.Details,
		pocSource.Suggestions,
		pocSource.RepairPriority,
		pocSource.Cve,
		pocSource.Cnvd,
		pocSource.Cnnvd,
		pocSource.IsPocDesc(),
		pocSource.HasPocDesc(),
		pocSource.HasExpDesc(),
		pocSource.LastResponseAt,
		pocSource.CreatedAt,
		pocSource.UpdatedAt,
		pocSource.Describe,
	}

	// 添加自定义字段值
	customFields, err := custom_column.NewCustomFieldMetaModel().GetByModuleType(custom_column.CustomFieldMetaModuleTypeVuln, false)
	if err == nil {
		for _, customField := range customFields {
			if value, ok := pocSource.CustomFields[customField.FieldKey]; ok {
				data = append(data, value)
			} else {
				data = append(data, "")
			}
		}
	}

	return data, nil
}

func (h *PocHandler) FilePrefix() string {
	return "漏洞列表"
}

func (h *PocHandler) PermissionType() int {
	return data_source.SourceTypePoc
}
