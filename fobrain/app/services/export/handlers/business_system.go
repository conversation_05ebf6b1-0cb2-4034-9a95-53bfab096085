package handlers

import (
	"encoding/json"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/olivere/elastic/v7"

	"fobrain/fobrain/app/request/asset_center"
	filtrate "fobrain/models/elastic"
	"fobrain/models/elastic/business_system"
	"fobrain/models/mysql/custom_column"
	"fobrain/pkg/utils"
)

type BusinessSystemHandler struct {
	isDeleted bool
}

func NewBusinessSystemHandler(isDeleted bool) *BusinessSystemHandler {
	return &BusinessSystemHandler{isDeleted: isDeleted}
}

func (h *BusinessSystemHandler) IndexName() string {
	return business_system.NewBusinessSystems().IndexName()
}

func (h *BusinessSystemHandler) BuildQuery(ctx *gin.Context, params interface{}) (*elastic.BoolQuery, error) {
	p := params.(*asset_center.BusinessSystemsSearchRequest)

	boolQuery := elastic.NewBoolQuery()

	// 删除状态查询
	existsQuery := elastic.NewExistsQuery("deleted_at")
	if h.isDeleted {
		// 查询 deleted_at 字段存在且不为空字符串
		boolQuery.Must(existsQuery)
	} else {
		// 查询 deleted_at 字段为 null 或空字符串
		boolQuery.MustNot(existsQuery)
	}

	// ID查询
	p.Ids = utils.CompactStrings(p.Ids)
	if len(p.Ids) > 0 {
		boolQuery = boolQuery.Must(elastic.NewTermsQueryFromStrings("fid", p.Ids...))
	}

	// 状态查询
	if p.Status > 0 {
		boolQuery = boolQuery.Must(elastic.NewTermQuery("status", p.Status))
	}

	// 关键字查询
	if p.Keyword != "" {
		boolQuery = boolQuery.Must(business_system.NewBusinessSystems().NewKeywordQuery(p.Keyword))
	}

	// 高级筛选
	if len(p.SearchCondition) > 0 && p.SearchCondition[0] != "" {
		conditions, err := filtrate.ParseQueryConditions(p.SearchCondition)
		if err != nil {
			return nil, err
		}
		for _, condition := range conditions {
			boolQuery = filtrate.BuildBoolQuery(condition.Field, condition.OperationTypeString, condition.LogicalConnective, condition.Value, boolQuery)
		}
	}

	return boolQuery, nil
}

func (h *BusinessSystemHandler) Headers(params interface{}) []string {
	header := []string{
		"数据来源",
		"业务系统名称",
		"业务系统地址",
		"业务系统负责人及部门",
		"关联内网IP",
		"关联互联网IP",
		"是否关基设施",
		"是否信创",
		"运行环境",
		"采购类型",
		"重要性",
		"运行状态",
		"等保级别",
		"系统版本",
		"连续性级别",
		"规划用途",
		"首次上报时间",
		"最后上报时间",
	}

	// 添加自定义字段名称
	customFields, err := custom_column.NewCustomFieldMetaModel().GetByModuleType(custom_column.CustomFieldMetaModuleTypeBusinessSystem, false)
	if err == nil {
		for _, customField := range customFields {
			header = append(header, customField.DisplayName)
		}
	}

	return header
}

func (h *BusinessSystemHandler) ParseRow(hit *elastic.SearchHit, params interface{}) ([]interface{}, error) {
	businessData := business_system.BusinessSystems{}
	if err := json.Unmarshal(hit.Source, &businessData); err != nil {
		return nil, err
	}

	// 处理业务系统负责人及部门
	perAndDep := ""
	for _, per := range businessData.PersonBase {
		perAndDep += per.Name + " "
		for _, dep := range per.Department {
			perAndDep += dep.Name + " "
		}
	}

	// 处理资产属性
	attr := h.getAttr(&businessData.AssetsAttribute)

	data := []interface{}{
		business_system.SourceStatusMap[businessData.From],
		businessData.BusinessName,
		businessData.Address,
		perAndDep,
		strings.Join(businessData.IntranetIps, ","),
		strings.Join(businessData.InternetIps, ","),
		attr["gj"],
		attr["is_xc"],
		attr["env"],
		attr["purchase_tp"],
		attr["important_tp"],
		attr["running_st"],
		attr["insuranceL"],
		businessData.SystemVersion,
		business_system.ContinuityLevel[businessData.ContinuityLevel],
		businessData.UseMark,
		businessData.CreatedAt,
		businessData.UpdatedAt,
	}

	// 添加自定义字段值
	customFields, err := custom_column.NewCustomFieldMetaModel().GetByModuleType(custom_column.CustomFieldMetaModuleTypeBusinessSystem, false)
	if err == nil {
		for _, customField := range customFields {
			if value, ok := businessData.CustomFields[customField.FieldKey]; ok {
				data = append(data, value)
			} else {
				data = append(data, "")
			}
		}
	}

	return data, nil
}

func (h *BusinessSystemHandler) FilePrefix() string {
	if h.isDeleted {
		return "业务系统回收站列表"
	}
	return "业务系统列表"
}

func (h *BusinessSystemHandler) PermissionType() int {
	return 0 // 业务系统导出无权限控制
}

// getAttr 处理资产属性
func (h *BusinessSystemHandler) getAttr(attr *business_system.AssetsAttribute) map[string]string {
	// 定义映射（与原业务系统服务保持一致）
	gJData := map[int]string{
		1: "是",
		2: "否",
	}

	xCData := map[int]string{
		1: "是",
		2: "否",
	}

	operatingEnv := map[int]string{
		1: "生产环境",
		2: "开发环境",
	}

	purchaseTypeData := map[int]string{
		1: "自研",
		2: "外包",
		3: "第三方采购",
	}

	importantTypes := map[int]string{
		1: "非常重要",
		2: "重要",
		3: "一般",
	}

	runningStateData := map[int]string{
		1: "运行中",
		0: "已下线",
	}

	insuranceLevel := map[int]string{
		1: "一级等保",
		2: "二级等保",
		3: "三级等保",
		4: "四级等保",
		5: "五级等保",
	}

	result := make(map[string]string)

	// 是否关基设施
	result["gj"] = ""
	if attr.IsGj != nil {
		if val, ok := gJData[*attr.IsGj]; ok {
			result["gj"] = val
		}
	}

	// 是否信创
	result["is_xc"] = ""
	if attr.IsXc != nil {
		if val, ok := xCData[*attr.IsXc]; ok {
			result["is_xc"] = val
		}
	}

	// 运行环境
	result["env"] = ""
	if attr.OperatingEnv != nil {
		if val, ok := operatingEnv[*attr.OperatingEnv]; ok {
			result["env"] = val
		}
	}

	// 采购类型
	result["purchase_tp"] = ""
	if attr.PurchaseType != nil {
		if val, ok := purchaseTypeData[*attr.PurchaseType]; ok {
			result["purchase_tp"] = val
		}
	}

	// 重要性
	result["important_tp"] = ""
	if attr.ImportantTypes != nil {
		if val, ok := importantTypes[*attr.ImportantTypes]; ok {
			result["important_tp"] = val
		}
	}

	// 运行状态
	result["running_st"] = ""
	if attr.RunningState != nil {
		if val, ok := runningStateData[*attr.RunningState]; ok {
			result["running_st"] = val
		}
	}

	// 等保级别
	result["insuranceL"] = ""
	if attr.InsuranceLevel != nil {
		if val, ok := insuranceLevel[*attr.InsuranceLevel]; ok {
			result["insuranceL"] = val
		}
	}

	return result
}
