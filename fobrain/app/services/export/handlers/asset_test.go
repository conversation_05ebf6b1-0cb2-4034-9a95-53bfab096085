package handlers

import (
	"encoding/json"
	"testing"
	"time"

	"github.com/olivere/elastic/v7"
	"github.com/stretchr/testify/assert"

	"fobrain/fobrain/app/repository/asset"
	"fobrain/fobrain/common/localtime"
	"fobrain/models/elastic/assets"
)

func TestAssetHandler_IndexName(t *testing.T) {
	handler := NewAssetHandler(asset.NetworkTypeInternal, asset.NotIsRecycleBin)
	indexName := handler.IndexName()
	assert.Contains(t, indexName, "asset")
}

func TestAssetHandler_FilePrefix(t *testing.T) {
	tests := []struct {
		name        string
		networkType int
		expected    string
	}{
		{
			name:        "内网资产",
			networkType: asset.NetworkTypeInternal,
			expected:    "内网资产列表",
		},
		{
			name:        "外网资产",
			networkType: asset.NetworkTypeExternal,
			expected:    "外网资产列表",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			handler := NewAssetHandler(tt.networkType, asset.NotIsRecycleBin)
			filePrefix := handler.FilePrefix()
			assert.Equal(t, tt.expected, filePrefix)
		})
	}
}

func TestAssetHandler_PermissionType(t *testing.T) {
	tests := []struct {
		name        string
		networkType int
		expected    int
	}{
		{
			name:        "内网资产权限",
			networkType: asset.NetworkTypeInternal,
			expected:    5, // data_source.SourceTypeIntranetAsset
		},
		{
			name:        "外网资产权限",
			networkType: asset.NetworkTypeExternal,
			expected:    4, // data_source.SourceTypeInternetAsset
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			handler := NewAssetHandler(tt.networkType, asset.NotIsRecycleBin)
			permissionType := handler.PermissionType()
			assert.Equal(t, tt.expected, permissionType)
		})
	}
}

func TestAssetHandler_Headers(t *testing.T) {
	tests := []struct {
		name         string
		networkType  int
		expectedBase []string
	}{
		{
			name:        "内网资产表头",
			networkType: asset.NetworkTypeInternal,
			expectedBase: []string{
				"IP地址",
				"端口",
				"协议",
				"URL",
				"网站标题",
				"域名",
				"组件信息",
				"资产状态",
				"主机名",
				"IP类型",
				"MAC地址",
				"所属区域",
				"关联基础设施",
				"业务系统",
				"业务负责人",
				"运维人员",
				"首次上报时间",
				"最后上报时间",
			},
		},
		{
			name:        "外网资产表头",
			networkType: asset.NetworkTypeExternal,
			expectedBase: []string{
				"IP地址",
				"端口",
				"协议",
				"URL",
				"网站标题",
				"域名",
				"组件信息",
				"资产状态",
				"主机名",
				"IP类型",
				"MAC地址",
				"所属区域",
				"关联基础设施",
				"业务系统",
				"业务负责人",
				"运维人员",
				"首次上报时间",
				"最后上报时间",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 设置数据库mock
			helper := NewTestHelper(t)
			defer helper.Close()
			helper.MockCustomFieldQuery("资产", 1)

			handler := NewAssetHandler(tt.networkType, asset.NotIsRecycleBin)
			params := struct{}{}

			headers := handler.Headers(params)

			// 检查基础表头
			for i, expected := range tt.expectedBase {
				assert.Equal(t, expected, headers[i])
			}

			// 表头数量应该至少包含基础字段
			assert.GreaterOrEqual(t, len(headers), len(tt.expectedBase))
		})
	}
}

func TestAssetHandler_ParseRow_Internal(t *testing.T) {
	// 设置数据库mock
	helper := NewTestHelper(t)
	defer helper.Close()
	helper.MockNetworkAreaQuery(1)
	helper.MockCustomFieldQuery("资产", 1)

	handler := NewAssetHandler(asset.NetworkTypeInternal, asset.NotIsRecycleBin)

	// 创建测试资产数据
	now := localtime.NewLocalTime(time.Now())
	assetData := assets.Assets{
		Ip:        "*************",
		HostName:  []string{"test-host"},
		Mac:       []string{"00:11:22:33:44:55"},
		Area:      1,
		Status:    1,
		IpType:    1,
		CreatedAt: now,
		UpdatedAt: now,
		CustomFields: map[string]string{
			"custom_field1": "value1",
		},
		Ports: []*assets.PortInfo{
			{
				Port:     80,
				Protocol: "tcp",
				Domain:   "test.com",
			},
		},
		RuleInfos: []*assets.RuleInfo{
			{
				Product: "nginx",
			},
		},
		Business: []*assets.Business{
			{
				System: "测试系统",
				PersonBase: []*assets.PersonBase{
					{
						Id:   "person1",
						Name: "负责人1",
					},
				},
			},
		},
		OperInfo: []*assets.PersonBase{
			{
				Id:   "oper1",
				Name: "运维人员1",
			},
		},
	}

	// 序列化为JSON
	assetJSON, err := json.Marshal(assetData)
	assert.NoError(t, err)

	// 创建模拟的搜索结果
	hit := &elastic.SearchHit{
		Source: assetJSON,
	}

	params := struct{}{}

	// 执行解析
	result, err := handler.ParseRow(hit, params)
	assert.NoError(t, err)
	assert.NotNil(t, result)

	// 验证结果长度（基础字段 + 自定义字段）
	assert.GreaterOrEqual(t, len(result), 15)

	// 验证具体字段值
	assert.Equal(t, "*************", result[0]) // IP地址
	assert.NotNil(t, result[1])                 // 端口信息（PortInfos返回的[][]string）
	assert.NotNil(t, result[2])                 // 组件信息
	assert.NotNil(t, result[3])                 // 资产状态
}

func TestAssetHandler_ParseRow_External(t *testing.T) {
	// 设置数据库mock
	helper := NewTestHelper(t)
	defer helper.Close()
	helper.MockNetworkAreaQuery(1)
	helper.MockCustomFieldQuery("资产", 1)

	handler := NewAssetHandler(asset.NetworkTypeExternal, asset.NotIsRecycleBin)

	// 创建测试外网资产数据
	now := localtime.NewLocalTime(time.Now())
	assetData := assets.Assets{
		Ip:        "*******",
		HostName:  []string{"dns.google"},
		Area:      1,
		Status:    1,
		IpType:    2, // 外网IP
		CreatedAt: now,
		UpdatedAt: now,
		Ports: []*assets.PortInfo{
			{
				Port:     443,
				Protocol: "tcp",
				Url:      "https://dns.google",
				Title:    "Google DNS",
				Domain:   "google.com",
			},
		},
		RuleInfos: []*assets.RuleInfo{
			{
				Product: "nginx",
			},
		},
	}

	assetJSON, err := json.Marshal(assetData)
	assert.NoError(t, err)

	hit := &elastic.SearchHit{
		Source: assetJSON,
	}

	params := struct{}{}

	result, err := handler.ParseRow(hit, params)
	assert.NoError(t, err)
	assert.NotNil(t, result)

	// 验证外网资产特有字段
	assert.Equal(t, "*******", result[0]) // IP地址
	assert.NotNil(t, result[1])           // 端口信息
}

func TestAssetHandler_ParseRow_InvalidJSON(t *testing.T) {
	handler := NewAssetHandler(asset.NetworkTypeInternal, asset.NotIsRecycleBin)

	// 创建无效的JSON数据
	hit := &elastic.SearchHit{
		Source: []byte(`{"invalid": json}`),
	}

	params := struct{}{}

	result, err := handler.ParseRow(hit, params)
	assert.Error(t, err)
	assert.Nil(t, result)
}

// 基准测试
func BenchmarkAssetHandler_ParseRow(b *testing.B) {
	handler := NewAssetHandler(asset.NetworkTypeInternal, asset.NotIsRecycleBin)

	assetData := assets.Assets{
		Ip:       "*************",
		HostName: []string{"test-host"},
		Mac:      []string{"00:11:22:33:44:55"},
		Area:     1,
		Status:   1,
		IpType:   1,
		Ports: []*assets.PortInfo{
			{
				Port:     80,
				Protocol: "tcp",
			},
		},
	}

	assetJSON, _ := json.Marshal(assetData)
	hit := &elastic.SearchHit{Source: assetJSON}
	params := struct{}{}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = handler.ParseRow(hit, params)
	}
}
