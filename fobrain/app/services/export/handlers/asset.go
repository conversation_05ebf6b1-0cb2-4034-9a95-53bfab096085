package handlers

import (
	"encoding/json"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/olivere/elastic/v7"

	"fobrain/fobrain/app/repository/asset"
	"fobrain/fobrain/app/request/asset_center"
	"fobrain/models/elastic/assets"
	"fobrain/models/mysql/custom_column"
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/network_areas"
)

type AssetHandler struct {
	networkType  int
	isRecycleBin int
}

func NewAssetHandler(networkType, isRecycleBin int) *AssetHandler {
	return &AssetHandler{
		networkType:  networkType,
		isRecycleBin: isRecycleBin,
	}
}

func (h *AssetHandler) IndexName() string {
	return assets.NewAssets().IndexName()
}

func (h *AssetHandler) BuildQuery(ctx *gin.Context, params interface{}) (*elastic.BoolQuery, error) {
	p := params.(*asset_center.InternalAssetRequest)
	return asset.CreateBoolQuery(p.Keyword, h.networkType, h.isRecycleBin, p.Ids, p)
}

func (h *AssetHandler) Headers(params interface{}) []string {
	var header []string
	if h.networkType == asset.NetworkTypeInternal {
		header = []string{
			"IP地址",
			"端口",
			"协议",
			"URL",
			"网站标题",
			"域名",
			"组件信息",
			"资产状态",
			"主机名",
			"IP类型",
			"MAC地址",
			"所属区域",
			"关联基础设施",
			"业务系统",
			"业务负责人",
			"运维人员",
			"首次上报时间",
			"最后上报时间",
		}
	} else {
		header = []string{
			"IP地址",
			"端口",
			"协议",
			"URL",
			"网站标题",
			"域名",
			"组件信息",
			"资产状态",
			"主机名",
			"IP类型",
			"MAC地址",
			"所属区域",
			"关联基础设施",
			"业务系统",
			"业务负责人",
			"运维人员",
			"首次上报时间",
			"最后上报时间",
		}
	}

	// 补充自定义字段
	customFields, err := custom_column.NewCustomFieldMetaModel().GetByModuleType(custom_column.CustomFieldMetaModuleTypeAsset, false)
	if err == nil {
		for _, field := range customFields {
			header = append(header, field.DisplayName)
		}
	}

	return header
}

func (h *AssetHandler) ParseRow(hit *elastic.SearchHit, params interface{}) ([]interface{}, error) {
	assetData := assets.Assets{}
	if err := json.Unmarshal(hit.Source, &assetData); err != nil {
		return nil, err
	}

	networkArea := network_areas.AllNetworkArea()

	var data []interface{}
	if h.networkType == asset.NetworkTypeInternal {
		// 手动处理端口数据，提取端口和协议
		portInfos := assetData.PortInfos()
		var ports, protocols []string
		for _, portInfo := range portInfos {
			if len(portInfo) >= 2 {
				ports = append(ports, portInfo[0])
				protocols = append(protocols, portInfo[1])
			}
		}

		data = []interface{}{
			assetData.Ip,                           // IP
			strings.Join(ports, ","),               //端口
			strings.Join(protocols, ","),           // 协议
			"",                                     // URL
			"",                                     // 网站标题
			strings.Join(assetData.Domains(), ","), // 域名
			assetData.GetProducts(),                // 组件信息
			assetData.StatusDesc(),                 // 资产状态
			func() string { // 主机名
				if len(assetData.HostName) > 0 {
					return strings.Join(assetData.HostName, ",")
				}
				return ""
			}(),
			assetData.IpTypeDesc(),              // IP类型
			strings.Join(assetData.Mac, ","),    // MAC地址
			networkArea[uint64(assetData.Area)], // 所属区域
			"",                                  // 关联基础设施
			assetData.BusinessSystems(),         // 业务系统
			assetData.BusinessOwners(),          // 业务负责人
			assetData.OperString(),              // 运维人员
			assetData.CreatedAt,                 // 首次上报时间
			assetData.UpdatedAt,                 // 最后上报时间
		}
	} else {
		// 手动处理外网资产端口数据
		portInfos := assetData.PortInfos()
		var ports, protocols, urls, titles, domains []string
		for _, portInfo := range portInfos {
			if len(portInfo) >= 6 {
				// 外网资产端口格式：[端口, 协议, URL, 标题, 域名, 状态码]
				// 去掉末尾状态码数据
				ports = append(ports, portInfo[0])
				protocols = append(protocols, portInfo[1])
				urls = append(urls, portInfo[2])
				titles = append(titles, portInfo[3])
				domains = append(domains, portInfo[4])
			}
		}

		data = []interface{}{
			assetData.Ip,
			strings.Join(ports, ","),
			strings.Join(protocols, ","),
			strings.Join(urls, ","),
			strings.Join(titles, ","),
			strings.Join(domains, ","),
			assetData.GetProducts(),
			assetData.StatusDesc(),
			func() string {
				if len(assetData.HostName) > 0 {
					return strings.Join(assetData.HostName, ",")
				}
				return ""
			}(),
			assetData.IpTypeDesc(),
			strings.Join(assetData.Mac, ","),
			networkArea[uint64(assetData.Area)],
			"",
			assetData.BusinessSystems(),
			assetData.BusinessOwners(),
			assetData.OperString(),
			assetData.CreatedAt,
			assetData.UpdatedAt,
		}
	}

	// 添加自定义字段值
	customFields, err := custom_column.NewCustomFieldMetaModel().GetByModuleType(custom_column.CustomFieldMetaModuleTypeAsset, false)
	if err == nil {
		for _, customField := range customFields {
			if value, ok := assetData.CustomFields[customField.FieldKey]; ok {
				data = append(data, value)
			} else {
				data = append(data, "")
			}
		}
	}

	return data, nil
}

func (h *AssetHandler) FilePrefix() string {
	if h.networkType == asset.NetworkTypeInternal {
		return "内网资产列表"
	} else {
		return "外网资产列表"
	}
}

func (h *AssetHandler) PermissionType() int {
	if h.networkType == asset.NetworkTypeInternal {
		return data_source.SourceTypeIntranetAsset
	} else {
		return data_source.SourceTypeInternetAsset
	}
}
