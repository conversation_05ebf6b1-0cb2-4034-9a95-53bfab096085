package handlers

import (
	"database/sql/driver"
	"regexp"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"gorm.io/gorm"

	testcommon "fobrain/fobrain/tests/common_test"
)

// TestHelper 测试辅助工具
type TestHelper struct {
	DB   *gorm.DB
	Mock sqlmock.Sqlmock
}

// NewTestHelper 创建测试辅助工具
func NewTestHelper(t *testing.T) *TestHelper {
	// 使用测试框架的mock
	mockDB := testcommon.GetMysqlMock()

	return &TestHelper{
		DB:   mockDB.MockGorm,
		Mock: mockDB.Mock,
	}
}

// Close 关闭测试辅助工具
func (h *TestHelper) Close() {
	// 测试框架会自动处理关闭
}

// MockCustomFieldQuery 模拟自定义字段查询
func (h *TestHelper) MockCustomFieldQuery(moduleType string, callCount int) {
	query := "SELECT \\* FROM `custom_field_meta` WHERE \\(module_type = \\? and deleted_at is null\\) AND `custom_field_meta`.`deleted_at` IS NULL"

	for i := 0; i < callCount; i++ {
		h.Mock.ExpectQuery(regexp.QuoteMeta(query)).
			WithArgs(moduleType).
			WillReturnRows(sqlmock.NewRows([]string{"id", "module_type", "field_key", "display_name", "field_type", "options"}).
				AddRow(1, moduleType, "custom_field1", "自定义字段1", "textarea", "").
				AddRow(2, moduleType, "custom_field2", "自定义字段2", "select", "选项1,选项2"))
	}
}

// MockNetworkAreaQuery 模拟网络区域查询
func (h *TestHelper) MockNetworkAreaQuery(callCount int) {
	query := "SELECT id,name FROM `network_areas`"

	for i := 0; i < callCount; i++ {
		h.Mock.ExpectQuery(regexp.QuoteMeta(query)).
			WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).
				AddRow(1, "默认区域").
				AddRow(2, "测试区域"))
	}
}

// MockDataSourceQuery 模拟数据源查询
func (h *TestHelper) MockDataSourceQuery(callCount int) {
	query := "SELECT \\* FROM `data_sources` WHERE `data_sources`.`deleted_at` IS NULL"

	for i := 0; i < callCount; i++ {
		h.Mock.ExpectQuery(regexp.QuoteMeta(query)).
			WillReturnRows(sqlmock.NewRows([]string{"id", "name", "type", "status"}).
				AddRow(1, "测试数据源1", 1, 1).
				AddRow(2, "测试数据源2", 2, 1))
	}
}

// MockAllQueries 模拟所有常用查询
func (h *TestHelper) MockAllQueries(moduleType string, customFieldCalls, networkAreaCalls, dataSourceCalls int) {
	if customFieldCalls > 0 {
		h.MockCustomFieldQuery(moduleType, customFieldCalls)
	}
	if networkAreaCalls > 0 {
		h.MockNetworkAreaQuery(networkAreaCalls)
	}
	if dataSourceCalls > 0 {
		h.MockDataSourceQuery(dataSourceCalls)
	}
}

// AnyArgs 返回任意参数匹配器
func AnyArgs() driver.Value {
	return sqlmock.AnyArg()
}

// SetupAssetHandlerMocks 设置资产处理器的mock
func (h *TestHelper) SetupAssetHandlerMocks() {
	// Headers方法会调用一次自定义字段查询
	h.MockCustomFieldQuery("资产", 2) // Headers + ParseRow
	// ParseRow方法会调用一次网络区域查询
	h.MockNetworkAreaQuery(2) // ParseRow调用
}

// SetupDeviceHandlerMocks 设置设备处理器的mock
func (h *TestHelper) SetupDeviceHandlerMocks() {
	h.MockCustomFieldQuery("设备", 1) // Headers
	h.MockDataSourceQuery(1)        // ParseRow
	h.MockNetworkAreaQuery(1)       // ParseRow
}

// SetupBusinessSystemHandlerMocks 设置业务系统处理器的mock
func (h *TestHelper) SetupBusinessSystemHandlerMocks() {
	h.MockCustomFieldQuery("业务系统", 1) // Headers
}

// SetupPocHandlerMocks 设置漏洞处理器的mock
func (h *TestHelper) SetupPocHandlerMocks() {
	h.MockCustomFieldQuery("漏洞", 1) // Headers
	h.MockDataSourceQuery(1)        // ParseRow
	h.MockNetworkAreaQuery(1)       // ParseRow
}
