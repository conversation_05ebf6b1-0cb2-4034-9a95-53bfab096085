package backup

import (
	"context"
	"encoding/hex"
	"errors"
	"fmt"
	"fobrain/fobrain/logs"
	"fobrain/initialize/redis"
	backup2 "fobrain/module/backup"
	"fobrain/module/zip"
	"fobrain/pkg/cfg"
	"os"
	"path/filepath"
	"sync"
	"time"

	"golang.org/x/sync/errgroup"
)

const (
	// 锁的键名
	lockKey = "backup_or_restore_lock"

	// 备份进度键名
	backupProgressKey = "backup:progress"
	// 恢复进度键名
	restoreProgressKey = "restore:progress"

	// 任务状态常量
	StatusNotStarted = 0
	StatusInProgress = 1
	StatusCompleted  = 2
	StatusFailed     = 3
)

// Manager 管理备份和恢复流程。
type Manager struct {
	esManager        backup2.BackupRestoreManager
	mysqlManager     backup2.BackupRestoreManager
	zipManager       zip.ZipManager
	backupDir        string
	restoreDir       string
	zipFilePath      string
	timeOut          time.Duration
	compressionLevel int // 添加压缩级别配置
}

// NewBackupManager 创建一个新的 Manager。
func NewBackupManager() *Manager {
	commonCfg := cfg.LoadCommon()
	backupDir := filepath.Join(commonCfg.StoragePath, "backup_data")
	restoreDir := filepath.Join(commonCfg.StoragePath, "restore_data")
	zipFilePath := filepath.Join(commonCfg.StoragePath, "backup_data_zip", "backup.zip")
	key, _ := hex.DecodeString(commonCfg.EncryptionKey)

	// ES 容器内外路径配置
	esContainerPath := "/usr/share/elasticsearch/data/snapshot" // ES 容器内路径
	esHostPath := "/data/fobrain/storage/data/es/snapshot/"
	//esHostPath := "/Users/<USER>/Desktop/docker/fobrain/storage/data/es/snapshot/"

	return &Manager{
		esManager: backup2.NewElasticsearchManager(
			fmt.Sprintf("http://%s:%d", cfg.LoadElastic().Address, cfg.LoadElastic().Port),
			cfg.LoadElastic().UserName,
			cfg.LoadElastic().Password,
			"fobrain2.0-es",
			"fobrain2.0-repo",
			esContainerPath, // ES 容器内路径
			backupDir,
			restoreDir,
			esHostPath, // 宿主机路径
		),
		mysqlManager: backup2.NewMySQLManager(
			cfg.LoadMysql().Address,
			cfg.LoadMysql().UserName,
			cfg.LoadMysql().Password,
			cfg.LoadMysql().Database,
			backupDir,
			restoreDir,
		),
		zipManager:       zip.NewManager(key),
		backupDir:        backupDir,
		restoreDir:       restoreDir,
		zipFilePath:      zipFilePath,
		timeOut:          30 * time.Minute,
		compressionLevel: 1, // 改为最低压缩级别，因为文件已经压缩过
	}
}

// Backup 执行备份过程并返回 ZIP 文件路径。
func (m *Manager) Backup() (string, error) {
	// 获取分布式锁，防止并发执行备份
	if acquired, err := m.AcquireLock(); err != nil || !acquired {
		logs.GetLogger().Error("Failed to acquire lock for backup", "error", err)
		return "", errors.New("正在备份或恢复，请稍后重试")
	}
	defer m.ReleaseLock()

	// 更新备份开始进度
	m.updateProgress(backupProgressKey, StatusNotStarted, "Backup started", 10)

	// 准备备份
	if err := m.prepareBackup(); err != nil {
		m.updateProgress(backupProgressKey, StatusFailed, fmt.Sprintf("Backup failed: %v", err), 0)
		return "", err
	}

	// 设置备份的超时时间
	ctx, cancel := context.WithTimeout(context.Background(), m.timeOut)
	logs.GetLogger().Info("Backup operation started with timeout", "timeout", m.timeOut)
	defer cancel()

	var g errgroup.Group

	// 并发执行备份任务
	g.Go(func() error {
		err := m.executeBackupTask(ctx, m.esManager.BackupWithContext, "Elasticsearch")
		if err != nil {
			logs.GetLogger().Error("Elasticsearch backup failed", "error", err)
		}
		return err
	})
	g.Go(func() error {
		err := m.executeBackupTask(ctx, m.mysqlManager.BackupWithContext, "MySQL")
		if err != nil {
			logs.GetLogger().Error("MySQL backup failed", "error", err)
		}
		return err
	})

	// 等待所有任务完成，捕获第一个错误
	if err := g.Wait(); err != nil {
		m.updateProgress(backupProgressKey, StatusFailed, fmt.Sprintf("Backup failed: %v", err), 0)
		return "", err
	}

	// 创建加密的 ZIP 文件，使用较低的压缩级别，因为文件已经压缩过
	zipFile, err := m.zipManager.CreateEncryptedZipFromDir(m.backupDir, m.zipFilePath, 1) // 使用最低压缩级别
	if err != nil {
		m.updateProgress(backupProgressKey, StatusFailed, fmt.Sprintf("Backup failed: %v", err), 0)
		return "", fmt.Errorf("failed to create ZIP: %v", err)
	}

	// 备份完成，更新进度
	m.updateProgress(backupProgressKey, StatusCompleted, "Backup completed successfully", 10, zipFile)

	// 返回 ZIP 文件路径
	return "", nil
}

// Restore 执行恢复过程。
func (m *Manager) Restore(zipFilePath string) error {
	// 获取分布式锁，防止并发执行恢复
	if acquired, err := m.AcquireLock(); err != nil || !acquired {
		logs.GetLogger().Error("Failed to acquire lock for restore", "error", err)
		return fmt.Errorf("restore in progress or lock error: %v", err)
	}
	defer m.ReleaseLock()

	// 更新恢复开始进度
	m.updateProgress(restoreProgressKey, StatusNotStarted, "Restore started", 10)

	// 准备恢复操作
	if err := m.prepareRestore(zipFilePath); err != nil {
		m.updateProgress(restoreProgressKey, StatusFailed, fmt.Sprintf("Restore failed: %v", err), 0)
		return err
	}

	// 设置恢复的超时时间
	ctx, cancel := context.WithTimeout(context.Background(), m.timeOut)
	logs.GetLogger().Info("Restore operation started with timeout", "timeout", m.timeOut)
	defer cancel()

	var g errgroup.Group

	// 并发执行恢复任务
	g.Go(func() error {
		err := m.executeRestoreTask(ctx, m.esManager.RestoreWithContext, "Elasticsearch")
		if err != nil {
			logs.GetLogger().Error("Elasticsearch restore failed", "error", err)
			m.updateProgress(restoreProgressKey, StatusFailed, fmt.Sprintf("Elasticsearch restore failed: %v", err), 0)
		}
		return err
	})

	g.Go(func() error {
		err := m.executeRestoreTask(ctx, m.mysqlManager.RestoreWithContext, "MySQL")
		if err != nil {
			logs.GetLogger().Error("MySQL restore failed", "error", err)
			m.updateProgress(restoreProgressKey, StatusFailed, fmt.Sprintf("MySQL restore failed: %v", err), 0)
		}
		return err
	})

	// 等待所有任务完成，捕获第一个错误
	if err := g.Wait(); err != nil {
		m.updateProgress(restoreProgressKey, StatusFailed, fmt.Sprintf("Restore failed: %v", err), 0)
		return err
	}

	// 恢复完成，更新进度
	m.updateProgress(restoreProgressKey, StatusCompleted, "Restore completed successfully", 10)

	return nil
}

// AcquireLock 获取分布式锁
func (m *Manager) AcquireLock() (bool, error) {
	acquired, err := redis.GetRedisClient().SetNX(context.Background(), lockKey, "locked", m.timeOut).Result()
	if err != nil {
		logs.GetLogger().Error("Error while trying to acquire lock", "error", err)
		return false, fmt.Errorf("failed to acquire lock: %v", err)
	}
	if !acquired {
		logs.GetLogger().Warn("Lock acquisition failed: another process might be holding the lock")
	}
	return acquired, err
}

// ReleaseLock 释放分布式锁
func (m *Manager) ReleaseLock() {
	if err := redis.GetRedisClient().Del(context.Background(), lockKey).Err(); err != nil {
		logs.GetLogger().Error("Error while releasing lock", "error", err)
	}
}

// prepareBackup 准备备份
func (m *Manager) prepareBackup() error {
	return m.prepareDirectory(m.backupDir, "backup")
}

// prepareRestore 准备恢复
func (m *Manager) prepareRestore(zipFilePath string) error {
	if err := m.prepareDirectory(m.restoreDir, "restore"); err != nil {
		return err
	}
	return m.zipManager.DecryptAndExtractZip(zipFilePath, m.restoreDir)
}

// prepareDirectory 确保目录存在并清理
func (m *Manager) prepareDirectory(dir, task string) error {
	if err := ensureDirectoryExists(dir); err != nil {
		logs.GetLogger().Error(fmt.Sprintf("Failed to prepare %s directory", task), "error", err)
		return fmt.Errorf("failed to prepare %s directory: %v", task, err)
	}
	return m.cleanDirectory(dir)
}

// executeBackupTask 执行备份任务
func (m *Manager) executeBackupTask(ctx context.Context, backupFunc func(context.Context) error, task string) error {
	if err := backupFunc(ctx); err != nil {
		logs.GetLogger().Error(fmt.Sprintf("%s backup failed", task), "error", err)
		return fmt.Errorf("%s backup failed: %v", task, err)
	}
	m.updateProgress(backupProgressKey, StatusInProgress, fmt.Sprintf("%s backup completed", task), 40)
	return nil
}

// executeRestoreTask 执行恢复任务
func (m *Manager) executeRestoreTask(ctx context.Context, restoreFunc func(context.Context) error, task string) error {
	if err := restoreFunc(ctx); err != nil {
		logs.GetLogger().Error(fmt.Sprintf("%s restore failed", task), "error", err)
		return fmt.Errorf("%s restore failed: %v", task, err)
	}
	m.updateProgress(restoreProgressKey, StatusInProgress, fmt.Sprintf("%s restore completed", task), 40)
	return nil
}

// updateProgress 更新任务进度
func (m *Manager) updateProgress(key string, status int, message string, increment float64, backFilePath ...string) {
	ctx := context.Background()
	msg := map[string]interface{}{
		"status":  status,
		"message": message,
	}
	if StatusNotStarted == status {
		msg["percentage"] = increment
	} else {
		redis.GetRedisClient().HIncrByFloat(ctx, key, "percentage", increment)
	}
	if len(backFilePath) > 0 {
		msg["back_file_path"] = backFilePath[0]
	}
	redis.GetRedisClient().HSet(ctx, key, msg)
	redis.GetRedisClient().Expire(ctx, key, 24*time.Hour)
}

// GetProgress 获取指定任务的进度信息
func GetProgress(taskType string) (map[string]string, error) {
	ctx := context.Background()
	var key string
	// 根据任务类型选择不同的 Redis 键
	switch taskType {
	case "backup":
		key = backupProgressKey
	case "recover":
		key = restoreProgressKey
	default:
		return nil, fmt.Errorf("invalid task type: %s", taskType)
	}

	// 从 Redis 获取进度信息
	progress, err := redis.GetRedisClient().HGetAll(ctx, key).Result()
	if err != nil {
		logs.GetLogger().Error("Failed to get progress from Redis", "taskType", taskType, "error", err)
		return nil, fmt.Errorf("failed to get progress from Redis: %v", err)
	}

	// 如果没有进度数据，返回 nil
	if len(progress) == 0 {
		return nil, nil
	}

	return progress, nil
}

func DeleteProgress(taskType string) error {
	ctx := context.Background()
	var key string
	// 根据任务类型选择不同的 Redis 键
	switch taskType {
	case "backup":
		key = backupProgressKey
	case "recover":
		key = restoreProgressKey
	default:
		return fmt.Errorf("invalid task type: %s", taskType)
	}
	_, err := redis.GetRedisClient().Del(ctx, key).Result()
	if err != nil {
		return err
	}
	return nil
}

// ensureDirectoryExists 确保指定的目录存在
func ensureDirectoryExists(path string) error {
	if _, err := os.Stat(path); os.IsNotExist(err) {
		logs.GetLogger().Info("Directory does not exist, creating", "path", path)
		if err := os.MkdirAll(path, 0755); err != nil {
			logs.GetLogger().Error("Failed to create directory", "path", path, "error", err)
			return fmt.Errorf("failed to create directory: %v", err)
		}
	}
	return nil
}

// cleanDirectory 清理指定目录中的所有文件和子目录
func (m *Manager) cleanDirectory(dir string) error {
	// 使用并发删除文件夹中的文件
	var wg sync.WaitGroup
	errChan := make(chan error, 1)

	_ = filepath.Walk(dir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			if os.IsNotExist(err) {
				logs.GetLogger().Warn("Path %s does not exist, skipping: %v", path, err)
				return nil
			}
			return fmt.Errorf("failed to access path %s: %v", path, err)
		}

		// 跳过目录本身
		if path == dir {
			return nil
		}

		// 并发删除
		wg.Add(1)
		go func(path string) {
			defer wg.Done()
			if err := os.RemoveAll(path); err != nil {
				errChan <- fmt.Errorf("failed to delete path %s: %v", path, err)
			} else {
				logs.GetLogger().Info("Deleted old data: %s", path)
			}
		}(path)

		return nil
	})

	// 等待所有并发任务完成
	go func() {
		wg.Wait()
		close(errChan)
	}()

	// 检查错误
	for err := range errChan {
		if err != nil {
			return err
		}
	}

	logs.GetLogger().Info("Directory %s cleaned successfully.", dir)
	return nil
}
