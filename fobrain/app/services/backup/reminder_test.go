package backup

import (
	"fmt"
	"testing"
	"time"

	testcommon "fobrain/fobrain/tests/common_test"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
)

func TestGetReminderStatus(t *testing.T) {
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()

	userID := uint64(123)

	// 测试用例1: 备份已开启，不显示任何提示
	t.Run("BackupEnabled", func(t *testing.T) {
		// Mock备份启用状态查询
		mockDb.ExpectQuery("SELECT value FROM `system_configs` WHERE `key` = ?").
			WithArgs(BackupEnabledKey).
			WillReturnRows(mockDb.NewRows([]string{"value"}).AddRow("true"))

		status := GetReminderStatus(userID)
		assert.False(t, status.ShowPopup)
		assert.False(t, status.ShowScroll)
	})

	// 测试用例2: 备份未开启，用户已忽略弹窗，仅显示滚动提示
	// t.Run("BackupDisabledUserIgnoredPopup", func(t *testing.T) {
	// 	// Mock备份启用状态查询
	// 	mockDb.ExpectQuery("SELECT value FROM `system_configs` WHERE `key` = ?").
	// 		WithArgs(BackupEnabledKey).
	// 		WillReturnRows(mockDb.NewRows([]string{"value"}).AddRow("false"))

	// 	// Mock用户忽略状态查询
	// 	ignoredKey := fmt.Sprintf("%s%d_ignored", UserBackupReminderPrefix, userID)
	// 	mockDb.ExpectQuery("SELECT `value` FROM `system_configs` WHERE `key` = ?").
	// 		WithArgs(ignoredKey).
	// 		WillReturnRows(mockDb.NewRows([]string{"value"}).AddRow("true"))

	// 	status := GetReminderStatus(userID)
	// 	assert.False(t, status.ShowPopup)
	// 	assert.True(t, status.ShowScroll)
	// })

	// 测试用例3: 备份未开启，用户未忽略，在1个月期限内，显示弹窗和滚动
	t.Run("BackupDisabledWithinOneMonth", func(t *testing.T) {
		// Mock备份启用状态查询
		mockDb.ExpectQuery("SELECT value FROM `system_configs` WHERE `key` = ?").
			WithArgs(BackupEnabledKey).
			WillReturnRows(mockDb.NewRows([]string{"value"}).AddRow("false"))

		// Mock用户忽略状态查询
		ignoredKey := fmt.Sprintf("%s%d_ignored", UserBackupReminderPrefix, userID)
		mockDb.ExpectQuery("SELECT value FROM `system_configs` WHERE `key` = ?").
			WithArgs(ignoredKey).
			WillReturnRows(mockDb.NewRows([]string{"value"}).AddRow("false"))

		// Mock首次时间查询（返回15天前的时间）
		firstTimeKey := fmt.Sprintf("%s%d_first_time", UserBackupReminderPrefix, userID)
		fifteenDaysAgo := time.Now().Add(-15 * 24 * time.Hour).Format(time.RFC3339)
		mockDb.ExpectQuery("SELECT `value` FROM `system_configs` WHERE `key` = ?").
			WithArgs(firstTimeKey).
			WillReturnRows(mockDb.NewRows([]string{"value"}).AddRow(fifteenDaysAgo))

		status := GetReminderStatus(userID)
		assert.True(t, status.ShowPopup)
		assert.True(t, status.ShowScroll)
	})

	// 测试用例4: 备份未开启，用户未忽略，超过1个月，仅显示滚动提示
	// t.Run("BackupDisabledOverOneMonth", func(t *testing.T) {
	// 	// Mock备份启用状态查询
	// 	mockDb.ExpectQuery("SELECT value FROM `system_configs` WHERE `key` = ?").
	// 		WithArgs(BackupEnabledKey).
	// 		WillReturnRows(mockDb.NewRows([]string{"value"}).AddRow("false"))

	// 	// Mock用户忽略状态查询
	// 	ignoredKey := fmt.Sprintf("%s%d_ignored", UserBackupReminderPrefix, userID)
	// 	mockDb.ExpectQuery("SELECT value FROM `system_configs` WHERE `key` = ?").
	// 		WithArgs(ignoredKey).
	// 		WillReturnRows(mockDb.NewRows([]string{"value"}).AddRow("false"))

	// 	// Mock首次时间查询（返回35天前的时间）
	// 	firstTimeKey := fmt.Sprintf("%s%d_first_time", UserBackupReminderPrefix, userID)
	// 	thirtyFiveDaysAgo := time.Now().Add(-35 * 24 * time.Hour).Format(time.RFC3339)
	// 	mockDb.ExpectQuery("SELECT value FROM `system_configs` WHERE `key` = ?").
	// 		WithArgs(firstTimeKey).
	// 		WillReturnRows(mockDb.NewRows([]string{"value"}).AddRow(thirtyFiveDaysAgo))

	// 	status := GetReminderStatus(userID)
	// 	assert.False(t, status.ShowPopup)
	// 	assert.True(t, status.ShowScroll)
	// })
}

func TestIgnorePopupReminder_Error(t *testing.T) {
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()

	userID := uint64(123)
	expectedKey := fmt.Sprintf("%s%d_ignored", UserBackupReminderPrefix, userID)

	// Mock更新配置失败
	mockDb.ExpectExec("UPDATE `system_configs` SET `value`=?,`updated_at`=? WHERE `key` = ?").
		WithArgs("true", sqlmock.AnyArg(), expectedKey).
		WillReturnError(sqlmock.ErrCancelled)

	err := IgnorePopupReminder(userID)
	assert.Error(t, err)
}

func TestIsBackupEnabled(t *testing.T) {
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()

	// 测试用例1: 备份已启用
	t.Run("BackupEnabled", func(t *testing.T) {
		mockDb.ExpectQuery("SELECT value FROM `system_configs` WHERE `key` = ?").
			WithArgs(BackupEnabledKey).
			WillReturnRows(mockDb.NewRows([]string{"value"}).AddRow("true"))

		result := isBackupEnabled()
		assert.True(t, result)
	})

	// 测试用例2: 备份未启用
	t.Run("BackupDisabled", func(t *testing.T) {
		mockDb.ExpectQuery("SELECT value FROM `system_configs` WHERE `key` = ?").
			WithArgs(BackupEnabledKey).
			WillReturnRows(mockDb.NewRows([]string{"value"}).AddRow("false"))

		result := isBackupEnabled()
		assert.False(t, result)
	})

	// 测试用例3: 查询错误
	t.Run("QueryError", func(t *testing.T) {
		mockDb.ExpectQuery("SELECT value FROM `system_configs` WHERE `key` = ?").
			WithArgs(BackupEnabledKey).
			WillReturnError(sqlmock.ErrCancelled)

		result := isBackupEnabled()
		assert.False(t, result)
	})
}

func TestIsPopupIgnored(t *testing.T) {
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()

	userID := uint64(123)
	expectedKey := fmt.Sprintf("%s%d_ignored", UserBackupReminderPrefix, userID)

	// 测试用例1: 用户已忽略
	t.Run("UserIgnored", func(t *testing.T) {
		mockDb.ExpectQuery("SELECT value FROM `system_configs` WHERE `key` = ?").
			WithArgs(expectedKey).
			WillReturnRows(mockDb.NewRows([]string{"value"}).AddRow("true"))

		result := isPopupIgnored(userID)
		assert.True(t, result)
	})

	// 测试用例2: 用户未忽略
	t.Run("UserNotIgnored", func(t *testing.T) {
		mockDb.ExpectQuery("SELECT value FROM `system_configs` WHERE `key` = ?").
			WithArgs(expectedKey).
			WillReturnRows(mockDb.NewRows([]string{"value"}).AddRow("false"))

		result := isPopupIgnored(userID)
		assert.False(t, result)
	})

	// 测试用例3: 查询错误
	t.Run("QueryError", func(t *testing.T) {
		mockDb.ExpectQuery("SELECT value FROM `system_configs` WHERE `key` = ?").
			WithArgs(expectedKey).
			WillReturnError(sqlmock.ErrCancelled)

		result := isPopupIgnored(userID)
		assert.False(t, result)
	})
}

func TestIsWithinOneMonth(t *testing.T) {
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()

	userID := uint64(123)
	expectedKey := fmt.Sprintf("%s%d_first_time", UserBackupReminderPrefix, userID)

	// 测试用例1: 首次访问，需要记录时间
	t.Run("FirstTime", func(t *testing.T) {
		// Mock查询首次时间（不存在）
		mockDb.ExpectQuery("SELECT value FROM `system_configs` WHERE `key` = ?").
			WithArgs(expectedKey).
			WillReturnRows(mockDb.NewRows([]string{"value"}))

		// Mock更新首次时间
		mockDb.ExpectExec("UPDATE `system_configs` SET `value`=?,`updated_at`=? WHERE `key` = ?").
			WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), expectedKey).
			WillReturnResult(sqlmock.NewResult(1, 1))

		result := isWithinOneMonth(userID)
		assert.True(t, result)
	})

	// 测试用例2: 在30天内
	t.Run("WithinOneMonth", func(t *testing.T) {
		// Mock查询首次时间（15天前）
		fifteenDaysAgo := time.Now().Add(-15 * 24 * time.Hour).Format(time.RFC3339)
		mockDb.ExpectQuery("SELECT value FROM `system_configs` WHERE `key` = ?").
			WithArgs(expectedKey).
			WillReturnRows(mockDb.NewRows([]string{"value"}).AddRow(fifteenDaysAgo))

		result := isWithinOneMonth(userID)
		assert.True(t, result)
	})

	// 测试用例4: 时间格式错误
	t.Run("InvalidTimeFormat", func(t *testing.T) {
		// Mock查询首次时间（无效格式）
		mockDb.ExpectQuery("SELECT value FROM `system_configs` WHERE `key` = ?").
			WithArgs(expectedKey).
			WillReturnRows(mockDb.NewRows([]string{"value"}).AddRow("invalid-time-format"))

		result := isWithinOneMonth(userID)
		assert.True(t, result) // 解析失败默认显示提醒
	})

	// 测试用例5: 首次访问，记录时间为空字符串
	t.Run("FirstTimeEmptyString", func(t *testing.T) {
		// Mock查询首次时间（空字符串）
		mockDb.ExpectQuery("SELECT value FROM `system_configs` WHERE `key` = ?").
			WithArgs(expectedKey).
			WillReturnRows(mockDb.NewRows([]string{"value"}).AddRow(""))

		// Mock更新首次时间
		mockDb.ExpectExec("UPDATE `system_configs` SET `value`=?,`updated_at`=? WHERE `key` = ?").
			WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), expectedKey).
			WillReturnResult(sqlmock.NewResult(1, 1))

		result := isWithinOneMonth(userID)
		assert.True(t, result)
	})
}
