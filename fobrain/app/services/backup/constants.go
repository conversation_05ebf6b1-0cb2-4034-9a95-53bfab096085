package backup

// 配置Key定义
const (
	BackupEnabledKey           = "backup_enabled"            // bool: 是否启用备份
	BackupTypeKey              = "backup_type"               // string: local/cloud
	BackupFullIntervalDaysKey  = "backup_full_interval_days" // int: 分组全量备份间隔天数(1-30)
	BackupIncrIntervalHoursKey = "backup_incr_interval_hours"// int: 分组增量备份间隔小时数(1-24)
	BackupRetentionDaysKey     = "backup_retention_days"     // int: 备份保留天数(7-365)
	BackupLastFullTimeKey      = "backup_last_full_time"     // string: 上次分组全量备份完成时间
	BackupLastIncrTimeKey      = "backup_last_incr_time"     // string: 上次分组增量备份完成时间

	// 云存储配置（当backup_type为cloud时使用）
	CloudStorageTypeKey        = "cloud_storage_type"        // string: 云存储类型 s3/oss/cos
	CloudStorageEndpointKey    = "cloud_storage_endpoint"    // string: 云存储端点
	CloudStorageBucketKey      = "cloud_storage_bucket"      // string: 存储桶名称
	CloudStorageAccessKeyKey   = "cloud_storage_access_key"  // string: 访问密钥
	CloudStorageSecretKeyKey   = "cloud_storage_secret_key"  // string: 密钥
	CloudStorageRegionKey      = "cloud_storage_region"      // string: 区域

	// 用户备份提醒相关
	UserBackupReminderPrefix   = "user_backup_reminder_"     // 用户备份提醒前缀
)
