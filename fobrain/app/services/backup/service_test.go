package backup

import (
	"sync"
	"testing"
	"time"

	testcommon "fobrain/fobrain/tests/common_test"

	"git.gobies.org/fobrain/unibackup/pkg/types"
	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
)

func TestIsEnabled(t *testing.T) {
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()

	// 测试用例1: 配置启用且服务已初始化
	t.Run("EnabledAndInitialized", func(t *testing.T) {
		// 重置全局状态
		resetGlobalState()

		// Mock配置查询
		mockDb.ExpectQuery("SELECT * FROM `system_configs` WHERE `key` IN").
			WillReturnRows(mockDb.NewRows([]string{"key", "value"}).
				AddRow(BackupEnabledKey, "true").
				AddRow(BackupTypeKey, "local"))

		// 由于依赖外部SDK，这里主要测试配置检查逻辑
		// 在实际环境中，IsEnabled会尝试初始化服务
		result := IsEnabled()
		// 由于无法mock外部SDK，这里主要验证不会panic
		assert.False(t, result) // 预期为false，因为SDK初始化会失败
	})

	// 测试用例2: 配置未启用
	t.Run("ConfigDisabled", func(t *testing.T) {
		// 重置全局状态
		resetGlobalState()

		// Mock配置查询
		mockDb.ExpectQuery("SELECT * FROM `system_configs` WHERE `key` IN").
			WillReturnRows(mockDb.NewRows([]string{"key", "value"}).
				AddRow(BackupEnabledKey, "false").
				AddRow(BackupTypeKey, "local"))

		result := IsEnabled()
		assert.False(t, result)
	})

	// 测试用例3: 配置查询失败
	t.Run("ConfigQueryError", func(t *testing.T) {
		// 重置全局状态
		resetGlobalState()

		// Mock配置查询失败
		mockDb.ExpectQuery("SELECT * FROM `system_configs` WHERE `key` IN").
			WillReturnError(sqlmock.ErrCancelled)

		result := IsEnabled()
		assert.False(t, result)
	})
}

func TestIsMergeTaskRunning(t *testing.T) {
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()

	// 测试用例1: 有融合任务正在运行
	t.Run("MergeTaskRunning", func(t *testing.T) {
		// Mock融合任务状态查询
		mockDb.ExpectQuery("SELECT count(*) FROM `merge_records` WHERE `task_status` = ?").
			WithArgs("执行中").
			WillReturnRows(mockDb.NewRows([]string{"count"}).AddRow(1))

		result := IsMergeTaskRunning()
		assert.True(t, result)
	})

	// 测试用例2: 没有融合任务正在运行
	t.Run("NoMergeTaskRunning", func(t *testing.T) {
		// Mock融合任务状态查询
		mockDb.ExpectQuery("SELECT count(*) FROM `merge_records` WHERE `task_status` = ?").
			WithArgs("执行中").
			WillReturnRows(mockDb.NewRows([]string{"count"}).AddRow(0))

		result := IsMergeTaskRunning()
		assert.False(t, result)
	})

	// 测试用例3: 查询错误
	t.Run("QueryError", func(t *testing.T) {
		// Mock查询失败
		mockDb.ExpectQuery("SELECT count(*) FROM `merge_records` WHERE `task_status` = ?").
			WithArgs("执行中").
			WillReturnError(sqlmock.ErrCancelled)

		result := IsMergeTaskRunning()
		assert.False(t, result) // 查询失败时返回false
	})
}

func TestUpdateLastBackupTime_Service(t *testing.T) {
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()

	testTime := time.Now()
	expectedTimeStr := testTime.Format(time.RFC3339)

	// Mock更新配置
	mockDb.ExpectExec("UPDATE `system_configs` SET `value`=?,`updated_at`=? WHERE `key` = ?").
		WithArgs(expectedTimeStr, sqlmock.AnyArg(), BackupLastFullTimeKey).
		WillReturnResult(sqlmock.NewResult(1, 1))

	// 测试对外接口
	assert.NotPanics(t, func() {
		UpdateLastBackupTime(BackupLastFullTimeKey, testTime)
	})
}

func TestGetManager(t *testing.T) {
	// 测试用例1: 管理器未初始化
	t.Run("ManagerNotInitialized", func(t *testing.T) {
		// 重置全局状态
		resetGlobalState()

		manager := GetManager()
		assert.Nil(t, manager)
	})
}

func TestIsBackupTaskRunning_Service(t *testing.T) {
	// 测试用例1: 管理器未初始化
	t.Run("ManagerNotInitialized", func(t *testing.T) {
		// 重置全局状态
		resetGlobalState()

		result := IsBackupTaskRunning(types.BackupTypeArchival)
		assert.False(t, result)
	})
}

// resetGlobalState 重置全局状态，用于测试
func resetGlobalState() {
	serviceMu.Lock()
	defer serviceMu.Unlock()

	manager = nil
	initialized = false
	// 重置once，允许重新初始化
	managerOnce = sync.Once{}
}

// 由于service.go中的大部分功能依赖外部unibackup SDK，
// 这里主要测试一些不依赖外部服务的逻辑函数

func TestInitService_ConfigError(t *testing.T) {
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()

	// 重置全局状态
	resetGlobalState()

	// Mock配置查询失败
	mockDb.ExpectQuery("SELECT * FROM `system_configs` WHERE `key` IN").
		WillReturnError(sqlmock.ErrCancelled)

	err := InitService()
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "获取备份配置失败")
}

func TestUpdateConfig_NotInitialized(t *testing.T) {
	// 重置全局状态
	resetGlobalState()

	err := UpdateConfig()
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "备份服务未初始化")
}

func TestShutdownService_NotInitialized(t *testing.T) {
	// 重置全局状态
	resetGlobalState()

	// 这个函数不应该panic，即使服务未初始化
	assert.NotPanics(t, func() {
		ShutdownService()
	})
}
