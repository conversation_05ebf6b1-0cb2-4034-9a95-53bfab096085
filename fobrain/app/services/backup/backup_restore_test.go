package backup

import (
	"context"
	"errors"
	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/pkg/cfg"
	"testing"
	"time"

	"github.com/alicebob/miniredis/v2"
	"github.com/go-redis/redis/v8"
)

// MockBackupRestoreManager 是 BackupRestoreManager 的 Mock 实现
type MockBackupRestoreManager struct {
	BackupFunc  func(ctx context.Context) error
	RestoreFunc func(ctx context.Context) error
}

func (m *MockBackupRestoreManager) BackupWithContext(ctx context.Context) error {
	if m.BackupFunc != nil {
		return m.BackupFunc(ctx)
	}
	return errors.New("BackupFunc not implemented")
}

func (m *MockBackupRestoreManager) RestoreWithContext(ctx context.Context) error {
	if m.RestoreFunc != nil {
		return m.RestoreFunc(ctx)
	}
	return errors.New("RestoreFunc not implemented")
}

// MockZipManager 是 ZipManager 的 Mock 实现
type MockZipManager struct {
	CreateEncryptedZipFromDirFunc func(dirPath, zipFilePath string, compressionLevel int) (string, error)
	DecryptAndExtractZipFunc      func(zipFilePath, outputDir string) error
}

func (m *MockZipManager) CreateEncryptedZipFromDir(dirPath, zipFilePath string, compressionLevel int) (string, error) {
	if m.CreateEncryptedZipFromDirFunc != nil {
		return m.CreateEncryptedZipFromDirFunc(dirPath, zipFilePath, compressionLevel)
	}
	return "", errors.New("CreateEncryptedZipFromDirFunc not implemented")
}

func (m *MockZipManager) DecryptAndExtractZip(zipFilePath, outputDir string) error {
	if m.DecryptAndExtractZipFunc != nil {
		return m.DecryptAndExtractZipFunc(zipFilePath, outputDir)
	}
	return errors.New("DecryptAndExtractZipFunc not implemented")
}

func TestManager_Backup(t *testing.T) {
	// 创建 Mock 依赖
	mockESManager := &MockBackupRestoreManager{
		BackupFunc: func(ctx context.Context) error {
			return nil // 模拟 Elasticsearch 备份成功
		},
	}
	mockMySQLManager := &MockBackupRestoreManager{
		BackupFunc: func(ctx context.Context) error {
			return nil // 模拟 MySQL 备份成功
		},
	}
	mockZipManager := &MockZipManager{
		CreateEncryptedZipFromDirFunc: func(dirPath, zipFilePath string, compressionLevel int) (string, error) {
			return cfg.LoadCommon().StoragePath + "/backup_data_zip/backup.zip", nil
		},
	}
	s, err := miniredis.Run()
	if err != nil {
		t.Fatal(err)
	}
	defer s.Close()
	cli := redis.NewClient(&redis.Options{
		Addr: s.Addr(),
	})
	testcommon.SetRedisClient(cli)

	// 创建 Manager 实例
	manager := &Manager{
		esManager:    mockESManager,
		mysqlManager: mockMySQLManager,
		zipManager:   mockZipManager,
		backupDir:    cfg.LoadCommon().StoragePath + "/backup_data",
		restoreDir:   cfg.LoadCommon().StoragePath + "/restore_data",
		zipFilePath:  cfg.LoadCommon().StoragePath + "/backup_data_zip/backup.zip",
		timeOut:      30 * time.Minute,
	}

	// 执行备份
	_, err = manager.Backup()
	if err != nil {
		t.Errorf("Backup failed: %v", err)
	}

}

func TestManager_Backup_Failure(t *testing.T) {
	// 模拟 Elasticsearch 备份失败
	mockESManager := &MockBackupRestoreManager{
		BackupFunc: func(ctx context.Context) error {
			return errors.New("Elasticsearch backup failed")
		},
	}
	mockMySQLManager := &MockBackupRestoreManager{
		BackupFunc: func(ctx context.Context) error {
			return nil
		},
	}
	mockZipManager := &MockZipManager{
		CreateEncryptedZipFromDirFunc: func(dirPath, zipFilePath string, compressionLevel int) (string, error) {
			return "", errors.New("ZIP creation failed")
		},
	}

	manager := &Manager{
		esManager:    mockESManager,
		mysqlManager: mockMySQLManager,
		zipManager:   mockZipManager,
		backupDir:    cfg.LoadCommon().StoragePath + "/backup_data",
		restoreDir:   cfg.LoadCommon().StoragePath + "/restore_data",
		zipFilePath:  cfg.LoadCommon().StoragePath + "/backup_data_zip/backup.zip",
		timeOut:      30 * time.Minute,
	}

	_, err := manager.Backup()
	if err == nil {
		t.Error("Expected backup to fail, but it succeeded")
	}
}

func TestManager_Restore(t *testing.T) {
	// 创建 Mock 依赖
	mockESManager := &MockBackupRestoreManager{
		RestoreFunc: func(ctx context.Context) error {
			return nil // 模拟 Elasticsearch 恢复成功
		},
	}
	mockMySQLManager := &MockBackupRestoreManager{
		RestoreFunc: func(ctx context.Context) error {
			return nil // 模拟 MySQL 恢复成功
		},
	}
	mockZipManager := &MockZipManager{
		DecryptAndExtractZipFunc: func(zipFilePath, outputDir string) error {
			return nil
		},
	}

	s, err := miniredis.Run()
	if err != nil {
		t.Fatal(err)
	}
	defer s.Close()
	cli := redis.NewClient(&redis.Options{
		Addr: s.Addr(),
	})
	testcommon.SetRedisClient(cli)

	// 创建 Manager 实例
	manager := &Manager{
		esManager:    mockESManager,
		mysqlManager: mockMySQLManager,
		zipManager:   mockZipManager,
		backupDir:    cfg.LoadCommon().StoragePath + "/backup_data",
		restoreDir:   cfg.LoadCommon().StoragePath + "/restore_data",
		zipFilePath:  cfg.LoadCommon().StoragePath + "/backup_data_zip/backup.zip",
		timeOut:      30 * time.Minute,
	}

	// 执行恢复
	err = manager.Restore(cfg.LoadCommon().StoragePath + "backup.zip")
	if err != nil {
		t.Errorf("Restore failed: %v", err)
	}
}

func TestManager_Restore_Failure(t *testing.T) {
	// 模拟 ZIP 文件解压失败
	mockESManager := &MockBackupRestoreManager{
		RestoreFunc: func(ctx context.Context) error {
			return nil
		},
	}
	mockMySQLManager := &MockBackupRestoreManager{
		RestoreFunc: func(ctx context.Context) error {
			return nil
		},
	}
	mockZipManager := &MockZipManager{
		DecryptAndExtractZipFunc: func(zipFilePath, outputDir string) error {
			return errors.New("ZIP extraction failed")
		},
	}

	manager := &Manager{
		esManager:    mockESManager,
		mysqlManager: mockMySQLManager,
		zipManager:   mockZipManager,
		backupDir:    cfg.LoadCommon().StoragePath + "/backup_data",
		restoreDir:   cfg.LoadCommon().StoragePath + "/restore_data",
		zipFilePath:  cfg.LoadCommon().StoragePath + "/backup_data_zip/backup.zip",
		timeOut:      30 * time.Minute,
	}

	err := manager.Restore(cfg.LoadCommon().StoragePath + "/backup_data_zip/backup.zip")
	if err == nil {
		t.Error("Expected restore to fail, but it succeeded")
	}
}
