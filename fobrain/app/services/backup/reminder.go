package backup

import (
	"fmt"
	"time"

	"fobrain/models/mysql/system_configs"
)

// ReminderStatus 备份提醒状态
type ReminderStatus struct {
	ShowPopup  bool `json:"show_popup"`  // 是否显示弹窗
	ShowScroll bool `json:"show_scroll"` // 是否显示右上角滚动提示
}

// GetReminderStatus 获取用户的备份提醒状态
func GetReminderStatus(userID uint64) ReminderStatus {
	// 1. 备份已开启 → 无任何提示
	if isBackupEnabled() {
		return ReminderStatus{false, false}
	}

	// 2. 用户已忽略弹窗 → 仅滚动提示
	if isPopupIgnored(uint64(int64(userID))) {
		return ReminderStatus{false, true}
	}

	// 3. 检查1个月期限
	if isWithinOneMonth(uint64(int64(userID))) {
		return ReminderStatus{true, true} // 弹窗 + 滚动
	}

	// 4. 超过1个月 → 仅滚动提示
	return ReminderStatus{false, true}
}

// IgnorePopupReminder 用户忽略弹窗提醒
func IgnorePopupReminder(userID uint64) error {
	configService := system_configs.NewSystemConfigs()
	key := fmt.Sprintf("%s%d_ignored", UserBackupReminderPrefix, userID)
	return configService.UpdateConfig(key, "true")
}

// isBackupEnabled 检查备份功能是否已开启
func isBackupEnabled() bool {
	configService := system_configs.NewSystemConfigs()
	enabled, _ := configService.GetConfig(BackupEnabledKey)
	return enabled == "true"
}

// isPopupIgnored 检查用户是否已忽略弹窗提醒
func isPopupIgnored(userID uint64) bool {
	configService := system_configs.NewSystemConfigs()
	key := fmt.Sprintf("%s%d_ignored", UserBackupReminderPrefix, userID)
	ignored, _ := configService.GetConfig(key)
	return ignored == "true"
}

// isWithinOneMonth 检查是否在1个月期限内
func isWithinOneMonth(userID uint64) bool {
	configService := system_configs.NewSystemConfigs()
	key := fmt.Sprintf("%s%d_first_time", UserBackupReminderPrefix, userID)

	firstTime, err := configService.GetConfig(key)
	if err != nil || firstTime == "" {
		// 首次提醒，记录时间
		configService.UpdateConfig(key, time.Now().Format(time.RFC3339))
		return true
	}

	// 检查是否超过30天
	t, err := time.Parse(time.RFC3339, firstTime)
	if err != nil {
		return true // 解析失败，默认显示提醒
	}

	return time.Since(t) <= 30*24*time.Hour
}
