package backup

import (
	"testing"
	"time"

	testcommon "fobrain/fobrain/tests/common_test"

	"git.gobies.org/fobrain/unibackup/pkg/types"
	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
)

func TestGetBackupConfig(t *testing.T) {
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()

	// 测试用例1: 获取完整配置
	t.Run("GetCompleteConfig", func(t *testing.T) {
		// Mock system_configs查询
		mockDb.ExpectQuery("SELECT * FROM `system_configs` WHERE `key` IN (?,?,?,?,?,?,?,?,?,?,?,?,?)").
			WithArgs(BackupEnabledKey, BackupTypeKey, BackupFullIntervalDaysKey, BackupIncrIntervalHoursKey, BackupRetentionDaysKey, BackupLastFullTimeKey, BackupLastIncrTimeK<PERSON>, CloudStorageTypeKey, CloudStorageEndpointKey, CloudStorageBucketKey, CloudStorageAccessKeyKey, CloudStorageSecretKeyKey, CloudStorageRegionKey).
			WillReturnRows(mockDb.NewRows([]string{"key", "value"}).
				AddRow(BackupEnabledKey, "true").
				AddRow(BackupTypeKey, "local").
				AddRow(BackupFullIntervalDaysKey, "7").
				AddRow(BackupIncrIntervalHoursKey, "6").
				AddRow(BackupRetentionDaysKey, "30").
				AddRow(BackupLastFullTimeKey, "2023-01-01T00:00:00Z").
				AddRow(BackupLastIncrTimeKey, "2023-01-01T06:00:00Z").
				AddRow(CloudStorageTypeKey, "s3").
				AddRow(CloudStorageEndpointKey, "http://localhost:9000").
				AddRow(CloudStorageBucketKey, "test-bucket").
				AddRow(CloudStorageAccessKeyKey, "access-key").
				AddRow(CloudStorageSecretKeyKey, "secret-key").
				AddRow(CloudStorageRegionKey, "us-east-1"))

		config, err := getBackupConfig()
		assert.NoError(t, err)
		assert.NotNil(t, config)
		assert.True(t, config.Enabled)
		assert.Equal(t, "local", config.Type)
		assert.Equal(t, 7, config.FullIntervalDays)
		assert.Equal(t, 6, config.IncrIntervalHours)
		assert.Equal(t, 30, config.RetentionDays)
		assert.Equal(t, "s3", config.CloudStorageType)
		assert.Equal(t, "http://localhost:9000", config.CloudStorageEndpoint)
		assert.Equal(t, "test-bucket", config.CloudStorageBucket)
		assert.Equal(t, "access-key", config.CloudStorageAccessKey)
		assert.Equal(t, "secret-key", config.CloudStorageSecretKey)
		assert.Equal(t, "us-east-1", config.CloudStorageRegion)
	})

	// 测试用例2: 获取默认配置（配置不存在）
	t.Run("GetDefaultConfig", func(t *testing.T) {
		mockDb.ExpectQuery("SELECT * FROM `system_configs` WHERE `key` IN (?,?,?,?,?,?,?,?,?,?,?,?,?)").
			WithArgs(BackupEnabledKey, BackupTypeKey, BackupFullIntervalDaysKey, BackupIncrIntervalHoursKey, BackupRetentionDaysKey, BackupLastFullTimeKey, BackupLastIncrTimeKey, CloudStorageTypeKey, CloudStorageEndpointKey, CloudStorageBucketKey, CloudStorageAccessKeyKey, CloudStorageSecretKeyKey, CloudStorageRegionKey).
			WillReturnRows(mockDb.NewRows([]string{"key", "value"}))

		config, err := getBackupConfig()
		assert.NoError(t, err)
		assert.NotNil(t, config)
		assert.False(t, config.Enabled)              // 默认为false
		assert.Equal(t, "local", config.Type)        // 默认为local
		assert.Equal(t, 1, config.FullIntervalDays)  // 默认为1
		assert.Equal(t, 6, config.IncrIntervalHours) // 默认为6
		assert.Equal(t, 30, config.RetentionDays)    // 默认为30
	})

	// 测试用例3: 数据库查询错误
	t.Run("DatabaseError", func(t *testing.T) {
		mockDb.ExpectQuery("SELECT * FROM `system_configs` WHERE `key` IN (?,?,?,?,?,?,?,?,?,?,?,?,?)").
			WithArgs(BackupEnabledKey, BackupTypeKey, BackupFullIntervalDaysKey, BackupIncrIntervalHoursKey, BackupRetentionDaysKey, BackupLastFullTimeKey, BackupLastIncrTimeKey, CloudStorageTypeKey, CloudStorageEndpointKey, CloudStorageBucketKey, CloudStorageAccessKeyKey, CloudStorageSecretKeyKey, CloudStorageRegionKey).
			WillReturnError(sqlmock.ErrCancelled)

		config, err := getBackupConfig()
		assert.Error(t, err)
		assert.Nil(t, config)
		assert.Contains(t, err.Error(), "获取备份配置失败")
	})
}

func TestGetBackupConfig_Public(t *testing.T) {
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()

	mockDb.ExpectQuery("SELECT * FROM `system_configs` WHERE `key` IN (?,?,?,?,?,?,?,?,?,?,?,?,?)").
		WithArgs(BackupEnabledKey, BackupTypeKey, BackupFullIntervalDaysKey, BackupIncrIntervalHoursKey, BackupRetentionDaysKey, BackupLastFullTimeKey, BackupLastIncrTimeKey, CloudStorageTypeKey, CloudStorageEndpointKey, CloudStorageBucketKey, CloudStorageAccessKeyKey, CloudStorageSecretKeyKey, CloudStorageRegionKey).
		WillReturnRows(mockDb.NewRows([]string{"key", "value"}).
			AddRow(BackupEnabledKey, "true").
			AddRow(BackupTypeKey, "cloud"))

	config, err := GetBackupConfig()
	assert.NoError(t, err)
	assert.NotNil(t, config)
	assert.True(t, config.Enabled)
	assert.Equal(t, "cloud", config.Type)
}

func TestBuildSDKConfig(t *testing.T) {
	// 准备测试配置
	config := &BackupConfig{
		Enabled:               true,
		Type:                  "cloud",
		FullIntervalDays:      7,
		IncrIntervalHours:     6,
		RetentionDays:         30,
		CloudStorageType:      "s3",
		CloudStorageEndpoint:  "http://localhost:9000",
		CloudStorageBucket:    "test-bucket",
		CloudStorageAccessKey: "access-key",
		CloudStorageSecretKey: "secret-key",
		CloudStorageRegion:    "us-east-1",
	}

	sdkConfig := buildSDKConfig(config)

	assert.NotNil(t, sdkConfig)
	assert.Equal(t, 5, sdkConfig.MaxConcurrentTasks)
	assert.Equal(t, 30, sdkConfig.TaskRetentionDays)
	assert.True(t, sdkConfig.CleanupBackupData)
	assert.Equal(t, types.Duration(24*time.Hour), sdkConfig.BackupTimeout)
	assert.Equal(t, types.Duration(24*time.Hour), sdkConfig.RestoreTimeout)

	// 测试MySQL配置
	assert.NotNil(t, sdkConfig.MySQL)

	// 测试ES配置
	assert.NotNil(t, sdkConfig.ES)
	assert.Equal(t, "fobrain-archival", sdkConfig.ES.ArchivalRepoName)
	assert.Equal(t, "fobrain-chains", sdkConfig.ES.ManagedRepoName)
	assert.True(t, sdkConfig.ES.AutoCreateRepos)

	// 测试云存储配置
	assert.NotNil(t, sdkConfig.CloudStorage)
	assert.True(t, sdkConfig.CloudStorage.Enabled)
	assert.Equal(t, "s3", sdkConfig.CloudStorage.Type)
	assert.Equal(t, "http://localhost:9000", sdkConfig.CloudStorage.Endpoint)
	assert.Equal(t, "test-bucket", sdkConfig.CloudStorage.Bucket)
	assert.Equal(t, "access-key", sdkConfig.CloudStorage.AccessKey)
	assert.Equal(t, "secret-key", sdkConfig.CloudStorage.SecretKey)
	assert.Equal(t, "us-east-1", sdkConfig.CloudStorage.Region)
}

func TestBuildSDKConfig_LocalType(t *testing.T) {
	// 测试本地类型配置
	config := &BackupConfig{
		Enabled:           true,
		Type:              "local",
		FullIntervalDays:  1,
		IncrIntervalHours: 12,
		RetentionDays:     7,
	}

	sdkConfig := buildSDKConfig(config)

	assert.NotNil(t, sdkConfig)
	assert.Nil(t, sdkConfig.CloudStorage) // 本地类型不应该有云存储配置
}

func TestParseTime(t *testing.T) {
	tests := []struct {
		name     string
		timeStr  string
		expected bool // 是否期望返回非nil
	}{
		{
			name:     "ValidTime",
			timeStr:  "2023-01-01T00:00:00Z",
			expected: true,
		},
		{
			name:     "EmptyString",
			timeStr:  "",
			expected: false,
		},
		{
			name:     "InvalidFormat",
			timeStr:  "invalid-time",
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := parseTime(tt.timeStr)
			if tt.expected {
				assert.NotNil(t, result)
			} else {
				assert.Nil(t, result)
			}
		})
	}
}

func TestUpdateLastBackupTime(t *testing.T) {
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()

	testTime := time.Now()

	// Mock更新配置
	mockDb.ExpectExec("SELECT * FROM `system_configs` WHERE `key` = ? ORDER BY `system_configs`.`id` LIMIT 1").
		WithArgs(BackupLastFullTimeKey).
		WillReturnResult(sqlmock.NewResult(1, 1))

	// 这个函数没有返回值，我们只是确保它不会panic
	assert.NotPanics(t, func() {
		updateLastBackupTime(BackupLastFullTimeKey, testTime)
	})
}

func TestGetStringWithDefault(t *testing.T) {
	configs := map[string]string{
		"existing_key": "existing_value",
		"empty_key":    "",
	}

	// 测试存在的key
	result := getStringWithDefault(configs, "existing_key", "default")
	assert.Equal(t, "existing_value", result)

	// 测试不存在的key
	result = getStringWithDefault(configs, "non_existing_key", "default")
	assert.Equal(t, "default", result)

	// 测试空值的key
	result = getStringWithDefault(configs, "empty_key", "default")
	assert.Equal(t, "default", result)
}

func TestGetIntWithDefault(t *testing.T) {
	configs := map[string]string{
		"valid_int":   "123",
		"invalid_int": "abc",
		"empty_key":   "",
	}

	// 测试有效的整数
	result := getIntWithDefault(configs, "valid_int", 999)
	assert.Equal(t, 123, result)

	// 测试无效的整数
	result = getIntWithDefault(configs, "invalid_int", 999)
	assert.Equal(t, 999, result)

	// 测试不存在的key
	result = getIntWithDefault(configs, "non_existing_key", 999)
	assert.Equal(t, 999, result)

	// 测试空值的key
	result = getIntWithDefault(configs, "empty_key", 999)
	assert.Equal(t, 999, result)
}
