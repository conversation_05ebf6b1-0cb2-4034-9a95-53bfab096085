package backup

import (
	"context"
	"fmt"
	"sync"
	"time"

	"git.gobies.org/fobrain/unibackup/pkg/unibackup"

	"fobrain/fobrain/logs"
	"fobrain/initialize/mysql"
	"fobrain/models/mysql/merge"

	"git.gobies.org/fobrain/unibackup/pkg/types"
)

var (
	manager     unibackup.BackupManager
	serviceMu   sync.RWMutex
	initialized bool
)

// InitService 初始化备份服务
func InitService() error {
	serviceMu.Lock()
	defer serviceMu.Unlock()

	// 如果已经初始化成功，直接返回
	if initialized && manager != nil {
		return nil
	}

	config, err := getBackupConfig()
	if err != nil {
		logs.GetLogger().Errorf("获取备份配置失败: %v", err)
		return fmt.Errorf("获取备份配置失败: %v", err)
	}

	sdkConfig := buildSDKConfig(config)
	mgr, err := unibackup.NewManager(sdkConfig)
	if err != nil {
		logs.GetLogger().Errorf("创建备份管理器失败: %v", err)
		return fmt.Errorf("创建备份管理器失败: %v", err)
	}

	manager = mgr
	initialized = true

	if config.Enabled {
		logs.GetLogger().Info("备份服务初始化成功，备份功能已启用")
	} else {
		logs.GetLogger().Info("备份服务初始化成功，备份功能未启用")
	}

	return nil
}

// UpdateConfig 动态更新配置
func UpdateConfig() error {
	serviceMu.Lock()
	defer serviceMu.Unlock()

	if !initialized || manager == nil {
		return fmt.Errorf("备份服务未初始化")
	}

	config, err := getBackupConfig()
	if err != nil {
		return err
	}

	newSDKConfig := buildSDKConfig(config)
	if err := manager.UpdateConfig(newSDKConfig); err != nil {
		return fmt.Errorf("动态更新配置失败: %v", err)
	}

	logs.GetLogger().Info("备份配置已动态更新")
	return nil
}

// GetManager 获取备份管理器实例
func GetManager() unibackup.BackupManager {
	serviceMu.RLock()
	defer serviceMu.RUnlock()
	return manager
}

// IsEnabled 检查备份服务是否启用（带自动重试初始化）
func IsEnabled() bool {
	serviceMu.RLock()
	isInit := initialized && manager != nil
	serviceMu.RUnlock()

	// 如果未初始化，尝试初始化
	if !isInit {
		if err := InitService(); err != nil {
			logs.GetLogger().Warnf("备份服务初始化失败: %v", err)
			return false
		}
	}

	serviceMu.RLock()
	defer serviceMu.RUnlock()

	if !initialized || manager == nil {
		return false
	}

	// 动态检查配置状态
	config, err := getBackupConfig()
	if err != nil {
		logs.GetLogger().Errorf("获取备份配置失败: %v", err)
		return false
	}

	return config.Enabled
}

// isBackupTaskRunning 检查指定类型的备份任务是否正在运行
func isBackupTaskRunning(backupType types.BackupType) bool {
	manager := GetManager()
	if manager == nil {
		return false
	}

	// 构建过滤条件，查询正在运行的指定类型备份任务
	filter := types.BackupFilter{
		BackupTypes: []types.BackupType{backupType},
		Limit:       10, // 只需要检查是否存在，不需要太多数据
	}

	result, err := manager.ListAllBackups(context.Background(), filter)
	if err != nil {
		logs.GetLogger().Errorf("查询备份任务状态失败: %v", err)
		return false
	}

	// 检查是否有正在运行的任务
	for _, task := range result.Tasks {
		if task.Status == types.TaskStatusRunning || task.Status == types.TaskStatusPending {
			return true
		}
	}

	return false
}

// isMergeTaskRunning 检查是否有融合任务正在运行
func isMergeTaskRunning() bool {
	count, err := merge.NewMergeRecordsModel().Count(mysql.WithColumnValue("task_status", merge.MergeRecordsStatusRunning))
	if err != nil {
		logs.GetLogger().Errorf("查询融合任务状态失败: %v", err)
		return false
	}
	return count > 0
}

// IsBackupTaskRunning 对外提供的检查备份任务是否正在运行的接口
func IsBackupTaskRunning(backupType types.BackupType) bool {
	return isBackupTaskRunning(backupType)
}

// IsMergeTaskRunning 对外提供的检查融合任务是否正在运行的接口
func IsMergeTaskRunning() bool {
	return isMergeTaskRunning()
}

// UpdateLastBackupTime 对外提供的更新上次备份时间接口
func UpdateLastBackupTime(key string, t time.Time) {
	updateLastBackupTime(key, t)
}

// ShutdownService 优雅关闭备份服务
func ShutdownService() {
	serviceMu.Lock()
	defer serviceMu.Unlock()

	if !initialized || manager == nil {
		logs.GetLogger().Info("备份服务未初始化，无需关闭")
		return
	}

	logs.GetLogger().Info("开始关闭备份服务...")

	// 检查是否有正在运行的任务
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 等待正在运行的任务完成（最多等待30秒）
	for {
		select {
		case <-ctx.Done():
			logs.GetLogger().Warn("等待备份任务完成超时，强制关闭")
			goto cleanup
		default:
			// 检查是否还有运行中的任务
			filter := types.BackupFilter{
				Limit: 10,
			}

			result, err := manager.ListAllBackups(context.Background(), filter)
			if err != nil {
				logs.GetLogger().Errorf("查询备份任务状态失败: %v", err)
				goto cleanup
			}

			hasRunning := false
			for _, task := range result.Tasks {
				if task.Status == types.TaskStatusRunning || task.Status == types.TaskStatusPending {
					hasRunning = true
					break
				}
			}

			if !hasRunning {
				logs.GetLogger().Info("所有备份任务已完成")
				goto cleanup
			}

			logs.GetLogger().Info("等待备份任务完成...")
			time.Sleep(2 * time.Second)
		}
	}

cleanup:
	// 调用SDK的优雅关闭方法
	if err := manager.Shutdown(); err != nil {
		logs.GetLogger().Errorf("关闭备份管理器失败: %v", err)
	} else {
		logs.GetLogger().Info("备份管理器已优雅关闭")
	}

	// 清理状态
	manager = nil
	initialized = false

	logs.GetLogger().Info("备份服务已关闭")
}
