package backup

import (
	"fmt"
	"path/filepath"
	"strconv"
	"time"

	"fobrain/fobrain/logs"
	"fobrain/models/mysql/system_configs"
	"fobrain/pkg/cfg"

	"git.gobies.org/fobrain/unibackup/pkg/types"
)

// getBackupConfig 获取备份配置
func getBackupConfig() (*BackupConfig, error) {
	configService := system_configs.NewSystemConfigs()

	// 获取所有备份相关配置
	keys := []string{
		BackupEnabledKey,
		BackupTypeKey,
		BackupFullIntervalDaysKey,
		BackupIncrIntervalHoursKey,
		BackupRetentionDaysKey,
		BackupLastFullTimeKey,
		BackupLastIncrTimeKey,
		// 云存储配置
		CloudStorageTypeKey,
		CloudStorageEndpointKey,
		CloudStorageBucketKey,
		CloudStorageAccessKeyKey,
		CloudStorageSecretKeyKey,
		CloudStorageRegionKey,
	}

	configs, err := configService.GetMultiConfig(keys)
	if err != nil {
		return nil, fmt.Errorf("获取备份配置失败: %v", err)
	}

	// 解析配置值，提供默认值
	enabled := configs[BackupEnabledKey] == "true"
	backupType := getStringWithDefault(configs, BackupTypeKey, "local")
	fullIntervalDays := getIntWithDefault(configs, BackupFullIntervalDaysKey, 1)
	incrIntervalHours := getIntWithDefault(configs, BackupIncrIntervalHoursKey, 6)
	retentionDays := getIntWithDefault(configs, BackupRetentionDaysKey, 30)
	lastFullTime := getStringWithDefault(configs, BackupLastFullTimeKey, "")
	lastIncrTime := getStringWithDefault(configs, BackupLastIncrTimeKey, "")

	// 云存储配置
	cloudStorageType := getStringWithDefault(configs, CloudStorageTypeKey, "s3")
	cloudStorageEndpoint := getStringWithDefault(configs, CloudStorageEndpointKey, "")
	cloudStorageBucket := getStringWithDefault(configs, CloudStorageBucketKey, "")
	cloudStorageAccessKey := getStringWithDefault(configs, CloudStorageAccessKeyKey, "")
	cloudStorageSecretKey := getStringWithDefault(configs, CloudStorageSecretKeyKey, "")
	cloudStorageRegion := getStringWithDefault(configs, CloudStorageRegionKey, "")

	return &BackupConfig{
		Enabled:           enabled,
		Type:              backupType,
		FullIntervalDays:  fullIntervalDays,
		IncrIntervalHours: incrIntervalHours,
		RetentionDays:     retentionDays,
		LastFullTime:      parseTime(lastFullTime),
		LastIncrTime:      parseTime(lastIncrTime),

		// 云存储配置
		CloudStorageType:      cloudStorageType,
		CloudStorageEndpoint:  cloudStorageEndpoint,
		CloudStorageBucket:    cloudStorageBucket,
		CloudStorageAccessKey: cloudStorageAccessKey,
		CloudStorageSecretKey: cloudStorageSecretKey,
		CloudStorageRegion:    cloudStorageRegion,
	}, nil
}

// GetBackupConfig 对外提供的获取备份配置接口
func GetBackupConfig() (*BackupConfig, error) {
	return getBackupConfig()
}

// buildSDKConfig 构建SDK配置
func buildSDKConfig(config *BackupConfig) *types.Config {
	// 使用系统配置的存储路径下的backup目录
	commonCfg := cfg.LoadCommon()
	backupRoot := filepath.Join(commonCfg.StoragePath, "backup")

	sdkConfig := &types.Config{
		BackupRoot:         backupRoot,
		Logger:             logs.GetBackupLogger().Logger, // 使用系统logger
		MaxConcurrentTasks: 5,
		TaskRetentionDays:  config.RetentionDays,
		MaxTaskHistory:     0,
		CleanupBackupData:  true, // 默认启用自动清理
		BackupTimeout:      types.Duration(24 * time.Hour),
		RestoreTimeout:     types.Duration(24 * time.Hour),
	}

	// 默认配置MySQL
	mysqlCfg := cfg.LoadMysql()
	sdkConfig.MySQL = &types.MySQLConfig{
		Host:     mysqlCfg.Address,
		Port:     mysqlCfg.Port,
		User:     mysqlCfg.UserName,
		Password: mysqlCfg.Password,
		DBName:   mysqlCfg.Database,
		ToolsPath: types.MySQLToolsPath{
			Mysqlbinlog: commonCfg.MysqlBinlogPath,
			Mysqldump:   commonCfg.MysqlDumpBinPath,
			Mysql:       commonCfg.MysqlBinPath,
			Mysqladmin:  commonCfg.MysqlAdminBinPath,
		},
		BinlogBasePath: commonCfg.BinlogBasePath,
	}

	// 默认配置Elasticsearch
	esCfg := cfg.LoadElastic()
	sdkConfig.ES = &types.ESConfig{
		Addresses:        []string{fmt.Sprintf("http://%s:%d", esCfg.Address, esCfg.Port)},
		User:             esCfg.UserName,
		Password:         esCfg.Password,
		ArchivalRepoName: "fobrain-archival",
		ManagedRepoName:  "fobrain-chains",
		AutoCreateRepos:  true,
		RepoBasePath:     commonCfg.RepoBasePath,
	}

	// 配置云存储
	if config.Type == "cloud" {
		sdkConfig.CloudStorage = &types.CloudStorageConfig{
			Enabled:   true,
			Type:      config.CloudStorageType,
			Endpoint:  config.CloudStorageEndpoint,
			Bucket:    config.CloudStorageBucket,
			AccessKey: config.CloudStorageAccessKey,
			SecretKey: config.CloudStorageSecretKey,
			Region:    config.CloudStorageRegion,
		}
	}

	return sdkConfig
}

// parseTime 解析时间字符串
func parseTime(timeStr string) *time.Time {
	if timeStr == "" {
		return nil
	}

	t, err := time.Parse(time.RFC3339, timeStr)
	if err != nil {
		return nil
	}

	return &t
}

// updateLastBackupTime 更新上次备份时间
func updateLastBackupTime(key string, t time.Time) {
	configService := system_configs.NewSystemConfigs()
	configService.UpdateConfig(key, t.Format(time.RFC3339))
}

// getStringWithDefault 从配置map中获取字符串值，如果不存在则返回默认值
func getStringWithDefault(configs map[string]string, key, defaultValue string) string {
	if value, exists := configs[key]; exists && value != "" {
		return value
	}
	return defaultValue
}

// getIntWithDefault 从配置map中获取整数值，如果不存在或解析失败则返回默认值
func getIntWithDefault(configs map[string]string, key string, defaultValue int) int {
	if value, exists := configs[key]; exists && value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}
