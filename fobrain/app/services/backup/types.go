package backup

import (
	"time"
)

// BackupConfig 备份配置结构
type BackupConfig struct {
	Enabled           bool       `json:"enabled"`
	Type              string     `json:"type"`                    // local/cloud
	FullIntervalDays  int        `json:"full_interval_days"`
	IncrIntervalHours int        `json:"incr_interval_hours"`
	RetentionDays     int        `json:"retention_days"`
	LastFullTime      *time.Time `json:"last_full_time,omitempty"` // 系统维护，不对外暴露修改
	LastIncrTime      *time.Time `json:"last_incr_time,omitempty"` // 系统维护，不对外暴露修改

	// 云存储配置（当Type为cloud时使用）
	CloudStorageType    string `json:"cloud_storage_type"`
	CloudStorageEndpoint string `json:"cloud_storage_endpoint"`
	CloudStorageBucket  string `json:"cloud_storage_bucket"`
	CloudStorageAccessKey string `json:"cloud_storage_access_key"`
	CloudStorageSecretKey string `json:"cloud_storage_secret_key"`
	CloudStorageRegion  string `json:"cloud_storage_region"`
}
