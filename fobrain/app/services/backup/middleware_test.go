package backup

import (
	"context"
	"testing"

	testcommon "fobrain/fobrain/tests/common_test"

	"github.com/alicebob/miniredis/v2"
	redis2 "github.com/go-redis/redis/v8"
	"github.com/stretchr/testify/assert"
)

// setupMockRedis 设置模拟Redis
func setupMockRedis(t *testing.T) (*miniredis.Miniredis, *redis2.Client) {
	s, err := miniredis.Run()
	if err != nil {
		t.Fatal(err)
	}

	client := redis2.NewClient(&redis2.Options{Addr: s.Addr()})
	testcommon.SetRedisClient(client)

	return s, client
}

// TestMatchesPattern 测试路径匹配函数
func TestMatchesPattern(t *testing.T) {
	tests := []struct {
		name     string
		path     string
		pattern  string
		expected bool
	}{
		{"完全匹配", "/api/v1/system/backup/v2/config", "/api/v1/system/backup/v2/config", true},
		{"路径参数匹配", "/api/v1/system/backup/v2/task/123", "/api/v1/system/backup/v2/task", true},
		{"前缀匹配", "/api/v1/system/backup/v2/task/123/status", "/api/v1/system/backup/v2/", true},
		{"不匹配", "/api/v1/other/endpoint", "/api/v1/system/backup/v2/config", false},
		{"空字符串匹配", "", "", true},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := matchesPattern(tt.path, tt.pattern)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// TestConstants 测试常量定义
func TestConstants(t *testing.T) {
	assert.Equal(t, "backup_v2_global_lock", BackupGlobalLockKey)
	assert.Equal(t, "backup_v2_task_status:", TaskStatusCachePrefix)
	assert.Equal(t, 300, TaskStatusCacheTTL)
}

// TestHasLegacyBackupLock 测试旧版本锁检查
func TestHasLegacyBackupLock(t *testing.T) {
	s, client := setupMockRedis(t)
	defer s.Close()
	defer client.Close()

	// 测试没有锁的情况
	result := hasLegacyBackupLock()
	assert.False(t, result)

	// 测试有锁的情况
	client.Set(context.Background(), "backup_or_restore_lock", "test", 0)
	result = hasLegacyBackupLock()
	assert.True(t, result)

	// 清理并测试其他锁
	client.Del(context.Background(), "backup_or_restore_lock")
	client.Set(context.Background(), "backup:progress", "test", 0)
	result = hasLegacyBackupLock()
	assert.True(t, result)
}

// TestSetAndReleaseGlobalLock 测试全局锁设置和释放
func TestSetAndReleaseGlobalLock(t *testing.T) {
	s, client := setupMockRedis(t)
	defer s.Close()
	defer client.Close()

	// 测试设置锁
	err := SetGlobalLock("test reason")
	assert.NoError(t, err)

	// 验证锁已设置
	val, err := client.Get(context.Background(), BackupGlobalLockKey).Result()
	assert.NoError(t, err)
	assert.Equal(t, "test reason", val)

	// 测试释放锁
	err = ReleaseGlobalLock()
	assert.NoError(t, err)

	// 验证锁已释放
	_, err = client.Get(context.Background(), BackupGlobalLockKey).Result()
	assert.Error(t, err) // 应该返回redis.Nil错误
}

// TestClearTaskStatusCache 测试清理任务状态缓存
func TestClearTaskStatusCache(t *testing.T) {
	s, client := setupMockRedis(t)
	defer s.Close()
	defer client.Close()

	// 设置一些缓存
	client.Set(context.Background(), TaskStatusCachePrefix+"backup", "running", 0)
	client.Set(context.Background(), TaskStatusCachePrefix+"restore", "stopped", 0)
	client.Set(context.Background(), "other_key", "value", 0)

	// 清理缓存
	ClearTaskStatusCache()

	// 验证相关缓存已清理
	_, err := client.Get(context.Background(), TaskStatusCachePrefix+"backup").Result()
	assert.Error(t, err)
	_, err = client.Get(context.Background(), TaskStatusCachePrefix+"restore").Result()
	assert.Error(t, err)

	// 验证其他键未受影响
	val, err := client.Get(context.Background(), "other_key").Result()
	assert.NoError(t, err)
	assert.Equal(t, "value", val)
}

// TestForceUnlock 测试强制解锁
func TestForceUnlock(t *testing.T) {
	s, client := setupMockRedis(t)
	defer s.Close()
	defer client.Close()

	// 设置各种锁和缓存
	client.Set(context.Background(), BackupGlobalLockKey, "test", 0)
	client.Set(context.Background(), TaskStatusCachePrefix+"backup", "running", 0)
	client.Set(context.Background(), "backup_or_restore_lock", "test", 0)
	client.Set(context.Background(), "backup:progress", "test", 0)
	client.Set(context.Background(), "restore:progress", "test", 0)

	// 执行强制解锁
	err := ForceUnlock()
	assert.NoError(t, err)

	// 验证所有锁都已清理
	_, err = client.Get(context.Background(), BackupGlobalLockKey).Result()
	assert.Error(t, err)
	_, err = client.Get(context.Background(), TaskStatusCachePrefix+"backup").Result()
	assert.Error(t, err)
	_, err = client.Get(context.Background(), "backup_or_restore_lock").Result()
	assert.Error(t, err)
}

// TestMiddleware_WithRedis 测试中间件在有Redis锁时的行为
// 注意：由于中间件依赖备份服务初始化，这里主要测试Redis锁检测逻辑
func TestMiddleware_WithRedis(t *testing.T) {
	// 设置mock Redis
	s, client := setupMockRedis(t)
	defer s.Close()
	defer client.Close()

	// 测试hasLegacyBackupLock函数
	// 测试1: 没有锁时应该返回false
	assert.False(t, hasLegacyBackupLock())

	// 测试2: 设置backup_or_restore_lock后应该返回true
	client.Set(context.Background(), "backup_or_restore_lock", "test", 0)
	assert.True(t, hasLegacyBackupLock())

	// 清除锁
	client.Del(context.Background(), "backup_or_restore_lock")
	assert.False(t, hasLegacyBackupLock())

	// 测试3: 设置backup:progress后应该返回true
	client.Set(context.Background(), "backup:progress", "test", 0)
	assert.True(t, hasLegacyBackupLock())

	// 清除锁
	client.Del(context.Background(), "backup:progress")
	assert.False(t, hasLegacyBackupLock())

	// 测试4: 设置restore:progress后应该返回true
	client.Set(context.Background(), "restore:progress", "test", 0)
	assert.True(t, hasLegacyBackupLock())
}
