package backup

import (
	"context"
	"net/http"
	"time"

	"fobrain/fobrain/common/response"
	"fobrain/initialize/redis"

	"git.gobies.org/fobrain/unibackup/pkg/types"
	"git.gobies.org/fobrain/unibackup/pkg/unibackup"
	"github.com/gin-gonic/gin"
)

const (
	// 备份恢复全局锁键
	BackupGlobalLockKey = "backup_v2_global_lock"
	// 任务状态缓存键前缀
	TaskStatusCachePrefix = "backup_v2_task_status:"
	// 缓存过期时间（秒）
	TaskStatusCacheTTL = 300 // 5分钟
)

// Middleware 新版备份系统的全局锁中间件
func Middleware() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		// 检查是否有备份/恢复任务正在运行
		if isAnyBackupOrRestoreRunning() {
			// 白名单路径，允许通过
			allowedPaths := []string{
				"/api/v1/system/backup/v2/config",
				"/api/v1/system/backup/v2/list",
				"/api/v1/system/backup/v2/task",
				"/api/v1/user",
				"/api/v1/user_access/my_menus_tree",
				// 保持与旧版本的兼容性
				"/api/v1/system/backup/export",
				"/api/v1/system/backup/upload",
				"/api/v1/system/backup/checkout",
				"/api/v1/system/backup/process",
			}

			// 检查当前路径是否在白名单中
			currentPath := ctx.FullPath()
			allowed := false
			for _, path := range allowedPaths {
				if currentPath == path || matchesPattern(currentPath, path) {
					allowed = true
					break
				}
			}

			if !allowed {
				response.FailWithDetailed(ctx, http.StatusLocked, map[string]string{
					"type": "backup_v2",
				}, "系统正在执行备份/恢复操作，请稍后再试")
				ctx.Abort()
				return
			}
		}
		ctx.Next()
	}
}

// isAnyBackupOrRestoreRunning 检查是否有任何备份或恢复任务正在运行
func isAnyBackupOrRestoreRunning() bool {
	if !IsEnabled() {
		return false
	}

	manager := GetManager()
	if manager == nil {
		return false
	}

	// // 检查是否有正在运行的备份任务
	// if hasRunningBackupTasks(manager) {
	// 	return true
	// }

	// 检查是否有正在运行的恢复任务
	if hasRunningRestoreTasks(manager) {
		return true
	}

	// 检查Redis中的全局锁（兼容旧版本）
	if hasLegacyBackupLock() {
		return true
	}

	return false
}

// hasRunningBackupTasks 检查是否有正在运行的备份任务
func hasRunningBackupTasks(manager unibackup.BackupManager) bool {
	return hasRunningTasksWithCache(manager, false)
}

// hasRunningRestoreTasks 检查是否有正在运行的恢复任务
func hasRunningRestoreTasks(manager unibackup.BackupManager) bool {
	return hasRunningTasksWithCache(manager, true)
}

// hasRunningTasksWithCache 带缓存的任务状态检查
func hasRunningTasksWithCache(manager unibackup.BackupManager, includeRestore bool) bool {
	client := redis.GetRedisClient()
	ctx := context.Background()

	// 构建缓存键
	cacheKey := TaskStatusCachePrefix + "backup"
	if includeRestore {
		cacheKey = TaskStatusCachePrefix + "restore"
	}

	// 尝试从缓存获取
	cached, err := client.Get(ctx, cacheKey).Result()
	if err == nil {
		// 缓存命中，返回缓存结果
		return cached == "running"
	}

	// 缓存未命中，查询SDK
	filter := types.BackupFilter{
		TaskTypes: []types.TaskType{types.RestoreAllTask},
		Statuses:  []types.TaskStatus{types.TaskStatusRunning, types.TaskStatusPending},
		Limit:     50, // 检查最近的50个任务
	}
	result, err := manager.ListAllBackups(ctx, filter)
	if err != nil {
		// 查询失败，缓存"无运行任务"状态，避免频繁查询
		client.Set(ctx, cacheKey, "stopped", time.Duration(TaskStatusCacheTTL)*time.Second)
		return false
	}

	// 检查是否有运行中的任务
	hasRunning := false
	if len(result.Tasks) > 0 {
		hasRunning = true
	}
	// 缓存结果
	status := "stopped"
	if hasRunning {
		status = "running"
	}
	client.Set(ctx, cacheKey, status, time.Duration(TaskStatusCacheTTL)*time.Second)

	return hasRunning
}

// hasLegacyBackupLock 检查是否有旧版本的备份锁（兼容性）
func hasLegacyBackupLock() bool {
	client := redis.GetRedisClient()

	// 检查旧版本的锁
	legacyKeys := []string{
		"backup_or_restore_lock",
		"backup:progress",
		"restore:progress",
	}

	for _, key := range legacyKeys {
		if client.Exists(context.Background(), key).Val() > 0 {
			return true
		}
	}

	return false
}

// matchesPattern 检查路径是否匹配模式（支持简单的通配符）
func matchesPattern(path, pattern string) bool {
	// 简单的前缀匹配，支持 /api/v1/system/backup/v2/task 匹配 /api/v1/system/backup/v2/task/:id
	if len(pattern) > 0 && pattern[len(pattern)-1] == '/' {
		return len(path) >= len(pattern) && path[:len(pattern)] == pattern
	}

	// 支持路径参数匹配
	if len(path) > len(pattern) && path[:len(pattern)] == pattern && path[len(pattern)] == '/' {
		return true
	}

	return path == pattern
}

// SetGlobalLock 设置全局锁（供手动控制使用）
func SetGlobalLock(reason string) error {
	client := redis.GetRedisClient()
	return client.Set(context.Background(), BackupGlobalLockKey, reason, 0).Err()
}

// ReleaseGlobalLock 释放全局锁
func ReleaseGlobalLock() error {
	client := redis.GetRedisClient()
	return client.Del(context.Background(), BackupGlobalLockKey).Err()
}

// ClearTaskStatusCache 清理任务状态缓存
func ClearTaskStatusCache() {
	client := redis.GetRedisClient()
	ctx := context.Background()

	// 清理所有任务状态缓存
	pattern := TaskStatusCachePrefix + "*"
	keys, err := client.Keys(ctx, pattern).Result()
	if err != nil {
		return
	}

	if len(keys) > 0 {
		client.Del(ctx, keys...)
	}
}

// ForceUnlock 强制解锁（紧急情况使用）
func ForceUnlock() error {
	client := redis.GetRedisClient()
	ctx := context.Background()

	// 清理全局锁
	client.Del(ctx, BackupGlobalLockKey)

	// 清理任务状态缓存
	ClearTaskStatusCache()

	// 清理旧版本锁（兼容性）
	legacyKeys := []string{
		"backup_or_restore_lock",
		"backup:progress",
		"restore:progress",
	}

	for _, key := range legacyKeys {
		client.Del(ctx, key)
	}

	return nil
}
