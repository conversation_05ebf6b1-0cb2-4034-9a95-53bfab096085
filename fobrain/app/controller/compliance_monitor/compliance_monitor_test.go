package compliance_monitor

import (
	"encoding/json"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"

	reqmonitor "fobrain/fobrain/app/request/compliance_monitor"
	testcommon "fobrain/fobrain/tests/common_test"
)

func TestList(t *testing.T) {

	t.Run("param err", func(t *testing.T) {
		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/v1/compliance_monitors", nil)

		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := Index(c)
		assert.Error(t, err)
		assert.Equal(t, err.Error(), "页数为必填字段")
	})

	t.Run("success", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()
		mockDb.ExpectQuery("SELECT count(*) FROM `compliance_monitors` WHERE event in (?)").
			WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(1))
		mockDb.ExpectQuery("SELECT * FROM `compliance_monitors` WHERE event in (?) ORDER BY created_at DESC LIMIT 10").
			WillReturnRows(mockDb.NewRows([]string{"id", "name"}).AddRow(1, "name"))

		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/v1/compliance_monitors?page=1&per_page=10&event=1", strings.NewReader(`{
			"event": 1,
    	}`))

		req.Header.Set("Content-Type", "application/json")
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := Index(c)
		assert.Nil(t, err)
		assert.NotEmpty(t, w.Body.String())
	})

}

func TestStore(t *testing.T) {
	t.Run("params err", func(t *testing.T) {
		// 创建一个测试请求和响应
		w := httptest.NewRecorder()
		req := httptest.NewRequest("POST", "/api/v1/compliance_monitors", nil)
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		// 调用函数
		err := Store(c)

		assert.NotNil(t, err)
		assert.Equal(t, err.Error(), "规则名称为必填字段")
	})
	t.Run("pass", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()
		mockDb.ExpectBegin()
		mockDb.ExpectExec("INSERT INTO `compliance_monitors`").
			WillReturnResult(sqlmock.NewResult(1, 1))
		mockDb.ExpectCommit()

		val, _ := json.Marshal(reqmonitor.StoreRequest{
			Name:      "合规监测名称",
			Desc:      "合规监测名称描述",
			Rule:      "test",
			AssetType: 0,
			Event:     1,
			Date:      "",
			Time:      "",
		})

		// 创建一个测试请求和响应
		w := httptest.NewRecorder()
		req := httptest.NewRequest("POST", "/api/v1/compliance_monitors", strings.NewReader(string(val)))
		req.Header.Set("Content-Type", "application/json")

		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		// 调用函数
		err := Store(c)

		assert.Nil(t, err)
	})
}

func TestEdit(t *testing.T) {
	t.Run("params err", func(t *testing.T) {
		// 创建一个测试请求和响应
		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/v1/compliance_monitors/:id/edit", nil)
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		// 调用函数
		err := Edit(c)
		assert.NotNil(t, err)
		assert.Equal(t, err.Error(), "合规监测ID为必填字段")
	})
	t.Run("pass", func(t *testing.T) {
		mockDb := testcommon.GetMysqlMock()
		defer mockDb.Close()
		mockDb.ExpectQuery("SELECT * FROM `compliance_monitors` WHERE `id` = ? ORDER BY `compliance_monitors`.`id` LIMIT 1").
			WillReturnRows(mockDb.NewRows([]string{"id", "name"}).AddRow(1, "name"))

		// 创建一个测试请求和响应
		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/v1/compliance_monitors/:id/edit?id=1", nil)
		req.Header.Set("Content-Type", "application/json")

		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		// 调用函数
		err := Edit(c)

		assert.Nil(t, err)
	})
}

func TestUpdate(t *testing.T) {
	t.Run("params err", func(t *testing.T) {
		// 创建一个测试请求和响应
		w := httptest.NewRecorder()
		req := httptest.NewRequest("PUT", "/api/v1/compliance_monitors", nil)
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		// 调用函数
		err := Update(c)
		assert.NotNil(t, err)
		assert.Equal(t, err.Error(), "规则id为必填字段")
	})
	t.Run("pass", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()
		mockDb.ExpectBegin()
		mockDb.ExpectExec("UPDATE `compliance_monitors`").
			WillReturnResult(sqlmock.NewResult(1, 1))
		mockDb.ExpectCommit()

		val, _ := json.Marshal(reqmonitor.UpdateRequest{
			Id:        1,
			Name:      "合规监测名称",
			Desc:      "合规监测名称描述",
			Rule:      "test",
			AssetType: 0,
			Event:     1,
			Date:      "",
			Time:      "",
		})

		// 创建一个测试请求和响应
		w := httptest.NewRecorder()
		req := httptest.NewRequest("PUT", "/api/v1/compliance_monitors", strings.NewReader(string(val)))
		req.Header.Set("Content-Type", "application/json")

		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		// 调用函数
		err := Update(c)

		assert.Nil(t, err)
	})
}

func TestSetStatus(t *testing.T) {
	t.Run("params err", func(t *testing.T) {
		val, _ := json.Marshal(reqmonitor.SetStatusRequest{
			//Ids:        1,
			Status: 1,
		})
		// 创建一个测试请求和响应
		w := httptest.NewRecorder()
		req := httptest.NewRequest("PUT", "/api/v1/compliance_monitors/set_status", strings.NewReader(string(val)))
		req.Header.Set("Content-Type", "application/json")
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		// 调用函数
		err := SetStatus(c)
		assert.NotNil(t, err)
		assert.Equal(t, err.Error(), "规则id为必填字段")
	})

	t.Run("pass", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()
		mockDb.ExpectBegin()
		mockDb.ExpectExec("UPDATE `compliance_monitors`").
			WillReturnResult(sqlmock.NewResult(1, 1))
		mockDb.ExpectCommit()

		val, _ := json.Marshal(reqmonitor.SetStatusRequest{
			Id:     1,
			Status: 1,
		})

		// 创建一个测试请求和响应
		w := httptest.NewRecorder()
		req := httptest.NewRequest("PUT", "/api/v1/compliance_monitors/set_status", strings.NewReader(string(val)))
		req.Header.Set("Content-Type", "application/json")

		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		// 调用函数
		err := SetStatus(c)

		assert.Nil(t, err)
	})
}

func TestExec(t *testing.T) {
	t.Run("params err", func(t *testing.T) {

		// 创建一个测试请求和响应
		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/v1/compliance_monitors/:id/start", nil)
		req.Header.Set("Content-Type", "application/json")
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		// 调用函数
		err := Exec(c)
		assert.NotNil(t, err)
		assert.Equal(t, err.Error(), "合规监测ID为必填字段")
	})

	t.Run("pass", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()
		mockDb.ExpectBegin()
		mockDb.ExpectExec("INSERT INTO `compliance_monitor_tasks`").
			WillReturnResult(sqlmock.NewResult(1, 1))
		mockDb.ExpectCommit()

		// 创建一个测试请求和响应
		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/v1/compliance_monitors/:id/start?id=1", nil)
		req.Header.Set("Content-Type", "application/json")

		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		// 调用函数
		err := Exec(c)

		assert.Nil(t, err)
	})
}
