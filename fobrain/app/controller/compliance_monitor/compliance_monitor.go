package compliance_monitor

import (
	"fobrain/fobrain/app/repository/compliance_monitor"
	reqmonitor "fobrain/fobrain/app/request/compliance_monitor"
	"fobrain/fobrain/common/request"
	"fobrain/fobrain/common/response"
	modelMonitor "fobrain/models/elastic/compliance_monitor"
	"github.com/gin-gonic/gin"
)

// Index 合规监测列表
// @Route /api/v1/compliance_monitors [GET]
func Index(c *gin.Context) error {
	params, err := request.Validate(c, &reqmonitor.IndexRequest{})
	if err != nil {
		return err
	}

	data, total, err := compliance_monitor.Index(params)
	if err != nil {
		return err
	}

	return response.OkWithPageData(c, total, params.Page, params.PerPage, data)
}

// Store 创建合规监测
// @Route /api/v1/compliance_monitors [POST]
func Store(c *gin.Context) error {
	params, err := request.Validate(c, &reqmonitor.StoreRequest{})
	if err != nil {
		return err
	}

	err = compliance_monitor.Store(c, params)
	if err != nil {
		return err
	}

	return response.Ok(c)
}

// Edit 合规监测编辑回写
// @Route /api/v1/compliance_monitors/:id/edit [GET]
func Edit(c *gin.Context) error {
	params, err := request.Validate(c, &struct {
		Id uint64 `json:"id" uri:"id" form:"id" validate:"required,number" zh:"合规监测ID"`
	}{})
	if err != nil {
		return err
	}

	edit, err := compliance_monitor.Edit(params.Id)
	if err != nil {
		return err
	}

	return response.OkWithData(c, edit)
}

// Update 合规监测编辑
// @Route /api/v1/compliance_monitors [PUT]
func Update(c *gin.Context) error {
	params, err := request.Validate(c, &reqmonitor.UpdateRequest{})
	if err != nil {
		return err
	}

	err = compliance_monitor.Update(c, params)
	if err != nil {
		return err
	}

	return response.Ok(c)
}

// SetStatus 合规监测状态设置
// @Route /api/v1/compliance_monitors/set_status [PUT]
func SetStatus(c *gin.Context) error {
	params, err := request.Validate(c, &reqmonitor.SetStatusRequest{})
	if err != nil {
		return err
	}

	err = compliance_monitor.SetStatus(params)
	if err != nil {
		return err
	}

	return response.Ok(c)
}

// Exec 合规监测立即执行
// @Route /api/v1/compliance_monitors/:id/start [GET]
func Exec(c *gin.Context) error {
	params, err := request.Validate(c, &struct {
		Id uint64 `json:"id" uri:"id" form:"id" validate:"required,number" zh:"合规监测ID"`
	}{})
	if err != nil {
		return err
	}

	err = compliance_monitor.Exec(params.Id)
	if err != nil {
		return err
	}

	return response.Ok(c)
}

// Show 合规监测详情
// @Route /api/v1/compliance_monitors/:id [PUT]
func Show(c *gin.Context) error {
	return nil
}

// Destroy 合规监测删除
// @Route /api/v1/compliance_monitors [DELETE]
func Destroy(c *gin.Context) error {
	params, err := request.Validate(c, &struct {
		Ids     []uint64 `json:"ids" uri:"ids" form:"ids" validate:"required" zh:"合规监测ID"`
		Keyword string   `json:"keyword" form:"keyword" uri:"keyword" validate:"omitempty,max=100" zh:"模糊搜索"`
	}{})
	if err != nil {
		return err
	}

	err = compliance_monitor.Destroy(params.Ids, params.Keyword)
	if err != nil {
		return err
	}

	return response.Ok(c)
}
func RiskPortsList(c *gin.Context) error {
	networkType := c.Query("network_type")
	// 1 - 内网资产 2 - 互联网资产
	if networkType == "2" {
		return response.OkWithData(c, map[string]any{
			"items": modelMonitor.InternetHighRiskPorts,
		})
	} else {
		return response.OkWithData(c, map[string]any{
			"items": modelMonitor.IntranetHighRiskPorts,
		})
	}
}
