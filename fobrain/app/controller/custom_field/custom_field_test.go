package custom_field

import (
	"encoding/json"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/tidwall/gjson"

	testcommon "fobrain/fobrain/tests/common_test"
)

func TestListByTable(t *testing.T) {

	t.Run("param err", func(t *testing.T) {
		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/v1/custom_fields", nil)

		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := ListByTable(c)
		assert.Error(t, err)
		assert.Equal(t, err.Error(), "Table为必填字段")
	})

	t.Run("success", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()
		mockDb.ExpectQuery("SELECT * FROM `custom_fields` WHERE `table` = ?").
			WillReturnRows(sqlmock.NewRows([]string{"id", "table", "field_name", "display_name"}).AddRow(1, "users", "name", "姓名"))

		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/v1/custom_fields?page=1&per_page=10&table=users", strings.NewReader(`{
			"event": 1,
    	}`))

		req.Header.Set("Content-Type", "application/json")
		c, _ := gin.CreateTestContext(w)
		c.Request = req
		err := ListByTable(c)

		assert.Nil(t, err)
		assert.NotEmpty(t, w.Body.String())
		assert.Equal(t, gjson.Get(w.Body.String(), "data.0.display_name").String(), "姓名")
	})

}

func TestCreateOrUpdate(t *testing.T) {
	t.Run("params err", func(t *testing.T) {
		// 创建一个测试请求和响应
		w := httptest.NewRecorder()
		req := httptest.NewRequest("POST", "/api/v1/custom_fields", nil)
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		// 调用函数
		err := CreateOrUpdate(c)

		assert.NotNil(t, err)
	})
	t.Run("pass", func(t *testing.T) {
		mockDb := testcommon.GetMysqlMock()
		defer mockDb.Close()

		mockDb.ExpectQuery("SELECT * FROM `custom_fields` WHERE (`display_name` = ? OR `field_name` = ?) AND `table` = ? ORDER BY `custom_fields`.`id` LIMIT 1").
			WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))

		mockDb.Mock.ExpectBegin()
		mockDb.ExpectExec(
			"UPDATE `custom_fields` SET `id`=?,`updated_at`=?,`display_name`=?,`field_name`=?,`field_type`=?,`table`=?,`sort`=? WHERE `id` = ?",
		).WithArgs(1, sqlmock.AnyArg(), "姓名", "name").WillReturnResult(sqlmock.NewResult(1, 1))
		mockDb.Mock.ExpectCommit()

		val, err := json.Marshal(map[string]interface{}{
			"table": "users",
			"fields": []map[string]interface{}{
				{
					"id":           1,
					"display_name": "姓名",
					"field_name":   "name",
					"sort":         1,
				},
			},
		})

		// 创建一个测试请求和响应
		w := httptest.NewRecorder()
		req := httptest.NewRequest("POST", "/api/v1/custom_fields", strings.NewReader(string(val)))
		req.Header.Set("Content-Type", "application/json")

		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		// 调用函数
		err = CreateOrUpdate(c)

		assert.Nil(t, err)
	})
}
