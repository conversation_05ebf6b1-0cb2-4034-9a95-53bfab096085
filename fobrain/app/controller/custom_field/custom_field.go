package custom_field

import (
	custom_field_service "fobrain/fobrain/app/services/custom_field"
	"fobrain/initialize/mysql"
	"github.com/gin-gonic/gin"

	"fobrain/fobrain/common/request"
	"fobrain/fobrain/common/response"
	"fobrain/models/mysql/custom_field"
)

func ListByTable(c *gin.Context) error {
	params, err := request.Validate(c, &struct {
		Table string `form:"table" validate:"required"`
	}{})
	if err != nil {
		return err
	}

	list, err := custom_field.NewCustomFieldModel().ListByTable(params.Table)
	if err != nil {
		return err
	}
	return response.OkWithData(c, list)
}

func CreateOrUpdate(c *gin.Context) error {
	params, err := request.Validate(c, &struct {
		Table  string `json:"table" validate:"required"`
		Fields []struct {
			Id          uint64 `json:"id"`
			DisplayName string `json:"display_name"`
			FieldName   string `json:"field_name"`
			Sort        int    `json:"sort"`
		} `json:"fields"`
	}{})
	if err != nil {
		return err
	}
	var newFields []struct {
		Id          uint64 `json:"id"`
		DisplayName string `json:"display_name"`
		FieldName   string `json:"field_name"`
		Sort        int    `json:"sort"`
	}
	for _, field := range params.Fields {
		if field.Id > 0 && field.DisplayName == "" && field.FieldName == "" { //删除
			err = custom_field.NewCustomFieldModel().Delete(
				mysql.WithWhere("`id` = ?", field.Id),
			)
			if err != nil {
				return err
			}
			continue
		} else {
			newFields = append(newFields, field)
		}
	}

	if len(newFields) > 10 {
		return response.FailWithMessage(c, "自定义字段数必须小于10")
	}

	for _, field := range newFields {
		err = custom_field_service.UpdateOrCreate(&custom_field.CustomField{
			BaseModel: mysql.BaseModel{
				Id: field.Id,
			},
			DisplayName:   field.DisplayName,
			FieldName:     field.FieldName,
			FieldType:     "string",
			Table:         params.Table,
			Sort:          field.Sort,
			IsSystemField: false,
		})
	}

	return response.OkWithMessage(c, "更新成功")
}
