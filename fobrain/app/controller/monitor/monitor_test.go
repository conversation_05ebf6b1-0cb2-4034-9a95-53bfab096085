package monitor

import (
	testcommon "fobrain/fobrain/tests/common_test"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"net/http/httptest"
	"testing"
	"time"
)

func TestList(t *testing.T) {
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()
	createdAtTime, _ := time.Parse("2006-01-02 15:04:05", "2024-08-29 12:00:00")

	mockDb.ExpectQuery("SELECT count(*) FROM `monitors`").
		WillReturnRows(mockDb.NewRows([]string{"count"}).AddRow(1))

	mockDb.ExpectQuery("SELECT * FROM `monitors` WHERE `period` = ? AND `name` = ? AND deleted_at IS NULL AND `monitors`.`deleted_at` IS NULL ORDER BY status asc, id asc LIMIT 20").
		WithArgs("0", "111aaa").
		WillReturnRows(mockDb.NewRows([]string{"id", "name", "desc", "event", "status", "admin_name", "last_time"}).
			AddRow(1, "111", "111", "fast", 0, "admin", createdAtTime))

	mockDb.ExpectQuery("SELECT `created_at` FROM `monitor_task_ips` WHERE task_id = ? AND `monitor_task_ips`.`deleted_at` IS NULL ORDER BY created_at DESC,`monitor_task_ips`.`id` LIMIT 1").
		WithArgs(1).
		WillReturnRows(mockDb.NewRows([]string{"created_at"}).AddRow(createdAtTime))

	mockDb.ExpectQuery("SELECT SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) AS last_normal_asset, SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END) AS last_abnormal_asset, COUNT(*) AS last_asset, MAX(elapsed) AS elapsed FROM `monitor_task_ips` WHERE task_id = ? AND `monitor_task_ips`.`deleted_at` IS NULL AND (task_id = ? AND created_at = ?) ORDER BY created_at DESC,`monitor_task_ips`.`id` LIMIT 1").
		WithArgs(1, 1, createdAtTime).
		WillReturnRows(mockDb.NewRows([]string{"last_normal_asset", "last_abnormal_asset", "last_asset", "elapsed"}).AddRow(5, 0, 5, 1.64))

	// 创建一个测试请求和响应
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/monitor?page=1&per_page=20&name=111aaa&period=0", nil)
	// 创建一个Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 调用函数
	err := List(c)

	// 断言错误为 nil
	assert.Empty(t, err)
	assert.Equal(t, `{"code":0,"message":"Success","data":{"total":1,"page":1,"per_page":20,"items":[{"id":1,"name":"111","desc":"111","event":"fast","status":0,"admin_name":"admin","start_at":"2024-08-29 12:00:00","end_at":"2024-08-29 12:00:01","elapsed":1,"last_abnormal_asset":0}]}}`, w.Body.String())
}

func TestRuleListById(t *testing.T) {
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()

	mockDb.ExpectQuery("SELECT * FROM `monitors` WHERE `monitors`.`id` = ? AND `monitors`.`deleted_at` IS NULL ORDER BY `monitors`.`id` LIMIT 1").
		WithArgs(1).
		WillReturnRows(mockDb.NewRows([]string{"name", "desc", "rule", "asset_type", "event", "date", "time", "notices"}).
			AddRow("111", "111", "[{\"id\":\"1\",\"name\":\"port\",\"op\":\"in\",\"value\":\"111\",\"logic\":\"\"}]", 0, "week", 8, "10:00", "{\"is_notice\":false,\"notice_obj\":[]}"))

	// 创建一个测试请求和响应
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/monitor/rules?id=1", nil)

	// 创建一个Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 调用函数
	err := RuleList(c)

	// 断言错误为 nil
	assert.Empty(t, err)
	assert.Equal(t, `{"code":0,"message":"Success","data":{"name":"111","desc":"111","rules":[{"id":"1","name":"port","op":"in","value":"111","logic":""}],"asset_type":0,"event":"week","date":"8","time":"10:00","notices":"{\"is_notice\":false,\"notice_obj\":[]}"}}`, w.Body.String())
}
