package monitor

import (
	"fobrain/fobrain/app/services/monitor"
	"fobrain/fobrain/common/request"
	"fobrain/fobrain/common/response"
	"fobrain/initialize/mysql"
	"fobrain/models/mysql/monitor_asset"
	"github.com/gin-gonic/gin"
)

// 根据规则查询资产
func ExecuteSelect(c *gin.Context) error {
	params, err := request.Validate(c, &monitor_asset.AssetRequest{})
	if err != nil {
		return err
	}
	err = monitor_asset.Execute(params.ID)
	if mysql.IsNotFound(err) {
		return response.FailWithMessage(c, "规则不存在")
	} else if err != nil {
		_ = monitor_asset.NewMonModel().TaskSet(params.ID, -1, 2)
		return response.FailWithMessage(c, "资产查询失败")
	}
	return response.OkWithData(c, gin.H{"succ": "执行成功"})
}

func ListAsset(c *gin.Context) error {
	var list any
	var total int

	params, err := request.Validate(c, &monitor_asset.AssetListRequest{})
	if err != nil {
		return err
	}
	list, total, err = monitor.Details(params)

	return response.OkWithPageData(c, int64(total), params.Page, params.PerPage, list)
}

// 根据id删除资产
func DeleteAsset(c *gin.Context) error {
	params, err := request.Validate(c, &monitor_asset.AssetRequest{})
	if err != nil {
		return err
	}
	// ID 为asset_id
	er := monitor.DeleteAsset(params.ID)
	if er != nil {
		return response.FailWithMessage(c, "删除失败")
	}
	return response.OkWithData(c, gin.H{"succ": "删除成功"})
}

// 批量导出资产数据
func ExportAsset(c *gin.Context) error {
	params, err := request.Validate(c, &monitor_asset.AssetExport{})
	if err != nil {
		return err
	}
	info, er := monitor.ExportExcel(params)
	if er != nil {
		return response.FailWithMessage(c, "导出失败")
	}
	return response.OkWithData(c, gin.H{"succ": info})
}
