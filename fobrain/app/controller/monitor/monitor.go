package monitor

import (
	"fobrain/fobrain/app/services/monitor"
	"fobrain/fobrain/common/request"
	"fobrain/fobrain/common/response"
	"fobrain/models/mysql/monitor_asset"
	"github.com/gin-gonic/gin"
)

func Insert(c *gin.Context) error {
	params, err := request.Validate(c, &monitor_asset.CreateReq{})
	if err != nil {
		return err
	}
	er := monitor.InitMonitor(params)
	if er != nil {
		// 任务创建成功，但任务是仅执行一次，所设置的执行时间本来就是过期的，所以任务不会被执行；由于没有定错误code，所以直接返回TimePast
		if er.Error() == monitor_asset.TimePast {
			return response.OkWithData(c, gin.H{"succ": monitor_asset.TimePast})
		} else {
			return response.FailWithMessage(c, "创建规则失败")
		}
	}
	return response.OkWithData(c, gin.H{"succ": "创建成功"})
}
func List(c *gin.Context) error {
	var list any
	var total int64

	params, err := request.Validate(c, &monitor_asset.ListReq{})
	if err != nil {
		return err
	}
	if params.Period == "0" {
		list, total, err = monitor.QueryMonitor(params)
		if err != nil {
			return response.FailWithMessage(c, "查询失败")
		}
	} else {
		list, total, err = monitor.QueryPeriodMonitor(params)
		if err != nil {
			return response.FailWithMessage(c, "查询失败")
		}
	}

	return response.OkWithPageData(c, total, params.Page, params.PerPage, list)
}
func Delete(c *gin.Context) error {
	params, err := request.Validate(c, &monitor_asset.IDReq{})
	if err != nil {
		return err
	}
	er := monitor.DeleteMonitor(params.Ids)
	if er != nil {
		return response.FailWithMessage(c, "删除失败")
	}
	return response.OkWithData(c, gin.H{"succ": "删除成功"})
}
func Update(c *gin.Context) error {
	params, err := request.Validate(c, &monitor_asset.UpdateReq{})
	if err != nil {
		return err
	}
	er := monitor.UpdateMonitor(params)
	if er != nil {
		// 更新成功，但仅执行一次的任务，所设置的执行时间是过期的，所以任务不会被执行
		if er.Error() == monitor_asset.TimePast {
			return response.OkWithData(c, gin.H{"succ": monitor_asset.TimePast})
		} else {
			return response.FailWithMessage(c, "更新失败")
		}
	}
	return response.OkWithData(c, gin.H{"succ": "更新成功"})
}
func RuleList(c *gin.Context) error {
	params, err := request.Validate(c, &monitor_asset.IDReq{})
	if err != nil {
		return err
	}
	list, er := monitor.RuleListMonitor(params)
	if er != nil {
		return response.FailWithMessage(c, "规则查询失败")
	}
	return response.OkWithData(c, list)
}
func Switch(c *gin.Context) error {
	params, err := request.Validate(c, &monitor_asset.SwitchReq{})
	if err != nil {
		return err
	}
	er := monitor.SwitchMonitor(params)
	if er != nil {
		return response.FailWithMessage(c, "操作失败")
	}
	return response.OkWithData(c, gin.H{"succ": "操作成功"})
}
