package field_tag_rules

import (
	"errors"

	"github.com/gin-gonic/gin"
	"go-micro.dev/v4/logger"

	res "fobrain/fobrain/app/repository/field_tag_rules"
	req "fobrain/fobrain/app/request/field_tag_rules"
	"fobrain/fobrain/common/request"
	"fobrain/fobrain/common/response"
)

// Create 函数用于处理创建操作
//
// 参数:
//
//	c: gin框架的上下文对象
//
// 返回值:
//
//	如果操作成功，返回nil；否则返回错误对象
//
// 功能描述:
//  1. 调用request.Validate函数验证请求参数，并将结果存储在params中
//  2. 如果验证失败，则返回错误对象
//  3. 调用res.TrigOnce函数执行某些操作，如果执行失败，则返回错误对象
//  4. 如果所有操作都成功，则调用response.OkWithMessage函数返回成功消息和状态码
func Create(c *gin.Context) error {
	params, err := request.Validate(c, &req.InsertOrUpdateFieldTagRules{})
	if err != nil {
		return err
	}
	params.UserId = request.GetUserId(c)
	_, err = res.CreateOrUpdate(params, 1, true)
	if err != nil {
		logger.Errorf("CreateOrUpdate err: %v", err)
		return errors.New("添加失败:" + err.Error())
	}
	return response.OkWithMessage(c, "添加成功")
}

// Update 函数用于处理更新操作
//
// 参数:
//
//	c: gin框架的上下文对象
//
// 返回值:
//
//	如果操作成功，返回nil；否则返回错误对象
//
// 功能描述:
//  1. 调用request.Validate函数验证请求参数，并将结果存储在params中
//  2. 如果验证失败，则返回错误对象
//  3. 检查params中的Id字段是否小于1，如果是，则返回错误对象，并提示"id不能为空"
//  4. 调用res.TrigOnce函数执行某些操作，如果执行失败，则返回错误对象
//  5. 如果所有操作都成功，则调用response.OkWithMessage函数返回成功消息和状态码
func Update(c *gin.Context) error {
	params, err := request.Validate(c, &req.InsertOrUpdateFieldTagRules{})
	if err != nil {
		return err
	}
	if params.Id < 1 {
		return errors.New("id不能为空")
	}

	_, err = res.CreateOrUpdate(params, 1, true)
	if err != nil {
		return err
	}
	return response.OkWithMessage(c, "修改成功")
}

func List(c *gin.Context) error {
	params, err := request.Validate(c, &req.FieldTagRulesList{})
	if err != nil {
		return err
	}
	list, total, err := res.List(params)
	if err != nil {
		return err
	}
	return response.OkWithPageData(c, total, params.Page, params.PerPage, list)
}

// FieldOrSetFiledList 函数用于处理获取字段或设置字段列表的请求
//
// 参数:
//
//	c: gin框架的上下文对象
//
// 返回值:
//
//	如果操作成功，返回nil；否则返回错误对象
//
// 功能描述:
//  1. 调用res.FieldOrSetFiledList函数获取字段或设置字段的列表
//  2. 调用response.OkWithData函数将获取到的列表作为响应数据返回给客户端
func FieldOrSetFiledList(c *gin.Context) error {
	list := res.FieldOrSetFiledList()
	return response.OkWithData(c, list)
}

// DeleteByIds 函数用于处理删除多个记录的请求
func DeleteByIds(c *gin.Context) error {
	params, err := request.Validate(c, &req.DelFieldTagRulesList{})
	if err != nil {
		return err
	}
	err = res.DeleteByIds(params)
	if err != nil {
		return err
	}
	return response.OkWithMessage(c, "删除成功")
}
