package field_tag_rules

import (
	"bytes"
	"io/ioutil"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"

	res "fobrain/fobrain/app/repository/field_tag_rules"
)

func TestCreate(t *testing.T) {
	// 创建一个测试请求和响应
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyFuncReturn(res.CreateOrUpdate, uint64(0), nil).Reset()

	str := `{
		"condition": "ip",
		"relation": "=",
		"condition_content": "127.0.0.1",
		"tag_type": "bussiness",
		"tag_content": "管理入口",
		"tag_content_id": "1fc3c1bd34d34046bff59fbfcb5d377a"
	}`
	w := httptest.NewRecorder()
	req := httptest.NewRequest("POST", "/api/v1/tag_rules", bytes.NewBufferString(str))
	req.Header.Set("Content-Type", "application/json")
	// 创建一个Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 调用函数
	err := Create(c)
	assert.NoError(t, err)

	// 解析响应数据
	response := w.Result()
	defer response.Body.Close()
	body, _ := ioutil.ReadAll(response.Body)
	assert.NotEmpty(t, body)
}

func TestUpdate(t *testing.T) {
	// 创建一个测试请求和响应
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyFuncReturn(res.CreateOrUpdate, uint64(1), nil).Reset()

	str := `{
		"id":2,
		"condition": "ip",
		"relation": "=",
		"condition_content": "*********",
		"tag_type": "bussiness",
		"tag_content": "管理入口",
		"tag_content_id": "1fc3c1bd34d34046bff59fbfcb5d377a"
	}`
	w := httptest.NewRecorder()
	req := httptest.NewRequest("PUT", "/api/v1/tag_rules", bytes.NewBufferString(str))
	req.Header.Set("Content-Type", "application/json")
	// 创建一个Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 调用函数
	err := Update(c)
	assert.NoError(t, err)

	// 解析响应数据
	response := w.Result()
	defer response.Body.Close()
	body, _ := ioutil.ReadAll(response.Body)
	assert.NotEmpty(t, body)
}

func TestList(t *testing.T) {
	t.Run("Parameter Error", func(t *testing.T) {
		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/v1/tag_rules?page=1&per_page=aaa", nil)
		req.Header.Set("Content-Type", "application/json")
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		// 调用函数
		err := List(c)
		assert.Error(t, err)
	})
	t.Run("Success", func(t *testing.T) {
		time.Sleep(time.Second)
		defer gomonkey.ApplyFuncReturn(res.List, nil, int64(0), nil).Reset()
		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/v1/tag_rules?page=1&per_page=10", nil)
		req.Header.Set("Content-Type", "application/json")
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		// 调用函数
		err := List(c)
		assert.NoError(t, err)

		// 解析响应数据
		response := w.Result()
		defer response.Body.Close()
		body, _ := ioutil.ReadAll(response.Body)
		assert.NotEmpty(t, body)
	})
}

func TestFieldOrSetFiledList(t *testing.T) {

	t.Run("Success", func(t *testing.T) {
		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/v1/tag_fields", nil)
		req.Header.Set("Content-Type", "application/json")
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		// 调用函数
		err := FieldOrSetFiledList(c)
		assert.NoError(t, err)

		// 解析响应数据
		response := w.Result()
		defer response.Body.Close()
		body, _ := ioutil.ReadAll(response.Body)
		assert.NotEmpty(t, body)
	})
}

func TestDeletes(t *testing.T) {
	// 创建一个测试请求和响应
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyFuncReturn(res.DeleteByIds, nil).Reset()

	str := `{"ids":[1]}`
	w := httptest.NewRecorder()
	req := httptest.NewRequest("DELETE", "/api/v1/tag_rules", bytes.NewBufferString(str))
	req.Header.Set("Content-Type", "application/json")
	// 创建一个Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 调用函数
	err := DeleteByIds(c)
	assert.NoError(t, err)

	// 解析响应数据
	response := w.Result()
	defer response.Body.Close()
	body, _ := ioutil.ReadAll(response.Body)
	assert.NotEmpty(t, body)
}
