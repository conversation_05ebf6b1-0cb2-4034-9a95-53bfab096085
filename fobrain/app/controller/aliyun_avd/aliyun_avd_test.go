package aliyun_avd

import (
	testcommon "fobrain/fobrain/tests/common_test"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"io/ioutil"
	"net/http/httptest"
	"testing"
)

func TestList(t *testing.T) {
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()

	mockDb.ExpectQuery("SELECT count(*) FROM `aliyun_avd`").
		WillReturnRows(mockDb.NewRows([]string{"count"}).AddRow(1))

	mockDb.ExpectQuery("SELECT * FROM `aliyun_avd` ORDER BY created_at DESC LIMIT 10").
		WillReturnRows(mockDb.NewRows([]string{"id", "avd", "cve", "name"}).AddRow(1, "avd-001", "cve-001", "aliyun_avd"))
	// 创建一个测试请求和响应
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/aliyun_avd/list?page=1&per_page=10", nil)
	// 创建一个Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 调用函数
	err := List(c)

	// 断言错误为 nil
	assert.NoError(t, err)

	// 解析响应数据
	response := w.Result()
	defer response.Body.Close()
	body, _ := ioutil.ReadAll(response.Body)

	// 断言响应不为空
	assert.NotEmpty(t, body)
}

func TestShow(t *testing.T) {
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()

	t.Run("params err", func(t *testing.T) {
		// 创建一个测试请求和响应
		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/aliyun_avd/list?_rule=exists&id=1", nil)
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		// 调用函数
		err := Show(c)

		// 断言错误为 nil
		assert.Error(t, err)
	})

	t.Run("show err", func(t *testing.T) {
		mockDb.ExpectQuery("SELECT count(*) FROM `aliyun_avd` WHERE id = ?").
			WithArgs("1").
			WillReturnRows(mockDb.NewRows([]string{"count"}).AddRow(1))

		// 创建一个测试请求和响应
		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/v1/aliyun_avd/1?_rule=exists&id=1", nil)
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		// 调用函数
		err := Show(c)

		// 断言错误为 nil
		assert.Error(t, err)
	})

	t.Run("success", func(t *testing.T) {
		mockDb.ExpectQuery("SELECT count(*) FROM `aliyun_avd` WHERE id = ?").
			WithArgs("1").
			WillReturnRows(mockDb.NewRows([]string{"count"}).AddRow(1))

		mockDb.ExpectQuery("SELECT * FROM `aliyun_avd` WHERE id = ? ORDER BY `aliyun_avd`.`id` LIMIT 1").
			WithArgs("1").
			WillReturnRows(mockDb.NewRows([]string{"id", "avd", "cve", "name"}).AddRow(1, "avd-001", "cve-001", "aliyun_avd"))

		// 创建一个测试请求和响应
		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/v1/aliyun_avd/1?_rule=exists&id=1", nil)
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		// 调用函数
		err := Show(c)

		// 断言错误为 nil
		assert.NoError(t, err)

		// 解析响应数据
		response := w.Result()
		defer response.Body.Close()
		body, _ := ioutil.ReadAll(response.Body)

		// 断言响应不为空
		assert.NotEmpty(t, body)
	})
}
