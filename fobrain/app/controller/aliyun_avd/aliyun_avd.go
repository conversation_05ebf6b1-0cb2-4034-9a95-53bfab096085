package aliyun_avd

import (
	"fobrain/fobrain/common/response"
	"fobrain/initialize/mysql"
	"fobrain/models/mysql/aliyun_avd"
	"fobrain/pkg/utils"

	"github.com/gin-gonic/gin"

	"fobrain/fobrain/common/request"
)

type (
	AvdRequest struct {
		request.PageRequest
		Keyword string `json:"keyword" form:"keyword" uri:"keyword" validate:"omitempty,max=100" zh:"模糊搜索"`
		Cve     string `json:"cve"  form:"cve"  uri:"cve"    validate:"omitempty,max=100" zh:"cve编号"`    // 限制为100个字符
		Avd     string `json:"avd"  form:"avd"  uri:"avd" validate:"omitempty,max=100" zh:"avd编号"`       // 限制为100个字符
		Name    string `json:"name" form:"name" uri:"strategies" validate:"omitempty,max=100" zh:"漏洞名称"` // 限制为100个字符
		Type    string `json:"type" form:"type" uri:"type" validate:"omitempty,max=100" zh:"漏洞类型"`       // 限制为100个字符
	}
	AvdShowRequest struct {
		Id string `json:"id" form:"id" uri:"id" validate:"required,db=aliyun_avd?_rule=exists&id" zh:"列表 ID"`
	}
)

var AvdValidate = request.Validate[AvdRequest]
var AvdShowValidate = request.Validate[AvdShowRequest]

// List avd列表
func List(c *gin.Context) error {
	params, err := AvdValidate(c, &AvdRequest{})
	if err != nil {
		return err
	}
	handlers := make([]mysql.HandleFunc, 0)
	handlers = append(handlers, mysql.WithOrder("created_at DESC"))
	handlers = utils.CanAppend(params.Type != "", handlers, mysql.WithWhere("type = ?", params.Type))
	handlers = utils.CanAppend(params.Avd != "", handlers, mysql.WithWhere("avd = ?", params.Avd))
	handlers = utils.CanAppend(params.Name != "", handlers, mysql.WithWhere("name =?", params.Name))
	handlers = utils.CanAppend(params.Cve != "", handlers, mysql.WithWhere("cve =?", params.Cve))
	handlers = utils.CanAppend(params.Keyword != "", handlers, mysql.WithLike("name", "%"+params.Keyword+"%"))
	list, total, err := aliyun_avd.NewAliYunAvdModel().List(params.Page, params.PerPage, handlers...)
	if err != nil {
		return err
	}
	return response.OkWithPageData(c, total, params.Page, params.PerPage, list)
}

func Show(c *gin.Context) error {
	params, err := AvdShowValidate(c, &AvdShowRequest{})
	if err != nil {
		return err
	}

	data, err := aliyun_avd.NewAliYunAvdModel().First(mysql.WithWhere("id = ?", params.Id))
	if err != nil {
		return err
	}

	return response.OkWithData(c, data)
}
