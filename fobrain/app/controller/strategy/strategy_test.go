package strategy

import (
	"fmt"
	"fobrain/fobrain/common/localtime"
	"fobrain/fobrain/common/wrapper"
	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/models/mysql/strategy"
	"fobrain/pkg/cfg"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"github.com/alicebob/miniredis/v2"
	"github.com/go-redis/redis/v8"
	"github.com/olivere/elastic/v7"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"
)

func TestGetDetail(t *testing.T) {
	cfg.InitLoadCfg()
	mockDb := testcommon.InitSqlMock()
	defer mockDb.Close()

	// 创建测试用例
	type strategyTest struct {
		Name         string
		BusinessType string
		ID           int
		ExpectedCode int
	}
	cases := []strategyTest{
		{Name: "正常", BusinessType: strategy.BusinessType_AssetMerge, ID: 1, ExpectedCode: http.StatusOK},
		{Name: "错误的type", BusinessType: "invalid_type", ID: 1, ExpectedCode: http.StatusBadRequest},
		{Name: "错误的ID", BusinessType: strategy.BusinessType_AssetMerge, ID: 9999999, ExpectedCode: http.StatusBadRequest},
	}

	r := gin.Default()
	gin.SetMode(gin.TestMode)
	r.GET("/strategy/detail/:id", wrapper.Fn(GetDetail))
	for _, v := range cases {
		w := httptest.NewRecorder()
		query := mockDb.ExpectQuery(strategy.NewStrategyModel().DB.ToSQL(func(tx *gorm.DB) *gorm.DB {
			return tx.Where("business_type =? and id =?", strategy.BusinessType_AssetMerge, 0)
		}))
		if v.Name == "正常" {
			t, _ := time.Parse("2006-01-02 15:04:05", "2024-07-22 15:43:05")
			query.WithArgs(v.BusinessType, v.ID).WillReturnRows(
				sqlmock.NewRows([]string{"id", "business_type", "field_name", "source_priority", "untrusted_source", "version", "created_at"}).AddRow(v.ID, v.BusinessType, "test_field", []byte("{\"1\": 2, \"2\": 2}"), []byte("[]"), "20230718120000", localtime.NewLocalTime(t)))
		}
		if v.Name == "错误的type" {
			query.WithArgs(v.BusinessType, v.ID).WillReturnError(gorm.ErrRecordNotFound)
		}
		if v.Name == "错误的ID" {
			query.WithArgs(v.BusinessType, v.ID).WillReturnError(gorm.ErrRecordNotFound)
		}
		req := httptest.NewRequest("GET", fmt.Sprintf("/strategy/detail/%d?business_type=%s", v.ID, v.BusinessType), nil)
		// 调用待测函数
		r.ServeHTTP(w, req)

		// 检查响应内容是否符合预期
		assert.Equal(t, v.ExpectedCode, w.Code)
		// fmt.Println(w.Body.String())
		// assert.JSONEq(t, w.Body.String(), v.ExpectedMsg)
	}
}

func TestGetDetailByName(t *testing.T) {
	cfg.InitLoadCfg()
	mockDb := testcommon.InitSqlMock()
	defer mockDb.Close()

	// 创建测试用例
	type strategyTest struct {
		Name         string
		BusinessType string
		FieldName    string
		ExpectedCode int
	}
	cases := []strategyTest{
		{Name: "正常", BusinessType: strategy.BusinessType_AssetMerge, FieldName: "hostname", ExpectedCode: http.StatusOK},
		{Name: "错误的type", BusinessType: "invalid_type", FieldName: "hostname", ExpectedCode: http.StatusBadRequest},
		{Name: "错误的field", BusinessType: strategy.BusinessType_AssetMerge, FieldName: "invalid_field", ExpectedCode: http.StatusBadRequest},
	}

	r := gin.Default()
	gin.SetMode(gin.TestMode)
	r.GET("/strategy/detailByName", wrapper.Fn(GetDetailByName))
	for _, v := range cases {
		w := httptest.NewRecorder()
		query := mockDb.ExpectQuery(strategy.NewStrategyModel().DB.ToSQL(func(tx *gorm.DB) *gorm.DB {
			return tx.Where("business_type =? and field_name =?", strategy.BusinessType_AssetMerge, "")
		}))
		if v.Name == "正常" {
			t, _ := time.Parse("2006-01-02 15:04:05", "2024-07-22 15:43:05")
			query.WithArgs(v.BusinessType, v.FieldName).WillReturnRows(
				sqlmock.NewRows([]string{"id", "business_type", "field_name", "source_priority", "untrusted_source", "version", "created_at"}).AddRow("1", v.BusinessType, "test_field", []byte("{\"1\": 2, \"2\": 2}"), []byte("[]"), "20230718120000", localtime.NewLocalTime(t)))
		}
		if v.Name == "错误的type" {
			query.WithArgs(v.BusinessType, v.FieldName).WillReturnError(gorm.ErrRecordNotFound)
		}
		if v.Name == "错误的ID" {
			query.WithArgs(v.BusinessType, v.FieldName).WillReturnError(gorm.ErrRecordNotFound)
		}
		req := httptest.NewRequest("GET", fmt.Sprintf("/strategy/detailByName?name=%s&business_type=%s", v.FieldName, v.BusinessType), nil)
		// 调用待测函数
		r.ServeHTTP(w, req)

		// 检查响应内容是否符合预期
		assert.Equal(t, v.ExpectedCode, w.Code)
		// fmt.Println(w.Body.String())
		// assert.JSONEq(t, w.Body.String(), v.ExpectedMsg)
	}
}

func TestGetDeviceConfig(t *testing.T) {
	mockDb := testcommon.InitSqlMock()
	defer mockDb.Close()
	mockDb.ExpectQuery("SELECT * FROM `system_configs` WHERE `key` IN (?,?)").
		WithArgs("dhcp_ips", "has_dhcp").
		WillReturnRows(sqlmock.NewRows([]string{""}))

	w := httptest.NewRecorder()

	req := httptest.NewRequest(http.MethodGet, "/api/v1/strategy/device", nil)
	req.Header.Set("Content-Type", "application/json")
	// 创建一个Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 调用函数
	err := GetDeviceConfig(c)

	// 断言错误为 nil
	assert.Empty(t, err)
}

func TestSetDeviceConfig(t *testing.T) {
	mockDb := testcommon.InitSqlMock()
	defer mockDb.Close()

	mockDb.ExpectQuery("SELECT * FROM `system_configs` WHERE `key` = ? ORDER BY `system_configs`.`id` LIMIT 1").
		WithArgs(sqlmock.AnyArg()).
		WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))

	// 定义期望的 SQL
	mockDb.ExpectBegin()
	mockDb.ExpectExec("UPDATE `system_configs` SET `value`=? WHERE `key` = ?").
		WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg()).
		WillReturnResult(sqlmock.NewResult(1, 1)) // 模拟更新成功，影响1行
	mockDb.ExpectCommit()

	mockDb.ExpectQuery("SELECT * FROM `system_configs` WHERE `key` = ? ORDER BY `system_configs`.`id` LIMIT 1").
		WithArgs(sqlmock.AnyArg()).
		WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))
	// 定义期望的 SQL
	mockDb.ExpectBegin()
	// 定义期望的 SQL
	mockDb.ExpectExec("UPDATE `system_configs` SET `value`=? WHERE `key` = ?").
		WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg()).
		WillReturnResult(sqlmock.NewResult(1, 1)) // 模拟更新成功，影响1行
	mockDb.ExpectCommit()

	// Setup miniredis
	s, err := miniredis.Run()
	if err != nil {
		t.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
	}
	defer s.Close()
	cli := redis.NewClient(&redis.Options{
		Addr: s.Addr(),
	})
	testcommon.SetRedisClient(cli)

	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()
	// 模拟 Elasticsearch 的搜索
	mockServer.Register("/device/_delete_by_query", &elastic.BulkIndexByScrollResponse{
		Deleted: 1,
	})

	w := httptest.NewRecorder()
	body := `
{
    "has_dhcp": true,
    "ip": [
        "***************",
        "*************"
    ]
}
`
	req := httptest.NewRequest(http.MethodPost, "/api/v1/strategy/device", strings.NewReader(body))
	req.Header.Set("Content-Type", "application/json")
	// 创建一个Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 调用函数
	err = SetDeviceConfig(c)

	// 断言错误为 nil
	assert.Nil(t, err)
}
