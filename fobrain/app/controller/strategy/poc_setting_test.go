package strategy

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/models/mysql/poc_settings"
	"fobrain/pkg/cfg"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func TestAllSettings(t *testing.T) {
	cfg.InitLoadCfg()
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()
	mockDb.ExpectQuery("SELECT * FROM `poc_settings`").
		WillReturnRows(sqlmock.NewRows([]string{""}))

	w := httptest.NewRecorder()

	req := httptest.NewRequest(http.MethodGet, "/api/v1/strategy/poc_settings", nil)
	req.Header.Set("Content-Type", "application/json")
	// 创建一个Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 调用函数
	err := AllSettings(c)

	// 断言错误为 nil
	assert.Empty(t, err)

}

func TestSettingsUpdate(t *testing.T) {

	t.Run("Get Err", func(t *testing.T) {
		w := httptest.NewRecorder()

		req := httptest.NewRequest(http.MethodPost, "/api/v1/strategy/poc_settings_update", nil)
		req.Header.Set("Content-Type", "application/json")
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		// 调用函数
		err := SettingsUpdate(c)

		// 断言错误为 nil
		assert.Equal(t, "严重漏洞数量为必填字段", err.Error())
	})

	t.Run("success", func(t *testing.T) {
		w := httptest.NewRecorder()

		jsonData, _ := json.Marshal(poc_settings.DefaultOriginValues)

		req := httptest.NewRequest(http.MethodPost, "/api/v1/strategy/poc_settings_update", strings.NewReader(string(jsonData)))
		req.Header.Set("Content-Type", "application/json")
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		// 调用函数
		err := SettingsUpdate(c)

		// 断言错误为 nil
		assert.Empty(t, err)
	})

}

func TestSetDefault(t *testing.T) {

	t.Run("success", func(t *testing.T) {

		w := httptest.NewRecorder()

		req := httptest.NewRequest(http.MethodPost, "/api/v1/strategy/poc_settings_update", nil)
		req.Header.Set("Content-Type", "application/json")
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		// 调用函数
		err := SetDefault(c)

		// 断言错误为 nil
		assert.Empty(t, err)
	})

}
