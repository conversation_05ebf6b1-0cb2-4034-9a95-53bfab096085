package strategy

import (
	"fmt"
	device "fobrain/fobrain/app/repository/asset_center/device_asset"
	"fobrain/fobrain/app/request/system_config"
	"fobrain/fobrain/app/services/system_configs/device_strategy"
	"fobrain/fobrain/common/request"
	"fobrain/fobrain/common/response"
	"fobrain/fobrain/common/validate"
	"fobrain/initialize/redis"
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/strategy"
	"fobrain/models/mysql/system_configs"
	redis_helper "fobrain/models/redis"
	"fobrain/pkg/utils"
	"slices"
	"strings"

	pb "fobrain/mergeService/proto"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/spf13/cast"
)

func List(ctx *gin.Context) error {
	business_type := ctx.Query("business_type")
	page := cast.ToInt(ctx.Query("page"))
	pageSize := cast.ToInt(ctx.Query("per_page"))
	if business_type == "" {
		return response.FailWithMessage(ctx, "参数错误,business_type不能为空")
	}
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}
	data, count, err := strategy.NewStrategyModel().ListDistinctByPage(business_type, page, pageSize)
	if err != nil {
		return err
	}
	if count > 0 {
		source := make([]string, 0)
		// 查询 source信息
		for _, v := range data {
			for key := range v.SourcePriority {
				source = append(source, key)
			}
			source = append(source, v.UntrustedSource...)
		}
		source = utils.ListDistinct(source)

		sources, err := data_source.NewSourceModel().GetByIds(source)
		if err != nil {
			return err
		}
		var sourceDTO []struct {
			SourceId   int    `json:"source_id"`
			SourceName string `json:"source_name"`
			ICON       string `json:"icon"`
		}
		for _, v := range sources {
			sourceDTO = append(sourceDTO, struct {
				SourceId   int    `json:"source_id"`
				SourceName string `json:"source_name"`
				ICON       string `json:"icon"`
			}{
				SourceId:   int(v.Id),
				SourceName: v.Name,
				ICON:       v.Icon,
			})
		}
		response.OkWithData(ctx, gin.H{"items": data, "total": count, "page": page, "per_page": pageSize, "source": sourceDTO})
	} else {
		response.OkWithData(ctx, gin.H{"items": data, "total": count, "page": page, "per_page": pageSize})
	}
	return nil
}

func GetDetail(ctx *gin.Context) error {
	business_type := ctx.Query("business_type")
	id := cast.ToInt(ctx.Param("id"))
	if business_type == "" {
		return response.FailWithMessage(ctx, "参数错误,business_type不能为空")
	}
	if id <= 0 {
		return response.FailWithMessage(ctx, "参数错误,id不能为空")
	}
	data, err := strategy.NewStrategyModel().GetDetail(business_type, id)
	if err != nil {
		return err
	}
	response.OkWithData(ctx, gin.H{"data": data})
	return nil
}

func GetDetailByName(ctx *gin.Context) error {
	business_type := ctx.Query("business_type")
	name := ctx.Query("name")
	if business_type == "" {
		return response.FailWithMessage(ctx, "参数错误,business_type不能为空")
	}
	if name == "" {
		return response.FailWithMessage(ctx, "参数错误,name不能为空")
	}
	data, err := strategy.NewStrategyModel().GetLatestByField(business_type, name)
	if err != nil {
		return err
	}
	response.OkWithData(ctx, gin.H{"data": data})
	return nil
}

func Create(ctx *gin.Context) error {
	input := strategy.Strategy{}
	if err := ctx.ShouldBindJSON(&input); err != nil {
		return err
	}
	ok, str := validate.Validator(input)
	if !ok {
		return response.FailWithMessage(ctx, str)
	}
	// 检查工号策略
	if err := checkWorkNumberStrategy(&input); err != nil {
		return response.FailWithMessage(ctx, err.Error())
	}
	// 创建新的策略
	data, err := strategy.NewStrategyModel().Create(input)
	if err != nil {
		return err
	}

	// 删除对应策略的redis缓存
	key := redis_helper.AssetMergeRuleKey()
	switch input.BusinessType {
	case strategy.BusinessType_AssetMerge:
		key = redis_helper.AssetMergeRuleKey()
	case strategy.BusinessType_DeviceMerge:
		key = redis_helper.DeviceMergeRuleKey()
	case strategy.BusinessType_VulnMerge:
		key = redis_helper.VulnMergeRuleKey()
	case strategy.BusinessType_PersonMerge:
		key = redis_helper.PersonMergeRuleKey()
	case strategy.BusinessType_DeviceMerge_Key:
		key = redis_helper.DeviceMergeKey()
	}
	redisClient := redis.GetRedisClient()
	redisClient.Del(ctx, key)

	response.OkWithData(ctx, data)
	return nil
}

// checkWorkNumberStrategy 检查工号策略, 工号策略的数据源优先级不能重复
func checkWorkNumberStrategy(input *strategy.Strategy) error {
	if input.BusinessType == strategy.BusinessType_PersonMerge && input.FieldName == "WorkNumber" {
		if len(input.SourcePriority) > 0 {
			// 判断value是否重复 input.SourcePriority
			values := make([]uint64, 0)
			for _, v := range input.SourcePriority {
				if slices.Contains(values, v) {
					return fmt.Errorf("工号策略不能设置相同的优先级")
				}
				values = append(values, v)
			}
		}
	}
	return nil
}

// TriggerMerge 触发立即融合
func TriggerMerge(ctx *gin.Context) error {
	input := &pb.ManualMergeRequest{}
	if err := ctx.ShouldBindJSON(input); err != nil {
		return err
	}
	u := uuid.New()
	batchNo := strings.ReplaceAll(u.String(), "-", "")
	input.BatchNo = batchNo
	ok, str := validate.Validator(input)
	if !ok {
		return response.FailWithMessage(ctx, str)
	}

	data, err := pb.GetProtoClient().ManualMerge(ctx, input, pb.SetRpcTimeoutOpt(60), pb.ClientWithAddress)
	if err != nil {
		if strings.Contains(err.Error(), "context deadline exceeded") {
			response.FailWithCodeMessage(ctx, 408, "数据量过大，请求已被执行但尚未得到结果，请稍后查看结果")
			return nil
		}
		return err
	}
	response.OkWithData(ctx, data)
	return nil
}

// SetDeviceConfig 设置设备提取配置
func SetDeviceConfig(ctx *gin.Context) error {
	params, err := request.Validate(ctx, &system_config.DeviceConfigReq{})
	if err != nil {
		return err
	}
	err = utils.CheckIPsAndCIDRs(params.Ip)
	if err != nil {
		return err
	}
	err = system_configs.NewSystemConfigs().SetMultiConfig(map[string]any{
		"dhcp_ips": strings.Join(params.Ip, ","),
		"has_dhcp": cast.ToString(params.HasDhcp),
	})
	if err != nil {
		return err
	}
	//删除缓存
	device_strategy.DeleteCache()
	//删除设备
	err = device.DeleteDeviceByIps(params.Ip)
	if err != nil {
		return err
	}
	return response.Ok(ctx)
}

// GetDeviceConfig 获取设备提取配置
func GetDeviceConfig(ctx *gin.Context) error {
	data, err := system_configs.NewSystemConfigs().GetMultiConfig([]string{"dhcp_ips", "has_dhcp"})
	if err != nil {
		return err
	}
	ips := make([]string, 0)
	if data["dhcp_ips"] != "" {
		ips = strings.Split(data["dhcp_ips"], ",")
	}
	deviceConfig := &system_config.DeviceConfigReq{
		Ip:      ips,
		HasDhcp: cast.ToBool(data["has_dhcp"]),
	}
	return response.OkWithData(ctx, deviceConfig)
}
