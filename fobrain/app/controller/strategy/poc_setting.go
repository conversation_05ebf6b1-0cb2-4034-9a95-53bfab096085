package strategy

import (
	"context"
	"errors"
	"fmt"
	poc2 "fobrain/fobrain/app/services/poc"
	"reflect"
	"strconv"
	"strings"
	"time"

	"fobrain/fobrain/common/request"
	"fobrain/fobrain/common/response"
	log "fobrain/fobrain/logs"
	pb "fobrain/mergeService/proto"
	"fobrain/models/mysql/poc_settings"

	"github.com/gin-gonic/gin"
)

var PocSettingValidate = request.Validate[poc_settings.PocSettingObj]

func AllSettings(ctx *gin.Context) error {
	ps := poc_settings.NewPocSettingModel()

	list, err := ps.GetTree()
	if err != nil {
		return err
	}

	return response.OkWithData(ctx, list)
}

func SettingsUpdate(ctx *gin.Context) error {
	param, err := PocSettingValidate(ctx, &poc_settings.PocSettingObj{})
	if err != nil {
		return err
	}
	//校验漏洞分值最大值
	t := reflect.TypeOf(*param)
	v := reflect.ValueOf(*param)
	err = poc2.CheckRiskLevel(param)
	if err != nil {
		return err
	}
	maxRisk := poc2.CalPocMaxRiskLevel(param)
	updateMaxRisk := 1
	vulRepairPriorityP0 := strings.Split(param.VulRepairPriorityP0, ",")
	if len(vulRepairPriorityP0) > 0 {
		updateMaxRisk, _ = strconv.Atoi(vulRepairPriorityP0[1])
	}
	if updateMaxRisk > maxRisk && updateMaxRisk != -1 {
		return fmt.Errorf("漏洞阈值最大值错误")
	}
	for i := 0; i < v.NumField(); i++ {
		field := v.Field(i)
		fieldName := t.Field(i).Tag.Get("json")
		fieldValue := field.Interface()
		fmt.Printf("Field Name: %s, Field Value: %v\n", fieldName, fieldValue)

		ps := poc_settings.NewPocSettingModel()
		err := ps.Update(fieldName, fieldValue)
		if err != nil {
			log.GetLogger().Errorw("SettingsUpdate", "err", err)
		}
	}

	ps := poc_settings.NewPocSettingModel()
	ps.Save2Redis()

	return response.Ok(ctx)
}

// ManualUpdate 手动更新数据
func ManualUpdate(ctx *gin.Context) error {
	// 重新计算风险等级
	taskId := fmt.Sprintf("%d", time.Now().Unix())
	result, err := pb.GetProtoClient().TriggerMergeForVuln(context.Background(), &pb.TriggerMergeForVulnRequest{
		TriggerMergeBaseRequest: &pb.TriggerMergeBaseRequest{
			SourceId:     0,
			NodeId:       0,
			TriggerEvent: "漏洞优先级更新",
			TaskId:       taskId,
			ChildTaskId:  taskId,
		},
	}, pb.ClientWithAddress)
	// err := strategy.NewStrategy().ReMerge() //业务系统状态触发重新融和
	if err != nil {
		//return err
		return err
	}
	if !result.Success {
		//return errors.New(result.Message)
		return errors.New(result.Message)
	}

	return response.Ok(ctx)
}

// SetDefault
func SetDefault(ctx *gin.Context) error {
	defer func() {
		ps := poc_settings.NewPocSettingModel()
		ps.Save2Redis()
	}()
	for k, v := range poc_settings.DefaultOriginValues {
		ps := poc_settings.NewPocSettingModel()
		err := ps.Update(k, v)
		if err != nil {
			log.GetLogger().Errorw("SetDefault", "err", err)
		} else {
			log.GetLogger().Infow("SetDefault", "key", k, "value", v)
		}
	}

	return response.Ok(ctx)
}
