package net_mapping

import (
	"fobrain/fobrain/app/request/net_mapping"
	service "fobrain/fobrain/app/services/net_mapping"
	"fobrain/fobrain/common/request"
	"fobrain/fobrain/common/response"
	model "fobrain/models/mysql/net_mapping"
	"github.com/gin-gonic/gin"
)

func Create(c *gin.Context) error {
	params, err := request.Validate(c, &net_mapping.CreateNetMappingRequest{})
	if err != nil {
		return response.FailWithMessage(c, "参数错误")
	}
	err = service.Create(params)
	if err != nil {
		return err
	}
	return response.OkWithMessage(c, "创建成功")
}

func Import(c *gin.Context) error {
	params, err := request.Validate(c, &net_mapping.ImportNetMappingRequest{})
	if err != nil {
		return response.FailWithMessage(c, "参数错误")
	}
	msg, err := service.Import(c, params)
	if err != nil {
		return response.FailWithMessage(c, msg)
	}
	return response.OkWithMessage(c, msg)
}

func ImportConfirm(c *gin.Context) error {
	params, err := request.Validate(c, &net_mapping.ImportNetMappingConfirmRequest{})
	if err != nil {
		return response.FailWithMessage(c, "参数错误")
	}
	res, err := service.ImportConfirm(c, params)
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	return response.OkWithData(c, res)
}

func Delete(c *gin.Context) error {
	params, err := request.Validate(c, &net_mapping.DeleteNetMappingRequest{})
	if err != nil {
		return response.FailWithMessage(c, "参数错误")
	}
	msg, err := service.Delete(c, params)
	if err != nil {
		return response.FailWithMessage(c, msg)
	}
	return response.OkWithMessage(c, msg)
}

func List(c *gin.Context) error {
	params, err := request.Validate(c, &net_mapping.ListNetMappingRequest{})
	if err != nil {
		return response.FailWithMessage(c, "参数错误")
	}
	data, total, err := service.List(params)
	if err != nil {
		return response.FailWithMessage(c, "查询失败")
	}
	return response.OkWithPageData(c, total, params.Page, params.PerPage, data)
}

func DownloadTemplate(c *gin.Context) error {
	params, err := request.Validate(c, &net_mapping.DownloadTemplateRequest{})
	if err != nil {
		return response.FailWithMessage(c, "参数错误")
	}
	filePath, err := service.CreateTemplateFile(params)
	if err != nil {
		return response.FailWithMessage(c, "创建映射模板失败")
	}
	return response.OkWithFile(c, filePath, true)
}

func ListAuditLog(c *gin.Context) error {
	params, err := request.Validate(c, &net_mapping.ListAuditLogRequest{})
	if err != nil {
		return response.FailWithMessage(c, "参数错误")
	}
	data, total, err := model.NewNetMappingAuditLogModel().List(c, params.Page, params.PerPage, "导入")
	if err != nil {
		return response.FailWithMessage(c, "查询失败")
	}
	return response.OkWithPageData(c, total, params.Page, params.PerPage, data)
}

func ListAuditData(c *gin.Context) error {
	params, err := request.Validate(c, &net_mapping.ListAuditDataRequest{})
	if err != nil {
		return response.FailWithMessage(c, "参数错误")
	}
	data, total, err := service.ListAuditData(c, params)
	if err != nil {
		return response.FailWithMessage(c, "查询失败")
	}
	return response.OkWithPageData(c, total, params.Page, params.PerPage, data)
}

func CheckAlarm(c *gin.Context) error {
	err := service.CheckAlarm()
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	return response.OkWithMessage(c, "更新告警数据成功")
}
