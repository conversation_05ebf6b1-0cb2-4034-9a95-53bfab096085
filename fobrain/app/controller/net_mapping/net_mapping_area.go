package net_mapping

import (
	"fobrain/fobrain/app/request/net_mapping_area"
	"fobrain/fobrain/app/services/net_mapping"
	"fobrain/fobrain/common/request"
	"fobrain/fobrain/common/response"
	"github.com/gin-gonic/gin"
)

func ListArea(c *gin.Context) error {
	res := net_mapping.ListArea()
	if res == nil {
		return response.FailWithMessage(c, "获取区域列表失败")
	}
	return response.OkWithData(c, res)
}

func UpdateAreaList(c *gin.Context) error {
	params, err := request.Validate(c, &net_mapping_area.UpdateAreaListRequest{})
	if err != nil {
		return response.FailWithMessage(c, "参数错误")
	}
	err = net_mapping.UpdateAreaList(params)
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	return response.OkWithMessage(c, "更新成功")
}
