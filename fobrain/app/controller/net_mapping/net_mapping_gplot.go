package net_mapping

import (
	"fobrain/fobrain/app/request/net_mapping"
	service "fobrain/fobrain/app/services/net_mapping"
	"fobrain/fobrain/common/request"
	"fobrain/fobrain/common/response"
	"github.com/gin-gonic/gin"
)

func GetMappingGplot(c *gin.Context) error {
	params, err := request.Validate(c, &net_mapping.GetMappingGplotRequest{})
	if err != nil {
		return response.FailWithMessage(c, "参数错误")
	}
	res, err := service.GetMappingGplot(params)
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	return response.OkWithData(c, res)
}

func GetMappingAssets(c *gin.Context) error {
	params, err := request.Validate(c, &net_mapping.GetMappingAssetsRequest{})
	if err != nil {
		return response.FailWithMessage(c, "参数错误")
	}
	res, total, err := service.GetMappingAssets(params)
	if err != nil {
		return response.OkWithMessage(c, err.Error())
	}
	return response.OkWithPageData(c, total, params.Page, params.PerPage, res)
}

func GetMappingBaseInfo(c *gin.Context) error {
	params, err := request.Validate(c, &net_mapping.GetMappingBaseInfoRequest{})
	if err != nil {
		return response.FailWithMessage(c, "参数错误")
	}
	res, err := service.GetMappingBaseInfo(params)
	if err != nil {
		return response.OkWithMessage(c, "当前关联资产不存在于资产中心")
	}
	return response.OkWithData(c, res)
}

// GetMappingByBusiness 根据业务系统获取拓扑图
func GetMappingByBusiness(c *gin.Context) error {
	params, err := request.Validate(c, &net_mapping.GetMappingByBusinessRequest{})
	if err != nil {
		return response.FailWithMessage(c, "参数错误")
	}
	res, err := service.GetMappingByBusiness(params)
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	return response.OkWithData(c, res)
}
