package workbench_ctl

import (
	"fobrain/fobrain/app/services/workbench"
	"fobrain/fobrain/common/response"
	"net/http"

	"github.com/gin-gonic/gin"
)

func AssetPanorama(ctx *gin.Context) error {
	result, err := workbench.NewService().AssetPanorama(ctx)
	if err != nil {
		return response.FailWithCodeMessage(ctx, http.StatusInternalServerError, err.Error())
	}

	return response.OkWithData(ctx, result)
}
