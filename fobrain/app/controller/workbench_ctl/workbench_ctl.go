package workbench_ctl

import (
	"fobrain/models/mysql/system_configs"
	"net/http"
	"sort"
	"strconv"

	"fobrain/fobrain/app/response/net_mapping"
	"fobrain/fobrain/app/services/workbench"
	"fobrain/fobrain/common/auth"
	"fobrain/fobrain/common/request"
	"fobrain/fobrain/common/response"
	workbenchModel "fobrain/models/mysql/workbench"
	"github.com/gin-gonic/gin"
	"go-micro.dev/v4/logger"
)

func WorkbenchHeadStats(ctx *gin.Context) error {
	refreshStr := ctx.Query("refresh")
	var refresh bool
	var err error
	if refreshStr != "" {
		refresh, err = strconv.ParseBool(refreshStr)
		if err != nil {
			refresh = false
		}
	}
	info, err := workbench.NewWorkbenchService().HeadStats(refresh)
	if err != nil {
		return response.FailWithCodeMessage(ctx, http.StatusInternalServerError, err.Error())
	}
	return response.OkWithData(ctx, info)
}

func NotifyAlarmList(ctx *gin.Context) error {
	page, err := strconv.Atoi(ctx.Query("page"))
	if err != nil {
		page = 1
	}
	size, err := strconv.Atoi(ctx.Query("size"))
	if err != nil {
		size = 5
	}
	msgType, err := strconv.Atoi(ctx.Query("msg_type"))
	if err != nil {
		msgType = -1
	}
	info, _, err := workbench.NewWorkbenchService().AlarmList(page, size, msgType, request.GetUserId(ctx))
	if err != nil {
		return response.FailWithCodeMessage(ctx, http.StatusInternalServerError, err.Error())
	}
	return response.OkWithData(ctx, info)
}

func NotifyAlarmSingle(ctx *gin.Context) error {
	id, err := strconv.Atoi(ctx.Query("id"))
	if err != nil {
		return response.FailWithMessage(ctx, "id传值错误")
	}
	info, err := workbench.NewWorkbenchService().AlarmSingle(id)
	if err != nil {
		return response.FailWithCodeMessage(ctx, http.StatusInternalServerError, err.Error())
	}
	return response.OkWithData(ctx, info)
}

func NotifyAlarmAdd(ctx *gin.Context) error {
	var body workbenchModel.NotifyAlarmCenter
	if err := ctx.ShouldBindJSON(&body); err != nil {
		return response.FailWithMessage(ctx, "参数错误"+err.Error())
	}
	err := workbench.NewWorkbenchService().AlarmCreate(&body)
	if err != nil {
		return response.FailWithCodeMessage(ctx, http.StatusInternalServerError, err.Error())
	}
	return response.Ok(ctx)
}

func NotifyAlarmRemindList(ctx *gin.Context) error {
	isSuper := auth.IsSuperManage(ctx)
	if isSuper {
		return response.OkWithData(ctx, []*workbenchModel.NotifyAlarmCenter{})
	}

	staffIds := auth.CurrentStaffIds(ctx)
	info, _, err := workbench.NewWorkbenchService().AlarmRemindList(staffIds)
	if err != nil {
		return response.FailWithCodeMessage(ctx, http.StatusInternalServerError, err.Error())
	}
	if info == nil {
		info = make([]*workbenchModel.NotifyAlarmCenter, 0)
	}
	return response.OkWithData(ctx, info)
}

func ReadNotifyAlarmRemind(ctx *gin.Context) error {
	params := &struct {
		Ids []int `json:"ids"`
	}{}
	if err := ctx.ShouldBindJSON(params); err != nil {
		return response.FailWithMessage(ctx, "参数错误"+err.Error())
	}

	err := workbench.NewWorkbenchService().ReadAlarmRemind(request.GetUserId(ctx), params.Ids)
	if err != nil {
		return response.FailWithCodeMessage(ctx, http.StatusInternalServerError, err.Error())
	}
	return response.Ok(ctx)
}

func ProbeOverview(ctx *gin.Context) error {
	info, err := workbench.NewWorkbenchService().ProbeOverview()
	if err != nil {
		return response.FailWithCodeMessage(ctx, http.StatusInternalServerError, err.Error())
	}
	return response.OkWithData(ctx, info)
}

func AssetSecurityCoverage(ctx *gin.Context) error {
	refreshStr := ctx.Query("refresh")
	var refresh bool
	var err error
	if refreshStr != "" {
		refresh, err = strconv.ParseBool(refreshStr)
		if err != nil {
			refresh = false
		}
	}
	info, err := workbench.NewWorkbenchService().AssetSecurityCoverage(refresh)
	if err != nil {
		return response.FailWithCodeMessage(ctx, http.StatusInternalServerError, err.Error())
	}
	return response.OkWithData(ctx, info)
}

func ProbeAssetContributionTop(ctx *gin.Context) error {
	info, err := workbench.NewWorkbenchService().ProbeAssetContributionTop()
	if err != nil {
		return response.FailWithCodeMessage(ctx, http.StatusInternalServerError, err.Error())
	}
	return response.OkWithData(ctx, info)
}

func WeeklyTaskOverview(ctx *gin.Context) error {
	info, err := workbench.NewWorkbenchService().WeeklyTaskOverview()
	if err != nil {
		return response.FailWithCodeMessage(ctx, http.StatusInternalServerError, err.Error())
	}
	return response.OkWithData(ctx, info)
}

func AssetsResult(ctx *gin.Context) error {
	info, err := workbench.NewWorkbenchService().AssetsResult()
	if err != nil {
		return response.FailWithCodeMessage(ctx, http.StatusInternalServerError, err.Error())
	}
	return response.OkWithData(ctx, info)
}

func DataProcessOverview(ctx *gin.Context) error {
	refreshStr := ctx.Query("refresh")
	var refresh bool
	var err error
	if refreshStr != "" {
		refresh, err = strconv.ParseBool(refreshStr)
		if err != nil {
			refresh = false
		}
	}
	info, err := workbench.NewWorkbenchService().DataProcessOverview(refresh)
	if err != nil {
		return response.FailWithCodeMessage(ctx, http.StatusInternalServerError, err.Error())
	}
	sort.Slice(info, func(i, j int) bool {
		return info[i].Id > info[j].Id
	})
	return response.OkWithData(ctx, info)
}

func VulnerabilityOverview(ctx *gin.Context) error {
	info, err := workbench.NewWorkbenchService().VulnerabilityOverview()
	if err != nil {
		return response.FailWithCodeMessage(ctx, http.StatusInternalServerError, err.Error())
	}
	return response.OkWithData(ctx, info)
}

func AssetExemptList(ctx *gin.Context) error {
	page, _ := strconv.Atoi(ctx.Query("page"))
	perPage, _ := strconv.Atoi(ctx.Query("per_page"))
	dimensions, _ := strconv.Atoi(ctx.Query("dimensions_id"))
	list, total, err := workbench.NewWorkbenchService().AssetExemptList(&workbench.AssetExemptListRequest{
		Page:       page,
		Size:       perPage,
		Dimensions: int64(dimensions),
	})
	if err != nil {
		return response.FailWithCodeMessage(ctx, http.StatusInternalServerError, err.Error())
	}
	return response.OkWithPageData(ctx, total, page, perPage, list)
}

func AssetExemptSave(ctx *gin.Context) error {
	var param workbench.AssetExemptRateSaveRequest
	if err := ctx.ShouldBindJSON(&param); err != nil {
		return response.FailWithCodeMessage(ctx, http.StatusBadRequest, err.Error())
	}
	if param.DimensionsId < 1 {
		return response.FailWithCodeMessage(ctx, http.StatusBadRequest, "未传条件")
	}
	err := workbench.NewWorkbenchService().AssetExemptRateSave(&param)
	if err != nil {
		if err == workbench.AssetExemptForbbidenErr {
			return response.FailWithCodeMessage(ctx, http.StatusBadRequest, err.Error())
		}
		return response.FailWithCodeMessage(ctx, http.StatusInternalServerError, err.Error())
	}
	return response.OkWithMessage(ctx, "success")
}

func AssetExemptDelete(ctx *gin.Context) error {
	params, err := request.Validate(ctx, &struct {
		Ids []int64 `json:"ids" uri:"ids" form:"ids" validate:"required,dive,number" zh:"主键id"`
	}{})

	if err != nil {
		logger.Errorf("[ERROR] AssetExemptDelete error: %v\n", err)
		return response.FailWithMessage(ctx, err.Error())
	}
	err = workbench.NewWorkbenchService().AssetExemptDelete(params.Ids)
	if err != nil {
		logger.Errorf("[ERROR] AssetExemptDelete exec error: %v\n", err)
		return response.FailWithCodeMessage(ctx, http.StatusInternalServerError, "删除失败")
	}

	return response.OkWithMessage(ctx, "success")
}

func WAFCoverageConfSave(ctx *gin.Context) error {
	var param net_mapping.ListAreaResponse
	if err := ctx.ShouldBindJSON(&param); err != nil {
		return response.FailWithCodeMessage(ctx, http.StatusBadRequest, err.Error())
	}
	if err := system_configs.NewSystemConfigs().SaveWAFCoverageConf(&param); err != nil {
		return response.FailWithCodeMessage(ctx, http.StatusInternalServerError, err.Error())
	}
	return response.OkWithMessage(ctx, "success")
}

func WAFCoverageConfInfo(ctx *gin.Context) error {
	conf, err := system_configs.NewSystemConfigs().GetWAFCoverageConf()
	if err != nil {
		return response.FailWithCodeMessage(ctx, http.StatusInternalServerError, err.Error())
	}
	return response.OkWithData(ctx, conf)
}
