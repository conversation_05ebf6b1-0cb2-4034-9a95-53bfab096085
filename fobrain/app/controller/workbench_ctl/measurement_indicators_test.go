package workbench_ctl

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"

	testcommon "fobrain/fobrain/tests/common_test"
)

func init() {
	gin.SetMode(gin.TestMode)
}

func TestDataInterpretation(t *testing.T) {
	tests := []struct {
		name     string
		key      string
		wantCode int
	}{
		{
			name:     "空key参数",
			key:      "",
			wantCode: 500,
		},
		{
			name:     "无效的key",
			key:      "invalid_key",
			wantCode: 500,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockDb := testcommon.InitSqlMock()
			defer mockDb.Close()

			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			req := httptest.NewRequest(http.MethodGet, "/api/v1/workbench/data_interpretation?key="+tt.key, nil)
			c.Request = req

			DataInterpretation(c)

			assert.Equal(t, tt.wantCode, w.Code)
		})
	}
}

func TestDataInterpretationUpdate(t *testing.T) {
	tests := []struct {
		name     string
		reqBody  string
		wantCode int
	}{
		{
			name:     "请求参数错误",
			reqBody:  `{"invalid": "json"`,
			wantCode: 400,
		},
		{
			name: "key参数缺失",
			reqBody: `{
				"normal_in_percentage": 80,
				"data_deficiencies_in_percentage": 10
			}`,
			wantCode: 400,
		},
		{
			name: "key参数超长",
			reqBody: `{
				"key": "` + string(make([]byte, 101)) + `",
				"normal_in_percentage": 80,
				"data_deficiencies_in_percentage": 10
			}`,
			wantCode: 400,
		},
		{
			name: "有效的参数格式",
			reqBody: `{
				"key": "source_sort_top5",
				"normal_in_percentage": 80,
				"data_deficiencies_in_percentage": 10
			}`,
			wantCode: 500, // 会因为数据库操作失败而返回500
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockDb := testcommon.InitSqlMock()
			defer mockDb.Close()

			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			req := httptest.NewRequest(http.MethodPost, "/api/v1/workbench/data_interpretation", bytes.NewBufferString(tt.reqBody))
			req.Header.Set("Content-Type", "application/json")
			c.Request = req

			DataInterpretationUpdate(c)

			assert.Equal(t, tt.wantCode, w.Code)

			// 检查参数验证错误的响应
			if tt.wantCode == 400 {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, float64(400), response["code"])
			}
		})
	}
}
