package data_board_config

import (
	"errors"
	"strings"

	"github.com/gin-gonic/gin"

	"fobrain/fobrain/app/services/workbench/data_board"
	"fobrain/fobrain/common/request"
	"fobrain/fobrain/common/response"
	"fobrain/models/mysql/system_configs"
)

type (
	data_board_config struct {
		Checked []string `json:"checked" form:"checked" uri:"checked" validate:"required" zh:"选中的看板配置"`
		Type    int      `json:"type" form:"type" uri:"type" validate:"required,oneof=1 2 3" zh:"类型： 1-资产 2-漏洞 3-运营"`
	}
	data_board_config_list struct {
		Type int `json:"type" form:"type" uri:"type" validate:"required,oneof=1 2 3" zh:"类型： 1-资产 2-漏洞 3-运营"`
	}
)

// DataBoardConfig 更新看板配置
func DataBoardConfig(c *gin.Context) error {
	params, err := request.Validate(c, &data_board_config{})
	if err != nil {
		return err
	}
	if len(params.Checked) == 0 {
		return errors.New("选中的看板配置不能为空")
	}
	var key string
	switch params.Type {
	case 1:
		key = "data_board_assets_config"
	case 2:
		key = "data_board_vul_config"
	case 3:
		key = "data_board_operation_config"
	}
	value := strings.Join(params.Checked, ",")
	// 更新数据库中的数据
	err = system_configs.NewSystemConfigs().UpdateConfig(key, value)
	if err != nil {
		return err
	}
	return response.OkWithMessage(c, "操作成功")
}

// DataBoardConfigList 获取看板配置
func DataBoardConfigList(c *gin.Context) error {
	params, err := request.Validate(c, &data_board_config_list{})
	if err != nil {
		return err
	}
	var key string
	switch params.Type {
	case 1:
		key = "data_board_assets_config"
	case 2:
		key = "data_board_vul_config"
	case 3:
		key = "data_board_operation_config"
	}
	value, err := system_configs.NewSystemConfigs().GetConfig(key)
	if err != nil {
		return err
	}
	checked := []string{}
	if value != "" {
		checked = strings.Split(value, ",")
	}
	boardData, err := data_board.NewService().GetConfigList(key)
	if len(checked) > 0 { // 默认全选
		for _, v := range boardData {
			checked = append(checked, v.Key)
			for _, vv := range v.Children {
				checked = append(checked, vv.Key)
			}
		}
	}
	if err != nil {
		return err
	}
	return response.OkWithData(c, map[string]interface{}{
		"checked":    checked,
		"board_data": boardData,
	})
}

func GetBoardData(c *gin.Context) error {
	params, err := request.Validate(c, &data_board.RequestParams{})
	if err != nil {
		return err
	}
	data, err := data_board.NewService().GetDataByRequest(params)
	if err != nil {
		return err
	}
	return response.OkWithData(c, data)
}
