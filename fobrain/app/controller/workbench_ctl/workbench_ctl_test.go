package workbench_ctl

import (
	"encoding/json"
	"errors"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/olivere/elastic/v7"

	testcommon "fobrain/fobrain/tests/common_test"

	workbench2 "fobrain/models/mysql/workbench"

	. "github.com/agiledragon/gomonkey/v2"
	"github.com/gin-gonic/gin"
	. "github.com/smartystreets/goconvey/convey"
	"github.com/stretchr/testify/assert"

	"github.com/agiledragon/gomonkey/v2"

	"fobrain/fobrain/app/services/workbench"
)

func TestWorkbenchHeadStats(t *testing.T) {
	//w := httptest.NewRecorder()
	//req := httptest.NewRequest("GET", "/api/v1/workbench/head/stats?refresh=true", nil)
	//c, _ := gin.CreateTestContext(w)
	//c.Request = req
	//
	//mockServer := testcommon.NewMockServer()
	//defer mockServer.Close()
	//mockDb := testcommon.InitSqlMock()
	//defer mockDb.Close()
	//
	//mockDb.ExpectQuery("SELECT count(*) FROM `data_nodes` WHERE status = 3 AND deleted_at IS NULL").WillReturnRows(sqlmock.NewRows([]string{"0"}))
	//mockDb.ExpectQuery("SELECT count(*) FROM `data_nodes` WHERE status != 3 AND deleted_at IS NULL").WillReturnRows(sqlmock.NewRows([]string{"0"}))
	//
	//// mock 资产返回数据
	//mockServer.Register("/asset/_search", []*elastic.SearchHit{
	//	{
	//		Ids:     "1",
	//		Source: []byte(`{"id":"1","name":"example"}`),
	//	},
	//})
	//
	//// mock 设备返回数据量
	//mockServer.Register("/device/_search", 1)
	//
	//// mock 漏洞返回数据量
	//mockServer.Register("/poc/_search", 2)
	//
	//// mock 合规风险返回数据量
	//mockServer.Register("/compliance_monitor_task_records", 3)
	//
	//err := WorkbenchHeadStats(c)
	//assert.Nil(t, err, nil)
}

func TestNotifyAlarmList(t *testing.T) {
	Convey("TestNotifyAlarmList", t, func() {
		Convey("Success", func() {
			defer ApplyMethodReturn(workbench.NewWorkbenchService(), "AlarmList", nil, int64(0), nil).Reset()
			w := httptest.NewRecorder()
			req := httptest.NewRequest("GET", "/api/v1/workbench/alarm/list", nil)
			c, _ := gin.CreateTestContext(w)
			c.Request = req

			err := NotifyAlarmList(c)
			assert.Nil(t, err)
		})

		Convey("Error", func() {
			w := httptest.NewRecorder()
			req := httptest.NewRequest("GET", "/api/v1/workbench/alarm/list", nil)
			c, _ := gin.CreateTestContext(w)
			c.Request = req

			err := NotifyAlarmList(c)
			assert.Nil(t, err)
		})
	})
}

func TestNotifyAlarmSingle(t *testing.T) {
	Convey("TestNotifyAlarmSingle", t, func() {
		Convey("Success", func() {
			defer ApplyMethodReturn(workbench.NewWorkbenchService(), "AlarmSingle", nil, nil).Reset()
			w := httptest.NewRecorder()
			req := httptest.NewRequest("GET", "/api/v1/workbench/alarm/single?id=1", nil)
			c, _ := gin.CreateTestContext(w)
			c.Request = req

			_ = NotifyAlarmSingle(c)
		})

		Convey("Params Error", func() {
			w := httptest.NewRecorder()
			req := httptest.NewRequest("GET", "/api/v1/workbench/alarm/single", nil)
			c, _ := gin.CreateTestContext(w)
			c.Request = req

			_ = NotifyAlarmSingle(c)
		})

		Convey("Error", func() {
			w := httptest.NewRecorder()
			req := httptest.NewRequest("GET", "/api/v1/workbench/alarm/single?id=1", nil)
			c, _ := gin.CreateTestContext(w)
			c.Request = req

			_ = NotifyAlarmSingle(c)
		})
	})
}

func TestNotifyAlarmAdd(t *testing.T) {
	Convey("TestNotifyAlarmAdd", t, func() {
		Convey("Success", func() {
			defer ApplyMethodReturn(workbench.NewWorkbenchService(), "AlarmCreate", nil).Reset()
			w := httptest.NewRecorder()
			req := httptest.NewRequest("POST", "/api/v1/workbench/alarm/add", strings.NewReader(`{}`))
			c, _ := gin.CreateTestContext(w)
			req.Header.Set("Content-Type", "application/json")
			c.Request = req

			_ = NotifyAlarmAdd(c)
		})

		Convey("Params Error", func() {
			w := httptest.NewRecorder()
			req := httptest.NewRequest("POST", "/api/v1/workbench/alarm/add", nil)
			c, _ := gin.CreateTestContext(w)
			c.Request = req

			_ = NotifyAlarmAdd(c)
		})

		Convey("Error", func() {
			w := httptest.NewRecorder()
			req := httptest.NewRequest("POST", "/api/v1/workbench/alarm/add", strings.NewReader(`{
				
			}`))
			c, _ := gin.CreateTestContext(w)
			req.Header.Set("Content-Type", "application/json")
			c.Request = req

			_ = NotifyAlarmAdd(c)
		})
	})
}

func TestProbeOverview(t *testing.T) {
	Convey("TestProbeOverview", t, func() {
		Convey("Success", func() {
			defer ApplyMethodReturn(workbench.NewWorkbenchService(), "ProbeOverview", nil, nil).Reset()
			w := httptest.NewRecorder()
			req := httptest.NewRequest("GET", "/api/v1/workbench/probe/overview", nil)
			c, _ := gin.CreateTestContext(w)
			c.Request = req

			_ = ProbeOverview(c)
		})

		Convey("Error", func() {
			w := httptest.NewRecorder()
			req := httptest.NewRequest("GET", "/api/v1/workbench/probe/overview", nil)
			c, _ := gin.CreateTestContext(w)
			c.Request = req

			_ = ProbeOverview(c)
		})
	})
}

func TestAssetSecurityCoverage(t *testing.T) {
	Convey("TestAssetSecurityCoverage", t, func() {
		Convey("Success", func() {
			defer ApplyMethodReturn(workbench.NewWorkbenchService(), "AssetSecurityCoverage", nil, nil).Reset()
			w := httptest.NewRecorder()
			req := httptest.NewRequest("GET", "/api/v1/workbench/asset/security/coverage", nil)
			c, _ := gin.CreateTestContext(w)
			c.Request = req

			_ = AssetSecurityCoverage(c)
		})

		Convey("Error", func() {
			w := httptest.NewRecorder()
			req := httptest.NewRequest("GET", "/api/v1/workbench/asset/security/coverage", nil)
			c, _ := gin.CreateTestContext(w)
			c.Request = req

			_ = AssetSecurityCoverage(c)
		})
	})
}

func TestProbeAssetContributionTop(t *testing.T) {
	Convey("ProbeAssetContributionTop", t, func() {
		Convey("Success", func() {
			defer ApplyMethodReturn(workbench.NewWorkbenchService(), "ProbeAssetContributionTop", nil, nil).Reset()
			w := httptest.NewRecorder()
			req := httptest.NewRequest("GET", "/api/v1/workbench/probe/asset/contribute", nil)
			c, _ := gin.CreateTestContext(w)
			c.Request = req

			_ = ProbeAssetContributionTop(c)
		})

		Convey("Error", func() {
			defer ApplyMethodReturn(workbench.NewWorkbenchService(), "ProbeAssetContributionTop", nil, errors.New("")).Reset()
			w := httptest.NewRecorder()
			req := httptest.NewRequest("GET", "/api/v1/workbench/probe/asset/contribute", nil)
			c, _ := gin.CreateTestContext(w)
			c.Request = req

			_ = ProbeAssetContributionTop(c)
		})
	})
}

func TestWeeklyTaskOverview(t *testing.T) {
	Convey("TestWeeklyTaskOverview", t, func() {
		Convey("Success", func() {
			defer ApplyMethodReturn(workbench.NewWorkbenchService(), "WeeklyTaskOverview", nil, nil).Reset()
			w := httptest.NewRecorder()
			req := httptest.NewRequest("GET", "/api/v1/workbench/weekly/task/overview", nil)
			c, _ := gin.CreateTestContext(w)
			c.Request = req

			_ = WeeklyTaskOverview(c)
		})

		Convey("Error", func() {
			defer ApplyMethodReturn(workbench.NewWorkbenchService(), "WeeklyTaskOverview", nil, errors.New("")).Reset()
			w := httptest.NewRecorder()
			req := httptest.NewRequest("GET", "/api/v1/workbench/weekly/task/overview", nil)
			c, _ := gin.CreateTestContext(w)
			c.Request = req

			_ = WeeklyTaskOverview(c)
		})
	})
}

func TestAssetsResult(t *testing.T) {
	Convey("TestAssetsResult", t, func() {
		Convey("Success", func() {
			defer ApplyMethodReturn(workbench.NewWorkbenchService(), "AssetsResult", nil, nil).Reset()
			w := httptest.NewRecorder()
			req := httptest.NewRequest("GET", "/api/v1/workbench/asset/result", nil)
			c, _ := gin.CreateTestContext(w)
			c.Request = req

			_ = AssetsResult(c)
		})

		Convey("Error", func() {
			defer ApplyMethodReturn(workbench.NewWorkbenchService(), "AssetsResult", nil, errors.New("")).Reset()
			w := httptest.NewRecorder()
			req := httptest.NewRequest("GET", "/api/v1/workbench/asset/result", nil)
			c, _ := gin.CreateTestContext(w)
			c.Request = req

			_ = AssetsResult(c)
		})
	})
}

func TestDataProcessOverview(t *testing.T) {
	Convey("TestNotifyAlarmSingle", t, func() {
		Convey("Success", func() {
			defer ApplyMethodReturn(workbench.NewWorkbenchService(), "DataProcessOverview", nil, nil).Reset()
			w := httptest.NewRecorder()
			req := httptest.NewRequest("GET", "/api/v1/workbench/data/process/overview?refresh=true", nil)
			c, _ := gin.CreateTestContext(w)
			c.Request = req

			_ = DataProcessOverview(c)
		})

		Convey("Params Error", func() {
			defer ApplyMethodReturn(workbench.NewWorkbenchService(), "DataProcessOverview", nil, nil).Reset()
			w := httptest.NewRecorder()
			req := httptest.NewRequest("GET", "/api/v1/workbench/data/process/overview?refresh=x", nil)
			c, _ := gin.CreateTestContext(w)
			c.Request = req

			_ = DataProcessOverview(c)
		})

		Convey("Error", func() {
			defer ApplyMethodReturn(workbench.NewWorkbenchService(), "DataProcessOverview", nil, errors.New("")).Reset()
			w := httptest.NewRecorder()
			req := httptest.NewRequest("GET", "/api/v1/workbench/data/process/overview?refresh=true", nil)
			c, _ := gin.CreateTestContext(w)
			c.Request = req

			_ = DataProcessOverview(c)
		})
	})
}

func TestVulnerabilityOverview(t *testing.T) {
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/vulnerability/overview", nil)
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	mockServer.Register("/poc/_search", &elastic.SearchResult{
		Aggregations: elastic.Aggregations{
			"network_type_count": json.RawMessage(`
				{
					"buckets": [
						{"key": 1, "doc_count": 100},
						{"key": 2, "doc_count": 200}
					]
				}
			`),
			"status_count": json.RawMessage(`
				{
					"buckets": [
						{"key": 1, "doc_count": 300},
						{"key": 30, "doc_count": 50}
					]
				}
			`),
			"level_count": json.RawMessage(`
				{
					"buckets": [
						{"key": 1, "doc_count": 400},
						{"key": 2, "doc_count": 150}
					]
				}
			`),
			"repair_priority_count": json.RawMessage(`
				{
					"buckets": [
						{"key": "p2", "doc_count": 50},
						{"key": "p1", "doc_count": 20}
					]
				}
			`),
		},
	})

	err := VulnerabilityOverview(c)
	assert.Nil(t, err)
}

func TestAssetExemptList(t *testing.T) {
	mockDb := testcommon.InitSqlMock()
	defer mockDb.Close()

	mockDb.ExpectQuery("SELECT count(*) FROM `workbench_asset_exempt_record` WHERE dimensions = ?").WithArgs(1)

	gomonkey.ApplyMethodReturn(workbench2.NewWorkbenchAssetExemptRecordModel(), "List", []*workbench2.AssetExemptRecord{
		{NetworkType: 1, Remark: "1"},
		{NetworkType: 2, Remark: "2"},
		{NetworkType: 1, Remark: "3"},
	}, int64(3), nil)

	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/asset/exempt?page=1&per_page=10&dimensions_id=1", nil)
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	err := AssetExemptList(c)
	assert.Nil(t, err)
}

func TestAssetExemptSave(t *testing.T) {
	w := httptest.NewRecorder()
	req := httptest.NewRequest("POST", "/api/v1/asset/exempt", strings.NewReader(`{"dimensions_id": 2, "is_show": true, "op_id": 1001}`))
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	patch := gomonkey.ApplyMethodReturn(workbench2.NewAssetSecurityCoverageModel(), "Save", nil)
	defer patch.Reset()

	err := AssetExemptSave(c)
	assert.Nil(t, err)
}

func TestAssetExemptSaveParams(t *testing.T) {
	w := httptest.NewRecorder()
	req := httptest.NewRequest("POST", "/api/v1/asset/exempt", strings.NewReader(`{"dimensions_id": 2, "is_show": true, "op_id": 1001}`))
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	err := AssetExemptSave(c)
	assert.Nil(t, err)
}

func TestAssetExemptDelete(t *testing.T) {
	w := httptest.NewRecorder()
	req := httptest.NewRequest("DELETE", "/api/v1/asset/exempt", strings.NewReader(`{"ids": [1]}`))
	c, _ := gin.CreateTestContext(w)
	req.Header.Set("Content-Type", "application/json")
	c.Request = req

	gomonkey.ApplyMethodReturn(workbench.NewWorkbenchService(), "AssetExemptDelete", nil).Reset()

	err := AssetExemptDelete(c)
	assert.Nil(t, err)
}
