package workbench_ctl

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"fobrain/fobrain/app/services/workbench"
	"fobrain/fobrain/common/request"
	"fobrain/fobrain/common/response"
	"fobrain/models/mysql/system_configs"
)

// 获取衡量指标配置
func DataInterpretation(ctx *gin.Context) error {
	key := ctx.Query("key")
	info, err := system_configs.NewSystemConfigs().GetWorkbenchDataInterpretation(key)
	if err != nil {
		return response.FailWithCodeMessage(ctx, http.StatusInternalServerError, err.Error())
	}
	return response.OkWithData(ctx, info)
}

// 更新衡量指标配置
func DataInterpretationUpdate(ctx *gin.Context) error {
	params, err := request.Validate(ctx, &system_configs.DataInterpretationUpdateParams{})
	if err != nil {
		return response.FailWithCodeMessage(ctx, http.StatusBadRequest, err.Error())
	}
	err = system_configs.NewSystemConfigs().UpdateWorkbenchDataInterpretation(params)
	if err != nil {
		return response.FailWithCodeMessage(ctx, http.StatusInternalServerError, err.Error())
	}
	return response.Ok(ctx)
}

// 悬浮框数据统一返回
func FloatingBoxData(ctx *gin.Context) error {
	key := ctx.Query("key")
	label := ctx.Query("label")
	info, err := workbench.NewWorkbenchService().GetFloatingBoxData(key, label)
	if err != nil {
		return response.FailWithCodeMessage(ctx, http.StatusInternalServerError, err.Error())
	}
	return response.OkWithData(ctx, info)
}
