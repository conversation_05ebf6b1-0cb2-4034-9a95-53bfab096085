package logs

import (
	"github.com/gin-gonic/gin"

	"fobrain/fobrain/app/repository/logs"
	"fobrain/fobrain/common/request"
	"fobrain/fobrain/common/response"
	"fobrain/models/mysql/audit_logs"
)

type (
	AuditLogsSearchRequest struct {
		request.PageRequest
		Keyword string  `json:"keyword" form:"keyword" uri:"keyword" validate:"omitempty,max=50" zh:"模糊搜索"`
		UserId  uint64  `json:"user_id" form:"user_id" uri:"user_id" validate:"omitempty,number" zh:"用户ID"`
		Ip      string  `json:"ip" form:"ip" uri:"ip" validate:"omitempty,max=50" zh:"IP"`
		Ids     []int64 `json:"ids" form:"ids" uri:"ids" validate:"omitempty,max=50" zh:"ids"`
	}

	AuditLogsIdsRequest struct {
		Ids []uint64 `json:"ids" form:"ids" uri:"ids" zh:"网络区域ID"`
	}
)

var AuditLogsSearchValidate = request.Validate[AuditLogsSearchRequest]
var AuditLogsIdsValidate = request.Validate[AuditLogsIdsRequest]

// List 用户升级日志列表
func List(c *gin.Context) error {
	params, err := AuditLogsSearchValidate(c, &AuditLogsSearchRequest{})
	if err != nil {
		return err
	}

	list, total, err := audit_logs.NewAuditLogModel().UserList([]int64{}, params.Keyword, params.Ip, params.UserId, params.Page, params.PerPage, audit_logs.OperationTypeList)
	if err != nil {
		return err
	}
	return response.OkWithPageData(c, total, params.Page, params.PerPage, list)
}

// DeleteByIds 删除用户操作日志
func DeleteByIds(c *gin.Context) error {
	params, err := AuditLogsIdsValidate(c, &AuditLogsIdsRequest{})
	if err != nil {
		return err
	}
	err = audit_logs.NewAuditLogModel().DeleteAuditLogIds(params.Ids)
	if err != nil {
		return err
	}
	return response.OkWithMessage(c, "删除成功")
}

// Users 检索列表用户信息
func Users(c *gin.Context) error {
	list, err := logs.UserLogsList()
	if err != nil {
		return err
	}
	return response.OkWithData(c, list)
}

// Ips 检索列表ip信息
func Ips(c *gin.Context) error {
	list, err := audit_logs.NewAuditLogModel().Ips()
	if err != nil {
		return err
	}
	return response.OkWithData(c, list)
}

// Export 导出用户操作日志
func Export(c *gin.Context) error {
	l := &AuditLogsSearchRequest{
		PageRequest: request.PageRequest{
			Page:    1,
			PerPage: 10,
		},
	}
	params, err := AuditLogsSearchValidate(c, l)
	if err != nil {
		return err
	}
	path, err := logs.ExportUserLogs(params.Ids, params.Keyword, params.Ip, params.UserId)
	if err != nil {
		return err
	}
	return response.OkWithFile(c, path, true)
}
