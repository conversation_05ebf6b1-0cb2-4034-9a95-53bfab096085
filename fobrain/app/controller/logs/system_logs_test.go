package logs

import (
	"io/ioutil"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"

	"fobrain/fobrain/app/repository/logs"
	"fobrain/models/mysql/system_logs"
)

func TestSystemList(t *testing.T) {
	data := []system_logs.SystemLogs{
		{
			ModuleName: "AA",
			Result:     "BB",
		},
	}
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyMethodReturn(system_logs.NewSystemLogModel(), "List", data, int64(1), nil).Reset()
	// 创建一个测试请求和响应
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/system_logs?page=1&per_page=10", nil)
	// 创建一个Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 调用函数
	err := SystemList(c)
	assert.NoError(t, err)

	// 解析响应数据
	response := w.Result()
	defer response.Body.Close()
	body, _ := ioutil.ReadAll(response.Body)
	assert.NotEmpty(t, body)
}

func TestSystemExport(t *testing.T) {
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyFuncReturn(logs.ExportSystemLogs, "/data/url", nil).Reset()
	// 创建一个测试请求和响应
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/system_logs/export?page=1&per_page=10&day=7", nil)
	// 创建一个Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 调用函数
	err := SystemExport(c)
	assert.NoError(t, err)

	// 解析响应数据
	response := w.Result()
	defer response.Body.Close()
	body, _ := ioutil.ReadAll(response.Body)
	assert.NotEmpty(t, body)
}
