package logs

import (
	"time"

	"github.com/gin-gonic/gin"

	"fobrain/fobrain/app/repository/logs"
	"fobrain/fobrain/common/request"
	"fobrain/fobrain/common/response"
	"fobrain/models/mysql/system_logs"
)

type (
	SystemLogsSearchRequest struct {
		request.PageRequest
		Keyword string `json:"keyword" form:"keyword" uri:"keyword" validate:"omitempty,max=50" zh:"模糊搜索"`
		Day     int    `json:"day" form:"day" uri:"day" validate:"omitempty,number,oneof=7 30" zh:"天数"`
	}
)

var SystemLogsSearchValidate = request.Validate[SystemLogsSearchRequest]

// SystemList 获取系统日志列表
func SystemList(c *gin.Context) error {
	params, err := SystemLogsSearchValidate(c, &SystemLogsSearchRequest{})
	if err != nil {
		return err
	}

	list, total, err := system_logs.NewSystemLogModel().List(params.Keyword, "", params.Page, params.PerPage, system_logs.OperationTypeList)
	if err != nil {
		return err
	}
	return response.OkWithPageData(c, total, params.Page, params.PerPage, list)
}

// SystemExport 导出系统日志
func SystemExport(c *gin.Context) error {
	l := &SystemLogsSearchRequest{
		PageRequest: request.PageRequest{
			Page:    1,
			PerPage: 10,
		},
	}
	params, err := SystemLogsSearchValidate(c, l)
	if err != nil {
		return err
	}

	var startTime string
	if params.Day > 0 {
		startTime = time.Now().AddDate(0, 0, -params.Day).Format("2006-01-02 15:04:05")
	}

	path, err := logs.ExportSystemLogs(params.Keyword, startTime)
	if err != nil {
		return err
	}
	return response.OkWithFile(c, path, true)
}
