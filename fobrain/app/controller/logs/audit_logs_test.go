package logs

import (
	"io/ioutil"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"

	"fobrain/fobrain/app/repository/logs"
	"fobrain/models/mysql/audit_logs"
)

func TestList(t *testing.T) {
	data := []audit_logs.UserLogs{
		{
			AuditLog: audit_logs.AuditLog{
				Title:  "哈哈哈",
				UserId: 111,
				Path:   "/api/v1/audit_logs",
				Type:   1,
				Result: "Success",
				Ip:     "***********",
			},
			UserName: "Superman",
		},
	}
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyMethodReturn(audit_logs.NewAuditLogModel(), "UserList", data, int64(1), nil).Reset()
	// 创建一个测试请求和响应
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/audit_logs?page=1&per_page=10", nil)
	// 创建一个Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 调用函数
	err := List(c)
	assert.NoError(t, err)

	// 解析响应数据
	response := w.Result()
	defer response.Body.Close()
	body, _ := ioutil.ReadAll(response.Body)
	assert.NotEmpty(t, body)
}

func TestDeleteByIds(t *testing.T) {
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyMethodReturn(audit_logs.NewAuditLogModel(), "DeleteAuditLogIds", nil).Reset()
	// 创建一个测试请求和响应
	w := httptest.NewRecorder()
	req := httptest.NewRequest("DELETE", "/audit_logs", nil)
	// 创建一个Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 调用函数
	err := DeleteByIds(c)
	assert.NoError(t, err)
}

func TestUsers(t *testing.T) {
	data := []logs.UserLogs{
		{
			UserID: 1,
			Name:   "abc",
		},
	}
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyFuncReturn(logs.UserLogsList, data, nil).Reset()
	// 创建一个测试请求和响应
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/audit_logs/users", nil)
	// 创建一个Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 调用函数
	err := Users(c)
	assert.NoError(t, err)

	// 解析响应数据
	response := w.Result()
	defer response.Body.Close()
	body, _ := ioutil.ReadAll(response.Body)
	assert.NotEmpty(t, body)
}

func TestIps(t *testing.T) {
	data := []string{
		"127.0.0.1",
	}
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyMethodReturn(audit_logs.NewAuditLogModel(), "Ips", data, nil).Reset()
	// 创建一个测试请求和响应
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/audit_logs/ips", nil)
	// 创建一个Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 调用函数
	err := Ips(c)
	assert.NoError(t, err)

	// 解析响应数据
	response := w.Result()
	defer response.Body.Close()
	body, _ := ioutil.ReadAll(response.Body)
	assert.NotEmpty(t, body)
}

func TestExport(t *testing.T) {
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyFuncReturn(logs.ExportUserLogs, "/data/url", nil).Reset()
	// 创建一个测试请求和响应
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/audit_logs/export?page=1&per_page=10", nil)
	// 创建一个Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 调用函数
	err := Export(c)
	assert.NoError(t, err)

	// 解析响应数据
	response := w.Result()
	defer response.Body.Close()
	body, _ := ioutil.ReadAll(response.Body)
	assert.NotEmpty(t, body)
}
