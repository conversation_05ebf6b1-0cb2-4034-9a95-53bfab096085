package logs

import (
	"errors"
	"fmt"
	"time"

	"github.com/gin-gonic/gin"

	"fobrain/fobrain/app/repository/logs"
	"fobrain/fobrain/common/request"
	"fobrain/fobrain/common/response"
	"fobrain/initialize/mysql"
	"fobrain/models/mysql/logs_qc_setting"
)

type (
	QcLogsSettingCreate struct {
		Host          string `json:"host" form:"host" uri:"host" validate:"required,max=50" zh:"地址"`
		Port          uint   `json:"port" form:"port" uri:"port" validate:"required,number,min=1,max=65536" zh:"端口"`
		Status        int    `json:"status" form:"status" uri:"status" validate:"required,number,oneof=1 2" zh:"状态 1启用 2禁用"`
		LogType       int    `json:"log_type" form:"log_type" uri:"log_type" validate:"required,number,oneof=1 2 3" zh:"日志类型 1用户日志 2系统日志 3全部"`
		Plan          int    `json:"plan" form:"plan" uri:"plan" validate:"omitempty,number,oneof=1 2 3 4 5" zh:"计划：1.立即执行、2.仅执行一次、3.每天、4.每周、5.每月"`
		Period        string `json:"period" form:"period" uri:"period"  zh:"周期：周几或者每月几号"`
		ExecuteTime   string `json:"execute_time" form:"execute_time" uri:"execute_time" zh:"执行时间、24小时制格式"`
		RepeatEndTime string `json:"repeat_end_time" form:"repeat_end_time" uri:"repeat_end_time" zh:"结束重复执行的时间"`
		OneTime       string `json:"one_time" form:"one_time" uri:"one_time" zh:"执行一次的日志"`
	}
)

var QcLogsSettingCreateValidate = request.Validate[QcLogsSettingCreate]

// CreateOrUpdateLogsQcSetting 日志外发配置
func CreateOrUpdateLogsQcSetting(c *gin.Context) error {
	params, err := QcLogsSettingCreateValidate(c, &QcLogsSettingCreate{})
	if err != nil {
		return err
	}

	if (params.Plan == logs_qc_setting.PlanExecutionOne || params.Plan == logs_qc_setting.PlanExecutionDay) && params.ExecuteTime == "" {
		return errors.New("请选择执行时间")
	}

	if (params.Plan == logs_qc_setting.PlanExecutionWeek || params.Plan == logs_qc_setting.PlanExecutionMonth) && params.Period == "" {
		return errors.New("请选择周期时间")
	}
	if params.Plan == logs_qc_setting.PlanExecutionOne && (params.RepeatEndTime == "" || params.OneTime == "") {
		params.RepeatEndTime = params.OneTime
	}
	if (params.Plan == logs_qc_setting.PlanExecutionOne || params.Plan == logs_qc_setting.PlanImmediateExecution) && params.RepeatEndTime == "" {
		format := time.Now().Format("2006-01-02")
		params.RepeatEndTime = fmt.Sprintf("%s 23:59:59", format)
	}

	info := logs_qc_setting.LogsQcSetting{
		Host:          params.Host,
		Port:          params.Port,
		Status:        params.Status,
		LogType:       params.LogType,
		Plan:          params.Plan,
		Period:        params.Period,
		ExecuteTime:   params.ExecuteTime,
		RepeatEndTime: params.RepeatEndTime,
		OneTime:       params.OneTime,
	}

	err = logs.CreateOrUpdateLogsQcSetting(info)
	if err != nil {
		return err
	}
	return response.OkWithMessage(c, "操作成功")
}

// GetLogsQcSetting 获取日志外发配置
func GetLogsQcSetting(c *gin.Context) error {
	handlers := make([]mysql.HandleFunc, 0)
	first, err := logs_qc_setting.NewLogsQcSettingModel().First(handlers...)
	if err != nil {
		if err.Error() == "record not found" {
			return response.OkWithData(c, nil)
		}
		return err
	}
	if first.RepeatEndTime != "" {
		t, _ := time.Parse(time.RFC3339, first.RepeatEndTime)
		first.RepeatEndTime = t.Format("2006-01-02")
	}
	return response.OkWithData(c, first)
}
