package logs

import (
	"bytes"
	"io/ioutil"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"

	"fobrain/fobrain/app/repository/logs"
	"fobrain/models/mysql/logs_qc_setting"
)

func TestCreateOrUpdateLogsQcSetting(t *testing.T) {
	str := `{
			"host": "*********",
			"port": 80,
			"status": 1,
			"log_type": 3,
			"plan": 2,
			"period": "",
			"execute_time": "12:36",
			"repeat_end_time":"",
			"one_time":""
		}`
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyFuncReturn(logs.CreateOrUpdateLogsQcSetting, nil).Reset()
	// 创建一个测试请求和响应
	w := httptest.NewRecorder()
	req := httptest.NewRequest("POST", "/api/v1/logs_qc_setting", bytes.NewBufferString(str))
	req.Header.Set("Content-Type", "application/json")
	// 创建一个Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 调用函数
	err := CreateOrUpdateLogsQcSetting(c)
	assert.NoError(t, err)

	// 解析响应数据
	response := w.Result()
	defer response.Body.Close()
	body, _ := ioutil.ReadAll(response.Body)
	assert.NotEmpty(t, body)
}

func TestGetLogsQcSetting(t *testing.T) {
	info := logs_qc_setting.LogsQcSetting{
		Host: "*********",
	}
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyMethodReturn(logs_qc_setting.NewLogsQcSettingModel(), "First", info, nil).Reset()
	// 创建一个测试请求和响应
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/logs_qc_setting", nil)
	req.Header.Set("Content-Type", "application/json")
	// 创建一个Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 调用函数
	err := GetLogsQcSetting(c)
	assert.NoError(t, err)

	// 解析响应数据
	response := w.Result()
	defer response.Body.Close()
	body, _ := ioutil.ReadAll(response.Body)
	assert.NotEmpty(t, body)
}
