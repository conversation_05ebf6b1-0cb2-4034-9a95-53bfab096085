package crontab

import (
	"bytes"
	crontab2 "fobrain/fobrain/app/crontab"
	"fobrain/initialize/mysql"
	"fobrain/models/mysql/crontab"
	"fobrain/pkg/utils"
	"net/http/httptest"
	"reflect"
	"testing"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"
)

func TestList(t *testing.T) {
	patch := gomonkey.ApplyMethodReturn(&crontab.CrontabModel{}, "List", []*crontab.CrontabModel{}, int64(0), nil)
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/crontab?page=1&per_page=10", bytes.NewReader([]byte{}))

	c, _ := gin.CreateTestContext(w)
	c.Request = req

	err := List(c)
	patch.Reset()
	assert.Nil(t, err)
}

func TestListError(t *testing.T) {
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/crontab", nil)

	c, _ := gin.CreateTestContext(w)
	c.Request = req

	err := List(c)
	assert.Error(t, err)
	assert.Equal(t, err.Error(), "页数为必填字段")
}

func TestAdd(t *testing.T) {
	patches := []*gomonkey.Patches{
		gomonkey.ApplyMethodReturn(&crontab.CrontabModel{}, "Create", nil),
		gomonkey.ApplyMethodReturn(&crontab2.Cron{}, "LoadMysqlJob", nil),
	}
	w := httptest.NewRecorder()
	req := httptest.NewRequest("POST", "/api/v1/crontab?name=name&type=1&spec=spec&method=method", bytes.NewReader([]byte{}))

	c, _ := gin.CreateTestContext(w)
	c.Request = req

	err := Add(c)
	for _, patch := range patches {
		patch.Reset()
	}
	assert.Nil(t, err)
}

func TestAddError(t *testing.T) {
	w := httptest.NewRecorder()
	req := httptest.NewRequest("POST", "/api/v1/crontab", nil)

	c, _ := gin.CreateTestContext(w)
	c.Request = req

	err := Add(c)
	assert.Error(t, err)
	assert.Equal(t, err.Error(), "定时任务名称为必填字段")
}

func TestDelete(t *testing.T) {
	patches := []*gomonkey.Patches{
		gomonkey.ApplyMethodReturn(&crontab.CrontabModel{}, "Delete", nil),
		gomonkey.ApplyFuncReturn(utils.NewPoolFnByWait, nil),
	}
	w := httptest.NewRecorder()
	req := httptest.NewRequest("DELETE", "/api/v1/crontab?ids=1", bytes.NewReader([]byte{}))

	c, _ := gin.CreateTestContext(w)
	c.Request = req

	err := Delete(c)
	for _, patch := range patches {
		patch.Reset()
	}
	assert.Nil(t, err)
}

func TestDeleteError(t *testing.T) {
	w := httptest.NewRecorder()
	req := httptest.NewRequest("DELETE", "/api/v1/crontab", nil)

	c, _ := gin.CreateTestContext(w)
	c.Request = req

	err := Delete(c)
	assert.Error(t, err)
	assert.Equal(t, err.Error(), "任务ID为必填字段")
}

func TestUpdate(t *testing.T) {
	patches := []*gomonkey.Patches{
		gomonkey.ApplyMethodReturn(&crontab.CrontabModel{}, "First", crontab.NewCrontabModel(), nil),
		gomonkey.ApplyMethodReturn(&crontab.CrontabModel{}, "Updates", nil),
	}
	w := httptest.NewRecorder()
	req := httptest.NewRequest("DELETE", "/api/v1/crontab?id=1&name=name&type=1&spec=spec&method=method", bytes.NewReader([]byte{}))

	c, _ := gin.CreateTestContext(w)
	c.Request = req

	err := Update(c)
	for _, patch := range patches {
		patch.Reset()
	}
	assert.Nil(t, err)
}

func TestUpdateError(t *testing.T) {
	w := httptest.NewRecorder()
	req := httptest.NewRequest("POST", "/api/v1/crontab/1", nil)

	c, _ := gin.CreateTestContext(w)
	c.Request = req

	err := Update(c)
	assert.Error(t, err)
	assert.Equal(t, err.Error(), "任务ID为必填字段")
}

func TestHistoryClear(t *testing.T) {
	w := httptest.NewRecorder()
	req := httptest.NewRequest("DELETE", "/api/v1/crontab/1/history?id=1", bytes.NewReader([]byte{}))

	c, _ := gin.CreateTestContext(w)
	c.Request = req

	patch := gomonkey.ApplyMethod(reflect.TypeOf(&crontab.HistroyModel{}), "Where",
		func(_ *crontab.HistroyModel, query interface{}, args ...interface{}) *gorm.DB {
			return &gorm.DB{}
		})

	patch.ApplyMethod(reflect.TypeOf(&gorm.DB{}), "Delete",
		func(_ *gorm.DB, value interface{}, conds ...interface{}) *gorm.DB {
			return &gorm.DB{Error: nil}
		})

	err := HistoryClear(c)
	patch.Reset()
	assert.Nil(t, err)
}

func TestHistoryClearError(t *testing.T) {
	w := httptest.NewRecorder()
	req := httptest.NewRequest("DELETE", "/api/v1/crontab/1/history", nil)

	c, _ := gin.CreateTestContext(w)
	c.Request = req

	err := HistoryClear(c)
	assert.Error(t, err)
	assert.Equal(t, err.Error(), "任务ID为必填字段")
}

func TestHistoryList(t *testing.T) {
	patch := gomonkey.ApplyMethod(reflect.TypeOf(&crontab.HistroyModel{}), "List",
		func(_ *crontab.HistroyModel, page, perPage int, handlers ...mysql.HandleFunc) (interface{}, int64, error) {
			return nil, int64(0), nil
		})
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/crontab/history?page=1&per_page=10", bytes.NewReader([]byte{}))

	c, _ := gin.CreateTestContext(w)
	c.Request = req

	err := HistoryList(c)
	patch.Reset()
	assert.Nil(t, err)
}
