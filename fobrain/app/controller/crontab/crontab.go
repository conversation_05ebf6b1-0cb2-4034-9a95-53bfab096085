package crontab

import (
	"reflect"

	crontab2 "fobrain/fobrain/app/crontab"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"

	"fobrain/fobrain/common/request"
	"fobrain/fobrain/common/response"
	log "fobrain/fobrain/logs"
	"fobrain/initialize/mysql"
	"fobrain/models/mysql/crontab"
	"fobrain/pkg/utils"
)

func List(ctx *gin.Context) error {
	params, err := request.Validate(ctx, &struct {
		request.PageRequest
		UserId    uint64   `json:"user_id" uri:"user_id" form:"user_id" validate:"omitempty,number" zh:"用户ID"`
		Name      string   `json:"name" uri:"name" form:"name" validate:"omitempty" zh:"定时任务名称"`
		Status    int      `json:"status" uri:"status" form:"status" validate:"omitempty,number,oneof=1 2" zh:"任务状态"`
		Type      int      `json:"type" uri:"type" form:"type" validate:"omitempty,number,oneof=1" zh:"任务类型"`
		Method    string   `json:"method" uri:"method" form:"method" validate:"omitempty" zh:"调用方法"`
		Params    string   `json:"params" uri:"params" form:"params" validate:"omitempty" zh:"任务参数"`
		CreatedAt []string `json:"created_at" uri:"created_at" form:"created_at" validate:"omitempty,dive,omitempty,datetime=2006-01-02 15:04:05" zh:"创建时间"`
		UpdatedAt []string `json:"updated_at" uri:"updated_at" form:"updated_at" validate:"omitempty,dive,omitempty,datetime=2006-01-02 15:04:05" zh:"更新时间"`
	}{})
	if err != nil {
		return err
	}
	// 查询条件
	handlers := make([]mysql.HandleFunc, 0)
	handlers = append(handlers, mysql.WithOrder("created_at DESC"))
	handlers = utils.CanAppend(params.Status > 0, handlers, mysql.WithColumnValue("status", params.Status))
	handlers = utils.CanAppend(params.Type > 0, handlers, mysql.WithColumnValue("type", params.Type))
	handlers = utils.CanAppend(params.Name != "", handlers, mysql.WithLike("name", "%"+params.Name+"%"))
	handlers = utils.CanAppend(params.Params != "", handlers, mysql.WithLike("params", "%"+params.Params+"%"))
	handlers = utils.CanAppend(params.Method != "", handlers, mysql.WithLike("method", "%"+params.Method+"%"))
	handlers = utils.CanAppend(len(params.CreatedAt) != 0, handlers, mysql.WithBetweenAt("created_at", params.CreatedAt))
	handlers = utils.CanAppend(len(params.UpdatedAt) != 0, handlers, mysql.WithBetweenAt("updated_at", params.UpdatedAt))
	// 查询结果,并返回
	list, total, err := crontab.NewCrontabModel().List(params.Page, params.PerPage, handlers...)
	if err != nil {
		return err
	}
	return response.OkWithPageData(ctx, total, params.Page, params.PerPage, list)
}

func Add(ctx *gin.Context) error {
	params, err := request.Validate(ctx, &struct {
		Name   string `json:"name" uri:"name" form:"name" validate:"required" zh:"定时任务名称"`
		Status int    `json:"status" uri:"status" form:"status" validate:"omitempty,number,oneof=1 2" zh:"任务状态"`
		Type   int    `json:"type" uri:"type" form:"type" validate:"required,number,oneof=1" zh:"任务类型"`
		Spec   string `json:"spec" uri:"spec" form:"spec" validate:"required" zh:"定时参数"`
		Method string `json:"method" uri:"method" form:"method" validate:"required" zh:"调用方法"`
		Params string `json:"params" uri:"params" form:"params" validate:"omitempty" zh:"任务参数"`
	}{})
	if err != nil {
		return err
	}
	cron := &crontab.CrontabModel{
		UserId: request.GetUserId(ctx), Name: params.Name, Status: params.Status,
		Type: params.Type, Spec: params.Spec, Method: params.Method, Params: params.Params,
	}
	if _, ok := reflect.TypeOf(crontab2.GetJobIns()).MethodByName(params.Method); !ok {
		return response.FailWithMessage(ctx, "任务方法不存在")
	}
	// 创建定时任务
	if err = crontab.NewCrontabModel().Create(cron); err != nil {
		return err
	}
	// 加载定时任务
	if err = crontab2.GetCronIns().LoadMysqlJob(cron.Id); err != nil {
		return err
	}
	return response.Ok(ctx)
}

func Delete(ctx *gin.Context) error {
	params, err := request.Validate(ctx, &struct {
		Ids []uint64 `json:"ids" uri:"ids" form:"ids" validate:"required,dive,number" zh:"任务ID"`
	}{})
	if err != nil {
		return err
	}
	// 删除任务
	if err = crontab.NewCrontabModel().Delete(params.Ids); err != nil {
		return err
	}
	if pErr := utils.NewPoolFnByWait(
		2,
		func() {
			// 停止定时任务
			utils.ListFunc(params.Ids, func(t uint64) (any, bool) {
				crontab2.GetCronIns().StopJob(cast.ToString(t))
				return nil, true
			})
		},
		func() {
			// 清除执行历史
			if dErr := crontab.NewHistoryModel().Del(mysql.WithValuesIn("cron_id", params.Ids)); dErr != nil {
				log.GetLogger().Warnln("清除定时任务执行历史数据失败", params.Ids)
			}
		},
	); pErr != nil {
		return pErr
	}
	return response.Ok(ctx)
}

func Update(ctx *gin.Context) error {
	params, err := request.Validate(ctx, &struct {
		Id     uint64 `json:"id" uri:"id" form:"id" validate:"required,number" zh:"任务ID"`
		Name   string `json:"name,omitempty" uri:"name" form:"name" validate:"required" zh:"定时任务名称"`
		Status int    `json:"status,omitempty" uri:"status" form:"status" validate:"omitempty,number" zh:"任务状态"`
		Type   int    `json:"type,omitempty" uri:"type" form:"type" validate:"required,number,oneof=1" zh:"任务类型"`
		Spec   string `json:"spec,omitempty" uri:"spec" form:"spec" validate:"required" zh:"定时参数"`
		Method string `json:"method,omitempty" uri:"method" form:"method" validate:"required" zh:"调用方法"`
		Params string `json:"params,omitempty" uri:"params" form:"params" validate:"omitempty" zh:"任务参数"`
	}{})
	if err != nil {
		return err
	}
	cronInfo, err := crontab.NewCrontabModel().First(mysql.WithColumnValue("id", params.Id))
	if err != nil {
		return err
	}
	cronInfo.Name = params.Name
	cronInfo.Type = params.Type
	cronInfo.Spec = params.Spec
	cronInfo.Method = params.Method
	cronInfo.Status = params.Status
	cronInfo.Params = params.Params
	err = crontab.NewCrontabModel().Updates(cronInfo, mysql.WithSelect("name", "type", "spec", "status", "method", "params"))
	if err != nil {
		return err
	}
	if cronInfo.Status == 0 {
		// 停止任务
		crontab2.GetCronIns().StopJob(cast.ToString(cronInfo.Id))
	} else {
		// 更新Job
		_ = crontab2.GetCronIns().LoadMysqlJob(cronInfo.Id)
	}
	return response.Ok(ctx)
}

func HistoryClear(ctx *gin.Context) error {
	params, err := request.Validate(ctx, &struct {
		Id uint64 `json:"id" uri:"id" form:"id" validate:"required,number" zh:"任务ID"`
	}{})
	if err != nil {
		return err
	}
	// 删除历史执行数据
	if hErr := crontab.NewHistoryModel().Where("cron_id in (?)", params.Id).Delete(&crontab.HistroyModel{}).Error; hErr != nil {
		return hErr
	}
	return response.Ok(ctx)
}

func HistoryList(ctx *gin.Context) error {
	params, err := request.Validate(ctx, &struct {
		request.PageRequest
		Id      uint64   `json:"id,omitempty" uri:"id" form:"id" validate:"omitempty,number" zh:"任务ID"`
		Status  int32    `json:"status,omitempty" uri:"status" form:"status" validate:"omitempty,number" zh:"执行状态"`
		StartAt []string `json:"start_at,omitempty" uri:"start_at" form:"start_at" validate:"omitempty,dive" zh:"开始执行时间"`
		EndAt   []string `json:"end_at,omitempty" uri:"end_at" form:"end_at" validate:"omitempty,dive" zh:"执行结束时间"`
	}{})
	handlers := make([]mysql.HandleFunc, 0)
	handlers = append(handlers, mysql.WithColumnValue("cron_id", params.Id))
	handlers = append(handlers, mysql.WithOrder("created_at DESC"))
	handlers = utils.CanAppend(params.Status > 0, handlers, mysql.WithWhere("status = ?", params.Status))
	handlers = utils.CanAppend(len(params.StartAt) != 0, handlers, mysql.WithBetweenAt("start_at", params.StartAt))
	handlers = utils.CanAppend(len(params.EndAt) != 0, handlers, mysql.WithBetweenAt("end_at", params.EndAt))
	list, total, err := crontab.NewHistoryModel().List(params.Page, params.PerPage, handlers...)
	if err != nil {
		return err
	}
	return response.OkWithPageData(ctx, total, params.Page, params.PerPage, list)
}
