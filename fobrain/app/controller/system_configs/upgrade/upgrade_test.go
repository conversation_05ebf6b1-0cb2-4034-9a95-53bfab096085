package upgrade

import (
	"bytes"
	"fobrain/fobrain/app/services/system_configs/upgrade"
	"fobrain/fobrain/common/localtime"
	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/pkg/fortest"
	"fobrain/pkg/utils"
	"io"
	"mime/multipart"
	"net/http"
	"net/http/httptest"
	"os"
	"os/exec"
	"path/filepath"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/gin-gonic/gin"
)

func TestSystemUpgradePageInfo(t *testing.T) {
	fortest.Init()
	fortest.SetGoTestMock()
	r := gin.Default()
	url := "/api/v1/system/upgrade/info"
	r.GET(url, func(ctx *gin.Context) {
		SystemUpgradePageInfo(ctx)
	})
	mockDb := testcommon.InitSqlMock()
	defer mockDb.Close()
	mockDb.ExpectQuery("SELECT * FROM `system_upgrade_log`").
		WillReturnRows(
			sqlmock.NewRows(
				[]string{"id", "from_version", "to_version", "status", "release_desc", "error_message", "created_at", "updated_at"}).
				AddRow(1, "1.0.0", "2.0.0", "success", "desc", "err_msg", localtime.NewLocalTime(time.Now()), localtime.NewLocalTime(time.Now())),
		)
	patches := gomonkey.ApplyGlobalVar(&upgrade.VersionFilePath, "./version")
	defer patches.Reset()
	info := &upgrade.VersionInfo{
		ProductModel:   "fobrain-00001",
		ReleaseVersion: "2.0.0",
		ReleaseDate:    "2024-09-06",
		ReleaseDesc:    "全新升级Fobrain带给您丝滑体验",
		VersionDependencies: []string{
			"1.0.0",
			"1.9.0",
		},
	}
	upgrade.GenVersionFile(upgrade.VersionFilePath, info)

	tests := []struct {
		name           string
		expectedStatus int
	}{
		{
			name: "test1",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req, _ := http.NewRequest(http.MethodGet, url, nil)
			q := req.URL.Query()
			req.URL.RawQuery = q.Encode()
			// 创建响应记录器
			w := httptest.NewRecorder()

			// 模拟请求
			r.ServeHTTP(w, req)
			// 验证响应
			t.Log(w.Code)
		})
	}
	t.Log("---!! success")
}

func TestSystemUpgradeUploadFile(t *testing.T) {
	time.Sleep(time.Second * 3)
	versionPatches := gomonkey.ApplyGlobalVar(&upgrade.VersionFilePath, "./version")
	defer versionPatches.Reset()
	dir, err := os.Getwd()
	if err != nil {
		t.Fatal(err)
	}
	patches := gomonkey.ApplyGlobalVar(&upgrade.UpgradeStoragePath, dir)
	defer patches.Reset()

	info := &upgrade.VersionInfo{
		ProductModel:   "fobrain-00001",
		ReleaseVersion: "2.0.0",
		ReleaseDate:    "2024-09-06",
		ReleaseDesc:    "全新升级Fobrain带给您丝滑体验",
		VersionDependencies: []string{
			"1.0.0",
			"1.9.0",
		},
	}
	gomonkey.ApplyFuncReturn(upgrade.SystemUpgradeExec, nil)

	fortest.Init()
	fortest.SetGoTestMock()
	r := gin.Default()
	url := "/api/v1/system/upgrade/upload"
	r.POST(url, func(ctx *gin.Context) {
		SystemUpgradeUploadFile(ctx)
	})

	// 创建文件
	exampleFileName := "example.txt"
	file, err := os.Create(exampleFileName)
	if err != nil {
		t.Fatal("Error creating file:", err)
	}
	defer file.Close()

	// 写入内容到文件
	_, err = file.WriteString("Hello, World!\n")
	if err != nil {
		t.Fatal("Error writing to file:", err)
	}
	// 要创建的 .tar.gz 文件名
	tarGzFileName := "example.tar.gz"

	upgrade.GenVersionFile("./version", info)

	// 准备 tar 命令参数
	args := []string{"-cvzf", tarGzFileName, exampleFileName, "version"}
	// 执行 tar 命令
	cmd := exec.Command("tar", args...)
	// 运行命令并捕获输出
	output, err := cmd.CombinedOutput()
	if err != nil {
		t.Fatalf("Error executing tar command: %v, Command output: %s\n", err, output)
	}
	os.Remove("./version")

	defer os.Remove(exampleFileName)
	defer os.Remove(tarGzFileName)

	t.Log("---预备工作完成")
	// 创建一个新的文件缓冲区
	body := new(bytes.Buffer)
	writer := multipart.NewWriter(body)
	// 创建一个 FormFile 并写入到 multipart.Writer
	part, err := writer.CreateFormFile("file", tarGzFileName)
	if err != nil {
		t.Fatalf("Failed to create form file: %v", err)
	}
	// 将临时文件的内容写入到 multipart.Writer
	file.Seek(0, io.SeekStart) // 将文件指针重置到文件开头
	_, err = io.Copy(part, file)
	if err != nil {
		t.Fatalf("Failed to copy file content: %v", err)
	}
	// 关闭 writer 以结束 multipart 的表单
	writer.Close()
	t.Log("---文件表单写入完毕")
	tests := []struct {
		name           string
		expectedStatus int
	}{
		{
			name:           "test1",
			expectedStatus: 200,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req, _ := http.NewRequest(http.MethodPost, url, body)
			req.Header.Set("Content-Type", writer.FormDataContentType())
			// 创建响应记录器
			w := httptest.NewRecorder()

			// 模拟请求
			r.ServeHTTP(w, req)
			// 验证响应
			t.Log(w.Code)
		})
	}
	os.RemoveAll("./storage")
	os.RemoveAll(time.Now().Format(utils.DateMinuteLayout))
	t.Log("---!! success")
}

// 测试 cleanUpgradeDirectory 函数
func TestCleanUpgradeDirectory(t *testing.T) {
	// 创建一个临时测试目录
	testDir := "test"
	err := os.Mkdir(testDir, 0777)
	if err != nil {
		t.Fatalf("Failed to create test directory: %v", err)
	}
	defer os.RemoveAll(testDir)
	// 创建一些测试文件和目录
	createTestEntries := func() {
		// 创建升级日志
		for _, name := range []string{"upgrade_20241010_123456.log", "upgrade_20241009_123456.log", "upgrade_20241008_123456.log", "upgrade_20241007_123456.log"} {
			path := filepath.Join(testDir, name)
			file, err := os.Create(path)
			if err != nil {
				t.Fatal(err)
			}
			file.Close()
		}

		// 创建升级包
		for _, name := range []string{"20241010123456-exec-dir", "20241009123456-exec-dir", "20241008123456-exec-dir", "20241007123456-exec-dir"} {
			path := filepath.Join(testDir, name)
			err := os.Mkdir(path, 0755)
			if err != nil {
				t.Fatalf("Failed to create directory %s: %v", path, err)
			}
		}
	}

	// 测试场景：保留最新的两个升级包和日志
	createTestEntries()

	// 调用 cleanUpgradeDirectory 函数
	err = cleanUpgradeDirectory(testDir, 2)
	if err != nil {
		t.Fatalf("cleanUpgradeDirectory failed: %v", err)
	}

	packages, logs, err := matchLogsAndPackage(testDir)
	if err != nil {
		t.Fatal(err)
	}
	if len(packages) != 2 || len(logs) != 2 {
		t.Fatal(packages, logs)
	}

	// 预期保留的升级包和日志

	expectedPackages := []string{testDir + "/upgrade_20241008_123456.log",
		testDir + "/upgrade_20241007_123456.log"}
	expectedLogs := []string{testDir + "/20241008123456-exec-dir",
		testDir + "/20241007123456-exec-dir"}

	for i := 0; i < len(packages); i++ {
		exeit := false
		for j := 0; j < len(expectedPackages); j++ {
			if expectedPackages[j] == packages[i] {
				exeit = true
			}
		}
		if exeit != true {
			t.Fatal("remove package expect err:", packages, expectedPackages)
		}
	}
	for i := 0; i < len(logs); i++ {
		exeit := false
		for j := 0; j < len(expectedLogs); j++ {
			if expectedLogs[j] == logs[i] {
				exeit = true
			}
		}
		if exeit != true {
			t.Fatal("remove logs expect err", logs, expectedLogs)
		}
	}
}
