package upgrade

import (
	"errors"
	"fmt"
	"fobrain/fobrain/app/services/system_configs/upgrade"
	"fobrain/fobrain/common/license"
	"fobrain/fobrain/common/response"
	"fobrain/pkg/utils"
	"net/http"
	"os"
	"path/filepath"
	"regexp"
	"sort"
	"sync"
	"sync/atomic"
	"time"

	"github.com/gin-gonic/gin"
	"go-micro.dev/v4/logger"
)

// 移除 upgradeLock
var (
	isUpgrading  atomic.Bool
	upgradeTimer *time.Timer
	timerLock    sync.Mutex
)

func UpgradeManualUnlock(ctx *gin.Context) error {
	// 尝试将 isUpgrading 设置为 false
	wasLocked := isUpgrading.Swap(false)

	// 如果之前是锁定状态，现在解锁了，停止升级定时器
	if wasLocked {
		stopUpgradeTimer()
	}

	// 返回更新后的版本信息
	return response.OkWithData(ctx, "手动解锁成功")
}

// 添加中间件函数
func UpgradeMiddleware() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		if isUpgrading.Load() &&
			ctx.FullPath() != "/api/v1/system/upgrade/upload" &&
			ctx.FullPath() != "/api/v1/system/upgrade/process" &&
			ctx.FullPath() != "/api/v1/system/upgrade/info" &&
			ctx.FullPath() != "/api/v1/user" &&
			ctx.FullPath() != "/api/v1/user_access/my_menus_tree" &&
			ctx.FullPath() != "/api/v1/system/upgrade/manual/unlock" {
			response.FailWithDetailed(ctx, http.StatusLocked, map[string]string{
				"type": "upgrade",
			}, "系统正在升级中，请稍后再试")
			ctx.Abort()
			return
		}
		ctx.Next()
	}
}

func SystemUpgradePageInfo(ctx *gin.Context) error {
	version, err := upgrade.GetVersionInfo(upgrade.VersionFilePath, false)
	if err != nil {
		logger.Errorf("[ERROR] GetVersionInfo Failed, err:%v,\n", err)
		return response.FailWithCodeMessage(ctx, http.StatusInternalServerError, err.Error())
	}
	version.IsLocked = isUpgrading.Load()

	return response.OkWithData(ctx, version)
}

func startUpgradeTimer() {
	stopUpgradeTimer() // 确保之前的定时器被停止
	upgradeTimer = time.AfterFunc(5*time.Minute, func() {
		if isUpgrading.Swap(false) {
			logger.Warn("[WARN] 升级过程超时，已自动解锁")
		}
	})
}

func stopUpgradeTimer() {
	timerLock.Lock()
	defer timerLock.Unlock()
	if upgradeTimer != nil {
		upgradeTimer.Stop()
		upgradeTimer = nil // 停止后将 timer 设为 nil
	}
}

func SystemUpgradeUploadFile(ctx *gin.Context) error {
	if isUpgrading.Load() {
		return response.FailWithMessage(ctx, "系统正在升级中，请稍后再试")
	}
	//true就是服务已过期，不能升级
	isExpired, _ := license.GetLicense().IsServiceExpired()
	if isExpired {
		return response.FailWithMessage(ctx, "服务授权已过期,暂时不能升级")
	}

	isUpgrading.Store(true)
	startUpgradeTimer() // 启动升级定时器

	defer func() {
		if err := recover(); err != nil {
			isUpgrading.Store(false)
			stopUpgradeTimer()
			panic(err) // 重新抛出 panic
		}
	}()

	savePath := filepath.Join(upgrade.UpgradeStoragePath, "exec-dir")
	limitType := map[string]struct{}{
		".gz":  {},
		".tgz": {},
	}

	result := utils.UploadFile(ctx, savePath, 5000, limitType, false)
	if result.Err != nil {
		isUpgrading.Store(false)
		stopUpgradeTimer()
		return response.FailWithMessage(ctx, result.Err.Error())
	}

	logger.Infof("[INFO] SystemUpgradeUploadFile saveAbsolutePath:%s, FileName:%s, FileExt:%s\n", savePath, result.FileName, result.FileExt)

	err := upgrade.SystemUpgradeExec(savePath, result.FileName, result.FileExt, "")
	if err != nil {
		isUpgrading.Store(false)
		stopUpgradeTimer()
		logger.Errorf("[ERROR] SystemUpgradeUploadFile SystemUpgradeExec Failed, err:%v,\n", err)

		if versionErr, ok := err.(*upgrade.VersionTooLowError); ok {
			// 版本过低错误，返回 400 状态码
			return response.FailWithCodeMessage(ctx, http.StatusBadRequest, versionErr.Error())
		}
		if errors.Is(err, utils.UpgradeNoNeed) {
			return response.FailWithCodeMessage(ctx, http.StatusBadRequest, err.Error())
		}

		// 其他错误仍然返回 500 状态码
		return response.FailWithCodeMessage(ctx, http.StatusInternalServerError, err.Error())
	}
	return response.Ok(ctx)
}

func SystemUpgradeProgress(ctx *gin.Context) error {
	const progressErrCode = 555
	info, err := upgrade.GetUpgradeProcess()
	if err != nil {
		logger.Errorf("[ERROR] SystemUpgradeProgress error: %v", err)
		return response.FailWithCodeMessage(ctx, progressErrCode, "获取升级进度失败")
	}

	if info == nil {
		return response.FailWithCodeMessage(ctx, progressErrCode, "无法获取升级进度信息")
	}

	// 检查升级是否完成或失败
	if info.IsSuccessful() || info.Failed {
		if isUpgrading.Swap(false) {
			stopUpgradeTimer()
		}
	}
	//若成功则删除多余的安装包，默认保留三个
	if err := cleanUpgradeDirectory(upgrade.UpgradeStoragePath, 3); err != nil {
		logger.Errorf("[ERROR]删除多余安装包失败%v", err)
	}

	if info.Failed {
		logger.Errorf("[ERROR] 升级失败: %s", info.Msg)
		return response.FailWithCodeMessage(ctx, progressErrCode, info.Msg)
	}

	return response.OkWithData(ctx, info.GetProcess())
}

func SystemGenVersionFile(ctx *gin.Context) error {
	var body upgrade.VersionInfo
	if err := ctx.ShouldBindJSON(&body); err != nil {
		return response.FailWithMessage(ctx, err.Error())
	}
	if !utils.IsDirExist(upgrade.VersionFilePath) {
		if mkdirErr := utils.Mkdir(upgrade.VersionFilePath); mkdirErr != nil {
			return response.FailWithCodeMessage(ctx, http.StatusInternalServerError, mkdirErr.Error())
		}
	}
	versionPath := filepath.Join(upgrade.VersionFilePath, fmt.Sprintf("version-%s", time.Now().Format(utils.DateMinuteLayout)))

	if err := upgrade.GenVersionFile(versionPath, &body); err != nil {
		return response.FailWithCodeMessage(ctx, http.StatusInternalServerError, err.Error())
	}
	return response.OkWithData(ctx, versionPath)
}

func cleanUpgradeDirectory(directory string, retainCount int) error {
	upgradePackages, upgradeLogs, err := matchLogsAndPackage(directory)
	if err != nil {
		return err
	}
	if len(upgradePackages) <= retainCount || len(upgradeLogs) <= retainCount {
		return nil
	}
	//log.Println("package number:", len(upgradePackages))
	//log.Println("log number:", len(upgradeLogs))

	//sort logs and packages by time
	sort.Slice(upgradePackages, func(i, j int) bool {
		infoI, _ := os.Stat(upgradePackages[i])
		infoJ, _ := os.Stat(upgradePackages[j])
		return infoI.ModTime().After(infoJ.ModTime())
	})

	sort.Slice(upgradeLogs, func(i, j int) bool {
		infoI, _ := os.Stat(upgradeLogs[i])
		infoJ, _ := os.Stat(upgradeLogs[j])
		return infoI.ModTime().After(infoJ.ModTime())
	})

	// retain lastest package and logs
	for i := retainCount; i < len(upgradePackages); i++ {
		err := os.Remove(upgradePackages[i])
		if err != nil {
			return err
		}
	}
	for i := retainCount; i < len(upgradeLogs); i++ {
		err := os.RemoveAll(upgradeLogs[i])
		if err != nil {
			return err
		}
	}

	return nil
}

func matchLogsAndPackage(dirname string) (upgradePackages []string, upgradeLogs []string, err error) {

	entries, err := os.ReadDir(dirname)
	if err != nil {
		return
	}

	// 分离升级包和升级日志
	upgradePackagePattern := regexp.MustCompile(`upgrade_\d{8}_\d{6}\.log`)
	upgradeLogPattern := regexp.MustCompile(`\d{14}-exec-dir`)

	for _, entry := range entries {
		entryPath := filepath.Join(dirname, entry.Name())

		if upgradePackagePattern.MatchString(entry.Name()) {
			upgradePackages = append(upgradePackages, entryPath)
		} else if upgradeLogPattern.MatchString(entry.Name()) {
			upgradeLogs = append(upgradeLogs, entryPath)
		} else {
			continue
		}
	}
	return
}
