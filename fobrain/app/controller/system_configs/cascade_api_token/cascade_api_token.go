package cascade_api_token

import (
	"fobrain/fobrain/common/response"
	"fobrain/models/mysql/system_configs"
	"fobrain/pkg/utils"
	"github.com/gin-gonic/gin"
)

var key = "cascade_api_token"

func GetCascadeApiToken(c *gin.Context) error {
	value, err := system_configs.NewSystemConfigs().GetConfig(key)
	if err != nil {
		return response.FailWithMessage(c, "API Token获取失败")
	}
	return response.OkWithData(c, value)
}

func RefreshCascadeApiToken(c *gin.Context) error {
	token, err := utils.GenerateRandomToken(16)
	if err != nil {
		return response.FailWithMessage(c, "API Token创建失败")
	}
	err = system_configs.NewSystemConfigs().UpdateConfig(key, token)
	if err != nil {
		return response.FailWithMessage(c, "API Token刷新失败")
	}
	return response.OkWithData(c, token)
}
