package cascade_api_token

import (
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"net/http/httptest"
	"testing"
)

func TestGetCascadeApiToken(t *testing.T) {
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/settings/cascade_api_token", nil)

	c, _ := gin.CreateTestContext(w)
	c.Request = req

	err := GetCascadeApiToken(c)
	assert.Nil(t, err)
}

func TestRefreshCascadeApiToken(t *testing.T) {
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/settings/cascade_api_token/refresh", nil)

	c, _ := gin.CreateTestContext(w)
	c.Request = req

	err := RefreshCascadeApiToken(c)
	assert.Nil(t, err)
}
