package peoplepgid

import (
	"context"
	"fobrain/fobrain/common/response"
	"fobrain/fobrain/logs"
	"fobrain/initialize/redis"
	"fobrain/models/mysql/system_configs"
	redis_helper "fobrain/models/redis"

	"github.com/gin-gonic/gin"
)

var key = "people_pgid"

func GetPeoplePgid(c *gin.Context) error {
	value, err := system_configs.NewSystemConfigs().GetConfig(key)
	if err != nil {
		return response.FailWithMessage(c, "people_pgid获取失败")
	}
	return response.OkWithData(c, value)
}

func SetPeoplePgid(c *gin.Context) error {
	value := c.Params.ByName("value")
	if value == "" {
		return response.FailWithMessage(c, "people_pgid设置失败")
	}
	err := system_configs.NewSystemConfigs().UpdateConfig(key, value)
	if err != nil {
		return response.FailWithMessage(c, "people_pgid设置失败")
	}
	// 缓存到redis
	redisClient := redis.GetRedisClient()
	redisKey := redis_helper.GetPeoplePgidKey()
	err = redisClient.Set(context.Background(), redisKey, value, 0).Err()
	if err != nil {
		logs.GetLogger().Warnf("Error while setting people_pgid", "error", err)
	}
	return response.OkWithData(c, "people_pgid设置成功")
}
