package backup

import (
	"fmt"
	"fobrain/fobrain/app/services/backup"
	"fobrain/fobrain/common/request"
	"fobrain/fobrain/common/response"
	"fobrain/initialize/mysql"
	"fobrain/models/mysql/merge"
	"io"
	"net/http"
	"os"
	"strconv"

	"github.com/gin-gonic/gin"
)

// Middleware 添加中间件函数
func Middleware() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		backupProgress, _ := backup.GetProgress("backup")
		recoverProgress, _ := backup.GetProgress("recover")
		if recoverProgress != nil || backupProgress != nil {
			if ctx.FullPath() != "/api/v1/system/backup/export" &&
				ctx.FullPath() != "/api/v1/system/backup/upload" &&
				ctx.FullPath() != "/api/v1/system/backup/checkout" &&
				ctx.FullPath() != "/api/v1/user" &&
				ctx.FullPath() != "/api/v1/user_access/my_menus_tree" &&
				ctx.FullPath() != "/api/v1/system/backup/process" {
				status := map[string]string{
					"type": "backup",
				}
				if recoverProgress != nil {
					status["type"] = "recover"
				}
				response.FailWithDetailed(ctx, http.StatusLocked, status, "系统正在备份/恢复中，请稍后再试")
				ctx.Abort()
				return
			}
		}
		ctx.Next()
	}
}

func Backup(ctx *gin.Context) error {
	bm := backup.NewBackupManager()
	go bm.Backup()
	return response.Ok(ctx)
}

func Restore(ctx *gin.Context) error {
	file, err := ctx.FormFile("file")
	if err != nil {
		return fmt.Errorf("restore failed: %v", err)
	}

	// 打开上传的文件
	src, err := file.Open()
	if err != nil {
		return fmt.Errorf("failed to open uploaded file: %v", err)
	}
	defer src.Close()

	// 创建临时文件
	tempFile, err := os.CreateTemp("", "upload-*.tmp")
	if err != nil {
		return fmt.Errorf("failed to create temp file: %v", err)
	}
	defer func() {
		tempFile.Close()
	}()

	// 将上传的文件内容复制到临时文件中
	if _, err := io.Copy(tempFile, src); err != nil {
		return fmt.Errorf("failed to copy file content: %v", err)
	}

	// 获取临时文件的路径
	tempFilePath := tempFile.Name()

	// 调用备份管理器进行恢复
	bm := backup.NewBackupManager()
	go bm.Restore(tempFilePath)
	// 返回成功响应
	return response.Ok(ctx)
}

// Checkout
// @description 检查是否存在融合任务
func Checkout(ctx *gin.Context) error {
	count, err := merge.NewMergeRecordsModel().Count(mysql.WithColumnValue("task_status", merge.MergeRecordsStatusRunning))
	if err != nil {
		return err
	}
	data := map[string]interface{}{
		"exist_task": count > 0,
	}
	return response.OkWithData(ctx, data)
}

// GetProcess
// @description 获取进度
func GetProcess(ctx *gin.Context) error {
	params, err := request.Validate(ctx, &struct {
		Method string `json:"method" form:"method" uri:"method" validate:"oneof=backup recover" zh:"进度类型"`
	}{})
	if err != nil {
		return err
	}
	msg, err := backup.GetProgress(params.Method)
	if err != nil {
		return err
	}
	if msg == nil {
		return response.FailWithCodeMessage(ctx, 555, "")
	}
	process, _ := msg["percentage"]
	status, _ := msg["status"]
	message, _ := msg["message"]
	state, _ := strconv.Atoi(status)
	if state == backup.StatusFailed {
		err := backup.DeleteProgress(params.Method)
		if err != nil {
			return err
		}
		return response.FailWithMessage(ctx, "操作失败："+message)
	}
	progress, _ := strconv.Atoi(process)
	if progress >= 100 {
		//删除进度
		if params.Method == "recover" {
			err := backup.DeleteProgress(params.Method)
			if err != nil {
				return err
			}
		}
		progress = 100
	}
	return response.OkWithData(ctx, progress)
}

// Download 下载备份文件
func Download(ctx *gin.Context) error {
	msg, err := backup.GetProgress("backup")
	if err != nil {
		return err
	}
	if msg == nil {
		return response.FailWithMessage(ctx, "没有备份文件")
	}
	filePath, ok := msg["back_file_path"]
	if !ok || filePath == "" {
		return response.FailWithMessage(ctx, "没有备份文件")
	}
	err = backup.DeleteProgress("backup")
	if err != nil {
		return err
	}
	return response.OkWithFile(ctx, filePath, false)
}
