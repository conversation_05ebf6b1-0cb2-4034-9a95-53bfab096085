package config

import (
	"fobrain/fobrain/common/response"
	"fobrain/models/mysql/system_configs"
	"fobrain/pkg/utils"
	"regexp"
	"slices"
	"strings"

	"github.com/gin-gonic/gin"
)

// 黑名单，这些配置只能通过专门接口获取
var blackList = []string{"people_pgid", "network_areas", "retrieve_system_usage", "intranet_ips", "networks", "email", "cascade_api_token", "cascade_api_token/refresh", "sso_server", "sso_server/sso_server"}

type ConfigItem struct {
	Key   string `json:"key" form:"key"  zh:"配置key" `
	Value string `json:"value" form:"value" zh:"配置value" `
}

type ConfigResult struct {
	Key     string `json:"key"`
	Success bool   `json:"success"`
	Message string `json:"message"`
}

func GetSystemConfig(ctx *gin.Context) error {
	key := ctx.Query("key")
	if key == "" {
		return response.FailWithMessage(ctx, "key不能为空")
	}
	// 拆分并去重
	keys := strings.Split(key, ",")
	keys = utils.ListDistinctNonZero(keys)
	for i, key := range keys {
		// 可能存在越权问题，需要添加权限控制或者黑白名单
		if slices.Contains(blackList, key) {
			// 如果key在黑名单中，则删除该key
			keys = slices.Delete(keys, i, len(keys))
			continue
		}
	}
	if len(keys) == 0 {
		return response.FailWithMessage(ctx, "key不能为空")
	}
	// 批量获取配置
	values, err := system_configs.NewSystemConfigs().GetMultiConfig(keys)
	if err != nil {
		return response.FailWithMessage(ctx, "获取配置失败: "+err.Error())
	}
	return response.OkWithData(ctx, values)
}

func CreateOrUpdateSystemConfig(ctx *gin.Context) error {
	var params []ConfigItem
	if err := ctx.ShouldBindJSON(&params); err != nil {
		return response.FailWithMessage(ctx, "参数绑定失败: "+err.Error())
	}

	if len(params) == 0 {
		return response.FailWithMessage(ctx, "参数列表不能为空")
	}

	results := make([]ConfigResult, 0, len(params))

	for _, item := range params {
		currentResult := ConfigResult{Key: item.Key}
		key := item.Key
		value := item.Value

		if key == "" || value == "" {
			currentResult.Success = false
			currentResult.Message = "key和value不能为空"
			results = append(results, currentResult)
			continue
		}
		// 可能存在越权问题，需要添加权限控制或者黑白名单
		if slices.Contains(blackList, key) {
			currentResult.Success = false
			currentResult.Message = "请使用专门接口创建或更新配置"
			results = append(results, currentResult)
			continue
		}
		// 限制key的长度，不能超过30个字符
		if len(key) > 30 {
			currentResult.Success = false
			currentResult.Message = "key长度不能超过30个字符"
			results = append(results, currentResult)
			continue
		}
		// 限制key的格式，只能包含字母、数字、下划线
		if !regexp.MustCompile(`^[a-zA-Z0-9_]+$`).MatchString(key) {
			currentResult.Success = false
			currentResult.Message = "key只能包含字母、数字、下划线"
			results = append(results, currentResult)
			continue
		}
		// 获取当前key的配置
		oldValue, err := system_configs.NewSystemConfigs().GetConfig(key)
		if err != nil {
			currentResult.Success = false
			currentResult.Message = "获取配置失败: " + err.Error()
			results = append(results, currentResult)
			continue
		}
		if oldValue == "" {
			err = system_configs.NewSystemConfigs().CreateConfig(key, value)
			if err != nil {
				currentResult.Success = false
				currentResult.Message = "创建配置失败: " + err.Error()
			} else {
				currentResult.Success = true
				currentResult.Message = "创建成功"
			}
		} else {
			err = system_configs.NewSystemConfigs().UpdateConfig(key, value)
			if err != nil {
				currentResult.Success = false
				currentResult.Message = "更新配置失败: " + err.Error()
			} else {
				currentResult.Success = true
				currentResult.Message = "更新成功"
			}
		}
		results = append(results, currentResult)
	}
	return response.OkWithData(ctx, results)
}
