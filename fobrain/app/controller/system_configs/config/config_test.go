package config

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	testcommon "fobrain/fobrain/tests/common_test"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

// setupMockDB 设置mock数据库并返回testcommon.MockDb
func setupMockDB() *testcommon.MockDb {
	return testcommon.InitSqlMock()
}

// createTestGinContext 创建测试用的gin.Context
func createTestGinContext(method, url string, body []byte) (*gin.Context, *httptest.ResponseRecorder) {
	gin.SetMode(gin.TestMode)
	w := httptest.NewRecorder()
	ctx, _ := gin.CreateTestContext(w)

	var req *http.Request
	if body != nil {
		req = httptest.NewRequest(method, url, bytes.NewBuffer(body))
		req.Header.Set("Content-Type", "application/json")
	} else {
		req = httptest.NewRequest(method, url, nil)
	}

	ctx.Request = req
	return ctx, w
}

// createJSONRequest 创建JSON请求体
func createJSONRequest(data interface{}) []byte {
	jsonData, _ := json.Marshal(data)
	return jsonData
}

// TestGetSystemConfig_Success_SingleKey 测试单个key查询成功
func TestGetSystemConfig_Success_SingleKey(t *testing.T) {
	mockDb := setupMockDB()
	defer mockDb.Close()

	// 设置查询期望
	mockDb.ExpectQuery("SELECT * FROM `system_configs` WHERE `key` IN (?)").
		WithArgs(sqlmock.AnyArg()).
		WillReturnRows(sqlmock.NewRows([]string{"key", "value"}).AddRow("test_key", "test_value"))

	ctx, w := createTestGinContext("GET", "/config?key=test_key", nil)

	err := GetSystemConfig(ctx)
	assert.NoError(t, err)
	assert.Equal(t, 200, w.Code)

	var response map[string]interface{}
	json.Unmarshal(w.Body.Bytes(), &response)
	assert.Equal(t, float64(0), response["code"])

	data := response["data"].(map[string]interface{})
	assert.Equal(t, "test_value", data["test_key"])
}

// TestGetSystemConfig_Success_MultipleKeys 测试多个key查询成功
func TestGetSystemConfig_Success_MultipleKeys(t *testing.T) {
	mockDb := setupMockDB()
	defer mockDb.Close()

	// 设置查询期望
	mockDb.ExpectQuery("SELECT * FROM `system_configs` WHERE `key` IN (?,?)").
		WithArgs("key1", "key2").
		WillReturnRows(sqlmock.NewRows([]string{"key", "value"}).AddRow("key1", "value1").AddRow("key2", "value2"))

	ctx, w := createTestGinContext("GET", "/config?key=key1,key2", nil)

	err := GetSystemConfig(ctx)
	assert.NoError(t, err)
	assert.Equal(t, 200, w.Code)

	var response map[string]interface{}
	json.Unmarshal(w.Body.Bytes(), &response)
	assert.Equal(t, float64(0), response["code"])

	data := response["data"].(map[string]interface{})
	assert.Equal(t, "value1", data["key1"])
	assert.Equal(t, "value2", data["key2"])
}

// TestGetSystemConfig_EmptyKey 测试key为空的情况
func TestGetSystemConfig_EmptyKey(t *testing.T) {
	mockDb := setupMockDB()
	defer mockDb.Close()

	ctx, w := createTestGinContext("GET", "/config?key=", nil)

	err := GetSystemConfig(ctx)
	assert.NoError(t, err)
	assert.Equal(t, 400, w.Code)

	var response map[string]interface{}
	json.Unmarshal(w.Body.Bytes(), &response)
	assert.Equal(t, float64(400), response["code"])
	assert.Equal(t, "key不能为空", response["message"])
}

// TestGetSystemConfig_BlacklistKey 测试黑名单key的情况
func TestGetSystemConfig_BlacklistKey(t *testing.T) {
	mockDb := setupMockDB()
	defer mockDb.Close()

	ctx, w := createTestGinContext("GET", "/config?key=people_pgid", nil)

	err := GetSystemConfig(ctx)
	assert.NoError(t, err)
	assert.Equal(t, 400, w.Code)

	var response map[string]interface{}
	json.Unmarshal(w.Body.Bytes(), &response)
	assert.Equal(t, float64(400), response["code"])

	// 黑名单key应该被跳过，返回空的data
	assert.Equal(t, "key不能为空", response["message"])
}

// TestGetSystemConfig_DatabaseError 测试数据库查询错误
func TestGetSystemConfig_DatabaseError(t *testing.T) {
	mockDb := setupMockDB()
	defer mockDb.Close()

	// 模拟数据库错误，但函数应该continue而不是返回错误
	mockDb.ExpectQuery("SELECT * FROM `system_configs` WHERE `key` IN (?)").
		WithArgs(sqlmock.AnyArg()).
		WillReturnError(assert.AnError)

	ctx, w := createTestGinContext("GET", "/config?key=test_key", nil)

	err := GetSystemConfig(ctx)
	assert.NoError(t, err)
	assert.Equal(t, 400, w.Code)

	var response map[string]interface{}
	json.Unmarshal(w.Body.Bytes(), &response)
	assert.Equal(t, float64(400), response["code"])

	// 数据库错误的key应该被跳过，返回空的data
	data := response["data"].(map[string]interface{})
	assert.Empty(t, data)
}

// TestCreateOrUpdateSystemConfig_Success_Create 测试创建新配置成功
func TestCreateOrUpdateSystemConfig_Success_Create(t *testing.T) {
	mockDb := setupMockDB()
	defer mockDb.Close()

	// 设置查询期望 - GetConfig返回空，表示配置不存在
	mockDb.ExpectQuery("SELECT value FROM `system_configs` WHERE `key` = ?").
		WithArgs("new_key").
		WillReturnRows(sqlmock.NewRows([]string{"value"}).AddRow(""))

	// 设置创建期望
	mockDb.ExpectBegin()
	mockDb.ExpectExec("INSERT INTO `system_configs` (`created_at`,`updated_at`,`key`,`value`) VALUES (?,?,?,?)").
		WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), "new_key", "new_value").
		WillReturnResult(sqlmock.NewResult(1, 1))
	mockDb.ExpectCommit()

	requestData := []ConfigItem{
		{Key: "new_key", Value: "new_value"},
	}
	jsonData := createJSONRequest(requestData)
	ctx, w := createTestGinContext("POST", "/config", jsonData)

	err := CreateOrUpdateSystemConfig(ctx)
	assert.NoError(t, err)
	assert.Equal(t, 200, w.Code)

	var response map[string]interface{}
	json.Unmarshal(w.Body.Bytes(), &response)
	assert.Equal(t, float64(0), response["code"])

	data := response["data"].([]interface{})
	assert.Len(t, data, 1)
	result := data[0].(map[string]interface{})
	assert.Equal(t, "new_key", result["key"])
	assert.Equal(t, true, result["success"])
	assert.Equal(t, "创建成功", result["message"])
}

// TestCreateOrUpdateSystemConfig_Success_Update 测试更新已有配置成功
func TestCreateOrUpdateSystemConfig_Success_Update(t *testing.T) {
	mockDb := setupMockDB()
	defer mockDb.Close()

	// 设置查询期望 - GetConfig返回旧值，表示配置已存在
	mockDb.ExpectQuery("SELECT value FROM `system_configs` WHERE `key` = ?").
		WithArgs("existing_key").
		WillReturnRows(sqlmock.NewRows([]string{"value"}).AddRow("old_value"))

	// 设置FirstOrCreate期望
	mockDb.ExpectQuery("SELECT * FROM `system_configs` WHERE `key` = ? ORDER BY `system_configs`.`id` LIMIT 1").
		WithArgs("existing_key").
		WillReturnRows(sqlmock.NewRows([]string{"id", "key", "value"}).AddRow(1, "existing_key", "old_value"))

	// 设置更新期望
	mockDb.ExpectBegin()
	mockDb.ExpectExec("UPDATE `system_configs` SET `value`=? WHERE `key` = ?").
		WithArgs("updated_value", "existing_key").
		WillReturnResult(sqlmock.NewResult(0, 1))
	mockDb.ExpectCommit()

	requestData := []ConfigItem{
		{Key: "existing_key", Value: "updated_value"},
	}
	jsonData := createJSONRequest(requestData)
	ctx, w := createTestGinContext("POST", "/config", jsonData)

	err := CreateOrUpdateSystemConfig(ctx)
	assert.NoError(t, err)
	assert.Equal(t, 200, w.Code)

	var response map[string]interface{}
	json.Unmarshal(w.Body.Bytes(), &response)
	assert.Equal(t, float64(0), response["code"])

	data := response["data"].([]interface{})
	assert.Len(t, data, 1)
	result := data[0].(map[string]interface{})
	assert.Equal(t, "existing_key", result["key"])
	assert.Equal(t, true, result["success"])
	assert.Equal(t, "更新成功", result["message"])
}

// TestCreateOrUpdateSystemConfig_Success_Batch 测试批量处理成功
func TestCreateOrUpdateSystemConfig_Success_Batch(t *testing.T) {
	mockDb := setupMockDB()
	defer mockDb.Close()

	// 第一个配置 - 创建新配置
	mockDb.ExpectQuery("SELECT value FROM `system_configs` WHERE `key` = ?").
		WithArgs("key1").
		WillReturnRows(sqlmock.NewRows([]string{"value"}).AddRow(""))
	mockDb.ExpectBegin()
	mockDb.ExpectExec("INSERT INTO `system_configs` (`created_at`,`updated_at`,`key`,`value`) VALUES (?,?,?,?)").
		WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), "key1", "value1").
		WillReturnResult(sqlmock.NewResult(1, 1))
	mockDb.ExpectCommit()

	// 第二个配置 - 更新已有配置
	mockDb.ExpectQuery("SELECT value FROM `system_configs` WHERE `key` = ?").
		WithArgs("key2").
		WillReturnRows(sqlmock.NewRows([]string{"value"}).AddRow("old_value2"))
	mockDb.ExpectQuery("SELECT * FROM `system_configs` WHERE `key` = ? ORDER BY `system_configs`.`id` LIMIT 1").
		WithArgs("key2").
		WillReturnRows(sqlmock.NewRows([]string{"id", "key", "value"}).AddRow(2, "key2", "old_value2"))
	mockDb.ExpectBegin()
	mockDb.ExpectExec("UPDATE `system_configs` SET `value`=? WHERE `key` = ?").
		WithArgs("value2", "key2").
		WillReturnResult(sqlmock.NewResult(0, 1))
	mockDb.ExpectCommit()

	requestData := []ConfigItem{
		{Key: "key1", Value: "value1"},
		{Key: "key2", Value: "value2"},
	}
	jsonData := createJSONRequest(requestData)
	ctx, w := createTestGinContext("POST", "/config", jsonData)

	err := CreateOrUpdateSystemConfig(ctx)
	assert.NoError(t, err)
	assert.Equal(t, 200, w.Code)

	var response map[string]interface{}
	json.Unmarshal(w.Body.Bytes(), &response)
	assert.Equal(t, float64(0), response["code"])

	data := response["data"].([]interface{})
	assert.Len(t, data, 2)

	result1 := data[0].(map[string]interface{})
	assert.Equal(t, "key1", result1["key"])
	assert.Equal(t, true, result1["success"])
	assert.Equal(t, "创建成功", result1["message"])

	result2 := data[1].(map[string]interface{})
	assert.Equal(t, "key2", result2["key"])
	assert.Equal(t, true, result2["success"])
	assert.Equal(t, "更新成功", result2["message"])
}

// TestCreateOrUpdateSystemConfig_EmptyParams 测试参数列表为空
func TestCreateOrUpdateSystemConfig_EmptyParams(t *testing.T) {
	mockDb := setupMockDB()
	defer mockDb.Close()

	requestData := []ConfigItem{}
	jsonData := createJSONRequest(requestData)
	ctx, w := createTestGinContext("POST", "/config", jsonData)

	err := CreateOrUpdateSystemConfig(ctx)
	assert.NoError(t, err)
	assert.Equal(t, 400, w.Code)

	var response map[string]interface{}
	json.Unmarshal(w.Body.Bytes(), &response)
	assert.Equal(t, float64(400), response["code"])
	assert.Equal(t, "参数列表不能为空", response["message"])
}

// TestCreateOrUpdateSystemConfig_InvalidJSON 测试JSON绑定失败
func TestCreateOrUpdateSystemConfig_InvalidJSON(t *testing.T) {
	mockDb := setupMockDB()
	defer mockDb.Close()

	invalidJSON := []byte(`{"invalid": json}`)
	ctx, w := createTestGinContext("POST", "/config", invalidJSON)

	err := CreateOrUpdateSystemConfig(ctx)
	assert.NoError(t, err)
	assert.Equal(t, 400, w.Code)

	var response map[string]interface{}
	json.Unmarshal(w.Body.Bytes(), &response)
	assert.Equal(t, float64(400), response["code"])
	assert.Contains(t, response["message"], "参数绑定失败")
}

// TestCreateOrUpdateSystemConfig_EmptyKeyValue 测试key或value为空
func TestCreateOrUpdateSystemConfig_EmptyKeyValue(t *testing.T) {
	mockDb := setupMockDB()
	defer mockDb.Close()

	requestData := []ConfigItem{
		{Key: "", Value: "value"},
		{Key: "key", Value: ""},
	}
	jsonData := createJSONRequest(requestData)
	ctx, w := createTestGinContext("POST", "/config", jsonData)

	err := CreateOrUpdateSystemConfig(ctx)
	assert.NoError(t, err)
	assert.Equal(t, 200, w.Code)

	var response map[string]interface{}
	json.Unmarshal(w.Body.Bytes(), &response)
	assert.Equal(t, float64(0), response["code"])

	data := response["data"].([]interface{})
	assert.Len(t, data, 2)

	// 两个配置都应该失败
	result1 := data[0].(map[string]interface{})
	assert.Equal(t, "", result1["key"])
	assert.Equal(t, false, result1["success"])
	assert.Equal(t, "key和value不能为空", result1["message"])

	result2 := data[1].(map[string]interface{})
	assert.Equal(t, "key", result2["key"])
	assert.Equal(t, false, result2["success"])
	assert.Equal(t, "key和value不能为空", result2["message"])
}

// TestCreateOrUpdateSystemConfig_BlacklistKey 测试黑名单key
func TestCreateOrUpdateSystemConfig_BlacklistKey(t *testing.T) {
	mockDb := setupMockDB()
	defer mockDb.Close()

	requestData := []ConfigItem{
		{Key: "people_pgid", Value: "test_value"},
	}
	jsonData := createJSONRequest(requestData)
	ctx, w := createTestGinContext("POST", "/config", jsonData)

	err := CreateOrUpdateSystemConfig(ctx)
	assert.NoError(t, err)
	assert.Equal(t, 200, w.Code)

	var response map[string]interface{}
	json.Unmarshal(w.Body.Bytes(), &response)
	assert.Equal(t, float64(0), response["code"])

	data := response["data"].([]interface{})
	assert.Len(t, data, 1)
	result := data[0].(map[string]interface{})
	assert.Equal(t, "people_pgid", result["key"])
	assert.Equal(t, false, result["success"])
	assert.Equal(t, "请使用专门接口创建或更新配置", result["message"])
}

// TestCreateOrUpdateSystemConfig_KeyTooLong 测试key长度超限
func TestCreateOrUpdateSystemConfig_KeyTooLong(t *testing.T) {
	mockDb := setupMockDB()
	defer mockDb.Close()

	longKey := "this_is_a_very_long_key_that_exceeds_thirty_characters_limit"
	requestData := []ConfigItem{
		{Key: longKey, Value: "test_value"},
	}
	jsonData := createJSONRequest(requestData)
	ctx, w := createTestGinContext("POST", "/config", jsonData)

	err := CreateOrUpdateSystemConfig(ctx)
	assert.NoError(t, err)
	assert.Equal(t, 200, w.Code)

	var response map[string]interface{}
	json.Unmarshal(w.Body.Bytes(), &response)
	assert.Equal(t, float64(0), response["code"])

	data := response["data"].([]interface{})
	assert.Len(t, data, 1)
	result := data[0].(map[string]interface{})
	assert.Equal(t, longKey, result["key"])
	assert.Equal(t, false, result["success"])
	assert.Equal(t, "key长度不能超过30个字符", result["message"])
}

// TestCreateOrUpdateSystemConfig_InvalidKeyFormat 测试key格式错误
func TestCreateOrUpdateSystemConfig_InvalidKeyFormat(t *testing.T) {
	mockDb := setupMockDB()
	defer mockDb.Close()

	invalidKeys := []string{"key-with-dash", "key with space", "key@special"}
	var requestData []ConfigItem
	for _, key := range invalidKeys {
		requestData = append(requestData, ConfigItem{Key: key, Value: "test_value"})
	}

	jsonData := createJSONRequest(requestData)
	ctx, w := createTestGinContext("POST", "/config", jsonData)

	err := CreateOrUpdateSystemConfig(ctx)
	assert.NoError(t, err)
	assert.Equal(t, 200, w.Code)

	var response map[string]interface{}
	json.Unmarshal(w.Body.Bytes(), &response)
	assert.Equal(t, float64(0), response["code"])

	data := response["data"].([]interface{})
	assert.Len(t, data, 3)

	// 所有无效格式的key都应该失败
	for i, invalidKey := range invalidKeys {
		result := data[i].(map[string]interface{})
		assert.Equal(t, invalidKey, result["key"])
		assert.Equal(t, false, result["success"])
		assert.Equal(t, "key只能包含字母、数字、下划线", result["message"])
	}
}

// TestCreateOrUpdateSystemConfig_GetConfigError 测试GetConfig失败
func TestCreateOrUpdateSystemConfig_GetConfigError(t *testing.T) {
	mockDb := setupMockDB()
	defer mockDb.Close()

	// 模拟GetConfig查询失败
	mockDb.ExpectQuery("SELECT value FROM `system_configs` WHERE `key` = ?").
		WithArgs("test_key").
		WillReturnError(assert.AnError)

	requestData := []ConfigItem{
		{Key: "test_key", Value: "test_value"},
	}
	jsonData := createJSONRequest(requestData)
	ctx, w := createTestGinContext("POST", "/config", jsonData)

	err := CreateOrUpdateSystemConfig(ctx)
	assert.NoError(t, err)
	assert.Equal(t, 200, w.Code)

	var response map[string]interface{}
	json.Unmarshal(w.Body.Bytes(), &response)
	assert.Equal(t, float64(0), response["code"])

	data := response["data"].([]interface{})
	assert.Len(t, data, 1)
	result := data[0].(map[string]interface{})
	assert.Equal(t, "test_key", result["key"])
	assert.Equal(t, false, result["success"])
	assert.Contains(t, result["message"], "获取配置失败")
}

// TestCreateOrUpdateSystemConfig_CreateConfigError 测试CreateConfig失败
func TestCreateOrUpdateSystemConfig_CreateConfigError(t *testing.T) {
	mockDb := setupMockDB()
	defer mockDb.Close()

	// GetConfig返回空，表示需要创建
	mockDb.ExpectQuery("SELECT value FROM `system_configs` WHERE `key` = ?").
		WithArgs("new_key").
		WillReturnRows(sqlmock.NewRows([]string{"value"}).AddRow(""))

	// CreateConfig失败
	mockDb.ExpectBegin()
	mockDb.ExpectExec("INSERT INTO `system_configs` (`created_at`,`updated_at`,`key`,`value`) VALUES (?,?,?,?)").
		WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), "new_key", "new_value").
		WillReturnError(assert.AnError)
	mockDb.ExpectRollback()

	requestData := []ConfigItem{
		{Key: "new_key", Value: "new_value"},
	}
	jsonData := createJSONRequest(requestData)
	ctx, w := createTestGinContext("POST", "/config", jsonData)

	err := CreateOrUpdateSystemConfig(ctx)
	assert.NoError(t, err)
	assert.Equal(t, 200, w.Code)

	var response map[string]interface{}
	json.Unmarshal(w.Body.Bytes(), &response)
	assert.Equal(t, float64(0), response["code"])

	data := response["data"].([]interface{})
	assert.Len(t, data, 1)
	result := data[0].(map[string]interface{})
	assert.Equal(t, "new_key", result["key"])
	assert.Equal(t, false, result["success"])
	assert.Contains(t, result["message"], "创建配置失败")
}

// TestCreateOrUpdateSystemConfig_UpdateConfigError 测试UpdateConfig失败
func TestCreateOrUpdateSystemConfig_UpdateConfigError(t *testing.T) {
	mockDb := setupMockDB()
	defer mockDb.Close()

	// GetConfig返回旧值，表示需要更新
	mockDb.ExpectQuery("SELECT value FROM `system_configs` WHERE `key` = ?").
		WithArgs("existing_key").
		WillReturnRows(sqlmock.NewRows([]string{"value"}).AddRow("old_value"))

	// FirstOrCreate查询
	mockDb.ExpectQuery("SELECT * FROM `system_configs` WHERE `key` = ? ORDER BY `system_configs`.`id` LIMIT 1").
		WithArgs("existing_key").
		WillReturnRows(sqlmock.NewRows([]string{"id", "key", "value"}).AddRow(1, "existing_key", "old_value"))

	// UpdateConfig失败
	mockDb.ExpectBegin()
	mockDb.ExpectExec("UPDATE `system_configs` SET `value`=? WHERE `key` = ?").
		WithArgs("updated_value", "existing_key").
		WillReturnError(assert.AnError)
	mockDb.ExpectRollback()

	requestData := []ConfigItem{
		{Key: "existing_key", Value: "updated_value"},
	}
	jsonData := createJSONRequest(requestData)
	ctx, w := createTestGinContext("POST", "/config", jsonData)

	err := CreateOrUpdateSystemConfig(ctx)
	assert.NoError(t, err)
	assert.Equal(t, 200, w.Code)

	var response map[string]interface{}
	json.Unmarshal(w.Body.Bytes(), &response)
	assert.Equal(t, float64(0), response["code"])

	data := response["data"].([]interface{})
	assert.Len(t, data, 1)
	result := data[0].(map[string]interface{})
	assert.Equal(t, "existing_key", result["key"])
	assert.Equal(t, false, result["success"])
	assert.Contains(t, result["message"], "更新配置失败")
}

// TestCreateOrUpdateSystemConfig_MixedResults 测试混合成功失败结果
func TestCreateOrUpdateSystemConfig_MixedResults(t *testing.T) {
	mockDb := setupMockDB()
	defer mockDb.Close()

	// 第一个配置：成功创建
	mockDb.ExpectQuery("SELECT value FROM `system_configs` WHERE `key` = ?").
		WithArgs("success_key").
		WillReturnRows(sqlmock.NewRows([]string{"value"}).AddRow(""))
	mockDb.ExpectBegin()
	mockDb.ExpectExec("INSERT INTO `system_configs` (`created_at`,`updated_at`,`key`,`value`) VALUES (?,?,?,?)").
		WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), "success_key", "success_value").
		WillReturnResult(sqlmock.NewResult(1, 1))
	mockDb.ExpectCommit()

	// 第二个配置：黑名单key失败（不需要数据库操作）

	// 第三个配置：GetConfig失败
	mockDb.ExpectQuery("SELECT value FROM `system_configs` WHERE `key` = ?").
		WithArgs("error_key").
		WillReturnError(assert.AnError)

	requestData := []ConfigItem{
		{Key: "success_key", Value: "success_value"},
		{Key: "people_pgid", Value: "blacklist_value"}, // 黑名单key
		{Key: "error_key", Value: "error_value"},
	}
	jsonData := createJSONRequest(requestData)
	ctx, w := createTestGinContext("POST", "/config", jsonData)

	err := CreateOrUpdateSystemConfig(ctx)
	assert.NoError(t, err)
	assert.Equal(t, 200, w.Code)

	var response map[string]interface{}
	json.Unmarshal(w.Body.Bytes(), &response)
	assert.Equal(t, float64(0), response["code"])

	data := response["data"].([]interface{})
	assert.Len(t, data, 3)

	// 第一个应该成功
	result1 := data[0].(map[string]interface{})
	assert.Equal(t, "success_key", result1["key"])
	assert.Equal(t, true, result1["success"])
	assert.Equal(t, "创建成功", result1["message"])

	// 第二个应该失败（黑名单）
	result2 := data[1].(map[string]interface{})
	assert.Equal(t, "people_pgid", result2["key"])
	assert.Equal(t, false, result2["success"])
	assert.Equal(t, "请使用专门接口创建或更新配置", result2["message"])

	// 第三个应该失败（数据库错误）
	result3 := data[2].(map[string]interface{})
	assert.Equal(t, "error_key", result3["key"])
	assert.Equal(t, false, result3["success"])
	assert.Contains(t, result3["message"], "获取配置失败")
}
