package ntp

import (
	"fobrain/fobrain/app/request/system_config"
	"fobrain/fobrain/app/services/system_configs/ntp"
	"fobrain/fobrain/common/request"
	"fobrain/fobrain/common/response"

	"github.com/gin-gonic/gin"
)

// UpdateNtpConfig
// @Summary 更新NTP配置
// @Description 更新NTP配置
// @Tags 系统配置
// @Accept json
// @Produce json
// @Param data body system_config.NtpConfigReq true "NTP配置"
// @Success 200 {object} response.Response{data=system_config.NtpConfigReq}
// @Router /api/v1/ntp [post]
func UpdateNtpConfig(c *gin.Context) error {
	param, err := request.Validate(c, &system_config.NtpConfigReq{})
	if err != nil {
		return err
	}
	// 更新配置NTP配置
	err = ntp.UpdateOrCreateNTPConfig(param)
	if err != nil {
		return err
	}
	return response.Ok(c)
}

// GetNtpConfig
// @Summary 获取NTP配置
// @Description 获取NTP配置
// @Tags 系统配置
// @Accept json
// @Produce json
// @Success 200 {object} response.Response{data=system_config.NtpConfigReq}
// @Router /api/v1/ntp [get]
// 获取NTP配置
func GetNtpConfig(c *gin.Context) error {
	// 获取NTP配置
	ntpConfig, err := ntp.GetNTPConfig()
	if err != nil {
		return err
	}
	return response.OkWithData(c, ntpConfig)
}
