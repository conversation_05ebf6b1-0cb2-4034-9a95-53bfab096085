package systemInfo

import (
	"net/http"
	"strings"
	"time"

	license2 "fobrain/fobrain/app/repository/settings/license"
	"fobrain/fobrain/app/services/clear_data"
	"fobrain/fobrain/app/services/system_info"
	"fobrain/fobrain/common/license"
	"fobrain/fobrain/common/response"
	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/models/mysql/system_configs"
	"fobrain/pkg/cfg"
	"fobrain/pkg/utils"

	"github.com/gin-gonic/gin"
	"go-micro.dev/v4/logger"
)

// RetrieveSystemUsage 系统运行信息
func RetrieveSystemUsage(c *gin.Context) error {
	systemUsage, err := utils.GetSystemUsage()
	if err != nil {
		logger.Errorf("[ERROR] RetrieveSystemUsage 执行失败 %v\n", err)
		return response.FailWithMessage(c, "查询出错")
	}
	return response.OkWithData(c, systemUsage)
}

func SaveSystemInfoConfig(ctx *gin.Context) error {
	var body system_configs.SystemInfoConfig
	if err := ctx.ShouldBindJSON(&body); err != nil {
		logger.Errorf("[ERROR] SaveSystemInfoConfig 解析失败: %v\n", err)
		return response.FailWithMessage(ctx, "解析请求失败")
	}
	if err := system_configs.NewSystemConfigs().UpdateSystemInfoConfig(&body); err != nil {
		logger.Errorf("[ERROR] SaveSystemInfoConfig save failed: %v\n", err)
		return response.FailWithCodeMessage(ctx, http.StatusInternalServerError, err.Error())
	}
	return response.Ok(ctx)
}

func ListSystemInfoConfig(ctx *gin.Context) error {
	conf, err := system_configs.NewSystemConfigs().GetSystemInfoConfig()
	if err != nil {
		logger.Errorf("[ERROR] 获取系统信息配置失败: %v\n", err)
		return response.FailWithMessage(ctx, "获取系统信息配置失败")
	}
	return response.OkWithData(ctx, conf)
}

func HiddenMenu(ctx *gin.Context) error {
	//人行定制化资产映射模块 前端路由 AssetRelevance 其他项目需要在配置中隐藏
	hiddenMenuConfig := cfg.LoadCommon().HiddenMenu
	hiddenMenu := []string{}
	if hiddenMenuConfig != "" {
		hiddenMenu = strings.Split(hiddenMenuConfig, ",")
	}

	// 获取产品授权模块
	isTest := testcommon.IsTest()
	isDev := license2.CheckDevModel(cfg.LoadCommon().Env)
	// 测试环境或开发环境不获取产品授权模块
	if isTest || isDev {
		return response.OkWithData(ctx, hiddenMenuConfig)
	}
	license := license.GetLicense()
	models, err := license.GetProductModels()
	if err != nil {
		logger.Errorf("获取产品授权模块失败: %v\n", err)
		return response.FailWithMessage(ctx, "获取产品授权模块失败")
	}
	logger.Infof("产品授权模块: %v\n", models)
	// 根据产品授权模块和环境获取隐藏菜单
	hiddenMenuByLicense, err := system_info.GetMenusByLicense(models, isDev, isTest)
	if err != nil {
		logger.Errorf("获取隐藏菜单失败: %v\n", err)
		return response.FailWithMessage(ctx, "获取隐藏菜单失败")
	}
	// 合并隐藏菜单
	hiddenMenu = append(hiddenMenu, hiddenMenuByLicense...)
	return response.OkWithData(ctx, strings.Join(hiddenMenu, ","))
}

func Menus(ctx *gin.Context) error {
	// 获取产品授权模块
	isLocal := cfg.LoadCommon().Local

	isTest := testcommon.IsTest()
	isDev := license2.CheckDevModel(cfg.LoadCommon().Env)
	// 测试环境或开发环境不获取产品授权模块
	if (isTest || isDev) && isLocal {
		return response.OkWithData(ctx, " ")
	}
	license := license.GetLicense()
	models, err := license.GetProductModels()
	if err != nil {
		logger.Errorf("获取产品授权模块失败: %v\n", err)
		return response.FailWithMessage(ctx, "获取产品授权模块失败")
	}
	logger.Infof("产品授权模块: %v\n", models)
	// local 具有最高优先级
	if isLocal {
		isDev = false
		isTest = false
	}
	// 根据产品授权模块和环境获取隐藏菜单
	menuByLicense, err := system_info.GetMenusByLicense(models, isDev, isTest)
	if err != nil {
		logger.Errorf("获取隐藏菜单失败: %v\n", err)
		return response.FailWithMessage(ctx, "获取隐藏菜单失败")
	}
	return response.OkWithData(ctx, strings.Join(menuByLicense, ","))
}

func ClearData(ctx *gin.Context) error {
	isSuper := ctx.GetBool("is_super_manage")
	if !isSuper {
		return response.FailWithMessage(ctx, "无权限操作")
	}
	err := clear_data.ClearDataWithTimeout(10 * time.Minute)
	time.Sleep(15 * time.Second)
	if err != nil {
		logger.Errorf("清除数据失败: %v\n", err)
		return response.FailWithMessage(ctx, "清除数据失败")
	}
	return response.Ok(ctx)
}
