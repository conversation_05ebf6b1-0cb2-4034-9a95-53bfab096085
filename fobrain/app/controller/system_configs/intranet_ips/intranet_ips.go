package intranet_ips

import (
	"github.com/gin-gonic/gin"

	"fobrain/fobrain/app/services/system_configs/intranet_ips"
	"fobrain/fobrain/common/request"
	"fobrain/fobrain/common/response"
	"fobrain/pkg/utils"
)

type (
	IntranetIpListResp struct {
		OtherConfigIntranetIps []string `json:"other_config_intranet_ips" form:"other_config_intranet_ips" uri:"other_config_intranet_ips" validate:"required" zh:"其他配置内网IP"`
		OtherConfigSutraUrl    string   `json:"other_config_sutra_url" form:"other_config_sutra_url" uri:"other_config_sutra_url" validate:"required" zh:"其他配置Sutra URL"`
	}
	IntranetIpListUpdateReq struct {
		OtherConfigIntranetIps []string `json:"other_config_intranet_ips" form:"other_config_intranet_ips" uri:"other_config_intranet_ips" validate:"required" zh:"其他配置内网IP"`
	}
)

// IntranetIpList 返回内网网段列表
func IntranetIpList(c *gin.Context) error {

	IPList, err := intranet_ips.IntranetIPList()

	if err != nil {
		return err
	}

	return response.OkWithData(c, IntranetIpListResp{
		OtherConfigIntranetIps: IPList,
		OtherConfigSutraUrl:    "",
	})
}

// IntranetIpListUpdate 修改内网网段列表
func IntranetIpListUpdate(c *gin.Context) error {
	params, err := request.Validate(c, &IntranetIpListUpdateReq{})
	if err != nil {
		return err
	}

	err = utils.CheckIpListFormat(params.OtherConfigIntranetIps)

	err = intranet_ips.IntranetIPListUpdate(params.OtherConfigIntranetIps)
	if err != nil {
		return err
	}
	return response.OkWithData(c, gin.H{})
}
