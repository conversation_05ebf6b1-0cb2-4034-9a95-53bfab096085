package system_configs

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"fobrain/fobrain/app/services/backup"
	testcommon "fobrain/fobrain/tests/common_test"

	"git.gobies.org/fobrain/unibackup/pkg/types"
	"github.com/DATA-DOG/go-sqlmock"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func TestGetBackupConfig(t *testing.T) {
	gin.SetMode(gin.TestMode)
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()

	// Mock配置查询
	mockDb.ExpectQuery("SELECT * FROM `system_configs` WHERE `key` IN (?,?,?,?,?,?,?,?,?,?,?,?,?)").
		WithArgs(backup.BackupEnabledKey, backup.BackupTypeKey, backup.BackupFullIntervalDaysKey, backup.BackupIncrIntervalHoursKey, backup.BackupRetentionDays<PERSON>ey, sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg()).
		WillReturnRows(mockDb.NewRows([]string{"key", "value"}).
			AddRow(backup.BackupEnabledKey, "true").
			AddRow(backup.BackupTypeKey, "local").
			AddRow(backup.BackupFullIntervalDaysKey, "7").
			AddRow(backup.BackupIncrIntervalHoursKey, "6").
			AddRow(backup.BackupRetentionDaysKey, "30"))

	router := gin.New()
	router.GET("/backup/config", func(c *gin.Context) {
		err := GetBackupConfig(c)
		if err != nil {
			c.JSON(500, gin.H{"error": err.Error()})
		}
	})

	req := httptest.NewRequest("GET", "/backup/config", nil)
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, float64(0), response["code"])
}

func TestGetBackupConfig_Error(t *testing.T) {
	gin.SetMode(gin.TestMode)
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()

	// Mock配置查询失败
	mockDb.ExpectQuery("SELECT * FROM `system_configs` WHERE `key` IN (?,?,?,?,?,?,?,?,?,?,?,?,?)").
		WithArgs(backup.BackupEnabledKey, backup.BackupTypeKey, backup.BackupFullIntervalDaysKey, backup.BackupIncrIntervalHoursKey, backup.BackupRetentionDaysKey, sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg()).
		WillReturnError(sqlmock.ErrCancelled)

	router := gin.New()
	router.GET("/backup/config", func(c *gin.Context) {
		err := GetBackupConfig(c)
		if err != nil {
			c.JSON(500, gin.H{"error": err.Error()})
		}
	})

	req := httptest.NewRequest("GET", "/backup/config", nil)
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusBadRequest, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Contains(t, response["message"], "获取备份配置失败")
}

func TestUpdateBackupConfig(t *testing.T) {
	gin.SetMode(gin.TestMode)
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()

	// 准备请求数据
	config := backupConfig{
		Type:              stringPtr("local"),
		FullIntervalDays:  intPtr(7),
		IncrIntervalHours: intPtr(6),
		RetentionDays:     intPtr(30),
	}

	jsonData, _ := json.Marshal(config)

	// Mock the SetMultiConfig operation - each UpdateConfig call does FirstOrCreate
	// For each config key, we need to mock the FirstOrCreate operation
	configKeys := []string{
		backup.BackupEnabledKey,
		backup.BackupTypeKey,
		backup.BackupFullIntervalDaysKey,
		backup.BackupIncrIntervalHoursKey,
		backup.BackupRetentionDaysKey,
	}

	for i := 0; i < len(configKeys); i++ {
		// Mock the SELECT part of FirstOrCreate (record doesn't exist)
		mockDb.ExpectQuery("SELECT * FROM `system_configs` WHERE `key` = ? ORDER BY `system_configs`.`id` LIMIT 1").
			WithArgs(sqlmock.AnyArg()).
			WillReturnRows(mockDb.NewRows([]string{"id", "created_at", "updated_at", "key", "value"}))

		// Mock the INSERT part of FirstOrCreate
		mockDb.ExpectBegin()
		mockDb.ExpectExec("INSERT INTO `system_configs`").
			WillReturnResult(sqlmock.NewResult(1, 1))
		mockDb.ExpectCommit()
	}
	// Mock backup service's IN query for IsEnabled, etc.
	mockDb.ExpectQuery("SELECT \\* FROM `system_configs` WHERE `key` IN \\(\\?\\,\\?\\,\\?\\,\\?\\,\\?\\,\\?\\,\\?\\,\\?\\,\\?\\,\\?\\,\\?\\,\\?\\,\\?\\)").
		WithArgs(
			backup.BackupEnabledKey, backup.BackupTypeKey, backup.BackupFullIntervalDaysKey, backup.BackupIncrIntervalHoursKey, backup.BackupRetentionDaysKey,
			"backup_last_full_time", "backup_last_incr_time", "cloud_storage_type", "cloud_storage_endpoint", "cloud_storage_bucket", "cloud_storage_access_key", "cloud_storage_secret_key", "cloud_storage_region",
		).
		WillReturnRows(mockDb.NewRows([]string{"key", "value"}).AddRow(backup.BackupEnabledKey, "false"))

	router := gin.New()
	router.POST("/backup/config", func(c *gin.Context) {
		err := UpdateBackupConfig(c)
		if err != nil {
			c.JSON(500, gin.H{"error": err.Error()})
		}
	})

	req := httptest.NewRequest("POST", "/backup/config", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, float64(0), response["code"])
}

func TestUpdateBackupConfig_ValidationError(t *testing.T) {
	gin.SetMode(gin.TestMode)

	// 准备无效请求数据
	invalidConfig := map[string]interface{}{
		"type":               "invalid_type", // 无效类型
		"full_interval_days": -1,             // 无效值
	}

	jsonData, _ := json.Marshal(invalidConfig)

	router := gin.New()
	router.POST("/backup/config", func(c *gin.Context) {
		err := UpdateBackupConfig(c)
		if err != nil {
			c.JSON(500, gin.H{"error": err.Error()})
		}
	})

	req := httptest.NewRequest("POST", "/backup/config", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusInternalServerError, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Contains(t, response["error"], "")
}

func TestGetLockStatus(t *testing.T) {
	gin.SetMode(gin.TestMode)

	router := gin.New()
	router.GET("/backup/lock/status", func(c *gin.Context) {
		err := GetLockStatus(c)
		if err != nil {
			c.JSON(500, gin.H{"error": err.Error()})
		}
	})

	req := httptest.NewRequest("GET", "/backup/lock/status", nil)
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, float64(0), response["code"])

	// 检查返回的数据结构
	data := response["data"].(map[string]interface{})
	assert.Contains(t, data, "is_locked")
	assert.Contains(t, data, "lock_reason")
	assert.Contains(t, data, "running_tasks")
}

func TestManualFullBackup_ServiceNotEnabled(t *testing.T) {
	gin.SetMode(gin.TestMode)
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()

	// Mock备份服务未启用
	mockDb.ExpectQuery("SELECT * FROM `system_configs` WHERE `key` IN (?,?,?,?,?,?,?,?,?,?,?,?,?)").
		WithArgs(backup.BackupEnabledKey, backup.BackupTypeKey, backup.BackupFullIntervalDaysKey, backup.BackupIncrIntervalHoursKey, backup.BackupRetentionDaysKey, sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg()).
		WillReturnRows(mockDb.NewRows([]string{"key", "value"}).
			AddRow(backup.BackupEnabledKey, "false"))

	router := gin.New()
	router.POST("/backup/manual/full", func(c *gin.Context) {
		err := ManualFullBackup(c)
		if err != nil {
			c.JSON(500, gin.H{"error": err.Error()})
		}
	})

	req := httptest.NewRequest("POST", "/backup/manual/full", nil)
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusBadRequest, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Contains(t, response["message"], "备份服务未启用")
}

func TestManualIncrBackup_ServiceNotEnabled(t *testing.T) {
	gin.SetMode(gin.TestMode)
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()

	// Mock备份服务未启用
	mockDb.ExpectQuery("SELECT * FROM `system_configs` WHERE `key` IN (?,?,?,?,?,?,?,?,?,?,?,?,?)").
		WithArgs(backup.BackupEnabledKey, backup.BackupTypeKey, backup.BackupFullIntervalDaysKey, backup.BackupIncrIntervalHoursKey, backup.BackupRetentionDaysKey, sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg()).
		WillReturnRows(mockDb.NewRows([]string{"key", "value"}).
			AddRow(backup.BackupEnabledKey, "false"))

	router := gin.New()
	router.POST("/backup/manual/incr", func(c *gin.Context) {
		err := ManualIncrBackup(c)
		if err != nil {
			c.JSON(500, gin.H{"error": err.Error()})
		}
	})

	req := httptest.NewRequest("POST", "/backup/manual/incr", nil)
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusBadRequest, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Contains(t, response["message"], "备份服务未启用")
}

func TestListBackups_ManagerNotAvailable(t *testing.T) {
	gin.SetMode(gin.TestMode)
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()

	// Mock backup service's query for IsEnabled check
	mockDb.ExpectQuery("SELECT \\* FROM `system_configs` WHERE `key` IN \\(\\?\\,\\?\\,\\?\\,\\?\\,\\?\\,\\?\\,\\?\\,\\?\\,\\?\\,\\?\\,\\?\\,\\?\\,\\?\\)").
		WithArgs(backup.BackupEnabledKey, backup.BackupTypeKey, backup.BackupFullIntervalDaysKey, backup.BackupIncrIntervalHoursKey, backup.BackupRetentionDaysKey, sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg()).
		WillReturnRows(mockDb.NewRows([]string{"key", "value"}).
			AddRow(backup.BackupEnabledKey, "false"))

	router := gin.New()
	router.GET("/backup/list", func(c *gin.Context) {
		err := ListBackups(c)
		if err != nil {
			c.JSON(500, gin.H{"error": err.Error()})
		}
	})

	req := httptest.NewRequest("GET", "/backup/list", nil)
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusBadRequest, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Contains(t, response["message"], "备份服务未启用")
}

func TestGetTaskStatus_EmptyID(t *testing.T) {
	gin.SetMode(gin.TestMode)

	router := gin.New()
	router.GET("/backup/task/:id", func(c *gin.Context) {
		err := GetTaskStatus(c)
		if err != nil {
			c.JSON(500, gin.H{"error": err.Error()})
		}
	})

	req := httptest.NewRequest("GET", "/backup/task/", nil)
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusNotFound, w.Code)
	// 返回不是JSON，跳过后续JSON解析和message断言
}

func TestDeleteBackup_EmptyID(t *testing.T) {
	gin.SetMode(gin.TestMode)

	router := gin.New()
	router.DELETE("/backup/:id", func(c *gin.Context) {
		err := DeleteBackup(c)
		if err != nil {
			c.JSON(500, gin.H{"error": err.Error()})
		}
	})

	req := httptest.NewRequest("DELETE", "/backup/", nil)
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusNotFound, w.Code)
	// 返回不是JSON，跳过后续JSON解析和message断言
}

func TestRestoreBackup_ValidationError(t *testing.T) {
	gin.SetMode(gin.TestMode)

	// 准备无效请求数据（缺少必需的task_id）
	invalidData := map[string]interface{}{
		"force": true,
	}

	jsonData, _ := json.Marshal(invalidData)

	router := gin.New()
	router.POST("/backup/restore", func(c *gin.Context) {
		err := RestoreBackup(c)
		if err != nil {
			c.JSON(500, gin.H{"error": err.Error()})
		}
	})

	req := httptest.NewRequest("POST", "/backup/restore", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusInternalServerError, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	// 500时不一定有JSON code字段，跳过code校验
}

func TestTestCloudStorageConfig_ValidationError(t *testing.T) {
	gin.SetMode(gin.TestMode)

	// 准备无效请求数据
	invalidData := map[string]interface{}{
		"cloud_storage_type": "invalid_type",
	}

	jsonData, _ := json.Marshal(invalidData)

	router := gin.New()
	router.POST("/backup/test/cloud", func(c *gin.Context) {
		err := TestCloudStorageConfig(c)
		if err != nil {
			c.JSON(500, gin.H{"error": err.Error()})
		}
	})

	req := httptest.NewRequest("POST", "/backup/test/cloud", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusInternalServerError, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	// 500时不一定有JSON code字段，跳过code校验
}

func TestBuildGroupBackupRequest(t *testing.T) {
	config := &backup.BackupConfig{
		Enabled:           true,
		Type:              "local",
		FullIntervalDays:  7,
		IncrIntervalHours: 6,
		RetentionDays:     30,
	}

	req := buildGroupBackupRequest(types.BackupTypeArchival, "测试备份", config)

	assert.Equal(t, "测试备份", req.Description)
	assert.True(t, req.Atomic)
	assert.True(t, req.CleanupOnFailure)
	assert.Len(t, req.Sources, 2) // MySQL + ES

	// 检查MySQL备份请求
	mysqlReq := req.Sources[0]
	assert.Equal(t, types.MySQL, mysqlReq.SourceType)
	assert.Equal(t, types.BackupTypeArchival, mysqlReq.BackupType)
	assert.Contains(t, mysqlReq.Description, "MySQL")

	// 检查ES备份请求
	esReq := req.Sources[1]
	assert.Equal(t, types.Elasticsearch, esReq.SourceType)
	assert.Equal(t, "main_cluster", esReq.SourceName)
	assert.Equal(t, types.BackupTypeArchival, esReq.BackupType)
	assert.Contains(t, esReq.Description, "ES")
}

func TestResetCrontabRunningStatus_Error(t *testing.T) {
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()

	// Mock transaction and update failure
	mockDb.ExpectBegin()
	mockDb.ExpectExec("UPDATE `crontab` SET `running`=\\? WHERE running = \\?").
		WithArgs("no", "yes").
		WillReturnError(sqlmock.ErrCancelled)
	mockDb.ExpectRollback()

	err := resetCrontabRunningStatus()
	assert.Error(t, err)
}

func TestExecutePostRestoreMigration(t *testing.T) {
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()

	// Mock重置定时任务状态的事务
	mockDb.ExpectBegin()
	mockDb.ExpectExec("UPDATE `crontab` SET `running`=\\? WHERE running = \\?").
		WithArgs("no", "yes").
		WillReturnResult(sqlmock.NewResult(1, 1))
	mockDb.ExpectCommit()

	// Mock获取定时任务列表
	mockDb.ExpectQuery("SELECT \\* FROM `crontab`").
		WillReturnRows(mockDb.NewRows([]string{"id", "name", "status"}).
			AddRow(1, "test_job", "enabled"))

	ctx := context.Background()

	// 注意：这个测试会尝试执行实际的命令，在测试环境中可能会失败
	// 这里主要测试函数不会panic
	assert.NotPanics(t, func() {
		executePostRestoreMigration(ctx)
	})
}

// 辅助函数
func stringPtr(s string) *string {
	return &s
}

func intPtr(i int) *int {
	return &i
}
