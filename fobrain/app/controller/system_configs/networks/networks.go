package networks

import (
	"encoding/json"
	"errors"
	"fmt"
	"fobrain/fobrain/logs"
	"github.com/google/uuid"
	"os"
	"os/exec"
	"strings"
	"time"

	"github.com/gin-gonic/gin"

	"fobrain/fobrain/common/request"
	"fobrain/fobrain/common/response"
	"fobrain/models/mysql/system_configs"
	"fobrain/pkg/utils"
)

type (
	common struct {
		NetworkWorkAddr        string `json:"network_work_addr" form:"network_work_addr" uri:"network_work_addr" validate:"ipv4" zh:"网络工作地址"`
		NetworkWorkMask        string `json:"network_work_mask" form:"network_work_mask" uri:"network_work_mask" validate:"ipv4" zh:"网络工作掩码"`
		NetworkWorkGatewayAddr string `json:"network_work_gateway_addr" form:"network_work_gateway_addr" uri:"network_work_gateway_addr" validate:"ipv4" zh:"网络工作网关地址"`
		NetworkWorkDNSMaster   string `json:"network_work_dns_master" form:"network_work_dns_master" uri:"network_work_dns_master" validate:"ipv4" zh:"网络工作DNS主服务器"`
		NetworkWorkDNSSlave    string `json:"network_work_dns_slave" form:"network_work_dns_slave" uri:"network_work_dns_slave" validate:"" zh:"网络工作DNS从服务器"`
	}
	GetNetworkSettingsReq struct {
		common
		NetworkManageAddr string `json:"network_manage_addr" form:"network_manage_addr" uri:"network_manage_addr" validate:"ipv4" zh:"网络管理地址"`
		NetworkManageMask string `json:"network_manage_mask" form:"network_manage_mask" uri:"network_manage_mask" validate:"ipv4" zh:"网络管理掩码"`
	}
	UpdateNetworkSettingsReq common
	PingReq                  struct {
		Addr string `json:"addr" form:"addr" uri:"addr" validate:"ipv4" zh:"网络地址"`
		Type string `json:"type" form:"type" uri:"type" validate:"required" zh:"操作类型"`
	}
	HostConfig struct {
		Id         string `json:"id" form:"id" uri:"id"  validate:"" zh:"ID"`
		Operator   string `json:"operator" form:"operator"  uri:"operator" validate:"required" zh:"操作人"`
		UpdateTime string `json:"update_time" form:"update_time"  uri:"update_time" validate:"" zh:"更新时间"`
		Ip         string `json:"ip" form:"ip" uri:"ip" binding:"ip" zh:"IP地址"`
		Domain     string `json:"domain" form:"domain" uri:"domain" validate:"" zh:"域名"`
	}
)

func GetNetworksSettings(c *gin.Context) error {
	// 获取网络设置信息
	networkSettings, err := system_configs.NewSystemConfigs().GetMultiConfig(
		[]string{"network_work_addr",
			"network_work_mask",
			"network_work_gateway_addr",
			"network_work_dns_master",
			"network_work_dns_slave",
			"network_manage_addr",
			"network_manage_mask"},
	)
	if err != nil {
		return err
	}
	return response.OkWithData(c, networkSettings)
}
func UpdateNetworkSettings(c *gin.Context) error {
	params, err := request.Validate(c, &UpdateNetworkSettingsReq{})
	if err != nil {
		return err
	}
	list, err := utils.StructToMap(params, "json")
	if err != nil {
		return err
	}
	err = system_configs.NewSystemConfigs().SetMultiConfig(list)
	if err != nil {
		return err
	}
	return response.Ok(c)
}
func Ping(c *gin.Context) error {
	params, err := request.Validate(c, &PingReq{})
	if err != nil {
		return err
	}
	// 执行 ping 命令
	cmd := exec.Command(params.Type, "-c", "2", params.Addr)
	// 获取命令的输出
	output, err := cmd.CombinedOutput()
	if err != nil {
		return response.FailWithDetailed(c, 400, strings.TrimSpace(string(output)), "执行失败")
	}
	return response.OkWithData(c, strings.TrimSpace(string(output)))
}

// GetHostConfig 获取主机配置信息
func GetHostConfig(c *gin.Context) error {
	// 先从数据库拿数据
	res, err := system_configs.NewSystemConfigs().GetConfig("hosts_config")
	if err != nil {
		return response.FailWithMessage(c, "获取主机配置信息失败")
	}

	var data []HostConfig

	// 数据库没有数据时，从/etc/hosts读取
	if res == "" {
		content, err := os.ReadFile("/etc/hosts")
		if err != nil {
			logs.GetLogger().Error("读取/etc/hosts文件失败:", err)
			return response.FailWithMessage(c, "获取主机配置失败")
		}
		data = SplitHostInfo(string(content))
	} else {
		if err = json.Unmarshal([]byte(res), &data); err != nil {
			logs.GetLogger().Error("json解析失败:", err.Error())
			return response.FailWithMessage(c, "获取主机配置信息失败")
		}
	}

	return response.OkWithData(c, data)
}

// UpdateHostConfig 更新主机配置信息
func UpdateHostConfig(c *gin.Context) error {

	var params []HostConfig
	if err := c.ShouldBind(&params); err != nil {
		return response.FailWithMessage(c, "参数校验失败")
	}
	// 对于新增配置设置唯一id
	for i := range params {
		if params[i].Id == "" {
			params[i].Id = strings.ReplaceAll(uuid.New().String(), "-", "")
		}
	}
	data, _ := json.Marshal(params)
	// 将更新的主机配置存入system_config数据库中
	err := system_configs.NewSystemConfigs().UpdateConfig("hosts_config", string(data))
	if err != nil {
		return response.FailWithMessage(c, "更新主机配置信息失败")
	}

	if UpdateHostsFile(string(data)) != nil {
		return response.FailWithMessage(c, "更新主机配置信息失败")
	}
	return response.Ok(c)

}

// UpdateHostsFile 更新主机配置文件
func UpdateHostsFile(data string) error {
	var err error

	// 如果没有传递参数，从数据库中读取
	if data == "" {
		data, err = system_configs.NewSystemConfigs().GetConfig("hosts_config")
		if err != nil {
			return errors.New("获取数据库中主机配置信息失败")
		}
		if data == "" {
			return errors.New("数据库中主机配置信息为空")
		}
	}

	var res []HostConfig
	if err = json.Unmarshal([]byte(data), &res); err != nil {
		logs.GetLogger().Error("json解析失败:", err.Error())
		return errors.New("json解析失败")
	}

	// 构造配置内容
	hostText := CombHostConfig(res)

	const maxRetry = 3
	for i := 0; i < maxRetry; i++ {
		err = os.WriteFile("/etc/hosts", []byte(hostText), 0644)
		if err == nil {
			logs.GetLogger().Info("本地/etc/hosts文件更新成功")
			return nil
		}
		logs.GetLogger().Errorf("写入/etc/hosts失败: %v", err)
		time.Sleep(1 * time.Second)
	}

	// 未更新成功 直接清空主机配置 用户看到的则是当前/etc/hosts文件 而不是数据库数据
	go system_configs.NewSystemConfigs().UpdateConfig("hosts_config", "")
	return errors.New("更新主机配置失败，写入/etc/hosts重试已达最大次数")
}

// SplitHostInfo 拆分主机配置信息
func SplitHostInfo(hostsConfig string) []HostConfig {
	//拆分每行主机
	lines := strings.Split(hostsConfig, "\n")

	now := time.Now().Format(time.DateTime)
	res := make([]HostConfig, 0)
	for i := range lines {
		lines[i] = strings.TrimSpace(lines[i])
		if lines[i] == "" || strings.HasPrefix(lines[i], "#") {
			continue
		}
		// 拆分IP和域名 标准格式 127.0.0.1 localhost
		parts := strings.Fields(lines[i])
		if len(parts) < 2 {
			continue
		}
		ip := parts[0]
		domains := parts[1:]
		res = append(res, HostConfig{
			Id:         strings.ReplaceAll(uuid.New().String(), "-", ""),
			Operator:   "admin",
			UpdateTime: now,
			Ip:         ip,
			Domain:     strings.Join(domains, " "),
		})
	}
	return res
}

// CombHostConfig 组合主机配置信息
func CombHostConfig(hostsConfig []HostConfig) string {
	var res string
	for i := range hostsConfig {
		res += fmt.Sprintf("%s\t%s\n", hostsConfig[i].Ip, hostsConfig[i].Domain)
	}
	return res
}
