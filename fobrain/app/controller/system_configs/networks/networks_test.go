package networks

import (
	"bytes"
	"encoding/json"
	"errors"
	"fobrain/models/mysql/system_configs"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"net/http"
	"net/http/httptest"
	"os"
	"reflect"
	"testing"
)

// 伪造的系统配置结构体，实现 GetConfig 方法的模拟
type MockSystemConfigs struct{}

func (m *MockSystemConfigs) GetConfig(key string) (string, error) {
	return "", nil
}
func TestUpdateHostsFile(t *testing.T) {
	gin.SetMode(gin.TestMode)

	testCases := []struct {
		name          string
		inputData     string
		mockDBData    string
		mockDBError   error
		mockReadError error
		mockReadData  string
		writeErrors   []error
		expectError   bool
	}{
		{
			name:        "传入data正常，写入成功",
			inputData:   `[{"operator":"admin","update_time":"2024-01-01","ip":"127.0.0.1","domain":"localhost"}]`,
			writeErrors: []error{nil},
			expectError: false,
		},
		{
			name:        "传入空data，从数据库读取正常数据，写入成功",
			inputData:   "",
			mockDBData:  `[{"operator":"admin","update_time":"2024-01-01","ip":"127.0.0.1","domain":"localhost"}]`,
			mockDBError: nil,
			writeErrors: []error{nil},
			expectError: false,
		},
		{
			name:        "传入空data，数据库返回空字符串",
			inputData:   "",
			mockDBData:  "",
			mockDBError: nil,
			expectError: true,
		},
		{
			name:        "传入空data，数据库读取失败",
			inputData:   "",
			mockDBError: errors.New("db error"),
			expectError: true,
		},
		{
			name:        "json解析失败",
			inputData:   "invalid json",
			expectError: true,
		},
		{
			name:          "读 /etc/hosts 失败但不影响写入",
			inputData:     "",
			mockDBData:    `[{"operator":"admin","update_time":"2024-01-01","ip":"127.0.0.1","domain":"localhost"}]`,
			mockDBError:   nil,
			mockReadError: errors.New("read /etc/hosts error"),
			writeErrors:   []error{nil},
			expectError:   false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			patches := gomonkey.NewPatches()
			defer patches.Reset()

			patches.ApplyMethod(reflect.TypeOf(&system_configs.SystemConfigs{}), "GetConfig",
				func(_ *system_configs.SystemConfigs, key string) (string, error) {
					return tc.mockDBData, tc.mockDBError
				})

			patches.ApplyFunc(os.ReadFile, func(name string) ([]byte, error) {
				if tc.mockReadError != nil {
					return nil, tc.mockReadError
				}
				return []byte(tc.mockReadData), nil
			})

			writeCallCount := 0
			patches.ApplyFunc(os.WriteFile, func(name string, data []byte, perm os.FileMode) error {
				if writeCallCount < len(tc.writeErrors) {
					err := tc.writeErrors[writeCallCount]
					writeCallCount++
					return err
				}
				return nil
			})

			err := UpdateHostsFile(tc.inputData)

			if tc.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestUpdateHostConfig_WithMockedUpdateHostsFile(t *testing.T) {
	gin.SetMode(gin.TestMode)
	patches := gomonkey.NewPatches()
	defer patches.Reset()

	patches.ApplyFunc(system_configs.NewSystemConfigs, func() *system_configs.SystemConfigs {
		return &system_configs.SystemConfigs{}
	})

	patches.ApplyMethod(reflect.TypeOf(&system_configs.SystemConfigs{}), "UpdateConfig", func(_ *system_configs.SystemConfigs, key, value string) error {
		return nil
	})

	testCases := []struct {
		name           string
		mockHostsError error
		expectedCode   int
		expectedMsg    string
	}{
		{
			name:           "UpdateHostsFile 返回 nil (成功)",
			mockHostsError: nil,
			expectedCode:   200,
			expectedMsg:    `"code":0`,
		},
		{
			name:           "UpdateHostsFile 返回错误",
			mockHostsError: errors.New("写入hosts文件失败"),
			expectedCode:   400,
			expectedMsg:    "更新主机配置信息失败",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)

			hostConfigs := []HostConfig{
				{Operator: "admin", Ip: "***********00", Domain: "example.com"},
			}
			body, err := json.Marshal(hostConfigs)
			assert.NoError(t, err)

			c.Request = httptest.NewRequest("POST", "/update", bytes.NewReader(body))
			c.Request.Header.Set("Content-Type", "application/json")

			patches.ApplyFunc(UpdateHostsFile, func(data string) error {
				return tc.mockHostsError
			})

			err = UpdateHostConfig(c)
			assert.NoError(t, err)

			assert.Equal(t, tc.expectedCode, w.Code)
			assert.Contains(t, w.Body.String(), tc.expectedMsg)
		})
	}
}

func TestUpdateHostConfig(t *testing.T) {
	gin.SetMode(gin.TestMode)

	basePatches := gomonkey.NewPatches()
	basePatches.ApplyFunc(system_configs.NewSystemConfigs, func() *system_configs.SystemConfigs {
		return &system_configs.SystemConfigs{}
	})
	defer basePatches.Reset()

	type testCase struct {
		name               string
		requestBody        []HostConfig
		mockUpdateError    error
		mockUpdateHostsErr error
		expectCode         int
		expectMessage      string
	}

	testCases := []testCase{
		{
			name:          "参数绑定失败",
			requestBody:   nil, // 触发绑定失败
			expectCode:    400,
			expectMessage: "参数校验失败",
		},
		{
			name:            "数据库更新失败",
			requestBody:     []HostConfig{{Operator: "admin", Ip: "127.0.0.1", Domain: "localhost"}},
			mockUpdateError: errors.New("db error"),
			expectCode:      400,
			expectMessage:   "更新主机配置信息失败",
		},
		{
			name:               "更新hosts文件失败",
			requestBody:        []HostConfig{{Operator: "admin", Ip: "127.0.0.1", Domain: "localhost"}},
			mockUpdateHostsErr: errors.New("file error"),
			expectCode:         400,
			expectMessage:      "更新主机配置信息失败",
		},
		{
			name:          "更新成功",
			requestBody:   []HostConfig{{Operator: "admin", Ip: "127.0.0.1", Domain: "localhost"}},
			expectCode:    200,
			expectMessage: `"code":0`,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {

			patches := gomonkey.NewPatches()
			defer patches.Reset()

			// patch UpdateConfig
			patches.ApplyMethod(reflect.TypeOf(&system_configs.SystemConfigs{}), "UpdateConfig",
				func(_ *system_configs.SystemConfigs, key, value string) error {
					return tc.mockUpdateError
				})

			// patch UpdateHostsFile
			patches.ApplyFunc(UpdateHostsFile, func(data string) error {
				return tc.mockUpdateHostsErr
			})

			// 构造请求
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)

			var bodyBytes []byte
			var err error
			if tc.requestBody != nil {
				bodyBytes, err = json.Marshal(tc.requestBody)
				assert.NoError(t, err)
			} else {
				// 绑定失败场景：传入非法 JSON
				bodyBytes = []byte(`{bad json}`)
			}

			c.Request = httptest.NewRequest(http.MethodPost, "/", bytes.NewReader(bodyBytes))
			c.Request.Header.Set("Content-Type", "application/json")

			// 调用被测函数
			err = UpdateHostConfig(c)
			assert.NoError(t, err)

			assert.Equal(t, tc.expectCode, w.Code)
			assert.Contains(t, w.Body.String(), tc.expectMessage)
		})
	}
}

func TestGetHostConfig(t *testing.T) {
	gin.SetMode(gin.TestMode)

	mockHostList := []HostConfig{
		{
			Operator:   "admin",
			UpdateTime: "2024-01-01 00:00:00",
			Ip:         "127.0.0.1",
			Domain:     "localhost",
		},
	}
	mockHostListStr, _ := json.Marshal(mockHostList)

	tests := []struct {
		name             string
		dbValue          string
		dbError          error
		readFileContent  string
		readFileError    error
		expectedCode     int
		expectedContains string
	}{
		{
			name:             "数据库正常返回",
			dbValue:          string(mockHostListStr),
			expectedCode:     http.StatusOK,
			expectedContains: `"code":0`,
		},
		{
			name:             "数据库为空，从文件读取成功",
			dbValue:          "",
			readFileContent:  "127.0.0.1 localhost",
			expectedCode:     http.StatusOK,
			expectedContains: `"code":0`,
		},
		{
			name:             "数据库为空，文件读取失败",
			dbValue:          "",
			readFileError:    errors.New("read error"),
			expectedCode:     http.StatusBadRequest,
			expectedContains: `"code":400`,
		},
		{
			name:             "数据库返回错误",
			dbError:          errors.New("db error"),
			expectedCode:     http.StatusBadRequest,
			expectedContains: `"code":400`,
		},
		{
			name:             "数据库 JSON 非法",
			dbValue:          "invalid json",
			expectedCode:     http.StatusBadRequest,
			expectedContains: `"code":400`,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			patches := gomonkey.NewPatches()
			defer patches.Reset()

			// 保证 NewSystemConfigs 返回可用对象
			patches.ApplyFunc(system_configs.NewSystemConfigs, func() *system_configs.SystemConfigs {
				return &system_configs.SystemConfigs{}
			})

			// patch GetConfig
			patches.ApplyMethod(
				reflect.TypeOf(&system_configs.SystemConfigs{}), "GetConfig",
				func(_ *system_configs.SystemConfigs, key string) (string, error) {
					return tt.dbValue, tt.dbError
				},
			)

			// patch os.ReadFile
			patches.ApplyFunc(os.ReadFile, func(name string) ([]byte, error) {
				if tt.readFileError != nil {
					return nil, tt.readFileError
				}
				return []byte(tt.readFileContent), nil
			})

			// 发起请求
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)

			err := GetHostConfig(c)
			assert.NoError(t, err)

			assert.Equal(t, tt.expectedCode, w.Code)
			assert.Contains(t, w.Body.String(), tt.expectedContains)
		})
	}
}

func TestSplitHostInfo(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected []HostConfig // 仅校验 Operator/IP/Domain，忽略 UpdateTime
	}{
		{
			name:  "Valid multiple entries",
			input: "127.0.0.1 localhost\n::1 ip6-localhost",
			expected: []HostConfig{
				{"1", "admin", "", "127.0.0.1", "localhost"},
				{"1", "admin", "", "::1", "ip6-localhost"},
			},
		},
		{
			name:  "With comment line",
			input: "# This is a comment\n127.0.0.1 localhost",
			expected: []HostConfig{
				{"1", "admin", "", "127.0.0.1", "localhost"},
			},
		},
		{
			name:  "With empty lines",
			input: "\n\n127.0.0.1 localhost\n",
			expected: []HostConfig{
				{"1", "admin", "", "127.0.0.1", "localhost"},
			},
		},
		{
			name:     "Only IP no domain",
			input:    "127.0.0.1",
			expected: nil,
		},
		{
			name:  "Mixed invalid and valid lines",
			input: "#comment\n  \n*********** example.com www.example.com",
			expected: []HostConfig{
				{"1", "admin", "", "***********", "example.com www.example.com"},
			},
		},
		{
			name:     "Empty input",
			input:    "",
			expected: nil,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			got := SplitHostInfo(tc.input)

			assert.Equal(t, len(tc.expected), len(got), "entry count mismatch")

			for i := range tc.expected {
				assert.Equal(t, tc.expected[i].Operator, got[i].Operator, "operator mismatch")
				assert.Equal(t, tc.expected[i].Ip, got[i].Ip, "IP mismatch")
				assert.Equal(t, tc.expected[i].Domain, got[i].Domain, "domain mismatch")
				assert.NotEmpty(t, got[i].UpdateTime, "UpdateTime should not be empty")
			}
		})
	}
}

func TestCombHostConfig(t *testing.T) {
	tests := []struct {
		name     string
		input    []HostConfig
		expected string
	}{
		{
			name:     "NilInput",
			input:    nil,
			expected: "",
		},
		{
			name:     "EmptySlice",
			input:    []HostConfig{},
			expected: "",
		},
		{
			name: "SingleValidEntry",
			input: []HostConfig{
				{"1", "admin", "2023-09-01", "***********", "example.com"},
			},
			expected: "***********\texample.com\n",
		},
		{
			name: "MultipleEntries",
			input: []HostConfig{
				{"1", "admin", "2023-09-01", "***********", "example.com"},
				{"1", "admin", "2023-09-01", "********", "test.local"},
			},
			expected: "***********\texample.com\n********\ttest.local\n",
		},
		{
			name: "EmptyEntriesFields",
			input: []HostConfig{
				{"1", "admin", "2023-09-01", "", ""},
				{"1", "admin", "2023-09-01", "***********", ""},
				{"1", "admin", "2023-09-01", "", "empty.domain"},
			},
			expected: "\t\n***********\t\n\tempty.domain\n",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := CombHostConfig(tt.input)
			if got != tt.expected {
				t.Errorf("CombHostConfig() = %q, want %q", got, tt.expected)
			}
		})
	}
}
