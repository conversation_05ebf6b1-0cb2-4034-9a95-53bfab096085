package scan_ban

import (
	"fobrain/fobrain/common/response"
	"fobrain/models/mysql/system_configs"
	"net/http"

	"github.com/gin-gonic/gin"
	"go-micro.dev/v4/logger"
)

func ReadScanBanConfig(ctx *gin.Context) error {
	conf, err := system_configs.NewSystemConfigs().GetScanBanConfig()
	if err != nil {
		logger.Errorf("[ERROR] 获取禁扫配置失败: %v\n", err)
		return response.FailWithMessage(ctx, "获取禁扫配置失败")
	}
	return response.OkWithData(ctx, conf)
}

func SaveScanBanConfig(ctx *gin.Context) error {
	var body system_configs.ScanConfig
	if err := ctx.ShouldBindJSON(&body); err != nil {
		logger.Errorf("[ERROR] SaveScanBanConfig 解析失败: %v\n", err)
		return response.FailWithMessage(ctx, "解析请求失败")
	}
	if err := system_configs.NewSystemConfigs().UpdateScanConfig(&body); err != nil {
		logger.Errorf("[ERROR] SaveScanBanConfig save failed: %v\n", err)
		return response.FailWithCodeMessage(ctx, http.StatusInternalServerError, err.Error())
	}
	return response.Ok(ctx)
}
