package scan_ban

import (
	"encoding/json"
	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/models/mysql/system_configs"
	"fobrain/pkg/fortest"
	"net"
	"net/http"
	"net/http/httptest"
	"reflect"
	"strings"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/gin-gonic/gin"
)

func TestReadScanBanConfig(t *testing.T) {
	fortest.Init()
	fortest.SetGoTestMock()
	value := `{"enable_ban":true,"week_days":["Monday"],"ban_periods":[{"start":"12:00:00","end":"14:00:00"}],"banned_assets":["www.wcewtewe.com","127.0.0.1"]}`
	mockDb := testcommon.InitSqlMock()
	defer mockDb.Close()
	mockDb.ExpectQuery("SELECT value FROM `system_configs` WHERE `key` = ?").
		WithArgs("scan_ban_key").
		WillReturnRows(
			sqlmock.NewRows([]string{"value"}).AddRow(value),
		)
	r := gin.Default()
	url := "/api/v1/system/scan/ban"
	r.GET(url, func(ctx *gin.Context) {
		ReadScanBanConfig(ctx)
	})

	req, _ := http.NewRequest(http.MethodGet, url, nil)
	q := req.URL.Query()
	req.URL.RawQuery = q.Encode()
	// 创建响应记录器
	w := httptest.NewRecorder()

	// 模拟请求
	r.ServeHTTP(w, req)
	// 验证响应
	t.Log(w.Code)
}

func TestSaveScanBanConfig(t *testing.T) {
	// 初始化测试环境
	fortest.Init()
	fortest.SetGoTestMock()
	count := 10000
	ips := make([]string, 0, count)             // 初始化切片，容量为count
	startIP := net.ParseIP("***********").To4() // 从 *********** 开始

	for i := 0; i < count; i++ {
		// 将每次的IP加1并存入切片
		ip := net.IPv4(startIP[0], startIP[1], startIP[2], startIP[3]+byte(i)).String()
		ips = append(ips, ip)
	}

	// 准备测试数据
	scanConfig := system_configs.ScanConfig{
		EnableBan:    true,
		WeekDays:     []string{time.Monday.String()},
		BanPeriods:   []system_configs.Period{{Start: "12:00:00", End: "14:00:00"}},
		BannedAssets: ips,
	}

	val, err := json.Marshal(scanConfig)
	if err != nil {
		t.Fatalf("无法序列化 ScanConfig: %v", err)
	}
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyMethod(reflect.TypeOf(system_configs.NewSystemConfigs()), "UpdateConfig", func(_ *system_configs.SystemConfigs, k, v string) error {
		t.Logf("k:%s, v size:%d\n", k, len([]rune(v)))

		return nil
	}).Reset()

	// 设置 Gin 路由
	r := gin.Default()
	url := "/api/v1/system/scan/ban"
	r.POST(url, func(ctx *gin.Context) {
		SaveScanBanConfig(ctx)
	})

	// 创建请求
	req, _ := http.NewRequest(http.MethodPost, "/api/v1/system/scan/ban", strings.NewReader(string(val)))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()

	// 执行请求
	r.ServeHTTP(w, req)

	// 验证响应
	if w.Code != http.StatusOK {
		t.Errorf("期望状态码 %d，但得到 %d", http.StatusOK, w.Code)
	}

}
