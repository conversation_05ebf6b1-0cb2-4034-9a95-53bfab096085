package black_white

import (
	"errors"
	"fmt"
	blackwhite "fobrain/fobrain/app/services/system_configs/black_white"
	"fobrain/fobrain/common/request"
	"fobrain/fobrain/common/response"
	"fobrain/pkg/cfg"
	"fobrain/pkg/utils"
	"github.com/gin-gonic/gin"
	"github.com/xuri/excelize/v2"
	"go-micro.dev/v4/logger"
	"net/http"
	"path/filepath"
	"time"
)

func ListBlackWhite(ctx *gin.Context) error {
	list, err := blackwhite.NewBlackWhiteService().List()
	if err != nil {
		logger.Errorf("[ERROR] ListBlackWhite error: %v\n", err)
		return response.FailWithCodeMessage(ctx, http.StatusInternalServerError, "获取黑白名单配置失败")
	}
	return response.OkWithData(ctx, list)
}

func AddBlackWhite(ctx *gin.Context) error {
	var body blackwhite.BlackWhiteConfAddRequest
	if err := ctx.ShouldBindJSON(&body); err != nil {
		logger.Errorf("[ERROR] AddBlackWhite error: %v\n", err)
		return response.FailWithMessage(ctx, "参数错误")
	}
	err := blackwhite.NewBlackWhiteService().Save(&body)
	if errors.Is(err, errors.New("不支持的类型")) {
		return response.FailWithCodeMessage(ctx, http.StatusBadRequest, err.Error())
	}
	if err != nil {
		logger.Errorf("[ERROR] AddBlackWhite save failed, body:%+v, error: %v\n", body, err)
		return response.FailWithCodeMessage(ctx, http.StatusInternalServerError, "保存失败")
	}

	return response.Ok(ctx)
}

func DeleteBlackWhite(ctx *gin.Context) error {
	params, err := request.Validate(ctx, &struct {
		Ids []int64 `json:"ids" uri:"ids" form:"ids" validate:"required,dive,number" zh:"主键id"`
	}{})

	if err != nil {
		logger.Errorf("[ERROR] DeleteBlackWhite error: %v\n", err)
		return response.FailWithMessage(ctx, err.Error())
	}
	err = blackwhite.NewBlackWhiteService().Delete(params.Ids)
	if err != nil {
		logger.Errorf("[ERROR] DeleteBlackWhite exec error: %v\n", err)
		return response.FailWithCodeMessage(ctx, http.StatusInternalServerError, "删除失败")
	}
	return response.Ok(ctx)
}

func BlackWhiteJudge(ctx *gin.Context) error {
	isAllowed, err := blackwhite.NewBlackWhiteService().Judge(ctx.Query("ip"))
	if err != nil {
		logger.Errorf("[ERROR] BlackWhiteJudge error: %v\n", err)
		return response.FailWithCodeMessage(ctx, http.StatusInternalServerError, "判断ip是否允许失败")
	}
	return response.OkWithData(ctx, isAllowed)
}

func SaveUploadedFile(ctx *gin.Context, path string, sizeLimit int64) ([][]string, error) {
	// 获取上传的文件
	file, err := ctx.FormFile("file")
	if err != nil {
		return nil, response.FailWithMessage(ctx, "获取上传文件失败")
	}

	if file.Size > sizeLimit*1024*1024 {
		return nil, fmt.Errorf("文件文件大小不能大于%dM", sizeLimit)
	}
	localFileName := fmt.Sprintf("%s-%s", time.Now().Format("20060102150405"), file.Filename)
	// 将文件保存到服务器本地
	storagePath := cfg.LoadCommon().StoragePath
	if err := ctx.SaveUploadedFile(file, filepath.Join(storagePath, path, localFileName)); err != nil {
		return nil, err
	}
	f, err := file.Open()
	if err != nil {
		return nil, err
	}
	defer f.Close()

	fileReader, err := excelize.OpenReader(f)
	if err != nil {
		return nil, err
	}
	sheetName := fileReader.GetSheetName(0)
	rows, err := fileReader.GetRows(sheetName, excelize.Options{RawCellValue: true})
	if err != nil {
		return nil, err
	}

	return rows, nil
}

// BlackWhiteImport 导入黑白名单模板
func BlackWhiteImport(ctx *gin.Context) error {
	rows, err := SaveUploadedFile(ctx, "/app/black/", 10)
	if err != nil {
		return response.FailWithCodeMessage(ctx, http.StatusInternalServerError, err.Error())
	}

	if err := blackwhite.NewBlackWhiteService().Import(rows); err != nil {
		return response.FailWithCodeMessage(ctx, http.StatusInternalServerError, err.Error())
	}
	return response.Ok(ctx)
}

type ExportRequest struct {
	Category string   `json:"category" form:"category" uri:"category" validate:"omitempty,max=50" zh:"分类"`
	Ids      []uint64 `json:"Ids" form:"Ids" uri:"Ids"  zh:"ids"`
}

func BlackWhiteExport(ctx *gin.Context) error {
	params, err := request.Validate(ctx, &ExportRequest{})
	if err != nil {
		return err
	}
	path, err := blackwhite.NewBlackWhiteService().Export(params.Category, utils.ListNonZero(params.Ids))
	if err != nil {
		logger.Errorf("[ERROR] BlackWhiteExport error: %v\n", err)
		return response.FailWithCodeMessage(ctx, http.StatusInternalServerError, err.Error())
	}
	return response.OkWithFile(ctx, path, true)
}
