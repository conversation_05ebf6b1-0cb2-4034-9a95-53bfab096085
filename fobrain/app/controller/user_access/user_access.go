package user_access

import (
	"fobrain/fobrain/app/repository/staff"
	"fobrain/fobrain/app/repository/user_access"
	requestPermission "fobrain/fobrain/app/request/permission"
	"fobrain/fobrain/app/services/permission"
	"fobrain/fobrain/common/request"
	"fobrain/fobrain/common/response"
	permissionModel "fobrain/models/mysql/permission"
	"github.com/gin-gonic/gin"
)

// 权限控制

// CreateUser
// @Summary 创建用户
func CreateUser(ctx *gin.Context) error {
	params, err := request.Validate(ctx, &requestPermission.AddUserRequest{})
	if err != nil {
		return response.FailWithMessage(ctx, err.Error())
	}
	s := permission.NewPermissionService()
	if _, err = s.AddUser(ctx, *params); err != nil {
		return err
	}

	return response.OkWithMessage(ctx, "创建成功")
}

func UserList(ctx *gin.Context) error {
	params, err := request.Validate(ctx, &requestPermission.GetUsersListRequest{})
	if err != nil {
		return response.FailWithMessage(ctx, err.Error())
	}
	s := permission.NewPermissionService()
	users, total, err := s.GetUsers(ctx, params)
	if err != nil {
		return response.FailWithMessage(ctx, err.Error())
	}

	return response.OkWithPageData(ctx, total, params.Page, params.PerPage, users)
}

// Roles
// @Summary 角色列表
func Roles(ctx *gin.Context) error {
	params, err := request.Validate(ctx, &requestPermission.GetRoleListRequest{})
	if err != nil {
		return response.FailWithMessage(ctx, err.Error())
	}
	s := permission.NewPermissionService()

	roles, total, err := s.GetRoles(ctx, params)
	if err != nil {
		return response.FailWithMessage(ctx, err.Error())
	}
	return response.OkWithPageData(ctx, total, params.Page, params.PerPage, roles)
}

// RolesAdd
// @Summary 添加角色
func RolesAdd(ctx *gin.Context) error {
	params, err := request.Validate(ctx, &requestPermission.AddRoleRequest{})
	if err != nil {
		return response.FailWithMessage(ctx, err.Error())
	}
	s := permission.NewPermissionService()

	roles, err := s.AddRole(ctx, *params)
	if err != nil {
		return response.FailWithMessage(ctx, err.Error())
	}
	return response.OkWithData(ctx, roles)
}

// RolesUpdate
// @Summary 修改角色
func RolesUpdate(ctx *gin.Context) error {
	params, err := request.Validate(ctx, &requestPermission.UpdateRoleRequest{})
	if err != nil {
		return response.FailWithMessage(ctx, err.Error())
	}
	s := permission.NewPermissionService()

	roles, err := s.UpdateRole(ctx, *params)
	if err != nil {
		return response.FailWithMessage(ctx, err.Error())
	}
	return response.OkWithData(ctx, roles)
}

// RolesDelete
// @Summary 删除角色
func RolesDelete(ctx *gin.Context) error {
	params, err := request.Validate(ctx, &requestPermission.DeleteRoleRequest{})
	if err != nil {
		return response.FailWithMessage(ctx, err.Error())
	}
	s := permission.NewPermissionService()

	err = s.DeleteRole(ctx, params.Ids)
	if err != nil {
		return response.FailWithMessage(ctx, err.Error())
	}
	return response.Ok(ctx)
}

// Staff
// 人员台账列表 - 滚动查询
func Staff(ctx *gin.Context) error {
	params, err := request.Validate(ctx, &struct {
		request.PageRequest
	}{})
	if err != nil {
		return response.FailWithMessage(ctx, err.Error())
	}

	roles, total := staff.CompactList(params.Page, params.PerPage)
	return response.OkWithPageData(ctx, total, params.Page, params.PerPage, roles)
}

func DeleteByIds(ctx *gin.Context) error {
	params, err := request.Validate(ctx, &struct {
		Ids []uint64 `json:"ids" form:"ids" uri:"ids" zh:"如果是批量删除，ids是一个数组"`
	}{})
	if err != nil {
		return response.FailWithMessage(ctx, err.Error())
	}

	s := permission.NewPermissionService()

	if err := s.DeleteUser(ctx, params.Ids); err != nil {
		return err
	}

	return response.OkWithMessage(ctx, "删除成功")
}

func UpdateUser(ctx *gin.Context) error {
	params, err := request.Validate(ctx, &requestPermission.UpdateUserRequest{})
	if err != nil {
		return response.FailWithMessage(ctx, err.Error())
	}
	s := permission.NewPermissionService()
	if _, err = s.UpdateUser(ctx, *params); err != nil {
		return err
	}

	return response.OkWithMessage(ctx, "更新成功")
}
func BatchSwitchUsersStatus(ctx *gin.Context) error {
	params, err := request.Validate(ctx, &requestPermission.BatchSwitchUsersStatusRequest{})
	if err != nil {
		return response.FailWithMessage(ctx, err.Error())
	}
	s := permission.NewPermissionService()
	if err = s.BatchSwitchUsersStatus(ctx, params.Ids, params.Type); err != nil {
		return response.FailWithMessage(ctx, err.Error())
	}

	return response.OkWithMessage(ctx, "操作成功")
}

func UpdateUserRuleInfoConfig(ctx *gin.Context) error {
	params, err := request.Validate(ctx, &user_access.UpdateUserRuleInfoConfig{})
	if err != nil {
		return response.FailWithMessage(ctx, err.Error())
	}
	userId := request.GetUserId(ctx)
	if err := user_access.UpdateRuleInfoById(params.RuleInfo, userId); err != nil {
		return response.FailWithMessage(ctx, err.Error())
	}

	return response.OkWithMessage(ctx, "更新成功")
}

// MenusTree
// 获取系统所有的菜单树(菜单管理用，暂未使用)
func MenusTree(ctx *gin.Context) error {
	service := permission.NewPermissionService()
	menus, err := service.GetMenusTree(ctx)
	if nil != err {
		return nil
	}
	return response.OkWithData(ctx, menus)
}

// MyMenusTree
// 获取当前登录用户有权限的菜单树
func MyMenusTree(ctx *gin.Context) error {
	service := permission.NewPermissionService()
	menus, err := service.GetUserMenus(ctx, ctx.GetUint64("user_id"))
	if nil != err {
		return err
	}
	return response.OkWithData(ctx, menus)
}

// RoleMenus
// 获取角色的菜单树，所有的和选中的
func RoleMenus(ctx *gin.Context) error {
	params, err := request.Validate(ctx, &requestPermission.RoleMenuRequest{})
	if err != nil {
		return response.FailWithMessage(ctx, err.Error())
	}
	service := permission.NewPermissionService()
	menus, err := service.GetMenusTreeForRole(ctx, params.Id)
	if nil != err {
		return response.FailWithMessage(ctx, err.Error())
	}
	return response.OkWithData(ctx, menus)
}

func GetDataPermissionLevelMap(ctx *gin.Context) error {
	return response.OkWithData(ctx, permissionModel.DataPermissionLevelMap)
}
