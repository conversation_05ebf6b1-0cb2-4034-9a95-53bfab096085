package user_access

import (
	"errors"
	"fobrain/fobrain/common/license"
	"net/http/httptest"
	"strings"
	"sync"
	"testing"

	"github.com/agiledragon/gomonkey/v2"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/olivere/elastic/v7"

	testcommon "fobrain/fobrain/tests/common_test"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

var syncLock = sync.RWMutex{}

func TestRoles(t *testing.T) {
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	t.Run("param err ", func(t *testing.T) {
		req := httptest.NewRequest("GET", "/api/v1/user_access/roles", strings.NewReader(`{}`))
		// 创建一个Gin上下文
		c.Request = req

		err := Roles(c)
		assert.Empty(t, err)
	})
	t.Run("list Err", func(t *testing.T) {
		mockDb := testcommon.GetMysqlMock()
		defer mockDb.Close()
		mockDb.ExpectQuery("SELECT count(*) FROM `roles`").WillReturnError(errors.New("list err"))

		req := httptest.NewRequest("GET", "/api/v1/user_access/roles?page=1&per_page=10", nil)
		// 创建一个Gin上下文
		c.Request = req
		_ = Roles(c)
		assert.Contains(t, w.Body.String(), "list err")
	})

	t.Run("Success", func(t *testing.T) {
		mockDb := testcommon.GetMysqlMock()
		defer mockDb.Close()
		mockDb.ExpectQuery("SELECT count(*) FROM `roles`").WillReturnRows(sqlmock.NewRows([]string{"count"}).
			AddRow(1))

		mockDb.ExpectQuery("SELECT * FROM `roles` LIMIT 10").WillReturnRows(sqlmock.NewRows([]string{"id", "name", "key", "status", "remark"}).
			AddRow(1, "1", "1", 1, "1"))
		// SELECT `role_id`,count(role_id) as count FROM `users_roles` WHERE `role_id` IN (1) GROUP BY `role_id`
		mockDb.ExpectQuery("SELECT `role_id`,count(role_id) as count FROM `users_roles` WHERE `role_id` IN (?) GROUP BY `role_id`").
			WithArgs(1).
			WillReturnRows(sqlmock.NewRows([]string{"role_id", "count"}).
				AddRow(1, 1))

		req := httptest.NewRequest("GET", "/api/v1/user_access/roles?page=1&per_page=10", nil)
		c.Request = req
		err := Roles(c)
		t.Log(w.Body.String())
		assert.NoError(t, err)
	})
}

func TestStaff(t *testing.T) {
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	t.Run("param err ", func(t *testing.T) {
		req := httptest.NewRequest("GET", "/api/v1/user_access/staff_list", strings.NewReader(`{}`))
		c.Request = req

		err := Staff(c)
		assert.Empty(t, err)
	})

	t.Run("Success", func(t *testing.T) {
		mockServer := testcommon.NewMockServer()
		defer mockServer.Close()
		mockServer.Register("staff/_search", []*elastic.SearchHit{
			{
				Id:     "1",
				Source: []byte(`{"id": 1, "name":"example"}`),
			},
		})

		req := httptest.NewRequest("GET", "/api/v1/user_access/staff_list?page=1&per_page=10", nil)
		c.Request = req
		err := Staff(c)
		assert.Empty(t, err)
	})
}

func TestCreateUser(t *testing.T) {
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)

	t.Run("param err ", func(t *testing.T) {
		req := httptest.NewRequest("POST", "/api/v1/user_access/create_user", strings.NewReader(`{}`))
		c.Request = req

		err := CreateUser(c)
		assert.Empty(t, err)
	})

	t.Run("err name is exist", func(t *testing.T) {
		mockDb := testcommon.GetMysqlMock()
		defer mockDb.Close()
		mockDb.ExpectQuery("SELECT count(*) FROM `users` WHERE `account` = ?").
			WithArgs("example").WillReturnRows(mockDb.NewRows([]string{"count"}).
			AddRow(1))

		req := httptest.NewRequest(
			"POST",
			"/api/v1/user_access/create_user?account=example&username=example&password=example&role_id=1&staff_ids[]=example1&staff_ids[]=example2",
			strings.NewReader(`{"account":"example", "username":"example", "password":"Flower.123", "roles":[1], "staff_id":["example1", "example2"]}`),
		)

		req.Header.Set("Content-Type", "application/json")
		c.Request = req
		err := CreateUser(c)
		assert.Contains(t, err.Error(), "账号已存在！")
	})

	t.Run("err role not exist", func(t *testing.T) {
		mockDb := testcommon.GetMysqlMock()
		defer mockDb.Close()
		mockDb.ExpectQuery("SELECT count(*) FROM `users` WHERE `account` = ?").
			WithArgs("example").WillReturnRows(mockDb.NewRows([]string{"count"}).
			AddRow(0))

		mockDb.ExpectQuery("SELECT count(*) FROM `roles` WHERE `id` IN (?)").
			WithArgs(1).WillReturnRows(mockDb.NewRows([]string{"count"}).
			AddRow(0))

		req := httptest.NewRequest(
			"POST",
			"/api/v1/user_access/create_user?account=example&username=example&password=example&role_id=1&staff_ids[]=example1&staff_ids[]=example2",
			strings.NewReader(`{"account":"example", "username":"example", "password":"Flower.123", "roles":[1], "staff_id":["example1", "example2"]}`),
		)

		req.Header.Set("Content-Type", "application/json")
		c.Request = req
		err := CreateUser(c)
		assert.Contains(t, err.Error(), "角色不存在！")
	})

	t.Run("user create err", func(t *testing.T) {
		mockDb := testcommon.GetMysqlMock()
		defer mockDb.Close()
		mockDb.ExpectQuery("SELECT count(*) FROM `users` WHERE `account` = ?").
			WithArgs("example").WillReturnRows(mockDb.NewRows([]string{"count"}).
			AddRow(0))

		mockDb.ExpectQuery("SELECT count(*) FROM `roles` WHERE `id` IN (?)").
			WithArgs(1).WillReturnRows(mockDb.NewRows([]string{"count"}).
			AddRow(1))

		req := httptest.NewRequest(
			"POST",
			"/api/v1/user_access/create_user?account=example&username=example&password=example&role_id=1&staff_ids[]=example1&staff_ids[]=example2",
			strings.NewReader(`{"account":"example", "username":"example", "password":"Flower.123", "roles":[1], "staff_id":["example1", "example2"]}`),
		)

		req.Header.Set("Content-Type", "application/json")

		c.Request = req
		err := CreateUser(c)
		t.Log(err)
		assert.NotNil(t, err)
	})

	t.Run("role create err", func(t *testing.T) {
		mockDb := testcommon.GetMysqlMock()
		defer mockDb.Close()
		mockDb.ExpectQuery("SELECT count(*) FROM `users` WHERE `account` = ?").
			WithArgs("example").WillReturnRows(mockDb.NewRows([]string{"count"}).
			AddRow(0))

		mockDb.ExpectQuery("SELECT count(*) FROM `roles` WHERE `id` IN (?)").
			WithArgs(1).WillReturnRows(mockDb.NewRows([]string{"count"}).
			AddRow(1))

		mockDb.ExpectBegin()
		mockDb.ExpectBegin()
		mockDb.ExpectExec("INSERT INTO `users` (`created_at`,`updated_at`,`username`,`account`,`password`,`staff_id`,`status`) VALUES (?,?,?,?,?,?,?)").
			WillReturnResult(sqlmock.NewResult(1, 1))

		mockDb.ExpectCommit()
		mockDb.ExpectCommit()

		req := httptest.NewRequest(
			"POST",
			"/api/v1/user_access/create_user?account=example&username=example&password=example&role_id=1&staff_ids[]=example1&staff_ids[]=example2",
			strings.NewReader(`{"account":"example", "username":"example", "password":"Flower.123", "roles":[1], "staff_id":["example1", "example2"]}`),
		)

		req.Header.Set("Content-Type", "application/json")
		c.Request = req
		err := CreateUser(c)
		t.Log(err)

		assert.NotNil(t, err)
	})

	t.Run("success", func(t *testing.T) {
		mockDb := testcommon.GetMysqlMock()
		defer mockDb.Close()
		mockDb.ExpectQuery("SELECT count(*) FROM `users` WHERE `account` = ?").
			WithArgs("example").WillReturnRows(mockDb.NewRows([]string{"count"}).
			AddRow(0))

		mockDb.ExpectQuery("SELECT count(*) FROM `roles` WHERE `id` IN (?)").
			WithArgs(1).WillReturnRows(mockDb.NewRows([]string{"count"}).
			AddRow(1))

		mockDb.ExpectBegin()
		mockDb.ExpectExec("INSERT INTO `users` (`created_at`,`updated_at`,`username`,`account`,`password`,`password_updated_at`,`status`,`rule_info`,`data_permission`) VALUES (?,?,?,?,?,?,?,?,?)").
			WillReturnResult(sqlmock.NewResult(1, 1))

		mockDb.ExpectExec("INSERT INTO `users_roles` (`created_at`,`updated_at`,`user_id`,`role_id`) VALUES (?,?,?,?)").
			WillReturnResult(sqlmock.NewResult(1, 1))

		// DELETE FROM `users_staffs` WHERE `user_id` IN (?,?)
		mockDb.ExpectExec("DELETE FROM `users_staffs` WHERE `staff_id` IN (?)").WithArgs("example").
			WillReturnResult(sqlmock.NewResult(1, 2))

		mockDb.ExpectExec("INSERT INTO `users_staffs` (`created_at`,`updated_at`,`user_id`,`staff_id`) VALUES (?,?,?,?)").
			WillReturnResult(sqlmock.NewResult(1, 1))
		mockDb.ExpectCommit()

		req := httptest.NewRequest("POST", "/api/v1/user_access/create_user", strings.NewReader(`{"account":"example", "username":"example", "password":"Flower.123", "roles":[1], "staff_id":["example"]}`))
		req.Header.Set("Content-Type", "application/json")
		c.Request = req
		err := CreateUser(c)
		assert.Empty(t, err)
	})
}

func TestUserList(t *testing.T) {
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	t.Run("Success", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()

		mockDb.ExpectQuery("SELECT count(*) FROM `users`").
			WillReturnRows(mockDb.NewRows([]string{"count"}))
		mockDb.ExpectQuery("SELECT * FROM `users` WHERE `username` like ? AND `account` like ? LIMIT 10").WithArgs("%haha%", "%haha%").
			WillReturnRows(mockDb.NewRows([]string{"id", "account", "password", "username", "status"}).
				AddRow(1, "test_account", "hashed_password", "haha", 1))

		//mockDb.ExpectQuery("SELECT * FROM `roles` WHERE `id` in (SELECT `role_id` FROM `users_roles` WHERE `user_id`=?)").
		//	WithArgs(1).
		//	WillReturnRows(mockDb.NewRows([]string{"id", "user_id", "role_id"}))
		//
		//mockDb.ExpectQuery("SELECT `staff_id` FROM `users_staffs` WHERE user_id = ?").
		//	WithArgs(1).
		//	WillReturnRows(mockDb.NewRows([]string{"id", "user_id", "staff_id"}))

		req := httptest.NewRequest("GET", "/api/v1/user_access/user_list?page=1&per_page=10&username=haha", nil)
		c.Request = req
		UserList(c)
	})

	t.Run("Param Err", func(t *testing.T) {
		req := httptest.NewRequest("GET", "/api/v1/user_access/user_list?page=1&per_pageErr=10&username=haha", nil)
		c.Request = req
		UserList(c)
	})

}

func TestDeleteByIds(t *testing.T) {
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	t.Run("Err", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()

		//mockDb.ExpectQuery("delete from `user`")

		req := httptest.NewRequest("DELETE", "/api/v1/user_access/delete_user?ids=10", nil)
		c.Request = req
		err := DeleteByIds(c)
		assert.NotEmpty(t, err)
	})
}

func TestUpdateUser(t *testing.T) {
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	t.Run("Err", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()
		mockDb.ExpectQuery("SELECT count(*) FROM `users` WHERE `id` != ? AND `account` = ?").WithArgs(26, "newAccout5").
			WillReturnRows(mockDb.NewRows([]string{"count"}))
		mockDb.ExpectQuery("SELECT count(*) FROM `roles` WHERE `id` IN (?)").WithArgs(1).
			WillReturnRows(mockDb.NewRows([]string{"count"}))

		data := `{
   			 "id": 26,
			 "account": "newAccout5",
   			 "roles": [1],
   			 "password": "sAda@@#sa1123",
   			 "username": "newAccout3",
   			 "status": 1,
 	         "staff_id": ["new1", "new2"]
		}`
		req := httptest.NewRequest("PUT", "/api/v1/user_access/update_user", strings.NewReader(data))
		req.Header.Set("Content-Type", "application/json")
		c.Request = req
		err := UpdateUser(c)
		t.Log(w.Body.String())
		assert.Equal(t, "角色不存在！", err.Error())
	})

	t.Run("success", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		//defer mockDb.Close()
		mockDb.ExpectQuery("SELECT count(*) FROM `users` WHERE `id` != ? AND `account` = ?").WithArgs(26, "newAccout5").
			WillReturnRows(mockDb.NewRows([]string{"count"}).AddRow(0))
		mockDb.ExpectQuery("SELECT count(*) FROM `roles` WHERE `id` IN (?)").WithArgs(1).
			WillReturnRows(mockDb.NewRows([]string{"count"}).AddRow(1))

		mockDb.ExpectQuery("SELECT * FROM `users` WHERE `id` = ? ORDER BY `users`.`id` LIMIT 1").WithArgs(26).
			WillReturnRows(sqlmock.NewRows([]string{"id", "account", "username", "password", "role_id", "status"}).
				AddRow(26, "abel", "abel", "abel", 1, 1))

		mockDb.ExpectBegin()
		mockDb.ExpectExec("UPDATE `users` SET").WillReturnResult(sqlmock.NewResult(1, 1))

		mockDb.ExpectExec("DELETE FROM `users_roles` WHERE `user_id` = ?").WithArgs(26).
			WillReturnResult(sqlmock.NewResult(1, 1))

		mockDb.ExpectExec("INSERT INTO `users_roles` (`created_at`,`updated_at`,`user_id`,`role_id`) VALUES (?,?,?,?)").
			WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), 26, 1). // 这里使用 AnyArg() 忽略时间戳
			WillReturnResult(sqlmock.NewResult(1, 1))

		mockDb.ExpectExec("DELETE FROM `users_staffs` WHERE `user_id` = ?").WithArgs(26).
			WillReturnResult(sqlmock.NewResult(1, 1))

		// DELETE FROM `users_staffs` WHERE `user_id` IN (?,?)
		mockDb.ExpectExec("DELETE FROM `users_staffs` WHERE `staff_id` IN (?,?)").WithArgs("new1", "new2").
			WillReturnResult(sqlmock.NewResult(1, 2))

		mockDb.ExpectExec("INSERT INTO `users_staffs` (`created_at`,`updated_at`,`user_id`,`staff_id`) VALUES (?,?,?,?)").
			WillReturnResult(sqlmock.NewResult(1, 1))
		mockDb.ExpectCommit()

		data := `{
   			 "id": 26,
			 "account": "newAccout5",
   			 "roles": [1],
   			 "password": "",
   			 "username": "newAccout3",
   			 "status": 1,
 	         "staff_id": ["new1", "new2"]
		}`
		req := httptest.NewRequest("PUT", "/api/v1/user_access/update_user", strings.NewReader(data))
		req.Header.Set("Content-Type", "application/json")
		c.Request = req

		err := UpdateUser(c)
		t.Log(err)
		assert.Equal(t, `{"code":0,"message":"更新成功","data":{}}`, w.Body.String())
	})
}

func TestRolesMenus(t *testing.T) {
	//testcommon.SetTestEnv(false)
	mockDb := testcommon.InitSqlMock()
	defer mockDb.Close()
	// 2025-02-25 19:03:05.005 INFO SQL mysql/dsl.go:162 [3.774ms] [rows:1] SELECT * FROM `roles_menus` WHERE `role_id` = 1
	// 2025-02-25 19:03:05.088 INFO SQL mysql/dsl.go:162 [2.533ms] [rows:184] SELECT * FROM `menus` WHERE `type` != 4
	mockDb.ExpectQuery("SELECT * FROM `roles` WHERE `id` = ?").
		WithArgs(1).
		WillReturnRows(mockDb.NewRows([]string{"id", "name", "key"}).
			AddRow(1, "二级管理员", "second"))
	mockDb.ExpectQuery("SELECT * FROM `roles_menus` WHERE `role_id` = ?").
		WithArgs(1).
		WillReturnRows(mockDb.NewRows([]string{"id", "role_id", "menu_id"}).
			AddRow(1, 1, 1))
	mockDb.ExpectQuery("SELECT * FROM `menus` WHERE `type` != ? AND `name` != ? ORDER BY sort asc").
		WithArgs(4, "Setting").
		WillReturnRows(mockDb.NewRows([]string{"id", "parent_id", "name", "path", "component", "icon", "sort", "type", "status", "created_at", "updated_at"}).
			AddRow(1, 0, "Dashboard", "/dashboard", "dashboard", "dashboard", 1, 1, 1, "2023-02-01 00:00:00.000", "2023-02-01 00:00:00.000").
			AddRow(2, 0, "Documentation", "/docs", "docs", "documentation", 2, 1, 1, "2023-02-01 00:00:00.000", "2023-02-01 00:00:00.000").
			AddRow(3, 0, "Example", "/example", "example", "example", 3, 1, 1, "2023-02-01 00:00:00.000", "2023-02-01 00:00:00.000").
			AddRow(4, 0, "Tools", "/tools", "tools", "tools", 4, 1, 1, "2023-02-01 00:00:00.000", "2023-02-01 00:00:00.000"))
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	req := httptest.NewRequest("GET", "/api/v1/user_access/get_role_menus?id=1", nil)
	req.Header.Set("Content-Type", "application/json")
	c.Request = req
	syncLock.Lock()
	p := gomonkey.ApplyMethodReturn(license.GetLicense(), "GetProductModels", []string{"enable_UpgradePlatform"}, nil)
	_ = RoleMenus(c)
	p.Reset()
	syncLock.Unlock()
	t.Log(w.Body.String())
	assert.Contains(t, w.Body.String(), `{"code":0,"message":"Success"`)
}
func TestUsersMenus(t *testing.T) {
	//testcommon.SetTestEnv(false)
	mockDb := testcommon.InitSqlMock()
	defer mockDb.Close()
	syncLock.Lock()
	defer syncLock.Unlock()
	p := gomonkey.ApplyMethodReturn(license.GetLicense(), "GetProductModels", []string{"enable_UpgradePlatform"}, nil)
	// 2025-02-25 18:39:05.352 INFO SQL mysql/dsl.go:162 [0.684ms] [rows:1] SELECT * FROM `users_roles` WHERE `user_id` = 1
	// 2025-02-25 18:39:05.356 INFO SQL mysql/dsl.go:197 [3.861ms] [rows:1] SELECT count(*) FROM `roles` WHERE `id` IN (1) AND `key` = 'admin'
	// 2025-02-25 18:39:05.358 INFO SQL mysql/dsl.go:162 [2.400ms] [rows:66] SELECT * FROM `menus` WHERE `type` IN (1,2)
	// 2025-02-25 18:39:05.360 INFO SQL mysql/dsl.go:162 [1.680ms] [rows:118] SELECT * FROM `menus` WHERE `type` = 3
	mockDb.ExpectQuery("SELECT * FROM `users_roles` WHERE `user_id` = ?").
		WithArgs(1).
		WillReturnRows(mockDb.NewRows([]string{"id", "user_id", "role_id"}).
			AddRow(1, 1, 1))
	mockDb.ExpectQuery("SELECT count(*) FROM `roles` WHERE `id` IN (?) AND `key` = ?").
		WithArgs(1, "admin").
		WillReturnRows(mockDb.NewRows([]string{"count"}).
			AddRow(1))
	mockDb.ExpectQuery("SELECT * FROM `menus` WHERE `type` IN (?,?)").
		WithArgs(1, 2).
		WillReturnRows(mockDb.NewRows([]string{"id", "parent_id", "title", "path", "icon", "type", "sort", "status"}).
			AddRow(1, 0, "系统管理", "/system", "el-icon-setting", 1, 1, 1).
			AddRow(2, 1, "用户管理", "/system/user", "el-icon-user", 2, 1, 1).
			AddRow(3, 1, "角色管理", "/system/role", "el-icon-user-solid", 3, 1, 1).
			AddRow(4, 1, "菜单管理", "/system/menu", "el-icon-menu", 4, 1, 1))
	mockDb.ExpectQuery("SELECT * FROM `menus` WHERE `type` = ?").
		WithArgs(3).
		WillReturnRows(mockDb.NewRows([]string{"id", "parent_id", "title", "path", "icon", "type", "sort", "status"}).
			AddRow(5, 4, "添加", "/system", "el-icon-setting", 3, 1, 1).
			AddRow(6, 4, "编辑", "/system/user", "el-icon-user", 3, 1, 1))
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Set("user_id", uint64(1))
	req := httptest.NewRequest("GET", "/api/v1/user_access/my_menus_tree", nil)
	req.Header.Set("Content-Type", "application/json")
	c.Request = req

	_ = MyMenusTree(c)
	t.Log(w.Body.String())
	assert.Contains(t, w.Body.String(), `{"code":0,"message":"Success"`)

	// 2025-02-25 18:39:05.352 INFO SQL mysql/dsl.go:162 [0.684ms] [rows:1] SELECT * FROM `users_roles` WHERE `user_id` = 1
	// 2025-02-25 18:39:05.356 INFO SQL mysql/dsl.go:197 [3.861ms] [rows:1] SELECT count(*) FROM `roles` WHERE `id` IN (1) AND `key` = 'admin'
	// 2025-02-25 18:39:05.358 INFO SQL mysql/dsl.go:162 [2.400ms] [rows:66] SELECT * FROM `menus` WHERE `type` IN (1,2)
	// 2025-02-25 18:39:05.360 INFO SQL mysql/dsl.go:162 [1.680ms] [rows:118] SELECT * FROM `menus` WHERE `type` = 3
	mockDb.ExpectQuery("SELECT * FROM `users_roles` WHERE `user_id` = ?").
		WithArgs(1).
		WillReturnRows(mockDb.NewRows([]string{"id", "user_id", "role_id"}).
			AddRow(1, 1, 1))
	mockDb.ExpectQuery("SELECT count(*) FROM `roles` WHERE `id` IN (?) AND `key` = ?").
		WithArgs(1, "admin").
		WillReturnRows(mockDb.NewRows([]string{"count"}).
			AddRow(0))
	mockDb.ExpectQuery("SELECT * FROM `users_roles` WHERE `user_id` = ?").
		WithArgs(1).
		WillReturnRows(mockDb.NewRows([]string{"id", "user_id", "role_id"}).
			AddRow(1, 1, 1))
	// SELECT * FROM `roles_menus` WHERE `role_id` IN (?)
	mockDb.ExpectQuery("SELECT * FROM `roles_menus` WHERE `role_id` IN (?)").
		WithArgs(1).
		WillReturnRows(mockDb.NewRows([]string{"id", "role_id", "menu_id"}).
			AddRow(1, 1, 1).AddRow(2, 1, 2).AddRow(3, 1, 3).AddRow(4, 1, 4))
	// SELECT * FROM `menus` WHERE `id` IN (1,2,3,4) AND `type` IN (1,2)
	mockDb.ExpectQuery("SELECT * FROM `menus` WHERE `id` IN (?,?,?,?) AND `type` IN (?,?)").
		WithArgs(1, 2, 3, 4, 1, 2).
		WillReturnRows(mockDb.NewRows([]string{"id", "parent_id", "title", "path", "icon", "type", "sort", "status"}).
			AddRow(1, 0, "系统管理", "/system", "el-icon-setting", 1, 1, 1).
			AddRow(2, 1, "用户管理", "/system/user", "el-icon-user", 2, 1, 1).
			AddRow(4, 1, "菜单管理", "/system/menu", "el-icon-menu", 2, 1, 1))
	// SELECT * FROM `menus` WHERE `id` IN (1,2,3,4) AND `type` IN (3)
	mockDb.ExpectQuery("SELECT * FROM `menus` WHERE `id` IN (?,?,?,?) AND `type` IN (?)").
		WithArgs(1, 2, 3, 4, 3).
		WillReturnRows(mockDb.NewRows([]string{"id", "parent_id", "title", "path", "icon", "type", "sort", "status"}).
			AddRow(3, 1, "角色管理", "/system/role", "el-icon-user-solid", 3, 1, 1))

	w = httptest.NewRecorder()
	c, _ = gin.CreateTestContext(w)
	c.Set("user_id", uint64(1))
	req = httptest.NewRequest("GET", "/api/v1/user_access/my_menus_tree", nil)
	req.Header.Set("Content-Type", "application/json")
	c.Request = req

	_ = MyMenusTree(c)
	p.Reset()
	t.Log(w.Body.String())
	assert.Contains(t, w.Body.String(), `{"code":0,"message":"Success"`)
}

func TestAllMenus(t *testing.T) {
	//testcommon.SetTestEnv(false)
	mockDb := testcommon.InitSqlMock()
	defer mockDb.Close()
	// 2025-02-25 18:39:05.352 INFO SQL mysql/dsl.go:162 [0.684ms] [rows:1] SELECT * FROM `menus`
	mockDb.ExpectQuery("SELECT * FROM `menus`").
		WillReturnRows(mockDb.NewRows([]string{"id", "parent_id", "title", "path", "icon", "type", "sort", "status"}).
			AddRow(1, 0, "系统管理", "/system", "el-icon-setting", 1, 1, 1).
			AddRow(2, 1, "用户管理", "/system/user", "el-icon-user", 2, 1, 1))

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Set("user_id", uint64(1))
	req := httptest.NewRequest("GET", "/api/v1/user_access/all_menus_tree", nil)
	req.Header.Set("Content-Type", "application/json")
	c.Request = req
	syncLock.Lock()
	defer syncLock.Unlock()
	p := gomonkey.ApplyMethodReturn(license.GetLicense(), "GetProductModels", []string{"enable_UpgradePlatform"}, nil)
	_ = MenusTree(c)
	p.Reset()
	t.Log(w.Body.String())
	assert.Contains(t, w.Body.String(), `{"code":0,"message":"Success"`)
}
func TestRolesAdd(t *testing.T) {
	//testcommon.SetTestEnv(false)
	mockDb := testcommon.InitSqlMock()
	defer mockDb.Close()
	// 2025-02-25 19:33:38.080 INFO SQL mysql/dsl.go:197 [0.776ms] [rows:1] SELECT count(*) FROM `roles` WHERE `key` = 'autotest' OR `name` = '自动测试1'
	//2025-02-25 19:33:38.087 INFO SQL mysql/dsl.go:270 [6.695ms] [rows:1] INSERT INTO `roles` (`created_at`,`updated_at`,`name`,`key`,`status`,`remark`,`sys`) VALUES ('2025-02-25 19:33:38.081','2025-02-25 19:33:38.081','自动测试1','autotest',1,'单测',0)
	//2025-02-25 19:33:38.095 INFO SQL mysql/dsl.go:285 [7.919ms] [rows:15] INSERT INTO `roles_menus` (`created_at`,`updated_at`,`role_id`,`menu_id`) VALUES ('2025-02-25 19:33:38.088','2025-02-25 19:33:38.088',3,1),('2025-02-25 19:33:38.088','2025-02-25 19:33:38.088',3,2),('2025-02-25 19:33:38.088','2025-02-25 19:33:38.088',3,3),('2025-02-25 19:33:38.088','2025-02-25 19:33:38.088',3,4),('2025-02-25 19:33:38.088','2025-02-25 19:33:38.088',3,5),('2025-02-25 19:33:38.088','2025-02-25 19:33:38.088',3,6),('2025-02-25 19:33:38.088','2025-02-25 19:33:38.088',3,7),('2025-02-25 19:33:38.088','2025-02-25 19:33:38.088',3,8),('2025-02-25 19:33:38.088','2025-02-25 19:33:38.088',3,9),('2025-02-25 19:33:38.088','2025-02-25 19:33:38.088',3,10),('2025-02-25 19:33:38.088','2025-02-25 19:33:38.088',3,11),('2025-02-25 19:33:38.088','2025-02-25 19:33:38.088',3,12),('2025-02-25 19:33:38.088','2025-02-25 19:33:38.088',3,13),('2025-02-25 19:33:38.088','2025-02-25 19:33:38.088',3,14),('2025-02-25 19:33:38.088','2025-02-25 19:33:38.088',3,15)
	mockDb.ExpectQuery("SELECT count(*) FROM `roles` WHERE `name` = ?").
		WithArgs("自动测试1").
		WillReturnRows(mockDb.NewRows([]string{"count"}).
			AddRow(0))
	mockDb.ExpectBegin()
	mockDb.ExpectExec("INSERT INTO `roles` (`created_at`,`updated_at`,`name`,`key`,`status`,`remark`,`sys`) VALUES (?,?,?,?,?,?,?)").
		WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), "自动测试1", "autotest", 1, "单测", 0).
		WillReturnResult(sqlmock.NewResult(3, 1))
	mockDb.ExpectCommit()
	mockDb.ExpectBegin()
	mockDb.ExpectExec("INSERT INTO `roles_menus` (`created_at`,`updated_at`,`role_id`,`menu_id`) VALUES (?,?,?,?)").
		WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), 3, 1).
		WillReturnResult(sqlmock.NewResult(1, 1))
	mockDb.ExpectCommit()
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Set("user_id", uint64(1))

	data := `{
			 "name": "自动测试1",
   			 "key": "autotest",
   			 "remark": "单测",
   			 "menus":[1]
		}`

	req := httptest.NewRequest("POST", "/api/v1/user_access/roles", strings.NewReader(data))
	req.Header.Set("Content-Type", "application/json")
	c.Request = req

	_ = RolesAdd(c)
	t.Log(w.Body.String())
	assert.Contains(t, w.Body.String(), `{"code":0,"message":"Success"`)
}
func TestRolesUpdate(t *testing.T) {
	//testcommon.SetTestEnv(false)
	mockDb := testcommon.InitSqlMock()
	defer mockDb.Close()

	// 2025-02-25 19:54:03.393 INFO SQL mysql/dsl.go:197 [1.012ms] [rows:1] SELECT count(*) FROM `roles` WHERE `id` != 3 AND (`key` = 'autotest' OR `name` = '自动测试3')
	//2025-02-25 19:54:03.395 INFO SQL mysql/dsl.go:183 [0.364ms] [rows:1] SELECT * FROM `roles` WHERE `id` = 3 ORDER BY `roles`.`id` LIMIT 1
	//2025-02-25 19:54:03.400 INFO SQL mysql/dsl.go:227 [4.717ms] [rows:1] UPDATE `roles` SET `updated_at`='2025-02-25 19:54:03.395',`name`='自动测试3',`key`='autotest',`status`=1,`remark`='单测3' WHERE `id` = 3
	//2025-02-25 19:54:03.412 INFO SQL mysql/dsl.go:259 [11.944ms] [rows:15] DELETE FROM `roles_menus` WHERE `role_id` = 3
	//2025-02-25 19:54:03.418 INFO SQL mysql/dsl.go:285 [5.836ms] [rows:1] INSERT INTO `roles_menus` (`created_at`,`updated_at`,`role_id`,`menu_id`) VALUES ('2025-02-25 19:54:03.412','2025-02-25 19:54:03.412',3,2)
	//2025-02-25 19:54:03.433 INFO SQL mysql/dsl.go:197 [15.183ms] [rows:1] SELECT count(*) FROM `users_roles` WHERE `role_id` = 3
	mockDb.ExpectQuery("SELECT count(*) FROM `roles` WHERE `id` != ? AND `name` = ?").
		WithArgs(3, "自动测试3").
		WillReturnRows(mockDb.NewRows([]string{"count"}).
			AddRow(0))
	mockDb.ExpectQuery("SELECT * FROM `roles` WHERE `id` = ? ORDER BY `roles`.`id` LIMIT 1").
		WithArgs(3).
		WillReturnRows(mockDb.NewRows([]string{"id", "name", "key", "status", "remark", "sys"}).
			AddRow(3, "自动测试3", "autotest", 1, "单测3", 0))
	mockDb.ExpectBegin()
	mockDb.ExpectExec("UPDATE `roles` SET `updated_at`=?,`name`=?,`key`=?,`status`=?,`remark`=? WHERE `id` = ?").
		WithArgs(sqlmock.AnyArg(), "自动测试3", "autotest", 1, "单测3", 3).
		WillReturnResult(sqlmock.NewResult(1, 1))
	mockDb.ExpectCommit()
	mockDb.ExpectBegin()
	mockDb.ExpectExec("DELETE FROM `roles_menus` WHERE `role_id` = ?").
		WithArgs(3).
		WillReturnResult(sqlmock.NewResult(1, 1))
	mockDb.ExpectCommit()
	mockDb.ExpectBegin()
	mockDb.ExpectExec("INSERT INTO `roles_menus` (`created_at`,`updated_at`,`role_id`,`menu_id`) VALUES (?,?,?,?)").
		WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), 3, 2).
		WillReturnResult(sqlmock.NewResult(1, 1))
	mockDb.ExpectCommit()
	mockDb.ExpectQuery("SELECT count(*) FROM `users_roles` WHERE `role_id` = ?").
		WithArgs(3).
		WillReturnRows(mockDb.NewRows([]string{"count"}).
			AddRow(0))
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Set("user_id", uint64(1))

	data := `{
			 "id":3,
			 "name": "自动测试3",
   			 "key": "autotest",
   			 "remark": "单测3",
			 "status":1,
   			 "menus":[2]
		}`

	req := httptest.NewRequest("PUT", "/api/v1/user_access/roles", strings.NewReader(data))
	req.Header.Set("Content-Type", "application/json")
	c.Request = req

	_ = RolesUpdate(c)
	t.Log(w.Body.String())
	assert.Contains(t, w.Body.String(), `{"code":0,"message":"Success"`)
}

func TestGetDataPermissionLevelMap(t *testing.T) {
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Set("user_id", uint64(1))
	req := httptest.NewRequest("GET", "/api/v1/user_access/data_permission_levels", nil)
	req.Header.Set("Content-Type", "application/json")
	c.Request = req

	_ = GetDataPermissionLevelMap(c)
	t.Log(w.Body.String())
	assert.Contains(t, w.Body.String(), `{"code":0,"message":"Success"`)
}
func TestBatchSwitchUsersStatus(t *testing.T) {
	mockDb := testcommon.InitSqlMock()
	defer mockDb.Close()

	mockDb.ExpectBegin()
	mockDb.ExpectExec("UPDATE `users` SET `status`=?,`updated_at`=? WHERE `id` IN (?,?,?)").
		WithArgs(1, sqlmock.AnyArg(), 1, 2, 3).
		WillReturnResult(sqlmock.NewResult(0, 3))
	mockDb.ExpectCommit()

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Set("user_id", uint64(1))

	data := `{
			 "ids":[1,2,3],
			 "type":1
		}`

	req := httptest.NewRequest("POST", "/api/v1/user_access/user_switch", strings.NewReader(data))
	req.Header.Set("Content-Type", "application/json")
	c.Request = req

	_ = BatchSwitchUsersStatus(c)
	t.Log(w.Body.String())
	assert.Contains(t, w.Body.String(), `{"code":0,"message":"操作成功"`)
}

func TestUpdateUserRuleInfoConfig(t *testing.T) {
	mockDb := testcommon.InitSqlMock()
	defer mockDb.Close()

	// SELECT * FROM `users` WHERE id =? ORDER BY `users`.`id` LIMIT 1
	mockDb.ExpectQuery("SELECT * FROM `users` WHERE id =? ORDER BY `users`.`id` LIMIT 1").
		WithArgs(1).
		WillReturnRows(mockDb.NewRows([]string{"id", "account", "username", "password", "rule_info", "status"}).
			AddRow(1, "admin", "admin", "1", "[]", 1))

	mockDb.ExpectBegin()
	mockDb.ExpectExec("UPDATE `users` SET `rule_info`=?,`updated_at`=? WHERE `id` = ?").
		WithArgs("{\"range\": 0, \"rule_info\": [\"业务层\", \"支撑层\", \"服务层\", \"系统层\", \"硬件层\"]}", sqlmock.AnyArg(), 1).
		WillReturnResult(sqlmock.NewResult(1, 1))
	mockDb.ExpectCommit()

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Set("user_id", uint64(1))

	data := `{"rule_info":"{\"range\": 0, \"rule_info\": [\"业务层\", \"支撑层\", \"服务层\", \"系统层\", \"硬件层\"]}"}`

	req := httptest.NewRequest("PUT", "/api/v1/statistical/rule_info/level/config", strings.NewReader(data))
	req.Header.Set("Content-Type", "application/json")
	c.Request = req

	_ = UpdateUserRuleInfoConfig(c)
	t.Log(w.Body.String())
	assert.Contains(t, w.Body.String(), `{"code":0,"message":"更新成功"`)
}
