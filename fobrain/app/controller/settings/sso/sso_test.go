package sso

import (
	"io/ioutil"
	"net/http/httptest"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"

	testcommon "fobrain/fobrain/tests/common_test"
)

func Test_ChangedDetails(t *testing.T) {
	t.Run("params err", func(t *testing.T) {
		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/settings/sso", nil)
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		// 调用函数
		err := ChangedDetails(c)

		// 断言错误为 nil
		assert.Nil(t, err)

		// 解析响应数据
		response := w.Result()
		defer response.Body.Close()
		body, _ := ioutil.ReadAll(response.Body)

		// 断言响应不为空
		assert.NotEmpty(t, body)
	})

	t.Run("success", func(t *testing.T) {
		mockDb := testcommon.GetMysqlMock()
		defer mockDb.Close()
		mockDb.ExpectBegin()
		mockDb.ExpectExec("UPDATE `system_configs` SET `value`=? WHERE `key` = ?").WillReturnResult(sqlmock.NewResult(1, 1))
		mockDb.ExpectCommit()
		mockDb.ExpectBegin()
		mockDb.ExpectExec("UPDATE `system_configs` SET `value`=? WHERE `key` = ?").WillReturnResult(sqlmock.NewResult(1, 1))
		mockDb.ExpectCommit()
		mockDb.ExpectBegin()
		mockDb.ExpectExec("UPDATE `system_configs` SET `value`=? WHERE `key` = ?").WillReturnResult(sqlmock.NewResult(1, 1))
		mockDb.ExpectCommit()
		mockDb.ExpectBegin()
		mockDb.ExpectExec("UPDATE `system_configs` SET `value`=? WHERE `key` = ?").WillReturnResult(sqlmock.NewResult(1, 1))
		mockDb.ExpectCommit()
		mockDb.ExpectBegin()
		mockDb.ExpectExec("UPDATE `system_configs` SET `value`=? WHERE `key` = ?").WillReturnResult(sqlmock.NewResult(1, 1))
		mockDb.ExpectCommit()
		mockDb.ExpectBegin()
		mockDb.ExpectExec("UPDATE `system_configs` SET `value`=? WHERE `key` = ?").WillReturnResult(sqlmock.NewResult(1, 1))
		mockDb.ExpectCommit()

		w := httptest.NewRecorder()
		req := httptest.NewRequest("PUT", "/settings/sso?auth_url=1&token_url=2&client_id=3&client_secret=4&sso_server=github&sso_enable=true", nil)
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		// 调用函数
		err := ChangedDetails(c)

		// 断言错误为 nil
		assert.Nil(t, err)

	})
}
