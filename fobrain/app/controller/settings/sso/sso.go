package sso

import (
	"github.com/gin-gonic/gin"

	"fobrain/fobrain/app/repository/settings/sso"
	"fobrain/fobrain/common/request"
	"fobrain/fobrain/common/response"
)

// GetDetails
// @Summary: 获取SSO详情
// @Routes /api/v1/settings/sso [GET]
func GetDetails(c *gin.Context) error {
	return response.OkWithData(c, sso.GetDetails())
}

// ChangedDetails
// @Summary: 修改SSO详情
// @Routes /api/v1/settings/sso [PUT]
func ChangedDetails(c *gin.Context) error {
	var param struct {
		AuthUrl      string `json:"auth_url" form:"auth_url"`
		TokenUrl     string `json:"token_url" form:"token_url"`
		ClientId     string `json:"client_id" binding:"required" form:"client_id"`
		ClientSecret string `json:"client_secret" binding:"required" form:"client_secret"`
		SsoEnable    string `json:"sso_enable" form:"sso_enable"`
		SsoServer    string `json:"sso_server" form:"sso_server"`
		LoginInfoUrl string `json:"login_info_url" form:"login_info_url"` // 单点登录后，需要获取用户信息的地址
		SSOCallback  string `json:"sso_callback" form:"sso_callback"`     // 单点登录平台回调应用的地址
		Tenant       string `json:"tenant" form:"tenant"`                 // 租户, 4A平台必填
		RoleID       string `json:"sso_role" form:"sso_role"`             // 角色ID
	}

	params, err := request.Validate(c, &param)
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}

	err = sso.ChangedDetails(
		params.AuthUrl, params.TokenUrl, params.ClientId,
		params.ClientSecret, params.SsoEnable, params.SsoServer,
		params.LoginInfoUrl, params.SSOCallback, params.Tenant, param.RoleID)
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}

	return response.Ok(c)
}

func ServerList(c *gin.Context) error {
	return response.OkWithData(c, sso.ServerList())
}
