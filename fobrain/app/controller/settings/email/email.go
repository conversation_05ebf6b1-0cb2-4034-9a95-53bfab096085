package email

import (
	"fobrain/fobrain/app/repository/email"
	"fobrain/fobrain/common/request"
	"fobrain/fobrain/common/response"

	"github.com/gin-gonic/gin"
)

// Infos 邮件配置信息
func Infos(c *gin.Context) error {
	info, err := email.Get()
	if err != nil {
		return err
	}

	return response.OkWithData(c, info)
}

func Save(c *gin.Context) error {
	var param struct {
		Address     string `json:"address" form:"address" binding:"required"`
		Port        uint64 `json:"port" form:"port" binding:"required"`
		EncryptType uint8  `json:"encrypt_type" form:"encrypt_type" binding:"required"`
		UserName    string `json:"user_name" form:"user_name" binding:"required"`
		Password    string `json:"password" form:"password" binding:"required"`
	}
	params, err := request.Validate(c, &param)
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}

	err = email.Save(params.Address, params.Port, params.EncryptType, params.UserName, params.Password, "SMTP")
	if err != nil {
		return err
	}

	return response.Ok(c)
}

func TestMail(c *gin.Context) error {
	err := email.TestMail()
	if err != nil {
		return err
	}

	return response.Ok(c)
}
