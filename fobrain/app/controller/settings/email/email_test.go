package email

import (
	testcommon "fobrain/fobrain/tests/common_test"
	"github.com/DATA-DOG/go-sqlmock"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"
	"net/http/httptest"
	"strings"
	"testing"
)

func TestSave(t *testing.T) {
	t.Run("params err", func(t *testing.T) {
		// 创建一个测试请求和响应
		w := httptest.NewRecorder()
		req := httptest.NewRequest("POST", "/api/v1/settings/email", strings.NewReader(`{"address":"123","port":123,"encrypt_type":1,"user_name":"123","password":"123"}`))
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		// 调用函数
		_ = Save(c)

		assert.Equal(t, 400, w.Code)
	})

	t.Run("success", func(t *testing.T) {
		mockDb := testcommon.GetMysqlMock()
		defer mockDb.Close()
		mockDb.ExpectQuery("SELECT * FROM `email_config` ORDER BY `email_config`.`id` LIMIT 1").
			WillReturnRows(
				sqlmock.NewRows(
					[]string{
						"id", "address", "port", "encrypt_type", "user_name", "password",
						"category", "created_at", "updated_at"},
				).AddRow(1, "smtp.exmail.qq.com", 465, 2, "<EMAIL>",
					"W4jdTHMQKZy5fiGy", "smtp", "2024-10-10 14:44:11", "2024-10-10 14:44:11"),
			)

		// 创建一个测试请求和响应
		w := httptest.NewRecorder()
		req := httptest.NewRequest("POST", "/api/v1/settings/email?address=123&port=123&encrypt_type=1&user_name=123&password=123", nil)
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		// 调用函数
		_ = Save(c)

		assert.Equal(t, 200, w.Code)
	})

	t.Run("first save success", func(t *testing.T) {
		mockDb := testcommon.GetMysqlMock()
		defer mockDb.Close()

		mockDb.ExpectQuery("SELECT * FROM `email_config` ORDER BY `email_config`.`id` LIMIT 1").
			WillReturnError(gorm.ErrRecordNotFound)

		// 创建一个测试请求和响应
		w := httptest.NewRecorder()
		req := httptest.NewRequest("POST", "/api/v1/settings/email?address=123&port=123&encrypt_type=1&user_name=123&password=123", nil)
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		// 调用函数
		_ = Save(c)

		assert.Equal(t, 200, w.Code)
	})
}
