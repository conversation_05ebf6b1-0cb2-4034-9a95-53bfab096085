package network_areas

import (
	"github.com/gin-gonic/gin"

	areasServer "fobrain/fobrain/app/repository/settings/network_areas"
	"fobrain/fobrain/common/request"
	"fobrain/fobrain/common/response"
	"fobrain/initialize/mysql"
	"fobrain/models/mysql/network_areas"
	"fobrain/pkg/utils"
)

type (
	NetWorkAreasNameRequest struct {
		request.PageRequest
		Keyword string `json:"keyword" form:"keyword" uri:"keyword" validate:"omitempty,max=50" zh:"模糊搜索"`
	}
	NetWorkAreasIdsRequest struct {
		Id uint64 `json:"id" form:"id" uri:"id" validate:"required,db=network_areas?_rule=exists&id" zh:"网络区域ID"`
	}
	NetWorkAreasRequest struct {
		Name        string `json:"name" form:"name" uri:"name" validate:"required,db=network_areas?_rule=not_exists&name,max=50" zh:"网络区域名称"`
		GEOId       uint64 `json:"geo_id" form:"geo_id" uri:"geo_id" validate:"omitempty,gte=1,db=geo_areas?_rule=exists&id" zh:"省市ID"`
		ISPId       uint64 `json:"isp_id" form:"isp_id" uri:"isp_id" validate:"omitempty,gte=1,db=isp_areas?_rule=exists&id" zh:"等保区域ID"`
		NetworkType int    `json:"network_type" form:"network_type" uri:"network_type" validate:"required,oneof=1 2" zh:"网络类型"`
		MachineRoom string `json:"machine_room" form:"machine_room" uri:"machine_room" validate:"omitempty,max=100" zh:"所属机房"`
	}
)

var NetWorkAreasNameValidate = request.Validate[NetWorkAreasNameRequest]
var NetWorkAreasIdValidate = request.Validate[NetWorkAreasIdsRequest]
var NetworkAreasValidate = request.Validate[NetWorkAreasRequest]

// List 网络区域列表
func List(c *gin.Context) error {
	params, err := NetWorkAreasNameValidate(c, &NetWorkAreasNameRequest{})
	if err != nil {
		return err
	}
	handlers := make([]mysql.HandleFunc, 0)
	handlers = append(handlers, mysql.WithOrder("created_at DESC"))
	handlers = utils.CanAppend(params.Keyword != "", handlers, mysql.WithLike("name", params.Keyword))
	list, total, err := network_areas.NewNetworkAreaModel().List(params.Page, params.PerPage, handlers...)
	if err != nil {
		return err
	}
	return response.OkWithPageData(c, total, params.Page, params.PerPage, list)
}

// Delete 删除网络区域
func Delete(c *gin.Context) error {
	params, err := NetWorkAreasIdValidate(c, &NetWorkAreasIdsRequest{})
	if err != nil {
		return err
	}
	//检测网络区域是否可删除
	used, err := areasServer.CheckNetworkAreasIsUsed(params.Id)
	if err != nil {
		return err
	}
	if used {
		return response.FailWithMessage(c, "数据源或资产漏洞数据在使用该网域，无法删除")
	}
	err = network_areas.NewNetworkAreaModel().DeleteById(params.Id)
	if err != nil {
		return err
	}
	return response.OkWithMessage(c, "网络区域删除成功")
}

// Create 创建网络区域
func Create(c *gin.Context) error {
	params, err := NetworkAreasValidate(c, &NetWorkAreasRequest{})
	if err != nil {
		return err
	}
	item := network_areas.NetworkArea{
		Name:        params.Name,
		GEOId:       params.GEOId,
		ISPId:       params.ISPId,
		NetworkType: params.NetworkType,
		MachineRoom: params.MachineRoom,
		Source:      1,
	}
	item.Name = params.Name
	err = network_areas.NewNetworkAreaModel().Create(&item)
	if err != nil {
		return err
	}
	return response.OkWithMessage(c, "添加成功")
}

// IspList 获取等保区域列表
func IspList(c *gin.Context) error {
	list, err := areasServer.NetworkIspList()
	if err != nil {
		return err
	}
	return response.OkWithData(c, list)
}

// GeoList 获取省市列表
func GeoList(c *gin.Context) error {
	list, err := areasServer.NetworkGeoTree()
	if err != nil {
		return err
	}
	return response.OkWithData(c, list)
}
