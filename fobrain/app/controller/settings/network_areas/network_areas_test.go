package network_areas

import (
	"bytes"
	"encoding/json"
	"errors"
	"io/ioutil"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/gin-gonic/gin"
	"github.com/olivere/elastic/v7"
	"github.com/stretchr/testify/assert"

	areasServer "fobrain/fobrain/app/repository/settings/network_areas"
	resp "fobrain/fobrain/app/response/network_areas"
	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/models/mysql/network_areas"
)

func TestList(t *testing.T) {
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()
	//测试数据
	data := network_areas.NetworkArea{
		Name:        "默认",
		GEOId:       1,
		ISPId:       1,
		NetworkType: 1,
		Source:      1,
		MachineRoom: "测试机房",
	}
	mockDb.ExpectQuery("SELECT count(*) FROM `network_areas`").
		WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(1))

	mockDb.ExpectQuery("SELECT * FROM `network_areas` ORDER BY created_at DESC LIMIT 10").
		WillReturnRows(sqlmock.NewRows([]string{"id", "name", "geo_id", "isp_id", "network_type", "machine_room"}).AddRow(1, data.Name, data.GEOId, data.ISPId, data.NetworkType, data.MachineRoom))
	// 创建一个测试请求和响应
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/settings/network_areas?page=1&per_page=10", nil)
	// 创建一个Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 调用函数
	err := List(c)

	// 断言错误为 nil
	assert.NoError(t, err)

	// 解析响应数据
	response := w.Result()
	defer response.Body.Close()
	body, _ := ioutil.ReadAll(response.Body)

	// 断言响应不为空
	assert.NotEmpty(t, body)
}

//func TestCreate(t *testing.T) {
//	mockDb := testcommon.GetMysqlMock()
//	defer mockDb.Close()
//	networkAreas := &NetWorkAreasRequest{
//		Name:        "测试",
//		GEOId:       0,
//		ISPId:       0,
//		NetworkType: 1,
//		MachineRoom: "测试机房",
//	}
//	jsonData, _ := json.Marshal(networkAreas)
//	mockDb.Mock.ExpectBegin()
//	mockDb.ExpectQuery("SELECT count(*) FROM `network_areas` WHERE name = ?").WithArgs("测试").
//		WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(0))
//
//	mockDb.ExpectExec("INSERT INTO `network_areas` (`created_at`,`updated_at`,`name`,`geo_id`,`isp_id`,`network_type`,`machine_room`) VALUES (?,?,?,?,?,?,?)").
//		WithArgs(time.Now(), time.Now(), "name", "测试", "geo_id", 1, "isp_id", 1, "network_type", 1, "machine_room", "测试机房").
//		WillReturnResult(sqlmock.NewResult(1, 1)) // Assuming one row affected with ID 1
//
//	mockDb.Mock.ExpectCommit()
//	// 创建一个测试请求和响应
//	w := httptest.NewRecorder()
//	req := httptest.NewRequest("POST", "/settings/network_areas", strings.NewReader(string(jsonData)))
//	//req.Header.Set("Authorization", tests.GetUserToken(1))
//	req.Header.Set("Content-Type", "application/json")
//	// 创建一个Gin上下文
//	c, _ := gin.CreateTestContext(w)
//	c.Request = req
//
//	// 调用函数
//	err := Create(c)
//
//	// 断言错误为 nil
//	assert.NoError(t, err)
//
//	// 解析响应数据
//	response := w.Result()
//	defer response.Body.Close()
//	body, _ := ioutil.ReadAll(response.Body)
//
//	// 断言响应不为空
//	assert.NotEmpty(t, body)
//}

func TestCreateError(t *testing.T) {
	testCases := []struct {
		name          string
		requestData   NetWorkAreasRequest
		expectedError string
	}{
		{
			name: "Exist NetworkName",
			requestData: NetWorkAreasRequest{
				Name:        "",
				NetworkType: 1,
			},
			expectedError: "网络区域名称为必填字段",
		},
		{
			name: "Invalid NetworkType",
			requestData: NetWorkAreasRequest{
				Name:        "测试网络区域",
				NetworkType: 3, // 不在1,2范围内
			},
			expectedError: "网络区域名称已存在",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 设置mock数据库
			mockDb := testcommon.GetMysqlMock()
			defer mockDb.Close()

			// 创建测试请求和响应
			w := httptest.NewRecorder()
			requestBody, _ := json.Marshal(tc.requestData)
			req := httptest.NewRequest("POST", "/settings/network_areas", bytes.NewBuffer(requestBody))
			req.Header.Set("Content-Type", "application/json")

			// 创建Gin测试上下文
			c, _ := gin.CreateTestContext(w)
			c.Request = req

			// 调用Create方法
			err := Create(c)

			// 验证错误
			assert.Error(t, err)
			assert.Contains(t, err.Error(), tc.expectedError)
		})
	}
}

func TestDelete(t *testing.T) {
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	mockServer.Register("/asset,poc/_count", []*elastic.SearchHit{})
	mockDb.ExpectQuery("SELECT count(*) FROM `network_areas` WHERE id = ?").WithArgs(1).
		WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(1))

	mockDb.ExpectQuery("SELECT count(*) FROM `data_nodes` WHERE area = ?").WithArgs(1).
		WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(1))

	mockDb.ExpectExec("DELETE FROM `network_areas` WHERE id = ?").WithArgs(1).
		WillReturnResult(sqlmock.NewResult(0, 1)) // 假设有1行被删除
	// 创建一个测试请求和响应
	w := httptest.NewRecorder()
	req := httptest.NewRequest("DELETE", "/settings/network_areas?id=1", nil)
	// 创建一个Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 调用函数
	err := Delete(c)

	// 断言错误为 nil
	assert.NoError(t, err)

	// 解析响应数据
	response := w.Result()
	defer response.Body.Close()
	body, _ := ioutil.ReadAll(response.Body)

	// 断言响应不为空
	assert.NotEmpty(t, body)
}

func TestIspList(t *testing.T) {
	isp := []resp.NetworkAreaIsp{
		{
			ID:   1,
			Name: "aa",
		},
	}
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyFuncReturn(areasServer.NetworkIspList, isp, nil).Reset()
	// 创建一个测试请求和响应
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/settings/network_areas/isp", nil)
	// 创建一个Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 调用函数
	err := IspList(c)

	// 断言错误为 nil
	assert.NoError(t, err)

	// 解析响应数据
	response := w.Result()
	defer response.Body.Close()
	body, _ := ioutil.ReadAll(response.Body)

	// 断言响应不为空
	assert.NotEmpty(t, body)
}

func TestIspListError(t *testing.T) {
	patch := gomonkey.ApplyFuncReturn(areasServer.NetworkIspList, nil, errors.New("err"))
	// 创建一个测试请求和响应
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/settings/network_areas/isp", nil)
	// 创建一个Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 调用函数
	err := IspList(c)

	assert.Error(t, err)

	// 解析响应数据
	response := w.Result()
	defer response.Body.Close()
	body, _ := ioutil.ReadAll(response.Body)

	patch.Reset()
	// 断言响应不为空
	assert.Empty(t, body)
}

func TestGeoList(t *testing.T) {
	isp := []*resp.GEOAreaNode{
		{
			ID:   1,
			Name: "aa",
		},
	}
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyFuncReturn(areasServer.NetworkGeoTree, isp, nil).Reset()
	// 创建一个测试请求和响应
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/settings/network_areas/geo", nil)
	// 创建一个Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 调用函数
	err := GeoList(c)

	// 断言错误为 nil
	assert.NoError(t, err)

	// 解析响应数据
	response := w.Result()
	defer response.Body.Close()
	body, _ := ioutil.ReadAll(response.Body)

	// 断言响应不为空
	assert.NotEmpty(t, body)
}

func TestGeoListError(t *testing.T) {
	patch := gomonkey.ApplyFuncReturn(areasServer.NetworkGeoTree, nil, errors.New("err"))
	// 创建一个测试请求和响应
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/settings/network_areas/geo", nil)
	// 创建一个Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 调用函数
	err := GeoList(c)

	// 断言错误为 nil
	assert.Error(t, err)

	// 解析响应数据
	response := w.Result()
	defer response.Body.Close()
	body, _ := ioutil.ReadAll(response.Body)

	patch.Reset()
	// 断言响应不为空
	assert.Empty(t, body)
}
