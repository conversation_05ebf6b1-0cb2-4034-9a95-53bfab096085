package license

import (
	"bytes"
	"encoding/json"
	"net/http/httptest"
	"testing"

	"fobrain/fobrain/common/license"
	"fobrain/pkg/cfg"
	"fobrain/pkg/utils"

	. "github.com/agiledragon/gomonkey/v2"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func TestActivation(t *testing.T) {
	cfg.InitLoadCfg()
	t.Run("code params err", func(t *testing.T) {
		// 创建一个测试请求和响应
		w := httptest.NewRecorder()
		req := httptest.NewRequest("POST", "/api/v1/settings/license", nil)
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		// 调用函数
		_ = Activation(c)

		var response map[string]interface{}
		statusCode := w.Code
		body := w.Body.String()
		err := json.Unmarshal([]byte(body), &response)
		if err != nil {
			t.Fatalf("Failed to unmarshal JSON: %v", err)
		}

		// 提取并打印 message 字段的内容
		message, ok := response["message"].(string)
		if !ok {
			t.Fatalf("Expected message to be a string, got %T", response["message"])
		}

		assert.Equal(t, 400, statusCode)
		assert.Equal(t, `激活方式 (1 激活码 2 license文件)为必填字段`, message)
	})
	t.Run("code invalid err", func(t *testing.T) {
		jsonData := `{"code": "111", "active_type": 1}`
		// 创建一个测试请求和响应
		w := httptest.NewRecorder()
		req := httptest.NewRequest("POST", "/api/v1/settings/license", bytes.NewBufferString(jsonData))
		req.Header.Set("Content-Type", "application/json")
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		// 调用函数
		_ = Activation(c)

		var response map[string]interface{}
		statusCode := w.Code
		body := w.Body.String()
		err := json.Unmarshal([]byte(body), &response)
		if err != nil {
			t.Fatalf("Failed to unmarshal JSON: %v", err)
		}

		// 提取并打印 message 字段的内容
		_, ok := response["message"].(string)
		if !ok {
			t.Fatalf("Expected message to be a string, got %T", response["message"])
		}
		assert.Equal(t, 400, statusCode)
	})
}

func TestActivationSuccess(t *testing.T) {
	t.Run("activation failed", func(t *testing.T) {
		CheckExpireCodePatch := ApplyMethodReturn(license.GetLicense(), "CheckExpireCode", true, nil)
		CreateFileIfNotExistPatch := ApplyFuncReturn(utils.CreateFileIfNotExist, nil)
		jsonData := `{"code": "111", "active_type": 1}`
		w := httptest.NewRecorder()
		req := httptest.NewRequest("POST", "/api/v1/settings/license", bytes.NewBufferString(jsonData))
		req.Header.Set("Content-Type", "application/json")
		c, _ := gin.CreateTestContext(w)
		c.Request = req
		_ = Activation(c)

		var response map[string]interface{}
		statusCode := w.Code
		body := w.Body.String()
		err := json.Unmarshal([]byte(body), &response)
		if err != nil {
			t.Fatalf("Failed to unmarshal JSON: %v", err)
		}

		_, ok := response["message"].(string)
		if !ok {
			t.Fatalf("Expected message to be a string, got %T", response["message"])
		}
		assert.Equal(t, 400, statusCode)
		CheckExpireCodePatch.Reset()
		CreateFileIfNotExistPatch.Reset()
	})
}

//func TestExpire(t *testing.T) {
//	cfg.InitLoadCfg()
//	t.Run("get activation info", func(t *testing.T) {
//		w := httptest.NewRecorder()
//		req := httptest.NewRequest("GET", "/api/v1/license", nil)
//		//创建一个gin上下文
//		c, _ := gin.CreateTestContext(w)
//		c.Request = req
//
//		//调用函数
//		_ = Expire(c)
//		var response map[string]interface{}
//		statusCode := w.Code
//		body := w.Body.String()
//		err := json.Unmarshal([]byte(body), &response)
//		if err != nil {
//			t.Fatalf("Failed to unmarshal JSON: %v", err)
//		}
//		success, ok := response["message"].(string)
//		if !ok {
//			t.Fatalf("Expected status to be a string, got %T", response["active_status"])
//		}
//		assert.Equal(t, 200, statusCode)
//		assert.Equal(t, "Success", success)
//	})
//}

func TestAllowAddSource(t *testing.T) {
	cfg.InitLoadCfg()
	tests := []struct {
		name           string
		expectedStatus int
		param          string
		msg            string
	}{
		{
			name:           "no param",
			param:          "{}",
			expectedStatus: 400,
			msg:            "数据源类型为必填字段",
		},
		{
			name:           "has param",
			param:          `{"source_type":"foeye"}`,
			expectedStatus: 200,
			msg:            "Success",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			w := httptest.NewRecorder()
			req := httptest.NewRequest("POST", "/api/v1/settings/allow_add_node", bytes.NewBufferString(tt.param))
			req.Header.Set("Content-Type", "application/json")
			//创建一个gin上下文
			c, _ := gin.CreateTestContext(w)
			c.Request = req

			//调用函数
			_ = AllowAddSource(c)
			var response map[string]interface{}
			//statusCode := w.Code
			body := w.Body.String()
			err := json.Unmarshal([]byte(body), &response)
			if err != nil {
				t.Fatalf("Failed to unmarshal JSON: %v", err)
			}
			_, ok := response["message"].(string)
			if !ok {
				t.Fatalf("Expected status to be a string, got %T", response["message"])
			}
			//assert.Equal(t, tt.expectedStatus, statusCode)
			//assert.Equal(t, tt.msg, success)
		})
	}
}
