package license

import (
	"fmt"
	license2 "fobrain/fobrain/app/repository/settings/license"
	"fobrain/fobrain/common/license"
	"fobrain/fobrain/common/request"
	"fobrain/fobrain/common/response"
	"fobrain/pkg/cfg"
	"fobrain/pkg/utils"
	"github.com/gin-gonic/gin"
	"io"
)

type (
	ActivationRequest struct {
		Code       string `json:"code" form:"code" uri:"code" validate:"required_if=active_type 1" zh:"激活码"`
		ActiveType int    `json:"active_type" form:"active_type" uri:"active_type" validate:"required,oneof=1 2" zh:"激活方式 (1 激活码 2 license文件)"`
	}
	AllowAddSourcesRequest struct {
		SourceType string `json:"source_type" form:"source_type" uri:"source_type" validate:"required" zh:"数据源类型"`
	}
)

// Activation 系统激活
func Activation(c *gin.Context) error {
	params, err := request.Validate(c, &ActivationRequest{})
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	var code string
	if params.ActiveType == 1 {
		code = params.Code
	} else {
		licenseFile, err := c.FormFile("license_file")
		if err != nil {
			return response.FailWithMessage(c, err.Error())
		}
		openedFile, err := licenseFile.Open()
		if err != nil {
			return response.FailWithMessage(c, err.Error())
		}
		defer openedFile.Close()
		fileBytes, err := io.ReadAll(openedFile)
		if err != nil {
			return response.FailWithMessage(c, err.Error())
		}
		code = string(fileBytes)
	}

	ok, err := license.GetLicense().Activation(code)
	if err != nil {
		fmt.Println("===Activation===", err.Error())
		if err.Error() == "exit status 2" {
			return response.FailWithMessage(c, "文件格式不正确或激活码不正确")
		}
		return response.FailWithMessage(c, err.Error())
	}
	if !ok {
		return response.FailWithMessage(c, "激活码过期")
	}
	return response.Ok(c)
}

// Expire 激活信息
func Expire(c *gin.Context) error {
	err := license.GetLicense().GetExpireInfo()
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	return response.OkWithData(c, license.GetLicense())
}

// AllowAddSource 允许添加数据源
func AllowAddSource(c *gin.Context) error {
	params, err := request.Validate(c, &AllowAddSourcesRequest{})
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	sources, err := license.GetLicense().GetDataSources(license2.CheckDevModel(cfg.GetInstance().Common.Env))
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	if utils.InArray(params.SourceType, sources) || utils.InArray("all", sources) {
		return response.Ok(c)
	}
	return response.FailWithMessage(c, "暂无权限，请联系厂商开通！")
}
