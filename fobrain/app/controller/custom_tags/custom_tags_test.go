package custom_tags

import (
	"bytes"
	"io/ioutil"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"

	res "fobrain/fobrain/app/repository/custom_tags"
)

func TestCreate(t *testing.T) {
	// 创建一个测试请求和响应
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyFuncReturn(res.Create, nil).Reset()

	str := `{"tag_name":"业务系统标签1"}`
	w := httptest.NewRecorder()
	req := httptest.NewRequest("POST", "/api/v1/tags", bytes.NewBufferString(str))
	req.Header.Set("Content-Type", "application/json")
	// 创建一个Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 调用函数
	err := Create(c)
	assert.NoError(t, err)

	// 解析响应数据
	response := w.Result()
	defer response.Body.Close()
	body, _ := ioutil.ReadAll(response.Body)
	assert.NotEmpty(t, body)
}

func TestList(t *testing.T) {
	t.Run("Success", func(t *testing.T) {
		time.Sleep(time.Second)
		defer gomonkey.ApplyFuncReturn(res.ALlList, nil, int64(0), nil).Reset()
		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/v1/tags", nil)
		req.Header.Set("Content-Type", "application/json")
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		// 调用函数
		err := List(c)
		assert.NoError(t, err)

		// 解析响应数据
		response := w.Result()
		defer response.Body.Close()
		body, _ := ioutil.ReadAll(response.Body)
		assert.NotEmpty(t, body)
	})
}
