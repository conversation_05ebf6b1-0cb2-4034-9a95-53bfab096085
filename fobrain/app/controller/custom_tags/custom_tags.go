package custom_tags

import (
	"github.com/gin-gonic/gin"

	res "fobrain/fobrain/app/repository/custom_tags"
	req "fobrain/fobrain/app/request/custom_tags"
	"fobrain/fobrain/common/request"
	"fobrain/fobrain/common/response"
)

// Create 添加
func Create(c *gin.Context) error {
	params, err := request.Validate(c, &req.InsertCustomTagsRequest{})
	if err != nil {
		return err
	}
	err = res.Create(params)
	if err != nil {
		return err
	}
	return response.OkWithMessage(c, "添加成功")
}

func List(c *gin.Context) error {
	params, err := request.Validate(c, &req.SearchCustomTagsRequest{})
	if err != nil {
		return err
	}
	list, _, err := res.ALlList(params)
	if err != nil {
		return err
	}
	return response.OkWithData(c, list)
}
