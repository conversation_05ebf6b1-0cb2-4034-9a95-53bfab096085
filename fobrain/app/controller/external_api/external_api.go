package external_api

import (
	"github.com/gin-gonic/gin"
)

// 此 API 仅处理外部 API 兼容 1.0 平台与部分节点的对接逻辑

// FoeyeAuth
// @Summary Foeye3.0 变更节点时需要获取的认证信息
// @Router /foeyeApi/auth [get]
func FoeyeAuth(ctx *gin.Context) error {
	ctx.JSON(200, map[string]any{
		"code":       200,
		"statusCode": 200,
	})

	return nil
}

type Params struct {
	Host string `json:"host"`
	Key  string `json:"key"`
}

// FoeyeAuthSftpPasswd
// @Summary Foeye3.0 变更节点时需要获取的认证信息
// @Router /foeyeApi/auth/sftpPasswd [get]
func FoeyeAuthSftpPasswd(ctx *gin.Context) error {
	ctx.JSON(200, map[string]any{
		"code":       200,
		"statusCode": 200,
		"token":      "123456",
	})

	return nil
}
