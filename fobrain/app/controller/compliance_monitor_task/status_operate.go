package compliance_monitor_task

import (
	"fmt"
	"fobrain/fobrain/app/controller/threat_center"
	"fobrain/fobrain/app/repository/compliance_history"
	srv "fobrain/fobrain/app/repository/threat_center"
	"fobrain/fobrain/app/repository/threat_history"
	"fobrain/fobrain/app/services/compliance_center"
	"fobrain/fobrain/common/response"
	"fobrain/initialize/mysql"
	"fobrain/models/elastic/poc"
	"fobrain/models/mysql/poc_accessorys"
	"fobrain/models/mysql/user"
	"strings"
	"time"

	"github.com/spf13/cast"

	"git.gobies.org/caasm/fobrain-components/utils"
	"github.com/gin-gonic/gin"
)

// 合规检测状态闭环变化
// 涉及到的状态：新增、待修复、待复测、复测中、复测未通过、超时未修复、复测通过、误报、无法修复
// 涉及到的操作：派发、转交、误报、无法修复、延时、催促、修复完成、复测

// ComplianceRisksDistributeBatch 批量派发，转交
func ComplianceRisksDistributeBatch(ctx *gin.Context) error {
	param, paramList, files, user, staff, ccStaff, err := threat_center.DistributeBatchParse(ctx)
	if err != nil {
		return err
	}

	limitEnd, err := handleLimiteDate(param.LimitDate)
	if err != nil {
		return err
	}

	// 查询合规检测记录
	records, err := compliance_center.GetComplianceRecords(ctx, param.PocIds, paramList)
	if err != nil {
		return err
	}

	// 转换为正确的类型
	complianceRecords := make([]map[string]interface{}, len(records))
	recordIds := make([]string, len(records))
	for i, record := range records {
		complianceRecords[i] = record.(map[string]interface{})
		recordIds[i] = complianceRecords[i]["id"].(string)
	}

	// 将threat_history参数转换为compliance_history参数
	complianceParam := &compliance_history.SomeComplianceHistory{
		RecordIds:         recordIds,
		ToStaffId:         param.ToStaffId,
		ToStaffName:       param.ToStaffName,
		LimitDate:         limitEnd,
		SendNotice:        param.SendNotice,
		TimeoutNotice:     param.TimeoutNotice,
		Descrition:        param.Descrition,
		ToCc:              param.ToCc,
		Status:            param.Status,
		ExecNow:           param.ExecNow,
		OriginalId:        param.OriginalId,
		OperationType:     param.OperationType,
		TimeoutReceiverId: param.TimeoutReceiverId,
		TimeoutFrequency:  param.TimeoutFrequency,
	}

	// 使用合规检测分发逻辑
	err = compliance_center.DistributeMutil(user, staff, ctx, complianceParam, complianceRecords, ccStaff)
	if err != nil {
		return err
	}

	// 处理文件上传
	if files != nil {
		recordIds := make([]string, 0)
		for _, record := range complianceRecords {
			recordIds = append(recordIds, cast.ToString(record["id"]))
		}

		source := poc_accessorys.VulDistribute
		if param.Status == poc.PocStatusOfForward {
			source = poc_accessorys.VulCare
		}
		for _, file := range files {
			if file.Size > 0 {
				err = srv.UploadPocFile(ctx, file, int64(source), recordIds, 1)
				if err != nil {
					return err
				}
			}
		}
	}

	return response.Ok(ctx)
}
func handleLimiteDate(limitDate string) (string, error) {
	if limitDate == "" {
		return "", nil
	}
	limitDateParsed, err := time.Parse(utils.DateTimeLayout, limitDate)
	if err != nil {
		return "", err
	}
	limitEnd := utils.EndOfDay(limitDateParsed)
	return limitEnd.Format(utils.DateTimeLayout), nil
}

// ComplianceRisksStatusOperation 批量延时、催促(无状态修改)、误报、无法修复、修复完成
func ComplianceRisksStatusOperation(ctx *gin.Context) error {
	param, paramList, files, user, err := threat_center.StatusOperationBatchParse(ctx)
	if err != nil {
		return err
	}
	limitEnd, err := handleLimiteDate(param.LimitDate)
	if err != nil {
		return err
	}

	records, err := compliance_center.GetComplianceRecords(ctx, param.PocIds, paramList)
	if err != nil {
		return err
	}

	ids := make([]string, 0)
	// 统计错误数量
	errCount := 0
	msgMap := make(map[string][]string)
	msg := []string{
		"操作成功",
	}
	for _, obj := range records {
		complianceObj := obj.(map[string]interface{})
		id := cast.ToString(complianceObj["id"])
		oneComplianceHistory := &compliance_history.OneComplianceHistory{
			RecordId:      id,
			ToStaffId:     param.ToStaffId,
			ToStaffName:   param.ToStaffName,
			LimitDate:     limitEnd,
			SendNotice:    param.SendNotice,
			TimeoutNotice: param.TimeoutNotice,
			Descrition:    param.Descrition,
			ToCc:          param.ToCc,
			Status:        param.Status,
			ExecNow:       param.ExecNow,
			OriginalId:    param.OriginalId,
			OperationType: param.OperationType,
		}
		ip := cast.ToString(complianceObj["ip"])
		// 延时，设置延时时间
		if param.Status == poc.PocStatusOfDelay {
			limitEnd, err := handleLimiteDate(param.LimitDate)
			if err != nil {
				return err
			}
			complianceObj["limit_date"] = limitEnd
		}
		err = compliance_center.StatusOperation(user, complianceObj, ctx, oneComplianceHistory)
		if err != nil {
			errCount++
			msgMap[err.Error()] = append(msgMap[err.Error()], ip)
		}
		if err == nil && param.Status == poc.PocStatusOfWaitRetest {
			ids = append(ids, id)
			msgMap[msg[0]] = append(msgMap[msg[0]], ip)
		}
	}

	if len(records) != errCount {
		if param.Status == poc.PocStatusOfErrorReport || param.Status == poc.PocStatusOfDelay || param.Status == poc.PocOperateOfUrge ||
			param.Status == poc.PocStatusOfCantRepaired || param.Status == poc.PocStatusOfWaitRetest {
			operationName := "误报"
			if param.Status == poc.PocStatusOfDelay {
				operationName = "延时"
			}
			if param.Status == poc.PocOperateOfUrge {
				operationName = "催促"
			}
			if param.Status == poc.PocStatusOfCantRepaired {
				operationName = "无法修复"
			}
			if param.Status == poc.PocStatusOfWaitRetest {
				operationName = "修复完成"
			}
			// 获取人员信息
			staff, err := threat_history.GetStaff(param.ToStaffId)
			if err != nil {
				return err
			}
			if staff == nil {
				return response.FailWithMessage(ctx, "人员不存在")
			}

			err = threat_history.SendLocalMsgNotice(user.Username, user.Id, staff.FidHash, len(records)-errCount, operationName)
			if err != nil {
				return err
			}
		}
	}

	if errCount > 0 {
		return response.FailWithMessage(ctx, combResult(msgMap))
	}

	if files != nil {
		source := poc_accessorys.VulDistribute
		if param.Status == poc.PocStatusOfForward {
			source = poc_accessorys.VulCare
		}
		for _, file := range files {
			if file.Size > 0 {
				err = srv.UploadPocFile(ctx, file, int64(source), ids, 1)
				if err != nil {
					return err
				}
			}
		}
	}

	return response.OkWithMessage(ctx, combResult(msgMap))
}

// ComplianceRisksReTest 批量复测，重新执行该ip对应的合规检测任务，若检测结果中已没有该ip的检测结果，则认为修复成功，需要在保存更新的地方增加判断，把当前合规检测id的记录全查出来，做判断
func ComplianceRisksReTest(ctx *gin.Context) error {
	param, err := threat_center.SomeThreatHistoryValidate(ctx, &threat_history.SomeThreatHistory{})
	if err != nil {
		return err
	}
	paramList, err := utils.StructToMap(param, "json")
	if err != nil {
		return err
	}

	// 获取当前用户
	currentUser, exists := ctx.Get("user_id")
	if !exists {
		return response.FailWithMessage(ctx, "用户信息不存在")
	}

	// 查询用户信息
	u, err := user.NewUserModel().First(mysql.WithId(currentUser.(uint64)))
	if err != nil {
		return response.FailWithMessage(ctx, "用户不存在")
	}

	// 查询待复测的合规检测记录，param.PocIds为空，意味着对全部数据（或带着筛选条件）进行批量操作
	records, err := compliance_center.GetComplianceRecords(ctx, param.PocIds, paramList)
	if err != nil {
		return err
	}

	if len(records) == 0 {
		return response.FailWithMessage(ctx, "未找到待复测的合规检测记录")
	}

	resMap := make(map[string][]string)
	msg := []string{
		"缺少字段",
		"状态不是待复测",
		"复测失败",
		"复测通过",
	}
	successCount := 0
	// 执行合规检测复测逻辑
	for _, record := range records {
		complianceObj := record.(map[string]interface{})
		ip := cast.ToString(complianceObj["ip"])

		if complianceObj["flow_status"] == nil {
			resMap[msg[0]] = append(resMap[msg[0]], ip)
			continue
		}

		flowStatus := cast.ToInt(complianceObj["flow_status"])

		// 状态不是待复测就不需要复测
		if flowStatus != poc.PocStatusOfWaitRetest {
			resMap[msg[1]] = append(resMap[msg[1]], ip)
			continue
		}
		tid := cast.ToUint64(complianceObj["compliance_monitor_task_id"])
		// 检查必要字段
		if tid == 0 {
			resMap[msg[0]] = append(resMap[msg[0]], ip)
			continue
		}

		err = compliance_history.ProcessTaskRetest(ctx, &u, tid, complianceObj)
		if err != nil {
			resMap[err.Error()] = append(resMap[err.Error()], ip)
		} else {
			successCount++
			resMap[msg[3]] = append(resMap[msg[3]], ip)
		}
	}
	// 组合
	if successCount == 0 {
		return response.FailWithMessage(ctx, combResult(resMap))
	}
	return response.OkWithMessage(ctx, combResult(resMap))
}

// CheckAsset 检查违规资产是否存在
func CheckAsset(ctx *gin.Context) error {
	res := compliance_center.CheckAsset(ctx, ctx.Param("asset_id"))
	if !res {
		return response.FailWithMessage(ctx, "未找到相关资产")
	}
	return response.Ok(ctx)
}

func combResult(resMap map[string][]string) string {
	message := ""
	for k, v := range resMap {

		message += fmt.Sprintf("%s:%s", k, strings.Join(v[:min(len(v), 3)], "、"))
		if len(v) > 3 {
			message += "等"
		}
		message += "\n"
	}
	return message
}
