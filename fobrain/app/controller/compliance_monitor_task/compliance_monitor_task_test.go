package compliance_monitor_task

import (
	"bytes"
	"errors"
	"fobrain/fobrain/app/repository/compliance_monitor_task"
	mycompliance_monitor_task "fobrain/models/mysql/compliance_monitor_task"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/agiledragon/gomonkey/v2"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"

	testcommon "fobrain/fobrain/tests/common_test"
)

func TestList(t *testing.T) {

	t.Run("param err", func(t *testing.T) {
		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/v1/compliance_monitor_tasks", nil)

		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := Index(c)
		assert.Error(t, err)
		assert.Equal(t, err.<PERSON><PERSON>r(), "页数为必填字段")
	})

	t.Run("success", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()
		mockDb.ExpectQuery("SELECT count(*) FROM `compliance_monitor_tasks`").
			WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(1))
		mockDb.ExpectQuery("SELECT * FROM `compliance_monitor_tasks` ORDER BY status ASC,created_at DESC LIMIT 10").
			WillReturnRows(mockDb.NewRows([]string{"id"}).AddRow(1))
		mockDb.ExpectQuery("SELECT * FROM `compliance_monitors` WHERE id = ? ORDER BY `compliance_monitors`.`id` LIMIT 1").
			WillReturnRows(mockDb.NewRows([]string{"id"}).AddRow(1))
		mockDb.ExpectQuery("SELECT * FROM `users` WHERE id = ? ORDER BY `users`.`id` LIMIT 1").
			WillReturnRows(mockDb.NewRows([]string{"id"}).AddRow(1))

		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/v1/compliance_monitor_tasks?page=1&per_page=10&event=1", strings.NewReader(`{
			"event": 1,
    	}`))

		req.Header.Set("Content-Type", "application/json")
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := Index(c)
		assert.Nil(t, err)
		assert.NotEmpty(t, w.Body.String())
	})

}

func TestRecords(t *testing.T) {
	patch := gomonkey.ApplyFuncReturn(compliance_monitor_task.Records, []string{""}, int64(0), nil)
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/compliance_monitor_tasks/records?page=1&per_page=15", bytes.NewReader([]byte{}))

	c, _ := gin.CreateTestContext(w)
	c.Request = req

	err := Records(c)
	patch.Reset()
	assert.Nil(t, err)
}

func TestRecordsError(t *testing.T) {
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/compliance_monitor_tasks/records", nil)

	c, _ := gin.CreateTestContext(w)
	c.Request = req

	err := Records(c)
	assert.Error(t, err)
	assert.Equal(t, err.Error(), "页数为必填字段")
}

func TestDestroy(t *testing.T) {
	patch := gomonkey.ApplyFuncReturn(compliance_monitor_task.Destroy, nil)
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/compliance_monitor_tasks?task_ids=1", bytes.NewReader([]byte{}))

	c, _ := gin.CreateTestContext(w)
	c.Request = req

	err := Destroy(c)
	patch.Reset()
	assert.Nil(t, err)
}

func TestDestroyError(t *testing.T) {
	w := httptest.NewRecorder()
	req := httptest.NewRequest("DELETE", "/api/v1/compliance_monitor_tasks", nil)

	c, _ := gin.CreateTestContext(w)
	c.Request = req

	err := Destroy(c)
	assert.Error(t, err)
	assert.Equal(t, err.Error(), "合规监测任务ID为必填字段")
}

func TestRecordsDestroy(t *testing.T) {
	patch := gomonkey.ApplyFuncReturn(compliance_monitor_task.RecordsDestroy, nil)
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/compliance_monitor_tasks/records?task_record_ids=1", bytes.NewReader([]byte{}))

	c, _ := gin.CreateTestContext(w)
	c.Request = req

	err := RecordsDestroy(c)
	patch.Reset()
	assert.Nil(t, err)
}

func TestRecordsDestroyError(t *testing.T) {
	w := httptest.NewRecorder()
	req := httptest.NewRequest("DELETE", "/api/v1/compliance_monitor_tasks/records", nil)

	c, _ := gin.CreateTestContext(w)
	c.Request = req

	err := RecordsDestroy(c)
	assert.Error(t, err)
	assert.Equal(t, err.Error(), "合规监测任务记录ID为必填字段")
}

func TestRecordsExport(t *testing.T) {
	patch := gomonkey.ApplyFuncReturn(compliance_monitor_task.RecordsExport, "", nil)
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/compliance_monitor_tasks/records/Export?task_record_ids=1", bytes.NewReader([]byte{}))

	c, _ := gin.CreateTestContext(w)
	c.Request = req

	err := RecordsExport(c)
	patch.Reset()
	assert.Nil(t, err)
}

func TestRecordsExportError(t *testing.T) {
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/compliance_monitor_tasks/records/Export", nil)

	c, _ := gin.CreateTestContext(w)
	c.Request = req

	err := RecordsDestroy(c)
	assert.Error(t, err)
	assert.Equal(t, err.Error(), "合规监测任务记录ID为必填字段")
}

func TestExec(t *testing.T) {
	patches := []*gomonkey.Patches{
		gomonkey.ApplyFuncReturn(compliance_monitor_task.Exec, nil),
		gomonkey.ApplyMethodReturn(&mycompliance_monitor_task.ComplianceMonitorTask{}, "First", nil, nil),
	}
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/compliance_monitor_tasks/1/exec?task_id=1", bytes.NewReader([]byte{}))

	c, _ := gin.CreateTestContext(w)
	c.Request = req

	err := Exec(c)
	for _, patch := range patches {
		patch.Reset()
	}
	assert.Nil(t, err)
}

func TestExecError(t *testing.T) {
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/compliance_monitor_tasks/1/exec", nil)

	c, _ := gin.CreateTestContext(w)
	c.Request = req

	err := Exec(c)
	assert.Error(t, err)
	assert.Equal(t, err.Error(), "合规监测任务ID为必填字段")
}

func TestRecordsHistory(t *testing.T) {
	// mock service 层函数
	patch := gomonkey.ApplyFuncReturn(
		compliance_monitor_task.RecordsHistory,
		[]compliance_monitor_task.RecordsHistoryResponse{{Id: 1}}, // 假数据
		int64(1),
		nil,
	)
	defer patch.Reset()

	w := httptest.NewRecorder()
	// 注意：record_id 为 url 参数
	req := httptest.NewRequest("GET",
		"/api/v1/compliance_monitor_tasks/history?page=1&per_page=10&id=abc123",
		nil)

	c, _ := gin.CreateTestContext(w)
	c.Request = req

	err := RecordsHistory(c) // 替换为真实 handler 函数
	assert.Nil(t, err)
	assert.Equal(t, http.StatusOK, w.Code)
}

func TestRecordsHistoryParamError(t *testing.T) {
	patch := gomonkey.ApplyFuncReturn(
		compliance_monitor_task.RecordsHistory,
		nil,
		int64(0),
		errors.New("页数为必填字段"),
	)
	defer patch.Reset()

	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET",
		"/api/v1/compliance_monitor_tasks/history?record_id=abc123",
		nil)

	c, _ := gin.CreateTestContext(w)
	c.Request = req

	err := RecordsHistory(c)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "页数")
}
