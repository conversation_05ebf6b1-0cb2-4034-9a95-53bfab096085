package compliance_monitor_task

import (
	"github.com/gin-gonic/gin"

	"fobrain/fobrain/common/auth"

	"fobrain/fobrain/app/repository/compliance_monitor_task"
	reqmonitor "fobrain/fobrain/app/request/compliance_monitor_task"
	"fobrain/fobrain/common/request"
	"fobrain/fobrain/common/response"
	"fobrain/initialize/mysql"
	mycompliance_monitor_task "fobrain/models/mysql/compliance_monitor_task"
)

// Index 合规监测任务列表
// @Route /api/v1/compliance_monitor_tasks [GET]
func Index(c *gin.Context) error {
	params, err := request.Validate(c, &reqmonitor.IndexRequest{})
	if err != nil {
		return err
	}

	data, total, err := compliance_monitor_task.Index(params)
	if err != nil {
		return err
	}

	return response.OkWithPageData(c, total, params.Page, params.PerPage, data)
}

// Records 合规监测任务详情
// @Route /api/v1/compliance_monitor_tasks/records [GET]
func Records(c *gin.Context) error {
	params, err := request.Validate(c, &reqmonitor.ShowRequest{})
	if err != nil {
		return err
	}

	data, total, err := compliance_monitor_task.Records(c, params, auth.IsSuperManage(c), auth.CurrentStaffIds(c))
	if err != nil {
		return err
	}

	return response.OkWithPageData(c, total, params.Page, params.PerPage, data)
}

// Destroy 合规监测任务删除
// @Route /api/v1/compliance_monitor_tasks [DELETE]
func Destroy(c *gin.Context) error {
	params, err := request.Validate(c, &struct {
		Ids     []uint64 `json:"task_ids" uri:"task_ids" form:"task_ids" validate:"required" zh:"合规监测任务ID"`
		Keyword string   `json:"keyword" form:"keyword" uri:"keyword" validate:"omitempty,max=100" zh:"模糊搜索"`
	}{})

	if err != nil {
		return err
	}

	err = compliance_monitor_task.Destroy(params.Ids, params.Keyword)
	if err != nil {
		return err
	}

	return response.Ok(c)
}

// RecordsDestroy 合规监测任务记录删除
// @Route /api/v1/compliance_monitor_tasks/records [DELETE]
func RecordsDestroy(c *gin.Context) error {
	params, err := request.Validate(c, &struct {
		Ids             []string `json:"task_record_ids" uri:"task_record_ids" form:"task_record_ids" validate:"required" zh:"合规监测任务记录ID"`
		Keyword         string   `json:"keyword" form:"keyword" uri:"keyword" validate:"omitempty,max=100" zh:"模糊搜索"`
		SearchCondition []string `json:"search_condition" form:"search_condition" uri:"search_condition"  zh:"搜索条件"`
	}{})
	if err != nil {
		return err
	}

	err = compliance_monitor_task.RecordsDestroy(c, params.Ids, params.SearchCondition, params.Keyword)
	if err != nil {
		return err
	}

	return response.Ok(c)
}

// RecordsExport 合规监测记录导出
// @Route /api/v1/compliance_monitor_tasks/records/Export [GET]
func RecordsExport(c *gin.Context) error {
	params, err := request.Validate(c, &reqmonitor.ExportRequest{})
	if err != nil {
		return err
	}

	path, err := compliance_monitor_task.RecordsExport(c, params)
	if err != nil {
		return err
	}

	return response.OkWithFile(c, path, true)
}

func Exec(c *gin.Context) error {
	params, err := request.Validate(c, &struct {
		Id uint64 `json:"task_id" uri:"task_id" form:"task_id" validate:"required,number" zh:"合规监测任务ID"`
	}{})
	if err != nil {
		return err
	}

	first, err := mycompliance_monitor_task.NewComplianceMonitorTaskModel().First(mysql.WithWhere("id = ?", params.Id))
	if err != nil {
		return err
	}
	err = compliance_monitor_task.Exec(first)
	if err != nil {
		return err
	}

	return response.Ok(c)
}

// RecordsHistory 查询合规风险历史
func RecordsHistory(c *gin.Context) error {
	params, err := request.Validate(c, &compliance_monitor_task.RecordsHistoryRequest{})
	if err != nil {
		return err
	}
	data, total, err := compliance_monitor_task.RecordsHistory(params)
	if err != nil {
		return err
	}

	return response.OkWithPageData(c, total, params.Reqmonitor.Page, params.Reqmonitor.PerPage, data)
}
