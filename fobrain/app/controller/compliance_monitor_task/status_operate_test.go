package compliance_monitor_task

import (
	"errors"
	"fobrain/pkg/utils"
	"mime/multipart"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"

	"fobrain/fobrain/app/controller/threat_center"
	"fobrain/fobrain/app/repository/compliance_history"
	"fobrain/fobrain/app/repository/threat"
	srv "fobrain/fobrain/app/repository/threat_center"
	"fobrain/fobrain/app/repository/threat_history"
	"fobrain/fobrain/app/services/compliance_center"
	"fobrain/fobrain/common/response"
	"fobrain/initialize/mysql"
	"fobrain/models/elastic/poc"
	"fobrain/models/elastic/staff"
	"fobrain/models/mysql/user"
)

func TestComplianceRisksDistributeBatch_ValidateError(t *testing.T) {
	gin.SetMode(gin.TestMode)
	time.Sleep(time.Second * 2)
	patches := gomonkey.NewPatches()
	defer patches.Reset()

	// Mock threat_center.DistributeBatchParse 返回错误
	patches.ApplyFunc(threat_center.DistributeBatchParse, func(ctx *gin.Context) (*threat_history.SomeThreatHistory, map[string]interface{}, []*multipart.FileHeader, *user.User, *staff.Staff, *staff.Staff, error) {
		return nil, nil, nil, nil, nil, nil, errors.New("参数验证失败")
	})

	w := httptest.NewRecorder()
	req := httptest.NewRequest("POST", "/api/v1/compliance_monitor_tasks/distribute", strings.NewReader(`{}`))
	req.Header.Set("Content-Type", "application/json")

	c, _ := gin.CreateTestContext(w)
	c.Request = req

	err := ComplianceRisksDistributeBatch(c)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "参数验证失败")
}

func TestComplianceRisksDistributeBatch_GetRecordsError(t *testing.T) {
	gin.SetMode(gin.TestMode)
	time.Sleep(time.Second * 2)
	patches := gomonkey.NewPatches()
	defer patches.Reset()

	// Mock threat_center.DistributeBatchParse 成功
	patches.ApplyFunc(threat_center.DistributeBatchParse, func(ctx *gin.Context) (*threat_history.SomeThreatHistory, map[string]interface{}, []*multipart.FileHeader, *user.User, *staff.Staff, *staff.Staff, error) {
		param := &threat_history.SomeThreatHistory{
			PocIds:    []string{"test-id-1"},
			LimitDate: "2025-07-23 17:43:11",
		}
		paramList := map[string]interface{}{"poc_ids": []string{"test-id-1"}, "search_condition": []string{}, "limit_date": "2025-07-23 17:43:11"}
		user := &user.User{BaseModel: mysql.BaseModel{Id: 1}, Username: "test"}
		staff := &staff.Staff{Id: "1", Name: "test-staff"}
		return param, paramList, nil, user, staff, staff, nil
	})

	// Mock threat.ShowMultiple 返回错误
	patches.ApplyFunc(threat.ShowMultiple, func(ctx *gin.Context, ids []string, paramList map[string]interface{}, model interface{}) ([]interface{}, error) {
		return nil, errors.New("获取记录失败")
	})

	w := httptest.NewRecorder()
	req := httptest.NewRequest("POST", "/api/v1/compliance_monitor_tasks/distribute", strings.NewReader(`{}`))
	req.Header.Set("Content-Type", "application/json")

	c, _ := gin.CreateTestContext(w)
	c.Request = req

	err := ComplianceRisksDistributeBatch(c)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "获取记录失败")
}

func TestComplianceRisksDistributeBatch_DistributeError(t *testing.T) {
	gin.SetMode(gin.TestMode)
	time.Sleep(time.Second * 2)
	patches := gomonkey.NewPatches()
	defer patches.Reset()

	// Mock threat_center.DistributeBatchParse 成功
	patches.ApplyFunc(threat_center.DistributeBatchParse, func(ctx *gin.Context) (*threat_history.SomeThreatHistory, map[string]interface{}, []*multipart.FileHeader, *user.User, *staff.Staff, *staff.Staff, error) {
		param := &threat_history.SomeThreatHistory{
			PocIds:    []string{"test-id-1"},
			LimitDate: "2025-07-23 17:43:11",
		}
		paramList := map[string]interface{}{
			"poc_ids":          []string{"test-id-1"},
			"search_condition": []string{},
		}
		user := &user.User{BaseModel: mysql.BaseModel{Id: 1}, Username: "test"}
		staff := &staff.Staff{Id: "1", Name: "test-staff"}
		return param, paramList, nil, user, staff, staff, nil
	})

	// Mock threat.ShowMultiple 成功
	patches.ApplyFunc(threat.ShowMultiple, func(ctx *gin.Context, ids []string, paramList map[string]interface{}, model interface{}) ([]interface{}, error) {
		return []interface{}{
			map[string]interface{}{
				"id":         "test-record-1",
				"limit_date": "2025-07-23 17:43:11",
			},
		}, nil
	})

	// Mock compliance_center.DistributeMutil 返回错误
	patches.ApplyFunc(compliance_center.DistributeMutil, func(user *user.User, staff *staff.Staff, ctx *gin.Context, param *compliance_history.SomeComplianceHistory, records []map[string]interface{}, ccStaff *staff.Staff) error {
		return errors.New("分发失败")
	})

	w := httptest.NewRecorder()
	req := httptest.NewRequest("POST", "/api/v1/compliance_monitor_tasks/distribute", strings.NewReader(`{}`))
	req.Header.Set("Content-Type", "application/json")

	c, _ := gin.CreateTestContext(w)
	c.Request = req

	err := ComplianceRisksDistributeBatch(c)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "分发失败")
}

func TestComplianceRisksDistributeBatch_Success(t *testing.T) {
	gin.SetMode(gin.TestMode)
	time.Sleep(time.Second * 2)
	patches := gomonkey.NewPatches()
	defer patches.Reset()

	// Mock threat_center.DistributeBatchParse 成功
	patches.ApplyFunc(threat_center.DistributeBatchParse, func(ctx *gin.Context) (*threat_history.SomeThreatHistory, map[string]interface{}, []*multipart.FileHeader, *user.User, *staff.Staff, *staff.Staff, error) {
		param := &threat_history.SomeThreatHistory{
			PocIds:    []string{"test-id-1"},
			LimitDate: "2025-07-23 17:43:11",
		}
		paramList := map[string]interface{}{
			"poc_ids":          []string{"test-id-1"},
			"search_condition": []string{},
		}
		user := &user.User{BaseModel: mysql.BaseModel{Id: 1}, Username: "test"}
		staff := &staff.Staff{Id: "1", Name: "test-staff"}
		return param, paramList, nil, user, staff, staff, nil
	})

	// Mock threat.ShowMultiple 成功
	patches.ApplyFunc(threat.ShowMultiple, func(ctx *gin.Context, ids []string, paramList map[string]interface{}, model interface{}) ([]interface{}, error) {
		return []interface{}{
			map[string]interface{}{
				"id": "test-record-1",
			},
		}, nil
	})

	// Mock compliance_center.DistributeMutil 成功
	patches.ApplyFunc(compliance_center.DistributeMutil, func(user *user.User, staff *staff.Staff, ctx *gin.Context, param *compliance_history.SomeComplianceHistory, records []map[string]interface{}, ccStaff *staff.Staff) error {
		return nil
	})

	// Mock response.Ok
	patches.ApplyFunc(response.Ok, func(ctx *gin.Context) error {
		return nil
	})

	w := httptest.NewRecorder()
	req := httptest.NewRequest("POST", "/api/v1/compliance_monitor_tasks/distribute", strings.NewReader(`{}`))
	req.Header.Set("Content-Type", "application/json")

	c, _ := gin.CreateTestContext(w)
	c.Request = req

	err := ComplianceRisksDistributeBatch(c)
	assert.NoError(t, err)
}

func TestComplianceRisksDistributeBatch_WithFiles(t *testing.T) {
	gin.SetMode(gin.TestMode)
	time.Sleep(time.Second * 2)
	patches := gomonkey.NewPatches()
	defer patches.Reset()

	// 创建模拟文件
	mockFile := &multipart.FileHeader{
		Filename: "test.txt",
		Size:     100,
	}

	// Mock threat_center.DistributeBatchParse 成功，包含文件
	patches.ApplyFunc(threat_center.DistributeBatchParse, func(ctx *gin.Context) (*threat_history.SomeThreatHistory, map[string]interface{}, []*multipart.FileHeader, *user.User, *staff.Staff, *staff.Staff, error) {
		param := &threat_history.SomeThreatHistory{
			PocIds:    []string{"test-id-1"},
			Status:    poc.PocStatusOfBeRepair,
			LimitDate: "2025-07-23 17:43:11",
		}
		paramList := map[string]interface{}{"poc_ids": []string{"test-id-1"}, "search_condition": []string{}}
		user := &user.User{BaseModel: mysql.BaseModel{Id: 1}, Username: "test"}
		staff := &staff.Staff{Id: "1", Name: "test-staff"}
		files := []*multipart.FileHeader{mockFile}
		return param, paramList, files, user, staff, staff, nil
	})

	// Mock threat.ShowMultiple 成功
	patches.ApplyFunc(threat.ShowMultiple, func(ctx *gin.Context, ids []string, paramList map[string]interface{}, model interface{}) ([]interface{}, error) {
		return []interface{}{
			map[string]interface{}{
				"id": "test-record-1",
			},
		}, nil
	})

	// Mock compliance_center.DistributeMutil 成功
	patches.ApplyFunc(compliance_center.DistributeMutil, func(user *user.User, staff *staff.Staff, ctx *gin.Context, param *compliance_history.SomeComplianceHistory, records []map[string]interface{}, ccStaff *staff.Staff) error {
		return nil
	})

	// Mock srv.UploadPocFile 成功
	patches.ApplyFunc(srv.UploadPocFile, func(ctx *gin.Context, file *multipart.FileHeader, source int64, recordIds []string, uploadType int64) error {
		return nil
	})

	// Mock response.Ok
	patches.ApplyFunc(response.Ok, func(ctx *gin.Context) error {
		return nil
	})

	w := httptest.NewRecorder()
	req := httptest.NewRequest("POST", "/api/v1/compliance_monitor_tasks/distribute", strings.NewReader(`{}`))
	req.Header.Set("Content-Type", "application/json")

	c, _ := gin.CreateTestContext(w)
	c.Request = req

	err := ComplianceRisksDistributeBatch(c)
	assert.NoError(t, err)
}

func TestComplianceRisksDistributeBatch_FileUploadError(t *testing.T) {
	gin.SetMode(gin.TestMode)
	time.Sleep(time.Second * 2)
	patches := gomonkey.NewPatches()
	defer patches.Reset()

	// 创建模拟文件
	mockFile := &multipart.FileHeader{
		Filename: "test.txt",
		Size:     100,
	}

	// Mock threat_center.DistributeBatchParse 成功，包含文件
	patches.ApplyFunc(threat_center.DistributeBatchParse, func(ctx *gin.Context) (*threat_history.SomeThreatHistory, map[string]interface{}, []*multipart.FileHeader, *user.User, *staff.Staff, *staff.Staff, error) {
		param := &threat_history.SomeThreatHistory{
			PocIds:    []string{"test-id-1"},
			Status:    poc.PocStatusOfBeRepair,
			LimitDate: "2025-07-23 17:43:11",
		}
		paramList := map[string]interface{}{"poc_ids": []string{"test-id-1"}, "search_condition": []string{}}
		user := &user.User{BaseModel: mysql.BaseModel{Id: 1}, Username: "test"}
		staff := &staff.Staff{Id: "1", Name: "test-staff"}
		files := []*multipart.FileHeader{mockFile}
		return param, paramList, files, user, staff, staff, nil
	})

	// Mock threat.ShowMultiple 成功
	patches.ApplyFunc(threat.ShowMultiple, func(ctx *gin.Context, ids []string, paramList map[string]interface{}, model interface{}) ([]interface{}, error) {
		return []interface{}{
			map[string]interface{}{
				"id": "test-record-1",
			},
		}, nil
	})

	// Mock compliance_center.DistributeMutil 成功
	patches.ApplyFunc(compliance_center.DistributeMutil, func(user *user.User, staff *staff.Staff, ctx *gin.Context, param *compliance_history.SomeComplianceHistory, records []map[string]interface{}, ccStaff *staff.Staff) error {
		return nil
	})

	// Mock srv.UploadPocFile 失败
	patches.ApplyFunc(srv.UploadPocFile, func(ctx *gin.Context, file *multipart.FileHeader, source int64, recordIds []string, uploadType int64) error {
		return errors.New("文件上传失败")
	})

	w := httptest.NewRecorder()
	req := httptest.NewRequest("POST", "/api/v1/compliance_monitor_tasks/distribute", strings.NewReader(`{}`))
	req.Header.Set("Content-Type", "application/json")

	c, _ := gin.CreateTestContext(w)
	c.Request = req

	err := ComplianceRisksDistributeBatch(c)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "文件上传失败")
}

// TestComplianceRisksStatusOperation_ValidateError 测试参数验证失败
func TestComplianceRisksStatusOperation_ValidateError(t *testing.T) {
	gin.SetMode(gin.TestMode)
	time.Sleep(time.Second * 2)
	patches := gomonkey.NewPatches()
	defer patches.Reset()

	// Mock threat_center.StatusOperationBatchParse 返回错误
	patches.ApplyFunc(threat_center.StatusOperationBatchParse, func(ctx *gin.Context) (*threat_history.SomeThreatHistory, map[string]interface{}, []*multipart.FileHeader, *user.User, error) {
		return nil, nil, nil, nil, errors.New("参数验证失败")
	})

	w := httptest.NewRecorder()
	req := httptest.NewRequest("POST", "/api/v1/compliance_monitor_tasks/status", strings.NewReader(`{}`))
	req.Header.Set("Content-Type", "application/json")

	c, _ := gin.CreateTestContext(w)
	c.Request = req

	err := ComplianceRisksStatusOperation(c)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "参数验证失败")
}

// TestComplianceRisksReTest_ValidateError 测试参数验证失败
func TestComplianceRisksReTest_ValidateError(t *testing.T) {
	gin.SetMode(gin.TestMode)
	time.Sleep(time.Second * 2)
	patches := gomonkey.NewPatches()
	defer patches.Reset()

	// Mock threat_center.SomeThreatHistoryValidate 返回错误
	patches.ApplyFunc(threat_center.SomeThreatHistoryValidate, func(ctx *gin.Context, param *threat_history.SomeThreatHistory) (*threat_history.SomeThreatHistory, error) {
		return nil, errors.New("参数验证失败")
	})

	w := httptest.NewRecorder()
	req := httptest.NewRequest("POST", "/api/v1/compliance_monitor_tasks/retest", strings.NewReader(`{}`))
	req.Header.Set("Content-Type", "application/json")

	c, _ := gin.CreateTestContext(w)
	c.Request = req
	c.Set("user_id", uint64(1)) // 设置用户ID

	err := ComplianceRisksReTest(c)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "参数验证失败")
}

// TestComplianceRisksReTest_EmptyPocIds 测试空的PocIds
func TestComplianceRisksReTest_EmptyPocIds(t *testing.T) {
	gin.SetMode(gin.TestMode)
	time.Sleep(time.Second * 2)
	patches := gomonkey.NewPatches()
	defer patches.Reset()

	// Mock threat_center.SomeThreatHistoryValidate 成功，但 PocIds 为空
	patches.ApplyFunc(threat_center.SomeThreatHistoryValidate, func(ctx *gin.Context, param *threat_history.SomeThreatHistory) (*threat_history.SomeThreatHistory, error) {
		return &threat_history.SomeThreatHistory{
			PocIds:    []string{}, // 空数组
			LimitDate: "2025-07-23 17:43:11",
		}, nil
	})

	// Mock utils.StructToMap 成功
	patches.ApplyFunc(utils.StructToMap, func(obj interface{}, tag string) (map[string]interface{}, error) {
		return map[string]interface{}{"poc_ids": []string{}, "search_condition": []string{}}, nil
	})

	w := httptest.NewRecorder()
	req := httptest.NewRequest("POST", "/api/v1/compliance_monitor_tasks/retest", strings.NewReader(`{}`))
	req.Header.Set("Content-Type", "application/json")

	c, _ := gin.CreateTestContext(w)
	c.Request = req
	c.Set("user_id", uint64(1)) // 设置用户ID

	// 这个测试不期望返回错误，因为 response.FailWithMessage 会直接响应HTTP
	err := ComplianceRisksReTest(c)
	assert.NoError(t, err)
}

func Test_handleLimiteDate(t *testing.T) {

	got, err := handleLimiteDate("2025-07-30 16:42:30")
	assert.NoError(t, err)
	assert.Equal(t, "2025-07-30 23:59:59", got)

}

// TestCheckAsset_Success 测试资产存在的情况
func TestCheckAsset_Success(t *testing.T) {
	gin.SetMode(gin.TestMode)
	patches := gomonkey.NewPatches()
	defer patches.Reset()

	// Mock compliance_center.CheckAsset 返回true表示资产存在
	patches.ApplyFunc(compliance_center.CheckAsset, func(ctx *gin.Context, assetId string) bool {
		assert.Equal(t, "test-asset-123", assetId)
		return true
	})

	// Mock response.Ok
	patches.ApplyFunc(response.Ok, func(ctx *gin.Context) error {
		return nil
	})

	// 创建测试上下文
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Params = gin.Params{
		{Key: "asset_id", Value: "test-asset-123"},
	}

	err := CheckAsset(c)
	assert.NoError(t, err)
}

// TestCheckAsset_NotFound 测试资产不存在的情况
func TestCheckAsset_NotFound(t *testing.T) {
	gin.SetMode(gin.TestMode)
	patches := gomonkey.NewPatches()
	defer patches.Reset()

	// Mock compliance_center.CheckAsset 返回false表示资产不存在
	patches.ApplyFunc(compliance_center.CheckAsset, func(ctx *gin.Context, assetId string) bool {
		assert.Equal(t, "non-existent-asset", assetId)
		return false
	})

	// Mock response.FailWithMessage
	patches.ApplyFunc(response.FailWithMessage, func(ctx *gin.Context, message string) error {
		assert.Equal(t, "未找到相关资产", message)
		return nil
	})

	// 创建测试上下文
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Params = gin.Params{
		{Key: "asset_id", Value: "non-existent-asset"},
	}

	err := CheckAsset(c)
	assert.NoError(t, err)
}

// TestCheckAsset_EmptyAssetId 测试空资产ID的情况
func TestCheckAsset_EmptyAssetId(t *testing.T) {
	gin.SetMode(gin.TestMode)
	patches := gomonkey.NewPatches()
	defer patches.Reset()

	// Mock compliance_center.CheckAsset 返回false
	patches.ApplyFunc(compliance_center.CheckAsset, func(ctx *gin.Context, assetId string) bool {
		assert.Equal(t, "", assetId)
		return false
	})

	// Mock response.FailWithMessage
	patches.ApplyFunc(response.FailWithMessage, func(ctx *gin.Context, message string) error {
		assert.Equal(t, "未找到相关资产", message)
		return nil
	})

	// 创建测试上下文
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Params = gin.Params{
		{Key: "asset_id", Value: ""},
	}

	err := CheckAsset(c)
	assert.NoError(t, err)
}

// TestCheckAsset_SpecialCharacters 测试特殊字符资产ID
func TestCheckAsset_SpecialCharacters(t *testing.T) {
	gin.SetMode(gin.TestMode)

	testCases := []struct {
		name     string
		assetId  string
		expected bool
	}{
		{"uuid format", "550e8400-e29b-41d4-a716-************", true},
		{"with special chars", "asset!@#$%^&*()", false},
		{"numeric only", "12345", true},
		{"very long id", strings.Repeat("a", 100), false},
		{"with spaces", "asset with spaces", false},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			patches := gomonkey.NewPatches()
			defer patches.Reset()

			// Mock compliance_center.CheckAsset
			patches.ApplyFunc(compliance_center.CheckAsset, func(ctx *gin.Context, assetId string) bool {
				assert.Equal(t, tc.assetId, assetId)
				return tc.expected
			})

			if tc.expected {
				// Mock response.Ok
				patches.ApplyFunc(response.Ok, func(ctx *gin.Context) error {
					return nil
				})
			} else {
				// Mock response.FailWithMessage
				patches.ApplyFunc(response.FailWithMessage, func(ctx *gin.Context, message string) error {
					assert.Equal(t, "未找到相关资产", message)
					return nil
				})
			}

			// 创建测试上下文
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			c.Params = gin.Params{
				{Key: "asset_id", Value: tc.assetId},
			}

			err := CheckAsset(c)
			assert.NoError(t, err)
		})
	}
}

// TestCheckAsset_ServiceError 测试服务层错误情况
func TestCheckAsset_ServiceError(t *testing.T) {
	gin.SetMode(gin.TestMode)
	patches := gomonkey.NewPatches()
	defer patches.Reset()

	// Mock compliance_center.CheckAsset 抛出 panic
	patches.ApplyFunc(compliance_center.CheckAsset, func(ctx *gin.Context, assetId string) bool {
		panic("database connection error")
	})

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Params = gin.Params{
		{Key: "asset_id", Value: "test-asset"},
	}

	assert.Panics(t, func() {
		CheckAsset(c)
	})
}
