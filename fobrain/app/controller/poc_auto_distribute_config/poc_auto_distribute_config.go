package poc_auto_distribute_config

import (
	"github.com/gin-gonic/gin"

	srv "fobrain/fobrain/app/repository/poc_auto_distribute_config"
	"fobrain/fobrain/app/request/poc_auto_distribute_config"
	"fobrain/fobrain/common/request"
	"fobrain/fobrain/common/response"
)

// Info 函数用于处理获取服务信息的HTTP请求
//
// 参数:
//     ctx: *gin.Context，Gin框架的上下文对象，用于处理HTTP请求和响应
//
// 返回值:
//     error: 如果获取服务信息过程中出现错误，则返回错误信息；否则返回nil
//
// 函数逻辑:
//     1. 调用srv.Info()方法获取服务信息
//     2. 如果在获取服务信息过程中发生错误，则返回该错误
//     3. 使用response.OkWithData(ctx, info)将获取到的服务信息作为响应返回给客户端
func Info(ctx *gin.Context) error {
	info, err := srv.Info()
	if err != nil {
		return err
	}
	return response.OkWithData(ctx, info)
}

// Create 函数用于处理创建PocAutoDistributeConfig的HTTP请求
//
// 参数:
//     ctx: *gin.Context，Gin框架的上下文对象，用于处理HTTP请求和响应
//
// 返回值:
//     error: 如果在创建PocAutoDistributeConfig过程中出现错误，则返回错误信息；否则返回nil
//
// 函数逻辑:
//     1. 调用request.Validate函数对HTTP请求进行验证，并将验证后的参数赋值给params
//     2. 如果验证失败，则返回错误信息
//     3. 调用srv.Create函数创建PocAutoDistributeConfig
//     4. 如果创建失败，则返回错误信息
//     5. 使用response.OkWithMessage函数返回成功消息"添加成功"给客户端
func Create(ctx *gin.Context) error {
	params, err := request.Validate(ctx, &poc_auto_distribute_config.PocAutoDistributeConfigCreate{})
	if err != nil {
		return err
	}
	err = srv.Create(params)
	if err != nil {
		return err
	}
	return response.OkWithMessage(ctx, "操作成功")
}
