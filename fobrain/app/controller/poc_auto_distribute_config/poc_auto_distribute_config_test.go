package poc_auto_distribute_config

import (
	"bytes"
	"io/ioutil"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"

	res "fobrain/fobrain/app/repository/poc_auto_distribute_config"
	"fobrain/fobrain/app/request/poc_auto_distribute_config"
)

func TestCreate(t *testing.T) {
	// 创建一个测试请求和响应
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyFuncReturn(res.Create, nil).Reset()

	str := `{
    "is_auto_dispatch": 1,
    "repair_time_type": "level",
    "repair_time_value": [
        {
            "label": "p0",
            "value": 1
        }
    ],
    "vul_repair_principal": "oper",
    "send_notice": 2,
    "send_person_id": [
        "11111"
    ],
    "timeout_notice": 2,
    "timeout_person_id": [
        "222222"
    ],
    "descrition": "33333"
}`
	w := httptest.NewRecorder()
	req := httptest.NewRequest("POST", "/api/v1/threat_center/auto_dispatch", bytes.NewBufferString(str))
	req.Header.Set("Content-Type", "application/json")
	// 创建一个Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 调用函数
	err := Create(c)
	assert.NoError(t, err)

	// 解析响应数据
	response := w.Result()
	defer response.Body.Close()
	body, _ := ioutil.ReadAll(response.Body)
	assert.NotEmpty(t, body)
}

func TestInfo(t *testing.T) {
	info := poc_auto_distribute_config.PocAutoDistributeConfigCreate{
		IsAutoDispatch:     1,
		RepairTimeType:     "level",
		RepairTimeValue:    nil,
		VulRepairPrincipal: "",
		SendNotice:         0,
		SendPersonId:       nil,
		TimeoutNotice:      0,
		TimeoutPersonId:    nil,
		Descrition:         "",
	}
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyFuncReturn(res.Info, info, nil).Reset()
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/threat_center/auto_dispatch", nil)
	req.Header.Set("Content-Type", "application/json")
	// 创建一个Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 调用函数
	err := Info(c)
	assert.NoError(t, err)

	// 解析响应数据
	response := w.Result()
	defer response.Body.Close()
	body, _ := ioutil.ReadAll(response.Body)
	assert.NotEmpty(t, body)

}
