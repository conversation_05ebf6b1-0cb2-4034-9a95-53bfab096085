package threat_center

import (
	"fobrain/fobrain/app/repository/threat"
	"fobrain/fobrain/common/request"
	"fobrain/fobrain/common/response"

	"github.com/gin-gonic/gin"
)

func VulRelevanceList(ctx *gin.Context) error {
	params, err := request.Validate(ctx, &threat.VulRelevanceListRequest{})
	if err != nil {
		return err
	}
	result, err := threat.PocNameIPAggsList(params)
	if err != nil {
		return err
	}

	return response.OkWithPageData(ctx, int64(result.Total), result.Page, result.PerPage, result.Items)
}

func VulRelevanceExport(ctx *gin.Context) error {
	r := struct {
		Ids []string `form:"ids"`
	}{}
	if err := ctx.ShouldBindQuery(&r); err != nil {
		return err
	}
	// 如果前端只传递 ids 这个参数，但是不传递值，数组 gin 默认会处理成长度为1，但是值为零值的数组
	if len(r.Ids) == 1 && r.Ids[0] == "" {
		r.Ids = []string{}
	}
	filePath, err := threat.PocNameIPAggsListExport(r.Ids)
	if err != nil {
		return err
	}
	return response.OkWithFile(ctx, filePath, true)
}

func VulRelevanceIPList(ctx *gin.Context) error {
	params, err := request.Validate(ctx, &threat.VulRelevanceListRequest{})
	if err != nil {
		return err
	}
	result, err := threat.PocIPAggsList(params)
	if err != nil {
		return err
	}

	return response.OkWithPageData(ctx, int64(result.Total), result.Page, result.PerPage, result.Items)
}

func VulRelevanceBusinessList(ctx *gin.Context) error {
	params, err := request.Validate(ctx, &threat.VulRelevanceListRequest{})
	if err != nil {
		return err
	}
	result, err := threat.PocBusinessAggsList(params)
	if err != nil {
		return err
	}

	return response.OkWithPageData(ctx, int64(result.Total), result.Page, result.PerPage, result.Items)
}

func VulRelevanceIPListExport(ctx *gin.Context) error {
	r := struct {
		Ids []string `form:"ids"`
	}{}
	if err := ctx.ShouldBindQuery(&r); err != nil {
		return err
	}
	// 如果前端只传递 ids 这个参数，但是不传递值，数组 gin 默认会处理成长度为1，但是值为零值的数组
	if len(r.Ids) == 1 && r.Ids[0] == "" {
		r.Ids = []string{}
	}
	filePath, err := threat.PocIPAggsListExport(r.Ids)
	if err != nil {
		return err
	}
	return response.OkWithFile(ctx, filePath, true)
}

func VulRelevanceBusinessListExport(ctx *gin.Context) error {
	r := struct {
		Ids []string `form:"ids"`
	}{}
	if err := ctx.ShouldBindQuery(&r); err != nil {
		return err
	}
	// 如果前端只传递 ids 这个参数，但是不传递值，数组 gin 默认会处理成长度为1，但是值为零值的数组
	if len(r.Ids) == 1 && r.Ids[0] == "" {
		r.Ids = []string{}
	}
	filePath, err := threat.PocBusinessAggsListExport(r.Ids)
	if err != nil {
		return err
	}
	return response.OkWithFile(ctx, filePath, true)
}
