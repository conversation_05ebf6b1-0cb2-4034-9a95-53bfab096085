package threat_center

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"fobrain/models/elastic/staff"
	"mime/multipart"

	"github.com/gin-gonic/gin"
	"go-micro.dev/v4/logger"

	srv "fobrain/fobrain/app/repository/threat_center"
	"fobrain/initialize/redis"
	"fobrain/models/mysql/poc_accessorys"
	"fobrain/models/mysql/user"

	"fobrain/fobrain/app/repository/threat"
	"fobrain/fobrain/app/repository/threat_history"
	"fobrain/fobrain/app/services/threat_center"
	"fobrain/fobrain/common/request"
	"fobrain/fobrain/common/response"
	"fobrain/fobrain/logs"
	"fobrain/initialize/mysql"
	"fobrain/models/elastic/poc"
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/threat_histories"
	"fobrain/models/mysql/threat_tasks"
	"fobrain/pkg/utils"
)

var OneThreatHistoryValidate = request.Validate[threat_history.OneThreatHistory]
var SomeThreatHistoryValidate = request.Validate[threat_history.SomeThreatHistory]
var ThreatHistoryOneValidate = request.Validate[threat_history.ThreatHistoryOneRequest]
var ThreatHistoryManualValidate = request.Validate[threat_history.ManualHistory]

type PocID struct {
	PocId string `json:"poc_id" form:"poc_id" uri:"poc_id" validate:"" zh:"漏洞ID"`
}

var PocIdValidate = request.Validate[PocID]

func DistributeBatchParse(ctx *gin.Context) (*threat_history.SomeThreatHistory, map[string]any, []*multipart.FileHeader, *user.User, *staff.Staff, *staff.Staff, error) {
	param, err := SomeThreatHistoryValidate(ctx, &threat_history.SomeThreatHistory{})
	if err != nil {
		return nil, nil, nil, nil, nil, nil, err
	}
	paramList, err := utils.StructToMap(param, "json")
	if err != nil {
		return nil, nil, nil, nil, nil, nil, err
	}
	form, err := ctx.MultipartForm()
	if err != nil {
		return nil, nil, nil, nil, nil, nil, err
	}
	files := form.File["file"]
	for _, file := range files {
		if file.Size > 5*1024*1024 {
			return nil, nil, nil, nil, nil, nil, errors.New("附件大小不能超过5M")
		}
	}
	// 获取当前用户信息
	user := request.GetUserInfo(ctx)

	// 获取人员信息
	staff, err := threat_history.GetStaff(param.ToStaffId)
	if err != nil {
		return nil, nil, nil, nil, nil, nil, err
	}
	if staff == nil {
		return nil, nil, nil, nil, nil, nil, response.FailWithMessage(ctx, "人员不存在")
	}
	ccStaff, err := threat_history.GetStaff(param.ToCc)
	if err != nil {
		return nil, nil, nil, nil, nil, nil, err
	}
	// 判断邮箱是否存在
	if param.TimeoutNotice != 0 {
		if len(staff.Email) <= 0 {
			return nil, nil, nil, nil, nil, nil, response.FailWithMessage(ctx, "人员邮箱不存在")
		}
	}
	return param, paramList, files, user, staff, ccStaff, nil
}

// DistributeBatch 批量派发，转交
func DistributeBatch(ctx *gin.Context) error {
	param, paramList, files, user, staff, ccStaff, err := DistributeBatchParse(ctx)
	if err != nil {
		return err
	}
	fids := make([]string, 0)
	// 获取漏洞信息
	objs, err := threat.ShowMultiple(ctx, param.PocIds, paramList, poc.NewPoc())
	if err != nil {
		return err
	}
	pocObjsMap := make([]map[string]interface{}, 0, len(objs))
	for _, obj := range objs {
		pocObj := obj.(map[string]interface{})
		fid, _ := pocObj["fid"].(string)
		fids = append(fids, fid)
		pocObjsMap = append(pocObjsMap, pocObj)
	}
	err = threat_center.DistributeMutil(user, staff, ctx, param, pocObjsMap, ccStaff)
	if err != nil {
		return err
	}
	if files != nil {
		source := poc_accessorys.VulDistribute
		if param.Status == poc.PocStatusOfForward {
			source = poc_accessorys.VulCare
		}
		for _, file := range files {
			if file.Size > 0 {
				err = srv.UploadPocFile(ctx, file, int64(source), fids, 0)
				if err != nil {
					return err
				}
			}
		}
	}

	return response.Ok(ctx)
}

func StatusOperationBatchParse(ctx *gin.Context) (*threat_history.SomeThreatHistory, map[string]any, []*multipart.FileHeader, *user.User, error) {
	param, err := SomeThreatHistoryValidate(ctx, &threat_history.SomeThreatHistory{})
	if err != nil {
		return nil, nil, nil, nil, err
	}

	paramList, err := utils.StructToMap(param, "json")
	if err != nil {
		return nil, nil, nil, nil, err
	}
	form, err := ctx.MultipartForm()
	if err != nil {
		return nil, nil, nil, nil, err
	}
	files := form.File["file"]
	for _, file := range files {
		if file.Size > 5*1024*1024 {
			return nil, nil, nil, nil, errors.New("附件大小不能超过5M")
		}
	}
	// 获取当前用户信息
	user := request.GetUserInfo(ctx)
	return param, paramList, files, user, nil
}

// StatusOperationBatch 批量延时、催促(无状态修改)、误报、无法修复、修复完成
func StatusOperationBatch(ctx *gin.Context) error {
	param, paramList, files, user, err := StatusOperationBatchParse(ctx)
	if err != nil {
		return err
	}
	// 获取漏洞信息
	objs, err := threat.ShowMultiple(ctx, param.PocIds, paramList, poc.NewPoc())
	if err != nil {
		return err
	}
	fids := make([]string, 0)
	// 统计错误数量
	errCount := 0
	for _, obj := range objs {
		pocObj := obj.(map[string]interface{})
		oneThreatHistory := threat_history.OneThreatHistory{
			PocId:         pocObj["id"].(string),
			ToStaffId:     param.ToStaffId,
			ToStaffName:   param.ToStaffName,
			LimitDate:     param.LimitDate,
			SendNotice:    param.SendNotice,
			TimeoutNotice: param.TimeoutNotice,
			Descrition:    param.Descrition,
			ToCc:          param.ToCc,
			Status:        param.Status,
			ExecNow:       param.ExecNow,
			OriginalId:    param.OriginalId,
			OperationType: param.OperationType,
		}
		// 延时，设置延时时间
		if param.Status == poc.PocStatusOfDelay {
			pocObj["limit_date"] = param.LimitDate
		}
		err = threat_center.StatusOperation(user, pocObj, ctx, &oneThreatHistory)
		if err != nil {
			errCount++
		}
		if err == nil && param.Status == poc.PocStatusOfWaitRetest {
			fids = append(fids, pocObj["fid"].(string))
		}
	}

	if len(objs) != errCount {
		if param.Status == poc.PocStatusOfErrorReport || param.Status == poc.PocStatusOfDelay || param.Status == poc.PocOperateOfUrge ||
			param.Status == poc.PocStatusOfCantRepaired || param.Status == poc.PocStatusOfWaitRetest {
			operationName := "误报"
			if param.Status == poc.PocStatusOfDelay {
				operationName = "延时"
			}
			if param.Status == poc.PocOperateOfUrge {
				operationName = "催促"
			}
			if param.Status == poc.PocStatusOfCantRepaired {
				operationName = "无法修复"
			}
			if param.Status == poc.PocStatusOfWaitRetest {
				operationName = "修复完成"
			}
			// 获取人员信息
			staff, err := threat_history.GetStaff(param.ToStaffId)
			if err != nil {
				return err
			}
			if staff == nil {
				return response.FailWithMessage(ctx, "人员不存在")
			}

			err = threat_history.SendLocalMsgNotice(user.Username, user.Id, staff.FidHash, len(objs)-errCount, operationName)
			if err != nil {
				return err
			}
		}
	}

	if errCount > 0 {
		return response.FailWithMessage(ctx, fmt.Sprintf("批量处理%d条,失败%d条", len(objs), errCount))
	}

	if files != nil {
		source := poc_accessorys.VulDistribute
		if param.Status == poc.PocStatusOfForward {
			source = poc_accessorys.VulCare
		}
		for _, file := range files {
			if file.Size > 0 {
				err = srv.UploadPocFile(ctx, file, int64(source), fids, 0)
				if err != nil {
					return err
				}
			}
		}
	}

	return response.Ok(ctx)
}

// ReTest 复测
func ReTest(ctx *gin.Context) error {
	param, err := SomeThreatHistoryValidate(ctx, &threat_history.SomeThreatHistory{})
	if err != nil {
		return err
	}
	paramList, err := utils.StructToMap(param, "json")
	if err != nil {
		return err
	}
	// 获取漏洞信息
	objs, err := threat.ShowMultiple(ctx, param.PocIds, paramList, poc.NewPoc())
	if err != nil {
		return err
	}
	logger := logs.GetLogger()
	errorCount := 0
	for _, obj := range objs {
		pocObj := obj.(map[string]interface{})
		pocStatus, _ := pocObj["statusCode"].(int)
		// 当前状态是否可以复测
		if _, ok := poc.CanReTestStatus[pocStatus]; !ok {
			logger.Errorf("漏洞id:%s,状态不可复测", pocObj["id"].(string))
			errorCount++
			continue
		}

		// 获取当前用户信息
		user := request.GetUserInfo(ctx)

		content := threat_history.GetOperateContent(user.Account, param.Status, "漏洞", param.ToStaffName)
		oneThreatHistory := threat_history.OneThreatHistory{
			PocId:         pocObj["id"].(string),
			ToStaffId:     param.ToStaffId,
			ToStaffName:   param.ToStaffName,
			LimitDate:     param.LimitDate,
			SendNotice:    param.SendNotice,
			TimeoutNotice: param.TimeoutNotice,
			Descrition:    param.Descrition,
			ToCc:          param.ToCc,
			Status:        param.Status,
			ExecNow:       param.ExecNow,
			OriginalId:    param.OriginalId,
			OperationType: param.OperationType,
		}
		// 创建操作记录
		err = threat_history.CreateHistory(ctx, user, &oneThreatHistory, nil, pocObj["statusCode"].(int), content)
		if err != nil {
			logger.Errorf("创建操作记录失败,漏洞id:%s,err:%s", pocObj["id"].(string), err.Error())
			errorCount++
			continue
		}

		if param.ExecNow == 0 {
			// 创建定时任务
			someThreatHistory := &threat_history.SomeThreatHistory{
				PocIds:            []string{pocObj["id"].(string)},
				Descrition:        param.Descrition,
				ToCc:              param.ToCc,
				Status:            param.Status,
				SendNotice:        param.SendNotice,
				ToStaffId:         param.ToStaffId,
				ToStaffName:       param.ToStaffName,
				LimitDate:         param.LimitDate,
				TimeoutNotice:     param.TimeoutNotice,
				ExecNow:           param.ExecNow,
				OriginalId:        param.OriginalId,
				OperationType:     param.OperationType,
				TimeoutReceiverId: param.TimeoutReceiverId,
				TimeoutFrequency:  param.TimeoutFrequency,
			}
			err = threat_history.CreateThreatTask(threat_tasks.TaskTypeRetest, user, someThreatHistory, nil)
			if err != nil {
				logger.Errorf("创建定时任务失败,漏洞id:%s,err:%s", pocObj["id"].(string), err.Error())
				errorCount++
				continue
			}
			continue
		}

		// 去复测
		err = threat_history.ReTest(ctx, user, &oneThreatHistory, pocObj)
		if err != nil {
			logger.Errorf("复测失败,漏洞id:%s,err:%s", pocObj["id"].(string), err.Error())
			errorCount++
			continue
		}

		// 修改状态
		err = threat_history.UpdateThreatStatus(&oneThreatHistory, pocObj, nil, nil)
		if err != nil {
			logger.Errorf("修改状态失败,漏洞id:%s,err:%s", pocObj["id"].(string), err.Error())
			errorCount++
			continue
		}
	}
	if errorCount > 0 {
		return response.FailWithMessage(ctx, fmt.Sprintf("批量处理%d条,失败%d条", len(objs), errorCount))
	}

	return response.Ok(ctx)
}

// ReTestManual 人工复测
func ReTestManual(ctx *gin.Context) error {
	paramManual, err := ThreatHistoryManualValidate(ctx, &threat_history.ManualHistory{})
	if err != nil {
		return err
	}
	paramList, err := utils.StructToMap(paramManual, "json")
	if err != nil {
		return err
	}
	form, err := ctx.MultipartForm()
	if err != nil {
		return err
	}
	files := form.File["file"]
	for _, file := range files {
		if file.Size > 5*1024*1024 {
			return errors.New("附件大小不能超过5M")
		}
	}
	// 获取当前用户信息
	user := request.GetUserInfo(ctx)
	logger := logs.GetLogger()
	// 复测只能复测未处理的漏洞
	paramList["data_range"] = 1
	// 获取所有漏洞信息
	objs, err := threat.ShowMultiple(ctx, paramManual.PocIds, paramList, poc.NewPoc())
	if err != nil {
		return err
	}
	errorCount := 0
	// 收集所有漏洞的fid
	fids := make([]string, 0)
	for _, obj := range objs {
		pocObj := obj.(map[string]interface{})
		pocStatus, _ := pocObj["statusCode"].(int)
		// 是否可以复测
		if _, ok := poc.CanReTestStatus[pocStatus]; !ok {
			logger.Errorf("漏洞id:%s,状态不可复测", pocObj["id"].(string))
			errorCount++
			continue
		}
		param := &threat_history.OneThreatHistory{
			PocId:         pocObj["id"].(string),
			Descrition:    paramManual.Descrition,
			OperationType: "manual",
			Status:        pocStatus,
		}
		if paramManual.Passed == 0 {
			param.Status = 15
		} else {
			param.Status = 30
		}

		content := threat_history.GetOperateContent(user.Account, param.Status, "漏洞")
		// 创建操作记录
		err = threat_history.CreateHistory(ctx, user, param, nil, pocObj["statusCode"].(int), content)
		if err != nil {
			logger.Errorf("创建操作记录失败,漏洞id:%s,err:%s", pocObj["id"].(string), err.Error())
			errorCount++
			continue
		}
		// 派发
		if paramManual.Distribute == 1 {
			vulReTestDistribute(paramManual.ToStaffId, pocObj["id"].(string), user)
		}
		// 修改状态
		err = threat_history.UpdateThreatStatus(param, pocObj, nil, nil)
		if err != nil {
			logger.Errorf("修改状态失败,漏洞id:%s,err:%s", pocObj["id"].(string), err.Error())
			errorCount++
			continue
		}
		fids = append(fids, pocObj["fid"].(string))
	}

	for _, file := range files {
		if file.Size > 0 {
			err = srv.UploadPocFile(ctx, file, poc_accessorys.VulRetest, fids, 0)
			if err != nil {
				logger.Errorf("上传附件失败,err:%s", err.Error())
				continue
			}
		}
	}

	// 如果存在失败，则返回失败
	// 要求这个判断必须在文件保存和派发之后，防止正常处理的数据没有被派发
	if errorCount > 0 {
		return response.FailWithMessage(ctx, fmt.Sprintf("批量处理%d条,失败%d条", len(objs), errorCount))
	}
	return response.Ok(ctx)
}

func vulReTestDistribute(toStaffId string, pocId string, user *user.User) {
	staff, err := threat_history.GetStaff(toStaffId)
	if err != nil {
		logger.Errorf("Fobrain vulReTestDistribute err:%s", err.Error())
	}

	client := redis.GetRedisClient()
	strategiesData := map[string]interface{}{
		"to_staff_id": toStaffId,
		"poc_ids":     pocId,
		"status":      poc.PocStatusOfForward,
		"user":        user,
		"staff":       staff,
	}
	dataJson, _ := json.Marshal(strategiesData)
	client.Set(context.Background(), fmt.Sprintf("cache:%s", pocId), dataJson, 0)
}

// GetNodeInfo 获取节点信息
func GetNodeInfo(ctx *gin.Context) error {
	param, err := PocIdValidate(ctx, &PocID{})
	if err != nil {
		return err
	}

	allNodeIds := []uint64{}
	if param.PocId != "" {
		// 获取漏洞信息
		obj, err := threat.Show(ctx, param.PocId)
		if err != nil {
			return err
		}
		pocObj := obj.(map[string]interface{})

		allNodeIds, _ = pocObj["all_node_ids"].([]uint64)
	}

	var opts []mysql.HandleFunc
	opts = append(opts, mysql.WithWhere("deleted_at", nil))
	if len(allNodeIds) > 0 {
		opts = append(opts, mysql.WithWhere("id", allNodeIds))
	}

	nodes, err := data_source.NewNodeModel().Item(opts...)
	if err != nil {
		return err
	}

	type nodeItem struct {
		ID   uint64 `json:"id"`
		Name string `json:"name"`
	}
	list := []nodeItem{}
	for _, v := range nodes {
		list = append(list, nodeItem{
			ID:   v.Id,
			Name: v.Name,
		})
	}
	return response.OkWithData(ctx, list)
}

// GetHistory 获取历史记录
func GetHistory(ctx *gin.Context) error {
	param, err := ThreatHistoryOneValidate(ctx, &threat_history.ThreatHistoryOneRequest{})
	if err != nil {
		return err
	}

	var opts []mysql.HandleFunc
	opts = append(opts, mysql.WithWhere("poc_id", param.Id))
	if param.Type != "" {
		opts = append(opts, mysql.WithWhere("category", param.Type))
	}
	// 按照id排序，因为历史记录的id是连续的，所以按照id排序可以保证历史记录的顺序
	opts = append(opts, mysql.WithOrder("id DESC"))
	th := threat_histories.NewThreatHistoryModel()
	list, _, err := th.List(0, 500, opts...)
	if err != nil {
		return err
	}

	sourceMap, err := threat_history.GetSourceIcons()
	if err != nil {
		return err
	}

	return response.OkWithData(ctx, th.ShowFields(list, sourceMap))
}
