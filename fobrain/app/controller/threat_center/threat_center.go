package threat_center

import (
	"context"
	"errors"
	"fmt"
	"fobrain/fobrain/app/services/export"
	"fobrain/fobrain/app/services/export/handlers"
	"io/ioutil"
	"net/http"
	"net/url"
	"path/filepath"
	"strings"

	"github.com/gabriel-vasile/mimetype"
	"github.com/olivere/elastic/v7"

	"fobrain/fobrain/app/repository/threat"
	"fobrain/fobrain/app/repository/threat_center"
	"fobrain/fobrain/app/request/asset_center"
	"fobrain/fobrain/app/services/poc"
	"fobrain/fobrain/common/auth"
	"fobrain/fobrain/common/request"
	"fobrain/fobrain/common/response"
	"fobrain/fobrain/common/validate"
	pb "fobrain/mergeService/proto"
	"fobrain/models/mysql/poc_accessorys"
	"fobrain/pkg/cfg"
	"fobrain/pkg/utils"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

type (
	CommonThreatRequest struct {
		SourceIds           []uint64 `json:"source_ids" form:"source_ids" uri:"source_ids" zh:"数据源"`
		ThreatName          []string `json:"threat_name" form:"threat_name" uri:"threat_name" zh:"漏洞名称"`
		Ips                 []string `json:"ips" form:"ips" uri:"ips"  zh:"IP地址"`
		Ports               []uint64 `json:"ports" form:"ports" uri:"ports"  zh:"端口"`
		Area                []uint64 `json:"area" form:"area" uri:"area"  zh:"所属区域"`
		ThreatUrl           []string `json:"threat_url" form:"threat_url" uri:"threat_url" zh:"漏洞地址"`
		ThreatType          []string `json:"threat_type" form:"threat_type" uri:"threat_type" zh:"漏洞类型"`
		ThreatLevel         []uint64 `json:"threat_level" form:"threat_level" uri:"threat_level" zh:"漏洞等级 低危：1 中危：2 高危：3 严重：4 未知：5-【策略】"`
		ThreatStatus        []uint64 `json:"threat_status" form:"threat_status" uri:"threat_status" zh:"漏洞状态-【唯一】漏洞流转状态：1新增 2复现 3修复 4未修复 5误报"`
		Risk                []uint64 `json:"risk" form:"risk" uri:"risk" zh:"风险值"`
		ThreatRepairPerson  []string `json:"threat_repair_person" form:"threat_repair_person" uri:"threat_repair_person" zh:"漏洞修复负责人"`
		RepairPriority      []string `json:"repair_priority" form:"repair_priority" uri:"repair_priority" zh:"修复优先级"`
		FirstTime           []string `json:"first_time" form:"first_time" uri:"first_time"  zh:"首次上报时间"`
		FinallyTime         []string `json:"finally_time" form:"finally_time" uri:"finally_time"  zh:"最后上报时间"`
		Field               string   `json:"field" form:"field" uri:"field"  zh:"排序字段"`
		Order               string   `json:"order" form:"order" uri:"order"  zh:"排序方式"`
		OperationTypeString string   `json:"operation_type_string" form:"operation_type_string" uri:"operation_type_string" validate:"omitempty,oneof=in not_in == !== null not_null = !="  zh:"操作类型"`
		DataRange           int      `json:"data_range" form:"data_range" uri:"data_range"  zh:"数据范围 1未处理 2已处理 3回收站"`
		// IsRecycleBin        int      `json:"is_recycle_bin" form:"is_recycle_bin" uri:"is_recycle_bin"  zh:"是否查询回收站 1是2否"`
		SearchCondition []string `json:"search_condition" form:"search_condition" uri:"search_condition"  zh:"搜索条件"`
	}
	ThreatListRequest struct {
		request.PageRequest
		Keyword string   `json:"keyword" form:"keyword" uri:"keyword" validate:"omitempty,max=100" zh:"模糊搜索"`
		Name    string   `json:"name" form:"name" uri:"name" validate:"omitempty,max=100" zh:"漏洞名称"`
		Ip      []string `json:"ip" form:"ip" uri:"ip" validate:"omitempty" zh:"IP"` // 多个 ip 用英文逗号分隔
		Area    uint64   `json:"area" form:"area" uri:"area" validate:"omitempty" zh:"所属区域"`
		CommonThreatRequest
	}

	DeleteToRecycleBinRequest struct {
		Ids     []string `json:"ids" form:"ids" uri:"ids" validate:"required" zh:"漏洞IDS"`
		Ip      []string `json:"ip" form:"ip" uri:"ip" validate:"omitempty" zh:"IP"`
		Keyword string   `json:"keyword" form:"keyword" uri:"keyword" validate:"omitempty,max=100" zh:"模糊搜索"`
		CommonThreatRequest
	}

	ThreatOneRequest struct {
		Id   string `json:"id" form:"id" uri:"id" validate:"required,min=1" zh:"漏洞ID"`
		Name string `json:"name" form:"name" uri:"name" validate:"omitempty,max=100" zh:"漏洞名称"`
	}

	ThreatIdsRequest struct {
		Ids     []string `json:"ids" form:"ids" uri:"ids" validate:"" zh:"漏洞IDS"`
		Keyword string   `json:"keyword" form:"keyword" uri:"keyword" zh:"模糊搜索"`
		CommonThreatRequest
	}
)

var ThreatListValidate = request.Validate[ThreatListRequest]
var ThreatOneValidate = request.Validate[ThreatOneRequest]
var ThreatIdsValidate = request.Validate[ThreatIdsRequest]

// List
// 漏洞列表
// @Router /api/v1/threat_center [get]
func List(ctx *gin.Context) error {
	params, err := ThreatListValidate(ctx, &ThreatListRequest{})
	if err != nil {
		return err
	}
	// 获取当前用户信息
	user := request.GetUserInfo(ctx)

	paramList, err := utils.StructToMap(params.CommonThreatRequest, "json")
	if err != nil {
		return err
	}

	isSuper := auth.IsSuperManage(ctx)
	staffIdsAny, _ := ctx.Get("staff_ids")
	list, total, err := threat.List(ctx, []uint64{params.Area}, params.Ip, params.Keyword, params.Page, params.PerPage, user, paramList, isSuper, staffIdsAny.([]string))
	if err != nil {
		return err
	}
	return response.OkWithPageData(ctx, total, params.Page, params.PerPage, list)
}

// Show
// 漏洞详情
func Show(ctx *gin.Context) error {
	params, err := ThreatOneValidate(ctx, &ThreatOneRequest{})
	if err != nil {
		return err
	}
	show, err := threat.Show(ctx, params.Id)
	if err != nil {
		return err
	}

	return response.OkWithData(ctx, show)
}

// Export
// 漏洞导出
// @Router /api/v1/threat_center/export [get]
func Export(ctx *gin.Context) error {
	params, err := ThreatIdsValidate(ctx, &ThreatIdsRequest{})
	if err != nil {
		return err
	}

	// 转换为处理器需要的类型
	handlerParams := &handlers.ThreatIdsRequest{
		Ids:     params.Ids,
		Keyword: params.Keyword,
		CommonThreatRequest: handlers.CommonThreatRequest{
			SourceIds:           params.SourceIds,
			ThreatName:          params.ThreatName,
			Ips:                 params.Ips,
			Ports:               params.Ports,
			Area:                params.Area,
			ThreatUrl:           params.ThreatUrl,
			ThreatType:          params.ThreatType,
			ThreatLevel:         params.ThreatLevel,
			ThreatStatus:        params.ThreatStatus,
			Risk:                params.Risk,
			ThreatRepairPerson:  params.ThreatRepairPerson,
			RepairPriority:      params.RepairPriority,
			FirstTime:           params.FirstTime,
			FinallyTime:         params.FinallyTime,
			Field:               params.Field,
			Order:               params.Order,
			OperationTypeString: params.OperationTypeString,
			DataRange:           params.DataRange,
			SearchCondition:     params.SearchCondition,
		},
	}

	// 使用新的导出框架
	handler := handlers.NewPocHandler("")
	exportService := export.NewService().WithConcurrency(10)

	path, err := exportService.Export(ctx, handler, handlerParams)
	if err != nil {
		return err
	}

	return response.OkWithFile(ctx, path, true)
}

// ManualCalibration 手动校准
func ManualCalibration(c *gin.Context) error {
	param := &pb.ManualCalibrationRequest{}
	err := c.ShouldBind(param)
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	param.BusinessType = "vuln"
	param.BatchNo = strings.ReplaceAll(uuid.New().String(), "-", "")
	if ok, msg := validate.Validator(param); !ok {
		return response.FailWithMessage(c, msg)
	}

	data, err := pb.GetProtoClient().ManualCalibration(context.Background(), param, pb.ClientWithAddress)
	if err != nil {
		return err
	}

	return response.OkWithData(c, data)
}

// Status 状态
func Status(c *gin.Context) error {
	return response.OkWithData(c, poc.StatusList())
}

// Create 添加
func Create(ctx *gin.Context) error {
	params, err := request.Validate(ctx, &asset_center.ThreatCreateRequest{})
	if err != nil {
		return err
	}
	form, err := ctx.MultipartForm()
	if err != nil {
		return err
	}
	files := form.File["file"]
	for _, file := range files {
		if file.Size > 5*1024*1024*10 {
			return errors.New("附件大小不能超过50M")
		}
	}
	// params.OriginalId = uuid.New().String()
	for _, file := range files {
		if file.Size > 0 {
			err = threat_center.UploadPocFile(ctx, file, poc_accessorys.OneImportSource, []string{params.OriginalId}, 0)
			if err != nil {
				return err
			}
		}
	}
	err = threat_center.Create(params)
	if err != nil {
		return err
	}
	return response.OkWithMessage(ctx, "添加成功")
}

func Accessory(ctx *gin.Context) error {
	params, err := request.Validate(ctx, &asset_center.ThreatAccessoryRequest{})
	if err != nil {
		return err
	}
	list, total, err := threat_center.Accessory(params)
	if err != nil {
		return err
	}
	return response.OkWithPageData(ctx, total, params.Page, params.PerPage, list)
}

func Upload(ctx *gin.Context) error {
	form, err := ctx.MultipartForm()
	if err != nil {
		return err
	}
	ids := form.Value["id"]
	if len(ids) == 0 {
		return errors.New("id不能为空")
	}
	files := form.File["file"]
	if len(files) == 0 {
		return errors.New("附件不能为空")
	}
	// 查询漏洞
	query := elastic.NewBoolQuery()
	query = query.Must(elastic.NewTermQuery("id", ids[0]))
	list, err := threat.GetByQuery(query, []string{"fid"})
	if err != nil || len(list) == 0 {
		return errors.New("漏洞查询失败或者漏洞不存在")
	}
	fid := list[0].Fid
	if fid == "" {
		return errors.New("异常漏洞数据，fid为空")
	}
	failsList := make([]string, 0)
	for _, file := range files {
		if file.Size > 5*1024*1024*10 {
			failsList = append(failsList, fmt.Sprintf("%s: 附件大小不能超过50M", file.Filename))
			continue
		}
		if file != nil && file.Size > 0 {
			err = threat_center.UploadPocFile(ctx, file, poc_accessorys.VulDetail, []string{fid}, 0)
			if err != nil {
				failsList = append(failsList, fmt.Sprintf("%s: %s", file.Filename, err.Error()))
				continue
			}
		}
	}
	return response.OkWithData(ctx, map[string]any{"details": failsList, "msg": "上传完成"})
}

func Delete(ctx *gin.Context) error {
	params, err := request.Validate(ctx, &struct {
		Ids []int `json:"ids" form:"ids" uri:"ids" validate:"required,min=1" zh:"文件名Id"`
	}{})
	if err != nil {
		return err
	}
	failedIds, err := threat_center.Delete(params.Ids)
	if err != nil {
		return err
	}
	if len(failedIds) > 0 {
		return response.OkWithMessage(ctx, fmt.Sprintf("删除成功，部分文件删除失败，id: %v", failedIds))
	}
	return response.OkWithMessage(ctx, "删除成功")
}

func DownloadFile(ctx *gin.Context) error {
	params, err := request.Validate(ctx, &struct {
		Id int `json:"id" form:"id" uri:"id" validate:"required,number" zh:"文件名Id"`
	}{})
	if err != nil {
		return err
	}
	info, err := threat_center.Info(params.Id)
	if err != nil {
		return err
	}

	storagePath := cfg.LoadCommon().StoragePath
	if !utils.FileExists(filepath.Join(storagePath, info.Path)) {
		return errors.New("文件不存在")
	}
	data, err := ioutil.ReadFile(filepath.Join(storagePath, info.Path))
	if err != nil {
		return err
	}
	// Compatible with Chinese.
	fileName := url.PathEscape(info.Name)

	// Detect returns the MIME type found from the provided byte slice.
	detect := mimetype.Detect(data)

	// Setting http responses header.
	ctx.Writer.Header().Set("Content-Disposition", `attachment; filename*=UTF-8''`+fileName+``)
	ctx.Writer.Header().Set("Content-Type", detect.String())

	// Write data into the responses body.
	if _, err = ctx.Writer.Write(data); err != nil {
		return err
	}

	// Write status code into the response header.
	ctx.Writer.WriteHeader(http.StatusOK)

	return nil
}

// DeleteToRecycleBin 删除到回收站
func DeleteToRecycleBin(ctx *gin.Context) error {
	params, err := request.Validate(ctx, &DeleteToRecycleBinRequest{})
	if err != nil {
		return err
	}
	paramList, err := utils.StructToMap(params.CommonThreatRequest, "json")
	if err != nil {
		return err
	}

	isSuper := auth.IsSuperManage(ctx)
	staffIdsAny, _ := ctx.Get("staff_ids")
	staffIds, _ := staffIdsAny.([]string)
	err = threat.DeleteToRecycleBin(ctx, params.Ids, params.Ip, params.Keyword, paramList, isSuper, staffIds)
	if err != nil {
		return err
	}
	return response.OkWithMessage(ctx, "删除成功")
}

// DeleteRecycleBin 删除回收站数据
func DeleteRecycleBin(ctx *gin.Context) error {
	params, err := request.Validate(ctx, &DeleteToRecycleBinRequest{})
	if err != nil {
		return err
	}
	paramList, err := utils.StructToMap(params.CommonThreatRequest, "json")
	if err != nil {
		return err
	}

	isSuper := auth.IsSuperManage(ctx)
	staffIdsAny, _ := ctx.Get("staff_ids")
	err = threat.DeleteRecycleBin(ctx, params.Ids, params.Ip, params.Keyword, paramList, isSuper, staffIdsAny.([]string))
	if err != nil {
		return err
	}
	return response.OkWithMessage(ctx, "删除成功")
}
