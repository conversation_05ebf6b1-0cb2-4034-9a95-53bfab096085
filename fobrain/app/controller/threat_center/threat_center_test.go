package threat_center

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/alicebob/miniredis/v2"
	redis2 "github.com/go-redis/redis/v8"

	"fobrain/fobrain/app/repository/threat_center"
	pb "fobrain/mergeService/proto"
	"fobrain/models/mysql/custom_column"
	"fobrain/models/mysql/network_areas"

	"fobrain/fobrain/app/repository/threat"
	testcommon "fobrain/fobrain/tests/common_test"

	"github.com/agiledragon/gomonkey/v2"
	. "github.com/agiledragon/gomonkey/v2"
	"github.com/gin-gonic/gin"
	"github.com/olivere/elastic/v7"
	. "github.com/smartystreets/goconvey/convey"
	"github.com/stretchr/testify/assert"
)

func TestList(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	mockServer.Register("poc/_search", []*elastic.SearchHit{
		{
			Id:     "1",
			Source: []byte(`{"name":"example"}`),
		},
	})
	mockDb := testcommon.InitSqlMock()
	defer mockDb.Close()
	// SELECT * FROM `users` WHERE `id` = ? ORDER BY `users`.`id` LIMIT 1
	mockDb.ExpectQuery("SELECT * FROM `users` WHERE `id` = ? ORDER BY `users`.`id` LIMIT 1").
		WithArgs(1).
		WillReturnRows(sqlmock.NewRows([]string{"id", "account", "username"}).AddRow(1, "admin", "admin"))

	// SELECT * FROM `roles` WHERE `id` in (SELECT `role_id` FROM `users_roles` WHERE `user_id`=?)
	mockDb.ExpectQuery("SELECT * FROM `roles` WHERE `id` in (SELECT `role_id` FROM `users_roles` WHERE `user_id`=?)").
		WithArgs(1).
		WillReturnRows(sqlmock.NewRows([]string{"id", "name", "key"}).AddRow(1, "admin", "admin"))
	// SELECT * FROM `users_roles` WHERE `user_id` = ?
	mockDb.ExpectQuery("SELECT * FROM `users_roles` WHERE `user_id` = ?").
		WithArgs(1).
		WillReturnRows(sqlmock.NewRows([]string{"user_id", "role_id"}).AddRow(1, 1))
	// SELECT count(*) FROM `roles` WHERE `id` IN (?) AND `key` = ?
	mockDb.ExpectQuery("SELECT count(*) FROM `roles` WHERE `id` IN (?) AND `key` = ?").
		WithArgs(1, "admin").
		WillReturnRows(sqlmock.NewRows([]string{"count(*)"}).AddRow(1))

	// 创建一个测试请求和响应
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/threat_center?page=1&per_page=10", nil)
	// 创建一个Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Set("user_id", uint64(1))
	c.Set("staff_ids", []string{"ss"})
	c.Set("is_super_manage", false)
	c.Request = req

	// 调用函数
	err := List(c)

	// 断言错误为 nil
	assert.Empty(t, err)
	assert.NotEmpty(t, w.Body.String())
}

func TestShow(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		mockServer := testcommon.NewMockServer()
		defer mockServer.Close()

		mockServer.Register("poc/_search", []*elastic.SearchHit{
			{
				Id:     "1",
				Source: []byte(`{"name":"example"}`),
			},
		})

		// 创建一个测试请求和响应
		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/v1/threat_center/?id=1&page=1&per_page=10", nil)
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req
		c.Set("user_id", uint64(1))
		c.Set("is_super_manage", true)
		// 调用函数
		err := Show(c)

		// 断言错误为 nil
		assert.Empty(t, err)
	})

}

func TestExport(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	s := miniredis.RunT(t)
	defer s.Close()

	client := redis2.NewClient(&redis2.Options{Addr: s.Addr()})
	testcommon.SetRedisClient(client)

	// 用于跟踪搜索调用次数
	var searchCallCount int

	// mock ES 数据计数 - 确保有数据
	mockServer.Register("/poc/_count", map[string]interface{}{
		"count": 1,
		"_shards": map[string]interface{}{
			"total":      1,
			"successful": 1,
			"skipped":    0,
			"failed":     0,
		},
	})

	// 注册自定义搜索处理器，正确处理 search_after 分页查询
	mockServer.RegisterHandler("/poc/_search", func(w http.ResponseWriter, r *http.Request) {
		searchCallCount++
		w.Header().Set("Content-Type", "application/json")

		if searchCallCount == 1 {
			// 第一次查询，返回有数据的响应
			response := elastic.SearchResult{
				Hits: &elastic.SearchHits{
					TotalHits: &elastic.TotalHits{
						Value:    1,
						Relation: "eq",
					},
					Hits: []*elastic.SearchHit{
						{
							Index: "poc",
							Id:    "1",
							Source: []byte(`{
								"id": "1",
								"fid": "poc-001",
								"ip": "************",
								"port": 80,
								"protocol": "http",
								"url": "http://************/test",
								"name": "测试漏洞",
								"level": 2,
								"vul_type": "SQL注入",
								"cve": "CVE-2024-0001",
								"status": 1,
								"person": {
									"name": "张三",
									"mobile": "13800138000"
								},
								"all_source_ids": [1],
								"created_at": "2024-01-01 10:00:00",
								"updated_at": "2024-01-01 10:00:00"
							}`),
							Sort: []interface{}{"2024-01-01 10:00:00", "1"},
						},
					},
				},
			}
			json.NewEncoder(w).Encode(response)
		} else {
			// 后续查询，返回空结果以结束分页
			response := elastic.SearchResult{
				Hits: &elastic.SearchHits{
					TotalHits: &elastic.TotalHits{
						Value:    0,
						Relation: "eq",
					},
					Hits: []*elastic.SearchHit{},
				},
			}
			json.NewEncoder(w).Encode(response)
		}
	})

	// mock 数据库查询
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()

	// mock 数据源查询
	mockDb.ExpectQuery("SELECT * FROM `data_sources`").
		WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).AddRow(1, "测试数据源"))

	patch := gomonkey.NewPatches()
	patch.ApplyFuncReturn(network_areas.AllNetworkArea, map[uint64]string{
		1: "example_area",
	})
	patch.ApplyMethodReturn(&custom_column.CustomFieldMeta{}, "GetByModuleType", []*custom_column.CustomFieldMeta{
		{
			FieldKey:    "custom_key",
			DisplayName: "自定义字段",
		},
	}, nil)
	defer patch.Reset()

	// 创建一个测试请求和响应
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/threat_center/export?ids=1&page=1&per_page=10", nil)
	// 创建一个Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req
	c.Set("user_id", uint64(1))
	c.Set("is_super_manage", true)
	// 调用函数
	err := Export(c)

	// 断言错误为 nil
	assert.Empty(t, err)
}

func TestStatus(t *testing.T) {
	// 创建一个测试请求和响应
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/threat_center/status", nil)
	// 创建一个Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 调用函数
	err := Status(c)

	// 断言错误为 nil
	assert.Empty(t, err)
	assert.NotEmpty(t, w.Body.String())
}

func TestManualCalibration(t *testing.T) {
	Convey("TestManualCalibration", t, func() {
		Convey("Success", func() {
			defer ApplyMethodReturn(pb.GetProtoClient(), "ManualCalibration", nil, nil).Reset()
			// 创建一个测试请求和响应
			w := httptest.NewRecorder()
			req := httptest.NewRequest("POST", "/api/v1/threat_center/manual_calibration", nil)
			// 创建一个Gin上下文
			c, _ := gin.CreateTestContext(w)
			c.Request = req

			// 调用函数
			err := ManualCalibration(c)

			// 断言错误为 nil
			assert.Empty(t, err)
			assert.NotEmpty(t, w.Body.String())
		})
	})
}

func TestVulRelevanceList(t *testing.T) {
	// 创建一个新的 gin 路由
	router := gin.Default()
	router.POST("/vul-relevance-list", func(ctx *gin.Context) {
		VulRelevanceList(ctx)
	})

	// 模拟请求
	req, _ := http.NewRequest(http.MethodPost, "/vul-relevance-list", nil)
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	patch := gomonkey.ApplyFunc(threat.PocNameIPAggsList, func(req *threat.VulRelevanceListRequest) (*threat.PocNameIPAggsListResponse, error) {
		return &threat.PocNameIPAggsListResponse{
			Total:   1,
			Page:    1,
			PerPage: 1,
			Items:   []*threat.PocNameIPAggregation{},
		}, nil
	})
	defer patch.Reset()

	w = httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

}

func TestCreate(t *testing.T) {
	patches := mockDependencies()
	defer func() {
		for _, patch := range patches {
			patch.Reset()
		}
	}()
	vuln := struct {
		Area    string `json:"area"`
		IP      string `json:"ip"`
		URL     string `json:"url"`
		Level   int    `json:"level"`
		Name    string `json:"name"`
		VulType string `json:"vulType"`
		CVE     string `json:"cve"`
		IsPoc   int    `json:"isPoc"`
	}{
		Area:    "1",
		IP:      "127.0.0.1",
		URL:     "http://127.0.0.1/api/test",
		Level:   1,
		Name:    "123",
		VulType: "弱口令",
		CVE:     "dsfsdfssf",
		IsPoc:   1,
	}
	// 生成多部分表单
	writer, body, err := genMultipartForm(vuln)
	assert.Nil(t, err)

	t.Run("Success ", func(t *testing.T) {
		time.Sleep(time.Second)
		patches := []*gomonkey.Patches{
			gomonkey.ApplyFuncReturn(threat_center.Create, nil),
		}
		// 创建一个测试请求和响应
		w := httptest.NewRecorder()
		req := httptest.NewRequest("POST", "/api/v1/threat_center", body)
		req.Header.Set("Content-Type", writer.FormDataContentType())
		req.Header.Set("Content-Length", fmt.Sprint(body.Len()))

		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		// 调用函数
		err := Create(c)
		for _, patch := range patches {
			patch.Reset()
		}
		assert.NoError(t, err)
	})
}

func TestAccessory(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()
	mockServer.Register("poc/_search", []*elastic.SearchHit{
		{
			Id:     "1",
			Source: []byte(`{"ip":"***********", "updated_at":"2023-04-25 12:34:56", "created_at":"2023-04-25 12:34:56","fid":"xxxxx","original_ids":["dfkladjfklajlfajl"]}`),
		},
	})
	mockDb.ExpectQuery("SELECT count(*) FROM `poc_accessorys` LEFT JOIN users  ON poc_accessorys.user_id = users.id WHERE poc_accessorys.fid = ? or poc_accessorys.fid in (?)").
		WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg()).
		WillReturnRows(mockDb.NewRows([]string{"count"}).AddRow(1))
	mockDb.ExpectQuery("SELECT poc_accessorys.*,users.username as user_name FROM `poc_accessorys` LEFT JOIN users ON poc_accessorys.user_id = users.id WHERE poc_accessorys.fid = ? or poc_accessorys.fid in (?) ORDER BY poc_accessorys.id desc LIMIT 10").
		WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg()).
		WillReturnRows(mockDb.NewRows([]string{"id"}).
			AddRow(1))

	// 创建一个测试请求和响应
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/threat_center/files?id=sdfhsfksdhfkhsd&page=1&per_page=10", nil)
	req.Header.Set("Content-Type", "application/json")

	// 创建一个Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 调用函数
	err := Accessory(c)
	assert.NoError(t, err)
}

func TestDownloadFile(t *testing.T) {
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()
	mockDb.ExpectQuery("SELECT * FROM `poc_accessorys` WHERE id = ? ORDER BY `poc_accessorys`.`id` LIMIT 1").
		WillReturnRows(mockDb.NewRows([]string{"id", "path", "name"}).AddRow(1, "app/upload_poc/a.doc", "doc"))

	// 创建一个测试请求和响应
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/threat_center/download/files?id=1", nil)
	req.Header.Set("Content-Type", "application/json")

	// 创建一个Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 调用函数
	err := DownloadFile(c)
	assert.Error(t, err)
}
