package threat_center

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	srv "fobrain/fobrain/app/repository/threat_center"
	"mime/multipart"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"git.gobies.org/caasm/fobrain-components/utils"

	"fobrain/models/elastic/staff"

	"fobrain/fobrain/app/repository/threat"
	"fobrain/fobrain/app/repository/threat_history"
	"fobrain/fobrain/app/services/threat_center"
	"fobrain/fobrain/common/request"
	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/models/mysql/user"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/alicebob/miniredis/v2"
	"github.com/gin-gonic/gin"
	redis2 "github.com/go-redis/redis/v8"
	"github.com/olivere/elastic/v7"
	"github.com/stretchr/testify/assert"
)

// 模拟依赖函数
func mockDependencies() []*gomonkey.Patches {
	return []*gomonkey.Patches{
		gomonkey.ApplyFuncReturn(threat_center.DistributeMutil, nil),                                              // 模拟 Distribute 函数
		gomonkey.ApplyFuncReturn(threat_history.GetStaff, &staff.Staff{Email: []string{"<EMAIL>"}}, nil), // 模拟 GetStaff 函数
		gomonkey.ApplyFuncReturn(threat.ShowMultiple, []interface{}{}, nil),                                       // 模拟 ShowMultiple 函数
		gomonkey.ApplyFuncReturn(threat_history.SendLocalMsgNotice, nil),                                          // 模拟 SendLocalMsgNotice 函数
		gomonkey.ApplyFuncReturn(srv.UploadPocFile, nil),                                                          // 模拟 UploadPocFile 函数
	}
}

// 生成多部分表单（包含文件和其他参数）
func genMultipartForm(params any) (*multipart.Writer, *bytes.Buffer, error) {
	body := &bytes.Buffer{}
	writer := multipart.NewWriter(body)

	// 添加文件字段
	fileContent := []byte("Hello, World!")
	part, err := writer.CreateFormFile("file", "testfile.txt")
	if err != nil {
		return nil, nil, err
	}
	_, err = part.Write(fileContent)
	if err != nil {
		return nil, nil, err
	}

	// 添加其他参数
	paramMap, err := utils.StructToMap(params, "json")
	if err != nil {
		return nil, nil, err
	}
	for key, value := range paramMap {
		_ = writer.WriteField(key, fmt.Sprintf("%v", value))
	}

	// 关闭 writer 以完成表单构造
	writer.Close()
	return writer, body, nil
}

// 测试正常情况
func TestDistributeBatch_Success(t *testing.T) {
	patches := mockDependencies()
	defer func() {
		for _, patch := range patches {
			patch.Reset()
		}
	}()

	// 构造请求参数
	params := threat_history.SomeThreatHistory{
		PocIds:        []string{"poc1"},
		Status:        16,
		ToStaffId:     "staff1",
		TimeoutNotice: 1,
		ToStaffName:   "Test Staff",
		LimitDate:     "2023-12-31 10:23:00",
		SendNotice:    1,
		Descrition:    "Test Description",
		ToCc:          "<EMAIL>",
		ExecNow:       1,
		OriginalId:    "original1",
		OperationType: "distribute",
	}

	// 生成多部分表单
	writer, body, err := genMultipartForm(params)
	assert.Nil(t, err)

	// 创建 HTTP 请求
	req := httptest.NewRequest(http.MethodPost, "/api/v1/threat_center/distribute", body)
	req.Header.Set("Content-Type", writer.FormDataContentType())
	req.Header.Set("Content-Length", fmt.Sprint(body.Len()))

	// 创建 Gin 上下文
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 调用被测试函数
	err = DistributeBatch(c)
	assert.Nil(t, err)
	assert.Equal(t, http.StatusOK, w.Code)
}

// 测试文件大小超过限制
func TestDistributeBatch_FileTooLarge(t *testing.T) {
	// 模拟依赖
	patches := mockDependencies()
	defer func() {
		for _, patch := range patches {
			patch.Reset()
		}
	}()

	// 生成多部分表单（文件大小超过 5MB）
	body := &bytes.Buffer{}
	writer := multipart.NewWriter(body)
	fileContent := make([]byte, 6*1024*1024) // 6MB
	part, err := writer.CreateFormFile("file", "largefile.txt")
	assert.Nil(t, err)
	_, err = part.Write(fileContent)
	assert.Nil(t, err)

	// 添加其他参数
	params := threat_history.SomeThreatHistory{
		PocIds: []string{"poc1"},
		Status: 16,
	}
	paramMap, err := utils.StructToMap(params, "json")
	assert.Nil(t, err)
	for key, value := range paramMap {
		_ = writer.WriteField(key, fmt.Sprintf("%v", value))
	}

	writer.Close()

	// 创建 HTTP 请求
	req := httptest.NewRequest(http.MethodPost, "/api/v1/threat_center/distribute", body)
	req.Header.Set("Content-Type", writer.FormDataContentType())
	req.Header.Set("Content-Length", fmt.Sprint(body.Len()))

	// 创建 Gin 上下文
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 调用被测试函数
	err = DistributeBatch(c)
	assert.NotNil(t, err)
	assert.Contains(t, err.Error(), "附件大小不能超过5M")
}

// 测试人员不存在的情况
func TestDistributeBatch_StaffNotFound(t *testing.T) {
	patches := mockDependencies()
	patches[1].Reset() // 重置 GetStaff 的模拟
	patches[1] = gomonkey.ApplyFuncReturn(threat_history.GetStaff, nil, errors.New("人员不存在"))
	defer func() {
		for _, patch := range patches {
			patch.Reset()
		}
	}()

	// 生成多部分表单
	params := threat_history.SomeThreatHistory{
		PocIds:    []string{"poc1"},
		Status:    16,
		ToStaffId: "staff1",
	}
	writer, body, err := genMultipartForm(params)
	assert.Nil(t, err)

	// 创建 HTTP 请求
	req := httptest.NewRequest(http.MethodPost, "/api/v1/threat_center/distribute", body)
	req.Header.Set("Content-Type", writer.FormDataContentType())
	req.Header.Set("Content-Length", fmt.Sprint(body.Len()))

	// 创建 Gin 上下文
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 调用被测试函数
	err = DistributeBatch(c)
	assert.NotNil(t, err)
	assert.Contains(t, err.Error(), "人员不存在")
}

func TestStatusOperationBatch(t *testing.T) {
	patches := mockDependencies()
	defer func() {
		for _, patch := range patches {
			patch.Reset()
		}
	}()
	params := threat_history.SomeThreatHistory{
		PocIds:        []string{"poc1"},
		Status:        16,
		ToStaffId:     "staff1",
		TimeoutNotice: 1,
		ToStaffName:   "Test Staff",
		LimitDate:     "2023-12-31 10:23:00",
		SendNotice:    1,
		Descrition:    "Test Description",
		ToCc:          "<EMAIL>",
		ExecNow:       1,
		OriginalId:    "original1",
		OperationType: "distribute",
	}

	// 生成多部分表单
	writer, body, err := genMultipartForm(params)
	assert.Nil(t, err)

	w := httptest.NewRecorder()

	req := httptest.NewRequest(http.MethodPost, "/api/v1/threat_center/status_operate", body)
	req.Header.Set("Content-Type", writer.FormDataContentType())
	req.Header.Set("Content-Length", fmt.Sprint(body.Len()))
	// 创建一个Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	err = StatusOperationBatch(c)
	for _, patch := range patches {
		patch.Reset()
	}
	assert.Nil(t, err)
}

func TestStatusOperationBatchError(t *testing.T) {
	patches := mockDependencies()
	defer func() {
		for _, patch := range patches {
			patch.Reset()
		}
	}()
	params := threat_history.SomeThreatHistory{
		PocIds:        []string{"poc1"},
		Status:        16,
		ToStaffId:     "staff1",
		TimeoutNotice: 1,
		ToStaffName:   "Test Staff",
		LimitDate:     "2023-12-31 10:23:00",
		SendNotice:    1,
		Descrition:    "Test Description",
		ToCc:          "<EMAIL>",
		ExecNow:       1,
		OriginalId:    "original1",
		OperationType: "distribute",
	}

	// 生成多部分表单
	writer, body, err := genMultipartForm(params)
	assert.Nil(t, err)

	w := httptest.NewRecorder()

	req := httptest.NewRequest(http.MethodPost, "/api/v1/threat_center/status_operate", body)
	req.Header.Set("Content-Type", writer.FormDataContentType())
	req.Header.Set("Content-Length", fmt.Sprint(body.Len()))
	// 创建一个Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	err = StatusOperationBatch(c)
	for _, patch := range patches {
		patch.Reset()
	}
	assert.Nil(t, err)
}
func TestStatusOperationBatchSuccess2(t *testing.T) {
	patches := mockDependencies()
	defer func() {
		for _, patch := range patches {
			patch.Reset()
		}
	}()
	params := threat_history.SomeThreatHistory{
		PocIds:        []string{"poc1"},
		Status:        16,
		ToStaffId:     "staff1",
		TimeoutNotice: 1,
		ToStaffName:   "Test Staff",
		LimitDate:     "2023-12-31 10:23:00",
		SendNotice:    1,
		Descrition:    "Test Description",
		ToCc:          "<EMAIL>",
		ExecNow:       1,
		OriginalId:    "original1",
		OperationType: "distribute",
	}

	// 生成多部分表单
	writer, body, err := genMultipartForm(params)
	assert.Nil(t, err)
	w := httptest.NewRecorder()

	mockServer := testcommon.NewMockServer()
	mockServer.Register("poc/_search", []*elastic.SearchHit{
		{
			Id:     "1",
			Source: []byte(`{"ip":"***********", "updated_at":"2023-04-25 12:34:56", "created_at":"2023-04-25 12:34:56"}`),
		},
	})

	req := httptest.NewRequest(http.MethodPost, "/api/v1/threat_center/status_operate", body)
	req.Header.Set("Content-Type", writer.FormDataContentType())
	req.Header.Set("Content-Length", fmt.Sprint(body.Len()))
	// 创建一个Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	err = StatusOperationBatch(c)
	for _, patch := range patches {
		patch.Reset()
	}
	mockServer.Close()
	assert.Nil(t, err)
}

func TestReTest(t *testing.T) {
	mock := testcommon.InitSqlMock()
	defer mock.Close()

	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	mockServer.Register("poc/_search", []*elastic.SearchHit{
		{
			Id:     "12f7876552424a80b8b18d17a900d239",
			Source: []byte(`{"status":1}`),
		},
	})

	t.Run("status is not 17", func(t *testing.T) {
		// 创建一个测试请求和响应
		w := httptest.NewRecorder()
		val, _ := json.Marshal(threat_history.OneThreatHistory{
			PocIds: []string{"547b17bac35f468db26aa56a4e883d57"},
			Status: 16,
		})

		req := httptest.NewRequest(http.MethodPost, "/api/v1/threat_center/retest", strings.NewReader(string(val)))
		req.Header.Set("Content-Type", "application/json")
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req
		c.Set("user_id", uint(1))
		c.Set("is_super_manage", true)
		// 调用函数
		err := ReTest(c)
		assert.NoError(t, err)
		var responseJSON map[string]interface{}
		json.Unmarshal(w.Body.Bytes(), &responseJSON)

		assert.Equal(t, "批量处理1条,失败1条", responseJSON["message"])
	})
}

func TestReTestManual(t *testing.T) {
	patches := mockDependencies()
	defer func() {
		for _, patch := range patches {
			patch.Reset()
		}
	}()
	params := threat_history.ManualHistory{
		PocIds:     []string{"poc1"},
		ToStaffId:  "staff1",
		Descrition: "Test Description",
	}

	// 生成多部分表单
	writer, body, err := genMultipartForm(params)
	assert.Nil(t, err)

	user := user.User{}
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyFuncReturn(request.GetUserInfo, &user).Reset()
	mock := testcommon.InitSqlMock()
	defer mock.Close()

	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	s := miniredis.RunT(t)
	defer s.Close()

	client := redis2.NewClient(&redis2.Options{Addr: s.Addr()})
	testcommon.SetRedisClient(client)

	t.Run("retest success", func(t *testing.T) {
		// 创建一个测试请求和响应
		w := httptest.NewRecorder()

		mockServer.Register("poc/_search", []*elastic.SearchHit{
			{
				Id:     "12f7876552424a80b8b18d17a900d239",
				Source: []byte(`{"status":17,"statusCode":17}`),
			},
		})
		mock.ExpectQuery("SELECT * FROM `users_roles` WHERE `user_id` = ?").
			WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))
		mock.ExpectQuery("SELECT count(*) FROM `roles` WHERE `id` IN (?) AND `key` = ?").
			WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(1))

		mock.ExpectQuery("SELECT * FROM `data_sources` WHERE id in (?)").
			WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))

		mock.ExpectQuery("SELECT name FROM `data_nodes` WHERE id IN (?)").
			WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))

		mock.ExpectQuery("SELECT `name` FROM `network_areas` WHERE id = ?").
			WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))

		mock.ExpectBegin()
		mock.ExpectExec(
			"INSERT INTO `threat_histories` (`created_at`,`updated_at`,`from_user_id`,`to_staff_id`,`limit_date`,`from_username`,`to_staff_name`,`upload_file`,`operation`,`descrition`,`to_cc`,`poc_id`,`status`,`new_status`,`timeout_notice`,`send_notice`,`category`,`source_ids`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)",
		).WillReturnResult(sqlmock.NewResult(1, 1))
		mock.ExpectCommit()

		req := httptest.NewRequest(http.MethodPost, "/api/v1/threat_center/retest_manual", body)
		req.Header.Set("Content-Type", writer.FormDataContentType())
		req.Header.Set("Content-Length", fmt.Sprint(body.Len()))
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req
		c.Set("user_id", uint64(1))
		c.Set("is_super_admin", true)
		// 调用函数
		err := ReTestManual(c)

		// 断言错误为 nil
		assert.Nil(t, err)
	})
}

func TestGetNodeInfo(t *testing.T) {
	mock := testcommon.InitSqlMock()
	defer mock.Close()

	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	s := miniredis.RunT(t)
	defer s.Close()

	client := redis2.NewClient(&redis2.Options{Addr: s.Addr()})
	testcommon.SetRedisClient(client)

	mockServer.Register("poc/_search", []*elastic.SearchHit{
		{
			Id:     "547b17bac35f468db26aa56a4e883d57",
			Source: []byte(`{"all_node_ids":[1,2]}`),
		},
	})

	// t.Run("get node info success", func(t *testing.T) {
	// 	// 创建一个测试请求和响应
	// 	w := httptest.NewRecorder()

	// 	nodes := []data_source.Node{}
	// 	node := data_source.Node{
	// 		Name: "foeye1",
	// 	}
	// 	node.Id = 1
	// 	nodes = append(nodes, node)
	// 	time.Sleep(time.Second)
	// 	defer gomonkey.ApplyMethodReturn(data_source.NewNodeModel(), "Item", nodes, nil).Reset()

	// 	req := httptest.NewRequest(http.MethodGet, "/api/v1/threat_center/get_node?poc_id=547b17bac35f468db26aa56a4e883d57", nil)
	// 	req.Header.Set("Content-Type", "application/json")
	// 	// 创建一个Gin上下文
	// 	c, _ := gin.CreateTestContext(w)
	// 	c.Request = req

	// 	// 调用函数
	// 	err := GetNodeInfo(c)

	// 	// 断言错误为 nil
	// 	assert.Empty(t, err)
	// 	var responseJSON map[string]interface{}
	// 	json.Unmarshal(w.Body.Bytes(), &responseJSON)

	// 	assert.Equal(t, "Success", responseJSON["message"])
	// 	assert.Equal(t, []interface{}([]interface{}{map[string]interface{}{"id": float64(1), "name": "foeye1"}}), responseJSON["data"])
	// })
}

func TestGetHistory(t *testing.T) {
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()
	t.Run("success", func(t *testing.T) {
		mockDb.ExpectQuery("SELECT * FROM `threat_histories` WHERE `poc_id` = ? AND `category` = ?").WithArgs("547b17bac35f468db26aa56a4e883d57", "operation").
			WillReturnRows(sqlmock.NewRows([]string{"id", "source_ids"}).AddRow(2, "1,2"))
		mockDb.ExpectQuery("SELECT * FROM `data_sources`").
			WillReturnRows(sqlmock.NewRows([]string{"id", "name", "icon"}).AddRow(2, "aaa", "icon"))

		// 创建一个测试请求和响应
		w := httptest.NewRecorder()

		req := httptest.NewRequest(http.MethodGet, "/api/v1/threat_center/history?id=547b17bac35f468db26aa56a4e883d57&type=operation", nil)
		req.Header.Set("Content-Type", "application/json")
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		// 调用函数
		err := GetHistory(c)

		// 断言错误为 nil
		assert.Empty(t, err)
		var responseJSON map[string]interface{}
		json.Unmarshal(w.Body.Bytes(), &responseJSON)

		assert.Equal(t, "Success", responseJSON["message"])
	})
}

func Test_vulReTestDistribute(t *testing.T) {
	s, err := miniredis.Run()
	if err != nil {
		t.Fatal(err)
	}
	defer s.Close()

	client := redis2.NewClient(&redis2.Options{Addr: s.Addr()})
	testcommon.SetRedisClient(client)
	patches := gomonkey.ApplyFuncReturn(threat_history.GetStaff, nil, nil)
	vulReTestDistribute("132", "vwrewv343", nil)

	patches.Reset()
}
