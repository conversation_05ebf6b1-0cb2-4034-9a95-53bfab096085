package internal_asset

import (
	"context"
	"fmt"
	"fobrain/fobrain/app/services/net_mapping"
	"fobrain/models/elastic/assets"
	"slices"
	"strings"

	"fobrain/fobrain/app/repository/asset"
	"fobrain/fobrain/app/request/asset_center"
	assetcenter "fobrain/fobrain/app/services/asset_center"
	"fobrain/fobrain/app/services/export"
	"fobrain/fobrain/app/services/export/handlers"
	"fobrain/fobrain/common/request"
	"fobrain/fobrain/common/response"
	"fobrain/fobrain/common/validate"
	pb "fobrain/mergeService/proto"
	"fobrain/pkg/utils"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

var InternalAssetValidate = request.Validate[asset_center.InternalAssetRequest]
var InternalAssetIdValidate = request.Validate[asset_center.InternalAssetIdRequest]
var InternalAssetIdsValidate = request.Validate[asset_center.InternalAssetIdsRequest]

// List
// 内网资产清单
func List(c *gin.Context) error {
	params, err := InternalAssetValidate(c, &asset_center.InternalAssetRequest{})
	if err != nil {
		return err
	}

	staffIds, _ := c.Get("staff_ids")
	isSuperManage, _ := c.Get("is_super_manage")

	list, total, err := asset.List(c, params.Keyword, params.Page, params.PerPage, asset.NetworkTypeInternal, params, isSuperManage.(bool), staffIds.([]string), nil, 1)
	if err != nil {
		return err
	}

	return response.OkWithPageData(c, total, params.Page, params.PerPage, list)
}

func DeleteByIds(c *gin.Context) error {
	params, _ := InternalAssetIdsValidate(c, &asset_center.InternalAssetIdsRequest{})
	isType := 0
	if len(params.Ids) == 0 {
		isType = 1
	}

	// 删除资产
	err := asset.DeleteByIds(c, params.Ids, params.Keyword, asset.NotIsRecycleBin, isType, params.SearchCondition)
	if err != nil {
		return err
	}

	err = net_mapping.CheckAlarm()
	if err != nil {
		return err
	}

	return response.OkWithMessage(c, "删除成功")
}

func Show(c *gin.Context) error {
	params, _ := InternalAssetIdValidate(c, &asset_center.InternalAssetIdRequest{})

	data, err := asset.Show(c, params.Id)
	if err != nil {
		return err
	}

	return response.OkWithData(c, data)
}

func RecycleBin(c *gin.Context) error {
	params, err := InternalAssetValidate(c, &asset_center.InternalAssetRequest{})
	if err != nil {
		return err
	}
	staffIds, _ := c.Get("staff_ids")
	isSuperManage, _ := c.Get("is_super_manage")
	list, total, err := asset.RecycleBin(c, params.Keyword, params.Page, params.PerPage, asset.NetworkTypeInternal, isSuperManage.(bool), staffIds.([]string))
	if err != nil {
		return err
	}

	return response.OkWithPageData(c, total, params.Page, params.PerPage, list)
}

// Reduction
// @description 将进入回收站的资产还原, 去除软删除的标识
func Reduction(c *gin.Context) error {
	params, _ := InternalAssetIdsValidate(c, &asset_center.InternalAssetIdsRequest{})

	err := asset.Reduction(c, params.Ids, params.Keyword, asset.NetworkTypeInternal)
	if err != nil {
		return err
	}

	return response.OkWithMessage(c, "还原成功")
}

// PurgeByIds
// @description 将资产彻底删除
func PurgeByIds(c *gin.Context) error {
	params, _ := InternalAssetIdsValidate(c, &asset_center.InternalAssetIdsRequest{})
	err := assetcenter.PurgeAsset(c, params.Ids, asset.NetworkTypeInternal, params.Keyword, params.SearchCondition)
	if err != nil {
		return err
	}

	return response.OkWithMessage(c, "删除成功")
}

func Export(c *gin.Context) error {
	internalAssetRequest := &asset_center.InternalAssetRequest{
		PageRequest: request.PageRequest{
			Page:    1,
			PerPage: 10,
		},
	}
	params, err := InternalAssetValidate(c, internalAssetRequest)
	if err != nil {
		return err
	}

	// 使用新的导出框架
	handler := handlers.NewAssetHandler(asset.NetworkTypeInternal, asset.NotIsRecycleBin)
	exportService := export.NewService().WithConcurrency(10)

	path, err := exportService.Export(c, handler, params)
	if err != nil {
		return err
	}

	return response.OkWithFile(c, path, true)
}

func RecycleBinExport(c *gin.Context) error {
	internalAssetRequest := &asset_center.InternalAssetRequest{
		PageRequest: request.PageRequest{
			Page:    1,
			PerPage: 10,
		},
	}
	params, err := InternalAssetValidate(c, internalAssetRequest)
	if err != nil {
		return err
	}

	// 使用新的导出框架
	handler := handlers.NewAssetHandler(asset.NetworkTypeInternal, asset.IsRecycleBin)
	exportService := export.NewService().WithConcurrency(10)

	path, err := exportService.Export(c, handler, params)
	if err != nil {
		return err
	}

	return response.OkWithFile(c, path, true)
}

// ManualCalibration 手动校准
func ManualCalibration(c *gin.Context) error {
	param := &pb.ManualCalibrationRequest{}
	err := c.ShouldBind(param)
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	if len(param.Ids) == 0 {
		return response.FailWithMessage(c, "不支持全量数据校准，可选择批量校准")
	}
	param.BusinessType = "asset"
	param.BatchNo = strings.ReplaceAll(uuid.New().String(), "-", "")
	if ok, msg := validate.Validator(param); !ok {
		return response.FailWithMessage(c, msg)
	}

	data, err := pb.GetProtoClient().ManualCalibration(context.Background(), param, pb.ClientWithAddress)
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}

	return response.OkWithData(c, data)
}

func IpBaseInfo(c *gin.Context) error {
	id, ok := c.GetQuery("id")
	if !ok || id == "" {
		return fmt.Errorf("id不能为空")
	}

	data, err := assetcenter.GetIpBaseInfo(id)
	if err != nil {
		return err
	}

	dataMap, _ := utils.StructToMap(data, "json")
	asset.HandleNetMapping([]any{dataMap})
	if val, ok := dataMap["net_mappings"]; ok {
		data.NetMappings = val
	}
	data.Domain = utils.IfNilToEmpty(data.Domain)
	data.Sn = utils.IfNilToEmpty(data.Sn)
	data.Mac = utils.IfNilToEmpty(data.Mac)
	data.Hostname = utils.IfNilToEmpty(data.Hostname)
	data.EthName = utils.IfNilToEmpty(data.EthName)
	data.Product = utils.IfNilToEmpty(data.Product)
	data.PublicIp = utils.IfNilToEmpty(data.PublicIp)
	data.Os = utils.IfNilToEmpty(data.Os)
	data.Device = utils.IfNilToEmpty(data.Device)

	return response.OkWithData(c, data)
}

func IpAdminInfo(c *gin.Context) error {
	param := &pb.IpInfoRequest{}
	err := c.ShouldBind(param)
	if err != nil {
		return err
	}
	if ok, msg := validate.Validator(param); !ok {
		return response.FailWithMessage(c, msg)
	}

	data, err := pb.GetProtoClient().IpAdminInfo(context.Background(), param, pb.ClientWithAddress)
	if err != nil {
		return err
	}
	businessDepartment := make([]*assets.DepartmentBase, 0)
	existBusinessDepartment := make(map[string]bool)
	operDepartment := make([]*assets.DepartmentBase, 0)
	existOperDepartment := make(map[string]bool)
	data.Admin = utils.IfNilToEmpty(data.Admin)
	for _, item := range data.Admin {
		item.PersonBase = utils.IfNilToEmpty(item.PersonBase)
		for _, base := range item.PersonBase {
			base.FindInfo = utils.IfNilToEmpty(base.FindInfo)
			base.Department = utils.IfNilToEmpty(base.Department)
			if base.Department != nil {
				for _, department := range base.Department {
					if !existBusinessDepartment[department.Name] {
						businessDepartment = append(businessDepartment, &assets.DepartmentBase{
							Name: department.Name,
							Id:   department.Id,
						})
						existBusinessDepartment[department.Name] = true
					}
				}
			}
		}
	}
	data.Oper = utils.IfNilToEmpty(data.Oper)
	// 保存出现过的id
	seen := make(map[string]bool)
	for _, item := range data.Oper {
		// 如果id已经出现过，跳过
		if seen[item.Id] {
			continue
		}
		// 保存出现过的id
		seen[item.Id] = true
		item.FindInfo = utils.IfNilToEmpty(item.FindInfo)
		item.Department = utils.IfNilToEmpty(item.Department)
		if item.Department != nil {
			for _, department := range item.Department {
				if !existOperDepartment[department.Name] {
					operDepartment = append(operDepartment, &assets.DepartmentBase{
						Name: department.Name,
						Id:   department.Id,
					})
					existOperDepartment[department.Name] = true
				}
			}
		}
	}
	// 删除重复的元素
	data.Oper = slices.DeleteFunc(data.Oper, func(item *pb.PersonBase) bool {
		if seen[item.Id] {
			// 保留出现的第一个元素
			seen[item.Id] = false
			return false
		}
		// 删除重复的元素
		return true
	})
	data.MachineRoom = utils.IfNilToEmpty(data.MachineRoom)
	data.Tags = utils.IfNilToEmpty(data.Tags)
	res, _ := utils.StructToMap(data, "json")
	res["business_department"] = businessDepartment
	res["oper_department"] = operDepartment
	res["oper_department"] = operDepartment
	return response.OkWithData(c, res)
}

func IpPortInfo(c *gin.Context) error {
	param := &pb.IpInfoRequest{}
	err := c.ShouldBind(param)
	if err != nil {
		return err
	}
	if ok, msg := validate.Validator(param); !ok {
		return response.FailWithMessage(c, msg)
	}

	data, err := pb.GetProtoClient().IpPortInfo(context.Background(), param, pb.ClientWithAddress)
	if err != nil {
		return err
	}
	data.Port = utils.IfNilToEmpty(data.Port)

	return response.OkWithData(c, data)
}

func IpHostInfo(c *gin.Context) error {
	param := &pb.IpInfoRequest{}
	err := c.ShouldBind(param)
	if err != nil {
		return err
	}
	if ok, msg := validate.Validator(param); !ok {
		return response.FailWithMessage(c, msg)
	}

	data, err := pb.GetProtoClient().IpHostInfo(context.Background(), param, pb.ClientWithAddress)
	if err != nil {
		return err
	}

	data.Maker = utils.IfNilToEmpty(data.Maker)
	data.Model = utils.IfNilToEmpty(data.Model)
	data.Sn = utils.IfNilToEmpty(data.Sn)
	data.Os = utils.IfNilToEmpty(data.Os)
	data.OsKernel = utils.IfNilToEmpty(data.OsKernel)
	data.MemorySize = utils.IfNilToEmpty(data.MemorySize)
	data.MemoryUsageRate = utils.IfNilToEmpty(data.MemoryUsageRate)
	data.CpuMaker = utils.IfNilToEmpty(data.CpuMaker)
	data.CpuBrand = utils.IfNilToEmpty(data.CpuBrand)
	data.CpuCount = utils.IfNilToEmpty(data.CpuCount)
	data.LoadAverage = utils.IfNilToEmpty(data.LoadAverage)
	data.DiskCount = utils.IfNilToEmpty(data.DiskCount)
	data.DiskSize = utils.IfNilToEmpty(data.DiskSize)
	data.DiskUsageRate = utils.IfNilToEmpty(data.DiskUsageRate)

	return response.OkWithData(c, data)
}

func IpSecurityInfo(c *gin.Context) error {
	param := &pb.IpInfoRequest{}
	err := c.ShouldBind(param)
	if err != nil {
		return err
	}
	if ok, msg := validate.Validator(param); !ok {
		return response.FailWithMessage(c, msg)
	}

	data, err := pb.GetProtoClient().IpSecurityInfo(context.Background(), param, pb.ClientWithAddress)
	if err != nil {
		return err
	}

	return response.OkWithData(c, data)
}

func IpTraceabilityBaseInfo(c *gin.Context) error {
	param := &pb.IpInfoRequest{}
	err := c.ShouldBind(param)
	if err != nil {
		return err
	}
	if ok, msg := validate.Validator(param); !ok {
		return response.FailWithMessage(c, msg)
	}

	data, err := pb.GetProtoClient().IpTraceabilityBaseInfo(context.Background(), param, pb.ClientWithAddress)
	if err != nil {
		return err
	}

	return response.OkWithData(c, data)
}

func IpTraceabilityProcessInfo(c *gin.Context) error {
	param := &pb.TraceabilityProcessInfoRequest{}
	err := c.ShouldBind(param)
	if err != nil {
		return err
	}
	if ok, msg := validate.Validator(param); !ok {
		return response.FailWithMessage(c, msg)
	}

	data, err := pb.GetProtoClient().IpTraceabilityProcessInfo(context.Background(), param, pb.ClientWithAddress)
	if err != nil {
		return err
	}

	return response.OkWithData(c, data)
}

func IpTraceabilityDetailInfo(c *gin.Context) error {
	param := &pb.TraceabilityDetailInfoRequest{}
	err := c.ShouldBind(param)
	if err != nil {
		return err
	}
	if ok, msg := validate.Validator(param); !ok {
		return response.FailWithMessage(c, msg)
	}

	data, err := pb.GetProtoClient().IpTraceabilityDetailInfo(context.Background(), param, pb.ClientWithAddress)
	if err != nil {
		return err
	}

	// 格式处理
	for _, item := range data.Items {
		item.Strategies = utils.IfNilToEmpty(item.Strategies)
		for _, strategy := range item.Strategies {
			strategy.UntrustedSource = utils.IfNilToEmpty(strategy.UntrustedSource)
		}
		item.MergedData.Business = utils.IfNilToEmpty(item.MergedData.Business)
		for _, business := range item.MergedData.Business {
			business.PersonBase = utils.IfNilToEmpty(business.PersonBase)
			for _, base := range business.PersonBase {
				base.FindInfo = utils.IfNilToEmpty(base.FindInfo)
			}
		}
		item.MergedData.Oper = utils.IfNilToEmpty(item.MergedData.Oper)
		for _, oper := range item.MergedData.Oper {
			oper.FindInfo = utils.IfNilToEmpty(oper.FindInfo)
		}
	}

	return response.OkWithData(c, data)
}
