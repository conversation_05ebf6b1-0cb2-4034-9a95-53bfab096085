package internal_asset

import (
	json2 "encoding/json"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/gin-gonic/gin"
	"github.com/olivere/elastic/v7"
	"github.com/stretchr/testify/assert"

	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/models/mysql/custom_column"
	"fobrain/models/mysql/network_areas"
)

func TestList(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	t.Run("es query err", func(t *testing.T) {
		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/v1/internal_asset?page=1&per_page=10", nil)
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Set("staff_ids", []string{})
		c.Set("is_super_manage", true)
		c.Request = req

		err := List(c)
		assert.NotNil(t, err)
	})

	mockServer.Register("asset/_search", []*elastic.SearchHit{
		{
			Id:     "1",
			Source: []byte(`{"ip":"***********", "updated_at":"2023-04-25 12:34:56", "created_at":"2023-04-25 12:34:56"}`),
		},
	})

	t.Run("ExternalIpAssetValidate err", func(t *testing.T) {
		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/v1/internal_asset?page=1", nil)
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req
		c.Set("staff_ids", []string{})
		c.Set("is_super_manage", true)
		err := List(c)
		assert.Equal(t, "条数为必填字段", err.Error())
	})

	// 创建一个测试请求和响应
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/internal_asset?page=1&per_page=10", nil)
	// 创建一个Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req
	c.Set("staff_ids", []string{})
	c.Set("is_super_manage", true)
	// 调用函数
	err := List(c)

	// 断言错误为 nil
	assert.Empty(t, err)

	var result struct {
		Code    int    `json:"code"`
		Message string `json:"message"`
		Data    struct {
			Total    int64                    `json:"total"`
			Page     int                      `json:"page"`
			PerPage  int                      `json:"per_page"`
			Items    []map[string]interface{} `json:"items"`
			PageInfo struct {
				Total   int64 `json:"total"`
				Page    int   `json:"page"`
				PerPage int   `json:"per_page"`
			}
		}
	}

	json2.Unmarshal(w.Body.Bytes(), &result)

	assert.Equal(t, int64(1), result.Data.Total)
	assert.Equal(t, "***********", result.Data.Items[0]["ip"])
	assert.Equal(t, "2023-04-25 12:34:56", result.Data.Items[0]["updated_at"].(string))
	assert.Equal(t, "2023-04-25 12:34:56", result.Data.Items[0]["created_at"].(string))
}

func TestDeleteByIds(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()
	mockDb := testcommon.InitSqlMock()
	defer mockDb.Close()

	// 模拟滚动查询的响应批次
	mockServer.Register("/asset/_search", []*elastic.SearchHit{
		{
			Id:     "1",
			Source: []byte(`{"id":"1","name":"example","area":1,"domains":["example.com"],"created_at":"2022-01-01 00:00:00","updated_at":"2022-01-02 00:00:00"}`),
		},
	})
	mockServer.RegisterEmptyScrollHandler()

	t.Run("es delete err", func(t *testing.T) {
		mockDb.ExpectQuery("SELECT count(*) FROM `merge_records` WHERE `task_status` = ?").
			WithArgs("执行中").WillReturnRows(mockDb.NewRows([]string{"count"}).AddRow(0))
		w := httptest.NewRecorder()
		req := httptest.NewRequest("DELETE", "/api/v1/internal_asset?ids=1&ids=2", strings.NewReader(`{"ids":["1"]}`))
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := DeleteByIds(c)
		assert.NotNil(t, err)
	})
}

func TestShow(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	t.Run("es query err", func(t *testing.T) {
		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/v1/internal_asset/1", nil)
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := Show(c)
		assert.NotNil(t, err)
	})

	mockServer.Register("asset/_search", []*elastic.SearchHit{
		{
			Id:     "1",
			Source: []byte(`{"ip":"***********", "updated_at":"2023-04-25 12:34:56", "created_at":"2023-04-25 12:34:56"}`),
		},
	})

	// 创建一个测试请求和响应
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/internal_asset/1", nil)
	// 创建一个Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 调用函数
	err := Show(c)

	// 断言错误为 nil
	assert.Empty(t, err)
}

func TestRecycleBin(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	t.Run("es query err", func(t *testing.T) {
		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/v1/internal_asset/recycle_bin?page=1&per_page=10", nil)
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req
		c.Set("staff_ids", []string{})
		c.Set("is_super_manage", true)
		err := RecycleBin(c)
		assert.NotNil(t, err)
	})

	mockServer.Register("asset/_search", []*elastic.SearchHit{
		{
			Id:     "1",
			Source: []byte(`{"ip":"***********", "updated_at":"2023-04-25 12:34:56", "created_at":"2023-04-25 12:34:56"}`),
		},
	})

	t.Run("ExternalIpAssetValidate err", func(t *testing.T) {
		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/v1/internal_asset/recycle_bin?page=1", nil)
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := RecycleBin(c)
		assert.Equal(t, "条数为必填字段", err.Error())
	})

	// 创建一个测试请求和响应
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/internal_asset/recycle_bin?page=1&per_page=10", nil)
	// 创建一个Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req
	c.Set("staff_ids", []string{})
	c.Set("is_super_manage", true)
	// 调用函数
	err := RecycleBin(c)

	// 断言错误为 nil
	assert.Empty(t, err)

	var result struct {
		Code    int    `json:"code"`
		Message string `json:"message"`
		Data    struct {
			Total    int64                    `json:"total"`
			Page     int                      `json:"page"`
			PerPage  int                      `json:"per_page"`
			Items    []map[string]interface{} `json:"items"`
			PageInfo struct {
				Total   int64 `json:"total"`
				Page    int   `json:"page"`
				PerPage int   `json:"per_page"`
			}
		}
	}

	json2.Unmarshal(w.Body.Bytes(), &result)

	assert.Equal(t, int64(1), result.Data.Total)
	assert.Equal(t, "***********", result.Data.Items[0]["ip"])
	assert.Equal(t, "2023-04-25 12:34:56", result.Data.Items[0]["updated_at"].(string))
	assert.Equal(t, "2023-04-25 12:34:56", result.Data.Items[0]["created_at"].(string))
}

func TestExport(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	// 用于跟踪搜索调用次数
	var searchCallCount int

	// mock ES 数据计数 - 确保有数据
	mockServer.Register("/asset/_count", map[string]interface{}{
		"count": 1,
		"_shards": map[string]interface{}{
			"total":      1,
			"successful": 1,
			"skipped":    0,
			"failed":     0,
		},
	})

	// 注册自定义搜索处理器，正确处理 search_after 分页查询
	mockServer.RegisterHandler("/asset/_search", func(w http.ResponseWriter, r *http.Request) {
		searchCallCount++
		w.Header().Set("Content-Type", "application/json")

		if searchCallCount == 1 {
			// 第一次查询，返回有数据的响应
			response := elastic.SearchResult{
				Hits: &elastic.SearchHits{
					TotalHits: &elastic.TotalHits{
						Value:    1,
						Relation: "eq",
					},
					Hits: []*elastic.SearchHit{
						{
							Index: "asset",
							Id:    "1",
							Source: []byte(`{
								"id": "1",
								"ip": "*************",
								"port": 80,
								"protocol": "http",
								"components": ["nginx"],
								"status": 1,
								"hostname": ["internal.example.com"],
								"ip_type": 1,
								"mac": ["00:11:22:33:44:55"],
								"domains": ["internal.example.com"],
								"area": 1,
								"business": [],
								"opers": [],
								"all_source_ids": [1],
								"created_at": "2022-01-01 00:00:00",
								"updated_at": "2022-01-02 00:00:00"
							}`),
							Sort: []interface{}{"2022-01-02 00:00:00", "1"},
						},
					},
				},
			}
			json2.NewEncoder(w).Encode(response)
		} else {
			// 后续查询，返回空结果以结束分页
			response := elastic.SearchResult{
				Hits: &elastic.SearchHits{
					TotalHits: &elastic.TotalHits{
						Value:    0,
						Relation: "eq",
					},
					Hits: []*elastic.SearchHit{},
				},
			}
			json2.NewEncoder(w).Encode(response)
		}
	})

	// mock 数据库查询
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()

	// mock 数据源查询 - 为两个测试用例各准备一次
	mockDb.ExpectQuery("SELECT * FROM `data_sources`").
		WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).AddRow(1, "测试数据源"))
	mockDb.ExpectQuery("SELECT * FROM `data_sources`").
		WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).AddRow(1, "测试数据源"))

	patch := gomonkey.NewPatches()
	patch.ApplyFuncReturn(network_areas.AllNetworkArea, map[uint64]string{
		1: "example_area",
	})
	patch.ApplyMethodReturn(&custom_column.CustomFieldMeta{}, "GetByModuleType", []*custom_column.CustomFieldMeta{
		{
			FieldKey:    "custom_key",
			DisplayName: "自定义字段",
		},
	}, nil)
	defer patch.Reset()

	t.Run("success all export", func(t *testing.T) {
		// 重置搜索调用计数
		searchCallCount = 0

		// 创建一个测试请求和响应
		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/v1/internal_asset/export", nil)
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req
		c.Set("staff_ids", []string{})
		c.Set("is_super_manage", true)
		// 调用函数
		err := Export(c)

		// 断言错误为 nil
		assert.Empty(t, err)
	})

	t.Run("success select ids export", func(t *testing.T) {
		// 重置搜索调用计数
		searchCallCount = 0

		// 创建一个测试请求和响应
		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/v1/internal_asset/export?ids=1&ids=2", nil)
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req
		c.Set("staff_ids", []string{})
		c.Set("is_super_manage", true)
		// 调用函数
		err := Export(c)

		// 断言错误为 nil
		assert.Empty(t, err)
	})
}

func TestRecycleBinExport(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	// 用于跟踪搜索调用次数
	var searchCallCount int

	// mock ES 数据计数 - 确保有数据
	mockServer.Register("/asset/_count", map[string]interface{}{
		"count": 1,
		"_shards": map[string]interface{}{
			"total":      1,
			"successful": 1,
			"skipped":    0,
			"failed":     0,
		},
	})

	// 注册自定义搜索处理器，正确处理 search_after 分页查询
	mockServer.RegisterHandler("/asset/_search", func(w http.ResponseWriter, r *http.Request) {
		searchCallCount++
		w.Header().Set("Content-Type", "application/json")

		if searchCallCount == 1 {
			// 第一次查询，返回有数据的响应
			response := elastic.SearchResult{
				Hits: &elastic.SearchHits{
					TotalHits: &elastic.TotalHits{
						Value:    1,
						Relation: "eq",
					},
					Hits: []*elastic.SearchHit{
						{
							Index: "asset",
							Id:    "1",
							Source: []byte(`{
								"id": "1",
								"ip": "*************",
								"port": 80,
								"protocol": "http",
								"components": ["nginx"],
								"status": 1,
								"hostname": ["internal.example.com"],
								"ip_type": 1,
								"mac": ["00:11:22:33:44:55"],
								"domains": ["internal.example.com"],
								"area": 1,
								"business": [],
								"opers": [],
								"all_source_ids": [1],
								"created_at": "2022-01-01 00:00:00",
								"updated_at": "2022-01-02 00:00:00"
							}`),
							Sort: []interface{}{"2022-01-02 00:00:00", "1"},
						},
					},
				},
			}
			json2.NewEncoder(w).Encode(response)
		} else {
			// 后续查询，返回空结果以结束分页
			response := elastic.SearchResult{
				Hits: &elastic.SearchHits{
					TotalHits: &elastic.TotalHits{
						Value:    0,
						Relation: "eq",
					},
					Hits: []*elastic.SearchHit{},
				},
			}
			json2.NewEncoder(w).Encode(response)
		}
	})

	// mock 数据库查询
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()

	// mock 数据源查询 - 为两个测试用例各准备一次
	mockDb.ExpectQuery("SELECT * FROM `data_sources`").
		WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).AddRow(1, "测试数据源"))
	mockDb.ExpectQuery("SELECT * FROM `data_sources`").
		WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).AddRow(1, "测试数据源"))

	patch := gomonkey.NewPatches()
	patch.ApplyFuncReturn(network_areas.AllNetworkArea, map[uint64]string{
		1: "example_area",
	})
	patch.ApplyMethodReturn(&custom_column.CustomFieldMeta{}, "GetByModuleType", []*custom_column.CustomFieldMeta{
		{
			FieldKey:    "custom_key",
			DisplayName: "自定义字段",
		},
	}, nil)
	defer patch.Reset()

	t.Run("success all export", func(t *testing.T) {
		// 重置搜索调用计数
		searchCallCount = 0

		// 创建一个测试请求和响应
		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/v1/internal_asset/recycle_bin_export", nil)
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req
		c.Set("staff_ids", []string{})
		c.Set("is_super_manage", true)
		// 调用函数
		err := RecycleBinExport(c)

		// 断言错误为 nil
		assert.Empty(t, err)
	})

	t.Run("success select ids export", func(t *testing.T) {
		// 重置搜索调用计数
		searchCallCount = 0

		// 创建一个测试请求和响应
		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/v1/internal_asset/recycle_bin_export?ids=1&ids=2", nil)
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req
		c.Set("staff_ids", []string{})
		c.Set("is_super_manage", true)
		// 调用函数
		err := RecycleBinExport(c)

		// 断言错误为 nil
		assert.Empty(t, err)
	})
}

func TestReduction(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()

	t.Run("err", func(t *testing.T) {
		mockDb.ExpectQuery("SELECT count(*) FROM `merge_records` WHERE `task_status` = ?").
			WithArgs("执行中").WillReturnRows(mockDb.NewRows([]string{"count"}).AddRow(0))
		w := httptest.NewRecorder()
		req := httptest.NewRequest("POST", "/api/v1/internal_asset/reduction", nil)
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req
		c.Set("user_id", uint64(1))
		c.Set("is_super_manage", true)
		// 调用函数
		err := Reduction(c)
		assert.NotNil(t, err)
	})
	mockDb.ExpectQuery("SELECT count(*) FROM `merge_records` WHERE `task_status` = ?").
		WithArgs("执行中").WillReturnRows(mockDb.NewRows([]string{"count"}).AddRow(0))
	mockServer.Register("/asset/_update_by_query", &elastic.BulkIndexByScrollResponse{
		Updated: 1,
	})

	w := httptest.NewRecorder()
	req := httptest.NewRequest("POST", "/api/v1/internal_asset/reduction", nil)
	// 创建一个Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req
	c.Set("user_id", uint64(1))
	c.Set("is_super_manage", true)
	// 调用函数
	err := Reduction(c)
	assert.Empty(t, err)
}

// func TestPurgeByIds(t *testing.T) {
// 	mockServer := testcommon.NewMockServer()
// 	defer mockServer.Close()

// 	t.Run("es delete err", func(t *testing.T) {
// 		w := httptest.NewRecorder()
// 		req := httptest.NewRequest("DELETE", "/api/v1/external_ip_asset?ids=1&ids=2", strings.NewReader(`{"ids":["1"]}`))
// 		// 创建一个Gin上下文
// 		c, _ := gin.CreateTestContext(w)
// 		c.Request = req

// 		err := PurgeByIds(c)
// 		assert.NotNil(t, err)
// 	})

// 	mockServer.Register("/asset/_update_by_query", &elastic.BulkIndexByScrollResponse{
// 		Updated: 1,
// 	})

// 	// 创建一个测试请求和响应
// 	w := httptest.NewRecorder()
// 	req := httptest.NewRequest("DELETE", "/api/v1/external_ip_asset?ids=1&ids=2", strings.NewReader(`{"ids":["1"]}`))
// 	// 创建一个Gin上下文
// 	c, _ := gin.CreateTestContext(w)
// 	c.Request = req

// 	// 调用函数
// 	err := PurgeByIds(c)

// 	// 断言错误为 nil
// 	assert.Empty(t, err)
// }
