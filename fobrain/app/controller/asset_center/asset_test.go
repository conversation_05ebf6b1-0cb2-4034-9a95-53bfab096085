package asset_center

import (
	json2 "encoding/json"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/gin-gonic/gin"
	"github.com/olivere/elastic/v7"
	"github.com/stretchr/testify/assert"

	ast "fobrain/fobrain/app/repository/asset"
	testcommon "fobrain/fobrain/tests/common_test"
)

func TestList(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	t.Run("es query err", func(t *testing.T) {
		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/v1/internal_asset?page=1&per_page=10", nil)
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Set("staff_ids", []string{})
		c.Set("is_super_manage", true)
		c.Set("user_id", uint64(1))
		c.Request = req

		err := List(c)
		assert.NotNil(t, err)
	})

	mockServer.Register("asset/_search", []*elastic.SearchHit{
		{
			Id:     "1",
			Source: []byte(`{"ip":"***********", "updated_at":"2023-04-25 12:34:56", "created_at":"2023-04-25 12:34:56"}`),
		},
	})

	t.Run("ExternalIpAssetValidate err", func(t *testing.T) {
		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/v1/internal_asset?page=1", nil)
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req
		c.Set("staff_ids", []string{})
		c.Set("is_super_manage", true)
		err := List(c)
		assert.Equal(t, "条数为必填字段", err.Error())
	})

	// 创建一个测试请求和响应
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/internal_asset?page=1&per_page=10", nil)
	// 创建一个Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req
	c.Set("staff_ids", []string{})
	c.Set("is_super_manage", true)
	c.Set("user_id", uint64(1))
	// 调用函数
	err := List(c)

	// 断言错误为 nil
	assert.Empty(t, err)

	var result struct {
		Code    int    `json:"code"`
		Message string `json:"message"`
		Data    struct {
			Total    int64                    `json:"total"`
			Page     int                      `json:"page"`
			PerPage  int                      `json:"per_page"`
			Items    []map[string]interface{} `json:"items"`
			PageInfo struct {
				Total   int64 `json:"total"`
				Page    int   `json:"page"`
				PerPage int   `json:"per_page"`
			}
		}
	}

	json2.Unmarshal(w.Body.Bytes(), &result)

	assert.Equal(t, int64(1), result.Data.Total)
	assert.Equal(t, "***********", result.Data.Items[0]["ip"])
	assert.Equal(t, "2023-04-25 12:34:56", result.Data.Items[0]["updated_at"].(string))
	assert.Equal(t, "2023-04-25 12:34:56", result.Data.Items[0]["created_at"].(string))
}

func TestIpLikeList(t *testing.T) {
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyFuncReturn(ast.AssetsKeyword, nil, nil).Reset()
	// 创建一个测试请求和响应
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/search_condition/fuzzy_search?keyword=127.0.0.1&type=1", nil)
	// 创建一个Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req
	// 调用函数
	err := IpLikeList(c)
	assert.NoError(t, err)
}

func TestIpRuleInfos(t *testing.T) {
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyFuncReturn(ast.CreateBoolQuery, elastic.NewBoolQuery(), nil).Reset()
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyFuncReturn(ast.AssetTop, nil, nil).Reset()
	// 创建一个测试请求和响应
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/asset/rule_infos?field=first_tag", nil)
	// 创建一个Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req
	// 调用函数
	err := IpRuleInfos(c)
	assert.NoError(t, err)
}
