package business_strategy

import (
	"bytes"
	"encoding/json"
	"fobrain/fobrain/app/request/business"
	"fobrain/initialize/mysql"
	businessStrategy "fobrain/models/mysql/business_strategies"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"io/ioutil"
	"net/http/httptest"
	"testing"
)

// TestList 测试 List 方法
func TestList(t *testing.T) {
	// 模拟业务层函数返回的策略数据
	data := []*businessStrategy.Strategies{
		{
			BaseModel:      mysql.BaseModel{Id: 1},
			Weight:         10,
			DataSourceName: "DataSource1",
		},
		{
			BaseModel:      mysql.BaseModel{Id: 2},
			Weight:         20,
			DataSourceName: "DataSource2",
		},
	}

	// 使用 gomonkey 模拟 NewStrategiesModel().Get() 方法
	patch := gomonkey.ApplyMethodReturn(businessStrategy.NewStrategiesModel(), "Get", data, nil)
	defer patch.Reset()

	// 创建一个测试请求和响应
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/strategies", nil)

	// 创建一个Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 调用 List 函数
	err := List(c)
	assert.NoError(t, err)

	// 解析响应数据
	response := w.Result()
	defer response.Body.Close()

	body, _ := ioutil.ReadAll(response.Body)
	assert.NotEmpty(t, body)
	assert.Contains(t, string(body), "DataSource1")
	assert.Contains(t, string(body), "DataSource2")
}

// TestUpdateBatch 测试 UpdateBatch 方法
func TestUpdateBatch(t *testing.T) {
	// 模拟请求参数
	params := &business.StrategiesReq{
		UpdateValue: []*business.StrategyReq{
			{Id: 1, Weight: 30},
			{Id: 2, Weight: 40},
		},
	}
	paramsJson, _ := json.Marshal(params)
	// 使用 gomonkey 模拟 business.NewStrategiesModel().UpdateBatch 方法，模拟其返回没有错误
	patch2 := gomonkey.ApplyMethodReturn(businessStrategy.NewStrategiesModel(), "UpdateBatch", nil)
	defer patch2.Reset()

	// 创建一个测试请求和响应
	w := httptest.NewRecorder()
	req := httptest.NewRequest("PUT", "/business_strategy", bytes.NewBuffer(paramsJson))

	// 创建一个 Gin 上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 调用 UpdateBatch 函数
	err := UpdateBatch(c)
	assert.NotEmpty(t, err)

	// 解析响应数据
	response := w.Result()
	defer response.Body.Close()

	body, _ := ioutil.ReadAll(response.Body)
	assert.Empty(t, body)
	assert.Contains(t, string(body), "")
}

// TestUpdateBatch_ValidationError 测试 UpdateBatch 方法验证失败
func TestUpdateBatch_ValidationError(t *testing.T) {
	// 模拟验证失败，返回一个验证错误

	// 创建一个测试请求和响应
	w := httptest.NewRecorder()
	req := httptest.NewRequest("POST", "/business_strategy", nil)

	// 创建一个 Gin 上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 调用 UpdateBatch 函数，验证是否返回错误
	err := UpdateBatch(c)
	assert.Error(t, err)

	// 解析响应数据
	response := w.Result()
	defer response.Body.Close()

	body, _ := ioutil.ReadAll(response.Body)
	assert.Contains(t, string(body), "")
}

// TestUpdateBatch_UpdateError 测试 UpdateBatch 方法调用 UpdateBatch 出错
func TestUpdateBatch_UpdateError(t *testing.T) {
	// 模拟请求参数
	params := &business.StrategiesReq{
		UpdateValue: []*business.StrategyReq{
			{Id: 1, Weight: 30},
			{Id: 2, Weight: 40},
		},
	}
	paramsJson, _ := json.Marshal(params)
	// 创建一个测试请求和响应
	w := httptest.NewRecorder()
	req := httptest.NewRequest("PUT", "/business_strategy", bytes.NewBuffer(paramsJson))

	// 创建一个 Gin 上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 调用 UpdateBatch 函数，验证是否返回错误
	err := UpdateBatch(c)
	assert.Error(t, err)

	// 解析响应数据
	response := w.Result()
	defer response.Body.Close()

	body, _ := ioutil.ReadAll(response.Body)
	assert.Empty(t, body)
	assert.Contains(t, string(body), "")
}
