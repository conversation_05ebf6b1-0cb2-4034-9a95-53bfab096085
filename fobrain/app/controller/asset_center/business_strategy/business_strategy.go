package business_strategy

import (
	business2 "fobrain/fobrain/app/request/business"
	"fobrain/fobrain/common/request"
	"fobrain/fobrain/common/response"
	"fobrain/initialize/mysql"
	"fobrain/initialize/redis"
	businessStrategy "fobrain/models/mysql/business_strategies"
	redis_helper "fobrain/models/redis"
	"github.com/gin-gonic/gin"
)

// List 规则列表
func List(c *gin.Context) error {
	handlers := make([]mysql.HandleFunc, 0)
	strategies, err := businessStrategy.NewStrategiesModel().Get(handlers...)
	if err != nil {
		return err
	}
	return response.OkWithData(c, strategies)
}

// UpdateBatch 规则更新
func UpdateBatch(c *gin.Context) error {
	params, err := request.Validate(c, &business2.StrategiesReq{})
	if err != nil {
		return err
	}
	var strategies []*businessStrategy.Strategies
	for _, strategy := range params.UpdateValue {
		strategies = append(strategies, &businessStrategy.Strategies{
			BaseModel: mysql.BaseModel{Id: strategy.Id},
			Weight:    strategy.Weight,
		})
	}
	err = businessStrategy.NewStrategiesModel().UpdateBatch(strategies)
	if err != nil {
		return err
	}
	redis.GetRedisClient().Del(c, redis_helper.BusinessStrategiesKey())
	return response.Ok(c)
}
