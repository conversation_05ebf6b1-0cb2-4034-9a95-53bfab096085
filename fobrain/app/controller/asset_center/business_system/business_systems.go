package business_system

import (
	"fmt"
	"fobrain/fobrain/app/repository/threat"
	"fobrain/models/elastic/staff"
	distributedlock "fobrain/pkg/distributedLock"
	"mime/multipart"

	"github.com/gin-gonic/gin"
	"github.com/xuri/excelize/v2"

	"fobrain/fobrain/app/request/asset_center"
	"fobrain/fobrain/app/services/asset_center/business_systems"
	"fobrain/fobrain/app/services/export"
	"fobrain/fobrain/app/services/export/handlers"
	"fobrain/fobrain/app/services/sync/file_import"
	"fobrain/fobrain/common/request"
	"fobrain/fobrain/common/response"
	business_system2 "fobrain/models/elastic/business_system"
	"fobrain/pkg/utils"
)

// Add 添加
func Add(c *gin.Context) error {
	lockKey := fmt.Sprintf("lock:business:merge")
	if !distributedlock.Lock(lockKey, "add", 120) {
		return response.FailWithCodeMessage(c, 503, "当前存在业务系统融合任务，请稍后再试")
	}
	defer distributedlock.Unlock(lockKey, "add")
	params, err := request.Validate(c, &asset_center.BusinessSystemsRequest{})
	if err != nil {
		return err
	}
	if params.BusinessName == "" {

	}
	businessSystemsService := business_systems.NewService()
	err = businessSystemsService.Add(params, "", request.GetUserId(c))
	if err != nil {
		return err
	}
	return response.OkWithMessage(c, "添加成功")
}

// Update 更新
func Update(c *gin.Context) error {
	lockKey := fmt.Sprintf("lock:business:merge")
	if !distributedlock.Lock(lockKey, "update", 120) {
		return response.FailWithCodeMessage(c, 503, "当前存在业务系统融合任务，请稍后再试")
	}
	defer distributedlock.Unlock(lockKey, "update")
	params, err := request.Validate(c, &asset_center.BusinessSystemsUpdateRequest{})
	if err != nil {
		return err
	}
	businessSystemsService := business_systems.NewService()
	err = businessSystemsService.Update(params, request.GetUserId(c))
	if err != nil {
		return err
	}
	return response.OkWithMessage(c, "更新成功")
}

// List 业务系统列表
func List(c *gin.Context) error {
	params, err := request.Validate(c, &asset_center.BusinessSystemsSearchRequest{})
	if err != nil {
		return err
	}
	businessSystemsService := business_systems.NewService()
	total, data, err := businessSystemsService.GetBusinessSystem(params, false)
	if err != nil {
		return err
	}
	return response.OkWithPageData(c, total, params.Page, params.PerPage, data)
}

func Detail(c *gin.Context) error {
	id := c.Param("id")
	if id == "" {
		return response.FailWithMessage(c, "id不能为空")
	}
	result, err := threat.GetPocBusinessSystemDetail(id)
	if err != nil {
		return err
	}
	return response.OkWithData(c, result)
}

// RecycleBin 业务系统回收站列表
func RecycleBin(c *gin.Context) error {
	params, err := request.Validate(c, &asset_center.BusinessSystemsSearchRequest{})
	if err != nil {
		return err
	}
	businessSystemsService := business_systems.NewService()
	total, data, err := businessSystemsService.GetBusinessSystem(params, true)
	if err != nil {
		return err
	}
	return response.OkWithPageData(c, total, params.Page, params.PerPage, data)
}

// Reduction 回收站恢复
func Reduction(c *gin.Context) error {
	params, err := request.Validate(c, &asset_center.BusinessSystemDeleteRequest{})
	if err != nil {
		return err
	}
	businessSystemsService := business_systems.NewService()
	params.Ids = utils.CompactStrings(params.Ids)
	err = businessSystemsService.UpdateDeletedAt(params.Ids, params.Keyword, false)
	if err != nil {
		return err
	}
	return response.Ok(c)
}

// Delete 软删除
func Delete(c *gin.Context) error {
	lockKey := fmt.Sprintf("lock:business:merge")
	if !distributedlock.Lock(lockKey, "delete", 120) {
		return response.FailWithCodeMessage(c, 503, "当前存在业务系统融合任务，请稍后再试")
	}
	defer distributedlock.Unlock(lockKey, "delete")
	params, err := request.Validate(c, &asset_center.BusinessSystemDeleteRequest{})
	if err != nil {
		return err
	}
	businessSystemsService := business_systems.NewService()
	params.Ids = utils.CompactStrings(params.Ids)
	err = businessSystemsService.UpdateDeletedAt(params.Ids, params.Keyword, true)
	if err != nil {
		return err
	}
	return response.Ok(c)
}

// Purge 彻底删除
func Purge(c *gin.Context) error {
	lockKey := fmt.Sprintf("lock:business:merge")
	if !distributedlock.Lock(lockKey, "purge", 120) {
		return response.FailWithCodeMessage(c, 503, "当前存在业务系统融合任务，请稍后再试")
	}
	defer distributedlock.Unlock(lockKey, "purge")
	params, err := request.Validate(c, &asset_center.BusinessSystemsSearchRequest{})
	if err != nil {
		return err
	}
	businessSystemsService := business_systems.NewService()
	params.Ids = utils.CompactStrings(params.Ids)
	err = businessSystemsService.Delete(params)
	if err != nil {
		return err
	}
	return response.Ok(c)
}

// Export 导出
func Export(c *gin.Context) error {
	params, err := request.Validate(c, &asset_center.BusinessSystemsSearchRequest{})
	if err != nil {
		return err
	}

	// 使用新的导出框架
	handler := handlers.NewBusinessSystemHandler(false)
	exportService := export.NewService().WithConcurrency(10)

	path, err := exportService.Export(c, handler, params)
	if err != nil {
		return err
	}

	return response.OkWithFile(c, path, true)
}

// RecycleBinExport 回收站导出
func RecycleBinExport(c *gin.Context) error {
	params, err := request.Validate(c, &asset_center.BusinessSystemsSearchRequest{})
	if err != nil {
		return err
	}

	// 使用新的导出框架
	handler := handlers.NewBusinessSystemHandler(true)
	exportService := export.NewService().WithConcurrency(10)

	path, err := exportService.Export(c, handler, params)
	if err != nil {
		return err
	}

	return response.OkWithFile(c, path, true)
}

func Import(c *gin.Context) error {
	lockKey := fmt.Sprintf("lock:business:merge")
	if !distributedlock.Lock(lockKey, "import", 7200) {
		return response.FailWithCodeMessage(c, 503, "当前存在业务系统融合任务，不可导入，请稍后再试")
	}
	defer distributedlock.Unlock(lockKey, "import")
	file, _, err := c.Request.FormFile("file")
	if err != nil {
		return err
	}

	defer func(file multipart.File) {
		err := file.Close()
		if err != nil {
			err := response.FailWithCodeMessage(c, 500, "关闭文件失败")
			if err != nil {
				return
			}
			return
		}
	}(file)

	f, err := excelize.OpenReader(file)
	if err != nil {
		return err
	}

	//读取Sheet1的数据
	rows, err := f.GetRows("Sheet1", excelize.Options{RawCellValue: true})
	if err != nil {
		return err
	}

	if len(rows) > 30*10000 {
		return response.FailWithCodeMessage(c, 400, "单次文件同步数量不能大于30w数据")
	}

	var data []file_import.BusinessSystems
	data, topErr := file_import.CheckBusinessSystem(rows)
	if topErr != nil {
		return response.FailWithDetailed(c, 400, nil, topErr.Error())
	}
	userId := request.GetUserId(c)
	// 导入的负责人字段
	staffField := c.PostForm("person_field")
	if staffField == "" {
		return response.FailWithCodeMessage(c, 400, "负责人匹配字段不能为空")
	}
	//更新或插入业务系统
	_ = staff.NewStaff().CacheAllStaff(true)
	err = business_systems.NewService().BatchUpdateOrCreate(data, userId, staffField)

	if err != nil {
		return err
	}

	return response.Ok(c)
}

func Batch(c *gin.Context) error {
	lockKey := fmt.Sprintf("lock:business:merge")
	if !distributedlock.Lock(lockKey, "batch", 120) {
		return response.FailWithCodeMessage(c, 503, "当前存在业务系统融合任务，请稍后再试")
	}
	defer distributedlock.Unlock(lockKey, "batch")
	params, err := request.Validate(c, &asset_center.BusinessSystemsSearchRequest{})
	if err != nil {
		return err
	}
	businessSystemsService := business_systems.NewService()
	params.Ids = utils.CompactStrings(params.Ids)
	err = businessSystemsService.BatchUpdate(params, request.GetUserId(c))
	if err != nil {
		return err
	}
	return response.OkWithMessage(c, "更新成功")
}

// SetBlacklist 移入黑名单
func SetBlacklist(c *gin.Context) error {
	lockKey := fmt.Sprintf("lock:business:merge")
	if !distributedlock.Lock(lockKey, "setBlackList", 120) {
		return response.FailWithCodeMessage(c, 503, "当前存在业务系统融合任务，请稍后再试")
	}
	defer distributedlock.Unlock(lockKey, "setBlackList")
	params, err := request.Validate(c, &asset_center.BusinessSystemDeleteRequest{})
	if err != nil {
		return err
	}
	businessSystemsService := business_systems.NewService()
	params.Ids = utils.CompactStrings(params.Ids)
	//err = businessSystemsService.SetStatus(params.Ids, params.Name, params.Status, params.SearchCondition, business_system2.TrustStatusNo)
	_, err = businessSystemsService.SetStatus(params.Ids, params.Keyword, params.Status, params.SearchCondition, business_system2.TrustStatusNo)
	if err != nil {
		return err
	}
	return response.Ok(c)
}

// UnconfirmedList 移出黑名单
func UnconfirmedList(c *gin.Context) error {
	lockKey := fmt.Sprintf("lock:business:merge")
	if !distributedlock.Lock(lockKey, "UnconfirmedList", 120) {
		return response.FailWithCodeMessage(c, 503, "当前存在业务系统融合任务，请稍后再试")
	}
	defer distributedlock.Unlock(lockKey, "UnconfirmedList")
	params, err := request.Validate(c, &asset_center.BusinessSystemDeleteRequest{})
	if err != nil {
		return err
	}
	businessSystemsService := business_systems.NewService()
	params.Ids = utils.CompactStrings(params.Ids)
	//err = businessSystemsService.SetStatus(params.Ids, params.Name, params.Status, params.SearchCondition, business_system2.TrustStatusYesOrNo)
	_, err = businessSystemsService.SetStatus(params.Ids, params.Keyword, params.Status, params.SearchCondition, business_system2.TrustStatusYesOrNo)
	if err != nil {
		return err
	}
	return response.Ok(c)
}

// SetCredible 批量设置可信（只要有业务系统名称和部门就将其改为可信）
func SetCredible(c *gin.Context) error {
	lockKey := fmt.Sprintf("lock:business:merge")
	if !distributedlock.Lock(lockKey, "SetCredible", 120) {
		return response.FailWithCodeMessage(c, 503, "当前存在业务系统融合任务，请稍后再试")
	}
	defer distributedlock.Unlock(lockKey, "SetCredible")
	params, err := request.Validate(c, &asset_center.BusinessSystemDeleteRequest{})
	if err != nil {
		return err
	}
	businessSystemsService := business_systems.NewService()
	params.Ids = utils.CompactStrings(params.Ids)
	//err = businessSystemsService.SetStatus(params.Ids, params.Name, params.Status, params.SearchCondition, business_system2.TrustStatusYes)
	res, err := businessSystemsService.SetStatus(params.Ids, params.Keyword, params.Status, params.SearchCondition, business_system2.TrustStatusYes)
	if err != nil {
		return err
	}
	//return response.Ok(c)
	return response.OkWithData(c, res)
}

// DepartmentList 部门列表
func DepartmentList(c *gin.Context) error {
	params, err := request.Validate(c, &asset_center.BusinessSystemDeleteRequest{})
	if err != nil {
		return err
	}
	businessSystemsService := business_systems.NewService()
	departments, err := businessSystemsService.GetDepartments(params.Keyword)
	if err != nil {
		return err
	}
	return response.OkWithData(c, departments)
}
