package business_system

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"fobrain/initialize/mysql"
	pb "fobrain/mergeService/proto"
	businessStrategy "fobrain/models/mysql/business_strategies"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/alicebob/miniredis/v2"
	"github.com/go-redis/redis/v8"

	"github.com/gin-gonic/gin"
	"github.com/olivere/elastic/v7"
	"github.com/stretchr/testify/assert"

	testcommon "fobrain/fobrain/tests/common_test"
)

func TestUpdate(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	// 初始化 Redis mock
	s, err := miniredis.Run()
	if err != nil {
		t.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
	}
	defer s.Close()

	cli := redis.NewClient(&redis.Options{
		Addr: s.Addr(),
	})
	testcommon.SetRedisClient(cli)

	// 模拟 Redis 设置操作
	mockRedisSet := gomonkey.ApplyMethodFunc(testcommon.GetRedisClient(), "Set", func(ctx context.Context, key string, value interface{}, expiration time.Duration) *redis.StatusCmd {
		cmd := redis.NewStatusCmd(ctx)
		return cmd
	})
	defer mockRedisSet.Reset()

	mockServer.Register("/_bulk", &elastic.BulkIndexByScrollResponse{
		Updated: 1,
	})
	mockServer.Register("/business_systems/_search", []*elastic.SearchHit{
		{
			Id: "1",
			Source: []byte(`{
				  "id": "1",
				  "fid": "1",
				  "business_name": "XXXX业务系统测试",
				  "business_app_principal": "",
				  "assets_attribute": {
					"is_gj": 1,
					"is_xc": 1,
					"purchase_type": 1,
					"important_types": 1,
					"insurance_level": 1
				  },
				  "system_version": "2.0",
				  "operating_env": 1
			}`),
		},
	})
	// 创建一个测试请求和响应
	w := httptest.NewRecorder()
	req := httptest.NewRequest("POST", "/api/v1/business_systems", bytes.NewBufferString(`{
  "id": "1",
  "business_name": "XXXX业务系统测试1",
  "business_app_principal": "",
  "assets_attribute": {
    "is_gj": 1,
    "is_xc": 1,
    "purchase_type": 1,
    "important_types": 1,
    "insurance_level": 1
  },
  "system_version": "2.0",
  "operating_env": 1
}`))

	req.Header.Set("Content-Type", "application/json")
	// 创建一个Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 调用函数
	err = Update(c)

	// 断言错误为 nil
	fmt.Print(err)
	assert.Errorf(t, err, "业务系统名称已存在")
	//assert.Empty(t, err)
}

func TestAdd(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()
	s, err := miniredis.Run()
	if err != nil {
		t.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
	}
	defer s.Close()

	cli := redis.NewClient(&redis.Options{
		Addr: s.Addr(),
	})
	testcommon.SetRedisClient(cli)

	defer gomonkey.ApplyMethodReturn(pb.GetProtoClient(), "TriggerMergeForAsset", nil, nil).Reset()

	mockServer.Register("/_bulk", &elastic.BulkIndexByScrollResponse{
		Created: 1,
	})
	mockServer.Register("/asset/_update_by_query", &elastic.BulkIndexByScrollResponse{
		Updated: 1,
	})
	// 创建一个测试请求和响应
	w := httptest.NewRecorder()
	req := httptest.NewRequest("POST", "/api/v1/business_systems", bytes.NewBufferString(`{
  "business_name": "XXXX业务系统",
  "business_app_principal": "",
  "assets_attribute": {
    "is_gj": 1,
    "is_xc": 1,
    "purchase_type": 1,
    "important_types": 1,
    "insurance_level": 1
  },
  "system_version": "2.0",
  "operating_env": 1
}`))
	mockServer.Register("/business_systems/_search", []*elastic.SearchHit{})
	mockServer.Register("/asset/_search", []*elastic.SearchHit{})
	req.Header.Set("Content-Type", "application/json")
	mockDB := gomonkey.ApplyMethodFunc(businessStrategy.NewStrategiesModel(), "Get", func(handlers ...mysql.HandleFunc) ([]*businessStrategy.Strategies, error) {
		return []*businessStrategy.Strategies{
			{BaseModel: mysql.BaseModel{Id: 1}, Weight: 10},
			{BaseModel: mysql.BaseModel{Id: 2}, Weight: 20},
		}, nil
	})
	// 模拟 Redis 设置操作
	mockRedisSet := gomonkey.ApplyMethodFunc(testcommon.GetRedisClient(), "Set", func(ctx context.Context, key string, value interface{}, expiration time.Duration) *redis.StatusCmd {
		cmd := redis.NewStatusCmd(ctx)
		return cmd
	})
	defer mockRedisSet.Reset()
	defer mockDB.Reset()
	// 创建一个Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 调用函数
	err = Add(c)
	// 断言错误为 nil
	assert.Nil(t, err)
}

func TestList(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	// 初始化 Redis mock
	s, err := miniredis.Run()
	if err != nil {
		t.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
	}
	defer s.Close()

	cli := redis.NewClient(&redis.Options{
		Addr: s.Addr(),
	})
	testcommon.SetRedisClient(cli)

	// 模拟 Redis 设置操作
	mockRedisSet := gomonkey.ApplyMethodFunc(testcommon.GetRedisClient(), "Set", func(ctx context.Context, key string, value interface{}, expiration time.Duration) *redis.StatusCmd {
		cmd := redis.NewStatusCmd(ctx)
		return cmd
	})
	defer mockRedisSet.Reset()

	mockServer.Register("/business_systems/_search", []*elastic.SearchHit{
		{
			Id:     "1",
			Source: []byte(`{"business_name":"example"}`),
		},
	})
	// 创建一个测试请求和响应
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/business?page=1&per_page=1", nil)
	req.Header.Set("Content-Type", "application/json")
	// 创建一个Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 调用函数
	err = List(c)

	// 断言错误为 nil
	fmt.Print(err)
	assert.Empty(t, err)
}

func TestRecycleBin(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	// 初始化 Redis mock
	s, err := miniredis.Run()
	if err != nil {
		t.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
	}
	defer s.Close()

	cli := redis.NewClient(&redis.Options{
		Addr: s.Addr(),
	})
	testcommon.SetRedisClient(cli)

	// 模拟 Redis 设置操作
	mockRedisSet := gomonkey.ApplyMethodFunc(testcommon.GetRedisClient(), "Set", func(ctx context.Context, key string, value interface{}, expiration time.Duration) *redis.StatusCmd {
		cmd := redis.NewStatusCmd(ctx)
		return cmd
	})
	defer mockRedisSet.Reset()

	mockServer.Register("/business_systems/_search", []*elastic.SearchHit{
		{
			Id:     "1",
			Source: []byte(`{"business_name":"example"}`),
		},
	})
	// 创建一个测试请求和响应
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/business/recycle_bin?page=1&per_page=1", nil)
	req.Header.Set("Content-Type", "application/json")
	// 创建一个Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 调用函数
	err = RecycleBin(c)

	// 断言错误为 nil
	fmt.Print(err)
	assert.Empty(t, err)
}

func TestReduction(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	mockServer.Register("/business_systems/_update_by_query", &elastic.BulkIndexByScrollResponse{
		Updated: 1,
	})

	// 初始化 Redis mock
	s, err := miniredis.Run()
	if err != nil {
		t.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
	}
	defer s.Close()

	cli := redis.NewClient(&redis.Options{
		Addr: s.Addr(),
	})
	testcommon.SetRedisClient(cli)

	// 模拟 Redis 设置操作
	mockRedisSet := gomonkey.ApplyMethodFunc(testcommon.GetRedisClient(), "Set", func(ctx context.Context, key string, value interface{}, expiration time.Duration) *redis.StatusCmd {
		cmd := redis.NewStatusCmd(ctx)
		return cmd
	})
	defer mockRedisSet.Reset()

	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()
	// mockDb.ExpectQuery("SELECT count(*) FROM `merge_records` WHERE `task_status` = ?").
	// 	WithArgs("执行中").WillReturnRows(mockDb.NewRows([]string{"count"}).AddRow(0))
	// mockServer.Register("/business_systems/_update_by_query", &elastic.BulkIndexByScrollResponse{
	// 	Updated: 1,
	// })
	// 创建一个测试请求和响应
	w := httptest.NewRecorder()
	req := httptest.NewRequest("POST", "/api/v1/business/reduction?page=1&per_page=1", bytes.NewBufferString(`{"ids": ["1"]}`))
	req.Header.Set("Content-Type", "application/json")
	// 创建一个Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req
	c.Set("user_id", uint64(1))
	c.Set("is_super_manage", true)
	// 调用函数
	err = Reduction(c)

	// 断言错误为 nil
	fmt.Print(err)
	assert.Empty(t, err)
}

func TestDelete(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	// 初始化 Redis mock
	s, err := miniredis.Run()
	if err != nil {
		t.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
	}
	defer s.Close()

	cli := redis.NewClient(&redis.Options{
		Addr: s.Addr(),
	})
	testcommon.SetRedisClient(cli)

	// 模拟 Redis 设置操作
	mockRedisSet := gomonkey.ApplyMethodFunc(testcommon.GetRedisClient(), "Set", func(ctx context.Context, key string, value interface{}, expiration time.Duration) *redis.StatusCmd {
		cmd := redis.NewStatusCmd(ctx)
		return cmd
	})
	defer mockRedisSet.Reset()

	mockServer.Register("/business_systems/_update_by_query", &elastic.BulkIndexByScrollResponse{
		Updated: 1,
	})
	// 创建一个测试请求和响应
	w := httptest.NewRecorder()
	req := httptest.NewRequest("DELETE", "/api/v1/business", bytes.NewBufferString(`{"ids": ["1"]}`))
	req.Header.Set("Content-Type", "application/json")
	// 创建一个Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 调用函数
	err = Delete(c)

	// 断言错误为 nil
	fmt.Print(err)
	assert.Empty(t, err)
}

func TestPurge(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	// 初始化 Redis mock
	s, err := miniredis.Run()
	if err != nil {
		t.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
	}
	defer s.Close()

	cli := redis.NewClient(&redis.Options{
		Addr: s.Addr(),
	})
	testcommon.SetRedisClient(cli)

	// 模拟 Redis 设置操作
	mockRedisSet := gomonkey.ApplyMethodFunc(testcommon.GetRedisClient(), "Set", func(ctx context.Context, key string, value interface{}, expiration time.Duration) *redis.StatusCmd {
		cmd := redis.NewStatusCmd(ctx)
		return cmd
	})
	defer mockRedisSet.Reset()

	mockServer.Register("/business_systems/_delete_by_query", &elastic.BulkIndexByScrollResponse{
		Deleted: 1,
	})
	mockServer.Register("/process_business_systems/_delete_by_query", &elastic.BulkIndexByScrollResponse{
		Deleted: 1,
	})
	// 创建一个测试请求和响应
	w := httptest.NewRecorder()
	req := httptest.NewRequest("DELETE", "/api/v1/business/purge?ids=1", nil)
	// 创建一个Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 调用函数
	err = Purge(c)

	// 断言错误为 nil
	fmt.Print(err)
	assert.Empty(t, err)
}

func TestExport(t *testing.T) {
	// 创建 mock 服务器
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	// 初始化 Redis mock
	s, err := miniredis.Run()
	if err != nil {
		t.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
	}
	defer s.Close()

	cli := redis.NewClient(&redis.Options{
		Addr: s.Addr(),
	})
	testcommon.SetRedisClient(cli)

	// 模拟 Redis 设置操作
	mockRedisSet := gomonkey.ApplyMethodFunc(testcommon.GetRedisClient(), "Set", func(ctx context.Context, key string, value interface{}, expiration time.Duration) *redis.StatusCmd {
		cmd := redis.NewStatusCmd(ctx)
		return cmd
	})
	defer mockRedisSet.Reset()

	// 用于跟踪搜索调用次数
	var searchCallCount int

	// mock ES 数据计数 - 确保有数据
	mockServer.Register("/business_systems/_count", map[string]interface{}{
		"count": 1,
		"_shards": map[string]interface{}{
			"total":      1,
			"successful": 1,
			"skipped":    0,
			"failed":     0,
		},
	})

	// 注册自定义搜索处理器，正确处理 search_after 分页查询
	mockServer.RegisterHandler("/business_systems/_search", func(w http.ResponseWriter, r *http.Request) {
		searchCallCount++
		w.Header().Set("Content-Type", "application/json")

		if searchCallCount == 1 {
			// 第一次查询，返回有数据的响应
			response := elastic.SearchResult{
				Hits: &elastic.SearchHits{
					TotalHits: &elastic.TotalHits{
						Value:    1,
						Relation: "eq",
					},
					Hits: []*elastic.SearchHit{
						{
							Index: "business_systems",
							Id:    "1",
							Source: []byte(`{
								"id": "1",
								"fid": "business-001",
								"business_name": "测试业务系统",
								"business_address": "http://test.example.com",
								"person_base": [
									{
										"name": "张三",
										"department": [
											{
												"name": "技术部"
											}
										]
									}
								],
								"assets_attribute": {
									"is_gj": 1,
									"is_xc": 1,
									"purchase_type": 1,
									"important_types": 1,
									"insurance_level": 1,
									"operating_env": 1,
									"continuity_level": 1,
									"planned_use": 1,
									"running_status": 1
								},
								"system_version": "v1.0",
								"created_at": "2024-01-01 10:00:00",
								"updated_at": "2024-01-01 10:00:00"
							}`),
							Sort: []interface{}{"2024-01-01 10:00:00", "1"},
						},
					},
				},
			}
			json.NewEncoder(w).Encode(response)
		} else {
			// 后续查询，返回空结果以结束分页
			response := elastic.SearchResult{
				Hits: &elastic.SearchHits{
					TotalHits: &elastic.TotalHits{
						Value:    0,
						Relation: "eq",
					},
					Hits: []*elastic.SearchHit{},
				},
			}
			json.NewEncoder(w).Encode(response)
		}
	})

	// mock 数据库查询
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()

	// mock 数据源查询
	mockDb.ExpectQuery("SELECT * FROM `data_sources`").
		WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).AddRow(1, "测试数据源"))

	// mock 自定义字段查询
	mockDb.ExpectQuery("SELECT * FROM `custom_field_meta` WHERE (module_type = ? and deleted_at is null) AND `custom_field_meta`.`deleted_at` IS NULL").
		WithArgs("业务系统").WillReturnRows(sqlmock.NewRows([]string{"id", "field_key", "display_name"}).AddRow(1, "custom_key", "自定义字段"))

	// 创建一个测试请求和响应
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/business/export?ids=1", nil)

	// 创建一个Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req
	c.Set("staff_ids", []string{})
	c.Set("is_super_manage", true)

	// 调用函数
	err = Export(c)

	// 断言错误为 nil
	fmt.Print(err)
	assert.Empty(t, err)
}

func TestBatch(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	// 初始化 Redis mock
	s, err := miniredis.Run()
	if err != nil {
		t.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
	}
	defer s.Close()

	cli := redis.NewClient(&redis.Options{
		Addr: s.Addr(),
	})
	testcommon.SetRedisClient(cli)

	// 模拟 Redis 设置操作
	mockRedisSet := gomonkey.ApplyMethodFunc(testcommon.GetRedisClient(), "Set", func(ctx context.Context, key string, value interface{}, expiration time.Duration) *redis.StatusCmd {
		cmd := redis.NewStatusCmd(ctx)
		return cmd
	})
	defer mockRedisSet.Reset()

	mockServer.Register("/business_systems/_update_by_query", &elastic.BulkIndexByScrollResponse{
		Updated: 1,
	})
	// 创建一个测试请求和响应
	w := httptest.NewRecorder()
	req := httptest.NewRequest("PUT", "/api/v1/business_systems/batch", bytes.NewBufferString(`{
  "assets_attribute": {
    "is_gj": 1,
    "is_xc": 1,
    "purchase_type": 1,
    "important_types": 1,
    "insurance_level": 1
  }
}`))
	mockServer.Register("/business_systems/_search", []*elastic.SearchHit{})
	mockServer.Register("/asset/_search", []*elastic.SearchHit{})
	mockServer.Register("/_search/scroll", []*elastic.SearchHit{})
	req.Header.Set("Content-Type", "application/json")
	// 创建一个Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 调用函数
	err = Batch(c)

	// 断言错误为 nil
	fmt.Print(err)
	assert.Empty(t, err)
}
