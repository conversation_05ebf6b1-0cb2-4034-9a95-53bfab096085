package search_condition

import (
	"fmt"
	services "fobrain/fobrain/app/services/ip_mapping"
	"fobrain/models/mysql/custom_column"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"

	"fobrain/fobrain/common/request"
	"fobrain/fobrain/common/response"
)

type (
	SearchConditionRequest struct {
		Type string `json:"type" form:"type" uri:"type" validate:"required,oneof=Internal External Loop Device Personal BusinessApp Mapping TagRule ComplianceRisk" zh:"模糊搜索"`
	}
	SearchConditionResponse struct {
		Label     string            `json:"label" form:"label" zh:"文本内容"`
		Type      string            `json:"type" form:"type" zh:"值类型, select, textarea, Date, cascader"`
		Field     string            `json:"field" form:"field" zh:"字段名称"`
		Condition OrderedConditions `json:"condition" form:"condition" zh:"操作符号"`
		Options   string            `json:"options" form:"options" zh:"下拉选项(字符串，仅下拉类型用)"`
	}

	OrderedConditions struct {
		In       string `json:"in,omitempty"`       // 如果为空则不输出到 JSON
		NotIn    string `json:"not_in,omitempty"`   // 如果为空则不输出到 JSON
		Equal    string `json:"==,omitempty"`       // 如果为空则不输出到 JSON
		NotEqual string `json:"!==,omitempty"`      // 如果为空则不输出到 JSON
		Null     string `json:"null,omitempty"`     // 如果为空则不输出到 JSON
		NotNull  string `json:"not_null,omitempty"` // 如果为空则不输出到 JSON
	}
)

const (
	EqualTo        = "=="
	NotEqualTo     = "!=="
	In             = "in"
	NotIn          = "not_in"
	NotNull        = "not_null"
	Nu             = "null"
	EqualToName    = "精准查询,有且仅有"
	NotEqualToName = "精准剔除"
	NuName         = "为空"
	NotNullName    = "不为空"
	InName         = "包含"
	NotInName      = "不包含"
)

var ConditionNames = map[string]string{
	In:         InName,
	NotIn:      NotInName,
	EqualTo:    EqualToName,
	NotEqualTo: NotEqualToName,
	Nu:         NuName,
	NotNull:    NotNullName,
}
var onlyEqualConditionNames = map[string]string{
	EqualTo:    EqualToName,
	NotEqualTo: NotEqualToName,
}
var inAndEqualConditionNames = map[string]string{
	In:         InName,
	NotIn:      NotInName,
	EqualTo:    EqualToName,
	NotEqualTo: NotEqualToName,
}

var SearchConditionValidate = request.Validate[SearchConditionRequest]

func SearchCondition(c *gin.Context) error {
	params, err := SearchConditionValidate(c, &SearchConditionRequest{})
	if err != nil {
		return err
	}
	condition := []SearchConditionResponse{}
	switch params.Type {
	case "Internal", "External":
		condition = internalOrExternal()
	case "Loop":
		condition = loop()
	case "Device":
		condition = device()
	case "Personal":
		condition = personal()
	case "Mapping":
		condition = mapping()
	case "TagRule":
		condition = tagRule()
	case "ComplianceRisk":
		condition = complianceRisks()
	case "BusinessApp":
		condition = business()
	default:
		return response.OkWithData(c, nil)
	}
	customType := ""
	switch params.Type {
	case "Internal", "External":
		customType = custom_column.CustomFieldMetaModuleTypeAsset
	case "Loop":
		customType = custom_column.CustomFieldMetaModuleTypeVuln
	case "Device":
		customType = custom_column.CustomFieldMetaModuleTypeDevice
	case "Personal":
		customType = custom_column.CustomFieldMetaModuleTypeStaff
	case "BusinessApp":
		customType = custom_column.CustomFieldMetaModuleTypeBusinessSystem
	}
	customCondition := []SearchConditionResponse{}
	if customType != "" {
		customFields, _ := custom_column.NewCustomFieldMetaModel().GetByModuleType(customType, false)
		for _, customField := range customFields {
			customCondition = append(customCondition, SearchConditionResponse{
				Label:     customField.DisplayName,
				Type:      customField.FieldType,
				Field:     fmt.Sprintf("custom_fields.%s", customField.FieldKey),
				Condition: BuildOrderedConditions(ConditionNames),
				Options:   customField.Options,
			})
		}
	}
	return response.OkWithData(c, append(condition, customCondition...))
}

// internalOrExternal 内部或外部资产搜索
func internalOrExternal() []SearchConditionResponse {
	return []SearchConditionResponse{
		{
			Label:     "数据源",
			Type:      "select",
			Field:     "all_source_ids",
			Condition: BuildOrderedConditions(ConditionNames),
		},
		{
			Label: "IP地址",
			Type:  "textarea",
			Field: "ip.keyword",
			Condition: BuildOrderedConditions(map[string]string{ // ip是必填属性，不支持按照是否空搜索（无意义）
				In:         InName,
				NotIn:      NotInName,
				EqualTo:    EqualToName,
				NotEqualTo: NotEqualToName,
			}),
		},
		{
			Label:     "端口",
			Type:      "textarea",
			Field:     "ports.port",
			Condition: BuildOrderedConditions(ConditionNames),
		},
		{
			Label:     "协议",
			Type:      "textarea",
			Field:     "ports.protocol",
			Condition: BuildOrderedConditions(ConditionNames),
		},
		{
			Label:     "组件信息",
			Type:      "textarea",
			Field:     "rule_infos.product",
			Condition: BuildOrderedConditions(ConditionNames),
		},
		{
			Label:     "资产状态",
			Type:      "select",
			Field:     "status",
			Condition: BuildOrderedConditions(ConditionNames),
		},
		//{
		//	Label: "映射IP", // TODO: 资产中无关联此字段
		//	Type:  "textarea",
		//	Field: "",
		//},
		{
			Label:     "IP类型",
			Type:      "select",
			Field:     "ip_type",
			Condition: BuildOrderedConditions(ConditionNames),
		},
		{
			Label:     "MAC地址",
			Type:      "textarea",
			Field:     "mac",
			Condition: BuildOrderedConditions(ConditionNames),
		},
		{
			Label:     "域名",
			Type:      "textarea",
			Field:     "ports.domain.keyword",
			Condition: BuildOrderedConditions(ConditionNames),
		},
		{
			Label:     "所属区域",
			Type:      "select",
			Field:     "area",
			Condition: BuildOrderedConditions(ConditionNames),
		},
		//{
		//	Label: "关联实体设备", // TODO: 资产中无关联此字段
		//	Type:  "textarea",
		//	Field: "",
		//},
		{
			Label:     "运维人员",
			Type:      "select",
			Field:     "oper_info.id",
			Condition: BuildOrderedConditions(ConditionNames),
		},
		{
			Label:     "运维部门",
			Type:      "cascader",
			Field:     "oper_department.name.keyword",
			Condition: BuildOrderedConditions(ConditionNames),
		},
		{
			Label:     "上报数据源",
			Type:      "select",
			Field:     "source_count",
			Condition: BuildOrderedConditionsOnlyEqual(ConditionNames),
		},
		{
			Label:     "是否关联漏洞",
			Type:      "select",
			Field:     "poc_num",
			Condition: BuildOrderedConditionsOnlyEqual(ConditionNames),
		},
		{
			Label:     "主机名",
			Type:      "textarea",
			Field:     "hostname",
			Condition: BuildOrderedConditions(ConditionNames),
		},
		{
			Label:     "业务系统",
			Type:      "select",
			Field:     "business.system",
			Condition: BuildOrderedConditions(ConditionNames),
		},
		{
			Label:     "业务系统负责人",
			Type:      "select",
			Field:     "business.person_base.id",
			Condition: BuildOrderedConditions(ConditionNames),
		},
		{
			Label:     "业务部门",
			Type:      "cascader",
			Field:     "business_department.name.keyword",
			Condition: BuildOrderedConditions(ConditionNames),
		},
		{
			Label:     "业务系统状态",
			Type:      "select",
			Field:     "business.business_trusted_state",
			Condition: BuildOrderedConditionsOnlyEqual(ConditionNames),
		},
		{
			Label:     "分类",
			Type:      "select",
			Field:     "rule_infos.first_tag",
			Condition: BuildOrderedConditions(ConditionNames),
		},
		{
			Label:     "资产类型",
			Type:      "select",
			Field:     "rule_infos.second_tag",
			Condition: BuildOrderedConditions(ConditionNames),
		},
	}
}

// device 设备搜索
func device() []SearchConditionResponse {
	return []SearchConditionResponse{
		{
			Label:     "数据源",
			Type:      "select",
			Field:     "all_source_ids",
			Condition: BuildOrderedConditions(ConditionNames),
		},
		{
			Label:     "设备名称",
			Type:      "textarea",
			Field:     "hostname",
			Condition: BuildOrderedConditions(ConditionNames),
		},
		//{
		//	Label: "设备类型", // TODO: 设备无关联此字段
		//	Type:  "textarea",
		//	Field: "",
		//},
		{
			Label:     "SN",
			Type:      "textarea",
			Field:     "sn",
			Condition: BuildOrderedConditions(ConditionNames),
		},
		{
			Label:     "MAC地址",
			Type:      "textarea",
			Field:     "mac",
			Condition: BuildOrderedConditions(ConditionNames),
		},
		{
			Label:     "操作系统",
			Type:      "textarea",
			Field:     "os",
			Condition: BuildOrderedConditions(ConditionNames),
		},
		{
			Label:     "机房",
			Type:      "textarea",
			Field:     "machine_room",
			Condition: BuildOrderedConditions(ConditionNames),
		},
		{
			Label:     "关联内网IP",
			Type:      "textarea",
			Field:     "private_ip",
			Condition: BuildOrderedConditions(ConditionNames),
		},
		{
			Label:     "关联互联网IP",
			Type:      "textarea",
			Field:     "public_ip",
			Condition: BuildOrderedConditions(ConditionNames),
		},
		{
			Label:     "运维人员",
			Type:      "select",
			Field:     "opers.oper_info.id",
			Condition: BuildOrderedConditions(ConditionNames),
		},
		{
			Label:     "是否关联漏洞",
			Type:      "select",
			Field:     "poc_num",
			Condition: BuildOrderedConditionsOnlyEqual(ConditionNames),
		},
	}
}

// personal 人员
func personal() []SearchConditionResponse {
	return []SearchConditionResponse{
		{
			Label:     "数据源",
			Type:      "select",
			Field:     "all_source_ids",
			Condition: BuildOrderedConditions(ConditionNames),
		},
		{
			Label:     "姓名",
			Type:      "textarea",
			Field:     "name",
			Condition: BuildOrderedConditions(ConditionNames),
		},
		{
			Label:     "英文名",
			Type:      "textarea",
			Field:     "english_name",
			Condition: BuildOrderedConditions(ConditionNames),
		},
		{
			Label:     "职位",
			Type:      "select",
			Field:     "title",
			Condition: BuildOrderedConditions(ConditionNames),
		},
		{
			Label:     "电话",
			Type:      "textarea",
			Field:     "mobile",
			Condition: BuildOrderedConditions(ConditionNames),
		},
		{
			Label:     "邮箱",
			Type:      "textarea",
			Field:     "email",
			Condition: BuildOrderedConditions(ConditionNames),
		},
		{
			Label:     "在职状态",
			Type:      "select",
			Field:     "status",
			Condition: BuildOrderedConditions(onlyEqualConditionNames),
		},
		{
			Label:     "工号",
			Type:      "textarea",
			Field:     "work_number",
			Condition: BuildOrderedConditions(ConditionNames),
		},
	}
}

// mapping 内外网映射ip
func mapping() []SearchConditionResponse {
	customFields, err := services.ListCustomFields("ip_mapping")
	if err != nil {
		return nil
	}
	var searchConditionResponse []SearchConditionResponse
	for _, customField := range customFields.Fields {
		for displayName, fieldName := range customField {
			searchConditionResponse = append(searchConditionResponse, SearchConditionResponse{
				Label:     displayName,
				Type:      "textarea",
				Field:     cast.ToString(fieldName),
				Condition: BuildOrderedConditions(ConditionNames),
			})
		}
	}
	return searchConditionResponse
}

func loop() []SearchConditionResponse {
	return []SearchConditionResponse{
		{
			Label:     "数据源",
			Type:      "select",
			Field:     "all_source_ids",
			Condition: BuildOrderedConditions(ConditionNames),
		},
		{
			Label:     "IP地址",
			Type:      "textarea",
			Field:     "ip.keyword",
			Condition: BuildOrderedConditions(ConditionNames),
		},
		{
			Label:     "端口",
			Type:      "textarea",
			Field:     "port",
			Condition: BuildOrderedConditions(ConditionNames),
		},
		{
			Label:     "区域",
			Type:      "select",
			Field:     "area",
			Condition: BuildOrderedConditions(ConditionNames),
		},
		{
			Label:     "漏洞名称",
			Type:      "textarea",
			Field:     "name.keyword",
			Condition: BuildOrderedConditions(ConditionNames),
		},
		{
			Label: "漏洞地址",
			Type:  "textarea",
			Field: "url",
			Condition: BuildOrderedConditions(map[string]string{ // 漏洞地址是必填属性，不支持按照是否空搜索（无意义）
				In:         InName,
				NotIn:      NotInName,
				EqualTo:    EqualToName,
				NotEqualTo: NotEqualToName,
			}),
		},
		{
			Label:     "漏洞类型",
			Type:      "textarea",
			Field:     "vulType",
			Condition: BuildOrderedConditions(ConditionNames),
		},
		{
			Label:     "漏洞等级",
			Type:      "select",
			Field:     "level",
			Condition: BuildOrderedConditions(ConditionNames),
		},
		{
			Label:     "漏洞状态",
			Type:      "select",
			Field:     "status",
			Condition: BuildOrderedConditions(inAndEqualConditionNames),
		},
		{
			Label:     "风险值",
			Type:      "textarea",
			Field:     "risk_num",
			Condition: BuildOrderedConditions(ConditionNames),
		},
		{
			Label:     "漏洞修复人",
			Type:      "textarea",
			Field:     "person.name.keyword",
			Condition: BuildOrderedConditions(ConditionNames),
		},
		{
			Label:     "修复优先级",
			Type:      "select",
			Field:     "repair_priority.keyword",
			Condition: BuildOrderedConditions(ConditionNames),
		},
		{
			Label:     "是否POC漏洞",
			Type:      "select",
			Field:     "is_poc",
			Condition: BuildOrderedConditions(onlyEqualConditionNames),
		},
		{
			Label:     "业务系统",
			Type:      "textarea",
			Field:     "business_name_tmp",
			Condition: BuildOrderedConditions(ConditionNames),
		},
		{
			Label:     "是否存在POC",
			Type:      "select",
			Field:     "has_poc",
			Condition: BuildOrderedConditions(onlyEqualConditionNames),
		},
		{
			Label:     "是否存在EXP",
			Type:      "select",
			Field:     "has_exp",
			Condition: BuildOrderedConditions(onlyEqualConditionNames),
		},
		{
			Label:     "运维部门",
			Type:      "cascader",
			Field:     "oper_department.name.keyword",
			Condition: BuildOrderedConditions(ConditionNames),
		},
		{
			Label:     "主机名",
			Type:      "textarea",
			Field:     "hostname.keyword",
			Condition: BuildOrderedConditions(ConditionNames),
		},
		{
			Label: "修复超期时间",
			Type:  "range-picker",
			Field: "limit_date",
			Condition: BuildOrderedConditions(map[string]string{
				In:      InName,
				NotIn:   NotInName,
				Nu:      NuName,
				NotNull: NotNullName,
			}),
		},
		{
			Label:     "业务部门",
			Type:      "cascader",
			Field:     "business_department.name.keyword",
			Condition: BuildOrderedConditions(ConditionNames),
		},
	}
}

func tagRule() []SearchConditionResponse {
	c := BuildOrderedConditions(map[string]string{
		In:         InName,
		NotIn:      NotInName,
		EqualTo:    EqualToName,
		NotEqualTo: NotEqualToName,
	})
	return []SearchConditionResponse{
		{
			Label:     "IP",
			Type:      "textarea",
			Field:     "ip",
			Condition: c,
		},
		{
			Label:     "网站标题",
			Type:      "textarea",
			Field:     "title",
			Condition: c,
		},
		{
			Label:     "组件",
			Type:      "textarea",
			Field:     "product",
			Condition: c,
		},
		{
			Label:     "操作系统",
			Type:      "textarea",
			Field:     "os",
			Condition: c,
		},
		{
			Label:     "业务系统",
			Type:      "select",
			Field:     "business",
			Condition: c,
		},
		{
			Label:     "人员信息",
			Type:      "textarea",
			Field:     "oper",
			Condition: c,
		},
		{
			Label:     "自定义标签",
			Type:      "textarea",
			Field:     "tag",
			Condition: c,
		},
	}
}

// complianceRisks 合规风险
func complianceRisks() []SearchConditionResponse {
	return []SearchConditionResponse{
		{
			Label:     "IP地址",
			Type:      "textarea",
			Field:     "ip.keyword",
			Condition: BuildOrderedConditions(ConditionNames),
		},
		{
			Label:     "命中规则",
			Type:      "textarea",
			Field:     "compliance_monitor_name",
			Condition: BuildOrderedConditions(inAndEqualConditionNames),
		},
		{
			Label:     "关联实体设备",
			Type:      "textarea",
			Field:     "hostname",
			Condition: BuildOrderedConditions(ConditionNames),
		},
		{
			Label:     "状态",
			Type:      "select",
			Field:     "flow_status",
			Condition: BuildOrderedConditions(inAndEqualConditionNames),
		},
		{
			Label:     "风险修复负责人",
			Type:      "textarea",
			Field:     "person_info.name.keyword",
			Condition: BuildOrderedConditions(ConditionNames),
		},
		{
			Label: "修复超期时间",
			Type:  "range-picker",
			Field: "limit_date",
			Condition: BuildOrderedConditions(map[string]string{
				In:      InName,
				NotIn:   NotInName,
				Nu:      NuName,
				NotNull: NotNullName,
			}),
		},
		{
			Label:     "业务系统",
			Type:      "select",
			Field:     "business.system",
			Condition: BuildOrderedConditions(ConditionNames),
		},
		{
			Label:     "业务系统负责人",
			Type:      "select",
			Field:     "business.person_base.id",
			Condition: BuildOrderedConditions(ConditionNames),
		},
		{
			Label:     "业务部门",
			Type:      "cascader",
			Field:     "business_department.name.keyword",
			Condition: BuildOrderedConditions(ConditionNames),
		},
		{
			Label:     "运维人员",
			Type:      "select",
			Field:     "oper_info.id",
			Condition: BuildOrderedConditions(ConditionNames),
		},
		{
			Label:     "运维部门",
			Type:      "cascader",
			Field:     "oper_department.name.keyword",
			Condition: BuildOrderedConditions(ConditionNames),
		},
		{
			Label:     "端口",
			Type:      "textarea",
			Field:     "ports.port",
			Condition: BuildOrderedConditions(ConditionNames),
		},
		{
			Label:     "协议",
			Type:      "textarea",
			Field:     "ports.protocol",
			Condition: BuildOrderedConditions(ConditionNames),
		},
		{
			Label:     "组件",
			Type:      "textarea",
			Field:     "rule_infos.product",
			Condition: BuildOrderedConditions(ConditionNames),
		},
	}
}

func business() []SearchConditionResponse {
	return []SearchConditionResponse{
		{
			Label: "数据来源",
			Type:  "select",
			Field: "from",
			Condition: BuildOrderedConditions(map[string]string{ //数据来源不可能为空，所以不支持null跟not_null
				In:         InName,
				NotIn:      NotInName,
				EqualTo:    EqualToName,
				NotEqualTo: NotEqualToName,
			}),
		},
		{
			Label:     "业务系统名称",
			Type:      "textarea",
			Field:     "business_name",
			Condition: BuildOrderedConditions(ConditionNames),
		},
		{
			Label:     "业务系统负责人",
			Type:      "textarea",
			Field:     "person_base.name",
			Condition: BuildOrderedConditions(ConditionNames),
		},
		{
			Label: "部门",
			Type:  "textarea",
			Field: "department_base.name.keyword",
			Condition: BuildOrderedConditions(map[string]string{
				In:      InName,
				EqualTo: EqualToName,
			}),
		},
		{
			Label:     "关联内网",
			Type:      "textarea",
			Field:     "intranet_ips",
			Condition: BuildOrderedConditions(ConditionNames),
		},
		{
			Label:     "关联互联网",
			Type:      "textarea",
			Field:     "internet_ips",
			Condition: BuildOrderedConditions(ConditionNames),
		},
		{
			Label:     "采购类型",
			Type:      "select",
			Field:     "assets_attribute.purchase_type",
			Condition: BuildOrderedConditions(ConditionNames),
		},
		{
			Label:     "重要性",
			Type:      "select",
			Field:     "assets_attribute.important_types",
			Condition: BuildOrderedConditions(ConditionNames),
		},
		{
			Label:     "等级保护",
			Type:      "select",
			Field:     "assets_attribute.insurance_level",
			Condition: BuildOrderedConditions(ConditionNames),
		},
		{
			Label:     "连续性级别",
			Type:      "select",
			Field:     "continuity_level",
			Condition: BuildOrderedConditions(ConditionNames),
		},
		{
			Label: "是否关基设施",
			Type:  "select",
			Field: "assets_attribute.is_gj",
			Condition: BuildOrderedConditions(map[string]string{
				EqualTo: EqualToName,
			}),
		},
		{
			Label: "是否信创",
			Type:  "select",
			Field: "assets_attribute.is_xc",
			Condition: BuildOrderedConditions(map[string]string{
				EqualTo: EqualToName,
			}),
		},
		{
			Label: "运行环境",
			Type:  "select",
			Field: "assets_attribute.operating_env",
			Condition: BuildOrderedConditions(map[string]string{
				EqualTo: EqualToName,
			}),
		},
		{
			Label: "运行状态",
			Type:  "select",
			Field: "assets_attribute.running_state",
			Condition: BuildOrderedConditions(map[string]string{
				EqualTo: EqualToName,
			}),
		},
	}
}

// BuildOrderedConditions 构造函数
func BuildOrderedConditions(input map[string]string) OrderedConditions {
	order := []string{"in", "not_in", "==", "!==", "null", "not_null"}
	condition := OrderedConditions{}
	// 按顺序填充结构体字段
	for _, key := range order {
		if value, exists := input[key]; exists {
			switch key {
			case "in":
				condition.In = value
			case "not_in":
				condition.NotIn = value
			case "==":
				condition.Equal = value
			case "!==":
				condition.NotEqual = value
			case "null":
				condition.Null = value
			case "not_null":
				condition.NotNull = value
			}
		}
	}
	return condition
}

// BuildOrderedConditionsOnlyEqual 只构建等于的条件
func BuildOrderedConditionsOnlyEqual(input map[string]string) OrderedConditions {
	order := []string{"=="}
	condition := OrderedConditions{}
	// 按顺序填充结构体字段
	for _, key := range order {
		if value, exists := input[key]; exists {
			switch key {
			case "==":
				condition.Equal = value
			}
		}
	}
	return condition
}
