package search_condition

import (
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func TestSearchCondition(t *testing.T) {
	t.Run("External Success", func(t *testing.T) {

		w := httptest.NewRecorder()
		// 发送无效的JSON数据
		req := httptest.NewRequest("GET", "/api/v1/search_condition/list?type=Internal", nil)

		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := SearchCondition(c)
		assert.Nil(t, err)
	})
	t.Run("Loop Success", func(t *testing.T) {

		w := httptest.NewRecorder()
		// 发送无效的JSON数据
		req := httptest.NewRequest("GET", "/api/v1/search_condition/list?type=Loop", nil)

		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := SearchCondition(c)
		assert.Nil(t, err)
	})
	t.Run("Device Success", func(t *testing.T) {

		w := httptest.NewRecorder()
		// 发送无效的JSON数据
		req := httptest.NewRequest("GET", "/api/v1/search_condition/list?type=Device", nil)

		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := SearchCondition(c)
		assert.Nil(t, err)
	})
	t.Run("Personal Success", func(t *testing.T) {

		w := httptest.NewRecorder()
		// 发送无效的JSON数据
		req := httptest.NewRequest("GET", "/api/v1/search_condition/list?type=Personal", nil)

		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := SearchCondition(c)
		assert.Nil(t, err)
	})
	t.Run("TagRule Success", func(t *testing.T) {

		w := httptest.NewRecorder()
		// 发送无效的JSON数据
		req := httptest.NewRequest("GET", "/api/v1/search_condition/list?type=TagRule", nil)

		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := SearchCondition(c)
		assert.Nil(t, err)
	})
	t.Run("Mapping Success", func(t *testing.T) {

		w := httptest.NewRecorder()
		// 发送无效的JSON数据
		req := httptest.NewRequest("GET", "/api/v1/search_condition/list?type=Mapping", nil)

		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := SearchCondition(c)
		assert.Nil(t, err)
	})
	t.Run("ComplianceMonitor Success", func(t *testing.T) {

		w := httptest.NewRecorder()
		// 发送无效的JSON数据
		req := httptest.NewRequest("GET", "/api/v1/search_condition/list?type=ComplianceRisk", nil)

		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := SearchCondition(c)
		assert.Nil(t, err)
	})
	t.Run("BusinessApp Success", func(t *testing.T) {

		w := httptest.NewRecorder()
		// 发送无效的JSON数据
		req := httptest.NewRequest("GET", "/api/v1/search_condition/list?type=BusinessApp", nil)

		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := SearchCondition(c)
		assert.Nil(t, err)
	})
}
