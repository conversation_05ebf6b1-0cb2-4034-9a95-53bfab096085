package deviceasset

import (
	"context"
	"strings"

	"fobrain/fobrain/app/controller/threat_center"
	device "fobrain/fobrain/app/repository/asset_center/device_asset"
	device_service "fobrain/fobrain/app/repository/device"
	"fobrain/fobrain/app/repository/threat"
	"fobrain/fobrain/app/request/asset_center"
	"fobrain/fobrain/app/services/export"
	"fobrain/fobrain/app/services/export/handlers"
	"fobrain/fobrain/common/auth"
	"fobrain/fobrain/common/request"
	"fobrain/fobrain/common/response"
	"fobrain/fobrain/common/validate"
	pb "fobrain/mergeService/proto"
	"fobrain/pkg/utils"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/olivere/elastic/v7"
)

var DeviceAssetIdValidate = request.Validate[asset_center.InternalAssetIdRequest]

type DeviceListRequest struct {
	request.PageRequest
	Keyword         string   `json:"keyword" form:"keyword" uri:"keyword" validate:"omitempty,max=100" zh:"模糊搜索"`
	Field           string   `json:"field" form:"field" uri:"field" validate:"" zh:"排序字段"`
	Order           string   `json:"order" form:"order" uri:"order" validate:"" zh:"排序方式"`
	SearchCondition []string `json:"search_condition" form:"search_condition" uri:"search_condition" validate:"" zh:"搜索条件"`
}

type DeviceIdsRequest struct {
	Ids                 []string `json:"ids" form:"ids" uri:"ids" validate:"" zh:"实体设备IDS"`
	Keyword             string   `json:"keyword" form:"keyword" uri:"keyword" validate:"omitempty,max=100" zh:"模糊搜索"`
	SourceIds           []uint64 `json:"source_ids" form:"source_ids" uri:"source_ids" zh:"数据源ids"`
	DeviceNames         []string `json:"device_names" form:"device_names" uri:"device_names" zh:"设备名称"`
	DeviceTypes         []string `json:"device_types" form:"device_types" uri:"device_types" zh:"设备类型"`
	Sn                  []string `json:"sn" form:"sn" uri:"sn" zh:"sn"`
	Mac                 []string `json:"mac" form:"mac" uri:"mac" zh:"mac"`
	Os                  []string `json:"os" form:"os" uri:"os" zh:"os"`
	MachineRoom         []string `json:"machine_room" form:"machine_room" uri:"machine_room" zh:"机房"`
	PrivateIp           []string `json:"private_ip" form:"private_ip" uri:"private_ip" zh:"内网IP"`
	PublicIp            []string `json:"public_ip" form:"public_ip" uri:"public_ip" zh:"互联网IP"`
	Area                []uint64 `json:"area" form:"area" uri:"area" zh:"所属区域"`
	Oper                []string `json:"oper" form:"oper" uri:"oper" zh:"运维负责人"`
	CreatedAt           []string `json:"created_at" form:"created_at" uri:"created_at" zh:"首次上报时间"`
	UpdatedAt           []string `json:"updated_at" form:"updated_at" uri:"updated_at" zh:"最后上报时间"`
	OperationTypeString string   `json:"operation_type_string" form:"operation_type_string" uri:"operation_type_string"  validate:"omitempty,oneof=in not_in == !== null not_null = !="  zh:"操作类型"`
	SearchCondition     []string `json:"search_condition" form:"search_condition" uri:"search_condition"  zh:"搜索条件"`
}

type DeviceDeleteRequest struct {
	Ids             []string `json:"ids" form:"ids" uri:"ids" validate:"" zh:"实体设备IDS"`
	Keyword         string   `json:"keyword" from:"keyword" uri:"keyword" validate:"omitempty,max=100"`
	SearchCondition []string `json:"search_condition" form:"search_condition" uri:"search_condition"  zh:"搜索条件"`
}

type DevicePocListRequest struct {
	DeviceId string `json:"device_id" form:"device_id" uri:"device_id" validate:"required,min=1" zh:"设备ID"`
	request.PageRequest
	Keyword string   `json:"keyword" form:"keyword" uri:"keyword" validate:"omitempty,max=100" zh:"模糊搜索"`
	Name    string   `json:"name" form:"name" uri:"name" validate:"omitempty,max=100" zh:"漏洞名称"`
	Ip      []string `json:"ip" form:"ip" uri:"ip" validate:"omitempty" zh:"IP"` // 多个 ip 用英文逗号分隔
	threat_center.CommonThreatRequest
}

type DevicePocExportRequest struct {
	DeviceId string   `json:"device_id" form:"device_id" uri:"device_id" validate:"required,min=1" zh:"设备ID"`
	Keyword  string   `json:"keyword" form:"keyword" uri:"keyword" validate:"omitempty,max=100" zh:"模糊搜索"`
	Name     string   `json:"name" form:"name" uri:"name" validate:"omitempty,max=100" zh:"漏洞名称"`
	Ip       []string `json:"ip" form:"ip" uri:"ip" validate:"omitempty" zh:"IP"` // 多个 ip 用英文逗号分隔
	threat_center.CommonThreatRequest
}

var DeviceIdsValidate = request.Validate[DeviceIdsRequest]
var DevicePocListValidate = request.Validate[DevicePocListRequest]
var DevicePocExportValidate = request.Validate[DevicePocExportRequest]

func fixDevice(devices []*device_service.DeviceDto) []*device_service.DeviceDto {
	if len(devices) > 0 {
		for _, d := range devices {
			d.Source = utils.IfNilToEmpty(d.Source)
			d.Area = utils.IfNilToEmpty(d.Area)
			d.PrivateIp = utils.IfNilToEmpty(d.PrivateIp)
			d.PublicIp = utils.IfNilToEmpty(d.PublicIp)
			d.Os = utils.IfNilToEmpty(d.Os)
			d.Hostname = utils.IfNilToEmpty(d.Hostname)
			d.Mac = utils.IfNilToEmpty(d.Mac)
			d.MachineRoom = utils.IfNilToEmpty(d.MachineRoom)
			d.Tags = utils.IfNilToEmpty(d.Tags)
			d.Business = utils.IfNilToEmpty(d.Business)
			d.OperInfo = utils.IfNilToEmpty(d.OperInfo)
		}
	}
	return devices
}

func List(c *gin.Context) error {
	param := &DeviceListRequest{}
	if err := c.ShouldBindQuery(param); err != nil {
		return response.FailWithMessage(c, "参数错误")
	}
	if ok, msg := validate.Validator(param); !ok {
		return response.FailWithMessage(c, msg)
	}
	staffIds, _ := c.Get("staff_ids")
	isSuperManage, _ := c.Get("is_super_manage")

	dataList, count, err := device_service.List(c, param.Page, param.PerPage, false, param.Keyword, isSuperManage.(bool), staffIds.([]string), param.SearchCondition, param.Field, param.Order)
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	dataList = fixDevice(dataList)

	return response.OkWithData(c, gin.H{"items": dataList, "total": count, "page": param.Page, "per_page": param.PerPage})
}

func Show(c *gin.Context) error {
	params, _ := DeviceAssetIdValidate(c, &asset_center.InternalAssetIdRequest{})

	data, err := device.Show(params.Id)
	if err != nil {
		return err
	}

	return response.OkWithData(c, data)
}

func RecycleBin(c *gin.Context) error {
	param := &DeviceListRequest{}
	if err := c.ShouldBind(param); err != nil {
		return response.FailWithMessage(c, "参数错误")
	}
	if ok, msg := validate.Validator(param); !ok {
		return response.FailWithMessage(c, msg)
	}
	staffIds, _ := c.Get("staff_ids")
	isSuperManage, _ := c.Get("is_super_manage")
	dataList, count, err := device_service.List(c, param.Page, param.PerPage, true, param.Keyword, isSuperManage.(bool), staffIds.([]string), param.SearchCondition, param.Field, param.Order)
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	dataList = fixDevice(dataList)

	return response.OkWithData(c, gin.H{"items": dataList, "total": count, "page": param.Page, "per_page": param.PerPage})
}

// 删除数据到回收站
func DeleteByIds(c *gin.Context) error {
	param := &DeviceDeleteRequest{}
	if err := c.ShouldBind(param); err != nil {
		return response.FailWithMessage(c, "参数错误")
	}
	if ok, msg := validate.Validator(param); !ok {
		return response.FailWithMessage(c, msg)
	}

	err := device_service.Delete(c, param.Ids, param.SearchCondition, param.Keyword)
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	return response.Ok(c)
}

// 删除回收站中的数据
func PurgeByIds(c *gin.Context) error {
	param := &DeviceDeleteRequest{}
	if err := c.ShouldBind(param); err != nil {
		return response.FailWithMessage(c, "参数错误")
	}
	if ok, msg := validate.Validator(param); !ok {
		return response.FailWithMessage(c, msg)
	}

	err := device_service.Purge(c, param.Ids, param.SearchCondition, param.Keyword)
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	return response.Ok(c)
}

// 设备列表导出
func Export(c *gin.Context) error {
	params, err := DeviceIdsValidate(c, &DeviceIdsRequest{})
	if err != nil {
		return err
	}

	// 转换为处理器需要的类型
	handlerParams := &handlers.DeviceIdsRequest{
		Ids:                 params.Ids,
		Keyword:             params.Keyword,
		SourceIds:           params.SourceIds,
		DeviceNames:         params.DeviceNames,
		DeviceTypes:         params.DeviceTypes,
		Sn:                  params.Sn,
		Mac:                 params.Mac,
		Os:                  params.Os,
		MachineRoom:         params.MachineRoom,
		PrivateIp:           params.PrivateIp,
		PublicIp:            params.PublicIp,
		Area:                params.Area,
		Oper:                params.Oper,
		CreatedAt:           params.CreatedAt,
		UpdatedAt:           params.UpdatedAt,
		OperationTypeString: params.OperationTypeString,
		SearchCondition:     params.SearchCondition,
	}

	// 使用新的导出框架
	handler := handlers.NewDeviceHandler()
	exportService := export.NewService().WithConcurrency(10)

	path, err := exportService.Export(c, handler, handlerParams)
	if err != nil {
		return err
	}

	return response.OkWithFile(c, path, true)
}

// 设备关联漏洞列表
func RelatedPocList(c *gin.Context) error {
	params, err := DevicePocListValidate(c, &DevicePocListRequest{})
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}

	// 获取设备信息
	device, err := device.GetById(params.DeviceId)
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}

	params.Ip = append(device.PrivateIp, device.PublicIp...)
	if len(params.Ip) == 0 {
		return response.FailWithMessage(c, "设备资产数据不存在")
	}

	// 获取当前用户信息
	user := request.GetUserInfo(c)

	paramList, err := utils.StructToMap(params.CommonThreatRequest, "json")
	if err != nil {
		return err
	}

	isSuper := auth.IsSuperManage(c)
	staffIdsAny, _ := c.Get("staff_ids")
	var areaList []uint64
	if len(device.Area) > 0 {
		for _, a := range device.Area {
			areaList = append(areaList, uint64(a))
		}
	}
	list, total, err := threat.List(c, areaList, params.Ip, params.Keyword, params.Page, params.PerPage, user, paramList, isSuper, staffIdsAny.([]string))
	if err != nil {
		return err
	}
	return response.OkWithPageData(c, total, params.Page, params.PerPage, list)
}

// 设备关联漏洞导出
func ExportRelatedPoc(c *gin.Context) error {
	params, err := DevicePocExportValidate(c, &DevicePocExportRequest{})
	if err != nil {
		return err
	}

	// 获取设备信息
	device, err := device.GetById(params.DeviceId)
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}

	params.Ip = append(device.PrivateIp, device.PublicIp...)
	if len(params.Ip) == 0 {
		return response.FailWithMessage(c, "设备资产数据不存在")
	}

	threatList, err := threat.GetByQuery(elastic.NewBoolQuery().Must(elastic.NewTermsQueryFromStrings("ip", params.Ip...)), []string{"id"})
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}

	ids := make([]string, 0, len(threatList))
	for _, v := range threatList {
		ids = append(ids, v.Id)
	}

	// 转换为处理器需要的类型
	handlerParams := &handlers.ThreatIdsRequest{
		Ids:     ids,
		Keyword: params.Keyword,
		CommonThreatRequest: handlers.CommonThreatRequest{
			SourceIds:           params.SourceIds,
			ThreatName:          params.ThreatName,
			Ips:                 params.Ip,  // 使用从设备提取的IP，不是用户请求的Ips
			Ports:               params.Ports,
			Area:                params.Area,
			ThreatUrl:           params.ThreatUrl,
			ThreatType:          params.ThreatType,
			ThreatLevel:         params.ThreatLevel,
			ThreatStatus:        params.ThreatStatus,
			Risk:                params.Risk,
			ThreatRepairPerson:  params.ThreatRepairPerson,
			RepairPriority:      params.RepairPriority,
			FirstTime:           params.FirstTime,
			FinallyTime:         params.FinallyTime,
			Field:               params.Field,
			Order:               params.Order,
			OperationTypeString: params.OperationTypeString,
			DataRange:           params.DataRange,
			SearchCondition:     params.SearchCondition,
		},
	}

	// 使用新的导出框架
	handler := handlers.NewPocHandler("device")
	exportService := export.NewService().WithConcurrency(10)

	path, err := exportService.Export(c, handler, handlerParams)
	if err != nil {
		return err
	}

	return response.OkWithFile(c, path, true)
}

// ManualCalibration 手动校准
func ManualCalibration(c *gin.Context) error {
	param := &pb.ManualCalibrationRequest{}
	err := c.ShouldBind(param)
	if err != nil {
		return err
	}
	param.BusinessType = "device"
	param.BatchNo = strings.ReplaceAll(uuid.New().String(), "-", "")
	if ok, msg := validate.Validator(param); !ok {
		return response.FailWithMessage(c, msg)
	}

	data, err := pb.GetProtoClient().ManualCalibration(context.Background(), param, pb.ClientWithAddress)
	if err != nil {
		// todo 零食返回OK，后面根据产品需求调整
		return response.OkWithData(c, map[string]interface{}{
			"success": true,
			"message": "",
		})
	}

	return response.OkWithData(c, data)
}

func DeviceBaseInfo(c *gin.Context) error {
	param := &pb.DeviceInfoRequest{}
	err := c.ShouldBind(param)
	if err != nil {
		return err
	}
	if ok, msg := validate.Validator(param); !ok {
		return response.FailWithMessage(c, msg)
	}

	data, err := pb.GetProtoClient().DeviceBaseInfo(context.Background(), param, pb.ClientWithAddress)
	if err != nil {
		return err
	}
	data.Sn = utils.IfNilToEmpty(data.Sn)
	data.Mac = utils.IfNilToEmpty(data.Mac)
	data.Hostname = utils.IfNilToEmpty(data.Hostname)
	data.Ip = utils.IfNilToEmpty(data.Ip)
	data.Os = utils.IfNilToEmpty(data.Os)
	data.Oper = utils.IfNilToEmpty(data.Oper)
	data.MachineRoom = utils.IfNilToEmpty(data.MachineRoom)
	data.Tags = utils.IfNilToEmpty(data.Tags)
	return response.OkWithData(c, data)
}

func DeviceRelatedIpInfo(c *gin.Context) error {
	param := &pb.DeviceInfoRequest{}
	err := c.ShouldBind(param)
	if err != nil {
		return err
	}
	if ok, msg := validate.Validator(param); !ok {
		return response.FailWithMessage(c, msg)
	}

	data, err := pb.GetProtoClient().DeviceRelatedIpInfo(context.Background(), param, pb.ClientWithAddress)
	if err != nil {
		return err
	}
	for _, v := range data.IpList {
		v.Assets = utils.IfNilToEmpty(v.Assets)
	}
	return response.OkWithData(c, data)
}

func DeviceRelatedEthInfo(c *gin.Context) error {
	param := &pb.DeviceInfoRequest{}
	err := c.ShouldBind(param)
	if err != nil {
		return err
	}
	if ok, msg := validate.Validator(param); !ok {
		return response.FailWithMessage(c, msg)
	}

	data, err := pb.GetProtoClient().DeviceRelatedEthInfo(context.Background(), param, pb.ClientWithAddress)
	if err != nil {
		return err
	}
	data.EthList = utils.IfNilToEmpty(data.EthList)
	for _, v := range data.EthList {
		v.DnsServer = utils.IfNilToEmpty(v.DnsServer)
	}
	return response.OkWithData(c, data)
}

func DeviceHostInfo(c *gin.Context) error {
	param := &pb.DeviceInfoRequest{}
	err := c.ShouldBind(param)
	if err != nil {
		return err
	}
	if ok, msg := validate.Validator(param); !ok {
		return response.FailWithMessage(c, msg)
	}

	data, err := pb.GetProtoClient().DeviceHostInfo(context.Background(), param, pb.ClientWithAddress)
	if err != nil {
		return err
	}

	data.Maker = utils.IfNilToEmpty(data.Maker)
	data.Model = utils.IfNilToEmpty(data.Model)
	data.Sn = utils.IfNilToEmpty(data.Sn)
	data.Os = utils.IfNilToEmpty(data.Os)
	data.OsKernel = utils.IfNilToEmpty(data.OsKernel)
	data.MemorySize = utils.IfNilToEmpty(data.MemorySize)
	data.MemoryUsageRate = utils.IfNilToEmpty(data.MemoryUsageRate)
	data.CpuMaker = utils.IfNilToEmpty(data.CpuMaker)
	data.CpuBrand = utils.IfNilToEmpty(data.CpuBrand)
	data.CpuCount = utils.IfNilToEmpty(data.CpuCount)
	data.LoadAverage = utils.IfNilToEmpty(data.LoadAverage)
	data.DiskCount = utils.IfNilToEmpty(data.DiskCount)
	data.DiskSize = utils.IfNilToEmpty(data.DiskSize)
	data.DiskUsageRate = utils.IfNilToEmpty(data.DiskUsageRate)

	return response.OkWithData(c, data)
}

func DeviceTraceabilityBaseInfo(c *gin.Context) error {
	param := &pb.DeviceInfoRequest{}
	err := c.ShouldBind(param)
	if err != nil {
		return err
	}
	if ok, msg := validate.Validator(param); !ok {
		return response.FailWithMessage(c, msg)
	}

	data, err := pb.GetProtoClient().DeviceTraceabilityBaseInfo(context.Background(), param, pb.ClientWithAddress)
	if err != nil {
		return err
	}

	data.Sn = utils.IfNilToEmpty(data.Sn)
	data.Mac = utils.IfNilToEmpty(data.Mac)
	data.Hostname = utils.IfNilToEmpty(data.Hostname)
	data.MergeKey = utils.IfNilToEmpty(data.MergeKey)

	return response.OkWithData(c, data)
}

func DeviceTraceabilityProcessInfo(c *gin.Context) error {
	param := &pb.TraceabilityProcessInfoRequest{}
	err := c.ShouldBind(param)
	if err != nil {
		return err
	}
	if ok, msg := validate.Validator(param); !ok {
		return response.FailWithMessage(c, msg)
	}

	data, err := pb.GetProtoClient().DeviceTraceabilityProcessInfo(context.Background(), param, pb.ClientWithAddress)
	if err != nil {
		return err
	}

	return response.OkWithData(c, data)
}

func DeviceTraceabilityDetailInfo(c *gin.Context) error {
	param := &pb.TraceabilityDetailInfoRequest{}
	err := c.ShouldBind(param)
	if err != nil {
		return err
	}
	if ok, msg := validate.Validator(param); !ok {
		return response.FailWithMessage(c, msg)
	}
	data, err := pb.GetProtoClient().DeviceTraceabilityDetailInfo(context.Background(), param, pb.ClientWithAddress)
	if err != nil {
		return err
	}
	// 格式处理
	for _, item := range data.Items {
		item.Strategies = utils.IfNilToEmpty(item.Strategies)
		for _, strategy := range item.Strategies {
			strategy.UntrustedSource = utils.IfNilToEmpty(strategy.UntrustedSource)
		}
	}
	return response.OkWithData(c, data)
}
