package deviceasset

import (
	device_service "fobrain/fobrain/app/repository/device"
	testcommon "fobrain/fobrain/tests/common_test"
	pb "fobrain/mergeService/proto"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"strings"

	"github.com/DATA-DOG/go-sqlmock"
	. "github.com/agiledragon/gomonkey/v2"
	"github.com/gin-gonic/gin"
	"github.com/olivere/elastic/v7"
	. "github.com/smartystreets/goconvey/convey"
	"github.com/stretchr/testify/assert"

	"testing"
)

func TestFixDevice(t *testing.T) {
	devices := []*device_service.DeviceDto{
		{
			Source:      nil,
			Area:        nil,
			PrivateIp:   []string{"127.0.0.1"},
			PublicIp:    []string{"127.0.0.1"},
			Os:          []string{"windows"},
			Hostname:    []string{"root"},
			Mac:         []string{"00:00:00:00:00:00"},
			MachineRoom: nil,
			OperInfo:    nil,
		},
	}
	_ = fixDevice(devices)
}

func TestList(t *testing.T) {
	Convey("TestList", t, func() {
		Convey("Success", func() {
			defer ApplyFuncReturn(device_service.List, nil, int64(0), nil).Reset()
			w := httptest.NewRecorder()
			req := httptest.NewRequest("GET", "/api/v1/device?page=1&per_page=10", nil)
			c, _ := gin.CreateTestContext(w)
			c.Request = req
			c.Set("staff_ids", []string{})
			c.Set("is_super_manage", true)

			err := List(c)
			assert.Nil(t, err)
		})

		Convey("Params error", func() {
			w := httptest.NewRecorder()
			req := httptest.NewRequest("GET", "/api/v1/device", nil)
			c, _ := gin.CreateTestContext(w)
			c.Request = req

			err := List(c)
			assert.Nil(t, err)
		})
	})
}

func TestRecycleBin(t *testing.T) {
	Convey("TestRecycleBin", t, func() {
		Convey("Success", func() {
			defer ApplyFuncReturn(device_service.List, nil, int64(0), nil).Reset()
			w := httptest.NewRecorder()
			req := httptest.NewRequest("GET", "/api/v1/device/recycle_bin", nil)
			c, _ := gin.CreateTestContext(w)
			c.Request = req
			c.Set("staff_ids", []string{})
			c.Set("is_super_manage", true)

			err := RecycleBin(c)
			assert.Nil(t, err)
		})
	})
}

func TestDeleteByIds(t *testing.T) {
	defer ApplyFuncReturn(device_service.Delete, nil).Reset()
	w := httptest.NewRecorder()
	req := httptest.NewRequest("DELETE", "/api/v1/device?page=1&per_page=10", nil)
	c, _ := gin.CreateTestContext(w)
	c.Request = req
	c.Set("staff_ids", []string{})
	c.Set("is_super_manage", true)

	err := DeleteByIds(c)
	assert.Nil(t, err)
}

func TestPurgeByIds(t *testing.T) {
	Convey("TestPurgeByIds", t, func() {
		Convey("Success", func() {
			defer ApplyFuncReturn(device_service.Purge, nil).Reset()
			w := httptest.NewRecorder()
			req := httptest.NewRequest("POST", "/api/v1/device/purge", nil)
			c, _ := gin.CreateTestContext(w)
			c.Request = req
			c.Set("staff_ids", []string{})
			c.Set("is_super_manage", true)

			err := PurgeByIds(c)
			assert.Nil(t, err)
		})
	})
}

func TestExport(t *testing.T) {
	Convey("TestExport", t, func() {
		Convey("Success", func() {
			// 创建 mock ES 服务器
			mockServer := testcommon.NewMockServer()
			defer mockServer.Close()

			// 用于跟踪搜索调用次数
			var searchCallCount int

			// mock ES 数据计数 - 确保有数据
			mockServer.Register("/device/_count", map[string]interface{}{
				"count": 1,
				"_shards": map[string]interface{}{
					"total":      1,
					"successful": 1,
					"skipped":    0,
					"failed":     0,
				},
			})

			// 注册自定义搜索处理器，正确处理 search_after 分页查询
			mockServer.RegisterHandler("/device/_search", func(w http.ResponseWriter, r *http.Request) {
				searchCallCount++
				w.Header().Set("Content-Type", "application/json")

				if searchCallCount == 1 {
					// 第一次查询，返回有数据的响应
					response := elastic.SearchResult{
						Hits: &elastic.SearchHits{
							TotalHits: &elastic.TotalHits{
								Value:    1,
								Relation: "eq",
							},
							Hits: []*elastic.SearchHit{
								{
									Index: "device",
									Id:    "1",
									Source: []byte(`{
										"id": "1",
										"fid": "device-001",
										"hostname": ["测试设备"],
										"sn": ["SN001"],
										"mac": ["00:11:22:33:44:55"],
										"tags": ["服务器"],
										"poc_num": 0,
										"os": ["Linux"],
										"machine_room": ["机房A"],
										"private_ip": ["***********"],
										"public_ip": ["*******"],
										"area": [1],
										"business": [],
										"opers": [],
										"all_source_ids": [1],
										"custom_fields": {},
										"created_at": "2024-01-01 10:00:00",
										"updated_at": "2024-01-01 10:00:00"
									}`),
									Sort: []interface{}{"2024-01-01 10:00:00", "1"},
								},
							},
						},
					}
					json.NewEncoder(w).Encode(response)
				} else {
					// 后续查询，返回空结果以结束分页
					response := elastic.SearchResult{
						Hits: &elastic.SearchHits{
							TotalHits: &elastic.TotalHits{
								Value:    0,
								Relation: "eq",
							},
							Hits: []*elastic.SearchHit{},
						},
					}
					json.NewEncoder(w).Encode(response)
				}
			})

			// mock 数据库
			mockDb := testcommon.GetMysqlMock()
			defer mockDb.Close()

			// mock 数据源查询 - 使用 testcommon 的 ExpectQuery 方法
			mockDb.ExpectQuery("SELECT * FROM `data_sources`").
				WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).AddRow(1, "测试数据源"))

			// mock 自定义字段查询
			mockDb.ExpectQuery("SELECT * FROM `custom_field_meta` WHERE (module_type = ? and deleted_at is null) AND `custom_field_meta`.`deleted_at` IS NULL").
				WithArgs("设备资产").WillReturnRows(sqlmock.NewRows([]string{"id", "field_key", "display_name"}).AddRow(1, "custom_key", "自定义字段"))

			// mock 网络区域查询
			mockDb.ExpectQuery("SELECT * FROM `network_areas` WHERE `network_areas`.`deleted_at` IS NULL").
				WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).AddRow(1, "测试区域"))

			w := httptest.NewRecorder()
			req := httptest.NewRequest("GET", "/api/v1/device/export", nil)
			c, _ := gin.CreateTestContext(w)
			c.Request = req
			c.Set("staff_ids", []string{})
			c.Set("is_super_manage", true)

			err := Export(c)
			assert.Nil(t, err)
		})
	})
}

func TestManualCalibration(t *testing.T) {
	Convey("TestManualCalibration", t, func() {
		Convey("Success", func() {
			defer ApplyMethodReturn(pb.GetProtoClient(), "ManualCalibration", &pb.ManualCalibrationResponse{}, nil).Reset()
			w := httptest.NewRecorder()
			req := httptest.NewRequest("POST", "/api/v1/device/manual_calibration", nil)
			c, _ := gin.CreateTestContext(w)
			c.Request = req

			err := ManualCalibration(c)
			assert.Nil(t, err)
		})
	})
}

func TestDeviceBaseInfo(t *testing.T) {
	Convey("TestDeviceBaseInfo", t, func() {
		Convey("Success", func() {
			defer ApplyMethodReturn(pb.GetProtoClient(), "DeviceBaseInfo", &pb.DeviceBaseInfoResponse{}, nil).Reset()
			w := httptest.NewRecorder()
			req := httptest.NewRequest("POST", "/api/v1/device/device_base_info", strings.NewReader(`{
				"id": "1"
			}`))
			req.Header.Set("Content-Type", "application/json")
			c, _ := gin.CreateTestContext(w)
			c.Request = req

			err := DeviceBaseInfo(c)
			assert.Nil(t, err)
		})
	})
}

func TestDeviceRelatedIpInfo(t *testing.T) {
	Convey("TestDeviceRelatedIpInfo", t, func() {
		Convey("Success", func() {
			defer ApplyMethodReturn(pb.GetProtoClient(), "DeviceRelatedIpInfo", &pb.DeviceRelatedIpInfoResponse{}, nil).Reset()
			w := httptest.NewRecorder()
			req := httptest.NewRequest("POST", "/api/v1/device/device_related_ip_info", strings.NewReader(`{
				"id": "1"
			}`))
			req.Header.Set("Content-Type", "application/json")
			c, _ := gin.CreateTestContext(w)
			c.Request = req

			err := DeviceRelatedIpInfo(c)
			assert.Nil(t, err)
		})
	})
}

func TestDeviceHostInfo(t *testing.T) {
	Convey("TestDeviceHostInfo", t, func() {
		Convey("Success", func() {
			defer ApplyMethodReturn(pb.GetProtoClient(), "DeviceHostInfo", &pb.HostInfo{}, nil).Reset()
			w := httptest.NewRecorder()
			req := httptest.NewRequest("POST", "/api/v1/device/device_host_info", strings.NewReader(`{
				"id": "1"
			}`))
			req.Header.Set("Content-Type", "application/json")
			c, _ := gin.CreateTestContext(w)
			c.Request = req

			err := DeviceHostInfo(c)
			assert.Nil(t, err)
		})
	})
}

func TestDeviceTraceabilityBaseInfo(t *testing.T) {
	Convey("TestDeviceTraceabilityBaseInfo", t, func() {
		Convey("Success", func() {
			defer ApplyMethodReturn(pb.GetProtoClient(), "DeviceTraceabilityBaseInfo", &pb.DeviceTraceabilityBaseInfoResponse{}, nil).Reset()
			w := httptest.NewRecorder()
			req := httptest.NewRequest("POST", "/api/v1/device/device_traceability_base_info", strings.NewReader(`{
				"id": "1"
			}`))
			req.Header.Set("Content-Type", "application/json")
			c, _ := gin.CreateTestContext(w)
			c.Request = req

			err := DeviceTraceabilityBaseInfo(c)
			assert.Nil(t, err)
		})
	})
}

func TestDeviceTraceabilityProcessInfo(t *testing.T) {
	Convey("TestDeviceTraceabilityProcessInfo", t, func() {
		defer ApplyMethodReturn(pb.GetProtoClient(), "DeviceTraceabilityProcessInfo", &pb.TraceabilityProcessInfoResponse{}, nil).Reset()
		w := httptest.NewRecorder()
		req := httptest.NewRequest("POST", "/api/v1/device/device_traceability_process_info", strings.NewReader(`{
				"id": "1",
				"page": 1,
				"per_page": 10
			}`))
		req.Header.Set("Content-Type", "application/json")
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := DeviceTraceabilityProcessInfo(c)
		assert.Nil(t, err)
	})
}

func TestDeviceTraceabilityDetailInfo(t *testing.T) {
	Convey("TestDeviceTraceabilityDetailInfo", t, func() {
		Convey("Success", func() {
			defer ApplyMethodReturn(pb.GetProtoClient(), "DeviceTraceabilityDetailInfo", &pb.DeviceTraceabilityDetailInfoResponse{}, nil).Reset()
			w := httptest.NewRecorder()
			req := httptest.NewRequest("POST", "/api/v1/device/device_traceability_process_info", strings.NewReader(`{
				"id": "1",
				"process_info": [
					{
						"id": "1"
					}
				]
			}`))
			req.Header.Set("Content-Type", "application/json")
			c, _ := gin.CreateTestContext(w)
			c.Request = req

			err := DeviceTraceabilityDetailInfo(c)
			assert.Nil(t, err)
		})
	})
}
