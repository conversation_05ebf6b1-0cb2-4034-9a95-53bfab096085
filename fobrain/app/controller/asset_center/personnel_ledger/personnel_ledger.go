package personnel_ledger

import (
	"context"
	"strings"

	"fobrain/fobrain/app/repository/department"
	"fobrain/fobrain/app/repository/staff"
	"fobrain/fobrain/common/request"
	"fobrain/fobrain/common/response"
	"fobrain/fobrain/common/validate"
	pb "fobrain/mergeService/proto"
	"fobrain/pkg/utils"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// 人员台账相关接口

type (
	PersonnelLedgerRequest struct {
		request.PageRequest
		Keyword             string   `json:"keyword" form:"keyword" uri:"keyword" validate:"omitempty,max=100" zh:"模糊搜索"`
		Id                  string   `json:"id" form:"id" uri:"id" validate:"omitempty" zh:"列表 ID"`
		Name                []string `json:"name" form:"name" uri:"name" validate:"omitempty,max=100" zh:"姓名"`
		EnName              []string `json:"en_name" form:"en_name" uri:"nen_nameame" zh:"英文姓名"`
		SourceIds           []uint64 `json:"source_ids" form:"source_ids" uri:"source_ids" zh:"数据源id"`
		Title               []string `json:"title" form:"title" uri:"title" zh:"职位"`
		Mobile              []string `json:"mobile" form:"mobile" uri:"mobile" zh:"电话"`
		Email               []string `json:"email" form:"email" uri:"email" zh:"邮箱"`
		Department          []string `json:"department" form:"department" uri:"department" zh:"部门"`
		Status              []int    `json:"status" form:"status" uri:"status" zh:"状态 1：在职；2：离职"`
		OperationTypeString string   `json:"operation_type_string" form:"operation_type_string" uri:"operation_type_string" validate:"omitempty,oneof=in not_in == !== null not_null = !="  zh:"操作类型"`
		Field               string   `json:"field" form:"field" uri:"field" zh:"排序字段 created_at updated_at"`
		Order               string   `json:"order" form:"order" uri:"order" zh:"排序 ascend->true 正排序，descend->false 倒排序"`
		SearchCondition     []string `json:"search_condition" form:"search_condition" uri:"search_condition"  zh:"搜索条件"`
		IncludeIds          []string `json:"include_ids" form:"include_ids" uri:"include_ids" zh:"包含的ID"` // 不受搜索条件限制，返回结果中必须包含这些数据，除非查不到，主要为前端回显使用
	}

	PersonnelLedgerDeleteByIDRequest struct {
		Ids             []string `json:"ids" form:"ids" uri:"ids" zh:"人员台账ID"`
		Keyword         string   `json:"keyword" form:"keyword" uri:"keyword" validate:"omitempty,max=100" zh:"模糊搜索"`
		SearchCondition []string `json:"search_condition" form:"search_condition" uri:"search_condition"  zh:"搜索条件"`
	}

	PersonnelLedgerOneRequest struct {
		Id   string `json:"id" form:"id" uri:"id" validate:"required,min=1" zh:"部门ID"`
		Name string `json:"name" form:"name" uri:"name" validate:"omitempty,max=100" zh:"部门名称"`
	}
)

var LedgerValidate = request.Validate[PersonnelLedgerRequest]

var LedgerByIdsValidate = request.Validate[PersonnelLedgerDeleteByIDRequest]

var LedgerIdValidate = request.Validate[PersonnelLedgerOneRequest]

// List
// 人员台账列表
func List(c *gin.Context) error {
	params, err := LedgerValidate(c, &PersonnelLedgerRequest{})
	if err != nil {
		return err
	}
	paramList, err := utils.StructToMap(params, "json")
	if err != nil {
		return err
	}
	staffIds, _ := c.Get("staff_ids")
	isSuperManage, _ := c.Get("is_super_manage")
	list, total, err := staff.List(params.Keyword, params.Page, params.PerPage, paramList, isSuperManage.(bool), staffIds.([]string), params.IncludeIds)
	if err != nil {
		return err
	}
	return response.OkWithPageData(c, total, params.Page, params.PerPage, list)
}

// DeleteByIds
// 人员台账删除接口
func DeleteByIds(c *gin.Context) error {
	params, _ := LedgerByIdsValidate(c, &PersonnelLedgerDeleteByIDRequest{})

	if err := staff.DeleteByIds(params.Ids, params.Keyword, params.SearchCondition); err != nil {
		return err
	}

	return response.OkWithMessage(c, "删除成功")
}

func DepartmentList(c *gin.Context) error {
	params, err := LedgerValidate(c, &PersonnelLedgerRequest{})
	if err != nil {
		return err
	}

	list, total, err := department.List(params.Keyword, params.Page, params.PerPage)
	if err != nil {
		return err
	}

	return response.OkWithPageData(c, total, params.Page, params.PerPage, list)
}

func DepartmentUpdate(c *gin.Context) error {
	params, err := LedgerIdValidate(c, &PersonnelLedgerOneRequest{})
	if err != nil {
		return err
	}

	if err := department.UpdateById(params.Name, params.Id); err != nil {
		return err
	}

	return response.OkWithMessage(c, "更新成功")
}

func DepartmentDelete(c *gin.Context) error {
	params, _ := LedgerByIdsValidate(c, &PersonnelLedgerDeleteByIDRequest{})

	if err := department.DeleteByIds(params.Ids); err != nil {
		return err
	}

	return response.OkWithMessage(c, "删除成功")
}

// ManualCalibration 手动校准
func ManualCalibration(c *gin.Context) error {
	param := &pb.ManualCalibrationRequest{}
	err := c.ShouldBind(param)
	if err != nil {
		return err
	}
	param.BusinessType = "person"
	param.BatchNo = strings.ReplaceAll(uuid.New().String(), "-", "")
	if ok, msg := validate.Validator(param); !ok {
		return response.FailWithMessage(c, msg)
	}

	data, err := pb.GetProtoClient().ManualCalibration(context.Background(), param, pb.ClientWithAddress)
	if err != nil {
		// todo 零食返回OK，后面根据产品需求调整
		return response.OkWithData(c, map[string]interface{}{
			"success": true,
			"message": "",
		})
	}

	return response.OkWithData(c, data)
}

// Position 人员台账职位
func Position(c *gin.Context) error {
	list, err := staff.Position(c)
	if err != nil {
		return err
	}
	return response.OkWithData(c, list)
}
