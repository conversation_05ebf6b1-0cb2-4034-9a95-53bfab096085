package personnel_ledger

import (
	"encoding/json"
	"fmt"
	"net/http/httptest"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"

	testcommon "fobrain/fobrain/tests/common_test"

	"github.com/gin-gonic/gin"
	"github.com/olivere/elastic/v7"
	"github.com/stretchr/testify/assert"
)

func TestList(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()
	mockDb.ExpectQuery("SELECT count(*) FROM `users_staffs` WHERE staff_id = ?").
		WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))

	mockDb.ExpectQuery("SELECT * FROM `data_sources` WHERE id in (?)").
		WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))

	t.Run("es query err", func(t *testing.T) {
		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/v1/personnel_ledger?page=1&per_page=10", nil)
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req
		c.Set("staff_ids", []string{})
		c.Set("is_super_manage", true)
		err := List(c)
		assert.NotNil(t, err)
	})

	mockServer.Register("staff/_search", []*elastic.SearchHit{
		{
			Id:     "1",
			Source: []byte(`{"name":"example"}`),
		},
	})

	mockServer.Register("staff/_count", &elastic.CountResponse{
		Count: 1,
	})

	t.Run("PersonnelLedgerRequest err", func(t *testing.T) {
		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/v1/personnel_ledger?page=1", nil)
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req
		c.Set("staff_ids", []string{})
		c.Set("is_super_manage", true)
		err := List(c)
		assert.Equal(t, "条数为必填字段", err.Error())
	})

	// 创建一个测试请求和响应
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/personnel_ledger?page=1&per_page=10", nil)
	// 创建一个Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req
	c.Set("staff_ids", []string{})
	c.Set("is_super_manage", true)
	// 调用函数
	err := List(c)

	// 断言错误为 nil
	assert.Empty(t, err)
}

func TestDeleteByIds(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	mockServer.Register("/staff/_search", []*elastic.SearchHit{
		{
			Id:     "1",
			Source: []byte(`{"name":"example"}`),
		},
	})
	mockServer.RegisterEmptyScrollHandler()

	mockServer.Register("/process_staff/_delete_by_query", &elastic.BulkIndexByScrollResponse{
		Deleted: 1,
	})
	mockServer.Register("/staff_record/_delete_by_query", &elastic.BulkIndexByScrollResponse{
		Deleted: 1,
	})
	mockServer.Register("/staff_merge_record/_delete_by_query", &elastic.BulkIndexByScrollResponse{
		Deleted: 1,
	})

	t.Run("es delete err", func(t *testing.T) {
		// 创建一个测试请求和响应
		w := httptest.NewRecorder()
		req := httptest.NewRequest("DELETE", "/api/v1/personnel_ledger?ids=1&ids=2", nil)
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := DeleteByIds(c)
		assert.Error(t, err)
	})

	mockServer.Register("/staff/_delete_by_query", &elastic.BulkIndexByScrollResponse{
		Deleted: 1,
	})

	// 创建一个测试请求和响应
	w := httptest.NewRecorder()
	req := httptest.NewRequest("DELETE", "/api/v1/personnel_ledger?ids=1&ids=2", nil)
	// 创建一个Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	err := DeleteByIds(c)
	fmt.Println(err, "-=-=-=-")
	assert.Empty(t, err)
}

func TestDepartmentList(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	t.Run("es query err", func(t *testing.T) {
		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/v1/personnel_ledger/department_list?page=1&per_page=10", nil)

		c, _ := gin.CreateTestContext(w)
		c.Request = req
		err := DepartmentList(c)
		assert.NotNil(t, err)
	})

	mockServer.Register("department/_search", []*elastic.SearchHit{
		{
			Id:     "1",
			Source: []byte(`{"name":"example"}`),
		},
	})

	t.Run("PersonnelLedgerRequest err", func(t *testing.T) {
		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/v1/personnel_ledger/department_list?page=1", nil)

		c, _ := gin.CreateTestContext(w)
		c.Request = req
		err := DepartmentList(c)
		assert.Equal(t, "条数为必填字段", err.Error())
	})

	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/personnel_ledger/department_list?page=1&per_page=10&keyword=example", nil)
	c, _ := gin.CreateTestContext(w)
	c.Request = req
	err := DepartmentList(c)
	assert.Empty(t, err)
}

func TestDepartmentUpdate(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()
	t.Run("es query err", func(t *testing.T) {
		w := httptest.NewRecorder()
		req := httptest.NewRequest("PUT", "/api/v1/personnel_ledger/department_list", nil)

		c, _ := gin.CreateTestContext(w)
		c.Request = req
		err := DepartmentUpdate(c)
		assert.NotNil(t, err)
	})

	mockServer.Register("/department/_search", []*elastic.SearchHit{
		{
			Id:     "1",
			Source: []byte(`{"name":"example"}`),
		},
	})

	t.Run("update err", func(t *testing.T) {
		mockServer.Register("/_bulk?refresh=true", &elastic.BulkIndexByScrollResponse{
			Updated: 1,
		})

		w := httptest.NewRecorder()
		req := httptest.NewRequest("PUT", "/api/v1/personnel_ledger/department_list?id=2", nil)

		c, _ := gin.CreateTestContext(w)
		c.Request = req
		err := DepartmentUpdate(c)
		assert.NotNil(t, err)
	})

	t.Run("success", func(t *testing.T) {
		mockServer.RegisterBulk()

		w := httptest.NewRecorder()
		req := httptest.NewRequest("PUT", "/api/v1/personnel_ledger/department_list?id=2", nil)

		c, _ := gin.CreateTestContext(w)
		c.Request = req
		err := DepartmentUpdate(c)
		assert.Empty(t, err)
		assert.Equal(t, "{\"code\":0,\"message\":\"更新成功\",\"data\":{}}", w.Body.String())
	})
}

func TestDepartmentDelete(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()
	t.Run("es query err", func(t *testing.T) {
		w := httptest.NewRecorder()
		req := httptest.NewRequest("DELETE", "/api/v1/personnel_ledger/department_list", nil)
		c, _ := gin.CreateTestContext(w)
		c.Request = req
		err := DepartmentDelete(c)
		assert.NotNil(t, err)
	})

	t.Run("all delete success", func(t *testing.T) {
		mockServer.Register("/department/_delete_by_query", &elastic.BulkIndexByScrollResponse{
			Deleted: 1,
		})

		w := httptest.NewRecorder()
		req := httptest.NewRequest("DELETE", "/api/v1/personnel_ledger/department_list", nil)
		c, _ := gin.CreateTestContext(w)
		c.Request = req
		err := DepartmentDelete(c)
		assert.Empty(t, err)
	})

	t.Run("all delete success", func(t *testing.T) {
		mockServer.Register("/department/_delete_by_query", &elastic.BulkIndexByScrollResponse{
			Deleted: 1,
		})

		w := httptest.NewRecorder()
		req := httptest.NewRequest("DELETE", "/api/v1/personnel_ledger/department_list?ids=2&ids=1", nil)
		c, _ := gin.CreateTestContext(w)
		c.Request = req
		err := DepartmentDelete(c)
		assert.Empty(t, err)
	})
}

func TestPosition(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	mockServer.Register("staff/_search", elastic.SearchResult{
		Aggregations: elastic.Aggregations{
			"title": json.RawMessage(`{
			"doc_count_error_upper_bound": 0,
			"sum_other_doc_count": 0,
			"buckets": [{"key":"销售副总裁","doc_count":2},{"key":"CEO","doc_count":1},{"key":"事业部副总经理","doc_count":1},{"key":"事业部总经理","doc_count":1},{"key":"产品服务中心总经理","doc_count":1},{"key":"人力行政中心总经理","doc_count":1},{"key":"企业产品中心总经理","doc_count":1},{"key":"公共事务总监","doc_count":1},{"key":"北区售前支持部负责人","doc_count":1},{"key":"售前支持中心总经理","doc_count":1},{"key":"总裁","doc_count":1},{"key":"桌面运维工程师","doc_count":1},{"key":"监管产品中心总经理","doc_count":1},{"key":"行政副总裁","doc_count":1},{"key":"财法支持中心总经理","doc_count":1},{"key":"销售中心副总经理","doc_count":1},{"key":"销售总监","doc_count":1},{"key":"高级副总裁","doc_count":1}]
		}`),
		},
	})

	// 创建一个测试请求和响应
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/personnel_ledger/position", nil)
	// 创建一个Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 调用函数
	err := Position(c)

	// 断言错误为 nil
	assert.Empty(t, err)
}
