package external_ip_asset

import (
	"fobrain/fobrain/app/repository/asset"
	"fobrain/fobrain/app/request/asset_center"
	assetcenter "fobrain/fobrain/app/services/asset_center"
	"fobrain/fobrain/app/services/export"
	"fobrain/fobrain/app/services/export/handlers"
	"fobrain/fobrain/app/services/net_mapping"
	"fobrain/fobrain/common/request"
	"fobrain/fobrain/common/response"
	"fobrain/initialize/mysql"
	"fobrain/models/mysql/merge"

	"github.com/gin-gonic/gin"
)

type (
	ExternalIpAssetRequest struct {
		request.PageRequest
		Keyword string `json:"keyword" form:"keyword" uri:"keyword" validate:"omitempty,max=100" zh:"模糊搜索"`
		Name    string `json:"name" form:"name" uri:"name" validate:"omitempty,max=100" zh:"姓名"`
		Id      string `json:"id" form:"id" uri:"id" validate:"omitempty,min=1" zh:"列表 ID"`
	}

	ExternalIpAssetIdRequest struct {
		Id string `json:"id" form:"id" uri:"id" validate:"omitempty,min=1" zh:"列表 ID"`
	}

	ExternalIpAssetIdsRequest struct {
		Ids             []string `json:"ids" form:"ids" uri:"ids" zh:"IDS"`
		Keyword         string   `json:"keyword" form:"keyword" uri:"keyword" validate:"omitempty,max=100" zh:"模糊搜索"`
		SearchCondition []string `json:"search_condition" form:"search_condition" uri:"search_condition"  zh:"搜索条件"`
	}
)

var ExternalIpAssetValidate = request.Validate[asset_center.InternalAssetRequest]
var ExternalIpAssetIdValidate = request.Validate[ExternalIpAssetIdRequest]
var ExternalIpAssetIdsValidate = request.Validate[ExternalIpAssetIdsRequest]

// List
// 外网资产清单
func List(c *gin.Context) error {
	params, err := ExternalIpAssetValidate(c, &asset_center.InternalAssetRequest{})
	if err != nil {
		return err
	}

	staffIds, _ := c.Get("staff_ids")
	isSuperManage, _ := c.Get("is_super_manage")

	list, total, err := asset.List(c, params.Keyword, params.Page, params.PerPage, asset.NetworkTypeExternal, params, isSuperManage.(bool), staffIds.([]string), nil, 1)
	if err != nil {
		return err
	}

	return response.OkWithPageData(c, total, params.Page, params.PerPage, list)
}

func DeleteByIds(c *gin.Context) error {
	params, _ := ExternalIpAssetIdsValidate(c, &ExternalIpAssetIdsRequest{})

	isType := 0
	if len(params.Ids) == 0 {
		isType = 2
	}

	// 删除资产
	err := asset.DeleteByIds(c, params.Ids, params.Keyword, asset.NotIsRecycleBin, isType, params.SearchCondition)
	if err != nil {
		return err
	}

	err = net_mapping.CheckAlarm()
	if err != nil {
		return err
	}

	return response.OkWithMessage(c, "删除成功")
}

// Reduction
// @description 将进入回收站的资产还原, 去除软删除的标识
func Reduction(c *gin.Context) error {
	count, err := merge.NewMergeRecordsModel().Count(mysql.WithColumnValue("task_status", merge.MergeRecordsStatusRunning))
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	if count > 0 {
		return response.FailWithMessage(c, "存在融合任务, 请稍后删除")
	}
	params, _ := ExternalIpAssetIdsValidate(c, &ExternalIpAssetIdsRequest{})

	err = asset.Reduction(c, params.Ids, params.Keyword, asset.NetworkTypeExternal)
	if err != nil {
		return err
	}

	return response.OkWithMessage(c, "还原成功")
}

// PurgeByIds
// @description 将资产彻底删除
func PurgeByIds(c *gin.Context) error {
	params, _ := ExternalIpAssetIdsValidate(c, &ExternalIpAssetIdsRequest{})
	err := assetcenter.PurgeAsset(c, params.Ids, asset.NetworkTypeExternal, params.Keyword, params.SearchCondition)
	if err != nil {
		return err
	}

	return response.OkWithMessage(c, "删除成功")
}

func Show(c *gin.Context) error {
	params, _ := ExternalIpAssetIdValidate(c, &ExternalIpAssetIdRequest{})

	data, err := asset.Show(c, params.Id)
	if err != nil {
		return err
	}

	return response.OkWithData(c, data)
}

func RecycleBin(c *gin.Context) error {
	params, err := ExternalIpAssetValidate(c, &asset_center.InternalAssetRequest{})
	if err != nil {
		return err
	}
	staffIds, _ := c.Get("staff_ids")
	isSuperManage, _ := c.Get("is_super_manage")
	list, total, err := asset.RecycleBin(c, params.Keyword, params.Page, params.PerPage, asset.NetworkTypeExternal, isSuperManage.(bool), staffIds.([]string))
	if err != nil {
		return err
	}

	return response.OkWithPageData(c, total, params.Page, params.PerPage, list)
}

func Export(c *gin.Context) error {
	internalAssetRequest := &asset_center.InternalAssetRequest{
		PageRequest: request.PageRequest{
			Page:    1,
			PerPage: 10,
		},
	}
	params, err := ExternalIpAssetValidate(c, internalAssetRequest)
	if err != nil {
		return err
	}

	// 使用新的导出框架
	handler := handlers.NewAssetHandler(asset.NetworkTypeExternal, asset.NotIsRecycleBin)
	exportService := export.NewService().WithConcurrency(10)

	path, err := exportService.Export(c, handler, params)
	if err != nil {
		return err
	}

	return response.OkWithFile(c, path, true)
}

func RecycleBinExport(c *gin.Context) error {
	internalAssetRequest := &asset_center.InternalAssetRequest{
		PageRequest: request.PageRequest{
			Page:    1,
			PerPage: 10,
		},
	}
	params, err := ExternalIpAssetValidate(c, internalAssetRequest)
	if err != nil {
		return err
	}

	// 使用新的导出框架
	handler := handlers.NewAssetHandler(asset.NetworkTypeExternal, asset.IsRecycleBin)
	exportService := export.NewService().WithConcurrency(10)

	path, err := exportService.Export(c, handler, params)
	if err != nil {
		return err
	}

	return response.OkWithFile(c, path, true)
}
