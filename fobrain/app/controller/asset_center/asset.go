package asset_center

import (
	"fmt"

	"github.com/gin-gonic/gin"
	"github.com/olivere/elastic/v7"

	"fobrain/fobrain/app/repository/asset"
	"fobrain/fobrain/app/request/asset_center"
	"fobrain/fobrain/common/request"
	"fobrain/fobrain/common/response"
	filtrate "fobrain/models/elastic"
)

var InternalAssetValidate = request.Validate[asset_center.InternalAssetRequest]

// List
// 内网资产清单
// 支持通过SelectFields参数指定需要返回的字段列表，如果不指定则返回所有字段
func List(c *gin.Context) error {
	params, err := InternalAssetValidate(c, &asset_center.InternalAssetRequest{})
	if err != nil {
		return err
	}

	staffIds, _ := c.Get("staff_ids")
	isSuperManage, _ := c.Get("is_super_manage")

	list, total, err := asset.List(c, params.Keyword, params.Page, params.PerPage, params.NetworkType, params, isSuperManage.(bool), staffIds.([]string), params.MustAttr, params.Recycle)
	if err != nil {
		return err
	}

	return response.OkWithPageData(c, total, params.Page, params.PerPage, list)
}

func IpLikeList(c *gin.Context) error {
	params, err := request.Validate(c, &asset_center.IpKeywordRequest{})
	if err != nil {
		return err
	}

	list, err := asset.AssetsKeyword(params)
	if err != nil {
		return err
	}
	return response.OkWithData(c, list)
}

func IpRuleInfos(c *gin.Context) error {
	params, err := request.Validate(c, &asset_center.AssetRuleInfosRequest{})
	if err != nil {
		return err
	}
	boolQuery := elastic.NewBoolQuery()
	if params != nil && len(params.SearchCondition) > 0 {
		conditions, err := filtrate.ParseQueryConditions(params.SearchCondition)
		if err != nil {
			return fmt.Errorf("解析查询条件失败,%v", err)
		}

		for _, condition := range conditions {
			boolQuery = filtrate.BuildBoolQuery(condition.Field, condition.OperationTypeString, condition.LogicalConnective, condition.Value, boolQuery)
		}
	}
	list, err := asset.AssetTop("rule_infos."+params.Field, 100, "rule_infos", params.Keyword, boolQuery)
	if err != nil {
		return err
	}
	var res []map[string]string
	for _, rule := range list {
		res = append(res, map[string]string{
			"label": rule.FieldValue,
			"value": rule.FieldValue,
		})
	}
	return response.OkWithData(c, res)
}
