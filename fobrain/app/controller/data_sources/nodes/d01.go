package nodes

import (
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"math/rand"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"

	"fobrain/fobrain/app/services/node/d01"
	"fobrain/fobrain/app/services/node/logs"
	"fobrain/fobrain/common/request"
	"fobrain/fobrain/common/response"
	"fobrain/initialize/mysql"
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/workbench"
	"fobrain/pkg/utils"
)

// CheckD01Status 检测节点状态
func CheckD01Status(c *gin.Context) error {
	params, err := request.Validate(c, &struct {
		Id uint64 `json:"id" uri:"id" form:"id" validate:"required" zh:"节点ID"`
	}{})

	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}

	node, err := data_source.NewNodeModel().First(mysql.WithColumnValue("id", params.Id))
	if err != nil {
		return err
	}

	cli := d01.NewD01()
	if err = cli.SetNode(node.Id); err != nil {
		return err
	}

	_, err = cli.CheckedNodeStatus()
	if err != nil {
		node.Status = data_source.StatusNONormal
		workbench.ProbeAbnormalSend(fmt.Sprintf("D01 %d探针异常", params.Id))
	} else {
		node.Status = data_source.StatusNormal
	}

	err = data_source.NewNodeModel().Update(*node)
	if err != nil {
		return err
	}

	return response.Ok(c)
}

// UpdateD01 更新节点数据
func UpdateD01(ctx *gin.Context) error {
	params, err := request.Validate(ctx, &struct {
		Id          uint64 `json:"id" uri:"id" form:"id" validate:"required,number" zh:"节点ID"`
		Name        string `json:"name" uri:"name" form:"name" validate:"required,omitempty" zh:"节点名称"`
		RepeatType  string `json:"repeat_type" uri:"repeat_type" form:"repeat_type" validate:"required" zh:"数据同步配置"`
		Date        string `json:"date" uri:"date" form:"date" zh:"数据同步配置"`
		StartTime   string `json:"start_time" uri:"start_time" form:"start_time" validate:"required" zh:"数据同步配置"`
		IP          string `json:"ip" uri:"ip" form:"types"  ip:"required,ipv4" zh:"节点地址"`
		ApiKey      string `json:"api_key" uri:"api_key" form:"api_key" validate:"required" zh:"API 密钥"`
		Protocol    string `json:"protocol" uri:"protocol" form:"protocol"  default:"https" zh:"协议"`
		NetworkType string `json:"network_type" uri:"network_type" form:"network_type" zh:"网络类型"`
		DataTypes   []int  `json:"data_types" uri:"data_types" form:"data_types" zh:"节点类型"`
		PersonField string `json:"person_field" uri:"person_field" form:"person_field" zh:"人员映射字段"`
		Version     string `json:"version" uri:"version" form:"version" zh:"版本号"`
	}{})
	if err != nil {
		return err
	}

	nodeInfo, err := data_source.NewNodeModel().First(mysql.WithColumnValue("id", params.Id))
	if err != nil {
		return err
	}

	if params.Protocol != "https" {
		return response.FailWithMessage(ctx, "协议必须为https")
	}

	// 验证 D01 节点配置能否连接上
	cli := d01.NewD01()
	err = cli.SetConfig(map[string]any{
		"api_key": params.ApiKey, "protocol": params.Protocol, "ip": params.IP, "version": params.Version,
	})

	if exists, err := data_source.NewNodeConfigModel().IpExists(params.Id, 0, params.IP); exists || err != nil {
		return response.FailWithMessage(ctx, "该IP已存在,请勿重复添加")
	}
	if err != nil {
		return err
	}
	_, err = cli.CheckedNodeStatus()
	if err != nil {
		return response.FailWithMessage(ctx, "连接节点地址超时")
	}

	// 将节点的任务类型保存为以 , 分隔的数字类型的字符串
	typesSlice := cast.ToStringSlice(params.DataTypes)
	types := strings.Join(typesSlice, ",")

	nodeInfo.Name = params.Name
	nodeInfo.DataTypes = types
	err = data_source.NewNodeModel().Updates(*nodeInfo, mysql.WithSelect("name", "use", "data_types"))
	if err != nil {
		return response.FailWithMessage(ctx, err.Error())
	}

	//json转map
	list, _ := utils.StructToMap(params, "json")
	for k, v := range list {
		if k == "id" || k == "name" || v == "" {
			continue
		}
		err := data_source.NewNodeConfigModel().Update(data_source.DataNodeConfig{
			NodeId: nodeInfo.Id,
			Key:    k,
			Value:  cast.ToString(v),
		})
		if err != nil {
			return response.FailWithMessage(ctx, err.Error())
		}
	}
	return response.Ok(ctx)
}

// D01 添加节点数据
func D01(c *gin.Context) error {
	params, err := request.Validate(c, &struct {
		SourceId    uint64      `json:"sources_id" uri:"sources_id" form:"sources_id" validate:"required" zh:"数据源ID"`
		Name        string      `json:"name" uri:"name" form:"name" validate:"required" zh:"节点名称"`
		Types       string      `json:"types" uri:"types" form:"types" zh:"节点类型"`
		IP          string      `json:"ip" uri:"ip" form:"types"  ip:"required,ipv4" zh:"节点地址"`
		ApiKey      string      `json:"api_key" uri:"api_key" form:"api_key" validate:"required" zh:"API 密钥"`
		Protocol    string      `json:"protocol" uri:"protocol" form:"protocol"  default:"https" zh:"协议"`
		RepeatType  string      `json:"repeat_type" uri:"repeat_type" form:"repeat_type" validate:"required" zh:"数据同步配置"`
		Date        string      `json:"date" uri:"date" form:"date" zh:"数据同步配置"`
		StartTime   string      `json:"start_time" uri:"start_time" form:"start_time" validate:"required" zh:"数据同步配置"`
		DataType    string      `json:"data_type" uri:"data_type" form:"data_type" zh:"时间"`
		Version     string      `json:"version" uri:"version" form:"version" zh:"版本号"`
		AreaId      uint64      `json:"area_id" uri:"area_id" form:"area_id" zh:"区域ID"`
		AreaType    string      `json:"area_type" uri:"area_type" form:"area_type" validate:"required,oneof=指定 规则" zh:"区域,指定,规则"`
		AreaRules   []*AreaRule `json:"area_rules" uri:"area_rules" form:"area_rules" zh:"区域规则"`
		NetworkType string      `json:"network_type" uri:"network_type" form:"network_type" zh:"网络类型"`
		DataTypes   []int       `json:"data_types" uri:"data_types" form:"data_types" zh:"节点类型"`
		PersonField string      `json:"person_field" uri:"person_field" form:"person_field" zh:"人员映射字段"`
	}{})
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	// 区域为规则时
	if params.AreaType == "规则" {
		if len(params.AreaRules) == 0 {
			return response.FailWithMessage(c, "区域规则不能为空")
		}
		// 检查区域规则
		if err := CheckAreaRules(params.AreaRules); err != nil {
			return response.FailWithMessage(c, err.Error())
		}
	}
	sourceInfo, err := data_source.NewSourceModel().First(mysql.WithWhere("id", params.SourceId))
	if err != nil {
		return response.FailWithMessage(c, "数据源验证不通过")
	}
	if sourceInfo.Type != "d01" {
		return response.FailWithMessage(c, "数据源验证不通过")
	}
	num, err := data_source.NewNodeModel().Total(mysql.WithWhere("source_id", params.SourceId), mysql.WithWhere("name", params.Name))
	if err != nil {
		return response.FailWithMessage(c, "该节点已添加,请勿重复添加")
	}
	if num > 0 {
		return response.FailWithMessage(c, "该节点已添加,请勿重复添加")
	}
	if exists, err := data_source.NewNodeConfigModel().IpExists(0, params.SourceId, params.IP); exists || err != nil {
		return response.FailWithMessage(c, "该IP已存在,请勿重复添加")
	}

	nodeIds, err := data_source.NewNodeModel().GetIds(params.SourceId)
	if err != nil {
		return response.FailWithMessage(c, "寻找节点错误")
	}
	_, err = data_source.NewNodeConfigModel().GetIpTotal(nodeIds, params.IP)
	if err != nil {
		return response.FailWithMessage(c, "寻找Ip错误")
	}

	// 将节点的任务类型保存为以 , 分隔的数字类型的字符串
	typesSlice := cast.ToStringSlice(params.DataTypes)
	types := strings.Join(typesSlice, ",")

	node := data_source.Node{
		SourceId:  params.SourceId,
		Name:      params.Name,
		Source:    sourceInfo.Type,
		Types:     params.Types,
		Status:    3,
		Use:       1,
		AreaType:  params.AreaType,
		AreaId:    params.AreaId,
		DataTypes: types,
	}
	if params.AreaType == "规则" {
		areaRules, err := json.Marshal(params.AreaRules)
		if err != nil {
			return response.FailWithMessage(c, err.Error())
		}
		node.AreaRules = string(areaRules)
	} else {
		node.AreaRules = ""
	}

	if params.Protocol != "https" {
		return response.FailWithMessage(c, "协议必须为https")
	}

	// 验证 D01 节点配置能否连接上
	cli := d01.NewD01()
	err = cli.SetConfig(map[string]any{
		"api_key": params.ApiKey, "protocol": params.Protocol, "ip": params.IP, "version": params.Version,
	})
	if err != nil {
		return err
	}
	_, err = cli.CheckedNodeStatus()
	if err != nil {
		return response.FailWithMessage(c, "连接节点地址超时")
	}

	url := fmt.Sprintf("https://%s", params.IP)
	rand.Seed(time.Now().UnixNano())
	randomNum := rand.Intn(1000)
	// 获取当前时间戳
	timestamp := strconv.FormatInt(time.Now().Unix(), 10)

	// 将字符串、随机数和时间戳拼接起来
	data := fmt.Sprintf("%s%d%s", url, randomNum, timestamp)

	// 计算MD5值
	md5Sum := md5.Sum([]byte(data))
	key := hex.EncodeToString(md5Sum[:])

	err = data_source.NewNodeModel().Create(&node).Error
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}

	nodeConfigs := []data_source.DataNodeConfig{
		{
			NodeId: node.Id,
			Key:    "key",
			Value:  key,
		},
		{
			NodeId: node.Id,
			Key:    "ip",
			Value:  params.IP,
		},
		{
			NodeId: node.Id,
			Key:    "protocol",
			Value:  params.Protocol,
		},
		{
			NodeId: node.Id,
			Key:    "api_key",
			Value:  params.ApiKey,
		},
		{
			NodeId: node.Id,
			Key:    "repeat_type",
			Value:  params.RepeatType,
		},
		{
			NodeId: node.Id,
			Key:    "start_time",
			Value:  params.StartTime,
		},
		{
			NodeId: node.Id,
			Key:    "version",
			Value:  params.Version,
		},
		{
			NodeId: node.Id,
			Key:    "date",
			Value:  params.Date,
		},
		{
			NodeId: node.Id,
			Key:    "data_type",
			Value:  params.DataType,
		},
		{
			NodeId: node.Id,
			Key:    "network_type",
			Value:  params.NetworkType,
		},
	}
	err = data_source.NewNodeConfigModel().Create(&nodeConfigs).Error
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	logData := logs.Create{
		IP:        params.IP,
		AwardCode: "",
		DataSyncConfig: logs.DataSyncConfig{
			RepeatType:  params.RepeatType,
			Date:        params.Date,
			StartTime:   params.StartTime,
			Version:     params.Version,
			NetDomainId: "",
		},
	}
	// 日志-新增节点
	err = logs.LogCreate(node.Id, params.Name, logData, logs.NodeLogCreate)
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	return response.Ok(c)
}

func D01Test(c *gin.Context) error {
	params, err := request.Validate(c, &struct {
		IP       string `json:"ip" uri:"ip" form:"types"  ip:"required,ipv4" zh:"节点地址"`
		ApiKey   string `json:"api_key" uri:"api_key" form:"api_key" validate:"required" zh:"API 密钥"`
		Protocol string `json:"protocol" uri:"protocol" form:"protocol"  default:"https" zh:"协议"`
		Version  string `json:"version" uri:"version" form:"version"  default:"v3" zh:"协议"`
	}{})
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	cli := d01.NewD01()
	err = cli.SetConfig(map[string]any{
		"api_key": params.ApiKey, "protocol": params.Protocol, "ip": params.IP, "version": params.Version,
	})
	if err != nil {
		return err
	}
	_, err = cli.CheckedNodeStatus()
	if err != nil {
		return response.FailWithMessage(c, "连接节点地址超时")
	}
	return response.Ok(c)
}
