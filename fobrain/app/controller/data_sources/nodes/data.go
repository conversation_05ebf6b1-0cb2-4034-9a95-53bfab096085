package nodes

import (
	"github.com/gin-gonic/gin"

	"fobrain/fobrain/app/repository/node"
	"fobrain/fobrain/common/request"
	"fobrain/fobrain/common/response"
)

type (
	DataRequest struct {
		request.PageRequest
		Search   string `json:"keyword" form:"keyword" uri:"keyword" validate:"omitempty" zh:"模糊查询"`
		NodeId   int    `json:"node_id"  form:"node_id" uri:"node_id" validate:"required" zh:"节点ID"`
		TaskId   int    `json:"task_id"  form:"task_id" uri:"task_id" validate:"omitempty" zh:"节点任务ID"`
		SyncType int    `json:"sync_type" form:"sync_type" uri:"sync_type" validate:"required,oneof=1 2 3" zh:"同步数据类型"`
	}
	FailDetailRequest struct {
		request.PageRequest
		ChildTaskId int `json:"child_task_id" form:"child_task_id" uri:"child_task_id" validate:"required" zh:"子任务ID"`
	}
)

// Data
// @router api/v1/node/data [get]
func Data(c *gin.Context) error {
	params, err := request.Validate(c, &DataRequest{})
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}

	data, total, listFields, listDetails, err := node.GetData(params.Page, params.PerPage, params.NodeId, params.TaskId, params.SyncType, params.Search)

	if err != nil {
		return err
	}
	return response.OkWithPageCustomData(c, total, params.Page, params.PerPage, data, listFields, listDetails)
}

func FailDetail(c *gin.Context) error {
	params, err := request.Validate(c, &FailDetailRequest{})
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	items, total, err := node.GetFailDetail(params.ChildTaskId, params.Page, params.PerPage)
	if err != nil {
		return err
	}
	return response.OkWithPageData(c, total, params.Page, params.PerPage, items)
}
