package aliyun_cloud

import (
	"bytes"
	"encoding/json"
	"io/ioutil"
	"net/http"
	"net/http/httptest"
	"reflect"
	"strings"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"

	"fobrain/fobrain/app/services/node/aliyun_cloud"
	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/models/mysql/data_source"
)

func TestAliYunCloud(t *testing.T) {
	t.Run("param err", func(t *testing.T) {
		w := httptest.NewRecorder()
		req := httptest.NewRequest("POST", "/api/v1/data/node/aliyun/cloud", nil)
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := AliYunCloud(c)
		assert.Nil(t, err)
	})

	t.Run("missing protocol scheme", func(t *testing.T) {
		w := httptest.NewRecorder()
		req := httptest.NewRequest("POST", "/api/v1/data/node/aliyun/cloud", strings.NewReader(`{
				"name":"阿里云云盾-1",
				"area_id":1,
				"area_type":"指定",
				"app_secret":"sdfkladsjfljasdlfdajsf",
				"app_key":"dsflkjadlfjaldf",
				"repeat_type":"day",
				"date":"",
				"start_time":"00:00",
				"ip":"baidu.com",
				"protocol":"https"
			}`))
		req.Header.Set("Content-Type", "application/json")
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := AliYunCloud(c)
		assert.Nil(t, err)
		assert.Equal(t, w.Body.String(), "{\"code\":400,\"message\":\"数据源ID为必填字段\",\"data\":{}}")
	})
	t.Run("success", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()
		mockDb.ExpectQuery("SELECT * FROM `data_sources` WHERE `id` = ? ORDER BY `data_sources`.`id` LIMIT 1").
			WillReturnRows(mockDb.NewRows([]string{"id", "name", "type"}).AddRow(1, "name", "aliyun_cloud"))

		mockDb.ExpectQuery("SELECT count(*) FROM `data_nodes` WHERE `source_id` = ? AND `name` = ? AND deleted_at IS NULL").
			WithArgs(8, "阿里云云盾-1").
			WillReturnRows(sqlmock.NewRows([]string{"count(*)"}).AddRow(0))

		time.Sleep(time.Second)
		defer gomonkey.ApplyMethod(reflect.TypeOf(&http.Client{}), "Do", func(client *http.Client, req *http.Request) (*http.Response, error) {
			return &http.Response{
				StatusCode: http.StatusOK,
				Body:       ioutil.NopCloser(bytes.NewBufferString(`{"status":"ok"}`)),
			}, nil
		}).Reset()

		mockDb.Mock.ExpectBegin()
		mockDb.ExpectExec(
			"INSERT INTO `data_nodes` (`created_at`,`updated_at`,`deleted_at`,`source_id`,`area_id`,`name`,`source`,`types`,`status`,`use`,`last_task_at`,`last_task_status`,`last_sync_result`,`data_types`, `area_type`, `area_rules`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)",
		).WillReturnResult(sqlmock.NewResult(1, 1))
		mockDb.Mock.ExpectCommit()

		mockDb.Mock.ExpectBegin()
		mockDb.ExpectExec(
			"INSERT INTO `data_node_configs` (`created_at`,`updated_at`,`node_id`,`key`,`value`,`hidden`) VALUES (?,?,?,?,?,?),(?,?,?,?,?,?),(?,?,?,?,?,?),(?,?,?,?,?,?),(?,?,?,?,?,?),(?,?,?,?,?,?),(?,?,?,?,?,?),(?,?,?,?,?,?)",
		).WillReturnResult(sqlmock.NewResult(1, 1))
		mockDb.Mock.ExpectCommit()

		mockDb.Mock.ExpectBegin()
		mockDb.ExpectExec(
			"INSERT INTO `data_node_logs` (`created_at`,`updated_at`,`node_id`,`name`,`log_type`,`content`) VALUES (?,?,?,?,?,?)",
		).WillReturnResult(sqlmock.NewResult(1, 1))
		mockDb.Mock.ExpectCommit()

		param, _ := json.Marshal(CustomSecretNodeRequest{
			Name:       "阿里云云盾-1",
			AreaId:     1,
			AreaType:   "指定",
			SourceId:   8,
			AppSecret:  "sdlfkajldfas",
			AppKey:     "dflkajflajldf",
			RepeatType: "day",
			Date:       "",
			StartTime:  "00:00",
			IP:         "***********",
			Protocol:   "https",
			Types:      "",
			DataTypes:  []int{1},
		})

		w := httptest.NewRecorder()
		req := httptest.NewRequest("POST", "/api/v1/data/node/aliyun/cloud", strings.NewReader(string(param)))
		req.Header.Set("Content-Type", "application/json")

		// Create a Gin context
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		// Call the function under test
		err := AliYunCloud(c)
		assert.Nil(t, err)
	})
}

func TestCheckAliYunCloudStatus(t *testing.T) {
	mockDb := testcommon.InitSqlMock()
	defer mockDb.Close()
	mockDb.ExpectQuery("SELECT * FROM `data_nodes` WHERE `id` = ? AND `data_nodes`.`deleted_at` IS NULL ORDER BY `data_nodes`.`id` LIMIT 1").
		WithArgs(1).
		WillReturnRows(mockDb.NewRows([]string{"id"}).AddRow(1))

	mockDb.ExpectQuery("SELECT * FROM `data_node_configs` WHERE `node_id` = ?").
		WithArgs(1).
		WillReturnRows(mockDb.NewRows([]string{"id"}).AddRow(1))

	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyMethodReturn(data_source.NewNodeModel(), "Update", nil).Reset()
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyMethodReturn(aliyun_cloud.NewAliYunCloud(), "GetToken", nil, nil).Reset()

	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/data/node/aliyun/cloud?id=1", nil)
	req.Header.Set("Content-Type", "application/json")

	// Create a Gin context
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// Call the function under test
	err := CheckAliYunCloudStatus(c)
	assert.Nil(t, err)
}

func TestUpdateAliYunCloud(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()
		mockDb.ExpectQuery("SELECT * FROM `data_nodes` WHERE `id` = ? AND `data_nodes`.`deleted_at` IS NULL ORDER BY `data_nodes`.`id` LIMIT 1").
			WithArgs(61).
			WillReturnRows(mockDb.NewRows([]string{"id", "name", "type"}).AddRow(1, "name", "aliyun_cloud"))

		time.Sleep(time.Second)
		defer gomonkey.ApplyMethod(reflect.TypeOf(&http.Client{}), "Do", func(client *http.Client, req *http.Request) (*http.Response, error) {
			return &http.Response{
				StatusCode: http.StatusOK,
				Body:       ioutil.NopCloser(bytes.NewBufferString(`{"status":"ok"}`)),
			}, nil
		}).Reset()

		time.Sleep(time.Second)
		defer gomonkey.ApplyMethodReturn(data_source.NewNodeModel(), "Updates", nil).Reset()
		time.Sleep(time.Second)
		defer gomonkey.ApplyMethodReturn(data_source.NewNodeConfigModel(), "Update", nil).Reset()
		time.Sleep(time.Second)
		defer gomonkey.ApplyMethodReturn(aliyun_cloud.NewAliYunCloud(), "GetToken", nil, nil).Reset()

		param, _ := json.Marshal(CustomSecretNodeUpdateRequest{
			Id: 61,
			CustomSecretNodeRequest: CustomSecretNodeRequest{
				Name:       "阿里云云盾-1",
				AreaId:     1,
				SourceId:   8,
				AppSecret:  "sdlfkajldfas",
				AppKey:     "dflkajflajldf",
				RepeatType: "day",
				Date:       "",
				StartTime:  "00:00",
				IP:         "***********",
				Protocol:   "https",
				Types:      "",
			},
		})
		w := httptest.NewRecorder()
		req := httptest.NewRequest("PUT", "/api/v1/data/node/aliyun/cloud", strings.NewReader(string(param)))
		req.Header.Set("Content-Type", "application/json")

		// Create a Gin context
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		// Call the function under test
		err := UpdateAliYunCloud(c)
		assert.Nil(t, err)
	})
}

func TestAliYunCloudTest(t *testing.T) {
	param, _ := json.Marshal(CustomSecretNodeRequest{
		Name:       "阿里云云盾-1",
		AreaId:     1,
		SourceId:   8,
		AreaType:   "指定",
		AppSecret:  "secret",
		AppKey:     "key",
		RepeatType: "day",
		Date:       "",
		StartTime:  "00:00",
		IP:         "***********",
		Protocol:   "https",
		Types:      "",
		DataTypes:  []int{1},
	})

	w := httptest.NewRecorder()
	req := httptest.NewRequest("POST", "/api/v1/data/node/aliyun/cloud", strings.NewReader(string(param)))
	req.Header.Set("Content-Type", "application/json")

	c, _ := gin.CreateTestContext(w)
	c.Request = req

	patches := gomonkey.ApplyMethodReturn(aliyun_cloud.NewAliYunCloud(), "GetToken", "", nil)

	err := AliYunCloudTest(c)

	patches.Reset()

	assert.Nil(t, err)
	assert.Equal(t, w.Body.String(), `{"code":0,"message":"Success","data":{}}`)
}
