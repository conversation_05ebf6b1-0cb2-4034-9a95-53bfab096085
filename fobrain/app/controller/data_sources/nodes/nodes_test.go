package nodes

import (
	"database/sql/driver"
	"encoding/json"
	"net/http/httptest"
	"reflect"
	"strings"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"

	reqnode "fobrain/fobrain/app/request/node"
	testcommon "fobrain/fobrain/tests/common_test"
)

func TestNodesList(t *testing.T) {
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()

	mockDb.ExpectQuery("SELECT count(*) FROM `data_nodes` LEFT JOIN data_node_configs as config ON data_nodes.id = config.node_id WHERE data_nodes.source_id = ? AND `data_nodes`.`deleted_at` IS NULL GROUP BY `data_nodes`.`id`").
		WithArgs(1).
		WillReturnRows(sqlmock.NewRows([]string{"count(*)"}).AddRow(1))

	mockDb.ExpectQuery("SELECT `data_nodes`.`id`,`data_nodes`.`created_at`,`data_nodes`.`updated_at`,`data_nodes`.`deleted_at`,`data_nodes`.`source_id`,`data_nodes`.`area_id`,`data_nodes`.`name`,`data_nodes`.`source`,`data_nodes`.`types`,`data_nodes`.`status`,`data_nodes`.`use`,`data_nodes`.`last_task_at`,`data_nodes`.`last_task_status`,`data_nodes`.`last_sync_result`,`data_nodes`.`data_types` FROM `data_nodes` LEFT JOIN data_node_configs as config ON data_nodes.id = config.node_id WHERE data_nodes.source_id = ? AND `data_nodes`.`deleted_at` IS NULL GROUP BY `data_nodes`.`id` LIMIT 10").
		WithArgs(1).
		WillReturnRows(sqlmock.NewRows([]string{"id", "source_id", "area_id", "name", "data_types"}).
			AddRows(
				[]driver.Value{1, 1, 1, "节点1", "1,2,3"},
			),
		)

	mockDb.ExpectQuery("SELECT * FROM `network_areas` WHERE `id` = ? ORDER BY `network_areas`.`id` LIMIT 1").
		WithArgs(1).
		WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).
			AddRow(1, "北京"))

	timeNow := time.Now()
	mockDb.ExpectQuery("SELECT status,end_at FROM `data_sync_tasks` WHERE node_id = ? ORDER BY end_at DESC LIMIT 1").
		WithArgs(1).
		WillReturnRows(sqlmock.NewRows([]string{"status", "end_at"}).
			AddRow(1, &timeNow))

	mockDb.ExpectQuery("SELECT * FROM `data_node_configs` WHERE `node_id` = ?").
		WithArgs(1).
		WillReturnRows(sqlmock.NewRows([]string{"id"}).
			AddRows(
				[]driver.Value{1},
			))

	mockDb.ExpectQuery("SELECT * FROM `data_sources` WHERE id = ? ORDER BY `data_sources`.`id` LIMIT 1").
		WithArgs(1).
		WillReturnRows(sqlmock.NewRows([]string{"has_asset_data", "has_vul_data", "has_personnel_data"}).AddRow(true, true, true))

	t.Run("test params err", func(t *testing.T) {
		// 创建一个测试请求和响应
		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/data/nodes?page=1&per_page=10", nil)
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		// 调用函数
		err := List(c)

		assert.NoError(t, err)
	})
	t.Run("success", func(t *testing.T) {
		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/data/nodes?page=1&per_page=10&source_id=1", nil)
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := List(c)
		assert.Nil(t, err)
	})
}

func TestItem(t *testing.T) {
	t.Run("pass", func(t *testing.T) {
		mockDb := testcommon.GetMysqlMock()
		defer mockDb.Close()

		mockDb.ExpectQuery("SELECT * FROM `data_nodes`").
			WillReturnRows(mockDb.NewRows([]string{"id"}).AddRow(1))
		// 创建一个测试请求和响应
		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/data/nodes?page=1&per_page=10", nil)
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		// 调用函数
		err := Item(c)
		assert.Nil(t, err)
	})
}

func TestGetNode(t *testing.T) {
	t.Run("params err", func(t *testing.T) {
		//mockDb := testcommon.GetMysqlMock()
		//defer mockDb.Close()
		//
		//mockDb.ExpectQuery("SELECT * FROM `data_nodes`").
		//	WillReturnRows(mockDb.NewRows([]string{"id"}).AddRow(1))
		// 创建一个测试请求和响应
		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/data/nodes?page=1&per_page=10", nil)
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		// 调用函数
		err := GetNode(c)

		assert.Nil(t, err)
		assert.Equal(t, `{"code":400,"message":"节点ID为必填字段","data":{}}`, w.Body.String())
	})
	t.Run("pass", func(t *testing.T) {
		mockDb := testcommon.GetMysqlMock()
		defer mockDb.Close()

		mockDb.ExpectQuery("SELECT * FROM `data_nodes` WHERE `id` = ? AND `data_nodes`.`deleted_at` IS NULL ORDER BY `data_nodes`.`id` LIMIT 1").
			WillReturnRows(mockDb.NewRows([]string{"id"}).AddRow(1))
		// 创建一个测试请求和响应
		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/data/nodes?id=1", nil)
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		// 调用函数
		err := GetNode(c)

		assert.Nil(t, err)
		assert.NotEmpty(t, w.Body.String())
	})
}

func TestDeleteNode(t *testing.T) {
	t.Run("params err", func(t *testing.T) {
		// 创建一个测试请求和响应
		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/data/nodes?page=1&per_page=10", nil)
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		// 调用函数
		err := DeleteNode(c)

		assert.Nil(t, err)
		assert.Equal(t, `{"code":400,"message":"节点ID为必填字段","data":{}}`, w.Body.String())
	})
	t.Run("pass", func(t *testing.T) {
		mockDb := testcommon.GetMysqlMock()
		defer mockDb.Close()

		mockDb.ExpectExec("DELETE FROM `data_nodes` WHERE id = ?").
			WillReturnResult(sqlmock.NewResult(1, 1)) // 假设有1行被删除
		// 创建一个测试请求和响应
		w := httptest.NewRecorder()
		req := httptest.NewRequest("DELETE", "/api/data/nodes?id=1", nil)
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		// 调用函数
		err := DeleteNode(c)

		assert.Nil(t, err)
	})
}

func TestCustomDataSourceNodeAdd(t *testing.T) {
	t.Run("params err", func(t *testing.T) {
		// 创建一个测试请求和响应
		w := httptest.NewRecorder()
		req := httptest.NewRequest("POST", "/api/data/nodes", nil)
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		// 调用函数
		err := CustomDataSourceNodeAdd(c)

		assert.NotNil(t, err)
		assert.Equal(t, err.Error(), "节点名称为必填字段")
	})
	t.Run("node name repeat", func(t *testing.T) {
		mockDb := testcommon.GetMysqlMock()
		defer mockDb.Close()

		mockDb.ExpectQuery("SELECT count(*) FROM `data_nodes` WHERE name = ? AND deleted_at IS NULL").
			WillReturnRows(mockDb.NewRows([]string{"count"}).AddRow(1))

		val, _ := json.Marshal(reqnode.CustomDataSourceNodeAddRequest{
			Name:     "node",
			SourceId: 1,
			AreaId:   1,
		})
		// 创建一个测试请求和响应
		w := httptest.NewRecorder()
		req := httptest.NewRequest("POST", "/api/v1/data/nodes", strings.NewReader(string(val)))
		req.Header.Set("Content-Type", "application/json")

		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		// 调用函数
		err := CustomDataSourceNodeAdd(c)

		assert.NotNil(t, err)
		assert.Equal(t, err.Error(), "节点名称重复")
	})
	t.Run("pass", func(t *testing.T) {
		mockDb := testcommon.GetMysqlMock()
		defer mockDb.Close()

		mockDb.ExpectQuery("SELECT count(*) FROM `data_nodes` WHERE name = ? AND deleted_at IS NULL").
			WillReturnRows(mockDb.NewRows([]string{"count"}).AddRow(0))
		val, _ := json.Marshal(reqnode.CustomDataSourceNodeAddRequest{
			Name:     "node",
			SourceId: 1,
			AreaId:   1,
		})
		// 创建一个测试请求和响应
		w := httptest.NewRecorder()
		req := httptest.NewRequest("POST", "/api/v1/data/nodes", strings.NewReader(string(val)))
		req.Header.Set("Content-Type", "application/json")

		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		// 调用函数
		err := CustomDataSourceNodeAdd(c)

		assert.NotNil(t, err)
	})
}

func TestCustomDataSourceNodeUpdate(t *testing.T) {
	t.Run("params err", func(t *testing.T) {
		// 创建一个测试请求和响应
		w := httptest.NewRecorder()
		req := httptest.NewRequest("PUT", "/api/data/nodes", nil)
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		// 调用函数
		err := CustomDataSourceNodeUpdate(c)

		assert.NotNil(t, err)
		assert.Equal(t, err.Error(), "节点名称为必填字段")
	})
	t.Run("node name repeat", func(t *testing.T) {
		mockDb := testcommon.GetMysqlMock()
		defer mockDb.Close()

		mockDb.ExpectQuery("SELECT count(*) FROM `data_nodes` WHERE name = ? AND id != ? AND deleted_at IS NULL").
			WillReturnRows(mockDb.NewRows([]string{"count"}).AddRow(1))

		val, _ := json.Marshal(reqnode.CustomDataSourceNodeUpdateRequest{
			Name: "node",
			Id:   1,
		})
		// 创建一个测试请求和响应
		w := httptest.NewRecorder()
		req := httptest.NewRequest("PUT", "/api/v1/data/nodes", strings.NewReader(string(val)))
		req.Header.Set("Content-Type", "application/json")

		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		// 调用函数
		err := CustomDataSourceNodeUpdate(c)

		assert.NotNil(t, err)
		assert.Equal(t, err.Error(), "节点名称重复")
	})
	t.Run("pass", func(t *testing.T) {
		mockDb := testcommon.GetMysqlMock()
		defer mockDb.Close()

		mockDb.ExpectQuery("SELECT count(*) FROM `data_nodes` WHERE name = ? AND deleted_at IS NULL").
			WillReturnRows(mockDb.NewRows([]string{"count"}).AddRow(0))
		val, _ := json.Marshal(reqnode.CustomDataSourceNodeUpdateRequest{
			Name: "node",
			Id:   1,
		})
		// 创建一个测试请求和响应
		w := httptest.NewRecorder()
		req := httptest.NewRequest("PUT", "/api/v1/data/nodes", strings.NewReader(string(val)))
		req.Header.Set("Content-Type", "application/json")

		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		// 调用函数
		err := CustomDataSourceNodeUpdate(c)

		assert.NotNil(t, err)
	})
}

func TestListSourceType(t *testing.T) {
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()

	mockDb.ExpectQuery("SELECT * FROM `data_sources` WHERE id = ? ORDER BY `data_sources`.`id` LIMIT 1").
		WithArgs(1).
		WillReturnRows(sqlmock.NewRows([]string{"has_asset_data", "has_vul_data", "has_personnel_data"}).AddRow(true, true, true))

	types, err := ListSourceType(1)

	assert.Nil(t, err)
	assert.True(t, reflect.DeepEqual(types, []int{1, 2, 3}))
}
