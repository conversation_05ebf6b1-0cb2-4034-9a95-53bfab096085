package nodes

import (
	"fmt"
	"fobrain/fobrain/common/response"
	tencent_ud "fobrain/models/elastic/source/tencent_ud"
	"github.com/gin-gonic/gin"
)

// GetUDFields 获取UD字段
func GetUDFields(c *gin.Context) error {
	listFields := tencent_ud.NewTencentUDStaffTaskModel().GetUDStaffListFields()
	type ListFields struct {
		Name string `json:"name"`
		Key  string `json:"all_key"`
	}
	result := make([]ListFields, 0)
	for _, item := range listFields {
		if _, exists := item["show_value"]; exists {
			result = append(result, ListFields{
				Name: item["name"],
				Key:  fmt.Sprintf("%s.%s", item["show_value"], item["key"]),
			})
		} else {
			result = append(result, ListFields{
				Name: item["name"],
				Key:  item["key"],
			})
		}
	}
	return response.OkWithData(c, result)
}
