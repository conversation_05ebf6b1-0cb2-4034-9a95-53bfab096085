package nodes

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"fobrain/dataSourceFrame/data_sync_frame"
	"fobrain/fobrain/app/repository/ip_ranges"
	"fobrain/fobrain/app/services/network_area"
	"fobrain/fobrain/app/services/node/logs"
	"fobrain/fobrain/common/request"
	"fobrain/fobrain/common/response"
	"fobrain/fobrain/common/validate"
	logs2 "fobrain/fobrain/logs"
	"fobrain/initialize/mysql"
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/workbench"
	"fobrain/pkg/utils"
	"slices"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

/*通用处理框架*/
type DataNodeConfigParams struct {
	Id       uint64 `json:"id" uri:"id" form:"id" validate:"number" zh:"节点ID"` // 创建节点后才有
	SourceId uint64 `json:"sources_id" uri:"sources_id" form:"sources_id" validate:"required,number" zh:"数据源ID"`

	// 节点配置
	IP       string `json:"ip" uri:"ip" form:"ip"  validate:"required" zh:"节点地址"`
	Name     string `json:"name" uri:"name" form:"name" validate:"required" zh:"节点名称"`
	Protocol string `json:"protocol" uri:"protocol" form:"protocol"  default:"https" zh:"协议"`
	// 节点配置，以下根据数据源实际情况传值
	Config map[string]string `json:"config" uri:"config" form:"config" zh:"配置"`
	// 数据同步配置
	RepeatType string `json:"repeat_type" uri:"repeat_type" form:"repeat_type" validate:"required" zh:"数据同步配置"`
	Date       string `json:"date" uri:"date" form:"date" zh:"数据同步配置"`
	StartTime  string `json:"start_time" uri:"start_time" form:"start_time" validate:"required" zh:"数据同步配置"`
	DataType   string `json:"data_type" uri:"data_type" form:"data_type" zh:"时间"`

	// 内外网类型
	NetworkType string `json:"network_type" uri:"network_type" form:"network_type" zh:"网络类型"`

	// 节点配置的数据类型
	DataTypes []int `json:"data_types" uri:"data_types" form:"data_types" zh:"节点类型"`

	// 区域配置
	AreaType  string      `json:"area_type" uri:"area_type" form:"area_type" validate:"required,oneof=指定 规则" zh:"区域类型,指定,规则"`
	AreaId    uint64      `json:"area_id" uri:"area_id" form:"area_id" validate:"required" zh:"区域ID"`
	AreaRules []*AreaRule `json:"area_rules" uri:"area_rules" form:"area_rules" zh:"区域规则"` // key=区域ID,value=ip段

	// 人员唯一值字段
	UniqueField string `json:"unique_field" uri:"unique_field" form:"unique_field" zh:"人员唯一值字段"`
}

type DataNodeValidTest func() error

func AddNode(c *gin.Context) error {
	params, err := FrameValidate(c)
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	// 区域为规则时
	if params.AreaType == "规则" {
		if len(params.AreaRules) == 0 {
			return response.FailWithMessage(c, "区域规则不能为空")
		}
		// 检查区域规则
		if err := CheckAreaRules(params.AreaRules); err != nil {
			return response.FailWithMessage(c, err.Error())
		}
	}
	sourceInfo, err := data_source.NewSourceModel().First(mysql.WithWhere("id", params.SourceId))
	if err != nil {
		return response.FailWithMessage(c, "数据源验证不通过")
	}
	num, err := data_source.NewNodeModel().Total(mysql.WithWhere("source_id", params.SourceId), mysql.WithWhere("name", params.Name))
	if err != nil {
		return response.FailWithMessage(c, "该节点已添加,请勿重复添加")
	}
	if num > 0 {
		return response.FailWithMessage(c, "该节点已添加,请勿重复添加")
	}
	if exists, err := data_source.NewNodeConfigModel().IpExists(0, params.SourceId, params.IP); exists || err != nil {
		return response.FailWithMessage(c, "该IP已存在,请勿重复添加")
	}
	// 如果数据源同步人员，必须传入人员唯一值
	if sourceInfo.HasPersonnelData {
		if params.UniqueField == "" {
			return response.FailWithMessage(c, "人员唯一值字段不能为空")
		}
	}
	list, _ := utils.StructToMap(params, "json")
	sdk := data_sync_frame.NewDataSourceSDK(params.SourceId, "")
	if sdk == nil {
		return response.FailWithMessage(c, "数据源类型不匹配")
	}
	for k, v := range params.Config {
		list[k] = v
	}
	err = sdk.NetworktConnectTest(context.Background(), list)
	if err != nil {
		logs2.GetLogger().Errorf("NetworktConnectTest error:%v", err)
		return response.FailWithMessage(c, err.Error())
	}
	// 将节点的任务类型保存为以 , 分隔的数字类型的字符串
	typesSlice := cast.ToStringSlice(params.DataTypes)
	types := strings.Join(typesSlice, ",")

	node := data_source.Node{
		SourceId:  params.SourceId,
		Name:      params.Name,
		Source:    sourceInfo.Type,
		Status:    3,
		Use:       1,
		AreaType:  params.AreaType,
		AreaId:    params.AreaId,
		DataTypes: types,
	}
	if params.AreaType == "规则" {
		areaRules, err := json.Marshal(params.AreaRules)
		if err != nil {
			return response.FailWithMessage(c, err.Error())
		}
		node.AreaRules = string(areaRules)
	} else {
		node.AreaRules = ""
	}
	err = data_source.NewNodeModel().Create(&node).Error
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}

	nodeConfigs := make([]data_source.DataNodeConfig, 0, len(list))

	for k, v := range list {
		if v == "" {
			continue
		}

		nodeConfigs = append(nodeConfigs, data_source.DataNodeConfig{
			NodeId: node.Id,
			Key:    k,
			Value:  cast.ToString(v),
		})
	}
	//处理节点配置
	for k, v := range params.Config {
		if v == "" {
			return response.FailWithMessage(c, "配置项"+k+"不能为空")
		}
		nodeConfigs = append(nodeConfigs, data_source.DataNodeConfig{
			NodeId: node.Id,
			Key:    k,
			Value:  v,
		})
	}
	err = data_source.NewNodeConfigModel().Create(&nodeConfigs).Error
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	logData := logs.Create{
		IP:        params.IP,
		AwardCode: "",
		DataSyncConfig: logs.DataSyncConfig{
			RepeatType:  params.RepeatType,
			Date:        params.Date,
			StartTime:   params.StartTime,
			Version:     "",
			NetDomainId: "",
		},
	}
	// 日志-新增节点
	err = logs.LogCreate(node.Id, params.Name, logData, logs.NodeLogCreate)
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}

	return response.Ok(c)
}

// 检查区域规则
func CheckAreaRules(areaRules []*AreaRule) error {
	if len(areaRules) == 0 {
		return errors.New("区域规则不能为空")
	}
	// 获取所有area
	networkAreaList, err := network_area.GetNetWorkAreaList()
	if err != nil {
		return err
	}
	// 转换为map
	areaMap := make(map[uint64]string)
	for _, area := range networkAreaList {
		areaMap[area.Id] = area.Name
	}
	// 检查areaRules
	areaRuleMap := make(map[uint64][]string)
	areaIds := make([]uint64, 0)
	for _, v := range areaRules {
		if v.NetworkAreasId < 1 || len(v.IpRange) == 0 {
			return errors.New("区域规则格式错误,区域ID不能为空,区域IP段不能为空")
		}
		if slices.Contains(areaIds, v.NetworkAreasId) {
			return errors.New("区域ID重复")
		}
		if _, ok := areaMap[v.NetworkAreasId]; !ok {
			return errors.New("区域ID不存在")
		}
		areaIds = append(areaIds, v.NetworkAreasId)

		for _, ipSegment := range v.IpRange {
			if slices.Contains(areaRuleMap[v.NetworkAreasId], ipSegment) {
				return errors.New("区域IP段重复")
			}
			// 检查ip段是否符合cidr
			if ok, err := ip_ranges.CheckIpRange(ipSegment); !ok || err != nil {
				return errors.New("区域IP段格式错误")
			}
			areaRuleMap[v.NetworkAreasId] = append(areaRuleMap[v.NetworkAreasId], ipSegment)
		}
	}
	// 检查ip段，不允许重叠
	overlap, areaId1, ipRange1, areaId2, ipRange2, err := utils.CheckForOverlappingIPsAcrossRegions(areaRuleMap)
	if overlap {
		return fmt.Errorf("区域IP段重叠,区域1:%s,IP段:%s,区域2:%s,IP段:%s", areaMap[areaId1], ipRange1, areaMap[areaId2], ipRange2)
	}
	return nil
}

func CheckDataNodeStatus(c *gin.Context) error {
	params, err := request.Validate(c, &struct {
		Id uint64 `json:"id" uri:"id" form:"id" validate:"required" zh:"节点ID"`
	}{})

	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}

	node, err := data_source.NewNodeModel().First(mysql.WithColumnValue("id", params.Id))
	if err != nil {
		response.FailWithMessage(c, err.Error())
	}

	config, err := data_source.NewNodeConfigModel().GetNodeConfig(node.Id)
	if err != nil {
		response.FailWithMessage(c, err.Error())
	}

	sdk := data_sync_frame.NewDataSourceSDK(node.SourceId, "")
	if sdk == nil {
		return response.FailWithMessage(c, "数据源类型不匹配")
	}
	err = sdk.NetworktConnectTest(context.Background(), config)
	if err != nil {
		logs2.GetLogger().Errorf("NetworktConnectTest error:%v", err)
		node.Status = data_source.StatusNONormal
		workbench.ProbeAbnormalSend(fmt.Sprintf("%s %d探针异常", node.Source, params.Id))
	} else {
		node.Status = data_source.StatusNormal
	}
	err = data_source.NewNodeModel().Update(*node)
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	return response.Ok(c)
}

func DataNodeTest(c *gin.Context) error {
	params, err := FrameValidate(c)
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	sdk := data_sync_frame.NewDataSourceSDK(params.SourceId, "")
	if sdk == nil {
		return response.FailWithMessage(c, "数据源类型不匹配")
	}
	config, _ := utils.StructToMap(params, "json")
	for k, v := range params.Config {
		config[k] = v
	}
	err = sdk.NetworktConnectTest(context.Background(), config)
	if err != nil {
		logs2.GetLogger().Errorf("NetworktConnectTest error:%v", err)
		return response.FailWithMessage(c, "探针连接异常")
	}
	return response.Ok(c)
}

func UpdateDataNode(c *gin.Context) error {
	params, err := FrameValidate(c)
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	if exists, err := data_source.NewNodeConfigModel().IpExists(params.Id, 0, params.IP); exists || err != nil {
		return response.FailWithMessage(c, "该IP已存在,请勿重复添加")
	}
	list, _ := utils.StructToMap(params, "json")
	sdk := data_sync_frame.NewDataSourceSDK(params.SourceId, "")
	if sdk == nil {
		return response.FailWithMessage(c, "数据源类型不匹配")
	}
	for k, v := range params.Config {
		list[k] = v
	}
	err = sdk.NetworktConnectTest(context.Background(), list)
	if err != nil {
		logs2.GetLogger().Errorf("NetworktConnectTest error:%v", err)
		return response.FailWithMessage(c, "探针连接异常")
	}

	nodeInfo, err := data_source.NewNodeModel().First(mysql.WithColumnValue("id", params.Id))
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	// 将节点的任务类型保存为以 , 分隔的数字类型的字符串
	typesSlice := cast.ToStringSlice(params.DataTypes)
	types := strings.Join(typesSlice, ",")
	nodeInfo.Name = params.Name
	nodeInfo.DataTypes = types
	err = data_source.NewNodeModel().Updates(*nodeInfo, mysql.WithSelect("name", "use", "data_types"))
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	for k, v := range list {
		if k == "id" || k == "area_id" || v == "" {
			continue
		}
		err := data_source.NewNodeConfigModel().Update(data_source.DataNodeConfig{
			NodeId: nodeInfo.Id,
			Key:    k,
			Value:  cast.ToString(v),
		})
		if err != nil {
			return response.FailWithMessage(c, err.Error())
		}
	}
	//处理节点配置
	for k, v := range params.Config {
		if v == "" {
			return response.FailWithMessage(c, "配置项"+k+"不能为空")
		}
		err := data_source.NewNodeConfigModel().Update(data_source.DataNodeConfig{
			NodeId: nodeInfo.Id,
			Key:    k,
			Value:  v,
		})
		if err != nil {
			return response.FailWithMessage(c, err.Error())
		}
	}
	return response.Ok(c)
}

// Validate 请求参数校验
func FrameValidate(ctx *gin.Context) (*DataNodeConfigParams, error) {
	var params = &DataNodeConfigParams{}
	if err := request.ShouldBind(ctx, params); err != nil {
		return nil, err
	}
	if params.SourceId == data_source.PublicHuaweiEcsSourceId {
		params.IP = "for-validate.com"
	}

	// 继续执行验证逻辑
	if ok, msg := validate.Validator(params); !ok {
		return nil, errors.New(msg)
	}
	return params, nil
}
