package nodes

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"net/http/httptest"
	"reflect"
	"strings"
	"testing"

	"github.com/alicebob/miniredis/v2"
	"github.com/go-redis/redis/v8"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"

	testcommon "fobrain/fobrain/tests/common_test"
)

func TestZhongyiFeishu(t *testing.T) {
	t.Run("param err", func(t *testing.T) {
		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/v1/data/node/zhongyi_feishu", nil)
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := ZhongyiFeishu(c)
		assert.Nil(t, err)
	})

	t.Run("success", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()
		mockDb.ExpectQuery("SELECT * FROM `data_sources` WHERE `id` = ? ORDER BY `data_sources`.`id` LIMIT 1").
			WillReturnRows(mockDb.NewRows([]string{"id", "name", "type"}).AddRow(1, "name", "zhongyi_feishu"))
		mockDb.ExpectQuery("SELECT count(*) FROM `data_nodes` WHERE `source_id` = ? AND `name` = ? AND deleted_at IS NULL").
			WithArgs(1, "zhongyi_feishu").
			WillReturnRows(mockDb.NewRows([]string{"count(*)"}).AddRow(0))
		mockDb.ExpectQuery("SELECT * FROM `data_node_configs` WHERE `key` =? AND value =?").
			WithArgs("ip", "***********").
			WillReturnRows(mockDb.NewRows(nil))

		patches := gomonkey.ApplyMethod(reflect.TypeOf(&http.Client{}), "Do", func(client *http.Client, req *http.Request) (*http.Response, error) {
			return &http.Response{
				StatusCode: http.StatusOK,
				Body:       ioutil.NopCloser(bytes.NewBufferString(`{"status":"ok"}`)),
			}, nil
		})
		defer patches.Reset()

		w := httptest.NewRecorder()
		req := httptest.NewRequest("POST", "/api/v1/data/node/zhongyi/feishu", strings.NewReader(`{
			"sources_id": 1,
			"name": "zhongyi_feishu",
			"ip": "***********",
			"area_type": "指定",
			"area_id": 1,
			"repeat_type": "1",
			"start_time": "2024-1-21",
			"sources_id": 22,
			"appsecret": "1",
			"appkey": "1",
			"protocol": "https"
    	}`))

		req.Header.Set("Content-Type", "application/json")

		// Create a Gin context
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		// Call the function under test
		err := ZhongyiFeishu(c)
		assert.Nil(t, err)
	})
}

func TestCheckZhongyiFeishuStatus(t *testing.T) {
	t.Run("param err", func(t *testing.T) {
		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/v1/node/zhongyi/feishu/:id/check_status", nil)
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := CheckZhongyiFeishuStatus(c)
		assert.Nil(t, err)
		assert.Equal(t, w.Body.String(), "{\"code\":400,\"message\":\"节点ID为必填字段\",\"data\":{}}")
	})
}

func TestUpdateZhongyiFeishu(t *testing.T) {
	t.Run("param err", func(t *testing.T) {
		w := httptest.NewRecorder()
		req := httptest.NewRequest("PUT", "/api/v1/data/node/zhongyi/feishu", nil)
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := UpdateBkCmdb(c)
		assert.Equal(t, err.Error(), "节点ID为必填字段")
	})

	t.Run("success", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()
		mockDb.ExpectQuery("SELECT count(*) FROM `data_nodes` WHERE `source_id` = ? AND `name` = ? AND deleted_at IS NULL").
			WithArgs(1, "zhongyi_feishu").
			WillReturnRows(mockDb.NewRows([]string{"count(*)"}).AddRow(0))
		mockDb.ExpectQuery("SELECT * FROM `data_node_configs` WHERE `key` =? AND value =?").
			WithArgs("ip", "***********").
			WillReturnRows(mockDb.NewRows(nil))

		patches := gomonkey.ApplyMethod(reflect.TypeOf(&http.Client{}), "Do", func(client *http.Client, req *http.Request) (*http.Response, error) {
			return &http.Response{
				StatusCode: http.StatusOK,
				Body:       ioutil.NopCloser(bytes.NewBufferString(`{"status":"ok"}`)),
			}, nil
		})
		defer patches.Reset()

		w := httptest.NewRecorder()
		req := httptest.NewRequest("PUT", "/api/v1/data/node/zhongyi/feishu", strings.NewReader(`{
			"id": 1,
			"name": "zhongyi_feishu",
			"ip": "***********",
			"repeat_type": "1",
			"start_time": "2024-1-21",
			"sources_id": 22,
			"appsecret": "1",
			"appkey": "1",
			"protocol": "https"
		}`))

		req.Header.Set("Content-Type", "application/json")

		// Create a Gin context
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		// Call the function under test
		err := UpdateZhongyiFeishu(c)
		assert.Nil(t, err)
	})
}

func TestZhongyiFeishuTest(t *testing.T) {
	s, err := miniredis.Run()
	if err != nil {
		t.Fatal(err)
	}
	defer s.Close()
	client := redis.NewClient(&redis.Options{Addr: s.Addr()})
	testcommon.SetRedisClient(client)

	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		res := map[string]any{
			"code":        200,
			"accessToken": "token",
			"expiresIn":   "10",
		}
		w.WriteHeader(http.StatusOK)
		_ = json.NewEncoder(w).Encode(res)
	}))
	defer server.Close()

	w := httptest.NewRecorder()
	req := httptest.NewRequest("POST", "/api/v1/data/node/zhongyi/feishu/test", strings.NewReader(fmt.Sprintf(`{
			"sources_id": 1,
			"name": "zhongyi_feishu",
			"ip": "%s",
			"area_type": "指定",
			"area_id": 1,
			"start_time": "2024-1-21",
			"sources_id": 22,
			"appsecret": "1",
			"appkey": "1",
			"repeat_type": "1",
			"protocol": "http",
			"unique_field": "name"
    	}`, server.Listener.Addr().String())))

	req.Header.Set("Content-Type", "application/json")

	c, _ := gin.CreateTestContext(w)
	c.Request = req

	err = ZhongyiFeishuTest(c)

	assert.Nil(t, err)
	assert.Equal(t, w.Body.String(), `{"code":0,"message":"Success","data":{}}`)
}
