package nodes

import (
	"fobrain/fobrain/common/response"

	"github.com/gin-gonic/gin"
)

func GetFileImportField(c *gin.Context) error {
	type ListFields struct {
		Name string `json:"name"`
		Key  string `json:"all_key"`
	}
	result := make([]ListFields, 0)
	result = append(result,
		ListFields{
			Name: "姓名",
			Key:  "name",
		}, ListFields{
			Name: "英文名",
			Key:  "english_name",
		}, ListFields{
			Name: "邮箱",
			Key:  "email",
		}, ListFields{
			Name: "手机号",
			Key:  "mobile",
		}, ListFields{
			Name: "工号",
			Key:  "work_number",
		}, ListFields{
			Name: "职位",
			Key:  "title",
		})
	return response.OkWithData(c, result)
}
