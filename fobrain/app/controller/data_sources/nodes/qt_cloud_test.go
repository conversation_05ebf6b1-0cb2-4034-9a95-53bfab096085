package nodes

import (
	"bytes"
	"encoding/json"
	"fmt"
	"fobrain/fobrain/app/services/node/qt_cloud"
	"fobrain/models/mysql/data_source"
	"io/ioutil"
	"net/http"
	"net/http/httptest"
	"reflect"
	"strings"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"

	testcommon "fobrain/fobrain/tests/common_test"
)

func TestQtCloud(t *testing.T) {
	t.Run("param err", func(t *testing.T) {
		w := httptest.NewRecorder()
		req := httptest.NewRequest("POST", "/api/v1/data/node/qty?page=1&per_page=10", nil)
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := QtCloud(c)
		assert.Nil(t, err)
	})

	t.Run("missing protocol scheme", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()
		mockDb.ExpectQuery("SELECT * FROM `data_sources` WHERE `id` = ? ORDER BY `data_sources`.`id` LIMIT 1").
			WillReturnRows(mockDb.NewRows([]string{"id", "name", "type"}).AddRow(1, "name", "qty"))
		mockDb.ExpectQuery("SELECT count(*) FROM `data_nodes` WHERE `source_id` = ? AND `name` = ? AND deleted_at IS NULL").
			WithArgs(1, "qty").
			WillReturnRows(mockDb.NewRows([]string{"count(*)"}).AddRow(0))
		mockDb.ExpectQuery("SELECT * FROM `data_node_configs` WHERE `key` =? AND value =?").
			WithArgs("ip", "***********").
			WillReturnRows(mockDb.NewRows(nil))

		w := httptest.NewRecorder()
		req := httptest.NewRequest("POST", "/api/v1/data/node/qty", strings.NewReader(`{
			"sources_id": 1,
			"name": "qty",
			"area_type": "指定",
			"area_id": 1,
			"ip": "***********",
			"app_code": "1",
			"account": "abel",
			"app_secret": "1",
			"repeat_type": "1",
			"start_time": "2024-1-21",
			"sources_id": 1,
			"appsecret": "1",
			"appkey": "1",
			"password": "sdfasdf"
		}`))
		req.Header.Set("Content-Type", "application/json")
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := QtCloud(c)
		assert.Nil(t, err)
		assert.Equal(t, w.Body.String(), "{\"code\":400,\"message\":\"parse \\\"://***********/v1/api/auth\\\": missing protocol scheme\",\"data\":{}}")
	})

	t.Run("success", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()

		mockDb.ExpectQuery("SELECT * FROM `data_sources` WHERE `id` = ? ORDER BY `data_sources`.`id` LIMIT 1").
			WillReturnRows(mockDb.NewRows([]string{"id", "name", "type"}).AddRow(1, "name", "qty"))

		mockDb.ExpectQuery("SELECT count(*) FROM `data_nodes` WHERE `source_id` = ? AND `name` = ? AND deleted_at IS NULL").
			WithArgs(1, "qty").
			WillReturnRows(mockDb.NewRows([]string{"count(*)"}).AddRow(0))

		mockDb.ExpectQuery("SELECT * FROM `data_node_configs` WHERE `key` =? AND value =?").
			WithArgs("ip", "***********").
			WillReturnRows(mockDb.NewRows(nil))

		mockDb.Mock.ExpectBegin()
		mockDb.ExpectExec(
			"INSERT INTO `data_nodes` (`created_at`,`updated_at`,`deleted_at`,`source_id`,`area_id`,`name`,`source`,`types`,`status`,`use`,`last_task_at`,`last_task_status`,`last_sync_result`,`data_types`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?)",
		).WillReturnResult(sqlmock.NewResult(1, 1))
		mockDb.Mock.ExpectCommit()

		mockDb.Mock.ExpectBegin()
		mockDb.ExpectExec(
			"INSERT INTO `data_node_configs` (`created_at`,`updated_at`,`node_id`,`key`,`value`,`hidden`) VALUES (?,?,?,?,?,?),(?,?,?,?,?,?),(?,?,?,?,?,?),(?,?,?,?,?,?),(?,?,?,?,?,?),(?,?,?,?,?,?),(?,?,?,?,?,?),(?,?,?,?,?,?)",
		).WillReturnResult(sqlmock.NewResult(1, 1))
		mockDb.Mock.ExpectCommit()

		mockDb.Mock.ExpectBegin()
		mockDb.ExpectExec(
			"INSERT INTO `data_node_logs` (`created_at`,`updated_at`,`node_id`,`name`,`log_type`,`content`) VALUES (?,?,?,?,?,?)",
		).WillReturnResult(sqlmock.NewResult(1, 1))
		mockDb.Mock.ExpectCommit()

		patches := gomonkey.ApplyMethod(reflect.TypeOf(&http.Client{}), "Do", func(client *http.Client, req *http.Request) (*http.Response, error) {
			return &http.Response{
				StatusCode: http.StatusOK,
				Body:       ioutil.NopCloser(bytes.NewBufferString(`{"status":"ok"}`)),
			}, nil
		})

		patchCli := gomonkey.ApplyMethodReturn(qt_cloud.NewQTCloud(), "GetToken", nil, nil)

		w := httptest.NewRecorder()
		req := httptest.NewRequest("POST", "/api/v1/data/node/qty", strings.NewReader(`{
			"sources_id": 1,
			"name": "qty",
			"area_type": "指定",
			"area_id": 1,
			"ip": "***********",
			"app_code": "1",
			"account": "abel",
			"app_secret": "1",
			"repeat_type": "1",
			"start_time": "2024-1-21",
			"sources_id": 1,
			"appsecret": "1",
			"appkey": "1",
			"password": "sdfasdf",
			"protocol": "https",
			"data_types": [1]
		}`))

		req.Header.Set("Content-Type", "application/json")

		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := QtCloud(c)

		patches.Reset()
		patchCli.Reset()

		assert.Nil(t, err)
	})
}

func TestCheckQtCloudStatus(t *testing.T) {
	t.Run("param err", func(t *testing.T) {
		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/v1/node/qty/:id/check_status", nil)
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := CheckQtCloudStatus(c)
		assert.Nil(t, err)
		assert.Equal(t, w.Body.String(), "{\"code\":400,\"message\":\"节点ID为必填字段\",\"data\":{}}")
	})
}

func TestUpdateQtCloud(t *testing.T) {
	t.Run("param err", func(t *testing.T) {
		w := httptest.NewRecorder()
		req := httptest.NewRequest("PUT", "/api/v1/data/node/foradar_saas", nil)
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := UpdateQtCloud(c)
		assert.Equal(t, err.Error(), "节点ID为必填字段")
	})

	t.Run("missing protocol scheme", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()
		mockDb.ExpectQuery("SELECT * FROM `data_node_configs` WHERE `key` =? AND value =?").
			WithArgs("ip", "***********").
			WillReturnRows(mockDb.NewRows(nil))

		w := httptest.NewRecorder()
		req := httptest.NewRequest("PUT", "/api/v1/data/node/foradar_saas", strings.NewReader(`{
			"id": 1,
			"name": "qty",
			"ip": "***********",
			"app_code": "1",
			"account": "abel",
			"app_secret": "1",
			"repeat_type": "1",
			"start_time": "2024-1-21",
			"sources_id": 1,
			"appsecret": "1",
			"appkey": "1",
			"password": "sdfasdf",
			"api_key": "1",
			"protocol": "https"
		}`))
		req.Header.Set("Content-Type", "application/json")
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := UpdateForadar(c)
		assert.Nil(t, err)
	})

	t.Run("success", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()

		mockDb.ExpectQuery("SELECT * FROM `data_node_configs` WHERE `key` =? AND value =?").
			WithArgs("ip", "***********").
			WillReturnRows(mockDb.NewRows(nil))

		mockDb.ExpectQuery("SELECT * FROM `data_nodes` WHERE `id` = ? AND `data_nodes`.`deleted_at` IS NULL ORDER BY `data_nodes`.`id` LIMIT 1").WithArgs(1).
			WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))

		mockDb.Mock.ExpectBegin()
		mockDb.ExpectExec(
			"UPDATE `data_nodes` SET `updated_at`=?,`name`=?,`use`=?,`data_types`=? WHERE `id` = ? AND `data_nodes`.`deleted_at` IS NULL",
		).WillReturnResult(sqlmock.NewResult(1, 1))
		mockDb.Mock.ExpectCommit()

		patchCli := gomonkey.ApplyMethodReturn(qt_cloud.NewQTCloud(), "GetToken", nil, nil)

		patchNodeConfig := gomonkey.ApplyMethodReturn(data_source.NewNodeConfigModel(), "Update", nil)

		w := httptest.NewRecorder()
		req := httptest.NewRequest("PUT", "/api/v1/data/node/foradar_saas", strings.NewReader(`{
			"id": 1,
			"name": "qty",
			"ip": "***********",
			"app_code": "1",
			"account": "abel",
			"app_secret": "1",
			"repeat_type": "1",
			"start_time": "2024-1-21",
			"sources_id": 1,
			"appsecret": "1",
			"appkey": "1",
			"password": "sdfasdf",
			"api_key": "1",
			"protocol": "https",
			"data_types": [1]
		}`))
		req.Header.Set("Content-Type", "application/json")
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := UpdateQtCloud(c)

		patchCli.Reset()
		patchNodeConfig.Reset()

		assert.Nil(t, err)
	})
}

func TestQtCloudTest(t *testing.T) {
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		res := map[string]any{
			"data": map[string]any{
				"jwt": "xx",
			},
		}
		w.WriteHeader(http.StatusOK)
		_ = json.NewEncoder(w).Encode(res)
	}))
	defer server.Close()

	w := httptest.NewRecorder()
	req := httptest.NewRequest("POST", "/api/v1/data/node/qty/test", strings.NewReader(fmt.Sprintf(`{
			"sources_id": 1,
			"name": "qty",
			"area_type": "指定",
			"area_id": 1,
			"ip": "%s",
			"app_code": "1",
			"account": "abel",
			"app_secret": "1",
			"repeat_type": "1",
			"start_time": "2024-1-21",
			"sources_id": 1,
			"appsecret": "1",
			"appkey": "1",
			"password": "sdfasdf",
			"protocol": "http",
			"data_types": [1]
		}`, server.Listener.Addr().String())))

	req.Header.Set("Content-Type", "application/json")

	c, _ := gin.CreateTestContext(w)
	c.Request = req

	err := QtCloudTest(c)
	assert.Nil(t, err)
	assert.Equal(t, w.Body.String(), `{"code":0,"message":"Success","data":{}}`)
}
