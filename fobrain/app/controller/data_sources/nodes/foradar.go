package nodes

import (
	"encoding/json"
	"fmt"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"

	"fobrain/fobrain/app/services/node/foradar"
	"fobrain/fobrain/app/services/node/logs"
	"fobrain/fobrain/common/request"
	"fobrain/fobrain/common/response"
	"fobrain/initialize/mysql"
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/workbench"
	"fobrain/pkg/utils"
)

func FoRadar(c *gin.Context) error {
	params, err := request.Validate(c, &struct {
		SourceId    uint64      `json:"sources_id" uri:"sources_id" form:"sources_id" validate:"required" zh:"数据源ID"`
		Name        string      `json:"name" uri:"name" form:"name" validate:"required" zh:"节点名称"`
		IP          string      `json:"ip" uri:"ip" form:"ip"  validate:"required" zh:"节点地址"`
		RepeatType  string      `json:"repeat_type" uri:"repeat_type" form:"repeat_type" validate:"required" zh:"数据同步配置"`
		Date        string      `json:"date" uri:"date" form:"date" zh:"数据同步配置"`
		StartTime   string      `json:"start_time" uri:"start_time" form:"start_time" validate:"required" zh:"数据同步配置"`
		DataType    string      `json:"data_type" uri:"data_type" form:"data_type" zh:"时间"`
		Protocol    string      `json:"protocol" uri:"protocol" form:"protocol"  default:"https" zh:"协议"`
		ApiKey      string      `json:"api_key" uri:"api_key" form:"api_key" validate:"required"  zh:"api_key"`
		Proxy       string      `json:"proxy" uri:"proxy" form:"proxy" zh:"代理地址"`
		AreaId      uint64      `json:"area_id" uri:"area_id" form:"area_id" zh:"区域ID"`
		AreaType    string      `json:"area_type" uri:"area_type" form:"area_type" validate:"required,oneof=指定 规则" zh:"区域,指定,规则"`
		AreaRules   []*AreaRule `json:"area_rules" uri:"area_rules" form:"area_rules" zh:"区域规则"`
		NetworkType string      `json:"network_type" uri:"network_type" form:"network_type" zh:"网络类型"`
		DataTypes   []int       `json:"data_types" uri:"data_types" form:"data_types" zh:"节点类型"`
		PersonField string      `json:"person_field" uri:"person_field" form:"person_field" zh:"人员映射字段"`
	}{})
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	// 区域为规则时
	if params.AreaType == "规则" {
		if len(params.AreaRules) == 0 {
			return response.FailWithMessage(c, "区域规则不能为空")
		}
		// 检查区域规则
		if err := CheckAreaRules(params.AreaRules); err != nil {
			return response.FailWithMessage(c, err.Error())
		}
	}
	sourceInfo, err := data_source.NewSourceModel().First(mysql.WithWhere("id", params.SourceId))
	if err != nil {
		return response.FailWithMessage(c, "数据源验证不通过")
	}
	if sourceInfo.Type != "foradar_saas" {
		return response.FailWithMessage(c, "数据源验证不通过")
	}
	num, err := data_source.NewNodeModel().Total(mysql.WithWhere("source_id", params.SourceId), mysql.WithWhere("name", params.Name))
	if err != nil {
		return response.FailWithMessage(c, "该节点已添加,请勿重复添加")
	}
	if num > 0 {
		return response.FailWithMessage(c, "该节点已添加,请勿重复添加")
	}
	if exists, err := data_source.NewNodeConfigModel().IpExists(0, params.SourceId, params.IP); exists || err != nil {
		return response.FailWithMessage(c, "该IP已存在,请勿重复添加")
	}

	// 将节点的任务类型保存为以 , 分隔的数字类型的字符串
	typesSlice := cast.ToStringSlice(params.DataTypes)
	types := strings.Join(typesSlice, ",")

	node := data_source.Node{
		SourceId:  params.SourceId,
		Name:      params.Name,
		Source:    sourceInfo.Type,
		Status:    3,
		Use:       1,
		AreaId:    params.AreaId,
		AreaType:  params.AreaType,
		DataTypes: types,
	}
	if params.AreaType == "规则" {
		areaRules, err := json.Marshal(params.AreaRules)
		if err != nil {
			return response.FailWithMessage(c, err.Error())
		}
		node.AreaRules = string(areaRules)
	} else {
		node.AreaRules = ""
	}
	// 验证节点配置能否连接上
	cli := foradar.NewFORadar()
	if err := cli.SetConfig(map[string]any{
		"proxy": params.Proxy, "ip": params.IP, "protocol": params.Protocol, "api_key": params.ApiKey,
	}); err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	_, err = cli.GetAssets(1, 1)
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}

	err = data_source.NewNodeModel().Create(&node).Error
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}

	list, _ := utils.StructToMap(params, "json")
	nodeConfigs := make([]data_source.DataNodeConfig, 0, len(list))

	for k, v := range list {
		nodeConfigs = append(nodeConfigs, data_source.DataNodeConfig{
			NodeId: node.Id,
			Key:    k,
			Value:  cast.ToString(v),
		})
	}
	err = data_source.NewNodeConfigModel().Create(&nodeConfigs).Error
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	logData := logs.Create{
		IP:        params.IP,
		AwardCode: "",
		DataSyncConfig: logs.DataSyncConfig{
			RepeatType:  params.RepeatType,
			Date:        params.Date,
			StartTime:   params.StartTime,
			Version:     "",
			NetDomainId: "",
		},
	}
	// 日志-新增节点
	err = logs.LogCreate(node.Id, params.Name, logData, logs.NodeLogCreate)
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	return response.Ok(c)
}

func CheckFoRadarStatus(c *gin.Context) error {
	params, err := request.Validate(c, &struct {
		Id uint64 `json:"id" uri:"id" form:"id" validate:"required" zh:"节点ID"`
	}{})

	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}

	node, err := data_source.NewNodeModel().First(mysql.WithColumnValue("id", params.Id))
	if err != nil {
		return err
	}

	cli := foradar.NewFORadar()
	if err = cli.SetNode(node.Id); err != nil {
		return err
	}

	_, err = cli.GetAssets(1, 1)
	if err != nil {
		node.Status = data_source.StatusNONormal
		workbench.ProbeAbnormalSend(fmt.Sprintf("FORADAR %d探针异常", params.Id))

	} else {
		node.Status = data_source.StatusNormal
	}

	err = data_source.NewNodeModel().Update(*node)
	if err != nil {
		return err
	}
	return response.Ok(c)
}

func UpdateForadar(ctx *gin.Context) error {
	params, err := request.Validate(ctx, &struct {
		Id          uint64 `json:"id" uri:"id" form:"id" validate:"required,number" zh:"节点ID"`
		Name        string `json:"name" uri:"name" form:"name" validate:"required" zh:"节点名称"`
		IP          string `json:"ip" uri:"ip" form:"ip"  validate:"required" zh:"节点地址"`
		RepeatType  string `json:"repeat_type" uri:"repeat_type" form:"repeat_type" validate:"required" zh:"数据同步配置"`
		Date        string `json:"date" uri:"date" form:"date" zh:"数据同步配置"`
		StartTime   string `json:"start_time" uri:"start_time" form:"start_time" validate:"required" zh:"数据同步配置"`
		DataType    string `json:"data_type" uri:"data_type" form:"data_type" zh:"时间"`
		Protocol    string `json:"protocol" uri:"protocol" form:"protocol"  default:"https" zh:"协议"`
		ApiKey      string `json:"api_key" uri:"api_key" form:"api_key" validate:"required"  zh:"api_key"`
		Proxy       string `json:"proxy" uri:"proxy" form:"proxy" zh:"代理地址"`
		AreaId      uint64 `json:"area_id" uri:"area_id" form:"area_id" validate:"number" zh:"区域ID"`
		NetworkType string `json:"network_type" uri:"network_type" form:"network_type" zh:"网络类型"`
		DataTypes   []int  `json:"data_types" uri:"data_types" form:"data_types" zh:"节点类型"`
		PersonField string `json:"person_field" uri:"person_field" form:"person_field" zh:"人员映射字段"`
	}{})
	if err != nil {
		return err
	}
	if exists, err := data_source.NewNodeConfigModel().IpExists(params.Id, 0, params.IP); exists || err != nil {
		return response.FailWithMessage(ctx, "该IP已存在,请勿重复添加")
	}

	// 验证FORadar节点配置能否连接上
	cli := foradar.NewFORadar()
	if err := cli.SetConfig(map[string]any{
		"proxy": params.Proxy, "ip": params.IP, "protocol": params.Protocol, "api_key": params.ApiKey,
	}); err != nil {
		return response.FailWithMessage(ctx, err.Error())
	}
	_, err = cli.GetAssets(1, 1)
	if err != nil {
		return response.FailWithMessage(ctx, err.Error())
	}

	// 将节点的任务类型保存为以 , 分隔的数字类型的字符串
	typesSlice := cast.ToStringSlice(params.DataTypes)
	types := strings.Join(typesSlice, ",")

	nodeInfo, err := data_source.NewNodeModel().First(mysql.WithColumnValue("id", params.Id))
	if err != nil {
		return err
	}
	nodeInfo.Name = params.Name
	nodeInfo.AreaId = params.AreaId
	nodeInfo.DataTypes = types
	err = data_source.NewNodeModel().Updates(*nodeInfo, mysql.WithSelect("name", "use", "data_types"))
	if err != nil {
		return err
	}
	//json转map
	list, _ := utils.StructToMap(params, "json")
	for k, v := range list {
		if k == "id" || k == "name" || k == "area_id" || v == "" {
			continue
		}
		err := data_source.NewNodeConfigModel().Update(data_source.DataNodeConfig{
			NodeId: nodeInfo.Id,
			Key:    k,
			Value:  cast.ToString(v),
		})
		if err != nil {
			return response.FailWithMessage(ctx, err.Error())
		}
	}
	return response.Ok(ctx)
}

func FoRadarTest(c *gin.Context) error {
	params, err := request.Validate(c, &struct {
		IP       string `json:"ip" uri:"ip" form:"ip"  validate:"required" zh:"节点地址"`
		Protocol string `json:"protocol" uri:"protocol" form:"protocol"  default:"https" zh:"协议"`
		ApiKey   string `json:"api_key" uri:"api_key" form:"api_key" validate:"required"  zh:"api_key"`
		Proxy    string `json:"proxy" uri:"proxy" form:"proxy" zh:"代理地址"`
	}{})
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	cli := foradar.NewFORadar()
	if err := cli.SetConfig(map[string]any{
		"proxy": params.Proxy, "ip": params.IP, "protocol": params.Protocol, "api_key": params.ApiKey,
	}); err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	_, err = cli.GetAssets(1, 1)
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	return response.Ok(c)
}
