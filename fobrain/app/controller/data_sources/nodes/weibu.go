package nodes

import (
	"encoding/json"
	"fmt"
	"strings"

	"fobrain/fobrain/app/services/node/weibu"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"

	"fobrain/fobrain/app/services/node/logs"
	"fobrain/fobrain/common/request"
	"fobrain/fobrain/common/response"
	"fobrain/initialize/mysql"
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/workbench"
	"fobrain/pkg/utils"
)

type WeiBuBase struct {
	Name        string      `json:"name" uri:"name" form:"name" validate:"required" zh:"节点名称"`
	Appsecret   string      `json:"appsecret" uri:"appsecret" form:"appsecret"  validate:"required" zh:"安全秘钥"`
	Appkey      string      `json:"appkey" uri:"appkey" form:"appkey"  validate:"required" zh:"安全秘钥"`
	RepeatType  string      `json:"repeat_type" uri:"repeat_type" form:"repeat_type" validate:"required" zh:"数据同步配置"`
	Date        string      `json:"date" uri:"date" form:"date" zh:"数据同步配置"`
	StartTime   string      `json:"start_time" uri:"start_time" form:"start_time" validate:"required" zh:"数据同步配置"`
	AreaId      uint64      `json:"area_id" uri:"area_id" form:"area_id" zh:"区域ID"`
	AreaType    string      `json:"area_type" uri:"area_type" form:"area_type" validate:"required,oneof=指定 规则" zh:"区域,指定,规则"`
	AreaRules   []*AreaRule `json:"area_rules" uri:"area_rules" form:"area_rules" zh:"区域规则"`
	IP          string      `json:"ip" uri:"ip" form:"types"  ip:"required,ipv4" zh:"节点地址"`
	Protocol    string      `json:"protocol" uri:"protocol" form:"protocol"  default:"https" zh:"协议"`
	NetworkType string      `json:"network_type" uri:"network_type" form:"network_type" zh:"网络类型"`
	DataTypes   []int       `json:"data_types" uri:"data_types" form:"data_types" zh:"节点类型"`
}

type WeiBuCreate struct {
	SourcesId   uint64 `json:"sources_id" uri:"sources_id" form:"sources_id" validate:"required" zh:"数据源ID"`
	Types       string `json:"types" uri:"types" form:"types" zh:"节点类型"`
	PersonField string `json:"person_field" uri:"person_field" form:"person_field" zh:"人员映射字段"`
	WeiBuBase
}

type WeiBuUpdate struct {
	Id          uint64 `json:"id" uri:"id" form:"id" validate:"required,number" zh:"节点ID"`
	PersonField string `json:"person_field" uri:"person_field" form:"person_field" zh:"人员映射字段"`
	WeiBuBase
}

func WeiBuNode(c *gin.Context) error {
	params, err := request.Validate(c, &WeiBuCreate{})
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	// 区域为规则时
	if params.AreaType == "规则" {
		if len(params.AreaRules) == 0 {
			return response.FailWithMessage(c, "区域规则不能为空")
		}
		// 检查区域规则
		if err := CheckAreaRules(params.AreaRules); err != nil {
			return response.FailWithMessage(c, err.Error())
		}
	}
	sourceInfo, err := data_source.NewSourceModel().First(mysql.WithWhere("id", params.SourcesId))
	if err != nil {
		return response.FailWithMessage(c, "数据源验证不通过")
	}
	if sourceInfo.Type != "tdp" {
		return response.FailWithMessage(c, "数据源验证不通过")
	}
	num, err := data_source.NewNodeModel().Total(mysql.WithWhere("source_id", params.SourcesId), mysql.WithWhere("name", params.Name))
	if err != nil {
		return response.FailWithMessage(c, "该节点已添加,请勿重复添加")
	}
	if num > 0 {
		return response.FailWithMessage(c, "该节点已添加,请勿重复添加")
	}
	if exists, err := data_source.NewNodeConfigModel().IpExists(0, params.SourcesId, params.IP); exists || err != nil {
		return response.FailWithMessage(c, "该IP已存在,请勿重复添加")
	}

	// 鉴权
	cli := weibu.NewWeiBu(nil, nil)
	if err = cli.SetConfig(map[string]any{
		"protocol":  params.Protocol,
		"ip":        params.IP,
		"appkey":    params.Appkey,
		"appsecret": params.Appsecret,
	}); err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	if err = cli.GetStatus(); err != nil {
		return response.FailWithMessage(c, "节点校验失败，请检查节点状态")
	}

	// 将节点的任务类型保存为以 , 分隔的数字类型的字符串
	typesSlice := cast.ToStringSlice(params.DataTypes)
	types := strings.Join(typesSlice, ",")

	node := data_source.Node{
		SourceId:  params.SourcesId,
		Name:      params.Name,
		Source:    sourceInfo.Type,
		Types:     params.Types,
		Status:    3,
		Use:       1,
		AreaType:  params.AreaType,
		AreaId:    params.AreaId,
		DataTypes: types,
	}
	if params.Protocol != "https" && params.Protocol != "http" {
		return response.FailWithMessage(c, "protocol必须为htt或https")
	}
	if params.AreaType == "规则" {
		areaRules, err := json.Marshal(params.AreaRules)
		if err != nil {
			return response.FailWithMessage(c, err.Error())
		}
		node.AreaRules = string(areaRules)
	} else {
		node.AreaRules = ""
	}

	err = data_source.NewNodeModel().Create(&node).Error
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	// 生成配置信息
	list, _ := utils.StructToMap(params.WeiBuBase, "json")
	nodeConfigs := make([]data_source.DataNodeConfig, 0, len(list))

	for k, v := range list {
		nodeConfigs = append(nodeConfigs, data_source.DataNodeConfig{
			NodeId: node.Id,
			Key:    k,
			Value:  cast.ToString(v),
		})
	}
	err = data_source.NewNodeConfigModel().Create(&nodeConfigs).Error
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	logData := logs.Create{
		IP:        params.IP,
		AwardCode: "",
		DataSyncConfig: logs.DataSyncConfig{
			RepeatType:  params.RepeatType,
			Date:        params.Date,
			StartTime:   params.StartTime,
			Version:     "",
			NetDomainId: "",
		},
	}
	// 日志-新增节点
	err = logs.LogCreate(node.Id, params.Name, logData, logs.NodeLogCreate)
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	return response.Ok(c)
}

func CheckWeiBuStatus(c *gin.Context) error {
	params, err := request.Validate(c, &struct {
		Id uint64 `json:"id" uri:"id" form:"id" validate:"required" zh:"节点ID"`
	}{})

	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}

	node, err := data_source.NewNodeModel().First(mysql.WithColumnValue("id", params.Id))
	if err != nil {
		return err
	}

	cli := weibu.NewWeiBu(nil, nil)
	if err = cli.SetNode(node.Id); err != nil {
		return err
	}

	err = cli.GetStatus()
	if err != nil {
		node.Status = data_source.StatusNONormal
		workbench.ProbeAbnormalSend(fmt.Sprintf("微步TDP %d探针异常", params.Id))
	} else {
		node.Status = data_source.StatusNormal
	}

	err = data_source.NewNodeModel().Update(*node)
	if err != nil {
		return err
	}

	return response.Ok(c)
}

func UpdateWeiBu(ctx *gin.Context) error {
	params, err := request.Validate(ctx, &WeiBuUpdate{})
	if err != nil {
		return err
	}

	if params.Protocol != "https" && params.Protocol != "http" {
		return response.FailWithMessage(ctx, "protocol必须为htt或https")
	}

	if exists, err := data_source.NewNodeConfigModel().IpExists(params.Id, 0, params.IP); exists || err != nil {
		return response.FailWithMessage(ctx, "该IP已存在,请勿重复添加")
	}

	// 鉴权
	cli := weibu.NewWeiBu(nil, nil)
	if err = cli.SetConfig(map[string]any{
		"protocol":  params.Protocol,
		"ip":        params.IP,
		"appkey":    params.Appkey,
		"appsecret": params.Appsecret,
	}); err != nil {
		return response.FailWithMessage(ctx, err.Error())
	}
	if err = cli.GetStatus(); err != nil {
		return response.FailWithMessage(ctx, "节点校验失败，请检查节点状态")
	}

	// 将节点的任务类型保存为以 , 分隔的数字类型的字符串
	typesSlice := cast.ToStringSlice(params.DataTypes)
	types := strings.Join(typesSlice, ",")

	nodeInfo, err := data_source.NewNodeModel().First(mysql.WithColumnValue("id", params.Id))
	if err != nil {
		return err
	}
	nodeInfo.Name = params.Name
	nodeInfo.AreaId = params.AreaId
	nodeInfo.DataTypes = types
	err = data_source.NewNodeModel().Updates(*nodeInfo, mysql.WithSelect("name", "use", "data_types"))
	if err != nil {
		return err
	}
	//json转map
	list, _ := utils.StructToMap(params.WeiBuBase, "json")
	for k, v := range list {
		if k == "id" || k == "name" || k == "area_id" {
			continue
		}
		err := data_source.NewNodeConfigModel().Update(data_source.DataNodeConfig{
			NodeId: nodeInfo.Id,
			Key:    k,
			Value:  cast.ToString(v),
		})
		if err != nil {
			return response.FailWithMessage(ctx, err.Error())
		}
	}
	return response.Ok(ctx)
}

func WeiBuNodeTest(c *gin.Context) error {
	params, err := request.Validate(c, &WeiBuCreate{})
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	cli := weibu.NewWeiBu(nil, nil)
	if err = cli.SetConfig(map[string]any{
		"protocol":  params.Protocol,
		"ip":        params.IP,
		"appkey":    params.Appkey,
		"appsecret": params.Appsecret,
	}); err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	if err = cli.GetStatus(); err != nil {
		return response.FailWithMessage(c, "节点校验失败，请检查节点状态")
	}
	return response.Ok(c)
}
