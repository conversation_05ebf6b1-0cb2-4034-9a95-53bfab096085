package nodes

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/agiledragon/gomonkey/v2"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"

	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/models/mysql/workbench"
)

func TestFoeye(t *testing.T) {
	t.Run("param err", func(t *testing.T) {
		w := httptest.NewRecorder()
		req := httptest.NewRequest("PUT", "/api/v1/data/node/bk_cmdb?page=1&per_page=10", nil)
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := FoEye(c)
		assert.Nil(t, err)
	})

	t.Run("missing protocol scheme", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()
		mockDb.ExpectQuery("SELECT * FROM `data_sources` WHERE `id` = ? ORDER BY `data_sources`.`id` LIMIT 1").
			WillReturnRows(mockDb.NewRows([]string{"id", "name", "type"}).AddRow(1, "foeye", "foeye"))
		mockDb.ExpectQuery("SELECT count(*) FROM `data_nodes` WHERE `source_id` = ? AND `name` = ? AND deleted_at IS NULL").
			WithArgs(1, "foeye").
			WillReturnRows(mockDb.NewRows([]string{"count(*)"}).AddRow(0))
		mockDb.ExpectQuery("SELECT * FROM `data_node_configs` WHERE `key` =? AND value =?").
			WithArgs("ip", "***********").
			WillReturnRows(mockDb.NewRows(nil))
		mockDb.ExpectQuery("SELECT `id` FROM `data_nodes` WHERE source_id = ?").
			WillReturnRows(mockDb.NewRows([]string{"id"}).AddRow(1))
		mockDb.ExpectQuery("SELECT count(*) FROM `data_node_configs` WHERE (`key` =? AND value =?) AND `node_id` =?").
			WithArgs("ip", "***********", 1).
			WillReturnRows(mockDb.NewRows([]string{"count(*)"}).AddRow(1))

		w := httptest.NewRecorder()
		req := httptest.NewRequest("PUT", "/api/v1/data/node/bk_cmdb", strings.NewReader(`{
			"sources_id": 1,
			"name": "foeye",
			"area_type": "指定",
			"ip": "***********",
			"app_code": "1",
			"api_key": "mockKey",
			"repeat_type": "1",
			"start_time": "2024-1-21",
			"sources_id": 1,
			"appsecret": "1",
			"appkey": "1",
			"password": "sdfasdf"
		}`))
		req.Header.Set("Content-Type", "application/json")
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := FoEye(c)
		assert.Nil(t, err)
		assert.Equal(t, w.Body.String(), "{\"code\":400,\"message\":\"寻找Ip错误\",\"data\":{}}")
	})

	t.Run("success", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()
		server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.Header().Set("Content-Type", "application/json")

			w.WriteHeader(http.StatusOK)
			w.Write([]byte(`{"err": "", "msg": "success", "data": {"content": [{"id": "engine1"}, {"id": "engine2"}]}}`))
		}))
		defer server.Close()

		mockDb.ExpectQuery("SELECT * FROM `data_nodes` WHERE `id` = ?").WithArgs(1).
			WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))

		mockDb.ExpectQuery("SELECT * FROM `data_node_configs` WHERE `key` =? AND value =?").
			WithArgs("ip", server.Listener.Addr().String()).
			WillReturnRows(mockDb.NewRows(nil))

		mockDb.Mock.ExpectBegin()
		mockDb.ExpectExec("UPDATE `data_nodes`").WillReturnResult(sqlmock.NewResult(1, 1))
		mockDb.Mock.ExpectCommit()

		mockDb.Mock.ExpectBegin()
		mockDb.ExpectExec("UPDATE `data_node_configs`").WillReturnResult(sqlmock.NewResult(1, 1))
		mockDb.Mock.ExpectCommit()

		mockDb.Mock.ExpectBegin()
		mockDb.ExpectExec("UPDATE `data_node_configs`").WillReturnResult(sqlmock.NewResult(1, 1))
		mockDb.Mock.ExpectCommit()

		mockDb.Mock.ExpectBegin()
		mockDb.ExpectExec("UPDATE `data_node_configs`").WillReturnResult(sqlmock.NewResult(1, 1))
		mockDb.Mock.ExpectCommit()

		mockDb.Mock.ExpectBegin()
		mockDb.ExpectExec("UPDATE `data_node_configs`").WillReturnResult(sqlmock.NewResult(1, 1))
		mockDb.Mock.ExpectCommit()

		mockDb.Mock.ExpectBegin()
		mockDb.ExpectExec("UPDATE `data_node_configs`").WillReturnResult(sqlmock.NewResult(1, 1))
		mockDb.Mock.ExpectCommit()

		mockDb.Mock.ExpectBegin()
		mockDb.ExpectExec("UPDATE `data_node_configs`").WillReturnResult(sqlmock.NewResult(1, 1))
		mockDb.Mock.ExpectCommit()

		w := httptest.NewRecorder()
		req := httptest.NewRequest("PUT", "/api/v1/data/node/d01", strings.NewReader(fmt.Sprintf(
			`{
			"id": 1,
			"name": "d01",
			"protocol": "https",
			"ip": "%s",
			"app_code": "1",
			"api_key": "mockKey",
			"repeat_type": "1",
			"password": "1",
			"start_time": "2024-1-21",
			"data_types": [1]
		}`, server.Listener.Addr().String(),
		)))
		req.Header.Set("Content-Type", "application/json")
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := FoEye(c)

		assert.Nil(t, err)
	})
}

func TestCheckFoEyeStatus(t *testing.T) {
	t.Run("param err", func(t *testing.T) {
		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/v1/node/foeye/:id/check_status", nil)
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req
		gomonkey.ApplyFuncReturn(workbench.ProbeAbnormalSend)
		err := CheckFoEyeStatus(c)
		assert.Nil(t, err)
		assert.Equal(t, w.Body.String(), "{\"code\":400,\"message\":\"节点ID为必填字段\",\"data\":{}}")
	})
}

func TestUpdateFoeye(t *testing.T) {
	t.Run("param err", func(t *testing.T) {
		w := httptest.NewRecorder()
		req := httptest.NewRequest("PUT", "/api/v1/data/node/foeye", nil)
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := UpdateFoeye(c)
		assert.Equal(t, err.Error(), "节点ID为必填字段")
	})

	t.Run("missing protocol scheme", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()
		mockDb.ExpectQuery("SELECT * FROM `data_nodes` WHERE `id` = ? AND `data_nodes`.`deleted_at` IS NULL ORDER BY `data_nodes`.`id` LIMIT 1").
			WillReturnRows(mockDb.NewRows([]string{"id", "name", "type"}).AddRow(1, "name", "foeye"))
		mockDb.ExpectQuery("SELECT * FROM `data_node_configs` WHERE `key` =? AND value =?").
			WithArgs("ip", "***********").
			WillReturnRows(mockDb.NewRows(nil))

		w := httptest.NewRecorder()
		req := httptest.NewRequest("PUT", "/api/v1/data/node/foeye", strings.NewReader(`{
			"id": 1,
			"name": "foeye",
			"ip": "***********",
			"app_code": "1",
			"api_key": "mockKey",
			"repeat_type": "1",
			"password": "1",
			"start_time": "2024-1-21",
			"protocol": "https"
		}`))
		req.Header.Set("Content-Type", "application/json")
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := UpdateFoeye(c)
		assert.Nil(t, err)
		assert.Equal(t, w.Body.String(), `{"code":400,"message":"连接节点地址超时","data":{}}`)
	})
	/*
		t.Run("success", func(t *testing.T) {
			mockDb := testcommon.InitSqlMock()
			server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				w.Header().Set("Content-Type", "application/json")

				w.WriteHeader(http.StatusOK)
				w.Write([]byte(`{"err": "", "msg": "success", "data": {"content": [{"id": "engine1"}, {"id": "engine2"}]}}`))
			}))
			defer server.Close()

			mockDb.ExpectQuery("SELECT * FROM `data_nodes` WHERE `id` = ?").WithArgs(1).
				WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))

			mockDb.ExpectQuery("SELECT * FROM `data_node_configs` WHERE `key` =? AND value =?").
				WithArgs("ip", server.Listener.Addr().String()).
				WillReturnRows(mockDb.NewRows(nil))

			mockDb.Mock.ExpectBegin()
			mockDb.ExpectExec(
				"UPDATE `data_nodes` SET `updated_at`=?,`name`=?,`use`=?,`data_types`=? WHERE `id` = ? AND `data_nodes`.`deleted_at` IS NULL",
			).WillReturnResult(sqlmock.NewResult(1, 1))
			mockDb.ExpectCommit()

			mockDb.ExpectQuery("SELECT * FROM `data_node_configs` WHERE node_id=? AND `key`=? ORDER BY `data_node_configs`.`id` LIMIT 1").
				WithArgs(1, sqlmock.AnyArg()).
				WillReturnRows(mockDb.NewRows(nil))

			mockDb.ExpectBegin()
			mockDb.ExpectExec("INSERT INTO `data_node_configs` (`created_at`,`updated_at`,`node_id`,`key`,`value`,`hidden`) VALUES (?,?,?,?,?,?)").
				WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg()).
				WillReturnResult(sqlmock.NewResult(1, 1))
			mockDb.ExpectCommit()

			// 需要更新 6 次 data_node_configs 表
			for i := 0; i < 6; i++ {
				mockDb.ExpectQuery("SELECT * FROM `data_node_configs` WHERE node_id=? AND `key`=? ORDER BY `data_node_configs`.`id` LIMIT 1").
					WithArgs(1, sqlmock.AnyArg()).
					WillReturnRows(mockDb.NewRows(nil))

				mockDb.ExpectBegin()
				mockDb.ExpectExec("INSERT INTO `data_node_configs` (`created_at`,`updated_at`,`node_id`,`key`,`value`,`hidden`) VALUES (?,?,?,?,?,?)").
					WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg()).
					WillReturnResult(sqlmock.NewResult(1, 1))
				mockDb.ExpectCommit()
			}

			patches := gomonkey.ApplyMethodReturn(foeye.NewFoeye(), "CheckedNodeStatus", "", nil)

			w := httptest.NewRecorder()
			req := httptest.NewRequest("PUT", "/api/v1/data/node/d01", strings.NewReader(fmt.Sprintf(
				`{
				"id": 1,
				"name": "d01",
				"protocol": "https",
				"ip": "%s",
				"app_code": "1",
				"api_key": "mockKey",
				"repeat_type": "1",
				"password": "1",
				"start_time": "2024-1-21",
				"data_types": [1]
			}`, server.Listener.Addr().String(),
			)))
			req.Header.Set("Content-Type", "application/json")
			c, _ := gin.CreateTestContext(w)
			c.Request = req

			err := UpdateFoeye(c)

			patches.Reset()
			mockDb.Close()

			assert.Nil(t, err)
			//assert.Equal(t, w.Body.String(), `{"code":0,"message":"Success","data":{}}`)
		})
	*/
}

func TestFoeyeTest(t *testing.T) {
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		res := map[string]any{
			"code": 200,
			"data": map[string]any{},
		}
		w.WriteHeader(http.StatusOK)
		_ = json.NewEncoder(w).Encode(res)
	}))
	defer server.Close()

	w := httptest.NewRecorder()
	req := httptest.NewRequest("PUT", "/api/v1/data/node/foeye/test", strings.NewReader(fmt.Sprintf(
		`{
			"id": 1,
			"name": "d01",
			"protocol": "http",
			"ip": "%s",
			"app_code": "1",
			"api_key": "mockKey",
			"repeat_type": "1",
			"password": "1",
			"start_time": "2024-1-21",
			"data_types": [1]
		}`, server.Listener.Addr().String(),
	)))
	req.Header.Set("Content-Type", "application/json")
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	err := FoEyeTest(c)

	assert.Nil(t, err)
	assert.Equal(t, w.Body.String(), `{"code":0,"message":"Success","data":{}}`)
}
