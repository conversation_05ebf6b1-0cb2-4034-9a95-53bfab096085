package nodes

import (
	"fmt"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"

	"fobrain/fobrain/app/services/node/dingtalk"
	"fobrain/fobrain/app/services/node/logs"
	"fobrain/fobrain/common/request"
	"fobrain/fobrain/common/response"
	"fobrain/initialize/mysql"
	dingtalk_model "fobrain/models/elastic/source/dingtalk"
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/workbench"
	"fobrain/pkg/utils"
)

func DingTalk(c *gin.Context) error {
	params, err := request.Validate(c, &struct {
		SourceId    uint64 `json:"sources_id" uri:"sources_id" form:"sources_id" validate:"required" zh:"数据源ID"`
		Types       string `json:"types" uri:"types" form:"types" zh:"节点类型"`
		Name        string `json:"name" uri:"name" form:"name" validate:"required" zh:"节点名称"`
		Appsecret   string `json:"appsecret" uri:"appsecret" form:"appsecret"  validate:"required" zh:"安全秘钥"`
		Appkey      string `json:"appkey" uri:"appkey" form:"appkey"  validate:"required" zh:"安全秘钥"`
		RepeatType  string `json:"repeat_type" uri:"repeat_type" form:"repeat_type" validate:"required" zh:"数据同步配置"`
		Date        string `json:"date" uri:"date" form:"date" zh:"数据同步配置"`
		StartTime   string `json:"start_time" uri:"start_time" form:"start_time" validate:"required" zh:"数据同步配置"`
		AreaId      uint64 `json:"area_id" uri:"area_id" form:"area_id" zh:"区域ID"`
		IP          string `json:"ip" uri:"ip" form:"types"  ip:"required,ipv4" zh:"节点地址"`
		Protocol    string `json:"protocol" uri:"protocol" form:"protocol" zh:"协议"`
		DataTypes   []int  `json:"data_types" uri:"data_types" form:"data_types" zh:"节点类型"`
		UniqueField string `json:"unique_field" uri:"unique_field" form:"unique_field" validate:"required" zh:"唯一字段"`
	}{})
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	sourceInfo, err := data_source.NewSourceModel().First(mysql.WithWhere("id", params.SourceId))
	if err != nil {
		return response.FailWithMessage(c, "数据源验证不通过")
	}
	if sourceInfo.Type != "dingtalk" {
		return response.FailWithMessage(c, "数据源验证不通过")
	}
	num, err := data_source.NewNodeModel().Total(mysql.WithWhere("source_id", params.SourceId), mysql.WithWhere("name", params.Name))
	if err != nil {
		return response.FailWithMessage(c, "该节点已添加,请勿重复添加")
	}
	if num > 0 {
		return response.FailWithMessage(c, "该节点已添加,请勿重复添加")
	}
	if exists, err := data_source.NewNodeConfigModel().IpExists(0, params.SourceId, params.IP); exists || err != nil {
		return response.FailWithMessage(c, "该IP已存在,请勿重复添加")
	}

	//params.IP = "oapi.dingtalk.com"
	//params.Protocol = "https"
	if params.Protocol != "https" {
		return response.FailWithMessage(c, "protocol必须为https")
	}

	// 鉴权
	cli := dingtalk.NewDingtalk()
	if err = cli.SetConfig(map[string]any{
		"protocol":  params.Protocol,
		"ip":        params.IP,
		"appkey":    params.Appkey,
		"appsecret": params.Appsecret,
	}); err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	if _, err = cli.GetToken(); err != nil {
		return response.FailWithMessage(c, "节点校验失败，请检查节点状态")
	}

	// 将节点的任务类型保存为以 , 分隔的数字类型的字符串
	typesSlice := cast.ToStringSlice(params.DataTypes)
	types := strings.Join(typesSlice, ",")

	node := data_source.Node{
		SourceId:  params.SourceId,
		Name:      params.Name,
		Source:    sourceInfo.Type,
		Types:     params.Types,
		Status:    3,
		Use:       1,
		AreaId:    params.AreaId,
		DataTypes: types,
	}

	err = data_source.NewNodeModel().Create(&node).Error
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	// 生成配置信息
	list, _ := utils.StructToMap(params, "json")
	nodeConfigs := make([]data_source.DataNodeConfig, 0, len(list))

	for k, v := range list {
		nodeConfigs = append(nodeConfigs, data_source.DataNodeConfig{
			NodeId: node.Id,
			Key:    k,
			Value:  cast.ToString(v),
		})
	}
	err = data_source.NewNodeConfigModel().Create(&nodeConfigs).Error
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	logData := logs.Create{
		IP:        params.IP,
		AwardCode: "",
		DataSyncConfig: logs.DataSyncConfig{
			RepeatType:  params.RepeatType,
			Date:        params.Date,
			StartTime:   params.StartTime,
			Version:     "",
			NetDomainId: "",
		},
	}
	// 日志-新增节点
	err = logs.LogCreate(node.Id, params.Name, logData, logs.NodeLogCreate)
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	return response.Ok(c)
}

func CheckDingTalkStatus(c *gin.Context) error {
	params, err := request.Validate(c, &struct {
		Id uint64 `json:"id" uri:"id" form:"id" validate:"required" zh:"节点ID"`
	}{})

	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}

	node, err := data_source.NewNodeModel().First(mysql.WithColumnValue("id", params.Id))
	if err != nil {
		return err
	}

	cli := dingtalk.NewDingtalk()
	if err = cli.SetNode(node.Id); err != nil {
		return err
	}

	_, err = cli.GetToken()
	if err != nil {
		node.Status = data_source.StatusNONormal
		workbench.ProbeAbnormalSend(fmt.Sprintf("钉钉 %d探针异常", params.Id))
	} else {
		node.Status = data_source.StatusNormal
	}

	err = data_source.NewNodeModel().Update(*node)
	if err != nil {
		return err
	}

	return response.Ok(c)
}

func UpdateDingTalk(ctx *gin.Context) error {
	params, err := request.Validate(ctx, &struct {
		Id         uint64 `json:"id" uri:"id" form:"id" validate:"required,number" zh:"节点ID"`
		Name       string `json:"name" uri:"name" form:"name" validate:"required" zh:"节点名称"`
		Appsecret  string `json:"appsecret" uri:"appsecret" form:"appsecret"  validate:"required" zh:"安全秘钥"`
		Appkey     string `json:"appkey" uri:"appkey" form:"appkey"  validate:"required" zh:"安全秘钥"`
		RepeatType string `json:"repeat_type" uri:"repeat_type" form:"repeat_type" validate:"required" zh:"数据同步配置"`
		Date       string `json:"date" uri:"date" form:"date" zh:"数据同步配置"`
		StartTime  string `json:"start_time" uri:"start_time" form:"start_time" validate:"required" zh:"数据同步配置"`
		AreaId     uint64 `json:"area_id" uri:"area_id" form:"area_id" zh:"区域ID"`
		IP         string `json:"ip" uri:"ip" form:"types"  ip:"required,ipv4" zh:"节点地址"`
		Protocol   string `json:"protocol" uri:"protocol" form:"protocol"  default:"https" zh:"协议"`
		DataTypes  []int  `json:"data_types" uri:"data_types" form:"data_types" zh:"节点类型"`
	}{})
	if err != nil {
		return err
	}
	//params.IP = "oapi.dingtalk.com"
	//params.Protocol = "https"
	if params.Protocol != "https" {
		return response.FailWithMessage(ctx, "protocol必须为https")
	}

	if exists, err := data_source.NewNodeConfigModel().IpExists(params.Id, 0, params.IP); exists || err != nil {
		return response.FailWithMessage(ctx, "该IP已存在,请勿重复添加")
	}

	// 鉴权
	cli := dingtalk.NewDingtalk()
	if err = cli.SetConfig(map[string]any{
		"protocol":  params.Protocol,
		"ip":        params.IP,
		"appkey":    params.Appkey,
		"appsecret": params.Appsecret,
	}); err != nil {
		return response.FailWithMessage(ctx, err.Error())
	}
	if _, err = cli.GetToken(); err != nil {
		return response.FailWithMessage(ctx, "节点校验失败，请检查节点状态")
	}

	// 将节点的任务类型保存为以 , 分隔的数字类型的字符串
	typesSlice := cast.ToStringSlice(params.DataTypes)
	types := strings.Join(typesSlice, ",")

	nodeInfo, err := data_source.NewNodeModel().First(mysql.WithColumnValue("id", params.Id))
	if err != nil {
		return err
	}
	nodeInfo.Name = params.Name
	nodeInfo.AreaId = params.AreaId
	nodeInfo.DataTypes = types
	err = data_source.NewNodeModel().Updates(*nodeInfo, mysql.WithSelect("name", "use", "data_types"))
	if err != nil {
		return err
	}
	//json转map
	list, _ := utils.StructToMap(params, "json")
	for k, v := range list {
		if k == "id" || k == "name" || k == "area_id" || v == "" {
			continue
		}
		err := data_source.NewNodeConfigModel().Update(data_source.DataNodeConfig{
			NodeId: nodeInfo.Id,
			Key:    k,
			Value:  cast.ToString(v),
		})
		if err != nil {
			return response.FailWithMessage(ctx, err.Error())
		}
	}
	return response.Ok(ctx)
}

func DingTalkTest(c *gin.Context) error {
	params, err := request.Validate(c, &struct {
		Appsecret string `json:"appsecret" uri:"appsecret" form:"appsecret"  validate:"required" zh:"安全秘钥"`
		Appkey    string `json:"appkey" uri:"appkey" form:"appkey"  validate:"required" zh:"安全秘钥"`
		IP        string `json:"ip" uri:"ip" form:"types"  ip:"required,ipv4" zh:"节点地址"`
		Protocol  string `json:"protocol" uri:"protocol" form:"protocol" zh:"协议"`
	}{})
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	cli := dingtalk.NewDingtalk()
	if err = cli.SetConfig(map[string]any{
		"protocol":  params.Protocol,
		"ip":        params.IP,
		"appkey":    params.Appkey,
		"appsecret": params.Appsecret,
	}); err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	if _, err = cli.GetToken(); err != nil {
		return response.FailWithMessage(c, "节点校验失败，请检查节点状态")
	}
	return response.Ok(c)
}

// GetDingTalkFields 获取DingTalk字段
func GetDingTalkFields(c *gin.Context) error {
	listFields := dingtalk_model.NewDingtalkTaskEmployeesModel().GetDingTalkPeopleListFields()
	type ListFields struct {
		Name string `json:"name"`
		Key  string `json:"all_key"`
	}
	result := make([]ListFields, 0)
	for _, item := range listFields {
		if _, exists := item["show_value"]; exists {
			result = append(result, ListFields{
				Name: item["name"],
				Key:  fmt.Sprintf("%s.%s", item["show_value"], item["key"]),
			})
		} else {
			result = append(result, ListFields{
				Name: item["name"],
				Key:  item["key"],
			})
		}
	}
	return response.OkWithData(c, result)
}
