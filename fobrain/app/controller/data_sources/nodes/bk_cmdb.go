package nodes

import (
	"encoding/json"
	"fmt"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"

	"fobrain/fobrain/app/services/node/bk_cmdb"
	"fobrain/fobrain/app/services/node/logs"
	"fobrain/fobrain/common/request"
	"fobrain/fobrain/common/response"
	"fobrain/initialize/mysql"
	cmdb_model "fobrain/models/elastic/source/bk_cmdb"
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/workbench"
	"fobrain/pkg/utils"
)

func BkCmdb(c *gin.Context) error {
	params, err := request.Validate(c, &struct {
		SourceId    uint64      `json:"sources_id" uri:"sources_id" form:"sources_id" validate:"required" zh:"数据源ID"`
		Name        string      `json:"name" uri:"name" form:"name" validate:"required" zh:"节点名称"`
		Ip          string      `json:"ip" uri:"ip" form:"ip"  validate:"required" zh:"节点地址"`
		AppCode     string      `json:"app_code" uri:"app_code" form:"app_code"  validate:"required" zh:"应用ID"`
		Account     string      `json:"account" uri:"account" form:"account"  validate:"required" zh:"安全秘钥"`
		AppSecret   string      `json:"app_secret" uri:"app_secret" form:"app_secret"  validate:"required" zh:"安全秘钥"`
		RepeatType  string      `json:"repeat_type" uri:"repeat_type" form:"repeat_type" validate:"required" zh:"数据同步配置"`
		Date        string      `json:"date" uri:"date" form:"date" zh:"数据同步配置"`
		StartTime   string      `json:"start_time" uri:"start_time" form:"start_time" validate:"required" zh:"数据同步配置"`
		Proxy       string      `json:"proxy" uri:"proxy" form:"proxy" zh:"代理地址"`
		AreaId      uint64      `json:"area_id" uri:"area_id" form:"area_id" zh:"区域ID"`
		AreaType    string      `json:"area_type" uri:"area_type" form:"area_type" validate:"required,oneof=指定 规则" zh:"区域类型,指定,规则"`
		AreaRules   []*AreaRule `json:"area_rules" uri:"area_rules" form:"area_rules" zh:"区域规则"`
		Protocol    string      `json:"protocol" uri:"protocol" form:"protocol"  default:"https" zh:"协议"`
		NetworkType string      `json:"network_type" uri:"network_type" form:"network_type" zh:"网络类型"`
		DataTypes   []int       `json:"data_types" uri:"data_types" form:"data_types" zh:"节点类型"`
		PersonField string      `json:"person_field" uri:"person_field" form:"person_field" zh:"人员映射字段"`
		UniqueField string      `json:"unique_field" uri:"unique_field" form:"unique_field" zh:"唯一字段"`
	}{})
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	sourceInfo, err := data_source.NewSourceModel().First(mysql.WithWhere("id", params.SourceId))
	if err != nil {
		return response.FailWithMessage(c, "数据源验证不通过")
	}

	// 验证数据源类型
	sourceType := []string{"bk_cmdb", "jw_cmdb", "bk_cmdb_vm_machine", "bk_cmdb_cloud_ecs", "bk_cmdb_domain", "bk_cmdb_business", "bk_cmdb_f5_vs", "bk_cmdb_f5_pool"}
	found := false
	for _, v := range sourceType {
		if sourceInfo.Type == v {
			found = true
			break
		}
	}
	if !found {
		return response.FailWithMessage(c, "数据源验证不通过")
	}
	if sourceInfo.Type == "bk_cmdb" && params.UniqueField == "" {
		return response.FailWithMessage(c, "唯一字段不能为空")
	}

	num, err := data_source.NewNodeModel().Total(mysql.WithWhere("source_id", params.SourceId), mysql.WithWhere("name", params.Name))
	if err != nil || num > 0 {
		return response.FailWithMessage(c, "该节点已添加,请勿重复添加")
	}
	if exists, err := data_source.NewNodeConfigModel().IpExists(0, params.SourceId, params.Ip); exists || err != nil {
		return response.FailWithMessage(c, "该IP已存在,请勿重复添加")
	}

	// 验证BkCmdb节点配置能否连接上
	cli := bk_cmdb.NewBkCmdb()
	err = cli.SetConfig(map[string]any{
		"ip": params.Ip, "app_code": params.AppCode, "app_secret": params.AppSecret, "account": params.Account, "protocol": params.Protocol,
	})
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	_, err = cli.GetAssets(1, 1)
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	// 区域为规则时
	if params.AreaType == "规则" {
		if len(params.AreaRules) == 0 {
			return response.FailWithMessage(c, "区域规则不能为空")
		}
		// 检查区域规则
		if err := CheckAreaRules(params.AreaRules); err != nil {
			return response.FailWithMessage(c, err.Error())
		}
	}
	// 将节点的任务类型保存为以 , 分隔的数字类型的字符串
	typesSlice := cast.ToStringSlice(params.DataTypes)
	types := strings.Join(typesSlice, ",")

	node := data_source.Node{
		SourceId:  params.SourceId,
		AreaId:    params.AreaId,
		Name:      params.Name,
		Source:    sourceInfo.Type,
		Types:     "",
		Status:    3,
		Use:       1,
		DataTypes: types,
	}
	node.AreaType = params.AreaType
	if params.AreaType == "规则" {
		areaRules, err := json.Marshal(params.AreaRules)
		if err != nil {
			return response.FailWithMessage(c, err.Error())
		}
		node.AreaRules = string(areaRules)
	} else {
		node.AreaRules = ""
	}
	err = data_source.NewNodeModel().Create(&node).Error
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	// 生成配置信息
	list, _ := utils.StructToMap(params, "json")
	nodeConfigs := make([]data_source.DataNodeConfig, 0, len(list))
	for k, v := range list {
		nodeConfigs = append(nodeConfigs, data_source.DataNodeConfig{
			NodeId: node.Id,
			Key:    k,
			Value:  cast.ToString(v),
		})
	}
	err = data_source.NewNodeConfigModel().Create(&nodeConfigs).Error
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	logData := logs.Create{
		IP:        params.Ip,
		AwardCode: "",
		DataSyncConfig: logs.DataSyncConfig{
			RepeatType:  params.RepeatType,
			Date:        params.Date,
			StartTime:   params.StartTime,
			Version:     "",
			NetDomainId: strconv.FormatUint(params.AreaId, 10),
		},
	}
	// 日志-新增节点
	err = logs.LogCreate(node.Id, params.Name, logData, logs.NodeLogCreate)
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	return response.Ok(c)
}

func CheckBkCmdbStatus(c *gin.Context) error {
	params, err := request.Validate(c, &struct {
		Id uint64 `json:"id" uri:"id" form:"id" validate:"required" zh:"节点ID"`
	}{})

	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}

	node, err := data_source.NewNodeModel().First(mysql.WithColumnValue("id", params.Id))
	if err != nil {
		return err
	}

	// 验证BkCmdb节点配置能否连接上
	cli := bk_cmdb.NewBkCmdb()
	//设置SyncBKCmdbClient请求的节点
	if err = cli.SetNode(node.Id); err != nil {
		return err
	}

	_, err = cli.GetAssets(1, 1)
	if err != nil {
		node.Status = data_source.StatusNONormal
		workbench.ProbeAbnormalSend(fmt.Sprintf("蓝鲸CMDB %d探针异常", params.Id))
	} else {
		node.Status = data_source.StatusNormal
	}

	err = data_source.NewNodeModel().Update(*node)
	if err != nil {
		return err
	}

	return response.Ok(c)
}

func UpdateBkCmdb(ctx *gin.Context) error {
	params, err := request.Validate(ctx, &struct {
		Id          uint64 `json:"id" uri:"id" form:"id" validate:"required,number" zh:"节点ID"`
		Name        string `json:"name" uri:"name" form:"name" validate:"required" zh:"节点名称"`
		Ip          string `json:"ip" uri:"ip" form:"ip"  validate:"required" zh:"节点地址"`
		AppCode     string `json:"app_code" uri:"app_code" form:"app_code"  validate:"required" zh:"应用ID"`
		Account     string `json:"account" uri:"account" form:"account"  validate:"required" zh:"安全秘钥"`
		AppSecret   string `json:"app_secret" uri:"app_secret" form:"app_secret"  validate:"required" zh:"安全秘钥"`
		RepeatType  string `json:"repeat_type" uri:"repeat_type" form:"repeat_type" validate:"required" zh:"数据同步配置"`
		Date        string `json:"date" uri:"date" form:"date" zh:"数据同步配置"`
		StartTime   string `json:"start_time" uri:"start_time" form:"start_time" validate:"required" zh:"数据同步配置"`
		Proxy       string `json:"proxy" uri:"proxy" form:"proxy" zh:"代理地址"`
		AreaId      uint64 `json:"area_id" uri:"area_id" form:"area_id" zh:"区域ID"`
		Protocol    string `json:"protocol" uri:"protocol" form:"protocol"  default:"https" zh:"协议"`
		NetworkType string `json:"network_type" uri:"network_type" form:"network_type" zh:"网络类型"`
		DataTypes   []int  `json:"data_types" uri:"data_types" form:"data_types" zh:"节点类型"`
		PersonField string `json:"person_field" uri:"person_field" form:"person_field" zh:"人员映射字段"`
	}{})
	if err != nil {
		return err
	}

	if exists, err := data_source.NewNodeConfigModel().IpExists(params.Id, 0, params.Ip); exists || err != nil {
		return response.FailWithMessage(ctx, "该IP已存在,请勿重复添加")
	}

	// 验证BkCmdb节点配置能否连接上
	cli := bk_cmdb.NewBkCmdb()
	err = cli.SetConfig(map[string]any{
		"ip": params.Ip, "app_code": params.AppCode, "app_secret": params.AppSecret, "account": params.Account, "protocol": params.Protocol,
	})
	if err != nil {
		return response.FailWithMessage(ctx, err.Error())
	}
	_, err = cli.GetAssets(1, 1)
	if err != nil {
		return response.FailWithMessage(ctx, err.Error())
	}

	// 将节点的任务类型保存为以 , 分隔的数字类型的字符串
	typesSlice := cast.ToStringSlice(params.DataTypes)
	types := strings.Join(typesSlice, ",")

	nodeInfo, err := data_source.NewNodeModel().First(mysql.WithColumnValue("id", params.Id))
	if err != nil {
		return err
	}
	nodeInfo.Name = params.Name
	nodeInfo.AreaId = params.AreaId
	nodeInfo.DataTypes = types
	err = data_source.NewNodeModel().Updates(*nodeInfo, mysql.WithSelect("name", "use", "data_types"))
	if err != nil {
		return err
	}
	//json转map
	list, _ := utils.StructToMap(params, "json")
	for k, v := range list {
		if k == "id" || k == "name" || k == "area_id" || v == "" {
			continue
		}
		err := data_source.NewNodeConfigModel().Update(data_source.DataNodeConfig{
			NodeId: nodeInfo.Id,
			Key:    k,
			Value:  cast.ToString(v),
		})
		if err != nil {
			return err
		}
	}
	return response.Ok(ctx)
}

func BkCmdbTest(c *gin.Context) error {
	params, err := request.Validate(c, &struct {
		Ip        string `json:"ip" uri:"ip" form:"ip"  validate:"required" zh:"节点地址"`
		AppCode   string `json:"app_code" uri:"app_code" form:"app_code"  validate:"required" zh:"应用ID"`
		Account   string `json:"account" uri:"account" form:"account"  validate:"required" zh:"安全秘钥"`
		AppSecret string `json:"app_secret" uri:"app_secret" form:"app_secret"  validate:"required" zh:"安全秘钥"`
		Protocol  string `json:"protocol" uri:"protocol" form:"protocol"  default:"https" zh:"协议"`
	}{})
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	cli := bk_cmdb.NewBkCmdb()
	err = cli.SetConfig(map[string]any{
		"ip": params.Ip, "app_code": params.AppCode, "app_secret": params.AppSecret, "account": params.Account, "protocol": params.Protocol,
	})
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	_, err = cli.GetAssets(1, 1)
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	return response.Ok(c)
}

// 获取BkCmdb人员列表字段
func GetBKCmdbPeopleListFields(c *gin.Context) error {
	listFields := cmdb_model.NewBKCmdbTaskEmployeesModel().GetPeopleListFields()
	type ListFields struct {
		Name string `json:"name"`
		Key  string `json:"all_key"`
	}
	result := make([]ListFields, 0)
	for _, item := range listFields {
		if _, exists := item["show_key"]; exists {
			result = append(result, ListFields{
				Name: item["name"],
				Key:  fmt.Sprintf("%s.%s", item["key"], item["show_key"]),
			})
		} else {
			result = append(result, ListFields{
				Name: item["name"],
				Key:  item["key"],
			})
		}
	}
	return response.OkWithData(c, result)
}
