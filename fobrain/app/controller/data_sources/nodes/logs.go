package nodes

import (
	"github.com/gin-gonic/gin"

	"fobrain/fobrain/app/services/node/logs"
	"fobrain/fobrain/common/request"
	"fobrain/fobrain/common/response"
	"fobrain/models/mysql/data_source"
)

type LogsResp struct {
	UpdatedAt string      `json:"updated_at" form:"updated_at" uri:"updated_at" zh:"更新时间"`
	NodeId    uint64      `json:"node_id" form:"node_id" uri:"node_id" zh:"节点ID"`
	NodeName  string      `json:"node_name" form:"node_name" uri:"node_name" zh:"节点名称"`
	LogType   int         `json:"log_type" form:"log_type" uri:"log_type" zh:"日志类型"` // 1:新增 2:更改 3:删除 4:升级
	Content   interface{} `json:"content" form:"content" uri:"content" zh:"内容"`
}
type LogsReq struct {
	request.PageRequest
	SourceId uint `json:"source_id" form:"source_id" uri:"source_id" zh:"源id"`
}

func GetLogs(c *gin.Context) error {
	params, err := request.Validate(c, &LogsReq{})

	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}

	Logs, total, err := data_source.NewDataNodeLogsModel().GetLogsList(params.Page, params.PerPage, params.SourceId)
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}

	LogList := make([]LogsResp, len(Logs))
	for i, r := range Logs {
		var log interface{}
		log, err = logs.FormatLog(r.Content, r.LogType)
		if err != nil {
			break
		}
		LogList[i] = LogsResp{
			UpdatedAt: r.UpdatedAt.String(),
			NodeId:    r.NodeId,
			NodeName:  r.Name,
			LogType:   r.LogType,
			Content:   log,
		}
	}
	return response.OkWithPageData(c, total, params.Page, params.PerPage, LogList)
}
