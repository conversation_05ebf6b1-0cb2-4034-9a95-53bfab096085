package nodes

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"fobrain/fobrain/app/services/node/weibu"
	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/workbench"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func TestWeiBuNode(t *testing.T) {
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()

	t.Run("param err", func(t *testing.T) {
		w := httptest.NewRecorder()
		req := httptest.NewRequest("POST", "/api/v1/data/node/weibu", nil)
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := WeiBuNode(c)
		assert.Nil(t, err)
		assert.Equal(t, `{"code":400,"message":"数据源ID为必填字段","data":{}}`, w.Body.String())
	})

	t.Run("success", func(t *testing.T) {
		mockDb.ExpectQuery("SELECT * FROM `data_sources` WHERE `id` = ? ORDER BY `data_sources`.`id` LIMIT 1").WithArgs(8).
			WillReturnRows(sqlmock.NewRows([]string{"id", "name", "type"}).AddRow(2, "weibu", "tdp"))

		mockDb.ExpectQuery("SELECT count(*) FROM `data_nodes` WHERE `source_id` = ? AND `name` = ? AND deleted_at IS NULL").
			WithArgs(8, "weibu001").
			WillReturnRows(mockDb.NewRows([]string{"count(*)"}).AddRow(0))

		mockDb.ExpectQuery("SELECT * FROM `data_node_configs` WHERE `key` =? AND value =?").
			WithArgs("ip", "************").
			WillReturnRows(mockDb.NewRows(nil))

		mockDb.Mock.ExpectBegin()
		mockDb.ExpectExec(
			"INSERT INTO `data_nodes` (`created_at`,`updated_at`,`deleted_at`,`source_id`,`area_id`,`name`,`source`,`types`,`status`,`use`,`last_task_at`,`last_task_status`,`last_sync_result`,`data_types`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?)",
		).WillReturnResult(sqlmock.NewResult(1, 1))
		mockDb.Mock.ExpectCommit()

		mockDb.Mock.ExpectBegin()
		mockDb.ExpectExec(
			"INSERT INTO `data_node_configs` (`created_at`,`updated_at`,`node_id`,`key`,`value`,`hidden`) VALUES (?,?,?,?,?,?),(?,?,?,?,?,?),(?,?,?,?,?,?),(?,?,?,?,?,?),(?,?,?,?,?,?),(?,?,?,?,?,?),(?,?,?,?,?,?),(?,?,?,?,?,?)",
		).WillReturnResult(sqlmock.NewResult(1, 1))
		mockDb.Mock.ExpectCommit()

		mockDb.Mock.ExpectBegin()
		mockDb.ExpectExec(
			"INSERT INTO `data_node_logs` (`created_at`,`updated_at`,`node_id`,`name`,`log_type`,`content`) VALUES (?,?,?,?,?,?)",
		).WillReturnResult(sqlmock.NewResult(1, 1))
		mockDb.Mock.ExpectCommit()

		patchStatus := gomonkey.ApplyMethodReturn(weibu.NewWeiBu(nil, nil), "GetStatus", nil)

		w := httptest.NewRecorder()
		val, _ := json.Marshal(WeiBuCreate{
			SourcesId: 8,
			WeiBuBase: WeiBuBase{
				Name:       "weibu001",
				AreaId:     1,
				AreaType:   "指定",
				Appsecret:  "weibu001",
				Appkey:     "weibu001",
				RepeatType: "weibu001",
				Date:       "weibu001",
				StartTime:  "weibu001",
				IP:         "************",
				Protocol:   "https",
				DataTypes:  []int{1},
			},
		})

		req := httptest.NewRequest("POST", "/api/v1/data/node/weibu", strings.NewReader(string(val)))
		req.Header.Set("Content-Type", "application/json")

		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := WeiBuNode(c)

		patchStatus.Reset()

		assert.Nil(t, err)
		assert.NotEmpty(t, w.Body.String())
	})
}

func TestCheckWeiBuStatus(t *testing.T) {
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()

	t.Run("success", func(t *testing.T) {
		mockDb.ExpectQuery("SELECT * FROM `data_nodes` WHERE `id` = ? AND `data_nodes`.`deleted_at` IS NULL ORDER BY `data_nodes`.`id` LIMIT 1").WithArgs(8).
			WillReturnRows(sqlmock.NewRows([]string{"id", "name", "type"}).AddRow(8, "weibu", "tdp"))
		mockDb.ExpectQuery("SELECT * FROM `data_node_configs` WHERE `node_id` = ?").WithArgs(8).
			WillReturnRows(sqlmock.NewRows([]string{"id", "key", "value"}).AddRow(int64(8), "key", "value"))

		mockDb.Mock.ExpectBegin()
		mockDb.ExpectExec(
			"UPDATE `data_nodes` SET `id`=?,`updated_at`=?,`name`=?,`status`=? WHERE `id` = ? AND `data_nodes`.`deleted_at` IS NULL",
		).WillReturnResult(sqlmock.NewResult(1, 1))
		mockDb.Mock.ExpectCommit()

		gomonkey.ApplyFuncReturn(workbench.ProbeAbnormalSend)

		w := httptest.NewRecorder()
		val, _ := json.Marshal(WeiBuCreate{
			SourcesId: 8,
			WeiBuBase: WeiBuBase{
				Name:       "weibu001",
				AreaId:     1,
				AreaType:   "指定",
				Appsecret:  "weibu001",
				Appkey:     "weibu001",
				RepeatType: "weibu001",
				Date:       "weibu001",
				StartTime:  "weibu001",
				IP:         "weibu001",
				Protocol:   "https",
			},
		})

		req := httptest.NewRequest("GET", "/api/v1/data/node/weibu/8/check_status?id=8", strings.NewReader(string(val)))
		req.Header.Set("Content-Type", "application/json")

		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := CheckWeiBuStatus(c)
		assert.Nil(t, err)
		assert.Equal(t, "{\"code\":0,\"message\":\"Success\",\"data\":{}}", w.Body.String())
	})
}

func TestUpdateWeiBu(t *testing.T) {
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()

	t.Run("success", func(t *testing.T) {

		mockDb.ExpectQuery("SELECT * FROM `data_node_configs` WHERE `key` =? AND value =?").
			WithArgs("ip", "************").
			WillReturnRows(mockDb.NewRows(nil))

		mockDb.ExpectQuery("SELECT * FROM `data_nodes` WHERE `id` = ?").WithArgs(1).
			WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))

		mockDb.Mock.ExpectBegin()
		mockDb.ExpectExec(
			"UPDATE `data_nodes` SET `updated_at`=?,`name`=?,`use`=?,`data_types`=? WHERE `id` = ? AND `data_nodes`.`deleted_at` IS NULL",
		).WillReturnResult(sqlmock.NewResult(1, 1))
		mockDb.Mock.ExpectCommit()

		patchNodeConfig := gomonkey.ApplyMethodReturn(data_source.NewNodeConfigModel(), "Update", nil)

		w := httptest.NewRecorder()
		val, _ := json.Marshal(WeiBuUpdate{
			Id: 1,
			WeiBuBase: WeiBuBase{
				Name:       "weibu001",
				AreaId:     1,
				AreaType:   "指定",
				Appsecret:  "weibu001",
				Appkey:     "weibu001",
				RepeatType: "weibu001",
				Date:       "weibu001",
				StartTime:  "weibu001",
				IP:         "************",
				Protocol:   "https",
				DataTypes:  []int{1},
			},
		})

		req := httptest.NewRequest("POST", "/api/v1/data/node/weibu", strings.NewReader(string(val)))
		req.Header.Set("Content-Type", "application/json")

		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := UpdateWeiBu(c)

		patchNodeConfig.Reset()

		assert.Nil(t, err)
		assert.NotEmpty(t, w.Body.String())
	})
}

func TestWeiBuNodeTest(t *testing.T) {
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		res := map[string]any{
			"response_code": 0,
		}
		w.WriteHeader(http.StatusOK)
		_ = json.NewEncoder(w).Encode(res)
	}))
	defer server.Close()

	w := httptest.NewRecorder()
	val, _ := json.Marshal(WeiBuCreate{
		SourcesId: 8,
		WeiBuBase: WeiBuBase{
			Name:       "weibu001",
			AreaId:     1,
			AreaType:   "指定",
			Appsecret:  "weibu001",
			Appkey:     "weibu001",
			RepeatType: "weibu001",
			Date:       "weibu001",
			StartTime:  "weibu001",
			IP:         server.Listener.Addr().String(),
			Protocol:   "http",
			DataTypes:  []int{1},
		},
	})

	req := httptest.NewRequest("POST", "/api/v1/data/node/weibu/test", strings.NewReader(string(val)))
	req.Header.Set("Content-Type", "application/json")

	c, _ := gin.CreateTestContext(w)
	c.Request = req

	err := WeiBuNodeTest(c)
	assert.Nil(t, err)
	assert.Equal(t, w.Body.String(), `{"code":0,"message":"Success","data":{}}`)
}
