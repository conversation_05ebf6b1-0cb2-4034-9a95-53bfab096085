package nodes

import (
	"errors"
	"github.com/DATA-DOG/go-sqlmock"
	"github.com/gin-gonic/gin"
	"github.com/olivere/elastic/v7"
	"net/http/httptest"
	"testing"
	"time"

	testcommon "fobrain/fobrain/tests/common_test"

	"github.com/stretchr/testify/assert"
)

func TestData(t *testing.T) {
	t.Run("param err", func(t *testing.T) {
		w := httptest.NewRecorder()
		req := httptest.NewRequest("PUT", "/api/v1/data/node/bk_cmdb?page=1&per_page=10", nil)
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := Data(c)
		assert.Nil(t, err)
	})

	t.Run("success", func(t *testing.T) {
		mockDb := testcommon.GetMysqlMock()
		defer mockDb.Close()

		mockDb.ExpectQuery("SELECT * FROM `data_nodes` WHERE `id` = ? AND `data_nodes`.`deleted_at` IS NULL ORDER BY `data_nodes`.`id` LIMIT 1").
			WillReturnRows(mockDb.NewRows([]string{"source_id", "area_id"}).AddRow(1, 1))

		mockServer := testcommon.NewMockServer()
		defer mockServer.Close()

		mockServer.Register("foeye_assets/_search", []*elastic.SearchHit{
			{
				Id:     "1",
				Source: []byte(`{"name":"example"}`),
			},
		})

		mockServer.Register("foeye_assets/_count", &elastic.CountResponse{
			Count: 1,
		})

		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/v1/node/data?page=1&per_page=10&node_id=1&sync_type=1", nil)
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := Data(c)
		assert.NotNil(t, err)
	})
}
func TestFailDetail1(t *testing.T) {
	t.Run("param err", func(t *testing.T) {
		w := httptest.NewRecorder()
		req := httptest.NewRequest("PUT", "/api/v1/fail_detail?page=1&per_page=10", nil)
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req
		err := FailDetail(c)
		assert.Nil(t, err)
	})

	t.Run("success", func(t *testing.T) {
		mockDb := testcommon.GetMysqlMock()
		defer mockDb.Close()

		// 模拟计数查询
		mockDb.ExpectQuery("").WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(1))

		// 模拟数据查询
		mockDb.ExpectQuery("").WillReturnRows(sqlmock.NewRows([]string{
			"id", "child_task_id", "data_type", "data_content", "failed_reason", "created_at", "updated_at"}).
			AddRow(1, 1, "asset", "{\"ip\":\"***********\"}", "数据格式错误", time.Now(), time.Now()))

		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/v1/node/fail_detail?page=1&per_page=10&child_task_id=1", nil)
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := FailDetail(c)
		assert.Nil(t, err)
	})

	t.Run("database error", func(t *testing.T) {
		mockDb := testcommon.GetMysqlMock()
		defer mockDb.Close()

		// 模拟数据库错误
		mockDb.ExpectQuery("").WillReturnError(errors.New("DatabaseQueryError"))

		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/v1/node/fail_detail?page=1&per_page=10&child_task_id=1", nil)
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := FailDetail(c)
		assert.NotNil(t, err)
	})
}
