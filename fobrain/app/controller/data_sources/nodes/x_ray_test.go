package nodes

import (
	"encoding/json"
	"errors"
	"fmt"
	"fobrain/fobrain/app/services/node/x_ray"
	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/models/mysql/data_source"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func TestXray(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()

		mockDb.ExpectQuery("SELECT * FROM `data_sources` WHERE `id` = ? ORDER BY `data_sources`.`id` LIMIT 1").
			WillReturnRows(mockDb.NewRows([]string{"id", "name", "type"}).AddRow(1, "name", "x_ray"))

		mockDb.ExpectQuery("SELECT count(*) FROM `data_nodes` WHERE `source_id` = ? AND `name` = ? AND deleted_at IS NULL").
			WithArgs(int64(13), "X-ray").
			WillReturnRows(mockDb.NewRows([]string{"num"}).AddRow(0))

		mockDb.ExpectQuery("SELECT * FROM `data_node_configs` WHERE `key` =? AND value =?").
			WithArgs("ip", "************").
			WillReturnRows(mockDb.NewRows([]string{"id"}).AddRow(0))

		mockDb.ExpectQuery("SELECT `id` FROM `data_nodes` WHERE source_id = ?").
			WithArgs(int64(13)).
			WillReturnRows(mockDb.NewRows([]string{"source_id"}).AddRow(1))

		mockDb.ExpectQuery("SELECT count(*) FROM `data_node_configs` WHERE (`key` = ? AND value = ?) AND `node_id` = ?").
			WithArgs("ip", "************", int64(1)).
			WillReturnRows(mockDb.NewRows([]string{"num"}).AddRow(0))

		mockDb.Mock.ExpectBegin()
		mockDb.ExpectExec(
			"INSERT INTO `data_nodes` (`created_at`,`updated_at`,`deleted_at`,`source_id`,`area_id`,`name`,`source`,`types`,`status`,`use`,`last_task_at`,`last_task_status`,`last_sync_result`,`data_types`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?)",
		).WillReturnResult(sqlmock.NewResult(1, 1))
		mockDb.Mock.ExpectCommit()

		mockDb.Mock.ExpectBegin()
		mockDb.ExpectExec(
			"INSERT INTO `data_node_configs` (`created_at`,`updated_at`,`node_id`,`key`,`value`,`hidden`) VALUES (?,?,?,?,?,?),(?,?,?,?,?,?),(?,?,?,?,?,?),(?,?,?,?,?,?),(?,?,?,?,?,?),(?,?,?,?,?,?),(?,?,?,?,?,?),(?,?,?,?,?,?)",
		).WillReturnResult(sqlmock.NewResult(1, 1))
		mockDb.Mock.ExpectCommit()

		mockDb.Mock.ExpectBegin()
		mockDb.ExpectExec(
			"INSERT INTO `data_node_logs` (`created_at`,`updated_at`,`node_id`,`name`,`log_type`,`content`) VALUES (?,?,?,?,?,?)",
		).WillReturnResult(sqlmock.NewResult(1, 1))
		mockDb.Mock.ExpectCommit()

		patchTemplate := gomonkey.ApplyMethodReturn(x_ray.New(), "TemplateAll", nil, nil)

		w := httptest.NewRecorder()
		req := httptest.NewRequest("POST", "/api/v1/data/node/x_ray", strings.NewReader(`{
    		"sources_id": 13,
            "name": "X-ray",
		    "area_id": 1,
			"area_type": "指定",
			"protocol": "https",
			"ip": "************",
   			"appKey": "******",
   			"repeat_type": "day",
            "date": "",
            "start_time": "00:00",
			"data_types": [1]
		}`))

		req.Header.Set("Content-Type", "application/json")

		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := Xray(c)

		patchTemplate.Reset()

		assert.Nil(t, err)
	})

	t.Run("param err", func(t *testing.T) {
		w := httptest.NewRecorder()
		req := httptest.NewRequest("POST", "/api/v1/data/node/x_ray", nil)
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := Xray(c)
		assert.Nil(t, err)
	})

	t.Run("param count > 0", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()
		mockDb.ExpectQuery("SELECT * FROM `data_sources` WHERE `id` = ? ORDER BY `data_sources`.`id` LIMIT 1").
			WillReturnRows(mockDb.NewRows([]string{"id", "name", "type"}).AddRow(1, "name", "x_ray"))

		mockDb.ExpectQuery("SELECT count(*) FROM `data_nodes` WHERE `source_id` = ? AND `name` = ? AND deleted_at IS NULL").
			WithArgs(int64(13), "X-ray").
			WillReturnRows(mockDb.NewRows([]string{"num"}).AddRow(1))

		w := httptest.NewRecorder()
		req := httptest.NewRequest("POST", "/api/v1/data/node/x_ray", strings.NewReader(`{
    		"sources_id": 13,
            "name": "X-ray",
		    "area_id": 1,
			"area_type": "指定",
			"protocol": "https",
			"ip": "************",
   			"appKey": "******",
   			"repeat_type": "day",
            "date": "",
            "start_time": "00:00"
		}`))

		req.Header.Set("Content-Type", "application/json")

		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := Xray(c)
		assert.Nil(t, err)
	})

	t.Run("param type != x_ray", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()
		mockDb.ExpectQuery("SELECT * FROM `data_sources` WHERE `id` = ? ORDER BY `data_sources`.`id` LIMIT 1").
			WillReturnRows(mockDb.NewRows([]string{"id", "name", "type"}).AddRow(1, "name", "foeye"))

		w := httptest.NewRecorder()
		req := httptest.NewRequest("POST", "/api/v1/data/node/x_ray", strings.NewReader(`{
    		"sources_id": 13,
            "name": "X-ray",
		    "area_id": 1,
			"area_type": "指定",
			"protocol": "https",
			"ip": "************",
   			"appKey": "******",
   			"repeat_type": "day",
            "date": "",
            "start_time": "00:00"
		}`))

		req.Header.Set("Content-Type", "application/json")

		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := Xray(c)
		assert.Nil(t, err)
	})

	t.Run("param type err", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()
		mockDb.ExpectQuery("SELECT * FROM `data_sources` WHERE `id` = ? ORDER BY `data_sources`.`id` LIMIT 1").
			WillReturnError(errors.New("err"))

		w := httptest.NewRecorder()
		req := httptest.NewRequest("POST", "/api/v1/data/node/x_ray", strings.NewReader(`{
    		"sources_id": 13,
            "name": "X-ray",
		    "area_id": 1,
			"area_type": "指定",
			"protocol": "https",
			"ip": "************",
   			"appKey": "******",
   			"repeat_type": "day",
            "date": "",
            "start_time": "00:00"
		}`))

		req.Header.Set("Content-Type", "application/json")

		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := Xray(c)
		assert.Nil(t, err)
	})

	t.Run("param count err", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()
		mockDb.ExpectQuery("SELECT * FROM `data_sources` WHERE `id` = ? ORDER BY `data_sources`.`id` LIMIT 1").
			WillReturnRows(mockDb.NewRows([]string{"id", "name", "type"}).AddRow(1, "name", "x_ray"))

		mockDb.ExpectQuery("SELECT count(*) FROM `data_nodes` WHERE `source_id` = ? AND `name` = ? AND deleted_at IS NULL").
			WillReturnError(errors.New("err"))
		w := httptest.NewRecorder()
		req := httptest.NewRequest("POST", "/api/v1/data/node/x_ray", strings.NewReader(`{
    		"sources_id": 13,
            "name": "X-ray",
		    "area_id": 1,
			"area_type": "指定",
			"protocol": "https",
			"ip": "************",
   			"appKey": "******",
   			"repeat_type": "day",
            "date": "",
            "start_time": "00:00"
		}`))

		req.Header.Set("Content-Type", "application/json")

		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := Xray(c)
		assert.Nil(t, err)
	})
}

func TestUpdateXray(t *testing.T) {
	t.Run("param err", func(t *testing.T) {
		w := httptest.NewRecorder()
		req := httptest.NewRequest("PUT", "/api/v1/data/node/x_ray", nil)
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := UpdateXray(c)
		assert.Equal(t, err.Error(), "节点ID为必填字段")
	})

	t.Run("missing protocol scheme", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()
		mockDb.ExpectQuery("SELECT * FROM `data_nodes` WHERE `id` = ? AND `data_nodes`.`deleted_at` IS NULL ORDER BY `data_nodes`.`id` LIMIT 1").
			WillReturnRows(mockDb.NewRows([]string{"id", "name", "type"}).AddRow(1, "name", "d01"))

		w := httptest.NewRecorder()
		req := httptest.NewRequest("PUT", "/api/v1/data/node/x_ray", strings.NewReader(`{
   			 "id": 43,
             "appKey": "-----",
    		 "date": "",
    		 "ip": "************",
    		 "name": "X-ray",
    		 "protocol": "https",
    		 "repeat_type": "month",
    		 "start_time": "16:12"
		}`))
		req.Header.Set("Content-Type", "application/json")
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := UpdateXray(c)
		assert.Nil(t, err)
		assert.Equal(t, w.Body.String(), `{"code":400,"message":"该IP已存在,请勿重复添加","data":{}}`)
	})

	t.Run("success", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()

		mockDb.ExpectQuery("SELECT * FROM `data_nodes` WHERE `id` = ? AND `data_nodes`.`deleted_at` IS NULL ORDER BY `data_nodes`.`id` LIMIT 1").WithArgs(1).
			WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))

		mockDb.ExpectQuery("SELECT * FROM `data_node_configs` WHERE `key` =? AND value =?").
			WithArgs("ip", "***********").
			WillReturnRows(mockDb.NewRows(nil))

		mockDb.Mock.ExpectBegin()
		mockDb.ExpectExec(
			"UPDATE `data_nodes` SET `updated_at`=?,`name`=?,`use`=?,`data_types`=? WHERE `id` = ? AND `data_nodes`.`deleted_at` IS NULL",
		).WillReturnResult(sqlmock.NewResult(1, 1))
		mockDb.Mock.ExpectCommit()

		patchTemplate := gomonkey.ApplyMethodReturn(x_ray.New(), "TemplateAll", nil, nil)

		patchNodeConfig := gomonkey.ApplyMethodReturn(data_source.NewNodeConfigModel(), "Update", nil)

		w := httptest.NewRecorder()
		req := httptest.NewRequest("PUT", "/api/v1/data/node/x_ray", strings.NewReader(`{
			"id": 1,
			"name": "qty",
			"ip": "***********",
			"app_code": "1",
			"account": "abel",
			"app_secret": "1",
			"repeat_type": "1",
			"start_time": "2024-1-21",
			"sources_id": 1,
			"appsecret": "1",
			"appkey": "1",
			"password": "sdfasdf",
			"api_key": "1",
			"protocol": "https",
			"data_types": [1]
		}`))
		req.Header.Set("Content-Type", "application/json")
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := UpdateXray(c)

		patchTemplate.Reset()

		patchNodeConfig.Reset()

		assert.Nil(t, err)
	})

}

func TestXrayTest(t *testing.T) {
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		res := map[string]any{}
		w.WriteHeader(http.StatusOK)
		_ = json.NewEncoder(w).Encode(res)
	}))
	defer server.Close()

	w := httptest.NewRecorder()
	req := httptest.NewRequest("POST", "/api/v1/data/node/x_ray/test", strings.NewReader(fmt.Sprintf(`{
    		"sources_id": 13,
            "name": "X-ray",
		    "area_id": 1,
			"area_type": "指定",
			"protocol": "http",
			"ip": "%s",
   			"appKey": "******",
   			"repeat_type": "day",
            "date": "",
            "start_time": "00:00",
			"data_types": [1]
		}`, server.Listener.Addr().String())))

	req.Header.Set("Content-Type", "application/json")

	c, _ := gin.CreateTestContext(w)
	c.Request = req

	err := XrayTest(c)
	assert.Nil(t, err)
	assert.Equal(t, w.Body.String(), `{"code":0,"message":"Success","data":{}}`)
}
