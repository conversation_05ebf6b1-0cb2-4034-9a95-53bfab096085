package nodes

import (
	"crypto/md5"
	"encoding/hex"
	"fmt"
	"fobrain/fobrain/app/services/node/logs"
	"fobrain/fobrain/app/services/node/x_ray"
	"fobrain/fobrain/common/request"
	"fobrain/fobrain/common/response"
	"fobrain/initialize/mysql"
	"fobrain/models/mysql/data_source"
	"fobrain/pkg/utils"
	"math/rand"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

// Xray 添加节点
// @Route /POST /api/v1/data/node/x_ray
func Xray(c *gin.Context) error {
	params, err := request.Validate(c, &struct {
		SourceId uint64 `json:"sources_id" uri:"sources_id" form:"sources_id" validate:"required" zh:"数据源ID"`
		Name     string `json:"name" uri:"name" form:"name" validate:"required" zh:"节点名称"`
		IP       string `json:"ip" uri:"ip" form:"types"  ip:"required,ipv4" zh:"节点地址"`
		AppKey   string `json:"appkey" uri:"appkey" form:"appkey" validate:"required"  zh:"appkey"`
		Protocol string `json:"protocol" uri:"protocol" form:"protocol"  default:"https" zh:"协议"`
		AreaId   uint64 `json:"area_id" uri:"area_id" form:"area_id" zh:"区域ID"`
		//AreaType    string      `json:"area_type" uri:"area_type" form:"area_type" validate:"required,oneof=指定 规则" zh:"区域,指定,规则"`
		//AreaRules   []*AreaRule `json:"area_rules" uri:"area_rules" form:"area_rules" zh:"区域规则"`
		ProjectId    string `json:"project_id" uri:"project_id" form:"project_id" zh:"项目ID"`
		Types        string `json:"types" uri:"types" form:"types" zh:"节点类型, "`
		NetworkType  string `json:"network_type" uri:"network_type" form:"network_type" zh:"网络类型"`
		RepeatType   string `json:"repeat_type" uri:"repeat_type" form:"repeat_type" validate:"required" zh:"数据同步配置"`
		Date         string `json:"date" uri:"date" form:"date" zh:"数据同步配置"`
		StartTime    string `json:"start_time" uri:"start_time" form:"start_time" validate:"required" zh:"数据同步配置"`
		DataTypes    []int  `json:"data_types" uri:"data_types" form:"data_types" zh:"节点类型"`
		PersonField  string `json:"person_field" uri:"person_field" form:"person_field" zh:"人员映射字段"`
		EngineChoice string `json:"engine_choice" uri:"engine_choice" form:"engine_choice" zh:"引擎选择"`
	}{})
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	// 区域为规则时
	// if params.AreaType == "规则" {
	// 	if len(params.AreaRules) == 0 {
	// 		return response.FailWithMessage(c, "区域规则不能为空")
	// 	}
	// 	// 检查区域规则
	// 	if err := CheckAreaRules(params.AreaRules); err != nil {
	// 		return response.FailWithMessage(c, err.Error())
	// 	}
	// }
	sourceInfo, err := data_source.NewSourceModel().First(mysql.WithWhere("id", params.SourceId))
	if err != nil {
		return response.FailWithMessage(c, "数据源验证不通过")
	}
	if sourceInfo.Type != "x_ray" {
		return response.FailWithMessage(c, "数据源验证不通过")
	}
	num, err := data_source.NewNodeModel().Total(mysql.WithWhere("source_id", params.SourceId), mysql.WithWhere("name", params.Name))
	if err != nil {
		return response.FailWithMessage(c, "该节点已添加,请勿重复添加")
	}
	if num > 0 {
		return response.FailWithMessage(c, "该节点已添加,请勿重复添加")
	}
	if exists, err := data_source.NewNodeConfigModel().IpExists(0, sourceInfo.Id, params.IP); exists || err != nil {
		return response.FailWithMessage(c, "该IP已存在,请勿重复添加")
	}

	nodeIds, err := data_source.NewNodeModel().GetIds(params.SourceId)
	if err != nil {
		return response.FailWithMessage(c, "寻找节点错误")
	}
	_, err = data_source.NewNodeConfigModel().GetIpTotal(nodeIds, params.IP)
	if err != nil {
		return response.FailWithMessage(c, "寻找Ip错误")
	}

	if params.Protocol != "https" {
		return response.FailWithMessage(c, "协议必须为https")
	}

	cli := x_ray.New()
	if err = cli.SetConfig(map[string]any{
		"protocol": params.Protocol,
		"ip":       params.IP,
		"appkey":   params.AppKey,
	}); err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	if _, err = cli.TemplateAll(); err != nil {
		return response.FailWithMessage(c, "节点校验失败，请检查节点状态")
	}
	// 将节点的任务类型保存为以 , 分隔的数字类型的字符串
	typesSlice := cast.ToStringSlice(params.DataTypes)
	types := strings.Join(typesSlice, ",")

	node := data_source.Node{
		SourceId: params.SourceId,
		Name:     params.Name,
		Source:   sourceInfo.Type,
		Types:    params.Types,
		Status:   3,
		Use:      1,
		AreaId:   params.AreaId,
		//AreaType:  params.AreaType,
		DataTypes: types,
	}
	// if params.AreaType == "规则" {
	// 	areaRules, err := json.Marshal(params.AreaRules)
	// 	if err != nil {
	// 		return response.FailWithMessage(c, err.Error())
	// 	}
	// 	node.AreaRules = string(areaRules)
	// } else {
	// 	node.AreaRules = ""
	// }

	url := fmt.Sprintf("https://%s", params.IP)
	rand.Seed(time.Now().UnixNano())
	randomNum := rand.Intn(1000)
	// 获取当前时间戳
	timestamp := strconv.FormatInt(time.Now().Unix(), 10)

	// 将字符串、随机数和时间戳拼接起来
	data := fmt.Sprintf("%s%d%s", url, randomNum, timestamp)

	// 计算MD5值
	md5Sum := md5.Sum([]byte(data))
	key := hex.EncodeToString(md5Sum[:])

	err = data_source.NewNodeModel().Create(&node).Error
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}

	nodeConfigs := []data_source.DataNodeConfig{
		{
			NodeId: node.Id,
			Key:    "key",
			Value:  key,
		},

		{
			NodeId: node.Id,
			Key:    "ip",
			Value:  params.IP,
		},
		{
			NodeId: node.Id,
			Key:    "appkey",
			Value:  params.AppKey,
		},
		{
			NodeId: node.Id,
			Key:    "protocol",
			Value:  params.Protocol,
		},
		{
			NodeId: node.Id,
			Key:    "area_id",
			Value:  strconv.FormatUint(params.AreaId, 10),
		},
		{
			NodeId: node.Id,
			Key:    "network_type",
			Value:  params.NetworkType,
		},
		{
			NodeId: node.Id,
			Key:    "repeat_type",
			Value:  params.RepeatType,
		},
		{
			NodeId: node.Id,
			Key:    "start_time",
			Value:  params.StartTime,
		},
		{
			NodeId: node.Id,
			Key:    "date",
			Value:  params.Date,
		},
		{
			NodeId: node.Id,
			Key:    "project_id",
			Value:  params.ProjectId,
		},
		{
			NodeId: node.Id,
			Key:    "engine_choice",
			Value:  params.EngineChoice,
		},
	}
	err = data_source.NewNodeConfigModel().Create(&nodeConfigs).Error
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	logData := logs.Create{
		IP:        params.IP,
		AwardCode: "",
		DataSyncConfig: logs.DataSyncConfig{
			RepeatType:  "",
			NetDomainId: "",
		},
	}
	// 日志-新增节点
	err = logs.LogCreate(node.Id, params.Name, logData, logs.NodeLogCreate)
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	return response.OkWithData(c, map[string]any{
		"node_id":   node.Id,
		"source_id": node.SourceId,
	})
}

// UpdateXray 更新节点数据
func UpdateXray(c *gin.Context) error {
	params, err := request.Validate(c, &struct {
		Id           uint64 `json:"id" uri:"id" form:"id" validate:"required,number" zh:"节点ID"`
		Name         string `json:"name" uri:"name" form:"name" validate:"required,omitempty" zh:"节点名称"`
		IP           string `json:"ip" uri:"ip" form:"types"  ip:"required,ipv4" zh:"节点地址"`
		AppKey       string `json:"appKey" uri:"appKey" form:"appKey" validate:"required"  zh:"appKey"`
		Protocol     string `json:"protocol" uri:"protocol" form:"protocol"  default:"https" zh:"协议"`
		ProjectId    string `json:"project_id" uri:"project_id" form:"project_id" zh:"项目ID"`
		NetworkType  string `json:"network_type" uri:"network_type" form:"network_type" zh:"网络类型"`
		RepeatType   string `json:"repeat_type" uri:"repeat_type" form:"repeat_type" validate:"required" zh:"数据同步配置"`
		Date         string `json:"date" uri:"date" form:"date" zh:"数据同步配置"`
		StartTime    string `json:"start_time" uri:"start_time" form:"start_time" validate:"required" zh:"数据同步配置"`
		DataTypes    []int  `json:"data_types" uri:"data_types" form:"data_types" zh:"节点类型"`
		PersonField  string `json:"person_field" uri:"person_field" form:"person_field" zh:"人员映射字段"`
		EngineChoice string `json:"engine_choice" uri:"engine_choice" form:"engine_choice" zh:"引擎选择"`
	}{})
	if err != nil {
		return err
	}

	nodeInfo, err := data_source.NewNodeModel().First(mysql.WithColumnValue("id", params.Id))
	if err != nil {
		return err
	}

	if exists, err := data_source.NewNodeConfigModel().IpExists(params.Id, nodeInfo.SourceId, params.IP); exists || err != nil {
		return response.FailWithMessage(c, "该IP已存在,请勿重复添加")
	}

	if params.Protocol != "https" {
		return response.FailWithMessage(c, "协议必须为https")
	}

	cli := x_ray.New()
	if err = cli.SetConfig(map[string]any{
		"protocol": params.Protocol,
		"ip":       params.IP,
		"appkey":   params.AppKey,
	}); err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	if _, err = cli.TemplateAll(); err != nil {
		return response.FailWithMessage(c, "节点校验失败，请检查节点状态")
	}
	// 将节点的任务类型保存为以 , 分隔的数字类型的字符串
	typesSlice := cast.ToStringSlice(params.DataTypes)
	types := strings.Join(typesSlice, ",")

	nodeInfo.Name = params.Name
	nodeInfo.DataTypes = types
	err = data_source.NewNodeModel().Updates(*nodeInfo, mysql.WithSelect("name", "use", "data_types"))
	if err != nil {
		return err
	}
	//json转map
	list, _ := utils.StructToMap(params, "json")
	for k, v := range list {
		if k == "id" || k == "name" || v == "" {
			continue
		}
		err := data_source.NewNodeConfigModel().Update(data_source.DataNodeConfig{
			NodeId: nodeInfo.Id,
			Key:    k,
			Value:  cast.ToString(v),
		})
		if err != nil {
			return response.FailWithMessage(c, err.Error())
		}
	}
	return response.Ok(c)
}

func XrayTest(c *gin.Context) error {
	params, err := request.Validate(c, &struct {
		IP       string `json:"ip" uri:"ip" form:"types"  ip:"required,ipv4" zh:"节点地址"`
		AppKey   string `json:"appkey" uri:"appkey" form:"appkey" validate:"required"  zh:"appkey"`
		Protocol string `json:"protocol" uri:"protocol" form:"protocol"  default:"https" zh:"协议"`
	}{})
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	cli := x_ray.New()
	if err = cli.SetConfig(map[string]any{
		"protocol": params.Protocol,
		"ip":       params.IP,
		"appkey":   params.AppKey,
	}); err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	if _, err = cli.TemplateAll(); err != nil {
		return response.FailWithMessage(c, "节点校验失败，请检查节点状态")
	}
	return response.Ok(c)
}
