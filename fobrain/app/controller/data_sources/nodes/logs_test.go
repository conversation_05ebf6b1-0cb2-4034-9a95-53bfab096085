package nodes

import (
	testcommon "fobrain/fobrain/tests/common_test"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"net/http/httptest"
	"testing"
)

func TestGetLogs(t *testing.T) {
	t.Run("param err", func(t *testing.T) {
		w := httptest.NewRecorder()
		req := httptest.NewRequest("PUT", "/api/v1/data/node/bk_cmdb?page=1&per_page=10", nil)
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := GetLogs(c)
		assert.Nil(t, err)
	})

	t.Run("success", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()
		mockDb.ExpectQuery("SELECT count(*) FROM `data_node_logs` JOIN data_nodes on data_nodes.id = data_node_logs.node_id AND data_nodes.source_id = ?").
			WillReturnRows(mockDb.NewRows([]string{"count"}).AddRow(1))

		mockDb.ExpectQuery("SELECT * FROM `data_node_logs` JOIN data_nodes on data_nodes.id = data_node_logs.node_id AND data_nodes.source_id = ? LIMIT 10").
			WillReturnRows(mockDb.NewRows([]string{"id"}).AddRow(1))

		w := httptest.NewRecorder()
		req := httptest.NewRequest("PUT", "/api/v1/data/node/bk_cmdb?page=1&per_page=10", nil)
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := GetLogs(c)
		assert.Nil(t, err)
	})

	t.Run("success1", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()
		mockDb.ExpectQuery("SELECT count(*) FROM `data_node_logs` JOIN data_nodes on data_nodes.id = data_node_logs.node_id AND data_nodes.source_id = ?").
			WillReturnRows(mockDb.NewRows([]string{"count"}).AddRow(1))

		mockDb.ExpectQuery("SELECT * FROM `data_node_logs` JOIN data_nodes on data_nodes.id = data_node_logs.node_id AND data_nodes.source_id = ? LIMIT 10").
			WillReturnRows(mockDb.NewRows([]string{"id", "content", "log_type", "created_at", "updated_at"}).AddRow(1, 1, 1, "2024-08-01 00:07:59", "2024-08-01 00:07:59"))

		w := httptest.NewRecorder()
		req := httptest.NewRequest("PUT", "/api/v1/data/node/bk_cmdb?page=1&per_page=10", nil)
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := GetLogs(c)
		assert.Nil(t, err)
	})
}
