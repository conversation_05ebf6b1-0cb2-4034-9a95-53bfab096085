package nodes

import (
	"fmt"
	"fobrain/fobrain/common/response"
	qianxin_pam "fobrain/models/elastic/source/qianxin_pam"
	"github.com/gin-gonic/gin"
)

// GetPamFields 获取Pam字段
func GetPamFields(c *gin.Context) error {
	listFields := qianxin_pam.NewPamStaffsTaskModel().GetEmployeeListFields()
	type ListFields struct {
		Name string `json:"name"`
		Key  string `json:"all_key"`
	}
	result := make([]ListFields, 0)
	for _, item := range listFields {
		if _, exists := item["show_value"]; exists {
			result = append(result, ListFields{
				Name: item["name"],
				Key:  fmt.Sprintf("%s.%s", item["show_value"], item["key"]),
			})
		} else {
			result = append(result, ListFields{
				Name: item["name"],
				Key:  item["key"],
			})
		}
	}
	return response.OkWithData(c, result)
}
