package nodes

import (
	"fobrain/fobrain/app/services/network_area"
	"fobrain/initialize/mysql"
	"fobrain/models/mysql/network_areas"
	"fobrain/pkg/utils"
	"testing"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/stretchr/testify/assert"
)

// TestCheckAreaRules 测试区域规则检查函数
func TestCheckAreaRules(t *testing.T) {
	// 模拟网络区域数据
	mockNetworkAreas := []*network_areas.NetworkArea{
		{
			BaseModel: mysql.BaseModel{
				Id: 1,
			},
			Name: "区域1",
		},
		{
			BaseModel: mysql.BaseModel{
				Id: 2,
			},
			Name: "区域2",
		},
		{
			BaseModel: mysql.BaseModel{
				Id: 3,
			},
			Name: "区域3",
		},
	}

	// 使用gomonkey模拟GetNetWorkAreaList函数
	patches := gomonkey.ApplyFunc(network_area.GetNetWorkAreaList, func() ([]*network_areas.NetworkArea, error) {
		return mockNetworkAreas, nil
	})
	defer patches.Reset() // 确保测试结束后恢复原函数

	// 模拟IsIpSegmentValid函数
	patches.ApplyFunc(utils.IsIpSegmentValid, func(ipSegment string) bool {
		// 简单判断，只有包含"/"的才是有效CIDR
		return ipSegment != "" && ipSegment != "invalid-cidr"
	})

	// 模拟IsIpSegmentOverlap函数
	patches.ApplyFunc(utils.IsIpSegmentOverlap, func(areaRuleMap map[uint64][]string) bool {
		// 简单模拟，只有当包含特定IP段时才认为有重叠
		for _, ipSegments := range areaRuleMap {
			for _, ipSegment := range ipSegments {
				if ipSegment == "***********/24" && len(ipSegments) > 1 {
					return true
				}
			}
		}
		return false
	})

	// 测试用例1: 空规则列表
	t.Run("EmptyRules", func(t *testing.T) {
		err := CheckAreaRules([]*AreaRule{})
		assert.Error(t, err)
		assert.Equal(t, "区域规则不能为空", err.Error())
	})

	// 测试用例2: 有效的规则列表
	t.Run("ValidRules", func(t *testing.T) {
		rules := []*AreaRule{
			{
				NetworkAreasId:   1,
				NetworkAreasName: "区域1",
				IpRange:          []string{"10.0.0.0/8"},
			},
			{
				NetworkAreasId:   2,
				NetworkAreasName: "区域2",
				IpRange:          []string{"**********/12"},
			},
		}
		err := CheckAreaRules(rules)
		assert.NoError(t, err)
	})

	// 测试用例3: 无效的区域ID
	t.Run("InvalidAreaId", func(t *testing.T) {
		rules := []*AreaRule{
			{
				NetworkAreasId:   999, // 不存在的区域ID
				NetworkAreasName: "不存在的区域",
				IpRange:          []string{"10.0.0.0/8"},
			},
		}
		err := CheckAreaRules(rules)
		assert.Error(t, err)
		assert.Equal(t, "区域ID不存在", err.Error())
	})

	// 测试用例4: 重复的区域ID
	t.Run("DuplicateAreaId", func(t *testing.T) {
		rules := []*AreaRule{
			{
				NetworkAreasId:   1,
				NetworkAreasName: "区域1",
				IpRange:          []string{"10.0.0.0/8"},
			},
			{
				NetworkAreasId:   1, // 重复的区域ID
				NetworkAreasName: "区域1",
				IpRange:          []string{"**********/12"},
			},
		}
		err := CheckAreaRules(rules)
		assert.Error(t, err)
		assert.Equal(t, "区域ID重复", err.Error())
	})

	// 测试用例5: 无效的IP段格式
	t.Run("InvalidIpSegment", func(t *testing.T) {
		rules := []*AreaRule{
			{
				NetworkAreasId:   1,
				NetworkAreasName: "区域1",
				IpRange:          []string{"invalid-cidr"}, // 无效的CIDR格式
			},
		}
		err := CheckAreaRules(rules)
		assert.Error(t, err)
		assert.Equal(t, "区域IP段格式错误", err.Error())
	})

	// 测试用例6: 重复的IP段
	t.Run("DuplicateIpSegment", func(t *testing.T) {
		rules := []*AreaRule{
			{
				NetworkAreasId:   1,
				NetworkAreasName: "区域1",
				IpRange:          []string{"10.0.0.0/8", "10.0.0.0/8"}, // 同一区域内重复的IP段
			},
		}
		err := CheckAreaRules(rules)
		assert.Error(t, err)
		assert.Equal(t, "区域IP段重复", err.Error())
	})

	// 测试用例7: 重叠的IP段
	t.Run("OverlappingIpSegments", func(t *testing.T) {
		rules := []*AreaRule{
			{
				NetworkAreasId:   1,
				NetworkAreasName: "区域1",
				IpRange:          []string{"***********/24", "10.0.0.0/8"}, // 模拟重叠的IP段
			},
			{
				NetworkAreasId:   2,
				NetworkAreasName: "区域2",
				IpRange:          []string{"***********/21", "10.0.0.0/8"}, // 模拟重叠的IP段
			},
		}
		err := CheckAreaRules(rules)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "区域IP段重叠")
	})

	// 测试用例8: 区域ID为0
	t.Run("ZeroAreaId", func(t *testing.T) {
		rules := []*AreaRule{
			{
				NetworkAreasId:   0, // 无效的区域ID
				NetworkAreasName: "无效区域",
				IpRange:          []string{"10.0.0.0/8"},
			},
		}
		err := CheckAreaRules(rules)
		assert.Error(t, err)
		assert.Equal(t, "区域规则格式错误,区域ID不能为空,区域IP段不能为空", err.Error())
	})

	// 测试用例9: 空IP段
	t.Run("EmptyIpRange", func(t *testing.T) {
		rules := []*AreaRule{
			{
				NetworkAreasId:   1,
				NetworkAreasName: "区域1",
				IpRange:          []string{}, // 空IP段
			},
		}
		err := CheckAreaRules(rules)
		assert.Error(t, err)
		assert.Equal(t, "区域规则格式错误,区域ID不能为空,区域IP段不能为空", err.Error())
	})
}
