package nodes

import (
	"crypto/md5"
	"encoding/hex"
	"fmt"
	"fobrain/fobrain/app/services/node/huawei_hk_cloud"
	huawei_hk_cloud2 "fobrain/models/elastic/source/huawei_hk_cloud"
	"fobrain/models/mysql/workbench"
	"math/rand"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"

	"fobrain/fobrain/app/services/node/logs"
	"fobrain/fobrain/common/request"
	"fobrain/fobrain/common/response"
	"fobrain/initialize/mysql"
	"fobrain/models/mysql/data_source"
	"fobrain/pkg/utils"
)

// UpdateHuaweiHkCloud 更新节点数据
func UpdateHuaweiHkCloud(ctx *gin.Context) error {
	params, err := request.Validate(ctx, &struct {
		Id          uint64 `json:"id" uri:"id" form:"id" validate:"required,number" zh:"节点ID"`
		Name        string `json:"name" uri:"name" form:"name" validate:"required,omitempty" zh:"节点名称"`
		RepeatType  string `json:"repeat_type" uri:"repeat_type" form:"repeat_type" validate:"required" zh:"数据同步配置"`
		Date        string `json:"date" uri:"date" form:"date" zh:"数据同步配置"`
		StartTime   string `json:"start_time" uri:"start_time" form:"start_time" validate:"required" zh:"数据同步配置"`
		IP          string `json:"ip" uri:"ip" form:"types"  ip:"required,ipv4" zh:"节点地址"`
		Account     string `json:"account" uri:"account" form:"account" validate:"required" zh:"节点账号"`
		Password    string `json:"password" uri:"password" form:"password" validate:"required" zh:"节点密码"`
		Protocol    string `json:"protocol" uri:"protocol" form:"protocol"  default:"https" zh:"协议"`
		NetworkType string `json:"network_type" uri:"network_type" form:"network_type" zh:"网络类型"`
		DataTypes   []int  `json:"data_types" uri:"data_types" form:"data_types" zh:"节点类型"`
		SNField     string `json:"sn_field" uri:"sn_field" form:"sn_field" zh:"节点数据sn"`
		PersonField string `json:"person_field" uri:"person_field" form:"person_field" zh:"人员映射字段"`
	}{})
	if err != nil {
		return err
	}

	nodeInfo, err := data_source.NewNodeModel().First(mysql.WithColumnValue("id", params.Id))
	if err != nil {
		return err
	}

	// 验证 D01 节点配置能否连接上
	cli := huawei_hk_cloud.New()
	cli.UserName = params.Account
	cli.Password = params.Password
	cli.Protocol = params.Protocol
	cli.Ip = params.IP
	if exists, err := data_source.NewNodeConfigModel().IpExists(params.Id, 0, params.IP); exists || err != nil {
		return response.FailWithMessage(ctx, "该IP已存在,请勿重复添加")
	}
	_, err = cli.GetToken()
	if err != nil {
		return response.FailWithMessage(ctx, "节点认证错误")
	}

	// 将节点的任务类型保存为以 , 分隔的数字类型的字符串
	typesSlice := cast.ToStringSlice(params.DataTypes)
	types := strings.Join(typesSlice, ",")

	nodeInfo.Name = params.Name
	nodeInfo.DataTypes = types
	err = data_source.NewNodeModel().Updates(*nodeInfo, mysql.WithSelect("name", "use", "data_types"))
	if err != nil {
		return err
	}
	//json转map
	list, _ := utils.StructToMap(params, "json")
	for k, v := range list {
		if k == "id" || k == "name" || v == "" {
			continue
		}
		err := data_source.NewNodeConfigModel().Update(data_source.DataNodeConfig{
			NodeId: nodeInfo.Id,
			Key:    k,
			Value:  cast.ToString(v),
		})
		if err != nil {
			return response.FailWithMessage(ctx, err.Error())
		}
	}
	return response.Ok(ctx)
}

// HuaweiHkCloud 添加节点数据
func HuaweiHkCloud(c *gin.Context) error {
	params, err := request.Validate(c, &struct {
		SourceId    uint64 `json:"sources_id" uri:"sources_id" form:"sources_id" validate:"required" zh:"数据源ID"`
		Name        string `json:"name" uri:"name" form:"name" validate:"required" zh:"节点名称"`
		Types       string `json:"types" uri:"types" form:"types" zh:"节点类型"`
		IP          string `json:"ip" uri:"ip" form:"types"  ip:"required,ipv4" zh:"节点地址"`
		Account     string `json:"account" uri:"account" form:"account" validate:"required" zh:"节点账号"`
		Password    string `json:"password" uri:"password" form:"password" validate:"required" zh:"节点密码"`
		Protocol    string `json:"protocol" uri:"protocol" form:"protocol"  default:"https" zh:"协议"`
		RepeatType  string `json:"repeat_type" uri:"repeat_type" form:"repeat_type" validate:"required" zh:"数据同步配置"`
		Date        string `json:"date" uri:"date" form:"date" zh:"数据同步配置"`
		StartTime   string `json:"start_time" uri:"start_time" form:"start_time" validate:"required" zh:"数据同步配置"`
		DataType    string `json:"data_type" uri:"data_type" form:"data_type" zh:"时间"`
		AreaId      uint64 `json:"area_id" uri:"area_id" form:"area_id" zh:"区域ID"`
		NetworkType string `json:"network_type" uri:"network_type" form:"network_type" zh:"网络类型"`
		DataTypes   []int  `json:"data_types" uri:"data_types" form:"data_types" zh:"节点类型"`
		SNField     string `json:"sn_field" uri:"sn_field" form:"sn_field" zh:"节点数据sn"`
		PersonField string `json:"person_field" uri:"person_field" form:"person_field" zh:"人员映射字段"`
	}{})
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	sourceInfo, err := data_source.NewSourceModel().First(mysql.WithWhere("id", params.SourceId))
	if err != nil {
		return response.FailWithMessage(c, "数据源验证不通过")
	}
	if sourceInfo.Type != "huawei_hk_cloud" {
		return response.FailWithMessage(c, "数据源验证不通过")
	}
	num, err := data_source.NewNodeModel().Total(mysql.WithWhere("source_id", params.SourceId), mysql.WithWhere("name", params.Name))
	if err != nil {
		return response.FailWithMessage(c, "该节点已添加,请勿重复添加")
	}
	if num > 0 {
		return response.FailWithMessage(c, "该节点已添加,请勿重复添加")
	}
	if exists, err := data_source.NewNodeConfigModel().IpExists(0, params.SourceId, params.IP); exists || err != nil {
		return response.FailWithMessage(c, "该IP已存在,请勿重复添加")
	}

	nodeIds, err := data_source.NewNodeModel().GetIds(params.SourceId)
	if err != nil {
		return response.FailWithMessage(c, "寻找节点错误")
	}
	_, err = data_source.NewNodeConfigModel().GetIpTotal(nodeIds, params.IP)
	if err != nil {
		return response.FailWithMessage(c, "寻找Ip错误")
	}

	// 将节点的任务类型保存为以 , 分隔的数字类型的字符串
	typesSlice := cast.ToStringSlice(params.DataTypes)
	types := strings.Join(typesSlice, ",")

	node := data_source.Node{
		SourceId:  params.SourceId,
		Name:      params.Name,
		Source:    sourceInfo.Type,
		Types:     params.Types,
		Status:    3,
		Use:       1,
		AreaId:    params.AreaId,
		DataTypes: types,
	}
	// 验证 节点配置能否连接上
	cli := huawei_hk_cloud.New()
	cli.UserName = params.Account
	cli.Password = params.Password
	cli.Protocol = params.Protocol
	cli.Ip = params.IP
	_, err = cli.GetToken()
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}

	url := fmt.Sprintf("http://%s", params.IP)
	rand.Seed(time.Now().UnixNano())
	randomNum := rand.Intn(1000)
	// 获取当前时间戳
	timestamp := strconv.FormatInt(time.Now().Unix(), 10)

	// 将字符串、随机数和时间戳拼接起来
	data := fmt.Sprintf("%s%d%s", url, randomNum, timestamp)

	// 计算MD5值
	md5Sum := md5.Sum([]byte(data))
	key := hex.EncodeToString(md5Sum[:])

	err = data_source.NewNodeModel().Create(&node).Error
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}

	nodeConfigs := []data_source.DataNodeConfig{
		{
			NodeId: node.Id,
			Key:    "key",
			Value:  key,
		},
		{
			NodeId: node.Id,
			Key:    "ip",
			Value:  params.IP,
		},
		{
			NodeId: node.Id,
			Key:    "protocol",
			Value:  params.Protocol,
		},
		{
			NodeId: node.Id,
			Key:    "account",
			Value:  params.Account,
		},
		{
			NodeId: node.Id,
			Key:    "password",
			Value:  params.Password,
		},
		{
			NodeId: node.Id,
			Key:    "repeat_type",
			Value:  params.RepeatType,
		},
		{
			NodeId: node.Id,
			Key:    "start_time",
			Value:  params.StartTime,
		},
		{
			NodeId: node.Id,
			Key:    "date",
			Value:  params.Date,
		},
		{
			NodeId: node.Id,
			Key:    "data_type",
			Value:  params.DataType,
		},
		{
			NodeId: node.Id,
			Key:    "network_type",
			Value:  params.NetworkType,
		},
		{
			NodeId: node.Id,
			Key:    "sn_field",
			Value:  params.SNField,
		},
		{
			NodeId: node.Id,
			Key:    "person_field",
			Value:  params.PersonField,
		},
	}
	err = data_source.NewNodeConfigModel().Create(&nodeConfigs).Error
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	logData := logs.Create{
		IP:        params.IP,
		AwardCode: "",
		DataSyncConfig: logs.DataSyncConfig{
			RepeatType:  params.RepeatType,
			Date:        params.Date,
			StartTime:   params.StartTime,
			NetDomainId: "",
		},
	}
	// 日志-新增节点
	err = logs.LogCreate(node.Id, params.Name, logData, logs.NodeLogCreate)
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	return response.Ok(c)
}

// GetField 获取源字段
func GetField(c *gin.Context) error {
	listFields := huawei_hk_cloud2.NewHuaweiHkCloudAssetsModel().GetAssetListFields()
	var fields []string
	for _, field := range listFields {
		if key, exists := field["key"]; exists {
			fields = append(fields, key)
		}
	}
	return response.OkWithData(c, fields)
}

// CheckHuaweiHkCloudStatus 检测节点状态
func CheckHuaweiHkCloudStatus(c *gin.Context) error {
	params, err := request.Validate(c, &struct {
		Id uint64 `json:"id" uri:"id" form:"id" validate:"required" zh:"节点ID"`
	}{})

	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}

	node, err := data_source.NewNodeModel().First(mysql.WithColumnValue("id", params.Id))
	if err != nil {
		return err
	}

	cli := huawei_hk_cloud.New()
	if err = cli.Client.SetNode(node.Id); err != nil {
		return err
	}
	cli.UserName = cli.Client.GetConfig("account").(string)
	cli.Password = cli.Client.GetConfig("password").(string)
	cli.Ip = cli.Client.GetConfig("ip").(string)
	cli.Protocol = cli.Client.GetConfig("protocol").(string)
	_, err = cli.GetToken()
	if err != nil {
		node.Status = data_source.StatusNONormal
		workbench.ProbeAbnormalSend(fmt.Sprintf("D01 %d探针异常", params.Id))
	} else {
		node.Status = data_source.StatusNormal
	}

	err = data_source.NewNodeModel().Update(*node)
	if err != nil {
		return err
	}
	return response.Ok(c)
}

func HuaweiHkCloudTest(c *gin.Context) error {
	params, err := request.Validate(c, &struct {
		IP       string `json:"ip" uri:"ip" form:"types"  ip:"required,ipv4" zh:"节点地址"`
		Account  string `json:"account" uri:"account" form:"account" validate:"required" zh:"节点账号"`
		Password string `json:"password" uri:"password" form:"password" validate:"required" zh:"节点密码"`
		Protocol string `json:"protocol" uri:"protocol" form:"protocol"  default:"https" zh:"协议"`
	}{})
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	cli := huawei_hk_cloud.New()
	cli.UserName = params.Account
	cli.Password = params.Password
	cli.Protocol = params.Protocol
	cli.Ip = params.IP
	_, err = cli.GetToken()
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	return response.Ok(c)
}
