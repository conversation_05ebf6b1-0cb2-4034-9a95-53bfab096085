package nodes

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"fobrain/fobrain/app/services/node/changting_waf"
	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/workbench"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func TestChangTingWafNodeParamErr(t *testing.T) {
	w := httptest.NewRecorder()
	req := httptest.NewRequest("POST", "/api/v1/data/node/longi_waf", nil)
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	err := ChangTingWafNode(c)
	assert.Nil(t, err)
	assert.Equal(t, `{"code":400,"message":"数据源ID为必填字段","data":{}}`, w.Body.String())
}

func TestChangTingWafNode(t *testing.T) {
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()

	t.Run("param err", func(t *testing.T) {
		w := httptest.NewRecorder()
		req := httptest.NewRequest("POST", "/api/v1/data/node/longi_waf", nil)
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := ChangTingWafNode(c)
		assert.Nil(t, err)
		assert.Equal(t, `{"code":400,"message":"数据源ID为必填字段","data":{}}`, w.Body.String())
	})

	t.Run("success", func(t *testing.T) {
		mockDb.ExpectQuery("SELECT * FROM `data_sources` WHERE `id` = ? ORDER BY `data_sources`.`id` LIMIT 1").WithArgs(8).
			WillReturnRows(sqlmock.NewRows([]string{"id", "name", "type"}).AddRow(2, "waf", "tdp"))

		mockDb.ExpectQuery("SELECT count(*) FROM `data_nodes` WHERE `source_id` = ? AND `name` = ? AND deleted_at IS NULL").WithArgs(8, "waf001").
			WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(0))

		mockDb.ExpectQuery("SELECT * FROM `data_node_configs` WHERE `key` =? AND value =?").WithArgs("ip", "************").
			WillReturnRows(sqlmock.NewRows([]string{"id", "name", "type"}))

		mockDb.Mock.ExpectBegin()
		mockDb.ExpectExec(
			"INSERT INTO `data_nodes` (`created_at`,`updated_at`,`deleted_at`,`source_id`,`area_type`,`area_rules`,`area_id`,`name`,`source`,`types`,`status`,`use`,`last_task_at`,`last_task_status`,`last_sync_result`,`data_types`,`mode`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)",
		).WillReturnResult(sqlmock.NewResult(1, 1))
		mockDb.Mock.ExpectCommit()

		mockDb.Mock.ExpectBegin()
		mockDb.ExpectExec(
			"INSERT INTO `data_node_configs` (`created_at`,`updated_at`,`node_id`,`key`,`value`,`hidden`) VALUES (?,?,?,?,?,?),(?,?,?,?,?,?),(?,?,?,?,?,?),(?,?,?,?,?,?),(?,?,?,?,?,?),(?,?,?,?,?,?),(?,?,?,?,?,?),(?,?,?,?,?,?)",
		).WillReturnResult(sqlmock.NewResult(1, 1))

		mockDb.Mock.ExpectCommit()

		mockDb.Mock.ExpectBegin()
		mockDb.ExpectExec(
			"INSERT INTO `data_node_logs` (`created_at`,`updated_at`,`node_id`,`name`,`log_type`,`content`) VALUES (?,?,?,?,?,?)",
		).WillReturnResult(sqlmock.NewResult(1, 1))
		mockDb.Mock.ExpectCommit()

		//patchIpExists := gomonkey.ApplyMethodReturn(data_source.NewNodeConfigModel(), "IpExists", false, nil)
		time.Sleep(time.Millisecond * 300)
		//patchResponse := gomonkey.ApplyMethodReturn(changting_waf.NewChangtingWaf(nil, nil), "GetResponse", "", nil)

		w := httptest.NewRecorder()
		val, _ := json.Marshal(ChangTingWafCreate{
			SourcesId: 8,
			ChangTingWafBase: ChangTingWafBase{
				Name:       "waf001",
				AreaId:     1,
				ApiToken:   "waf001",
				AreaType:   "指定",
				RepeatType: "waf001",
				Date:       "waf001",
				StartTime:  "waf001",
				IP:         "************",
				Protocol:   "https",
			},
		})

		req := httptest.NewRequest("POST", "/api/v1/data/node/longi_waf", strings.NewReader(string(val)))
		req.Header.Set("Content-Type", "application/json")

		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		_ = ChangTingWafNode(c)

		//patchIpExists.Reset()

		//patchResponse.Reset()

		//assert.Nil(t, err)
		//assert.Equal(t, "{\"code\":0,\"message\":\"Success\",\"data\":{}}", w.Body.String())
	})
}

func TestCheckChangTingWafStatus(t *testing.T) {
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()

	t.Run("success", func(t *testing.T) {
		mockDb.ExpectQuery("SELECT * FROM `data_nodes` WHERE `id` = ? AND `data_nodes`.`deleted_at` IS NULL ORDER BY `data_nodes`.`id` LIMIT 1").WithArgs(8).
			WillReturnRows(sqlmock.NewRows([]string{"id", "name", "type"}).AddRow(8, "waf", "tdp"))
		mockDb.ExpectQuery("SELECT * FROM `data_node_configs` WHERE `node_id` = ?").WithArgs(8).
			WillReturnRows(sqlmock.NewRows([]string{"id", "key", "value"}).AddRow(int64(8), "key", "value"))

		mockDb.Mock.ExpectBegin()
		mockDb.ExpectExec(
			"UPDATE `data_nodes` SET `id`=?,`updated_at`=?,`name`=?,`status`=? WHERE `id` = ? AND `data_nodes`.`deleted_at` IS NULL",
		).WillReturnResult(sqlmock.NewResult(1, 1))
		mockDb.Mock.ExpectCommit()
		gomonkey.ApplyFuncReturn(workbench.ProbeAbnormalSend)
		w := httptest.NewRecorder()

		req := httptest.NewRequest("GET", "/api/v1/data/node/longi_waf/8/check_status?id=8", nil)
		req.Header.Set("Content-Type", "application/json")

		cli := changting_waf.NewChangtingWaf(nil, nil)
		server := testcommon.SetupMockServer(`{
			"err": "",
			"msg": ""
		}`, http.StatusOK)
		defer server.Close()

		if err := cli.SetConfig(map[string]any{
			"api_token": "aarrr", "ip": testcommon.ExtractAfterProtocol(server.URL), "protocol": "http",
		}); err != nil {
			fmt.Println(err)
		}

		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := CheckChangTingWafStatus(c)
		assert.Nil(t, err)
		assert.Equal(t, "{\"code\":0,\"message\":\"Success\",\"data\":{}}", w.Body.String())
	})
}

func TestUpdateChangTingWaf(t *testing.T) {
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()

	t.Run("success", func(t *testing.T) {
		mockDb.ExpectQuery("SELECT * FROM `data_node_configs` WHERE `key` =? AND value =?").WithArgs("ip", "************").
			WillReturnRows(sqlmock.NewRows([]string{"id", "name", "type"}))
		mockDb.ExpectQuery("SELECT * FROM `data_nodes` WHERE `id` = ?").WithArgs(1).
			WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))

		mockDb.Mock.ExpectBegin()
		mockDb.ExpectExec(
			"UPDATE `data_nodes` SET `updated_at`=?,`area_id`=?,`name`=?,`data_types`=? WHERE `id` = ? AND `data_nodes`.`deleted_at` IS NULL",
		).WillReturnResult(sqlmock.NewResult(1, 1))
		mockDb.Mock.ExpectCommit()

		//patchIpExists := gomonkey.ApplyMethodReturn(data_source.NewNodeConfigModel(), "IpExists", false, nil)

		patchResponse := gomonkey.ApplyMethodReturn(changting_waf.NewChangtingWaf(nil, nil), "GetResponse", "", nil)

		patchUpdate := gomonkey.ApplyMethodReturn(data_source.NewNodeConfigModel(), "Update", nil)

		w := httptest.NewRecorder()
		val, _ := json.Marshal(ChangTingWafUpdate{
			Id: 1,
			ChangTingWafBase: ChangTingWafBase{
				Name:       "waf001",
				AreaId:     1,
				AreaType:   "指定",
				ApiToken:   "waf001",
				RepeatType: "waf001",
				Date:       "waf001",
				StartTime:  "waf001",
				IP:         "************",
				Protocol:   "https",
			},
		})

		req := httptest.NewRequest("PUT", "/api/v1/data/node/longi_waf", strings.NewReader(string(val)))
		req.Header.Set("Content-Type", "application/json")

		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := UpdateChangTingWaf(c)

		//patchIpExists.Reset()

		patchResponse.Reset()

		patchUpdate.Reset()

		assert.Nil(t, err)
		assert.Equal(t, "{\"code\":0,\"message\":\"Success\",\"data\":{}}", w.Body.String())
	})
}

func TestChangTingWafNodeTest(t *testing.T) {
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		res := map[string]any{}
		w.WriteHeader(http.StatusOK)
		_ = json.NewEncoder(w).Encode(res)
	}))
	defer server.Close()

	w := httptest.NewRecorder()
	val, _ := json.Marshal(ChangTingWafCreate{
		SourcesId: 8,
		ChangTingWafBase: ChangTingWafBase{
			Name:       "waf001",
			AreaId:     1,
			AreaType:   "指定",
			ApiToken:   "waf001",
			RepeatType: "waf001",
			Date:       "waf001",
			StartTime:  "waf001",
			IP:         server.Listener.Addr().String(),
			Protocol:   "http",
		},
	})

	req := httptest.NewRequest("POST", "/api/v1/data/node/longi_waf/test", strings.NewReader(string(val)))
	req.Header.Set("Content-Type", "application/json")

	c, _ := gin.CreateTestContext(w)
	c.Request = req

	err := ChangTingWafNodeTest(c)
	assert.Nil(t, err)
	assert.Equal(t, w.Body.String(), `{"code":0,"message":"Success","data":{}}`)
}
