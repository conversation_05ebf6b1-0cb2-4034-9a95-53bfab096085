package nodes

import (
	"encoding/json"
	"errors"
	"fmt"
	"fobrain/fobrain/app/services/node/huawei_hk_cloud"
	"fobrain/models/mysql/data_source"
	"net/http"
	"net/http/httptest"
	"reflect"
	"strings"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"

	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/models/mysql/workbench"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func TestCheckHuaweiHkCloudStatus(t *testing.T) {
	t.Run("Param err", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()
		gomonkey.ApplyFuncReturn(workbench.ProbeAbnormalSend)

		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/v1/data/node/huawei_hk_cloud/check_status", nil)
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := CheckHuaweiHkCloudStatus(c)
		assert.Nil(t, err)
		assert.Equal(t, w.Body.String(), "{\"code\":400,\"message\":\"节点ID为必填字段\",\"data\":{}}")
	})
}

func TestUpdateHuaweiHkCloud(t *testing.T) {
	t.Run("param err", func(t *testing.T) {
		w := httptest.NewRecorder()
		req := httptest.NewRequest("PUT", "/api/v1/data/node/huawei_hk_cloud", nil)
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := UpdateHuaweiHkCloud(c)
		assert.Equal(t, err.Error(), "节点ID为必填字段")
	})

	t.Run("missing protocol scheme", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()
		mockDb.ExpectQuery("SELECT * FROM `data_nodes` WHERE `id` = ? AND `data_nodes`.`deleted_at` IS NULL ORDER BY `data_nodes`.`id` LIMIT 1").
			WillReturnRows(mockDb.NewRows([]string{"id", "name", "type"}).AddRow(1, "name", "huawei_hk_cloud"))
		mockDb.ExpectQuery("SELECT * FROM `data_node_configs` WHERE `key` =? AND value =?").
			WithArgs("ip", "***********").
			WillReturnRows(mockDb.NewRows(nil))

		w := httptest.NewRecorder()
		req := httptest.NewRequest("PUT", "/api/v1/data/node/huawei_hk_cloud", strings.NewReader(`{
			"id": 1,
			"name": "huawei_hk_cloud",
			"ip": "***********",
			"app_code": "1",
			"account": "abel",
			"app_secret": "1",
			"repeat_type": "1",
			"password": "1",
			"start_time": "2024-1-21"
		}`))
		req.Header.Set("Content-Type", "application/json")
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := UpdateHuaweiHkCloud(c)
		assert.Nil(t, err)
		assert.Equal(t, w.Body.String(), `{"code":400,"message":"节点认证错误","data":{}}`)
	})

	t.Run("success", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()

		mockDb.ExpectQuery("SELECT * FROM `data_nodes` WHERE `id` = ?").WithArgs(1).
			WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))

		mockDb.ExpectQuery("SELECT * FROM `data_node_configs` WHERE `key` =? AND value =?").
			WithArgs("ip", "***********").
			WillReturnRows(mockDb.NewRows(nil))

		mockDb.Mock.ExpectBegin()
		mockDb.ExpectExec(
			"UPDATE `data_nodes` SET `updated_at`=?,`name`=?,`use`=?,`data_types`=? WHERE `id` = ? AND `data_nodes`.`deleted_at` IS NULL",
		).WillReturnResult(sqlmock.NewResult(1, 1))
		mockDb.Mock.ExpectCommit()

		patchCli := gomonkey.ApplyMethodReturn(huawei_hk_cloud.New(), "GetToken", nil, nil)

		patchNodeConfig := gomonkey.ApplyMethodReturn(data_source.NewNodeConfigModel(), "Update", nil)

		w := httptest.NewRecorder()
		req := httptest.NewRequest("PUT", "/api/v1/data/node/huawei_hk_cloud", strings.NewReader(`{
			"id": 1,
			"name": "huawei_hk_cloud",
			"ip": "***********",
			"app_code": "1",
			"account": "abel",
			"app_secret": "1",
			"repeat_type": "1",
			"password": "1",
			"start_time": "2024-1-21",
			"data_types": [1]
		}`))
		req.Header.Set("Content-Type", "application/json")
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := UpdateHuaweiHkCloud(c)

		patchCli.Reset()
		patchNodeConfig.Reset()

		assert.Nil(t, err)
		assert.Equal(t, w.Body.String(), `{"code":0,"message":"Success","data":{}}`)
	})
}

func TestHuaweiHkCloud(t *testing.T) {
	t.Run("param err", func(t *testing.T) {
		w := httptest.NewRecorder()
		req := httptest.NewRequest("POST", "/api/v1/data/node/huawei_hk_cloud?page=1&per_page=10", nil)
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := HuaweiHkCloud(c)
		assert.Nil(t, err)
	})

	t.Run("missing protocol scheme", func(t *testing.T) {
		w := httptest.NewRecorder()
		req := httptest.NewRequest("POST", "/api/v1/data/node/huawei_hk_cloud", strings.NewReader(`{
			"name": "huawei_hk_cloud",
			"area_type": "指定",
			"area_id": 1,
			"ip": "***********",
			"app_code": "1",
			"account": "abel",
			"app_secret": "1",
			"repeat_type": "1",
			"start_time": "2024-1-21",
			"sources_id": 1,
			"appsecret": "1",
			"appkey": "1",
			"password": "sdfasdf"
		}`))
		req.Header.Set("Content-Type", "application/json")
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := HuaweiHkCloud(c)
		assert.Nil(t, err)
		assert.Equal(t, w.Body.String(), "{\"code\":400,\"message\":\"数据源验证不通过\",\"data\":{}}")
	})

	t.Run("success", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()

		mockDb.ExpectQuery("SELECT * FROM `data_sources` WHERE `id` = ? ORDER BY `data_sources`.`id` LIMIT 1").
			WillReturnRows(mockDb.NewRows([]string{"id", "name", "type"}).AddRow(1, "name", "huawei_hk_cloud"))

		mockDb.ExpectQuery("SELECT count(*) FROM `data_nodes` WHERE `source_id` = ? AND `name` = ? AND deleted_at IS NULL").
			WithArgs(int64(1), "huawei_hk_cloud").
			WillReturnRows(mockDb.NewRows([]string{"num"}).AddRow(0))

		mockDb.ExpectQuery("SELECT * FROM `data_node_configs` WHERE `key` =? AND value =?").
			WithArgs("ip", "***********").
			WillReturnRows(mockDb.NewRows(nil))

		mockDb.ExpectQuery("SELECT `id` FROM `data_nodes` WHERE source_id = ?").
			WithArgs(1).
			WillReturnRows(mockDb.NewRows(nil))

		mockDb.Mock.ExpectBegin()
		mockDb.ExpectExec(
			"INSERT INTO `data_nodes` (`created_at`,`updated_at`,`deleted_at`,`source_id`,`area_id`,`name`,`source`,`types`,`status`,`use`,`last_task_at`,`last_task_status`,`last_sync_result`,`data_types`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?)",
		).WillReturnResult(sqlmock.NewResult(1, 1))
		mockDb.Mock.ExpectCommit()

		mockDb.Mock.ExpectBegin()
		mockDb.ExpectExec(
			"INSERT INTO `data_node_configs` (`created_at`,`updated_at`,`node_id`,`key`,`value`,`hidden`) VALUES (?,?,?,?,?,?),(?,?,?,?,?,?),(?,?,?,?,?,?),(?,?,?,?,?,?),(?,?,?,?,?,?),(?,?,?,?,?,?),(?,?,?,?,?,?),(?,?,?,?,?,?)",
		).WillReturnResult(sqlmock.NewResult(1, 1))
		mockDb.Mock.ExpectCommit()

		mockDb.Mock.ExpectBegin()
		mockDb.ExpectExec(
			"INSERT INTO `data_node_logs` (`created_at`,`updated_at`,`node_id`,`name`,`log_type`,`content`) VALUES (?,?,?,?,?,?)",
		).WillReturnResult(sqlmock.NewResult(1, 1))
		mockDb.Mock.ExpectCommit()

		cli := huawei_hk_cloud.New()

		patches := gomonkey.ApplyMethod(reflect.TypeOf(cli), "GetToken", func(client *huawei_hk_cloud.SourceNode) (string, error) {
			return "mocked-token", nil
		})

		patchCli := gomonkey.ApplyMethodReturn(huawei_hk_cloud.New(), "GetToken", nil, nil)

		w := httptest.NewRecorder()
		req := httptest.NewRequest("POST", "/api/v1/data/node/huawei_hk_cloud", strings.NewReader(`{
			"name": "huawei_hk_cloud",
			"area_type": "指定",
			"area_id": 1,
			"ip": "***********",
			"app_code": "1",
			"account": "abel",
			"app_secret": "1",
			"repeat_type": "1",
			"start_time": "2024-1-21",
			"sources_id": 1,
			"appsecret": "1",
			"appkey": "1",
			"password": "sdfasdf",
			"data_types": [1]
		}`))

		req.Header.Set("Content-Type", "application/json")

		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := HuaweiHkCloud(c)

		patches.Reset()
		patchCli.Reset()

		assert.Nil(t, err)
	})

	t.Run("param count > 0", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()
		mockDb.ExpectQuery("SELECT * FROM `data_sources` WHERE `id` = ? ORDER BY `data_sources`.`id` LIMIT 1").
			WillReturnRows(mockDb.NewRows([]string{"id", "name", "type"}).AddRow(1, "name", "huawei_hk_cloud"))

		mockDb.ExpectQuery("SELECT count(*) FROM `data_nodes` WHERE `source_id` = ? AND `name` = ? AND deleted_at IS NULL").
			WithArgs(int64(1), "huawei_hk_cloud").
			WillReturnRows(mockDb.NewRows([]string{"num"}).AddRow(1))

		w := httptest.NewRecorder()
		req := httptest.NewRequest("POST", "/api/v1/data/node/huawei_hk_cloud", strings.NewReader(`{
			"name": "huawei_hk_cloud",
			"area_type": "指定",
			"area_id": 1,
			"ip": "***********",
			"app_code": "1",
			"account": "abel",
			"app_secret": "1",
			"repeat_type": "1",
			"start_time": "2024-1-21",
			"sources_id": 1,
			"appsecret": "1",
			"appkey": "1",
			"password": "sdfasdf"
		}`))

		req.Header.Set("Content-Type", "application/json")

		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := HuaweiHkCloud(c)
		assert.Nil(t, err)
	})

	t.Run("param type != huawei_hk_cloud", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()
		mockDb.ExpectQuery("SELECT * FROM `data_sources` WHERE `id` = ? ORDER BY `data_sources`.`id` LIMIT 1").
			WillReturnRows(mockDb.NewRows([]string{"id", "name", "type"}).AddRow(1, "name", "foeye"))

		w := httptest.NewRecorder()
		req := httptest.NewRequest("POST", "/api/v1/data/node/huawei_hk_cloud", strings.NewReader(`{
			"name": "huawei_hk_cloud",
			"area_type": "指定",
			"area_id": 1,
			"ip": "***********",
			"app_code": "1",
			"account": "abel",
			"app_secret": "1",
			"repeat_type": "1",
			"start_time": "2024-1-21",
			"sources_id": 1,
			"appsecret": "1",
			"appkey": "1",
			"password": "sdfasdf"
		}`))

		req.Header.Set("Content-Type", "application/json")

		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := HuaweiHkCloud(c)
		assert.Nil(t, err)
	})

	t.Run("param type err", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()
		mockDb.ExpectQuery("SELECT * FROM `data_sources` WHERE `id` = ? ORDER BY `data_sources`.`id` LIMIT 1").
			WillReturnError(errors.New("err"))

		w := httptest.NewRecorder()
		req := httptest.NewRequest("POST", "/api/v1/data/node/huawei_hk_cloud", strings.NewReader(`{
			"name": "huawei_hk_cloud",
			"area_type": "指定",
			"area_id": 1,
			"ip": "***********",
			"app_code": "1",
			"account": "abel",
			"app_secret": "1",
			"repeat_type": "1",
			"start_time": "2024-1-21",
			"sources_id": 1,
			"appsecret": "1",
			"appkey": "1",
			"password": "sdfasdf"
		}`))

		req.Header.Set("Content-Type", "application/json")

		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := HuaweiHkCloud(c)
		assert.Nil(t, err)
	})

	t.Run("param count err", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()
		mockDb.ExpectQuery("SELECT * FROM `data_sources` WHERE `id` = ? ORDER BY `data_sources`.`id` LIMIT 1").
			WillReturnRows(mockDb.NewRows([]string{"id", "name", "type"}).AddRow(1, "name", "huawei_hk_cloud"))

		mockDb.ExpectQuery("SELECT count(*) FROM `data_nodes` WHERE `source_id` = ? AND `name` = ? AND deleted_at IS NULL").
			WillReturnError(errors.New("err"))
		w := httptest.NewRecorder()
		req := httptest.NewRequest("POST", "/api/v1/data/node/huawei_hk_cloud", strings.NewReader(`{
			"name": "huawei_hk_cloud",
			"area_type": "指定",
			"area_id": 1,
			"ip": "***********",
			"app_code": "1",
			"account": "abel",
			"app_secret": "1",
			"repeat_type": "1",
			"start_time": "2024-1-21",
			"sources_id": 1,
			"appsecret": "1",
			"appkey": "1",
			"password": "sdfasdf"
		}`))

		req.Header.Set("Content-Type", "application/json")

		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := HuaweiHkCloud(c)
		assert.Nil(t, err)
	})

}

func TestGetField(t *testing.T) {
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/data/node/huawei_hk_cloud/field", nil)
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	err := HuaweiHkCloud(c)
	assert.Nil(t, err)
}

func TestHuaweiHkCloudTest(t *testing.T) {
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		res := map[string]any{
			"access": "token",
		}
		w.WriteHeader(http.StatusOK)
		_ = json.NewEncoder(w).Encode(res)
	}))
	defer server.Close()

	w := httptest.NewRecorder()
	req := httptest.NewRequest("POST", "/api/v1/data/node/huawei_hk_cloud/test", strings.NewReader(fmt.Sprintf(`{
			"name": "huawei_hk_cloud",
			"area_type": "指定",
			"area_id": 1,
			"ip": "%s",
			"app_code": "1",
			"account": "abel",
			"app_secret": "1",
			"repeat_type": "1",
			"start_time": "2024-1-21",
			"sources_id": 1,
			"appsecret": "1",
			"appkey": "1",
			"password": "sdfasdf",
    		"protocol": "http",
			"data_types": [1]
		}`, server.Listener.Addr().String())))

	req.Header.Set("Content-Type", "application/json")

	c, _ := gin.CreateTestContext(w)
	c.Request = req

	err := HuaweiHkCloudTest(c)
	assert.Nil(t, err)
	assert.Equal(t, w.Body.String(), `{"code":0,"message":"Success","data":{}}`)
}
