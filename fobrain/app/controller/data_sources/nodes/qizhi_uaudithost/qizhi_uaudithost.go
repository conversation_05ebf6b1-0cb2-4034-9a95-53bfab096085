package qizhi_uaudithost

import (
	"encoding/json"
	"fmt"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"

	"fobrain/fobrain/app/controller/data_sources/nodes"
	"fobrain/fobrain/app/services/node/logs"
	"fobrain/fobrain/app/services/node/qizhi_uaudithost"
	"fobrain/fobrain/common/request"
	"fobrain/fobrain/common/response"
	"fobrain/initialize/mysql"
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/workbench"
	"fobrain/pkg/utils"
)

type CustomAccountAndPwdNodeRequest struct {
	Name        string            `json:"name" uri:"name" form:"name" validate:"required" zh:"节点名称"`
	AreaId      uint64            `json:"area_id" uri:"area_id" form:"area_id" validate:"required,min=1" zh:"区域ID"`
	AreaType    string            `json:"area_type" uri:"area_type" form:"area_type" validate:"required,oneof=指定 规则" zh:"区域,指定,规则"`
	AreaRules   []*nodes.AreaRule `json:"area_rules" uri:"area_rules" form:"area_rules" zh:"区域规则"`
	SourceId    uint64            `json:"sources_id" uri:"sources_id" form:"sources_id" validate:"required" zh:"数据源ID"`
	Protocol    string            `json:"protocol" uri:"protocol" form:"protocol"  default:"https" zh:"协议"`
	IP          string            `json:"ip" uri:"ip" form:"ip"  validate:"required" zh:"节点地址"`
	Account     string            `json:"account" uri:"account" form:"account"   zh:"账号"`
	Password    string            `json:"password" uri:"password" form:"password"   zh:"密码"`
	RepeatType  string            `json:"repeat_type" uri:"repeat_type" form:"repeat_type" validate:"required" zh:"数据同步配置 day天 week周 month月"`
	Date        string            `json:"date" uri:"date" form:"date" zh:"数据同步配置 日期"`
	StartTime   string            `json:"start_time" uri:"start_time" form:"start_time" validate:"required" zh:"数据同步配置 时间"`
	NetworkType string            `json:"network_type" uri:"network_type" form:"network_type" zh:"网络类型"`
	Types       string            `json:"types" uri:"types" form:"types" zh:"节点类型"`
	DataTypes   []int             `json:"data_types" uri:"data_types" form:"data_types" zh:"节点类型"`
	PersonField string            `json:"person_field" uri:"person_field" form:"person_field" zh:"人员映射字段"`
}

type CustomAccountAndPwdUpdateRequest struct {
	Id          uint64 `json:"id" uri:"id" form:"id" validate:"required,number" zh:"节点ID"`
	PersonField string `json:"person_field" uri:"person_field" form:"person_field" zh:"人员映射字段"`
	CustomAccountAndPwdNodeRequest
}

// QiZhiUAuditHost 添加齐治堡垒机节点的请求
// 参数:
//
//	c: gin上下文对象
//
// 返回值:
//
//	如果成功返回nil，否则返回错误
func QiZhiUAuditHost(c *gin.Context) error {
	params, err := request.Validate(c, &CustomAccountAndPwdNodeRequest{})
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	// 区域为规则时
	if params.AreaType == "规则" {
		if len(params.AreaRules) == 0 {
			return response.FailWithMessage(c, "区域规则不能为空")
		}
		// 检查区域规则
		if err := nodes.CheckAreaRules(params.AreaRules); err != nil {
			return response.FailWithMessage(c, err.Error())
		}
	}

	sourceInfo, err := data_source.NewSourceModel().First(mysql.WithWhere("id", params.SourceId))
	if err != nil {
		return response.FailWithMessage(c, "数据源验证不通过")
	}
	if sourceInfo.Type != data_source.QiZhiUAuditHost {
		return response.FailWithMessage(c, "数据源验证不通过")
	}
	num, err := data_source.NewNodeModel().Total(mysql.WithWhere("source_id", params.SourceId), mysql.WithWhere("name", params.Name))
	if err != nil {
		return response.FailWithMessage(c, "该节点已添加,请勿重复添加")
	}
	if num > 0 {
		return response.FailWithMessage(c, "该节点已添加,请勿重复添加")
	}

	// 将节点的任务类型保存为以 , 分隔的数字类型的字符串
	typesSlice := cast.ToStringSlice(params.DataTypes)
	types := strings.Join(typesSlice, ",")

	nodeData := data_source.Node{
		SourceId:  params.SourceId,
		AreaType:  params.AreaType,
		AreaId:    params.AreaId,
		Name:      params.Name,
		Source:    params.Types,
		Types:     "",
		Status:    3,
		Use:       1,
		DataTypes: types,
	}
	if params.AreaType == "规则" {
		areaRules, err := json.Marshal(params.AreaRules)
		if err != nil {
			return response.FailWithMessage(c, err.Error())
		}
		nodeData.AreaRules = string(areaRules)
	} else {
		nodeData.AreaRules = ""
	}

	cli := qizhi_uaudithost.NewQiZhiUAuditHost()
	if err = cli.SetConfig(map[string]any{
		"ip": params.IP, "account": params.Account, "password": params.Password, "protocol": params.Protocol,
	}); err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	_, err = cli.GetToken()
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}

	err = data_source.NewNodeModel().Create(&nodeData).Error
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	// 生成配置信息
	list, _ := utils.StructToMap(params, "json")
	nodeConfigs := make([]data_source.DataNodeConfig, 0, len(list))

	for k, v := range list {
		nodeConfigs = append(nodeConfigs, data_source.DataNodeConfig{
			NodeId: nodeData.Id,
			Key:    k,
			Value:  cast.ToString(v),
		})
	}
	// 插入节点配置
	err = data_source.NewNodeConfigModel().Create(&nodeConfigs).Error
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	logData := logs.Create{
		IP:        params.IP,
		AwardCode: "",
		DataSyncConfig: logs.DataSyncConfig{
			RepeatType:  params.RepeatType,
			Date:        params.Date,
			StartTime:   params.StartTime,
			Version:     "",
			NetDomainId: "",
		},
	}
	// 日志-新增节点
	err = logs.LogCreate(nodeData.Id, params.Name, logData, logs.NodeLogCreate)
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	return response.Ok(c)
}

// CheckQiZhiUAuditHostStatus 检查齐治堡垒机节点的状态
//
// 参数:
//
//	c: gin上下文对象
//
// 返回值:
//
//	如果成功返回nil，否则返回错误
func CheckQiZhiUAuditHostStatus(c *gin.Context) error {
	params, err := request.Validate(c, &struct {
		Id uint64 `json:"id" uri:"id" form:"id" validate:"required" zh:"节点ID"`
	}{})

	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}

	nodeData, err := data_source.NewNodeModel().First(mysql.WithColumnValue("id", params.Id))
	if err != nil {
		return err
	}

	cli := qizhi_uaudithost.NewQiZhiUAuditHost()
	if err = cli.SetNode(nodeData.Id); err != nil {
		return err
	}

	_, err = cli.GetToken()
	if err != nil {
		nodeData.Status = data_source.StatusNONormal
		workbench.ProbeAbnormalSend(fmt.Sprintf("齐治堡垒机 %d探针异常", params.Id))
	} else {
		nodeData.Status = data_source.StatusNormal
	}
	err = data_source.NewNodeModel().Update(*nodeData)
	if err != nil {
		return err
	}

	return response.Ok(c)
}

// UpdateQiZhiUAuditHost 更新齐治堡垒机节点信息
//
// 参数:
//
//	c: gin上下文对象
//
// 返回值:
//
//	如果更新成功则返回nil，否则返回错误信息
func UpdateQiZhiUAuditHost(c *gin.Context) error {
	params, err := request.Validate(c, &CustomAccountAndPwdUpdateRequest{})
	if err != nil {
		return err
	}
	cli := qizhi_uaudithost.NewQiZhiUAuditHost()
	if err := cli.SetConfig(map[string]any{
		"ip": params.IP, "account": params.Account, "password": params.Password, "protocol": params.Protocol,
	}); err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	token, err := cli.GetToken()
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	fmt.Print(token)

	// 将节点的任务类型保存为以 , 分隔的数字类型的字符串
	typesSlice := cast.ToStringSlice(params.DataTypes)
	types := strings.Join(typesSlice, ",")

	nodeInfo, err := data_source.NewNodeModel().First(mysql.WithColumnValue("id", params.Id))
	if err != nil {
		return err
	}
	nodeInfo.Name = params.Name
	nodeInfo.AreaId = params.AreaId
	nodeInfo.DataTypes = types
	err = data_source.NewNodeModel().Updates(*nodeInfo, mysql.WithSelect("name", "use", "data_types"))
	if err != nil {
		return err
	}
	//json转map
	list, _ := utils.StructToMap(params.CustomAccountAndPwdNodeRequest, "json")
	for k, v := range list {
		if k == "id" || k == "name" || k == "area_id" || v == "" {
			continue
		}
		err := data_source.NewNodeConfigModel().Update(data_source.DataNodeConfig{
			NodeId: nodeInfo.Id,
			Key:    k,
			Value:  cast.ToString(v),
		})
		if err != nil {
			return response.FailWithMessage(c, err.Error())
		}
	}
	return response.Ok(c)
}

func QiZhiUAuditHostTest(c *gin.Context) error {
	params, err := request.Validate(c, &CustomAccountAndPwdNodeRequest{})
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	cli := qizhi_uaudithost.NewQiZhiUAuditHost()
	if err = cli.SetConfig(map[string]any{
		"ip": params.IP, "account": params.Account, "password": params.Password, "protocol": params.Protocol,
	}); err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	_, err = cli.GetToken()
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	return response.Ok(c)
}
