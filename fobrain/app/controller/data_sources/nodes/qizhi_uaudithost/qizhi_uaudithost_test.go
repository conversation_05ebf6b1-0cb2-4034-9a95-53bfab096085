package qizhi_uaudithost

import (
	"bytes"
	"encoding/json"
	"io/ioutil"
	"net/http"
	"net/http/httptest"
	"reflect"
	"strings"
	"testing"
	"time"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"

	"fobrain/fobrain/app/services/node/qizhi_uaudithost"
	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/models/mysql/data_source"
)

func TestQiZhiUAuditHost(t *testing.T) {
	t.Run("param err", func(t *testing.T) {
		w := httptest.NewRecorder()
		req := httptest.NewRequest("POST", "/api/v1/data/node/qizhi/uaudithost", nil)
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := QiZhiUAuditHost(c)
		assert.Nil(t, err)
	})

	t.Run("missing protocol scheme", func(t *testing.T) {
		w := httptest.NewRecorder()
		req := httptest.NewRequest("POST", "/api/v1/data/node/qizhi/uaudithost", strings.NewReader(`{
				"name":"齐治堡垒机-1",
				"area_id":1,
				"area_type":"指定",
				"protocol":"https",
				"ip":"baidu.com",
				"account":"abcdefg",
				"password":"hijklmn",
				"repeat_type":"day",
				"date":"",
				"start_time":"00:00"
			}`))
		req.Header.Set("Content-Type", "application/json")
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := QiZhiUAuditHost(c)
		assert.Nil(t, err)
		assert.Equal(t, w.Body.String(), "{\"code\":400,\"message\":\"数据源ID为必填字段\",\"data\":{}}")
	})
	t.Run("success", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()
		mockDb.ExpectQuery("SELECT * FROM `data_sources` WHERE `id` = ? ORDER BY `data_sources`.`id` LIMIT 1").
			WillReturnRows(mockDb.NewRows([]string{"id", "name", "type"}).AddRow(1, "name", "aliyun_cloud"))

		time.Sleep(time.Second)
		defer gomonkey.ApplyMethod(reflect.TypeOf(&http.Client{}), "Do", func(client *http.Client, req *http.Request) (*http.Response, error) {
			return &http.Response{
				StatusCode: http.StatusOK,
				Body:       ioutil.NopCloser(bytes.NewBufferString(`{"status":"ok"}`)),
			}, nil
		}).Reset()

		time.Sleep(time.Second)
		defer gomonkey.ApplyMethodReturn(data_source.NewNodeModel(), "Create", nil).Reset()
		time.Sleep(time.Second)
		defer gomonkey.ApplyMethodReturn(data_source.NewNodeConfigModel(), "Create", nil).Reset()
		param, _ := json.Marshal(CustomAccountAndPwdNodeRequest{
			Name:       "阿里云云盾-1",
			AreaId:     1,
			AreaType:   "指定",
			SourceId:   11,
			Account:    "sdlfkajldfas",
			Password:   "dflkajflajldf",
			RepeatType: "day",
			Date:       "",
			StartTime:  "00:00",
			IP:         "***********",
			Protocol:   "https",
		})

		w := httptest.NewRecorder()
		req := httptest.NewRequest("POST", "/api/v1/data/node/qizhi/uaudithost", strings.NewReader(string(param)))
		req.Header.Set("Content-Type", "application/json")

		// Create a Gin context
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		// Call the function under test
		err := QiZhiUAuditHost(c)
		assert.Nil(t, err)
	})
}

func TestCheckQiZhiUAuditHostStatus(t *testing.T) {
	mockDb := testcommon.InitSqlMock()
	defer mockDb.Close()
	mockDb.ExpectQuery("SELECT * FROM `data_nodes` WHERE `id` = ? AND `data_nodes`.`deleted_at` IS NULL ORDER BY `data_nodes`.`id` LIMIT 1").
		WithArgs(1).
		WillReturnRows(mockDb.NewRows([]string{"id"}).AddRow(1))

	mockDb.ExpectQuery("SELECT * FROM `data_node_configs` WHERE `node_id` = ?").
		WithArgs(1).
		WillReturnRows(mockDb.NewRows([]string{"id"}).AddRow(1))

	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyMethodReturn(data_source.NewNodeModel(), "Update", nil).Reset()
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyMethodReturn(qizhi_uaudithost.NewQiZhiUAuditHost(), "GetToken", nil, nil).Reset()

	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/data/node/qizhi/uaudithost/1/check_status?id=1", nil)
	req.Header.Set("Content-Type", "application/json")

	// Create a Gin context
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// Call the function under test
	err := CheckQiZhiUAuditHostStatus(c)
	assert.Nil(t, err)
}

func TestUpdateQiZhiUAuditHost(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()
		mockDb.ExpectQuery("SELECT * FROM `data_nodes` WHERE `id` = ? AND `data_nodes`.`deleted_at` IS NULL ORDER BY `data_nodes`.`id` LIMIT 1").
			WithArgs(61).
			WillReturnRows(mockDb.NewRows([]string{"id"}).AddRow(1))
		time.Sleep(time.Second)
		defer gomonkey.ApplyMethodReturn(qizhi_uaudithost.NewQiZhiUAuditHost(), "GetToken", nil, nil).Reset()
		time.Sleep(time.Second)
		defer gomonkey.ApplyMethodReturn(data_source.NewNodeModel(), "Updates", nil).Reset()
		time.Sleep(time.Second)
		defer gomonkey.ApplyMethodReturn(data_source.NewNodeConfigModel(), "Update", nil).Reset()

		param, _ := json.Marshal(CustomAccountAndPwdUpdateRequest{
			Id: 61,
			CustomAccountAndPwdNodeRequest: CustomAccountAndPwdNodeRequest{
				Name:       "齐治堡垒机",
				AreaId:     1,
				AreaType:   "指定",
				SourceId:   11,
				Account:    "sdlfkajldfas",
				Password:   "dflkajflajldf",
				RepeatType: "day",
				Date:       "",
				StartTime:  "00:00",
				IP:         "***********",
				Protocol:   "https",
			},
		})
		w := httptest.NewRecorder()
		req := httptest.NewRequest("PUT", "/api/v1/data/node/qizhi/uaudithost", strings.NewReader(string(param)))
		req.Header.Set("Content-Type", "application/json")

		// Create a Gin context
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		// Call the function under test
		err := UpdateQiZhiUAuditHost(c)
		assert.Nil(t, err)
	})
}

func TestQiZhiUAuditHostTest(t *testing.T) {
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		res := map[string]any{}
		w.WriteHeader(http.StatusOK)
		_ = json.NewEncoder(w).Encode(res)
	}))
	defer server.Close()

	param, _ := json.Marshal(CustomAccountAndPwdNodeRequest{
		Name:       "qizhi",
		AreaId:     1,
		AreaType:   "指定",
		SourceId:   11,
		Account:    "account",
		Password:   "password",
		RepeatType: "day",
		Date:       "",
		StartTime:  "00:00",
		IP:         server.Listener.Addr().String(),
		Protocol:   "http",
	})

	w := httptest.NewRecorder()
	req := httptest.NewRequest("POST", "/api/v1/data/node/qizhi/uaudithost/test", strings.NewReader(string(param)))
	req.Header.Set("Content-Type", "application/json")

	c, _ := gin.CreateTestContext(w)
	c.Request = req

	err := QiZhiUAuditHostTest(c)
	assert.Nil(t, err)
	assert.Equal(t, w.Body.String(), `{"code":0,"message":"Success","data":{}}`)
}
