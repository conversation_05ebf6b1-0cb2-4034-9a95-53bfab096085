package nodes

import (
	"encoding/json"
	"fobrain/fobrain/app/repository/node"
	reqnode "fobrain/fobrain/app/request/node"
	"fobrain/fobrain/app/services/node/d01"
	"fobrain/fobrain/app/services/node/foeye"
	"fobrain/models/mysql/network_areas"
	"fobrain/pkg/utils"
	"strings"

	"github.com/gin-gonic/gin"

	"fobrain/fobrain/app/services/node/logs"
	"fobrain/fobrain/common/localtime"
	"fobrain/fobrain/common/request"
	"fobrain/fobrain/common/response"
	"fobrain/initialize/mysql"
	"fobrain/models/mysql/data_source"
)

type Process struct {
	Status   bool   `json:"status"`
	Progress int    `json:"progress"`
	Message  string `json:"message"`
}
type (
	ListRequest struct {
		request.PageRequest
		Search    string `json:"keyword" form:"keyword" uri:"keyword" validate:"omitempty" zh:"模糊查询"`
		SourceId  int    `json:"source_id"  form:"source_id" uri:"source_id" validate:"required" zh:"数据源ID"`
		Status    int    `json:"status" form:"status" uri:"status" validate:"omitempty" zh:"状态"`
		UseStatus int    `json:"use_status" form:"use_status" uri:"use_status" validate:"omitempty" zh:"使用状态"`
	}
	HadNodeRequest struct {
		HadNode bool `json:"had_node" form:"had_node" uri:"had_node" validate:"omitempty" zh:"是否接入数据源"`
	}
	NodeData struct {
		data_source.Node
		NodeConfig NodeConfig `json:"node_config"`
	}
	NodeDataRes struct {
		SourceId       uint64         `json:"source_id"`
		AreaId         uint64         `json:"area_id"`
		AreaName       string         `json:"area_name"`
		AreaType       string         `json:"area_type"`
		AreaRules      []*AreaRule    `json:"area_rules"`
		Name           string         `json:"name"`           //  节点名称
		Source         string         `json:"source"`         //  数据源标识
		Types          string         `json:"types"`          //  资源类型
		AllDataTypes   []int          `json:"all_data_types"` // 数据源支持的资源类型
		DataTypes      []int          `json:"data_types"`
		Status         int64          `json:"status"` //  状态：1 init 2 ftp ok 101 ftp no 102 swoole no 3 正常 100 异常
		Use            int64          `json:"use"`    //  使用状态：1 启用 2 禁用
		LastTaskAt     string         `json:"last_task_at"`
		LastTaskStatus int64          `json:"last_task_status"`
		LastSyncResult string         `json:"last_sync_result"`
		Id             uint64         `gorm:"primaryKey;autoIncrement;comment:id" json:"id"`
		CreatedAt      localtime.Time `gorm:"autoCreateTime;comment:创建时间" json:"created_at"`
		UpdatedAt      localtime.Time `gorm:"autoUpdateTime;comment:更新时间" json:"updated_at"`
		NodeConfig     NodeConfig     `json:"node_config"`
	}
	NodeConfig map[string]any
	AreaRule   struct {
		NetworkAreasId   uint64   `json:"network_areas_id"`
		NetworkAreasName string   `json:"network_areas_name"`
		IpRange          []string `json:"ip_range"`
	}
)

func Item(c *gin.Context) error {
	params, err := request.Validate(c, &struct {
		SourceId uint64 `json:"source_id" uri:"source_id" form:"source_id" validate:"omitempty,number" zh:"数据源ID"`
	}{})
	var opts []mysql.HandleFunc
	if params.SourceId > 0 {
		opts = append(opts, mysql.WithWhere("source_id", params.SourceId))
	}
	nodeItem, err := data_source.NewNodeModel().Item(opts...)
	if err != nil {
		return err
	}
	return response.OkWithData(c, nodeItem)
}

func List(c *gin.Context) error {
	params, err := request.Validate(c, &ListRequest{})
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	nodes, total, err := data_source.NewNodeModel().List(params.Page, params.PerPage, params.Search, params.SourceId, params.Status, params.UseStatus)
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	source, err := data_source.NewSourceModel().First(mysql.WithWhere("id", params.SourceId))
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	customFieldConfig := make([]data_source.CustomFieldConfig, 0)
	json.Unmarshal([]byte(source.CustomFieldConfig), &customFieldConfig)
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	// 获取所有区域
	areaList := network_areas.AllNetworkArea()
	var nodeList []map[string]interface{}
	for _, node := range nodes {
		item := &NodeDataRes{}
		item.Id = node.Id
		item.CreatedAt = node.CreatedAt
		item.UpdatedAt = node.UpdatedAt
		item.SourceId = node.SourceId
		item.AreaType = node.AreaType
		item.AreaId = node.AreaId
		item.AreaName = areaList[node.AreaId]
		if node.AreaType == "规则" {
			item.AreaRules = []*AreaRule{}
			if node.AreaRules != "" {
				var rules []*AreaRule
				json.Unmarshal([]byte(node.AreaRules), &rules)
				for _, rule := range rules {
					item.AreaRules = append(item.AreaRules, &AreaRule{
						NetworkAreasId:   rule.NetworkAreasId,
						NetworkAreasName: areaList[rule.NetworkAreasId],
						IpRange:          rule.IpRange,
					})
				}
			}
		}
		item.Name = node.Name
		item.Source = node.Source
		item.Types = node.Types
		item.Status = node.Status
		item.Use = node.Use

		status, endAt, err := data_source.NewNodeModel().AddTimeAndStatus(item.Id)
		if err != nil {
			return err
		}

		if endAt != nil {
			item.LastTaskAt = endAt.Format(localtime.TimeFormat)
		}
		item.LastTaskStatus = status
		config, err := data_source.NewNodeConfigModel().GetNodeConfig(node.Id)
		if err == nil {
			// 如果数据源支持资产类型
			if strings.Contains(node.DataTypes, "1") {
				// 如果person_field不存在，设置默认值
				if _, ok := config["person_field"]; !ok {
					config["person_field"] = "name"
				}
			}
			config["area_type"] = item.AreaType
			config["area_rules"] = item.AreaRules
			item.NodeConfig = config
		}

		// 将类型以 , 分隔的字符串转换为 []int
		item.DataTypes, err = utils.ConvertStringToIntSlice(node.DataTypes)
		if err != nil {
			return err
		}

		// 根据数据源id获取当前具有的资源类型
		item.AllDataTypes, err = ListSourceType(node.SourceId)
		if err != nil {
			return err
		}
		var hardwareInfos map[string]interface{}
		switch params.SourceId {
		case data_source.FoeyeSourceId:
			cli := foeye.NewFoeye()
			err = cli.SetConfig(map[string]any{
				"api_key":  config["api_key"],
				"protocol": config["protocol"], "ip": config["ip"], "version": config["version"],
			})
			if err != nil {
				return err
			}
			hardwareInfos, err = cli.GetSystemsInfo()
			if err != nil {
				item.Status = 100
			}
		case data_source.D01SourceId:
			cli := d01.NewD01()
			err = cli.SetConfig(map[string]any{
				"api_key":  config["api_key"],
				"protocol": config["protocol"], "ip": config["ip"], "version": config["version"],
			})
			if err != nil {
				return err
			}
			hardwareInfos, err = cli.GetSystemsInfo()
			if err != nil {
				item.Status = 100
			}
		}
		jsonItem, _ := utils.StructToMap(item, "json")
		jsonItem["hardware_usage"] = hardwareInfos
		jsonItem["custom_field_config"] = customFieldConfig

		nodeList = append(nodeList, jsonItem)
	}
	return response.OkWithPageData(c, total, params.Page, params.PerPage, nodeList)
}

func GetNode(c *gin.Context) error {
	params, err := request.Validate(c, &struct {
		Id uint64 `json:"id" uri:"id" form:"id" validate:"required,number" zh:"节点ID"`
	}{})
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	nodeInfo, err := data_source.NewNodeModel().First(mysql.WithWhere("id", params.Id))
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	return response.OkWithData(c, nodeInfo)
}
func DeleteNode(c *gin.Context) error {
	params, err := request.Validate(c, &struct {
		Id uint64 `json:"id" uri:"id" form:"id" validate:"required,number" zh:"节点ID"`
	}{})
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	err = data_source.NewNodeModel().Delete([]uint64{params.Id})
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}

	// 日志-删除节点
	err = logs.LogCreate(params.Id, "", logs.Delete{
		IP:             "",
		AwardCode:      "",
		DataSyncConfig: "",
	}, logs.NodeLogDelete)

	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	//TODO 清除节点数据
	return response.Ok(c)
}

// ClearNode
// @router api/data/clear/nodes [post]
func ClearNode(c *gin.Context) error {
	params, err := request.Validate(c, &struct {
		Id uint64 `json:"id" uri:"id" form:"id" validate:"required,number" zh:"节点ID"`
	}{})
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}

	err = node.ClearNodeData(params.Id)
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}

	//TODO 清除节点数据
	//fmt.Println(nodeConfig.Value)

	return response.Ok(c)
}

func CustomDataSourceNodeAdd(c *gin.Context) error {
	params, err := request.Validate(c, &reqnode.CustomDataSourceNodeAddRequest{})
	if err != nil {
		return err
	}

	userId := request.GetUserId(c)
	err = node.CustomDataSourceNodeAdd(params, userId)
	if err != nil {
		return err
	}

	return response.Ok(c)
}

func CustomDataSourceNodeUpdate(ctx *gin.Context) error {
	params, err := request.Validate(ctx, &reqnode.CustomDataSourceNodeUpdateRequest{})
	if err != nil {
		return err
	}

	err = node.CustomDataSourceNodeUpdate(params)
	if err != nil {
		return err
	}

	return response.Ok(ctx)
}

// ListSourceType 根据数据源id获取它节点对应的数据同步类型
func ListSourceType(sourceId uint64) ([]int, error) {
	source := &data_source.Source{}
	if err := data_source.NewSourceModel().Where("id = ?", sourceId).First(source).Error; err != nil {
		return nil, err
	}

	// 根据数据源信息获取对应的同步类型
	types := make([]int, 0)
	if source.HasAssetData {
		types = append(types, 1)
	}
	if source.HasVulData {
		types = append(types, 2)
	}
	if source.HasPersonnelData {
		types = append(types, 3)
	}
	return types, nil
}
