package nodes

import (
	"fobrain/fobrain/common/response"

	"github.com/gin-gonic/gin"
)

func GetRizhiyiField(c *gin.Context) error {
	type ListFields struct {
		Name string `json:"name"`
		Key  string `json:"all_key"`
	}
	result := make([]ListFields, 0)
	result = append(result,
		ListFields{
			Name: "姓名",
			Key:  "operation_and_maintenance_angle_a",
		}, ListFields{
			Name: "邮箱",
			Key:  "email",
		}, ListFields{
			Name: "department",
			Key:  "business_team",
		})
	return response.OkWithData(c, result)
}
