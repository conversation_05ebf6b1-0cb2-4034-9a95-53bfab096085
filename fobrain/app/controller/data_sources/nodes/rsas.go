package nodes

import (
	"fobrain/fobrain/app/repository/proactive_task/source_task"
	"fobrain/fobrain/common/request"
	"fobrain/fobrain/common/response"
	"fobrain/models/elastic/poc"
	"fobrain/models/elastic/source/nsfocus_rsas"
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/task"

	"git.gobies.org/caasm/fobrain-components/dataSourceSdk/nsfocus/rsas"
	"github.com/gin-gonic/gin"
)

type SyncVulnTemplatesRequest struct {
	NodeId uint64 `json:"node_id" form:"node_id" uri:"node_id" validate:"required" zh:"节点ID"`
}

//  获取系统漏洞模板列表
func SyncVulnTemplates(c *gin.Context) error {
	op := &source_task.Common{
		Base:                &source_task.Base{Task: &task.ProactiveTasks{SourceId: data_source.NSFocusRsasSourceId}},
		Sdk:                 rsas.NewRSAS(),
		IndexTaskThreats:    nsfocus_rsas.NewNsfocusRsasTaskThreatsModel().IndexName(),
		IndexProcessThreats: poc.NewProcessPocModel().IndexName(),
	}
	params, err := request.Validate(c, &SyncVulnTemplatesRequest{})
	if err != nil {
		return err
	}
	config, err := data_source.NewNodeConfigModel().GetNodeConfig(params.NodeId)
	if err != nil {
		return err
	}

	op.NodeConfig = config

	ret, err := rsas.NewRSAS().GetSysVulnTemplateList(op)
	if err != nil {
		return err
	}
	return response.OkWithData(c, ret)
}
