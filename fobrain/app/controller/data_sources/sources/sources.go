package sources

import (
	"fmt"

	"github.com/gin-gonic/gin"

	repositorysource "fobrain/fobrain/app/repository/source"
	reqsource "fobrain/fobrain/app/request/source"
	"fobrain/fobrain/common/request"
	"fobrain/fobrain/common/response"
	"fobrain/models/mysql/data_source"
	"fobrain/pkg/utils"
)

type (
	ListRequest struct {
		request.PageRequest
		Search     string `json:"keyword" form:"keyword" uri:"keyword" validate:"omitempty" zh:"模糊查询"`
		SourceType []int  `json:"source_type" form:"source_type" uri:"source_type" validate:"omitempty" zh:"数据源类型"`
		HadNode    int    `json:"had_node" form:"had_node" uri:"had_node" validate:"omitempty" zh:"是否存在异常节点"`
		HasData    int    `json:"has_data" form:"has_data" uri:"has_data" validate:"omitempty" zh:"上报字段"`
		HasSync    int    `json:"has_sync" form:"has_sync" uri:"has_sync" validate:"omitempty" zh:"节点类型"`
		IsNode     int    `json:"is_node" form:"is_node" uri:"is_node" validate:"omitempty" zh:"是否接入数据源 1是2否"`
	}
	HadNodeRequest struct {
		HadNode bool `json:"had_node" form:"had_node" uri:"had_node" validate:"omitempty" zh:"是否接入数据源"`
	}

	SourceItem struct {
		data_source.SourceListItem
		Children []data_source.SourceNodeItem `json:"children" zh:"节点"`
	}
)

func Item(c *gin.Context) error {
	sources, err := data_source.NewSourceModel().Item()
	if err != nil {
		return err
	}
	return response.OkWithData(c, sources)
}

// List
// @router api/data/source [GET]
func List(c *gin.Context) error {
	params, err := request.Validate(c, &ListRequest{})
	if err != nil {
		return err
	}
	sources, total, err := data_source.NewSourceModel().List(params.Page, params.PerPage, params.Search, params.SourceType, params.HadNode, params.HasSync, params.HasData, params.IsNode)
	if err != nil {
		return err
	}
	return response.OkWithPageData(c, total, params.Page, params.PerPage, sources)
}
func HadNode(c *gin.Context) error {
	total, err := data_source.NewNodeModel().Total()
	if err != nil {
		return err
	}
	return response.OkWithData(c, gin.H{"total": total, "had_node": true})
}
func SourceTypes(c *gin.Context) error {
	list := HadNodeRequest{
		HadNode: false,
	}
	params, err := request.Validate(c, &list)
	if err != nil {
		return err
	}
	sourceTypes, err := data_source.NewSourceTypeModel().Item(params.HadNode)
	if err != nil {
		return err
	}
	return response.OkWithData(c, sourceTypes)
}
func NodeList(c *gin.Context) error {
	sources, err := data_source.NewSourceModel().ItemByTask()
	if err != nil {
		return err
	}
	var SourceItems []SourceItem
	for _, source := range sources {
		source.Md5 = utils.Get32MD5Encode(fmt.Sprintf("source_%d", source.Id))
		item := SourceItem{SourceListItem: source}
		nodes, err := data_source.NewNodeModel().ItemByTask(source.Id)
		if err == nil {
			for _, node := range nodes {
				node.Md5 = utils.Get32MD5Encode(fmt.Sprintf("node_%d", node.Id))
				item.Children = append(item.Children, node)
			}
		}
		SourceItems = append(SourceItems, item)
	}
	return response.OkWithData(c, SourceItems)
}

func Add(c *gin.Context) error {
	params, err := request.Validate(c, &reqsource.AddRequest{})
	if err != nil {
		return err
	}

	err = repositorysource.Add(params)
	if err != nil {
		return err
	}

	return response.Ok(c)
}
