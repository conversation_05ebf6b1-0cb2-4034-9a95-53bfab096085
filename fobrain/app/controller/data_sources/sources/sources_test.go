package sources

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"

	"fobrain/fobrain/app/request/source"
	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/models/mysql/data_source"
)

func TestItem(t *testing.T) {
	mockDb := testcommon.InitSqlMock()
	defer mockDb.Close()
	mockDb.ExpectQuery("SELECT * FROM `data_sources`").
		WillReturnRows(mockDb.NewRows([]string{"id", "name"}).AddRow(1, "name"))

	// 创建一个测试请求和响应
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/data/sources", nil)
	// 创建一个Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 调用函数
	err := Item(c)
	fmt.Println(w.Body.String())
	// 断言错误为 nil
	assert.Empty(t, err)
	assert.NotEmpty(t, w.Body.String())
}

func TestList(t *testing.T) {
	mockDb := testcommon.InitSqlMock()
	defer mockDb.Close()
	mockDb.ExpectQuery("SELECT count(*) FROM `data_sources` LEFT JOIN data_nodes as node ON data_sources.id = node.source_id and node.deleted_at is null LEFT JOIN data_source_type_map as map ON map.source_id = data_sources.id LEFT JOIN data_source_types as type ON type.id = map.source_type_id WHERE data_sources.show = 1 GROUP BY `data_sources`.`id` ORDER BY data_sources.sort asc").
		WillReturnRows(mockDb.NewRows([]string{"count"}).AddRow(1))

	// 创建一个测试请求和响应
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/data/source?page=1&per_page=10", nil)
	// 创建一个Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 调用函数
	err := List(c)

	// 断言错误为 nil
	assert.Empty(t, err)
	assert.Equal(t, `{"code":0,"message":"Success","data":{"total":1,"page":1,"per_page":10,"items":null}}`, w.Body.String())
}

func TestHadNode(t *testing.T) {
	mockDb := testcommon.InitSqlMock()
	defer mockDb.Close()
	mockDb.ExpectQuery("SELECT count(*) FROM `data_nodes` WHERE deleted_at IS NULL").
		WillReturnRows(mockDb.NewRows([]string{"count"}).AddRow(1))

	// 创建一个测试请求和响应
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/data/source_types?had_node=true", nil)
	// 创建一个Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 调用函数
	err := HadNode(c)

	// 断言错误为 nil
	assert.Empty(t, err)
	assert.Equal(t, `{"code":0,"message":"Success","data":{"had_node":true,"total":1}}`, w.Body.String())
}

func TestSourceTypes(t *testing.T) {
	mockDb := testcommon.InitSqlMock()
	defer mockDb.Close()
	mockDb.ExpectQuery("SELECT data_source_types.*, COUNT(DISTINCT source.id) AS num FROM `data_source_types` LEFT JOIN data_source_type_map as map ON data_source_types.id = map.source_type_id LEFT JOIN data_sources as source ON map.source_id = source.id WHERE  1  GROUP BY `data_source_types`.`id`").
		WillReturnRows(mockDb.NewRows([]string{"id", "name", "num"}).AddRow(1, "name", 1))

	// 创建一个测试请求和响应
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/data/source_types?page=1&per_page=10", nil)
	// 创建一个Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 调用函数
	err := SourceTypes(c)

	// 断言错误为 nil
	assert.Empty(t, err)
	assert.Equal(t, `{"code":0,"message":"Success","data":[{"id":1,"name":"name","is_checked":false,"num":1}]}`, w.Body.String())
}

func TestNodeList(t *testing.T) {
	mockDb := testcommon.InitSqlMock()
	defer mockDb.Close()
	mockDb.ExpectQuery("SELECT data_sources.id,data_sources.icon,data_sources.name,'source' as type,COUNT(DISTINCT task.id) AS num FROM `data_sources` LEFT JOIN data_sync_tasks as task ON data_sources.id = task.source_id GROUP BY `data_sources`.`id` HAVING COUNT(DISTINCT task.id) >0").
		WillReturnRows(mockDb.NewRows([]string{"id", "name", "num"}).AddRow(1, "name", 1))

	// 创建一个测试请求和响应
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/data/nodes?page=1&per_page=10", nil)
	// 创建一个Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 调用函数
	err := NodeList(c)

	// 断言错误为 nil
	assert.Empty(t, err)
	//assert.Equal(t, `{"code":0,"message":"Success","data":[{"id":1,"name":"name","type":"","md5":"928AE334EBCD1FFA2FC5E5CDB14847F6","num":1,"children":null}]}`, w.Body.String())
}

func TestAddParamsError(t *testing.T) {
	jsonData := `{"code": "111"}`

	// 创建一个测试请求和响应
	w := httptest.NewRecorder()
	req := httptest.NewRequest("POST", "/api/v1/data/source", bytes.NewBufferString(jsonData))
	// 创建一个Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 调用函数
	err := Add(c)

	// 断言错误为 nil
	assert.NotNil(t, err)
	assert.Equal(t, `数据源名称为必填字段`, err.Error())
}

func TestAddNameError(t *testing.T) {
	mockDb := testcommon.InitSqlMock()
	defer mockDb.Close()
	mockDb.ExpectQuery("SELECT count(*) FROM `data_sources` WHERE name = ?").
		WillReturnRows(mockDb.NewRows([]string{"count"}).AddRow(1))

	val, _ := json.Marshal(source.AddRequest{
		Name:             "foeye",
		EnName:           "en_foeye",
		Icon:             "/storage/app/public/icon/foeye.png",
		Version:          "v1.0.1",
		HasAssetData:     true,
		HasVulData:       true,
		HasPersonnelData: true,
		IsTaskSync:       true,
		IsCronSync:       true,
		DataSourceTypes:  []int{1, 2},
	})

	// 创建一个测试请求和响应
	w := httptest.NewRecorder()
	req := httptest.NewRequest("POST", "/api/v1/auth/login", strings.NewReader(string(val)))
	req.Header.Set("Content-Type", "application/json")

	// 创建一个Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 调用函数
	err := Add(c)
	// 断言错误为 nil
	assert.NotNil(t, err)
	assert.Equal(t, `数据源名称重复`, err.Error())
}

func TestAddPass(t *testing.T) {

	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyMethodReturn(data_source.NewSourceModel(), "Count", int64(0), nil).Reset()
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyMethodReturn(data_source.NewSourceModel(), "Create", nil).Reset()
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyMethodReturn(data_source.NewSourceTypeMapModel(), "Create", nil).Reset()

	val, _ := json.Marshal(source.AddRequest{
		Name:             "foeye",
		EnName:           "en_foeye",
		Icon:             "/storage/app/public/icon/foeye.png",
		Version:          "v1.0.1",
		HasAssetData:     true,
		HasVulData:       true,
		HasPersonnelData: true,
		IsTaskSync:       true,
		IsCronSync:       true,
		DataSourceTypes:  []int{1, 2},
	})

	// 创建一个测试请求和响应
	w := httptest.NewRecorder()
	req := httptest.NewRequest("POST", "/api/v1/auth/login", strings.NewReader(string(val)))
	req.Header.Set("Content-Type", "application/json")

	// 创建一个Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 调用函数
	err := Add(c)

	// 断言错误为 nil
	assert.Nil(t, err)
	assert.Equal(t, `{"code":0,"message":"Success","data":{}}`, w.Body.String())
}
