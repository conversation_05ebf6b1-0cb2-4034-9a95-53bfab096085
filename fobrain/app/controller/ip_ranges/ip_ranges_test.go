package ip_ranges

import (
	"bytes"
	"io/ioutil"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"

	"fobrain/initialize/mysql"
	ipRangesSql "fobrain/models/mysql/ip_ranges"
)

func TestUpdate(t *testing.T) {
	str := `{
			"id": 4,
			"ip_range_type": 1,
			"ip_range_status": 1,
			"address_remark": "aaaaa"
		}`
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyMethodReturn(ipRangesSql.NewIpRangesModel(), "Update", nil).Reset()
	// 创建一个测试请求和响应
	w := httptest.NewRecorder()
	req := httptest.NewRequest("PUT", "/api/v1/ip_ranges", bytes.NewBufferString(str))
	req.Header.Set("Content-Type", "application/json")
	// 创建一个Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 调用函数
	err := Update(c)
	assert.NoError(t, err)

	// 解析响应数据
	response := w.Result()
	defer response.Body.Close()
	body, _ := ioutil.ReadAll(response.Body)
	assert.NotEmpty(t, body)
}

func TestFirst(t *testing.T) {

	info := ipRangesSql.IpRanges{
		NetworkAreasId: 1,
		IpRange:        "127.0.0.1/24",
		IpNumber:       1,
		IpRangeStatus:  1,
	}
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyMethodReturn(ipRangesSql.NewIpRangesModel(), "First", info, nil).Reset()
	// 创建一个测试请求和响应
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/ip_ranges?id=1", nil)
	req.Header.Set("Content-Type", "application/json")
	// 创建一个Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 调用函数
	err := First(c)
	assert.NoError(t, err)

	// 解析响应数据
	response := w.Result()
	defer response.Body.Close()
	body, _ := ioutil.ReadAll(response.Body)
	assert.NotEmpty(t, body)
}

func TestDeleteByIds(t *testing.T) {
	str := `{
		"ids":[]
	}`

	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyMethodReturn(ipRangesSql.NewIpRangesModel(), "DeleteByIds", nil).Reset()
	// 创建一个测试请求和响应
	w := httptest.NewRecorder()
	req := httptest.NewRequest("DELETE", "/api/v1/ip_ranges", strings.NewReader(str))
	req.Header.Set("Content-Type", "application/json")
	// 创建一个Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 调用函数
	err := DeleteByIds(c)
	assert.NoError(t, err)

	// 解析响应数据
	response := w.Result()
	defer response.Body.Close()
	body, _ := ioutil.ReadAll(response.Body)
	assert.NotEmpty(t, body)
}

func TestList(t *testing.T) {
	list := []ipRangesSql.IpRangesList{
		{
			IpRanges: ipRangesSql.IpRanges{
				BaseModel: mysql.BaseModel{
					Id: 1,
				},
				NetworkAreasId: 1,
				IpRange:        "127.0.0.1/24",
				IpNumber:       1,
				IpRangeStatus:  1,
				AddressRemark:  "",
			},
			NetworkAreasName: "AAA",
		},
	}
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyMethodReturn(ipRangesSql.NewIpRangesModel(), "IpRangesList", list, int64(1), nil).Reset()

	// 创建一个测试请求和响应
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/ip_ranges?page=1&per_page=10", nil)
	req.Header.Set("Content-Type", "application/json")
	// 创建一个Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 调用函数
	err := List(c)
	assert.NoError(t, err)

	// 解析响应数据
	response := w.Result()
	defer response.Body.Close()
	body, _ := ioutil.ReadAll(response.Body)
	assert.NotEmpty(t, body)
}

func TestExport(t *testing.T) {
	list := []ipRangesSql.IpRangesList{
		{
			IpRanges: ipRangesSql.IpRanges{
				BaseModel: mysql.BaseModel{
					Id: 1,
				},
				NetworkAreasId: 1,
				IpRange:        "127.0.0.1/24",
				IpNumber:       1,
				IpRangeStatus:  1,
				AddressRemark:  "",
			},
			NetworkAreasName: "AAA",
		},
	}
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyMethodReturn(ipRangesSql.NewIpRangesModel(), "IpRangesList", list, int64(1), nil).Reset()

	// 创建一个测试请求和响应
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/ip_ranges/export?keyword=111", nil)
	req.Header.Set("Content-Type", "application/json")
	// 创建一个Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 调用函数
	err := Export(c)
	assert.NoError(t, err)

	// 解析响应数据
	response := w.Result()
	defer response.Body.Close()
	body, _ := ioutil.ReadAll(response.Body)
	assert.NotEmpty(t, body)
}
