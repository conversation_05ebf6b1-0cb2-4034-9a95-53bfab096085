package ip_ranges

import (
	"mime/multipart"

	"github.com/gin-gonic/gin"
	"github.com/xuri/excelize/v2"

	"fobrain/fobrain/app/crontab"
	ipRangesRep "fobrain/fobrain/app/repository/ip_ranges"
	ipRangesReq "fobrain/fobrain/app/request/ip_ranges"
	"fobrain/fobrain/common/request"
	"fobrain/fobrain/common/response"
	"fobrain/initialize/mysql"
	"fobrain/models/mysql/ip_ranges"
)

// Create Ip地址段创建
func Create(c *gin.Context) error {
	params, err := request.Validate(c, &ipRangesReq.IpRangesCreate{})
	if err != nil {
		return err
	}
	err = ipRangesRep.Create(params)
	if err != nil {
		return err
	}

	// 触发IP段探针映射缓存更新
	crontab.TriggerIPRangeProbeMappingRefresh("IP段新增")

	return response.OkWithMessage(c, "添加成功")
}

// Update Ip地址段更新
func Update(c *gin.Context) error {
	params, err := request.Validate(c, &ipRangesReq.IpRangesUpdate{})
	if err != nil {
		return err
	}
	err = ipRangesRep.Update(params)
	if err != nil {
		return err
	}

	// 触发IP段探针映射缓存更新
	crontab.TriggerIPRangeProbeMappingRefresh("IP段更新")

	return response.OkWithMessage(c, "更新成功")
}

// First 查询单个ip段数据
func First(c *gin.Context) error {
	params, err := request.Validate(c, &ipRangesReq.FirstRequest{})
	if err != nil {
		return err
	}
	handlers := make([]mysql.HandleFunc, 0)
	handlers = append(handlers, mysql.WithWhere("id = ?", params.Id))
	info, err := ip_ranges.NewIpRangesModel().First(handlers...)
	if err != nil {
		return err
	}
	return response.OkWithData(c, info)
}

// DeleteByIds 删除ip段数据
func DeleteByIds(c *gin.Context) error {
	params, err := request.Validate(c, &ipRangesReq.DeleteRequest{})
	if err != nil {
		return err
	}
	err = ip_ranges.NewIpRangesModel().DeleteByIds(params.Keyword, params.Ids)
	if err != nil {
		return err
	}

	// 触发IP段探针映射缓存更新
	crontab.TriggerIPRangeProbeMappingRefresh("IP段删除")

	return response.OkWithMessage(c, "删除成功")
}

// List 查询ip段列表
func List(c *gin.Context) error {
	params, err := request.Validate(c, &ipRangesReq.IpRangesRequest{})
	if err != nil {
		return err
	}
	list, total, err := ipRangesRep.List(params)
	if err != nil {
		return err
	}
	return response.OkWithPageData(c, total, params.Page, params.PerPage, list)
}

// MergeUpdate 手动合并更新
func MergeUpdate(c *gin.Context) error {
	err := ipRangesRep.ManualUpdate(c)
	if err != nil {
		return err
	}
	return response.OkWithMessage(c, "操作成功")
}

// Export 导出ip段
func Export(c *gin.Context) error {
	params, err := request.Validate(c, &ipRangesReq.ExportRequest{})
	if err != nil {
		return err
	}
	filePath, err := ipRangesRep.Export(params)
	if err != nil {
		return err
	}
	return response.OkWithFile(c, filePath, true)
}

// IpRangesImport 导入ip段
func IpRangesImport(c *gin.Context) error {
	file, _, err := c.Request.FormFile("file")
	if err != nil {
		return err
	}

	defer func(file multipart.File) {
		err := file.Close()
		if err != nil {
			err := response.FailWithCodeMessage(c, 500, "关闭文件失败")
			if err != nil {
				return
			}
			return
		}
	}(file)

	f, err := excelize.OpenReader(file)
	if err != nil {
		return err
	}

	//读取Sheet1的数据
	rows, err := f.GetRows("Sheet1", excelize.Options{RawCellValue: true})
	if err != nil {
		return err
	}

	if (len(rows) - 4) > 1000 {
		return response.FailWithCodeMessage(c, 400, "单次导入IP段数量最大1000个")
	}

	errorStr, err := ipRangesRep.IpRangesImport(rows)
	if err != nil {
		return err
	}

	err = ipRangesRep.ManualUpdate(c)
	if err != nil {
		return err
	}

	// 触发IP段探针映射缓存更新
	crontab.TriggerIPRangeProbeMappingRefresh("IP段批量导入")

	return response.OkWithData(c, errorStr)
}
