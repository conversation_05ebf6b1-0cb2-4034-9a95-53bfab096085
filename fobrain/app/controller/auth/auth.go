package auth

import (
	"crypto/md5"
	"encoding/json"
	"errors"
	"fmt"
	"fobrain/fobrain/app/services/backup"
	"fobrain/fobrain/app/services/permission"
	"fobrain/pkg/utils"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"

	"fobrain/fobrain/app/repository/user_access"
	jwtAuth "fobrain/fobrain/common/auth"
	"fobrain/fobrain/common/captcha"
	"fobrain/fobrain/common/request"
	"fobrain/fobrain/common/response"
	"fobrain/initialize/mysql"
	pb "fobrain/mergeService/proto"
	"fobrain/models/mysql/system_configs"
	"fobrain/models/mysql/token"
	"fobrain/models/mysql/user"
	"fobrain/pkg/oauth"
)

type (
	LoginRequest struct {
		Account     string `json:"account" form:"account" uri:"account" validate:"required" zh:"账号"`
		Password    string `json:"password" from:"password" uri:"password" validate:"required" zh:"密码"`
		CaptchaId   string `json:"captcha_id" from:"captcha_id" uri:"captcha_id" validate:"required" zh:"验证码序号"`
		CaptchaCode string `json:"captcha_code" from:"captcha_code" uri:"captcha_code" validate:"required" zh:"验证码"`
	}

	ChangePwdRequest struct {
		OldPassword string `json:"old_password" from:"old_password" uri:"old_password" validate:"required" zh:"旧密码"`
		NewPassword string `json:"new_password" from:"new_password" uri:"new_password" validate:"required" zh:"新密码"`
		CaptchaId   string `json:"captcha_id" from:"captcha_id" uri:"captcha_id" validate:"required" zh:"验证码序号"`
		CaptchaCode string `json:"captcha_code" from:"captcha_code" uri:"captcha_code" validate:"required" zh:"验证码"`
	}

	GenApiTokenRequest struct {
		Ref bool `json:"ref" form:"ref" uri:"ref" validate:"omitempty,boolean" zh:"刷新Token"`
	}
)

var LoginValidate = request.Validate[LoginRequest]
var GenApiTokenValidate = request.Validate[GenApiTokenRequest]
var VersionStr string
var BuildTime string

func Captcha(c *gin.Context) error {
	id, bs64, err := captcha.Generate()
	if err != nil {
		return err
	}
	return response.OkWithData(c, gin.H{"id": id, "image": bs64})
}

func Login(c *gin.Context) error {
	params, err := LoginValidate(c, &LoginRequest{})
	if err != nil {
		return err
	}
	if !captcha.Verify(params.CaptchaId, params.CaptchaCode) {
		return errors.New("验证码错误")
	}
	userInfo, err := user.NewUserModel().First(mysql.WithWhere("account", params.Account))
	if err != nil {
		return errors.New("用户名信息错误")
	}
	locked := false
	locked, err = userInfo.IsAccountLocked()
	if err != nil {
		return errors.New("登录失败")
	}
	if locked {
		return errors.New(fmt.Sprintf("账号被锁定，请在%s后重试", userInfo.LockedUntil.String()))
	}
	if userInfo.Status == 2 {
		// 记录登录失败的操作
		err = userInfo.RecordLoginFailure()
		if err != nil {
			return errors.New("登录失败")
		}
		return errors.New("账号未启用")
	}
	if userInfo.Password != fmt.Sprintf("%x", md5.Sum([]byte(params.Password))) {
		// 记录登录失败的操作
		err = userInfo.RecordLoginFailure()
		if err != nil {
			return errors.New("登录失败")
		}
		return errors.New("密码错误")
	}
	// 登录成功后重置失败次数和锁定时间
	err = userInfo.ResetLoginFailures()
	if err != nil {
		return errors.New("登录失败")
	}
	// 判断密码有效期
	expired := false
	expired, err = userInfo.IsPasswordExpired()
	if err != nil {
		return errors.New("检查密码是否过期失败")
	}
	// 生成token
	tokenStr, exp, err := jwtAuth.GenerateToken(userInfo)
	if err != nil {
		return errors.New("Token生成失败:" + err.Error())
	}

	// 获取备份提醒状态
	backupReminder := backup.GetReminderStatus(userInfo.Id)

	return response.OkWithData(c, gin.H{
		"token":           tokenStr,
		"expire_at":       exp.Format("2006-01-02 15:04:05"),
		"pwd_expired":     expired,
		"is_first_login":  userInfo.PasswordUpdatedAt == nil,
		"backup_reminder": backupReminder,
	})
}

func ChangePwd(c *gin.Context) error {
	params, err := request.Validate(c, &ChangePwdRequest{})
	if err != nil {
		return err
	}
	if !captcha.Verify(params.CaptchaId, params.CaptchaCode) {
		return errors.New("验证码错误")
	}
	userInfo := request.GetUserInfo(c)
	if userInfo == nil {
		return errors.New("获取用户信息失败")
	}
	// base64解码旧密码
	oldPwd, err := utils.Base64Decode(params.OldPassword)
	if err != nil {
		return errors.New("旧密码解码失败")
	}
	// 验证旧密码
	if userInfo.EncryptedPassword(oldPwd) != userInfo.Password {
		return errors.New("旧密码错误")
	}
	// base64解码新密码
	newPwd, err := utils.Base64Decode(params.NewPassword)
	if err != nil {
		return errors.New("新密码解码失败")
	}
	// 验证新密码格式
	if !user_access.CheckPassword(newPwd) {
		return errors.New("密码必须包含大小写字母、数字、特殊符号，长度在8到20位！")
	}
	// 修改密码
	encryptedPwd := userInfo.EncryptedPassword(newPwd)
	if encryptedPwd == userInfo.Password {
		return errors.New("新密码不能和旧密码相同")
	}
	err = userInfo.ChangePwd(userInfo.Id, encryptedPwd)
	if err != nil {
		return errors.New("修改密码失败")
	}
	// 删除token
	err = token.NewTokenModel().Del(mysql.WithWhere("user_id", userInfo.Id), mysql.WithWhere("type", token.TokenTypeNormal))
	if err != nil {
		return errors.New("退出登录失败")
	}
	return response.Ok(c)
}

func FourALogin(c *gin.Context) error {
	enable, _ := system_configs.NewSystemConfigs().GetConfig("sso_enable")
	if enable != "true" {
		fmt.Println("!!!!!!4a没启用")
		c.Redirect(http.StatusTemporaryRedirect, "/")
		return nil
	}
	loginUrl, _ := system_configs.NewSystemConfigs().GetConfig("login_info_url")
	if loginUrl == "" {
		fmt.Println("!!!!!!4a login_info_url没配置")
		c.Redirect(http.StatusTemporaryRedirect, "/")
		return nil
	}
	tenant, _ := system_configs.NewSystemConfigs().GetConfig("tenant")
	if tenant == "" {
		fmt.Println("!!!!!!4a tenant没配置")
		c.Redirect(http.StatusTemporaryRedirect, "/")
		return nil
	}

	// 获取票据参数
	ticket := c.DefaultQuery("iamcaspticket", "")
	if ticket == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "票据 必填"})
		return nil
	}
	info, err := oauth.Handle4ASSOUserInfo(ticket)
	if err != nil {
		return err
	}

	expireTime := time.Now().Add(60 * 24 * time.Hour) // 当前时间加上 60 天
	c.SetCookie("token", info["token"].(string), int(expireTime.Unix()), "/", "", false, false)
	c.Redirect(http.StatusTemporaryRedirect, "/")
	return nil
}

func Oauth2Login(c *gin.Context) error {
	if system_configs.SsoEnable() {
		redirectUri := c.Query("redirect_uri")
		protocol := "http" // 默认是 http
		if c.Request.Header.Get("X-Forwarded-Proto") == "https" {
			protocol = "https"
		} else if c.Request.TLS != nil {
			protocol = "https" // 确认请求是否通过 TLS（即 https）
		}
		redirectUri = protocol + "://" + c.Request.Host + "/" + redirectUri
		c.Redirect(http.StatusTemporaryRedirect, oauth.SSORedirectTo(redirectUri, system_configs.SSOConfigs()))
		c.Abort()
	}

	c.Redirect(http.StatusTemporaryRedirect, "/")
	return nil
}

func LoginOut(c *gin.Context) error {
	userInfo := request.GetUserInfo(c)
	if err := token.NewTokenModel().Del(mysql.WithWhere("user_id", userInfo.Id), mysql.WithWhere("type", token.TokenTypeNormal)); err != nil {
		return err
	}
	return response.Ok(c)
}

func GenApiToken(c *gin.Context) error {
	userInfo := request.GetUserInfo(c)
	if userInfo == nil {
		return errors.New("获取用户信息失败")
	}
	params, err := GenApiTokenValidate(c, &GenApiTokenRequest{})
	if err != nil {
		return err
	}
	tokenStr, exp, err := jwtAuth.GenerateApiToken(userInfo, params.Ref)
	if err != nil {
		return errors.New("Token生成失败:" + err.Error())
	}
	return response.OkWithData(c, gin.H{"token": tokenStr, "expire_at": exp.String()})
}

// IgnoreBackupReminder 忽略备份提醒
func IgnoreBackupReminder(c *gin.Context) error {
	userInfo := request.GetUserInfo(c)
	if userInfo == nil {
		return errors.New("获取用户信息失败")
	}

	if err := backup.IgnorePopupReminder(userInfo.Id); err != nil {
		return errors.New("操作失败: " + err.Error())
	}

	return response.OkWithMessage(c, "操作成功")
}

func UserInfo(c *gin.Context) error {
	u := request.GetUserInfo(c)
	if u == nil {
		return errors.New("user not found")
	}
	userInfo, err := utils.StructToMap(u, "json")
	if err != nil {
		return fmt.Errorf("failed to convert user info to map: %v", err)
	}

	ruleInfo := map[string]interface{}{}
	if u.RuleInfo == "" {
		return errors.New("RuleInfo is empty or invalid")
	}
	err = json.Unmarshal([]byte(u.RuleInfo), &ruleInfo)
	if err != nil {
		return fmt.Errorf("failed to unmarshal RuleInfo: %v", err)
	}

	userInfo["rule_info"] = ruleInfo
	menus, err := permission.NewPermissionService().GetUserMenus(c, u.Id)
	if err != nil {
		return response.FailWithMessage(c, "failed to query user menus:"+err.Error())
	}
	userInfo["homePath"] = menus.Menus[0].Path
	userInfo["message"] = c.GetString("message")
	return response.OkWithData(c, userInfo)
}

func Hi(c *gin.Context) error {
	param := &pb.TestMethodRequest{
		Name: c.Query("name"),
	}
	if err := c.ShouldBindQuery(param); err != nil {
		return response.FailWithMessage(c, "参数错误")
	}
	rsp, err := pb.GetProtoClient().TestMethod(c, param, pb.ClientWithAddress)
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}

	return response.OkWithData(c, rsp)
}

func Version(c *gin.Context) error {
	return response.OkWithData(c, gin.H{"version": VersionStr, "build_time": BuildTime})
}
