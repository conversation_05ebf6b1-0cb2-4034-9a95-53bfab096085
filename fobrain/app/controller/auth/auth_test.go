package auth

import (
	"bytes"
	"errors"
	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/initialize/mysql"
	"fobrain/models/mysql/token"
	"io/ioutil"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"

	. "github.com/agiledragon/gomonkey/v2"
	"github.com/gin-gonic/gin"
	. "github.com/smartystreets/goconvey/convey"
	"github.com/stretchr/testify/suite"

	jwtAuth "fobrain/fobrain/common/auth"
	"fobrain/fobrain/common/captcha"
	"fobrain/fobrain/common/localtime"
	"fobrain/fobrain/common/request"
	"fobrain/fobrain/common/response"
	"fobrain/fobrain/config"
	"fobrain/models/mysql/user"
	"fobrain/pkg/cfg"
)

type AuthSuite struct {
	suite.Suite
	ctx *gin.Context
}

func TestAuthSuite(t *testing.T) {
	auth := new(AuthSuite)
	_ = config.LoadConfigByFile("../../../.env")
	suite.Run(t, auth)
}

func (s *AuthSuite) SetupTest() {
	// 初始化 ctx
	w := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", "/captcha", nil)
	s.ctx, _ = gin.CreateTestContext(w)
	s.ctx.Request = req
}

func (s *AuthSuite) Test_Controller_Captcha() {
	Convey("Test_Controller_Captcha", s.T(), func() {
		Convey("GenerateError", func() {
			time.Sleep(time.Millisecond * 300)
			defer ApplyFuncReturn(captcha.Generate, nil, nil, errors.New("GenerateError")).Reset()
			err := Captcha(s.ctx)
			So(err, ShouldBeError)
		})
	})
}

func (s *AuthSuite) Test_Controller_Captcha1() {
	Convey("Test_Controller_Captcha", s.T(), func() {

		Convey("Pass", func() {
			time.Sleep(time.Millisecond * 300)
			defer ApplyFuncReturn(captcha.Generate, nil, nil, nil).Reset()
			defer ApplyFuncReturn(response.OkWithData, nil).Reset()
			err := Captcha(s.ctx)
			So(err, ShouldBeNil)
		})
	})
}

func (s *AuthSuite) Test_Controller_Login() {
	Convey("Test_Controller_Login", s.T(), func() {
		time.Sleep(time.Millisecond * 300)
		loginRequest := &LoginRequest{
			Account:     "Account",
			Password:    "Password",
			CaptchaId:   "CaptchaId",
			CaptchaCode: "CaptchaCode",
		}

		Convey("ValidateError", func() {
			defer ApplyFuncVarReturn(&LoginValidate, loginRequest, errors.New("LoginValidateError")).Reset()
			err := Login(s.ctx)
			So(err, ShouldBeError)
		})

		Convey("VerifyError", func() {
			defer ApplyFuncVarReturn(&LoginValidate, loginRequest, nil).Reset()
			defer ApplyFuncReturn(captcha.Verify, false).Reset()
			err := Login(s.ctx)
			So(err, ShouldBeError)
		})

		Convey("UserFirstError", func() {
			defer ApplyFuncVarReturn(&LoginValidate, loginRequest, nil).Reset()
			defer ApplyFuncReturn(captcha.Verify, true).Reset()
			defer ApplyMethodReturn(user.NewUserModel(), "First", user.User{}, errors.New("UserFirstError")).Reset()
			err := Login(s.ctx)
			So(err, ShouldBeError)
		})

		Convey("PasswordError", func() {
			defer ApplyFuncVarReturn(&LoginValidate, loginRequest, nil).Reset()
			defer ApplyFuncReturn(captcha.Verify, true).Reset()
			defer ApplyMethodReturn(user.NewUserModel(), "First", user.User{
				Password: "Password",
			}, nil).Reset()
			err := Login(s.ctx)
			So(err, ShouldBeError)
		})

		Convey("TokenError", func() {
			defer ApplyFuncVarReturn(&LoginValidate, loginRequest, nil).Reset()
			defer ApplyFuncReturn(captcha.Verify, true).Reset()
			defer ApplyMethodReturn(user.NewUserModel(), "First", user.User{
				Password: "dc647eb65e6711e155375218212b3964",
			}, nil).Reset()
			defer ApplyFuncReturn(jwtAuth.GenerateToken, "token", nil, errors.New("TokenError")).Reset()
			err := Login(s.ctx)
			So(err, ShouldBeError)
		})

		Convey("Pass", func() {
			defer ApplyFuncVarReturn(&LoginValidate, loginRequest, nil).Reset()
			defer ApplyFuncReturn(captcha.Verify, true).Reset()
			defer ApplyMethodReturn(user.NewUserModel(), "First", user.User{
				Password: "dc647eb65e6711e155375218212b3964",
			}, nil).Reset()
			defer ApplyMethodReturn(user.NewUserModel(), "IsAccountLocked", false, nil).Reset()
			defer ApplyMethodReturn(user.NewUserModel(), "ResetLoginFailures", nil).Reset()
			defer ApplyMethodReturn(user.NewUserModel(), "IsPasswordExpired", false, nil).Reset()
			expiredAt := time.Now().Add(time.Duration(cfg.LoadJwt().Expire) * time.Minute)
			defer ApplyFuncReturn(jwtAuth.GenerateToken, "token", &expiredAt, nil).Reset()
			defer ApplyFuncReturn(response.OkWithData, nil).Reset()
			err := Login(s.ctx)
			So(err, ShouldBeNil)
		})
	})
}

func (s *AuthSuite) Test_Controller_GenApiToken() {
	Convey("Test_Controller_GenApiToken", s.T(), func() {

		genApiTokenRequest := &GenApiTokenRequest{
			Ref: true,
		}

		Convey("UserInfoIsNull", func() {
			defer ApplyFuncReturn(request.GetUserInfo, nil).Reset()
			err := GenApiToken(s.ctx)
			So(err, ShouldBeError)
		})

		Convey("GenApiTokenValidateError", func() {
			defer ApplyFuncReturn(request.GetUserInfo, &user.User{}).Reset()
			defer ApplyFuncVarReturn(&GenApiTokenValidate, genApiTokenRequest, errors.New("GenApiTokenValidateError")).Reset()
			err := GenApiToken(s.ctx)
			So(err, ShouldBeError)
		})

		Convey("GenerateApiTokenError", func() {
			defer ApplyFuncReturn(request.GetUserInfo, &user.User{}).Reset()
			defer ApplyFuncVarReturn(&GenApiTokenValidate, genApiTokenRequest, nil).Reset()
			//expiredAt := time.Now().Add(time.Duration(cfg.LoadJwt().Expire) * time.Minute)
			defer ApplyFuncReturn(jwtAuth.GenerateApiToken, "token", nil, errors.New("GenerateApiTokenError")).Reset()
			err := GenApiToken(s.ctx)
			So(err, ShouldBeError)
		})

		Convey("Pass", func() {
			defer ApplyFuncReturn(request.GetUserInfo, &user.User{}).Reset()
			defer ApplyFuncVarReturn(&GenApiTokenValidate, genApiTokenRequest, nil).Reset()
			expiredAt := localtime.Time(time.Now().AddDate(999, 0, 0))
			defer ApplyFuncReturn(jwtAuth.GenerateApiToken, "token", &expiredAt, nil).Reset()
			defer ApplyFuncReturn(response.OkWithData, nil).Reset()
			err := GenApiToken(s.ctx)
			So(err, ShouldBeNil)
		})
	})
}

func TestUserInfo(t *testing.T) {
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	req := httptest.NewRequest("GET", "/api/v1/user", nil)
	// 创建一个Gin上下文
	c.Request = req

	// 调用函数
	err := UserInfo(c)
	assert.Error(t, err, "user not found")

	// 解析响应数据
	response := w.Result()
	defer response.Body.Close()
	body, _ := ioutil.ReadAll(response.Body)
	assert.Empty(t, body)
}

func TestGenLoginToken(t *testing.T) {
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()

	mockDb.ExpectQuery("SELECT * FROM `users` WHERE `id` = ?").WithArgs(1).WillReturnRows(mockDb.NewRows([]string{"id", "account", "username", "password", "created_at", "updated_at"}).AddRow(1, "test", "超级管理员", "test", time.Now(), time.Now()))

	mockDb.ExpectBegin()
	mockDb.ExpectExec("INSERT INTO `tokens` (`created_at`,`updated_at`,`type`,`token`,`user_id`,`expired_at`) VALUES (?,?,?,?,?,?)").WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), token.TokenTypeNormal, sqlmock.AnyArg(), 1, sqlmock.AnyArg()).WillReturnResult(sqlmock.NewResult(1, 1))
	mockDb.ExpectCommit()

	userInfo, _ := user.NewUserModel().First(mysql.WithId(1))
	token, _, err := jwtAuth.GenerateToken(userInfo)
	t.Log(token)
	assert.NoError(t, err)
}

func TestChangePwd(t *testing.T) {
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()

	userInfo := &user.User{
		Password: "61bf8f71beef54f99ee8fd12753c0abc",
	}
	defer ApplyFuncReturn(captcha.Verify, true).Reset()
	defer ApplyFuncReturn(request.GetUserInfo, userInfo).Reset()
	defer ApplyMethodReturn(userInfo, "ChangePwd", nil).Reset()
	defer ApplyMethodReturn(token.NewTokenModel(), "Del", nil).Reset()

	// 创建一个测试请求和响应
	w := httptest.NewRecorder()
	// 创建一个测试请求
	req := httptest.NewRequest("PUT", "/change-password", bytes.NewBuffer([]byte(`{"captcha_id":"CaptchaId","captcha_code":"CaptchaCode","old_password":"YWRtaW5AZmIwMSE=","new_password":"YWRtaW5AZmIwMSFB"}`)))
	req.Header.Set("Content-Type", "application/json")
	c, _ := gin.CreateTestContext(w)
	// 设置请求
	c.Request = req
	err := ChangePwd(c)
	assert.Nil(t, err)
	assert.Equal(t, http.StatusOK, w.Result().StatusCode)
}
