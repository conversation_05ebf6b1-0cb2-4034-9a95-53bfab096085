package file

import (
	"bytes"
	"errors"
	"fmt"
	"fobrain/fobrain/app/services/storage_logs"
	"io/ioutil"
	"net/http"
	"net/url"
	"path/filepath"

	"github.com/gabriel-vasile/mimetype"
	"github.com/gin-gonic/gin"
	"github.com/xuri/excelize/v2"

	"fobrain/fobrain/common/request"
	"fobrain/fobrain/common/response"
	"fobrain/models/mysql/custom_column"
	"fobrain/pkg/cfg"
	"fobrain/pkg/utils"
)

// File 下载自定义文件
func File(ctx *gin.Context) error {
	params, err := request.Validate(ctx, &struct {
		File string `json:"template" form:"template" uri:"template" validate:"required" zh:"文件名"`
	}{})
	// 设置响应头 Content-Type
	ctx.Header("Content-Type", "application/octet-stream")
	// 设置响应头 Content-Description
	ctx.Header("Content-Description", "File Transfer")
	// 设置响应头 Content-Transfer-Encoding
	ctx.Header("Content-Transfer-Encoding", "binary")
	if err != nil {
		return err
	}
	if path, canDelete, dErr := utils.DecodeFilePath(params.File); dErr != nil {
		return response.FailWithCodeMessage(ctx, 400, "解析文件信息失败")
	} else {
		if !utils.FileExists(path) {
			return response.FailWithCodeMessage(ctx, 404, "文件不存在")
		}
		return response.OkWithFile(ctx, path, canDelete)
	}
}

// Storage 下载Storage文件
func Storage(ctx *gin.Context) error {
	params, err := request.Validate(ctx, &struct {
		File string `json:"template" form:"template" uri:"template" validate:"required" zh:"文件名"`
	}{})
	// 设置响应头 Content-Type
	ctx.Header("Content-Type", "application/octet-stream")
	// 设置响应头 Content-Description
	ctx.Header("Content-Description", "File Transfer")
	// 设置响应头 Content-Transfer-Encoding
	ctx.Header("Content-Transfer-Encoding", "binary")
	if err != nil {
		return err
	}
	return response.OkWithFile(ctx, filepath.Join(cfg.LoadCommon().StoragePath+"/app/files/", params.File), false)
}

// ImageIcon 下载图标文件
func ImageIcon(ctx *gin.Context) error {
	params, err := request.Validate(ctx, &struct {
		File string `json:"template" form:"template" uri:"template" validate:"required" zh:"文件名"`
	}{})
	if err != nil {
		return err
	}
	storagePath := cfg.LoadCommon().StoragePath
	if !utils.FileExists(filepath.Join(storagePath, "/app/public/icon/"+params.File)) {
		return response.FailWithCodeMessage(ctx, 404, "文件不存在")
	}
	return response.OkWithImage(ctx, filepath.Join(storagePath, "/app/public/icon/"+params.File))
}

// ImageLogo 下载Logo文件
func ImageLogo(ctx *gin.Context) error {
	params, err := request.Validate(ctx, &struct {
		File string `json:"template" form:"template" uri:"template" validate:"required" zh:"文件名"`
	}{})
	if err != nil {
		return err
	}
	storagePath := cfg.LoadCommon().StoragePath
	if !utils.FileExists(filepath.Join(storagePath, "/app/public/logo/"+params.File)) {
		return response.FailWithCodeMessage(ctx, 404, "文件不存在")
	}
	return response.OkWithImage(ctx, filepath.Join(storagePath, "/app/public/logo/"+params.File))
}

// Template 下载Template文件
func Template(ctx *gin.Context) error {
	params, err := request.Validate(ctx, &struct {
		Id int `json:"id" form:"id" uri:"id" validate:"required,number" zh:"文件名Id"`
	}{})
	if err != nil {
		return err
	}

	fileName := ""
	switch params.Id {
	case 1:
		fileName = "导入资产模板.xlsx"
	case 2:
		fileName = "导入漏洞模板.xlsx"
	case 3:
		fileName = "导入人员模板.xlsx"
	case 4:
		fileName = "内外网映射关系模板.xlsx"
	case 5:
		fileName = "导入IP段模板.xlsx"
	case 6:
		fileName = "导入单位模板.xlsx"
	case 7:
		fileName = "导入业务系统模板.xlsx"
	case 8:
		fileName = "资产映射导入模版.xlsx"
	case 9:
		fileName = "导入黑白名单模板.xlsx"
	}

	storagePath := cfg.LoadCommon().StoragePath
	if !utils.FileExists(filepath.Join(storagePath, "/app/public/template/"+fileName)) {
		return response.FailWithCodeMessage(ctx, 404, "文件不存在")
	}

	data, err := ioutil.ReadFile(filepath.Join(storagePath, "/app/public/template/"+fileName))
	if err != nil {
		return err
	}

	// 向模板中补充自定义字段
	var customFields []*custom_column.CustomFieldMeta
	switch params.Id {
	case 1:
		fileName = "导入资产模板.xlsx"
		// 资产自定义字段
		assetCustomFields, err := custom_column.NewCustomFieldMetaModel().GetByModuleType(custom_column.CustomFieldMetaModuleTypeAsset, false)
		if err != nil {
			return response.FailWithMessage(ctx, "获取资产自定义字段失败")
		}
		// 设备自定义字段
		deviceCustomFields, err := custom_column.NewCustomFieldMetaModel().GetByModuleType(custom_column.CustomFieldMetaModuleTypeDevice, false)
		if err != nil {
			return response.FailWithMessage(ctx, "获取设备自定义字段失败")
		}
		// 合并资产和设备自定义字段
		customFields = append(customFields, assetCustomFields...)
		customFields = append(customFields, deviceCustomFields...)
	case 2:
		fileName = "导入漏洞模板.xlsx"
		customFields, err = custom_column.NewCustomFieldMetaModel().GetByModuleType(custom_column.CustomFieldMetaModuleTypeVuln, false)
		if err != nil {
			return response.FailWithMessage(ctx, "获取自定义字段失败")
		}
	case 3:
		fileName = "导入人员模板.xlsx"
		customFields, err = custom_column.NewCustomFieldMetaModel().GetByModuleType(custom_column.CustomFieldMetaModuleTypeStaff, false)
		if err != nil {
			return response.FailWithMessage(ctx, "获取自定义字段失败")
		}
	case 7:
		fileName = "导入业务系统模板.xlsx"
		customFields, err = custom_column.NewCustomFieldMetaModel().GetByModuleType(custom_column.CustomFieldMetaModuleTypeBusinessSystem, false)
		if err != nil {
			return response.FailWithMessage(ctx, "获取自定义字段失败")
		}
	}
	// 修改excel文件
	data, err = AddCustomFieldsToExcel(data, customFields)
	if err != nil {
		return response.FailWithMessage(ctx, "修改自定义字段失败")
	}

	// Compatible with Chinese.
	fileName = url.PathEscape(fileName)

	// Detect returns the MIME type found from the provided byte slice.
	detect := mimetype.Detect(data)

	// Setting http responses header.
	ctx.Writer.Header().Set("Content-Disposition", `attachment; filename*=UTF-8''`+fileName+``)
	ctx.Writer.Header().Set("Content-Type", detect.String())

	// Write data into the responses body.
	if _, err = ctx.Writer.Write(data); err != nil {
		return err
	}

	// Write status code into the response header.
	ctx.Writer.WriteHeader(http.StatusOK)

	return nil
}

func AddCustomFieldsToExcel(excelData []byte, customFields []*custom_column.CustomFieldMeta) ([]byte, error) {
	f, err := excelize.OpenReader(bytes.NewReader(excelData))
	if err != nil {
		return nil, err
	}
	sheetName := f.GetSheetName(0) // 默认第一个sheet
	// 以第2行为表头（如有需要可调整）
	headerRowIdx := 2
	headerRow, err := f.GetRows(sheetName)
	if err != nil {
		return nil, err
	}
	header := headerRow[headerRowIdx-1]
	// 追加自定义字段
	colStart := len(header) + 1
	for i, field := range customFields {
		colName, _ := excelize.ColumnNumberToName(colStart + i)
		cell := fmt.Sprintf("%s%d", colName, headerRowIdx)
		if field.ModuleType == custom_column.CustomFieldMetaModuleTypeAsset {
			// 设置自定义字段key，该行是隐藏的
			f.SetCellValue(sheetName, cell, "asset.custom_fields."+field.FieldKey)
		} else if field.ModuleType == custom_column.CustomFieldMetaModuleTypeDevice {
			// 设置自定义字段key，该行是隐藏的
			f.SetCellValue(sheetName, cell, "device.custom_fields."+field.FieldKey)
		} else {
			// 设置自定义字段key，该行是隐藏的
			f.SetCellValue(sheetName, cell, "custom_fields."+field.FieldKey)
		}
		// 设置下一行为显示名
		f.SetCellValue(sheetName, fmt.Sprintf("%s%d", colName, headerRowIdx+1), field.DisplayName)
		// 提示信息
		f.SetCellValue(sheetName, fmt.Sprintf("%s%d", colName, headerRowIdx+2), "自定义字段")
	}
	// 导出
	var buf bytes.Buffer
	if err := f.Write(&buf); err != nil {
		return nil, err
	}
	return buf.Bytes(), nil
}

// UploadIcon 上传图标
func UploadIcon(c *gin.Context) error {
	file, _ := c.FormFile("icon")

	fileType, err := request.GetFileType(file)
	if err != nil {
		return err
	}

	if !utils.ListContains([]string{"image/png", "image/jpeg", "image/jpg",
		"image/svg+xml", "image/x-icon", "text/xml; charset=utf-8", "text/plain; charset=utf-8"}, fileType) {
		return errors.New("不被允许上传的文件类型:" + fileType)
	}

	storagePath := cfg.LoadCommon().StoragePath
	// 完成路径
	dst := filepath.Join(storagePath, "/app/public/icon/", file.Filename)
	// 保存文件
	if err = c.SaveUploadedFile(file, dst); err != nil {
		return err
	}

	return response.OkWithData(c, gin.H{"path": dst})
}

func ListLogFiles(c *gin.Context) error {
	files, err := storage_logs.ListLogFiles()
	if err != nil {
		return err
	}
	return response.OkWithData(c, files)
}

func DownloadLogFiles(c *gin.Context) error {
	params, err := request.Validate(c, &struct {
		Files []string `json:"ids" form:"ids" uri:"ids" validate:"required" zh:"文件名"`
	}{})
	if err != nil {
		return err
	}
	names := utils.ListNonZero(params.Files)
	if len(names) == 0 {
		return errors.New("未选择文件")
	}
	zipFile, err := storage_logs.DownloadLogFiles(names)
	if err != nil {
		return err
	}
	return response.OkWithFile(c, zipFile, true)
}
