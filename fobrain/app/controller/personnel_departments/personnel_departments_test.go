package personnel_departments

import (
	"bytes"
	"encoding/json"
	"io"
	"io/ioutil"
	"mime/multipart"
	"net/http"
	"net/http/httptest"
	"reflect"
	"strings"
	"testing"
	"time"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/xuri/excelize/v2"

	srv "fobrain/fobrain/app/repository/personnel_departments"
	req "fobrain/fobrain/app/request/personnel_departments"
	res "fobrain/fobrain/app/response/personnel_departments"
	"fobrain/initialize/mysql"
	models "fobrain/models/mysql/personnel_departments"
)

// mockMultipartFile 实现 multipart.File 接口
type mockMultipartFile struct {
	*bytes.Reader
}

func (m *mockMultipartFile) Close() error {
	return nil
}

func (m *mockMultipartFile) ReadAt(p []byte, off int64) (int, error) {
	return m.Reader.ReadAt(p, off)
}

func (m *mockMultipartFile) Seek(offset int64, whence int) (int64, error) {
	return m.Reader.Seek(offset, whence)
}

func TestImport(t *testing.T) {
	// 模拟文件上传
	var buf bytes.Buffer
	writer := multipart.NewWriter(&buf)
	part, _ := writer.CreateFormFile("file", "test.xlsx")
	part.Write([]byte("dummy content"))
	writer.Close()

	data := req.ImportRequest{
		InsertType: 1,
		FileType:   1,
	}
	val, _ := json.Marshal(data)

	w := httptest.NewRecorder()
	req := httptest.NewRequest("POST", "/api/v1/personnel_departments/department_import", strings.NewReader(string(val)))
	req.Header.Set("Content-Type", "application/json")
	// 创建一个Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 使用 gomonkey 进行打桩
	patches := gomonkey.NewPatches()

	// 打桩 FormFile，返回 mockMultipartFile
	patches.ApplyMethod(reflect.TypeOf(c.Request), "FormFile", func(*http.Request, string) (multipart.File, *multipart.FileHeader, error) {
		return &mockMultipartFile{Reader: bytes.NewReader([]byte("dummy content"))}, &multipart.FileHeader{}, nil
	})

	// 打桩 excelize.OpenReader
	patches.ApplyFunc(excelize.OpenReader, func(r io.Reader) (*excelize.File, error) {
		f := &excelize.File{}
		return f, nil
	})

	// 打桩 f.GetRows
	patches.ApplyMethod(reflect.TypeOf(&excelize.File{}), "GetRows", func(f *excelize.File, sheet string, opts ...excelize.Options) ([][]string, error) {
		return [][]string{
			{"header1", "header2"},
			{"data1", "data2"},
		}, nil
	})

	// 打桩 srv.Import
	patches.ApplyFunc(srv.Import, func(rows [][]string, params interface{}) ([]string, error) {
		return []string{}, nil
	})

	defer patches.Reset() // 确保测试完成后重置打桩

	// 调用函数
	err := Import(c)
	assert.NoError(t, err)
}

func TestList(t *testing.T) {
	t.Run("Parameter Error", func(t *testing.T) {
		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/v1/personnel_departments?page=1&per_page=aaa", nil)
		req.Header.Set("Content-Type", "application/json")
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		// 调用函数
		err := List(c)
		assert.Error(t, err)
	})
	t.Run("Success", func(t *testing.T) {
		// 创建一个测试请求和响应
		list := []res.List{
			{
				Id:       1,
				Name:     "AA",
				PerNum:   0,
				DevNum:   0,
				BppNum:   0,
				ParentId: 0,
				Children: []res.List{},
			},
		}
		time.Sleep(time.Second)
		defer gomonkey.ApplyFuncReturn(srv.List, list, int64(1), nil).Reset()

		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/v1/personnel_departments?page=1&per_page=10", nil)
		req.Header.Set("Content-Type", "application/json")
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		// 调用函数
		err := List(c)
		assert.NoError(t, err)

		// 解析响应数据
		response := w.Result()
		defer response.Body.Close()
		body, _ := ioutil.ReadAll(response.Body)
		assert.NotEmpty(t, body)
	})

}

func TestAllTreeList(t *testing.T) {
	// 创建一个测试请求和响应
	list := []res.List{
		{
			Id:       1,
			Name:     "AA",
			PerNum:   0,
			DevNum:   0,
			BppNum:   0,
			ParentId: 0,
			Children: []res.List{},
		},
	}
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyFuncReturn(srv.AllTreeList, list, nil).Reset()

	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/personnel_departments/all/tree/list", nil)
	req.Header.Set("Content-Type", "application/json")
	// 创建一个Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 调用函数
	err := AllTreeList(c)
	assert.NoError(t, err)

	// 解析响应数据
	response := w.Result()
	defer response.Body.Close()
	body, _ := ioutil.ReadAll(response.Body)
	assert.NotEmpty(t, body)
}

func TestCreate(t *testing.T) {

	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyFuncReturn(srv.Create, nil).Reset()

	data := req.InsertPersonnelDepartmentsRequest{
		Name:     "AA",
		ParentId: 0,
	}
	val, _ := json.Marshal(data)
	w := httptest.NewRecorder()
	req := httptest.NewRequest("POST", "/api/v1/personnel_departments", strings.NewReader(string(val)))
	req.Header.Set("Content-Type", "application/json")
	// 创建一个Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 调用函数
	err := Create(c)
	assert.NoError(t, err)

	// 解析响应数据
	response := w.Result()
	defer response.Body.Close()
	body, _ := ioutil.ReadAll(response.Body)
	assert.NotEmpty(t, body)
}

func TestUpdate(t *testing.T) {
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyFuncReturn(srv.Update, nil).Reset()

	data := req.UpdatePersonnelDepartmentsRequest{
		Id:       1,
		Name:     "AA",
		ParentId: 0,
	}
	val, _ := json.Marshal(data)
	w := httptest.NewRecorder()
	req := httptest.NewRequest("PUT", "/api/v1/personnel_departments", strings.NewReader(string(val)))
	req.Header.Set("Content-Type", "application/json")
	// 创建一个Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 调用函数
	err := Update(c)
	assert.NoError(t, err)

	// 解析响应数据
	response := w.Result()
	defer response.Body.Close()
	body, _ := ioutil.ReadAll(response.Body)
	assert.NotEmpty(t, body)
}

func TestDeleteByIds(t *testing.T) {
	// 创建一个测试请求和响应
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyFuncReturn(srv.DeleteByIds, nil).Reset()

	w := httptest.NewRecorder()
	req := httptest.NewRequest("DELETE", "/api/v1/personnel_departments", bytes.NewBufferString(`{"ids": [1]}`))
	req.Header.Set("Content-Type", "application/json")
	// 创建一个Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 调用函数
	err := DeleteByIds(c)
	assert.NoError(t, err)

	// 解析响应数据
	response := w.Result()
	defer response.Body.Close()
	body, _ := ioutil.ReadAll(response.Body)
	assert.NotEmpty(t, body)
}

func TestFirst(t *testing.T) {
	// 创建一个测试请求和响应
	list := &models.PersonnelDepartments{
		BaseModel: mysql.BaseModel{
			Id: 1,
		},
		Name:       "AA",
		ParentId:   0,
		RegionTree: "0",
		FullName:   "AA",
		ParentName: "",
		Level:      1,
		Source:     1,
	}
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyMethodReturn(models.NewPersonnelDepartmentsModel(), "First", list, nil).Reset()

	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/personnel_departments?id=1", nil)
	req.Header.Set("Content-Type", "application/json")
	// 创建一个Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 调用函数
	err := First(c)
	assert.NoError(t, err)

	// 解析响应数据
	response := w.Result()
	defer response.Body.Close()
	body, _ := ioutil.ReadAll(response.Body)
	assert.NotEmpty(t, body)
}

func TestCheckDel(t *testing.T) {
	// 创建响应记录器和Gin上下文
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/personnel_departments/check/del?keyword=abc", nil)
	req.Header.Set("Content-Type", "application/json")
	// 创建一个Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 使用gomonkey创建打桩对象
	patches := gomonkey.NewPatches()
	defer patches.Reset() // 确保测试完成后重置打桩

	// 打桩 request.Validate

	// 打桩 srv.CheckDel，模拟返回checkDel和content的值
	patches.ApplyFunc(srv.CheckDel, func(params interface{}) (bool, string, error) {
		return true, "Delete success", nil
	})

	// 执行 CheckDel 函数
	err := CheckDel(c)

	// 检查返回结果
	assert.NoError(t, err)
	assert.Equal(t, http.StatusOK, w.Code)
}
