package personnel_departments

import (
	"mime/multipart"

	"github.com/gin-gonic/gin"
	"github.com/xuri/excelize/v2"

	srv "fobrain/fobrain/app/repository/personnel_departments"
	"fobrain/fobrain/app/request/personnel_departments"
	"fobrain/fobrain/common/request"
	"fobrain/fobrain/common/response"
)

func Import(c *gin.Context) error {
	params, err := request.Validate(c, &personnel_departments.ImportRequest{})
	if err != nil {
		return err
	}
	file, _, err := c.Request.FormFile("file")
	if err != nil {
		return err
	}

	defer func(file multipart.File) {
		err := file.Close()
		if err != nil {
			err := response.FailWithCodeMessage(c, 500, "关闭文件失败")
			if err != nil {
				return
			}
			return
		}
	}(file)

	f, err := excelize.OpenReader(file)
	if err != nil {
		return err
	}

	//读取Sheet1的数据
	rows, err := f.GetRows("Sheet1", excelize.Options{RawCellValue: true})
	if err != nil {
		return err
	}
	strings, err := srv.Import(rows, params)
	if err != nil {
		return err
	}
	return response.OkWithData(c, strings)
}

func List(c *gin.Context) error {
	params, err := request.Validate(c, &personnel_departments.PersonnelDepartmentsRequest{})
	if err != nil {
		return err
	}
	list, total, err := srv.List(params)
	return response.OkWithPageData(c, total, params.Page, params.PerPage, list)
}

func AllTreeList(c *gin.Context) error {
	list, err := srv.AllTreeList()
	if err != nil {
		return err
	}
	return response.OkWithData(c, list)

}

func Create(c *gin.Context) error {
	params, err := request.Validate(c, &personnel_departments.InsertPersonnelDepartmentsRequest{})
	if err != nil {
		return err
	}
	err = srv.Create(params)
	if err != nil {
		return err
	}
	return response.OkWithMessage(c, "添加成功")
}

func First(c *gin.Context) error {
	params, err := request.Validate(c, &personnel_departments.FirstRequest{})
	if err != nil {
		return err
	}
	data, err := srv.First(params)
	if err != nil {
		return err
	}
	return response.OkWithData(c, data)
}

func Update(c *gin.Context) error {
	params, err := request.Validate(c, &personnel_departments.UpdatePersonnelDepartmentsRequest{})
	if err != nil {
		return err
	}

	err = srv.Update(params)
	if err != nil {
		return err
	}
	return response.OkWithMessage(c, "修改成功")

}

// DeleteByIds 根据提供的ID列表删除对应的人员部门信息
//
// 参数：
// c: gin.Context对象，用于处理HTTP请求
//
// 返回值：
// error: 如果删除失败，返回错误信息；否则返回nil
func DeleteByIds(c *gin.Context) error {
	params, err := request.Validate(c, &personnel_departments.DeleteByIdsRequest{})
	if err != nil {
		return err
	}
	err = srv.DeleteByIds(params)
	if err != nil {
		return err
	}
	return response.OkWithMessage(c, "删除成功")
}

func CheckDel(c *gin.Context) error {
	params, err := request.Validate(c, &personnel_departments.DeleteByIdsRequest{})
	if err != nil {
		return err
	}
	checkDel, content, err := srv.CheckDel(params)
	if err != nil {
		return err
	}
	return response.OkWithData(c, map[string]interface{}{
		"data":    checkDel,
		"message": content,
	})
}
