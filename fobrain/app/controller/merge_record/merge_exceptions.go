package merge_record

import (
	"fobrain/fobrain/app/services/merge_exception"
	"fobrain/fobrain/common/request"
	"fobrain/fobrain/common/response"

	"github.com/gin-gonic/gin"
)

type ListExceptionsRequest struct {
	request.PageRequest
	MergeType string `json:"merge_type" form:"merge_type" uri:"merge_type" validate:"required"`             // 融合类型
	RecordId  string `json:"record_id" form:"record_id" uri:"record_id" validate:"required"`                // 融合记录ID
	Status    string `json:"status" form:"status" uri:"status" validate:"omitempty,oneof=FAILED DISCARDED"` // 状态
	Keyword   string `json:"keyword" form:"keyword" uri:"keyword" validate:"omitempty,max=255"`             // 关键字
}

// ListExceptions 获取融合异常列表
func ListExceptions(c *gin.Context) error {
	params := &ListExceptionsRequest{}
	params, err := request.Validate(c, params)
	if err != nil {
		return err
	}
	results, total, err := merge_exception.ListExceptions(params.Page, params.PerPage, params.MergeType, params.RecordId, params.Status, params.Keyword)
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	return response.OkWithPageData(c, total, params.Page, params.PerPage, results)
}
