package merge_record

import (
	"fobrain/fobrain/common/request"
	"fobrain/fobrain/common/response"
	"fobrain/initialize/mysql"
	"fobrain/models/mysql/merge"
	"strconv"

	"github.com/gin-gonic/gin"
)

type ListRequest struct {
	request.PageRequest
	MergeType     string   `json:"merge_type" form:"merge_type" uri:"merge_type"`             // 融合类型
	Keyword       string   `json:"keyword" form:"keyword" uri:"keyword"`                      // 关键字
	ExcludeStatus []string `json:"exclude_status" form:"exclude_status" uri:"exclude_status"` // 排除状态
}

func List(c *gin.Context) error {
	params := &ListRequest{}
	params, err := request.Validate(c, params)
	if err != nil {
		return err
	}
	query := make([]mysql.HandleFunc, 0)
	if params.MergeType != "" {
		query = append(query, mysql.WithColumnValue("merge_type", params.MergeType))
	}
	if len(params.ExcludeStatus) > 0 {
		query = append(query, mysql.WithValueNotIn("task_status", params.ExcludeStatus))
	}
	if params.Keyword != "" {
		keyword := "%" + params.Keyword + "%"
		query = append(query, mysql.WithWhere("trigger_event like ? OR task_desc like ? OR merge_type like ? OR sources.name like ? OR data_nodes.name like ?", keyword, keyword, keyword, keyword, keyword))
	}
	data, total, err := merge.NewMergeRecordsModel().List(params.Page, params.PerPage, nil, query...)
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}

	// 统计运行中的任务数量
	count, err := merge.NewMergeRecordsModel().Count(mysql.WithColumnValue("task_status", merge.MergeRecordsStatusRunning))
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}

	return response.OkWithData(c, map[string]interface{}{
		"items":         data,
		"total":         total,
		"page":          params.Page,
		"per_page":      params.PerPage,
		"running_count": count,
	})
}

func Detail(c *gin.Context) error {
	id := c.Param("id")
	if id == "" {
		return response.FailWithMessage(c, "id不能为空")
	}
	idInt, err := strconv.ParseUint(id, 10, 64)
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	mergeRecord, err := merge.NewMergeRecordsModel().GetById(idInt)
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	return response.OkWithData(c, mergeRecord)
}
