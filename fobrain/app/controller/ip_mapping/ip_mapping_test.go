package ip_mapping

import (
	"bytes"
	"encoding/json"
	"errors"
	"mime/multipart"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"

	"fobrain/fobrain/app/request/ip_mapping"
	services "fobrain/fobrain/app/services/ip_mapping"
	"fobrain/fobrain/logs"
	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/models/mysql/custom_field"
	db "fobrain/models/mysql/ip_mapping"
	"fobrain/pkg/cfg"
	"fobrain/pkg/utils/common_logs"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"go.uber.org/zap"
)

func TestCreate(t *testing.T) {
	patches := []*gomonkey.Patches{
		gomonkey.ApplyMethodReturn(&db.IpMapping{}, "Create", nil),
		gomonkey.ApplyMethodReturn(&custom_field.CustomField{}, "First", &custom_field.CustomField{}, nil),
	}
	input := &map[string]interface{}{
		"public_ip":                      "127.0.0.1",
		"public_port":                    8800,
		"custom_field_1733889890_TrOOAL": "************",
		"custom_field_1733889890_l2gxAq": 9900,
	}

	body, _ := json.Marshal(input)
	w := httptest.NewRecorder()
	// 发送JSON数据
	req := httptest.NewRequest("POST", "/api/v1/ip-mapping/create", bytes.NewReader(body))

	c, _ := gin.CreateTestContext(w)
	c.Request = req

	err := Create(c)
	for _, patch := range patches {
		patch.Reset()
	}
	assert.Nil(t, err)
}

func TestImport(t *testing.T) {
	patches := []*gomonkey.Patches{
		gomonkey.ApplyFunc(logs.GetLogger, func() *common_logs.Logger {
			return &common_logs.Logger{
				Logger: &zap.Logger{},
				Err:    nil,
			}
		}),
		gomonkey.ApplyMethodReturn(&gin.Context{}, "FormFile", &multipart.FileHeader{
			Filename: "test",
			Header:   nil,
			Size:     100,
		}, nil),
		gomonkey.ApplyFuncReturn(cfg.LoadCommon, cfg.Common{StoragePath: ""}),
		gomonkey.ApplyMethodReturn(&gin.Context{}, "SaveUploadedFile", nil),
		gomonkey.ApplyMethodReturn(&multipart.FileHeader{}, "Open", nil, nil),
	}
	w := httptest.NewRecorder()
	req := httptest.NewRequest("POST", "/api/v1/ip-mapping/import", bytes.NewReader([]byte("")))

	c, _ := gin.CreateTestContext(w)
	c.Request = req

	err := Import(c)

	for _, patch := range patches {
		patch.Reset()
	}

	assert.Nil(t, err)
}

func TestImportConfirm(t *testing.T) {
	patches := []*gomonkey.Patches{
		gomonkey.ApplyFunc(logs.GetLogger, func() *common_logs.Logger {
			return &common_logs.Logger{
				Logger: &zap.Logger{},
				Err:    nil,
			}
		}),
		gomonkey.ApplyMethodReturn(&gin.Context{}, "FormFile", &multipart.FileHeader{
			Filename: "test",
			Header:   nil,
			Size:     100,
		}, nil),
		gomonkey.ApplyFuncReturn(cfg.LoadCommon, cfg.Common{StoragePath: ""}),
		gomonkey.ApplyMethodReturn(&gin.Context{}, "SaveUploadedFile", nil),
		gomonkey.ApplyMethodReturn(&multipart.FileHeader{}, "Open", nil, nil),
		gomonkey.ApplyFuncReturn(services.GetFieldName, map[string]string{"ip": "ip", "port": "port", "domain": "domain", "protocol": "protocol"}, true),
	}
	w := httptest.NewRecorder()
	req := httptest.NewRequest("POST", "/api/v1/ip-mapping/import/confirm", bytes.NewReader([]byte("")))

	c, _ := gin.CreateTestContext(w)
	c.Request = req

	err := ImportConfirm(c)

	for _, patch := range patches {
		patch.Reset()
	}

	assert.Nil(t, err)
}

func TestDelete(t *testing.T) {
	mockDb := testcommon.InitSqlMock()
	defer mockDb.Close()
	patches := []*gomonkey.Patches{
		gomonkey.ApplyFunc(logs.GetLogger, func() *common_logs.Logger {
			return &common_logs.Logger{
				Logger: &zap.Logger{},
				Err:    nil,
			}
		}),
		gomonkey.ApplyMethodReturn(&db.IpMappingAuditLog{}, "Create", nil),
		gomonkey.ApplyMethodReturn(&db.IpMappingAuditData{}, "CreateBatch", nil),
	}

	mockDb.ExpectQuery("SELECT column_name FROM information_schema.columns WHERE table_name = ?").
		WillReturnRows(sqlmock.NewRows([]string{"column_name"}).AddRow("public_ip"))

	mockDb.ExpectQuery("SELECT * FROM `ip_mapping` WHERE public_ip LIKE ? AND id IN (?,?,?)").
		WithArgs("%***********%", 1, 2, 3).
		WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))

	mockDb.Mock.ExpectBegin()
	mockDb.ExpectExec("DELETE FROM `ip_mapping` WHERE public_ip LIKE ? AND id IN (?,?,?) AND `ip_mapping`.`id` = ?").
		WillReturnResult(sqlmock.NewResult(1, 1))
	mockDb.Mock.ExpectCommit()

	input := &ip_mapping.DeleteRequest{
		Ids:     []uint64{1, 2, 3},
		Keyword: "***********",
	}
	jsonInput, _ := json.Marshal(input)
	w := httptest.NewRecorder()
	req := httptest.NewRequest("DELETE", "/api/v1/ip-mapping/delete", bytes.NewReader(jsonInput))

	c, _ := gin.CreateTestContext(w)
	c.Request = req

	err := Delete(c)
	for _, patch := range patches {
		patch.Reset()
	}
	assert.Nil(t, err)
}

func TestList(t *testing.T) {
	patches := []*gomonkey.Patches{
		gomonkey.ApplyFunc(logs.GetLogger, func() *common_logs.Logger {
			return &common_logs.Logger{
				Logger: &zap.Logger{},
				Err:    nil,
			}
		}),
		gomonkey.ApplyMethodReturn(&db.IpMapping{}, "List", []map[string]interface{}{}, int64(0), nil),
	}
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/ip-mapping/list?page=2&per_page=2&keyword=a", nil)

	c, _ := gin.CreateTestContext(w)
	c.Request = req

	err := List(c)
	for _, patch := range patches {
		patch.Reset()
	}
	assert.Nil(t, err)
}

func TestAuditList(t *testing.T) {
	patch := gomonkey.ApplyMethodReturn(&db.IpMappingAuditLog{}, "List", int64(0), []*db.IpMappingAuditLog{}, nil)
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/ip-mapping/audit_list?page=2&per_page=2&keyword=a", nil)

	c, _ := gin.CreateTestContext(w)
	c.Request = req

	err := AuditList(c)
	patch.Reset()
	assert.Nil(t, err)
}

func TestAuditDetail(t *testing.T) {
	patch := gomonkey.ApplyMethodReturn(&db.IpMappingAuditData{}, "List", int64(0), []*db.IpMappingAuditData{}, nil)
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/ip-mapping/audit_detail?page=2&per_page=2&batch_no=a", nil)

	c, _ := gin.CreateTestContext(w)
	c.Request = req

	err := AuditDetail(c)
	patch.Reset()
	assert.Nil(t, err)
}

func TestCheckIpMappingAlarm1(t *testing.T) {
	patch := gomonkey.ApplyFuncReturn(services.GetIpMappingForAlarm, errors.New("err"))
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/ip-mapping/check_alarm", nil)

	c, _ := gin.CreateTestContext(w)
	c.Request = req

	err := CheckIpMappingAlarm(c)
	patch.Reset()
	assert.Nil(t, err)
}

func TestCheckIpMappingAlarm2(t *testing.T) {
	patches := []*gomonkey.Patches{
		gomonkey.ApplyFuncReturn(services.GetIpMappingForAlarm, nil),
		gomonkey.ApplyMethodReturn(&db.IpMappingStatisticsAudit{}, "Create", nil),
	}
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/ip-mapping/check_alarm", nil)

	c, _ := gin.CreateTestContext(w)
	c.Request = req

	err := CheckIpMappingAlarm(c)
	for _, patch := range patches {
		patch.Reset()
	}
	assert.Nil(t, err)
}

func TestAlarmList1(t *testing.T) {
	patches := []*gomonkey.Patches{
		gomonkey.ApplyMethodReturn(&db.IpMappingStatisticsAudit{}, "Last", &db.IpMappingStatisticsAudit{}, nil),
		gomonkey.ApplyMethodReturn(&db.IpMappingStatistics{}, "List", int64(0), []map[string]interface{}{}, nil),
	}
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/ip-mapping/alarm_list?page=2&per_page=2&keyword=a", nil)

	c, _ := gin.CreateTestContext(w)
	c.Request = req

	err := AlarmList(c)
	for _, patch := range patches {
		patch.Reset()
	}
	assert.Nil(t, err)
}

func TestAlarmList2(t *testing.T) {
	patch1 := gomonkey.ApplyMethodReturn(&db.IpMappingStatisticsAudit{}, "Last", &db.IpMappingStatisticsAudit{}, errors.New("err"))
	patch2 := gomonkey.ApplyMethodReturn(&db.IpMappingStatistics{}, "List", int64(0), []map[string]interface{}{}, nil)
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/ip-mapping/alarm_list?page=2&per_page=2&keyword=a", nil)

	c, _ := gin.CreateTestContext(w)
	c.Request = req

	err := AlarmList(c)
	patch1.Reset()
	patch2.Reset()
	assert.Nil(t, err)
}

func TestExportAlarmList(t *testing.T) {
	// 设置 Gin 测试模式
	gin.SetMode(gin.TestMode)

	// 创建一个模拟请求的输入
	input := &ip_mapping.ExportRequest{
		Ids:     []uint64{1, 2, 3},
		Keyword: "test",
	}

	// 序列化输入为 JSON
	jsonData, _ := json.Marshal(input)
	req, _ := http.NewRequest("POST", "/api/v1/ip_mapping/export_alarm_list", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	// 创建响应记录器和上下文
	w := httptest.NewRecorder()
	ctx, _ := gin.CreateTestContext(w)
	ctx.Request = req

	mockDb := testcommon.InitSqlMock()
	mockDb.ExpectQuery("SELECT * FROM `ip_mapping_statistics_audit` ORDER BY id desc,`ip_mapping_statistics_audit`.`id` LIMIT 1").
		WillReturnRows(
			mockDb.NewRows([]string{"updated_at", "trigger_type", "trigger_time", "id",
				"handle_status", "handle_message", "end_time", "created_at", "batch_no"}).
				AddRow("2024-11-01 18:14:06", "自动", "2024-11-01 18:14:06", 1, "报警信息更新失败,err:获取内外网映射关系为空,page:1,perPage:100", "test", "2024-11-01 18:14:06", "2024-11-01 18:14:06", "20241101181406"))

	mockDb.ExpectQuery("SELECT * FROM `ip_mapping_statistics` WHERE `id` IN (?,?,?)").WithArgs(1, 2, 3).
		WillReturnRows(
			mockDb.NewRows([]string{"updated_at", "trigger_type", "trigger_time", "id",
				"handle_status", "handle_message", "end_time", "created_at", "batch_no"}).
				AddRow("2024-11-01 18:14:06", "自动", "2024-11-01 18:14:06", 1, "报警信息更新失败,err:获取内外网映射关系为空,page:1,perPage:100", "test", "2024-11-01 18:14:06", "2024-11-01 18:14:06", "20241101181406"))

	err := ExportAlarmList(ctx)
	mockDb.Close()
	assert.NoError(t, err)
	assert.Equal(t, http.StatusOK, w.Code)

	// 根据实际响应格式断言
	assert.NotEmpty(t, w.Body.Bytes())
}

func TestExportAlarmListError(t *testing.T) {
	// 设置 Gin 测试模式
	gin.SetMode(gin.TestMode)

	// 创建一个模拟请求的输入
	input := &ip_mapping.ExportRequest{
		Ids:     []uint64{1, 2, 3},
		Keyword: "test",
	}

	// 序列化输入为 JSON
	jsonData, _ := json.Marshal(input)
	req, _ := http.NewRequest("POST", "/api/v1/ip_mapping/export_alarm_list", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	// 创建响应记录器和上下文
	w := httptest.NewRecorder()
	ctx, _ := gin.CreateTestContext(w)
	ctx.Request = req

	mockDb := testcommon.InitSqlMock()
	mockDb.ExpectQuery("SELECT * FROM `ip_mapping_statistics_audit` ORDER BY id desc,`ip_mapping_statistics_audit`.`id` LIMIT 1").
		WillReturnRows(
			mockDb.NewRows([]string{"updated_at", "trigger_type", "trigger_time", "id",
				"handle_status", "handle_message", "end_time", "created_at", "batch_no"}).
				AddRow("2024-11-01 18:14:06", "自动", "2024-11-01 18:14:06", 1, "报警信息更新失败,err:获取内外网映射关系为空,page:1,perPage:100", "test", "2024-11-01 18:14:06", "2024-11-01 18:14:06", "20241101181406"))

	mockDb.ExpectQuery("SELECT * FROM `ip_mapping_statistics` WHERE `id` IN (?,?,?)").WithArgs(1, 2, 3).
		WillReturnError(errors.New("err"))

	err := ExportAlarmList(ctx)
	mockDb.Close()
	assert.NoError(t, err)
	assert.Equal(t, http.StatusBadRequest, w.Code)

	// 根据实际响应格式断言
	assert.NotEmpty(t, w.Body.Bytes())
}
