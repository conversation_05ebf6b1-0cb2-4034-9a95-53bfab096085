package ip_mapping

import (
	"bufio"
	"errors"
	"fmt"
	"io"
	"path/filepath"
	"strings"
	"time"

	"github.com/spf13/cast"

	ip_mapping2 "fobrain/fobrain/app/response/ip_mapping"
	"fobrain/models/mysql/custom_field"

	"fobrain/fobrain/app/request/ip_mapping"
	services "fobrain/fobrain/app/services/ip_mapping"
	"fobrain/fobrain/common/localtime"
	"fobrain/fobrain/common/response"
	"fobrain/fobrain/common/validate"
	"fobrain/fobrain/logs"
	"fobrain/initialize/mysql"
	db "fobrain/models/mysql/ip_mapping"
	"fobrain/pkg/cfg"
	"fobrain/pkg/utils"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/xuri/excelize/v2"
)

// Create todo
func Create(ctx *gin.Context) error {
	params := make(map[string]interface{})
	if err := ctx.ShouldBindJSON(&params); err != nil {
		return response.FailWithMessage(ctx, "参数错误")
	}
	// 调用model层接口保存数据
	batchNo := uuid.New().String()
	params["data_source"] = "人工录入"
	params["batch_no"] = batchNo
	ipCount := 0
	// 校验数据格式
	for key, value := range params {
		customField, err := custom_field.NewCustomFieldModel().First(mysql.WithWhere("`field_name` = ?", key))
		if err != nil {
			return response.FailWithMessage(ctx, "数据库错误")
		}
		if strings.Contains(customField.DisplayName, "IP") {
			if !utils.IsValidIP(cast.ToString(value)) {
				if cast.ToString(value) != "" {
					return errors.New(customField.DisplayName + "格式错误")
				}
			} else {
				ipCount += 1
			}
		} else if strings.Contains(customField.DisplayName, "端口") {
			if !utils.IsValidatePort(cast.ToString(value)) {
				if cast.ToString(value) != "" {
					return response.FailWithMessage(ctx, "端口格式错误")
				}
			}
		}
	}
	if ipCount < 2 {
		return response.FailWithMessage(ctx, "请至少填写两个IP")
	}
	// 保存关系数据
	err := db.NewIpMappingModel().Create(params)
	if err != nil {
		return err
	}
	return response.OkWithData(ctx, "创建成功")
}

func Import(ctx *gin.Context) error {
	logger := logs.GetLogger()
	input := &ip_mapping.ImportRequest{}
	if err := ctx.ShouldBind(input); err != nil {
		return err
	}
	ok, str := validate.Validator(input)
	if !ok {
		return response.FailWithMessage(ctx, str)
	}
	file, err := ctx.FormFile("file")
	if err != nil {
		return err
	}
	if file.Size > 10*1024*1024 {
		logger.Error("内外网关系映射文件文件大小不能大于10M")
		return response.FailWithCodeMessage(ctx, 400, "文件大小不能大于10M")
	}
	// 本地文件名
	localFileName := fmt.Sprintf("%s-%s", time.Now().Format("20060102150405"), file.Filename)
	// 将文件保存到服务器本地
	storagePath := cfg.LoadCommon().StoragePath
	if err := ctx.SaveUploadedFile(file, filepath.Join(storagePath, "/app/ip_mapping/", localFileName)); err != nil {
		return err
	}

	// 打开文件
	f, err := file.Open()
	if err != nil {
		logger.Errorf("内外网关系映射文件打开文件失败,err:%v", err)
		return response.FailWithCodeMessage(ctx, 400, "文件打开失败")
	}
	defer f.Close()
	// 记录审计数据
	successCount := int64(0)
	msg := ""
	batchNo := uuid.New().String()
	auditLog := &db.IpMappingAuditLog{
		BatchNo:         batchNo,
		FileName:        file.Filename,
		LocalFileName:   localFileName,
		OperationStatus: "成功",
		Operation:       "导入",
	}

	if input.FileType == 1 {
		auditLog.FileType = "模板文件"
		// 打开文件
		fileContent, err := excelize.OpenReader(f)
		if err != nil {
			logger.Errorf("内外网关系映射文件打开文件失败,err:%v", err)
			return response.FailWithCodeMessage(ctx, 400, "文件打开失败")
		}

		//读取Sheet1的数据
		rows, err := fileContent.GetRows("Sheet1", excelize.Options{RawCellValue: true})
		if err != nil {
			return err
		}
		// GetRows默认会去除末尾的空列，补全空列
		for index, _ := range rows {
			// 补全空值，确保每行有 8 列
			for len(rows[index]) < 8 {
				rows[index] = append(rows[index], "")
			}
		}
		if len(rows) == 0 {
			logger.Error("内外网关系映射模板文件,内容不能为空")
			auditLog.OperationStatus = "失败"
			auditLog.FailReason = "文件内容不能为空"
			err = db.NewIpMappingAuditLogModel().Create(ctx, auditLog)
			if err != nil {
				logger.Errorf("内外网关系映射文件保存审计记录失败,err:%v", err)
			}
			return response.FailWithCodeMessage(ctx, 400, "文件内容不能为空")
		}
		if len(rows) > 300000 {
			logger.Error("内外网关系映射模板文件,单次文件同步数量不能大于30w数据")
			auditLog.OperationStatus = "失败"
			auditLog.FailReason = "单次文件同步数量不能大于30w数据"
			err = db.NewIpMappingAuditLogModel().Create(ctx, auditLog)
			if err != nil {
				logger.Errorf("内外网关系映射文件保存审计记录失败,err:%v", err)
			}
			return response.FailWithCodeMessage(ctx, 400, "单次文件同步数量不能大于30w数据")
		}
		// 保存映射数据
		successCount, msg, err = services.ImportIpMappingByTemplate(batchNo, "模板文件", rows, input.FromMapping, input.ToMapping)
	} else if input.FileType == 2 {
		auditLog.FileType = "华为防火墙配置文件"
		// 打开文件
		scanner := bufio.NewScanner(f)
		lines := make([]string, 0)
		for scanner.Scan() {
			line := strings.TrimSpace(scanner.Text())
			if strings.HasPrefix(line, "nat server") {
				lines = append(lines, line)
			}
		}
		if len(lines) == 0 {
			logger.Error("内外网关系映射文件,不能解析到符合格式的数据")
			auditLog.OperationStatus = "失败"
			auditLog.FailReason = "不能解析到符合格式的数据"
			err = db.NewIpMappingAuditLogModel().Create(ctx, auditLog)
			if err != nil {
				logger.Errorf("内外网关系映射文件保存审计记录失败,err:%v", err)
			}
			return response.FailWithCodeMessage(ctx, 400, "不能解析到符合格式的数据")
		}
		// 保存映射数据
		successCount, msg, err = services.ImportIpMappingByHuaweiConfig(batchNo, "华为防火墙配置文件", lines, input.FromMapping, input.ToMapping)
	} else if input.FileType == 3 {
		auditLog.FileType = "A10数据源"
		datas := services.BuildA10Data(f)
		// 保存映射数据
		successCount, msg, err = services.ImportIpMappingByA10(batchNo, "A10数据源", datas, input.FromMapping, input.ToMapping)
	} else if input.FileType == 4 {
		auditLog.FileType = "Fortinet防火墙数据源"
		content, _ := io.ReadAll(f)
		successCount, msg, err = services.ImportIpMappingByFortinet(batchNo, "Fortinet防火墙数据源", string(content), input.FromMapping, input.ToMapping)
	} else if input.FileType == 5 {
		auditLog.FileType = "华为防火墙8000E-X8配置文件"
		// 打开文件
		scanner := bufio.NewScanner(f)
		lines := make([]string, 0)
		for scanner.Scan() {
			line := strings.TrimSpace(scanner.Text())
			if strings.HasPrefix(line, "nat server") {
				lines = append(lines, line)
			}
		}
		if len(lines) == 0 {
			logger.Error("内外网关系映射文件,不能解析到符合格式的数据")
			auditLog.OperationStatus = "失败"
			auditLog.FailReason = "不能解析到符合格式的数据"
			err = db.NewIpMappingAuditLogModel().Create(ctx, auditLog)
			if err != nil {
				logger.Errorf("内外网关系映射文件保存审计记录失败,err:%v", err)
			}
			return response.FailWithCodeMessage(ctx, 400, "不能解析到符合格式的数据")
		}
		// 保存映射数据
		successCount, msg, err = services.ImportIpMappingByHuaweiFWConfig(batchNo, "华为防火墙8000E-X8配置文件", lines, input.FromMapping, input.ToMapping)
	}
	// 失败也有可能存在保存成功的数据
	auditLog.EffectCount = int(successCount)
	if err != nil {
		auditLog.OperationStatus = "失败"
		auditLog.FailReason = msg + err.Error()
	} else {
		auditLog.OperationStatus = "成功"
	}

	// 保存审计记录
	err = db.NewIpMappingAuditLogModel().Create(ctx, auditLog)
	if err != nil {
		logger.Errorf("内外网关系映射文件保存审计记录失败,err:%v", err)
		return response.FailWithCodeMessage(ctx, 400, "保存审计记录失败")
	}
	return response.OkWithData(ctx, msg)
}

func ImportConfirm(ctx *gin.Context) error {
	input := &ip_mapping.ImportConfirmRequest{}
	if err := ctx.ShouldBind(input); err != nil {
		return err
	}
	ok, str := validate.Validator(input)
	if !ok {
		return response.FailWithMessage(ctx, str)
	}
	file, err := ctx.FormFile("file")
	if err != nil {
		return err
	}
	if file.Size > 10*1024*1024 {
		return response.FailWithCodeMessage(ctx, 400, "文件大小不能大于10M")
	}
	// 本地文件名
	localFileName := fmt.Sprintf("%s-%s", time.Now().Format("20060102150405"), file.Filename)
	// 将文件保存到服务器本地
	storagePath := cfg.LoadCommon().StoragePath
	if err := ctx.SaveUploadedFile(file, filepath.Join(storagePath, "/app/ip_mapping/", localFileName)); err != nil {
		return err
	}

	// 打开文件
	f, err := file.Open()
	if err != nil {
		return response.FailWithCodeMessage(ctx, 400, "文件打开失败")
	}
	defer f.Close()

	parts := strings.Split(input.Fields, ",")
	if len(parts) < 2 {
		return response.FailWithMessage(ctx, "参数错误")
	}
	fromMapping := parts[0]
	toMapping := parts[1]

	fromMap, ok := services.GetFieldName(fromMapping)
	if !ok {
		return response.FailWithMessage(ctx, "导入映射文件失败，字段不存在")
	}
	toMap, ok := services.GetFieldName(toMapping)
	if !ok {
		return response.FailWithMessage(ctx, "导入映射文件失败，字段不存在")
	}

	internetFields := []map[string]interface{}{
		{
			"type":     "ip",
			"label":    fromMapping + "IP",
			"show_key": fromMap["ip"],
		},
		{
			"type":     "port",
			"label":    fromMapping + "端口",
			"show_key": fromMap["port"],
		},
		{
			"type":     "domain",
			"label":    fromMapping + "域名",
			"show_key": fromMap["domain"],
		},
		{
			"type":     "protocol",
			"label":    fromMapping + "协议",
			"show_key": fromMap["protocol"],
		},
	}
	intranetFields := []map[string]interface{}{
		{
			"type":     "ip",
			"label":    toMapping + "IP",
			"show_key": toMap["ip"],
		},
		{
			"type":     "port",
			"label":    toMapping + "端口",
			"show_key": toMap["port"],
		},
		{
			"type":     "domain",
			"label":    toMapping + "域名",
			"show_key": toMap["domain"],
		},
		{
			"type":     "protocol",
			"label":    toMapping + "协议",
			"show_key": toMap["protocol"],
		},
	}

	fieldNames := []string{
		fromMapping + "IP",
		fromMapping + "端口",
		fromMapping + "域名",
		fromMapping + "协议",
		toMapping + "IP",
		toMapping + "端口",
		toMapping + "域名",
		toMapping + "协议",
	}

	var mappings []map[string]interface{}
	if input.FileType == 1 {
		// 打开文件
		fileContent, err := excelize.OpenReader(f)
		if err != nil {
			return response.FailWithCodeMessage(ctx, 400, "文件打开失败")
		}

		//读取Sheet1的数据
		rows, err := fileContent.GetRows("Sheet1", excelize.Options{RawCellValue: true})
		if err != nil {
			return err
		}
		// GetRows默认会去除末尾的空列，补全空列
		for index, _ := range rows {
			// 补全空值，确保每行有 8 列
			for len(rows[index]) < 8 {
				rows[index] = append(rows[index], "")
			}
		}
		if len(rows) == 0 {
			return response.FailWithCodeMessage(ctx, 400, "文件内容不能为空")
		}
		if len(rows) > 300000 {
			return response.FailWithCodeMessage(ctx, 400, "单次文件同步数量不能大于30w数据")
		}
		mappings, err = services.ImportIpMappingByTemplateConfirm(rows, fromMap, toMap)
	} else if input.FileType == 2 {
		// 打开文件
		scanner := bufio.NewScanner(f)
		lines := make([]string, 0)
		for scanner.Scan() {
			line := strings.TrimSpace(scanner.Text())
			if strings.HasPrefix(line, "nat server") {
				lines = append(lines, line)
			}
		}
		if len(lines) == 0 {
			return response.FailWithCodeMessage(ctx, 400, "不能解析到符合格式的数据")
		}
		// 保存映射数据
		mappings, err = services.ImportIpMappingByHuaweiConfigConfirm(lines, fromMap, toMap)
	} else if input.FileType == 3 {
		// 打开文件
		scanner := bufio.NewScanner(f)
		datas := make(map[string][]string)
		key := ""
		for scanner.Scan() {
			line := strings.TrimSpace(scanner.Text())
			if strings.HasPrefix(line, "slb virtual-server vip_") ||
				strings.HasPrefix(line, "slb template http http-hostswitching") ||
				strings.HasPrefix(line, "slb template http http_host") ||
				strings.HasPrefix(line, "slb service-group") {
				key = line
			}
			if ((strings.HasPrefix(line, "port") ||
				strings.HasPrefix(line, "service-group")) ||
				strings.HasPrefix(line, "template http http_host") ||
				strings.HasPrefix(line, "template http http-hostswitching") ||
				strings.HasPrefix(line, "host-switching") ||
				strings.HasPrefix(line, "member")) && key != "" {
				datas[key] = append(datas[key], line)
			}
			if strings.HasPrefix(line, "!") {
				key = ""
			}
		}
		// 保存映射数据
		mappings, err = services.ImportIpMappingByA10Confirm(datas, fromMap, toMap)
	} else if input.FileType == 4 {
		content, _ := io.ReadAll(f)
		mappings, err = services.ImportIpMappingByFortinetConfirm(string(content), fromMap, toMap)
	} else if input.FileType == 5 {
		// 打开文件
		scanner := bufio.NewScanner(f)
		lines := make([]string, 0)
		for scanner.Scan() {
			line := strings.TrimSpace(scanner.Text())
			if strings.HasPrefix(line, "nat server") {
				lines = append(lines, line)
			}
		}
		if len(lines) == 0 {
			return response.FailWithCodeMessage(ctx, 400, "不能解析到符合格式的数据")
		}
		// 保存映射数据
		mappings, err = services.ImportIpMappingByHuaweiFWConfigConfirm(lines, fromMap, toMap)
	}
	if len(fieldNames) == 0 || len(mappings) == 0 {
		return response.FailWithMessage(ctx, "数据错误")
	}
	return response.OkWithData(ctx, ip_mapping2.ImportConfirmResponse{
		Custom1Fields: internetFields,
		Custom2Fields: intranetFields,
		Items:         mappings,
		FieldsName: map[string]string{
			"custom1_fields": fromMapping,
			"custom2_fields": toMapping,
		},
	})
}

func Delete(ctx *gin.Context) error {
	logger := logs.GetLogger()
	input := &ip_mapping.DeleteRequest{}
	if err := ctx.ShouldBindJSON(input); err != nil {
		return err
	}
	ok, str := validate.Validator(input)
	if !ok {
		return response.FailWithMessage(ctx, str)
	}
	// 删除映射关系
	deletedDatas, err := db.NewIpMappingModel().Delete(ctx, input.Ids, input.Keyword)
	if err != nil {
		logger.Errorf("内外网关系映射文件删除映射关系失败,err:%v", err)
		return response.FailWithCodeMessage(ctx, 400, "删除映射关系失败")
	}
	// 保存审计记录
	batchNo := uuid.New().String()
	auditLog := &db.IpMappingAuditLog{
		BatchNo:         batchNo,
		OperationStatus: "成功",
		Operation:       "删除",
		EffectCount:     len(deletedDatas),
	}
	// 保存审计记录
	err = db.NewIpMappingAuditLogModel().Create(ctx, auditLog)
	if err != nil {
		logger.Errorf("内外网关系映射文件保存审计记录失败,err:%v", err)
		return response.FailWithCodeMessage(ctx, 400, "保存审计记录失败")
	}
	if len(deletedDatas) > 0 {
		// 保存审计数据
		auditDataList := make([]*db.IpMappingAuditData, 0)
		for _, data := range deletedDatas {
			auditData := &db.IpMappingAuditData{
				BatchNo:       batchNo,
				DataId:        data.Id,
				PublicDomain:  data.PublicDomain,
				PublicIp:      data.PublicIp,
				PublicPort:    cast.ToUint32(data.PublicPort),
				PrivateDomain: data.PrivateDomain,
				PrivateIp:     data.PrivateIp,
				PrivatePort:   cast.ToUint32(data.PrivatePort),
			}
			auditDataList = append(auditDataList, auditData)
		}
		err = db.NewIpMappingAuditDataModel().CreateBatch(ctx, auditDataList)
		if err != nil {
			logger.Errorf("删除数据成功,保存审计数据失败,err:%v", err)
			return response.FailWithCodeMessage(ctx, 400, "数据删除成功,保存审计数据失败")
		}
	}
	return response.OkWithData(ctx, fmt.Sprintf("删除成功,删除数量:%d", len(deletedDatas)))
}

func List(ctx *gin.Context) error {
	logger := logs.GetLogger()
	input := &ip_mapping.ListRequest{}
	if err := ctx.ShouldBind(input); err != nil {
		return err
	}
	ok, str := validate.Validator(input)
	if !ok {
		return response.FailWithMessage(ctx, str)
	}

	data, count, err := db.NewIpMappingModel().List(input.Page, input.PerPage, input.Keyword)
	if err != nil {
		logger.Errorf("获取内外网映射关系失败,err:%v", err)
		return response.FailWithCodeMessage(ctx, 400, "获取映射关系失败")
	}
	return response.OkWithData(ctx, gin.H{
		"total":    count,
		"items":    data,
		"page":     input.Page,
		"per_page": input.PerPage,
	})
}

func DownloadTemplate(ctx *gin.Context) error {
	params := &ip_mapping.DownloadTemplateRequest{}
	if err := ctx.ShouldBind(&params); err != nil {
		return response.FailWithMessage(ctx, "参数错误")
	}
	filePath := services.CreateTemplateFile(params.FromMapping, params.ToMapping)
	return response.OkWithFile(ctx, filePath, true)
}

func AuditList(ctx *gin.Context) error {
	input := &ip_mapping.ListRequest{}
	if err := ctx.ShouldBind(input); err != nil {
		return err
	}
	ok, str := validate.Validator(input)
	if !ok {
		return response.FailWithMessage(ctx, str)
	}
	count, data, err := db.NewIpMappingAuditLogModel().List(ctx, input.Page, input.PerPage, "导入")
	if err != nil {
		return response.FailWithMessage(ctx, "获取审计记录失败")
	}
	return response.OkWithData(ctx, gin.H{
		"total":    count,
		"items":    data,
		"page":     input.Page,
		"per_page": input.PerPage,
	})
}

func AuditDetail(ctx *gin.Context) error {
	input := &ip_mapping.AuditDetailRequest{}
	if err := ctx.ShouldBind(input); err != nil {
		return err
	}
	ok, str := validate.Validator(input)
	if !ok {
		return response.FailWithMessage(ctx, str)
	}
	count, data, err := db.NewIpMappingAuditDataModel().List(ctx, input.BatchNo, input.Page, input.PerPage)
	if err != nil {
		return response.FailWithMessage(ctx, "获取审计数据失败")
	}
	return response.OkWithData(ctx, gin.H{
		"total":    count,
		"items":    data,
		"page":     input.Page,
		"per_page": input.PerPage,
	})
}

func CheckIpMappingAlarm(ctx *gin.Context) error {
	handleMsg := ""
	batchNo := uuid.New().String()
	audit := &db.IpMappingStatisticsAudit{
		BatchNo:      batchNo,
		TriggerType:  "自动",
		TriggerTime:  localtime.NewLocalTime(time.Now()),
		HandleStatus: "处理中",
	}
	// 开始处理
	err := services.GetIpMappingForAlarm(batchNo)
	if err != nil {
		handleMsg = fmt.Sprintf("报警信息更新失败,err:%v", err)
		audit.HandleStatus = "失败"
		audit.HandleMessage = handleMsg
	} else {
		audit.HandleStatus = "成功"
	}

	audit.EndTime = localtime.NewLocalTime(time.Now())
	// 记录报警操作审计
	err = db.NewIpMappingStatisticsAuditModel().Create(ctx, audit)
	if err != nil {
		if handleMsg == "" {
			handleMsg = "数据处理成功；记录报警操作审计失败"
		} else {
			handleMsg = fmt.Sprintf("%s；记录报警操作审计失败", handleMsg)
		}
		return response.FailWithMessage(ctx, handleMsg)
	}

	if handleMsg != "" {
		return response.FailWithMessage(ctx, handleMsg)
	}
	return response.OkWithData(ctx, "报警信息更新成功")
}

func AlarmList(ctx *gin.Context) error {
	input := &ip_mapping.ListRequest{}
	if err := ctx.ShouldBind(input); err != nil {
		return err
	}
	audit, err := db.NewIpMappingStatisticsAuditModel().Last(ctx)
	if err != nil {
		if mysql.IsNotFound(err) {
			//return response.FailWithMessage(ctx, "没有报警信息")
			audit = &db.IpMappingStatisticsAudit{
				BatchNo: "",
			}
		} else {
			return response.FailWithMessage(ctx, "获取报警信息失败")
		}
	}
	paramList, err := utils.StructToMap(input, "json")
	if err != nil {
		return err
	}

	count, data, err := db.NewIpMappingStatisticsModel().List(ctx, input.Page, input.PerPage, input.Keyword, audit.BatchNo, paramList)
	if err != nil {
		return response.FailWithMessage(ctx, "获取报警信息失败")
	}
	return response.OkWithData(ctx, gin.H{
		"total":    count,
		"items":    data,
		"page":     input.Page,
		"per_page": input.PerPage,
	})
}

func ExportAlarmList(ctx *gin.Context) error {
	input := &ip_mapping.ExportRequest{}
	if err := ctx.ShouldBind(input); err != nil {
		return err
	}
	ok, str := validate.Validator(input)
	if !ok {
		return response.FailWithMessage(ctx, str)
	}

	paramList, err := utils.StructToMap(input, "json")
	if err != nil {
		return err
	}

	filePath, err := services.ExportAlarmList(ctx, input.Ids, input.Keyword, paramList)
	if err != nil {
		return response.FailWithMessage(ctx, "导出报警信息失败")
	}
	return response.OkWithFile(ctx, filePath, true)
}
