package ip_mapping

import (
	"bytes"
	"encoding/json"
	ip_mapping2 "fobrain/fobrain/app/request/ip_mapping"
	"fobrain/fobrain/app/response/ip_mapping"
	services "fobrain/fobrain/app/services/ip_mapping"
	"fobrain/initialize/mysql"
	"fobrain/models/mysql/custom_field"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"net/http/httptest"
	"testing"
)

func TestListCustomFields(t *testing.T) {
	patches := []*gomonkey.Patches{
		gomonkey.ApplyFuncReturn(services.ListCustomFieldGroups, []custom_field.CustomFieldGroup{
			{
				BaseModel: mysql.BaseModel{Id: 1},
				Name:      "DMZ",
				Table:     "ip_mapping",
				Fields:    "",
				Sort:      1,
			},
		}, nil),
	}

	w := httptest.NewRecorder()
	// 发送JSON数据
	req := httptest.NewRequest("GET", "/api/v1/ip-mapping/custom_fields", nil)

	c, _ := gin.CreateTestContext(w)
	c.Request = req

	err := ListCustomFields(c)
	for _, patch := range patches {
		patch.Reset()
	}
	assert.Nil(t, err)
}

func TestListCustomFieldsDetail(t *testing.T) {
	patches := []*gomonkey.Patches{
		gomonkey.ApplyFuncReturn(services.ListCustomFields, ip_mapping.ListCustomFieldsDetailResponse{
			Fields: []map[string]interface{}{
				{
					"field1": "公网",
				},
			},
		}, nil),
	}

	w := httptest.NewRecorder()
	// 发送JSON数据
	req := httptest.NewRequest("GET", "/api/v1/ip-mapping/custom_fields/detail", nil)

	c, _ := gin.CreateTestContext(w)
	c.Request = req

	err := ListCustomFieldsDetail(c)
	for _, patch := range patches {
		patch.Reset()
	}
	assert.Nil(t, err)
}

func TestListCustomFieldCombination(t *testing.T) {
	patches := []*gomonkey.Patches{
		gomonkey.ApplyFuncReturn(services.ListCustomFieldCombination, ip_mapping.ListCustomFieldCombinationResponse{
			FieldCombinations: [][]string{
				{"公网", "内网"},
			},
		}, nil),
	}

	w := httptest.NewRecorder()
	// 发送JSON数据
	req := httptest.NewRequest("GET", "/api/v1/ip-mapping/custom_fields/combination", nil)

	c, _ := gin.CreateTestContext(w)
	c.Request = req

	err := ListCustomFieldCombination(c)
	for _, patch := range patches {
		patch.Reset()
	}
	assert.Nil(t, err)
}

func TestCreateOrUpdateCustomFields(t *testing.T) {
	patches := []*gomonkey.Patches{
		gomonkey.ApplyFuncReturn(services.DeleteCustomFieldGroup, nil),
		gomonkey.ApplyFuncReturn(services.UpdateCustomFieldGroup, nil),
		gomonkey.ApplyFuncReturn(services.CreateCustomFieldGroup, nil),
	}
	input := ip_mapping2.CreateOrUpdateCustomFieldsRequest{
		Fields: []struct {
			Id   uint64 `json:"id"`
			Name string `json:"name"`
			Sort int    `json:"sort"`
		}{
			{
				Id:   uint64(1),
				Name: "公网",
				Sort: 1,
			},
			{
				Id:   uint64(2),
				Name: "内网",
				Sort: 100,
			},
			{
				Id:   uint64(3),
				Name: "DMZ",
				Sort: 2,
			},
			{
				Id:   uint64(0),
				Name: "虚拟",
				Sort: 3,
			},
		},
	}

	body, _ := json.Marshal(input)
	w := httptest.NewRecorder()
	// 发送JSON数据
	req := httptest.NewRequest("PUT", "/api/v1/ip-mapping/custom_fields", bytes.NewReader(body))

	c, _ := gin.CreateTestContext(w)
	c.Request = req

	err := CreateOrUpdateCustomFields(c)
	for _, patch := range patches {
		patch.Reset()
	}
	assert.Nil(t, err)
}

func TestListStatisticCustomFields(t *testing.T) {
	patches := []*gomonkey.Patches{
		gomonkey.ApplyFuncReturn(services.ListCustomFieldGroups, []custom_field.CustomFieldGroup{
			{
				BaseModel: mysql.BaseModel{Id: 1},
				Name:      "DMZ",
				Table:     "ip_mapping",
				Fields:    "",
				Sort:      1,
			},
		}, nil),
	}

	w := httptest.NewRecorder()
	// 发送JSON数据
	req := httptest.NewRequest("GET", "/api/v1/ip-mapping/statistic_custom_fields", nil)

	c, _ := gin.CreateTestContext(w)
	c.Request = req

	err := ListStatisticCustomFields(c)
	for _, patch := range patches {
		patch.Reset()
	}
	assert.Nil(t, err)
}

func TestListStatisticCustomFieldsDetail(t *testing.T) {
	patches := []*gomonkey.Patches{
		gomonkey.ApplyFuncReturn(services.ListCustomFields, ip_mapping.ListCustomFieldsDetailResponse{
			Fields: []map[string]interface{}{
				{
					"field1": "公网",
				},
			},
		}, nil),
	}

	w := httptest.NewRecorder()
	// 发送JSON数据
	req := httptest.NewRequest("GET", "/api/v1/ip-mapping/statistic_custom_fields/detail", nil)

	c, _ := gin.CreateTestContext(w)
	c.Request = req

	err := ListStatisticCustomFieldsDetail(c)
	for _, patch := range patches {
		patch.Reset()
	}
	assert.Nil(t, err)
}
