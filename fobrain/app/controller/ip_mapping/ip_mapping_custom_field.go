package ip_mapping

import (
	"fobrain/fobrain/app/request/ip_mapping"
	ip_mapping2 "fobrain/fobrain/app/response/ip_mapping"
	services "fobrain/fobrain/app/services/ip_mapping"
	"fobrain/fobrain/common/request"
	"fobrain/fobrain/common/response"
	"github.com/gin-gonic/gin"
)

func ListCustomFields(ctx *gin.Context) error {
	list, err := services.ListCustomFieldGroups("ip_mapping")
	if err != nil {
		return response.FailWithMessage(ctx, "获取自定义字段失败")
	}
	var res []ip_mapping2.ListCustomFieldsResponse
	for _, value := range list {
		res = append(res, ip_mapping2.ListCustomFieldsResponse{
			Id:   value.Id,
			Name: value.Name,
		})
	}
	return response.OkWithData(ctx, res)
}

func ListCustomFieldsDetail(ctx *gin.Context) error {
	listCustomFieldsDetailResponse, err := services.ListCustomFields("ip_mapping")
	if err != nil {
		return err
	}
	return response.OkWithData(ctx, listCustomFieldsDetailResponse)
}

func ListCustomFieldCombination(ctx *gin.Context) error {
	listCustomFieldCombinationResponse, err := services.ListCustomFieldCombination("ip_mapping")
	if err != nil {
		return err
	}
	return response.OkWithData(ctx, listCustomFieldCombinationResponse)
}

func CreateOrUpdateCustomFields(ctx *gin.Context) error {
	params, err := request.Validate(ctx, &ip_mapping.CreateOrUpdateCustomFieldsRequest{})
	if err != nil {
		return err
	}

	var newFields []struct {
		Id   uint64 `json:"id"`
		Name string `json:"name"`
		Sort int    `json:"sort"`
	}

	for _, field := range params.Fields {
		if field.Id > 0 && field.Name == "" {
			err = services.DeleteCustomFieldGroup(field.Id)
			if err != nil {
				return err
			}
		} else {
			newFields = append(newFields, field)
		}
	}

	if len(newFields) > 10 {
		return response.FailWithMessage(ctx, "自定义字段数必须小于10")
	}

	for _, field := range newFields {
		if field.Id > 0 {
			err := services.UpdateCustomFieldGroup(field.Id, field.Name, field.Sort)
			if err != nil {
				return response.FailWithMessage(ctx, err.Error())
			}
		} else if field.Name != "" {
			err := services.CreateCustomFieldGroup(field.Name, field.Sort)
			if err != nil {
				return response.FailWithMessage(ctx, err.Error())
			}
		}
	}

	return response.OkWithMessage(ctx, "更新成功")
}

func ListStatisticCustomFields(ctx *gin.Context) error {
	list, err := services.ListCustomFieldGroups("ip_mapping_statistics")
	if err != nil {
		return response.FailWithMessage(ctx, "获取自定义字段失败")
	}
	var res []ip_mapping2.ListCustomFieldsResponse
	for _, value := range list {
		res = append(res, ip_mapping2.ListCustomFieldsResponse{
			Id:   value.Id,
			Name: value.Name,
		})
	}
	return response.OkWithData(ctx, res)
}

func ListStatisticCustomFieldsDetail(ctx *gin.Context) error {
	listCustomFieldsDetailResponse, err := services.ListCustomFields("ip_mapping_statistics")
	if err != nil {
		return err
	}
	return response.OkWithData(ctx, listCustomFieldsDetailResponse)
}
