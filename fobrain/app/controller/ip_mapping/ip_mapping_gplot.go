package ip_mapping

import (
	"fobrain/fobrain/app/request/ip_mapping"
	ip_mapping2 "fobrain/fobrain/app/services/ip_mapping"
	"fobrain/fobrain/common/response"
	"github.com/gin-gonic/gin"
)

func GetMappingGplot(c *gin.Context) error {
	params := &ip_mapping.GetMappingGplotRequest{}
	if err := c.ShouldBind(params); err != nil {
		return response.FailWithMessage(c, "参数错误")
	}
	if params.Keyword == "" {
		return response.FailWithMessage(c, "请输入查询参数")
	}
	res, err := ip_mapping2.GetMappingGplotResponse(params)
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	return response.OkWithData(c, res)
}

func GetMappingAssets(c *gin.Context) error {
	params := &ip_mapping.GetMappingAssetsRequest{}
	if err := c.ShouldBind(params); err != nil {
		return response.FailWithMessage(c, "参数错误")
	}
	if params.Keyword == "" {
		return response.FailWithMessage(c, "请输入查询参数")
	}

	res, total, err := ip_mapping2.GetMappingGplotAssets(params.Keyword, params.Page, params.PerPage)
	if err != nil {
		return response.FailWithMessage(c, "查询失败")
	}
	return response.OkWithPageData(c, total, params.Page, params.PerPage, res)
}

func GetMappingBaseInfo(c *gin.Context) error {
	params := &ip_mapping.GetMappingBaseInfoRequest{}
	if err := c.ShouldBind(params); err != nil {
		return response.FailWithMessage(c, "参数错误")
	}
	if params.Keyword == "" {
		return response.FailWithMessage(c, "请输入查询参数")
	}

	res := ip_mapping2.GetMappingBaseInfoResponse(params)
	if res == nil {
		return response.FailWithMessage(c, "无资产数据")
	}
	return response.OkWithData(c, res)
}
