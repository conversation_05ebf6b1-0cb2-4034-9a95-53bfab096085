package account_4a

import (
	"encoding/xml"
	"fmt"
	requestPermission "fobrain/fobrain/app/request/permission"
	"fobrain/fobrain/app/services/permission"
	permission2 "fobrain/models/mysql/permission"
	"fobrain/models/mysql/system_configs"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"

	"fobrain/initialize/mysql"
	"fobrain/models/elastic/staff"
	"fobrain/models/mysql/user"
)

// AccountAccId
//
//	@Summary: 4A 返回逻辑
type AccountAccId struct {
	XMLName xml.Name `xml:"account"`
	AccID   string   `xml:"accId"`
}

type Accounts struct {
	XMLName xml.Name       `xml:"accounts"`
	Items   []AccountAccId `xml:"account"`
}

type RequestBody struct {
	UserId string `xml:"userId"`
}

// AccountFind
//
//	@Summary: 4A 从账号查询
func AccountFind(c *gin.Context) {
	var requestBody RequestBody

	// 解析 XML 请求体
	if err := c.ShouldBindXML(&requestBody); err != nil {
		c.XML(http.StatusOK, []Accounts{
			{
				Items: []AccountAccId{},
			},
		})
		return
	}

	// 获取 userId
	userId := requestBody.UserId
	if userId == "" {
		c.XML(http.StatusOK, []Accounts{
			{
				Items: []AccountAccId{},
			},
		})
		return
	}

	userInfo, err := user.NewUserModel().First(mysql.WithWhere("account", userId))
	if err != nil {
		c.XML(http.StatusOK, []Accounts{
			{
				Items: []AccountAccId{},
			},
		})
		return
	}

	c.XML(http.StatusOK, []Accounts{
		{
			Items: []AccountAccId{
				{
					AccID: userInfo.Account,
				},
			},
		},
	})

	return
}

type Account struct {
	AccId                    string `xml:"accId"`
	UserPassword             string `xml:"userPassword"`
	Name                     string `xml:"name"`
	Sn                       string `xml:"sn"`
	Description              string `xml:"description"`
	Email                    string `xml:"email"`
	Nation                   string `xml:"nation"`
	Gender                   string `xml:"gender"`
	Birthday                 string `xml:"birthday"`
	C                        string `xml:"c"`
	Religion                 string `xml:"religion"`
	TelephoneNumber          string `xml:"telephoneNumber"`
	Mobile                   string `xml:"mobile"`
	PreferredMobile          string `xml:"preferredMobile"`
	PostalAddress            string `xml:"postalAddress"`
	PostalCode               string `xml:"postalCode"`
	FacsimileTelephoneNumber string `xml:"facsimileTelephoneNumber"`
	StartTime                string `xml:"startTime"`
	EndTime                  string `xml:"endTime"`
	Status                   string `xml:"status"`
	PasswordModifiedDate     string `xml:"passwordModifiedDate"`
	IdCardNumber             string `xml:"idCardNumber"`
	MemberOf                 string `xml:"memberOf"`
	EmployeeNumber           string `xml:"employeeNumber"`
	Level                    string `xml:"level"`
	LevelName                string `xml:"levelName"`
	Category                 string `xml:"category"`
	Function                 string `xml:"function"`
	DisplayOrder             string `xml:"displayOrder"`
	EntryTime                string `xml:"entryTime"`
	O                        string `xml:"o"`
	WorkOrg                  string `xml:"workOrg"`
	Duty                     string `xml:"duty"`
	PositionLevel            string `xml:"positionLevel"`
	EmployeeType             string `xml:"employeeType"`
	L                        string `xml:"L"`
	SupporterCorpName        string `xml:"supporterCorpName"`
	SupporterDept            string `xml:"supporterDept"`
	SupporterCorpContact     string `xml:"supporterCorpContact"`
	SuperviseDept            string `xml:"superviseDept"`
	Supervisor               string `xml:"supervisor"`
	OrgLevel                 string `xml:"orgLevel"`
}

// AddUserInfoRequest 用于解析请求参数
type AddUserInfoRequest struct {
	XMLName  xml.Name  `xml:"accounts"`
	Accounts []Account `xml:"account"`
}

// ResultItem 单个结果项
type ResultItem struct {
	XMLName    xml.Name `xml:"result"`
	ReturnCode string   `xml:"returncode,attr"`
	AccIds     []string `xml:"accId"`
}

// WebServiceResponse 返回结果结构
type WebServiceResponse struct {
	XMLName  xml.Name     `xml:"results"`
	Results  []ResultItem `xml:"result"`
	ErrorMsg string       `xml:"errorMsg"`
}

// AccountAdd
// @Summary: 4A 账号同步逻辑
func AccountAdd(c *gin.Context) {
	// 读取 XML 请求体
	body, err := c.GetRawData()
	if err != nil {
		c.XML(http.StatusBadRequest, WebServiceResponse{
			ErrorMsg: "Invalid request body",
			Results: []ResultItem{
				{
					ReturnCode: "1301",
					AccIds:     []string{},
				},
			},
		})
		return
	}

	// 解析 XML
	var request AddUserInfoRequest
	if err := xml.Unmarshal(body, &request); err != nil {
		c.XML(http.StatusBadRequest, WebServiceResponse{
			ErrorMsg: "Failed to parse XML",
			Results: []ResultItem{
				{
					ReturnCode: "1301",
					AccIds:     []string{},
				},
			},
		})
		return
	}

	errs := []string{}

	// 模拟处理逻辑
	var results []ResultItem
	for _, account := range request.Accounts {
		staffs, err := staff.NewStaff().FindByWorkNumber(account.EmployeeNumber, "fid_hash", "work_number")
		if err != nil {
			errs = append(errs, err.Error())
			results = append(results, ResultItem{
				ReturnCode: "1301",
				AccIds:     []string{account.AccId},
			})
		}

		StaffIds := []string{}
		for _, s := range staffs {
			StaffIds = append(StaffIds, fmt.Sprintf("%v", s.Id))
		}

		password := ("Admin@D01!4A" + uuid.New().String())[:6]
		s := permission.NewPermissionService()
		_, err = s.AddUser(c, requestPermission.AddUserRequest{
			Account:  account.AccId,
			Username: account.Name,
			Password: password,
			Roles:    []uint64{system_configs.SSORole()},
			StaffId:  StaffIds,
			DataPermission: permission2.DataPermission{
				PrivateAndPublicSame: true,
				NoMaster:             false,
				Private: permission2.DataPermissionItem{
					DataLevel: []int8{int8(permission2.DataPermissionLevelAssigned)},
				},
				Public: permission2.DataPermissionItem{
					DataLevel: []int8{int8(permission2.DataPermissionLevelAssigned)},
				},
			},
		})

		if err != nil {
			errs = append(errs, err.Error())
			results = append(results, ResultItem{
				ReturnCode: "1301",
				AccIds:     []string{account.AccId},
			})
		} else {
			results = append(results, ResultItem{
				ReturnCode: "1300",
				AccIds:     []string{account.AccId},
			})
		}
	}

	// 模拟错误信息
	var errorMsg string
	if len(errs) > 0 {
		errorMsg = strings.Join(errs, ",")
	}

	// 返回符合要求的响应
	c.XML(http.StatusOK, WebServiceResponse{
		Results:  results,
		ErrorMsg: errorMsg,
	})
}
