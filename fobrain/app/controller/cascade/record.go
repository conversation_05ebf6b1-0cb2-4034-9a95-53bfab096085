package cascade

import (
	"context"
	"fobrain/fobrain/app/request/cascade"
	cascade2 "fobrain/fobrain/app/response/cascade"
	"fobrain/fobrain/common/request"
	"fobrain/fobrain/common/response"
	"fobrain/fobrain/common/validate"
	"fobrain/initialize/es"
	"fobrain/initialize/mysql"
	cascade_model "fobrain/models/elastic/cascade"
	"fobrain/models/mysql/cascade_sync_record"
	models "fobrain/module/cascade/models/data_capture_nodes"
	"fobrain/pkg/utils"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/olivere/elastic/v7"
)

func ListCascadeSyncRecord(c *gin.Context) error {
	params, err := request.Validate(c, &cascade.ListCascadeSyncRecordRequest{})
	if err != nil {
		return err
	}
	handleFuncs := []mysql.HandleFunc{}
	if params.Id > 0 {
		handleFuncs = append(handleFuncs, mysql.WithWhere("`node_id` = ?", params.Id))
	}
	if params.Keyword != "" {
		if params.Keyword == "成功" {
			handleFuncs = append(handleFuncs, mysql.WithWhere("`status` = ?", cascade_sync_record.Success))
		} else if params.Keyword == "失败" {
			handleFuncs = append(handleFuncs, mysql.WithWhere("`status` = ?", cascade_sync_record.Fail))
		} else if params.Keyword == "同步中" {
			handleFuncs = append(handleFuncs, mysql.WithWhere("`status` = ?", cascade_sync_record.Waiting))
		} else {
			handleFuncs = append(handleFuncs, mysql.WithWhere("`tag` = ?", params.Keyword))
		}
	}
	cascadeSyncRecords, count, err := cascade_sync_record.NewCascadeSyncRecord().
		ListByPage(params.Page, params.PerPage, handleFuncs...)
	if err != nil {
		return err
	}
	var responseData []cascade2.ListCascadeSyncRecordResponse
	for _, cascadeSyncRecord := range cascadeSyncRecords {
		status := cascadeSyncRecord.Status
		if cascadeSyncRecord.Status == cascade_sync_record.Waiting && cascadeSyncRecord.Total > 0 {
			if cascadeSyncRecord.SuccessCount+cascadeSyncRecord.FailCount < cascadeSyncRecord.Total {
				if cascadeSyncRecord.EndTime.Sub(cascadeSyncRecord.StartTime) < 600*time.Second {
					status = cascade_sync_record.Waiting
				} else {
					status = cascade_sync_record.Fail
				}
			} else {
				if cascadeSyncRecord.FailCount > 0 {
					status = cascade_sync_record.Fail
				} else {
					status = cascade_sync_record.Success
				}
			}
			err := cascade_sync_record.NewCascadeSyncRecord().Update(&cascade_sync_record.CascadeSyncRecord{BaseModel: mysql.BaseModel{Id: cascadeSyncRecord.Id}, Status: status})
			if err != nil {
				return response.FailWithMessage(c, "更新记录失败")
			}
		}
		dataCaptureNode, err := models.NewDataCaptureNodesModel().First(mysql.WithWhere("`id` = ?", cascadeSyncRecord.NodeId))
		if err != nil {
			continue
		}
		listCascadeSyncRecordResponse := cascade2.ListCascadeSyncRecordResponse{
			Id:           cascadeSyncRecord.Id,
			BranchName:   dataCaptureNode.Name,
			Status:       status,
			SyncDuration: "",
			StartTime:    cascadeSyncRecord.StartTime,
			EndTime:      cascadeSyncRecord.EndTime,
			NodeId:       cascadeSyncRecord.NodeId,
			Tag:          cascadeSyncRecord.Tag,
			SuccessCount: cascadeSyncRecord.SuccessCount,
			FailCount:    cascadeSyncRecord.FailCount,
			Total:        cascadeSyncRecord.Total,
		}
		if cascadeSyncRecord.EndTime != nil {
			duration := cascadeSyncRecord.EndTime.Sub(cascadeSyncRecord.StartTime)
			listCascadeSyncRecordResponse.SyncDuration = duration.String()
		}
		responseData = append(responseData, listCascadeSyncRecordResponse)
	}
	return response.OkWithPageData(c, count, params.Page, params.PerPage, responseData)
}

func ListCascadeTaskAsset(c *gin.Context) error {
	param := &cascade.ListCascadeTaskRequest{}
	if err := c.ShouldBind(param); err != nil {
		return response.FailWithMessage(c, "参数错误")
	}
	if ok, msg := validate.Validator(param); !ok {
		return response.FailWithMessage(c, msg)
	}
	query := elastic.NewBoolQuery()
	if param.Keyword != "" {
		query.Must(elastic.NewMatchQuery("ip.keyword", param.Keyword))
	} else {
		query.Must(elastic.NewMatchAllQuery())
	}
	if param.RecordId > 0 {
		query.Must(elastic.NewTermQuery("record_id", param.RecordId))
	}

	count, err := es.GetCount(cascade_model.NewCascadeTaskAsset().IndexName(), query)
	if err != nil {
		return response.FailWithMessage(c, "级联资产统计失败")
	}

	if count > 0 {
		res, err := es.GetEsClient().Search(cascade_model.NewCascadeTaskAsset().IndexName()).From(es.GetFrom(param.Page, param.PerPage)).
			Size(es.GetSize(param.PerPage)).
			Query(query).
			Do(context.Background())
		if err != nil {
			return response.FailWithMessage(c, "查询失败")
		}
		hits := res.Hits.Hits
		list := make([]any, 0, len(hits))
		for _, hit := range hits {
			asset := utils.ParseSampleHash(hit, &cascade_model.CascadeTaskAsset{})
			list = append(list, asset)
		}
		return response.OkWithPageData(c, count, param.Page, param.PerPage, list)
	}
	return response.OkWithPageData(c, 0, param.Page, param.PerPage, []any{})
}

func ListCascadeTaskVuln(c *gin.Context) error {
	param := &cascade.ListCascadeTaskRequest{}
	if err := c.ShouldBind(param); err != nil {
		return response.FailWithMessage(c, "参数错误")
	}
	if ok, msg := validate.Validator(param); !ok {
		return response.FailWithMessage(c, msg)
	}

	query := elastic.NewBoolQuery()
	if param.Keyword != "" {
		query = cascade_model.NewCascadeTaskPoc().NewKeywordQuery(param.Keyword, query)
	} else {
		query.Must(elastic.NewMatchAllQuery())
	}
	if param.RecordId > 0 {
		query.Must(elastic.NewTermQuery("record_id", param.RecordId))
	}

	count, err := es.GetCount(cascade_model.NewCascadeTaskPoc().IndexName(), query)
	if err != nil {
		return response.FailWithMessage(c, "级联漏洞统计失败")
	}

	if count > 0 {
		res, err := es.GetEsClient().Search(cascade_model.NewCascadeTaskPoc().IndexName()).From(es.GetFrom(param.Page, param.PerPage)).
			Size(es.GetSize(param.PerPage)).
			Query(query).
			Do(context.Background())
		if err != nil {
			return response.FailWithMessage(c, "查询失败")
		}
		hits := res.Hits.Hits
		list := make([]any, 0, len(hits))
		for _, hit := range hits {
			asset := utils.ParseSampleHash(hit, &cascade_model.CascadeTaskPoc{})
			list = append(list, asset)
		}
		return response.OkWithPageData(c, count, param.Page, param.PerPage, list)
	}
	return response.OkWithPageData(c, 0, param.Page, param.PerPage, []any{})
}
