package cascade

import (
	"bytes"
	"encoding/json"
	"io/ioutil"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"

	"fobrain/fobrain/app/request/cascade"
	cascade_service "fobrain/fobrain/app/services/cascade"
	cascade_upgrade_service "fobrain/fobrain/app/services/upgrade"
	"fobrain/models/mysql/cascade_upgrade"
	"fobrain/models/mysql/system_configs"
	"fobrain/pkg/cfg"
)

func TestListUpgradePackage(t *testing.T) {
	list := []cascade_upgrade.CascadeUpgradePackage{
		{
			PackageType: 1,
			TaskType:    2,
		},
	}
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyMethodReturn(cascade_upgrade.NewCascadeUpgradePackageModel(), "List", list, int64(1), nil).Reset()

	// 创建一个测试请求和响应
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/upgrade?page=1&per_page=10", nil)
	req.Header.Set("Content-Type", "application/json")
	// 创建一个Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 调用函数
	err := ListUpgradePackage(c)
	assert.NoError(t, err)

	// 解析响应数据
	response := w.Result()
	defer response.Body.Close()
	body, _ := ioutil.ReadAll(response.Body)
	assert.NotEmpty(t, body)
}

func TestUpgradePackageDistribute(t *testing.T) {
	param := &cascade.UpgradePackageDistributeRequest{
		Id:         1,
		PlatformId: 1,
	}
	marshal, _ := json.Marshal(param)

	info := cascade_upgrade.CascadeUpgradePackage{
		PackageType: 1,
		TaskType:    2,
		PackageName: "aaa",
		PackagePath: "aaa",
		Version:     "aa",
		PackageHash: "dfadsfafad",
	}
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyMethodReturn(cascade_upgrade.NewCascadeUpgradePackageModel(), "First", info, nil).Reset()
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyMethodReturn(cascade_upgrade.NewCascadeUpgradePackageModel(), "Update", nil).Reset()
	// 创建一个gomonkey对象
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyFunc(cascade_service.TriggerNewUpgrade, func() {

		return
	}).Reset()

	// 创建一个测试请求和响应
	w := httptest.NewRecorder()
	req := httptest.NewRequest("POST", "/api/v1/upgrade/distribute", bytes.NewReader(marshal))
	req.Header.Set("Content-Type", "application/json")
	// 创建一个Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 调用函数
	err := UpgradePackageDistribute(c)
	assert.NoError(t, err)

	// 解析响应数据
	response := w.Result()
	defer response.Body.Close()
	body, _ := ioutil.ReadAll(response.Body)
	assert.NotEmpty(t, body)
}

func TestListSubPlatformUpgradeRecord(t *testing.T) {
	list := []cascade_upgrade.CascadeUpgradeRecord{
		{
			PackageType: 1,
			TaskType:    2,
		},
	}
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyMethodReturn(cascade_upgrade.NewCascadeUpgradeRecordModel(), "List", list, int64(1), nil).Reset()

	// 创建一个测试请求和响应
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/upgrade/sub_platform?page=1&per_page=10", nil)
	req.Header.Set("Content-Type", "application/json")
	// 创建一个Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 调用函数
	err := ListSubPlatformUpgradeRecord(c)
	assert.NoError(t, err)

	// 解析响应数据
	response := w.Result()
	defer response.Body.Close()
	body, _ := ioutil.ReadAll(response.Body)
	assert.NotEmpty(t, body)
}

func TestUpgradePackageSubPlatformVerify(t *testing.T) {

	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyFuncReturn(cascade_upgrade_service.UpgradePackageSubPlatformVerify, nil).Reset()

	// 创建一个测试请求和响应
	w := httptest.NewRecorder()
	req := httptest.NewRequest("POST", "/api/v1/upgrade/sub_platform/verify", strings.NewReader(`{"id":"1"}`))
	req.Header.Set("Content-Type", "application/json")
	// 创建一个Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 调用函数
	err := UpgradePackageSubPlatformVerify(c)
	assert.NoError(t, err)

	// 解析响应数据
	response := w.Result()
	defer response.Body.Close()
	body, _ := ioutil.ReadAll(response.Body)
	assert.NotEmpty(t, body)
}

func TestUpgradeSubPlatform(t *testing.T) {

	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyFuncReturn(cascade_upgrade_service.UpgradeSubPlatform, nil).Reset()

	// 创建一个测试请求和响应
	w := httptest.NewRecorder()
	req := httptest.NewRequest("POST", "/api/v1/upgrade/sub_platform", strings.NewReader(`{"id":"1"}`))
	req.Header.Set("Content-Type", "application/json")
	// 创建一个Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 调用函数
	err := UpgradeSubPlatform(c)
	assert.NoError(t, err)

	// 解析响应数据
	response := w.Result()
	defer response.Body.Close()
	body, _ := ioutil.ReadAll(response.Body)
	assert.NotEmpty(t, body)
}

func TestDownloadUpgradePackage(t *testing.T) {

	records := &cascade_upgrade.CascadeUpgradeRecord{
		NodeId:      1,
		TaskType:    1,
		PackageType: 1,
		PackageName: "aa",
		PackagePath: "aa",
		Version:     "aa",
		DownloadUrl: "xxx.ba.com",
		PackageHash: "ddd",
	}
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyMethodReturn(cascade_upgrade.NewCascadeUpgradeRecordModel(), "First", records, nil).Reset()

	// 创建一个gomonkey对象
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyFunc(cascade_service.TriggerGenerateDownloadToken, func() {
		return
	}).Reset()
	// 创建一个测试请求和响应
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/upgrade/sub_platform/download?id=1", nil)
	req.Header.Set("Content-Type", "application/json")
	// 创建一个Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 调用函数
	err := DownloadUpgradePackage(c)
	assert.NoError(t, err)

	// 解析响应数据
	response := w.Result()
	defer response.Body.Close()
	body, _ := ioutil.ReadAll(response.Body)
	assert.NotEmpty(t, body)
}

func TestGetDownloadUrl_FromDB(t *testing.T) {
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyFuncReturn(cfg.LoadCommon, nil).Reset()
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyMethodReturn(&system_configs.SystemConfigs{}, "GetConfig", "http://127.0.0.1:8080/upgrade/", nil).Reset()

	url, err := getDownloadUrl()
	assert.NoError(t, err)
	assert.Equal(t, "http://127.0.0.1:8080/upgrade/", url)
}
