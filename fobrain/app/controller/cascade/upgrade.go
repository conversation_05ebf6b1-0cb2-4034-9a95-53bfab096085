package cascade

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"path/filepath"
	"regexp"
	"runtime"
	"strconv"
	"strings"
	"time"

	cascade2 "fobrain/fobrain/app/response/cascade"
	"fobrain/fobrain/common/localtime"
	"fobrain/fobrain/common/request"
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/system_configs"

	"fobrain/fobrain/app/request/cascade"
	cascade_service "fobrain/fobrain/app/services/cascade"
	cascade_upgrade_service "fobrain/fobrain/app/services/upgrade"
	"fobrain/fobrain/common/response"
	"fobrain/initialize/mysql"
	"fobrain/initialize/redis"
	"fobrain/models/mysql/cascade_upgrade"
	redis_helper "fobrain/models/redis"
	"fobrain/module/cascade/sdk"
	"fobrain/pkg/cfg"
	"fobrain/pkg/utils"

	"github.com/gin-gonic/gin"
)

// ListUpgradePackage 上级获取升级包列表
func ListUpgradePackage(c *gin.Context) error {
	param := &cascade.ListUpgradeRequest{}
	if err := c.ShouldBind(param); err != nil {
		return response.FailWithMessage(c, "参数错误")
	}
	list := make([]cascade.List, 0)
	data, count, err := cascade_upgrade.NewCascadeUpgradePackageModel().List(param.Page, param.PerPage, param.Keyword, param.TaskType)
	if err != nil {
		return err
	}
	for _, datum := range data {
		packageName := ""
		// 1:fobrain升级包 2:x-ray规则库升级包 3:d01规则库升级包
		switch datum.PackageType {
		case 1:
			packageName = "fobrain系统升级包"
		case 2:
			packageName = "x-ray漏洞库升级包"
		case 3:
			packageName = "d01漏洞库升级包"
		case 4:
			packageName = "x-ray引擎升级包"
		}
		downloadCount := 0
		cascadeUpgradeDownload, err := cascade_upgrade.NewCascadeUpgradeDownload().First(mysql.WithWhere("`id` = ?", datum.Id))
		if err == nil {
			downloadCount = cascadeUpgradeDownload.DownloadCount
		}
		arch := getArchFromFilename(datum.PackageName)
		list = append(list, cascade.List{
			Id:                          datum.Id,
			TaskType:                    packageName,
			TaskTypeId:                  datum.TaskType, // 任务类型 TODO:任务类型已经需要生成记录
			Filename:                    datum.PackageName,
			Version:                     datum.Version,
			UploadTime:                  datum.CreatedAt,
			DistributeStatus:            datum.DistributeStatus,
			DistributeTime:              datum.DistributeTime,
			SubplatformDownloadProgress: fmt.Sprintf("%d/%d", downloadCount, datum.DistributeTotalCount),
			Arch:                        arch,
		})
	}
	return response.OkWithPageData(c, count, param.Page, param.PerPage, list)
}

// CreateUpgradePackage 上级创建升级包
func CreateUpgradePackage(c *gin.Context) error {
	param := &cascade.NewUpgradeRequest{}
	if err := c.ShouldBind(param); err != nil {
		return response.FailWithMessage(c, "参数错误")
	}
	file, err := c.FormFile("file")
	if err != nil {
		return response.FailWithMessage(c, "文件上传失败")
	}
	if file.Filename == "" {
		return response.FailWithMessage(c, "文件名不能为空")
	}

	packageType := ConvertPackageType(param.TaskType, param.PackageType)
	if packageType < 1 {
		return response.FailWithMessage(c, "指定升级包类型错误")
	}

	version := ""
	// 只有fobrain需要验证文件名
	if param.TaskType == 1 {
		version, err = upgradePackageFileNameVerifyForFobrain(file.Filename)
		if err != nil {
			return response.FailWithMessage(c, err.Error())
		}
	}
	// 保存包文件
	storagePath := cfg.LoadCommon().StoragePath
	// 完成路径
	dst := filepath.Join(storagePath, "/app/public/upgrade/", file.Filename)
	// 保存文件
	if err := c.SaveUploadedFile(file, dst); err != nil {
		return response.FailWithMessage(c, "文件保存失败")
	}

	packageHash, err := utils.GetFileSHA256(dst)
	if err != nil {
		// 删除文件
		os.Remove(dst)
		return response.FailWithMessage(c, "文件哈希获取失败")
	}
	var handles []mysql.HandleFunc
	handles = append(handles, mysql.WithWhere("package_name = ?", file.Filename))
	handles = append(handles, mysql.WithWhere("version = ?", version))
	count, err := cascade_upgrade.NewCascadeUpgradePackageModel().Count(handles...)
	if err != nil {
		// 删除文件
		os.Remove(dst)
		return err
	}
	if count > 0 {
		// 删除文件
		os.Remove(dst)
		return response.FailWithMessage(c, "该版本已存在")
	}

	model := cascade_upgrade.NewCascadeUpgradePackageModel()
	model.PackageType = packageType
	model.TaskType = param.TaskType
	model.PackageName = file.Filename
	model.PackagePath = dst
	model.Version = version
	model.PackageHash = packageHash
	model.IsDeleted = false
	err = cascade_upgrade_service.CreateUpgradePackage(model)
	if err != nil {
		// 删除文件
		os.Remove(dst)
		return response.FailWithMessage(c, "创建失败")
	}
	// 触发升级
	if param.AutoUpgrade {
		err = cascade_upgrade_service.UpgradeProbe(model.Id, "")
		if err != nil {
			return response.FailWithMessage(c, err.Error())
		}
	}
	return response.OkWithMessage(c, "创建成功")
}

// DeleteUpgradePackage 上级删除升级包
func DeleteUpgradePackage(c *gin.Context) error {
	param := &cascade.DeleteUpgradeRequest{}
	if err := c.ShouldBind(param); err != nil {
		return response.FailWithMessage(c, "参数错误")
	}
	err := cascade_upgrade.NewCascadeUpgradePackageModel().Delete(param.Ids)
	if err != nil {
		return response.FailWithMessage(c, "删除失败")
	}
	return response.OkWithMessage(c, "删除成功")
}

// ConvertPackageType 转换packageType
func ConvertPackageType(taskType, packageType int) (res int) {
	// Fobrain
	if taskType == 1 {
		res = 1
	} else if taskType == 2 {
		// xray
		if packageType == 2 {
			res = 2
		} else if packageType == 3 {
			res = 4
		}
	} else if taskType == 3 {
		// d01
		res = 3
	}
	return res
}

// 升级包文件名验证 // todo这个函数换个地方
func upgradePackageFileNameVerifyForFobrain(filename string) (string, error) {
	// 文件名格式为 fobrain-upgrade-longji-v2.0.2-beta-arm.tar.gz，使用正则表达式
	re := regexp.MustCompile(`-v(\d+\.\d+\.\d+)(?:-([a-zA-Z]+))?(?:-(.*?))?\.tar\.gz$`)
	matches := re.FindStringSubmatch(filename)
	if matches == nil {
		return "", errors.New("文件名格式错误")
	}

	version := matches[1] // 提取到的版本号
	if matches[2] != "" { // 检查是否有后缀
		version += " " + matches[2] // 添加后缀
	}
	return version, nil
}

// UpgradePackageDistribute 上级升级包下发
func UpgradePackageDistribute(c *gin.Context) error {
	param := &cascade.UpgradePackageDistributeRequest{}
	if err := c.ShouldBind(param); err != nil {
		return response.FailWithMessage(c, "参数错误")
	}
	upgradePackage, err := cascade_upgrade.NewCascadeUpgradePackageModel().First(mysql.WithWhere("id = ?", param.Id))
	if err != nil {
		return err
	}

	newUpgradeRequest := &cascade_service.NewUpgradeRequest{
		PackageId:   upgradePackage.Id,
		PackageType: upgradePackage.PackageType,
		PackageName: upgradePackage.PackageName,
		Version:     upgradePackage.Version,
		PackageHash: upgradePackage.PackageHash,
		DownloadUrl: "",
		TaskType:    upgradePackage.TaskType,
		NodeId:      0,
	}

	downloadUrl, err := getDownloadUrl()
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	newUpgradeRequest.DownloadUrl = downloadUrl

	// 指定节点下发
	if param.PlatformId != 0 {
		newUpgradeRequest.NodeId = param.PlatformId
		cascade_service.TriggerNewUpgrade(param.PlatformId, newUpgradeRequest)
	} else {
		// 全部节点下发
		list, err := sdk.GetAllNodes()
		if err != nil {
			return response.FailWithMessage(c, err.Error())
		}
		for _, node := range list {
			newUpgradeRequest.NodeId = int(node.Id)
			cascade_service.TriggerNewUpgrade(int(node.Id), newUpgradeRequest)
		}
	}
	err = cascade_upgrade.NewCascadeUpgradePackageModel().Update(&cascade_upgrade.CascadeUpgradePackage{
		BaseModel: mysql.BaseModel{
			Id: upgradePackage.Id,
		},
		DistributeStatus: 2,
	})
	if err != nil {
		return errors.New("升级包下发失败")
	}
	return response.OkWithMessage(c, "升级包下发成功")
}

func getDownloadUrl() (string, error) {
	url := cfg.LoadCommon().DownloadUrl
	if url == "" {
		systemConfig, err := system_configs.NewSystemConfigs().GetConfig("cascade_download_url")
		if err != nil {
			return "", err
		}
		url = systemConfig
	}
	if url == "" {
		return "", errors.New("下载地址为空")
	}
	return url, nil
}

// UpgradePackage 上级提供升级包下载接口
func UpgradePackage(c *gin.Context) error {
	param := &cascade.UpgradePackage{}
	if err := c.ShouldBind(param); err != nil {
		return response.FailWithMessage(c, "参数错误")
	}

	key := redis_helper.CascadeUpgradeTokenKey(param.Token)
	res, err := redis.GetRedisClient().Get(context.Background(), key).Result()
	if err != nil {
		return response.FailWithMessage(c, "Token错误")
	}

	var data cascade_service.GenerateDownloadTokenRequest
	err = json.Unmarshal([]byte(res), &data)
	if err != nil {
		return err
	}

	upgradePackage, err := cascade_upgrade.NewCascadeUpgradePackageModel().First(mysql.WithWhere("id = ?", data.PackageId))
	if err != nil {
		return err
	}
	storagePath := cfg.LoadCommon().StoragePath
	dst := filepath.Join(storagePath, "/app/public/upgrade/", upgradePackage.PackageName)
	if _, err = os.Stat(dst); os.IsNotExist(err) {
		return response.FailWithMessage(c, "文件不存在")
	}

	cascadeUpgradeDownload, err := cascade_upgrade.NewCascadeUpgradeDownload().First(mysql.WithWhere("version = ?", upgradePackage.Version))
	if err != nil {
		err = cascade_upgrade.NewCascadeUpgradeDownload().Create(&cascade_upgrade.CascadeUpgradeDownload{
			PackageName:       upgradePackage.PackageName,
			Version:           upgradePackage.Version,
			PackageType:       upgradePackage.PackageType,
			PlatformName:      "",
			FirstDownloadTime: localtime.NewLocalTime(time.Now()),
			LastDownloadTime:  localtime.NewLocalTime(time.Now()),
			DownloadCount:     1,
		})
	}
	err = cascade_upgrade.NewCascadeUpgradeDownload().Update(&cascade_upgrade.CascadeUpgradeDownload{
		DownloadCount:    cascadeUpgradeDownload.DownloadCount + 1,
		LastDownloadTime: localtime.NewLocalTime(time.Now()),
	}, mysql.WithWhere("`id` = ?", cascadeUpgradeDownload.Id))
	if err != nil {
		return response.FailWithMessage(c, "添加记录失败")
	}

	return response.OkWithFile(c, dst, false)
}

// DownloadUpgradePackage 下级升级包下载
func DownloadUpgradePackage(c *gin.Context) error {
	param := &cascade.DownloadUpgradePackageRequestIds{}
	if err := c.ShouldBind(param); err != nil {
		return response.FailWithMessage(c, "参数错误")
	}
	records, err := cascade_upgrade.NewCascadeUpgradeRecordModel().First(mysql.WithWhere("id = ?", param.Id))
	if err != nil {
		return err
	}

	cascade_service.TriggerGenerateDownloadToken(&cascade_service.GenerateDownloadTokenRequest{
		NodeId:      records.NodeId,
		PackageId:   records.PackageId,
		PackageType: records.PackageType,
		PackageName: records.PackageName,
		Version:     records.Version,
		RecordId:    records.Id,
	})

	return response.OkWithMessage(c, "开始下载升级包，请稍候查看")
}

// UpgradePackageSubPlatformVerify 下级升级包校验
func UpgradePackageSubPlatformVerify(c *gin.Context) error {
	param := &struct {
		Id       string `json:"id"`
		DetailId string `json:"node_id"`
	}{}
	if err := c.ShouldBind(param); err != nil {
		return response.FailWithMessage(c, "参数错误")
	}

	err := cascade_upgrade_service.UpgradePackageSubPlatformVerify(param.Id, param.DetailId)
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}

	return response.OkWithMessage(c, "升级包完整性校验通过")
}

// UpgradeSubPlatform 下级平台升级
func UpgradeSubPlatform(c *gin.Context) error {
	param := &struct {
		Id       string `json:"id"`
		DetailId string `json:"node_id"`
	}{}
	if err := c.ShouldBind(param); err != nil {
		return response.FailWithMessage(c, "参数错误")
	}
	err := cascade_upgrade_service.UpgradeSubPlatform(param.Id, param.DetailId)
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	return response.OkWithMessage(c, "下发成功")
}

// UpgradeProbe 上级平台触发探针升级
func UpgradeProbe(c *gin.Context) error {
	param := &struct {
		PackageId      uint64 `json:"package_id"`
		RecordDetailId string `json:"record_detail_id"`
	}{}
	if err := c.ShouldBind(param); err != nil {
		return response.FailWithMessage(c, "参数错误")
	}
	err := cascade_upgrade_service.UpgradeProbe(param.PackageId, param.RecordDetailId)
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	return response.OkWithMessage(c, "下发成功")
}

// ListSubPlatformUpgradeRecord 下级获取升级记录
func ListSubPlatformUpgradeRecord(c *gin.Context) error {
	param := &cascade.ListSubPlatformUpgradeRecordRequest{}
	if err := c.ShouldBindQuery(param); err != nil {
		return response.FailWithMessage(c, "参数错误")
	}
	var handles []mysql.HandleFunc
	if param.Keyword != "" {
		handles = append(handles, mysql.WithWhere("package_name like ?", "%"+param.Keyword+"%"))
	}
	if param.TaskType > 0 {
		handles = append(handles, mysql.WithWhere("task_type = ?", param.TaskType))
	}
	// 获取架构条件
	handles = append(handles, getArchHandle())
	handles = append(handles, mysql.WithOrder("updated_at DESC"))
	records, count, err := cascade_upgrade.NewCascadeUpgradeRecordModel().List(param.Page, param.PerPage, handles...)
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	list := make([]*cascade2.ListSubPlatformUpgradeRecordResponse, 0)
	for _, record := range records {
		// 只获取当前架构下的升级包
		arch := getArchFromFilename(record.PackageName)
		packageType := ""
		switch record.PackageType {
		case cascade_upgrade.PackageTypeFobrain:
			packageType = "fobrain升级包"
		case cascade_upgrade.PackageTypeXRayVuln:
			packageType = "x-ray规则库升级包"
		case cascade_upgrade.PackageTypeD01:
			packageType = "d01规则库升级包"
		case cascade_upgrade.PackageTypeXRayEngine:
			packageType = "x-ray引擎升级包"
		default:
			packageType = "fobrain升级包"
		}
		taskType := ""
		switch record.TaskType {
		case cascade_upgrade.TaskTypeFobrain:
			taskType = "Fobrain"
		case cascade_upgrade.TaskTypeD01:
			taskType = "D01"
		case cascade_upgrade.TaskTypeXRay:
			taskType = "Xray"
		default:
			taskType = "Fobrain"
		}
		list = append(list, &cascade2.ListSubPlatformUpgradeRecordResponse{
			PackageType:    packageType,
			PackageTypeId:  record.PackageType,
			Filename:       record.PackageName,
			Version:        record.Version,
			DownloadStatus: record.DownloadStatus,
			DownloadTime:   record.DownloadTime.String(),
			DownloadErr:    record.DownloadErr,
			VerifyStatus:   record.VerifyStatus,
			UpgradeStatus:  record.UpgradeStatus,
			UpgradeTime:    record.UpgradeTime.String(),
			UpdateTime:     record.UpdatedAt.String(),
			Id:             strconv.FormatUint(record.Id, 10),
			TaskType:       taskType,
			TaskTypeId:     record.TaskType,
			Arch:           arch,
		})
	}
	return response.OkWithPageData(c, count, param.Page, param.PerPage, list)
}

// ListSubPlatformUnUpgradeRecord 下级获取未升级的升级记录
func ListSubPlatformUnUpgradeRecord(c *gin.Context) error {
	param := &request.PageRequest{}
	if err := c.ShouldBind(param); err != nil {
		return response.FailWithMessage(c, "参数错误")
	}

	// recordDetails 里面只记录了 xray 和 d01 的数据，没有记录 fobrain 的数据
	recordDetails, err := cascade_upgrade.NewCascadeUpgradeRecordDetailsModel().ListAll(
		mysql.WithWhere("upgrade_status != ?", 2))
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}

	unUpgradeRecordIds := make([]uint64, 0)
	for _, recordDetail := range recordDetails {
		unUpgradeRecordIds = append(unUpgradeRecordIds, recordDetail.RecordId)
	}

	condition, value := getPackageNameCondition()
	handles := []mysql.HandleFunc{
		mysql.WithWhere("id IN (?) OR (upgrade_status = ? AND package_type = ? AND "+condition+")", unUpgradeRecordIds, cascade_upgrade.WaitUpgrade, cascade_upgrade.PackageTypeFobrain, value),
		mysql.WithOrder("updated_at DESC"),
	}
	records, count, err := cascade_upgrade.NewCascadeUpgradeRecordModel().List(param.Page, param.PerPage, handles...)
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	list := make([]cascade2.ListSubPlatformUnUpgradeRecordResponse, 0)
	for _, record := range records {
		packageType := ""
		switch record.PackageType {
		case cascade_upgrade.PackageTypeFobrain:
			packageType = "fobrain升级包"
		case cascade_upgrade.PackageTypeXRayVuln:
			packageType = "x-ray规则库升级包"
		case cascade_upgrade.PackageTypeD01:
			packageType = "d01规则库升级包"
		case cascade_upgrade.PackageTypeXRayEngine:
			packageType = "x-ray引擎升级包"
		default:
			packageType = "fobrain升级包"
		}
		list = append(list, cascade2.ListSubPlatformUnUpgradeRecordResponse{
			PackageType: packageType,
			Filename:    record.PackageName,
			UpdateTime:  record.UpdatedAt.String(),
		})
	}
	return response.OkWithPageData(c, count, param.Page, param.PerPage, list)
}

// ListUpgradeRecordDetail 获取升级包升级详情
func ListUpgradeRecordDetail(c *gin.Context) error {
	params := &cascade.ListUpgradeRecordDetailRequest{}
	if err := c.ShouldBindQuery(params); err != nil {
		return response.FailWithMessage(c, "参数错误")
	}

	records, total, err := cascade_upgrade.NewCascadeUpgradeRecordDetailsModel().List(params.Page, params.PerPage, mysql.WithWhere("record_id = ?", params.Id))
	if err != nil {
		return response.FailWithMessage(c, "数据库错误")
	}

	list := make([]cascade2.ListSubPlatformUpgradeRecordDetailResponse, 0)
	successCount := 0
	failCount := 0
	for _, record := range records {
		node, err := data_source.NewNodeModel().First(mysql.WithWhere("`id` = ?", record.ThirdPartyId))
		if err != nil {
			continue
		}
		list = append(list, cascade2.ListSubPlatformUpgradeRecordDetailResponse{
			NodeId:          fmt.Sprintf("%d", record.ThirdPartyId),
			Node:            node.Name,
			UpgradeStatus:   record.UpgradeStatus,
			LastUpgradeTime: record.LastUpgradeTime,
		})
		if record.UpgradeStatus == 2 {
			successCount++
		} else if record.UpgradeStatus == 3 {
			failCount++
		}
	}
	return response.OkWithData(c, map[string]interface{}{
		"total":           total,
		"page":            params.Page,
		"per_page":        params.PerPage,
		"success_count":   successCount,
		"upgrading_count": len(list) - successCount - failCount,
		"fail_count":      failCount,
		"items":           list,
	})
}

func getPackageNameCondition() (string, string) {
	arch := getArch()
	if arch == "arm" {
		return "package_name LIKE ?", "%arm%"
	}
	return "package_name NOT LIKE ?", "%arm%"
}

func getArchHandle() mysql.HandleFunc {
	condition, value := getPackageNameCondition()
	return mysql.WithWhere("package_type != 1 OR "+condition, value)
}

// getArchFromFilename 根据文件名判断系统架构
func getArchFromFilename(filename string) string {
	// 定义正则表达式模式，用于匹配文件名中是否包含arm（不区分大小写）
	pattern := regexp.MustCompile(`(?i)arm`)

	// 在文件名中查找匹配的关键内容
	matches := pattern.FindStringSubmatch(filename)

	if len(matches) > 0 {
		return "arm"
	}

	return "x86"
}

// getArch 获取当前系统架构
func getArch() string {
	arch := runtime.GOARCH
	if strings.Contains(arch, "arm") {
		return "arm"
	}
	return "x86"
}
