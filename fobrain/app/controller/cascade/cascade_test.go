package cascade

import (
	"testing"
)

func TestUpgradePackageFileNameVerify(t *testing.T) {
	tests := []struct {
		filename string
		expected string
		err      bool
	}{
		{"fobrain-upgrade-longji-v2.0.2-beta.tar.gz", "2.0.2 beta", false},
		{"fobrain-upgrade-longji-v2.0.2-release.tar.gz", "2.0.2 release", false},
		{"fobrain-upgrade-longji-v2.0.2-release-arm64.tar.gz", "2.0.2 release", false},
		{"invalid-filename.tar.gz", "", true},
		{"fobrain-upgrade-longji-v2.0.2.tar.gz", "2.0.2", false},
		{"fobrain-upgrade-longji-2.0.2-beta.tar.gz", "", true},
	}

	for _, test := range tests {
		version, err := upgradePackageFileNameVerifyForFobrain(test.filename)
		if (err != nil) != test.err {
			t.<PERSON><PERSON><PERSON>("Expected error: %v, got: %v for filename: %s", test.err, err, test.filename)
		}
		if version != test.expected {
			t.Errorf("Expected version: %s, got: %s for filename: %s", test.expected, version, test.filename)
		}
	}
}
