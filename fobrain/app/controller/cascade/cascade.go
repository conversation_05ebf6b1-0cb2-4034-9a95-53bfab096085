package cascade

import (
	"errors"
	"fmt"
	"fobrain/fobrain/app/request/cascade"
	cascade_service "fobrain/fobrain/app/services/cascade"
	"fobrain/fobrain/common/request"
	"fobrain/fobrain/common/response"
	"fobrain/fobrain/common/validate"
	redis_helper "fobrain/models/redis"
	"fobrain/module/cascade/sdk"
	distributedlock "fobrain/pkg/distributedLock"

	"github.com/gin-gonic/gin"
)

func PullCascadeData(c *gin.Context) error {
	params := &cascade.PullCascadeDataRequest{}
	if err := c.ShouldBind(params); err != nil {
		return err
	}
	// 根据节点配置参数触发资产或漏洞数据拉取
	nodeInfo, err := sdk.GetNodeInfo(params.NodeId)
	if err != nil {
		return response.FailWithMessage(c, "获取节点信息失败")
	}
	var assetErr, vulnErr error
	tagMap := cascade_service.ParseTag(nodeInfo.Tag)
	if tagMap["has_asset_data"] {
		lockKey := redis_helper.GetCascadeAssetLockKey()
		ok := distributedlock.Lock(lockKey, "PullCascadeAsset", 600)
		if !ok {
			assetErr = errors.New("数据处理中，无法同步资产数据")
		} else {
			// 触发资产数据拉取
			err := cascade_service.TriggerGetAssetList(params.NodeId)
			if err != nil {
				distributedlock.Unlock(lockKey, "PullCascadeAsset")
				assetErr = fmt.Errorf("触发资产数据拉取失败, %v", err.Error())
			}
		}
	}
	if tagMap["has_vul_data"] {
		lockKey := redis_helper.GetCascadeVulnLockKey()
		ok := distributedlock.Lock(lockKey, "PullCascadeVuln", 600)
		if !ok {
			vulnErr = errors.New("数据处理中，无法同步漏洞数据")
		} else {
			// 触发漏洞数据拉取
			err := cascade_service.TriggerGetVulnList(params.NodeId)
			if err != nil {
				distributedlock.Unlock(lockKey, "PullCascadeVuln")
				vulnErr = fmt.Errorf("触发漏洞数据拉取失败, %v", err.Error())
			}
		}
	}
	msg := ""
	if assetErr != nil {
		msg += assetErr.Error()
	}
	if vulnErr != nil {
		msg += " "
		msg += vulnErr.Error()
	}
	if msg != "" {
		return response.FailWithMessage(c, msg)
	}
	return response.OkWithMessage(c, "触发成功")
}

func ListCascadeAsset(c *gin.Context) error {
	param := &cascade.ListCascadeRequest{}
	if err := c.ShouldBind(param); err != nil {
		return response.FailWithMessage(c, "参数错误")
	}
	if ok, msg := validate.Validator(param); !ok {
		return response.FailWithMessage(c, msg)
	}

	list, total, err := cascade_service.ListCascadeAsset(param)
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}

	return response.OkWithPageData(c, total, param.Page, param.PerPage, list)
}

func ListCascadeVuln(c *gin.Context) error {
	param := &cascade.ListCascadeRequest{}
	if err := c.ShouldBind(param); err != nil {
		return response.FailWithMessage(c, "参数错误")
	}
	if ok, msg := validate.Validator(param); !ok {
		return response.FailWithMessage(c, msg)
	}

	list, total, err := cascade_service.ListCascadeVuln(param)
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}

	return response.OkWithPageData(c, total, param.Page, param.PerPage, list)
}

func DeleteCascadeAsset(c *gin.Context) error {
	params, err := request.Validate(c, &cascade.DeleteCascadeAssetRequest{})
	if err != nil {
		return response.FailWithMessage(c, "参数错误")
	}
	err = cascade_service.DeleteCascadeAsset(params)
	if err != nil {
		return err
	}
	return response.OkWithMessage(c, "删除成功")
}

func DeleteCascadeVuln(c *gin.Context) error {
	params, err := request.Validate(c, &cascade.DeleteCascadeVulnRequest{})
	if err != nil {
		return response.FailWithMessage(c, "参数错误")
	}
	err = cascade_service.DeleteCascadeVuln(params)
	if err != nil {
		return err
	}
	return response.OkWithMessage(c, "删除成功")
}
