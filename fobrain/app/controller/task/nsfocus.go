package task

import (
	"fobrain/fobrain/app/repository/proactive_task/source_task"
	"fobrain/fobrain/common/request"
	"fobrain/fobrain/common/response"
	"fobrain/models/mysql/data_source"
	"git.gobies.org/caasm/fobrain-components/dataSourceSdk/nsfocus/rsas"
	"github.com/gin-gonic/gin"
)

func VulTemplateList(c *gin.Context) error {
	var param struct {
		NodeId   uint64 `json:"node_id" form:"node_id" binding:"required" zh:"节点ID"`
		SourceId uint64 `json:"source_id" form:"source_id" binding:"required" zh:"数据源ID"`
	}
	params, err := request.Validate(c, &param)
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	config, err := data_source.NewNodeConfigModel().GetNodeConfig(params.NodeId)
	if err != nil {
		return err
	}
	sdk := rsas.NewRSAS()
	operate := &source_task.Common{}
	operate.NodeConfig = config

	list, err := sdk.GetSysVulnTemplateList(operate)
	if err != nil {
		return err
	}
	return response.OkWithData(c, list)
}
