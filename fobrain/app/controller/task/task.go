package task

import (
	"errors"
	"fmt"
	"fobrain/pkg/cfg"
	"mime/multipart"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/xuri/excelize/v2"

	"fobrain/fobrain/app/repository/node"
	"fobrain/fobrain/app/services/sync/data_import"
	"fobrain/fobrain/app/services/sync/file_import"
	"fobrain/fobrain/app/services/task"
	"fobrain/fobrain/common/request"
	"fobrain/fobrain/common/response"
	"fobrain/fobrain/logs"
	"fobrain/initialize/mysql"
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/data_sync_child_task"
	"fobrain/models/mysql/data_sync_task"
	"fobrain/models/mysql/merge"
	"fobrain/models/mysql/user"
	"fobrain/pkg/utils"
)

type (
	SyncRequest struct {
		NodeId            uint64 `json:"node_id" form:"node_id" uri:"node_id" validate:"required" zh:"节点ID"`
		Type              int    `json:"type" form:"type" uri:"type"  validate:"omitempty,number,oneof=0 1 2 3" zh:"任务同步数据类型"`
		ClearData         int    `json:"clear_data" form:"clear_data" uri:"clear_data"  validate:"omitempty,number,oneof=0 1" zh:"是否清空数据"`
		MappingField      string `json:"mapping_field" form:"mapping_field" uri:"mapping_field"  validate:"omitempty" zh:"映射字段"`
		PeopleUniqueField string `json:"people_unique_field" form:"people_unique_field" uri:"people_unique_field"  validate:"omitempty" zh:"人员唯一字段"`
	}
	SyncDataRequest struct {
		NodeApiUnique string                   `json:"node_api_unique" form:"node_api_unique" uri:"node_api_unique" validate:"required" zh:"链接验证码"`
		SyncType      int                      `json:"sync_type" form:"sync_type" uri:"sync_type"  validate:"required,number,oneof=1 2 3" zh:"同步数据类型：1资产 2漏洞 3人员"`
		Data          []map[string]interface{} `json:"data" form:"data" uri:"data"  validate:"required" zh:"同步数据"`
	}
	SyncAllRequest struct {
		Types []int `json:"types" form:"types" uri:"types"  validate:"required" zh:"任务同步数据类型"`
	}
	ChildListRequest struct {
		request.PageRequest
		TaskId       uint64 `json:"task_id" form:"task_id" uri:"task_id" validate:"omitempty,number" zh:"任务ID"`
		NodeId       uint64 `json:"node_id" form:"node_id" uri:"node_id" validate:"omitempty,number" zh:"节点ID"`
		SourceId     uint64 `json:"source_id" form:"source_id" uri:"source_id" validate:"omitempty,number" zh:"数据源ID"`
		Type         int    `json:"type" form:"type" uri:"type"  validate:"omitempty,number,oneof=0  2 3 4" zh:"任务同步数据类型"`
		Status       int    `json:"status" form:"status" uri:"status"  validate:"omitempty,number,oneof=0 1 2 3 4" zh:"状态"`
		CreateStart  string `json:"create_start" form:"create_start" uri:"create_start"  zh:"同步开始时间"`
		CreateEnd    string `json:"create_end" form:"create_end" uri:"create_end"  zh:"同步结束时间"`
		UpdatedStart string `json:"update_start" form:"update_start" uri:"update_start"  zh:"同步结束时间"`
		UpdatedEnd   string `json:"update_end" form:"update_end" uri:"update_end"  zh:"同步结束时间"`
	}
	ListRequest struct {
		request.PageRequest
		NodeId   uint64 `json:"node_id" form:"node_id" uri:"node_id" validate:"omitempty,number" zh:"节点ID"`
		SourceId uint64 `json:"source_id" form:"source_id" uri:"source_id" validate:"omitempty,number" zh:"数据源ID"`
		Keyword  string `json:"keyword" form:"keyword" uri:"keyword"`
	}

	ChildTaskList struct {
		*data_sync_child_task.DataSyncChildTask
		MergeProgress []*merge.MergeProgress `json:"merge_progress"`
		NodeName      string                 `json:"node_name"`
		SourceName    string                 `json:"source_name"`
	}
	ListResponse struct {
		data_sync_task.DataSyncTask
		NodeName   string              `json:"node_name"`
		SourceName string              `json:"source_name"`
		ChildTasks []uint64            `json:"child_tasks"`
		Types      []int               `json:"types"`
		TypesTotal map[int]interface{} `json:"types_total"`
		File       string              `json:"file"`
	}
)

func ChildList(c *gin.Context) error {
	params, err := request.Validate(c, &ChildListRequest{})
	if err != nil {
		return err
	}
	logs.GetLogger().Infof("params: %+v", params)
	var createStart, createEnd, updatedStart, updatedEnd time.Time
	if params.CreateStart != "" {
		createStart, _ = time.Parse("2006-01-02", params.CreateStart)
	}
	logs.GetLogger().Infof("createStart: %s", params.CreateStart)
	if params.CreateEnd != "" {
		createEnd, _ = time.Parse("2006-01-02", params.CreateEnd)
	}
	if params.UpdatedStart != "" {
		updatedStart, _ = time.Parse("2006-01-02", params.UpdatedStart)
	}
	if params.UpdatedEnd != "" {
		updatedEnd, _ = time.Parse("2006-01-02", params.UpdatedEnd)
	}
	var opts []mysql.HandleFunc

	if params.NodeId > 0 {
		opts = append(opts, mysql.WithWhere("node_id", params.NodeId))
	}

	if params.TaskId > 0 {
		opts = append(opts, mysql.WithWhere("task_id", params.TaskId))
	}

	if params.SourceId > 0 {
		opts = append(opts, mysql.WithWhere("source_id", params.SourceId))
	}
	if params.Status > 0 {
		opts = append(opts, mysql.WithWhere("status", params.Status))
	}
	if params.Type > 0 {
		opts = append(opts, mysql.WithWhere("type", params.Type))
	}
	logs.GetLogger().Infof("createStart %s", createStart.Format("2006-01-02 15:04:05"))
	if !createStart.IsZero() {
		opts = append(opts, mysql.WithWhere("created_at >= ?", createStart.Format("2006-01-02 15:04:05")))
	}
	if !createEnd.IsZero() {
		opts = append(opts, mysql.WithWhere("created_at <= ?", createEnd.Format("2006-01-02 15:04:05")))
	}
	if !updatedStart.IsZero() {
		opts = append(opts, mysql.WithWhere("updated_at >= ?", updatedStart.Format("2006-01-02 15:04:05")))
	}
	if !updatedEnd.IsZero() {
		opts = append(opts, mysql.WithWhere("updated_at <= ?", updatedEnd.Format("2006-01-02 15:04:05")))
	}
	logs.GetLogger().Infof("ListDataSyncChildTask params: %+v", opts)
	tasks, total, err := data_sync_child_task.NewDataSyncChildTaskModel().List(params.Page, params.PerPage, opts...)
	if err != nil {
		return err
	}

	var taskIds []string
	var list []*ChildTaskList
	for _, taskItem := range tasks {
		// 获取所有任务id
		taskIds = append(taskIds, fmt.Sprintf("%d", taskItem.Id))

		item := &ChildTaskList{
			DataSyncChildTask: taskItem,
			MergeProgress:     []*merge.MergeProgress{},
		}

		Node, err := data_source.NewNodeModel().UnscopedFirst(mysql.WithWhere("id", taskItem.NodeId))
		if err == nil {
			item.NodeName = Node.Name
		}

		Source, err := data_source.NewSourceModel().First(mysql.WithWhere("id", taskItem.SourceId))
		if err == nil {
			item.SourceName = Source.Name
		}

		list = append(list, item)
	}

	// // 获取所有任务的融合进度
	// if len(taskIds) > 0 {
	// 	mergeProgress, err := merge.NewMergeProgressModel().GetByTaskIds("sync", taskIds)
	// 	if err != nil {
	// 		return err
	// 	}
	// 	for _, task := range list {
	// 		if mergeProgressItem, ok := mergeProgress[fmt.Sprintf("%d", task.DataSyncChildTask.Ids)]; ok {
	// 			task.MergeProgress = mergeProgressItem
	// 		}
	// 	}
	// }

	return response.OkWithPageData(c, total, params.Page, params.PerPage, list)
}
func List(c *gin.Context) error {
	params, err := request.Validate(c, &ListRequest{})
	if err != nil {
		return err
	}
	var opts []mysql.HandleFunc
	if params.NodeId > 0 {
		opts = append(opts, mysql.WithWhere("node_id", params.NodeId))
	}
	if params.SourceId > 0 {
		opts = append(opts, mysql.WithWhere("source_id", params.SourceId))
	}
	if len(strings.TrimSpace(params.Keyword)) != 0 {
		ids, err := data_source.NewNodeModel().SearchIdByName(params.Keyword)
		if err != nil {
			return err
		}
		opts = append(opts, mysql.WithValuesIn("node_id", ids))
	}

	tasks, total, err := data_sync_task.NewDataSyncTaskModel().List(params.Page, params.PerPage, opts...)
	if err != nil {
		return err
	}
	var list []ListResponse

	for _, item := range tasks {
		data := ListResponse{
			DataSyncTask: item,
		}
		data.File = item.File
		childTasks, _, err := data_sync_child_task.NewDataSyncChildTaskModel().List(0, 0, mysql.WithWhere("task_id", item.Id))
		if err != nil {
			return err
		}

		data.TypesTotal = make(map[int]interface{})
		for _, childTask := range childTasks {
			data.ChildTasks = append(data.ChildTasks, childTask.Id)
			data.Types = append(data.Types, childTask.Type)
			data.TypesTotal[childTask.Type] = map[string]int{"sync_data_total": childTask.SyncDataTotal, "sync_data_success_total": childTask.SyncDataSuccessTotal}
		}

		Node, err := data_source.NewNodeModel().UnscopedFirst(mysql.WithWhere("id", item.NodeId))
		if err == nil {
			data.NodeName = Node.Name
		}

		Source, err := data_source.NewSourceModel().First(mysql.WithWhere("id", item.SourceId))
		if err == nil {
			data.SourceName = Source.Name
		}

		list = append(list, data)
	}
	if list == nil {
		list = []ListResponse{}
	}
	return response.OkWithPageData(c, total, params.Page, params.PerPage, list)
}
func Sync(c *gin.Context) error {
	params, err := request.Validate(c, &SyncRequest{})
	if err != nil {
		return err
	}

	nd := &data_source.Node{}
	if err = data_source.NewNodeModel().Where("id = ?", params.NodeId).First(nd).Error; err != nil {
		return err
	}
	// 获取当前节点同步的任务类型
	types, err := utils.ConvertStringToIntSlice(nd.DataTypes)

	err = task.NewSyncDataTask().Dispatch(params.NodeId, types, data_sync_task.SourceHandle, "", "")
	if err != nil {
		return err
	}

	return response.Ok(c)
}

func SyncAll(c *gin.Context) error {
	params, err := request.Validate(c, &SyncAllRequest{})
	if err != nil {
		return err
	}

	var opts []mysql.HandleFunc
	opts = append(opts, mysql.WithWhere("has_sync", data_source.HasSyncYes))

	sources, _, err := data_source.NewSourceModel().Items(0, 0, opts...)
	if err != nil {
		return err
	}

	for _, source := range sources {
		nodes, _, err := data_source.NewNodeModel().Items(0, 0, mysql.WithWhere("source_id", source.Id), mysql.WithWhere("status", data_source.StatusNormal))
		if err != nil {
			return err
		}

		if len(nodes) == 0 {
			continue
		}

		//增加判断源类型是否包含本次下发的类型 包含则下发
		if (source.HasAssetData && utils.InArray(data_sync_task.SyncAsset, params.Types)) ||
			(source.HasVulData && utils.InArray(data_sync_task.SyncThreat, params.Types)) ||
			(source.HasPersonnelData && utils.InArray(data_sync_task.SyncPeople, params.Types)) {
			for _, node := range nodes {
				err = task.NewSyncDataTask().Dispatch(node.Id, params.Types, data_sync_task.SourceHandle, "", "")
				if err != nil {
					return err
				}
			}
		}
	}

	return response.Ok(c)
}
func SyncFile(c *gin.Context) error {
	params, err := request.Validate(c, &SyncRequest{})
	if err != nil {
		return err
	}

	file, fileHeader, err := c.Request.FormFile("file")
	if err != nil {
		return err
	}
	defer func(file multipart.File) {
		err := file.Close()
		if err != nil {
			err := response.FailWithCodeMessage(c, 500, "关闭文件失败")
			if err != nil {
				return
			}
			return
		}
	}(file)

	f, err := excelize.OpenReader(file)
	if err != nil {
		return err
	}

	//读取Sheet1的数据
	rows, err := f.GetRows("Sheet1", excelize.Options{RawCellValue: true})
	if err != nil {
		return err
	}

	if len(rows) > 30*10000 {
		return response.FailWithCodeMessage(c, 400, "单次文件同步数量不能大于30w数据")
	}

	var data string

	var clearDataScope []string
	//校验数据格式
	if params.Type == data_sync_task.SyncAsset {
		var hasDevice bool
		data, hasDevice, err = file_import.CheckAssets(rows, params.MappingField)
		if hasDevice {
			clearDataScope = append(clearDataScope, "asset", "device")
		} else {
			clearDataScope = append(clearDataScope, "asset")
		}
	}
	if params.Type == data_sync_task.SyncThreat {
		data, err = file_import.CheckThreats(rows)
		clearDataScope = append(clearDataScope, "vuln")
	}
	if params.Type == data_sync_task.SyncPeople {
		uniqueField := params.PeopleUniqueField
		if uniqueField == "" {
			uniqueField = "name+mobile"
		}
		data, err = file_import.CheckPeoples(rows, uniqueField)
		clearDataScope = append(clearDataScope, "staff")
	}

	if err != nil {
		return err
	}
	if data == "null" {
		return errors.New("文件无数据")
	}
	//保存文件
	// 指定保存目录和文件名
	batch := time.Now().Format("20060102150405")
	baseDir := cfg.LoadCommon().StoragePath + "/app/files/"
	fileName := batch + "_" + fileHeader.Filename
	savePath := filepath.Join(baseDir, fileName)

	// 保存文件到指定路径
	if err := c.SaveUploadedFile(fileHeader, savePath); err != nil {
		return errors.New("failed to save file")
	}
	// 清空历史数据
	if params.ClearData == 1 {
		err = node.ClearNodeData(params.NodeId, clearDataScope...)
		if err != nil {
			return err
		}
	}
	err = task.NewSyncDataTask().Dispatch(params.NodeId, []int{params.Type}, data_sync_task.SourceHandle, data, fileName)
	if err != nil {
		return err
	}

	return response.Ok(c)
}

func SyncData(c *gin.Context) error {
	params, err := request.Validate(c, &SyncDataRequest{})
	if err != nil {
		return err
	}

	//限制最大支持5w
	if len(params.Data) > 50000 {
		return errors.New("API同步最大支持5万")
	}

	//校验params.NodeApiUnique是否有效
	var total int64
	data_source.NewNodeConfigModel().Where("value = ?", params.NodeApiUnique).Count(&total)
	if total == 0 {
		return errors.New("无效的链接验证码")
	}

	//减密NodeApiUnique
	decrypt, err := utils.LaravelDecrypt(params.NodeApiUnique)
	if err != nil {
		return err
	}

	//校验NodeApiUnique里面的数据是否被删除
	result := strings.Split(strings.Trim(decrypt, "\""), "@#")
	// 将字符串转换为 uint64
	userId, err := strconv.ParseUint(result[0], 10, 64)
	if err != nil {
		return err
	}
	sourceId, err := strconv.ParseUint(result[1], 10, 64)
	if err != nil {
		return err
	}
	nodeId, err := strconv.ParseUint(result[2], 10, 64)
	if err != nil {
		return err
	}

	total = 0
	user.NewUserModel().Where("id = ?", userId).Count(&total)
	if total == 0 {
		return errors.New("链接验证码用户不存在")
	}
	total = 0
	data_source.NewSourceModel().Where("id = ?", sourceId).Count(&total)
	if total == 0 {
		return errors.New("链接验证码数据源不存在")
	}
	total = 0
	data_source.NewNodeModel().Where("id = ?", nodeId).Count(&total)
	if total == 0 {
		return errors.New("链接验证码节点不存在")
	}
	var data string

	//校验数据格式
	if params.SyncType == data_sync_task.SyncAsset {
		data, err = data_import.CheckAssets(params.Data)
	}
	if params.SyncType == data_sync_task.SyncThreat {
		data, err = data_import.CheckThreats(params.Data)
	}
	if params.SyncType == data_sync_task.SyncPeople {
		data, err = data_import.CheckPeoples(params.Data)
	}

	if err != nil {
		return err
	}

	err = task.NewSyncDataTask().Dispatch(nodeId, []int{params.SyncType}, data_sync_task.SourceHandle, data, "")
	if err != nil {
		return err
	}

	return response.Ok(c)
}
