package foeye_proactive_scanning

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"fmt"
	"fobrain/fobrain/app/repository/task_center/proactive_scan"
	"fobrain/fobrain/common/localtime"
	"github.com/alicebob/miniredis/v2"
	goRedis "github.com/go-redis/redis/v8"
	"github.com/olivere/elastic/v7"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"

	testcommon "fobrain/fobrain/tests/common_test"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func TestCreate(t *testing.T) {
	t.Run("params err", func(t *testing.T) {
		gin.SetMode(gin.TestMode)
		w := httptest.NewRecorder()
		req := httptest.NewRequest("POST", "/api/v1/task_center/proactive_scan?tool_i=1", nil)

		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := Create(c)

		assert.NotEmpty(t, err)
		assert.Equal(t, http.StatusOK, w.Code)
	})

	t.Run("database err", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()
		mockDb.ExpectQuery("SELECT * FROM `data_node_configs` WHERE `node_id` = ?").WithArgs(int64(1)).
			WillReturnRows(mockDb.NewRows([]string{"id", "node_id", "key", "value"}).AddRow(1, 1, "scan_mode", 1))

		gin.SetMode(gin.TestMode)
		w := httptest.NewRecorder()
		req := httptest.NewRequest("POST", "/api/v1/task_center/proactive_scan?name=test&desc=test&task_type=1&source_id=1&scan_plan=1&scan_period=1&scan_time=1&repeat_end_time=1&scan_mode=1&scan_ip_range_type=1&scan_ip_ranges=1&poc_scan_type=1&selected_pocs=1&scan_port=1&scan_type=1&bandwidth=1&concurrency=1&other_cfgs=1&data_node_ids=1", nil)

		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := Create(c)

		fmt.Print(err.Error(), "\n")
		assert.NotEmpty(t, err)
		assert.Equal(t, http.StatusOK, w.Code)
	})

	t.Run("success with user_input", func(t *testing.T) {
		s, err := miniredis.Run()
		if err != nil {
			panic(err)
		}
		defer s.Close()

		server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(http.StatusOK)
			_, _ = w.Write([]byte(`{"err": "", "msg": "success", "data": {"content": [{"id": "engine1"}, {"id": "engine2"}]}}`))
		}))
		defer server.Close()

		cli := goRedis.NewClient(&goRedis.Options{
			Addr: s.Addr(),
		})
		testcommon.SetRedisClient(cli)

		mockDb := testcommon.InitSqlMock()

		// Mock 禁扫配置查询 - 需要多次查询
		// 1. Create 函数中的第一次查询
		mockDb.ExpectQuery("SELECT value FROM `system_configs` WHERE `key` = ?").WithArgs("scan_ban_key").
			WillReturnRows(mockDb.NewRows([]string{"value"}).AddRow(`{"enable_ban": false, "week_days": [], "ban_periods": [], "banned_assets": []}`))

		// 2. SaveTask 函数中的查询
		mockDb.ExpectQuery("SELECT value FROM `system_configs` WHERE `key` = ?").WithArgs("scan_ban_key").
			WillReturnRows(mockDb.NewRows([]string{"value"}).AddRow(`{"enable_ban": false, "week_days": [], "ban_periods": [], "banned_assets": []}`))

		// 3. FilterScanIps 函数中的查询
		mockDb.ExpectQuery("SELECT value FROM `system_configs` WHERE `key` = ?").WithArgs("scan_ban_key").
			WillReturnRows(mockDb.NewRows([]string{"value"}).AddRow(`{"enable_ban": false, "week_days": [], "ban_periods": [], "banned_assets": []}`))

		// Mock 任务创建
		mockDb.ExpectBegin()
		mockDb.ExpectQuery("SELECT * FROM `data_node_configs` WHERE `node_id` = ?").WithArgs(1).
			WillReturnRows(mockDb.NewRows([]string{"key", "value"}).AddRows([]driver.Value{"protocol", "http"}, []driver.Value{"ip", server.Listener.Addr().String()}))

		mockDb.ExpectExec("INSERT INTO `proactive_tasks`").WillReturnResult(sqlmock.NewResult(1, 1))
		mockDb.ExpectExec("INSERT INTO `proactive_task_node_relations`").WillReturnResult(sqlmock.NewResult(1, 1))
		mockDb.ExpectCommit()

		// Mock 定时任务相关查询
		mockDb.ExpectBegin()
		mockDb.ExpectExec("INSERT INTO `crontab`").
			WillReturnResult(sqlmock.NewResult(1, 1))
		mockDb.ExpectCommit()

		mockDb.ExpectQuery("SELECT * FROM `crontab` WHERE `status` = ? AND `id` IN (?)").
			WithArgs(1, 1).
			WillReturnRows(mockDb.NewRows([]string{"id", "status"}).AddRow(1, 1))

		mockDb.ExpectBegin()
		mockDb.ExpectExec("UPDATE `crontab`").
			WillReturnResult(sqlmock.NewResult(1, 1))
		mockDb.ExpectCommit()

		mockDb.ExpectBegin()
		mockDb.ExpectExec("INSERT INTO `crontab`").
			WillReturnResult(sqlmock.NewResult(1, 1))
		mockDb.ExpectCommit()

		mockDb.ExpectQuery("SELECT * FROM `crontab` WHERE `status` = ? AND `id` IN (?)").
			WithArgs(1, 1).
			WillReturnRows(mockDb.NewRows([]string{"id", "status"}).AddRow(1, 1))

		gin.SetMode(gin.TestMode)
		w := httptest.NewRecorder()

		params := CreateRequest{
			Name:            "FOBrain资产普通漏洞任务下发",
			Desc:            "desc",
			TaskType:        1,
			SourceId:        1,
			RepeatEndTime:   "2024-12-25",
			ScanMode:        1,
			ScanIpRangeType: "user_input",
			ScanIpRanges:    []string{"**********/24"},
			PocScanType:     "all",
			SelectedPocs:    "",
			ScanPort:        80,
			ScanType:        "quick",
			Bandwidth:       100,
			Concurrency:     0,
			OtherCfgs: map[string]any{
				"is_deep_scan":        false,
				"is_ping_recognition": true,
			},
			DataNodeIds: []int{1},
			SyncConfig: SyncConfig{
				ScanPlan: 4,
				Config:   []Config{{ScanPeriod: "1", ScanTime: "01:01"}},
			},
		}
		body, err := json.Marshal(params)
		assert.Nil(t, err)

		req := httptest.NewRequest("POST", "/api/v1/task_center/proactive_scan", strings.NewReader(string(body)))

		req.Header.Set("Content-Type", "application/json")
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err = Create(c)

		mockDb.Close()

		assert.Nil(t, err)
		assert.Equal(t, http.StatusOK, w.Code)
	})

	t.Run("success with task splitting", func(t *testing.T) {
		// 创建mock ES服务器
		mockServer := testcommon.NewMockServer()
		defer mockServer.Close()

		// 准备大量IP数据，模拟需要拆分的场景（减少数量避免无限循环）
		testAssets := make([]*elastic.SearchHit, 100)
		for i := 0; i < 100; i++ {
			testAssets[i] = &elastic.SearchHit{
				Id:     fmt.Sprintf("%d", i+1),
				Source: json.RawMessage(fmt.Sprintf(`{"ip": "192.168.1.%d"}`, i+1)),
			}
		}

		// 注册ES响应
		mockServer.Register("/asset/_search.*", testAssets)

		s, err := miniredis.Run()
		if err != nil {
			panic(err)
		}
		defer s.Close()

		server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(http.StatusOK)
			_, _ = w.Write([]byte(`{"err": "", "msg": "success", "data": {"content": [{"id": "engine1"}, {"id": "engine2"}]}}`))
		}))
		defer server.Close()

		cli := goRedis.NewClient(&goRedis.Options{
			Addr: s.Addr(),
		})
		testcommon.SetRedisClient(cli)

		mockDb := testcommon.InitSqlMock()

		// Mock 禁扫配置查询 - 需要多次查询
		// 1. Create 函数中的第一次查询
		mockDb.ExpectQuery("SELECT value FROM `system_configs` WHERE `key` = ?").WithArgs("scan_ban_key").
			WillReturnRows(mockDb.NewRows([]string{"value"}).AddRow(`{"enable_ban": false, "week_days": [], "ban_periods": [], "banned_assets": []}`))

		// 2. SaveTask 函数中的查询（第一个任务）
		mockDb.ExpectQuery("SELECT value FROM `system_configs` WHERE `key` = ?").WithArgs("scan_ban_key").
			WillReturnRows(mockDb.NewRows([]string{"value"}).AddRow(`{"enable_ban": false, "week_days": [], "ban_periods": [], "banned_assets": []}`))

		// 3. FilterScanIps 函数中的查询（第一个任务）
		mockDb.ExpectQuery("SELECT value FROM `system_configs` WHERE `key` = ?").WithArgs("scan_ban_key").
			WillReturnRows(mockDb.NewRows([]string{"value"}).AddRow(`{"enable_ban": false, "week_days": [], "ban_periods": [], "banned_assets": []}`))

		// Mock 第一个任务创建
		mockDb.ExpectBegin()
		mockDb.ExpectQuery("SELECT * FROM `data_node_configs` WHERE `node_id` = ?").WithArgs(1).
			WillReturnRows(mockDb.NewRows([]string{"key", "value"}).AddRows([]driver.Value{"protocol", "http"}, []driver.Value{"ip", server.Listener.Addr().String()}))

		mockDb.ExpectExec("INSERT INTO `proactive_tasks`").WillReturnResult(sqlmock.NewResult(1, 1))
		mockDb.ExpectExec("INSERT INTO `proactive_task_node_relations`").WillReturnResult(sqlmock.NewResult(1, 1))
		mockDb.ExpectCommit()

		// 4. SaveTask 函数中的查询（第二个任务）
		mockDb.ExpectQuery("SELECT value FROM `system_configs` WHERE `key` = ?").WithArgs("scan_ban_key").
			WillReturnRows(mockDb.NewRows([]string{"value"}).AddRow(`{"enable_ban": false, "week_days": [], "ban_periods": [], "banned_assets": []}`))

		// 5. FilterScanIps 函数中的查询（第二个任务）
		mockDb.ExpectQuery("SELECT value FROM `system_configs` WHERE `key` = ?").WithArgs("scan_ban_key").
			WillReturnRows(mockDb.NewRows([]string{"value"}).AddRow(`{"enable_ban": false, "week_days": [], "ban_periods": [], "banned_assets": []}`))

		// Mock 第二个任务创建
		mockDb.ExpectBegin()
		mockDb.ExpectQuery("SELECT * FROM `data_node_configs` WHERE `node_id` = ?").WithArgs(1).
			WillReturnRows(mockDb.NewRows([]string{"key", "value"}).AddRows([]driver.Value{"protocol", "http"}, []driver.Value{"ip", server.Listener.Addr().String()}))

		mockDb.ExpectExec("INSERT INTO `proactive_tasks`").WillReturnResult(sqlmock.NewResult(2, 1))
		mockDb.ExpectExec("INSERT INTO `proactive_task_node_relations`").WillReturnResult(sqlmock.NewResult(2, 1))
		mockDb.ExpectCommit()

		gin.SetMode(gin.TestMode)
		w := httptest.NewRecorder()

		params := CreateRequest{
			Name:            "大量IP扫描任务",
			Desc:            "测试任务拆分",
			TaskType:        1,
			SourceId:        1,
			RepeatEndTime:   "2024-12-25",
			ScanMode:        1,
			ScanIpRangeType: "account_asset_all",
			ScanIpRanges:    []string{},
			PocScanType:     "all",
			SelectedPocs:    "",
			ScanPort:        80,
			ScanType:        "quick",
			Bandwidth:       100,
			Concurrency:     0,
			OtherCfgs: map[string]any{
				"is_deep_scan":        false,
				"is_ping_recognition": true,
			},
			DataNodeIds: []int{1},
			SyncConfig: SyncConfig{
				ScanPlan: 1, // 立即执行
				Config:   []Config{},
			},
		}
		body, err := json.Marshal(params)
		assert.Nil(t, err)

		req := httptest.NewRequest("POST", "/api/v1/task_center/proactive_scan", strings.NewReader(string(body)))
		req.Header.Set("Content-Type", "application/json")
		c, _ := gin.CreateTestContext(w)
		c.Request = req
		c.Set("is_super_manage", true) // 设置为超级管理员，跳过权限检查

		err = Create(c)

		mockDb.Close()

		assert.Nil(t, err)
		assert.Equal(t, http.StatusOK, w.Code)
	})
}

func TestScanTool(t *testing.T) {

	t.Run("err", func(t *testing.T) {
		mockDb := testcommon.GetMysqlMock()
		defer mockDb.Close()
		mockDb.ExpectQuery("SELECT * FROM `data_sources` WHERE id in (?)").WithArgs(1).
			WillReturnError(errors.New("mock err"))

		gin.SetMode(gin.TestMode)
		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)

		err := ScanTool(c)
		assert.NotEmpty(t, err)
		assert.Equal(t, http.StatusOK, w.Code)
	})

	t.Run("success", func(t *testing.T) {
		gin.SetMode(gin.TestMode)
		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)

		mockDb := testcommon.GetMysqlMock()
		defer mockDb.Close()
		mockDb.ExpectQuery("SELECT `source_id` FROM `data_nodes` WHERE deleted_at IS NULL").
			WillReturnRows(mockDb.NewRows([]string{"source_id"}).AddRow(1).AddRow(10).AddRow(14))

		mockDb.ExpectQuery("SELECT * FROM `data_sources` WHERE id in (?,?,?)").WithArgs(1, 10, 14).
			WillReturnRows(mockDb.NewRows([]string{"id", "name", "description", "created_at", "updated_at"}).AddRow(1, "ScanTool1", "Description1", time.Now(), time.Now()).
				AddRow(10, "ScanTool1", "Description1", time.Now(), time.Now()))

		ScanTool(c)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.NotEmpty(t, w.Body.String())
	})
}

func TestScanTypes(t *testing.T) {
	t.Run("err", func(t *testing.T) {
		gin.SetMode(gin.TestMode)
		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/v1/task_center/proactive_scan/scan_types", nil)
		c, _ := gin.CreateTestContext(w)
		c.Request = req
		_ = ScanTypes(c)
		assert.Equal(t, w.Code, http.StatusBadRequest)
	})

	t.Run("sql err", func(t *testing.T) {
		gin.SetMode(gin.TestMode)
		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/v1/task_center/proactive_scan/scan_types?node_id=1&source_id=14", nil)
		c, _ := gin.CreateTestContext(w)
		c.Request = req
		_ = ScanTypes(c)
		assert.Equal(t, w.Code, http.StatusBadRequest)
	})

	t.Run("success", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()
		server := testcommon.SetupMockServer(`{"message": "mocked-token"}`, http.StatusOK)
		defer server.Close()

		mockDb.ExpectQuery("SELECT * FROM `data_node_configs` WHERE `node_id` = ?").WithArgs(int64(1)).
			WillReturnRows(mockDb.NewRows([]string{"id", "node_id", "key", "value"}).AddRow(1, 1, "scan_mode", 1).
				AddRow(1, 1, "protocol", "http").AddRow(1, 1, "ip", testcommon.ExtractAfterProtocol(server.URL)))

		gin.SetMode(gin.TestMode)
		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/v1/task_center/proactive_scan/scan_types?node_id=1&source_id=14", nil)
		c, _ := gin.CreateTestContext(w)
		c.Request = req
		_ = ScanTypes(c)
		assert.Equal(t, w.Code, http.StatusOK)
	})
}

func TestScanNode(t *testing.T) {
	t.Run("params err", func(t *testing.T) {
		gin.SetMode(gin.TestMode)
		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/v1/task_center/proactive_scan/scan_node?tool_i=1&mode_id=2", nil)
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := ScanNode(c)

		assert.NotEmpty(t, err)
		assert.Equal(t, http.StatusOK, w.Code)
	})

	t.Run("database search err", func(t *testing.T) {
		gin.SetMode(gin.TestMode)
		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/v1/task_center/proactive_scan/scan_node?tool_id=1&mode_id=2", nil)
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := ScanNode(c)

		assert.NotEmpty(t, err)
		assert.Equal(t, http.StatusOK, w.Code)
	})

	t.Run("success", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()

		mockDb.ExpectQuery("SELECT id,name FROM `data_nodes` WHERE source_id = ? AND deleted_at IS NULL AND status = ?").WithArgs(1, 3).
			WillReturnRows(mockDb.NewRows([]string{"id", "name"}).AddRow(1, "Node1"))

		gin.SetMode(gin.TestMode)
		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/v1/task_center/proactive_scan/scan_node?tool_id=1&mode_id=2", nil)
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := ScanNode(c)

		assert.Empty(t, err)
		assert.Equal(t, http.StatusOK, w.Code)
	})
}

// TestListOnce 单次任务列表展示 - 测试
func TestListOnce(t *testing.T) {
	t.Run("NoSearch", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()

		mockDb.ExpectQuery("SELECT * FROM `data_sources`").
			WillReturnRows(mockDb.NewRows([]string{"id", "name"}))

		mockDb.ExpectQuery("SELECT count(*) FROM `proactive_tasks` WHERE scan_plan = ?").
			WithArgs(1).
			WillReturnRows(mockDb.NewRows([]string{"count"}).AddRow(1))

		mockDb.ExpectQuery("SELECT * FROM `proactive_tasks` WHERE scan_plan = ? ORDER BY created_at DESC LIMIT 10").
			WithArgs(1).
			WillReturnRows(mockDb.NewRows([]string{"id", "name", "desc", "task_type", "source_id", "scan_plan", "scan_period", "scan_time", "status", "progress", "repeat_end_time", "begin_time", "end_time", "scan_mode", "use_seconds", "scan_result", "scan_ip_range_type", "scan_ip_ranges", "poc_scan_type", "selected_pocs", "scan_port", "scan_type", "bandwidth", "concurrency", "other_cfgs"}))
		mockDb.ExpectQuery("SELECT * FROM `users`").
			WillReturnRows(mockDb.NewRows([]string{"id", "name"}))
		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/v1/task_center/proactive_scan/once_list?page=1&per_page=10", nil)
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		ListOnce(c)
	})

	t.Run("WithSearch", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()

		mockDb.ExpectQuery("SELECT * FROM `data_sources`").
			WillReturnRows(mockDb.NewRows([]string{"id", "name"}))

		mockDb.ExpectQuery("SELECT count(*) FROM `proactive_tasks` WHERE scan_plan = ? AND name LIKE ?").
			WithArgs(1, "%1%").
			WillReturnRows(mockDb.NewRows([]string{"count"}).AddRow(1))

		mockDb.ExpectQuery("SELECT * FROM `proactive_tasks` WHERE scan_plan = ? AND name LIKE ? ORDER BY created_at DESC LIMIT 10").
			WithArgs(1, "%1%").
			WillReturnRows(mockDb.NewRows([]string{"id", "name", "desc", "task_type", "source_id", "scan_plan", "scan_period", "scan_time", "status", "progress", "repeat_end_time", "begin_time", "end_time", "scan_mode", "use_seconds", "scan_result", "scan_ip_range_type", "scan_ip_ranges", "poc_scan_type", "selected_pocs", "scan_port", "scan_type", "bandwidth", "concurrency", "other_cfgs"}).AddRow(1, "Task1", "Description", 1, 1, 1, "weekly", "12:00", 1, 50, nil, nil, nil, 1, "60s", "result", "user_input", `["**********/24"]`, "all", nil, 80, "quick", 100, 5, `{}`))

		mockDb.ExpectQuery("SELECT * FROM `users`").
			WillReturnRows(mockDb.NewRows([]string{"id", "name"}))

		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/v1/task_center/proactive_scan/once_list?page=1&per_page=10&keyword=1", nil)
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		ListOnce(c)

		assert.NotEmpty(t, w.Body.String())
	})

	t.Run("WithSearch err", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()
		mockDb.ExpectQuery("SELECT * FROM `data_sources`").
			WillReturnRows(mockDb.NewRows([]string{"id", "name"}))

		mockDb.ExpectQuery("SELECT count(*) FROM `proactive_tasks` WHERE scan_plan = ? AND name LIKE ?").
			WithArgs(1, "%1%").
			WillReturnRows(mockDb.NewRows([]string{"count"}).AddRow(1))

		mockDb.ExpectQuery("SELECT * FROM `proactive_tasks` WHERE scan_plan = ? AND name LIKE ? ORDER BY created_at DESC LIMIT 10").
			WillReturnError(errors.New("mock err"))

		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/v1/task_center/proactive_scan/once_list?page=1&per_page=10&keyword=1", nil)
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := ListOnce(c)

		assert.NotEmpty(t, err)
	})

	t.Run("WithSearch err count", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()

		mockDb.ExpectQuery("SELECT * FROM `data_sources`").
			WillReturnRows(mockDb.NewRows([]string{"id", "name"}))

		mockDb.ExpectQuery("SELECT count(*) FROM `proactive_tasks` WHERE scan_plan = ? AND name LIKE ?").
			WillReturnError(errors.New("mock err"))

		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/v1/task_center/proactive_scan/once_list?page=1&per_page=10&keyword=1", nil)
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := ListOnce(c)

		assert.NotEmpty(t, err)
	})

}

// TestGetScheduledTasksList 周期任务列表展示 - 测试
func TestGetScheduledTasksList(t *testing.T) {

	t.Run("NoSearch", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()

		mockDb.ExpectQuery("SELECT * FROM `data_sources`").
			WillReturnRows(mockDb.NewRows([]string{"id", "name"}))

		mockDb.ExpectQuery("SELECT count(*) FROM `proactive_tasks` WHERE scan_plan != ?").
			WillReturnRows(mockDb.NewRows([]string{"count"}).AddRow(0))

		mockDb.ExpectQuery("SELECT * FROM `proactive_tasks` WHERE scan_plan != ? ORDER BY created_at DESC LIMIT 10").
			WithArgs(1).
			WillReturnRows(mockDb.NewRows([]string{"id", "name", "desc", "task_type", "source_id", "scan_plan", "scan_period", "scan_time", "status", "progress", "repeat_end_time", "begin_time", "end_time", "scan_mode", "use_seconds", "scan_result", "scan_ip_range_type", "scan_ip_ranges", "poc_scan_type", "selected_pocs", "scan_port", "scan_type", "bandwidth", "concurrency", "other_cfgs"}))
		mockDb.ExpectQuery("SELECT * FROM `users`").
			WillReturnRows(mockDb.NewRows([]string{"id", "name"}))
		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/v1/task_center/proactive_scan/scheduled_list?page=1&per_page=10", nil)
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		GetScheduledTasksList(c)

		expected := `{"code":0,"message":"Success","data":{"total":0,"page":1,"per_page":10,"items":[]}}`
		assert.JSONEq(t, expected, w.Body.String())
	})

	t.Run("WithSearch", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()

		mockDb.ExpectQuery("SELECT * FROM `data_sources`").
			WillReturnRows(mockDb.NewRows([]string{"id", "name"}))

		mockDb.ExpectQuery("SELECT count(*) FROM `proactive_tasks` WHERE scan_plan != ? AND name LIKE ?").
			WithArgs(1, "%1%").
			WillReturnRows(mockDb.NewRows([]string{"count"}).AddRow(1))

		scanIPRanges, _ := json.Marshal([]string{"**********/24"})
		otherCfgs, _ := json.Marshal(map[string]string{"is_deep_scan": "on"})

		mockDb.ExpectQuery("SELECT * FROM `proactive_tasks` WHERE scan_plan != ? AND name LIKE ? ORDER BY created_at DESC LIMIT 10").
			WithArgs(1, "%1%").
			WillReturnRows(mockDb.NewRows([]string{"id", "name", "desc", "task_type", "source_id", "scan_plan", "scan_period", "scan_time", "status", "progress", "repeat_end_time", "begin_time", "end_time", "scan_mode", "use_seconds", "scan_result", "scan_ip_range_type", "scan_ip_ranges", "poc_scan_type", "selected_pocs", "scan_port", "scan_type", "bandwidth", "concurrency", "other_cfgs"}).
				AddRow(1, "Task1", "Description", 1, 1, 1, "weekly", "12:00", 1, 50, nil, nil, nil, 1, "60s", "result", "user_input", scanIPRanges, "all", nil, 80, "quick", 100, 5, otherCfgs))
		mockDb.ExpectQuery("SELECT * FROM `users`").
			WillReturnRows(mockDb.NewRows([]string{"id", "name"}))
		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/v1/task_center/proactive_scan/scheduled_list?page=1&per_page=10&keyword=1", nil)
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		GetScheduledTasksList(c)

	})

	t.Run("WithSearch count err", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()

		mockDb.ExpectQuery("SELECT * FROM `data_sources`").
			WillReturnRows(mockDb.NewRows([]string{"id", "name"}))

		mockDb.ExpectQuery("SELECT count(*) FROM `proactive_tasks` WHERE scan_plan != ? AND name LIKE ?").
			WithArgs(1, "%1%").
			WillReturnError(errors.New("count err"))

		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/v1/task_center/proactive_scan/scheduled_list?page=1&per_page=10&keyword=1", nil)
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := GetScheduledTasksList(c)
		assert.NotEmpty(t, err)
	})

	t.Run("WithSearch data err", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()

		mockDb.ExpectQuery("SELECT * FROM `data_sources`").
			WillReturnRows(mockDb.NewRows([]string{"id", "name"}))

		mockDb.ExpectQuery("SELECT count(*) FROM `proactive_tasks` WHERE scan_plan != ? AND name LIKE ?").
			WillReturnError(errors.New("mock err"))

		mockDb.ExpectQuery("SELECT * FROM `proactive_tasks` WHERE scan_plan != ? AND name LIKE ? ORDER BY created_at DESC LIMIT 10").
			WithArgs("%@%").
			WillReturnRows(mockDb.NewRows([]string{"id", "name", "desc", "task_type", "source_id", "scan_plan", "scan_period", "scan_time", "status", "progress", "repeat_end_time", "begin_time", "end_time", "scan_mode", "use_seconds", "scan_result", "scan_ip_range_type", "scan_ip_ranges", "poc_scan_type", "selected_pocs", "scan_port", "scan_type", "bandwidth", "concurrency", "other_cfgs"}).
				AddRow(1, "Task1", "Description", 1, 1, 1, "weekly", "12:00", 1, 50, nil, nil, nil, 1, "60s", "result", "user_input", "**********/24", "all", nil, 80, "quick", 100, 5, "{}"))

		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/v1/task_center/proactive_scan/scheduled_list?page=0&per_page=@&keyword=@", nil)
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := GetScheduledTasksList(c)

		assert.NotEmpty(t, err)
	})
}

// TestGetTaskDetail 任务详情 - 测试
func TestGetTaskDetail(t *testing.T) {
	t.Run("Err id < 0", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()

		mockDb.ExpectQuery("SELECT * FROM `proactive_tasks` WHERE id = ? ORDER BY `proactive_tasks`.`id` LIMIT 1").
			WithArgs(int64(1)).
			WillReturnRows(mockDb.NewRows([]string{
				"id", "name", "desc", "task_type", "source_id", "scan_plan", "scan_period", "scan_time",
				"status", "progress", "repeat_end_time", "begin_time", "end_time", "scan_mode", "use_seconds",
				"scan_result", "scan_ip_range_type", "scan_ip_ranges", "poc_scan_type", "selected_pocs",
				"scan_port", "scan_type", "bandwidth", "concurrency", "other_cfgs",
			}))

		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/v1/task_center/proactive_scan?task_id=-1", nil)
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		GetTaskDetail(c)

		//expected := `{"code":0,"message":"Success","data":{"created_at":"2024-09-12 15:08:25","updated_at":"2024-09-12 15:08:25","id":22,"name":"漏洞扫描任务6","desc":"测试漏洞扫描任务~~~","task_type":33,"source_id":40,"scan_plan":1,"scan_period":"Ut","scan_time":"2021-04-27 18:25:51","status":1,"progress":0,"repeat_end_time":"1977-12-17 21:50:29","begin_time":"2024-09-10 15:21:54","end_time":"2024-09-12 15:21:55","scan_mode":1,"use_seconds":"3600","scan_result":"扫描结果: 发现8个漏洞。","scan_ip_range_type":"user_input","ScanIPRanges":["**********/24"],"scan_ip_ranges":"[\"**********/24\"]","poc_scan_type":"all","selected_pocs":"poc1","scan_port":40,"scan_type":"quick","bandwidth":100,"concurrency":90,"OtherCfgs":{"is_deep_scan":"minim","is_ping_recognition":"cupidatat"},"other_cfgs":"{\"is_deep_scan\":\"minim\",\"is_ping_recognition\":\"cupidatat\"}"}}`
		//assert.JSONEq(t, expected, w.Body.String())
	})

	t.Run("Success", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()

		mockDb.ExpectQuery("SELECT * FROM `proactive_tasks` WHERE id = ? ORDER BY `proactive_tasks`.`id` LIMIT 1").
			WithArgs(int64(22)).
			WillReturnRows(mockDb.NewRows([]string{
				"id", "name", "desc", "task_type", "source_id", "scan_plan", "scan_period", "scan_time",
				"status", "progress", "scan_mode", "use_seconds",
				"scan_result", "scan_ip_range_type", "scan_ip_ranges", "poc_scan_type", "selected_pocs",
				"scan_port", "scan_type", "bandwidth", "concurrency", "other_cfgs",
			}).AddRow(22, "Task Name", "Task Desc", 1, 1, 1, "daily", "10:00", 1, 50, 1, "3600", "scan result", "user_input", "[\"**********/24\"]", "special", "poc1,poc2", 80, "quick", 1000, 10, `{"key":"value"}`))

		mockDb.ExpectQuery("SELECT name,`type` FROM `data_sources` WHERE id = ?").
			WithArgs(1).
			WillReturnRows(sqlmock.NewRows([]string{"name", "type"}).AddRow("钉钉", "dingtalk"))

		mockDb.ExpectQuery("SELECT `username` FROM `users` WHERE id = ?").
			WithArgs(int64(0)).
			WillReturnRows(mockDb.NewRows([]string{"username"}).AddRow("User Name"))

		mockDb.ExpectQuery("SELECT node_id FROM `proactive_task_node_relations` WHERE `task_id` IN (?)").
			WithArgs(uint64(22)).
			WillReturnRows(mockDb.NewRows([]string{"node_id"}).AddRow("22"))

		mockDb.ExpectQuery("SELECT `scan_plan`,`scan_period`,`scan_time` FROM `proactive_tasks` WHERE id = ? ORDER BY `proactive_tasks`.`scan_plan` LIMIT 1").
			WithArgs(22).
			WillReturnRows(sqlmock.NewRows([]string{"scan_plan", "scan_period", "scan_time"}).AddRow(1, "", ""))

		mockDb.ExpectQuery("SELECT * FROM `crontab` WHERE params = ? AND method = ?").
			WithArgs(`{"taskId":22}`, "MonitorTaskToStart").
			WillReturnRows(sqlmock.NewRows([]string{"spec"}).AddRows([]driver.Value{"00 04 04 * * 3"}, []driver.Value{"00 07 07 * * 6"}))

		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/v1/task_center/proactive_scan?task_id=22", nil)
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := GetTaskDetail(c)
		assert.Nil(t, err)
	})

	t.Run("Err", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()

		mockDb.ExpectQuery("SELECT * FROM `proactive_tasks` WHERE id = ? ORDER BY `proactive_tasks`.`id` LIMIT 1").
			WithArgs(int64(22)).
			WillReturnError(errors.New("Err"))

		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/v1/task_center/proactive_scan?task_id=22", nil)
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		GetTaskDetail(c)

	})

	t.Run("Err", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()

		mockDb.ExpectQuery("SELECT * FROM `proactive_tasks` WHERE id = ? ORDER BY `proactive_tasks`.`id` LIMIT 1").
			WithArgs("#").
			WillReturnError(errors.New("err"))

		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/v1/task_center/proactive_scan?task_id=#", nil)
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		GetTaskDetail(c)
	})
}

func TestPortGroupList(t *testing.T) {
	gin.SetMode(gin.TestMode)
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)

	err := PortGroupList(c)

	assert.Empty(t, err)
	assert.Equal(t, http.StatusOK, w.Code)

}

// TestRetryTask 再次执行任务 - 测试
func TestRetryTask(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()
		mockDb.ExpectQuery("SELECT * FROM `proactive_tasks` WHERE id = ? ORDER BY `proactive_tasks`.`id` LIMIT 1").
			WithArgs(int64(22)).
			WillReturnRows(mockDb.NewRows([]string{
				"id", "name", "desc", "task_type", "source_id", "scan_plan", "scan_period", "scan_time",
				"status", "progress", "scan_mode", "use_seconds",
				"scan_result", "scan_ip_range_type", "scan_ip_ranges", "poc_scan_type", "selected_pocs",
				"scan_port", "scan_type", "bandwidth", "concurrency", "other_cfgs",
			}).AddRow(22, "Task Name", "Task Desc", 1, 1, 1, "daily", "10:00", 1, 50, 1, "3600", "scan result", "user_input", "[\"**********/24\"]", "special", "poc1,poc2", 80, "quick", 1000, 10, `{"key":"value"}`))
		mockDb.ExpectQuery("SELECT `name` FROM `data_sources` WHERE id = ? ").
			WithArgs(int64(1)).
			WillReturnRows(sqlmock.NewRows([]string{"name"}).AddRow(1))

		mockDb.ExpectQuery("SELECT `username` FROM `users` WHERE id = ?").
			WithArgs(int64(0)).
			WillReturnRows(sqlmock.NewRows([]string{"name"}).AddRow(1))

		mockDb.ExpectQuery("SELECT `node_id` FROM `proactive_task_node_relations` WHERE task_id = ? ").
			WithArgs(int64(22)).
			WillReturnRows(sqlmock.NewRows([]string{"node_id"}).AddRow(1))

		mockDb.ExpectQuery("SELECT * FROM `data_node_configs` WHERE `node_id` = ?").WithArgs(int64(2)).
			WillReturnRows(mockDb.NewRows([]string{"id", "node_id", "key", "value"}).AddRow(1, 1, "scan_mode", 1))

		mockDb.Mock.ExpectBegin()
		mockDb.ExpectExec(
			"INSERT INTO `proactive_tasks` (`created_at`,`updated_at`,`name`,`desc`,`task_type`,`source_id`,`scan_plan`,`scan_period`,`scan_time`,`status`,`progress`,`repeat_end_time`,`begin_time`,`end_time`,`scan_mode`,`use_seconds`,`scan_result`,`scan_ip_range_type`,`scan_ip_ranges`,`poc_scan_type`,`selected_pocs`,`scan_port`,`scan_type`,`bandwidth`,`concurrency`,`other_cfgs`,`user_id`,`is_start`,`sync_status`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)",
		).WillReturnResult(sqlmock.NewResult(1, 1))

		mockDb.ExpectExec(
			"INSERT INTO `proactive_task_node_relations` (`created_at`,`updated_at`,`task_id`,`node_id`,`node_task_id`,`source_id`,`state`,`progress`,`node_task_result`,`node_task_use`,`asset_sum`,`threat_sum`,`sync_asset_status`,`sync_threat_status`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?)",
		).WillReturnResult(sqlmock.NewResult(1, 1))
		mockDb.Mock.ExpectCommit()

		gin.SetMode(gin.TestMode)
		w := httptest.NewRecorder()
		req := httptest.NewRequest("POST", "/api/v1/task_center/proactive_scan/foeye/retry?task_id=22", nil)

		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := RetryTask(c)
		assert.NotEmpty(t, err)
		//assert.Empty(t, err)
		//assert.Equal(t, http.StatusOK, w.Code)
	})

	t.Run("err request taskInfo", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()
		mockDb.ExpectQuery("SELECT * FROM `proactive_tasks` WHERE id = ? ORDER BY `proactive_tasks`.`id` LIMIT 1").
			WithArgs(int64(22)).
			WillReturnError(errors.New("err request taskInfo"))

		gin.SetMode(gin.TestMode)
		w := httptest.NewRecorder()
		req := httptest.NewRequest("POST", "/api/v1/task_center/proactive_scan/foeye/retry?task_id=22", nil)

		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := RetryTask(c)
		assert.NotEmpty(t, err)
	})

	t.Run("err request node", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()
		mockDb.ExpectQuery("SELECT * FROM `proactive_tasks` WHERE id = ? ORDER BY `proactive_tasks`.`id` LIMIT 1").
			WithArgs(int64(22)).
			WillReturnRows(mockDb.NewRows([]string{
				"id", "name", "desc", "task_type", "source_id", "scan_plan", "scan_period", "scan_time",
				"status", "progress", "scan_mode", "use_seconds",
				"scan_result", "scan_ip_range_type", "scan_ip_ranges", "poc_scan_type", "selected_pocs",
				"scan_port", "scan_type", "bandwidth", "concurrency", "other_cfgs",
			}).AddRow(22, "Task Name", "Task Desc", 1, 1, 1, "daily", "10:00", 1, 50, 1, "3600", "scan result", "user_input", "[\"**********/24\"]", "special", "poc1,poc2", 80, "quick", 1000, 10, `{"key":"value"}`))
		mockDb.ExpectQuery("SELECT `name` FROM `data_sources` WHERE id = ? ").
			WithArgs(int64(1)).
			WillReturnRows(sqlmock.NewRows([]string{"name"}).AddRow(1))

		mockDb.ExpectQuery("SELECT `username` FROM `users` WHERE id = ?").
			WithArgs(int64(0)).
			WillReturnRows(sqlmock.NewRows([]string{"name"}).AddRow(1))
		mockDb.ExpectQuery("SELECT `node_id` FROM `proactive_task_node_relations` WHERE task_id = ? ").
			WithArgs(int64(22)).
			WillReturnError(errors.New("找不到节点"))

		gin.SetMode(gin.TestMode)
		w := httptest.NewRecorder()
		req := httptest.NewRequest("POST", "/api/v1/task_center/proactive_scan/foeye/retry?task_id=22", nil)

		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := RetryTask(c)
		assert.NotEmpty(t, err)
	})

	t.Run("err request", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()
		mockDb.ExpectQuery("SELECT * FROM `proactive_tasks` WHERE id = ? ORDER BY `proactive_tasks`.`id` LIMIT 1").
			WithArgs("#").
			WillReturnError(errors.New("入参绑定错误"))

		gin.SetMode(gin.TestMode)
		w := httptest.NewRecorder()
		req := httptest.NewRequest("POST", "/api/v1/task_center/proactive_scan/foeye/retry?task_id=#", nil)

		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := RetryTask(c)
		assert.NotEmpty(t, err)
	})

}

// TestUpdateTask 更新任务 - 测试
func TestUpdateTask(t *testing.T) {
	t.Run("Request Err", func(t *testing.T) {
		gin.SetMode(gin.TestMode)
		w := httptest.NewRecorder()
		req := httptest.NewRequest("PUT", "/api/v1/task_center/proactive_scan/foeye/update?name=test&desc=test&task_type=1&source_id=1&scan_plan=1&scan_period=1&scan_time=1&repeat_end_time=1&scan_mode=1&scan_ip_range_type=1&scan_ip_ranges=1&poc_scan_type=1&selected_pocs=1&scan_port=1&scan_type=1&bandwidth=1&concurrency=1&other_cfgs={}&data_node_ids=1", nil)
		c, _ := gin.CreateTestContext(w)
		c.Request = req
		err := UpdateTask(c)
		assert.NotEmpty(t, err)
	})
	t.Run("success", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()
		mockDb.ExpectQuery("SELECT value FROM `system_configs` WHERE `key` = ?").WithArgs(sqlmock.AnyArg()).
			WillReturnRows(mockDb.NewRows([]string{"value"}).AddRow(`{"enable_ban": false, "week_days": [], "ban_periods": [], "banned_assets": []}`))

		mockDb.ExpectQuery("SELECT value FROM `system_configs` WHERE `key` = ?").WithArgs(sqlmock.AnyArg()).
			WillReturnRows(mockDb.NewRows([]string{"value"}).AddRow(`{"enable_ban": false, "week_days": [], "ban_periods": [], "banned_assets": []}`))

		mockDb.ExpectQuery("SELECT * FROM `data_node_configs` WHERE `node_id` = ?").WithArgs(int64(2)).
			WillReturnRows(mockDb.NewRows([]string{"id", "node_id", "key", "value"}).AddRow(1, 1, "scan_mode", 1))

		mockDb.Mock.ExpectBegin()

		mockDb.ExpectQuery("SELECT `created_at` FROM `proactive_tasks` WHERE id = ?").WithArgs(int64(55)).
			WillReturnRows(mockDb.NewRows([]string{"created_at"}).AddRow(time.Time{}))

		mockDb.ExpectExec(
			"DELETE FROM `proactive_task_node_relations` WHERE task_id = ?",
		).WithArgs(55).WillReturnResult(sqlmock.NewResult(0, 1))

		mockDb.ExpectExec(
			"UPDATE `proactive_tasks` SET `created_at`=?,`updated_at`=?,`name`=?,`desc`=?,`task_type`=?,`source_id`=?,`scan_plan`=?,`scan_period`=?,`scan_time`=?,`status`=?,`progress`=?,`repeat_end_time`=?,`begin_time`=?,`end_time`=?,`scan_mode`=?,`use_seconds`=?,`scan_result`=?,`scan_ip_range_type`=?,`scan_ip_ranges`=?,`poc_scan_type`=?,`selected_pocs`=?,`scan_port`=?,`scan_type`=?,`bandwidth`=?,`concurrency`=?,`other_cfgs`=?,`user_id`=?,`is_start`=?,`sync_status`=? WHERE `id` = ?",
		).WillReturnResult(sqlmock.NewResult(0, 1))

		mockDb.ExpectExec(
			"INSERT INTO `proactive_task_node_relations` (`created_at`,`updated_at`,`task_id`,`node_id`,`node_task_id`,`source_id`,`state`,`progress`,`node_task_result`,`node_task_use`,`asset_sum`,`threat_sum`,`sync_asset_status`,`sync_threat_status`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?)",
		).WillReturnResult(sqlmock.NewResult(1, 1))
		mockDb.Mock.ExpectCommit()

		gin.SetMode(gin.TestMode)
		w := httptest.NewRecorder()
		req := httptest.NewRequest("PUT", "/api/v1/task_center/proactive_scan/foeye/update?id=55&name=test&desc=test&task_type=1&source_id=1&scan_plan=1&scan_period=1&scan_time=1&repeat_end_time=1&scan_mode=1&scan_ip_range_type=1&scan_ip_ranges=1&poc_scan_type=1&selected_pocs=1&scan_port=1&scan_type=1&bandwidth=1&concurrency=1&other_cfgs={}&data_node_ids=1", nil)

		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := UpdateTask(c)
		assert.NotEmpty(t, err)
	})
}

// TestStartTask 立即执行任务 - 测试
func TestStartTask(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()
		mockDb.ExpectQuery("SELECT * FROM `proactive_tasks` WHERE id = ? ORDER BY `proactive_tasks`.`id` LIMIT 1").
			WithArgs(int64(22)).
			WillReturnRows(mockDb.NewRows([]string{
				"id", "name", "desc", "task_type", "source_id", "scan_plan", "scan_period", "scan_time",
				"status", "progress", "scan_mode", "use_seconds",
				"scan_result", "scan_ip_range_type", "scan_ip_ranges", "poc_scan_type", "selected_pocs",
				"scan_port", "scan_type", "bandwidth", "concurrency", "other_cfgs",
			}).AddRow(22, "Task Name", "Task Desc", 1, 1, 1, "daily", "10:00", 1, 50, 1, "3600", "scan result", "user_input", "[\"**********/24\"]", "special", "poc1,poc2", 80, "quick", 1000, 10, `{"key":"value"}`))

		mockDb.ExpectQuery("SELECT `name` FROM `data_sources` WHERE id = ? ").
			WithArgs(int64(1)).
			WillReturnRows(sqlmock.NewRows([]string{"name"}).AddRow(1))

		mockDb.ExpectQuery("SELECT `username` FROM `users` WHERE id = ?").
			WithArgs(int64(0)).
			WillReturnRows(sqlmock.NewRows([]string{"name"}).AddRow(1))

		mockDb.ExpectQuery("SELECT `node_id` FROM `proactive_task_node_relations` WHERE task_id = ? ").
			WithArgs(int64(22)).
			WillReturnRows(sqlmock.NewRows([]string{"node_id"}).AddRow(1))

		mockDb.ExpectQuery("SELECT * FROM `data_node_configs` WHERE `node_id` = ?").WithArgs(int64(2)).
			WillReturnRows(mockDb.NewRows([]string{"id", "node_id", "key", "value"}).AddRow(1, 1, "scan_mode", 1))
		mockDb.Mock.ExpectBegin()
		mockDb.ExpectExec(
			"INSERT INTO `proactive_tasks` (`created_at`,`updated_at`,`name`,`desc`,`task_type`,`source_id`,`scan_plan`,`scan_period`,`scan_time`,`status`,`progress`,`repeat_end_time`,`begin_time`,`end_time`,`scan_mode`,`use_seconds`,`scan_result`,`scan_ip_range_type`,`scan_ip_ranges`,`poc_scan_type`,`selected_pocs`,`scan_port`,`scan_type`,`bandwidth`,`concurrency`,`other_cfgs`,`user_id`,`is_start`,`sync_status`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)",
		).WillReturnResult(sqlmock.NewResult(1, 1))

		mockDb.ExpectExec(
			"INSERT INTO `proactive_task_node_relations` (`created_at`,`updated_at`,`task_id`,`node_id`,`node_task_id`,`source_id`,`state`,`progress`,`node_task_result`,`node_task_use`,`asset_sum`,`threat_sum`,`sync_asset_status`,`sync_threat_status`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?)",
		).WillReturnResult(sqlmock.NewResult(1, 1))
		mockDb.Mock.ExpectCommit()

		gin.SetMode(gin.TestMode)
		w := httptest.NewRecorder()
		req := httptest.NewRequest("POST", "/api/v1/task_center/proactive_scan/foeye/start?task_id=22", nil)

		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := StartTask(c)
		assert.NotEmpty(t, err)
	})

	t.Run("err request taskInfo", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()
		mockDb.ExpectQuery("SELECT * FROM `proactive_tasks` WHERE id = ? ORDER BY `proactive_tasks`.`id` LIMIT 1").
			WithArgs(int64(22)).
			WillReturnError(errors.New("查询任务详情失败"))

		gin.SetMode(gin.TestMode)
		w := httptest.NewRecorder()
		req := httptest.NewRequest("POST", "/api/v1/task_center/proactive_scan/foeye/start?task_id=22", nil)

		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := StartTask(c)
		assert.NotEmpty(t, err)
	})

	t.Run("err request node", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()
		mockDb.ExpectQuery("SELECT * FROM `proactive_tasks` WHERE id = ? ORDER BY `proactive_tasks`.`id` LIMIT 1").
			WithArgs(int64(22)).
			WillReturnRows(mockDb.NewRows([]string{
				"id", "name", "desc", "task_type", "source_id", "scan_plan", "scan_period", "scan_time",
				"status", "progress", "scan_mode", "use_seconds",
				"scan_result", "scan_ip_range_type", "scan_ip_ranges", "poc_scan_type", "selected_pocs",
				"scan_port", "scan_type", "bandwidth", "concurrency", "other_cfgs",
			}).AddRow(22, "Task Name", "Task Desc", 1, 1, 1, "daily", "10:00", 1, 50, 1, "3600", "scan result", "user_input", "[\"**********/24\"]", "special", "poc1,poc2", 80, "quick", 1000, 10, `{"key":"value"}`))
		mockDb.ExpectQuery("SELECT `name` FROM `data_sources` WHERE id = ? ").
			WithArgs(int64(1)).
			WillReturnRows(sqlmock.NewRows([]string{"name"}).AddRow(1))

		mockDb.ExpectQuery("SELECT `username` FROM `users` WHERE id = ?").
			WithArgs(int64(0)).
			WillReturnRows(sqlmock.NewRows([]string{"name"}).AddRow(1))
		mockDb.ExpectQuery("SELECT `node_id` FROM `proactive_task_node_relations` WHERE task_id = ? ").
			WithArgs(int64(22)).
			WillReturnError(errors.New("找不到节点"))

		gin.SetMode(gin.TestMode)
		w := httptest.NewRecorder()
		req := httptest.NewRequest("POST", "/api/v1/task_center/proactive_scan/foeye/start?task_id=22", nil)

		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := StartTask(c)
		assert.NotEmpty(t, err)
	})

	t.Run("err request", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()
		mockDb.ExpectQuery("SELECT * FROM `proactive_tasks` WHERE id = ? ORDER BY `proactive_tasks`.`id` LIMIT 1").
			WithArgs("#").
			WillReturnError(errors.New("入参绑定错误"))

		gin.SetMode(gin.TestMode)
		w := httptest.NewRecorder()
		req := httptest.NewRequest("POST", "/api/v1/task_center/proactive_scan/foeye/start?task_id=#", nil)

		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := StartTask(c)
		assert.NotEmpty(t, err)
	})

	t.Run("err response", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()
		mockDb.ExpectQuery("SELECT * FROM `proactive_tasks` WHERE id = ? ORDER BY `proactive_tasks`.`id` LIMIT 1").
			WithArgs(int64(22)).
			WillReturnRows(mockDb.NewRows([]string{
				"id", "name", "desc", "task_type", "source_id", "scan_plan", "scan_period", "scan_time",
				"status", "progress", "scan_mode", "use_seconds",
				"scan_result", "scan_ip_range_type", "scan_ip_ranges", "poc_scan_type", "selected_pocs",
				"scan_port", "scan_type", "bandwidth", "concurrency", "other_cfgs",
			}).AddRow(22, "Task Name", "Task Desc", 1, 1, 1, "daily", "10:00", 1, 50, 1, "3600", "scan result", "user_input", "[\"**********/24\"]", "special", "poc1,poc2", 80, "quick", 1000, 10, `{"key":"value"}`))

		mockDb.ExpectQuery("SELECT `name` FROM `data_sources` WHERE id = ? ").
			WithArgs(int64(1)).
			WillReturnRows(sqlmock.NewRows([]string{"name"}).AddRow(1))

		mockDb.ExpectQuery("SELECT `username` FROM `users` WHERE id = ?").
			WithArgs(int64(0)).
			WillReturnRows(sqlmock.NewRows([]string{"name"}).AddRow(1))

		mockDb.ExpectQuery("SELECT `node_id` FROM `proactive_task_node_relations` WHERE task_id = ? ").
			WithArgs(int64(22)).
			WillReturnRows(sqlmock.NewRows([]string{"node_id"}).AddRow(1))

		mockDb.ExpectQuery("SELECT * FROM `data_node_configs` WHERE `node_id` = ?").WithArgs(int64(2)).
			WillReturnRows(mockDb.NewRows([]string{"id", "node_id", "key", "value"}).AddRow(1, 1, "scan_mode", 1))
		mockDb.Mock.ExpectBegin()
		mockDb.ExpectExec(
			"INSERT INTO `proactive_tasks` (`created_at`,`updated_at`,`name`,`desc`,`task_type`,`source_id`,`scan_plan`,`scan_period`,`scan_time`,`status`,`progress`,`repeat_end_time`,`begin_time`,`end_time`,`scan_mode`,`use_seconds`,`scan_result`,`scan_ip_range_type`,`scan_ip_ranges`,`poc_scan_type`,`selected_pocs`,`scan_port`,`scan_type`,`bandwidth`,`concurrency`,`other_cfgs`,`user_id`,`is_start`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)",
		).WillReturnResult(sqlmock.NewResult(1, 1))

		mockDb.ExpectExec(
			"INSERT INTO `proactive_task_node_relations` (`created_at`,`updated_at`,`task_id`,`node_id`,`node_task_id`,`source_id`,`state`,`progress`,`node_task_result`,`node_task_use`,`asset_sum`,`threat_sum`,`sync_asset_status`,`sync_threat_status`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?)",
		).WillReturnError(errors.New("response err"))
		mockDb.Mock.ExpectCommit()

		gin.SetMode(gin.TestMode)
		w := httptest.NewRecorder()
		req := httptest.NewRequest("POST", "/api/v1/task_center/proactive_scan/foeye/start?task_id=22", nil)

		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := StartTask(c)
		assert.NotEmpty(t, err)
	})
}

// TestIsOpenTask 开启/关闭任务 - 测试
func TestIsOpenTask(t *testing.T) {
	t.Run("Success", func(t *testing.T) {
		mockDb := testcommon.GetMysqlMock()
		defer mockDb.Close()
		mockDb.ExpectBegin()
		mockDb.ExpectExec("UPDATE `proactive_tasks` SET `is_start`=CASE WHEN is_start = 1 THEN 0 ELSE 1 END WHERE id = ?").WithArgs(int64(2)).
			WillReturnResult(sqlmock.NewResult(0, 2))
		mockDb.ExpectCommit()
		w := httptest.NewRecorder()
		req := httptest.NewRequest("PUT", "/task_center/proactive_scan/foeye/is_open?task_id=2", nil)

		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := IsOpenTask(c)
		assert.NotEmpty(t, err)
	})

	t.Run("Err", func(t *testing.T) {
		mockDb := testcommon.GetMysqlMock()
		defer mockDb.Close()

		mockDb.ExpectExec("UPDATE `proactive_tasks` SET `is_start` = CASE WHEN `is_start` = 1 THEN 0 ELSE 1 END WHERE `id` = #").
			WillReturnError(errors.New("入参错误"))

		w := httptest.NewRecorder()
		req := httptest.NewRequest("PUT", "/task_center/proactive_scan/foeye/is_open?task_id=#", nil)

		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := IsOpenTask(c)
		assert.NotEmpty(t, err)
	})
}

func TestDelTask(t *testing.T) {
	mockDb := testcommon.InitSqlMock()

	rows := sqlmock.NewRows([]string{
		"id", "name", "desc", "task_type", "source_id", "scan_plan", "status", "progress",
		"scan_result", "scan_type", "scan_port", "user_id", "is_start", "sync_status", "scan_ip_range",
		"scan_ip_ranges", "other_cfgs", "scan_period", "scan_time", "repeat_end_time", "begin_time", "end_time",
		"scan_mode", "use_seconds", "poc_scan_type", "selected_pocs", "bandwidth", "concurrency",
		"ports_num", "rule_infos_num", "companies_num", "task_plan",
	}).
		AddRow(
			1,
			"Task Name",
			"Description",
			1,
			1,
			3,
			4,
			99.5,
			"scan_result_data",
			"quick",
			8080,
			1,
			1,
			2,
			`["**********/24", "***********/24"]`,
			`["**********/24", "***********/24"]`,
			`{"key1": "value1", "key2": "value2"}`,
			"Monday",
			"10:00",
			"2024-12-31T23:59:59",
			"2024-12-01T10:00:00",
			"2024-12-01T12:00:00",
			1,
			"3600",
			"all",
			"POC1, POC2",
			100,
			10,
			10,
			5,
			3,
			"Plan A",
		)

	mockDb.ExpectQuery("SELECT * FROM `proactive_tasks` WHERE id =? ORDER BY `proactive_tasks`.`id` LIMIT 1").
		WithArgs(1).WillReturnRows(rows)

	mockDb.ExpectQuery("SELECT id, node_id, node_task_id FROM `proactive_task_node_relations` WHERE task_id = ?").
		WithArgs(1).WillReturnRows(sqlmock.NewRows([]string{"id", "node_id", "node_task_id"}).AddRow(1, 1, 1))

	mockDb.ExpectQuery("SELECT * FROM `data_node_configs` WHERE `node_id` = ?").
		WithArgs(1).WillReturnRows(sqlmock.NewRows([]string{"id", "node_id", "node_task_id"}).
		AddRow(1, 1, 1))

	mockDb.ExpectBegin()
	mockDb.ExpectExec(`
		DELETE pr 
		FROM proactive_task_node_relations pr
		LEFT JOIN proactive_tasks pt ON pr.task_id = pt.id
		WHERE pt.id IN (?)
	`).
		WithArgs(1).WillReturnResult(sqlmock.NewResult(1, 1))
	mockDb.ExpectCommit()

	w := httptest.NewRecorder()
	params := struct {
		Ids        []uint64 `json:"ids" uri:"ids" form:"ids"`
		DeleteType string   `json:"delete_type" uri:"delete_type" form:"delete_type"`
		Keyword    string   `json:"keyword" form:"keyword" uri:"keyword"`
	}{
		Ids:        []uint64{1},
		DeleteType: "cycle",
	}
	body, err := json.Marshal(params)
	assert.Nil(t, err)

	req := httptest.NewRequest("DELETE", "/api/v1/task_center/proactive_scan/foeye", strings.NewReader(string(body)))

	req.Header.Set("Content-Type", "application/json")
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	err = DelTask(c)

	mockDb.Close()
}

func TestHasIntervalLessThanTwoHours(t *testing.T) {
	// 测试用例1: 存在两个时间间隔小于2小时
	configs1 := []Config{
		{ScanPeriod: "1", ScanTime: "08:00"},
		{ScanPeriod: "1", ScanTime: "09:30"},
		{ScanPeriod: "1", ScanTime: "12:00"},
	}
	result1 := hasIntervalLessThanTwoHours(configs1)
	if !result1 {
		t.Errorf("Test case 1 failed: expected true, got false")
	}

	// 测试用例2: 所有时间间隔都大于等于2小时
	configs2 := []Config{
		{ScanPeriod: "1", ScanTime: "08:00"},
		{ScanPeriod: "1", ScanTime: "10:30"},
		{ScanPeriod: "1", ScanTime: "15:00"},
	}
	result2 := hasIntervalLessThanTwoHours(configs2)
	if result2 {
		t.Errorf("Test case 2 failed: expected false, got true")
	}

	// 测试用例3: 边界情况：恰好两个时间间隔为2小时
	configs3 := []Config{
		{ScanPeriod: "1", ScanTime: "08:00"},
		{ScanPeriod: "1", ScanTime: "10:00"},
		{ScanPeriod: "1", ScanTime: "12:00"},
	}
	result3 := hasIntervalLessThanTwoHours(configs3)
	if !result3 {
		t.Errorf("Test case 3 failed: expected true, got false")
	}

	// 测试用例4: 只有一个时间点，应该返回false
	configs4 := []Config{
		{ScanPeriod: "1", ScanTime: "08:00"},
	}
	result4 := hasIntervalLessThanTwoHours(configs4)
	if result4 {
		t.Errorf("Test case 4 failed: expected false, got true")
	}

	// 测试用例5：没有时间点，应该返回false
	configs5 := make([]Config, 0)
	result5 := hasIntervalLessThanTwoHours(configs5)
	if result5 {
		t.Errorf("Test case 5 failed: expected false, got true")
	}

	// 测试用例6: 相差一天，返回true
	configs6 := []Config{
		{ScanPeriod: "1", ScanTime: "08:00"},
		{ScanPeriod: "2", ScanTime: "10:00"},
	}
	result6 := hasIntervalLessThanTwoHours(configs6)
	if !result6 {
		t.Errorf("Test case 6 failed: expected true, got false")
	}

	// 测试用例7：相差两天，返回false
	configs7 := []Config{
		{ScanPeriod: "1", ScanTime: "08:00"},
		{ScanPeriod: "3", ScanTime: "08:00"},
	}
	result7 := hasIntervalLessThanTwoHours(configs7)
	if result7 {
		t.Errorf("Test case 7 failed: expected false, got true")
	}

	// 测试用例8: 28和1不可以相邻，返回true
	configs8 := []Config{
		{ScanPeriod: "1", ScanTime: "08:00"},
		{ScanPeriod: "28", ScanTime: "09:00"},
	}
	result8 := hasIntervalLessThanTwoHours(configs8)
	if !result8 {
		t.Errorf("Test case 8 failed: expected true, got false")
	}

	// 测试用例8: 30和1不可以相邻，返回true
	configs9 := []Config{
		{ScanPeriod: "1", ScanTime: "08:00"},
		{ScanPeriod: "30", ScanTime: "09:00"},
	}
	result9 := hasIntervalLessThanTwoHours(configs9)
	if !result9 {
		t.Errorf("Test case 9 failed: expected true, got false")
	}

	// 测试用例10: 30和1超过2小时可以相邻，返回false
	configs10 := []Config{
		{ScanPeriod: "1", ScanTime: "08:00"},
		{ScanPeriod: "30", ScanTime: "11:00"},
	}
	result10 := hasIntervalLessThanTwoHours(configs10)
	if result10 {
		t.Errorf("Test case 10 failed: expected false, got true")
	}

	// 测试用例11：1、28、30都超过了2小时，返回false
	configs11 := []Config{
		{ScanPeriod: "1", ScanTime: "08:00"},
		{ScanPeriod: "28", ScanTime: "11:00"},
		{ScanPeriod: "30", ScanTime: "14:00"},
	}
	result11 := hasIntervalLessThanTwoHours(configs11)
	if result11 {
		t.Errorf("Test case 11 failed: expected false, got true")
	}

	// 测试用例12：1、28、30都没超过了2小时，返回true
	configs12 := []Config{
		{ScanPeriod: "1", ScanTime: "08:00"},
		{ScanPeriod: "28", ScanTime: "9:00"},
		{ScanPeriod: "30", ScanTime: "10:00"},
	}
	result12 := hasIntervalLessThanTwoHours(configs12)
	if !result12 {
		t.Errorf("Test case 12 failed: expected true, got false")
	}

	configs13 := []Config{
		{ScanPeriod: "31", ScanTime: "23:41"},
		{ScanPeriod: "1", ScanTime: "00:57"},
	}
	result13 := hasIntervalLessThanTwoHours(configs13)
	if !result13 {
		t.Errorf("Test case 13 failed: expected true, got false")
	}
	configs14 := []Config{
		{ScanPeriod: "29", ScanTime: "23:41"},
		{ScanPeriod: "1", ScanTime: "00:57"},
	}
	result14 := hasIntervalLessThanTwoHours(configs14)
	if result14 {
		t.Errorf("Test case 14 failed: expected true, got false")
	}
}

func TestGenerateAndParseCronSpec(t *testing.T) {
	// 定义多个测试案例
	tests := []struct {
		cronSpec  string
		config    Config
		scanPlan  int
		expected  Config
		expectErr bool
		testName  string
	}{
		{
			// 每周扫描
			cronSpec:  "00 00 30 * * 1",
			config:    Config{ScanTime: "30:00", ScanPeriod: "1"},
			scanPlan:  proactive_scan.RunEveryWeek,
			expected:  Config{ScanTime: "30:00", ScanPeriod: "1"},
			expectErr: false,
			testName:  "WeeklyScan",
		},
		{
			// 每月15号扫描
			cronSpec:  "00 00 45 15 * *",
			config:    Config{ScanTime: "45:00", ScanPeriod: "15"},
			scanPlan:  proactive_scan.RunEveryMonth,
			expected:  Config{ScanTime: "45:00", ScanPeriod: "15"},
			expectErr: false,
			testName:  "MonthlyScan",
		},
		{
			// 每天扫描
			cronSpec:  "00 00 20 * * *",
			config:    Config{ScanTime: "20:00"},
			scanPlan:  proactive_scan.RunEveryDay,
			expected:  Config{ScanTime: "20:00"},
			expectErr: false,
			testName:  "DailyScan",
		},
		{
			// 指定日期时间运行一次
			cronSpec:  "00 00 10 25 12 *",
			config:    Config{ScanTime: "10:00", ScanPeriod: fmt.Sprintf("%s-12-25", time.Now().Format(localtime.YearFormat))},
			scanPlan:  proactive_scan.ExecuteOnceAt,
			expected:  Config{ScanTime: "10:00", ScanPeriod: fmt.Sprintf("%s-12-25", time.Now().Format(localtime.YearFormat))},
			expectErr: false,
			testName:  "SpecificDateScan",
		},
		// 无效的 Cron 表达式
		{
			cronSpec:  "* * * * *",
			config:    Config{},
			scanPlan:  0,
			expected:  Config{},
			expectErr: true,
			testName:  "InvalidCronFields",
		},
	}

	// 遍历测试案例
	for _, test := range tests {
		t.Run(test.testName, func(t *testing.T) {
			// 使用 generateCronSpec 生成 Cron 表达式
			cronSpec := generateCronSpec(test.config, test.scanPlan)

			// 验证生成的 Cron 表达式是否正确
			if cronSpec != test.cronSpec {
				t.Errorf("For %s, expected cron spec %s, but got %s", test.testName, test.cronSpec, cronSpec)
			}

			// 使用 parseCronSpec 解析 Cron 表达式
			parsedConfig, err := parseCronSpec(cronSpec, *localtime.Now())
			if err != nil && !test.expectErr {
				t.Fatalf("Failed to parse cron spec for %s: %v", test.testName, err)
			}

			// 验证解析后的配置是否匹配
			if parsedConfig != test.expected {
				t.Errorf("For %s, expected config %+v but got %+v", test.testName, test.expected, parsedConfig)
			}
		})
	}
}
