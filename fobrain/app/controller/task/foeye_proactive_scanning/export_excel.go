package foeye_proactive_scanning

import (
	"github.com/gin-gonic/gin"

	"fobrain/fobrain/app/repository/proactive_task/proactive_task_export"
	"fobrain/fobrain/common/request"
	"fobrain/fobrain/common/response"
)

type ExportDataRequest struct {
	Ids        []string `json:"ids" form:"ids" uri:"ids"`
	ExportType int      `json:"export_type" form:"export_type" uri:"export_type"`
	TaskId     int      `json:"task_id" form:"task_id" uri:"task_id"`
	SourceId   int      `json:"source_id" form:"source_id" uri:"source_id"`
}

type ExportTaskBulletinRequest struct {
	Id int64 `json:"id" form:"id" uri:"id"`
}

// ExportAsset 导出资产/漏洞列表
func ExportAsset(c *gin.Context) error {
	params, _ := request.Validate(c, &ExportDataRequest{})

	path, err := proactive_task_export.ExportDataExcel(params.Ids, params.ExportType, params.TaskId, params.SourceId)
	if err != nil {
		return err
	}

	return response.OkWithFile(c, path, true)
}

// ExportTaskBulletin 任务简报导出
func ExportTaskBulletin(c *gin.Context) error {
	params, _ := request.Validate(c, &ExportTaskBulletinRequest{})

	path, err := proactive_task_export.ExportTaskBulletin(params.Id)
	if err != nil {
		return err
	}

	return response.OkWithFile(c, path, true)
}
