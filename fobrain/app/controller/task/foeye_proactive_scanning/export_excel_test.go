package foeye_proactive_scanning

import (
	"net/http/httptest"
	"testing"
	"time"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"

	"fobrain/fobrain/app/repository/proactive_task/proactive_task_export"
)

func TestExportAsset(t *testing.T) {
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyFuncReturn(proactive_task_export.ExportDataExcel, "", nil).Reset()
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/task_center/proactive_scan/foeye/data_export?id=3", nil)

	c, _ := gin.CreateTestContext(w)
	c.Request = req
	err := ExportAsset(c)
	assert.NoError(t, err)
}

func TestExportTaskBulletin(t *testing.T) {
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyFuncReturn(proactive_task_export.ExportTaskBulletin, "", nil).Reset()
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/task_center/proactive_scan/foeye/export_task_bulletin?id=38", nil)

	c, _ := gin.CreateTestContext(w)
	c.Request = req
	err := ExportTaskBulletin(c)
	assert.NoError(t, err)
}
