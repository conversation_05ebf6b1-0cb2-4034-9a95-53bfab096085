package foeye_proactive_scanning

import (
	"fobrain/fobrain/app/repository/proactive_task/proactive_task_infos"
	"fobrain/fobrain/common/request"
	"fobrain/fobrain/common/response"
	"github.com/gin-gonic/gin"
)

// ResultRequest
// @Summary 主动扫描的结果请求体
type ResultRequest struct {
	request.PageRequest
	Keyword string `json:"keyword" form:"keyword" uri:"keyword" validate:"omitempty,max=100" zh:"模糊搜索"`
	Id      int64  `json:"id" form:"id" uri:"id" validate:"omitempty,min=1" zh:"列表 ID"`
}

var ResultRequestValidate = request.Validate[ResultRequest]

// Asset
// @Summary 主动扫描的资产结果
func Asset(ctx *gin.Context) error {
	params, err := ResultRequestValidate(ctx, &ResultRequest{})
	if err != nil {
		return err
	}

	data, total, listFields, listDetails, err := proactive_task_infos.AssetList(params.Id, params.Keyword, params.Page, params.PerPage)

	if err != nil {
		return err
	}
	return response.OkWithPageCustomData(ctx, total, params.Page, params.PerPage, data, listFields, listDetails)
}

// Threat
// @Summary 主动扫描的威胁结果
func Threat(ctx *gin.Context) error {
	params, err := ResultRequestValidate(ctx, &ResultRequest{})
	if err != nil {
		return err
	}

	data, total, listFields, listDetails, err := proactive_task_infos.ThreatList(params.Id, params.Keyword, params.Page, params.PerPage)

	if err != nil {
		return err
	}
	return response.OkWithPageCustomData(ctx, total, params.Page, params.PerPage, data, listFields, listDetails)
}
