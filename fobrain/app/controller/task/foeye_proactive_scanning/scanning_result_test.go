package foeye_proactive_scanning

import (
	testcommon "fobrain/fobrain/tests/common_test"
	"github.com/gin-gonic/gin"
	"net/http/httptest"
	"testing"
)

func TestAsset(t *testing.T) {

	mockDb := testcommon.InitSqlMock()
	defer mockDb.Close()
	mockDb.ExpectQuery("SELECT * FROM `proactive_tasks` WHERE id =? ORDER BY `proactive_tasks`.`id` LIMIT 1").
		WithArgs(12).
		WillReturnRows(mockDb.NewRows([]string{
			"id", "name", "desc", "task_type", "source_id", "scan_plan", "scan_period", "scan_time",
			"status", "progress", "scan_mode", "use_seconds",
			"scan_result", "scan_ip_range_type", "scan_ip_ranges", "poc_scan_type", "selected_pocs",
			"scan_port", "scan_type", "bandwidth", "concurrency", "other_cfgs",
		}).AddRow(3, "Task Name", "Task Desc", 1, 1, 1, "daily", "10:00", 1, 50, 1, "3600", "scan result", "user_input", "[\"**********/24\"]", "special", "poc1,poc2", 80, "quick", 1000, 10, `{"key":"value"}`))

	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/task_center/proactive_scan/foeye/asset_result?id=1&page=1&per_page=10", nil)

	c, _ := gin.CreateTestContext(w)
	c.Request = req

	Asset(c)
}
