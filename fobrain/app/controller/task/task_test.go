package task

import (
	"encoding/json"
	"fobrain/fobrain/app/services/task"
	"github.com/DATA-DOG/go-sqlmock"
	"github.com/agiledragon/gomonkey/v2"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"

	testcommon "fobrain/fobrain/tests/common_test"
)

func TestList(t *testing.T) {
	t.Run("NoSearch", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()
		mockDb.ExpectQuery("SELECT count(*) FROM `data_sync_tasks` WHERE `node_id` = ?").
			WillReturnRows(mockDb.NewRows([]string{"count"}).AddRow(1))

		mockDb.ExpectQuery("SELECT * FROM `data_sync_tasks` WHERE `node_id` = ? ORDER BY created_at desc LIMIT 10").
			WillReturnRows(mockDb.NewRows([]string{"id", "node_id"}).AddRow(1, 1))
		// 创建一个测试请求和响应
		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/v1/task/list?page=1&per_page=10&node_id=1", nil)
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		// 调用函数
		err := List(c)

		// 断言错误为 nil
		assert.Empty(t, err)
		assert.NotEmpty(t, w.Body.String())
	})

}

func TestChildList(t *testing.T) {
	mockDb := testcommon.InitSqlMock()
	defer mockDb.Close()
	mockDb.ExpectQuery("SELECT count(*) FROM `data_sync_child_tasks`").
		WillReturnRows(mockDb.NewRows([]string{"count"}).AddRow(1))

	// 创建一个测试请求和响应
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/task/child_list?page=1&per_page=10", nil)
	// 创建一个Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 调用函数
	err := ChildList(c)

	// 断言错误为 nil
	assert.Empty(t, err)
	assert.Equal(t, `{"code":0,"message":"Success","data":{"total":1,"page":1,"per_page":10,"items":null}}`, w.Body.String())
}

func TestSync(t *testing.T) {
	t.Run("node_id params err", func(t *testing.T) {
		// 创建一个测试请求和响应
		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/v1/task/sync", nil)
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		// 调用函数
		err := Sync(c)
		// 断言错误为 nil
		assert.NotNil(t, err)
		assert.Equal(t, `节点ID为必填字段`, err.Error())
	})
	t.Run("type params err", func(t *testing.T) {
		// 创建一个测试请求和响应
		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/v1/task/sync?node_id=1&type=4", nil)
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		// 调用函数
		err := Sync(c)
		// 断言错误为 nil
		assert.NotNil(t, err)
		assert.Equal(t, `任务同步数据类型必须是[0 1 2 3]中的一个`, err.Error())
	})
	t.Run("success", func(t *testing.T) {
		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/v1/task/sync?node_id=1&type=0", nil)
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		mockDb := testcommon.GetMysqlMock()
		defer mockDb.Close()

		mockDb.ExpectQuery("SELECT * FROM `data_nodes` WHERE id = ? AND `data_nodes`.`deleted_at` IS NULL ORDER BY `data_nodes`.`id` LIMIT 1").
			WithArgs(1).
			WillReturnRows(sqlmock.NewRows([]string{"types"}).AddRow("1,2"))

		patches := gomonkey.ApplyMethodReturn(task.NewSyncDataTask(), "Dispatch", nil)

		err := Sync(c)
		assert.Nil(t, err)

		patches.Reset()
	})
}

func TestSyncAll(t *testing.T) {
	t.Run("node_id params err", func(t *testing.T) {
		// 创建一个测试请求和响应
		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/v1/task/sync/all", nil)
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		// 调用函数
		err := SyncAll(c)
		// 断言错误为 nil
		assert.NotNil(t, err)
		assert.Equal(t, `任务同步数据类型为必填字段`, err.Error())
	})

	t.Run("pass", func(t *testing.T) {
		mockDb := testcommon.GetMysqlMock()
		defer mockDb.Close()
		mockDb.ExpectQuery("SELECT * FROM `data_sources` WHERE `has_sync` = ? AND has_asset_data = ?").
			WillReturnRows(mockDb.NewRows([]string{"id", "name"}).AddRow(1, "name"))
		mockDb.ExpectQuery("SELECT * FROM `data_nodes` WHERE `source_id` = ? AND `status` = ? AND deleted_at IS NULL AND `data_nodes`.`deleted_at` IS NULL").
			WillReturnRows(mockDb.NewRows([]string{"id", "name"}).AddRow(1, "name"))
		mockDb.ExpectQuery("SELECT * FROM `data_nodes` WHERE `id` = ? AND `data_nodes`.`deleted_at` IS NULL ORDER BY `data_nodes`.`id` LIMIT 1").
			WillReturnRows(mockDb.NewRows([]string{"id", "name"}).AddRow(1, "name"))
		mockDb.ExpectQuery("SELECT * FROM `data_sources` WHERE `id` = ? ORDER BY `data_sources`.`id` LIMIT 1").
			WillReturnRows(mockDb.NewRows([]string{"id", "name"}).AddRow(1, "name"))

		// 创建一个测试请求和响应
		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/v1/task/sync/all?types=1", nil)
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		// 调用函数
		err := SyncAll(c)
		// 断言错误为 nil
		assert.NotNil(t, err)
	})
}

func TestSyncFile(t *testing.T) {
	t.Run("node_id params err", func(t *testing.T) {
		// 创建一个测试请求和响应
		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/v1/task/sync", nil)
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		// 调用函数
		err := SyncFile(c)
		// 断言错误为 nil
		assert.NotNil(t, err)
		assert.Equal(t, `节点ID为必填字段`, err.Error())
	})
	t.Run("type params err", func(t *testing.T) {
		// 创建一个测试请求和响应
		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/v1/task/sync?node_id=1&type=4", nil)
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		// 调用函数
		err := SyncFile(c)
		// 断言错误为 nil
		assert.NotNil(t, err)
		assert.Equal(t, `任务同步数据类型必须是[0 1 2 3]中的一个`, err.Error())
	})
}

func TestSyncData(t *testing.T) {
	t.Run("node_api_unique params err", func(t *testing.T) {
		// 创建一个测试请求和响应
		w := httptest.NewRecorder()
		req := httptest.NewRequest("POST", "/api/v1/task/sync/data", nil)
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		// 调用函数
		err := SyncData(c)
		// 断言错误为 nil
		assert.NotNil(t, err)
		assert.Equal(t, `链接验证码为必填字段`, err.Error())
	})
	t.Run("type node_api_unique params err", func(t *testing.T) {
		val, _ := json.Marshal(SyncDataRequest{
			NodeApiUnique: "node_api_unique",
			SyncType:      1,
			Data:          []map[string]interface{}{},
		})
		// 创建一个测试请求和响应
		w := httptest.NewRecorder()
		req := httptest.NewRequest("POST", "/api/v1/task/sync/data", strings.NewReader(string(val)))
		req.Header.Set("Content-Type", "application/json")
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		// 调用函数
		err := SyncData(c)
		// 断言错误为 nil
		assert.NotNil(t, err)
		assert.Equal(t, `无效的链接验证码`, err.Error())
	})

	t.Run("count user  err", func(t *testing.T) {
		mockDb := testcommon.GetMysqlMock()
		defer mockDb.Close()

		mockDb.ExpectQuery("SELECT count(*) FROM `data_node_configs` WHERE value = ?").
			WillReturnRows(mockDb.NewRows([]string{"count"}).AddRow(1))

		val, _ := json.Marshal(SyncDataRequest{
			NodeApiUnique: "eyJpdiI6Iks5ZHQwR2VISmZ1cTNuN3N1RjNEV1E9PSIsIm1hYyI6IjYyYTBmNTk0MzQwMjU1NzkxYTVmOWViNDNjYTRlYTMyYjk4MDgyZmNiNTI2MzMzM2VlOGYwMTdmYzc3YzVjZDYiLCJ2YWx1ZSI6Ilg5aUk4U05KV2p5ZGNBTFBIOUFUT1htOTR0MUw5d0Q0M0g5SS93UmdRclU9In0=",
			SyncType:      1,
			Data:          []map[string]interface{}{},
		})
		// 创建一个测试请求和响应
		w := httptest.NewRecorder()
		req := httptest.NewRequest("POST", "/api/v1/task/sync/data", strings.NewReader(string(val)))
		req.Header.Set("Content-Type", "application/json")
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		// 调用函数
		err := SyncData(c)
		// 断言错误为 nil
		assert.NotNil(t, err)
		assert.Equal(t, `链接验证码用户不存在`, err.Error())
	})

	t.Run("count data_source  err", func(t *testing.T) {
		mockDb := testcommon.GetMysqlMock()
		defer mockDb.Close()

		mockDb.ExpectQuery("SELECT count(*) FROM `data_node_configs` WHERE value = ?").
			WillReturnRows(mockDb.NewRows([]string{"count"}).AddRow(1))

		mockDb.ExpectQuery("SELECT count(*) FROM `users` WHERE id = ?").
			WillReturnRows(mockDb.NewRows([]string{"count"}).AddRow(1))

		val, _ := json.Marshal(SyncDataRequest{
			NodeApiUnique: "eyJpdiI6Iks5ZHQwR2VISmZ1cTNuN3N1RjNEV1E9PSIsIm1hYyI6IjYyYTBmNTk0MzQwMjU1NzkxYTVmOWViNDNjYTRlYTMyYjk4MDgyZmNiNTI2MzMzM2VlOGYwMTdmYzc3YzVjZDYiLCJ2YWx1ZSI6Ilg5aUk4U05KV2p5ZGNBTFBIOUFUT1htOTR0MUw5d0Q0M0g5SS93UmdRclU9In0=",
			SyncType:      1,
			Data:          []map[string]interface{}{},
		})
		// 创建一个测试请求和响应
		w := httptest.NewRecorder()
		req := httptest.NewRequest("POST", "/api/v1/task/sync/data", strings.NewReader(string(val)))
		req.Header.Set("Content-Type", "application/json")
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		// 调用函数
		err := SyncData(c)
		// 断言错误为 nil
		assert.NotNil(t, err)
		assert.Equal(t, `链接验证码数据源不存在`, err.Error())
	})

	t.Run("count node  err", func(t *testing.T) {
		mockDb := testcommon.GetMysqlMock()
		defer mockDb.Close()

		mockDb.ExpectQuery("SELECT count(*) FROM `data_node_configs` WHERE value = ?").
			WillReturnRows(mockDb.NewRows([]string{"count"}).AddRow(1))

		mockDb.ExpectQuery("SELECT count(*) FROM `users` WHERE id = ?").
			WillReturnRows(mockDb.NewRows([]string{"count"}).AddRow(1))

		mockDb.ExpectQuery("SELECT count(*) FROM `data_sources` WHERE id = ?").
			WillReturnRows(mockDb.NewRows([]string{"count"}).AddRow(1))

		val, _ := json.Marshal(SyncDataRequest{
			NodeApiUnique: "eyJpdiI6Iks5ZHQwR2VISmZ1cTNuN3N1RjNEV1E9PSIsIm1hYyI6IjYyYTBmNTk0MzQwMjU1NzkxYTVmOWViNDNjYTRlYTMyYjk4MDgyZmNiNTI2MzMzM2VlOGYwMTdmYzc3YzVjZDYiLCJ2YWx1ZSI6Ilg5aUk4U05KV2p5ZGNBTFBIOUFUT1htOTR0MUw5d0Q0M0g5SS93UmdRclU9In0=",
			SyncType:      1,
			Data:          []map[string]interface{}{},
		})
		// 创建一个测试请求和响应
		w := httptest.NewRecorder()
		req := httptest.NewRequest("POST", "/api/v1/task/sync/data", strings.NewReader(string(val)))
		req.Header.Set("Content-Type", "application/json")
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		// 调用函数
		err := SyncData(c)
		// 断言错误为 nil
		assert.NotNil(t, err)
		assert.Equal(t, `链接验证码节点不存在`, err.Error())
	})

	t.Run("asset format err", func(t *testing.T) {
		mockDb := testcommon.GetMysqlMock()
		defer mockDb.Close()

		mockDb.ExpectQuery("SELECT count(*) FROM `data_node_configs` WHERE value = ?").
			WillReturnRows(mockDb.NewRows([]string{"count"}).AddRow(1))

		mockDb.ExpectQuery("SELECT count(*) FROM `users` WHERE id = ?").
			WillReturnRows(mockDb.NewRows([]string{"count"}).AddRow(1))

		mockDb.ExpectQuery("SELECT count(*) FROM `data_sources` WHERE id = ?").
			WillReturnRows(mockDb.NewRows([]string{"count"}).AddRow(1))

		mockDb.ExpectQuery("SELECT count(*) FROM `data_nodes` WHERE id = ?").
			WillReturnRows(mockDb.NewRows([]string{"count"}).AddRow(1))

		val, _ := json.Marshal(SyncDataRequest{
			NodeApiUnique: "eyJpdiI6Iks5ZHQwR2VISmZ1cTNuN3N1RjNEV1E9PSIsIm1hYyI6IjYyYTBmNTk0MzQwMjU1NzkxYTVmOWViNDNjYTRlYTMyYjk4MDgyZmNiNTI2MzMzM2VlOGYwMTdmYzc3YzVjZDYiLCJ2YWx1ZSI6Ilg5aUk4U05KV2p5ZGNBTFBIOUFUT1htOTR0MUw5d0Q0M0g5SS93UmdRclU9In0=",
			SyncType:      1,
			Data: []map[string]interface{}{
				{
					"area": "默认",
					"ip":   "1",
				},
			},
		})
		// 创建一个测试请求和响应
		w := httptest.NewRecorder()
		req := httptest.NewRequest("POST", "/api/v1/task/sync/data", strings.NewReader(string(val)))
		req.Header.Set("Content-Type", "application/json")
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		// 调用函数
		err := SyncData(c)
		// 断言错误为 nil
		assert.NotNil(t, err)
		assert.Equal(t, `第 1 行存在格式错误，错误信息：IP必须是一个有效的IP地址`, err.Error())
	})

	t.Run("threat format err", func(t *testing.T) {
		mockDb := testcommon.GetMysqlMock()
		defer mockDb.Close()

		mockDb.ExpectQuery("SELECT count(*) FROM `data_node_configs` WHERE value = ?").
			WillReturnRows(mockDb.NewRows([]string{"count"}).AddRow(1))

		mockDb.ExpectQuery("SELECT count(*) FROM `users` WHERE id = ?").
			WillReturnRows(mockDb.NewRows([]string{"count"}).AddRow(1))

		mockDb.ExpectQuery("SELECT count(*) FROM `data_sources` WHERE id = ?").
			WillReturnRows(mockDb.NewRows([]string{"count"}).AddRow(1))

		mockDb.ExpectQuery("SELECT count(*) FROM `data_nodes` WHERE id = ?").
			WillReturnRows(mockDb.NewRows([]string{"count"}).AddRow(1))

		val, _ := json.Marshal(SyncDataRequest{
			NodeApiUnique: "eyJpdiI6Iks5ZHQwR2VISmZ1cTNuN3N1RjNEV1E9PSIsIm1hYyI6IjYyYTBmNTk0MzQwMjU1NzkxYTVmOWViNDNjYTRlYTMyYjk4MDgyZmNiNTI2MzMzM2VlOGYwMTdmYzc3YzVjZDYiLCJ2YWx1ZSI6Ilg5aUk4U05KV2p5ZGNBTFBIOUFUT1htOTR0MUw5d0Q0M0g5SS93UmdRclU9In0=",
			SyncType:      2,
			Data: []map[string]interface{}{
				{
					"area": "默认",
					"ip":   "1",
				},
			},
		})
		// 创建一个测试请求和响应
		w := httptest.NewRecorder()
		req := httptest.NewRequest("POST", "/api/v1/task/sync/data", strings.NewReader(string(val)))
		req.Header.Set("Content-Type", "application/json")
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		// 调用函数
		err := SyncData(c)
		// 断言错误为 nil
		assert.NotNil(t, err)
		assert.Equal(t, `第 1 行存在格式错误，错误信息：IP必须是一个有效的IP地址`, err.Error())
	})

	t.Run("people format err", func(t *testing.T) {
		mockDb := testcommon.GetMysqlMock()
		defer mockDb.Close()

		mockDb.ExpectQuery("SELECT count(*) FROM `data_node_configs` WHERE value = ?").
			WillReturnRows(mockDb.NewRows([]string{"count"}).AddRow(1))

		mockDb.ExpectQuery("SELECT count(*) FROM `users` WHERE id = ?").
			WillReturnRows(mockDb.NewRows([]string{"count"}).AddRow(1))

		mockDb.ExpectQuery("SELECT count(*) FROM `data_sources` WHERE id = ?").
			WillReturnRows(mockDb.NewRows([]string{"count"}).AddRow(1))

		mockDb.ExpectQuery("SELECT count(*) FROM `data_nodes` WHERE id = ?").
			WillReturnRows(mockDb.NewRows([]string{"count"}).AddRow(1))

		val, _ := json.Marshal(SyncDataRequest{
			NodeApiUnique: "eyJpdiI6Iks5ZHQwR2VISmZ1cTNuN3N1RjNEV1E9PSIsIm1hYyI6IjYyYTBmNTk0MzQwMjU1NzkxYTVmOWViNDNjYTRlYTMyYjk4MDgyZmNiNTI2MzMzM2VlOGYwMTdmYzc3YzVjZDYiLCJ2YWx1ZSI6Ilg5aUk4U05KV2p5ZGNBTFBIOUFUT1htOTR0MUw5d0Q0M0g5SS93UmdRclU9In0=",
			SyncType:      3,
			Data: []map[string]interface{}{
				{
					"area":   "默认",
					"status": 100,
				},
			},
		})
		// 创建一个测试请求和响应
		w := httptest.NewRecorder()
		req := httptest.NewRequest("POST", "/api/v1/task/sync/data", strings.NewReader(string(val)))
		req.Header.Set("Content-Type", "application/json")
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		// 调用函数
		err := SyncData(c)
		// 断言错误为 nil
		assert.NotNil(t, err)
		assert.Equal(t, `第 1 行存在格式错误，错误信息：名称为必填字段`, err.Error())
	})
}
