package asset_mapping

import (
	"bytes"
	"encoding/json"
	"github.com/xuri/excelize/v2"
	"mime/multipart"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"

	testcommon "fobrain/fobrain/tests/common_test"
)

func TestList(t *testing.T) {

	t.Run("param err", func(t *testing.T) {
		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/v1/asset_mappings", nil)

		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := Index(c)
		assert.Error(t, err)
		assert.Equal(t, err.Error(), "页数为必填字段")
	})

	t.<PERSON>("success", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()
		mockDb.ExpectQuery("SELECT count(*) FROM `asset_mappings`").
			WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(1))
		mockDb.ExpectQuery("SELECT * FROM `asset_mappings` ORDER BY created_at DESC LIMIT 10").
			WillReturnRows(mockDb.NewRows([]string{"id", "name"}).AddRow(1, "name"))
		mockDb.ExpectQuery("SELECT * FROM `custom_fields` WHERE `table` = ?").
			WithArgs("asset_mappings").
			WillReturnRows(mockDb.NewRows([]string{"id", "name"}).AddRow(1, "name"))

		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/v1/asset_mappings?page=1&per_page=10&event=1", strings.NewReader(`{
			"event": 1,
    	}`))

		req.Header.Set("Content-Type", "application/json")
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := Index(c)
		assert.Nil(t, err)
		assert.NotEmpty(t, w.Body.String())
	})

}

func TestStore(t *testing.T) {
	t.Run("params err", func(t *testing.T) {
		// 创建一个测试请求和响应
		w := httptest.NewRecorder()
		req := httptest.NewRequest("POST", "/api/v1/asset_mappings", nil)
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		// 调用函数
		err := Store(c)

		assert.NotNil(t, err)
	})
	t.Run("pass", func(t *testing.T) {
		mockDb := testcommon.GetMysqlMock()
		defer mockDb.Close()
		mockDb.ExpectQuery("SELECT * FROM `asset_mappings` WHERE `host_ip` = ? AND `host_port` = ? LIMIT 1").
			WillReturnRows(mockDb.NewRows([]string{"id"}))

		mockDb.Mock.ExpectBegin()
		mockDb.ExpectExec(
			"INSERT INTO `asset_mappings` (`dmz_ip`,`dmz_port`,`finance_ip`,`finance_port`,`float_ip`,`float_port`,`host_ip`,`host_port`,`load_ip`,`load_port`,`ssl_ip`,`ssl_port`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?)",
		).WithArgs("***********", "50", "***********", "50", "***********", "50", "***********", "50", "***********", "50", "***********", "50").WillReturnResult(sqlmock.NewResult(1, 1))
		mockDb.Mock.ExpectCommit()

		val, err := json.Marshal(map[string]interface{}{
			"host_ip":      "***********",
			"host_port":    "50",
			"float_ip":     "***********",
			"float_port":   "50",
			"load_ip":      "***********",
			"load_port":    "50",
			"ssl_ip":       "***********",
			"ssl_port":     "50",
			"dmz_ip":       "***********",
			"dmz_port":     "50",
			"finance_ip":   "***********",
			"finance_port": "50",
		})

		// 创建一个测试请求和响应
		w := httptest.NewRecorder()
		req := httptest.NewRequest("POST", "/api/v1/asset_mappings", strings.NewReader(string(val)))
		req.Header.Set("Content-Type", "application/json")

		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		// 调用函数
		err = Store(c)

		assert.Nil(t, err)
	})
}

func TestUpdate(t *testing.T) {
	t.Run("params err", func(t *testing.T) {
		// 创建一个测试请求和响应
		w := httptest.NewRecorder()
		req := httptest.NewRequest("PUT", "/api/v1/asset_mappings", nil)
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		// 调用函数
		err := Update(c)
		assert.NotNil(t, err)
	})
	t.Run("pass", func(t *testing.T) {

		mockDb := testcommon.GetMysqlMock()
		defer mockDb.Close()

		mockDb.ExpectQuery("SELECT * FROM `asset_mappings` WHERE `host_ip` = ? AND `host_port` = ? LIMIT 1").
			WillReturnRows(mockDb.NewRows([]string{"id"}))

		mockDb.Mock.ExpectBegin()
		mockDb.ExpectExec(
			"UPDATE `asset_mappings` SET `dmz_ip`=?,`dmz_port`=?,`finance_ip`=?,`finance_port`=?,`float_ip`=?,`float_port`=?,`host_ip`=?,`host_port`=?,`load_ip`=?,`load_port`=?,`ssl_ip`=?,`ssl_port`=?,`updated_at`=? WHERE `id` = ?",
		).WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg()).WillReturnResult(sqlmock.NewResult(1, 1))
		mockDb.Mock.ExpectCommit()

		val, err := json.Marshal(map[string]interface{}{
			"host_ip":      "***********",
			"host_port":    "50",
			"float_ip":     "***********",
			"float_port":   "50",
			"load_ip":      "***********",
			"load_port":    "50",
			"ssl_ip":       "***********",
			"ssl_port":     "50",
			"dmz_ip":       "***********",
			"dmz_port":     "50",
			"finance_ip":   "***********",
			"finance_port": "50",
		})

		// 创建一个测试请求和响应
		w := httptest.NewRecorder()
		req := httptest.NewRequest("PUT", "/api/v1/asset_mappings", strings.NewReader(string(val)))
		req.Header.Set("Content-Type", "application/json")

		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		// 调用函数
		err = Update(c)

		assert.Nil(t, err)
	})
}

func TestDestroy(t *testing.T) {
	t.Run("params err", func(t *testing.T) {
		// 创建一个测试请求和响应
		w := httptest.NewRecorder()
		req := httptest.NewRequest("DELETE", "/api/v1/asset_mappings", nil)
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		// 调用函数
		err := Destroy(c)
		assert.NotNil(t, err)
	})
	t.Run("pass", func(t *testing.T) {
		mockDb := testcommon.GetMysqlMock()
		defer mockDb.Close()

		mockDb.Mock.ExpectBegin()
		mockDb.ExpectExec("DELETE FROM `asset_mappings` WHERE id in (?)").WithArgs(1).
			WillReturnResult(sqlmock.NewResult(1, 1))
		mockDb.Mock.ExpectCommit()

		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/v1/asset_mappings?ids=1", bytes.NewReader([]byte{}))

		c, _ := gin.CreateTestContext(w)
		c.Request = req

		err := Destroy(c)
		assert.Nil(t, err)
	})
}

func TestStoreFile(t *testing.T) {
	excelFile := excelize.NewFile()
	sheetName := "Sheet1"
	excelFile.SetCellValue(sheetName, "主机IP", "***********9")
	excelFile.SetCellValue(sheetName, "主机端口", "80")
	excelFile.SetCellValue(sheetName, "浮动IP", "*******")
	excelFile.SetCellValue(sheetName, "浮动端口", "81")

	buffer := new(bytes.Buffer)
	err := excelFile.Write(buffer)
	assert.NoError(t, err)

	// 创建 Gin 上下文
	gin.SetMode(gin.TestMode)
	w := httptest.NewRecorder()
	ctx, _ := gin.CreateTestContext(w)

	// 创建文件上传请求
	body := &bytes.Buffer{}
	writer := multipart.NewWriter(body)

	part, err := writer.CreateFormFile("file", "资产映射导入模版.xlsx")
	assert.NoError(t, err)

	_, err = part.Write(buffer.Bytes())
	assert.NoError(t, err)
	writer.Close()

	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()
	mockDb.ExpectBegin()
	mockDb.ExpectQuery("SELECT * FROM `asset_mappings`").
		WillReturnRows(sqlmock.NewRows([]string{
			"id", "created_at", "updated_at", "host_ip", "host_port", "float_ip", "float_port",
			"load_ip", "load_port", "ssl_ip", "ssl_port", "dmz_ip", "dmz_port", "finance_ip",
			"finance_port", "domain",
		}).AddRow(
			1, time.Now(), time.Now(), "127.0.0.1", 80, "127.0.0.1", 80,
			"127.0.0.1", 80, "127.0.0.1", 80, "127.0.0.1", 80, "127.0.0.1",
			80, "www.com",
		))

	mockDb.ExpectCommit()

	req := httptest.NewRequest("POST", "/api/v1/asset_mappings/file", body)
	req.Header.Set("Content-Type", writer.FormDataContentType())
	ctx.Request = req

	err = StoreFile(ctx)
	assert.Nil(t, err)
}
