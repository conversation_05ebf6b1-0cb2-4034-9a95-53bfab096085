package asset_mapping

import (
	"fmt"
	"fobrain/fobrain/app/services/sync/file_import"
	"github.com/gin-gonic/gin"
	"github.com/xuri/excelize/v2"
	"mime/multipart"

	"fobrain/fobrain/app/repository/asset_mapping"
	req "fobrain/fobrain/app/request/asset_mapping"
	"fobrain/fobrain/common/request"
	"fobrain/fobrain/common/response"

	asset "fobrain/models/mysql/asset_mapping"
)

// Index 资产映射列表
// @Route /api/v1/asset_mappings [GET]
func Index(c *gin.Context) error {
	params, err := request.Validate(c, &req.IndexRequest{})
	if err != nil {
		return err
	}

	data, total, err := asset_mapping.Index(params)
	if err != nil {
		return err
	}

	return response.OkWithPageData(c, total, params.Page, params.PerPage, data)
}

// Store 创建资产映射
// @Route /api/v1/asset_mappings [POST]
func Store(c *gin.Context) error {
	// 接收不固定字段的请求体
	var data map[string]interface{}

	err := c.ShouldBindJSON(&data)
	if err != nil {
		return err
	}

	err = asset_mapping.Store(data)
	if err != nil {
		return err
	}

	return response.Ok(c)
}

// Update 资产映射编辑
// @Route /api/v1/asset_mappings [PUT]
func Update(c *gin.Context) error {
	var data map[string]interface{}
	err := c.ShouldBindJSON(&data)
	if err != nil {
		return err
	}
	err = asset_mapping.Update(data)
	if err != nil {
		return err
	}

	return response.Ok(c)
}

// Destroy 资产映射删除
// @Route /api/v1/asset_mappings [DELETE]
func Destroy(c *gin.Context) error {
	params, err := request.Validate(c, &struct {
		Ids     []uint64 `json:"ids" uri:"ids" form:"ids" validate:"required" zh:"资产映射ID"`
		Keyword string   `json:"keyword" form:"keyword" uri:"keyword" validate:"omitempty,max=100" zh:"模糊搜索"`
	}{})
	if err != nil {
		return err
	}

	err = asset_mapping.Destroy(params.Ids, params.Keyword)
	if err != nil {
		return err
	}

	return response.Ok(c)
}

// StoreFile 资产映射添加 - 文件导入的方式
// @Route /api/v1/asset_mappings/file [POST]
func StoreFile(c *gin.Context) error {
	file, _, err := c.Request.FormFile("file")
	if err != nil {
		return err
	}

	defer func(file multipart.File) {
		err := file.Close()
		if err != nil {
			err := response.FailWithCodeMessage(c, 500, "关闭文件失败")
			if err != nil {
				return
			}
			return
		}
	}(file)

	f, err := excelize.OpenReader(file)
	if err != nil {
		return err
	}

	rows, err := f.GetRows("Sheet1", excelize.Options{RawCellValue: true})
	if err != nil {
		return err
	}

	if len(rows) > 30*10000 {
		return response.FailWithCodeMessage(c, 400, "单次文件同步数量不能大于30w数据")
	}

	data, row, err := file_import.CheckAssetMapping(rows)
	if err != nil {
		if row > 0 {
			return response.FailWithCodeMessage(c, 400, fmt.Sprintf("第%d行存在格式错误", row))
		}
		return response.FailWithCodeMessage(c, 400, err.Error())
	}
	err = asset.NewAssetMappingModel().AddAssetMapping(data)
	if err != nil {
		return err
	}
	return response.Ok(c)
}
