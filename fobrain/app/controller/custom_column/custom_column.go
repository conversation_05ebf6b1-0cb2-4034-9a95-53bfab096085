package custom_column

import (
	"fmt"
	"strconv"
	"time"

	"fobrain/fobrain/common/request"
	"fobrain/fobrain/common/response"
	"fobrain/initialize/mysql"
	"fobrain/models/mysql/custom_column"
	"fobrain/pkg/utils"

	"github.com/gin-gonic/gin"
)

// CustomFieldMetaList 列表接口
// @Summary 获取自定义字段元数据列表
// @Tags CustomFieldMeta
// @Produce json
// @Param module_type query string false "业务模块类型"
// @Success 200 {object} []custom_column.CustomFieldMeta
// @Router /api/custom_fields [get]
func CustomFieldMetaList(c *gin.Context) error {
	params, err := request.Validate(c, &struct {
		Page       int    `json:"page" form:"page" validate:"required"`
		PerPage    int    `json:"per_page" form:"per_page" validate:"required"`
		ModuleType string `json:"module_type" form:"module_type" validate:"omitempty,oneof=资产 设备 漏洞 人员 业务系统"`
		Keyword    string `json:"keyword" form:"keyword" validate:"omitempty"`
	}{})
	if err != nil {
		return response.FailWithMessage(c, "参数错误")
	}

	query := make([]mysql.HandleFunc, 0)
	if params.ModuleType != "" {
		query = append(query, mysql.WithColumnValue("module_type", params.ModuleType))
	}
	if params.Keyword != "" {
		query = append(query, mysql.WithLike("display_name", params.Keyword))
	}
	list, total, err := custom_column.NewCustomFieldMetaModel().GetByQuery(query, params.Page, params.PerPage, false)
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	return response.OkWithPageData(c, int64(total), params.Page, params.PerPage, list)
}

// CustomFieldList 列表接口，给前端做动态表头
func CustomFieldList(c *gin.Context) error {
	params, err := request.Validate(c, &struct {
		ModuleType string `json:"module_type" form:"module_type" validate:"required,oneof=资产 设备 漏洞 人员 业务系统"`
	}{})
	if err != nil {
		return response.FailWithMessage(c, "参数错误")
	}

	list, err := custom_column.NewCustomFieldMetaModel().GetByModuleType(params.ModuleType, false)
	if err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	result := make([]struct {
		AllKey  string `json:"all_key"`
		Name    string `json:"name"`
		Options string `json:"options"`
		Type    string `json:"type"`
	}, 0)
	for _, item := range list {
		result = append(result, struct {
			AllKey  string `json:"all_key"`
			Name    string `json:"name"`
			Options string `json:"options"`
			Type    string `json:"type"`
		}{
			AllKey:  fmt.Sprintf("custom_fields.%s", item.FieldKey),
			Name:    item.DisplayName,
			Options: item.Options,
			Type:    item.FieldType,
		})
	}
	return response.OkWithData(c, result)
}

// CustomFieldMetaCreate 新增接口
// @Summary 新增自定义字段元数据
// @Tags CustomFieldMeta
// @Accept json
// @Produce json
// @Param body body custom_column.CustomFieldMeta true "自定义字段元数据"
// @Success 200 {object} custom_column.CustomFieldMeta
// @Router /api/custom_fields [post]
func CustomFieldMetaCreate(c *gin.Context) error {
	params, err := request.Validate(c, &struct {
		ModuleType  string `json:"module_type" validate:"required,oneof=资产 设备 漏洞 人员 业务系统"`
		FieldKey    string `json:"field_key" validate:"omitempty"`
		DisplayName string `json:"display_name" validate:"required"`
		FieldType   string `json:"field_type" validate:"required,oneof=textarea select"`
		Options     string `json:"options" validate:"omitempty,max=1024"`
	}{})
	if err != nil {
		return response.FailWithMessage(c, "参数错误")
	}
	if params.FieldType == "select" && params.Options == "" {
		return response.FailWithMessage(c, "字段类型为select时，下拉选项不能为空")
	}
	if params.FieldKey == "" {
		params.FieldKey = time.Now().Format("20060102150405")
	}
	// 检查是否已存在
	exist, err := custom_column.NewCustomFieldMetaModel().GetByModuleTypeAndFieldKey(params.ModuleType, params.FieldKey, true)
	if err != nil {
		return response.FailWithMessage(c, "检查字段是否存在失败")
	}
	if exist != nil {
		return response.FailWithMessage(c, fmt.Sprintf("%s模块已存在该字段", params.ModuleType))
	}
	// 检查当前模块的总数
	count, err := custom_column.NewCustomFieldMetaModel().GetCountByQuery([]mysql.HandleFunc{mysql.WithColumnValue("module_type", params.ModuleType)}, true)
	if err != nil {
		return response.FailWithMessage(c, "检查字段总数失败")
	}
	if count >= 500 {
		return response.FailWithMessage(c, fmt.Sprintf("%s模块最多只能创建500个字段(含已删除字段)", params.ModuleType))
	}

	if err := custom_column.NewCustomFieldMetaModel().Create(&custom_column.CustomFieldMeta{
		ModuleType:  params.ModuleType,
		FieldKey:    params.FieldKey,
		DisplayName: params.DisplayName,
		FieldType:   params.FieldType,
		Options:     params.Options,
	}); err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	return response.OkWithMessage(c, "创建成功")
}

// CustomFieldMetaUpdate 修改接口
// @Summary 修改自定义字段元数据
// @Tags CustomFieldMeta
// @Accept json
// @Produce json
// @Param id path int true "主键ID"
// @Param body body custom_column.CustomFieldMeta true "自定义字段元数据"
// @Success 200 {object} custom_column.CustomFieldMeta
// @Router /api/custom_fields/{id} [put]
func CustomFieldMetaUpdate(c *gin.Context) error {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		return response.FailWithMessage(c, "ID参数错误")
	}
	params, err := request.Validate(c, &struct {
		DisplayName string `json:"display_name" validate:"required"`
		FieldType   string `json:"field_type" validate:"required,oneof=textarea select"`
		Options     string `json:"options" validate:"omitempty,max=1024"`
	}{})
	if err != nil {
		return response.FailWithMessage(c, "参数错误")
	}
	if params.FieldType == "select" && params.Options == "" {
		return response.FailWithMessage(c, "字段类型为select时，下拉选项不能为空")
	}
	if err := custom_column.NewCustomFieldMetaModel().UpdateByMap(id, map[string]interface{}{
		"display_name": params.DisplayName,
		"field_type":   params.FieldType,
		"options":      params.Options,
	}); err != nil {
		return response.FailWithMessage(c, err.Error())
	}
	return response.OkWithMessage(c, "更新成功")
}

// CustomFieldMetaDelete 删除接口
// @Summary 删除自定义字段元数据
// @Tags CustomFieldMeta
// @Param id path int true "主键ID"
// @Success 200 {object} gin.H
// @Router /api/custom_fields [delete]
func CustomFieldMetaDelete(c *gin.Context) error {
	params, err := request.Validate(c, &struct {
		Ids     []uint64 `json:"ids"`
		Keyword string   `json:"keyword"`
	}{})
	if err != nil {
		return response.FailWithMessage(c, "参数错误")
	}
	// 去除重复的ID
	params.Ids = utils.ListDistinctNonZero(params.Ids)

	// 删除全部
	if len(params.Ids) == 0 && params.Keyword == "" {
		if err := custom_column.NewCustomFieldMetaModel().DeleteAll(); err != nil {
			return response.FailWithMessage(c, err.Error())
		}
		return response.OkWithMessage(c, "删除成功")
	} else if len(params.Ids) > 0 {
		if err := custom_column.NewCustomFieldMetaModel().DeleteByIds(params.Ids); err != nil {
			return response.FailWithMessage(c, err.Error())
		}
		return response.OkWithMessage(c, "删除成功")
	} else if params.Keyword != "" {
		if err := custom_column.NewCustomFieldMetaModel().DeleteByQuery([]mysql.HandleFunc{mysql.WithLike("display_name", params.Keyword)}); err != nil {
			return response.FailWithMessage(c, err.Error())
		}
		return response.OkWithMessage(c, "删除成功")
	}
	return response.OkWithMessage(c, "删除成功")
}
