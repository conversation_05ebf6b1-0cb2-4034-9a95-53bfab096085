package statistical

import (
	"fobrain/fobrain/app/repository/asset"
	"fobrain/fobrain/app/request/asset_center"
	"fobrain/fobrain/app/request/statistical"
	statistical2 "fobrain/fobrain/app/services/statistical"
	"fobrain/fobrain/common/request"
	"fobrain/fobrain/common/response"

	"github.com/gin-gonic/gin"
)

var InternalAssetValidate = request.Validate[asset_center.InternalAssetRequest]

// TreeList 统计列表
func TreeList(c *gin.Context) error {
	params, err := request.Validate(c, &statistical.TreeRequest{})
	if err != nil {
		return err
	}
	service := statistical2.NewService()
	assetTop, err := service.GetList(params)
	if err != nil {
		return err
	}
	return response.OkWithData(c, assetTop)
}

func Count(c *gin.Context) error {
	params, err := request.Validate(c, &statistical.CountRequest{})
	if err != nil {
		return err
	}
	service := statistical2.NewService()
	counts, err := service.GetCountByFields(params)
	if err != nil {
		return err
	}
	return response.OkWithData(c, counts)
}

func Assets(c *gin.Context) error {
	params, err := InternalAssetValidate(c, &asset_center.InternalAssetRequest{})
	if err != nil {
		return err
	}

	staffIds, _ := c.Get("staff_ids")
	isSuperManage, _ := c.Get("is_super_manage")

	list, total, err := asset.List(c, params.Keyword, params.Page, params.PerPage, params.NetworkType, params, isSuperManage.(bool), staffIds.([]string), params.MustAttr, 0)
	if err != nil {
		return err
	}

	return response.OkWithPageData(c, total, params.Page, params.PerPage, list)
}
