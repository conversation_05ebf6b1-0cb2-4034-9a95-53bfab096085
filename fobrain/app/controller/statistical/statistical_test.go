package statistical

import (
	"bytes"
	json2 "encoding/json"
	"fmt"
	testcommon "fobrain/fobrain/tests/common_test"
	"github.com/gin-gonic/gin"
	"github.com/olivere/elastic/v7"
	"github.com/stretchr/testify/assert"
	"net/http/httptest"
	"testing"
)

func TestTreeList(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()
	t.Run("es query err", func(t *testing.T) {
		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/v1/statistical/list", nil)
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req
		err := TreeList(c)
		assert.NotNil(t, err)
	})
	t.Run("es query success", func(t *testing.T) {
		mockServer.Register("asset/_search", []*elastic.SearchHit{
			{
				Id:     "1",
				Source: []byte(`{"ip":"***********", "updated_at":"2023-04-25 12:34:56", "created_at":"2023-04-25 12:34:56"}`),
			},
		})

		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/v1/statistical/list?type=business", nil)
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req
		err := TreeList(c)
		// 断言错误为 nil
		fmt.Print(err)
		assert.Empty(t, err)
	})
}

func TestCount(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()
	t.Run("es query err", func(t *testing.T) {
		w := httptest.NewRecorder()
		req := httptest.NewRequest("POST", "/api/v1/statistical/info", nil)
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req
		err := TreeList(c)
		assert.NotNil(t, err)
	})
	t.Run("query other success", func(t *testing.T) {
		mockServer.Register("asset/_search", []*elastic.SearchHit{
			{
				Id:     "1",
				Source: []byte(`{"ip":"***********", "updated_at":"2023-04-25 12:34:56", "created_at":"2023-04-25 12:34:56"}`),
			},
		})

		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/v1/statistical/inf?type=business", bytes.NewBufferString(`{"params": [{"field": "business_oper_num","type": "other"}}`))
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req
		err := TreeList(c)
		// 断言错误为 nil
		fmt.Print(err)
		assert.Empty(t, err)
	})
	t.Run("query pie success", func(t *testing.T) {
		mockServer.Register("asset/_search", []*elastic.SearchHit{
			{
				Id:     "1",
				Source: []byte(`{"ip":"***********", "updated_at":"2023-04-25 12:34:56", "created_at":"2023-04-25 12:34:56"}`),
			},
		})

		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/v1/statistical/inf?type=business", bytes.NewBufferString(`{"params": [{"field": "business_oper_num","type": "pie"}}`))
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req
		err := TreeList(c)
		// 断言错误为 nil
		fmt.Print(err)
		assert.Empty(t, err)
	})
}

func TestAssets(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	t.Run("es query err", func(t *testing.T) {
		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/v1/statistical/asset?page=1&per_page=10", nil)
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req
		c.Set("staff_ids", []string{})
		c.Set("is_super_manage", true)
		err := Assets(c)
		assert.NotNil(t, err)
	})

	mockServer.Register("asset/_search", []*elastic.SearchHit{
		{
			Id:     "1",
			Source: []byte(`{"ip":"***********", "updated_at":"2023-04-25 12:34:56", "created_at":"2023-04-25 12:34:56"}`),
		},
	})

	t.Run("ExternalIpAssetValidate err", func(t *testing.T) {
		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/v1/external_ip_asset?page=1", nil)
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req
		c.Set("staff_ids", []string{})
		c.Set("is_super_manage", true)
		err := Assets(c)
		assert.Equal(t, "条数为必填字段", err.Error())
	})

	// 创建一个测试请求和响应
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/external_ip_asset?page=1&per_page=10", nil)
	// 创建一个Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req
	c.Set("staff_ids", []string{})
	c.Set("is_super_manage", true)
	// 调用函数
	err := Assets(c)

	// 断言错误为 nil
	assert.Empty(t, err)

	var result struct {
		Code    int    `json:"code"`
		Message string `json:"message"`
		Data    struct {
			Total    int64                    `json:"total"`
			Page     int                      `json:"page"`
			PerPage  int                      `json:"per_page"`
			Items    []map[string]interface{} `json:"items"`
			PageInfo struct {
				Total   int64 `json:"total"`
				Page    int   `json:"page"`
				PerPage int   `json:"per_page"`
			}
		}
	}

	json2.Unmarshal(w.Body.Bytes(), &result)

	assert.Equal(t, int64(1), result.Data.Total)
	assert.Equal(t, "***********", result.Data.Items[0]["ip"])
	assert.Equal(t, "2023-04-25 12:34:56", result.Data.Items[0]["updated_at"].(string))
	assert.Equal(t, "2023-04-25 12:34:56", result.Data.Items[0]["created_at"].(string))
}
