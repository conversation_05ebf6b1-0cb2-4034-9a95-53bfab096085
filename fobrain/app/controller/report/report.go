package report

import (
	"fobrain/fobrain/app/repository/reports"
	"fobrain/fobrain/common/request"
	"fobrain/fobrain/common/response"
	"github.com/gin-gonic/gin"
)

// List
// @Summary 报告列表
func List(ctx *gin.Context) error {
	return nil
}

// Generate
// 生成报告
func Generate(ctx *gin.Context) error {
	return nil
}

// TemplateList
// @Summary 报告模板列表
func TemplateList(ctx *gin.Context) error {
	var reportRequest struct {
		response.PageResult
		Keyword string `json:"keyword" form:"keyword" uri:"keyword" validate:"omitempty" zh:"关键字"`
	}
	params, err := request.Validate(ctx, &reportRequest)
	if err != nil {
		return err
	}

	list, total, err := reports.List(reportRequest.Keyword)
	if err != nil {
		return err
	}

	return response.OkWithPageData(ctx, total, params.Page, params.PerPage, list)
}

// CreateTemplate
// @Summary 创建报告模板
func CreateTemplate(ctx *gin.Context) error {
	var reportRequest struct {
		Name          string `json:"name" form:"name" uri:"name" validate:"omitempty,min-50,max=200" zh:"报告名称"`
		TimeRange     int    `json:"time_range" form:"time_range" uri:"time_range" validate:"omitempty" zh:"时间范围"`
		CategoryLeve1 int    `json:"category_leve_1" form:"category_leve_1" uri:"category_leve_1" validate:"omitempty" zh:"一级分类"`
		CategoryLeve2 int    `json:"category_leve_2" form:"category_leve_2" uri:"category_leve_2" validate:"omitempty" zh:"二级分类"`
		CategoryLeve3 int    `json:"category_leve_3" form:"category_leve_3" uri:"category_leve_3" validate:"omitempty" zh:"三级分类"`
		NoticeEmail   string `json:"notice_email" form:"notice_email" uri:"notice_email" validate:"omitempty" zh:"通知人"`
	}
	validate, err := request.Validate(ctx, &reportRequest)
	if err != nil {
		return err
	}

	return reports.Create(
		validate.Name, validate.TimeRange,
		validate.CategoryLeve1, validate.CategoryLeve2,
		validate.CategoryLeve3, validate.NoticeEmail,
	)
}
