package asset_graph

import (
	"errors"

	"github.com/gin-gonic/gin"

	"fobrain/fobrain/app/repository/asset_graph"
	req "fobrain/fobrain/app/request/asset_graph"
	"fobrain/fobrain/common/request"
	"fobrain/fobrain/common/response"
)

// Index 函数处理资产图表的索引请求
//
// 参数:
//     c: gin.Context 上下文对象，包含请求和响应信息
//
// 返回值:
//     error 错误对象，如果发生错误则返回错误对象，否则返回nil

func Index(c *gin.Context) error {
	params, err := request.Validate(c, &req.AssetGraphRequest{})
	if err != nil {
		return err
	}
	if (params.Type == "ip" && params.Ip == "") || (params.Type == "business" && params.Business == "") {
		return errors.New("搜索条件为空，请输入搜索条件")
	}
	info, err := asset_graph.AssetGraphList(params)
	if err != nil {
		return err
	}
	return response.OkWithData(c, info)
}
