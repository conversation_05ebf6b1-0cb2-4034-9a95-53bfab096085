package asset_graph

import (
	"io/ioutil"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"

	"fobrain/fobrain/app/repository/asset_graph"
)

func TestIndex(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		time.Sleep(time.Second)
		defer gomonkey.ApplyFuncReturn(asset_graph.AssetGraphList, nil, nil).Reset()
		// 创建一个测试请求和响应
		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/v1/asset_graph?type=business&business=oa系统", nil)
		req.Header.Set("Content-Type", "application/json")
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		// 调用函数
		err := Index(c)
		assert.NoError(t, err)

		// 解析响应数据
		response := w.Result()
		defer response.Body.Close()
		body, _ := ioutil.ReadAll(response.Body)
		assert.NotEmpty(t, body)
	})
	t.Run("error", func(t *testing.T) {
		// 创建一个测试请求和响应
		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/api/v1/asset_graph?type=business&business=", nil)
		req.Header.Set("Content-Type", "application/json")
		// 创建一个Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		// 调用函数
		err := Index(c)
		assert.Error(t, err)
	})
}
