package vulnerability

import (
	"fmt"
	vulnerability_services "fobrain/fobrain/app/services/vulnerability"
	"fobrain/fobrain/common/request"
	"fobrain/fobrain/common/response"
	"fobrain/models/mysql/vulnerability"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

type ListRequest struct {
	request.PageRequest
	Keyword string `json:"keyword" form:"keyword" uri:"keyword" validate:"omitempty,max=100" zh:"漏洞名称关键词"`
	// CVEID uint64 `json:"node_id" form:"node_id" uri:"node_id" validate:"omitempty,number" zh:"节点ID"`
}

// 上传zip文件并记录和保存数据
func Upload(c *gin.Context) error {
	startTime := time.Now() // 开始计时
	batch := time.Now().Format("20060102150405")
	// uploader := "user" // 暂时设置user
	uploader := request.GetUserId(c)

	file, err := c.FormFile("file")
	if err != nil {
		return err
	}

	if !strings.HasSuffix(file.Filename, ".zip") {
		return fmt.Errorf("invalid file type: %s", file.Filename)
	}

	// 使用批次作为文件夹名
	baseDir := "../storage/app/vulnerability/"
	specificDir := filepath.Join(baseDir, batch) // 构建具有时间戳的目录路径

	dst := filepath.Join(specificDir, file.Filename) // 文件保存路径包括新的文件夹
	if err := c.SaveUploadedFile(file, dst); err != nil {
		return fmt.Errorf("failed to save the file: %s", err)
	}

	// 解压到新创建的文件夹下
	extractPath := filepath.Join(specificDir, "extracted")
	if err := os.MkdirAll(extractPath, 0755); err != nil {
		return fmt.Errorf("failed to create extraction directory: %s", err)
	}

	if err := vulnerability_services.Unzip(dst, extractPath, batch, uploader); err != nil {
		return err
	}
	// 数据到记录表结束时间
	recordendTime := time.Now()
	durationrecord := recordendTime.Sub(startTime) // 计算消耗时间

	// 文件导入到原始表中
	if err := vulnerability_services.Writetosource(extractPath, batch); err != nil {
		return err
	}
	// 数据到原始表结束时间
	sourceendTime := time.Now()
	durationsource := sourceendTime.Sub(recordendTime) // 计算消耗时间

	// 删除解压后的文件
	if err := os.RemoveAll(extractPath); err != nil {
		return fmt.Errorf("failed to remove extracted files: %s", err)
	}
	fmt.Println("解压后的文件已成功删除")

	vulnerability_services.VulnerabilityIntegrationall(batch)
	integrationendTime := time.Now()
	durationintegration := integrationendTime.Sub(sourceendTime) // 计算消耗时间
	// 数据到记录消耗时间
	fmt.Printf("记录表消耗时间 %v\n", durationrecord)
	// 数据到原始表消耗时间
	fmt.Printf("原始表消耗时间： %v\n", durationsource)
	// 数据到结果表消耗时间
	fmt.Printf("结果表消耗时间： %v\n", durationintegration)

	return response.Ok(c)
}

// 整合结果列表
func List(c *gin.Context) error {
	params, err := request.Validate(c, &ListRequest{})
	if err != nil {
		return err
	}

	list, total, err := vulnerability.VulnerabilityList(params.Page, params.PerPage, params.Keyword)
	if err != nil {
		return err
	}
	return response.OkWithPageData(c, total, params.Page, params.PerPage, list)
}
