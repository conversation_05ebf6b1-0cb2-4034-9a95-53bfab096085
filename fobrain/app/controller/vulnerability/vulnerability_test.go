package vulnerability

import (
	"fobrain/models/mysql/vulnerability"
	. "github.com/agiledragon/gomonkey/v2"
	"github.com/gin-gonic/gin"
	. "github.com/smartystreets/goconvey/convey"
	"github.com/stretchr/testify/assert"
	"net/http/httptest"
	"testing"
)

func TestList(t *testing.T) {
	<PERSON><PERSON>("TestList", t, func() {
		<PERSON><PERSON>("Success", func() {
			defer ApplyFuncReturn(vulnerability.VulnerabilityList, nil, int64(0), nil).Reset()
			w := httptest.NewRecorder()
			req := httptest.NewRequest("GET", "/api/v1/vulnerability/list?page=1&per_page=10", nil)
			c, _ := gin.CreateTestContext(w)
			c.Request = req

			err := List(c)
			assert.Nil(t, err)
		})
	})
}
