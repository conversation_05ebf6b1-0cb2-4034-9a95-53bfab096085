package source

import (
	"fobrain/fobrain/app/request/source"
	testcommon "fobrain/fobrain/tests/common_test"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
)

func TestAdd(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()

		mockDb.ExpectQuery("SELECT count(*) FROM `data_sources` WHERE name = ?").
			WithArgs("a").
			WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(0))

		mockDb.ExpectQuery("SELECT count(*) FROM `data_sources` WHERE en_name = ?").
			WithArgs("").
			WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(0))

		mockDb.ExpectBegin()
		mockDb.ExpectExec("INSERT INTO `data_sources` (`created_at`,`updated_at`,`name`,`en_name`,`type`,`desc`,`icon`,`version`,`status`,`show`,`sort`,`is_task_sync`,`is_cron_sync`,`has_asset_data`,`has_vul_data`,`has_personnel_data`,`has_relations`,`has_sync`,`custom_field_config`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)").
			WithArgs(
				sqlmock.AnyArg(), // created_at
				sqlmock.AnyArg(), // updated_at
				"a",              // name
				"",               // en_name
				sqlmock.AnyArg(), // type
				sqlmock.AnyArg(), // desc
				"",               // icon
				"",               // version
				sqlmock.AnyArg(), // status
				sqlmock.AnyArg(), // show
				sqlmock.AnyArg(), // sort
				false,            // is_task_sync
				false,            // is_cron_sync
				false,            // has_asset_data
				false,            // has_vul_data
				false,            // has_personnel_data
				false,            // has_relations
				sqlmock.AnyArg(), // has_sync
				"[]",             // custom_field_config
			).
			WillReturnResult(sqlmock.NewResult(1, 1))
		mockDb.ExpectCommit()
		mockDb.ExpectBegin()
		mockDb.ExpectExec("INSERT INTO `data_source_type_map` (`created_at`,`updated_at`,`source_id`,`source_type_id`) VALUES (?,?,?,?)").
			WithArgs(
				sqlmock.AnyArg(),
				sqlmock.AnyArg(),
				uint64(1),
				uint64(1),
			).
			WillReturnResult(sqlmock.NewResult(1, 1))
		mockDb.ExpectCommit()

		err := Add(&source.AddRequest{
			Name:             "a",
			EnName:           "",
			Icon:             "",
			Version:          "",
			HasAssetData:     false,
			HasVulData:       false,
			HasPersonnelData: false,
			IsTaskSync:       false,
			IsCronSync:       false,
			DataSourceTypes:  []int{1},
		})

		assert.Nil(t, err)
		assert.NoError(t, mockDb.ExpectationsWereMet())
	})
	t.Run("count > 0", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()

		mockDb.ExpectQuery("SELECT count(*) FROM `data_sources` WHERE name = ?").
			WithArgs("").
			WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(1))
		err := Add(&source.AddRequest{})
		assert.Error(t, err)
	})
	t.Run("countEn > 0", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()

		mockDb.ExpectQuery("SELECT count(*) FROM `data_sources` WHERE name = ?").
			WithArgs("").
			WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(0))
		mockDb.ExpectQuery("SELECT count(*) FROM `data_sources` WHERE en_name = ?").
			WithArgs("").
			WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(1))
		err := Add(&source.AddRequest{})
		assert.Error(t, err)
	})
}
