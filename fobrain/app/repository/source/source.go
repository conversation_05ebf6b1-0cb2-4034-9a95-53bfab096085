package source

import (
	"errors"

	"fobrain/fobrain/app/request/source"
	"fobrain/initialize/mysql"
	"fobrain/models/mysql/data_source"
)

func Add(params *source.AddRequest) error {
	//校验数据源名称是否重复
	count, err := data_source.NewSourceModel().Count(mysql.WithWhere("name = ?", params.Name))

	if err != nil {
		return err
	}

	if count > 0 {
		return errors.New("数据源名称重复")
	}

	//校验数据源英文名称是否重复
	countEn, err := data_source.NewSourceModel().Count(mysql.WithWhere("en_name = ?", params.EnName))

	if err != nil {
		return err
	}

	if countEn > 0 {
		return errors.New("数据源英文名称重复")
	}

	item := data_source.Source{
		Name:              params.Name,
		EnName:            params.EnName,
		Type:              "custom",
		Desc:              "custom source",
		Icon:              params.Icon,
		Version:           params.Version,
		Status:            data_source.StatusEnable,
		Show:              data_source.ShowEnable,
		Sort:              100,
		IsTaskSync:        params.IsTaskSync,
		IsCronSync:        params.IsCronSync,
		HasAssetData:      params.HasAssetData,
		HasVulData:        params.HasVulData,
		HasPersonnelData:  params.HasPersonnelData,
		HasSync:           data_source.HasSyncNo,
		CustomFieldConfig: "[]",
	}

	//添加数据源
	err = data_source.NewSourceModel().Create(&item)
	if err != nil {
		return err
	}

	//添加数据源所属类型
	for _, typeId := range params.DataSourceTypes {
		err = data_source.NewSourceTypeMapModel().Create(&data_source.SourceTypeMap{
			SourceId:     item.Id,
			SourceTypeId: uint64(typeId),
		})

		if err != nil {
			return err
		}
	}
	return nil
}
