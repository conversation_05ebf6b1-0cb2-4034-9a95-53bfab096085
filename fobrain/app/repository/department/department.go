package department

import (
	"context"
	"encoding/json"
	"errors"
	"fobrain/initialize/es"
	"fobrain/models/elastic/department"
	"fobrain/pkg/utils"
	"github.com/olivere/elastic/v7"
)

func List(keyword string, page, perPage int) (any, int64, error) {
	boolQuery := elastic.NewBoolQuery()
	if keyword != "" {
		boolQuery = boolQuery.Should(elastic.NewMatchQuery("name", keyword))
	}

	result, err := es.GetEsClient().Search(department.NewDepartmentModel().IndexName()).From(es.GetFrom(page, perPage)).
		Size(es.GetSize(perPage)).Query(boolQuery).Sort("updated_at", false).Do(context.TODO())

	if err != nil {
		return nil, 0, err
	}

	list := make([]any, 0)

	for _, hit := range result.Hits.Hits {
		list = append(list, hit.Source)
	}

	return list, result.TotalHits(), nil
}

func UpdateById(name string, id interface{}) error {
	boolQuery := elastic.NewBoolQuery()
	boolQuery = boolQuery.Must(elastic.NewTermQuery("id", id))
	result, err := es.GetEsClient().Search(department.NewDepartmentModel().IndexName()).Query(boolQuery).Do(context.TODO())
	if err != nil {
		return err
	}

	dM := department.Department{}
	if len(result.Hits.Hits) == 0 {
		return errors.New("修改的数据不存在")
	}

	json.Unmarshal(result.Hits.Hits[0].Source, &dM)
	dM.Name = name
	return es.CreateOrUpdate(dM)
}

func DeleteByIds(ids []string) error {
	boolQuery := elastic.NewBoolQuery()

	if len(utils.CompactStrings(ids)) == 0 {
		boolQuery = boolQuery.Must(elastic.NewMatchAllQuery())
	} else {
		boolQuery = boolQuery.Must(elastic.NewTermsQueryFromStrings("id", ids...))
	}
	_, err := es.GetEsClient().DeleteByQuery().Index(department.NewDepartmentModel().IndexName()).Query(boolQuery).Refresh("true").Do(context.TODO())

	return err
}
