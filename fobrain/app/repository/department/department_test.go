package department

import (
	"errors"
	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/initialize/es"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/olivere/elastic/v7"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestList(t *testing.T) {
	patches := []*gomonkey.Patches{
		gomonkey.ApplyFuncReturn(es.GetEsClient, &es.SafeClient{}),
		gomonkey.ApplyMethodReturn(&elastic.SearchService{}, "Do", &elastic.SearchResult{
			Hits: &elastic.SearchHits{
				TotalHits: &elastic.TotalHits{
					Value:    1,
					Relation: "",
				},
				MaxScore: nil,
				Hits: []*elastic.SearchHit{
					&elastic.SearchHit{},
				},
			},
		}, nil),
	}
	_, _, err := List("a", 1, 1)
	for _, patch := range patches {
		patch.Reset()
	}
	assert.Nil(t, err)
}

func TestListError(t *testing.T) {
	patches := []*gomonkey.Patches{
		gomonkey.ApplyFuncReturn(es.GetEsClient, &es.SafeClient{}),
		gomonkey.ApplyMethodReturn(&elastic.SearchService{}, "Do", &elastic.SearchResult{}, errors.New("err")),
	}
	_, _, err := List("a", 1, 1)
	for _, patch := range patches {
		patch.Reset()
	}
	assert.Error(t, err)
}

func TestUpdateById(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	mockServer.Register("department/_search", []*elastic.SearchHit{})

	err := UpdateById("a", 1)
	mockServer.Close()
	assert.NotNil(t, err)
}

func TestDeleteByIds1(t *testing.T) {
	patches := []*gomonkey.Patches{
		gomonkey.ApplyFuncReturn(es.GetEsClient, &es.SafeClient{}),
		gomonkey.ApplyMethodReturn(&elastic.DeleteByQueryService{}, "Do", &elastic.BulkIndexByScrollResponse{}, nil),
	}
	err := DeleteByIds([]string{"a"})
	for _, patch := range patches {
		patch.Reset()
	}
	assert.Nil(t, err)
}

func TestDeleteByIds2(t *testing.T) {
	patches := []*gomonkey.Patches{
		gomonkey.ApplyFuncReturn(es.GetEsClient, &es.SafeClient{}),
		gomonkey.ApplyMethodReturn(&elastic.DeleteByQueryService{}, "Do", &elastic.BulkIndexByScrollResponse{}, nil),
	}

	err := DeleteByIds([]string{})
	for _, patch := range patches {
		patch.Reset()
	}
	assert.Nil(t, err)
}
