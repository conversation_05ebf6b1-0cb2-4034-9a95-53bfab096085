package poc_auto_distribute_config

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"strconv"
	"strings"
	"time"

	"github.com/olivere/elastic/v7"
	"gorm.io/gorm"

	"fobrain/fobrain/app/repository/email"
	"fobrain/fobrain/app/repository/threat_history"
	"fobrain/fobrain/app/request/poc_auto_distribute_config"
	"fobrain/fobrain/logs"
	"fobrain/initialize/es"
	"fobrain/initialize/mysql"
	"fobrain/models/elastic/assets"
	"fobrain/models/elastic/poc"
	"fobrain/models/elastic/staff"
	models "fobrain/models/mysql/poc_auto_distribute_config"
	"fobrain/models/mysql/threat_histories"
	"fobrain/models/mysql/threat_tasks"
	"fobrain/models/mysql/user"
)

var logger = logs.GetLogger()

// Create 函数用于创建一个新的PocAutoDistributeConfig配置实例
//
// 参数:
//
//	param: *poc_auto_distribute_config.PocAutoDistributeConfigCreate 类型，包含创建PocAutoDistributeConfig实例所需的参数
//
// 返回值:
//
//	error: 如果创建成功，则返回nil；如果创建失败，则返回错误信息
func Create(param *poc_auto_distribute_config.PocAutoDistributeConfigCreate) error {
	var handlers []mysql.HandleFunc
	first, err := models.NewPocAutoDistributeConfigModel().First(handlers...)
	if err != nil && err != gorm.ErrRecordNotFound {
		return err
	}
	id := first.Id
	repairTimeValue, _ := json.Marshal(param.RepairTimeValue)
	sendPersonId, _ := json.Marshal(param.SendPersonId)
	timeoutPersonId, _ := json.Marshal(param.TimeoutPersonId)
	config := &models.PocAutoDistributeConfig{
		IsAutoDispatch:     param.IsAutoDispatch,
		RepairTimeType:     param.RepairTimeType,
		RepairTimeValue:    string(repairTimeValue),
		VulRepairPrincipal: param.VulRepairPrincipal,
		SendNotice:         param.SendNotice,
		SendPersonId:       string(sendPersonId),
		TimeoutNotice:      param.TimeoutNotice,
		TimeoutPersonId:    string(timeoutPersonId),
		Descrition:         param.Descrition,
		ShowRepair:         param.ShowRepair,
		TimeoutFrequency:   param.TimeoutFrequency,
	}
	if id > 0 { //更新
		config.Id = id
		err = models.NewPocAutoDistributeConfigModel().Update(config)
		if err != nil {
			return err
		}
		if first.IsAutoDispatch == 2 && param.IsAutoDispatch == 1 {
			go ExecPocAutoDistribute()
		}
	} else {
		err = models.NewPocAutoDistributeConfigModel().CreateItem(config)
		if err != nil {
			return err
		}
		go ExecPocAutoDistribute()
	}
	return nil
}

// Info 函数用于获取PocAutoDistributeConfig的创建信息
//
// 返回值:
//
//	poc_auto_distribute_config.PocAutoDistributeConfigCreate: 包含创建PocAutoDistributeConfig所需信息的结构体
//	error: 如果在获取创建信息过程中发生错误，则返回相应的错误信息；否则返回nil
func Info() (poc_auto_distribute_config.PocAutoDistributeConfigCreate, error) {
	var info poc_auto_distribute_config.PocAutoDistributeConfigCreate
	var handlers []mysql.HandleFunc
	first, err := models.NewPocAutoDistributeConfigModel().First(handlers...)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return info, nil
		}
		return info, err
	}
	info = poc_auto_distribute_config.PocAutoDistributeConfigCreate{
		Id:                 first.Id,
		IsAutoDispatch:     first.IsAutoDispatch,
		RepairTimeType:     first.RepairTimeType,
		RepairTimeValue:    jsonToStruct(first.RepairTimeValue),
		VulRepairPrincipal: first.VulRepairPrincipal,
		SendNotice:         first.SendNotice,
		SendPersonId:       jsonToString(first.SendPersonId),
		TimeoutNotice:      first.TimeoutNotice,
		TimeoutPersonId:    jsonToString(first.TimeoutPersonId),
		Descrition:         first.Descrition,
		TimeoutFrequency:   first.TimeoutFrequency,
	}
	return info, nil
}

func jsonToString(jsonStr string) []string {
	strSlice := make([]string, 0)
	err := json.Unmarshal([]byte(jsonStr), &strSlice)
	if err != nil {
		return strSlice
	}
	return strSlice
}

func jsonToStruct(jsonStr string) []poc_auto_distribute_config.RepairTimeValue {
	repairTimeValue := make([]poc_auto_distribute_config.RepairTimeValue, 0)
	err := json.Unmarshal([]byte(jsonStr), &repairTimeValue)
	if err != nil {
		return repairTimeValue
	}
	return repairTimeValue
}

// ExecPocAutoDistribute 执行poc自动派发，只查询新增,复现 2个状态
func ExecPocAutoDistribute() error {
	logger.Infof("execPocAutoDistribute 执行poc自动派发")
	first, err := models.NewPocAutoDistributeConfigModel().First()
	if err != nil {
		logger.Errorf("execPocAutoDistribute 获取自动派发配置失败: %v", err.Error())
		return err
	}
	if first.IsAutoDispatch == 2 {
		logger.Infof("execPocAutoDistribute 自动派发配置为是禁用状态,不执行")
		return nil
	}
	userAssets, err := getAssets(first.VulRepairPrincipal)
	if err != nil {
		logger.Errorf("execPocAutoDistribute 获取资产失败: %v", err)
		return err
	}
	err = execUserPocsSend(userAssets, &first)
	if err != nil {
		logger.Errorf("execPocAutoDistribute 自动派发失败: %v", err)
		return err
	}
	logger.Infof("execPocAutoDistribute 自动派发完成")
	return nil
}

// execUserPocsSend 执行用户poc自动派发 发送邮件通知
func execUserPocsSend(userAssets []*poc_auto_distribute_config.UserInfo, conf *models.PocAutoDistributeConfig) error {
	logger.Infof("execUserPocsSend 执行用户poc自动派发")
	for _, asset := range userAssets {
		userId := asset.UserId
		staffInfo, err := getUserInfo([]interface{}{userId})
		if err != nil {
			logger.Errorf("execUserPocsSend 获取用户信息失败: %v", err)
			continue
		}
		if staffInfo.Id == "" {
			logger.Errorf("execUserPocsSend 用户不存在: %v", userId)
			continue
		}
		logger.Infof("execUserPocsSend 用户名: %v,用户Id: %v, IpsNum: %v", staffInfo.Name, staffInfo.Id, len(asset.Ips))
		err = getPocs(asset.Ips, staffInfo, conf)
		if err != nil {
			logger.Errorf("execUserPocsSend getPocs 处理失败: %v", err)
			continue
		}
	}
	logger.Infof("execUserPocsSend 用户poc自动派发完成")
	return nil
}

// getJsonToMap 函数将一个 JSON 字符串转换为 map[string]int 类型的映射表。
//
// 参数：
// jsonStr string: 需要转换的 JSON 字符串。
//
// 返回值：
// map[string]int: 转换后的映射表，键为字符串类型，值为整数类型。
//
// 说明：
// 该函数首先创建一个空的 RepairTimeValue 切片，用于存储解析后的 JSON 数据。
// 然后，使用 json.Unmarshal 函数将 JSON 字符串解析到 RepairTimeValue 切片中。
// 如果解析失败，则返回 nil。
// 如果解析成功，则创建一个空的 map[string]int 映射表，并遍历 RepairTimeValue 切片。
// 在遍历过程中，将 RepairTimeValue 的 Label 字段作为映射表的键，将 Value 字段转换为整数类型后作为映射表的值。
// 最后，返回生成的映射表。
func getJsonToMap(jsonStr string) map[string]int {
	repairTimeValue := make([]poc_auto_distribute_config.RepairTimeValue, 0)
	err := json.Unmarshal([]byte(jsonStr), &repairTimeValue)
	if err != nil {
		return nil
	}
	result := make(map[string]int)
	for _, value := range repairTimeValue {
		result[value.Label] = int(value.Value)
	}
	return result
}

// getPocs 函数用于从Elasticsearch中检索与给定IP列表匹配的漏洞利用（Poc）列表。
//
// 参数：
// - ips []string: 一个字符串切片，包含需要检索的IP地址列表。
//
// 返回值：
// - []poc.Poc: 一个Poc切片，包含从Elasticsearch中检索到的与给定IP列表匹配的漏洞利用列表。
// - error: 如果在检索过程中发生错误，则返回错误信息；否则返回nil。
func getPocs(ips []*poc_auto_distribute_config.IpArea, staff staff.Staff, conf *models.PocAutoDistributeConfig) error {
	logger.Infof("getPocs 获取用户: %v,IpsNum: %v", staff.Name, len(ips))
	ipInterface := make([]interface{}, 0)
	areaIpMap := make(map[string][]string)
	theatHistory := make([]threat_histories.ThreatHistory, 0) //创建漏洞流转历史记录
	threatTask := make(map[string][]string)                   // 创建定时任务数据
	//oneHistory := new(threat_history.OneThreatHistory)
	pocObj := make([]map[string]interface{}, 0)
	for _, ip := range ips {
		ipInterface = append(ipInterface, ip.Ip)
		areaIpMap[ip.Area] = append(areaIpMap[ip.Area], ip.Ip)
	}

	boolQuery := elastic.NewBoolQuery()
	boolQuery.Must(elastic.NewTermsQuery("ip.keyword", ipInterface...))
	boolQuery.Must(elastic.NewTermsQuery("status", []interface{}{0, 1}...))

	scroll := es.GetEsClient().Scroll().
		Index(poc.NewPoc().IndexName()).
		Query(boolQuery).
		Size(1000).
		Scroll("1m")
	defer scroll.Clear(context.Background())
	pocEmailData := make([]threat_history.PocEmailData, 0)
	for {
		searchResult, err := scroll.Do(context.Background())
		if err == io.EOF {
			break
		}

		if searchResult != nil {
			if searchResult.Hits.TotalHits.Value == 0 {
				break
			}

			if len(searchResult.Hits.Hits) == 0 {
				break
			}
			for _, hit := range searchResult.Hits.Hits {
				pocSource := poc.Poc{}
				err = json.Unmarshal(hit.Source, &pocSource)
				if err != nil {
					logger.Error("searchResult getPocs Unmarshal error", err)
					continue
				}
				// 判断漏洞是否在用户分配的区域
				if _, ok := areaIpMap[strconv.FormatUint(pocSource.Area, 10)]; !ok {
					continue
				}
				limitDate := ""
				if conf.ShowRepair == 1 {
					if conf.RepairTimeType == "level" {
						limitDate = repairLevelPriorityTime(conf.RepairTimeValue, pocSource.Level)
					}

					if conf.RepairTimeType == "repair" {
						limitDate = repairPriorityTime(conf.RepairTimeValue, pocSource.RepairPriority)
					}
					if limitDate == "" {
						logger.Infof("自动派发类型：%v,自动派发策略: %v,漏洞id: %v,漏洞名称: %v,漏洞等级: %v,漏洞修复优先级: %v", conf.RepairTimeType, conf.RepairTimeValue, pocSource.Id, pocSource.Name, pocSource.Level, pocSource.RepairPriority)
					}
				}

				tocc := make([]string, 0)
				_ = json.Unmarshal([]byte(conf.SendPersonId), &tocc)
				theatHistory = append(theatHistory, threat_histories.ThreatHistory{
					PocId:         pocSource.Id,
					FromUserId:    0,
					FromUserName:  "系统",
					LimitDate:     limitDate,
					SendNotice:    int(conf.SendNotice),
					TimeoutNotice: int(conf.TimeoutNotice),
					Descrition:    conf.Descrition,
					ToCc:          strings.Join(tocc, ","),
					NewStatus:     poc.PocStatusOfBeRepair,
					Status:        pocSource.Status,
					Operation:     threat_history.GetOperateContent("系统", poc.PocStatusOfBeRepair, "漏洞", staff.Name),
					Category:      "operation",
				})
				pocObj = append(pocObj, map[string]interface{}{
					"name":            pocSource.Name,
					"repair_priority": pocSource.RepairPriority,
					"id":              pocSource.Id,
					"limit_date":      limitDate,
				})
				businessNames := make([]string, 0)
				if len(pocSource.Business) > 0 {
					for _, business := range pocSource.Business {
						if len(business.System) > 0 {
							businessNames = append(businessNames, business.System)
						}
					}
				}
				pocEmailData = append(pocEmailData, threat_history.PocEmailData{
					Name:          pocSource.Name,
					Level:         pocSource.Level,
					BusinessNames: businessNames,
					Ip:            pocSource.Ip,
					Url:           poc.CheckUrl(pocSource.Url),
				})
				// 按日期归类并追加 poc_ids
				threatTask[limitDate] = append(threatTask[limitDate], pocSource.Id)
			}
		}
	}
	if len(theatHistory) > 0 {
		logger.Infof("自动派发类型：%v,自动派发策略: %v,数量：%v", conf.RepairTimeType, conf.RepairTimeValue, len(theatHistory))
		if conf.SendNotice == 1 && conf.SendPersonId != "" {
			err := sendNotice(conf, &staff, len(theatHistory), pocEmailData)
			if err != nil {
				logger.Errorf("sendNotice: %v", err)
				return err
			}
		}

		go updateThreatStatus(pocObj, &staff)
		go setThreatHistory(theatHistory)
		go threat_history.SendLocalMsgNotice("系统", 0, "", len(theatHistory), "派发")

		// 如果是超期通知则需要创建定时任务
		if conf.TimeoutNotice == 1 && conf.ShowRepair == 1 && conf.TimeoutPersonId != "" {
			for lDate, pocIds := range threatTask {
				if lDate != "" {
					createThreatTask(conf, lDate, strings.Join(pocIds, ","), &staff)
				}
			}
		}
	}
	logger.Infof("end getPocs 获取用户: %v,IpsNum: %v,数量：%v", staff.Name, len(ips), len(theatHistory))
	return nil
}

// setThreatHistory 创建漏洞流转历史记录
func setThreatHistory(items []threat_histories.ThreatHistory) {
	for _, item := range items {
		err := threat_histories.NewThreatHistoryModel().Create(&item)
		if err != nil {
			logger.Errorf("setThreatHistory 创建漏洞流转历史记录失败: %v", err)
		}
	}
}

// createThreatTask 如果是超期通知则需要创建定时任务
func createThreatTask(conf *models.PocAutoDistributeConfig, lDate, pocIds string, staff *staff.Staff) {
	// 创建定时任务
	layout := "2006-01-02 15:04:05"
	lDate = lDate + " " + time.Now().Format("15:04:05")
	limitDate, err := time.ParseInLocation(layout, lDate, time.Local)
	if err != nil {
		logger.Errorf("createThreatTask 解析时间失败: %v", err)
	}
	toc := make([]string, 0)
	_ = json.Unmarshal([]byte(conf.TimeoutPersonId), &toc)

	toEmail := ""
	if len(staff.Email) > 0 {
		toEmail = staff.Email[0]
	}
	task := threat_tasks.ThreatTask{
		UserId:        0,
		LimitDate:     &limitDate,
		TimeoutNotice: 1,
		Status:        0,
		TaskType:      threat_tasks.TaskTypeTimeoutNotice,
		Genre:         threat_tasks.GenreMulti,
		PocIds:        pocIds,
		ToStaffId:     staff.Id,
		ToStaffEmail:  toEmail,
		ToCc:          strings.Join(toc, ","),

		TimeoutFrequency:  int(conf.TimeoutFrequency),
		TimeoutReceiverId: strings.Join(jsonToString(conf.TimeoutPersonId), ","),
	}

	err = threat_tasks.NewThreatTaskModel().Create(&task)
	if err != nil {
		logger.Errorf("createThreatTask 创建定时任务失败: %v", err)
	}
}

// sendWebhookMsg 发送webhook消息 #TODO:暂时先不调用
func sendWebhookMsg(his *threat_history.OneThreatHistory, staff *staff.Staff, pocObj []map[string]interface{}) {
	user := user.User{
		Username: "系统",
	}
	err := threat_history.SendWebhookMsg(his, staff, &user, pocObj)
	if err != nil {
		logger.Errorf("sendWebhookMsg 发送webhook消息失败: %v", err)
	}
}

// sendNotice 发送邮件通知
func sendNotice(conf *models.PocAutoDistributeConfig, staffObj *staff.Staff, pocNum int, pocEmailData []threat_history.PocEmailData) error {
	mail := ""
	if len(staffObj.Email) > 0 {
		mail = staffObj.Email[0]
	}
	logger.Infof("SendNotice 发送邮件通知给: %v", mail)
	if conf.SendNotice == 1 && mail != "" {
		title, content := threat_history.GetEmailContent(poc.PocStatusOfBeRepair, staffObj.Name, pocNum, pocEmailData)
		cc := []string{}
		if conf.SendPersonId != "" {
			// 抄送
			sendPersonId := make([]string, 0)
			_ = json.Unmarshal([]byte(conf.SendPersonId), &sendPersonId)
			ccEmails, err := threat_history.GetStaffsEmails(sendPersonId)
			if err == nil {
				for _, v := range ccEmails {
					if len(v) > 0 && strings.Contains(v, "@") {
						cc = append(cc, v)
					}
				}
			}
		}
		logger.Infof("SendNotice 抄送: %v", cc)
		// 发送邮件
		// 收件人、抄送、 邮件标题、邮件内容、附件（可选）
		err := email.NewEmail().CommonSend([]string{mail}, cc, title, content)
		if err != nil {
			fmt.Println("邮件发送失败: ", err.Error())
			return err
		}
	}
	logger.Infof("SendNotice 发送邮件通知给: %v 成功", mail)
	return nil
}

// UpdateThreatStatus　更新漏洞流转状态
func updateThreatStatus(pocObjs []map[string]interface{}, staffObj *staff.Staff) {
	for _, obj := range pocObjs {
		err := threat_history.PushQueue(staffObj, strconv.Itoa(poc.PocStatusOfBeRepair), obj, nil)
		if err != nil {
			logger.Errorf("updateThreatStatus 更新漏洞流转状态失败id:%v,err: %v", obj["id"], err)
		}
	}
}

// getUserInfo 根据用户ID获取用户的电子邮件地址
//
// 参数:
// userId string: 用户的唯一标识符
//
// 返回值:
// staffSource: 用户信息
// error: 如果在获取用户信息时发生错误，则返回相应的错误信息
//
// 函数逻辑:
// 1. 初始化一个staffSource，用于存储用户信息
// 2. 创建一个 BoolQuery 查询对象，并设置必须匹配的条件为用户ID等于传入的 userId
// 3. 使用 Elasticsearch 客户端执行搜索查询，查询条件为构建的 BoolQuery，查询索引为 staff.NewStaff().IndexName() 返回的索引名
// 4. 如果查询过程中出现错误，记录错误日志并返回错误信息和空字符串
// 5. 如果查询结果不为空且命中总数大于0，遍历查询结果中的每一个命中项
// 6. 对于每一个命中项，将其 Source 字段（即文档内容）解析为 staff.Staff 结构体实例
// 7. 如果解析过程中出现错误，记录错误日志并继续遍历下一个命中项
// 8. 如果解析成功，将用户的电子邮件地址赋值给 email 变量
// 9. 遍历结束后，返回 staffSource 变量和 nil（表示没有错误发生）
func getUserInfo(userIds []interface{}) (staff.Staff, error) {
	var staffSource staff.Staff
	boolQuery := elastic.NewBoolQuery()
	boolQuery.Must(elastic.NewTermsQuery("id", userIds...))
	searchResult, err := es.GetEsClient().Search().
		Index(staff.NewStaff().IndexName()).
		Query(boolQuery).
		Do(context.TODO())
	if err != nil {
		logger.Errorf("get user info error: %v", err)
		return staffSource, err
	}
	if searchResult.Hits != nil && searchResult.Hits.TotalHits.Value > 0 {
		for _, hit := range searchResult.Hits.Hits {
			err = json.Unmarshal(hit.Source, &staffSource)
			if err != nil {
				logger.Error("searchResult getUserInfo Unmarshal error", err)
				continue
			}
		}
	}
	return staffSource, nil
}

// getAssets 函数根据提供的类型（genre）获取资产列表，并返回包含用户信息和IP列表的切片
//
// 参数:
// genre string: 类型，可以是 "oper" 或 "business"，表示获取不同类型的资产列表
//
// 返回值:
// []poc_auto_distribute_config.UserInfo: 包含用户信息和IP列表的切片
// error: 如果发生错误，则返回错误信息；否则返回nil
//
// 函数逻辑:
// 1. 初始化一个空的用户信息切片 userIpList
// 2. 根据 genre 构建 BoolQuery 和 NestedAggregation
//   - 如果 genre 是 "oper" 或 "business"，则根据 genre 设置相应的路径和字段，并构建 NestedQuery 和 NestedAggregation
//   - 否则，返回错误信息
//
// 3. 使用 Elasticsearch 客户端执行查询，查询条件为构建的 BoolQuery，聚合条件为构建的 NestedAggregation
// 4. 如果查询失败，记录错误日志并返回错误信息和空的用户信息切片
// 5. 解析查询结果，提取用户信息和IP列表，填充到 userIpList 中
// 6. 如果解析失败，记录错误日志
// 7. 返回填充后的 userIpList 和可能的错误信息
func getAssets(genre string) ([]*poc_auto_distribute_config.UserInfo, error) {
	userIpList := make([]*poc_auto_distribute_config.UserInfo, 0)
	// 构建 BoolQuery 和 NestedAggregation
	boolQuery := elastic.NewBoolQuery()
	nestedAgg := elastic.NewNestedAggregation()
	nestedQuery := elastic.NewNestedQuery("", elastic.NewBoolQuery().Must(elastic.NewExistsQuery("id")).MustNot(elastic.NewTermQuery("id", "")))

	if genre == "oper" || genre == "business" {
		path, field := "", ""
		if genre == "oper" {
			path = "oper_info"
			field = "oper_info.id" //用户id

			nestedAgg = elastic.NewNestedAggregation().
				Path(path).
				SubAggregation("user_id_terms", elastic.NewTermsAggregation().
					Field(field).
					Size(100000).ShardSize(100000).
					SubAggregation("ip_area_list", elastic.NewReverseNestedAggregation().
						SubAggregation("ip_terms", elastic.NewTermsAggregation().
							Field("ip.keyword").
							Size(100000).ShardSize(100000).
							SubAggregation("area_terms", elastic.NewTermsAggregation().
								Field("area").
								Size(100000).ShardSize(100000)))))

			nestedQuery = elastic.NewNestedQuery(path,
				elastic.NewBoolQuery().Must(elastic.NewExistsQuery(field)).
					MustNot(elastic.NewTermQuery(field, "")),
			)
		} else if genre == "business" {
			path = "business"
			field = "business.person_base.id" //用户id

			// 两层 Nested Aggregation
			nestedAgg = elastic.NewNestedAggregation().
				Path("business").
				SubAggregation("person_base_nested", elastic.NewNestedAggregation().
					Path("business.person_base").
					SubAggregation("user_id_terms", elastic.NewTermsAggregation().
						Field("business.person_base.id").
						Size(100000).ShardSize(100000).
						SubAggregation("ip_area_list", elastic.NewReverseNestedAggregation().
							SubAggregation("ip_terms", elastic.NewTermsAggregation().
								Field("ip.keyword").
								Size(100000).ShardSize(100000).
								SubAggregation("area_terms", elastic.NewTermsAggregation().
									Field("area").
									Size(100000).ShardSize(100000))))))

			nestedQuery = elastic.NewNestedQuery("business",
				elastic.NewNestedQuery("business.person_base",
					elastic.NewBoolQuery().
						Must(elastic.NewExistsQuery("business.person_base.id")).
						MustNot(elastic.NewTermQuery("business.person_base.id", "")),
				),
			)
		}

		boolQuery = boolQuery.Must(nestedQuery)
	} else {
		return nil, fmt.Errorf("invalid genre: %s", genre)
	}

	// 执行查询
	searchResult, err := es.GetEsClient().Search().
		Index(assets.NewAssets().IndexName()).
		Query(boolQuery).
		Size(0).
		Aggregation("user_info", nestedAgg).
		Do(context.TODO())
	if err != nil {
		logger.Errorf("getAssets 获取资产列表失败: %v", err.Error())
		return userIpList, err
	}

	// 解析聚合结果
	userIpList, err = parseAggregationResult(searchResult)
	if err != nil {
		logger.Errorf("getAssets 解析聚合结果失败: %v", err.Error())
	}
	return userIpList, err
}

// parseAggregationResult 函数用于解析 Elasticsearch 的聚合查询结果，提取用户信息和对应的 IP 列表
//
// 参数:
//
//	searchResult: *elastic.SearchResult 类型，指向 Elasticsearch 搜索结果的指针
//
// 返回值:
//
//	[]poc_auto_distribute_config.UserInfo: 包含用户信息和 IP 列表的切片
//	error: 如果解析过程中出现错误，则返回错误信息；否则返回 nil
//
// 函数逻辑:
//  1. 初始化一个空的用户信息切片 userIpList
//  2. 从搜索结果中获取名为 "user_info" 的嵌套聚合结果
//  3. 如果未找到 "user_info" 聚合结果，则返回空的用户信息切片和 nil
//  4. 从 "user_info" 聚合结果中获取名为 "user_id_terms" 的术语聚合结果
//  5. 如果未找到 "user_id_terms" 聚合结果，则返回空的用户信息切片和 nil
//  6. 遍历 "user_id_terms" 聚合结果的桶，提取每个桶中的 user_id
//  7. 对于每个 user_id，从桶的聚合结果中获取名为 "ip_list" 的反向嵌套聚合结果
//  8. 如果未找到 "ip_list" 聚合结果，则跳过当前循环
//  9. 从 "ip_list" 聚合结果中获取名为 "ip_trems" 的术语聚合结果
//  10. 如果未找到 "ip_trems" 聚合结果，则跳过当前循环
//  11. 遍历 "ip_trems" 聚合结果的桶，提取每个桶中的 IP 地址
//  12. 将提取到的 IP 地址添加到当前 user_id 对应的 IP 列表中
//  13. 将用户信息和 IP 列表添加到 userIpList 切片中
//  14. 返回填充后的 userIpList 切片和 nil
func parseAggregationResult(searchResult *elastic.SearchResult) ([]*poc_auto_distribute_config.UserInfo, error) {
	userIpList := make([]*poc_auto_distribute_config.UserInfo, 0)

	aggResult, found := searchResult.Aggregations.Nested("user_info")
	if !found {
		return userIpList, nil // 没有结果，返回空列表
	}

	// 兼容两种嵌套结构：oper（单层）和 business（双层）
	// 判断是否是 business（双层嵌套）
	if personBaseAgg, ok := aggResult.Aggregations.Nested("person_base_nested"); ok {
		// business 类型处理
		userTerms, userFound := personBaseAgg.Aggregations.Terms("user_id_terms")
		if !userFound {
			return userIpList, nil
		}

		for _, bucket := range userTerms.Buckets {
			userId := bucket.Key.(string)

			ipAreaAgg, ipListFound := bucket.Aggregations.ReverseNested("ip_area_list")
			if !ipListFound {
				continue
			}

			var ipAreas []*poc_auto_distribute_config.IpArea
			if ipTerms, found := ipAreaAgg.Aggregations.Terms("ip_terms"); found {
				for _, ipBucket := range ipTerms.Buckets {
					ip := ipBucket.Key.(string)

					if areaTerms, found := ipBucket.Aggregations.Terms("area_terms"); found {
						for _, areaBucket := range areaTerms.Buckets {
							area := fmt.Sprintf("%v", areaBucket.Key)
							ipAreas = append(ipAreas, &poc_auto_distribute_config.IpArea{Ip: ip, Area: area})
						}
					}
				}
			}

			userIpList = append(userIpList, &poc_auto_distribute_config.UserInfo{
				UserId: userId,
				Ips:    ipAreas,
			})
		}
	} else {
		// oper 类型处理（单层结构）
		userTerms, userFound := aggResult.Aggregations.Terms("user_id_terms")
		if !userFound {
			return userIpList, nil
		}

		for _, bucket := range userTerms.Buckets {
			userId := bucket.Key.(string)

			ipAreaAgg, ipListFound := bucket.Aggregations.ReverseNested("ip_area_list")
			if !ipListFound {
				continue
			}

			var ipAreas []*poc_auto_distribute_config.IpArea
			if ipTerms, found := ipAreaAgg.Aggregations.Terms("ip_terms"); found {
				for _, ipBucket := range ipTerms.Buckets {
					ip := ipBucket.Key.(string)

					if areaTerms, found := ipBucket.Aggregations.Terms("area_terms"); found {
						for _, areaBucket := range areaTerms.Buckets {
							area := fmt.Sprintf("%v", areaBucket.Key)
							ipAreas = append(ipAreas, &poc_auto_distribute_config.IpArea{Ip: ip, Area: area})
						}
					}
				}
			}

			userIpList = append(userIpList, &poc_auto_distribute_config.UserInfo{
				UserId: userId,
				Ips:    ipAreas,
			})
		}
	}

	return userIpList, nil
}

// repairPriorityTime 根据修复优先级和天数获取修复时间
func repairLevelPriorityTime(repairPriority string, pocLevel int) string {
	repairTime := ""
	repairTimeValue := getJsonToMap(repairPriority)

	// 定义优先级到天数的映射（针对 level）
	levelDays := map[int]string{
		1: "low",
		2: "middle",
		3: "high",
		4: "super",
	}
	// 判断是否是 level 类型
	if level, ok := levelDays[pocLevel]; ok {
		if day, exists := repairTimeValue[level]; exists {
			if day == 0 {
				return repairTime
			}
			repairTime = time.Now().AddDate(0, 0, day).Format("2006-01-02")
		}
	}
	return repairTime
}
func repairPriorityTime(repairPriorityValue, repairPriority string) string {
	repairTime := ""
	repairTimeValue := make([]struct {
		Label string `json:"label"`
		Value int    `json:"value"`
	}, 0)

	// 解析JSON输入
	err := json.Unmarshal([]byte(repairPriorityValue), &repairTimeValue)
	if err != nil {
		logger.Errorf("repairPriorityTime 解析自动派发配置失败: %v", err.Error())
		return repairTime
	}
	// 遍历修复优先级值
	for _, value := range repairTimeValue {
		if value.Label == repairPriority && value.Value == 0 {
			return ""
		}
		if value.Label == repairPriority {
			repairTime = time.Now().AddDate(0, 0, value.Value).Format("2006-01-02")
			break
		}
	}
	return repairTime
}
