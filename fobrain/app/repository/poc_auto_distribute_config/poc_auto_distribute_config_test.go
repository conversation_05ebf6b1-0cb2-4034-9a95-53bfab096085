package poc_auto_distribute_config

import (
	"context"
	"encoding/json"
	"errors"
	"io"
	"reflect"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/olivere/elastic/v7"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"

	"fobrain/fobrain/app/repository/email"
	"fobrain/fobrain/app/repository/threat_history"
	"fobrain/fobrain/app/request/poc_auto_distribute_config"
	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/initialize/mysql"
	"fobrain/models/elastic/staff"
	models "fobrain/models/mysql/poc_auto_distribute_config"
	"fobrain/models/mysql/threat_histories"
)

func TestInfoNil(t *testing.T) {
	time.Sleep(time.Millisecond * 300)
	p := gomonkey.ApplyMethodReturn(models.NewPocAutoDistributeConfigModel(), "First", nil, gorm.ErrRecordNotFound)
	info, err := Info()
	p.Reset()
	assert.NoError(t, err)
	assert.NotNil(t, info)
}

func TestInfo(t *testing.T) {
	poc := models.PocAutoDistributeConfig{
		BaseModel: mysql.BaseModel{
			Id: 1,
		},
		IsAutoDispatch:     1,
		RepairTimeType:     "level",
		RepairTimeValue:    `[{"label":"p0","value":1}]`,
		VulRepairPrincipal: "oper",
		SendNotice:         1,
		SendPersonId:       `["11111"]`,
		TimeoutNotice:      1,
		TimeoutPersonId:    `["2222"]`,
		Descrition:         "ssss",
	}
	time.Sleep(time.Millisecond * 300)
	p := gomonkey.ApplyMethodReturn(models.NewPocAutoDistributeConfigModel(), "First", poc, nil)
	info, err := Info()
	p.Reset()
	assert.NoError(t, err)
	assert.NotNil(t, info)
}
func TestCreateErr(t *testing.T) {
	param := poc_auto_distribute_config.PocAutoDistributeConfigCreate{}
	time.Sleep(time.Millisecond * 300)
	p := gomonkey.ApplyMethodReturn(models.NewPocAutoDistributeConfigModel(), "First", nil, errors.New("err"))
	err := Create(&param)
	p.Reset()
	assert.Error(t, err)
}

func TestCreateSuccess(t *testing.T) {
	param := poc_auto_distribute_config.PocAutoDistributeConfigCreate{
		IsAutoDispatch:     1,
		RepairTimeType:     "level",
		RepairTimeValue:    []poc_auto_distribute_config.RepairTimeValue{{Label: "p0", Value: 1}},
		VulRepairPrincipal: "oper",
		SendNotice:         1,
		SendPersonId:       []string{""},
		TimeoutNotice:      1,
		TimeoutPersonId:    []string{""},
		Descrition:         "ssss",
		ShowRepair:         1,
	}
	con := models.PocAutoDistributeConfig{}
	time.Sleep(time.Millisecond * 300)
	p := gomonkey.ApplyMethodReturn(models.NewPocAutoDistributeConfigModel(), "First", con, nil)
	time.Sleep(time.Millisecond * 300)
	p.ApplyMethodReturn(models.NewPocAutoDistributeConfigModel(), "CreateItem", nil)
	err := Create(&param)
	p.Reset()
	assert.NoError(t, err)
}

func TestUpdate(t *testing.T) {
	param := poc_auto_distribute_config.PocAutoDistributeConfigCreate{
		IsAutoDispatch:     1,
		RepairTimeType:     "level",
		RepairTimeValue:    []poc_auto_distribute_config.RepairTimeValue{{Label: "p0", Value: 1}},
		VulRepairPrincipal: "oper",
		SendNotice:         1,
		SendPersonId:       []string{},
		TimeoutNotice:      1,
		TimeoutPersonId:    []string{},
		Descrition:         "ssss",
		ShowRepair:         1,
	}
	con := models.PocAutoDistributeConfig{
		BaseModel: mysql.BaseModel{
			Id: 1,
		},
		IsAutoDispatch:     1,
		RepairTimeType:     "level",
		RepairTimeValue:    `[{"label":"p0","value":1}]`,
		VulRepairPrincipal: "oper",
		SendNotice:         1,
		SendPersonId:       `["11111"]`,
		TimeoutNotice:      1,
		TimeoutPersonId:    `["2222"]`,
		Descrition:         "ssss",
		ShowRepair:         1,
	}
	time.Sleep(time.Millisecond * 300)
	p := gomonkey.ApplyMethodReturn(models.NewPocAutoDistributeConfigModel(), "First", con, nil)
	p.ApplyMethodReturn(models.NewPocAutoDistributeConfigModel(), "Update", nil)
	err := Create(&param)
	p.Reset()
	assert.NoError(t, err)
}

func TestGetAssets(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()
	mockServer.Register("/asset/_search", elastic.SearchResult{
		Aggregations: elastic.Aggregations{
			"user_info": json.RawMessage(`{
			  "doc_count" : 2,
			  "user_id_terms" : {
				"doc_count_error_upper_bound" : 0,
				"sum_other_doc_count" : 0,
				"buckets" : [ {
				  "key" : "65aa2171d6a543a4844366169e26c096",
				  "doc_count" : 2,
				  "ip_list" : {
					"doc_count" : 2,
					"ip_trems" : {
					  "doc_count_error_upper_bound" : 0,
					  "sum_other_doc_count" : 0,
					  "buckets" : [ {
						"key" : "*************",
						"doc_count" : 1
					  }, {
						"key" : "************",
						"doc_count" : 1
					  } ]
					}
				  }
				} ]
			  }
			}`),
		},
	})
	assets, err := getAssets("oper")
	assert.NoError(t, err)
	assert.NotNil(t, assets)
}

func TestExecPocAutoDistribute(t *testing.T) {
	poc := models.PocAutoDistributeConfig{
		BaseModel: mysql.BaseModel{
			Id: 1,
		},
		IsAutoDispatch:     1,
		RepairTimeType:     "level",
		RepairTimeValue:    `[{"label":"super","value":30},{"label":"high","value":15},{"label":"middle","value":7},{"label":"low","value":0}]`,
		VulRepairPrincipal: "oper",
		SendNotice:         1,
		SendPersonId:       `["41de794636904b829cac76aa11a7a7bc"]`,
		TimeoutNotice:      1,
		TimeoutPersonId:    `[]`,
		Descrition:         "ssss",
	}
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyMethodReturn(models.NewPocAutoDistributeConfigModel(), "First", poc, nil).Reset()
	userAssets := []*poc_auto_distribute_config.UserInfo{
		{
			UserId: "dfdladfhdkfhahdkfjhakfhda",
			Ips:    []*poc_auto_distribute_config.IpArea{{Ip: "127.0.0.1", Area: "1"}},
		},
	}
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyFuncReturn(getAssets, userAssets, nil).Reset()
	staffInfo := staff.Staff{
		Id: "sldkfjlsfjsldjf",
	}
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyFuncReturn(getUserInfo, staffInfo, nil).Reset()
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyFuncReturn(getPocs, nil).Reset()

	err := ExecPocAutoDistribute()
	assert.NoError(t, err)
}

func TestGetPocs(t *testing.T) {
	s := staff.Staff{
		Id:    "dsflahdfkdah",
		Name:  "凤梨酥",
		Email: []string{"<EMAIL>"},
	}
	poc := models.PocAutoDistributeConfig{
		BaseModel: mysql.BaseModel{
			Id: 1,
		},
		IsAutoDispatch:     1,
		RepairTimeType:     "level",
		RepairTimeValue:    `[{"label":"super","value":30},{"label":"high","value":15},{"label":"middle","value":7},{"label":"low","value":0}]`,
		VulRepairPrincipal: "oper",
		SendNotice:         2,
		SendPersonId:       `[]`,
		TimeoutNotice:      2,
		TimeoutPersonId:    `[]`,
		Descrition:         "ssss",
	}
	// 构造模拟的真实数据
	mockData := `{"id":"dfceb9a114624918a38c4ebcdf03d542","fid":"0:0:0:http://***********:2181","fid_hash":"61e55fb846c6214e33b542f398950940","original_ids":["f5a2bd56735787ca446483ec105041b1"],"original_ids_source":{"1-16":"f5a2bd56735787ca446483ec105041b1"},"area":1,"process_ids":["16_1_f5a2bd56735787ca446483ec105041b1"],"source_ids":[1],"node_ids":[16],"poc_task_ids":["250_16_1_f5a2bd56735787ca446483ec105041b1"],"all_source_ids":[1],"all_node_ids":[16],"all_poc_task_ids":["250_16_1_f5a2bd56735787ca446483ec105041b1"],"all_process_ids":["16_1_f5a2bd56735787ca446483ec105041b1"],"ip":"***********","ip_source":null,"ip_type":1,"network_type":1,"port":2181,"port_source":null,"is_poc":1,"is_poc_source":null,"url":"http://***********:2181","url_source":null,"level":3,"level_source":null,"cve":"","cve_source":null,"cnvd":"","cnvd_source":null,"cnnvd":"","cnnvd_source":null,"has_exp":1,"has_exp_source":null,"has_poc":1,"has_poc_source":null,"status":0,"status_source":null,"person":null,"name":"Apache Zookeeper 未授权访问漏洞","name_source":null,"vulType":"未授权访问","vulType_source":null,"describe":"<p>Zookeeper 是一个开源的分布式协调服务，用于管理和协调大规模分布式应用程序的配置信息、命名服务、分布式锁和分布式队列等。<br></p><p>Zookeeper 存在未授权访问漏洞，攻击者可以利用该漏洞通过网络访问并获取未经授权的Zookeeper服务器资源。<br></p>","describe_source":null,"details":"<p>Zookeeper 存在未授权访问漏洞，攻击者可以利用该漏洞通过网络访问并获取未经授权的Zookeeper服务器资源。<br></p>","details_source":null,"hazard":"","hazard_source":null,"suggestions":"<p>1、修改 ZooKeeper 默认端口，采用其他端口服务，配置服务来源地址限制策略。</p><p>2、增加 ZooKeeper 的认证配置。</p>","suggestions_source":null,"last_response":"","last_response_source":null,"created_at":"2024-12-12 17:06:31","updated_at":"2024-12-12 17:06:31","merge_count":1,"person_limit":null,"person_limit_hash":null,"risk_num":16,"repair_priority":"p3"}`
	var mockResponse map[string]interface{}
	err := json.Unmarshal([]byte(mockData), &mockResponse)
	assert.NoError(t, err)

	// 模拟 BuildQuery 返回值
	mockScroll1 := &elastic.SearchResult{
		Hits: &elastic.SearchHits{
			TotalHits: &elastic.TotalHits{
				Value: 1,
			},
			Hits: []*elastic.SearchHit{
				{
					Source: json.RawMessage(mockData),
				},
			},
		},
	}
	// 第二轮返回空数据
	mockScroll2 := &elastic.SearchResult{
		Hits: &elastic.SearchHits{
			TotalHits: &elastic.TotalHits{
				Value: 0,
			},
			Hits: []*elastic.SearchHit{},
		},
	}
	// 模拟调用次数
	callCount := 0
	// 使用 gomonkey 模拟 `scroll.Do` 方法
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyMethod(reflect.TypeOf(&elastic.ScrollService{}), "Do", func(_ *elastic.ScrollService, ctx context.Context) (*elastic.SearchResult, error) {
		if callCount == 0 {
			callCount++
			return mockScroll1, nil // 第一次返回有效数据
		} else if callCount == 1 {
			callCount++
			return mockScroll2, io.EOF // 第二次返回 EOF，模拟循环结束
		}
		return nil, io.EOF
	}).Reset()
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyFuncReturn(sendNotice, nil).Reset()
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyFuncReturn(threat_history.PushQueue, nil).Reset()
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyFuncReturn(threat_history.SendLocalMsgNotice, nil).Reset()
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyMethodReturn(threat_histories.NewThreatHistoryModel(), "Create", nil).Reset()

	err = getPocs([]*poc_auto_distribute_config.IpArea{{Ip: "127.0.0.1", Area: "1"}}, s, &poc)
	assert.NoError(t, err)
}

func TestRepairPriorityTime(t *testing.T) {
	repairPriorityValue := `[{"label":"p0","value":30},{"label":"p1","value":15},{"label":"p2","value":7},{"label":"p3","value":0}]`
	ti := repairPriorityTime(repairPriorityValue, "p1")
	repairTime := time.Now().AddDate(0, 0, 15).Format("2006-01-02")
	assert.Equal(t, repairTime, ti)
}

func TestRepairLevelPriorityTime(t *testing.T) {
	repairPriorityValue := `[{"label":"super","value":30},{"label":"high","value":15},{"label":"middle","value":7},{"label":"low","value":0}]`
	ti := repairLevelPriorityTime(repairPriorityValue, 2)
	repairTime := time.Now().AddDate(0, 0, 7).Format("2006-01-02")
	assert.Equal(t, repairTime, ti)
}

func TestGetUserInfo(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()
	mockData := `{"id":"20b57e40ae8f4cf8b1d7b8abbbba51a1","sso_id":"","sso_name":"","fid":"FOF","fid_hash":"1192ab6ca69703cd9d4c3b33865f03d4","original_ids":[""],"original_ids_source":{"4-8":""},"name":"FOF）","english_name":null,"english_name_source":{"4-8":""},"area":0,"area_source":{"4-8":1},"title":null,"title_source":{"4-8":""},"mobile":"18614280252","email":["<EMAIL>"],"department":["北京/a/b"],"department_source":{"4-8":"北京/a/b"},"status":1,"status_source":{"4-8":1},"process_ids":["8_1_FO）"],"departments_ids":["152"],"all_departments_ids":["102","113","152"],"source_ids":[4],"node_ids":[8],"staff_task_ids":["240_8_1_013948295126-1904135439"],"all_source_ids":[4],"all_node_ids":[8],"all_staff_task_ids":["240_8_1_013948295126-1904135439"],"all_process_ids":["8_1_FO）"],"created_at":"2024-12-06 14:38:18","updated_at":"2024-12-12 03:40:43","merge_count":2}`

	mockServer.Register("/staff/_search", elastic.SearchResult{
		TookInMillis: 1,
		TimedOut:     false,
		Shards: &elastic.ShardsInfo{
			Total:      5,
			Successful: 5,
			Skipped:    0,
			Failed:     0,
		},
		Hits: &elastic.SearchHits{
			TotalHits: &elastic.TotalHits{
				Value:    1,
				Relation: "eq",
			},
			Hits: []*elastic.SearchHit{
				{
					Source: json.RawMessage(mockData),
				},
			},
		},
	})
	info, err := getUserInfo([]interface{}{"20b57e40ae8f4cf8b1d7b8abbbba51a1"})
	assert.NoError(t, err)
	assert.Equal(t, "20b57e40ae8f4cf8b1d7b8abbbba51a1", info.Id)
}

func TestCreateThreatTask(t *testing.T) {
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()
	mockDb.Mock.ExpectBegin()
	mockDb.ExpectExec("INSERT INTO `threat_tasks` (`created_at`,`updated_at`,`user_id`,`to_staff_id`,`to_staff_email`,`limit_date`,`begin_time`,`timeout_notice`,`to_cc`,`poc_id`,`status`,`task_type`,`genre`,`poc_ids`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?)").
		WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg()).
		WillReturnResult(sqlmock.NewResult(1, 1))
	mockDb.Mock.ExpectCommit()

	st := staff.Staff{
		Id:    "20b57e40ae8f4cf8b1d7b8abbbba51a1",
		Email: []string{"<EMAIL>"},
	}
	createThreatTask(&models.PocAutoDistributeConfig{
		TimeoutPersonId:  "aaa,bbb,cc",
		TimeoutFrequency: 1,
	}, "2024-10-10", "111,2222,333,44", &st)
}

func TestSendNotice(t *testing.T) {
	poc := models.PocAutoDistributeConfig{
		BaseModel: mysql.BaseModel{
			Id: 1,
		},
		IsAutoDispatch:     1,
		RepairTimeType:     "level",
		RepairTimeValue:    `[{"label":"super","value":30},{"label":"high","value":15},{"label":"middle","value":7},{"label":"low","value":0}]`,
		VulRepairPrincipal: "oper",
		SendNotice:         1,
		SendPersonId:       `["41de794636904b829cac76aa11a7a7bc"]`,
		TimeoutNotice:      1,
		TimeoutPersonId:    `[]`,
		Descrition:         "ssss",
	}
	s := staff.Staff{
		Id:    "dsflahdfkdah",
		Name:  "凤梨酥",
		Email: []string{"<EMAIL>"},
	}
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyFuncReturn(threat_history.GetStaffsEmails, []string{"a:18135101111"}, nil).Reset()
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyMethodReturn(email.NewEmail(), "CommonSend", nil).Reset()
	err := sendNotice(&poc, &s, 1, []threat_history.PocEmailData{})
	assert.NoError(t, err)
}
