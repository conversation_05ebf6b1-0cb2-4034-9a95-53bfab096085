package custom_tags

import (
	"errors"
	"strconv"

	req "fobrain/fobrain/app/request/custom_tags"
	res "fobrain/fobrain/app/response/custom_tags"
	"fobrain/initialize/mysql"
	models "fobrain/models/mysql/custom_tags"
)

// Create create custom tags
func Create(params *req.InsertCustomTagsRequest) error {
	count, err := models.NewCustomTagsConfig().Count(mysql.WithWhere("tag_name = ?", params.TagName))
	if err != nil {
		return err
	}
	if count > 0 {
		return errors.New("标签已经存在，无法继续添加")
	}

	tagCreate := models.CustomTags{
		TagName: params.TagName,
	}
	err = models.NewCustomTagsConfig().CreateItem(&tagCreate)
	if err != nil {
		return err
	}
	return nil
}

// ALlList list custom tags
func ALlList(params *req.SearchCustomTagsRequest) ([]*res.List, int64, error) {
	var handleFuncs []mysql.HandleFunc
	if params.Keyword != "" {
		handleFuncs = append(handleFuncs, mysql.WithWhere("tag_name like ?", "%"+params.Keyword+"%"))
	}
	list, total, err := models.NewCustomTagsConfig().List(0, 0, handleFuncs...)
	if err != nil {
		return nil, 0, err
	}
	resList := make([]*res.List, 0)
	for _, tags := range list {
		resList = append(resList, &res.List{
			Name: tags.TagName,
			Id:   strconv.FormatUint(tags.Id, 10),
		})
	}
	return resList, total, nil
}
