package custom_tags

import (
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"

	req "fobrain/fobrain/app/request/custom_tags"
	testcommon "fobrain/fobrain/tests/common_test"
)

func TestCreate(t *testing.T) {
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()
	t.Run("Create Error", func(t *testing.T) {
		tagName := "自定义标签"
		mockDb.ExpectQuery("SELECT count(*) FROM `custom_tags` WHERE tag_name = ?").
			WithArgs(tagName).
			WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(1))
		params := req.InsertCustomTagsRequest{
			TagName: tagName,
		}
		err := Create(&params)
		assert.Equal(t, "标签已经存在，无法继续添加", err.Error())
		// 确保数据库期望被调用
		assert.NoError(t, mockDb.ExpectationsWereMet())
	})
	t.Run("Create", func(t *testing.T) {
		tagName := "tag标签"
		mockDb.ExpectQuery("SELECT count(*) FROM `custom_tags` WHERE tag_name = ?").
			WithArgs(tagName).
			WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(0))

		mockDb.Mock.ExpectBegin()
		// 设置插入操作，模拟标签插入
		mockDb.ExpectExec("INSERT INTO `custom_tags` (`created_at`,`updated_at`,`tag_name`) VALUES (?,?,?)").
			WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), tagName).
			WillReturnResult(sqlmock.NewResult(1, 1)) // 假设插入成功，返回一行受影响数据
		mockDb.Mock.ExpectCommit()

		params := req.InsertCustomTagsRequest{
			TagName: tagName,
		}
		err := Create(&params)
		assert.NoError(t, err)
		assert.NoError(t, mockDb.ExpectationsWereMet())
	})
}

func TestALlList(t *testing.T) {
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()
	mockDb.ExpectQuery("SELECT count(*) FROM `custom_tags`").
		WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(1))

	mockDb.ExpectQuery("SELECT * FROM `custom_tags`").
		WillReturnRows(mockDb.NewRows([]string{"id"}).AddRow(1))

	params := req.SearchCustomTagsRequest{
		Keyword: "",
	}
	list, total, err := ALlList(&params)
	assert.NoError(t, err)
	assert.Equal(t, int64(1), total)
	assert.Equal(t, 1, len(list))

	// 确保数据库期望的查询都被执行
	assert.NoError(t, mockDb.ExpectationsWereMet())
}
