package email

import (
	"fobrain/models/mysql/email_config"
)

func Get() (*email_config.EmailConfig, error) {
	return email_config.NewEmailConfig().Settings()
}

func Save(address string, port uint64, encryptType uint8, userName string, password string, category string) error {
	EmailConfig := email_config.NewEmailConfig()
	EmailConfig.Address = address
	EmailConfig.Port = port
	EmailConfig.EncryptType = encryptType
	EmailConfig.UserName = userName
	EmailConfig.Category = category
	if password != "" {
		EmailConfig.Password = password
	}
	return EmailConfig.UpdateOrCreate()
}

func TestMail() error {
	mail, _ := email_config.NewEmailConfig().Settings()

	e := NewEmail()
	// 添加收件人
	e.AddTo(mail.UserName)
	// 设置邮件主题
	e.Subject = "测试"
	// 设置邮件正文
	e.AddText("测试邮件")
	// 设置邮件正文为HTML格式
	e.AddHtml("<h1>测试邮件</h1>")

	// 发送邮件
	err := e.Send()
	if err != nil {
		return err
	}
	return nil
}
