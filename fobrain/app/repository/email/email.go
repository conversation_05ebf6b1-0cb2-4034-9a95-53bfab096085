package email

import (
	"bytes"
	"crypto/rand"
	"crypto/tls"
	"encoding/base64"
	"errors"
	"fmt"
	"io"
	"math"
	"math/big"
	"mime"
	"mime/multipart"
	"mime/quotedprintable"
	"net"
	"net/mail"
	"net/smtp"
	"net/textproto"
	"os"
	"path/filepath"
	"strings"
	"time"
)

type Email struct {
	ReplyTo     []string
	From        string // 发件人
	To          []string
	Bcc         []string
	Cc          []string
	Subject     string
	Text        []byte
	HTML        []byte
	Sender      string
	Headers     textproto.MIMEHeader
	Attachments []*Attachment
	ReadReceipt []string
	Addr        string
	Auth        smtp.Auth
}

type Attachment struct {
	Filename    string
	ContentType string
	Header      textproto.MIMEHeader
	Content     []byte
	HTMLRelated bool
}

const (
	// MaxLineLength
	// @Summary 邮件最大长度
	MaxLineLength = 76
)

func NewEmail() *Email {
	emailConfig, _ := Get()
	return &Email{
		From:    emailConfig.UserName,
		Addr:    fmt.Sprintf("%s:%d", emailConfig.Address, emailConfig.Port),
		Auth:    smtp.PlainAuth("", emailConfig.UserName, emailConfig.Password, emailConfig.Address),
		Headers: textproto.MIMEHeader{},
	}
}

// smtp.SendMail 函数存在异常，故而重写了一下
//func (email *Email) Send() error {
//	to := make([]string, 0, len(email.To)+len(email.Cc)+len(email.Bcc))
//	to = append(append(append(to, email.To...), email.Cc...), email.Bcc...)
//	for i := 0; i < len(to); i++ {
//		addr, err := mail.ParseAddress(to[i])
//		if err != nil {
//			return err
//		}
//		to[i] = addr.Address
//	}
//	if email.From == "" || len(to) == 0 {
//		return errors.New("must specify at least one From address and one To address")
//	}
//	sender, err := email.parseSender()
//	if err != nil {
//		return err
//	}
//	raw, err := email.Bytes()
//	if err != nil {
//		return err
//	}
//	fmt.Println("smtp.SendMail 参数：", email.Addr, email.Auth, sender, to, raw)
//
//	return smtp.SendMail(email.Addr, email.Auth, sender, to, raw)
//}

func (email *Email) Send() error {
	// 验证发件人和收件人是否设置正确
	to := make([]string, 0, len(email.To)+len(email.Cc)+len(email.Bcc))
	to = append(append(append(to, email.To...), email.Cc...), email.Bcc...)
	for i := 0; i < len(to); i++ {
		addr, err := mail.ParseAddress(to[i])
		if err != nil {
			return err
		}
		to[i] = addr.Address
	}
	if email.From == "" || len(to) == 0 {
		return errors.New("必须指定至少一个发件人地址和一个收件人地址")
	}

	// 解析发件人地址
	sender, err := email.parseSender()
	if err != nil {
		return err
	}

	// 生成邮件内容
	raw, err := email.Bytes()
	if err != nil {
		return err
	}

	// 自定义超时配置
	dialer := &net.Dialer{
		Timeout: 10 * time.Second, // 设置连接超时时间
	}

	// 设置 TLS 配置
	tlsConfig := &tls.Config{
		InsecureSkipVerify: true, // 仅用于测试，生产环境建议为 false
		ServerName:         strings.Split(email.Addr, ":")[0],
	}

	// 通过 TLS 建立连接
	conn, err := tls.DialWithDialer(dialer, "tcp", email.Addr, tlsConfig)
	if err != nil {
		return fmt.Errorf("无法连接到 SMTP 服务器: %v", err)
	}
	defer conn.Close()

	// 创建 SMTP 客户端
	client, err := smtp.NewClient(conn, strings.Split(email.Addr, ":")[0])
	if err != nil {
		return fmt.Errorf("无法创建 SMTP 客户端: %v", err)
	}
	defer client.Quit()

	// 进行身份认证
	if err := client.Auth(email.Auth); err != nil {
		return fmt.Errorf("SMTP 认证失败: %v", err)
	}

	// 设置发件人
	if err := client.Mail(sender); err != nil {
		return fmt.Errorf("设置发件人失败: %v", err)
	}

	// 设置收件人
	for _, recipient := range to {
		if err := client.Rcpt(recipient); err != nil {
			return fmt.Errorf("设置收件人失败: %v", err)
		}
	}

	// 发送邮件数据
	wc, err := client.Data()
	if err != nil {
		return fmt.Errorf("发送邮件数据失败: %v", err)
	}

	_, err = wc.Write(raw)
	if err != nil {
		return fmt.Errorf("写入邮件数据失败: %v", err)
	}

	// 关闭数据写入流
	err = wc.Close()
	if err != nil {
		return fmt.Errorf("关闭邮件数据写入失败: %v", err)
	}

	return nil
}

// CommonSend
// @Summary 通用发送邮件
func (email *Email) CommonSend(to, cc []string, subject string, body string, attachFile ...string) error {
	email.AddTo(to...)
	if len(cc) > 0 {
		email.AddCc(cc...)
	}
	email.Subject = subject
	email.AddHtml(body)

	if len(attachFile) > 0 {
		email.AddAttachFile(attachFile[0])
	}

	return email.Send()
}

// AddTo
// @Summary 收件人
func (email *Email) AddTo(To ...string) {
	email.To = To
}

// AddCc
// @Summary 抄送人
func (email *Email) AddCc(Cc ...string) {
	email.Cc = Cc
}

// AddBcc
// @Summary 密送人
func (email *Email) AddBcc(Bcc ...string) {
	email.Bcc = Bcc
}

// AddReplyTo
// @Summary 回复人
func (email *Email) AddReplyTo(ReplyTo ...string) {
	email.ReplyTo = ReplyTo
}

// AddText
// @Summary 邮件正文
func (email *Email) AddText(text any) {
	switch v := text.(type) {
	case string:
		email.Text = []byte(v) // 将字符串转换为 []byte
	case []byte:
		email.Text = v // 已经是 []byte，直接赋值
	default:
		fmt.Println("Unsupported type")
	}
}

// AddHtml
// @Summary 邮件正文
func (email *Email) AddHtml(html any) {
	switch v := html.(type) {
	case string:
		email.HTML = []byte(v) // 将字符串转换为 []byte
	case []byte:
		email.HTML = v // 已经是 []byte，直接赋值
	default:
		fmt.Println("Unsupported type")
	}
}

func (email *Email) Attach(r io.Reader, filename string, c string) (a *Attachment, err error) {
	var buffer bytes.Buffer
	if _, err = io.Copy(&buffer, r); err != nil {
		return
	}
	at := &Attachment{
		Filename:    filename,
		ContentType: c,
		Header:      textproto.MIMEHeader{},
		Content:     buffer.Bytes(),
	}
	email.Attachments = append(email.Attachments, at)
	return at, nil
}

// AddAttachFile
// @Summary 添加附件
// @Param filename 附件路径
func (email *Email) AddAttachFile(filename string) (a *Attachment, err error) {
	f, err := os.Open(filename)
	if err != nil {
		return
	}
	defer f.Close()

	ct := mime.TypeByExtension(filepath.Ext(filename))
	basename := filepath.Base(filename)
	return email.Attach(f, basename, ct)
}

func (email *Email) parseSender() (string, error) {
	if email.Sender != "" {
		sender, err := mail.ParseAddress(email.Sender)
		if err != nil {
			return "", err
		}
		return sender.Address, nil
	} else {
		from, err := mail.ParseAddress(email.From)
		if err != nil {
			return "", err
		}
		return from.Address, nil
	}
}

func writeMessage(buff io.Writer, msg []byte, multipart bool, mediaType string, w *multipart.Writer) error {
	if multipart {
		header := textproto.MIMEHeader{
			"Content-Type":              {mediaType + "; charset=UTF-8"},
			"Content-Transfer-Encoding": {"quoted-printable"},
		}
		if _, err := w.CreatePart(header); err != nil {
			return err
		}
	}

	qp := quotedprintable.NewWriter(buff)
	// Write the text
	if _, err := qp.Write(msg); err != nil {
		return err
	}
	return qp.Close()
}

func (email *Email) categorizeAttachments() (htmlRelated, others []*Attachment) {
	for _, a := range email.Attachments {
		if a.HTMLRelated {
			htmlRelated = append(htmlRelated, a)
		} else {
			others = append(others, a)
		}
	}
	return
}

func (email *Email) Bytes() ([]byte, error) {
	// TODO: better guess buffer size
	buff := bytes.NewBuffer(make([]byte, 0, 4096))

	headers, err := email.msgHeaders()
	if err != nil {
		return nil, err
	}

	htmlAttachments, otherAttachments := email.categorizeAttachments()
	if len(email.HTML) == 0 && len(htmlAttachments) > 0 {
		return nil, errors.New("there are HTML attachments, but no HTML body")
	}

	var (
		isMixed       = len(otherAttachments) > 0
		isAlternative = len(email.Text) > 0 && len(email.HTML) > 0
		isRelated     = len(email.HTML) > 0 && len(htmlAttachments) > 0
	)

	var w *multipart.Writer
	if isMixed || isAlternative || isRelated {
		w = multipart.NewWriter(buff)
	}
	switch {
	case isMixed:
		headers.Set("Content-Type", "multipart/mixed;\r\n boundary="+w.Boundary())
	case isAlternative:
		headers.Set("Content-Type", "multipart/alternative;\r\n boundary="+w.Boundary())
	case isRelated:
		headers.Set("Content-Type", "multipart/related;\r\n boundary="+w.Boundary())
	case len(email.HTML) > 0:
		headers.Set("Content-Type", "text/html; charset=UTF-8")
		headers.Set("Content-Transfer-Encoding", "quoted-printable")
	default:
		headers.Set("Content-Type", "text/plain; charset=UTF-8")
		headers.Set("Content-Transfer-Encoding", "quoted-printable")
	}
	headerToBytes(buff, headers)
	_, err = io.WriteString(buff, "\r\n")
	if err != nil {
		return nil, err
	}

	// Check to see if there is a Text or HTML field
	if len(email.Text) > 0 || len(email.HTML) > 0 {
		var subWriter *multipart.Writer

		if isMixed && isAlternative {
			// Create the multipart alternative part
			subWriter = multipart.NewWriter(buff)
			header := textproto.MIMEHeader{
				"Content-Type": {"multipart/alternative;\r\n boundary=" + subWriter.Boundary()},
			}
			if _, err := w.CreatePart(header); err != nil {
				return nil, err
			}
		} else {
			subWriter = w
		}
		// Create the body sections
		if len(email.Text) > 0 {
			// Write the text
			if err := writeMessage(buff, email.Text, isMixed || isAlternative, "text/plain", subWriter); err != nil {
				return nil, err
			}
		}
		if len(email.HTML) > 0 {
			messageWriter := subWriter
			var relatedWriter *multipart.Writer
			if (isMixed || isAlternative) && len(htmlAttachments) > 0 {
				relatedWriter = multipart.NewWriter(buff)
				header := textproto.MIMEHeader{
					"Content-Type": {"multipart/related;\r\n boundary=" + relatedWriter.Boundary()},
				}
				if _, err := subWriter.CreatePart(header); err != nil {
					return nil, err
				}

				messageWriter = relatedWriter
			} else if isRelated && len(htmlAttachments) > 0 {
				relatedWriter = w
				messageWriter = w
			}
			// Write the HTML
			if err := writeMessage(buff, email.HTML, isMixed || isAlternative || isRelated, "text/html", messageWriter); err != nil {
				return nil, err
			}
			if len(htmlAttachments) > 0 {
				for _, a := range htmlAttachments {
					a.setDefaultHeaders()
					ap, err := relatedWriter.CreatePart(a.Header)
					if err != nil {
						return nil, err
					}
					// Write the base64Wrapped content to the part
					base64Wrap(ap, a.Content)
				}

				if isMixed || isAlternative {
					relatedWriter.Close()
				}
			}
		}
		if isMixed && isAlternative {
			if err := subWriter.Close(); err != nil {
				return nil, err
			}
		}
	}

	for _, a := range otherAttachments {
		a.setDefaultHeaders()
		ap, err := w.CreatePart(a.Header)
		if err != nil {
			return nil, err
		}
		base64Wrap(ap, a.Content)
	}
	if isMixed || isAlternative || isRelated {
		if err := w.Close(); err != nil {
			return nil, err
		}
	}
	return buff.Bytes(), nil
}

// base64Wrap encodes the attachment content, and wraps it according to RFC 2045 standards (every 76 chars)
// The output is then written to the specified io.Writer
func base64Wrap(w io.Writer, b []byte) {
	// 57 raw bytes per 76-byte base64 line.
	const maxRaw = 57
	// Buffer for each line, including trailing CRLF.
	buffer := make([]byte, MaxLineLength+len("\r\n"))
	copy(buffer[MaxLineLength:], "\r\n")
	// Process raw chunks until there's no longer enough to fill a line.
	for len(b) >= maxRaw {
		base64.StdEncoding.Encode(buffer, b[:maxRaw])
		_, _ = w.Write(buffer)
		b = b[maxRaw:]
	}
	// Handle the last chunk of bytes.
	if len(b) > 0 {
		out := buffer[:base64.StdEncoding.EncodedLen(len(b))]
		base64.StdEncoding.Encode(out, b)
		out = append(out, "\r\n"...)
		_, _ = w.Write(out)
	}
}

func (email *Email) msgHeaders() (textproto.MIMEHeader, error) {
	res := make(textproto.MIMEHeader, len(email.Headers)+6)
	if email.Headers != nil {
		for _, h := range []string{"Reply-To", "To", "Cc", "From", "Subject", "Date", "Message-Ids", "MIME-Version"} {
			if v, ok := email.Headers[h]; ok {
				res[h] = v
			}
		}
	}
	if _, ok := res["Reply-To"]; !ok && len(email.ReplyTo) > 0 {
		res.Set("Reply-To", strings.Join(email.ReplyTo, ", "))
	}
	if _, ok := res["To"]; !ok && len(email.To) > 0 {
		res.Set("To", strings.Join(email.To, ", "))
	}
	if _, ok := res["Cc"]; !ok && len(email.Cc) > 0 {
		res.Set("Cc", strings.Join(email.Cc, ", "))
	}
	if _, ok := res["Subject"]; !ok && email.Subject != "" {
		res.Set("Subject", email.Subject)
	}
	if _, ok := res["Message-Ids"]; !ok {
		id, err := generateMessageID()
		if err != nil {
			return nil, err
		}
		res.Set("Message-Ids", id)
	}
	// Date and From are required headers.
	if _, ok := res["From"]; !ok {
		res.Set("From", email.From)
	}
	if _, ok := res["Date"]; !ok {
		res.Set("Date", time.Now().Format(time.RFC1123Z))
	}
	if _, ok := res["MIME-Version"]; !ok {
		res.Set("MIME-Version", "1.0")
	}
	for field, vals := range email.Headers {
		if _, ok := res[field]; !ok {
			res[field] = vals
		}
	}
	return res, nil
}

func (at *Attachment) setDefaultHeaders() {
	contentType := "application/octet-stream"
	if len(at.ContentType) > 0 {
		contentType = at.ContentType
	}
	at.Header.Set("Content-Type", contentType)

	if len(at.Header.Get("Content-Disposition")) == 0 {
		disposition := "attachment"
		if at.HTMLRelated {
			disposition = "inline"
		}
		at.Header.Set("Content-Disposition", fmt.Sprintf("%s;\r\n filename=\"%s\"", disposition, at.Filename))
	}
	if len(at.Header.Get("Content-ID")) == 0 {
		at.Header.Set("Content-ID", fmt.Sprintf("<%s>", at.Filename))
	}
	if len(at.Header.Get("Content-Transfer-Encoding")) == 0 {
		at.Header.Set("Content-Transfer-Encoding", "base64")
	}
}

// headerToBytes renders "header" to "buff". If there are multiple values for a
// field, multiple "Field: value\r\n" lines will be emitted.
func headerToBytes(buff io.Writer, header textproto.MIMEHeader) {
	for field, vals := range header {
		for _, subval := range vals {
			_, _ = io.WriteString(buff, field)
			_, _ = io.WriteString(buff, ": ")
			switch {
			case field == "Content-Type" || field == "Content-Disposition":
				_, _ = buff.Write([]byte(subval))
			case field == "From" || field == "To" || field == "Cc" || field == "Bcc":
				participants := strings.Split(subval, ",")
				for i, v := range participants {
					addr, err := mail.ParseAddress(v)
					if err != nil {
						continue
					}
					participants[i] = addr.String()
				}
				_, _ = buff.Write([]byte(strings.Join(participants, ", ")))
			default:
				_, _ = buff.Write([]byte(mime.QEncoding.Encode("UTF-8", subval)))
			}
			_, _ = io.WriteString(buff, "\r\n")
		}
	}
}

var maxBigInt = big.NewInt(math.MaxInt64)

// generateMessageID generates and returns a string suitable for an RFC 2822
// compliant Message-ID, e.g.:
// <1444789264909237300.3464.1819418242800517193@DESKTOP01>
//
// The following parameters are used to generate a Message-ID:
// - The nanoseconds since Epoch
// - The calling PID
// - A cryptographically random int64
// - The sending hostname
func generateMessageID() (string, error) {
	t := time.Now().UnixNano()
	pid := os.Getpid()
	rint, err := rand.Int(rand.Reader, maxBigInt)
	if err != nil {
		return "", err
	}
	h, err := os.Hostname()
	// If we can't get the hostname, we'll use localhost
	if err != nil {
		h = "localhost.localdomain"
	}
	msgid := fmt.Sprintf("<%d.%d.%d@%s>", t, pid, rint, h)
	return msgid, nil
}
