package email

import (
	"fmt"
	testcommon "fobrain/fobrain/tests/common_test"
	"io"
	"net/smtp"
	"reflect"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/agiledragon/gomonkey/v2"
)

// MyWriteCloser 是一个实现 io.Writer 和 io.Closer 接口的结构体
type MyWriteCloser struct{}

// Write 实现了 io.Writer 接口
func (m *MyWriteCloser) Write(p []byte) (n int, err error) {
	// 在这里处理写入逻辑
	fmt.Printf("Writing: %s\n", p)
	return len(p), nil
}

// Close 实现了 io.Closer 接口
func (m *MyWriteCloser) Close() error {
	// 在这里处理关闭逻辑
	fmt.Println("Closing WriteCloser")
	return nil
}

func TestSend(t *testing.T) {
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()
	mockDb.ExpectQuery("SELECT * FROM `email_config` ORDER BY `email_config`.`id` LIMIT 1").
		WillReturnRows(
			sqlmock.NewRows(
				[]string{
					"id", "address", "port", "encrypt_type", "user_name", "password",
					"category", "created_at", "updated_at"},
			).AddRow(1, "smtp.exmail.qq.com", 465, 2, "<EMAIL>",
				"W4jdTHMQKZy5fiGy", "smtp", "2024-10-10 14:44:11", "2024-10-10 14:44:11"),
		)

	var writer io.WriteCloser = &MyWriteCloser{}
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyMethod(reflect.TypeOf(&smtp.Client{}), "Data", func(client *smtp.Client) (io.WriteCloser, error) {
		return writer, nil
	}).Reset()

	e := NewEmail()
	// 添加收件人
	e.AddTo("<EMAIL>")
	// 添加抄送人
	e.AddCc("<EMAIL>")
	// 添加密送人
	e.AddBcc("<EMAIL>")
	// 设置邮件主题
	e.Subject = "邮件主题"
	// 设置邮件正文
	e.AddText("邮件正文")
	// 设置邮件正文为HTML格式
	e.AddHtml("<h1>邮件正文1</h1>")
	// 添加附件
	//_, err := e.AddAttachFile("test.txt")
	//if err != nil {
	//	fmt.Println("附件添加失败")
	//}

	// 发送邮件
	err := e.Send()
	if err != nil {
		fmt.Println("邮件发送失败: ", err.Error())
	}
}

// TestCommonSend
//
//	@Summary: 测试通用发送邮件
func TestCommonSend(t *testing.T) {
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()
	mockDb.ExpectQuery("SELECT * FROM `email_config` ORDER BY `email_config`.`id` LIMIT 1").
		WillReturnRows(
			sqlmock.NewRows(
				[]string{
					"id", "address", "port", "encrypt_type", "user_name", "password",
					"category", "created_at", "updated_at"},
			).AddRow(1, "smtp.exmail.qq.com", 465, 2, "<EMAIL>",
				"W4jdTHMQKZy5fiGy", "smtp", "2024-10-10 14:44:11", "2024-10-10 14:44:11"),
		)

	var writer io.WriteCloser = &MyWriteCloser{}
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyMethod(reflect.TypeOf(&smtp.Client{}), "Data", func(client *smtp.Client) (io.WriteCloser, error) {
		return writer, nil
	}).Reset()

	// 收件人、抄送、 邮件标题、邮件内容、附件（可选）
	err := NewEmail().CommonSend([]string{"<EMAIL>"}, []string{}, "通用邮件发送服务", "sample content")

	if err != nil {
		fmt.Println("邮件发送失败: ", err.Error())
	}
}
