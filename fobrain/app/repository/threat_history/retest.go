package threat_history

import (
	"context"
	"encoding/json"
	"fobrain/fobrain/logs"
	fbredis "fobrain/initialize/redis"
	"fobrain/models/elastic/poc"
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/user"
	"github.com/spf13/cast"
	"time"

	"github.com/go-redis/redis/v8"
	"go-micro.dev/v4/logger"
)

type FoeyeBatchRetestTaskInfo struct {
	User       *user.User
	History    *OneThreatHistory
	PocObj     map[string]interface{}
	NodeInfo   *data_source.Node
	RetryCount int
}

const FoeyeBatchRetestKey = "fobrain:foeyeBatchRetest"

func StartFoeyeBatchRetestConsumer() {
	go func() {
		for {
			result, err := fbredis.GetRedisClient().BRPop(context.TODO(), time.Second*5, FoeyeBatchRetestKey).Result()
			if err == redis.Nil {
				continue
			}
			if err != nil {
				logs.GetLogger().Errorf("从Redis获取任务失败: %v\n", err)
				continue
			}

			if len(result) < 2 {
				logs.GetLogger().Warn("接收到的消息格式不正确:", result)
				continue
			}

			var task FoeyeBatchRetestTaskInfo
			if err := json.Unmarshal([]byte(result[1]), &task); err != nil {
				logs.GetLogger().Errorf("解析任务数据失败: %v\n", err)
				continue
			}

			// 处理任务
			processTask(&task)
		}
	}()
}

func processTask(task *FoeyeBatchRetestTaskInfo) {
	if task.NodeInfo.Source != "foeye" && task.NodeInfo.Source != "d01" {
		logs.GetLogger().Errorf("FoeyeBatchRetestStart invalid node info:%s\n", task.NodeInfo.Source)
		return
	}

	originalId := task.History.OriginalId
	if originalId == "" {
		return
	}

	node := SetNode(task.NodeInfo)
	_, err := node.Retest([]string{originalId})
	if err != nil {
		logs.GetLogger().Infow("node.Retest", "err", err)

		task.History.Status = poc.PocStatusOfNoRepair
		UpdateThreatStatus(task.History, task.PocObj, nil, nil)

		content := "请求节点复测失败，漏洞状态变为" + poc.PocStatusRelations[task.History.Status]
		CreateHistory(nil, task.User, task.History, nil, cast.ToInt(task.PocObj["statusCode"]), content)
		// foeye核查会很耗时，避免频繁提交
		time.Sleep(15 * time.Minute)
		// 只重试三次
		if task.RetryCount > 4 {
			logs.GetLogger().Errorf("foeye/d01批量复测重试三次失败,pocId:%s,originalId:%s,userId:%d\n", task.History.PocId, originalId, task.User.Id)

			return
		}
		task.RetryCount++
		AddFoeyeRetestTask(task)
		return
	}

	//获取复测的进度和结果
	GetCheckProgressAndResult(node, task.User, task.History, task.PocObj)
}

func AddFoeyeRetestTask(info *FoeyeBatchRetestTaskInfo) error {
	data, err := json.Marshal(info)
	if err != nil {
		logger.Errorf("AddFoeyeRetestTask failed to Marshal data, msg:%+v\n,  err:%v\n", info.PocObj, err)
		return err
	}

	err = fbredis.GetRedisClient().LPush(context.TODO(), FoeyeBatchRetestKey, data).Err()
	if err != nil {
		logger.Errorf("AddFoeyeRetestTask failed to push data to redis, msg:%+v\n,  err:%v\n", info.PocObj, err)
		return err
	}
	return nil
}
