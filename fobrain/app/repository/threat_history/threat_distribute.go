package threat_history

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"fobrain/fobrain/app/services/node/yuntu_retest"
	"fobrain/fobrain/common/localtime"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/spf13/cast"

	"fobrain/fobrain/app/repository/email"
	"fobrain/fobrain/app/repository/threat"
	"fobrain/fobrain/app/services/node"
	"fobrain/fobrain/app/services/node/foeye"
	"fobrain/fobrain/app/services/node/foradar"
	"fobrain/fobrain/app/services/node/qt_cloud"
	"fobrain/fobrain/app/services/node/x_ray"
	poc_service "fobrain/fobrain/app/services/poc"
	sync_x_ray "fobrain/fobrain/app/services/sync/x_ray"
	"fobrain/fobrain/logs"
	"fobrain/initialize/es"
	"fobrain/initialize/mysql"
	"fobrain/initialize/redis"
	"fobrain/models/elastic/poc"
	"fobrain/models/elastic/staff"
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/threat_histories"
	"fobrain/models/mysql/threat_tasks"
	"fobrain/models/mysql/user"
	"fobrain/models/mysql/workbench"
	"fobrain/pkg/cfg"
	"fobrain/pkg/queue"
	"fobrain/pkg/utils"
	"fobrain/webhook"
	"fobrain/webhook/dao"

	"github.com/gin-gonic/gin"
	"github.com/olivere/elastic/v7"
	"github.com/tidwall/gjson"
)

type (
	OneThreatHistory struct {
		PocId       string   `json:"poc_id" form:"poc_id" uri:"poc_id" validate:"omitempty" zh:"漏洞ID"`
		PocIds      []string `json:"poc_ids" form:"poc_ids" uri:"poc_ids" validate:"omitempty" zh:"漏洞ID"`
		ToStaffId   string   `json:"to_staff_id" form:"to_staff_id" uri:"to_staff_id" validate:"omitempty" zh:"漏洞修复负责人ID"`
		ToStaffName string   `json:"to_staff_name" form:"to_staff_name" uri:"to_staff_name" validate:"omitempty" zh:"漏洞修复负责人ID"`
		LimitDate   string   `json:"limit_date" form:"limit_date" uri:"limit_date" validate:"omitempty,datetime=2006-01-02 15:04:05" zh:"修复时间要求"`

		SendNotice    int    `json:"send_notice" form:"send_notice" uri:"send_notice" validate:"omitempty,number" zh:"是否发送邮件"`
		TimeoutNotice int    `json:"timeout_notice" form:"timeout_notice" uri:"timeout_notice" validate:"omitempty,number" zh:"超期是否邮件通知"`
		Descrition    string `json:"descrition" form:"descrition" uri:"descrition" validate:"" zh:"备注"`

		ToCc    string `json:"to_cc" form:"to_cc" uri:"to_cc" validate:"" zh:"抄送邮件"`
		Status  int    `json:"status" form:"status" uri:"status" validate:"required,number" zh:"流转状态"`
		ExecNow int    `json:"exec_now" form:"exec_now" uri:"exec_now" validate:"omitempty,number" zh:"是否立即执行"`

		OriginalId    string `json:"original_id" form:"original_id" uri:"original_id" validate:"" zh:"原始漏洞ID"`
		OperationType string `json:"operation_type" form:"operation_type" uri:"operation_type" validate:"" zh:"操作类型"`

		TimeoutReceiverId []string `json:"timeout_receiver_id" form:"timeout_receiver_id" uri:"timeout_receiver_id"` // 超期邮件通知抄送人ID
		TimeoutFrequency  int      `json:"timeout_frequency" form:"timeout_frequency" uri:"timeout_frequency"`       //超期邮件发送频率（1-每天一次，2-三天一次，3-每周一次，4-每月一次，5-三月一次）
	}

	SomeThreatHistory struct {
		PocIds      []string `json:"poc_ids" form:"poc_ids" uri:"poc_ids" validate:"required" zh:"漏洞ID列表"`
		ToStaffId   string   `json:"to_staff_id" form:"to_staff_id" uri:"to_staff_id" validate:"omitempty" zh:"漏洞修复负责人ID"`
		ToStaffName string   `json:"to_staff_name" form:"to_staff_name" uri:"to_staff_name" validate:"omitempty" zh:"漏洞修复负责人ID"`
		LimitDate   string   `json:"limit_date" form:"limit_date" uri:"limit_date" validate:"omitempty,datetime=2006-01-02 15:04:05" zh:"修复时间要求"`

		SendNotice    int    `json:"send_notice" form:"send_notice" uri:"send_notice" validate:"omitempty,number" zh:"是否发送邮件"`
		TimeoutNotice int    `json:"timeout_notice" form:"timeout_notice" uri:"timeout_notice" validate:"omitempty,number" zh:"超期是否邮件通知"`
		Descrition    string `json:"descrition" form:"descrition" uri:"descrition" validate:"" zh:"备注"`

		ToCc    string `json:"to_cc" form:"to_cc" uri:"to_cc" validate:"" zh:"抄送邮件"`
		Status  int    `json:"status" form:"status" uri:"status" validate:"required,number" zh:"流转状态"`
		ExecNow int    `json:"exec_now" form:"exec_now" uri:"exec_now" validate:"omitempty,number" zh:"是否立即执行"`

		OriginalId    string `json:"original_id" form:"original_id" uri:"original_id" validate:"" zh:"原始漏洞ID"`
		OperationType string `json:"operation_type" form:"operation_type" uri:"operation_type" validate:"" zh:"操作类型"`

		IsRecycleBin    int      `json:"is_recycle_bin" form:"is_recycle_bin" uri:"is_recycle_bin"  zh:"是否查询回收站 1是2否"`
		Keyword         string   `json:"keyword" form:"keyword" uri:"keyword" zh:"模糊搜索"`
		SearchCondition []string `json:"search_condition" form:"search_condition" uri:"search_condition"  zh:"搜索条件"`

		TimeoutReceiverId []string `json:"timeout_receiver_id" form:"timeout_receiver_id" uri:"timeout_receiver_id"` // 超期邮件通知抄送人ID
		TimeoutFrequency  int      `json:"timeout_frequency" form:"timeout_frequency" uri:"timeout_frequency"`       //超期邮件发送频率（1-每天一次，2-三天一次，3-每周一次，4-每月一次，5-三月一次）
		DataRange         int      `json:"data_range" form:"data_range" uri:"data_range"  zh:"数据范围 1未处理 2已处理 3回收站"`
	}

	ManualHistory struct {
		PocIds     []string `json:"poc_ids" form:"poc_ids" uri:"poc_ids" validate:"required" zh:"漏洞ID"`
		Descrition string   `json:"descrition" form:"descrition" uri:"descrition" validate:"" zh:"备注"`
		Passed     int      `json:"passed" form:"passed" uri:"passed" zh:"是否通过"`
		Distribute int      `json:"distribute" form:"distribute" uri:"distribute" zh:"是否重新派发"`
		ToStaffId  string   `json:"to_staff_id" form:"to_staff_id" uri:"to_staff_id" zh:"人员id"`

		Keyword         string   `json:"keyword" form:"keyword" uri:"keyword" zh:"模糊搜索"`
		SearchCondition []string `json:"search_condition" form:"search_condition" uri:"search_condition"  zh:"搜索条件"`
		DataRange       int      `json:"data_range" form:"data_range" uri:"data_range"  zh:"数据范围 1未处理 2已处理 3回收站"`
	}

	ThreatHistoryOneRequest struct {
		Id   string `json:"id" form:"id" uri:"id" validate:"required,min=1" zh:"漏洞ID"`
		Type string `json:"type" form:"type" uri:"type" validate:"omitempty,max=100" zh:"日志类型"`
	}

	PocEmailData struct {
		Name          string   `json:"name"`           // 漏洞名称-【策略】
		Level         int      `json:"level"`          //  漏洞等级 低危：1 中危：2 高危：3 严重：4 未知：5-【策略】
		Ip            string   `json:"ip"`             //  漏洞ip -【唯一】
		BusinessNames []string `json:"business_names"` // 业务系统名称
		Url           string   `json:"url"`            //  漏洞地址 -【唯一】
	}
)

// CreateHistory
// 创建漏洞流转历史记录
func CreateHistory(ctx *gin.Context, user *user.User, param *OneThreatHistory, staffObj *staff.Staff, oldStatus int, operations ...string) error {
	if _, ok := poc.PocStatusRelations[param.Status]; !ok {
		return errors.New("状态不存在")
	}

	theatHistory := threat_histories.ThreatHistory{
		PocId:         param.PocId,
		FromUserId:    user.Id,
		FromUserName:  user.Username,
		LimitDate:     param.LimitDate,
		SendNotice:    SetOtherOne(param.SendNotice),
		TimeoutNotice: SetOtherOne(param.TimeoutNotice),
		Descrition:    param.Descrition,
		ToCc:          param.ToCc,
		NewStatus:     param.Status,
		Status:        oldStatus,

		Category: "operation",
	}

	if len(operations) <= 0 || operations[0] == "upload_file" {
		fileName, err := HandleFile(ctx)
		if err != nil {
			return err
		}
		theatHistory.UploadFile = fileName
	}

	if staffObj != nil {
		theatHistory.ToStaffId = staffObj.Id
		theatHistory.ToStaffName = staffObj.Name
	}

	if param.Status == poc.PocStatusOfReRepairing {
		if param.ExecNow == 1 {
			theatHistory.Operation = "复测立即执行"
		} else {
			theatHistory.Operation = "复测指定时间" + param.LimitDate
		}
	}
	if len(operations) > 0 {
		theatHistory.Operation = strings.Join(operations, ",")
	}

	th := threat_histories.NewThreatHistoryModel()
	err := th.Create(&theatHistory)
	if err != nil {
		return err
	}

	return nil
}

// CreateThreatTask 创建定时任务
func CreateThreatTask(taskType int, user *user.User, param *SomeThreatHistory, staffObj *staff.Staff) error {
	// 创建定时任务
	layout := "2006-01-02 15:04:05"
	if param.LimitDate == "" {
		param.LimitDate = time.Now().Format(layout)
	}
	limitDate, err := time.ParseInLocation(layout, param.LimitDate, time.Local)
	if err != nil {
		return err
	}
	task := threat_tasks.ThreatTask{
		UserId:            user.Id,
		LimitDate:         &limitDate,
		TimeoutNotice:     param.TimeoutNotice,
		PocId:             param.PocIds[0],
		PocIds:            strings.Join(param.PocIds, ","),
		Status:            0,
		TaskType:          taskType,
		Genre:             threat_tasks.GenreOne,
		TimeoutReceiverId: strings.Join(param.TimeoutReceiverId, ","),
		TimeoutFrequency:  param.TimeoutFrequency,
	}
	if staffObj != nil && len(staffObj.Email) > 0 {
		task.ToStaffId = staffObj.Id
		task.ToStaffEmail = staffObj.Email[0]
		task.ToCc = param.ToCc
	}

	tt := threat_tasks.NewThreatTaskModel()
	err = tt.Create(&task)
	if err != nil {
		return err
	}
	return nil
}

// UpdateThreatStatus　更新漏洞流转状态
func UpdateThreatStatus(param *OneThreatHistory, pocObj map[string]interface{}, staffObj *staff.Staff, ccStaff *staff.Staff) error {
	status := cast.ToString(pocObj["statusCode"])
	newStatus := strconv.Itoa(param.Status)
	personFid := cast.ToString(pocObj["person_fid"])
	newPersonFid := ""
	if staffObj != nil {
		newPersonFid = staffObj.FidHash
	}
	if status == newStatus {
		if newPersonFid != "" && newPersonFid == personFid {
			return nil
		}
	}

	PushQueue(staffObj, newStatus, pocObj, ccStaff)

	return nil
}

func PushQueue(staffObj *staff.Staff, newStatus string, pocObj map[string]interface{}, ccStaff *staff.Staff) error {
	client := redis.GetRedisClient()
	rq := &queue.RedisQueue{Client: client}
	if staffObj == nil {
		staffObj = &staff.Staff{}
	}
	if ccStaff == nil {
		ccStaff = &staff.Staff{}
	}
	limitDate := ""
	if pocObj["limit_date"] != nil {
		limitDate = cast.ToString(pocObj["limit_date"])
	}
	obj := []map[string]interface{}{{"id": pocObj["id"], "status": newStatus, "person": staffObj.Fid, "cc_person": ccStaff.Fid, "limit_date": limitDate}}
	count := rq.Push(cfg.LoadQueue().VulnUpdateQueue, obj)

	logs.GetLogger().Infow("PushQueue", "count", count, "obj", obj)
	return nil
}

// SendNotice 发送邮件通知
func SendNotice(ctx *gin.Context, param *SomeThreatHistory, staffObj *staff.Staff) error {
	mail := ""
	if len(staffObj.Email) > 0 {
		mail = staffObj.Email[0]
	}
	pocs, err := poc_service.GetPocsByIds(param.PocIds)
	if err != nil {
		return err
	}
	pocEmailData := []PocEmailData{}
	for _, v := range pocs {
		businessNames := []string{}
		for _, b := range v.Business {
			businessNames = append(businessNames, b.System)
		}
		pocEmailData = append(pocEmailData, PocEmailData{
			Name:          v.Name,
			Level:         v.Level,
			BusinessNames: businessNames,
			Ip:            v.Ip,
			Url:           poc.CheckUrl(v.Url),
		})
	}
	logs.GetLogger().Infow("SendNotice ", "email", mail)
	if param.SendNotice == 1 && mail != "" {
		title, content := GetEmailContent(param.Status, staffObj.Name, len(pocEmailData), pocEmailData)
		cc := []string{}
		if param.ToCc != "" {
			// 抄送
			ccs := strings.Split(param.ToCc, ",")
			ccEmails, err := GetStaffsEmails(ccs)
			if err == nil {
				for _, v := range ccEmails {
					if len(v) > 0 && strings.Contains(v, "@") {
						cc = append(cc, v)
					}
				}
			}
		}
		cc = utils.ListDistinct(cc)
		logs.GetLogger().Infow("SendNotice", "cc", cc)
		//fmt.Println(title, content)
		// 发送邮件
		// 收件人、抄送、 邮件标题、邮件内容、附件（可选）
		err := email.NewEmail().CommonSend([]string{mail}, cc, title, content)

		if err != nil {
			fmt.Println("邮件发送失败: ", err.Error())
			return err
		} else {
			fmt.Println("邮件发送成功")
		}
	}
	return nil
}

func GetEmailContent(status int, name string, num int, pocEmailData []PocEmailData) (title, content string) {
	t := time.Now()
	layout := "2006-01-02 15:04:05"
	timeStr := t.Format(layout)
	n := fmt.Sprintf("%d", num)
	switch status {
	case poc.PocStatusOfBeRepair:
		title = "『漏洞修复提醒』"
		content = "您好，" + name + "<br /><span style=\"color:red\">" + timeStr + "</span> 有" + n + "个漏洞派发给您，请登录系统进行查看"
	case poc.PocStatusOfForward:
		title = "『漏洞修复提醒』"
		content = "您好，" + name + "<br /><span style=\"color:red\">" + timeStr + "</span> 有" + n + "个漏洞转交给您，请登录系统进行查看"
	case poc.PocStatusOfTimeout:
		title = "『漏洞修复超时提醒』"
		content = "您好，" + name + "<br />有" + n + "个漏洞超时未修复，请登录系统进行查看"
	}
	if len(pocEmailData) > 0 {
		// 组装邮件 Html表格数据
		tableHeader := "<table border='1' cellspacing='0' cellpadding='5' style='border-collapse: collapse; margin-top: 15px;'>\n"
		tableHeader += "<tr style='background-color: #f2f2f2;'>\n"
		tableHeader += "<th>序号</th>\n"
		tableHeader += "<th>漏洞名称</th>\n"
		tableHeader += "<th>IP地址</th>\n"
		tableHeader += "<th>漏洞等级</th>\n"
		tableHeader += "<th>业务系统</th>\n"
		tableHeader += "<th>漏洞地址</th>\n"
		tableHeader += "</tr>\n"

		tableBody := ""
		for i, item := range pocEmailData {
			// 获取漏洞等级对应的文本和颜色
			levelText := "未知"
			levelColor := "#808080" // 灰色为默认
			switch item.Level {
			case 1:
				levelText = "低危"
				levelColor = "#00BFFF" // 蓝色
			case 2:
				levelText = "中危"
				levelColor = "#FFD700" // 黄色
			case 3:
				levelText = "高危"
				levelColor = "#FFA500" // 橙色
			case 4:
				levelText = "严重"
				levelColor = "#FF0000" // 红色
			}

			// 将业务系统名称数组转换为字符串，用逗号分隔
			businessNames := strings.Join(item.BusinessNames, ", ")

			tableBody += "<tr>\n"
			tableBody += fmt.Sprintf("<td>%d</td>\n", i+1)
			tableBody += fmt.Sprintf("<td>%s</td>\n", item.Name)
			tableBody += fmt.Sprintf("<td>%s</td>\n", item.Ip)
			tableBody += fmt.Sprintf("<td style='color: %s; font-weight: bold;'>%s</td>\n", levelColor, levelText)
			tableBody += fmt.Sprintf("<td>%s</td>\n", businessNames)
			tableBody += fmt.Sprintf("<td><a href='%s' target='_blank'>%s</a></td>\n", item.Url, item.Url)
			tableBody += "</tr>\n"
		}

		tableFooter := "</table>"

		// 将表格添加到内容中
		content += "<br/><br/>漏洞详情如下：<br/>" + tableHeader + tableBody + tableFooter
	}
	return
}

func GenRiskWebhookMsg(staffObj *staff.Staff, status, num int, msgInfo string) (event string, content string) {
	t := time.Now()
	layout := "2006-01-02 15:04:05"
	timeStr := t.Format(layout)
	switch status {
	case poc.PocStatusOfBeRepair:
		event = dao.EventVulDistribute
		content = "『" + msgInfo + "修复提醒』" + "您好，" + staffObj.Name + " " + timeStr + " 有" + fmt.Sprintf("%d个", num) + msgInfo + "派发给您，请登录系统进行查看"
	case poc.PocStatusOfForward:
		event = dao.EventVulTransfer
		content = "『" + msgInfo + "修复提醒』" + "您好，" + staffObj.Name + " " + timeStr + " 有" + fmt.Sprintf("%d个", num) + msgInfo + "转交给您，请登录系统进行查看"
	case poc.PocStatusOfTimeout:
		event = dao.EventVulTimeout
		content = "『" + msgInfo + "修复超时提醒』" + "您好，" + staffObj.Name + " " + timeStr + " 有" + fmt.Sprintf("%d个", num) + msgInfo + "超时未修复，请登录系统进行查看"
	case poc.PocStatusOfWaitRetest:
		event = dao.EventVulWaitRetest
		content = "『" + msgInfo + "修复完成提醒』" + "您好，" + staffObj.Name + " " + timeStr + " 有" + fmt.Sprintf("%d个", num) + msgInfo + "修复完成，请登录系统进行查看"
	case poc.PocStatusOfErrorReport:
		event = dao.EventVulErrorReport
		content = "『" + msgInfo + "误报提醒』" + "您好，" + staffObj.Name + " " + timeStr + " 有" + fmt.Sprintf("%d个", num) + msgInfo + "误报，请登录系统进行查看"
	case poc.PocStatusOfCantRepaired:
		event = dao.EventVulCantRepaired
		content = "『" + msgInfo + "无法修复提醒』" + "您好，" + staffObj.Name + timeStr + "有" + fmt.Sprintf("%d个", num) + msgInfo + "无法修复，请登录系统进行查看"
	}
	return
}
func SendWebhookMsg(param *OneThreatHistory, staffObj *staff.Staff, user *user.User, pocObj []map[string]interface{}) error {
	event, content := GenRiskWebhookMsg(staffObj, param.Status, len(pocObj), "漏洞")
	if event == "" {
		return nil
	}
	hook := make([]webhook.VulMsgDetails, 0)
	vulnerabilityId := param.PocId
	limitDate := param.LimitDate
	for _, p := range pocObj {
		if id, exists := p["id"]; exists {
			vulnerabilityId = id.(string)
		}
		if lDate, exists := p["limit_date"]; exists {
			switch v := lDate.(type) {
			case string:
				limitDate = v
			case *localtime.Time:
				if v != nil {
					limitDate = v.Format(utils.DateTimeLayout)
				} else {
					limitDate = ""
				}
			}
		}
		hook = append(hook, webhook.VulMsgDetails{
			VulnerabilityId:        vulnerabilityId,
			VulnerabilityName:      p["name"].(string),
			RepairDeadline:         limitDate,
			RepairPriority:         p["repair_priority"].(string),
			ReceiverPersons:        []string{staffObj.Name},
			ReceiverThirdUsernames: []string{staffObj.SsoName},
			ReceiverThirdUserId:    []string{staffObj.SsoId},
			ReceiverUsers: []webhook.ReceiverUser{
				{
					Id:      staffObj.Id,
					SsoId:   staffObj.SsoId,
					Fid:     staffObj.Fid,
					FidHash: staffObj.FidHash,
				},
			},
			DispatchTime: time.Now().Format(utils.DateTimeLayout),
		})
	}
	vulMsg := &webhook.VulnerabilityMsg{
		Event:      event,
		Title:      content,
		OpName:     user.Username,
		BusinessId: param.PocId,
		Details:    hook,
	}
	return vulMsg.SendMsg()
}

// SendLocalNotice 发送本地通知(站内信)
func SendLocalNotice(pocObj map[string]interface{}) error {
	// 发送站内信
	notice := &workbench.NotifyAlarmCenter{
		MsgType:         workbench.MsgTypeAbnormal,
		MsgContent:      pocObj["name"].(string) + "-" + pocObj["person_name"].(string),
		MsgSource:       "vulnerability_urge",
		RelationType:    "",
		RelationContent: "",
		Remark:          "",
	}

	return workbench.NewNotifyAlarmCenter().Create(notice)
}

// SendLocalMsgNotice 发送本地消息通知(站内信)
func SendLocalMsgNotice(adminName string, userId uint64, staffFidHash string, num int, operationName string) error {

	// 发送站内信
	notice := &workbench.NotifyAlarmCenter{
		MsgType:         workbench.MsgTypeRemind,
		MsgContent:      "管理员" + adminName + operationName + "给您" + fmt.Sprintf("%d", num) + "条漏洞",
		MsgSource:       workbench.MsgSouceVulnerabilityRemind,
		RelationType:    "",
		RelationContent: "",
		Remark:          "",
		Read:            false,
		UserId:          userId,
		StaffId:         staffFidHash,
	}

	return workbench.NewNotifyAlarmCenter().Create(notice)
}

// GetStaff 获取漏洞修复负责人信息
func GetStaff(staffId string) (*staff.Staff, error) {
	boolQuery := elastic.NewBoolQuery()
	boolQuery = boolQuery.Must(elastic.NewTermQuery("id", staffId))

	result, err := es.GetEsClient().Search(staff.NewStaff().IndexName()).
		Size(1).
		Query(boolQuery).
		Do(context.TODO())

	if err != nil {
		return nil, err
	}

	staff := new(staff.Staff)
	if result.TotalHits() > 0 {
		_ = json.Unmarshal(result.Hits.Hits[0].Source, staff)
	}

	return staff, nil
}

// GetStaff 获取漏洞修复负责人信息
func GetStaffsFidHash(staffId []string) ([]string, error) {
	boolQuery := elastic.NewBoolQuery()
	boolQuery = boolQuery.Must(elastic.NewTermsQueryFromStrings("id", staffId...))

	result, err := es.GetEsClient().Search(staff.NewStaff().IndexName()).
		Size(10000).
		FetchSource(false).
		DocvalueField("fid_hash").
		Query(boolQuery).
		Do(context.TODO())

	if err != nil {
		return nil, err
	}

	fid_hashs := make([]string, 0)
	if result.TotalHits() > 0 {
		for _, hit := range result.Hits.Hits {
			// 获取字段值
			fid, _ := hit.Fields.Strings("fid_hash")
			fid_hashs = append(fid_hashs, fid...)
		}
	}
	return fid_hashs, nil
}

func GetStaffsEmails(staffId []string) ([]string, error) {
	boolQuery := elastic.NewBoolQuery()
	boolQuery = boolQuery.Must(elastic.NewTermsQueryFromStrings("id", staffId...))

	result, err := es.GetEsClient().Search(staff.NewStaff().IndexName()).
		Size(10000).
		FetchSource(false).
		DocvalueField("email").
		Query(boolQuery).
		Do(context.TODO())

	if err != nil {
		return nil, err
	}

	fid_hashs := make([]string, 0)
	if result.TotalHits() > 0 {
		for _, hit := range result.Hits.Hits {
			// 获取字段值
			fid, _ := hit.Fields.Strings("email")
			fid_hashs = append(fid_hashs, fid...)
		}
	}
	return fid_hashs, nil
}

// HandleFile 文件上传处理
func HandleFile(ctx *gin.Context) (string, error) {
	contentType := ctx.Request.Header.Get("Content-Type")
	if !strings.Contains(contentType, "multipart/form-data") {
		return "", nil
	}
	file, err := ctx.FormFile("file")
	if err != nil && err.Error() != "http: no such file" {
		return "", err
	}
	if file != nil {
		if CheckFileType(file.Filename) == false {
			return "", errors.New(fmt.Errorf("invalid file type: %s", file.Filename).Error())
		}
		batch := time.Now().Format("20060102150405")
		baseDir := "../storage/threat_history/"
		dst := filepath.Join(baseDir, batch+"_"+file.Filename)
		if err := ctx.SaveUploadedFile(file, dst); err != nil {
			return "", err
		}
		return dst, nil
	}
	return "", nil
}

// CheckFileType 检查文件类型
func CheckFileType(fileName string) bool {
	fileType := getFileType(fileName)
	if fileType == "unknown" {
		return false
	}
	return true
}

// getFileType 获取文件类型
func getFileType(fileName string) string {
	// 将文件名转换为小写
	fileName = strings.ToLower(fileName)

	// 根据文件名后缀判断文件类型
	if strings.HasSuffix(fileName, ".doc") {
		return "doc"
	} else if strings.HasSuffix(fileName, ".docx") {
		return "docx"
	} else if strings.HasSuffix(fileName, ".pdf") {
		return "pdf"
	} else if strings.HasSuffix(fileName, ".jpg") || strings.HasSuffix(fileName, ".jpeg") {
		return "jpeg"
	} else if strings.HasSuffix(fileName, ".png") {
		return "png"
	} else {
		return "unknown"
	}
}

// SetOtherOne 设置其他状态为1
func SetOtherOne(status int) int {
	if status == 0 {
		return 0
	}
	return 1
}

// ReTest 复测
func ReTest(ctx *gin.Context, user *user.User, param *OneThreatHistory, pocObj map[string]interface{}) error {
	pocStatus, _ := pocObj["statusCode"].(int)

	// 是否可以复测
	if _, ok := poc.CanReTestStatus[pocStatus]; ok {
		nodeInfo, err := GetNodeInfo(pocObj)
		if err != nil {
			return err
		}
		logs.GetLogger().Infow("ReTest ", "node_type", nodeInfo.Source)
		if nodeInfo.Source != "" {
			if pocObj["original_ids"] == nil {
				return errors.New("没有原始漏洞ID，请手动复测该漏洞")
			}
			originalIds := pocObj["original_ids"].([]string)
			if len(originalIds) <= 0 { // 这种情况应该不会出现，如果出现了，可能是漏洞数据的问题
				return errors.New("没有原始漏洞ID，请手动复测该漏洞")
			}
			if strings.Contains(originalIds[0], "_") {
				param.OriginalId = strings.Split(originalIds[0], "_")[2]
			} else {
				param.OriginalId = originalIds[0]
			}

			node := SetNode(nodeInfo)

			if node != nil {
				pocIds := []string{param.OriginalId}
				if nodeInfo.Source == "x_ray" {
					poc, err := poc_service.GetPocById(param.OriginalId)
					if err != nil {
						return errors.New("获取漏洞数据失败")
					}
					task, err := sync_x_ray.GetTaskDataById(poc.TaskDataIds[0])
					if err != nil {
						return errors.New("获取任务数据失败")
					}
					pocIds = []string{fmt.Sprintf("%d", task.Id)}
					pocObj["node_source"] = "x_ray"
				} else if nodeInfo.Source == "qty" {
					// id_ip_originalId
					id := fmt.Sprintf("%s_%s_%s", pocObj["id"], pocObj["ip"], param.OriginalId)
					pocIds = []string{id}
					pocObj["node_source"] = "qty"
				}
				if nodeInfo.Source == "foeye" || nodeInfo.Source == "d01" {
					AddFoeyeRetestTask(&FoeyeBatchRetestTaskInfo{
						User:     user,
						PocObj:   pocObj,
						History:  param,
						NodeInfo: nodeInfo,
					})
					return nil
				}

				resp, err := node.Retest(pocIds)
				if err != nil {
					logs.GetLogger().Infow("node.Retest", "err", err)

					param.Status = poc.PocStatusOfNoRepair
					UpdateThreatStatus(param, pocObj, nil, nil)

					content := "请求节点复测失败，漏洞状态变为" + poc.PocStatusRelations[param.Status]
					CreateHistory(ctx, user, param, nil, pocObj["statusCode"].(int), content) // 不是api接口调用,不上传文件。

					return errors.New("请求节点复测失败，复测未通过")
				}
				if nodeInfo.Source == "x_ray" {
					param.OriginalId = resp
				}

				//获取复测的进度和结果
				go GetCheckProgressAndResult(node, user, param, pocObj)
			}
		}
	}
	return nil
}

// GetNodeInfo 获取节点信息
func GetNodeInfo(pocObj map[string]interface{}) (*data_source.Node, error) {
	allNodeIds := pocObj["all_node_ids"].([]uint64)
	if len(allNodeIds) > 0 {
		nodeId := allNodeIds[0]
		node, err := data_source.NewNodeModel().First(mysql.WithColumnValue("id", nodeId))
		if err != nil {
			return nil, err
		}
		return node, nil
	}

	return nil, nil
}

// SetNode 设置节点
func SetNode(node *data_source.Node) node.SendRequest {
	switch node.Source {
	case "foeye", "d01":
		n := foeye.NewFoeye()
		n.SetNode(node.Id)
		return n
	case "foradar_saas":
		n := foradar.NewFORadar()
		n.SetNode(node.Id)
		return n
	case "x_ray":
		n := x_ray.New()
		n.SetNode(node.Id)
		return n
	case "qty":
		n := qt_cloud.NewQTCloud()
		n.SetNode(node.Id)
		return n
	case "yuntu":
		n := yuntu_retest.NewYuntuRetest()
		n.SetNode(node.Id)
		return n
	}

	return nil
}

// GetCheckProgressAndResult 获取漏洞检测进度和结果
func GetCheckProgressAndResult(node node.SendRequest, user *user.User, param *OneThreatHistory, pocObj map[string]interface{}) {
	pocIds := []string{param.OriginalId}
	GetProgress(node, pocIds, 600) // 最大重试次数, 最多等待30分钟

	GetResultAndUpdateStatus(node, user, param, pocObj)
}

func GetResultAndUpdateStatus(node node.SendRequest, user *user.User, param *OneThreatHistory, pocObj map[string]interface{}) error {
	pocIds := []string{param.OriginalId}
	result, total, err := node.GetCheckResult(pocIds, pocObj)
	if err != nil {
		logs.GetLogger().Errorf("GetResultAndUpdateStatus GetCheckResult err:", err.Error())
	}
	ctx := &gin.Context{} // 将值类型的gin.Context{}修改为指针类型&gin.Context{}
	if pocObj["node_source"] == "x_ray" {
		logs.GetLogger().Infow("GetResultAndUpdateStatus ", "result", result)
		msg := ""
		res := gjson.Get(result.String(), "data.result")
		if res.String() == "FIXED" {
			msg = "复测通过"
			param.Status = poc.PocStatusOfRepaired
		} else {
			msg = "复测未通过"
			param.Status = poc.PocStatusOfNoRepair
		}
		UpdateThreatStatus(param, pocObj, nil, nil)

		content := msg + "，漏洞状态变为" + poc.PocStatusRelations[param.Status]
		CreateHistory(ctx, user, param, nil, pocObj["statusCode"].(int), content) // 不是api接口调用,不上传文件。
		return nil
	} else if pocObj["node_source"] == "qty" {
		state := result.Raw == "true"
		msg := ""
		if state { // 漏洞修复成功
			msg = "复测通过"
			param.Status = poc.PocStatusOfRepaired
		} else { // 漏洞未修复
			msg = "复测未通过"
			param.Status = poc.PocStatusOfNoRepair
		}
		logs.GetLogger().Infow("GetResultAndUpdateStatus ", "param.Status", param.Status)
		UpdateThreatStatus(param, pocObj, nil, nil)

		content := msg + "，漏洞状态变为" + poc.PocStatusRelations[param.Status]
		CreateHistory(ctx, user, param, nil, pocObj["statusCode"].(int), content) // 不是api接口调用,不上传文件。
		return nil
	}
	logs.GetLogger().Infow("GetResultAndUpdateStatus ", "total", total)
	if total == 0 {
		// 漏洞不存在
		param.Status = poc.PocStatusOfNoRepair
		UpdateThreatStatus(param, pocObj, nil, nil)

		content := "节点漏洞不存在，漏洞状态变为" + poc.PocStatusRelations[param.Status]
		CreateHistory(ctx, user, param, nil, pocObj["statusCode"].(int), content) // 漏洞不存在
		logs.GetLogger().Infow("GetResultAndUpdateStatus ", "info", "节点漏洞不存在")
		return nil
	}
	logs.GetLogger().Infow("GetResultAndUpdateStatus ", "result.Exists()", result.Exists())
	if result.Exists() && total > 0 {
		result.ForEach(func(key, value gjson.Result) bool {
			state := int(gjson.Get(value.String(), "state").Int())
			msg := ""
			if state == 4 { // 漏洞修复成功
				param.Status = poc.PocStatusOfRepaired
				msg = "复测通过"
			} else { // if state == 2 { // 漏洞ip离线
				// 其它状态为未修复
				param.Status = poc.PocStatusOfNoRepair
				msg = "复测未通过"
			}
			logs.GetLogger().Infow("GetResultAndUpdateStatus ", "param.Status", param.Status)
			UpdateThreatStatus(param, pocObj, nil, nil)

			content := msg + "，漏洞状态变为" + poc.PocStatusRelations[param.Status]

			CreateHistory(ctx, user, param, nil, cast.ToInt(pocObj["statusCode"]), content) // 不是api接口调用,不上传文件。
			return true
		})
	}
	return nil
}

func GetProgress(node node.SendRequest, pocIds []string, maxRetry int) error {
	//maxRetry := 600 // 最大重试次数, 最多等待30分钟
	count := 0
	for {
		count += 1
		time.Sleep(3 * time.Second)
		progress, err := node.GetCheckProgress(pocIds)
		logs.GetLogger().Infow("GetProgress ", "progress", progress, "pocIds", pocIds)
		if err != nil {
			logs.GetLogger().Errorf("GetCheckProgress pocIds:%v,err:%s", pocIds, err.Error())
			if count >= maxRetry {
				break
			}
			continue
		}
		if progress >= 100 || count >= maxRetry {
			break
		}
	}
	return nil
}

func CheckStatus(oldStatus, newStatus int) (bool, string) {
	if _, ok := poc.PocStatusRelations[newStatus]; !ok {
		return false, "要更改的状态不存在"
	}

	if newStatus == 0 || newStatus == 1 {
		return false, "不能改新增或复现状态"
	}

	if _, ok := poc.CanNotReturnStatus[oldStatus]; ok {
		return false, "该状态已经不可更改"
	}

	return true, ""
}

func GetOperateContent(username string, status int, msgInfo string, toStaffName ...string) string {
	operationName := threat.GetOperaterName(status)

	// 根据操作类型添加目标人员信息
	if len(toStaffName) > 0 && toStaffName[0] != "" {
		switch status {
		case poc.PocStatusOfBeRepair: // 派发 (状态码 10)
			content := username + operationName + "给" + toStaffName[0] + "," + msgInfo + "状态变为" + poc.PocStatusRelations[status]
			return content
		case poc.PocStatusOfForward: // 转交 (状态码 11)
			content := username + operationName + "给" + toStaffName[0] + "," + msgInfo + "状态变为" + poc.PocStatusRelations[status]
			return content
		case poc.PocOperateOfUrge: // 催促 (状态码 16)
			content := username + operationName + toStaffName[0] + "修复" + msgInfo + ",状态变为" + poc.PocStatusRelations[status]
			return content
		}
	}

	// 默认格式（其他操作或没有目标人员时）
	content := username + operationName + "," + msgInfo + "状态变为" + poc.PocStatusRelations[status]
	return content
}

func GetSourceIcons() (map[string]any, error) {
	var opts []mysql.HandleFunc
	sources, _, err := data_source.NewSourceModel().Items(0, 0, opts...)
	if err != nil {
		return nil, err
	}
	sourceMap := make(map[string]any)
	for _, source := range sources {
		id := strconv.FormatUint(source.Id, 10)
		icon := map[string]string{"icon": source.Icon, "name": source.Name}
		sourceMap[id] = icon
	}
	return sourceMap, nil
}
