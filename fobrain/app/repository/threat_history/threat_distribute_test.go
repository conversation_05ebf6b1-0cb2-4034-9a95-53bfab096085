package threat_history

import (
	"fobrain/webhook/dao"
	"reflect"
	"strings"
	"testing"
	"time"

	"fobrain/fobrain/app/services/node/foeye"
	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/models/elastic/poc"
	"fobrain/models/elastic/staff"
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/user"
	"fobrain/webhook"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/alicebob/miniredis/v2"
	"github.com/gin-gonic/gin"
	redis2 "github.com/go-redis/redis/v8"
	"github.com/olivere/elastic/v7"
	"github.com/stretchr/testify/assert"
	"github.com/tidwall/gjson"
)

func TestCheckFileType(t *testing.T) {
	t.Run("false", func(t *testing.T) {
		fileType := CheckFileType("test.txt")
		assert.False(t, fileType)
	})

	t.Run("true", func(t *testing.T) {
		fileType := CheckFileType("test.docx")
		assert.True(t, fileType)
	})
}

func TestGetFileType(t *testing.T) {
	t.Run("unknown", func(t *testing.T) {
		fileType := getFileType("test.txt")
		assert.Equal(t, fileType, "unknown")
	})

	t.Run("doc", func(t *testing.T) {
		fileType := getFileType("test.doc")
		assert.Equal(t, fileType, "doc")
	})

	t.Run("docx", func(t *testing.T) {
		fileType := getFileType("test.docx")
		assert.Equal(t, fileType, "docx")
	})

	t.Run("pdf", func(t *testing.T) {
		fileType := getFileType("test.pdf")
		assert.Equal(t, fileType, "pdf")
	})

	t.Run("jpeg", func(t *testing.T) {
		fileType := getFileType("test.jpeg")
		assert.Equal(t, fileType, "jpeg")
		fileType = getFileType("test.jpg")
		assert.Equal(t, fileType, "jpeg")
	})

	t.Run("png", func(t *testing.T) {
		fileType := getFileType("test.png")
		assert.Equal(t, fileType, "png")
	})
}

func TestSetOtherOne(t *testing.T) {
	t.Run("0", func(t *testing.T) {
		r := SetOtherOne(0)
		assert.Equal(t, 0, r)
	})

	t.Run("1", func(t *testing.T) {
		r := SetOtherOne(1)
		assert.Equal(t, 1, r)

		r = SetOtherOne(2)
		assert.Equal(t, 1, r)
	})
}

func TestGetEmailContent(t *testing.T) {
	t.Run("Name1", func(t *testing.T) {
		name := "Name1"
		title, content := GetEmailContent(poc.PocStatusOfBeRepair, name, 1, []PocEmailData{})
		assert.Equal(t, "『漏洞修复提醒』", title)
		assert.Contains(t, content, "有1个漏洞派发给您，请登录系统进行查看")
	})

	t.Run("Name2", func(t *testing.T) {
		name := "Name2"
		title, content := GetEmailContent(poc.PocStatusOfForward, name, 1, []PocEmailData{})
		assert.Equal(t, "『漏洞修复提醒』", title)
		assert.Contains(t, content, "有1个漏洞转交给您，请登录系统进行查看")
	})

	t.Run("Name3", func(t *testing.T) {
		name := "Name3"
		title, content := GetEmailContent(poc.PocStatusOfTimeout, name, 1, []PocEmailData{})
		assert.Equal(t, "『漏洞修复超时提醒』", title)
		assert.Equal(t, "您好，"+name+"<br />有1个漏洞超时未修复，请登录系统进行查看", content)
	})
}

func TestGetStaff(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	t.Run("No err", func(t *testing.T) {
		mockServer.Register("staff/_search", []*elastic.SearchHit{
			{
				Id:     "12f7876552424a80b8b18d17a900d239",
				Source: []byte(`{"name":"example", "email":["<EMAIL>"]}`),
			},
		})

		staff, err := GetStaff("12f7876552424a80b8b18d17a900d239")
		assert.NoError(t, err)
		assert.Equal(t, "example", staff.Name)
		assert.Equal(t, "<EMAIL>", staff.Email[0])
	})
}

func TestGetStaffsFidHash(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	t.Run("No err", func(t *testing.T) {
		mockServer.Register("staff/_search", []*elastic.SearchHit{
			{
				Id: "12f7876552424a80b8b18d17a900d239",
				Fields: map[string]interface{}{
					"fid_hash": []string{"7611151ebb354e5790d664b870837ca4"},
				},
			},
		})

		hashArr, err := GetStaffsFidHash([]string{"12f7876552424a80b8b18d17a900d239"})
		assert.NoError(t, err)
		assert.Equal(t, "7611151ebb354e5790d664b870837ca4", hashArr[0])
	})
}

func TestGetStaffsEmails(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	t.Run("No err", func(t *testing.T) {
		mockServer.Register("staff/_search", []*elastic.SearchHit{
			{
				Id: "12f7876552424a80b8b18d17a900d239",
				Fields: map[string]interface{}{
					"email": []string{"<EMAIL>"},
				},
			},
		})

		hashArr, err := GetStaffsEmails([]string{"12f7876552424a80b8b18d17a900d239"})
		assert.NoError(t, err)
		assert.Equal(t, "<EMAIL>", hashArr[0])
	})
}

func TestReTest(t *testing.T) {
	ctx := gin.Context{}
	ctx.Set("user_id", uint64(1))
	ctx.Set("is_super_manage", true)
	user := user.User{}
	param := OneThreatHistory{}
	var ids []uint64
	ids = append(ids, uint64(5))
	pocObj := map[string]interface{}{"statusCode": 17, "all_node_ids": ids}

	mockDb := testcommon.GetMysqlMock()

	t.Run("Get err", func(t *testing.T) {
		mockDb.ExpectQuery("SELECT * FROM `data_nodes` WHERE `id` = ? AND `data_nodes`.`deleted_at` IS NULL ORDER BY `data_nodes`.`id` LIMIT 1").WithArgs(5).
			WillReturnRows(sqlmock.NewRows([]string{"source"}).AddRow("foeye"))
		r := ReTest(&ctx, &user, &param, pocObj)
		assert.Equal(t, "没有原始漏洞ID，请手动复测该漏洞", r.Error())
	})

	t.Run("nil", func(t *testing.T) {
		mockDb.ExpectQuery("SELECT * FROM `data_nodes` WHERE `id` = ? AND `data_nodes`.`deleted_at` IS NULL ORDER BY `data_nodes`.`id` LIMIT 1").WithArgs(5).
			WillReturnRows(sqlmock.NewRows([]string{"source"}).AddRow("abc"))
		pocObj["original_ids"] = []string{"swer"}
		r := ReTest(&ctx, &user, &param, pocObj)
		assert.Nil(t, r)
	})
	defer mockDb.Close()
}

func TestSetNode(t *testing.T) {
	node := data_source.Node{Source: "foeye"}
	node.Id = 1
	t.Run("not nil", func(t *testing.T) {
		time.Sleep(time.Second)
		r := SetNode(&node)
		assert.NotNil(t, r)

		node.Source = "d01"
		time.Sleep(time.Second)
		r = SetNode(&node)
		assert.NotNil(t, r)

		node.Source = "foradar_saas"
		time.Sleep(time.Second)
		r = SetNode(&node)
		assert.NotNil(t, r)
	})

	t.Run("nil", func(t *testing.T) {
		node.Source = "D01"
		time.Sleep(time.Second)
		r := SetNode(&node)
		assert.Nil(t, r)
	})
}

func TestGetResultAndUpdateStatus(t *testing.T) {
	node := data_source.Node{Source: "foeye"}
	node.Id = 1
	foeye := SetNode(&node)
	user := user.User{}
	param := OneThreatHistory{}
	var ids []interface{}
	ids = append(ids, uint64(5))
	pocObj := map[string]interface{}{"statusCode": 1, "all_node_ids": ids}

	// 测试 total 为 0 的情况
	body := gjson.Result{}
	body.Raw = `{"total": 0}`
	p1 := gomonkey.ApplyMethodReturn(foeye, "GetCheckResult", body, 0, nil)
	p2 := gomonkey.ApplyFuncReturn(UpdateThreatStatus, nil)
	p3 := gomonkey.ApplyFuncReturn(CreateHistory, nil)
	r := GetResultAndUpdateStatus(foeye, &user, &param, pocObj)
	assert.Nil(t, r)
	p1.Reset()
	p2.Reset()
	p3.Reset()

	// 测试 total 为 1 的情况
	body = gjson.Result{}
	body.Raw = `{"total": 1, "data": {"info":[{"id": 1, "name": "foeye", "state":4}]}}`
	p1 = gomonkey.ApplyMethodReturn(foeye, "GetCheckResult", body, 1, nil)
	p2 = gomonkey.ApplyFuncReturn(UpdateThreatStatus, nil)
	p3 = gomonkey.ApplyFuncReturn(CreateHistory, nil)
	r = GetResultAndUpdateStatus(foeye, &user, &param, pocObj)
	assert.Nil(t, r)
	p1.Reset()
	p2.Reset()
	p3.Reset()

}

func TestGetProgress(t *testing.T) {
	node := data_source.Node{Source: "foeye"}
	node.Id = 1
	foeye := foeye.NewFoeye()
	foeye.SetNode(node.Id)

	t.Run("err not nil", func(t *testing.T) {
		//foeye.On("GetCheckProgress", []string{"34"}, 1).Return(52, errors.New("err"))
		r := GetProgress(foeye, []string{"34"}, 1)
		assert.Nil(t, r)
	})

	t.Run("err nil", func(t *testing.T) {
		//foeye.On("GetCheckProgress", []string{"34"}, 1).Return(100, nil)
		r := GetProgress(foeye, []string{"34"}, 1)
		assert.Nil(t, r)
	})

}

func TestUpdateThreatStatus(t *testing.T) {
	param := OneThreatHistory{Status: 1}
	var ids []uint64
	ids = append(ids, uint64(5))
	pocObj := map[string]interface{}{"statusCode": 1, "all_node_ids": ids, "person_fid": "aaa"}

	t.Run("err nil", func(t *testing.T) {
		r := UpdateThreatStatus(&param, pocObj, &staff.Staff{
			FidHash: "aaa",
		}, nil)
		assert.Nil(t, r)
	})
}

func TestPushQueue(t *testing.T) {
	s := miniredis.RunT(t)
	defer s.Close()

	client := redis2.NewClient(&redis2.Options{Addr: s.Addr()})
	testcommon.SetRedisClient(client)

	pocObj := map[string]interface{}{"statusCode": 1, "all_node_ids": ""}

	t.Run("err nil", func(t *testing.T) {
		r := PushQueue(&staff.Staff{}, "13", pocObj, nil)
		assert.Nil(t, r)
	})
}
func TestSendWebhookMsg(t *testing.T) {
	// Mock input parameters
	param := &OneThreatHistory{
		Status:    poc.PocStatusOfBeRepair,
		PocId:     "123",
		LimitDate: "2024-11-30",
	}
	staffObj := &staff.Staff{
		Name: "Test Staff",
	}
	userObj := &user.User{
		Username: "TestUser",
	}
	pocObj := map[string]interface{}{
		"name":            "Test Vulnerability",
		"repair_priority": "High",
	}

	// Mock the SendMsg method
	patches := gomonkey.ApplyMethod(reflect.TypeOf(&webhook.VulnerabilityMsg{}), "SendMsg", func(_ *webhook.VulnerabilityMsg) error {
		return nil
	})
	defer patches.Reset()

	// Call SendWebhookMsg and assert no errors for normal case
	err := SendWebhookMsg(param, staffObj, userObj, []map[string]interface{}{pocObj})
	assert.NoError(t, err)

	param.Status = poc.PocStatusOfTimeout
	err = SendWebhookMsg(param, staffObj, userObj, []map[string]interface{}{pocObj})
	assert.NoError(t, err)
}

func TestSendLocalMsgNotice(t *testing.T) {
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()

	mockDb.Mock.ExpectBegin()
	mockDb.ExpectExec("INSERT INTO `notify_alarm_center` (`created_at`,`updated_at`,`msg_type`,`msg_content`,`msg_source`,`relation_type`,`relation_content`,`remark`,`read`,`user_id`,`staff_id`) VALUES (?,?,?,?,?,?,?,?,?,?,?)").
		WillReturnResult(sqlmock.NewResult(1, 1))
	mockDb.Mock.ExpectCommit()

	err := SendLocalMsgNotice("admin", 1, "staff", 1, " operation")
	assert.Nil(t, err)
}

// TestGetOperateContent 测试 GetOperateContent 函数
func TestGetOperateContent(t *testing.T) {
	// 测试用例结构
	tests := []struct {
		name         string
		username     string
		status       int
		toStaffName  []string
		expectedText string
		description  string
	}{
		// 派发操作测试
		{
			name:         "派发操作_有目标人员",
			username:     "张三",
			status:       poc.PocStatusOfBeRepair,
			toStaffName:  []string{"李四"},
			expectedText: "张三派发给李四,漏洞状态变为" + poc.PocStatusRelations[poc.PocStatusOfBeRepair],
			description:  "派发操作应该显示目标人员信息",
		},
		{
			name:         "派发操作_无目标人员",
			username:     "张三",
			status:       poc.PocStatusOfBeRepair,
			toStaffName:  []string{},
			expectedText: "张三派发,漏洞状态变为" + poc.PocStatusRelations[poc.PocStatusOfBeRepair],
			description:  "派发操作无目标人员时使用默认格式",
		},
		{
			name:         "派发操作_空目标人员",
			username:     "张三",
			status:       poc.PocStatusOfBeRepair,
			toStaffName:  []string{""},
			expectedText: "张三派发,漏洞状态变为" + poc.PocStatusRelations[poc.PocStatusOfBeRepair],
			description:  "派发操作目标人员为空字符串时使用默认格式",
		},

		// 转交操作测试
		{
			name:         "转交操作_有目标人员",
			username:     "王五",
			status:       poc.PocStatusOfForward,
			toStaffName:  []string{"赵六"},
			expectedText: "王五转交给赵六,漏洞状态变为" + poc.PocStatusRelations[poc.PocStatusOfForward],
			description:  "转交操作应该显示目标人员信息",
		},
		{
			name:         "转交操作_无目标人员",
			username:     "王五",
			status:       poc.PocStatusOfForward,
			toStaffName:  []string{},
			expectedText: "王五转交,漏洞状态变为" + poc.PocStatusRelations[poc.PocStatusOfForward],
			description:  "转交操作无目标人员时使用默认格式",
		},

		// 催促操作测试
		{
			name:         "催促操作_有目标人员",
			username:     "管理员",
			status:       poc.PocOperateOfUrge,
			toStaffName:  []string{"技术人员"},
			expectedText: "管理员催促技术人员修复漏洞,状态变为" + poc.PocStatusRelations[poc.PocOperateOfUrge],
			description:  "催促操作应该显示催促目标和修复漏洞",
		},
		{
			name:         "催促操作_无目标人员",
			username:     "管理员",
			status:       poc.PocOperateOfUrge,
			toStaffName:  []string{},
			expectedText: "管理员催促,漏洞状态变为" + poc.PocStatusRelations[poc.PocOperateOfUrge],
			description:  "催促操作无目标人员时使用默认格式",
		},

		// 其他操作测试（延时）
		{
			name:         "延时操作_有目标人员但不应该显示",
			username:     "用户A",
			status:       poc.PocStatusOfDelay,
			toStaffName:  []string{"用户B"},
			expectedText: "用户A延时,漏洞状态变为" + poc.PocStatusRelations[poc.PocStatusOfDelay],
			description:  "延时等其他操作即使有目标人员也不显示，使用默认格式",
		},
		{
			name:         "延时操作_无目标人员",
			username:     "用户A",
			status:       poc.PocStatusOfDelay,
			toStaffName:  []string{},
			expectedText: "用户A延时,漏洞状态变为" + poc.PocStatusRelations[poc.PocStatusOfDelay],
			description:  "延时操作使用默认格式",
		},

		// 误报操作测试
		{
			name:         "误报操作",
			username:     "系统",
			status:       poc.PocStatusOfErrorReport,
			toStaffName:  []string{"某人"},
			expectedText: "系统误报,漏洞状态变为" + poc.PocStatusRelations[poc.PocStatusOfErrorReport],
			description:  "误报操作使用默认格式",
		},

		// 边界情况测试
		{
			name:         "空用户名",
			username:     "",
			status:       poc.PocStatusOfBeRepair,
			toStaffName:  []string{"李四"},
			expectedText: "派发给李四,漏洞状态变为" + poc.PocStatusRelations[poc.PocStatusOfBeRepair],
			description:  "用户名为空时仍应该正常工作",
		},
		{
			name:         "特殊字符用户名",
			username:     "张三@admin",
			status:       poc.PocStatusOfForward,
			toStaffName:  []string{"李四#user"},
			expectedText: "张三@admin转交给李四#user,漏洞状态变为" + poc.PocStatusRelations[poc.PocStatusOfForward],
			description:  "用户名包含特殊字符时应该正常工作",
		},
		{
			name:         "多个目标人员_只使用第一个",
			username:     "张三",
			status:       poc.PocStatusOfBeRepair,
			toStaffName:  []string{"李四", "王五", "赵六"},
			expectedText: "张三派发给李四,漏洞状态变为" + poc.PocStatusRelations[poc.PocStatusOfBeRepair],
			description:  "传入多个目标人员时只使用第一个",
		},
	}

	// 执行测试用例
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var result string
			if len(tt.toStaffName) == 0 {
				result = GetOperateContent(tt.username, tt.status, "漏洞")
			} else {
				result = GetOperateContent(tt.username, tt.status, "漏洞", tt.toStaffName...)
			}

			assert.Equal(t, tt.expectedText, result, tt.description)
		})
	}
}

// TestGetOperateContent_StatusTypes 测试不同状态类型的操作名称
func TestGetOperateContent_StatusTypes(t *testing.T) {
	testCases := []struct {
		status      int
		expectedOp  string
		description string
	}{
		{poc.PocStatusOfBeRepair, "派发", "派发状态"},
		{poc.PocStatusOfForward, "转交", "转交状态"},
		{poc.PocOperateOfUrge, "催促", "催促状态"},
		{poc.PocStatusOfDelay, "延时", "延时状态"},
		{poc.PocStatusOfErrorReport, "误报", "误报状态"},
		{poc.PocStatusOfCantRepaired, "无法修复", "无法修复状态"},
		{poc.PocStatusOfWaitRetest, "待复测", "待复测状态"},
	}

	for _, tc := range testCases {
		t.Run(tc.description, func(t *testing.T) {
			result := GetOperateContent("测试用户", tc.status, "漏洞")
			assert.Contains(t, result, tc.expectedOp, "应该包含正确的操作名称")
			assert.Contains(t, result, "测试用户", "应该包含用户名")
			assert.Contains(t, result, "漏洞状态变为", "应该包含状态变更信息")
		})
	}
}

// TestGetOperateContent_Performance 性能测试
func TestGetOperateContent_Performance(t *testing.T) {
	// 简单的性能测试，确保函数执行效率
	for i := 0; i < 1000; i++ {
		result := GetOperateContent("用户", poc.PocStatusOfBeRepair, "漏洞", "目标用户")
		assert.NotEmpty(t, result)
	}
}

// TestGetOperateContent_NilPointer 测试空指针安全性
func TestGetOperateContent_NilPointer(t *testing.T) {
	// 确保函数不会因为nil或空参数而panic
	assert.NotPanics(t, func() {
		GetOperateContent("", 0, "漏洞")
	}, "空参数不应该导致panic")

	assert.NotPanics(t, func() {
		GetOperateContent("用户", poc.PocStatusOfBeRepair, "漏洞", "")
	}, "空目标用户不应该导致panic")
}

// TestGetOperateContent_Integration 集成测试：模拟真实使用场景
func TestGetOperateContent_Integration(t *testing.T) {
	// 模拟真实的威胁处理流程
	scenarios := []struct {
		name        string
		username    string
		status      int
		targetStaff string
		expected    string
	}{
		{
			name:        "安全管理员派发高危漏洞给技术团队",
			username:    "安全管理员",
			status:      poc.PocStatusOfBeRepair,
			targetStaff: "技术团队负责人",
			expected:    "安全管理员派发给技术团队负责人,漏洞状态变为" + poc.PocStatusRelations[poc.PocStatusOfBeRepair],
		},
		{
			name:        "技术主管转交漏洞给专家",
			username:    "技术主管",
			status:      poc.PocStatusOfForward,
			targetStaff: "安全专家",
			expected:    "技术主管转交给安全专家,漏洞状态变为" + poc.PocStatusRelations[poc.PocStatusOfForward],
		},
		{
			name:        "项目经理催促开发人员修复",
			username:    "项目经理",
			status:      poc.PocOperateOfUrge,
			targetStaff: "开发人员",
			expected:    "项目经理催促开发人员修复漏洞,状态变为" + poc.PocStatusRelations[poc.PocOperateOfUrge],
		},
		{
			name:        "系统自动派发",
			username:    "系统",
			status:      poc.PocStatusOfBeRepair,
			targetStaff: "默认负责人",
			expected:    "系统派发给默认负责人,漏洞状态变为" + poc.PocStatusRelations[poc.PocStatusOfBeRepair],
		},
	}

	for _, scenario := range scenarios {
		t.Run(scenario.name, func(t *testing.T) {
			result := GetOperateContent(scenario.username, scenario.status, "漏洞", scenario.targetStaff)
			assert.Equal(t, scenario.expected, result)

			// 验证消息格式的合理性
			assert.Contains(t, result, scenario.username, "消息应包含操作者")
			assert.Contains(t, result, scenario.targetStaff, "消息应包含目标人员")
			assert.Contains(t, result, "状态变为", "消息应包含状态变更信息")
		})
	}
}
func TestGenRiskWebhookMsg(t *testing.T) {
	now := time.Now().Format("2006-01-02 15:04:05")
	staff := &staff.Staff{Name: "测试用户"}
	msg := "漏洞"
	num := 3

	tests := []struct {
		name           string
		status         int
		expectedEvent  string
		expectedSubstr string // 检查 content 中是否包含此子串
	}{
		{
			"BeRepair",
			poc.PocStatusOfBeRepair,
			dao.EventVulDistribute,
			"『漏洞修复提醒』您好，测试用户 " + now + " 有3个漏洞派发给您，请登录系统进行查看",
		},
		{
			"Forward",
			poc.PocStatusOfForward,
			dao.EventVulTransfer,
			"『漏洞修复提醒』您好，测试用户 " + now + " 有3个漏洞转交给您，请登录系统进行查看",
		},
		{
			"Timeout",
			poc.PocStatusOfTimeout,
			dao.EventVulTimeout,
			"『漏洞修复超时提醒』您好，测试用户 " + now + " 有3个漏洞超时未修复，请登录系统进行查看",
		},
		{
			"WaitRetest",
			poc.PocStatusOfWaitRetest,
			dao.EventVulWaitRetest,
			"『漏洞修复完成提醒』您好，测试用户 " + now + " 有3个漏洞修复完成，请登录系统进行查看",
		},
		{
			"ErrorReport",
			poc.PocStatusOfErrorReport,
			dao.EventVulErrorReport,
			"『漏洞误报提醒』您好，测试用户 " + now + " 有3个漏洞误报，请登录系统进行查看",
		},
		{
			"CantRepaired",
			poc.PocStatusOfCantRepaired,
			dao.EventVulCantRepaired,
			"『漏洞无法修复提醒』您好，测试用户" + now + "有3个漏洞无法修复，请登录系统进行查看",
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			event, content := GenRiskWebhookMsg(staff, tc.status, num, msg)

			if event != tc.expectedEvent {
				t.Errorf("expected event %q, got %q", tc.expectedEvent, event)
			}
			if !strings.Contains(content, tc.expectedSubstr) {
				t.Errorf("expected content to contain %q, got %q", tc.expectedSubstr, content)
			}
		})
	}
}
