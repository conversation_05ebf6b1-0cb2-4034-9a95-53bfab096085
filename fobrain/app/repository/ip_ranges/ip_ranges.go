package ip_ranges

import (
	"context"
	"errors"
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"time"

	ipRangesReq "fobrain/fobrain/app/request/ip_ranges"
	"fobrain/initialize/mysql"
	ipRangesSql "fobrain/models/mysql/ip_ranges"
	"fobrain/models/mysql/network_areas"
	"fobrain/pkg/utils"
	ipranges_service "fobrain/services/ip_ranges"
)

func Create(params *ipRangesReq.IpRangesCreate) (err error) {
	if len(params.AddressRemark) > 0 && !regexp.MustCompile(`^[\p{Han}a-zA-Z0-9]+$`).MatchString(params.AddressRemark) {
		return errors.New("地址规划用途只支持中文、字母和数字")
	}
	failedList := make([]string, 0)
	// 去重
	params.IpRange = utils.ListDistinctNonZero(params.IpRange)
	// 拆分ip地址段
	for _, ipRange := range params.IpRange {
		// 验证IP地址段格式
		ipRangeValid, err := CheckIpRange(ipRange)
		if err != nil {
			return err
		}
		if !ipRangeValid {
			failedList = append(failedList, fmt.Sprintf("%s,IP地址段格式错误", ipRange))
			continue
		}
		// 计算IP规划容量
		ipRangeCapacity, err := calculateCapacity(ipRange)
		if err != nil {
			failedList = append(failedList, fmt.Sprintf("%s,计算IP规划容量错误", ipRange))
			continue
		}
		handlers := make([]mysql.HandleFunc, 0)
		handlers = append(handlers, mysql.WithWhere("network_areas_id = ?", params.NetworkAreasId))
		handlers = append(handlers, mysql.WithWhere("ip_range = ?", ipRange))
		total, err := ipRangesSql.NewIpRangesModel().Count(handlers...)
		if err != nil {
			failedList = append(failedList, fmt.Sprintf("%s,检查是否冲突错误", ipRange))
			continue
		}
		if total > 0 {
			failedList = append(failedList, fmt.Sprintf("%s,相同区域内IP段冲突", ipRange))
			continue
		}

		ipCreate := ipRangesSql.IpRanges{
			NetworkAreasId:  params.NetworkAreasId,
			IpRange:         ipRange,
			IpRangeStatus:   params.IpRangeStatus,
			AddressRemark:   params.AddressRemark,
			IpRangeCapacity: ipRangeCapacity,
		}
		err = ipRangesSql.NewIpRangesModel().CreateItem(&ipCreate)
		if err != nil {
			failedList = append(failedList, fmt.Sprintf("%s,数据保存失败", ipRange))
			continue
		}
	}
	if len(failedList) > 0 {
		return fmt.Errorf("共处理%d条(去重后)数据，%d条数据未能成功保存，详细信息：%s", len(params.IpRange), len(failedList), strings.Join(failedList, ";"))
	}
	return nil
}

func Update(params *ipRangesReq.IpRangesUpdate) (err error) {
	if len(params.AddressRemark) > 0 && !regexp.MustCompile(`^[\p{Han}a-zA-Z0-9]+$`).MatchString(params.AddressRemark) {
		return errors.New("地址规划用途只支持中文、字母和数字")
	}
	ipUpdate := ipRangesSql.IpRanges{
		BaseModel: mysql.BaseModel{
			Id: params.Id,
		},
		IpRangeStatus: params.IpRangeStatus,
		AddressRemark: params.AddressRemark,
	}
	err = ipRangesSql.NewIpRangesModel().Update(&ipUpdate)
	return err
}

func List(params *ipRangesReq.IpRangesRequest) (List []*ipRangesReq.ListResponse, total int64, err error) {
	list := make([]*ipRangesReq.ListResponse, 0)
	ids := utils.ListDistinctNonZero(params.IpRangeIds)
	res, total, err := ipRangesSql.NewIpRangesModel().IpRangesList(params.Page, params.PerPage, params.Keyword, ids)
	if err != nil {
		return list, total, err
	}

	ipRangeIds := make([]uint64, 0)
	for _, ipRangeId := range res {
		ipRangeIds = append(ipRangeIds, ipRangeId.Id)
	}

	for _, ipRange := range res {
		list = append(list, &ipRangesReq.ListResponse{
			IpRanges: ipRangesSql.IpRanges{
				BaseModel: mysql.BaseModel{
					Id:        ipRange.Id,
					CreatedAt: ipRange.CreatedAt,
					UpdatedAt: ipRange.UpdatedAt,
				},
				NetworkAreasId:  ipRange.NetworkAreasId,
				IpRange:         ipRange.IpRange,
				IpNumber:        ipRange.IpNumber,
				IpRangeCapacity: ipRange.IpRangeCapacity,
				IpUsageRate:     ipRange.IpUsageRate,
				IpRangeStatus:   ipRange.IpRangeStatus,
				AddressRemark:   ipRange.AddressRemark,
			},
			NetworkAreasName: ipRange.NetworkAreasName,
		})
	}
	return list, total, nil
}

func ManualUpdate(ctx context.Context) error {
	return ipranges_service.RefreshIpUsageRate()
}

func Export(params *ipRangesReq.ExportRequest) (string, error) {
	ids := utils.ListDistinctNonZero(params.IpRangeIds)
	res, _, err := ipRangesSql.NewIpRangesModel().IpRangesList(0, 0, params.Keyword, ids)
	if err != nil {
		return "", err
	}
	var datum = make([][]interface{}, 0)
	for _, ipRange := range res {
		ipRangeStatusName := ""
		switch ipRange.IpRangeStatus {
		case ipRangesSql.IpRangeStatusDisable:
			ipRangeStatusName = "禁用"
		case ipRangesSql.IpRangeStatusEnable:
			ipRangeStatusName = "在用"
		default:
			ipRangeStatusName = "未知"
		}
		ipUsageRateStr := func() string {
			if ipRange.IpUsageRate == 0 {
				return "0%"
			}
			return fmt.Sprintf("%d%%", ipRange.IpUsageRate/100)
		}()
		d := []interface{}{
			ipRange.NetworkAreasName,
			ipRange.IpRange,
			ipRange.IpRangeCapacity,
			ipRange.IpNumber,
			ipUsageRateStr,
			ipRangeStatusName,
			ipRange.AddressRemark,
			ipRange.CreatedAt.String(),
			ipRange.UpdatedAt.String(),
		}
		datum = append(datum, d)
	}
	filePath := time.Now().Format("20060102150405") + "ip段管理列表.xlsx"
	utils.WriterExcel(filePath,
		[]string{
			"网络区域",
			"ip段地址",
			"ip规划容量",
			"管理ip数量",
			"ip使用率",
			"网段状态",
			"地址规划用途",
			"创建时间",
			"更新时间",
		},
		datum,
	)
	return filePath, nil
}

func IpRangesImport(rows [][]string) ([]string, error) {
	areaList, err := netWorkAreaList() // 网络区域
	if err != nil {
		return nil, err
	}
	ipRanges, err := ipRangesList() // ip段
	if err != nil {
		return nil, err
	}
	rows = rows[4:]
	errorMsg := make([]string, 0)
	createIpRanges := make([]*ipRangesSql.IpRanges, 0)
	rowLine := 0
	for _, row := range rows {
		rowLine++
		if len(row) < 2 {
			errorMsg = append(errorMsg, fmt.Sprintf("第%d行数据格式错误", rowLine))
			continue
		}
		netWorkAreaName := row[0]
		if len(row[0]) < 1 {
			netWorkAreaName = "默认"
		}
		netWorkAreaId, exists := areaList[netWorkAreaName]
		if !exists {
			errorMsg = append(errorMsg, fmt.Sprintf("第%d行数据网络区域[%s]不存在", rowLine, row[0]))
			continue
		}

		ipRange := row[1]
		if len(ipRange) < 1 {
			errorMsg = append(errorMsg, fmt.Sprintf("第%d行数据Ip段地址为空", rowLine))
			continue
		}
		ipRangeValid, err := CheckIpRange(ipRange)
		if err != nil {
			errorMsg = append(errorMsg, fmt.Sprintf("第%d行数据Ip段地址[%s]错误", rowLine, ipRange))
			continue
		}
		if !ipRangeValid {
			errorMsg = append(errorMsg, fmt.Sprintf("第%d行数据Ip段地址[%s]错误", rowLine, ipRange))
			continue
		}

		remark := ""
		if len(row) > 2 {
			remark = row[2]
		}
		if len(remark) > 0 && !regexp.MustCompile(`^[\p{Han}a-zA-Z0-9]+$`).MatchString(remark) {
			errorMsg = append(errorMsg, fmt.Sprintf("第%d行地址规划用途只支持中文、字母和数字", rowLine))
			continue
		}
		// 计算IP规划容量
		ipRangeCapacity, err := calculateCapacity(ipRange)
		if err != nil {
			errorMsg = append(errorMsg, fmt.Sprintf("第%d行数据IP地址段格式错误", rowLine))
			continue
		}

		ipRangeId, exists := ipRanges[row[1]+"_"+strconv.FormatUint(netWorkAreaId, 10)]
		if !exists {
			createIpRanges = append(createIpRanges, &ipRangesSql.IpRanges{
				NetworkAreasId:  netWorkAreaId,
				IpRange:         row[1],
				IpRangeStatus:   2,
				AddressRemark:   remark,
				IpRangeCapacity: ipRangeCapacity,
				IpNumber:        0,
				IpUsageRate:     0,
			})
		}
		// 更新
		if ipRangeId > 0 {
			ipUpdate := ipRangesSql.IpRanges{
				BaseModel: mysql.BaseModel{
					Id: ipRangeId,
				},
				AddressRemark: remark,
			}
			err = ipRangesSql.NewIpRangesModel().Update(&ipUpdate)
			if err != nil {
				errorMsg = append(errorMsg, fmt.Sprintf("第%d行更新错误", rowLine))
				continue
			}
		}
	}
	if len(createIpRanges) > 0 {
		err = ipRangesSql.NewIpRangesModel().BatchCreate(createIpRanges)
		if err != nil {
			errorMsg = append(errorMsg, "数据插入错误")
			return errorMsg, err
		}
	}
	return errorMsg, nil
}

func netWorkAreaList() (map[string]uint64, error) {
	data := map[string]uint64{}
	handlers := make([]mysql.HandleFunc, 0)
	list, _, err := network_areas.NewNetworkAreaModel().List(0, 0, handlers...)
	if err != nil {
		return nil, err
	}
	for _, area := range list {
		data[area.Name] = area.Id
	}
	return data, nil
}

func ipRangesList() (map[string]uint64, error) {
	data := map[string]uint64{}
	handlers := make([]mysql.HandleFunc, 0)
	ipRanges, _, err := ipRangesSql.NewIpRangesModel().List(0, 0, handlers...)
	if err != nil {
		return nil, err
	}
	for _, ipRange := range ipRanges {
		data[ipRange.IpRange+"_"+strconv.FormatUint(ipRange.NetworkAreasId, 10)] = ipRange.Id
	}

	return data, nil
}
