package ip_ranges

import (
	"errors"
	"net"
	"strconv"
	"strings"
)

// calculateCapacity 根据传入的IP段参数，计算IP段的ip容量
func calculateCapacity(ipRange string) (uint64, error) {
	if strings.Contains(ipRange, "/") {
		return calculateCapacityForCidr(ipRange)
	}
	return calculateCapacityForIpRange(ipRange)
}

// calculateCapacityForCidr 根据传入的IP段参数，计算IP段的ip容量
// 只支持CIDR格式
func calculateCapacityForCidr(ipRange string) (uint64, error) {
	_, ipNet, err := net.ParseCIDR(ipRange)
	if err != nil {
		return 0, err
	}
	ones, bits := ipNet.Mask.Size()
	ipRangeCapacity := uint64(1 << (bits - ones))
	return ipRangeCapacity, nil
}

// calculateCapacityForIpRange 根据传入的IP段参数，计算IP段的ip容量
// 只支持IP段格式
func calculateCapacityForIpRange(ipRangeStr string) (uint64, error) {
	ipRange := strings.Split(ipRangeStr, "-")
	if len(ipRange) != 2 {
		return 0, errors.New("IP地址段格式错误")
	}
	ip := net.ParseIP(ipRange[0])
	ipStartInt, err := strconv.Atoi(strings.Split(ip.String(), ".")[3])
	if err != nil {
		return 0, errors.New("IP地址段格式错误")
	}
	ipEndInt, err := strconv.Atoi(ipRange[1])
	if err != nil {
		return 0, errors.New("IP地址段格式错误")
	}
	ipRangeCapacity := uint64(ipEndInt - ipStartInt + 1)
	return ipRangeCapacity, nil
}

// checkIpRange 验证IP地址段格式
// 只支持CIDR格式和IP段格式
func CheckIpRange(ipRange string) (bool, error) {
	if strings.Contains(ipRange, "/") {
		return checkIpRangeForCidr(ipRange)
	}
	return checkIpRangeForIpRange(ipRange)
}

// checkIpRangeForCidr 验证CIDR格式
func checkIpRangeForCidr(ipRange string) (bool, error) {
	_, _, err := net.ParseCIDR(ipRange)
	if err != nil {
		return false, err
	}
	return true, nil
}

// checkIpRangeForIpRange 验证IP段格式
func checkIpRangeForIpRange(ipRangeStr string) (bool, error) {
	// 验证IP地址段格式
	ipRange := strings.Split(ipRangeStr, "-")
	if len(ipRange) != 2 {
		return false, errors.New("IP地址段格式错误,格式必须为ip-范围(0-255)")
	}
	// 验证ipRange[0]是否为IP地址
	ip := net.ParseIP(ipRange[0])
	if ip == nil {
		return false, errors.New("IP地址段格式错误,-前必须为IP地址")
	}
	// 验证ipRange[1]是否为0-255之间的数字
	// 首先把ipRange[1]转换为数字
	ipEnd, err := strconv.Atoi(ipRange[1])
	if err != nil {
		return false, errors.New("IP地址段格式错误,范围必须为0-255之间的数字")
	}
	if ipEnd < 0 || ipEnd > 255 {
		return false, errors.New("IP地址段格式错误,范围必须为0-255之间的数字")
	}
	// 获取ipRange[0]的最后一位开始位置
	ipStart := strings.Split(ip.String(), ".")[3]
	ipStartInt, _ := strconv.Atoi(ipStart)
	// 比较ipStart和ipEnd
	if ipStartInt > ipEnd {
		return false, errors.New("IP地址段格式错误,范围必须大于ip开始的数字")
	}
	return true, nil
}
