package ip_ranges

import (
	"testing"
)

func TestCheckIpRangeForIpRange(t *testing.T) {
	tests := []struct {
		name    string
		ipRange string
		want    bool
		err     bool
	}{
		{
			name:    "自定义正确的IP段",
			ipRange: "***********-10",
			want:    true,
			err:     false,
		},
		{
			name:    "自定义错误的IP段-范围格式错误",
			ipRange: "***********-***********0",
			want:    false,
			err:     true,
		},
		{
			name:    "自定义错误的IP段-范围错误",
			ipRange: "***********-888",
			want:    false,
			err:     true,
		},
		{
			name:    "自定义错误的IP段-范围错误",
			ipRange: "***********0-2",
			want:    false,
			err:     true,
		},
		{
			name:    "自定义错误的IP段-格式错误",
			ipRange: "***********0-2-1",
			want:    false,
			err:     true,
		},
		{
			name:    "自定义错误的IP段-ip地址错误",
			ipRange: "192.168.x.1-10",
			want:    false,
			err:     true,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			got, err := checkIpRangeForIpRange(test.ipRange)
			if err != nil && !test.err {
				t.Errorf("checkIpRange(%q) = %v; want %v", test.ipRange, err, test.err)
			}
			if got != test.want {
				t.Errorf("checkIpRange(%q) = %v; want %v", test.ipRange, got, test.want)
			}
		})
	}
}

func TestCheckIpRangeForCidr(t *testing.T) {
	tests := []struct {
		name    string
		ipRange string
		want    bool
		err     bool
	}{
		{
			name:    "自定义正确的CIDR",
			ipRange: "***********/24",
			want:    true,
			err:     false,
		},
		{
			name:    "自定义错误的CIDR-格式错误",
			ipRange: "***********/24/24",
			want:    false,
			err:     true,
		},
		{
			name:    "自定义错误的CIDR-格式错误",
			ipRange: "***********/24/24",
			want:    false,
			err:     true,
		},
		{
			name:    "自定义错误的CIDR-格式错误",
			ipRange: "***********/24/24",
			want:    false,
			err:     true,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			got, err := checkIpRangeForCidr(test.ipRange)
			if err != nil && !test.err {
				t.Errorf("checkIpRange(%q) = %v; want %v", test.ipRange, err, test.err)
			}
			if got != test.want {
				t.Errorf("checkIpRange(%q) = %v; want %v", test.ipRange, got, test.want)
			}
		})
	}
}

func TestCheckIpRange(t *testing.T) {
	tests := []struct {
		name    string
		ipRange string
		want    bool
		err     bool
	}{
		{
			name:    "自定义正确的IP段",
			ipRange: "***********-10",
			want:    true,
			err:     false,
		},
		{
			name:    "自定义错误的IP段",
			ipRange: "***********0-2-1",
			want:    false,
			err:     true,
		},
		{
			name:    "正确的CIDR",
			ipRange: "***********/24",
			want:    true,
			err:     false,
		},
		{
			name:    "错误的CIDR",
			ipRange: "***********/24/24",
			want:    false,
			err:     true,
		},
		{
			name:    "错误的CIDR",
			ipRange: "***********/24/24",
			want:    false,
			err:     true,
		},
		{
			name:    "错误的字符串",
			ipRange: "错误的字符串",
			want:    false,
			err:     true,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			got, err := CheckIpRange(test.ipRange)
			if err != nil && !test.err {
				t.Errorf("checkIpRange(%q) = %v; want %v", test.ipRange, err, test.err)
			}
			if got != test.want {
				t.Errorf("checkIpRange(%q) = %v; want %v", test.ipRange, got, test.want)
			}
		})
	}
}

func TestCalculateCapacityForIpRange(t *testing.T) {
	tests := []struct {
		name    string
		ipRange string
		want    uint64
		err     bool
	}{
		{
			name:    "自定义正确的IP段",
			ipRange: "***********-10",
			want:    10,
			err:     false,
		},
		{
			name:    "自定义错误的IP段",
			ipRange: "***********0-2-1",
			want:    0,
			err:     true,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			got, err := calculateCapacityForIpRange(test.ipRange)
			if err != nil && !test.err {
				t.Errorf("calculateCapacityForIpRange(%q) = %v; want %v", test.ipRange, err, test.err)
			}
			if got != test.want {
				t.Errorf("calculateCapacityForIpRange(%q) = %v; want %v", test.ipRange, got, test.want)
			}
		})
	}
}

func TestCalculateCapacityForCidr(t *testing.T) {
	tests := []struct {
		name    string
		ipRange string
		want    uint64
		err     bool
	}{
		{
			name:    "正确的CIDR",
			ipRange: "***********/24",
			want:    256,
			err:     false,
		},
		{
			name:    "错误的CIDR",
			ipRange: "***********/24/24",
			want:    0,
			err:     true,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			got, err := calculateCapacityForCidr(test.ipRange)
			if err != nil && !test.err {
				t.Errorf("calculateCapacityForCidr(%q) = %v; want %v", test.ipRange, err, test.err)
			}
			if got != test.want {
				t.Errorf("calculateCapacityForCidr(%q) = %v; want %v", test.ipRange, got, test.want)
			}
		})
	}
}
