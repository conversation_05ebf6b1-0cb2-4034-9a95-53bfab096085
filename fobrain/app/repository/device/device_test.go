package device_test

import (
	"errors"
	"testing"

	device_service "fobrain/fobrain/app/repository/device"
	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/initialize/es"
	"fobrain/initialize/mysql"
	"fobrain/models/elastic/assets"
	"fobrain/models/elastic/device"
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/network_areas"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/olivere/elastic/v7"
	. "github.com/smartystreets/goconvey/convey"
	"github.com/stretchr/testify/assert"
)

func TestDeviceList(t *testing.T) {
	assert := assert.New(t)

	// Mock es.GetCount
	getCountMock := gomonkey.ApplyFunc(es.GetCount, func(indexName string, query elastic.Query) (int64, error) {
		return 1, nil
	})
	defer getCountMock.Reset()

	// Mock es.List
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	mockServer.Register("device/_search", []*elastic.SearchHit{
		{
			Id:     "1",
			Source: []byte(`{"id": "1", "host_name": ["hostname1"], "sn": ["sn1"], "mac": ["mac1"], "ip": ["ip1"], "private_ip": ["privateIp1"], "public_ip": ["publicIp1"], "machine_room": ["machineRoom1"], "os": ["os1"], "created_at": "2021-11-11 11:11:11", "updated_at": "2021-11-11 11:11:11", "source_ids": [1], "area": [1]}`),
		},
	})

	// Mock data_source.NewSourceModel().SourceNames
	datasource := data_source.NewSourceModel()
	sourceNamesMock := gomonkey.ApplyMethodReturn(datasource, "SourceNames", []*data_source.Source{{
		BaseModel: mysql.BaseModel{Id: uint64(1)},
		Name:      "source1",
		Icon:      "icon1",
	}},
	)
	defer sourceNamesMock.Reset()

	// Mock network_areas.NewNetworkAreaModel().GetByIds
	networkares := network_areas.NewNetworkAreaModel()
	getAreaByIdsMock := gomonkey.ApplyMethodReturn(networkares, "GetByIds", []*network_areas.NetworkArea{{
		BaseModel: mysql.BaseModel{Id: uint64(1)},
		Name:      "area1",
	}}, nil)
	defer getAreaByIdsMock.Reset()
	Convey("happy path", t, func() {
		data, count, err := device_service.List(nil, 1, 10, false, "", true, nil, nil, "", "")
		assert.Nil(err)
		assert.Equal(int64(1), count)
		assert.Len(data, 1)
		assert.Equal("1", data[0].Id)
	})

	Convey("no devices found", t, func() {
		// Edge case: No devices found
		getCountMock.Reset()
		getCountMock = gomonkey.ApplyFunc(es.GetCount, func(indexName string, query elastic.Query) (int64, error) {
			return 0, nil
		})
		data, count, err := device_service.List(nil, 10, 10, false, "", false, nil, nil, "", "")
		assert.Nil(err)
		assert.Equal(int64(0), count)
		assert.Len(data, 0)
	})
}

func TestDeviceList_GetCountError(t *testing.T) {
	assert := assert.New(t)
	// Mock es.GetCount
	getCountMock := gomonkey.ApplyFunc(es.GetCount, func(indexName string, query elastic.Query) (int64, error) {
		return 1, nil
	})
	defer getCountMock.Reset()

	// Mock es.List
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	mockServer.Register("device/_search", []*elastic.SearchHit{
		{
			Id:     "1",
			Source: []byte(`{"id": "1", "host_name": ["hostname1"], "sn": ["sn1"], "mac": ["mac1"], "ip": ["ip1"], "private_ip": ["privateIp1"], "public_ip": ["publicIp1"], "machine_room": ["machineRoom1"], "os": ["os1"], "created_at": "2021-11-11 11:11:11", "updated_at": "2021-11-11 11:11:11", "source_ids": [1], "area": [1]}`),
		},
	})

	// Mock data_source.NewSourceModel().SourceNames
	datasource := data_source.NewSourceModel()
	sourceNamesMock := gomonkey.ApplyMethodReturn(datasource, "SourceNames", []*data_source.Source{{
		BaseModel: mysql.BaseModel{Id: uint64(1)},
		Name:      "source1",
		Icon:      "icon1",
	}},
	)
	defer sourceNamesMock.Reset()

	// Mock network_areas.NewNetworkAreaModel().GetByIds
	networkares := network_areas.NewNetworkAreaModel()
	getAreaByIdsMock := gomonkey.ApplyMethodReturn(networkares, "GetByIds", []*network_areas.NetworkArea{{
		BaseModel: mysql.BaseModel{Id: uint64(1)},
		Name:      "area1",
	}}, nil)
	defer getAreaByIdsMock.Reset()

	// Edge case: Error in es.GetCount
	getCountMock.Reset()
	getCountMock = gomonkey.ApplyFunc(es.GetCount, func(indexName string, query elastic.Query) (int64, error) {
		return 0, errors.New("es error")
	})
	defer getCountMock.Reset()

	data, count, err := device_service.List(nil, 10, 10, false, "", false, nil, nil, "", "")
	assert.NotNil(err)
	assert.Equal("es error", err.Error())
	assert.Equal(int64(0), count)
	assert.Len(data, 0)
}

func TestAssetDepartmentToPb(t *testing.T) {
	t.Parallel()
	t.Run("happy path", func(t *testing.T) {
		assetDepartment := &assets.DepartmentBase{
			BusinessSystemId:   "businessSystemId1",
			BusinessSystemName: "businessSystemName1",
			UserId:             "userId1",
			UserName:           "userName1",
			Name:               "department1",
			Parents: []*assets.DepartmentBase{
				{
					BusinessSystemId:   "businessSystemId2",
					BusinessSystemName: "businessSystemName2",
					UserId:             "userId2",
					UserName:           "userName2",
					Name:               "parent1",
				},
			},
		}
		department := device_service.DepartmentToPb(assetDepartment)
		assert := assert.New(t)
		assert.Equal("department1", department.Name)
		assert.Equal("businessSystemId1", department.BusinessSystemId)
		assert.Equal("businessSystemName1", department.BusinessSystemName)
		assert.Equal("userId1", department.UserId)
		assert.Equal("userName1", department.UserName)
		assert.Equal("parent1", department.Parents[0].Name)
		assert.Equal("businessSystemId2", department.Parents[0].BusinessSystemId)
		assert.Equal("businessSystemName2", department.Parents[0].BusinessSystemName)
		assert.Equal("userId2", department.Parents[0].UserId)
		assert.Equal("userName2", department.Parents[0].UserName)
	})

	t.Run("nil parents", func(t *testing.T) {
		assetDepartment := &assets.DepartmentBase{
			Name:     "department1",
			UserId:   "userId1",
			UserName: "userName1",
			Parents:  []*assets.DepartmentBase{},
		}
		department := device_service.DepartmentToPb(assetDepartment)
		assert := assert.New(t)
		assert.Equal("department1", department.Name)
		assert.Len(department.Parents, 0)
	})

	t.Run("nil department", func(t *testing.T) {
		department := device_service.DepartmentToPb(nil)
		assert := assert.New(t)
		assert.Equal(&device_service.DepartmentDto{}, department)
	})
}

func TestIpInfoToPb(t *testing.T) {
	t.Parallel()
	t.Run("happy path", func(t *testing.T) {
		ipInfo := []*device.IpInfo{
			{
				Ip:   "***********",
				Area: 1,
			},
			{
				Ip:   "***********",
				Area: 2,
			},
		}
		pbIpInfo := device_service.IpInfoToPb(ipInfo)
		assert := assert.New(t)
		assert.Equal("***********", pbIpInfo[0].Ip)
		assert.Equal("***********", pbIpInfo[1].Ip)
		assert.Equal(uint64(1), pbIpInfo[0].Area)
		assert.Equal(uint64(2), pbIpInfo[1].Area)
	})

	t.Run("nil ipInfo", func(t *testing.T) {
		pbIpInfo := device_service.IpInfoToPb(nil)
		assert := assert.New(t)
		assert.Equal([]*device_service.IpDto{}, pbIpInfo)
	})
}
