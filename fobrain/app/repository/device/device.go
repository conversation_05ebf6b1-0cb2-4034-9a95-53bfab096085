package device

import (
	"context"
	"fmt"
	"fobrain/fobrain/app/services/permission"
	"fobrain/fobrain/logs"
	"fobrain/initialize/es"
	"fobrain/models/elastic/assets"
	"fobrain/models/elastic/device"
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/network_areas"
	"fobrain/pkg/utils"
	pgidservice "fobrain/services/people_pgid"
	"sync"
	"time"

	filtrate "fobrain/models/elastic"

	"github.com/gin-gonic/gin"
	"github.com/olivere/elastic/v7"
)

type AreaDto struct {
	Id   uint64 `json:"id"`
	Name string `json:"name"`
}

type SourceDto struct {
	Id   uint64 `json:"id"`
	Name string `json:"name"`
	Icon string `json:"icon"`
}

type DeviceBusinessDto struct {
	Business *assets.Business `json:"business" form:"business" uri:"business" validate:"omitempty" zh:"业务"`
	Ip       []*IpDto         `json:"ip" form:"ip" uri:"ip" validate:"omitempty" zh:"IP"`
}

type DeviceOperDto struct {
	OperInfo       *assets.PersonBase     `json:"oper_info"`
	OperDepartment *assets.DepartmentBase `json:"oper_department"`
	Ip             []*IpDto               `json:"ip"`
}

type OperInfoDto struct {
	Id   string `json:"id"`
	Fid  string `json:"fid"`
	Name string `json:"name"`
}

type BusinessInfoDto struct {
	System     string   `json:"system"`
	SystemId   string   `json:"system_id"`
	Owner      string   `json:"owner"`
	OwnerId    string   `json:"owner_id"`
	Department []string `json:"department"`
}

type DepartmentDto struct {
	BusinessSystemId   string           `json:"business_system_id"`
	BusinessSystemName string           `json:"business_system_name"`
	UserId             string           `json:"user_id"`
	UserName           string           `json:"user_name"`
	Name               string           `json:"name"`
	Id                 uint64           `json:"id"`
	Parents            []*DepartmentDto `json:"parents"`
}

type IpDto struct {
	Ip   string `json:"ip"`
	Area uint64 `json:"area"`
}

type DeviceDto struct {
	Id           string               `json:"id" form:"id" uri:"id" validate:"required,max=100" zh:"设备 ID"` // 限制为100个字符
	Fid          string               `json:"fid" form:"fid" uri:"fid" validate:"required,max=100" zh:"设备唯一标识"`
	Source       []*SourceDto         `json:"source" form:"source" uri:"source" validate:"omitempty" zh:"数据源"`
	Hostname     []string             `json:"hostname" form:"hostname" uri:"hostname" validate:"omitempty" zh:"主机名"`
	Sn           []string             `json:"sn" form:"sn" uri:"sn" validate:"omitempty" zh:"SN"`
	Mac          []string             `json:"mac" form:"mac" uri:"mac" validate:"omitempty" zh:"MAC"`
	Ip           []string             `json:"ip" form:"ip" uri:"ip" validate:"omitempty" zh:"IP"`
	PrivateIp    []string             `json:"private_ip" form:"private_ip" uri:"private_ip" validate:"omitempty" zh:"内网IP"`
	PublicIp     []string             `json:"public_ip" form:"public_ip" uri:"public_ip" validate:"omitempty" zh:"互联网IP"`
	MachineRoom  []string             `json:"machine_room" form:"machine_room" uri:"machine_room" validate:"omitempty" zh:"机房"`
	Area         []*AreaDto           `json:"area" form:"area" uri:"area" validate:"omitempty" zh:"区域"`
	OperInfo     []*DeviceOperDto     `json:"oper_info" form:"oper_info" uri:"oper_info" validate:"omitempty" zh:"运维人员"`
	Os           []string             `json:"os" form:"os" uri:"os" validate:"omitempty" zh:"操作系统"`
	CreatedAt    string               `json:"created_at" form:"created_at" uri:"created_at" validate:"omitempty" zh:"创建时间"`
	UpdatedAt    string               `json:"updated_at" form:"updated_at" uri:"updated_at" validate:"omitempty" zh:"更新时间"`
	DeletedAt    string               `json:"deleted_at" form:"deleted_at" uri:"deleted_at" validate:"omitempty" zh:"删除时间"`
	SourceId     []uint64             `json:"source_id" form:"source_id" uri:"source_id" validate:"omitempty" zh:"数据源 ID"`
	AreaId       []uint64             `json:"area_id" form:"area_id" uri:"area_id" validate:"omitempty" zh:"区域 ID"`
	Tags         []string             `json:"tags" form:"tags" uri:"tags" validate:"omitempty" zh:"标签"`
	PocNum       int64                `json:"poc_num" form:"poc_num" uri:"poc_num" validate:"omitempty" zh:"漏洞数量"`
	Business     []*DeviceBusinessDto `json:"business" form:"business" uri:"business" validate:"omitempty" zh:"业务系统"`
	CustomFields map[string]string    `json:"custom_fields" form:"custom_fields" uri:"custom_fields" validate:"omitempty" zh:"自定义字段"`
}

func List(c *gin.Context, page, perPage int, isDeleted bool, keyword string, isSuperManage bool, staffIds []string, searchCondition []string, field string, order string) ([]*DeviceDto, int64, error) {
	logger := logs.GetLogger()
	result := make([]*DeviceDto, 0)
	query := elastic.NewBoolQuery()
	// 权限相关的查询条件
	permissionQuery, err := permission.NewPermissionService().ProcessElasticsearchQuery(c, data_source.SourceTypeDevice)
	if err != nil {
		return nil, 0, err
	}
	if permissionQuery != nil {
		query = query.Must(permissionQuery)
	}
	if isDeleted {
		// 查询 deleted_at 字段存在且不为空字符串
		existsQuery := elastic.NewExistsQuery("deleted_at")
		query.Must(existsQuery)
	} else {
		// 查询 deleted_at 字段为 null 或空字符串
		query.MustNot(elastic.NewExistsQuery("deleted_at"))
	}
	if keyword != "" {
		query = device.NewDeviceModel().NewKeywordQuery(keyword, query)
	}

	// 高级筛选
	if len(searchCondition) > 0 {
		conditions, err := filtrate.ParseQueryConditions(searchCondition)
		if err != nil {
			logs.GetLogger().Warnf("device CreateBoolQuery 解析查询条件失败 %s", err.Error())
		}
		for _, condition := range conditions {
			query = filtrate.BuildBoolQuery(condition.Field, condition.OperationTypeString, condition.LogicalConnective, condition.Value, query)
		}
	}
	sortList := make([]elastic.Sorter, 0)
	if len(field) > 0 && len(order) > 0 {
		var sorter elastic.Sorter
		switch order {
		case "ascend":
			sorter = elastic.NewFieldSort(field).Asc()
		default:
			sorter = elastic.NewFieldSort(field).Desc()
		}
		sortList = append(sortList, sorter, elastic.NewFieldSort("id").Desc())
	} else {
		sortList = append(sortList,
			elastic.NewFieldSort("updated_at").Desc(),
			elastic.NewFieldSort("created_at").Desc(),
			elastic.NewFieldSort("id").Desc(),
		)
	}
	count, err := es.GetCount(device.NewDeviceModel().IndexName(), query)
	if err != nil {
		return result, 0, err
	}
	if count > 0 {
		_, data, err := es.List[device.Device](page, perPage, query, sortList)
		if err != nil {
			return result, count, err
		}
		allSourceId := make([]uint64, 0)
		allAreaId := make([]uint64, 0)
		allSourceList := make([]*data_source.Source, 0)
		allAreaList := make([]*network_areas.NetworkArea, 0)
		for _, v := range data {
			allSourceId = append(allSourceId, v.AllSourceIds...)
			for _, a := range v.Area {
				allAreaId = append(allAreaId, uint64(a))
			}
		}
		// 并发查询数据源和网络区域
		wg := &sync.WaitGroup{}
		wg.Add(2)
		go func() {
			defer wg.Done()
			allSourceId = utils.ListDistinctNonZero(allSourceId)
			if len(allSourceId) > 0 {
				allSourceList = data_source.NewSourceModel().SourceNames(allSourceId)
			}
		}()
		go func() {
			defer wg.Done()
			allAreaId = utils.ListDistinctNonZero(allAreaId)
			if len(allAreaId) > 0 {
				allAreaList, err = network_areas.NewNetworkAreaModel().GetByIds(allAreaId)
				if err != nil {
					logger.Warnf("get network area list error: %v", err)
				}
			}
		}()
		wg.Wait()
		for _, v := range data {
			result = append(result, &DeviceDto{
				Id:  v.Id,
				Fid: v.Fid,
				Hostname: func() []string {
					if len(v.HostName) == 0 {
						return []string{}
					}
					return v.HostName
				}(),
				Sn: func() []string {
					if len(v.Sn) == 0 {
						return make([]string, 0)
					}
					return v.Sn
				}(),
				Mac:         v.Mac,
				Ip:          v.Ip,
				PrivateIp:   v.PrivateIp,
				PublicIp:    v.PublicIp,
				MachineRoom: v.MachineRoom,
				Os:          v.Os,
				CreatedAt: func() string {
					if v.CreatedAt != nil {
						return v.CreatedAt.String()
					}
					return ""
				}(),
				UpdatedAt: func() string {
					if v.UpdatedAt != nil {
						return v.UpdatedAt.String()
					}
					return ""
				}(),
				SourceId: v.SourceIds,
				Source: func() []*SourceDto {
					if len(allSourceList) > 0 {
						sourceList := make([]*SourceDto, 0)
						for _, s := range v.AllSourceIds {
							source := getSourceFromSourceListById(s, allSourceList)
							if source != nil {
								sourceList = append(sourceList, source)
							}
						}
						return sourceList
					}
					return make([]*SourceDto, 0)
				}(),
				AreaId: func() []uint64 {
					ids := make([]uint64, 0)
					for _, a := range v.Area {
						ids = append(ids, uint64(a))
					}
					return ids
				}(),
				Area: func() []*AreaDto {
					if len(allAreaList) > 0 {
						areaList := make([]*AreaDto, 0)
						for _, a := range v.Area {
							area := getAreaFromAreaListById(uint64(a), allAreaList)
							if area != nil {
								areaList = append(areaList, area)
							}
						}
						return areaList
					}
					return make([]*AreaDto, 0)
				}(),
				Tags:   v.Tags,
				PocNum: v.PocNum,
				Business: func() []*DeviceBusinessDto {
					businessList := make([]*DeviceBusinessDto, 0)
					for _, b := range v.Business {
						if b.Business != nil {
							for _, p := range b.Business.PersonBase {
								p.Pgid, _ = pgidservice.GetPgidById(p.Id)
							}
						}
						businessList = append(businessList, &DeviceBusinessDto{
							Business: b.Business,
							Ip:       IpInfoToPb(b.Ip),
						})
					}
					return businessList
				}(),
				OperInfo: func() []*DeviceOperDto {
					opersList := make([]*DeviceOperDto, 0)
					for _, o := range v.Opers {
						if o.OperInfo != nil {
							o.OperInfo.Pgid, _ = pgidservice.GetPgidById(o.OperInfo.Id)
						}
						opersList = append(opersList, &DeviceOperDto{
							OperInfo:       o.OperInfo,
							OperDepartment: o.OperDepartment,
							Ip:             IpInfoToPb(o.Ip),
						})
					}
					return opersList
				}(),
				CustomFields: v.CustomFields,
			})
		}
	}
	return result, count, nil
}

func DepartmentToPb(department *assets.DepartmentBase) *DepartmentDto {
	if department == nil {
		return &DepartmentDto{}
	}

	return &DepartmentDto{
		BusinessSystemId:   department.BusinessSystemId,
		BusinessSystemName: department.BusinessSystemName,
		UserId:             department.UserId,
		UserName:           department.UserName,
		Name:               department.Name,
		Parents: func() []*DepartmentDto {
			if len(department.Parents) > 0 {
				parents := make([]*DepartmentDto, 0)
				for _, p := range department.Parents {
					parents = append(parents, DepartmentToPb(p))
				}
				return parents
			}
			return make([]*DepartmentDto, 0)
		}(),
	}
}

func IpInfoToPb(ips []*device.IpInfo) []*IpDto {
	if len(ips) == 0 {
		return make([]*IpDto, 0)
	}
	ipList := make([]*IpDto, 0)
	for _, ip := range ips {
		ipList = append(ipList, &IpDto{
			Ip:   ip.Ip,
			Area: uint64(ip.Area),
		})
	}
	return ipList
}

// getSourceFromSourceListById 获取数据源信息
func getSourceFromSourceListById(sourceId uint64, sourceList []*data_source.Source) *SourceDto {
	if sourceId == 0 {
		return nil
	}
	for _, s := range sourceList {
		if s.Id == sourceId {
			return &SourceDto{
				Id:   s.Id,
				Name: s.Name,
				Icon: s.Icon,
			}
		}
	}
	return nil
}

// getAreaFromAreaListById 获取区域信息
func getAreaFromAreaListById(areaId uint64, areaList []*network_areas.NetworkArea) *AreaDto {
	for _, a := range areaList {
		if a.Id == areaId {
			return &AreaDto{
				Id:   a.Id,
				Name: a.Name,
			}
		}
	}
	return nil
}

func updateDeviceDeletedAt(ctx *gin.Context, ids []string, searchCondition []string, keyword string) (query *elastic.BoolQuery, existDevices []*device.Device, err error) {
	logs := logs.GetLogger()
	ids = utils.ListDistinctNonZero(ids)
	query = elastic.NewBoolQuery()
	// 权限相关的查询条件
	permissionQuery, err := permission.NewPermissionService().ProcessElasticsearchQuery(ctx, data_source.SourceTypeDevice)
	if err != nil {
		return nil, nil, err
	}
	if permissionQuery != nil {
		query = query.Must(permissionQuery)
	}
	query = query.MustNot(elastic.NewExistsQuery("deleted_at"))
	if len(ids) > 0 {
		query.Must(elastic.NewTermsQueryFromStrings("id", ids...))
	} else {
		if len(searchCondition) > 0 {
			conditions, err := filtrate.ParseQueryConditions(searchCondition)
			if err != nil {
				logs.Errorf("device CreateBoolQuery 解析查询条件失败 %s", err.Error())
			}
			for _, condition := range conditions {
				query = filtrate.BuildBoolQuery(condition.Field, condition.OperationTypeString, condition.LogicalConnective, condition.Value, query)
			}
		}
		if keyword != "" {
			query = device.NewDeviceModel().NewKeywordQuery(keyword, query)
		}
	}

	// 查询需要删除的设备信息
	existDevices, err = es.All[device.Device](1000, query, nil, "id", "source_ids", "fid")
	if err != nil {
		return query, nil, err
	}

	// 更新删除时间
	resp, err := elastic.NewUpdateByQueryService(es.GetEsClient().Client).Index(device.NewDeviceModel().IndexName()).Query(query).Script(elastic.NewScript("ctx._source.deleted_at = params.deleted_at").
		Params(map[string]interface{}{
			"deleted_at": time.Now().Format("2006-01-02 15:04:05"),
		})).Do(context.Background())
	if err != nil {
		return query, existDevices, err
	}
	logs.Infof("更新删除时间成功: %v", resp)

	return query, existDevices, nil
}

func Delete(ctx *gin.Context, ids []string, searchCondition []string, keyword string) error {
	logs := logs.GetLogger()

	// 更新删除时间
	_, existDevices, err := updateDeviceDeletedAt(ctx, ids, searchCondition, keyword)
	if err != nil {
		logs.Errorf("删除设备，更新删除时间失败: %v", err)
		return err
	}

	logs.Infof("成功删除 %d 个设备", len(existDevices))
	return nil
}

// Purge 彻底删除设备（硬删除，从数据库中删除）
func Purge(ctx *gin.Context, ids []string, searchCondition []string, keyword string) error {
	logs := logs.GetLogger()
	query := elastic.NewBoolQuery().Must(elastic.NewExistsQuery("deleted_at"))
	// 权限相关的查询条件
	permissionQuery, err := permission.NewPermissionService().ProcessElasticsearchQuery(ctx, data_source.SourceTypeDevice)
	if err != nil {
		return err
	}
	if permissionQuery != nil {
		query = query.Must(permissionQuery)
	}

	hasCondition := false
	if len(ids) > 0 {
		hasCondition = true
		query.Must(elastic.NewTermsQueryFromStrings("id", ids...))
	} else {
		if len(searchCondition) > 0 {
			hasCondition = true
			conditions, err := filtrate.ParseQueryConditions(searchCondition)
			if err != nil {
				logs.Errorf("assets CreateBoolQuery 解析查询条件失败 %s", err.Error())
			}
			for _, condition := range conditions {
				query = filtrate.BuildBoolQuery(condition.Field, condition.OperationTypeString, condition.LogicalConnective, condition.Value, query)
			}
		}
		if keyword != "" {
			hasCondition = true
			query = device.NewDeviceModel().NewKeywordQuery(keyword, query)
		}
	}
	if !hasCondition {
		query.Must(elastic.NewExistsQuery("deleted_at"))
	}

	// 查找资产信息，source+sn+mac+hostname+merge_key
	existDevices, err := es.All[device.Device](1000, query, nil, "id", "source_ids", "fid", "all_process_ids")
	if err != nil {
		msg := fmt.Sprintf("彻底删除设备，查询设备信息失败: %v", err)
		logs.Errorf(msg)
		return err
	}
	if len(existDevices) == 0 {
		msg := "彻底删除设备，没有找到需要删除的设备信息"
		logs.Warn(msg)
		return nil
	}

	// 删除设备数据
	resp, err := es.GetEsClient().DeleteByQuery(device.NewDeviceModel().IndexName()).Refresh("true").ScrollSize(1000).Query(query).Do(context.Background())
	if err != nil {
		msg := fmt.Sprintf("彻底删除设备，删除设备数据失败: %v", err)
		logs.Errorf(msg)
		return err
	}
	logs.Infof("彻底删除设备，删除设备数据成功: %v", resp)

	// 收集需要删除的设备id
	existDeviceIds := make([]string, 0)
	processIds := make([]string, 0)
	for _, device := range existDevices {
		existDeviceIds = append(existDeviceIds, device.Id)
		processIds = append(processIds, device.AllProcessIds...)
	}

	// 删除设备融合记录
	mergeRecordQuery := elastic.NewBoolQuery().Must(elastic.NewTermsQueryFromStrings("device_id", existDeviceIds...))
	resp, err = es.GetEsClient().DeleteByQuery(device.NewMergeRecordsModel().IndexName()).ScrollSize(1000).Query(mergeRecordQuery).Do(context.Background())
	if err != nil {
		msg := fmt.Sprintf("彻底删除设备，删除设备融合记录失败: %v", err)
		logs.Errorf(msg)
	}
	logs.Infof("彻底删除设备，删除设备融合记录成功: %v", resp)

	// 删除设备历史记录
	deviceRecordQuery := elastic.NewBoolQuery().Must(elastic.NewTermsQueryFromStrings("device_id", existDeviceIds...))
	resp, err = es.GetEsClient().DeleteByQuery(device.NewDeviceRecordModel().IndexName()).ScrollSize(1000).Query(deviceRecordQuery).Do(context.Background())
	if err != nil {
		msg := fmt.Sprintf("彻底删除设备，删除设备历史记录失败: %v", err)
		logs.Errorf(msg)
		return err
	}
	logs.Infof("彻底删除设备，删除设备历史记录成功: %v", resp)

	// 删除设备过程表
	processDeviceQuery := elastic.NewBoolQuery().Must(elastic.NewTermsQueryFromStrings("id", processIds...))
	resp, err = es.GetEsClient().DeleteByQuery(device.NewProcessDeviceModel().IndexName()).Refresh("true").ScrollSize(1000).Query(processDeviceQuery).Do(context.Background())
	if err != nil {
		msg := fmt.Sprintf("彻底删除设备，删除设备过程表失败: %v", err)
		logs.Errorf(msg)
		return err
	}
	logs.Infof("彻底删除设备，删除设备过程表成功: %v", resp)

	batch := 1
	batchSize := 100
	if len(existDevices) > batchSize {
		batch = len(existDevices) / batchSize
	}
	for i := 0; i < batch; i++ {
		// 删除过程表
		querys := make([]elastic.Query, 0)
		start := i * batchSize
		end := (i + 1) * batchSize
		if end > len(existDevices) {
			end = len(existDevices)
		}
		// 收集本批次需要删除的设备id，用于删除设备历史记录和设备融合记录
		deviceIds := make([]string, 0)
		for _, device := range existDevices[start:end] {
			sIds := utils.ListDistinctNonZero(device.SourceIds)
			for _, sourceId := range sIds {
				querys = append(querys, elastic.NewBoolQuery().Must(elastic.NewTermQuery("source", sourceId), elastic.NewTermQuery("unique_key", device.Fid)))
			}
			deviceIds = append(deviceIds, device.Id)
		}
		query = elastic.NewBoolQuery().Should(querys...).MinimumNumberShouldMatch(1)
		resp, err = es.GetEsClient().DeleteByQuery(device.NewProcessDeviceModel().IndexName()).Refresh("true").Query(query).Do(context.Background())
		if err != nil {
			msg := fmt.Sprintf("彻底删除设备，删除过程表[%d-%d]失败: %v", start, end, err)
			logs.Errorf(msg)
			continue
		}
		logs.Infof("彻底删除设备，删除过程表[%d-%d]成功: %v", start, end, resp)

		// 删除设备历史记录
		deviceRecordQuery := elastic.NewBoolQuery().Must(elastic.NewTermsQueryFromStrings("device_id", deviceIds...))
		resp, err = es.GetEsClient().DeleteByQuery(device.NewDeviceRecordModel().IndexName()).Query(deviceRecordQuery).Do(context.Background())
		if err != nil {
			msg := fmt.Sprintf("彻底删除设备，删除设备历史记录[%d-%d]失败: %v", start, end, err)
			logs.Errorf(msg)
			continue
		}
		logs.Infof("彻底删除设备，删除设备历史记录[%d-%d]成功: %v", start, end, resp)

		// 删除设备融合记录
		mergeRecordQuery := elastic.NewBoolQuery().Must(elastic.NewTermsQueryFromStrings("device_id", deviceIds...))
		resp, err = es.GetEsClient().DeleteByQuery(device.NewMergeRecordsModel().IndexName()).Query(mergeRecordQuery).Do(context.Background())
		if err != nil {
			msg := fmt.Sprintf("彻底删除设备，删除设备融合记录[%d-%d]失败: %v", start, end, err)
			logs.Errorf(msg)
			continue
		}
		logs.Infof("彻底删除设备，删除设备融合记录[%d-%d]成功: %v", start, end, resp)
	}

	return nil
}
