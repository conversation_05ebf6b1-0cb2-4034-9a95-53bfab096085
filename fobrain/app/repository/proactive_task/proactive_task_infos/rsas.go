package proactive_task_infos

import (
	"fobrain/models/elastic/source/nsfocus_rsas"
	"fobrain/models/mysql/data_source"
	"github.com/olivere/elastic/v7"
)

func init() {
	DataSourceConfig[data_source.NSFocusRsasSourceId] = RSAS{}
}

// RSAS 实现 RSAS 数据源的查询接口
type RSAS struct{}

func (x RSAS) IndexName(syncType int, proactiveTaskId int64) string {
	return nsfocus_rsas.NewNsfocusRsasTaskThreatsModel().IndexName()
}
func (x RSAS) BuildQuery(syncType int, search string) *elastic.BoolQuery {
	query := elastic.NewBoolQuery()
	query.Must(elastic.NewTermQuery("task_type", 2))
	if search != "" {
		return query.Must(nsfocus_rsas.NewNsfocusRsasTaskThreatsModel().NewKeywordQuery(search))
	}
	return query
}
func (x RSAS) GetListFields(syncType int, proactiveTaskId int64) []map[string]string {
	return nsfocus_rsas.NewNsfocusRsasTaskThreatsModel().GetNsfocusRsasThreatListFields()
}
func (x RSAS) GetDetailFields(syncType int, proactiveTaskId int64) []map[string]string {
	return nsfocus_rsas.NewNsfocusRsasTaskThreatsModel().GetNsfocusRsasThreatListDetailFields()
}
