package proactive_task_infos

import (
	"fobrain/models/elastic/source/d01"
	"fobrain/models/elastic/source/foeye"
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/data_sync_task"

	"github.com/olivere/elastic/v7"
)

func init() {
	DataSourceConfig[data_source.D01SourceId] = D01{}
}

// D01 实现 D01 数据源的查询接口
type D01 struct{}

func (d D01) IndexName(syncType int, proactiveTaskId int64) string {
	version := foeye.QueryProactiveTaskVersion(int(proactiveTaskId))
	switch syncType {
	case data_sync_task.SyncAsset:
		if version == "v2" {
			return d01.D01V2FinishedAssets
		}
		return d01.NewFinishedAssetsModel().IndexName()
	case data_sync_task.SyncThreat:
		if version == "v2" {
			return d01.D01V2FinishedThreats
		}
		return d01.NewFinishedThreatsModel().IndexName()
	}
	return ""
}
func (d D01) BuildQuery(syncType int, search string) *elastic.BoolQuery {
	query := elastic.NewBoolQuery()
	if search == "" {
		return query
	}
	switch syncType {
	case data_sync_task.SyncAsset:
		return query.Must(d01.NewTaskAssetsModel().NewKeywordQuery(search))
	case data_sync_task.SyncThreat:
		return query.Must(d01.NewTaskThreatsModel().NewKeywordQuery(search))
	}
	return query
}
func (d D01) GetListFields(syncType int, proactiveTaskId int64) []map[string]string {
	version := foeye.QueryProactiveTaskVersion(int(proactiveTaskId))
	switch syncType {
	case data_sync_task.SyncAsset:
		if version == "v2" {
			return foeye.NewFoeyeTaskAssetsModel().GetFoeyeAssetListFieldsV2Rule()
		}
		return d01.NewTaskAssetsModel().GetAssetListFields()
	case data_sync_task.SyncThreat:
		return d01.NewTaskThreatsModel().GetThreatListFields()
	}
	return nil
}
func (d D01) GetDetailFields(syncType int, proactiveTaskId int64) []map[string]string {
	version := foeye.QueryProactiveTaskVersion(int(proactiveTaskId))
	switch syncType {
	case data_sync_task.SyncAsset:
		if version == "v2" {
			return foeye.NewFoeyeTaskAssetsModel().GetFoeyeAssetListDetailFieldsV2()
		}
		return d01.NewTaskAssetsModel().GetAssetListDetailFields()
	case data_sync_task.SyncThreat:
		return d01.NewTaskThreatsModel().GetThreatListDetailFields()
	}
	return nil
}
