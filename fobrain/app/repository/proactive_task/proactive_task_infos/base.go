package proactive_task_infos

import (
	"context"
	"errors"
	"fobrain/initialize/es"
	"fobrain/models/mysql/data_sync_task"
	"fobrain/models/mysql/task"
	"github.com/olivere/elastic/v7"
)

var DataSourceConfig = make(map[int]DataSource)

type DataSource interface {
	IndexName(syncType int, proactiveTaskId int64) string
	BuildQuery(syncType int, search string) *elastic.BoolQuery
	GetListFields(syncType int, proactiveTaskId int64) []map[string]string
	GetDetailFields(syncType int, proactiveTaskId int64) []map[string]string
}

// DataSource 定义数据源接口
func getDataSource(sourceId int) (DataSource, error) {
	sourceInterface := DataSourceConfig[sourceId]
	if sourceInterface == nil {
		return nil, errors.New("unsupported data source")
	}

	return sourceInterface, nil
}

// searchList 公共的搜索逻辑
func searchList(id int64, keyword string, page int, perPage int, syncType int) (any, int64, []map[string]string, []map[string]string, error) {
	proactiveTask, err := task.NewProactiveTasks().FindById(id)
	if err != nil {
		return nil, 0, nil, nil, err
	}

	dataSource, err := getDataSource(proactiveTask.SourceId)
	if err != nil {
		return nil, 0, nil, nil, err
	}

	indexName := dataSource.IndexName(syncType, id)
	if indexName == "" {
		return "", 0, nil, nil, errors.New("数据异常")
	}

	// 添加 proactive_task_id = id 的过滤条件
	boolQuery := dataSource.BuildQuery(syncType, keyword)
	boolQuery = boolQuery.Must(elastic.NewTermQuery("proactive_task_id", id))

	result, err := es.GetEsClient().Search(indexName).TrackTotalHits(true).
		From(es.GetFrom(page, perPage)).Size(es.GetSize(perPage)).Query(boolQuery).
		Do(context.TODO())
	if err != nil {
		return "", 0, nil, nil, err
	}

	list := make([]any, 0)
	for _, hit := range result.Hits.Hits {
		list = append(list, hit.Source)
	}

	return list, result.TotalHits(), dataSource.GetListFields(syncType, id), dataSource.GetDetailFields(syncType, id), nil
}

// AssetList 任务资产列表
func AssetList(id int64, keyword string, page int, perPage int) (any, int64, []map[string]string, []map[string]string, error) {
	return searchList(id, keyword, page, perPage, data_sync_task.SyncAsset)
}

// ThreatList 任务风险列表
func ThreatList(id int64, keyword string, page int, perPage int) (any, int64, []map[string]string, []map[string]string, error) {
	return searchList(id, keyword, page, perPage, data_sync_task.SyncThreat)
}
