package proactive_task_infos

import (
	xray "fobrain/models/elastic/source/x-ray"
	"fobrain/models/mysql/data_source"

	"github.com/olivere/elastic/v7"
)

func init() {
	DataSourceConfig[data_source.XRaySourceId] = XRay{}
}

// XRay 实现 XRay 数据源的查询接口
type XRay struct{}

func (x XRay) IndexName(syncType int, proactiveTaskId int64) string {
	return xray.NewFinishedThreatsModel().IndexName()
}
func (x XRay) BuildQuery(syncType int, search string) *elastic.BoolQuery {
	query := elastic.NewBoolQuery()
	if search != "" {
		return query.Must(xray.NewTaskThreatsModel().NewKeywordQuery(search))
	}
	return query
}
func (x XRay) GetListFields(syncType int, proactiveTaskId int64) []map[string]string {
	return xray.NewTaskThreatsModel().GetThreatListFields()
}
func (x XRay) GetDetailFields(syncType int, proactiveTaskId int64) []map[string]string {
	return xray.NewTaskThreatsModel().GetThreatListDetailFields()
}
