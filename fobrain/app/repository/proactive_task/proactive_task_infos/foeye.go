package proactive_task_infos

import (
	"fobrain/models/elastic/source/foeye"
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/data_sync_task"
	"github.com/olivere/elastic/v7"
)

func init() {
	DataSourceConfig[data_source.FoeyeSourceId] = Foeye{}
}

type Foeye struct{}

func (f Foeye) IndexName(syncType int, proactiveTaskId int64) string {
	version := foeye.QueryProactiveTaskVersion(int(proactiveTaskId))
	switch syncType {
	case data_sync_task.SyncAsset:
		if version == "v2" {
			return foeye.FoeyeV2FinishedAssets
		}
		return foeye.NewFoeyeFinishedAssetsModel().IndexName()
	case data_sync_task.SyncThreat:
		if version == "v2" {
			return foeye.FoeyeV2FinishedThreats
		}
		return foeye.NewFoeyeFinishedThreatsModel().IndexName()
	}
	return ""
}
func (f Foeye) BuildQuery(syncType int, search string) *elastic.BoolQuery {
	query := elastic.NewBoolQuery()
	if search == "" {
		return query
	}
	switch syncType {
	case data_sync_task.SyncAsset:
		return query.Must(foeye.NewFoeyeTaskAssetsModel().NewKeywordQuery(search))
	case data_sync_task.SyncThreat:
		return query.Must(foeye.NewFoeyeTaskThreatsModel().NewKeywordQuery(search))
	}
	return query
}
func (f Foeye) GetListFields(syncType int, proactiveTaskId int64) []map[string]string {
	version := foeye.QueryProactiveTaskVersion(int(proactiveTaskId))
	switch syncType {
	case data_sync_task.SyncAsset:
		if version == "v2" {
			return foeye.NewFoeyeTaskAssetsModel().GetFoeyeAssetListFieldsV2Rule()
		}
		return foeye.NewFoeyeTaskAssetsModel().GetFoeyeAssetListFields()
	case data_sync_task.SyncThreat:
		return foeye.NewFoeyeTaskThreatsModel().GetThreatListFields()
	}
	return nil
}
func (f Foeye) GetDetailFields(syncType int, proactiveTaskId int64) []map[string]string {
	version := foeye.QueryProactiveTaskVersion(int(proactiveTaskId))
	switch syncType {
	case data_sync_task.SyncAsset:
		if version == "v2" {
			return foeye.NewFoeyeTaskAssetsModel().GetFoeyeAssetListDetailFieldsV2()
		}
		return foeye.NewFoeyeTaskAssetsModel().GetFoeyeAssetListDetailFields()
	case data_sync_task.SyncThreat:
		return foeye.NewFoeyeTaskThreatsModel().GetThreatListDetailFields()
	}
	return nil
}
