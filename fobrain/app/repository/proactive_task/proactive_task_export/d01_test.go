package proactive_task_export

import (
	"testing"

	"github.com/olivere/elastic/v7"
)

func TestParseAssetDataOnD01(t *testing.T) {
	handler := &D01Handler{}

	// 构建一个模拟的 Elasticsearch hit（模拟查询结果）
	assetData := `{
		"ip": "***********",
		"port_list": [
			{
				"port": 80,
				"protocol": "TCP",
				"rule_info": [
					{"name": "HTTP服务"},
					{"name": "备用HTTP服务"}
				]
			},
			{
				"port": 443,
				"protocol": "TCP",
				"rule_info": [
					{"name": "HTTPS服务"}
				]
			}
		],
		"domain": "example.com",
		"os": "Linux",
		"mac": "00:1A:2B:3C:4D:5E"
	}`

	// 模拟 Elasticsearch 的 SearchHit
	hit := &elastic.SearchHit{
		Source: []byte(assetData),
	}

	// 调用函数
	result, err := handler.parseAssetData(hit)
	if err != nil {
		t.Fatalf("Error parsing asset data: %v", err)
	}

	// 预期的输出
	expected := []interface{}{
		"***********", // IP
		[][]string{
			{"80", "TCP", "HTTP服务,备用HTTP服务"},
			{"443", "TCP", "HTTPS服务"},
		}, // 端口和协议信息
		"example.com",       // 域名
		"Linux",             // 操作系统
		"00:1A:2B:3C:4D:5E", // MAC 地址
	}

	// 比较结果
	if len(result) != len(expected) {
		t.Errorf("Expected result length %d, got %d", len(expected), len(result))
	}

	for i := range result {
		if !equal(result[i], expected[i]) {
			t.Errorf("Expected result[%d] %v, got %v", i, expected[i], result[i])
		}
	}
}
