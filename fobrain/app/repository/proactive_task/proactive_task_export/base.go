package proactive_task_export

import (
	"context"
	"errors"
	"fmt"
	"io"
	"math"
	"strconv"
	"strings"
	"time"

	"github.com/olivere/elastic/v7"
	"github.com/xuri/excelize/v2"
	"go-micro.dev/v4/logger"

	"fobrain/initialize/es"
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/proactive_task_node_relations"
	"fobrain/models/mysql/task"
	"fobrain/pkg/utils"
)

// Asset 数据模型定义
type Asset struct {
	Ip        string        `json:"ip"`
	Os        string        `json:"os"`
	PortList  []Port        `json:"port_list"`
	Mac       string        `json:"mac"`
	Domain    string        `json:"domain"`
	RuleInfos []D01RuleInfo `json:"rule_infos"`
}

type Port struct {
	Protocol string     `json:"protocol"`
	Port     int        `json:"port"`
	RuleInfo []RuleInfo `json:"rule_info"`
	Host     string     `json:"host"`
}
type D01RuleInfo struct {
	Ports []int  `json:"ports"`
	Title string `json:"title"`
}
type RuleInfo struct {
	Description string `json:"description"`
	Name        string `json:"name"`
}

type Threat struct {
	UID               string `json:"uid"`
	CommonTitle       string `json:"common_title"`
	CreateTime        string `json:"createtime"`
	LastUpdateTime    string `json:"lastupdatetime"`
	Level             int    `json:"level"`
	VulType           string `json:"vulType"`
	IP                string `json:"ip"`
	CveID             string `json:"cveId"`
	URL               string `json:"url"`
	IsXC              int    `json:"is_xc"`
	CommonDescription string `json:"common_description"`
	Recommandation    string `json:"recommandation"`
}

// 常量定义
const (
	TypeAssetData  = 1
	TypeThreatData = 2
)

var DataSourceConfig = make(map[int]DataSourceHandler)

// DataSourceHandler 数据源处理接口
type DataSourceHandler interface {
	Headers(exportType int) []string
	GetIndexName(exportType int, proactiveTaskId int) (string, error)
	BuildQuery(ids []string, taskId int) *elastic.BoolQuery
	ParseData(hit *elastic.SearchHit, exportType int) ([]interface{}, error)
}

// 获取处理器
func getHandler(sourceId int) (DataSourceHandler, error) {
	source := DataSourceConfig[sourceId]
	if source != nil {
		return source, nil
	}

	return nil, errors.New("未识别的数据源")
}

// ExportDataExcel
// @Summary 导出 Excel 数据
func ExportDataExcel(ids []string, exportType int, taskId int, sourceId int) (string, error) {
	handler, err := getHandler(sourceId)
	if err != nil {
		return "", err
	}

	indexName, err := handler.GetIndexName(exportType, taskId)
	if err != nil {
		return "", err
	}

	query := handler.BuildQuery(ids, taskId)
	scroll := es.GetEsClient().Scroll().Index(indexName).Query(query).Size(1000).Scroll("1m")
	defer scroll.Clear(context.Background())

	headers := handler.Headers(exportType)
	var data [][]interface{}
	totalFetched, maxResults := 0, 10000

	startTime := time.Now()

	for {
		result, err := scroll.Do(context.TODO())
		if errors.Is(err, io.EOF) || len(result.Hits.Hits) == 0 {
			break
		}
		if err != nil {
			return "", err
		}

		for _, hit := range result.Hits.Hits {
			rowData, err := handler.ParseData(hit, exportType)
			if err != nil {
				return "", err
			}
			data = append(data, rowData)
			totalFetched++
			if totalFetched >= maxResults {
				break
			}
		}

		if totalFetched >= maxResults {
			break
		}

		//scroll = es.GetEsClient().Scroll().ScrollId(result.ScrollId).Scroll("1m")
	}
	fmt.Println("整理 es 数据耗时：", time.Since(startTime).Seconds(), "秒")

	if len(data) == 0 {
		return "", errors.New("导出的数据不存在")
	}

	filePath := generateFilePath(exportType)
	fmt.Println("表头：", headers)
	fmt.Println("数据：", data)
	return saveToFile(filePath, headers, data)
}

// 构建查询
func buildQuery(ids []string, taskId int) *elastic.BoolQuery {
	query := elastic.NewBoolQuery()
	if len(utils.CompactStrings(ids)) > 0 {
		query = query.Must(elastic.NewTermsQueryFromStrings("id", ids...))
	} else {
		query = query.Must(elastic.NewTermQuery("proactive_task_id", taskId))
	}
	return query
}

// 生成文件路径
func generateFilePath(exportType int) string {
	var prefix string
	if exportType == TypeAssetData {
		prefix = "资产列表"
	} else {
		prefix = "漏洞列表"
	}
	return fmt.Sprintf("%s-%s.xlsx", time.Now().Format("20060102150405"), prefix)
}

// 保存数据到 Excel 文件
func saveToFile(filePath string, headers []string, data [][]interface{}) (string, error) {
	if _, err := utils.WriterExcel(filePath, headers, data); err != nil {
		fmt.Println("生成文件失败", err.Error())
		return "", err
	}
	return filePath, nil
}

func convertLevel(level int) string {
	switch level {
	case 1:
		return "低危"
	case 2:
		return "中危"
	case 3:
		return "高危"
	case 4:
		return "严重"
	default:
		return "未知"
	}
}

// ExportTaskBulletin 导出任务公告
//
// 该函数根据任务ID导出任务的公告信息。
//
// 参数:
//
//	id: 任务ID，类型为uint64
//
// 返回值:
//
//	无返回值
func ExportTaskBulletin(id int64) (string, error) {
	detail, err := task.NewProactiveTasks().GetTaskDetail(id)
	if err != nil {
		return "", err
	}
	filePath := time.Now().Format("20060102150405") + "-任务简报.xlsx"
	ex := excelize.NewFile()
	_, err = taskBulletin(ex, detail, "主动扫描任务简报", filePath)
	if err != nil {
		return "", err
	}

	return filePath, nil
}

// taskBulletin 函数用于生成并保存包含任务概览和详细信息的Excel文件。
//
// 参数：
// ex: *excelize.File类型，指向Excel文件对象的指针。
// detail: *task.ProactiveTasks类型，包含任务详细信息的结构体指针。
// sheetName: string类型，表示Excel文件中工作表的名称。
// filePath: string类型，表示要保存Excel文件的路径。
//
// 返回值：
// string类型，表示保存的Excel文件的路径。
// error类型，表示函数执行过程中可能发生的错误。
func taskBulletin(ex *excelize.File, detail *task.ProactiveTasks, sheetName, filePath string) (string, error) {
	ids := int(detail.Id)
	if detail.Type == "x_ray" {
		detail.TaskType = 2
	}
	ex, assetCount, vulCount, err := taskTypeExport(ids, detail.SourceId, detail.TaskType, ex)
	if err != nil {
		return "", err
	}
	otherCfgs := detail.OtherCfgs
	taskTimeout := 0.00         //最大运行时间
	ceiling := 0.00             //最大链接数
	retryNum := 0.00            //重试次数
	maxRedirectNum := 0.00      //最大重定向次数
	httpConnectTimeout := 0.00  //请求连接超时时间
	httpResponseTimeout := 0.00 //请求响应超时时间
	maxConnectNum := 0.00       //每个站点并发连接数
	maxPageSize := 0.00         //页面大小限制
	maxRequestPerSecond := 0.00 //每秒最大请求数
	if otherCfgs["task_timeout"] != nil {
		taskTimeout = otherCfgs["task_timeout"].(float64)
	}
	if otherCfgs["ceiling"] != nil {
		ceiling = otherCfgs["ceiling"].(float64)
	}
	if otherCfgs["retry_num"] != nil {
		retryNum = otherCfgs["retry_num"].(float64)
	}
	if otherCfgs["max_redirect_num"] != nil {
		maxRedirectNum = otherCfgs["max_redirect_num"].(float64)
	}
	if otherCfgs["http_connect_timeout"] != nil {
		httpConnectTimeout = otherCfgs["http_connect_timeout"].(float64)
	}
	if otherCfgs["http_response_timeout"] != nil {
		httpResponseTimeout = otherCfgs["http_response_timeout"].(float64)
	}
	if otherCfgs["max_connect_num"] != nil {
		maxConnectNum = otherCfgs["max_connect_num"].(float64)
	}
	if otherCfgs["max_page_size"] != nil {
		maxPageSize = otherCfgs["max_page_size"].(float64)
	}
	if otherCfgs["max_request_per_second"] != nil {
		maxRequestPerSecond = otherCfgs["max_request_per_second"].(float64)
	}
	sourceName := detail.SourceName
	if otherCfgs["scan_type_name"] != nil && detail.Type == "x_ray" {
		sourceName = otherCfgs["scan_type_name"].(string)
	}

	// Merge cells for headers
	sheet1 := sheetName
	ex.SetSheetName("Sheet1", sheetName)
	ex.MergeCell(sheet1, "A1", "H1")

	// Add headers
	ex.SetCellValue(sheet1, "A1", sheetName)

	seconds, err := formatToMinutesAndSeconds(detail.UseSeconds)
	if err != nil {
		logger.Errorf("formatToMinutesAndSeconds failed, err: %v", err)
	}

	// Add task details
	ex.SetCellValue(sheet1, "A2", "任务名称")
	ex.SetCellValue(sheet1, "A3", detail.Name)
	ex.SetCellValue(sheet1, "B2", "任务类型")
	ex.SetCellValue(sheet1, "B3", taskTypeName(detail.TaskType))
	ex.SetCellValue(sheet1, "C2", "节点类型")
	ex.SetCellValue(sheet1, "C3", detail.Type)
	ex.SetCellValue(sheet1, "D2", "任务状态")
	ex.SetCellValue(sheet1, "D3", getStatusName(detail.Status))
	ex.SetCellValue(sheet1, "E2", "开始时间")
	ex.SetCellValue(sheet1, "E3", detail.BeginTime.Format("2006-01-02 15:04:05"))
	ex.SetCellValue(sheet1, "F2", "结束时间")
	ex.SetCellValue(sheet1, "F3", detail.EndTime.Format("2006-01-02 15:04:05"))
	ex.SetCellValue(sheet1, "G2", "任务耗时")
	ex.SetCellValue(sheet1, "G3", seconds)
	ex.SetCellValue(sheet1, "H2", "发起人")
	ex.SetCellValue(sheet1, "H3", detail.UserName)
	portNames := portOrNames()
	scanPortName := ""
	if value, exists := portNames[detail.ScanPort]; exists {
		scanPortName = value
	}

	// 任务参数 相关
	ex.SetCellValue(sheet1, "A5", "任务参数")
	ex.SetCellValue(sheet1, "A6", "任务名称")
	ex.SetCellValue(sheet1, "B6", detail.Name)
	ex.SetCellValue(sheet1, "A7", "任务描述")
	ex.SetCellValue(sheet1, "B7", detail.Desc)
	ex.SetCellValue(sheet1, "A8", "扫描工具")
	ex.SetCellValue(sheet1, "B8", sourceName)
	ex.SetCellValue(sheet1, "A9", "扫描节点")
	ex.SetCellValue(sheet1, "B9", getNodeName(detail.Id))
	ex.SetCellValue(sheet1, "A10", "任务类型")
	ex.SetCellValue(sheet1, "B10", taskTypeName(detail.TaskType))
	ex.SetCellValue(sheet1, "A11", "扫描类型")
	ex.SetCellValue(sheet1, "B11", scanTypeName(detail.ScanType))
	ex.SetCellValue(sheet1, "A12", "扫描目标")
	ex.SetCellValue(sheet1, "B12", strings.Join(detail.ScanIPRanges, ","))
	ex.SetCellValue(sheet1, "A13", "扫描端口")
	ex.SetCellValue(sheet1, "B14", scanPortName)
	ex.SetCellValue(sheet1, "A14", "扫描带宽")
	ex.SetCellValue(sheet1, "B14", detail.Bandwidth)
	concurrency := strconv.Itoa(detail.Concurrency)
	if detail.Concurrency < 1 {
		concurrency = "动态分配"
	}
	ex.SetCellValue(sheet1, "A15", "并发")
	ex.SetCellValue(sheet1, "B15", concurrency)
	ex.SetCellValue(sheet1, "A16", "同步配置")
	ex.SetCellValue(sheet1, "B16", scanPlaneName(detail.ScanPlan))

	// xr
	if otherCfgs["scan_type_name"] != nil && detail.Type == "x_ray" {
		ex.SetCellValue(sheet1, "C6", "高级设置")
		ex.SetCellValue(sheet1, "D6", "")
		ex.SetCellValue(sheet1, "C7", "最大运行时间")
		ex.SetCellValue(sheet1, "D7", taskTimeout)
		ex.SetCellValue(sheet1, "C8", "重定向次数")
		ex.SetCellValue(sheet1, "D8", maxRedirectNum)
		ex.SetCellValue(sheet1, "C9", "最大链路数")
		ex.SetCellValue(sheet1, "D9", ceiling)
		ex.SetCellValue(sheet1, "C10", "重试次数")
		ex.SetCellValue(sheet1, "D10", retryNum)
		ex.SetCellValue(sheet1, "C11", "链接超时时间")
		ex.SetCellValue(sheet1, "D11", httpConnectTimeout)
		ex.SetCellValue(sheet1, "C12", "响应超时时间")
		ex.SetCellValue(sheet1, "D12", httpResponseTimeout)
		ex.SetCellValue(sheet1, "C13", "并发链接数")
		ex.SetCellValue(sheet1, "D13", maxConnectNum)
		ex.SetCellValue(sheet1, "C14", "页面限制")
		ex.SetCellValue(sheet1, "D14", maxPageSize)
		ex.SetCellValue(sheet1, "C15", "最大请求数")
		ex.SetCellValue(sheet1, "D15", maxRequestPerSecond)
	}

	// 扫描数据详情 详情
	ex.SetCellValue(sheet1, "A18", "扫描数据详情")
	ex.SetCellValue(sheet1, "A19", "资产数量")
	ex.SetCellValue(sheet1, "B19", assetCount)
	ex.SetCellValue(sheet1, "C19", "漏洞数量")
	ex.SetCellValue(sheet1, "D19", vulCount)

	// 设置Sheet1整体列宽
	for _, col := range []string{"A", "B", "C", "D", "E", "F", "G", "H"} {
		if err := ex.SetColWidth(sheet1, col, col, 28); err != nil {
			logger.Fatalf("设置列宽失败: %v", err)
		}
	}

	// 设置 A1:H1 样式：字体大小 24，居中
	headerStyle, _ := ex.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Size: 24,
		},
		Alignment: &excelize.Alignment{
			Horizontal: "center",
			Vertical:   "center",
		},
	})
	// 设置行高为 61
	if err := ex.SetRowHeight(sheetName, 1, 61); err != nil {
		logger.Fatalf("设置行高失败: %v", err)
	}
	ex.SetCellStyle(sheetName, "A1", "H1", headerStyle)

	// 设置A2:H3 A5:D16 样式：边框
	// 定义四周边框样式
	left, _ := ex.NewStyle(&excelize.Style{
		Border: []excelize.Border{
			{Type: "left", Color: "000000", Style: 1},
		},
	})
	A2H2top, _ := ex.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Size:   14,       // 字体大小
			Bold:   true,     // 加粗
			Family: "宋体-简",   // 如果支持简体宋体，可以设置
			Color:  "000000", // 字体颜色（默认黑色）
		},
		Border: []excelize.Border{
			{Type: "top", Color: "000000", Style: 1},
		},
	})
	top, _ := ex.NewStyle(&excelize.Style{
		Border: []excelize.Border{
			{Type: "top", Color: "000000", Style: 1},
		},
	})

	bottom, _ := ex.NewStyle(&excelize.Style{
		Border: []excelize.Border{
			{Type: "bottom", Color: "000000", Style: 1},
		},
	})

	A6A16left, _ := ex.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Size:   14,       // 字体大小
			Bold:   true,     // 加粗
			Family: "宋体-简",   // 如果支持简体宋体，可以设置
			Color:  "000000", // 字体颜色（默认黑色）
		},
		Border: []excelize.Border{
			{Type: "left", Color: "000000", Style: 1},
		},
	})

	setFont, _ := ex.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Size:   14,       // 字体大小
			Bold:   true,     // 加粗
			Family: "宋体-简",   // 如果支持简体宋体，可以设置
			Color:  "000000", // 字体颜色（默认黑色）
		},
	})

	ex.SetCellStyle(sheetName, "A2", "A3", left)
	ex.SetCellStyle(sheetName, "A2", "H2", A2H2top)
	ex.SetCellStyle(sheetName, "A3", "H3", bottom)
	ex.SetCellStyle(sheetName, "I2", "I3", left)

	ex.SetCellStyle(sheetName, "A5", "A6", left)
	ex.SetCellStyle(sheetName, "A6", "A16", A6A16left)
	ex.SetCellStyle(sheetName, "A5", "D5", top)
	ex.SetCellStyle(sheetName, "B16", "D16", bottom)
	ex.SetCellStyle(sheetName, "A17", "A17", top)
	ex.SetCellStyle(sheetName, "E5", "E16", left)
	ex.SetCellStyle(sheetName, "C6", "C15", setFont)

	A18D18top, _ := ex.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Size:   16,       // 字体大小
			Bold:   true,     // 加粗
			Family: "宋体-简",   // 如果支持简体宋体，可以设置
			Color:  "000000", // 字体颜色（默认黑色）
		},
		Border: []excelize.Border{
			{Type: "top", Color: "000000", Style: 1},
		},
	})
	ex.SetCellStyle(sheetName, "A18", "A19", left)
	ex.SetCellStyle(sheetName, "A18", "D18", A18D18top)
	ex.SetCellStyle(sheetName, "A19", "D19", bottom)
	ex.SetCellStyle(sheetName, "E18", "E19", left)
	ex.SetCellStyle(sheetName, "A5", "A5", A18D18top)

	if err := ex.SaveAs(filePath); err != nil {
		return "", err
	}
	return filePath, nil
}

// getNodeName 函数根据任务ID获取对应的节点名称，并以逗号分隔的字符串形式返回。
//
// 参数：
// taskId: uint64类型，表示任务的ID。
//
// 返回值：
// string类型，表示与任务ID对应的节点名称，节点名称之间以逗号分隔。
func getNodeName(taskId uint64) string {
	ids := proactive_task_node_relations.GetNodeIds(int(taskId))
	nodesIds := make([]uint64, 0)
	for _, id := range ids {
		nodesIds = append(nodesIds, uint64(id))
	}
	nodesName := data_source.NodeNames(nodesIds)
	return strings.Join(nodesName, ",")
}

// taskTypeExport 函数根据任务类型、数据源ID和导出类型导出数据到Excel文件中，并返回更新后的Excel文件对象、资产数量、漏洞数量以及错误信息。
//
// 参数：
// taskId - int类型，表示任务ID。
// sourceId - int类型，表示数据源ID。
// exportType - int类型，表示导出类型，1表示导出资产数据，2表示导出漏洞数据，3表示同时导出资产和漏洞数据。
// ef - *excelize.File类型，表示Excel文件对象。
//
// 返回值：
// *excelize.File - 更新后的Excel文件对象。
// int - 导出的资产数量。
// int - 导出的漏洞数量。
// error - 函数执行过程中可能出现的错误。
func taskTypeExport(taskId, sourceId, exportType int, ef *excelize.File) (*excelize.File, int, int, error) {
	var err error
	var assetCount, vulCount int
	switch exportType {
	case 1:
		ef, assetCount, err = getExportAssetOrVulList(taskId, sourceId, exportType, "资产数据", ef)
		if err != nil {
			return ef, 0, 0, err
		}
	case 2:
		ef, vulCount, err = getExportAssetOrVulList(taskId, sourceId, exportType, "漏洞数据", ef)
		if err != nil {
			return ef, 0, 0, err
		}

	case 3:
		ef, assetCount, err = getExportAssetOrVulList(taskId, sourceId, 1, "资产数据", ef)
		if err != nil {
			return ef, 0, 0, err
		}
		ef, vulCount, err = getExportAssetOrVulList(taskId, sourceId, 2, "漏洞数据", ef)
		if err != nil {
			return ef, 0, 0, err
		}
	}
	return ef, assetCount, vulCount, nil
}

// getExportAssetOrVulList 函数用于从Elasticsearch中导出资产或漏洞列表，并将其写入Excel文件中。
//
// 参数：
// taskId：int类型，表示任务的ID。
// sourceId：int类型，表示数据源ID。
// exportType：int类型，表示导出类型，1表示资产，2表示漏洞。
// sheetName：string类型，表示Excel文件中的工作表名称。
// ex：*excelize.File类型，表示Excel文件对象。
//
// 返回值：
// *excelize.File：更新后的Excel文件对象。
// int：导出的数据条数。
// error：函数执行过程中可能出现的错误。
func getExportAssetOrVulList(taskId, sourceId, exportType int, sheetName string, ex *excelize.File) (*excelize.File, int, error) {
	handler, err := getHandler(sourceId)
	if err != nil {
		return nil, 0, err
	}

	indexName, err := handler.GetIndexName(exportType, taskId)
	if err != nil {
		return nil, 0, err
	}

	query := handler.BuildQuery([]string{}, taskId)
	scroll := es.GetEsClient().Scroll().Index(indexName).Query(query).Size(1000).Scroll("1m")
	defer scroll.Clear(context.Background())

	headers := handler.Headers(exportType)
	var data [][]interface{}
	totalFetched, maxResults := 0, 10000

	startTime := time.Now()

	for {
		result, err := scroll.Do(context.TODO())
		if errors.Is(err, io.EOF) || len(result.Hits.Hits) == 0 {
			break
		}
		if err != nil {
			return nil, 0, err
		}

		for _, hit := range result.Hits.Hits {
			rowData, err := handler.ParseData(hit, exportType)
			if err != nil {
				return nil, 0, err
			}
			data = append(data, rowData)
			totalFetched++
			if totalFetched >= maxResults {
				break
			}
		}

		if totalFetched >= maxResults {
			break
		}
		//scroll = es.GetEsClient().Scroll().ScrollId(result.ScrollId).Scroll("1m")
	}
	fmt.Println("整理 es 数据耗时：", time.Since(startTime).Seconds(), "秒")

	fmt.Println("表头：", headers)
	fmt.Println("数据：", data)
	to, err := utils.NewSheetWriterExcel(ex, sheetName, headers, data)
	if err != nil {
		return nil, 0, err
	}

	return to, len(data), nil
}

// getStatusName 根据给定的状态码返回对应的状态名称
//
// 参数:
//
//	status: int类型，表示任务的状态码
//
// 返回值:
//
//	string类型，表示对应的状态名称。如果状态码无效，返回空字符串。
func getStatusName(status int) string {
	switch status {
	case 1:
		return "等待执行"
	case 2:
		return "扫描中"
	case 3:
		return "暂停"
	case 4:
		return "扫描完成"
	case 5:
		return "扫描失败"
	default:
		return ""
	}
}

func formatToMinutesAndSeconds(useSeconds string) (string, error) {
	if len(useSeconds) == 0 {
		return "", nil
	}
	num, err := strconv.ParseFloat(useSeconds, 64)
	if err != nil {
		logger.Errorf("parse float error: %s", err.Error())
		return "", err
	}
	// 将秒数转换为分钟和秒
	minutes := int(num) / 60                         // 总秒数除以60取整，得到分钟数
	seconds := math.Round(num) - float64(minutes*60) // 剩余的秒数

	// 格式化输出
	return fmt.Sprintf("%d分钟%d秒", minutes, int(seconds)), nil
}

func taskTypeName(num int) string {
	switch num {
	case 1:
		return "资产扫描"
	case 2:
		return "漏洞扫描"
	case 3:
		return "资产及漏洞扫描"
	default:
		return ""
	}
}

func scanPlaneName(num int) string {
	switch num {
	case 1:
		return "立即执行"
	case 2:
		return "仅执行一次"
	case 3:
		return "每天"
	case 4:
		return "每周"
	case 5:
		return "每月"
	default:
		return ""
	}
}

func scanTypeName(scanType string) string {
	switch scanType {
	case "quick":
		return "快速扫描"
	case "common":
		return "深度扫描"
	case "ping":
		return "存活扫描"
	default:
		return ""
	}
}

func portOrNames() map[int]string {
	return map[int]string{
		1:  "网络精简端口",
		2:  "知名端口",
		3:  "常用端口TOP50",
		4:  "数据库端口",
		5:  "企业端口",
		6:  "工控端口",
		7:  "全部常用端口",
		8:  "视频网专用端口组",
		9:  "公安网专用端口组",
		10: "运维端口",
		11: "0-65535",
		12: "现有端口组",
		13: "全部预置端口组",
		15: "存活专用端口",
	}
}
