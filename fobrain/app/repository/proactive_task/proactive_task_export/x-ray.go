package proactive_task_export

import (
	"encoding/json"

	"github.com/olivere/elastic/v7"

	xray "fobrain/models/elastic/source/x-ray"
	"fobrain/models/mysql/data_source"
	"fobrain/pkg/utils"
)

func init() {
	DataSourceConfig[data_source.XRaySourceId] = &XRayHandler{}
}

// XRayHandler D01 数据源的实现
type XRayHandler struct{}

func (d *XRayHandler) Headers(exportType int) []string {
	return []string{"IP地址", "漏洞名称", "CVE编号", "漏洞类型", "漏洞等级", "漏洞地址", "发现时间", "详情", "修复建议"}
}

func (d *XRayHandler) GetIndexName(exportType, taskId int) (string, error) {
	return xray.NewFinishedThreatsModel().IndexName(), nil
}

func (d *XRayHandler) BuildQuery(ids []string, taskId int) *elastic.BoolQuery {
	return buildQuery(ids, taskId)
}

func (d *XRayHandler) ParseData(hit *elastic.SearchHit, exportType int) ([]interface{}, error) {
	return d.parseThreatData(hit)
}

// 解析漏洞数据
func (d *XRayHandler) parseThreatData(hit *elastic.SearchHit) ([]interface{}, error) {
	var threat struct {
		UID         string `json:"uid"`
		Title       string `json:"title"`
		CreateTime  string `json:"created_at"`
		Level       int    `json:"level"`
		VulType     string `json:"vulType"`
		IP          string `json:"ip"`
		CveID       string `json:"cveId"`
		URL         string `json:"url"`
		Details     string `json:"details"`
		Suggestions string `json:"suggestions"`
	}

	if err := json.Unmarshal(hit.Source, &threat); err != nil {
		return nil, err
	}
	return []interface{}{
		threat.IP,
		threat.Title,
		threat.CveID,
		threat.VulType,
		convertLevel(threat.Level),
		threat.URL,
		threat.CreateTime,
		threat.Details,
		utils.StripHTML(threat.Suggestions),
	}, nil
}
