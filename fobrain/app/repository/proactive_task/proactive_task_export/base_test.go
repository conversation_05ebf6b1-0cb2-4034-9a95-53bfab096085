package proactive_task_export

import (
	"context"
	"encoding/json"
	"io"
	"os"
	"reflect"
	"testing"
	"time"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/olivere/elastic/v7"
	"github.com/stretchr/testify/assert"

	"fobrain/fobrain/common/localtime"
	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/proactive_task_node_relations"
	"fobrain/models/mysql/task"
)

func TestExportTaskBulletinAsset(t *testing.T) {
	detail := &task.ProactiveTasks{
		Name:             "名称",
		Desc:             "名称",
		TaskType:         1,
		SourceId:         1,
		SourceName:       "foeye",
		Type:             "aa",
		SupportVulData:   true,
		SupportAssetData: true,
		ScanPlan:         1,
		ScanPeriod:       "",
		ScanTime:         "",
		Status:           4,
		Progress:         100,
		RepeatEndTime:    localtime.Time{},
		BeginTime:        localtime.Time{},
		EndTime:          localtime.Time{},
		ScanMode:         2,
		UseSeconds:       "128",
		ScanResult:       "success",
		ScanIPRangeType:  "user_input",
		ScanIPRanges:     []string{"127.0.0.1", "*********"},
		ScanIPRangesRaw:  "127.0.0.1",
		PocScanType:      "all",
		SelectedPocs:     "",
		ScanPort:         7,
		ScanType:         "common",
		Bandwidth:        500,
		Concurrency:      100,
		OtherCfgs:        nil,
		OtherCfgsRaw:     "",
		UserId:           1,
		UserName:         "管理员",
		IsStart:          0,
		SyncStatus:       0,
		PortsNum:         0,
		RuleInfosNum:     0,
		CompaniesNum:     0,
		NodeIds:          nil,
		TaskPlan:         "",
	}
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyMethodReturn(task.NewProactiveTasks(), "GetTaskDetail", detail, nil).Reset()

	// 构造模拟的真实数据
	mockData := `{"uid":"***********","id":"970_***********","ip":"***********","ipv6_raw":"","state":1,"os":"Linux-操作系统","asset_level":"","port_list":[{"protocol":"redis","port":6379,"rule_info":[{"icon":"Linux-操作系统","name":"Linux-操作系统","description":"操作系统|系统软件","is_threat":false,"is_xc":0},{"icon":"redis","name":"redis","description":"数据库系统|系统软件","is_threat":true,"is_xc":0}],"banner":"","title":"","host":"","certs":"","cert_string":"","website_image":"","link_url":""},{"protocol":"elastic","port":9200,"rule_info":[{"icon":"Elasticsearch","name":"Elasticsearch","description":"数据库系统|系统软件","is_threat":true,"is_xc":0},{"icon":"Log4j2","name":"Log4j2","description":"组件|支撑系统","is_threat":false,"is_xc":0}],"banner":"","title":"","host":"","certs":"","cert_string":"","website_image":"","link_url":""},{"protocol":"mysql","port":3306,"rule_info":[{"icon":"Oracle-MySQL","name":"Oracle-MySQL","description":"数据库系统|系统软件","is_threat":false,"is_xc":0}],"banner":"","title":"","host":"","certs":"","cert_string":"","website_image":"","link_url":""},{"protocol":"ssh","port":22,"rule_info":[{"icon":"OpenSSH","name":"OpenSSH","description":"其他支撑系统|支撑系统","is_threat":false,"is_xc":0}],"banner":"","title":"","host":"","certs":"","cert_string":"","website_image":"","link_url":""},{"protocol":"docker","port":2375,"rule_info":[{"icon":"Linux-操作系统","name":"Linux-操作系统","description":"操作系统|系统软件","is_threat":false,"is_xc":0}],"banner":"","title":"","host":"","certs":"","cert_string":"","website_image":"","link_url":""},{"protocol":"http","port":8000,"rule_info":[{"icon":"JSDELIVR","name":"JSDELIVR","description":"服务|支撑系统","is_threat":false,"is_xc":0}],"banner":"","title":"","host":"***********:8000","certs":"","cert_string":"","website_image":"","link_url":"http://***********:8000"},{"protocol":"http","port":9001,"rule_info":[{"icon":"MinIO-Console","name":"MinIO-Console","description":"其他企业应用|企业应用","is_threat":false,"is_xc":0}],"banner":"","title":"MinIO Console","host":"***********:9001","certs":"","cert_string":"","website_image":"","link_url":"http://***********:9001"},{"protocol":"http","port":9000,"rule_info":[{"icon":"amazon-云服务器","name":"amazon-云服务器","description":"其他支撑系统|支撑系统","is_threat":false,"is_xc":0}],"banner":"","title":"","host":"***********:9000","certs":"","cert_string":"","website_image":"","link_url":"http://***********:9000"},{"protocol":"http","port":80,"rule_info":[{"icon":"NGINX","name":"NGINX","description":"服务|支撑系统","is_threat":false,"is_xc":0}],"banner":"","title":"资产主动探测识别系统","host":"***********","certs":"","cert_string":"","website_image":"","link_url":"http://***********:80"},{"protocol":"etcd","port":2379,"rule_info":null,"banner":"","title":"","host":"","certs":"","cert_string":"","website_image":"","link_url":""},{"protocol":"portmap-udp","port":111,"rule_info":null,"banner":"","title":"","host":"","certs":"","cert_string":"","website_image":"","link_url":""},{"protocol":"unknown","port":2181,"rule_info":null,"banner":"","title":"","host":"","certs":"","cert_string":"","website_image":"","link_url":""}],"domain":null,"hostname":"","website_image":"","website_title":"MinIO Console","rdp_image":"","createtime":"2024-11-18 20:20:55","lastupdatetime":"2024-11-18 20:21:14","mac":"","is_ipv6":false,"hosts":null,"name":"","city":"","business_app":"","username":"","computer_room":"","company":"","manager_email":"","manager_mobile":"","custom_fields":null,"threat_status":null,"is_honeypot":false,"is_fraud":false,"is_xc":0,"node_id":29,"area_id":1,"proactive_task_id":49}` // 截断部分数据
	var mockResponse map[string]interface{}
	err := json.Unmarshal([]byte(mockData), &mockResponse)
	assert.NoError(t, err)

	// 模拟 BuildQuery 返回值
	mockScroll1 := &elastic.SearchResult{
		Hits: &elastic.SearchHits{
			TotalHits: &elastic.TotalHits{
				Value: 1,
			},
			Hits: []*elastic.SearchHit{
				{
					Source: json.RawMessage(mockData),
				},
			},
		},
	}
	// 第二轮返回空数据
	mockScroll2 := &elastic.SearchResult{
		Hits: &elastic.SearchHits{
			TotalHits: &elastic.TotalHits{
				Value: 0,
			},
			Hits: []*elastic.SearchHit{},
		},
	}
	// 模拟调用次数
	callCount := 0
	// 使用 gomonkey 模拟 `scroll.Do` 方法
	patches := gomonkey.ApplyMethod(reflect.TypeOf(&elastic.ScrollService{}), "Do", func(_ *elastic.ScrollService, ctx context.Context) (*elastic.SearchResult, error) {
		if callCount == 0 {
			callCount++
			return mockScroll1, nil // 第一次返回有效数据
		} else if callCount == 1 {
			callCount++
			return mockScroll2, io.EOF // 第二次返回 EOF，模拟循环结束
		}
		return nil, io.EOF
	})
	defer patches.Reset()
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyFuncReturn(proactive_task_node_relations.GetNodeIds, []int{1}).Reset()
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyFuncReturn(data_source.NodeNames, []string{"节点名称"}).Reset()

	filePath, err := ExportTaskBulletin(48)
	assert.NoError(t, err)
	assert.NotEmpty(t, filePath)
	_ = os.Remove("./" + filePath)
}

func TestExportTaskBulletinVul(t *testing.T) {
	detail := &task.ProactiveTasks{
		Name:             "漏洞名称",
		Desc:             "漏洞名称",
		TaskType:         2,
		SourceId:         1,
		SourceName:       "foeye",
		Type:             "aa",
		SupportVulData:   true,
		SupportAssetData: true,
		ScanPlan:         1,
		ScanPeriod:       "",
		ScanTime:         "",
		Status:           4,
		Progress:         100,
		RepeatEndTime:    localtime.Time{},
		BeginTime:        localtime.Time{},
		EndTime:          localtime.Time{},
		ScanMode:         2,
		UseSeconds:       "128",
		ScanResult:       "success",
		ScanIPRangeType:  "user_input",
		ScanIPRanges:     []string{"127.0.0.1", "*********"},
		ScanIPRangesRaw:  "127.0.0.1",
		PocScanType:      "all",
		SelectedPocs:     "",
		ScanPort:         7,
		ScanType:         "common",
		Bandwidth:        500,
		Concurrency:      100,
		OtherCfgs: map[string]interface{}{
			"task_timeout":           float64(180),
			"ceiling":                float64(1000),
			"retry_num":              float64(1),
			"max_redirect_num":       float64(5),
			"http_connect_timeout":   float64(3000),
			"http_response_timeout":  float64(10000),
			"max_connect_num":        float64(50),
			"max_page_size":          float64(5120),
			"max_request_per_second": float64(500),
			"scan_type_name":         "x-ray",
		},
		OtherCfgsRaw: "",
		UserId:       1,
		UserName:     "管理员",
		IsStart:      0,
		SyncStatus:   0,
		PortsNum:     0,
		RuleInfosNum: 0,
		CompaniesNum: 0,
		NodeIds:      nil,
		TaskPlan:     "",
	}
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyMethodReturn(task.NewProactiveTasks(), "GetTaskDetail", detail, nil).Reset()

	// 构造模拟的真实数据
	mockData := `{"uid":"***********","id":"12d45ba8e8f095cfe4d362e8bba982db","common_title":"Redis 未授权访问漏洞","createtime":"2023-12-28 21:20:36","lastupdatetime":"2024-11-18 20:22:36","level":4,"vulType":"未授权访问","ip":"***********","cveId":"","business_app":"","manager_mobile":"","mac":"","url":"***********:6379","hostinfo":"http://***********:6379","computer_room":"","province":"","has_exp":1,"name":"","notice_time":"","company":"","manager_email":"","last_response":"$5030\r\n# Server\r\nredis_version:7.0.15\r\nredis_git_sha1:00000000\r\nredis_git_dirty:0\r\nredis_build_id:b8bac26a1c037198\r\nredis_mode:standalone\r\nos:Linux 3.10.0-1160.95.1.el7.x86_64 x86_64\r\narch_bits:64\r\nmonotonic_clock:POSIX clock_gettime\r\nmultiplexing_api:epoll\r\natomicvar_api:c11-builtin\r\ngcc_version:12.2.0\r\nprocess_id:1\r\nprocess_supervised:no\r\nrun_id:97619df546369a41a8b7af3fa694574c8e4574c5\r\ntcp_port:6379\r\nserver_time_usec:1731933623672069\r\nuptime_in_seconds:5279884\r\nuptime_in_days:61\r\nhz:10\r\nconfigured_hz:10\r\nlru_clock:3880375\r\nexecutable:/data/redis-server\r\nconfig_file:\r\nio_threads_active:0\r\n\r\n# Clients\r\nconnected_clients:11\r\ncluster_connections:0\r\nmaxclients:10000\r\nclient_recent_max_input_buffer:8\r\nclient_recent_max_output_buffer:0\r\nblocked_clients:0\r\ntracking_clients:0\r\nclients_in_timeout_table:0\r\n\r\n# Memory\r\nused_memory:1283872\r\nused_memory_human:1.22M\r\nused_memory_rss:4145152\r\nused_memory_rss_human:3.95M\r\nused_memory_peak:1598400\r\nused_memory_peak_human:1.52M\r\nused_memory_peak_perc:80.32%\r\nused_memory_over","username":"","common_description":"<p>Redis是美国Redis Labs公司赞助的一套开源的使用ANSI C编写、支持网络、可基于内存亦可持久化的日志型、键值（Key-Value）存储数据库，并提供多种语言的API。<br></p><p>Redis中存在未授权访问漏洞，该漏洞源于程序在默认配置下会绑定在6379端口，这导致其直接暴露在公网中，可以接受来自任何地址发来的请求。当程序没有开启认证选项端口对外开放时，攻击者可借助目标服务器访问权限利用该漏洞未授权访问Redis并读取Redis的数据，在服务器上写入公钥，进而使用对应的私钥直接登录目标服务器。<br></p>","common_impact":"<p>Redis中存在未授权访问漏洞，该漏洞源于程序在默认配置下会绑定在6379端口，这导致其直接暴露在公网中，可以接受来自任何地址发来的请求。当程序没有开启认证选项端口对外开放时，攻击者可借助目标服务器访问权限利用该漏洞未授权访问Redis并读取Redis的数据，在服务器上写入公钥，进而使用对应的私钥直接登录目标服务器。<br></p>","recommandation":"<p>1、禁止Redis服务向公网开放：在redis.conf文件# bind 127.0.0.1前面找到#删除，然后保存。2、设置访问密码：在redis.conf查找requirepass字段中，删除其注释，并在后面填写密码。<br></p>","custom_fields":null,"verity":true,"city":"","state":1,"asset_level":"","is_xc":0,"port":6379,"node_id":29,"area_id":1,"proactive_task_id":49}`
	var mockResponse map[string]interface{}
	err := json.Unmarshal([]byte(mockData), &mockResponse)
	assert.NoError(t, err)

	// 模拟 BuildQuery 返回值
	mockScroll1 := &elastic.SearchResult{
		Hits: &elastic.SearchHits{
			TotalHits: &elastic.TotalHits{
				Value: 1,
			},
			Hits: []*elastic.SearchHit{
				{
					Source: json.RawMessage(mockData),
				},
			},
		},
	}
	// 第二轮返回空数据
	mockScroll2 := &elastic.SearchResult{
		Hits: &elastic.SearchHits{
			TotalHits: &elastic.TotalHits{
				Value: 0,
			},
			Hits: []*elastic.SearchHit{},
		},
	}
	// 模拟调用次数
	callCount := 0
	// 使用 gomonkey 模拟 `scroll.Do` 方法
	patches := gomonkey.ApplyMethod(reflect.TypeOf(&elastic.ScrollService{}), "Do", func(_ *elastic.ScrollService, ctx context.Context) (*elastic.SearchResult, error) {
		if callCount == 0 {
			callCount++
			return mockScroll1, nil // 第一次返回有效数据
		} else if callCount == 1 {
			callCount++
			return mockScroll2, io.EOF // 第二次返回 EOF，模拟循环结束
		}
		return nil, io.EOF
	})
	defer patches.Reset()
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyFuncReturn(proactive_task_node_relations.GetNodeIds, []int{1}).Reset()
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyFuncReturn(data_source.NodeNames, []string{"节点名称"}).Reset()

	filePath, err := ExportTaskBulletin(48)
	assert.NoError(t, err)
	assert.NotEmpty(t, filePath)
	_ = os.Remove("./" + filePath)
}

func TestFormatToMinutesAndSeconds(t *testing.T) {
	s, err := formatToMinutesAndSeconds("376.2")
	assert.Nil(t, err)
	assert.Equal(t, "6分钟16秒", s)
}

// TestConvertLevel 测试 convertLevel 函数
func TestConvertLevel(t *testing.T) {
	tests := []struct {
		name     string
		level    int
		expected string
	}{
		{
			name:     "低危等级",
			level:    1,
			expected: "低危",
		},
		{
			name:     "中危等级",
			level:    2,
			expected: "中危",
		},
		{
			name:     "高危等级",
			level:    3,
			expected: "高危",
		},
		{
			name:     "严重等级",
			level:    4,
			expected: "严重",
		},
		{
			name:     "未知等级-0",
			level:    0,
			expected: "未知",
		},
		{
			name:     "未知等级-5",
			level:    5,
			expected: "未知",
		},
		{
			name:     "未知等级-负数",
			level:    -1,
			expected: "未知",
		},
		{
			name:     "未知等级-大数",
			level:    999,
			expected: "未知",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := convertLevel(tt.level)
			assert.Equal(t, tt.expected, result, "convertLevel函数返回值不符合预期")
		})
	}
}

// TestGetStatusName 测试 getStatusName 函数
func TestGetStatusName(t *testing.T) {
	tests := []struct {
		name     string
		status   int
		expected string
	}{
		{
			name:     "等待执行状态",
			status:   1,
			expected: "等待执行",
		},
		{
			name:     "扫描中状态",
			status:   2,
			expected: "扫描中",
		},
		{
			name:     "暂停状态",
			status:   3,
			expected: "暂停",
		},
		{
			name:     "扫描完成状态",
			status:   4,
			expected: "扫描完成",
		},
		{
			name:     "扫描失败状态",
			status:   5,
			expected: "扫描失败",
		},
		{
			name:     "未知状态-0",
			status:   0,
			expected: "",
		},
		{
			name:     "未知状态-6",
			status:   6,
			expected: "",
		},
		{
			name:     "未知状态-负数",
			status:   -1,
			expected: "",
		},
		{
			name:     "未知状态-大数",
			status:   999,
			expected: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := getStatusName(tt.status)
			assert.Equal(t, tt.expected, result, "getStatusName函数返回值不符合预期")
		})
	}
}

// TestSaveToFile 测试 saveToFile 函数
func TestSaveToFile(t *testing.T) {
	tests := []struct {
		name        string
		filePath    string
		headers     []string
		data        [][]interface{}
		expectError bool
	}{
		{
			name:     "正常保存文件",
			filePath: "test_output.xlsx",
			headers:  []string{"列1", "列2", "列3"},
			data: [][]interface{}{
				{"数据1", "数据2", "数据3"},
				{"数据4", "数据5", "数据6"},
			},
			expectError: false,
		},
		{
			name:     "空数据保存",
			filePath: "test_empty.xlsx",
			headers:  []string{"列1", "列2"},
			data:     [][]interface{}{},
			expectError: false,
		},
		{
			name:     "单行数据",
			filePath: "test_single.xlsx",
			headers:  []string{"列1"},
			data: [][]interface{}{
				{"单个数据"},
			},
			expectError: false,
		},
		{
			name:        "无效文件路径",
			filePath:    "/invalid/path/test.xlsx",
			headers:     []string{"列1"},
			data:        [][]interface{}{{"数据"}},
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := saveToFile(tt.filePath, tt.headers, tt.data)

			if tt.expectError {
				assert.Error(t, err, "应该返回错误")
				assert.Empty(t, result, "错误情况下结果应该为空")
			} else {
				assert.NoError(t, err, "不应该返回错误")
				assert.Equal(t, tt.filePath, result, "返回的文件路径应该与输入一致")

				// 清理生成的文件
				defer func() {
					if _, statErr := os.Stat(tt.filePath); statErr == nil {
						os.Remove(tt.filePath)
					}
				}()
			}
		})
	}
}
