package proactive_task_export

import (
	"encoding/json"
	"fmt"
	"fobrain/models/elastic/source/foeye"
	"strings"

	"github.com/olivere/elastic/v7"

	"fobrain/models/elastic/source/d01"
	"fobrain/models/mysql/data_source"
	"fobrain/pkg/utils"
)

func init() {
	DataSourceConfig[data_source.D01SourceId] = &D01Handler{}
}

// D01Handler D01 数据源的实现
type D01Handler struct{}

func (d *D01Handler) Headers(exportType int) []string {
	switch exportType {
	case TypeAssetData:
		return []string{"IP地址", "端口", "协议", "组件", "域名", "操作系统", "MAC地址"}
	case TypeThreatData:
		return []string{"IP地址", "漏洞名称", "CVE编号", "漏洞类型", "漏洞等级", "漏洞地址", "发现时间", "通报时间", "详情", "修复建议"}
	}

	return []string{}
}

func (d *D01Handler) GetIndexName(exportType, taskId int) (string, error) {
	version := foeye.QueryProactiveTaskVersion(taskId)
	if version == "v2" {
		if exportType == TypeAssetData {
			return d01.D01V2FinishedAssets, nil
		}
		return d01.D01V2FinishedThreats, nil
	}

	if exportType == TypeAssetData {
		return d01.NewFinishedAssetsModel().IndexName(), nil
	}
	return d01.NewFinishedThreatsModel().IndexName(), nil
}

func (d *D01Handler) BuildQuery(ids []string, taskId int) *elastic.BoolQuery {
	return buildQuery(ids, taskId)
}

func (d *D01Handler) ParseData(hit *elastic.SearchHit, exportType int) ([]interface{}, error) {
	if exportType == TypeAssetData {
		return d.parseAssetData(hit)
	}
	return d.parseThreatData(hit)
}

// 解析资产数据
func (d *D01Handler) parseAssetData(hit *elastic.SearchHit) ([]interface{}, error) {
	var asset Asset
	if err := json.Unmarshal(hit.Source, &asset); err != nil {
		return nil, err
	}

	// 初始化返回的数据
	var data []interface{}
	var portInfo [][]string

	ruleInfos := make(map[int][]string)
	if asset.RuleInfos != nil {
		for _, rule := range asset.RuleInfos {
			if rule.Ports != nil {
				for _, port := range rule.Ports {
					if port > 0 && rule.Title != "" {
						ruleInfos[port] = append(ruleInfos[port], rule.Title)
					}
				}
			}
		}
	}

	// 遍历 PortList，构建端口、协议和描述信息
	for _, port := range asset.PortList {
		// 使用 strings.Join 拼接所有 rule.Name，用逗号分隔
		var ruleNames []string
		for _, rule := range port.RuleInfo {
			ruleNames = append(ruleNames, rule.Name)
		}
		joinedRuleNames := strings.Join(ruleNames, ",") // 将所有 Name 字段拼接为一个逗号分隔的字符串
		if joinedRuleNames == "" {
			joinedRuleNames = strings.Join(ruleInfos[port.Port], ",")
		}
		// 将端口、协议和描述拼接到 portInfo 中
		portData := []string{
			fmt.Sprint(port.Port), // 端口
			port.Protocol,         // 协议
			joinedRuleNames,       // 拼接后的组件描述
		}
		portInfo = append(portInfo, portData)
	}

	// 添加到最终的数据结构
	data = append(data,
		asset.Ip,     // IP
		portInfo,     // 端口和协议信息的二维数组
		asset.Domain, // 域名
		asset.Os,     // 操作系统
		asset.Mac,    // MAC 地址
	)

	return data, nil
}

// 解析漏洞数据
func (d *D01Handler) parseThreatData(hit *elastic.SearchHit) ([]interface{}, error) {
	var threat Threat
	if err := json.Unmarshal(hit.Source, &threat); err != nil {
		return nil, err
	}
	return []interface{}{
		threat.IP,
		threat.CommonTitle,
		threat.CveID,
		threat.VulType,
		convertLevel(threat.Level),
		threat.URL,
		threat.CreateTime,
		threat.LastUpdateTime,
		threat.CommonDescription,
		utils.StripHTML(threat.Recommandation),
	}, nil
}
