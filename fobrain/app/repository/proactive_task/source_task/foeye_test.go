package source_task

import (
	"testing"

	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/models/mysql/data_source"
	task2 "fobrain/models/mysql/task"
)

func TestFoeye_ImmediateTask(t *testing.T) {
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()
	mockDb.ExpectQuery("SELECT `node_id` FROM `proactive_task_node_relations` WHERE task_id = ?").
		WillReturnRows(mockDb.NewRows([]string{"node_id"}).AddRow(1))

	mockDb.ExpectQuery("SELECT `node_id` FROM `proactive_task_node_relations` WHERE task_id = ?").
		WillReturnRows(mockDb.NewRows([]string{"node_id"}).AddRow(1))

	mockDb.ExpectQuery("SELECT * FROM `data_node_configs` WHERE `node_id` = ?").WithArgs(int64(2)).
		WillReturnRows(mockDb.NewRows([]string{"id", "title", "scan_plan", "status"}).AddRow(1, "test", 1, 1))

	mockDb.ExpectQuery("SELECT * FROM `data_node_configs` WHERE `node_id` = ?").WithArgs(int64(2)).
		WillReturnRows(mockDb.NewRows([]string{"id", "title", "scan_plan", "status"}).AddRow(1, "test", 1, 1))

	task := task2.ProactiveTasks{SourceId: data_source.FoeyeSourceId, Name: "test", Status: -1}
	task.Id = 1
	New(&task).ImmediateTask()
}

func TestStartTask(t *testing.T) {

	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()
	mockDb.ExpectQuery("SELECT id, node_id, node_task_id FROM `proactive_task_node_relations` WHERE task_id = ?").WithArgs(int64(1)).
		WillReturnRows(mockDb.NewRows([]string{"id", "node_id", "node_task_id"}).AddRow(1, 2, 3))

	mockDb.ExpectQuery("SELECT * FROM `data_node_configs` WHERE `node_id` = ?").WithArgs(int64(2)).
		WillReturnRows(mockDb.NewRows([]string{"id", "node_id", "key", "value"}).AddRow(1, 1, "scan_mode", 1))

	task := task2.ProactiveTasks{SourceId: data_source.FoeyeSourceId, Name: "test", Status: -1}
	task.Id = 1
	New(&task).StartTask()
}
