package source_task

import (
	"encoding/json"
	"fmt"
	"fobrain/pkg/filter_scan_ips"

	"fobrain/fobrain/app/services/node/x_ray"
	xray2 "fobrain/fobrain/app/services/sync/x_ray"
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/proactive_task_node_relations"
	"fobrain/models/mysql/task"
)

func init() {
	SourceConfig[data_source.XRaySourceId] = func(task *task.ProactiveTasks) SourceTask {
		return &XRay{Base: &Base{Task: task}}
	}
}

type XRay struct {
	*Base
}

const (
	XrayScanWebThreat = "basic_web" // X-ray Web 漏洞扫描
)

// TaskSetting
// @Summary 任务设置
func (x *XRay) TaskSetting(plugins any) map[string]any {
	if x.Task.ScanType == XrayScanWebThreat {
		return map[string]any{
			"taskRestriction":                x.TaskRestriction(),
			"infoCollection":                 x.InfoCollection(),
			"customFingerprintPluginSetting": x.CustomFingerprintPluginSetting(),
			"vulnScan":                       x.vulnScan(),
			"pluginSetting":                  plugins,
			"pluginConfigDetail": map[string]any{
				"bruteForceSetting": map[string]any{
					"enabled": false,
				},
				"dirScanDictionary": map[string]any{
					"enabled": false,
				},
			},
		}
	}

	return map[string]any{
		"taskRestriction": x.TaskRestriction(),
		"infoCollection":  x.InfoCollection(),
		"HTTPRequest": map[string]any{
			"userAgent": "Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/71.0.3578.98 Safari/537.36",
			"cookies":   []string{},
			"headers":   []string{},
			"proxy":     "",
		},
		"blindReversePlatform": map[string]any{"enabled": true}, // 可选开启，使用内置盲打平台或手动指定
		"extraOptions": map[string]any{ // 其他的探测选项
			"scanDepth":              2,     // 设置漏洞插件对web目标资产所能探测到的路径深度，默认为2，最大可设置为10
			"maxParallel":            30,    // 最大并发插件数，默认为30，最大可设置为100
			"openFingerBindingCheck": false, // 智能插件调用
		},
		"customFingerprintPluginSetting": x.CustomFingerprintPluginSetting(),
		// "vulnScan":                       x.vulnScan(),
		"pluginSetting": plugins,
		"pluginConfigDetail": map[string]any{
			"bruteForceSetting": map[string]any{
				"enabled": false,
			},
			"dirScanDictionary": map[string]any{
				"enabled": false,
			},
		},
	}
}

func (x *XRay) vulnScan() map[string]any {
	return map[string]any{
		"HTTPRequest": map[string]any{
			"userAgent": "Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/71.0.3578.98 Safari/537.36",
			"cookies":   []string{},
			"headers":   []string{},
			"proxy":     "",
		},
		"urlDeduplication": map[string]any{
			"timeWindow":    2,     // 将在设置时间间隔内不对同一目标进行重复检测
			"caseSensitive": true,  // 是否保留URL中Query部分（？和#间的部分）
			"keepHash":      false, // 是否保留URL中Hash部分（#后的部分）
			"keepQuery":     false, // 大小写是否敏感
			"rewrite":       true,  // 智能识别 URL 重写
		},
		"blindReversePlatform": map[string]any{"enabled": true}, // 可选开启，使用内置盲打平台或手动指定
		"extraOptions": map[string]any{ // 其他的探测选项
			"scanDepth":              2,     // 设置漏洞插件对web目标资产所能探测到的路径深度，默认为2，最大可设置为10
			"maxParallel":            30,    // 最大并发插件数，默认为30，最大可设置为100
			"openFingerBindingCheck": false, // 智能插件调用
		},
	}
}

// InfoCollection
// @Summary 信息收集相关参数
func (x *XRay) InfoCollection() map[string]any {
	if x.Task.ScanType == XrayScanWebThreat {
		return map[string]any{
			"HTTPRequest": map[string]any{
				"userAgent": "Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/71.0.3578.98 Safari/537.36",
				"cookies":   []string{},
				"headers":   []string{},
				"proxy":     "",
			},
			"certSetting": map[string]any{
				"enabled": false,
			},
			"basicAuthSetting": map[string]any{
				"enabled": false,
			},
			"formAuthSetting": map[string]any{
				"enabled": false,
			},
			"websiteAvailableCheck": map[string]any{
				"enabled": false,
			},
			"crawlerSetting": map[string]any{
				"enabled": false,
			},
			"burpFileSetting": map[string]any{
				"enabled": false,
			},
			"pathbrutedictFileSetting": map[string]any{
				"enabled": false,
			},
		}
	}

	return map[string]any{
		"HTTPRequest": map[string]any{
			"userAgent": "Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/71.0.3578.98 Safari/537.36",
			"cookies":   []string{},
			"headers":   []string{},
			"proxy":     "",
		},
		"HostLoginSetting": map[string]any{
			"enabled": false,
		},
		"hostLiveDetection": map[string]any{
			"enabled":          true,
			"skipDownHost":     true,             // 跳过无响应主机
			"hostLiveScanType": []string{"ICMP"}, // 主机存活探测方式
			"ICMPConfig": map[string]any{
				"timeout":  1,   // 默认1 秒
				"minRetry": 3,   // 最小重试次数
				"maxRetry": 100, // 最大重试次数
			},
			"TCPSYNConfig": map[string]any{
				"timeout":  1,  // 默认1 秒
				"minRetry": 2,  // 最小重试次数
				"maxRetry": 14, // 最大重试次数
			},
			"TCPConnectConfig": map[string]any{
				"timeout":       4,   // 默认1 秒
				"minRetry":      2,   // 最小重试次数
				"maxRetry":      2,   // 最大重试次数
				"maxQPS":        0,   // 最大QPS
				"maxConcurrent": 100, // 最大并发
			},
			"UDPConfig": map[string]any{
				"timeout":  1,  // 默认1 秒
				"minRetry": 2,  // 最小重试次数
				"maxRetry": 14, // 最大重试次数
			},
		},
		"TCPPort": map[string]any{
			"enabled":           true,
			"enableFingerprint": false,
			"detectionLevel":    "MID", // FAST 快速, MID 常规, SLOW 精准
			"TCPPorts":          []string{"7", "9", "13", "21-23", "25-26", "37", "53", "79-81", "88", "106", "110-111", "113", "119", "135", "139", "143-144", "179", "199", "389", "427", "443-445", "465", "513-515", "543-544", "548", "554", "587", "631", "646", "873", "888", "990", "993", "995", "1025-1029", "1080", "1110", "1433", "1443", "1720", "1723", "1755", "1900", "2000-2001", "2049", "2121", "2181", "2717", "3000", "3128", "3306", "3389", "3986", "4899", "5000", "5009", "5051", "5060", "5101", "5190", "5357", "5432", "5631", "5666", "5800", "5900", "6000-6001", "6646", "7000-7005", "7070", "8000", "8008-8009", "8080-8081", "8443", "8888", "9999-10000", "11211", "32768", "49152-49157"},
			"scanType":          "SYN",
			"config": map[string]any{
				"timeout":  1,  // 默认1 秒
				"minRetry": 2,  // 最小重试次数
				"maxRetry": 14, // 最大重试次数
			},
		},
		"UDPPort": map[string]any{
			"enabled": false,
		},
		"other": map[string]any{
			"enableLocalNetworkOptimization": true,  // 如果在同一局域网中则使用ARP优化扫描请求
			"enableDnsReverse":               false, // 通过DNS反查获取IP对应的域名
		},
	}
}

func (x *XRay) CustomFingerprintPluginSetting() []any {
	return []any{}
}

func (x *XRay) TaskRestriction() map[string]any {
	otherCfgs := x.Task.OtherCfgs

	if x.Task.ScanType == XrayScanWebThreat {
		return map[string]any{
			"taskTimeout":         otherCfgs["task_timeout"],           // 任务最大运行时间 单位分钟。
			"maxConnectNum":       otherCfgs["max_connect_num"],        // 每个站点并发连接数
			"retryNum":            otherCfgs["retry_num"],              // 请求失败后的重试次数。
			"maxRedirectNum":      otherCfgs["max_redirect_num"],       // 最大重定向次数
			"httpConnectTimeout":  otherCfgs["http_connect_timeout"],   // HTTP 请求连接超时时间
			"httpResponseTimeout": otherCfgs["http_response_timeout"],  // // HTTP 请求响应超时时间
			"maxPageSize":         otherCfgs["max_page_size"],          // 页面大小限制
			"maxRequestPerSecond": otherCfgs["max_request_per_second"], // 最大请求每秒数
			"ceiling":             otherCfgs["ceiling"],                // 允许收集的最大链接数
			"updateAsset":         true,
			"createAsset":         true,
			"updateVuln":          true,
			"finishAutoReport":    map[string]any{"enabled": false},
			"email":               map[string]any{"enabled": false},
			"debug":               true,
			"white":               map[string]any{"enabled": false},
		}
	}

	return map[string]any{
		"taskTimeout":      otherCfgs["task_timeout"], // 任务最大运行时间 单位分钟。
		"maxParallel":      otherCfgs["max_parallel"], // 最大并发主机数
		"bandWidth":        x.Task.Bandwidth,          // 带宽限制
		"updateAsset":      true,
		"createAsset":      true,
		"updateVuln":       true,
		"finishAutoReport": map[string]any{"enabled": false},
		"email":            map[string]any{"enabled": false},
		"debug":            false,
		"white":            map[string]any{"enabled": false},
		"transformDead":    false,
	}
}

func (x *XRay) TaskParams(plugins any, cli *x_ray.XRay) []byte {
	projectId := cli.GetConfigToUint64("project_id")
	if projectId == 0 {
		projectId = 1
	}
	engineChoice := cli.GetConfigToSlice("engine_choice")
	if len(engineChoice) == 0 {
		engineChoice = []string{"00000000000000000000000000000001"}
	}
	param := map[string]interface{}{
		"project_id":       projectId,
		"active":           false,
		"task_template_id": int(x.Task.TaskType),
		"exec_right_now":   true,
		"task_setting":     x.TaskSetting(plugins),
		"basic_setting": map[string]interface{}{
			"taskName": x.Task.Name,
			"remark":   x.Task.Desc,
			"taskTarget": map[string]interface{}{
				"target":     x.Task.ScanIPRanges,
				"targetType": "MANUAL",
			},
			"target": []string{},
			"planSetting": map[string]interface{}{
				"enabled":  false,
				"planType": "NOW",
			},
			"engineChoice": engineChoice,
			"executionSetting": map[string]interface{}{
				"enabled": false,
			},
		},
	}

	jsonData, _ := json.Marshal(param)
	return jsonData
}

// SyncStatus
// @Summary 定时同步任务状态
func (x *XRay) SyncStatus() string {
	nodeTaskIds := proactive_task_node_relations.GetNodeTaskIds(int(x.Task.Id)) // 获取 当前任务对应的节点
	for _, nodeTask := range nodeTaskIds {
		cli, err := x.GetCli(uint64(nodeTask["node_id"]))
		if err != nil {
			return err.Error()
		}

		taskInfo, err := cli.GetTaskInfo(int64(nodeTask["node_task_id"]))
		if err != nil {
			return err.Error()
		}

		var progress float64
		switch taskInfo.Status {
		case "FINISHED":
			err = proactive_task_node_relations.UpdateNodeProgress(uint64(nodeTask["node_id"]), x.Task.Id, 100, proactive_task_node_relations.StateFin, taskInfo.EndTime)
			_ = task.NewProactiveTasks().UpdateEndTimeAndEstimateTime(taskInfo.EndTime, x.Task.Id) // 更新结束时间～
			if err != nil {
				return err.Error()
			}
		case "FAILED":
			err = proactive_task_node_relations.UpdateNodeProgress(uint64(nodeTask["node_id"]), x.Task.Id, 100, proactive_task_node_relations.StateAutFail, taskInfo.EndTime)
			_ = task.NewProactiveTasks().UpdateFiledStatus(taskInfo.EndTime, x.Task.Id, "节点未返回明确失败原因！") // 更新结束时间～ TODO 接口没做明确返回: 原始数据: {"id":76,"plan_id":68,"created_time":"2024-11-05T21:18:02.142812+08:00","start_time":"2024-11-05T21:18:07.502197+08:00","end_time":"2024-11-05T21:18:07.558386+08:00","status":"FAILED","xprocess":null}

			if err != nil {
				return err.Error()
			}
		case "PAUSED":
			err = proactive_task_node_relations.UpdateNodeState(int(x.Task.Id), uint64(nodeTask["node_id"]), nodeTask["node_task_id"], proactive_task_node_relations.StateStop)
			if err != nil {
				return err.Error()
			}
		case "WORKING":
			progress, _ = cli.GetProgress(int64(nodeTask["node_task_id"]))
			err = proactive_task_node_relations.UpdateNodeProgress(uint64(nodeTask["node_id"]), x.Task.Id, progress, int64(proactive_task_node_relations.StateRunning), taskInfo.StartTime)
			err = proactive_task_node_relations.UpdateBeginTime(taskInfo.StartTime, x.Task.Id)
			if err != nil {
				return err.Error()
			}
		}
	}

	// 子节点全部完成
	if proactive_task_node_relations.ObtainNodeTaskAllFinishedState(x.Task.Id) {
		_ = task.NewProactiveTasks().UpdateStateTask(x.Task.Id)
	} else if proactive_task_node_relations.ObtainNodeTaskAllScanningState(x.Task.Id) {
		_ = task.NewProactiveTasks().UpdateScanningProgress(x.Task.Id, proactive_task_node_relations.ObtainNodeTaskAllAvgProgress(x.Task.Id))
	} else {
		_ = task.NewProactiveTasks().UpdateProgress(x.Task.Id, proactive_task_node_relations.ObtainNodeTaskAllAvgProgress(x.Task.Id))
	}

	return "状态同步成功"
}

// ImmediateTask
// @Summary 立即执行任务
func (x *XRay) ImmediateTask() string {
	var err error
	defer func() {
		// 如果出现错误，将任务状态设置为失败
		if reErr := recover(); reErr != nil {
			x.Task.Status = 5
			x.Task.ScanResult = fmt.Sprintf("%v", reErr)
			task.NewProactiveTasks().Save(&x.Task)
		}
	}()

	nodeIds := proactive_task_node_relations.GetNodeIds(int(x.Task.Id))
	for _, nodeId := range nodeIds {
		cli := x_ray.New()
		err = cli.SetNode(uint64(nodeId))
		if err != nil {
			panic(err.Error())
		}

		config, err := data_source.NewNodeConfigModel().GetNodeConfig(uint64(nodeId))
		if err != nil {
			panic(err.Error())
		}

		err = cli.SetConfig(config)
		if err != nil {
			panic(err.Error())
		}

		plugins, err := cli.Plugin(int64(x.Task.TaskType))
		if err != nil {
			return ""
		}

		nodeTaskId, err := cli.Push(x.TaskParams(plugins, cli))
		if err != nil {
			panic(err.Error())
		}

		x.Task.Status = 1
		task.NewProactiveTasks().Save(&x.Task)
		err = proactive_task_node_relations.UpdateNodeTaskId(uint64(nodeId), x.Task.Id, nodeTaskId)
		if err != nil {
			panic(err.Error())
		}
	}

	return "立即执行任务"
}

// StartTask
// @Summary 开始执行任务
func (x *XRay) StartTask() string {
	// 获取节点任务 ID
	nodeTasks := proactive_task_node_relations.GetNodeTaskIds(int(x.Task.Id))
	for _, nodeTask := range nodeTasks {
		cli := x_ray.New()
		err := cli.SetNode(uint64(nodeTask["node_id"]))
		if err != nil {
			return err.Error()
		}

		config, err := data_source.NewNodeConfigModel().GetNodeConfig(uint64(nodeTask["node_id"]))
		if err != nil {
			return err.Error()
		}

		err = cli.SetConfig(config)
		if err != nil {
			return err.Error()
		}

		err = cli.StartTask(nodeTask["node_task_id"])
		if err != nil {
			return err.Error()
		}

		x.Task.Status = 2
		err = task.NewProactiveTasks().Save(&x.Task).Error
		if err != nil {
			return err.Error()
		}
	}

	return "开始成功！"
}

// PauseTask
// @Summary 暂停任务
func (x *XRay) PauseTask() string {
	// 获取节点任务 ID
	nodeTasks := proactive_task_node_relations.GetNodeTaskIds(int(x.Task.Id))
	for _, nodeTask := range nodeTasks {
		cli := x_ray.New()
		err := cli.SetNode(uint64(nodeTask["node_id"]))
		if err != nil {
			return err.Error()
		}

		config, err := data_source.NewNodeConfigModel().GetNodeConfig(uint64(nodeTask["node_id"]))
		if err != nil {
			return err.Error()
		}

		err = cli.SetConfig(config)
		if err != nil {
			return err.Error()
		}

		err = cli.StopTask(nodeTask["node_task_id"])
		if err != nil {
			return err.Error()
		}

		x.Task.Status = 3
		err = task.NewProactiveTasks().Save(&x.Task).Error
		if err != nil {
			return err.Error()
		}
	}

	return "暂停成功"
}

// SyncResult
// @Summary 同步任务结果
func (x *XRay) SyncResult(node *data_source.Node, taskInfo map[string]any) error {
	return xray2.NewSyncTask(node, taskInfo)
}

func (x *XRay) VerifyTaskParams(nodeIds []int) []error {
	failNodeErrs := make([]error, 0)
	// 校验协议是否合法 , 目前只有X-ray 的 Web漏洞扫描
	if x.Task.ScanType == XrayScanWebThreat {
		if err := filter_scan_ips.IsValidProtocol(x.Task.ScanIPRanges); err != nil {
			return append(failNodeErrs, err)
		}
	} else { // 检查ip，ip段，域名是否合法
		if err := filter_scan_ips.CheckIpFormat(x.Task.ScanIPRanges); err != nil {
			return append(failNodeErrs, err)
		}
	}
	return nil
}

// DelTask
// @Summary 删除任务
func (x *XRay) DelTask() string {

	nodeTasks := proactive_task_node_relations.GetNodeTaskIds(int(x.Task.Id))

	for _, nodeTask := range nodeTasks {
		cli := x_ray.New()
		err := cli.SetNode(uint64(nodeTask["node_id"]))

		config, err := data_source.NewNodeConfigModel().GetNodeConfig(uint64(nodeTask["node_id"]))
		if err != nil {
			return err.Error()
		}

		err = cli.SetConfig(config)
		if err != nil {
			return err.Error()
		}

		err = cli.DelNodeTask(nodeTask["node_task_id"])
		if err != nil {
			return err.Error()
		}

	}
	return "删除成功"
}

func (x *XRay) GetCli(nodeId uint64) (*x_ray.XRay, error) {
	cli := x_ray.New()
	err := cli.SetNode(nodeId)
	if err != nil {
		return nil, err
	}

	config, err := data_source.NewNodeConfigModel().GetNodeConfig(nodeId)
	if err != nil {
		return nil, err
	}

	err = cli.SetConfig(config)
	if err != nil {
		return nil, err
	}

	return cli, nil

}

func (x *XRay) ScanTypes(nodeId uint64) ([]map[string]any, error) {
	cli := x_ray.New()
	err := cli.SetNode(uint64(nodeId))
	if err != nil {
		return []map[string]any{}, err
	}

	all, err := cli.TemplateAll()
	if err != nil {
		return []map[string]any{}, err
	}

	return all, nil
}
