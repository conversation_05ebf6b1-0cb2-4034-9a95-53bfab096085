package source_task

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"fobrain/fobrain/common/localtime"
	"fobrain/fobrain/logs"
	"fobrain/initialize/es"
	"fobrain/initialize/mysql"
	"fobrain/initialize/redis"
	"fobrain/models/elastic/poc"
	"fobrain/models/elastic/source/nsfocus_rsas"
	"fobrain/models/elastic/staff"
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/data_sync_task"
	"fobrain/models/mysql/proactive_task_node_relations"
	"fobrain/models/mysql/task"
	"fobrain/pkg/filter_scan_ips"
	"fobrain/pkg/utils/handle_es_bulk_error"
	"runtime/debug"
	"strconv"
	"strings"
	"time"

	goRedis "github.com/go-redis/redis/v8"
	"github.com/google/uuid"
	"github.com/olivere/elastic/v7"

	pb "fobrain/mergeService/proto"

	"git.gobies.org/caasm/fobrain-components/dataSourceSdk/nsfocus/rsas"
	sdkframe "git.gobies.org/caasm/fobrain-components/dataSourceSdk/sdk_frame"
)

type Common struct {
	*Base
	Sdk                        sdkframe.ActiveScanSDK
	Node                       *data_source.Node
	NodeConfig                 map[string]any
	Source                     data_source.Source
	IndexTaskAssets            string
	IndexTaskThreats           string
	IndexTaskPeople            string
	IndexProcessAssets         string
	IndexProcessDevice         string
	IndexProcessThreats        string
	IndexProcessPeople         string
	proactiveTaskNodeRelations *proactive_task_node_relations.ProactiveTaskNodeRelations
}

func init() {
	SourceConfig[data_source.NSFocusRsasSourceId] = func(task *task.ProactiveTasks) SourceTask {
		return &Common{
			Base:                &Base{Task: task},
			Sdk:                 rsas.NewRSAS(),
			IndexTaskThreats:    nsfocus_rsas.NewNsfocusRsasTaskThreatsModel().IndexName(),
			IndexProcessThreats: poc.NewProcessPocModel().IndexName(),
		}
	}
}

// SyncStatus
// @Summary 定时同步任务状态
func (f *Common) SyncStatus() string {
	nodeTaskIds := proactive_task_node_relations.GetNodeTaskIds(int(f.Task.Id)) // 获取 当前任务对应的节点
	for _, nodeTask := range nodeTaskIds {
		config, err := data_source.NewNodeConfigModel().GetNodeConfig(uint64(nodeTask["node_id"]))
		if err != nil {
			return err.Error()
		}

		f.NodeConfig = config
		taskInfo, err := f.Sdk.GetTaskProgress(context.Background(), sdkframe.TaskID(nodeTask["node_task_id"]), f)
		if err != nil {
			return err.Error()
		}
		// 获取查询到 三方任务执行的 进度和状态
		progress := taskInfo.Progress
		state := taskInfo.State
		beginTime := taskInfo.BeginTime
		startAt := taskInfo.BeginTime
		endAt := taskInfo.EndTime

		if state == sdkframe.StateRunning { // 任务正在扫描，同步进度条
			err = proactive_task_node_relations.UpdateNodeProgress(uint64(nodeTask["node_id"]), f.Task.Id, progress, proactive_task_node_relations.StateRunning, startAt.Format("2006-01-02 15:04:05"))
			_ = proactive_task_node_relations.UpdateBeginTime(beginTime.Format("2006-01-02 15:04:05"), f.Task.Id)
			if err != nil {
				return err.Error()
			}
		} else if state == sdkframe.StateFin { // 任务执行完成，同步进度条
			err = proactive_task_node_relations.UpdateNodeProgress(uint64(nodeTask["node_id"]), f.Task.Id, 100, proactive_task_node_relations.StateFin, endAt.Format("2006-01-02 15:04:05"))
			_ = task.NewProactiveTasks().UpdateEndTimeAndEstimateTime(endAt.Format("2006-01-02 15:04:05"), f.Task.Id) // 更新结束时间～
			if err != nil {
				return err.Error()
			}
			// node, _ := data_source.NewNodeModel().First(mysql.WithWhere("id =?", nodeTask["node_id"]))
			// if node.Id == 0 {
			// 	// 节点不存在时，标注为同步失败
			// 	proactive_task_node_relations.UpdateSyncStatus(f.Task.Id, nodeTask["node_id"], task.SyncStatusSyncFailed, task.SyncStatusSyncFailed)
			// 	continue
			// }
			// f.Node = node
			// // 扫描完成同步结果
			// go f.SyncResult(f.Node, map[string]any{
			// 	"task_id":                         f.Task.Id,
			// 	"proactive_task_node_relation_id": nodeTask["id"],
			// })
		} else {
			err = proactive_task_node_relations.UpdateNodeState(int(f.Task.Id), uint64(nodeTask["node_id"]), nodeTask["node_task_id"], proactive_task_node_relations.StateWait)
			if err != nil {
				return err.Error()
			}

		}
	}

	// 子节点全部完成
	if proactive_task_node_relations.ObtainNodeTaskAllFinishedState(f.Task.Id) {
		err := task.NewProactiveTasks().UpdateStateTask(f.Task.Id)
		if err != nil {
			return err.Error()
		}
	} else if proactive_task_node_relations.ObtainNodeTaskAllScanningState(f.Task.Id) {
		_ = task.NewProactiveTasks().UpdateScanningProgress(f.Task.Id, proactive_task_node_relations.ObtainNodeTaskAllAvgProgress(f.Task.Id))
	} else {
		_ = task.NewProactiveTasks().UpdateProgress(f.Task.Id, proactive_task_node_relations.ObtainNodeTaskAllAvgProgress(f.Task.Id))
	}

	return "状态同步成功"
}

// ImmediateTask
// @Summary 立即执行任务
func (f *Common) ImmediateTask() string {
	nodeIds := proactive_task_node_relations.GetNodeIds(int(f.Task.Id))
	for _, nodeId := range nodeIds {
		config, err := data_source.NewNodeConfigModel().GetNodeConfig(uint64(nodeId))
		if err != nil {
			logs.GetLogger().Errorf("ImmediateTask GetNodeConfig error: %s", err.Error())
			return err.Error()
		}

		f.NodeConfig = config
		params := f.Task.OtherCfgs
		if params == nil {
			params = make(map[string]any)
		}
		params["scan_ip_range_type"] = f.Task.ScanIPRangeType
		params["scan_ip_ranges"] = f.Task.ScanIPRanges
		params["poc_scan_type"] = f.Task.PocScanType
		params["selected_pocs"] = f.Task.SelectedPocs
		params["scan_port"] = f.Task.ScanPort
		params["scan_type"] = f.Task.ScanType
		params["bandwidth"] = f.Task.Bandwidth
		params["concurrency"] = f.Task.Concurrency
		nodeTaskId, err := f.Sdk.DistributeTask(context.Background(), f, params)
		if err != nil {
			logs.GetLogger().Errorf("ImmediateTask DistributeTask error: %s", err.Error())
			return err.Error()
		}

		f.Task.Status = sdkframe.StateRunning
		f.Task.SyncStatus = task.SyncStatusUnSync
		task.NewProactiveTasks().Save(&f.Task)
		var nodeTaskIdInt64 int64
		switch v := nodeTaskId.(type) {
		case int:
			nodeTaskIdInt64 = int64(v)
		case int64:
			nodeTaskIdInt64 = v
		case float32:
			nodeTaskIdInt64 = int64(v)
		case float64:
			nodeTaskIdInt64 = int64(v)
		case string:
			nodeTaskIdInt64, err = strconv.ParseInt(v, 10, 64)
			if err != nil {
				logs.GetLogger().Errorf("ImmediateTask ParseInt error: %s", err.Error())
				return err.Error()
			}
		case int32:
			nodeTaskIdInt64 = int64(v)
		case uint32:
			nodeTaskIdInt64 = int64(v)
		case uint:
			nodeTaskIdInt64 = int64(v)
		case uint64:
			nodeTaskIdInt64 = int64(v)
		default:
			return "nodeTaskId is not int64"
		}
		err = proactive_task_node_relations.UpdateNodeTaskId(uint64(nodeId), f.Task.Id, nodeTaskIdInt64)
		if err != nil {
			logs.GetLogger().Errorf("ImmediateTask update nodeTaskId error: %s", err.Error())
			return err.Error()
		}
	}

	return "立即执行任务"
}

// StartTask
// @Summary 开始执行任务
func (f *Common) StartTask() string {
	// 获取节点任务 ID
	nodeTasks := proactive_task_node_relations.GetNodeTaskIds(int(f.Task.Id))
	for _, nodeTask := range nodeTasks {
		config, err := data_source.NewNodeConfigModel().GetNodeConfig(uint64(nodeTask["node_id"]))
		if err != nil {
			logs.GetLogger().Error(fmt.Sprintf("获取节点配置失败: %s", err))
			return err.Error()
		}

		f.NodeConfig = config
		err = f.Sdk.ResumeTask(context.Background(), nodeTask["node_task_id"], f)
		if err != nil {
			f.WriteLog(fmt.Sprintf("恢复任务失败: %s", err))
			return err.Error()
		}

		f.Task.Status = sdkframe.StateRunning
		err = task.NewProactiveTasks().Save(&f.Task).Error
		if err != nil {
			logs.GetLogger().Errorf("保存任务状态失败：%s", err.Error())
			return err.Error()
		}
	}

	return "开始成功！"
}

// PauseTask
// @Summary 暂停任务
func (f *Common) PauseTask() string {
	// 获取节点任务 ID
	nodeTasks := proactive_task_node_relations.GetNodeTaskIds(int(f.Task.Id))
	for _, nodeTask := range nodeTasks {
		config, err := data_source.NewNodeConfigModel().GetNodeConfig(uint64(nodeTask["node_id"]))
		if err != nil {
			return err.Error()
		}
		f.NodeConfig = config

		err = f.Sdk.PauseTask(context.Background(), nodeTask["node_task_id"], f)
		if err != nil {
			logs.GetLogger().Error(fmt.Sprintf("任务暂停失败: %s", err.Error()))
			return err.Error()
		}

		f.Task.Status = sdkframe.StateStop
		err = task.NewProactiveTasks().Save(&f.Task).Error
		if err != nil {
			return err.Error()
		}
	}

	return "暂停成功"
}

// SyncResult
// @Summary 同步结果
func (f *Common) SyncResult(node *data_source.Node, taskInfo map[string]any) error {
	defer func() {
		if err := recover(); err != nil {
			logs.GetLogger().Errorf("SyncResult panic: %v stack:%v", err, string(debug.Stack()))
		}
	}()
	config, err := data_source.NewNodeConfigModel().GetNodeConfig(node.Id)
	if err != nil {
		logs.GetLogger().Errorf("获取节点配置失败: %v", err)
		return err
	}
	f.NodeConfig = config
	f.Node = node
	proactiveTask, err := task.NewProactiveTasks().FindById(int64(taskInfo["task_id"].(uint64)))
	if err != nil {
		logs.GetLogger().Errorf("获取任务失败: %v", err)
		return err
	}

	proactiveTaskNodeRelation, err := proactive_task_node_relations.FindById(int64(taskInfo["proactive_task_node_relation_id"].(int)))
	if err != nil {
		logs.GetLogger().Errorf("获取任务关系失败: %v", err)
		return err
	}
	f.proactiveTaskNodeRelations = proactiveTaskNodeRelation

	proactiveTask.SyncStatus = task.SyncStatusSyncing
	task.NewProactiveTasks().Save(proactiveTask)
	// 同步资产 或者 漏洞
	switch proactiveTask.TaskType {
	case 1:
		ret, err := f.Sdk.GetTaskResultAssets(context.Background(), proactiveTaskNodeRelation.NodeTaskId, f)
		if err != nil {
			logs.GetLogger().Errorf("GetTaskResultAssets error:%v", err)
			proactive_task_node_relations.UpdateNodeDataSyncAssetStatus(proactiveTaskNodeRelation.Id, proactive_task_node_relations.SyncFailStatus)
			return err
		}
		proactiveTask.SyncStatus = task.SyncStatusSuccess
		task.NewProactiveTasks().Save(proactiveTask)
		if err := proactive_task_node_relations.UpdateAssetSum(proactiveTaskNodeRelation.Id, ret.AssetNum); err != nil {
			logs.GetLogger().Errorf("sync task asset - save proactiveTaskNodeRelation Asset sum error: %v", err.Error())
		}
		err = proactive_task_node_relations.UpdateNodeDataSyncAssetStatus(proactiveTaskNodeRelation.Id, proactive_task_node_relations.SyncSuccessStatus)
		if err != nil {
			logs.GetLogger().Errorf("sync task asset - save proactiveTaskNodeRelation status error: %v", err.Error())
		}
		mergeResult, err := pb.GetProtoClient().TriggerMergeForAsset(context.Background(), &pb.TriggerMergeForAssetRequest{
			TriggerMergeBaseRequest: &pb.TriggerMergeBaseRequest{
				SourceId:     f.Node.SourceId,
				NodeId:       f.Node.Id,
				TriggerEvent: "主动扫描任务结束",
				TaskId:       fmt.Sprint(f.Task.Id + mysql.ProactiveTaskIdAdd),
				ChildTaskId:  fmt.Sprint(proactiveTaskNodeRelation.Id + mysql.ProactiveTaskIdAdd),
			},
			DataRangeByTask: &pb.DataRangeByTask{
				TaskId:      fmt.Sprint(f.Task.Id + mysql.ProactiveTaskIdAdd),
				ChildTaskId: fmt.Sprint(proactiveTaskNodeRelation.Id + mysql.ProactiveTaskIdAdd),
				NodeId:      f.Node.Id,
			},
		}, pb.ClientWithAddress)
		if err != nil {
			logs.GetLogger().Errorf("foeye proactive task end Send to TriggerMergeRequest error:%v", err)
		}
		if mergeResult != nil && !mergeResult.Success {
			logs.GetLogger().Errorf("foeye proactive task end Send to TriggerMergeRequest Message error:%v", mergeResult.Message)
		}
	case 2:
		ret, err := f.Sdk.GetTaskResultVul(context.Background(), proactiveTaskNodeRelation.NodeTaskId, f)
		if err != nil {
			logs.GetLogger().Errorf("foeye proactive task end GetTaskResultVul error:%v", err)
			proactive_task_node_relations.UpdateNodeDataSyncAssetStatus(proactiveTaskNodeRelation.Id, proactive_task_node_relations.SyncFailStatus)
			return err
		}
		proactiveTask.SyncStatus = task.SyncStatusSuccess
		task.NewProactiveTasks().Save(proactiveTask)
		if err := proactive_task_node_relations.UpdateAssetSum(proactiveTaskNodeRelation.Id, ret.AssetNum); err != nil {
			logs.GetLogger().Errorf("sync task asset - save proactiveTaskNodeRelation Asset sum error: %v", err.Error())
		}
		err = proactive_task_node_relations.UpdateNodeDataSyncAssetStatus(proactiveTaskNodeRelation.Id, proactive_task_node_relations.SyncSuccessStatus)
		if err != nil {
			logs.GetLogger().Errorf("sync task asset - save proactiveTaskNodeRelation status error: %v", err.Error())
		}
		mergeResult, err := pb.GetProtoClient().TriggerMergeForVuln(context.Background(), &pb.TriggerMergeForVulnRequest{
			TriggerMergeBaseRequest: &pb.TriggerMergeBaseRequest{
				SourceId:     f.Node.SourceId,
				NodeId:       f.Node.Id,
				TriggerEvent: "主动扫描任务结束",
				TaskId:       fmt.Sprint(f.Task.Id + mysql.ProactiveTaskIdAdd),
				ChildTaskId:  fmt.Sprint(proactiveTaskNodeRelation.Id + mysql.ProactiveTaskIdAdd),
			},
			DataRangeByTask: &pb.DataRangeByTask{
				TaskId:      fmt.Sprint(f.Task.Id + mysql.ProactiveTaskIdAdd),
				ChildTaskId: fmt.Sprint(proactiveTaskNodeRelation.Id + mysql.ProactiveTaskIdAdd),
				NodeId:      f.Node.Id,
			},
		}, pb.ClientWithAddress)
		if err != nil {
			logs.GetLogger().Errorf("foeye proactive task end Send to TriggerMergeRequest error:%v", err)
		}
		if mergeResult != nil && !mergeResult.Success {
			logs.GetLogger().Errorf("foeye proactive task end Send to TriggerMergeRequest Message error:%v", mergeResult.Message)
		}
	default:
		ret, err := f.Sdk.GetTaskResultAssets(context.Background(), proactiveTaskNodeRelation.NodeTaskId, f)
		if err != nil {
			logs.GetLogger().Errorf("proactive task end GetTaskResultAssets error:%v", err)
			proactive_task_node_relations.UpdateNodeDataSyncAssetStatus(proactiveTaskNodeRelation.Id, proactive_task_node_relations.SyncFailStatus)
			return err
		}
		proactiveTask.SyncStatus = task.SyncStatusSuccess
		task.NewProactiveTasks().Save(proactiveTask)
		if err := proactive_task_node_relations.UpdateAssetSum(proactiveTaskNodeRelation.Id, ret.AssetNum); err != nil {
			logs.GetLogger().Errorf("sync task asset - save proactiveTaskNodeRelation Asset sum error: %v", err.Error())
		}
		err = proactive_task_node_relations.UpdateNodeDataSyncAssetStatus(proactiveTaskNodeRelation.Id, proactive_task_node_relations.SyncSuccessStatus)
		if err != nil {
			logs.GetLogger().Errorf("sync task asset - save proactiveTaskNodeRelation status error: %v", err.Error())
		}
		ret, err = f.Sdk.GetTaskResultVul(context.Background(), proactiveTaskNodeRelation.NodeTaskId, f)
		if err != nil {
			logs.GetLogger().Errorf("sync task asset - get task result vul error: %v", err.Error())
			proactive_task_node_relations.UpdateNodeDataSyncAssetStatus(proactiveTaskNodeRelation.Id, proactive_task_node_relations.SyncFailStatus)
			return err
		}
		proactiveTask.SyncStatus = task.SyncStatusSuccess
		task.NewProactiveTasks().Save(proactiveTask)
		if err := proactive_task_node_relations.UpdateAssetSum(proactiveTaskNodeRelation.Id, ret.AssetNum); err != nil {
			logs.GetLogger().Errorf("sync task asset - save proactiveTaskNodeRelation Asset sum error: %v", err.Error())
		}
		err = proactive_task_node_relations.UpdateNodeDataSyncAssetStatus(proactiveTaskNodeRelation.Id, proactive_task_node_relations.SyncSuccessStatus)
		if err != nil {
			logs.GetLogger().Errorf("sync task asset - save proactiveTaskNodeRelation status error: %v", err.Error())
		}
		mergeResult, err := pb.GetProtoClient().TriggerMergeForAsset(context.Background(), &pb.TriggerMergeForAssetRequest{
			TriggerMergeBaseRequest: &pb.TriggerMergeBaseRequest{
				SourceId:     f.Node.SourceId,
				NodeId:       f.Node.Id,
				TriggerEvent: "主动扫描任务结束",
				TaskId:       fmt.Sprint(f.Task.Id + mysql.ProactiveTaskIdAdd),
				ChildTaskId:  fmt.Sprint(proactiveTaskNodeRelation.Id + mysql.ProactiveTaskIdAdd),
			},
			DataRangeByTask: &pb.DataRangeByTask{
				TaskId:      fmt.Sprint(f.Task.Id + mysql.ProactiveTaskIdAdd),
				ChildTaskId: fmt.Sprint(proactiveTaskNodeRelation.Id + mysql.ProactiveTaskIdAdd),
				NodeId:      f.Node.Id,
			},
		}, pb.ClientWithAddress)
		if err != nil {
			logs.GetLogger().Errorf("foeye proactive task end Send to TriggerMergeRequest error:%v", err)
		}
		if mergeResult != nil && !mergeResult.Success {
			logs.GetLogger().Errorf("foeye proactive task end Send to TriggerMergeRequest Message error:%v", mergeResult.Message)
		}
		mergeResult, err = pb.GetProtoClient().TriggerMergeForVuln(context.Background(), &pb.TriggerMergeForVulnRequest{
			TriggerMergeBaseRequest: &pb.TriggerMergeBaseRequest{
				SourceId:     f.Node.SourceId,
				NodeId:       f.Node.Id,
				TriggerEvent: "主动扫描任务结束",
				TaskId:       fmt.Sprint(f.Task.Id + mysql.ProactiveTaskIdAdd),
				ChildTaskId:  fmt.Sprint(proactiveTaskNodeRelation.Id + mysql.ProactiveTaskIdAdd),
			},
			DataRangeByTask: &pb.DataRangeByTask{
				TaskId:      fmt.Sprint(f.Task.Id + mysql.ProactiveTaskIdAdd),
				ChildTaskId: fmt.Sprint(proactiveTaskNodeRelation.Id + mysql.ProactiveTaskIdAdd),
				NodeId:      f.Node.Id,
			},
		}, pb.ClientWithAddress)
		if err != nil {
			logs.GetLogger().Errorf("foeye proactive task end Send to TriggerMergeRequest error:%v", err)
		}
		if mergeResult != nil && !mergeResult.Success {
			logs.GetLogger().Errorf("foeye proactive task end Send to TriggerMergeRequest Message error:%v", mergeResult.Message)
		}
	}

	return nil
}

// VerifyTaskParams
// @Summary 验证任务参数 - 包括节点状态，节点模式等是否一致
func (f *Common) VerifyTaskParams(dataNodeIds []int) []error {
	failNodeErrs := make([]error, 0)

	if err := filter_scan_ips.CheckIpFormat(f.Task.ScanIPRanges); err != nil {
		return append(failNodeErrs, err)
	}

	return failNodeErrs
}

func (f *Common) DelTask() string {
	nodeTasks := proactive_task_node_relations.GetNodeTaskIds(int(f.Task.Id))

	// 设置节点配置
	for _, nodeTask := range nodeTasks {
		config, err := data_source.NewNodeConfigModel().GetNodeConfig(uint64(nodeTask["node_id"]))
		if err != nil {
			return err.Error()
		}
		f.NodeConfig = config

		err = f.Sdk.DeleteTask(context.Background(), nodeTask["node_task_id"], f)
		if err != nil {
			logs.GetLogger().Errorf("删除任务失败: %v", err)
			return err.Error()
		}

	}
	return "删除成功"
}

func (f *Common) ScanTypes(nodeId uint64) ([]map[string]any, error) {
	return nil, errors.New("not support")
}

// BatchSaveToES 将数据保存至es
func (f *Common) BatchSaveToES(indexName string, data []map[string]interface{}) error {
	bulkRequest := es.GetEsClient().Bulk()
	size := len(data)
	if size == 0 {
		return nil
	}
	for i := 0; i < size; i++ {
		id, ok := data[i]["id"].(string)
		if !ok {
			logs.GetLogger().Errorf("BatchSaveToES item id is not a string, item: %+v", data[i])
			continue
		}
		docReq := elastic.NewBulkIndexRequest().Index(indexName).Id(id).Doc(data[i])
		bulkRequest = bulkRequest.Add(docReq)

		if (i+1)%10 == 0 || (i+1) == size {
			bulkDo, err := bulkRequest.Refresh("true").Do(context.Background())
			if err != nil || bulkDo.Errors {
				errString := handle_es_bulk_error.HandleBulkResp(indexName, err, bulkDo)
				f.WriteLog(errString)
			}
		}
	}
	return nil
}

// BatchSaveData 追加数据及保存数据
func (f *Common) BatchSaveData(sourceData, processData []map[string]interface{}, syncType int64, processIndexName string) error {
	//获取对应源名称
	var sourceTaskIndexName = ""
	switch syncType {
	case data_sync_task.SyncAsset:
		sourceTaskIndexName = f.IndexTaskAssets
	case data_sync_task.SyncThreat:
		sourceTaskIndexName = f.IndexTaskThreats
	case data_sync_task.SyncPeople:
		sourceTaskIndexName = f.IndexTaskPeople
	default:
		return fmt.Errorf("BatchSaveData 数据源索引不存在,syncType:%d,processIndexName:%s", syncType, processIndexName)
	}

	if sourceTaskIndexName == "" {
		return fmt.Errorf("BatchSaveData 数据源索引不存在,syncType:%d,processIndexName:%s", syncType, processIndexName)
	}
	var networkType int
	var err error
	if syncType != data_sync_task.SyncPeople {
		networkType, err = f.Node.GetNetworkType()
		if err != nil {
			return err
		}
	}

	var taskData = make([]map[string]interface{}, 0, len(sourceData))
	for _, sourceItem := range sourceData {
		id := strings.ReplaceAll(uuid.New().String(), "-", "")
		sourceItem["id"] = id

		var taskItem = make(map[string]interface{})
		for key, value := range sourceItem {
			taskItem[key] = value
		}

		taskItem = f.appendAttribute(syncType, taskItem)
		if syncType != data_sync_task.SyncPeople {
			taskItem["network_type"] = networkType
		}
		taskData = append(taskData, taskItem)
	}
	for k, processItem := range processData {
		id := strings.ReplaceAll(uuid.New().String(), "-", "")
		processItem["id"] = id
		processItem = f.appendAttribute(syncType, processItem)
		if syncType != data_sync_task.SyncPeople {
			processItem["network_type"] = networkType
		}
		processData[k] = processItem
	}

	if err := f.BatchSaveToES(sourceTaskIndexName, taskData); err != nil {
		return err
	}
	if err := f.BatchSaveToES(processIndexName, processData); err != nil {
		return err
	}
	return nil
}

func (f *Common) appendAttribute(syncType int64, item map[string]interface{}) map[string]interface{} {
	switch syncType {
	case data_sync_task.SyncAsset:
		item["asset_task_id"] = item["id"]
	case data_sync_task.SyncThreat:
		item["poc_task_id"] = item["id"]
	case data_sync_task.SyncPeople:
		item["staff_task_id"] = item["id"]
	default:
		return nil
	}

	item["source"] = f.Node.SourceId
	item["source_id"] = f.Node.SourceId
	item["node_id"] = f.Node.Id
	item["node"] = f.Node.Id
	ip, _ := item["ip"].(string)
	areaId, _ := f.Node.GetAreaByIp(ip)
	item["area_id"] = areaId
	item["area"] = areaId
	item["task_id"] = f.Task.Id + mysql.ProactiveTaskIdAdd
	item["child_task_id"] = f.proactiveTaskNodeRelations.Id + mysql.ProactiveTaskIdAdd

	item["sync_created_at"] = localtime.NewLocalTime(time.Now())
	item["sync_updated_at"] = localtime.NewLocalTime(time.Now())
	return item
}

func (f *Common) BatchUpdateProcessDevice(processDeviceData []map[string]interface{}) error {
	for i := 0; i < len(processDeviceData); i++ {
		d := processDeviceData[i]
		d["source"] = f.Node.SourceId
		d["node"] = f.Node.Id
		d["area"] = f.Node.AreaId
		d["task_id"] = f.Task.Id + mysql.ProactiveTaskIdAdd
		d["child_task_id"] = f.proactiveTaskNodeRelations.Id + mysql.ProactiveTaskIdAdd
	}
	return f.BatchSaveToES(f.IndexProcessDevice, processDeviceData)
}

func (f *Common) BatchSaveRelations(total int64, sourceData, processData []map[string]interface{}) error {
	return nil
}

// BatchSaveAsset 批量保存资产数据
func (f *Common) BatchSaveAsset(total int64, sourceData, processData, processDeviceData []map[string]interface{}) error {
	var processValidData = make([]map[string]interface{}, 0, len(processData))
	for i := 0; i < len(processData); i++ {
		// 检测IP是否正确
		ip, ok := processData[i]["ip"].(string)
		if !ok || ip == "" {
			js, _ := json.Marshal(processData[i])
			f.WriteLog(fmt.Sprintf("node %d data format err, data: %s", f.Node.Id, string(js)))
			f.RecordFailedData(data_sync_task.SyncAsset, "ip", ip, "保存资产时ip校验未通过")
			continue
		}
		processValidData = append(processValidData, processData[i])
	}

	err := f.BatchSaveData(sourceData, processValidData, data_sync_task.SyncAsset, f.IndexProcessAssets)
	if err != nil {
		return err
	}
	if len(processDeviceData) > 0 {
		return f.BatchUpdateProcessDevice(processDeviceData)
	}
	return nil
}

// BatchSaveVul 批量保存漏洞数据
func (f *Common) BatchSaveVul(total int64, sourceData, processData []map[string]interface{}) error {
	return f.BatchSaveData(sourceData, processData, data_sync_task.SyncThreat, f.IndexProcessThreats)
}

// BatchSaveStaff 批量保存人员数据
func (f *Common) BatchSaveStaff(total int64, sourceData, processData []map[string]interface{}) error {
	return f.BatchSaveData(sourceData, processData, data_sync_task.SyncPeople, staff.NewProcessStaffModel().IndexName())
}

func (f *Common) GetNodeConfigByKey(key string) any {
	if v, ok := f.NodeConfig[key]; ok {
		return v
	}
	return nil
}

func (f *Common) WriteLog(msg string) {
	logEntry := fmt.Sprintf("SyncTask WriteLog msg:%s", msg)
	if f.Task != nil && f.Node != nil {
		logEntry = fmt.Sprintf("SyncTask WriteLog msg:%s sourceId:%d,nodeId:%d, taskId:%d",
			msg, f.Task.SourceId, f.Node.Id, f.Task.Id)
	}

	logs.GetSyncLogger().Infof(logEntry)
}

func (f *Common) GetRedisClient() *goRedis.Client {
	return redis.GetRedisClient()
}

func (f *Common) RecordFailedData(syncType int64, dataType, dataContent, failedReason string) error {
	return nil
}
