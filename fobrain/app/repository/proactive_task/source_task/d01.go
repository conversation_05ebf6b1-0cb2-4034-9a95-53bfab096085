package source_task

import (
	"errors"
	"fmt"
	"fobrain/pkg/filter_scan_ips"

	"fobrain/fobrain/app/services/node/d01"
	"fobrain/fobrain/app/services/node/foeye"
	d2 "fobrain/fobrain/app/services/sync/d01"
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/proactive_task_node_relations"
	"fobrain/models/mysql/task"
)

func init() {
	SourceConfig[data_source.D01SourceId] = func(task *task.ProactiveTasks) SourceTask {
		return &D01{Base: &Base{Task: task}}
	}
}

type D01 struct {
	*Base
}

// SyncStatus
// @Summary 定时同步任务状态
func (d *D01) SyncStatus() string {
	nodeTaskIds := proactive_task_node_relations.GetNodeTaskIds(int(d.Task.Id)) // 获取 当前任务对应的节点
	for _, nodeTask := range nodeTaskIds {
		cli, err := d.GetCli(uint64(nodeTask["node_id"]))
		if err != nil {
			return err.Error()
		}

		taskInfo, err := cli.GetTaskInfo(int64(nodeTask["node_task_id"]))
		if err != nil {
			return err.Error()
		}
		// 获取查询到 三方任务执行的 进度和状态
		progress := taskInfo.Progress
		state := taskInfo.State
		beginTime := taskInfo.RealBeginTime
		startAt := taskInfo.RealBeginTime
		endAt := taskInfo.RealEndTime

		if state == FoeyeTaskStateRunning { // 任务正在扫描，同步进度条
			err = proactive_task_node_relations.UpdateNodeProgress(uint64(nodeTask["node_id"]), d.Task.Id, progress, int64(state), startAt)
			err = proactive_task_node_relations.UpdateBeginTime(beginTime, d.Task.Id)
			if err != nil {
				return err.Error()
			}
		} else if state == FoeyeTaskStateFin { // 任务执行完成，同步进度条
			err = proactive_task_node_relations.UpdateNodeProgress(uint64(nodeTask["node_id"]), d.Task.Id, 100, int64(state), *endAt)
			if err != nil {
				return err.Error()
			}
			err = task.NewProactiveTasks().UpdateEndTimeAndEstimateTime(*endAt, d.Task.Id) // 更新结束时间～
			if err != nil {
				return err.Error()
			}
		} else {
			err = proactive_task_node_relations.UpdateNodeState(int(d.Task.Id), uint64(nodeTask["node_id"]), nodeTask["node_task_id"], state)
			if err != nil {
				return err.Error()
			}

		}
	}

	// 子节点全部完成
	if proactive_task_node_relations.ObtainNodeTaskAllFinishedState(d.Task.Id) {
		_ = task.NewProactiveTasks().UpdateStateTask(d.Task.Id)
	} else if proactive_task_node_relations.ObtainNodeTaskAllScanningState(d.Task.Id) {
		_ = task.NewProactiveTasks().UpdateScanningProgress(d.Task.Id, proactive_task_node_relations.ObtainNodeTaskAllAvgProgress(d.Task.Id))
	} else {
		_ = task.NewProactiveTasks().UpdateProgress(d.Task.Id, proactive_task_node_relations.ObtainNodeTaskAllAvgProgress(d.Task.Id))
	}

	return "状态同步成功"
}

// ImmediateTask
// @Summary 立即执行任务
func (d *D01) ImmediateTask() string {
	nodeIds := proactive_task_node_relations.GetNodeIds(int(d.Task.Id))
	for _, nodeId := range nodeIds {
		cli := foeye.NewFoeye()
		err := cli.SetNode(uint64(nodeId))
		if err != nil {
			return err.Error()
		}

		config, err := data_source.NewNodeConfigModel().GetNodeConfig(uint64(nodeId))
		if err != nil {
			return err.Error()
		}

		err = cli.SetConfig(config)
		if err != nil {
			return err.Error()
		}

		params := d.Task.FoeyeTaskParams()
		if d.Task.PocScanType != "special" && cli.IsFoeyeV2() {
			params["selected_pocs"] = "all"
		}
		nodeTaskId, err := cli.Push(params)
		if err != nil {
			return err.Error()
		}

		d.Task.Status = 1
		task.NewProactiveTasks().Save(&d.Task)
		err = proactive_task_node_relations.UpdateNodeTaskId(uint64(nodeId), d.Task.Id, nodeTaskId)
		if err != nil {
			return err.Error()
		}
	}

	return "立即执行任务"
}

// StartTask
// @Summary 执行任务 - 用于暂停后重新启动任务
func (d *D01) StartTask() string {
	nodeTasks := proactive_task_node_relations.GetNodeTaskIds(int(d.Task.Id))
	for _, nodeTask := range nodeTasks {
		cli := d01.NewD01()
		err := cli.SetNode(uint64(nodeTask["node_id"]))
		if err != nil {
			return err.Error()
		}

		config, err := data_source.NewNodeConfigModel().GetNodeConfig(uint64(nodeTask["node_id"]))
		if err != nil {
			return err.Error()
		}

		err = cli.SetConfig(config)
		if err != nil {
			return err.Error()
		}

		err = cli.StartTask(nodeTask["node_task_id"])
		if err != nil {
			return err.Error()
		}

		d.Task.Status = 2
		err = task.NewProactiveTasks().Save(&d.Task).Error
		if err != nil {
			return err.Error()
		}
	}
	return "开始成功！"
}

// PauseTask
// @Summary 暂停任务
func (d *D01) PauseTask() string {
	// 获取节点任务 ID
	nodeTasks := proactive_task_node_relations.GetNodeTaskIds(int(d.Task.Id))
	for _, nodeTask := range nodeTasks {
		cli := d01.NewD01()
		err := cli.SetNode(uint64(nodeTask["node_id"]))
		if err != nil {
			return err.Error()
		}

		config, err := data_source.NewNodeConfigModel().GetNodeConfig(uint64(nodeTask["node_id"]))
		if err != nil {
			return err.Error()
		}

		err = cli.SetConfig(config)
		if err != nil {
			return err.Error()
		}

		err = cli.StopTask(nodeTask["node_task_id"])
		if err != nil {
			return err.Error()
		}

		d.Task.Status = 3
		err = task.NewProactiveTasks().Save(&d.Task).Error
		if err != nil {
			return err.Error()
		}
	}
	return "暂停成功"
}

func (d *D01) SyncResult(node *data_source.Node, taskInfo map[string]any) error {
	return d2.NewSyncTask(node, taskInfo)
}

func (d *D01) VerifyTaskParams(dataNodeIds []int) []error {
	failNodeErrs := make([]error, 0)

	if err := filter_scan_ips.CheckIpFormat(d.Task.ScanIPRanges); err != nil {
		return append(failNodeErrs, err)
	}

	for _, nodeId := range dataNodeIds {
		// TODO: 创建任务前，先获取节点上的模式类型是否与当前任务选择的模式类型一致（集中管控模式、节点模式）
		cli := foeye.NewFoeye()
		// 设置 foeyeClient 请求的节点
		if err := cli.SetNode(uint64(nodeId)); err != nil {
			failNodeErrs = append(failNodeErrs, errors.New(fmt.Sprintf("nodeId: %d, err: %v", nodeId, err.Error())))
			continue
		}

		nodeMode, err := cli.NodeMode()
		if err != nil {
			failNodeErrs = append(failNodeErrs, errors.New(fmt.Sprintf("nodeId: %d, err: %v", nodeId, err.Error())))
		}

		if nodeMode != d.Task.ScanMode {
			failNodeErrs = append(failNodeErrs, errors.New(fmt.Sprintf("nodeId: %d, 节点模式与任务模式不匹配", nodeId)))
			continue
		}
	}

	return failNodeErrs
}

func (d *D01) GetCli(nodeId uint64) (*foeye.Foeye, error) {
	cli := foeye.NewFoeye()
	err := cli.SetNode(nodeId)
	if err != nil {
		return nil, err
	}

	config, err := data_source.NewNodeConfigModel().GetNodeConfig(nodeId)
	if err != nil {
		return nil, err
	}

	err = cli.SetConfig(config)
	if err != nil {
		return nil, err
	}

	return cli, nil
}

func (d *D01) DelTask() string {
	nodeTasks := proactive_task_node_relations.GetNodeTaskIds(int(d.Task.Id))

	// 设置节点配置
	for _, nodeTask := range nodeTasks {
		cli := d01.NewD01()
		err := cli.SetNode(uint64(nodeTask["node_id"]))
		if err != nil {
			return err.Error()
		}

		config, err := data_source.NewNodeConfigModel().GetNodeConfig(uint64(nodeTask["node_id"]))
		if err != nil {
			return err.Error()
		}

		err = cli.SetConfig(config)
		if err != nil {
			return err.Error()
		}

		err = cli.DelNodeTask(nodeTask["node_task_id"])
		if err != nil {
			return err.Error()
		}

	}
	return "删除成功"
}

func (d *D01) ScanTypes(nodeId uint64) ([]map[string]any, error) {
	return []map[string]any{}, nil
}
