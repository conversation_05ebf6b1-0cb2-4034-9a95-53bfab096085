package source_task

import (
	"go-micro.dev/v4/logger"

	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/task"
)

// @Description 所有节点任务此处分发

var SourceConfig = make(map[int]func(task *task.ProactiveTasks) SourceTask)

type SourceTask interface {
	// SyncStatus 定时同步任务状态
	SyncStatus() string
	// ImmediateTask 立即执行任务
	ImmediateTask() string
	// StartTask 执行任务 - 用于暂停后重新启动任务
	StartTask() string
	// PauseTask 暂停任务
	PauseTask() string
	// SyncResult 同步结果
	SyncResult(node *data_source.Node, taskInfo map[string]any) error
	// VerifyTaskParams 验证任务参数 - 包括节点状态，节点模式等是否一致
	VerifyTaskParams(nodeIds []int) []error
	// DelTask 删除任务
	DelTask() string
	// ScanTypes 扫描任务类型
	ScanTypes(nodeId uint64) ([]map[string]any, error) // 扫描任务类型 为了应对可能存在某些节点需要有自定义的任务类型如 X-Ray 等
}

type Base struct {
	SourceTask
	Task *task.ProactiveTasks
}

// New
// @Summary 初始化
func New(task *task.ProactiveTasks) SourceTask {
	defer func() {
		if err := recover(); err != nil {
			logger.Errorf("New ProactiveTask Error: %v", err)
		}
	}()

	constructor, exists := SourceConfig[task.SourceId]

	if !exists {
		panic("Not Implement")
	}

	return constructor(task)
}

func StartTask() {
	tasks := make([]*task.ProactiveTasks, 0)
	query := task.NewProactiveTasks().Where("status = ?", 3)
	query.Find(&tasks)

	for _, t := range tasks {
		New(t).StartTask()
	}
}

func PauseTask() {
	tasks := make([]*task.ProactiveTasks, 0)
	query := task.NewProactiveTasks().Where("status = ?", 2)
	query.Find(&tasks)

	for _, t := range tasks {
		New(t).PauseTask()
	}
}
