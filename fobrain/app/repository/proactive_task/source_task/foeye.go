package source_task

import (
	"errors"
	"fmt"
	"fobrain/pkg/filter_scan_ips"

	"fobrain/fobrain/app/services/node/foeye"
	foeye2 "fobrain/fobrain/app/services/sync/foeye"
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/proactive_task_node_relations"
	"fobrain/models/mysql/task"
)

func init() {
	SourceConfig[data_source.FoeyeSourceId] = func(task *task.ProactiveTasks) SourceTask {
		return &Foeye{Base: &Base{Task: task}}
	}
}

type Foeye struct {
	*Base
}

const (
	FoeyeTaskStateWait    = 0 // 待执行
	FoeyeTaskStateRunning = 1 // 执行中
	FoeyeTaskStateStop    = 2 // 暂停
	FoeyeTaskStateFin     = 3 // 完成
	FoeyeTaskStateAutStop = 4 // 自动暂停
)

// SyncStatus
// @Summary 定时同步任务状态
func (f *Foeye) SyncStatus() string {
	nodeTaskIds := proactive_task_node_relations.GetNodeTaskIds(int(f.Task.Id)) // 获取 当前任务对应的节点
	for _, nodeTask := range nodeTaskIds {
		cli, err := f.GetCli(uint64(nodeTask["node_id"]))
		if err != nil {
			return err.Error()
		}

		taskInfo, err := cli.GetTaskInfo(int64(nodeTask["node_task_id"]))
		if err != nil {
			return err.Error()
		}
		// 获取查询到 三方任务执行的 进度和状态
		progress := taskInfo.Progress
		state := taskInfo.State
		beginTime := taskInfo.RealBeginTime
		startAt := taskInfo.RealBeginTime
		endAt := taskInfo.RealEndTime

		if state == FoeyeTaskStateRunning { // 任务正在扫描，同步进度条
			err = proactive_task_node_relations.UpdateNodeProgress(uint64(nodeTask["node_id"]), f.Task.Id, progress, int64(state), startAt)
			err = proactive_task_node_relations.UpdateBeginTime(beginTime, f.Task.Id)
			if err != nil {
				return err.Error()
			}
		} else if state == FoeyeTaskStateFin { // 任务执行完成，同步进度条
			err = proactive_task_node_relations.UpdateNodeProgress(uint64(nodeTask["node_id"]), f.Task.Id, 100, int64(state), *endAt)
			_ = task.NewProactiveTasks().UpdateEndTimeAndEstimateTime(*endAt, f.Task.Id) // 更新结束时间～
			if err != nil {
				return err.Error()
			}
		} else {
			err = proactive_task_node_relations.UpdateNodeState(int(f.Task.Id), uint64(nodeTask["node_id"]), nodeTask["node_task_id"], state)
			if err != nil {
				return err.Error()
			}

		}
	}

	// 子节点全部完成
	if proactive_task_node_relations.ObtainNodeTaskAllFinishedState(f.Task.Id) {
		_ = task.NewProactiveTasks().UpdateStateTask(f.Task.Id)
	} else if proactive_task_node_relations.ObtainNodeTaskAllScanningState(f.Task.Id) {
		_ = task.NewProactiveTasks().UpdateScanningProgress(f.Task.Id, proactive_task_node_relations.ObtainNodeTaskAllAvgProgress(f.Task.Id))
	} else {
		_ = task.NewProactiveTasks().UpdateProgress(f.Task.Id, proactive_task_node_relations.ObtainNodeTaskAllAvgProgress(f.Task.Id))
	}

	return "状态同步成功"
}

// ImmediateTask
// @Summary 立即执行任务
func (f *Foeye) ImmediateTask() string {
	nodeIds := proactive_task_node_relations.GetNodeIds(int(f.Task.Id))
	for _, nodeId := range nodeIds {
		cli := foeye.NewFoeye()
		err := cli.SetNode(uint64(nodeId))
		if err != nil {
			return err.Error()
		}

		config, err := data_source.NewNodeConfigModel().GetNodeConfig(uint64(nodeId))
		if err != nil {
			return err.Error()
		}

		err = cli.SetConfig(config)
		if err != nil {
			return err.Error()
		}

		nodeTaskId, err := cli.Push(f.Task.FoeyeTaskParams())
		if err != nil {
			return err.Error()
		}

		f.Task.Status = 1
		task.NewProactiveTasks().Save(&f.Task)
		err = proactive_task_node_relations.UpdateNodeTaskId(uint64(nodeId), f.Task.Id, nodeTaskId)
		if err != nil {
			return err.Error()
		}
	}

	return "立即执行任务"
}

// StartTask
// @Summary 开始执行任务
func (f *Foeye) StartTask() string {
	// 获取节点任务 ID
	nodeTasks := proactive_task_node_relations.GetNodeTaskIds(int(f.Task.Id))
	for _, nodeTask := range nodeTasks {
		cli := foeye.NewFoeye()
		err := cli.SetNode(uint64(nodeTask["node_id"]))
		if err != nil {
			return err.Error()
		}

		config, err := data_source.NewNodeConfigModel().GetNodeConfig(uint64(nodeTask["node_id"]))
		if err != nil {
			return err.Error()
		}

		err = cli.SetConfig(config)
		if err != nil {
			return err.Error()
		}

		err = cli.StartTask(nodeTask["node_task_id"])
		if err != nil {
			return err.Error()
		}

		f.Task.Status = 2
		err = task.NewProactiveTasks().Save(&f.Task).Error
		if err != nil {
			return err.Error()
		}
	}

	return "开始成功！"
}

// PauseTask
// @Summary 暂停任务
func (f *Foeye) PauseTask() string {
	// 获取节点任务 ID
	nodeTasks := proactive_task_node_relations.GetNodeTaskIds(int(f.Task.Id))
	for _, nodeTask := range nodeTasks {
		cli := foeye.NewFoeye()
		err := cli.SetNode(uint64(nodeTask["node_id"]))
		if err != nil {
			return err.Error()
		}

		config, err := data_source.NewNodeConfigModel().GetNodeConfig(uint64(nodeTask["node_id"]))
		if err != nil {
			return err.Error()
		}

		err = cli.SetConfig(config)
		if err != nil {
			return err.Error()
		}

		err = cli.StopTask(nodeTask["node_task_id"])
		if err != nil {
			return err.Error()
		}

		f.Task.Status = 3
		err = task.NewProactiveTasks().Save(&f.Task).Error
		if err != nil {
			return err.Error()
		}
	}

	return "暂停成功"
}

// SyncResult
// @Summary 同步结果
func (f *Foeye) SyncResult(node *data_source.Node, taskInfo map[string]any) error {
	return foeye2.NewSyncFoeyeTask(node, taskInfo)
}

// VerifyTaskParams
// @Summary 验证任务参数 - 包括节点状态，节点模式等是否一致
func (f *Foeye) VerifyTaskParams(dataNodeIds []int) []error {
	failNodeErrs := make([]error, 0)

	if err := filter_scan_ips.CheckIpFormat(f.Task.ScanIPRanges); err != nil {
		return append(failNodeErrs, err)
	}

	// 节点校验
	for _, nodeId := range dataNodeIds {
		// TODO: 创建任务前，先获取节点上的模式类型是否与当前任务选择的模式类型一致（集中管控模式、节点模式）
		cli := foeye.NewFoeye()
		// 设置 foeyeClient 请求的节点
		if err := cli.SetNode(uint64(nodeId)); err != nil {
			failNodeErrs = append(failNodeErrs, errors.New(fmt.Sprintf("nodeId: %d, err: %v", nodeId, err.Error())))
			continue
		}

		nodeMode, err := cli.NodeMode()
		if err != nil {
			failNodeErrs = append(failNodeErrs, errors.New(fmt.Sprintf("nodeId: %d, err: %v", nodeId, err.Error())))
		}

		if nodeMode != f.Task.ScanMode {
			failNodeErrs = append(failNodeErrs, errors.New(fmt.Sprintf("nodeId: %d, 节点模式与任务模式不匹配", nodeId)))
			continue
		}
	}

	return failNodeErrs
}

func (f *Foeye) GetCli(nodeId uint64) (*foeye.Foeye, error) {
	cli := foeye.NewFoeye()
	err := cli.SetNode(nodeId)
	if err != nil {
		return nil, err
	}

	config, err := data_source.NewNodeConfigModel().GetNodeConfig(nodeId)
	if err != nil {
		return nil, err
	}

	err = cli.SetConfig(config)
	if err != nil {
		return nil, err
	}

	return cli, nil
}

func (f *Foeye) DelTask() string {
	nodeTasks := proactive_task_node_relations.GetNodeTaskIds(int(f.Task.Id))

	// 设置节点配置
	for _, nodeTask := range nodeTasks {
		cli := foeye.NewFoeye()
		err := cli.SetNode(uint64(nodeTask["node_id"]))
		if err != nil {
			return err.Error()
		}

		config, err := data_source.NewNodeConfigModel().GetNodeConfig(uint64(nodeTask["node_id"]))
		if err != nil {
			return err.Error()
		}

		err = cli.SetConfig(config)
		if err != nil {
			return err.Error()
		}

		err = cli.DelNodeTask(nodeTask["node_task_id"])
		if err != nil {
			return err.Error()
		}

	}
	return "删除成功"
}

func (f *Foeye) ScanTypes(nodeId uint64) ([]map[string]any, error) {
	return []map[string]any{}, nil
}
