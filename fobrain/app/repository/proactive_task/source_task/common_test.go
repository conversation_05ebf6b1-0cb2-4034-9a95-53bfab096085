package source_task

import (
	"context"
	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/initialize/mysql"
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/data_sync_task"
	"fobrain/models/mysql/proactive_task_node_relations"
	"fobrain/models/mysql/task"
	"testing"

	sdkframe "git.gobies.org/caasm/fobrain-components/dataSourceSdk/sdk_frame"
	"github.com/DATA-DOG/go-sqlmock"
	"github.com/alicebob/miniredis/v2"
	"github.com/go-redis/redis/v8"
	"github.com/stretchr/testify/assert"
)

type MockActiveScanSDK struct {
}

func (m *MockActiveScanSDK) DistributeTask(ctx context.Context, operate sdkframe.DataSyncOperate, params map[string]any) (sdkframe.TaskID, error) {
	return 1, nil
}

func (m *MockActiveScanSDK) DeleteTask(ctx context.Context, taskID sdkframe.TaskID, operate sdkframe.DataSyncOperate) error {
	return nil
}

func (m *MockActiveScanSDK) PauseTask(ctx context.Context, taskID sdkframe.TaskID, operate sdkframe.DataSyncOperate) error {
	return nil
}

func (m *MockActiveScanSDK) ResumeTask(ctx context.Context, taskID sdkframe.TaskID, operate sdkframe.DataSyncOperate) error {
	return nil
}

func (m *MockActiveScanSDK) GetTaskResultVul(ctx context.Context, taskID sdkframe.TaskID, operate sdkframe.DataSyncOperate) (sdkframe.TaskResult, error) {
	return sdkframe.TaskResult{}, nil
}

func (m *MockActiveScanSDK) GetTaskResultAssets(ctx context.Context, taskID sdkframe.TaskID, operate sdkframe.DataSyncOperate) (sdkframe.TaskResult, error) {
	return sdkframe.TaskResult{}, nil
}

func (m *MockActiveScanSDK) GetTaskProgress(ctx context.Context, taskID sdkframe.TaskID, operate sdkframe.DataSyncOperate) (sdkframe.TaskProgress, error) {
	return sdkframe.TaskProgress{}, nil
}

func (m *MockActiveScanSDK) NetworktConnectTest(ctx context.Context, config map[string]any) error {
	return nil
}

func (m *MockActiveScanSDK) SyncAssets(ctx context.Context, operate sdkframe.DataSyncOperate) (*sdkframe.SyncResponse, error) {
	return &sdkframe.SyncResponse{}, nil
}

func (m *MockActiveScanSDK) SyncRelations(ctx context.Context, operate sdkframe.DataSyncOperate) (*sdkframe.SyncResponse, error) {
	return &sdkframe.SyncResponse{}, nil
}

func (m *MockActiveScanSDK) SyncVulnerability(ctx context.Context, operate sdkframe.DataSyncOperate) (*sdkframe.SyncResponse, error) {
	return &sdkframe.SyncResponse{}, nil
}

func (m *MockActiveScanSDK) SyncStaff(ctx context.Context, operate sdkframe.DataSyncOperate) (*sdkframe.SyncResponse, error) {
	return &sdkframe.SyncResponse{}, nil
}

// createTestCommon 创建测试用的 Common 实例
func createTestCommon() *Common {
	base := &Base{
		Task: &task.ProactiveTasks{
			BaseModel: mysql.BaseModel{Id: 1},
			Name:      "test_task",
			TaskType:  1,
			SourceId:  1,
		},
	}
	node := &data_source.Node{
		SoftDeleteModel: mysql.SoftDeleteModel{Id: 1},
		SourceId:        1,
		AreaId:          1,
	}
	return &Common{
		Base: base,
		Node: node,
		proactiveTaskNodeRelations: &proactive_task_node_relations.ProactiveTaskNodeRelations{
			BaseModel: mysql.BaseModel{Id: 1},
		},
		IndexTaskAssets:     "test_task_assets",
		IndexTaskThreats:    "test_task_threats",
		IndexTaskPeople:     "test_task_people",
		IndexProcessAssets:  "test_process_assets",
		IndexProcessThreats: "test_process_threats",
		IndexProcessPeople:  "test_process_people",
	}
}

func TestCommon_BatchSaveData(t *testing.T) {
	// 1. mock ES
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()
	mockServer.RegisterBulk()
	testcommon.SetElasticClient(mockServer.NewElasticClient())

	sourceData := []map[string]interface{}{
		{"ip": "***********", "name": "test_asset_1"},
		{"ip": "***********", "name": "test_asset_2"},
	}
	processData := []map[string]interface{}{
		{"ip": "***********", "name": "processed_asset_1"},
		{"ip": "***********", "name": "processed_asset_2"},
	}
	tests := []struct {
		name          string
		syncType      int64
		processIndex  string
		expectedError bool
		errorMessage  string
		setupCommon   func(*Common)
	}{
		{
			name:          "成功保存资产数据",
			syncType:      data_sync_task.SyncAsset,
			processIndex:  "test_process_assets",
			expectedError: false,
			setupCommon:   nil,
		},
		{
			name:          "成功保存威胁数据",
			syncType:      data_sync_task.SyncThreat,
			processIndex:  "test_process_threats",
			expectedError: false,
			setupCommon:   nil,
		},
		{
			name:          "无效的同步类型",
			syncType:      999,
			processIndex:  "test_process_invalid",
			expectedError: true,
			errorMessage:  "BatchSaveData 数据源索引不存在",
			setupCommon: func(c *Common) {
				c.IndexTaskAssets = ""
				c.IndexTaskThreats = ""
			},
		},
	}

	// 3. mock SQL（足够多次）
	mockDb := testcommon.InitSqlMock()
	defer mockDb.Close()
	for i := 0; i < 2; i++ {
		mockDb.ExpectQuery("SELECT * FROM `data_node_configs` WHERE node_id = ? AND `key` = ? ORDER BY `data_node_configs`.`id` LIMIT 1").
			WithArgs(1, "network_type").
			WillReturnRows(sqlmock.NewRows([]string{"id", "node_id", "key", "value"}).AddRow(1, 1, "network_type", "1"))
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			common := createTestCommon()
			if tt.setupCommon != nil {
				tt.setupCommon(common)
			}
			err := common.BatchSaveData(sourceData, processData, tt.syncType, tt.processIndex)
			if tt.expectedError {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errorMessage)
			} else {
				assert.NoError(t, err)
				for _, item := range processData {
					assert.Contains(t, item, "asset_task_id")
					assert.Contains(t, item, "source")
					assert.Contains(t, item, "node_id")
					assert.Contains(t, item, "area_id")
					assert.Contains(t, item, "task_id")
					assert.Contains(t, item, "child_task_id")
					assert.Contains(t, item, "network_type")
					assert.Contains(t, item, "sync_created_at")
					assert.Contains(t, item, "sync_updated_at")
					assert.Equal(t, uint64(1), item["source"])
					assert.Equal(t, uint64(1), item["node_id"])
					assert.Equal(t, uint64(1), item["area_id"])
					assert.Equal(t, uint64(1)+mysql.ProactiveTaskIdAdd, item["task_id"])
					assert.Equal(t, uint64(1)+mysql.ProactiveTaskIdAdd, item["child_task_id"])
					assert.Equal(t, 1, item["network_type"])
					assert.NotEmpty(t, item["sync_created_at"])
					assert.NotEmpty(t, item["sync_updated_at"])
				}
			}
		})
	}
}

// TestCommon_VerifyTaskParams 测试任务参数验证
func TestCommon_VerifyTaskParams(t *testing.T) {
	tests := []struct {
		name          string
		scanIPRanges  []string
		expectedError bool
	}{
		{
			name:          "有效的IP范围",
			scanIPRanges:  []string{"***********", "10.0.0.0/24"},
			expectedError: false,
		},
		{
			name:          "无效的IP格式",
			scanIPRanges:  []string{"invalid_ip", "256.256.256.256"},
			expectedError: true,
		},
		{
			name:          "空IP范围",
			scanIPRanges:  []string{},
			expectedError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			common := createTestCommon()
			common.Task.ScanIPRanges = tt.scanIPRanges

			errs := common.VerifyTaskParams([]int{1, 2})

			if tt.expectedError {
				assert.NotEmpty(t, errs)
			} else {
				assert.Empty(t, errs)
			}
		})
	}
}

func TestCommon_ImmediateTask(t *testing.T) {
	// 初始化 mock
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()

	// 创建测试数据
	testTask := &task.ProactiveTasks{
		BaseModel:       mysql.BaseModel{Id: 1},
		Name:            "测试任务",
		TaskType:        1,
		SourceId:        data_source.NSFocusRsasSourceId,
		ScanIPRangeType: "user_input",
		ScanIPRanges:    []string{"***********/24"},
		PocScanType:     "all",
		ScanPort:        1,
		ScanType:        "quick",
		Bandwidth:       100,
		Concurrency:     10,
		Status:          1,
	}

	// 设置预期的 SQL 查询
	mockDb.ExpectQuery("SELECT `node_id` FROM `proactive_task_node_relations` WHERE task_id = ?").
		WithArgs(1).
		WillReturnRows(mockDb.NewRows([]string{"node_id"}).AddRow(1))

	mockDb.ExpectQuery("SELECT * FROM `data_node_configs` WHERE `node_id` = ?").
		WithArgs(1).
		WillReturnRows(mockDb.NewRows([]string{"id", "node_id", "key", "value"}).AddRow(1, 1, "network_type", "1"))
	mockDb.ExpectBegin()
	mockDb.ExpectExec("UPDATE `proactive_tasks` SET `created_at`=?,`updated_at`=?,`name`=?,`desc`=?,`task_type`=?,`source_id`=?,`scan_plan`=?,`scan_period`=?,`scan_time`=?,`status`=?,`progress`=?,`repeat_end_time`=?,`begin_time`=?,`end_time`=?,`scan_mode`=?,`use_seconds`=?,`scan_result`=?,`scan_ip_range_type`=?,`scan_ip_ranges`=?,`poc_scan_type`=?,`selected_pocs`=?,`scan_port`=?,`scan_type`=?,`bandwidth`=?,`concurrency`=?,`other_cfgs`=?,`user_id`=?,`is_start`=?,`sync_status`=? WHERE `id` = ?").
		// WithArgs(testTask.CreatedAt, testTask.UpdatedAt, testTask.Name, testTask.Desc, testTask.TaskType, testTask.SourceId, testTask.ScanPlan, testTask.ScanPeriod, testTask.ScanTime, testTask.Status, testTask.Progress, testTask.RepeatEndTime, testTask.BeginTime, testTask.EndTime, testTask.ScanMode, testTask.UseSeconds, testTask.ScanResult, testTask.ScanIPRangeType, testTask.ScanIPRanges, testTask.PocScanType, testTask.SelectedPocs, testTask.ScanPort, testTask.ScanType, testTask.Bandwidth, testTask.Concurrency, testTask.OtherCfgs, testTask.UserId, testTask.IsStart, testTask.SyncStatus, testTask.Id).
		WillReturnResult(sqlmock.NewResult(1, 1))
	mockDb.ExpectCommit()
	mockDb.ExpectBegin()
	mockDb.ExpectExec("UPDATE `proactive_task_node_relations` SET `node_task_id`=? WHERE node_id = ? AND task_id = ?").
		// WithArgs(1, 1, 1).
		WillReturnResult(sqlmock.NewResult(1, 1))
	mockDb.ExpectCommit()

	// 创建 Common 实例
	common := &Common{
		Base: &Base{Task: testTask},
	}
	common.Sdk = &MockActiveScanSDK{}

	// 执行测试
	result := common.ImmediateTask()

	// 验证结果
	assert.Equal(t, "立即执行任务", result)

	// 验证所有预期的查询都被执行
	err := mockDb.ExpectationsWereMet()
	assert.NoError(t, err)
}

func TestCommon_StartTask(t *testing.T) {
	// 初始化 mock
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()

	// 创建测试数据
	testTask := &task.ProactiveTasks{
		BaseModel:       mysql.BaseModel{Id: 1},
		Name:            "测试任务",
		TaskType:        1,
		SourceId:        data_source.NSFocusRsasSourceId,
		ScanIPRangeType: "user_input",
		ScanIPRanges:    []string{"***********/24"},
		PocScanType:     "all",
		ScanPort:        1,
		ScanType:        "quick",
		Bandwidth:       100,
		Concurrency:     10,
		Status:          1,
	}

	// 设置预期的 SQL 查询
	mockDb.ExpectQuery("SELECT id, node_id, node_task_id FROM `proactive_task_node_relations` WHERE task_id = ?").
		WithArgs(1).
		WillReturnRows(mockDb.NewRows([]string{"id", "node_id", "node_task_id"}).AddRow(1, 1, 1))

	mockDb.ExpectQuery("SELECT * FROM `data_node_configs` WHERE `node_id` = ?").
		WithArgs(1).
		WillReturnRows(mockDb.NewRows([]string{"id", "source_id", "name"}).AddRow(1, data_source.NSFocusRsasSourceId, "测试节点"))

	mockDb.ExpectBegin()
	mockDb.ExpectExec("UPDATE `proactive_tasks` SET `created_at`=?,`updated_at`=?,`name`=?,`desc`=?,`task_type`=?,`source_id`=?,`scan_plan`=?,`scan_period`=?,`scan_time`=?,`status`=?,`progress`=?,`repeat_end_time`=?,`begin_time`=?,`end_time`=?,`scan_mode`=?,`use_seconds`=?,`scan_result`=?,`scan_ip_range_type`=?,`scan_ip_ranges`=?,`poc_scan_type`=?,`selected_pocs`=?,`scan_port`=?,`scan_type`=?,`bandwidth`=?,`concurrency`=?,`other_cfgs`=?,`user_id`=?,`is_start`=?,`sync_status`=? WHERE `id` = ?").
		// WithArgs(testTask.CreatedAt, testTask.UpdatedAt, testTask.Name, testTask.Desc, testTask.TaskType, testTask.SourceId, testTask.ScanPlan, testTask.ScanPeriod, testTask.ScanTime, testTask.Status, testTask.Progress, testTask.RepeatEndTime, testTask.BeginTime, testTask.EndTime, testTask.ScanMode, testTask.UseSeconds, testTask.ScanResult, testTask.ScanIPRangeType, testTask.ScanIPRanges, testTask.PocScanType, testTask.SelectedPocs, testTask.ScanPort, testTask.ScanType, testTask.Bandwidth, testTask.Concurrency, testTask.OtherCfgs, testTask.UserId, testTask.IsStart, testTask.SyncStatus, testTask.Id).
		WillReturnResult(sqlmock.NewResult(1, 1))
	mockDb.ExpectCommit()
	// 创建 Common 实例
	common := &Common{
		Base: &Base{Task: testTask},
		Sdk:  &MockActiveScanSDK{},
	}

	// 执行测试
	result := common.StartTask()

	// 验证结果
	assert.Equal(t, "开始成功！", result)

	// 验证所有预期的查询都被执行
	err := mockDb.ExpectationsWereMet()
	assert.NoError(t, err)
}

func TestCommon_PauseTask(t *testing.T) {
	// 初始化 mock
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()

	// 创建测试数据
	testTask := &task.ProactiveTasks{
		BaseModel:       mysql.BaseModel{Id: 1},
		Name:            "测试任务",
		TaskType:        1,
		SourceId:        data_source.NSFocusRsasSourceId,
		ScanIPRangeType: "user_input",
		ScanIPRanges:    []string{"***********/24"},
		PocScanType:     "all",
		ScanPort:        1,
		ScanType:        "quick",
		Bandwidth:       100,
		Concurrency:     10,
		Status:          2,
	}

	// 设置预期的 SQL 查询
	mockDb.ExpectQuery("SELECT id, node_id, node_task_id FROM `proactive_task_node_relations` WHERE task_id = ?").
		WithArgs(1).
		WillReturnRows(mockDb.NewRows([]string{"id", "node_id", "node_task_id"}).AddRow(1, 1, 1))

	mockDb.ExpectQuery("SELECT * FROM `data_node_configs` WHERE `node_id` = ?").
		WithArgs(1).
		WillReturnRows(mockDb.NewRows([]string{"id", "source_id", "name"}).AddRow(1, data_source.NSFocusRsasSourceId, "测试节点"))

	mockDb.ExpectBegin()
	mockDb.ExpectExec("UPDATE `proactive_tasks` SET `created_at`=?,`updated_at`=?,`name`=?,`desc`=?,`task_type`=?,`source_id`=?,`scan_plan`=?,`scan_period`=?,`scan_time`=?,`status`=?,`progress`=?,`repeat_end_time`=?,`begin_time`=?,`end_time`=?,`scan_mode`=?,`use_seconds`=?,`scan_result`=?,`scan_ip_range_type`=?,`scan_ip_ranges`=?,`poc_scan_type`=?,`selected_pocs`=?,`scan_port`=?,`scan_type`=?,`bandwidth`=?,`concurrency`=?,`other_cfgs`=?,`user_id`=?,`is_start`=?,`sync_status`=? WHERE `id` = ?").
		// WithArgs(testTask.CreatedAt, testTask.UpdatedAt, testTask.Name, testTask.Desc, testTask.TaskType, testTask.SourceId, testTask.ScanPlan, testTask.ScanPeriod, testTask.ScanTime, testTask.Status, testTask.Progress, testTask.RepeatEndTime, testTask.BeginTime, testTask.EndTime, testTask.ScanMode, testTask.UseSeconds, testTask.ScanResult, testTask.ScanIPRangeType, testTask.ScanIPRanges, testTask.PocScanType, testTask.SelectedPocs, testTask.ScanPort, testTask.ScanType, testTask.Bandwidth, testTask.Concurrency, testTask.OtherCfgs, testTask.UserId, testTask.IsStart, testTask.SyncStatus, testTask.Id).
		WillReturnResult(sqlmock.NewResult(1, 1))
	mockDb.ExpectCommit()

	// 创建 Common 实例
	common := &Common{
		Base: &Base{Task: testTask},
		Sdk:  &MockActiveScanSDK{},
	}

	// 执行测试
	result := common.PauseTask()

	// 验证结果
	assert.Equal(t, "暂停成功", result)

	// 验证所有预期的查询都被执行
	err := mockDb.ExpectationsWereMet()
	assert.NoError(t, err)
}

func TestCommon_DelTask(t *testing.T) {
	// 初始化 mock
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()

	// 创建测试数据
	testTask := &task.ProactiveTasks{
		BaseModel:       mysql.BaseModel{Id: 1},
		Name:            "测试任务",
		TaskType:        1,
		SourceId:        data_source.NSFocusRsasSourceId,
		ScanIPRangeType: "user_input",
		ScanIPRanges:    []string{"***********/24"},
		PocScanType:     "all",
		ScanPort:        1,
		ScanType:        "quick",
		Bandwidth:       100,
		Concurrency:     10,
		Status:          4,
	}

	// 设置预期的 SQL 查询
	mockDb.ExpectQuery("SELECT id, node_id, node_task_id FROM `proactive_task_node_relations` WHERE task_id = ?").
		WithArgs(1).
		WillReturnRows(mockDb.NewRows([]string{"id", "node_id", "node_task_id"}).AddRow(1, 1, 1))

	mockDb.ExpectQuery("SELECT * FROM `data_node_configs` WHERE `node_id` = ?").
		WithArgs(1).
		WillReturnRows(mockDb.NewRows([]string{"id", "source_id", "name"}).AddRow(1, data_source.NSFocusRsasSourceId, "测试节点"))

	// 创建 Common 实例
	common := &Common{
		Base: &Base{Task: testTask},
		Sdk:  &MockActiveScanSDK{},
	}

	// 执行测试
	result := common.DelTask()

	// 验证结果
	assert.Equal(t, "删除成功", result)

	// 验证所有预期的查询都被执行
	err := mockDb.ExpectationsWereMet()
	assert.NoError(t, err)
}

func TestCommon_BatchSaveAsset(t *testing.T) {
	// 初始化 mock
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()
	mockServer.RegisterBulk()
	testcommon.SetElasticClient(mockServer.NewElasticClient())

	// 创建测试数据
	testTask := &task.ProactiveTasks{
		BaseModel: mysql.BaseModel{Id: 1},
		Name:      "测试任务",
		TaskType:  1,
		SourceId:  data_source.NSFocusRsasSourceId,
	}

	// 创建 Common 实例
	common := &Common{
		Base: &Base{Task: testTask},
		Node: &data_source.Node{
			SoftDeleteModel: mysql.SoftDeleteModel{Id: 1},
			SourceId:        data_source.NSFocusRsasSourceId,
			AreaId:          1,
		},
		proactiveTaskNodeRelations: &proactive_task_node_relations.ProactiveTaskNodeRelations{
			BaseModel: mysql.BaseModel{Id: 1},
		},
		IndexTaskAssets:    "task_assets",
		IndexProcessAssets: "process_assets",
		IndexProcessDevice: "process_device",
	}

	// 测试数据
	sourceData := []map[string]interface{}{
		{
			"id": "1",
			"ip": "***********",
		},
	}
	processData := []map[string]interface{}{
		{
			"id": "1",
			"ip": "***********",
		},
	}
	processDeviceData := []map[string]interface{}{
		{
			"id": "1",
			"ip": "***********",
		},
	}
	mockDb := testcommon.InitSqlMock()
	defer mockDb.Close()
	for i := 0; i < 1; i++ {
		mockDb.ExpectQuery("SELECT * FROM `data_node_configs` WHERE node_id = ? AND `key` = ? ORDER BY `data_node_configs`.`id` LIMIT 1").
			WithArgs(1, "network_type").
			WillReturnRows(sqlmock.NewRows([]string{"id", "node_id", "key", "value"}).AddRow(1, 1, "network_type", "1"))
	}

	// 执行测试
	err := common.BatchSaveAsset(1, sourceData, processData, processDeviceData)
	assert.NoError(t, err)
}

func TestCommon_BatchSaveVul(t *testing.T) {
	// 初始化 mock
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()
	mockServer.RegisterBulk()
	testcommon.SetElasticClient(mockServer.NewElasticClient())

	// 创建测试数据
	testTask := &task.ProactiveTasks{
		BaseModel: mysql.BaseModel{Id: 1},
		Name:      "测试任务",
		TaskType:  1,
		SourceId:  data_source.NSFocusRsasSourceId,
	}

	// 创建 Common 实例
	common := &Common{
		Base: &Base{Task: testTask},
		Node: &data_source.Node{
			SoftDeleteModel: mysql.SoftDeleteModel{Id: 1},
			SourceId:        data_source.NSFocusRsasSourceId,
			AreaId:          1,
		},
		proactiveTaskNodeRelations: &proactive_task_node_relations.ProactiveTaskNodeRelations{
			BaseModel: mysql.BaseModel{Id: 1},
		},
		IndexTaskThreats:    "task_threats",
		IndexProcessThreats: "process_threats",
	}

	// 测试数据
	sourceData := []map[string]interface{}{
		{
			"id": "1",
			"ip": "***********",
		},
	}
	processData := []map[string]interface{}{
		{
			"id": "1",
			"ip": "***********",
		},
	}
	mockDb := testcommon.InitSqlMock()
	defer mockDb.Close()
	for i := 0; i < 1; i++ {
		mockDb.ExpectQuery("SELECT * FROM `data_node_configs` WHERE node_id = ? AND `key` = ? ORDER BY `data_node_configs`.`id` LIMIT 1").
			WithArgs(1, "network_type").
			WillReturnRows(sqlmock.NewRows([]string{"id", "node_id", "key", "value"}).AddRow(1, 1, "network_type", "1"))
	}
	// 执行测试
	err := common.BatchSaveVul(1, sourceData, processData)
	assert.NoError(t, err)
}

func TestCommon_BatchSaveStaff(t *testing.T) {
	// 初始化 mock
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()
	mockServer.RegisterBulk()
	testcommon.SetElasticClient(mockServer.NewElasticClient())

	// 创建测试数据
	testTask := &task.ProactiveTasks{
		BaseModel: mysql.BaseModel{Id: 1},
		Name:      "测试任务",
		TaskType:  1,
		SourceId:  data_source.NSFocusRsasSourceId,
	}

	// 创建 Common 实例
	common := &Common{
		Base: &Base{Task: testTask},
		Node: &data_source.Node{
			SoftDeleteModel: mysql.SoftDeleteModel{Id: 1},
			SourceId:        data_source.NSFocusRsasSourceId,
			AreaId:          1,
		},
		proactiveTaskNodeRelations: &proactive_task_node_relations.ProactiveTaskNodeRelations{
			BaseModel: mysql.BaseModel{Id: 1},
		},
		IndexTaskPeople:    "test_task_people",
		IndexProcessPeople: "test_process_people",
	}

	// 测试数据
	sourceData := []map[string]interface{}{
		{
			"id":   "1",
			"name": "张三",
		},
	}
	processData := []map[string]interface{}{
		{
			"id":   "1",
			"name": "张三",
		},
	}

	// 执行测试
	err := common.BatchSaveStaff(1, sourceData, processData)
	assert.NoError(t, err)
}

func TestCommon_GetNodeConfigByKey(t *testing.T) {
	// 创建测试数据
	testTask := &task.ProactiveTasks{
		BaseModel: mysql.BaseModel{Id: 1},
		Name:      "测试任务",
		TaskType:  1,
		SourceId:  data_source.NSFocusRsasSourceId,
	}

	// 创建 Common 实例
	common := &Common{
		Base: &Base{Task: testTask},
		Node: &data_source.Node{
			SoftDeleteModel: mysql.SoftDeleteModel{Id: 1},
			SourceId:        data_source.NSFocusRsasSourceId,
			AreaId:          1,
		},
		NodeConfig: map[string]any{
			"test_key": "test_value",
		},
	}

	// 测试存在的键
	result := common.GetNodeConfigByKey("test_key")
	assert.Equal(t, "test_value", result)

	// 测试不存在的键
	result = common.GetNodeConfigByKey("non_existent_key")
	assert.Nil(t, result)
}

func TestCommon_WriteLog(t *testing.T) {
	// 创建测试数据
	testTask := &task.ProactiveTasks{
		BaseModel: mysql.BaseModel{Id: 1},
		Name:      "测试任务",
		TaskType:  1,
		SourceId:  data_source.NSFocusRsasSourceId,
	}

	// 创建 Common 实例
	common := &Common{
		Base: &Base{Task: testTask},
		Node: &data_source.Node{
			SoftDeleteModel: mysql.SoftDeleteModel{Id: 1},
			SourceId:        data_source.NSFocusRsasSourceId,
			AreaId:          1,
		},
	}

	// 执行测试 - 由于这是一个日志记录函数，我们只需要确保它不会崩溃
	common.WriteLog("测试日志消息")
}

func TestCommon_GetRedisClient(t *testing.T) {
	// 初始化 mock redis
	s := miniredis.RunT(t)
	defer s.Close()

	client := redis.NewClient(&redis.Options{Addr: s.Addr()})
	testcommon.SetRedisClient(client)

	// 创建测试数据
	testTask := &task.ProactiveTasks{
		BaseModel: mysql.BaseModel{Id: 1},
		Name:      "测试任务",
		TaskType:  1,
		SourceId:  data_source.NSFocusRsasSourceId,
	}

	// 创建 Common 实例
	common := &Common{
		Base: &Base{Task: testTask},
		Node: &data_source.Node{
			SoftDeleteModel: mysql.SoftDeleteModel{Id: 1},
			SourceId:        data_source.NSFocusRsasSourceId,
			AreaId:          1,
		},
	}

	// 执行测试
	redisClient := common.GetRedisClient()
	assert.NotNil(t, redisClient)
}

func TestCommon_RecordFailedData(t *testing.T) {
	// 创建测试数据
	testTask := &task.ProactiveTasks{
		BaseModel: mysql.BaseModel{Id: 1},
		Name:      "测试任务",
		TaskType:  1,
		SourceId:  data_source.NSFocusRsasSourceId,
	}

	// 创建 Common 实例
	common := &Common{
		Base: &Base{Task: testTask},
		Node: &data_source.Node{
			SoftDeleteModel: mysql.SoftDeleteModel{Id: 1},
			SourceId:        data_source.NSFocusRsasSourceId,
			AreaId:          1,
		},
	}

	// 执行测试
	err := common.RecordFailedData(1, "test_type", "test_content", "test_reason")
	assert.NoError(t, err)
}

func TestCommon_SyncStatus(t *testing.T) {
	// 初始化 mock
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()

	// 设置 SQL 期望

	mockDb.ExpectQuery("SELECT id, node_id, node_task_id FROM `proactive_task_node_relations` WHERE task_id = ?").
		WithArgs(1).
		WillReturnRows(mockDb.NewRows([]string{"id", "node_id", "node_task_id"}).AddRow(1, 1, 1))
	mockDb.ExpectQuery("SELECT * FROM `data_node_configs` WHERE `node_id` = ?").
		WithArgs(1).
		WillReturnRows(mockDb.NewRows([]string{"id", "node_id", "key", "value"}).AddRow(1, 1, "network_type", "1"))
	mockDb.ExpectBegin()
	mockDb.ExpectExec("UPDATE `proactive_task_node_relations` SET `state`=? WHERE task_id = ? AND node_id = ? AND node_task_id = ?").WillReturnResult(sqlmock.NewResult(1, 1))
	mockDb.ExpectCommit()
	mockDb.ExpectQuery("SELECT state FROM `proactive_task_node_relations` WHERE task_id =?").
		WithArgs(1).
		WillReturnRows(mockDb.NewRows([]string{"state"}).AddRow(1))
	mockDb.ExpectQuery("SELECT state FROM `proactive_task_node_relations` WHERE task_id =?").
		WithArgs(1).
		WillReturnRows(mockDb.NewRows([]string{"state"}).AddRow(1))
	mockDb.ExpectQuery("SELECT avg(progress) as progress FROM `proactive_task_node_relations` WHERE task_id =?").
		WithArgs(1).
		WillReturnRows(mockDb.NewRows([]string{"progress"}).AddRow(1))
	mockDb.ExpectBegin()
	mockDb.ExpectExec("UPDATE `proactive_tasks` SET `progress`=? WHERE id = ?").WillReturnResult(sqlmock.NewResult(1, 1))
	mockDb.ExpectCommit()
	// 创建测试数据
	testTask := &task.ProactiveTasks{
		BaseModel: mysql.BaseModel{Id: 1},
		Name:      "测试任务",
		TaskType:  1,
		SourceId:  data_source.NSFocusRsasSourceId,
	}

	// 创建 Common 实例
	common := &Common{
		Base: &Base{Task: testTask},
		Node: &data_source.Node{
			SoftDeleteModel: mysql.SoftDeleteModel{Id: 1},
			SourceId:        data_source.NSFocusRsasSourceId,
			AreaId:          1,
		},
		proactiveTaskNodeRelations: &proactive_task_node_relations.ProactiveTaskNodeRelations{
			BaseModel: mysql.BaseModel{Id: 1},
		},
	}

	// 创建 mock SDK
	mockSDK := &MockActiveScanSDK{}
	common.Sdk = mockSDK

	// 设置 SQL 期望
	//mockDb.ExpectBegin()
	//mockDb.ExpectExec("UPDATE `proactive_task_node_relations` SET").
	//	WithArgs(sqlmock.AnyArg(), 1, 1, 0, 1, sqlmock.AnyArg()).
	//	WillReturnResult(sqlmock.NewResult(1, 1))
	//mockDb.ExpectCommit()

	// 执行测试
	result := common.SyncStatus()
	assert.Equal(t, "状态同步成功", result)

	// 验证所有期望都被满足
	assert.NoError(t, mockDb.ExpectationsWereMet())
}

func TestCommon_SyncResult(t *testing.T) {
	// 初始化 mock
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()

	// 设置 SQL 期望
	mockDb.ExpectQuery("SELECT * FROM `data_node_configs` WHERE `node_id` = ?").
		WithArgs(1).
		WillReturnRows(mockDb.NewRows([]string{"id", "node_id", "key", "value"}).AddRow(1, 1, "network_type", "1"))

	mockDb.ExpectQuery("SELECT * FROM `proactive_tasks` WHERE id =? ORDER BY `proactive_tasks`.`id` LIMIT 1").
		WithArgs(1).
		WillReturnRows(mockDb.NewRows([]string{"id", "name", "task_type", "source_id", "scan_ip_ranges", "other_cfgs"}).AddRow(1, "test_task", 1, 1, "[]", "{}"))

	mockDb.ExpectQuery("SELECT * FROM `proactive_task_node_relations` WHERE id = ?").
		WithArgs(1).
		WillReturnRows(mockDb.NewRows([]string{"id", "task_id", "node_id", "node_task_id"}).AddRow(1, 1, 1, 1))
	mockDb.ExpectBegin()
	mockDb.ExpectExec("UPDATE `proactive_tasks` SET `created_at`=?,`updated_at`=?,`name`=?,`desc`=?,`task_type`=?,`source_id`=?,`scan_plan`=?,`scan_period`=?,`scan_time`=?,`status`=?,`progress`=?,`repeat_end_time`=?,`begin_time`=?,`end_time`=?,`scan_mode`=?,`use_seconds`=?,`scan_result`=?,`scan_ip_range_type`=?,`scan_ip_ranges`=?,`poc_scan_type`=?,`selected_pocs`=?,`scan_port`=?,`scan_type`=?,`bandwidth`=?,`concurrency`=?,`other_cfgs`=?,`user_id`=?,`is_start`=?,`sync_status`=? WHERE `id` = ?").WillReturnResult(sqlmock.NewResult(1, 1))
	mockDb.ExpectCommit()
	mockDb.ExpectBegin()
	mockDb.ExpectExec("UPDATE `proactive_tasks` SET `created_at`=?,`updated_at`=?,`name`=?,`desc`=?,`task_type`=?,`source_id`=?,`scan_plan`=?,`scan_period`=?,`scan_time`=?,`status`=?,`progress`=?,`repeat_end_time`=?,`begin_time`=?,`end_time`=?,`scan_mode`=?,`use_seconds`=?,`scan_result`=?,`scan_ip_range_type`=?,`scan_ip_ranges`=?,`poc_scan_type`=?,`selected_pocs`=?,`scan_port`=?,`scan_type`=?,`bandwidth`=?,`concurrency`=?,`other_cfgs`=?,`user_id`=?,`is_start`=?,`sync_status`=? WHERE `id` = ?").WillReturnResult(sqlmock.NewResult(1, 1))
	mockDb.ExpectCommit()
	mockDb.ExpectBegin()
	mockDb.ExpectExec("UPDATE `proactive_task_node_relations` SET `asset_sum`=? WHERE id = ?").WillReturnResult(sqlmock.NewResult(1, 1))
	mockDb.ExpectCommit()
	mockDb.ExpectBegin()
	mockDb.ExpectExec("UPDATE `proactive_task_node_relations` SET `sync_asset_status`=?,`updated_at`=? WHERE id = ?").WillReturnResult(sqlmock.NewResult(1, 1))
	mockDb.ExpectCommit()
	// 创建测试数据
	testTask := &task.ProactiveTasks{
		BaseModel: mysql.BaseModel{Id: 1},
		Name:      "测试任务",
		TaskType:  1,
		SourceId:  data_source.NSFocusRsasSourceId,
	}

	// 创建 Common 实例
	common := &Common{
		Base: &Base{Task: testTask},
		Node: &data_source.Node{
			SoftDeleteModel: mysql.SoftDeleteModel{Id: 1},
			SourceId:        data_source.NSFocusRsasSourceId,
			AreaId:          1,
		},
		proactiveTaskNodeRelations: &proactive_task_node_relations.ProactiveTaskNodeRelations{
			BaseModel: mysql.BaseModel{Id: 1},
		},
	}

	// 创建 mock SDK
	mockSDK := &MockActiveScanSDK{}
	common.Sdk = mockSDK

	// 设置 SQL 期望
	//mockDb.ExpectBegin()
	//mockDb.ExpectExec("UPDATE `proactive_tasks` SET").
	//	WithArgs(sqlmock.AnyArg(), 1).
	//	WillReturnResult(sqlmock.NewResult(1, 1))
	//mockDb.ExpectCommit()
	//
	//mockDb.ExpectBegin()
	//mockDb.ExpectExec("UPDATE `proactive_task_node_relations` SET").
	//	WithArgs(sqlmock.AnyArg(), 1).
	//	WillReturnResult(sqlmock.NewResult(1, 1))
	//mockDb.ExpectCommit()

	// 执行测试
	taskInfo := map[string]any{
		"task_id":                         uint64(1),
		"proactive_task_node_relation_id": 1,
	}
	err := common.SyncResult(common.Node, taskInfo)
	assert.NoError(t, err)

	// 验证所有期望都被满足
	assert.NoError(t, mockDb.ExpectationsWereMet())
}
