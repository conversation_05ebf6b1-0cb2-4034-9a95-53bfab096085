package source_task

import (
	"fobrain/fobrain/app/services/node/x_ray"
	"fobrain/models/mysql/task"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestXRay_TaskSetting(t *testing.T) {
	t.Run("web vul success", func(t *testing.T) {
		x := &XRay{Base: &Base{Task: &task.ProactiveTasks{Name: "test", TaskType: 1}}}
		assert.NotEmpty(t, x.TaskSetting([]any{}))
	})

	t.Run("host vul success", func(t *testing.T) {
		x := &XRay{Base: &Base{Task: &task.ProactiveTasks{Name: "test", TaskType: 2}}}
		assert.NotEmpty(t, x.TaskSetting([]any{}))
	})
}

func TestXRay_TaskParams(t *testing.T) {
	t.Run("web vul success", func(t *testing.T) {
		x := &XRay{Base: &Base{Task: &task.ProactiveTasks{Name: "test", TaskType: 1}}}
		assert.NotEmpty(t, x.TaskParams([]any{}, x_ray.New()))
	})

	t.Run("host vul success", func(t *testing.T) {
		x := &XRay{Base: &Base{Task: &task.ProactiveTasks{Name: "test", TaskType: 2}}}
		assert.NotEmpty(t, x.TaskParams([]any{}, x_ray.New()))
	})
}
