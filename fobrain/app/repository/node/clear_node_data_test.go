package node

import (
	testcommon "fobrain/fobrain/tests/common_test"
	pb "fobrain/mergeService/proto"
	"fobrain/models/mysql/merge"
	"testing"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/alicebob/miniredis/v2"
	"github.com/go-redis/redis/v8"
	"github.com/olivere/elastic/v7"
	"github.com/stretchr/testify/assert"
)

func TestClearNodeData(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	t.Run("test node don't exist", func(t *testing.T) {
		err := ClearNodeData(5)
		assert.Error(t, err)
	})

	t.Run("node exist", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()

		mockDb.ExpectQuery("SELECT * FROM `data_nodes` WHERE `id` = ? AND `data_nodes`.`deleted_at` IS NULL ORDER BY `data_nodes`.`id` LIMIT 1").
			WillReturnRows(mockDb.NewRows([]string{"id", "node_id", "source_id"}).
				AddRow(34, 5, 1))

		mockDb.ExpectQuery("SELECT * FROM `data_sources` WHERE `id` = ? ORDER BY `data_sources`.`id` LIMIT 1").
			WillReturnRows(mockDb.NewRows([]string{"id", "source_id", "has_asset_data", "has_vul_data", "has_personnel_data"}).
				AddRow(1, 1, true, true, true))

		mockDb.ExpectQuery("SELECT merge_records.*, sources.name as trigger_source_name, data_nodes.name as trigger_node_name FROM `merge_records` left join data_sources sources on sources.id = merge_records.trigger_source_id left join data_nodes data_nodes on data_nodes.id = merge_records.trigger_node_id WHERE `task_status` IN (?,?)").WithArgs(merge.MergeRecordsStatusWaiting, merge.MergeRecordsStatusRunning).
			WillReturnRows(mockDb.NewRows([]string{"id", "task_status", "merge_type", "trigger_source_id", "trigger_node_id"}).
				AddRow(0, merge.MergeRecordsStatusWaiting, merge.MergeRecordsTypeAsset, 1, 5))

		mockServer.Register("/asset,device,poc,staff/_delete_by_query", &elastic.BulkIndexByScrollResponse{
			Deleted: 1,
		})
		mockServer.Register("/process_asset,process_device,process_poc,process_staff/_delete_by_query", &elastic.BulkIndexByScrollResponse{
			Deleted: 1,
		})

		patch := gomonkey.ApplyMethodReturn(pb.GetProtoClient(), "TriggerMergeForAsset", &pb.TriggerMergeResponse{
			Success: true,
		}, nil)
		patch.ApplyMethodReturn(pb.GetProtoClient(), "TriggerMergeForVuln", &pb.TriggerMergeResponse{
			Success: true,
		}, nil)
		patch.ApplyMethodReturn(pb.GetProtoClient(), "TriggerMergeForStaff", &pb.TriggerMergeResponse{
			Success: true,
		}, nil)
		patch.ApplyMethodReturn(pb.GetProtoClient(), "TriggerMergeForDevice", &pb.TriggerMergeResponse{
			Success: true,
		}, nil)
		defer patch.Reset()

		// Setup mock redis
		s, err := miniredis.Run()
		if err != nil {
			t.Fatal(err)
		}
		defer s.Close()
		client := redis.NewClient(&redis.Options{Addr: s.Addr()})
		testcommon.SetRedisClient(client)

		err = ClearNodeData(1, "asset", "device", "vuln", "staff")
		assert.NoError(t, err)
	})
}

func TestHasDeviceData_True(t *testing.T) {
	mockDb := testcommon.InitSqlMock()
	defer mockDb.Close()

	mockDb.ExpectQuery("SELECT * FROM `data_node_configs` WHERE `node_id` = ?").
		WillReturnRows(mockDb.NewRows([]string{"id", "node_id", "key", "value", "hidden"}).
			AddRow(1, 1, "sn_field", "1", 1))

	hasDeviceData, err := hasDeviceData(1)
	assert.NoError(t, err)
	assert.True(t, hasDeviceData)
}

func TestHasDeviceData_False(t *testing.T) {
	mockDb := testcommon.InitSqlMock()
	defer mockDb.Close()

	mockDb.ExpectQuery("SELECT * FROM `data_node_configs` WHERE `node_id` = ?").
		WillReturnRows(mockDb.NewRows([]string{"id", "node_id", "key", "value", "hidden"}))

	hasDeviceData, err := hasDeviceData(1)
	assert.NoError(t, err)
	assert.False(t, hasDeviceData)
}
