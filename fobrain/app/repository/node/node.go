package node

import (
	"context"
	"errors"
	"fmt"
	"fobrain/models/elastic/source/aliyun_slb"
	"fobrain/models/elastic/source/anheng_apt"
	"fobrain/models/elastic/source/bk_cmdb_business"
	"fobrain/models/elastic/source/bk_cmdb_domain"
	"fobrain/models/elastic/source/bk_cmdb_f5"
	"fobrain/models/elastic/source/haiyi_pas"
	"fobrain/models/elastic/source/huawei_cloud"
	"fobrain/models/elastic/source/huawei_hk_cloud"
	"fobrain/models/elastic/source/huawei_manager_one"
	jumpserver "fobrain/models/elastic/source/jump_server"
	"fobrain/models/elastic/source/k8s"
	"fobrain/models/elastic/source/nsfocus_rsas"
	"fobrain/models/elastic/source/qianxin_pam"
	"fobrain/models/elastic/source/qianxin_server"
	"fobrain/models/elastic/source/qianxin_tianqing"
	"fobrain/models/elastic/source/qingteng"
	"fobrain/models/elastic/source/rizhiyi"
	"fobrain/models/elastic/source/sangfor_sip"
	"fobrain/models/elastic/source/sangfor_ssl_vpn"
	"fobrain/models/elastic/source/tenable_sc"
	"fobrain/models/elastic/source/tencent"
	tencent_ud "fobrain/models/elastic/source/tencent_ud"
	"fobrain/models/elastic/source/ucloud"
	"fobrain/models/elastic/source/weibu_onesec"
	"fobrain/models/elastic/source/youyue_cmdb"
	youyun_cmdb "fobrain/models/elastic/source/youyun"
	"fobrain/models/elastic/source/yuntu"
	"github.com/spf13/cast"
	"gorm.io/gorm"

	"fobrain/models/elastic/source/bk_cmdb_custom_module_property"
	xray "fobrain/models/elastic/source/x-ray"

	"fobrain/models/elastic/source/aliyun_cloud"

	"fobrain/models/elastic/source/d01"

	reqnode "fobrain/fobrain/app/request/node"
	"fobrain/initialize/es"
	"fobrain/initialize/mysql"
	"fobrain/models/elastic/source/bk_cmdb"
	"fobrain/models/elastic/source/changting_waf"
	"fobrain/models/elastic/source/data_import"
	"fobrain/models/elastic/source/dingtalk"
	"fobrain/models/elastic/source/file_import"
	"fobrain/models/elastic/source/foeye"
	"fobrain/models/elastic/source/foradar"
	"fobrain/models/elastic/source/mach_lake"
	qizhi_uaudithost2 "fobrain/models/elastic/source/qizhi_uaudithost"
	"fobrain/models/elastic/source/qt_cloud"
	"fobrain/models/elastic/source/weibu"
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/data_sync_child_task"
	"fobrain/models/mysql/data_sync_task"
	"fobrain/pkg/utils"

	"github.com/olivere/elastic/v7"
)

func GetData(page int, perPage int, nodeId int, taskId int, syncType int, search string) (any, int64, []map[string]string, []map[string]string, error) {
	listFields := make([]map[string]string, 0)
	listDetailFields := make([]map[string]string, 0)
	list := make([]any, 0)

	//获取节点信息
	nodeInfo := &data_source.Node{}
	err := data_source.NewNodeModel().Unscoped().Where("id = ?", nodeId).First(nodeInfo).Error

	if err != nil {
		return list, 0, listFields, listDetailFields, err
	}

	//根据节点源和是否存在taskId以及syncType同步数据类型确定查询es的那个index
	indexName, boolQuery, listFields, listDetailFields := getIndexNameBySourceIdAndTaskIdAndSyncType(nodeInfo, taskId, syncType, search)
	if indexName == "" {
		return list, 0, listFields, listDetailFields, errors.New("节点数据不存在")
	}

	count, err := es.GetCount(indexName, boolQuery)
	if err != nil || count == 0 {
		return list, 0, listFields, listDetailFields, err
	}

	result, err := es.GetEsClient().Search(indexName).From(es.GetFrom(page, perPage)).
		Size(es.GetSize(perPage)).Query(boolQuery).Do(context.TODO())

	if err != nil {
		return list, 0, listFields, listDetailFields, err
	}

	for _, hit := range result.Hits.Hits {
		list = append(list, hit.Source)
	}

	return list, count, listFields, listDetailFields, nil
}
func GetFailDetail(taskId, page, pre_page int) ([]data_sync_child_task.DataSyncChildTaskFailRecord, int64, error) {
	list, total, err := mysql.List[data_sync_child_task.DataSyncChildTaskFailRecord](page, pre_page, func(db *gorm.DB) {
		db.Where("child_task_id = ?", taskId).Order("id desc")
	})
	return list, total, err
}

// 统一的数据源接口
type DataSourceGeneralFunctions interface {
	GetIndexName(syncType int) string
	NewKeywordQuery(keyword string) *elastic.QueryStringQuery
	GetListFields(syncType int) []map[string]string
	GetListDetailFields(syncType int) []map[string]string
}

// 统一的数据源映射表
var dataSourceFunctionsMap = map[int64]DataSourceGeneralFunctions{
	data_source.SangforSSLVPNSourceId: sangfor_ssl_vpn.NewSangforSSLVPNTaskModel(),
	data_source.AnhengAPT:             anheng_apt.NewAnhengAptTaskModel(),
}

// 构建基础查询条件
func buildBaseQuery(nodeId uint64, taskId int) *elastic.BoolQuery {
	boolQuery := elastic.NewBoolQuery()
	boolQuery.Must(elastic.NewTermQuery("node_id", nodeId))
	// 合并主动扫描和接口直接拉取数据，通过task_type区分 2是主动扫描数据，不再分开建索引
	boolQuery.MustNot(elastic.NewTermQuery("task_type", 2))

	if taskId != 0 {
		boolQuery.Must(elastic.NewTermQuery("task_id", taskId))
	}

	return boolQuery
}

// 处理通用数据源逻辑
func processGeneralDataSource(dataSource DataSourceGeneralFunctions, syncType int, search string, boolQuery *elastic.BoolQuery) (string, []map[string]string, []map[string]string) {
	indexName := dataSource.GetIndexName(syncType)

	if search != "" {
		boolQuery = boolQuery.Must(dataSource.NewKeywordQuery(search))
	}

	listFields := dataSource.GetListFields(syncType)
	listDetailFields := dataSource.GetListDetailFields(syncType)

	return indexName, listFields, listDetailFields
}

func getIndexNameListFieldsBySourceId(nodeInfo *data_source.Node, taskId int, syncType int, search string) (string, *elastic.BoolQuery, []map[string]string, []map[string]string) {
	var listFields, listDetailFields = make([]map[string]string, 0), make([]map[string]string, 0)
	var indexName string

	boolQuery := buildBaseQuery(nodeInfo.Id, taskId)

	dataSource, exists := dataSourceFunctionsMap[int64(nodeInfo.SourceId)]
	if !exists {
		return "", nil, listFields, listDetailFields
	}
	indexName, listFields, listDetailFields = processGeneralDataSource(dataSource, syncType, search, boolQuery)

	if nodeInfo.SourceId == data_source.FoeyeSourceId || nodeInfo.SourceId == data_source.D01SourceId {
		config, _ := data_source.NewNodeConfigModel().GetNodeConfig(nodeInfo.Id)
		if config != nil {
			version := cast.ToString(config["version"])
			if version == "v2" {
				if nodeInfo.SourceId == data_source.FoeyeSourceId {
					if syncType == data_sync_task.SyncAsset {
						indexName = foeye.FoeyeV2SourceTaskAssetsIndex
						listFields = foeye.NewFoeyeTaskAssetsModel().GetFoeyeAssetListFieldsV2()
						listDetailFields = foeye.NewFoeyeTaskAssetsModel().GetFoeyeAssetListDetailFields()
					} else if syncType == data_sync_task.SyncThreat {
						indexName = foeye.FoeyeV2SourceTaskThreatsIndex
					}
				} else if nodeInfo.SourceId == data_source.D01SourceId {
					if syncType == data_sync_task.SyncAsset {
						indexName = d01.D01V2SourceTaskAssetsIndex
						listFields = foeye.NewFoeyeTaskAssetsModel().GetFoeyeAssetListFieldsV2()
						listDetailFields = foeye.NewFoeyeTaskAssetsModel().GetFoeyeAssetListDetailFields()
					} else if syncType == data_sync_task.SyncThreat {
						indexName = d01.D01V2SourceTaskThreatsIndex
					}
				}
			}
		}
	}
	return indexName, boolQuery, listFields, listDetailFields
}

// 根据源id、任务id、同步数据类型获取对应的indexName
func getIndexNameBySourceIdAndTaskIdAndSyncType(nodeInfo *data_source.Node, taskId int, syncType int, search string) (string, *elastic.BoolQuery, []map[string]string, []map[string]string) {
	indexName, boolQuery, listFields, listDetailFields := getIndexNameListFieldsBySourceId(nodeInfo, taskId, syncType, search)
	if indexName != "" {
		return indexName, boolQuery, listFields, listDetailFields
	}

	boolQuery = elastic.NewBoolQuery()
	boolQuery.Must(elastic.NewTermQuery("node_id", nodeInfo.Id))
	// 合并主动扫描和接口直接拉取数据，通过task_type区分 2是主动扫描数据，不再分开建索引
	boolQuery.MustNot(elastic.NewTermQuery("task_type", 2))

	version := ""
	if nodeInfo.SourceId == data_source.FoeyeSourceId || nodeInfo.SourceId == data_source.D01SourceId {
		config, _ := data_source.NewNodeConfigModel().GetNodeConfig(nodeInfo.Id)
		if config != nil {
			t, ok := config["version"].(string)
			if ok {
				version = t
			}
		}
	}

	switch int(nodeInfo.SourceId) {
	// 2025年4月9日 此后新加数据源不再保存原始数据的索引，只保存带task的索引
	case data_source.WeibuOnesecSourceId:
		if syncType == data_sync_task.SyncAsset {
			t := weibu_onesec.NewWeibuOneSecTaskAssets()
			if taskId != 0 {
				indexName = t.IndexName()
				boolQuery.Must(elastic.NewTermQuery("task_id", taskId))
			}
			if search != "" {
				boolQuery = boolQuery.Must(t.NewKeywordQuery(search))
			}
			listFields = t.GetAssetListFields()
			listDetailFields = t.GetAssetListDetailFields()
		}

	case data_source.PublicHuaweiEcsSourceId:
		if syncType == data_sync_task.SyncAsset {
			t := huawei_cloud.NewHuaweiCloudEcsTaskAssets()
			if taskId != 0 {
				indexName = t.IndexName()
				boolQuery.Must(elastic.NewTermQuery("task_id", taskId))
			}
			if search != "" {
				boolQuery = boolQuery.Must(t.NewKeywordQuery(search))
			}
			listFields = t.GetAssetListFields()
			listDetailFields = t.GetAssetListDetailFields()
		}
	case data_source.PublicTencentCVMSourceId:
		if syncType == data_sync_task.SyncAsset {
			t := tencent.NewTencentCVMTaskAssets()
			if taskId != 0 {
				indexName = t.IndexName()
				boolQuery.Must(elastic.NewTermQuery("task_id", taskId))
			}
			if search != "" {
				boolQuery = boolQuery.Must(t.NewKeywordQuery(search))
			}
			listFields = t.GetAssetListFields()
			listDetailFields = t.GetAssetListDetailFields()
		}
	case data_source.PublicTencentCLBSourceId:
		if syncType == data_sync_task.SyncAsset {
			t := tencent.NewTencentCLBTaskAssets()
			if taskId != 0 {
				indexName = t.IndexName()
				boolQuery.Must(elastic.NewTermQuery("task_id", taskId))
			}
			if search != "" {
				boolQuery = boolQuery.Must(t.NewKeywordQuery(search))
			}
			listFields = t.GetAssetListFields()
			listDetailFields = t.GetAssetListDetailFields()
		}
		// =====
	case data_source.AliyunSLBSourceId:
		if syncType == data_sync_task.SyncAsset {
			indexName = aliyun_slb.NewTaskAssetsModel().IndexName()
			if taskId != 0 {
				indexName = aliyun_slb.NewTaskAssetsModel().IndexName()
				boolQuery.Must(elastic.NewTermQuery("task_id", taskId))
			}
			if search != "" {
				boolQuery = boolQuery.Must(aliyun_slb.NewTaskAssetsModel().NewKeywordQuery(search))
			}
			listFields = aliyun_slb.NewTaskAssetsModel().GetAssetListFields()
			listDetailFields = aliyun_slb.NewTaskAssetsModel().GetAssetListDetailFields()

		}
	case data_source.YuntuSourceId:
		if syncType == data_sync_task.SyncAsset {
			t := yuntu.NewTaskAssetsModel()
			indexName = t.IndexName()
			if taskId != 0 {
				indexName = t.IndexName()
				boolQuery.Must(elastic.NewTermQuery("task_id", taskId))
			}
			if search != "" {
				boolQuery = boolQuery.Must(t.NewKeywordQuery(search))
			}
			listFields = t.GetAssetListFields()
			listDetailFields = t.GetAssetListDetailFields()

		} else if syncType == data_sync_task.SyncThreat {
			t := yuntu.NewTaskThreatsModel()
			indexName = t.IndexName()

			if taskId != 0 {
				boolQuery.Must(elastic.NewTermQuery("task_id", taskId))
				indexName = t.IndexName()
			}
			if search != "" {
				boolQuery = boolQuery.Must(t.NewKeywordQuery(search))
			}
			listFields = t.GetThreatListFields()
			listDetailFields = t.GetThreatListDetailFields()

		}
	case data_source.RizhiyiSourceId:
		if syncType == data_sync_task.SyncAsset {
			indexName = rizhiyi.NewTaskAssetsModel().IndexName()
			if taskId != 0 {
				indexName = rizhiyi.NewTaskAssetsModel().IndexName()
				boolQuery.Must(elastic.NewTermQuery("task_id", taskId))
			}
			if search != "" {
				boolQuery = boolQuery.Must(rizhiyi.NewTaskAssetsModel().NewKeywordQuery(search))
			}
			listFields = rizhiyi.NewTaskAssetsModel().GetAssetListFields()
			listDetailFields = rizhiyi.NewTaskAssetsModel().GetAssetListDetailFields()
		} else if syncType == data_sync_task.SyncPeople {
			indexName = rizhiyi.NewEmployeesModel().IndexName()
			if taskId != 0 {
				boolQuery.Must(elastic.NewTermQuery("task_id", taskId))
				indexName = rizhiyi.NewTaskEmployeesModel().IndexName()
			}
			if search != "" {
				boolQuery = boolQuery.Must(rizhiyi.NewEmployeesModel().NewKeywordQuery(search))
			}
			listFields = rizhiyi.NewEmployeesModel().GetEmployeeListFields()
			listDetailFields = rizhiyi.NewEmployeesModel().GetEmployeeListDetailFields()
		}
	case data_source.QianxinServerSourceId:
		if syncType == data_sync_task.SyncAsset {
			indexName = qianxin_server.NewTaskAssetsModel().IndexName()
			if taskId != 0 {
				indexName = qianxin_server.NewTaskAssetsModel().IndexName()
				boolQuery.Must(elastic.NewTermQuery("task_id", taskId))
			}
			if search != "" {
				boolQuery = boolQuery.Must(qianxin_server.NewTaskAssetsModel().NewKeywordQuery(search))
			}
			listFields = qianxin_server.NewTaskAssetsModel().GetAssetListFields()
			listDetailFields = qianxin_server.NewTaskAssetsModel().GetAssetListDetailFields()
		}
	case data_source.UCloudSourceId:
		if syncType == data_sync_task.SyncAsset {
			indexName = ucloud.NewTaskAssetsModel().IndexName()
			if taskId != 0 {
				indexName = ucloud.NewTaskAssetsModel().IndexName()
				boolQuery.Must(elastic.NewTermQuery("task_id", taskId))
			}

			if search != "" {
				boolQuery = boolQuery.Must(ucloud.NewTaskAssetsModel().NewKeywordQuery(search))
			}
			//维护前端显示字段
			//"list_fields":[{"name":"IP","key":"ip"}],
			//"list_detail_fields":[{"name":"IP","key":"ip"}],
			listFields = ucloud.NewTaskAssetsModel().GetAssetListFields()
			listDetailFields = ucloud.NewTaskAssetsModel().GetAssetListDetailFields()
		}
	case data_source.FoeyeSourceId:
		if syncType == data_sync_task.SyncAsset {
			t := foeye.NewFoeyeTaskAssetsModel()
			indexName = t.IndexName()
			if taskId != 0 {
				indexName = t.IndexName()
				boolQuery.Must(elastic.NewTermQuery("task_id", taskId))
			}

			if search != "" {
				boolQuery = boolQuery.Must(t.NewKeywordQuery(search))
			}
			//维护前端显示字段
			//"list_fields":[{"name":"IP","key":"ip"}],
			//"list_detail_fields":[{"name":"IP","key":"ip"}],
			listFields = t.GetFoeyeAssetListFields()
			listDetailFields = t.GetFoeyeAssetListDetailFields()
			if version == "v2" {
				indexName = foeye.FoeyeV2SourceTaskAssetsIndex
				listFields = t.GetFoeyeAssetListFieldsV2()
				listDetailFields = t.GetFoeyeAssetListDetailFields()
			}
		} else if syncType == data_sync_task.SyncThreat {
			t := foeye.NewFoeyeTaskThreatsModel()
			indexName = t.IndexName()

			if taskId != 0 {
				boolQuery.Must(elastic.NewTermQuery("task_id", taskId))
				indexName = t.IndexName()
			}

			if search != "" {
				boolQuery = boolQuery.Must(t.NewKeywordQuery(search))
			}
			listFields = t.GetThreatListFields()
			listDetailFields = t.GetThreatListDetailFields()
			if version == "v2" {
				indexName = foeye.FoeyeV2SourceTaskThreatsIndex
			}
		}
	case data_source.D01SourceId:
		if syncType == data_sync_task.SyncAsset {
			t := d01.NewTaskAssetsModel()
			indexName = t.IndexName()
			if taskId != 0 {
				indexName = t.IndexName()
				boolQuery.Must(elastic.NewTermQuery("task_id", taskId))
			}
			if search != "" {
				boolQuery = boolQuery.Must(t.NewKeywordQuery(search))
			}
			//维护前端显示字段
			//"list_fields":[{"name":"IP","key":"ip"}],
			//"list_detail_fields":[{"name":"IP","key":"ip"}],
			listFields = t.GetAssetListFields()
			listDetailFields = t.GetAssetListDetailFields()
			if version == "v2" {
				indexName = d01.D01V2SourceTaskAssetsIndex
				listFields = foeye.NewFoeyeTaskAssetsModel().GetFoeyeAssetListFieldsV2()
				listDetailFields = foeye.NewFoeyeTaskAssetsModel().GetFoeyeAssetListDetailFields()
			}
		} else if syncType == data_sync_task.SyncThreat {
			t := d01.NewTaskThreatsModel()
			indexName = t.IndexName()
			if taskId != 0 {
				boolQuery.Must(elastic.NewTermQuery("task_id", taskId))
				indexName = t.IndexName()
			}
			if search != "" {
				boolQuery = boolQuery.Must(t.NewKeywordQuery(search))
			}
			listFields = t.GetThreatListFields()
			listDetailFields = t.GetThreatListDetailFields()
			if version == "v2" {
				indexName = d01.D01V2SourceTaskThreatsIndex
			}
		}
	case data_source.ForadarSourceId:
		if syncType == data_sync_task.SyncAsset {
			t := foradar.NewForadarTaskAssetsModel()
			indexName = t.IndexName()
			if taskId != 0 {
				boolQuery.Must(elastic.NewTermQuery("task_id", taskId))
				indexName = t.IndexName()
			}
			if search != "" {
				boolQuery = boolQuery.Must(t.NewKeywordQuery(search))
			}
			listFields = t.GetAssetListFields()
			listDetailFields = t.GetAssetListDetailFields()
		} else if syncType == data_sync_task.SyncThreat {
			t := foradar.NewForadarTaskThreatsModel()
			indexName = t.IndexName()
			if taskId != 0 {
				boolQuery.Must(elastic.NewTermQuery("task_id", taskId))
				indexName = t.IndexName()
			}
			if search != "" {
				boolQuery = boolQuery.Must(t.NewKeywordQuery(search))
			}
			listFields = t.GetThreatListFields()
			listDetailFields = t.GetThreatListDetailFields()
		}
	case data_source.QTCloudSourceId:
		if syncType == data_sync_task.SyncAsset {
			indexName = qt_cloud.NewQTCloudTaskAssetsModel().IndexName()
			if taskId != 0 {
				boolQuery.Must(elastic.NewTermQuery("task_id", taskId))
				indexName = qt_cloud.NewQTCloudTaskAssetsModel().IndexName()
			}
			if search != "" {
				boolQuery = boolQuery.Must(qt_cloud.NewQTCloudTaskAssetsModel().NewKeywordQuery(search))
			}
			listFields = qt_cloud.NewQTCloudTaskAssetsModel().GetQTCloudAssetListFields()
			listDetailFields = qt_cloud.NewQTCloudTaskAssetsModel().GetQTCloudAssetListDetailFields()
		} else if syncType == data_sync_task.SyncThreat {
			indexName = qt_cloud.NewQTCloudTaskThreatsModel().IndexName()
			if taskId != 0 {
				boolQuery.Must(elastic.NewTermQuery("task_id", taskId))
				indexName = qt_cloud.NewQTCloudTaskThreatsModel().IndexName()
			}
			if search != "" {
				boolQuery = boolQuery.Must(qt_cloud.NewQTCloudTaskThreatsModel().NewKeywordQuery(search))
			}
			listFields = qt_cloud.NewQTCloudTaskThreatsModel().GetQTCloudThreatListFields()
			listDetailFields = qt_cloud.NewQTCloudTaskThreatsModel().GetQTCloudThreatListDetailFields()
		} else if syncType == data_sync_task.SyncPeople {
			indexName = qt_cloud.NewQTCloudLinuxEmployeesModel().IndexName()
			if taskId != 0 {
				boolQuery.Must(elastic.NewTermQuery("task_id", taskId))
				indexName = qt_cloud.NewQTCloudTaskLinuxEmployeesModel().IndexName()
			}
			if search != "" {
				boolQuery = boolQuery.Must(qt_cloud.NewQTCloudLinuxEmployeesModel().NewKeywordQuery(search))
			}
			listFields = qt_cloud.NewQTCloudLinuxEmployeesModel().GetQTCloudPeopleListFields()
			listDetailFields = qt_cloud.NewQTCloudLinuxEmployeesModel().GetQTCloudPeopleListDetailFields()
		}
	case data_source.DingTalkSourceId:
		if syncType == data_sync_task.SyncPeople {
			t := dingtalk.NewDingtalkTaskEmployeesModel()
			indexName = t.IndexName()
			if taskId != 0 {
				boolQuery.Must(elastic.NewTermQuery("task_id", taskId))
				indexName = t.IndexName()
			}
			if search != "" {
				boolQuery = boolQuery.Must(t.NewKeywordQuery(search))
			}
			listFields = t.GetDingTalkPeopleListFields()
			listDetailFields = t.GetDingTalkPeopleListDetailFields()
		}
	case data_source.BKCmdbSourceId:
		if syncType == data_sync_task.SyncAsset {
			t := bk_cmdb.NewBKCmdbTaskAssetsModel()
			indexName = t.IndexName()
			if taskId != 0 {
				boolQuery.Must(elastic.NewTermQuery("task_id", taskId))
				indexName = t.IndexName()
			}
			if search != "" {
				boolQuery = boolQuery.Must(t.NewKeywordQuery(search))
			}
			listFields = t.GetAssetListFields()
			listDetailFields = t.GetAssetListDetailFields()
		} else if syncType == data_sync_task.SyncPeople {
			t := bk_cmdb.NewBKCmdbTaskEmployeesModel()
			indexName = t.IndexName()
			if taskId != 0 {
				boolQuery.Must(elastic.NewTermQuery("task_id", taskId))
				indexName = t.IndexName()
			}
			if search != "" {
				boolQuery = boolQuery.Must(t.NewKeywordQuery(search))
			}
			listFields = t.GetPeopleListFields()
			listDetailFields = t.GetPeopleListDetailFields()
		}
	case data_source.FileImportSourceId:
		if syncType == data_sync_task.SyncAsset {
			t := file_import.NewFileImportTaskAssetsModel()
			indexName = t.IndexName()
			if taskId != 0 {
				boolQuery.Must(elastic.NewTermQuery("task_id", taskId))
				indexName = t.IndexName()
			}
			if search != "" {
				boolQuery = boolQuery.Must(t.NewKeywordQuery(search))
			}
			listFields = t.GetAssetListFields()
			listDetailFields = t.GetAssetListDetailFields()
		} else if syncType == data_sync_task.SyncThreat {
			t := file_import.NewFileImportTaskThreatsModel()
			indexName = t.IndexName()
			if taskId != 0 {
				boolQuery.Must(elastic.NewTermQuery("task_id", taskId))
				indexName = t.IndexName()
			}
			if search != "" {
				boolQuery = boolQuery.Must(t.NewKeywordQuery(search))
			}
			listFields = t.GetThreatListFields()
			listDetailFields = t.GetThreatListDetailFields()
		} else if syncType == data_sync_task.SyncPeople {
			t := file_import.NewFileImportTaskPeoplesModel()
			indexName = t.IndexName()
			if taskId != 0 {
				boolQuery.Must(elastic.NewTermQuery("task_id", taskId))
				indexName = t.IndexName()
			}
			if search != "" {
				boolQuery = boolQuery.Must(t.NewKeywordQuery(search))
			}
			listFields = t.GetPeopleListFields()
			listDetailFields = t.GetPeopleListDetailFields()
		}
	case data_source.QiZhiUAuditHostSourceId:
		if syncType == data_sync_task.SyncAsset {
			indexName = qizhi_uaudithost2.NewQiZhiUAuditHostAssetsModel().IndexName()
			if taskId != 0 {
				indexName = qizhi_uaudithost2.NewQiZhiUAuditHostTaskAssetsModel().IndexName()
				boolQuery.Must(elastic.NewTermQuery("task_id", taskId))
			}
			if search != "" {
				boolQuery = boolQuery.Must(qizhi_uaudithost2.NewQiZhiUAuditHostAssetsModel().NewKeywordQuery(search))
			}
			listFields = qizhi_uaudithost2.NewQiZhiUAuditHostAssetsModel().GetAssetListFields()
			listDetailFields = qizhi_uaudithost2.NewQiZhiUAuditHostAssetsModel().GetAssetListDetailFields()
		} else if syncType == data_sync_task.SyncPeople {

		}
	case data_source.AliYunCloudSourceId:
		if syncType == data_sync_task.SyncAsset {

		} else if syncType == data_sync_task.SyncThreat {
			threatModel := aliyun_cloud.NewAliYunCloudTaskThreatsModel()
			indexName = threatModel.IndexName()
			if taskId != 0 {
				boolQuery.Must(elastic.NewTermQuery("task_id", taskId))
				indexName = aliyun_cloud.NewAliYunCloudTaskThreatsModel().IndexName()
			}
			if search != "" {
				boolQuery = boolQuery.Must(threatModel.NewKeywordQuery(search))
			}
			listFields = threatModel.GetThreatListFields()
			listDetailFields = threatModel.GetThreatListDetailFields()
		}
	case data_source.WeiBuSourceId:
		if syncType == data_sync_task.SyncAsset {
			assetModel := weibu.NewWeibuTaskAssetsModel()
			indexName = assetModel.IndexName()
			if taskId != 0 {
				boolQuery.Must(elastic.NewTermQuery("task_id", taskId))
				indexName = weibu.NewWeibuTaskAssetsModel().IndexName()
			}
			if search != "" {
				boolQuery = boolQuery.Must(assetModel.NewKeywordQuery(search))
			}
			listFields = assetModel.GetWeibuAssetListFields()
			listDetailFields = assetModel.GetWeibuAssetListDetailFields()
		} else if syncType == data_sync_task.SyncThreat {
			threatModel := weibu.NewWeibuTaskThreatsModel()
			indexName = threatModel.IndexName()
			if taskId != 0 {
				boolQuery.Must(elastic.NewTermQuery("task_id", taskId))
				indexName = weibu.NewWeibuTaskThreatsModel().IndexName()
			}
			if search != "" {
				boolQuery = boolQuery.Must(threatModel.NewKeywordQuery(search))
			}
			listFields = threatModel.GetWeibuThreatListFields()
			listDetailFields = threatModel.GetWeibuThreatListDetailFields()
		}
	case data_source.ChangTingWAFSourceId:
		if syncType == data_sync_task.SyncAsset {
			ctModel := changting_waf.NewChangtingWafTaskAssetsModel()
			indexName = ctModel.IndexName()
			if taskId != 0 {
				boolQuery.Must(elastic.NewTermQuery("task_id", taskId))
				indexName = ctModel.IndexName()
			}
			if search != "" {
				boolQuery = boolQuery.Must(ctModel.NewKeywordQuery(search))
			}
			listFields = ctModel.GetCTWafAssetListFields()
			listDetailFields = ctModel.GetCTWafAssetListDetailFields()
		}
	case data_source.MachLakeSourceId:
		if syncType == data_sync_task.SyncAsset {
			ctModel := mach_lake.NewMachLakeTaskAssetsModel()
			indexName = ctModel.IndexName()
			if taskId != 0 {
				boolQuery.Must(elastic.NewTermQuery("task_id", taskId))
				indexName = mach_lake.NewMachLakeTaskAssetsModel().IndexName()
			}
			if search != "" {
				boolQuery = boolQuery.Must(ctModel.NewKeywordQuery(search))
			}
			listFields = ctModel.GetMachLakeAssetListFields()
			listDetailFields = ctModel.GetMachLakeAssetListDetailFields()
		}
	case data_source.XRaySourceId:
		if syncType == data_sync_task.SyncThreat {
			threatModel := xray.NewTaskThreatsModel()
			indexName = threatModel.IndexName()
			if taskId != 0 {
				boolQuery.Must(elastic.NewTermQuery("task_id", taskId))
				indexName = xray.NewTaskThreatsModel().IndexName()
			}
			if search != "" {
				boolQuery = boolQuery.Must(threatModel.NewKeywordQuery(search))
			}
			listFields = threatModel.GetThreatListFields()
			listDetailFields = threatModel.GetThreatListDetailFields()
		} else if syncType == data_sync_task.SyncAsset {
			assetModel := xray.NewTaskAssetsModel()
			indexName = assetModel.IndexName()
			if taskId != 0 {
				boolQuery.Must(elastic.NewTermQuery("task_id", taskId))
				indexName = xray.NewTaskAssetsModel().IndexName()
			}

			if search != "" {
				boolQuery = boolQuery.Must(assetModel.NewKeywordQuery(search))
			}

			listFields = assetModel.GetAssetListFields()
			listDetailFields = assetModel.GetAssetListDetailFields()
		}
	case data_source.BKCmdbCustomVmMachineSourceId:
		if syncType == data_sync_task.SyncAsset {
			assetModel := bk_cmdb_custom_module_property.NewBkCmdbCustomTaskVmMachineAssetsModel()
			indexName = assetModel.IndexName()
			if taskId != 0 {
				boolQuery.Must(elastic.NewTermQuery("task_id", taskId))
				indexName = bk_cmdb_custom_module_property.NewBkCmdbCustomTaskVmMachineAssetsModel().IndexName()
			}

			if search != "" {
				boolQuery = boolQuery.Must(assetModel.NewKeywordQuery(search))
			}

			listFields = assetModel.GetAssetListFields()
			listDetailFields = assetModel.GetAssetListDetailFields()
		}
	case data_source.BKCmdbCustomCloudEcsSourceId:
		if syncType == data_sync_task.SyncAsset {
			assetModel := bk_cmdb_custom_module_property.NewBkCmdbCustomTaskEcsAssetsModel()
			indexName = assetModel.IndexName()
			if taskId != 0 {
				boolQuery.Must(elastic.NewTermQuery("task_id", taskId))
				indexName = bk_cmdb_custom_module_property.NewBkCmdbCustomTaskEcsAssetsModel().IndexName()
			}

			if search != "" {
				boolQuery = boolQuery.Must(assetModel.NewKeywordQuery(search))
			}

			listFields = assetModel.GetAssetListFields()
			listDetailFields = assetModel.GetAssetListDetailFields()
		}
	case data_source.HuaweiHkCloudSourceId:
		if syncType == data_sync_task.SyncAsset {
			indexName = huawei_hk_cloud.NewHuaweiHkCloudAssetsModel().IndexName()
			if taskId != 0 {
				indexName = huawei_hk_cloud.NewHuaweiHkCloudTaskAssetsModel().IndexName()
				boolQuery.Must(elastic.NewTermQuery("task_id", taskId))
			}
			if search != "" {
				boolQuery = boolQuery.Must(huawei_hk_cloud.NewHuaweiHkCloudAssetsModel().NewKeywordQuery(search))
			}
			listFields = huawei_hk_cloud.NewHuaweiHkCloudAssetsModel().GetAssetListFields()
			listDetailFields = huawei_hk_cloud.NewHuaweiHkCloudAssetsModel().GetAssetListDetailFields()
		}
	case data_source.BKCmdbCustomDomainSourceId:
		if syncType == data_sync_task.SyncAsset {
			assetModel := bk_cmdb_domain.NewBKCmdbDomainTaskAssetsModel()
			indexName = assetModel.IndexName()
			if taskId != 0 {
				boolQuery.Must(elastic.NewTermQuery("task_id", taskId))
				indexName = assetModel.IndexName()
			}

			if search != "" {
				boolQuery = boolQuery.Must(assetModel.NewKeywordQuery(search))
			}

			listFields = assetModel.GetAssetListFields()
			listDetailFields = assetModel.GetAssetListDetailFields()
		}
	case data_source.BKCmdbCustomBusinessSourceId:
		if syncType == data_sync_task.SyncAsset {
			assetModel := bk_cmdb_business.NewBKCmdbBusinessTaskAssetsModel()
			indexName = assetModel.IndexName()
			if taskId != 0 {
				boolQuery.Must(elastic.NewTermQuery("task_id", taskId))
				indexName = bk_cmdb_business.NewBKCmdbBusinessTaskAssetsModel().IndexName()
			}

			if search != "" {
				boolQuery = boolQuery.Must(assetModel.NewKeywordQuery(search))
			}

			listFields = assetModel.GetAssetListFields()
			listDetailFields = assetModel.GetAssetListDetailFields()
		}
	case data_source.BKCmdbCustomF5VsSourceId:
		if syncType == data_sync_task.SyncAsset {
			assetModel := bk_cmdb_f5.NewBkCmdbF5VsTaskAssetsModel()
			indexName = assetModel.IndexName()
			if taskId != 0 {
				boolQuery.Must(elastic.NewTermQuery("task_id", taskId))
				indexName = assetModel.IndexName()
			}

			if search != "" {
				boolQuery = boolQuery.Must(assetModel.NewKeywordQuery(search))
			}

			listFields = assetModel.GetAssetListFields()
			listDetailFields = assetModel.GetAssetListDetailFields()
		}
	case data_source.BKCmdbCustomF5PoolSourceId:
		if syncType == data_sync_task.SyncAsset {
			assetModel := bk_cmdb_f5.NewBkCmdbF5PoolTaskAssetsModel()
			indexName = assetModel.IndexName()
			if taskId != 0 {
				boolQuery.Must(elastic.NewTermQuery("task_id", taskId))
				indexName = assetModel.IndexName()
			}

			if search != "" {
				boolQuery = boolQuery.Must(assetModel.NewKeywordQuery(search))
			}

			listFields = assetModel.GetAssetListFields()
			listDetailFields = assetModel.GetAssetListDetailFields()
		}
	case data_source.SangforSIPSourceId:
		if syncType == data_sync_task.SyncAsset {
			assetModel := sangfor_sip.NewSangforSipTaskAssetsModel()
			indexName = assetModel.IndexName()
			if taskId != 0 {
				boolQuery.Must(elastic.NewTermQuery("task_id", taskId))
				indexName = sangfor_sip.NewSangforSipTaskAssetsModel().IndexName()
			}

			if search != "" {
				boolQuery = boolQuery.Must(assetModel.NewKeywordQuery(search))
			}
			listFields = assetModel.GetSangforSIPAssetListFields()
			listDetailFields = assetModel.GetSangforSIPAssetListDetailFields()
		} else if syncType == data_sync_task.SyncThreat {
			indexName = sangfor_sip.NewSangforSipTaskThreatsModel().IndexName()
			if taskId != 0 {
				boolQuery.Must(elastic.NewTermQuery("task_id", taskId))
				indexName = sangfor_sip.NewSangforSipTaskThreatsModel().IndexName()
			}
			if search != "" {
				boolQuery = boolQuery.Must(sangfor_sip.NewSangforSipTaskThreatsModel().NewKeywordQuery(search))
			}
			listFields = sangfor_sip.NewSangforSipTaskThreatsModel().GetSangforSIPThreatListFields()
			listDetailFields = sangfor_sip.NewSangforSipTaskThreatsModel().GetSangforSIPThreatListDetailFields()
		}
	case data_source.NSFocusRsasSourceId:
		if syncType == data_sync_task.SyncAsset {
			// not support
		} else if syncType == data_sync_task.SyncThreat {
			indexName = nsfocus_rsas.NewNsfocusRsasTaskThreatsModel().IndexName()
			m := nsfocus_rsas.NewNsfocusRsasTaskThreatsModel()
			if taskId != 0 {
				boolQuery.Must(elastic.NewTermQuery("task_id", taskId))
				indexName = m.IndexName()
			}
			if search != "" {
				boolQuery = boolQuery.Must(m.NewKeywordQuery(search))
			}
			listFields = m.GetNsfocusRsasThreatListFields()
			listDetailFields = m.GetNsfocusRsasThreatListDetailFields()
		}
	case data_source.JumpServerSourceId:
		if syncType == data_sync_task.SyncAsset {
			indexName = jumpserver.NewJumpServerAssetsTaskModel().IndexName()
			if taskId != 0 {
				boolQuery.Must(elastic.NewTermQuery("task_id", taskId))
				indexName = jumpserver.NewJumpServerAssetsTaskModel().IndexName()
			}
			if search != "" {
				boolQuery = boolQuery.Must(jumpserver.NewJumpServerAssetsTaskModel().NewKeywordQuery(search))
			}
			listFields = jumpserver.NewJumpServerAssetsTaskModel().GetJumpServerAssetListFields()
			listDetailFields = jumpserver.NewJumpServerAssetsTaskModel().GetJumpServerAssetListDetailFields()
		}
	case data_source.K8SSourceId:
		if syncType == data_sync_task.SyncAsset {
			indexName = k8s.NewK8SAssetsTaskModel().IndexName()
			if taskId != 0 {
				boolQuery.Must(elastic.NewTermQuery("task_id", taskId))
			}
			if search != "" {
				boolQuery = boolQuery.Must(k8s.NewK8SAssetsTaskModel().NewKeywordQuery(search))
			}
			listFields = k8s.NewK8SAssetsTaskModel().GetK8SAssetListFields()
			listDetailFields = k8s.NewK8SAssetsTaskModel().GetK8SAssetListDetailFields()
		}
	case data_source.HaiyiPasSourceId:
		if syncType == data_sync_task.SyncAsset {
			indexName = haiyi_pas.NewPasAssetsTaskModel().IndexName()
			if taskId != 0 {
				boolQuery.Must(elastic.NewTermQuery("task_id", taskId))
			}
			if search != "" {
				boolQuery = boolQuery.Must(haiyi_pas.NewPasAssetsTaskModel().NewKeywordQuery(search))
			}
			listFields = haiyi_pas.NewPasAssetsTaskModel().GetPasAssetListFields()
			listDetailFields = haiyi_pas.NewPasAssetsTaskModel().GetPasAssetListDetailFields()
		}
	case data_source.TenableScSourceId:
		if syncType == data_sync_task.SyncThreat {
			indexName = tenable_sc.NewScVulTaskModel().IndexName()
			if taskId != 0 {
				boolQuery.Must(elastic.NewTermQuery("task_id", taskId))
			}
			if search != "" {
				boolQuery = boolQuery.Must(tenable_sc.NewScVulTaskModel().NewKeywordQuery(search))
			}
			listFields = tenable_sc.NewScVulTaskModel().GetPasAssetListFields()
			listDetailFields = tenable_sc.NewScVulTaskModel().GetPasAssetListDetailFields()
		}
	case data_source.QiAnXinTianQingSourceId:
		if syncType == data_sync_task.SyncAsset {
			indexName = qianxin_tianqing.NewTianQingAssetsTaskModel().IndexName()
			if taskId != 0 {
				boolQuery.Must(elastic.NewTermQuery("task_id", taskId))
			}
			if search != "" {
				boolQuery = boolQuery.Must(qianxin_tianqing.NewTianQingAssetsTaskModel().NewKeywordQuery(search))
			}
			listFields = qianxin_tianqing.NewTianQingAssetsTaskModel().GetTianQingAssetListFields()
			listDetailFields = qianxin_tianqing.NewTianQingAssetsTaskModel().GetTianQingAssetListDetailFields()

		}
	case data_source.HuaweiManagerOneSourceId:
		if syncType == data_sync_task.SyncAsset {
			indexName = huawei_manager_one.NewHuaweiManagerOneAssetsTaskModel().IndexName()
			if taskId != 0 {
				boolQuery.Must(elastic.NewTermQuery("task_id", taskId))
			}
			if search != "" {
				boolQuery = boolQuery.Must(huawei_manager_one.NewHuaweiManagerOneAssetsTaskModel().NewKeywordQuery(search))
			}
			listFields = huawei_manager_one.NewHuaweiManagerOneAssetsTaskModel().GetHuaweiManagerOneAssetListFields()
			listDetailFields = huawei_manager_one.NewHuaweiManagerOneAssetsTaskModel().GetHuaweiManagerOneAssetListDetailFields()

		}
	case data_source.TencentUDSourceId:
		if syncType == data_sync_task.SyncPeople {
			indexName = tencent_ud.NewTencentUDStaffTaskModel().IndexName()
			if taskId != 0 {
				boolQuery.Must(elastic.NewTermQuery("task_id", taskId))
			}
			if search != "" {
				boolQuery = boolQuery.Must(tencent_ud.NewTencentUDStaffTaskModel().NewKeywordQuery(search))
			}
			listFields = tencent_ud.NewTencentUDStaffTaskModel().GetUDStaffListFields()
			listDetailFields = tencent_ud.NewTencentUDStaffTaskModel().GetUDStaffListDetailFields()

		}
	case data_source.QiAnXinPamSourceId:
		if syncType == data_sync_task.SyncAsset {
			indexName = qianxin_pam.NewPamAssetsTaskModel().IndexName()
			if taskId != 0 {
				boolQuery.Must(elastic.NewTermQuery("task_id", taskId))
			}
			if search != "" {
				boolQuery = boolQuery.Must(qianxin_pam.NewPamAssetsTaskModel().NewKeywordQuery(search))
			}
			listFields = qianxin_pam.NewPamAssetsTaskModel().GetPamAssetListFields()
			listDetailFields = qianxin_pam.NewPamAssetsTaskModel().GetPamAssetListDetailFields()
		} else if syncType == data_sync_task.SyncPeople {
			indexName = qianxin_pam.NewPamStaffsTaskModel().IndexName()
			if taskId != 0 {
				boolQuery.Must(elastic.NewTermQuery("task_id", taskId))
			}
			if search != "" {
				boolQuery = boolQuery.Must(qianxin_pam.NewPamStaffsTaskModel().NewKeywordQuery(search))
			}
			listFields = qianxin_pam.NewPamStaffsTaskModel().GetEmployeeListFields()
			listDetailFields = qianxin_pam.NewPamStaffsTaskModel().GetEmployeeListDetailFields()
		}
	case data_source.YouYunCMDBDataSourceId:
		if syncType == data_sync_task.SyncAsset {
			indexName = youyun_cmdb.NewTaskAssetModel().IndexName()
			if taskId != 0 {
				boolQuery.Must(elastic.NewTermQuery("task_id", taskId))
			}
			if search != "" {
				boolQuery = boolQuery.Must(youyun_cmdb.NewTaskAssetModel().NewKeywordQuery(search))
			}
			listFields = youyun_cmdb.NewTaskAssetModel().GetListFields()
			listDetailFields = youyun_cmdb.NewTaskAssetModel().GetDetailFields()
		}
	case data_source.QingTengBeehiveDataSourceId:
		if syncType == data_sync_task.SyncThreat {
			indexName = qingteng.NewQingTengBeehiveTaskThreatsModel().IndexName()
			if taskId != 0 {
				boolQuery.Must(elastic.NewTermQuery("task_id", taskId))
			}
			if search != "" {
				boolQuery = boolQuery.Must(qingteng.NewQingTengBeehiveTaskThreatsModel().NewKeywordQuery(search))
			}
			listFields = qingteng.NewQingTengBeehiveTaskThreatsModel().GetQingTengBeehiveThreatListFields()
			listDetailFields = qingteng.NewQingTengBeehiveTaskThreatsModel().GetQingTengBeehiveThreatListDetailFields()
		}
	case data_source.YouYueCMDBDataSourceId:
		if syncType == data_sync_task.SyncAsset {
			indexName = youyue_cmdb.NewYouYueCMDBAssetsTaskModel().IndexName()
			if taskId != 0 {
				boolQuery.Must(elastic.NewTermQuery("task_id", taskId))
			}
			if search != "" {
				boolQuery = boolQuery.Must(youyue_cmdb.NewYouYueCMDBAssetsTaskModel().NewKeywordQuery(search))
			}
			listFields = youyue_cmdb.NewYouYueCMDBAssetsTaskModel().GetYouYueCMDBAssetListFields()
			listDetailFields = youyue_cmdb.NewYouYueCMDBAssetsTaskModel().GetYouYueCMDBAssetListDetailFields()
		}
	default:
		source, _ := data_source.NewSourceModel().First(mysql.WithWhere("id = ?", nodeInfo.SourceId))

		if syncType == data_sync_task.SyncAsset {
			indexName = data_import.NewDataImportAssetsModel().IndexName(source.EnName)
			if taskId != 0 {
				boolQuery.Must(elastic.NewTermQuery("task_id", taskId))
				indexName = data_import.NewDataImportTaskAssetsModel().IndexName(source.EnName)
			}
			if search != "" {
				boolQuery = boolQuery.Must(data_import.NewDataImportAssetsModel().NewKeywordQuery(search))
			}
			listFields = data_import.NewDataImportAssetsModel().GetDataImportAssetListFields()
			listDetailFields = data_import.NewDataImportAssetsModel().GetDataImportAssetListDetailFields()
		} else if syncType == data_sync_task.SyncThreat {
			indexName = data_import.NewDataImportThreatsModel().IndexName(source.EnName)
			if taskId != 0 {
				boolQuery.Must(elastic.NewTermQuery("task_id", taskId))
				indexName = data_import.NewDataImportTaskThreatsModel().IndexName(source.EnName)
			}
			if search != "" {
				boolQuery = boolQuery.Must(data_import.NewDataImportThreatsModel().NewKeywordQuery(search))
			}
			listFields = data_import.NewDataImportThreatsModel().GetDataImportThreatListFields()
			listDetailFields = data_import.NewDataImportThreatsModel().GetDataImportThreatListDetailFields()
		} else if syncType == data_sync_task.SyncPeople {
			indexName = data_import.NewDataImportPeoplesModel().IndexName(source.EnName)
			if taskId != 0 {
				boolQuery.Must(elastic.NewTermQuery("task_id", taskId))
				indexName = data_import.NewDataImportTaskPeoplesModel().IndexName(source.EnName)
			}
			if search != "" {
				boolQuery = boolQuery.Must(data_import.NewDataImportPeoplesModel().NewKeywordQuery(search))
			}
			listFields = data_import.NewDataImportPeoplesModel().GetDataImportPeopleListFields()
			listDetailFields = data_import.NewDataImportPeoplesModel().GetDataImportPeopleListDetailFields()
		}
	}

	return indexName, boolQuery, listFields, listDetailFields
}

func CustomDataSourceNodeAdd(params *reqnode.CustomDataSourceNodeAddRequest, userId uint64) error {
	//校验自定义数据源节点是否重复
	count, err := data_source.NewNodeModel().Total(mysql.WithWhere("name = ?", params.Name))

	if err != nil {
		return err
	}

	if count > 0 {
		return errors.New("节点名称重复")
	}

	item := data_source.Node{
		Name:     params.Name,
		SourceId: params.SourceId,
		AreaId:   params.AreaId,
		Source:   "custom",
		Status:   data_source.StatusNormal,
		Use:      data_source.UseNormal,
	}

	//添加自定义数据源节点
	err = data_source.NewNodeModel().CreateItem(&item)
	if err != nil {
		return err
	}

	encrypt, err := utils.LaravelEncrypt(fmt.Sprintf("%d@#%d@#%d", userId, params.SourceId, item.Id))
	if err != nil {
		return err
	}

	//添加自定义数据源节点
	err = data_source.NewNodeConfigModel().CreateItem(&data_source.DataNodeConfig{
		NodeId: item.Id,
		Key:    "sync_node_data_api_unique",
		Value:  encrypt,
		Hidden: 1,
	})

	if err != nil {
		return err
	}

	return nil
}

func CustomDataSourceNodeUpdate(params *reqnode.CustomDataSourceNodeUpdateRequest) error {
	//校验自定义数据源节点是否重复
	count, err := data_source.NewNodeModel().Total(mysql.WithWhere("name = ?", params.Name), mysql.WithWhere("id != ?", params.Id))

	if err != nil {
		return err
	}

	if count > 0 {
		return errors.New("节点名称重复")
	}

	item := data_source.Node{
		SoftDeleteModel: mysql.SoftDeleteModel{Id: params.Id},
		Name:            params.Name,
		//SourceId: params.SourceId,
		//AreaId:   params.AreaId,
		//Source:   "custom",
		Status: data_source.StatusNormal,
		Use:    data_source.UseNormal,
	}

	//添加自定义数据源节点
	err = data_source.NewNodeModel().Update(item)
	if err != nil {
		return err
	}

	return nil
}
