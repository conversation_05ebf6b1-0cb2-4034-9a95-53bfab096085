package node

import (
	"context"
	"errors"
	"fmt"
	"fobrain/fobrain/logs"
	"fobrain/initialize/es"
	"fobrain/initialize/mysql"
	pb "fobrain/mergeService/proto"
	"fobrain/models/elastic/assets"
	"fobrain/models/elastic/device"
	"fobrain/models/elastic/poc"
	"fobrain/models/elastic/staff"
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/merge"
	"slices"
	"sync"
	"time"

	"github.com/olivere/elastic/v7"
)

var lock sync.Mutex

// ClearNodeData
// @Description: 清理节点数据
func ClearNodeData(nodeId uint64, scope ...string) error {
	// 查询节点是否存在
	node, err := data_source.NewNodeModel().First(mysql.WithWhere("id", nodeId))
	if err != nil {
		return errors.New("查询节点信息失败")
	}

	// 查询source信息
	source, err := data_source.NewSourceModel().First(mysql.WithWhere("id", node.SourceId))
	if err != nil {
		return errors.New("查询source信息失败")
	}

	triggerMergeScope := []string{}
	mergeType := []string{}
	if len(scope) == 0 {
		// 判断节点是否存在设备数据
		hasDeviceData, err := hasDeviceData(nodeId)
		if err != nil {
			return err
		}
		if hasDeviceData {
			// 获取需要融合的设备
			triggerMergeScope = append(triggerMergeScope, "device")
			mergeType = append(mergeType, merge.MergeRecordsTypeDevice)
		}
		// 根据数据源，判断该源可以同步什么数据
		if source.HasAssetData {
			// 获取需要融合的资产
			triggerMergeScope = append(triggerMergeScope, "asset")
			mergeType = append(mergeType, merge.MergeRecordsTypeAsset, merge.MergeRecordsTypeDevice)
		}
		if source.HasVulData {
			// 获取需要融合的漏洞
			triggerMergeScope = append(triggerMergeScope, "vuln")
			mergeType = append(mergeType, merge.MergeRecordsTypeVuln)
		}
		if source.HasPersonnelData {
			// 获取需要融合的人员
			triggerMergeScope = append(triggerMergeScope, "staff")
			mergeType = append(mergeType, merge.MergeRecordsTypeStaff)
		}
	} else {
		triggerMergeScope = scope
	}
	if len(triggerMergeScope) == 0 {
		return nil
	}

	// 判断是否存在等待中或执行中的融合任务，存在则返回错误
	lock.Lock()
	defer lock.Unlock()
	mergeRecord, err := merge.NewMergeRecordsModel().GetByQuery(nil, mysql.WithValuesIn("task_status", []string{merge.MergeRecordsStatusWaiting, merge.MergeRecordsStatusRunning}), mysql.WithValuesIn("merge_type", mergeType))
	if err != nil {
		logs.GetLogger().Errorf("获取执行中或等待中的融合任务失败,任务类型:%s,err:%s", mergeType, err.Error())
		return err
	}
	if mergeRecord != nil && mergeRecord.Id > 0 {
		return fmt.Errorf("存在执行中或等待中的融合任务,任务类型:%s,taskId: %d", mergeType, mergeRecord.Id)
	}

	// 如果节点存在，则清空此节点 资产、漏洞、人员等问题
	// 删除总库资产、漏洞、人员

	scrollSize := 500
	// 需要删除结果数据的索引
	indices := []string{}
	// 需要删除的过程表
	processIndices := []string{}
	if slices.Contains(triggerMergeScope, "asset") {
		indices = append(indices, assets.NewAssets().IndexName())
		processIndices = append(processIndices, assets.NewProcessAssetsModel().IndexName())
	}
	if slices.Contains(triggerMergeScope, "device") {
		indices = append(indices, device.NewDeviceModel().IndexName())
		processIndices = append(processIndices, device.NewProcessDeviceModel().IndexName())
	}
	if slices.Contains(triggerMergeScope, "vuln") {
		indices = append(indices, poc.NewPoc().IndexName())
		processIndices = append(processIndices, poc.NewProcessPocModel().IndexName())
	}
	if slices.Contains(triggerMergeScope, "staff") {
		indices = append(indices, staff.NewStaff().IndexName())
		processIndices = append(processIndices, staff.NewProcessStaffModel().IndexName())
	}
	// 删除多个表
	resp1, err := es.GetEsClient().DeleteByQuery(indices...).Query(elastic.NewBoolQuery().Must(elastic.NewTermsQuery("all_node_ids", nodeId))).ScrollSize(scrollSize).Refresh("true").Do(context.TODO())
	if err != nil {
		logs.GetLogger().Errorf("删除结果数据失败 err:%s", err.Error())
		return err
	}
	// 删除过程表
	resp2, err := es.GetEsClient().DeleteByQuery(processIndices...).Query(elastic.NewBoolQuery().Must(elastic.NewTermsQuery("node", nodeId))).ScrollSize(scrollSize).Refresh("true").Do(context.TODO())
	if err != nil {
		logs.GetLogger().Errorf("删除过程数据失败 err:%s", err.Error())
		return err
	}
	logs.GetLogger().Infof("删除结果数据成功 successCount:%d, 删除过程数据成功 successCount:%d", resp1.Deleted, resp2.Deleted)

	// 发送融合任务
	childTaskId := fmt.Sprintf("%d", time.Now().Unix())
	triggerBaseParams := &pb.TriggerMergeBaseRequest{
		SourceId:     source.Id,
		NodeId:       nodeId,
		TriggerEvent: "清理节点数据",
		TaskId:       fmt.Sprintf("clear_node_data_%d", nodeId),
		ChildTaskId:  childTaskId,
	}
	if slices.Contains(triggerMergeScope, "asset") {
		resp, err := pb.GetProtoClient().TriggerMergeForAsset(context.TODO(), &pb.TriggerMergeForAssetRequest{
			TriggerMergeBaseRequest: triggerBaseParams,
		}, pb.SetRpcTimeoutOpt(60), pb.ClientWithAddress)
		if err != nil || !resp.Success {
			logs.GetLogger().Errorf("下发资产融合任务失败. msg:%s, err:%s", resp.Message, err.Error())
		}
	}
	if slices.Contains(triggerMergeScope, "device") {
		resp, err := pb.GetProtoClient().TriggerMergeForDevice(context.TODO(), &pb.TriggerMergeForDeviceRequest{
			TriggerMergeBaseRequest: triggerBaseParams,
		}, pb.SetRpcTimeoutOpt(60), pb.ClientWithAddress)
		if err != nil || !resp.Success {
			logs.GetLogger().Errorf("下发设备融合任务失败. msg:%s, err:%s", resp.Message, err.Error())
		}
	}
	if slices.Contains(triggerMergeScope, "vuln") {
		resp, err := pb.GetProtoClient().TriggerMergeForVuln(context.TODO(), &pb.TriggerMergeForVulnRequest{
			TriggerMergeBaseRequest: triggerBaseParams,
		}, pb.SetRpcTimeoutOpt(60), pb.ClientWithAddress)
		if err != nil || !resp.Success {
			logs.GetLogger().Errorf("下发漏洞融合任务失败. msg:%s, err:%s", resp.Message, err.Error())
		}
	}
	if slices.Contains(triggerMergeScope, "staff") {
		resp, err := pb.GetProtoClient().TriggerMergeForStaff(context.TODO(), &pb.TriggerMergeForStaffRequest{
			TriggerMergeBaseRequest: triggerBaseParams,
		}, pb.SetRpcTimeoutOpt(60), pb.ClientWithAddress)
		if err != nil || !resp.Success {
			logs.GetLogger().Errorf("下发人员融合任务失败. msg:%s, err:%s", resp.Message, func() string {
				if err != nil {
					return err.Error()
				}
				return ""
			}())
		}
	}

	return nil
}

// hasDeviceData 判断节点是否存在设备数据
func hasDeviceData(nodeId uint64) (bool, error) {
	// 查询节点配置
	nodeConfig, err := data_source.NewNodeConfigModel().GetNodeConfig(nodeId)
	if err != nil {
		return false, errors.New("查询节点配置失败")
	}
	hasDeviceData := false
	if snField, ok := nodeConfig["sn_field"]; ok && snField != "" {
		hasDeviceData = true
	}
	return hasDeviceData, nil
}
