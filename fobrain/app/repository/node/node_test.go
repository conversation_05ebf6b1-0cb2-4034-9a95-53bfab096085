package node

import (
	"errors"
	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/initialize/mysql"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"

	"github.com/stretchr/testify/assert"

	reqnode "fobrain/fobrain/app/request/node"
	"fobrain/fobrain/common/constant"
	"fobrain/initialize/es"
	"fobrain/models/elastic/source/anheng_apt"
	"fobrain/models/elastic/source/sangfor_ssl_vpn"
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/data_sync_task"
	"fobrain/pkg/utils"

	. "github.com/agiledragon/gomonkey/v2"
)

func TestRepository_GetData(t *testing.T) {
	t.Log("Test_Repository_GetData")
	t.Log("DataSourceFirstError")

	time.Sleep(time.Millisecond * 300)
	patch := ApplyMethodReturn(&data_source.Node{}, "First", nil, errors.New("DataSourceFirstError"))
	_, _, _, _, err := GetData(constant.DefaultPage, constant.DefaultSize, 1, 1, 0, "")
	assert.NotNil(t, err)
	patch.Reset()

	t.Log("NodeDataError")
	time.Sleep(time.Millisecond * 300)
	patch1 := ApplyMethodReturn(&data_source.Node{}, "First", nil, nil)
	patch1.ApplyFuncReturn(getIndexNameBySourceIdAndTaskIdAndSyncType, "", nil, nil, nil)
	_, _, _, _, err = GetData(constant.DefaultPage, constant.DefaultSize, 1, 1, 0, "")
	assert.NotNil(t, err)
	patch1.Reset()
	t.Log("EsSearchError")
	time.Sleep(time.Millisecond * 300)
	patch2 := ApplyMethodReturn(&data_source.Node{}, "First", nil, nil)
	patch2.ApplyFuncReturn(getIndexNameBySourceIdAndTaskIdAndSyncType, "indexName", nil, nil, nil)
	patch2.ApplyMethodReturn(es.GetEsClient().Count("indexName").Query(nil), "Do", int64(1), nil)
	patch2.ApplyMethodReturn(es.GetEsClient().Search("indexName").From(es.GetFrom(constant.DefaultPage, constant.DefaultSize)).Size(es.GetSize(constant.DefaultSize)).Query(nil), "Do", nil, errors.New("EsSearchError"))
	_, _, _, _, err = GetData(constant.DefaultPage, constant.DefaultSize, 1, 1, 0, "")
	assert.NotNil(t, err)
	patch2.Reset()
}

func TestRepository_getIndexNameBySourceIdAndTaskIdAndSyncType(t *testing.T) {
	time.Sleep(time.Second)
	t.Log("Test_Repository_getIndexNameBySourceIdAndTaskIdAndSyncType")
	//FoeyeSourceId
	t.Log("EQFoeyeTaskAssets")
	indexName := ""
	indexName, _, _, _ = getIndexNameBySourceIdAndTaskIdAndSyncType(&data_source.Node{
		SourceId: data_source.FoeyeSourceId,
	}, 1, data_sync_task.SyncAsset, "")
	t.Log(indexName)
	assert.Equal(t, "foeye_task_assets", indexName)

	t.Log("EQFoeyeTaskThreats")
	indexName, _, _, _ = getIndexNameBySourceIdAndTaskIdAndSyncType(&data_source.Node{
		SourceId: data_source.FoeyeSourceId,
	}, 1, data_sync_task.SyncThreat, "")
	assert.EqualValues(t, "foeye_task_threats", indexName)
	t.Log("EQFoeyeAssets")
	indexName, _, _, _ = getIndexNameBySourceIdAndTaskIdAndSyncType(&data_source.Node{
		SourceId: data_source.FoeyeSourceId,
	}, 0, data_sync_task.SyncAsset, "")
	assert.EqualValues(t, "foeye_task_assets", indexName)

	t.Log("EQFoeyeThreats")
	indexName, _, _, _ = getIndexNameBySourceIdAndTaskIdAndSyncType(&data_source.Node{
		SourceId: data_source.FoeyeSourceId,
	}, 0, data_sync_task.SyncThreat, "")
	assert.Equal(t, "foeye_task_threats", indexName)

	//ForadarSourceId
	t.Log("EQForadarTaskAssets")
	indexName, _, _, _ = getIndexNameBySourceIdAndTaskIdAndSyncType(&data_source.Node{
		SourceId: data_source.ForadarSourceId,
	}, 1, data_sync_task.SyncAsset, "")
	assert.Equal(t, "foradar_task_assets", indexName)

	t.Log("EQForadarTaskThreats")
	indexName, _, _, _ = getIndexNameBySourceIdAndTaskIdAndSyncType(&data_source.Node{
		SourceId: data_source.ForadarSourceId,
	}, 1, data_sync_task.SyncThreat, "")
	assert.Equal(t, "foradar_task_threats", indexName)

	t.Log("EQForadarAssets")
	indexName, _, _, _ = getIndexNameBySourceIdAndTaskIdAndSyncType(&data_source.Node{
		SourceId: data_source.ForadarSourceId,
	}, 0, data_sync_task.SyncAsset, "")
	assert.Equal(t, "foradar_task_assets", indexName)

	t.Log("EQForadarThreats")
	indexName, _, _, _ = getIndexNameBySourceIdAndTaskIdAndSyncType(&data_source.Node{
		SourceId: data_source.ForadarSourceId,
	}, 0, data_sync_task.SyncThreat, "")
	assert.Equal(t, "foradar_task_threats", indexName)

	//QTCloudSourceId
	t.Log("EQQTCloudTaskAssets")
	indexName, _, _, _ = getIndexNameBySourceIdAndTaskIdAndSyncType(&data_source.Node{
		SourceId: data_source.QTCloudSourceId,
	}, 1, data_sync_task.SyncAsset, "")
	assert.Equal(t, "qt_cloud_task_assets", indexName)

	t.Log("EQQTCloudTaskThreats")
	indexName, _, _, _ = getIndexNameBySourceIdAndTaskIdAndSyncType(&data_source.Node{
		SourceId: data_source.QTCloudSourceId,
	}, 1, data_sync_task.SyncThreat, "")
	assert.Equal(t, "qt_cloud_task_threats", indexName)

	t.Log("EQQTCloudTaskPeople")
	indexName, _, _, _ = getIndexNameBySourceIdAndTaskIdAndSyncType(&data_source.Node{
		SourceId: data_source.QTCloudSourceId,
	}, 1, data_sync_task.SyncPeople, "")
	assert.Equal(t, "qt_cloud_task_linux_employees", indexName)
	t.Log("EQQTCloudAssets")
	indexName, _, _, _ = getIndexNameBySourceIdAndTaskIdAndSyncType(&data_source.Node{
		SourceId: data_source.QTCloudSourceId,
	}, 0, data_sync_task.SyncAsset, "")
	assert.Equal(t, "qt_cloud_task_assets", indexName)

	t.Log("EQQTCloudThreats")
	indexName, _, _, _ = getIndexNameBySourceIdAndTaskIdAndSyncType(&data_source.Node{
		SourceId: data_source.QTCloudSourceId,
	}, 0, data_sync_task.SyncThreat, "")
	assert.Equal(t, "qt_cloud_task_threats", indexName)

	t.Log("EQQTCloudPeople")
	indexName, _, _, _ = getIndexNameBySourceIdAndTaskIdAndSyncType(&data_source.Node{
		SourceId: data_source.QTCloudSourceId,
	}, 0, data_sync_task.SyncPeople, "")
	assert.Equal(t, "qt_cloud_linux_employees", indexName)

	//DingTalkSourceId
	t.Log("EQDingTalkTaskPeople")
	indexName, _, _, _ = getIndexNameBySourceIdAndTaskIdAndSyncType(&data_source.Node{
		SourceId: data_source.DingTalkSourceId,
	}, 1, data_sync_task.SyncPeople, "")
	assert.Equal(t, "dingtalk_task_employees", indexName)

	t.Log("EQDingTalkPeople")
	indexName, _, _, _ = getIndexNameBySourceIdAndTaskIdAndSyncType(&data_source.Node{
		SourceId: data_source.DingTalkSourceId,
	}, 0, data_sync_task.SyncPeople, "")
	assert.Equal(t, "dingtalk_task_employees", indexName)

	//BKCmdbSourceId
	t.Log("EQBKCmdbTaskAssets")
	indexName, _, _, _ = getIndexNameBySourceIdAndTaskIdAndSyncType(&data_source.Node{
		SourceId: data_source.BKCmdbSourceId,
	}, 1, data_sync_task.SyncAsset, "")
	assert.Equal(t, "bk_cmdb_task_assets", indexName)

	t.Log("EQBKCmdbAssets")
	indexName, _, _, _ = getIndexNameBySourceIdAndTaskIdAndSyncType(&data_source.Node{
		SourceId: data_source.BKCmdbSourceId,
	}, 0, data_sync_task.SyncAsset, "")
	assert.Equal(t, "bk_cmdb_task_assets", indexName)

	t.Log("EQBKCmdbTaskPeople")
	indexName, _, _, _ = getIndexNameBySourceIdAndTaskIdAndSyncType(&data_source.Node{
		SourceId: data_source.BKCmdbSourceId,
	}, 1, data_sync_task.SyncPeople, "")
	assert.Equal(t, "bk_cmdb_task_employees", indexName)

	t.Log("EQBKCmdbPeople")
	indexName, _, _, _ = getIndexNameBySourceIdAndTaskIdAndSyncType(&data_source.Node{
		SourceId: data_source.BKCmdbSourceId,
	}, 0, data_sync_task.SyncPeople, "")
	assert.Equal(t, "bk_cmdb_task_employees", indexName)

	//CustomSource
	t.Log("EQCustomTaskAssets")
	indexName, _, _, _ = getIndexNameBySourceIdAndTaskIdAndSyncType(&data_source.Node{
		SourceId: 0,
	}, 1, data_sync_task.SyncAsset, "")
	assert.Equal(t, "_task_assets", indexName)

	t.Log("EQCustomTaskThreats")
	indexName, _, _, _ = getIndexNameBySourceIdAndTaskIdAndSyncType(&data_source.Node{
		SourceId: 0,
	}, 1, data_sync_task.SyncThreat, "")
	assert.Equal(t, "_task_threats", indexName)

	t.Log("EQCustomTaskPeople")
	indexName, _, _, _ = getIndexNameBySourceIdAndTaskIdAndSyncType(&data_source.Node{
		SourceId: 0,
	}, 1, data_sync_task.SyncPeople, "")
	assert.Equal(t, "_task_employees", indexName)

	t.Log("EQCustomAssets")
	indexName, _, _, _ = getIndexNameBySourceIdAndTaskIdAndSyncType(&data_source.Node{
		SourceId: 0,
	}, 0, data_sync_task.SyncAsset, "")
	assert.Equal(t, "_assets", indexName)

	t.Log("EQCustomThreats")
	indexName, _, _, _ = getIndexNameBySourceIdAndTaskIdAndSyncType(&data_source.Node{
		SourceId: 0,
	}, 0, data_sync_task.SyncThreat, "")
	assert.Equal(t, "_threats", indexName)

	t.Log("EQCustomPeople")
	indexName, _, _, _ = getIndexNameBySourceIdAndTaskIdAndSyncType(&data_source.Node{
		SourceId: 0,
	}, 0, data_sync_task.SyncPeople, "")
	assert.Equal(t, "_employees", indexName)

	//FileImportSourceId
	t.Log("EQFileImportTaskAssets")
	indexName, _, _, _ = getIndexNameBySourceIdAndTaskIdAndSyncType(&data_source.Node{
		SourceId: data_source.FileImportSourceId,
	}, 1, data_sync_task.SyncAsset, "")
	assert.Equal(t, "file_import_task_assets", indexName)

	t.Log("EQFileImportTaskThreats")
	indexName, _, _, _ = getIndexNameBySourceIdAndTaskIdAndSyncType(&data_source.Node{
		SourceId: data_source.FileImportSourceId,
	}, 1, data_sync_task.SyncThreat, "")
	assert.Equal(t, "file_import_task_threats", indexName)
	t.Log("EQFileImportTaskPeople")
	indexName, _, _, _ = getIndexNameBySourceIdAndTaskIdAndSyncType(&data_source.Node{
		SourceId: data_source.FileImportSourceId,
	}, 1, data_sync_task.SyncPeople, "")
	assert.Equal(t, "file_import_task_peoples", indexName)
	t.Log("EQFileImportAssets")
	indexName, _, _, _ = getIndexNameBySourceIdAndTaskIdAndSyncType(&data_source.Node{
		SourceId: data_source.FileImportSourceId,
	}, 0, data_sync_task.SyncAsset, "")
	assert.Equal(t, "file_import_task_assets", indexName)

	t.Log("EQFileImportThreats")
	indexName, _, _, _ = getIndexNameBySourceIdAndTaskIdAndSyncType(&data_source.Node{
		SourceId: data_source.FileImportSourceId,
	}, 0, data_sync_task.SyncThreat, "")
	assert.Equal(t, "file_import_task_threats", indexName)

	t.Log("EQFileImportPeople")
	indexName, _, _, _ = getIndexNameBySourceIdAndTaskIdAndSyncType(&data_source.Node{
		SourceId: data_source.FileImportSourceId,
	}, 0, data_sync_task.SyncPeople, "")
	assert.Equal(t, "file_import_task_peoples", indexName)

	t.Run("ucloud资产", func(t *testing.T) {
		indexName, _, _, _ = getIndexNameBySourceIdAndTaskIdAndSyncType(&data_source.Node{
			SourceId: data_source.UCloudSourceId,
		}, 0, data_sync_task.SyncAsset, "")
		assert.Equal(t, "ucloud_task_assets", indexName)
	})

	t.Run("阿里云SLB资产", func(t *testing.T) {
		indexName, _, _, _ = getIndexNameBySourceIdAndTaskIdAndSyncType(&data_source.Node{
			SourceId: data_source.AliyunSLBSourceId,
		}, 0, data_sync_task.SyncAsset, "")
		assert.Equal(t, "aliyun_slb_task_assets", indexName)
	})

	t.Run("云图资产", func(t *testing.T) {
		indexName, _, _, _ = getIndexNameBySourceIdAndTaskIdAndSyncType(&data_source.Node{
			SourceId: data_source.YuntuSourceId,
		}, 0, data_sync_task.SyncAsset, "")
		assert.Equal(t, "yuntu_task_assets", indexName)
	})

	t.Run("云图漏洞", func(t *testing.T) {
		indexName, _, _, _ = getIndexNameBySourceIdAndTaskIdAndSyncType(&data_source.Node{
			SourceId: data_source.YuntuSourceId,
		}, 0, data_sync_task.SyncThreat, "")
		assert.Equal(t, "yuntu_task_threats", indexName)
	})

	t.Run("日志易资产", func(t *testing.T) {
		indexName, _, _, _ = getIndexNameBySourceIdAndTaskIdAndSyncType(&data_source.Node{
			SourceId: data_source.RizhiyiSourceId,
		}, 0, data_sync_task.SyncAsset, "")
		assert.Equal(t, "rizhiyi_task_assets", indexName)
	})

	t.Run("日志易人员", func(t *testing.T) {
		indexName, _, _, _ = getIndexNameBySourceIdAndTaskIdAndSyncType(&data_source.Node{
			SourceId: data_source.RizhiyiSourceId,
		}, 0, data_sync_task.SyncPeople, "")
		assert.Equal(t, "rizhiyi_employees", indexName)
	})
	t.Run("D01资产", func(t *testing.T) {
		indexName, _, _, _ = getIndexNameBySourceIdAndTaskIdAndSyncType(&data_source.Node{
			SourceId: data_source.D01SourceId,
		}, 0, data_sync_task.SyncAsset, "")
		assert.Equal(t, "d01_task_assets", indexName)
	})

	t.Run("D01漏洞", func(t *testing.T) {
		indexName, _, _, _ = getIndexNameBySourceIdAndTaskIdAndSyncType(&data_source.Node{
			SourceId: data_source.D01SourceId,
		}, 0, data_sync_task.SyncThreat, "")
		assert.Equal(t, "d01_task_threats", indexName)
	})
	t.Run("qizhi资产", func(t *testing.T) {
		indexName, _, _, _ = getIndexNameBySourceIdAndTaskIdAndSyncType(&data_source.Node{
			SourceId: data_source.QiZhiUAuditHostSourceId,
		}, 0, data_sync_task.SyncAsset, "")
		assert.Equal(t, "qizhi_uaudithost_assets", indexName)
	})
	t.Run("阿里云盾漏洞", func(t *testing.T) {
		indexName, _, _, _ = getIndexNameBySourceIdAndTaskIdAndSyncType(&data_source.Node{
			SourceId: data_source.AliYunCloudSourceId,
		}, 0, data_sync_task.SyncThreat, "")
		assert.Equal(t, "aliyun_cloud_task_threats", indexName)
	})
	t.Run("weibu资产", func(t *testing.T) {
		indexName, _, _, _ = getIndexNameBySourceIdAndTaskIdAndSyncType(&data_source.Node{
			SourceId: data_source.WeiBuSourceId,
		}, 0, data_sync_task.SyncAsset, "")
		assert.Equal(t, "weibu_task_assets", indexName)
	})

	t.Run("weibu漏洞", func(t *testing.T) {
		indexName, _, _, _ = getIndexNameBySourceIdAndTaskIdAndSyncType(&data_source.Node{
			SourceId: data_source.WeiBuSourceId,
		}, 0, data_sync_task.SyncThreat, "")
		assert.Equal(t, "weibu_task_threats", indexName)
	})
	t.Run("长亭waf资产", func(t *testing.T) {
		indexName, _, _, _ = getIndexNameBySourceIdAndTaskIdAndSyncType(&data_source.Node{
			SourceId: data_source.ChangTingWAFSourceId,
		}, 0, data_sync_task.SyncAsset, "")
		assert.Equal(t, "changting_waf_task_assets", indexName)
	})
	t.Run("mach_lake资产", func(t *testing.T) {
		indexName, _, _, _ = getIndexNameBySourceIdAndTaskIdAndSyncType(&data_source.Node{
			SourceId: data_source.MachLakeSourceId,
		}, 0, data_sync_task.SyncAsset, "")
		assert.Equal(t, "mach_lake_task_assets", indexName)
	})
	t.Run("xray资产", func(t *testing.T) {
		indexName, _, _, _ = getIndexNameBySourceIdAndTaskIdAndSyncType(&data_source.Node{
			SourceId: data_source.XRaySourceId,
		}, 0, data_sync_task.SyncAsset, "")
		assert.Equal(t, "xray_task_assets", indexName)
	})
	t.Run("xray漏洞", func(t *testing.T) {
		indexName, _, _, _ = getIndexNameBySourceIdAndTaskIdAndSyncType(&data_source.Node{
			SourceId: data_source.XRaySourceId,
		}, 0, data_sync_task.SyncThreat, "")
		assert.Equal(t, "xray_task_threats", indexName)
	})
	t.Run("BKCmdbCustomVmMachine资产", func(t *testing.T) {
		indexName, _, _, _ = getIndexNameBySourceIdAndTaskIdAndSyncType(&data_source.Node{
			SourceId: data_source.BKCmdbCustomVmMachineSourceId,
		}, 0, data_sync_task.SyncAsset, "")
		assert.Equal(t, "bk_cmdb_vm_machine_task_assets", indexName)
	})
	t.Run("BKCmdbCustomCloudEcs资产", func(t *testing.T) {
		indexName, _, _, _ = getIndexNameBySourceIdAndTaskIdAndSyncType(&data_source.Node{
			SourceId: data_source.BKCmdbCustomCloudEcsSourceId,
		}, 0, data_sync_task.SyncAsset, "")
		assert.Equal(t, "bk_cmdb_cloud_ecs_task_assets", indexName)
	})
	t.Run("HuaweiHkCloud资产", func(t *testing.T) {
		indexName, _, _, _ = getIndexNameBySourceIdAndTaskIdAndSyncType(&data_source.Node{
			SourceId: data_source.HuaweiHkCloudSourceId,
		}, 0, data_sync_task.SyncAsset, "")
		assert.Equal(t, "huawei_hk_cloud_assets", indexName)
	})
	t.Run("BKCmdbCustomDomain资产", func(t *testing.T) {
		indexName, _, _, _ = getIndexNameBySourceIdAndTaskIdAndSyncType(&data_source.Node{
			SourceId: data_source.BKCmdbCustomDomainSourceId,
		}, 0, data_sync_task.SyncAsset, "")
		assert.Equal(t, "bk_cmdb_domain_task_assets", indexName)
	})
	t.Run("BKCmdbCustomBusiness资产", func(t *testing.T) {
		indexName, _, _, _ = getIndexNameBySourceIdAndTaskIdAndSyncType(&data_source.Node{
			SourceId: data_source.BKCmdbCustomBusinessSourceId,
		}, 0, data_sync_task.SyncAsset, "")
		assert.Equal(t, "bk_cmdb_business_task_assets", indexName)
	})
	t.Run("BKCmdbCustomF5Vs资产", func(t *testing.T) {
		indexName, _, _, _ = getIndexNameBySourceIdAndTaskIdAndSyncType(&data_source.Node{
			SourceId: data_source.BKCmdbCustomF5VsSourceId,
		}, 0, data_sync_task.SyncAsset, "")
		assert.Equal(t, "bk_cmdb_f5_vs_task_assets", indexName)
	})
	t.Run("BKCmdbCustomF5Pool资产", func(t *testing.T) {
		indexName, _, _, _ = getIndexNameBySourceIdAndTaskIdAndSyncType(&data_source.Node{
			SourceId: data_source.BKCmdbCustomF5PoolSourceId,
		}, 0, data_sync_task.SyncAsset, "")
		assert.Equal(t, "bk_cmdb_f5_pool_task_assets", indexName)
	})
}

func TestRepository_CustomDataSourceNodeAdd(t *testing.T) {
	t.Log("Test_Repository_CustomDataSourceNodeAdd")
	t.Log("CountNodeError")
	time.Sleep(time.Millisecond * 600)
	patch := ApplyMethodReturn(&data_source.Node{}, "Total", int64(0), errors.New("CountNodeError"))
	err := CustomDataSourceNodeAdd(&reqnode.CustomDataSourceNodeAddRequest{Name: "name", SourceId: uint64(1), AreaId: uint64(1)}, 0)
	assert.NotNil(t, err)
	assert.Equal(t, "CountNodeError", err.Error())
	patch.Reset()
	t.Log("NodeNameRepeatError")
	time.Sleep(time.Millisecond * 300)
	patch1 := ApplyMethodReturn(&data_source.Node{}, "Total", int64(1), nil)
	err = CustomDataSourceNodeAdd(&reqnode.CustomDataSourceNodeAddRequest{Name: "name", SourceId: uint64(1), AreaId: uint64(1)}, 0)
	assert.NotNil(t, err)
	assert.Equal(t, "节点名称重复", err.Error())
	patch1.Reset()
	t.Run("CreateNodeError", func(t *testing.T) {
		time.Sleep(time.Millisecond * 300)
		patch1 := ApplyMethodReturn(&data_source.Node{}, "Total", int64(0), nil)
		patch2 := ApplyMethodReturn(&data_source.Node{}, "CreateItem", errors.New("CreateNodeError"))
		err := CustomDataSourceNodeAdd(&reqnode.CustomDataSourceNodeAddRequest{Name: "name", SourceId: uint64(1), AreaId: uint64(1)}, 0)
		assert.NotNil(t, err)
		assert.Equal(t, "CreateNodeError", err.Error())
		patch1.Reset()
		patch2.Reset()
	})
	t.Run("LaravelEncryptError", func(t *testing.T) {
		time.Sleep(time.Millisecond * 300)
		patch1 := ApplyMethodReturn(&data_source.Node{}, "Total", int64(0), nil)
		patch2 := ApplyMethodReturn(&data_source.Node{}, "CreateItem", nil)
		patch3 := ApplyFuncReturn(utils.LaravelEncrypt, "CreateItem", errors.New("LaravelEncryptError"))
		err := CustomDataSourceNodeAdd(&reqnode.CustomDataSourceNodeAddRequest{Name: "name", SourceId: uint64(1), AreaId: uint64(1)}, 0)
		assert.NotNil(t, err)
		patch1.Reset()
		patch2.Reset()
		patch3.Reset()
	})
	t.Run("CreateNodeConfigError", func(t *testing.T) {
		time.Sleep(time.Millisecond * 300)
		patch1 := ApplyMethodReturn(&data_source.Node{}, "Total", int64(0), nil)
		patch2 := ApplyMethodReturn(&data_source.Node{}, "CreateItem", nil)
		patch3 := ApplyFuncReturn(utils.LaravelEncrypt, "CreateItem", nil)
		patch4 := ApplyMethodReturn(data_source.NewNodeConfigModel(), "CreateItem", errors.New("CreateNodeConfigError"))
		err := CustomDataSourceNodeAdd(&reqnode.CustomDataSourceNodeAddRequest{Name: "name", SourceId: uint64(1), AreaId: uint64(1)}, 0)
		assert.NotNil(t, err)
		assert.Equal(t, "CreateNodeConfigError", err.Error())
		patch1.Reset()
		patch2.Reset()
		patch3.Reset()
		patch4.Reset()
	})
	t.Run("PASS", func(t *testing.T) {
		time.Sleep(time.Millisecond * 300)
		patch1 := ApplyMethodReturn(&data_source.Node{}, "Total", int64(0), nil)
		patch2 := ApplyMethodReturn(&data_source.Node{}, "CreateItem", nil)
		patch3 := ApplyFuncReturn(utils.LaravelEncrypt, "CreateItem", nil)
		patch4 := ApplyMethodReturn(data_source.NewNodeConfigModel(), "CreateItem", nil)
		err := CustomDataSourceNodeAdd(&reqnode.CustomDataSourceNodeAddRequest{Name: "name", SourceId: uint64(1), AreaId: uint64(1)}, 0)
		assert.Nil(t, err)
		patch1.Reset()
		patch2.Reset()
		patch3.Reset()
		patch4.Reset()
	})
}

func TestRepository_CustomDataSourceNodeUpdate(t *testing.T) {
	t.Log("Test_Repository_CustomDataSourceNodeUpdate")
	t.Log("CountNodeError")

	time.Sleep(time.Millisecond * 600)
	patch1 := ApplyMethodReturn(&data_source.Node{}, "Total", int64(0), errors.New("CountNodeError"))
	err := CustomDataSourceNodeUpdate(&reqnode.CustomDataSourceNodeUpdateRequest{Name: "name", Id: uint64(1)})
	assert.NotNil(t, err)
	patch1.Reset()
	assert.Equal(t, "CountNodeError", err.Error())

	t.Log("NodeNameRepeatError")
	time.Sleep(time.Millisecond * 600)
	patch2 := ApplyMethodReturn(&data_source.Node{}, "Total", int64(1), nil)
	err = CustomDataSourceNodeUpdate(&reqnode.CustomDataSourceNodeUpdateRequest{Name: "name", Id: uint64(1)})
	patch2.Reset()
	assert.NotNil(t, err)
	assert.Equal(t, "节点名称重复", err.Error())

	t.Log("UpdateNodeError")
	time.Sleep(time.Millisecond * 300)
	patch3 := ApplyMethodReturn(&data_source.Node{}, "Total", int64(0), nil)
	patch3.ApplyMethodReturn(&data_source.Node{}, "Update", errors.New("UpdateNodeError"))
	err = CustomDataSourceNodeUpdate(&reqnode.CustomDataSourceNodeUpdateRequest{Name: "name", Id: uint64(1)})
	assert.NotNil(t, err)
	patch3.Reset()
	assert.Equal(t, "UpdateNodeError", err.Error())

	t.Log("PASS")
	time.Sleep(time.Millisecond * 300)
	defer ApplyMethodReturn(&data_source.Node{}, "Total", int64(0), nil).Reset()
	defer ApplyMethodReturn(&data_source.Node{}, "Update", nil).Reset()
	err = CustomDataSourceNodeUpdate(&reqnode.CustomDataSourceNodeUpdateRequest{Name: "name", Id: uint64(1)})
	assert.Nil(t, err)
}

func TestGetFailDetail(t *testing.T) {
	t.Log("TestGetFailDetail")

	// 测试数据库查询错误
	t.Log("DatabaseQueryError")

	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()

	// 使用 AnyQuery() 而不是尝试匹配精确的 SQL 查询
	mockDb.ExpectQuery("").WillReturnError(errors.New("DatabaseQueryError"))

	_, _, err := GetFailDetail(1, constant.DefaultPage, constant.DefaultSize)
	assert.NotNil(t, err)
	assert.Equal(t, "DatabaseQueryError", err.Error())

	// 测试成功获取数据
	t.Log("SuccessfulQuery")

	mockDb = testcommon.GetMysqlMock()
	defer mockDb.Close()

	// 使用 AnyQuery() 匹配计数查询
	mockDb.ExpectQuery("").WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(1))

	// 使用 AnyQuery() 匹配数据查询
	mockDb.ExpectQuery("").WillReturnRows(sqlmock.NewRows([]string{
		"id", "child_task_id", "data_type", "data_content", "failed_reason", "created_at", "updated_at"}).
		AddRow(1, 1, "asset", "{\"ip\":\"***********\"}", "数据格式错误", time.Now(), time.Now()))

	records, total, err := GetFailDetail(1, constant.DefaultPage, constant.DefaultSize)
	assert.Nil(t, err)
	assert.Equal(t, int64(1), total)
	assert.Equal(t, 1, len(records))
	assert.Equal(t, uint64(1), records[0].Id)
	assert.Equal(t, uint64(1), records[0].ChildTaskId)
	assert.Equal(t, "asset", records[0].DataType)
	assert.Equal(t, "{\"ip\":\"***********\"}", records[0].DataContent)
	assert.Equal(t, "数据格式错误", records[0].FailedReason)

	// 测试空结果
	t.Log("EmptyResult")

	mockDb = testcommon.GetMysqlMock()
	defer mockDb.Close()

	// 使用 AnyQuery() 匹配计数查询返回0
	mockDb.ExpectQuery("").WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(0))

	// 使用 AnyQuery() 匹配空数据查询
	mockDb.ExpectQuery("").WillReturnRows(sqlmock.NewRows([]string{
		"id", "child_task_id", "data_type", "data_content", "failed_reason", "created_at", "updated_at"}))

	records, total, err = GetFailDetail(1, constant.DefaultPage, constant.DefaultSize)
	assert.Nil(t, err)
	assert.Equal(t, int64(0), total)
	assert.Equal(t, 0, len(records))
}

// TestGetIndexNameListFieldsBySourceId 测试优化后的统一接口方法
func TestGetIndexNameListFieldsBySourceId(t *testing.T) {
	t.Log("Test_GetIndexNameListFieldsBySourceId - 优化后的统一接口测试")

	// 测试统一接口数据源 - SangforSSLVPN
	t.Run("SangforSSLVPN_Asset", func(t *testing.T) {
		nodeInfo := &data_source.Node{
			SoftDeleteModel: mysql.SoftDeleteModel{Id: 1},
			SourceId:        data_source.SangforSSLVPNSourceId,
		}
		indexName, boolQuery, listFields, listDetailFields := getIndexNameListFieldsBySourceId(nodeInfo, 123, data_sync_task.SyncAsset, "test")

		assert.Equal(t, "sangfor_ssl_vpn_task_assets", indexName)
		assert.NotNil(t, boolQuery)
		assert.Greater(t, len(listFields), 0)
		assert.Greater(t, len(listDetailFields), 0)
		assert.Equal(t, "虚拟 ip", listFields[0]["name"])
	})

	// 测试统一接口数据源 - AnhengAPT
	t.Run("AnhengAPT_Asset", func(t *testing.T) {
		nodeInfo := &data_source.Node{
			SoftDeleteModel: mysql.SoftDeleteModel{Id: 2},
			SourceId:        data_source.AnhengAPT,
		}
		indexName, boolQuery, listFields, listDetailFields := getIndexNameListFieldsBySourceId(nodeInfo, 456, data_sync_task.SyncAsset, "192.168")

		assert.Equal(t, "anheng_apt_task_assets", indexName)
		assert.NotNil(t, boolQuery)
		assert.Greater(t, len(listFields), 0)
		assert.Greater(t, len(listDetailFields), 0)
		assert.Equal(t, "资产IP", listFields[0]["name"])
	})

	// 测试不支持的数据源
	t.Run("UnsupportedDataSource", func(t *testing.T) {
		nodeInfo := &data_source.Node{
			SoftDeleteModel: mysql.SoftDeleteModel{Id: 5},

			SourceId: 99999, // 不存在的数据源ID
		}
		indexName, boolQuery, listFields, listDetailFields := getIndexNameListFieldsBySourceId(nodeInfo, 0, data_sync_task.SyncAsset, "")

		assert.Equal(t, "", indexName)
		assert.Nil(t, boolQuery)
		assert.Equal(t, 0, len(listFields))
		assert.Equal(t, 0, len(listDetailFields))
	})

	// 测试查询构建逻辑
	t.Run("QueryBuilding", func(t *testing.T) {
		nodeInfo := &data_source.Node{
			SoftDeleteModel: mysql.SoftDeleteModel{Id: 1},
			SourceId:        data_source.SangforSSLVPNSourceId,
		}

		// 测试有 taskId 的情况
		_, boolQuery, _, _ := getIndexNameListFieldsBySourceId(nodeInfo, 123, data_sync_task.SyncAsset, "")
		assert.NotNil(t, boolQuery)

		// 测试无 taskId 的情况
		_, boolQuery2, _, _ := getIndexNameListFieldsBySourceId(nodeInfo, 0, data_sync_task.SyncAsset, "")
		assert.NotNil(t, boolQuery2)

		// 测试有搜索关键字的情况
		_, boolQuery3, _, _ := getIndexNameListFieldsBySourceId(nodeInfo, 0, data_sync_task.SyncAsset, "test")
		assert.NotNil(t, boolQuery3)
	})
}

// TestDataSourceInterface 测试数据源接口实现
func TestDataSourceInterface(t *testing.T) {
	t.Log("Test_DataSourceInterface - 测试数据源接口实现")

	// 测试 SangforSSLVPN 接口实现
	t.Run("SangforSSLVPN_Interface", func(t *testing.T) {
		model := sangfor_ssl_vpn.NewSangforSSLVPNTaskModel()

		// 测试 GetIndexName
		assert.Equal(t, "sangfor_ssl_vpn_task_assets", model.GetIndexName(data_sync_task.SyncAsset))

		// 测试 GetListFields
		assetFields := model.GetListFields(data_sync_task.SyncAsset)
		assert.Greater(t, len(assetFields), 0)
		assert.Equal(t, "虚拟 ip", assetFields[0]["name"])

		// 测试 GetListDetailFields
		assetDetailFields := model.GetListDetailFields(data_sync_task.SyncAsset)
		assert.Greater(t, len(assetDetailFields), 0)

		// 测试 NewKeywordQuery
		query := model.NewKeywordQuery("test")
		assert.NotNil(t, query)
	})

	// 测试 AnhengAPT 接口实现
	t.Run("AnhengAPT_Interface", func(t *testing.T) {
		model := anheng_apt.NewAnhengAptTaskModel()

		// 测试 GetIndexName
		assert.Equal(t, "anheng_apt_task_assets", model.GetIndexName(data_sync_task.SyncAsset))

		// 测试 GetListFields
		assetFields := model.GetListFields(data_sync_task.SyncAsset)
		assert.Greater(t, len(assetFields), 0)
		assert.Equal(t, "资产IP", assetFields[0]["name"])

		// 测试 NewKeywordQuery
		query := model.NewKeywordQuery("***********")
		assert.NotNil(t, query)

	})
}

// TestBuildBaseQuery 测试基础查询构建
func TestBuildBaseQuery(t *testing.T) {
	t.Log("Test_BuildBaseQuery - 测试基础查询构建")

	t.Run("WithTaskId", func(t *testing.T) {
		boolQuery := buildBaseQuery(123, 456)
		assert.NotNil(t, boolQuery)
		// 验证查询包含必要的条件
	})

	t.Run("WithoutTaskId", func(t *testing.T) {
		boolQuery := buildBaseQuery(123, 0)
		assert.NotNil(t, boolQuery)
		// 验证查询包含必要的条件但不包含 task_id
	})
}

// TestProcessGeneralDataSource 测试通用数据源处理
func TestProcessGeneralDataSource(t *testing.T) {
	t.Log("Test_ProcessGeneralDataSource - 测试通用数据源处理")

	model := sangfor_ssl_vpn.NewSangforSSLVPNTaskModel()
	boolQuery := buildBaseQuery(123, 456)

	t.Run("WithSearch", func(t *testing.T) {
		indexName, listFields, listDetailFields := processGeneralDataSource(model, data_sync_task.SyncAsset, "test", boolQuery)

		assert.Equal(t, "sangfor_ssl_vpn_task_assets", indexName)
		assert.Greater(t, len(listFields), 0)
		assert.Greater(t, len(listDetailFields), 0)
	})

	t.Run("WithoutSearch", func(t *testing.T) {
		indexName, listFields, listDetailFields := processGeneralDataSource(model, data_sync_task.SyncAsset, "", boolQuery)

		assert.Equal(t, "sangfor_ssl_vpn_task_assets", indexName)
		assert.Greater(t, len(listFields), 0)
		assert.Greater(t, len(listDetailFields), 0)
	})

}

// TestGetIndexNameBySourceIdAndTaskIdAndSyncType 测试原始巨型方法的100%覆盖率
func TestGetIndexNameBySourceIdAndTaskIdAndSyncType(t *testing.T) {
	t.Log("Test_GetIndexNameBySourceIdAndTaskIdAndSyncType - 原始巨型方法100%覆盖率测试")

	// 测试 WeibuOnesec 数据源
	t.Run("WeibuOnesec_Asset", func(t *testing.T) {
		nodeInfo := &data_source.Node{
			SoftDeleteModel: mysql.SoftDeleteModel{Id: 1},
			SourceId:        data_source.WeibuOnesecSourceId,
		}
		indexName, boolQuery, listFields, listDetailFields := getIndexNameBySourceIdAndTaskIdAndSyncType(nodeInfo, 123, data_sync_task.SyncAsset, "test")

		assert.NotEqual(t, "", indexName)
		assert.NotNil(t, boolQuery)
		assert.Greater(t, len(listFields), 0)
		assert.Greater(t, len(listDetailFields), 0)
	})

	t.Run("WeibuOnesec_Asset_NoTaskId", func(t *testing.T) {
		nodeInfo := &data_source.Node{
			SoftDeleteModel: mysql.SoftDeleteModel{Id: 1},
			SourceId:        data_source.WeibuOnesecSourceId,
		}
		indexName, boolQuery, listFields, listDetailFields := getIndexNameBySourceIdAndTaskIdAndSyncType(nodeInfo, 0, data_sync_task.SyncAsset, "")

		assert.Equal(t, "", indexName) // taskId为0时不设置indexName
		assert.NotNil(t, boolQuery)
		assert.Greater(t, len(listFields), 0)
		assert.Greater(t, len(listDetailFields), 0)
	})

	t.Run("WeibuOnesec_UnsupportedType", func(t *testing.T) {
		nodeInfo := &data_source.Node{
			SoftDeleteModel: mysql.SoftDeleteModel{Id: 1},
			SourceId:        data_source.WeibuOnesecSourceId,
		}
		indexName, boolQuery, listFields, listDetailFields := getIndexNameBySourceIdAndTaskIdAndSyncType(nodeInfo, 123, data_sync_task.SyncThreat, "")

		assert.Equal(t, "", indexName)
		assert.NotNil(t, boolQuery)
		assert.Equal(t, 0, len(listFields))
		assert.Equal(t, 0, len(listDetailFields))
	})

	// 测试华为云 ECS
	t.Run("HuaweiEcs_Asset", func(t *testing.T) {
		nodeInfo := &data_source.Node{
			SoftDeleteModel: mysql.SoftDeleteModel{Id: 2},
			SourceId:        data_source.PublicHuaweiEcsSourceId,
		}
		indexName, boolQuery, listFields, listDetailFields := getIndexNameBySourceIdAndTaskIdAndSyncType(nodeInfo, 456, data_sync_task.SyncAsset, "192.168")

		assert.NotEqual(t, "", indexName)
		assert.NotNil(t, boolQuery)
		assert.Greater(t, len(listFields), 0)
		assert.Greater(t, len(listDetailFields), 0)
	})

	// 测试腾讯云 CVM
	t.Run("TencentCVM_Asset", func(t *testing.T) {
		nodeInfo := &data_source.Node{
			SoftDeleteModel: mysql.SoftDeleteModel{Id: 3},
			SourceId:        data_source.PublicTencentCVMSourceId,
		}
		indexName, boolQuery, listFields, listDetailFields := getIndexNameBySourceIdAndTaskIdAndSyncType(nodeInfo, 789, data_sync_task.SyncAsset, "")

		assert.NotEqual(t, "", indexName)
		assert.NotNil(t, boolQuery)
		assert.Greater(t, len(listFields), 0)
		assert.Greater(t, len(listDetailFields), 0)
	})

	// 测试腾讯云 CLB
	t.Run("TencentCLB_Asset", func(t *testing.T) {
		nodeInfo := &data_source.Node{
			SoftDeleteModel: mysql.SoftDeleteModel{Id: 4},
			SourceId:        data_source.PublicTencentCLBSourceId,
		}
		indexName, boolQuery, listFields, listDetailFields := getIndexNameBySourceIdAndTaskIdAndSyncType(nodeInfo, 111, data_sync_task.SyncAsset, "test")

		assert.NotEqual(t, "", indexName)
		assert.NotNil(t, boolQuery)
		assert.Greater(t, len(listFields), 0)
		assert.Greater(t, len(listDetailFields), 0)
	})

	// 测试阿里云 SLB
	t.Run("AliyunSLB_Asset", func(t *testing.T) {
		nodeInfo := &data_source.Node{
			SoftDeleteModel: mysql.SoftDeleteModel{Id: 5},
			SourceId:        data_source.AliyunSLBSourceId,
		}
		indexName, boolQuery, listFields, listDetailFields := getIndexNameBySourceIdAndTaskIdAndSyncType(nodeInfo, 222, data_sync_task.SyncAsset, "")

		assert.NotEqual(t, "", indexName)
		assert.NotNil(t, boolQuery)
		assert.Greater(t, len(listFields), 0)
		assert.Greater(t, len(listDetailFields), 0)
	})

	// 测试云图 - 资产
	t.Run("Yuntu_Asset", func(t *testing.T) {
		nodeInfo := &data_source.Node{
			SoftDeleteModel: mysql.SoftDeleteModel{Id: 6},
			SourceId:        data_source.YuntuSourceId,
		}
		indexName, boolQuery, listFields, listDetailFields := getIndexNameBySourceIdAndTaskIdAndSyncType(nodeInfo, 333, data_sync_task.SyncAsset, "test")

		assert.NotEqual(t, "", indexName)
		assert.NotNil(t, boolQuery)
		assert.Greater(t, len(listFields), 0)
		assert.Greater(t, len(listDetailFields), 0)
	})

	// 测试云图 - 威胁
	t.Run("Yuntu_Threat", func(t *testing.T) {
		nodeInfo := &data_source.Node{
			SoftDeleteModel: mysql.SoftDeleteModel{Id: 6},
			SourceId:        data_source.YuntuSourceId,
		}
		indexName, boolQuery, listFields, listDetailFields := getIndexNameBySourceIdAndTaskIdAndSyncType(nodeInfo, 444, data_sync_task.SyncThreat, "")

		assert.NotEqual(t, "", indexName)
		assert.NotNil(t, boolQuery)
		assert.Greater(t, len(listFields), 0)
		assert.Greater(t, len(listDetailFields), 0)
	})

	// 测试日志易 - 资产
	t.Run("Rizhiyi_Asset", func(t *testing.T) {
		nodeInfo := &data_source.Node{
			SoftDeleteModel: mysql.SoftDeleteModel{Id: 7},
			SourceId:        data_source.RizhiyiSourceId,
		}
		indexName, boolQuery, listFields, listDetailFields := getIndexNameBySourceIdAndTaskIdAndSyncType(nodeInfo, 555, data_sync_task.SyncAsset, "test")

		assert.NotEqual(t, "", indexName)
		assert.NotNil(t, boolQuery)
		assert.Greater(t, len(listFields), 0)
		assert.Greater(t, len(listDetailFields), 0)
	})

	// 测试日志易 - 人员
	t.Run("Rizhiyi_People", func(t *testing.T) {
		nodeInfo := &data_source.Node{
			SoftDeleteModel: mysql.SoftDeleteModel{Id: 7},
			SourceId:        data_source.RizhiyiSourceId,
		}
		indexName, boolQuery, listFields, listDetailFields := getIndexNameBySourceIdAndTaskIdAndSyncType(nodeInfo, 666, data_sync_task.SyncPeople, "")

		assert.NotEqual(t, "", indexName)
		assert.NotNil(t, boolQuery)
		assert.Greater(t, len(listFields), 0)
		assert.Greater(t, len(listDetailFields), 0)
	})

	// 测试奇安信服务器
	t.Run("QianxinServer_Asset", func(t *testing.T) {
		nodeInfo := &data_source.Node{
			SoftDeleteModel: mysql.SoftDeleteModel{Id: 8},
			SourceId:        data_source.QianxinServerSourceId,
		}
		indexName, boolQuery, listFields, listDetailFields := getIndexNameBySourceIdAndTaskIdAndSyncType(nodeInfo, 777, data_sync_task.SyncAsset, "test")

		assert.NotEqual(t, "", indexName)
		assert.NotNil(t, boolQuery)
		assert.Greater(t, len(listFields), 0)
		assert.Greater(t, len(listDetailFields), 0)
	})

	// 测试 UCloud
	t.Run("UCloud_Asset", func(t *testing.T) {
		nodeInfo := &data_source.Node{
			SoftDeleteModel: mysql.SoftDeleteModel{Id: 9},
			SourceId:        data_source.UCloudSourceId,
		}
		indexName, boolQuery, listFields, listDetailFields := getIndexNameBySourceIdAndTaskIdAndSyncType(nodeInfo, 888, data_sync_task.SyncAsset, "")

		assert.NotEqual(t, "", indexName)
		assert.NotNil(t, boolQuery)
		assert.Greater(t, len(listFields), 0)
		assert.Greater(t, len(listDetailFields), 0)
	})

	// 测试 Foeye - 资产（普通版本）
	t.Run("Foeye_Asset_V1", func(t *testing.T) {
		// Mock 节点配置获取返回 v1 版本
		patch := ApplyMethodReturn(data_source.NewNodeConfigModel(), "GetNodeConfig",
			map[string]interface{}{"version": "v1"}, nil)
		defer patch.Reset()

		nodeInfo := &data_source.Node{
			SoftDeleteModel: mysql.SoftDeleteModel{Id: 10},
			SourceId:        data_source.FoeyeSourceId,
		}
		indexName, boolQuery, listFields, listDetailFields := getIndexNameBySourceIdAndTaskIdAndSyncType(nodeInfo, 999, data_sync_task.SyncAsset, "test")

		assert.NotEqual(t, "", indexName)
		assert.NotNil(t, boolQuery)
		assert.Greater(t, len(listFields), 0)
		assert.Greater(t, len(listDetailFields), 0)
	})

	// 测试 Foeye - 威胁（普通版本）
	t.Run("Foeye_Threat_V1", func(t *testing.T) {
		// Mock 节点配置获取返回 v1 版本
		patch := ApplyMethodReturn(data_source.NewNodeConfigModel(), "GetNodeConfig",
			map[string]interface{}{"version": "v1"}, nil)
		defer patch.Reset()

		nodeInfo := &data_source.Node{
			SoftDeleteModel: mysql.SoftDeleteModel{Id: 10},
			SourceId:        data_source.FoeyeSourceId,
		}
		indexName, boolQuery, listFields, listDetailFields := getIndexNameBySourceIdAndTaskIdAndSyncType(nodeInfo, 111, data_sync_task.SyncThreat, "test")

		assert.NotEqual(t, "", indexName)
		assert.NotNil(t, boolQuery)
		assert.Greater(t, len(listFields), 0)
		assert.Greater(t, len(listDetailFields), 0)
	})

	// 测试 Foradar - 资产
	t.Run("Foradar_Asset", func(t *testing.T) {
		nodeInfo := &data_source.Node{
			SoftDeleteModel: mysql.SoftDeleteModel{Id: 12},
			SourceId:        data_source.ForadarSourceId,
		}
		indexName, boolQuery, listFields, listDetailFields := getIndexNameBySourceIdAndTaskIdAndSyncType(nodeInfo, 333, data_sync_task.SyncAsset, "")

		assert.NotEqual(t, "", indexName)
		assert.NotNil(t, boolQuery)
		assert.Greater(t, len(listFields), 0)
		assert.Greater(t, len(listDetailFields), 0)
	})

	// 测试 Foradar - 威胁
	t.Run("Foradar_Threat", func(t *testing.T) {
		nodeInfo := &data_source.Node{
			SoftDeleteModel: mysql.SoftDeleteModel{Id: 12},
			SourceId:        data_source.ForadarSourceId,
		}
		indexName, boolQuery, listFields, listDetailFields := getIndexNameBySourceIdAndTaskIdAndSyncType(nodeInfo, 444, data_sync_task.SyncThreat, "test")

		assert.NotEqual(t, "", indexName)
		assert.NotNil(t, boolQuery)
		assert.Greater(t, len(listFields), 0)
		assert.Greater(t, len(listDetailFields), 0)
	})

	// 测试 QTCloud - 资产
	t.Run("QTCloud_Asset", func(t *testing.T) {
		nodeInfo := &data_source.Node{
			SoftDeleteModel: mysql.SoftDeleteModel{Id: 13},
			SourceId:        data_source.QTCloudSourceId,
		}
		indexName, boolQuery, listFields, listDetailFields := getIndexNameBySourceIdAndTaskIdAndSyncType(nodeInfo, 555, data_sync_task.SyncAsset, "")

		assert.NotEqual(t, "", indexName)
		assert.NotNil(t, boolQuery)
		assert.Greater(t, len(listFields), 0)
		assert.Greater(t, len(listDetailFields), 0)
	})

	// 测试 QTCloud - 威胁
	t.Run("QTCloud_Threat", func(t *testing.T) {
		nodeInfo := &data_source.Node{
			SoftDeleteModel: mysql.SoftDeleteModel{Id: 13},
			SourceId:        data_source.QTCloudSourceId,
		}
		indexName, boolQuery, listFields, listDetailFields := getIndexNameBySourceIdAndTaskIdAndSyncType(nodeInfo, 666, data_sync_task.SyncThreat, "test")

		assert.NotEqual(t, "", indexName)
		assert.NotNil(t, boolQuery)
		assert.Greater(t, len(listFields), 0)
		assert.Greater(t, len(listDetailFields), 0)
	})

	// 测试 QTCloud - 人员
	t.Run("QTCloud_People", func(t *testing.T) {
		nodeInfo := &data_source.Node{
			SoftDeleteModel: mysql.SoftDeleteModel{Id: 13},
			SourceId:        data_source.QTCloudSourceId,
		}
		indexName, boolQuery, listFields, listDetailFields := getIndexNameBySourceIdAndTaskIdAndSyncType(nodeInfo, 777, data_sync_task.SyncPeople, "")

		assert.NotEqual(t, "", indexName)
		assert.NotNil(t, boolQuery)
		assert.Greater(t, len(listFields), 0)
		assert.Greater(t, len(listDetailFields), 0)
	})

	// 测试 DingTalk - 人员
	t.Run("DingTalk_People", func(t *testing.T) {
		nodeInfo := &data_source.Node{
			SoftDeleteModel: mysql.SoftDeleteModel{Id: 14},
			SourceId:        data_source.DingTalkSourceId,
		}
		indexName, boolQuery, listFields, listDetailFields := getIndexNameBySourceIdAndTaskIdAndSyncType(nodeInfo, 888, data_sync_task.SyncPeople, "test")

		assert.NotEqual(t, "", indexName)
		assert.NotNil(t, boolQuery)
		assert.Greater(t, len(listFields), 0)
		assert.Greater(t, len(listDetailFields), 0)
	})

	// 测试 BKCmdb - 资产
	t.Run("BKCmdb_Asset", func(t *testing.T) {
		nodeInfo := &data_source.Node{
			SoftDeleteModel: mysql.SoftDeleteModel{Id: 15},
			SourceId:        data_source.BKCmdbSourceId,
		}
		indexName, boolQuery, listFields, listDetailFields := getIndexNameBySourceIdAndTaskIdAndSyncType(nodeInfo, 999, data_sync_task.SyncAsset, "")

		assert.NotEqual(t, "", indexName)
		assert.NotNil(t, boolQuery)
		assert.Greater(t, len(listFields), 0)
		assert.Greater(t, len(listDetailFields), 0)
	})

	// 测试 BKCmdb - 人员
	t.Run("BKCmdb_People", func(t *testing.T) {
		nodeInfo := &data_source.Node{
			SoftDeleteModel: mysql.SoftDeleteModel{Id: 15},
			SourceId:        data_source.BKCmdbSourceId,
		}
		indexName, boolQuery, listFields, listDetailFields := getIndexNameBySourceIdAndTaskIdAndSyncType(nodeInfo, 111, data_sync_task.SyncPeople, "test")

		assert.NotEqual(t, "", indexName)
		assert.NotNil(t, boolQuery)
		assert.Greater(t, len(listFields), 0)
		assert.Greater(t, len(listDetailFields), 0)
	})

	// 测试 FileImport - 资产
	t.Run("FileImport_Asset", func(t *testing.T) {
		nodeInfo := &data_source.Node{
			SoftDeleteModel: mysql.SoftDeleteModel{Id: 16},
			SourceId:        data_source.FileImportSourceId,
		}
		indexName, boolQuery, listFields, listDetailFields := getIndexNameBySourceIdAndTaskIdAndSyncType(nodeInfo, 222, data_sync_task.SyncAsset, "")

		assert.NotEqual(t, "", indexName)
		assert.NotNil(t, boolQuery)
		assert.Greater(t, len(listFields), 0)
		assert.Greater(t, len(listDetailFields), 0)
	})

	// 测试 FileImport - 威胁
	t.Run("FileImport_Threat", func(t *testing.T) {
		nodeInfo := &data_source.Node{
			SoftDeleteModel: mysql.SoftDeleteModel{Id: 16},
			SourceId:        data_source.FileImportSourceId,
		}
		indexName, boolQuery, listFields, listDetailFields := getIndexNameBySourceIdAndTaskIdAndSyncType(nodeInfo, 333, data_sync_task.SyncThreat, "test")

		assert.NotEqual(t, "", indexName)
		assert.NotNil(t, boolQuery)
		assert.Greater(t, len(listFields), 0)
		assert.Greater(t, len(listDetailFields), 0)
	})

	// 测试 FileImport - 人员
	t.Run("FileImport_People", func(t *testing.T) {
		nodeInfo := &data_source.Node{
			SoftDeleteModel: mysql.SoftDeleteModel{Id: 16},
			SourceId:        data_source.FileImportSourceId,
		}
		indexName, boolQuery, listFields, listDetailFields := getIndexNameBySourceIdAndTaskIdAndSyncType(nodeInfo, 444, data_sync_task.SyncPeople, "")

		assert.NotEqual(t, "", indexName)
		assert.NotNil(t, boolQuery)
		assert.Greater(t, len(listFields), 0)
		assert.Greater(t, len(listDetailFields), 0)
	})

	// 测试 QiZhiUAuditHost - 资产
	t.Run("QiZhiUAuditHost_Asset", func(t *testing.T) {
		nodeInfo := &data_source.Node{
			SoftDeleteModel: mysql.SoftDeleteModel{Id: 17},
			SourceId:        data_source.QiZhiUAuditHostSourceId,
		}
		indexName, boolQuery, listFields, listDetailFields := getIndexNameBySourceIdAndTaskIdAndSyncType(nodeInfo, 555, data_sync_task.SyncAsset, "test")

		assert.NotEqual(t, "", indexName)
		assert.NotNil(t, boolQuery)
		assert.Greater(t, len(listFields), 0)
		assert.Greater(t, len(listDetailFields), 0)
	})

	// 测试 QiZhiUAuditHost - 人员（空实现）
	t.Run("QiZhiUAuditHost_People", func(t *testing.T) {
		nodeInfo := &data_source.Node{
			SoftDeleteModel: mysql.SoftDeleteModel{Id: 17},
			SourceId:        data_source.QiZhiUAuditHostSourceId,
		}
		indexName, boolQuery, listFields, listDetailFields := getIndexNameBySourceIdAndTaskIdAndSyncType(nodeInfo, 666, data_sync_task.SyncPeople, "")

		assert.Equal(t, "", indexName) // 空实现
		assert.NotNil(t, boolQuery)
		assert.Equal(t, 0, len(listFields))
		assert.Equal(t, 0, len(listDetailFields))
	})

	// 测试 AliYunCloud - 资产（空实现）
	t.Run("AliYunCloud_Asset", func(t *testing.T) {
		nodeInfo := &data_source.Node{
			SoftDeleteModel: mysql.SoftDeleteModel{Id: 18},
			SourceId:        data_source.AliYunCloudSourceId,
		}
		indexName, boolQuery, listFields, listDetailFields := getIndexNameBySourceIdAndTaskIdAndSyncType(nodeInfo, 777, data_sync_task.SyncAsset, "test")

		assert.Equal(t, "", indexName) // 空实现
		assert.NotNil(t, boolQuery)
		assert.Equal(t, 0, len(listFields))
		assert.Equal(t, 0, len(listDetailFields))
	})

	// 测试 AliYunCloud - 威胁
	t.Run("AliYunCloud_Threat", func(t *testing.T) {
		nodeInfo := &data_source.Node{
			SoftDeleteModel: mysql.SoftDeleteModel{Id: 18},
			SourceId:        data_source.AliYunCloudSourceId,
		}
		indexName, boolQuery, listFields, listDetailFields := getIndexNameBySourceIdAndTaskIdAndSyncType(nodeInfo, 888, data_sync_task.SyncThreat, "")

		assert.NotEqual(t, "", indexName)
		assert.NotNil(t, boolQuery)
		assert.Greater(t, len(listFields), 0)
		assert.Greater(t, len(listDetailFields), 0)
	})

	// 测试 WeiBu - 资产
	t.Run("WeiBu_Asset", func(t *testing.T) {
		nodeInfo := &data_source.Node{
			SoftDeleteModel: mysql.SoftDeleteModel{Id: 19},
			SourceId:        data_source.WeiBuSourceId,
		}
		indexName, boolQuery, listFields, listDetailFields := getIndexNameBySourceIdAndTaskIdAndSyncType(nodeInfo, 999, data_sync_task.SyncAsset, "test")

		assert.NotEqual(t, "", indexName)
		assert.NotNil(t, boolQuery)
		assert.Greater(t, len(listFields), 0)
		assert.Greater(t, len(listDetailFields), 0)
	})

	// 测试 WeiBu - 威胁
	t.Run("WeiBu_Threat", func(t *testing.T) {
		nodeInfo := &data_source.Node{
			SoftDeleteModel: mysql.SoftDeleteModel{Id: 19},
			SourceId:        data_source.WeiBuSourceId,
		}
		indexName, boolQuery, listFields, listDetailFields := getIndexNameBySourceIdAndTaskIdAndSyncType(nodeInfo, 111, data_sync_task.SyncThreat, "")

		assert.NotEqual(t, "", indexName)
		assert.NotNil(t, boolQuery)
		assert.Greater(t, len(listFields), 0)
		assert.Greater(t, len(listDetailFields), 0)
	})

	// 测试 ChangTingWAF - 资产
	t.Run("ChangTingWAF_Asset", func(t *testing.T) {
		nodeInfo := &data_source.Node{
			SoftDeleteModel: mysql.SoftDeleteModel{Id: 20},
			SourceId:        data_source.ChangTingWAFSourceId,
		}
		indexName, boolQuery, listFields, listDetailFields := getIndexNameBySourceIdAndTaskIdAndSyncType(nodeInfo, 222, data_sync_task.SyncAsset, "test")

		assert.NotEqual(t, "", indexName)
		assert.NotNil(t, boolQuery)
		assert.Greater(t, len(listFields), 0)
		assert.Greater(t, len(listDetailFields), 0)
	})

	// 测试 MachLake - 资产
	t.Run("MachLake_Asset", func(t *testing.T) {
		nodeInfo := &data_source.Node{
			SoftDeleteModel: mysql.SoftDeleteModel{Id: 21},
			SourceId:        data_source.MachLakeSourceId,
		}
		indexName, boolQuery, listFields, listDetailFields := getIndexNameBySourceIdAndTaskIdAndSyncType(nodeInfo, 333, data_sync_task.SyncAsset, "")

		assert.NotEqual(t, "", indexName)
		assert.NotNil(t, boolQuery)
		assert.Greater(t, len(listFields), 0)
		assert.Greater(t, len(listDetailFields), 0)
	})

	// 测试 XRay - 威胁
	t.Run("XRay_Threat", func(t *testing.T) {
		nodeInfo := &data_source.Node{
			SoftDeleteModel: mysql.SoftDeleteModel{Id: 22},
			SourceId:        data_source.XRaySourceId,
		}
		indexName, boolQuery, listFields, listDetailFields := getIndexNameBySourceIdAndTaskIdAndSyncType(nodeInfo, 444, data_sync_task.SyncThreat, "test")

		assert.NotEqual(t, "", indexName)
		assert.NotNil(t, boolQuery)
		assert.Greater(t, len(listFields), 0)
		assert.Greater(t, len(listDetailFields), 0)
	})

	// 测试 XRay - 资产
	t.Run("XRay_Asset", func(t *testing.T) {
		nodeInfo := &data_source.Node{
			SoftDeleteModel: mysql.SoftDeleteModel{Id: 22},
			SourceId:        data_source.XRaySourceId,
		}
		indexName, boolQuery, listFields, listDetailFields := getIndexNameBySourceIdAndTaskIdAndSyncType(nodeInfo, 555, data_sync_task.SyncAsset, "")

		assert.NotEqual(t, "", indexName)
		assert.NotNil(t, boolQuery)
		assert.Greater(t, len(listFields), 0)
		assert.Greater(t, len(listDetailFields), 0)
	})

	// 测试 AnhengAPT - 资产
	t.Run("AnhengAPT_Asset", func(t *testing.T) {
		nodeInfo := &data_source.Node{
			SoftDeleteModel: mysql.SoftDeleteModel{Id: 23},
			SourceId:        data_source.AnhengAPT,
		}
		indexName, boolQuery, listFields, listDetailFields := getIndexNameBySourceIdAndTaskIdAndSyncType(nodeInfo, 666, data_sync_task.SyncAsset, "test")

		assert.NotEqual(t, "", indexName)
		assert.NotNil(t, boolQuery)
		assert.Greater(t, len(listFields), 0)
		assert.Greater(t, len(listDetailFields), 0)
	})

	// 测试 QiAnXinPam - 资产
	t.Run("QiAnXinPam_Asset", func(t *testing.T) {
		nodeInfo := &data_source.Node{
			SoftDeleteModel: mysql.SoftDeleteModel{Id: 24},
			SourceId:        data_source.QiAnXinPamSourceId,
		}
		indexName, boolQuery, listFields, listDetailFields := getIndexNameBySourceIdAndTaskIdAndSyncType(nodeInfo, 777, data_sync_task.SyncAsset, "")

		assert.NotEqual(t, "", indexName)
		assert.NotNil(t, boolQuery)
		assert.Greater(t, len(listFields), 0)
		assert.Greater(t, len(listDetailFields), 0)
	})

	// 测试 QiAnXinPam - 人员
	t.Run("QiAnXinPam_People", func(t *testing.T) {
		nodeInfo := &data_source.Node{
			SoftDeleteModel: mysql.SoftDeleteModel{Id: 24},
			SourceId:        data_source.QiAnXinPamSourceId,
		}
		indexName, boolQuery, listFields, listDetailFields := getIndexNameBySourceIdAndTaskIdAndSyncType(nodeInfo, 888, data_sync_task.SyncPeople, "test")

		assert.NotEqual(t, "", indexName)
		assert.NotNil(t, boolQuery)
		assert.Greater(t, len(listFields), 0)
		assert.Greater(t, len(listDetailFields), 0)
	})

	// 测试版本配置获取失败的情况
	t.Run("Foeye_ConfigError", func(t *testing.T) {
		// Mock 节点配置获取失败
		patch := ApplyMethodReturn(data_source.NewNodeConfigModel(), "GetNodeConfig",
			nil, errors.New("config error"))
		defer patch.Reset()

		nodeInfo := &data_source.Node{
			SoftDeleteModel: mysql.SoftDeleteModel{Id: 28},
			SourceId:        data_source.FoeyeSourceId,
		}
		indexName, boolQuery, listFields, listDetailFields := getIndexNameBySourceIdAndTaskIdAndSyncType(nodeInfo, 333, data_sync_task.SyncAsset, "")

		assert.NotEqual(t, "", indexName) // 应该使用默认版本
		assert.NotNil(t, boolQuery)
		assert.Greater(t, len(listFields), 0)
		assert.Greater(t, len(listDetailFields), 0)
	})

	// 测试版本配置类型转换失败的情况
	t.Run("Foeye_VersionTypeError", func(t *testing.T) {
		// Mock 节点配置获取返回非字符串类型的版本
		patch := ApplyMethodReturn(data_source.NewNodeConfigModel(), "GetNodeConfig",
			map[string]interface{}{"version": 123}, nil) // 数字而非字符串
		defer patch.Reset()

		nodeInfo := &data_source.Node{
			SoftDeleteModel: mysql.SoftDeleteModel{Id: 29},
			SourceId:        data_source.FoeyeSourceId,
		}
		indexName, boolQuery, listFields, listDetailFields := getIndexNameBySourceIdAndTaskIdAndSyncType(nodeInfo, 444, data_sync_task.SyncAsset, "test")

		assert.NotEqual(t, "", indexName) // 应该使用默认版本
		assert.NotNil(t, boolQuery)
		assert.Greater(t, len(listFields), 0)
		assert.Greater(t, len(listDetailFields), 0)
	})

	// 测试非 Foeye/D01 数据源的版本处理
	t.Run("NonVersionAware_DataSource", func(t *testing.T) {
		nodeInfo := &data_source.Node{
			SoftDeleteModel: mysql.SoftDeleteModel{Id: 30},
			SourceId:        data_source.YuntuSourceId, // 非版本感知数据源
		}
		indexName, boolQuery, listFields, listDetailFields := getIndexNameBySourceIdAndTaskIdAndSyncType(nodeInfo, 555, data_sync_task.SyncAsset, "")

		assert.NotEqual(t, "", indexName)
		assert.NotNil(t, boolQuery)
		assert.Greater(t, len(listFields), 0)
		assert.Greater(t, len(listDetailFields), 0)
	})
}
