package user_access

import (
	testcommon "fobrain/fobrain/tests/common_test"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
)

func TestCheckPassword(t *testing.T) {
	t.Run("less than 8 digits", func(t *testing.T) {
		assert.False(t, CheckPassword("cc"))
	})

	t.Run("less than 8 digits", func(t *testing.T) {
		assert.False(t, CheckPassword("Fll.123"))
	})

	t.Run("More than 20 digits", func(t *testing.T) {
		assert.False(t, CheckPassword("Flssdf.2123sdfadfjjjjjdssdOOos999"))
	})

	t.Run("return true", func(t *testing.T) {
		assert.True(t, CheckPassword("Flower.123"))
	})

	t.Run("return 8 true", func(t *testing.T) {
		assert.True(t, CheckPassword("Fss1ds.1"))
	})

	t.Run("return 20 true", func(t *testing.T) {
		assert.True(t, CheckPassword("Floskd@#ds1.d1123219"))
	})

	t.Run("1234!aA1 true", func(t *testing.T) {
		assert.True(t, CheckPassword("1234!aA1"))
	})

	t.Run("1234!aA false", func(t *testing.T) {
		assert.False(t, CheckPassword("1234!aA"))
	})

	t.Run("1234!aA false", func(t *testing.T) {
		assert.False(t, CheckPassword("ddddddd"))
	})
}

func TestUpdateRuleInfoById(t *testing.T) {
	userInfo := &UpdateUserRuleInfoConfig{
		RuleInfo: "",
	}
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()
	mockDb.ExpectQuery("SELECT * FROM `users` WHERE id =? ORDER BY `users`.`id` LIMIT 1").
		WithArgs(1).
		WillReturnRows(sqlmock.NewRows([]string{"id", "account", "username", "password", "role_id", "status"}).
			AddRow(1, "admin", "admin", "admin", 1, 1))

	mockDb.ExpectBegin()
	mockDb.ExpectExec("UPDATE `users` SET `rule_info`=?,`updated_at`=? WHERE `id` = ?").
		WithArgs(userInfo.RuleInfo, sqlmock.AnyArg(), 1).
		WillReturnResult(sqlmock.NewResult(1, 1))
	mockDb.ExpectCommit()

	err := UpdateRuleInfoById("", 1)
	assert.NoError(t, err)
}
