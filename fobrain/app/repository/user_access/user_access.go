package user_access

import (
	"fmt"
	"regexp"

	"fobrain/models/mysql/user"
)

type UserInfo struct {
	Id       uint64   `json:"id" zh:"ID"`
	Account  string   `json:"account" zh:"用户名" validate:"required"`
	Username string   `json:"username" zh:"人员台账名称" validate:"required"`
	Password string   `json:"password" zh:"密码" validate:"required"`
	RoleId   uint64   `json:"role_id" zh:"角色ID" validate:"required"`
	StaffIds []string `json:"staff_ids" zh:"人员台账ID" validate:"required"`
}

type UpdateUserInfo struct {
	Id       uint64   `json:"id" zh:"ID"`
	Account  string   `json:"account" zh:"用户名" validate:"required"`
	Username string   `json:"username" zh:"人员台账名称" validate:"required"`
	RoleId   uint64   `json:"role_id" zh:"角色ID" validate:"required"`
	StaffIds []string `json:"staff_ids" zh:"人员台账ID" validate:"required"`
	Password string   `json:"password" zh:"密码"`
}

type UpdateUserRuleInfoConfig struct {
	RuleInfo string `json:"rule_info"`
}

// CheckPassword
// @Summary 检查密码是否符合 定义正则表达式：密码必须包含大小写字母、数字、特殊符号，长度在8到20位
func CheckPassword(password string) bool {
	// 定义各个条件的正则表达式
	hasUpper := regexp.MustCompile(`[A-Z]`)
	hasLower := regexp.MustCompile(`[a-z]`)
	hasDigit := regexp.MustCompile(`\d`)
	hasSpecial := regexp.MustCompile(`[!@#$%^&*()_+{}\[\]:;<>,.?~\-]`)
	isValidLength := regexp.MustCompile(`^.{8,20}$`)

	// 验证各个条件
	if hasUpper.MatchString(password) &&
		hasLower.MatchString(password) &&
		hasDigit.MatchString(password) &&
		hasSpecial.MatchString(password) &&
		isValidLength.MatchString(password) {
		return true
	} else {
		return false
	}
}

// UpdateRuleInfoById 根据用户ID更新规则显示配置
func UpdateRuleInfoById(value string, id uint64) error {
	userInformation, err := user.NewUserModel().GetUserInfo(id)
	if err != nil {
		return err
	}
	if userInformation == nil {
		return fmt.Errorf("用户不存在")
	}
	return user.NewUserModel().Model(userInformation).Update("rule_info", value).Error
}
