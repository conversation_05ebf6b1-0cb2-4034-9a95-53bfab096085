package asset_mapping

import (
	"errors"
	"fmt"
	req "fobrain/fobrain/app/request/asset_mapping"
	"fobrain/initialize/mysql"
	"fobrain/models/mysql/asset_mapping"
	"fobrain/models/mysql/custom_field"
	"fobrain/pkg/utils"
)

func Index(req *req.IndexRequest) (any, int64, error) {
	var total int64

	handlers := make([]mysql.HandleFunc, 0)
	handlers = append(handlers, mysql.WithOrder("created_at DESC"))
	handlers = utils.CanAppend(req.Id != 0, handlers, mysql.WithWhere("id = ?", req.Id))
	handlers = utils.CanAppend(req.Keyword != "", handlers, mysql.WithWhere("host_ip LIKE ?", "%"+req.Keyword+"%"))
	handlers = utils.CanAppend(req.Keyword != "", handlers, mysql.WithOrWhere("host_port LIKE ?", "%"+req.Keyword+"%"))
	handlers = utils.CanAppend(req.Keyword != "", handlers, mysql.WithOrWhere("float_ip LIKE ?", "%"+req.Keyword+"%"))
	handlers = utils.CanAppend(req.Keyword != "", handlers, mysql.WithOrWhere("float_port LIKE ?", "%"+req.Keyword+"%"))
	handlers = utils.CanAppend(req.Keyword != "", handlers, mysql.WithOrWhere("load_ip LIKE ?", "%"+req.Keyword+"%"))
	handlers = utils.CanAppend(req.Keyword != "", handlers, mysql.WithOrWhere("load_port LIKE ?", "%"+req.Keyword+"%"))
	handlers = utils.CanAppend(req.Keyword != "", handlers, mysql.WithOrWhere("ssl_ip LIKE ?", "%"+req.Keyword+"%"))
	handlers = utils.CanAppend(req.Keyword != "", handlers, mysql.WithOrWhere("ssl_port LIKE ?", "%"+req.Keyword+"%"))
	handlers = utils.CanAppend(req.Keyword != "", handlers, mysql.WithOrWhere("dmz_ip LIKE ?", "%"+req.Keyword+"%"))
	handlers = utils.CanAppend(req.Keyword != "", handlers, mysql.WithOrWhere("dmz_port LIKE ?", "%"+req.Keyword+"%"))
	handlers = utils.CanAppend(req.Keyword != "", handlers, mysql.WithOrWhere("finance_ip LIKE ?", "%"+req.Keyword+"%"))
	handlers = utils.CanAppend(req.Keyword != "", handlers, mysql.WithOrWhere("finance_port LIKE ?", "%"+req.Keyword+"%"))
	handlers = utils.CanAppend(req.Keyword != "", handlers, mysql.WithOrWhere("domain LIKE ?", "%"+req.Keyword+"%"))

	data, total, err := asset_mapping.NewAssetMappingModel().List(req.Page, req.PerPage, handlers...)
	if err != nil {
		return nil, 0, err
	}
	listFields := []map[string]string{
		{"all_key": "host_ip", "name": "主机IP", "type": "host"},
		{"all_key": "host_port", "name": "主机端口", "type": "host"},
		{"all_key": "float_ip", "name": "浮动IP", "type": "float"},
		{"all_key": "float_port", "name": "浮动端口", "type": "float"},
		{"all_key": "load_ip", "name": "负载IP", "type": "load"},
		{"all_key": "load_port", "name": "负载端口", "type": "load"},
		{"all_key": "ssl_ip", "name": "SSL卸载IP", "type": "ssh"},
		{"all_key": "ssl_port", "name": "SSL卸载端口", "type": "ssh"},
		{"all_key": "dmz_ip", "name": "DMZ区IP", "type": "dmz"},
		{"all_key": "dmz_port", "name": "DMZ区端口", "type": "dmz"},
		{"all_key": "finance_ip", "name": "金融城域网IP", "type": "finance"},
		{"all_key": "finance_port", "name": "金融城域网端口", "type": "finance"},
		{"all_key": "domain", "name": "域名", "type": ""},
	}
	customFields, err := custom_field.NewCustomFieldModel().ListByTable("asset_mappings")
	for _, customField := range customFields {
		var ok = false
		for _, listField := range listFields {
			if listField["all_key"] == customField.FieldName || listField["name"] == customField.DisplayName {
				ok = true
				break
			}
		}
		if !ok {
			listFields = append(listFields, map[string]string{
				"all_key": customField.FieldName,
				"name":    customField.DisplayName,
				"type":    "",
			})
		}
	}
	// todo
	return map[string]interface{}{
		"items":       data,
		"list_fields": listFields,
	}, total, err
}

func Store(data map[string]interface{}) error {
	oldAssetMapping, err := asset_mapping.NewAssetMappingModel().First(mysql.WithWhere("`host_ip` = ? AND `host_port` = ?", data["host_ip"], data["host_port"]))
	if err == nil && len(oldAssetMapping) > 0 {
		return errors.New("资产映射已存在")
	}

	err = checkData(data)
	if err != nil {
		return err
	}

	for key, value := range data {
		if value == "" {
			delete(data, key)
		}
	}
	err = asset_mapping.NewAssetMappingModel().Create(data)
	if err != nil {
		return err
	}

	return err
}

func Update(data map[string]interface{}) error {
	// 初始化查询条件
	where := "`host_ip` = ? AND `host_port` = ?"
	params := []interface{}{data["host_ip"], data["host_port"]}

	// 如果存在 ID，追加查询条件
	// 检查字段是否存在
	val, ok := data["id"]
	if ok {
		// 确保类型是 float64
		floatVal, ok := val.(float64)
		if !ok {
			return fmt.Errorf("ID 类型错误")
		}
		// 安全地转换为 uint64
		id := uint64(floatVal)
		where += " AND `id` != ?"
		params = append(params, id)
	}

	// 构建最终查询
	query := mysql.WithWhere(where, params...)
	// 执行查询
	oldAssetMapping, err := asset_mapping.NewAssetMappingModel().First(query)
	if err == nil && len(oldAssetMapping) > 0 {
		return errors.New("资产映射已存在")
	}

	err = checkData(data)
	if err != nil {
		return err
	}

	for key, value := range data {
		// 判断 value类型
		if value == "" {
			data[key] = nil
			continue
		}
		if utils.StrContains(key, "port") {
			if floatVal, ok := value.(float64); ok {
				data[key] = int64(floatVal)
			}

		}
	}
	err = asset_mapping.NewAssetMappingModel().Update(data)

	if err != nil {
		return err
	}

	return err
}

func Destroy(ids []uint64, keyword string) error {
	handlers := make([]mysql.HandleFunc, 0)
	handlers = utils.CanAppend(keyword != "", handlers, mysql.WithWhere("host_ip LIKE ?", "%"+keyword+"%"))
	handlers = utils.CanAppend(keyword != "", handlers, mysql.WithOrWhere("host_port LIKE ?", "%"+keyword+"%"))
	handlers = utils.CanAppend(keyword != "", handlers, mysql.WithOrWhere("float_ip LIKE ?", "%"+keyword+"%"))
	handlers = utils.CanAppend(keyword != "", handlers, mysql.WithOrWhere("float_port LIKE ?", "%"+keyword+"%"))
	handlers = utils.CanAppend(keyword != "", handlers, mysql.WithOrWhere("load_ip LIKE ?", "%"+keyword+"%"))
	handlers = utils.CanAppend(keyword != "", handlers, mysql.WithOrWhere("load_port LIKE ?", "%"+keyword+"%"))
	handlers = utils.CanAppend(keyword != "", handlers, mysql.WithOrWhere("ssl_ip LIKE ?", "%"+keyword+"%"))
	handlers = utils.CanAppend(keyword != "", handlers, mysql.WithOrWhere("ssl_port LIKE ?", "%"+keyword+"%"))
	handlers = utils.CanAppend(keyword != "", handlers, mysql.WithOrWhere("dmz_ip LIKE ?", "%"+keyword+"%"))
	handlers = utils.CanAppend(keyword != "", handlers, mysql.WithOrWhere("dmz_port LIKE ?", "%"+keyword+"%"))
	handlers = utils.CanAppend(keyword != "", handlers, mysql.WithOrWhere("finance_ip LIKE ?", "%"+keyword+"%"))
	handlers = utils.CanAppend(keyword != "", handlers, mysql.WithOrWhere("finance_port LIKE ?", "%"+keyword+"%"))
	handlers = utils.CanAppend(keyword != "", handlers, mysql.WithOrWhere("domain LIKE ?", "%"+keyword+"%"))

	if len(ids) <= 0 || ids[0] == 0 {
		//删除所有
		err := asset_mapping.NewAssetMappingModel().Delete(mysql.WithWhere("id > 0"))
		if err != nil {
			return err
		}

		return nil
	}

	//删除指定ids
	err := asset_mapping.NewAssetMappingModel().Delete(mysql.WithWhere("id in  (?)", ids))
	if err != nil {
		return err
	}

	return nil
}

func checkData(data map[string]interface{}) error {
	if _, ok := data["host_ip"].(string); ok && !utils.IsIP(data["host_ip"].(string)) {
		return errors.New("主机IP格式不正确")
	}
	if _, ok := data["host_port"]; ok && data["host_port"].(string) != "" && !utils.IsValidatePort(data["host_port"].(string)) {
		return errors.New("主机端口格式不正确")
	}
	if _, ok := data["float_ip"].(string); ok && data["float_ip"].(string) != "" && !utils.IsIP(data["float_ip"].(string)) {
		return errors.New("浮动IP格式不正确")
	}
	if _, ok := data["float_port"]; ok && data["float_port"].(string) != "" && !utils.IsValidatePort(data["float_port"].(string)) {
		return errors.New("浮动端口格式不正确")
	}
	if _, ok := data["load_ip"].(string); ok && data["load_ip"].(string) != "" && !utils.IsIP(data["load_ip"].(string)) {
		return errors.New("负载IP格式不正确")
	}
	if _, ok := data["load_port"]; ok && data["load_port"].(string) != "" && !utils.IsValidatePort(data["load_port"].(string)) {
		return errors.New("负载端口格式不正确")
	}
	if _, ok := data["ssl_ip"]; ok && data["ssl_ip"].(string) != "" && !utils.IsIP(data["ssl_ip"].(string)) {
		return errors.New("SSL卸载IP格式不正确")
	}
	if _, ok := data["ssl_port"]; ok && data["ssl_port"].(string) != "" && !utils.IsValidatePort(data["ssl_port"].(string)) {
		return errors.New("SSL卸载端口格式不正确")
	}
	if _, ok := data["dmz_ip"]; ok && data["dmz_ip"].(string) != "" && !utils.IsIP(data["dmz_ip"].(string)) {
		return errors.New("DMZ区IP格式不正确")
	}
	if _, ok := data["dmz_port"]; ok && data["dmz_port"].(string) != "" && !utils.IsValidatePort(data["dmz_port"].(string)) {
		return errors.New("DMZ区端口格式不正确")
	}
	if _, ok := data["finance_ip"]; ok && data["finance_ip"].(string) != "" && !utils.IsIP(data["finance_ip"].(string)) {
		return errors.New("金融域域网IP格式不正确")
	}
	if _, ok := data["finance_port"]; ok && data["finance_port"].(string) != "" && !utils.IsValidatePort(data["finance_port"].(string)) {
		return errors.New("金融域域网端口格式不正确")
	}
	return nil
}
