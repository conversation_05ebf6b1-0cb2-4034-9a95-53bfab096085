package asset_mapping

import (
	"testing"
)

func TestCheckData(t *testing.T) {
	tests := []struct {
		name    string
		data    map[string]interface{}
		wantErr bool
	}{
		//{
		//	name:    "空数据测试",
		//	data:    nil,
		//	wantErr: true,
		//},
		{
			name:    "主机IP格式不正确测试",
			data:    map[string]interface{}{"host_ip": "***********.1"},
			wantErr: true,
		},
		//{
		//	name:    "主机端口格式不正确测试",
		//	data:    map[string]interface{}{"host_ip": "***********", "host_port": "808080"},
		//	wantErr: true,
		//},
		{
			name:    "浮动IP格式不正确测试",
			data:    map[string]interface{}{"host_ip": "***********", "host_port": "8080", "float_ip": "***********.1"},
			wantErr: true,
		},
		{
			name:    "浮动IP为空测试",
			data:    map[string]interface{}{"host_ip": "***********", "host_port": "8080", "float_ip": nil},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := checkData(tt.data); (err != nil) != tt.wantErr {
				t.Errorf("checkData() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
