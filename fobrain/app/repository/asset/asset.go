package asset

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"fobrain/fobrain/app/services/permission"
	"fobrain/initialize/mysql"
	"fobrain/models/mysql/merge"
	"fobrain/models/mysql/net_mapping"
	"io"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"

	"fobrain/fobrain/app/request/asset_center"
	"fobrain/fobrain/common/localtime"
	"fobrain/initialize/es"
	pb "fobrain/mergeService/proto"
	filtrate "fobrain/models/elastic"
	"fobrain/models/elastic/assets"
	"fobrain/models/elastic/business_system"
	"fobrain/models/elastic/poc"
	"fobrain/models/mysql/data_source"
	db "fobrain/models/mysql/ip_mapping"

	"git.gobies.org/caasm/fobrain-components/utils"

	"github.com/olivere/elastic/v7"
	pkgerror "github.com/pkg/errors"
	"go-micro.dev/v4/logger"
)

const (
	// NetworkTypeInternal 内网资产
	NetworkTypeInternal = 1
	// NetworkTypeExternal 外网资产
	NetworkTypeExternal = 2
	// NotIsRecycleBin 标识未进入回收站
	NotIsRecycleBin = 1
	// IsRecycleBin 标识已进入回收站
	IsRecycleBin = 2
)

type Department struct {
	Id       uint64        `json:"id"`
	Name     string        `json:"name"`
	UserId   string        `json:"user_id"`
	UserName string        `json:"user_name"`
	Parents  []*Department `json:"parents"`
}

type BusinessInfo struct {
	SystemId   string        `json:"system_id"`
	Name       string        `json:"name"`
	UserId     string        `json:"user_id"`
	UserName   string        `json:"user_name"`
	Department []*Department `json:"department"`
}

type OperInfo struct {
	Oper       string        `json:"oper"`
	UserId     string        `json:"user_id"`
	UserName   string        `json:"user_name"`
	Department []*Department `json:"department"`
}

type AssetDTO struct {
	Assets       map[string]interface{} `json:"assets"`
	BusinessInfo []*BusinessInfo        `json:"business_info"`
	OperInfo     []*OperInfo            `json:"oper_info"`
}

// CreateBoolQuery
// @description: 构造BoolQuery
func CreateBoolQuery(keyword string, networkType, isRecycleBin int, ids []string, params *asset_center.InternalAssetRequest) (*elastic.BoolQuery, error) {
	boolQuery := elastic.NewBoolQuery()
	if isRecycleBin == NotIsRecycleBin {
		boolQuery = boolQuery.MustNot(elastic.NewExistsQuery("deleted_at"))
	} else if isRecycleBin == IsRecycleBin {
		boolQuery = boolQuery.Must(elastic.NewExistsQuery("deleted_at"))
	}

	boolQuery = boolQuery.MustNot(elastic.NewExistsQuery("purged_at"))

	if keyword != "" {
		boolQuery = assets.NewAssets().NewKeywordQuery(keyword, boolQuery)
	}
	if networkType != 0 {
		boolQuery = filtrate.BuildBoolQuery("network_type", "==", "and", []int{networkType}, boolQuery)
		//boolQuery = boolQuery.Must(elastic.NewTermQuery("network_type", networkType))
	}
	if len(utils.CompactStrings(ids)) > 0 {
		boolQuery = boolQuery.Must(elastic.NewTermsQueryFromStrings("id", ids...))
	}
	//else {
	//	boolQuery = boolQuery.Must(elastic.NewMatchAllQuery())
	//}
	if params != nil && len(params.SearchCondition) > 0 {
		conditions, err := filtrate.ParseQueryConditions(params.SearchCondition)
		if err != nil {
			return nil, fmt.Errorf("解析查询条件失败,%v", err)
		}

		for _, condition := range conditions {
			boolQuery = filtrate.BuildBoolQuery(condition.Field, condition.OperationTypeString, condition.LogicalConnective, condition.Value, boolQuery)
		}
	}
	return boolQuery, nil
}

// searchAssets
// @description: 搜索资产
func searchAssets(query *elastic.BoolQuery, page, perPage int, fieldKey string, fieldSort string, selectFields []string) (*elastic.SearchResult, error) {
	searchService := es.GetEsClient().Search(assets.NewAssets().IndexName()).From(es.GetFrom(page, perPage)).
		Size(es.GetSize(perPage)).
		TrackTotalHits(true).
		Query(query)

	// 如果指定了需要返回的字段列表，则只返回这些字段
	if len(selectFields) > 0 {
		searchService = searchService.FetchSourceContext(elastic.NewFetchSourceContext(true).Include(selectFields...))
	}

	sortList := searchSort(fieldKey, fieldSort)
	for key, value := range sortList {
		searchService = searchService.Sort(key, value)
	}

	return searchService.Do(context.TODO())
}

// searchSort 设置排序条件
func searchSort(fieldKey string, fieldSort string) map[string]bool {
	sortList := make(map[string]bool)
	if fieldKey != "" {
		sortList[fieldKey] = fieldSort == "ascend"
		return sortList
	}
	sortList["updated_at"] = false
	sortList["created_at"] = false
	sortList["id"] = false
	return sortList
}

// SearchAssetsCount
// queryType 查询类型
// field 查询字段
// match/wildcard 查询的值
// nestedPath nested类型查询的路径
func SearchAssetsCount(queryType, field, value, nestedPath string, additionQueries ...elastic.Query) (int64, error) {
	var esQuery elastic.Query
	// 创建布尔查询
	boolQuery := elastic.NewBoolQuery()
	//去除回收站数据
	boolQuery.Must(elastic.NewBoolQuery().MustNot(elastic.NewExistsQuery("deleted_at"), elastic.NewExistsQuery("purged_at")))
	// 排除 "create_at" 不为空的文档
	boolQuery.MustNot(
		elastic.NewExistsQuery("create_at"),
	)

	for _, q := range additionQueries {
		boolQuery = boolQuery.Must(q)
	}

	// 判断查询类型并创建相应的查询
	switch queryType {
	case "exists":
		// "exists" 查询，检查字段是否存在
		if nestedPath != "" {
			// 创建 Nested 查询，路径是 nestedPath
			nestedQuery := elastic.NewNestedQuery(nestedPath, elastic.NewExistsQuery(field))
			spaceQuery := elastic.NewNestedQuery(nestedPath, elastic.NewTermQuery(field, ""))
			boolQuery.Must(nestedQuery)
			boolQuery.MustNot(spaceQuery)
		} else {
			// 普通查询，字段是否存在
			existsQuery := elastic.NewExistsQuery(field)
			boolQuery.Must(existsQuery)
			boolQuery.MustNot(elastic.NewTermQuery(field, ""))
		}
	case "match":
		// "match" 查询，检查字段是否匹配指定值
		if nestedPath != "" {
			// 创建 Nested 查询，路径是 nestedPath
			nestedQuery := elastic.NewNestedQuery(nestedPath, elastic.NewTermQuery(field, value))
			boolQuery.Must(nestedQuery)
		} else {
			// 普通查询，字段值是否匹配指定值
			matchQuery := elastic.NewMatchQuery(field, value)
			boolQuery.Must(matchQuery)
		}
	case "wildcard":
		// "wildcard" 查询，字段是否匹配通配符
		if nestedPath != "" {
			// 创建 Nested 查询，路径是 nestedPath
			nestedQuery := elastic.NewNestedQuery(nestedPath, elastic.NewWildcardQuery(field, "*"+value+"*"))
			boolQuery.Must(nestedQuery)
		} else {
			// 普通查询，字段值是否匹配通配符
			wildcardQuery := elastic.NewWildcardQuery(field, "*"+value+"*")
			boolQuery.Must(wildcardQuery)
		}
	default:
		return 0, fmt.Errorf("unsupported query type: %s", queryType)
	}
	// 构造最终查询
	esQuery = boolQuery

	// 执行查询
	searchService := es.GetEsClient().Search().
		Index(assets.NewAssets().IndexName()). // 设置索引
		Query(esQuery).                        // 使用构建的查询
		Size(0)                                // 设置 Size 为 0，因为只关心总数

	// 根据字段判断是否需要去重聚合
	if field == "business.person_base.name" {
		// 使用 Nested 聚合
		searchService = searchService.Aggregation(
			"nested_agg",
			elastic.NewNestedAggregation().
				Path("business.person_base"). // 指定 nested 路径
				SubAggregation("unique_field_count",
					elastic.NewCardinalityAggregation().Field("business.person_base.name"), // Cardinality 聚合
				),
		)
	}
	// 执行查询
	searchResult, err := searchService.Do(context.TODO())
	if err != nil {
		return 0, fmt.Errorf("error executing search query: %v", err)
	}
	// 返回查询结果的总数据量
	var total int64

	// 解析 Nested 聚合
	if field == "business.person_base.name" {
		nestedAgg, found := searchResult.Aggregations.Nested("nested_agg")
		if !found {
			return 0, fmt.Errorf("nested aggregation result not found")
		}
		cardinalityAgg, found := nestedAgg.Cardinality("unique_field_count")
		if !found {
			return 0, fmt.Errorf("cardinality aggregation result not found")
		}
		total := int64(*cardinalityAgg.Value)
		return total, nil
	} else {
		// 如果不是去重字段，直接获取查询的总数
		totalHits := searchResult.Hits.TotalHits.Value
		total = int64(totalHits)
	}

	return total, nil
}

// ParseAssets
// @description: 解析资产
func ParseAssets(hits []*elastic.SearchHit) ([]any, error) {
	list := make([]any, 0, len(hits))
	for _, hit := range hits {
		asset := utils.ParseSampleHash(hit, &assets.Assets{})
		data, _, err := db.NewIpMappingModel().List(1, 100, fmt.Sprintf("%s", asset["ip"]))
		if err != nil {
			logger.Warnf("get IpMappingStatistics List error: %v", err)
		}

		//关联关系
		asset["ip_mappings"] = func() map[string]*pb.PortMappingList {
			portMappingList := make(map[string]*pb.PortMappingList)

			switch asset["network_type"] {
			case "内网":
				for _, datum := range data {
					if portMappingList[cast.ToString(datum["PublicIp"])] == nil {
						portMappingList[cast.ToString(datum["PublicIp"])] = &pb.PortMappingList{
							Mappings: []*pb.PortMapping{},
						}
					}
					portMappingList[cast.ToString(datum["PublicIp"])] = &pb.PortMappingList{
						Mappings: append(portMappingList[cast.ToString(datum["PublicIp"])].Mappings, &pb.PortMapping{
							PublicIp:    cast.ToString(datum["PublicIp"]),
							PublicPort:  cast.ToUint64(datum["PublicPort"]),
							PrivateIp:   cast.ToString(datum["PrivateIp"]),
							PrivatePort: cast.ToUint64(datum["PrivatePort"]),
						}),
					}
				}
			case "互联网":
				for _, datum := range data {
					if portMappingList[cast.ToString(datum["PrivateIp"])] == nil {
						portMappingList[cast.ToString(datum["PrivateIp"])] = &pb.PortMappingList{
							Mappings: []*pb.PortMapping{},
						}
					}
					portMappingList[datum["PrivateIp"].(string)] = &pb.PortMappingList{
						Mappings: append(portMappingList[datum["PrivateIp"].(string)].Mappings, &pb.PortMapping{
							PublicIp:    cast.ToString(datum["PublicIp"]),
							PublicPort:  cast.ToUint64(datum["PublicPort"]),
							PrivateIp:   cast.ToString(datum["PrivateIp"]),
							PrivatePort: cast.ToUint64(datum["PrivatePort"]),
						}),
					}
				}
			}

			return portMappingList
		}()

		// 运维人员部门
		operDepartment := make([]*pb.OperDepartment, 0)
		operDepartmentList := make([]*assets.DepartmentBase, 0)
		if asset["oper_department"] != nil {
			operDepartmentList = asset["oper_department"].([]*assets.DepartmentBase)
		}
		if len(asset["oper"].([]string)) > 0 {
			for _, oper := range asset["oper"].([]string) {
				operDepartment = append(operDepartment, &pb.OperDepartment{
					Oper: oper,

					Department: getStaffDepartmentByName("", oper, operDepartmentList),
				})
			}
		}

		// 业务系统人员部门
		business := make([]*assets.Business, 0)
		businessDepartmentList := make([]*assets.DepartmentBase, 0)
		if asset["business_department"] != nil {
			businessDepartmentList = asset["business_department"].([]*assets.DepartmentBase)
		}
		assetBusiness, ok := asset["business"].([]*assets.Business)
		if ok && len(assetBusiness) > 0 {
			for _, value := range assetBusiness {
				if value == nil {
					continue
				}
				if value.OwnerId != "" || value.Owner != "" {
					value.Department = getStaffDepartmentByName(value.OwnerId, value.Owner, businessDepartmentList)
				}

				//增加业务系统可信状态展示到内外网资产
				businessInfo, ok := value.BusinessInfo.(map[string]interface{})
				if ok && len(businessInfo["business_name"].(string)) > 0 { //存在业务系统
					businessInfo["reliability"] = 0 //默认为0

					boolQuery := elastic.NewBoolQuery()
					boolQuery.Must(elastic.NewTermQuery("business_name", businessInfo["business_name"].(string)))

					businessSystem, err := es.First[business_system.BusinessSystems](boolQuery, nil, "status")
					if err != nil {
						logger.Errorf("parseAssets Failed to es.First err:%+v", err)
					}

					if businessSystem != nil { //业务系统存在资产业务系统 取其可信状态
						businessInfo["reliability"] = businessSystem.Status
					}

					value.BusinessInfo = businessInfo //回填
				}

				business = append(business, value)
			}
		}
		asset["business"] = business
		ruleInfos, ok := asset["rule_infos"].([]*assets.RuleInfo)
		if ok && len(ruleInfos) > 0 {
			var noEmptyList = make([]*assets.RuleInfo, 0, len(ruleInfos))
			for _, ruleInfo := range ruleInfos {
				if ruleInfo != nil && ruleInfo.Product != "" {
					noEmptyList = append(noEmptyList, ruleInfo)
				}
			}
			asset["rule_infos"] = noEmptyList
		}
		list = append(list, asset)
	}
	return list, nil
}

// getStaffDepartmentByName  获取用户部门
func getStaffDepartmentByName(userId string, name string, departmentList []*assets.DepartmentBase) []string {
	departmentResult := make([]string, 0)
	if len(departmentList) > 0 {
		for _, sf := range departmentList {
			if sf == nil {
				continue
			}
			if sf.UserId == userId {
				departmentResult = append(departmentResult, sf.Name)
			} else if sf.UserName == name {
				departmentResult = append(departmentResult, sf.Name)
			}
		}
	}
	return departmentResult
}

// List
// @description: 列表接口
func List(c *gin.Context, keyword string, page, perPage, networkType int, params *asset_center.InternalAssetRequest, isSuperManage bool, staffIds []string, mustAttr []string, IsRecycleBin int) (any, int64, error) {
	query, err := CreateBoolQuery(keyword, networkType, IsRecycleBin, nil, params)
	if err != nil {
		return nil, 0, err
	}
	sourceType := data_source.SourceTypeInternetAsset
	if networkType == assets.NetworkTypeInternal {
		sourceType = data_source.SourceTypeIntranetAsset
	}
	permissionQuery, err := permission.NewPermissionService().ProcessElasticsearchQuery(c, sourceType)
	if err != nil {
		return nil, 0, err
	}
	if permissionQuery != nil {
		query = query.Must(permissionQuery)
	}
	m := map[string][]string{
		"business":   {"business", "business.system"},
		"department": {"business_department", "business_department.name.keyword"},
		"rule_infos": {"rule_infos", "rule_infos.second_tag"},
	}
	for _, attr := range mustAttr {
		if v, ok := m[attr]; ok {
			nestedBoolQuery := elastic.NewBoolQuery()
			nestedBoolQuery = nestedBoolQuery.Must(
				elastic.NewExistsQuery(v[1]),
			)
			// 将 BoolQuery 放入 NestedQuery 中
			nestedQuery := elastic.NewNestedQuery(
				v[0],
				nestedBoolQuery,
			)
			query = query.Must(nestedQuery)
		} else {
			query = query.Must(elastic.NewExistsQuery(attr))
		}
	}

	fieldTime := ""
	if len(params.Field) > 0 {
		fieldTime = params.Field
	}

	start := time.Now().UnixMilli()
	result, err := searchAssets(query, page, perPage, fieldTime, params.Order, params.SelectFields)
	defer func() {
		logger.Infof("List cost %d ms", time.Now().UnixMilli()-start)
	}()
	if err != nil {
		return nil, 0, err
	}
	list, err := ParseAssets(result.Hits.Hits)
	if err != nil {
		return nil, 0, err
	}
	HandleNetMapping(list)
	return list, result.TotalHits(), nil
}

func HandleNetMapping(list []any) {
	// 处理ip映射
	var ipArr = make([]string, 0, len(list))
	for _, v := range list {
		asset, ok := v.(map[string]interface{})
		if !ok {
			continue
		}
		ip, _ := asset["ip"].(string)
		ipArr = append(ipArr, ip)
	}
	areaIdNameMap, err := net_mapping.NewNetMappingAreaModel().GetIdNameMap()
	netMapping, err := net_mapping.NewNetMappingModel().ListByOpt(mysql.WithWhere("from_ip in (?) OR to_ip in (?)", ipArr, ipArr))
	if err == nil && len(netMapping) > 0 {
		var netMappingGroup = make(map[string][]map[string]interface{})
		for _, v := range netMapping {
			if _, ok := netMappingGroup[v.FromIp]; !ok {
				netMappingGroup[v.FromIp] = make([]map[string]interface{}, 0)
			}
			if _, ok := netMappingGroup[v.ToIp]; !ok {
				netMappingGroup[v.ToIp] = make([]map[string]interface{}, 0)
			}
			t := map[string]interface{}{
				"from_area": areaIdNameMap[v.FromArea],
				"from_ip":   v.FromIp,
				"from_port": v.FromPort,
				"to_area":   areaIdNameMap[v.ToArea],
				"to_ip":     v.ToIp,
				"to_port":   v.ToPort,
			}
			netMappingGroup[v.FromIp] = append(netMappingGroup[v.FromIp], t)
			netMappingGroup[v.ToIp] = append(netMappingGroup[v.ToIp], t)
		}

		for _, v := range list {
			asset, ok := v.(map[string]interface{})
			if !ok {
				continue
			}
			ip, _ := asset["ip"].(string)
			if val, ok := netMappingGroup[ip]; ok {
				asset["net_mappings"] = val
			}
		}
	}
}

// DeleteByIds
// @description: 按照id删除资产 -- 实际是修改资产状态将其变为删除状态（回收站模式）
// @param: isRecycleBin 字段为1时，导出所有不存在 deleted_at 字段的资产，isRecycleBin 为2时，导出存在 deleted_at 的资产

func DeleteByIds(c *gin.Context, ids []string, keyword string, isRecycleBin int, isType int, searchCondition []string) error {
	count, err := merge.NewMergeRecordsModel().Count(mysql.WithColumnValue("task_status", merge.MergeRecordsStatusRunning))
	if err != nil {
		return err
	}
	if count > 0 {
		return errors.New("存在融合任务, 请稍后删除")
	}
	params := &asset_center.InternalAssetRequest{
		AssetRequest: asset_center.AssetRequest{
			SearchCondition: searchCondition,
		},
	}
	boolQuery, err := CreateBoolQuery(keyword, 0, isRecycleBin, ids, params)
	if err != nil {
		return err
	}
	if isType == 1 {
		networkTypeQuery := elastic.NewTermQuery("network_type", 1)
		boolQuery = boolQuery.Filter(networkTypeQuery)
	}

	if isType == 2 {
		networkTypeQuery := elastic.NewTermQuery("network_type", 2)
		boolQuery = boolQuery.Filter(networkTypeQuery)
	}

	// 构建脚本更新 deleted_at 字段
	script := elastic.NewScriptInline("ctx._source.deleted_at = params.deleted_at").
		Param("deleted_at", localtime.NewLocalTime(time.Now()))

	// 执行 UpdateByQuery
	_, err = es.GetEsClient().UpdateByQuery(assets.NewAssets().IndexName()).
		Query(boolQuery).
		Script(script).
		Refresh("true").
		ScrollSize(200). // 设置滚动大小,防止数据量太大导致es报错
		Do(context.TODO())

	return err
}

// Show retrieves a specific asset by its ID.
func Show(c *gin.Context, id string) (any, error) {
	query := elastic.NewBoolQuery().Must(elastic.NewTermQuery("id", id))
	result, err := es.GetEsClient().Search(assets.NewAssets().IndexName()).Size(1).Query(query).Do(context.TODO())
	if err != nil {
		return nil, err
	}
	if len(result.Hits.Hits) == 0 {
		return nil, errors.New("资产数据不存在")
	}
	asset := utils.ParseSampleHash(result.Hits.Hits[0], assets.NewAssets())
	// 运维人员部门
	operDepartment := make([]*pb.OperDepartment, 0)
	operDepartmentList := make([]*assets.DepartmentBase, 0)
	if asset["oper_department"] != nil {
		operDepartmentList = asset["oper_department"].([]*assets.DepartmentBase)
	}
	if len(asset["oper"].([]string)) > 0 {
		for _, oper := range asset["oper"].([]string) {
			operDepartment = append(operDepartment, &pb.OperDepartment{
				Oper: oper,

				Department: getStaffDepartmentByName("", oper, operDepartmentList),
			})
		}
	}
	// asset["oper_department"] = operDepartment

	// 业务系统人员部门
	businessDepartmentList := make([]*assets.DepartmentBase, 0)
	if asset["business_department"] != nil {
		businessDepartmentList = asset["business_department"].([]*assets.DepartmentBase)
	}
	assetBusiness, ok := asset["business"].([]*assets.Business)
	if ok && len(assetBusiness) > 0 {
		for _, value := range assetBusiness {
			if value == nil {
				continue
			}
			if value.OwnerId != "" || value.Owner != "" {
				value.Department = getStaffDepartmentByName(value.OwnerId, value.Owner, businessDepartmentList)
			}

			//增加业务系统可信状态展示到内外网资产
			businessInfo, ok := value.BusinessInfo.(map[string]interface{})
			//存在业务系统
			if ok && len(businessInfo["business_name"].(string)) > 0 {
				businessInfo["reliability"] = 0 //默认为0

				boolQuery := elastic.NewBoolQuery()
				boolQuery.Must(elastic.NewTermQuery("business_name", businessInfo["business_name"].(string)))

				businessSystem, err := es.First[business_system.BusinessSystems](boolQuery, nil, "status")
				if err != nil {
					logger.Errorf("parseAssets Failed to es.First err:%+v", err)
				}

				if businessSystem != nil { //业务系统存在资产业务系统 取其可信状态
					businessInfo["reliability"] = businessSystem.Status
				}

				value.BusinessInfo = businessInfo //回填
			}
		}
	}

	return asset, nil
}

// RecycleBin
// @description: 回收站接口，指进行了软删除的资产
func RecycleBin(c *gin.Context, keyword string, page, perPage, networkType int, isSuperManage bool, staffIds []string) (any, int64, error) {
	query := elastic.NewBoolQuery()
	query.Must(elastic.NewExistsQuery("deleted_at"))
	query.MustNot(elastic.NewExistsQuery("purged_at"))
	if keyword != "" {
		query = assets.NewAssets().NewKeywordQuery(keyword, query)
	}
	if networkType != 0 {
		query = query.Must(elastic.NewTermQuery("network_type", networkType))
	}
	sourceType := data_source.SourceTypeInternetAsset
	if networkType == assets.NetworkTypeInternal {
		sourceType = data_source.SourceTypeIntranetAsset
	}
	permissionQuery, err := permission.NewPermissionService().ProcessElasticsearchQuery(c, sourceType)
	if err != nil {
		return nil, 0, err
	}
	if permissionQuery != nil {
		query = query.Must(permissionQuery)
	}
	result, err := searchAssets(query, page, perPage, "updated_at", "descend", []string{})
	if err != nil {
		return nil, 0, err
	}
	// 传递空字段列表，表示不进行字段过滤，返回所有字段
	list, err := ParseAssets(result.Hits.Hits)
	if err != nil {
		return nil, 0, err
	}
	HandleNetMapping(list)
	return list, result.TotalHits(), nil
}

// Reduction
// @description: 从回收站还原资产数据
func Reduction(c *gin.Context, ids []string, keyword string, networkType int) error {

	count, err := merge.NewMergeRecordsModel().Count(mysql.WithColumnValue("task_status", merge.MergeRecordsStatusRunning))
	if err != nil {
		return err
	}
	if count > 0 {
		return errors.New("存在融合任务, 请稍后删除")
	}
	boolQuery, err := CreateBoolQuery(keyword, networkType, IsRecycleBin, ids, nil)
	if err != nil {
		return err
	}
	sourceType := data_source.SourceTypeInternetAsset
	if networkType == assets.NetworkTypeInternal {
		sourceType = data_source.SourceTypeIntranetAsset
	}
	permissionQuery, err := permission.NewPermissionService().ProcessElasticsearchQuery(c, sourceType)
	if err != nil {
		return err
	}
	if permissionQuery != nil {
		boolQuery = boolQuery.Must(permissionQuery)
	}
	script := elastic.NewScriptInline("ctx._source.deleted_at = params.deleted_at").
		Param("deleted_at", nil)

	_, err = es.GetEsClient().UpdateByQuery(assets.NewAssets().IndexName()).
		Query(boolQuery).
		Script(script).
		Refresh("true").
		Do(context.TODO())
	return err
}

type AssetsIPStatsInfo struct {
	InternalIPCount int64 `json:"internal_ip_count"`
	ExternalIPCount int64 `json:"external_ip_count"`
}

type RecycleAssetsIPStatsInfo struct {
	RecycleInternalIPCount int64 `json:"recycle_internal_ip_count"`
	RecycleExternalIPCount int64 `json:"recycle_external_ip_count"`
}

func RecycleIpStats() (*RecycleAssetsIPStatsInfo, error) {
	result := &RecycleAssetsIPStatsInfo{}
	query := elastic.NewBoolQuery().
		MustNot(
			elastic.NewExistsQuery("purged_at"),
		).
		Must(
			elastic.NewExistsQuery("deleted_at"),
			elastic.NewTermsQuery("network_type", NetworkTypeInternal, NetworkTypeExternal),
		)

	agg := elastic.NewTermsAggregation().Field("network_type").Size(10)
	searchResult, err := es.GetEsClient().Search().
		Index(assets.NewAssets().IndexName()).
		Query(query).
		Aggregation("network_type_counts", agg).
		Do(context.TODO())
	if err != nil {
		return result, pkgerror.WithMessage(err, "es search failed")
	}

	aggResult, found := searchResult.Aggregations.Terms("network_type_counts")
	if found {
		for _, bucket := range aggResult.Buckets {
			networkType := bucket.Key.(float64) // 这里是浮点数表示的 network_type
			count := bucket.DocCount            // 该类型的 IP 数量
			if networkType == NetworkTypeInternal {
				result.RecycleInternalIPCount = count
			} else if networkType == NetworkTypeExternal {
				result.RecycleExternalIPCount = count
			}
		}
	} else {
		logger.Warn("No assets ip query results found.")
		return result, nil
	}
	return result, nil
}

func IPStats() (*AssetsIPStatsInfo, error) {
	result := &AssetsIPStatsInfo{}
	query := assets.NewAssets().GenQueryNoDeletedAndPurged().Must(
		elastic.NewTermsQuery("network_type", NetworkTypeInternal, NetworkTypeExternal),
	)

	agg := elastic.NewTermsAggregation().Field("network_type").Size(10)
	searchResult, err := es.GetEsClient().Search().
		Index(assets.NewAssets().IndexName()).
		Query(query).
		Aggregation("network_type_counts", agg).
		Do(context.TODO())
	if err != nil {
		return result, pkgerror.WithMessage(err, "es search failed")
	}

	aggResult, found := searchResult.Aggregations.Terms("network_type_counts")
	if found {
		for _, bucket := range aggResult.Buckets {
			networkType := bucket.Key.(float64) // 这里是浮点数表示的 network_type
			count := bucket.DocCount            // 该类型的 IP 数量
			if networkType == NetworkTypeInternal {
				result.InternalIPCount = count
			} else if networkType == NetworkTypeExternal {
				result.ExternalIPCount = count
			}
		}
	} else {
		logger.Warn("No assets ip query results found.")
		return result, nil
	}
	return result, nil
}

type ProbeContributionItem struct {
	USourceId   uint64 `json:"-"`
	SourceId    string `json:"source_id"`
	SubAllCount int64  `json:"sub_all_count"`
	ProbeType   string `json:"probe_type"`
	ShareCount  int64  `json:"share_count"`
	UniqueCount int64  `json:"unique_count"`
}

func ProbeAssetContributionTop() ([]*ProbeContributionItem, error) {
	var result []*ProbeContributionItem
	topResult, err := AssetTop("source_ids", 10000, "", "") // size(0) 获取所有
	if err != nil {
		return nil, err
	}
	var allIds []string
	var allUids []uint64
	for _, item := range topResult {
		allIds = append(allIds, item.FieldValue)
		uid, _ := strconv.ParseUint(item.FieldValue, 10, 64)
		if item.FieldValue == "0" {
			continue
		}
		result = append(result, &ProbeContributionItem{
			SourceId:    item.FieldValue,
			SubAllCount: item.FieldCount,
			USourceId:   uid,
		})

		allUids = append(allUids, uid)
	}
	sources := data_source.NewSourceModel().SourceNames(allUids)
	var sourceNameMap = make(map[uint64]string)
	for _, s := range sources {
		sourceNameMap[s.Id] = s.Name
	}
	for _, item := range result {
		var filterIds = make([]interface{}, 0, len(allIds))
		for _, id := range allIds {
			if id != item.SourceId {
				filterIds = append(filterIds, id)
			}
		}

		filterQuery := assets.NewAssets().GenQueryNoDeletedAndPurged().Must(
			elastic.NewExistsQuery("source_ids"),
			elastic.NewBoolQuery().MustNot(
				elastic.NewTermQuery("source_ids", ""),
			),
		)

		if len(filterIds) > 0 {
			filterQuery = filterQuery.MustNot(
				elastic.NewTermsQuery("source_ids", filterIds...),
			)
		}

		count, err := es.GetCount(assets.NewAssets().IndexName(), filterQuery)
		if err != nil {
			fmt.Printf("es filter count failed: %v\n", err)
			return nil, pkgerror.WithMessage(err, "es filter count failed")
		}
		item.ProbeType = sourceNameMap[item.USourceId]
		item.UniqueCount = count
		item.ShareCount = item.SubAllCount - count
		if item.ShareCount < 0 {
			item.ShareCount = 0
		}
	}

	return result, nil
}

func AssetUnfused(networkType int) (int64, error) {
	query := assets.NewAssets().GenQueryNoDeletedAndPurged().Must(
		elastic.NewTermQuery("network_type", networkType),
		elastic.NewTermQuery("is_device_extracted", 2),
	)
	return es.GetCount(assets.NewAssets().IndexName(), query)
}

func RecycleAssetUnfused(networkType int) (int64, error) {
	query := elastic.NewBoolQuery().
		MustNot(
			elastic.NewExistsQuery("purged_at"),
		).
		Must(
			elastic.NewTermQuery("network_type", networkType),
			elastic.NewTermQuery("is_device_extracted", 2),
			elastic.NewExistsQuery("deleted_at"),
		)
	return es.GetCount(assets.NewAssets().IndexName(), query)
}

type AssetTopResult struct {
	FieldValue               string `json:"field_value"`
	FieldCount               int64  `json:"field_count"`
	FieldPercentage          int64  `json:"field_percentage"`            // 占比百分比
	InternalCount            int64  `json:"internal_count"`              // network_type 1 count
	ExternalCount            int64  `json:"external_count"`              // network_type 2 count
	InternalExternalSumCount int64  `json:"internal_external_sum_count"` // Total count for the field (summation of network_type 1 and 2)
	Children                 []*AssetTopResult
}

// AggregationField 定义字段和类型
type AggregationField struct {
	Name       string // 字段名
	IsNested   bool   // 是否是 nested 类型
	NestedPath string // 如果是 nested 类型，需要指定路径
}

// AssetTopConfig 配置结构体，用于新的方法调用
type AssetTopConfig struct {
	Field           string
	Size            int
	NestedPath      string
	NestedSubField  string
	Keyword         string
	IsDualNested    bool
	AdditionQueries []elastic.Query //查询
	AggFilters      []elastic.Query //第一层聚合过滤条件
	IndexName       string
	Must            bool
	IncludeMissing  bool // 是否包含缺失字段的文档
}

// CalculatePercentages 计算资产TOP数据的占比
// results: TOP数据结果
// totalAssets: 总资产数量，如果为0则使用TOP数据的总和
func CalculatePercentages(results []*AssetTopResult, totalAssets int64) {
	if len(results) == 0 {
		return
	}

	// 如果没有提供总资产数量，则使用TOP数据的总和
	var total int64 = totalAssets
	if total == 0 {
		for _, result := range results {
			total += result.FieldCount
		}
	}

	// 计算每个项目的占比
	for _, result := range results {
		if total > 0 {
			result.FieldPercentage = utils.CalculatePercentageRate(result.FieldCount, total)
		}
		// 递归计算子项的占比
		if len(result.Children) > 0 {
			CalculatePercentages(result.Children, 0) // 子项使用子项总和计算占比
		}
	}
}

// AssetTopProcess 基于配置结构体
func AssetTopProcess(config AssetTopConfig) ([]*AssetTopResult, error) {
	// 构造布尔查询，排除 "deleted_at" 和 "purged_at"
	query := assets.NewAssets().GenQueryNoDeletedAndPurged()

	for _, q := range config.AdditionQueries {
		query = query.Must(q)
	}

	// 构造 Nested 查询条件
	if config.NestedPath != "" && config.Must {
		nestedBoolQuery := elastic.NewBoolQuery()
		if config.Keyword != "" {
			nestedBoolQuery = nestedBoolQuery.Must(
				elastic.NewWildcardQuery(config.Field, "*"+config.Keyword+"*"),
			)
		} else {
			nestedBoolQuery = nestedBoolQuery.Must(
				elastic.NewExistsQuery(config.Field),
			)
			nestedBoolQuery = nestedBoolQuery.MustNot(elastic.NewTermQuery(config.Field, ""))
		}

		nestedQuery := elastic.NewNestedQuery(config.NestedPath, nestedBoolQuery)
		query = query.Must(nestedQuery)
	} else if config.Must {
		query = query.Must(elastic.NewExistsQuery(config.Field))
	}

	// 组装聚合字段名
	aggField := strings.ReplaceAll(config.Field, ".", "_") + "_counts"

	// 构造聚合
	agg := buildAggregation(config, aggField)
	if config.IndexName == "" {
		config.IndexName = assets.NewAssets().IndexName()
	}
	// 创建搜索服务
	searchService := es.GetEsClient().Search().
		Index(config.IndexName).
		Size(0).
		Query(query).
		Aggregation(aggField, agg)
	// 如果需要包含缺失字段的文档
	if config.IncludeMissing {
		// 创建一个 filter 聚合，用于处理缺失字段的情况

		// 解析字段路径，获取嵌套路径
		pathParts := strings.Split(config.Field, ".")
		nestedPath := ""
		if len(pathParts) > 1 {
			nestedPath = pathParts[0]
		}

		// 创建查询，精确匹配缺失指定字段的文档
		var filterQuery elastic.Query

		// 处理嵌套字段的情况
		if nestedPath != "" {
			// 创建一个匹配所有有效文档的查询（即存在嵌套字段并且存在指定字段的文档）
			validDocsQuery := elastic.NewNestedQuery(nestedPath,
				elastic.NewExistsQuery(config.Field))

			// 创建一个匹配所有文档的查询
			allDocsQuery := elastic.NewMatchAllQuery()

			// 使用 bool.must_not 排除所有有效文档，即只保留缺失字段的文档
			filterQuery = elastic.NewBoolQuery().
				Must(allDocsQuery).
				MustNot(validDocsQuery)
		} else {
			// 非嵌套字段，直接查询缺失字段
			filterQuery = elastic.NewBoolQuery().
				MustNot(elastic.NewExistsQuery(config.Field))
		}

		// 创建 filter 聚合
		filterAgg := elastic.NewFilterAggregation().Filter(filterQuery)

		// 添加 ids 基数聚合
		filterAgg.SubAggregation("ids", elastic.NewCardinalityAggregation().Field("_id"))

		// 如果有子字段，添加子聚合
		if config.NestedSubField != "" && !config.IsDualNested {
			// 构造子聚合名称
			subAggName := strings.ReplaceAll(config.NestedSubField, ".", "_") + "_counts"

			// 添加子聚合
			filterAgg.SubAggregation(subAggName, elastic.NewTermsAggregation().
				Field(config.NestedSubField).
				Size(config.Size).
				MinDocCount(1).
				Order("_count", false))
		} else if config.NestedSubField != "" && config.IsDualNested {
			// 添加第二层nested聚合
			subAgg := elastic.NewNestedAggregation().Path(config.NestedSubField)
			subAgg.SubAggregation(aggField, elastic.NewTermsAggregation().
				Field(config.NestedSubField).
				Size(config.Size).
				MinDocCount(1).
				Order("_count", false))
			filterAgg.SubAggregation("ids", elastic.NewCardinalityAggregation().Field("_id"))
		}

		// 将 filter 聚合添加到搜索服务
		searchService.Aggregation("missing_"+aggField, filterAgg)
	}

	// 执行查询
	searchResult, err := searchService.Do(context.TODO())
	if err != nil {
		return nil, err
	}

	// 解析聚合结果
	return parseAggregationResult(searchResult, config, aggField)
}

// buildAggregation 构造聚合逻辑
// buildAggregation 构造聚合逻辑，支持动态聚合字段名称
// 支持以下几种聚合情况：
// 1. 双层 nested 聚合（当 config.IsDualNested=true 且 config.NestedPath 和 config.NestedSubField 都不为空）
// 2. nested 与普通聚合组合（当 config.NestedPath 不为空，config.NestedSubField 不为空，但 config.IsDualNested=false）
// 3. 双层普通聚合（当 config.NestedPath 为空，但 config.NestedSubField 不为空）
// 4. 单层聚合（当只有 config.Field 有值）
// 5. 包含缺失字段的聚合（当 config.IncludeMissing=true）
// 6. 聚合过滤（当 config.AggFilters 不为空时，只过滤聚合结果，不影响查询范围）
func buildAggregation(config AssetTopConfig, aggField string) elastic.Aggregation {
	// 确保 Size 不为零或负数，否则设置默认值
	if config.Size <= 0 {
		config.Size = 10
	}

	// 如果存在 NestedPath，说明第一层要进行 Nested 聚合
	if config.NestedPath != "" {
		// 创建第一级 Nested 聚合
		nestedAgg := elastic.NewNestedAggregation().Path(config.NestedPath)

		// 检查是否有聚合过滤条件
		if len(config.AggFilters) > 0 {
			// 创建过滤聚合
			filterBoolQuery := elastic.NewBoolQuery()
			for _, filter := range config.AggFilters {
				filterBoolQuery.Must(filter)
			}

			filterAgg := elastic.NewFilterAggregation().Filter(filterBoolQuery)

			// 创建 Terms 聚合
			termsAgg := elastic.NewTermsAggregation().
				Field(config.Field).
				Size(config.Size).
				SubAggregation("ids", elastic.NewCardinalityAggregation().Field("_id"))

			// 处理第二层聚合
			if config.NestedSubField != "" {
				if config.IsDualNested {
					// 第二层使用 Terms 聚合
					subAgg := elastic.NewTermsAggregation().
						Field(config.NestedSubField).
						Size(config.Size).
						SubAggregation("ids", elastic.NewCardinalityAggregation().Field("_id"))
					// 添加到第一层terms
					subAggName := config.NestedSubField + "_counts"
					termsAgg = termsAgg.SubAggregation(subAggName, subAgg)
				} else {
					// ===== 新方案：nested + terms + reverse_nested + 普通terms结构 =====
					// 构造子聚合名称
					subAggName := strings.ReplaceAll(config.NestedSubField, ".", "_") + "_counts"

					// 创建子聚合
					subTermsAgg := elastic.NewTermsAggregation().
						Field(config.NestedSubField).
						Size(config.Size).
						MinDocCount(1).
						Order("_count", false)

					// ids 基数聚合
					idsAgg := elastic.NewCardinalityAggregation().Field("_id")

					// reverse_nested 回到根，挂子聚合和 ids
					reverseNestedAgg := elastic.NewReverseNestedAggregation().
						SubAggregation(subAggName, subTermsAgg).
						SubAggregation("ids", idsAgg)

					// termsAgg 下挂 reverse_nested
					termsAgg = termsAgg.SubAggregation("to_root", reverseNestedAgg)
				}
			}

			// 将 Terms 聚合添加到过滤聚合
			filterAgg.SubAggregation("filtered_terms", termsAgg)

			// 将过滤聚合添加到嵌套聚合
			return nestedAgg.SubAggregation(aggField, filterAgg)
		}

		// 原有逻辑：创建 Terms 聚合
		termsAgg := elastic.NewTermsAggregation().
			Field(config.Field).
			Size(config.Size).
			SubAggregation("ids", elastic.NewCardinalityAggregation().Field("_id"))

		// 处理第二层聚合
		if config.NestedSubField != "" {
			var subAgg elastic.Aggregation

			// 如果是双层 Nested 聚合，保持原有逻辑
			if config.IsDualNested {
				// 第二层使用 Terms 聚合
				subAgg = elastic.NewTermsAggregation().
					Field(config.NestedSubField).
					Size(config.Size).
					SubAggregation("ids", elastic.NewCardinalityAggregation().Field("_id"))
				// 添加到第一层terms
				subAggName := config.NestedSubField + "_counts"
				termsAgg = termsAgg.SubAggregation(subAggName, subAgg)
				return nestedAgg.SubAggregation(aggField, termsAgg)
			}

			// ===== 新方案：nested + terms + reverse_nested + 普通terms结构 =====
			// 1. 先对嵌套字段分组
			// 2. 每个分组下 reverse_nested 回到根
			// 3. 根下再对普通字段分组
			// 4. 并统计ids
			// 构造子聚合名称
			subAggName := strings.ReplaceAll(config.NestedSubField, ".", "_") + "_counts"

			// 创建子聚合
			subTermsAgg := elastic.NewTermsAggregation().
				Field(config.NestedSubField).
				Size(config.Size).
				MinDocCount(1).
				Order("_count", false)

			// ids 基数聚合
			idsAgg := elastic.NewCardinalityAggregation().Field("_id")

			// reverse_nested 回到根，挂子聚合和 ids
			reverseNestedAgg := elastic.NewReverseNestedAggregation().
				SubAggregation(subAggName, subTermsAgg).
				SubAggregation("ids", idsAgg)

			// termsAgg 下挂 reverse_nested
			termsAgg = termsAgg.SubAggregation("to_root", reverseNestedAgg)

			// nestedAgg 下挂 termsAgg
			return nestedAgg.SubAggregation(aggField, termsAgg)

		}

		// 将第一层的 Terms 聚合与第二层的聚合合并
		return nestedAgg.SubAggregation(aggField, termsAgg)
	}

	// 如果没有 NestedPath，处理普通聚合
	if config.NestedSubField != "" && !config.IsDualNested {
		// 双层普通聚合
		mainAgg := elastic.NewTermsAggregation().
			Field(config.Field).
			Size(config.Size).
			Order("_count", false)

		// 添加第二层普通聚合
		subAgg := elastic.NewTermsAggregation().
			Field(config.NestedSubField).
			Size(config.Size).
			Order("_count", false)

		// 将第二层聚合添加到第一层，使用子字段名称作为聚合名称
		subAggName := strings.ReplaceAll(config.NestedSubField, ".", "_") + "_counts"
		mainAgg = mainAgg.SubAggregation(subAggName, subAgg)
		return mainAgg
	} else if config.NestedSubField != "" && config.IsDualNested {
		// 支持普通字段 + nested子聚合的结构
		// 第一层：普通terms聚合
		mainAgg := elastic.NewTermsAggregation().
			Field(config.Field).
			Size(config.Size).
			Order("_count", false)

		// 第二层：nested聚合
		// 从 NestedSubField 中提取 nested path (如 "business.system" -> "business")
		nestedPath := ""
		if strings.Contains(config.NestedSubField, ".") {
			parts := strings.Split(config.NestedSubField, ".")
			if len(parts) >= 2 {
				nestedPath = parts[0] // 取第一部分作为nested path
			}
		}

		if nestedPath != "" {
			// 创建nested聚合
			nestedAgg := elastic.NewNestedAggregation().Path(nestedPath)

			// 在nested聚合内创建terms聚合
			nestedTermsAgg := elastic.NewTermsAggregation().
				Field(config.NestedSubField).
				Size(config.Size).
				Order("_count", false)

			// 构造子聚合名称
			subAggName := strings.ReplaceAll(config.NestedSubField, ".", "_") + "_counts"
			nestedAgg = nestedAgg.SubAggregation(subAggName, nestedTermsAgg)

			// 将nested聚合添加到主聚合
			nestedAggName := strings.ReplaceAll(config.NestedSubField, ".", "_") + "_nested"
			mainAgg = mainAgg.SubAggregation(nestedAggName, nestedAgg)
		} else {
			// 如果无法解析nested path，退回到普通聚合
			subAgg := elastic.NewTermsAggregation().
				Field(config.NestedSubField).
				Size(config.Size).
				Order("_count", false)

			subAggName := strings.ReplaceAll(config.NestedSubField, ".", "_") + "_counts"
			mainAgg = mainAgg.SubAggregation(subAggName, subAgg)
		}

		return mainAgg
	}

	// 单层普通聚合
	termsAgg := elastic.NewTermsAggregation().
		Field(config.Field).
		Size(config.Size).
		Order("_count", false)

	return termsAgg
}

// parseAggregationResult 解析聚合结果，支持动态聚合字段名称
// parseSubAggregationFromAgg 解析子聚合的桶（例如 terms 聚合）
func parseSubAggregationFromAgg(subAgg json.RawMessage) ([]*AssetTopResult, error) {
	var subResults []*AssetTopResult
	var subAggMap map[string]interface{}

	// 解码 subAgg 为 map
	if err := json.Unmarshal(subAgg, &subAggMap); err != nil {
		return nil, fmt.Errorf("failed to unmarshal sub-aggregation: %v", err)
	}

	// 解析 "buckets" 字段
	if buckets, exists := subAggMap["buckets"]; exists {
		// 确认 buckets 是否为 []interface{}
		switch v := buckets.(type) {
		case []interface{}:
			// 如果 buckets 是 []interface{} 类型，逐个处理桶
			for _, item := range v {
				if bucketItem, ok := item.(map[string]interface{}); ok {
					ids, ok := bucketItem["ids"]
					if !ok || bucketItem["key"] == "" {
						continue
					}
					node := &AssetTopResult{
						FieldValue: fmt.Sprint(bucketItem["key"]), // 聚合键作为节点的值
					}
					if value, ok := ids.(map[string]interface{}); ok {
						// 现在可以安全地对 valueMap 进行索引
						if val, exists := value["value"]; exists {
							node.FieldCount = int64(val.(float64))
						} else {
							return nil, fmt.Errorf("'value' key not found in map")
						}
					} else {
						return nil, fmt.Errorf("value is not a map[string]interface{}")
					}

					subResults = append(subResults, node)
				}
			}
		default:
			return nil, fmt.Errorf("unexpected type for buckets: %T", v)
		}
	}

	return subResults, nil
}

// parseAggregationResult 处理聚合结果
// 支持处理以下聚合结果：
// 1. 双层 nested 聚合
// 2. nested 与普通聚合组合
// 3. 双层普通聚合
// 4. 单层普通聚合
// 5. 包含缺失字段的聚合
func parseAggregationResult(searchResult *elastic.SearchResult, config AssetTopConfig, aggField string) ([]*AssetTopResult, error) {
	var result []*AssetTopResult
	// 处理 IncludeMissing 的情况，先解析 filter 聚合的结果
	if config.IncludeMissing {
		missingAggName := "missing_" + aggField
		if filterAgg, found := searchResult.Aggregations.Filter(missingAggName); found && filterAgg != nil {
			// 创建缺失字段的结果节点
			missingNode := &AssetTopResult{
				FieldValue: "", // 空字符串表示缺失字段
				FieldCount: filterAgg.DocCount,
			}

			// 解析 ids 基数聚合
			if idsAgg, ok := filterAgg.Aggregations.Cardinality("ids"); ok && idsAgg != nil {
				missingNode.FieldCount = int64(*idsAgg.Value)
			}

			// 解析子聚合
			if config.NestedSubField != "" {
				// 构造子聚合名称
				subAggName := strings.ReplaceAll(config.NestedSubField, ".", "_") + "_counts"

				if subAgg, found := filterAgg.Aggregations.Terms(subAggName); found && subAgg != nil {
					var children []*AssetTopResult
					for _, bucket := range subAgg.Buckets {
						children = append(children, &AssetTopResult{
							FieldValue: fmt.Sprint(bucket.Key),
							FieldCount: bucket.DocCount,
						})
					}
					missingNode.Children = children
				}
			}

			// 添加到结果中
			result = append(result, missingNode)
		}
	}
	// 处理 Nested 聚合
	if config.NestedPath != "" {
		if config.AggFilters != nil {
			// 从 Aggregations 中提取原始数据
			raw, found := searchResult.Aggregations[aggField]
			if !found {
				return nil, nil
			}

			var temp map[string]interface{}
			if err := json.Unmarshal(raw, &temp); err != nil {
				return nil, fmt.Errorf("unmarshal failed: %w", err)
			}

			// Safely navigate the map structure to avoid panics
			nestedAgg, ok := temp[aggField].(map[string]interface{})
			if !ok {
				return nil, nil
			}

			filteredTerms, ok := nestedAgg["filtered_terms"].(map[string]interface{})
			if !ok {
				return nil, nil
			}

			buckets, ok := filteredTerms["buckets"].([]interface{})
			if !ok {
				return nil, nil
			}

			var topResults []*AssetTopResult
			for _, bucketItem := range buckets {
				bucket, ok := bucketItem.(map[string]interface{})
				if !ok {
					continue
				}

				key, keyOk := bucket["key"].(string)
				docCount, docCountOk := bucket["doc_count"].(float64)

				if !keyOk || !docCountOk {
					continue
				}

				topResults = append(topResults, &AssetTopResult{
					FieldValue: key,
					FieldCount: int64(docCount),
				})
			}
			return topResults, nil
		}
		// 获取 Nested 聚合结果
		nestedAggResult, found := searchResult.Aggregations.Nested(aggField)
		if !found || nestedAggResult == nil {
			return nil, nil
		}
		// 获取第一层 Terms 聚合结果
		termsAggResult, found := nestedAggResult.Aggregations.Terms(aggField)
		if !found || termsAggResult == nil {
			return result, nil // 返回已处理的 Missing 聚合结果（如果有）
		}

		// 处理一级聚合的结果
		for _, bucket := range termsAggResult.Buckets {
			v, ok := bucket.Aggregations.Cardinality("ids")
			if !ok || bucket.Key == "" {
				continue
			}
			node := &AssetTopResult{
				FieldValue: fmt.Sprint(bucket.Key), // 聚合键作为节点的值
				FieldCount: int64(*v.Value),
			}

			// 新增：适配 nested+terms+reverse_nested+普通terms 结构
			// 判断是否存在 reverse_nested 聚合（to_root）
			if toRootAgg, found := bucket.Aggregations.ReverseNested("to_root"); found && toRootAgg != nil {
				// 构造子聚合名称
				subAggName := strings.ReplaceAll(config.NestedSubField, ".", "_") + "_counts"

				// 解析子聚合
				if subAgg, found := toRootAgg.Aggregations.Terms(subAggName); found && subAgg != nil {
					var children []*AssetTopResult
					for _, bucket := range subAgg.Buckets {
						children = append(children, &AssetTopResult{
							FieldValue: fmt.Sprint(bucket.Key),
							FieldCount: bucket.DocCount,
						})
					}
					node.Children = children
				}
				// 解析 ids 基数聚合（如有需要，可覆盖 FieldCount）
				if idsAgg, ok := toRootAgg.Aggregations.Cardinality("ids"); ok && idsAgg != nil {
					node.FieldCount = int64(*idsAgg.Value)
				}
			} else {
				// 旧逻辑：兼容原有双层nested和普通nested
				// 处理子聚合
				// 定义可能的子聚合名称
				subAggName := ""
				if config.NestedSubField != "" {
					// 对于嵌套与普通聚合组合，使用子字段名称加_counts
					subAggName = config.NestedSubField + "_counts"
				} else {
					// 对于双层嵌套聚合，使用与主聚合相同的名称
					subAggName = aggField
				}

				// 检查子聚合结果
				if subAgg, found := bucket.Aggregations[subAggName]; found {
					subResults, err := parseSubAggregationFromAgg(subAgg)
					if err != nil {
						return nil, err
					}
					node.Children = subResults
				}
			}

			result = append(result, node)
		}
	} else {
		// 非 Nested 聚合

		// 如果配置了包含缺失字段，先尝试处理复合聚合
		if config.IncludeMissing {
			// 尝试获取复合聚合
			if compositeAgg, found := searchResult.Aggregations.Composite("composite"); found && compositeAgg != nil {
				// 先处理 Missing 聚合
				if missingAgg, found := compositeAgg.Aggregations.Missing("missing"); found && missingAgg != nil {
					// 创建缺失字段的结果节点
					missingNode := &AssetTopResult{
						FieldValue: "", // 空字符串表示缺失字段
						FieldCount: missingAgg.DocCount,
					}
					result = append(result, missingNode)
				}

				// 然后处理 Terms 聚合
				if termsAgg, found := compositeAgg.Aggregations.Terms("terms"); found && termsAgg != nil {
					// 处理每个桶
					for _, bucket := range termsAgg.Buckets {
						if bucket.Key == "" {
							continue
						}

						// 创建结果节点
						node := &AssetTopResult{
							FieldValue: fmt.Sprint(bucket.Key),
							FieldCount: bucket.DocCount,
						}
						result = append(result, node)
					}
				}

				// 已处理复合聚合，直接返回结果
				return result, nil
			}
		}

		// 如果没有复合聚合或不需要包含缺失字段，处理普通 Terms 聚合
		termsAggResult, found := searchResult.Aggregations.Terms(aggField)
		if !found || termsAggResult == nil {
			// 如果配置了包含缺失字段，尝试直接获取 Missing 聚合
			if config.IncludeMissing {
				if missingAgg, found := searchResult.Aggregations.Missing("missing_" + aggField); found && missingAgg != nil {
					// 创建缺失字段的结果节点
					missingNode := &AssetTopResult{
						FieldValue: "", // 空字符串表示缺失字段
						FieldCount: missingAgg.DocCount,
					}
					return []*AssetTopResult{missingNode}, nil
				}
			}
			return nil, nil
		}

		// 处理每个桶
		for _, bucket := range termsAggResult.Buckets {
			if bucket.Key == "" {
				continue
			}

			// 创建结果节点
			node := &AssetTopResult{
				FieldValue: fmt.Sprint(bucket.Key),
				FieldCount: bucket.DocCount,
			}

			// 处理子聚合
			if config.NestedSubField != "" && config.IsDualNested {
				// 处理普通字段 + nested子聚合的结构 (area + business.system)
				// 构造nested聚合名称: business_system_nested
				nestedAggName := strings.ReplaceAll(config.NestedSubField, ".", "_") + "_nested"

				// 检查是否有nested聚合
				if nestedAgg, found := bucket.Aggregations.Nested(nestedAggName); found && nestedAgg != nil {
					// 构造子聚合名称: business_system_counts
					subAggName := strings.ReplaceAll(config.NestedSubField, ".", "_") + "_counts"

					// 解析nested内部的terms聚合
					if subTermsAgg, found := nestedAgg.Aggregations.Terms(subAggName); found && subTermsAgg != nil {
						var subResults []*AssetTopResult
						for _, subBucket := range subTermsAgg.Buckets {
							if subBucket.Key == "" {
								continue
							}
							subResults = append(subResults, &AssetTopResult{
								FieldValue: fmt.Sprint(subBucket.Key),
								FieldCount: subBucket.DocCount,
							})
						}
						node.Children = subResults
					}
				}
			} else if config.NestedSubField != "" {
				// 处理双层普通聚合的子聚合
				// 定义子聚合名称
				subAggName := config.NestedSubField + "_counts"

				// 检查是否有子聚合
				if subAgg, found := bucket.Aggregations[subAggName]; found {
					// 尝试获取子聚合的 Terms 结果
					subTermsAgg, found := bucket.Aggregations.Terms(subAggName)
					if found && subTermsAgg != nil {
						// 处理子聚合的桶
						var subResults []*AssetTopResult
						for _, subBucket := range subTermsAgg.Buckets {
							if subBucket.Key == "" {
								continue
							}
							subResults = append(subResults, &AssetTopResult{
								FieldValue: fmt.Sprint(subBucket.Key),
								FieldCount: subBucket.DocCount,
							})
						}
						node.Children = subResults
					} else {
						// 如果不能直接解析，尝试使用通用的子聚合解析函数
						subResults, err := parseSubAggregationFromAgg(subAgg)
						if err != nil {
							return nil, err
						}
						node.Children = subResults
					}
				} else if subAgg, found := bucket.Aggregations[aggField]; found {
					// 兼容旧版的子聚合名称
					subResults, err := parseSubAggregationFromAgg(subAgg)
					if err != nil {
						return nil, err
					}
					node.Children = subResults
				}
			}

			result = append(result, node)
		}
	}

	return result, nil
}

// AssetTop 包装旧参数的兼容方法
func AssetTop(field string, size int, nestedPath, keyword string, additionQueries ...elastic.Query) ([]*AssetTopResult, error) {
	config := AssetTopConfig{
		Field:           field,
		Size:            size,
		NestedPath:      nestedPath,
		Keyword:         keyword,
		AdditionQueries: additionQueries,
		Must:            true,
	}
	return AssetTopProcess(config)
}

// SearchAssetsPocData
// queryType 查询类型
// field 查询字段
// match/wildcard 查询的值
// nestedPath nested类型查询的路径
func SearchAssetsPocData(field string, size int, nestedPath, keyword string, additionQueries ...elastic.Query) ([]*AssetTopResult, error) {
	list := make([]*AssetTopResult, 0)
	query := assets.NewAssets().GenQueryNoDeletedAndPurged()
	for _, q := range additionQueries {
		query = query.Must(q)
	}

	if nestedPath != "" {
		nestedBoolQuery := elastic.NewBoolQuery()
		// 如果 keyword 不为空，则添加模糊匹配条件
		if keyword != "" {
			nestedBoolQuery = nestedBoolQuery.Must(
				elastic.NewWildcardQuery(field, "*"+keyword+"*"),
			)
		} else {
			nestedBoolQuery = nestedBoolQuery.
				Must(elastic.NewExistsQuery(field)) // 确保字段有值
		}
		// 将 BoolQuery 放入 NestedQuery 中
		nestedQuery := elastic.NewNestedQuery(
			nestedPath,
			nestedBoolQuery,
		)
		query = query.Must(nestedQuery)
	}

	sourceContext := elastic.NewFetchSourceContext(true).Include("id", "ip")
	levelTotals := make(map[float64]int64)
	client := es.GetEsClient()
	// 执行查询
	searchService := client.Scroll().
		Index(assets.NewAssets().IndexName()). // 设置索引
		Query(query).                          // 使用构建的查询
		Size(1000).
		FetchSourceContext(sourceContext).
		Scroll("1m")

	// 使用 defer 确保释放滚动上下文
	defer searchService.Clear(context.Background())
	// 执行查询
	searchResult, err := searchService.Do(context.TODO())
	if err != nil || err == io.EOF {
		if err == io.EOF {
			err = nil
		}
		return list, err
	}
	// 保存滚动 ID
	//scrollId := searchResult.ScrollId

	for {
		// 检查是否有数据
		if searchResult.Hits == nil || searchResult.Hits.Hits == nil || len(searchResult.Hits.Hits) == 0 {
			break
		}

		if err != nil {
			return nil, err
		}
		ips := make([]interface{}, 0)
		for _, hit := range searchResult.Hits.Hits {
			asset := assets.Assets{}
			err = json.Unmarshal(hit.Source, &asset)
			if err != nil {
				logger.Errorf("Error unmarshalling asset json: %v", err)
				continue
			}
			ips = append(ips, asset.Ip)
		}
		level := elastic.NewTermsAggregation().Field("level")
		pocSearch, err := es.GetEsClient().Search().
			Index(poc.NewPoc().IndexName()).
			Query(elastic.NewBoolQuery().Must(elastic.NewTermsQuery("ip", ips...))).
			Aggregation("level_aggs", level).
			Do(context.TODO())
		if err != nil {
			logger.Errorf("Error searching poc: %v", err)
			continue
		}
		if agg, found := pocSearch.Aggregations.Terms("level_aggs"); found {
			for _, bucket := range agg.Buckets {
				if k, ok := bucket.Key.(float64); ok {
					levelTotals[k] += bucket.DocCount
				}
			}
		}

		// 如果没有更多的数据需要滚动查询，则退出循环
		if searchResult.ScrollId == "" {
			break
		}

		// 获取下一批数据
		searchResult, err = searchService.Scroll("1m").Do(context.TODO())
		if err != nil {
			logger.Errorf("Error scrolling poc: %v", err)
			break
		}
	}

	levelNames := map[float64]string{
		1: "低危",
		2: "中危",
		3: "高危",
		4: "严重",
		5: "未知",
		6: "未知",
		7: "未知",
	}
	// 遍历 levelTotals，构建结果列表
	for k, v := range levelTotals {
		if name, exists := levelNames[k]; exists {
			list = append(list, &AssetTopResult{FieldValue: name, FieldCount: v})
		}
	}
	return list, nil
}

// SearchAssetsPocCount
// queryType 查询类型
// field 查询字段
// match/wildcard 查询的值
// nestedPath nested类型查询的路径
func SearchAssetsPocCount(queryType, field, value, nestedPath string, additionQueries ...elastic.Query) (int64, error) {
	var esQuery elastic.Query
	// 创建布尔查询
	boolQuery := elastic.NewBoolQuery()
	//去除回收站数据
	boolQuery.Must(elastic.NewBoolQuery().MustNot(elastic.NewExistsQuery("deleted_at"), elastic.NewExistsQuery("purged_at")))
	// 排除 "create_at" 不为空的文档
	boolQuery.MustNot(
		elastic.NewExistsQuery("create_at"),
	)

	for _, q := range additionQueries {
		boolQuery = boolQuery.Must(q)
	}

	// 判断查询类型并创建相应的查询
	switch queryType {
	case "exists":
		// "exists" 查询，检查字段是否存在
		if nestedPath != "" {
			// 创建 Nested 查询，路径是 nestedPath
			nestedQuery := elastic.NewNestedQuery(nestedPath, elastic.NewExistsQuery(field))
			spaceQuery := elastic.NewNestedQuery(nestedPath, elastic.NewTermQuery(field, ""))
			boolQuery.Must(nestedQuery)
			boolQuery.MustNot(spaceQuery)
		} else {
			// 普通查询，字段是否存在
			existsQuery := elastic.NewExistsQuery(field)
			boolQuery.Must(existsQuery)
			boolQuery.MustNot(elastic.NewTermQuery(field, ""))
		}
	case "match":
		// "match" 查询，检查字段是否匹配指定值
		if nestedPath != "" {
			// 创建 Nested 查询，路径是 nestedPath
			nestedQuery := elastic.NewNestedQuery(nestedPath, elastic.NewTermQuery(field, value))
			boolQuery.Must(nestedQuery)
		} else {
			// 普通查询，字段值是否匹配指定值
			matchQuery := elastic.NewMatchQuery(field, value)
			boolQuery.Must(matchQuery)
		}
	case "wildcard":
		// "wildcard" 查询，字段是否匹配通配符
		if nestedPath != "" {
			// 创建 Nested 查询，路径是 nestedPath
			nestedQuery := elastic.NewNestedQuery(nestedPath, elastic.NewWildcardQuery(field, "*"+value+"*"))
			boolQuery.Must(nestedQuery)
		} else {
			// 普通查询，字段值是否匹配通配符
			wildcardQuery := elastic.NewWildcardQuery(field, "*"+value+"*")
			boolQuery.Must(wildcardQuery)
		}
	default:
		return 0, fmt.Errorf("unsupported query type: %s", queryType)
	}
	// 构造最终查询
	esQuery = boolQuery

	total, err := es.GetEsClient().Count().Index(poc.NewPoc().IndexName()).Query(esQuery).Do(context.TODO())
	if err != nil {
		return 0, err
	}
	return total, nil
}

// domain统计
// result,每个domain对应的doc数量
// sum 全部有域名的doc数量，不对ip去重
func DomainStat(networkType int64) (map[string]int64, int64, error) {
	if networkType != NetworkTypeInternal && networkType != NetworkTypeExternal {
		networkType = NetworkTypeInternal
	}
	query := elastic.NewBoolQuery().
		MustNot(elastic.NewExistsQuery("deleted_at")).
		MustNot(elastic.NewExistsQuery("purged_at")).
		Must(elastic.NewTermsQuery("network_type", networkType))
	aggregation := elastic.NewTermsAggregation().
		Field("ports.domain.keyword").
		Size(10000)

	searchResult, err := es.GetEsClient().Search().
		Index(assets.NewAssets().IndexName()).
		Query(query).
		Aggregation("unique_domains", aggregation).
		Size(0).
		Do(context.Background())
	if err != nil {
		return nil, 0, err
	}

	// 打印聚合结果
	var sum int64
	var result = make(map[string]int64)
	if uniqueDomains, found := searchResult.Aggregations.Terms("unique_domains"); found {
		for _, bucket := range uniqueDomains.Buckets {
			domain := fmt.Sprint(bucket.Key)
			if domain == "" {
				continue
			}
			sum += bucket.DocCount
			result[domain] = bucket.DocCount
		}
	} else {
		return nil, 0, errors.New("No unique domains found")
	}
	return result, sum, nil
}
