package asset

import (
	"encoding/json"
	testcommon "fobrain/fobrain/tests/common_test"
	"github.com/gin-gonic/gin"
	"github.com/olivere/elastic/v7"
	"github.com/stretchr/testify/assert"
	"net/http/httptest"
	"testing"
)

func TestQueryAssetIps(t *testing.T) {
	gin.SetMode(gin.TestMode)

	t.Run("success with assets", func(t *testing.T) {
		// 创建mock ES服务器
		mockServer := testcommon.NewMockServer()
		defer mockServer.Close()

		// 准备测试数据
		testAssets := []*elastic.SearchHit{
			{
				Id:     "1",
				Source: json.RawMessage(`{"ip": "***********"}`),
			},
			{
				Id:     "2", 
				Source: json.RawMessage(`{"ip": "***********"}`),
			},
			{
				Id:     "3",
				Source: json.RawMessage(`{"ip": ""}`), // 空IP应该被过滤
			},
		}

		// 注册ES响应
		mockServer.Register("/asset/_search.*", testAssets)

		// 创建测试上下文
		w := httptest.NewRecorder()
		ctx, _ := gin.CreateTestContext(w)
		ctx.Set("is_super_manage", true) // 设置为超级管理员，跳过权限检查

		// 执行测试
		ips, err := QueryAssetIps(ctx, nil)

		// 验证结果
		assert.NoError(t, err)
		assert.Len(t, ips, 2)
		assert.Contains(t, ips, "***********")
		assert.Contains(t, ips, "***********")
	})

	t.Run("empty result", func(t *testing.T) {
		mockServer := testcommon.NewMockServer()
		defer mockServer.Close()

		// 注册空响应
		mockServer.Register("/asset/_search.*", []*elastic.SearchHit{})

		w := httptest.NewRecorder()
		ctx, _ := gin.CreateTestContext(w)
		ctx.Set("is_super_manage", true)

		ips, err := QueryAssetIps(ctx, nil)

		assert.NoError(t, err)
		assert.Len(t, ips, 0)
	})

	t.Run("with custom query", func(t *testing.T) {
		mockServer := testcommon.NewMockServer()
		defer mockServer.Close()

		testAssets := []*elastic.SearchHit{
			{
				Id:     "1",
				Source: json.RawMessage(`{"ip": "********"}`),
			},
		}

		mockServer.Register("/asset/_search.*", testAssets)

		w := httptest.NewRecorder()
		ctx, _ := gin.CreateTestContext(w)
		ctx.Set("is_super_manage", true)

		// 使用自定义查询
		customQuery := elastic.NewBoolQuery().Must(elastic.NewTermQuery("area", "test"))
		ips, err := QueryAssetIps(ctx, customQuery)

		assert.NoError(t, err)
		assert.Len(t, ips, 1)
		assert.Equal(t, "********", ips[0])
	})
}

func TestResolveTargetIps(t *testing.T) {
	gin.SetMode(gin.TestMode)

	t.Run("user_input type", func(t *testing.T) {
		w := httptest.NewRecorder()
		ctx, _ := gin.CreateTestContext(w)

		inputIps := []string{"***********", "***********", "***********"} // 包含重复IP
		ips, err := ResolveTargetIps(ctx, "user_input", inputIps)

		assert.NoError(t, err)
		assert.Len(t, ips, 2) // 去重后应该只有2个
		assert.Contains(t, ips, "***********")
		assert.Contains(t, ips, "***********")
	})

	t.Run("account_asset type", func(t *testing.T) {
		w := httptest.NewRecorder()
		ctx, _ := gin.CreateTestContext(w)

		inputIps := []string{"********", "********"}
		ips, err := ResolveTargetIps(ctx, "account_asset", inputIps)

		assert.NoError(t, err)
		assert.Equal(t, inputIps, ips)
	})

	t.Run("account_asset_all type", func(t *testing.T) {
		mockServer := testcommon.NewMockServer()
		defer mockServer.Close()

		testAssets := []*elastic.SearchHit{
			{
				Id:     "1",
				Source: json.RawMessage(`{"ip": "**********"}`),
			},
		}

		mockServer.Register("/asset/_search.*", testAssets)

		w := httptest.NewRecorder()
		ctx, _ := gin.CreateTestContext(w)
		ctx.Set("is_super_manage", true)

		ips, err := ResolveTargetIps(ctx, "account_asset_all", nil)

		assert.NoError(t, err)
		assert.Len(t, ips, 1)
		assert.Equal(t, "**********", ips[0])
	})

	t.Run("business_system type", func(t *testing.T) {
		mockServer := testcommon.NewMockServer()
		defer mockServer.Close()

		testAssets := []*elastic.SearchHit{
			{
				Id:     "1",
				Source: json.RawMessage(`{"ip": "*************"}`),
			},
		}

		mockServer.Register("/asset/_search.*", testAssets)

		w := httptest.NewRecorder()
		ctx, _ := gin.CreateTestContext(w)
		ctx.Set("is_super_manage", true)

		businessSystemIds := []string{"sys1", "sys2"}
		ips, err := ResolveTargetIps(ctx, "business_system", businessSystemIds)

		assert.NoError(t, err)
		assert.Len(t, ips, 1)
		assert.Equal(t, "*************", ips[0])
	})

	t.Run("business_system_all type", func(t *testing.T) {
		mockServer := testcommon.NewMockServer()
		defer mockServer.Close()

		testAssets := []*elastic.SearchHit{
			{
				Id:     "1",
				Source: json.RawMessage(`{"ip": "*************"}`),
			},
		}

		mockServer.Register("/asset/_search.*", testAssets)

		w := httptest.NewRecorder()
		ctx, _ := gin.CreateTestContext(w)
		ctx.Set("is_super_manage", true)

		ips, err := ResolveTargetIps(ctx, "business_system_all", nil)

		assert.NoError(t, err)
		assert.Len(t, ips, 1)
		assert.Equal(t, "*************", ips[0])
	})

	t.Run("unsupported type", func(t *testing.T) {
		w := httptest.NewRecorder()
		ctx, _ := gin.CreateTestContext(w)

		ips, err := ResolveTargetIps(ctx, "unsupported_type", nil)

		assert.Error(t, err)
		assert.Nil(t, ips)
		assert.Contains(t, err.Error(), "不支持的扫描目标类型")
	})
}

func TestQueryBusinessSystemIps(t *testing.T) {
	gin.SetMode(gin.TestMode)

	t.Run("with specific business system ids", func(t *testing.T) {
		mockServer := testcommon.NewMockServer()
		defer mockServer.Close()

		testAssets := []*elastic.SearchHit{
			{
				Id:     "1",
				Source: json.RawMessage(`{"ip": "***********00"}`),
			},
		}

		mockServer.Register("/asset/_search.*", testAssets)

		w := httptest.NewRecorder()
		ctx, _ := gin.CreateTestContext(w)
		ctx.Set("is_super_manage", true)

		businessSystemIds := []string{"sys1", "sys2"}
		ips, err := QueryBusinessSystemIps(ctx, businessSystemIds)

		assert.NoError(t, err)
		assert.Len(t, ips, 1)
		assert.Equal(t, "***********00", ips[0])
	})

	t.Run("with empty business system ids (query all)", func(t *testing.T) {
		mockServer := testcommon.NewMockServer()
		defer mockServer.Close()

		testAssets := []*elastic.SearchHit{
			{
				Id:     "1",
				Source: json.RawMessage(`{"ip": "*************"}`),
			},
		}

		mockServer.Register("/asset/_search.*", testAssets)

		w := httptest.NewRecorder()
		ctx, _ := gin.CreateTestContext(w)
		ctx.Set("is_super_manage", true)

		ips, err := QueryBusinessSystemIps(ctx, nil)

		assert.NoError(t, err)
		assert.Len(t, ips, 1)
		assert.Equal(t, "*************", ips[0])
	})

	t.Run("with empty slice", func(t *testing.T) {
		mockServer := testcommon.NewMockServer()
		defer mockServer.Close()

		testAssets := []*elastic.SearchHit{
			{
				Id:     "1",
				Source: json.RawMessage(`{"ip": "*************"}`),
			},
		}

		mockServer.Register("/asset/_search.*", testAssets)

		w := httptest.NewRecorder()
		ctx, _ := gin.CreateTestContext(w)
		ctx.Set("is_super_manage", true)

		ips, err := QueryBusinessSystemIps(ctx, []string{})

		assert.NoError(t, err)
		assert.Len(t, ips, 1)
		assert.Equal(t, "*************", ips[0])
	})
}
