package asset

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http/httptest"
	"os"
	"reflect"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/gin-gonic/gin"

	"fobrain/fobrain/app/request/asset_center"
	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/initialize/es"
	"fobrain/models/elastic/assets"
	"fobrain/models/mysql/custom_column"
	"fobrain/models/mysql/network_areas"

	"github.com/olivere/elastic/v7"
	"github.com/stretchr/testify/assert"
)

// 初始化mock server
var mockServer *testcommon.MockServer

func TestMain(m *testing.M) {
	mockServer = testcommon.NewMockServer()
	defer mockServer.Close()
	m.Run()
}

func TestList(t *testing.T) {
	// 模拟 Elasticsearch 的搜索响应
	mockServer.Register("/asset/_search", []*elastic.SearchHit{
		{
			Id:     "1",
			Source: []byte(`{"id":"1","name":"example"}`),
		},
	})

	// Create a new gin context for testing
	gin.SetMode(gin.TestMode)
	c, _ := gin.CreateTestContext(httptest.NewRecorder())

	params := asset_center.InternalAssetRequest{}
	c.Set("user_id", uint64(1))
	c.Set("is_super_manage", true)
	list, total, err := List(c, "example", 1, 10, NetworkTypeInternal, &params,
		true, []string{"************"}, nil, 0)
	assert.Nil(t, err)
	assert.Equal(t, int64(1), total)
	fmt.Println(total)
	assert.Len(t, list, 1)
}

func TestHandleNetMapping(t *testing.T) {
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()

	// 准备测试数据：Asset 列表
	list := []any{
		map[string]interface{}{
			"ip": "***********",
		},
		map[string]interface{}{
			"ip": "***********",
		},
	}

	// mock area id -> name 映射查询
	mockDb.ExpectQuery("SELECT * FROM `net_mapping_areas`").
		WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).
			AddRow(1, "Area-A").
			AddRow(2, "Area-B"))

	// mock 映射列表查询
	mockDb.ExpectQuery("SELECT * FROM `net_mappings` WHERE from_ip in (?,?) OR to_ip in (?,?) ORDER BY created_at DESC").
		WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg()).
		WillReturnRows(sqlmock.NewRows([]string{
			"from_ip", "from_port", "from_area",
			"to_ip", "to_port", "to_area",
		}).AddRow("***********", 80, 1, "***********", 443, 2))

	// 替换实际的 list 参数
	HandleNetMapping(list)

	// 验证结果是否写入了 net_mappings 字段
	asset1 := list[0].(map[string]interface{})
	asset2 := list[1].(map[string]interface{})

	assert.Contains(t, asset1, "net_mappings")
	assert.Contains(t, asset2, "net_mappings")

	mappings1 := asset1["net_mappings"].([]map[string]interface{})
	mappings2 := asset2["net_mappings"].([]map[string]interface{})

	assert.Len(t, mappings1, 1)
	assert.Len(t, mappings2, 1)

	assert.Equal(t, "***********", mappings1[0]["from_ip"])
	assert.Equal(t, "***********", mappings1[0]["to_ip"])
	assert.Equal(t, "Area-A", mappings1[0]["from_area"])
	assert.Equal(t, "Area-B", mappings1[0]["to_area"])
}
func TestDeleteByIds(t *testing.T) {
	// 模拟 Elasticsearch 的 UpdateByQuery 响应
	mockServer.Register("/asset/_update_by_query", []*elastic.BulkIndexByScrollResponse{
		{
			Updated: 1,
		},
	})

	err := DeleteByIds(nil, []string{"1"}, "example", NotIsRecycleBin, 0, []string{})
	assert.NotNil(t, err)
}

func TestShow(t *testing.T) {
	// 模拟 Elasticsearch 的搜索响应
	mockServer.Register("/asset/_search", []*elastic.SearchHit{
		{
			Id:     "1",
			Source: []byte(`{"id":"1","name":"example"}`),
		},
	})

	result, err := Show(nil, "1")
	assert.Nil(t, err)
	assert.NotNil(t, result)
}

func TestRecycleBin(t *testing.T) {
	// 模拟 Elasticsearch 的搜索响应
	mockServer.Register("/asset/_search", []*elastic.SearchHit{
		{
			Id:     "1",
			Source: []byte(`{"id":"1","name":"example"}`),
		},
	})

	list, total, err := RecycleBin(nil, "example", 1, 10, NetworkTypeInternal, true, []string{})
	assert.Nil(t, err)
	assert.Equal(t, int64(1), total)
	assert.Len(t, list, 1)
}

func TestGenerateExcel(t *testing.T) {
	patch := gomonkey.NewPatches()
	patch.ApplyFuncReturn(network_areas.AllNetworkArea, map[uint64]string{
		1: "example_area",
	})
	patch.ApplyMethodReturn(&custom_column.CustomFieldMeta{}, "GetByModuleType", []*custom_column.CustomFieldMeta{
		{
			FieldKey:    "custom_key",
			DisplayName: "自定义字段",
		},
	}, nil)
	defer patch.Reset()
	// 模拟滚动查询的响应批次
	mockServer.Register("/asset/_search", []*elastic.SearchHit{
		{
			Id:     "1",
			Source: []byte(`{"id":"1","name":"example","area":1,"domains":["example.com"],"created_at":"2022-01-01 00:00:00","updated_at":"2022-01-02 00:00:00"}`),
		},
	})

	mockServer.RegisterEmptyScrollHandler()

	t.Run("内网资产导出", func(t *testing.T) {
		// 调用 GenerateExcel 函数
		params := &asset_center.InternalAssetRequest{}
		filePath, err := GenerateExcel(nil, []string{"1"}, "example", NetworkTypeInternal, NotIsRecycleBin, params, true, []string{})
		assert.Nil(t, err)
		assert.NotEmpty(t, filePath)

		// 确保测试完成后删除文件
		defer func() {
			if err := os.Remove(filePath); err != nil {
				t.Errorf("Failed to delete file: %s", err)
			}
		}()
	})

	t.Run("外网资产导出", func(t *testing.T) {
		// 调用 GenerateExcel 函数
		params := &asset_center.InternalAssetRequest{}
		filePath, err := GenerateExcel(nil, []string{"1"}, "example", NetworkTypeExternal, NotIsRecycleBin, params, true, []string{})
		assert.Nil(t, err)
		assert.NotEmpty(t, filePath)

		// 确保测试完成后删除文件
		defer func() {
			if err := os.Remove(filePath); err != nil {
				t.Errorf("Failed to delete file: %s", err)
			}
		}()
	})
}

func TestIPStats(t *testing.T) {
	t.Run("正常", func(t *testing.T) {
		mockServer.Register("/asset/_search", elastic.SearchResult{
			TookInMillis: 1,
			TimedOut:     false,
			Shards: &elastic.ShardsInfo{
				Total:      1,
				Successful: 1,
				Skipped:    0,
				Failed:     0,
			},
			Hits: &elastic.SearchHits{
				TotalHits: &elastic.TotalHits{
					Value:    2,
					Relation: "eq",
				},
			},
			Aggregations: elastic.Aggregations{
				"network_type_counts": json.RawMessage(`
            {
                "doc_count_error_upper_bound": 0,
                "sum_other_doc_count": 0,
                "buckets": [
                    {"key": 1, "doc_count": 941},
                    {"key": 2, "doc_count": 10310}
                ]
            }
        `),
			},
		})
		want := &AssetsIPStatsInfo{
			ExternalIPCount: 10310,
			InternalIPCount: 941,
		}
		got, err := IPStats()
		if err != nil {
			t.Errorf("stats failed,err:%v\n", err)
		}
		t.Log(got)
		if !assert.Equal(t, want, got) {
			t.Errorf("stats failed,want:%+v, got:%+v", want, got)
		}
	})

}

func TestAssetTop(t *testing.T) {
	t.Run("正常source_ids", func(t *testing.T) {
		// 直接模拟AssetTopProcess函数的返回值
		want := []*AssetTopResult{
			{FieldValue: "2", FieldCount: 392},
			{FieldValue: "1", FieldCount: 203},
			{FieldValue: "3", FieldCount: 148},
			{FieldValue: "5", FieldCount: 130},
			{FieldValue: "4", FieldCount: 100},
		}

		// 使用gomonkey直接模拟AssetTopProcess的返回值
		patch := gomonkey.ApplyFunc(AssetTopProcess, func(config AssetTopConfig) ([]*AssetTopResult, error) {
			// 验证传入的参数是否正确
			if config.Field != "source_ids" || config.Size != 5 {
				return nil, fmt.Errorf("unexpected parameters: field=%s, size=%d", config.Field, config.Size)
			}
			return want, nil
		})
		defer patch.Reset()

		// 调用被测试的函数
		got, err := AssetTop("source_ids", 5, "", "")
		if err != nil {
			t.Fatalf("stats failed,err:%v\n", err)
		}

		// 检查结果
		if len(got) == 0 {
			t.Fatalf("stats failed, got empty result")
		}

		if len(want) != len(got) {
			t.Fatalf("stats failed,want len:%v, got len:%v", len(want), len(got))
		}

		for i := range want {
			if !assert.Equal(t, want[i].FieldValue, got[i].FieldValue) || !assert.Equal(t, want[i].FieldCount, got[i].FieldCount) {
				t.Fatalf("stats failed,want %v, got %v", *want[i], *got[i])
			}
		}
	})

	t.Run("带AggFilters过滤的business.system", func(t *testing.T) {
		// 使用SearchHits数组模拟带过滤聚合的响应
		mockServer.Register("/asset/_search", []*elastic.SearchHit{
			{
				Id:     "1",
				Source: []byte(`{"id":"1"}`),
			},
		})

		// 手动构建带过滤聚合的结果
		patch := gomonkey.ApplyMethod(reflect.TypeOf(&elastic.SearchService{}), "Do",
			func(_ *elastic.SearchService, _ context.Context) (*elastic.SearchResult, error) {
				return &elastic.SearchResult{
					Hits: &elastic.SearchHits{
						TotalHits: &elastic.TotalHits{
							Value: 1,
						},
					},
					Aggregations: elastic.Aggregations{
						"business_system_counts": json.RawMessage(`
						{
							"doc_count": 867,
							"business_system_counts": {
								"doc_count": 500,
								"filtered_terms": {
									"doc_count_error_upper_bound": 0,
									"sum_other_doc_count": 0,
									"buckets": [
										{"key": "系统A", "doc_count": 200},
										{"key": "系统B", "doc_count": 150},
										{"key": "系统C", "doc_count": 100},
										{"key": "系统D", "doc_count": 50}
									]
								}
							}
						}`),
					},
				}, nil
			})
		defer patch.Reset()

		want := []*AssetTopResult{
			{FieldValue: "系统A", FieldCount: 200},
			{FieldValue: "系统B", FieldCount: 150},
			{FieldValue: "系统C", FieldCount: 100},
			{FieldValue: "系统D", FieldCount: 50},
		}

		// 使用AssetTopConfig并添加AggFilters
		config := AssetTopConfig{
			Field:      "business.system",
			Size:       5,
			NestedPath: "business",
			Must:       true,
			AggFilters: []elastic.Query{
				elastic.NewTermQuery("business.business_trusted_state", 2),
			},
		}
		got, err := AssetTopProcess(config)

		if err != nil {
			t.Fatalf("stats failed,err:%v\n", err)
		}

		// 检查结果
		if len(got) == 0 {
			t.Fatalf("stats failed, got empty result")
		}

		if len(want) != len(got) {
			t.Fatalf("stats failed,want len:%v, got len:%v", len(want), len(got))
		}

		for i := range want {
			if !assert.Equal(t, want[i].FieldValue, got[i].FieldValue) || !assert.Equal(t, want[i].FieldCount, got[i].FieldCount) {
				t.Fatalf("stats failed,want %v, got %v", *want[i], *got[i])
			}
		}
	})
}

func TestProbeAssetContributionTop(t *testing.T) {
	t.Run("正常", func(t *testing.T) {
		// 模拟 AssetTop 函数的返回值
		assetTopPatch := gomonkey.ApplyFunc(AssetTop, func(field string, size int, nestedPath, keyword string, additionQueries ...elastic.Query) ([]*AssetTopResult, error) {
			if field != "source_ids" {
				return nil, fmt.Errorf("unexpected field: %s", field)
			}
			return []*AssetTopResult{
				{FieldValue: "2", FieldCount: 392},
				{FieldValue: "1", FieldCount: 203},
				{FieldValue: "3", FieldCount: 148},
				{FieldValue: "5", FieldCount: 130},
				{FieldValue: "4", FieldCount: 100},
			}, nil
		})
		defer assetTopPatch.Reset()

		// 模拟数据库查询，返回数据源信息
		mockDb := testcommon.GetMysqlMock()
		defer mockDb.Close()

		mockDb.ExpectQuery("SELECT * FROM `data_sources` WHERE id in (?,?,?,?,?)").
			WithArgs(2, 1, 3, 5, 4).
			WillReturnRows(mockDb.NewRows([]string{"id", "name"}).
				AddRow(1, "FOEYE·网络资产测绘及风险分析系统").
				AddRow(2, "FORadar·互联网资产攻击面管理平台").
				AddRow(3, "万相·主机自适应安全平台").
				AddRow(4, "钉钉").
				AddRow(5, "蓝鲸CMDB"))

		// 模拟 es.GetCount 函数，返回固定的计数值
		esCountPatch := gomonkey.ApplyFunc(es.GetCount, func(index string, query elastic.Query) (int64, error) {
			return 60, nil
		})
		defer esCountPatch.Reset()

		// 执行测试
		got, err := ProbeAssetContributionTop()
		if err != nil {
			t.Fatalf("stats failed,err:%v\n", err)
		}

		// 预期结果
		want := []*ProbeContributionItem{
			{USourceId: 2, SourceId: "2", SubAllCount: 392, ProbeType: "FORadar·互联网资产攻击面管理平台", ShareCount: 332, UniqueCount: 60},
			{USourceId: 1, SourceId: "1", SubAllCount: 203, ProbeType: "FOEYE·网络资产测绘及风险分析系统", ShareCount: 143, UniqueCount: 60},
			{USourceId: 3, SourceId: "3", SubAllCount: 148, ProbeType: "万相·主机自适应安全平台", ShareCount: 88, UniqueCount: 60},
			{USourceId: 5, SourceId: "5", SubAllCount: 130, ProbeType: "蓝鲸CMDB", ShareCount: 70, UniqueCount: 60},
			{USourceId: 4, SourceId: "4", SubAllCount: 100, ProbeType: "钉钉", ShareCount: 40, UniqueCount: 60},
		}

		// 验证结果
		if len(want) != len(got) {
			t.Fatalf("stats failed,want len:%v, got len:%v", len(want), len(got))
		}

		for i := range want {
			if !assert.Equal(t, *want[i], *got[i]) {
				t.Fatalf("stats failed,want %v, got %v", *want[i], *got[i])
			}
		}
	})
}

func TestAssetUnfused(t *testing.T) {
	t.Run("正常", func(t *testing.T) {
		mockServer.Register("/asset/_count", elastic.CountResponse{
			Count:           60,
			TerminatedEarly: false,
			Shards: &elastic.ShardsInfo{
				Total:      5,
				Successful: 5,
				Skipped:    0,
				Failed:     0,
			},
		})

		got, err := AssetUnfused(1)
		if err != nil {
			t.Fatalf("stats failed,err:%v\n", err)
		}

		if got != 60 {
			t.Fatalf("stats failed,want:%v, got:%v", 60, got)
		}
	})

}

func TestSearchSort(t *testing.T) {
	t.Run("指定排序字段-正序", func(t *testing.T) {
		got := searchSort("fieldKey", "ascend")
		assert.NotNil(t, got)
		assert.Equal(t, 1, len(got))
		assert.Equal(t, true, got["fieldKey"])
	})

	t.Run("指定排序字段-倒序", func(t *testing.T) {
		got := searchSort("fieldKey", "descend")
		assert.Equal(t, 1, len(got))
		assert.Equal(t, false, got["fieldKey"])
	})

	t.Run("指定排序字段为空", func(t *testing.T) {
		got := searchSort("", "descend")
		assert.Equal(t, 3, len(got))
		assert.Equal(t, false, got["updated_at"])
		assert.Equal(t, false, got["created_at"])
	})

	t.Run("指定排序字段为updated_at", func(t *testing.T) {
		got := searchSort("updated_at", "descend")
		assert.Equal(t, 1, len(got))
		assert.Equal(t, false, got["updated_at"])
	})
}

func TestDeleteByIds_Real(t *testing.T) {
	// testcommon.SetTestEnv(false)
	// cfg.InitLoadCfg()
	// // es插入数据
	// bulkSercice := es.GetEsClient().Bulk().Refresh("true")
	// indexName := esmodel.NewAssets().IndexName()
	// id := uuid.New().String()
	// bulkSercice.Add(elastic.NewBulkCreateRequest().Index(indexName).Ids(id).Doc(map[string]interface{}{
	// 	"id":         id,
	// 	"ip":         "*******",
	// 	"deleted_at": nil,
	// }))
	// _, err := bulkSercice.Do(context.Background())
	// if err != nil {
	// 	t.Fatalf("DeleteByIds failed, err: %v\n", err)
	// }
	// err = DeleteByIds([]string{id}, "", 0, 0)
	// assert.Nil(t, err)
}

func TestGetStaffDepartmentByName(t *testing.T) {
	t.Run("正常", func(t *testing.T) {
		departmentList := []*assets.DepartmentBase{
			{UserId: "1", Name: "2"},
			{UserId: "2", Name: "3"},
		}
		got := getStaffDepartmentByName("1", "2", departmentList)
		assert.Equal(t, got, []string{"2"})
	})

	t.Run("部门为空", func(t *testing.T) {
		departmentList := []*assets.DepartmentBase{
			nil,
		}
		got := getStaffDepartmentByName("1", "2", departmentList)
		assert.Equal(t, got, []string{})
	})
}

func TestSearchAssetsPocData(t *testing.T) {
	mockServer.Register("/poc/_search", elastic.SearchResult{
		TookInMillis: 12,
		TimedOut:     false,
		Shards: &elastic.ShardsInfo{
			Total:      5,
			Successful: 5,
			Skipped:    0,
			Failed:     0,
		},
		Hits: &elastic.SearchHits{
			TotalHits: &elastic.TotalHits{
				Value:    867,
				Relation: "eq",
			},
		},
		Aggregations: elastic.Aggregations{
			"level_aggs": json.RawMessage(`
            {
                "doc_count_error_upper_bound": 0,
                "sum_other_doc_count": 0,
                "buckets": [
                    {"key": 2, "doc_count": 392},
                    {"key": 1, "doc_count": 203},
					{"key": 3, "doc_count": 148},
					{"key": 5, "doc_count": 130},
					{"key": 4, "doc_count": 100}
                ]
            }
        `),
		},
	})

	// 构造模拟的真实数据
	mockData := `{"fid":"************:1","sn_source":{"1-3":""},"load_average_source":{"1-3":""},"os_source":{"1-3":"Linux/4.4"},"oper_department":[],"cpu_maker_source":{"1-3":""},"disk_count":null,"model_source":{"1-3":""},"memory_usage_rate":null,"node_ids":[3],"cpu_maker":null,"hostname":null,"business_system_source":{"0":"刘苗的业务系统"},"disk_usage_rate":null,"memory_size_source":{"1-3":""},"all_source_ids":[1,0],"memory_usage_rate_source":{"1-3":""},"model":null,"id":"86792016adba458bbf1b6e228103627a","tag":null,"hostname_source":{"1-3":""},"area":1,"product_source":{"1-3":"Jetty, Oracle-JSP, Jenkins, Oracle-JAVA, Log4j2, NGINX/1.12.2, 7, redis/5.0.7, Linux-操作系统"},"ip":"************","ports_source":{"1-3":[{"protocol":"http","port":8081,"domain":"","title":"","url":"http://************:8081","status":0},{"protocol":"http","port":81,"domain":"","title":"404 Not Found","url":"http://************:81","status":0},{"protocol":"redis","port":6379,"domain":"","title":"","url":"","status":0},{"protocol":"ssh","port":22,"domain":"","title":"","url":"","status":0}]},"disk_usage_rate_source":{"1-3":""},"ip_type":1,"business_owner_source":{"0":""},"deleted_at":null,"status_source":{"1-3":1},"all_process_ids":["56_89_3_1_************"],"maker_source":{"1-3":""},"is_device_extracted":1,"fid_hash":"016488a0ae890770896069623088b9a9","oper_source":{"1-3":""},"ip_segment_source":{"1-3":""},"status":1,"asset_task_ids":["56_3_1_************"],"business_source":{"0":[{"owner":"刘苗","system":"刘苗的业务系统","business_info":{"fid":"a59b466c98254af0b7ea1f6952283516","business_name":"刘苗的业务系统","address":"","business":{"business_department_name":["IT二部"],"business_oper":"523dbb662ba24118bcb08702298bc161","business_department":null,"business_oper_name":"刘苗","business_oper_not_found":""},"person_limit":null,"intranet_ips":[""],"continuity_level":0,"business_app_principal_name":"刘苗","from_mark":0,"created_at":"2024-12-13 12:52:27","internet_ips":[""],"assets_attribute":{"purchase_type":null,"important_types":null,"is_gj":null,"operating_env":null,"running_state":null,"is_xc":null,"insurance_level":null},"ips":null,"deleted_at":null,"person_limit_hash":null,"updated_at":"2024-12-13 12:52:27","system_version":"","use_mark":"","tag_id":0,"from":1,"id":"a59b466c98254af0b7ea1f6952283516","department":{"name":"IT二部","id":"","parents":null},"status":1},"system_id":"a59b466c98254af0b7ea1f6952283516","owner_id":"523dbb662ba24118bcb08702298bc161","source":"","department":null,"business_from":0,"addition":"","business_trusted_state":0}],"1-3":[{"owner":"","system":"","business_info":null,"system_id":"","owner_id":"","source":"original","department":null,"business_from":0,"addition":"","business_trusted_state":0}]},"machine_room":null,"disk_count_source":{"1-3":0},"cpu_count_source":{"1-3":0},"created_at":"2024-12-14 10:02:34","mac_source":{"1-3":""},"ports":[{"protocol":"http","port":8081,"domain":"","title":"","url":"http://************:8081","status":0},{"protocol":"http","port":81,"domain":"","title":"404 Not Found","url":"http://************:81","status":0},{"protocol":"redis","port":6379,"domain":"","title":"","url":"","status":0},{"protocol":"ssh","port":22,"domain":"","title":"","url":"","status":0}],"merge_count":1,"person_limit_hash":null,"mac":null,"cpu_count":null,"source_ids":[1,0],"load_average":null,"updated_at":"2024-12-14 10:06:47","business_department":[],"all_asset_task_ids":["56_3_1_************"],"cpu_brand":null,"sn":null,"ip_segment":null,"kernel_source":{"1-3":""},"product":["Jetty","Oracle-JSP","Jenkins","Oracle-JAVA","Log4j2","NGINX/1.12.2","7","redis/5.0.7","Linux-操作系统"],"eth_name_source":{"1-3":""},"os":["Linux/4.4"],"business":[{"owner":"刘苗","system":"刘苗的业务系统","business_info":{"fid":"a59b466c98254af0b7ea1f6952283516","business_name":"刘苗的业务系统","address":"","business":{"business_department_name":["IT二部"],"business_oper":"523dbb662ba24118bcb08702298bc161","business_department":null,"business_oper_name":"刘苗","business_oper_not_found":""},"person_limit":null,"intranet_ips":[""],"continuity_level":0,"business_app_principal_name":"刘苗","from_mark":0,"created_at":"2024-12-13 12:52:27","internet_ips":[""],"assets_attribute":{"purchase_type":null,"important_types":null,"is_gj":null,"operating_env":null,"running_state":null,"is_xc":null,"insurance_level":null},"ips":null,"deleted_at":null,"person_limit_hash":null,"updated_at":"2024-12-13 12:52:27","system_version":"","use_mark":"","tag_id":0,"from":1,"id":"a59b466c98254af0b7ea1f6952283516","department":{"name":"IT二部","id":"","parents":null},"status":1},"system_id":"a59b466c98254af0b7ea1f6952283516","owner_id":"523dbb662ba24118bcb08702298bc161","source":"","department":null,"business_from":0,"addition":"","business_trusted_state":0}],"cpu_brand_source":{"1-3":""},"person_limit":null,"kernel":null,"maker":null,"disk_size":null,"machine_room_source":{"1-3":""},"disk_size_source":{"1-3":0},"memory_size":null,"all_node_ids":[3],"purged_at":null,"oper":null,"fusion_rules":null,"network_type":1,"eth_name":null,"process_ids":["56_89_3_1_************"]}`
	var mockResponse map[string]interface{}
	err := json.Unmarshal([]byte(mockData), &mockResponse)
	assert.NoError(t, err)

	// 模拟 BuildQuery 返回值
	mockScroll1 := &elastic.SearchResult{
		Hits: &elastic.SearchHits{
			TotalHits: &elastic.TotalHits{
				Value: 1,
			},
			Hits: []*elastic.SearchHit{
				{
					Source: json.RawMessage(mockData),
				},
			},
		},
	}
	// 第二轮返回空数据
	mockScroll2 := &elastic.SearchResult{
		Hits: &elastic.SearchHits{
			TotalHits: &elastic.TotalHits{
				Value: 0,
			},
			Hits: []*elastic.SearchHit{},
		},
	}
	// 模拟调用次数
	callCount := 0
	// 使用 gomonkey 模拟 `scroll.Do` 方法
	patches := gomonkey.ApplyMethod(reflect.TypeOf(&elastic.ScrollService{}), "Do", func(_ *elastic.ScrollService, ctx context.Context) (*elastic.SearchResult, error) {
		if callCount == 0 {
			callCount++
			return mockScroll1, nil // 第一次返回有效数据
		} else if callCount == 1 {
			callCount++
			return mockScroll2, io.EOF // 第二次返回 EOF，模拟循环结束
		}
		return nil, io.EOF
	})
	defer patches.Reset()
	list, err := SearchAssetsPocData("business", 5, "", "")
	assert.NoError(t, err)
	assert.NotNil(t, list)
}

func TestSearchAssetsPocCount(t *testing.T) {
	mockServer.Register("/poc/_count", elastic.CountResponse{
		Count:           867, // 这里直接使用Count字段
		TerminatedEarly: false,
		Shards: &elastic.ShardsInfo{
			Total:      5,
			Successful: 5,
			Skipped:    0,
			Failed:     0,
		},
	})
	// 构造模拟的真实数据
	mockData := `{"fid":"************:1","sn_source":{"1-3":""},"load_average_source":{"1-3":""},"os_source":{"1-3":"Linux/4.4"},"oper_department":[],"cpu_maker_source":{"1-3":""},"disk_count":null,"model_source":{"1-3":""},"memory_usage_rate":null,"node_ids":[3],"cpu_maker":null,"hostname":null,"business_system_source":{"0":"刘苗的业务系统"},"disk_usage_rate":null,"memory_size_source":{"1-3":""},"all_source_ids":[1,0],"memory_usage_rate_source":{"1-3":""},"model":null,"id":"86792016adba458bbf1b6e228103627a","tag":null,"hostname_source":{"1-3":""},"area":1,"product_source":{"1-3":"Jetty, Oracle-JSP, Jenkins, Oracle-JAVA, Log4j2, NGINX/1.12.2, 7, redis/5.0.7, Linux-操作系统"},"ip":"************","ports_source":{"1-3":[{"protocol":"http","port":8081,"domain":"","title":"","url":"http://************:8081","status":0},{"protocol":"http","port":81,"domain":"","title":"404 Not Found","url":"http://************:81","status":0},{"protocol":"redis","port":6379,"domain":"","title":"","url":"","status":0},{"protocol":"ssh","port":22,"domain":"","title":"","url":"","status":0}]},"disk_usage_rate_source":{"1-3":""},"ip_type":1,"business_owner_source":{"0":""},"deleted_at":null,"status_source":{"1-3":1},"all_process_ids":["56_89_3_1_************"],"maker_source":{"1-3":""},"is_device_extracted":1,"fid_hash":"016488a0ae890770896069623088b9a9","oper_source":{"1-3":""},"ip_segment_source":{"1-3":""},"status":1,"asset_task_ids":["56_3_1_************"],"business_source":{"0":[{"owner":"刘苗","system":"刘苗的业务系统","business_info":{"fid":"a59b466c98254af0b7ea1f6952283516","business_name":"刘苗的业务系统","address":"","business":{"business_department_name":["IT二部"],"business_oper":"523dbb662ba24118bcb08702298bc161","business_department":null,"business_oper_name":"刘苗","business_oper_not_found":""},"person_limit":null,"intranet_ips":[""],"continuity_level":0,"business_app_principal_name":"刘苗","from_mark":0,"created_at":"2024-12-13 12:52:27","internet_ips":[""],"assets_attribute":{"purchase_type":null,"important_types":null,"is_gj":null,"operating_env":null,"running_state":null,"is_xc":null,"insurance_level":null},"ips":null,"deleted_at":null,"person_limit_hash":null,"updated_at":"2024-12-13 12:52:27","system_version":"","use_mark":"","tag_id":0,"from":1,"id":"a59b466c98254af0b7ea1f6952283516","department":{"name":"IT二部","id":"","parents":null},"status":1},"system_id":"a59b466c98254af0b7ea1f6952283516","owner_id":"523dbb662ba24118bcb08702298bc161","source":"","department":null,"business_from":0,"addition":"","business_trusted_state":0}],"1-3":[{"owner":"","system":"","business_info":null,"system_id":"","owner_id":"","source":"original","department":null,"business_from":0,"addition":"","business_trusted_state":0}]},"machine_room":null,"disk_count_source":{"1-3":0},"cpu_count_source":{"1-3":0},"created_at":"2024-12-14 10:02:34","mac_source":{"1-3":""},"ports":[{"protocol":"http","port":8081,"domain":"","title":"","url":"http://************:8081","status":0},{"protocol":"http","port":81,"domain":"","title":"404 Not Found","url":"http://************:81","status":0},{"protocol":"redis","port":6379,"domain":"","title":"","url":"","status":0},{"protocol":"ssh","port":22,"domain":"","title":"","url":"","status":0}],"merge_count":1,"person_limit_hash":null,"mac":null,"cpu_count":null,"source_ids":[1,0],"load_average":null,"updated_at":"2024-12-14 10:06:47","business_department":[],"all_asset_task_ids":["56_3_1_************"],"cpu_brand":null,"sn":null,"ip_segment":null,"kernel_source":{"1-3":""},"product":["Jetty","Oracle-JSP","Jenkins","Oracle-JAVA","Log4j2","NGINX/1.12.2","7","redis/5.0.7","Linux-操作系统"],"eth_name_source":{"1-3":""},"os":["Linux/4.4"],"business":[{"owner":"刘苗","system":"刘苗的业务系统","business_info":{"fid":"a59b466c98254af0b7ea1f6952283516","business_name":"刘苗的业务系统","address":"","business":{"business_department_name":["IT二部"],"business_oper":"523dbb662ba24118bcb08702298bc161","business_department":null,"business_oper_name":"刘苗","business_oper_not_found":""},"person_limit":null,"intranet_ips":[""],"continuity_level":0,"business_app_principal_name":"刘苗","from_mark":0,"created_at":"2024-12-13 12:52:27","internet_ips":[""],"assets_attribute":{"purchase_type":null,"important_types":null,"is_gj":null,"operating_env":null,"running_state":null,"is_xc":null,"insurance_level":null},"ips":null,"deleted_at":null,"person_limit_hash":null,"updated_at":"2024-12-13 12:52:27","system_version":"","use_mark":"","tag_id":0,"from":1,"id":"a59b466c98254af0b7ea1f6952283516","department":{"name":"IT二部","id":"","parents":null},"status":1},"system_id":"a59b466c98254af0b7ea1f6952283516","owner_id":"523dbb662ba24118bcb08702298bc161","source":"","department":null,"business_from":0,"addition":"","business_trusted_state":0}],"cpu_brand_source":{"1-3":""},"person_limit":null,"kernel":null,"maker":null,"disk_size":null,"machine_room_source":{"1-3":""},"disk_size_source":{"1-3":0},"memory_size":null,"all_node_ids":[3],"purged_at":null,"oper":null,"fusion_rules":null,"network_type":1,"eth_name":null,"process_ids":["56_89_3_1_************"]}`
	var mockResponse map[string]interface{}
	err := json.Unmarshal([]byte(mockData), &mockResponse)
	assert.NoError(t, err)

	// 模拟 BuildQuery 返回值
	mockScroll1 := &elastic.SearchResult{
		Hits: &elastic.SearchHits{
			TotalHits: &elastic.TotalHits{
				Value: 1,
			},
			Hits: []*elastic.SearchHit{
				{
					Source: json.RawMessage(mockData),
				},
			},
		},
	}
	// 第二轮返回空数据
	mockScroll2 := &elastic.SearchResult{
		Hits: &elastic.SearchHits{
			TotalHits: &elastic.TotalHits{
				Value: 0,
			},
			Hits: []*elastic.SearchHit{},
		},
	}
	// 模拟调用次数
	callCount := 0
	// 使用 gomonkey 模拟 `scroll.Do` 方法
	patches := gomonkey.ApplyMethod(reflect.TypeOf(&elastic.ScrollService{}), "Do", func(_ *elastic.ScrollService, ctx context.Context) (*elastic.SearchResult, error) {
		if callCount == 0 {
			callCount++
			return mockScroll1, nil // 第一次返回有效数据
		} else if callCount == 1 {
			callCount++
			return mockScroll2, io.EOF // 第二次返回 EOF，模拟循环结束
		}
		return nil, io.EOF
	})
	defer patches.Reset()

	// 期望返回值为867，与模拟的TotalHits.Value一致
	count, err := SearchAssetsPocCount("exists", "business", "", "")
	assert.NoError(t, err)
	assert.Equal(t, int64(867), count)
}

func TestDomainStat(t *testing.T) {
	mockServer.Register("/asset/_search", elastic.SearchResult{
		TookInMillis: 2,
		TimedOut:     false,
		Shards: &elastic.ShardsInfo{
			Total:      5,
			Successful: 5,
			Skipped:    0,
			Failed:     0,
		},
		Hits: &elastic.SearchHits{
			TotalHits: &elastic.TotalHits{
				Value:    1,
				Relation: "eq",
			},
		},
		Aggregations: elastic.Aggregations{
			"unique_domains": json.RawMessage(`
            {
            "doc_count_error_upper_bound": 0,
            "sum_other_doc_count": 0,
            "buckets": [
                {
                    "key": "",
                    "doc_count": 1
                },
                {
                    "key": "118",
                    "doc_count": 1
                },
                {
                    "key": "AAAA",
                    "doc_count": 1
                },
                {
                    "key": "asfsda",
                    "doc_count": 1
                },
                {
                    "key": "dsags",
                    "doc_count": 1
                },
                {
                    "key": "tttt",
                    "doc_count": 1
                }
            ]
        }
        `),
		},
	})
	wantExt := map[string]int64{
		"118":    1,
		"AAAA":   1,
		"asfsda": 1,
		"dsags":  1,
		"tttt":   1,
	}
	gotExt, extNum, err := DomainStat(2)
	assert.NoError(t, err)
	assert.Equal(t, extNum, int64(5))
	assert.Equal(t, wantExt, gotExt)
}

// TestCalculatePercentages 测试计算占比功能
func TestCalculatePercentages(t *testing.T) {
	t.Run("空结果测试", func(t *testing.T) {
		var results []*AssetTopResult
		CalculatePercentages(results, 100)
		// 空结果应该不做任何操作，不会panic
		assert.Len(t, results, 0)
	})

	t.Run("使用提供的总资产数量计算占比", func(t *testing.T) {
		results := []*AssetTopResult{
			{FieldValue: "A", FieldCount: 30},
			{FieldValue: "B", FieldCount: 70},
		}
		totalAssets := int64(100)

		CalculatePercentages(results, totalAssets)

		// 验证占比计算正确 (30/100 = 30%, 70/100 = 70%)
		// utils.CalculatePercentageRate 返回万分比，所以30% = 3000, 70% = 7000
		assert.Equal(t, int64(3000), results[0].FieldPercentage) // 30% = 3000 万分比
		assert.Equal(t, int64(7000), results[1].FieldPercentage) // 70% = 7000 万分比
	})

	t.Run("使用TOP数据总和计算占比", func(t *testing.T) {
		results := []*AssetTopResult{
			{FieldValue: "A", FieldCount: 25},
			{FieldValue: "B", FieldCount: 75},
		}

		// 传入0表示使用TOP数据总和
		CalculatePercentages(results, 0)

		// 总和为100，占比应该是25%和75%
		assert.Equal(t, int64(2500), results[0].FieldPercentage) // 25% = 2500 万分比
		assert.Equal(t, int64(7500), results[1].FieldPercentage) // 75% = 7500 万分比
	})

	t.Run("有子项的递归计算", func(t *testing.T) {
		results := []*AssetTopResult{
			{
				FieldValue: "Parent1",
				FieldCount: 60,
				Children: []*AssetTopResult{
					{FieldValue: "Child1", FieldCount: 20},
					{FieldValue: "Child2", FieldCount: 40},
				},
			},
			{FieldValue: "Parent2", FieldCount: 40},
		}

		CalculatePercentages(results, 100)

		// 验证父项占比
		assert.Equal(t, int64(6000), results[0].FieldPercentage) // 60% = 6000 万分比
		assert.Equal(t, int64(4000), results[1].FieldPercentage) // 40% = 4000 万分比

		// 验证子项占比（基于子项总和60计算）
		// 子项总和为60，所以20/60≈33.33%, 40/60≈66.67%
		assert.InDelta(t, int64(3333), results[0].Children[0].FieldPercentage, 5) // 约33.33% = 3333 万分比
		assert.InDelta(t, int64(6667), results[0].Children[1].FieldPercentage, 5) // 约66.67% = 6667 万分比
	})

	t.Run("边界情况-总数为0", func(t *testing.T) {
		results := []*AssetTopResult{
			{FieldValue: "A", FieldCount: 0},
			{FieldValue: "B", FieldCount: 0},
		}

		// 当总数为0时，占比应该为0
		CalculatePercentages(results, 0)

		assert.Equal(t, int64(0), results[0].FieldPercentage)
		assert.Equal(t, int64(0), results[1].FieldPercentage)
	})

	t.Run("单个结果测试", func(t *testing.T) {
		results := []*AssetTopResult{
			{FieldValue: "Only", FieldCount: 50},
		}

		CalculatePercentages(results, 100)

		// 50/100 = 50%
		assert.Equal(t, int64(5000), results[0].FieldPercentage) // 50% = 5000 万分比
	})

	t.Run("多层嵌套子项测试", func(t *testing.T) {
		results := []*AssetTopResult{
			{
				FieldValue: "Level1",
				FieldCount: 100,
				Children: []*AssetTopResult{
					{
						FieldValue: "Level2-1",
						FieldCount: 30,
						Children: []*AssetTopResult{
							{FieldValue: "Level3-1", FieldCount: 10},
							{FieldValue: "Level3-2", FieldCount: 20},
						},
					},
					{FieldValue: "Level2-2", FieldCount: 70},
				},
			},
		}

		CalculatePercentages(results, 200)

		// 验证第一层：100/200 = 50%
		assert.Equal(t, int64(5000), results[0].FieldPercentage) // 50% = 5000 万分比

		// 验证第二层：基于第二层总和100计算
		// 30/100 = 30%, 70/100 = 70%
		assert.Equal(t, int64(3000), results[0].Children[0].FieldPercentage) // 30% = 3000 万分比
		assert.Equal(t, int64(7000), results[0].Children[1].FieldPercentage) // 70% = 7000 万分比

		// 验证第三层：基于第三层总和30计算
		// 10/30≈33.33%, 20/30≈66.67%
		assert.InDelta(t, int64(3333), results[0].Children[0].Children[0].FieldPercentage, 5) // 约33.33% = 3333 万分比
		assert.InDelta(t, int64(6667), results[0].Children[0].Children[1].FieldPercentage, 5) // 约66.67% = 6667 万分比
	})

	t.Run("验证CalculatePercentageRate返回值格式", func(t *testing.T) {
		results := []*AssetTopResult{
			{FieldValue: "Test", FieldCount: 50},
		}

		CalculatePercentages(results, 100)

		// 打印实际值来了解返回格式
		t.Logf("实际返回的FieldPercentage值: %d", results[0].FieldPercentage)

		// 50/100 = 50%，如果返回万分比则应该是5000
		// 如果返回百分比则应该是50
		// 根据实际值调整断言
		if results[0].FieldPercentage == 5000 {
			// 万分比格式 (50% = 5000)
			assert.Equal(t, int64(5000), results[0].FieldPercentage)
		} else if results[0].FieldPercentage == 50 {
			// 百分比格式 (50% = 50)
			assert.Equal(t, int64(50), results[0].FieldPercentage)
		} else {
			// 记录实际值以便调试
			t.Errorf("未预期的FieldPercentage值: %d", results[0].FieldPercentage)
		}
	})

	t.Run("精度测试-小数点后的计算", func(t *testing.T) {
		results := []*AssetTopResult{
			{FieldValue: "Precision", FieldCount: 1},
		}

		CalculatePercentages(results, 3)

		// 1/3 ≈ 33.33%，万分比约3333
		assert.InDelta(t, int64(3333), results[0].FieldPercentage, 5)
	})

	t.Run("混合子项-部分有子项部分没有", func(t *testing.T) {
		results := []*AssetTopResult{
			{
				FieldValue: "WithChildren",
				FieldCount: 50,
				Children: []*AssetTopResult{
					{FieldValue: "Child1", FieldCount: 30},
					{FieldValue: "Child2", FieldCount: 20},
				},
			},
			{
				FieldValue: "WithoutChildren",
				FieldCount: 50,
				// 没有子项
			},
		}

		CalculatePercentages(results, 100)

		// 验证父项
		assert.Equal(t, int64(5000), results[0].FieldPercentage) // 50% = 5000
		assert.Equal(t, int64(5000), results[1].FieldPercentage) // 50% = 5000

		// 验证子项
		assert.Equal(t, int64(6000), results[0].Children[0].FieldPercentage) // 30/50 = 60% = 6000
		assert.Equal(t, int64(4000), results[0].Children[1].FieldPercentage) // 20/50 = 40% = 4000

		// 验证没有子项的项目不受影响
		assert.Nil(t, results[1].Children)
	})
}
