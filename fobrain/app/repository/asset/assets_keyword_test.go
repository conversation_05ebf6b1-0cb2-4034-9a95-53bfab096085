package asset

import (
	"encoding/json"
	"testing"

	"github.com/olivere/elastic/v7"
	"github.com/stretchr/testify/assert"

	"fobrain/fobrain/app/request/asset_center"
	testcommon "fobrain/fobrain/tests/common_test"
)

func TestAssetsKeyword(t *testing.T) {
	t.Run("asset", func(t *testing.T) {
		mockServer = testcommon.NewMockServer()
		defer mockServer.Close()
		mockServer.Register("asset/_search", elastic.SearchResult{
			Aggregations: elastic.Aggregations{
				"ip_aggs": json.RawMessage(`{
			"doc_count_error_upper_bound": 0,
			"sum_other_doc_count": 0,
			"buckets": [{"key":"127.0.0.1","doc_count":2}]
		}`),
			},
		})
		params := asset_center.IpKeywordRequest{
			Keyword:     "127.0.0.1",
			KeyWordType: 1,
		}
		list, err := AssetsKeyword(&params)
		assert.NoError(t, err)
		assert.Equal(t, 1, len(list))
	})
	t.Run("poc", func(t *testing.T) {
		mockServer = testcommon.NewMockServer()
		defer mockServer.Close()
		mockServer.Register("poc/_search", elastic.SearchResult{
			Aggregations: elastic.Aggregations{
				"ip_aggs": json.RawMessage(`{
			"doc_count_error_upper_bound": 0,
			"sum_other_doc_count": 0,
			"buckets": [{"key":"127.0.0.1","doc_count":2}]
		}`),
			},
		})
		params := asset_center.IpKeywordRequest{
			Keyword:     "127.0.0.1",
			KeyWordType: 2,
		}
		list, err := AssetsKeyword(&params)
		assert.NoError(t, err)
		assert.Equal(t, 1, len(list))
	})

	t.Run("device", func(t *testing.T) {
		mockServer = testcommon.NewMockServer()
		defer mockServer.Close()
		mockServer.Register("device/_search", elastic.SearchResult{
			Aggregations: elastic.Aggregations{
				"ip_aggs": json.RawMessage(`{
			"doc_count_error_upper_bound": 0,
			"sum_other_doc_count": 0,
			"buckets": [{"key":"127.0.0.1","doc_count":2}]
		}`),
			},
		})
		params := asset_center.IpKeywordRequest{
			Keyword:     "127.0.0.1",
			KeyWordType: 3,
		}
		list, err := AssetsKeyword(&params)
		assert.NoError(t, err)
		assert.Equal(t, 1, len(list))
	})

	t.Run("compliance_monitor_task_records", func(t *testing.T) {
		mockServer = testcommon.NewMockServer()
		defer mockServer.Close()
		mockServer.Register("compliance_monitor_task_records/_search", elastic.SearchResult{
			Aggregations: elastic.Aggregations{
				"ip_aggs": json.RawMessage(`{
			"doc_count_error_upper_bound": 0,
			"sum_other_doc_count": 0,
			"buckets": [{"key":"127.0.0.1","doc_count":2}]
		}`),
			},
		})
		params := asset_center.IpKeywordRequest{
			Keyword:     "127.0.0.1",
			KeyWordType: 4,
		}
		list, err := AssetsKeyword(&params)
		assert.NoError(t, err)
		assert.Equal(t, 1, len(list))
	})
}

func TestBusinessSystemsKeyword(t *testing.T) {
	mockServer = testcommon.NewMockServer()
	defer mockServer.Close()
	mockServer.Register("asset/_search", elastic.SearchResult{
		Aggregations: elastic.Aggregations{
			"business_system": json.RawMessage(`{
			  "doc_count" : 331,
			  "filtered_system_terms" : {
				"doc_count" : 331,
				"system_terms" : {
				  "doc_count_error_upper_bound" : 0,
				  "sum_other_doc_count" : 0,
				  "buckets" : [ {
					"key" : "刘苗的业务系统",
					"doc_count" : 166
				  }, {
					"key" : "FOEYE业务系统",
					"doc_count" : 165
				  } ]
				}
			  }
			}`),
		},
	})
	keyword, err := businessSystemsKeyword(1, "业务系统")
	assert.NoError(t, err)
	assert.Equal(t, 2, len(keyword))
}
