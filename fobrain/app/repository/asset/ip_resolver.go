package asset

import (
	"fmt"
	"fobrain/fobrain/app/services/permission"
	"fobrain/initialize/es"
	"fobrain/models/elastic/assets"
	"fobrain/models/mysql/data_source"
	"fobrain/pkg/utils"

	"github.com/gin-gonic/gin"
	"github.com/olivere/elastic/v7"
)

// QueryAssetIps 查询资产IP，集成权限过滤，使用es.All方法
func QueryAssetIps(ctx *gin.Context, query *elastic.BoolQuery) ([]string, error) {
	if query == nil {
		query = assets.NewAssets().GenQueryNoDeletedAndPurged()
	}

	// 添加权限过滤
	sourceType := data_source.SourceTypeAsset
	permissionQuery, err := permission.NewPermissionService().ProcessElasticsearchQuery(ctx, sourceType)
	if err != nil {
		return nil, err
	}
	if permissionQuery != nil {
		query = query.Must(permissionQuery)
	}

	// 使用es.All方法，只查询IP字段
	results, err := es.All[assets.Assets](1000, query, nil, "ip")
	if err != nil {
		return nil, err
	}

	// 提取IP列表并去重
	var ips []string
	for _, asset := range results {
		if asset.Ip != "" && utils.IsIPv4(asset.Ip) {
			ips = append(ips, asset.Ip)
		}
	}

	return utils.Unique(ips), nil
}

// ResolveTargetIps 根据不同类型解析扫描目标IP
func ResolveTargetIps(ctx *gin.Context, scanIpRangeType string, scanIpRanges []string) ([]string, error) {
	switch scanIpRangeType {
	case "user_input":
		return utils.Unique(scanIpRanges), nil

	case "account_asset":
		return utils.Unique(scanIpRanges), nil

	case "account_asset_all":
		return QueryAllAssetIps(ctx)

	case "business_system":
		return QueryBusinessSystemIps(ctx, scanIpRanges)

	case "business_system_all":
		return QueryBusinessSystemIps(ctx, nil) // 传入nil表示查询所有业务系统

	default:
		return nil, fmt.Errorf("不支持的扫描目标类型: %s", scanIpRangeType)
	}
}

// QueryAllAssetIps 查询所有资产IP
func QueryAllAssetIps(ctx *gin.Context) ([]string, error) {
	query := assets.NewAssets().GenQueryNoDeletedAndPurged()
	return QueryAssetIps(ctx, query)
}

// QueryBusinessSystemIps 根据业务系统ID查询资产IP
// businessSystemIds: 业务系统ID列表，如果为nil或空，则查询所有有业务系统的资产
func QueryBusinessSystemIps(ctx *gin.Context, businessSystemIds []string) ([]string, error) {
	query := assets.NewAssets().GenQueryNoDeletedAndPurged()

	if len(businessSystemIds) == 0 {
		// 查询所有有业务系统ID的资产
		query.Must(
			elastic.NewNestedQuery("business",
				elastic.NewExistsQuery("business.system_id"),
			),
		)
	} else {
		// 拆分查询条件，避免超过ES terms查询的参数限制
		const batchSize = 1000 // 每批处理的业务系统ID数量
		businessQuery := elastic.NewBoolQuery()

		// 将业务系统ID分批添加到should条件中
		for i := 0; i < len(businessSystemIds); i += batchSize {
			end := i + batchSize
			if end > len(businessSystemIds) {
				end = len(businessSystemIds)
			}

			batchIds := businessSystemIds[i:end]
			businessQuery.Should(
				elastic.NewNestedQuery("business",
					elastic.NewTermsQueryFromStrings("business.system_id", batchIds...),
				),
			)
		}

		// 至少满足一个should条件
		businessQuery.MinimumShouldMatch("1")
		query.Must(businessQuery)
	}

	return QueryAssetIps(ctx, query)
}
