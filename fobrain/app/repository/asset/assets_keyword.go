package asset

import (
	"context"
	"encoding/json"
	"fmt"
	"fobrain/pkg/utils"

	"github.com/olivere/elastic/v7"
	"go-micro.dev/v4/logger"

	"fobrain/fobrain/app/request/asset_center"
	"fobrain/initialize/es"
	"fobrain/models/elastic/assets"
	"fobrain/models/elastic/business_system"
	"fobrain/models/elastic/compliance_monitor"
	"fobrain/models/elastic/device"
	"fobrain/models/elastic/poc"
)

func AssetsKeyword(params *asset_center.IpKeywordRequest) ([]string, error) {
	indexName := ""
	queryField := "ip.keyword"
	if params.Field != "" {
		queryField = params.Field
	}
	if queryField == "ports.port" {
		return []string{}, nil
	}
	if queryField == "port" && !utils.IsValidatePort(params.Keyword) {
		return []string{}, nil
	}
	switch params.KeyWordType {
	case 1:
		indexName = assets.NewAssets().IndexName()
	case 2:
		indexName = poc.NewPoc().IndexName()
	case 3:
		if params.Field == "" {
			queryField = "fid"
		} else if params.Field == "public_ip" || params.Field == "private_ip" {
			queryField = fmt.Sprintf("%s.keyword", params.Field)
		}
		indexName = device.NewDeviceModel().IndexName()
	case 4:
		indexName = compliance_monitor.NewComplianceMonitorTaskRecords().IndexName()
	case 5:
		indexName = business_system.NewBusinessSystems().IndexName()
	case 6:
		businessNames, err := businessSystemsKeyword(params.Recycle, params.Keyword)
		if err != nil {
			return []string{}, err
		}
		return businessNames, nil
	default:
		return []string{}, nil
	}
	ips, err := getIps(params.Keyword, queryField, indexName, params.Recycle)
	if err != nil {
		logger.Errorf("查询失败,params.Keyword:%v, queryField:%v, indexName:%v, params.Recycle:%v\n", params.Keyword, queryField, indexName, params.Recycle)
		return []string{}, nil
	}
	return ips, nil
}

// getIps 获取关键字对应的ip
func getIps(keyword, field, indexName string, recycle int) ([]string, error) {
	ips := make([]string, 0)
	boolQuery := elastic.NewBoolQuery()
	switch indexName {
	case poc.NewPoc().IndexName():
		if recycle == NotIsRecycleBin {
			boolQuery = boolQuery.MustNot(elastic.NewTermsQuery("status", []interface{}{40, 41}...))
		} else if recycle == IsRecycleBin {
			boolQuery = boolQuery.Must(elastic.NewTermsQuery("status", []interface{}{40, 41}...))
		}
	case assets.NewAssets().IndexName(), poc.NewPoc().IndexName():
		if recycle == NotIsRecycleBin {
			boolQuery = boolQuery.MustNot(elastic.NewExistsQuery("deleted_at"))
		} else if recycle == IsRecycleBin {
			boolQuery = boolQuery.Must(elastic.NewExistsQuery("deleted_at"))
		}
	case business_system.NewBusinessSystems().IndexName():
		if recycle > 0 {
			boolQuery = boolQuery.Must(elastic.NewTermQuery("status", recycle))
		}
	}

	boolQuery.Must(elastic.NewPrefixQuery(field, keyword))
	agg := elastic.NewTermsAggregation().Field(field).Size(10).ShardSize(20).
		Include(fmt.Sprintf("%s.*", keyword))
	searchResult, err := es.GetEsClient().Search().
		Index(indexName).
		Query(boolQuery).
		Aggregation("ip_aggs", agg).
		Do(context.TODO())
	if err != nil {
		return nil, err
	}
	aggResult, found := searchResult.Aggregations.Terms("ip_aggs")
	if found {
		for _, bucket := range aggResult.Buckets {
			ips = append(ips, bucket.Key.(string))
		}
	}
	return ips, nil
}

func businessSystemsKeyword(recycle int, keyword string) ([]string, error) {
	businessNames := make([]string, 0)
	if len([]rune(keyword)) <= 1 {
		return []string{}, nil
	}
	boolQuery := elastic.NewBoolQuery()
	if recycle == NotIsRecycleBin {
		boolQuery = boolQuery.MustNot(elastic.NewExistsQuery("deleted_at"))
	} else if recycle == IsRecycleBin {
		boolQuery = boolQuery.Must(elastic.NewExistsQuery("deleted_at"))
	}

	nestedBoolQuery := elastic.NewBoolQuery()
	// 使用模糊查询，支持类似通配符匹配
	nestedBoolQuery = nestedBoolQuery.Must(elastic.NewWildcardQuery("business.system", fmt.Sprintf("*%s*", keyword)).CaseInsensitive(true))
	query := elastic.NewNestedQuery("business", nestedBoolQuery)
	boolQuery = boolQuery.Must(query)
	nestedAgg := elastic.NewNestedAggregation().
		Path("business"). // 嵌套字段路径
		SubAggregation(
			"filtered_system_terms", // 使用 Filter 子聚合
			elastic.NewFilterAggregation().
				Filter(nestedBoolQuery). // 这里应用模糊查询条件
				SubAggregation(
					"system_terms", // 子聚合名称
					elastic.NewTermsAggregation().
						Field("business.system"). // 聚合字段
						Size(1000000).
						ShardSize(1000000),
				),
		)

	source, _ := boolQuery.Source()
	marshal, _ := json.Marshal(source)
	logger.Infof("businessSystemsKeyword: %v", string(marshal))

	searchResult, err := es.GetEsClient().Search().
		Index(assets.NewAssets().IndexName()).
		Query(boolQuery).
		Size(0).
		Aggregation("business_system", nestedAgg).
		Do(context.TODO())
	if err != nil {
		return nil, err
	}
	aggResult, found := searchResult.Aggregations.Terms("business_system")
	if found {
		// 获取子聚合结果
		termsAggResult, foundTerms := aggResult.Terms("filtered_system_terms")
		if foundTerms {
			systemTerms, f := termsAggResult.Terms("system_terms")
			if f {
				for _, bucket := range systemTerms.Buckets {
					businessNames = append(businessNames, bucket.Key.(string))
				}
			}
		}
	}
	return businessNames, nil
}
