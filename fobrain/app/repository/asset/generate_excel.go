package asset

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"strings"
	"time"

	"github.com/xuri/excelize/v2"

	"github.com/gin-gonic/gin"

	"fobrain/fobrain/app/request/asset_center"
	"fobrain/fobrain/app/services/permission"
	"fobrain/initialize/es"
	"fobrain/models/elastic/assets"
	"fobrain/models/mysql/custom_column"
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/network_areas"
	"fobrain/pkg/utils"
)

// GenerateExcel
// @description: isRecycleBin 字段为1时，导出所有不存在 deleted_at 字段的资产，isRecycleBin 为2时，导出存在 deleted_at 的资产
func GenerateExcel(c *gin.Context, ids []string, Keyword string, networkType, isRecycleBin int, params *asset_center.InternalAssetRequest, isSuperManage bool, staffIds []string) (string, error) {
	var header []string
	if networkType == NetworkTypeInternal {
		header = []string{
			"IP地址",
			"端口",
			"协议",
			"组件信息",
			"资产状态",
			"主机名",
			"IP类型",
			"MAC地址",
			"域名",
			"所属区域",
			"关联基础设施",
			"业务系统",
			"业务系统负责人",
			"运维人员",
			"首次上报时间",
			"最后上报时间",
		}
	} else if networkType == NetworkTypeExternal {
		header = []string{
			"IP地址",
			"端口",
			"协议",
			"URL",
			"网站标题",
			"域名",
			"组件信息",
			"资产状态",
			"主机名",
			"IP类型",
			"MAC地址",
			"所属区域",
			"关联基础设施",
			"业务系统",
			"业务系统负责人",
			"运维人员",
			"首次上报时间",
			"最后上报时间",
		}
	}
	// 补充自定义字段
	customFields, err := custom_column.NewCustomFieldMetaModel().GetByModuleType(custom_column.CustomFieldMetaModuleTypeAsset, false)
	if err != nil {
		return "", errors.New("获取自定义字段失败")
	}
	for _, field := range customFields {
		header = append(header, field.DisplayName)
	}

	networkArea := network_areas.AllNetworkArea()

	// 初始化滚动搜索请求
	client := es.GetEsClient()
	query, err := CreateBoolQuery(Keyword, networkType, isRecycleBin, ids, params)
	if err != nil {
		return "", err
	}
	sourceType := data_source.SourceTypeInternetAsset
	if networkType == assets.NetworkTypeInternal {
		sourceType = data_source.SourceTypeIntranetAsset
	}
	permissionQuery, err := permission.NewPermissionService().ProcessElasticsearchQuery(c, sourceType)
	if err != nil {
		return "", err
	}
	if permissionQuery != nil {
		query = query.Must(permissionQuery)
	}

	scroll := client.Scroll().Index(assets.NewAssets().IndexName()).Query(query).Sort("updated_at", false).Size(1000).Scroll("1m")
	defer scroll.Clear(context.Background())
	var filePath string
	var datum [][]interface{}
	maxResults := 10000 // 最大结果数
	totalFetched := 0   // 已拉取的记录数
	var sourceNames []string

	fmt.Println("开始整理 es 数据")
	time2 := time.Now()
	for {
		result, err := scroll.Do(context.TODO())
		if errors.Is(err, io.EOF) {
			err = nil
			break
		}

		if err != nil {
			return "", err
		}

		if len(result.Hits.Hits) == 0 {
			break
		}
		for _, hit := range result.Hits.Hits {
			asset := assets.Assets{}
			json.Unmarshal(hit.Source, &asset)

			// 获取数据源名称
			source := data_source.NewSourceModel().SourceNames(asset.AllSourceIds)
			var names []string
			for _, v := range source {
				names = append(names, v.Name)
			}
			sourceName := strings.Join(names, ",")
			sourceNames = append(sourceNames, sourceName)

			data := make([]interface{}, 0)
			if networkType == NetworkTypeInternal {
				data = []interface{}{
					asset.Ip,
					asset.PortInfos(),
					asset.GetProducts(),
					asset.StatusDesc(),
					func() string {
						if len(asset.HostName) > 0 {
							return strings.Join(asset.HostName, ",")
						}
						return ""
					}(),
					asset.IpTypeDesc(),
					strings.Join(asset.Mac, ","),
					strings.Join(asset.Domains(), ","),
					networkArea[uint64(asset.Area)],
					"",
					asset.BusinessSystems(),
					asset.BusinessOwners(),
					asset.OperString(),
					asset.CreatedAt,
					asset.UpdatedAt,
				}
			} else {
				ports := asset.PortInfos()
				for i := range ports {
					if ports[i] != nil {
						// 去掉末尾状态码数据
						ports[i] = ports[i][:len(ports[i])-1]
					}
				}
				data = []interface{}{
					asset.Ip,
					ports,
					asset.GetProducts(),
					asset.StatusDesc(),
					func() string {
						if len(asset.HostName) > 0 {
							return strings.Join(asset.HostName, ",")
						}
						return ""
					}(),
					asset.IpTypeDesc(),
					strings.Join(asset.Mac, ","),
					networkArea[uint64(asset.Area)],
					"",
					asset.BusinessSystems(),
					asset.BusinessOwners(),
					asset.OperString(),
					asset.CreatedAt,
					asset.UpdatedAt,
				}
			}
			// 添加自定义字段值
			for _, customField := range customFields {
				if _, ok := asset.CustomFields[customField.FieldKey]; ok {
					data = append(data, asset.CustomFields[customField.FieldKey])
				} else {
					data = append(data, "")
				}
			}
			datum = append(datum, data)

			totalFetched++
			if totalFetched >= maxResults {
				break
			}
		}

		if totalFetched >= maxResults {
			break
		}

		//scroll = client.Scroll().ScrollId(result.ScrollId).Scroll("1m")
	}
	fmt.Println("整理 es 数据耗时：", time.Since(time2).Seconds(), "秒")

	if len(datum) == 0 {
		return "", errors.New("导出的数据不存在")
	}

	// 生成文件路径
	filePath = time.Now().Format("20060102150405") + "-资产列表.xlsx"
	time1 := time.Now()
	fmt.Println("开始生成文件")
	if _, err := utils.WriterExcel(filePath, header, datum); err != nil {
		fmt.Println("开始生成文件失败", err.Error())
		return "", err
	}
	err = insertDataSource(filePath, datum, sourceNames, "Sheet1")
	if err != nil {
		return "", err
	}
	fmt.Println("生成文件耗时：", time.Since(time1).Seconds(), "秒")
	return filePath, nil
}

func insertDataSource(filePath string, datum [][]interface{}, sourceNames []string, sheet string) error {
	ef, err := excelize.OpenFile(filePath)
	if err != nil {
		fmt.Println("打开生成文件失败", err.Error())
		return err
	}
	err = ef.InsertCols(sheet, "A", 1)
	if err != nil {
		fmt.Println("插入数据源列失败", err.Error())
		return err
	}
	err = ef.SetColWidth(sheet, "A", "A", 30)
	if err != nil {
		return err
	}
	style, err := ef.GetCellStyle(sheet, "B1")
	if err != nil {
		return err
	}
	err = ef.SetCellStyle(sheet, "A1", "A1", style)
	if err != nil {
		return err
	}
	err = ef.SetCellValue(sheet, "A1", "数据源")
	if err != nil {
		return err
	}

	var merges []struct{ StartCell, EndCell string }
	rowNumbers := 2
	startRow, endRow := 2, 2
	for i, v := range sourceNames {
		err = ef.SetCellValue(sheet, fmt.Sprintf("A%d", rowNumbers), v)
		if err != nil {
			return err
		}
		startRow = rowNumbers
		pp, _ := datum[i][1].([][]string)
		rowNumbers += len(pp)
		endRow = rowNumbers - 1

		if startRow != endRow {
			merges = append(merges, struct{ StartCell, EndCell string }{
				StartCell: fmt.Sprintf("A%d", startRow),
				EndCell:   fmt.Sprintf("A%d", endRow),
			})
		}
	}
	for _, merge := range merges {
		if err := ef.MergeCell(sheet, merge.StartCell, merge.EndCell); err != nil {
			return err
		}
	}
	if err := ef.Save(); err != nil {
		fmt.Println("保存生成文件失败", err.Error())
		return err
	}
	return nil
}
