package license

import (
	"fobrain/pkg/cfg"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestCheckDevModel(t *testing.T) {
	cfg.InitLoadCfg()
	tests := []struct {
		name       string
		base64Code string
	}{
		//{
		//	name:       "success",
		//	base64Code: "br2RW77fnBKmNfbH+MuLno5c8fY2GpljHhzz1XhCqHsCNF1XnTykySjtJO92hAPCqeBkW4iLoWodgb5W21j5Kw==", //{"model":"dev","expire":"2024-11-11"}
		//},
		{
			name:       "err",
			base64Code: "PpyuWkXlOtleJeC3Oq3nnkBrvIZtpRP/5Uz57aFAF6TQTnF2zFZt7kEjBZYYPZ3PuotzTX6da77+wbxxtpzHFg==", //{"model":"dev","expire":"2024-9-11"}
		},
		{
			name:       "model_err",
			base64Code: "AuHYfSHfocUbM/wJtBZBGzgd0+3Gp8SopHctcAJuziqUBmDhmPxF6LAP0DH4M8EPv7h9j5QKW+uEco2+6p7vCA==", //{"model":"devv","expire":"2024-11-11"}
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			isDevModel := CheckDevModel(tt.base64Code)
			if tt.name == "success" {
				assert.True(t, isDevModel)
			}
			if tt.name == "err" || tt.name == "model_err" {
				assert.False(t, isDevModel)
			}
		})
	}
}
