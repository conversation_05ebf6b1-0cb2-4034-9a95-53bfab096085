package license

import (
	"context"
	"encoding/json"
	"fobrain/initialize/es"
	redis2 "fobrain/initialize/redis"
	esmodel_asset "fobrain/models/elastic/assets"
	redis_helper "fobrain/models/redis"
	"fobrain/pkg/cfg"
	"fobrain/pkg/utils"
)

const cryptoKey = "fobrain@2024!!Bt"

// UpdateAssetWriteState 更新license资产上限状态
func UpdateAssetWriteState() error {
	redisClient := redis2.GetRedisClient()
	isLicenseAssetLimitReached, err := es.IsLicenseAssetLimitReached(esmodel_asset.NewAssets().IndexName(), CheckDevModel(cfg.LoadCommon().Env))
	if err != nil {
		return err
	}
	isLicenseProcessAssetLimitReached, err := es.IsLicenseProcessAssetLimitReached(esmodel_asset.NewProcessAssetsModel().IndexName(), CheckDevModel(cfg.<PERSON>ad<PERSON>ommon().Env))
	if err != nil {
		return err
	}
	key := redis_helper.GetIsLicenseAssetLimitReachedKey()
	if isLicenseAssetLimitReached {
		redisClient.Set(context.Background(), key, "true", 0)
	} else {
		redisClient.Set(context.Background(), key, "false", 0)
	}

	key = redis_helper.GetIsLicenseProcessAssetLimitReachedKey()
	if isLicenseProcessAssetLimitReached {
		redisClient.Set(context.Background(), key, "true", 0)
	} else {
		redisClient.Set(context.Background(), key, "false", 0)
	}
	return nil
}

type DevInfo struct {
	Model  string `json:"model"`
	Expire string `json:"expire"`
}

// CheckDevModel 检测是否dev模式
func CheckDevModel(base64Code string) bool {
	code, err := utils.Base64Decode(base64Code)
	if err != nil {
		return false
	}
	decryptCode, err := utils.AesDecrypt([]byte(code), cryptoKey)
	if err != nil {
		return false
	}
	var devInfo DevInfo
	err = json.Unmarshal(decryptCode, &devInfo)
	if err != nil {
		return false
	}
	if devInfo.Model == "dev" && utils.IsFutureTime(devInfo.Expire) {
		return true
	}
	return false
}
