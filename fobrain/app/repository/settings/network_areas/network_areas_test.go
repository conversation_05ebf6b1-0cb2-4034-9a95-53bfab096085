package network_areas

import (
	"errors"
	"fmt"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/olivere/elastic/v7"
	"github.com/stretchr/testify/assert"

	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/initialize/mysql"
	"fobrain/models/mysql/geo_areas"
	"fobrain/models/mysql/isp_areas"
)

func TestCheckNetworkAreasIsUsed(t *testing.T) {
	// 创建模拟服务器实例
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()
	// 创建模拟数据库对象
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()

	// es请求
	mockServer.Register("/asset,poc/_count", []*elastic.SearchHit{})

	// 模拟数据库查询
	mockDb.ExpectQuery("SELECT count(*) FROM `data_nodes` WHERE area = ?").WithArgs(1). //表示传入的参数
												WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(1)) //期望得到的结果

	ok, err := CheckNetworkAreasIsUsed(1)
	fmt.Println(ok)
	assert.Nil(t, err)
}

func TestNetworkIspList(t *testing.T) {
	data := []isp_areas.ISPArea{{
		BaseModel: mysql.BaseModel{
			Id: 1,
		},
		Name: "AAA",
	}}
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyMethodReturn(isp_areas.NewISPAreaModel(), "List", data, int64(1), nil).Reset()
	list, err := NetworkIspList()
	assert.NoError(t, err)
	assert.Equal(t, 1, len(list))
}

func TestNetworkGeoList(t *testing.T) {
	data := []geo_areas.GEOArea{{
		BaseModel: mysql.BaseModel{
			Id: 1,
		},
		Name: "AAA",
	}}
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyMethodReturn(geo_areas.NewGEOAreaModel(), "List", data, int64(1), nil).Reset()
	list, err := NetworkGeoList()
	assert.NoError(t, err)
	assert.Equal(t, 1, len(list))
}

func TestNetworkGeoListError(t *testing.T) {
	mockDb := testcommon.InitSqlMock()
	mockDb.ExpectQuery("SELECT * FROM `geo_areas`").
		WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))
	list, err := NetworkGeoList()
	mockDb.Close()
	assert.Nil(t, err)
	assert.Equal(t, 1, len(list))
}

func TestNetworkGeoTree(t *testing.T) {
	data := []*geo_areas.GEOArea{{
		BaseModel: mysql.BaseModel{
			Id: 1,
		},
		Name:     "AAA",
		ParentId: 0,
	}, {
		BaseModel: mysql.BaseModel{
			Id: 2,
		},
		Name:     "BBB",
		ParentId: 1,
	}}
	patch := gomonkey.ApplyMethodReturn(geo_areas.NewGEOAreaModel(), "ListPointer", data, int64(2), nil)
	list, err := NetworkGeoTree()
	patch.Reset()
	assert.NoError(t, err)
	assert.Equal(t, 1, len(list))
}

func TestNetworkGeoTreeError(t *testing.T) {
	time.Sleep(time.Second)
	patch := gomonkey.ApplyMethodReturn(geo_areas.NewGEOAreaModel(), "ListPointer", nil, int64(0), errors.New("err"))
	list, err := NetworkGeoTree()
	patch.Reset()
	assert.Error(t, err)
	assert.Equal(t, 0, len(list))
}
