package network_areas

import (
	"reflect"

	"github.com/olivere/elastic/v7"

	"fobrain/fobrain/app/response/network_areas"
	"fobrain/initialize/es"
	"fobrain/initialize/mysql"
	assetEsModel "fobrain/models/elastic/assets"
	pocEsModel "fobrain/models/elastic/poc"
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/geo_areas"
	"fobrain/models/mysql/isp_areas"
)

func CheckNetworkAreasIsUsed(networkAreasId uint64) (bool, error) {
	//判断资产/漏洞索引数据是否使用网域
	q := elastic.NewBoolQuery()
	q = q.Must(elastic.NewTermQuery("area", networkAreasId))
	indices := []string{
		reflect.ValueOf(new(assetEsModel.Assets)).MethodByName("IndexName").Call([]reflect.Value{})[0].String(),
		reflect.ValueOf(new(pocEsModel.Poc)).MethodByName("IndexName").Call([]reflect.Value{})[0].String(),
	}
	isUsed, err := es.GetCountByMultiIndexAndQuery(indices, q)
	if err != nil {
		return false, err
	}
	if isUsed {
		return true, err
	}
	//判断数据源是否使用网域
	used, err := data_source.NewNodeModel().Total(mysql.WithWhere("area = ?", networkAreasId))
	if err != nil {
		return false, err
	}
	return used > 0, nil
}

// NetworkIspList 获取网域列表，等保列表数据
func NetworkIspList() ([]network_areas.NetworkAreaIsp, error) {
	data := make([]network_areas.NetworkAreaIsp, 0)
	handlers := make([]mysql.HandleFunc, 0)
	list, _, err := isp_areas.NewISPAreaModel().List(0, 0, handlers...)
	if err != nil {
		return data, err
	}
	for _, area := range list {
		data = append(data, network_areas.NetworkAreaIsp{
			ID:   area.Id,
			Name: area.Name,
		})
	}
	return data, nil
}

// NetworkGeoList 获取网域列表，区域数据
func NetworkGeoList() ([]network_areas.NetworkAreaGeo, error) {
	data := make([]network_areas.NetworkAreaGeo, 0)
	handlers := make([]mysql.HandleFunc, 0)
	list, _, err := geo_areas.NewGEOAreaModel().List(0, 0, handlers...)
	if err != nil {
		return data, err
	}
	for _, area := range list {
		data = append(data, network_areas.NetworkAreaGeo{
			ID:   area.Id,
			Name: area.Name,
		})
	}
	return data, nil
}

func NetworkGeoTree() ([]*network_areas.GEOAreaNode, error) {
	areaMap := make(map[uint64]*network_areas.GEOAreaNode)
	handlers := make([]mysql.HandleFunc, 0)
	list, _, err := geo_areas.NewGEOAreaModel().ListPointer(0, 0, handlers...)
	if err != nil {
		return nil, err
	}

	for _, area := range list {
		areaMap[area.Id] = &network_areas.GEOAreaNode{
			ID:       area.Id,
			Name:     area.Name,
			ParentId: area.ParentId,
			Adcode:   area.Adcode,
			SubAreas: make([]*network_areas.GEOAreaNode, 0),
		}
	}

	var data []*network_areas.GEOAreaNode
	for _, node := range areaMap {
		if node.ParentId == 0 {
			data = append(data, node)
		} else {
			if parent, exists := areaMap[node.ParentId]; exists {
				parent.SubAreas = append(parent.SubAreas, node)
			}
		}
	}

	return data, nil
}
