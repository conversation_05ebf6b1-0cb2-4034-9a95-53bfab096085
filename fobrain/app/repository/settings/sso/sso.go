package sso

import (
	"github.com/spf13/cast"

	"fobrain/models/mysql/system_configs"
)

func GetDetails() map[string]string {
	return system_configs.SSOConfigs()
}

func ChangedDetails(authUrl, tokenUrl, clientId, clientSecret, ssoEnable, ssoServer, loginInfoUrl, ssoCallback, tenant, roleID string) error {
	err := system_configs.NewSystemConfigs().SetMultiConfig(map[string]any{
		"auth_url":       authUrl,
		"token_url":      tokenUrl,
		"client_id":      clientId,
		"client_secret":  clientSecret,
		"sso_enable":     cast.ToString(ssoEnable),
		"sso_server":     ssoServer,
		"login_info_url": loginInfoUrl,
		"sso_callback":   ssoCallback,
		"tenant":         tenant,
		"sso_role":       roleID,
	})
	if err != nil {
		return err
	}

	return nil
}

func ServerList() []map[string]any {
	return []map[string]any{
		{
			"name": "派拉",
			"id":   "paula",
		},
		{
			"name": "GitHub",
			"id":   "github",
		},
		{
			"name": "4A",
			"id":   "4A",
		},
	}
}
