package compliance_history

import (
	"errors"
	"fobrain/models/elastic/assets"
	"net/http/httptest"
	"testing"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"

	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/initialize/mysql"
	"fobrain/models/elastic/compliance_monitor"
	"fobrain/models/elastic/poc"
	"fobrain/models/elastic/staff"
	compliance_monitor_mysql "fobrain/models/mysql/compliance_monitor"
	"fobrain/models/mysql/user"
)

// TestProcessTaskRetest_EmptyRecord 测试记录数据为空的情况
func TestProcessTaskRetest_EmptyRecord(t *testing.T) {
	gin.SetMode(gin.TestMode)

	ctx, _ := gin.CreateTestContext(httptest.NewRecorder())
	user := &user.User{BaseModel: mysql.BaseModel{Id: 1}}

	err := ProcessTaskRetest(ctx, user, 1, map[string]interface{}{})
	assert.Error(t, err)
	assert.Contains(t, err.<PERSON>rror(), "记录数据为空")
}

// TestProcessTaskRetest_NilUser 测试用户信息为空的情况
func TestProcessTaskRetest_NilUser(t *testing.T) {
	gin.SetMode(gin.TestMode)

	ctx, _ := gin.CreateTestContext(httptest.NewRecorder())
	oldRecord := map[string]interface{}{"id": "test"}

	err := ProcessTaskRetest(ctx, nil, 1, oldRecord)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "用户信息为空")
}

// TestProcessTaskRetest_ZeroTaskId 测试任务ID为0的情况
func TestProcessTaskRetest_ZeroTaskId(t *testing.T) {
	gin.SetMode(gin.TestMode)

	ctx, _ := gin.CreateTestContext(httptest.NewRecorder())
	user := &user.User{BaseModel: mysql.BaseModel{Id: 1}}
	oldRecord := map[string]interface{}{"id": "test"}

	err := ProcessTaskRetest(ctx, user, 0, oldRecord)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "任务ID不能为空")
}

// TestProcessTaskRetest_EmptyRecordId 测试记录ID为空的情况
func TestProcessTaskRetest_EmptyRecordId(t *testing.T) {
	gin.SetMode(gin.TestMode)

	ctx, _ := gin.CreateTestContext(httptest.NewRecorder())
	user := &user.User{BaseModel: mysql.BaseModel{Id: 1}}
	oldRecord := map[string]interface{}{"id": ""}

	err := ProcessTaskRetest(ctx, user, 1, oldRecord)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "记录ID为空")
}

// TestProcessTaskRetest_NotWaitRetestStatus 测试状态不是待复测的情况
func TestProcessTaskRetest_NotWaitRetestStatus(t *testing.T) {
	gin.SetMode(gin.TestMode)

	ctx, _ := gin.CreateTestContext(httptest.NewRecorder())
	user := &user.User{BaseModel: mysql.BaseModel{Id: 1}}
	oldRecord := map[string]interface{}{
		"id":          "test-record",
		"flow_status": poc.PocStatusOfRepaired, // 不是待复测状态
	}

	err := ProcessTaskRetest(ctx, user, 1, oldRecord)
	assert.NoError(t, err) // 应该跳过处理，不返回错误
}

// TestProcessTaskRetest_ZeroMonitorId 测试合规监测任务ID为空的情况
func TestProcessTaskRetest_ZeroMonitorId(t *testing.T) {
	gin.SetMode(gin.TestMode)

	ctx, _ := gin.CreateTestContext(httptest.NewRecorder())
	user := &user.User{BaseModel: mysql.BaseModel{Id: 1}}
	oldRecord := map[string]interface{}{
		"id":                    "test-record",
		"flow_status":           poc.PocStatusOfWaitRetest,
		"compliance_monitor_id": uint64(0), // 监控ID为0
	}

	err := ProcessTaskRetest(ctx, user, 1, oldRecord)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "合规监测任务ID为空")
}

// TestProcessTaskRetest_EmptyIP 测试IP地址为空的情况
func TestProcessTaskRetest_EmptyIP(t *testing.T) {
	gin.SetMode(gin.TestMode)

	ctx, _ := gin.CreateTestContext(httptest.NewRecorder())
	user := &user.User{BaseModel: mysql.BaseModel{Id: 1}}
	oldRecord := map[string]interface{}{
		"id":                    "test-record",
		"flow_status":           poc.PocStatusOfWaitRetest,
		"compliance_monitor_id": uint64(1),
		"ip":                    "", // IP为空
	}

	err := ProcessTaskRetest(ctx, user, 1, oldRecord)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "IP地址为空")
}

// TestProcessTaskRetest_Success 测试成功的复测流程
func TestProcessTaskRetest_Success(t *testing.T) {
	gin.SetMode(gin.TestMode)

	// 设置MySQL mock
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()

	ctx, _ := gin.CreateTestContext(httptest.NewRecorder())
	u := &user.User{BaseModel: mysql.BaseModel{Id: 1}}
	oldRecord := map[string]interface{}{
		"id":                    "test-record",
		"flow_status":           poc.PocStatusOfWaitRetest,
		"compliance_monitor_id": uint64(1),
		"ip":                    "***********",
	}

	patches := gomonkey.NewPatches()
	defer patches.Reset()

	// Mock ExecuteComplianceMonitor 成功，无违规
	patches.ApplyFunc(ExecuteComplianceMonitor, func(taskId, monitorId uint64, ip string) (*ComplianceRetestResult, error) {
		return &ComplianceRetestResult{
			HasViolation: false,
			Details:      map[string]interface{}{"ip": ip},
		}, nil
	})

	// Mock UpdateComplianceStatus 成功
	patches.ApplyFunc(UpdateComplianceStatus, func(h *OneComplianceHistory, oldRecord map[string]interface{}, staff interface{}, ccStaff interface{}) error {
		return nil
	})

	// Mock CreateHistory 成功
	patches.ApplyFunc(CreateHistory, func(ctx *gin.Context, user *user.User, param *OneComplianceHistory, staffObj *staff.Staff, oldStatus int, operations ...string) error {
		return nil
	})

	err := ProcessTaskRetest(ctx, u, 1, oldRecord)
	assert.NoError(t, err)
}

// TestExecuteComplianceMonitor_Success 测试成功执行合规检测
func TestExecuteComplianceMonitor_Success(t *testing.T) {
	gin.SetMode(gin.TestMode)

	// 设置MySQL mock
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()

	patches := gomonkey.NewPatches()
	defer patches.Reset()

	// Mock 数据库查询成功
	patches.ApplyMethodReturn(&compliance_monitor_mysql.ComplianceMonitor{}, "First",
		compliance_monitor_mysql.ComplianceMonitor{
			Rule:     `[{"rule_name":"port","whether":"in","content":"80"}]`,
			RuleType: compliance_monitor_mysql.RuleTypePort,
		}, nil)

	// Mock 查询资产成功
	patches.ApplyFunc(QueryAssetsByIP, func(ip string) ([]*assets.Assets, error) {
		return []*assets.Assets{
			{Ip: "***********"},
		}, nil
	})

	// Mock 检查违规返回 false
	patches.ApplyFunc(CheckAssetViolation, func(asset *assets.Assets, rules []compliance_monitor.QueryRule, ruleType int) bool {
		return false
	})

	result, err := ExecuteComplianceMonitor(1, 1, "***********")
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.False(t, result.HasViolation)
}

// TestExecuteComplianceMonitor_DatabaseError 测试数据库查询失败
func TestExecuteComplianceMonitor_DatabaseError(t *testing.T) {
	gin.SetMode(gin.TestMode)

	// 设置MySQL mock
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()

	patches := gomonkey.NewPatches()
	defer patches.Reset()

	// Mock 数据库查询失败
	patches.ApplyMethodReturn(&compliance_monitor_mysql.ComplianceMonitor{}, "First",
		compliance_monitor_mysql.ComplianceMonitor{}, errors.New("database error"))

	result, err := ExecuteComplianceMonitor(1, 1, "***********")
	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "database error")
}

// TestExecuteComplianceMonitor_InvalidJSON 测试解析规则JSON失败
func TestExecuteComplianceMonitor_InvalidJSON(t *testing.T) {
	gin.SetMode(gin.TestMode)

	// 设置MySQL mock
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()

	patches := gomonkey.NewPatches()
	defer patches.Reset()

	// Mock 数据库查询成功，但规则JSON无效
	patches.ApplyMethodReturn(&compliance_monitor_mysql.ComplianceMonitor{}, "First",
		compliance_monitor_mysql.ComplianceMonitor{
			Rule: "invalid-json",
		}, nil)

	result, err := ExecuteComplianceMonitor(1, 1, "***********")
	assert.Error(t, err)
	assert.Nil(t, result)
}

// TestExecuteComplianceMonitor_NoAssets 测试无资产情况
func TestExecuteComplianceMonitor_NoAssets(t *testing.T) {
	gin.SetMode(gin.TestMode)

	// 设置MySQL mock
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()

	patches := gomonkey.NewPatches()
	defer patches.Reset()

	// Mock 数据库查询成功
	patches.ApplyMethodReturn(&compliance_monitor_mysql.ComplianceMonitor{}, "First",
		compliance_monitor_mysql.ComplianceMonitor{
			Rule: `[{"rule_name":"port","whether":"in","content":"80"}]`,
		}, nil)

	// Mock 查询资产成功，但无资产
	patches.ApplyFunc(QueryAssetsByIP, func(ip string) ([]*assets.Assets, error) {
		return []*assets.Assets{}, nil
	})

	result, err := ExecuteComplianceMonitor(1, 1, "***********")
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.False(t, result.HasViolation)
	assert.Equal(t, "IP不存在，认为修复成功", result.Details["message"])
}

// TestCheckAssetViolation_PortRule 测试端口规则类型
func TestCheckAssetViolation_PortRule(t *testing.T) {
	gin.SetMode(gin.TestMode)

	patches := gomonkey.NewPatches()
	defer patches.Reset()

	// Mock checkPortViolation 返回 true
	patches.ApplyFunc(checkPortViolation, func(asset *assets.Assets, rules []compliance_monitor.QueryRule) bool {
		return true
	})

	asset := &assets.Assets{
		Ip: "***********",
	}
	rules := []compliance_monitor.QueryRule{
		{RuleName: "port", Whether: "in", Content: "80"},
	}

	result := CheckAssetViolation(asset, rules, compliance_monitor_mysql.RuleTypePort)
	assert.True(t, result)
}

// TestCheckAssetViolation_IPRule 测试IP规则类型
func TestCheckAssetViolation_IPRule(t *testing.T) {
	gin.SetMode(gin.TestMode)

	patches := gomonkey.NewPatches()
	defer patches.Reset()

	// Mock checkIPViolation 返回 true
	patches.ApplyFunc(checkIPViolation, func(asset *assets.Assets, rules []compliance_monitor.QueryRule) bool {
		return true
	})

	asset := &assets.Assets{
		Ip: "***********",
	}
	rules := []compliance_monitor.QueryRule{
		{RuleName: "ip", Whether: "on_line"},
	}

	result := CheckAssetViolation(asset, rules, compliance_monitor_mysql.RuleTypeIp)
	assert.True(t, result)
}

// TestCheckAssetViolation_UnsupportedRule 测试不支持的规则类型
func TestCheckAssetViolation_UnsupportedRule(t *testing.T) {
	gin.SetMode(gin.TestMode)

	asset := &assets.Assets{
		Ip: "***********",
	}
	rules := []compliance_monitor.QueryRule{
		{RuleName: "port", Whether: "in", Content: "80"},
	}

	result := CheckAssetViolation(asset, rules, 999) // 不支持的规则类型
	assert.False(t, result)
}

// TestCheckPortViolation_PortMatch 测试端口规则匹配
func TestCheckPortViolation_PortMatch(t *testing.T) {
	gin.SetMode(gin.TestMode)

	patches := gomonkey.NewPatches()
	defer patches.Reset()

	// Mock checkPortRule 返回 true
	patches.ApplyFunc(checkPortRule, func(ports []*assets.PortInfo, rule compliance_monitor.QueryRule) bool {
		return true
	})

	asset := &assets.Assets{
		Ports: []*assets.PortInfo{
			{Port: 80, Protocol: "tcp"},
		},
	}
	rules := []compliance_monitor.QueryRule{
		{RuleName: "port", Whether: "in", Content: "80"},
	}

	result := checkPortViolation(asset, rules)
	assert.True(t, result)
}

// TestCheckIPViolation_OnlineCheck 测试在线状态检查
func TestCheckIPViolation_OnlineCheck(t *testing.T) {
	gin.SetMode(gin.TestMode)

	asset := &assets.Assets{
		Ip:     "***********",
		Status: 2, // 离线状态
	}
	rules := []compliance_monitor.QueryRule{
		{RuleName: "ip", Whether: "on_line"},
	}

	result := checkIPViolation(asset, rules)
	assert.True(t, result) // 期望在线但实际离线，违规
}

// TestCheckPortRule_PortInList 测试端口在列表中
func TestCheckPortRule_PortInList(t *testing.T) {
	gin.SetMode(gin.TestMode)

	ports := []*assets.PortInfo{
		{Port: 80, Protocol: "tcp"},
		{Port: 443, Protocol: "tcp"},
	}
	rule := compliance_monitor.QueryRule{
		RuleName: "port",
		Whether:  "in",
		Content:  "80,22",
	}

	result := checkPortRule(ports, rule)
	assert.True(t, result) // 端口80在禁止列表中，违规
}

// TestCheckProtocolRule_ProtocolMatch 测试协议匹配
func TestCheckProtocolRule_ProtocolMatch(t *testing.T) {
	gin.SetMode(gin.TestMode)

	ports := []*assets.PortInfo{
		{Port: 80, Protocol: "tcp"},
		{Port: 53, Protocol: "udp"},
	}
	rule := compliance_monitor.QueryRule{
		RuleName: "protocol",
		Whether:  "in",
		Content:  "tcp,ftp",
	}

	result := checkProtocolRule(ports, rule)
	assert.True(t, result) // 协议tcp在禁止列表中，违规
}

// TestCheckProductRule_ProductMatch 测试组件匹配
func TestCheckProductRule_ProductMatch(t *testing.T) {
	gin.SetMode(gin.TestMode)

	ruleInfos := []*assets.RuleInfo{
		{Product: "nginx"},
		{Product: "apache"},
	}
	rule := compliance_monitor.QueryRule{
		RuleName: "product",
		Whether:  "in",
		Content:  "nginx,mysql",
	}

	result := checkProductRule(ruleInfos, rule)
	assert.True(t, result) // 组件nginx在禁止列表中，违规
}

// TestContains_Found 测试包含元素的情况
func TestContains_Found(t *testing.T) {
	gin.SetMode(gin.TestMode)

	slice := []string{"apple", "banana", "orange"}
	result := contains(slice, "banana")
	assert.True(t, result)
}

// TestContains_NotFound 测试不包含元素的情况
func TestContains_NotFound(t *testing.T) {
	gin.SetMode(gin.TestMode)

	slice := []string{"apple", "banana", "orange"}
	result := contains(slice, "grape")
	assert.False(t, result)
}

// TestContains_EmptySlice 测试空切片的情况
func TestContains_EmptySlice(t *testing.T) {
	gin.SetMode(gin.TestMode)

	slice := []string{}
	result := contains(slice, "apple")
	assert.False(t, result)
}
