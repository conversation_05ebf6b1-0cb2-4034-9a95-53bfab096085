package compliance_history

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"fobrain/fobrain/logs"
	"fobrain/initialize/es"
	"fobrain/initialize/mysql"
	"fobrain/models/elastic/assets"
	"fobrain/models/elastic/compliance_monitor"
	"fobrain/models/elastic/poc"
	compliance_monitor_mysql "fobrain/models/mysql/compliance_monitor"
	"fobrain/models/mysql/user"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/olivere/elastic/v7"
	"github.com/spf13/cast"
)

// ProcessTaskRetest 处理单个任务的复测逻辑
func ProcessTaskRetest(ctx *gin.Context, user *user.User, taskId uint64, oldRecord map[string]interface{}) error {
	// 参数验证
	if len(oldRecord) == 0 {
		return errors.New("记录数据为空")
	}
	if user == nil {
		return errors.New("用户信息为空")
	}
	if taskId == 0 {
		return errors.New("任务ID不能为空")
	}

	// 获取记录ID
	recordId := cast.ToString(oldRecord["id"])
	if recordId == "" {
		return errors.New("记录ID为空")
	}

	// 检查流转状态
	flowStatus := cast.ToInt(oldRecord["flow_status"])
	if flowStatus != poc.PocStatusOfWaitRetest {
		logs.GetLogger().Infof("记录状态不是待复测，跳过处理: recordId=%s, flowStatus=%d", recordId, flowStatus)
		return nil
	}

	monitorId := cast.ToUint64(oldRecord["compliance_monitor_id"])
	if monitorId == 0 {
		return fmt.Errorf("合规监测任务ID为空")
	}

	ip := cast.ToString(oldRecord["ip"])
	if ip == "" {
		return fmt.Errorf("IP地址为空")
	}

	logs.GetLogger().Infof("开始处理复测: recordId=%s, taskId=%d, monitorId=%d, ip=%s", recordId, taskId, monitorId, ip)

	// 执行合规检测
	res, err := ExecuteComplianceMonitor(taskId, monitorId, ip)
	if err != nil {
		logs.GetLogger().Errorf("重新执行合规检测任务失败: recordId=%s, error=%v", recordId, err)
		return fmt.Errorf("重新执行合规检测任务失败")
	}

	status := poc.PocStatusOfRepaired

	// 存在违规 复测未通过 反之复测通过
	if res.HasViolation {
		status = poc.PocStatusOfNoRepair
	}

	// 验证返回结果
	resultIP, ok := res.Details["ip"]
	if !ok {
		logs.GetLogger().Warnf("复测结果中缺少IP信息: recordId=%s", recordId)
		return errors.New("缺少IP信息")
	}

	h := &OneComplianceHistory{
		RecordId: recordId,
		Status:   status,
	}

	// 更新状态
	err = UpdateComplianceStatus(h, oldRecord, nil, nil)
	if err != nil {
		logs.GetLogger().Errorf("更新合规检测状态失败: recordId=%s, error=%v", recordId, err)
		return fmt.Errorf("更新合规检测状态失败")
	}

	// 创建历史记录
	content := fmt.Sprintf("合规检测状态变为%s", poc.PocStatusRelations[status])
	err = CreateHistory(ctx, user, h, nil, flowStatus, content)
	if err != nil {
		logs.GetLogger().Errorf("创建合规检测历史记录失败: recordId=%s, error=%v", recordId, err)
	}

	logs.GetLogger().Infof("合规检测复测完成: recordId=%s, ip=%s, oldStatus=%d, newStatus=%d",
		recordId, resultIP, flowStatus, status)

	if status == poc.PocStatusOfNoRepair {
		return fmt.Errorf("存在违规")
	}

	return nil
}

// ComplianceRetestResult 合规检测复测结果
type ComplianceRetestResult struct {
	HasViolation bool                   `json:"has_violation"` // 是否有违规
	Details      map[string]interface{} `json:"details"`       // 详细结果
}

// ExecuteComplianceMonitor 执行合规检测监控任务
func ExecuteComplianceMonitor(taskId, monitorId uint64, ip string) (*ComplianceRetestResult, error) {
	// 获取合规监测规则信息
	monitor, err := compliance_monitor_mysql.NewComplianceMonitorModel().First(mysql.WithWhere("id = ?", monitorId))
	if err != nil {
		return nil, err
	}

	// 解析合规监测规则
	var rules []compliance_monitor.QueryRule
	err = json.Unmarshal([]byte(monitor.Rule), &rules)
	if err != nil {
		return nil, err
	}

	// 查询当前IP的资产信息
	currentAssets, err := QueryAssetsByIP(ip)
	if err != nil {
		return nil, err
	}

	// 如果没有找到资产，说明IP已经不存在，认为修复成功
	if len(currentAssets) == 0 {
		return &ComplianceRetestResult{
			HasViolation: false,
			Details:      map[string]interface{}{"message": "IP不存在，认为修复成功"},
		}, nil
	}

	// 根据规则类型执行检测
	hasViolation := false
	for _, asset := range currentAssets {
		if CheckAssetViolation(asset, rules, monitor.RuleType) {
			hasViolation = true
			break
		}
	}

	return &ComplianceRetestResult{
		HasViolation: hasViolation,
		Details: map[string]interface{}{
			"ip":           ip,
			"task_id":      taskId,
			"monitor_id":   monitorId,
			"assets_count": len(currentAssets),
		},
	}, nil
}

// QueryAssetsByIP 根据IP查询资产信息
func QueryAssetsByIP(ip string) ([]*assets.Assets, error) {
	boolQuery := elastic.NewBoolQuery()
	boolQuery = boolQuery.Must(elastic.NewTermQuery("ip", ip))
	boolQuery = boolQuery.Must(elastic.NewTermQuery("status", 1)) // 只查询在线资产

	result, err := es.GetEsClient().Search(assets.NewAssets().IndexName()).
		Size(100).
		Query(boolQuery).
		Do(context.TODO())

	if err != nil {
		return nil, err
	}

	res := make([]*assets.Assets, 0)
	if result.TotalHits() > 0 {
		for _, hit := range result.Hits.Hits {
			a := &assets.Assets{}
			err = json.Unmarshal(hit.Source, a)
			if err != nil {
				continue
			}
			res = append(res, a)
		}
	}
	return res, nil
}

// CheckAssetViolation 检查资产是否违规
func CheckAssetViolation(asset *assets.Assets, rules []compliance_monitor.QueryRule, ruleType int) bool {
	switch ruleType {
	case compliance_monitor_mysql.RuleTypePort:
		return checkPortViolation(asset, rules)
	case compliance_monitor_mysql.RuleTypeIp:
		return checkIPViolation(asset, rules)
	default:
		return false
	}
}

// checkPortViolation 检查端口违规
func checkPortViolation(asset *assets.Assets, rules []compliance_monitor.QueryRule) bool {
	for _, rule := range rules {
		switch rule.RuleName {
		case "port", "ports.port":
			if checkPortRule(asset.Ports, rule) {
				return true
			}
		case "protocol", "ports.protocol":
			if checkProtocolRule(asset.Ports, rule) {
				return true
			}
		case "product", "rule_infos.product":
			if checkProductRule(asset.RuleInfos, rule) {
				return true
			}
		}
	}
	return false
}

// checkIPViolation 检查IP违规
func checkIPViolation(asset *assets.Assets, rules []compliance_monitor.QueryRule) bool {
	for _, rule := range rules {
		if rule.RuleName == "ip" {
			if rule.Whether == "on_line" && asset.Status != 1 {
				return true
			}
			if rule.Whether == "off_line" && asset.Status != 2 {
				return true
			}
		}
	}
	return false
}

// checkPortRule 检查端口规则
func checkPortRule(ports []*assets.PortInfo, rule compliance_monitor.QueryRule) bool {
	if rule.Whether == "in" {
		for _, port := range ports {
			if contains(strings.Split(rule.Content, ","), cast.ToString(port.Port)) {
				return true
			}
		}
	}
	return false
}

// checkProtocolRule 检查协议规则
func checkProtocolRule(ports []*assets.PortInfo, rule compliance_monitor.QueryRule) bool {
	if rule.Whether == "in" {
		for _, port := range ports {
			if contains(strings.Split(rule.Content, ","), port.Protocol) {
				return true
			}
		}
	}
	return false
}

// checkProductRule 检查组件规则
func checkProductRule(ruleInfos []*assets.RuleInfo, rule compliance_monitor.QueryRule) bool {
	if rule.Whether == "in" {
		for _, ruleInfo := range ruleInfos {
			if contains(strings.Split(rule.Content, ","), ruleInfo.Product) {
				return true
			}
		}
	}
	return false
}

// contains 检查切片是否包含指定元素
func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}
