package compliance_history

import (
	"encoding/json"
	"errors"
	"fmt"
	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/initialize/mysql"
	"fobrain/models/elastic/compliance_monitor"
	"fobrain/models/elastic/poc"
	"fobrain/models/elastic/staff"
	"fobrain/models/mysql/compliance_risks"
	"fobrain/models/mysql/user"
	"github.com/alicebob/miniredis/v2"
	"github.com/go-redis/redis/v8"
	"github.com/olivere/elastic/v7"
	"io"
	"net/http/httptest"
	"net/smtp"
	"reflect"
	"strings"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

// TestCreateHistory_Success 测试成功创建历史记录
func TestCreateHistory_Success(t *testing.T) {
	gin.SetMode(gin.TestMode)

	// 设置MySQL mock
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()

	// 创建测试上下文
	ctx, _ := gin.CreateTestContext(httptest.NewRecorder())

	// 准备测试数据
	user := &user.User{
		BaseModel: mysql.BaseModel{Id: 1},
		Username:  "test_user",
	}

	staff := &staff.Staff{
		Id:   "staff_123",
		Name: "测试人员",
	}

	param := &OneComplianceHistory{
		RecordId:      "record_123",
		ToStaffId:     "staff_123",
		ToStaffName:   "测试人员",
		LimitDate:     "2024-12-31 15:30:00",
		SendNotice:    1,
		TimeoutNotice: 1,
		Descrition:    "测试描述",
		ToCc:          "<EMAIL>",
		Status:        poc.PocStatusOfBeRepair,
		ExecNow:       0,
		OriginalId:    "original_123",
		OperationType: "distribute",
	}

	oldStatus := poc.PocStatusOfNew

	// 模拟数据库事务和插入操作
	mockDb.Mock.ExpectBegin()
	mockDb.Mock.ExpectExec("INSERT INTO `compliance_risks_histories`").
		WillReturnResult(sqlmock.NewResult(1, 1))
	mockDb.Mock.ExpectCommit()

	// 使用 gomonkey 模拟依赖函数
	patches := gomonkey.NewPatches()
	defer patches.Reset()

	// 模拟状态关系映射存在
	patches.ApplyGlobalVar(&poc.PocStatusRelations, map[int]string{
		poc.PocStatusOfBeRepair: "待修复",
		poc.PocStatusOfNew:      "新发现",
	})

	// 模拟 HandleFile 返回空（无文件上传）
	patches.ApplyFuncReturn(HandleFile, "", nil)

	// 执行测试
	err := CreateHistory(ctx, user, param, staff, oldStatus)

	// 验证结果
	assert.NoError(t, err)
	assert.NoError(t, mockDb.ExpectationsWereMet())
}

// TestCreateHistory_InvalidStatus 测试无效状态错误
func TestCreateHistory_InvalidStatus(t *testing.T) {
	gin.SetMode(gin.TestMode)

	// 设置MySQL mock
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()

	ctx, _ := gin.CreateTestContext(httptest.NewRecorder())

	user := &user.User{
		BaseModel: mysql.BaseModel{Id: 1},
		Username:  "test_user",
	}

	param := &OneComplianceHistory{
		RecordId: "record_123",
		Status:   999, // 无效状态
	}

	// 使用 gomonkey 模拟状态关系映射（不包含999）
	patches := gomonkey.NewPatches()
	defer patches.Reset()

	patches.ApplyGlobalVar(&poc.PocStatusRelations, map[int]string{
		poc.PocStatusOfBeRepair: "待修复",
		poc.PocStatusOfNew:      "新发现",
	})

	// 执行测试
	err := CreateHistory(ctx, user, param, nil, poc.PocStatusOfNew)

	// 验证结果
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "状态不存在")
}

// TestCreateHistory_WithFileUpload 测试带文件上传的情况
func TestCreateHistory_WithFileUpload(t *testing.T) {
	gin.SetMode(gin.TestMode)

	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()

	ctx, _ := gin.CreateTestContext(httptest.NewRecorder())

	user := &user.User{
		BaseModel: mysql.BaseModel{Id: 1},
		Username:  "test_user",
	}

	param := &OneComplianceHistory{
		RecordId: "record_123",
		Status:   poc.PocStatusOfBeRepair,
	}

	// 模拟数据库事务和插入操作
	mockDb.Mock.ExpectBegin()
	mockDb.Mock.ExpectExec("INSERT INTO `compliance_risks_histories`").
		WillReturnResult(sqlmock.NewResult(1, 1))
	mockDb.Mock.ExpectCommit()

	patches := gomonkey.NewPatches()
	defer patches.Reset()

	patches.ApplyGlobalVar(&poc.PocStatusRelations, map[int]string{
		poc.PocStatusOfBeRepair: "待修复",
	})

	// 模拟 HandleFile 返回文件名
	patches.ApplyFuncReturn(HandleFile, "test_file.pdf", nil)

	// 执行测试
	err := CreateHistory(ctx, user, param, nil, poc.PocStatusOfNew)

	// 验证结果
	assert.NoError(t, err)
}

// TestCreateHistory_FileUploadError 测试文件上传错误
func TestCreateHistory_FileUploadError(t *testing.T) {
	gin.SetMode(gin.TestMode)

	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()

	ctx, _ := gin.CreateTestContext(httptest.NewRecorder())

	user := &user.User{
		BaseModel: mysql.BaseModel{Id: 1},
		Username:  "test_user",
	}

	param := &OneComplianceHistory{
		RecordId: "record_123",
		Status:   poc.PocStatusOfBeRepair,
	}
	time.Sleep(time.Second)
	patches := gomonkey.NewPatches()
	defer patches.Reset()

	patches.ApplyGlobalVar(&poc.PocStatusRelations, map[int]string{
		poc.PocStatusOfBeRepair: "待修复",
	})

	// 模拟 HandleFile 返回错误
	patches.ApplyFuncReturn(HandleFile, "", errors.New("文件上传失败"))

	// 执行测试
	err := CreateHistory(ctx, user, param, nil, poc.PocStatusOfNew)

	// 验证结果
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "文件上传失败")
}

// TestCreateHistory_DatabaseError 测试数据库创建失败
func TestCreateHistory_DatabaseError(t *testing.T) {
	gin.SetMode(gin.TestMode)

	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()

	ctx, _ := gin.CreateTestContext(httptest.NewRecorder())

	user := &user.User{
		BaseModel: mysql.BaseModel{Id: 1},
		Username:  "test_user",
	}

	param := &OneComplianceHistory{
		RecordId: "record_123",
		Status:   poc.PocStatusOfBeRepair,
	}

	// 模拟数据库插入失败
	mockDb.Mock.ExpectExec("INSERT INTO `compliance_risks_histories`").
		WillReturnError(errors.New("数据库连接失败"))

	patches := gomonkey.NewPatches()
	defer patches.Reset()

	patches.ApplyGlobalVar(&poc.PocStatusRelations, map[int]string{
		poc.PocStatusOfBeRepair: "待修复",
	})

	patches.ApplyFuncReturn(HandleFile, "", nil)

	// 执行测试
	err := CreateHistory(ctx, user, param, nil, poc.PocStatusOfNew)

	// 验证结果
	assert.Error(t, err)
}

// TestCreateHistory_SetOtherOneFunction 测试SetOtherOne函数的行为
func TestCreateHistory_SetOtherOneFunction(t *testing.T) {
	gin.SetMode(gin.TestMode)

	tests := []struct {
		name     string
		input    int
		expected int
	}{
		{
			name:     "零值返回零",
			input:    0,
			expected: 0,
		},
		{
			name:     "非零值返回一",
			input:    5,
			expected: 1,
		},
		{
			name:     "负数返回一",
			input:    -1,
			expected: 1,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := SetOtherOne(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// 基准测试
func BenchmarkCreateHistory(b *testing.B) {
	gin.SetMode(gin.TestMode)

	ctx, _ := gin.CreateTestContext(httptest.NewRecorder())

	user := &user.User{
		BaseModel: mysql.BaseModel{Id: 1},
		Username:  "bench_user",
	}

	param := &OneComplianceHistory{
		RecordId: "bench_record",
		Status:   poc.PocStatusOfBeRepair,
	}

	// 模拟依赖
	patches := gomonkey.NewPatches()
	defer patches.Reset()

	patches.ApplyGlobalVar(&poc.PocStatusRelations, map[int]string{
		poc.PocStatusOfBeRepair: "待修复",
	})

	patches.ApplyFuncReturn(HandleFile, "", nil)

	// 模拟数据库创建成功
	patches.ApplyMethodFunc((*compliance_risks.ComplianceRisksHistory)(nil), "Create",
		func(c *compliance_risks.ComplianceRisksHistory, history *compliance_risks.ComplianceRisksHistory) error {
			return nil
		})

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		CreateHistory(ctx, user, param, nil, poc.PocStatusOfNew)
	}
}

func TestSendLocalNotice(t *testing.T) {
	mockDb := testcommon.InitSqlMock()
	defer mockDb.Close()
	mockDb.ExpectBegin()
	mockDb.ExpectExec("INSERT INTO `notify_alarm_center` (`created_at`,`updated_at`,`msg_type`,`msg_content`,`msg_source`,`relation_type`,`relation_content`,`remark`,`read`,`user_id`,`staff_id`) VALUES (?,?,?,?,?,?,?,?,?,?,?)").
		WillReturnResult(sqlmock.NewResult(1, 1))
	mockDb.ExpectCommit()
	err := SendLocalNotice(map[string]interface{}{
		"ip":          "***********",
		"person_name": "333",
	}, "dfsdabdb")
	assert.NoError(t, err)
}

func TestGetEmailContent(t *testing.T) {
	testCases := []struct {
		name                string
		status              int
		receiverName        string
		num                 int
		emailData           []ComplianceEmailData
		expectedTitle       string
		expectedContentFrag []string // 内容中应该包含的字段片段
	}{
		{
			name:         "BeRepair with table",
			status:       poc.PocStatusOfBeRepair,
			receiverName: "Alice",
			num:          2,
			emailData: []ComplianceEmailData{
				{Ip: "***********", RuleName: "禁止弱口令", BusinessNames: []string{"业务A", "业务B"}},
				{Ip: "********", RuleName: "禁止明文密码", BusinessNames: []string{"业务C"}},
			},
			expectedTitle: "『合规检测修复提醒』",
			expectedContentFrag: []string{
				"您好，Alice",
				"有2个合规检测问题派发给您",
				"<table",
				"***********",
				"禁止弱口令",
				"业务A, 业务B",
			},
		},
		{
			name:          "Forward no table",
			status:        poc.PocStatusOfForward,
			receiverName:  "Bob",
			num:           1,
			emailData:     nil,
			expectedTitle: "『合规检测修复提醒』",
			expectedContentFrag: []string{
				"您好，Bob",
				"有1个合规检测问题转交给您",
			},
		},
		{
			name:          "Timeout with empty table data",
			status:        poc.PocStatusOfTimeout,
			receiverName:  "Charlie",
			num:           3,
			emailData:     []ComplianceEmailData{},
			expectedTitle: "『合规检测修复超时提醒』",
			expectedContentFrag: []string{
				"您好，Charlie",
				"有3个合规检测问题超时未修复",
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			title, content := GetEmailContent(tc.status, tc.receiverName, tc.num, tc.emailData)

			if title != tc.expectedTitle {
				t.Errorf("expected title %q, got %q", tc.expectedTitle, title)
			}

			for _, frag := range tc.expectedContentFrag {
				if !strings.Contains(content, frag) {
					t.Errorf("expected content to contain %q, but it was missing", frag)
				}
			}
		})
	}
}
func TestGetFileType(t *testing.T) {
	tests := []struct {
		fileName     string
		expectedType string
	}{
		{"report.doc", "doc"},
		{"report.DOCX", "docx"},
		{"manual.pdf", "pdf"},
		{"image.jpg", "jpeg"},
		{"photo.JPEG", "jpeg"},
		{"picture.png", "png"},
		{"unknownfile.txt", "unknown"},
		{"noextension", "unknown"},
		{"UPPER.PNG", "png"},
	}

	for _, tt := range tests {
		t.Run(tt.fileName, func(t *testing.T) {
			got := getFileType(tt.fileName)
			if got != tt.expectedType {
				t.Errorf("GetFileType(%q) = %q; want %q", tt.fileName, got, tt.expectedType)
			}
		})
	}
}

func TestCheckFileType(t *testing.T) {
	tests := []struct {
		fileName    string
		expectedRes bool
	}{
		{"a.doc", true},
		{"a.pdf", true},
		{"a.jpeg", true},
		{"a.JPG", true},
		{"a.unknown", false},
		{"a.txt", false},
		{"justname", false},
	}

	for _, tt := range tests {
		t.Run(tt.fileName, func(t *testing.T) {
			got := CheckFileType(tt.fileName)
			if got != tt.expectedRes {
				t.Errorf("CheckFileType(%q) = %v; want %v", tt.fileName, got, tt.expectedRes)
			}
		})
	}
}

func TestSendLocalMsgNotice(t *testing.T) {
	mockDb := testcommon.InitSqlMock()
	defer mockDb.Close()
	mockDb.ExpectBegin()
	mockDb.ExpectExec("INSERT INTO `notify_alarm_center` (`created_at`,`updated_at`,`msg_type`,`msg_content`,`msg_source`,`relation_type`,`relation_content`,`remark`,`read`,`user_id`,`staff_id`) VALUES (?,?,?,?,?,?,?,?,?,?,?)").
		WillReturnResult(sqlmock.NewResult(1, 1))
	mockDb.ExpectCommit()
	err := SendLocalMsgNotice("admin", 1, "savdfsa", 3, "2f2f")
	assert.NoError(t, err)
}

//func TestSendWebhookMsg(t *testing.T) {
//
//	patch := gomonkey.ApplyFuncReturn((*webhook.VulnerabilityMsg)(nil), "SendMsg", nil)
//	defer  patch.Reset()
//	err := SendWebhookMsg(&OneComplianceHistory{
//		RecordId:  "988da3gbve3b3",
//		LimitDate: "2025-07-07 14:32:12",
//		Status:    poc.PocStatusOfBeRepair,
//	}, &staff.Staff{}, &user.User{}, []map[string]interface{}{})
//	assert.NoError(t, err)
//}

func TestCreateComplianceTask(t *testing.T) {
	mockDb := testcommon.InitSqlMock()
	defer mockDb.Close()

	// 定义期望的 SQL 行为
	mockDb.ExpectBegin()
	mockDb.ExpectExec("INSERT INTO `threat_tasks` (`created_at`,`updated_at`,`user_id`,`to_staff_id`,`to_staff_email`,`limit_date`,`begin_time`,`timeout_notice`,`to_cc`,`poc_id`,`status`,`task_type`,`genre`,`poc_ids`,`timeout_receiver_id`,`timeout_frequency`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)").
		WillReturnResult(sqlmock.NewResult(1, 1)) // 模拟返回插入 1 行的结果
	mockDb.ExpectCommit()
	err := CreateComplianceTask(1, &user.User{
		BaseModel: mysql.BaseModel{
			Id: 1,
		},
	},
		&SomeComplianceHistory{
			TimeoutNotice: 1,
			RecordIds:     []string{"11"},
		},
		&staff.Staff{
			Id:    "2gb3webe",
			Email: []string{"1234"},
		})
	assert.NoError(t, err)
}

func TestUpdateComplianceStatus(t *testing.T) {
	s, err := miniredis.Run()
	if err != nil {
		t.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
	}
	defer s.Close()

	cli := redis.NewClient(&redis.Options{
		Addr: s.Addr(),
	})
	testcommon.SetRedisClient(cli)

	err = UpdateComplianceStatus(&OneComplianceHistory{
		Status: 123,
	},
		map[string]interface{}{
			"flow_status": 10,
			"person_fid":  "233423",
		}, &staff.Staff{
			Id: "323w",
		}, &staff.Staff{
			Id: "323w",
		})
	assert.NoError(t, err)
}
func TestGetStaffsEmails(t *testing.T) {
	mockEs := testcommon.NewMockServer()
	defer mockEs.Close()
	// 模拟 staff 的 _search 返回（只含 docvalue_fields）
	mockEs.Register("/staff/_search", elastic.SearchResult{
		TookInMillis: 1,
		TimedOut:     false,
		Shards: &elastic.ShardsInfo{
			Total:      1,
			Successful: 1,
			Skipped:    0,
			Failed:     0,
		},
		Hits: &elastic.SearchHits{
			TotalHits: &elastic.TotalHits{
				Value:    1,
				Relation: "eq",
			},
			Hits: []*elastic.SearchHit{
				{
					Fields: map[string]interface{}{
						"email": []interface{}{"<EMAIL>"},
					},
				},
			},
		},
	})

	ids := []string{"staff-123"}
	emails, err := GetStaffsEmails(ids)

	assert.NoError(t, err)
	assert.Len(t, emails, 1)
	assert.Equal(t, "<EMAIL>", emails[0])
}
func TestGetComplianceRecordsByIds(t *testing.T) {
	mockDoc := compliance_monitor.ComplianceMonitorTaskRecords{
		Id: "id-123",
		Ip: "***********",
	}
	docJSON, _ := json.Marshal(mockDoc)

	mockEs := testcommon.NewMockServer()
	defer mockEs.Close()
	mockEs.Register("/compliance_monitor_task_records/_search", elastic.SearchResult{
		TookInMillis: 1,
		TimedOut:     false,
		Shards: &elastic.ShardsInfo{
			Total:      5,
			Successful: 5,
			Skipped:    0,
			Failed:     0,
		},
		Hits: &elastic.SearchHits{
			TotalHits: &elastic.TotalHits{
				Value:    1,
				Relation: "eq",
			},
			Hits: []*elastic.SearchHit{
				{
					Source: docJSON,
				},
			},
		},
	})

	ids := []string{"id-123"}
	records, err := GetComplianceRecordsByIds(ids)
	assert.NoError(t, err)
	assert.Len(t, records, 1)
	assert.Equal(t, "id-123", records[0].Id)
	assert.Equal(t, "***********", records[0].Ip)
}

type MyWriteCloser struct{}

// Write 实现了 io.Writer 接口
func (m *MyWriteCloser) Write(p []byte) (n int, err error) {
	// 在这里处理写入逻辑
	fmt.Printf("Writing: %s\n", p)
	return len(p), nil
}

// Close 实现了 io.Closer 接口
func (m *MyWriteCloser) Close() error {
	// 在这里处理关闭逻辑
	fmt.Println("Closing WriteCloser")
	return nil
}
func TestSendNotice(t *testing.T) {
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()
	mockDb.ExpectQuery("SELECT * FROM `email_config` ORDER BY `email_config`.`id` LIMIT 1").
		WillReturnRows(
			sqlmock.NewRows(
				[]string{
					"id", "address", "port", "encrypt_type", "user_name", "password",
					"category", "created_at", "updated_at"},
			).AddRow(1, "smtp.exmail.qq.com", 465, 2, "<EMAIL>",
				"W4jdTHMQKZy5fiGy", "smtp", "2024-10-10 14:44:11", "2024-10-10 14:44:11"),
		)

	var writer io.WriteCloser = &MyWriteCloser{}
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyMethod(reflect.TypeOf(&smtp.Client{}), "Data", func(client *smtp.Client) (io.WriteCloser, error) {
		return writer, nil
	}).Reset()
	mockDoc := compliance_monitor.ComplianceMonitorTaskRecords{
		Id: "id-123",
		Ip: "***********",
	}
	docJSON, _ := json.Marshal(mockDoc)

	mockEs := testcommon.NewMockServer()
	defer mockEs.Close()
	mockEs.Register("/compliance_monitor_task_records/_search", elastic.SearchResult{
		TookInMillis: 1,
		TimedOut:     false,
		Shards: &elastic.ShardsInfo{
			Total:      5,
			Successful: 5,
			Skipped:    0,
			Failed:     0,
		},
		Hits: &elastic.SearchHits{
			TotalHits: &elastic.TotalHits{
				Value:    1,
				Relation: "eq",
			},
			Hits: []*elastic.SearchHit{
				{
					Source: docJSON,
				},
			},
		},
	})
	mockEs.Register("/staff/_search", elastic.SearchResult{
		TookInMillis: 1,
		TimedOut:     false,
		Shards: &elastic.ShardsInfo{
			Total:      1,
			Successful: 1,
			Skipped:    0,
			Failed:     0,
		},
		Hits: &elastic.SearchHits{
			TotalHits: &elastic.TotalHits{
				Value:    1,
				Relation: "eq",
			},
			Hits: []*elastic.SearchHit{
				{
					Fields: map[string]interface{}{
						"email": []interface{}{"<EMAIL>"},
					},
				},
			},
		},
	})

	err := SendNotice(&SomeComplianceHistory{
		SendNotice: 1,
		Status:     1,
		ToCc:       "wsvw",
		RecordIds:  []string{"adb4t"},
	}, &staff.Staff{
		Name:  "sdfe",
		Email: []string{"<EMAIL>"},
	})

	assert.NoError(t, err)
}
