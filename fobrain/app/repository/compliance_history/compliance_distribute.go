package compliance_history

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"fobrain/fobrain/app/repository/threat_history"
	"fobrain/fobrain/common/localtime"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/spf13/cast"

	"fobrain/fobrain/app/repository/email"
	"fobrain/fobrain/logs"
	"fobrain/initialize/es"
	"fobrain/initialize/redis"
	"fobrain/models/elastic/compliance_monitor"
	"fobrain/models/elastic/poc"
	"fobrain/models/elastic/staff"
	"fobrain/models/mysql/compliance_risks"
	"fobrain/models/mysql/threat_tasks"
	"fobrain/models/mysql/user"
	"fobrain/models/mysql/workbench"
	"fobrain/pkg/queue"
	"fobrain/pkg/utils"
	"fobrain/webhook"
	"github.com/gin-gonic/gin"
	"github.com/olivere/elastic/v7"
)

type (
	OneComplianceHistory struct {
		RecordId    string   `json:"record_id" form:"record_id" uri:"record_id" validate:"omitempty" zh:"合规检测记录ID"`
		RecordIds   []string `json:"record_ids" form:"record_ids" uri:"record_ids" validate:"omitempty" zh:"合规检测记录ID列表"`
		ToStaffId   string   `json:"to_staff_id" form:"to_staff_id" uri:"to_staff_id" validate:"omitempty" zh:"修复负责人ID"`
		ToStaffName string   `json:"to_staff_name" form:"to_staff_name" uri:"to_staff_name" validate:"omitempty" zh:"修复负责人姓名"`
		LimitDate   string   `json:"limit_date" form:"limit_date" uri:"limit_date" validate:"omitempty,datetime=2006-01-02 15:04:05" zh:"修复时间要求"`

		SendNotice    int    `json:"send_notice" form:"send_notice" uri:"send_notice" validate:"omitempty,number" zh:"是否发送邮件"`
		TimeoutNotice int    `json:"timeout_notice" form:"timeout_notice" uri:"timeout_notice" validate:"omitempty,number" zh:"超期是否邮件通知"`
		Descrition    string `json:"descrition" form:"descrition" uri:"descrition" validate:"" zh:"备注"`

		ToCc    string `json:"to_cc" form:"to_cc" uri:"to_cc" validate:"" zh:"抄送邮件"`
		Status  int    `json:"status" form:"status" uri:"status" validate:"required,number" zh:"流转状态"`
		ExecNow int    `json:"exec_now" form:"exec_now" uri:"exec_now" validate:"omitempty,number" zh:"是否立即执行"`

		OriginalId    string `json:"original_id" form:"original_id" uri:"original_id" validate:"" zh:"原始记录ID"`
		OperationType string `json:"operation_type" form:"operation_type" uri:"operation_type" validate:"" zh:"操作类型"`

		TimeoutReceiverId []string `json:"timeout_receiver_id" form:"timeout_receiver_id" uri:"timeout_receiver_id"` // 超期邮件通知抄送人ID
		TimeoutFrequency  int      `json:"timeout_frequency" form:"timeout_frequency" uri:"timeout_frequency"`       //超期邮件发送频率（1-每天一次，2-三天一次，3-每周一次，4-每月一次，5-三月一次）
	}

	SomeComplianceHistory struct {
		RecordIds   []string `json:"record_ids" form:"record_ids" uri:"record_ids" validate:"required" zh:"合规检测记录ID列表"`
		ToStaffId   string   `json:"to_staff_id" form:"to_staff_id" uri:"to_staff_id" validate:"omitempty" zh:"修复负责人ID"`
		ToStaffName string   `json:"to_staff_name" form:"to_staff_name" uri:"to_staff_name" validate:"omitempty" zh:"修复负责人姓名"`
		LimitDate   string   `json:"limit_date" form:"limit_date" uri:"limit_date" validate:"omitempty,datetime=2006-01-02 15:04:05" zh:"修复时间要求"`

		SendNotice    int    `json:"send_notice" form:"send_notice" uri:"send_notice" validate:"omitempty,number" zh:"是否发送邮件"`
		TimeoutNotice int    `json:"timeout_notice" form:"timeout_notice" uri:"timeout_notice" validate:"omitempty,number" zh:"超期是否邮件通知"`
		Descrition    string `json:"descrition" form:"descrition" uri:"descrition" validate:"" zh:"备注"`

		ToCc    string `json:"to_cc" form:"to_cc" uri:"to_cc" validate:"" zh:"抄送邮件"`
		Status  int    `json:"status" form:"status" uri:"status" validate:"required,number" zh:"流转状态"`
		ExecNow int    `json:"exec_now" form:"exec_now" uri:"exec_now" validate:"omitempty,number" zh:"是否立即执行"`

		OriginalId    string `json:"original_id" form:"original_id" uri:"original_id" validate:"" zh:"原始记录ID"`
		OperationType string `json:"operation_type" form:"operation_type" uri:"operation_type" validate:"" zh:"操作类型"`

		IsRecycleBin    int      `json:"is_recycle_bin" form:"is_recycle_bin" uri:"is_recycle_bin"  zh:"是否查询回收站 1是2否"`
		Keyword         string   `json:"keyword" form:"keyword" uri:"keyword" zh:"模糊搜索"`
		SearchCondition []string `json:"search_condition" form:"search_condition" uri:"search_condition"  zh:"搜索条件"`

		TimeoutReceiverId []string `json:"timeout_receiver_id" form:"timeout_receiver_id" uri:"timeout_receiver_id"` // 超期邮件通知抄送人ID
		TimeoutFrequency  int      `json:"timeout_frequency" form:"timeout_frequency" uri:"timeout_frequency"`       //超期邮件发送频率（1-每天一次，2-三天一次，3-每周一次，4-每月一次，5-三月一次）
		DataRange         int      `json:"data_range" form:"data_range" uri:"data_range"  zh:"数据范围 1未处理 2已处理 3回收站"`
	}

	ComplianceEmailData struct {
		Ip            string   `json:"ip"`             // IP地址
		RuleName      string   `json:"rule_name"`      // 合规规则名称
		BusinessNames []string `json:"business_names"` // 业务系统名称
	}

	// QueryRule 查询规则结构体
	QueryRule struct {
		RuleName  string `json:"rule_name"`
		Whether   string `json:"whether"`
		Content   string `json:"content"`
		Condition string `json:"condition"`
	}
)

// CreateHistory 创建合规检测流转历史记录
func CreateHistory(ctx *gin.Context, user *user.User, param *OneComplianceHistory, staffObj *staff.Staff, oldStatus int, operations ...string) error {
	if _, ok := poc.PocStatusRelations[param.Status]; !ok {
		return errors.New("状态不存在")
	}

	complianceHistory := compliance_risks.ComplianceRisksHistory{
		RecordId:      param.RecordId,
		FromUserId:    user.Id,
		FromUserName:  user.Username,
		LimitDate:     param.LimitDate,
		SendNotice:    SetOtherOne(param.SendNotice),
		TimeoutNotice: SetOtherOne(param.TimeoutNotice),
		Description:   param.Descrition,
		ToCc:          param.ToCc,
		NewStatus:     param.Status,
		Status:        oldStatus,
		Category:      "operation",
	}

	if len(operations) <= 0 || operations[0] == "upload_file" {
		fileName, err := HandleFile(ctx)
		if err != nil {
			return err
		}
		complianceHistory.UploadFile = fileName
	}

	if staffObj != nil {
		complianceHistory.ToStaffId = staffObj.Id
		complianceHistory.ToStaffName = staffObj.Name
	}

	if param.Status == poc.PocStatusOfReRepairing {
		if param.ExecNow == 1 {
			complianceHistory.Operation = "复测立即执行"
		} else {
			complianceHistory.Operation = "复测指定时间" + param.LimitDate
		}
	}
	if len(operations) > 0 {
		complianceHistory.Operation = strings.Join(operations, ",")
	}

	ch := compliance_risks.NewComplianceRisksHistoryModel()
	err := ch.Create(&complianceHistory)
	if err != nil {
		return err
	}

	return nil
}

// CreateComplianceTask 创建定时任务
func CreateComplianceTask(taskType int, user *user.User, param *SomeComplianceHistory, staffObj *staff.Staff) error {
	// 创建定时任务
	layout := "2006-01-02 15:04:05"
	if param.LimitDate == "" {
		param.LimitDate = time.Now().Format(layout)
	}
	limitDate, err := time.ParseInLocation(layout, param.LimitDate, time.Local)
	if err != nil {
		return err
	}
	task := threat_tasks.ThreatTask{
		UserId:            user.Id,
		LimitDate:         &limitDate,
		TimeoutNotice:     param.TimeoutNotice,
		PocId:             param.RecordIds[0],
		PocIds:            strings.Join(param.RecordIds, ","),
		Status:            0,
		TaskType:          taskType,
		Genre:             threat_tasks.GenreOne,
		TimeoutReceiverId: strings.Join(param.TimeoutReceiverId, ","),
		TimeoutFrequency:  param.TimeoutFrequency,
	}
	if staffObj != nil && len(staffObj.Email) > 0 {
		task.ToStaffId = staffObj.Id
		task.ToStaffEmail = staffObj.Email[0]
		task.ToCc = param.ToCc
	}

	tt := threat_tasks.NewThreatTaskModel()
	err = tt.Create(&task)
	if err != nil {
		return err
	}
	return nil
}

// UpdateComplianceStatus 更新合规检测流转状态
func UpdateComplianceStatus(param *OneComplianceHistory, complianceObj map[string]interface{}, staffObj *staff.Staff, ccStaff *staff.Staff) error {
	status := cast.ToString(complianceObj["flow_status"])
	newStatus := strconv.Itoa(param.Status)
	personFid := cast.ToString(complianceObj["person_fid"])
	newPersonFid := ""
	if staffObj != nil {
		newPersonFid = staffObj.FidHash
	}
	if status == newStatus {
		if newPersonFid != "" && newPersonFid == personFid {
			return nil
		}
	}

	PushComplianceQueue(staffObj, newStatus, complianceObj, ccStaff)

	return nil
}

const ComplianceUpdateQueueName = "fobrain:compliance:update"

func PushComplianceQueue(staffObj *staff.Staff, newStatus string, complianceObj map[string]interface{}, ccStaff *staff.Staff) error {
	client := redis.GetRedisClient()
	rq := &queue.RedisQueue{Client: client}
	if staffObj == nil {
		staffObj = &staff.Staff{}
	}
	if ccStaff == nil {
		ccStaff = &staff.Staff{}
	}
	limitDate := ""
	if complianceObj["limit_date"] != nil {
		limitDate = cast.ToString(complianceObj["limit_date"])
	}
	obj := []map[string]interface{}{{"id": complianceObj["id"], "flow_status": newStatus, "staff_fid": staffObj.Fid, "cc_staff_fid": ccStaff.Fid, "limit_date": limitDate}}
	// 使用合规检测专用队列名称
	count := rq.Push(ComplianceUpdateQueueName, obj)

	logs.GetLogger().Infow("PushComplianceQueue", "count", count, "obj", obj)
	return nil
}

// SendNotice 发送邮件通知
func SendNotice(param *SomeComplianceHistory, staffObj *staff.Staff) error {
	mail := ""
	if len(staffObj.Email) > 0 {
		mail = staffObj.Email[0]
	}

	// 查询合规检测记录
	complianceRecords, err := GetComplianceRecordsByIds(param.RecordIds)
	if err != nil {
		return err
	}

	complianceEmailData := []ComplianceEmailData{}
	for _, v := range complianceRecords {
		businessNames := []string{}
		for _, b := range v.Business {
			businessNames = append(businessNames, b.System)
		}
		complianceEmailData = append(complianceEmailData, ComplianceEmailData{
			Ip:            v.Ip,
			RuleName:      "合规检测规则", // 根据实际需要获取规则名称
			BusinessNames: businessNames,
		})
	}

	logs.GetLogger().Infow("SendNotice ", "email", mail)
	if param.SendNotice == 1 && mail != "" {
		title, content := GetEmailContent(param.Status, staffObj.Name, len(param.RecordIds), complianceEmailData)
		cc := []string{}
		if param.ToCc != "" {
			// 抄送
			ccs := strings.Split(param.ToCc, ",")
			ccEmails, err := GetStaffsEmails(ccs)
			if err == nil {
				for _, v := range ccEmails {
					if len(v) > 0 && strings.Contains(v, "@") {
						cc = append(cc, v)
					}
				}
			}
		}
		cc = utils.ListDistinct(cc)
		logs.GetLogger().Infow("SendNotice", "cc", cc)

		// 发送邮件
		err := email.NewEmail().CommonSend([]string{mail}, cc, title, content)
		if err != nil {
			fmt.Println("邮件发送失败: ", err.Error())
			return err
		} else {
			fmt.Println("邮件发送成功")
		}
	}
	return nil
}

func GetEmailContent(status int, name string, num int, complianceEmailData []ComplianceEmailData) (title, content string) {
	t := time.Now()
	layout := "2006-01-02 15:04:05"
	timeStr := t.Format(layout)
	n := fmt.Sprintf("%d", num)
	switch status {
	case poc.PocStatusOfBeRepair:
		title = "『合规检测修复提醒』"
		content = "您好，" + name + "<br /><span style=\"color:red\">" + timeStr + "</span> 有" + n + "个合规检测问题派发给您，请登录系统进行查看"
	case poc.PocStatusOfForward:
		title = "『合规检测修复提醒』"
		content = "您好，" + name + "<br /><span style=\"color:red\">" + timeStr + "</span> 有" + n + "个合规检测问题转交给您，请登录系统进行查看"
	case poc.PocStatusOfTimeout:
		title = "『合规检测修复超时提醒』"
		content = "您好，" + name + "<br />有" + n + "个合规检测问题超时未修复，请登录系统进行查看"
	}

	if len(complianceEmailData) > 0 {
		// 组装邮件 Html表格数据
		tableHeader := "<table border='1' cellspacing='0' cellpadding='5' style='border-collapse: collapse; margin-top: 15px;'>\n"
		tableHeader += "<tr style='background-color: #f2f2f2;'>\n"
		tableHeader += "<th>序号</th>\n"
		tableHeader += "<th>IP地址</th>\n"
		tableHeader += "<th>合规规则</th>\n"
		tableHeader += "<th>业务系统</th>\n"
		tableHeader += "</tr>\n"

		tableBody := ""
		for i, item := range complianceEmailData[:min(len(complianceEmailData), 10)] {
			// 将业务系统名称数组转换为字符串，用逗号分隔
			businessNames := strings.Join(item.BusinessNames, ", ")

			tableBody += "<tr>\n"
			tableBody += fmt.Sprintf("<td>%d</td>\n", i+1)
			tableBody += fmt.Sprintf("<td>%s</td>\n", item.Ip)
			tableBody += fmt.Sprintf("<td>%s</td>\n", item.RuleName)
			tableBody += fmt.Sprintf("<td>%s</td>\n", businessNames)
			tableBody += "</tr>\n"
		}

		tableFooter := "</table>"

		// 将表格添加到内容中
		content += "<br/><br/>合规检测详情如下(前十条)：<br/>" + tableHeader + tableBody + tableFooter
	}
	return
}

// SendWebhookMsg 发送webhook消息
func SendWebhookMsg(param *OneComplianceHistory, staffObj *staff.Staff, user *user.User, complianceObjs []map[string]interface{}) error {
	event, content := threat_history.GenRiskWebhookMsg(staffObj, param.Status, len(complianceObjs), "合规风险")
	if event == "" {
		return nil
	}
	hook := make([]webhook.VulMsgDetails, 0)
	complianceId := param.RecordId
	limitDate := param.LimitDate
	for _, p := range complianceObjs {
		if id, exists := p["id"]; exists {
			complianceId = id.(string)
		}
		if lDate, exists := p["limit_date"]; exists {
			switch v := lDate.(type) {
			case string:
				limitDate = v
			case *localtime.Time:
				if v != nil {
					limitDate = v.Format(utils.DateTimeLayout)
				} else {
					limitDate = ""
				}
			}
		}
		hook = append(hook, webhook.VulMsgDetails{
			VulnerabilityId:        complianceId,
			VulnerabilityName:      "合规检测问题-" + cast.ToString(p["ip"]),
			RepairDeadline:         limitDate,
			RepairPriority:         "中",
			ReceiverPersons:        []string{staffObj.Name},
			ReceiverThirdUsernames: []string{staffObj.SsoName},
			ReceiverThirdUserId:    []string{staffObj.SsoId},
			ReceiverUsers: []webhook.ReceiverUser{
				{
					Id:      staffObj.Id,
					SsoId:   staffObj.SsoId,
					Fid:     staffObj.Fid,
					FidHash: staffObj.FidHash,
				},
			},
			DispatchTime: time.Now().Format(utils.DateTimeLayout),
		})
	}
	vulMsg := &webhook.VulnerabilityMsg{
		Event:      event,
		Title:      content,
		OpName:     user.Username,
		BusinessId: param.RecordId,
		Details:    hook,
	}
	return vulMsg.SendMsg()
}

// SendLocalNotice 发送本地通知(站内信)
func SendLocalNotice(complianceObj map[string]interface{}, desc string) error {
	// 发送站内信
	notice := &workbench.NotifyAlarmCenter{
		MsgType:         workbench.MsgTypeAbnormal,
		MsgContent:      "合规检测问题-" + cast.ToString(complianceObj["ip"]) + "-" + cast.ToString(complianceObj["person_name"]) + "  " + desc,
		MsgSource:       "compliance_urge",
		RelationType:    "",
		RelationContent: "",
		Remark:          desc,
	}

	return workbench.NewNotifyAlarmCenter().Create(notice)
}

// SendLocalMsgNotice 发送本地消息通知(站内信)
func SendLocalMsgNotice(adminName string, userId uint64, staffFidHash string, num int, operationName string) error {
	// 发送站内信
	notice := &workbench.NotifyAlarmCenter{
		MsgType:         workbench.MsgTypeRemind,
		MsgContent:      "管理员" + adminName + operationName + "给您" + fmt.Sprintf("%d", num) + "条合规检测问题",
		MsgSource:       workbench.MsgSouceVulnerabilityRemind,
		RelationType:    "",
		RelationContent: "",
		Remark:          "",
		Read:            false,
		UserId:          userId,
		StaffId:         staffFidHash,
	}

	return workbench.NewNotifyAlarmCenter().Create(notice)
}

func GetStaffsEmails(staffId []string) ([]string, error) {
	boolQuery := elastic.NewBoolQuery()
	boolQuery = boolQuery.Must(elastic.NewTermsQueryFromStrings("id", staffId...))

	result, err := es.GetEsClient().Search(staff.NewStaff().IndexName()).
		Size(10000).
		FetchSource(false).
		DocvalueField("email").
		Query(boolQuery).
		Do(context.TODO())

	if err != nil {
		return nil, err
	}

	emails := make([]string, 0)
	if result.TotalHits() > 0 {
		for _, hit := range result.Hits.Hits {
			// 获取字段值
			email, _ := hit.Fields.Strings("email")
			emails = append(emails, email...)
		}
	}
	return emails, nil
}

// HandleFile 文件上传处理
func HandleFile(ctx *gin.Context) (string, error) {
	contentType := ctx.Request.Header.Get("Content-Type")
	if !strings.Contains(contentType, "multipart/form-data") {
		return "", nil
	}
	file, err := ctx.FormFile("file")
	if err != nil && err.Error() != "http: no such file" {
		return "", err
	}
	if file != nil {
		if CheckFileType(file.Filename) == false {
			return "", errors.New(fmt.Errorf("invalid file type: %s", file.Filename).Error())
		}
		batch := time.Now().Format("20060102150405")
		baseDir := "../storage/compliance_history/"
		dst := filepath.Join(baseDir, batch+"_"+file.Filename)
		if err := ctx.SaveUploadedFile(file, dst); err != nil {
			return "", err
		}
		return dst, nil
	}
	return "", nil
}

// CheckFileType 检查文件类型
func CheckFileType(fileName string) bool {
	fileType := getFileType(fileName)
	if fileType == "unknown" {
		return false
	}
	return true
}

// getFileType 获取文件类型
func getFileType(fileName string) string {
	// 将文件名转换为小写
	fileName = strings.ToLower(fileName)

	// 根据文件名后缀判断文件类型
	if strings.HasSuffix(fileName, ".doc") {
		return "doc"
	} else if strings.HasSuffix(fileName, ".docx") {
		return "docx"
	} else if strings.HasSuffix(fileName, ".pdf") {
		return "pdf"
	} else if strings.HasSuffix(fileName, ".jpg") || strings.HasSuffix(fileName, ".jpeg") {
		return "jpeg"
	} else if strings.HasSuffix(fileName, ".png") {
		return "png"
	} else {
		return "unknown"
	}
}

// SetOtherOne 设置其他状态为1
func SetOtherOne(status int) int {
	if status == 0 {
		return 0
	}
	return 1
}

func GetComplianceRecordsByIds(ids []string) ([]*compliance_monitor.ComplianceMonitorTaskRecords, error) {
	boolQuery := elastic.NewBoolQuery()
	boolQuery = boolQuery.Must(elastic.NewTermsQueryFromStrings("id", ids...))

	result, err := es.GetEsClient().Search(compliance_monitor.NewComplianceMonitorTaskRecords().IndexName()).
		Size(10000).
		Query(boolQuery).
		Do(context.TODO())

	if err != nil {
		return nil, err
	}

	records := make([]*compliance_monitor.ComplianceMonitorTaskRecords, 0)
	if result.TotalHits() > 0 {
		for _, hit := range result.Hits.Hits {
			record := &compliance_monitor.ComplianceMonitorTaskRecords{}
			err := json.Unmarshal(hit.Source, record)
			if err != nil {
				continue
			}
			records = append(records, record)
		}
	}
	return records, nil
}
