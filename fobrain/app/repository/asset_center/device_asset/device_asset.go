package device

import (
	"context"
	"errors"
	"fmt"
	"fobrain/models/elastic/assets"
	"strings"
	"sync"
	"time"

	"fobrain/fobrain/app/services/network_area"
	"fobrain/fobrain/app/services/permission"
	"fobrain/models/mysql/custom_column"
	"fobrain/models/mysql/data_source"

	"go-micro.dev/v4/logger"

	filtrate "fobrain/models/elastic"

	"fobrain/initialize/es"
	logs "fobrain/mergeService/utils/log"
	"fobrain/models/elastic/device"
	"fobrain/pkg/utils"

	"github.com/gin-gonic/gin"
	"github.com/olivere/elastic/v7"
)

var wg sync.WaitGroup

// 查询并保存ecxcel文件
func GenerateExcel(c *gin.Context, ids []string, keyword string, params map[string]any, isSuperManage bool, staffIds []string) (string, error) {
	logs := logs.GetLogger("service")
	boolQuery := elastic.NewBoolQuery() //构建一个 Elasticsearch 的布尔查询
	// 权限相关的查询条件
	permissionQuery, err := permission.NewPermissionService().ProcessElasticsearchQuery(c, data_source.SourceTypeDevice)
	if err != nil {
		return "", err
	}
	if permissionQuery != nil {
		boolQuery = boolQuery.Must(permissionQuery)
	}
	//用于组合多个查询的弹性搜索查询类型，允许你使用 Must（必须匹配）、Should（至少匹配一个）和 MustNot（必须不匹配）等子句。
	if len(utils.CompactStrings(ids)) > 0 {
		// 使用 Must 子句和 NewTermsQueryFromStrings 创建一个查询条件，这个条件将搜索字段 id 中匹配给定列表的文档。
		boolQuery = boolQuery.Must(elastic.NewTermsQueryFromStrings("id", ids...))
	} else {
		boolQuery = boolQuery.Must(elastic.NewMatchAllQuery()) //如果 ids 列表为空，则使用 Must 子句添加一个 NewMatchAllQuery，即匹配所有文档。
	}

	if keyword != "" {
		boolQuery = device.NewDeviceModel().NewKeywordQuery(keyword, boolQuery)
	}

	searchCondition := params["search_condition"].([]string)
	if len(searchCondition) > 0 && searchCondition[0] != "" {
		conditions, err := filtrate.ParseQueryConditions(searchCondition)
		if err != nil {
			logger.Errorf("解析查询条件失败GenerateExcel err：%s", err.Error())
			return "", err
		}
		for _, condition := range conditions {
			boolQuery = filtrate.BuildBoolQuery(condition.Field, condition.OperationTypeString, condition.LogicalConnective, condition.Value, boolQuery)
		}
	}

	// scroll滚动分页查询
	deviceList, err := es.All[device.Device](1000, boolQuery, []elastic.Sorter{elastic.NewFieldSort("id")},
		"source_ids", "hostname", "sn", "mac", "os", "machine_room", "private_ip", "public_ip", "area", "created_at", "updated_at", "custom_fields", "opers", "business", "all_source_ids", "fid", "tag", "poc_num")
	if err != nil {
		logs.Errorf("读取 device 索引中的数据失败：%s", err.Error())
		return "", err
	}

	// 获取数据源
	sources, _, _ := data_source.AllSources()
	// 获取Area
	networkAreas, err := network_area.GetNetWorkAreaList()
	// 获取自定义字段
	customFields, err := custom_column.NewCustomFieldMetaModel().GetByModuleType(custom_column.CustomFieldMetaModuleTypeDevice, false)
	if err != nil {
		return "", err
	}

	// 查询结果格式转换并保存到datum
	var datum = make([][]interface{}, 0)
	dataChan := make(chan []interface{}, 0)

	maxWorkers := 20
	semaphore := make(chan struct{}, maxWorkers)

	go func() {
		timeout := time.After(10 * time.Minute) // 设置10分钟超时
		for {
			select {
			case data, ok := <-dataChan:
				if !ok {
					logs.Infof("Excel数据准备完成，共收集 %d 条记录", len(datum))
					return
				}
				datum = append(datum, data)
			case <-timeout:
				logs.Errorf("数据收集超时，已收集 %d 条记录，预期 %d 条", len(datum), len(deviceList))
				return
			}
		}
	}()

	for i, dev := range deviceList {
		// 获取信号量，限制并发
		wg.Add(1)
		semaphore <- struct{}{}

		go func(devicedata *device.Device, t int) {
			defer func() {
				<-semaphore
				wg.Done()
			}()
			deviceSource := make([]string, 0)
			for _, sourceId := range devicedata.AllSourceIds {
				for _, source := range sources {
					if sourceId == source.Id {
						deviceSource = append(deviceSource, source.Name)
						break
					}
				}
			}
			deviceArea := make([]string, 0)
			for _, networkAreaId := range devicedata.Area {
				for _, networkArea := range networkAreas {
					if networkAreaId == int(networkArea.Id) {
						deviceArea = append(deviceArea, networkArea.Name)
						break
					}
				}
			}
			dataChan <- tostring(*devicedata, deviceSource, deviceArea, customFields)
		}(dev, i)
	}

	wg.Wait()
	close(dataChan)
	header := []string{
		"数据源",
		"唯一值",
		"设备名称",
		"SN",
		"MAC地址",
		"标签",
		"漏洞数量",
		"操作系统",
		"机房",
		"关联内网IP",
		"关联互联网IP",
		"所属区域",
		"业务系统",
		"业务系统负责人",
		"业务部门",
		"运维负责人",
		"运维部门",
		"首次上报时间",
		"最后上报时间",
	}
	// 添加自定义字段名称
	for _, customField := range customFields {
		header = append(header, customField.DisplayName)
	}
	//生成 Excel 文件
	filePath := createxcel(header, datum)

	return filePath, nil
}

// 生成 Excel 文件
func createxcel(header []string, datum [][]interface{}) string {
	filePath := time.Now().Format("20060102150405") + "设备列表.xlsx"
	utils.WriterExcel(filePath,
		header,
		datum,
	)
	return filePath
}

// 查询结果格式转换并保存到datum
func tostring(device device.Device, deviceSource, deviceArea []string, customFields []*custom_column.CustomFieldMeta) []interface{} {
	var businessName, businessPerson, businessDepartment, operDepartment, opers []string
	for _, o := range device.Opers {
		a := assets.NewAssets()
		a.OperInfo = []*assets.PersonBase{
			o.OperInfo,
		}
		t := a.OperStringSlice()
		opers = append(opers, t...)
		if o.OperDepartment != nil {
			operDepartment = append(operDepartment, o.OperDepartment.Name)
		}
	}

	for _, business := range device.Business {
		if business == nil || business.Business == nil {
			continue
		}
		b := business.Business
		businessName = append(businessName, b.System)
		for _, v := range b.PersonBase {
			businessPerson = append(businessPerson, v.Name)
		}
		for _, v := range b.DepartmentBase {
			businessDepartment = append(businessDepartment, v.Name)
		}
	}
	data := []interface{}{
		strings.Join(deviceSource, ", "),
		device.Fid,
		strings.Join(device.HostName, ", "),
		strings.Join(device.Sn, ", "),
		strings.Join(device.Mac, ", "),
		strings.Join(device.Tags, ", "),
		device.PocNum,
		strings.Join(device.Os, ", "),
		strings.Join(device.MachineRoom, ", "),
		strings.Join(device.PrivateIp, ", "),
		strings.Join(device.PublicIp, ", "),
		strings.Join(deviceArea, ", "),
		strings.Join(utils.ListDistinct(businessName), ", "),
		strings.Join(utils.ListDistinct(businessPerson), ", "),
		strings.Join(utils.ListDistinct(businessDepartment), ", "),
		strings.Join(utils.ListDistinct(opers), ", "),
		strings.Join(utils.ListDistinct(operDepartment), ", "),
		device.CreatedAt,
		device.UpdatedAt,
	}
	// 添加自定义字段值
	for _, customField := range customFields {
		if _, ok := device.CustomFields[customField.FieldKey]; ok {
			data = append(data, device.CustomFields[customField.FieldKey])
		} else {
			data = append(data, "")
		}
	}
	return data
}

// Show retrieves a specific device asset by its ID.
func Show(id string) (any, error) {
	query := elastic.NewBoolQuery().Must(elastic.NewTermQuery("id", id))
	result, err := es.GetEsClient().Search(device.NewDeviceModel().IndexName()).Query(query).Do(context.TODO())
	if err != nil {
		return nil, err
	}
	if len(result.Hits.Hits) == 0 {
		return nil, errors.New("设备资产数据不存在")
	}

	return utils.ParseSampleHash(result.Hits.Hits[0], device.NewDeviceModel()), nil
}

func GetById(id string) (*device.Device, error) {
	query := elastic.NewBoolQuery().Must(elastic.NewTermQuery("id", id))
	result, err := es.GetEsClient().Search(device.NewDeviceModel().IndexName()).Query(query).Do(context.TODO())
	if err != nil {
		return nil, err
	}
	if len(result.Hits.Hits) == 0 {
		return nil, errors.New("设备资产数据不存在")
	}
	device, err := device.ConvertToDeviceModel(result.Hits.Hits[0])
	if err != nil {
		return nil, err
	}
	return device, nil
}

func DeleteDeviceByIps(ips []string) error {
	query := elastic.NewBoolQuery().Must(elastic.NewTermsQueryFromStrings("private_ip", ips...))
	_, err := es.GetEsClient().DeleteByQuery(device.NewDeviceModel().IndexName()).Query(query).Refresh("true").Do(context.TODO())
	if err != nil {
		return err
	}
	return nil
}

// AggCountByDepartment 根据部门聚合统计设备数量
func AggCountByDepartment(ctx context.Context) (map[string]int64, error) {
	// 构建基础查询，排除已删除的设备
	query := assets.NewAssets().GenQueryNoDeletedAndPurged()

	// 使用脚本将两个字段合并后去重
	script := elastic.NewScript(`
		def result = new HashSet();
		if (doc.containsKey('business_department_ids') && !doc['business_department_ids'].empty) {
			result.addAll(doc['business_department_ids']);
		}
		if (doc.containsKey('oper_department_ids') && !doc['oper_department_ids'].empty) {
			result.addAll(doc['oper_department_ids']);
		}
		return result;
	`)

	// 构建聚合查询
	agg := elastic.NewTermsAggregation().
		Script(script).
		Size(10000) // 设置足够大的size以获取所有部门

	// 执行搜索查询
	res, err := es.GetEsClient().Search().
		Index(device.NewDeviceModel().IndexName()).
		Query(query).
		Size(0).
		Aggregation("department_counts", agg).
		Do(ctx)

	if err != nil {
		return nil, err
	}

	// 获取聚合结果
	departmentCounts, found := res.Aggregations.Terms("department_counts")
	if !found {
		return make(map[string]int64), nil
	}

	// 构建返回结果
	counts := make(map[string]int64)
	for _, bucket := range departmentCounts.Buckets {
		// 因为是数字类型，需要转换处理
		departmentId := fmt.Sprint(bucket.Key)
		counts[departmentId] = bucket.DocCount
	}

	return counts, nil
}
