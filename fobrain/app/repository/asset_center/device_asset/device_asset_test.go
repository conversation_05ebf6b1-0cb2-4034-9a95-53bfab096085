package device

import (
	"fmt"
	"fobrain/fobrain/app/services/network_area"
	"fobrain/fobrain/common/localtime"
	"fobrain/fobrain/logs"
	"fobrain/initialize/mysql"
	"fobrain/models/elastic/assets"
	"fobrain/models/elastic/device"
	"fobrain/models/mysql/custom_column"
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/network_areas"
	"fobrain/pkg/utils"
	"fobrain/pkg/utils/common_logs"
	"sync"
	"testing"
	"time"

	"go.uber.org/zap"

	testcommon "fobrain/fobrain/tests/common_test"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/olivere/elastic/v7"
	"github.com/stretchr/testify/assert"
)

// 初始化mock server
var mockServer *testcommon.MockServer

func TestMain(m *testing.M) {
	mockServer = testcommon.NewMockServer()
	defer mockServer.Close()
	m.Run()
}

func TestGenerateExcel(t *testing.T) {
	patches := []*gomonkey.Patches{
		gomonkey.ApplyFunc(logs.GetLogger, func() *common_logs.Logger {
			return &common_logs.Logger{
				Logger: &zap.Logger{},
				Err:    nil,
			}
		}),
		gomonkey.ApplyFuncReturn(data_source.AllSources, []*data_source.Source{
			&data_source.Source{
				BaseModel: mysql.BaseModel{Id: uint64(1)},
			},
		}, nil, nil),
	}
	patches = append(patches, gomonkey.ApplyFuncReturn(network_area.GetNetWorkAreaList, []*network_areas.NetworkArea{
		&network_areas.NetworkArea{
			BaseModel: mysql.BaseModel{Id: uint64(1)},
			Name:      "example_area",
		},
	}, nil))
	patches = append(patches, gomonkey.ApplyMethodReturn(&custom_column.CustomFieldMeta{}, "GetByModuleType", []*custom_column.CustomFieldMeta{
		{
			FieldKey:    "custom_key",
			DisplayName: "自定义字段",
		},
	}, nil))
	mockServer := testcommon.NewMockServer()

	mockServer.Register("device/_search", []*elastic.SearchHit{
		{
			Id:     "1",
			Source: []byte(`{"id": "1", "host_name": ["hostname1"], "sn": ["sn1"], "mac": ["mac1"], "ip": ["ip1"], "private_ip": ["privateIp1"], "public_ip": ["publicIp1"], "machine_room": ["machineRoom1"], "os": ["os1"], "created_at": "2021-11-11 11:11:11", "updated_at": "2021-11-11 11:11:11", "source_ids": [1], "area": [1]}`),
		},
	})
	mockServer.RegisterEmptyScrollHandler()
	params := map[string]any{
		"operation_type_string": "in",
		"source_ids":            []uint64{1},
		"device_names":          []string{""},
		"device_types":          []string{""},
		"sn":                    []string{""},
		"mac":                   []string{""},
		"os":                    []string{""},
		"machine_room":          []string{""},
		"private_ip":            []string{""},
		"public_ip":             []string{""},
		"search_condition":      []string{""},
		"area":                  []uint64{1},
		"oper":                  []string{""},
		"created_at":            []string{"2021-11-11 11:11:11"},
		"updated_at":            []string{"2021-11-11 11:11:11"},
	}
	staffIds := []string{""}
	_, err := GenerateExcel(nil, make([]string, 0), "", params, false, staffIds)
	for _, patch := range patches {
		patch.Reset()
	}
	mockServer.Close()
	fmt.Println("!!!!!", err == nil)
	assert.Nil(t, err)
}
func TestCreateExcel(t *testing.T) {
	patch := gomonkey.ApplyFuncReturn(utils.WriterExcel, "path", nil)
	createxcel([]string{}, [][]interface{}{})
	patch.Reset()
}
func TestShow(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()
	// 模拟 Elasticsearch 的搜索响应
	mockServer.Register("/device/_search", []*elastic.SearchHit{
		{
			Id:     "1",
			Source: []byte(`{"id":"1","name":"example"}`),
		},
	})

	result, err := Show("1")
	assert.Nil(t, err)
	assert.NotNil(t, result)
}

func Test_tostring(t *testing.T) {
	now := time.Now()

	// 模拟 device.Device
	dev := device.Device{
		Fid:         "test-fid",
		Tags:        []string{"tag1", "tag2"},
		PocNum:      10,
		HostName:    []string{"host1"},
		Sn:          []string{"SN123"},
		Mac:         []string{"00:11:22:33:44:55"},
		Os:          []string{"Linux"},
		MachineRoom: []string{"RoomA"},
		PrivateIp:   []string{"***********"},
		PublicIp:    []string{"*******"},
		CustomFields: map[string]string{
			"field1": "value1",
			// "field2" 没有填，模拟缺失字段情况
		},
		CreatedAt: localtime.NewLocalTime(now),
		UpdatedAt: localtime.NewLocalTime(now),
		Opers: []*device.OperInfo{
			{
				OperInfo: &assets.PersonBase{
					Id:   "g3r",
					Name: "Admin",
				},
			},
		},
		Business: []*device.Business{}, // 空业务系统信息
	}

	deviceSource := []string{"sourceA"}
	deviceArea := []string{"Area1", "Area2"}

	customFields := []*custom_column.CustomFieldMeta{
		{FieldKey: "field1"},
		{FieldKey: "field2"},
	}

	got := tostring(dev, deviceSource, deviceArea, customFields)

	// 根据实际 tostring 函数的实现调整预期结果
	expected := []interface{}{
		"sourceA",                   // deviceSource (strings.Join)
		"test-fid",                  // Fid
		"host1",                     // HostName (strings.Join)
		"SN123",                     // Sn (strings.Join)
		"00:11:22:33:44:55",         // Mac (strings.Join)
		"tag1, tag2",                // Tags
		int64(10),                   // PocNum
		"Linux",                     // Os (strings.Join)
		"RoomA",                     // MachineRoom (strings.Join)
		"***********",               // PrivateIp (strings.Join)
		"*******",                   // PublicIp (strings.Join)
		"Area1, Area2",              // deviceArea (strings.Join)
		"",                          // businessName (空，因为没有业务系统)
		"",                          // businessPerson (空，因为没有业务系统)
		"",                          // businessDepartment (空，因为没有业务系统)
		"Admin",                     // opers (通过 OperStringSlice 获取)
		"",                          // operDepartment (空，因为 OperDepartment 为 nil)
		localtime.NewLocalTime(now), // CreatedAt
		localtime.NewLocalTime(now), // UpdatedAt
	}
	expected = append(expected, "value1", "") // Custom Fields

	// 验证返回结果的长度
	assert.Equal(t, len(expected), len(got), "返回结果长度不匹配")

	// 逐个验证每个字段
	for i, expectedVal := range expected {
		if i < len(got) {
			assert.Equal(t, expectedVal, got[i], "第 %d 个字段不匹配", i)
		}
	}
}

// TestConcurrentProcessing 测试修复后的并发处理逻辑
func TestConcurrentProcessing(t *testing.T) {
	t.Run("测试通道缓冲区动态调整", func(t *testing.T) {
		// 模拟设备列表
		deviceCount := 1500

		// 根据设备数量动态设置缓冲区大小
		bufferSize := deviceCount
		if bufferSize < 1000 {
			bufferSize = 1000
		}

		dataChan := make(chan []interface{}, bufferSize)

		// 验证缓冲区大小足够
		assert.GreaterOrEqual(t, cap(dataChan), deviceCount)
		assert.GreaterOrEqual(t, cap(dataChan), 1000)
	})

	t.Run("测试并发安全处理", func(t *testing.T) {
		var wg sync.WaitGroup
		const numWorkers = 10
		const numTasks = 100

		// 创建足够大的缓冲通道
		dataChan := make(chan []interface{}, numTasks)
		semaphore := make(chan struct{}, numWorkers)

		// 启动多个 goroutine 模拟并发处理
		for i := 0; i < numTasks; i++ {
			semaphore <- struct{}{}
			wg.Add(1)

			go func(taskId int) {
				defer func() {
					if r := recover(); r != nil {
						t.Errorf("Goroutine %d panicked: %v", taskId, r)
					}
					wg.Done()
					<-semaphore
				}()

				// 模拟处理工作
				time.Sleep(time.Millisecond * 10)

				// 使用 select 防止阻塞
				select {
				case dataChan <- []interface{}{taskId, "test_data"}:
					// 成功发送
				default:
					t.Errorf("Channel blocked for task %d", taskId)
				}
			}(i)
		}

		// 等待所有任务完成
		go func() {
			wg.Wait()
			close(dataChan)
		}()

		// 收集结果
		var results [][]interface{}
		timeout := time.After(5 * time.Second)

		for {
			select {
			case data, ok := <-dataChan:
				if !ok {
					// 通道关闭，完成收集
					goto complete
				}
				results = append(results, data)
			case <-timeout:
				t.Error("测试超时")
				goto complete
			}
		}

	complete:
		assert.Equal(t, numTasks, len(results), "应该收集到所有任务的结果")
	})

	t.Run("测试超时机制", func(t *testing.T) {
		dataChan := make(chan []interface{}, 10)

		// 模拟一个会超时的场景
		timeout := time.After(100 * time.Millisecond)
		collected := 0

		// 启动一个 goroutine 向通道发送数据，但延迟较长
		go func() {
			time.Sleep(200 * time.Millisecond) // 延迟超过超时时间
			dataChan <- []interface{}{"delayed_data"}
			close(dataChan)
		}()

		// 收集数据，应该会超时
		for {
			select {
			case data, ok := <-dataChan:
				if !ok {
					goto timeoutComplete
				}
				collected++
				_ = data
			case <-timeout:
				// 预期会超时
				goto timeoutComplete
			}
		}

	timeoutComplete:
		// 验证超时机制正常工作
		assert.Equal(t, 0, collected, "超时时不应该收集到数据")
	})
}

// TestDataChannelCapacity 测试数据通道容量计算
func TestDataChannelCapacity(t *testing.T) {
	tests := []struct {
		name        string
		deviceCount int
		expectedCap int
		description string
	}{
		{
			name:        "少量设备",
			deviceCount: 500,
			expectedCap: 1000,
			description: "设备数少于1000时，应使用1000作为缓冲区大小",
		},
		{
			name:        "大量设备",
			deviceCount: 2000,
			expectedCap: 2000,
			description: "设备数超过1000时，应使用设备数作为缓冲区大小",
		},
		{
			name:        "零设备",
			deviceCount: 0,
			expectedCap: 1000,
			description: "无设备时，应使用最小缓冲区大小1000",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 模拟缓冲区大小计算逻辑
			bufferSize := tt.deviceCount
			if bufferSize < 1000 {
				bufferSize = 1000
			}

			assert.Equal(t, tt.expectedCap, bufferSize, tt.description)
		})
	}
}

// TestGoroutineLeakPrevention 测试 goroutine 泄漏防护
func TestGoroutineLeakPrevention(t *testing.T) {
	var wg sync.WaitGroup
	const numWorkers = 5

	panicCount := 0

	for i := 0; i < numWorkers; i++ {
		wg.Add(1)

		go func(workerId int) {
			defer func() {
				if r := recover(); r != nil {
					panicCount++
					t.Logf("Worker %d recovered from panic: %v", workerId, r)
				}
				wg.Done()
			}()

			// 模拟可能发生 panic 的情况
			if workerId == 2 {
				panic("模拟异常情况")
			}

			// 正常处理
			time.Sleep(10 * time.Millisecond)
		}(i)
	}

	// 等待所有 goroutine 完成
	done := make(chan bool)
	go func() {
		wg.Wait()
		done <- true
	}()

	// 设置超时
	select {
	case <-done:
		// 正常完成
		assert.Equal(t, 1, panicCount, "应该捕获到一个panic")
	case <-time.After(1 * time.Second):
		t.Error("等待 goroutine 完成超时，可能存在泄漏")
	}
}

// TestProducerConsumerPattern 测试生产者-消费者模式的正确性
func TestProducerConsumerPattern(t *testing.T) {
	t.Run("验证所有数据都被正确收集", func(t *testing.T) {
		const numTasks = 100
		var wg sync.WaitGroup

		// 创建足够大的缓冲通道
		dataChan := make(chan []interface{}, numTasks)
		semaphore := make(chan struct{}, 10) // 限制并发数

		// 生产者：启动多个 goroutine 生产数据
		for i := 0; i < numTasks; i++ {
			semaphore <- struct{}{}
			wg.Add(1)

			go func(taskId int) {
				defer func() {
					if r := recover(); r != nil {
						t.Errorf("Producer %d panicked: %v", taskId, r)
					}
					wg.Done()
					<-semaphore
				}()

				// 模拟处理工作
				time.Sleep(time.Millisecond * 5)

				// 直接发送数据（因为缓冲区足够大）
				dataChan <- []interface{}{taskId, "data"}
			}(i)
		}

		// 等待所有生产者完成，然后关闭通道
		go func() {
			wg.Wait()
			close(dataChan)
		}()

		// 消费者：收集所有数据
		var results [][]interface{}
		for data := range dataChan {
			results = append(results, data)
		}

		// 验证所有数据都被收集到
		assert.Equal(t, numTasks, len(results), "应该收集到所有任务的数据")

		// 验证没有重复数据
		taskIds := make(map[int]bool)
		for _, result := range results {
			taskId := result[0].(int)
			assert.False(t, taskIds[taskId], "不应该有重复的任务ID: %d", taskId)
			taskIds[taskId] = true
		}
	})

	t.Run("测试缓冲区大小不足的情况", func(t *testing.T) {
		const numTasks = 10
		const bufferSize = 5 // 故意设置较小的缓冲区
		var wg sync.WaitGroup

		dataChan := make(chan []interface{}, bufferSize)

		// 记录是否有 goroutine 被阻塞
		completed := make(chan int, numTasks)

		// 启动生产者
		for i := 0; i < numTasks; i++ {
			wg.Add(1)
			go func(taskId int) {
				defer wg.Done()

				// 模拟一些处理时间
				time.Sleep(time.Millisecond * 10)

				// 这里可能会阻塞，因为缓冲区较小
				dataChan <- []interface{}{taskId}
				completed <- taskId
			}(i)
		}

		// 等待一小段时间，让生产者开始工作
		time.Sleep(50 * time.Millisecond)

		// 开始消费数据
		var results [][]interface{}
		go func() {
			for data := range dataChan {
				results = append(results, data)
				// 模拟消费处理时间
				time.Sleep(time.Millisecond * 5)
			}
		}()

		// 等待所有生产者完成
		wg.Wait()
		close(dataChan)

		// 等待消费完成
		time.Sleep(100 * time.Millisecond)

		// 验证结果
		assert.Equal(t, numTasks, len(results), "即使缓冲区较小，也应该收集到所有数据")
	})
}
