package staff

import (
	"context"
	"encoding/json"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"

	"github.com/olivere/elastic/v7"
	"github.com/stretchr/testify/assert"

	testcommon "fobrain/fobrain/tests/common_test"
)

func TestList(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		mockServer := testcommon.NewMockServer()
		defer mockServer.Close()
		mockServer.Register("staff/_search", []*elastic.SearchHit{
			{
				Id:     "1",
				Source: []byte(`{"id": 1, "name":"example", "fid":"L8iz:18088203184", "fid_hash": "aceb52c83d8855d6f34a224550e0d567"}`),
			},
		})

		mockServer.Register("staff/_count", &elastic.CountResponse{
			Count: 1,
		})

		mockDb := testcommon.GetMysqlMock()
		defer mockDb.Close()

		mockDb.ExpectQuery("SELECT * FROM `data_sources` WHERE id in (?)").
			WithArgs(sqlmock.AnyArg()).
			WillReturnRows(sqlmock.NewRows([]string{""}))

		mockDb.ExpectQuery("SELECT count(*) FROM `users_staffs` WHERE staff_id = ?").
			WithArgs("aceb52c83d8855d6f34a224550e0d567").
			WillReturnRows(sqlmock.NewRows([]string{"id"}))

		_, _, err := List("a", 1, 1, map[string]any{
			"operation_type_string": "in",
			"name":                  []string{"a"},
			"en_name":               []string{"a"},
			"title":                 []string{"a"},
			"mobile":                []string{"a"},
			"email":                 []string{"a"},
			"department":            []string{"a"},
			"status":                []int{1},
			"source_ids":            []uint64{1},
			"search_condition":      []string{""},
			"order":                 "ascend",
			"filed":                 "name",
		}, false, []string{"a"}, []string{"a"})
		assert.Nil(t, err)
	})
}

func TestDeleteByIds1(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	mockServer.Register("/staff/_search", []*elastic.SearchHit{
		{
			Id:     "1",
			Source: []byte(`{"name":"example"}`),
		},
	})
	mockServer.RegisterEmptyScrollHandler()

	mockServer.Register("/process_staff/_delete_by_query", &elastic.BulkIndexByScrollResponse{
		Deleted: 1,
	})
	mockServer.Register("/staff_record/_delete_by_query", &elastic.BulkIndexByScrollResponse{
		Deleted: 1,
	})
	mockServer.Register("/staff_merge_record/_delete_by_query", &elastic.BulkIndexByScrollResponse{
		Deleted: 1,
	})
	mockServer.Register("/staff/_delete_by_query", &elastic.BulkIndexByScrollResponse{
		Deleted: 1,
	})
	err := DeleteByIds([]string{"a"}, "a", []string{
		`{"name":["aa"],"operation_type_string":"in","condition":"or"}`,
	})
	mockServer.Close()
	assert.Nil(t, err)
}

func TestDeleteByIds2(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	mockServer.Register("/staff/_search", []*elastic.SearchHit{
		{
			Id:     "1",
			Source: []byte(`{"name":"example"}`),
		},
	})
	mockServer.RegisterEmptyScrollHandler()
	mockServer.Register("/staff/_delete_by_query", &elastic.BulkIndexByScrollResponse{
		Deleted: 1,
	})
	mockServer.Register("/process_staff/_delete_by_query", &elastic.BulkIndexByScrollResponse{
		Deleted: 1,
	})
	mockServer.Register("/staff_record/_delete_by_query", &elastic.BulkIndexByScrollResponse{
		Deleted: 1,
	})
	mockServer.Register("/staff_merge_record/_delete_by_query", &elastic.BulkIndexByScrollResponse{
		Deleted: 1,
	})
	mockServer.Register("/staff/_delete_by_query", &elastic.BulkIndexByScrollResponse{
		Deleted: 1,
	})
	err := DeleteByIds([]string{}, "a", []string{})
	mockServer.Close()
	assert.Nil(t, err)
}

func TestCompactList(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	mockServer.Register("staff/_search", []*elastic.SearchHit{
		{
			Id:     "1",
			Source: []byte(`{"id": 1, "name":"example"}`),
		},
	})
	_, _ = CompactList(1, 1)
	mockServer.Close()
}

func TestPosition(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	buckets := []*elastic.AggregationBucketKeyItem{
		&elastic.AggregationBucketKeyItem{
			Key: "a",
		},
	}
	bucketKeyItems := &elastic.AggregationBucketKeyItems{
		Buckets: buckets,
	}
	jsonBucketKeyItems, _ := json.Marshal(bucketKeyItems)
	mockServer.Register("staff/_search", elastic.SearchResult{
		Aggregations: map[string]json.RawMessage{
			"title": json.RawMessage([]byte(jsonBucketKeyItems)),
		},
	})
	c := context.Context(context.Background())
	_, err := Position(c)
	mockServer.Close()
	assert.Nil(t, err)
}

func TestStringInArray1(t *testing.T) {
	assert.True(t, stringInArray("a", []string{"a"}))
}
func TestStringInArray2(t *testing.T) {
	assert.False(t, stringInArray("", []string{"a"}))
}

func TestFindBy(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()
	mockServer.Register("staff/_search", []*elastic.SearchHit{
		{
			Id:     "1",
			Source: []byte(`{"id": 1, "fid": "123", "fid_hash": "123", "name":"example"}`),
		},
	})

	res, err := FindBy("original_ids", "123")
	assert.Nil(t, err)
	assert.Equal(t, res["fid"], "123")
	assert.Equal(t, res["fid_hash"], "123")
	assert.Equal(t, res["name"], "example")
}

func TestGetById(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()
	mockServer.Register("staff/_search", []*elastic.SearchHit{
		{
			Id:     "1",
			Source: []byte(`{"id": "1", "fid": "123", "fid_hash": "123", "name":"example"}`),
		},
	})
	staff, err := GetById("1")
	assert.Nil(t, err)
	assert.Equal(t, staff.Name, "example")
}
