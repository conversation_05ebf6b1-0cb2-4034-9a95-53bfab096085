package staff

import (
	"context"
	"encoding/json"
	"fmt"
	"fobrain/fobrain/logs"
	"fobrain/models/mysql/user_staff"
	"regexp"

	"fobrain/initialize/es"
	filtrate "fobrain/models/elastic"
	"fobrain/models/elastic/staff"
	"fobrain/pkg/utils"

	pkgerror "github.com/pkg/errors"

	"github.com/olivere/elastic/v7"
	"go-micro.dev/v4/logger"

	pgidservice "fobrain/services/people_pgid"
)

func List(keyword string, page, perPage int, params map[string]any, isSuperManage bool, staffIds []string, includeIds []string) (any, int64, error) {
	boolQuery := elastic.NewBoolQuery()
	if keyword != "" {
		boolQuery = staff.NewStaff().NewKeywordQuery(keyword, boolQuery)
	}

	searchCondition, _ := params["search_condition"].([]string)
	if len(searchCondition) > 0 {
		conditions, err := filtrate.ParseQueryConditions(searchCondition)
		if err != nil {
			logger.Errorf("staff List parse query condition error:%s", err.Error())
		}
		for _, condition := range conditions {
			boolQuery = filtrate.BuildBoolQuery(condition.Field, condition.OperationTypeString, condition.LogicalConnective, condition.Value, boolQuery)
		}
	}

	eq := es.GetEsClient().Search(staff.NewStaff().IndexName())

	// 获取总记录数
	total, err := es.GetCount(staff.NewStaff().IndexName(), boolQuery)
	if err != nil {
		return nil, 0, err
	}
	if total == 0 {
		return []any{}, 0, nil
	}

	sortBool := true
	if params["order"] != "" && params["order"].(string) == "ascend" {
		sortBool = false
	}

	field, ok := params["field"].(string)
	if !ok { //兼容前端没有传field
		field = ""
	}

	if field != "" {
		eq = eq.Sort(field, sortBool)
		eq = eq.Sort("id", false)
	} else {
		eq = eq.Sort("updated_at", false)
		eq = eq.Sort("id", false)
	}

	result, err := eq.From(es.GetFrom(page, perPage)).
		//Sort(fieldStr, sortBool).
		Size(es.GetSize(perPage)).
		Query(boolQuery).
		Do(context.TODO())

	if err != nil {
		return nil, total, err
	}

	allHits := result.Hits.Hits

	// 获取id列表
	includeIds = utils.ListDistinctNonZero(includeIds)
	if len(includeIds) > 0 {
		idsQuery := elastic.NewBoolQuery().Must(elastic.NewTermsQueryFromStrings("id", includeIds...))
		hits, err := es.GetEsClient().Search(staff.NewStaff().IndexName()).Query(idsQuery).Do(context.TODO())
		if err != nil {
			return nil, total, err
		}
		// 所有查询结果统一处理
		allHits = append(allHits, hits.Hits.Hits...)
	}

	// 处理结果
	// 使用id去重
	idMap := make(map[string]bool)
	list := make([]any, 0)
	for _, hit := range allHits {
		sampleHash := utils.ParseSampleHash(hit, staff.NewStaff())
		// 去重
		if _, ok := idMap[sampleHash["id"].(string)]; ok {
			continue
		}
		idMap[sampleHash["id"].(string)] = true
		// 通过 staff_id 在 users_staffs 表中查询对应的 user_id，并处理多种可能的情况：
		// 1.没有账号：通过 staff_id 未查找到 user_id 数据
		// 2.有账号：通过 staff_id 找到了 user_id 数据
		// hash_account: 1、无账号，2、有账号
		// todo： 目前因为数据结构原因，只可以查出：无账号和有账号（可能是独立账号、关联账号）
		fieHash := sampleHash["fid_hash"].(string)

		count := int64(0)
		if err = user_staff.NewUserRoleModel().Where("staff_id = ?", fieHash).Count(&count).Error; err != nil {
			return nil, total, err
		}

		// 通过 staff_id 找不到 user_id：无账号
		if count == 0 {
			sampleHash["has_account"] = 1
		} else {
			// 有账号
			sampleHash["has_account"] = 2
		}

		// pgid
		pid := sampleHash["id"].(string)
		pgid, _ := pgidservice.GetPgidById(pid)
		sampleHash["pgid"] = pgid

		mobile, _ := sampleHash["mobile"].(string)
		if mobile != "" {
			re := regexp.MustCompile(`[a-zA-Z]`)
			if re.MatchString(mobile) {
				sampleHash["mobile"] = ""
			}
		}

		list = append(list, sampleHash)
	}

	return list, total, nil
}

// DeleteByIds
// 按照ID删除人员信息
func DeleteByIds(ids []string, keyword string, searchCondition []string) error {
	boolQuery := elastic.NewBoolQuery()

	if keyword != "" {
		boolQuery = staff.NewStaff().NewKeywordQuery(keyword, boolQuery)
	}

	if len(searchCondition) > 0 {
		conditions, err := filtrate.ParseQueryConditions(searchCondition)
		if err != nil {
			logger.Errorf("DeleteByIds staff  parse query condition error:%s", err.Error())
		}
		for _, condition := range conditions {
			boolQuery = filtrate.BuildBoolQuery(condition.Field, condition.OperationTypeString, condition.LogicalConnective, condition.Value, boolQuery)
		}
	}

	if len(utils.CompactStrings(ids)) > 0 {
		boolQuery = boolQuery.Must(elastic.NewTermsQueryFromStrings("id", ids...))
	} else {
		boolQuery = boolQuery.Must(elastic.NewMatchAllQuery())
	}

	staffIndexName := staff.NewStaff().IndexName()
	// 查询涉及到的process_ids
	processIdsResult, err := es.All[staff.Staff](1000, boolQuery, nil, "id", "all_process_ids")
	if err != nil {
		return err
	}
	processIds := make([]string, 0)
	staffIds := make([]string, 0)
	for _, pi := range processIdsResult {
		processIds = append(processIds, pi.AllProcessIds...)
		staffIds = append(staffIds, pi.Id)
	}
	processIds = utils.ListDistinctNonZero(processIds)
	staffIds = utils.ListDistinctNonZero(staffIds)

	// 执行删除查询
	// 删除过程表
	_, err = es.GetEsClient().DeleteByQuery().Index(staff.NewProcessStaffModel().IndexName()).ScrollSize(1000).Query(elastic.NewTermsQueryFromStrings("id", processIds...)).Do(context.TODO())
	if err != nil {
		logs.GetLogger().Errorf("删除人员过程索引错误, error:%s", err.Error())
		return err
	}
	// 删除人员记录表
	_, err = es.GetEsClient().DeleteByQuery().Index(staff.NewStaffRecord().IndexName()).ScrollSize(1000).Query(elastic.NewTermsQueryFromStrings("staff.id", staffIds...)).Refresh("true").Do(context.TODO())
	if err != nil {
		logs.GetLogger().Errorf("删除人员记录索引错误, error:%s", err.Error())
		return err
	}

	// 删除人员融合记录表
	_, err = es.GetEsClient().DeleteByQuery().Index(staff.NewMergeRecordsModel().IndexName()).ScrollSize(1000).Query(elastic.NewTermsQueryFromStrings("staff_id", staffIds...)).Do(context.TODO())
	if err != nil {
		logs.GetLogger().Errorf("删除人员融合记录索引错误, error:%s", err.Error())
		return err
	}
	// 删除人员表
	_, err = es.GetEsClient().DeleteByQuery().Index(staffIndexName).ScrollSize(1000).Query(boolQuery).Refresh("true").Do(context.TODO())
	if err != nil {
		logs.GetLogger().Errorf("删除人员索引错误, error:%s", err.Error())
		return err
	}
	go staff.NewStaff().CacheAllStaff(true)
	return nil
}

// CompactList
// 人员列表 - 仅返回ID和Name，手机号掩码返回
// @param page int 页码 默认为 1
// @param perPage int 每页数量 默认为50
func CompactList(page, perPage int) ([]any, int64) {
	boolQuery := elastic.NewBoolQuery()

	result, err := es.GetEsClient().Search(staff.NewStaff().IndexName()).From(es.GetFrom(page, perPage)).
		Sort("updated_at", false).Size(es.GetSize(perPage)).Query(boolQuery).
		FetchSourceContext(elastic.NewFetchSourceContext(true).Include("fid_hash", "name", "mobile", "department", "id")).
		Do(context.TODO())
	if err != nil {
		return []any{}, 0
	}

	list := make([]any, 0)
	for _, hit := range result.Hits.Hits {
		var data struct {
			Id         string   `json:"id"`
			Fid        string   `json:"fid_hash"`
			Name       string   `json:"name"`
			Department []string `json:"department"`
			Mobile     string   `json:"mobile"`
			Pgid       string   `json:"pgid"`
		}

		json.Unmarshal(hit.Source, &data)
		// 手机号掩码返回
		if len(data.Mobile) >= 10 {
			data.Mobile = data.Mobile[:3] + "****" + data.Mobile[7:]
		}
		// pgid
		data.Pgid, _ = pgidservice.GetPgidById(data.Id)
		list = append(list, data)
	}
	return list, result.TotalHits()

}

func Position(ctx context.Context) ([]string, error) {
	data := make([]string, 0)
	agg := elastic.NewTermsAggregation().Field("title").Size(1000000).ShardSize(1000000)
	searchResult, err := es.GetEsClient().Search().
		Index(staff.NewStaff().IndexName()).
		Aggregation("title", agg).
		Do(context.TODO())
	if err != nil {
		return nil, pkgerror.WithMessage(err, "es search failed")
	}
	aggResult, found := searchResult.Aggregations.Terms("title")
	if found {
		for _, bucket := range aggResult.Buckets {
			if !stringInArray(bucket.Key.(string), data) {
				data = append(data, bucket.Key.(string))
			}
		}
	} else {
		logger.Warn("No assets ip query results found.")
		return data, nil
	}

	return data, nil
}

func stringInArray(str string, arr []string) bool {
	for _, v := range arr {
		if v == str {
			return true
		}
	}
	return false
}

// FindBy
// @Summary 按照指定字段查询人员信息
func FindBy(key, val string) (map[string]any, error) {
	boolQuery := elastic.NewBoolQuery()
	boolQuery.Must(elastic.NewTermQuery(key, val))
	res, err := es.GetEsClient().Search(staff.NewStaff().IndexName()).Query(boolQuery).Size(1).Do(context.TODO())
	if err != nil {
		return nil, err
	}
	if len(res.Hits.Hits) == 0 {
		return nil, nil
	}

	hit := res.Hits.Hits[0]
	return utils.ParseSampleHash(hit, staff.NewStaff()), nil
}

func GetById(id string) (*staff.Staff, error) {
	boolQuery := elastic.NewBoolQuery()
	boolQuery.Must(elastic.NewTermQuery("_id", id))
	res, err := es.GetEsClient().Search(staff.NewStaff().IndexName()).Query(boolQuery).Size(1).Do(context.TODO())
	if err != nil {
		return nil, err
	}

	result, err := staff.ConvertToStaffsModel(res.Hits.Hits)
	if err != nil {
		return nil, err
	}
	if len(result) > 0 {
		return result[0], nil
	}
	return nil, fmt.Errorf("staff not found")
}
