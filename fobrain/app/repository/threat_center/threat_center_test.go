package threat_center

import (
	"os"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/stretchr/testify/assert"

	"fobrain/fobrain/app/request/asset_center"
	"fobrain/fobrain/app/services/task"
	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/initialize/mysql"
	"fobrain/models/mysql/poc_accessorys"
)

func TestCreate(t *testing.T) {
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()
	mockDb.ExpectQuery("SELECT * FROM `data_nodes` WHERE source_id = ? AND `data_nodes`.`deleted_at` IS NULL ORDER BY `data_nodes`.`id` LIMIT 1").
		WithArgs(6).WillReturnRows(mockDb.NewRows([]string{"id"}).AddRow(1))

	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyMethodReturn(task.NewSyncDataTask(), "Dispatch", nil).Reset()
	params := asset_center.ThreatCreateRequest{
		Area:         "1",
		Ip:           "127.0.0.1",
		Port:         80,
		Url:          "http://127.0.0.1/api/v1/test",
		Level:        1,
		Name:         "漏洞",
		VulType:      "漏洞",
		IsPoc:        1,
		HasExp:       1,
		HasPoc:       1,
		Status:       1,
		Describe:     "",
		Details:      "",
		Hazard:       "",
		Suggestions:  "",
		LastResponse: "",
		Cve:          "cve-1192837",
		Cnvd:         "",
		Cnnvd:        "",
	}
	err := Create(&params)
	assert.NoError(t, err)
}

func TestDelete(t *testing.T) {
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()
	mockDb.ExpectBegin()
	mockDb.ExpectExec("DELETE FROM `poc_accessorys` WHERE id = ?").
		WithArgs(sqlmock.AnyArg()).
		WillReturnResult(sqlmock.NewResult(1, 1))
	mockDb.ExpectCommit()

	listPatch := gomonkey.ApplyMethodReturn(poc_accessorys.NewPocAccessorysModel(), "List", []*poc_accessorys.PocAccessorys{
		{BaseModel: mysql.BaseModel{Id: 1}},
	}, int64(1), nil)
	defer listPatch.Reset()

	// mock os.Remove
	osRemovePatch := gomonkey.ApplyFuncReturn(os.Remove, nil)
	defer osRemovePatch.Reset()

	ids := []int{1, 2, 3}
	failedIds, err := Delete(ids)
	assert.NoError(t, err)
	assert.Equal(t, 0, len(failedIds))
}
