package threat_center

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"fobrain/models/mysql/compliance_accessorys"
	"mime/multipart"
	"os"
	"path"
	"path/filepath"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/olivere/elastic/v7"

	"fobrain/fobrain/app/request/asset_center"
	"fobrain/fobrain/app/services/task"
	"fobrain/fobrain/common/request"
	"fobrain/initialize/mysql"
	"fobrain/models/elastic/poc"
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/data_sync_task"
	"fobrain/models/mysql/poc_accessorys"
	"fobrain/pkg/cfg"
	"fobrain/pkg/utils"
)

type ThreatCreate struct {
	Area         string `json:"area" zh:"区域"`
	Ip           string `json:"ip"   zh:"IP"`
	Port         int    `json:"port" zh:"端口"`
	Url          string `json:"url"  zh:"URL"`
	Level        int    `json:"level"  zh:"漏洞等级"`
	Name         string `json:"common_title"  zh:"漏洞名称"`
	VulType      string `json:"vulType"       zh:"漏洞类型"`
	Cve          string `json:"cve"           zh:"cve编号"`
	Cnvd         string `json:"cnvd"  zh:"cnvd编号"`
	Cnnvd        string `json:"cnnvd" zh:"cnnvd编号"`
	HasExp       int    `json:"has_exp"  zh:"是否存在exp"`
	HasPoc       int    `json:"has_poc" zh:"是否存在poc"`
	Status       int    `json:"status"  zh:"状态"`
	Describe     string `json:"describe"  zh:"描述"`
	Details      string `json:"details" zh:"详情"`
	Hazard       string `json:"hazard"  zh:"危险性"`
	Suggestions  string `json:"suggestions"  zh:"修复建议"`
	LastResponse string `json:"last_response"  zh:"最后一次响应"`
	IsPoc        int    `json:"is_poc" zh:"是否poc漏洞"`
	OriginalId   string `json:"original_id"  zh:"original_id"`
}

func Create(params *asset_center.ThreatCreateRequest) error {
	threats := []ThreatCreate{
		{
			Area:         params.Area,
			Ip:           params.Ip,
			Port:         params.Port,
			Url:          params.Url,
			Level:        params.Level,
			Name:         params.Name,
			VulType:      params.VulType,
			Cve:          params.Cve,
			Cnvd:         params.Cnvd,
			Cnnvd:        params.Cnnvd,
			HasExp:       params.HasExp,
			HasPoc:       params.HasPoc,
			Status:       params.Status,
			Describe:     params.Describe,
			Details:      params.Details,
			Hazard:       params.Hazard,
			Suggestions:  params.Suggestions,
			LastResponse: params.LastResponse,
			IsPoc:        params.IsPoc,
			OriginalId:   params.OriginalId,
		},
	}
	marshal, err := json.Marshal(threats)
	if err != nil {
		return err
	}
	var handlers []mysql.HandleFunc
	handlers = append(handlers, mysql.WithWhere("source_id = ?", data_source.FileImportSourceId))
	first, err := data_source.NewNodeModel().First(handlers...)
	if err != nil {
		return err
	}
	err = task.NewSyncDataTask().Dispatch(first.Id, []int{2}, data_sync_task.SourceHandle, string(marshal), "")
	if err != nil {
		return err
	}
	return nil
}

func UploadPocFile(ctx *gin.Context, file *multipart.FileHeader, source int64, fids []string, uploadType int64) error {
	fileName := file.Filename
	fileExt := strings.ToLower(path.Ext(file.Filename)) // 文件后缀转为小写
	limitType := map[string]struct{}{
		".doc":  {},
		".docx": {},
		".png":  {},
		".jpg":  {},
		".jpeg": {},
	}

	if len(limitType) > 0 {
		_, ok := limitType[fileExt]
		if !ok {
			return errors.New("文件格式不在要求范围内")
		}
	}

	// 本地文件名
	localFileName := fmt.Sprintf("%s-%s", time.Now().Format("20060102150405"), file.Filename)

	// 将文件保存到服务器本地
	storagePath := cfg.LoadCommon().StoragePath
	//检查文件夹是否存在
	err := ensureDirExists(filepath.Join(storagePath, "/app/poc_upload/"))
	if err != nil {
		return err
	}

	if err = ctx.SaveUploadedFile(file, filepath.Join(storagePath, "/app/poc_upload/", localFileName)); err != nil {
		return err
	}
	for _, v := range fids {
		if uploadType == 1 {
			item := compliance_accessorys.ComplianceAccessorys{
				Path:               filepath.Join("/app/poc_upload/", localFileName),
				ComplianceRecordId: v,
				UserId:             request.GetUserId(ctx),
				Source:             source,
				Name:               fileName,
			}
			err = compliance_accessorys.NewComplianceAccessorys().CreateItem(&item)
			if err != nil {
				return err
			}
		}
		item := poc_accessorys.PocAccessorys{
			Path:   filepath.Join("/app/poc_upload/", localFileName),
			Fid:    v,
			UserId: request.GetUserId(ctx),
			Source: source,
			Name:   fileName,
		}
		err = poc_accessorys.NewPocAccessorysModel().CreateItem(&item)
		if err != nil {
			return err
		}
	}
	return nil
}

func ensureDirExists(path string) error {
	// 检查文件夹是否存在
	info, err := os.Stat(path)
	if os.IsNotExist(err) {
		// 文件夹不存在，创建它
		err = os.MkdirAll(path, os.ModePerm)
		if err != nil {
			return fmt.Errorf("failed to create directory: %w", err)
		}
	} else if err != nil {
		// 其他错误
		return fmt.Errorf("failed to check directory: %w", err)
	} else if !info.IsDir() {
		// 路径存在，但不是文件夹
		return fmt.Errorf("path exists but is not a directory: %s", path)
	} else {
		fmt.Printf("Directory already exists: %s\n", path)
	}
	return nil
}

// Accessory 根据提供的参数获取威胁附件列表
//
// 参数：
// params: 包含过滤条件的 ThreatAccessoryRequest 结构体指针
//
// 返回值：
// []asset_center.ThreatAccessoryResponse: 威胁附件列表，每个元素包含文件名、操作人、创建时间和文件路径
// int64: 威胁附件总数
// error: 如果获取过程中发生错误，则返回错误信息；否则返回 nil

func Accessory(params *asset_center.ThreatAccessoryRequest) ([]asset_center.ThreatAccessoryResponse, int64, error) {
	data := make([]asset_center.ThreatAccessoryResponse, 0)

	boolQuery := elastic.NewBoolQuery()
	boolQuery.Must(elastic.NewTermQuery("id", params.Id))
	searchResult, err := poc.NewPoc().GetClient().Search().
		Index(poc.NewPoc().IndexName()).
		Query(boolQuery).
		Do(context.Background())
	if err != nil {
		return nil, 0, err
	}
	if len(searchResult.Hits.Hits) == 0 {
		return nil, 0, err
	}
	var fid string
	for _, hit := range searchResult.Hits.Hits {
		pocSource := poc.Poc{}
		json.Unmarshal(hit.Source, &pocSource)
		fid = pocSource.Fid
	}

	list, total, err := poc_accessorys.NewPocAccessorysModel().UserPocAccessorysList(fid, params.Page, params.PerPage)
	if err != nil {
		return nil, 0, err
	}
	for _, accessorys := range list {
		//根据文件后缀判断文件类型
		fileExt := strings.ToLower(path.Ext(accessorys.Path))
		fileType := 0
		base64Img := ""
		if fileExt == ".png" || fileExt == ".jpg" || fileExt == ".jpeg" {
			fileType = 2
			storagePath := cfg.LoadCommon().StoragePath
			base64Img = utils.GetBase64Img(accessorys.Path, storagePath)
		}
		data = append(data, asset_center.ThreatAccessoryResponse{
			FileName:    accessorys.Name,
			Oper:        accessorys.UserName,
			CreatedTime: accessorys.CreatedAt,
			Id:          accessorys.Id,
			FileType:    int32(fileType),
			ImgBase64:   base64Img,
		})
	}
	return data, total, nil
}

func Info(id int) (poc_accessorys.PocAccessorys, error) {
	first, err := poc_accessorys.NewPocAccessorysModel().First(mysql.WithWhere("id = ?", id))
	if err != nil {
		return poc_accessorys.PocAccessorys{}, err
	}
	return first, nil
}

// 删除附件
// ids: 附件id列表
// 返回值：
// []uint64: 删除失败的附件id列表
// error: 如果删除过程中发生错误，则返回错误信息；否则返回 nil
func Delete(ids []int) ([]uint64, error) {
	failedIds := make([]uint64, 0)
	model := poc_accessorys.NewPocAccessorysModel()
	query := mysql.WithWhere("id in (?)", ids)
	dataList, _, err := model.List(0, 0, query)
	if err != nil {
		return failedIds, err
	}
	for _, item := range dataList {
		// 标记删除
		if err := model.Delete(mysql.WithWhere("id = ?", item.Id)); err != nil {
			failedIds = append(failedIds, item.Id)
			continue
		}
		// 删除文件
		storagePath := cfg.LoadCommon().StoragePath
		if err := os.Remove(filepath.Join(storagePath, item.Path)); err != nil {
			failedIds = append(failedIds, item.Id)
			continue
		}
	}
	return failedIds, nil
}
