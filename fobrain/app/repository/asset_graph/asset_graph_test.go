package asset_graph

import (
	"context"
	"encoding/json"
	"io"
	"reflect"
	"testing"
	"time"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/olivere/elastic/v7"
	"github.com/stretchr/testify/assert"

	req "fobrain/fobrain/app/request/asset_graph"
	resp "fobrain/fobrain/app/response/asset_graph"
	"fobrain/fobrain/app/services/statistical"
	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/initialize/mysql"
	pb "fobrain/mergeService/proto"
	business_system2 "fobrain/models/elastic/business_system"
	"fobrain/models/mysql/compliance_monitor"
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/network_areas"
)

var mockServer *testcommon.MockServer

func TestMain(m *testing.M) {
	mockServer = testcommon.NewMockServer()
	defer mockServer.Close()
	m.Run()
}

func TestAppendUniqueString(t *testing.T) {
	t.Run("append unique string", func(t *testing.T) {
		a := []string{"a", "b", "c"}
		uniqueString := AppendUniqueString(a, "d")
		assert.Equal(t, uniqueString, []string{"a", "b", "c", "d"})
	})
	t.Run("append unique string", func(t *testing.T) {
		a := []string{"a", "b", "c"}
		uniqueString := AppendUniqueString(a, "c")
		assert.Equal(t, uniqueString, []string{"a", "b", "c"})
	})
}

func TestGetComplianceRiskList(t *testing.T) {
	res := resp.AssetGraphResponse{
		Type: "ip",
		AssetGraphData: resp.AssetGraphData{
			Ip:             "",
			Id:             "",
			BasicInfo:      resp.BasicInfo{},
			Source:         nil,
			BusinessSystem: []*resp.BusinessSystem{},
			SecurityInfo: resp.SecurityInfo{
				SecurityInfoIp:       resp.SecurityInfoIp{},
				SecurityInfoBusiness: []*resp.SecurityInfoBusiness{},
			},
			ThreatRisk:      []*resp.ThreatRisk{},
			ThreatNum:       0,
			ComplianceNum:   0,
			ComplianceRisk:  []*resp.ComplianceRisk{},
			AssetMapping:    resp.AssetMapping{},
			DeviceRelevance: []*resp.DeviceRelevance{},
		},
	}
	mockServer.Register("/compliance_monitor_task_records/_search", elastic.SearchResult{
		TookInMillis: 1,
		TimedOut:     false,
		Shards: &elastic.ShardsInfo{
			Total:      5,
			Successful: 5,
			Skipped:    0,
			Failed:     0,
		},
		Hits: &elastic.SearchHits{
			TotalHits: &elastic.TotalHits{
				Value:    1,
				Relation: "eq",
			},
		},
		Aggregations: elastic.Aggregations{
			"complianceMonitorIds": json.RawMessage(`
				{
					"buckets": [
						{"key": "11", "doc_count": 100},
						{"key": "0", "doc_count": 100}
					]
				}
			`),
		},
	})
	list := []compliance_monitor.ComplianceMonitor{
		{
			BaseModel: mysql.BaseModel{
				Id: uint64(1),
			},
			Name: "test",
			Desc: "test",
		},
		{
			BaseModel: mysql.BaseModel{
				Id: uint64(2),
			},
			Name: "test1",
			Desc: "test",
		},
	}
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyMethodReturn(compliance_monitor.NewComplianceMonitorModel(), "List", list, int64(2), nil).Reset()
	getComplianceRiskList([]string{"127.0.0.1"}, &res)
}

func TestGetPocList(t *testing.T) {
	res := resp.AssetGraphResponse{
		Type: "ip",
		AssetGraphData: resp.AssetGraphData{
			Ip:             "",
			Id:             "",
			BasicInfo:      resp.BasicInfo{},
			Source:         nil,
			BusinessSystem: []*resp.BusinessSystem{},
			SecurityInfo: resp.SecurityInfo{
				SecurityInfoIp:       resp.SecurityInfoIp{},
				SecurityInfoBusiness: []*resp.SecurityInfoBusiness{},
			},
			ThreatRisk:      []*resp.ThreatRisk{},
			ThreatNum:       0,
			ComplianceNum:   0,
			ComplianceRisk:  []*resp.ComplianceRisk{},
			AssetMapping:    resp.AssetMapping{},
			DeviceRelevance: []*resp.DeviceRelevance{},
		},
	}
	mockData := `{"id":"42e3f882effd4fddb658b3958803354e","fid":"0:0:0:*************","fid_hash":"e67b92f33e80e774cb9812253ffb10a1","original_ids":["62f9b1e6845cea91454b6948f3e40eb2"],"original_ids_source":{"8-31":"62f9b1e6845cea91454b6948f3e40eb2"},"area":1,"process_ids":["31_1_62f9b1e6845cea91454b6948f3e40eb2"],"source_ids":[8],"node_ids":[31],"poc_task_ids":["96_31_1_62f9b1e6845cea91454b6948f3e40eb2"],"all_source_ids":[8],"all_node_ids":[31],"all_poc_task_ids":["96_31_1_62f9b1e6845cea91454b6948f3e40eb2"],"all_process_ids":["31_1_62f9b1e6845cea91454b6948f3e40eb2"],"ip":"*************","ip_source":null,"ip_type":1,"network_type":2,"port":0,"port_source":null,"is_poc":2,"is_poc_source":null,"url":"*************","url_source":null,"level":2,"level_source":null,"cve":"","cve_source":null,"cnvd":"","cnvd_source":null,"cnnvd":"","cnnvd_source":null,"has_exp":0,"has_exp_source":null,"has_poc":2,"has_poc_source":null,"status":0,"status_source":null,"person":null,"name":"Alibaba Nacos鉴权功能缺陷","name_source":null,"vulType":"配置不当","vulType_source":null,"describe":"","describe_source":null,"details":"","details_source":null,"hazard":"","hazard_source":null,"suggestions":"","suggestions_source":null,"last_response":"","last_response_source":null,"created_at":"2024-11-25 23:15:35","updated_at":"2024-11-27 11:36:04","merge_count":1,"person_limit":null,"person_limit_hash":null,"risk_num":10,"repair_priority":"p3"}`

	var mockResponse map[string]interface{}
	err := json.Unmarshal([]byte(mockData), &mockResponse)
	assert.NoError(t, err)

	// 模拟 BuildQuery 返回值
	mockScroll1 := &elastic.SearchResult{
		Hits: &elastic.SearchHits{
			TotalHits: &elastic.TotalHits{
				Value: 1,
			},
			Hits: []*elastic.SearchHit{
				{
					Source: json.RawMessage(mockData),
				},
			},
		},
	}
	// 第二轮返回空数据
	mockScroll2 := &elastic.SearchResult{
		Hits: &elastic.SearchHits{
			TotalHits: &elastic.TotalHits{
				Value: 0,
			},
			Hits: []*elastic.SearchHit{},
		},
	}
	// 模拟调用次数
	callCount := 0
	// 使用 gomonkey 模拟 `scroll.Do` 方法
	patches := gomonkey.ApplyMethod(reflect.TypeOf(&elastic.ScrollService{}), "Do", func(_ *elastic.ScrollService, ctx context.Context) (*elastic.SearchResult, error) {
		if callCount == 0 {
			callCount++
			return mockScroll1, nil // 第一次返回有效数据
		} else if callCount == 1 {
			callCount++
			return mockScroll2, io.EOF // 第二次返回 EOF，模拟循环结束
		}
		return nil, io.EOF
	})
	defer patches.Reset()
	getPocList([]string{"127.0.0.1"}, &res)
}

func TestGetBusinessMappingList(t *testing.T) {
	res := resp.AssetGraphResponse{
		Type: "ip",
		AssetGraphData: resp.AssetGraphData{
			Ip:             "",
			Id:             "",
			BasicInfo:      resp.BasicInfo{},
			Source:         nil,
			BusinessSystem: []*resp.BusinessSystem{},
			SecurityInfo: resp.SecurityInfo{
				SecurityInfoIp:       resp.SecurityInfoIp{},
				SecurityInfoBusiness: []*resp.SecurityInfoBusiness{},
			},
			ThreatRisk:      []*resp.ThreatRisk{},
			ThreatNum:       0,
			ComplianceNum:   0,
			ComplianceRisk:  []*resp.ComplianceRisk{},
			AssetMapping:    resp.AssetMapping{},
			DeviceRelevance: []*resp.DeviceRelevance{},
		},
	}
	mockData := `{"id":"95d83aefb2b04887b0129a0f6b77a265","fid":"***********:1","fid_hash":"a139d7d3a8fd72a00c252ff12b589918","area":1,"process_ids":["3_5_3_1_***********"],"source_ids":[1],"node_ids":[3],"asset_task_ids":["3_3_1_***********"],"all_source_ids":[1],"all_node_ids":[3],"all_asset_task_ids":["3_3_1_***********"],"all_process_ids":["3_5_3_1_***********"],"ip":"***********","ip_type":1,"ip_segment":null,"ip_segment_source":{"1-3":""},"hostname":null,"hostname_source":{"1-3":""},"eth_name":null,"eth_name_source":{"1-3":""},"os":null,"os_source":{"1-3":""},"kernel":null,"kernel_source":{"1-3":""},"model":null,"model_source":{"1-3":""},"maker":null,"maker_source":{"1-3":""},"sn":null,"sn_source":{"1-3":""},"mac":null,"mac_source":{"1-3":""},"product":null,"product_source":{"1-3":""},"business":[{"owner":"左佐","system":"支付系统编辑","business_info":null,"system_id":"","owner_id":"","source":"original","department":null,"addition":""}],"business_source":{"1-3":[{"owner":"左佐","system":"支付系统编辑","business_info":null,"system_id":"","owner_id":"","source":"original","department":null,"addition":""}]},"business_system_source":{},"business_owner_source":{},"oper":["左佐"],"oper_source":{"1-3":"左佐"},"machine_room":["天美国际资产中心"],"machine_room_source":{"1-3":"天美国际资产中心"},"status":1,"status_source":{"1-3":1},"ports":[{"protocol":"unknown","port":22,"domain":"","title":"","url":"","status":0},{"protocol":"unknown","port":80,"domain":"","title":"","url":"","status":0}],"ports_source":{"1-3":[{"protocol":"unknown","port":22,"domain":"","title":"","url":"","status":0},{"protocol":"unknown","port":80,"domain":"","title":"","url":"","status":0}]},"memory_size":null,"memory_size_source":{"1-3":""},"memory_usage_rate":null,"memory_usage_rate_source":{"1-3":""},"cpu_maker":null,"cpu_maker_source":{"1-3":""},"cpu_brand":null,"cpu_brand_source":{"1-3":""},"cpu_count":null,"cpu_count_source":{"1-3":0},"disk_count":null,"disk_count_source":{"1-3":0},"disk_size":null,"disk_size_source":{"1-3":0},"disk_usage_rate":null,"disk_usage_rate_source":{"1-3":""},"load_average":null,"load_average_source":{"1-3":""},"network_type":1,"fusion_rules":null,"deleted_at":null,"purged_at":null,"created_at":"2024-11-21 17:45:46","updated_at":"2024-11-22 16:30:24","is_device_extracted":1,"merge_count":2,"person_limit":null,"person_limit_hash":null,"tag":null}`

	var mockResponse map[string]interface{}
	err := json.Unmarshal([]byte(mockData), &mockResponse)
	assert.NoError(t, err)

	// 模拟 BuildQuery 返回值
	mockScroll1 := &elastic.SearchResult{
		Hits: &elastic.SearchHits{
			TotalHits: &elastic.TotalHits{
				Value: 1,
			},
			Hits: []*elastic.SearchHit{
				{
					Source: json.RawMessage(mockData),
				},
			},
		},
	}
	// 第二轮返回空数据
	mockScroll2 := &elastic.SearchResult{
		Hits: &elastic.SearchHits{
			TotalHits: &elastic.TotalHits{
				Value: 0,
			},
			Hits: []*elastic.SearchHit{},
		},
	}
	// 模拟调用次数
	callCount := 0
	// 使用 gomonkey 模拟 `scroll.Do` 方法
	patches := gomonkey.ApplyMethod(reflect.TypeOf(&elastic.ScrollService{}), "Do", func(_ *elastic.ScrollService, ctx context.Context) (*elastic.SearchResult, error) {
		if callCount == 0 {
			callCount++
			return mockScroll1, nil // 第一次返回有效数据
		} else if callCount == 1 {
			callCount++
			return mockScroll2, io.EOF // 第二次返回 EOF，模拟循环结束
		}
		return nil, io.EOF
	})
	defer patches.Reset()
	getBusinessMappingList([]string{"127.0.0.1"}, &res)
}

func TestGetAssetSourceIdsOrIps(t *testing.T) {
	mockServer.Register("/asset/_search", elastic.SearchResult{
		TookInMillis: 1,
		TimedOut:     false,
		Shards: &elastic.ShardsInfo{
			Total:      5,
			Successful: 5,
			Skipped:    0,
			Failed:     0,
		},
		Hits: &elastic.SearchHits{
			TotalHits: &elastic.TotalHits{
				Value:    1,
				Relation: "eq",
			},
		},
		Aggregations: elastic.Aggregations{
			"source_ids": json.RawMessage(`
				{
					"buckets": [
						{"key": "11", "doc_count": 100},
						{"key": "0", "doc_count": 100}
					]
				}
			`),
			"ipAgg": json.RawMessage(`
				{
					"buckets": [
						{"key": "127.0.0.1", "doc_count": 100},
						{"key": "*********", "doc_count": 100}
					]
				}
			`),
		},
	})
	ips, strings, err := getAssetSourceIdsOrIps("127.0.0.1", "")
	assert.NoError(t, err)
	assert.NotNil(t, ips)
	assert.NotNil(t, strings)
}

func TestStatisticalBusinessInfo(t *testing.T) {
	res := resp.AssetGraphResponse{
		Type: "ip",
		AssetGraphData: resp.AssetGraphData{
			Ip:             "",
			Id:             "",
			BasicInfo:      resp.BasicInfo{},
			Source:         nil,
			BusinessSystem: []*resp.BusinessSystem{},
			SecurityInfo: resp.SecurityInfo{
				SecurityInfoIp:       resp.SecurityInfoIp{},
				SecurityInfoBusiness: []*resp.SecurityInfoBusiness{},
			},
			ThreatRisk:      []*resp.ThreatRisk{},
			ThreatNum:       0,
			ComplianceNum:   0,
			ComplianceRisk:  []*resp.ComplianceRisk{},
			AssetMapping:    resp.AssetMapping{},
			DeviceRelevance: []*resp.DeviceRelevance{},
		},
	}
	list := []map[string]interface{}{
		{
			"value": int64(1),
			"name":  "CMDB覆盖率",
			"type":  "coverage",
			"field": "cmdb_coverage",
		},
	}
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyMethodReturn(statistical.NewService(), "GetCountByFields", list, nil).Reset()
	statisticalBusinessInfo("oa系统", &res)
}

func TestBusinessBasicInfo(t *testing.T) {
	res := resp.AssetGraphResponse{
		Type: "ip",
		AssetGraphData: resp.AssetGraphData{
			Ip:             "",
			Id:             "",
			BasicInfo:      resp.BasicInfo{},
			Source:         nil,
			BusinessSystem: []*resp.BusinessSystem{},
			SecurityInfo: resp.SecurityInfo{
				SecurityInfoIp:       resp.SecurityInfoIp{},
				SecurityInfoBusiness: []*resp.SecurityInfoBusiness{},
			},
			ThreatRisk:      []*resp.ThreatRisk{},
			ThreatNum:       0,
			ComplianceNum:   0,
			ComplianceRisk:  []*resp.ComplianceRisk{},
			AssetMapping:    resp.AssetMapping{},
			DeviceRelevance: []*resp.DeviceRelevance{},
		},
	}
	businessName := "aaa"
	i := new(int)
	*i = 1
	info := business_system2.BusinessSystems{
		AssetsAttribute: business_system2.AssetsAttribute{
			IsGj:           i,
			IsXc:           i,
			PurchaseType:   i,
			ImportantTypes: i,
			InsuranceLevel: i,
			OperatingEnv:   i,
			RunningState:   i,
		},
		SystemVersion: "V1.0.0",
	}
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyMethodReturn(business_system2.NewBusinessSystems(), "GetByName", &info, nil).Reset()
	businessBasicInfo(businessName, &res)
}

func TestOperDepartment(t *testing.T) {
	mockData := `{"id":"11","fid":"111","fid_hash":"111","name":"1111","english_name":null,"english_name_source":{"4-6":""},"area":0,"area_source":{"4-6":1},"title":null,"title_source":{"4-6":""},"mobile":"11","email":["<EMAIL>"],"email_source":{"4-6":"<EMAIL>"},"department":["北京华司/O部/中心"],"department_source":{"4-6":"北京华司/O部/中心"},"status":1,"status_source":{"4-6":1},"process_ids":["11"],"departments_ids":["42"],"all_departments_ids":["1","41","42"],"source_ids":[4],"node_ids":[6],"staff_task_ids":["11"],"all_source_ids":[4],"all_node_ids":[6],"all_staff_task_ids":["11"],"all_process_ids":["11"],"created_at":"2024-11-26 14:39:35","updated_at":"2024-11-26 14:46:05","merge_count":2}`

	mockServer.Register("/staff/_search", elastic.SearchResult{
		TookInMillis: 1,
		TimedOut:     false,
		Shards: &elastic.ShardsInfo{
			Total:      1,
			Successful: 1,
			Skipped:    0,
			Failed:     0,
		},
		Hits: &elastic.SearchHits{
			TotalHits: &elastic.TotalHits{
				Value:    1,
				Relation: "eq",
			},
			Hits: []*elastic.SearchHit{
				{
					Source: json.RawMessage(mockData),
				},
			},
		},
	})

	department := operDepartment("aa")
	assert.Equal(t, 1, len(department))
}

func TestGetDevice(t *testing.T) {
	res := resp.AssetGraphResponse{
		Type: "ip",
		AssetGraphData: resp.AssetGraphData{
			Ip:             "",
			Id:             "",
			BasicInfo:      resp.BasicInfo{},
			Source:         nil,
			BusinessSystem: []*resp.BusinessSystem{},
			SecurityInfo: resp.SecurityInfo{
				SecurityInfoIp:       resp.SecurityInfoIp{},
				SecurityInfoBusiness: []*resp.SecurityInfoBusiness{},
			},
			ThreatRisk:      []*resp.ThreatRisk{},
			ThreatNum:       0,
			ComplianceNum:   0,
			ComplianceRisk:  []*resp.ComplianceRisk{},
			AssetMapping:    resp.AssetMapping{},
			DeviceRelevance: []*resp.DeviceRelevance{},
		},
	}

	mockData := `{"hostname":["12345","67890"],"id":"sdfklafads"}`

	mockServer.Register("/device/_search", elastic.SearchResult{
		TookInMillis: 1,
		TimedOut:     false,
		Shards: &elastic.ShardsInfo{
			Total:      1,
			Successful: 1,
			Skipped:    0,
			Failed:     0,
		},
		Hits: &elastic.SearchHits{
			TotalHits: &elastic.TotalHits{
				Value:    1,
				Relation: "eq",
			},
			Hits: []*elastic.SearchHit{
				{
					Source: json.RawMessage(mockData),
				},
			},
		},
	})

	getDevice("127.0.0.1", &res)
}

func TestAssetGraphList(t *testing.T) {
	mockDb := testcommon.InitSqlMock()
	defer mockDb.Close()
	t.Run("ip", func(t *testing.T) {
		params := req.AssetGraphRequest{
			Type:     "ip",
			Ip:       "127.0.0.1",
			Business: "",
		}
		time.Sleep(time.Second)
		defer gomonkey.ApplyFuncReturn(getAssetSourceIdsOrIps, []uint64{1, 2, 3}, []string{"127.0.0.1"}, nil).Reset()
		dataSources := []*data_source.Source{
			{
				BaseModel: mysql.BaseModel{
					Id: 1,
				},
				Name:       "aa",
				Desc:       "",
				Icon:       "a/a/a",
				IsTaskSync: true,
			},
		}
		time.Sleep(time.Second)
		defer gomonkey.ApplyMethodReturn(data_source.NewSourceModel(), "SourceNames", dataSources).Reset()
		time.Sleep(time.Second)
		defer gomonkey.ApplyFunc(getPocList, func(ips []string, res *resp.AssetGraphResponse) {
			return
		}).Reset()
		time.Sleep(time.Second)
		defer gomonkey.ApplyFunc(getComplianceRiskList, func(ips []string, res *resp.AssetGraphResponse) {
			return
		}).Reset()
		time.Sleep(time.Second)
		defer gomonkey.ApplyFunc(statisticalIpInfo, func(ip string, res *resp.AssetGraphResponse) {
			return
		}).Reset()
		time.Sleep(time.Second)
		defer gomonkey.ApplyFunc(getDevice, func(ip string, res *resp.AssetGraphResponse) {
			return
		}).Reset()
		mockDb.ExpectQuery("SELECT `source_id` FROM `data_nodes`").
			WillReturnRows(mockDb.NewRows([]string{"source_id"}).
				AddRow(1).
				AddRow(2).
				AddRow(1)) // 包含重复 ID
		list, err := AssetGraphList(&params)
		assert.NoError(t, err)
		assert.NotNil(t, list)
	})
	t.Run("business", func(t *testing.T) {
		mockDb.ExpectQuery("SELECT `source_id` FROM `data_nodes`").
			WillReturnRows(mockDb.NewRows([]string{"source_id"}).
				AddRow(1).
				AddRow(2).
				AddRow(1)) // 包含重复 ID
		params := req.AssetGraphRequest{
			Type:     "business",
			Ip:       "",
			Business: "交易系统",
		}
		time.Sleep(time.Second)
		defer gomonkey.ApplyFuncReturn(getAssetSourceIdsOrIps, []uint64{1, 2, 3}, []string{"127.0.0.1"}, nil).Reset()
		dataSources := []*data_source.Source{
			{
				BaseModel: mysql.BaseModel{
					Id: 1,
				},
				Name:       "aa",
				Desc:       "",
				Icon:       "a/a/a",
				IsTaskSync: true,
			},
		}
		time.Sleep(time.Second)
		defer gomonkey.ApplyMethodReturn(data_source.NewSourceModel(), "SourceNames", dataSources).Reset()
		time.Sleep(time.Second)
		defer gomonkey.ApplyFunc(getPocList, func(ips []string, res *resp.AssetGraphResponse) {
			return
		}).Reset()
		time.Sleep(time.Second)
		defer gomonkey.ApplyFunc(getComplianceRiskList, func(ips []string, res *resp.AssetGraphResponse) {
			return
		}).Reset()
		time.Sleep(time.Second)
		defer gomonkey.ApplyFunc(getBusinessMappingList, func(ips []string, res *resp.AssetGraphResponse) {
			return
		}).Reset()
		time.Sleep(time.Second)
		defer gomonkey.ApplyFunc(statisticalBusinessInfo, func(businessApp string, res *resp.AssetGraphResponse) {
			return
		}).Reset()
		time.Sleep(time.Second)
		defer gomonkey.ApplyFunc(businessBasicInfo, func(businessApp string, res *resp.AssetGraphResponse) {
			return
		}).Reset()

		list, err := AssetGraphList(&params)
		assert.NoError(t, err)
		assert.NotNil(t, list)
	})
}

func TestStatisticalIpInfo(t *testing.T) {
	res := resp.AssetGraphResponse{
		Type: "ip",
		AssetGraphData: resp.AssetGraphData{
			Ip:             "",
			Id:             "",
			BasicInfo:      resp.BasicInfo{},
			Source:         nil,
			BusinessSystem: []*resp.BusinessSystem{},
			SecurityInfo: resp.SecurityInfo{
				SecurityInfoIp:       resp.SecurityInfoIp{},
				SecurityInfoBusiness: []*resp.SecurityInfoBusiness{},
			},
			ThreatRisk:      []*resp.ThreatRisk{},
			ThreatNum:       0,
			ComplianceNum:   0,
			ComplianceRisk:  []*resp.ComplianceRisk{},
			AssetMapping:    resp.AssetMapping{},
			DeviceRelevance: []*resp.DeviceRelevance{},
		},
	}
	mockData := `{"id":"95d83aefb2b04887b0129a0f6b77a265","fid":"***********:1","fid_hash":"a139d7d3a8fd72a00c252ff12b589918","area":1,"process_ids":["3_5_3_1_***********"],"source_ids":[1],"node_ids":[3],"asset_task_ids":["3_3_1_***********"],"all_source_ids":[1],"all_node_ids":[3],"all_asset_task_ids":["3_3_1_***********"],"all_process_ids":["3_5_3_1_***********"],"ip":"***********","ip_type":1,"ip_segment":null,"ip_segment_source":{"1-3":""},"hostname":null,"hostname_source":{"1-3":""},"eth_name":null,"eth_name_source":{"1-3":""},"os":null,"os_source":{"1-3":""},"kernel":null,"kernel_source":{"1-3":""},"model":null,"model_source":{"1-3":""},"maker":null,"maker_source":{"1-3":""},"sn":null,"sn_source":{"1-3":""},"mac":null,"mac_source":{"1-3":""},"product":null,"product_source":{"1-3":""},"business":[{"owner":"左佐","system":"支付系统编辑","business_info":null,"system_id":"","owner_id":"","source":"original","department":null,"addition":""}],"business_source":{"1-3":[{"owner":"左佐","system":"支付系统编辑","business_info":null,"system_id":"","owner_id":"","source":"original","department":null,"addition":""}]},"business_system_source":{},"business_owner_source":{},"oper":["左佐"],"oper_source":{"1-3":"左佐"},"machine_room":["天美国际资产中心"],"machine_room_source":{"1-3":"天美国际资产中心"},"status":1,"status_source":{"1-3":1},"ports":[{"protocol":"unknown","port":22,"domain":"","title":"","url":"","status":0},{"protocol":"unknown","port":80,"domain":"","title":"","url":"","status":0}],"ports_source":{"1-3":[{"protocol":"unknown","port":22,"domain":"","title":"","url":"","status":0},{"protocol":"unknown","port":80,"domain":"","title":"","url":"","status":0}]},"memory_size":null,"memory_size_source":{"1-3":""},"memory_usage_rate":null,"memory_usage_rate_source":{"1-3":""},"cpu_maker":null,"cpu_maker_source":{"1-3":""},"cpu_brand":null,"cpu_brand_source":{"1-3":""},"cpu_count":null,"cpu_count_source":{"1-3":0},"disk_count":null,"disk_count_source":{"1-3":0},"disk_size":null,"disk_size_source":{"1-3":0},"disk_usage_rate":null,"disk_usage_rate_source":{"1-3":""},"load_average":null,"load_average_source":{"1-3":""},"network_type":1,"fusion_rules":null,"deleted_at":null,"purged_at":null,"created_at":"2024-11-21 17:45:46","updated_at":"2024-11-22 16:30:24","is_device_extracted":1,"merge_count":2,"person_limit":null,"person_limit_hash":null,"tag":null}`

	mockServer.Register("/asset/_search", elastic.SearchResult{
		TookInMillis: 1,
		TimedOut:     false,
		Shards: &elastic.ShardsInfo{
			Total:      1,
			Successful: 1,
			Skipped:    0,
			Failed:     0,
		},
		Hits: &elastic.SearchHits{
			TotalHits: &elastic.TotalHits{
				Value:    1,
				Relation: "eq",
			},
			Hits: []*elastic.SearchHit{
				{
					Source: json.RawMessage(mockData),
				},
			},
		},
	})

	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyMethodReturn(network_areas.NewNetworkAreaModel(), "First", network_areas.NetworkArea{
		BaseModel: mysql.BaseModel{
			Id: 1,
		},
		Name: "aaa",
	}, nil).Reset()
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyMethodReturn(pb.GetProtoClient(), "IpSecurityInfo", nil, nil).Reset()

	statisticalIpInfo("127.0.0.1", &res)
}
