package asset_graph

import (
	"context"
	"encoding/json"
	"errors"
	"fobrain/fobrain/app/repository/asset"
	"fobrain/pkg/utils"
	pgidservice "fobrain/services/people_pgid"
	"io"
	"strconv"

	"github.com/olivere/elastic/v7"

	req "fobrain/fobrain/app/request/asset_graph"
	stat "fobrain/fobrain/app/request/statistical"
	resp "fobrain/fobrain/app/response/asset_graph"
	"fobrain/fobrain/app/services/asset_center/business_systems"
	"fobrain/fobrain/app/services/statistical"
	"fobrain/fobrain/logs"
	"fobrain/initialize/es"
	"fobrain/initialize/mysql"
	pb "fobrain/mergeService/proto"
	"fobrain/models/elastic/assets"
	business_system2 "fobrain/models/elastic/business_system"
	esComplianceMonitor "fobrain/models/elastic/compliance_monitor"
	"fobrain/models/elastic/device"
	"fobrain/models/elastic/poc"
	"fobrain/models/elastic/staff"
	"fobrain/models/mysql/compliance_monitor"
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/network_areas"
)

var logger = logs.GetLogger()

var coverage = map[string]int{
	"cmdb_coverage":       1,
	"host_coverage":       2,
	"uaudithost_coverage": 3,
	"detect_coverage":     4,
	"waf_coverage":        5,
	"oper_coverage":       6,
}

func AssetGraphList(params *req.AssetGraphRequest) (*resp.AssetGraphResponse, error) {
	ids, ips, err := getAssetSourceIdsOrIps(params.Ip, params.Business)
	if err != nil {
		logger.Errorf("Get asset source ids failed: %v", err.Error())
	}
	sources := make([]*resp.Source, 0)
	//获取添加过节点的数据源ID列表
	allExistsSourceIds, err := data_source.NewNodeModel().GetAllExistingSourceIds(true)
	if err != nil {
		return nil, err
	}
	names := data_source.NewSourceModel().SourceNames(ids)
	isTaskSync := 0
	for _, source := range names {
		sources = append(sources, &resp.Source{
			Id:   source.Id,
			Name: source.Name,
			Icon: source.Icon,
		})
		//有任务同步的数据源
		if isTaskSync == 0 && source.IsTaskSync == true {
			isTaskSync = 2
		}
		// 无任务同步的数据源 但添加过任务同步的数据源
		if isTaskSync != 2 && utils.ListContains(allExistsSourceIds, source.Id) {
			isTaskSync = 1
		}
	}

	res := resp.AssetGraphResponse{
		Type: params.Type,
		AssetGraphData: resp.AssetGraphData{
			Ip:             "",
			Id:             "",
			BasicInfo:      resp.BasicInfo{},
			Source:         sources,
			BusinessSystem: []*resp.BusinessSystem{},
			SecurityInfo: resp.SecurityInfo{
				SecurityInfoIp: resp.SecurityInfoIp{
					IsInitiative: int32(isTaskSync),
				},
				SecurityInfoBusiness: []*resp.SecurityInfoBusiness{},
			},
			ThreatRisk:      []*resp.ThreatRisk{},
			ThreatNum:       0,
			ComplianceNum:   0,
			ComplianceRisk:  []*resp.ComplianceRisk{},
			AssetMapping:    resp.AssetMapping{},
			DeviceRelevance: []*resp.DeviceRelevance{},
			Oper:            []*resp.PersonInfo{},
		},
	}
	getPocList(ips, &res)
	getComplianceRiskList(ips, &res)

	switch params.Type {
	case "ip":
		statisticalIpInfo(params.Ip, &res)
		getDevice(params.Ip, &res)
		mid := map[string]interface{}{
			"ip": res.AssetGraphData.Ip,
		}
		var tmp = []any{
			mid,
		}
		asset.HandleNetMapping(tmp)
		for _, v := range tmp {
			t, ok := v.(map[string]interface{})
			if ok && t["ip"] == res.AssetGraphData.Ip {
				res.AssetGraphData.NetMappings = t["net_mappings"]
			}
		}

	case "business":
		getBusinessMappingList(ips, &res)
		statisticalBusinessInfo(params.Business, &res)
		businessBasicInfo(params.Business, &res)
	}
	return &res, nil
}

// statisticalIpInfo 基础信息
func statisticalIpInfo(ip string, res *resp.AssetGraphResponse) {
	boolQuery := elastic.NewBoolQuery()
	boolQuery.Must(elastic.NewTermQuery("ip", ip))
	searchResult, err := es.GetEsClient().Search().
		Index(assets.NewAssets().IndexName()).
		Query(boolQuery).
		Do(context.TODO())
	if err != nil {
		logger.Error("getAssets error", err)
		return
	}
	id := ""
	areaId := 0
	b := make([]*resp.BusinessSystem, 0)
	if searchResult.Hits != nil && searchResult.Hits.TotalHits.Value > 0 {
		for _, hit := range searchResult.Hits.Hits {
			assetSource := assets.Assets{}
			err := json.Unmarshal(hit.Source, &assetSource)
			if err != nil {
				logger.Error("getAssets Unmarshal error", err)
				continue
			}
			id = assetSource.Id
			areaId = assetSource.Area
			networkType := "internal"
			if assetSource.NetworkType == 2 {
				networkType = "external"
			}
			res.AssetGraphData.BasicInfo.NetworkType = networkType
			flag := false
			if assetSource.MergeCount > 0 {
				flag = true
			}

			res.AssetGraphData.BasicInfo.IsFusion = flag
			res.AssetGraphData.BasicInfo.Status = assetSource.Status
			for _, port := range assetSource.Ports {
				if port.Domain != "" {
					res.AssetGraphData.BasicInfo.Domain = append(res.AssetGraphData.BasicInfo.Domain, port.Domain)
				}
				if port.Port > -1 {
					res.AssetGraphData.BasicInfo.Ports = append(res.AssetGraphData.BasicInfo.Ports, port.Port)
				}
			}
			res.AssetGraphData.BasicInfo.CreatedAt = assetSource.CreatedAt
			res.AssetGraphData.BasicInfo.UpdatedAt = assetSource.UpdatedAt
			for _, businesses := range assetSource.Business {
				if businesses == nil {
					continue
				}
				personInfo := make([]*resp.PersonInfo, 0)
				for _, person := range businesses.PersonBase {
					staff, err := staff.NewStaff().GetById(context.Background(), person.Id)
					if err != nil {
						logger.Errorf("Failed to get staff")
					}
					if staff == nil {
						personInfo = append(personInfo, &resp.PersonInfo{
							PersonBase: person,
							Phone:      "",
							Email:      make([]string, 0),
						})
						continue
					}
					email := make([]string, 0)
					for _, e := range staff.Email {
						if e != "" {
							email = append(email, e)
						}
					}
					person.Pgid, _ = pgidservice.GetPgidById(person.Id)
					personInfo = append(personInfo, &resp.PersonInfo{
						PersonBase: person,
						Phone:      staff.Mobile,
						Email:      email,
					})
				}
				b = append(b, &resp.BusinessSystem{
					BusinessName: businesses.System,
					Business:     personInfo,
				})
			}
			for _, o := range assetSource.OperInfo {
				//获取人员信息
				operStaff, err := staff.NewStaff().GetById(context.Background(), o.Id)
				if err != nil {
					logger.Errorf("Failed to get business staff")
				}
				email := make([]string, 0)
				phone := ""
				if operStaff != nil {
					if operStaff.Email != nil {
						email = operStaff.Email
					}
					if len(operStaff.Mobile) > 0 {
						phone = operStaff.Mobile
					}
					o.Pgid, _ = pgidservice.GetPgidById(o.Id)
				}
				res.AssetGraphData.Oper = append(res.AssetGraphData.Oper, &resp.PersonInfo{
					PersonBase: o,
					Email:      email,
					Phone:      phone,
				})
			}
			res.AssetGraphData.Ip = ip
			res.AssetGraphData.Id = id
		}
	}
	res.AssetGraphData.BusinessSystem = b
	var handlers []mysql.HandleFunc
	handlers = append(handlers, mysql.WithWhere("id = ?", areaId))
	first, err := network_areas.NewNetworkAreaModel().First(handlers...)
	if err != nil {
		logger.Errorf("Get first network area model failed: %v", err.Error())
	}
	res.AssetGraphData.BasicInfo.Area = "默认"
	if first.Id != 0 {
		res.AssetGraphData.BasicInfo.Area = first.Name
	}

	param := &pb.IpInfoRequest{
		Id: id,
	}
	info, err := pb.GetProtoClient().IpSecurityInfo(context.Background(), param, pb.ClientWithAddress)
	if err != nil {
		logger.Errorf("Get ip security info failed: %v", err.Error())
	}
	if info != nil {
		res.AssetGraphData.SecurityInfo.SecurityInfoIp.IsCmdb = info.IsCmdb
		res.AssetGraphData.SecurityInfo.SecurityInfoIp.IsHids = info.IsHids
		res.AssetGraphData.SecurityInfo.SecurityInfoIp.IsJumpserver = info.IsJumpserver
		res.AssetGraphData.SecurityInfo.SecurityInfoIp.HasAdminForBusiness = info.HasAdminForBusiness
		res.AssetGraphData.SecurityInfo.SecurityInfoIp.HasAdminForOperation = info.HasAdminForOperation
		res.AssetGraphData.SecurityInfo.SecurityInfoIp.HasDeparment = info.HasDeparment
		res.AssetGraphData.SecurityInfo.SecurityInfoIp.HasWaf = info.HasWaf
	}
}

func getDevice(ip string, res *resp.AssetGraphResponse) {
	boolQuery := elastic.NewBoolQuery()
	boolQuery.Should(elastic.NewTermQuery("private_ip", ip))
	boolQuery.Should(elastic.NewTermQuery("public_ip", ip))
	boolQuery.MinimumNumberShouldMatch(1)
	searchResult, err := es.GetEsClient().Search().
		Index(device.NewDeviceModel().IndexName()).
		Query(boolQuery).
		Do(context.TODO())
	if err != nil {
		logger.Error("getDevice error", err)
		return
	}
	deviceRelevance := make([]*resp.DeviceRelevance, 0)
	if searchResult.Hits != nil && searchResult.Hits.TotalHits.Value > 0 {
		for _, hit := range searchResult.Hits.Hits {
			deviceSource := device.Device{}
			err := json.Unmarshal(hit.Source, &deviceSource)
			if err != nil {
				logger.Error("getDevice Unmarshal error", err)
				continue
			}
			deviceRelevance = append(deviceRelevance, &resp.DeviceRelevance{
				Id:                deviceSource.Id,
				Sn:                deviceSource.Sn,
				DeviceName:        deviceSource.HostName,
				InternetRelevance: deviceSource.PublicIp,
				IntranetRelevance: deviceSource.PrivateIp,
				Maker:             deviceSource.Maker,
				Model:             deviceSource.Model,
			})
		}
	}
	res.AssetGraphData.DeviceRelevance = deviceRelevance
}

// operDepartment 获取人员部门
func operDepartment(id string) []string {
	staffDepartment := make([]string, 0)
	boolQuery := elastic.NewBoolQuery()
	boolQuery.Must(elastic.NewTermQuery("id", id))
	searchResult, err := es.GetEsClient().Search().
		Index(staff.NewStaff().IndexName()).
		Query(boolQuery).
		Do(context.TODO())
	if err != nil {
		logger.Error("getStaff error", err)
		return staffDepartment
	}
	if searchResult.Hits != nil && searchResult.Hits.TotalHits.Value > 0 {
		for _, hit := range searchResult.Hits.Hits {
			staffSource := staff.Staff{}
			err := json.Unmarshal(hit.Source, &staffSource)
			if err != nil {
				logger.Error("getAssets Unmarshal error", err)
				continue
			}
			staffDepartment = staffSource.Department
		}
	}
	return staffDepartment
}

// statisticalBusinessInfo 业务系统统计
func statisticalBusinessInfo(businessName string, res *resp.AssetGraphResponse) {
	s := statistical.NewService()
	var params = stat.CountRequest{
		BusinessApp: businessName,
		Params: []*stat.CountParams{
			{
				Field: "cmdb_coverage",
				Type:  "coverage",
			},
			{
				Field: "host_coverage",
				Type:  "coverage",
			},
			{
				Field: "uaudithost_coverage",
				Type:  "coverage",
			},
			{
				Field: "detect_coverage",
				Type:  "coverage",
			},
			{
				Field: "waf_coverage",
				Type:  "coverage",
			},
			{
				Field: "oper_coverage",
				Type:  "coverage",
			},
		},
	}
	list, err := s.GetCountByFields(&params)
	if err != nil {
		logger.Errorf("GetCountByFields failed: %v", err.Error())
		return
	}
	securityInfoBusiness := make([]*resp.SecurityInfoBusiness, 0)
	for _, item := range list {
		if field, ok := item["field"].(string); ok {
			if id, exists := coverage[field]; exists {
				if value, valueOk := item["value"].(int64); valueOk {
					securityInfoBusiness = append(securityInfoBusiness, &resp.SecurityInfoBusiness{
						Id:    id,
						Value: value,
					})
				}
			}
		}
	}
	res.AssetGraphData.SecurityInfo.SecurityInfoBusiness = securityInfoBusiness
}

// businessBasicInfo 函数用于获取业务基本信息并填充到响应结构体中
//
// 参数：
//
//	businessName - 业务名称字符串
//	res          - 响应结构体指针，用于存储查询结果
//
// 返回值：
//
//	返回error类型，表示函数执行过程中出现的错误。如果没有错误发生，则返回nil。
func businessBasicInfo(businessName string, res *resp.AssetGraphResponse) {
	businessInfo, err := business_system2.NewBusinessSystems().GetByName(context.Background(), businessName)
	if err != nil {
		logger.Errorf("BusinessSystems GetByName failed: %v", err.Error())
		return
	}
	if businessInfo == nil {
		logger.Errorf("BusinessSystems GetByName failed: %v", businessName)
		return
	}
	businessServer := business_systems.BusinessSystemsService{}
	attr := businessServer.GetAttr(&business_systems.AssetsAttribute{
		IsGj:           businessInfo.AssetsAttribute.IsGj,
		IsXc:           businessInfo.AssetsAttribute.IsXc,
		PurchaseType:   businessInfo.AssetsAttribute.PurchaseType,
		ImportantTypes: businessInfo.AssetsAttribute.ImportantTypes,
		InsuranceLevel: businessInfo.AssetsAttribute.InsuranceLevel,
		OperatingEnv:   businessInfo.AssetsAttribute.OperatingEnv,
		RunningState:   businessInfo.AssetsAttribute.RunningState,
	}, false)
	for key, value := range attr {
		if value != "" {
			switch key {
			case "is_xc":
				res.AssetGraphData.BasicInfo.IsXc = value
			case "gj":
				res.AssetGraphData.BasicInfo.IsGj = value
			case "env":
				res.AssetGraphData.BasicInfo.OperatingEnv = value
			case "insuranceL":
				res.AssetGraphData.BasicInfo.InsuranceLevel = value
			case "purchase_tp":
				res.AssetGraphData.BasicInfo.PurchaseType = value
			case "important_tp":
				res.AssetGraphData.BasicInfo.ImportantTypes = value
			default:
			}
		}
	}
	personInfo := make([]*resp.PersonInfo, 0)
	for _, person := range businessInfo.PersonBase {
		staff, err := staff.NewStaff().GetById(context.Background(), person.Id)
		if err != nil {
			logger.Errorf("Failed to get staff")
		}
		if staff == nil {
			personInfo = append(personInfo, &resp.PersonInfo{
				PersonBase: person,
				Phone:      "",
				Email:      make([]string, 0),
			})
			continue
		}
		email := make([]string, 0)
		for _, e := range staff.Email {
			if e != "" {
				email = append(email, e)
			}
		}
		person.Pgid, _ = pgidservice.GetPgidById(person.Id)
		personInfo = append(personInfo, &resp.PersonInfo{
			PersonBase: person,
			Phone:      staff.Mobile,
			Email:      email,
		})
	}
	res.AssetGraphData.BasicInfo.SystemVersion = businessInfo.SystemVersion
	res.AssetGraphData.BusinessSystem = []*resp.BusinessSystem{
		{
			BusinessName: businessName,
			Business:     personInfo,
		},
	}
}

// getAssetSourceIdsOrIps 函数用于根据IP地址或业务名称从Elasticsearch中检索对应的资产数据源ID和IP地址列表。
//
// 参数：
//
//	ip string: 需要检索的IP地址。如果为空，则不根据IP地址进行检索。
//	businessName string: 需要检索的业务名称。如果为空，则不根据业务名称进行检索。
//
// 返回值：
//
//	[]uint64: 检索到的资产数据源ID列表。
//	[]string: 检索到的IP地址列表。
//	error: 如果检索过程中发生错误，则返回错误信息；否则返回nil。
//
// 功能描述：
//  1. 根据传入的IP地址和业务名称构建Elasticsearch查询条件。
//  2. 对Elasticsearch中的资产索引执行查询，并聚合检索到的资产数据源ID和IP地址。
//  3. 从聚合结果中提取数据源ID和IP地址，并分别存储到切片中。
//  4. 返回数据源ID和IP地址列表，如果检索过程中发生错误，则返回错误信息。
func getAssetSourceIdsOrIps(ip, businessName string) ([]uint64, []string, error) {
	data := make([]uint64, 0)
	ips := make([]string, 0)
	boolQuery := elastic.NewBoolQuery()
	if ip != "" {
		boolQuery.Must(elastic.NewTermQuery("ip", ip))
	}
	if businessName != "" {
		nestedQuery := elastic.NewNestedQuery(
			"business", // 嵌套字段路径
			elastic.NewTermQuery("business.system", businessName), // 查询条件
		)
		boolQuery.Must(nestedQuery)
	}

	sourceIdsAgg := elastic.NewTermsAggregation().Field("source_ids").Size(1000000).ShardSize(1000000)
	ipAgg := elastic.NewTermsAggregation().Field("ip").Size(1000000).ShardSize(1000000)
	searchResult, err := es.GetEsClient().Search().
		Index(assets.NewAssets().IndexName()).
		Query(boolQuery).
		Aggregation("source_ids", sourceIdsAgg).
		Aggregation("ipAgg", ipAgg).
		Do(context.TODO())
	if err != nil {
		return nil, nil, err
	}
	// 获取数据源id
	aggResult, found := searchResult.Aggregations.Terms("source_ids")
	if found {
		for _, bucket := range aggResult.Buckets {
			sourceId, ok := bucket.Key.(string)
			if !ok {
				logger.Errorf("sourceId type assertion failed: %v", bucket.Key)
				continue
			}
			sId, err := strconv.ParseUint(sourceId, 10, 64)
			if err != nil {
				logger.Errorf("sourceId parse failed: %v", bucket.Key)
				continue
			}
			data = append(data, sId)
		}
	}
	ipResult, found := searchResult.Aggregations.Terms("ipAgg")
	if found {
		for _, bucket := range ipResult.Buckets {
			ips = append(ips, bucket.Key.(string))
		}
	}
	return data, ips, nil
}

// getBusinessMappingList 函数用于根据给定的IP地址列表，获取对应的资产映射信息，并将结果填充到响应结构体中。
//
// 参数：
// ips []string: 需要查询的IP地址列表。
// res *resp.AssetGraphResponse: 响应结构体指针，用于存储查询结果。
//
// 返回值：
// 无返回值。查询结果将直接填充到响应结构体中。
//
// 说明：
// 该函数首先遍历传入的IP地址列表，将其转换为Elasticsearch查询所需的格式。
// 然后，使用Elasticsearch客户端执行查询，获取与给定IP地址列表匹配的资产信息。
// 接着，遍历查询结果，根据资产的网络类型（内部网络或外部网络），将对应的IP地址分别添加到内部IP列表和外部IP列表中。
// 最后，将内部IP列表和外部IP列表封装到响应结构体中，以便后续处理或展示。

func getBusinessMappingList(ips []string, res *resp.AssetGraphResponse) {
	ip := make([]interface{}, 0)
	internalIp := make([]string, 0)
	externalIp := make([]string, 0)
	for _, s := range ips {
		ip = append(ip, s)
	}
	boolQuery := elastic.NewBoolQuery()
	boolQuery.Must(elastic.NewTermsQuery("ip", ip...))
	scrollService := es.GetEsClient().Scroll().
		Index(assets.NewAssets().IndexName()).
		Size(1000).
		Query(boolQuery)
	defer scrollService.Clear(context.Background())
	for {
		searchResult, err := scrollService.Do(context.Background())
		if errors.Is(err, io.EOF) {
			err = nil
			break
		}
		if err != nil || len(searchResult.Hits.Hits) == 0 {
			break
		}
		if len(searchResult.Hits.Hits) == 0 {
			break
		}
		if searchResult.Hits != nil && searchResult.Hits.TotalHits.Value > 0 {
			for _, hit := range searchResult.Hits.Hits {
				assetSource := assets.Assets{}
				err := json.Unmarshal(hit.Source, &assetSource)
				if err != nil {
					logger.Error("getAssets Unmarshal error", err)
					continue
				}
				if assetSource.NetworkType == 1 {
					internalIp = AppendUniqueString(internalIp, assetSource.Ip)
				}
				if assetSource.NetworkType == 2 {
					externalIp = AppendUniqueString(externalIp, assetSource.Ip)
				}
			}
		}
	}

	res.AssetGraphData.AssetMapping = resp.AssetMapping{
		InternalIp: internalIp,
		ExternalIp: externalIp,
	}
}

// AppendUniqueString 追加字符串并去重
func AppendUniqueString(existing []string, item string) []string {
	for _, v := range existing {
		if v == item {
			// 如果已经存在，直接返回原切片
			return existing
		}
	}
	// 如果不存在，追加字符串
	return append(existing, item)
}

// getPocList 函数用于根据提供的IP列表查询对应的漏洞利用代码（POC）列表，并将结果填充到响应结构体中。
//
// 参数：
//
//	ips []string：需要查询的IP地址列表。
//	res *resp.AssetGraphResponse：响应结构体指针，用于存储查询结果。
//
// 功能描述：
//  1. 将IP地址列表转换为接口切片。
//  2. 创建一个布尔查询对象，并设置查询条件：必须包含在给定的IP列表中，且名称不能为空。
//  3. 使用Elasticsearch客户端执行查询，获取符合条件的POC列表。
//  4. 遍历查询结果，将每个POC的信息提取出来，并填充到响应结构体中。
//  5. 如果查询过程中发生错误，将记录错误日志并返回。
//
// 返回值：
//
//	无返回值。查询结果将直接填充到响应结构体中。
func getPocList(ips []string, res *resp.AssetGraphResponse) {
	ip := make([]interface{}, 0)
	for _, s := range ips {
		ip = append(ip, s)
	}
	boolQuery := elastic.NewBoolQuery()
	boolQuery.Must(elastic.NewTermsQuery("ip", ip...))
	boolQuery.MustNot(elastic.NewTermQuery("name.keyword", ""))
	scrollService := es.GetEsClient().Scroll().
		Index(poc.NewPoc().IndexName()).Scroll("1m").Size(1000).Query(boolQuery)
	defer scrollService.Clear(context.Background())

	pocNum := 0
	threatRisk := make([]*resp.ThreatRisk, 0)
	for {
		searchResult, err := scrollService.Do(context.Background())
		if errors.Is(err, io.EOF) {
			err = nil
			break
		}
		if err != nil || len(searchResult.Hits.Hits) == 0 {
			break
		}
		if len(searchResult.Hits.Hits) == 0 {
			break
		}
		if searchResult.Hits != nil && searchResult.Hits.TotalHits.Value > 0 {
			pocNum = int(searchResult.Hits.TotalHits.Value)
			for _, hit := range searchResult.Hits.Hits {
				pocSource := poc.Poc{}
				err = json.Unmarshal(hit.Source, &pocSource)
				if err != nil {
					logger.Error("getPocList error", err)
					continue
				}
				threatRisk = append(threatRisk, &resp.ThreatRisk{
					Id:          pocSource.Id,
					Name:        pocSource.Name,
					ThreatLevel: pocSource.LevelDesc(),
				})
			}
		}
	}
	res.AssetGraphData.ThreatNum = pocNum
	res.AssetGraphData.ThreatRisk = threatRisk
}

// getComplianceRiskList 函数用于获取指定IP地址列表对应的合规风险列表，并将结果填充到响应结构体中。
//
// 参数：
// ips []string: 需要查询的IP地址列表。
// res *resp.AssetGraphResponse: 响应结构体指针，用于存储查询结果。
//
// 返回值：
// 无返回值，查询结果将直接填充到响应结构体中。
//
// 说明：
// 该函数首先根据传入的IP地址列表构建Elasticsearch查询条件，查询Elasticsearch中的合规监控任务记录，获取对应的合规监控ID列表。
// 然后，使用获取到的合规监控ID列表从MySQL数据库中查询对应的合规风险信息。
// 最后，将查询到的合规风险信息填充到响应结构体中，以便后续处理或展示。
func getComplianceRiskList(ips []string, res *resp.AssetGraphResponse) {
	ip := make([]interface{}, 0)
	cIds := make([]uint64, 0)
	for _, s := range ips {
		ip = append(ip, s)
	}
	boolQuery := elastic.NewBoolQuery()
	boolQuery.Must(elastic.NewTermsQuery("ip", ip...))
	complianceMonitorIds := elastic.NewTermsAggregation().Field("compliance_monitor_id").Size(1000000).ShardSize(1000000)
	searchResult, err := es.GetEsClient().Search().
		Index(esComplianceMonitor.NewComplianceMonitorTaskRecords().IndexName()).
		Query(boolQuery).
		Aggregation("complianceMonitorIds", complianceMonitorIds).
		Do(context.TODO())
	if err != nil {
		logger.Errorf("getComplianceRiskList error:%s", err)
	}
	complianceNum := 0
	if searchResult.Hits != nil && searchResult.Hits.TotalHits.Value > 0 {
		complianceNum = int(searchResult.Hits.TotalHits.Value)
	}
	// 获取数据源id
	aggResult, found := searchResult.Aggregations.Terms("complianceMonitorIds")
	if found {
		for _, bucket := range aggResult.Buckets {
			id, ok := bucket.Key.(float64)
			if !ok {
				logger.Errorf("getComplianceRiskList complianceMonitorIds: %s", bucket.Key)
				continue
			}
			cIds = append(cIds, uint64(id))
		}
	}
	complianceRisk := make([]*resp.ComplianceRisk, 0)
	var handlers []mysql.HandleFunc
	handlers = append(handlers, mysql.WithWhere("id in (?)", cIds))
	list, _, err := compliance_monitor.NewComplianceMonitorModel().List(1, 3, handlers...)
	if err != nil {
		logger.Errorf("getComplianceRiskList error:%s", err)
	}
	for _, v := range list {
		complianceRisk = append(complianceRisk, &resp.ComplianceRisk{
			Id:   v.Id,
			Name: v.Name,
			Desc: v.Desc,
		})
	}
	res.AssetGraphData.ComplianceNum = complianceNum
	res.AssetGraphData.ComplianceRisk = complianceRisk
}
