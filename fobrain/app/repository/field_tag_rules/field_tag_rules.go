package field_tag_rules

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"

	"github.com/olivere/elastic/v7"
	"go-micro.dev/v4/logger"

	"fobrain/fobrain/app/repository/staff"
	req "fobrain/fobrain/app/request/field_tag_rules"
	res "fobrain/fobrain/app/response/field_tag_rules"
	"fobrain/initialize/es"
	"fobrain/initialize/mysql"
	pb "fobrain/mergeService/proto"
	"fobrain/models/elastic/business_system"
	models "fobrain/models/mysql/field_tag_rules"
	"fobrain/pkg/utils"
)

func DeleteByIds(params *req.DelFieldTagRulesList) error {
	delIds := make([]uint64, 0)
	delIds = params.Ids
	if len(params.SearchCondition) > 0 {
		var handlers []mysql.HandleFunc
		handlers = utils.CanAppend(params.Keyword != "", handlers, mysql.WithLike("value", "%"+params.Keyword+"%"))
		handlers = utils.CanAppend(params.Keyword != "", handlers, mysql.WithLike("set_value", "%"+params.Keyword+"%"))
		if len(params.SearchCondition) > 0 {
			handlers = searchMysql(params.SearchCondition, handlers)
		}

		data, err := models.NewFieldTagRuleConfig().Page(0, 0, handlers...)
		if err != nil {
			return err
		}
		for _, datum := range data {
			delIds = append(delIds, datum.Id)
		}
	}
	err := models.NewFieldTagRuleConfig().DeleteByIds(delIds)
	if err != nil {
		return err
	}
	return nil
}

// FieldOrSetFiledList TODO: 先写死，后续需要将字段存在数据库中，方便随时添加
func FieldOrSetFiledList() map[string]interface{} {
	relation := make([]res.Relation, 0)
	for _, r := range models.Relation {
		relation = append(relation, res.Relation{
			Name: r,
		})
	}
	condition := make([]res.CommonList, 0)
	for fk, fv := range models.Field {
		condition = append(condition, res.CommonList{
			Name: fk,
			Id:   fv,
		})
	}
	tagType := make([]res.CommonList, 0)
	for tk, tv := range models.SetField {
		tagType = append(tagType, res.CommonList{
			Name: tk,
			Id:   tv,
		})
	}

	return map[string]interface{}{
		"condition": condition,
		"relation":  relation,
		"tag_type":  tagType,
	}
}

func List(params *req.FieldTagRulesList) ([]*res.List, int64, error) {
	list := make([]*res.List, 0)
	var handlers []mysql.HandleFunc
	handlers = append(handlers, mysql.WithOrder("created_at DESC"))
	if params.Keyword != "" {
		likeKey := "%" + params.Keyword + "%"
		handlers = append(handlers, mysql.WithWhere("value LIKE ? OR set_value LIKE ?", likeKey, likeKey))
	}
	if len(params.SearchCondition) > 0 {
		handlers = searchMysql(params.SearchCondition, handlers)
	}

	data, total, err := models.NewFieldTagRuleConfig().List(params.Page, params.PerPage, handlers...)
	if err != nil {
		return nil, 0, err
	}
	for _, datum := range data {
		reliability := 0 //默认为0
		//增加业务系统可信状态展示到内外网资产
		if datum.SetField == models.SetFieldBusiness && len(datum.SetValue) > 0 {
			boolQuery := elastic.NewBoolQuery()
			boolQuery.Must(elastic.NewTermQuery("business_name", datum.SetValue))

			businessSystem, err := es.First[business_system.BusinessSystems](boolQuery, nil, "status")
			if err != nil {
				logger.Errorf("parseAssets Failed to es.First err:%+v", err)
			}

			if businessSystem != nil { //业务系统存在资产业务系统 取其可信状态
				reliability = businessSystem.Status
			}
		}

		list = append(list, &res.List{
			Id:               datum.Id,
			Condition:        datum.Field,
			ConditionName:    models.FieldKeys[datum.Field],
			Relation:         datum.Condition,
			RelationName:     models.RelationKeys[datum.Condition],
			ConditionContent: datum.Value,
			TagType:          datum.SetField,
			TagTypeName:      models.SetFieldKeys[datum.SetField],
			TagContent:       datum.SetValue,
			TagContentId:     datum.SourceId,
			Reliability:      reliability,
		})
	}
	return list, total, err
}

func searchMysql(searchCondition []string, handlers []mysql.HandleFunc) []mysql.HandleFunc {
	// 解析查询条件
	conditions, err := utils.ParseQueryConditions(searchCondition)
	if err != nil {
		return nil
	}

	// 处理每个条件
	for _, condition := range conditions {
		var fieldKey, valueKey string
		var value interface{}
		// 确定字段类别和对应的字段键
		switch condition.Field {
		case models.FieldIp, models.FieldOs, models.FieldProduct, models.FieldTitle:
			fieldKey = "field"
			valueKey = "value"
		case models.SetFieldBusiness, models.SetFieldOper, models.SetFieldTag:
			fieldKey = "set_field"
			valueKey = "set_value"
		default:
			continue
		}

		// 如果是 "==" 或 "!=="，取数组的第一个元素
		value = condition.Value
		if condition.OperationTypeString == "==" || condition.OperationTypeString == "!==" {
			if array, ok := condition.Value.([]interface{}); ok && len(array) > 0 {
				value = array[0]
			}
		}
		// 处理逻辑连接符
		if condition.Condition == "or" {
			// 构造查询条件
			switch condition.OperationTypeString {
			case "in":
				//handlers = append(handlers, mysql.WithOrWhere(fmt.Sprintf("%s = ?", fieldKey), condition.Field))
				handlers = append(handlers, mysql.WithOrWhere(fmt.Sprintf("%s IN (?)", valueKey), value))
			case "not_in":
				//handlers = append(handlers, mysql.WithOrWhere(fmt.Sprintf("%s = ?", fieldKey), condition.Field))
				handlers = append(handlers, mysql.WithOrWhere(fmt.Sprintf("%s NOT IN (?)", valueKey), value))
			case "==":
				//handlers = append(handlers, mysql.WithOrWhere(fmt.Sprintf("%s = ?", fieldKey), condition.Field))
				handlers = append(handlers, mysql.WithOrWhere(fmt.Sprintf("%s = ?", valueKey), value))
			case "!==", "!=":
				//handlers = append(handlers, mysql.WithOrWhere(fmt.Sprintf("%s = ?", fieldKey), condition.Field))
				handlers = append(handlers, mysql.WithOrWhere(fmt.Sprintf("%s != ?", valueKey), value))
			case "null":
				handlers = append(handlers, mysql.WithWhere(fmt.Sprintf("%s = ?", fieldKey), condition.Field))
				handlers = append(handlers, mysql.WithColumnNull(valueKey))
			case "not_null":
				handlers = append(handlers, mysql.WithWhere(fmt.Sprintf("%s = ?", fieldKey), condition.Field))
				handlers = append(handlers, mysql.WithColumnNotNull(valueKey))
			}
		} else {
			// 构造查询条件
			switch condition.OperationTypeString {
			case "in":
				//handlers = append(handlers, mysql.WithWhere(fmt.Sprintf("%s = ?", fieldKey), condition.Field))
				handlers = append(handlers, mysql.WithWhere(fmt.Sprintf("%s IN (?)", valueKey), value))
			case "not_in":
				//handlers = append(handlers, mysql.WithWhere(fmt.Sprintf("%s = ?", fieldKey), condition.Field))
				handlers = append(handlers, mysql.WithWhere(fmt.Sprintf("%s NOT IN (?)", valueKey), value))
			case "==":
				//handlers = append(handlers, mysql.WithWhere(fmt.Sprintf("%s = ?", fieldKey), condition.Field))
				handlers = append(handlers, mysql.WithWhere(fmt.Sprintf("%s = ?", valueKey), value))
			case "!=", "!==":
				//handlers = append(handlers, mysql.WithWhere(fmt.Sprintf("%s = ?", fieldKey), condition.Field))
				handlers = append(handlers, mysql.WithWhere(fmt.Sprintf("%s != ?", valueKey), value))
			case "null":
				handlers = append(handlers, mysql.WithWhere(fmt.Sprintf("%s = ?", fieldKey), condition.Field))
				handlers = append(handlers, mysql.WithColumnNull(condition.Field))
			case "not_null":
				handlers = append(handlers, mysql.WithWhere(fmt.Sprintf("%s = ?", fieldKey), condition.Field))
				handlers = append(handlers, mysql.WithColumnNotNull(valueKey))
			}
		}
	}
	return handlers
}

func CreateOrUpdate(rules *req.InsertOrUpdateFieldTagRules, sourceType int, isExecTrigger bool) (uint64, error) {
	// 如果是IP字段的时候，判断IP地址段格式
	if rules.Condition == models.FieldIp && !utils.IsValidIPs(rules.ConditionContent) {
		return 0, errors.New("IP段格式错误，示例：***********/24、127.0.0.1")
	}
	id := rules.Id
	var err error
	// 判断是否需要更新
	if id > 0 {
		err = update(rules)
		if err != nil {
			return 0, err
		}
		if isExecTrigger {
			go execTriggerFieldTagger(rules)
		}
		return rules.Id, nil
	} else {
		id, err = create(rules, sourceType)
		if err != nil {
			return 0, err
		}
	}
	if isExecTrigger {
		go execTriggerFieldTagger(rules)
	}
	return id, nil
}

// execTriggerFieldTagger 函数用于执行字段标签触发器的规则
//
// 参数：
//
//	rules *req.InsertOrUpdateFieldTagRules：包含触发器规则的指针
//
// 函数执行以下操作：
// 1. 根据 rules.SetField 的值，选择不同的操作。
//   - 如果 rules.SetField 为 models.SetFieldBusiness，则从业务系统中获取资产业务结构，并将其转换为 JSON 字符串。
//   - 如果 rules.SetField 为 models.SetFieldOper 或 models.SetFieldTag，则不执行任何操作。
//
// 2. 构造 TriggerFieldTaggerRequest 请求参数。
// 3. 调用 GetProtoClient().TriggerFieldTagger 方法发送请求，并处理可能发生的错误。
func execTriggerFieldTagger(rules *req.InsertOrUpdateFieldTagRules) {
	var setFiledVal string
	switch rules.TagType {
	case models.SetFieldBusiness: // 业务系统
		businessList, err := business_system.NewBusinessSystems().GetAssetBusinessStructById(context.Background(), rules.TagContentId)
		if err != nil {
			logger.Errorf("获取业务系统失败", err)
			return
		}
		marshal, err := json.Marshal(businessList)
		if err != nil {
			logger.Errorf("json marshal failed", err)
			return
		}
		setFiledVal = string(marshal)
	case models.SetFieldOper:
		staff, err := staff.GetById(rules.TagContentId)
		if err != nil {
			logger.Errorf("获取人员信息失败", err)
			return
		}
		setFiledVal = staff.Name
	case models.SetFieldTag:
		setFiledVal = rules.TagContent
	}
	param := pb.TriggerFieldTaggerRequest{
		Field:     rules.Condition,
		Condition: rules.Relation,
		Value:     rules.ConditionContent,
		SetField:  rules.TagType,
		SetValue:  setFiledVal,
	}
	_, err := pb.GetProtoClient().TriggerFieldTagger(context.Background(), &param, pb.ClientWithAddress)
	if err != nil {
		logger.Errorf("触发字段标签失败", err)
		return
	}
}

// create 函数用于创建新的FieldTagRule配置
//
// 参数:
//   r: 包含FieldTagRule配置信息的req.InsertOrUpdateFieldTagRules类型指针
//
// 返回值:
//   如果创建成功，返回nil；否则返回错误对象
//
// 功能描述:
//   1. 创建一个models.FieldTagRule对象，并根据传入的req.InsertOrUpdateFieldTagRules参数初始化其字段
//   2. 调用models.NewFieldTagRuleConfig().CreateItem方法，将新创建的FieldTagRule对象保存到数据库中
//   3. 如果保存成功，则返回nil；否则返回错误对象

func create(r *req.InsertOrUpdateFieldTagRules, sourceType int) (uint64, error) {
	fieldCreate := models.FieldTagRule{
		Field:      r.Condition,
		Condition:  r.Relation,
		Value:      r.ConditionContent,
		SetField:   r.TagType,
		SetValue:   r.TagContent,
		UserId:     r.UserId,
		SourceType: sourceType,
		SourceId:   r.TagContentId,
	}
	var handlers []mysql.HandleFunc
	handlers = append(handlers, mysql.WithWhere("field", fieldCreate.Field))
	handlers = append(handlers, mysql.WithWhere("value", fieldCreate.Value))
	handlers = append(handlers, mysql.WithWhere("condition", fieldCreate.Condition))
	handlers = append(handlers, mysql.WithWhere("set_field", fieldCreate.SetField))
	handlers = append(handlers, mysql.WithWhere("set_value", fieldCreate.SetValue))
	first, err := models.NewFieldTagRuleConfig().First(handlers...)
	if err != nil {
		return 0, err
	}
	if first != nil {
		return 0, errors.New("已存在一条记录，无法再创建")
	}
	err = models.NewFieldTagRuleConfig().CreateItem(&fieldCreate)
	if err != nil {
		return 0, err
	}
	return fieldCreate.Id, nil
}

// update 函数用于更新FieldTagRule配置
//
// 参数:
//
//	r: 包含FieldTagRule配置信息的req.InsertOrUpdateFieldTagRules类型指针
//
// 返回值:
//
//	如果更新成功，返回nil；否则返回错误对象
//
// 功能描述:
//  1. 创建一个空的mysql.HandleFunc切片，用于存储查询条件
//  2. 将id等于r.Id的查询条件添加到handlers切片中
//  3. 调用models.NewFieldTagRuleConfig().First方法，根据handlers中的条件查询第一个FieldTagRule配置，并将结果存储在first变量中
//  4. 如果查询失败，则返回错误对象
//  5. 如果查询到的FieldTagRule配置的Id为0，表示该规则不存在，返回错误对象，并提示"不存在该规则,无法修改"
//  6. 创建一个models.FieldTagRule对象，并根据传入的req.InsertOrUpdateFieldTagRules参数初始化其字段
//  7. 调用models.NewFieldTagRuleConfig().Update方法，根据handlers中的条件更新FieldTagRule配置
//  8. 如果更新失败，则返回错误对象
//  9. 如果所有操作都成功，则返回nil
func update(r *req.InsertOrUpdateFieldTagRules) error {
	var handlers []mysql.HandleFunc
	handlers = append(handlers, mysql.WithWhere("id = ?", r.Id))
	first, err := models.NewFieldTagRuleConfig().First(handlers...)
	if err != nil {
		return err
	}
	if first.Id == 0 {
		return errors.New("不存在该规则,无法修改")
	}
	fieldUpdate := models.FieldTagRule{
		BaseModel: mysql.BaseModel{
			Id: r.Id,
		},
		Field:     r.Condition,
		Condition: r.Relation,
		Value:     r.ConditionContent,
		SetField:  r.TagType,
		SetValue:  r.TagContent,
		SourceId:  r.TagContentId,
		UserId:    r.UserId,
	}
	err = models.NewFieldTagRuleConfig().Update(&fieldUpdate)
	if err != nil {
		return err
	}
	return nil
}
