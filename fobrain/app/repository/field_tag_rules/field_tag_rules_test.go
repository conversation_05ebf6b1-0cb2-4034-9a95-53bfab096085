package field_tag_rules

import (
	"testing"
	"time"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/stretchr/testify/assert"

	req "fobrain/fobrain/app/request/field_tag_rules"
	"fobrain/fobrain/common/request"
	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/initialize/mysql"
	models "fobrain/models/mysql/field_tag_rules"
)

func TestFieldOrSetFiledList(t *testing.T) {
	list := FieldOrSetFiledList()
	assert.NotNil(t, list)
}

func TestDelete(t *testing.T) {
	t.Run("delete or", func(t *testing.T) {
		list := []*models.FieldTagRule{
			{
				BaseModel: mysql.BaseModel{
					Id: 1,
				},
				Field:     "ip",
				Condition: "=",
				Value:     "127.0.0.1",
				SetField:  "business",
				SetValue:  "fofa",
				UserId:    1,
			},
		}
		time.Sleep(time.Second)
		defer gomonkey.ApplyMethodReturn(models.NewFieldTagRuleConfig(), "Page", list, nil).Reset()
		time.Sleep(time.Second)
		defer gomonkey.ApplyMethodReturn(models.NewFieldTagRuleConfig(), "DeleteByIds", nil).Reset()
		params := req.DelFieldTagRulesList{
			Ids: []uint64{1, 2},
			SearchCondition: []string{
				`{"ip":["***********"],"operation_type_string":"==","condition":"or"}`,
				`{"tag":["abc"],"operation_type_string":"in","condition":"or"}`,
				`{"tag":["abc"],"operation_type_string":"not_null","condition":"or"}`,
			},
		}
		err := DeleteByIds(&params)
		assert.NoError(t, err)
	})
	t.Run("delete and", func(t *testing.T) {
		list := []*models.FieldTagRule{
			{
				BaseModel: mysql.BaseModel{
					Id: 1,
				},
				Field:     "ip",
				Condition: "=",
				Value:     "127.0.0.1",
				SetField:  "business",
				SetValue:  "fofa",
				UserId:    1,
			},
		}
		time.Sleep(time.Second)
		defer gomonkey.ApplyMethodReturn(models.NewFieldTagRuleConfig(), "Page", list, nil).Reset()
		time.Sleep(time.Second)
		defer gomonkey.ApplyMethodReturn(models.NewFieldTagRuleConfig(), "DeleteByIds", nil).Reset()
		params := req.DelFieldTagRulesList{
			Ids: []uint64{1, 2},
			SearchCondition: []string{
				`{"ip":["***********"],"operation_type_string":"==","condition":"and"}`,
				`{"tag":["abc"],"operation_type_string":"in","condition":"and"}`,
				`{"tag":["abc"],"operation_type_string":"not_null","condition":"and"}`,
			},
		}
		err := DeleteByIds(&params)
		assert.NoError(t, err)
	})
}

func TestList(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	params := req.FieldTagRulesList{
		PageRequest: request.PageRequest{
			Page:    1,
			PerPage: 10,
		},
		Keyword: "",
	}
	list := []*models.FieldTagRule{
		{
			BaseModel: mysql.BaseModel{
				Id: 1,
			},
			Field:     "ip",
			Condition: "=",
			Value:     "127.0.0.1",
			SetField:  "business",
			SetValue:  "fofa",
			UserId:    1,
		},
	}
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyMethodReturn(models.NewFieldTagRuleConfig(), "List", list, int64(1), nil).Reset()
	rules, total, err := List(&params)
	assert.NoError(t, err)
	assert.Equal(t, int64(1), total)
	assert.Equal(t, 1, len(rules))
}

func TestCreateOrUpdate(t *testing.T) {
	t.Run("CreateOrUpdate Ip Error", func(t *testing.T) {
		rules := req.InsertOrUpdateFieldTagRules{
			Id:               0,
			UserId:           1,
			Condition:        "ip",
			Relation:         "=",
			ConditionContent: "127.0.0",
			TagType:          "business",
			TagContent:       "测试系统",
			TagContentId:     "20c5c516a86b4a019f510c986968e63c",
		}
		id, err := CreateOrUpdate(&rules, 1, true)
		assert.Error(t, err)
		assert.Equal(t, uint64(0), id)
	})

	t.Run("CreateOrUpdate Create Success", func(t *testing.T) {
		time.Sleep(time.Second)
		defer gomonkey.ApplyMethodReturn(models.NewFieldTagRuleConfig(), "CreateItem", nil).Reset()
		defer gomonkey.ApplyMethodReturn(models.NewFieldTagRuleConfig(), "First", nil, nil).Reset()
		time.Sleep(time.Second)
		rules := req.InsertOrUpdateFieldTagRules{
			Id:               0,
			UserId:           1,
			Condition:        "ip",
			Relation:         "=",
			ConditionContent: "127.0.0.1",
			TagType:          "business",
			TagContent:       "测试系统",
			TagContentId:     "20c5c516a86b4a019f510c986968e63c",
		}
		id, err := CreateOrUpdate(&rules, 1, true)
		assert.NoError(t, err)
		assert.NotNil(t, id)
	})

	t.Run("CreateOrUpdate Update Success", func(t *testing.T) {
		first := &models.FieldTagRule{
			BaseModel: mysql.BaseModel{
				Id: 1,
			},
			Field:     "ip",
			Condition: "=",
			Value:     "*********",
			SetField:  "business",
			SetValue:  "fofa1",
			UserId:    1,
		}
		time.Sleep(time.Second)
		defer gomonkey.ApplyMethodReturn(models.NewFieldTagRuleConfig(), "First", first, nil).Reset()
		time.Sleep(time.Second)
		defer gomonkey.ApplyMethodReturn(models.NewFieldTagRuleConfig(), "Update", nil).Reset()
		rules := req.InsertOrUpdateFieldTagRules{
			Id:               1,
			UserId:           1,
			Condition:        "ip",
			Relation:         "=",
			ConditionContent: "127.0.0.1",
			TagType:          "business",
			TagContent:       "测试系统",
			TagContentId:     "20c5c516a86b4a019f510c986968e63c",
		}
		id, err := CreateOrUpdate(&rules, 1, true)
		assert.NoError(t, err)
		assert.Equal(t, uint64(1), id)
	})
}
