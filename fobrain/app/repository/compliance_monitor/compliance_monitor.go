package compliance_monitor

import (
	"context"
	"errors"
	"fmt"

	"github.com/gin-gonic/gin"
	"github.com/jinzhu/copier"
	"github.com/olivere/elastic/v7"

	reqmonitor "fobrain/fobrain/app/request/compliance_monitor"
	"fobrain/fobrain/app/response"
	"fobrain/fobrain/common/request"
	"fobrain/initialize/es"
	"fobrain/initialize/mysql"
	escompliancemonitor "fobrain/models/elastic/compliance_monitor"
	"fobrain/models/mysql/compliance_monitor"
	"fobrain/models/mysql/compliance_monitor_task"
	"fobrain/models/mysql/user"
	"fobrain/pkg/utils"
)

func Index(reqmonitor *reqmonitor.IndexRequest) (any, int64, error) {
	var total int64

	handlers := make([]mysql.HandleFunc, 0)
	handlers = append(handlers, mysql.WithOrder("created_at DESC"))
	handlers = utils.CanAppend(reqmonitor.Keyword != "", handlers, mysql.WithWhere("name LIKE ?", "%"+reqmonitor.Keyword+"%"))
	handlers = utils.CanAppend(len(reqmonitor.Event) > 0, handlers, mysql.WithWhere("event in (?)", reqmonitor.Event))

	data, total, err := compliance_monitor.NewComplianceMonitorModel().List(reqmonitor.Page, reqmonitor.PerPage, handlers...)
	if err != nil {
		return nil, 0, err
	}
	result := make([]response.ComplianceMonitorIndex, 0)
	for _, complianceMonitor := range data {
		var item response.ComplianceMonitorIndex
		//增加用户名称
		err = copier.Copy(&item, &complianceMonitor)
		if err != nil {
			return nil, 0, err
		}
		userInfo, _ := user.NewUserModel().First(mysql.WithId(complianceMonitor.UserId))
		item.UserName = userInfo.Username
		result = append(result, item)
	}

	return result, total, err
}

func Store(c *gin.Context, reqmonitor *reqmonitor.StoreRequest) error {

	// TODO 部分参数校验,如周期任务校验时间等
	// 如果不是立即执行，需要判断时间是否传入
	if reqmonitor.Event != 1 {
		if reqmonitor.Time == "" {
			return errors.New("开始时间必须传入")
		}
	}

	// TODO 规则校验

	// 创建规则
	taskModel := reqmonitor.TaskModel
	if taskModel == 0 {
		taskModel = compliance_monitor.TaskModelMark // 默认标记模式
	}
	data := &compliance_monitor.ComplianceMonitor{
		UserId:    request.GetUserId(c),
		Name:      reqmonitor.Name,
		Desc:      reqmonitor.Desc,
		Rule:      reqmonitor.Rule,
		RuleType:  reqmonitor.RuleType,
		TaskModel: taskModel,
		AssetType: reqmonitor.AssetType,
		Event:     reqmonitor.Event,
		Date:      reqmonitor.Date,
		Time:      reqmonitor.Time,
		Status:    compliance_monitor.StatusEnable,
	}
	err := compliance_monitor.NewComplianceMonitorModel().Create(data)
	if err != nil {
		return err
	}

	// 非周期任务创建任务
	if reqmonitor.Event == compliance_monitor.EventExec || reqmonitor.Event == compliance_monitor.EventOnce {

		// 获取开始时间
		startAt, err := compliance_monitor.NewComplianceMonitorModel().GetLocalTimeByEvent(reqmonitor.Event, reqmonitor.Date, reqmonitor.Time)
		if err != nil {
			return err
		}

		err = compliance_monitor_task.NewComplianceMonitorTaskModel().Create(&compliance_monitor_task.ComplianceMonitorTask{
			ComplianceMonitorId: data.Id,
			StartAt:             &startAt,
			Status:              compliance_monitor_task.StatusNotStart,
		})

	}

	return err
}

func Edit(id uint64) (compliance_monitor.ComplianceMonitor, error) {

	first, err := compliance_monitor.NewComplianceMonitorModel().First(mysql.WithWhere("id", id))

	if err != nil {
		return compliance_monitor.ComplianceMonitor{}, err
	}

	return first, nil
}

func Update(c *gin.Context, reqmonitor *reqmonitor.UpdateRequest) error {

	// TODO 部分参数校验,如周期任务校验时间等

	// TODO 规则校验

	// 更新规则
	taskModel := reqmonitor.TaskModel
	if taskModel == 0 {
		taskModel = compliance_monitor.TaskModelMark // 默认标记模式
	}
	data := compliance_monitor.ComplianceMonitor{
		BaseModel: mysql.BaseModel{
			Id: reqmonitor.Id,
		},
		UserId:    request.GetUserId(c),
		Name:      reqmonitor.Name,
		Desc:      reqmonitor.Desc,
		Rule:      reqmonitor.Rule,
		TaskModel: taskModel,
		AssetType: reqmonitor.AssetType,
		Event:     reqmonitor.Event,
		Date:      reqmonitor.Date,
		Time:      reqmonitor.Time,
		Status:    compliance_monitor.StatusEnable,
	}
	err := compliance_monitor.NewComplianceMonitorModel().Update(data)

	if err != nil {
		return err
	}

	// 非周期任务创建任务
	if reqmonitor.Event == compliance_monitor.EventExec || reqmonitor.Event == compliance_monitor.EventOnce {

		// 获取开始时间
		startAt, err := compliance_monitor.NewComplianceMonitorModel().GetLocalTimeByEvent(reqmonitor.Event, reqmonitor.Date, reqmonitor.Time)
		if err != nil {
			return err
		}

		err = compliance_monitor_task.NewComplianceMonitorTaskModel().Create(&compliance_monitor_task.ComplianceMonitorTask{
			ComplianceMonitorId: data.Id,
			StartAt:             &startAt,
			Status:              compliance_monitor_task.StatusNotStart,
		})

	}

	return err
}

func SetStatus(reqmonitor *reqmonitor.SetStatusRequest) error {
	// 更新规则
	data := compliance_monitor.ComplianceMonitor{
		BaseModel: mysql.BaseModel{
			Id: reqmonitor.Id,
		},

		Status: reqmonitor.Status,
	}
	return compliance_monitor.NewComplianceMonitorModel().Update(data)
}

func Exec(id uint64) error {

	// 获取开始时间
	startAt, err := compliance_monitor.NewComplianceMonitorModel().GetLocalTimeByEvent(compliance_monitor.EventExec, "", "")
	if err != nil {
		return err
	}

	err = compliance_monitor_task.NewComplianceMonitorTaskModel().Create(&compliance_monitor_task.ComplianceMonitorTask{
		ComplianceMonitorId: id,
		StartAt:             &startAt,
		Status:              compliance_monitor_task.StatusNotStart,
	})

	return err
}

func Destroy(ids []uint64, keyword string) error {
	if len(ids) <= 0 {
		//获取合规监测周期ids
		var monitorIds []string

		handlers := make([]mysql.HandleFunc, 0)
		handlers = append(handlers, mysql.WithWhere("event > ?", 2))
		handlers = utils.CanAppend(keyword != "", handlers, mysql.WithWhere("name LIKE ?", "%"+keyword+"%"))

		monitors, _, err := compliance_monitor.NewComplianceMonitorModel().List(0, 0, handlers...)
		for _, monitor := range monitors {
			monitorIds = append(monitorIds, fmt.Sprintf("%d", monitor.Id))
		}

		//删除合规监测
		err = compliance_monitor.NewComplianceMonitorModel().Delete(handlers...)
		if err != nil {
			return err
		}

		//获取周期下发的任务ids
		var taskIds []string
		tasks, _, err := compliance_monitor_task.NewComplianceMonitorTaskModel().List(0, 0, mysql.WithWhere("compliance_monitor_id in  (?)", monitorIds))
		for _, task := range tasks {
			taskIds = append(taskIds, fmt.Sprintf("%d", task.Id))
		}

		//删除任务
		err = compliance_monitor_task.NewComplianceMonitorTaskModel().Delete(mysql.WithWhere("compliance_monitor_id in  (?)", monitorIds))
		if err != nil {
			return err
		}

		//删除任务结果
		boolQuery := elastic.NewBoolQuery()
		boolQuery = boolQuery.Must(elastic.NewTermsQueryFromStrings("compliance_monitor_task_id", taskIds...))
		_, err = es.GetEsClient().DeleteByQuery().Index(escompliancemonitor.NewComplianceMonitorTaskRecords().IndexName()).Query(boolQuery).Refresh("true").Do(context.TODO())
		if err != nil {
			return err
		}
		return nil
	}

	//删除合规监测
	err := compliance_monitor.NewComplianceMonitorModel().Delete(mysql.WithWhere("id in  (?)", ids))
	if err != nil {
		return err
	}

	//获取周期下发的任务ids
	var taskIds []string
	tasks, _, err := compliance_monitor_task.NewComplianceMonitorTaskModel().List(0, 0, mysql.WithWhere("compliance_monitor_id in  (?)", ids))
	for _, task := range tasks {
		taskIds = append(taskIds, fmt.Sprintf("%d", task.Id))
	}

	//删除任务
	err = compliance_monitor_task.NewComplianceMonitorTaskModel().Delete(mysql.WithWhere("compliance_monitor_id in  (?)", ids))
	if err != nil {
		return err
	}

	//删除任务结果
	boolQuery := elastic.NewBoolQuery()
	boolQuery = boolQuery.Must(elastic.NewTermsQueryFromStrings("compliance_monitor_task_id", taskIds...))
	_, err = es.GetEsClient().DeleteByQuery().Index(escompliancemonitor.NewComplianceMonitorTaskRecords().IndexName()).Query(boolQuery).Refresh("true").Do(context.TODO())
	if err != nil {
		return err
	}

	return nil
}
