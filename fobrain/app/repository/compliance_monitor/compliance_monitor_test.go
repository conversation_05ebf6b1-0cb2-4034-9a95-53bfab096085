package compliance_monitor

import (
	"errors"
	"fobrain/initialize/es"
	"git.gobies.org/caasm/fobrain-components/utils/localtime"
	"github.com/olivere/elastic/v7"
	"testing"
	"time"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"

	reqmonitor "fobrain/fobrain/app/request/compliance_monitor"

	"fobrain/initialize/mysql"
	"fobrain/models/mysql/compliance_monitor"
	"fobrain/models/mysql/compliance_monitor_task"
	"fobrain/models/mysql/user"
)

func TestIndex(t *testing.T) {
	patches := []*gomonkey.Patches{
		gomonkey.ApplyMethodReturn(&compliance_monitor.ComplianceMonitor{}, "List", []compliance_monitor.ComplianceMonitor{
			compliance_monitor.ComplianceMonitor{
				BaseModel:         mysql.BaseModel{Id: 1},
				UserId:            0,
				Name:              "",
				Desc:              "",
				Rule:              "",
				RuleType:          0,
				AssetType:         0,
				Event:             0,
				Date:              "",
				Time:              "",
				Status:            0,
				LastTime:          &localtime.Time{},
				LastAbnormalAsset: 0,
			},
		}, int64(0), nil),
		gomonkey.ApplyMethodReturn(&user.User{}, "First", user.User{
			BaseModel: mysql.BaseModel{Id: 1},
			Username:  "",
			Account:   "",
			Password:  "",
			Status:    0,
			Role:      user.Role{},
			StaffIds:  nil,
		}, nil),
	}
	time.Sleep(time.Second)
	_, _, err := Index(&reqmonitor.IndexRequest{})
	for _, patch := range patches {
		patch.Reset()
	}
	assert.Nil(t, err)

	patches = []*gomonkey.Patches{
		gomonkey.ApplyMethodReturn(&compliance_monitor.ComplianceMonitor{}, "List", []compliance_monitor.ComplianceMonitor{
			compliance_monitor.ComplianceMonitor{
				BaseModel:         mysql.BaseModel{Id: 1},
				UserId:            0,
				Name:              "",
				Desc:              "",
				Rule:              "",
				RuleType:          0,
				AssetType:         0,
				Event:             0,
				Date:              "",
				Time:              "",
				Status:            0,
				LastTime:          &localtime.Time{},
				LastAbnormalAsset: 0,
			},
		}, int64(0), errors.New("err")),
		gomonkey.ApplyMethodReturn(&user.User{}, "First", user.User{
			BaseModel: mysql.BaseModel{Id: 1},
			Username:  "",
			Account:   "",
			Password:  "",
			Status:    0,
			Role:      user.Role{},
			StaffIds:  nil,
		}, nil),
	}
	time.Sleep(time.Second)
	_, _, err = Index(&reqmonitor.IndexRequest{})
	for _, patch := range patches {
		patch.Reset()
	}
	assert.NotNil(t, err)
}

func TestStore(t *testing.T) {
	patches := []*gomonkey.Patches{
		gomonkey.ApplyMethodReturn(&compliance_monitor.ComplianceMonitor{}, "Create", nil),
		gomonkey.ApplyMethodReturn(&compliance_monitor.ComplianceMonitor{}, "GetLocalTimeByEvent", localtime.Time{}, nil),
		gomonkey.ApplyMethodReturn(&compliance_monitor_task.ComplianceMonitorTask{}, "Create", nil),
	}
	time.Sleep(time.Second)
	err := Store(&gin.Context{}, &reqmonitor.StoreRequest{Event: compliance_monitor.EventExec})
	for _, patch := range patches {
		patch.Reset()
	}
	assert.Nil(t, err)

	patches = []*gomonkey.Patches{
		gomonkey.ApplyMethodReturn(&compliance_monitor.ComplianceMonitor{}, "Create", errors.New("err")),
		gomonkey.ApplyMethodReturn(&compliance_monitor.ComplianceMonitor{}, "GetLocalTimeByEvent", localtime.Time{}, nil),
		gomonkey.ApplyMethodReturn(&compliance_monitor_task.ComplianceMonitorTask{}, "Create", nil),
	}
	time.Sleep(time.Second)
	err = Store(&gin.Context{}, &reqmonitor.StoreRequest{Event: compliance_monitor.EventExec})
	for _, patch := range patches {
		patch.Reset()
	}
	assert.NotNil(t, err)

	patches = []*gomonkey.Patches{
		gomonkey.ApplyMethodReturn(&compliance_monitor.ComplianceMonitor{}, "Create", nil),
		gomonkey.ApplyMethodReturn(&compliance_monitor.ComplianceMonitor{}, "GetLocalTimeByEvent", localtime.Time{}, errors.New("err")),
		gomonkey.ApplyMethodReturn(&compliance_monitor_task.ComplianceMonitorTask{}, "Create", nil),
	}
	time.Sleep(time.Second)
	err = Store(&gin.Context{}, &reqmonitor.StoreRequest{Event: compliance_monitor.EventExec})
	for _, patch := range patches {
		patch.Reset()
	}
	assert.NotNil(t, err)
}

func TestEdit(t *testing.T) {
	patch := gomonkey.ApplyMethodReturn(&compliance_monitor.ComplianceMonitor{}, "First", compliance_monitor.ComplianceMonitor{}, nil)
	_, err := Edit(uint64(1))
	patch.Reset()
	assert.Nil(t, err)

	patch = gomonkey.ApplyMethodReturn(&compliance_monitor.ComplianceMonitor{}, "First", compliance_monitor.ComplianceMonitor{}, errors.New("err"))
	time.Sleep(time.Second)
	_, err = Edit(uint64(1))
	patch.Reset()
	assert.NotNil(t, err)
}

func TestUpdate(t *testing.T) {
	patches := []*gomonkey.Patches{
		gomonkey.ApplyMethodReturn(&compliance_monitor.ComplianceMonitor{}, "Update", nil),
		gomonkey.ApplyMethodReturn(&compliance_monitor.ComplianceMonitor{}, "GetLocalTimeByEvent", localtime.Time{}, nil),
		gomonkey.ApplyMethodReturn(&compliance_monitor_task.ComplianceMonitorTask{}, "Create", nil),
	}
	err := Update(&gin.Context{}, &reqmonitor.UpdateRequest{Event: compliance_monitor.EventExec})
	for _, patch := range patches {
		patch.Reset()
	}
	assert.Nil(t, err)

	patches = []*gomonkey.Patches{
		gomonkey.ApplyMethodReturn(&compliance_monitor.ComplianceMonitor{}, "Update", errors.New("err")),
		gomonkey.ApplyMethodReturn(&compliance_monitor.ComplianceMonitor{}, "GetLocalTimeByEvent", localtime.Time{}, nil),
		gomonkey.ApplyMethodReturn(&compliance_monitor_task.ComplianceMonitorTask{}, "Create", nil),
	}
	time.Sleep(time.Second)
	err = Update(&gin.Context{}, &reqmonitor.UpdateRequest{Event: compliance_monitor.EventExec})
	for _, patch := range patches {
		patch.Reset()
	}
	assert.NotNil(t, err)

	patches = []*gomonkey.Patches{
		gomonkey.ApplyMethodReturn(&compliance_monitor.ComplianceMonitor{}, "Update", nil),
		gomonkey.ApplyMethodReturn(&compliance_monitor.ComplianceMonitor{}, "GetLocalTimeByEvent", localtime.Time{}, errors.New("err")),
		gomonkey.ApplyMethodReturn(&compliance_monitor_task.ComplianceMonitorTask{}, "Create", nil),
	}
	err = Update(&gin.Context{}, &reqmonitor.UpdateRequest{Event: compliance_monitor.EventExec})
	for _, patch := range patches {
		patch.Reset()
	}
	assert.NotNil(t, err)
}

func TestSetStatus(t *testing.T) {
	patch := gomonkey.ApplyMethodReturn(&compliance_monitor.ComplianceMonitor{}, "Update", errors.New("err"))
	err := SetStatus(&reqmonitor.SetStatusRequest{})
	patch.Reset()
	assert.NotNil(t, err)
}

func TestExec(t *testing.T) {
	patches := []*gomonkey.Patches{
		gomonkey.ApplyMethodReturn(&compliance_monitor.ComplianceMonitor{}, "GetLocalTimeByEvent", localtime.Time{}, nil),
		gomonkey.ApplyMethodReturn(&compliance_monitor_task.ComplianceMonitorTask{}, "Create", nil),
	}
	err := Exec(uint64(1))
	for _, patch := range patches {
		patch.Reset()
	}
	assert.Nil(t, err)

	patches = []*gomonkey.Patches{
		gomonkey.ApplyMethodReturn(&compliance_monitor.ComplianceMonitor{}, "GetLocalTimeByEvent", localtime.Time{}, errors.New("err")),
		gomonkey.ApplyMethodReturn(&compliance_monitor_task.ComplianceMonitorTask{}, "Create", nil),
	}
	time.Sleep(time.Second)
	err = Exec(uint64(1))
	for _, patch := range patches {
		patch.Reset()
	}
	assert.NotNil(t, err)
}

func TestDestroy(t *testing.T) {
	patches := []*gomonkey.Patches{
		gomonkey.ApplyMethodReturn(&compliance_monitor.ComplianceMonitor{}, "Delete", nil),
		gomonkey.ApplyMethodReturn(&compliance_monitor_task.ComplianceMonitorTask{}, "List", []compliance_monitor_task.ComplianceMonitorTask{}, int64(0), nil),
		gomonkey.ApplyMethodReturn(&compliance_monitor_task.ComplianceMonitorTask{}, "Delete", nil),
		gomonkey.ApplyMethodReturn(&elastic.DeleteByQueryService{}, "Do", &elastic.BulkIndexByScrollResponse{}, nil),
		gomonkey.ApplyMethodReturn(&elastic.DeleteByQueryService{}, "Index", &elastic.DeleteByQueryService{}),
		gomonkey.ApplyMethodReturn(&elastic.DeleteByQueryService{}, "Query", &elastic.DeleteByQueryService{}),
		gomonkey.ApplyMethodReturn(&elastic.DeleteByQueryService{}, "Refresh", &elastic.DeleteByQueryService{}),
		gomonkey.ApplyMethodReturn(&es.SafeClient{}, "DeleteByQuery", &elastic.DeleteByQueryService{}),
	}
	time.Sleep(time.Second)
	err1 := Destroy([]uint64{}, "")
	err2 := Destroy([]uint64{1}, "")

	for _, patch := range patches {
		patch.Reset()
	}

	assert.Nil(t, err1)
	assert.Nil(t, err2)
}
