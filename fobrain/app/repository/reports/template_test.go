package reports

import (
	testcommon "fobrain/fobrain/tests/common_test"
	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestCreate(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()
		mockDb.ExpectBegin()
		mockDb.ExpectExec("INSERT INTO `report_templates` (`created_at`,`updated_at`,`name`,`time_range`,`category_leve_1`,`category_leve_2`,`category_leve_3`,`notice_email`,`is_default`) VALUES (?,?,?,?,?,?,?,?,?)").
			WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg()).
			WillReturnResult(sqlmock.NewResult(1, 1))
		mockDb.ExpectCommit()
		err := Create("", 0, 0, 0, 0, "")
		assert.Nil(t, err)
	})
}
