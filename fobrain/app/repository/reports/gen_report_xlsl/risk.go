package gen_report_xlsl

import (
	"github.com/olivere/elastic/v7"
)

type Risk struct {
	GenerateExcel
}

func (r *Risk) Header() []string {
	if len(r.Headers) == 0 {
		r.Headers = []string{"风险名称", "风险类型", "风险等级", "风险描述", "风险标签", "风险创建时间", "风险更新时间"}
	}

	return r.Headers
}

func (r *Risk) BoolQuery(query ...elastic.BoolQuery) *elastic.BoolQuery {
	panic("not implemented")
}

func (r *Risk) IndexName() string {
	return ""
}

func (r *Risk) Row(hit *elastic.SearchHit) []interface{} {
	panic("not implemented")
}

func (r *Risk) Execute() string {
	return ""
}
