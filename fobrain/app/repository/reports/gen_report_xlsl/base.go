package gen_report_xlsl

import (
	"context"
	"errors"
	"fmt"
	"fobrain/initialize/es"
	"github.com/olivere/elastic/v7"
	"io"
	"time"
)

type GenerateExcelInterface interface {
	Header() []string
	Row(hit *elastic.SearchHit) []interface{}
	BoolQuery(query ...elastic.BoolQuery) *elastic.BoolQuery
	IndexName() string
	Execute() string
}

type GenerateExcel struct {
	GenerateExcelInterface
	boolQuery *elastic.BoolQuery
	Headers   []string
	Param     map[string]any
}

// Header
// @Summary 表头
func (g *GenerateExcel) Header() []string {
	panic("not implemented")
}

// IndexName
// @Summary 索引名称
func (g *GenerateExcel) IndexName() string {
	panic("not implemented")
}

// BoolQuery
// @Summary bool查询
func (g *GenerateExcel) BoolQuery(query ...elastic.BoolQuery) *elastic.BoolQuery {
	g.boolQuery = elastic.NewBoolQuery()
	for _, v := range query {
		g.boolQuery = g.boolQuery.Must(&v)
	}

	if g.Param["time_range"] == 1 {
		g.boolQuery = g.boolQuery.Must(
			elastic.NewRangeQuery("created_at").Gte(time.Now().AddDate(0, -1, 0)).Lte(time.Now()),
		)
	}

	return g.boolQuery
}

func (g *GenerateExcel) SetBoolQuery(boolQuery *elastic.BoolQuery) {
	g.boolQuery = boolQuery
}

// Execute
// @Summary 执行
// @return string 返回生成的文件路径
func (g *GenerateExcel) Execute() string {
	panic("not implemented")
}

func (g *GenerateExcel) Row(hit *elastic.SearchHit) []interface{} {
	panic("not implemented")
}

func (g *GenerateExcel) GenXlsx(indexName, filePath string, row func(hit *elastic.SearchHit) []interface{}) error {
	var datum [][]interface{}

	if g.boolQuery == nil {
		g.boolQuery = elastic.NewBoolQuery()
	}

	scroll := es.GetEsClient().Scroll().Index(indexName).
		Query(g.BoolQuery()).Sort("updated_at", false).Size(1000).Scroll("1m")
	defer scroll.Clear(context.Background())

	fmt.Println(g.BoolQuery(), "g.BoolQuery()")
	for {
		result, err := scroll.Do(context.TODO())
		if errors.Is(err, io.EOF) {
			err = nil
			break
		}

		if err != nil {
			panic(err)
		}

		if len(result.Hits.Hits) == 0 {
			break
		}

		for _, hit := range result.Hits.Hits {
			datum = append(datum, row(hit))
		}

		//scroll = es.GetEsClient().Scroll().ScrollId(result.ScrollId).Scroll("1m")
	}

	return nil
}

// GenerateReport
// @Summary 生成报告
//
// @Param reportType string 报告类型， 可选值：asset、threat、risk
//
//	@Param param map[string]interface{}{
//		"time_range": 1, // 1全部、2一个月
//	}
func GenerateReport(reportType string, param map[string]any) string {
	var gen GenerateExcelInterface
	switch reportType {
	case "asset":
		gen = &Asset{GenerateExcel: GenerateExcel{Param: param}}
	case "threat":
		gen = &Threat{GenerateExcel: GenerateExcel{Param: param}}
	case "risk":
		gen = &Risk{GenerateExcel: GenerateExcel{Param: param}}
	default:
		panic("not implemented")
	}

	gen.IndexName()
	return gen.Execute()
}
