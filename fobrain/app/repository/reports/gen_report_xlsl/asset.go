package gen_report_xlsl

import (
	"encoding/json"
	"fobrain/models/elastic/assets"
	"fobrain/models/mysql/network_areas"
	"strings"
	"time"

	"github.com/olivere/elastic/v7"
)

type Asset struct {
	GenerateExcel
}

func (a *Asset) Header() []string {
	if len(a.Headers) == 0 {
		a.Headers = []string{
			"IP地址", "端口", "协议", "组件信息", "IP类型", "映射IP", "MAC地址",
			"内网域名", "所属区域", "关联基础设施", "业务系统", "业务系统负责人", "运维负责人", "首次上报时间", "最后上报时间",
		}
	}

	return a.Headers
}

func (a *Asset) IndexName() string {
	return assets.NewAssets().IndexName()
}

func (a *Asset) BoolQuery(query ...elastic.BoolQuery) *elastic.BoolQuery {
	a.boolQuery = elastic.NewBoolQuery()
	for _, v := range query {
		a.boolQuery = a.boolQuery.Must(&v)
	}

	return a.boolQuery
}

// Row
// @Summary 行数据组装
func (a *Asset) Row(hit *elastic.SearchHit) []interface{} {
	networkArea := network_areas.AllNetworkArea()

	asset := assets.Assets{}
	if err := json.Unmarshal(hit.Source, &asset); err != nil {
		return []interface{}{}
	}

	return []interface{}{
		asset.Ip,
		asset.PortInfos(),
		strings.Join(asset.Product, ","),
		asset.StatusDesc(),
		asset.IpTypeDesc(),
		strings.Join(asset.Mac, ","),
		strings.Join(asset.Domains(), ","),
		networkArea[uint64(asset.Area)],
		"",
		asset.BusinessSystems(),
		asset.BusinessOwners(),
		asset.OperString(),
		asset.CreatedAt,
		asset.UpdatedAt,
	}
}

func (a *Asset) Execute() string {
	filePath := time.Now().Format("20060102150405") + "-资产列表.xlsx"
	a.GenXlsx(a.IndexName(), filePath, a.Row)

	return filePath
}
