package gen_report_xlsl

import (
	"github.com/olivere/elastic/v7"
)

type Threat struct {
	GenerateExcel
}

func (t *Threat) Header() []string {
	if len(t.Headers) == 0 {
		t.Headers = []string{"威胁名称", "威胁类型", "威胁等级", "威胁描述", "威胁标签", "威胁创建时间", "威胁更新时间"}
	}

	return t.Headers
}

func (t *Threat) BoolQuery(query ...elastic.BoolQuery) *elastic.BoolQuery {
	panic("not implemented")
}

func (t *Threat) IndexName() string {
	return ""
}

func (t *Threat) Row(hit *elastic.SearchHit) []interface{} {
	panic("not implemented")
}

func (t *Threat) Execute() string {
	return ""
}
