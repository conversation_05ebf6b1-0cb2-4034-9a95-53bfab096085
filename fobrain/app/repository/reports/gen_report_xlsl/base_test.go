package gen_report_xlsl

import (
	testcommon "fobrain/fobrain/tests/common_test"
	"github.com/olivere/elastic/v7"
	"testing"
)

func TestGenerateReport(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()
	mockServer.Register("/asset/_search", []*elastic.SearchHit{
		{
			Id:     "1",
			Source: []byte(`{"id":"1","name":"example","area":1,"domains":["example.com"],"created_at":"2022-01-01 00:00:00","updated_at":"2022-01-02 00:00:00"}`),
		},
	})

	mockServer.RegisterEmptyScrollHandler()

	GenerateReport("asset", map[string]any{"time_range": 2})
}
