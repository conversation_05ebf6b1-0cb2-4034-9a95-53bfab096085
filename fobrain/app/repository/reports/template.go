package reports

import (
	"fobrain/models/mysql/report_templates"
)

// Create
// @Summary 创建报告模板
func Create(
	name string, timeRange, categoryLeve1, categoryLeve2, categoryLeve3 int, noticeEmail string,
) error {
	template := report_templates.NewReportTemplatesModel()
	template.Name = name
	template.TimeRange = timeRange
	template.CategoryLeve1 = categoryLeve1
	template.CategoryLeve2 = categoryLeve2
	template.CategoryLeve3 = categoryLeve3
	template.NoticeEmail = noticeEmail
	return template.Create()
}

func List(keyword string) (any, int64, error) {
	templates := report_templates.NewReportTemplatesModel()
	return templates.List(keyword)
}
