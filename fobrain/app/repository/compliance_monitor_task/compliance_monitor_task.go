package compliance_monitor_task

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"fobrain/fobrain/app/repository/threat"
	"fobrain/fobrain/common/request"
	"fobrain/models/elastic/poc"
	"fobrain/models/mysql/compliance_risks"
	"io"
	"strconv"
	"strings"
	"time"

	"git.gobies.org/caasm/fobrain-components/utils/localtime"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"github.com/jinzhu/copier"
	"github.com/olivere/elastic/v7"
	"go-micro.dev/v4/logger"

	pb "fobrain/mergeService/proto"
	filtrate "fobrain/models/elastic"
	pgidservice "fobrain/services/people_pgid"

	"fobrain/fobrain/app/repository/asset"
	reqmonitor "fobrain/fobrain/app/request/compliance_monitor_task"
	res "fobrain/fobrain/app/response"
	"fobrain/fobrain/logs"
	"fobrain/initialize/es"
	"fobrain/initialize/mysql"
	"fobrain/models/elastic/assets"
	escompliancemonitor "fobrain/models/elastic/compliance_monitor"
	esmodel_device "fobrain/models/elastic/device"
	"fobrain/models/mysql/compliance_monitor"
	"fobrain/models/mysql/compliance_monitor_task"
	"fobrain/models/mysql/user"
	"fobrain/pkg/utils"
)

func Index(reqmonitor *reqmonitor.IndexRequest) (any, int64, error) {
	var total int64
	data := make([]res.ComplianceMonitorTaskIndex, 0)

	var monitorIds []uint64
	if reqmonitor.Keyword != "" {
		ids, err := compliance_monitor.NewComplianceMonitorModel().GetIds(mysql.WithWhere("name LIKE ?", "%"+reqmonitor.Keyword+"%"))
		if err != nil || len(ids) == 0 {
			return data, total, err
		}

		monitorIds = ids
	}

	var opts []mysql.HandleFunc
	if reqmonitor.Field == "" && reqmonitor.Order == "" {
		opts = append(opts, mysql.WithOrder("status ASC"))
		opts = append(opts, mysql.WithOrder("created_at DESC"))
	} else {
		opts = append(opts, mysql.WithOrder(fmt.Sprintf("%s %s", reqmonitor.Field, reqmonitor.Order)))
	}

	if len(monitorIds) > 0 {
		opts = append(opts, mysql.WithWhere("compliance_monitor_id IN (?)", monitorIds))
	}

	if reqmonitor.Status > 0 {
		opts = append(opts, mysql.WithWhere("status = ?", reqmonitor.Status))
	}

	list, total, err := compliance_monitor_task.NewComplianceMonitorTaskModel().List(reqmonitor.Page, reqmonitor.PerPage, opts...)
	if err != nil {
		return data, total, err
	}

	for _, task := range list {
		monitorItem, err := compliance_monitor.NewComplianceMonitorModel().First(mysql.WithWhere("id = ?", task.ComplianceMonitorId))
		if err != nil {
			return data, total, err
		}
		userItem, err := user.NewUserModel().First(mysql.WithWhere("id = ?", monitorItem.UserId))
		if err != nil {
			return data, total, err
		}

		var monitorIndex res.ComplianceMonitorTaskIndex
		monitorIndex.TaskId = task.Id
		monitorIndex.TaskStatus = task.Status
		monitorIndex.TaskStartAt = task.StartAt
		monitorIndex.TaskEndAt = task.EndAt
		monitorIndex.TaskAbnormalAsset = task.AbnormalAsset
		monitorIndex.MonitorId = monitorItem.Id
		monitorIndex.MonitorName = monitorItem.Name
		monitorIndex.MonitorDesc = monitorItem.Desc
		monitorIndex.MonitorEvent = monitorItem.Event
		monitorIndex.MonitorTaskModel = monitorItem.TaskModel
		monitorIndex.UserId = monitorItem.UserId
		monitorIndex.UserName = userItem.Username
		data = append(data, monitorIndex)
	}

	return data, total, err
}

func Destroy(ids []uint64, keyword string) error {
	if len(ids) <= 0 {
		var monitorIds []string
		var periodIds []uint64

		handlers := make([]mysql.HandleFunc, 0)
		//handlers = append(handlers, mysql.WithWhere("event < ?", 3))
		handlers = utils.CanAppend(keyword != "", handlers, mysql.WithWhere("name LIKE ?", "%"+keyword+"%"))
		monitors, _, err := compliance_monitor.NewComplianceMonitorModel().List(0, 0, handlers...)

		for _, monitor := range monitors {
			if monitor.Event > 2 {
				periodIds = append(periodIds, monitor.Id)
			}
			monitorIds = append(monitorIds, fmt.Sprintf("%d", monitor.Id))
		}
		// # TODO:临时增加提示话术，如果有周期任务，不允许删除。
		// # 测底修改，需要解决定时任务创建数据的问题
		if len(periodIds) > 0 {
			return errors.New("选择数据中包含周期任务，不允许删除。")
		}

		//删除合规监测
		err = compliance_monitor.NewComplianceMonitorModel().Delete(handlers...)
		if err != nil {
			return err
		}

		//获取周期下发的任务ids
		var taskIds []string
		tasks, _, err := compliance_monitor_task.NewComplianceMonitorTaskModel().List(0, 0, mysql.WithWhere("compliance_monitor_id in  (?)", monitorIds))
		for _, task := range tasks {
			taskIds = append(taskIds, fmt.Sprintf("%d", task.Id))
		}

		//删除任务
		err = compliance_monitor_task.NewComplianceMonitorTaskModel().Delete(mysql.WithWhere("compliance_monitor_id in  (?)", monitorIds))
		if err != nil {
			return err
		}

		//删除任务结果
		boolQuery := elastic.NewBoolQuery()
		boolQuery = boolQuery.Must(elastic.NewTermsQueryFromStrings("compliance_monitor_task_id", taskIds...))
		_, err = es.GetEsClient().DeleteByQuery().Index(escompliancemonitor.NewComplianceMonitorTaskRecords().IndexName()).Query(boolQuery).Refresh("true").Do(context.TODO())
		if err != nil {
			return err
		}

		return nil
	}

	//删除任务
	err := compliance_monitor_task.NewComplianceMonitorTaskModel().Delete(mysql.WithWhere("id in (?)", ids))
	if err != nil {
		return err
	}

	var strIds []string
	for _, id := range ids {
		strIds = append(strIds, fmt.Sprintf("%d", id))
	}

	//删除任务结果
	boolQuery := elastic.NewBoolQuery()
	boolQuery = boolQuery.Must(elastic.NewTermsQueryFromStrings("compliance_monitor_task_id", strIds...))

	_, err = es.GetEsClient().DeleteByQuery().Index(escompliancemonitor.NewComplianceMonitorTaskRecords().IndexName()).Query(boolQuery).Refresh("true").Do(context.TODO())
	if err != nil {
		return err
	}

	return nil
}

func RecordsDestroy(c *gin.Context, ids, searchCondition []string, keyword string) error {
	//删除任务结果
	boolQuery := elastic.NewBoolQuery()

	// // 增加权限过滤
	// permissionQuery, err := permission.NewPermissionService().ProcessElasticsearchQuery(c, data_source.SourceTypeInternetAsset)
	// if err != nil {
	// 	return err
	// }
	// permissionQuery2, err := permission.NewPermissionService().ProcessElasticsearchQuery(c, data_source.SourceTypeIntranetAsset)
	// if err != nil {
	// 	return err
	// }
	// if permissionQuery != nil || permissionQuery2 != nil {
	// 	shouldQuery := elastic.NewBoolQuery()
	// 	if permissionQuery != nil {
	// 		shouldQuery = shouldQuery.Should(permissionQuery)
	// 	}
	// 	if permissionQuery2 != nil {
	// 		shouldQuery = shouldQuery.Should(permissionQuery2)
	// 	}
	// 	boolQuery = boolQuery.Must(shouldQuery)
	// }

	if len(searchCondition) > 0 {
		conditions, err := filtrate.ParseQueryConditions(searchCondition)
		if err != nil {
			logger.Errorf("Records ParseQueryConditions 解析查询条件失败 %s", err.Error())
		}
		for _, condition := range conditions {
			boolQuery = filtrate.BuildBoolQuery(condition.Field, condition.OperationTypeString, condition.LogicalConnective, condition.Value, boolQuery)
		}
	}

	if keyword != "" {
		boolQuery = escompliancemonitor.NewComplianceMonitorTaskRecords().NewKeywordQuery(keyword, boolQuery)
	}

	if len(ids) > 0 {
		boolQuery = boolQuery.Must(elastic.NewTermsQueryFromStrings("id", ids...))
	} else {
		boolQuery = boolQuery.Must(elastic.NewMatchAllQuery())
	}

	_, err := es.GetEsClient().DeleteByQuery().Index(escompliancemonitor.NewComplianceMonitorTaskRecords().IndexName()).Query(boolQuery).Refresh("true").Do(context.TODO())
	if err != nil {
		return err
	}

	return nil
}

func Exec(monitorTask compliance_monitor_task.ComplianceMonitorTask) error {
	logs.GetLogger().Infof("exec monitorTask compliance_monitor_task_id=%d", monitorTask.Id)
	complianceMonitor, err := compliance_monitor.NewComplianceMonitorModel().First(mysql.WithWhere("id = ?", monitorTask.ComplianceMonitorId))
	if err != nil {
		return errors.New(fmt.Sprintf("compliance_monitor_task Exec First Error：%s ", err))
	}

	var rules []escompliancemonitor.QueryRule
	err = json.Unmarshal([]byte(complianceMonitor.Rule), &rules)
	if err != nil {
		return errors.New(fmt.Sprintf("compliance_monitor_task Exec json.Unmarshal err:%s", err))
	}

	query := escompliancemonitor.NewComplianceMonitorTaskRecords().BuildESQuery(rules, complianceMonitor.RuleType)
	switch complianceMonitor.AssetType {
	case asset.NetworkTypeInternal:
		query.Must(elastic.NewTermQuery("network_type", asset.NetworkTypeInternal))
	case asset.NetworkTypeExternal:
		query.Must(elastic.NewTermQuery("network_type", asset.NetworkTypeExternal))
	}

	result, total := assets.NewAssets().FindAllByQuery(context.Background(), query, nil)
	// 将查询对象转换为序列化格式
	queryInterface, _ := query.Source()
	// 将查询对象序列化为 JSON
	queryByte, _ := json.MarshalIndent(queryInterface, "", "  ")

	// 打印最终的 JSON 查询
	logs.GetLogger().Infof("exec monitorTask assets.FindAllByQuery query=%s", string(queryByte))
	logs.GetLogger().Infof("exec monitorTask assets.FindAllByQuery total=%d", total)

	err = compliance_monitor_task.NewComplianceMonitorTaskModel().Update(compliance_monitor_task.ComplianceMonitorTask{
		BaseModel:     mysql.BaseModel{Id: monitorTask.Id},
		AbnormalAsset: uint64(total),
	})
	if err != nil {
		return errors.New(fmt.Sprintf("compliance_monitor_task Exec Update.AbnormalAsset err:%s", err))
	}

	err = compliance_monitor.NewComplianceMonitorModel().Update(compliance_monitor.ComplianceMonitor{
		BaseModel:         mysql.BaseModel{Id: monitorTask.ComplianceMonitorId},
		LastTime:          localtime.NewLocalTime(time.Now()),
		LastAbnormalAsset: uint64(total),
	})

	if err != nil {
		return errors.New(fmt.Sprintf("compliance_monitor_task Exec Update.LastAbnormalAsset err:%s", err))
	}
	if total > 0 {
		for _, resultAsset := range result {
			bulkRequest := es.GetEsClient().Bulk()
			complianceMonitorRecord := escompliancemonitor.NewComplianceMonitorTaskRecords()

			// 根据任务模式决定数据处理方式
			var sourceAsset *assets.Assets
			if complianceMonitor.TaskModel == compliance_monitor.TaskModelList {
				// 列表模式：过滤数据，只保留匹配的端口/组件/协议数据
				sourceAsset = complianceMonitorRecord.FilterAssetDataForListMode(resultAsset, rules, complianceMonitor.RuleType)
			} else {
				// 标记模式：复制完整资产数据（前端负责高亮显示）
				sourceAsset = resultAsset
			}
			// 保存资产id
			complianceMonitorRecord.AssetId = sourceAsset.Id
			// 生成去重ID：ip+任务规则ComplianceMonitorId的哈希值
			uniqueKey := fmt.Sprintf("%s_%d", resultAsset.Ip, monitorTask.ComplianceMonitorId)
			complianceMonitorRecordId := utils.Md5sHash(uniqueKey, false)

			err = copier.Copy(&complianceMonitorRecord, &sourceAsset)
			if err != nil {
				return errors.New(fmt.Sprintf("compliance_monitor_task Exec copier.Copy err:%s", err))
			}
			complianceMonitorRecord.ComplianceMonitorTaskId = monitorTask.Id
			complianceMonitorRecord.ComplianceMonitorId = monitorTask.ComplianceMonitorId
			complianceMonitorRecord.ComplianceMonitorName = complianceMonitor.Name
			complianceMonitorRecord.Id = complianceMonitorRecordId

			// 保护现有记录的特定字段
			err = preserveProtectedFields(complianceMonitorRecord, complianceMonitorRecordId)
			if err != nil {
				logs.GetLogger().Warnf("保护字段失败: %v", err)
			}
			complianceMonitorRecord.DeletedAt = nil
			complianceMonitorRecord.CreatedAt = localtime.NewLocalTime(time.Now())
			complianceMonitorRecord.UpdatedAt = localtime.NewLocalTime(time.Now())
			last := localtime.Parse(localtime.TimeFormat, sourceAsset.LastResponseAt.String())
			sourceResponse := localtime.Parse(localtime.TimeFormat, sourceAsset.DataSourceResponseAt.String())
			// 文件导入的资产的sourceAsset.LastResponseAt是null 特判
			if last.Unix() > 0 {
				complianceMonitorRecord.LastResponseAt = &last
			}

			complianceMonitorRecord.DataSourceResponseAt = &sourceResponse

			// 使用 upsert 操作：存在则更新，不存在则创建
			complianceMonitorRecordReq := elastic.NewBulkUpdateRequest().Index(complianceMonitorRecord.IndexName()).Id(complianceMonitorRecordId).Doc(complianceMonitorRecord).DocAsUpsert(true)
			bulkRequest = bulkRequest.Add(complianceMonitorRecordReq)

			complianceMonitorRecordBulkReq, err := bulkRequest.Refresh("true").Do(context.Background())
			if err != nil || complianceMonitorRecordBulkReq.Errors {
				if complianceMonitorRecordBulkReq != nil {
					for i, item := range complianceMonitorRecordBulkReq.Items {
						for op, e := range item {
							if e.Error != nil {
								// 如果有错误，打印出错误信息
								err = errors.New(fmt.Sprintf("Item %d, operation %s: error %s: %s\n", i, op, e.Error.Type, e.Error.Reason))
							}
						}
					}
				}
				return errors.New(fmt.Sprintf("compliance_monitor_task Exec Bulk.Do err:%s", err))
			}
		}
	}

	err = compliance_monitor_task.NewComplianceMonitorTaskModel().Update(compliance_monitor_task.ComplianceMonitorTask{
		BaseModel: mysql.BaseModel{Id: monitorTask.Id},
		Status:    compliance_monitor_task.StatusSuccess,
		EndAt:     localtime.NewLocalTime(time.Now()),
	})

	if err != nil {
		return errors.New(fmt.Sprintf("compliance_monitor_task Exec Update.StatusSuccess err:%s", err))
	}

	return nil
}

func Records(c *gin.Context, reqmonitor *reqmonitor.ShowRequest, isSuperManage bool, staffIds []string) (any, int64, error) {
	data := make([]any, 0)
	user := request.GetUserInfo(c)
	boolQuery := elastic.NewBoolQuery()
	if len(reqmonitor.SearchCondition) > 0 {
		conditions, err := filtrate.ParseQueryConditions(reqmonitor.SearchCondition)
		if err != nil {
			logger.Errorf("Records ParseQueryConditions 解析查询条件失败 %s", err.Error())
			return nil, 0, err
		}
		for _, condition := range conditions {
			if condition.Field == "ports.port" {
				portList := make([]string, 0)
				switch condition.Value.(type) {
				case string:
					portList = append(portList, condition.Value.(string))
				case []string:
					portList = condition.Value.([]string)
				case []interface{}:
					for _, v := range condition.Value.([]interface{}) {
						portList = append(portList, v.(string))
					}
				}
				for _, port := range portList {
					if !utils.IsValidatePort(port) {
						return nil, 0, errors.New("请输入正确的端口")
					}
				}
			}
			boolQuery = filtrate.BuildBoolQuery(condition.Field, condition.OperationTypeString, condition.LogicalConnective, condition.Value, boolQuery)
		}
	}

	if reqmonitor.Keyword != "" {
		boolQuery = escompliancemonitor.NewComplianceMonitorTaskRecords().NewKeywordQuery(reqmonitor.Keyword, boolQuery)
	}

	if reqmonitor.TaskId > 0 {
		boolQuery = boolQuery.Must(elastic.NewTermsQuery("compliance_monitor_task_id", reqmonitor.TaskId))
	}

	// if c != nil {
	// 	permissionQuery1, err := permission.NewPermissionService().ProcessElasticsearchQuery(c, data_source.SourceTypeInternetAsset)
	// 	if err != nil {
	// 		return nil, 0, err
	// 	}
	// 	permissionQuery2, err := permission.NewPermissionService().ProcessElasticsearchQuery(c, data_source.SourceTypeIntranetAsset)
	// 	if err != nil {
	// 		return nil, 0, err
	// 	}
	// 	if permissionQuery1 != nil || permissionQuery2 != nil {
	// 		shouldQuery := elastic.NewBoolQuery()
	// 		if permissionQuery1 != nil {
	// 			shouldQuery = shouldQuery.Should(permissionQuery1)
	// 		}
	// 		if permissionQuery2 != nil {
	// 			shouldQuery = shouldQuery.Should(permissionQuery2)
	// 		}
	// 		boolQuery = boolQuery.Must(shouldQuery)
	// 	}
	// }

	query := es.GetEsClient().Search(escompliancemonitor.NewComplianceMonitorTaskRecords().IndexName()).
		TrackTotalHits(true).
		Query(boolQuery)
	query = query.Sort("flow_status_change_time", false)
	query = query.Sort("updated_at", false)
	result, err := query.From(es.GetFrom(reqmonitor.Page, reqmonitor.PerPage)).
		Size(es.GetSize(reqmonitor.PerPage)).
		Do(context.TODO())
	if err != nil {
		return nil, 0, err
	}
	var assetsIds = make([]string, 0)
	var complianceMonitorIds = make([]uint64, 0)
	var rows = make([]escompliancemonitor.ComplianceMonitorTaskRecords, 0)
	for _, hit := range result.Hits.Hits {
		record := escompliancemonitor.ComplianceMonitorTaskRecords{}
		if err = json.Unmarshal(hit.Source, &record); err != nil {
			return nil, 0, err
		}
		assetsIds = append(assetsIds, record.Id)
		complianceMonitorIds = append(complianceMonitorIds, record.ComplianceMonitorId)
		rows = append(rows, record)
	}
	complianceMonitor, _, err := compliance_monitor.NewComplianceMonitorModel().List(0, 0, mysql.WithWhere("id in ?", complianceMonitorIds))
	if err != nil {
		return nil, 0, err
	}
	var mapComplianceMonitor = make(map[uint64]compliance_monitor.ComplianceMonitor)
	for _, v := range complianceMonitor {
		mapComplianceMonitor[v.Id] = v
	}
	complianceMonitor = nil

	// 收集所有需要查询的IP
	var ips []string
	for _, record := range rows {
		ips = append(ips, record.Ip)
	}

	// 处理记录
	for _, record := range rows {
		monitorItem, ok := mapComplianceMonitor[record.ComplianceMonitorId]
		if !ok {
			return data, 0, errors.New(fmt.Sprintf("compliance_monitor_task not exists:%d", record.ComplianceMonitorId))
		}
		var item res.ComplianceMonitorShow
		complianceRules := res.ComplianceRule{
			Ports:    make([]int, 0),
			Product:  make([]string, 0),
			Protocol: make([]string, 0),
			Ips:      make([]string, 0),
		}
		item.Id = record.Id
		item.Ip = record.Ip
		item.ComplianceMonitorId = record.ComplianceMonitorId
		item.ComplianceMonitorTaskId = record.ComplianceMonitorTaskId
		item.ComplianceMonitor = monitorItem
		item.Ports = record.Ports

		var noEmptyList = make([]*assets.RuleInfo, 0, len(record.RuleInfos))
		for _, ruleInfo := range record.RuleInfos {
			if ruleInfo != nil && ruleInfo.Product != "" {
				noEmptyList = append(noEmptyList, ruleInfo)
			}
		}
		item.RuleInfos = noEmptyList
		item.Product = record.Product
		item.Business = record.Business
		business := item.Business
		for i := range business {
			for j := range business[i].PersonBase {
				business[i].PersonBase[j].Pgid, _ = pgidservice.GetPgidById(business[i].PersonBase[j].Id)
			}
		}
		item.Business = business
		item.CreatedAt = record.CreatedAt
		item.DeviceNames = record.HostName
		item.AssetId = record.AssetId
		item.NetworkType = record.NetworkType // 网络类型
		item.CcPerson = record.CcPerson       // 抄送人
		item.PersonInfo = record.PersonInfo   // 修复负责人信息
		// 查询pgid
		for i, person := range item.PersonInfo {
			item.PersonInfo[i].Pgid, _ = pgidservice.GetPgidById(person.Id)
		}
		item.LimitDate = record.LimitDate   // 修复时间要求
		item.FlowStatus = record.FlowStatus // 流转状态
		item.Oper = func() []*assets.PersonBase {
			if record.OperInfo == nil || len(record.OperInfo) == 0 {
				return make([]*assets.PersonBase, 0)
			}
			for _, op := range record.OperInfo {
				op.Pgid, _ = pgidservice.GetPgidById(op.Id)
			}
			return record.OperInfo
		}()
		item.Admin = utils.IfNilToEmpty(func() []*pb.IpAdminInfoResponseItem {
			arr := make([]*pb.IpAdminInfoResponseItem, 0)
			for _, p := range record.Business {
				arr = append(arr, convertToPbBusinessSystem(p))
			}
			return arr
		}())
		// 原Merge service接口没看到返回这个字段
		item.OperDepartment = record.OperDepartment
		item.BusinessDepartment = record.BusinessDepartment
		var rules []escompliancemonitor.QueryRule
		err = json.Unmarshal([]byte(monitorItem.Rule), &rules)
		for _, val := range rules {
			if val.Whether == "in" || val.RuleName == "ip" {
				v := strings.Split(val.Content, ",")
				for _, l := range v {
					//处理端口
					if l != "" && val.RuleName == "port" {
						port, _ := strconv.Atoi(l)
						if !utils.ListContains(complianceRules.Ports, port) {
							complianceRules.Ports = append(complianceRules.Ports, port)
						}
					}
					//协议
					if l != "" && val.RuleName == "protocol" && !utils.ListContains(complianceRules.Protocol, l) {
						complianceRules.Protocol = append(complianceRules.Protocol, l)
					}
					//组件
					if l != "" && val.RuleName == "product" && !utils.ListContains(complianceRules.Product, l) {
						complianceRules.Product = append(complianceRules.Product, l)
					}
					if l != "" && val.RuleName == "ip" && !utils.ListContains(complianceRules.Ips, l) {
						complianceRules.Ips = append(complianceRules.Ips, l)
					}
				}
			}
		}
		item.ComplianceRules = &complianceRules
		recordItem, err := utils.StructToMap(item, "json")
		if err != nil {
			return nil, 0, err
		}
		recordItem["statusCode"] = recordItem["flow_status"]
		recordItem = threat.ActionType(recordItem, user, isSuperManage)
		data = append(data, recordItem)
	}

	return data, result.TotalHits(), nil
}

func convertToPbBusinessSystem(info *assets.Business) *pb.IpAdminInfoResponseItem {
	if info == nil {
		return nil
	}
	return &pb.IpAdminInfoResponseItem{
		System:   info.System,
		SystemId: info.SystemId,
		Owner:    info.Owner,
		OwnerId:  info.OwnerId,
		PersonBase: func() []*pb.PersonBase {
			arr := make([]*pb.PersonBase, 0)
			for _, p := range info.PersonBase {
				arr = append(arr, convertToPbPersonBase(p))
			}
			return arr
		}(),
		Reliability: int64(info.BusinessTrustedState),
	}
}

func convertToPbPersonBase(info *assets.PersonBase) *pb.PersonBase {
	if info == nil {
		return nil
	}
	return &pb.PersonBase{
		Id:   info.Id,
		Fid:  info.Fid,
		Name: info.Name,
		Pgid: func() string {
			if info.Id != "" {
				pgid, err := pgidservice.GetPgidById(info.Id, "%s-%s")
				if err != nil {
					return ""
				}
				return pgid
			}
			return ""
		}(),
		FindInfo: func() []*pb.PersonFindInfo {
			rsp := make([]*pb.PersonFindInfo, 0)
			for _, v := range info.FindInfo {
				rsp = append(rsp, convertToPbPersonFindInfo(v))
			}
			return rsp
		}(),
		Department: func() []*pb.DepartmentBase {
			rsp := make([]*pb.DepartmentBase, 0)
			for _, v := range info.Department {
				rsp = append(rsp, convertToPbDepartmentBase(v))
			}
			return rsp
		}(),
	}
}

func convertToPbDepartmentBase(info *assets.DepartmentBase) *pb.DepartmentBase {
	if info == nil {
		return nil
	}
	return &pb.DepartmentBase{
		Id:                 info.Id,
		Name:               info.Name,
		BusinessSystemId:   info.BusinessSystemId,
		BusinessSystemName: info.BusinessSystemName,
		UserId:             info.UserId,
		UserName:           info.UserName,
		Parents: func() []*pb.DepartmentBase {
			if len(info.Parents) > 0 {
				arr := make([]*pb.DepartmentBase, 0)
				for _, v := range info.Parents {
					arr = append(arr, convertToPbDepartmentBase(v))
				}
				return arr
			}
			return make([]*pb.DepartmentBase, 0)
		}(),
	}
}

func convertToPbPersonFindInfo(info *assets.PersonFindInfo) *pb.PersonFindInfo {
	if info == nil {
		return nil
	}
	return &pb.PersonFindInfo{
		SourceId:     info.SourceId,
		NodeId:       info.NodeId,
		SourceValue:  info.SourceValue,
		MappingField: info.MappingField,
		FindCount:    int32(info.FindCount),
	}
}

func RecordsExport(c *gin.Context, reqmonitor *reqmonitor.ExportRequest) (string, error) {
	header := []string{
		"IP地址",
		"命中规则",
		"端口",
		"协议",
		"组件信息",
		"关联实体设备",
		"状态",
		"风险修复负责人",
		"抄送人",
		"超期时间",
		"业务系统",
		"业务系统负责人",
		"业务部门",
		"运维人员",
		"运维部门",
	}

	boolQuery := elastic.NewBoolQuery()

	if reqmonitor.Keyword != "" {
		boolQuery = escompliancemonitor.NewComplianceMonitorTaskRecords().NewKeywordQuery(reqmonitor.Keyword, boolQuery)
	}

	if reqmonitor.TaskId > 0 {
		boolQuery = boolQuery.Must(elastic.NewTermsQuery("compliance_monitor_task_id", reqmonitor.TaskId))
	}
	idsNoZero := utils.ListDistinctNonZero(reqmonitor.Ids)
	if len(idsNoZero) > 0 {
		boolQuery = boolQuery.Must(elastic.NewTermsQueryFromStrings("id", idsNoZero...))
	}

	// // 增加权限过滤
	// permissionQuery, err := permission.NewPermissionService().ProcessElasticsearchQuery(c, data_source.SourceTypeInternetAsset)
	// if err != nil {
	// 	return "", err
	// }
	// permissionQuery2, err := permission.NewPermissionService().ProcessElasticsearchQuery(c, data_source.SourceTypeIntranetAsset)
	// if err != nil {
	// 	return "", err
	// }
	// if permissionQuery != nil || permissionQuery2 != nil {
	// 	shouldQuery := elastic.NewBoolQuery()
	// 	if permissionQuery != nil {
	// 		shouldQuery = shouldQuery.Should(permissionQuery)
	// 	}
	// 	if permissionQuery2 != nil {
	// 		shouldQuery = shouldQuery.Should(permissionQuery2)
	// 	}
	// 	boolQuery = boolQuery.Must(shouldQuery)
	// }

	// 初始化滚动搜索请求
	client := es.GetEsClient()

	scroll := client.Scroll().Index(escompliancemonitor.NewComplianceMonitorTaskRecords().IndexName()).
		Query(boolQuery).Sort("updated_at", false).Size(1000).Scroll("1m")
	defer scroll.Clear(context.Background())
	var filePath string
	var datum [][]interface{}
	totalFetched := 0 // 已拉取的记录数

	for {
		result, err := scroll.Do(context.TODO())
		if errors.Is(err, io.EOF) {
			err = nil
			break
		}

		if err != nil {
			return "", err
		}

		if len(result.Hits.Hits) == 0 {
			break
		}

		for _, hit := range result.Hits.Hits {
			complianceMonitorTaskRecords := escompliancemonitor.ComplianceMonitorTaskRecords{}
			if err = json.Unmarshal(hit.Source, &complianceMonitorTaskRecords); err != nil {
				return "", err
			}
			ports := ""
			protocol := ""
			for _, port := range complianceMonitorTaskRecords.Ports {
				if ports == "" {
					ports = fmt.Sprintf("%d", port.Port)
				} else {
					ports = fmt.Sprintf("%s,%d", ports, port.Port)
				}

				if protocol == "" {
					protocol = fmt.Sprintf("%s", port.Protocol)
				} else {
					protocol = fmt.Sprintf("%s,%s", protocol, port.Protocol)
				}
			}
			monitorItem, err := compliance_monitor.NewComplianceMonitorModel().First(mysql.WithWhere("id = ?", complianceMonitorTaskRecords.ComplianceMonitorId))
			if err != nil {
				return "", err
			}

			var t = assets.Assets{
				RuleInfos:          complianceMonitorTaskRecords.RuleInfos,
				Business:           complianceMonitorTaskRecords.Business,
				OperInfo:           complianceMonitorTaskRecords.OperInfo,
				OperDepartment:     complianceMonitorTaskRecords.OperDepartment,
				BusinessDepartment: complianceMonitorTaskRecords.BusinessDepartment,
			}
			// 处理抄送人
			var ccPerson string
			if complianceMonitorTaskRecords.CcPerson != nil {
				if fid, ok := complianceMonitorTaskRecords.CcPerson["fid"]; ok {
					pgid, err := pgidservice.GetPgidByFid(fid)
					if err == nil {
						ccPerson = pgid
					}
				}
			}
			var flowStatusDesc string
			if v, ok := poc.PocStatusRelations[complianceMonitorTaskRecords.FlowStatus]; ok {
				flowStatusDesc = v
			}
			// 业务系统风险修复负责人
			// 获取pgids
			var pgids []string
			for _, person := range complianceMonitorTaskRecords.PersonInfo {
				pgid, err := pgidservice.GetPgidById(person.Id)
				if err == nil {
					pgids = append(pgids, pgid)
				} else {
					logger.Errorf("获取pgid失败: %v", err)
					pgids = append(pgids, "")
				}

			}
			datum = append(datum, []interface{}{
				complianceMonitorTaskRecords.Ip,
				monitorItem.Name,
				ports,
				protocol,
				strings.Join(t.RuleProduct(), ","),
				strings.Join(getDeviceNamesByIp(complianceMonitorTaskRecords.Ip), ","),
				flowStatusDesc,                         // 状态
				strings.Join(pgids, ","),               // 风险修复负责人
				ccPerson,                               // 抄送人
				complianceMonitorTaskRecords.LimitDate, // 超期时间字段
				t.BusinessSystems(),
				t.BusinessOwners(),
				t.BusinessDepartments(),
				strings.Join(t.OperStringSlice(), ","),
				t.OperDepartments(),
			})

			totalFetched++

		}
	}

	if len(datum) == 0 {
		return "", errors.New("导出的数据不存在")
	}

	// 生成文件路径
	filePath = time.Now().Format("20060102150405") + "-合规监测.xlsx"
	if _, err := utils.WriterExcel(filePath, header, datum); err != nil {
		return "", err
	}

	return filePath, nil
}

// 提取PersonInfo中所有人员的姓名
func getPersonInfoNames(personInfo []*assets.PersonBase) string {
	if personInfo == nil || len(personInfo) == 0 {
		return ""
	}

	var names []string
	for _, person := range personInfo {
		if person != nil && person.Name != "" {
			names = append(names, person.Name)
		}
	}

	return strings.Join(names, ",")
}
func getDeviceNamesByIp(ip string) []string {

	boolQuery := elastic.NewBoolQuery()
	boolQuery = boolQuery.Must(elastic.NewTermsQuery("ip", ip))

	result, err := es.GetEsClient().Search(esmodel_device.NewDeviceModel().IndexName()).
		Query(boolQuery).
		Do(context.TODO())

	deviceNames := make([]string, 0)
	for _, hit := range result.Hits.Hits {
		device := esmodel_device.Device{}
		if err = json.Unmarshal(hit.Source, &device); err != nil {
			return deviceNames
		}
		deviceNames = append(deviceNames, device.HostName...)
	}

	return deviceNames
}

func countDevicesByComplianceMonitorTaskId(reqmonitor *reqmonitor.ShowRequest) int64 {
	if reqmonitor.TaskId <= 0 {
		return 0
	}

	boolQuery := elastic.NewBoolQuery()

	if reqmonitor.Keyword != "" {
		boolQuery = escompliancemonitor.NewComplianceMonitorTaskRecords().NewKeywordQuery(reqmonitor.Keyword, boolQuery)
	}

	if reqmonitor.TaskId > 0 {
		boolQuery = boolQuery.Must(elastic.NewTermsQuery("compliance_monitor_task_id", reqmonitor.TaskId))
	}

	result, _ := escompliancemonitor.NewComplianceMonitorTaskRecords().FindAllByQuery(context.Background(), boolQuery)
	var ips []interface{}
	for _, complianceMonitorTaskRecord := range result {
		ips = append(ips, complianceMonitorTaskRecord.Ip)
	}

	devices, _ := es.GetEsClient().Search(esmodel_device.NewDeviceModel().IndexName()).
		Query(elastic.NewTermsQuery("ip", ips...)).
		Do(context.TODO())

	return devices.TotalHits()
}

func getFieldNumByQuery(field string, boolQuery *elastic.BoolQuery) int64 {
	var num int64
	// 定义聚合查询
	aggregation := elastic.NewTermsAggregation().Field(field).Size(10000) // 聚合端口并限制返回10个结果
	// 执行查询
	searchResult, _ := es.GetEsClient().Search().
		Index(escompliancemonitor.NewComplianceMonitorTaskRecords().IndexName()). // 设置索引
		Query(boolQuery).                                                         // 查询所有记录
		Aggregation("field_count", aggregation).                                  // 使用聚合
		Do(context.TODO())                                                        // 执行查询

	// 初始化字段计数映射
	fieldCountMap := make(map[string]int64)

	// 解析聚合结果
	if fieldCount, found := searchResult.Aggregations.Terms("field_count"); found {
		for _, bucket := range fieldCount.Buckets {
			// 将 float64 转换为 string
			key := fmt.Sprintf("%v", bucket.Key)
			fieldCountMap[key] = bucket.DocCount
		}
	}

	return num
}

func getNestedFieldNumByQuery(field string, boolQuery *elastic.BoolQuery) int64 {
	var num int64
	// 定义嵌套聚合查询
	nestedAgg := elastic.NewNestedAggregation().Path("business")
	termsAgg := elastic.NewTermsAggregation().Field("business." + field).Size(10000)
	// 将termsAgg作为子聚合添加到nestedAgg中
	nestedAgg.SubAggregation("field_count", termsAgg)

	// 执行查询
	searchResult, _ := es.GetEsClient().Search().
		Index(escompliancemonitor.NewComplianceMonitorTaskRecords().IndexName()).
		Query(boolQuery).
		Aggregation("nested_field_count", nestedAgg).
		Do(context.TODO())

	// 解析聚合结果
	aggResult, _ := searchResult.Aggregations.Nested("nested_field_count")
	portCount, _ := aggResult.Terms("field_count")

	// 输出端口统计结果
	for _, bucket := range portCount.Buckets {
		if bucket.Key == "[]" || bucket.Key == "" {
			continue
		}

		num = num + bucket.DocCount
	}

	return num
}

// preserveProtectedFields 保护现有记录的特定字段不被覆盖
func preserveProtectedFields(record *escompliancemonitor.ComplianceMonitorTaskRecords, recordId string) error {
	// 查询现有记录
	searchResult, err := es.GetEsClient().Search().
		Index(record.IndexName()).
		Query(elastic.NewTermQuery("id", recordId)).
		Size(1).
		Do(context.Background())

	if err != nil {
		return err
	}

	// 如果记录不存在，无需保护
	if searchResult.TotalHits() == 0 {
		return nil
	}

	// 解析现有记录
	hit := searchResult.Hits.Hits[0]
	var existingRecord escompliancemonitor.ComplianceMonitorTaskRecords
	err = json.Unmarshal(hit.Source, &existingRecord)
	if err != nil {
		return err
	}

	// 保护特定字段：只有当现有记录中这些字段有值时才保护
	if existingRecord.FlowStatus != 0 {
		record.FlowStatus = existingRecord.FlowStatus
	}
	if existingRecord.PersonInfo != nil && len(existingRecord.PersonInfo) > 0 {
		record.PersonInfo = existingRecord.PersonInfo
	}
	if existingRecord.PersonDepartment != nil && len(existingRecord.PersonDepartment) > 0 {
		record.PersonDepartment = existingRecord.PersonDepartment
	}
	if existingRecord.CcPerson != nil && len(existingRecord.CcPerson) > 0 {
		record.CcPerson = existingRecord.CcPerson
	}
	if existingRecord.LimitDate != nil {
		record.LimitDate = existingRecord.LimitDate
	}
	if existingRecord.ProcessedTime != nil {
		record.ProcessedTime = existingRecord.ProcessedTime
	}
	if existingRecord.FlowStatusChangeTime != nil {
		record.FlowStatusChangeTime = existingRecord.FlowStatusChangeTime
	}

	return nil
}

type RecordsHistoryRequest struct {
	Reqmonitor *reqmonitor.ShowRequest
	RecordId   string `form:"id" json:"id" uri:"id"` // 新增字段

}

type RecordsHistoryResponse struct {
	Id         uint64 `json:"id"`
	Descrition string `json:"descrition"`
	Operation  string `json:"operation"`
	CreatedAt  string `json:"created_at"`
}

func RecordsHistory(recordsHistory *RecordsHistoryRequest) (any, int64, error) {
	data := make([]RecordsHistoryResponse, 0)
	var opts []mysql.HandleFunc
	// 如果传了 record_id，就加上过滤条件
	if recordsHistory.RecordId != "" {
		opts = append(opts, func(tx *gorm.DB) {
			tx.Where("record_id = ?", recordsHistory.RecordId)
		})
	}
	opts = append(opts, func(tx *gorm.DB) {
		tx.Order("id DESC")
	})
	list, total, err := compliance_risks.NewComplianceRisksHistoryModel().List(recordsHistory.Reqmonitor.Page, recordsHistory.Reqmonitor.PerPage, opts...)
	if err != nil {
		return data, total, err
	}
	for _, task := range list {
		monitorItem := RecordsHistoryResponse{
			Id:         task.Id,
			Descrition: task.Description,
			Operation:  task.Operation,
			CreatedAt:  task.CreatedAt.Format("2006-01-02 15:04:05"),
		}
		data = append(data, monitorItem)
	}
	return data, total, err
}
