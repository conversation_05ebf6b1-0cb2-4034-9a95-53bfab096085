package proactive_scan

import (
	"errors"
	"fobrain/pkg/filter_scan_ips"
	"strings"
	"time"

	"gorm.io/gorm"

	"fobrain/fobrain/app/repository/proactive_task/source_task"
	"fobrain/fobrain/common/localtime"
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/proactive_task_node_relations"
	"fobrain/models/mysql/system_configs"
	"fobrain/models/mysql/task"
)

const (
	ExecuteOnce   = 1 // 立即执行
	ExecuteOnceAt = 2 // 仅执行一次
	RunEveryDay   = 3 // 每天执行
	RunEveryWeek  = 4 // 每周执行
	RunEveryMonth = 5 // 每月执行

)

func ScanTool() (any, error) {
	return data_source.NewSourceModel().TaskSyncSource()
}

// ScanNode 扫描节点
// @param sourceId int 数据源ID
// @return any, error
func ScanNode(sourceId int, modeId int) (any, error) {
	return data_source.NewNodeModel().GetBySourceId(uint64(sourceId), modeId, "id", "name")
}

// 扫描计划：1.立即执行、2.仅执行一次、3.每天、4.每周、5.每月
var needRepeadEndTimePlan = map[int]struct{}{
	3: {},
	4: {},
	5: {},
}

func SaveTask(
	id *int64, name, desc string, taskType, sourceId, scanPlan int, scanPeriod, scanTime, repeatEndTime string,
	scanMode int, scanIpRangeType string, scanIpRanges []string, pocScanType, selectedPocs string, scanPort int,
	scanType string, bandwidth int, concurrency int, otherCfgs map[string]any, dataNodeIds []int, userId uint64) (int, error, int) {

	_, needOk := needRepeadEndTimePlan[scanPlan]
	if needOk && repeatEndTime == "" {
		return 0, errors.New("时间为空"), 0
	}
	conf, errBan := system_configs.NewSystemConfigs().GetScanBanConfig()
	if errBan != nil {
		return 0, errors.New("禁扫时间配置异常！"), 0
	}
	if system_configs.NewSystemConfigs().ScanTimeIsBan(conf, scanPlan, scanPeriod, scanTime) {
		return 0, errors.New("任务扫描时间在禁扫时间内，请修改扫描时间！"), 0
	}
	scanStatus := -1
	oldLengthIpRanges := len(scanIpRanges)
	newLengthIpRanges := len(scanIpRanges)
	result := scanIpRanges

	db := task.NewProactiveTasks().DB
	var proactiveTaskId int
	if !strings.Contains(repeatEndTime, " ") {
		repeatEndTime = repeatEndTime + " 00:00:00"
	}

	err := db.Transaction(func(tx *gorm.DB) error {
		proactiveTask := task.ProactiveTasks{
			Name:            name,
			Desc:            desc,
			TaskType:        taskType,
			SourceId:        sourceId,
			ScanPlan:        scanPlan,
			ScanPeriod:      scanPeriod,
			ScanTime:        scanTime,
			RepeatEndTime:   localtime.Parse(time.DateTime, repeatEndTime),
			ScanMode:        scanMode,
			ScanIPRangeType: scanIpRangeType,
			ScanIPRanges:    result,
			PocScanType:     pocScanType,
			SelectedPocs:    selectedPocs,
			ScanPort:        scanPort,
			ScanType:        scanType,
			Bandwidth:       bandwidth,
			Concurrency:     concurrency,
			OtherCfgs:       otherCfgs,
			Status:          scanStatus,
			UserId:          int(userId),
			IsStart:         1,
		}

		failNodeErrs := source_task.New(&proactiveTask).VerifyTaskParams(dataNodeIds)
		if len(failNodeErrs) > 0 {
			return failNodeErrs[0]
		}

		if conf.EnableBan {
			result = filter_scan_ips.FilterScanIps(scanIpRanges) // 过滤禁扫IP
			newLengthIpRanges = len(result)
			if oldLengthIpRanges-newLengthIpRanges > 0 {
				if len(result) == 0 {
					if scanPlan == 1 && id == nil { // 立即执行且为新增任务
						scanStatus = 5
					}
					return errors.New("扫描目标中存在禁扫资产！")
				}
			}
		}

		proactiveTask.ScanIPRanges = result

		if id != nil { // 更新操作
			proactiveTask.BaseModel.Id = uint64(*id)
			var createdAt time.Time
			if err := tx.Table(task.NewProactiveTasks().TableName()).Where("id = ?", *id).Pluck("created_at", &createdAt).Error; err != nil {
				return err
			}
			proactiveTask.CreatedAt = localtime.Time(createdAt)

			// 删除旧节点
			if err := tx.Table(proactive_task_node_relations.NewProactiveTaskNodeRelations().TableName()).Where("task_id = ?", *id).Delete(&proactive_task_node_relations.ProactiveTaskNodeRelations{}).Error; err != nil {
				return err
			}
		}

		if err := tx.Table(task.NewProactiveTasks().TableName()).Save(&proactiveTask).Error; err != nil {
			return err
		}

		proactiveTaskId = int(proactiveTask.Id) // 保存任务 ID ， 创建接口需要返回

		for _, nodeId := range dataNodeIds {
			pTR := proactive_task_node_relations.ProactiveTaskNodeRelations{
				TaskId: proactiveTaskId,
				NodeId: nodeId,
			}

			if err := tx.Table(proactive_task_node_relations.NewProactiveTaskNodeRelations().TableName()).Save(&pTR).Error; err != nil {
				return err
			}
		}

		return nil
	})

	if err != nil {
		return 0, err, 0
	}

	length := oldLengthIpRanges - newLengthIpRanges
	if length < 0 {
		length = 0
	}

	return proactiveTaskId, nil, length
}

func ScanTypes(nodeId, SourceId uint64) ([]map[string]any, error) {
	if SourceId != data_source.XRaySourceId {
		return []map[string]any{}, nil
	}

	return source_task.New(&task.ProactiveTasks{SourceId: data_source.XRaySourceId}).ScanTypes(nodeId)
}
