package proactive_scan

import (
	"fobrain/models/mysql/data_source"
)

// SplitIpsByBatchSize 按批次大小拆分IP
func SplitIpsByBatchSize(ips []string, batchSize int) [][]string {
	var batches [][]string

	for i := 0; i < len(ips); i += batchSize {
		end := i + batchSize
		if end > len(ips) {
			end = len(ips)
		}
		batch := ips[i:end]
		batches = append(batches, batch)
	}

	return batches
}

// GetMaxIpsPerTask 获取不同扫描工具的IP限制
func GetMaxIpsPerTask(sourceId int) int {
	switch sourceId {
	case data_source.FoeyeSourceId:
		return 5000
	case data_source.D01SourceId:
		return 5000
	case data_source.XRaySourceId:
		return 5000
	default:
		return 5000
	}
}
