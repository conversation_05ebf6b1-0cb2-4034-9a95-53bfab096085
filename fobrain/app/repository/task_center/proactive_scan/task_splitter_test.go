package proactive_scan

import (
	"fobrain/models/mysql/data_source"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestSplitIpsByBatchSize(t *testing.T) {
	t.Run("empty ips", func(t *testing.T) {
		ips := []string{}
		batches := SplitIpsByBatchSize(ips, 5000)

		assert.Len(t, batches, 0)
	})

	t.Run("ips less than batch size", func(t *testing.T) {
		ips := []string{"***********", "***********", "***********"}
		batches := SplitIpsByBatchSize(ips, 5000)

		assert.Len(t, batches, 1)
		assert.Len(t, batches[0], 3)
		assert.Equal(t, ips, batches[0])
	})

	t.Run("ips equal to batch size", func(t *testing.T) {
		ips := make([]string, 5000)
		for i := 0; i < 5000; i++ {
			ips[i] = "***********"
		}

		batches := SplitIpsByBatchSize(ips, 5000)

		assert.Len(t, batches, 1)
		assert.Len(t, batches[0], 5000)
	})

	t.Run("ips more than batch size", func(t *testing.T) {
		ips := make([]string, 12000)
		for i := 0; i < 12000; i++ {
			ips[i] = "***********"
		}

		batches := SplitIpsByBatchSize(ips, 5000)

		assert.Len(t, batches, 3)
		assert.Len(t, batches[0], 5000)
		assert.Len(t, batches[1], 5000)
		assert.Len(t, batches[2], 2000)
	})

	t.Run("ips slightly more than batch size", func(t *testing.T) {
		ips := make([]string, 5001)
		for i := 0; i < 5001; i++ {
			ips[i] = "***********"
		}

		batches := SplitIpsByBatchSize(ips, 5000)

		assert.Len(t, batches, 2)
		assert.Len(t, batches[0], 5000)
		assert.Len(t, batches[1], 1)
	})

	t.Run("small batch size", func(t *testing.T) {
		ips := []string{"***********", "***********", "***********", "***********", "***********"}
		batches := SplitIpsByBatchSize(ips, 2)

		assert.Len(t, batches, 3)
		assert.Len(t, batches[0], 2)
		assert.Len(t, batches[1], 2)
		assert.Len(t, batches[2], 1)

		assert.Equal(t, []string{"***********", "***********"}, batches[0])
		assert.Equal(t, []string{"***********", "***********"}, batches[1])
		assert.Equal(t, []string{"***********"}, batches[2])
	})
}

func TestGetMaxIpsPerTask(t *testing.T) {
	t.Run("foeye source", func(t *testing.T) {
		maxIps := GetMaxIpsPerTask(data_source.FoeyeSourceId)
		assert.Equal(t, 5000, maxIps)
	})

	t.Run("d01 source", func(t *testing.T) {
		maxIps := GetMaxIpsPerTask(data_source.D01SourceId)
		assert.Equal(t, 5000, maxIps)
	})

	t.Run("xray source", func(t *testing.T) {
		maxIps := GetMaxIpsPerTask(data_source.XRaySourceId)
		assert.Equal(t, 5000, maxIps)
	})

	t.Run("unknown source", func(t *testing.T) {
		maxIps := GetMaxIpsPerTask(999) // 未知的sourceId
		assert.Equal(t, 5000, maxIps)   // 应该返回默认值
	})
}

func TestSplitIpsRealScenarios(t *testing.T) {
	t.Run("10000 ips with 5000 batch size", func(t *testing.T) {
		// 模拟10000个IP的场景
		ips := make([]string, 10000)
		for i := 0; i < 10000; i++ {
			ips[i] = "***********"
		}

		batches := SplitIpsByBatchSize(ips, 5000)

		assert.Len(t, batches, 2)
		assert.Len(t, batches[0], 5000)
		assert.Len(t, batches[1], 5000)
	})

	t.Run("500000 ips with 5000 batch size", func(t *testing.T) {
		// 模拟50万个IP的场景
		ips := make([]string, 500000)
		for i := 0; i < 500000; i++ {
			ips[i] = "***********"
		}

		batches := SplitIpsByBatchSize(ips, 5000)

		assert.Len(t, batches, 100) // 500000 / 5000 = 100

		// 验证前几个和最后一个批次的大小
		for i := 0; i < 5; i++ {
			assert.Len(t, batches[i], 5000)
		}
		assert.Len(t, batches[99], 5000) // 最后一个批次也是5000
	})

	t.Run("7500 ips with 5000 batch size", func(t *testing.T) {
		// 模拟7500个IP的场景
		ips := make([]string, 7500)
		for i := 0; i < 7500; i++ {
			ips[i] = "***********"
		}

		batches := SplitIpsByBatchSize(ips, 5000)

		assert.Len(t, batches, 2)
		assert.Len(t, batches[0], 5000)
		assert.Len(t, batches[1], 2500)
	})
}
