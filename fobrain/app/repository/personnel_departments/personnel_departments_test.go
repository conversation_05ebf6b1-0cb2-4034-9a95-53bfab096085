package personnel_departments

import (
	"testing"

	res "fobrain/fobrain/app/response/personnel_departments"
)

func TestCalculateDepartmentTotals(t *testing.T) {
	// 创建一些测试数据
	departments := []res.List{
		{
			Id:     1,
			DevNum: 10,
			BppNum: 5,
			Children: []res.List{
				{
					Id:     2,
					DevNum: 3,
					BppNum: 2,
					Children: []res.List{
						{
							Id:     3,
							DevNum: 1,
							BppNum: 1,
						},
					},
				},
			},
		},
	}

	// 运行测试
	totalDevNum, totalBppNum := calculateDepartmentTotals(departments)

	// 验证结果
	if totalDevNum != 14 || totalBppNum != 8 {
		t.Errorf("计算结果不正确，期望: %d, %d，实际: %d, %d", 14, 8, totalDevNum, totalBppNum)
	}
}
