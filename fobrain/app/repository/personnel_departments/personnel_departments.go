package personnel_departments

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	goRedis "github.com/go-redis/redis/v8"
	"go-micro.dev/v4/logger"

	device "fobrain/fobrain/app/repository/asset_center/device_asset"
	req "fobrain/fobrain/app/request/personnel_departments"
	res "fobrain/fobrain/app/response/personnel_departments"
	"fobrain/fobrain/app/services/asset_center/business_systems"
	"fobrain/initialize/mysql"
	"fobrain/initialize/redis"
	"fobrain/mergeService/event"
	"fobrain/models/elastic/staff"
	models "fobrain/models/mysql/personnel_departments"
	"fobrain/pkg/utils"
)

func Import(rows [][]string, params *req.ImportRequest) ([]string, error) {
	errorStr := make([]string, 0)
	handlers := make([]mysql.HandleFunc, 0)
	list, _, err := models.NewPersonnelDepartmentsModel().List(0, 0, handlers...)
	if err != nil {
		return nil, err
	}
	dataStr := make([]string, 0)
	for _, department := range list {
		dataStr = append(dataStr, department.Name)
	}

	rows = rows[3:]
	for i, row := range rows {
		parentDepartment := strings.Split(row[1], "/")
		parentDepartment = append(parentDepartment, row[0])
		if hasDuplicate(parentDepartment, dataStr) {
			errorStr = append(errorStr, fmt.Sprintf("第%d行数据重复", i+1))
			continue
		}
		_ = batchCreate(parentDepartment, i)
	}
	go SetPersonnelDepartmentsRedis()
	return errorStr, nil
}

// hasDuplicate 判断文件数据是否重复
func hasDuplicate(fileData, data []string) bool {
	for _, strA := range fileData {
		for _, strB := range data {
			if strA == strB {
				return true
			}
		}
	}
	return false
}

// batchCreate 批量创建数据
func batchCreate(name []string, line int) map[string]string {
	parentId := uint64(0)
	regionTree := "0"
	parentName := ""
	fullName := ""
	ids := map[string]string{}
	for _, v := range name {
		fullName = v
		item := &models.PersonnelDepartments{
			Name:       v,
			ParentId:   parentId,
			RegionTree: regionTree,
			ParentName: parentName,
			FullName:   fullName,
			Level:      countElements(regionTree),
		}
		err := item.CreateItem(item)
		if err != nil {
			logger.Errorf("batchCreate name %s,line %d,error: %v", v, line, err)
			break
		}
		regionTree = item.RegionTree + "-" + strconv.FormatUint(item.ParentId, 10)
		parentName = item.FullName
		parentId = item.Id
	}
	return ids
}

func List(params *req.PersonnelDepartmentsRequest) ([]res.List, int64, error) {
	var err error
	handlers := make([]mysql.HandleFunc, 0)
	handlers = append(handlers, mysql.WithWhere("parent_id = ?", 0))
	childrenIds := make([]uint64, 0)
	if params.Keyword != "" {
		parentId, childrenId, err := keywordSearch(params.Keyword)
		if err != nil {
			return nil, 0, err
		}
		childrenIds = append(childrenIds, childrenId...)
		handlers = append(handlers, mysql.WithWhere("id in (?)", parentId))
	}
	list, total, err := models.NewPersonnelDepartmentsModel().List(params.Page, params.PerPage, handlers...)
	if err != nil {
		return nil, 0, err
	}

	// 获取人员台账中关联的部门信息
	departmentsList, err := staff.NewStaff().AggPersonnelDepartmentsList(context.Background())
	if err != nil {
		return nil, 0, err
	}
	//获取部门设备数量
	device, err := device.AggCountByDepartment(context.Background())
	if err != nil {
		return nil, 0, err
	}
	//获取部门BPP数量
	bpp, err := business_systems.NewService().AggCountByDepartment(context.Background())
	if err != nil {
		return nil, 0, err
	}
	children := make([]res.List, 0)
	if len(childrenIds) > 0 {
		children, err = keywordPersonnelDepartmentsChildren(list, childrenIds, departmentsList, device, bpp)
	} else {
		children, err = personnelDepartmentsChildren(list, departmentsList, device, bpp)
	}
	if err != nil {
		return nil, 0, err
	}
	return children, total, nil
}

func keywordSearch(keyword string) ([]uint64, []uint64, error) {
	parentId := make([]uint64, 0)
	childrenId := make([]uint64, 0)
	handlers := make([]mysql.HandleFunc, 0)
	handlers = append(handlers, mysql.WithWhere("full_name like ?", "%"+keyword+"%"))
	list, _, err := models.NewPersonnelDepartmentsModel().List(0, 0, handlers...)
	if err != nil {
		return nil, nil, err
	}
	for _, departments := range list {
		if departments.Level == 1 {
			parentId = append(parentId, departments.Id)
		}
		if departments.Level > 1 {
			ids := strings.Split(departments.RegionTree, "-")
			for i, id := range ids {
				num, err := strconv.ParseUint(id, 10, 64)
				if err != nil {
					logger.Errorf("strconv.ParseUint error: %v", err)
					continue
				}
				if i == 1 {
					parentId = append(parentId, num)
				}
				if i > 1 {
					childrenId = append(childrenId, num)
				}
			}
			childrenId = append(childrenId, departments.Id)
		}
	}
	return parentId, childrenId, nil
}

// AllTreeList 详情所有部门无限级分类列表
func AllTreeList() ([]res.List, error) {
	var staff map[string]int64
	var device map[string]int64
	var bpp map[string]int64
	handlers := make([]mysql.HandleFunc, 0)
	list, _, err := models.NewPersonnelDepartmentsModel().List(0, 0, handlers...)
	if err != nil {
		return nil, err
	}
	return treeList(list, 0, staff, device, bpp), nil
}

// personnelDepartmentsChildren 根据条件查询子类数据
func personnelDepartmentsChildren(list []*models.PersonnelDepartments, staff map[string]int64, device map[string]int64, bpp map[string]int64) ([]res.List, error) {
	var data []res.List
	for _, department := range list {
		handlers := make([]mysql.HandleFunc, 0)
		regionTree := department.RegionTree + "-" + strconv.FormatUint(department.Id, 10)
		handlers = utils.CanAppend(regionTree != "", handlers, mysql.WithLike("region_tree", "%"+regionTree))
		departments, _, err := models.NewPersonnelDepartmentsModel().List(0, 0, handlers...)
		if err != nil {
			return nil, err
		}
		list = append(list, departments...)
		t := []*models.PersonnelDepartments{department}
		t = append(t, departments...)
		data = append(data, treeList(t, 0, staff, device, bpp)...)
	}
	// 使用递归方式计算每个部门的设备和业务系统总数
	calculateDepartmentTotals(data)
	return data, nil
}

// keywordPersonnelDepartmentsChildren 根据条件查询子类数据
func keywordPersonnelDepartmentsChildren(list []*models.PersonnelDepartments, childrenId []uint64, staff map[string]int64, device map[string]int64, bpp map[string]int64) ([]res.List, error) {
	var data []res.List
	handlers := make([]mysql.HandleFunc, 0)
	handlers = append(handlers, mysql.WithWhere("id in (?)", childrenId))
	departments, _, err := models.NewPersonnelDepartmentsModel().List(0, 0, handlers...)
	if err != nil {
		return nil, err
	}
	list = append(list, departments...)

	data = treeList(list, 0, staff, device, bpp)
	// 使用递归方式计算每个部门的设备和业务系统总数
	calculateDepartmentTotals(data)
	return data, nil
}

// calculateDepartmentTotals 递归计算部门及其所有子部门的设备和业务系统总数
func calculateDepartmentTotals(departments []res.List) (int64, int64) {
	var totalDevNum, totalBppNum int64

	for i := range departments {
		// 先递归处理子部门
		childDevNum, childBppNum := calculateDepartmentTotals(departments[i].Children)

		// 当前部门的直接数量
		currentDevNum := departments[i].DevNum
		currentBppNum := departments[i].BppNum

		// 将子部门的数量加到当前部门的数量上
		departments[i].DevNum = currentDevNum + childDevNum
		departments[i].BppNum = currentBppNum + childBppNum

		// 累加到总数中（包含当前部门的直接数量和子部门的数量）
		totalDevNum += departments[i].DevNum
		totalBppNum += departments[i].BppNum
	}

	return totalDevNum, totalBppNum
}

// treeList 无限级分类列表
func treeList(list []*models.PersonnelDepartments, parentId uint64, staff map[string]int64, device map[string]int64, bpp map[string]int64) []res.List {
	var data []res.List
	for _, dept := range list {
		var staffCount int64
		if val, exists := staff[strconv.FormatUint(dept.Id, 10)]; exists {
			staffCount = val
		}

		if dept.ParentId == parentId {
			devNum := int64(0)
			bppNum := int64(0)
			if val, exists := device[strconv.FormatUint(dept.Id, 10)]; exists {
				devNum = val
			}
			if val, exists := bpp[strconv.FormatUint(dept.Id, 10)]; exists {
				bppNum = val
			}
			node := res.List{
				Id:        dept.Id,
				Name:      dept.Name,
				PerNum:    staffCount,
				DevNum:    devNum,
				BppNum:    bppNum,
				ParentId:  dept.ParentId,
				CreatedAt: dept.CreatedAt,
				UpdatedAt: dept.UpdatedAt,
				Children:  treeList(list, dept.Id, staff, device, bpp),
			}
			if len(node.Children) == 0 {
				node.Children = []res.List{}
			}
			data = append(data, node)
		}
	}
	return data
}

// Create 创建部门
func Create(params *req.InsertPersonnelDepartmentsRequest) error {
	countHandlers := make([]mysql.HandleFunc, 0)
	countHandlers = append(countHandlers, mysql.WithWhere("parent_id = ?", params.ParentId))
	countHandlers = append(countHandlers, mysql.WithWhere("name = ?", params.Name))
	count, err := models.NewPersonnelDepartmentsModel().Count(countHandlers...)
	if err != nil {
		return err
	}
	if count > 0 {
		msg := "同一个父级，相同层级不能重复"
		if params.ParentId == 0 {
			msg = "顶级单位部门不允许重复"
		}
		return errors.New(msg)
	}

	handlers := make([]mysql.HandleFunc, 0)
	handlers = append(handlers, mysql.WithWhere("id = ?", params.ParentId))
	parentInfo, err := models.NewPersonnelDepartmentsModel().First(handlers...)
	if err != nil && err.Error() != "record not found" {
		return err
	}

	regionTree := "0"
	fullName := params.Name
	parentName := ""
	if parentInfo != nil {
		regionTree = parentInfo.RegionTree + "-" + strconv.FormatUint(params.ParentId, 10)
		fullName = parentInfo.FullName + "/" + params.Name
		parentName = parentInfo.FullName
	}
	item := &models.PersonnelDepartments{
		Name:       params.Name,
		ParentId:   params.ParentId,
		RegionTree: regionTree,
		FullName:   fullName,
		ParentName: parentName,
		Source:     models.SourceManual,
		Level:      countElements(regionTree),
	}
	err = models.NewPersonnelDepartmentsModel().CreateItem(item)
	if err != nil {
		return err
	}
	go SetPersonnelDepartmentsRedis()
	return nil
}
func acquireLock(key string, expiration time.Duration, redis *goRedis.Client) bool {
	return redis.SetNX(context.Background(), key, "locked", expiration).Val()
}

func releaseLock(key string, redis *goRedis.Client) {
	redis.Del(context.Background(), key)
}

// GetOrCreateDepartmentTree 自动同步调用方法
func GetOrCreateDepartmentTree(hierarchy []string) ([]string, []string) {
	var tmp = make([]string, 0, len(hierarchy))
	for _, v := range hierarchy {
		t := strings.Split(v, "&")
		tmp = append(tmp, t...)
	}
	hierarchy = tmp
	redisClient := redis.GetRedisClient()
	for !acquireLock(models.UniqueWriteLockPersonnelDepartments, 10*time.Second, redisClient) {
		time.Sleep(100 * time.Millisecond)
	}
	defer releaseLock(models.UniqueWriteLockPersonnelDepartments, redisClient)

	departmentsIds := make([]string, 0)
	allDepartmentsIds := make([]string, 0)
	uniqueIds := make(map[string]bool)
	departments, err := redisClient.HGetAll(context.Background(), models.RedisPersonnelDepartmentsKey).Result()
	if err != nil && !errors.Is(err, goRedis.Nil) {
		logger.Errorf("get department tree from redis error: %v", err)
	}
	for _, s := range hierarchy {
		if val, exists := departments[s]; exists {
			ids := strings.Split(val, "-")
			count := len(ids)
			departmentsIds = append(departmentsIds, ids[count-1])
			for _, id := range ids {
				if !uniqueIds[id] && id != "0" {
					allDepartmentsIds = append(allDepartmentsIds, id)
					uniqueIds[id] = true
				}
			}
			continue
		}
		var parentID uint64 = 0     // 初始顶级部门的 parentID 为 0
		var regionRree string = "0" // 用于存储节点的 ID 路径
		var parentName, fullName string
		levels := strings.Split(s, "/")
		count := len(levels)
		for i, name := range levels {
			if name == "" {
				continue // 跳过空层级
			}
			fullName = parentName + "/" + name
			fullName = strings.TrimPrefix(fullName, "/")
			if val, exists := departments[fullName]; exists {
				ids := strings.Split(val, "-")
				parentID, _ = strconv.ParseUint(ids[len(ids)-1], 10, 64)
				regionRree = val
				parentName = fullName
				parentName = strings.TrimPrefix(parentName, "/")

				continue
			}
			dept := &models.PersonnelDepartments{
				Name:       name,
				ParentId:   parentID,
				RegionTree: regionRree,
				FullName:   fullName,
				ParentName: parentName,
				Level:      i + 1,
				Source:     models.SourceSync,
			}
			err = models.NewPersonnelDepartmentsModel().CreateItem(dept)
			if err != nil {
				logger.Errorf("GetOrCreateDepartmentTree error: %v", err)
				break
			}

			parentID = dept.Id
			idStr := strconv.FormatUint(dept.Id, 10)
			regionRree = dept.RegionTree + "-" + idStr
			parentName = dept.ParentName + "/" + name
			parentName = strings.TrimPrefix(parentName, "/")

			if count == i+1 {
				departmentsIds = append(departmentsIds, idStr)
			}

			if !uniqueIds[idStr] && idStr != "0" {
				allDepartmentsIds = append(allDepartmentsIds, idStr)
				uniqueIds[idStr] = true
			}
			redisClient.HSet(context.Background(), models.RedisPersonnelDepartmentsKey, fullName, regionRree)
		}
	}
	return departmentsIds, allDepartmentsIds
}

// countElements 拆分统计
func countElements(s string) int {
	parts := strings.Split(s, "-")
	return len(parts)
}

// First 详情信息
func First(params *req.FirstRequest) (*models.PersonnelDepartments, error) {
	handlers := make([]mysql.HandleFunc, 0)
	handlers = append(handlers, mysql.WithWhere("id = ?", params.Id))
	first, err := models.NewPersonnelDepartmentsModel().First(handlers...)
	if err != nil {
		return nil, err
	}
	return first, nil
}

// Update 更新部门
func Update(params *req.UpdatePersonnelDepartmentsRequest) error {
	countHandlers := make([]mysql.HandleFunc, 0)
	countHandlers = append(countHandlers, mysql.WithWhere("id != ?", params.Id))
	countHandlers = append(countHandlers, mysql.WithWhere("parent_id = ?", params.ParentId))
	countHandlers = append(countHandlers, mysql.WithWhere("name = ?", params.Name))
	count, err := models.NewPersonnelDepartmentsModel().Count(countHandlers...)
	if err != nil {
		return err
	}
	if count > 0 {
		return errors.New("该部门名称已存在")
	}

	handlers := make([]mysql.HandleFunc, 0)
	handlers = append(handlers, mysql.WithWhere("id = ?", params.ParentId))
	parentInfo, err := models.NewPersonnelDepartmentsModel().First(handlers...)
	if err != nil && err.Error() != "record not found" {
		return err
	}

	firstHandlers := make([]mysql.HandleFunc, 0)
	firstHandlers = append(firstHandlers, mysql.WithWhere("id = ?", params.Id))
	info, err := models.NewPersonnelDepartmentsModel().First(firstHandlers...)
	if err != nil {
		return err
	}

	regionTree := "0"
	fullName := params.Name
	parentName := ""
	if parentInfo != nil {
		regionTree = parentInfo.RegionTree + "-" + strconv.FormatUint(params.ParentId, 10)
		fullName = parentInfo.FullName + "/" + params.Name
		parentName = parentInfo.FullName
	}
	item := &models.PersonnelDepartments{
		BaseModel: mysql.BaseModel{
			Id: params.Id,
		},
		Name:       params.Name,
		ParentId:   params.ParentId,
		RegionTree: regionTree,
		FullName:   fullName,
		ParentName: parentName,
		Level:      countElements(regionTree),
	}
	err = models.NewPersonnelDepartmentsModel().Update(item)
	if err != nil {
		return err
	}
	sourceRegionTree := info.RegionTree + "-" + strconv.FormatUint(info.Id, 10)
	sourceParentName := info.FullName
	go batchUpdateNames(sourceRegionTree, sourceParentName, params.Name, info.Level)
	return nil
}

func batchUpdateNames(regionTree, sourceName, newName string, level int) {
	handlers := make([]mysql.HandleFunc, 0)
	handlers = utils.CanAppend(regionTree != "", handlers, mysql.WithLike("region_tree", "%"+regionTree))
	handlers = utils.CanAppend(sourceName != "", handlers, mysql.WithLike("parent_name", "%"+sourceName))

	list, _, err := models.NewPersonnelDepartmentsModel().List(0, 0, handlers...)
	if err != nil {
		logger.Errorf("batchUpdateNames error: %v", err)
		return
	}
	if len(list) > 0 {
		for _, departments := range list {
			fullName := processString(departments.FullName, level-1, newName)
			parentName := processString(departments.ParentName, level-1, newName)
			err = models.NewPersonnelDepartmentsModel().Update(&models.PersonnelDepartments{
				BaseModel: mysql.BaseModel{
					Id: departments.Id,
				},
				FullName:   fullName,
				ParentName: parentName,
				Level:      countElements(regionTree),
			})
			if err != nil {
				logger.Errorf("batchUpdateNames error: %v", err)
			}
		}
		go SetPersonnelDepartmentsRedis()
	}
}

func processString(input string, index int, replacement string) string {
	parts := strings.Split(input, "/")
	if index >= 0 && index < len(parts) {
		parts[index] = replacement
	}
	return strings.Join(parts, "/")
}

func DeleteByIds(params *req.DeleteByIdsRequest) error {
	ids := make([]uint64, 0)
	if len(params.Ids) > 0 {
		ids = params.Ids
	}
	if params.Keyword != "" {
		handlers := make([]mysql.HandleFunc, 0)
		handlers = append(handlers, mysql.WithWhere("full_name like ?", "%"+params.Keyword+"%"))
		list, _, err := models.NewPersonnelDepartmentsModel().List(0, 0, handlers...)
		if err != nil {
			return err
		}
		for _, item := range list {
			ids = append(ids, item.Id)
		}
	}

	countHandlers := make([]mysql.HandleFunc, 0)
	countHandlers = append(countHandlers, mysql.WithWhere("parent_id  in (?)", ids))
	countHandlers = append(countHandlers, mysql.WithWhere("id not in (?)", ids))
	count, err := models.NewPersonnelDepartmentsModel().Count(countHandlers...)
	if err != nil {
		return err
	}
	if count > 0 {
		return errors.New("该部门下存在子部门，无法删除")
	}

	listHandlers := make([]mysql.HandleFunc, 0)
	listHandlers = append(listHandlers, mysql.WithWhere("id  in (?)", ids))
	list, _, err := models.NewPersonnelDepartmentsModel().List(0, 0, listHandlers...)
	if err != nil {
		return err
	}
	departments := make([]string, 0)
	for _, item := range list {
		departments = append(departments, item.FullName)
	}

	err = models.NewPersonnelDepartmentsModel().DeleteByIds(ids)
	if err != nil {
		return err
	}
	go staff.NewStaff().DeleteByDepartments(departments, ids)
	go SetPersonnelDepartmentsRedis()
	go staff.NewStaff().CacheAllStaff(true)
	return nil
}

func CheckDel(params *req.DeleteByIdsRequest) (bool, string, error) {
	ids := make([]interface{}, 0)
	if len(params.Ids) > 0 {
		for _, id := range params.Ids {
			ids = append(ids, id)
		}
	}
	if params.Keyword != "" {
		handlers := make([]mysql.HandleFunc, 0)
		handlers = append(handlers, mysql.WithWhere("full_name like ?", "%"+params.Keyword+"%"))
		list, _, err := models.NewPersonnelDepartmentsModel().List(0, 0, handlers...)
		if err != nil {
			return false, "", err
		}
		for _, item := range list {
			ids = append(ids, item.Id)
		}
	}

	departments, _, err := staff.NewStaff().CountStaffDepartments(context.Background(), ids)
	if err != nil {
		return false, "", err
	}
	flag := true
	content := ""
	if departments > 0 {
		flag = false
		content = "当前删除的部门已关联人员信息，是否要删除"
	}
	return flag, content, nil
}

func SetPersonnelDepartmentsRedisHandler(nodeTaskInfo map[uint64][]uint64, sourceId, nodeId uint64, taskType, taskId, personTaskId string) error {
	logger.Infof("人员数据融合任务事件(%s)开始. nodeTaskInfo: %v, sourceId: %d, nodeId: %d, taskType: %s, taskId: %s, personTaskId: %s", event.Event_Person_MergeTask_Start, nodeTaskInfo, sourceId, nodeId, taskType, taskId, personTaskId)
	SetPersonnelDepartmentsRedis()
	return nil
}
func SetPersonnelDepartmentsRedis() {
	redisClient := redis.GetRedisClient()
	redisClient.Del(context.Background(), models.RedisPersonnelDepartmentsKey)

	handlers := make([]mysql.HandleFunc, 0)
	list, count, err := models.NewPersonnelDepartmentsModel().List(0, 0, handlers...)
	if err != nil {
		logger.Errorf("setPersonnelDepartmentsRedis error: %v", err)
	}
	// 有数据才会写入redis中
	if count > 0 {
		redisList := make(map[string]string)
		for _, departments := range list {
			redisList[departments.FullName] = departments.RegionTree + "-" + strconv.FormatUint(departments.Id, 10)
		}
		redisClient.HSet(context.Background(), models.RedisPersonnelDepartmentsKey, redisList)
		redisClient.Expire(context.Background(), models.RedisPersonnelDepartmentsKey, time.Hour*24)
	}
}
