package threat

import (
	"context"
	"errors"
	"fmt"
	"fobrain/fobrain/app/services/permission"
	"fobrain/fobrain/common/localtime"
	"fobrain/initialize/mysql"
	"fobrain/models/mysql/menu"
	"strconv"
	"time"

	"github.com/spf13/cast"
	"gorm.io/gorm"

	"fobrain/initialize/es"
	filtrate "fobrain/models/elastic"
	"fobrain/models/elastic/compliance_monitor"
	"fobrain/models/elastic/poc"
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/user"
	"fobrain/pkg/utils"

	"github.com/gin-gonic/gin"
	"github.com/olivere/elastic/v7"
)

var (
	PaiFa         = map[string]interface{}{"id": 1, "name": "派发", "operation_type": "paifa", "status": 10}
	ZhuanJiao     = map[string]interface{}{"id": 2, "name": "转交", "operation_type": "zhuanjiao", "status": 11}
	CuiCu         = map[string]interface{}{"id": 3, "name": "催促", "operation_type": "cuicu", "status": 16}
	YanShi        = map[string]interface{}{"id": 4, "name": "延时", "operation_type": "yanshi", "status": 12}
	WuBao         = map[string]interface{}{"id": 5, "name": "误报", "operation_type": "wubao", "status": 40}
	WuFaXiuFu     = map[string]interface{}{"id": 6, "name": "无法修复", "operation_type": "wufaxiufu", "status": 41}
	XiuFuWanCheng = map[string]interface{}{"id": 7, "name": "修复完成", "operation_type": "xiufuwancheng", "status": 17}
	FuCe          = map[string]interface{}{"id": 8, "name": "复测", "operation_type": "fuce", "status": 14}
)

func List(c *gin.Context, area []uint64, ip []string, keyword string, page, perPage int, user *user.User, params map[string]any, isSuper bool, staffFidHashArr []string) (any, int64, error) {
	boolQuery := elastic.NewBoolQuery()
	if keyword != "" {
		boolQuery = poc.NewPoc().NewKeywordQuery(keyword, boolQuery)
	}
	ip = utils.ListDistinctNonZero(ip)
	if len(ip) > 0 {
		boolQuery = boolQuery.Must(elastic.NewTermsQueryFromStrings("ip", ip...))
	}
	area = utils.ListDistinctNonZero(area)
	if len(area) > 0 {
		var areas []interface{}
		for _, a := range area {
			areas = append(areas, a)
		}
		boolQuery = boolQuery.Must(elastic.NewTermsQuery("area", areas...))
	}
	var err error
	boolQuery, err = BuildBoolQueryForThreat(params, boolQuery)
	if err != nil {
		return nil, 0, err
	}
	permissionQuery, err := permission.NewPermissionService().ProcessElasticsearchQuery(c, data_source.SourceTypePoc)
	if err != nil {
		return nil, 0, err
	}
	if permissionQuery != nil {
		boolQuery = boolQuery.Must(permissionQuery)
	}
	query := es.GetEsClient().Search(poc.NewPoc().IndexName()).
		Query(boolQuery).
		TrackTotalHits(true)

	// 用户指定的字段优先排序
	if params["field"].(string) != "" {
		if params["field"].(string) == "repair_priority" {
			params["field"] = "repair_priority.keyword"
		}

		if params["field"].(string) == "updated_at" {
			query = query.SortBy(
				es.TmpSortByWrongDate("updated_at", params["order"].(string) != "ascend"),
			)
		} else if params["field"].(string) == "created_at" {
			query = query.SortBy(
				es.TmpSortByWrongDate("created_at", params["order"].(string) != "ascend"),
			)
		} else {
			query = query.Sort(params["field"].(string), params["order"].(string) == "ascend")
		}
	}
	// 根据状态排序
	// 10 待修复
	// 11 待修复
	// 12 延时
	// 13 超时
	// 14 复测中
	// 15 复测未通过
	// 16 催促
	// 17 待复测
	// 0 新增
	// 1 复现
	// 30 复测通过
	// 40 误报
	// 41 无法修复

	statusSort := make(map[string]interface{})
	status := []int{10, 11, 12, 13, 14, 15, 16, 17, 0, 1, 41, 30, 40, 41}
	for ind, s := range status {
		statusSort[strconv.Itoa(s)] = ind
	}
	scriptSort := elastic.NewScriptSort(elastic.NewScript(`
		params.status[String.valueOf(doc['status'].value)]
		`).Param("status", statusSort), "number").
		Order(true)

	query = query.Sort("repair_priority.keyword", true)
	query = query.Sort("level", false)
	query = query.SortBy(scriptSort)
	query = query.Sort("updated_at", false)

	result, err := query.
		From(es.GetFrom(page, perPage)).
		Size(es.GetSize(perPage)).
		Do(context.TODO())

	if err != nil {
		return nil, 0, err
	}

	list := make([]any, 0)

	for _, hit := range result.Hits.Hits {
		poc := utils.ParseSampleHash(hit, poc.NewPoc())
		poc = ActionType(poc, user, isSuper)
		list = append(list, poc)
	}

	return list, result.TotalHits(), nil
}

func Show(c *gin.Context, id string) (any, error) {
	boolQuery := elastic.NewBoolQuery()
	boolQuery = boolQuery.Must(elastic.NewTermQuery("id", id))
	permissionQuery, err := permission.NewPermissionService().ProcessElasticsearchQuery(c, data_source.SourceTypePoc)
	if err != nil {
		return nil, err
	}
	if permissionQuery != nil {
		boolQuery = boolQuery.Must(permissionQuery)
	}
	result, err := es.GetEsClient().Search(poc.NewPoc().IndexName()).Query(boolQuery).Do(context.TODO())
	if err != nil {
		return nil, err
	}

	if len(result.Hits.Hits) == 0 {
		return nil, errors.New("数据不存在")
	}

	return utils.ParseSampleHash(result.Hits.Hits[0], poc.NewPoc()), nil
}

type ShowMultipleInfoCommon interface {
	NewKeywordQuery(keyword string, boolQuery *elastic.BoolQuery) *elastic.BoolQuery
	IndexName() string
}

func ShowMultiple(c *gin.Context, ids []string, paramList map[string]any, common ShowMultipleInfoCommon) ([]any, error) {
	boolQuery := elastic.NewBoolQuery()
	ids = utils.ListDistinctNonZero(ids)
	if len(ids) > 0 {
		boolQuery = boolQuery.Must(elastic.NewTermsQuery("id", stringToInterface(ids)...))
	}

	keyword := cast.ToString(paramList["keyword"])
	if keyword != "" {
		boolQuery = common.NewKeywordQuery(keyword, boolQuery)
	}
	var err error
	if common.IndexName() != compliance_monitor.NewComplianceMonitorTaskRecords().IndexName() {
		permissionQuery, err := permission.NewPermissionService().ProcessElasticsearchQuery(c, data_source.SourceTypePoc)
		if err != nil {
			return nil, err
		}
		if permissionQuery != nil {
			boolQuery = boolQuery.Must(permissionQuery)
		}
	}

	boolQuery, err = BuildBoolQueryForThreat(paramList, boolQuery)
	if err != nil {
		return nil, err
	}
	search := es.GetEsClient().
		Search(common.IndexName()).
		Query(boolQuery).
		From(es.GetFrom(1, 1000)).
		Sort("updated_at", false).
		Size(es.GetSize(1000))

	result, err := search.Do(context.TODO())

	if err != nil {
		return nil, err
	}

	if len(result.Hits.Hits) == 0 {
		return nil, errors.New("未找到任何数据")
	}

	// 解析所有返回的结果
	var results []any
	for _, hit := range result.Hits.Hits {
		results = append(results, utils.ParseSampleHash(hit, common))
	}

	return results, nil
}

// 辅助函数：将字符串切片转换为空接口切片
func stringToInterface(strs []string) []interface{} {
	interfaces := make([]interface{}, len(strs))
	for i, s := range strs {
		interfaces[i] = s
	}
	return interfaces
}

func BuildBoolQueryForThreat(params map[string]any, boolQuery *elastic.BoolQuery) (*elastic.BoolQuery, error) {
	// issue:3554
	// 未处理：包括新增(0)、复现(1)、待修复(10)、待修复-转交(11)、待复测(17)、复测中(14)、复测未通过(15)、延时(12)、超时未修复(13)
	// 已处理：包括复测通过(30)、误报(40)、无法修复(41)
	// 回收站：包括已删除的数据
	dataRange, ok := params["data_range"].(int)
	if !ok {
		dataRange = 1
	}
	if dataRange == 1 { // 未处理
		boolQuery = boolQuery.Must(elastic.NewTermsQuery("status", []interface{}{0, 1, 10, 11, 12, 13, 14, 15, 17}...))
		boolQuery = boolQuery.MustNot(elastic.NewExistsQuery("deleted_at"))
	} else if dataRange == 2 { // 已处理
		boolQuery = boolQuery.Must(elastic.NewTermsQuery("status", []interface{}{30, 40, 41}...))
		boolQuery = boolQuery.MustNot(elastic.NewExistsQuery("deleted_at"))
	} else if dataRange == 3 { // 回收站
		boolQuery = boolQuery.Must(elastic.NewExistsQuery("deleted_at"))
	}

	searchCondition := params["search_condition"].([]string)
	if len(searchCondition) > 0 {
		conditions, err := filtrate.ParseQueryConditions(searchCondition)
		if err != nil {
			return nil, fmt.Errorf("解析查询条件失败,%v", err)
		}
		for _, condition := range conditions {
			if condition.Field == "status" {
				switch condition.Value.(type) {
				case []interface{}:
					v := cast.ToStringSlice(condition.Value)
					if utils.ListContains(v, "10") || utils.ListContains(v, "11") {
						v = utils.ListDistinctNonZero(append(v, "10", "11"))
					}
					condition.Value = v
				case []string:
					var v = condition.Value.([]string)
					if utils.ListContains(v, "10") || utils.ListContains(v, "11") {
						v = utils.ListDistinctNonZero(append(v, "10", "11"))
					}
					condition.Value = v
				}
			}
			boolQuery = filtrate.BuildBoolQuery(condition.Field, condition.OperationTypeString, condition.LogicalConnective, condition.Value, boolQuery)
		}
	}

	return boolQuery, nil
}

var PocActionTypeMap = map[string]map[string]interface{}{
	"fuce_compliance_risks":          FuCe,          //复测
	"cuicu_compliance_risks":         CuiCu,         //催促
	"yanshi_compliance_risks":        YanShi,        //延时
	"zhuanjiao_compliance_risks":     ZhuanJiao,     //转交
	"xiufuwancheng_compliance_risks": XiuFuWanCheng, //修复完成
	"wufaxiufu_compliance_risks":     WuFaXiuFu,     //无法修复
	"wubao_compliance_risks":         WuBao,         //误报
	"paifa_compliance_risks":         PaiFa,         //派发
}

var PocStatusNewActionTypeMap = map[string]map[string]interface{}{
	"paifa_compliance_risks":         PaiFa,         //派发
	"wubao_compliance_risks":         WuBao,         //误报
	"wufaxiufu_compliance_risks":     WuFaXiuFu,     //无法修复
	"xiufuwancheng_compliance_risks": XiuFuWanCheng, //修复完成
}

var PocStatusBeRepairActionTypeMap = map[string]map[string]interface{}{
	"zhuanjiao_compliance_risks":     ZhuanJiao,
	"cuicu_compliance_risks":         CuiCu,
	"yanshi_compliance_risks":        YanShi,
	"wubao_compliance_risks":         WuBao,
	"wufaxiufu_compliance_risks":     WuFaXiuFu,
	"xiufuwancheng_compliance_risks": XiuFuWanCheng,
}

// ActionType 点击菜单
func ActionType(pocObj map[string]interface{}, u *user.User, isSuper bool) map[string]interface{} {
	// user 控制权限
	operations := []map[string]interface{}{}

	roleId := u.Role.Id
	var menusName = make([]string, 0)
	err := mysql.GetDbClient().Model(&menu.Menu{}).
		Select("menus.name").
		Joins("INNER JOIN roles_menus ON menus.id = roles_menus.menu_id").
		Where("roles_menus.role_id = ?", roleId).
		Pluck("name", &menusName).
		Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		pocObj["action_type"] = operations
		return pocObj
	}

	switch pocObj["statusCode"] {
	case poc.PocStatusOfNew, poc.PocStatusOfStillExist:
		if isSuper {
			operations = append(operations, PaiFa, WuBao, WuFaXiuFu, XiuFuWanCheng)
		} else {
			for _, v := range menusName {
				if action, ok := PocStatusNewActionTypeMap[v]; ok {
					operations = append(operations, action)
				}
			}
		}

	case poc.PocStatusOfBeRepair, poc.PocStatusOfForward, poc.PocStatusOfDelay, poc.PocStatusOfTimeout, poc.PocStatusOfNoRepair, poc.PocOperateOfUrge:
		if isSuper {
			operations = append(operations, ZhuanJiao, CuiCu, YanShi, WuBao, WuFaXiuFu, XiuFuWanCheng)
		} else {
			for _, v := range menusName {
				if action, ok := PocStatusBeRepairActionTypeMap[v]; ok {
					operations = append(operations, action)
				}
			}
		}
	case poc.PocStatusOfWaitRetest:
		if isSuper {
			operations = append(operations, FuCe)
		} else {
			for _, v := range menusName {
				if v == "fuce_compliance_risks" {
					operations = append(operations, FuCe)
				}
			}
		}
	case poc.PocStatusOfErrorReport, poc.PocStatusOfCantRepaired:
		if isSuper {
			operations = append(operations, PaiFa)
		} else {
			for _, v := range menusName {
				if v == "paifa_compliance_risks" {
					operations = append(operations, PaiFa)
				}
			}
		}
	case poc.PocStatusOfReRepairing, poc.PocStatusOfRepaired: // 复测中、已修复　不能再有操作。

	}

	pocObj["action_type"] = operations

	return pocObj
}

func GetOperaterName(statusCode int) string {
	all := []map[string]interface{}{
		PaiFa, ZhuanJiao, CuiCu, YanShi, WuBao, WuFaXiuFu, XiuFuWanCheng, FuCe,
	}

	for _, op := range all {
		if op["status"].(int) == statusCode {
			return op["name"].(string)
		}
	}
	return ""
}

func GetByQuery(query *elastic.BoolQuery, fields []string) ([]*poc.Poc, error) {
	search := es.GetEsClient().Search(poc.NewPoc().IndexName()).Query(query)
	if len(fields) > 0 {
		search = search.FetchSourceContext(elastic.NewFetchSourceContext(true).Include(fields...))
	}
	result, err := search.Do(context.TODO())
	if err != nil {
		return nil, err
	}

	list := make([]*poc.Poc, 0)
	for _, hit := range result.Hits.Hits {
		poc, err := poc.ConvertToPoc(hit)
		if err != nil {
			return nil, err
		}
		list = append(list, poc)
	}
	return list, nil
}

// DeleteToRecycleBin 删除到回收站
func DeleteToRecycleBin(c *gin.Context, ids []string, ip []string, keyword string, params map[string]any, isSuper bool, staffFidHashArr []string) error {
	// 构建bool查询
	boolQuery := elastic.NewBoolQuery()

	// 如果ids不为空，则添加id条件，否则添加高级筛选条件
	if len(ids) > 0 {
		boolQuery = boolQuery.Must(elastic.NewTermsQueryFromStrings("id", ids...))
	} else {
		if keyword != "" {
			boolQuery = poc.NewPoc().NewKeywordQuery(keyword, boolQuery)
		}
		ip = utils.ListDistinctNonZero(ip)
		if len(ip) > 0 {
			boolQuery = boolQuery.Must(elastic.NewTermsQueryFromStrings("ip", ip...))
		}
		var err error
		boolQuery, err = BuildBoolQueryForThreat(params, boolQuery)
		if err != nil {
			return err
		}
	}
	// 添加deleted_at条件
	boolQuery = boolQuery.MustNot(elastic.NewExistsQuery("deleted_at"))
	script := elastic.NewScriptInline("ctx._source.deleted_at = params.deleted_at").
		Param("deleted_at", localtime.NewLocalTime(time.Now()))
	permissionQuery, err := permission.NewPermissionService().ProcessElasticsearchQuery(c, data_source.SourceTypePoc)
	if err != nil {
		return err
	}
	if permissionQuery != nil {
		boolQuery = boolQuery.Must(permissionQuery)
	}
	_, err = es.GetEsClient().UpdateByQuery(poc.NewPoc().IndexName()).Query(boolQuery).Script(script).Do(context.TODO())
	return err
}

// DeleteRecycleBin 删除回收站数据
func DeleteRecycleBin(c *gin.Context, ids []string, ip []string, keyword string, params map[string]any, isSuper bool, staffFidHashArr []string) error {
	// 构建bool查询
	boolQuery := elastic.NewBoolQuery()

	// 如果ids不为空，则添加id条件，否则添加高级筛选条件
	if len(ids) > 0 {
		boolQuery = boolQuery.Must(elastic.NewTermsQueryFromStrings("id", ids...))
	} else {
		if keyword != "" {
			boolQuery = poc.NewPoc().NewKeywordQuery(keyword, boolQuery)
		}
		ip = utils.ListDistinctNonZero(ip)
		if len(ip) > 0 {
			boolQuery = boolQuery.Must(elastic.NewTermsQueryFromStrings("ip", ip...))
		}
		var err error
		boolQuery, err = BuildBoolQueryForThreat(params, boolQuery)
		if err != nil {
			return err
		}
	}
	boolQuery = boolQuery.Must(elastic.NewExistsQuery("deleted_at"))
	permissionQuery, err := permission.NewPermissionService().ProcessElasticsearchQuery(c, data_source.SourceTypePoc)
	if err != nil {
		return err
	}
	if permissionQuery != nil {
		boolQuery = boolQuery.Must(permissionQuery)
	}
	// 查询回收站数据
	recycleBin, err := es.All[poc.Poc](1000, boolQuery, nil, "id", "all_process_ids")
	if err != nil {
		return err
	}
	recycleBinIds := make([]string, 0)
	processIds := make([]string, 0)
	for _, poc := range recycleBin {
		recycleBinIds = append(recycleBinIds, poc.Id)
		processIds = append(processIds, poc.AllProcessIds...)
	}

	// 删除回收站数据
	_, err = es.GetEsClient().DeleteByQuery(poc.NewPoc().IndexName()).Refresh("true").ScrollSize(1000).Query(boolQuery).Do(context.TODO())
	if err != nil {
		return err
	}
	// 删除漏洞融合记录
	mergeRecordQuery := elastic.NewBoolQuery().Must(elastic.NewTermsQueryFromStrings("poc_id", recycleBinIds...))
	_, err = es.GetEsClient().DeleteByQuery(poc.NewPocRecord().IndexName()).ScrollSize(1000).Query(mergeRecordQuery).Do(context.TODO())
	if err != nil {
		return err
	}
	// 删除漏洞历史记录
	historyQuery := elastic.NewBoolQuery().Must(elastic.NewTermsQueryFromStrings("poc_id", recycleBinIds...))
	_, err = es.GetEsClient().DeleteByQuery(poc.NewMergeRecordsModel().IndexName()).ScrollSize(1000).Query(historyQuery).Do(context.TODO())
	if err != nil {
		return err
	}
	// 删除漏洞过程表
	processQuery := elastic.NewBoolQuery().Must(elastic.NewTermsQueryFromStrings("id", processIds...))
	_, err = es.GetEsClient().DeleteByQuery(poc.NewProcessPocModel().IndexName()).Refresh("true").ScrollSize(1000).Query(processQuery).Do(context.TODO())
	if err != nil {
		return err
	}

	return nil
}
