package threat

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"fobrain/fobrain/app/services/asset_center/business_systems"
	"fobrain/fobrain/common/request"
	"fobrain/initialize/es"
	"fobrain/models/elastic/assets"
	"fobrain/models/elastic/business_system"
	"fobrain/models/elastic/poc"
	"fobrain/models/mysql/data_source"
	"fobrain/pkg/utils"
	"sort"
	"strings"
	"time"

	"github.com/tidwall/gjson"

	pgidservice "fobrain/services/people_pgid"

	"github.com/olivere/elastic/v7"
	"github.com/pkg/errors"
)

type PocNameIPAggregation struct {
	ID           string `json:"id"`   // 漏洞名称编码值
	Name         string `json:"name"` // 漏洞名称
	VulType      string `json:"vul_type"`
	Level        string `json:"level"`
	RelevanceNum int64  `json:"relevance_num"`
	IsPoc        string `json:"is_poc"`
	Cve          string `json:"cve"`
	Cnvd         string `json:"cnvd"`
	Cnnvd        string `json:"cnnvd"`
	Describe     string `json:"describe"`
	Details      string `json:"details"`
	Suggestions  string `json:"suggestions"`
	UpdatedAt    string `json:"updated_at"`

	SourceNames []SourceName `json:"source_names"`
}

type SourceName struct {
	Id   uint64 `json:"id"`
	Name string `json:"name"`
	Icon string `json:"icon"`
}

type PocNameIPAggsListResponse struct {
	Total   int                     `json:"total"`
	Page    int                     `json:"page"`
	PerPage int                     `json:"per_page"`
	Items   []*PocNameIPAggregation `json:"items"`
}
type VulRelevanceListRequest struct {
	request.PageRequest
	Keyword string `json:"keyword" form:"keyword" uri:"keyword" validate:"omitempty,max=100" zh:"模糊搜索"`
	Field   string `json:"field" form:"field" uri:"field"`
	Order   string `json:"order" form:"order" uri:"order"`
}

func getAllSourceNameMap() (map[uint64]SourceName, error) {
	sources, _, err := data_source.AllSources()
	if err != nil {
		return nil, err
	}
	var sourceMap = make(map[uint64]SourceName)
	for _, s := range sources {
		sourceMap[s.Id] = SourceName{
			Id:   s.Id,
			Name: s.Name,
			Icon: s.Icon,
		}
	}
	return sourceMap, nil
}

func PocNameIPAggsList(req *VulRelevanceListRequest) (*PocNameIPAggsListResponse, error) {
	sourceMap, err := getAllSourceNameMap()
	if err != nil {
		return nil, err
	}
	alllist, err := poc.NewPoc().NameIPAggregationList(req.Keyword)
	if err != nil {
		return nil, err
	}
	if len(alllist) == 0 {
		return &PocNameIPAggsListResponse{
			Total:   0,
			Page:    req.Page,
			PerPage: req.PerPage,
			Items:   []*PocNameIPAggregation{},
		}, nil
	}
	if req.Order == "ascend" {
		sort.Slice(alllist, func(i, j int) bool {
			return alllist[i].Level < alllist[j].Level
		})
	} else {
		sort.Slice(alllist, func(i, j int) bool {
			return alllist[i].Level > alllist[j].Level
		})
	}

	pageList := make([]poc.NameIPAggregationListResult, 0, len(alllist))
	s, e, ok := utils.SlicePage(req.Page, req.PerPage, len(alllist))
	if !ok {
		pageList = append(pageList, alllist[0])
	} else {
		pageList = alllist[s:e]
	}
	var names = make([]interface{}, 0, len(pageList))
	var listMap = make(map[string]poc.NameIPAggregationListResult)
	var querySize int
	for _, v := range pageList {
		names = append(names, v.Name)
		querySize += int(v.DocCount)
		listMap[v.Name] = v
	}

	q := elastic.NewBoolQuery().Must(
		elastic.NewTermsQuery("name.keyword", names...),
	)

	result, err := es.GetEsClient().Search().
		Index(poc.NewPoc().IndexName()).
		Query(q).
		Size(querySize).
		Sort("level", false).
		Do(context.TODO())

	if err != nil {
		return nil, errors.WithMessage(err, "获取漏洞基本信息失败")
	}

	var nameSourceMap = make(map[string][]uint64, 0)
	var pocMap = make(map[string]*PocNameIPAggregation, len(pageList))
	var pocIndex = make([]string, 0)
	for _, hit := range result.Hits.Hits {
		pocSource := poc.Poc{}
		json.Unmarshal(hit.Source, &pocSource)
		if pocSource.Name == "" {
			continue
		}
		nameSourceMap[pocSource.Name] = append(nameSourceMap[pocSource.Name], pocSource.SourceIds...)
		if info, exists := pocMap[pocSource.Name]; exists {
			if info.VulType == "" && pocSource.VulType != "" {
				info.VulType = pocSource.VulType
			}
			if info.Describe == "" && pocSource.Describe != "" {
				info.Describe = pocSource.Describe
			}
			if info.Details == "" && pocSource.Details != "" {
				info.Details = pocSource.Details
			}
			if info.Suggestions == "" && pocSource.Suggestions != "" {
				info.Suggestions = pocSource.Suggestions
			}
			if info.Cve == "" && pocSource.Cve != "" {
				info.Cve = pocSource.Cve
			}
			//if info.Level == "未知" && pocSource.LevelDesc() != "未知" {
			//	info.Level = pocSource.LevelDesc()
			//}
			if info.IsPoc == "未知" && pocSource.IsPocDesc() != "未知" {
				info.IsPoc = pocSource.IsPocDesc()
			}
			continue
		}
		if pocMap[pocSource.Name] == nil {
			if pocSource.Name == "GEPON ONU设备默认口令" {
				level := pocSource.Level
				fmt.Println(pocSource, level, pocSource.LevelDesc())
			}
			pocMap[pocSource.Name] = &PocNameIPAggregation{
				ID:          base64.StdEncoding.EncodeToString([]byte(pocSource.Name)),
				Name:        pocSource.Name,
				VulType:     pocSource.VulType,
				Level:       pocSource.LevelDesc(),
				IsPoc:       pocSource.IsPocDesc(),
				Cve:         pocSource.Cve,
				Cnvd:        pocSource.Cnvd,
				Cnnvd:       pocSource.Cnnvd,
				Describe:    pocSource.Describe,
				Details:     pocSource.Details,
				Suggestions: pocSource.Suggestions,
				UpdatedAt:   pocSource.UpdatedAt.Format("2006-01-02 15:04:05"),
				SourceNames: []SourceName{},
			}
			pocIndex = append(pocIndex, pocSource.Name)
		}
	}

	var items = make([]*PocNameIPAggregation, 0, len(pageList))
	for _, v := range pageList {
		t, ok := pocMap[v.Name]
		if !ok {
			continue
		}
		t.RelevanceNum = v.UniqueIPCount
		sIds, ok := nameSourceMap[v.Name]
		if ok {
			ids := utils.ListDistinct(sIds)
			for _, id := range ids {
				if sourceInfo, ok := sourceMap[id]; ok {
					t.SourceNames = append(t.SourceNames, sourceInfo)
				}
			}
		}
		items = append(items, pocMap[v.Name])
	}
	return &PocNameIPAggsListResponse{
		Total:   len(alllist),
		Page:    req.Page,
		PerPage: req.PerPage,
		Items:   items,
	}, nil
}

func PocNameIPAggsListExport(ids []string) (string, error) {
	exportAll := false
	if len(ids) == 0 {
		exportAll = true
	}
	var dataList = make([][]interface{}, 0)
	var page = 1
	var perPage = 10000
	//  全部
	if exportAll {
		for {
			pocList, err := PocNameIPAggsList(&VulRelevanceListRequest{
				PageRequest: request.PageRequest{
					Page:    page,
					PerPage: perPage,
				},
				Keyword: "",
			})
			if err != nil {
				return "", err
			}

			for _, v := range pocList.Items {
				var str []string
				for _, sourcename := range v.SourceNames {
					str = append(str, sourcename.Name)
				}
				dataList = append(dataList, []interface{}{
					strings.Join(str, ","),
					v.Name,
					v.VulType,
					v.Level,
					v.RelevanceNum,
					v.IsPoc,
					v.Cve,
					v.Describe,
					v.Details,
				})
			}
			total := pocList.Total
			if page*perPage >= total {
				break
			}
			page++
		}
	} else {
		// 部分
		for _, v := range ids {
			name, err := utils.Base64Decode(v)
			if err != nil {
				continue
			}
			pocList, err := PocNameIPAggsList(&VulRelevanceListRequest{
				PageRequest: request.PageRequest{
					Page:    1,
					PerPage: 10,
				},
				Keyword: name,
			})
			if err != nil {
				return "", err
			}
			if len(pocList.Items) == 0 {
				continue
			}
			var poc *PocNameIPAggregation
			for _, v := range pocList.Items {
				if name == v.Name {
					poc = v
				}
			}
			if poc == nil {
				poc = pocList.Items[0]
			}
			var str []string
			for _, sourcename := range poc.SourceNames {
				str = append(str, sourcename.Name)
			}
			dataList = append(dataList, []interface{}{
				strings.Join(str, ","),
				poc.Name,
				poc.VulType,
				poc.Level,
				poc.RelevanceNum,
				poc.IsPoc,
				poc.Cve,
				poc.Describe,
				poc.Details,
			})
		}
	}

	filePath := time.Now().Format("20060102150405") + "漏洞聚合漏洞视角列表.xlsx"
	utils.WriterExcel(filePath,
		[]string{
			"数据源",
			"漏洞名称",
			"漏洞类型",
			"漏洞等级",
			"关联IP数量",
			"是否POC漏洞",
			"CVE编号",
			"描述",
			"详情",
		},
		dataList,
	)
	return filePath, nil
}

type PocIPAggregation struct {
	// 列表需要
	SourceNames    []SourceName `json:"source_names"`
	Ip             string       `json:"ip"`
	Id             string       `json:"id"`
	Ports          []string     `json:"ports"`
	CreatedAt      string       `json:"created_at"`
	UpdatedAt      string       `json:"updated_at"`
	LastResponseAt string       `json:"last_response_at"`
	PocNum         int64        `json:"poc_num"`      // 漏洞数量
	PocTypeNum     int64        `json:"poc_type_num"` // 漏洞种类数量
	AssetId        string       `json:"asset_id"`     // 资产ip对应的id
	NetworkType    int          `json:"network_type"`
}

func getIPAggPocBaseInfo(ips []interface{}, pageList map[string]poc.PocAggregationListResult) ([]*PocIPAggregation, error) {
	q := elastic.NewBoolQuery().Must(
		elastic.NewTermsQuery("ip.keyword", ips...),
	)

	result, err := es.GetEsClient().Search().Index(poc.NewPoc().IndexName()).
		Query(q).Size(10000).Sort("level", false).Do(context.TODO())

	if err != nil {
		return nil, errors.WithMessage(err, "按照ip获取漏洞基本信息失败")
	}

	var pocInfoMap = make(map[string][]*poc.Poc)
	for _, hit := range result.Hits.Hits {
		pocSource := poc.Poc{}
		json.Unmarshal(hit.Source, &pocSource)
		if pocSource.Name == "" {
			continue
		}
		if _, ok := pocInfoMap[pocSource.Ip]; !ok {
			pocInfoMap[pocSource.Ip] = make([]*poc.Poc, 0)
		}
		pocInfoMap[pocSource.Ip] = append(pocInfoMap[pocSource.Ip], &pocSource)
	}
	sourceMap, err := getAllSourceNameMap()
	if err != nil {
		return nil, err
	}

	var items = make([]*PocIPAggregation, 0, len(pageList))
	for ip, agg := range pageList {
		pocs, ok := pocInfoMap[ip]
		if !ok {
			items = append(items, &PocIPAggregation{
				Ip:     agg.Key,
				PocNum: agg.DocCount,
			})
			continue
		}
		if pocs == nil || len(pocs) == 0 {
			continue
		}
		sort.Slice(pocs, func(i, j int) bool {
			return pocs[i].Level > pocs[j].Level
		})
		var ports = make([]string, 0)
		var nameMap = make(map[string]struct{})
		for _, v := range pocs {
			ports = append(ports, fmt.Sprint(v.Port))
			nameMap[v.Name] = struct{}{}
		}

		t := &PocIPAggregation{
			Ports:          utils.ListDistinct(ports),
			CreatedAt:      pocs[0].CreatedAt.String(),
			UpdatedAt:      pocs[0].UpdatedAt.String(),
			LastResponseAt: pocs[0].LastResponseAt.String(),
			Ip:             agg.Key,
			PocNum:         agg.DocCount,
			PocTypeNum:     int64(len(nameMap)),
		}

		for _, id := range utils.ListDistinctNonZero(pocs[0].SourceIds) {
			source, ok := sourceMap[id]
			if !ok {
				continue
			}
			t.SourceNames = append(t.SourceNames, SourceName{
				Id:   source.Id,
				Name: source.Name,
				Icon: source.Icon,
			})
		}
		items = append(items, t)
	}

	return items, nil
}

type PocAggregationResponse struct {
	Total   int                      `json:"total"`
	Page    int                      `json:"page"`
	PerPage int                      `json:"per_page"`
	Items   []map[string]interface{} `json:"items"`
}

func PocIPAggsList(req *VulRelevanceListRequest) (*PocAggregationResponse, error) {
	alllist, err := poc.NewPoc().PocAggregationList(req.Keyword, "ip.keyword")
	if err != nil {
		return nil, err
	}
	if len(alllist) == 0 {
		return &PocAggregationResponse{
			Total:   0,
			Page:    req.Page,
			PerPage: req.PerPage,
			Items:   []map[string]interface{}{},
		}, nil
	}
	pageList := make([]poc.PocAggregationListResult, 0, len(alllist))
	s, e, ok := utils.SlicePage(req.Page, req.PerPage, len(alllist))
	if !ok {
		pageList = append(pageList, alllist[0])
	} else {
		pageList = alllist[s:e]
	}

	var ips = make([]interface{}, 0, len(pageList))
	var listMap = make(map[string]poc.PocAggregationListResult)
	for _, v := range pageList {
		ips = append(ips, v.Key)
		listMap[v.Key] = v
	}
	items, err := getIPAggPocBaseInfo(ips, listMap)
	if err != nil {
		return nil, err
	}

	query := elastic.NewBoolQuery().Must(elastic.NewTermsQuery("ip.keyword", ips...))
	assetsPage, _ := assets.NewAssets().FindAllByQuery(context.Background(), query, nil)
	var assetsMap = make(map[string]*assets.Assets, 0)
	for _, v := range assetsPage {
		assetsMap[v.Ip] = v
	}

	var itemMap = make([]map[string]interface{}, 0, len(items))
	for _, v := range items {
		asset, ok := assetsMap[v.Ip]
		v.Id = base64.StdEncoding.EncodeToString([]byte(v.Ip))
		if ok {
			v.AssetId = asset.Id
			v.NetworkType = asset.NetworkType
		}

		data, _ := json.Marshal(v)
		var t = make(map[string]interface{})
		json.Unmarshal(data, &t)
		itemMap = append(itemMap, utils.HandleEmptySlicesAndMaps(t))
	}

	var result = &PocAggregationResponse{
		Total:   len(alllist),
		Page:    req.Page,
		PerPage: req.PerPage,
		Items:   itemMap,
	}

	return result, nil
}

type PocBusinessAggregation struct {
	// 列表需要
	BusinessNameKey string       `json:"business_name_key"`
	SourceNames     []SourceName `json:"source_names"`
	CreatedAt       string       `json:"created_at"`
	UpdatedAt       string       `json:"updated_at"`
	LastResponseAt  string       `json:"last_response_at"`
	PocNum          int64        `json:"poc_num"`      // 漏洞数量
	PocTypeNum      int64        `json:"poc_type_num"` // 漏洞种类数量
	IpNum           int64        `json:"ip_num"`       // ip数量
	// 详情需要
	business_systems.ServiceBusinessSystems
}

func PocBusinessAggsList(req *VulRelevanceListRequest) (*PocAggregationResponse, error) {
	alllist, err := poc.NewPoc().PocAggregationList(req.Keyword, "business_name_tmp")
	if err != nil {
		return nil, err
	}
	if len(alllist) == 0 {
		return &PocAggregationResponse{
			Total:   0,
			Page:    req.Page,
			PerPage: req.PerPage,
			Items:   []map[string]interface{}{},
		}, nil
	}
	if req.Keyword != "" {
		tmpList := make([]poc.PocAggregationListResult, 0, len(alllist))
		for _, v := range alllist {
			if strings.Contains(v.Key, req.Keyword) {
				tmpList = append(tmpList, v)
			}
		}
		alllist = tmpList
	}
	pageList := make([]poc.PocAggregationListResult, 0, len(alllist))
	s, e, ok := utils.SlicePage(req.Page, req.PerPage, len(alllist))
	if !ok {
		pageList = append(pageList, alllist[0])
	} else {
		pageList = alllist[s:e]
	}

	var businessName = make([]interface{}, 0, len(pageList))
	var listMap = make(map[string]poc.PocAggregationListResult)
	for _, v := range pageList {
		businessName = append(businessName, v.Key)
		listMap[v.Key] = v
	}

	items, err := getBusinessAggPocBaseInfo(businessName, listMap)
	if err != nil {
		return nil, err
	}

	query := elastic.NewBoolQuery().Must(elastic.NewTermsQuery("business_name", businessName...))

	business, _ := business_system.NewBusinessSystems().FindAllByQuery(context.Background(), query, []string{})
	newBS, err := business_systems.NewService().ProcessPersonnelAndDepartment(business)
	if err != nil {
		return nil, err
	}
	var businessMap = make(map[string]*business_systems.ServiceBusinessSystems, 0)
	for _, v := range newBS {
		for _, p := range v.PersonBase {
			if p.Id != "" {
				p.Pgid, _ = pgidservice.GetPgidById(p.Id)
			}
		}

		businessMap[v.BusinessName] = v
	}
	var itemMap = make([]map[string]interface{}, 0, len(items))
	for _, v := range items {
		business, ok := businessMap[v.BusinessNameKey]
		if !ok {
			continue
		}

		v.ServiceBusinessSystems = *business
		v.Id = base64.StdEncoding.EncodeToString([]byte(v.BusinessNameKey))
		data, _ := json.Marshal(v)
		var t = make(map[string]interface{})
		json.Unmarshal(data, &t)
		itemMap = append(itemMap, utils.HandleEmptySlicesAndMaps(t))
	}

	return &PocAggregationResponse{
		Total:   len(alllist),
		Page:    req.Page,
		PerPage: req.PerPage,
		Items:   itemMap,
	}, nil
}
func GetPocBusinessSystemDetail(id string) (*PocBusinessAggregation, error) {
	data, err := business_systems.NewService().GetById(id)
	if err != nil {
		return nil, err
	}

	query := elastic.NewBoolQuery().Must(
		elastic.NewTermQuery("business_name_tmp", data.BusinessName),
	)
	count, err := es.GetCount(poc.NewPoc().IndexName(), query)
	if err != nil {
		return nil, err
	}
	aggResult, err := getBusinessAggPocBaseInfo([]interface{}{data.BusinessName}, map[string]poc.PocAggregationListResult{
		data.BusinessName: {
			Key:      data.BusinessName,
			DocCount: count,
		},
	})
	if err != nil {
		return nil, err
	}
	var result *PocBusinessAggregation
	for _, v := range aggResult {
		if v.BusinessNameKey == data.BusinessName {
			v.ServiceBusinessSystems = *data
			result = v
		}
	}
	return result, nil
}

func getBusinessAggPocBaseInfo(businessNames []interface{}, pageList map[string]poc.PocAggregationListResult) ([]*PocBusinessAggregation, error) {
	q := elastic.NewBoolQuery().Must(
		elastic.NewTermsQuery("business_name_tmp", businessNames...),
	)
	result, err := es.GetEsClient().Search().Index(poc.NewPoc().IndexName()).
		Query(q).Size(10000).Sort("level", false).Do(context.TODO())
	if err != nil {
		return nil, errors.WithMessage(err, "按照ip获取漏洞基本信息失败")
	}

	var pocInfoMap = make(map[string][]*poc.Poc)
	for _, hit := range result.Hits.Hits {
		pocSource := poc.Poc{}
		json.Unmarshal(hit.Source, &pocSource)
		if pocSource.Name == "" {
			continue
		}
		for _, businessName := range pocSource.BusinessNamesTmp {
			if _, ok := pocInfoMap[businessName]; !ok {
				pocInfoMap[businessName] = make([]*poc.Poc, 0)
			}
			pocInfoMap[businessName] = append(pocInfoMap[businessName], &pocSource)
		}

	}
	sourceMap, err := getAllSourceNameMap()
	if err != nil {
		return nil, err
	}

	var items = make([]*PocBusinessAggregation, 0, len(pageList))
	for businessName, agg := range pageList {
		pocs, ok := pocInfoMap[businessName]
		if !ok {
			items = append(items, &PocBusinessAggregation{
				BusinessNameKey: agg.Key,
				PocNum:          agg.DocCount,
			})
			continue
		}
		if pocs == nil || len(pocs) == 0 {
			continue
		}
		sort.Slice(pocs, func(i, j int) bool {
			return pocs[i].Level > pocs[j].Level
		})

		var nameMap = make(map[string]struct{})
		var ipMap = make(map[string]struct{})
		for _, v := range pocs {
			nameMap[v.Name] = struct{}{}
			ipMap[v.Ip] = struct{}{}
		}

		t := &PocBusinessAggregation{
			CreatedAt:       pocs[0].CreatedAt.String(),
			UpdatedAt:       pocs[0].UpdatedAt.String(),
			LastResponseAt:  pocs[0].LastResponseAt.String(),
			BusinessNameKey: agg.Key,
			PocNum:          agg.DocCount,
			PocTypeNum:      int64(len(nameMap)),
			IpNum:           int64(len(ipMap)),
		}
		for _, id := range pocs[0].SourceIds {
			source, ok := sourceMap[id]
			if !ok {
				continue
			}
			t.SourceNames = append(t.SourceNames, SourceName{
				Id:   source.Id,
				Name: source.Name,
				Icon: source.Icon,
			})
		}
		items = append(items, t)
	}

	return items, nil
}

func PocIPAggsListExport(ids []string) (string, error) {
	exportAll := false
	if len(ids) == 0 {
		exportAll = true
	}
	convertFn := func(pocInfo map[string]interface{}) []interface{} {
		pocByte, _ := json.Marshal(pocInfo)
		var names []string
		gjson.Get(string(pocByte), "source_names").ForEach(func(k, v gjson.Result) bool {
			names = append(names, gjson.Get(v.String(), "name").String())
			return true
		})
		var ports []string
		gjson.Get(string(pocByte), "ports").ForEach(func(k, v gjson.Result) bool {
			ports = append(ports, v.String())
			return true
		})
		var postsStr string
		if len(ports) > 0 {
			postsStr = strings.Join(ports, ",")
		}
		return []interface{}{
			strings.Join(names, ","),
			gjson.Get(string(pocByte), "ip").String(),
			postsStr,
			gjson.Get(string(pocByte), "poc_num").Int(),
			gjson.Get(string(pocByte), "poc_type_num").Int(),
			gjson.Get(string(pocByte), "created_at").String(),
			gjson.Get(string(pocByte), "updated_at").String(),
		}
	}
	var dataList = make([][]interface{}, 0)
	var page = 1
	var perPage = 10000
	//  全部
	if exportAll {
		for {
			pocList, err := PocIPAggsList(&VulRelevanceListRequest{
				PageRequest: request.PageRequest{
					Page:    page,
					PerPage: perPage,
				},
				Keyword: "",
			})
			if err != nil {
				return "", err
			}

			for _, v := range pocList.Items {
				dataList = append(dataList, convertFn(v))
			}

			total := pocList.Total
			if page*perPage >= total {
				break
			}
			page++
		}
	} else {
		// 部分
		for _, v := range ids {
			ip, err := utils.Base64Decode(v)
			if err != nil {
				continue
			}
			pocList, err := PocIPAggsList(&VulRelevanceListRequest{
				PageRequest: request.PageRequest{
					Page:    1,
					PerPage: 1,
				},
				Keyword: ip,
			})
			if err != nil {
				return "", err
			}
			if len(pocList.Items) == 0 {
				continue
			}
			dataList = append(dataList, convertFn(pocList.Items[0]))
		}
	}

	filePath := time.Now().Format("20060102150405") + "漏洞聚合IP视角列表.xlsx"
	utils.WriterExcel(filePath,
		[]string{
			"数据源",
			"IP地址",
			"影响端口",
			"关联漏洞数量",
			"关联漏洞种类",
			"首次上报时间",
			"最后上报时间",
		},
		dataList,
	)
	return filePath, nil
}

func PocBusinessAggsListExport(ids []string) (string, error) {
	exportAll := len(ids) == 0

	convertFn := func(pocInfo map[string]interface{}) []interface{} {
		pocByte, _ := json.Marshal(pocInfo)
		importantInt := gjson.Get(string(pocByte), "assets_attribute.important_types").Int()

		important := ""
		switch importantInt {
		case 1:
			important = "非常重要"
		case 2:
			important = "重要"
		case 3:
			important = "一般"
		}

		var depts []string

		//获取部门
		gjson.Get(string(pocByte), "department_base").ForEach(func(k, v gjson.Result) bool {
			depts = append(depts, v.Get("name").String())
			return true
		})
		var uName []string
		//获取人员, 不考虑是否存在
		//不存在需要访问person_base[i].find_info[j].source_value
		gjson.Get(string(pocByte), "person_base").ForEach(func(k, v gjson.Result) bool {
			if v.Get("name").String() != "" {
				uName = append(uName, v.Get("name").String())
				return true
			}
			v.Get("find_info").ForEach(func(k1, v1 gjson.Result) bool {
				uName = append(uName, v1.Get("source_value").String())
				return true
			})
			return true
		})
		var deptNames = ""
		if len(depts) > 0 {
			deptNames = strings.Join(depts, ",")
		}
		userNames := ""
		if len(uName) > 0 {
			userNames = strings.Join(uName, ",")
		}

		return []interface{}{
			gjson.Get(string(pocByte), "business_name_key").String(),
			gjson.Get(string(pocByte), "address").String(),
			userNames,
			deptNames,
			gjson.Get(string(pocByte), "poc_num").Int(),
			gjson.Get(string(pocByte), "poc_type_num").Int(),
			gjson.Get(string(pocByte), "ip_num").Int(),
			important,
			gjson.Get(string(pocByte), "created_at").String(),
			gjson.Get(string(pocByte), "updated_at").String(),
		}
	}

	var dataList = make([][]interface{}, 0)
	var page = 1
	var perPage = 10000
	//  全部
	if exportAll {
		for {
			pocList, err := PocBusinessAggsList(&VulRelevanceListRequest{
				PageRequest: request.PageRequest{
					Page:    page,
					PerPage: perPage,
				},
				Keyword: "",
			})
			if err != nil {
				return "", err
			}

			for _, v := range pocList.Items {
				dataList = append(dataList, convertFn(v))
			}

			total := pocList.Total
			if page*perPage >= total {
				break
			}
			page++
		}
	} else {
		// 部分
		for _, v := range ids {
			businessName, err := utils.Base64Decode(v)
			if err != nil {
				continue
			}
			pocList, err := PocBusinessAggsList(&VulRelevanceListRequest{
				PageRequest: request.PageRequest{
					Page:    1,
					PerPage: 1,
				},
				Keyword: businessName,
			})
			if err != nil {
				return "", err
			}
			if len(pocList.Items) == 0 {
				continue
			}
			dataList = append(dataList, convertFn(pocList.Items[0]))
		}
	}

	filePath := time.Now().Format("20060102150405") + "漏洞聚合业务系统列表.xlsx"
	utils.WriterExcel(filePath,
		[]string{
			"业务系统",
			"访问地址",
			"业务系统负责人",
			"部门",
			"关联漏洞数量",
			"关联漏洞种类",
			"关联IP数量",
			"重要性",
			"首次上报时间",
			"最后上报时间",
		},
		dataList,
	)
	return filePath, nil
}
