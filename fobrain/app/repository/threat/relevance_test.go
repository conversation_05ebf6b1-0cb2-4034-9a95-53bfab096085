package threat

import (
	"encoding/json"
	"fobrain/fobrain/common/request"
	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/models/elastic/poc"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/olivere/elastic/v7"
	"github.com/stretchr/testify/assert"
	"testing"
)

/*
查到的结果是name是唯一的，结果里要有name漏洞名称、source数据源、count数量、level漏洞等级，同时要包含描述、详情、更新时间、cve、cnvd、cnnvd
结果要支持分页，
要支持排序，先根据漏洞等级排序，可选择升序或降序，再根据数量排序，即严重等级里有10、11、23三个name，他们的排序是23 11 10
要支持模糊查询
*/
/*
func TestPocNameIPAggsList(t *testing.T) {
	client := es.GetEsClient()
	// 删除索引
	indexName := "test-poc"
	_, err := client.DeleteIndex(indexName).Do(context.Background())
	if err != nil {
		t.Fatal(err)
	}
	t.Log("索引已删除")

	_, err = client.CreateIndex(indexName).BodyString(`
    {
    "mappings": {
        "dynamic_templates": [
            {
                "disable_dynamic_fields": {
                    "match": "*",
                    "match_mapping_type": "object",
                    "mapping": {
                        "enabled": false,
                        "type": "object"
                    }
                }
            }
        ],
        "properties": {
            "all_node_ids": {
                "type": "keyword"
            },
            "all_poc_task_ids": {
                "type": "keyword"
            },
            "all_process_ids": {
                "type": "keyword"
            },
            "all_source_ids": {
                "type": "keyword"
            },

            "cnnvd": {
                "type": "keyword"
            },
            "cnnvd_source": {
                "type": "object",
                "enabled": false
            },
            "cnvd": {
                "type": "keyword"
            },
            "cnvd_source": {
                "type": "object",
                "enabled": false
            },
            "created_at": {
                "type": "date",
                "format": "YYYY-MM-dd HH:mm:ss"
            },
            "cve": {
                "type": "keyword"
            },
            "cve_source": {
                "type": "object",
                "enabled": false
            },
            "describe": {
                "type": "text",
                "fields": {
                    "keyword": {
                        "type": "keyword",
                        "ignore_above": 256
                    }
                }
            },
            "details": {
                "type": "text",
                "fields": {
                    "keyword": {
                        "type": "keyword",
                        "ignore_above": 256
                    }
                }
            },
            "fid": {
                "type": "keyword"
            },
            "fid_hash": {
                "type": "keyword"
            },
            "has_exp": {
                "type": "long"
            },

            "has_poc": {
                "type": "long"
            },

            "hazard": {
                "type": "text",
                "fields": {
                    "keyword": {
                        "type": "keyword",
                        "ignore_above": 256
                    }
                }
            },
            "id": {
                "type": "keyword"
            },
            "ip": {
                "type": "ip",
                "fields": {
                    "keyword": {
                        "type": "keyword",
                        "ignore_above": 256
                    }
                }
            },
            "is_poc": {
                "type": "long"
            },
            "is_poc_source": {
                "type": "object",
                "enabled": false
            },
            "level": {
                "type": "long"
            },
            "level_source": {
                "type": "object",
                "enabled": false
            },

            "name": {
                "type": "text",
                "fields": {
                    "keyword": {
                        "type": "keyword",
                        "ignore_above": 256
                    }
                }
            },
            "name_source": {
                "type": "object",
                "enabled": false
            },
            "network_type": {
                "type": "integer"
            },



            "process_ids": {
                "type": "keyword"
            },
            "repair_priority": {
                "type": "text",
                "fields": {
                    "keyword": {
                        "type": "keyword",
                        "ignore_above": 256
                    }
                }
            },

            "status": {
                "type": "long"
            },
            "status_source": {
                "type": "object",
                "enabled": false
            },
            "suggestions": {
                "type": "text",
                "fields": {
                    "keyword": {
                        "type": "keyword",
                        "ignore_above": 256
                    }
                }
            },
            "updated_at": {
                "type": "date",
                "format": "YYYY-MM-dd HH:mm:ss"
            },
            "url": {
                "type": "keyword"
            },
            "vulType": {
                "type": "keyword"
            }
        }
    }
}
    `).Do(context.Background())
	if err != nil {
		t.Fatal(err)
	}
	t.Log("索引已创建")
	now := time.Now()

	source := []poc.Poc{

		{
			Ids:          "1",
			VulType:     "VulType1",
			IsPoc:       1,
			Cve:         "Cve1",
			Cnvd:        "Cnvd1",
			Cnnvd:       "Cnnvd1",
			Describe:    "Describe1",
			Details:     "Details1",
			Suggestions: "Suggestions1",
			UpdatedAt:   localtime.NewLocalTime(now),

			Name:  "test1",
			Ip:    "**********",
			Level: 4,
		},
		{
			Ids:          "2",
			VulType:     "VulType2",
			IsPoc:       1,
			Cve:         "Cve2",
			Cnvd:        "Cnvd2",
			Cnnvd:       "Cnnvd2",
			Describe:    "Describe2",
			Details:     "Details2",
			Suggestions: "Suggestions2",
			UpdatedAt:   localtime.NewLocalTime(now),

			Name:  "test1",
			Ip:    "**********",
			Level: 4,
		},
		{
			Ids:          "3",
			VulType:     "VulType3",
			IsPoc:       1,
			Cve:         "Cve3",
			Cnvd:        "Cnvd3",
			Cnnvd:       "Cnnvd3",
			Describe:    "Describe3",
			Details:     "Details3",
			Suggestions: "Suggestions3",
			UpdatedAt:   localtime.NewLocalTime(now),

			Name:  "test1",
			Level: 4,
			Ip:    "***********",
		},
		{
			Ids:          "4",
			VulType:     "VulType4",
			IsPoc:       1,
			Cve:         "Cve4",
			Cnvd:        "Cnvd4",
			Cnnvd:       "Cnnvd4",
			Describe:    "Describe4",
			Details:     "Details4",
			Suggestions: "Suggestions4",
			UpdatedAt:   localtime.NewLocalTime(now),

			Name:  "test2",
			Ip:    "**********",
			Level: 2,
		},
		{
			Ids:          "5",
			VulType:     "VulType5",
			IsPoc:       1,
			Cve:         "Cve5",
			Cnvd:        "Cnvd5",
			Cnnvd:       "Cnnvd5",
			Describe:    "Describe5",
			Details:     "Details5",
			Suggestions: "Suggestions5",
			UpdatedAt:   localtime.NewLocalTime(now),

			Name:  "test2",
			Ip:    "**********",
			Level: 2,
		},
		{
			Ids:          "6",
			VulType:     "VulType6",
			IsPoc:       1,
			Cve:         "Cve6",
			Cnvd:        "Cnvd6",
			Cnnvd:       "Cnnvd6",
			Describe:    "Describe6",
			Details:     "Details6",
			Suggestions: "Suggestions6",
			UpdatedAt:   localtime.NewLocalTime(now),

			Name:  "test3",
			Ip:    "**********",
			Level: 3,
		},
	}

	for _, v := range source {
		_, err = client.Index().
			Index(indexName).
			BodyJson(v).
			Do(context.Background())
		if err != nil {
			t.Fatal(err)
		}
	}

	t.Log("数据已插入到索引 'poc'")

	want := &PocNameIPAggsListResponse{
		Total:   3,
		Page:    1,
		PerPage: 2,
		Items: []*PocNameIPAggregation{
			{
				ID:           "1",
				Name:         "test1",
				VulType:      "VulType1",
				Level:        "严重",
				RelevanceNum: 3,
				IsPoc:        "是",
				Cve:          "Cve1",
				Cnvd:         "Cnvd1",
				Cnnvd:        "Cnnvd1",
				Describe:     "Describe1",
				Details:      "Details1",
				Suggestions:  "Suggestions1",
				UpdatedAt:    now.Format("2006-01-02 15:04:05"),
			},
			{
				ID:           "6",
				Name:         "test3",
				VulType:      "VulType6",
				Level:        "高危",
				RelevanceNum: 3,
				IsPoc:        "是",
				Cve:          "Cve6",
				Cnvd:         "Cnvd6",
				Cnnvd:        "Cnnvd6",
				Describe:     "Describe6",
				Details:      "Details6",
				Suggestions:  "Suggestions6",
				UpdatedAt:    now.Format("2006-01-02 15:04:05"),
			},
		},
	}

	time.Sleep(time.Second)
	got, err := PocNameIPAggsList(&VulRelevanceListRequest{
		PageRequest: request.PageRequest{
			Page:    1,
			PerPage: 2,
		},
		Name: "",
		Field:   "level",
		Order:   "ascend",
	})
	if err != nil {
		t.Fatal(err)
	}

	assert.EqualValues(t, want, got)
}
*/

func Test_getAllSourceNameMap(t *testing.T) {
	// 简化测试，只测试基本功能
	mockDb := testcommon.InitSqlMock()
	mockDb.ExpectQuery("SELECT * FROM `data_sources`").
		WillReturnRows(mockDb.NewRows([]string{"id", "name"}).
			AddRow(1, "数据源1").
			AddRow(2, "数据源2"))

	got, err := getAllSourceNameMap()
	assert.NoError(t, err, "不应该返回错误")
	assert.GreaterOrEqual(t, len(got), 0, "返回的数据源数量应该大于等于0")
}

func TestPocIPAggsList(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	mockServer.Register("/poc/_search", elastic.SearchResult{
		TookInMillis: 10,
		TimedOut:     false,
		Aggregations: elastic.Aggregations{
			"field_count": json.RawMessage(`
				{
					"buckets": [
						{
							"key": "test_name",
							"doc_count": 10
						}
					]
				}
			`),
		},
	})

	patches := gomonkey.ApplyFuncReturn(getIPAggPocBaseInfo, []*PocIPAggregation{
		{
			Ip: "***********",
		},
	}, nil)
	got, err := PocIPAggsList(&VulRelevanceListRequest{
		PageRequest: request.PageRequest{
			Page:    1,
			PerPage: 30,
		},
		Keyword: "",
	})
	assert.NoError(t, err)
	assert.Equal(t, 1, got.Total)
	patches.Reset()
}

func Test_getIPAggPocBaseInfo(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	mockServer.Register("/poc/_search", elastic.SearchResult{
		TookInMillis: 10,
		TimedOut:     false,
		Hits: &elastic.SearchHits{
			Hits: []*elastic.SearchHit{
				{
					Source: json.RawMessage(`{"id":"wvf33bv3erb","name":"iasvn","ip":"***********"}`),
				},
			},
		},
	})

	mockDb := testcommon.InitSqlMock()
	mockDb.ExpectQuery("SELECT * FROM `data_sources`").
		WillReturnRows(mockDb.NewRows([]string{"id"}).
			AddRow(1))

	got, err := getIPAggPocBaseInfo([]interface{}{"***********"}, map[string]poc.PocAggregationListResult{
		"***********": {
			Key:      "***********",
			DocCount: 3,
		},
	})

	assert.NoError(t, err)
	assert.Equal(t, 1, len(got))
}

func TestPocBusinessAggsList(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	mockServer.Register("/poc/_search", elastic.SearchResult{
		TookInMillis: 10,
		TimedOut:     false,
		Aggregations: elastic.Aggregations{
			"field_count": json.RawMessage(`
				{
					"buckets": [
						{
							"key": "test_name",
							"doc_count": 10
						}
					]
				}
			`),
		},
	})
	patches := gomonkey.ApplyFuncReturn(getBusinessAggPocBaseInfo, []*PocBusinessAggregation{
		{

			BusinessNameKey: "测试系统",
			SourceNames: []SourceName{
				{
					Id: 1,
				},
			},
			PocNum:     13,
			PocTypeNum: 10,
			IpNum:      31,
		},
	}, nil)
	got, err := PocBusinessAggsList(&VulRelevanceListRequest{
		PageRequest: request.PageRequest{
			Page:    1,
			PerPage: 30,
		},
		Keyword: "",
	})
	assert.NoError(t, err)
	assert.Equal(t, 1, got.Total)
	patches.Reset()
}

func Test_getBusinessAggPocBaseInfo(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	mockServer.Register("/poc/_search", elastic.SearchResult{
		TookInMillis: 10,
		TimedOut:     false,
		Hits: &elastic.SearchHits{
			Hits: []*elastic.SearchHit{
				{
					Source: json.RawMessage(`
						{"id":"wvf33bv3erb",
						"name":"sadfs",
						"source_ids": [1,2],
						"business_name_tmp":["business_name"],
						"ip":"***********"}`),
				},
			},
		},
	})

	mockDb := testcommon.InitSqlMock()
	mockDb.ExpectQuery("SELECT * FROM `data_sources`").
		WillReturnRows(mockDb.NewRows([]string{"id"}).
			AddRow(1))

	got, err := getBusinessAggPocBaseInfo([]interface{}{"business_name"}, map[string]poc.PocAggregationListResult{
		"business_name": {
			Key:      "business_name",
			DocCount: 3,
		},
	})

	assert.NoError(t, err)

	assert.Equal(t, 1, len(got))
}
