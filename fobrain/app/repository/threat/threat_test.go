package threat

import (
	"encoding/base64"
	"fmt"
	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/models/mysql/user"
	"os"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/stretchr/testify/assert"
)

func TestActionType(t *testing.T) {
	operations := map[string]interface{}{"statusCode": 1}

	t.Run("status=1", func(t *testing.T) {
		mockDb := testcommon.GetMysqlMock()
		defer mockDb.Close()
		mockDb.ExpectQuery("SELECT menus.name FROM `menus` INNER JOIN roles_menus ON menus.id = roles_menus.menu_id WHERE roles_menus.role_id = ?").
			WillReturnRows(sqlmock.NewRows([]string{"name"}).
				AddRow("zhuanjiao_compliance_risks"))
		poc := ActionType(operations, &user.User{
			Role: user.Role{
				Id: 1,
			},
		}, true)

		result := []map[string]interface{}{
			<PERSON><PERSON><PERSON><PERSON>,
			<PERSON><PERSON><PERSON>,
			<PERSON><PERSON><PERSON><PERSON>,
			<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
		}
		assert.Equal(t, result, poc["action_type"])
	})

	t.Run("status=10", func(t *testing.T) {
		mockDb := testcommon.GetMysqlMock()
		defer mockDb.Close()
		mockDb.ExpectQuery("SELECT menus.name FROM `menus` INNER JOIN roles_menus ON menus.id = roles_menus.menu_id WHERE roles_menus.role_id = ?").
			WillReturnRows(sqlmock.NewRows([]string{"name"}).
				AddRow("zhuanjiao_compliance_risks"))
		operations["statusCode"] = 10
		poc := ActionType(operations, &user.User{
			Role: user.Role{
				Id: 1,
			},
		}, true)

		result := []map[string]interface{}{
			ZhuanJiao,
			CuiCu,
			YanShi,
			WuBao,
			WuFaXiuFu,
			XiuFuWanCheng,
		}
		assert.Equal(t, result, poc["action_type"])
	})

	t.Run("status=14", func(t *testing.T) {
		operations["statusCode"] = 14
		poc := ActionType(operations, &user.User{
			Role: user.Role{
				Id: 1,
			},
		}, true)

		result := []map[string]interface{}{}
		assert.Equal(t, result, poc["action_type"])
	})

	t.Run("status=17", func(t *testing.T) {
		mockDb := testcommon.GetMysqlMock()
		defer mockDb.Close()
		mockDb.ExpectQuery("SELECT menus.name FROM `menus` INNER JOIN roles_menus ON menus.id = roles_menus.menu_id WHERE roles_menus.role_id = ?").
			WillReturnRows(sqlmock.NewRows([]string{"name"}).
				AddRow("zhuanjiao_compliance_risks"))
		operations["statusCode"] = 17
		poc := ActionType(operations, &user.User{
			Role: user.Role{
				Id: 1,
			},
		}, true)

		result := []map[string]interface{}{
			FuCe,
		}
		assert.Equal(t, result, poc["action_type"])
	})

	t.Run("status=16", func(t *testing.T) {
		mockDb := testcommon.GetMysqlMock()
		defer mockDb.Close()
		mockDb.ExpectQuery("SELECT menus.name FROM `menus` INNER JOIN roles_menus ON menus.id = roles_menus.menu_id WHERE roles_menus.role_id = ?").
			WillReturnRows(sqlmock.NewRows([]string{"name"}).
				AddRow("zhuanjiao_compliance_risks"))
		operations["statusCode"] = 16
		poc := ActionType(operations, &user.User{
			Role: user.Role{
				Id: 1,
			},
		}, true)

		result := []map[string]interface{}{
			ZhuanJiao,
			CuiCu,
			YanShi,
			WuBao,
			WuFaXiuFu,
			XiuFuWanCheng,
		}
		assert.Equal(t, result, poc["action_type"])
	})

	t.Run("status=30", func(t *testing.T) {
		mockDb := testcommon.GetMysqlMock()
		defer mockDb.Close()
		mockDb.ExpectQuery("SELECT menus.name FROM `menus` INNER JOIN roles_menus ON menus.id = roles_menus.menu_id WHERE roles_menus.role_id = ?").
			WillReturnRows(sqlmock.NewRows([]string{"name"}).
				AddRow("zhuanjiao_compliance_risks"))
		operations["statusCode"] = 30
		poc := ActionType(operations, &user.User{
			Role: user.Role{
				Id: 1,
			},
		}, true)

		result := []map[string]interface{}{}
		assert.Equal(t, result, poc["action_type"])
	})

	t.Run("status=40 or 41", func(t *testing.T) {
		mockDb := testcommon.GetMysqlMock()
		defer mockDb.Close()
		mockDb.ExpectQuery("SELECT menus.name FROM `menus` INNER JOIN roles_menus ON menus.id = roles_menus.menu_id WHERE roles_menus.role_id = ?").
			WillReturnRows(sqlmock.NewRows([]string{"name"}).
				AddRow("zhuanjiao_compliance_risks"))
		operations["statusCode"] = 40
		poc := ActionType(operations, &user.User{
			Role: user.Role{
				Id: 1,
			},
		}, true)

		result := []map[string]interface{}{PaiFa}
		assert.Equal(t, result, poc["action_type"])

	})
}

func TestPocNameIPAggsListExport(t *testing.T) {
	time.Sleep(time.Millisecond * 200)
	patches := gomonkey.ApplyFuncReturn(PocNameIPAggsList, &PocNameIPAggsListResponse{
		Total:   2,
		Page:    1,
		PerPage: 10,
		Items: []*PocNameIPAggregation{
			{
				Name:         "漏洞1",
				VulType:      "类型1",
				Level:        "高",
				RelevanceNum: 10,
				IsPoc:        "是",
			},
			{
				Name:         "漏洞2",
				VulType:      "类型2",
				Level:        "中",
				RelevanceNum: 5,
				IsPoc:        "否",
			},
		},
	}, nil)

	// 测试导出所有数据
	filePath, err := PocNameIPAggsListExport([]string{})
	assert.NoError(t, err)
	assert.NotEmpty(t, filePath)

	// 测试导出部分数据
	ids := []string{base64.StdEncoding.EncodeToString([]byte("test_id_1")), base64.StdEncoding.EncodeToString([]byte("test_id_2"))}
	filePath, err = PocNameIPAggsListExport(ids)
	assert.NoError(t, err)
	assert.NotEmpty(t, filePath)

	// 测试无效ID
	invalidIds := []string{"invalid_id"}
	filePath, err = PocNameIPAggsListExport(invalidIds)
	assert.NoError(t, err)
	assert.NotEmpty(t, filePath)
	fmt.Println("!!!filePath:", filePath)
	// 删除当前目录下的.xlsx文件
	os.Remove(filePath)
	patches.Reset()
}

func TestPocIPAggsListExport(t *testing.T) {
	time.Sleep(time.Millisecond * 200)
	patches := gomonkey.ApplyFuncReturn(PocIPAggsList, &PocAggregationResponse{
		Total:   2,
		Page:    1,
		PerPage: 10,
		Items: []map[string]interface{}{
			{
				"ip":    "***********",
				"ports": []string{"8434", "4242"},
			},
		},
	}, nil)

	// 测试导出所有数据
	filePath, err := PocIPAggsListExport([]string{})
	assert.NoError(t, err)
	assert.NotEmpty(t, filePath)

	// 测试导出部分数据
	ids := []string{base64.StdEncoding.EncodeToString([]byte("test_id_1")), base64.StdEncoding.EncodeToString([]byte("test_id_2"))}
	filePath, err = PocIPAggsListExport(ids)
	assert.NoError(t, err)
	assert.NotEmpty(t, filePath)

	// 测试无效ID
	invalidIds := []string{"invalid_id"}
	filePath, err = PocIPAggsListExport(invalidIds)
	assert.NoError(t, err)
	assert.NotEmpty(t, filePath)
	fmt.Println("!!!filePath:", filePath)
	// 删除当前目录下的.xlsx文件
	os.Remove(filePath)
	patches.Reset()
}

func TestPocBusinessAggsListExport(t *testing.T) {
	time.Sleep(time.Millisecond * 200)
	patches := gomonkey.ApplyFuncReturn(PocBusinessAggsList, &PocAggregationResponse{
		Total:   2,
		Page:    1,
		PerPage: 10,
		Items: []map[string]interface{}{
			{
				"ip":    "***********",
				"ports": []string{"8434", "4242"},
			},
		},
	}, nil)

	// 测试导出所有数据
	filePath, err := PocBusinessAggsListExport([]string{})
	assert.NoError(t, err)
	assert.NotEmpty(t, filePath)

	// 测试导出部分数据
	ids := []string{base64.StdEncoding.EncodeToString([]byte("test_id_1")), base64.StdEncoding.EncodeToString([]byte("test_id_2"))}
	filePath, err = PocBusinessAggsListExport(ids)
	assert.NoError(t, err)
	assert.NotEmpty(t, filePath)

	// 测试无效ID
	invalidIds := []string{"invalid_id"}
	filePath, err = PocBusinessAggsListExport(invalidIds)
	assert.NoError(t, err)
	assert.NotEmpty(t, filePath)
	fmt.Println("!!!filePath:", filePath)
	// 删除当前目录下的.xlsx文件
	os.Remove(filePath)
	patches.Reset()
}
