package logs

import (
	"net"
	"os"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/stretchr/testify/assert"

	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/initialize/mysql"
	"fobrain/models/mysql/audit_logs"
	"fobrain/models/mysql/logs_qc_setting"
	"fobrain/models/mysql/system_logs"
	"fobrain/models/mysql/user"
)

func TestUserLogsList(t *testing.T) {
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()

	mockDb.ExpectQuery("SELECT * FROM `users` WHERE status = ?").
		WithArgs(1).
		WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))

	data := []user.User{{
		Username: "AA",
		Account:  "AA",
		Password: "AAA",
		Status:   1,
	}}
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyMethodReturn(user.NewUserModel(), "List", data, int64(1), nil).Reset()
	list, err := UserLogsList()
	assert.NoError(t, err)
	assert.Equal(t, 1, len(list))
}

func TestExportUserLogs(t *testing.T) {
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()

	mockDb.ExpectQuery("SELECT audit_logs.*,users.username as user_name FROM `audit_logs` LEFT JOIN users  ON audit_logs.user_id = users.id ORDER BY audit_logs.id desc").
		WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))
	data := []audit_logs.UserLogs{{
		AuditLog: audit_logs.AuditLog{
			Title:  "AA",
			UserId: 1,
			Path:   "/a/b/c",
			Type:   1,
			Result: "Success",
			Ip:     "127.0.0.1",
		},
		UserName: "aaa",
	}}
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyMethodReturn(audit_logs.NewAuditLogModel(), "UserList", data, int64(1), nil).Reset()
	filePath, err := ExportUserLogs([]int64{}, "", "", 0)
	assert.NoError(t, err)
	assert.NotNil(t, filePath)
	os.Remove(filePath)
}

func TestExportSystemLogs(t *testing.T) {
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()

	mockDb.ExpectQuery("SELECT * FROM `system_logs` ORDER BY id desc").
		WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))
	data := []system_logs.SystemLogs{{
		ModuleName: "AA",
		Result:     "BBB",
	}}
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyMethodReturn(system_logs.NewSystemLogModel(), "List", data, int64(1), nil).Reset()
	filePath, err := ExportSystemLogs("", "")
	assert.NoError(t, err)
	assert.NotNil(t, filePath)
	os.Remove(filePath)
}

func TestCreateOrUpdateLogsQcSetting(t *testing.T) {

	t.Run("update", func(t *testing.T) {
		data := logs_qc_setting.LogsQcSetting{
			BaseModel: mysql.BaseModel{
				Id: 1,
			},
			Host:          "127.0.0.1",
			Port:          8091,
			Status:        1,
			LogType:       2,
			Plan:          1,
			Period:        "",
			ExecuteTime:   "",
			Msg:           "",
			RepeatEndTime: "",
		}
		time.Sleep(time.Second)
		defer gomonkey.ApplyMethodReturn(logs_qc_setting.NewLogsQcSettingModel(), "First", data, nil).Reset()
		time.Sleep(time.Second)
		defer gomonkey.ApplyMethodReturn(logs_qc_setting.NewLogsQcSettingModel(), "Update", nil).Reset()

		err := CreateOrUpdateLogsQcSetting(data)
		assert.NoError(t, err)
	})

}

func TestExecLogsQc(t *testing.T) {
	t.Run("LogTypeUser", func(t *testing.T) {
		data := logs_qc_setting.LogsQcSetting{
			BaseModel: mysql.BaseModel{
				Id: 1,
			},
			Host:          "127.0.0.1",
			Port:          8091,
			Status:        1,
			LogType:       1,
			Plan:          1,
			Period:        "",
			ExecuteTime:   "",
			Msg:           "",
			RepeatEndTime: "",
		}

		userList := []audit_logs.UserLogs{{
			AuditLog: audit_logs.AuditLog{
				Title:  "AA",
				UserId: 1,
				Path:   "/a/b/c",
				Type:   1,
				Result: "Success",
				Ip:     "127.0.0.1",
			},
			UserName: "aaa",
		}}
		time.Sleep(time.Second)
		defer gomonkey.ApplyMethodReturn(audit_logs.NewAuditLogModel(), "UserList", userList, int64(1), nil).Reset()

		err := ExecLogsQc(data)
		assert.NoError(t, err)
	})
	t.Run("LogTypeSystem", func(t *testing.T) {
		data := logs_qc_setting.LogsQcSetting{
			BaseModel: mysql.BaseModel{
				Id: 1,
			},
			Host:          "127.0.0.1",
			Port:          8091,
			Status:        1,
			LogType:       2,
			Plan:          1,
			Period:        "",
			ExecuteTime:   "",
			Msg:           "",
			RepeatEndTime: "",
		}
		systemList := []system_logs.SystemLogs{{
			ModuleName: "AA",
			Result:     "BBB",
		}}

		time.Sleep(time.Second)
		defer gomonkey.ApplyMethodReturn(system_logs.NewSystemLogModel(), "List", systemList, int64(1), nil).Reset()

		err := ExecLogsQc(data)
		assert.NoError(t, err)
	})
	t.Run("LogTypeAll", func(t *testing.T) {
		data := logs_qc_setting.LogsQcSetting{
			BaseModel: mysql.BaseModel{
				Id: 1,
			},
			Host:          "127.0.0.1",
			Port:          8091,
			Status:        1,
			LogType:       3,
			Plan:          1,
			Period:        "",
			ExecuteTime:   "",
			Msg:           "",
			RepeatEndTime: "",
		}
		userList := []audit_logs.UserLogs{{
			AuditLog: audit_logs.AuditLog{
				Title:  "AA",
				UserId: 1,
				Path:   "/a/b/c",
				Type:   1,
				Result: "Success",
				Ip:     "127.0.0.1",
			},
			UserName: "aaa",
		}}
		time.Sleep(time.Second)
		defer gomonkey.ApplyMethodReturn(audit_logs.NewAuditLogModel(), "UserList", userList, int64(1), nil).Reset()
		systemList := []system_logs.SystemLogs{{
			ModuleName: "AA",
			Result:     "BBB",
		}}
		time.Sleep(time.Second)
		defer gomonkey.ApplyMethodReturn(system_logs.NewSystemLogModel(), "List", systemList, int64(1), nil).Reset()

		err := ExecLogsQc(data)
		assert.NoError(t, err)
	})
}

func TestSendViaUDP(t *testing.T) {
	// 创建一个 UDP 服务器来接收消息
	addr := "127.0.0.1:12345"
	server, err := net.ListenPacket("udp", addr)
	if err != nil {
		t.Fatalf("Failed to start UDP server: %v", err)
	}
	defer server.Close()

	// 创建一个通道，用于接收服务器收到的消息
	messageChan := make(chan string, 1)

	// 启动一个 Goroutine 来监听消息
	go func() {
		buffer := make([]byte, 1024)
		n, _, err := server.ReadFrom(buffer)
		if err != nil {
			t.Logf("Failed to read from UDP server: %v", err)
			return
		}
		messageChan <- string(buffer[:n])
	}()

	// 定义测试用例
	testMessage := "Hello, UDP!"

	// 调用函数发送消息
	err = sendViaUDP(addr, testMessage)
	if err != nil {
		t.Fatalf("Failed to send message: %v", err)
	}

	// 验证服务器是否收到了正确的消息
	select {
	case receivedMessage := <-messageChan:
		if receivedMessage != testMessage {
			t.Errorf("Expected message '%s', but got '%s'", testMessage, receivedMessage)
		}
	case <-time.After(2 * time.Second): // 超时时间
		t.Fatalf("Timeout waiting for UDP server to receive the message")
	}
}
