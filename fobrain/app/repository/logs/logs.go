package logs

import (
	"encoding/json"
	"fmt"
	"net"
	"os/exec"
	"time"

	"go-micro.dev/v4/logger"

	"fobrain/initialize/mysql"
	"fobrain/models/mysql/audit_logs"
	"fobrain/models/mysql/logs_qc_setting"
	"fobrain/models/mysql/system_logs"
	"fobrain/models/mysql/user"
	"fobrain/pkg/utils"
)

type UserLogs struct {
	UserID uint64 `json:"user_id"`
	Name   string `json:"name"`
}

func UserLogsList() ([]UserLogs, error) {
	data := make([]UserLogs, 0)
	handlers := make([]mysql.HandleFunc, 0)
	handlers = append(handlers, mysql.WithWhere("status = ?", 1))
	list, _, err := user.NewUserModel().List(0, 0, handlers...)
	if err != nil {
		return data, err
	}
	for _, val := range list {
		data = append(data, UserLogs{
			UserID: val.Id,
			Name:   val.Username,
		})
	}
	return data, nil
}

func ExportUserLogs(ids []int64, keyword, ip string, userId uint64) (string, error) {
	ids = utils.ListNonZero(ids)
	list, _, err := audit_logs.NewAuditLogModel().UserList(ids, keyword, ip, userId, 0, 0, audit_logs.OperationTypeExport)
	if err != nil {
		return "", err
	}
	var datum = make([][]interface{}, 0)
	for _, log := range list {
		datum = append(datum, []interface{}{
			log.UserName,
			log.Title,
			log.Path,
			log.Result,
			log.Ip,
			log.CreatedAt.String(),
		})
	}
	filePath := time.Now().Format("20060102150405") + "用户日志列表.xlsx"
	utils.WriterExcel(filePath,
		[]string{
			"用户名",
			"用户行为",
			"url",
			"操作结果",
			"操作IP",
			"操作时间",
		},
		datum,
	)
	return filePath, nil
}

func ExportSystemLogs(keyword, startTime string) (string, error) {
	list, _, err := system_logs.NewSystemLogModel().List(keyword, startTime, 0, 0, system_logs.OperationTypeExport)
	if err != nil {
		return "", err
	}
	var datum = make([][]interface{}, 0)
	for _, log := range list {
		datum = append(datum, []interface{}{
			log.ModuleName,
			log.Result,
			log.CreatedAt.String(),
		})
	}
	filePath := time.Now().Format("20060102150405") + "系统日志列表.xlsx"
	utils.WriterExcel(filePath,
		[]string{
			"模块名称",
			"操作结果",
			"操作时间",
		},
		datum,
	)
	return filePath, nil
}

// CreateOrUpdateLogsQcSetting 创建或更新日志外发设置
func CreateOrUpdateLogsQcSetting(setting logs_qc_setting.LogsQcSetting) error {
	handlers := make([]mysql.HandleFunc, 0)
	first, err := logs_qc_setting.NewLogsQcSettingModel().First(handlers...)
	if err != nil && err.Error() == "record not found" {
		err = logs_qc_setting.NewLogsQcSettingModel().CreateItem(&setting)
		if err != nil {
			return err
		}
		if setting.Plan == logs_qc_setting.PlanImmediateExecution {
			return ExecLogsQc(setting)
		}
	}

	if err == nil && first.Id != 0 {
		setting.Id = first.Id
		err = logs_qc_setting.NewLogsQcSettingModel().Update(setting, mysql.WithWhere("id = ?", first.Id))
		if err != nil {
			return err
		}
		if setting.Plan == logs_qc_setting.PlanImmediateExecution {
			return ExecLogsQc(setting)
		}
	}
	return nil
}

// ExecLogsQc 执行日志外发
func ExecLogsQc(setting logs_qc_setting.LogsQcSetting) error {
	var err error
	userLogs := make([]audit_logs.UserLogs, 0)
	sysLogs := make([]system_logs.SystemLogs, 0)
	switch setting.LogType {
	case logs_qc_setting.LogTypeUser:
		userLogs, err = execUserLogs()
		if err != nil {
			logger.Errorf("[ExecLogsQc] 执行用户日志外发失败 [err: %v]", err.Error())
		}
	case logs_qc_setting.LogTypeSystem:
		sysLogs, err = execSystemLogs()
		if err != nil {
			logger.Errorf("[ExecLogsQc] 执行系统日志外发失败 [err: %v]", err.Error())
		}
	case logs_qc_setting.LogTypeAll:
		userLogs, err = execUserLogs()
		if err != nil {
			logger.Errorf("[ExecLogsQc] 执行用户日志外发失败 [err: %v]", err.Error())
		}
		sysLogs, err = execSystemLogs()
		if err != nil {
			logger.Errorf("[ExecLogsQc] 执行系统日志外发失败 [err: %v]", err.Error())
		}
	default:
		logger.Errorf("[ExecLogsQc] 未找到日志类型 [type: %d]", setting.LogType)
		return nil
	}
	err = execLogsQc(userLogs, sysLogs, setting)
	if err != nil {
		logger.Errorf("ExecLogsQc error [err: %v]", err)
		return err
	}

	return nil
}

// execLogsQc 执行日志外发
func execLogsQc(userLogs []audit_logs.UserLogs, systemLogs []system_logs.SystemLogs, setting logs_qc_setting.LogsQcSetting) error {
	logger.Infof("-------userLogs--------%v", len(userLogs))
	if len(userLogs) > 0 {
		for _, userLog := range userLogs {
			j, err := json.Marshal(userLog)
			if err != nil {
				logger.Errorf("[execLogsQc] user logs json 序列化失败 [err: %v]", err.Error())
			}
			err = sendSyslogMessage(string(j), setting.Host, int(setting.Port))
			if err != nil {
				logger.Errorf("Failed to send syslog message via UDP: %v", err)
				break
			}
		}
	}
	logger.Infof("-------systemLogs--------%v", len(systemLogs))
	if len(systemLogs) > 0 {
		for _, systemLog := range systemLogs {
			j, err := json.Marshal(systemLog)
			if err != nil {
				logger.Errorf("[execLogsQc] system logs json 序列化失败 [err: %v]", err.Error())
			}
			err = sendSyslogMessage(string(j), setting.Host, int(setting.Port))
			if err != nil {
				logger.Errorf("Failed to send syslog message via UDP: %v", err)
				break
			}
		}
	}
	return nil
}

func sendSyslogMessage(message, serverIP string, port int) error {
	// 构造 logger 命令
	cmd := exec.Command("logger", "-n", serverIP, "-P", fmt.Sprintf("%d", port), "-d", message)
	// 执行命令
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("failed to send syslog message: %v", err)
	}
	return nil
}

// 使用网络 Syslog 服务（UDP）
func sendViaUDP(address, message string) error {
	conn, err := net.Dial("udp", address)
	if err != nil {
		return fmt.Errorf("failed to dial UDP server: %w", err)
	}
	defer conn.Close()

	_, err = conn.Write([]byte(message))
	if err != nil {
		return fmt.Errorf("failed to send message: %w", err)
	}
	return nil
}

// execUserLogs 获取执行用户日志
func execUserLogs() ([]audit_logs.UserLogs, error) {
	list, _, err := audit_logs.NewAuditLogModel().UserList([]int64{}, "", "", 0, 1, 10000, audit_logs.OperationTypeExport)
	if err != nil {
		return nil, err
	}
	return list, nil
}

// execSystemLogs 获取执行用户日志
func execSystemLogs() ([]system_logs.SystemLogs, error) {
	list, _, err := system_logs.NewSystemLogModel().List("", "", 1, 10000, system_logs.OperationTypeExport)
	if err != nil {
		return nil, err
	}
	return list, nil
}
