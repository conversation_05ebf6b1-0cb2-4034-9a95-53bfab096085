package foeye

import (
	"encoding/json"
	"errors"
	"fobrain/fobrain/common/wsocket"
	"fobrain/fobrain/logs"

	"github.com/gin-gonic/gin"
	"github.com/tidwall/gjson"
)

// send 发送消息
func send(conn *wsocket.WsConn, cmd string, data any) error {
	result, err := json.Marshal(gin.H{"cmd": cmd, "data": data})
	if err != nil {
		return err
	}
	return conn.InChanWrite(result)
}

// Parse 解析Ws命令
func Parse(msg string) (cmd string, params map[string]any, err error) {
	params = make(map[string]any, 0)
	// 提取CMD
	cmd = gjson.Get(msg, "cmd").String()
	// 提取参数
	requestData := gjson.Get(msg, "data").String()
	if err = json.Unmarshal([]byte(requestData), &params); err != nil {
		return cmd, params, errors.New("数据解析失败:" + err.Error())
	}
	return cmd, params, nil
}

// Register 节点注册
func Register(conn *wsocket.WsConn, data map[string]any) error {
	// {
	//	"cmd": "register",
	//	"data": {
	//		"mid": "test",
	//		"key": "test",
	//	}
	// }
	logs.GetLogger().Infoln("mid:", data["mid"], "key:", data["key"])
	return send(conn, "register", gin.H{"statusCode": 200, "messages": "success"})
}

// DccHeartBeat 心跳信息
func DccHeartBeat(conn *wsocket.WsConn, data map[string]any) error {
	// {
	//   "cmd": "dccHeartBeat",
	//   "data": {
	//      "service_date":1607702400,   // 服务到期时间
	//      "test_date":1607702400 | 1  // 试用期时间，时间代表试用期时间 1代表正式
	//      "sftp_online": true,    // 数据上传状态
	//      "https_online": false,  // 任务下发状态
	//      "code_version": "2.3.452",  // 软件版本
	//      "poc_version": "21040200",  // POC版本
	//   }
	// }
	logs.GetLogger().Infoln("service_date:", data["service_date"], "test_date:", data["test_date"])
	return nil
}

// SystemConfig 系统配置
func SystemConfig(conn *wsocket.WsConn, data map[string]any) error {
	// {
	//      "cmd" : "systemConfig",
	//      "data" : {
	//           "name":"ens33",
	//           "addr":"10.10.10.167",
	//           "mac":"00:0c:29:de:1b:59",
	//           "mask":"255.255.255.0",
	//           "getway":"10.10.10.1",
	//           "getway_mac":"",
	//           "pramary_dns":"202.106.195.68",
	//           "secondary_dns":"202.106.46.151",
	//           "name2":"eth1",
	//           "addr2":"192.168.55.100",
	//           "mask2":"255.255.255.0",
	//           "getway2":"192.168.55.1",
	//           "cpu":9.7,
	//           "cpu_hz":"3.60GHz",
	//           "mem_total":8156772,
	//           "mem_used":4830664,
	//           "disk_total":"46G",
	//           "disk_used":"30G",
	//           "asset_num":100
	//       }
	//    }
	logs.GetLogger().Infoln(data)
	return nil
}

// Sync 资产上传
func Sync(conn *wsocket.WsConn, data map[string]any) error {
	// {
	//    "cmd": "sync",
	//    "data":{
	//	  "type": "asset",
	//          "taskId": 234233,
	//          "filename":"assets.zip"
	//      }
	//   }
	// type="asset":资产上传;type="subdomain":fofaee的subdomain上传;type="service":fofaee的service上传
	logs.GetLogger().Infoln(data)
	return nil
}
