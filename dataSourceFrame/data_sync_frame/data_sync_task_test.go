package data_sync_frame

import (
	"context"
	"errors"
	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/initialize/mysql"
	"fobrain/models/elastic/source/d01"
	"fobrain/models/elastic/source/foeye"
	"fobrain/models/elastic/source/foradar"
	"fobrain/models/elastic/source/qt_cloud"
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/data_sync_child_task"
	"fobrain/models/mysql/data_sync_task"
	"testing"
	"time"

	sdkframe "git.gobies.org/caasm/fobrain-components/dataSourceSdk/sdk_frame"

	"github.com/DATA-DOG/go-sqlmock"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/stretchr/testify/assert"
)

func TestInitTask(t *testing.T) {
	st := &DataSyncTask{
		Node: &data_source.Node{
			SourceId:        1,
			SoftDeleteModel: mysql.SoftDeleteModel{Id: 1},
		},
		Source: data_source.Source{
			HasAssetData:     true,
			HasVulData:       true,
			HasPersonnelData: true,
		},
		Task: &data_sync_task.DataSyncTask{},
		ChildTasks: map[int64]*data_sync_child_task.DataSyncChildTask{
			data_sync_task.SyncAsset:  {},
			data_sync_task.SyncThreat: {},
			data_sync_task.SyncPeople: {},
		},
	}

	t.Run("asset success", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()

		mockDb.Mock.ExpectBegin()
		mockDb.ExpectExec(
			"INSERT INTO `data_sync_tasks` (`created_at`,`updated_at`,`source_id`,`node_id`,`status`,`source`,`file`,`start_at`,`end_at`,`message`) VALUES (?,?,?,?,?,?,?,?,?,?)",
		).WillReturnResult(sqlmock.NewResult(1, 1))
		mockDb.Mock.ExpectCommit()

		mockDb.Mock.ExpectBegin()
		mockDb.ExpectExec(
			"INSERT INTO `data_sync_child_tasks` (`created_at`,`updated_at`,`source_id`,`node_id`,`task_id`,`status`,`type`,`sync_data_total`,`sync_data_success_total`,`sync_data_drop_total`,`message`,`start_at`,`end_at`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?)",
		).WillReturnResult(sqlmock.NewResult(1, 1))
		mockDb.Mock.ExpectCommit()

		// 测试初始化任务
		err := st.initTask([]int{data_sync_task.SyncAsset}, 1, "test_file")
		assert.Nil(t, err)

		// 验证任务属性
		assert.Equal(t, st.Task.SourceId, st.Node.SourceId)
		assert.Equal(t, st.Task.NodeId, st.Node.Id)
		assert.Equal(t, st.Task.Source, int(1))
		assert.Equal(t, st.Task.File, "test_file")

		// 验证子任务是否创建
		assert.NotNil(t, st.ChildTasks[data_sync_task.SyncAsset])
	})

	t.Run("vul success", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()

		mockDb.Mock.ExpectBegin()
		mockDb.ExpectExec(
			"INSERT INTO `data_sync_tasks` (`created_at`,`updated_at`,`source_id`,`node_id`,`status`,`source`,`file`,`start_at`,`end_at`,`message`,`id`) VALUES (?,?,?,?,?,?,?,?,?,?,?)",
		).WillReturnResult(sqlmock.NewResult(1, 1))
		mockDb.Mock.ExpectCommit()

		mockDb.Mock.ExpectBegin()
		mockDb.ExpectExec(
			"INSERT INTO `data_sync_child_tasks` (`created_at`,`updated_at`,`source_id`,`node_id`,`task_id`,`status`,`type`,`sync_data_total`,`sync_data_success_total`,`sync_data_drop_total`,`message`,`start_at`,`end_at`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?)",
		).WillReturnResult(sqlmock.NewResult(1, 1))
		mockDb.Mock.ExpectCommit()

		// 测试初始化任务
		err := st.initTask([]int{data_sync_task.SyncThreat}, 1, "test_file")
		assert.Nil(t, err)

		// 验证任务属性
		assert.Equal(t, st.Task.SourceId, st.Node.SourceId)
		assert.Equal(t, st.Task.NodeId, st.Node.Id)
		assert.Equal(t, st.Task.Source, int(1))
		assert.Equal(t, st.Task.File, "test_file")

		// 验证子任务是否创建
		assert.NotNil(t, st.ChildTasks[data_sync_task.SyncThreat])

	})
	t.Run("people success", func(t *testing.T) {
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()

		mockDb.Mock.ExpectBegin()
		mockDb.ExpectExec(
			"INSERT INTO `data_sync_tasks` (`created_at`,`updated_at`,`source_id`,`node_id`,`status`,`source`,`file`,`start_at`,`end_at`,`message`,`id`) VALUES (?,?,?,?,?,?,?,?,?,?,?)",
		).WillReturnResult(sqlmock.NewResult(1, 1))
		mockDb.Mock.ExpectCommit()

		mockDb.Mock.ExpectBegin()
		mockDb.ExpectExec(
			"INSERT INTO `data_sync_child_tasks` (`created_at`,`updated_at`,`source_id`,`node_id`,`task_id`,`status`,`type`,`sync_data_total`,`sync_data_success_total`,`sync_data_drop_total`,`message`,`start_at`,`end_at`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?)",
		).WillReturnResult(sqlmock.NewResult(1, 1))
		mockDb.Mock.ExpectCommit()

		// 测试初始化任务
		err := st.initTask([]int{data_sync_task.SyncPeople}, 1, "test_file")
		assert.Nil(t, err)

		// 验证任务属性
		assert.Equal(t, st.Task.SourceId, st.Node.SourceId)
		assert.Equal(t, st.Task.NodeId, st.Node.Id)
		assert.Equal(t, st.Task.Source, int(1))
		assert.Equal(t, st.Task.File, "test_file")

		// 验证子任务是否创建
		assert.NotNil(t, st.ChildTasks[data_sync_task.SyncPeople])
	})

}

func TestGetSourceIndexModel(t *testing.T) {
	st := &DataSyncTask{
		NodeConfig: map[string]any{
			"version": "v2",
		},
		Source: data_source.Source{
			EnName: "test_source",
		},
	}

	tests := []struct {
		sourceId  uint64
		syncType  int64
		expected2 string
	}{
		{data_source.FoeyeSourceId, data_sync_task.SyncAsset, foeye.FoeyeV2SourceTaskAssetsIndex},
		{data_source.FoeyeSourceId, data_sync_task.SyncThreat, foeye.FoeyeV2SourceTaskThreatsIndex},
		{data_source.D01SourceId, data_sync_task.SyncAsset, d01.D01V2SourceTaskAssetsIndex},
		{data_source.ForadarSourceId, data_sync_task.SyncAsset, foradar.NewForadarTaskAssetsModel().IndexName()},
		{data_source.QTCloudSourceId, data_sync_task.SyncPeople, qt_cloud.NewQTCloudTaskLinuxEmployeesModel().IndexName()},
		{9999, data_sync_task.SyncAsset, "test_source_task_assets"}, // 测试未知数据源
	}

	for _, test := range tests {
		index := st.getSourceIndexModel(test.sourceId, test.syncType)
		if index != test.expected2 {
			t.Errorf("For sourceId: %d, syncType: %d, expected %s, got %s", test.sourceId, test.syncType, test.expected2, index)
		}
	}
}

// func TestAppendAttribute(t *testing.T) {
// 	st := NewDataSyncTask()
// 	res := st.appendAttribute(data_sync_task.SyncPeople, make(map[string]interface{}))
// 	assert.NotNil(t, res)
// }

func TestSyncTask_UpdateSyncDataSuccessTotal(t *testing.T) {
	time.Sleep(time.Millisecond * 300)
	patch := gomonkey.ApplyMethodReturn(&data_sync_child_task.DataSyncChildTask{}, "UpdateSyncDataSuccessTotal", nil)
	err := NewDataSyncTask().UpdateSyncDataSuccessTotal(1, 1)
	patch.Reset()
	assert.Nil(t, err)
	time.Sleep(time.Millisecond * 300)
	patch = gomonkey.ApplyMethodReturn(&data_sync_child_task.DataSyncChildTask{}, "UpdateSyncDataSuccessTotal", errors.New("err"))
	err = NewDataSyncTask().UpdateSyncDataSuccessTotal(1, 1)
	patch.Reset()
	assert.NotNil(t, err)
}

func TestSyncTask_UpdateSyncDataTotalal(t *testing.T) {
	patch := gomonkey.ApplyMethodReturn(&data_sync_child_task.DataSyncChildTask{}, "UpdateSyncDataTotal", nil)
	time.Sleep(time.Millisecond * 300)
	err := NewDataSyncTask().UpdateSyncDataTotal(1, 1)
	patch.Reset()
	assert.Nil(t, err)

	patch = gomonkey.ApplyMethodReturn(&data_sync_child_task.DataSyncChildTask{}, "UpdateSyncDataTotal", errors.New("err"))
	time.Sleep(time.Millisecond * 300)
	err = NewDataSyncTask().UpdateSyncDataTotal(1, 1)
	patch.Reset()
	assert.NotNil(t, err)
}

func TestSyncTask_SetStatus(t *testing.T) {
	mockDb := testcommon.InitSqlMock()
	defer mockDb.Close()
	mockDb.ExpectQuery("SELECT * FROM `data_sync_child_tasks` WHERE `id` = ? ORDER BY `data_sync_child_tasks`.`id` LIMIT 1").
		WithArgs(sqlmock.AnyArg()).
		WillReturnRows(sqlmock.NewRows([]string{"sync_data_success_total", "sync_data_total"}).AddRow(3, 4))

	err := NewDataSyncTask().SetStatus(1, 1, "")
	assert.Nil(t, err)
}

func TestDataSyncTask_CheckNodeInfo(t *testing.T) {
	st := NewDataSyncTask()
	mockDb := testcommon.InitSqlMock()
	defer mockDb.Close()
	mockDb.ExpectQuery("SELECT * FROM `data_nodes` WHERE `id` = ? AND `data_nodes`.`deleted_at` IS NULL ORDER BY `data_nodes`.`id` LIMIT 1").
		WithArgs(sqlmock.AnyArg()).
		WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))

	mockDb.ExpectQuery("SELECT * FROM `data_node_configs` WHERE `node_id` = ?").
		WithArgs(sqlmock.AnyArg()).
		WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))

	mockDb.ExpectQuery("SELECT * FROM `data_sources` WHERE `id` = ? ORDER BY `data_sources`.`id` LIMIT 1").
		WithArgs(sqlmock.AnyArg()).
		WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))

	err := st.CheckNodeInfo(1)
	assert.Nil(t, err)
}

type mockSDK struct{}

func (m *mockSDK) NetworktConnectTest(ctx context.Context, config map[string]any) error {
	return nil
}
func (m *mockSDK) SyncAssets(ctx context.Context, operate sdkframe.DataSyncOperate) (*sdkframe.SyncResponse, error) {
	return &sdkframe.SyncResponse{Total: 100, Sync: 90, Success: 80}, nil
}
func (m *mockSDK) SyncVulnerability(ctx context.Context, operate sdkframe.DataSyncOperate) (*sdkframe.SyncResponse, error) {
	return &sdkframe.SyncResponse{Total: 200, Sync: 180, Success: 170}, nil
}
func (m *mockSDK) SyncRelations(ctx context.Context, operate sdkframe.DataSyncOperate) (*sdkframe.SyncResponse, error) {
	return &sdkframe.SyncResponse{Total: 200, Sync: 180, Success: 170}, nil
}
func (m *mockSDK) SyncStaff(ctx context.Context, operate sdkframe.DataSyncOperate) (*sdkframe.SyncResponse, error) {
	return &sdkframe.SyncResponse{Total: 50, Sync: 40, Success: 35}, nil
}

func TestSync(t *testing.T) {
	ctx := context.Background()
	mockTask := NewDataSyncTask()
	mockSdk := &mockSDK{}

	t.Run("SyncAssets Success", func(t *testing.T) {
		err := mockTask.Sync(ctx, data_sync_task.SyncAsset, mockSdk)
		assert.NoError(t, err)
	})

	t.Run("SyncVulnerability Success", func(t *testing.T) {
		err := mockTask.Sync(ctx, data_sync_task.SyncThreat, mockSdk)
		assert.NoError(t, err)
	})

	t.Run("SyncStaff Success", func(t *testing.T) {
		err := mockTask.Sync(ctx, data_sync_task.SyncPeople, mockSdk)
		assert.NoError(t, err)
	})

	t.Run("Invalid SyncType", func(t *testing.T) {
		err := mockTask.Sync(ctx, -1, mockSdk)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "can not sync data type")
	})
}

func TestDataSyncTask_Dispatch(t *testing.T) {
	t.Run("no sdk", func(t *testing.T) {
		st := NewDataSyncTask()
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()
		mockDb.ExpectQuery("SELECT * FROM `data_nodes` WHERE `id` = ? AND `data_nodes`.`deleted_at` IS NULL ORDER BY `data_nodes`.`id` LIMIT 1").
			WithArgs(sqlmock.AnyArg()).
			WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))

		mockDb.ExpectQuery("SELECT * FROM `data_node_configs` WHERE `node_id` = ?").
			WithArgs(sqlmock.AnyArg()).
			WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))

		mockDb.ExpectQuery("SELECT * FROM `data_sources` WHERE `id` = ? ORDER BY `data_sources`.`id` LIMIT 1").
			WithArgs(sqlmock.AnyArg()).
			WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))

		err := st.Dispatch(context.TODO(), 1, []int{1}, 1, "")
		assert.Error(t, err)
	})
	t.Run("inittask error", func(t *testing.T) {
		st := NewDataSyncTask()
		st.Source.Id = data_source.FoeyeSourceId
		mockDb := testcommon.InitSqlMock()
		defer mockDb.Close()
		mockDb.ExpectQuery("SELECT * FROM `data_nodes` WHERE `id` = ? AND `data_nodes`.`deleted_at` IS NULL ORDER BY `data_nodes`.`id` LIMIT 1").
			WithArgs(sqlmock.AnyArg()).
			WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))

		mockDb.ExpectQuery("SELECT * FROM `data_node_configs` WHERE `node_id` = ?").
			WithArgs(sqlmock.AnyArg()).
			WillReturnRows(sqlmock.NewRows([]string{"node_id", "key", "value"}).AddRow(1, "version", "v2"))

		mockDb.ExpectQuery("SELECT * FROM `data_sources` WHERE `id` = ? ORDER BY `data_sources`.`id` LIMIT 1").
			WithArgs(sqlmock.AnyArg()).
			WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))

		err := st.Dispatch(context.TODO(), 1, []int{1}, 1, "")
		assert.Error(t, err)
	})

}
