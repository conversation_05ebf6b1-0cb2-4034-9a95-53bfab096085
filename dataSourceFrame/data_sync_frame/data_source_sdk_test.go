package data_sync_frame

import (
	"fobrain/models/mysql/data_source"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestNewDataSourceSDK(t *testing.T) {
	t.Run("foeyev2", func(t *testing.T) {
		got := NewDataSourceSDK(data_source.FoeyeSourceId, "v2")
		assert.NotNil(t, got)
	})
	t.Run("9999", func(t *testing.T) {
		got := NewDataSourceSDK(9999, "v1")
		assert.Nil(t, got)
	})
	t.Run("foeyev1", func(t *testing.T) {
		got := NewDataSourceSDK(data_source.FoeyeSourceId, "v1")
		assert.Nil(t, got)
	})
	t.Run("d01 v2", func(t *testing.T) {
		got := NewDataSourceSDK(data_source.D01SourceId, "v2")
		assert.NotNil(t, got)
	})
}
