package data_sync_frame

import (
	"fobrain/models/mysql/data_source"
	"git.gobies.org/caasm/fobrain-components/dataSourceSdk/json_source_frame"
	youyue "git.gobies.org/caasm/fobrain-components/dataSourceSdk/youyue/sdk"

	//"git.gobies.org/caasm/fobrain-components/dataSourceSdk/json_source_frame"

	aliyun_slb "git.gobies.org/caasm/fobrain-components/dataSourceSdk/aliyun/slb"
	d01_v2 "git.gobies.org/caasm/fobrain-components/dataSourceSdk/d01/v2"
	foeyev2 "git.gobies.org/caasm/fobrain-components/dataSourceSdk/foeye/v2"
	haiyiPas "git.gobies.org/caasm/fobrain-components/dataSourceSdk/haiyi/pas"
	hw_ecs "git.gobies.org/caasm/fobrain-components/dataSourceSdk/huawei/ecs"
	huaweiManagerOne "git.gobies.org/caasm/fobrain-components/dataSourceSdk/huawei/manager_one"
	jumpserver "git.gobies.org/caasm/fobrain-components/dataSourceSdk/jump_server/sdk"
	k8s "git.gobies.org/caasm/fobrain-components/dataSourceSdk/k8s/svc"
	nsfous_rsas "git.gobies.org/caasm/fobrain-components/dataSourceSdk/nsfocus/rsas"
	qianxin_server "git.gobies.org/caasm/fobrain-components/dataSourceSdk/qianxin/sdk"
	qianxinPam "git.gobies.org/caasm/fobrain-components/dataSourceSdk/qianxin_bastion_host/sdk"
	qianxinTianqing "git.gobies.org/caasm/fobrain-components/dataSourceSdk/qianxin_tianqing/tq_sdk"
	qingteng "git.gobies.org/caasm/fobrain-components/dataSourceSdk/qingteng/sdk"
	rizhiyi "git.gobies.org/caasm/fobrain-components/dataSourceSdk/rizhiyi/sdk"
	"git.gobies.org/caasm/fobrain-components/dataSourceSdk/sangfor-sip/sip"
	sdkframe "git.gobies.org/caasm/fobrain-components/dataSourceSdk/sdk_frame"
	tenableSc "git.gobies.org/caasm/fobrain-components/dataSourceSdk/tenable/sc"
	tencent_clb "git.gobies.org/caasm/fobrain-components/dataSourceSdk/tencent/clb"
	tencent_cvm "git.gobies.org/caasm/fobrain-components/dataSourceSdk/tencent/cvm"
	ucloud "git.gobies.org/caasm/fobrain-components/dataSourceSdk/ucloud/sdk"
	tencent_ud "git.gobies.org/caasm/fobrain-components/dataSourceSdk/udAPI/ud_sdk"
	weibu_onesec "git.gobies.org/caasm/fobrain-components/dataSourceSdk/weibu/onesec/sdk"
	youyun "git.gobies.org/caasm/fobrain-components/dataSourceSdk/youyun/sdk"
	yuntu "git.gobies.org/caasm/fobrain-components/dataSourceSdk/yuntu/sdk"
)

func NewDataSourceSDK(sourceId uint64, version string) sdkframe.DataSourceSDK {
	if sourceId == data_source.FoeyeSourceId && version == "v2" {
		return foeyev2.NewFoeyev2()
	}
	if sourceId == data_source.D01SourceId && version == "v2" {
		return d01_v2.NewD01V2()
	}
	var DataSourceRegister = map[uint64]sdkframe.DataSourceSDK{
		data_source.YuntuSourceId:         yuntu.NewYuntuSDK(),
		data_source.UCloudSourceId:        ucloud.NewUCloud(),
		data_source.AliyunSLBSourceId:     aliyun_slb.NewAliyunSLB(),
		data_source.RizhiyiSourceId:       rizhiyi.NewRizhiyi(),
		data_source.QianxinServerSourceId: qianxin_server.NewQianxin(),
		// data_source.PublicAliyunSlbSourceId:(),
		// data_source.PublicliyunEcsSourceId:(),
		data_source.PublicTencentCVMSourceId: tencent_cvm.NewTencentCvm(),
		data_source.PublicTencentCLBSourceId: tencent_clb.NewTencentCLB(),
		data_source.PublicHuaweiEcsSourceId:  hw_ecs.NewHuaweicloudECS(),
		//data_source.HuaweiHkCloudSourceId:        huawei_hk_cloud.New(),
		//data_source.BKCmdbCustomDomainSourceId:   nodeBkCmdbCustom.NewBkCmdbCustom(),
		//data_source.BKCmdbCustomBusinessSourceId: nodeBkCmdbCustom.NewBkCmdbCustomBusiness(),
		//data_source.BKCmdbCustomF5VsSourceId:     nodeBkCmdbCustom.NewBkCmdbCustomF5Vs(),
		//data_source.BKCmdbCustomF5PoolSourceId:   nodeBkCmdbCustom.NewBkCmdbCustomF5Pool(),
		//data_source.ZhongyiFeishuSourceId:        zhongyi_feishu.NewZhongyiFeishuTask(),
		//data_source.QTCloudSourceId:              qt_cloud.NewQingTengSync(), //err = qt_cloud.SyncQTCloud(node, string(marshal))
		data_source.SangforSIPSourceId:            sip.NewSIP(),
		data_source.JumpServerSourceId:            jumpserver.NewJumpServer(),
		data_source.WeibuOnesecSourceId:           weibu_onesec.NewWeibuOneSec(),
		data_source.NSFocusRsasSourceId:           nsfous_rsas.NewRSAS(),
		data_source.K8SSourceId:                   k8s.NewK8SSvc(),
		data_source.HaiyiPasSourceId:              haiyiPas.NewPAS(),
		data_source.TenableScSourceId:             tenableSc.NewTenableSC(),
		data_source.QiAnXinPamSourceId:            qianxinPam.NewPAM(),
		data_source.YouYunCMDBDataSourceId:        youyun.NewYouyun(),
		data_source.HuaweiPrivateCloudEcsSourceId: hw_ecs.NewHuaweicloudECS(),
		data_source.QingTengBeehiveDataSourceId:   qingteng.NewBeehive(),
		data_source.YouYueCMDBDataSourceId:        youyue.NewYouYue(),
		data_source.QiAnXinTianQingSourceId:       qianxinTianqing.NewTianQingSDK(),
		data_source.TencentUDSourceId:             tencent_ud.NewUD(),
		data_source.HuaweiManagerOneSourceId:      huaweiManagerOne.NewManagerOne(),
	}

	// 以下为json配置化接入数据源
	var jsonSourceMap = map[uint64]string{
		//data_source.WeibuOnesecSourceId:           "onesec_config",
		data_source.SangforSSLVPNSourceId: "sangfor_ssl_vpn",
		data_source.AnhengAPT:             "anheng_apt",
	}
	for id, jsonName := range jsonSourceMap {
		t, err := json_source_frame.NewFramework(jsonName)
		if err != nil {
			panic(err)
		}
		DataSourceRegister[id] = t
	}

	nodeInterface, ok := DataSourceRegister[sourceId]
	if ok {
		return nodeInterface
	}
	return nil
}
