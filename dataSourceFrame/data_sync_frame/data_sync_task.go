package data_sync_frame

import (
	"context"
	"errors"
	"fmt"
	"fobrain/fobrain/common/localtime"
	"fobrain/fobrain/logs"
	"fobrain/initialize/mysql"
	"fobrain/models/elastic/source/aliyun_cloud"
	"fobrain/models/elastic/source/aliyun_slb"
	"fobrain/models/elastic/source/bk_cmdb"
	"fobrain/models/elastic/source/changting_waf"
	"fobrain/models/elastic/source/d01"
	"fobrain/models/elastic/source/data_import"
	"fobrain/models/elastic/source/dingtalk"
	"fobrain/models/elastic/source/file_import"
	"fobrain/models/elastic/source/foeye"
	"fobrain/models/elastic/source/foradar"
	"fobrain/models/elastic/source/huawei_cloud"
	"fobrain/models/elastic/source/mach_lake"
	"fobrain/models/elastic/source/qianxin_server"
	qizhi_uaudithost2 "fobrain/models/elastic/source/qizhi_uaudithost"
	"fobrain/models/elastic/source/qt_cloud"
	"fobrain/models/elastic/source/rizhiyi"
	"fobrain/models/elastic/source/tencent"
	"fobrain/models/elastic/source/ucloud"
	"fobrain/models/elastic/source/weibu"
	xray "fobrain/models/elastic/source/x-ray"
	"fobrain/models/elastic/source/youyun"
	"fobrain/models/elastic/source/yuntu"
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/data_sync_child_task"
	"fobrain/models/mysql/data_sync_task"
	"fobrain/pkg/utils"
	"sync"
	"time"

	"github.com/spf13/cast"

	sdkframe "git.gobies.org/caasm/fobrain-components/dataSourceSdk/sdk_frame"
)

type DataSyncTask struct {
	Task       *data_sync_task.DataSyncTask
	ChildTasks map[int64]*data_sync_child_task.DataSyncChildTask
	Node       *data_source.Node
	NodeConfig map[string]any
	Source     data_source.Source
	AssetOnce  sync.Once
	VulOnce    sync.Once
	StaffOnce  sync.Once
	mu         sync.Mutex
}

type Option func(*DataSyncTask)

func NewDataSyncTask(opts ...Option) *DataSyncTask {
	st := &DataSyncTask{
		Task: &data_sync_task.DataSyncTask{},
		ChildTasks: map[int64]*data_sync_child_task.DataSyncChildTask{
			1: {}, //资产类型
			2: {}, //漏洞类型
			3: {}, //人员类型
			4: {}, // 内外网映射关系
		},
		Node:      &data_source.Node{},
		AssetOnce: sync.Once{},
		StaffOnce: sync.Once{},
		VulOnce:   sync.Once{},
		mu:        sync.Mutex{},
	}
	for _, opt := range opts {
		opt(st)
	}
	return st
}

func (st *DataSyncTask) CheckNodeInfo(nodeId uint64) error {
	// 获取节点信息
	node, err := data_source.NewNodeModel().First(mysql.WithWhere("id", nodeId))
	if err != nil {
		st.WriteLog(fmt.Sprintf("sync data get node err : %+v", err))
		return err
	}
	//获取节点配置及client代理等
	config, err := data_source.NewNodeConfigModel().GetNodeConfig(nodeId)
	if err != nil {
		st.WriteLog(fmt.Sprintf("sync data get node config err : %+v", err))
		return err
	}

	st.NodeConfig = config

	//获取数据源信息
	dataSource, err := data_source.NewSourceModel().First(mysql.WithWhere("id", node.SourceId))
	if err != nil {
		st.WriteLog(fmt.Sprintf("sync data get source err : %+v", err))
		return err
	}
	st.Source = dataSource
	st.Node = node

	return nil
}

// initTask 初始化主任务及子任务
func (st *DataSyncTask) initTask(syncTypes []int, taskFrom int64, fileName string) error {
	node := st.Node
	dataSource := st.Source
	st.Task.SourceId = node.SourceId
	st.Task.NodeId = node.Id
	st.Task.Source = int(taskFrom)
	st.Task.File = fileName
	st.Task.StartAt = localtime.Time(time.Now())

	//创建任务信息
	if err := data_sync_task.NewDataSyncTaskModel().Create(st.Task); err != nil {
		return err
	}

	//创建子任务
	// 数据源存在数据资产数据且在本次同步的类型当中创建任务
	if dataSource.HasAssetData && utils.InArray(data_sync_task.SyncAsset, syncTypes) {
		st.ChildTasks[data_sync_task.SyncAsset].SourceId = node.SourceId
		st.ChildTasks[data_sync_task.SyncAsset].NodeId = node.Id
		st.ChildTasks[data_sync_task.SyncAsset].TaskId = st.Task.Id
		st.ChildTasks[data_sync_task.SyncAsset].Type = data_sync_task.SyncAsset
		err := data_sync_child_task.NewDataSyncChildTaskModel().Create(st.ChildTasks[data_sync_task.SyncAsset])
		if err != nil {
			return err
		}
	}
	if dataSource.HasRelations && utils.InArray(data_sync_task.SyncRelations, syncTypes) {
		st.ChildTasks[data_sync_task.SyncRelations].SourceId = node.SourceId
		st.ChildTasks[data_sync_task.SyncRelations].NodeId = node.Id
		st.ChildTasks[data_sync_task.SyncRelations].TaskId = st.Task.Id
		st.ChildTasks[data_sync_task.SyncRelations].Type = data_sync_task.SyncRelations
		err := data_sync_child_task.NewDataSyncChildTaskModel().Create(st.ChildTasks[data_sync_task.SyncRelations])
		if err != nil {
			return err
		}
	}

	if dataSource.HasVulData && utils.InArray(data_sync_task.SyncThreat, syncTypes) {
		st.ChildTasks[data_sync_task.SyncThreat].SourceId = node.SourceId
		st.ChildTasks[data_sync_task.SyncThreat].NodeId = node.Id
		st.ChildTasks[data_sync_task.SyncThreat].TaskId = st.Task.Id
		st.ChildTasks[data_sync_task.SyncThreat].Type = data_sync_task.SyncThreat
		err := data_sync_child_task.NewDataSyncChildTaskModel().Create(st.ChildTasks[data_sync_task.SyncThreat])
		if err != nil {
			return err
		}
	}

	if dataSource.HasPersonnelData && utils.InArray(data_sync_task.SyncPeople, syncTypes) {
		st.ChildTasks[data_sync_task.SyncPeople].SourceId = node.SourceId
		st.ChildTasks[data_sync_task.SyncPeople].NodeId = node.Id
		st.ChildTasks[data_sync_task.SyncPeople].TaskId = st.Task.Id
		st.ChildTasks[data_sync_task.SyncPeople].Type = data_sync_task.SyncPeople
		err := data_sync_child_task.NewDataSyncChildTaskModel().Create(st.ChildTasks[data_sync_task.SyncPeople])
		if err != nil {
			return err
		}
	}

	return nil
}

// 新框架的Dispatch
func (st *DataSyncTask) Dispatch(ctx context.Context, nodeId uint64, syncTypeArray []int, taskFrom int64, fileName string) (err error) {
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("recover node task DataSyncTask Dispatch err: %v", r)
		}
	}()

	logMsg := fmt.Sprintf("DataSyncTask Dispatch: sourceId:%d,nodeId:%d,syncType:%d,taskFrom:%d,taskId:%d",
		st.Task.SourceId, st.Node.Id, syncTypeArray, taskFrom, st.Task.Id)

	logs.GetLogger().Infof("init " + logMsg)
	if err := st.CheckNodeInfo(nodeId); err != nil {
		return err
	}

	sdk := NewDataSourceSDK(st.Source.Id, cast.ToString(st.NodeConfig["version"]))
	if sdk == nil {
		return fmt.Errorf("can not get data source sdk")
	}

	//初始化任务
	if err := st.initTask(syncTypeArray, taskFrom, fileName); err != nil {
		return err
	}

	//更新主任务状态为进行中
	if err := data_sync_task.NewDataSyncTaskModel().UpdateStatusById(st.Task.Id, data_sync_task.StatusDoing); err != nil {
		st.WriteLog(fmt.Sprintf("sync data err : %+v", err))
		return err
	}
	var wg sync.WaitGroup
	var errChan = make(chan error)
	for syncTypeKey, childTask := range st.ChildTasks {
		if childTask.Id == 0 {
			continue
		}
		wg.Add(1)
		go func() {
			defer wg.Done()
			st.WriteLog(fmt.Sprintf("start sync %s,syncType:%d", logMsg, syncTypeKey))
			// 设置主任务状态为进行中
			err = st.SetStatus(syncTypeKey, data_sync_child_task.StatusDoing, "")
			if err != nil {
				st.WriteLog(fmt.Sprintf("ProcessTask SetStatus err: %+v", err.Error()))
				errChan <- err
				return
			}
			// 执行同步
			if err := st.Sync(ctx, syncTypeKey, sdk); err != nil {
				// 设置主任务状态为失败
				setStatusErr := st.SetStatus(syncTypeKey, data_sync_child_task.StatusFail, err.Error())
				if setStatusErr != nil {
					st.WriteLog(fmt.Sprintf("ProcessTask SetStatus err: %+v", setStatusErr.Error()))
				}
				errChan <- err
				return
			}

			// 设置主任务状态为成功
			err = st.SetStatus(syncTypeKey, data_sync_child_task.StatusSuccess, "")
			if err != nil {
				st.WriteLog(fmt.Sprintf("ProcessTask Dispatch SetStatus err: %+v", err.Error()))
				errChan <- err
				return
			}
		}()

	}
	go func() {
		wg.Wait()
		close(errChan)
	}()
	var errMsg string
	for err := range errChan {
		if err != nil {
			errMsg += err.Error()
		}
	}
	if errMsg != "" {
		return errors.New(errMsg)
	}

	st.WriteLog("sync data success," + logMsg)
	return nil
}

func (st *DataSyncTask) Sync(ctx context.Context, syncType int64, sdk sdkframe.DataSourceSDK) (err error) {
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("recover node task DataSyncTask Sync err: %v", r)
		}
	}()

	var resp *sdkframe.SyncResponse
	switch syncType {
	case data_sync_task.SyncAsset:
		resp, err = sdk.SyncAssets(ctx, st)
	case data_sync_task.SyncThreat:
		resp, err = sdk.SyncVulnerability(ctx, st)
	case data_sync_task.SyncPeople:
		resp, err = sdk.SyncStaff(ctx, st)
	default:
		return fmt.Errorf("can not sync data type %d", syncType)
	}

	if err != nil {
		return err
	}

	if resp != nil {
		logs.GetSyncLogger().Infof("DataSyncTask Sync Sync END,全部数量:%d,拿到的数量:%d,处理成功的数量:%d\n", resp.Total, resp.Sync, resp.Success)
	}

	return nil
}

/*
资产同步后会有source、sourceTask、process_asset、process_device
漏洞同步后会有source、sourceTask、process_poc
人员同步后会有source、sourceTask、process_staff
1、所有的数据都由sourceDatas生成，是个[]map[string]any类型
2、转换时生成过程索引数据，应该由固定对象做约束
3、sourceData会生成一个sourceTaskData
4、结果表会根据task_data_id查到原始任务表，过程表由查到原始表，原始表根据查到sourceTask表
*/
func (st *DataSyncTask) appendAttribute(syncType int64, item map[string]interface{}) map[string]interface{} {
	switch syncType {
	case data_sync_task.SyncAsset:
		item["asset_task_id"] = item["id"]
	case data_sync_task.SyncThreat:
		item["poc_task_id"] = item["id"]
	case data_sync_task.SyncPeople:
		item["staff_task_id"] = item["id"]
	default:
		return nil
	}

	item["source"] = st.Node.SourceId
	item["source_id"] = st.Node.SourceId
	item["node_id"] = st.Node.Id
	item["node"] = st.Node.Id
	ip, _ := item["ip"].(string)
	areaId, _ := st.Node.GetAreaByIp(ip)
	item["area_id"] = areaId
	item["area"] = areaId
	item["task_id"] = st.Task.Id
	if syncType != data_sync_task.SyncPeople {
		networkType, err := st.Node.GetNetworkType()
		if err != nil {
			return nil
		}
		item["network_type"] = networkType
	}
	item["child_task_id"] = st.ChildTasks[syncType].Id
	item["sync_created_at"] = localtime.NewLocalTime(time.Now())
	item["sync_updated_at"] = localtime.NewLocalTime(time.Now())
	return item
}

var SourceIndexNameMap = map[string]string{
	// foeye v2和v3
	genSourceIndexMapKey(data_source.FoeyeSourceId, data_sync_task.SyncAsset, "v2"):  foeye.FoeyeV2SourceTaskAssetsIndex,
	genSourceIndexMapKey(data_source.FoeyeSourceId, data_sync_task.SyncThreat, "v2"): foeye.FoeyeV2SourceTaskThreatsIndex,
	genSourceIndexMapKey(data_source.FoeyeSourceId, data_sync_task.SyncAsset, ""):    foeye.NewFoeyeTaskAssetsModel().IndexName(),
	genSourceIndexMapKey(data_source.FoeyeSourceId, data_sync_task.SyncThreat, ""):   foeye.NewFoeyeTaskThreatsModel().IndexName(),

	// d01 v2和v3
	genSourceIndexMapKey(data_source.D01SourceId, data_sync_task.SyncAsset, ""):    d01.NewTaskAssetsModel().IndexName(),
	genSourceIndexMapKey(data_source.D01SourceId, data_sync_task.SyncThreat, ""):   d01.NewTaskThreatsModel().IndexName(),
	genSourceIndexMapKey(data_source.D01SourceId, data_sync_task.SyncAsset, "v2"):  d01.D01V2SourceTaskAssetsIndex,
	genSourceIndexMapKey(data_source.D01SourceId, data_sync_task.SyncThreat, "v2"): d01.D01V2SourceTaskThreatsIndex,

	genSourceIndexMapKey(data_source.ForadarSourceId, data_sync_task.SyncAsset, ""):  foradar.NewForadarTaskAssetsModel().IndexName(),
	genSourceIndexMapKey(data_source.ForadarSourceId, data_sync_task.SyncThreat, ""): foradar.NewForadarTaskThreatsModel().IndexName(),

	genSourceIndexMapKey(data_source.QTCloudSourceId, data_sync_task.SyncAsset, ""):  qt_cloud.NewQTCloudTaskAssetsModel().IndexName(),
	genSourceIndexMapKey(data_source.QTCloudSourceId, data_sync_task.SyncThreat, ""): qt_cloud.NewQTCloudTaskThreatsModel().IndexName(),
	genSourceIndexMapKey(data_source.QTCloudSourceId, data_sync_task.SyncPeople, ""): qt_cloud.NewQTCloudTaskLinuxEmployeesModel().IndexName(),

	genSourceIndexMapKey(data_source.DingTalkSourceId, data_sync_task.SyncPeople, ""): dingtalk.NewDingtalkTaskEmployeesModel().IndexName(),

	genSourceIndexMapKey(data_source.BKCmdbSourceId, data_sync_task.SyncAsset, ""):  bk_cmdb.NewBKCmdbTaskAssetsModel().IndexName(),
	genSourceIndexMapKey(data_source.BKCmdbSourceId, data_sync_task.SyncPeople, ""): bk_cmdb.NewBKCmdbTaskEmployeesModel().IndexName(),

	genSourceIndexMapKey(data_source.FileImportSourceId, data_sync_task.SyncAsset, ""):  file_import.NewFileImportTaskAssetsModel().IndexName(),
	genSourceIndexMapKey(data_source.FileImportSourceId, data_sync_task.SyncThreat, ""): file_import.NewFileImportTaskThreatsModel().IndexName(),
	genSourceIndexMapKey(data_source.FileImportSourceId, data_sync_task.SyncPeople, ""): file_import.NewFileImportTaskPeoplesModel().IndexName(),

	genSourceIndexMapKey(data_source.QiZhiUAuditHostSourceId, data_sync_task.SyncAsset, ""): qizhi_uaudithost2.NewQiZhiUAuditHostTaskAssetsModel().IndexName(),

	genSourceIndexMapKey(data_source.AliYunCloudSourceId, data_sync_task.SyncThreat, ""): aliyun_cloud.NewAliYunCloudTaskThreatsModel().IndexName(),

	genSourceIndexMapKey(data_source.WeiBuSourceId, data_sync_task.SyncAsset, ""):  weibu.NewWeibuTaskAssetsModel().IndexName(),
	genSourceIndexMapKey(data_source.WeiBuSourceId, data_sync_task.SyncThreat, ""): weibu.NewWeibuTaskThreatsModel().IndexName(),

	genSourceIndexMapKey(data_source.ChangTingWAFSourceId, data_sync_task.SyncAsset, ""): changting_waf.NewChangtingWafTaskAssetsModel().IndexName(),

	genSourceIndexMapKey(data_source.MachLakeSourceId, data_sync_task.SyncAsset, ""): mach_lake.NewMachLakeTaskAssetsModel().IndexName(),

	genSourceIndexMapKey(data_source.XRaySourceId, data_sync_task.SyncThreat, ""): xray.NewTaskThreatsModel().IndexName(),
	genSourceIndexMapKey(data_source.XRaySourceId, data_sync_task.SyncAsset, ""):  xray.NewTaskAssetsModel().IndexName(),
	// ucloud
	genSourceIndexMapKey(data_source.UCloudSourceId, data_sync_task.SyncAsset, ""): ucloud.NewTaskAssetsModel().IndexName(),

	// 阿里云负载均衡SLB
	genSourceIndexMapKey(data_source.AliyunSLBSourceId, data_sync_task.SyncAsset, ""): aliyun_slb.NewTaskAssetsModel().IndexName(),

	// 云图
	genSourceIndexMapKey(data_source.YuntuSourceId, data_sync_task.SyncAsset, ""):  yuntu.NewTaskAssetsModel().IndexName(),
	genSourceIndexMapKey(data_source.YuntuSourceId, data_sync_task.SyncThreat, ""): yuntu.NewTaskThreatsModel().IndexName(),

	// 日志易
	genSourceIndexMapKey(data_source.RizhiyiSourceId, data_sync_task.SyncAsset, ""):  rizhiyi.NewTaskAssetsModel().IndexName(),
	genSourceIndexMapKey(data_source.RizhiyiSourceId, data_sync_task.SyncPeople, ""): rizhiyi.NewTaskEmployeesModel().IndexName(),

	// 奇安信
	genSourceIndexMapKey(data_source.QianxinServerSourceId, data_sync_task.SyncAsset, ""): qianxin_server.NewTaskAssetsModel().IndexName(),

	// 2025年4月9日 此后新加数据源不再保存原始数据的索引，只保存带task的索引
	// 腾讯云
	genSourceIndexMapKey(data_source.PublicTencentCVMSourceId, data_sync_task.SyncAsset, ""): tencent.NewTencentCVMTaskAssets().IndexName(),
	genSourceIndexMapKey(data_source.PublicTencentCLBSourceId, data_sync_task.SyncAsset, ""): tencent.NewTencentCLBTaskAssets().IndexName(),

	// 华为云
	genSourceIndexMapKey(data_source.PublicHuaweiEcsSourceId, data_sync_task.SyncAsset, ""): huawei_cloud.NewHuaweiCloudEcsTaskAssets().IndexName(),

	// YouYunCMDB
	genSourceIndexMapKey(data_source.YouYunCMDBDataSourceId, data_sync_task.SyncAsset, ""): youyun.NewTaskAssetModel().IndexName(),
}
var dataSourcesEnName = []string{
	"foeye",
	"foradar_saas",
	"qty",
	"dingtalk",
	"bk_cmdb",
	"file_import",
	"handwork",
	"WeiBuTDP",
	"LongiWAF",
	"d01",
	"qizhi_uaudithost",
	"aliyun_cloud",
	"MachLake",
	"x_ray",
	"bk_cmdb_vm_machine",
	"bk_cmdb_cloud_ecs",
	"huawei_hk_cloud",
	"bk_cmdb_domain",
	"bk_cmdb_business",
	"bk_cmdb_f5_vs",
	"bk_cmdb_f5_pool",
	"zhongyi_feishu",
	"yuntu",
	"ucloud",
	"rizhiyi",
	"aliyun_slb",
	"public_aliyun_slb",
	"public_aliyun_ecs",
	"public_tencent_cvm",
	"public_tencent_clb",
	"public_huawei_ecs",
	"sangfor_sip",
	"qianxin_server",
	"jump_server",
	"weibu_onesec",
	"nsfocus_rsas",
}

func genSourceIndexMapKey(sourceId uint64, syncType int64, version string) string {
	return fmt.Sprintf("%d_%d_%s", sourceId, syncType, version)
}

func (st *DataSyncTask) getSourceIndexModel(sourceId uint64, syncType int64) string {
	version := ""
	if sourceId == data_source.FoeyeSourceId || sourceId == data_source.D01SourceId {
		version, _ = st.NodeConfig["version"].(string)
	}
	key := genSourceIndexMapKey(sourceId, syncType, version)
	if name, ok := SourceIndexNameMap[key]; ok {
		return name
	}
	source := st.Source

	if syncType == data_sync_task.SyncAsset {
		return data_import.NewDataImportTaskAssetsModel().IndexName(source.EnName)
	} else if syncType == data_sync_task.SyncThreat {
		return data_import.NewDataImportTaskThreatsModel().IndexName(source.EnName)
	} else if syncType == data_sync_task.SyncPeople {
		return data_import.NewDataImportTaskPeoplesModel().IndexName(source.EnName)
	}
	return ""
}

// UpdateSyncDataTotal 更新子任务总数
func (st *DataSyncTask) UpdateSyncDataTotal(syncType int64, total int64) error {
	//记录日志
	st.WriteLog(fmt.Sprintf("UpdateSyncDataTotal total:%d ", total))

	err := data_sync_child_task.NewDataSyncChildTaskModel().UpdateSyncDataTotal(st.ChildTasks[syncType].Id, int(total))
	if err != nil {
		return err
	}
	return nil
}

// UpdateSyncDataSuccessTotal 更新子任务成功总数
func (st *DataSyncTask) UpdateSyncDataSuccessTotal(syncType int64, num int) error {
	//记录日志
	st.WriteLog(fmt.Sprintf("UpdateSyncDataSuccessTotal num:%d ", num))

	err := data_sync_child_task.NewDataSyncChildTaskModel().UpdateSyncDataSuccessTotal(st.ChildTasks[syncType].Id, num)
	if err != nil {
		return err
	}
	return nil
}

// SetStatus 更新子任务和主任务的状态（用于更新任务为进行中，或者已完成任务的更新）
// @param syncType 同步类型 data_sync_task.SyncAsset//资产类型 data_sync_task.SyncThreat//漏洞类型 data_sync_task.SyncPeople//人员类型
// @param status 任务状态 data_sync_child_task.StatusDoing data_sync_child_task.StatusSuccess data_sync_child_task.StatusFail
// @param msg 任务失败错误消息
func (st *DataSyncTask) SetStatus(syncType int64, status int64, msg string) error {
	//st.mu.Lock()
	//defer st.mu.Unlock()

	//记录日志
	st.WriteLog(fmt.Sprintf("SetStatus status:%d msg:%s syncType:%d", status, msg, syncType))

	if status != data_sync_child_task.StatusFail {
		err := data_sync_child_task.NewDataSyncChildTaskModel().UpdateStatusById(st.ChildTasks[syncType].Id, int(status))
		return err
	}

	//更新主任务状态为失败
	err := data_sync_task.NewDataSyncTaskModel().UpdateFailById(st.Task.Id, msg)
	if err != nil {
		return err
	}

	//更新子任务状态为失败
	childTaskErr := data_sync_child_task.NewDataSyncChildTaskModel().UpdateFailById(st.ChildTasks[syncType].Id, msg)
	if childTaskErr != nil {
		return err
	}
	return nil
}
