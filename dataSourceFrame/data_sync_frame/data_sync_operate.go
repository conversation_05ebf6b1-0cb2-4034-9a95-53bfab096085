package data_sync_frame

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"fobrain/fobrain/logs"
	"fobrain/initialize/es"
	"fobrain/initialize/redis"
	"fobrain/models/elastic/assets"
	"fobrain/models/elastic/device"
	"fobrain/models/elastic/poc"
	"fobrain/models/elastic/staff"
	"fobrain/models/mysql/data_sync_child_task"
	"fobrain/models/mysql/data_sync_task"
	"fobrain/pkg/utils/handle_es_bulk_error"
	"strings"

	goRedis "github.com/go-redis/redis/v8"
	"github.com/google/uuid"
	"github.com/olivere/elastic/v7"
)

// BatchSaveToES 将数据保存至es
func (st *DataSyncTask) BatchSaveToES(indexName string, data []map[string]interface{}) error {
	st.mu.Lock()
	defer st.mu.Unlock()

	bulkRequest := es.GetEsClient().Bulk()
	size := len(data)
	for i := 0; i < size; i++ {
		id, ok := data[i]["id"].(string)
		if !ok {
			return fmt.Errorf("BatchSaveToES item id is not a string, indexName:%s, item:%+v", indexName, data[i])
		}
		docReq := elastic.NewBulkIndexRequest().Index(indexName).Id(id).Doc(data[i])
		bulkRequest = bulkRequest.Add(docReq)

		if (i+1)%10 == 0 || (i+1) == size {
			bulkDo, err := bulkRequest.Refresh("true").Do(context.Background())
			if err != nil || bulkDo.Errors {
				errString := handle_es_bulk_error.HandleBulkResp(indexName, err, bulkDo)
				st.WriteLog(errString)
				return errors.New(errString)
			}
		}
	}
	return nil
}

// BatchSaveData 追加数据及保存数据
func (st *DataSyncTask) BatchSaveData(sourceData, processData []map[string]interface{}, syncType int64, processIndexName string) error {
	//获取对应源名称
	sourceTaskIndexName := st.getSourceIndexModel(st.Task.SourceId, syncType)
	if sourceTaskIndexName == "" {
		return fmt.Errorf("BatchSaveData 数据源索引不存在,syncType:%d,processIndexName:%s", syncType, processIndexName)
	}

	var taskData = make([]map[string]interface{}, 0, len(sourceData))
	for _, sourceItem := range sourceData {
		id := strings.ReplaceAll(uuid.New().String(), "-", "")
		sourceItem["id"] = id

		var taskItem = make(map[string]interface{})
		for key, value := range sourceItem {
			taskItem[key] = value
		}

		taskItem = st.appendAttribute(syncType, taskItem)
		taskData = append(taskData, taskItem)
	}
	for _, processItem := range processData {
		id := strings.ReplaceAll(uuid.New().String(), "-", "")
		processItem["id"] = id
		processItem = st.appendAttribute(syncType, processItem)
		if st.GetNodeConfigByKey("person_field") != nil && st.GetNodeConfigByKey("person_field") != "" && syncType == data_sync_task.SyncAsset {
			processItem["person_field"] = st.GetNodeConfigByKey("person_field")
		}
	}

	if err := st.BatchSaveToES(sourceTaskIndexName, taskData); err != nil {
		return err
	}
	if err := st.BatchSaveToES(processIndexName, processData); err != nil {
		return err
	}
	return st.UpdateSyncDataSuccessTotal(syncType, len(processData))
}

func (st *DataSyncTask) BatchUpdateProcessDevice(processDeviceData []map[string]interface{}) error {
	for i := 0; i < len(processDeviceData); i++ {
		device := processDeviceData[i]
		device["source"] = st.Node.SourceId
		device["node"] = st.Node.Id
		device["area"] = st.Node.AreaId
		device["task_id"] = st.Task.Id
		device["child_task_id"] = st.ChildTasks[data_sync_task.SyncAsset].Id
	}
	return st.BatchSaveToES(device.NewProcessDeviceModel().IndexName(), processDeviceData)
}

func (st *DataSyncTask) BatchSaveRelations(total int64, sourceData, processData []map[string]interface{}) error {
	return nil
}

// BatchSaveAsset 批量保存资产数据
func (st *DataSyncTask) BatchSaveAsset(total int64, sourceData, processData, processDeviceData []map[string]interface{}) error {
	st.AssetOnce.Do(func() {
		err := st.UpdateSyncDataTotal(data_sync_task.SyncAsset, total)
		if err != nil {
			st.WriteLog(fmt.Sprintf("保存资产数据时更新任务总数失败,nodeId:%d,taskId:%d,childTaskId:%d,total:%d,err:%v",
				st.Node.Id, st.Task.Id, st.ChildTasks[data_sync_task.SyncAsset].Id, total, err))
			return
		}
	})
	var processValidData = make([]map[string]interface{}, 0, len(processData))
	for i := 0; i < len(processData); i++ {
		// 检测IP是否正确
		ip, ok := processData[i]["ip"].(string)
		if !ok || ip == "" {
			js, _ := json.Marshal(processData[i])
			st.WriteLog(fmt.Sprintf("node %d data format err, data: %s", st.Node.Id, string(js)))
			st.RecordFailedData(data_sync_task.SyncAsset, "ip", ip, "保存资产时ip校验未通过")
			continue
		}
		processValidData = append(processValidData, processData[i])
	}

	err := st.BatchSaveData(sourceData, processValidData, data_sync_task.SyncAsset, assets.NewProcessAssetsModel().IndexName())
	if err != nil {
		return err
	}
	if len(processDeviceData) > 0 {
		return st.BatchUpdateProcessDevice(processDeviceData)
	}
	return nil
}

// BatchSaveVul 批量保存漏洞数据
func (st *DataSyncTask) BatchSaveVul(total int64, sourceData, processData []map[string]interface{}) error {
	st.VulOnce.Do(func() {
		err := st.UpdateSyncDataTotal(data_sync_task.SyncThreat, total)
		if err != nil {
			st.WriteLog(fmt.Sprintf("保存漏洞数据时更新任务总数失败,nodeId:%d,taskId:%d,childTaskId:%d,total:%d,err:%v",
				st.Node.Id, st.Task.Id, st.ChildTasks[data_sync_task.SyncThreat].Id, total, err))
			return
		}
	})
	return st.BatchSaveData(sourceData, processData, data_sync_task.SyncThreat, poc.NewProcessPocModel().IndexName())
}

// BatchSaveStaff 批量保存人员数据
func (st *DataSyncTask) BatchSaveStaff(total int64, sourceData, processData []map[string]interface{}) error {
	st.StaffOnce.Do(func() {
		err := st.UpdateSyncDataTotal(data_sync_task.SyncPeople, total)
		if err != nil {
			st.WriteLog(fmt.Sprintf("保存人员数据时更新任务总数失败,nodeId:%d,taskId:%d,childTaskId:%d,total:%d,err:%v",
				st.Node.Id, st.Task.Id, st.ChildTasks[data_sync_task.SyncPeople].Id, total, err))
			return
		}
	})
	return st.BatchSaveData(sourceData, processData, data_sync_task.SyncPeople, staff.NewProcessStaffModel().IndexName())
}

func (st *DataSyncTask) GetNodeConfigByKey(key string) any {
	if v, ok := st.NodeConfig[key]; ok {
		return v
	}
	return nil
}

func (st *DataSyncTask) WriteLog(msg string) {
	logEntry := fmt.Sprintf("SyncTask WriteLog msg:%s sourceId:%d,nodeId:%d, taskId:%d",
		msg, st.Task.SourceId, st.Node.Id, st.Task.Id)
	logs.GetSyncLogger().Infof(logEntry)
}

func (st *DataSyncTask) GetRedisClient() *goRedis.Client {
	return redis.GetRedisClient()
}

func (st *DataSyncTask) RecordFailedData(syncType int64, dataType, dataContent, failedReason string) error {
	child, ok := st.ChildTasks[syncType]
	if !ok {
		return errors.New("未找到指定类型的child task")
	}
	err := data_sync_child_task.NewDataSyncChildTaskModel().UpdateSyncDataDropTotal(child.Id, 1)
	if err != nil {
		return err
	}
	r := data_sync_child_task.NewDataSyncChildTaskFailRecord()
	return r.Create(&data_sync_child_task.DataSyncChildTaskFailRecord{
		ChildTaskId:  child.Id,
		DataType:     dataType,
		DataContent:  dataContent,
		FailedReason: failedReason,
	})
}
