package data_sync_frame

import (
	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/initialize/es"
	"fobrain/initialize/mysql"
	"fobrain/models/mysql/data_sync_child_task"
	"fobrain/models/mysql/data_sync_task"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/olivere/elastic/v7"

	"github.com/stretchr/testify/assert"
)

func TestDataSyncTask_GetNodeConfigByKey(t *testing.T) {
	st := NewDataSyncTask()
	st.NodeConfig = map[string]any{
		"test": "testValue",
	}
	got := st.GetNodeConfigByKey("test")
	assert.Equal(t, got, "testValue")
}

func TestDataSyncTask_BatchSaveToES(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()
	mockServer.Register("/_bulk?refresh=true", &elastic.BulkIndexByScrollResponse{
		Updated: 1,
	})

	st := NewDataSyncTask()
	t.Run("test id error", func(t *testing.T) {
		data := []map[string]interface{}{
			{
				"id": int(1),
			},
		}
		err := st.BatchSaveToES("test-index", data)
		assert.NotNil(t, err)
	})
	t.Run("success", func(t *testing.T) {
		patch := gomonkey.ApplyMethodReturn(&es.SafeBulkService{}, "Do", &elastic.BulkResponse{
			Items: []map[string]*elastic.BulkResponseItem{
				{
					"test": &elastic.BulkResponseItem{
						Error: &elastic.ErrorDetails{Type: "err", Reason: ""},
					},
				},
			},
		}, nil)
		data := []map[string]interface{}{
			{
				"id": "gqeoprgjveopnbvoeib",
			},
		}
		err := st.BatchSaveToES("test-index", data)
		assert.Nil(t, err)
		patch.Reset()
	})

}

// func TestBatchSaveData(t *testing.T) {
// 	st := NewDataSyncTask()
// 	st.Node.AreaType = "指定"
// 	// 模拟数据
// 	sourceData := []map[string]interface{}{
// 		{"name": "source1", "ip": "127.0.0.1"},
// 		{"name": "source2", "ip": "*********"},
// 	}
// 	processData := []map[string]interface{}{
// 		{"name": "process1", "ip": "127.0.0.1"},
// 		{"name": "process2", "ip": "*********"},
// 	}

// 	patch := gomonkey.ApplyMethodReturn(st, "BatchSaveToES", nil)
// 	patch.ApplyMethodReturn(&data_sync_child_task.DataSyncChildTask{}, "UpdateSyncDataSuccessTotal", nil)
// 	patch.ApplyMethodReturn(&data_sync_child_task.DataSyncChildTask{}, "UpdateSyncDataDropTotal", nil)
// 	patch.ApplyMethodReturn(&data_source.Node{}, "GetNetworkType", "lan", nil)
// 	// 测试成功情况
// 	err := st.BatchSaveData(sourceData, processData, data_sync_task.SyncAsset, "process_index")
// 	assert.Nil(t, err)

// 	t.Log(sourceData)
// 	t.Log(processData)

// 	// 测试 BatchSaveToES 返回错误
// 	patch.ApplyMethodReturn(st, "BatchSaveToES", errors.New("es error"))
// 	err = st.BatchSaveData(sourceData, processData, data_sync_task.SyncAsset, "process_index")
// 	assert.NotNil(t, err)
// 	assert.Equal(t, "es error", err.Error())

// 	patch.Reset()
// }

func TestDataSyncTask_BatchUpdateProcessDevice(t *testing.T) {
	st := NewDataSyncTask()
	processData := []map[string]interface{}{
		{"name": "process1"},
		{"name": "process2"},
	}
	time.Sleep(time.Millisecond * 300)
	patch := gomonkey.ApplyMethodReturn(st, "BatchSaveToES", nil)
	err := st.BatchUpdateProcessDevice(processData)
	assert.Nil(t, err)
	patch.Reset()
}

func TestDataSyncTask_BatchSaveAsset(t *testing.T) {
	st := NewDataSyncTask()
	sourceData := []map[string]interface{}{
		{"name": "source1"},
		{"name": "source2"},
	}
	processData := []map[string]interface{}{
		{"name": "process1"},
		{"name": "process2"},
	}
	deviceData := []map[string]interface{}{
		{"name": "device1"},
		{"name": "device2"},
	}
	time.Sleep(time.Millisecond * 300)
	patch := gomonkey.ApplyMethodReturn(st, "BatchSaveData", nil)
	patch.ApplyMethodReturn(st, "BatchSaveToES", nil)
	patch.ApplyMethodReturn(&data_sync_child_task.DataSyncChildTask{}, "UpdateSyncDataTotal", nil)
	err := st.BatchSaveAsset(1, sourceData, processData, deviceData)
	assert.Nil(t, err)
	patch.Reset()

}

func TestDataSyncTask_BatchSaveVul(t *testing.T) {
	st := NewDataSyncTask()
	sourceData := []map[string]interface{}{
		{"name": "source1"},
		{"name": "source2"},
	}
	processData := []map[string]interface{}{
		{"name": "process1"},
		{"name": "process2"},
	}
	time.Sleep(time.Millisecond * 300)
	patch := gomonkey.ApplyMethodReturn(st, "BatchSaveData", nil)
	patch.ApplyMethodReturn(&data_sync_child_task.DataSyncChildTask{}, "UpdateSyncDataTotal", nil)
	err := st.BatchSaveVul(1, sourceData, processData)
	assert.Nil(t, err)
	patch.Reset()
}

func TestDataSyncTask_BatchSaveStaff(t *testing.T) {
	st := NewDataSyncTask()
	sourceData := []map[string]interface{}{
		{"name": "source1"},
		{"name": "source2"},
	}
	processData := []map[string]interface{}{
		{"name": "process1"},
		{"name": "process2"},
	}
	time.Sleep(time.Millisecond * 300)
	patch := gomonkey.ApplyMethodReturn(st, "BatchSaveData", nil)
	patch.ApplyMethodReturn(&data_sync_child_task.DataSyncChildTask{}, "UpdateSyncDataTotal", nil)
	err := st.BatchSaveStaff(1, sourceData, processData)
	assert.Nil(t, err)
	patch.Reset()
}

func TestDataSyncTask_RecordFailedData(t *testing.T) {
	st := NewDataSyncTask()
	st.ChildTasks[data_sync_task.SyncAsset] = &data_sync_child_task.DataSyncChildTask{
		BaseModel: mysql.BaseModel{
			Id: 111,
		},
		SourceId: 2,
	}
	mockDb := testcommon.InitSqlMock()
	mockDb.ExpectQuery("SELECT * FROM `data_sync_child_tasks` WHERE `id` = ? ORDER BY `data_sync_child_tasks`.`id` LIMIT 1").
		WithArgs(sqlmock.AnyArg()).
		WillReturnRows(mockDb.NewRows([]string{"sync_data_drop_total"}).
			AddRow(1))
	mockDb.ExpectBegin()
	mockDb.ExpectExec("UPDATE `data_sync_child_tasks` SET `sync_data_drop_total`=?,`updated_at`=? WHERE `id` = ? AND `id` = ? ORDER BY `data_sync_child_tasks`.`id` LIMIT 1").
		WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg()).
		WillReturnResult(sqlmock.NewResult(0, 1))
	mockDb.Mock.ExpectCommit()

	mockDb.ExpectBegin()
	mockDb.ExpectExec("INSERT INTO `data_sync_child_task_fail_record` (`created_at`,`updated_at`,`child_task_id`,`data_type`,`data_content`,`failed_reason`) VALUES (?,?,?,?,?,?)").
		WillReturnResult(sqlmock.NewResult(1, 1))
	mockDb.ExpectCommit()
	err := st.RecordFailedData(data_sync_task.SyncAsset, "ip", "***********", "丢弃")
	assert.Nil(t, err)
}
