# This is an example .goreleaser.yml template with some sensible defaults.
# Make sure to check the documentation at https://goreleaser.com
dist: bin
builds:
  - id: fobrain
    binary: "fobrain_{{ .Os }}_{{ .Arch }}_{{.Version}}"
    goos:
      - linux
    ldflags:
      - "-w -s -X main.Version={{.Branch}}-{{.ShortCommit}} -X main.date={{.Timestamp}}"
    goarch:
      - amd64
      - arm64
    main: ./fobrain  # 指定包含 main 函数的包路径
    no_unique_dist_dir: true
  - id: cmd
    binary: "cmd_{{ .Os }}_{{ .Arch }}_{{.Version}}"
    goos:
      - linux
    ldflags:
      - "-w -s -X main.version={{.Version}} -X main.date={{.Timestamp}}"
    goarch:
      - amd64
      - arm64
    main: ./cmd  # 指定包含 main 函数的包路径
    no_unique_dist_dir: true
  - id: mergeService
    binary: "mergeService_{{.Os }}_{{.Arch }}_{{.Version}}"
    goos:
      - linux
    ldflags:
      - "-w -s"
    goarch:
      - amd64
      - arm64
    main: ./mergeService  # 指定包含 main 函数的包路径
    no_unique_dist_dir: true
  - id: adapter
    binary: "adapter_{{.Os }}_{{.Arch }}"
    goos:
      - linux
    ldflags:
      - "-w -s"
    goarch:
      - amd64
      - arm64
    main: ./adapter  # 指定包含 main 函数的包路径
    no_unique_dist_dir: true
nfpms:
  - id: fobrain
    package_name: fobrain
    license: Apache2.0
    bindir: /usr/sbin
    vendor: 北京华顺信安信息技术有限公司
    maintainer: FOBrainGroup <<EMAIL>>
    homepage: https://www.baimaohui.net
    version_metadata: git
    description: "              FOBrain - web server {{ .Version }}"
    builds:
      - fobrain
      - cmd
    file_name_template: "fobrain{{ .Os }}_{{ .Arch }}_{{ .Version }}"
    contents:
      - src: "install/amd64/build/fobrain/fobrain.service"
        dst: "/usr/lib/systemd/system/fobrain.service"
      - src: "/usr/sbin/fobrain_{{ .Os }}_{{ .Arch }}_{{ .Version }}"
        dst: "/usr/sbin/fobrain"
        type: "symlink"
        file_info:
          mode: 0755
      - src: "install/amd64/build/fobrain/fobrain.json"
        dst: /etc/fobrain/config.json
        type: config
    rpm:
      group: Unspecified
      summary: FOBrain - web server
      compression: gzip
    formats:
      - rpm
  - id: mergeService
    package_name: mergeService
    license: Apache2.0
    bindir: /usr/sbin
    vendor: 北京华顺信安信息技术有限公司
    maintainer: FOBrainGroup <EMAIL>
    homepage: URL_ADDRESS
    version_metadata: git
    description: "              FOBrain - web server {{ .Version }}"
    builds:
      - mergeService
    file_name_template: "mergeService_{{ .Os }}_{{ .Arch }}_{{ .Version }}"
    contents:
      - src: "install/amd64/build/fobrain/fobrain.service"
        dst: "/usr/lib/systemd/system/fobrain.service"
      - src: "/usr/sbin/mergeService_{{ .Os }}_{{ .Arch }}_{{ .Version }}"
        dst: "/usr/sbin/mergeService"
        type: "symlink"
        file_info:
          mode: 0755
      - src: "install/amd64/build/fobrain/fobrain.json"
        dst: /etc/fobrain/config.json
        type: config
    rpm:
      group: Unspecified
      summary: FOBrain - web server
      compression: gzip
    formats:
      - rpm
archives:
  - id: rpm
    builds:
      - cmd
      - fobrain
      - mergeService
    files:
      - ./cmd_*
      - ./fobrain_*
      - ./mergeService_*
    allow_different_binary_count: true
checksum:
  name_template: 'checksums.txt'
snapshot:
  name_template: "{{ incpatch .Version }}"
changelog:
  sort: asc
  filters:
    exclude:
      - '^docs:'
      - '^test:'
release:
  prerelease: auto
  header: |
    ## Some title ({{ .Date }})

    Welcome to this new release!

  # Footer template for the release body.
  # Defaults to empty.
  footer: |
    ## Thanks!

    Those were the changes on {{ .Tag }}!
gitlab_urls:
  api: https://git.baimaohui.net/api/v4/
  download: https://git.baimaohui.net
