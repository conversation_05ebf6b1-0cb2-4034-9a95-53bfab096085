package event

import (
	"fmt"
	logs "fobrain/mergeService/utils/log"
	"fobrain/models/elastic/assets"
	"fobrain/models/elastic/device"
	"fobrain/models/elastic/poc"
	"fobrain/models/elastic/staff"
	"fobrain/models/mysql/strategy"
	"reflect"
	"sync"
	"time"
)

const (
	Event_Asset_MergeTask_Start  = "event_asset_merge_task_start"
	Event_Asset_MergeTask_End    = "event_asset_merge_task_end"
	Event_Asset_MergeData        = "event_asset_merge_data"              // 资产融合单条数据，字段计算结束，人工校准未开始
	Event_Asset_MergeData_End    = "event_asset_merge_data_end"          // 资产融合单条数据结束，人工校准已结束
	EvtAssetMergeDataBeforeWrite = "event_asset_merge_data_before_write" // 资产融合分批写入asset和asset_record前事件，可以最后对数据进行批量加工

	Event_Device_MergeTask_Start  = "event_device_merge_task_start"
	Event_Device_MergeTask_End    = "event_device_merge_task_end"
	Event_Device_MergeData        = "event_device_merge_data"     // 设备融合单条数据
	Event_Device_MergeData_End    = "event_device_merge_data_end" // 设备融合单条数据结束
	EvtDeviceMergeDataBeforeWrite = "event_device_merge_data_before_write"

	Event_Person_MergeTask_Start = "event_person_merge_task_start"
	Event_Person_MergeTask_End   = "event_person_merge_task_end"
	Event_Person_MergeData       = "event_person_merge_data"     // 人员融合单条数据，字段计算结束，人工校准未开始
	Event_Person_MergeData_End   = "event_person_merge_data_end" // 人员融合单条数据结束，人工校准已结束

	Event_Vuln_MergeTask_Start = "event_vuln_merge_task_start"
	Event_Vuln_MergeTask_End   = "event_vuln_merge_task_end"
	Event_Vuln_MergeData       = "event_vuln_merge_data"       // 漏洞融合单条数据
	Event_Vuln_ConvertData     = "event_vuln_convert_data"     // 漏洞单条数据转换,poc漏洞 或 cve&cnvd&cnnvd均为空 的漏洞
	Event_Vuln_MergeData_End   = "event_vuln_merge_data_end"   // 漏洞融合单条数据结束
	Event_Vuln_ConvertData_End = "event_vuln_convert_data_end" // 漏洞单条数据转换结束

	Event_Manual_Calibration    = "event_manual_calibration"           // 人工校准
	Event_Vuln_UpdateData       = "event_vuln_update_data"             // 漏洞更新事件
	EvtVulnMergeDataBeforeWrite = "event_vuln_merge_data_before_write" // 漏洞批量写入前事件
)

type (
	// AssetMergeTaskStartHandler 资产融合任务开始
	// @param nodeTaskInfo map[uint64]uint64 每个节点的最新taskid
	// @param sourceId uint64 触发数据源id
	// @param nodeId uint64 触发数据源节点id
	// @param taskType string 任务类型
	// @param taskId string 任务id
	// @param assetTaskId string 资产融合任务id
	AssetMergeTaskStartHandler func(nodeTaskInfo map[uint64]uint64, sourceId, nodeId uint64, taskType, taskId, assetTaskId string) error
	// AssetMergeTaskEndHandler 资产融合任务结束
	// @param taskType string 任务类型
	// @param taskId string 任务id
	AssetMergeTaskEndHandler func(taskType, taskId string) error
	// AssetMergeDataHandler 资产融合单条数据
	// @param data *assets.Assets 融合后的资产数据
	// @param strategies []*strategy.Strategy 策略
	// @param sourceData []*assets.ProcessAssets 原始资产数据
	// @param latestData map[string]*assets.ProcessAssets 每个源节点的最新数据
	AssetMergeDataHandler func(data *assets.Assets, strategies []*strategy.Strategy, sourceData []*assets.ProcessAssets, latestData map[string]*assets.ProcessAssets) error
	// AssetMergeDataEndHandler 单条资产融合结束，可以在此处更新人工校准数据
	// @param data *assets.Assets 本次融合后的资产数据
	// @param existData *assets.Assets 已有的资产数据
	AssetMergeDataEndHandler func(data *assets.Assets, existData *assets.Assets) error

	// DeviceMergeTaskStartHandler 设备融合任务开始
	// @param nodeTaskInfo map[uint64]uint64 每个节点的最新taskid
	// @param sourceId uint64 触发数据源id
	// @param nodeId uint64 触发数据源节点id
	// @param taskType string 任务类型
	// @param taskId string 任务id
	// @param deviceTaskId string 设备融合任务id
	DeviceMergeTaskStartHandler func(nodeTaskInfo map[uint64]uint64, sourceId, nodeId uint64, taskType, taskId, deviceTaskId string) error
	// DeviceMergeTaskEndHandler 设备融合任务结束
	// @param taskType string 任务类型
	// @param taskId string 任务id
	DeviceMergeTaskEndHandler func(taskType, taskId string) error
	// DeviceMergeDataHandler 设备融合单条数据
	// @param data *device.Device 融合后的设备数据
	// @param strategies []*strategy.Strategy 策略
	// @param sourceData []*device.ProcessDevice 原始设备数据
	// @param latestData map[string]*device.ProcessDevice 每个源节点的最新数据
	DeviceMergeDataHandler func(data *device.Device, strategies []*strategy.Strategy, sourceData []*device.ProcessDevice, latestData map[string]*device.ProcessDevice) error
	// DeviceMergeDataEndHandler 单条设备融合结束，可以在此处更新人工校准数据
	// @param data *device.Device 本次融合后的设备数据
	// @param existData *device.Device 已有的设备数据
	DeviceMergeDataEndHandler func(data *device.Device, existData *device.Device) error

	// PersonMergeTaskStartHandler 人员融合任务开始
	// @param nodeTaskInfo map[uint64]uint64 每个节点的最新taskid
	// @param sourceId uint64 触发数据源id
	// @param nodeId uint64 触发数据源节点id
	// @param taskType string 任务类型
	// @param taskId string 任务id
	// @param personTaskId string 人员融合任务id
	PersonMergeTaskStartHandler func(nodeTaskInfo map[uint64]uint64, sourceId, nodeId uint64, taskType, taskId, personTaskId string) error
	// 人员融合任务结束
	// @param taskType string 任务类型
	// @param taskId string 任务id
	PersonMergeTaskEndHandler func(taskType, taskId string) error
	// 人员融合单条数据
	// @param data *staff.Staff 融合后的人员数据
	// @param strategies []*strategy.Strategy 策略
	// @param sourceData []*staff.ProcessStaff 原始人员数据
	// @param latestData map[string]*staff.ProcessStaff 每个源节点的最新数据
	PersonMergeDataHandler func(data *staff.Staff, strategies []*strategy.Strategy, sourceData []*staff.ProcessStaff, latestData map[string]*staff.ProcessStaff) error
	// 单条人员融合结束，可以在此处更新人工校准数据
	// @param data *staff.Staff 本次融合后的人员数据
	// @param existData *staff.Staff 已有的人员数据
	PersonMergeDataEndHandler func(data *staff.Staff, existData *staff.Staff) error

	// 漏洞融合任务开始
	// @param nodeTaskInfo map[uint64]uint64 每个节点的最新taskid
	// @param sourceId uint64 触发数据源id
	// @param nodeId uint64 触发数据源节点id
	// @param taskType string 任务类型
	// @param taskId string 任务id
	// @param vulTaskId string 漏洞融合任务id
	VulnMergeTaskStartHandler func(nodeTaskInfo map[uint64]uint64, sourceId, nodeId uint64, taskType, taskId, vulTaskId string) error
	// 漏洞融合任务结束
	// @param taskType string 任务类型
	// @param taskId string 任务id
	VulnMergeTaskEndHandler func(taskType, taskId string) error
	// 漏洞融合单条数据
	// @param data *poc.Poc 融合后的漏洞数据
	// @param strategies []*strategy.Strategy 策略
	// @param sourceData []*poc.ProcessPoc 原始漏洞数据
	// @param latestData map[string]*poc.ProcessPoc 每个源节点的最新数据
	VulnMergeDataHandler func(data *poc.Poc, strategies []*strategy.Strategy, sourceData []*poc.ProcessPoc, latestData map[string]*poc.ProcessPoc) error
	// 漏洞单条数据转换,poc漏洞 或 cve&cnvd&cnnvd均为空 的漏洞
	// @param data *poc.Poc 转换后的漏洞数据
	// @param sourceData *poc.ProcessPoc 原始漏洞数据
	VulnConvertDataHandler func(data *poc.Poc, sourceData *poc.ProcessPoc) error
	// 单条漏洞融合结束，可以在此处更新人工校准数据
	// @param data *poc.Poc 本次融合后的漏洞数据
	// @param existData *poc.Poc 已有的漏洞数据
	VulnMergeDataEndHandler func(data *poc.Poc, existData *poc.Poc) error
	// 单条漏洞转换结束
	// @param data *poc.Poc 转换后的漏洞数据
	// @param sourceData *poc.ProcessPoc 原始漏洞数据
	VulnConvertDataEndHandler func(data *poc.Poc, sourceData *poc.ProcessPoc) error

	// 人工校准
	// @param businessType string 业务类型,可能的值:asset,device,vuln,person
	// @param ids []string 要修改的数据id
	// @param values map[string]string 要修改的数据值
	// @param eventData map[string]interface{} 处理结果对应的数据
	// @param eventSource map[string]interface{} 处理结果对应的source字段数据
	ManualCalibrationHandler func(businessType string, ids []string, values map[string]interface{}, eventData map[string]interface{}, eventSource map[string]interface{}) error
	// 漏洞更新事件
	// @param data *poc.Poc 更新后的漏洞数据
	VulnUpdateDataHandler func(data *poc.Poc) error
	// 漏洞融合批量数据写入前事件-权限字段处理
	// @param data map[string]interface{} 处理结果对应的数据
	VulnMergeDataBeforeWriteHandler func(list []*poc.PocRecord) error
)

type HandlerFunc interface{}

type EventBus struct {
	handlers map[string][]*EventHandler
	lock     sync.Mutex
}

type EventHandler struct {
	Handler     HandlerFunc
	Description string
}

var (
	instance *EventBus
	once     sync.Once
)

// NewEventBus 创建并返回一个单例的EventBus实例
func NewEventBus() *EventBus {
	once.Do(func() {
		instance = &EventBus{
			handlers: make(map[string][]*EventHandler),
		}
	})
	return instance
}

// RegisterEventHandler 注册事件处理器
func (eb *EventBus) RegisterEventHandler(event string, handler HandlerFunc, description string) error {
	if handler == nil {
		return fmt.Errorf("handler is nil")
	}
	eb.lock.Lock()
	defer eb.lock.Unlock()

	// 检查处理器的签名是否匹配
	handlerType := reflect.TypeOf(handler)
	switch event {
	case Event_Asset_MergeTask_Start, Event_Device_MergeTask_Start, Event_Person_MergeTask_Start, Event_Vuln_MergeTask_Start:
		if handlerType.NumIn() != 6 || handlerType.NumOut() != 1 || handlerType.Out(0) != reflect.TypeOf((*error)(nil)).Elem() {
			return fmt.Errorf("handler for event %s must have signature func(map[uint64][]uint64, string, string, string) error", event)
		}
	case Event_Asset_MergeTask_End, Event_Device_MergeTask_End, Event_Person_MergeTask_End, Event_Vuln_MergeTask_End:
		if handlerType.NumIn() != 2 || handlerType.NumOut() != 1 || handlerType.Out(0) != reflect.TypeOf((*error)(nil)).Elem() {
			return fmt.Errorf("handler for event %s must have signature func(string, string) error", event)
		}
	case Event_Asset_MergeData:
		if handlerType.NumIn() != 4 || handlerType.NumOut() != 1 || handlerType.Out(0) != reflect.TypeOf((*error)(nil)).Elem() {
			return fmt.Errorf("handler for event %s must have signature func(*esmodel_asset.Assets, []*dbmodel.Strategy, []*esmodel_asset.ProcessAssets, map[string]*esmodel_asset.ProcessAssets) error", event)
		}
	case Event_Asset_MergeData_End:
		if handlerType.NumIn() != 2 || handlerType.NumOut() != 1 || handlerType.Out(0) != reflect.TypeOf((*error)(nil)).Elem() {
			return fmt.Errorf("handler for event %s must have signature func(*esmodel_asset.Assets, *esmodel_asset.Assets) error", event)
		}
	case Event_Device_MergeData:
		if handlerType.NumIn() != 4 || handlerType.NumOut() != 1 || handlerType.Out(0) != reflect.TypeOf((*error)(nil)).Elem() {
			return fmt.Errorf("handler for event %s must have signature func(*esmodel_device.Device, []*dbmodel.Strategy, []*esmodel_device.ProcessDevice, map[string]*esmodel_device.ProcessDevice) error", event)
		}
	case Event_Device_MergeData_End:
		if handlerType.NumIn() != 2 || handlerType.NumOut() != 1 || handlerType.Out(0) != reflect.TypeOf((*error)(nil)).Elem() {
			return fmt.Errorf("handler for event %s must have signature func(*esmodel_device.Device, *esmodel_device.Device) error", event)
		}
	case Event_Person_MergeData:
		if handlerType.NumIn() != 4 || handlerType.NumOut() != 1 || handlerType.Out(0) != reflect.TypeOf((*error)(nil)).Elem() {
			return fmt.Errorf("handler for event %s must have signature func(*esmodel_staff.Staff, []*dbmodel.Strategy, []*esmodel_staff.ProcessStaff, map[string]*esmodel_staff.ProcessStaff) error", event)
		}
	case Event_Person_MergeData_End:
		if handlerType.NumIn() != 2 || handlerType.NumOut() != 1 || handlerType.Out(0) != reflect.TypeOf((*error)(nil)).Elem() {
			return fmt.Errorf("handler for event %s must have signature func(*esmodel_staff.Staff, *esmodel_staff.Staff) error", event)
		}
	case Event_Manual_Calibration:
		if handlerType.NumIn() != 5 || handlerType.NumOut() != 1 || handlerType.Out(0) != reflect.TypeOf((*error)(nil)).Elem() {
			return fmt.Errorf("handler for event %s must have signature func(string, []string, map[string]interface{}, map[string]interface{}, map[string]interface{}) error", event)
		}
	case Event_Vuln_MergeData:
		if handlerType.NumIn() != 4 || handlerType.NumOut() != 1 || handlerType.Out(0) != reflect.TypeOf((*error)(nil)).Elem() {
			return fmt.Errorf("handler for event %s must have signature func(*esmodel_poc.Poc, []*dbmodel.Strategy, []*esmodel_poc.ProcessPoc, map[string]*esmodel_poc.ProcessPoc) error", event)
		}
	case Event_Vuln_ConvertData:
		if handlerType.NumIn() != 2 || handlerType.NumOut() != 1 || handlerType.Out(0) != reflect.TypeOf((*error)(nil)).Elem() {
			return fmt.Errorf("handler for event %s must have signature func(*esmodel_poc.Poc, *esmodel_poc.ProcessPoc) error", event)
		}
	case Event_Vuln_MergeData_End, Event_Vuln_ConvertData_End:
		if handlerType.NumIn() != 2 || handlerType.NumOut() != 1 || handlerType.Out(0) != reflect.TypeOf((*error)(nil)).Elem() {
			return fmt.Errorf("handler for event %s must have signature func(*esmodel_poc.Poc, *esmodel_poc.Poc) error", event)
		}
	case Event_Vuln_UpdateData:
		if handlerType.NumIn() != 1 || handlerType.NumOut() != 1 || handlerType.Out(0) != reflect.TypeOf((*error)(nil)).Elem() {
			return fmt.Errorf("handler for event %s must have signature func(*esmodel_poc.Poc) error", event)
		}
	case EvtAssetMergeDataBeforeWrite:
		if handlerType.NumIn() != 2 || handlerType.NumOut() != 1 || handlerType.Out(0) != reflect.TypeOf((*error)(nil)).Elem() {
			return fmt.Errorf("handler for event %s must have signature func([]*esmodel.AssetRecord, map[string]struct{}) error", event)
		}
	case EvtDeviceMergeDataBeforeWrite:
		if handlerType.NumIn() != 1 || handlerType.NumOut() != 1 || handlerType.Out(0) != reflect.TypeOf((*error)(nil)).Elem() {
			return fmt.Errorf("handler for event %s must have signature func([]*device.DeviceRecord) error", event)
		}
	case EvtVulnMergeDataBeforeWrite:
		if handlerType.NumIn() != 1 || handlerType.NumOut() != 1 || handlerType.Out(0) != reflect.TypeOf((*error)(nil)).Elem() {
			return fmt.Errorf("handler for event %s must have signature func([]*esmodel_poc.PocRecord) error", event)
		}
	default:
		return fmt.Errorf("unknown event type: %s", event)
	}

	eb.handlers[event] = append(eb.handlers[event], &EventHandler{Handler: handler, Description: description})
	logs.GetLogger("asset").Infof("注册事件处理器成功,事件类型: %s, 处理器: %s", event, description)
	return nil
}

// UnregisterEventHandler 注销事件处理器
func (eb *EventBus) UnregisterEventHandler(event string, handler HandlerFunc) {
	eb.lock.Lock()
	defer eb.lock.Unlock()

	if handlers, ok := eb.handlers[event]; ok {
		for i, h := range handlers {
			if reflect.ValueOf(h.Handler).Pointer() == reflect.ValueOf(handler).Pointer() {
				eb.handlers[event] = append(handlers[:i], handlers[i+1:]...)
				logs.GetLogger("asset").Infof("注销事件处理器(%s)成功,事件类型: %s, 处理器: %v", h.Description, event, handler)
				break
			}
		}
	}
}

// Emit 触发事件
func (eb *EventBus) Emit(event string, params ...interface{}) error {
	logger := logs.GetLogger("asset")
	t := time.Now().UnixMilli()
	logger.Infof("开始执行事件%s,执行批次: %d", event, t)
	wg := sync.WaitGroup{}
	if handlers, ok := eb.handlers[event]; ok {
		for _, handler := range handlers {
			handlerValue := reflect.ValueOf(handler.Handler)
			// 构造反射参数
			args := make([]reflect.Value, len(params))
			for i, param := range params {
				args[i] = reflect.ValueOf(param)
			}
			logger.Debugf("事件%s,执行处理器(%s)开始", event, handler.Description)
			tt := time.Now().UnixMilli()

			// 检查函数签名是否匹配
			if handlerValue.Type().NumIn() != len(params) {
				return fmt.Errorf("事件%s的处理器(%s)参数数量不匹配，期望%d，实际传入%d", event, handler.Description, handlerValue.Type().NumIn(), len(params))
			}

			// 使用反射检查参数类型是否匹配
			for i := 0; i < len(params); i++ {
				if handlerValue.Type().In(i) != reflect.TypeOf(params[i]) {
					return fmt.Errorf("事件%s的处理器(%s)参数类型(index: %d, name: %+v)不匹配，期望%+v，实际传入%+v", event, handler.Description, i, params[i], reflect.TypeOf(params[i]), handlerValue.Type().In(i))
				}
			}

			// 检查返回值
			if handlerValue.Type().NumOut() != 1 || handlerValue.Type().Out(0) != reflect.TypeOf((*error)(nil)).Elem() {
				return fmt.Errorf("事件%s的处理器(%s)必须返回一个error", event, handler.Description)
			}

			// 调用处理器
			wg.Add(1)
			hd := handler
			go func(h HandlerFunc, tt int64) {
				defer func() {
					if r := recover(); r != nil {
						logger.Errorf("事件%s的处理器(%s)执行失败,执行批次: %d,err: %v", event, hd.Description, t, r)
					}
					wg.Done()
				}()
				// 调用处理器，并获取返回值
				rets := handlerValue.Call(args)
				if len(rets) > 0 && !rets[0].IsNil() {
					logger.Errorf("事件%s的处理器(%s)执行失败,执行批次: %d,err: %v", event, hd.Description, t, rets[0].Interface())
				} else {
					logger.Infof("事件%s的处理器(%s)执行成功,执行批次:%d,耗时: %d ms", event, hd.Description, t, time.Now().UnixMilli()-tt)
				}
			}(hd, tt)
		}
	}
	wg.Wait()
	logger.Infof("事件%s的所有处理器执行完毕,执行批次: %d", event, t)
	return nil
}
