package event

import (
	"errors"
	"fobrain/models/elastic/assets"
	"fobrain/models/elastic/device"
	"fobrain/models/elastic/poc"
	"fobrain/models/elastic/staff"
	"fobrain/models/mysql/strategy"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestEventBus(t *testing.T) {
	eb := NewEventBus()
	assert.NotNil(t, eb)
}

func TestRegisterEventHandler(t *testing.T) {
	eb := NewEventBus()

	t.Run("事件不存在", func(t *testing.T) {
		err := eb.RegisterEventHandler("test", func() {}, "测试事件")
		assert.Error(t, err)
	})

	// t.Run("func签名错误", func(t *testing.T) {
	// 	err := eb.RegisterEventHandler(Event_Asset_MergeTask_Start, func(a int) {})
	// 	assert.Error(t, err)
	// })

	t.Run("func为空", func(t *testing.T) {
		err := eb.RegisterEventHandler(Event_Asset_MergeTask_Start, nil, "测试事件")
		assert.Error(t, err)
	})

	t.Run("注册资产融合开始事件", func(t *testing.T) {
		var handler AssetMergeTaskStartHandler
		// 类型断言
		handler = func(nodeTaskInfo map[uint64]uint64, sourceId, nodeId uint64, taskType, taskId, assetTaskId string) error {
			return nil
		}
		err := eb.RegisterEventHandler(Event_Asset_MergeTask_Start, handler, "测试事件")
		assert.Nil(t, err)

		// 获取事件处理器
		handlers := eb.handlers[Event_Asset_MergeTask_Start]
		assert.Equal(t, 1, len(handlers))
	})

	t.Run("注册资产融合结束事件", func(t *testing.T) {
		var handler AssetMergeTaskEndHandler
		handler = func(taskType, taskId string) error {
			return nil
		}
		err := eb.RegisterEventHandler(Event_Asset_MergeTask_End, handler, "测试事件")
		assert.Nil(t, err)

		handlers := eb.handlers[Event_Asset_MergeTask_End]
		assert.Equal(t, 1, len(handlers))
	})

	t.Run("注册单条资产融合事件", func(t *testing.T) {
		var handler AssetMergeDataHandler
		handler = func(data *assets.Assets, strategies []*strategy.Strategy, sourceData []*assets.ProcessAssets, latestData map[string]*assets.ProcessAssets) error {
			return nil
		}
		err := eb.RegisterEventHandler(Event_Asset_MergeData, handler, "测试事件")
		assert.Nil(t, err)

		handlers := eb.handlers[Event_Asset_MergeData]
		assert.Equal(t, 1, len(handlers))
	})

	t.Run("注册单条资产融合结束事件", func(t *testing.T) {
		var handler AssetMergeDataEndHandler
		handler = func(data *assets.Assets, existData *assets.Assets) error {
			return nil
		}
		err := eb.RegisterEventHandler(Event_Asset_MergeData_End, handler, "测试事件")
		assert.Nil(t, err)

		handlers := eb.handlers[Event_Asset_MergeData_End]
		assert.Equal(t, 1, len(handlers))
	})

	t.Run("注册设备融合任务开始事件", func(t *testing.T) {
		var handler DeviceMergeTaskStartHandler
		handler = func(nodeTaskInfo map[uint64]uint64, sourceId, nodeId uint64, taskType, taskId, deviceTaskId string) error {
			return nil
		}
		err := eb.RegisterEventHandler(Event_Device_MergeTask_Start, handler, "测试事件")
		assert.Nil(t, err)

		handlers := eb.handlers[Event_Device_MergeTask_Start]
		assert.Equal(t, 1, len(handlers))
	})

	t.Run("注册设备融合任务结束事件", func(t *testing.T) {
		var handler DeviceMergeTaskEndHandler
		handler = func(taskType, taskId string) error {
			return nil
		}
		err := eb.RegisterEventHandler(Event_Device_MergeTask_End, handler, "测试事件")
		assert.Nil(t, err)

		handlers := eb.handlers[Event_Device_MergeTask_End]
		assert.Equal(t, 1, len(handlers))
	})

	t.Run("注册单条设备融合事件", func(t *testing.T) {
		var handler DeviceMergeDataHandler
		handler = func(data *device.Device, strategies []*strategy.Strategy, sourceData []*device.ProcessDevice, latestData map[string]*device.ProcessDevice) error {
			return nil
		}
		err := eb.RegisterEventHandler(Event_Device_MergeData, handler, "测试事件")
		assert.Nil(t, err)

		handlers := eb.handlers[Event_Device_MergeData]
		assert.Equal(t, 1, len(handlers))
	})

	t.Run("注册单条设备融合结束事件", func(t *testing.T) {
		var handler DeviceMergeDataEndHandler
		handler = func(data *device.Device, existData *device.Device) error {
			return nil
		}
		err := eb.RegisterEventHandler(Event_Device_MergeData_End, handler, "测试事件")
		assert.Nil(t, err)

		handlers := eb.handlers[Event_Device_MergeData_End]
		assert.Equal(t, 1, len(handlers))
	})

	t.Run("注册人员融合任务开始事件", func(t *testing.T) {
		var handler PersonMergeTaskStartHandler
		handler = func(nodeTaskInfo map[uint64]uint64, sourceId, nodeId uint64, taskType, taskId, personTaskId string) error {
			return nil
		}
		err := eb.RegisterEventHandler(Event_Person_MergeTask_Start, handler, "测试事件")
		assert.Nil(t, err)

		handlers := eb.handlers[Event_Person_MergeTask_Start]
		assert.Equal(t, 1, len(handlers))
	})

	t.Run("注册人员融合任务结束事件", func(t *testing.T) {
		var handler PersonMergeTaskEndHandler
		handler = func(taskType, taskId string) error {
			return nil
		}
		err := eb.RegisterEventHandler(Event_Person_MergeTask_End, handler, "测试事件")
		assert.Nil(t, err)

		handlers := eb.handlers[Event_Person_MergeTask_End]
		assert.Equal(t, 1, len(handlers))
	})

	t.Run("注册单条人员融合事件", func(t *testing.T) {
		var handler PersonMergeDataHandler
		handler = func(data *staff.Staff, strategies []*strategy.Strategy, sourceData []*staff.ProcessStaff, latestData map[string]*staff.ProcessStaff) error {
			return nil
		}
		err := eb.RegisterEventHandler(Event_Person_MergeData, handler, "测试事件")
		assert.Nil(t, err)

		handlers := eb.handlers[Event_Person_MergeData]
		assert.Equal(t, 1, len(handlers))
	})

	t.Run("注册单条人员融合结束事件", func(t *testing.T) {
		var handler PersonMergeDataEndHandler
		handler = func(data *staff.Staff, existData *staff.Staff) error {
			return nil
		}
		err := eb.RegisterEventHandler(Event_Person_MergeData_End, handler, "测试事件")
		assert.Nil(t, err)

		handlers := eb.handlers[Event_Person_MergeData_End]
		assert.Equal(t, 1, len(handlers))
	})

	t.Run("注册漏洞融合任务开始事件", func(t *testing.T) {
		var handler VulnMergeTaskStartHandler
		handler = func(nodeTaskInfo map[uint64]uint64, sourceId, nodeId uint64, taskType, taskId, vulnTaskId string) error {
			return nil
		}
		err := eb.RegisterEventHandler(Event_Vuln_MergeTask_Start, handler, "测试事件")
		assert.Nil(t, err)

		handlers := eb.handlers[Event_Vuln_MergeTask_Start]
		assert.Equal(t, 1, len(handlers))
	})

	t.Run("注册漏洞融合任务结束事件", func(t *testing.T) {
		var handler VulnMergeTaskEndHandler
		handler = func(taskType, taskId string) error {
			return nil
		}
		err := eb.RegisterEventHandler(Event_Vuln_MergeTask_End, handler, "测试事件")
		assert.Nil(t, err)

		handlers := eb.handlers[Event_Vuln_MergeTask_End]
		assert.Equal(t, 1, len(handlers))
	})

	t.Run("注册单条漏洞融合事件", func(t *testing.T) {
		var handler VulnMergeDataHandler
		handler = func(data *poc.Poc, strategies []*strategy.Strategy, sourceData []*poc.ProcessPoc, latestData map[string]*poc.ProcessPoc) error {
			return nil
		}
		err := eb.RegisterEventHandler(Event_Vuln_MergeData, handler, "测试事件")
		assert.Nil(t, err)

		handlers := eb.handlers[Event_Vuln_MergeData]
		assert.Equal(t, 1, len(handlers))
	})

	t.Run("注册漏洞转换事件", func(t *testing.T) {
		var handler VulnConvertDataHandler
		handler = func(data *poc.Poc, sourceData *poc.ProcessPoc) error {
			return nil
		}
		err := eb.RegisterEventHandler(Event_Vuln_ConvertData, handler, "测试事件")
		assert.Nil(t, err)

		handlers := eb.handlers[Event_Vuln_ConvertData]
		assert.Equal(t, 1, len(handlers))
	})

	t.Run("注册人工校准事件", func(t *testing.T) {
		var handler ManualCalibrationHandler
		handler = func(businessType string, ids []string, values map[string]interface{}, eventData map[string]interface{}, eventSource map[string]interface{}) error {
			return nil
		}
		err := eb.RegisterEventHandler(Event_Manual_Calibration, handler, "测试事件")
		assert.Nil(t, err)

		handlers := eb.handlers[Event_Manual_Calibration]
		assert.Equal(t, 1, len(handlers))
	})

	t.Run("注册漏洞更新事件", func(t *testing.T) {
		var handler VulnUpdateDataHandler
		handler = func(data *poc.Poc) error {
			return nil
		}
		err := eb.RegisterEventHandler(Event_Vuln_UpdateData, handler, "测试事件")
		assert.Nil(t, err)

		handlers := eb.handlers[Event_Vuln_UpdateData]
		assert.Equal(t, 1, len(handlers))
	})
}

func TestUnregisterEventHandler(t *testing.T) {
	eb := NewEventBus()
	handler := func(nodeTaskInfo map[uint64]uint64, sourceId, nodeId uint64, taskType, taskId, assetTaskId string) error {
		return nil
	}

	t.Run("正常注销", func(t *testing.T) {
		eb.handlers = make(map[string][]*EventHandler)
		eb.RegisterEventHandler(Event_Asset_MergeTask_Start, handler, "测试事件")
		assert.NotPanics(t, func() {
			eb.UnregisterEventHandler(Event_Asset_MergeTask_Start, handler)
		})
		handlers := eb.handlers[Event_Asset_MergeTask_Start]
		assert.Equal(t, 0, len(handlers))
	})

	t.Run("注销不存在的事件", func(t *testing.T) {
		eb.handlers = make(map[string][]*EventHandler)
		eb.RegisterEventHandler(Event_Asset_MergeTask_Start, handler, "测试事件")
		handler2 := func(nodeTaskInfo map[uint64]uint64, sourceId, nodeId uint64, taskType, taskId, assetTaskId string) error {
			return nil
		}
		assert.NotPanics(t, func() {
			eb.UnregisterEventHandler(Event_Asset_MergeTask_Start, handler2)
		})
		handlers := eb.handlers[Event_Asset_MergeTask_Start]
		assert.Equal(t, 1, len(handlers))
	})
}

func TestEmit(t *testing.T) {
	eb := NewEventBus()

	t.Run("正常触发", func(t *testing.T) {
		c := 0
		eb.RegisterEventHandler(Event_Asset_MergeTask_Start, func(nodeTaskInfo map[uint64]uint64, sourceId, nodeId uint64, taskType, taskId, assetTaskId string) error {
			c++
			return nil
		}, "测试事件")
		err := eb.Emit(Event_Asset_MergeTask_Start, map[uint64]uint64{uint64(1): uint64(1)}, uint64(1), uint64(1), "1", "1", "1")
		assert.Nil(t, err)
		assert.Equal(t, 1, c)
	})

	t.Run("触发不存在的事件", func(t *testing.T) {
		err := eb.Emit("test", map[uint64]uint64{uint64(1): uint64(1)}, uint64(1), uint64(1), "1", "1", "1")
		assert.NoError(t, err)
	})

	t.Run("触发事件时发生错误", func(t *testing.T) {
		eb.RegisterEventHandler(Event_Asset_MergeTask_Start, func(nodeTaskInfo map[uint64]uint64, sourceId, nodeId uint64, taskType, taskId, assetTaskId string) error {
			return errors.New("test")
		}, "测试事件")
		err := eb.Emit(Event_Asset_MergeTask_Start, map[uint64]uint64{uint64(1): uint64(1)}, uint64(1), uint64(1), "1", "1", "1")
		assert.NoError(t, err)
	})

	t.Run("触发事件参数数量不匹配", func(t *testing.T) {
		eb.RegisterEventHandler(Event_Asset_MergeTask_Start, func(nodeTaskInfo map[uint64]uint64, sourceId, nodeId uint64, taskType, taskId, assetTaskId string) error {
			return nil
		}, "测试事件")
		err := eb.Emit(Event_Asset_MergeTask_Start, map[uint64]uint64{uint64(1): uint64(1)}, uint64(1), uint64(1), "1", "1")
		assert.Error(t, err)
	})

	t.Run("触发事件参数类型不匹配", func(t *testing.T) {
		eb.RegisterEventHandler(Event_Asset_MergeTask_Start, func(nodeTaskInfo map[uint64]uint64, sourceId, nodeId uint64, taskType, taskId, assetTaskId string) error {
			return nil
		}, "测试事件")
		err := eb.Emit(Event_Asset_MergeTask_Start, map[uint64]uint64{uint64(1): uint64(1)}, uint64(1), uint64(1), 1, "1", "1")
		assert.Error(t, err)
	})
}
