# 融合事件机制说明

融合任务会在特定时机触发特定事件，其他模块开发者可以注册对应事件的处理函数，实现在融合特定时机插入相关业务处理逻辑。

## 支持的事件

* **资产融合事件**
  * **融合任务开始前**
    * 事件名称: `event_asset_merge_task_start`
    * 事件处理函数签名: `func(nodeTaskInfo map[uint64]uint64, sourceId, nodeId uint64, taskType, taskId, assetTaskId string) error`
    * 说明: 这个事件会在资产融合任务开始之前触发，用户可以通过参数获取每个节点的最新taskid，触发数据源，触发数据源节点，以及对应的任务id。
    * 参数说明:
      * `nodeTaskInfo`: 每个节点的最新taskid
      * `sourceId`: 触发数据源id
      * `nodeId`: 触发数据源节点id
      * `taskType`: 任务类型
      * `taskId`: 任务id
      * `assetTaskId`: 资产融合任务id
  * **融合任务结束后**
    * 事件名称: `event_asset_merge_task_end`
    * 事件处理函数签名: `func(taskType, taskId string) error`
    * 说明: 这个事件会在所有资产数据融合任务已全部处理完成后触发。
    * 参数说明:
      * `taskType`: 任务类型
      * `taskId`: 任务id
  * **单条数据融合**
    * 事件名称: `event_asset_merge_data`
    * 事件处理函数签名: `func(data *assets.Assets, strategies []*strategy.Strategy, sourceData []*assets.ProcessAssets, latestData map[string]*assets.ProcessAssets) error`
    * 说明: 处理单条资产数据的融合。
    * 参数说明:
      * `data`: 融合后的资产数据
      * `strategies`: 策略
      * `sourceData`: 原始资产数据
      * `latestData`: 每个源节点的最新数据
  * **单条数据融合结束**
    * 事件名称: `event_asset_merge_data_end`
    * 事件处理函数签名: `func(data *assets.Assets, existData *assets.Assets) error`
    * 说明: 单条资产融合结束，可以在此处更新人工校准数据。
    * 参数说明:
      * `data`: 本次融合后的资产数据
      * `existData`: 已有的资产数据

* **人员融合事件**
  * **融合任务开始前**
    * 事件名称: `event_person_merge_task_start`
    * 事件处理函数签名: `func(nodeTaskInfo map[uint64]uint64, sourceId, nodeId uint64, taskType, taskId, personTaskId string) error`
    * 说明: 这个事件会在人员融合任务开始之前触发。
    * 参数说明:
      * `nodeTaskInfo`: 每个节点的最新taskid
      * `sourceId`: 触发数据源id
      * `nodeId`: 触发数据源节点id
      * `taskType`: 任务类型
      * `taskId`: 任务id
      * `personTaskId`: 人员融合任务id
  * **融合任务结束后**
    * 事件名称: `event_person_merge_task_end`
    * 事件处理函数签名: `func(taskType, taskId string) error`
    * 说明: 这个事件会在所有人员数据融合任务已全部处理完成后触发。
    * 参数说明:
      * `taskType`: 任务类型
      * `taskId`: 任务id
  * **单条数据融合**
    * 事件名称: `event_person_merge_data`
    * 事件处理函数签名: `func(data *staff.Staff, strategies []*strategy.Strategy, sourceData []*staff.ProcessStaff, latestData map[string]*staff.ProcessStaff) error`
    * 说明: 处理单条人员数据的融合。
    * 参数说明:
      * `data`: 融合后的人员数据
      * `strategies`: 策略
      * `sourceData`: 原始人员数据
      * `latestData`: 每个源节点的最新数据

* **漏洞融合事件**
  * **融合任务开始前**
    * 事件名称: `event_vuln_merge_task_start`
    * 事件处理函数签名: `func(nodeTaskInfo map[uint64]uint64, sourceId, nodeId uint64, taskType, taskId, vulTaskId string) error`
    * 说明: 这个事件会在漏洞融合任务开始之前触发。
    * 参数说明:
      * `nodeTaskInfo`: 每个节点的最新taskid
      * `sourceId`: 触发数据源id
      * `nodeId`: 触发数据源节点id
      * `taskType`: 任务类型
      * `taskId`: 任务id
      * `vulTaskId`: 漏洞融合任务id
  * **融合任务结束后**
    * 事件名称: `event_vuln_merge_task_end`
    * 事件处理函数签名: `func(taskType, taskId string) error`
    * 说明: 这个事件会在所有漏洞数据融合任务已全部处理完成后触发。
    * 参数说明:
      * `taskType`: 任务类型
      * `taskId`: 任务id
  * **单条数据融合**
    * 事件名称: `event_vuln_merge_data`
    * 事件处理函数签名: `func(data *poc.Poc, strategies []*strategy.Strategy, sourceData []*poc.ProcessPoc, latestData map[string]*poc.ProcessPoc) error`
    * 说明: 处理单条漏洞数据的融合。
    * 参数说明:
      * `data`: 融合后的漏洞数据
      * `strategies`: 策略
      * `sourceData`: 原始漏洞数据
      * `latestData`: 每个源节点的最新数据
  * **单条数据转换**
    * 事件名称: `event_vuln_convert_data`
    * 事件处理函数签名: `func(data *poc.Poc, sourceData *poc.ProcessPoc) error`
    * 说明: 漏洞单条数据转换，适用于poc漏洞或cve&cnvd&cnnvd均为空的漏洞。
    * 参数说明:
      * `data`: 转换后的漏洞数据
      * `sourceData`: 原始漏洞数据

## 注册方法
1. 按照对应事件的处理函数签名编写自定义逻辑
2. 在 `mergeService/event/event_handler/register.go` 文件的 `init` 方法中注册
3. 注册方式参考下面的代码
   ``` golang
   	// 注册资产数据融合任务开始事件-日志记录
	err := eb.RegisterEventHandler(event.Event_Asset_MergeTask_Start, AssetMergeTaskStartLogHandler)
	if err != nil {
		logger.Errorf("register asset merge task start log err: %v", err)
		return
	} else {
		logger.Infof("register asset merge task start log success")
	}
    ```

## 事件注册机制
> 本段内容是内部机制的实现说明，注册者可以参考，不影响调用。

### 内部实现
* 在融合服务内部存在一个事件总线，提供了事件注册、反注册、触发方法
* 事件注册和反注册方法在维护一个全局变量，该变量存储了事件和对应的处理函数
* 触发方法需要指定事件名称和对应参数
* 注册和触发都利用了反射机制检查处理函数的签名，以实现动态注册和触发

### 注册实现
* 所有注册函数都在一个文件的方法中注册
* 融合服务启动时会引用注册文件，触发注册文件的`init`，注册所有处理函数
