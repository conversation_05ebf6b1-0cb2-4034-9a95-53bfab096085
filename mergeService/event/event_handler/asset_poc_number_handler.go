package event_handler

import (
	"context"
	"fmt"
	"fobrain/fobrain/common/localtime"
	"fobrain/mergeService/model/async_stat"
	esmodel "fobrain/models/elastic/assets"
)

func AssetPocNumberHandler(records []esmodel.AssetRecord, ipAreaList map[string]struct{}) error {
	aggRes, err := async_stat.FetchPocAggResultsForIPAreaSet(context.Background(), ipAreaList)
	if err != nil {
		return fmt.Errorf("获取资产统计聚合结果失败. err: %v", err)
	} else {
		aggMap := make(map[string]int64)
		var affectedKeys []interface{}
		for _, agg := range aggRes {
			key := fmt.Sprintf("%s_%d", agg.IP, agg.Area)
			aggMap[key] = agg.VulnCount
			affectedKeys = append(affectedKeys, key)
		}
		for k, record := range records {
			if aggCount, ok := aggMap[fmt.Sprintf("%s_%d", record.Ip, record.Area)]; ok {
				records[k].Asset.PocNum = aggCount
			}
			records[k].Asset.PocNumUpdatedAt = localtime.Now()
		}
		return nil
	}
}
