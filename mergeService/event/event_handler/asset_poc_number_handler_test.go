package event_handler

import (
	"context"
	"errors"
	"fobrain/mergeService/model/async_stat"
	esmodel "fobrain/models/elastic/assets"
	"testing"

	"github.com/agiledragon/gomonkey/v2"
	. "github.com/smartystreets/goconvey/convey"
)

func TestAssetPocNumberHandler(t *testing.T) {
	Convey("AssetPocNumberHandler 单元测试", t, func() {
		// 初始化测试数据
		ipAreaList := map[string]struct{}{
			"***********_1": {},
			"********_2":    {},
		}

		baseRecords := []esmodel.AssetRecord{
			{
				Ip:   "***********",
				Area: 1,
				Asset: &esmodel.Assets{
					PocNum: 0, // 初始值
				},
			},
			{
				Ip:   "********",
				Area: 2,
				Asset: &esmodel.Assets{
					PocNum: 0,
				},
			},
			{
				Ip:   "**********", // 无匹配的聚合结果
				Area: 3,
				Asset: &esmodel.Assets{
					PocNum: 0,
				},
			},
		}

		<PERSON>vey("场景1: 正常聚合并更新 PocNum", func() {
			// Mock 聚合函数返回有效数据
			patches := gomonkey.NewPatches()
			defer patches.Reset()

			patches.ApplyFunc(
				async_stat.FetchPocAggResultsForIPAreaSet,
				func(_ context.Context, _ map[string]struct{}) ([]async_stat.AggResult, error) {
					return []async_stat.AggResult{
						{IP: "***********", Area: 1, VulnCount: 5},
						{IP: "********", Area: 2, VulnCount: 3},
					}, nil
				},
			)

			// 拷贝测试数据避免污染
			records := make([]esmodel.AssetRecord, len(baseRecords))
			copy(records, baseRecords)

			// 执行处理
			err := AssetPocNumberHandler(records, ipAreaList)

			// 验证
			So(err, ShouldBeNil)
			So(records[0].Asset.PocNum, ShouldEqual, 5) // 匹配第一条
			So(records[1].Asset.PocNum, ShouldEqual, 3) // 匹配第二条
			So(records[2].Asset.PocNum, ShouldEqual, 0) // 无匹配，保持原值
		})

		Convey("场景2: 聚合函数返回错误", func() {
			patches := gomonkey.NewPatches()
			defer patches.Reset()

			expectedErr := errors.New("模拟聚合失败")
			patches.ApplyFunc(
				async_stat.FetchPocAggResultsForIPAreaSet,
				func(_ context.Context, _ map[string]struct{}) ([]async_stat.AggResult, error) {
					return nil, expectedErr
				},
			)

			records := make([]esmodel.AssetRecord, len(baseRecords))
			copy(records, baseRecords)

			err := AssetPocNumberHandler(records, ipAreaList)

			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, "获取资产统计聚合结果失败")
			// 验证数据未被修改
			for _, r := range records {
				So(r.Asset.PocNum, ShouldEqual, 0)
			}
		})

		Convey("场景3: 无匹配的聚合数据", func() {
			patches := gomonkey.NewPatches()
			defer patches.Reset()

			patches.ApplyFunc(
				async_stat.FetchPocAggResultsForIPAreaSet,
				func(_ context.Context, _ map[string]struct{}) ([]async_stat.AggResult, error) {
					// 返回与 records 不匹配的数据
					return []async_stat.AggResult{
						{IP: "*******", Area: 1, VulnCount: 10},
					}, nil
				},
			)

			records := make([]esmodel.AssetRecord, len(baseRecords))
			copy(records, baseRecords)

			err := AssetPocNumberHandler(records, ipAreaList)

			So(err, ShouldBeNil)
			for _, r := range records {
				So(r.Asset.PocNum, ShouldEqual, 0)
			}
		})

		Convey("场景4: 空记录列表", func() {
			patches := gomonkey.NewPatches()
			defer patches.Reset()

			patches.ApplyFunc(
				async_stat.FetchPocAggResultsForIPAreaSet,
				func(_ context.Context, _ map[string]struct{}) ([]async_stat.AggResult, error) {
					return []async_stat.AggResult{}, nil
				},
			)

			emptyRecords := []esmodel.AssetRecord{}
			err := AssetPocNumberHandler(emptyRecords, ipAreaList)

			So(err, ShouldBeNil)
		})
	})
}
