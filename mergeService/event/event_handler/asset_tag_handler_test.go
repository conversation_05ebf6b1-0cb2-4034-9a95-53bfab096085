package event_handler

import (
	"encoding/json"
	"fmt"
	"fobrain/models/mysql/field_tag_rules"
	"testing"
)

func TestJson(t *testing.T) {
	var s = `[{"id":12504,"created_at":"2025-04-09 19:43:34","updated_at":"2025-04-09 19:43:34","field":"ip","condition":"=","value":"*************","set_field":"business","set_value":"默认设备维护管理平台 V5","user_id":0,"source_type":2,"source_id":"3d726fd04ad1cc1a35ab8a79d242f6bb"}]`
	var rules []*field_tag_rules.FieldTagRule
	err := json.Unmarshal([]byte(s), &rules)
	fmt.Println(err)
	getAssetTagRules("ip", "*************")
}
