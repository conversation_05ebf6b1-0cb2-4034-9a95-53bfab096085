package log_handler

import (
	logs "fobrain/mergeService/utils/log"
	"fobrain/models/elastic/poc"
	"fobrain/models/mysql/strategy"
)

func VulnMergeDataLogHandler(data *poc.Poc, strategies []*strategy.Strategy, sourceData []*poc.ProcessPoc, latestData map[string]*poc.ProcessPoc) error {
	logs.GetLogger("vuln").Debugf("单条漏洞融合事件. data: %v, strategies: %v, sourceData: %v, latestData: %v", data, strategies, sourceData, latestData)
	return nil
}
