package log_handler

import (
	"fobrain/mergeService/event"
	logs "fobrain/mergeService/utils/log"
)

func PersonMergeTaskStartLogHandler(nodeTaskInfo map[uint64][]uint64, sourceId, nodeId uint64, taskType, taskId, personTaskId string) error {
	logs.GetLogger("person").Infof("人员数据融合任务(%s)开始. nodeTaskInfo: %v, sourceId: %d, nodeId: %d, taskType: %s, taskId: %s, personTaskId: %s", event.Event_Person_MergeTask_Start, nodeTaskInfo, sourceId, nodeId, taskType, taskId, personTaskId)
	return nil
}
