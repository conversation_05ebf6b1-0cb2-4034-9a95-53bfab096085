package log_handler

import logs "fobrain/mergeService/utils/log"

func ManualCalibrationLogHandler(businessType string, ids []string, values map[string]interface{}, eventData map[string]interface{}, eventSource map[string]interface{}) error {
	logs.GetLogger(businessType).Infof("人工校准事件触发. businessType: %s, ids: %v, values: %v, eventData: %v, eventSource: %v", businessType, ids, values, eventData, eventSource)
	return nil
}
