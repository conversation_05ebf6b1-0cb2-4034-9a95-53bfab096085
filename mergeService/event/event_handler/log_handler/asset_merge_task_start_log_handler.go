package log_handler

import (
	"fobrain/mergeService/event"
	logs "fobrain/mergeService/utils/log"
)

func AssetMergeTaskStartLogHandler(nodeTaskInfo map[uint64][]uint64, sourceId, nodeId uint64, taskType, taskId, assetTaskId string) error {
	logs.GetLogger("asset").Infof("资产数据融合任务(%s)开始. nodeTaskInfo: %v, sourceId: %d, nodeId: %d, taskType: %s, taskId: %s, assetTaskId: %s", event.Event_Asset_MergeTask_Start, nodeTaskInfo, sourceId, nodeId, taskType, taskId, assetTaskId)
	return nil
}
