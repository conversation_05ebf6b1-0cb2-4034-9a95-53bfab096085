package log_handler

import (
	logs "fobrain/mergeService/utils/log"
)

func DeviceMergeTaskStartLogHandler(nodeTaskInfo map[uint64][]uint64, sourceId, nodeId uint64, taskType, taskId, deviceTaskId string) error {
	logs.GetLogger("device").Infof("触发设备数据融合任务开始事件,nodeTaskInfo:%v,sourceId:%v,nodeId:%v,taskType:%v,taskId:%v,deviceTaskId:%v", nodeTaskInfo, sourceId, nodeId, taskType, taskId, deviceTaskId)
	return nil
}
