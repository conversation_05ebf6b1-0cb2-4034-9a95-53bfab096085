package log_handler

import (
	logs "fobrain/mergeService/utils/log"
	"fobrain/models/elastic/staff"
	"fobrain/models/mysql/strategy"
)

func PersonMergeDataLogHandler(data *staff.Staff, strategies []*strategy.Strategy, sourceData []*staff.ProcessStaff, latestData map[string]*staff.ProcessStaff) error {
	logs.GetLogger("person").Debugf("单条人员融合事件. data: %v, strategies: %v, sourceData: %v, latestData: %v", data, strategies, sourceData, latestData)
	return nil
}
