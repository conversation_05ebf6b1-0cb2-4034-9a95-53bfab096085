package log_handler

import (
	"fobrain/mergeService/event"
	logs "fobrain/mergeService/utils/log"
)

func VulnMergeTaskStartLogHandler(nodeTaskInfo map[uint64][]uint64, sourceId, nodeId uint64, taskType, taskId, vulTaskId string) error {
	logs.GetLogger("vuln").Infof("漏洞数据融合任务(%s)开始. nodeTaskInfo: %v, sourceId: %d, nodeId: %d, taskType: %s, taskId: %s, vulTaskId: %s", event.Event_Vuln_MergeTask_Start, nodeTaskInfo, sourceId, nodeId, taskType, taskId, vulTaskId)
	return nil
}
