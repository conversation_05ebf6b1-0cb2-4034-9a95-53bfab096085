package log_handler

import (
	logs "fobrain/mergeService/utils/log"
	"fobrain/models/elastic/device"
	"fobrain/models/mysql/strategy"
	"fobrain/pkg/utils"
)

func DeviceMergeDataLogHandler(device *device.Device, strategies []*strategy.Strategy, sourceData []*device.ProcessDevice, latestData map[string]*device.ProcessDevice) error {
	logger := logs.GetLogger("device")
	logger.Infof("触发设备数据融合事件,device:%v,strategies:%v,sourceData:%v,latestData:%v", device, utils.AnyToStr(strategies), utils.AnyToStr(sourceData), utils.AnyToStr(latestData))
	return nil
}
