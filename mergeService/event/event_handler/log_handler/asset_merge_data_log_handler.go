package log_handler

import (
	logs "fobrain/mergeService/utils/log"
	"fobrain/models/elastic/assets"
	"fobrain/models/mysql/strategy"
)

func AssetMergeDataLogHandler(data *assets.Assets, strategies []*strategy.Strategy, sourceData []*assets.ProcessAssets, latestData map[string]*assets.ProcessAssets) error {
	logs.GetLogger("asset").Debugf("单条资产融合事件. data: %v, strategies: %v, sourceData: %v, latestData: %v", data, strategies, sourceData, latestData)
	return nil
}
