package event_handler

import (
	"context"
	"fmt"
	"fobrain/initialize/redis"
	logs "fobrain/mergeService/utils/log"
	"fobrain/models/elastic/assets"
	"fobrain/models/elastic/business_system"
	"fobrain/models/elastic/staff"
	"sync"

	"fobrain/initialize/es"
	merge_helper "fobrain/mergeService/model/helper"
	redis_helper "fobrain/models/redis"

	"github.com/olivere/elastic/v7"
)

// DeviceMergeTaskStartPreparationHandler 设备数据融合任务开始事件-准备工作
// 执行时机：设备数据融合任务开始事件
func DeviceMergeTaskStartPreparationHandler(nodeTaskInfo map[uint64][]uint64, sourceId, nodeId uint64, taskType, taskId, assetTaskId string) error {
	logger := logs.GetLogger("asset")
	redisClient := redis.GetRedisClient()
	var wg sync.WaitGroup

	// 缓存部门数据
	logger.Infof("缓存部门数据")
	wg.Add(1)
	go func() {
		err := merge_helper.CacheDepartmentData(redisClient)
		if err != nil {
			logger.Errorf("缓存部门数据失败. err: %v", err)
		}
		wg.Done()
	}()

	// 缓存人员数据
	logger.Infof("缓存人员数据")
	wg.Add(1)
	go func() {
		err := cacheStaffs()
		if err != nil {
			logger.Errorf("缓存人员数据失败. err: %v", err)
		} else {
		}
		wg.Done()
	}()

	// 缓存资产数据
	logger.Infof("缓存资产数据")
	wg.Add(1)
	go func() {
		err := cacheAssets()
		if err != nil {
			logger.Errorf("缓存资产数据失败. err: %v", err)
		}
		wg.Done()
	}()
	wg.Add(1)
	go func() {
		err := merge_helper.CacheDeviceUniqueKeyToID(redisClient)
		if err != nil {
			logger.Errorf("缓存设备唯一key到id失败. err: %v", err)
		}
		wg.Done()
	}()
	wg.Add(1)
	go func() {
		err := merge_helper.CacheProcessDeviceUniqueKeyToID(redisClient)
		if err != nil {
			logger.Errorf("缓存过程表设备唯一key到id失败. err: %v", err)
		}
		wg.Done()
	}()

	// 等待所有缓存任务完成
	wg.Wait()
	return nil
}

func cacheAssets() error {
	indexName := assets.NewAssets().IndexName()

	sorts := []elastic.Sorter{elastic.NewFieldSort("id").Desc()}

	var lastSortValues []interface{}
	redisKey := redis_helper.AssetKey("map_ip_area_to_id")
	redisClient := redis.GetRedisClient()
	redisClient.Del(context.Background(), redisKey)
	for {
		// 每次循环新建 SearchService
		search := es.GetEsClient().Search().Index(indexName)
		search = search.SortBy(sorts...) // 必须重新设置排序
		search = search.FetchSourceContext(elastic.NewFetchSourceContext(true).Include("id", "area", "ip"))

		// 设置 search_after 参数
		if len(lastSortValues) > 0 {
			search = search.SearchAfter(lastSortValues...)
		}

		// 执行查询
		result, err := search.Size(1000).Query(elastic.NewMatchAllQuery()).Do(context.TODO())
		if err != nil {
			return err
		}

		hits := result.Hits.Hits
		if len(hits) == 0 {
			break
		}
		assetsList := es.ParseHitsValue[assets.Assets](hits)
		mapIp2ID := make(map[string]string)
		for _, asset := range assetsList {
			mapIp2ID[fmt.Sprintf("%s_%d", asset.Ip, asset.Area)] = asset.Id
		}
		redisClient.HSet(context.Background(), redisKey, mapIp2ID)
		// 获取到的数据小于分页数据，则退出
		if len(hits) < 1000 {
			break
		}
		// 更新 search_after 参数为最后一个文档的排序值
		lastSortValues = hits[len(hits)-1].Sort // 需确保 SortValues 存在

	}
	return nil
}

func cacheBusiness() error {
	return business_system.NewBusinessSystems().CacheAllBusinessSystems(context.Background())
}
func cacheStaffs() error {
	return staff.NewStaff().CacheAllStaff(true)
}
