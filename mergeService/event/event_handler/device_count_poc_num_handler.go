package event_handler

import (
	"context"
	"fmt"
	"fobrain/fobrain/common/localtime"
	"fobrain/mergeService/model/async_stat"
	"fobrain/models/elastic/device"
)

// DeviceCountPocNumHandler 设备统计 POC 数量
// 注意目前设备上存的区域和IP没有对应，如果多个区域的内网有相同的IP，并且同一设备属于多个区域时，会出现漏洞数据
func DeviceCountPocNumHandler(records []*device.DeviceRecord) error {
	var ipAreaList = make(map[string]struct{})
	for _, record := range records {
		for _, area := range record.Device.Area {
			for _, ip := range record.Device.PublicIp {
				ipAreaList[fmt.Sprintf("%s_%d", ip, area)] = struct{}{}
			}
			for _, ip := range record.Device.PrivateIp {
				ipAreaList[fmt.Sprintf("%s_%d", ip, area)] = struct{}{}
			}
		}
	}
	aggRes, err := async_stat.FetchPocAggResultsForIPAreaSet(context.Background(), ipAreaList)
	if err != nil {
		return fmt.Errorf("获取资产统计聚合结果失败. err: %v", err)
	} else {
		aggMap := make(map[string]int64)
		for _, agg := range aggRes {
			key := fmt.Sprintf("%s_%d", agg.IP, agg.Area)
			aggMap[key] = agg.VulnCount
		}
		for _, record := range records {
			d := record.Device
			// 设备文档可能包含多个 IP，此处统计所有与设备 area 组合的漏洞数量
			var total int64 = 0
			for _, ip := range d.PublicIp {
				for _, area := range d.Area {
					// 组合 key 为 "ip_area"
					key := fmt.Sprintf("%s_%d", ip, area)
					if cnt, ok := aggMap[key]; ok {
						total += cnt
					}
				}
			}
			for _, ip := range d.PrivateIp {
				for _, area := range d.Area {
					// 组合 key 为 "ip_area"
					key := fmt.Sprintf("%s_%d", ip, area)
					if cnt, ok := aggMap[key]; ok {
						total += cnt
					}
				}
			}
			record.Device.PocNum = total
			record.Device.PocNumUpdatedAt = localtime.Now()
		}
		return nil
	}
}
