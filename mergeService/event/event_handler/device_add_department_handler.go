package event_handler

import (
	"context"
	"encoding/json"
	"fmt"
	"fobrain/initialize/es"
	"fobrain/initialize/redis"
	logs "fobrain/mergeService/utils/log"
	"fobrain/models/elastic/assets"
	"fobrain/models/elastic/device"
	redis_helper "fobrain/models/redis"
	"fobrain/pkg/utils"

	"github.com/olivere/elastic/v7"
)

// DeviceAddDepartmentBatchHandler 添加设备业务系统信息和运维人员信息/资产来源ID
func DeviceAddDepartmentBatchHandler(records []*device.DeviceRecord) error {
	logger := logs.GetLogger("device")
	// 根据设备的关联ip获取资产的业务系统信息和运维人员信息
	redisClient := redis.GetRedisClient()
	redisKey := redis_helper.AssetKey("map_ip_area_to_id")
	var fields = make([]string, 0)
	for _, record := range records {
		for _, ip := range record.Device.PrivateIp {
			if len(record.Device.Area) > 0 {
				for _, area := range record.Device.Area {
					fields = append(fields, fmt.Sprintf("%s_%d", ip, area))
				}
			} else {
				// 如果设备没有区域，则默认使用1
				fields = append(fields, fmt.Sprintf("%s_%d", ip, 1))
			}
		}
		for _, ip := range record.Device.PublicIp {
			if len(record.Device.Area) > 0 {
				for _, area := range record.Device.Area {
					fields = append(fields, fmt.Sprintf("%s_%d", ip, area))
				}
			} else {
				// 如果设备没有区域，则默认使用1
				fields = append(fields, fmt.Sprintf("%s_%d", ip, 1))
			}
		}
	}
	idList, err := redisClient.HMGet(context.Background(), redisKey, fields...).Result()
	if err != nil {
		logger.Errorf("获取资产ID失败: %v", err)
		return err
	}
	var ids = make([]string, 0)
	for _, id := range idList {
		if id != nil {
			ids = append(ids, id.(string))
		}
	}

	esClient := es.GetEsClient()
	mget := esClient.Mget()
	for _, id := range ids {
		mget.Add(elastic.NewMultiGetItem().Id(id).Index(assets.NewAssets().IndexName()))
	}
	result, err := mget.Do(context.Background())
	if err != nil {
		logger.Errorf("根据设备IP查找资产失败: %v", err)
		return err
	}
	allAssets := make([]assets.Assets, 0)
	for _, response := range result.Docs {
		if response.Error != nil {
			logger.Errorf("根据设备IP查找资产失败: %v", response.Error)
			continue
		}
		var asset assets.Assets
		err = json.Unmarshal(response.Source, &asset)
		if err != nil {
			logger.Errorf("根据设备IP查找资产失败: %v", err)
			continue
		}
		allAssets = append(allAssets, asset)
	}
	if len(allAssets) == 0 {
		logger.Errorf("根据设备IP查找资产为空")
		return nil
	}
	var mapAssets = make(map[string]map[int]assets.Assets)
	for _, asset := range allAssets {
		if _, ok := mapAssets[asset.Ip]; !ok {
			mapAssets[asset.Ip] = make(map[int]assets.Assets)
		}
		mapAssets[asset.Ip][asset.Area] = asset
	}
	allAssets = nil
	for k, record := range records {
		var deviceData = record.Device
		// 获取业务系统信息
		deviceData.Business = make([]*device.Business, 0)
		// 设备已经关联的业务系统，key为业务系统id,value为业务系统信息
		mapBusiness := make(map[string]*device.Business)
		// 获取运维人员信息
		deviceData.Opers = make([]*device.OperInfo, 0)
		// 设备已经关联的运维人员，key为运维人员id,value为运维人员信息
		mapOper := make(map[string]*device.OperInfo)
		operIds := make([]string, 0)
		operDepartments := make([]uint64, 0)
		businessOwnerIds := make([]string, 0)
		businessDepartmentIds := make([]uint64, 0)
		// 遍历所有资产
		var allIp = append(deviceData.PublicIp, deviceData.PrivateIp...)
		allSourceIds := deviceData.AllSourceIds
		for _, ip := range allIp {
			if len(deviceData.Area) > 0 {
				for _, area := range deviceData.Area {
					if a, ok := mapAssets[ip][area]; ok {
						allSourceIds = append(allSourceIds, a.AllSourceIds...)
						operIds = append(operIds, a.OperStaffIds...)
						operDepartments = append(operDepartments, a.OperDepartmentIds...)
						businessOwnerIds = append(businessOwnerIds, a.BusinessStaffIds...)
						businessDepartmentIds = append(businessDepartmentIds, a.BusinessDepartmentIds...)
						// 遍历资产的业务系统
						for _, assetBusiness := range a.Business {
							// 如果业务系统不存在，则添加业务系统信息
							if _, ok := mapBusiness[assetBusiness.SystemId]; !ok {
								deviceBusiness := &device.Business{
									Business:           assetBusiness,
									BusinessDepartment: getDepartment(a.BusinessDepartment, assetBusiness.SystemId),
									Ip:                 make([]*device.IpInfo, 0),
								}
								deviceBusiness.Ip = append(deviceBusiness.Ip, &device.IpInfo{
									Ip:   a.Ip,
									Area: a.Area,
								})
								mapBusiness[assetBusiness.SystemId] = deviceBusiness
							} else {
								// 如果业务系统存在，则添加IP信息
								mapBusiness[assetBusiness.SystemId].Ip = append(mapBusiness[assetBusiness.SystemId].Ip, &device.IpInfo{
									Ip:   a.Ip,
									Area: a.Area,
								})
							}
						}
						// 遍历资产的运维人员
						for _, oper := range a.OperInfo {
							// 如果运维人员不存在，则添加运维人员信息
							if _, ok := mapOper[oper.Id]; !ok {
								deviceOper := &device.OperInfo{
									OperInfo:       oper,
									OperDepartment: getOperDepartment(a.OperDepartment, oper.Id),
									Ip:             make([]*device.IpInfo, 0),
								}
								deviceOper.Ip = append(deviceOper.Ip, &device.IpInfo{
									Ip:   a.Ip,
									Area: a.Area,
								})
								mapOper[oper.Id] = deviceOper
							} else {
								// 如果运维人员存在，则添加IP信息
								mapOper[oper.Id].Ip = append(mapOper[oper.Id].Ip, &device.IpInfo{
									Ip:   a.Ip,
									Area: a.Area,
								})
							}
						}
					}
				}
			} else {
				if allAssetsForDevice, ok := mapAssets[ip]; ok {
					for _, a := range allAssetsForDevice {
						allSourceIds = append(allSourceIds, a.AllSourceIds...)
						operIds = append(operIds, a.OperStaffIds...)
						operDepartments = append(operDepartments, a.OperDepartmentIds...)
						businessOwnerIds = append(businessOwnerIds, a.BusinessStaffIds...)
						businessDepartmentIds = append(businessDepartmentIds, a.BusinessDepartmentIds...)
						// 遍历资产的业务系统
						for _, assetBusiness := range a.Business {
							// 如果业务系统不存在，则添加业务系统信息
							if _, ok := mapBusiness[assetBusiness.SystemId]; !ok {
								deviceBusiness := &device.Business{
									Business:           assetBusiness,
									BusinessDepartment: getDepartment(a.BusinessDepartment, assetBusiness.SystemId),
									Ip:                 make([]*device.IpInfo, 0),
								}
								deviceBusiness.Ip = append(deviceBusiness.Ip, &device.IpInfo{
									Ip:   a.Ip,
									Area: a.Area,
								})
								mapBusiness[assetBusiness.SystemId] = deviceBusiness
							} else {
								// 如果业务系统存在，则添加IP信息
								mapBusiness[assetBusiness.SystemId].Ip = append(mapBusiness[assetBusiness.SystemId].Ip, &device.IpInfo{
									Ip:   a.Ip,
									Area: a.Area,
								})
							}
						}
						// 遍历资产的运维人员
						for _, oper := range a.OperInfo {
							// 如果运维人员不存在，则添加运维人员信息
							if _, ok := mapOper[oper.Id]; !ok {
								deviceOper := &device.OperInfo{
									OperInfo:       oper,
									OperDepartment: getOperDepartment(a.OperDepartment, oper.Id),
									Ip:             make([]*device.IpInfo, 0),
								}
								deviceOper.Ip = append(deviceOper.Ip, &device.IpInfo{
									Ip:   a.Ip,
									Area: a.Area,
								})
								mapOper[oper.Id] = deviceOper
							} else {
								// 如果运维人员存在，则添加IP信息
								mapOper[oper.Id].Ip = append(mapOper[oper.Id].Ip, &device.IpInfo{
									Ip:   a.Ip,
									Area: a.Area,
								})
							}
						}
					}
				}
			}
		}
		// 将设备业务系统信息和运维人员信息添加到设备中
		deviceData.Business = make([]*device.Business, 0)
		deviceData.Opers = make([]*device.OperInfo, 0)
		for _, business := range mapBusiness {
			deviceData.Business = append(deviceData.Business, business)
		}
		for _, oper := range mapOper {
			deviceData.Opers = append(deviceData.Opers, oper)
		}
		deviceData.AllSourceIds = utils.ListDistinctNonZero(allSourceIds)
		deviceData.OperStaffIds = utils.ListDistinctNonZero(operIds)
		deviceData.OperDepartmentIds = utils.ListDistinctNonZero(operDepartments)
		deviceData.BusinessStaffIds = utils.ListDistinctNonZero(businessOwnerIds)
		deviceData.BusinessDepartmentIds = utils.ListDistinctNonZero(businessDepartmentIds)
		records[k].Device = deviceData
	}

	return nil
}

// getDepartment 获取业务系统部门信息
func getDepartment(department []*assets.DepartmentBase, businessSystemId string) *assets.DepartmentBase {
	for _, d := range department {
		if d.Parents == nil {
			continue
		}
		if d.BusinessSystemId == businessSystemId {
			return d
		}
	}
	return nil
}

// getOperDepartment 获取运维人员部门信息
func getOperDepartment(department []*assets.DepartmentBase, operId string) *assets.DepartmentBase {
	for _, d := range department {
		if d.Parents == nil {
			continue
		}
		if d.UserId == operId {
			return d
		}
	}
	return nil
}
