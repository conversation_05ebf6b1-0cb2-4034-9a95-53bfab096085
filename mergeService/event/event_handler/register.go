package event_handler

import (
	"fobrain/fobrain/app/repository/personnel_departments"
	"fobrain/mergeService/event"
	"fobrain/mergeService/event/event_handler/log_handler"
	"fobrain/mergeService/handler"
	"fobrain/mergeService/model/business_extract"
	logs "fobrain/mergeService/utils/log"
)

func init() {
	logger := logs.GetLogger("service")
	eb := event.NewEventBus()

	// 注册资产数据融合任务开始事件-日志记录
	err := eb.RegisterEventHandler(event.Event_Asset_MergeTask_Start, log_handler.AssetMergeTaskStartLogHandler, "资产数据融合任务开始事件-日志记录")
	if err != nil {
		logger.Errorf("【资产】【融合】【任务】【开始】-日志记录注册失败: %v", err)
	} else {
		logger.Infof("【资产】【融合】【任务】【开始】-日志记录注册成功")
	}

	// 注册资产数据融合单条数据事件-日志记录
	err = eb.RegisterEventHandler(event.Event_Asset_MergeData, log_handler.AssetMergeDataLogHandler, "资产数据融合单条数据事件-日志记录")
	if err != nil {
		logger.Errorf("【资产】【融合】【单条】【计算】-日志记录注册失败: %v", err)
	} else {
		logger.Infof("【资产】【融合】【单条】【计算】-日志记录注册成功")
	}

	// 注册资产数据融合单条数据结束事件-日志记录
	err = eb.RegisterEventHandler(event.Event_Asset_MergeData_End, log_handler.AssetMergeDataEndLogHandler, "资产数据融合单条数据结束事件-日志记录")
	if err != nil {
		logger.Errorf("【资产】【融合】【单条】【结束】-日志记录注册失败: %v", err)
	} else {
		logger.Infof("【资产】【融合】【单条】【结束】-日志记录注册成功")
	}

	// 注册资产数据融合任务结束事件-日志记录
	err = eb.RegisterEventHandler(event.Event_Asset_MergeTask_End, log_handler.AssetMergeTaskEndLogHandler, "资产数据融合任务结束事件-日志记录")
	if err != nil {
		logger.Errorf("【资产】【融合】【任务】【结束】-日志记录注册失败: %v", err)
	} else {
		logger.Infof("【资产】【融合】【任务】【结束】-日志记录注册成功")
	}

	// 注册人员数据融合任务开始事件-日志记录
	err = eb.RegisterEventHandler(event.Event_Person_MergeTask_Start, log_handler.PersonMergeTaskStartLogHandler, "人员数据融合任务开始事件-日志记录")
	if err != nil {
		logger.Errorf("【人员】【融合】【任务】【开始】-日志记录注册失败: %v", err)
	} else {
		logger.Infof("【人员】【融合】【任务】【开始】-日志记录注册成功")
	}
	// 注册人员数据融合任务开始事件-缓存部门数据
	err = eb.RegisterEventHandler(event.Event_Person_MergeTask_Start, personnel_departments.SetPersonnelDepartmentsRedisHandler, "人员数据融合任务开始事件-缓存部门数据")
	if err != nil {
		logger.Errorf("【人员】【融合】【任务】【开始】-缓存部门数据注册失败: %v", err)
	} else {
		logger.Infof("【人员】【融合】【任务】【开始】-缓存部门数据注册成功")
	}

	// 注册人员数据融合单条数据事件-日志记录
	err = eb.RegisterEventHandler(event.Event_Person_MergeData, log_handler.PersonMergeDataLogHandler, "人员数据融合单条数据事件-日志记录")
	if err != nil {
		logger.Errorf("【人员】【融合】【单条】【计算】-日志记录注册失败: %v", err)
	} else {
		logger.Infof("【人员】【融合】【单条】【计算】-日志记录注册成功")
	}

	// 注册人员数据融合单条数据结束事件-日志记录
	err = eb.RegisterEventHandler(event.Event_Person_MergeData_End, log_handler.PersonMergeDataEndLogHandler, "人员数据融合单条数据结束事件-日志记录")
	if err != nil {
		logger.Errorf("【人员】【融合】【单条】【结束】-日志记录注册失败: %v", err)
	} else {
		logger.Infof("【人员】【融合】【单条】【结束】-日志记录注册成功")
	}

	// 注册人员数据融合任务结束事件-日志记录
	err = eb.RegisterEventHandler(event.Event_Person_MergeTask_End, log_handler.PersonMergeTaskEndLogHandler, "人员数据融合任务结束事件-日志记录")
	if err != nil {
		logger.Errorf("【人员】【融合】【任务】【结束】-日志记录注册失败: %v", err)
	} else {
		logger.Infof("【人员】【融合】【任务】【结束】-日志记录注册成功")
	}

	// 注册漏洞数据融合任务开始事件-日志记录
	err = eb.RegisterEventHandler(event.Event_Vuln_MergeTask_Start, log_handler.VulnMergeTaskStartLogHandler, "漏洞数据融合任务开始事件-日志记录")
	if err != nil {
		logger.Errorf("【漏洞】【融合】【任务】【开始】-日志记录注册失败: %v", err)
	} else {
		logger.Infof("【漏洞】【融合】【任务】【开始】-日志记录注册成功")
	}

	// 注册漏洞数据融合单条数据事件-日志记录
	err = eb.RegisterEventHandler(event.Event_Vuln_MergeData, log_handler.VulnMergeDataLogHandler, "漏洞数据融合单条数据事件-日志记录")
	if err != nil {
		logger.Errorf("【漏洞】【融合】【单条】【计算】-日志记录注册失败: %v", err)
	} else {
		logger.Infof("【漏洞】【融合】【单条】【计算】-日志记录注册成功")
	}

	// 注册漏洞数据单条数据转换事件-日志记录
	err = eb.RegisterEventHandler(event.Event_Vuln_ConvertData, log_handler.VulnConvertDataLogHandler, "漏洞数据单条数据转换事件-日志记录")
	if err != nil {
		logger.Errorf("【漏洞】【融合】【单条】【转换】-日志记录注册失败: %v", err)
	} else {
		logger.Infof("【漏洞】【融合】【单条】【转换】-日志记录注册成功")
	}

	// 注册漏洞数据融合任务结束事件-日志记录
	err = eb.RegisterEventHandler(event.Event_Vuln_MergeTask_End, log_handler.VulnMergeTaskEndLogHandler, "漏洞数据融合任务结束事件-日志记录")
	if err != nil {
		logger.Errorf("【漏洞】【融合】【任务】【结束】-日志记录注册失败: %v", err)
	} else {
		logger.Infof("【漏洞】【融合】【任务】【结束】-日志记录注册成功")
	}

	// 注册漏洞更新事件-日志记录
	err = eb.RegisterEventHandler(event.Event_Vuln_UpdateData, log_handler.VulnUpdateLogHandler, "漏洞更新事件-日志记录")
	if err != nil {
		logger.Errorf("【漏洞】【更新】【单条】-日志记录注册失败: %v", err)
	} else {
		logger.Infof("【漏洞】【更新】【单条】-日志记录注册成功")
	}

	// 注册设备数据融合任务开始事件-准备工作
	err = eb.RegisterEventHandler(event.Event_Device_MergeTask_Start, DeviceMergeTaskStartPreparationHandler, "设备数据融合任务开始事件-准备工作")
	if err != nil {
		logger.Errorf("【设备】【融合】【任务】【开始】-准备工作注册失败: %v", err)
	} else {
		logger.Infof("【设备】【融合】【任务】【开始】-准备工作注册成功")
	}

	// 注册资产数据融合任务开始事件-业务处理
	err = eb.RegisterEventHandler(event.Event_Asset_MergeTask_Start, business_extract.AssetMergeTaskStartBusinessHandler, "资产数据融合任务开始事件-提取业务系统")
	if err != nil {
		logger.Errorf("【资产】【融合】【任务】【开始】-提取业务系统注册失败: %v", err)
	} else {
		logger.Infof("【资产】【融合】【任务】【开始】-提取业务系统注册成功")
	}

	// 打标签逻辑已经融合进了资产融合流程，不需要单独处理业务配置相关的标签
	// // 注册资产数据融合任务结束事件-业务配置标签处理
	// err = eb.RegisterEventHandler(event.Event_Asset_MergeTask_End, business_extract.AssetMergeTaskEndHandler, "资产数据融合任务结束事件-业务配置标签处理")
	// if err != nil {
	// 	logger.Errorf("【资产】【融合】【任务】【结束】-业务系统配置标签处理注册失败: %v", err)
	// } else {
	// 	logger.Infof("【资产】【融合】【任务】【结束】-业务系统配置标签处理注册成功")
	// }

	// 注册资产数据融合单条数据结束事件-业务配置校准数据处理
	err = eb.RegisterEventHandler(event.Event_Asset_MergeData_End, business_extract.AssetMergeDataEndHandler, "资产单条数据融合结束事件-业务配置校准数据处理")
	if err != nil {
		logger.Errorf("【资产】【融合】【单条】【结束】-业务系统配置校准数据处理注册失败: %v", err)
	} else {
		logger.Infof("【资产】【融合】【单条】【结束】-业务系统配置校准数据处理注册成功")
	}

	// 注册漏洞数据融合单条数据业务系统处理事件
	err = eb.RegisterEventHandler(event.Event_Vuln_MergeData, business_extract.VulnMergeBusinessHandler, "漏洞数据融合单条数据事件-业务系统处理")
	if err != nil {
		logger.Errorf("【漏洞】【融合】【单条】【计算】-业务系统处理注册失败: %v", err)
	} else {
		logger.Infof("【漏洞】【融合】【单条】【计算】-业务系统处理注册成功")
	}

	// 注册漏洞数据融合批量数据写入前事件-权限字段处理  VulnConvertBusinessDataHandler/VulnMergeBusinessHandler 已处理
	err = eb.RegisterEventHandler(event.EvtVulnMergeDataBeforeWrite, PocPermissionFieldsHandler, "注册漏洞数据融合批量数据写入前事件-权限字段处理")
	if err != nil {
		logger.Errorf("【漏洞】【融合】【批量】【写入前】-注册漏洞数据融合批量数据写入前事件注册失败: %v", err)
	} else {
		logger.Infof("【漏洞】【融合】【批量】【写入前】-注册漏洞数据融合批量数据写入前事件注册成功")
	}

	// 注册漏洞转换事件-漏洞数据poc跟cve等编号为空 融合单条数据业务系统处理事件
	err = eb.RegisterEventHandler(event.Event_Vuln_ConvertData, business_extract.VulnConvertBusinessDataHandler, "漏洞转换事件-漏洞数据poc跟cve等编号为空 融合单条数据业务系统处理事件")
	if err != nil {
		logger.Errorf("【漏洞】【融合】【单条】【转换】-漏洞数据poc跟cve等编号为空，业务系统处理事件注册失败: %v", err)
	} else {
		logger.Infof("【漏洞】【融合】【单条】【转换】-漏洞数据poc跟cve等编号为空，业务系统处理事件注册成功")
	}

	// 注册漏洞更新事件-漏洞派发
	err = eb.RegisterEventHandler(event.Event_Vuln_UpdateData, VulUpdateDistributeHandler, "漏洞更新事件-漏洞派发")
	if err != nil {
		logger.Errorf("【漏洞】【更新】【单条】-漏洞派发注册失败: %v", err)
	} else {
		logger.Infof("【漏洞】【更新】【单条】-漏洞派发注册成功")
	}

	// 注册资产的权限相关数据字段处理
	err = eb.RegisterEventHandler(event.Event_Asset_MergeData_End, handler.AssetPermissionFieldsHandler, "资产数据融合单条数据结束事件-资产的权限相关数据字段处理")
	if err != nil {
		logger.Errorf("【资产】【融合】【单条】【结束】-资产的权限相关数据字段处理注册失败: %v", err)
	} else {
		logger.Infof("【资产】【融合】【单条】【结束】-资产的权限相关数据字段处理注册成功")
	}

	// 注册设备数据融合任务开始事件-日志记录
	err = eb.RegisterEventHandler(event.Event_Device_MergeTask_Start, log_handler.DeviceMergeTaskStartLogHandler, "设备数据融合任务开始事件-日志记录")
	if err != nil {
		logger.Errorf("【设备】【融合】【任务】【开始】-日志记录注册失败: %v", err)
	} else {
		logger.Infof("【设备】【融合】【任务】【开始】-日志记录注册成功")
	}

	// 注册设备数据融合任务结束事件-日志记录
	err = eb.RegisterEventHandler(event.Event_Device_MergeTask_End, log_handler.DeviceMergeTaskEndLogHandler, "设备数据融合任务结束事件-日志记录")
	if err != nil {
		logger.Errorf("【设备】【融合】【任务】【结束】-日志记录注册失败: %v", err)
	} else {
		logger.Infof("【设备】【融合】【任务】【结束】-日志记录注册成功")
	}

	// 注册设备数据融合单条数据事件-日志记录
	err = eb.RegisterEventHandler(event.Event_Device_MergeData, log_handler.DeviceMergeDataLogHandler, "设备数据融合单条数据事件-日志记录")
	if err != nil {
		logger.Errorf("【设备】【融合】【单条】【计算】-日志记录注册失败: %v", err)
	} else {
		logger.Infof("【设备】【融合】【单条】【计算】-日志记录注册成功")
	}

	// 注册设备数据融合单条数据结束事件-日志记录
	err = eb.RegisterEventHandler(event.Event_Device_MergeData_End, log_handler.DeviceMergeDataEndLogHandler, "设备数据融合单条数据结束事件-日志记录")
	if err != nil {
		logger.Errorf("【设备】【融合】【单条】【结束】-日志记录注册失败: %v", err)
	} else {
		logger.Infof("【设备】【融合】【单条】【结束】-日志记录注册成功")
	}

	// 注册设备数据融合单条数据结束事件-添加设备相关资产来源ID
	//err = eb.RegisterEventHandler(event.Event_Device_MergeData_End, DeviceAddRelatedSource, "设备数据融合单条数据结束事件-添加设备相关资产来源ID")
	//if err != nil {
	//	logger.Errorf("【设备】【融合】【单条】【结束】-添加设备相关资产来源ID注册失败: %v", err)
	//} else {
	//	logger.Infof("【设备】【融合】【单条】【结束】-添加设备相关资产来源ID注册成功")
	//}

	// 注册人工校准事件
	err = eb.RegisterEventHandler(event.Event_Manual_Calibration, log_handler.ManualCalibrationLogHandler, "人工校准事件-日志记录")
	if err != nil {
		logger.Errorf("【人工】【校准】-日志记录注册失败: %v", err)
	} else {
		logger.Infof("【人工】【校准】-日志记录注册成功")
	}

	// 注册资产人工校准事件, 更新业务系统数据
	err = eb.RegisterEventHandler(event.Event_Manual_Calibration, business_extract.Business_AssetManualCalibrationHandler_UpdateBusiness, "资产人工校准事件-更新业务系统数据")
	if err != nil {
		logger.Errorf("【人工】【校准】-更新业务系统数据注册失败: %v", err)
	} else {
		logger.Infof("【人工】【校准】-更新业务系统数据注册成功")
	}

	// 注册资产人工校准事件, 更新漏洞关联的业务系统及运维人员信息
	// 走触发二次局部融合了
	//err = eb.RegisterEventHandler(event.Event_Manual_Calibration, business_extract.Business_AssetManualCalibrationHandler_UpdatePoc, "资产人工校准事件-更新漏洞关联的业务系统及运维人员")
	//if err != nil {
	//	logger.Errorf("【人工】【校准】-更新漏洞关联的业务系统注册失败: %v", err)
	//} else {
	//	logger.Infof("【人工】【校准】-更新漏洞关联的业务系统注册成功")
	//}

	// 注册统计资产的关联的漏洞数量
	err = eb.RegisterEventHandler(event.EvtDeviceMergeDataBeforeWrite, DeviceCountPocNumHandler, "设备数据融合批次写入前事件-统计设备的关联的漏洞数量")
	if err != nil {
		logger.Errorf("【设备】【融合】【批次】【写入前】-统计设备的关联的漏洞数量注册失败: %v", err)
	} else {
		logger.Infof("【设备】【融合】【批次】【写入前】-统计设备的关联的漏洞数量注册成功")
	}
	// 注册设备负责人，运维负责人，业务系统负责人处理，资产关联来源ID逻辑，从资产提取
	err = eb.RegisterEventHandler(event.EvtDeviceMergeDataBeforeWrite, DeviceAddDepartmentBatchHandler, "设备数据融合批次写入前事件-添加设备业务系统信息和运维人员信息/资产来源ID")
	if err != nil {
		logger.Errorf("【设备】【融合】【批次】【写入前】-添加设备业务系统信息和运维人员信息/资产来源ID注册失败: %v", err)
	} else {
		logger.Infof("【设备】【融合】【批次】【写入前】-添加设备业务系统信息和运维人员信息/资产来源ID注册成功")
	}

	// 注册设备数据融合单条数据结束事件-添加设备业务系统信息和运维人员信息
	//err = eb.RegisterEventHandler(event.Event_Device_MergeData_End, DeviceAddDepartmentHandler, "设备数据融合单条数据结束事件-添加设备业务系统信息和运维人员信息")
	//if err != nil {
	//	logger.Errorf("【设备】【融合】【单条】【结束】-添加设备业务系统信息和运维人员信息注册失败: %v", err)
	//} else {
	//	logger.Infof("【设备】【融合】【单条】【结束】-添加设备业务系统信息和运维人员信息注册成功")
	//}

	// 注册设备数据融合单条数据结束事件-设备权限相关数据字段处理
	//err = eb.RegisterEventHandler(event.Event_Device_MergeData_End, handler.DevicePermissionFieldsHandler, "设备数据融合单条数据结束事件-设备权限相关数据字段处理")
	//if err != nil {
	//	logger.Errorf("【设备】【融合】【单条】【结束】-设备权限相关数据字段处理注册失败: %v", err)
	//} else {
	//	logger.Infof("【设备】【融合】【单条】【结束】-设备权限相关数据字段处理注册成功")
	//}

	// 注册资产数据融合单条数据事件-补充资产的部门信息
	err = eb.RegisterEventHandler(event.Event_Asset_MergeData, AssetDepartmentHandler, "资产数据融合单条数据事件-补充资产的部门信息")
	if err != nil {
		logger.Errorf("【资产】【融合】【单条】【计算】-补充资产的部门信息注册失败: %v", err)
	} else {
		logger.Infof("【资产】【融合】【单条】【计算】-补充资产的部门信息注册成功")
	}

	// 注册人员数据融合单条数据结束事件-部门提取
	err = eb.RegisterEventHandler(event.Event_Person_MergeData_End, PersonMergeDataEnd_ForExtractDepartment, "人员数据融合单条数据结束事件-部门提取")
	if err != nil {
		logger.Errorf("【人员】【融合】【单条】【结束】-部门提取注册失败: %v", err)
	} else {
		logger.Infof("【人员】【融合】【单条】【结束】-部门提取注册成功")
	}

	// 注册人员数据融合单条数据结束事件-刷新PGID缓存
	err = eb.RegisterEventHandler(event.Event_Person_MergeData_End, PersonMergeDataEnd_RefreshPgidCache, "人员数据融合单条数据结束事件-刷新PGID缓存")
	if err != nil {
		logger.Errorf("【人员】【融合】【单条】【结束】-刷新PGID缓存注册失败: %v", err)
	} else {
		logger.Infof("【人员】【融合】【单条】【结束】-刷新PGID缓存注册成功")
	}

	// 注册人工校准事件-刷新PGID缓存
	err = eb.RegisterEventHandler(event.Event_Manual_Calibration, ManualCalibration_RefreshPgidCache, "人工校准事件-刷新PGID缓存")
	if err != nil {
		logger.Errorf("【人工】【校准】-刷新PGID缓存注册失败: %v", err)
	} else {
		logger.Infof("【人工】【校准】-刷新PGID缓存注册成功")
	}

	// 注册漏洞数据融合单条数据事件-补充运维信息 Event_Vuln_ConvertData已经处理了重复
	//err = eb.RegisterEventHandler(event.Event_Vuln_MergeData, VulnDepartmentHandlerForMerge, "漏洞数据融合单条数据事件-补充运维信息")
	//if err != nil {
	//	logger.Errorf("【漏洞】【融合】【单条】【计算】-补充运维信息注册失败: %v", err)
	//} else {
	//	logger.Infof("【漏洞】【融合】【单条】【计算】-补充运维信息注册成功")
	//}

	// 注册漏洞数据转换单条数据事件-补充运维信息 Event_Vuln_ConvertData 已经处理了重复
	//err = eb.RegisterEventHandler(event.Event_Vuln_ConvertData, VulnDepartmentHandlerForConvert, "漏洞数据转换单条数据事件-补充运维信息")
	//if err != nil {
	//	logger.Errorf("【漏洞】【转换】【单条】【计算】-补充运维信息注册失败: %v", err)
	//} else {
	//	logger.Infof("【漏洞】【转换】【单条】【计算】-补充运维信息注册成功")
	//}

	// 注册资产数据融合单条数据事件-标签处理
	err = eb.RegisterEventHandler(event.Event_Asset_MergeData, AssetTagHandler, "资产数据融合单条数据事件-标签处理")
	if err != nil {
		logger.Errorf("【资产】【融合】【单条】【计算】-标签处理注册失败: %v", err)
	} else {
		logger.Infof("【资产】【融合】【单条】【计算】-标签处理注册成功")
	}
	// 注册资产数据融合批次写入前事件-漏洞数量处理
	err = eb.RegisterEventHandler(event.EvtAssetMergeDataBeforeWrite, AssetPocNumberHandler, "注册资产数据融合批次写入前事件-漏洞数量处理")
	if err != nil {
		logger.Errorf("【资产】【融合】【批次】【写入前】-漏洞数量处理注册失败: %v", err)
	} else {
		logger.Infof("【资产】【融合】【批次】【写入前】-漏洞数量处理注册成功")
	}

	// 注册资产融合任务结束时间-IP段关联IP使用率更新
	err = eb.RegisterEventHandler(event.Event_Asset_MergeTask_End, AssetIpUsageRateHandler, "资产融合任务结束时间-IP段关联IP使用率更新")
	if err != nil {
		logger.Errorf("【资产】【融合】【任务】【结束】-IP段关联IP使用率更新注册失败: %v", err)
	} else {
		logger.Infof("【资产】【融合】【任务】【结束】-IP段关联IP使用率更新注册成功")
	}

	// 注册资产融合任务结束事件- IP关联业务系统变更后更新业务系统关联IP事件
	err = eb.RegisterEventHandler(event.Event_Asset_MergeTask_End, business_extract.UpdateBusinessRelationAssets, "资产融合任务结束时间-IP关联业务系统变更后更新业务系统关联IP事件")
	if err != nil {
		logger.Errorf("【资产】【融合】【任务】【结束】-IP关联业务系统变更后更新业务系统关联IP事件注册失败: %v", err)
	} else {
		logger.Infof("【资产】【融合】【任务】【结束】-IP关联业务系统变更后更新业务系统关联IP事件注册成功")
	}

	// 注册人员数据融合任务结束事件- 更新业务系统责任人信息
	err = eb.RegisterEventHandler(event.Event_Person_MergeTask_End, business_extract.UpdateBusinessStaff, "人员融合任务结束时间-更新业务系统责任人信息")
	if err != nil {
		logger.Errorf("【人员】【融合】【任务】【结束】-更新业务系统责任人信息注册失败: %v", err)
	} else {
		logger.Infof("【人员】【融合】【任务】【结束】-更新业务系统责任人信息注册成功")
	}
}
