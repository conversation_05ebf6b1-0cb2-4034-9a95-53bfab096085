package event_handler

import (
	logs "fobrain/mergeService/utils/log"
	"fobrain/models/elastic/assets"
	"fobrain/models/elastic/helper"
	"fobrain/models/mysql/strategy"
	"fobrain/pkg/utils"
)

// AssetDepartmentHandler 补充资产的部门信息
func AssetDepartmentHandler(data *assets.Assets, strategies []*strategy.Strategy, sourceData []*assets.ProcessAssets, latestData map[string]*assets.ProcessAssets) error {
	logger := logs.GetLogger("asset")

	data.OperInfo = make([]*assets.PersonBase, 0)
	data.OperDepartment = make([]*assets.DepartmentBase, 0)
	if data.PersonLimit == nil {
		data.PersonLimit = make([]string, 0)
	}
	if data.PersonLimitHash == nil {
		data.PersonLimitHash = make([]string, 0)
	}

	// 处理运维人员映射,key为映射字段，value为来源完整信息
	mapOper := make(map[string][]*assets.PersonWithMapping)
	for _, operWithMapping := range data.OperWithMapping {
		mapOper[operWithMapping.MappingField] = append(mapOper[operWithMapping.MappingField], operWithMapping)
	}
	// 根据原始信息获取运维人员信息
	personResult, departmentResult, err := helper.GetPersonInfoByList(mapOper)
	if err != nil {
		logger.Warnf("根据映射字段获取人员信息失败.映射字段: %+v, err: %v", mapOper, err)
		return err
	}
	// 更新运维人员信息
	data.OperInfo = append(data.OperInfo, personResult...)
	data.OperDepartment = append(data.OperDepartment, departmentResult...)
	fidList, fidHashList := getPersonFid(personResult)
	// 更新人员限制字段
	data.PersonLimit = append(data.PersonLimit, fidList...)
	data.PersonLimitHash = append(data.PersonLimitHash, fidHashList...)
	return nil
}

// 获取人员fid列表
func getPersonFid(personList []*assets.PersonBase) ([]string, []string) {
	var fidList []string
	var fidHashList []string
	for _, person := range personList {
		fidList = append(fidList, person.Fid)
		fidHashList = append(fidHashList, utils.Md5Hash(person.Fid))
	}
	return fidList, fidHashList
}
