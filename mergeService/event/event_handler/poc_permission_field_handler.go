package event_handler

import (
	"fobrain/models/elastic/poc"
	"fobrain/pkg/utils"
)

// PocPermissionFieldsHandler 漏洞数据融合批量数据写入前事件-权限字段处理
// 执行时机：漏洞数据融合批量数据写入前
func PocPermissionFieldsHandler(data []*poc.PocRecord) error {
	for _, v := range data {
		// 漏洞修复人部门ids
		repairDepartmentIds := make([]uint64, 0)
		personDepartment := v.Poc.PersonDepartment
		for _, dep := range personDepartment {
			repairDepartmentIds = append(repairDepartmentIds, dep.Id)
			for _, parent := range dep.Parents {
				repairDepartmentIds = append(repairDepartmentIds, parent.Id)
			}
		}
		v.Poc.RepairDepartmentIds = utils.ListDistinctNonZero(repairDepartmentIds)
	}
	return nil
}
