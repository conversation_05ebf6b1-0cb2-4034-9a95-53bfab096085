package event_handler

import (
	logs "fobrain/mergeService/utils/log"
	"fobrain/models/elastic/staff"
	pgidservice "fobrain/services/people_pgid"
)

// PersonMergeDataEnd_RefreshPgidCache 触发人员数据融合单条数据结束事件，刷新PGID缓存
func PersonMergeDataEnd_RefreshPgidCache(staff *staff.Staff, msgData *staff.Staff) error {
	mlog := logs.GetLogger("person")
	_, err := pgidservice.CachePeopleBaseInfo(staff.Id, staff)
	if err != nil {
		mlog.Warnf("缓存人员基本信息失败. staffId: %s, staffName: %s, err: %v", staff.Id, staff.Name, err)
	}
	return nil
}

// ManualCalibration_RefreshPgidCache 触发人工校准事件，刷新PGID缓存
func ManualCalibration_RefreshPgidCache(businessType string, ids []string, values map[string]interface{}, eventData map[string]interface{}, eventSource map[string]interface{}) error {
	if businessType == "person" {
		for _, id := range ids {
			// 删除缓存
			pgidservice.ClearCache(id)
		}
	}
	return nil
}
