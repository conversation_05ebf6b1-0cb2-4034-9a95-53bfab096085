package event_handler

import (
	"context"
	"encoding/json"
	"testing"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/alicebob/miniredis/v2"
	"github.com/go-redis/redis/v8"
	"github.com/stretchr/testify/assert"

	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/initialize/mysql"
	"fobrain/models/elastic/poc"
	"fobrain/models/elastic/staff"
	"fobrain/models/mysql/user"
)

func TestVulUpdateDistributeHandler(t *testing.T) {
	s, err := miniredis.Run()
	if err != nil {
		t.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
	}
	defer s.Close()

	cli := redis.NewClient(&redis.Options{
		Addr: s.Addr(),
	})
	testcommon.SetRedisClient(cli)
	cacheData := map[string]interface{}{
		"to_staff_id": "c7a6e5510b65420a8e3263ceaf2f6c111",
		"poc_ids":     "c7a6e5510b65420a8e3263ceaf2f6c95",
		"status":      poc.PocStatusOfForward,
		"user": user.User{
			BaseModel: mysql.BaseModel{
				Id: 1,
			},
			Username: "AA",
			Account:  "aa",
		},
		"staff": staff.Staff{
			Id:      "c7a6e5510b65420a8e3263ceaf2f6c122",
			SsoId:   "c7a6e5510b65420a8e3263ceaf2f6c122",
			SsoName: "aaa",
			Fid:     "A:***********",
			FidHash: "********************************",
			Name:    "AA",
			Email: []string{
				"<EMAIL>",
			},
		},
	}
	cacheDataJSON, _ := json.Marshal(cacheData)
	// 模拟 Redis 设置操作
	mockRedisGet := gomonkey.ApplyMethodFunc(testcommon.GetRedisClient(), "Get", func(ctx context.Context, key string) *redis.StringCmd {
		cmd := redis.NewStringCmd(ctx)
		cmd.SetVal(string(cacheDataJSON)) // 模拟缓存数据
		return cmd
	})
	defer mockRedisGet.Reset()
	data := poc.Poc{
		Id:     "c7a6e5510b65420a8e3263ceaf2f6c95",
		Status: 15,
	}
	err = VulUpdateDistributeHandler(&data)
	assert.NoError(t, err)
}
