package event_handler

import (
	"encoding/json"
	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/models/elastic/device"
	"testing"

	"github.com/olivere/elastic/v7"
	"github.com/stretchr/testify/assert"
)

func TestDeviceAddRelatedSource(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	mockServer.Register("/asset/_search", elastic.SearchResult{
		ScrollId: "scroll-id-2",
		Hits: &elastic.SearchHits{
			TotalHits: &elastic.TotalHits{
				Value:    2,
				Relation: "eq",
			},
			Hits: []*elastic.SearchHit{
				{
					Id:     "1",
					Source: json.RawMessage(`{"id":"1","all_source_ids":[1,2]}`),
				},
				{
					Id:     "2",
					Source: json.RawMessage(`{"id":"2","all_source_ids":[2,3]}`),
				},
			},
		},
	})
	mockServer.RegisterEmptyScrollHandler()

	device := &device.Device{
		PrivateIp: []string{"IP_ADDRESS"},
		PublicIp:  []string{"*******"},
		Area:      []int{1, 2},
	}

	err := DeviceAddRelatedSource(device, nil)
	assert.Nil(t, err)
	assert.Equal(t, []uint64{1, 2, 3}, device.AllSourceIds)
}
