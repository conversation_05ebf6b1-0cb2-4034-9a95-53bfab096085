package event_handler

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"fobrain/initialize/mysql"
	"fobrain/initialize/redis"
	logs "fobrain/mergeService/utils/log"
	"fobrain/models/elastic/assets"
	"fobrain/models/elastic/business_system"
	"fobrain/models/elastic/helper"
	"fobrain/models/elastic/staff"
	"fobrain/models/mysql/field_tag_rules"
	"fobrain/models/mysql/strategy"
	key_helper "fobrain/models/redis"
	"fobrain/pkg/utils"
	"strconv"

	goRedis "github.com/go-redis/redis/v8"
)

var redisClient *goRedis.Client

func init() {
	redisClient = redis.GetRedisClient()
}

// AssetTagHandler 资产打标签
// 执行时机：单条数据原始信息融合已完成，人工校准未开始之前
func AssetTagHandler(data *assets.Assets, strategies []*strategy.Strategy, sourceData []*assets.ProcessAssets, latestData map[string]*assets.ProcessAssets) error {
	logger := logs.GetLogger("asset")
	logger.Debugf("开始处理资产标签. Ip: %s,area: %d", data.Ip, data.Area)

	// 根据ip查询规则
	rules, err := getAssetTagRules("ip", data.Ip)
	if err != nil {
		logger.Errorf("获取资产标签规则失败. err: %v", err)
		return err
	}
	logger.Debugf("获取资产标签规则成功. 共%d条规则", len(rules))
	// 按照setField分组
	ruleMap := make(map[string][]string)
	for _, rule := range rules {
		logger.Debugf("执行规则. rule id: %d", rule.Id)
		// 标签直接保存SetValue
		if rule.SetField == field_tag_rules.SetFieldTag {
			ruleMap[field_tag_rules.SetFieldTag] = append(ruleMap[field_tag_rules.SetFieldTag], rule.SetValue)
		} else {
			// 其他字段保存SourceId
			ruleMap[rule.SetField] = append(ruleMap[rule.SetField], rule.SourceId)
		}
	}
	// 执行规则，即更新资产模型
	for setField, sourceContent := range ruleMap {
		switch setField {
		case field_tag_rules.SetFieldBusiness:
			// 更新业务系统
			businessList, err := business_system.NewBusinessSystems().GetAssetBusinessStructByIds(context.Background(), sourceContent)
			if err != nil || businessList == nil {
				logger.Errorf("获取业务系统失败或业务系统不存在，sourceIds:%s,err:%v", sourceContent, err)
				continue
			}
			// 去重,避免重复添加
			businessMap := make(map[string]bool)
			for _, business := range data.Business {
				businessMap[business.SystemId] = true
			}
			for _, business := range businessList {
				if _, ok := businessMap[business.SystemId]; !ok {
					data.Business = append(data.Business, business)
					data.BusinessDepartment = append(data.BusinessDepartment, business.DepartmentBase...)
				}
			}
		case field_tag_rules.SetFieldOper:
			// 更新运维人员
			staffList, err := staff.NewStaff().GetByFIDsFromCache(context.Background(), sourceContent)
			if err != nil || staffList == nil {
				logger.Errorf("获取人员信息失败,staffId:%s,err:%v", sourceContent, err)
				continue
			}
			// 去重,避免重复添加
			staffMap := make(map[string]bool)
			for _, s := range data.OperInfo {
				staffMap[s.Fid] = true
			}
			for _, s := range staffList {
				personBase := convertStaffToPersonBase(s)
				if personBase == nil {
					continue
				}
				if _, ok := staffMap[personBase.Fid]; !ok || personBase.Fid == "" {
					data.OperInfo = appendDistinctPersonBase(data.OperInfo, personBase)
					data.OperDepartment = append(data.OperDepartment, personBase.Department...)
				}
			}
		case field_tag_rules.SetFieldTag:
			// 更新自定义标签
			data.Tags = append(data.Tags, sourceContent...)
			data.Tags = utils.ListDistinctNonZero(data.Tags)
		}
	}
	return nil
}

// getAssetTagRules 获取资产标签规则，优先从redis获取，如果redis中没有数据或者redis获取失败，从mysql中获取
func getAssetTagRules(field, value string) ([]*field_tag_rules.FieldTagRule, error) {
	// 从redis获取
	key := fmt.Sprintf("%s:%s", field, value)
	redisKey := key_helper.AssetTagRulesKey(key)
	var rules []*field_tag_rules.FieldTagRule
	cmd := redisClient.Get(context.Background(), redisKey)
	var err = cmd.Err()
	var val = cmd.Val()
	if len(val) > 0 && err == nil {
		err = json.Unmarshal([]byte(val), &rules)
	}
	if err != nil && !errors.Is(err, goRedis.Nil) {
		logs.GetLogger("asset").Errorf("获取资产标签规则失败. key: %s,err: %v, val: %s", redisKey, err, val)
		// 如果redis中没有数据或者redis获取失败，从mysql中获取
		rules, err = field_tag_rules.NewFieldTagRuleConfig().Page(0, 0, mysql.WithColumnValue("field", field), mysql.WithColumnValue("value", value))
		if err != nil {
			return nil, err
		}
	}
	return rules, nil
}

// convertStaffToPersonBase 转换人员信息
func convertStaffToPersonBase(staff *staff.Staff) *assets.PersonBase {
	if staff == nil {
		return nil
	}

	// 获取人员部门信息
	var departmentInfo []*assets.DepartmentBase
	for _, departmentId := range staff.DepartmentsIds {
		// string 类型的departmentId转uint64类型
		departmentId, err := strconv.ParseUint(departmentId, 10, 64)
		if err != nil {
			continue
		}
		department := helper.GetDepartment(departmentId, staff.Name, staff.Id, "", "")
		if department != nil {
			departmentInfo = append(departmentInfo, department)
		}
	}

	return &assets.PersonBase{
		Id:   staff.Id,
		Fid:  staff.Fid,
		Name: staff.Name,
		FindInfo: []*assets.PersonFindInfo{
			{
				SourceId:     uint64(7), // 规则的数据源视为7，人工添加
				NodeId:       uint64(0), // 规则的节点视为0，人工添加
				SourceValue:  staff.Id,  // 规则是通过id查询的
				MappingField: "id",
				FindCount:    1,
			},
		},
		Department: departmentInfo,
	}
}

// appendDistinctPersonBase 添加不重复的人员信息，人员重复时，合并findInfo
func appendDistinctPersonBase(personList []*assets.PersonBase, newPerson *assets.PersonBase) []*assets.PersonBase {
	for _, person := range personList {
		if person.Id == newPerson.Id {
			findInfoMap := make(map[string]bool)
			for _, findInfo := range person.FindInfo {
				key := fmt.Sprintf("%v_%v_%v_%v", findInfo.SourceId, findInfo.NodeId, findInfo.SourceValue, findInfo.MappingField)
				findInfoMap[key] = true
			}
			for _, findInfo := range newPerson.FindInfo {
				key := fmt.Sprintf("%v_%v_%v_%v", findInfo.SourceId, findInfo.NodeId, findInfo.SourceValue, findInfo.MappingField)
				if _, ok := findInfoMap[key]; !ok {
					person.FindInfo = append(person.FindInfo, findInfo)
				}
			}
			return personList
		}
	}
	return append(personList, newPerson)
}
