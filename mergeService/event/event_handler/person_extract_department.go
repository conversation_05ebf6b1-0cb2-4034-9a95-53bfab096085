package event_handler

import (
	"fobrain/fobrain/app/repository/personnel_departments"
	"fobrain/models/elastic/staff"
)

// 单条人员融合结束，提取部门数据
func PersonMergeDataEnd_ForExtractDepartment(data *staff.Staff, existData *staff.Staff) error {
	if len(data.Department) > 0 {
		departmentsIds, allDepartmentsIds := personnel_departments.GetOrCreateDepartmentTree(data.Department)
		data.DepartmentsIds = departmentsIds
		data.AllDepartmentsIds = allDepartmentsIds
	}
	return nil
}
