package event_handler

import (
	"context"
	"fobrain/mergeService/model/async_stat"
	"fobrain/models/elastic/device"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestDeviceCountPocNumHandler(t *testing.T) {
	//mockServer := testcommon.NewMockServer()
	//mockServer.Register("/poc/_search", elastic.SearchResult{
	//	TookInMillis: 10,
	//	TimedOut:     false,
	//	Shards: &elastic.ShardsInfo{
	//		Total:      5,
	//		Successful: 5,
	//		Skipped:    0,
	//		Failed:     0,
	//	},
	//	Aggregations: elastic.Aggregations{
	//		"ip_aggs": json.RawMessage(`
	//			{
	//				"buckets": [
	//					{"key": "127.0.0.1", "doc_count": 100},
	//					{"key": "*********", "doc_count": 200}
	//				]
	//			}
	//		`),
	//	},
	//})

	var records = make([]*device.DeviceRecord, 0)
	records = append(records, &device.DeviceRecord{
		Device: &device.Device{
			PrivateIp: []string{"127.0.0.1"},
			PublicIp:  []string{"*********"},
			Id:        "1",
			Area:      []int{1},
		},
		Id:       "1",
		DeviceId: "1",
	})
	patches := gomonkey.NewPatches()
	defer patches.Reset()

	patches.ApplyFunc(
		async_stat.FetchPocAggResultsForIPAreaSet,
		func(_ context.Context, _ map[string]struct{}) ([]async_stat.AggResult, error) {
			return []async_stat.AggResult{
				{IP: "127.0.0.1", Area: 1, VulnCount: 5},
				{IP: "*********", Area: 1, VulnCount: 3},
			}, nil
		},
	)

	err := DeviceCountPocNumHandler(records)
	assert.Nil(t, err)
	assert.Equal(t, int64(8), records[0].Device.PocNum)
}
