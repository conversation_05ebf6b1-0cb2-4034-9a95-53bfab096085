package event_handler

import (
	"testing" // 标准测试库

	"github.com/agiledragon/gomonkey/v2" // Mocking 库
	"github.com/stretchr/testify/assert" // 断言库

	"fobrain/models/elastic/assets"
	"fobrain/models/elastic/helper"
	"fobrain/models/mysql/strategy"
	"fobrain/pkg/utils"
)

// TestAssetDepartmentHandler 使用 t.Run 进行重构，测试 AssetDepartmentHandler 的不同场景
func TestAssetDepartmentHandler(t *testing.T) {
	// 通用设置
	strategies := []*strategy.Strategy{}
	sourceData := []*assets.ProcessAssets{}
	latestData := map[string]*assets.ProcessAssets{}

	// 定义测试用例结构体
	testCases := []struct {
		name                    string                                        // 测试用例名称
		assetInput              *assets.Assets                                // 输入的资产数据
		mockGetPersonInfo       func(t *testing.T, patches *gomonkey.Patches) // Mock elastic.GetPersonInfoByList 的函数
		expectedErr             error                                         // 预期错误
		expectedOperInfo        []*assets.PersonBase                          // 预期的 OperInfo
		expectedOperDepartment  []*assets.DepartmentBase                      // 预期的 OperDepartment
		expectedPersonLimit     []string                                      // 预期的 PersonLimit
		expectedPersonLimitHash []string                                      // 预期的 PersonLimitHash
	}{
		{
			name: "场景1: 正常获取人员信息并更新资产",
			assetInput: &assets.Assets{
				OperWithMapping: []*assets.PersonWithMapping{
					{MappingField: "id", SourceValue: "123", SourceId: 1},
					{MappingField: "name", SourceValue: "张三", SourceId: 2}, // handler 可能只使用 id
				},
			},
			mockGetPersonInfo: func(t *testing.T, patches *gomonkey.Patches) {
				expectedPersons := []*assets.PersonBase{{Fid: "123", Name: "张三"}}
				expectedDepartments := []*assets.DepartmentBase{{Id: 1001, Name: "技术部"}}
				patches.ApplyFunc(
					helper.GetPersonInfoByList,
					func(mapOper map[string][]*assets.PersonWithMapping) ([]*assets.PersonBase, []*assets.DepartmentBase, error) {
						assert.NotNil(t, mapOper, "传递给 GetPersonInfoByList 的 mapOper 不应为 nil")
						assert.Contains(t, mapOper, "id", "mapOper 应包含 'id' 键")
						assert.Len(t, mapOper["id"], 1, "mapOper['id'] 应包含一个元素")
						assert.Equal(t, "123", mapOper["id"][0].SourceValue, "SourceValue 应为 '123'")
						return expectedPersons, expectedDepartments, nil
					},
				)
			},
			expectedErr: nil,
			expectedOperInfo: []*assets.PersonBase{
				{Fid: "123", Name: "张三"},
			},
			expectedOperDepartment: []*assets.DepartmentBase{
				{Id: 1001, Name: "技术部"},
			},
			expectedPersonLimit:     []string{"123"},
			expectedPersonLimitHash: []string{utils.Md5Hash("123")},
		},
	}

	// 遍历并执行测试用例
	for _, tc := range testCases {
		tc := tc // 捕获 range 变量，防止闭包问题
		t.Run(tc.name, func(t *testing.T) {
			// 注意: gomonkey v2 在并行测试中可能存在并发问题
			// 如果测试不稳定，请考虑移除并行或调整 mock 策略
			// t.Parallel()

			patches := gomonkey.NewPatches()
			// 使用 t.Cleanup 确保每个子测试结束后重置 patches
			t.Cleanup(patches.Reset)

			// 应用当前测试用例的 mock
			if tc.mockGetPersonInfo != nil {
				tc.mockGetPersonInfo(t, patches)
			}

			// 复制输入数据，防止子测试间互相干扰
			// 注意：如果 AssetDepartmentHandler 修改了 assetInput 的深层结构，可能需要深拷贝
			currentAsset := &assets.Assets{}
			if tc.assetInput != nil {
				*currentAsset = *tc.assetInput                                                                            // 浅拷贝结构体本身
				currentAsset.OperWithMapping = append([]*assets.PersonWithMapping(nil), tc.assetInput.OperWithMapping...) // 拷贝切片
				// 显式初始化其他切片，以防 handler 内部或断言时出现 nil panic
				currentAsset.OperInfo = []*assets.PersonBase{}
				currentAsset.OperDepartment = []*assets.DepartmentBase{}
				currentAsset.PersonLimit = []string{}
				currentAsset.PersonLimitHash = []string{}
			}

			// 执行被测试的函数
			err := AssetDepartmentHandler(currentAsset, strategies, sourceData, latestData)

			// 断言结果
			if tc.expectedErr != nil {
				// 预期有错误
				assert.Error(t, err, "预期应该发生错误")
				assert.EqualError(t, err, tc.expectedErr.Error(), "错误信息应匹配")
				// 在错误情况下，检查相关字段是否已初始化但为空 (根据原始测试逻辑)
				assert.NotNil(t, currentAsset.OperInfo, "错误情况下 OperInfo 应已初始化")
				assert.Empty(t, currentAsset.OperInfo, "错误情况下 OperInfo 应为空")
				assert.NotNil(t, currentAsset.OperDepartment, "错误情况下 OperDepartment 应已初始化")
				assert.Empty(t, currentAsset.OperDepartment, "错误情况下 OperDepartment 应为空")
				assert.NotNil(t, currentAsset.PersonLimit, "错误情况下 PersonLimit 应已初始化")
				assert.Empty(t, currentAsset.PersonLimit, "错误情况下 PersonLimit 应为空")
				assert.NotNil(t, currentAsset.PersonLimitHash, "错误情况下 PersonLimitHash 应已初始化")
				assert.Empty(t, currentAsset.PersonLimitHash, "错误情况下 PersonLimitHash 应为空")
			} else {
				// 预期没有错误
				assert.NoError(t, err, "预期不应发生错误")
				// 检查结果字段是否已初始化 (handler 内部逻辑应保证)
				assert.NotNil(t, currentAsset.OperInfo, "OperInfo 不应为 nil")
				assert.NotNil(t, currentAsset.OperDepartment, "OperDepartment 不应为 nil")
				assert.NotNil(t, currentAsset.PersonLimit, "PersonLimit 不应为 nil")
				assert.NotNil(t, currentAsset.PersonLimitHash, "PersonLimitHash 不应为 nil")

				// 使用 assert.Equal 比较切片内容和顺序
				// 如果顺序不重要，可以使用 assert.ElementsMatch
				assert.Equal(t, tc.expectedOperInfo, currentAsset.OperInfo, "OperInfo 内容应匹配")
				assert.Equal(t, tc.expectedOperDepartment, currentAsset.OperDepartment, "OperDepartment 内容应匹配")
				assert.Equal(t, tc.expectedPersonLimit, currentAsset.PersonLimit, "PersonLimit 内容应匹配")
				assert.Equal(t, tc.expectedPersonLimitHash, currentAsset.PersonLimitHash, "PersonLimitHash 内容应匹配")
			}
		})
	}
}

func TestGetPersonFid(t *testing.T) {
	t.Run("正常获取Fid", func(t *testing.T) {
		personList := []*assets.PersonBase{
			{Fid: "123"},
			{Fid: "456"},
		}
		fidList, fidHashList := getPersonFid(personList)
		assert.ElementsMatch(t, []string{"123", "456"}, fidList)
		assert.ElementsMatch(t, []string{utils.Md5Hash("123"), utils.Md5Hash("456")}, fidHashList)
	})
	t.Run("处理空列表", func(t *testing.T) {
		personList := []*assets.PersonBase{}
		fidList, fidHashList := getPersonFid(personList)
		assert.Empty(t, fidList)
		assert.Empty(t, fidHashList)
	})
}
