package event_handler

import (
	logs "fobrain/mergeService/utils/log"
	"fobrain/models/elastic/assets"
	"fobrain/models/elastic/device"
	"fobrain/pkg/utils"
	"strconv"

	es "fobrain/initialize/es"

	"github.com/olivere/elastic/v7"
)

// DeviceAddRelatedSource 添加设备相关资产来源ID
func DeviceAddRelatedSource(device *device.Device, existData *device.Device) error {
	logger := logs.GetLogger("device")
	// 获取设备IP
	ipList := append(device.PrivateIp, device.PublicIp...)
	ipList = utils.ListDistinctNonZero(ipList)
	// 根据设备IP查找资产
	query := elastic.NewBoolQuery().Must(elastic.NewTermsQueryFromStrings("ip", ipList...))
	if len(device.Area) > 0 {
		areaStr := make([]string, 0)
		for _, a := range device.Area {
			areaStr = append(areaStr, strconv.Itoa(a))
		}
		query.Must(elastic.NewTermsQueryFromStrings("area", areaStr...))
	}
	allAssets, err := es.All[assets.Assets](100, query, nil, "all_source_ids")
	if err != nil {
		logger.Errorf("根据设备IP查找资产失败: %v", err)
		return err
	}
	allSourceIds := make([]uint64, 0)
	// 获取资产的来源ID
	for _, asset := range allAssets {
		allSourceIds = append(allSourceIds, asset.AllSourceIds...)
	}
	// 将资产的来源ID添加到设备中
	device.AllSourceIds = append(device.AllSourceIds, allSourceIds...)
	// 去重
	device.AllSourceIds = utils.ListDistinctNonZero(device.AllSourceIds)
	return nil
}
