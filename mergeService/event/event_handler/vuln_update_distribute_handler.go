package event_handler

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"fobrain/fobrain/app/repository/threat_history"
	"fobrain/initialize/redis"
	logs "fobrain/mergeService/utils/log"
	"fobrain/models/elastic/poc"
	"fobrain/models/elastic/staff"
	"fobrain/models/mysql/user"
	"fobrain/services/threat_center"
)

func VulUpdateDistributeHandler(data *poc.Poc) error {
	logger := logs.GetLogger("vuln")
	time.Sleep(20 * time.Millisecond) // 防止redis没有写进来数据
	result, err := redis.GetRedisClient().Get(context.Background(), fmt.Sprintf("cache:%s", data.Id)).Result()

	if err != nil && err.Error() != "redis: nil" {
		return err
	}
	if err != nil && err.Error() == "redis: nil" {
		return nil
	}
	var strategiesCache map[string]interface{}
	err = json.Unmarshal([]byte(result), &strategiesCache)
	if err != nil {
		return err
	}

	userJsonData, err := json.Marshal(strategiesCache["user"])
	if err != nil {
		logger.Errorf("user json Marshal error:%s", err)
		return err
	}

	var u *user.User
	err = json.Unmarshal(userJsonData, &u)
	if err != nil {
		logger.Errorf("user json Unmarshal error:%s", err)
		return err
	}

	staffJsonData, err := json.Marshal(strategiesCache["staff"])
	if err != nil {
		logger.Errorf("staff json Marshal error:%s", err)
		return err
	}

	var staff *staff.Staff
	err = json.Unmarshal(staffJsonData, &staff)
	if err != nil {
		logger.Errorf("staff json Unmarshal error:%s", err)
		return err
	}
	var pocObj map[string]interface{}
	pocJsonData, err := json.Marshal(data)
	if err != nil {
		logger.Errorf("poc json Marshal error:%s", err)
		return err
	}
	err = json.Unmarshal(pocJsonData, &pocObj)
	if err != nil {
		logger.Errorf("poc json Unmarshal error:%s", err)
		return err
	}
	tostaffid, _ := strategiesCache["to_staff_id"].(string)
	oneThreatHistory := threat_history.OneThreatHistory{
		PocId:       data.Id,
		ToStaffId:   tostaffid,
		ToStaffName: "",
		Status:      poc.PocStatusOfForward,
		LimitDate:   "",
	}
	pocObj["statusCode"] = data.Status
	err = threat_center.Distribute(u, staff, pocObj, &oneThreatHistory)
	if err != nil {
		return err
	}

	redis.GetRedisClient().Del(context.Background(), fmt.Sprintf("cache:%s", data.Id))
	return nil
}
