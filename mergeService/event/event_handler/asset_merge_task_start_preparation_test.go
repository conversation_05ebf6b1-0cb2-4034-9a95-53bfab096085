package event_handler

import (
	testcommon "fobrain/fobrain/tests/common_test"
	"testing"

	"github.com/alicebob/miniredis/v2"
	goRedis "github.com/go-redis/redis/v8"
	"github.com/olivere/elastic/v7"

	"github.com/stretchr/testify/assert"
)

func TestCacheAllBusinessSystems(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	mockServer.Register("/business_systems/_search", []*elastic.SearchHit{
		{
			Id: "1",
			Source: []byte(`{
				  "business_name": "1"
			}`),
		},
		{
			Id: "2",
			Source: []byte(`{
				  "business_name": "2"
			}`),
		},
	})
	mockServer.Register("/_search/scroll", []*elastic.SearchHit{})

	err := cacheBusiness()
	assert.NoError(t, err)
}
func TestCacheAllStaffs(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	// 设置 miniredis
	s, err := miniredis.Run()
	if err != nil {
		t.Fatalf("无法启动 miniredis: %v", err)
	}
	defer s.Close()

	// 创建 Redis 客户端
	cli := goRedis.NewClient(&goRedis.Options{
		Addr: s.Addr(),
	})
	testcommon.SetRedisClient(cli)

	mockServer.Register("/staff/_search", []*elastic.SearchHit{
		{
			Id: "1",
			Source: []byte(`{
				  "fid": "1"
			}`),
		},
		{
			Id: "2",
			Source: []byte(`{
				  "fid": "2"
			}`),
		},
	})
	mockServer.Register("/_search/scroll", []*elastic.SearchHit{})

	err = cacheStaffs()
	assert.NoError(t, err)
}
func TestCacheAssets(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	// 设置 miniredis
	s, err := miniredis.Run()
	if err != nil {
		t.Fatalf("无法启动 miniredis: %v", err)
	}
	defer s.Close()

	// 创建 Redis 客户端
	cli := goRedis.NewClient(&goRedis.Options{
		Addr: s.Addr(),
	})
	testcommon.SetRedisClient(cli)

	mockServer.Register("/asset/_search", []*elastic.SearchHit{
		{
			Id: "1",
			Source: []byte(`{
				  "fid": "1"
			}`),
		},
		{
			Id: "2",
			Source: []byte(`{
				  "fid": "2"
			}`),
		},
	})
	mockServer.Register("/_search/scroll", []*elastic.SearchHit{})

	err = cacheAssets()
	assert.NoError(t, err)
}
