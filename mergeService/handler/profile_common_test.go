package handler

import (
	"context"
	"encoding/json"
	"errors"
	"fobrain/fobrain/common/localtime"
	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/initialize/es"
	"fobrain/initialize/mysql"
	pb "fobrain/mergeService/proto"
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/network_areas"
	"reflect"
	"testing"
	"time"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/alicebob/miniredis/v2"
	redis2 "github.com/go-redis/redis/v8"
	"github.com/olivere/elastic/v7"
	"github.com/stretchr/testify/assert"

	"fobrain/models/elastic/assets"
	"fobrain/models/elastic/source/bk_cmdb"
	"fobrain/models/elastic/source/file_import"
	"fobrain/models/elastic/source/foeye"
	"fobrain/models/elastic/source/foradar"
	"fobrain/models/elastic/source/qt_cloud"

	. "github.com/smartystreets/goconvey/convey"
)

func TestGetAllSources(t *testing.T) {
	// Setup mock redis
	s, err := miniredis.Run()
	if err != nil {
		t.Fatal(err)
	}
	defer s.Close()
	client := redis2.NewClient(&redis2.Options{Addr: s.Addr()})
	testcommon.SetRedisClient(client)

	ds := &data_source.Source{
		BaseModel: mysql.BaseModel{
			Id: 1,
		},
		Name: "test",
		Icon: "test",
	}
	dsList := []*data_source.Source{ds}
	redisKey := "data:source:all"

	// 正常情况
	// Mock the data_source.NewSourceModel() Items method
	datasourceItemsMock := gomonkey.ApplyMethodReturn(data_source.NewSourceModel(), "Items", dsList, int64(1), nil)

	data, err := getAllSources()
	assert.Nil(t, err)
	assert.Equal(t, 1, len(data))
	assert.Equal(t, ds.Id, data[0].Id)

	redisResult, _ := client.Get(context.Background(), redisKey).Result()
	assert.NotNil(t, redisResult)
	var sources []*data_source.Source
	json.Unmarshal([]byte(redisResult), &sources)
	assert.Equal(t, 1, len(sources))
	assert.Equal(t, ds.Id, sources[0].Id)

	// 测试redis缓存命中
	client.Set(context.Background(), redisKey, `[{"id":1,"name":"test","icon":"test"}]`, 0)
	data, err = getAllSources()
	assert.Nil(t, err)
	assert.Equal(t, 1, len(data))
	assert.Equal(t, ds.Id, data[0].Id)

	// 测试redis缓存失效
	client.Del(context.Background(), redisKey)
	data, err = getAllSources()
	assert.Nil(t, err)
	assert.Equal(t, 1, len(data))
	assert.Equal(t, ds.Id, data[0].Id)

	// 重置mock
	datasourceItemsMock.Reset()

	// 测试db错误
	// clear redis cache
	client.Del(context.Background(), redisKey)

	// Mock the data_source.NewSourceModel() Items method
	datasourceItemsMock = gomonkey.ApplyMethodReturn(data_source.NewSourceModel(), "Items", nil, int64(0), errors.New("db error"))
	defer datasourceItemsMock.Reset()

	data, err = getAllSources()
	assert.NotNil(t, err)
	assert.Nil(t, data)
}

func TestGetSourceIdByIndexName(t *testing.T) {
	tests := []struct {
		indexName      string
		expectedSource uint64
		expectedList   []map[string]string
		expectedDetail []map[string]string
	}{
		{"foeye_task_assets", 1, foeye.NewFoeyeTaskAssetsModel().GetFoeyeAssetListFields(), foeye.NewFoeyeTaskAssetsModel().GetFoeyeAssetListDetailFields()},
		{"foradar_task_assets", 2, foradar.NewForadarTaskAssetsModel().GetAssetListFields(), foradar.NewForadarTaskAssetsModel().GetAssetListDetailFields()},
		{"qt_cloud_task_assets", 3, qt_cloud.NewQTCloudTaskAssetsModel().GetQTCloudAssetListFields(), qt_cloud.NewQTCloudTaskAssetsModel().GetQTCloudAssetListDetailFields()},
		{"bk_cmdb_task_assets", 5, bk_cmdb.NewBKCmdbTaskAssetsModel().GetAssetListFields(), bk_cmdb.NewBKCmdbTaskAssetsModel().GetAssetListDetailFields()},
		{"file_import_task_assets", 6, file_import.NewFileImportTaskAssetsModel().GetAssetListFields(), file_import.NewFileImportTaskAssetsModel().GetAssetListDetailFields()},
		{"invalid_index", 0, nil, nil},
		{"invalid_index_task_assets", 8, nil, nil},
		{"", 0, nil, nil},
	}

	for _, tt := range tests {
		source, list, detail := getSourceIdByIndexName(tt.indexName)
		assert.Equal(t, tt.expectedSource, source, "Source mismatch for indexName %s", tt.indexName)
		assert.Equal(t, tt.expectedList, list, "List mismatch for indexName %s", tt.indexName)
		assert.Equal(t, tt.expectedDetail, detail, "Detail mismatch for indexName %s", tt.indexName)
	}
}

func TestGetSourceFromSourceListById(t *testing.T) {
	// Test cases
	tests := []struct {
		name       string
		sourceId   uint64
		sourceList []*data_source.Source
		expected   *pb.Source
	}{
		{
			name:     "Happy Path - Source Found",
			sourceId: 1,
			sourceList: []*data_source.Source{
				{BaseModel: mysql.BaseModel{Id: uint64(1)}, Name: "Source1", Icon: "icon1"},
				{BaseModel: mysql.BaseModel{Id: uint64(2)}, Name: "Source2", Icon: "icon2"},
			},
			expected: &pb.Source{Id: 1, Name: "Source1", Icon: "icon1"},
		},
		{
			name:     "Edge Case - Source Not Found",
			sourceId: 3,
			sourceList: []*data_source.Source{
				{BaseModel: mysql.BaseModel{Id: uint64(1)}, Name: "Source1", Icon: "icon1"},
				{BaseModel: mysql.BaseModel{Id: uint64(2)}, Name: "Source2", Icon: "icon2"},
			},
			expected: nil,
		},
		{
			name:       "Edge Case - Empty Source List",
			sourceId:   1,
			sourceList: []*data_source.Source{},
			expected:   nil,
		},
		{
			name:       "Edge Case - Nil Source List",
			sourceId:   1,
			sourceList: nil,
			expected:   nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := getSourceFromSourceListById(tt.sourceId, tt.sourceList)
			if result == nil && tt.expected == nil {
				return
			}
			if result == nil || tt.expected == nil || result.Id != tt.expected.Id || result.Name != tt.expected.Name || result.Icon != tt.expected.Icon {
				t.Errorf("Expected %v, got %v", tt.expected, result)
			}
		})
	}
}

func TestGetAllArea(t *testing.T) {
	Convey("test getAllArea", t, func() {
		// Setup mock redis
		s, err := miniredis.Run()
		if err != nil {
			t.Fatal(err)
		}
		defer s.Close()
		client := redis2.NewClient(&redis2.Options{Addr: s.Addr()})
		testcommon.SetRedisClient(client)

		area := &network_areas.NetworkArea{
			BaseModel: mysql.BaseModel{
				Id: 1,
			},
			Name: "test",
		}
		areaList := []*network_areas.NetworkArea{area}
		redisKey := "data:area:all"

		Convey("Happy path", func() {
			// Mock the data_source.NewSourceModel() Items method
			allAreaMock := gomonkey.ApplyFuncReturn(network_areas.AllNetworkAreas, areaList, int64(1), nil)
			defer allAreaMock.Reset()

			data, err := getAllArea()
			assert.Nil(t, err)
			assert.Equal(t, 1, len(data))
			assert.Equal(t, area.Id, data[0].Id)

			redisResult, _ := client.Get(context.Background(), redisKey).Result()
			assert.NotNil(t, redisResult)
			var sources []*network_areas.NetworkArea
			json.Unmarshal([]byte(redisResult), &sources)
			assert.Equal(t, 1, len(sources))
			assert.Equal(t, area.Id, sources[0].Id)

			Convey("redis cache hit", func() {
				client.Set(context.Background(), redisKey, `[{"id":1,"name":"test"}]`, 0)
				data, err := getAllArea()
				assert.Nil(t, err)
				assert.Equal(t, 1, len(data))
				assert.Equal(t, area.Id, data[0].Id)
			})

			Convey("redis cache miss", func() {
				client.Del(context.Background(), redisKey)
				data, err := getAllArea()
				assert.Nil(t, err)
				assert.Equal(t, 1, len(data))
				assert.Equal(t, area.Id, data[0].Id)
			})
		})

		Convey("db error", func() {
			// clear redis cache
			client.Del(context.Background(), redisKey)

			// Mock the data_source.NewSourceModel() Items method
			allAreaMock := gomonkey.ApplyFuncReturn(network_areas.AllNetworkAreas, nil, int64(1), errors.New("db error"))
			defer allAreaMock.Reset()

			data, err := getAllArea()
			assert.NotNil(t, err)
			assert.Nil(t, data)
		})
	})
}

func TestGetAreaFromAreaListById(t *testing.T) {
	// Test cases
	tests := []struct {
		name     string
		areaId   uint64
		areaList []*network_areas.NetworkArea
		expected *pb.Area
	}{
		{
			name:   "Happy Path - Area Found",
			areaId: 1,
			areaList: []*network_areas.NetworkArea{
				{BaseModel: mysql.BaseModel{Id: uint64(1)}, Name: "Area1"},
				{BaseModel: mysql.BaseModel{Id: uint64(2)}, Name: "Area2"},
			},
			expected: &pb.Area{Id: 1, Name: "Area1"},
		},
		{
			name:   "Edge Case - Area Not Found",
			areaId: 3,
			areaList: []*network_areas.NetworkArea{
				{BaseModel: mysql.BaseModel{Id: uint64(1)}, Name: "Area1"},
				{BaseModel: mysql.BaseModel{Id: uint64(2)}, Name: "Area2"},
			},
			expected: nil,
		},
		{
			name:     "Edge Case - Empty Area List",
			areaId:   1,
			areaList: []*network_areas.NetworkArea{},
			expected: nil,
		},
		{
			name:   "Edge Case - Duplicate Areas",
			areaId: 2,
			areaList: []*network_areas.NetworkArea{
				{BaseModel: mysql.BaseModel{Id: uint64(1)}, Name: "Area1"},
				{BaseModel: mysql.BaseModel{Id: uint64(2)}, Name: "Area2"},
				{BaseModel: mysql.BaseModel{Id: uint64(2)}, Name: "Area2Duplicate"},
			},
			expected: &pb.Area{Id: 2, Name: "Area2"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := getAreaFromAreaListById(tt.areaId, tt.areaList)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestGetAssetList(t *testing.T) {
	esGetCountMock := gomonkey.ApplyFuncReturn(es.GetCount, int64(1), nil)
	defer esGetCountMock.Reset()

	getAllSourcesMock := gomonkey.ApplyFuncReturn(getAllSources, []*data_source.Source{}, nil)
	defer getAllSourcesMock.Reset()

	getAllAreaMock := gomonkey.ApplyFuncReturn(getAllArea, []*network_areas.NetworkArea{}, nil)
	defer getAllAreaMock.Reset()

	// Mock es.List
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	mockServer.Register("asset/_search", []*elastic.SearchHit{
		{
			Id:     "1",
			Source: []byte(`{"id": "1", "host_name": ["hostname1"], "sn": ["sn1"], "mac": ["mac1"], "ip": "***********", "machine_room": ["machineRoom1"], "os": ["os1"], "created_at": "2021-11-11 11:11:11", "updated_at": "2021-11-11 11:11:11", "source_ids": [1], "area": 1}`),
		},
	})

	Convey("Happy Path", t, func() {
		ipList := []string{"***********"}
		data, count, err := getAssetList(1, 10, ipList, []int{})
		assert.Nil(t, err)
		assert.Equal(t, int64(1), count)
		assert.Len(t, data, 1)
		assert.Equal(t, "1", data[0].Id)
	})

	Convey("count empty", t, func() {
		esGetCountMock = gomonkey.ApplyFuncReturn(es.GetCount, int64(0), nil)
		defer esGetCountMock.Reset()

		data, count, err := getAssetList(1, 10, []string{}, []int{})
		assert.Nil(t, err)
		assert.Len(t, data, 0)
		assert.Equal(t, int64(0), count)

	})
}

func TestGetAssetList_CountError(t *testing.T) {
	esGetCountMock := gomonkey.ApplyFuncReturn(es.GetCount, int64(1), nil)
	defer esGetCountMock.Reset()

	getAllSourcesMock := gomonkey.ApplyFuncReturn(getAllSources, []*data_source.Source{}, nil)
	defer getAllSourcesMock.Reset()

	getAllAreaMock := gomonkey.ApplyFuncReturn(getAllArea, []*network_areas.NetworkArea{}, nil)
	defer getAllAreaMock.Reset()

	// Mock es.List
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	mockServer.Register("asset/_search", []*elastic.SearchHit{
		{
			Id:     "1",
			Source: []byte(`{"id": "1", "host_name": ["hostname1"], "sn": ["sn1"], "mac": ["mac1"], "ip": "***********", "machine_room": ["machineRoom1"], "os": ["os1"], "created_at": "2021-11-11 11:11:11", "updated_at": "2021-11-11 11:11:11", "source_ids": [1], "area": 1}`),
		},
	})
	esGetCountMock = gomonkey.ApplyFuncReturn(es.GetCount, int64(0), errors.New("count error"))
	defer esGetCountMock.Reset()

	ipList := []string{"***********"}
	data, count, err := getAssetList(1, 10, ipList, []int{})
	assert.NotNil(t, err)
	assert.Len(t, data, 0)
	assert.Equal(t, int64(0), count)
}

func TestSortProcess(t *testing.T) {
	// 定义测试用例
	tests := []struct {
		name     string
		process  []*pb.TraceabilityProcessInfoResponseItem
		sortType string
		expected []*pb.TraceabilityProcessInfoResponseItem
	}{
		{
			name: "asc sort",
			process: []*pb.TraceabilityProcessInfoResponseItem{
				{Time: "2023-05-01"},
				{Time: "2023-05-02"},
				{Time: "2023-05-03"},
			},
			sortType: "asc",
			expected: []*pb.TraceabilityProcessInfoResponseItem{
				{Time: "2023-05-01"},
				{Time: "2023-05-02"},
				{Time: "2023-05-03"},
			},
		},
		{
			name: "desc sort",
			process: []*pb.TraceabilityProcessInfoResponseItem{
				{Time: "2023-05-03"},
				{Time: "2023-05-02"},
				{Time: "2023-05-01"},
			},
			sortType: "desc",
			expected: []*pb.TraceabilityProcessInfoResponseItem{
				{Time: "2023-05-03"},
				{Time: "2023-05-02"},
				{Time: "2023-05-01"},
			},
		},
	}

	// 执行测试
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			sortProcess(tt.process, tt.sortType)
			assert.True(t, reflect.DeepEqual(tt.process, tt.expected), "sortProcess result does not match expected")
		})
	}
}

func TestConvertToPbAsset(t *testing.T) {
	allSourceList := []*data_source.Source{
		{BaseModel: mysql.BaseModel{Id: uint64(1)}, Name: "Source1", Icon: "icon1"},
	}
	allAreaList := []*network_areas.NetworkArea{
		{BaseModel: mysql.BaseModel{Id: uint64(1)}, Name: "Area1"},
	}
	tests := []struct {
		name     string
		asset    *assets.Assets
		expected *pb.Asset
	}{
		{
			name: "happy path",
			asset: &assets.Assets{
				Id:           "1",
				CreatedAt:    localtime.NewLocalTime(time.Date(2021, 11, 11, 11, 11, 11, 0, time.Local)),
				UpdatedAt:    localtime.NewLocalTime(time.Date(2021, 11, 11, 11, 11, 11, 0, time.Local)),
				DeletedAt:    localtime.NewLocalTime(time.Date(2021, 11, 11, 11, 11, 11, 0, time.Local)),
				PurgedAt:     localtime.NewLocalTime(time.Date(2021, 11, 11, 11, 11, 11, 0, time.Local)),
				MergeCount:   1,
				Area:         1,
				ProcessIds:   []string{"1"},
				SourceIds:    []uint64{1},
				NodeIds:      []uint64{2},
				TaskDataIds:  []string{"1"},
				AllSourceIds: []uint64{1},
				AllNodeIds:   []uint64{2},
				HostName:     []string{"hostname1"},
				EthName:      []string{"eth1"},
				Os:           []string{"os1"},
				Kernel:       []string{"kernel1"},
				Model:        []string{"model1"},
				Maker:        []string{"maker1"},
				Sn:           []string{"sn1"},
				Mac:          []string{"mac1"},
				Product:      []string{"product1"},
				RuleInfos: []*assets.RuleInfo{
					{
						Level:     "level1",
						Product:   "product1",
						FirstTag:  "firstTag1",
						SecondTag: "secondTag1",
					},
				},
				Business: []*assets.Business{
					{
						SystemId: "1",
						System:   "business1",
						PersonBase: []*assets.PersonBase{
							{
								Name: "oper1",
								Id:   "1",
								Fid:  "1234567890",
								FindInfo: []*assets.PersonFindInfo{
									{SourceId: 1, NodeId: 2, SourceValue: "oper1", FindCount: 1},
								},
								Department: []*assets.DepartmentBase{
									{
										Id:      1,
										Name:    "department1",
										Parents: []*assets.DepartmentBase{},
									},
								},
							},
						},
					},
				},
				OperInfo: []*assets.PersonBase{
					{
						Name: "oper1",
						Id:   "1",
						Fid:  "1234567890",
						FindInfo: []*assets.PersonFindInfo{
							{SourceId: 1, NodeId: 2, SourceValue: "oper1", FindCount: 1},
						},
						Department: []*assets.DepartmentBase{
							{
								Id:      1,
								Name:    "department1",
								Parents: []*assets.DepartmentBase{},
							},
						},
					},
				},
				MachineRoom:           []string{"machineRoom1"},
				Tags:                  []string{"tag1"},
				MachineRoomSource:     map[string]string{"1": "machineRoom1"},
				TagsSource:            map[string]string{"1": "tag1"},
				MemorySize:            []string{"1024"},
				MemorySizeSource:      map[string]string{"1": "1024"},
				MemoryUsageRate:       []string{"50"},
				MemoryUsageRateSource: map[string]string{"1": "50"},
				CpuMaker:              []string{"cpuMaker1"},
				CpuMakerSource:        map[string]string{"1": "cpuMaker1"},
				CpuBrand:              []string{"cpuBrand1"},
				CpuBrandSource:        map[string]string{"1": "cpuBrand1"},
				CpuCount:              []int{1},
				CpuCountSource:        map[string]int{"1": 1},
				DiskCount:             []int{1},
				DiskCountSource:       map[string]int{"1": 1},
				DiskSize:              []int{1024},
				DiskSizeSource:        map[string]int{"1": 1024},
				DiskUsageRate:         []string{"50"},
				DiskUsageRateSource:   map[string]string{"1": "50"},
				LoadAverage:           []string{"0.5"},
				LoadAverageSource:     map[string]string{"1": "0.5"},
				NetworkType:           1,
			},
			expected: &pb.Asset{
				Id:         "1",
				CreatedAt:  "2021-11-11 11:11:11",
				UpdatedAt:  "2021-11-11 11:11:11",
				DeletedAt:  "2021-11-11 11:11:11",
				PurgedAt:   "2021-11-11 11:11:11",
				MergeCount: 1,
				AreaId:     1,
				Area: &pb.Area{
					Id:   1,
					Name: "Area1",
				},
				ProcessIds:      []string{"1"},
				SourceIds:       []uint64{1},
				NodeIds:         []uint64{2},
				TaskDataIds:     []string{"1"},
				AllSourceIds:    []uint64{1},
				AllNodeIds:      []uint64{2},
				AllTaskDataIds:  []string{"1"},
				AllProcessIds:   []string{"1"},
				Ip:              "***********",
				IpType:          1,
				IpTypeText:      "ipType1",
				IpSegment:       []string{"***********"},
				IpSegmentSource: map[string]string{"1": "***********"},
				HostName:        []string{"hostname1"},
				HostNameSource:  map[string]string{"1": "hostname1"},
				EthName:         []string{"eth1"},
				EthNameSource:   map[string]string{"1": "eth1"},
				Os:              []string{"os1"},
				OsSource:        map[string]string{"1": "os1"},
				Kernel:          []string{"kernel1"},
				KernelSource:    map[string]string{"1": "kernel1"},
				Model:           []string{"model1"},
				ModelSource:     map[string]string{"1": "model1"},
				Maker:           []string{"maker1"},
				MakerSource:     map[string]string{"1": "maker1"},
				Sn:              []string{"sn1"},
				SnSource:        map[string]string{"1": "sn1"},
				Mac:             []string{"mac1"},
				MacSource:       map[string]string{"1": "mac1"},
				Product:         []string{"product1"},
				ProductSource:   map[string]string{"1": "product1"},
				RuleInfos: []*pb.RuleInfo{
					{
						Level:     "level1",
						Product:   "product1",
						FirstTag:  "firstTag1",
						SecondTag: "secondTag1",
					},
				},
				Business: []*pb.IpAdminInfoResponseItem{
					{
						System:   "business1",
						SystemId: "1",
						PersonBase: []*pb.PersonBase{
							{
								Name: "oper1",
								Id:   "1",
								Fid:  "1234567890",
								FindInfo: []*pb.PersonFindInfo{
									{
										SourceId:    1,
										NodeId:      2,
										SourceValue: "oper1",
										FindCount:   1,
									},
								},
								Department: []*pb.DepartmentBase{
									{
										Id:      1,
										Name:    "department1",
										Parents: []*pb.DepartmentBase{},
									},
								},
							},
						},
					},
				},
				Oper: []*pb.PersonBase{
					{
						Name: "oper1",
						Id:   "1",
						Fid:  "1234567890",
						FindInfo: []*pb.PersonFindInfo{
							{
								SourceId:    1,
								NodeId:      2,
								SourceValue: "oper1",
								FindCount:   1,
							},
						},
						Department: []*pb.DepartmentBase{
							{
								Id:      1,
								Name:    "department1",
								Parents: []*pb.DepartmentBase{},
							},
						},
					},
				},
				OperSource:        map[string]string{"1": "oper1"},
				MachineRoom:       []string{"machineRoom1"},
				MachineRoomSource: map[string]string{"1": "machineRoom1"},
				Tags:              []string{"tag1"},
				Status:            1,
				StatusSource:      map[string]int32{"1": 1},
				Ports: []*pb.PortInfo{
					{
						Port:     22,
						Protocol: "tcp",
						Url:      "ssh",
						Status:   1,
						Title:    "",
					},
				},
				PortsSource: map[string]*pb.ListPortInfo{"1": {
					Ports: []*pb.PortInfo{
						{
							Port:     22,
							Protocol: "tcp",
							Url:      "ssh",
							Status:   1,
							Title:    "",
						},
					},
				}},
				MemorySize:            []string{"1024"},
				MemorySizeSource:      map[string]string{"1": "1024"},
				MemoryUsageRate:       []string{"50"},
				MemoryUsageRateSource: map[string]string{"1": "50"},
				CpuMaker:              []string{"cpuMaker1"},
				CpuMakerSource:        map[string]string{"1": "cpuMaker1"},
				CpuBrand:              []string{"cpuBrand1"},
				CpuBrandSource:        map[string]string{"1": "cpuBrand1"},
				CpuCount:              []int32{1},
				CpuCountSource:        map[string]int32{"1": 1},
				DiskCount:             []int32{1},
				DiskCountSource:       map[string]int32{"1": 1},
				DiskSize:              []int32{1024},
				DiskSizeSource:        map[string]int32{"1": 1024},
				DiskUsageRate:         []string{"50"},
				DiskUsageRateSource:   map[string]string{"1": "50"},
				LoadAverage:           []string{"0.5"},
				LoadAverageSource:     map[string]string{"1": "0.5"},
				NetworkType:           1,
				NetworkTypeText:       "networkType1",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := convertToPbAsset(tt.asset, allSourceList, allAreaList)
			assert.Equal(t, tt.expected.MachineRoom, result.MachineRoom)
			assert.Equal(t, tt.expected.Tags, result.Tags)
			assert.Equal(t, tt.expected.MachineRoomSource, result.MachineRoomSource)
			assert.Equal(t, tt.expected.MemorySize, result.MemorySize)
			assert.Equal(t, tt.expected.MemorySizeSource, result.MemorySizeSource)
			assert.Equal(t, tt.expected.Business, result.Business)
			assert.Equal(t, tt.expected.Oper, result.Oper)
		})
	}
}

func TestConvertToPbBusinessSystem(t *testing.T) {
	tests := []struct {
		name     string
		business *assets.Business
		expected *pb.IpAdminInfoResponseItem
	}{
		{
			name: "happy path",
			business: &assets.Business{
				System:   "business1",
				SystemId: "1",
				PersonBase: []*assets.PersonBase{
					{
						Name: "oper1",
						Id:   "1",
						Fid:  "1234567890",
						FindInfo: []*assets.PersonFindInfo{
							{
								SourceId:    1,
								NodeId:      2,
								SourceValue: "oper1",
								FindCount:   1,
							},
						},
						Department: []*assets.DepartmentBase{
							{
								Id:      1,
								Name:    "department1",
								Parents: []*assets.DepartmentBase{},
							},
						},
					},
				},
				BusinessTrustedState: 1,
			},
			expected: &pb.IpAdminInfoResponseItem{
				System:   "business1",
				SystemId: "1",
				PersonBase: []*pb.PersonBase{
					{
						Name: "oper1",
						Id:   "1",
						Fid:  "1234567890",
						FindInfo: []*pb.PersonFindInfo{
							{SourceId: 1, NodeId: 2, SourceValue: "oper1", FindCount: 1},
						},
						Department: []*pb.DepartmentBase{
							{
								Id:      1,
								Name:    "department1",
								Parents: []*pb.DepartmentBase{},
							},
						},
					},
				},
				Reliability: 1,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := convertToPbBusinessSystem(tt.business)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestConvertToPbPersonBase(t *testing.T) {
	tests := []struct {
		name     string
		input    *assets.PersonBase
		expected *pb.PersonBase
	}{
		{
			name:     "nil input",
			input:    nil,
			expected: nil,
		},
		{
			name: "empty fields",
			input: &assets.PersonBase{
				Id:         "",
				Fid:        "",
				Name:       "",
				FindInfo:   nil,
				Department: nil,
			},
			expected: &pb.PersonBase{
				Id:         "",
				Fid:        "",
				Name:       "",
				FindInfo:   make([]*pb.PersonFindInfo, 0),
				Department: make([]*pb.DepartmentBase, 0),
			},
		},
		{
			name: "with all fields",
			input: &assets.PersonBase{
				Id:   "123",
				Fid:  "F123",
				Name: "张三",
				FindInfo: []*assets.PersonFindInfo{
					{
						SourceId:     uint64(1),
						NodeId:       uint64(1),
						SourceValue:  "value1",
						MappingField: "field1",
						FindCount:    5,
					},
				},
				Department: []*assets.DepartmentBase{
					{
						Id:                 uint64(456),
						Name:               "技术部",
						BusinessSystemId:   "789",
						BusinessSystemName: "系统1",
						UserId:             "U123",
						UserName:           "李四",
					},
				},
			},
			expected: &pb.PersonBase{
				Id:   "123",
				Fid:  "F123",
				Name: "张三",
				FindInfo: []*pb.PersonFindInfo{
					{
						SourceId:     uint64(1),
						NodeId:       uint64(1),
						SourceValue:  "value1",
						MappingField: "field1",
						FindCount:    5,
					},
				},
				Department: []*pb.DepartmentBase{
					{
						Id:                 uint64(456),
						Name:               "技术部",
						BusinessSystemId:   "789",
						BusinessSystemName: "系统1",
						UserId:             "U123",
						UserName:           "李四",
						Parents:            make([]*pb.DepartmentBase, 0),
					},
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := convertToPbPersonBase(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestConvertToPbDepartmentBase(t *testing.T) {
	tests := []struct {
		name     string
		input    *assets.DepartmentBase
		expected *pb.DepartmentBase
	}{
		{
			name:     "nil input",
			input:    nil,
			expected: nil,
		},
		{
			name: "empty fields",
			input: &assets.DepartmentBase{
				Id:                 uint64(0),
				Name:               "",
				BusinessSystemId:   "",
				BusinessSystemName: "",
				UserId:             "",
				UserName:           "",
				Parents:            nil,
			},
			expected: &pb.DepartmentBase{
				Id:                 uint64(0),
				Name:               "",
				BusinessSystemId:   "",
				BusinessSystemName: "",
				UserId:             "",
				UserName:           "",
				Parents:            make([]*pb.DepartmentBase, 0),
			},
		},
		{
			name: "with fields and no parents",
			input: &assets.DepartmentBase{
				Id:                 uint64(123),
				Name:               "技术部",
				BusinessSystemId:   "456",
				BusinessSystemName: "系统1",
				UserId:             "U789",
				UserName:           "张三",
				Parents:            nil,
			},
			expected: &pb.DepartmentBase{
				Id:                 uint64(123),
				Name:               "技术部",
				BusinessSystemId:   "456",
				BusinessSystemName: "系统1",
				UserId:             "U789",
				UserName:           "张三",
				Parents:            make([]*pb.DepartmentBase, 0),
			},
		},
		{
			name: "with fields and parents",
			input: &assets.DepartmentBase{
				Id:                 uint64(123),
				Name:               "技术部",
				BusinessSystemId:   "456",
				BusinessSystemName: "系统1",
				UserId:             "U789",
				UserName:           "张三",
				Parents: []*assets.DepartmentBase{
					{
						Id:                 uint64(789),
						Name:               "总部",
						BusinessSystemId:   "456",
						BusinessSystemName: "系统1",
						UserId:             "U999",
						UserName:           "李四",
					},
				},
			},
			expected: &pb.DepartmentBase{
				Id:                 uint64(123),
				Name:               "技术部",
				BusinessSystemId:   "456",
				BusinessSystemName: "系统1",
				UserId:             "U789",
				UserName:           "张三",
				Parents: []*pb.DepartmentBase{
					{
						Id:                 uint64(789),
						Name:               "总部",
						BusinessSystemId:   "456",
						BusinessSystemName: "系统1",
						UserId:             "U999",
						UserName:           "李四",
						Parents:            make([]*pb.DepartmentBase, 0),
					},
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := convertToPbDepartmentBase(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestConvertToPbPersonFindInfo(t *testing.T) {
	tests := []struct {
		name     string
		input    *assets.PersonFindInfo
		expected *pb.PersonFindInfo
	}{
		{
			name:     "nil input",
			input:    nil,
			expected: nil,
		},
		{
			name: "empty fields",
			input: &assets.PersonFindInfo{
				SourceId:     uint64(0),
				NodeId:       uint64(0),
				SourceValue:  "",
				MappingField: "",
				FindCount:    0,
			},
			expected: &pb.PersonFindInfo{
				SourceId:     uint64(0),
				NodeId:       uint64(0),
				SourceValue:  "",
				MappingField: "",
				FindCount:    0,
			},
		},
		{
			name: "with all fields",
			input: &assets.PersonFindInfo{
				SourceId:     uint64(123),
				NodeId:       uint64(456),
				SourceValue:  "value1",
				MappingField: "field1",
				FindCount:    5,
			},
			expected: &pb.PersonFindInfo{
				SourceId:     uint64(123),
				NodeId:       uint64(456),
				SourceValue:  "value1",
				MappingField: "field1",
				FindCount:    5,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := convertToPbPersonFindInfo(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}
