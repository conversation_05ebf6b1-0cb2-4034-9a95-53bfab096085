package handler

import (
	"context"
	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/initialize/es"
	"fobrain/initialize/mysql"
	pb "fobrain/mergeService/proto"
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/network_areas"
	"testing"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/olivere/elastic/v7"
	. "github.com/smartystreets/goconvey/convey"
	"github.com/stretchr/testify/assert"
)

func TestDeviceBaseInfo(t *testing.T) {
	// Mock es.List
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	mockServer.Register("device/_doc/1", &elastic.GetResult{
		Id:     "1",
		Source: []byte(`{"id": "1", "hostname": ["hostname1"], "sn": ["sn1"], "mac": ["mac1"], "ip": ["ip1"], "private_ip": ["privateIp1"], "public_ip": ["publicIp1"], "machine_room": ["machineRoom1"], "os": ["os1"], "created_at": "2021-11-11 11:11:11", "updated_at": "2021-11-11 11:11:11", "all_source_ids": [1], "area": [1]}`),
		Found:  true,
	})

	Convey("test DeviceBaseInfo", t, func() {
		getAllSourcesMock := gomonkey.ApplyFuncReturn(getAllSources, []*data_source.Source{
			{
				BaseModel: mysql.BaseModel{Id: uint64(1)},
				Name:      "source1",
				Icon:      "icon1",
			},
		}, nil)
		defer getAllSourcesMock.Reset()

		Convey("happy path", func() {
			getAllAreasMock := gomonkey.ApplyFuncReturn(getAllArea, []*network_areas.NetworkArea{
				{
					BaseModel: mysql.BaseModel{Id: uint64(1)},
					Name:      "area1",
				},
			}, nil)
			defer getAllAreasMock.Reset()

			m := &Merge{}
			rsp := &pb.DeviceBaseInfoResponse{}
			err := m.DeviceBaseInfo(context.Background(), &pb.DeviceInfoRequest{Id: "1"}, rsp)
			assert.Nil(t, err)
			assert.Equal(t, "ip1", rsp.Ip[0])
			assert.Equal(t, "sn1", rsp.Sn[0])
			assert.Equal(t, "mac1", rsp.Mac[0])
			assert.Equal(t, "hostname1", rsp.Hostname[0])
			assert.Equal(t, "machineRoom1", rsp.MachineRoom[0])
			assert.Equal(t, "os1", rsp.Os[0])
			assert.Len(t, rsp.Oper, 0)
			assert.Equal(t, uint64(1), rsp.SourceId[0])
			assert.Equal(t, uint64(1), rsp.AreaId[0])
			assert.Equal(t, "source1", rsp.Source[0].Name)
			assert.Equal(t, "area1", rsp.Area[0].Name)
		})

		Convey("area not found", func() {
			getAllAreasMock := gomonkey.ApplyFuncReturn(getAllArea, []*network_areas.NetworkArea{}, nil)
			defer getAllAreasMock.Reset()

			m := &Merge{}
			rsp := &pb.DeviceBaseInfoResponse{}
			err := m.DeviceBaseInfo(context.Background(), &pb.DeviceInfoRequest{Id: "1"}, rsp)
			assert.Nil(t, err)
			assert.Equal(t, "ip1", rsp.Ip[0])
			assert.Equal(t, "sn1", rsp.Sn[0])
			assert.Equal(t, "mac1", rsp.Mac[0])
			assert.Equal(t, "hostname1", rsp.Hostname[0])
			assert.Equal(t, "machineRoom1", rsp.MachineRoom[0])
			assert.Equal(t, "os1", rsp.Os[0])
			assert.Len(t, rsp.Oper, 0)
			assert.Equal(t, uint64(1), rsp.SourceId[0])
			assert.Equal(t, "source1", rsp.Source[0].Name)
			assert.Equal(t, uint64(1), rsp.AreaId[0])
			assert.Len(t, rsp.Area, 0)
		})
	})

}

func TestDeviceRelatedIpInfo(t *testing.T) {
	// Mock es.List
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	mockServer.Register("device/_doc/1", &elastic.GetResult{
		Id:     "1",
		Source: []byte(`{"id": "1", "hostname": ["hostname1"], "sn": ["sn1"], "mac": ["mac1"], "ip": ["ip1","ip2"], "private_ip": ["ip1"], "public_ip": ["ip2"], "machine_room": ["machineRoom1"], "os": ["os1"], "created_at": "2021-11-11 11:11:11", "updated_at": "2021-11-11 11:11:11", "source_ids": [1], "area": [1]}`),
		Found:  true,
	})

	assetList := make([]*pb.Asset, 0)
	assetList = append(assetList, &pb.Asset{Ip: "ip1", NetworkType: 1})
	assetList = append(assetList, &pb.Asset{Ip: "ip2", NetworkType: 2})
	getAssetListMock := gomonkey.ApplyFuncReturn(getAssetList, assetList, int64(2), nil)
	defer getAssetListMock.Reset()

	m := &Merge{}
	rsp := &pb.DeviceRelatedIpInfoResponse{}
	err := m.DeviceRelatedIpInfo(context.Background(), &pb.DeviceInfoRequest{Id: "1"}, rsp)
	assert.Nil(t, err)
	assert.Len(t, rsp.IpList["private"].Assets, 1)
	assert.Len(t, rsp.IpList["public"].Assets, 1)
}

func TestDeviceHostInfo(t *testing.T) {
	// Mock es.List
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	mockServer.Register("device/_doc/1", &elastic.GetResult{
		Id:     "1",
		Source: []byte(`{"id": "1","area": [1],"area_source": {"6-1": 1},"ip": ["**************"],"ip_source": {"6-1": "**************"},"private_ip": null,"public_ip": ["**************"],"hostname": ["hostname5"],"hostname_source": {"6-1": "hostname5"},"eth_name": ["eth0"],"eth_name_source": {"6-1": "eth0"},"os": ["centos"],"os_source": {"6-1": "centos"},"kernel": ["linux"],"kernel_source": {"6-1": "linux"},"model": ["HP ProLiant"],"model_source": {"6-1": "HP ProLiant "},"maker": ["HP"],"maker_source": {"6-1": "HP "},"sn": ["XYZ987658"],"sn_source": {"6-1": "XYZ987658"},"mac": ["9c:69:b4:60:b0:e4"],"mac_source": {"6-1": "9c:69:b4:60:b0:e4"},"machine_room": ["北京华顺"],"machine_room_source": {"6-1": "北京华顺"},"memory_size": ["128"],"memory_size_source": {"6-1": "128"},"memory_usage_rate": ["0.5"],"memory_usage_rate_source": {"6-1": "0.5"},"cpu_maker": ["intel"],"cpu_maker_source": {"6-1": "intel"},"cpu_brand": ["Xeon"],"cpu_brand_source": {"6-1": "Xeon"},"cpu_count": [16],"cpu_count_source": {"6-1": 16},"disk_count": [4],"disk_count_source": {"6-1": 4},"disk_size": [1000],"disk_size_source": {"6-1": 1000},"disk_usage_rate": ["0.8"],"disk_usage_rate_source": {"6-1": "0.8"},"load_average": ["1.8"],"load_average_source": {"6-1": "1.8"},"fusion_rules": null,"deleted_at": null,"purged_at": null,"created_at": "2024-09-05 18:31:21","updated_at": null,"merge_count": 1,"merge_key": ["hostname"]}`),
		Found:  true,
	})

	m := &Merge{}
	rsp := &pb.HostInfo{}
	err := m.DeviceHostInfo(context.Background(), &pb.DeviceInfoRequest{Id: "1"}, rsp)
	assert.Nil(t, err)
	assert.Equal(t, "HP", rsp.Maker[0])
	assert.Equal(t, "HP ProLiant", rsp.Model[0])
	assert.Equal(t, "XYZ987658", rsp.Sn[0])
	assert.Equal(t, "centos", rsp.Os[0])
	assert.Equal(t, "linux", rsp.OsKernel[0])
	assert.Equal(t, "128", rsp.MemorySize[0])
	assert.Equal(t, "0.5", rsp.MemoryUsageRate[0])
	assert.Equal(t, "intel", rsp.CpuMaker[0])
	assert.Equal(t, "Xeon", rsp.CpuBrand[0])
	assert.Equal(t, int32(16), rsp.CpuCount[0])
	assert.Equal(t, "1.8", rsp.LoadAverage[0])
	assert.Equal(t, int32(4), rsp.DiskCount[0])
	assert.Equal(t, int32(1000), rsp.DiskSize[0])
	assert.Equal(t, "0.8", rsp.DiskUsageRate[0])
}

func TestDeviceTraceabilityBaseInfo(t *testing.T) {
	// Mock es.List
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	mockServer.Register("device/_doc/1", &elastic.GetResult{
		Id:     "1",
		Source: []byte(`{"id": "1","area": [1],"ip": ["**************"],"hostname": ["hostname5"],"eth_name": ["eth0"],"os": ["centos"],"sn": ["XYZ987658"],"mac": ["9c:69:b4:60:b0:e4"],"deleted_at": null,"purged_at": null,"created_at": "2024-09-05 18:31:21","updated_at": null,"merge_count": 1,"merge_key": ["hostname"],"source_ids": [1],"all_source_ids":[1]}`),
		Found:  true,
	})

	getAllSourcesMock := gomonkey.ApplyFuncReturn(getAllSources, []*data_source.Source{
		{
			BaseModel: mysql.BaseModel{Id: uint64(1)},
			Name:      "source1",
			Icon:      "icon1",
		},
	}, nil)
	defer getAllSourcesMock.Reset()

	m := &Merge{}
	rsp := &pb.DeviceTraceabilityBaseInfoResponse{}
	err := m.DeviceTraceabilityBaseInfo(context.Background(), &pb.DeviceInfoRequest{Id: "1"}, rsp)
	assert.NoError(t, err)
	assert.Equal(t, uint64(1), rsp.AllSourceId[0])
	assert.Equal(t, uint64(1), rsp.SourceId[0])
	assert.Equal(t, "2024-09-05 18:31:21", rsp.FirstMergeTime)
	assert.Equal(t, rsp.FirstMergeTime, rsp.LastMergeTime)
}

func TestDeviceTraceabilityProcessInfo(t *testing.T) {
	// Mock es.List
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()
	mockServer.Register("device/_doc/1", &elastic.GetResult{
		Id:     "1",
		Source: []byte(`{"id": "1","hostname": ["hostname5"],"eth_name": ["eth0"],"os": ["centos"],"sn": ["XYZ987658"],"mac": ["9c:69:b4:60:b0:e4"],"deleted_at": null,"purged_at": null,"created_at": "2024-09-05 18:31:21","updated_at": null,"merge_count": 1,"merge_key": ["hostname"],"source_ids": [1],"all_source_ids":[1]}`),
		Found:  true,
	})

	mockServer.Register("device_merge_record/_search", []*elastic.SearchHit{
		{
			Id:     "1",
			Source: []byte(`{"id": "merge-records-1","device_id": "f5b0b19f263749059c470199c77a141a","device_record_id": "db2075ec411e4055bbc3b1ce3b358b28","sn": null,"mac": null,"hostname": ["hostname5"],"status": 1,"message": "","source_ids": [6],"node_ids": [1],"asset_task_ids": ["328_1_1_81.238.241.243"],"key": ["hostname"],"strategies": [{"id": 852,"created_at": "2024-08-30 11:52:31","updated_at": "2024-08-30 11:52:31","business_type": "device_merge","field_name": "Area","display_name": "Area","source_priority": {"1": 1,"2": 1,"3": 1,"4": 1,"5": 1,"6": 1,"7": 1},"untrusted_source": [],"version": 1724989951},{"id": 677,"created_at": "2024-08-22 10:25:47","updated_at": "2024-08-22 10:25:47","business_type": "device_merge","field_name": "Business","display_name": "业务系统","source_priority": {"2": 1,"4": 1,"5": 1,"6": 1,"7": 1},"untrusted_source": ["1", "3"],"version": 1724293546}],"created_at": "2024-09-05 18:31:53","updated_at": null,"merge_mode": "auto","batch_no": ""}`),
		},
	})

	ds := &data_source.Source{
		BaseModel: mysql.BaseModel{
			Id: 1,
		},
		Name: "test",
		Icon: "test",
	}
	dsList := []*data_source.Source{ds}
	// Mock the data_source.NewSourceModel() Items method
	datasourceItemsMock := gomonkey.ApplyMethodReturn(data_source.NewSourceModel(), "Items", dsList, int64(1), nil)
	defer datasourceItemsMock.Reset()

	esGetCountMock := gomonkey.ApplyFuncReturn(es.GetCount, int64(1), nil)
	defer esGetCountMock.Reset()

	m := &Merge{}
	rsp := &pb.TraceabilityProcessInfoResponse{}
	err := m.DeviceTraceabilityProcessInfo(context.Background(), &pb.TraceabilityProcessInfoRequest{Id: "1"}, rsp)
	assert.NoError(t, err)
	assert.Len(t, rsp.Process, 1)
	assert.Equal(t, int32(2), rsp.Process[0].Type)
	assert.Equal(t, "merge-records-1", rsp.Process[0].Id)
}

func TestDeviceTraceabilityDetailInfo(t *testing.T) {
	// Mock es.List
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()
	mockServer.Register("device/_doc/1", &elastic.GetResult{
		Id:     "1",
		Source: []byte(`{"id": "1","hostname": ["hostname5"],"eth_name": ["eth0"],"os": ["centos"],"sn": ["XYZ987658"],"mac": ["9c:69:b4:60:b0:e4"],"deleted_at": null,"purged_at": null,"created_at": "2024-09-05 18:31:21","updated_at": null,"merge_count": 1,"merge_key": ["hostname"],"source_ids": [1],"all_source_ids":[1]}`),
		Found:  true,
	})
	mockServer.Register("device_merge_record/_search", []*elastic.SearchHit{
		{
			Id:     "1",
			Source: []byte(`{"id": "merge-records-1","device_id": "f5b0b19f263749059c470199c77a141a","device_record_id": "db2075ec411e4055bbc3b1ce3b358b28","sn": null,"mac": null,"hostname": ["hostname5"],"status": 1,"message": "","source_ids": [6],"node_ids": [1],"asset_task_ids": ["328_1_1_81.238.241.243"],"key": ["hostname"],"strategies": [{"id": 852,"created_at": "2024-08-30 11:52:31","updated_at": "2024-08-30 11:52:31","business_type": "device_merge","field_name": "Area","display_name": "Area","source_priority": {"1": 1,"2": 1,"3": 1,"4": 1,"5": 1,"6": 1,"7": 1},"untrusted_source": [],"version": 1724989951}],"created_at": "2024-09-05 18:31:53","updated_at": null,"merge_mode": "auto","batch_no": ""}`),
		},
	})

	mockServer.Register("device_record/_doc/db2075ec411e4055bbc3b1ce3b358b28", &elastic.GetResult{
		Id:     "1",
		Source: []byte(`{"id": "db2075ec411e4055bbc3b1ce3b358b28","Device": {"id": "db2075ec411e4055bbc3b1ce3b358b28","hostname": ["设备编号: 001-0000000086"],"hostname_source": {"3-30": "设备编号: 001-0000000086"},"sn": null,"sn_source": {"3-30": ""},"mac": ["0f:00:00:00:00:56"],"deleted_at": null,"purged_at": null,"created_at": "2024-09-05 18:31:21","updated_at": null,"merge_count": 1,"merge_key": ["hostname"]}}`),
		Found:  true,
	})

	mockServer.Register(".*_task_assets/_search", []*elastic.SearchHit{
		{
			Id:     "1",
			Index:  "foeye_task_assets",
			Source: []byte(`{}`),
		},
	})

	getAllSourcesMock := gomonkey.ApplyFuncReturn(getAllSources, []*data_source.Source{
		{
			BaseModel: mysql.BaseModel{Id: uint64(1)},
			Name:      "source1",
			Icon:      "icon1",
		},
	}, nil)
	defer getAllSourcesMock.Reset()

	// Mock the data_source.NewSourceModel() Items method
	area := &network_areas.NetworkArea{
		BaseModel: mysql.BaseModel{
			Id: 1,
		},
		Name: "test",
	}
	areaList := []*network_areas.NetworkArea{area}
	allAreaMock := gomonkey.ApplyFuncReturn(network_areas.AllNetworkAreas, areaList, int64(1), nil)
	defer allAreaMock.Reset()

	m := &Merge{}
	rsp := &pb.DeviceTraceabilityDetailInfoResponse{}
	err := m.DeviceTraceabilityDetailInfo(context.Background(), &pb.TraceabilityDetailInfoRequest{Id: "1", ProcessInfo: []*pb.TraceabilityProcessInfoResponseItem{{
		Id:   "merge-records-1",
		Type: TRACEABILITY_PROCESS_DETAIL_TYPE_AUTO_MERGE,
	}}}, rsp)
	assert.NoError(t, err)
	assert.Len(t, rsp.Items, 1)
	assert.Len(t, rsp.Items[0].OriginalData, 1)
	assert.Len(t, rsp.Items[0].Strategies, 1)
	assert.Equal(t, "db2075ec411e4055bbc3b1ce3b358b28", rsp.Items[0].MergedData.Id)
	assert.Len(t, rsp.Items[0].AllSourceForStrategy, 1)
	assert.Len(t, rsp.Items[0].AllSourceForMergeData, 1)
}

func TestDeviceRelatedEthInfo(t *testing.T) {
	// Mock es.List
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	mockServer.Register("device/_doc/1", &elastic.GetResult{
		Id:     "1",
		Source: []byte(`{"id": "1","hostname": ["hostname1"],"hostname_source": {"3-30": "hostname1"},"sn": null,"sn_source": {"3-30": ""},"mac": ["0f:00:00:00:00:56"],"deleted_at": null,"purged_at": null,"created_at": "2024-09-05 18:31:21","updated_at": null,"merge_count": 1,"merge_key": ["hostname"],"network_cards": [{"name": "eth0","mac": "0f:00:00:00:00:56","ipv4": "***********","ipv6": "2001:db8::1","broadcast": "*************","netmask": "*************","dns_server": ["*******"],"gateway": "***********","speed": 1000,"status": "1"}]}`),
		Found:  true,
	})

	m := &Merge{}
	rsp := &pb.DeviceRelatedEthInfoResponse{}
	err := m.DeviceRelatedEthInfo(context.Background(), &pb.DeviceInfoRequest{Id: "1"}, rsp)
	assert.NoError(t, err)
	assert.Len(t, rsp.EthList, 1)
}
