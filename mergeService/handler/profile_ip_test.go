package handler

import (
	"context"
	"encoding/json"
	business_system2 "fobrain/models/elastic/business_system"
	staff2 "fobrain/models/elastic/staff"
	"testing"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/alicebob/miniredis/v2"
	"github.com/olivere/elastic/v7"
	"github.com/stretchr/testify/assert"

	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/initialize/mysql"
	pb "fobrain/mergeService/proto"
	"fobrain/models/elastic/assets"
	"fobrain/models/mysql/data_source"

	goRedis "github.com/go-redis/redis/v8"
)

func TestMerge_IpAdminInfo(t *testing.T) {
	m := &Merge{}
	ctx := context.Background()

	// 模拟 es.GetById
	asset := &assets.Assets{
		OperInfo:    []*assets.PersonBase{{Id: "1", Fid: "1", Name: "operator1", FindInfo: []*assets.PersonFindInfo{{SourceId: 1, NodeId: 1, MappingField: "name", SourceValue: "operator1"}}}},
		MachineRoom: []string{"Room1"},
		Business:    []*assets.Business{{System: "System1", SystemId: "System111", OwnerId: "Owner111", Owner: "Owner"}},
	}
	assetJson, _ := json.Marshal(asset)
	business := business_system2.BusinessSystems{
		Id:           "System111",
		Fid:          "System111",
		BusinessName: "System1",
		Address:      "System111",
	}
	staff := staff2.Staff{
		Id:         "Owner111",
		Fid:        "Owner111",
		FidHash:    "",
		Name:       "Owner1",
		Department: nil,
	}
	businessJson, _ := json.Marshal(business)
	staffJson, _ := json.Marshal(staff)
	// Mock es.GetById
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	mockServer.Register("asset/_doc/1", &elastic.GetResult{
		Id:     "1",
		Source: assetJson,
		Found:  true,
	})
	mockServer.Register("staff/_search", []*elastic.SearchHit{
		{
			Id:     "1",
			Source: staffJson,
		},
	})
	mockServer.Register("business_systems/_search", []*elastic.SearchHit{
		{
			Id:     "1",
			Source: businessJson,
		},
	})

	s, err := miniredis.Run()
	if err != nil {
		t.Fatalf("无法启动 miniredis: %v", err)
	}
	defer s.Close()

	// 创建 Redis 客户端
	cli := goRedis.NewClient(&goRedis.Options{
		Addr: s.Addr(),
	})
	testcommon.SetRedisClient(cli)

	req := &pb.IpInfoRequest{Id: "1"}
	rsp := &pb.IpAdminInfoResponse{}

	err = m.IpAdminInfo(ctx, req, rsp)

	assert.NoError(t, err)
	assert.Equal(t, "operator1", rsp.Oper[0].Name)
	assert.Equal(t, "Room1", rsp.MachineRoom[0])
	assert.Len(t, rsp.Admin, 1)
	assert.Equal(t, "System1", rsp.Admin[0].System)
	assert.Equal(t, "Owner", rsp.Admin[0].Owner)
}

func TestMerge_IpAccountInfo(t *testing.T) {
	m := &Merge{}
	ctx := context.Background()

	req := &pb.IpInfoRequest{Id: "1"}
	rsp := &pb.IpAccountInfoResponse{}

	err := m.IpAccountInfo(ctx, req, rsp)

	assert.NoError(t, err)
	// 由于当前实现为空，我们只需确保它不返回错误
}

func TestMerge_IpPortInfo(t *testing.T) {
	m := &Merge{}
	ctx := context.Background()

	// 模拟 es.GetById
	asset := &assets.Assets{
		Ports: []*assets.PortInfo{
			{Port: 80, Protocol: "TCP", Status: 1, Url: "http://example.com", Domain: "example.com", Title: "Example"},
		},
	}
	assetJson, _ := json.Marshal(asset)

	// Mock es.GetById
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	mockServer.Register("asset/_doc/1", &elastic.GetResult{
		Id:     "1",
		Source: assetJson,
		Found:  true,
	})

	req := &pb.IpInfoRequest{Id: "1"}
	rsp := &pb.IpPortInfoResponse{}

	err := m.IpPortInfo(ctx, req, rsp)

	assert.NoError(t, err)
	assert.Len(t, rsp.Port, 1)
	assert.Equal(t, int32(80), rsp.Port[0].Port)
	assert.Equal(t, "TCP", rsp.Port[0].Protocol)
	assert.Equal(t, int32(1), rsp.Port[0].Status)
	assert.Equal(t, "http://example.com", rsp.Port[0].Url)
	assert.Equal(t, "example.com", rsp.Port[0].Domain)
	assert.Equal(t, "Example", rsp.Port[0].Title)
}

func TestMerge_IpHostInfo(t *testing.T) {
	m := &Merge{}
	ctx := context.Background()

	asset := &assets.Assets{
		Maker:           []string{"Maker1"},
		Model:           []string{"Model1"},
		Sn:              []string{"SN1"},
		Os:              []string{"OS1"},
		Kernel:          []string{"Kernel1"},
		MemorySize:      []string{"8GB"},
		MemoryUsageRate: []string{"50%"},
		CpuMaker:        []string{"CPUMaker1"},
		CpuBrand:        []string{"CPUBrand1"},
		CpuCount:        []int{4},
		LoadAverage:     []string{"1.5"},
		DiskCount:       []int{2},
		DiskSize:        []int{1000},
		DiskUsageRate:   []string{"75%"},
	}
	assetJson, _ := json.Marshal(asset)

	// Mock es.GetById
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	mockServer.Register("asset/_doc/1", &elastic.GetResult{
		Id:     "1",
		Source: assetJson,
		Found:  true,
	})
	s, err := miniredis.Run()
	if err != nil {
		t.Fatalf("无法启动 miniredis: %v", err)
	}
	defer s.Close()

	// 创建 Redis 客户端
	cli := goRedis.NewClient(&goRedis.Options{
		Addr: s.Addr(),
	})
	testcommon.SetRedisClient(cli)

	req := &pb.IpInfoRequest{Id: "1"}
	rsp := &pb.HostInfo{}

	err = m.IpHostInfo(ctx, req, rsp)

	assert.NoError(t, err)
	assert.Equal(t, "Maker1", rsp.Maker[0])
	assert.Equal(t, "Model1", rsp.Model[0])
	assert.Equal(t, "SN1", rsp.Sn[0])
	assert.Equal(t, "OS1", rsp.Os[0])
	assert.Equal(t, "Kernel1", rsp.OsKernel[0])
	assert.Equal(t, "8GB", rsp.MemorySize[0])
	assert.Equal(t, "50%", rsp.MemoryUsageRate[0])
	assert.Equal(t, "CPUMaker1", rsp.CpuMaker[0])
	assert.Equal(t, "CPUBrand1", rsp.CpuBrand[0])
	assert.Equal(t, int32(4), rsp.CpuCount[0])
	assert.Equal(t, "1.5", rsp.LoadAverage[0])
	assert.Equal(t, int32(2), rsp.DiskCount[0])
	assert.Equal(t, int32(1000), rsp.DiskSize[0])
	assert.Equal(t, "75%", rsp.DiskUsageRate[0])
}

func TestMerge_IpSecurityInfo(t *testing.T) {
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()
	mockDb.ExpectQuery("SELECT `source_id` FROM `data_nodes`").
		WillReturnRows(mockDb.NewRows([]string{"source_id"}).
			AddRow(1).
			AddRow(2).
			AddRow(1)) // 包含重复 ID

	m := &Merge{}
	ctx := context.Background()

	asset := &assets.Assets{
		Id:                    "1",
		Ip:                    "***********",
		Business:              []*assets.Business{{Owner: "owner1"}},
		OperInfo:              []*assets.PersonBase{{Id: "1", Fid: "1", Name: "operator1", FindInfo: []*assets.PersonFindInfo{{SourceId: 1, NodeId: 1, MappingField: "name", SourceValue: "operator1"}}}},
		SourceIds:             []uint64{1, 2},
		AllSourceIds:          []uint64{1, 2, 3},
		BusinessDepartmentIds: []uint64{1},
	}
	assetJson, _ := json.Marshal(asset)

	// Mock es.GetById
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	mockServer.Register("asset/_doc/1", &elastic.GetResult{
		Id:     "1",
		Source: assetJson,
		Found:  true,
	})
	mockServer.Register("/poc/_count", []*elastic.SearchHit{})
	mockServer.Register("staff/_search", []*elastic.SearchHit{
		{
			Id:     "1",
			Source: []byte(`{"name": "owner1", "department": ["dept1"]}`),
		},
	})
	mockServer.RegisterEmptyScrollHandler()

	// 模拟 data_source.NewSourceModel().SourceNames
	sourceModel := &data_source.Source{}
	sourceMock := gomonkey.ApplyMethodReturn(sourceModel, "SourceNames", []*data_source.Source{
		{BaseModel: mysql.BaseModel{Id: 1}, Name: "Source1"},
		{BaseModel: mysql.BaseModel{Id: 2}, Name: "Source2"},
	})
	defer sourceMock.Reset()

	// 模拟 data_source.NewSourceTypeModel().All
	sourceTypeModel := &data_source.SourceType{}
	sourceTypeMock := gomonkey.ApplyMethodReturn(sourceTypeModel, "All", []*data_source.SourceType{
		{BaseModel: mysql.BaseModel{Id: 1}, Name: "CMDB"},
		{BaseModel: mysql.BaseModel{Id: 2}, Name: "堡垒机"},
		{BaseModel: mysql.BaseModel{Id: 3}, Name: "防火墙"},
		{BaseModel: mysql.BaseModel{Id: 4}, Name: "主机安全"},
	}, nil)
	defer sourceTypeMock.Reset()

	// 模拟 data_source.NewSourceTypeMapModel().GetByTypeId
	sourceTypeMapModel := &data_source.SourceTypeMap{}
	sourceTypeMapMock := gomonkey.ApplyMethodReturn(sourceTypeMapModel, "GetByTypeId", []*data_source.SourceTypeMap{
		{SourceTypeId: 1, SourceId: 1},
		{SourceTypeId: 2, SourceId: 2},
		{SourceTypeId: 3, SourceId: 3},
		{SourceTypeId: 4, SourceId: 4},
	}, nil)
	defer sourceTypeMapMock.Reset()

	req := &pb.IpInfoRequest{Id: "1"}
	rsp := &pb.IpSecurityInfoResponse{}

	err := m.IpSecurityInfo(ctx, req, rsp)

	assert.NoError(t, err)
	assert.True(t, rsp.HasAdminForBusiness)
	assert.True(t, rsp.HasAdminForOperation)
	assert.True(t, rsp.HasDeparment)
	assert.Equal(t, rsp.IsCmdb, int32(2))
	assert.Equal(t, rsp.IsJumpserver, int32(2))
	//assert.Equal(t, rsp.IsHids, int32(1))
	assert.Equal(t, rsp.HasWaf, int32(2))
	assert.Equal(t, int32(0), rsp.PocCount)
}

func TestGetIntersection(t *testing.T) {
	testCases := []struct {
		name     string
		s1       []uint64
		s2       []uint64
		expected bool
		result   []uint64
	}{
		{
			name:     "有交集",
			s1:       []uint64{1, 2, 3},
			s2:       []uint64{2, 3, 4},
			expected: true,
			result:   []uint64{2, 3},
		},
		{
			name:     "无交集",
			s1:       []uint64{1, 2, 3},
			s2:       []uint64{4, 5, 6},
			expected: false,
			result:   []uint64{},
		},
		{
			name:     "空切片",
			s1:       []uint64{},
			s2:       []uint64{1, 2, 3},
			expected: false,
			result:   []uint64{},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			hasIntersection, result := getIntersection(tc.s1, tc.s2)
			assert.Equal(t, tc.expected, hasIntersection)
			assert.ElementsMatch(t, tc.result, result)
		})
	}
}

func TestGetSourceInfoForIpSecurityInfo(t *testing.T) {
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()
	mockDb.ExpectQuery("SELECT `source_id` FROM `data_nodes`").
		WillReturnRows(mockDb.NewRows([]string{"source_id"}).
			AddRow(1).
			AddRow(2).
			AddRow(1)) // 包含重复 ID
	asset := &assets.Assets{
		Id:           "1",
		SourceIds:    []uint64{1, 2},
		AllSourceIds: []uint64{1, 2, 3},
	}

	// 模拟 data_source.NewSourceModel().SourceNames
	sourceModel := &data_source.Source{}
	sourceMock := gomonkey.ApplyMethodReturn(sourceModel, "SourceNames", []*data_source.Source{
		{BaseModel: mysql.BaseModel{Id: 1}, Name: "Source1"},
		{BaseModel: mysql.BaseModel{Id: 2}, Name: "Source2"},
	})
	defer sourceMock.Reset()

	// 模拟 data_source.NewSourceTypeModel().All
	sourceTypeModel := &data_source.SourceType{}
	sourceTypeMock := gomonkey.ApplyMethodReturn(sourceTypeModel, "All", []*data_source.SourceType{
		{BaseModel: mysql.BaseModel{Id: 1}, Name: "CMDB"},
		{BaseModel: mysql.BaseModel{Id: 2}, Name: "堡垒机"},
		{BaseModel: mysql.BaseModel{Id: 3}, Name: "防火墙"},
		{BaseModel: mysql.BaseModel{Id: 4}, Name: "主机安全"},
	}, nil)
	defer sourceTypeMock.Reset()

	// 模拟 data_source.NewSourceTypeMapModel().GetByTypeId
	sourceTypeMapModel := &data_source.SourceTypeMap{}
	sourceTypeMapMock := gomonkey.ApplyMethodReturn(sourceTypeMapModel, "GetByTypeId", []*data_source.SourceTypeMap{
		{SourceTypeId: 1, SourceId: 1},
		{SourceTypeId: 2, SourceId: 2},
		{SourceTypeId: 3, SourceId: 3},
		{SourceTypeId: 4, SourceId: 4},
	}, nil)
	defer sourceTypeMapMock.Reset()

	isCmdb, isJumpserver, hasWaf, isHids, err := getSourceInfoForIpSecurityInfo(asset)

	assert.NoError(t, err)
	assert.Equal(t, isCmdb, int32(2))
	assert.Equal(t, isJumpserver, int32(2))
	assert.Equal(t, hasWaf, int32(2))
	assert.Equal(t, isHids, int32(0))
}

func TestGetSourceInfoForIpSecurityInfo_NoDataSource(t *testing.T) {
	asset := &assets.Assets{
		Id:        "1",
		SourceIds: []uint64{},
	}

	_, _, _, _, err := getSourceInfoForIpSecurityInfo(asset)

	assert.Error(t, err)
	assert.Equal(t, "no data source found", err.Error())
}

func TestFindStatusByName(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	mockServer.Register("business_systems/_search", []*elastic.SearchHit{
		{
			Id:     "1",
			Source: []byte(`{"status": 1}`),
		},
	})

	m := &Merge{}
	status, err := m.FindStatusByName("新增加的业务系统")

	assert.Nil(t, err)
	assert.Equal(t, status, 1)
}
