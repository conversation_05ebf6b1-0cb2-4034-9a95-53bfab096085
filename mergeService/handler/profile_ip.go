package handler

import (
	"context"
	"errors"
	"fmt"
	business_system2 "fobrain/models/elastic/business_system"
	"slices"
	"sync"

	"fobrain/initialize/es"
	"fobrain/initialize/mysql"
	pb "fobrain/mergeService/proto"
	"fobrain/mergeService/utils"
	logs "fobrain/mergeService/utils/log"
	models "fobrain/models/elastic"
	"fobrain/models/elastic/assets"
	"fobrain/models/elastic/poc"
	"fobrain/models/mysql/data_source"
	utils2 "fobrain/pkg/utils"

	"github.com/olivere/elastic/v7"
)

const (
	TRACEABILITY_PROCESS_DETAIL_TYPE_AUTO_MERGE         = 2 // 自动合并
	TRACEABILITY_PROCESS_DETAIL_TYPE_MANUAL_MERGE       = 3 // 立即融合
	TRACEABILITY_PROCESS_DETAIL_TYPE_MANUAL_CALIBRATION = 4 // 手动校准
)

// FindStatusByName 根据业务系统的名称查找业务系统的状态
func (m *Merge) FindStatusByName(name string) (int, error) {
	boolQuery := elastic.NewBoolQuery()
	boolQuery.Must(elastic.NewTermQuery("business_name", name))

	businessSystem, err := es.First[business_system2.BusinessSystems](boolQuery, nil, "status")
	if err != nil {
		logs.GetLogger("service").Warnf("parse business system error: %+v\n", err)
		return 0, err
	}

	if businessSystem != nil {
		return businessSystem.Status, nil
	}

	return 0, nil
}

// IpAdminInfo  IP管理信息
func (m *Merge) IpAdminInfo(ctx context.Context, req *pb.IpInfoRequest, rsp *pb.IpAdminInfoResponse) error {
	asset, err := es.GetById[assets.Assets](req.Id)
	if err != nil {
		return err
	}
	rsp.Oper = func() []*pb.PersonBase {
		arr := make([]*pb.PersonBase, 0)
		for _, p := range asset.OperInfo {
			arr = append(arr, convertToPbPersonBase(p))
		}
		return arr
	}()
	rsp.Admin = func() []*pb.IpAdminInfoResponseItem {
		arr := make([]*pb.IpAdminInfoResponseItem, 0)
		for _, p := range asset.Business {
			arr = append(arr, convertToPbBusinessSystem(p))
		}
		return arr
	}()
	rsp.MachineRoom = asset.MachineRoom
	rsp.Tags = asset.Tags

	return nil
}

// IpAccountInfo  IP账户信息
func (m *Merge) IpAccountInfo(ctx context.Context, req *pb.IpInfoRequest, rsp *pb.IpAccountInfoResponse) error {
	return nil
}

// IpPortInfo  IP端口信息
func (m *Merge) IpPortInfo(ctx context.Context, req *pb.IpInfoRequest, rsp *pb.IpPortInfoResponse) error {
	asset, err := es.GetById[assets.Assets](req.Id)
	if err != nil {
		return err
	}
	rsp.Port = make([]*pb.IpPortInfoResponseItem, 0)
	if len(asset.Ports) > 0 {
		for _, port := range asset.Ports {
			item := &pb.IpPortInfoResponseItem{
				Port:     int32(port.Port),
				Protocol: port.Protocol,
				Status:   int32(port.Status),
				Url:      port.Url,
				Domain:   port.Domain,
				Title:    port.Title,
			}
			rsp.Port = append(rsp.Port, item)
		}
	}
	return nil
}

// IpHostInfo  IP主机信息
func (m *Merge) IpHostInfo(ctx context.Context, req *pb.IpInfoRequest, rsp *pb.HostInfo) error {
	asset, err := es.GetById[assets.Assets](req.Id)
	if err != nil {
		return err
	}
	rsp.Maker = asset.Maker
	rsp.Model = asset.Model
	rsp.Sn = asset.Sn

	rsp.Os = asset.Os
	rsp.OsKernel = asset.Kernel

	rsp.MemorySize = asset.MemorySize
	rsp.MemoryUsageRate = asset.MemoryUsageRate

	rsp.CpuMaker = asset.CpuMaker
	rsp.CpuBrand = asset.CpuBrand
	rsp.CpuCount = utils.SlicesConvertInt32(asset.CpuCount)
	rsp.LoadAverage = asset.LoadAverage

	rsp.DiskCount = utils.SlicesConvertInt32(asset.DiskCount)
	rsp.DiskSize = utils.SlicesConvertInt32(asset.DiskSize)
	rsp.DiskUsageRate = asset.DiskUsageRate

	return nil
}

// IpSecurityInfo  IP安全信息
func (m *Merge) IpSecurityInfo(ctx context.Context, req *pb.IpInfoRequest, rsp *pb.IpSecurityInfoResponse) error {
	mlogs := logs.GetLogger("service")
	asset, err := es.GetById[assets.Assets](req.Id)
	if err != nil {
		return err
	}

	rsp.HasAdminForBusiness = func() bool {
		return len(asset.Business) > 0
	}()
	rsp.HasAdminForOperation = func() bool {
		return len(asset.OperInfo) > 0
	}()
	// 查询人员所属部门信息
	rsp.HasDeparment = func() bool {
		return len(asset.BusinessDepartmentIds) > 0 || len(asset.OperDepartmentIds) > 0
	}()
	rsp.IsCmdb = 0
	rsp.IsHids = 0
	rsp.IsJumpserver = 0
	rsp.HasWaf = 0

	wg := &sync.WaitGroup{}
	var err2 error

	// 查询关联数据源的数据源信息
	wg.Add(1) // 修复: 在启动goroutine前调用Add
	go func() {
		defer wg.Done()
		rsp.IsCmdb, rsp.IsJumpserver, rsp.HasWaf, rsp.IsHids, err = getSourceInfoForIpSecurityInfo(asset)
		if err != nil {
			mlogs.Warnf("get source info error: %v", err)
			err2 = err
			return
		}
	}()
	wg.Add(1) // 修复: 在启动goroutine前调用Add
	go func() {
		defer wg.Done()
		// poc 数量需要做关联
		threatQuery := elastic.NewBoolQuery()
		threatQuery = threatQuery.Must(elastic.NewTermsQueryFromStrings("ip", asset.Ip))
		threatQuery = threatQuery.Must(elastic.NewRangeQuery("status").Lt(30))
		total, err := es.GetCount(poc.NewPoc().IndexName(), threatQuery)
		if err != nil {
			err2 = err
			return
		}
		rsp.PocCount = int32(total)
	}()

	wg.Wait()
	if err2 != nil {
		return err2
	}
	return nil
}

// 获取数据源信息
func getSourceInfoForIpSecurityInfo(asset *assets.Assets) (isCmdb int32, isJumpserver int32, hasWaf int32, isHids int32, err error) {
	mlogs := logs.GetLogger("service")
	// 查询关联数据源的数据源信息
	if len(asset.AllSourceIds) == 0 {
		return 0, 0, 0, 0, errors.New("no data source found")
	}
	sources := data_source.NewSourceModel().SourceNames(asset.AllSourceIds)
	mlogs.Debugf("sources: %v", sources)

	// 获取数据源类型列表
	dataSourceTypeList, err := data_source.NewSourceTypeModel().All(mysql.WithValuesIn("name", []string{"CMDB", "堡垒机", "防火墙", "主机安全"}))
	if err != nil {
		mlogs.Warnf("查询数据源类型错误: %v", err)
		return 0, 0, 0, 0, err
	}
	// 收集数据源类型ID列表
	dataSourceTypeIdList := make([]uint64, 0)
	for _, sourceType := range dataSourceTypeList {
		dataSourceTypeIdList = append(dataSourceTypeIdList, sourceType.Id)
	}
	// 获取数据源类型映射表，得到数据源类型ID与数据源ID的映射关系
	dataSourceMapList, err := data_source.NewSourceTypeMapModel().GetByTypeId(dataSourceTypeIdList)
	if err != nil {
		mlogs.Warnf("查询数据源类型映射表错误: %v", err)
		return 0, 0, 0, 0, err
	}
	// 将数据源类型ID与数据源ID的映射关系转换为map
	dataSourceMapWithTypeId := make(map[uint64][]uint64)
	for _, typeMap := range dataSourceMapList {
		dataSourceMapWithTypeId[typeMap.SourceTypeId] = append(dataSourceMapWithTypeId[typeMap.SourceTypeId], typeMap.SourceId)
	}
	//获取添加过节点的数据源ID列表
	allExistsSourceIds, err := data_source.NewNodeModel().GetAllExistingSourceIds(true)
	if err != nil {
		mlogs.Warnf("查询添加过节点的数据源ID列表错误: %v", err)
		return 0, 0, 0, 0, err
	}
	// 遍历数据源类型列表，判断对应数据源类型的数据源，是否包含当前数据的数据源ID
	for _, sourceType := range dataSourceTypeList {
		switch sourceType.Name {
		case "CMDB":
			val, ok := dataSourceMapWithTypeId[sourceType.Id]
			if ok {
				if h, _ := getIntersection(val, asset.AllSourceIds); h {
					isCmdb = 2
				} else if h, _ := getIntersection(val, allExistsSourceIds); h {
					isCmdb = 1
				}
			}
		case "堡垒机":
			val, ok := dataSourceMapWithTypeId[sourceType.Id]
			if ok {
				if h, _ := getIntersection(val, asset.AllSourceIds); h {
					isJumpserver = 2
				} else if h, _ := getIntersection(val, allExistsSourceIds); h {
					isJumpserver = 1
				}
			}
		case "防火墙":
			val, ok := dataSourceMapWithTypeId[sourceType.Id]
			if ok {
				if h, _ := getIntersection(val, asset.AllSourceIds); h {
					hasWaf = 2
				} else if h, _ := getIntersection(val, allExistsSourceIds); h {
					hasWaf = 1
				}
			}
		case "主机安全":
			val, ok := dataSourceMapWithTypeId[sourceType.Id]
			if ok {
				if h, _ := getIntersection(val, asset.AllSourceIds); h {
					isHids = 2
				} else if h, _ := getIntersection(val, allExistsSourceIds); h {
					isHids = 1
				}
			}
		}
	}
	return isCmdb, isJumpserver, hasWaf, isHids, nil
}

// 获取两个slice的交集
func getIntersection(s1, s2 []uint64) (bool, []uint64) {
	m := make(map[uint64]bool)
	for _, v := range s1 {
		m[v] = true
	}
	var res []uint64
	for _, v := range s2 {
		if m[v] {
			res = append(res, v)
		}
	}
	return len(res) > 0, res
}

// IpTraceabilityBaseInfo  IP溯源基础信息
func (m *Merge) IpTraceabilityBaseInfo(ctx context.Context, req *pb.IpInfoRequest, rsp *pb.IpTraceabilityBaseInfoResponse) error {
	asset, err := es.GetById[assets.Assets](req.Id)
	if err != nil {
		return err
	}
	if len(asset.AllSourceIds) > 0 {
		// 关联所有数据源
		allSourceList := data_source.NewSourceModel().SourceNames(asset.AllSourceIds)
		// 给出所有数据源的名称和图标
		for _, as := range asset.AllSourceIds {
			if as == 0 {
				// 0 表示人工校准数据
				continue
			}
			s := getSourceFromSourceListById(as, allSourceList)
			rsp.AllSource = append(rsp.AllSource, &pb.Source{
				Id:   s.Id,
				Name: s.Name,
				Icon: s.Icon,
			})
			// 给出当前资产关联的源数据
			if slices.Contains(asset.SourceIds, as) {
				rsp.Source = append(rsp.Source, &pb.Source{
					Id:   s.Id,
					Name: s.Name,
					Icon: s.Icon,
				})
			}
		}
	}
	rsp.AllSourceId = asset.SourceIds
	rsp.SourceId = asset.SourceIds
	rsp.IsExtratDevice = int32(asset.IsDeviceExtracted)
	rsp.MergeCount = int32(asset.MergeCount)
	rsp.FirstMergeTime = asset.CreatedAt.String()
	rsp.LastMergeTime = func() string {
		if asset.UpdatedAt == nil {
			return asset.CreatedAt.String()
		}
		return asset.UpdatedAt.String()
	}()
	return nil
}

// IpTraceabilityProcessInfo  IP溯源流程信息
func (m *Merge) IpTraceabilityProcessInfo(ctx context.Context, req *pb.TraceabilityProcessInfoRequest, rsp *pb.TraceabilityProcessInfoResponse) error {
	logs := logs.GetLogger("service")
	asset, err := es.GetById[assets.Assets](req.Id)
	if err != nil {
		return err
	}

	// 查询所有数据源, 用于给出所有数据源的名称和图标
	sources, sourceCount, err := data_source.NewSourceModel().Items(1, 500)
	if err != nil {
		logs.Warnf("get all sources error: %v", err)
	}
	if sourceCount < 1 {
		logs.Warn("no source found")
	}

	rsp.Page = req.PerPage
	rsp.CurrentPage = req.Page
	rsp.Process = make([]*pb.TraceabilityProcessInfoResponseItem, 0)

	// 查询融合记录表
	sortList := []elastic.Sorter{
		elastic.NewFieldSort("created_at").Desc(),
	}
	mergeRecordQuery := elastic.NewBoolQuery()
	autoMergeRecordQuery := elastic.NewBoolQuery().Must(elastic.NewTermQuery("asset_id", asset.Id))
	manualMergeRecordQuery := elastic.NewBoolQuery().Must(elastic.NewTermQuery("merge_mode", models.MergeMode_Manual), elastic.NewRangeQuery("created_at").Gt(asset.CreatedAt.String()))
	calibrationMergeRecordQuery := elastic.NewBoolQuery().Must(elastic.NewTermQuery("merge_mode", models.MergeMode_Calibration), elastic.NewMatchQuery("asset_ids", req.Id))
	mergeRecordQuery.Should(autoMergeRecordQuery, manualMergeRecordQuery, calibrationMergeRecordQuery).MinimumNumberShouldMatch(1)

	wg := &sync.WaitGroup{}
	wg.Add(2)
	go func() {
		defer wg.Done()
		// 查询记录总数
		total, err := es.GetCount(assets.NewMergeRecordsModel().IndexName(), mergeRecordQuery)
		if err != nil {
			logs.Warnf("get merge record count by ip(%s) and area(%s) error: %v", asset.Ip, asset.Area, err)
		}
		rsp.Total = total
	}()

	go func() {
		defer wg.Done()
		// 查询当前页的数据，mergeRecordCount 无法作为总数，超过 10000 的数据量，该数据可能不准确
		_, mergeRecordData, err := es.List[assets.MergeRecords](int(req.Page), int(req.PerPage), mergeRecordQuery, sortList, "id", "asset_id", "source_ids", "all_source_ids", "created_at", "updated_at", "merge_mode", "batch_no", "is_device_extracted", "tigger_source_id")
		if err != nil {
			logs.Warnf("get merge record by ip(%s) and area(%s) error: %v", asset.Ip, asset.Area, err)
		} else {
			for _, mrd := range mergeRecordData {
				item := &pb.TraceabilityProcessInfoResponseItem{
					Id:   mrd.Id,
					Time: mrd.CreatedAt.String(),
				}
				if mrd.MergeMode == models.MergeMode_Auto {
					item.Type = TRACEABILITY_PROCESS_DETAIL_TYPE_AUTO_MERGE
					item.Name = "资产自动融合"
					item.TiggerSourceId = mrd.TiggerSourceId
					item.TiggerSource = getSourceFromSourceListById(mrd.TiggerSourceId, sources)
					sourceIds := append(mrd.SourceIds, mrd.AllSourceIds...)
					sourceIds = append(sourceIds, mrd.TiggerSourceId)
					sourceIds = utils2.ListDistinctNonZero(sourceIds)
					item.SourceId = sourceIds
					item.Source = func() []*pb.Source {
						s := make([]*pb.Source, 0)
						for _, sourceId := range sourceIds {
							if sourceId == 0 {
								continue
							}
							s = append(s, getSourceFromSourceListById(sourceId, sources))
						}
						return s
					}()
					if mrd.IsDeviceExtracted == 2 {
						item.Name = "IP不融合，入库显示"
					}
				} else if mrd.MergeMode == models.MergeMode_Manual {
					item.Type = TRACEABILITY_PROCESS_DETAIL_TYPE_MANUAL_MERGE
					item.Name = "资产手动融合"
					item.TiggerSourceId = mrd.TiggerSourceId
					item.TiggerSource = getSourceFromSourceListById(mrd.TiggerSourceId, sources)
					item.Source = []*pb.Source{}
					item.SourceId = []uint64{}
				} else if mrd.MergeMode == models.MergeMode_Calibration {
					item.Type = TRACEABILITY_PROCESS_DETAIL_TYPE_MANUAL_CALIBRATION
					item.Name = "人工校准"
					item.TiggerSourceId = mrd.TiggerSourceId
					item.TiggerSource = getSourceFromSourceListById(mrd.TiggerSourceId, sources)
					item.Source = []*pb.Source{}
					item.SourceId = []uint64{}
				}
				rsp.Process = append(rsp.Process, item)
			}
		}

		// 对 rsp.Process 进行排序
		sortProcess(rsp.Process, "desc")
	}()

	wg.Wait()
	return nil
}

// IpTraceabilityDetailInfo  IP溯源详细信息
func (m *Merge) IpTraceabilityDetailInfo(ctx context.Context, req *pb.TraceabilityDetailInfoRequest, rsp *pb.IpTraceabilityDetailInfoResponse) error {
	logs := logs.GetLogger("service")
	asset, err := es.GetById[assets.Assets](req.Id)
	if err != nil {
		return err
	}

	allSources, err := getAllSources()
	if err != nil {
		logs.Warnf("get all sources error: %v", err)
	}
	if len(allSources) < 1 {
		logs.Warn("no source found")
	}

	// 获取所有区域列表,用于转换结果中补充完整区域信息
	areaList, err := getAllArea()
	if err != nil {
		logs.Warnf("get all area error: %v", err)
	}

	rsp.Items = make([]*pb.IpTraceabilityDetailInfoResponseItem, 0)
	for _, process := range req.ProcessInfo {
		if process.Id == "" {
			continue
		}
		processItem := &pb.IpTraceabilityDetailInfoResponseItem{
			OriginalData:          []*pb.TraceabilityDetailInfoResponseOriginalDataItem{},
			Strategies:            []*pb.TraceabilityDetailInfoResponseStrategyItem{},
			MergedData:            &pb.Asset{},
			AllSourceForMergeData: make(map[string]*pb.Source),
			AllSourceForStrategy:  make(map[string]*pb.Source),
			Type:                  process.Type,
			Id:                    process.Id,
			ListFields:            make([]*pb.MapStringString, 0),
			FieldValInfo:          make([]*pb.TraceabilityDetailInfoResponseFieldValInfo, 0),
		}
		// 查询融合记录
		processData, err := es.GetByIdSafe[assets.MergeRecords](process.Id)
		if err != nil {
			logs.Warnf("get merge record by id(%s) error: %v", process.Id, err)
		}

		for _, v := range processData.FieldValInfoList {
			processItem.FieldValInfo = append(processItem.FieldValInfo, &pb.TraceabilityDetailInfoResponseFieldValInfo{
				FieldName: v.FieldName,
				SourceIds: func() []int64 {
					arr := make([]int64, 0)
					for _, v := range v.SourceIds {
						arr = append(arr, int64(v))
					}
					return arr
				}(),
				NodeIds: func() []int64 {
					arr := make([]int64, 0)
					for _, v := range v.NodeIds {
						arr = append(arr, int64(v))
					}
					return arr
				}(),
				TaskDataIds: v.TaskDataIds,
				ProcessIds:  v.ProcessIds,
			})
		}

		wg := &sync.WaitGroup{}
		wg.Add(1)
		// 策略、源信息数据处理
		go func() {
			defer wg.Done()

			// 按照 sourceId-nodeId 作为 key 存储所有源数据，方便前端展示
			for _, sourceId := range processData.SourceIds {
				for _, nodeId := range processData.NodeIds {
					k := fmt.Sprintf("%d-%d", sourceId, nodeId)
					processItem.AllSourceForMergeData[k] = getSourceFromSourceListById(sourceId, allSources)
				}
			}

			// 按照 sourceId 作为 key 存储所有源数据，方便前端展示
			for _, s := range allSources {
				processItem.AllSourceForStrategy[fmt.Sprintf("%d", s.Id)] = &pb.Source{
					Id:   s.Id,
					Name: s.Name,
					Icon: s.Icon,
				}
			}

			// 融合策略
			if len(processData.Strategies) > 0 {
				for _, strategy := range processData.Strategies {
					item := &pb.TraceabilityDetailInfoResponseStrategyItem{
						BusinessType: strategy.BusinessType,
						FieldName:    strategy.FieldName,
						DisplayName:  strategy.DisplayName,
						SourcePriority: func() map[string]int32 {
							m := make(map[string]int32)
							for k, v := range strategy.SourcePriority {
								m[k] = int32(v)
							}
							return m
						}(),
						UntrustedSource: strategy.UntrustedSource,
						Version:         strategy.Version,
					}
					processItem.Strategies = append(processItem.Strategies, item)
				}
			}
		}()
		if process.Type == TRACEABILITY_PROCESS_DETAIL_TYPE_AUTO_MERGE {
			wg.Add(2)

			// 融合结果数据处理
			go func() {
				defer wg.Done()
				// 融合结果
				assetRecord, err := es.GetById[assets.AssetRecord](processData.AssetRecordId)
				if err != nil {
					logs.Warnf("get asset record by id(%s) error: %v", processData.AssetRecordId, err)
				}
				if assetRecord != nil && assetRecord.Asset != nil {
					processItem.MergedData = convertToPbAsset(assetRecord.Asset, allSources, areaList)
				}
			}()

			// 原始数据处理
			go func() {
				defer wg.Done()
				// 根据融合记录中的资产任务ID查询资产任务数据
				query := elastic.NewTermsQueryFromStrings("_id", processData.AssetTaskIds...)
				searchResult, err := es.GetEsClient().Search().Index("*_task_assets").Query(query).Do(context.Background())
				if err != nil {
					logs.Warnf("get task assets by id(%s) error: %v", processData.AssetTaskIds, err)
				}
				if searchResult == nil {
					logs.Warnf("get task assets by id(%s) not found", processData.AssetTaskIds)
					return
				}
				// 资产任务数据来自不同索引，数据结构不同，直接返回原始数据
				if searchResult.Hits.TotalHits.Value > 0 {
					for _, hit := range searchResult.Hits.Hits {
						sourceId, listFields, _ := getSourceIdByIndexName(hit.Index)
						item := &pb.TraceabilityDetailInfoResponseOriginalDataItem{
							SourceId: hit.Id,
							Source:   getSourceFromSourceListById(sourceId, allSources),
							Data:     string(hit.Source),
							ListFields: func() []*pb.MapStringString {
								arr := make([]*pb.MapStringString, 0)
								for _, field := range listFields {
									item := &pb.MapStringString{
										Fields: field,
									}
									arr = append(arr, item)
								}
								return arr
							}(),
						}
						processItem.OriginalData = append(processItem.OriginalData, item)
					}
				}
			}()
		} else if process.Type == TRACEABILITY_PROCESS_DETAIL_TYPE_MANUAL_MERGE || process.Type == TRACEABILITY_PROCESS_DETAIL_TYPE_MANUAL_CALIBRATION {
			wg.Add(1)

			// 手动融合数据处理
			go func() {
				defer wg.Done()
				// 手动融合结果
				query := elastic.NewBoolQuery()
				query.Must(elastic.NewTermQuery("backup_mode", "diff"))
				query.Must(elastic.NewTermQuery("asset_id", asset.Id))
				query.Must(elastic.NewTermQuery("batch_no", processData.BatchNo))
				assetRecord, err := es.First[assets.AssetRecord](query, nil)
				if err != nil {
					logs.Warnf("get asset record by id(%s) error: %v", processData.AssetRecordId, err)
				}
				if assetRecord != nil && assetRecord.Asset != nil {
					processItem.MergedData = convertToPbAsset(assetRecord.Asset, allSources, areaList)
				}
				if processData.Strategies[0].FieldName == "Business" {
					processItem.ListFields = append(processItem.ListFields, &pb.MapStringString{
						Fields: map[string]string{
							"show_value": utils.CamelToSnake(processData.Strategies[0].FieldName),
							"name":       "业务系统负责人",
							"key":        "owner",
						},
					})
					processItem.ListFields = append(processItem.ListFields, &pb.MapStringString{
						Fields: map[string]string{
							"show_value": utils.CamelToSnake(processData.Strategies[0].FieldName),
							"name":       "业务系统",
							"key":        "system",
						},
					})
				} else if processData.Strategies[0].FieldName == "Ports" {
					processItem.ListFields = append(processItem.ListFields, &pb.MapStringString{
						Fields: map[string]string{
							"show_value": utils.CamelToSnake(processData.Strategies[0].FieldName),
							"name":       "协议",
							"key":        "protocol",
						},
					})
					processItem.ListFields = append(processItem.ListFields, &pb.MapStringString{
						Fields: map[string]string{
							"show_value": utils.CamelToSnake(processData.Strategies[0].FieldName),
							"name":       "URL",
							"key":        "url",
						},
					})
					processItem.ListFields = append(processItem.ListFields, &pb.MapStringString{
						Fields: map[string]string{
							"show_value": utils.CamelToSnake(processData.Strategies[0].FieldName),
							"name":       "网站标题",
							"key":        "title",
						},
					})
					processItem.ListFields = append(processItem.ListFields, &pb.MapStringString{
						Fields: map[string]string{
							"show_value": utils.CamelToSnake(processData.Strategies[0].FieldName),
							"name":       "域名",
							"key":        "domain",
						},
					})
					processItem.ListFields = append(processItem.ListFields, &pb.MapStringString{
						Fields: map[string]string{
							"show_value": utils.CamelToSnake(processData.Strategies[0].FieldName),
							"name":       "端口",
							"key":        "port",
						},
					})
					processItem.ListFields = append(processItem.ListFields, &pb.MapStringString{
						Fields: map[string]string{
							"show_value": utils.CamelToSnake(processData.Strategies[0].FieldName),
							"name":       processData.Strategies[0].DisplayName,
							"key":        "status",
						},
					})
				} else {
					for _, v := range processData.Strategies {
						fieldKey := utils.CamelToSnake(v.FieldName)
						if fieldKey == "tag" {
							fieldKey = "tags"
						}
						if v.DisplayName == "" {
							continue
						}
						processItem.ListFields = append(processItem.ListFields, &pb.MapStringString{
							Fields: map[string]string{
								"name": v.DisplayName,
								"key":  fieldKey,
							},
						})
					}
				}
			}()
		}
		wg.Wait()
		rsp.Items = append(rsp.Items, processItem)
	}

	return nil
}
