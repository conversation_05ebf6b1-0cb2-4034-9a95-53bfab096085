package handler

import (
	"context"
	"errors"
	manual_model "fobrain/mergeService/model/manual_calibration"
	pb "fobrain/mergeService/proto"
	"testing"
	"time"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/stretchr/testify/assert"
)

func TestMerge_TestMethod(t *testing.T) {
	m := &Merge{}
	ctx := context.Background()

	// Happy path test case
	t.Run("HappyPath", func(t *testing.T) {
		req := &pb.TestMethodRequest{Name: "John"}
		rsp := &pb.TestMethodResponse{}
		err := m.TestMethod(ctx, req, rsp)
		assert.NoError(t, err)
		assert.Equal(t, "Hello John", rsp.Welcome)
	})

	// Edge case: Empty name
	t.Run("EmptyName", func(t *testing.T) {
		req := &pb.TestMethodRequest{Name: ""}
		rsp := &pb.TestMethodResponse{}
		err := m.TestMethod(ctx, req, rsp)
		assert.Error(t, err)
		assert.Equal(t, "please enter your name", err.Error())
	})

	// Edge case: Name with spaces
	t.Run("NameWithSpaces", func(t *testing.T) {
		req := &pb.TestMethodRequest{Name: "   "}
		rsp := &pb.TestMethodResponse{}
		err := m.TestMethod(ctx, req, rsp)
		assert.NoError(t, err)
		assert.Equal(t, "Hello    ", rsp.Welcome)
	})

	// Edge case: Long name
	t.Run("LongName", func(t *testing.T) {
		longName := "A"
		for i := 0; i < 1000; i++ {
			longName += "A"
		}
		req := &pb.TestMethodRequest{Name: longName}
		rsp := &pb.TestMethodResponse{}
		err := m.TestMethod(ctx, req, rsp)
		assert.NoError(t, err)
		assert.Equal(t, "Hello "+longName, rsp.Welcome)
	})
}

func TestMerge_ManualCalibration(t *testing.T) {
	m := &Merge{}
	ctx := context.Background()

	patch := gomonkey.NewPatches()
	patch.ApplyMethodReturn(&manual_model.ManualCalibrationModel{}, "GetCustomKeys", []string{"custom_key"})
	defer patch.Reset()

	tests := []struct {
		name    string
		req     *pb.ManualCalibrationRequest
		wantErr bool
		setup   func(patches *gomonkey.Patches)
	}{
		/*
			{
				name: "成功校准",
				req: &pb.ManualCalibrationRequest{
					BusinessType: "asset",
					BatchNo:      "test_batch",
					Ids:          []string{"1", "2", "3"},
					Values:       map[string]string{"key": "value"},
				},
				wantErr: false,
				setup: func(patches *gomonkey.Patches) {
					patches.ApplyFunc(manual_model.GetAssetAllowKeys, func() []string {
						return []string{"key"}
					})
					manualCalibration := &manual_model.ManualCalibrationModel{}
					patches.ApplyMethodReturn(manualCalibration, "ConvertValue",
						map[string]interface{}{"key": "value"}, map[string]interface{}{"key_source": "value"}, nil)
					patches.ApplyMethodReturn(manualCalibration, "Check", nil)
					patches.ApplyMethodReturn(manualCalibration, "BatchUpdate", nil)
					patches.ApplyFuncReturn(model.ExecuteReindexWithScript, int64(3), nil)
					patches.ApplyFuncReturn(manual_model.GetDisplayName, "Display Name")
					patches.ApplyFuncReturn(model.CreateMergeRecord, nil)
				},
			},

		*/
		{
			name: "无效的业务类型",
			req: &pb.ManualCalibrationRequest{
				BusinessType: "invalid",
				BatchNo:      "test_batch",
				Ids:          []string{"1", "2", "3"},
				Values:       map[string]string{"key": "value"},
			},
			wantErr: true,
			setup:   func(patches *gomonkey.Patches) {},
		},
		{
			name: "ConvertValue 失败",
			req: &pb.ManualCalibrationRequest{
				BusinessType: "asset",
				BatchNo:      "test_batch",
				Ids:          []string{"1", "2", "3"},
				Values:       map[string]string{"key": "value"},
			},
			wantErr: true,
			setup: func(patches *gomonkey.Patches) {
				patches.ApplyFunc(manual_model.GetAssetAllowKeys, func() []string {
					return []string{"key"}
				})
				manualCalibration := &manual_model.ManualCalibrationModel{}
				patches.ApplyMethodReturn(manualCalibration, "ConvertValue",
					nil, nil, errors.New("convert error"))
			},
		},
		// 可以添加更多测试用例，如 Check 失败、BatchUpdate 失败等
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			time.Sleep(time.Second)
			patches := gomonkey.NewPatches()

			tt.setup(patches)

			rsp := &pb.ManualCalibrationResponse{}

			err := m.ManualCalibration(ctx, tt.req, rsp)

			patches.Reset()
			if tt.wantErr {
				assert.Error(t, err)
				assert.False(t, rsp.Success)
			} else {
				assert.NoError(t, err)
				assert.True(t, rsp.Success)
			}
		})
	}
}
