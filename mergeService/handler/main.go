package handler

import (
	"context"
	"fmt"
	"fobrain/models/elastic/assets"
	"strconv"
	"strings"
	"time"

	elastic2 "github.com/olivere/elastic/v7"
	"github.com/pkg/errors"

	"fobrain/initialize/es"
	"fobrain/mergeService/event"
	"fobrain/mergeService/model"
	"fobrain/mergeService/model/field_tagger"
	manual_model "fobrain/mergeService/model/manual_calibration"
	"fobrain/mergeService/model/trigger_merge"
	pb "fobrain/mergeService/proto"
	"fobrain/mergeService/utils"
	logs "fobrain/mergeService/utils/log"
	"fobrain/models/elastic"
	"fobrain/models/mysql/field_tag_rules"
	"fobrain/models/mysql/strategy"
	distributedlock "fobrain/pkg/distributedLock"
	"fobrain/pkg/utils/common_logs"
)

var logger *common_logs.Logger

func init() {
	logger = logs.GetLogger("service")
}

type Merge struct{}

func (m *Merge) TestMethod(ctx context.Context, req *pb.TestMethodRequest, rsp *pb.TestMethodResponse) error {
	if req.Name == "" {
		// 返回错误
		return fmt.Errorf("please enter your name")
	}

	// 正常情况只需要把返回值设置到 rsp 对象，并且返回 nil 即可
	rsp.Welcome = "Hello " + req.Name
	return nil
}

// TriggerFieldTagger 触发字段打标签
func (m *Merge) TriggerFieldTagger(ctx context.Context, req *pb.TriggerFieldTaggerRequest, rsp *pb.TriggerFieldTaggerResponse) error {
	ft := field_tagger.New(field_tagger.WithGetElasticClient(es.GetEsClient))
	err := ft.TriggerOnce(ctx, &field_tag_rules.SetFieldType{
		Field:     req.Field,
		Condition: req.Condition,
		Value:     req.Value,
		SetField:  req.SetField,
		SetValue:  req.SetValue,
	})
	if err != nil {
		rsp.Success = false
		rsp.Message = err.Error()
		return nil
	}
	rsp.Success = true
	return nil
}

// TriggerMergeForAsset 触发资产数据融合
func (m *Merge) TriggerMergeForAsset(ctx context.Context, req *pb.TriggerMergeForAssetRequest, rsp *pb.TriggerMergeResponse) error {
	triggerParams := &trigger_merge.TriggerParamsForAsset{
		TriggerParams: trigger_merge.TriggerParams{
			TriggerSourceId: req.TriggerMergeBaseRequest.SourceId,
			TriggerNodeId:   req.TriggerMergeBaseRequest.NodeId,
			TriggerEvent:    req.TriggerMergeBaseRequest.TriggerEvent,
			TaskId:          req.TriggerMergeBaseRequest.TaskId,
			ChildTaskId:     req.TriggerMergeBaseRequest.ChildTaskId,
			SubTrigger:      int8(req.SubTrigger),
		},
		AssetIds: req.AssetIds,
		IpInfos: func() []*trigger_merge.IpInfo {
			var ipInfos []*trigger_merge.IpInfo
			for _, ipInfo := range req.IpInfos {
				ipInfos = append(ipInfos, &trigger_merge.IpInfo{
					Ip:   ipInfo.Ip,
					Area: ipInfo.Area,
				})
			}
			return ipInfos
		}(),
		DataRangeByTask: func() *trigger_merge.DataRangeByTask {
			if req.DataRangeByTask == nil {
				return nil
			}
			return &trigger_merge.DataRangeByTask{
				TaskId:      req.DataRangeByTask.TaskId,
				ChildTaskId: req.DataRangeByTask.ChildTaskId,
				NodeId:      req.DataRangeByTask.NodeId,
			}
		}(),
		Fields: req.Fields,
	}
	err := trigger_merge.TriggerMergeForAsset(triggerParams)
	if err != nil {
		rsp.Success = false
		rsp.Message = err.Error()
		return nil
	}
	rsp.Success = true
	return nil
}

// TriggerMergeForVul 触发漏洞数据融合
func (m *Merge) TriggerMergeForVuln(ctx context.Context, req *pb.TriggerMergeForVulnRequest, rsp *pb.TriggerMergeResponse) error {
	triggerParams := &trigger_merge.TriggerParamsForVuln{
		TriggerParams: trigger_merge.TriggerParams{
			TriggerSourceId: req.TriggerMergeBaseRequest.SourceId,
			TriggerNodeId:   req.TriggerMergeBaseRequest.NodeId,
			TriggerEvent:    req.TriggerMergeBaseRequest.TriggerEvent,
			TaskId:          req.TriggerMergeBaseRequest.TaskId,
			ChildTaskId:     req.TriggerMergeBaseRequest.ChildTaskId,
			SubTrigger:      int8(req.SubTrigger),
		},
		VulnIds: req.VulIds,
		DataRangeByTask: func() *trigger_merge.DataRangeByTask {
			if req.DataRangeByTask == nil {
				return nil
			}
			return &trigger_merge.DataRangeByTask{
				TaskId:      req.DataRangeByTask.TaskId,
				ChildTaskId: req.DataRangeByTask.ChildTaskId,
				NodeId:      req.DataRangeByTask.NodeId,
			}
		}(),
		Fields: req.Fields,
	}
	err := trigger_merge.TriggerMergeForVuln(triggerParams)
	if err != nil {
		rsp.Success = false
		rsp.Message = err.Error()
		return nil
	}
	rsp.Success = true
	return nil
}

// TriggerMergeForStaff 触发人员数据融合
func (m *Merge) TriggerMergeForStaff(ctx context.Context, req *pb.TriggerMergeForStaffRequest, rsp *pb.TriggerMergeResponse) error {
	triggerParams := &trigger_merge.TriggerParamsForStaff{
		TriggerParams: trigger_merge.TriggerParams{
			TriggerSourceId: req.TriggerMergeBaseRequest.SourceId,
			TriggerNodeId:   req.TriggerMergeBaseRequest.NodeId,
			TriggerEvent:    req.TriggerMergeBaseRequest.TriggerEvent,
			TaskId:          req.TriggerMergeBaseRequest.TaskId,
			ChildTaskId:     req.TriggerMergeBaseRequest.ChildTaskId,
			SubTrigger:      int8(req.SubTrigger),
		},
		StaffIds: req.StaffIds,
		DataRangeByTask: func() *trigger_merge.DataRangeByTask {
			if req.DataRangeByTask == nil {
				return nil
			}
			return &trigger_merge.DataRangeByTask{
				TaskId:      req.DataRangeByTask.TaskId,
				ChildTaskId: req.DataRangeByTask.ChildTaskId,
				NodeId:      req.DataRangeByTask.NodeId,
			}
		}(),
		Fields: req.Fields,
	}
	err := trigger_merge.TriggerMergeForStaff(triggerParams)
	if err != nil {
		rsp.Success = false
		rsp.Message = err.Error()
		return nil
	}
	rsp.Success = true
	return nil
}

// TriggerMergeForDevice 触发设备数据融合
func (m *Merge) TriggerMergeForDevice(ctx context.Context, req *pb.TriggerMergeForDeviceRequest, rsp *pb.TriggerMergeResponse) error {
	triggerParams := &trigger_merge.TriggerParamsForDevice{
		TriggerParams: trigger_merge.TriggerParams{
			TriggerSourceId: req.TriggerMergeBaseRequest.SourceId,
			TriggerNodeId:   req.TriggerMergeBaseRequest.NodeId,
			TriggerEvent:    req.TriggerMergeBaseRequest.TriggerEvent,
			TaskId:          req.TriggerMergeBaseRequest.TaskId,
			ChildTaskId:     req.TriggerMergeBaseRequest.ChildTaskId,
			SubTrigger:      int8(req.SubTrigger),
		},
		DeviceIds: req.DeviceIds,
		DataRangeByTask: func() *trigger_merge.DataRangeByTask {
			if req.DataRangeByTask == nil {
				return nil
			}
			return &trigger_merge.DataRangeByTask{
				TaskId:      req.DataRangeByTask.TaskId,
				ChildTaskId: req.DataRangeByTask.ChildTaskId,
				NodeId:      req.DataRangeByTask.NodeId,
			}
		}(),
		Fields: req.Fields,
	}
	err := trigger_merge.TriggerMergeForDevice(triggerParams)
	if err != nil {
		rsp.Success = false
		rsp.Message = err.Error()
		return nil
	}
	rsp.Success = true
	return nil
}

// UpdateMergeDataToTask 更新合并数据到任务
func (m *Merge) UpdateMergeDataToTask(ctx context.Context, req *pb.UpdateMergeDataToTaskRequest, rsp *pb.UpdateMergeDataToTaskResponse) error {
	taskId := strconv.FormatInt(req.TaskId, 10)
	totalCount, successCount, failedCount, discardedCount, start, end, workHours, duration, err := model.UpdateMergeInfoToTask(req.RecordId, req.TaskType, taskId, req.BusinessType, false)
	if err != nil {
		return err
	}
	rsp.Count = int32(totalCount)
	rsp.SuccessCount = int32(successCount)
	rsp.FailedCount = int32(failedCount)
	rsp.DiscardedCount = int32(discardedCount)
	rsp.StartTime = start.Format("2006-01-02 15:04:05")
	rsp.EndTime = end.Format("2006-01-02 15:04:05")
	rsp.WorkHours = workHours.Milliseconds()
	rsp.Duration = duration.Milliseconds()
	return nil
}

// ManualMerge 融合策略修改后，手动合并，立即融合
func (m *Merge) ManualMerge(ctx context.Context, req *pb.ManualMergeRequest, rsp *pb.ManualMergeResponse) error {
	logger.Infof("ManualMerge, start: %s, batchNo: %s", time.Now().Format("2006-01-02 15:04:05"), req.BatchNo)
	rsp.BatchNo = req.BatchNo
	rsp.Message = "OK"
	hasUnlocked := false
	// 通过分布式锁，锁定索引-立即执行合并，防止重复执行
	lockKey := fmt.Sprintf("lock:merge_%s", req.BusinessType)
	if lockRes := distributedlock.Lock(lockKey, req.BatchNo, 200); !lockRes {
		logger.Warnf("手动合并已在执行中, batchNo: %s, lockKey: %s", req.BatchNo, lockKey)
		return fmt.Errorf("合并已在执行中")
	}
	defer func() {
		if !hasUnlocked {
			// 释放锁
			if unlockRes := distributedlock.Unlock(lockKey, req.BatchNo); !unlockRes {
				logger.Errorf("释放锁失败, lockKey: %s, batchNo: %s", lockKey, req.BatchNo)
			}
		}
	}()

	hasAssetBusinessField := false
	for _, field := range req.Fields {
		if strings.ToLower(field) == "business" && req.BusinessType == model.MergeFlowType_Asset {
			hasAssetBusinessField = true
			break
		}
	}
	if hasAssetBusinessField {
		err := trigger_merge.TriggerMergeForAsset(&trigger_merge.TriggerParamsForAsset{
			TriggerParams: trigger_merge.TriggerParams{
				TriggerSourceId: 0,
				TriggerNodeId:   0,
				TriggerEvent:    "业务系统融合策略修改",
				TaskId:          req.BatchNo,
				ChildTaskId:     req.BatchNo,
			},
		})
		if err != nil {
			return fmt.Errorf("资产业务系统融合策略修改触发融合失败, %v", err.Error())
		}
	} else {
		sussessField := make([]string, 0)
		rsp.Results = make([]*pb.ManualMergeResult, 0)
		for _, field := range req.Fields {

			res := &pb.ManualMergeResult{
				Field: field,
			}
			// 查询最新融合策略，错误则跳过
			s, err := strategy.NewStrategyModel().GetLatestByField(req.BusinessType+"_merge", field)
			if err != nil {
				res.Success = false
				res.Message = "未找到策略."
				rsp.Results = append(rsp.Results, res)
				continue
			}
			// 触发脚本合并，不论脚本执行结果如何，一定会往后执行创建合并记录
			mergeStatus := true
			sourcePriority := make(map[uint64][]string)
			for sid, pri := range s.SourcePriority {
				// 添加到可信源列表中
				sourcePriority[pri] = append(sourcePriority[pri], sid)
			}
			updateCount, err := model.ExecuteMergeWithScript(req.BusinessType, field, req.BatchNo, sourcePriority)
			if err != nil {
				mergeStatus = false
				res.Success = false
				res.Message = func() string {
					if errors.Is(context.DeadlineExceeded, err) {
						return "数据更新超时."
					}
					return fmt.Sprintf("数据更新失败,err:%s.", err.Error())
				}()
			} else {
				res.Updated = updateCount
				res.Message = "数据更新成功."
				res.Success = true
				sussessField = append(sussessField, utils.CamelToSnake(field))
			}

			// 创建合并记录，错误则跳过
			err = model.CreateMergeRecord(req.BusinessType, req.BatchNo, elastic.MergeMode_Manual, []string{}, []*strategy.Strategy{s}, mergeStatus)
			if err != nil {
				res.Success = false
				res.Message = res.Message + "创建合并记录失败"
				rsp.Results = append(rsp.Results, res)
				continue
			} else {
				if mergeStatus {
					res.Success = true
					res.Message = "立即融合成功."
					rsp.Results = append(rsp.Results, res)
				} else {
					rsp.Results = append(rsp.Results, res)
				}
			}
		}
		if len(sussessField) > 0 {
			// 创建数据快照
			backupCount, err := model.ExecuteReindexWithScript(req.BusinessType, req.BatchNo, sussessField, nil)
			if err != nil {
				logger.Errorf("数据快照创建失败，err: %v", err)
				rsp.Message = "数据更新完成，但是数据快照创建失败，可能影响数据溯源."
			}
			logger.Infof("ManualMerge, backupCount: %d", backupCount)
		}
	}

	// 释放锁
	if unlockRes := distributedlock.Unlock(lockKey, req.BatchNo); !unlockRes {
		logger.Errorf("释放锁失败, lockKey: %s, batchNo: %s", lockKey, req.BatchNo)
		rsp.Message = "立即融合完成，但是释放锁失败，最多 200 秒内无法再次执行立即融合."
	}
	hasUnlocked = true
	logger.Infof("ManualMerge, end: %s, batchNo: %s, results: %v", time.Now().Format("2006-01-02 15:04:05"), req.BatchNo, rsp.Results)
	return nil
}

// ManualCalibration 手动校准
func (m *Merge) ManualCalibration(ctx context.Context, req *pb.ManualCalibrationRequest, rsp *pb.ManualCalibrationResponse) error {
	logger.Infof("ManualCalibration, start: %s, batchNo: %s", time.Now().Format("2006-01-02 15:04:05"), req.BatchNo)
	manualCalibration := &manual_model.ManualCalibrationModel{}
	manualCalibration.BusinessType = req.BusinessType
	manualCalibration.Ids = req.Ids
	// 检查数据是否合法
	allowKeys := make([]string, 0)
	// 获取允许的key
	switch req.BusinessType {
	case "asset":
		allowKeys = manual_model.GetAssetAllowKeys()
	case "device":
		allowKeys = manual_model.GetDeviceAllowKeys()
	case "vuln":
		allowKeys = manual_model.GetVulnAllowKeys()
	case "person":
		allowKeys = manual_model.GetPersonAllowKeys()
	default:
		return fmt.Errorf("business_type不合法")
	}
	// 获取custom字段
	customKeys := manualCalibration.GetCustomKeys()
	// 检查数据是否合法
	err := manualCalibration.Check(allowKeys, customKeys, req.Ids, req.Values)
	if err != nil {
		logger.Errorf("数据校验失败，err: %v", err)
		rsp.Success = false
		rsp.Message = err.Error()
		return err
	}
	// 转换数据
	values, valuesSource, err := manualCalibration.ConvertValue(req.Values)
	if err != nil {
		logger.Errorf("数据转换失败，err: %v", err)
		rsp.Success = false
		rsp.Message = err.Error()
		return err
	}

	// 触发人工校准事件
	eventSource := make(map[string]interface{})
	eventData := make(map[string]interface{})
	err = event.NewEventBus().Emit(event.Event_Manual_Calibration, req.BusinessType, req.Ids, values, eventData, eventSource)
	if err != nil {
		logger.Errorf("触发人工校准事件失败，err: %v", err)
		rsp.Success = false
		rsp.Message = err.Error()
		return err
	}

	// 合并values和eventData
	for key, value := range eventData {
		values[key] = value
	}
	for key, value := range eventSource {
		valuesSource[key] = value
	}
	manualCalibration.Values = values
	manualCalibration.ValuesSource = valuesSource

	// 批量更新数据
	err = manualCalibration.BatchUpdate()
	if err != nil {
		logger.Errorf("数据更新失败，err: %v", err)
		rsp.Success = false
		rsp.Message = err.Error()
		return err
	}
	// 获取字段
	fields := make([]string, 0)
	for key := range manualCalibration.Values {
		fields = append(fields, key)
	}
	// 备份数据
	backupCount, err := model.ExecuteReindexWithScript(req.BusinessType, req.BatchNo, fields, req.Ids)
	if err != nil {
		logger.Errorf("数据快照创建失败，err: %v", err)
		rsp.Message = "数据更新完成，但是数据快照创建失败，可能影响数据溯源."
	}
	logger.Infof("ManualCalibration, backupCount: %d", backupCount)
	// 创建策略
	strategies := make([]*strategy.Strategy, 0)
	for _, field := range fields {
		strategyModel := &strategy.Strategy{}
		strategyModel.BusinessType = req.BusinessType + "_merge"
		strategyModel.SourcePriority = make(strategy.SourcePriority)
		strategyModel.SourcePriority["calibration"] = 0
		strategyModel.UntrustedSource = make(strategy.UntrustedSource, 0)
		strategyModel.FieldName = field
		strategyModel.DisplayName = manual_model.GetDisplayName(req.BusinessType, field)
		strategies = append(strategies, strategyModel)
	}
	// 创建合并记录
	err = model.CreateMergeRecord(req.BusinessType, req.BatchNo, elastic.MergeMode_Calibration, req.Ids, strategies, true)
	if err != nil {
		logger.Errorf("创建合并记录失败，err: %v", err)
		rsp.Message = "数据更新完成，但是创建合并记录失败，可能影响数据溯源."
	}
	logger.Infof("ManualCalibration, end: %s, batchNo: %s", time.Now().Format("2006-01-02 15:04:05"), req.BatchNo)
	switch req.BusinessType {
	case "asset": // 资产校准后会影响关联设备的数据权限，运维人员、部门，业务系统等，触发局部融合
		query := elastic2.NewBoolQuery().Must(elastic2.NewTermsQueryFromStrings("id", req.Ids...))
		all, err := es.All[assets.Assets](1000, query, nil, "id", "ip", "area")
		if err != nil {
			return err
		}
		if all != nil && len(all) > 0 {
			affected := make(map[string]struct{})
			for _, asset := range all {
				key := fmt.Sprintf("%s_%d", asset.Ip, asset.Area)
				affected[key] = struct{}{}
			}
			deviceIds, err := model.FetchDeviceIds(ctx, affected)
			if err != nil {
				logger.Errorf("获取设备id失败，err: %v", err)
			} else if len(deviceIds) > 0 {
				err = trigger_merge.TriggerMergeForDevice(&trigger_merge.TriggerParamsForDevice{
					TriggerParams: trigger_merge.TriggerParams{
						TriggerSourceId: 0,
						TriggerNodeId:   0,
						TriggerEvent:    "资产校准",
						TaskId:          fmt.Sprintf("device_merge_by_asset_%s", req.BatchNo),
						ChildTaskId:     fmt.Sprintf("%d", time.Now().Unix()),
					},
					DeviceIds: deviceIds,
				})
				if err != nil {
					logger.Warnf("businessType: %s, 批次ID: %v, 二次触发关联融合失败. err: %v", req.BusinessType, req.BatchNo, err)
				}
			}
			pocIds, err := model.FetchPocIds(ctx, affected)
			if err != nil {
				logger.Errorf("获取设备id失败，err: %v", err)
			} else if len(pocIds) > 0 {
				err = trigger_merge.TriggerMergeForVuln(&trigger_merge.TriggerParamsForVuln{
					TriggerParams: trigger_merge.TriggerParams{
						TriggerSourceId: 0,
						TriggerNodeId:   0,
						TriggerEvent:    "资产校准",
						TaskId:          fmt.Sprintf("poc_merge_by_asset_%s", req.BatchNo),
						ChildTaskId:     fmt.Sprintf("%d", time.Now().Unix()),
					},
					VulnIds: pocIds,
				})
				if err != nil {
					logger.Warnf("businessType: %s, 批次ID: %v, 二次触发关联融合失败. err: %v", req.BusinessType, req.BatchNo, err)
				}
			}
		}
	}
	// 2025-02-14，手动校准数据，不触发融合，因为想不出触发的必要性
	// 触发融合
	// scope := []string{req.BusinessType}
	// if req.BusinessType == "asset" {
	// 	scope = []string{"asset", "device"}
	// }
	// trigger_merge.TriggerMerge(0, 0, "手动校准数据", req.BatchNo, req.BatchNo, "", "", scope)

	rsp.Success = true
	return nil
}

// RecalRiskLevel 重新计算漏洞风险值
func (m *Merge) RecalRiskLevel(ctx context.Context, req *pb.Empty, rsp *pb.RecalRiskLevelResponse) error {
	batchNo := time.Now().Format("20060102150405")
	count, err := model.BatchCalculateRiskLevel(true)
	logger.Infof("重新计算漏洞风险值，受影响的数据量: %d", count)
	if err != nil {
		rsp.Success = false
		rsp.Message = fmt.Sprintf("重新计算风险值失败. err: %s", err.Error())
		return err
	}
	if count > 0 {
		// 创建数据快照
		backupCount, err := model.ExecuteReindexWithScript(model.MergeFlowType_Vulnerability, batchNo, []string{"risk_num", "repair_priority"}, nil)
		if err != nil {
			logger.Errorf("数据快照创建失败，err: %v", err)
			rsp.Message = "数据更新完成，但是数据快照创建失败，可能影响数据溯源."
		}
		logger.Infof("RecalRiskLevel, backupCount: %d", backupCount)

		// 创建策略
		strategies := make([]*strategy.Strategy, 2)
		strategyModel := &strategy.Strategy{}
		strategyModel.BusinessType = "vuln_merge"
		strategyModel.SourcePriority = make(strategy.SourcePriority)
		strategyModel.SourcePriority["calibration"] = 0
		strategyModel.UntrustedSource = make(strategy.UntrustedSource, 0)
		strategyModel.FieldName = "risk_num"
		strategyModel.DisplayName = "风险值"
		strategies = append(strategies, strategyModel)
		strategyModel = &strategy.Strategy{}
		strategyModel.BusinessType = "vuln_merge"
		strategyModel.SourcePriority = make(strategy.SourcePriority)
		strategyModel.SourcePriority["calibration"] = 0
		strategyModel.UntrustedSource = make(strategy.UntrustedSource, 0)
		strategyModel.FieldName = "repair_priority"
		strategyModel.DisplayName = "修复优先级"
		strategies = append(strategies, strategyModel)

		// 创建合并记录
		err = model.CreateMergeRecord(model.MergeFlowType_Vulnerability, batchNo, elastic.MergeMode_Update, []string{}, strategies, true)
		if err != nil {
			logger.Errorf("创建合并记录失败，err: %v", err)
			rsp.Message = "数据更新完成，但是创建合并记录失败，可能影响数据溯源."
		}
	}
	rsp.Success = true
	return nil
}
