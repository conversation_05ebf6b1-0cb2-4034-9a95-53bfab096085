package handler

import (
	"fobrain/models/elastic/assets"
	"fobrain/pkg/utils"
)

// AssetPermissionFieldsHandler 资产权限字段处理
func AssetPermissionFieldsHandler(data *assets.Assets, existData *assets.Assets) error {
	//运维责任人及部门相关字段数据处理
	operIds := make([]string, 0)
	operDepartments := make([]uint64, 0)
	for _, oper := range data.OperInfo {
		if oper.Id != "" {
			operIds = append(operIds, oper.Id)
		}
		for _, department := range oper.Department {
			if department.Id != 0 {
				operDepartments = append(operDepartments, department.Id)
			}
		}
	}
	data.OperStaffIds = utils.ListDistinctNonZero(operIds)
	data.OperDepartmentIds = utils.ListDistinctNonZero(operDepartments)

	return nil
}
