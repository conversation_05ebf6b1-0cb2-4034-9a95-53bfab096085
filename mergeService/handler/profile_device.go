package handler

import (
	"context"
	"fmt"
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/network_areas"
	"sync"

	"fobrain/initialize/es"
	pb "fobrain/mergeService/proto"
	"fobrain/mergeService/utils"
	logs "fobrain/mergeService/utils/log"
	models "fobrain/models/elastic"
	esmodel_device "fobrain/models/elastic/device"

	"github.com/olivere/elastic/v7"
)

func (m *Merge) DeviceBaseInfo(ctx context.Context, req *pb.DeviceInfoRequest, rsp *pb.DeviceBaseInfoResponse) error {
	logs := logs.GetLogger("service")
	device, err := es.GetById[esmodel_device.Device](req.Id)
	if err != nil {
		return err
	}
	wg := &sync.WaitGroup{}
	wg.Add(2)

	// 区域信息
	go func() {
		defer wg.Done()
		rsp.AreaId = utils.SlicesConvertUint64(device.Area)
		rsp.Area = make([]*pb.Area, 0)
		if len(device.Area) > 0 {
			// 获取所有区域信息
			allNetworkArea, _ := getAllArea()
			if len(allNetworkArea) > 0 {
				// 分析区域，拼接区域详情
				for _, id := range device.Area {
					areaId := uint64(id)
					area := getAreaFromAreaListById(areaId, allNetworkArea)
					if area == nil {
						logs.Warnf("areaId: %d not found", areaId)
						continue
					}
					rsp.Area = append(rsp.Area, area)
				}
			}
		}
	}()
	// 数据源信息
	go func() {
		defer wg.Done()
		rsp.SourceId = device.AllSourceIds
		rsp.Source = make([]*pb.Source, 0)
		if len(device.AllSourceIds) > 0 {
			// 获取所有数据源信息
			sources, _ := getAllSources()
			if len(sources) > 0 {
				// 分析数据源，拼接数据源详情
				for _, sourceId := range device.AllSourceIds {
					source := getSourceFromSourceListById(sourceId, sources)
					if source == nil {
						logs.Warnf("sourceId: %d not found", sourceId)
						continue
					}
					rsp.Source = append(rsp.Source, source)
				}
			}
		}
	}()
	rsp.Hostname = func() []string {
		if len(device.HostName) > 0 {
			return device.HostName
		}
		return []string{}
	}()
	rsp.Sn = device.Sn
	rsp.Mac = device.Mac
	rsp.Ip = device.Ip
	rsp.Os = device.Os
	rsp.MachineRoom = device.MachineRoom
	rsp.Oper = device.Oper
	rsp.Tags = device.Tags

	wg.Wait()
	return nil
}

func (m *Merge) DeviceRelatedIpInfo(ctx context.Context, req *pb.DeviceInfoRequest, rsp *pb.DeviceRelatedIpInfoResponse) error {
	logs := logs.GetLogger("service")
	// 获取设备信息
	device, err := es.GetById[esmodel_device.Device](req.Id)
	if err != nil {
		return err
	}
	allIpList := append(device.PrivateIp, device.PublicIp...)
	if len(allIpList) == 0 {
		logs.Warnf("device %s ip is empty", device.Id)
		return nil
	}
	// 根据设备IP获取资产信息
	ipList, _, err := getAssetList(1, len(allIpList), allIpList, device.Area)
	if err != nil {
		logs.Warnf("device %s search related ip error: %v", device.Id, err)
		return err
	}
	// 将资产信息转换为响应格式，包括私有IP和公有IP
	rsp.IpList = make(map[string]*pb.ListAsset)
	rsp.IpList["private"] = &pb.ListAsset{
		Assets: []*pb.Asset{},
	}
	rsp.IpList["public"] = &pb.ListAsset{
		Assets: []*pb.Asset{},
	}
	// 遍历资产信息
	for _, existIp := range ipList {
		if existIp.NetworkType == 1 {
			// 将私有IP对应的资产添加到私有IP列表中
			rsp.IpList["private"].Assets = append(rsp.IpList["private"].Assets, existIp)
		} else {
			// 将公有IP对应的资产添加到公有IP列表中
			rsp.IpList["public"].Assets = append(rsp.IpList["public"].Assets, existIp)
		}
	}
	return nil
}

func (m *Merge) DeviceRelatedEthInfo(ctx context.Context, req *pb.DeviceInfoRequest, rsp *pb.DeviceRelatedEthInfoResponse) error {
	// 获取设备信息
	device, err := es.GetById[esmodel_device.Device](req.Id)
	if err != nil {
		return err
	}
	rsp.EthList = make([]*pb.DeviceRelatedEthInfoItem, 0)
	for _, eth := range device.NetworkCards {
		rsp.EthList = append(rsp.EthList, &pb.DeviceRelatedEthInfoItem{
			Name:      eth.Name,
			Mac:       eth.Mac,
			Ipv4:      eth.Ipv4,
			Ipv6:      eth.Ipv6,
			Gateway:   eth.Gateway,
			Netmask:   eth.Netmask,
			DnsServer: eth.DnsServer,
			Broadcast: eth.Broadcast,
			Speed:     eth.Speed,
			Status: func() string {
				if eth.Status == "" {
					return "未知"
				}
				if eth.Status == "1" {
					return "已连接"
				}
				return "未连接"
			}(),
		})
	}
	return nil
}

func (m *Merge) DeviceHostInfo(ctx context.Context, req *pb.DeviceInfoRequest, rsp *pb.HostInfo) error {
	device, err := es.GetById[esmodel_device.Device](req.Id)
	if err != nil {
		return err
	}
	rsp.Maker = device.Maker
	rsp.Model = device.Model
	rsp.Sn = device.Sn

	rsp.Os = device.Os
	rsp.OsKernel = device.Kernel

	rsp.MemorySize = device.MemorySize
	rsp.MemoryUsageRate = device.MemoryUsageRate

	rsp.CpuMaker = device.CpuMaker
	rsp.CpuBrand = device.CpuBrand
	rsp.CpuCount = utils.SlicesConvertInt32(device.CpuCount)
	rsp.LoadAverage = device.LoadAverage

	rsp.DiskCount = utils.SlicesConvertInt32(device.DiskCount)
	rsp.DiskSize = utils.SlicesConvertInt32(device.DiskSize)
	rsp.DiskUsageRate = device.DiskUsageRate

	return nil
}

func (m *Merge) DeviceTraceabilityBaseInfo(ctx context.Context, req *pb.DeviceInfoRequest, rsp *pb.DeviceTraceabilityBaseInfoResponse) error {
	logs := logs.GetLogger("service")
	device, err := es.GetById[esmodel_device.Device](req.Id)
	if err != nil {
		return err
	}
	wg := &sync.WaitGroup{}
	wg.Add(1)
	// 数据源信息
	go func() {
		defer wg.Done()
		allSources, err := getAllSources()
		if err != nil {
			logs.Warnf("get all sources error: %v", err)
		}
		rsp.Source = make([]*pb.Source, 0)
		rsp.AllSource = make([]*pb.Source, 0)
		for _, sourceId := range device.SourceIds {
			if sourceId == 0 {
				continue
			}
			rsp.Source = append(rsp.Source, getSourceFromSourceListById(sourceId, allSources))
		}
		for _, sourceId := range device.AllSourceIds {
			if sourceId == 0 {
				continue
			}
			rsp.AllSource = append(rsp.AllSource, getSourceFromSourceListById(sourceId, allSources))
		}
	}()

	rsp.AllSourceId = device.AllSourceIds
	rsp.SourceId = device.SourceIds
	rsp.Fid = device.Fid
	rsp.FirstMergeTime = device.CreatedAt.String()
	rsp.LastMergeTime = func() string {
		if device.UpdatedAt == nil {
			return device.CreatedAt.String()
		}
		return device.UpdatedAt.String()
	}()

	wg.Wait()
	return nil
}

func (m *Merge) DeviceTraceabilityProcessInfo(ctx context.Context, req *pb.TraceabilityProcessInfoRequest, rsp *pb.TraceabilityProcessInfoResponse) error {
	logs := logs.GetLogger("service")
	deviceData, err := es.GetById[esmodel_device.Device](req.Id)
	if err != nil {
		return err
	}

	// 查询所有数据源, 用于给出所有数据源的名称和图标
	sources, sourceCount, err := data_source.NewSourceModel().Items(1, 500)
	if err != nil {
		logs.Warnf("get all sources error: %v", err)
	}
	if sourceCount < 1 {
		logs.Warn("no source found")
	}

	rsp.Page = req.PerPage
	rsp.CurrentPage = req.Page
	rsp.Process = make([]*pb.TraceabilityProcessInfoResponseItem, 0)

	// 查询融合记录表
	sortList := []elastic.Sorter{
		elastic.NewFieldSort("created_at").Desc(),
	}
	mergeRecordQuery := elastic.NewBoolQuery()
	autoMergeRecordQuery := elastic.NewBoolQuery().Must(elastic.NewTermQuery("device_id", deviceData.Id))
	// for _, s := range deviceData.MergeKey {
	// 	switch s {
	// 	case "sn":
	// 		autoMergeRecordQuery.Must(elastic.NewTermsQueryFromStrings("sn", deviceData.Sn...))
	// 	case "mac":
	// 		autoMergeRecordQuery.Must(elastic.NewTermsQueryFromStrings("mac", deviceData.Mac...))
	// 	case "hostname":
	// 		autoMergeRecordQuery.Must(elastic.NewTermsQueryFromStrings("hostname", deviceData.HostName...))
	// 	}
	// }
	manualMergeRecordQuery := elastic.NewBoolQuery().Must(elastic.NewTermsQueryFromStrings("merge_mode", models.MergeMode_Manual, models.MergeMode_Calibration), elastic.NewRangeQuery("created_at").Gt(deviceData.CreatedAt))
	calibrationMergeRecordQuery := elastic.NewBoolQuery().Must(elastic.NewTermQuery("merge_mode", models.MergeMode_Calibration), elastic.NewMatchQuery("device_ids", req.Id))
	mergeRecordQuery.Should(autoMergeRecordQuery, manualMergeRecordQuery, calibrationMergeRecordQuery).MinimumNumberShouldMatch(1)

	wg := &sync.WaitGroup{}
	wg.Add(2)
	go func() {
		defer wg.Done()
		// 查询记录总数
		total, err := es.GetCount(esmodel_device.NewMergeRecordsModel().IndexName(), mergeRecordQuery)
		if err != nil {
			logs.Warnf("get merge record count error: %v", err)
		}
		rsp.Total = total
	}()

	go func() {
		defer wg.Done()
		// 查询当前页的数据，mergeRecordCount 无法作为总数，超过 10000 的数据量，该数据可能不准确
		_, mergeRecordData, err := es.List[esmodel_device.MergeRecords](int(req.Page), int(req.PerPage), mergeRecordQuery, sortList, "id", "source_ids", "key", "created_at", "updated_at", "merge_mode", "batch_no", "tigger_source_id")
		if err != nil {
			logs.Warnf("get merge record error: %v", err)
		} else {
			for _, mrd := range mergeRecordData {
				item := &pb.TraceabilityProcessInfoResponseItem{
					Id:   mrd.Id,
					Time: mrd.CreatedAt.String(),
				}
				if mrd.MergeMode == models.MergeMode_Auto {
					item.Type = TRACEABILITY_PROCESS_DETAIL_TYPE_AUTO_MERGE
					item.Name = "设备自动提取"
					item.TiggerSourceId = mrd.TiggerSourceId
					item.TiggerSource = getSourceFromSourceListById(mrd.TiggerSourceId, sources)
					item.SourceId = mrd.SourceIds
					item.Source = func() []*pb.Source {
						s := make([]*pb.Source, 0)
						for _, sourceId := range mrd.SourceIds {
							if sourceId == 0 {
								continue
							}
							s = append(s, getSourceFromSourceListById(sourceId, sources))
						}
						return s
					}()
				} else if mrd.MergeMode == models.MergeMode_Manual {
					item.Type = TRACEABILITY_PROCESS_DETAIL_TYPE_MANUAL_MERGE
					item.Name = "设备手动提取"
					item.TiggerSourceId = mrd.TiggerSourceId
					item.TiggerSource = getSourceFromSourceListById(mrd.TiggerSourceId, sources)
					item.Source = []*pb.Source{}
					item.SourceId = []uint64{}
				} else if mrd.MergeMode == models.MergeMode_Calibration {
					item.Type = TRACEABILITY_PROCESS_DETAIL_TYPE_MANUAL_CALIBRATION
					item.Name = "人工校准"
					item.TiggerSourceId = mrd.TiggerSourceId
					item.TiggerSource = getSourceFromSourceListById(mrd.TiggerSourceId, sources)
					item.Source = []*pb.Source{}
					item.SourceId = []uint64{}
				}
				rsp.Process = append(rsp.Process, item)
			}
		}

		// 对 rsp.Process 进行排序
		sortProcess(rsp.Process, "desc")
	}()

	wg.Wait()
	return nil
}

// DeviceTraceabilityDetailInfo 获取设备融合详情信息
func (m *Merge) DeviceTraceabilityDetailInfo(ctx context.Context, req *pb.TraceabilityDetailInfoRequest, rsp *pb.DeviceTraceabilityDetailInfoResponse) error {
	logs := logs.GetLogger("service")
	deviceData, err := es.GetById[esmodel_device.Device](req.Id)
	if err != nil {
		return err
	}
	// 查询所有数据源, 用于给出所有数据源的名称和图标
	allSources, err := getAllSources()
	if err != nil {
		logs.Warnf("get all sources error: %v", err)
	}
	if len(allSources) < 1 {
		logs.Warn("no source found")
	}
	// 查询所有区域
	allAreaList, _, err := network_areas.AllNetworkAreas()
	if err != nil {
		logs.Warnf("get network area list error: %v", err)
	}

	rsp.Items = make([]*pb.DeviceTraceabilityDetailInfoResponseItem, 0)
	for _, process := range req.ProcessInfo {
		if process.Id == "" {
			continue
		}
		processItem := &pb.DeviceTraceabilityDetailInfoResponseItem{
			OriginalData:          []*pb.TraceabilityDetailInfoResponseOriginalDataItem{},
			Strategies:            []*pb.TraceabilityDetailInfoResponseStrategyItem{},
			MergedData:            &pb.Device{},
			AllSourceForMergeData: make(map[string]*pb.Source),
			AllSourceForStrategy:  make(map[string]*pb.Source),
			Type:                  process.Type,
			Id:                    process.Id,
		}

		// 查询融合记录
		processData, err := es.GetByIdSafe[esmodel_device.MergeRecords](process.Id)
		if err != nil {
			logs.Warnf("get merge record by id(%s) error: %v", process.Id, err)
		}

		wg := &sync.WaitGroup{}
		wg.Add(1)

		// 策略、源信息数据处理
		go func() {
			defer wg.Done()

			// 按照 sourceId-nodeId 作为 key 存储所有源数据，方便前端展示
			for _, sourceId := range processData.SourceIds {
				for _, nodeId := range processData.NodeIds {
					k := fmt.Sprintf("%d-%d", sourceId, nodeId)
					processItem.AllSourceForMergeData[k] = getSourceFromSourceListById(sourceId, allSources)
				}
			}

			// 按照 sourceId 作为 key 存储所有源数据，方便前端展示
			for _, s := range allSources {
				processItem.AllSourceForStrategy[fmt.Sprintf("%d", s.Id)] = &pb.Source{
					Id:   s.Id,
					Name: s.Name,
					Icon: s.Icon,
				}
			}

			// 融合策略
			if len(processData.Strategies) > 0 {
				for _, strategy := range processData.Strategies {
					item := &pb.TraceabilityDetailInfoResponseStrategyItem{
						BusinessType: strategy.BusinessType,
						FieldName:    strategy.FieldName,
						DisplayName:  strategy.DisplayName,
						SourcePriority: func() map[string]int32 {
							m := make(map[string]int32)
							for k, v := range strategy.SourcePriority {
								m[k] = int32(v)
							}
							return m
						}(),
						UntrustedSource: strategy.UntrustedSource,
						Version:         strategy.Version,
					}
					processItem.Strategies = append(processItem.Strategies, item)
				}
			}
		}()

		if process.Type == TRACEABILITY_PROCESS_DETAIL_TYPE_AUTO_MERGE {
			wg.Add(2)

			// 融合结果数据处理
			go func() {
				defer wg.Done()
				// 融合结果
				deviceRecord, err := es.GetById[esmodel_device.DeviceRecord](processData.DeviceRecordId)
				if err != nil {
					logs.Warnf("get device record by id(%s) error: %v", processData.DeviceRecordId, err)
				}
				processItem.MergedData = convertToPbDevice(deviceRecord.Device, allSources, allAreaList)
			}()

			// 原始数据处理
			go func() {
				defer wg.Done()
				// 根据融合记录中的资产任务ID查询资产任务数据
				query := elastic.NewTermsQueryFromStrings("_id", processData.TaskDataIds...)
				searchResult, err := es.GetEsClient().Search().Index("*_task_assets").Query(query).Do(context.Background())
				if err != nil {
					logs.Warnf("get task assets by id(%s) error: %v", processData.TaskDataIds, err)
				}
				// 资产任务数据来自不同索引，数据结构不同，直接返回原始数据
				if searchResult != nil && searchResult.Hits != nil && searchResult.Hits.TotalHits.Value > 0 {
					for _, hit := range searchResult.Hits.Hits {
						sourceId, listFields, _ := getSourceIdByIndexName(hit.Index)
						item := &pb.TraceabilityDetailInfoResponseOriginalDataItem{
							SourceId: hit.Id,
							Source:   getSourceFromSourceListById(sourceId, allSources),
							Data:     string(hit.Source),
							ListFields: func() []*pb.MapStringString {
								arr := make([]*pb.MapStringString, 0)
								for _, field := range listFields {
									item := &pb.MapStringString{
										Fields: field,
									}
									arr = append(arr, item)
								}
								return arr
							}(),
						}
						processItem.OriginalData = append(processItem.OriginalData, item)
					}
				}
			}()
		} else if process.Type == TRACEABILITY_PROCESS_DETAIL_TYPE_MANUAL_MERGE || process.Type == TRACEABILITY_PROCESS_DETAIL_TYPE_MANUAL_CALIBRATION {
			wg.Add(1)

			// 手动融合数据处理
			go func() {
				defer wg.Done()
				// 手动融合结果
				query := elastic.NewBoolQuery()
				query.Must(elastic.NewTermQuery("backup_mode", "diff"))
				query.Must(elastic.NewTermQuery("asset_id", deviceData.Id))
				query.Must(elastic.NewTermQuery("batch_no", processData.BatchNo))
				assetRecord, err := es.First[esmodel_device.DeviceRecord](query, nil)
				if err != nil {
					logs.Warnf("get device record by id(%s) error: %v", processData.DeviceRecordId, err)
				}
				if assetRecord != nil && assetRecord.Device != nil {
					processItem.MergedData = convertToPbDevice(assetRecord.Device, allSources, allAreaList)
				}
				for _, v := range processData.Strategies {
					fieldKey := utils.CamelToSnake(v.FieldName)
					if fieldKey == "tag" {
						fieldKey = "tags"
					}
					processItem.ListFields = append(processItem.ListFields, &pb.MapStringString{
						Fields: map[string]string{
							"name": v.DisplayName,
							"key":  fieldKey,
						},
					})
				}

			}()
		}

		wg.Wait()
		rsp.Items = append(rsp.Items, processItem)
	}
	return nil
}
