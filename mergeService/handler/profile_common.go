package handler

import (
	"context"
	"encoding/json"
	"sort"
	"strconv"
	"strings"
	"time"

	"fobrain/initialize/es"
	"fobrain/initialize/redis"
	pb "fobrain/mergeService/proto"
	"fobrain/mergeService/utils"
	"fobrain/models/elastic/assets"
	esmodel_asset "fobrain/models/elastic/assets"
	esmodel_device "fobrain/models/elastic/device"
	"fobrain/models/elastic/source/bk_cmdb"
	"fobrain/models/elastic/source/bk_cmdb_business"
	"fobrain/models/elastic/source/bk_cmdb_custom_module_property"
	"fobrain/models/elastic/source/bk_cmdb_domain"
	"fobrain/models/elastic/source/bk_cmdb_f5"
	"fobrain/models/elastic/source/changting_waf"
	"fobrain/models/elastic/source/d01"
	"fobrain/models/elastic/source/file_import"
	"fobrain/models/elastic/source/foeye"
	"fobrain/models/elastic/source/foradar"
	"fobrain/models/elastic/source/huawei_hk_cloud"
	"fobrain/models/elastic/source/mach_lake"
	"fobrain/models/elastic/source/qizhi_uaudithost"
	"fobrain/models/elastic/source/qt_cloud"
	"fobrain/models/elastic/source/weibu"
	xray "fobrain/models/elastic/source/x-ray"
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/network_areas"
	pkgutils "fobrain/pkg/utils"
	pgidservice "fobrain/services/people_pgid"

	"github.com/olivere/elastic/v7"
)

// getAllSources 获取所有数据源列表，如果缓存中没有数据，则从数据库中获取
func getAllSources() ([]*data_source.Source, error) {
	key := "data:source:all"
	var sources []*data_source.Source
	var err error
	redisResult, _ := redis.GetRedisClient().Get(context.Background(), key).Result()
	if redisResult != "" {
		json.Unmarshal([]byte(redisResult), &sources)
	}
	if len(sources) < 1 {
		sources, _, err = data_source.NewSourceModel().Items(1, 500)
		if err == nil && len(sources) > 0 {
			jsonBytes, _ := json.Marshal(sources)
			redis.GetRedisClient().Set(context.Background(), key, jsonBytes, 30*time.Minute)
		}
	}

	return sources, err
}

// getSourceIdByIndexName 获取数据源信息
// @return sourceId, listFields, listDetailFields
func getSourceIdByIndexName(indexName string) (uint64, []map[string]string, []map[string]string) {
	// 1	FOEYE·网络资产测绘及风险分析系统	foeye
	// 2	FORadar·互联网资产攻击面管理平台	foradar_saas
	// 3	万相·主机自适应安全平台	qty
	// 4	钉钉	dingtalk
	// 5	蓝鲸CMDB	bk_cmdb
	// 6	文件导入	file_import
	// 7	人工添加	handwork
	// 8	微步TDP	TDP
	// 9	LongiWAF	LongiWAF,长亭waf
	// 10	D01·网络风险资产监测分析系统	d01
	// 11	齐治堡垒机	qizhi_uaudithost
	// 12	阿里云-云盾	aliyun_cloud
	// 13	MachLake	MachLake
	// 14	X-RAY·长亭洞鉴安全评估系统	x_ray
	// 15	CMDB_VM
	// 16	CMDB_ECS
	// 17	CMDB_DOMAIN
	// 18	CMDB_BUSINESS
	if indexName == "" || !strings.HasSuffix(indexName, "_task_assets") {
		return 0, nil, nil
	}
	switch indexName {
	case "foeye_task_assets":
		listFields := foeye.NewFoeyeTaskAssetsModel().GetFoeyeAssetListFields()
		listDetailFields := foeye.NewFoeyeTaskAssetsModel().GetFoeyeAssetListDetailFields()
		return 1, listFields, listDetailFields
	case "foeyev2_task_assets":
		listFields := foeye.NewFoeyeTaskAssetsModel().GetFoeyeAssetListFieldsV2()
		listDetailFields := foeye.NewFoeyeTaskAssetsModel().GetFoeyeAssetListDetailFields()
		return 1, listFields, listDetailFields
	case "foradar_task_assets":
		listFields := foradar.NewForadarTaskAssetsModel().GetAssetListFields()
		listDetailFields := foradar.NewForadarTaskAssetsModel().GetAssetListDetailFields()
		return 2, listFields, listDetailFields
	case "qt_cloud_task_assets":
		listFields := qt_cloud.NewQTCloudTaskAssetsModel().GetQTCloudAssetListFields()
		listDetailFields := qt_cloud.NewQTCloudTaskAssetsModel().GetQTCloudAssetListDetailFields()
		return 3, listFields, listDetailFields
	case "bk_cmdb_task_assets":
		listFields := bk_cmdb.NewBKCmdbTaskAssetsModel().GetAssetListFields()
		listDetailFields := bk_cmdb.NewBKCmdbTaskAssetsModel().GetAssetListDetailFields()
		return 5, listFields, listDetailFields
	case "file_import_task_assets":
		listFields := file_import.NewFileImportTaskAssetsModel().GetAssetListFields()
		listDetailFields := file_import.NewFileImportTaskAssetsModel().GetAssetListDetailFields()
		return 6, listFields, listDetailFields
	case "weibu_task_assets":
		listFields := weibu.NewWeibuTaskAssetsModel().GetWeibuAssetListFields()
		listDetailFields := weibu.NewWeibuTaskAssetsModel().GetWeibuAssetListDetailFields()
		return 8, listFields, listDetailFields
	case "changting_waf_task_assets":
		listFields := changting_waf.NewChangtingWafTaskAssetsModel().GetCTWafAssetListFields()
		listDetailFields := changting_waf.NewChangtingWafTaskAssetsModel().GetCTWafAssetListDetailFields()
		return 9, listFields, listDetailFields
	case "d01_task_assets":
		listFields := d01.NewTaskAssetsModel().GetAssetListFields()
		listDetailFields := d01.NewTaskAssetsModel().GetAssetListDetailFields()
		return 10, listFields, listDetailFields
	case "d01v2_task_assets":
		listFields := foeye.NewFoeyeTaskAssetsModel().GetFoeyeAssetListFieldsV2()
		listDetailFields := d01.NewTaskAssetsModel().GetAssetListDetailFields()
		return 10, listFields, listDetailFields
	case "qizhi_uaudithost_task_assets":
		listFields := qizhi_uaudithost.NewQiZhiUAuditHostAssetsModel().GetAssetListFields()
		listDetailFields := qizhi_uaudithost.NewQiZhiUAuditHostAssetsModel().GetAssetListDetailFields()
		return 11, listFields, listDetailFields
	case "mach_lake_task_assets":
		listFields := mach_lake.NewMachLakeTaskAssetsModel().GetMachLakeAssetListFields()
		listDetailFields := mach_lake.NewMachLakeTaskAssetsModel().GetMachLakeAssetListDetailFields()
		return 13, listFields, listDetailFields
	case "xray_task_assets":
		listFields := xray.NewTaskAssetsModel().GetAssetListFields()
		listDetailFields := xray.NewTaskAssetsModel().GetAssetListDetailFields()
		return 14, listFields, listDetailFields
	case "bk_cmdb_vm_machine_task_assets":
		listFields := bk_cmdb_custom_module_property.NewBkCmdbCustomTaskVmMachineAssetsModel().GetAssetListFields()
		listDetailFields := bk_cmdb_custom_module_property.NewBkCmdbCustomTaskVmMachineAssetsModel().GetAssetListDetailFields()
		return 15, listFields, listDetailFields
	case "bk_cmdb_cloud_ecs_task_assets":
		listFields := bk_cmdb_custom_module_property.NewBkCmdbCustomTaskEcsAssetsModel().GetAssetListFields()
		listDetailFields := bk_cmdb_custom_module_property.NewBkCmdbCustomTaskEcsAssetsModel().GetAssetListDetailFields()
		return 16, listFields, listDetailFields
	case "bk_cmdb_domain_task_assets":
		listFields := bk_cmdb_domain.NewBKCmdbDomainTaskAssetsModel().GetAssetListFields()
		listDetailFields := bk_cmdb_domain.NewBKCmdbDomainTaskAssetsModel().GetAssetListDetailFields()
		return 18, listFields, listDetailFields
	case "bk_cmdb_business_task_assets":
		listFields := bk_cmdb_business.NewBKCmdbBusinessTaskAssetsModel().GetAssetListFields()
		listDetailFields := bk_cmdb_business.NewBKCmdbBusinessTaskAssetsModel().GetAssetListDetailFields()
		return 19, listFields, listDetailFields
	case "bk_cmdb_f5_vs_task_assets":
		listFields := bk_cmdb_f5.NewBkCmdbF5VsTaskAssetsModel().GetAssetListFields()
		listDetailFields := bk_cmdb_f5.NewBkCmdbF5VsTaskAssetsModel().GetAssetListDetailFields()
		return 20, listFields, listDetailFields
	case "bk_cmdb_f5_pool_task_assets":
		listFields := bk_cmdb_f5.NewBkCmdbF5PoolTaskAssetsModel().GetAssetListFields()
		listDetailFields := bk_cmdb_f5.NewBkCmdbF5PoolTaskAssetsModel().GetAssetListDetailFields()
		return 21, listFields, listDetailFields
	case "huawei_hk_cloud_task_assets":
		listFields := huawei_hk_cloud.NewHuaweiHkCloudAssetsModel().GetAssetListFields()
		listDetailFields := huawei_hk_cloud.NewHuaweiHkCloudAssetsModel().GetAssetListDetailFields()
		return 17, listFields, listDetailFields
	default:
		return uint64(8), nil, nil
	}
}

// getSourceFromSourceListById 获取数据源信息
func getSourceFromSourceListById(sourceId uint64, sourceList []*data_source.Source) *pb.Source {
	if sourceId == 0 {
		return nil
	}
	for _, s := range sourceList {
		if s.Id == sourceId {
			return &pb.Source{
				Id:   s.Id,
				Name: s.Name,
				Icon: s.Icon,
			}
		}
	}
	return nil
}

// getAreaFromAreaListById 获取区域信息
func getAreaFromAreaListById(areaId uint64, areaList []*network_areas.NetworkArea) *pb.Area {
	for _, a := range areaList {
		if a.Id == areaId {
			return &pb.Area{
				Id:   a.Id,
				Name: a.Name,
			}
		}
	}
	return nil
}

// getAllArea 获取所有区域列表，如果缓存中没有数据，则从数据库中获取
func getAllArea() ([]*network_areas.NetworkArea, error) {
	key := "data:area:all"
	var areas []*network_areas.NetworkArea
	var err error
	redisResult, _ := redis.GetRedisClient().Get(context.Background(), key).Result()
	if redisResult != "" {
		json.Unmarshal([]byte(redisResult), &areas)
	}
	if len(areas) < 1 {
		areas, _, err = network_areas.AllNetworkAreas()
		if err == nil && len(areas) > 0 {
			jsonBytes, _ := json.Marshal(areas)
			redis.GetRedisClient().Set(context.Background(), key, jsonBytes, 30*time.Minute)
		}
	}
	return areas, err
}

// getAssetList 获取资产列表
func getAssetList(page, perPage int, ipList []string, area []int) ([]*pb.Asset, int64, error) {
	result := make([]*pb.Asset, 0)

	// 获取所有数据源列表,用于转换结果中补充完整数据源信息
	allSourceList, err := getAllSources()
	if err != nil {
		return result, 0, err
	}
	// 获取所有区域列表,用于转换结果中补充完整区域信息
	areaList, err := getAllArea()
	if err != nil {
		return result, 0, err
	}

	query := elastic.NewBoolQuery()
	query.Must(elastic.NewTermsQueryFromStrings("ip", ipList...))
	if len(area) > 0 {
		areaStr := make([]string, 0)
		for _, a := range area {
			areaStr = append(areaStr, strconv.Itoa(a))
		}
		query.Must(elastic.NewTermsQueryFromStrings("area", areaStr...))
	}
	sortList := []elastic.Sorter{
		elastic.NewFieldSort("updated_at").Desc(),
		elastic.NewFieldSort("created_at").Desc(),
	}
	count, err := es.GetCount(esmodel_asset.NewAssets().IndexName(), query)
	if err != nil {
		return result, 0, err
	}

	if count > 0 {
		_, data, err := es.List[esmodel_asset.Assets](page, perPage, query, sortList)
		if err != nil {
			return result, count, err
		}
		result = utils.SlicesConvert(data, func(a *esmodel_asset.Assets) *pb.Asset {
			return convertToPbAsset(a, allSourceList, areaList)
		})
	}
	return result, count, nil
}

// sortProcess 排序进程信息
// @param process 进程信息
// @param sortType 排序类型, desc 降序，asc 升序
func sortProcess(process []*pb.TraceabilityProcessInfoResponseItem, sortType string) {
	if sortType == "desc" {
		sort.Slice(process, func(i, j int) bool {
			// <从小到大，>从大到小
			return process[i].Time > process[j].Time
		})
	} else {
		sort.Slice(process, func(i, j int) bool {
			// <从小到大，>从大到小
			return process[i].Time < process[j].Time
		})
	}
}

// convertToPbAsset 转换为pb.Asset
func convertToPbAsset(asset *esmodel_asset.Assets, allSourceList []*data_source.Source, areaList []*network_areas.NetworkArea) *pb.Asset {
	result := &pb.Asset{}
	result.Id = asset.Id
	result.CreatedAt = asset.CreatedAt.String()
	result.UpdatedAt = asset.UpdatedAt.String()
	result.DeletedAt = asset.DeletedAt.String()
	result.PurgedAt = asset.PurgedAt.String()
	result.MergeCount = int32(asset.MergeCount)
	result.AreaId = int32(asset.Area)
	result.Area = &pb.Area{
		Id: uint64(asset.Area),
		Name: func() string {
			for _, a := range areaList {
				if a.Id == uint64(asset.Area) {
					return a.Name
				}
			}
			return ""
		}(),
	}
	result.ProcessIds = asset.ProcessIds
	result.SourceIds = asset.SourceIds
	result.Source = func() []*pb.Source {
		if len(allSourceList) > 0 {
			sourceList := make([]*pb.Source, 0)
			for _, s := range asset.AllSourceIds {
				source := getSourceFromSourceListById(s, allSourceList)
				if source != nil {
					sourceList = append(sourceList, source)
				}
			}
			return sourceList
		}
		return make([]*pb.Source, 0)
	}()
	result.NodeIds = asset.NodeIds
	result.TaskDataIds = asset.TaskDataIds
	result.AllSourceIds = asset.AllSourceIds
	result.AllSource = func() []*pb.Source {
		if len(allSourceList) > 0 {
			sourceList := make([]*pb.Source, 0)
			for _, s := range asset.AllSourceIds {
				source := getSourceFromSourceListById(s, allSourceList)
				if source != nil {
					sourceList = append(sourceList, source)
				}
			}
			return sourceList
		}
		return make([]*pb.Source, 0)
	}()
	result.AllNodeIds = asset.AllNodeIds
	result.AllTaskDataIds = asset.AllTaskDataIds
	result.AllProcessIds = asset.AllProcessIds
	result.Ip = asset.Ip
	result.IpType = int32(asset.IpType)
	result.IpTypeText = func() string {
		return pkgutils.GetIpTypeText(asset.Ip)
	}()
	result.IpSegment = asset.IpSegment
	result.IpSegmentSource = asset.IpSegmentSource
	result.HostName = asset.HostName
	result.HostNameSource = asset.HostNameSource
	result.EthName = asset.EthName
	result.EthNameSource = asset.EthNameSource
	result.Os = asset.Os
	result.OsSource = asset.OsSource
	result.Kernel = asset.Kernel
	result.KernelSource = asset.KernelSource
	result.Model = asset.Model
	result.ModelSource = asset.ModelSource
	result.Maker = asset.Maker
	result.MakerSource = asset.MakerSource
	result.Sn = asset.Sn
	result.SnSource = asset.SnSource
	result.Mac = asset.Mac
	result.MacSource = asset.MacSource
	result.Product = asset.Product
	result.RuleInfos = func() []*pb.RuleInfo {
		var arr []*pb.RuleInfo
		for _, b := range asset.RuleInfos {
			item := &pb.RuleInfo{
				Product: b.Product,
			}
			arr = append(arr, item)
		}
		return arr
	}()
	result.ProductSource = asset.ProductSource
	result.Business = func() []*pb.IpAdminInfoResponseItem {
		arr := make([]*pb.IpAdminInfoResponseItem, 0)
		for _, b := range asset.Business {
			if b == nil {
				continue
			}
			arr = append(arr, convertToPbBusinessSystem(b))
		}
		return arr
	}()
	result.BusinessSource = func() map[string]*pb.IpAdminInfoResponseList {
		m := make(map[string]*pb.IpAdminInfoResponseList)
		for k, v := range asset.BusinessSource {
			arr := make([]*pb.IpAdminInfoResponseItem, 0)
			for _, b := range v {
				arr = append(arr, convertToPbBusinessSystem(b))
			}
			m[k] = &pb.IpAdminInfoResponseList{Admin: arr}
		}
		return m
	}()
	result.Oper = func() []*pb.PersonBase {
		arr := make([]*pb.PersonBase, 0)
		for _, p := range asset.OperInfo {
			if p == nil {
				continue
			}
			arr = append(arr, convertToPbPersonBase(p))
		}
		return arr
	}()
	result.OperSource = asset.OperSource
	result.MachineRoom = asset.MachineRoom
	result.Tags = asset.Tags
	result.MachineRoomSource = asset.MachineRoomSource
	result.Status = int32(asset.Status)
	result.StatusSource = utils.SlicesConvertMapInt32(asset.StatusSource)
	result.Ports = func() []*pb.PortInfo {
		arr := make([]*pb.PortInfo, 0)
		for _, p := range asset.Ports {
			item := &pb.PortInfo{
				Port:     int32(p.Port),
				Protocol: p.Protocol,
				Url:      p.Url,
				Domain:   p.Domain,
				Status:   int32(p.Status),
				Title:    p.Title,
			}
			arr = append(arr, item)
		}
		return arr
	}()
	result.PortsSource = func() map[string]*pb.ListPortInfo {
		m := make(map[string]*pb.ListPortInfo)
		for k, v := range asset.PortsSource {
			arr := make([]*pb.PortInfo, 0)
			for _, p := range v {
				item := &pb.PortInfo{
					Port:     int32(p.Port),
					Protocol: p.Protocol,
					Url:      p.Url,
					Domain:   p.Domain,
					Status:   int32(p.Status),
					Title:    p.Title,
				}
				arr = append(arr, item)
			}
			m[k] = &pb.ListPortInfo{Ports: arr}
		}
		return m
	}()
	result.MemorySize = asset.MemorySize
	result.MemorySizeSource = asset.MemorySizeSource
	result.MemoryUsageRate = asset.MemoryUsageRate
	result.MemoryUsageRateSource = asset.MemoryUsageRateSource
	result.CpuMaker = asset.CpuMaker
	result.CpuMakerSource = asset.CpuMakerSource
	result.CpuBrand = asset.CpuBrand
	result.CpuBrandSource = asset.CpuBrandSource
	result.CpuCount = utils.SlicesConvertInt32(asset.CpuCount)
	result.CpuCountSource = utils.SlicesConvertMapInt32(asset.CpuCountSource)
	result.DiskCount = utils.SlicesConvertInt32(asset.DiskCount)
	result.DiskCountSource = utils.SlicesConvertMapInt32(asset.DiskCountSource)
	result.DiskSize = utils.SlicesConvertInt32(asset.DiskSize)
	result.DiskSizeSource = utils.SlicesConvertMapInt32(asset.DiskSizeSource)
	result.DiskUsageRate = asset.DiskUsageRate
	result.DiskUsageRateSource = asset.DiskUsageRateSource
	result.LoadAverage = asset.LoadAverage
	result.LoadAverageSource = asset.LoadAverageSource
	result.NetworkType = int32(asset.NetworkType)
	result.NetworkTypeText = pkgutils.GetNetworkTypeText(asset.Ip)
	return result
}

// convertToPbDevice 转换为pb.Device
// @param v esmodel_device.Device 数据源
// @param allSourceList 所有数据源列表
// @param allAreaList 所有区域列表
func convertToPbDevice(v *esmodel_device.Device, allSourceList []*data_source.Source, allAreaList []*network_areas.NetworkArea) *pb.Device {
	result := &pb.Device{
		Id: v.Id,
		Hostname: func() []string {
			if len(v.HostName) == 0 {
				return []string{}
			}
			return v.HostName
		}(),
		Sn: func() []string {
			if len(v.Sn) == 0 {
				return make([]string, 0)
			}
			return v.Sn
		}(),
		Mac:         v.Mac,
		Ip:          v.Ip,
		PrivateIp:   v.PrivateIp,
		PublicIp:    v.PublicIp,
		MachineRoom: v.MachineRoom,
		Tags:        v.Tags,
		Os:          v.Os,
		CreatedAt: func() string {
			if v.CreatedAt != nil {
				return v.CreatedAt.String()
			}
			return ""
		}(),
		UpdatedAt: func() string {
			if v.UpdatedAt != nil {
				return v.UpdatedAt.String()
			}
			return ""
		}(),
		// 设备没有运维负责人字段，留空
		Oper:     []string{},
		SourceId: v.SourceIds,
		Source: func() []*pb.Source {
			if len(allSourceList) > 0 {
				sourceList := make([]*pb.Source, 0)
				for _, s := range v.SourceIds {
					source := getSourceFromSourceListById(s, allSourceList)
					if source != nil {
						sourceList = append(sourceList, source)
					}
				}
				return sourceList
			}
			return make([]*pb.Source, 0)
		}(),
		AreaId: func() []uint64 {
			ids := make([]uint64, 0)
			for _, a := range v.Area {
				ids = append(ids, uint64(a))
			}
			return ids
		}(),
		Area: func() []*pb.Area {
			if len(allAreaList) > 0 {
				areaList := make([]*pb.Area, 0)
				for _, a := range v.Area {
					area := getAreaFromAreaListById(uint64(a), allAreaList)
					if area != nil {
						areaList = append(areaList, area)
					}
				}
				return areaList
			}
			return make([]*pb.Area, 0)
		}(),
	}
	return result
}

func convertToPbBusinessSystem(info *assets.Business) *pb.IpAdminInfoResponseItem {
	if info == nil {
		return nil
	}
	return &pb.IpAdminInfoResponseItem{
		System:   info.System,
		SystemId: info.SystemId,
		Owner:    info.Owner,
		OwnerId:  info.OwnerId,
		PersonBase: func() []*pb.PersonBase {
			arr := make([]*pb.PersonBase, 0)
			for _, p := range info.PersonBase {
				arr = append(arr, convertToPbPersonBase(p))
			}
			return arr
		}(),
		Reliability: int64(info.BusinessTrustedState),
	}
}

func convertToPbPersonBase(info *assets.PersonBase) *pb.PersonBase {
	if info == nil {
		return nil
	}
	return &pb.PersonBase{
		Id:   info.Id,
		Fid:  info.Fid,
		Name: info.Name,
		Pgid: func() string {
			if info.Id != "" {
				pgid, err := pgidservice.GetPgidById(info.Id, "%s-%s")
				if err != nil {
					return ""
				}
				return pgid
			}
			return ""
		}(),
		FindInfo: func() []*pb.PersonFindInfo {
			rsp := make([]*pb.PersonFindInfo, 0)
			for _, v := range info.FindInfo {
				rsp = append(rsp, convertToPbPersonFindInfo(v))
			}
			return rsp
		}(),
		Department: func() []*pb.DepartmentBase {
			rsp := make([]*pb.DepartmentBase, 0)
			for _, v := range info.Department {
				rsp = append(rsp, convertToPbDepartmentBase(v))
			}
			return rsp
		}(),
	}
}

func convertToPbDepartmentBase(info *assets.DepartmentBase) *pb.DepartmentBase {
	if info == nil {
		return nil
	}
	return &pb.DepartmentBase{
		Id:                 info.Id,
		Name:               info.Name,
		BusinessSystemId:   info.BusinessSystemId,
		BusinessSystemName: info.BusinessSystemName,
		UserId:             info.UserId,
		UserName:           info.UserName,
		Parents: func() []*pb.DepartmentBase {
			if len(info.Parents) > 0 {
				arr := make([]*pb.DepartmentBase, 0)
				for _, v := range info.Parents {
					arr = append(arr, convertToPbDepartmentBase(v))
				}
				return arr
			}
			return make([]*pb.DepartmentBase, 0)
		}(),
	}
}

func convertToPbPersonFindInfo(info *assets.PersonFindInfo) *pb.PersonFindInfo {
	if info == nil {
		return nil
	}
	return &pb.PersonFindInfo{
		SourceId:     info.SourceId,
		NodeId:       info.NodeId,
		SourceValue:  info.SourceValue,
		MappingField: info.MappingField,
		FindCount:    int32(info.FindCount),
	}
}
