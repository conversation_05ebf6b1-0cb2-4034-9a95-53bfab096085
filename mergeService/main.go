package main

import (
	"context"
	"fmt"
	"fobrain/mergeService/model/async_stat"
	"fobrain/services/strategy_business"
	"runtime"
	"time"

	"fobrain/initialize/redis"
	_ "fobrain/mergeService/event/event_handler"
	"fobrain/mergeService/handler"
	"fobrain/mergeService/model"
	"fobrain/mergeService/model/trigger_merge"
	pb "fobrain/mergeService/proto"
	redis_helper "fobrain/models/redis"
	"fobrain/pkg/cfg"
	"fobrain/pkg/scheduler"
	"fobrain/webhook/dao"

	"go-micro.dev/v4"
	"go-micro.dev/v4/logger"
)

const (
	// ServiceName 服务名称
	ServiceName = "fobrain.api.merge"
	// ServiceVersion 服务版本
	ServiceVersion = "latest"
)

func main() {
	// 初始化配置
	cfg.InitLoadCfg()
	// 启动API服务
	srv := micro.NewService(
		micro.Name(ServiceName),
		micro.Version(ServiceVersion),
		micro.RegisterTTL(5*time.Second),      // TTL指定从上一次心跳间隔起，超过这个时间服务会被服务发现移除
		micro.RegisterInterval(4*time.Second), // 让服务在指定时间内重新注册，保持TTL获取的注册时间有效
		micro.Logger(logger.DefaultLogger),
		micro.Address(fmt.Sprintf("127.0.0.1:%s", cfg.LoadCommon().MergeListen)),
	)
	srv.Init(func(o *micro.Options) {
		fmt.Println("CPU数量:", runtime.NumCPU())
		clearRedisLocks()
		// 清理未正常结束的任务
		// 增加该功能后，融合服务不再支持多实例启动
		err := trigger_merge.CleanUnNormalEndTask()
		if err != nil {
			fmt.Println("清理未正常结束的任务失败,err:", err)
		}
		fmt.Println("清理未正常结束的任务成功")

		// 启动资产融合流
		model.NewAssetMergeFlow().Merge()
		// 启动漏洞融合流
		model.NewVulnMergeFlow().Merge()
		// 启动漏洞更新流
		model.NewVulnUpdateFlow().Update()
		// 启动人员融合流
		model.NewPersonMergeFlow().Merge()
		// 启动设备融合流
		model.NewDeviceMergeFlow().Merge()
		// 启动定时监听poc变化更新device和asset的poc_num
		async_stat.StartMonitorVul()
		// 业务系统批量写入
		strategy_business.NewStrategy().WriteResult()
		// 初始化许可证限制检查结果,默认限制
		model.LicenseLimitCheckResultAsset = true
		// 许可证限制检查
		licenseLimitCheck()
	})

	dao.InitMysql(dao.MysqlConfig{
		Address:  cfg.LoadMysql().Address,
		Port:     cfg.LoadMysql().Port,
		UserName: cfg.LoadMysql().UserName,
		Password: cfg.LoadMysql().Password,
		Database: cfg.LoadMysql().Database,
		CharSet:  cfg.LoadMysql().CharSet,
		LogLevel: cfg.LoadMysql().LogLevel,
		SlowTime: cfg.LoadMysql().SlowTime,
	})

	// Register service handler
	// merge handler
	if err := pb.RegisterMergeHandler(srv.Server(), new(handler.Merge)); err != nil {
		panic(err)
	}
	if err := srv.Run(); err != nil {
		panic(err)
	}

}

// 许可证限制检查
func licenseLimitCheck() {
	scheduler.Start("licenseLimitCheck", 15*time.Second, true, func() {
		limit, err := redis.GetRedisClient().Get(context.Background(), redis_helper.GetIsLicenseAssetLimitReachedKey()).Result()
		if err != nil {
			fmt.Println("licenseLimitCheck", err)
			// 查询出错，默认限制
			model.LicenseLimitCheckResultAsset = true
		}
		if limit == "0" || limit == "false" {
			// fmt.Println("licenseLimitCheck", "license limit not reached")
			// 许可证限制检查结果为false，则不再限制
			model.LicenseLimitCheckResultAsset = false
		} else {
			fmt.Println("licenseLimitCheck", "license limit reached")
			// 许可证限制检查结果为true，则限制
			model.LicenseLimitCheckResultAsset = true
		}
	})
}

func clearRedisLocks() {
	redis_helper.DeleteKeysByPrefixWithScan(context.Background(), redis.GetRedisClient(), "lock:merge_flow_duration_print*")
	redis_helper.DeleteKeysByPrefixWithScan(context.Background(), redis.GetRedisClient(), "lock:redis-queue-cleaner*")
	// 清理业务系统提取的锁
	redis_helper.DeleteKeysByPrefixWithScan(context.Background(), redis.GetRedisClient(), "lock:business:merge*")
}
