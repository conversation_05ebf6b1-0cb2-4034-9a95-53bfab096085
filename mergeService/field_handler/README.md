## 开发人员 - 注册字段处理器

### 背景
因为业务扩展需要对业务模型增加字段且字段信息来自数据源上报时，可以考虑使用字段处理器处理。使用这种方式可以在不影响融合流程和其他字段处理的情况下，快速添加字段处理逻辑。
如果增加的字段来自关联查询结果时，建议使用事件方式处理。

### 0. 准备工作
1. 在业务实体模型中增加字段
2. 如果该字段使用融合策略，需要在`cmd/databases/seeders/merge_strategy_seeder.go`中提前注册，可以参考`AppendStrategyWorkNumberForPerson()`方法

### 1. 字段处理器注册方法

```go
RegisterFieldHandlerForDevice(
    fieldName string,    // 字段名称
    getAllData bool,     // 是否获取所有数据源的值
    checkFunc func(dataSource *device.ProcessDevice) (any, bool),    // 字段值获取函数
    resultHandler func(device *device.Device, result []any) error,   // 结果处理函数
) error
```

### 2. 参数说明

* `fieldName`: 字段名称，不区分大小写
* `getAllData`:

  * true: 获取所有数据源的值
  * false: 按照融合策略获取数据源的值
* `checkFunc`: 字段值获取函数

  * 输入: 数据源 `*device.ProcessDevice`
  * 返回: (值, 是否获取成功)
* `resultHandler`: 结果处理函数，可直接修改参数中的设备对象

  * 输入: 设备对象和所有获取到的值
  * 返回: 错误信息

### 3. 注册步骤

1. 在`mergeService/field_handler`目录下，找到对应业务的子目录，比如设备业务则进入`device_field_handler`目录
2. 创建对应字段的处理文件，文件名建议使用`{业务字段}_field_handler.go`，比如`ip_field_handler.go`
3. 保持包名为`device_field_handler`，不同业务参考对应目录下`base_field_handler.go`中的包名
4. 创建`init`函数，并在函数中调用注册方法实现注册

### 4.注册示例
```go
package device_field_handler  // 注意包名

import (
	frame "fobrain/mergeFrame"
	"fobrain/models/elastic/device"
)
 
// 在init函数中注册，确保注册函数被调用
func init() {
    // 注册私有IP字段处理器
    frame.RegisterFieldHandlerForDevice(
        "PrivateIp",    // 字段名
        true,           // 获取所有数据源的值
        // 字段值获取函数
        func(dataSource *device.ProcessDevice) (any, bool) {
            if len(dataSource.PrivateIp) > 0 {
                return dataSource.PrivateIp, true
            }
            return nil, false
        },
        // 结果处理函数
        func(device *device.Device, result []any) error {
            // []any转为[]string
            device.PrivateIp = result  // 直接改device对象
            return nil
        },
    )
}
```

## 二、系统开发人员 - 调用处理器(一步)

### 字段处理函数
``` go
// 设备融合字段处理
func DeviceFieldProcess(
    device *device.Device,                      // 设备对象
    strategies []*strategy.Strategy,            // 所有字段的融合策略
    latestData map[string]*device.ProcessDevice // 所有原始数据  
    )
```

### 调用示例
``` go
// 设备融合字段处理
frame.DeviceFieldProcess(device, e.Strategy, latestData)
```

## 三、系统开发人员 - 调用处理器(分步)

### 1. 获取字段处理器

```go
// 获取所有注册的字段处理器
fieldHandlerMap := frame.GetAllFieldHandlerForDevice()

// 获取指定字段的处理器
handler, exists := frame.GetFieldHandlerForDevice("fieldName")
```

### 2. 调用处理器

处理器提供以下方法：

```go
// 判断是否获取所有数据
GetAllData() bool

// 获取字段值
Getter(
    sourcePriority []uint64,                          // 优先级顺序，获取全部数据时可以为nil
    sourceList map[uint64][]string,                   // 源节点列表，获取全部数据时可以为nil
    allData map[string]*device.ProcessDevice,         // 所有数据源，key为源节点id,value为源节点数据
) ([]any, []uint64, []uint64, []string, []string, error)  // 返回值为字段值列表、源节点id列表、节点id列表、任务数据id列表、处理id列表、错误信息

// 处理结果
ResultHandler(device *device.Device, result []any) error
```

### 3. 调用示例

```go
// 获取所有字段及对应的处理器
fieldHandlerMap := frame.GetAllFieldHandlerForDevice()
// 遍历所有字段处理器
for fieldName, fieldHandler := range fieldHandlerMap {
    if fieldHandler.GetAllData() {
        // 获取所有数据源的值
        fieldVal, err := fieldHandler.Getter(nil, nil, latestData)
        if err != nil {
            // 错误处理
            continue
        }
        // 保存结果
        err = fieldHandler.ResultHandler(device, fieldVal.Values)
    } else {
        // 按照融合策略获取数据
        if strategy, ok := strategyMap[fieldName]; ok {
            // 获取字段策略
            sourcePriority, sourceList := frame.GetSourceByPriority(strategy)
            // 调用处理器，取值
            fieldVal, err := 
                fieldHandler.Getter(sourcePriority, sourceList, latestData)
            // 保存字段结果
            err = fieldHandler.ResultHandler(device, fieldVal.Values)
            if err != nil {
                logs.GetLogger("device").Errorf("保存字段结果失败. fieldName: %s, err: %v", fieldName, err)
                continue
            }
            // 保存采信信息
            device.SourceIds = append(device.SourceIds, fieldVal.SourceIds...)
            device.NodeIds = append(device.NodeIds, fieldVal.NodeIds...)
            device.TaskDataIds = append(device.TaskDataIds, fieldVal.TaskDataIds...)
            device.ProcessIds = append(device.ProcessIds, fieldVal.ProcessIds...)
        }
    }
}
```

## 四、注意事项

1. 字段名不区分大小写，注册时会自动转换为小写
2. 同一字段名只能注册一次，重复注册会返回错误
3. `getAllData` 为 `true` 时，Getter 方法的 `sourcePriority` 和 `sourceList` 参数可以为 nil
4. 处理器返回的错误需要妥善处理，避免影响其他字段的处理
5. 系统开发人员调用字段处理器选择一种方式即可，建议使用一步调用的简单方式