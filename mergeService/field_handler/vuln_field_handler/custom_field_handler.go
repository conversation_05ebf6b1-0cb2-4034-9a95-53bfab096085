package vuln_field_handler

import (
	frame "fobrain/mergeFrame"
	"fobrain/models/elastic/poc"
	"maps"
)

func init() {
	frame.RegisterFieldHandlerForVuln[map[string]string]("custom_fields", true, func(dataSource *poc.ProcessPoc) (map[string]string, bool) {
		if len(dataSource.CustomFields) > 0 {
			return dataSource.CustomFields, true
		}
		return nil, false
	}, func(poc *poc.Poc, result []map[string]string) error {
		poc.CustomFields = make(map[string]string)
		for _, v := range result {
			// 将any转换为map[string]string
			maps.Copy(poc.CustomFields, v)
		}
		return nil
	})
}
