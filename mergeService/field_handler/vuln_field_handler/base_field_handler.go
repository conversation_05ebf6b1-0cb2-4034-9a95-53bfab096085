package vuln_field_handler

import (
	"fobrain/fobrain/common/localtime"
	frame "fobrain/mergeFrame"
	merge_helper "fobrain/mergeService/model/helper"
	"fobrain/models/elastic/poc"
	"fobrain/pkg/utils"
)

func init() {
	_ = frame.RegisterFieldHandlerForVuln("ispoc", false, func(dataSource *poc.ProcessPoc) (int, bool) {
		return dataSource.IsPoc, true
	}, func(poc *poc.Poc, result []int) error {
		return frame.IntFieldResultHandler(&poc.IsPoc, result)
	})
	_ = frame.RegisterFieldHandlerForVuln[map[string]any]("ip", false, func(dataSource *poc.ProcessPoc) (map[string]any, bool) {
		if dataSource.Ip == "" {
			return nil, false
		}
		return map[string]any{
			"ip":   dataSource.Ip,
			"area": dataSource.Area,
		}, true
	}, func(poc *poc.Poc, result []map[string]any) error {
		if len(result) == 0 {
			return nil
		}
		data := result[0]
		poc.Ip, _ = data["ip"].(string)
		poc.IpType = utils.GetIpType(poc.Ip)
		poc.NetworkType = utils.GetNetworkType(poc.Ip)
		poc.Area = merge_helper.AnyToUint64(data["area"])
		return nil
	})
	_ = frame.RegisterFieldHandlerForVuln[int]("port", false, func(dataSource *poc.ProcessPoc) (int, bool) {
		if dataSource.Port == 0 {
			return 0, false
		}
		return dataSource.Port, true
	}, func(poc *poc.Poc, result []int) error {
		return frame.IntFieldResultHandler(&poc.Port, result)
	})
	_ = frame.RegisterFieldHandlerForVuln[int]("level", false, func(dataSource *poc.ProcessPoc) (int, bool) {
		return dataSource.Level, true
	}, func(poc *poc.Poc, result []int) error {
		return frame.IntFieldResultHandler(&poc.Level, result)
	})
	_ = frame.RegisterFieldHandlerForVuln[map[string]any]("name", false, func(dataSource *poc.ProcessPoc) (map[string]any, bool) {
		if dataSource.Name == "" {
			return nil, false
		}
		return map[string]any{
			"name":           dataSource.Name,
			"vultype":        dataSource.VulType,
			"describe":       dataSource.Describe,
			"details":        dataSource.Details,
			"hazard":         dataSource.Hazard,
			"suggestions":    dataSource.Suggestions,
			"lastresponseat": dataSource.LastResponseAt,
		}, true
	}, func(poc *poc.Poc, result []map[string]any) error {
		if len(result) == 0 {
			return nil
		}
		data := result[0]
		poc.Name, _ = data["name"].(string)
		poc.VulType, _ = data["vultype"].(string)
		poc.Describe, _ = data["describe"].(string)
		poc.Details, _ = data["details"].(string)
		poc.Hazard, _ = data["hazard"].(string)
		poc.Suggestions, _ = data["suggestions"].(string)
		poc.LastResponseAt, _ = data["lastresponseat"].(*localtime.Time)
		return nil
	})
	_ = frame.RegisterFieldHandlerForVuln[int]("hasexp", false, func(dataSource *poc.ProcessPoc) (int, bool) {
		return dataSource.HasExp, true
	}, func(poc *poc.Poc, result []int) error {
		return frame.IntFieldResultHandler(&poc.HasExp, result)
	})
	_ = frame.RegisterFieldHandlerForVuln[int]("haspoc", false, func(dataSource *poc.ProcessPoc) (int, bool) {
		return dataSource.HasPoc, true
	}, func(poc *poc.Poc, result []int) error {
		return frame.IntFieldResultHandler(&poc.HasPoc, result)
	})
}
