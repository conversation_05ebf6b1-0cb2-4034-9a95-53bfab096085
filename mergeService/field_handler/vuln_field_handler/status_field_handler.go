package vuln_field_handler

import (
	frame "fobrain/mergeFrame"
	merge_helper "fobrain/mergeService/model/helper"
	"fobrain/models/elastic/poc"
)

func init() {
	// 如果是已修复的漏洞自动标记为已修复，如果是其他的状态全部按照新增处理
	// 如果多个源上报，则以时间最新的为准
	_ = frame.RegisterFieldHandlerForVuln[map[string]any]("status", true, func(dataSource *poc.ProcessPoc) (map[string]any, bool) {

		// 数据源状态为3，表示已修复
		if dataSource.Status == 3 {
			// 返回漏洞已修复状态
			return map[string]any{
				"status":     poc.PocStatusOfRepaired,
				"updated_at": dataSource.UpdatedAt,
				"source":     dataSource.Source,
			}, true
		}
		// 数据源是其他状态，返回漏洞新增状态
		return map[string]any{
			"status":     poc.PocStatusOfNew,
			"updated_at": dataSource.UpdatedAt,
			"source":     dataSource.Source,
		}, true
	}, func(poc *poc.Poc, result []map[string]any) error {
		if len(result) == 0 {
			return nil
		}
		for i, val := range result {
			data := val
			poc.StatusSource[merge_helper.AnyToString(data["source"])], _ = data["status"].(int)
			if i == 0 {
				poc.Status, _ = data["status"].(int)
			}
		}
		return nil
	})
}
