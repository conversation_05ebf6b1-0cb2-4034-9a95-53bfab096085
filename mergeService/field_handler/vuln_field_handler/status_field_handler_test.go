package vuln_field_handler

import (
	"testing"
	"time"

	"fobrain/fobrain/common/localtime"
	frame "fobrain/mergeFrame"
	"fobrain/models/elastic/poc"

	"github.com/stretchr/testify/assert"
)

func TestStatusFieldHandler_Repaired(t *testing.T) {
	// 测试已修复状态的漏洞
	now := localtime.NewLocalTime(time.Now())
	dataSource := &poc.ProcessPoc{
		Status:    3, // 已修复
		UpdatedAt: now,
		Source:    uint64(123),
	}
	handler, ok := frame.GetFieldHandlerForVuln[map[string]any]("status")
	assert.True(t, ok)
	assert.NotNil(t, handler)

	sourcePriority := []uint64{1}
	sourceList := map[uint64][]string{
		1: {"test"},
	}
	value, err := handler.(*frame.GenericFieldHandlerForVuln[map[string]any]).Getter(sourcePriority, sourceList, map[string]*poc.ProcessPoc{"test": dataSource})
	assert.NoError(t, err)
	assert.NotEmpty(t, value.Values)
	assert.Equal(t, poc.PocStatusOfRepaired, value.Values[0]["status"])
	assert.Equal(t, now, value.Values[0]["updated_at"])
	assert.Equal(t, uint64(123), value.Values[0]["source"])
}

func TestStatusFieldHandler_New(t *testing.T) {
	// 测试新增状态的漏洞
	now := localtime.NewLocalTime(time.Now())
	dataSource := &poc.ProcessPoc{
		Status:    1, // 新增
		UpdatedAt: now,
		Source:    uint64(123),
	}
	handler, ok := frame.GetFieldHandlerForVuln[map[string]any]("status")
	assert.True(t, ok)
	assert.NotNil(t, handler)

	sourcePriority := []uint64{1}
	sourceList := map[uint64][]string{
		1: {"test"},
	}
	value, err := handler.(*frame.GenericFieldHandlerForVuln[map[string]any]).Getter(sourcePriority, sourceList, map[string]*poc.ProcessPoc{"test": dataSource})
	assert.NoError(t, err)
	assert.NotEmpty(t, value.Values)
	assert.Equal(t, poc.PocStatusOfNew, value.Values[0]["status"])
	assert.Equal(t, now, value.Values[0]["updated_at"])
	assert.Equal(t, uint64(123), value.Values[0]["source"])
}
