package vuln_field_handler

import (
	"testing"
	"time"

	"fobrain/fobrain/common/localtime"
	frame "fobrain/mergeFrame"
	"fobrain/models/elastic/poc"

	"github.com/stretchr/testify/assert"
)

func TestBaseFieldHandler_IsPoc(t *testing.T) {
	// 测试 IsPoc 字段处理器
	dataSource := &poc.ProcessPoc{
		IsPoc: 1,
	}
	handler, ok := frame.GetFieldHandlerForVuln[int]("ispoc")
	assert.True(t, ok)
	assert.NotNil(t, handler)

	// 添加必要的参数
	sourcePriority := []uint64{1}
	sourceList := map[uint64][]string{
		1: {"test"},
	}
	value, err := handler.(*frame.GenericFieldHandlerForVuln[int]).Getter(sourcePriority, sourceList, map[string]*poc.ProcessPoc{"test": dataSource})
	assert.NoError(t, err)
	assert.NotEmpty(t, value.Values)
	assert.Equal(t, 1, value.Values[0])

	// 测试结果处理器
	p := &poc.Poc{}
	err = handler.ResultHandler(p, value.Values)
	assert.NoError(t, err)
	assert.Equal(t, 1, p.IsPoc)
}

func TestBaseFieldHandler_Ip(t *testing.T) {
	// 测试 Ip 字段处理器
	dataSource := &poc.ProcessPoc{
		Ip:   "***********",
		Area: 1,
	}
	handler, ok := frame.GetFieldHandlerForVuln[map[string]any]("ip")
	assert.True(t, ok)
	assert.NotNil(t, handler)

	sourcePriority := []uint64{1}
	sourceList := map[uint64][]string{
		1: {"test"},
	}
	value, err := handler.(*frame.GenericFieldHandlerForVuln[map[string]any]).Getter(sourcePriority, sourceList, map[string]*poc.ProcessPoc{"test": dataSource})
	assert.NoError(t, err)
	assert.NotEmpty(t, value.Values)
	assert.Equal(t, "***********", value.Values[0]["ip"])
	assert.Equal(t, uint64(1), value.Values[0]["area"])

	// 测试结果处理器
	p := &poc.Poc{}
	err = handler.ResultHandler(p, value.Values)
	assert.NoError(t, err)
	assert.Equal(t, "***********", p.Ip)
	assert.Equal(t, uint64(1), p.Area)
}

func TestBaseFieldHandler_Port(t *testing.T) {
	// 测试 Port 字段处理器
	dataSource := &poc.ProcessPoc{
		Port: 8080,
	}
	handler, ok := frame.GetFieldHandlerForVuln[int]("port")
	assert.True(t, ok)
	assert.NotNil(t, handler)

	sourcePriority := []uint64{1}
	sourceList := map[uint64][]string{
		1: {"test"},
	}
	value, err := handler.(*frame.GenericFieldHandlerForVuln[int]).Getter(sourcePriority, sourceList, map[string]*poc.ProcessPoc{"test": dataSource})
	assert.NoError(t, err)
	assert.NotEmpty(t, value.Values)
	assert.Equal(t, 8080, value.Values[0])

	// 测试结果处理器
	p := &poc.Poc{}
	err = handler.ResultHandler(p, value.Values)
	assert.NoError(t, err)
	assert.Equal(t, 8080, p.Port)
}

func TestBaseFieldHandler_Level(t *testing.T) {
	// 测试 Level 字段处理器
	dataSource := &poc.ProcessPoc{
		Level: 2,
	}
	handler, ok := frame.GetFieldHandlerForVuln[int]("level")
	assert.True(t, ok)
	assert.NotNil(t, handler)

	sourcePriority := []uint64{1}
	sourceList := map[uint64][]string{
		1: {"test"},
	}
	value, err := handler.(*frame.GenericFieldHandlerForVuln[int]).Getter(sourcePriority, sourceList, map[string]*poc.ProcessPoc{"test": dataSource})
	assert.NoError(t, err)
	assert.NotEmpty(t, value.Values)
	assert.Equal(t, 2, value.Values[0])

	// 测试结果处理器
	p := &poc.Poc{}
	err = handler.ResultHandler(p, value.Values)
	assert.NoError(t, err)
	assert.Equal(t, 2, p.Level)
}

func TestBaseFieldHandler_Name(t *testing.T) {
	// 测试 Name 字段处理器
	now := localtime.NewLocalTime(time.Now())
	dataSource := &poc.ProcessPoc{
		Name:           "test_vuln",
		VulType:        "web",
		Describe:       "test description",
		Details:        "test details",
		Hazard:         "test hazard",
		Suggestions:    "test suggestions",
		LastResponseAt: now,
	}
	handler, ok := frame.GetFieldHandlerForVuln[map[string]any]("name")
	assert.True(t, ok)
	assert.NotNil(t, handler)

	sourcePriority := []uint64{1}
	sourceList := map[uint64][]string{
		1: {"test"},
	}
	value, err := handler.(*frame.GenericFieldHandlerForVuln[map[string]any]).Getter(sourcePriority, sourceList, map[string]*poc.ProcessPoc{"test": dataSource})
	assert.NoError(t, err)
	assert.NotEmpty(t, value.Values)
	assert.Equal(t, "test_vuln", value.Values[0]["name"])
	assert.Equal(t, "web", value.Values[0]["vultype"])
	assert.Equal(t, "test description", value.Values[0]["describe"])
	assert.Equal(t, "test details", value.Values[0]["details"])
	assert.Equal(t, "test hazard", value.Values[0]["hazard"])
	assert.Equal(t, "test suggestions", value.Values[0]["suggestions"])
	assert.Equal(t, now, value.Values[0]["lastresponseat"])

	// 测试结果处理器
	p := &poc.Poc{}
	err = handler.ResultHandler(p, value.Values)
	assert.NoError(t, err)
	assert.Equal(t, "test_vuln", p.Name)
	assert.Equal(t, "web", p.VulType)
	assert.Equal(t, "test description", p.Describe)
	assert.Equal(t, "test details", p.Details)
	assert.Equal(t, "test hazard", p.Hazard)
	assert.Equal(t, "test suggestions", p.Suggestions)
	assert.Equal(t, now, p.LastResponseAt)
}

func TestBaseFieldHandler_HasExp(t *testing.T) {
	// 测试 HasExp 字段处理器
	dataSource := &poc.ProcessPoc{
		HasExp: 1,
	}
	handler, ok := frame.GetFieldHandlerForVuln[int]("hasexp")
	assert.True(t, ok)
	assert.NotNil(t, handler)

	sourcePriority := []uint64{1}
	sourceList := map[uint64][]string{
		1: {"test"},
	}
	value, err := handler.(*frame.GenericFieldHandlerForVuln[int]).Getter(sourcePriority, sourceList, map[string]*poc.ProcessPoc{"test": dataSource})
	assert.NoError(t, err)
	assert.NotEmpty(t, value.Values)
	assert.Equal(t, 1, value.Values[0])

	// 测试结果处理器
	p := &poc.Poc{}
	err = handler.ResultHandler(p, value.Values)
	assert.NoError(t, err)
	assert.Equal(t, 1, p.HasExp)
}

func TestBaseFieldHandler_HasPoc(t *testing.T) {
	// 测试 HasPoc 字段处理器
	dataSource := &poc.ProcessPoc{
		HasPoc: 1,
	}
	handler, ok := frame.GetFieldHandlerForVuln[int]("haspoc")
	assert.True(t, ok)
	assert.NotNil(t, handler)

	sourcePriority := []uint64{1}
	sourceList := map[uint64][]string{
		1: {"test"},
	}
	value, err := handler.(*frame.GenericFieldHandlerForVuln[int]).Getter(sourcePriority, sourceList, map[string]*poc.ProcessPoc{"test": dataSource})
	assert.NoError(t, err)
	assert.NotEmpty(t, value.Values)
	assert.Equal(t, 1, value.Values[0])

	// 测试结果处理器
	p := &poc.Poc{}
	err = handler.ResultHandler(p, value.Values)
	assert.NoError(t, err)
	assert.Equal(t, 1, p.HasPoc)
}
