package vuln_field_handler

import (
	frame "fobrain/mergeFrame"
	"fobrain/models/elastic/poc"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestCustomFieldHandler(t *testing.T) {
	tests := []struct {
		name       string
		dataSource map[string]*poc.ProcessPoc
		wantValue  map[string]string
		wantBool   bool
	}{
		{
			name: "空数据",
			dataSource: map[string]*poc.ProcessPoc{
				"s1": &poc.ProcessPoc{},
			},
			wantValue: map[string]string{},
			wantBool:  false,
		},
		{
			name: "自定义字段",
			dataSource: map[string]*poc.ProcessPoc{
				"s1": &poc.ProcessPoc{
					CustomFields: map[string]string{
						"custom1": "值1",
						"custom2": "值2",
					},
				},
			},
			wantValue: map[string]string{
				"custom1": "值1",
				"custom2": "值2",
			},
			wantBool: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 获取注册的处理器
			fieldHandler, ok := frame.GetFieldHandlerForVuln[map[string]string]("custom_fields")
			assert.True(t, ok)
			assert.NotNil(t, fieldHandler)

			// 测试checkFunc
			gotValue, err := fieldHandler.Getter([]uint64{1}, map[uint64][]string{
				1: {"s1"},
			}, tt.dataSource)
			assert.NoError(t, err)

			// 测试resultHandlerFunc
			err = fieldHandler.ResultHandler(&poc.Poc{}, gotValue.Values)
			assert.NoError(t, err)
		})
	}
}
