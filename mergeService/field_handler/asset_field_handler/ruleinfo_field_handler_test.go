package asset_field_handler

import (
	"testing"

	frame "fobrain/mergeFrame"
	"fobrain/models/elastic/assets"

	"github.com/stretchr/testify/assert"
)

func TestRuleInfoSourceFieldHandler(t *testing.T) {
	tests := []struct {
		name     string
		input    *assets.ProcessAssets
		expected map[string]any
	}{
		{
			name: "空规则信息",
			input: &assets.ProcessAssets{
				Source:    1,
				Node:      1,
				RuleInfos: []*assets.RuleInfo{},
			},
			expected: map[string]any{
				"source":     "1-1",
				"rule_infos": []*assets.RuleInfo{},
			},
		},
		{
			name: "有规则信息",
			input: &assets.ProcessAssets{
				Source: 1,
				Node:   1,
				RuleInfos: []*assets.RuleInfo{
					{
						Product: "test_product",
					},
				},
			},
			expected: map[string]any{
				"source": "1-1",
				"rule_infos": []*assets.RuleInfo{
					{
						Product: "test_product",
					},
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			handler, ok := frame.GetFieldHandlerForAsset[map[string]interface{}]("rule_info_source")
			assert.True(t, ok)
			result, err := handler.Getter([]uint64{1}, map[uint64][]string{
				1: {"1"},
			}, map[string]*assets.ProcessAssets{
				"1": tt.input,
			})

			assert.NoError(t, err)
			actual := result.Values[0]

			assert.Equal(t, tt.expected["source"], actual["source"])
			edRuleInfos, _ := tt.expected["rule_infos"].([]*assets.RuleInfo)
			alRuleInfos, _ := actual["rule_infos"].([]*assets.RuleInfo)
			assert.Equal(t, len(edRuleInfos),
				len(alRuleInfos))

			if len(edRuleInfos) > 0 {
				expectedRules := edRuleInfos
				actualRules := alRuleInfos
				assert.Equal(t, expectedRules[0].Product, actualRules[0].Product)
			}
		})
	}
}

func TestProductFieldHandler(t *testing.T) {
	tests := []struct {
		name     string
		input    *assets.ProcessAssets
		expected []*assets.RuleInfo
	}{
		{
			name: "空规则信息",
			input: &assets.ProcessAssets{
				RuleInfos: []*assets.RuleInfo{},
			},
			expected: nil,
		},
		{
			name: "有规则信息",
			input: &assets.ProcessAssets{
				RuleInfos: []*assets.RuleInfo{
					{
						Product: "test_product",
					},
				},
			},
			expected: []*assets.RuleInfo{
				{
					Product: "test_product",
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			handler, ok := frame.GetFieldHandlerForAsset[[]*assets.RuleInfo]("product")
			assert.True(t, ok)
			result, err := handler.Getter([]uint64{1}, map[uint64][]string{
				1: {"1"},
			}, map[string]*assets.ProcessAssets{
				"1": tt.input,
			})

			assert.NoError(t, err)

			if tt.expected == nil {
				assert.Equal(t, 0, len(result.Values))
				return
			}

			actual := result.Values[0]
			assert.Equal(t, len(tt.expected), len(actual))

			if len(tt.expected) > 0 {
				assert.Equal(t, tt.expected[0].Product, actual[0].Product)
			}
		})
	}
}

func TestRuleInfoSourceMerge(t *testing.T) {
	asset := &assets.Assets{}

	results := []map[string]interface{}{
		{
			"source": "1-1",
			"rule_infos": []*assets.RuleInfo{
				{
					Product: "test_product",
				},
			},
		},
	}

	handler, ok := frame.GetFieldHandlerForAsset[map[string]interface{}]("rule_info_source")
	assert.True(t, ok)
	err := handler.ResultHandler(asset, results)

	assert.NoError(t, err)
	assert.Len(t, asset.RuleInfosSource["1-1"], 1)
	assert.Equal(t, "test_product", asset.RuleInfosSource["1-1"][0].Product)
}

func TestAnyToRuleInfo(t *testing.T) {
	tests := []struct {
		name     string
		input    any
		expected []*assets.RuleInfo
	}{
		{
			name:     "nil input",
			input:    nil,
			expected: []*assets.RuleInfo{},
		},
		{
			name: "valid input",
			input: []*assets.RuleInfo{
				{
					Product: "test_product",
				},
			},
			expected: []*assets.RuleInfo{
				{
					Product: "test_product",
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			actual := anyToRuleInfo(tt.input)
			assert.Equal(t, len(tt.expected), len(actual))

			if len(tt.expected) > 0 {
				assert.Equal(t, tt.expected[0].Product, actual[0].Product)
			}
		})
	}
}

func TestRegisterFieldsCompleteness(t *testing.T) {
	allFieldHandler := frame.GetAllFieldHandlerForAsset()
	handler, ok := allFieldHandler["product"]
	assert.True(t, ok)
	h, ok := handler.(frame.FieldHandlerForAsset[[]*assets.RuleInfo])
	assert.True(t, ok)
	assert.Equal(t, h.GetAllData(), false)

	handler, ok = allFieldHandler["rule_info_source"]
	assert.True(t, ok)
	hh, ok := handler.(frame.FieldHandlerForAsset[map[string]interface{}])
	assert.True(t, ok)
	assert.Equal(t, hh.GetAllData(), true)
}
