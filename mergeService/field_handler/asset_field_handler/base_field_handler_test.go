package asset_field_handler

import (
	frame "fobrain/mergeFrame"
	"fobrain/models/elastic/assets"
	"testing"

	"github.com/stretchr/testify/assert"
)

func Test_RegisteredFieldHandlers(t *testing.T) {
	// 准备测试数据
	testCases := []struct {
		name       string
		fieldName  string
		dataSource assets.ProcessAssets
		asset      assets.Assets
		wantValue  any
		wantBool   bool
		wantResult []string
	}{
		{
			name:      "IpSegment处理",
			fieldName: "IpSegment",
			dataSource: assets.ProcessAssets{
				IpSegment: "***********/24",
			},
			asset:      assets.Assets{},
			wantValue:  []string{"***********/24"},
			wantBool:   true,
			wantResult: []string{"***********/24"},
		},
		{
			name:      "空IpSegment处理",
			fieldName: "IpSegment",
			dataSource: assets.ProcessAssets{
				IpSegment: "",
			},
			asset:      assets.Assets{},
			wantValue:  []string{},
			wantBool:   false,
			wantResult: []string{},
		},
		{
			name:      "HostName处理",
			fieldName: "HostName",
			dataSource: assets.ProcessAssets{
				HostName: "test-host",
			},
			asset:      assets.Assets{},
			wantValue:  []string{"test-host"},
			wantBool:   true,
			wantResult: []string{"test-host"},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 获取注册的处理器
			fieldHandler, ok := frame.GetFieldHandlerForAsset[string](tc.fieldName)
			assert.True(t, ok, "字段处理器不应为nil")
			assert.NotNil(t, fieldHandler, "字段处理器不应为nil")

			// 测试checkFunc
			gotValue, err := fieldHandler.Getter([]uint64{1}, map[uint64][]string{
				1: {"s1"},
			}, map[string]*assets.ProcessAssets{
				"s1": &tc.dataSource,
			})
			assert.NoError(t, err)
			assert.Equal(t, tc.wantValue, gotValue.Values)

			// 测试resultHandlerFunc
			err = fieldHandler.ResultHandler(&tc.asset, gotValue.Values)
			assert.NoError(t, err)
			// assert.Equal(t, tc.wantResult, tc.asset)
		})
	}
}

// 测试重复注册的情况
func Test_DuplicateRegistration(t *testing.T) {
	// 尝试重复注册一个已存在的处理器
	err := frame.RegisterFieldHandlerForAsset[string]("IpSegment", false, func(dataSource *assets.ProcessAssets) (string, bool) {
		return frame.StrFieldCheck(dataSource.IpSegment)
	}, func(asset *assets.Assets, result []string) error {
		return frame.StrSliceFieldResultHandler(&asset.IpSegment, result)
	})
	assert.Error(t, err)
}

// 测试注册的完整性
func Test_RegisteredFieldsCompleteness(t *testing.T) {
	expectedFields := []string{
		"IpSegment", "HostName", "EthName", "Os", "Kernel",
		"Model", "Maker", "Sn", "Mac",
		"MachineRoom", "MemorySize", "MemoryUsageRate",
		"CpuMaker", "CpuBrand", "DiskUsageRate", "LoadAverage",
	}

	for _, field := range expectedFields {
		handler, exist := frame.GetFieldHandlerForAsset[string](field)
		assert.True(t, exist, "字段 %s 的getter未注册", field)
		assert.NotNil(t, handler, "字段 %s 的handler未注册", field)
	}
	expectedFields = []string{
		"Oper",
	}

	for _, field := range expectedFields {
		handler, exist := frame.GetFieldHandlerForAsset[*assets.PersonWithMapping](field)
		assert.True(t, exist, "字段 %s 的getter未注册", field)
		assert.NotNil(t, handler, "字段 %s 的handler未注册", field)
	}
	expectedFields = []string{
		"Ports",
	}

	for _, field := range expectedFields {
		handler, exist := frame.GetFieldHandlerForAsset[[]*assets.PortInfo](field)
		assert.True(t, exist, "字段 %s 的getter未注册", field)
		assert.NotNil(t, handler, "字段 %s 的handler未注册", field)
	}
	expectedFields = []string{
		"Status", "CpuCount", "DiskCount", "DiskSize",
	}

	for _, field := range expectedFields {
		handler, exist := frame.GetFieldHandlerForAsset[int](field)
		assert.True(t, exist, "字段 %s 的getter未注册", field)
		assert.NotNil(t, handler, "字段 %s 的handler未注册", field)
	}
}

func TestBaseFieldHandler_IpSegment(t *testing.T) {
	// 测试 IpSegment 字段处理器
	dataSource := &assets.ProcessAssets{
		IpSegment: "***********/24",
	}
	handler, ok := frame.GetFieldHandlerForAsset[string]("IpSegment")
	assert.True(t, ok)
	assert.NotNil(t, handler)

	sourcePriority := []uint64{1}
	sourceList := map[uint64][]string{
		1: {"test"},
	}
	value, err := handler.(*frame.GenericFieldHandlerForAsset[string]).Getter(sourcePriority, sourceList, map[string]*assets.ProcessAssets{"test": dataSource})
	assert.NoError(t, err)
	assert.NotEmpty(t, value.Values)
	assert.Equal(t, "***********/24", value.Values[0])
}

func TestBaseFieldHandler_HostName(t *testing.T) {
	// 测试 HostName 字段处理器
	dataSource := &assets.ProcessAssets{
		HostName: "test-server",
	}
	handler, ok := frame.GetFieldHandlerForAsset[string]("HostName")
	assert.True(t, ok)
	assert.NotNil(t, handler)

	sourcePriority := []uint64{1}
	sourceList := map[uint64][]string{
		1: {"test"},
	}
	value, err := handler.(*frame.GenericFieldHandlerForAsset[string]).Getter(sourcePriority, sourceList, map[string]*assets.ProcessAssets{"test": dataSource})
	assert.NoError(t, err)
	assert.NotEmpty(t, value.Values)
	assert.Equal(t, "test-server", value.Values[0])
}

func TestBaseFieldHandler_EthName(t *testing.T) {
	// 测试 EthName 字段处理器
	dataSource := &assets.ProcessAssets{
		EthName: "eth0",
	}
	handler, ok := frame.GetFieldHandlerForAsset[string]("EthName")
	assert.True(t, ok)
	assert.NotNil(t, handler)

	sourcePriority := []uint64{1}
	sourceList := map[uint64][]string{
		1: {"test"},
	}
	value, err := handler.(*frame.GenericFieldHandlerForAsset[string]).Getter(sourcePriority, sourceList, map[string]*assets.ProcessAssets{"test": dataSource})
	assert.NoError(t, err)
	assert.NotEmpty(t, value.Values)
	assert.Equal(t, "eth0", value.Values[0])
}

func TestBaseFieldHandler_Os(t *testing.T) {
	// 测试 Os 字段处理器
	dataSource := &assets.ProcessAssets{
		Os: "Linux",
	}
	handler, ok := frame.GetFieldHandlerForAsset[string]("Os")
	assert.True(t, ok)
	assert.NotNil(t, handler)

	sourcePriority := []uint64{1}
	sourceList := map[uint64][]string{
		1: {"test"},
	}
	value, err := handler.(*frame.GenericFieldHandlerForAsset[string]).Getter(sourcePriority, sourceList, map[string]*assets.ProcessAssets{"test": dataSource})
	assert.NoError(t, err)
	assert.NotEmpty(t, value.Values)
	assert.Equal(t, "Linux", value.Values[0])
}

func TestBaseFieldHandler_Kernel(t *testing.T) {
	// 测试 Kernel 字段处理器
	dataSource := &assets.ProcessAssets{
		Kernel: "4.15.0",
	}
	handler, ok := frame.GetFieldHandlerForAsset[string]("Kernel")
	assert.True(t, ok)
	assert.NotNil(t, handler)

	sourcePriority := []uint64{1}
	sourceList := map[uint64][]string{
		1: {"test"},
	}
	value, err := handler.(*frame.GenericFieldHandlerForAsset[string]).Getter(sourcePriority, sourceList, map[string]*assets.ProcessAssets{"test": dataSource})
	assert.NoError(t, err)
	assert.NotEmpty(t, value.Values)
	assert.Equal(t, "4.15.0", value.Values[0])
}

func TestBaseFieldHandler_Model(t *testing.T) {
	// 测试 Model 字段处理器
	dataSource := &assets.ProcessAssets{
		Model: "X86_64",
	}
	handler, ok := frame.GetFieldHandlerForAsset[string]("Model")
	assert.True(t, ok)
	assert.NotNil(t, handler)

	sourcePriority := []uint64{1}
	sourceList := map[uint64][]string{
		1: {"test"},
	}
	value, err := handler.(*frame.GenericFieldHandlerForAsset[string]).Getter(sourcePriority, sourceList, map[string]*assets.ProcessAssets{"test": dataSource})
	assert.NoError(t, err)
	assert.NotEmpty(t, value.Values)
	assert.Equal(t, "X86_64", value.Values[0])
}

func TestBaseFieldHandler_Maker(t *testing.T) {
	// 测试 Maker 字段处理器
	dataSource := &assets.ProcessAssets{
		Maker: "Dell",
	}
	handler, ok := frame.GetFieldHandlerForAsset[string]("Maker")
	assert.True(t, ok)
	assert.NotNil(t, handler)

	sourcePriority := []uint64{1}
	sourceList := map[uint64][]string{
		1: {"test"},
	}
	value, err := handler.(*frame.GenericFieldHandlerForAsset[string]).Getter(sourcePriority, sourceList, map[string]*assets.ProcessAssets{"test": dataSource})
	assert.NoError(t, err)
	assert.NotEmpty(t, value.Values)
	assert.Equal(t, "Dell", value.Values[0])
}

func TestBaseFieldHandler_Sn(t *testing.T) {
	// 测试 Sn 字段处理器
	dataSource := &assets.ProcessAssets{
		Sn: "SN123456",
	}
	handler, ok := frame.GetFieldHandlerForAsset[string]("Sn")
	assert.True(t, ok)
	assert.NotNil(t, handler)

	sourcePriority := []uint64{1}
	sourceList := map[uint64][]string{
		1: {"test"},
	}
	value, err := handler.(*frame.GenericFieldHandlerForAsset[string]).Getter(sourcePriority, sourceList, map[string]*assets.ProcessAssets{"test": dataSource})
	assert.NoError(t, err)
	assert.NotEmpty(t, value.Values)
	assert.Equal(t, "SN123456", value.Values[0])
}

func TestBaseFieldHandler_Mac(t *testing.T) {
	// 测试 Mac 字段处理器
	dataSource := &assets.ProcessAssets{
		Mac: "00:11:22:33:44:55",
	}
	handler, ok := frame.GetFieldHandlerForAsset[string]("Mac")
	assert.True(t, ok)
	assert.NotNil(t, handler)

	sourcePriority := []uint64{1}
	sourceList := map[uint64][]string{
		1: {"test"},
	}
	value, err := handler.(*frame.GenericFieldHandlerForAsset[string]).Getter(sourcePriority, sourceList, map[string]*assets.ProcessAssets{"test": dataSource})
	assert.NoError(t, err)
	assert.NotEmpty(t, value.Values)
	assert.Equal(t, "00:11:22:33:44:55", value.Values[0])
}

func TestBaseFieldHandler_Oper(t *testing.T) {
	// 测试 Oper 字段处理器
	dataSource := &assets.ProcessAssets{
		Oper:        "admin1,admin2",
		Source:      123,
		Node:        456,
		PersonField: "name",
	}
	handler, ok := frame.GetFieldHandlerForAsset[*assets.PersonWithMapping]("Oper")
	assert.True(t, ok)
	assert.NotNil(t, handler)

	sourcePriority := []uint64{1}
	sourceList := map[uint64][]string{
		1: {"test"},
	}
	value, err := handler.(*frame.GenericFieldHandlerForAsset[*assets.PersonWithMapping]).Getter(sourcePriority, sourceList, map[string]*assets.ProcessAssets{"test": dataSource})
	assert.NoError(t, err)
	assert.NotEmpty(t, value.Values)
	assert.Equal(t, uint64(123), value.Values[0].SourceId)
	assert.Equal(t, uint64(456), value.Values[0].NodeId)
	assert.Equal(t, "admin1,admin2", value.Values[0].SourceValue)
	assert.Equal(t, "name", value.Values[0].MappingField)
}

func TestBaseFieldHandler_MachineRoom(t *testing.T) {
	// 测试 MachineRoom 字段处理器
	dataSource := &assets.ProcessAssets{
		MachineRoom: "Room A",
	}
	handler, ok := frame.GetFieldHandlerForAsset[string]("MachineRoom")
	assert.True(t, ok)
	assert.NotNil(t, handler)

	sourcePriority := []uint64{1}
	sourceList := map[uint64][]string{
		1: {"test"},
	}
	value, err := handler.(*frame.GenericFieldHandlerForAsset[string]).Getter(sourcePriority, sourceList, map[string]*assets.ProcessAssets{"test": dataSource})
	assert.NoError(t, err)
	assert.NotEmpty(t, value.Values)
	assert.Equal(t, "Room A", value.Values[0])
}

func TestBaseFieldHandler_MemorySize(t *testing.T) {
	// 测试 MemorySize 字段处理器
	dataSource := &assets.ProcessAssets{
		MemorySize: "16GB",
	}
	handler, ok := frame.GetFieldHandlerForAsset[string]("MemorySize")
	assert.True(t, ok)
	assert.NotNil(t, handler)

	sourcePriority := []uint64{1}
	sourceList := map[uint64][]string{
		1: {"test"},
	}
	value, err := handler.(*frame.GenericFieldHandlerForAsset[string]).Getter(sourcePriority, sourceList, map[string]*assets.ProcessAssets{"test": dataSource})
	assert.NoError(t, err)
	assert.NotEmpty(t, value.Values)
	assert.Equal(t, "16GB", value.Values[0])
}

func TestBaseFieldHandler_MemoryUsageRate(t *testing.T) {
	// 测试 MemoryUsageRate 字段处理器
	dataSource := &assets.ProcessAssets{
		MemoryUsageRate: "50%",
	}
	handler, ok := frame.GetFieldHandlerForAsset[string]("MemoryUsageRate")
	assert.True(t, ok)
	assert.NotNil(t, handler)

	sourcePriority := []uint64{1}
	sourceList := map[uint64][]string{
		1: {"test"},
	}
	value, err := handler.(*frame.GenericFieldHandlerForAsset[string]).Getter(sourcePriority, sourceList, map[string]*assets.ProcessAssets{"test": dataSource})
	assert.NoError(t, err)
	assert.NotEmpty(t, value.Values)
	assert.Equal(t, "50%", value.Values[0])
}

func TestBaseFieldHandler_CpuMaker(t *testing.T) {
	// 测试 CpuMaker 字段处理器
	dataSource := &assets.ProcessAssets{
		CpuMaker: "Intel",
	}
	handler, ok := frame.GetFieldHandlerForAsset[string]("CpuMaker")
	assert.True(t, ok)
	assert.NotNil(t, handler)

	sourcePriority := []uint64{1}
	sourceList := map[uint64][]string{
		1: {"test"},
	}
	value, err := handler.(*frame.GenericFieldHandlerForAsset[string]).Getter(sourcePriority, sourceList, map[string]*assets.ProcessAssets{"test": dataSource})
	assert.NoError(t, err)
	assert.NotEmpty(t, value.Values)
	assert.Equal(t, "Intel", value.Values[0])
}

func TestBaseFieldHandler_CpuBrand(t *testing.T) {
	// 测试 CpuBrand 字段处理器
	dataSource := &assets.ProcessAssets{
		CpuBrand: "Core i7",
	}
	handler, ok := frame.GetFieldHandlerForAsset[string]("CpuBrand")
	assert.True(t, ok)
	assert.NotNil(t, handler)

	sourcePriority := []uint64{1}
	sourceList := map[uint64][]string{
		1: {"test"},
	}
	value, err := handler.(*frame.GenericFieldHandlerForAsset[string]).Getter(sourcePriority, sourceList, map[string]*assets.ProcessAssets{"test": dataSource})
	assert.NoError(t, err)
	assert.NotEmpty(t, value.Values)
	assert.Equal(t, "Core i7", value.Values[0])
}

func TestBaseFieldHandler_DiskUsageRate(t *testing.T) {
	// 测试 DiskUsageRate 字段处理器
	dataSource := &assets.ProcessAssets{
		DiskUsageRate: "75%",
	}
	handler, ok := frame.GetFieldHandlerForAsset[string]("DiskUsageRate")
	assert.True(t, ok)
	assert.NotNil(t, handler)

	sourcePriority := []uint64{1}
	sourceList := map[uint64][]string{
		1: {"test"},
	}
	value, err := handler.(*frame.GenericFieldHandlerForAsset[string]).Getter(sourcePriority, sourceList, map[string]*assets.ProcessAssets{"test": dataSource})
	assert.NoError(t, err)
	assert.NotEmpty(t, value.Values)
	assert.Equal(t, "75%", value.Values[0])
}

func TestBaseFieldHandler_LoadAverage(t *testing.T) {
	// 测试 LoadAverage 字段处理器
	dataSource := &assets.ProcessAssets{
		LoadAverage: "1.5",
	}
	handler, ok := frame.GetFieldHandlerForAsset[string]("LoadAverage")
	assert.True(t, ok)
	assert.NotNil(t, handler)

	sourcePriority := []uint64{1}
	sourceList := map[uint64][]string{
		1: {"test"},
	}
	value, err := handler.(*frame.GenericFieldHandlerForAsset[string]).Getter(sourcePriority, sourceList, map[string]*assets.ProcessAssets{"test": dataSource})
	assert.NoError(t, err)
	assert.NotEmpty(t, value.Values)
	assert.Equal(t, "1.5", value.Values[0])
}

func TestBaseFieldHandler_CpuCount(t *testing.T) {
	// 测试 CpuCount 字段处理器
	dataSource := &assets.ProcessAssets{
		CpuCount: 8,
	}
	handler, ok := frame.GetFieldHandlerForAsset[int]("CpuCount")
	assert.True(t, ok)
	assert.NotNil(t, handler)

	sourcePriority := []uint64{1}
	sourceList := map[uint64][]string{
		1: {"test"},
	}
	value, err := handler.(*frame.GenericFieldHandlerForAsset[int]).Getter(sourcePriority, sourceList, map[string]*assets.ProcessAssets{"test": dataSource})
	assert.NoError(t, err)
	assert.NotEmpty(t, value.Values)
	assert.Equal(t, 8, value.Values[0])
}

func TestBaseFieldHandler_DiskCount(t *testing.T) {
	// 测试 DiskCount 字段处理器
	dataSource := &assets.ProcessAssets{
		DiskCount: 2,
	}
	handler, ok := frame.GetFieldHandlerForAsset[int]("DiskCount")
	assert.True(t, ok)
	assert.NotNil(t, handler)

	sourcePriority := []uint64{1}
	sourceList := map[uint64][]string{
		1: {"test"},
	}
	value, err := handler.(*frame.GenericFieldHandlerForAsset[int]).Getter(sourcePriority, sourceList, map[string]*assets.ProcessAssets{"test": dataSource})
	assert.NoError(t, err)
	assert.NotEmpty(t, value.Values)
	assert.Equal(t, 2, value.Values[0])
}

func TestBaseFieldHandler_DiskSize(t *testing.T) {
	// 测试 DiskSize 字段处理器
	dataSource := &assets.ProcessAssets{
		DiskSize: 1000,
	}
	handler, ok := frame.GetFieldHandlerForAsset[int]("DiskSize")
	assert.True(t, ok)
	assert.NotNil(t, handler)

	sourcePriority := []uint64{1}
	sourceList := map[uint64][]string{
		1: {"test"},
	}
	value, err := handler.(*frame.GenericFieldHandlerForAsset[int]).Getter(sourcePriority, sourceList, map[string]*assets.ProcessAssets{"test": dataSource})
	assert.NoError(t, err)
	assert.NotEmpty(t, value.Values)
	assert.Equal(t, 1000, value.Values[0])
}
