package asset_field_handler

import (
	frame "fobrain/mergeFrame"
	"fobrain/models/elastic/assets"
	"fobrain/pkg/utils"
)

func init() {
	_ = frame.RegisterFieldHandlerForAsset[int]("status", false, func(dataSource *assets.ProcessAssets) (int, bool) {
		return dataSource.Status, true
	}, func(asset *assets.Assets, result []int) error {
		allStatus := make([]int, 0)
		for _, v := range result {
			allStatus = append(allStatus, v)
		}
		asset.Status = assetStatusMerge(allStatus)
		return nil
	})
}

// assetStatusMerge 资产状态合并
func assetStatusMerge(allStatus []int) int {
	status := 1
	allStatus = utils.ListDistinctNonZero[int](allStatus)
	// 如果状态为空，则设置为1，表示在线
	if len(allStatus) == 0 {
		return status
	} else if len(allStatus) > 1 {
		// 只要有一个状态为1，则设置为1，表示在线
		// 如果状态大于1，则设置为1，表示在线
		return status
	} else {
		// 否则设置为数据源上报的状态
		status = allStatus[0]
		// 如果状态不为1和2，则设置为1，表示在线
		if status != 1 && status != 2 {
			status = 1
		}
	}
	return status
}
