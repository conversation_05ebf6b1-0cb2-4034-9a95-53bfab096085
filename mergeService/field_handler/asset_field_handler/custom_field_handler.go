package asset_field_handler

import (
	frame "fobrain/mergeFrame"
	"fobrain/models/elastic/assets"
	"maps"
)

func init() {
	frame.RegisterFieldHandlerForAsset[map[string]string]("custom_fields", true, func(dataSource *assets.ProcessAssets) (map[string]string, bool) {
		if len(dataSource.CustomFields) > 0 {
			return dataSource.CustomFields, true
		}
		return nil, false
	}, func(asset *assets.Assets, result []map[string]string) error {
		asset.CustomFields = make(map[string]string)
		for _, customFields := range result {
			// 将any转换为map[string]string
			maps.Copy(asset.CustomFields, customFields)
		}
		return nil
	})
}
