package asset_field_handler

import (
	frame "fobrain/mergeFrame"
	"fobrain/models/elastic/assets"
)

func init() {
	_ = frame.RegisterFieldHandlerForAsset[[]*assets.PortInfo]("ports", false, func(dataSource *assets.ProcessAssets) ([]*assets.PortInfo, bool) {
		if len(dataSource.Ports) == 0 {
			return nil, false
		}
		return dataSource.Ports, true
	}, func(asset *assets.Assets, result [][]*assets.PortInfo) error {
		if len(asset.Ports) == 0 {
			asset.Ports = make([]*assets.PortInfo, 0)
		}
		for _, ports := range result {
			if ports != nil {
				for _, port := range ports {
					exist := false
					for _, existPort := range asset.Ports {
						if existPort.Equal(port) {
							exist = true
							break
						}
					}
					if !exist {
						asset.Ports = append(asset.Ports, port)
					}
				}
			}
		}
		return nil
	})
}
