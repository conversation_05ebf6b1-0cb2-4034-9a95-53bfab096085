package asset_field_handler

import (
	frame "fobrain/mergeFrame"
	"fobrain/models/elastic/assets"
	"testing"

	"github.com/stretchr/testify/assert"
)

func Test_PortsFieldHandler(t *testing.T) {
	tests := []struct {
		name       string
		dataSource map[string]*assets.ProcessAssets
		wantValue  []*assets.PortInfo
		wantBool   bool
		wantPorts  []*assets.PortInfo
	}{
		{
			name: "空端口列表",
			dataSource: map[string]*assets.ProcessAssets{
				"s1": {
					Ports: []*assets.PortInfo{},
				},
			},
			wantValue: nil,
			wantBool:  false,
			wantPorts: []*assets.PortInfo{},
		},
		{
			name: "单个端口",
			dataSource: map[string]*assets.ProcessAssets{
				"s1": {
					Ports: []*assets.PortInfo{
						{
							Port:     80,
							Protocol: "tcp",
							Status:   1,
							Url:      "http://localhost:80",
							Domain:   "localhost",
							Title:    "test",
						},
					},
				},
			},
			wantValue: []*assets.PortInfo{
				{
					Port:     80,
					Protocol: "tcp",
					Status:   1,
					Url:      "http://localhost:80",
					Domain:   "localhost",
					Title:    "test",
				},
			},
			wantBool: true,
			wantPorts: []*assets.PortInfo{
				{
					Port:     80,
					Protocol: "tcp",
					Status:   1,
					Url:      "http://localhost:80",
					Domain:   "localhost",
					Title:    "test",
				},
			},
		},
		{
			name: "合并重复端口",
			dataSource: map[string]*assets.ProcessAssets{
				"s1": {
					Ports: []*assets.PortInfo{
						{
							Port:     80,
							Protocol: "tcp",
							Status:   1,
							Url:      "http://localhost:80",
							Domain:   "localhost",
							Title:    "test",
						},
					},
				},
				"s2": {
					Ports: []*assets.PortInfo{
						{
							Port:     80,
							Protocol: "tcp",
							Status:   1,
							Url:      "http://localhost:80",
							Domain:   "localhost",
							Title:    "test",
						},
					},
				},
			},
			wantValue: []*assets.PortInfo{
				{
					Port:     80,
					Protocol: "tcp",
					Status:   1,
					Url:      "http://localhost:80",
					Domain:   "localhost",
					Title:    "test",
				},
			},
			wantBool: true,
			wantPorts: []*assets.PortInfo{
				{
					Port:     80,
					Protocol: "tcp",
					Status:   1,
					Url:      "http://localhost:80",
					Domain:   "localhost",
					Title:    "test",
				},
			},
		},
		{
			name: "合并不同端口",
			dataSource: map[string]*assets.ProcessAssets{
				"s1": {
					Ports: []*assets.PortInfo{
						{
							Port:     80,
							Protocol: "tcp",
							Status:   1,
							Url:      "http://localhost:80",
							Domain:   "localhost",
							Title:    "test",
						},
					},
				},
				"s2": {
					Ports: []*assets.PortInfo{
						{
							Port:     443,
							Protocol: "tcp",
							Status:   1,
							Url:      "http://localhost:443",
							Domain:   "localhost",
							Title:    "test",
						},
					},
				},
			},
			wantValue: []*assets.PortInfo{
				{
					Port:     80,
					Protocol: "tcp",
					Status:   1,
					Url:      "http://localhost:80",
					Domain:   "localhost",
					Title:    "test",
				},
				{
					Port:     443,
					Protocol: "tcp",
					Status:   1,
					Url:      "http://localhost:443",
					Domain:   "localhost",
					Title:    "test",
				},
			},
			wantBool: true,
			wantPorts: []*assets.PortInfo{
				{
					Port:     443,
					Protocol: "tcp",
					Status:   1,
					Url:      "http://localhost:443",
					Domain:   "localhost",
					Title:    "test",
				},
				{
					Port:     80,
					Protocol: "tcp",
					Status:   1,
					Url:      "http://localhost:80",
					Domain:   "localhost",
					Title:    "test",
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 获取注册的处理器
			fieldHandler, ok := frame.GetFieldHandlerForAsset[[]*assets.PortInfo]("ports")
			assert.True(t, ok)
			assert.NotNil(t, fieldHandler)

			// 测试checkFunc
			gotValue, err := fieldHandler.Getter([]uint64{1}, map[uint64][]string{
				1: {"s1", "s2"},
			}, tt.dataSource)
			assert.NoError(t, err)
			for _, wantPort := range tt.wantValue {
				found := false
				for _, gotPortValue := range gotValue.Values {
					for _, port := range gotPortValue {
						if port.Equal(wantPort) {
							found = true
							break
						}
					}
					if found {
						break
					}
				}
				if !found {
					t.Errorf("期望的端口 %v 未在结果中找到", wantPort)
				}
			}

			// 测试resultHandlerFunc
			asset := &assets.Assets{}
			err = fieldHandler.ResultHandler(asset, gotValue.Values)
			assert.NoError(t, err)

			// 验证每个端口是否都在结果中
			for _, wantPort := range tt.wantPorts {
				found := false
				for _, gotPort := range asset.Ports {
					if gotPort.Equal(wantPort) {
						found = true
						break
					}
				}
				if !found {
					t.Errorf("期望的端口 %v 未在结果中找到", wantPort)
				}
			}
		})
	}
}
