package asset_field_handler

import (
	"testing"
	"time"

	"fobrain/fobrain/common/localtime"
	frame "fobrain/mergeFrame"
	"fobrain/models/elastic/assets"

	"github.com/stretchr/testify/assert"
)

func TestLastResponseFieldHandler_EmptyResult(t *testing.T) {
	// 测试空结果的情况
	handler, ok := frame.GetFieldHandlerForAsset[*localtime.Time]("last_response_at")
	assert.True(t, ok)
	assert.NotNil(t, handler)

	sourcePriority := []uint64{1}
	sourceList := map[uint64][]string{
		1: {"test"},
	}
	value, err := handler.(*frame.GenericFieldHandlerForAsset[*localtime.Time]).Getter(sourcePriority, sourceList, map[string]*assets.ProcessAssets{"test": {}})
	assert.NoError(t, err)
	assert.NotNil(t, value.Values)
	assert.Len(t, value.Values, 1)
	assert.Nil(t, value.Values[0])

	// 测试结果处理器
	asset := &assets.Assets{}
	err = handler.ResultHandler(asset, value.Values)
	assert.NoError(t, err)
	assert.Nil(t, asset.LastResponseAt)
}

func TestLastResponseFieldHandler_SingleTime(t *testing.T) {
	// 测试单个时间值的情况
	now := localtime.NewLocalTime(time.Now())
	dataSource := &assets.ProcessAssets{
		LastResponseAt: now,
	}
	handler, ok := frame.GetFieldHandlerForAsset[*localtime.Time]("last_response_at")
	assert.True(t, ok)
	assert.NotNil(t, handler)

	sourcePriority := []uint64{1}
	sourceList := map[uint64][]string{
		1: {"test"},
	}
	value, err := handler.(*frame.GenericFieldHandlerForAsset[*localtime.Time]).Getter(sourcePriority, sourceList, map[string]*assets.ProcessAssets{"test": dataSource})
	assert.NoError(t, err)
	assert.NotEmpty(t, value.Values)
	assert.Equal(t, now, value.Values[0])

	// 测试结果处理器
	asset := &assets.Assets{}
	err = handler.ResultHandler(asset, value.Values)
	assert.NoError(t, err)
	assert.Equal(t, now, asset.LastResponseAt)
}

func TestLastResponseFieldHandler_MultipleTimes(t *testing.T) {
	// 测试多个时间值的情况，选择最晚的时间
	time1 := localtime.NewLocalTime(time.Now().Add(-24 * time.Hour))
	time2 := localtime.NewLocalTime(time.Now())
	time3 := localtime.NewLocalTime(time.Now().Add(-12 * time.Hour))

	dataSource1 := &assets.ProcessAssets{
		LastResponseAt: time1,
	}
	dataSource2 := &assets.ProcessAssets{
		LastResponseAt: time2,
	}
	dataSource3 := &assets.ProcessAssets{
		LastResponseAt: time3,
	}

	handler, ok := frame.GetFieldHandlerForAsset[*localtime.Time]("last_response_at")
	assert.True(t, ok)
	assert.NotNil(t, handler)

	sourcePriority := []uint64{1}
	sourceList := map[uint64][]string{
		1: {"test1", "test2", "test3"},
	}
	value, err := handler.(*frame.GenericFieldHandlerForAsset[*localtime.Time]).Getter(sourcePriority, sourceList, map[string]*assets.ProcessAssets{
		"test1": dataSource1,
		"test2": dataSource2,
		"test3": dataSource3,
	})
	assert.NoError(t, err)
	assert.Len(t, value.Values, 3)

	// 测试结果处理器
	asset := &assets.Assets{}
	err = handler.ResultHandler(asset, value.Values)
	assert.NoError(t, err)
	assert.Equal(t, time2, asset.LastResponseAt)
}

func TestLastResponseFieldHandler_WithNilTime(t *testing.T) {
	// 测试包含 nil 时间值的情况
	time1 := localtime.NewLocalTime(time.Now())
	time2 := localtime.NewLocalTime(time.Now().Add(24 * time.Hour))

	dataSource1 := &assets.ProcessAssets{
		LastResponseAt: time1,
	}
	dataSource2 := &assets.ProcessAssets{
		LastResponseAt: nil,
	}
	dataSource3 := &assets.ProcessAssets{
		LastResponseAt: time2,
	}

	handler, ok := frame.GetFieldHandlerForAsset[*localtime.Time]("last_response_at")
	assert.True(t, ok)
	assert.NotNil(t, handler)

	sourcePriority := []uint64{1}
	sourceList := map[uint64][]string{
		1: {"test1", "test2", "test3"},
	}
	value, err := handler.(*frame.GenericFieldHandlerForAsset[*localtime.Time]).Getter(sourcePriority, sourceList, map[string]*assets.ProcessAssets{
		"test1": dataSource1,
		"test2": dataSource2,
		"test3": dataSource3,
	})
	assert.NoError(t, err)
	assert.Len(t, value.Values, 3)

	// 测试结果处理器
	asset := &assets.Assets{}
	err = handler.ResultHandler(asset, value.Values)
	assert.NoError(t, err)
	assert.Equal(t, time2, asset.LastResponseAt)
}
