package asset_field_handler

import (
	frame "fobrain/mergeFrame"
	"fobrain/models/elastic/assets"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestCustomFieldHandler(t *testing.T) {
	tests := []struct {
		name       string
		dataSource map[string]*assets.ProcessAssets
		wantValue  map[string]string
		wantBool   bool
	}{
		{
			name: "空数据",
			dataSource: map[string]*assets.ProcessAssets{
				"s1": &assets.ProcessAssets{},
			},
			wantValue: map[string]string{},
			wantBool:  false,
		},
		{
			name: "自定义字段",
			dataSource: map[string]*assets.ProcessAssets{
				"s1": &assets.ProcessAssets{
					CustomFields: map[string]string{
						"custom1": "值1",
						"custom2": "值2",
					},
				},
			},
			wantValue: map[string]string{
				"custom1": "值1",
				"custom2": "值2",
			},
			wantBool: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 获取注册的处理器
			fieldHandler, ok := frame.GetFieldHandlerForAsset[map[string]string]("custom_fields")
			assert.True(t, ok)
			assert.NotNil(t, fieldHandler)

			// 测试checkFunc
			gotValue, err := fieldHandler.Getter([]uint64{1}, map[uint64][]string{
				1: {"s1"},
			}, tt.dataSource)
			assert.NoError(t, err)

			// 测试resultHandlerFunc
			err = fieldHandler.ResultHandler(&assets.Assets{}, gotValue.Values)
			assert.NoError(t, err)
		})
	}
}
