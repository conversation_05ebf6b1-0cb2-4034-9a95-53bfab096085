// Package asset_field_handler handles asset field merging operations
package asset_field_handler

import (
	"fobrain/fobrain/common/localtime"
	frame "fobrain/mergeFrame"
	logs "fobrain/mergeService/utils/log"
	"fobrain/models/elastic/assets"
)

func init() {
	_ = frame.RegisterFieldHandlerForAsset[*localtime.Time]("last_response_at", true, func(dataSource *assets.ProcessAssets) (*localtime.Time, bool) {
		return dataSource.LastResponseAt, true
	}, func(asset *assets.Assets, result []*localtime.Time) error {
		// 添加日志记录
		if len(result) == 0 {
			logs.GetLogger("asset").Debug("last_response_at result is empty, setting to nil")
			asset.LastResponseAt = nil
			return nil
		}
		// 如果result为slice，则取最晚的时间
		var lastResponseAt *localtime.Time = nil
		for _, time := range result {
			if time == nil {
				// logs.GetLogger("asset").Warnf("invalid time format in last_response_at: %v", v)
				continue
			}
			if lastResponseAt == nil || (lastResponseAt != nil && time.After(*lastResponseAt)) {
				lastResponseAt = time
			}
		}
		asset.LastResponseAt = lastResponseAt
		return nil
	})
}
