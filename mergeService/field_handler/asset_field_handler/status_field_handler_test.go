package asset_field_handler

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestAssetStatusMerge(t *testing.T) {
	t.Run("status is empty", func(t *testing.T) {
		status := assetStatusMerge([]int{})
		assert.Equal(t, 1, status)
	})

	t.Run("status is 1", func(t *testing.T) {
		status := assetStatusMerge([]int{1})
		assert.Equal(t, 1, status)
	})

	t.Run("status is 0", func(t *testing.T) {
		status := assetStatusMerge([]int{0})
		assert.Equal(t, 1, status)
	})

	t.Run("status is 1 and 2", func(t *testing.T) {
		status := assetStatusMerge([]int{1, 2})
		assert.Equal(t, 1, status)
	})

	t.Run("status is 2 and 0 and 1", func(t *testing.T) {
		status := assetStatusMerge([]int{2, 0, 1})
		assert.Equal(t, 1, status)
	})

	t.Run("status is 2 ", func(t *testing.T) {
		status := assetStatusMerge([]int{2, 2})
		assert.Equal(t, 2, status)
	})
}
