package asset_field_handler

import (
	"testing"

	frame "fobrain/mergeFrame"
	"fobrain/models/elastic/assets"
	"fobrain/models/elastic/business_system"
	strategy "fobrain/services/strategy_business"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/stretchr/testify/assert"
)

func TestBusinessSourceFieldHandler(t *testing.T) {
	tests := []struct {
		name     string
		input    *assets.ProcessAssets
		expected *frame.Business
	}{
		{
			name: "空业务系统和所有者",
			input: &assets.ProcessAssets{
				BusinessSystem: "",
				BusinessOwner:  "",
				Source:         1,
				Node:           1,
			},
			expected: &frame.Business{
				Business: &assets.Business{
					System: "",
					Owner:  "",
					Source: "original",
				},
				Source: 1,
				Node:   1,
			},
		},
		{
			name: "有业务系统的内网IP",
			input: &assets.ProcessAssets{
				BusinessSystem: "test_system",
				BusinessOwner:  "test_owner",
				Source:         1,
				Node:           1,
				Ip:             "***********",
				NetworkType:    1, // 内网
			},
			expected: &frame.Business{
				Business: &assets.Business{
					System: "test_system",
					Owner:  "test_owner",
					Source: "original",
				},
				Source: 1,
				Node:   1,
			},
		},
	}
	patches := gomonkey.ApplyMethodReturn(business_system.NewBusinessSystems(), "GetBusinessByName", nil, nil)
	defer patches.Reset()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			handler, ok := frame.GetFieldHandlerForAsset[*frame.Business]("business_source")
			assert.True(t, ok)
			result, err := handler.Getter([]uint64{1}, map[uint64][]string{
				1: {"1"},
			}, map[string]*assets.ProcessAssets{
				"1": tt.input,
			})

			assert.NoError(t, err)
			actual := result.Values[0]

			assert.Equal(t, tt.expected.Business.System, actual.Business.System)
			assert.Equal(t, tt.expected.Business.Owner, actual.Business.Owner)
			assert.Equal(t, tt.expected.Business.Source, actual.Business.Source)
			assert.Equal(t, tt.expected.Source, actual.Source)
			assert.Equal(t, tt.expected.Node, actual.Node)
		})
	}
}

func TestBusinessFieldHandler(t *testing.T) {
	tests := []struct {
		name     string
		input    *assets.ProcessAssets
		expected *assets.Business
	}{
		{
			name: "空业务系统和所有者",
			input: &assets.ProcessAssets{
				BusinessSystem: "",
				BusinessOwner:  "",
			},
			expected: nil,
		},
		{
			name: "只有业务所有者",
			input: &assets.ProcessAssets{
				BusinessSystem: "",
				BusinessOwner:  "test_owner",
			},
			expected: &assets.Business{
				System: "",
				Owner:  "test_owner",
				Source: "original",
			},
		},
		{
			name: "有业务系统",
			input: &assets.ProcessAssets{
				BusinessSystem: "test_system",
				BusinessOwner:  "test_owner",
			},
			expected: &assets.Business{
				System: "test_system",
				Owner:  "test_owner",
				Source: "original",
			},
		},
	}

	patches := gomonkey.ApplyMethodReturn(strategy.NewStrategy(), "UpSet", nil, nil)
	patches.ApplyMethodReturn(business_system.NewBusinessSystems(), "GetBusinessByName", nil, nil)
	defer patches.Reset()

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			handler, ok := frame.GetFieldHandlerForAsset[[]*assets.BusinessInfo]("business")
			assert.True(t, ok)
			result, err := handler.Getter([]uint64{1}, map[uint64][]string{
				1: {"1"},
			}, map[string]*assets.ProcessAssets{
				"1": tt.input,
			})

			assert.NoError(t, err)

			if tt.expected == nil {
				assert.Equal(t, 0, len(result.Values))
				return
			}
			actual := result.Values[0]
			if !ok {
				t.Log("类型不符合预期，return")
				return
			}

			assert.Equal(t, tt.expected.System, actual[0].BusinessSystem)
			assert.Equal(t, tt.expected.Owner, actual[0].BusinessOwner)
		})
	}
}

func TestBusinessSourceMerge(t *testing.T) {
	asset := &assets.Assets{
		BusinessSource: make(map[string][]*assets.Business),
	}

	results := []*frame.Business{
		&frame.Business{
			Business: &assets.Business{
				System: "test_system",
				Owner:  "test_owner",
				Source: "original",
			},
			Source: 1,
			Node:   1,
		},
	}

	patches := gomonkey.ApplyMethodReturn(strategy.NewStrategy(), "UpSet", nil, nil)
	defer patches.Reset()

	handler, ok := frame.GetFieldHandlerForAsset[*frame.Business]("business_source")
	assert.True(t, ok)
	err := handler.ResultHandler(asset, results)

	assert.NoError(t, err)
	assert.Len(t, asset.BusinessSource["1-1"], 1)
	assert.Equal(t, "test_system", asset.BusinessSource["1-1"][0].System)
	assert.Equal(t, "test_owner", asset.BusinessSource["1-1"][0].Owner)
	assert.Equal(t, "original", asset.BusinessSource["1-1"][0].Source)
}

//func TestBusinessMerge(t *testing.T) {
//	asset := &assets.Assets{}
//
//	results := []any{
//		&assets.Business{
//			System: "test_system",
//			Owner:  "test_owner",
//			Source: "original",
//		},
//	}
//
//	patches := gomonkey.ApplyMethodReturn(strategy.NewStrategy(), "UpSet", nil, nil)
//	defer patches.Reset()
//
//	handler, ok := frame.GetFieldHandlerForAsset("business")
//	assert.True(t, ok)
//	err := handler.ResultHandler(asset, results)
//
//	assert.NoError(t, err)
//	assert.Equal(t, "test_system", asset.Business[0].System)
//	assert.Equal(t, "test_owner", asset.Business[0].Owner)
//	assert.Equal(t, "original", asset.Business[0].Source)
//}

func TestBusinessFieldHandlerRegisterFieldsCompleteness(t *testing.T) {
	allFieldHandler := frame.GetAllFieldHandlerForAsset()
	handler, ok := allFieldHandler["business"]
	assert.True(t, ok)
	h, ok := handler.(frame.FieldHandlerForAsset[[]*assets.BusinessInfo])
	assert.True(t, ok)
	assert.Equal(t, h.GetAllData(), false)

	handler, ok = allFieldHandler["business_source"]
	assert.True(t, ok)
	hh, ok := handler.(frame.FieldHandlerForAsset[*frame.Business])
	assert.True(t, ok)
	assert.Equal(t, hh.GetAllData(), true)
}
