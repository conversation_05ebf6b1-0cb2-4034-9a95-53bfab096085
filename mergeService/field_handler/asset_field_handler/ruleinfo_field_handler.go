package asset_field_handler

import (
	"fmt"
	frame "fobrain/mergeFrame"
	esmodel_asset "fobrain/models/elastic/assets"
)

func init() {
	// 产品来源字段处理
	_ = frame.RegisterFieldHandlerForAsset[map[string]interface{}]("rule_info_source", true, func(dataSource *esmodel_asset.ProcessAssets) (map[string]interface{}, bool) {
		return map[string]any{
			"source":     fmt.Sprintf("%d-%d", dataSource.Source, dataSource.Node),
			"rule_infos": dataSource.RuleInfos,
		}, true
	}, func(asset *esmodel_asset.Assets, result []map[string]interface{}) error {
		if len(result) == 0 {
			return nil
		}
		asset.RuleInfosSource = make(map[string][]*esmodel_asset.RuleInfo, 0)
		for _, val := range result {
			sourceKey, _ := val["source"].(string)
			asset.RuleInfosSource[sourceKey], _ = val["rule_infos"].([]*esmodel_asset.RuleInfo)
		}
		return nil
	})

	// 产品字段处理
	_ = frame.RegisterFieldHandlerForAsset[[]*esmodel_asset.RuleInfo]("product", false, func(dataSource *esmodel_asset.ProcessAssets) ([]*esmodel_asset.RuleInfo, bool) {
		if len(dataSource.RuleInfos) == 0 {
			return nil, false
		}
		return dataSource.RuleInfos, true
	}, func(asset *esmodel_asset.Assets, result [][]*esmodel_asset.RuleInfo) error {
		allProduct := make(map[string]*esmodel_asset.RuleInfo, 0)
		for _, ruleInfo := range result {
			for _, v := range ruleInfo {
				allProduct[v.Product] = v
			}
		}
		asset.RuleInfos = make([]*esmodel_asset.RuleInfo, 0)
		for _, v := range allProduct {
			asset.RuleInfos = append(asset.RuleInfos, v)
		}
		return nil
	})
}

func anyToRuleInfo(data any) []*esmodel_asset.RuleInfo {
	ruleInfo := make([]*esmodel_asset.RuleInfo, 0)
	if data == nil {
		return ruleInfo
	}
	ruleInfo = data.([]*esmodel_asset.RuleInfo)
	return ruleInfo
}
