package asset_field_handler

import (
	"context"
	"fmt"
	frame "fobrain/mergeFrame"
	"fobrain/models/elastic/assets"
	"fobrain/models/elastic/business_system"
	"fobrain/pkg/utils"
)

func init() {
	// 业务系统来源处理逻辑
	frame.RegisterFieldHandlerForAsset[*frame.Business]("business_source", true, func(data *assets.ProcessAssets) (*frame.Business, bool) {
		if data.BusinessSystem == "" && data.BusinessOwner == "" {
			return &frame.Business{
				Business: &assets.Business{
					System: data.BusinessSystem,
					Owner:  data.BusinessOwner,
					Source: "original",
				},
				Source: data.Source,
				Node:   data.Node,
			}, true
		}
		var d *assets.Business
		if data.BusinessSystem != "" {
			business, _ := business_system.NewBusinessSystems().GetBusinessByName(context.Background(), data.BusinessSystem)
			if business != nil {
				business.Source = "original"
				d = business
			} else {
				d = &assets.Business{System: data.BusinessSystem, Owner: data.BusinessOwner, Source: "original"}
			}
		} else {
			d = &assets.Business{System: data.BusinessSystem, Owner: data.BusinessOwner, Source: "original"}
		}
		return &frame.Business{
			Business: d,
			Source:   data.Source,
			Node:     data.Node,
		}, true
	}, func(asset *assets.Assets, result []*frame.Business) error {
		for _, business := range result {
			sourceKey := fmt.Sprintf("%d-%d", business.Source, business.Node)
			if _, ok := asset.BusinessSource[sourceKey]; !ok {
				asset.BusinessSource[sourceKey] = make([]*assets.Business, 0)
			}
			asset.BusinessSource[sourceKey] = append(asset.BusinessSource[sourceKey], business.Business)
		}
		return nil
	})

	// 业务系统处理逻辑
	frame.RegisterFieldHandlerForAsset[[]*assets.BusinessInfo]("business", false, func(dataSource *assets.ProcessAssets) ([]*assets.BusinessInfo, bool) {
		// 优先判断BusinessInfo
		if len(dataSource.BusinessInfo) > 0 {
			return dataSource.BusinessInfo, true
		}

		// 采信判断逻辑
		if dataSource.BusinessSystem == "" && dataSource.BusinessOwner == "" {
			return nil, false
		}
		return []*assets.BusinessInfo{
			{
				BusinessSystem: dataSource.BusinessSystem,
				BusinessOwner:  dataSource.BusinessOwner,
			},
		}, true
	}, func(asset *assets.Assets, result [][]*assets.BusinessInfo) error {
		asset.Business = make([]*assets.Business, 0)
		asset.BusinessDepartment = make([]*assets.DepartmentBase, 0)
		businessInfo := make([]*assets.BusinessInfo, 0)
		for _, info := range result {
			businessInfo = append(businessInfo, info...)
		}
		// 业务系统列表，后续批量查询
		businessSystemList := make([]string, 0)
		// 循环收集到的业务系统
		for _, info := range businessInfo {
			var d *assets.Business
			// 如果业务系统不为空，则添加到业务系统列表
			if info.BusinessSystem != "" {
				businessSystemList = append(businessSystemList, info.BusinessSystem)
			} else {
				// 如果业务系统为空，则创建一个业务系统对象
				d = &assets.Business{System: info.BusinessSystem, Owner: info.BusinessOwner, Source: "original"}
				asset.Business = append(asset.Business, d)
			}
		}
		// 去重
		businessSystemList = utils.ListDistinctNonZero(businessSystemList)
		if len(businessSystemList) > 0 {
			// 批量查询业务系统
			businessSystems, err := business_system.NewBusinessSystems().GetByNames(context.Background(), businessSystemList)
			if err != nil {
				return err
			}
			// 循环查询到的业务系统，转换格式后添加到资产对象中
			for _, businessSystem := range businessSystems {
				asset.Business = append(asset.Business, &assets.Business{
					SystemId:     businessSystem.Id,
					System:       businessSystem.BusinessName,
					BusinessInfo: businessSystem,
					Source:       "original",
					PersonBase: func() []*assets.PersonBase {
						if businessSystem.PersonBase == nil {
							return make([]*assets.PersonBase, 0)
						}
						for _, base := range businessSystem.PersonBase {
							if base.FindInfo == nil {
								base.FindInfo = make([]*assets.PersonFindInfo, 0)
							}
							if base.Department == nil {
								base.Department = make([]*assets.DepartmentBase, 0)
							}
						}
						return businessSystem.PersonBase
					}(),
					DepartmentBase: func() []*assets.DepartmentBase {
						if businessSystem.DepartmentBase == nil {
							return make([]*assets.DepartmentBase, 0)
						}
						return businessSystem.DepartmentBase
					}(),
					BusinessTrustedState: businessSystem.Status,
				})
				if businessSystem.DepartmentBase != nil {
					asset.BusinessDepartment = append(asset.BusinessDepartment, businessSystem.DepartmentBase...)
				}
			}
		}
		return nil
	})

}
