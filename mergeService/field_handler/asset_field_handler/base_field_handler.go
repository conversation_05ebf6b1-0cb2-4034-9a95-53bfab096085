package asset_field_handler

import (
	"fmt"
	"fobrain/fobrain/common/localtime"
	frame "fobrain/mergeFrame"
	"fobrain/models/elastic/assets"
	"sort"
	"strings"
)

func init() {
	_ = frame.RegisterFieldHandlerForAsset[string]("IpSegment", false, func(dataSource *assets.ProcessAssets) (string, bool) {
		return frame.StrFieldCheck(dataSource.IpSegment)
	}, func(asset *assets.Assets, result []string) error {
		return frame.StrSliceFieldResultHandler[string](&asset.IpSegment, result)
	})

	_ = frame.RegisterFieldHandlerForAsset[string]("HostName", false, func(dataSource *assets.ProcessAssets) (string, bool) {
		return frame.StrFieldCheck(dataSource.HostName)
	}, func(asset *assets.Assets, result []string) error {
		return frame.StrSliceFieldResultHandler[string](&asset.HostName, result)
	})

	_ = frame.RegisterFieldHandlerForAsset[string]("EthName", false, func(dataSource *assets.ProcessAssets) (string, bool) {
		return frame.StrFieldCheck(dataSource.EthName)
	}, func(asset *assets.Assets, result []string) error {
		return frame.StrSliceFieldResultHandler[string](&asset.EthName, result)
	})

	_ = frame.RegisterFieldHandlerForAsset[string]("Os", false, func(dataSource *assets.ProcessAssets) (string, bool) {
		return frame.StrFieldCheck(dataSource.Os)
	}, func(asset *assets.Assets, result []string) error {
		return frame.StrSliceFieldResultHandler[string](&asset.Os, result)
	})

	_ = frame.RegisterFieldHandlerForAsset[string]("Kernel", false, func(dataSource *assets.ProcessAssets) (string, bool) {
		return frame.StrFieldCheck(dataSource.Kernel)
	}, func(asset *assets.Assets, result []string) error {
		return frame.StrSliceFieldResultHandler[string](&asset.Kernel, result)
	})

	_ = frame.RegisterFieldHandlerForAsset[string]("Model", false, func(dataSource *assets.ProcessAssets) (string, bool) {
		return frame.StrFieldCheck(dataSource.Model)
	}, func(asset *assets.Assets, result []string) error {
		return frame.StrSliceFieldResultHandler[string](&asset.Model, result)
	})

	_ = frame.RegisterFieldHandlerForAsset[string]("Maker", false, func(dataSource *assets.ProcessAssets) (string, bool) {
		return frame.StrFieldCheck(dataSource.Maker)
	}, func(asset *assets.Assets, result []string) error {
		return frame.StrSliceFieldResultHandler[string](&asset.Maker, result)
	})

	_ = frame.RegisterFieldHandlerForAsset[string]("Sn", false, func(dataSource *assets.ProcessAssets) (string, bool) {
		return frame.StrFieldCheck(dataSource.Sn)
	}, func(asset *assets.Assets, result []string) error {
		return frame.StrSliceFieldResultHandler[string](&asset.Sn, result)
	})

	_ = frame.RegisterFieldHandlerForAsset[string]("Mac", false, func(dataSource *assets.ProcessAssets) (string, bool) {
		return frame.StrFieldCheck(dataSource.Mac)
	}, func(asset *assets.Assets, result []string) error {
		return frame.StrSliceFieldResultHandler[string](&asset.Mac, result)
	})

	_ = frame.RegisterFieldHandlerForAsset[*assets.PersonWithMapping]("Oper", false, func(dataSource *assets.ProcessAssets) (*assets.PersonWithMapping, bool) {
		if dataSource.Oper == "" {
			return nil, false
		}
		return &assets.PersonWithMapping{
			SourceId:     dataSource.Source,
			NodeId:       dataSource.Node,
			SourceValue:  dataSource.Oper,
			MappingField: dataSource.PersonField,
		}, true
	}, func(asset *assets.Assets, result []*assets.PersonWithMapping) error {
		for _, item := range result {
			if item == nil {
				continue
			}
			// 把原始数据按照逗号分隔
			sourceValues := strings.Split(item.SourceValue, ",")
			for _, sourceValue := range sourceValues {
				personWithMapping := &assets.PersonWithMapping{
					SourceId:     item.SourceId,
					NodeId:       item.NodeId,
					SourceValue:  sourceValue,
					MappingField: item.MappingField,
				}
				asset.OperWithMapping = append(asset.OperWithMapping, personWithMapping)
			}
		}
		return nil
	})

	_ = frame.RegisterFieldHandlerForAsset[string]("MachineRoom", false, func(dataSource *assets.ProcessAssets) (string, bool) {
		return frame.StrFieldCheck(dataSource.MachineRoom)
	}, func(asset *assets.Assets, result []string) error {
		return frame.StrSliceFieldResultHandler[string](&asset.MachineRoom, result)
	})

	_ = frame.RegisterFieldHandlerForAsset[string]("MemorySize", false, func(dataSource *assets.ProcessAssets) (string, bool) {
		return frame.StrFieldCheck(dataSource.MemorySize)
	}, func(asset *assets.Assets, result []string) error {
		return frame.StrSliceFieldResultHandler[string](&asset.MemorySize, result)
	})

	_ = frame.RegisterFieldHandlerForAsset[string]("MemoryUsageRate", false, func(dataSource *assets.ProcessAssets) (string, bool) {
		return frame.StrFieldCheck(dataSource.MemoryUsageRate)
	}, func(asset *assets.Assets, result []string) error {
		return frame.StrSliceFieldResultHandler[string](&asset.MemoryUsageRate, result)
	})

	_ = frame.RegisterFieldHandlerForAsset[string]("CpuMaker", false, func(dataSource *assets.ProcessAssets) (string, bool) {
		return frame.StrFieldCheck(dataSource.CpuMaker)
	}, func(asset *assets.Assets, result []string) error {
		return frame.StrSliceFieldResultHandler[string](&asset.CpuMaker, result)
	})

	_ = frame.RegisterFieldHandlerForAsset[string]("CpuBrand", false, func(dataSource *assets.ProcessAssets) (string, bool) {
		return frame.StrFieldCheck(dataSource.CpuBrand)
	}, func(asset *assets.Assets, result []string) error {
		return frame.StrSliceFieldResultHandler[string](&asset.CpuBrand, result)
	})

	_ = frame.RegisterFieldHandlerForAsset[string]("DiskUsageRate", false, func(dataSource *assets.ProcessAssets) (string, bool) {
		return frame.StrFieldCheck(dataSource.DiskUsageRate)
	}, func(asset *assets.Assets, result []string) error {
		return frame.StrSliceFieldResultHandler[string](&asset.DiskUsageRate, result)
	})

	_ = frame.RegisterFieldHandlerForAsset[string]("LoadAverage", false, func(dataSource *assets.ProcessAssets) (string, bool) {
		return frame.StrFieldCheck(dataSource.LoadAverage)
	}, func(asset *assets.Assets, result []string) error {
		return frame.StrSliceFieldResultHandler[string](&asset.LoadAverage, result)
	})

	_ = frame.RegisterFieldHandlerForAsset[int]("CpuCount", false, func(dataSource *assets.ProcessAssets) (int, bool) {
		return frame.IntFieldCheck(dataSource.CpuCount)
	}, func(asset *assets.Assets, result []int) error {
		return frame.IntSliceFieldResultHandler[int](&asset.CpuCount, result)
	})

	_ = frame.RegisterFieldHandlerForAsset[int]("DiskCount", false, func(dataSource *assets.ProcessAssets) (int, bool) {
		return frame.IntFieldCheck(dataSource.DiskCount)
	}, func(asset *assets.Assets, result []int) error {
		return frame.IntSliceFieldResultHandler[int](&asset.DiskCount, result)
	})

	_ = frame.RegisterFieldHandlerForAsset[int]("DiskSize", false, func(dataSource *assets.ProcessAssets) (int, bool) {
		return frame.IntFieldCheck(dataSource.DiskSize)
	}, func(asset *assets.Assets, result []int) error {
		return frame.IntSliceFieldResultHandler[int](&asset.DiskSize, result)
	})

	// ruleinfo 字段处理替换 product 字段处理
	// _ = frame.RegisterFieldHandlerForAsset("Product", false, func(dataSource *assets.ProcessAssets) (any, bool) {
	// 	if len(dataSource.Product) == 0 {
	// 		return nil, false
	// 	}
	// 	return dataSource.Product, true
	// }, func(asset *assets.Assets, result []any) error {
	// 	for _, v := range result {
	// 		// 将any转换为[]string
	// 		asset.Product = append(asset.Product, merge_helper.AnyToStringSlice(v)...)
	// 	}
	// 	asset.Product = utils.ListDistinctNonZero(asset.Product)
	// 	return nil
	// })

	_ = frame.RegisterFieldHandlerForAsset[*localtime.Time]("DataSourceResponseAt", true, func(dataSource *assets.ProcessAssets) (*localtime.Time, bool) {
		return dataSource.CreatedAt, true
	}, func(asset *assets.Assets, result []*localtime.Time) error {
		for _, item := range result {
			// 如果资产的DataSourceResponseAt为空，或者item的时间大于资产的DataSourceResponseAt的时间，则更新资产的DataSourceResponseAt
			if asset.DataSourceResponseAt == nil || item.Time().After(asset.DataSourceResponseAt.Time()) {
				asset.DataSourceResponseAt = item
			}
		}
		return nil
	})

	_ = frame.RegisterFieldHandlerForAsset[[]*assets.JarPackageInfo]("JarPackageInfo", false, func(dataSource *assets.ProcessAssets) ([]*assets.JarPackageInfo, bool) {
		return dataSource.JarPackageInfo, true
	}, func(asset *assets.Assets, result [][]*assets.JarPackageInfo) error {
		for _, item := range result {
			if len(item) > 0 {
				for _, jarPackageInfo := range item {
					// 获取数据源的jar包hash
					itemHashStr := getJarPackageInfoHash(jarPackageInfo.JarPackageInfo)
					// 遍历资产当前的jar包信息
					if len(asset.JarPackageInfo) == 0 {
						asset.JarPackageInfo = make([]*assets.JarPackageInfo, 0)
						asset.JarPackageInfo = append(asset.JarPackageInfo, jarPackageInfo)
					} else {
						for _, kv := range asset.JarPackageInfo {
							// 如果资产当前的jar包信息中已经存在该jar包，则跳过
							if getJarPackageInfoHash(kv.JarPackageInfo) == itemHashStr {
								continue
							}
							// 如果资产当前的jar包信息中不存在该jar包，则添加到资产当前的jar包信息中
							asset.JarPackageInfo = append(asset.JarPackageInfo, jarPackageInfo)
						}
					}
				}
			}
		}
		return nil
	})
}

func getJarPackageInfoHash(item []*assets.KV) string {
	itemHash := make([]string, 0)
	for _, jarPackageInfo := range item {
		itemHash = append(itemHash, fmt.Sprintf("%s:%s", jarPackageInfo.Key, jarPackageInfo.Value))
	}
	sort.Slice(itemHash, func(i, j int) bool {
		return itemHash[i] < itemHash[j]
	})
	return strings.Join(itemHash, ",")
}
