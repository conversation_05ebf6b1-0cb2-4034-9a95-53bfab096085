package person_field_handler

import (
	frame "fobrain/mergeFrame"
	"fobrain/models/elastic/staff"
	"maps"
)

func init() {
	frame.RegisterFieldHandlerForPerson[map[string]string]("custom_fields", true, func(dataSource *staff.ProcessStaff) (map[string]string, bool) {
		if len(dataSource.CustomFields) > 0 {
			return dataSource.CustomFields, true
		}
		return nil, false
	}, func(person *staff.Staff, result []map[string]string) error {
		person.CustomFields = make(map[string]string)
		for _, v := range result {
			// 将any转换为map[string]string
			maps.Copy(person.CustomFields, v)
		}
		return nil
	})
}
