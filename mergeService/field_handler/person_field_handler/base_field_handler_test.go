package person_field_handler

import (
	"testing"

	frame "fobrain/mergeFrame"
	"fobrain/models/elastic/staff"

	"github.com/stretchr/testify/assert"
)

func TestBaseFieldHandler_EnglishName(t *testing.T) {
	// 测试 EnglishName 字段处理器
	dataSource := &staff.ProcessStaff{
		EnglishName: "John Doe",
	}
	handler, ok := frame.GetFieldHandlerForPerson[string]("englishname")
	assert.True(t, ok)
	assert.NotNil(t, handler)

	sourcePriority := []uint64{1}
	sourceList := map[uint64][]string{
		1: {"test"},
	}
	value, err := handler.(*frame.GenericFieldHandlerForPerson[string]).Getter(sourcePriority, sourceList, map[string]*staff.ProcessStaff{"test": dataSource})
	assert.NoError(t, err)
	assert.NotEmpty(t, value.Values)
	assert.Equal(t, "John Doe", value.Values[0])
}

func TestBaseFieldHandler_Title(t *testing.T) {
	// 测试 Title 字段处理器
	dataSource := &staff.ProcessStaff{
		Title: "Software Engineer",
	}
	handler, ok := frame.GetFieldHandlerForPerson[string]("title")
	assert.True(t, ok)
	assert.NotNil(t, handler)

	sourcePriority := []uint64{1}
	sourceList := map[uint64][]string{
		1: {"test"},
	}
	value, err := handler.(*frame.GenericFieldHandlerForPerson[string]).Getter(sourcePriority, sourceList, map[string]*staff.ProcessStaff{"test": dataSource})
	assert.NoError(t, err)
	assert.NotEmpty(t, value.Values)
	assert.Equal(t, "Software Engineer", value.Values[0])
}

func TestBaseFieldHandler_Email(t *testing.T) {
	// 测试 Email 字段处理器
	dataSource := &staff.ProcessStaff{
		Email: "<EMAIL>",
	}
	handler, ok := frame.GetFieldHandlerForPerson[string]("email")
	assert.True(t, ok)
	assert.NotNil(t, handler)

	sourcePriority := []uint64{1}
	sourceList := map[uint64][]string{
		1: {"test"},
	}
	value, err := handler.(*frame.GenericFieldHandlerForPerson[string]).Getter(sourcePriority, sourceList, map[string]*staff.ProcessStaff{"test": dataSource})
	assert.NoError(t, err)
	assert.NotEmpty(t, value.Values)
	assert.Equal(t, "<EMAIL>", value.Values[0])
}

func TestBaseFieldHandler_Department(t *testing.T) {
	// 测试 Department 字段处理器
	dataSource := &staff.ProcessStaff{
		Department: "Engineering",
	}
	handler, ok := frame.GetFieldHandlerForPerson[string]("department")
	assert.True(t, ok)
	assert.NotNil(t, handler)

	sourcePriority := []uint64{1}
	sourceList := map[uint64][]string{
		1: {"test"},
	}
	value, err := handler.(*frame.GenericFieldHandlerForPerson[string]).Getter(sourcePriority, sourceList, map[string]*staff.ProcessStaff{"test": dataSource})
	assert.NoError(t, err)
	assert.NotEmpty(t, value.Values)
	assert.Equal(t, "Engineering", value.Values[0])
}

func TestBaseFieldHandler_WorkNumber(t *testing.T) {
	// 测试 WorkNumber 字段处理器
	dataSource := &staff.ProcessStaff{
		WorkNumber: "EMP001",
	}
	handler, ok := frame.GetFieldHandlerForPerson[string]("worknumber")
	assert.True(t, ok)
	assert.NotNil(t, handler)

	sourcePriority := []uint64{1}
	sourceList := map[uint64][]string{
		1: {"test"},
	}
	value, err := handler.(*frame.GenericFieldHandlerForPerson[string]).Getter(sourcePriority, sourceList, map[string]*staff.ProcessStaff{"test": dataSource})
	assert.NoError(t, err)
	assert.NotEmpty(t, value.Values)
	assert.Equal(t, "EMP001", value.Values[0])
}

func TestBaseFieldHandler_Area(t *testing.T) {
	// 测试 Area 字段处理器
	dataSource := &staff.ProcessStaff{
		Area: 1,
	}
	handler, ok := frame.GetFieldHandlerForPerson[int]("area")
	assert.True(t, ok)
	assert.NotNil(t, handler)

	sourcePriority := []uint64{1}
	sourceList := map[uint64][]string{
		1: {"test"},
	}
	value, err := handler.(*frame.GenericFieldHandlerForPerson[int]).Getter(sourcePriority, sourceList, map[string]*staff.ProcessStaff{"test": dataSource})
	assert.NoError(t, err)
	assert.NotEmpty(t, value.Values)
	assert.Equal(t, 1, value.Values[0])
}

func TestBaseFieldHandler_Status(t *testing.T) {
	// 测试 Status 字段处理器
	dataSource := &staff.ProcessStaff{
		Status: 1, // 在职
	}
	handler, ok := frame.GetFieldHandlerForPerson[int]("status")
	assert.True(t, ok)
	assert.NotNil(t, handler)

	sourcePriority := []uint64{1}
	sourceList := map[uint64][]string{
		1: {"test"},
	}
	value, err := handler.(*frame.GenericFieldHandlerForPerson[int]).Getter(sourcePriority, sourceList, map[string]*staff.ProcessStaff{"test": dataSource})
	assert.NoError(t, err)
	assert.NotEmpty(t, value.Values)
	assert.Equal(t, 1, value.Values[0])
}
