package person_field_handler

import (
	"fobrain/fobrain/common/localtime"
	frame "fobrain/mergeFrame"
	"fobrain/models/elastic/staff"
	"fobrain/pkg/utils"
)

func init() {
	_ = frame.RegisterFieldHandlerForPerson[string]("englishname", false, func(dataSource *staff.ProcessStaff) (string, bool) {
		if dataSource.EnglishName == "" {
			return "", false
		}
		return dataSource.EnglishName, true
	}, func(person *staff.Staff, result []string) error {
		person.EnglishName = result
		return nil
	})
	_ = frame.RegisterFieldHandlerForPerson[string]("title", false, func(dataSource *staff.ProcessStaff) (string, bool) {
		if dataSource.Title == "" {
			return "", false
		}
		return dataSource.Title, true
	}, func(person *staff.Staff, result []string) error {
		person.Title = result
		return nil
	})
	_ = frame.RegisterFieldHandlerForPerson[string]("email", false, func(dataSource *staff.ProcessStaff) (string, bool) {
		if dataSource.Email == "" {
			return "", false
		}
		return dataSource.Email, true
	}, func(person *staff.Staff, result []string) error {
		person.Email = result
		return nil
	})
	_ = frame.RegisterFieldHandlerForPerson[string]("department", false, func(dataSource *staff.ProcessStaff) (string, bool) {
		if dataSource.Department == "" {
			return "", false
		}
		return dataSource.Department, true
	}, func(person *staff.Staff, result []string) error {
		person.Department = utils.ListDistinct(result)
		return nil
	})
	_ = frame.RegisterFieldHandlerForPerson("worknumber", false, func(dataSource *staff.ProcessStaff) (string, bool) {
		if dataSource.WorkNumber == "" {
			return "", false
		}
		return dataSource.WorkNumber, true
	}, func(person *staff.Staff, result []string) error {
		if len(result) == 0 {
			return nil
		}
		return frame.StrFieldResultHandler(&person.WorkNumber, result)
	})
	_ = frame.RegisterFieldHandlerForPerson[int]("area", false, func(dataSource *staff.ProcessStaff) (int, bool) {
		return dataSource.Area, true
	}, func(person *staff.Staff, result []int) error {
		return frame.IntFieldResultHandler(&person.Area, result)
	})
	_ = frame.RegisterFieldHandlerForPerson[int]("status", false, func(dataSource *staff.ProcessStaff) (int, bool) {
		return dataSource.Status, true
	}, func(person *staff.Staff, result []int) error {
		return frame.IntFieldResultHandler(&person.Status, result)
	})
	_ = frame.RegisterFieldHandlerForPerson[*localtime.Time]("DataSourceResponseAt", true, func(dataSource *staff.ProcessStaff) (*localtime.Time, bool) {
		return dataSource.CreatedAt, true
	}, func(person *staff.Staff, result []*localtime.Time) error {
		for _, item := range result {
			// 如果人的DataSourceResponseAt为空，或者item的时间大于人的DataSourceResponseAt的时间，则更新人的DataSourceResponseAt
			if person.DataSourceResponseAt == nil || item.Time().After(person.DataSourceResponseAt.Time()) {
				person.DataSourceResponseAt = item
			}
		}
		return nil
	})
}
