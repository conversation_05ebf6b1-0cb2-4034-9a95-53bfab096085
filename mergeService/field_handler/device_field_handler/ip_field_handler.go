package device_field_handler

import (
	frame "fobrain/mergeFrame"
	"fobrain/models/elastic/device"
)

func init() {
	_ = frame.RegisterFieldHandlerForDevice[[]string]("PrivateIp", true, func(dataSource *device.ProcessDevice) ([]string, bool) {
		if len(dataSource.PrivateIp) > 0 {
			return dataSource.PrivateIp, true
		}
		return nil, false
	}, func(device *device.Device, result [][]string) error {
		return frame.SliceStrSliceFieldResultHandler(&device.PrivateIp, result)
	})
	_ = frame.RegisterFieldHandlerForDevice[[]string]("PublicIp", true, func(dataSource *device.ProcessDevice) ([]string, bool) {
		if len(dataSource.PublicIp) > 0 {
			return dataSource.PublicIp, true
		}
		return nil, false
	}, func(device *device.Device, result [][]string) error {
		return frame.SliceStrSliceFieldResultHandler(&device.PublicIp, result)
	})
}
