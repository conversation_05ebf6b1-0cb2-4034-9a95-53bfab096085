package device_field_handler

import (
	"fmt"
	frame "fobrain/mergeFrame"
	"fobrain/models/elastic/device"
)

type BaseFieldHandlerForDevice struct {
}

func init() {
	err := frame.RegisterFieldHandlerForDevice[int]("Area", true, func(dataSource *device.ProcessDevice) (int, bool) {
		return frame.IntFieldCheck(dataSource.Area)
	}, func(device *device.Device, result []int) error {
		return frame.IntSliceFieldResultHandler[int](&device.Area, result)
	})
	fmt.Println(err)
	_ = frame.RegisterFieldHandlerForDevice[string]("Oper", false, func(dataSource *device.ProcessDevice) (string, bool) {
		return frame.StrFieldCheck(dataSource.Oper)
	}, func(device *device.Device, result []string) error {
		return frame.StrSliceFieldResultHandler[string](&device.Oper, result)
	})
	_ = frame.RegisterFieldHandlerForDevice[string]("Hostname", false, func(dataSource *device.ProcessDevice) (string, bool) {
		return frame.StrFieldCheck(dataSource.HostName)
	}, func(device *device.Device, result []string) error {
		return frame.StrSliceFieldResultHandler[string](&device.HostName, result)
	})
	_ = frame.RegisterFieldHandlerForDevice[string]("Os", false, func(dataSource *device.ProcessDevice) (string, bool) {
		return frame.StrFieldCheck(dataSource.Os)
	}, func(device *device.Device, result []string) error {
		return frame.StrSliceFieldResultHandler[string](&device.Os, result)
	})
	_ = frame.RegisterFieldHandlerForDevice[string]("Kernel", false, func(dataSource *device.ProcessDevice) (string, bool) {
		return frame.StrFieldCheck(dataSource.Kernel)
	}, func(device *device.Device, result []string) error {
		return frame.StrSliceFieldResultHandler[string](&device.Kernel, result)
	})
	_ = frame.RegisterFieldHandlerForDevice[string]("Model", false, func(dataSource *device.ProcessDevice) (string, bool) {
		return frame.StrFieldCheck(dataSource.Model)
	}, func(device *device.Device, result []string) error {
		return frame.StrSliceFieldResultHandler[string](&device.Model, result)
	})
	_ = frame.RegisterFieldHandlerForDevice[string]("Maker", false, func(dataSource *device.ProcessDevice) (string, bool) {
		return frame.StrFieldCheck(dataSource.Maker)
	}, func(device *device.Device, result []string) error {
		return frame.StrSliceFieldResultHandler[string](&device.Maker, result)
	})
	_ = frame.RegisterFieldHandlerForDevice[string]("SN", false, func(dataSource *device.ProcessDevice) (string, bool) {
		return frame.StrFieldCheck(dataSource.Sn)
	}, func(device *device.Device, result []string) error {
		return frame.StrSliceFieldResultHandler[string](&device.Sn, result)
	})
	_ = frame.RegisterFieldHandlerForDevice[string]("Mac", false, func(dataSource *device.ProcessDevice) (string, bool) {
		return frame.StrFieldCheck(dataSource.Mac)
	}, func(device *device.Device, result []string) error {
		return frame.StrSliceFieldResultHandler[string](&device.Mac, result)
	})
	_ = frame.RegisterFieldHandlerForDevice[string]("MachineRoom", false, func(dataSource *device.ProcessDevice) (string, bool) {
		return frame.StrFieldCheck(dataSource.MachineRoom)
	}, func(device *device.Device, result []string) error {
		return frame.StrSliceFieldResultHandler[string](&device.MachineRoom, result)
	})
	_ = frame.RegisterFieldHandlerForDevice[string]("MemorySize", false, func(dataSource *device.ProcessDevice) (string, bool) {
		return frame.StrFieldCheck(dataSource.MemorySize)
	}, func(device *device.Device, result []string) error {
		return frame.StrSliceFieldResultHandler[string](&device.MemorySize, result)
	})
	_ = frame.RegisterFieldHandlerForDevice[string]("MemoryUsageRate", false, func(dataSource *device.ProcessDevice) (string, bool) {
		return frame.StrFieldCheck(dataSource.MemoryUsageRate)
	}, func(device *device.Device, result []string) error {
		return frame.StrSliceFieldResultHandler[string](&device.MemoryUsageRate, result)
	})
	_ = frame.RegisterFieldHandlerForDevice[string]("CpuMaker", false, func(dataSource *device.ProcessDevice) (string, bool) {
		return frame.StrFieldCheck(dataSource.CpuMaker)
	}, func(device *device.Device, result []string) error {
		return frame.StrSliceFieldResultHandler[string](&device.CpuMaker, result)
	})
	_ = frame.RegisterFieldHandlerForDevice[string]("CpuBrand", false, func(dataSource *device.ProcessDevice) (string, bool) {
		return frame.StrFieldCheck(dataSource.CpuBrand)
	}, func(device *device.Device, result []string) error {
		return frame.StrSliceFieldResultHandler[string](&device.CpuBrand, result)
	})
	_ = frame.RegisterFieldHandlerForDevice[int]("CpuCount", false, func(dataSource *device.ProcessDevice) (int, bool) {
		return frame.IntFieldCheck(dataSource.CpuCount)
	}, func(device *device.Device, result []int) error {
		return frame.IntSliceFieldResultHandler(&device.CpuCount, result)
	})
	_ = frame.RegisterFieldHandlerForDevice[int]("DiskCount", false, func(dataSource *device.ProcessDevice) (int, bool) {
		return frame.IntFieldCheck(dataSource.DiskCount)
	}, func(device *device.Device, result []int) error {
		return frame.IntSliceFieldResultHandler(&device.DiskCount, result)
	})
	_ = frame.RegisterFieldHandlerForDevice[int]("DiskSize", false, func(dataSource *device.ProcessDevice) (int, bool) {
		return frame.IntFieldCheck(dataSource.DiskSize)
	}, func(device *device.Device, result []int) error {
		return frame.IntSliceFieldResultHandler(&device.DiskSize, result)
	})
	_ = frame.RegisterFieldHandlerForDevice[string]("DiskUsageRate", false, func(dataSource *device.ProcessDevice) (string, bool) {
		return frame.StrFieldCheck(dataSource.DiskUsageRate)
	}, func(device *device.Device, result []string) error {
		return frame.StrSliceFieldResultHandler[string](&device.DiskUsageRate, result)
	})
	_ = frame.RegisterFieldHandlerForDevice[string]("LoadAverage", false, func(dataSource *device.ProcessDevice) (string, bool) {
		return frame.StrFieldCheck(dataSource.LoadAverage)
	}, func(device *device.Device, result []string) error {
		return frame.StrSliceFieldResultHandler[string](&device.LoadAverage, result)
	})
}
