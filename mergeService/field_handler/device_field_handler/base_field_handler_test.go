package device_field_handler

import (
	"testing"

	frame "fobrain/mergeFrame"
	"fobrain/models/elastic/device"

	"github.com/stretchr/testify/assert"
)

// TestBaseFieldHandlerForDevice 测试设备字段处理器的注册和功能
func TestBaseFieldHandlerForDevice(t *testing.T) {
	// 测试所有注册的字段处理器
	testFields := []string{
		"Area", "CpuCount", "DiskCount", "DiskSize",
	}

	// 验证所有字段都已注册
	for _, fieldName := range testFields {
		handler, exists := frame.GetFieldHandlerForDevice[int](fieldName)
		assert.True(t, exists, "字段处理器 %s 未注册", fieldName)
		assert.NotNil(t, handler, "字段处理器 %s 为空", fieldName)
	}
	// 测试所有注册的字段处理器
	testFields = []string{
		"Oper", "Hostname", "Os", "Kernel", "Model", "Maker",
		"SN", "<PERSON>", "MachineRoom", "MemorySize", "MemoryUsageRate",
		"CpuMaker", "CpuBrand",
		"DiskUsageRate", "LoadAverage",
	}

	// 验证所有字段都已注册
	for _, fieldName := range testFields {
		handler, exists := frame.GetFieldHandlerForDevice[string](fieldName)
		assert.True(t, exists, "字段处理器 %s 未注册", fieldName)
		assert.NotNil(t, handler, "字段处理器 %s 为空", fieldName)
	}
}

// TestAreaFieldHandler 测试Area字段处理器
func TestAreaFieldHandler(t *testing.T) {
	// 获取字段处理器
	handler, exists := frame.GetFieldHandlerForDevice[int]("Area")
	assert.True(t, exists)
	assert.NotNil(t, handler)
	assert.True(t, handler.GetAllData(), "Area字段应该获取所有数据")

	// 测试数据
	testCases := []struct {
		name     string
		input    *device.ProcessDevice
		expected any
		isValid  bool
	}{
		{
			name:     "有效值",
			input:    &device.ProcessDevice{Area: 123},
			expected: 123,
			isValid:  true,
		},
		{
			name:     "零值",
			input:    &device.ProcessDevice{Area: 0},
			expected: 0,
			isValid:  false,
		},
	}

	// 模拟数据
	allData := make(map[string]*device.ProcessDevice)
	for i, tc := range testCases {
		allData[string(rune('A'+i))] = tc.input
	}

	// 测试Getter方法
	result, err := handler.Getter(nil, nil, allData)
	assert.NoError(t, err)
	assert.NotNil(t, result)

	// 验证结果中只包含有效值
	validValues := 0
	for _, tc := range testCases {
		if tc.isValid {
			validValues++
			assert.Contains(t, result.Values, tc.expected)
		}
	}
	assert.Len(t, result.Values, validValues)

	// 测试ResultHandler方法
	testDevice := &device.Device{}
	err = handler.ResultHandler(testDevice, []int{123, 456})
	assert.NoError(t, err)
	assert.Equal(t, []int{123, 456}, testDevice.Area)
}

// TestStrFieldHandlers 测试字符串类型字段处理器
func TestStrFieldHandlers(t *testing.T) {
	// 测试所有字符串类型的字段
	strFields := []struct {
		fieldName           string
		setter              func(d *device.Device) *[]string
		processDeviceSetter func(d *device.ProcessDevice, value string)
	}{
		{"Oper", func(d *device.Device) *[]string { return &d.Oper }, func(d *device.ProcessDevice, v string) { d.Oper = v }},
		{"Hostname", func(d *device.Device) *[]string { return &d.HostName }, func(d *device.ProcessDevice, v string) { d.HostName = v }},
		{"Os", func(d *device.Device) *[]string { return &d.Os }, func(d *device.ProcessDevice, v string) { d.Os = v }},
		{"Kernel", func(d *device.Device) *[]string { return &d.Kernel }, func(d *device.ProcessDevice, v string) { d.Kernel = v }},
		{"Model", func(d *device.Device) *[]string { return &d.Model }, func(d *device.ProcessDevice, v string) { d.Model = v }},
		{"Maker", func(d *device.Device) *[]string { return &d.Maker }, func(d *device.ProcessDevice, v string) { d.Maker = v }},
		{"SN", func(d *device.Device) *[]string { return &d.Sn }, func(d *device.ProcessDevice, v string) { d.Sn = v }},
		{"Mac", func(d *device.Device) *[]string { return &d.Mac }, func(d *device.ProcessDevice, v string) { d.Mac = v }},
		{"MachineRoom", func(d *device.Device) *[]string { return &d.MachineRoom }, func(d *device.ProcessDevice, v string) { d.MachineRoom = v }},
		{"MemorySize", func(d *device.Device) *[]string { return &d.MemorySize }, func(d *device.ProcessDevice, v string) { d.MemorySize = v }},
		{"MemoryUsageRate", func(d *device.Device) *[]string { return &d.MemoryUsageRate }, func(d *device.ProcessDevice, v string) { d.MemoryUsageRate = v }},
		{"CpuMaker", func(d *device.Device) *[]string { return &d.CpuMaker }, func(d *device.ProcessDevice, v string) { d.CpuMaker = v }},
		{"CpuBrand", func(d *device.Device) *[]string { return &d.CpuBrand }, func(d *device.ProcessDevice, v string) { d.CpuBrand = v }},
		{"DiskUsageRate", func(d *device.Device) *[]string { return &d.DiskUsageRate }, func(d *device.ProcessDevice, v string) { d.DiskUsageRate = v }},
		{"LoadAverage", func(d *device.Device) *[]string { return &d.LoadAverage }, func(d *device.ProcessDevice, v string) { d.LoadAverage = v }},
	}

	for _, field := range strFields {
		t.Run(field.fieldName, func(t *testing.T) {
			// 获取字段处理器
			handler, exists := frame.GetFieldHandlerForDevice[string](field.fieldName)
			assert.True(t, exists)
			assert.NotNil(t, handler)
			assert.False(t, handler.GetAllData(), field.fieldName+"字段不应该获取所有数据")

			// 测试数据
			validValue := "test_" + field.fieldName
			emptyValue := ""

			// 创建测试数据
			validData := &device.ProcessDevice{Source: 1, Node: 1, TaskDataId: "task1", Id: "id1"}
			emptyData := &device.ProcessDevice{Source: 2, Node: 2, TaskDataId: "task2", Id: "id2"}

			// 直接设置字段值
			field.processDeviceSetter(validData, validValue)
			field.processDeviceSetter(emptyData, emptyValue)

			// 模拟数据
			allData := map[string]*device.ProcessDevice{
				"1": validData,
				"2": emptyData,
			}

			// 设置优先级
			sourcePriority := []uint64{1, 2}
			sourceList := map[uint64][]string{
				1: {"1"},
				2: {"2"},
			}

			// 测试Getter方法
			result, err := handler.Getter(sourcePriority, sourceList, allData)
			assert.NoError(t, err)
			assert.NotNil(t, result)
			assert.Contains(t, result.Values, validValue)
			assert.Len(t, result.Values, 1)
			assert.Equal(t, []uint64{1}, result.SourceIds)
			assert.Equal(t, []uint64{1}, result.NodeIds)
			assert.Equal(t, []string{"task1"}, result.TaskDataIds)
			assert.Equal(t, []string{"id1"}, result.ProcessIds)

			// 测试ResultHandler方法
			testDevice := &device.Device{}
			err = handler.ResultHandler(testDevice, []string{validValue, "another_value"})
			assert.NoError(t, err)
			assert.Equal(t, []string{validValue, "another_value"}, *field.setter(testDevice))
		})
	}
}

// TestIntFieldHandlers 测试整数类型字段处理器
func TestIntFieldHandlers(t *testing.T) {
	// 测试所有整数类型的字段
	intFields := []struct {
		fieldName           string
		setter              func(d *device.Device) *[]int
		processDeviceSetter func(d *device.ProcessDevice, value int)
	}{
		{"CpuCount", func(d *device.Device) *[]int { return &d.CpuCount }, func(d *device.ProcessDevice, v int) { d.CpuCount = v }},
		{"DiskCount", func(d *device.Device) *[]int { return &d.DiskCount }, func(d *device.ProcessDevice, v int) { d.DiskCount = v }},
		{"DiskSize", func(d *device.Device) *[]int { return &d.DiskSize }, func(d *device.ProcessDevice, v int) { d.DiskSize = v }},
	}

	for _, field := range intFields {
		t.Run(field.fieldName, func(t *testing.T) {
			// 获取字段处理器
			handler, exists := frame.GetFieldHandlerForDevice[int](field.fieldName)
			assert.True(t, exists)
			assert.NotNil(t, handler)
			assert.False(t, handler.GetAllData(), field.fieldName+"字段不应该获取所有数据")

			// 测试数据
			validValue := 42
			emptyValue := 0

			// 创建测试数据
			validData := &device.ProcessDevice{Source: 1, Node: 1, TaskDataId: "task1", Id: "id1"}
			emptyData := &device.ProcessDevice{Source: 2, Node: 2, TaskDataId: "task2", Id: "id2"}

			// 直接设置字段值
			field.processDeviceSetter(validData, validValue)
			field.processDeviceSetter(emptyData, emptyValue)

			// 模拟数据
			allData := map[string]*device.ProcessDevice{
				"1": validData,
				"2": emptyData,
			}

			// 设置优先级
			sourcePriority := []uint64{1, 2}
			sourceList := map[uint64][]string{
				1: {"1"},
				2: {"2"},
			}

			// 测试Getter方法
			result, err := handler.Getter(sourcePriority, sourceList, allData)
			assert.NoError(t, err)
			assert.NotNil(t, result)
			assert.Contains(t, result.Values, validValue)
			assert.Len(t, result.Values, 1)
			assert.Equal(t, []uint64{1}, result.SourceIds)
			assert.Equal(t, []uint64{1}, result.NodeIds)
			assert.Equal(t, []string{"task1"}, result.TaskDataIds)
			assert.Equal(t, []string{"id1"}, result.ProcessIds)

			// 测试ResultHandler方法
			testDevice := &device.Device{}
			err = handler.ResultHandler(testDevice, []int{validValue, 99})
			assert.NoError(t, err)
			assert.Equal(t, []int{validValue, 99}, *field.setter(testDevice))
		})
	}
}
