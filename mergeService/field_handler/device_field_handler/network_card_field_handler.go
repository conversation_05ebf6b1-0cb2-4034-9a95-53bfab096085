package device_field_handler

import (
	frame "fobrain/mergeFrame"
	"fobrain/models/elastic/device"
)

func init() {
	// 网卡信息，收集所有网卡信息，并去重
	_ = frame.RegisterFieldHandlerForDevice[[]*device.NetworkCardInfo]("NetworkCard", true, func(dataSource *device.ProcessDevice) ([]*device.NetworkCardInfo, bool) {
		if len(dataSource.NetworkCards) > 0 {
			return dataSource.NetworkCards, true
		}
		return nil, false
	}, func(device *device.Device, result [][]*device.NetworkCardInfo) error {
		if len(result) == 0 {
			return nil
		}
		var exists = make(map[string]struct{})
		for _, v := range device.NetworkCards {
			if _, ok := exists[v.Hash()]; !ok {
				exists[v.Hash()] = struct{}{}
			}
		}
		for _, v := range result {
			for _, card := range v {
				if _, ok := exists[card.Hash()]; !ok {
					exists[card.Hash()] = struct{}{}
					device.NetworkCards = append(device.NetworkCards, card)
				}
			}
		}
		return nil
	})
}
