package device_field_handler

import (
	"testing"

	frame "fobrain/mergeFrame"
	"fobrain/models/elastic/device"

	"github.com/stretchr/testify/assert"
)

// TestNetworkCardFieldHandler 测试NetworkCard字段处理器
func TestNetworkCardFieldHandler(t *testing.T) {

	// 获取字段处理器
	handler, exists := frame.GetFieldHandlerForDevice[[]*device.NetworkCardInfo]("NetworkCard")
	assert.True(t, exists, "NetworkCard字段处理器未注册")
	assert.NotNil(t, handler, "NetworkCard字段处理器为空")
	assert.True(t, handler.GetAllData(), "NetworkCard字段应该获取所有数据")

	// 创建测试网卡数据
	networkCard1 := &device.NetworkCardInfo{
		Name:      "eth0",
		Mac:       "5e:94:c2:33:4d:f1",
		Ipv4:      "***********",
		Ipv6:      "38b5:f705:bbed:7297:7da6:a060:345f:4907",
		Gateway:   "*************",
		DnsServer: []string{"*******", "***************"},
		Netmask:   "*************",
		Broadcast: "*************",
		Status:    "1",
		Speed:     1000,
	}

	networkCard2 := &device.NetworkCardInfo{
		Name:      "eth1",
		Mac:       "5e:94:c2:33:4d:f2",
		Ipv4:      "********",
		Ipv6:      "48b5:f705:bbed:7297:7da6:a060:345f:4907",
		Gateway:   "**********",
		DnsServer: []string{"*******", "***************"},
		Netmask:   "*************",
		Broadcast: "**********",
		Status:    "1",
		Speed:     1000,
	}

	// 创建重复网卡（与networkCard1有相同的Hash）
	networkCard1Duplicate := &device.NetworkCardInfo{
		Name:      "eth0",
		Mac:       "5e:94:c2:33:4d:f1",
		Ipv4:      "***********",
		Ipv6:      "38b5:f705:bbed:7297:7da6:a060:345f:4907",
		Gateway:   "*************",
		DnsServer: []string{"*******", "***************"},
		Netmask:   "*************",
		Broadcast: "*************",
		Status:    "1",
		Speed:     1000,
	}

	// 测试数据
	testCases := []struct {
		name     string
		input    *device.ProcessDevice
		expected []*device.NetworkCardInfo
		isValid  bool
	}{
		{
			name:     "有效值-单个网卡",
			input:    &device.ProcessDevice{NetworkCards: []*device.NetworkCardInfo{networkCard1}},
			expected: []*device.NetworkCardInfo{networkCard1},
			isValid:  true,
		},
		{
			name:     "有效值-多个网卡",
			input:    &device.ProcessDevice{NetworkCards: []*device.NetworkCardInfo{networkCard1, networkCard2}},
			expected: []*device.NetworkCardInfo{networkCard1, networkCard2},
			isValid:  true,
		},
		{
			name:     "有效值-包含重复网卡",
			input:    &device.ProcessDevice{NetworkCards: []*device.NetworkCardInfo{networkCard1, networkCard1Duplicate, networkCard2}},
			expected: []*device.NetworkCardInfo{networkCard1, networkCard2},
			isValid:  true,
		},
		{
			name:     "空值",
			input:    &device.ProcessDevice{NetworkCards: []*device.NetworkCardInfo{}},
			expected: []*device.NetworkCardInfo{},
			isValid:  false,
		},
		{
			name:     "nil值",
			input:    &device.ProcessDevice{NetworkCards: nil},
			expected: nil,
			isValid:  false,
		},
	}

	// 模拟数据
	allData := make(map[string]*device.ProcessDevice)
	for i, tc := range testCases {
		key := string(rune('A' + i))
		allData[key] = tc.input
	}

	// 测试Getter方法
	result, err := handler.Getter(nil, nil, allData)
	assert.NoError(t, err)
	assert.NotNil(t, result)

	// 验证结果中只包含有效值
	validValues := 0
	for _, tc := range testCases {
		if tc.isValid {
			validValues++
		}
	}

	// 测试ResultHandler方法
	testDevice := &device.Device{}
	err = handler.ResultHandler(testDevice, [][]*device.NetworkCardInfo{
		[]*device.NetworkCardInfo{networkCard1},
		[]*device.NetworkCardInfo{networkCard2},
	})
	assert.NoError(t, err)
	assert.Len(t, testDevice.NetworkCards, 2)

	// 验证网卡信息
	foundCard1 := false
	foundCard2 := false
	for _, card := range testDevice.NetworkCards {
		if card.Name == "eth0" && card.Ipv4 == "***********" {
			foundCard1 = true
		}
		if card.Name == "eth1" && card.Ipv4 == "********" {
			foundCard2 = true
		}
	}
	assert.True(t, foundCard1, "未找到eth0网卡")
	assert.True(t, foundCard2, "未找到eth1网卡")

	// 测试空结果
	testDevice = &device.Device{}
	err = handler.ResultHandler(testDevice, [][]*device.NetworkCardInfo{})
	assert.NoError(t, err)
	assert.Empty(t, testDevice.NetworkCards)
}

// TestNetworkCardResultHandler 测试网卡结果处理函数
func TestNetworkCardResultHandler(t *testing.T) {
	// 创建测试网卡数据
	networkCard1 := &device.NetworkCardInfo{
		Name:      "eth0",
		Mac:       "5e:94:c2:33:4d:f1",
		Ipv4:      "***********",
		Ipv6:      "38b5:f705:bbed:7297:7da6:a060:345f:4907",
		Gateway:   "*************",
		DnsServer: []string{"*******", "***************"},
		Netmask:   "*************",
		Broadcast: "*************",
		Status:    "1",
		Speed:     1000,
	}

	networkCard2 := &device.NetworkCardInfo{
		Name:      "eth1",
		Mac:       "5e:94:c2:33:4d:f2",
		Ipv4:      "********",
		Ipv6:      "48b5:f705:bbed:7297:7da6:a060:345f:4907",
		Gateway:   "**********",
		DnsServer: []string{"*******", "***************"},
		Netmask:   "*************",
		Broadcast: "**********",
		Status:    "1",
		Speed:     1000,
	}

	// 创建重复网卡（与networkCard1有相同的Hash）
	networkCard1Duplicate := &device.NetworkCardInfo{
		Name:      "eth0",
		Mac:       "5e:94:c2:33:4d:f1",
		Ipv4:      "***********",
		Ipv6:      "38b5:f705:bbed:7297:7da6:a060:345f:4907",
		Gateway:   "*************",
		DnsServer: []string{"*******", "***************"},
		Netmask:   "*************",
		Broadcast: "*************",
		Status:    "1",
		Speed:     1000,
	}

	// 测试用例
	testCases := []struct {
		name     string
		input    [][]*device.NetworkCardInfo
		expected int // 期望的网卡数量
	}{
		{
			name:     "单个网卡",
			input:    [][]*device.NetworkCardInfo{[]*device.NetworkCardInfo{networkCard1}},
			expected: 1,
		},
		{
			name:     "多个不同网卡",
			input:    [][]*device.NetworkCardInfo{[]*device.NetworkCardInfo{networkCard1, networkCard2}},
			expected: 2,
		},
		{
			name:     "包含重复网卡",
			input:    [][]*device.NetworkCardInfo{[]*device.NetworkCardInfo{networkCard1, networkCard1Duplicate, networkCard2}},
			expected: 2,
		},
		{
			name:     "多个来源的网卡",
			input:    [][]*device.NetworkCardInfo{[]*device.NetworkCardInfo{networkCard1}, []*device.NetworkCardInfo{networkCard2}},
			expected: 2,
		},
		{
			name:     "多个来源包含重复网卡",
			input:    [][]*device.NetworkCardInfo{[]*device.NetworkCardInfo{networkCard1}, []*device.NetworkCardInfo{networkCard1Duplicate, networkCard2}},
			expected: 2,
		},
		{
			name:     "空结果",
			input:    [][]*device.NetworkCardInfo{},
			expected: 0,
		},
	}

	// 测试网卡结果处理函数
	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 使用mockNetworkCardResultHandler直接测试
			testDevice := &device.Device{}
			hd, ok := frame.GetFieldHandlerForDevice[[]*device.NetworkCardInfo]("NetworkCard")
			assert.True(t, ok)
			err := hd.ResultHandler(testDevice, tc.input)
			assert.NoError(t, err)
			assert.Len(t, testDevice.NetworkCards, tc.expected)

			// 验证去重逻辑
			if tc.expected > 0 {
				// 检查是否有重复的网卡（根据Hash）
				hashMap := make(map[string]bool)
				for _, card := range testDevice.NetworkCards {
					hash := card.Hash()
					assert.False(t, hashMap[hash], "存在重复的网卡Hash: %s", hash)
					hashMap[hash] = true
				}
			}
		})
	}
}

// TestNetworkCardInfoHash 测试NetworkCardInfo的Hash方法
func TestNetworkCardInfoHash(t *testing.T) {
	// 创建相同内容的网卡
	networkCard1 := &device.NetworkCardInfo{
		Name:      "eth0",
		Mac:       "5e:94:c2:33:4d:f1",
		Ipv4:      "***********",
		Ipv6:      "38b5:f705:bbed:7297:7da6:a060:345f:4907",
		Gateway:   "*************",
		DnsServer: []string{"*******", "***************"},
		Netmask:   "*************",
		Broadcast: "*************",
		Status:    "1",
		Speed:     1000,
	}

	networkCard2 := &device.NetworkCardInfo{
		Name:      "eth0",
		Mac:       "5e:94:c2:33:4d:f1",
		Ipv4:      "***********",
		Ipv6:      "38b5:f705:bbed:7297:7da6:a060:345f:4907",
		Gateway:   "*************",
		DnsServer: []string{"*******", "***************"},
		Netmask:   "*************",
		Broadcast: "*************",
		Status:    "1",
		Speed:     1000,
	}

	// 创建不同内容的网卡
	networkCard3 := &device.NetworkCardInfo{
		Name:      "eth1", // 不同名称
		Mac:       "5e:94:c2:33:4d:f1",
		Ipv4:      "***********",
		Ipv6:      "38b5:f705:bbed:7297:7da6:a060:345f:4907",
		Gateway:   "*************",
		DnsServer: []string{"*******", "***************"},
		Netmask:   "*************",
		Broadcast: "*************",
		Status:    "1",
		Speed:     1000,
	}

	networkCard4 := &device.NetworkCardInfo{
		Name:      "eth0",
		Mac:       "6e:94:c2:33:4d:f1", // 不同MAC
		Ipv4:      "***********",
		Ipv6:      "38b5:f705:bbed:7297:7da6:a060:345f:4907",
		Gateway:   "*************",
		DnsServer: []string{"*******", "***************"},
		Netmask:   "*************",
		Broadcast: "*************",
		Status:    "1",
		Speed:     1000,
	}

	networkCard5 := &device.NetworkCardInfo{
		Name:      "eth0",
		Mac:       "5e:94:c2:33:4d:f1",
		Ipv4:      "***********", // 不同IP
		Ipv6:      "38b5:f705:bbed:7297:7da6:a060:345f:4907",
		Gateway:   "*************",
		DnsServer: []string{"*******", "***************"},
		Netmask:   "*************",
		Broadcast: "*************",
		Status:    "1",
		Speed:     1000,
	}

	// 测试相同内容的网卡应该有相同的Hash
	assert.Equal(t, networkCard1.Hash(), networkCard2.Hash(), "相同内容的网卡应该有相同的Hash")

	// 测试不同内容的网卡应该有不同的Hash
	assert.NotEqual(t, networkCard1.Hash(), networkCard3.Hash(), "不同名称的网卡应该有不同的Hash")
	assert.NotEqual(t, networkCard1.Hash(), networkCard4.Hash(), "不同MAC的网卡应该有不同的Hash")
	assert.NotEqual(t, networkCard1.Hash(), networkCard5.Hash(), "不同IP的网卡应该有不同的Hash")
}
