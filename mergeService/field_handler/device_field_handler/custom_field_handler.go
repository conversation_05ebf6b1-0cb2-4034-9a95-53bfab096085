package device_field_handler

import (
	frame "fobrain/mergeFrame"
	"fobrain/models/elastic/device"
	"maps"
)

func init() {
	frame.RegisterFieldHandlerForDevice[map[string]string]("custom_fields", true, func(dataSource *device.ProcessDevice) (map[string]string, bool) {
		if len(dataSource.CustomFields) > 0 {
			return dataSource.CustomFields, true
		}
		return nil, false
	}, func(device *device.Device, result []map[string]string) error {
		device.CustomFields = make(map[string]string)
		for _, v := range result {
			// 将any转换为map[string]string
			maps.Copy(device.CustomFields, v)
		}
		return nil
	})
}
