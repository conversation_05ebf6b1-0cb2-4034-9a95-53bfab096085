package device_field_handler

import (
	"testing"

	frame "fobrain/mergeFrame"
	"fobrain/models/elastic/device"

	"github.com/stretchr/testify/assert"
)

// TestPrivateIpFieldHandler 测试PrivateIp字段处理器
func TestPrivateIpFieldHandler(t *testing.T) {
	// 获取字段处理器
	handler, exists := frame.GetFieldHandlerForDevice[[]string]("PrivateIp")
	assert.True(t, exists, "PrivateIp字段处理器未注册")
	assert.NotNil(t, handler, "PrivateIp字段处理器为空")
	assert.True(t, handler.GetAllData(), "PrivateIp字段应该获取所有数据")

	// 测试数据
	testCases := []struct {
		name     string
		input    *device.ProcessDevice
		expected []string
		isValid  bool
	}{
		{
			name:     "有效值-单个IP",
			input:    &device.ProcessDevice{PrivateIp: []string{"***********"}},
			expected: []string{"***********"},
			isValid:  true,
		},
		{
			name:     "有效值-多个IP",
			input:    &device.ProcessDevice{PrivateIp: []string{"***********", "***********"}},
			expected: []string{"***********", "***********"},
			isValid:  true,
		},
		{
			name:     "空值",
			input:    &device.ProcessDevice{PrivateIp: []string{}},
			expected: []string{},
			isValid:  false,
		},
		{
			name:     "nil值",
			input:    &device.ProcessDevice{PrivateIp: nil},
			expected: nil,
			isValid:  false,
		},
	}

	// 模拟数据
	allData := make(map[string]*device.ProcessDevice)
	for i, tc := range testCases {
		key := string(rune('A' + i))
		allData[key] = tc.input
	}

	// 测试Getter方法
	result, err := handler.Getter(nil, nil, allData)
	assert.NoError(t, err)
	assert.NotNil(t, result)

	// 验证结果中只包含有效值
	validValues := 0
	for _, tc := range testCases {
		if tc.isValid {
			validValues++
			resultValues := make([]string, 0)
			for _, v := range result.Values {
				resultValues = append(resultValues, v...)
			}
			for _, ip := range tc.expected {
				assert.Contains(t, resultValues, ip)
			}
		}
	}

	// 测试ResultHandler方法
	testDevice := &device.Device{}
	err = handler.ResultHandler(testDevice, [][]string{
		[]string{"***********"},
		[]string{"***********", "***********"},
	})
	assert.NoError(t, err)
	assert.ElementsMatch(t, []string{"***********", "***********", "***********"}, testDevice.PrivateIp)

	// 测试空结果
	testDevice = &device.Device{}
	err = handler.ResultHandler(testDevice, [][]string{})
	assert.NoError(t, err)
	assert.Empty(t, testDevice.PrivateIp)
}

// TestPublicIpFieldHandler 测试PublicIp字段处理器
func TestPublicIpFieldHandler(t *testing.T) {
	// 获取字段处理器
	handler, exists := frame.GetFieldHandlerForDevice[[]string]("PublicIp")
	assert.True(t, exists, "PublicIp字段处理器未注册")
	assert.NotNil(t, handler, "PublicIp字段处理器为空")
	assert.True(t, handler.GetAllData(), "PublicIp字段应该获取所有数据")

	// 测试数据
	testCases := []struct {
		name     string
		input    *device.ProcessDevice
		expected []string
		isValid  bool
	}{
		{
			name:     "有效值-单个IP",
			input:    &device.ProcessDevice{PublicIp: []string{"*******"}},
			expected: []string{"*******"},
			isValid:  true,
		},
		{
			name:     "有效值-多个IP",
			input:    &device.ProcessDevice{PublicIp: []string{"*******", "*******"}},
			expected: []string{"*******", "*******"},
			isValid:  true,
		},
		{
			name:     "空值",
			input:    &device.ProcessDevice{PublicIp: []string{}},
			expected: []string{},
			isValid:  false,
		},
		{
			name:     "nil值",
			input:    &device.ProcessDevice{PublicIp: nil},
			expected: nil,
			isValid:  false,
		},
	}

	// 模拟数据
	allData := make(map[string]*device.ProcessDevice)
	for i, tc := range testCases {
		key := string(rune('A' + i))
		allData[key] = tc.input
	}

	// 测试Getter方法
	result, err := handler.Getter(nil, nil, allData)
	assert.NoError(t, err)
	assert.NotNil(t, result)

	// 验证结果中只包含有效值
	validValues := 0
	for _, tc := range testCases {
		if tc.isValid {
			validValues++
			resultValues := make([]string, 0)
			for _, v := range result.Values {
				resultValues = append(resultValues, v...)
			}
			for _, ip := range tc.expected {
				assert.Contains(t, resultValues, ip)
			}
		}
	}

	// 测试ResultHandler方法
	testDevice := &device.Device{}
	err = handler.ResultHandler(testDevice, [][]string{
		[]string{"*******"},
		[]string{"*******", "***************"},
	})
	assert.NoError(t, err)
	assert.ElementsMatch(t, []string{"*******", "*******", "***************"}, testDevice.PublicIp)

	// 测试空结果
	testDevice = &device.Device{}
	err = handler.ResultHandler(testDevice, [][]string{})
	assert.NoError(t, err)
	assert.Empty(t, testDevice.PublicIp)
}

// TestStrSliceFieldResultHandler 测试StrSliceFieldResultHandler函数
func TestStrSliceFieldResultHandler(t *testing.T) {
	// 使用gomonkey模拟ListDistinctNonZero函数
	//patches := gomonkey.ApplyFunc(frame.SliceStrSliceFieldResultHandler, func(field *[]string, result [][]string) error {
	//	if len(result) == 0 {
	//		*field = []string{}
	//		return nil
	//	}
	//	if len(*field) == 0 {
	//		*field = []string{}
	//	}
	//	for _, v := range result {
	//		*field = append(*field, v...)
	//	}
	//	// 模拟去重操作
	//	seen := make(map[string]struct{})
	//	unique := make([]string, 0, len(*field))
	//	for _, item := range *field {
	//		if item == "" {
	//			continue
	//		}
	//		if _, ok := seen[item]; !ok {
	//			seen[item] = struct{}{}
	//			unique = append(unique, item)
	//		}
	//	}
	//	*field = unique
	//	return nil
	//})
	//defer patches.Reset()

	// 测试PrivateIp字段处理器
	testPrivateIp := []string{}
	err := frame.SliceStrSliceFieldResultHandler(&testPrivateIp, [][]string{
		[]string{"***********"},
		[]string{"***********", "***********"},
		[]string{"***********"},
	})
	assert.NoError(t, err)
	assert.ElementsMatch(t, []string{"***********", "***********", "***********", "***********"}, testPrivateIp)

	// 测试PublicIp字段处理器
	testPublicIp := []string{}
	err = frame.SliceStrSliceFieldResultHandler(&testPublicIp, [][]string{
		[]string{"*******"},
		[]string{"*******", "***************"},
		[]string{"*********"},
	})
	assert.NoError(t, err)
	assert.ElementsMatch(t, []string{"*******", "*******", "***************", "*********"}, testPublicIp)

	// 测试空结果
	testEmptyIp := []string{}
	err = frame.SliceStrSliceFieldResultHandler(&testEmptyIp, [][]string{})
	assert.NoError(t, err)
	assert.Empty(t, testEmptyIp)

	// 测试已有初始值
	testExistingIp := []string{"***********"}
	err = frame.SliceStrSliceFieldResultHandler(&testExistingIp, [][]string{
		[]string{"***********"},
	})
	assert.NoError(t, err)
	assert.ElementsMatch(t, []string{"***********", "***********"}, testExistingIp)

	// 测试重复值
	testDuplicateIp := []string{}
	err = frame.SliceStrSliceFieldResultHandler(&testDuplicateIp, [][]string{
		[]string{"***********", "***********"},
		[]string{"***********", "***********"},
	})
	assert.NoError(t, err)
	assert.ElementsMatch(t, []string{"***********", "***********"}, testDuplicateIp)
}
