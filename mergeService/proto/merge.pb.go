// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v5.29.3
// source: merge.proto

package merge

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Empty struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Empty) Reset() {
	*x = Empty{}
	if protoimpl.UnsafeEnabled {
		mi := &file_merge_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Empty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Empty) ProtoMessage() {}

func (x *Empty) ProtoReflect() protoreflect.Message {
	mi := &file_merge_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Empty.ProtoReflect.Descriptor instead.
func (*Empty) Descriptor() ([]byte, []int) {
	return file_merge_proto_rawDescGZIP(), []int{0}
}

type Source struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id   uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	Icon string `protobuf:"bytes,3,opt,name=icon,proto3" json:"icon"`
}

func (x *Source) Reset() {
	*x = Source{}
	if protoimpl.UnsafeEnabled {
		mi := &file_merge_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Source) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Source) ProtoMessage() {}

func (x *Source) ProtoReflect() protoreflect.Message {
	mi := &file_merge_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Source.ProtoReflect.Descriptor instead.
func (*Source) Descriptor() ([]byte, []int) {
	return file_merge_proto_rawDescGZIP(), []int{1}
}

func (x *Source) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Source) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Source) GetIcon() string {
	if x != nil {
		return x.Icon
	}
	return ""
}

type RuleInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Product   string `protobuf:"bytes,1,opt,name=product,proto3" json:"product"`
	FirstTag  string `protobuf:"bytes,2,opt,name=first_tag,json=firstTag,proto3" json:"first_tag"`
	SecondTag string `protobuf:"bytes,3,opt,name=second_tag,json=secondTag,proto3" json:"second_tag"`
	Level     string `protobuf:"bytes,4,opt,name=level,proto3" json:"level"`
}

func (x *RuleInfo) Reset() {
	*x = RuleInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_merge_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RuleInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RuleInfo) ProtoMessage() {}

func (x *RuleInfo) ProtoReflect() protoreflect.Message {
	mi := &file_merge_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RuleInfo.ProtoReflect.Descriptor instead.
func (*RuleInfo) Descriptor() ([]byte, []int) {
	return file_merge_proto_rawDescGZIP(), []int{2}
}

func (x *RuleInfo) GetProduct() string {
	if x != nil {
		return x.Product
	}
	return ""
}

func (x *RuleInfo) GetFirstTag() string {
	if x != nil {
		return x.FirstTag
	}
	return ""
}

func (x *RuleInfo) GetSecondTag() string {
	if x != nil {
		return x.SecondTag
	}
	return ""
}

func (x *RuleInfo) GetLevel() string {
	if x != nil {
		return x.Level
	}
	return ""
}

type Area struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id   uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
}

func (x *Area) Reset() {
	*x = Area{}
	if protoimpl.UnsafeEnabled {
		mi := &file_merge_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Area) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Area) ProtoMessage() {}

func (x *Area) ProtoReflect() protoreflect.Message {
	mi := &file_merge_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Area.ProtoReflect.Descriptor instead.
func (*Area) Descriptor() ([]byte, []int) {
	return file_merge_proto_rawDescGZIP(), []int{3}
}

func (x *Area) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Area) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type TestMethodRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name"`
}

func (x *TestMethodRequest) Reset() {
	*x = TestMethodRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_merge_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TestMethodRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestMethodRequest) ProtoMessage() {}

func (x *TestMethodRequest) ProtoReflect() protoreflect.Message {
	mi := &file_merge_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestMethodRequest.ProtoReflect.Descriptor instead.
func (*TestMethodRequest) Descriptor() ([]byte, []int) {
	return file_merge_proto_rawDescGZIP(), []int{4}
}

func (x *TestMethodRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type TestMethodResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Welcome string `protobuf:"bytes,1,opt,name=welcome,proto3" json:"welcome"`
}

func (x *TestMethodResponse) Reset() {
	*x = TestMethodResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_merge_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TestMethodResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestMethodResponse) ProtoMessage() {}

func (x *TestMethodResponse) ProtoReflect() protoreflect.Message {
	mi := &file_merge_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestMethodResponse.ProtoReflect.Descriptor instead.
func (*TestMethodResponse) Descriptor() ([]byte, []int) {
	return file_merge_proto_rawDescGZIP(), []int{5}
}

func (x *TestMethodResponse) GetWelcome() string {
	if x != nil {
		return x.Welcome
	}
	return ""
}

type TriggerFieldTaggerRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Field     string `protobuf:"bytes,1,opt,name=field,proto3" json:"field"`
	Condition string `protobuf:"bytes,2,opt,name=condition,proto3" json:"condition"`
	Value     string `protobuf:"bytes,3,opt,name=value,proto3" json:"value"`
	SetField  string `protobuf:"bytes,4,opt,name=set_field,json=setField,proto3" json:"set_field"`
	SetValue  string `protobuf:"bytes,5,opt,name=set_value,json=setValue,proto3" json:"set_value"`
}

func (x *TriggerFieldTaggerRequest) Reset() {
	*x = TriggerFieldTaggerRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_merge_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TriggerFieldTaggerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TriggerFieldTaggerRequest) ProtoMessage() {}

func (x *TriggerFieldTaggerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_merge_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TriggerFieldTaggerRequest.ProtoReflect.Descriptor instead.
func (*TriggerFieldTaggerRequest) Descriptor() ([]byte, []int) {
	return file_merge_proto_rawDescGZIP(), []int{6}
}

func (x *TriggerFieldTaggerRequest) GetField() string {
	if x != nil {
		return x.Field
	}
	return ""
}

func (x *TriggerFieldTaggerRequest) GetCondition() string {
	if x != nil {
		return x.Condition
	}
	return ""
}

func (x *TriggerFieldTaggerRequest) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *TriggerFieldTaggerRequest) GetSetField() string {
	if x != nil {
		return x.SetField
	}
	return ""
}

func (x *TriggerFieldTaggerRequest) GetSetValue() string {
	if x != nil {
		return x.SetValue
	}
	return ""
}

type TriggerFieldTaggerResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool   `protobuf:"varint,1,opt,name=success,proto3" json:"success"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
}

func (x *TriggerFieldTaggerResponse) Reset() {
	*x = TriggerFieldTaggerResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_merge_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TriggerFieldTaggerResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TriggerFieldTaggerResponse) ProtoMessage() {}

func (x *TriggerFieldTaggerResponse) ProtoReflect() protoreflect.Message {
	mi := &file_merge_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TriggerFieldTaggerResponse.ProtoReflect.Descriptor instead.
func (*TriggerFieldTaggerResponse) Descriptor() ([]byte, []int) {
	return file_merge_proto_rawDescGZIP(), []int{7}
}

func (x *TriggerFieldTaggerResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *TriggerFieldTaggerResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type TriggerMergeBaseRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SourceId     uint64 `protobuf:"varint,1,opt,name=source_id,json=sourceId,proto3" json:"source_id"`
	NodeId       uint64 `protobuf:"varint,2,opt,name=node_id,json=nodeId,proto3" json:"node_id"`
	TaskId       string `protobuf:"bytes,3,opt,name=task_id,json=taskId,proto3" json:"task_id"`
	ChildTaskId  string `protobuf:"bytes,4,opt,name=child_task_id,json=childTaskId,proto3" json:"child_task_id"`
	TriggerEvent string `protobuf:"bytes,5,opt,name=trigger_event,json=triggerEvent,proto3" json:"trigger_event"`
}

func (x *TriggerMergeBaseRequest) Reset() {
	*x = TriggerMergeBaseRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_merge_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TriggerMergeBaseRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TriggerMergeBaseRequest) ProtoMessage() {}

func (x *TriggerMergeBaseRequest) ProtoReflect() protoreflect.Message {
	mi := &file_merge_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TriggerMergeBaseRequest.ProtoReflect.Descriptor instead.
func (*TriggerMergeBaseRequest) Descriptor() ([]byte, []int) {
	return file_merge_proto_rawDescGZIP(), []int{8}
}

func (x *TriggerMergeBaseRequest) GetSourceId() uint64 {
	if x != nil {
		return x.SourceId
	}
	return 0
}

func (x *TriggerMergeBaseRequest) GetNodeId() uint64 {
	if x != nil {
		return x.NodeId
	}
	return 0
}

func (x *TriggerMergeBaseRequest) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *TriggerMergeBaseRequest) GetChildTaskId() string {
	if x != nil {
		return x.ChildTaskId
	}
	return ""
}

func (x *TriggerMergeBaseRequest) GetTriggerEvent() string {
	if x != nil {
		return x.TriggerEvent
	}
	return ""
}

type IpInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ip   string `protobuf:"bytes,1,opt,name=ip,proto3" json:"ip"`
	Area uint64 `protobuf:"varint,2,opt,name=area,proto3" json:"area"`
}

func (x *IpInfo) Reset() {
	*x = IpInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_merge_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IpInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IpInfo) ProtoMessage() {}

func (x *IpInfo) ProtoReflect() protoreflect.Message {
	mi := &file_merge_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IpInfo.ProtoReflect.Descriptor instead.
func (*IpInfo) Descriptor() ([]byte, []int) {
	return file_merge_proto_rawDescGZIP(), []int{9}
}

func (x *IpInfo) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *IpInfo) GetArea() uint64 {
	if x != nil {
		return x.Area
	}
	return 0
}

type DataRangeByTask struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId      string `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id"`
	ChildTaskId string `protobuf:"bytes,2,opt,name=child_task_id,json=childTaskId,proto3" json:"child_task_id"`
	NodeId      uint64 `protobuf:"varint,3,opt,name=node_id,json=nodeId,proto3" json:"node_id"`
}

func (x *DataRangeByTask) Reset() {
	*x = DataRangeByTask{}
	if protoimpl.UnsafeEnabled {
		mi := &file_merge_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DataRangeByTask) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DataRangeByTask) ProtoMessage() {}

func (x *DataRangeByTask) ProtoReflect() protoreflect.Message {
	mi := &file_merge_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DataRangeByTask.ProtoReflect.Descriptor instead.
func (*DataRangeByTask) Descriptor() ([]byte, []int) {
	return file_merge_proto_rawDescGZIP(), []int{10}
}

func (x *DataRangeByTask) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *DataRangeByTask) GetChildTaskId() string {
	if x != nil {
		return x.ChildTaskId
	}
	return ""
}

func (x *DataRangeByTask) GetNodeId() uint64 {
	if x != nil {
		return x.NodeId
	}
	return 0
}

type TriggerMergeForAssetRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TriggerMergeBaseRequest *TriggerMergeBaseRequest `protobuf:"bytes,1,opt,name=trigger_merge_base_request,json=triggerMergeBaseRequest,proto3" json:"trigger_merge_base_request"`
	AssetIds                []string                 `protobuf:"bytes,2,rep,name=asset_ids,json=assetIds,proto3" json:"asset_ids"`
	IpInfos                 []*IpInfo                `protobuf:"bytes,3,rep,name=ip_infos,json=ipInfos,proto3" json:"ip_infos"`
	DataRangeByTask         *DataRangeByTask         `protobuf:"bytes,4,opt,name=data_range_by_task,json=dataRangeByTask,proto3" json:"data_range_by_task"`
	Fields                  []string                 `protobuf:"bytes,5,rep,name=fields,proto3" json:"fields"`
	SubTrigger              int32                    `protobuf:"varint,6,opt,name=sub_trigger,json=subTrigger,proto3" json:"sub_trigger"`
}

func (x *TriggerMergeForAssetRequest) Reset() {
	*x = TriggerMergeForAssetRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_merge_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TriggerMergeForAssetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TriggerMergeForAssetRequest) ProtoMessage() {}

func (x *TriggerMergeForAssetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_merge_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TriggerMergeForAssetRequest.ProtoReflect.Descriptor instead.
func (*TriggerMergeForAssetRequest) Descriptor() ([]byte, []int) {
	return file_merge_proto_rawDescGZIP(), []int{11}
}

func (x *TriggerMergeForAssetRequest) GetTriggerMergeBaseRequest() *TriggerMergeBaseRequest {
	if x != nil {
		return x.TriggerMergeBaseRequest
	}
	return nil
}

func (x *TriggerMergeForAssetRequest) GetAssetIds() []string {
	if x != nil {
		return x.AssetIds
	}
	return nil
}

func (x *TriggerMergeForAssetRequest) GetIpInfos() []*IpInfo {
	if x != nil {
		return x.IpInfos
	}
	return nil
}

func (x *TriggerMergeForAssetRequest) GetDataRangeByTask() *DataRangeByTask {
	if x != nil {
		return x.DataRangeByTask
	}
	return nil
}

func (x *TriggerMergeForAssetRequest) GetFields() []string {
	if x != nil {
		return x.Fields
	}
	return nil
}

func (x *TriggerMergeForAssetRequest) GetSubTrigger() int32 {
	if x != nil {
		return x.SubTrigger
	}
	return 0
}

type TriggerMergeForDeviceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TriggerMergeBaseRequest *TriggerMergeBaseRequest `protobuf:"bytes,1,opt,name=trigger_merge_base_request,json=triggerMergeBaseRequest,proto3" json:"trigger_merge_base_request"`
	DeviceIds               []string                 `protobuf:"bytes,2,rep,name=device_ids,json=deviceIds,proto3" json:"device_ids"`
	UniqueKeys              []string                 `protobuf:"bytes,3,rep,name=unique_keys,json=uniqueKeys,proto3" json:"unique_keys"`
	DataRangeByTask         *DataRangeByTask         `protobuf:"bytes,4,opt,name=data_range_by_task,json=dataRangeByTask,proto3" json:"data_range_by_task"`
	Fields                  []string                 `protobuf:"bytes,5,rep,name=fields,proto3" json:"fields"`
	SubTrigger              int32                    `protobuf:"varint,6,opt,name=sub_trigger,json=subTrigger,proto3" json:"sub_trigger"`
}

func (x *TriggerMergeForDeviceRequest) Reset() {
	*x = TriggerMergeForDeviceRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_merge_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TriggerMergeForDeviceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TriggerMergeForDeviceRequest) ProtoMessage() {}

func (x *TriggerMergeForDeviceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_merge_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TriggerMergeForDeviceRequest.ProtoReflect.Descriptor instead.
func (*TriggerMergeForDeviceRequest) Descriptor() ([]byte, []int) {
	return file_merge_proto_rawDescGZIP(), []int{12}
}

func (x *TriggerMergeForDeviceRequest) GetTriggerMergeBaseRequest() *TriggerMergeBaseRequest {
	if x != nil {
		return x.TriggerMergeBaseRequest
	}
	return nil
}

func (x *TriggerMergeForDeviceRequest) GetDeviceIds() []string {
	if x != nil {
		return x.DeviceIds
	}
	return nil
}

func (x *TriggerMergeForDeviceRequest) GetUniqueKeys() []string {
	if x != nil {
		return x.UniqueKeys
	}
	return nil
}

func (x *TriggerMergeForDeviceRequest) GetDataRangeByTask() *DataRangeByTask {
	if x != nil {
		return x.DataRangeByTask
	}
	return nil
}

func (x *TriggerMergeForDeviceRequest) GetFields() []string {
	if x != nil {
		return x.Fields
	}
	return nil
}

func (x *TriggerMergeForDeviceRequest) GetSubTrigger() int32 {
	if x != nil {
		return x.SubTrigger
	}
	return 0
}

type TriggerMergeForVulnRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TriggerMergeBaseRequest *TriggerMergeBaseRequest `protobuf:"bytes,1,opt,name=trigger_merge_base_request,json=triggerMergeBaseRequest,proto3" json:"trigger_merge_base_request"`
	VulIds                  []string                 `protobuf:"bytes,2,rep,name=vul_ids,json=vulIds,proto3" json:"vul_ids"`
	DataRangeByTask         *DataRangeByTask         `protobuf:"bytes,3,opt,name=data_range_by_task,json=dataRangeByTask,proto3" json:"data_range_by_task"`
	Fields                  []string                 `protobuf:"bytes,4,rep,name=fields,proto3" json:"fields"`
	SubTrigger              int32                    `protobuf:"varint,5,opt,name=sub_trigger,json=subTrigger,proto3" json:"sub_trigger"`
}

func (x *TriggerMergeForVulnRequest) Reset() {
	*x = TriggerMergeForVulnRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_merge_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TriggerMergeForVulnRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TriggerMergeForVulnRequest) ProtoMessage() {}

func (x *TriggerMergeForVulnRequest) ProtoReflect() protoreflect.Message {
	mi := &file_merge_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TriggerMergeForVulnRequest.ProtoReflect.Descriptor instead.
func (*TriggerMergeForVulnRequest) Descriptor() ([]byte, []int) {
	return file_merge_proto_rawDescGZIP(), []int{13}
}

func (x *TriggerMergeForVulnRequest) GetTriggerMergeBaseRequest() *TriggerMergeBaseRequest {
	if x != nil {
		return x.TriggerMergeBaseRequest
	}
	return nil
}

func (x *TriggerMergeForVulnRequest) GetVulIds() []string {
	if x != nil {
		return x.VulIds
	}
	return nil
}

func (x *TriggerMergeForVulnRequest) GetDataRangeByTask() *DataRangeByTask {
	if x != nil {
		return x.DataRangeByTask
	}
	return nil
}

func (x *TriggerMergeForVulnRequest) GetFields() []string {
	if x != nil {
		return x.Fields
	}
	return nil
}

func (x *TriggerMergeForVulnRequest) GetSubTrigger() int32 {
	if x != nil {
		return x.SubTrigger
	}
	return 0
}

type TriggerMergeForStaffRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TriggerMergeBaseRequest *TriggerMergeBaseRequest `protobuf:"bytes,1,opt,name=trigger_merge_base_request,json=triggerMergeBaseRequest,proto3" json:"trigger_merge_base_request"`
	StaffIds                []string                 `protobuf:"bytes,2,rep,name=staff_ids,json=staffIds,proto3" json:"staff_ids"`
	DataRangeByTask         *DataRangeByTask         `protobuf:"bytes,3,opt,name=data_range_by_task,json=dataRangeByTask,proto3" json:"data_range_by_task"`
	Fields                  []string                 `protobuf:"bytes,4,rep,name=fields,proto3" json:"fields"`
	SubTrigger              int32                    `protobuf:"varint,5,opt,name=sub_trigger,json=subTrigger,proto3" json:"sub_trigger"`
}

func (x *TriggerMergeForStaffRequest) Reset() {
	*x = TriggerMergeForStaffRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_merge_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TriggerMergeForStaffRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TriggerMergeForStaffRequest) ProtoMessage() {}

func (x *TriggerMergeForStaffRequest) ProtoReflect() protoreflect.Message {
	mi := &file_merge_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TriggerMergeForStaffRequest.ProtoReflect.Descriptor instead.
func (*TriggerMergeForStaffRequest) Descriptor() ([]byte, []int) {
	return file_merge_proto_rawDescGZIP(), []int{14}
}

func (x *TriggerMergeForStaffRequest) GetTriggerMergeBaseRequest() *TriggerMergeBaseRequest {
	if x != nil {
		return x.TriggerMergeBaseRequest
	}
	return nil
}

func (x *TriggerMergeForStaffRequest) GetStaffIds() []string {
	if x != nil {
		return x.StaffIds
	}
	return nil
}

func (x *TriggerMergeForStaffRequest) GetDataRangeByTask() *DataRangeByTask {
	if x != nil {
		return x.DataRangeByTask
	}
	return nil
}

func (x *TriggerMergeForStaffRequest) GetFields() []string {
	if x != nil {
		return x.Fields
	}
	return nil
}

func (x *TriggerMergeForStaffRequest) GetSubTrigger() int32 {
	if x != nil {
		return x.SubTrigger
	}
	return 0
}

type TriggerMergeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool   `protobuf:"varint,1,opt,name=success,proto3" json:"success"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
}

func (x *TriggerMergeResponse) Reset() {
	*x = TriggerMergeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_merge_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TriggerMergeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TriggerMergeResponse) ProtoMessage() {}

func (x *TriggerMergeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_merge_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TriggerMergeResponse.ProtoReflect.Descriptor instead.
func (*TriggerMergeResponse) Descriptor() ([]byte, []int) {
	return file_merge_proto_rawDescGZIP(), []int{15}
}

func (x *TriggerMergeResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *TriggerMergeResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type UpdateMergeDataToTaskRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskType     string `protobuf:"bytes,1,opt,name=task_type,json=taskType,proto3" json:"task_type"`
	TaskId       int64  `protobuf:"varint,2,opt,name=task_id,json=taskId,proto3" json:"task_id"`
	BusinessType string `protobuf:"bytes,3,opt,name=business_type,json=businessType,proto3" json:"business_type"`
	RecordId     uint64 `protobuf:"varint,4,opt,name=record_id,json=recordId,proto3" json:"record_id"`
}

func (x *UpdateMergeDataToTaskRequest) Reset() {
	*x = UpdateMergeDataToTaskRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_merge_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateMergeDataToTaskRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateMergeDataToTaskRequest) ProtoMessage() {}

func (x *UpdateMergeDataToTaskRequest) ProtoReflect() protoreflect.Message {
	mi := &file_merge_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateMergeDataToTaskRequest.ProtoReflect.Descriptor instead.
func (*UpdateMergeDataToTaskRequest) Descriptor() ([]byte, []int) {
	return file_merge_proto_rawDescGZIP(), []int{16}
}

func (x *UpdateMergeDataToTaskRequest) GetTaskType() string {
	if x != nil {
		return x.TaskType
	}
	return ""
}

func (x *UpdateMergeDataToTaskRequest) GetTaskId() int64 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

func (x *UpdateMergeDataToTaskRequest) GetBusinessType() string {
	if x != nil {
		return x.BusinessType
	}
	return ""
}

func (x *UpdateMergeDataToTaskRequest) GetRecordId() uint64 {
	if x != nil {
		return x.RecordId
	}
	return 0
}

type RecalRiskLevelResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool   `protobuf:"varint,1,opt,name=success,proto3" json:"success"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
}

func (x *RecalRiskLevelResponse) Reset() {
	*x = RecalRiskLevelResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_merge_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecalRiskLevelResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecalRiskLevelResponse) ProtoMessage() {}

func (x *RecalRiskLevelResponse) ProtoReflect() protoreflect.Message {
	mi := &file_merge_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecalRiskLevelResponse.ProtoReflect.Descriptor instead.
func (*RecalRiskLevelResponse) Descriptor() ([]byte, []int) {
	return file_merge_proto_rawDescGZIP(), []int{17}
}

func (x *RecalRiskLevelResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *RecalRiskLevelResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type UpdateMergeDataToTaskResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Count          int32  `protobuf:"varint,1,opt,name=count,proto3" json:"count"`
	SuccessCount   int32  `protobuf:"varint,2,opt,name=success_count,json=successCount,proto3" json:"success_count"`
	FailedCount    int32  `protobuf:"varint,3,opt,name=failed_count,json=failedCount,proto3" json:"failed_count"`
	DiscardedCount int32  `protobuf:"varint,4,opt,name=discarded_count,json=discardedCount,proto3" json:"discarded_count"`
	StartTime      string `protobuf:"bytes,5,opt,name=start_time,json=startTime,proto3" json:"start_time"`
	EndTime        string `protobuf:"bytes,6,opt,name=end_time,json=endTime,proto3" json:"end_time"`
	WorkHours      int64  `protobuf:"varint,7,opt,name=work_hours,json=workHours,proto3" json:"work_hours"`
	Duration       int64  `protobuf:"varint,8,opt,name=duration,proto3" json:"duration"`
}

func (x *UpdateMergeDataToTaskResponse) Reset() {
	*x = UpdateMergeDataToTaskResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_merge_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateMergeDataToTaskResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateMergeDataToTaskResponse) ProtoMessage() {}

func (x *UpdateMergeDataToTaskResponse) ProtoReflect() protoreflect.Message {
	mi := &file_merge_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateMergeDataToTaskResponse.ProtoReflect.Descriptor instead.
func (*UpdateMergeDataToTaskResponse) Descriptor() ([]byte, []int) {
	return file_merge_proto_rawDescGZIP(), []int{18}
}

func (x *UpdateMergeDataToTaskResponse) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *UpdateMergeDataToTaskResponse) GetSuccessCount() int32 {
	if x != nil {
		return x.SuccessCount
	}
	return 0
}

func (x *UpdateMergeDataToTaskResponse) GetFailedCount() int32 {
	if x != nil {
		return x.FailedCount
	}
	return 0
}

func (x *UpdateMergeDataToTaskResponse) GetDiscardedCount() int32 {
	if x != nil {
		return x.DiscardedCount
	}
	return 0
}

func (x *UpdateMergeDataToTaskResponse) GetStartTime() string {
	if x != nil {
		return x.StartTime
	}
	return ""
}

func (x *UpdateMergeDataToTaskResponse) GetEndTime() string {
	if x != nil {
		return x.EndTime
	}
	return ""
}

func (x *UpdateMergeDataToTaskResponse) GetWorkHours() int64 {
	if x != nil {
		return x.WorkHours
	}
	return 0
}

func (x *UpdateMergeDataToTaskResponse) GetDuration() int64 {
	if x != nil {
		return x.Duration
	}
	return 0
}

type ManualMergeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BusinessType string   `protobuf:"bytes,1,opt,name=business_type,json=businessType,proto3" json:"business_type"`
	Fields       []string `protobuf:"bytes,2,rep,name=fields,proto3" json:"fields"`
	BatchNo      string   `protobuf:"bytes,3,opt,name=batch_no,json=batchNo,proto3" json:"batch_no"`
}

func (x *ManualMergeRequest) Reset() {
	*x = ManualMergeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_merge_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ManualMergeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ManualMergeRequest) ProtoMessage() {}

func (x *ManualMergeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_merge_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ManualMergeRequest.ProtoReflect.Descriptor instead.
func (*ManualMergeRequest) Descriptor() ([]byte, []int) {
	return file_merge_proto_rawDescGZIP(), []int{19}
}

func (x *ManualMergeRequest) GetBusinessType() string {
	if x != nil {
		return x.BusinessType
	}
	return ""
}

func (x *ManualMergeRequest) GetFields() []string {
	if x != nil {
		return x.Fields
	}
	return nil
}

func (x *ManualMergeRequest) GetBatchNo() string {
	if x != nil {
		return x.BatchNo
	}
	return ""
}

type ManualMergeResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Field   string `protobuf:"bytes,1,opt,name=field,proto3" json:"field"`
	Success bool   `protobuf:"varint,2,opt,name=success,proto3" json:"success"`
	Message string `protobuf:"bytes,3,opt,name=message,proto3" json:"message"`
	Updated int64  `protobuf:"varint,4,opt,name=updated,proto3" json:"updated"`
}

func (x *ManualMergeResult) Reset() {
	*x = ManualMergeResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_merge_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ManualMergeResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ManualMergeResult) ProtoMessage() {}

func (x *ManualMergeResult) ProtoReflect() protoreflect.Message {
	mi := &file_merge_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ManualMergeResult.ProtoReflect.Descriptor instead.
func (*ManualMergeResult) Descriptor() ([]byte, []int) {
	return file_merge_proto_rawDescGZIP(), []int{20}
}

func (x *ManualMergeResult) GetField() string {
	if x != nil {
		return x.Field
	}
	return ""
}

func (x *ManualMergeResult) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *ManualMergeResult) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ManualMergeResult) GetUpdated() int64 {
	if x != nil {
		return x.Updated
	}
	return 0
}

type ManualMergeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Results []*ManualMergeResult `protobuf:"bytes,1,rep,name=results,proto3" json:"results"`
	BatchNo string               `protobuf:"bytes,2,opt,name=batch_no,json=batchNo,proto3" json:"batch_no"`
	Message string               `protobuf:"bytes,3,opt,name=message,proto3" json:"message"`
}

func (x *ManualMergeResponse) Reset() {
	*x = ManualMergeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_merge_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ManualMergeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ManualMergeResponse) ProtoMessage() {}

func (x *ManualMergeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_merge_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ManualMergeResponse.ProtoReflect.Descriptor instead.
func (*ManualMergeResponse) Descriptor() ([]byte, []int) {
	return file_merge_proto_rawDescGZIP(), []int{21}
}

func (x *ManualMergeResponse) GetResults() []*ManualMergeResult {
	if x != nil {
		return x.Results
	}
	return nil
}

func (x *ManualMergeResponse) GetBatchNo() string {
	if x != nil {
		return x.BatchNo
	}
	return ""
}

func (x *ManualMergeResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type ReMergeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId  string `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id"`
	Success bool   `protobuf:"varint,2,opt,name=success,proto3" json:"success"`
	Message string `protobuf:"bytes,3,opt,name=message,proto3" json:"message"`
}

func (x *ReMergeResponse) Reset() {
	*x = ReMergeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_merge_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReMergeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReMergeResponse) ProtoMessage() {}

func (x *ReMergeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_merge_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReMergeResponse.ProtoReflect.Descriptor instead.
func (*ReMergeResponse) Descriptor() ([]byte, []int) {
	return file_merge_proto_rawDescGZIP(), []int{22}
}

func (x *ReMergeResponse) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *ReMergeResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *ReMergeResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type ManualCalibrationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BusinessType string            `protobuf:"bytes,1,opt,name=business_type,json=businessType,proto3" json:"business_type"`
	Ids          []string          `protobuf:"bytes,2,rep,name=ids,proto3" json:"ids"`
	Values       map[string]string `protobuf:"bytes,3,rep,name=values,proto3" json:"values" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // values的值是json字符串,为了免去proto中使用any类型
	BatchNo      string            `protobuf:"bytes,4,opt,name=batch_no,json=batchNo,proto3" json:"batch_no"`
}

func (x *ManualCalibrationRequest) Reset() {
	*x = ManualCalibrationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_merge_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ManualCalibrationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ManualCalibrationRequest) ProtoMessage() {}

func (x *ManualCalibrationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_merge_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ManualCalibrationRequest.ProtoReflect.Descriptor instead.
func (*ManualCalibrationRequest) Descriptor() ([]byte, []int) {
	return file_merge_proto_rawDescGZIP(), []int{23}
}

func (x *ManualCalibrationRequest) GetBusinessType() string {
	if x != nil {
		return x.BusinessType
	}
	return ""
}

func (x *ManualCalibrationRequest) GetIds() []string {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *ManualCalibrationRequest) GetValues() map[string]string {
	if x != nil {
		return x.Values
	}
	return nil
}

func (x *ManualCalibrationRequest) GetBatchNo() string {
	if x != nil {
		return x.BatchNo
	}
	return ""
}

type ManualCalibrationResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool   `protobuf:"varint,1,opt,name=success,proto3" json:"success"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
}

func (x *ManualCalibrationResponse) Reset() {
	*x = ManualCalibrationResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_merge_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ManualCalibrationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ManualCalibrationResponse) ProtoMessage() {}

func (x *ManualCalibrationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_merge_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ManualCalibrationResponse.ProtoReflect.Descriptor instead.
func (*ManualCalibrationResponse) Descriptor() ([]byte, []int) {
	return file_merge_proto_rawDescGZIP(), []int{24}
}

func (x *ManualCalibrationResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *ManualCalibrationResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type IPInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ip   string `protobuf:"bytes,1,opt,name=ip,proto3" json:"ip"`
	Area uint64 `protobuf:"varint,2,opt,name=area,proto3" json:"area"`
}

func (x *IPInfo) Reset() {
	*x = IPInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_merge_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IPInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IPInfo) ProtoMessage() {}

func (x *IPInfo) ProtoReflect() protoreflect.Message {
	mi := &file_merge_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IPInfo.ProtoReflect.Descriptor instead.
func (*IPInfo) Descriptor() ([]byte, []int) {
	return file_merge_proto_rawDescGZIP(), []int{25}
}

func (x *IPInfo) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *IPInfo) GetArea() uint64 {
	if x != nil {
		return x.Area
	}
	return 0
}

type BusinessInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	System     string   `protobuf:"bytes,1,opt,name=system,proto3" json:"system"`
	SystemId   string   `protobuf:"bytes,2,opt,name=system_id,json=systemId,proto3" json:"system_id"`
	Owner      string   `protobuf:"bytes,3,opt,name=owner,proto3" json:"owner"`
	OwnerId    string   `protobuf:"bytes,4,opt,name=owner_id,json=ownerId,proto3" json:"owner_id"`
	Department []string `protobuf:"bytes,5,rep,name=department,proto3" json:"department"`
}

func (x *BusinessInfo) Reset() {
	*x = BusinessInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_merge_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BusinessInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessInfo) ProtoMessage() {}

func (x *BusinessInfo) ProtoReflect() protoreflect.Message {
	mi := &file_merge_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessInfo.ProtoReflect.Descriptor instead.
func (*BusinessInfo) Descriptor() ([]byte, []int) {
	return file_merge_proto_rawDescGZIP(), []int{26}
}

func (x *BusinessInfo) GetSystem() string {
	if x != nil {
		return x.System
	}
	return ""
}

func (x *BusinessInfo) GetSystemId() string {
	if x != nil {
		return x.SystemId
	}
	return ""
}

func (x *BusinessInfo) GetOwner() string {
	if x != nil {
		return x.Owner
	}
	return ""
}

func (x *BusinessInfo) GetOwnerId() string {
	if x != nil {
		return x.OwnerId
	}
	return ""
}

func (x *BusinessInfo) GetDepartment() []string {
	if x != nil {
		return x.Department
	}
	return nil
}

type Department struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BusinessSystemId   string        `protobuf:"bytes,1,opt,name=business_system_id,json=businessSystemId,proto3" json:"business_system_id"`
	BusinessSystemName string        `protobuf:"bytes,2,opt,name=business_system_name,json=businessSystemName,proto3" json:"business_system_name"`
	UserId             string        `protobuf:"bytes,3,opt,name=user_id,json=userId,proto3" json:"user_id"`
	UserName           string        `protobuf:"bytes,4,opt,name=user_name,json=userName,proto3" json:"user_name"`
	Name               string        `protobuf:"bytes,5,opt,name=name,proto3" json:"name"`
	Id                 uint64        `protobuf:"varint,6,opt,name=id,proto3" json:"id"`
	Parents            []*Department `protobuf:"bytes,7,rep,name=parents,proto3" json:"parents"`
}

func (x *Department) Reset() {
	*x = Department{}
	if protoimpl.UnsafeEnabled {
		mi := &file_merge_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Department) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Department) ProtoMessage() {}

func (x *Department) ProtoReflect() protoreflect.Message {
	mi := &file_merge_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Department.ProtoReflect.Descriptor instead.
func (*Department) Descriptor() ([]byte, []int) {
	return file_merge_proto_rawDescGZIP(), []int{27}
}

func (x *Department) GetBusinessSystemId() string {
	if x != nil {
		return x.BusinessSystemId
	}
	return ""
}

func (x *Department) GetBusinessSystemName() string {
	if x != nil {
		return x.BusinessSystemName
	}
	return ""
}

func (x *Department) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *Department) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

func (x *Department) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Department) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Department) GetParents() []*Department {
	if x != nil {
		return x.Parents
	}
	return nil
}

type OperInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id   string `protobuf:"bytes,1,opt,name=id,proto3" json:"id"`
	Fid  string `protobuf:"bytes,2,opt,name=fid,proto3" json:"fid"`
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name"`
}

func (x *OperInfo) Reset() {
	*x = OperInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_merge_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OperInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OperInfo) ProtoMessage() {}

func (x *OperInfo) ProtoReflect() protoreflect.Message {
	mi := &file_merge_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OperInfo.ProtoReflect.Descriptor instead.
func (*OperInfo) Descriptor() ([]byte, []int) {
	return file_merge_proto_rawDescGZIP(), []int{28}
}

func (x *OperInfo) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *OperInfo) GetFid() string {
	if x != nil {
		return x.Fid
	}
	return ""
}

func (x *OperInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type DeviceBusiness struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Business           *BusinessInfo `protobuf:"bytes,1,opt,name=business,proto3" json:"business"`
	BusinessDepartment *Department   `protobuf:"bytes,2,opt,name=business_department,json=businessDepartment,proto3" json:"business_department"`
	Ip                 []*IPInfo     `protobuf:"bytes,3,rep,name=ip,proto3" json:"ip"`
}

func (x *DeviceBusiness) Reset() {
	*x = DeviceBusiness{}
	if protoimpl.UnsafeEnabled {
		mi := &file_merge_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeviceBusiness) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceBusiness) ProtoMessage() {}

func (x *DeviceBusiness) ProtoReflect() protoreflect.Message {
	mi := &file_merge_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceBusiness.ProtoReflect.Descriptor instead.
func (*DeviceBusiness) Descriptor() ([]byte, []int) {
	return file_merge_proto_rawDescGZIP(), []int{29}
}

func (x *DeviceBusiness) GetBusiness() *BusinessInfo {
	if x != nil {
		return x.Business
	}
	return nil
}

func (x *DeviceBusiness) GetBusinessDepartment() *Department {
	if x != nil {
		return x.BusinessDepartment
	}
	return nil
}

func (x *DeviceBusiness) GetIp() []*IPInfo {
	if x != nil {
		return x.Ip
	}
	return nil
}

type DeviceOpers struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OperInfo       *OperInfo   `protobuf:"bytes,1,opt,name=oper_info,json=operInfo,proto3" json:"oper_info"`
	OperDepartment *Department `protobuf:"bytes,2,opt,name=oper_department,json=operDepartment,proto3" json:"oper_department"`
	Ip             []*IPInfo   `protobuf:"bytes,3,rep,name=ip,proto3" json:"ip"`
}

func (x *DeviceOpers) Reset() {
	*x = DeviceOpers{}
	if protoimpl.UnsafeEnabled {
		mi := &file_merge_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeviceOpers) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceOpers) ProtoMessage() {}

func (x *DeviceOpers) ProtoReflect() protoreflect.Message {
	mi := &file_merge_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceOpers.ProtoReflect.Descriptor instead.
func (*DeviceOpers) Descriptor() ([]byte, []int) {
	return file_merge_proto_rawDescGZIP(), []int{30}
}

func (x *DeviceOpers) GetOperInfo() *OperInfo {
	if x != nil {
		return x.OperInfo
	}
	return nil
}

func (x *DeviceOpers) GetOperDepartment() *Department {
	if x != nil {
		return x.OperDepartment
	}
	return nil
}

func (x *DeviceOpers) GetIp() []*IPInfo {
	if x != nil {
		return x.Ip
	}
	return nil
}

type Device struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          string            `protobuf:"bytes,1,opt,name=id,proto3" json:"id"`
	Source      []*Source         `protobuf:"bytes,2,rep,name=source,proto3" json:"source"`
	Hostname    []string          `protobuf:"bytes,3,rep,name=hostname,proto3" json:"hostname"`
	Sn          []string          `protobuf:"bytes,4,rep,name=sn,proto3" json:"sn"`
	Mac         []string          `protobuf:"bytes,5,rep,name=mac,proto3" json:"mac"`
	Ip          []string          `protobuf:"bytes,6,rep,name=ip,proto3" json:"ip"`
	PrivateIp   []string          `protobuf:"bytes,7,rep,name=private_ip,json=privateIp,proto3" json:"private_ip"`
	PublicIp    []string          `protobuf:"bytes,8,rep,name=public_ip,json=publicIp,proto3" json:"public_ip"`
	MachineRoom []string          `protobuf:"bytes,9,rep,name=machine_room,json=machineRoom,proto3" json:"machine_room"`
	Area        []*Area           `protobuf:"bytes,10,rep,name=area,proto3" json:"area"`
	Os          []string          `protobuf:"bytes,11,rep,name=os,proto3" json:"os"`
	CreatedAt   string            `protobuf:"bytes,12,opt,name=created_at,json=createdAt,proto3" json:"created_at"`
	UpdatedAt   string            `protobuf:"bytes,13,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at"`
	DeletedAt   string            `protobuf:"bytes,14,opt,name=deleted_at,json=deletedAt,proto3" json:"deleted_at"`
	Oper        []string          `protobuf:"bytes,15,rep,name=oper,proto3" json:"oper"`                                 //运维人员
	SourceId    []uint64          `protobuf:"varint,16,rep,packed,name=source_id,json=sourceId,proto3" json:"source_id"` //数据源ID
	AreaId      []uint64          `protobuf:"varint,17,rep,packed,name=area_id,json=areaId,proto3" json:"area_id"`       //区域ID
	Tags        []string          `protobuf:"bytes,18,rep,name=tags,proto3" json:"tags"`                                 //标签
	Fid         string            `protobuf:"bytes,19,opt,name=fid,proto3" json:"fid"`                                   //融合key
	PocNum      int64             `protobuf:"varint,20,opt,name=poc_num,json=pocNum,proto3" json:"poc_num"`              // 设备对应的漏洞数量
	Business    []*DeviceBusiness `protobuf:"bytes,21,rep,name=business,proto3" json:"business"`                         // 业务系统
	Opers       []*DeviceOpers    `protobuf:"bytes,22,rep,name=opers,proto3" json:"opers"`                               // 运维人员
}

func (x *Device) Reset() {
	*x = Device{}
	if protoimpl.UnsafeEnabled {
		mi := &file_merge_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Device) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Device) ProtoMessage() {}

func (x *Device) ProtoReflect() protoreflect.Message {
	mi := &file_merge_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Device.ProtoReflect.Descriptor instead.
func (*Device) Descriptor() ([]byte, []int) {
	return file_merge_proto_rawDescGZIP(), []int{31}
}

func (x *Device) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Device) GetSource() []*Source {
	if x != nil {
		return x.Source
	}
	return nil
}

func (x *Device) GetHostname() []string {
	if x != nil {
		return x.Hostname
	}
	return nil
}

func (x *Device) GetSn() []string {
	if x != nil {
		return x.Sn
	}
	return nil
}

func (x *Device) GetMac() []string {
	if x != nil {
		return x.Mac
	}
	return nil
}

func (x *Device) GetIp() []string {
	if x != nil {
		return x.Ip
	}
	return nil
}

func (x *Device) GetPrivateIp() []string {
	if x != nil {
		return x.PrivateIp
	}
	return nil
}

func (x *Device) GetPublicIp() []string {
	if x != nil {
		return x.PublicIp
	}
	return nil
}

func (x *Device) GetMachineRoom() []string {
	if x != nil {
		return x.MachineRoom
	}
	return nil
}

func (x *Device) GetArea() []*Area {
	if x != nil {
		return x.Area
	}
	return nil
}

func (x *Device) GetOs() []string {
	if x != nil {
		return x.Os
	}
	return nil
}

func (x *Device) GetCreatedAt() string {
	if x != nil {
		return x.CreatedAt
	}
	return ""
}

func (x *Device) GetUpdatedAt() string {
	if x != nil {
		return x.UpdatedAt
	}
	return ""
}

func (x *Device) GetDeletedAt() string {
	if x != nil {
		return x.DeletedAt
	}
	return ""
}

func (x *Device) GetOper() []string {
	if x != nil {
		return x.Oper
	}
	return nil
}

func (x *Device) GetSourceId() []uint64 {
	if x != nil {
		return x.SourceId
	}
	return nil
}

func (x *Device) GetAreaId() []uint64 {
	if x != nil {
		return x.AreaId
	}
	return nil
}

func (x *Device) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *Device) GetFid() string {
	if x != nil {
		return x.Fid
	}
	return ""
}

func (x *Device) GetPocNum() int64 {
	if x != nil {
		return x.PocNum
	}
	return 0
}

func (x *Device) GetBusiness() []*DeviceBusiness {
	if x != nil {
		return x.Business
	}
	return nil
}

func (x *Device) GetOpers() []*DeviceOpers {
	if x != nil {
		return x.Opers
	}
	return nil
}

type IpInfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id" form:"id" validate:"required"`  
}

func (x *IpInfoRequest) Reset() {
	*x = IpInfoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_merge_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IpInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IpInfoRequest) ProtoMessage() {}

func (x *IpInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_merge_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IpInfoRequest.ProtoReflect.Descriptor instead.
func (*IpInfoRequest) Descriptor() ([]byte, []int) {
	return file_merge_proto_rawDescGZIP(), []int{32}
}

func (x *IpInfoRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type DeviceInfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id" form:"id" validate:"required"`  
}

func (x *DeviceInfoRequest) Reset() {
	*x = DeviceInfoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_merge_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeviceInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceInfoRequest) ProtoMessage() {}

func (x *DeviceInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_merge_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceInfoRequest.ProtoReflect.Descriptor instead.
func (*DeviceInfoRequest) Descriptor() ([]byte, []int) {
	return file_merge_proto_rawDescGZIP(), []int{33}
}

func (x *DeviceInfoRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

// DepartmentBase 部门信息
type DepartmentBase struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BusinessSystemId   string            `protobuf:"bytes,1,opt,name=business_system_id,json=businessSystemId,proto3" json:"business_system_id"`       // 业务系统
	BusinessSystemName string            `protobuf:"bytes,2,opt,name=business_system_name,json=businessSystemName,proto3" json:"business_system_name"` // 业务系统名称
	UserId             string            `protobuf:"bytes,3,opt,name=user_id,json=userId,proto3" json:"user_id"`                                       // 用户id
	UserName           string            `protobuf:"bytes,4,opt,name=user_name,json=userName,proto3" json:"user_name"`                                 // 用户名称
	Name               string            `protobuf:"bytes,5,opt,name=name,proto3" json:"name"`                                                         // 部门名称,完整的部门名称，以/分隔
	Id                 uint64            `protobuf:"varint,6,opt,name=id,proto3" json:"id"`                                                            // 部门id
	Parents            []*DepartmentBase `protobuf:"bytes,7,rep,name=parents,proto3" json:"parents"`                                                   // 部门父级
}

func (x *DepartmentBase) Reset() {
	*x = DepartmentBase{}
	if protoimpl.UnsafeEnabled {
		mi := &file_merge_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DepartmentBase) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DepartmentBase) ProtoMessage() {}

func (x *DepartmentBase) ProtoReflect() protoreflect.Message {
	mi := &file_merge_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DepartmentBase.ProtoReflect.Descriptor instead.
func (*DepartmentBase) Descriptor() ([]byte, []int) {
	return file_merge_proto_rawDescGZIP(), []int{34}
}

func (x *DepartmentBase) GetBusinessSystemId() string {
	if x != nil {
		return x.BusinessSystemId
	}
	return ""
}

func (x *DepartmentBase) GetBusinessSystemName() string {
	if x != nil {
		return x.BusinessSystemName
	}
	return ""
}

func (x *DepartmentBase) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *DepartmentBase) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

func (x *DepartmentBase) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DepartmentBase) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DepartmentBase) GetParents() []*DepartmentBase {
	if x != nil {
		return x.Parents
	}
	return nil
}

// PersonFindInfo 人员查找信息
type PersonFindInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SourceId     uint64 `protobuf:"varint,1,opt,name=source_id,json=sourceId,proto3" json:"source_id"`            // 来源id
	NodeId       uint64 `protobuf:"varint,2,opt,name=node_id,json=nodeId,proto3" json:"node_id"`                  // 节点id
	SourceValue  string `protobuf:"bytes,3,opt,name=source_value,json=sourceValue,proto3" json:"source_value"`    // 来源字段值
	MappingField string `protobuf:"bytes,4,opt,name=mapping_field,json=mappingField,proto3" json:"mapping_field"` // 映射字段，name,english_name,email,phone,work_number
	FindCount    int32  `protobuf:"varint,5,opt,name=find_count,json=findCount,proto3" json:"find_count"`         // 找到的人数
}

func (x *PersonFindInfo) Reset() {
	*x = PersonFindInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_merge_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PersonFindInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PersonFindInfo) ProtoMessage() {}

func (x *PersonFindInfo) ProtoReflect() protoreflect.Message {
	mi := &file_merge_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PersonFindInfo.ProtoReflect.Descriptor instead.
func (*PersonFindInfo) Descriptor() ([]byte, []int) {
	return file_merge_proto_rawDescGZIP(), []int{35}
}

func (x *PersonFindInfo) GetSourceId() uint64 {
	if x != nil {
		return x.SourceId
	}
	return 0
}

func (x *PersonFindInfo) GetNodeId() uint64 {
	if x != nil {
		return x.NodeId
	}
	return 0
}

func (x *PersonFindInfo) GetSourceValue() string {
	if x != nil {
		return x.SourceValue
	}
	return ""
}

func (x *PersonFindInfo) GetMappingField() string {
	if x != nil {
		return x.MappingField
	}
	return ""
}

func (x *PersonFindInfo) GetFindCount() int32 {
	if x != nil {
		return x.FindCount
	}
	return 0
}

// PersonBase 人员基本信息
type PersonBase struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         string            `protobuf:"bytes,1,opt,name=id,proto3" json:"id"`
	Fid        string            `protobuf:"bytes,2,opt,name=fid,proto3" json:"fid"`
	Name       string            `protobuf:"bytes,3,opt,name=name,proto3" json:"name"`
	Pgid       string            `protobuf:"bytes,4,opt,name=pgid,proto3" json:"pgid"`
	FindInfo   []*PersonFindInfo `protobuf:"bytes,5,rep,name=find_info,json=findInfo,proto3" json:"find_info"`
	Department []*DepartmentBase `protobuf:"bytes,6,rep,name=department,proto3" json:"department"`
}

func (x *PersonBase) Reset() {
	*x = PersonBase{}
	if protoimpl.UnsafeEnabled {
		mi := &file_merge_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PersonBase) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PersonBase) ProtoMessage() {}

func (x *PersonBase) ProtoReflect() protoreflect.Message {
	mi := &file_merge_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PersonBase.ProtoReflect.Descriptor instead.
func (*PersonBase) Descriptor() ([]byte, []int) {
	return file_merge_proto_rawDescGZIP(), []int{36}
}

func (x *PersonBase) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *PersonBase) GetFid() string {
	if x != nil {
		return x.Fid
	}
	return ""
}

func (x *PersonBase) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PersonBase) GetPgid() string {
	if x != nil {
		return x.Pgid
	}
	return ""
}

func (x *PersonBase) GetFindInfo() []*PersonFindInfo {
	if x != nil {
		return x.FindInfo
	}
	return nil
}

func (x *PersonBase) GetDepartment() []*DepartmentBase {
	if x != nil {
		return x.Department
	}
	return nil
}

// PersonWithMapping 人员映射信息
// PersonWithMapping 人员映射信息
type PersonWithMapping struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SourceId     uint64 `protobuf:"varint,1,opt,name=source_id,json=sourceId,proto3" json:"source_id"`            // 来源id
	NodeId       uint64 `protobuf:"varint,2,opt,name=node_id,json=nodeId,proto3" json:"node_id"`                  // 节点id
	SourceValue  string `protobuf:"bytes,3,opt,name=source_value,json=sourceValue,proto3" json:"source_value"`    // 来源字段值
	MappingField string `protobuf:"bytes,4,opt,name=mapping_field,json=mappingField,proto3" json:"mapping_field"` // 映射字段，name,english_name,email,phone,work_number
}

func (x *PersonWithMapping) Reset() {
	*x = PersonWithMapping{}
	if protoimpl.UnsafeEnabled {
		mi := &file_merge_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PersonWithMapping) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PersonWithMapping) ProtoMessage() {}

func (x *PersonWithMapping) ProtoReflect() protoreflect.Message {
	mi := &file_merge_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PersonWithMapping.ProtoReflect.Descriptor instead.
func (*PersonWithMapping) Descriptor() ([]byte, []int) {
	return file_merge_proto_rawDescGZIP(), []int{37}
}

func (x *PersonWithMapping) GetSourceId() uint64 {
	if x != nil {
		return x.SourceId
	}
	return 0
}

func (x *PersonWithMapping) GetNodeId() uint64 {
	if x != nil {
		return x.NodeId
	}
	return 0
}

func (x *PersonWithMapping) GetSourceValue() string {
	if x != nil {
		return x.SourceValue
	}
	return ""
}

func (x *PersonWithMapping) GetMappingField() string {
	if x != nil {
		return x.MappingField
	}
	return ""
}

type IpAdminInfoResponseItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	System      string        `protobuf:"bytes,1,opt,name=system,proto3" json:"system"`                           // 业务系统
	SystemId    string        `protobuf:"bytes,2,opt,name=system_id,json=systemId,proto3" json:"system_id"`       // 业务系统id
	Owner       string        `protobuf:"bytes,3,opt,name=owner,proto3" json:"owner"`                             // 负责人
	OwnerId     string        `protobuf:"bytes,4,opt,name=owner_id,json=ownerId,proto3" json:"owner_id"`          // 负责人id
	PersonBase  []*PersonBase `protobuf:"bytes,5,rep,name=person_base,json=personBase,proto3" json:"person_base"` // 负责人信息
	Reliability int64         `protobuf:"varint,6,opt,name=reliability,proto3" json:"reliability"`                // 可信度（1 - 可信 2 - 待确认 3 - 黑名单）
}

func (x *IpAdminInfoResponseItem) Reset() {
	*x = IpAdminInfoResponseItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_merge_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IpAdminInfoResponseItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IpAdminInfoResponseItem) ProtoMessage() {}

func (x *IpAdminInfoResponseItem) ProtoReflect() protoreflect.Message {
	mi := &file_merge_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IpAdminInfoResponseItem.ProtoReflect.Descriptor instead.
func (*IpAdminInfoResponseItem) Descriptor() ([]byte, []int) {
	return file_merge_proto_rawDescGZIP(), []int{38}
}

func (x *IpAdminInfoResponseItem) GetSystem() string {
	if x != nil {
		return x.System
	}
	return ""
}

func (x *IpAdminInfoResponseItem) GetSystemId() string {
	if x != nil {
		return x.SystemId
	}
	return ""
}

func (x *IpAdminInfoResponseItem) GetOwner() string {
	if x != nil {
		return x.Owner
	}
	return ""
}

func (x *IpAdminInfoResponseItem) GetOwnerId() string {
	if x != nil {
		return x.OwnerId
	}
	return ""
}

func (x *IpAdminInfoResponseItem) GetPersonBase() []*PersonBase {
	if x != nil {
		return x.PersonBase
	}
	return nil
}

func (x *IpAdminInfoResponseItem) GetReliability() int64 {
	if x != nil {
		return x.Reliability
	}
	return 0
}

type IpAdminInfoResponseList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Admin []*IpAdminInfoResponseItem `protobuf:"bytes,1,rep,name=admin,proto3" json:"admin"` //业务系统信息
}

func (x *IpAdminInfoResponseList) Reset() {
	*x = IpAdminInfoResponseList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_merge_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IpAdminInfoResponseList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IpAdminInfoResponseList) ProtoMessage() {}

func (x *IpAdminInfoResponseList) ProtoReflect() protoreflect.Message {
	mi := &file_merge_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IpAdminInfoResponseList.ProtoReflect.Descriptor instead.
func (*IpAdminInfoResponseList) Descriptor() ([]byte, []int) {
	return file_merge_proto_rawDescGZIP(), []int{39}
}

func (x *IpAdminInfoResponseList) GetAdmin() []*IpAdminInfoResponseItem {
	if x != nil {
		return x.Admin
	}
	return nil
}

// 定义 IP 对应的端口映射列表的消息类型
type PortMappingList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Mappings []*PortMapping `protobuf:"bytes,1,rep,name=mappings,proto3" json:"mappings"` // 端口映射的列表
}

func (x *PortMappingList) Reset() {
	*x = PortMappingList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_merge_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PortMappingList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PortMappingList) ProtoMessage() {}

func (x *PortMappingList) ProtoReflect() protoreflect.Message {
	mi := &file_merge_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PortMappingList.ProtoReflect.Descriptor instead.
func (*PortMappingList) Descriptor() ([]byte, []int) {
	return file_merge_proto_rawDescGZIP(), []int{40}
}

func (x *PortMappingList) GetMappings() []*PortMapping {
	if x != nil {
		return x.Mappings
	}
	return nil
}

// 定义端口映射的消息类型
type PortMapping struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PublicIp    string `protobuf:"bytes,1,opt,name=public_ip,json=publicIp,proto3" json:"public_ip"`           // 公网 IP
	PublicPort  uint64 `protobuf:"varint,2,opt,name=public_port,json=publicPort,proto3" json:"public_port"`    // 公网端口
	PrivateIp   string `protobuf:"bytes,3,opt,name=private_ip,json=privateIp,proto3" json:"private_ip"`        // 内网 IP
	PrivatePort uint64 `protobuf:"varint,4,opt,name=private_port,json=privatePort,proto3" json:"private_port"` // 内网端口
}

func (x *PortMapping) Reset() {
	*x = PortMapping{}
	if protoimpl.UnsafeEnabled {
		mi := &file_merge_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PortMapping) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PortMapping) ProtoMessage() {}

func (x *PortMapping) ProtoReflect() protoreflect.Message {
	mi := &file_merge_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PortMapping.ProtoReflect.Descriptor instead.
func (*PortMapping) Descriptor() ([]byte, []int) {
	return file_merge_proto_rawDescGZIP(), []int{41}
}

func (x *PortMapping) GetPublicIp() string {
	if x != nil {
		return x.PublicIp
	}
	return ""
}

func (x *PortMapping) GetPublicPort() uint64 {
	if x != nil {
		return x.PublicPort
	}
	return 0
}

func (x *PortMapping) GetPrivateIp() string {
	if x != nil {
		return x.PrivateIp
	}
	return ""
}

func (x *PortMapping) GetPrivatePort() uint64 {
	if x != nil {
		return x.PrivatePort
	}
	return 0
}

type PersonInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          string `protobuf:"bytes,1,opt,name=id,proto3" json:"id"`
	Fid         string `protobuf:"bytes,2,opt,name=fid,proto3" json:"fid"`
	Name        string `protobuf:"bytes,3,opt,name=name,proto3" json:"name"`
	FindBy      string `protobuf:"bytes,4,opt,name=find_by,json=findBy,proto3" json:"find_by"`                // 查找方式
	SourceValue string `protobuf:"bytes,5,opt,name=source_value,json=sourceValue,proto3" json:"source_value"` // 来源值
}

func (x *PersonInfo) Reset() {
	*x = PersonInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_merge_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PersonInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PersonInfo) ProtoMessage() {}

func (x *PersonInfo) ProtoReflect() protoreflect.Message {
	mi := &file_merge_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PersonInfo.ProtoReflect.Descriptor instead.
func (*PersonInfo) Descriptor() ([]byte, []int) {
	return file_merge_proto_rawDescGZIP(), []int{42}
}

func (x *PersonInfo) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *PersonInfo) GetFid() string {
	if x != nil {
		return x.Fid
	}
	return ""
}

func (x *PersonInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PersonInfo) GetFindBy() string {
	if x != nil {
		return x.FindBy
	}
	return ""
}

func (x *PersonInfo) GetSourceValue() string {
	if x != nil {
		return x.SourceValue
	}
	return ""
}

type IpAdminInfoResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Admin          []*IpAdminInfoResponseItem `protobuf:"bytes,1,rep,name=admin,proto3" json:"admin"`                                         //业务系统信息
	Oper           []*PersonBase              `protobuf:"bytes,2,rep,name=oper,proto3" json:"oper"`                                           //运维人员
	MachineRoom    []string                   `protobuf:"bytes,3,rep,name=machine_room,json=machineRoom,proto3" json:"machine_room"`          //机房信息
	OperDepartment []*OperDepartment          `protobuf:"bytes,4,rep,name=oper_department,json=operDepartment,proto3" json:"oper_department"` //机房信息
	Tags           []string                   `protobuf:"bytes,5,rep,name=tags,proto3" json:"tags"`                                           // 标签信息
}

func (x *IpAdminInfoResponse) Reset() {
	*x = IpAdminInfoResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_merge_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IpAdminInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IpAdminInfoResponse) ProtoMessage() {}

func (x *IpAdminInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_merge_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IpAdminInfoResponse.ProtoReflect.Descriptor instead.
func (*IpAdminInfoResponse) Descriptor() ([]byte, []int) {
	return file_merge_proto_rawDescGZIP(), []int{43}
}

func (x *IpAdminInfoResponse) GetAdmin() []*IpAdminInfoResponseItem {
	if x != nil {
		return x.Admin
	}
	return nil
}

func (x *IpAdminInfoResponse) GetOper() []*PersonBase {
	if x != nil {
		return x.Oper
	}
	return nil
}

func (x *IpAdminInfoResponse) GetMachineRoom() []string {
	if x != nil {
		return x.MachineRoom
	}
	return nil
}

func (x *IpAdminInfoResponse) GetOperDepartment() []*OperDepartment {
	if x != nil {
		return x.OperDepartment
	}
	return nil
}

func (x *IpAdminInfoResponse) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

type OperDepartment struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Oper       string   `protobuf:"bytes,1,opt,name=oper,proto3" json:"oper"`             // 运维人员
	Department []string `protobuf:"bytes,2,rep,name=department,proto3" json:"department"` // 部门
}

func (x *OperDepartment) Reset() {
	*x = OperDepartment{}
	if protoimpl.UnsafeEnabled {
		mi := &file_merge_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OperDepartment) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OperDepartment) ProtoMessage() {}

func (x *OperDepartment) ProtoReflect() protoreflect.Message {
	mi := &file_merge_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OperDepartment.ProtoReflect.Descriptor instead.
func (*OperDepartment) Descriptor() ([]byte, []int) {
	return file_merge_proto_rawDescGZIP(), []int{44}
}

func (x *OperDepartment) GetOper() string {
	if x != nil {
		return x.Oper
	}
	return ""
}

func (x *OperDepartment) GetDepartment() []string {
	if x != nil {
		return x.Department
	}
	return nil
}

type IpAccountInfoResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *IpAccountInfoResponse) Reset() {
	*x = IpAccountInfoResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_merge_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IpAccountInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IpAccountInfoResponse) ProtoMessage() {}

func (x *IpAccountInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_merge_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IpAccountInfoResponse.ProtoReflect.Descriptor instead.
func (*IpAccountInfoResponse) Descriptor() ([]byte, []int) {
	return file_merge_proto_rawDescGZIP(), []int{45}
}

type IpPortInfoResponseItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Port     int32  `protobuf:"varint,1,opt,name=port,proto3" json:"port"`        // 端口
	Protocol string `protobuf:"bytes,2,opt,name=protocol,proto3" json:"protocol"` // 协议
	Status   int32  `protobuf:"varint,3,opt,name=status,proto3" json:"status"`    // 状态
	Url      string `protobuf:"bytes,4,opt,name=url,proto3" json:"url"`           // URL
	Domain   string `protobuf:"bytes,5,opt,name=domain,proto3" json:"domain"`     // Domain
	Title    string `protobuf:"bytes,6,opt,name=title,proto3" json:"title"`       // 网站标题
}

func (x *IpPortInfoResponseItem) Reset() {
	*x = IpPortInfoResponseItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_merge_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IpPortInfoResponseItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IpPortInfoResponseItem) ProtoMessage() {}

func (x *IpPortInfoResponseItem) ProtoReflect() protoreflect.Message {
	mi := &file_merge_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IpPortInfoResponseItem.ProtoReflect.Descriptor instead.
func (*IpPortInfoResponseItem) Descriptor() ([]byte, []int) {
	return file_merge_proto_rawDescGZIP(), []int{46}
}

func (x *IpPortInfoResponseItem) GetPort() int32 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *IpPortInfoResponseItem) GetProtocol() string {
	if x != nil {
		return x.Protocol
	}
	return ""
}

func (x *IpPortInfoResponseItem) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *IpPortInfoResponseItem) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *IpPortInfoResponseItem) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *IpPortInfoResponseItem) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

type IpPortInfoResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Port []*IpPortInfoResponseItem `protobuf:"bytes,1,rep,name=port,proto3" json:"port"` // 端口信息
}

func (x *IpPortInfoResponse) Reset() {
	*x = IpPortInfoResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_merge_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IpPortInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IpPortInfoResponse) ProtoMessage() {}

func (x *IpPortInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_merge_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IpPortInfoResponse.ProtoReflect.Descriptor instead.
func (*IpPortInfoResponse) Descriptor() ([]byte, []int) {
	return file_merge_proto_rawDescGZIP(), []int{47}
}

func (x *IpPortInfoResponse) GetPort() []*IpPortInfoResponseItem {
	if x != nil {
		return x.Port
	}
	return nil
}

type HostInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Maker           []string `protobuf:"bytes,1,rep,name=maker,proto3" json:"maker"`                                              // 厂商
	Model           []string `protobuf:"bytes,2,rep,name=model,proto3" json:"model"`                                              // 型号
	Sn              []string `protobuf:"bytes,3,rep,name=sn,proto3" json:"sn"`                                                    // 序列号
	Os              []string `protobuf:"bytes,4,rep,name=os,proto3" json:"os"`                                                    // 操作系统
	OsKernel        []string `protobuf:"bytes,5,rep,name=os_kernel,json=osKernel,proto3" json:"os_kernel"`                        // 操作系统内核
	MemorySize      []string `protobuf:"bytes,6,rep,name=memory_size,json=memorySize,proto3" json:"memory_size"`                  // 内存大小
	MemoryUsageRate []string `protobuf:"bytes,7,rep,name=memory_usage_rate,json=memoryUsageRate,proto3" json:"memory_usage_rate"` // 内存使用率
	CpuMaker        []string `protobuf:"bytes,8,rep,name=cpu_maker,json=cpuMaker,proto3" json:"cpu_maker"`                        // CPU厂商
	CpuBrand        []string `protobuf:"bytes,9,rep,name=cpu_brand,json=cpuBrand,proto3" json:"cpu_brand"`                        // CPU品牌
	CpuCount        []int32  `protobuf:"varint,10,rep,packed,name=cpu_count,json=cpuCount,proto3" json:"cpu_count"`               // CPU核数
	LoadAverage     []string `protobuf:"bytes,11,rep,name=load_average,json=loadAverage,proto3" json:"load_average"`              // 负载均衡
	DiskCount       []int32  `protobuf:"varint,12,rep,packed,name=disk_count,json=diskCount,proto3" json:"disk_count"`            // 硬盘数量
	DiskSize        []int32  `protobuf:"varint,13,rep,packed,name=disk_size,json=diskSize,proto3" json:"disk_size"`               // 硬盘大小
	DiskUsageRate   []string `protobuf:"bytes,14,rep,name=disk_usage_rate,json=diskUsageRate,proto3" json:"disk_usage_rate"`      // 硬盘使用率
}

func (x *HostInfo) Reset() {
	*x = HostInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_merge_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HostInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HostInfo) ProtoMessage() {}

func (x *HostInfo) ProtoReflect() protoreflect.Message {
	mi := &file_merge_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HostInfo.ProtoReflect.Descriptor instead.
func (*HostInfo) Descriptor() ([]byte, []int) {
	return file_merge_proto_rawDescGZIP(), []int{48}
}

func (x *HostInfo) GetMaker() []string {
	if x != nil {
		return x.Maker
	}
	return nil
}

func (x *HostInfo) GetModel() []string {
	if x != nil {
		return x.Model
	}
	return nil
}

func (x *HostInfo) GetSn() []string {
	if x != nil {
		return x.Sn
	}
	return nil
}

func (x *HostInfo) GetOs() []string {
	if x != nil {
		return x.Os
	}
	return nil
}

func (x *HostInfo) GetOsKernel() []string {
	if x != nil {
		return x.OsKernel
	}
	return nil
}

func (x *HostInfo) GetMemorySize() []string {
	if x != nil {
		return x.MemorySize
	}
	return nil
}

func (x *HostInfo) GetMemoryUsageRate() []string {
	if x != nil {
		return x.MemoryUsageRate
	}
	return nil
}

func (x *HostInfo) GetCpuMaker() []string {
	if x != nil {
		return x.CpuMaker
	}
	return nil
}

func (x *HostInfo) GetCpuBrand() []string {
	if x != nil {
		return x.CpuBrand
	}
	return nil
}

func (x *HostInfo) GetCpuCount() []int32 {
	if x != nil {
		return x.CpuCount
	}
	return nil
}

func (x *HostInfo) GetLoadAverage() []string {
	if x != nil {
		return x.LoadAverage
	}
	return nil
}

func (x *HostInfo) GetDiskCount() []int32 {
	if x != nil {
		return x.DiskCount
	}
	return nil
}

func (x *HostInfo) GetDiskSize() []int32 {
	if x != nil {
		return x.DiskSize
	}
	return nil
}

func (x *HostInfo) GetDiskUsageRate() []string {
	if x != nil {
		return x.DiskUsageRate
	}
	return nil
}

type IpSecurityInfoResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PocCount             int32 `protobuf:"varint,1,opt,name=poc_count,json=pocCount,proto3" json:"poc_count"`                                         // 安全漏洞数量
	IsCmdb               int32 `protobuf:"varint,2,opt,name=is_cmdb,json=isCmdb,proto3" json:"is_cmdb"`                                               // 是否加入cmdb
	IsHids               int32 `protobuf:"varint,3,opt,name=is_hids,json=isHids,proto3" json:"is_hids"`                                               // 是否加入hids
	IsJumpserver         int32 `protobuf:"varint,4,opt,name=is_jumpserver,json=isJumpserver,proto3" json:"is_jumpserver"`                             // 是否加入jumpserver
	HasAdminForBusiness  bool  `protobuf:"varint,5,opt,name=has_admin_for_business,json=hasAdminForBusiness,proto3" json:"has_admin_for_business"`    // 是否有业务管理员
	HasAdminForOperation bool  `protobuf:"varint,6,opt,name=has_admin_for_operation,json=hasAdminForOperation,proto3" json:"has_admin_for_operation"` // 是否有运维管理员
	HasDeparment         bool  `protobuf:"varint,7,opt,name=has_deparment,json=hasDeparment,proto3" json:"has_deparment"`                             // 是否有归属部门
	HasWaf               int32 `protobuf:"varint,8,opt,name=has_waf,json=hasWaf,proto3" json:"has_waf"`                                               // 是否有waf
}

func (x *IpSecurityInfoResponse) Reset() {
	*x = IpSecurityInfoResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_merge_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IpSecurityInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IpSecurityInfoResponse) ProtoMessage() {}

func (x *IpSecurityInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_merge_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IpSecurityInfoResponse.ProtoReflect.Descriptor instead.
func (*IpSecurityInfoResponse) Descriptor() ([]byte, []int) {
	return file_merge_proto_rawDescGZIP(), []int{49}
}

func (x *IpSecurityInfoResponse) GetPocCount() int32 {
	if x != nil {
		return x.PocCount
	}
	return 0
}

func (x *IpSecurityInfoResponse) GetIsCmdb() int32 {
	if x != nil {
		return x.IsCmdb
	}
	return 0
}

func (x *IpSecurityInfoResponse) GetIsHids() int32 {
	if x != nil {
		return x.IsHids
	}
	return 0
}

func (x *IpSecurityInfoResponse) GetIsJumpserver() int32 {
	if x != nil {
		return x.IsJumpserver
	}
	return 0
}

func (x *IpSecurityInfoResponse) GetHasAdminForBusiness() bool {
	if x != nil {
		return x.HasAdminForBusiness
	}
	return false
}

func (x *IpSecurityInfoResponse) GetHasAdminForOperation() bool {
	if x != nil {
		return x.HasAdminForOperation
	}
	return false
}

func (x *IpSecurityInfoResponse) GetHasDeparment() bool {
	if x != nil {
		return x.HasDeparment
	}
	return false
}

func (x *IpSecurityInfoResponse) GetHasWaf() int32 {
	if x != nil {
		return x.HasWaf
	}
	return 0
}

type IpTraceabilityBaseInfoResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AllSourceId    []uint64  `protobuf:"varint,1,rep,packed,name=all_source_id,json=allSourceId,proto3" json:"all_source_id"`   // 所有数据源
	SourceId       []uint64  `protobuf:"varint,2,rep,packed,name=source_id,json=sourceId,proto3" json:"source_id"`              // 最后一次融合生效的数据源
	AllSource      []*Source `protobuf:"bytes,3,rep,name=all_source,json=allSource,proto3" json:"all_source"`                   // 所有数据源信息
	Source         []*Source `protobuf:"bytes,4,rep,name=source,proto3" json:"source"`                                          // 最后一次融合生效的数据源信息
	IsExtratDevice int32     `protobuf:"varint,5,opt,name=is_extrat_device,json=isExtratDevice,proto3" json:"is_extrat_device"` // 是否提取了设备
	MergeCount     int32     `protobuf:"varint,6,opt,name=merge_count,json=mergeCount,proto3" json:"merge_count"`               // 合并次数
	FirstMergeTime string    `protobuf:"bytes,7,opt,name=first_merge_time,json=firstMergeTime,proto3" json:"first_merge_time"`  // 第一次合并时间
	LastMergeTime  string    `protobuf:"bytes,8,opt,name=last_merge_time,json=lastMergeTime,proto3" json:"last_merge_time"`     // 最后一次合并时间
}

func (x *IpTraceabilityBaseInfoResponse) Reset() {
	*x = IpTraceabilityBaseInfoResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_merge_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IpTraceabilityBaseInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IpTraceabilityBaseInfoResponse) ProtoMessage() {}

func (x *IpTraceabilityBaseInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_merge_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IpTraceabilityBaseInfoResponse.ProtoReflect.Descriptor instead.
func (*IpTraceabilityBaseInfoResponse) Descriptor() ([]byte, []int) {
	return file_merge_proto_rawDescGZIP(), []int{50}
}

func (x *IpTraceabilityBaseInfoResponse) GetAllSourceId() []uint64 {
	if x != nil {
		return x.AllSourceId
	}
	return nil
}

func (x *IpTraceabilityBaseInfoResponse) GetSourceId() []uint64 {
	if x != nil {
		return x.SourceId
	}
	return nil
}

func (x *IpTraceabilityBaseInfoResponse) GetAllSource() []*Source {
	if x != nil {
		return x.AllSource
	}
	return nil
}

func (x *IpTraceabilityBaseInfoResponse) GetSource() []*Source {
	if x != nil {
		return x.Source
	}
	return nil
}

func (x *IpTraceabilityBaseInfoResponse) GetIsExtratDevice() int32 {
	if x != nil {
		return x.IsExtratDevice
	}
	return 0
}

func (x *IpTraceabilityBaseInfoResponse) GetMergeCount() int32 {
	if x != nil {
		return x.MergeCount
	}
	return 0
}

func (x *IpTraceabilityBaseInfoResponse) GetFirstMergeTime() string {
	if x != nil {
		return x.FirstMergeTime
	}
	return ""
}

func (x *IpTraceabilityBaseInfoResponse) GetLastMergeTime() string {
	if x != nil {
		return x.LastMergeTime
	}
	return ""
}

type TraceabilityProcessInfoResponseItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id             string    `protobuf:"bytes,1,opt,name=id,proto3" json:"id"`                                                  // 过程ID
	Type           int32     `protobuf:"varint,2,opt,name=type,proto3" json:"type"`                                             // 过程类型
	Name           string    `protobuf:"bytes,3,opt,name=name,proto3" json:"name"`                                              // 过程名称
	Time           string    `protobuf:"bytes,4,opt,name=time,proto3" json:"time"`                                              // 时间
	Source         []*Source `protobuf:"bytes,5,rep,name=source,proto3" json:"source"`                                          // 数据源信息
	SourceId       []uint64  `protobuf:"varint,6,rep,packed,name=source_id,json=sourceId,proto3" json:"source_id"`              // 数据源ID
	TiggerSourceId uint64    `protobuf:"varint,7,opt,name=tigger_source_id,json=tiggerSourceId,proto3" json:"tigger_source_id"` // 触发源ID
	TiggerSource   *Source   `protobuf:"bytes,8,opt,name=tigger_source,json=tiggerSource,proto3" json:"tigger_source"`          // 触发源信息
}

func (x *TraceabilityProcessInfoResponseItem) Reset() {
	*x = TraceabilityProcessInfoResponseItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_merge_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TraceabilityProcessInfoResponseItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TraceabilityProcessInfoResponseItem) ProtoMessage() {}

func (x *TraceabilityProcessInfoResponseItem) ProtoReflect() protoreflect.Message {
	mi := &file_merge_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TraceabilityProcessInfoResponseItem.ProtoReflect.Descriptor instead.
func (*TraceabilityProcessInfoResponseItem) Descriptor() ([]byte, []int) {
	return file_merge_proto_rawDescGZIP(), []int{51}
}

func (x *TraceabilityProcessInfoResponseItem) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *TraceabilityProcessInfoResponseItem) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *TraceabilityProcessInfoResponseItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *TraceabilityProcessInfoResponseItem) GetTime() string {
	if x != nil {
		return x.Time
	}
	return ""
}

func (x *TraceabilityProcessInfoResponseItem) GetSource() []*Source {
	if x != nil {
		return x.Source
	}
	return nil
}

func (x *TraceabilityProcessInfoResponseItem) GetSourceId() []uint64 {
	if x != nil {
		return x.SourceId
	}
	return nil
}

func (x *TraceabilityProcessInfoResponseItem) GetTiggerSourceId() uint64 {
	if x != nil {
		return x.TiggerSourceId
	}
	return 0
}

func (x *TraceabilityProcessInfoResponseItem) GetTiggerSource() *Source {
	if x != nil {
		return x.TiggerSource
	}
	return nil
}

type TraceabilityProcessInfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id      string `protobuf:"bytes,1,opt,name=id,proto3" json:"id" form:"id" validate:"required" zh:"数据ID"`                            
	Page    int32  `protobuf:"varint,2,opt,name=page,proto3" json:"page" form:"page" validate:"required,gt=0" zh:"页数"`                       
	PerPage int32  `protobuf:"varint,3,opt,name=per_page,json=perPage,proto3" json:"per_page" form:"per_page" validate:"required,gt=0" zh:"条数"`  
}

func (x *TraceabilityProcessInfoRequest) Reset() {
	*x = TraceabilityProcessInfoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_merge_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TraceabilityProcessInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TraceabilityProcessInfoRequest) ProtoMessage() {}

func (x *TraceabilityProcessInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_merge_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TraceabilityProcessInfoRequest.ProtoReflect.Descriptor instead.
func (*TraceabilityProcessInfoRequest) Descriptor() ([]byte, []int) {
	return file_merge_proto_rawDescGZIP(), []int{52}
}

func (x *TraceabilityProcessInfoRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *TraceabilityProcessInfoRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *TraceabilityProcessInfoRequest) GetPerPage() int32 {
	if x != nil {
		return x.PerPage
	}
	return 0
}

type TraceabilityProcessInfoResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total       int64                                  `protobuf:"varint,1,opt,name=total,proto3" json:"total"`
	CurrentPage int32                                  `protobuf:"varint,2,opt,name=current_page,json=currentPage,proto3" json:"current_page"`
	Page        int32                                  `protobuf:"varint,3,opt,name=page,proto3" json:"page"`
	Process     []*TraceabilityProcessInfoResponseItem `protobuf:"bytes,4,rep,name=process,proto3" json:"process"` // 过程信息
}

func (x *TraceabilityProcessInfoResponse) Reset() {
	*x = TraceabilityProcessInfoResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_merge_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TraceabilityProcessInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TraceabilityProcessInfoResponse) ProtoMessage() {}

func (x *TraceabilityProcessInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_merge_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TraceabilityProcessInfoResponse.ProtoReflect.Descriptor instead.
func (*TraceabilityProcessInfoResponse) Descriptor() ([]byte, []int) {
	return file_merge_proto_rawDescGZIP(), []int{53}
}

func (x *TraceabilityProcessInfoResponse) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *TraceabilityProcessInfoResponse) GetCurrentPage() int32 {
	if x != nil {
		return x.CurrentPage
	}
	return 0
}

func (x *TraceabilityProcessInfoResponse) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *TraceabilityProcessInfoResponse) GetProcess() []*TraceabilityProcessInfoResponseItem {
	if x != nil {
		return x.Process
	}
	return nil
}

type TraceabilityDetailInfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          string                                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id" form:"id" validate:"required" zh:"数据ID"`                                       
	ProcessInfo []*TraceabilityProcessInfoResponseItem `protobuf:"bytes,2,rep,name=process_info,json=processInfo,proto3" json:"process_info" form:"process_info" validate:"required,min=1" zh:"过程ID"`  
}

func (x *TraceabilityDetailInfoRequest) Reset() {
	*x = TraceabilityDetailInfoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_merge_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TraceabilityDetailInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TraceabilityDetailInfoRequest) ProtoMessage() {}

func (x *TraceabilityDetailInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_merge_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TraceabilityDetailInfoRequest.ProtoReflect.Descriptor instead.
func (*TraceabilityDetailInfoRequest) Descriptor() ([]byte, []int) {
	return file_merge_proto_rawDescGZIP(), []int{54}
}

func (x *TraceabilityDetailInfoRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *TraceabilityDetailInfoRequest) GetProcessInfo() []*TraceabilityProcessInfoResponseItem {
	if x != nil {
		return x.ProcessInfo
	}
	return nil
}

type DeviceTraceabilityDetailInfoResponseItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OriginalData          []*TraceabilityDetailInfoResponseOriginalDataItem `protobuf:"bytes,1,rep,name=original_data,json=originalData,proto3" json:"original_data"`                                                                                                                          // 数据信息
	MergedData            *Device                                           `protobuf:"bytes,2,opt,name=merged_data,json=mergedData,proto3" json:"merged_data"`                                                                                                                                // 融合数据
	Strategies            []*TraceabilityDetailInfoResponseStrategyItem     `protobuf:"bytes,3,rep,name=strategies,proto3" json:"strategies"`                                                                                                                                                  // 策略信息
	AllSourceForMergeData map[string]*Source                                `protobuf:"bytes,4,rep,name=all_source_for_merge_data,json=allSourceForMergeData,proto3" json:"all_source_for_merge_data" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // 所有数据源信息，用于融合
	AllSourceForStrategy  map[string]*Source                                `protobuf:"bytes,5,rep,name=all_source_for_strategy,json=allSourceForStrategy,proto3" json:"all_source_for_strategy" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`      // 所有数据源信息，用于策略
	Type                  int32                                             `protobuf:"varint,6,opt,name=type,proto3" json:"type"`                                                                                                                                                             // 节点类型，原样返回参数中的值
	Id                    string                                            `protobuf:"bytes,7,opt,name=id,proto3" json:"id"`                                                                                                                                                                  // 数据ID, 原样返回参数中的值
	ListFields            []*MapStringString                                `protobuf:"bytes,8,rep,name=list_fields,json=listFields,proto3" json:"list_fields"`                                                                                                                                // 列表字段
}

func (x *DeviceTraceabilityDetailInfoResponseItem) Reset() {
	*x = DeviceTraceabilityDetailInfoResponseItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_merge_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeviceTraceabilityDetailInfoResponseItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceTraceabilityDetailInfoResponseItem) ProtoMessage() {}

func (x *DeviceTraceabilityDetailInfoResponseItem) ProtoReflect() protoreflect.Message {
	mi := &file_merge_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceTraceabilityDetailInfoResponseItem.ProtoReflect.Descriptor instead.
func (*DeviceTraceabilityDetailInfoResponseItem) Descriptor() ([]byte, []int) {
	return file_merge_proto_rawDescGZIP(), []int{55}
}

func (x *DeviceTraceabilityDetailInfoResponseItem) GetOriginalData() []*TraceabilityDetailInfoResponseOriginalDataItem {
	if x != nil {
		return x.OriginalData
	}
	return nil
}

func (x *DeviceTraceabilityDetailInfoResponseItem) GetMergedData() *Device {
	if x != nil {
		return x.MergedData
	}
	return nil
}

func (x *DeviceTraceabilityDetailInfoResponseItem) GetStrategies() []*TraceabilityDetailInfoResponseStrategyItem {
	if x != nil {
		return x.Strategies
	}
	return nil
}

func (x *DeviceTraceabilityDetailInfoResponseItem) GetAllSourceForMergeData() map[string]*Source {
	if x != nil {
		return x.AllSourceForMergeData
	}
	return nil
}

func (x *DeviceTraceabilityDetailInfoResponseItem) GetAllSourceForStrategy() map[string]*Source {
	if x != nil {
		return x.AllSourceForStrategy
	}
	return nil
}

func (x *DeviceTraceabilityDetailInfoResponseItem) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *DeviceTraceabilityDetailInfoResponseItem) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *DeviceTraceabilityDetailInfoResponseItem) GetListFields() []*MapStringString {
	if x != nil {
		return x.ListFields
	}
	return nil
}

type DeviceTraceabilityDetailInfoResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items []*DeviceTraceabilityDetailInfoResponseItem `protobuf:"bytes,1,rep,name=items,proto3" json:"items"` // 数据信息
}

func (x *DeviceTraceabilityDetailInfoResponse) Reset() {
	*x = DeviceTraceabilityDetailInfoResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_merge_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeviceTraceabilityDetailInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceTraceabilityDetailInfoResponse) ProtoMessage() {}

func (x *DeviceTraceabilityDetailInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_merge_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceTraceabilityDetailInfoResponse.ProtoReflect.Descriptor instead.
func (*DeviceTraceabilityDetailInfoResponse) Descriptor() ([]byte, []int) {
	return file_merge_proto_rawDescGZIP(), []int{56}
}

func (x *DeviceTraceabilityDetailInfoResponse) GetItems() []*DeviceTraceabilityDetailInfoResponseItem {
	if x != nil {
		return x.Items
	}
	return nil
}

type MapStringString struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Fields map[string]string `protobuf:"bytes,1,rep,name=fields,proto3" json:"fields" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *MapStringString) Reset() {
	*x = MapStringString{}
	if protoimpl.UnsafeEnabled {
		mi := &file_merge_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MapStringString) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MapStringString) ProtoMessage() {}

func (x *MapStringString) ProtoReflect() protoreflect.Message {
	mi := &file_merge_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MapStringString.ProtoReflect.Descriptor instead.
func (*MapStringString) Descriptor() ([]byte, []int) {
	return file_merge_proto_rawDescGZIP(), []int{57}
}

func (x *MapStringString) GetFields() map[string]string {
	if x != nil {
		return x.Fields
	}
	return nil
}

type TraceabilityDetailInfoResponseOriginalDataItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SourceId         string             `protobuf:"bytes,1,opt,name=source_id,json=sourceId,proto3" json:"source_id"`                           // 数据源 ID
	Source           *Source            `protobuf:"bytes,2,opt,name=source,proto3" json:"source"`                                               // 数据源信息
	Data             string             `protobuf:"bytes,3,opt,name=data,proto3" json:"data"`                                                   // 数据
	ListFields       []*MapStringString `protobuf:"bytes,4,rep,name=list_fields,json=listFields,proto3" json:"list_fields"`                     // 列表字段
	ListDetailFields []*MapStringString `protobuf:"bytes,5,rep,name=list_detail_fields,json=listDetailFields,proto3" json:"list_detail_fields"` // 列表详细字段
}

func (x *TraceabilityDetailInfoResponseOriginalDataItem) Reset() {
	*x = TraceabilityDetailInfoResponseOriginalDataItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_merge_proto_msgTypes[58]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TraceabilityDetailInfoResponseOriginalDataItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TraceabilityDetailInfoResponseOriginalDataItem) ProtoMessage() {}

func (x *TraceabilityDetailInfoResponseOriginalDataItem) ProtoReflect() protoreflect.Message {
	mi := &file_merge_proto_msgTypes[58]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TraceabilityDetailInfoResponseOriginalDataItem.ProtoReflect.Descriptor instead.
func (*TraceabilityDetailInfoResponseOriginalDataItem) Descriptor() ([]byte, []int) {
	return file_merge_proto_rawDescGZIP(), []int{58}
}

func (x *TraceabilityDetailInfoResponseOriginalDataItem) GetSourceId() string {
	if x != nil {
		return x.SourceId
	}
	return ""
}

func (x *TraceabilityDetailInfoResponseOriginalDataItem) GetSource() *Source {
	if x != nil {
		return x.Source
	}
	return nil
}

func (x *TraceabilityDetailInfoResponseOriginalDataItem) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

func (x *TraceabilityDetailInfoResponseOriginalDataItem) GetListFields() []*MapStringString {
	if x != nil {
		return x.ListFields
	}
	return nil
}

func (x *TraceabilityDetailInfoResponseOriginalDataItem) GetListDetailFields() []*MapStringString {
	if x != nil {
		return x.ListDetailFields
	}
	return nil
}

type Business struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	System string `protobuf:"bytes,1,opt,name=system,proto3" json:"system"` // 业务系统名称
	Owner  string `protobuf:"bytes,2,opt,name=owner,proto3" json:"owner"`   // 业务系统负责人
}

func (x *Business) Reset() {
	*x = Business{}
	if protoimpl.UnsafeEnabled {
		mi := &file_merge_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Business) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Business) ProtoMessage() {}

func (x *Business) ProtoReflect() protoreflect.Message {
	mi := &file_merge_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Business.ProtoReflect.Descriptor instead.
func (*Business) Descriptor() ([]byte, []int) {
	return file_merge_proto_rawDescGZIP(), []int{59}
}

func (x *Business) GetSystem() string {
	if x != nil {
		return x.System
	}
	return ""
}

func (x *Business) GetOwner() string {
	if x != nil {
		return x.Owner
	}
	return ""
}

type PortInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Port     int32  `protobuf:"varint,1,opt,name=port,proto3" json:"port"`        // 端口
	Protocol string `protobuf:"bytes,2,opt,name=protocol,proto3" json:"protocol"` // 协议
	Status   int32  `protobuf:"varint,3,opt,name=status,proto3" json:"status"`    // 状态
	Url      string `protobuf:"bytes,4,opt,name=url,proto3" json:"url"`           // URL
	Domain   string `protobuf:"bytes,5,opt,name=domain,proto3" json:"domain"`     // Domain
	Title    string `protobuf:"bytes,6,opt,name=title,proto3" json:"title"`       // 网站标题
}

func (x *PortInfo) Reset() {
	*x = PortInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_merge_proto_msgTypes[60]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PortInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PortInfo) ProtoMessage() {}

func (x *PortInfo) ProtoReflect() protoreflect.Message {
	mi := &file_merge_proto_msgTypes[60]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PortInfo.ProtoReflect.Descriptor instead.
func (*PortInfo) Descriptor() ([]byte, []int) {
	return file_merge_proto_rawDescGZIP(), []int{60}
}

func (x *PortInfo) GetPort() int32 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *PortInfo) GetProtocol() string {
	if x != nil {
		return x.Protocol
	}
	return ""
}

func (x *PortInfo) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *PortInfo) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *PortInfo) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *PortInfo) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

type ListPortInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ports []*PortInfo `protobuf:"bytes,1,rep,name=ports,proto3" json:"ports"` // 端口信息
}

func (x *ListPortInfo) Reset() {
	*x = ListPortInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_merge_proto_msgTypes[61]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPortInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPortInfo) ProtoMessage() {}

func (x *ListPortInfo) ProtoReflect() protoreflect.Message {
	mi := &file_merge_proto_msgTypes[61]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPortInfo.ProtoReflect.Descriptor instead.
func (*ListPortInfo) Descriptor() ([]byte, []int) {
	return file_merge_proto_rawDescGZIP(), []int{61}
}

func (x *ListPortInfo) GetPorts() []*PortInfo {
	if x != nil {
		return x.Ports
	}
	return nil
}

type Asset struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                    string                              `protobuf:"bytes,1,opt,name=id,proto3" json:"id"`                                                                                                                                                                 // ID
	AreaId                int32                               `protobuf:"varint,2,opt,name=area_id,json=areaId,proto3" json:"area_id"`                                                                                                                                          // 区域ID
	ProcessIds            []string                            `protobuf:"bytes,3,rep,name=process_ids,json=processIds,proto3" json:"process_ids"`                                                                                                                               // 过程表ids
	SourceIds             []uint64                            `protobuf:"varint,4,rep,packed,name=source_ids,json=sourceIds,proto3" json:"source_ids"`                                                                                                                          // 数据源ids
	NodeIds               []uint64                            `protobuf:"varint,5,rep,packed,name=node_ids,json=nodeIds,proto3" json:"node_ids"`                                                                                                                                // 节点ids
	TaskDataIds           []string                            `protobuf:"bytes,6,rep,name=task_data_ids,json=taskDataIds,proto3" json:"task_data_ids"`                                                                                                                          // 资产任务ids
	AllSourceIds          []uint64                            `protobuf:"varint,7,rep,packed,name=all_source_ids,json=allSourceIds,proto3" json:"all_source_ids"`                                                                                                               // 所有数据源ids
	AllNodeIds            []uint64                            `protobuf:"varint,8,rep,packed,name=all_node_ids,json=allNodeIds,proto3" json:"all_node_ids"`                                                                                                                     // 所有节点ids
	AllTaskDataIds        []string                            `protobuf:"bytes,9,rep,name=all_task_data_ids,json=allTaskDataIds,proto3" json:"all_task_data_ids"`                                                                                                               // 所有任务ids
	AllProcessIds         []string                            `protobuf:"bytes,10,rep,name=all_process_ids,json=allProcessIds,proto3" json:"all_process_ids"`                                                                                                                   // 所有过程ids
	Ip                    string                              `protobuf:"bytes,11,opt,name=ip,proto3" json:"ip"`                                                                                                                                                                // IP
	IpType                int32                               `protobuf:"varint,12,opt,name=ip_type,json=ipType,proto3" json:"ip_type"`                                                                                                                                         // IP类型
	IpSegment             []string                            `protobuf:"bytes,13,rep,name=ip_segment,json=ipSegment,proto3" json:"ip_segment"`                                                                                                                                 // IP段
	IpSegmentSource       map[string]string                   `protobuf:"bytes,14,rep,name=ip_segment_source,json=ipSegmentSource,proto3" json:"ip_segment_source" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`                     // IP段来源
	HostName              []string                            `protobuf:"bytes,15,rep,name=host_name,json=hostName,proto3" json:"host_name"`                                                                                                                                    // 主机名
	HostNameSource        map[string]string                   `protobuf:"bytes,16,rep,name=host_name_source,json=hostNameSource,proto3" json:"host_name_source" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`                        // 主机名来源
	EthName               []string                            `protobuf:"bytes,17,rep,name=eth_name,json=ethName,proto3" json:"eth_name"`                                                                                                                                       // 网卡名
	EthNameSource         map[string]string                   `protobuf:"bytes,18,rep,name=eth_name_source,json=ethNameSource,proto3" json:"eth_name_source" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`                           // 网卡名来源
	Os                    []string                            `protobuf:"bytes,19,rep,name=os,proto3" json:"os"`                                                                                                                                                                // 操作系统
	OsSource              map[string]string                   `protobuf:"bytes,20,rep,name=os_source,json=osSource,proto3" json:"os_source" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`                                            // 操作系统来源
	Kernel                []string                            `protobuf:"bytes,21,rep,name=kernel,proto3" json:"kernel"`                                                                                                                                                        // 内核
	KernelSource          map[string]string                   `protobuf:"bytes,22,rep,name=kernel_source,json=kernelSource,proto3" json:"kernel_source" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`                                // 内核来源
	Model                 []string                            `protobuf:"bytes,25,rep,name=model,proto3" json:"model"`                                                                                                                                                          // 型号
	ModelSource           map[string]string                   `protobuf:"bytes,26,rep,name=model_source,json=modelSource,proto3" json:"model_source" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`                                   // 型号来源
	Maker                 []string                            `protobuf:"bytes,27,rep,name=maker,proto3" json:"maker"`                                                                                                                                                          // 制造商
	MakerSource           map[string]string                   `protobuf:"bytes,28,rep,name=maker_source,json=makerSource,proto3" json:"maker_source" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`                                   // 制造商来源
	Sn                    []string                            `protobuf:"bytes,29,rep,name=sn,proto3" json:"sn"`                                                                                                                                                                // 序列号
	SnSource              map[string]string                   `protobuf:"bytes,30,rep,name=sn_source,json=snSource,proto3" json:"sn_source" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`                                            // 序列号来源
	Mac                   []string                            `protobuf:"bytes,31,rep,name=mac,proto3" json:"mac"`                                                                                                                                                              // MAC地址
	MacSource             map[string]string                   `protobuf:"bytes,32,rep,name=mac_source,json=macSource,proto3" json:"mac_source" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`                                         // MAC地址来源
	Product               []string                            `protobuf:"bytes,33,rep,name=product,proto3" json:"product"`                                                                                                                                                      // 组件
	ProductSource         map[string]string                   `protobuf:"bytes,34,rep,name=product_source,json=productSource,proto3" json:"product_source" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`                             // 组件来源
	Business              []*IpAdminInfoResponseItem          `protobuf:"bytes,35,rep,name=business,proto3" json:"business"`                                                                                                                                                    // 业务系统
	BusinessSystemSource  map[string]string                   `protobuf:"bytes,36,rep,name=business_system_source,json=businessSystemSource,proto3" json:"business_system_source" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`      // 业务系统来源
	BusinessOwnerSource   map[string]string                   `protobuf:"bytes,37,rep,name=business_owner_source,json=businessOwnerSource,proto3" json:"business_owner_source" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`         // 业务系统负责人来源
	Oper                  []*PersonBase                       `protobuf:"bytes,38,rep,name=oper,proto3" json:"oper"`                                                                                                                                                            // 运维人员
	OperSource            map[string]string                   `protobuf:"bytes,39,rep,name=oper_source,json=operSource,proto3" json:"oper_source" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`                                      // 运维人员来源
	MachineRoom           []string                            `protobuf:"bytes,40,rep,name=machine_room,json=machineRoom,proto3" json:"machine_room"`                                                                                                                           // 机房
	MachineRoomSource     map[string]string                   `protobuf:"bytes,41,rep,name=machine_room_source,json=machineRoomSource,proto3" json:"machine_room_source" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`               // 机房来源
	Status                int32                               `protobuf:"varint,42,opt,name=status,proto3" json:"status"`                                                                                                                                                       // 状态
	StatusSource          map[string]int32                    `protobuf:"bytes,43,rep,name=status_source,json=statusSource,proto3" json:"status_source" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`                               // 状态来源
	Ports                 []*PortInfo                         `protobuf:"bytes,44,rep,name=ports,proto3" json:"ports"`                                                                                                                                                          // 端口
	PortsSource           map[string]*ListPortInfo            `protobuf:"bytes,45,rep,name=ports_source,json=portsSource,proto3" json:"ports_source" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`                                   // 端口来源
	MemorySize            []string                            `protobuf:"bytes,46,rep,name=memory_size,json=memorySize,proto3" json:"memory_size"`                                                                                                                              // 内存大小
	MemorySizeSource      map[string]string                   `protobuf:"bytes,47,rep,name=memory_size_source,json=memorySizeSource,proto3" json:"memory_size_source" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`                  // 内存大小来源
	MemoryUsageRate       []string                            `protobuf:"bytes,48,rep,name=memory_usage_rate,json=memoryUsageRate,proto3" json:"memory_usage_rate"`                                                                                                             // 内存使用率
	MemoryUsageRateSource map[string]string                   `protobuf:"bytes,49,rep,name=memory_usage_rate_source,json=memoryUsageRateSource,proto3" json:"memory_usage_rate_source" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // 内存使用率来源
	CpuMaker              []string                            `protobuf:"bytes,50,rep,name=cpu_maker,json=cpuMaker,proto3" json:"cpu_maker"`                                                                                                                                    // CPU厂商
	CpuMakerSource        map[string]string                   `protobuf:"bytes,51,rep,name=cpu_maker_source,json=cpuMakerSource,proto3" json:"cpu_maker_source" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`                        // CPU厂商来源
	CpuBrand              []string                            `protobuf:"bytes,52,rep,name=cpu_brand,json=cpuBrand,proto3" json:"cpu_brand"`                                                                                                                                    // CPU品牌
	CpuBrandSource        map[string]string                   `protobuf:"bytes,53,rep,name=cpu_brand_source,json=cpuBrandSource,proto3" json:"cpu_brand_source" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`                        // CPU品牌来源
	CpuCount              []int32                             `protobuf:"varint,54,rep,packed,name=cpu_count,json=cpuCount,proto3" json:"cpu_count"`                                                                                                                            // CPU数量
	CpuCountSource        map[string]int32                    `protobuf:"bytes,55,rep,name=cpu_count_source,json=cpuCountSource,proto3" json:"cpu_count_source" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`                       // CPU数量来源
	DiskCount             []int32                             `protobuf:"varint,56,rep,packed,name=disk_count,json=diskCount,proto3" json:"disk_count"`                                                                                                                         // 磁盘数量
	DiskCountSource       map[string]int32                    `protobuf:"bytes,57,rep,name=disk_count_source,json=diskCountSource,proto3" json:"disk_count_source" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`                    // 磁盘数量来源
	DiskSize              []int32                             `protobuf:"varint,58,rep,packed,name=disk_size,json=diskSize,proto3" json:"disk_size"`                                                                                                                            // 磁盘大小
	DiskSizeSource        map[string]int32                    `protobuf:"bytes,59,rep,name=disk_size_source,json=diskSizeSource,proto3" json:"disk_size_source" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`                       // 磁盘大小来源
	DiskUsageRate         []string                            `protobuf:"bytes,60,rep,name=disk_usage_rate,json=diskUsageRate,proto3" json:"disk_usage_rate"`                                                                                                                   // 磁盘使用率
	DiskUsageRateSource   map[string]string                   `protobuf:"bytes,61,rep,name=disk_usage_rate_source,json=diskUsageRateSource,proto3" json:"disk_usage_rate_source" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`       // 磁盘使用率来源
	LoadAverage           []string                            `protobuf:"bytes,62,rep,name=load_average,json=loadAverage,proto3" json:"load_average"`                                                                                                                           // 负载
	LoadAverageSource     map[string]string                   `protobuf:"bytes,63,rep,name=load_average_source,json=loadAverageSource,proto3" json:"load_average_source" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`               // 负载来源
	NetworkType           int32                               `protobuf:"varint,66,opt,name=network_type,json=networkType,proto3" json:"network_type"`                                                                                                                          // 网络类型
	DeletedAt             string                              `protobuf:"bytes,68,opt,name=deleted_at,json=deletedAt,proto3" json:"deleted_at"`                                                                                                                                 // 删除时间
	PurgedAt              string                              `protobuf:"bytes,69,opt,name=purged_at,json=purgedAt,proto3" json:"purged_at"`                                                                                                                                    // 彻底删除时间
	CreatedAt             string                              `protobuf:"bytes,70,opt,name=created_at,json=createdAt,proto3" json:"created_at"`                                                                                                                                 // 创建时间
	UpdatedAt             string                              `protobuf:"bytes,71,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at"`                                                                                                                                 // 更新时间
	IsDeviceExtracted     int32                               `protobuf:"varint,72,opt,name=is_device_extracted,json=isDeviceExtracted,proto3" json:"is_device_extracted"`                                                                                                      // 是否参与了实体提取
	MergeCount            int32                               `protobuf:"varint,73,opt,name=merge_count,json=mergeCount,proto3" json:"merge_count"`                                                                                                                             // 融合次数
	IpTypeText            string                              `protobuf:"bytes,74,opt,name=ip_type_text,json=ipTypeText,proto3" json:"ip_type_text"`                                                                                                                            // IP类型文本
	NetworkTypeText       string                              `protobuf:"bytes,75,opt,name=network_type_text,json=networkTypeText,proto3" json:"network_type_text"`                                                                                                             // 网络类型文本
	Area                  *Area                               `protobuf:"bytes,76,opt,name=area,proto3" json:"area"`                                                                                                                                                            // 区域
	Source                []*Source                           `protobuf:"bytes,77,rep,name=source,proto3" json:"source"`                                                                                                                                                        // 数据源
	AllSource             []*Source                           `protobuf:"bytes,78,rep,name=all_source,json=allSource,proto3" json:"all_source"`                                                                                                                                 // 所有数据源
	RuleInfos             []*RuleInfo                         `protobuf:"bytes,79,rep,name=rule_infos,json=ruleInfos,proto3" json:"rule_infos"`                                                                                                                                 // 组件
	Tags                  []string                            `protobuf:"bytes,80,rep,name=tags,proto3" json:"tags"`                                                                                                                                                            // 标签信息
	BusinessSource        map[string]*IpAdminInfoResponseList `protobuf:"bytes,81,rep,name=business_source,json=businessSource,proto3" json:"business_source" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`                          // 业务系统来源
}

func (x *Asset) Reset() {
	*x = Asset{}
	if protoimpl.UnsafeEnabled {
		mi := &file_merge_proto_msgTypes[62]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Asset) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Asset) ProtoMessage() {}

func (x *Asset) ProtoReflect() protoreflect.Message {
	mi := &file_merge_proto_msgTypes[62]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Asset.ProtoReflect.Descriptor instead.
func (*Asset) Descriptor() ([]byte, []int) {
	return file_merge_proto_rawDescGZIP(), []int{62}
}

func (x *Asset) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Asset) GetAreaId() int32 {
	if x != nil {
		return x.AreaId
	}
	return 0
}

func (x *Asset) GetProcessIds() []string {
	if x != nil {
		return x.ProcessIds
	}
	return nil
}

func (x *Asset) GetSourceIds() []uint64 {
	if x != nil {
		return x.SourceIds
	}
	return nil
}

func (x *Asset) GetNodeIds() []uint64 {
	if x != nil {
		return x.NodeIds
	}
	return nil
}

func (x *Asset) GetTaskDataIds() []string {
	if x != nil {
		return x.TaskDataIds
	}
	return nil
}

func (x *Asset) GetAllSourceIds() []uint64 {
	if x != nil {
		return x.AllSourceIds
	}
	return nil
}

func (x *Asset) GetAllNodeIds() []uint64 {
	if x != nil {
		return x.AllNodeIds
	}
	return nil
}

func (x *Asset) GetAllTaskDataIds() []string {
	if x != nil {
		return x.AllTaskDataIds
	}
	return nil
}

func (x *Asset) GetAllProcessIds() []string {
	if x != nil {
		return x.AllProcessIds
	}
	return nil
}

func (x *Asset) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *Asset) GetIpType() int32 {
	if x != nil {
		return x.IpType
	}
	return 0
}

func (x *Asset) GetIpSegment() []string {
	if x != nil {
		return x.IpSegment
	}
	return nil
}

func (x *Asset) GetIpSegmentSource() map[string]string {
	if x != nil {
		return x.IpSegmentSource
	}
	return nil
}

func (x *Asset) GetHostName() []string {
	if x != nil {
		return x.HostName
	}
	return nil
}

func (x *Asset) GetHostNameSource() map[string]string {
	if x != nil {
		return x.HostNameSource
	}
	return nil
}

func (x *Asset) GetEthName() []string {
	if x != nil {
		return x.EthName
	}
	return nil
}

func (x *Asset) GetEthNameSource() map[string]string {
	if x != nil {
		return x.EthNameSource
	}
	return nil
}

func (x *Asset) GetOs() []string {
	if x != nil {
		return x.Os
	}
	return nil
}

func (x *Asset) GetOsSource() map[string]string {
	if x != nil {
		return x.OsSource
	}
	return nil
}

func (x *Asset) GetKernel() []string {
	if x != nil {
		return x.Kernel
	}
	return nil
}

func (x *Asset) GetKernelSource() map[string]string {
	if x != nil {
		return x.KernelSource
	}
	return nil
}

func (x *Asset) GetModel() []string {
	if x != nil {
		return x.Model
	}
	return nil
}

func (x *Asset) GetModelSource() map[string]string {
	if x != nil {
		return x.ModelSource
	}
	return nil
}

func (x *Asset) GetMaker() []string {
	if x != nil {
		return x.Maker
	}
	return nil
}

func (x *Asset) GetMakerSource() map[string]string {
	if x != nil {
		return x.MakerSource
	}
	return nil
}

func (x *Asset) GetSn() []string {
	if x != nil {
		return x.Sn
	}
	return nil
}

func (x *Asset) GetSnSource() map[string]string {
	if x != nil {
		return x.SnSource
	}
	return nil
}

func (x *Asset) GetMac() []string {
	if x != nil {
		return x.Mac
	}
	return nil
}

func (x *Asset) GetMacSource() map[string]string {
	if x != nil {
		return x.MacSource
	}
	return nil
}

func (x *Asset) GetProduct() []string {
	if x != nil {
		return x.Product
	}
	return nil
}

func (x *Asset) GetProductSource() map[string]string {
	if x != nil {
		return x.ProductSource
	}
	return nil
}

func (x *Asset) GetBusiness() []*IpAdminInfoResponseItem {
	if x != nil {
		return x.Business
	}
	return nil
}

func (x *Asset) GetBusinessSystemSource() map[string]string {
	if x != nil {
		return x.BusinessSystemSource
	}
	return nil
}

func (x *Asset) GetBusinessOwnerSource() map[string]string {
	if x != nil {
		return x.BusinessOwnerSource
	}
	return nil
}

func (x *Asset) GetOper() []*PersonBase {
	if x != nil {
		return x.Oper
	}
	return nil
}

func (x *Asset) GetOperSource() map[string]string {
	if x != nil {
		return x.OperSource
	}
	return nil
}

func (x *Asset) GetMachineRoom() []string {
	if x != nil {
		return x.MachineRoom
	}
	return nil
}

func (x *Asset) GetMachineRoomSource() map[string]string {
	if x != nil {
		return x.MachineRoomSource
	}
	return nil
}

func (x *Asset) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *Asset) GetStatusSource() map[string]int32 {
	if x != nil {
		return x.StatusSource
	}
	return nil
}

func (x *Asset) GetPorts() []*PortInfo {
	if x != nil {
		return x.Ports
	}
	return nil
}

func (x *Asset) GetPortsSource() map[string]*ListPortInfo {
	if x != nil {
		return x.PortsSource
	}
	return nil
}

func (x *Asset) GetMemorySize() []string {
	if x != nil {
		return x.MemorySize
	}
	return nil
}

func (x *Asset) GetMemorySizeSource() map[string]string {
	if x != nil {
		return x.MemorySizeSource
	}
	return nil
}

func (x *Asset) GetMemoryUsageRate() []string {
	if x != nil {
		return x.MemoryUsageRate
	}
	return nil
}

func (x *Asset) GetMemoryUsageRateSource() map[string]string {
	if x != nil {
		return x.MemoryUsageRateSource
	}
	return nil
}

func (x *Asset) GetCpuMaker() []string {
	if x != nil {
		return x.CpuMaker
	}
	return nil
}

func (x *Asset) GetCpuMakerSource() map[string]string {
	if x != nil {
		return x.CpuMakerSource
	}
	return nil
}

func (x *Asset) GetCpuBrand() []string {
	if x != nil {
		return x.CpuBrand
	}
	return nil
}

func (x *Asset) GetCpuBrandSource() map[string]string {
	if x != nil {
		return x.CpuBrandSource
	}
	return nil
}

func (x *Asset) GetCpuCount() []int32 {
	if x != nil {
		return x.CpuCount
	}
	return nil
}

func (x *Asset) GetCpuCountSource() map[string]int32 {
	if x != nil {
		return x.CpuCountSource
	}
	return nil
}

func (x *Asset) GetDiskCount() []int32 {
	if x != nil {
		return x.DiskCount
	}
	return nil
}

func (x *Asset) GetDiskCountSource() map[string]int32 {
	if x != nil {
		return x.DiskCountSource
	}
	return nil
}

func (x *Asset) GetDiskSize() []int32 {
	if x != nil {
		return x.DiskSize
	}
	return nil
}

func (x *Asset) GetDiskSizeSource() map[string]int32 {
	if x != nil {
		return x.DiskSizeSource
	}
	return nil
}

func (x *Asset) GetDiskUsageRate() []string {
	if x != nil {
		return x.DiskUsageRate
	}
	return nil
}

func (x *Asset) GetDiskUsageRateSource() map[string]string {
	if x != nil {
		return x.DiskUsageRateSource
	}
	return nil
}

func (x *Asset) GetLoadAverage() []string {
	if x != nil {
		return x.LoadAverage
	}
	return nil
}

func (x *Asset) GetLoadAverageSource() map[string]string {
	if x != nil {
		return x.LoadAverageSource
	}
	return nil
}

func (x *Asset) GetNetworkType() int32 {
	if x != nil {
		return x.NetworkType
	}
	return 0
}

func (x *Asset) GetDeletedAt() string {
	if x != nil {
		return x.DeletedAt
	}
	return ""
}

func (x *Asset) GetPurgedAt() string {
	if x != nil {
		return x.PurgedAt
	}
	return ""
}

func (x *Asset) GetCreatedAt() string {
	if x != nil {
		return x.CreatedAt
	}
	return ""
}

func (x *Asset) GetUpdatedAt() string {
	if x != nil {
		return x.UpdatedAt
	}
	return ""
}

func (x *Asset) GetIsDeviceExtracted() int32 {
	if x != nil {
		return x.IsDeviceExtracted
	}
	return 0
}

func (x *Asset) GetMergeCount() int32 {
	if x != nil {
		return x.MergeCount
	}
	return 0
}

func (x *Asset) GetIpTypeText() string {
	if x != nil {
		return x.IpTypeText
	}
	return ""
}

func (x *Asset) GetNetworkTypeText() string {
	if x != nil {
		return x.NetworkTypeText
	}
	return ""
}

func (x *Asset) GetArea() *Area {
	if x != nil {
		return x.Area
	}
	return nil
}

func (x *Asset) GetSource() []*Source {
	if x != nil {
		return x.Source
	}
	return nil
}

func (x *Asset) GetAllSource() []*Source {
	if x != nil {
		return x.AllSource
	}
	return nil
}

func (x *Asset) GetRuleInfos() []*RuleInfo {
	if x != nil {
		return x.RuleInfos
	}
	return nil
}

func (x *Asset) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *Asset) GetBusinessSource() map[string]*IpAdminInfoResponseList {
	if x != nil {
		return x.BusinessSource
	}
	return nil
}

type ListAsset struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Assets []*Asset `protobuf:"bytes,1,rep,name=assets,proto3" json:"assets"` // 资产列表
}

func (x *ListAsset) Reset() {
	*x = ListAsset{}
	if protoimpl.UnsafeEnabled {
		mi := &file_merge_proto_msgTypes[63]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAsset) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAsset) ProtoMessage() {}

func (x *ListAsset) ProtoReflect() protoreflect.Message {
	mi := &file_merge_proto_msgTypes[63]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAsset.ProtoReflect.Descriptor instead.
func (*ListAsset) Descriptor() ([]byte, []int) {
	return file_merge_proto_rawDescGZIP(), []int{63}
}

func (x *ListAsset) GetAssets() []*Asset {
	if x != nil {
		return x.Assets
	}
	return nil
}

type TraceabilityDetailInfoResponseStrategyItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BusinessType    string           `protobuf:"bytes,1,opt,name=business_type,json=businessType,proto3" json:"business_type"`                                                                                                // 业务类型
	FieldName       string           `protobuf:"bytes,2,opt,name=field_name,json=fieldName,proto3" json:"field_name"`                                                                                                         // 字段名称
	DisplayName     string           `protobuf:"bytes,3,opt,name=display_name,json=displayName,proto3" json:"display_name"`                                                                                                   // 字段显示名称
	SourcePriority  map[string]int32 `protobuf:"bytes,4,rep,name=source_priority,json=sourcePriority,proto3" json:"source_priority" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"` // 源优先级
	UntrustedSource []string         `protobuf:"bytes,5,rep,name=untrusted_source,json=untrustedSource,proto3" json:"untrusted_source"`                                                                                       // 不可信源
	Version         int64            `protobuf:"varint,6,opt,name=version,proto3" json:"version"`                                                                                                                             // 版本
}

func (x *TraceabilityDetailInfoResponseStrategyItem) Reset() {
	*x = TraceabilityDetailInfoResponseStrategyItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_merge_proto_msgTypes[64]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TraceabilityDetailInfoResponseStrategyItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TraceabilityDetailInfoResponseStrategyItem) ProtoMessage() {}

func (x *TraceabilityDetailInfoResponseStrategyItem) ProtoReflect() protoreflect.Message {
	mi := &file_merge_proto_msgTypes[64]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TraceabilityDetailInfoResponseStrategyItem.ProtoReflect.Descriptor instead.
func (*TraceabilityDetailInfoResponseStrategyItem) Descriptor() ([]byte, []int) {
	return file_merge_proto_rawDescGZIP(), []int{64}
}

func (x *TraceabilityDetailInfoResponseStrategyItem) GetBusinessType() string {
	if x != nil {
		return x.BusinessType
	}
	return ""
}

func (x *TraceabilityDetailInfoResponseStrategyItem) GetFieldName() string {
	if x != nil {
		return x.FieldName
	}
	return ""
}

func (x *TraceabilityDetailInfoResponseStrategyItem) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *TraceabilityDetailInfoResponseStrategyItem) GetSourcePriority() map[string]int32 {
	if x != nil {
		return x.SourcePriority
	}
	return nil
}

func (x *TraceabilityDetailInfoResponseStrategyItem) GetUntrustedSource() []string {
	if x != nil {
		return x.UntrustedSource
	}
	return nil
}

func (x *TraceabilityDetailInfoResponseStrategyItem) GetVersion() int64 {
	if x != nil {
		return x.Version
	}
	return 0
}

type TraceabilityDetailInfoResponseFieldValInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FieldName   string   `protobuf:"bytes,1,opt,name=field_name,json=fieldName,proto3" json:"field_name"`         // 字段名称
	SourceIds   []int64  `protobuf:"varint,2,rep,packed,name=source_ids,json=sourceIds,proto3" json:"source_ids"` // 字段采信数据源ID
	NodeIds     []int64  `protobuf:"varint,3,rep,packed,name=node_ids,json=nodeIds,proto3" json:"node_ids"`       // 字段采信节点ID
	TaskDataIds []string `protobuf:"bytes,4,rep,name=task_data_ids,json=taskDataIds,proto3" json:"task_data_ids"` // 字段采信任务ID
	ProcessIds  []string `protobuf:"bytes,5,rep,name=process_ids,json=processIds,proto3" json:"process_ids"`      // 字段采信过程ID
}

func (x *TraceabilityDetailInfoResponseFieldValInfo) Reset() {
	*x = TraceabilityDetailInfoResponseFieldValInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_merge_proto_msgTypes[65]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TraceabilityDetailInfoResponseFieldValInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TraceabilityDetailInfoResponseFieldValInfo) ProtoMessage() {}

func (x *TraceabilityDetailInfoResponseFieldValInfo) ProtoReflect() protoreflect.Message {
	mi := &file_merge_proto_msgTypes[65]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TraceabilityDetailInfoResponseFieldValInfo.ProtoReflect.Descriptor instead.
func (*TraceabilityDetailInfoResponseFieldValInfo) Descriptor() ([]byte, []int) {
	return file_merge_proto_rawDescGZIP(), []int{65}
}

func (x *TraceabilityDetailInfoResponseFieldValInfo) GetFieldName() string {
	if x != nil {
		return x.FieldName
	}
	return ""
}

func (x *TraceabilityDetailInfoResponseFieldValInfo) GetSourceIds() []int64 {
	if x != nil {
		return x.SourceIds
	}
	return nil
}

func (x *TraceabilityDetailInfoResponseFieldValInfo) GetNodeIds() []int64 {
	if x != nil {
		return x.NodeIds
	}
	return nil
}

func (x *TraceabilityDetailInfoResponseFieldValInfo) GetTaskDataIds() []string {
	if x != nil {
		return x.TaskDataIds
	}
	return nil
}

func (x *TraceabilityDetailInfoResponseFieldValInfo) GetProcessIds() []string {
	if x != nil {
		return x.ProcessIds
	}
	return nil
}

type IpTraceabilityDetailInfoResponseItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OriginalData          []*TraceabilityDetailInfoResponseOriginalDataItem `protobuf:"bytes,1,rep,name=original_data,json=originalData,proto3" json:"original_data"`                                                                                                                          // 数据信息
	MergedData            *Asset                                            `protobuf:"bytes,2,opt,name=merged_data,json=mergedData,proto3" json:"merged_data"`                                                                                                                                // 融合数据
	Strategies            []*TraceabilityDetailInfoResponseStrategyItem     `protobuf:"bytes,3,rep,name=strategies,proto3" json:"strategies"`                                                                                                                                                  // 策略信息
	AllSourceForMergeData map[string]*Source                                `protobuf:"bytes,4,rep,name=all_source_for_merge_data,json=allSourceForMergeData,proto3" json:"all_source_for_merge_data" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // 所有数据源信息，用于融合
	AllSourceForStrategy  map[string]*Source                                `protobuf:"bytes,5,rep,name=all_source_for_strategy,json=allSourceForStrategy,proto3" json:"all_source_for_strategy" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`      // 所有数据源信息，用于策略
	Type                  int32                                             `protobuf:"varint,6,opt,name=type,proto3" json:"type"`                                                                                                                                                             // 节点类型，原样返回参数中的值
	ListFields            []*MapStringString                                `protobuf:"bytes,7,rep,name=list_fields,json=listFields,proto3" json:"list_fields"`                                                                                                                                // 列表字段
	Id                    string                                            `protobuf:"bytes,8,opt,name=id,proto3" json:"id"`                                                                                                                                                                  // 数据ID, 原样返回参数中的值
	FieldValInfo          []*TraceabilityDetailInfoResponseFieldValInfo     `protobuf:"bytes,9,rep,name=field_val_info,json=fieldValInfo,proto3" json:"field_val_info"`                                                                                                                        // 字段采信信息
}

func (x *IpTraceabilityDetailInfoResponseItem) Reset() {
	*x = IpTraceabilityDetailInfoResponseItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_merge_proto_msgTypes[66]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IpTraceabilityDetailInfoResponseItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IpTraceabilityDetailInfoResponseItem) ProtoMessage() {}

func (x *IpTraceabilityDetailInfoResponseItem) ProtoReflect() protoreflect.Message {
	mi := &file_merge_proto_msgTypes[66]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IpTraceabilityDetailInfoResponseItem.ProtoReflect.Descriptor instead.
func (*IpTraceabilityDetailInfoResponseItem) Descriptor() ([]byte, []int) {
	return file_merge_proto_rawDescGZIP(), []int{66}
}

func (x *IpTraceabilityDetailInfoResponseItem) GetOriginalData() []*TraceabilityDetailInfoResponseOriginalDataItem {
	if x != nil {
		return x.OriginalData
	}
	return nil
}

func (x *IpTraceabilityDetailInfoResponseItem) GetMergedData() *Asset {
	if x != nil {
		return x.MergedData
	}
	return nil
}

func (x *IpTraceabilityDetailInfoResponseItem) GetStrategies() []*TraceabilityDetailInfoResponseStrategyItem {
	if x != nil {
		return x.Strategies
	}
	return nil
}

func (x *IpTraceabilityDetailInfoResponseItem) GetAllSourceForMergeData() map[string]*Source {
	if x != nil {
		return x.AllSourceForMergeData
	}
	return nil
}

func (x *IpTraceabilityDetailInfoResponseItem) GetAllSourceForStrategy() map[string]*Source {
	if x != nil {
		return x.AllSourceForStrategy
	}
	return nil
}

func (x *IpTraceabilityDetailInfoResponseItem) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *IpTraceabilityDetailInfoResponseItem) GetListFields() []*MapStringString {
	if x != nil {
		return x.ListFields
	}
	return nil
}

func (x *IpTraceabilityDetailInfoResponseItem) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *IpTraceabilityDetailInfoResponseItem) GetFieldValInfo() []*TraceabilityDetailInfoResponseFieldValInfo {
	if x != nil {
		return x.FieldValInfo
	}
	return nil
}

type IpTraceabilityDetailInfoResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items []*IpTraceabilityDetailInfoResponseItem `protobuf:"bytes,1,rep,name=items,proto3" json:"items"` // 详细信息
}

func (x *IpTraceabilityDetailInfoResponse) Reset() {
	*x = IpTraceabilityDetailInfoResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_merge_proto_msgTypes[67]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IpTraceabilityDetailInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IpTraceabilityDetailInfoResponse) ProtoMessage() {}

func (x *IpTraceabilityDetailInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_merge_proto_msgTypes[67]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IpTraceabilityDetailInfoResponse.ProtoReflect.Descriptor instead.
func (*IpTraceabilityDetailInfoResponse) Descriptor() ([]byte, []int) {
	return file_merge_proto_rawDescGZIP(), []int{67}
}

func (x *IpTraceabilityDetailInfoResponse) GetItems() []*IpTraceabilityDetailInfoResponseItem {
	if x != nil {
		return x.Items
	}
	return nil
}

type DeviceBaseInfoResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Hostname    []string  `protobuf:"bytes,1,rep,name=hostname,proto3" json:"hostname"`
	Sn          []string  `protobuf:"bytes,2,rep,name=sn,proto3" json:"sn"`
	Mac         []string  `protobuf:"bytes,3,rep,name=mac,proto3" json:"mac"`
	SourceId    []uint64  `protobuf:"varint,4,rep,packed,name=source_id,json=sourceId,proto3" json:"source_id"`
	Source      []*Source `protobuf:"bytes,5,rep,name=source,proto3" json:"source"`
	AreaId      []uint64  `protobuf:"varint,6,rep,packed,name=area_id,json=areaId,proto3" json:"area_id"`
	Area        []*Area   `protobuf:"bytes,7,rep,name=area,proto3" json:"area"`
	Os          []string  `protobuf:"bytes,8,rep,name=os,proto3" json:"os"`
	Ip          []string  `protobuf:"bytes,10,rep,name=ip,proto3" json:"ip"` // 映射 IP，目前为空
	MachineRoom []string  `protobuf:"bytes,11,rep,name=machine_room,json=machineRoom,proto3" json:"machine_room"`
	Oper        []string  `protobuf:"bytes,12,rep,name=oper,proto3" json:"oper"` // 运维人员，目前为空
	Tags        []string  `protobuf:"bytes,13,rep,name=tags,proto3" json:"tags"` // 标签，目前为空
}

func (x *DeviceBaseInfoResponse) Reset() {
	*x = DeviceBaseInfoResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_merge_proto_msgTypes[68]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeviceBaseInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceBaseInfoResponse) ProtoMessage() {}

func (x *DeviceBaseInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_merge_proto_msgTypes[68]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceBaseInfoResponse.ProtoReflect.Descriptor instead.
func (*DeviceBaseInfoResponse) Descriptor() ([]byte, []int) {
	return file_merge_proto_rawDescGZIP(), []int{68}
}

func (x *DeviceBaseInfoResponse) GetHostname() []string {
	if x != nil {
		return x.Hostname
	}
	return nil
}

func (x *DeviceBaseInfoResponse) GetSn() []string {
	if x != nil {
		return x.Sn
	}
	return nil
}

func (x *DeviceBaseInfoResponse) GetMac() []string {
	if x != nil {
		return x.Mac
	}
	return nil
}

func (x *DeviceBaseInfoResponse) GetSourceId() []uint64 {
	if x != nil {
		return x.SourceId
	}
	return nil
}

func (x *DeviceBaseInfoResponse) GetSource() []*Source {
	if x != nil {
		return x.Source
	}
	return nil
}

func (x *DeviceBaseInfoResponse) GetAreaId() []uint64 {
	if x != nil {
		return x.AreaId
	}
	return nil
}

func (x *DeviceBaseInfoResponse) GetArea() []*Area {
	if x != nil {
		return x.Area
	}
	return nil
}

func (x *DeviceBaseInfoResponse) GetOs() []string {
	if x != nil {
		return x.Os
	}
	return nil
}

func (x *DeviceBaseInfoResponse) GetIp() []string {
	if x != nil {
		return x.Ip
	}
	return nil
}

func (x *DeviceBaseInfoResponse) GetMachineRoom() []string {
	if x != nil {
		return x.MachineRoom
	}
	return nil
}

func (x *DeviceBaseInfoResponse) GetOper() []string {
	if x != nil {
		return x.Oper
	}
	return nil
}

func (x *DeviceBaseInfoResponse) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

type DeviceRelatedIpInfoResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IpList map[string]*ListAsset `protobuf:"bytes,1,rep,name=ip_list,json=ipList,proto3" json:"ip_list" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // IP列表,key为内外网标识,value为IP列表
}

func (x *DeviceRelatedIpInfoResponse) Reset() {
	*x = DeviceRelatedIpInfoResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_merge_proto_msgTypes[69]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeviceRelatedIpInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceRelatedIpInfoResponse) ProtoMessage() {}

func (x *DeviceRelatedIpInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_merge_proto_msgTypes[69]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceRelatedIpInfoResponse.ProtoReflect.Descriptor instead.
func (*DeviceRelatedIpInfoResponse) Descriptor() ([]byte, []int) {
	return file_merge_proto_rawDescGZIP(), []int{69}
}

func (x *DeviceRelatedIpInfoResponse) GetIpList() map[string]*ListAsset {
	if x != nil {
		return x.IpList
	}
	return nil
}

type DeviceRelatedEthInfoItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name      string   `protobuf:"bytes,1,opt,name=name,proto3" json:"name"`                            // 网卡名
	Mac       string   `protobuf:"bytes,2,opt,name=mac,proto3" json:"mac"`                              // MAC地址
	Ipv4      string   `protobuf:"bytes,3,opt,name=ipv4,proto3" json:"ipv4"`                            // IP地址
	Ipv6      string   `protobuf:"bytes,4,opt,name=ipv6,proto3" json:"ipv6"`                            // IPV6地址
	Gateway   string   `protobuf:"bytes,5,opt,name=gateway,proto3" json:"gateway"`                      // 网关
	Netmask   string   `protobuf:"bytes,6,opt,name=netmask,proto3" json:"netmask"`                      // 子网掩码
	DnsServer []string `protobuf:"bytes,7,rep,name=dns_server,json=dnsServer,proto3" json:"dns_server"` // DNS
	Broadcast string   `protobuf:"bytes,8,opt,name=broadcast,proto3" json:"broadcast"`                  // 广播
	Speed     int64    `protobuf:"varint,9,opt,name=speed,proto3" json:"speed"`                         // 速度
	Status    string   `protobuf:"bytes,10,opt,name=status,proto3" json:"status"`                       // 状态
}

func (x *DeviceRelatedEthInfoItem) Reset() {
	*x = DeviceRelatedEthInfoItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_merge_proto_msgTypes[70]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeviceRelatedEthInfoItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceRelatedEthInfoItem) ProtoMessage() {}

func (x *DeviceRelatedEthInfoItem) ProtoReflect() protoreflect.Message {
	mi := &file_merge_proto_msgTypes[70]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceRelatedEthInfoItem.ProtoReflect.Descriptor instead.
func (*DeviceRelatedEthInfoItem) Descriptor() ([]byte, []int) {
	return file_merge_proto_rawDescGZIP(), []int{70}
}

func (x *DeviceRelatedEthInfoItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DeviceRelatedEthInfoItem) GetMac() string {
	if x != nil {
		return x.Mac
	}
	return ""
}

func (x *DeviceRelatedEthInfoItem) GetIpv4() string {
	if x != nil {
		return x.Ipv4
	}
	return ""
}

func (x *DeviceRelatedEthInfoItem) GetIpv6() string {
	if x != nil {
		return x.Ipv6
	}
	return ""
}

func (x *DeviceRelatedEthInfoItem) GetGateway() string {
	if x != nil {
		return x.Gateway
	}
	return ""
}

func (x *DeviceRelatedEthInfoItem) GetNetmask() string {
	if x != nil {
		return x.Netmask
	}
	return ""
}

func (x *DeviceRelatedEthInfoItem) GetDnsServer() []string {
	if x != nil {
		return x.DnsServer
	}
	return nil
}

func (x *DeviceRelatedEthInfoItem) GetBroadcast() string {
	if x != nil {
		return x.Broadcast
	}
	return ""
}

func (x *DeviceRelatedEthInfoItem) GetSpeed() int64 {
	if x != nil {
		return x.Speed
	}
	return 0
}

func (x *DeviceRelatedEthInfoItem) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

type DeviceRelatedEthInfoResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EthList []*DeviceRelatedEthInfoItem `protobuf:"bytes,1,rep,name=eth_list,json=ethList,proto3" json:"eth_list"` // 网卡列表
}

func (x *DeviceRelatedEthInfoResponse) Reset() {
	*x = DeviceRelatedEthInfoResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_merge_proto_msgTypes[71]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeviceRelatedEthInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceRelatedEthInfoResponse) ProtoMessage() {}

func (x *DeviceRelatedEthInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_merge_proto_msgTypes[71]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceRelatedEthInfoResponse.ProtoReflect.Descriptor instead.
func (*DeviceRelatedEthInfoResponse) Descriptor() ([]byte, []int) {
	return file_merge_proto_rawDescGZIP(), []int{71}
}

func (x *DeviceRelatedEthInfoResponse) GetEthList() []*DeviceRelatedEthInfoItem {
	if x != nil {
		return x.EthList
	}
	return nil
}

type DeviceTraceabilityBaseInfoResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AllSourceId    []uint64  `protobuf:"varint,1,rep,packed,name=all_source_id,json=allSourceId,proto3" json:"all_source_id"`  // 所有数据源
	SourceId       []uint64  `protobuf:"varint,2,rep,packed,name=source_id,json=sourceId,proto3" json:"source_id"`             // 最后一次融合生效的数据源
	AllSource      []*Source `protobuf:"bytes,3,rep,name=all_source,json=allSource,proto3" json:"all_source"`                  // 所有数据源信息
	Source         []*Source `protobuf:"bytes,4,rep,name=source,proto3" json:"source"`                                         // 最后一次融合生效的数据源信息
	FirstMergeTime string    `protobuf:"bytes,5,opt,name=first_merge_time,json=firstMergeTime,proto3" json:"first_merge_time"` // 第一次合并时间
	LastMergeTime  string    `protobuf:"bytes,6,opt,name=last_merge_time,json=lastMergeTime,proto3" json:"last_merge_time"`    // 最后一次合并时间
	MergeKey       []string  `protobuf:"bytes,7,rep,name=merge_key,json=mergeKey,proto3" json:"merge_key"`                     // 合并键
	Sn             []string  `protobuf:"bytes,8,rep,name=sn,proto3" json:"sn"`                                                 // 序列号
	Hostname       []string  `protobuf:"bytes,9,rep,name=hostname,proto3" json:"hostname"`                                     // 主机名
	Mac            []string  `protobuf:"bytes,10,rep,name=mac,proto3" json:"mac"`                                              // MAC地址
	Fid            string    `protobuf:"bytes,11,opt,name=fid,proto3" json:"fid"`                                              // 融合key
}

func (x *DeviceTraceabilityBaseInfoResponse) Reset() {
	*x = DeviceTraceabilityBaseInfoResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_merge_proto_msgTypes[72]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeviceTraceabilityBaseInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceTraceabilityBaseInfoResponse) ProtoMessage() {}

func (x *DeviceTraceabilityBaseInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_merge_proto_msgTypes[72]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceTraceabilityBaseInfoResponse.ProtoReflect.Descriptor instead.
func (*DeviceTraceabilityBaseInfoResponse) Descriptor() ([]byte, []int) {
	return file_merge_proto_rawDescGZIP(), []int{72}
}

func (x *DeviceTraceabilityBaseInfoResponse) GetAllSourceId() []uint64 {
	if x != nil {
		return x.AllSourceId
	}
	return nil
}

func (x *DeviceTraceabilityBaseInfoResponse) GetSourceId() []uint64 {
	if x != nil {
		return x.SourceId
	}
	return nil
}

func (x *DeviceTraceabilityBaseInfoResponse) GetAllSource() []*Source {
	if x != nil {
		return x.AllSource
	}
	return nil
}

func (x *DeviceTraceabilityBaseInfoResponse) GetSource() []*Source {
	if x != nil {
		return x.Source
	}
	return nil
}

func (x *DeviceTraceabilityBaseInfoResponse) GetFirstMergeTime() string {
	if x != nil {
		return x.FirstMergeTime
	}
	return ""
}

func (x *DeviceTraceabilityBaseInfoResponse) GetLastMergeTime() string {
	if x != nil {
		return x.LastMergeTime
	}
	return ""
}

func (x *DeviceTraceabilityBaseInfoResponse) GetMergeKey() []string {
	if x != nil {
		return x.MergeKey
	}
	return nil
}

func (x *DeviceTraceabilityBaseInfoResponse) GetSn() []string {
	if x != nil {
		return x.Sn
	}
	return nil
}

func (x *DeviceTraceabilityBaseInfoResponse) GetHostname() []string {
	if x != nil {
		return x.Hostname
	}
	return nil
}

func (x *DeviceTraceabilityBaseInfoResponse) GetMac() []string {
	if x != nil {
		return x.Mac
	}
	return nil
}

func (x *DeviceTraceabilityBaseInfoResponse) GetFid() string {
	if x != nil {
		return x.Fid
	}
	return ""
}

var File_merge_proto protoreflect.FileDescriptor

var file_merge_proto_rawDesc = []byte{
	0x0a, 0x0b, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x6d,
	0x65, 0x72, 0x67, 0x65, 0x22, 0x07, 0x0a, 0x05, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x40, 0x0a,
	0x06, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x69,
	0x63, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x22,
	0x76, 0x0a, 0x08, 0x52, 0x75, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x18, 0x0a, 0x07, 0x70,
	0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x72,
	0x6f, 0x64, 0x75, 0x63, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x74,
	0x61, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x72, 0x73, 0x74, 0x54,
	0x61, 0x67, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x5f, 0x74, 0x61, 0x67,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x54, 0x61,
	0x67, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x22, 0x2a, 0x0a, 0x04, 0x41, 0x72, 0x65, 0x61, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x22, 0x27, 0x0a, 0x11, 0x54, 0x65, 0x73, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f,
	0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x2e, 0x0a, 0x12,
	0x54, 0x65, 0x73, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x77, 0x65, 0x6c, 0x63, 0x6f, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x77, 0x65, 0x6c, 0x63, 0x6f, 0x6d, 0x65, 0x22, 0x9f, 0x01, 0x0a,
	0x19, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x54, 0x61, 0x67,
	0x67, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x66, 0x69,
	0x65, 0x6c, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x66, 0x69, 0x65, 0x6c, 0x64,
	0x12, 0x1c, 0x0a, 0x09, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x14,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x65, 0x74, 0x5f, 0x66, 0x69, 0x65, 0x6c,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x65, 0x74, 0x46, 0x69, 0x65, 0x6c,
	0x64, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x65, 0x74, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x65, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x50,
	0x0a, 0x1a, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x54, 0x61,
	0x67, 0x67, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07,
	0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73,
	0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x22, 0xb1, 0x01, 0x0a, 0x17, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x4d, 0x65, 0x72, 0x67,
	0x65, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x08, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x6e, 0x6f, 0x64,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x6e, 0x6f, 0x64, 0x65,
	0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0d, 0x63,
	0x68, 0x69, 0x6c, 0x64, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x63, 0x68, 0x69, 0x6c, 0x64, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12,
	0x23, 0x0a, 0x0d, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x5f, 0x65, 0x76, 0x65, 0x6e, 0x74,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x45,
	0x76, 0x65, 0x6e, 0x74, 0x22, 0x2c, 0x0a, 0x06, 0x49, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x12, 0x12,
	0x0a, 0x04, 0x61, 0x72, 0x65, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x61, 0x72,
	0x65, 0x61, 0x22, 0x67, 0x0a, 0x0f, 0x44, 0x61, 0x74, 0x61, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x42,
	0x79, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x22,
	0x0a, 0x0d, 0x63, 0x68, 0x69, 0x6c, 0x64, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x68, 0x69, 0x6c, 0x64, 0x54, 0x61, 0x73, 0x6b,
	0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x06, 0x6e, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x22, 0xbf, 0x02, 0x0a, 0x1b,
	0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x4d, 0x65, 0x72, 0x67, 0x65, 0x46, 0x6f, 0x72, 0x41,
	0x73, 0x73, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x5b, 0x0a, 0x1a, 0x74,
	0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x5f, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x5f, 0x62, 0x61, 0x73,
	0x65, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1e, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x4d,
	0x65, 0x72, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52,
	0x17, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x4d, 0x65, 0x72, 0x67, 0x65, 0x42, 0x61, 0x73,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x61, 0x73, 0x73, 0x65,
	0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x61, 0x73, 0x73,
	0x65, 0x74, 0x49, 0x64, 0x73, 0x12, 0x28, 0x0a, 0x08, 0x69, 0x70, 0x5f, 0x69, 0x6e, 0x66, 0x6f,
	0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e,
	0x49, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x69, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x12,
	0x43, 0x0a, 0x12, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x62, 0x79,
	0x5f, 0x74, 0x61, 0x73, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x6d, 0x65,
	0x72, 0x67, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x42, 0x79, 0x54,
	0x61, 0x73, 0x6b, 0x52, 0x0f, 0x64, 0x61, 0x74, 0x61, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x42, 0x79,
	0x54, 0x61, 0x73, 0x6b, 0x12, 0x16, 0x0a, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x18, 0x05,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x12, 0x1f, 0x0a, 0x0b,
	0x73, 0x75, 0x62, 0x5f, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0a, 0x73, 0x75, 0x62, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x22, 0xb9, 0x02,
	0x0a, 0x1c, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x4d, 0x65, 0x72, 0x67, 0x65, 0x46, 0x6f,
	0x72, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x5b,
	0x0a, 0x1a, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x5f, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x5f,
	0x62, 0x61, 0x73, 0x65, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x54, 0x72, 0x69, 0x67, 0x67,
	0x65, 0x72, 0x4d, 0x65, 0x72, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x52, 0x17, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x4d, 0x65, 0x72, 0x67, 0x65,
	0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x09, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x6e,
	0x69, 0x71, 0x75, 0x65, 0x5f, 0x6b, 0x65, 0x79, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x0a, 0x75, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x4b, 0x65, 0x79, 0x73, 0x12, 0x43, 0x0a, 0x12, 0x64,
	0x61, 0x74, 0x61, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x62, 0x79, 0x5f, 0x74, 0x61, 0x73,
	0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e,
	0x44, 0x61, 0x74, 0x61, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x42, 0x79, 0x54, 0x61, 0x73, 0x6b, 0x52,
	0x0f, 0x64, 0x61, 0x74, 0x61, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x42, 0x79, 0x54, 0x61, 0x73, 0x6b,
	0x12, 0x16, 0x0a, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x75, 0x62, 0x5f,
	0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x73,
	0x75, 0x62, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x22, 0x90, 0x02, 0x0a, 0x1a, 0x54, 0x72,
	0x69, 0x67, 0x67, 0x65, 0x72, 0x4d, 0x65, 0x72, 0x67, 0x65, 0x46, 0x6f, 0x72, 0x56, 0x75, 0x6c,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x5b, 0x0a, 0x1a, 0x74, 0x72, 0x69, 0x67,
	0x67, 0x65, 0x72, 0x5f, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x5f, 0x62, 0x61, 0x73, 0x65, 0x5f, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x6d,
	0x65, 0x72, 0x67, 0x65, 0x2e, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x4d, 0x65, 0x72, 0x67,
	0x65, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x17, 0x74, 0x72,
	0x69, 0x67, 0x67, 0x65, 0x72, 0x4d, 0x65, 0x72, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x76, 0x75, 0x6c, 0x5f, 0x69, 0x64, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x76, 0x75, 0x6c, 0x49, 0x64, 0x73, 0x12, 0x43,
	0x0a, 0x12, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x62, 0x79, 0x5f,
	0x74, 0x61, 0x73, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x6d, 0x65, 0x72,
	0x67, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x42, 0x79, 0x54, 0x61,
	0x73, 0x6b, 0x52, 0x0f, 0x64, 0x61, 0x74, 0x61, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x42, 0x79, 0x54,
	0x61, 0x73, 0x6b, 0x12, 0x16, 0x0a, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x18, 0x04, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x73,
	0x75, 0x62, 0x5f, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0a, 0x73, 0x75, 0x62, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x22, 0x95, 0x02, 0x0a,
	0x1b, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x4d, 0x65, 0x72, 0x67, 0x65, 0x46, 0x6f, 0x72,
	0x53, 0x74, 0x61, 0x66, 0x66, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x5b, 0x0a, 0x1a,
	0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x5f, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x5f, 0x62, 0x61,
	0x73, 0x65, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1e, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72,
	0x4d, 0x65, 0x72, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x52, 0x17, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x4d, 0x65, 0x72, 0x67, 0x65, 0x42, 0x61,
	0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x74, 0x61,
	0x66, 0x66, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x73, 0x74,
	0x61, 0x66, 0x66, 0x49, 0x64, 0x73, 0x12, 0x43, 0x0a, 0x12, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x72,
	0x61, 0x6e, 0x67, 0x65, 0x5f, 0x62, 0x79, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x16, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x52,
	0x61, 0x6e, 0x67, 0x65, 0x42, 0x79, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x0f, 0x64, 0x61, 0x74, 0x61,
	0x52, 0x61, 0x6e, 0x67, 0x65, 0x42, 0x79, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x16, 0x0a, 0x06, 0x66,
	0x69, 0x65, 0x6c, 0x64, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x66, 0x69, 0x65,
	0x6c, 0x64, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x75, 0x62, 0x5f, 0x74, 0x72, 0x69, 0x67, 0x67,
	0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x73, 0x75, 0x62, 0x54, 0x72, 0x69,
	0x67, 0x67, 0x65, 0x72, 0x22, 0x4a, 0x0a, 0x14, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x4d,
	0x65, 0x72, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07,
	0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73,
	0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x22, 0x96, 0x01, 0x0a, 0x1c, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x65, 0x72, 0x67, 0x65,
	0x44, 0x61, 0x74, 0x61, 0x54, 0x6f, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x61, 0x73, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x12, 0x17,
	0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09,
	0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x08, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x49, 0x64, 0x22, 0x4c, 0x0a, 0x16, 0x52, 0x65, 0x63,
	0x61, 0x6c, 0x52, 0x69, 0x73, 0x6b, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x18, 0x0a,
	0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x9b, 0x02, 0x0a, 0x1d, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x4d, 0x65, 0x72, 0x67, 0x65, 0x44, 0x61, 0x74, 0x61, 0x54, 0x6f, 0x54, 0x61, 0x73,
	0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x23, 0x0a, 0x0d, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x5f, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x66, 0x61, 0x69, 0x6c,
	0x65, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x27, 0x0a, 0x0f, 0x64, 0x69, 0x73, 0x63, 0x61,
	0x72, 0x64, 0x65, 0x64, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0e, 0x64, 0x69, 0x73, 0x63, 0x61, 0x72, 0x64, 0x65, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x77, 0x6f,
	0x72, 0x6b, 0x5f, 0x68, 0x6f, 0x75, 0x72, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09,
	0x77, 0x6f, 0x72, 0x6b, 0x48, 0x6f, 0x75, 0x72, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x75, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x64, 0x75, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x6c, 0x0a, 0x12, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x4d,
	0x65, 0x72, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x62, 0x61, 0x74, 0x63,
	0x68, 0x5f, 0x6e, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x62, 0x61, 0x74, 0x63,
	0x68, 0x4e, 0x6f, 0x22, 0x77, 0x0a, 0x11, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x4d, 0x65, 0x72,
	0x67, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x66, 0x69, 0x65, 0x6c,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x12, 0x18,
	0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x07, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x22, 0x7e, 0x0a, 0x13,
	0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x4d, 0x65, 0x72, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x32, 0x0a, 0x07, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x4d, 0x61, 0x6e,
	0x75, 0x61, 0x6c, 0x4d, 0x65, 0x72, 0x67, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x07,
	0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x62, 0x61, 0x74, 0x63, 0x68,
	0x5f, 0x6e, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x62, 0x61, 0x74, 0x63, 0x68,
	0x4e, 0x6f, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x5e, 0x0a, 0x0f,
	0x52, 0x65, 0x4d, 0x65, 0x72, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x17, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63,
	0x65, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0xec, 0x01, 0x0a,
	0x18, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x43, 0x61, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x54, 0x79, 0x70, 0x65, 0x12, 0x10,
	0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x03, 0x69, 0x64, 0x73,
	0x12, 0x43, 0x0a, 0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x2b, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x43,
	0x61, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x62, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x6e,
	0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x62, 0x61, 0x74, 0x63, 0x68, 0x4e, 0x6f,
	0x1a, 0x39, 0x0a, 0x0b, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x4f, 0x0a, 0x19, 0x4d,
	0x61, 0x6e, 0x75, 0x61, 0x6c, 0x43, 0x61, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63,
	0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x2c, 0x0a, 0x06,
	0x49, 0x50, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x61, 0x72, 0x65, 0x61, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x61, 0x72, 0x65, 0x61, 0x22, 0x94, 0x01, 0x0a, 0x0c, 0x42,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x16, 0x0a, 0x06, 0x73,
	0x79, 0x73, 0x74, 0x65, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x79, 0x73,
	0x74, 0x65, 0x6d, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x49, 0x64,
	0x12, 0x14, 0x0a, 0x05, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x49,
	0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x18,
	0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x22, 0xf3, 0x01, 0x0a, 0x0a, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x12, 0x2c, 0x0a, 0x12, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x73, 0x79, 0x73,
	0x74, 0x65, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x49, 0x64, 0x12, 0x30,
	0x0a, 0x14, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x73, 0x79, 0x73, 0x74, 0x65,
	0x6d, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x75, 0x73, 0x65,
	0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73,
	0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x2b, 0x0a, 0x07, 0x70, 0x61,
	0x72, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x6d, 0x65,
	0x72, 0x67, 0x65, 0x2e, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x07,
	0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x73, 0x22, 0x40, 0x0a, 0x08, 0x4f, 0x70, 0x65, 0x72, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x66, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x66, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0xa4, 0x01, 0x0a, 0x0e, 0x44, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x12, 0x2f, 0x0a, 0x08,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13,
	0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x08, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x12, 0x42, 0x0a,
	0x13, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x6d, 0x65, 0x72,
	0x67, 0x65, 0x2e, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x12, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x12, 0x1d, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0d, 0x2e,
	0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x49, 0x50, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x02, 0x69, 0x70,
	0x22, 0x96, 0x01, 0x0a, 0x0b, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4f, 0x70, 0x65, 0x72, 0x73,
	0x12, 0x2c, 0x0a, 0x09, 0x6f, 0x70, 0x65, 0x72, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x4f, 0x70, 0x65, 0x72,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x6f, 0x70, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x3a,
	0x0a, 0x0f, 0x6f, 0x70, 0x65, 0x72, 0x5f, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e,
	0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x0e, 0x6f, 0x70, 0x65, 0x72,
	0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x1d, 0x0a, 0x02, 0x69, 0x70,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x49,
	0x50, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x02, 0x69, 0x70, 0x22, 0xe0, 0x04, 0x0a, 0x06, 0x44, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x25, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x53, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x68,
	0x6f, 0x73, 0x74, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x68,
	0x6f, 0x73, 0x74, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x73, 0x6e, 0x18, 0x04, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x02, 0x73, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x61, 0x63, 0x18, 0x05,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x61, 0x63, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18,
	0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x69,
	0x76, 0x61, 0x74, 0x65, 0x5f, 0x69, 0x70, 0x18, 0x07, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x70,
	0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x49, 0x70, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x75, 0x62, 0x6c,
	0x69, 0x63, 0x5f, 0x69, 0x70, 0x18, 0x08, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x70, 0x75, 0x62,
	0x6c, 0x69, 0x63, 0x49, 0x70, 0x12, 0x21, 0x0a, 0x0c, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65,
	0x5f, 0x72, 0x6f, 0x6f, 0x6d, 0x18, 0x09, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x6d, 0x61, 0x63,
	0x68, 0x69, 0x6e, 0x65, 0x52, 0x6f, 0x6f, 0x6d, 0x12, 0x1f, 0x0a, 0x04, 0x61, 0x72, 0x65, 0x61,
	0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x41,
	0x72, 0x65, 0x61, 0x52, 0x04, 0x61, 0x72, 0x65, 0x61, 0x12, 0x0e, 0x0a, 0x02, 0x6f, 0x73, 0x18,
	0x0b, 0x20, 0x03, 0x28, 0x09, 0x52, 0x02, 0x6f, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x64, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6f, 0x70, 0x65, 0x72, 0x18, 0x0f,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x6f, 0x70, 0x65, 0x72, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x10, 0x20, 0x03, 0x28, 0x04, 0x52, 0x08, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x61, 0x72, 0x65, 0x61, 0x5f,
	0x69, 0x64, 0x18, 0x11, 0x20, 0x03, 0x28, 0x04, 0x52, 0x06, 0x61, 0x72, 0x65, 0x61, 0x49, 0x64,
	0x12, 0x12, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18, 0x12, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04,
	0x74, 0x61, 0x67, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x66, 0x69, 0x64, 0x18, 0x13, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x66, 0x69, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x6f, 0x63, 0x5f, 0x6e, 0x75,
	0x6d, 0x18, 0x14, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x70, 0x6f, 0x63, 0x4e, 0x75, 0x6d, 0x12,
	0x31, 0x0a, 0x08, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x18, 0x15, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x15, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x52, 0x08, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x12, 0x28, 0x0a, 0x05, 0x6f, 0x70, 0x65, 0x72, 0x73, 0x18, 0x16, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x4f, 0x70, 0x65, 0x72, 0x73, 0x52, 0x05, 0x6f, 0x70, 0x65, 0x72, 0x73, 0x22, 0x1f, 0x0a, 0x0d,
	0x49, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x23, 0x0a,
	0x11, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02,
	0x69, 0x64, 0x22, 0xfb, 0x01, 0x0a, 0x0e, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x42, 0x61, 0x73, 0x65, 0x12, 0x2c, 0x0a, 0x12, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x10, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x79, 0x73, 0x74, 0x65,
	0x6d, 0x49, 0x64, 0x12, 0x30, 0x0a, 0x14, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x12, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x79, 0x73, 0x74, 0x65,
	0x6d, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1b,
	0x0a, 0x09, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x2f, 0x0a, 0x07, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x15, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x42, 0x61, 0x73, 0x65, 0x52, 0x07, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x73,
	0x22, 0xad, 0x01, 0x0a, 0x0e, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x46, 0x69, 0x6e, 0x64, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64,
	0x12, 0x17, 0x0a, 0x07, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x06, 0x6e, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x23, 0x0a, 0x0d,
	0x6d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x46, 0x69, 0x65, 0x6c,
	0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x69, 0x6e, 0x64, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x66, 0x69, 0x6e, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x22, 0xc1, 0x01, 0x0a, 0x0a, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x42, 0x61, 0x73, 0x65, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x10, 0x0a, 0x03, 0x66, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x66, 0x69,
	0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x67, 0x69, 0x64, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x67, 0x69, 0x64, 0x12, 0x32, 0x0a, 0x09, 0x66, 0x69, 0x6e,
	0x64, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6d,
	0x65, 0x72, 0x67, 0x65, 0x2e, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x46, 0x69, 0x6e, 0x64, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x08, 0x66, 0x69, 0x6e, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x35, 0x0a,
	0x0a, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x15, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x42, 0x61, 0x73, 0x65, 0x52, 0x0a, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x22, 0x91, 0x01, 0x0a, 0x11, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x57,
	0x69, 0x74, 0x68, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x6e, 0x6f, 0x64, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x6e, 0x6f, 0x64, 0x65, 0x49, 0x64,
	0x12, 0x21, 0x0a, 0x0c, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x5f, 0x66,
	0x69, 0x65, 0x6c, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d, 0x61, 0x70, 0x70,
	0x69, 0x6e, 0x67, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x22, 0xd5, 0x01, 0x0a, 0x17, 0x49, 0x70, 0x41,
	0x64, 0x6d, 0x69, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x49, 0x74, 0x65, 0x6d, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x12, 0x1b, 0x0a, 0x09,
	0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x6f, 0x77, 0x6e,
	0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x12,
	0x19, 0x0a, 0x08, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x49, 0x64, 0x12, 0x32, 0x0a, 0x0b, 0x70, 0x65,
	0x72, 0x73, 0x6f, 0x6e, 0x5f, 0x62, 0x61, 0x73, 0x65, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x11, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x42, 0x61,
	0x73, 0x65, 0x52, 0x0a, 0x70, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x42, 0x61, 0x73, 0x65, 0x12, 0x20,
	0x0a, 0x0b, 0x72, 0x65, 0x6c, 0x69, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0b, 0x72, 0x65, 0x6c, 0x69, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79,
	0x22, 0x4f, 0x0a, 0x17, 0x49, 0x70, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x05, 0x61,
	0x64, 0x6d, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x6d, 0x65, 0x72,
	0x67, 0x65, 0x2e, 0x49, 0x70, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x05, 0x61, 0x64, 0x6d, 0x69,
	0x6e, 0x22, 0x41, 0x0a, 0x0f, 0x50, 0x6f, 0x72, 0x74, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67,
	0x4c, 0x69, 0x73, 0x74, 0x12, 0x2e, 0x0a, 0x08, 0x6d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x50,
	0x6f, 0x72, 0x74, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x52, 0x08, 0x6d, 0x61, 0x70, 0x70,
	0x69, 0x6e, 0x67, 0x73, 0x22, 0x8d, 0x01, 0x0a, 0x0b, 0x50, 0x6f, 0x72, 0x74, 0x4d, 0x61, 0x70,
	0x70, 0x69, 0x6e, 0x67, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x5f, 0x69,
	0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x49,
	0x70, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x5f, 0x70, 0x6f, 0x72, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x50, 0x6f,
	0x72, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x5f, 0x69, 0x70,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x49,
	0x70, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x5f, 0x70, 0x6f, 0x72,
	0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0b, 0x70, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65,
	0x50, 0x6f, 0x72, 0x74, 0x22, 0x7e, 0x0a, 0x0a, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x66, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x66, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x66, 0x69, 0x6e, 0x64,
	0x5f, 0x62, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x66, 0x69, 0x6e, 0x64, 0x42,
	0x79, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x22, 0xe9, 0x01, 0x0a, 0x13, 0x49, 0x70, 0x41, 0x64, 0x6d, 0x69, 0x6e,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x34, 0x0a, 0x05,
	0x61, 0x64, 0x6d, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x6d, 0x65,
	0x72, 0x67, 0x65, 0x2e, 0x49, 0x70, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x05, 0x61, 0x64, 0x6d,
	0x69, 0x6e, 0x12, 0x25, 0x0a, 0x04, 0x6f, 0x70, 0x65, 0x72, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x11, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x42,
	0x61, 0x73, 0x65, 0x52, 0x04, 0x6f, 0x70, 0x65, 0x72, 0x12, 0x21, 0x0a, 0x0c, 0x6d, 0x61, 0x63,
	0x68, 0x69, 0x6e, 0x65, 0x5f, 0x72, 0x6f, 0x6f, 0x6d, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x0b, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x52, 0x6f, 0x6f, 0x6d, 0x12, 0x3e, 0x0a, 0x0f,
	0x6f, 0x70, 0x65, 0x72, 0x5f, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x18,
	0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x4f, 0x70,
	0x65, 0x72, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x0e, 0x6f, 0x70,
	0x65, 0x72, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x12, 0x0a, 0x04,
	0x74, 0x61, 0x67, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73,
	0x22, 0x44, 0x0a, 0x0e, 0x4f, 0x70, 0x65, 0x72, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6f, 0x70, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6f, 0x70, 0x65, 0x72, 0x12, 0x1e, 0x0a, 0x0a, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x65, 0x70, 0x61,
	0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x22, 0x17, 0x0a, 0x15, 0x49, 0x70, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0xa0, 0x01, 0x0a, 0x16, 0x49, 0x70, 0x50, 0x6f, 0x72, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x6f,
	0x72, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x1a,
	0x0a, 0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x75, 0x72, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x14, 0x0a, 0x05,
	0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74,
	0x6c, 0x65, 0x22, 0x47, 0x0a, 0x12, 0x49, 0x70, 0x50, 0x6f, 0x72, 0x74, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x31, 0x0a, 0x04, 0x70, 0x6f, 0x72, 0x74,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x49,
	0x70, 0x50, 0x6f, 0x72, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x22, 0x9e, 0x03, 0x0a, 0x08,
	0x48, 0x6f, 0x73, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x61, 0x6b, 0x65,
	0x72, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x61, 0x6b, 0x65, 0x72, 0x12, 0x14,
	0x0a, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x73, 0x6e, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x02, 0x73, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x6f, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x02, 0x6f, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x6f, 0x73, 0x5f, 0x6b, 0x65, 0x72, 0x6e, 0x65,
	0x6c, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x6f, 0x73, 0x4b, 0x65, 0x72, 0x6e, 0x65,
	0x6c, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x5f, 0x73, 0x69, 0x7a, 0x65,
	0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x53, 0x69,
	0x7a, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x5f, 0x75, 0x73, 0x61,
	0x67, 0x65, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x07, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0f, 0x6d,
	0x65, 0x6d, 0x6f, 0x72, 0x79, 0x55, 0x73, 0x61, 0x67, 0x65, 0x52, 0x61, 0x74, 0x65, 0x12, 0x1b,
	0x0a, 0x09, 0x63, 0x70, 0x75, 0x5f, 0x6d, 0x61, 0x6b, 0x65, 0x72, 0x18, 0x08, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x08, 0x63, 0x70, 0x75, 0x4d, 0x61, 0x6b, 0x65, 0x72, 0x12, 0x1b, 0x0a, 0x09, 0x63,
	0x70, 0x75, 0x5f, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x18, 0x09, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08,
	0x63, 0x70, 0x75, 0x42, 0x72, 0x61, 0x6e, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x70, 0x75, 0x5f,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x05, 0x52, 0x08, 0x63, 0x70, 0x75,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x61, 0x76,
	0x65, 0x72, 0x61, 0x67, 0x65, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x6c, 0x6f, 0x61,
	0x64, 0x41, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x69, 0x73, 0x6b,
	0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x05, 0x52, 0x09, 0x64, 0x69,
	0x73, 0x6b, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x64, 0x69, 0x73, 0x6b, 0x5f,
	0x73, 0x69, 0x7a, 0x65, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x05, 0x52, 0x08, 0x64, 0x69, 0x73, 0x6b,
	0x53, 0x69, 0x7a, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x64, 0x69, 0x73, 0x6b, 0x5f, 0x75, 0x73, 0x61,
	0x67, 0x65, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x0e, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0d, 0x64,
	0x69, 0x73, 0x6b, 0x55, 0x73, 0x61, 0x67, 0x65, 0x52, 0x61, 0x74, 0x65, 0x22, 0xb6, 0x02, 0x0a,
	0x16, 0x49, 0x70, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x6f, 0x63, 0x5f, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x6f, 0x63, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x69, 0x73, 0x5f, 0x63, 0x6d, 0x64, 0x62, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x69, 0x73, 0x43, 0x6d, 0x64, 0x62, 0x12, 0x17, 0x0a,
	0x07, 0x69, 0x73, 0x5f, 0x68, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06,
	0x69, 0x73, 0x48, 0x69, 0x64, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x69, 0x73, 0x5f, 0x6a, 0x75, 0x6d,
	0x70, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x69,
	0x73, 0x4a, 0x75, 0x6d, 0x70, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x12, 0x33, 0x0a, 0x16, 0x68,
	0x61, 0x73, 0x5f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x66, 0x6f, 0x72, 0x5f, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x13, 0x68, 0x61, 0x73,
	0x41, 0x64, 0x6d, 0x69, 0x6e, 0x46, 0x6f, 0x72, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x12, 0x35, 0x0a, 0x17, 0x68, 0x61, 0x73, 0x5f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x66, 0x6f,
	0x72, 0x5f, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x14, 0x68, 0x61, 0x73, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x46, 0x6f, 0x72, 0x4f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x23, 0x0a, 0x0d, 0x68, 0x61, 0x73, 0x5f, 0x64,
	0x65, 0x70, 0x61, 0x72, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c,
	0x68, 0x61, 0x73, 0x44, 0x65, 0x70, 0x61, 0x72, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x17, 0x0a, 0x07,
	0x68, 0x61, 0x73, 0x5f, 0x77, 0x61, 0x66, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x68,
	0x61, 0x73, 0x57, 0x61, 0x66, 0x22, 0xd3, 0x02, 0x0a, 0x1e, 0x49, 0x70, 0x54, 0x72, 0x61, 0x63,
	0x65, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x42, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x22, 0x0a, 0x0d, 0x61, 0x6c, 0x6c, 0x5f,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x03, 0x28, 0x04, 0x52,
	0x0b, 0x61, 0x6c, 0x6c, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x03, 0x28, 0x04, 0x52,
	0x08, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x0a, 0x61, 0x6c, 0x6c,
	0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0d, 0x2e,
	0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x09, 0x61, 0x6c,
	0x6c, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x25, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e,
	0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x28,
	0x0a, 0x10, 0x69, 0x73, 0x5f, 0x65, 0x78, 0x74, 0x72, 0x61, 0x74, 0x5f, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x69, 0x73, 0x45, 0x78, 0x74, 0x72,
	0x61, 0x74, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x65, 0x72, 0x67,
	0x65, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x6d,
	0x65, 0x72, 0x67, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x28, 0x0a, 0x10, 0x66, 0x69, 0x72,
	0x73, 0x74, 0x5f, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0e, 0x66, 0x69, 0x72, 0x73, 0x74, 0x4d, 0x65, 0x72, 0x67, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x72, 0x67,
	0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6c, 0x61,
	0x73, 0x74, 0x4d, 0x65, 0x72, 0x67, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x93, 0x02, 0x0a, 0x23,
	0x54, 0x72, 0x61, 0x63, 0x65, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x50, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x49,
	0x74, 0x65, 0x6d, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x12,
	0x25, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x0d, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x06,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x03, 0x28, 0x04, 0x52, 0x08, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x10, 0x74, 0x69, 0x67, 0x67, 0x65, 0x72, 0x5f, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0e, 0x74,
	0x69, 0x67, 0x67, 0x65, 0x72, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x12, 0x32, 0x0a,
	0x0d, 0x74, 0x69, 0x67, 0x67, 0x65, 0x72, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x53, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x52, 0x0c, 0x74, 0x69, 0x67, 0x67, 0x65, 0x72, 0x53, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x22, 0x5f, 0x0a, 0x1e, 0x54, 0x72, 0x61, 0x63, 0x65, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74,
	0x79, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x65, 0x72, 0x5f, 0x70,
	0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x70, 0x65, 0x72, 0x50, 0x61,
	0x67, 0x65, 0x22, 0xb4, 0x01, 0x0a, 0x1f, 0x54, 0x72, 0x61, 0x63, 0x65, 0x61, 0x62, 0x69, 0x6c,
	0x69, 0x74, 0x79, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x21, 0x0a, 0x0c,
	0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0b, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x67, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70,
	0x61, 0x67, 0x65, 0x12, 0x44, 0x0a, 0x07, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x18, 0x04,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x54, 0x72, 0x61,
	0x63, 0x65, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x49, 0x74, 0x65, 0x6d,
	0x52, 0x07, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x22, 0x7e, 0x0a, 0x1d, 0x54, 0x72, 0x61,
	0x63, 0x65, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x4d, 0x0a, 0x0c, 0x70, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x2a, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x54, 0x72, 0x61, 0x63, 0x65, 0x61, 0x62,
	0x69, 0x6c, 0x69, 0x74, 0x79, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x0b, 0x70, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0xa1, 0x06, 0x0a, 0x28, 0x44, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x54, 0x72, 0x61, 0x63, 0x65, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x5a, 0x0a, 0x0d, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e,
	0x61, 0x6c, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x35, 0x2e,
	0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x54, 0x72, 0x61, 0x63, 0x65, 0x61, 0x62, 0x69, 0x6c, 0x69,
	0x74, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x4f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x44, 0x61, 0x74, 0x61,
	0x49, 0x74, 0x65, 0x6d, 0x52, 0x0c, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x2e, 0x0a, 0x0b, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x64, 0x5f, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e,
	0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x0a, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x64, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x51, 0x0a, 0x0a, 0x73, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x69, 0x65, 0x73,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x54,
	0x72, 0x61, 0x63, 0x65, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x53, 0x74, 0x72,
	0x61, 0x74, 0x65, 0x67, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x0a, 0x73, 0x74, 0x72, 0x61, 0x74,
	0x65, 0x67, 0x69, 0x65, 0x73, 0x12, 0x84, 0x01, 0x0a, 0x19, 0x61, 0x6c, 0x6c, 0x5f, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x5f, 0x66, 0x6f, 0x72, 0x5f, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x5f, 0x64,
	0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4a, 0x2e, 0x6d, 0x65, 0x72, 0x67,
	0x65, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x72, 0x61, 0x63, 0x65, 0x61, 0x62, 0x69,
	0x6c, 0x69, 0x74, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x2e, 0x41, 0x6c, 0x6c, 0x53, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x46, 0x6f, 0x72, 0x4d, 0x65, 0x72, 0x67, 0x65, 0x44, 0x61, 0x74, 0x61,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x15, 0x61, 0x6c, 0x6c, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x46, 0x6f, 0x72, 0x4d, 0x65, 0x72, 0x67, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x80, 0x01, 0x0a,
	0x17, 0x61, 0x6c, 0x6c, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x66, 0x6f, 0x72, 0x5f,
	0x73, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x49,
	0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x72, 0x61,
	0x63, 0x65, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x2e,
	0x41, 0x6c, 0x6c, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x46, 0x6f, 0x72, 0x53, 0x74, 0x72, 0x61,
	0x74, 0x65, 0x67, 0x79, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x14, 0x61, 0x6c, 0x6c, 0x53, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x46, 0x6f, 0x72, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x12,
	0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x37, 0x0a, 0x0b, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x66, 0x69, 0x65, 0x6c,
	0x64, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65,
	0x2e, 0x4d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x52, 0x0a, 0x6c, 0x69, 0x73, 0x74, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x1a, 0x57, 0x0a, 0x1a,
	0x41, 0x6c, 0x6c, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x46, 0x6f, 0x72, 0x4d, 0x65, 0x72, 0x67,
	0x65, 0x44, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x23, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x6d, 0x65,
	0x72, 0x67, 0x65, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x56, 0x0a, 0x19, 0x41, 0x6c, 0x6c, 0x53, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x46, 0x6f, 0x72, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x23, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x53, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x6d, 0x0a,
	0x24, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x72, 0x61, 0x63, 0x65, 0x61, 0x62, 0x69, 0x6c,
	0x69, 0x74, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x45, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x44, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x54, 0x72, 0x61, 0x63, 0x65, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x22, 0x88, 0x01, 0x0a,
	0x0f, 0x4d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x12, 0x3a, 0x0a, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x22, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x4d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x1a, 0x39, 0x0a, 0x0b,
	0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x87, 0x02, 0x0a, 0x2e, 0x54, 0x72, 0x61, 0x63,
	0x65, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x4f, 0x72, 0x69, 0x67, 0x69, 0x6e,
	0x61, 0x6c, 0x44, 0x61, 0x74, 0x61, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e,
	0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x61,
	0x74, 0x61, 0x12, 0x37, 0x0a, 0x0b, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64,
	0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e,
	0x4d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x52,
	0x0a, 0x6c, 0x69, 0x73, 0x74, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x12, 0x44, 0x0a, 0x12, 0x6c,
	0x69, 0x73, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64,
	0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e,
	0x4d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x52,
	0x10, 0x6c, 0x69, 0x73, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x46, 0x69, 0x65, 0x6c, 0x64,
	0x73, 0x22, 0x38, 0x0a, 0x08, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x12, 0x16, 0x0a,
	0x06, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73,
	0x79, 0x73, 0x74, 0x65, 0x6d, 0x12, 0x14, 0x0a, 0x05, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x22, 0x92, 0x01, 0x0a, 0x08,
	0x50, 0x6f, 0x72, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x6f, 0x72, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x1a, 0x0a, 0x08,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75,
	0x72, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69,
	0x74, 0x6c, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x22, 0x35, 0x0a, 0x0c, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x6f, 0x72, 0x74, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x25, 0x0a, 0x05, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x0f, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x50, 0x6f, 0x72, 0x74, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x05, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x22, 0xe6, 0x29, 0x0a, 0x05, 0x41, 0x73, 0x73, 0x65,
	0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x17, 0x0a, 0x07, 0x61, 0x72, 0x65, 0x61, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x06, 0x61, 0x72, 0x65, 0x61, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x0a, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x64, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x04, 0x52,
	0x09, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x6e, 0x6f,
	0x64, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x04, 0x52, 0x07, 0x6e, 0x6f,
	0x64, 0x65, 0x49, 0x64, 0x73, 0x12, 0x22, 0x0a, 0x0d, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x64, 0x61,
	0x74, 0x61, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x74, 0x61,
	0x73, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x49, 0x64, 0x73, 0x12, 0x24, 0x0a, 0x0e, 0x61, 0x6c, 0x6c,
	0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28,
	0x04, 0x52, 0x0c, 0x61, 0x6c, 0x6c, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x73, 0x12,
	0x20, 0x0a, 0x0c, 0x61, 0x6c, 0x6c, 0x5f, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18,
	0x08, 0x20, 0x03, 0x28, 0x04, 0x52, 0x0a, 0x61, 0x6c, 0x6c, 0x4e, 0x6f, 0x64, 0x65, 0x49, 0x64,
	0x73, 0x12, 0x29, 0x0a, 0x11, 0x61, 0x6c, 0x6c, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x64, 0x61,
	0x74, 0x61, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0e, 0x61, 0x6c,
	0x6c, 0x54, 0x61, 0x73, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x49, 0x64, 0x73, 0x12, 0x26, 0x0a, 0x0f,
	0x61, 0x6c, 0x6c, 0x5f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x73, 0x18,
	0x0a, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x6c, 0x6c, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x49, 0x64, 0x73, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x02, 0x69, 0x70, 0x12, 0x17, 0x0a, 0x07, 0x69, 0x70, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x69, 0x70, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a,
	0x0a, 0x69, 0x70, 0x5f, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x0d, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x09, 0x69, 0x70, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x4d, 0x0a, 0x11,
	0x69, 0x70, 0x5f, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x18, 0x0e, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e,
	0x41, 0x73, 0x73, 0x65, 0x74, 0x2e, 0x49, 0x70, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x53,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0f, 0x69, 0x70, 0x53, 0x65,
	0x67, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x68,
	0x6f, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0f, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08,
	0x68, 0x6f, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x4a, 0x0a, 0x10, 0x68, 0x6f, 0x73, 0x74,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x10, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x20, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74,
	0x2e, 0x48, 0x6f, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x0e, 0x68, 0x6f, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x53, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x74, 0x68, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x11, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x65, 0x74, 0x68, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x47, 0x0a, 0x0f, 0x65, 0x74, 0x68, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x18, 0x12, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65,
	0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x2e, 0x45, 0x74, 0x68, 0x4e, 0x61, 0x6d, 0x65, 0x53, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0d, 0x65, 0x74, 0x68, 0x4e, 0x61,
	0x6d, 0x65, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x6f, 0x73, 0x18, 0x13,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x02, 0x6f, 0x73, 0x12, 0x37, 0x0a, 0x09, 0x6f, 0x73, 0x5f, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x14, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x6d, 0x65,
	0x72, 0x67, 0x65, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x2e, 0x4f, 0x73, 0x53, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x08, 0x6f, 0x73, 0x53, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x12, 0x16, 0x0a, 0x06, 0x6b, 0x65, 0x72, 0x6e, 0x65, 0x6c, 0x18, 0x15, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x06, 0x6b, 0x65, 0x72, 0x6e, 0x65, 0x6c, 0x12, 0x43, 0x0a, 0x0d, 0x6b, 0x65, 0x72,
	0x6e, 0x65, 0x6c, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x16, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x1e, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x2e, 0x4b,
	0x65, 0x72, 0x6e, 0x65, 0x6c, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x0c, 0x6b, 0x65, 0x72, 0x6e, 0x65, 0x6c, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x14,
	0x0a, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x19, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x12, 0x40, 0x0a, 0x0c, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x18, 0x1a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6d, 0x65, 0x72,
	0x67, 0x65, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0b, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x61, 0x6b, 0x65, 0x72, 0x18,
	0x1b, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x61, 0x6b, 0x65, 0x72, 0x12, 0x40, 0x0a, 0x0c,
	0x6d, 0x61, 0x6b, 0x65, 0x72, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x1c, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74,
	0x2e, 0x4d, 0x61, 0x6b, 0x65, 0x72, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x0b, 0x6d, 0x61, 0x6b, 0x65, 0x72, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x0e,
	0x0a, 0x02, 0x73, 0x6e, 0x18, 0x1d, 0x20, 0x03, 0x28, 0x09, 0x52, 0x02, 0x73, 0x6e, 0x12, 0x37,
	0x0a, 0x09, 0x73, 0x6e, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x1e, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x2e,
	0x53, 0x6e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x08, 0x73,
	0x6e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x61, 0x63, 0x18, 0x1f,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x61, 0x63, 0x12, 0x3a, 0x0a, 0x0a, 0x6d, 0x61, 0x63,
	0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x20, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e,
	0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x2e, 0x4d, 0x61, 0x63, 0x53,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x09, 0x6d, 0x61, 0x63, 0x53,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74,
	0x18, 0x21, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x12,
	0x46, 0x0a, 0x0e, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x18, 0x22, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e,
	0x41, 0x73, 0x73, 0x65, 0x74, 0x2e, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x53, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0d, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63,
	0x74, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x3a, 0x0a, 0x08, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x18, 0x23, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x6d, 0x65, 0x72, 0x67,
	0x65, 0x2e, 0x49, 0x70, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x08, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x12, 0x5c, 0x0a, 0x16, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x24, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x41, 0x73, 0x73, 0x65,
	0x74, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d,
	0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x14, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x53, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x12, 0x59, 0x0a, 0x15, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x6f, 0x77,
	0x6e, 0x65, 0x72, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x25, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x25, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x2e, 0x42,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x53, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x13, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x25, 0x0a, 0x04,
	0x6f, 0x70, 0x65, 0x72, 0x18, 0x26, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x6d, 0x65, 0x72,
	0x67, 0x65, 0x2e, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x42, 0x61, 0x73, 0x65, 0x52, 0x04, 0x6f,
	0x70, 0x65, 0x72, 0x12, 0x3d, 0x0a, 0x0b, 0x6f, 0x70, 0x65, 0x72, 0x5f, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x18, 0x27, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65,
	0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x53, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x53, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x5f, 0x72, 0x6f,
	0x6f, 0x6d, 0x18, 0x28, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e,
	0x65, 0x52, 0x6f, 0x6f, 0x6d, 0x12, 0x53, 0x0a, 0x13, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65,
	0x5f, 0x72, 0x6f, 0x6f, 0x6d, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x29, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x23, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74,
	0x2e, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x52, 0x6f, 0x6f, 0x6d, 0x53, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x11, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65,
	0x52, 0x6f, 0x6f, 0x6d, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x2a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x43, 0x0a, 0x0d, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x18, 0x2b, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x6d, 0x65, 0x72, 0x67,
	0x65, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x53, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0c, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x25, 0x0a, 0x05, 0x70, 0x6f, 0x72, 0x74, 0x73,
	0x18, 0x2c, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x50,
	0x6f, 0x72, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x12, 0x40,
	0x0a, 0x0c, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x2d,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x41, 0x73, 0x73,
	0x65, 0x74, 0x2e, 0x50, 0x6f, 0x72, 0x74, 0x73, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x0b, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18,
	0x2e, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x53, 0x69, 0x7a,
	0x65, 0x12, 0x50, 0x0a, 0x12, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x5f, 0x73, 0x69, 0x7a, 0x65,
	0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x2f, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e,
	0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x2e, 0x4d, 0x65, 0x6d, 0x6f,
	0x72, 0x79, 0x53, 0x69, 0x7a, 0x65, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x10, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x53, 0x69, 0x7a, 0x65, 0x53, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x5f, 0x75, 0x73,
	0x61, 0x67, 0x65, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x30, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0f,
	0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x55, 0x73, 0x61, 0x67, 0x65, 0x52, 0x61, 0x74, 0x65, 0x12,
	0x60, 0x0a, 0x18, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x5f, 0x75, 0x73, 0x61, 0x67, 0x65, 0x5f,
	0x72, 0x61, 0x74, 0x65, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x31, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x2e,
	0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x55, 0x73, 0x61, 0x67, 0x65, 0x52, 0x61, 0x74, 0x65, 0x53,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x15, 0x6d, 0x65, 0x6d, 0x6f,
	0x72, 0x79, 0x55, 0x73, 0x61, 0x67, 0x65, 0x52, 0x61, 0x74, 0x65, 0x53, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x70, 0x75, 0x5f, 0x6d, 0x61, 0x6b, 0x65, 0x72, 0x18, 0x32,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x63, 0x70, 0x75, 0x4d, 0x61, 0x6b, 0x65, 0x72, 0x12, 0x4a,
	0x0a, 0x10, 0x63, 0x70, 0x75, 0x5f, 0x6d, 0x61, 0x6b, 0x65, 0x72, 0x5f, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x18, 0x33, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65,
	0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x2e, 0x43, 0x70, 0x75, 0x4d, 0x61, 0x6b, 0x65, 0x72, 0x53,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0e, 0x63, 0x70, 0x75, 0x4d,
	0x61, 0x6b, 0x65, 0x72, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x70,
	0x75, 0x5f, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x18, 0x34, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x63,
	0x70, 0x75, 0x42, 0x72, 0x61, 0x6e, 0x64, 0x12, 0x4a, 0x0a, 0x10, 0x63, 0x70, 0x75, 0x5f, 0x62,
	0x72, 0x61, 0x6e, 0x64, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x35, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x20, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x2e,
	0x43, 0x70, 0x75, 0x42, 0x72, 0x61, 0x6e, 0x64, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x0e, 0x63, 0x70, 0x75, 0x42, 0x72, 0x61, 0x6e, 0x64, 0x53, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x70, 0x75, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x36, 0x20, 0x03, 0x28, 0x05, 0x52, 0x08, 0x63, 0x70, 0x75, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x4a, 0x0a, 0x10, 0x63, 0x70, 0x75, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x18, 0x37, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x6d, 0x65, 0x72,
	0x67, 0x65, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x2e, 0x43, 0x70, 0x75, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0e, 0x63, 0x70,
	0x75, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x1d, 0x0a, 0x0a,
	0x64, 0x69, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x38, 0x20, 0x03, 0x28, 0x05,
	0x52, 0x09, 0x64, 0x69, 0x73, 0x6b, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x4d, 0x0a, 0x11, 0x64,
	0x69, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x18, 0x39, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x41,
	0x73, 0x73, 0x65, 0x74, 0x2e, 0x44, 0x69, 0x73, 0x6b, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0f, 0x64, 0x69, 0x73, 0x6b, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x64, 0x69,
	0x73, 0x6b, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x3a, 0x20, 0x03, 0x28, 0x05, 0x52, 0x08, 0x64,
	0x69, 0x73, 0x6b, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x4a, 0x0a, 0x10, 0x64, 0x69, 0x73, 0x6b, 0x5f,
	0x73, 0x69, 0x7a, 0x65, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x3b, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x20, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x2e,
	0x44, 0x69, 0x73, 0x6b, 0x53, 0x69, 0x7a, 0x65, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x0e, 0x64, 0x69, 0x73, 0x6b, 0x53, 0x69, 0x7a, 0x65, 0x53, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x64, 0x69, 0x73, 0x6b, 0x5f, 0x75, 0x73, 0x61, 0x67,
	0x65, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x3c, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0d, 0x64, 0x69,
	0x73, 0x6b, 0x55, 0x73, 0x61, 0x67, 0x65, 0x52, 0x61, 0x74, 0x65, 0x12, 0x5a, 0x0a, 0x16, 0x64,
	0x69, 0x73, 0x6b, 0x5f, 0x75, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x5f, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x3d, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x65,
	0x72, 0x67, 0x65, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x2e, 0x44, 0x69, 0x73, 0x6b, 0x55, 0x73,
	0x61, 0x67, 0x65, 0x52, 0x61, 0x74, 0x65, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x13, 0x64, 0x69, 0x73, 0x6b, 0x55, 0x73, 0x61, 0x67, 0x65, 0x52, 0x61, 0x74,
	0x65, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x6c, 0x6f, 0x61, 0x64, 0x5f,
	0x61, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x18, 0x3e, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x6c,
	0x6f, 0x61, 0x64, 0x41, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x12, 0x53, 0x0a, 0x13, 0x6c, 0x6f,
	0x61, 0x64, 0x5f, 0x61, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x18, 0x3f, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e,
	0x41, 0x73, 0x73, 0x65, 0x74, 0x2e, 0x4c, 0x6f, 0x61, 0x64, 0x41, 0x76, 0x65, 0x72, 0x61, 0x67,
	0x65, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x11, 0x6c, 0x6f,
	0x61, 0x64, 0x41, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12,
	0x21, 0x0a, 0x0c, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x42, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74,
	0x18, 0x44, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41,
	0x74, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x75, 0x72, 0x67, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x45,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x75, 0x72, 0x67, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1d,
	0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x46, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a,
	0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x47, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x2e, 0x0a, 0x13,
	0x69, 0x73, 0x5f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x65, 0x78, 0x74, 0x72, 0x61, 0x63,
	0x74, 0x65, 0x64, 0x18, 0x48, 0x20, 0x01, 0x28, 0x05, 0x52, 0x11, 0x69, 0x73, 0x44, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x45, 0x78, 0x74, 0x72, 0x61, 0x63, 0x74, 0x65, 0x64, 0x12, 0x1f, 0x0a, 0x0b,
	0x6d, 0x65, 0x72, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x49, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0a, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x20, 0x0a,
	0x0c, 0x69, 0x70, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x4a, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x69, 0x70, 0x54, 0x79, 0x70, 0x65, 0x54, 0x65, 0x78, 0x74, 0x12,
	0x2a, 0x0a, 0x11, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f,
	0x74, 0x65, 0x78, 0x74, 0x18, 0x4b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x54, 0x65, 0x78, 0x74, 0x12, 0x1f, 0x0a, 0x04, 0x61,
	0x72, 0x65, 0x61, 0x18, 0x4c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x6d, 0x65, 0x72, 0x67,
	0x65, 0x2e, 0x41, 0x72, 0x65, 0x61, 0x52, 0x04, 0x61, 0x72, 0x65, 0x61, 0x12, 0x25, 0x0a, 0x06,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x4d, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x6d,
	0x65, 0x72, 0x67, 0x65, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x06, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x12, 0x2c, 0x0a, 0x0a, 0x61, 0x6c, 0x6c, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x18, 0x4e, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e,
	0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x09, 0x61, 0x6c, 0x6c, 0x53, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x12, 0x2e, 0x0a, 0x0a, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x73, 0x18,
	0x4f, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x52, 0x75,
	0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x72, 0x75, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x73, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18, 0x50, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x04, 0x74, 0x61, 0x67, 0x73, 0x12, 0x49, 0x0a, 0x0f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x51, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20,
	0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x2e, 0x42, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x0e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x1a, 0x42, 0x0a, 0x14, 0x49, 0x70, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x3a, 0x02, 0x38, 0x01, 0x1a, 0x41, 0x0a, 0x13, 0x48, 0x6f, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65,
	0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x40, 0x0a, 0x12, 0x45, 0x74, 0x68, 0x4e, 0x61,
	0x6d, 0x65, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a,
	0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x3b, 0x0a, 0x0d, 0x4f, 0x73, 0x53,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x3f, 0x0a, 0x11, 0x4b, 0x65, 0x72, 0x6e, 0x65, 0x6c,
	0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x3e, 0x0a, 0x10, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x3e, 0x0a, 0x10, 0x4d, 0x61, 0x6b, 0x65, 0x72,
	0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x3b, 0x0a, 0x0d, 0x53, 0x6e, 0x53, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x3a, 0x02, 0x38, 0x01, 0x1a, 0x3c, 0x0a, 0x0e, 0x4d, 0x61, 0x63, 0x53, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02,
	0x38, 0x01, 0x1a, 0x40, 0x0a, 0x12, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x53, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x3a, 0x02, 0x38, 0x01, 0x1a, 0x47, 0x0a, 0x19, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x46, 0x0a,
	0x18, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x53, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x3d, 0x0a, 0x0f, 0x4f, 0x70, 0x65, 0x72, 0x53, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x3a, 0x02, 0x38, 0x01, 0x1a, 0x44, 0x0a, 0x16, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x52,
	0x6f, 0x6f, 0x6d, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x3f, 0x0a, 0x11, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x53, 0x0a, 0x10, 0x50,
	0x6f, 0x72, 0x74, 0x73, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x29, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x13, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x6f, 0x72,
	0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01,
	0x1a, 0x43, 0x0a, 0x15, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x53, 0x69, 0x7a, 0x65, 0x53, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x48, 0x0a, 0x1a, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x55,
	0x73, 0x61, 0x67, 0x65, 0x52, 0x61, 0x74, 0x65, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a,
	0x41, 0x0a, 0x13, 0x43, 0x70, 0x75, 0x4d, 0x61, 0x6b, 0x65, 0x72, 0x53, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02,
	0x38, 0x01, 0x1a, 0x41, 0x0a, 0x13, 0x43, 0x70, 0x75, 0x42, 0x72, 0x61, 0x6e, 0x64, 0x53, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x41, 0x0a, 0x13, 0x43, 0x70, 0x75, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x42, 0x0a, 0x14, 0x44, 0x69, 0x73, 0x6b,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x41, 0x0a, 0x13,
	0x44, 0x69, 0x73, 0x6b, 0x53, 0x69, 0x7a, 0x65, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a,
	0x46, 0x0a, 0x18, 0x44, 0x69, 0x73, 0x6b, 0x55, 0x73, 0x61, 0x67, 0x65, 0x52, 0x61, 0x74, 0x65,
	0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x44, 0x0a, 0x16, 0x4c, 0x6f, 0x61, 0x64, 0x41,
	0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x61, 0x0a,
	0x13, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x34, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x49, 0x70,
	0x41, 0x64, 0x6d, 0x69, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01,
	0x22, 0x31, 0x0a, 0x09, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x12, 0x24, 0x0a,
	0x06, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0c, 0x2e,
	0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x06, 0x61, 0x73, 0x73,
	0x65, 0x74, 0x73, 0x22, 0x8b, 0x03, 0x0a, 0x2a, 0x54, 0x72, 0x61, 0x63, 0x65, 0x61, 0x62, 0x69,
	0x6c, 0x69, 0x74, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x49, 0x74,
	0x65, 0x6d, 0x12, 0x23, 0x0a, 0x0d, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x69, 0x65, 0x6c, 0x64,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x66, 0x69, 0x65,
	0x6c, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61,
	0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x69,
	0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x6e, 0x0a, 0x0f, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x5f, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x18, 0x04, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x45, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x54, 0x72, 0x61, 0x63, 0x65,
	0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67,
	0x79, 0x49, 0x74, 0x65, 0x6d, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x50, 0x72, 0x69, 0x6f,
	0x72, 0x69, 0x74, 0x79, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0e, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x50, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x12, 0x29, 0x0a, 0x10, 0x75, 0x6e, 0x74,
	0x72, 0x75, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x05, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x0f, 0x75, 0x6e, 0x74, 0x72, 0x75, 0x73, 0x74, 0x65, 0x64, 0x53, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x1a, 0x41,
	0x0a, 0x13, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x50, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x22, 0xca, 0x01, 0x0a, 0x2a, 0x54, 0x72, 0x61, 0x63, 0x65, 0x61, 0x62, 0x69, 0x6c, 0x69,
	0x74, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x56, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x1d, 0x0a, 0x0a, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x03, 0x52, 0x09, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x73, 0x12, 0x19,
	0x0a, 0x08, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x03,
	0x52, 0x07, 0x6e, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x73, 0x12, 0x22, 0x0a, 0x0d, 0x74, 0x61, 0x73,
	0x6b, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x0b, 0x74, 0x61, 0x73, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x49, 0x64, 0x73, 0x12, 0x1f, 0x0a,
	0x0b, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x05, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x0a, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x64, 0x73, 0x22, 0xec,
	0x06, 0x0a, 0x24, 0x49, 0x70, 0x54, 0x72, 0x61, 0x63, 0x65, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74,
	0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x5a, 0x0a, 0x0d, 0x6f, 0x72, 0x69, 0x67, 0x69,
	0x6e, 0x61, 0x6c, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x35,
	0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x54, 0x72, 0x61, 0x63, 0x65, 0x61, 0x62, 0x69, 0x6c,
	0x69, 0x74, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x4f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x44, 0x61, 0x74,
	0x61, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x0c, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x44,
	0x61, 0x74, 0x61, 0x12, 0x2d, 0x0a, 0x0b, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x64, 0x5f, 0x64, 0x61,
	0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65,
	0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x0a, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x64, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x51, 0x0a, 0x0a, 0x73, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x69, 0x65, 0x73,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x54,
	0x72, 0x61, 0x63, 0x65, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x53, 0x74, 0x72,
	0x61, 0x74, 0x65, 0x67, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x0a, 0x73, 0x74, 0x72, 0x61, 0x74,
	0x65, 0x67, 0x69, 0x65, 0x73, 0x12, 0x80, 0x01, 0x0a, 0x19, 0x61, 0x6c, 0x6c, 0x5f, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x5f, 0x66, 0x6f, 0x72, 0x5f, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x5f, 0x64,
	0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x46, 0x2e, 0x6d, 0x65, 0x72, 0x67,
	0x65, 0x2e, 0x49, 0x70, 0x54, 0x72, 0x61, 0x63, 0x65, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x2e, 0x41, 0x6c, 0x6c, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x46, 0x6f, 0x72, 0x4d, 0x65, 0x72, 0x67, 0x65, 0x44, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x15, 0x61, 0x6c, 0x6c, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x46, 0x6f, 0x72, 0x4d,
	0x65, 0x72, 0x67, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x7c, 0x0a, 0x17, 0x61, 0x6c, 0x6c, 0x5f,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x66, 0x6f, 0x72, 0x5f, 0x73, 0x74, 0x72, 0x61, 0x74,
	0x65, 0x67, 0x79, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x45, 0x2e, 0x6d, 0x65, 0x72, 0x67,
	0x65, 0x2e, 0x49, 0x70, 0x54, 0x72, 0x61, 0x63, 0x65, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x2e, 0x41, 0x6c, 0x6c, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x46, 0x6f, 0x72, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x14, 0x61, 0x6c, 0x6c, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x46, 0x6f, 0x72, 0x53, 0x74,
	0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x37, 0x0a, 0x0b, 0x6c, 0x69,
	0x73, 0x74, 0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x16, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x4d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x52, 0x0a, 0x6c, 0x69, 0x73, 0x74, 0x46, 0x69, 0x65,
	0x6c, 0x64, 0x73, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x57, 0x0a, 0x0e, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x76, 0x61, 0x6c,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x6d, 0x65,
	0x72, 0x67, 0x65, 0x2e, 0x54, 0x72, 0x61, 0x63, 0x65, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x56, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c,
	0x66, 0x69, 0x65, 0x6c, 0x64, 0x56, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x1a, 0x57, 0x0a, 0x1a,
	0x41, 0x6c, 0x6c, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x46, 0x6f, 0x72, 0x4d, 0x65, 0x72, 0x67,
	0x65, 0x44, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x23, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x6d, 0x65,
	0x72, 0x67, 0x65, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x56, 0x0a, 0x19, 0x41, 0x6c, 0x6c, 0x53, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x46, 0x6f, 0x72, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x23, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x53, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x65, 0x0a,
	0x20, 0x49, 0x70, 0x54, 0x72, 0x61, 0x63, 0x65, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x41, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x2b, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x49, 0x70, 0x54, 0x72, 0x61, 0x63, 0x65,
	0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x05, 0x69,
	0x74, 0x65, 0x6d, 0x73, 0x22, 0xbf, 0x02, 0x0a, 0x16, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x42,
	0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x1a, 0x0a, 0x08, 0x68, 0x6f, 0x73, 0x74, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x08, 0x68, 0x6f, 0x73, 0x74, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x73,
	0x6e, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x02, 0x73, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x6d,
	0x61, 0x63, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x61, 0x63, 0x12, 0x1b, 0x0a,
	0x09, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x03, 0x28, 0x04,
	0x52, 0x08, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x06, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x6d, 0x65, 0x72,
	0x67, 0x65, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x12, 0x17, 0x0a, 0x07, 0x61, 0x72, 0x65, 0x61, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x03,
	0x28, 0x04, 0x52, 0x06, 0x61, 0x72, 0x65, 0x61, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x04, 0x61, 0x72,
	0x65, 0x61, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65,
	0x2e, 0x41, 0x72, 0x65, 0x61, 0x52, 0x04, 0x61, 0x72, 0x65, 0x61, 0x12, 0x0e, 0x0a, 0x02, 0x6f,
	0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x09, 0x52, 0x02, 0x6f, 0x73, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x70, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x12, 0x21, 0x0a, 0x0c, 0x6d,
	0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x5f, 0x72, 0x6f, 0x6f, 0x6d, 0x18, 0x0b, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x0b, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x52, 0x6f, 0x6f, 0x6d, 0x12, 0x12,
	0x0a, 0x04, 0x6f, 0x70, 0x65, 0x72, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x6f, 0x70,
	0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x22, 0xb3, 0x01, 0x0a, 0x1b, 0x44, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x49, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x47, 0x0a, 0x07, 0x69, 0x70, 0x5f, 0x6c, 0x69, 0x73,
	0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e,
	0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x49, 0x70, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x49, 0x70, 0x4c, 0x69,
	0x73, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x69, 0x70, 0x4c, 0x69, 0x73, 0x74, 0x1a,
	0x4b, 0x0a, 0x0b, 0x49, 0x70, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x26, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x10, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x73, 0x73, 0x65,
	0x74, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x87, 0x02, 0x0a,
	0x18, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x45, 0x74,
	0x68, 0x49, 0x6e, 0x66, 0x6f, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a,
	0x03, 0x6d, 0x61, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x61, 0x63, 0x12,
	0x12, 0x0a, 0x04, 0x69, 0x70, 0x76, 0x34, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69,
	0x70, 0x76, 0x34, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x70, 0x76, 0x36, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x69, 0x70, 0x76, 0x36, 0x12, 0x18, 0x0a, 0x07, 0x67, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x12, 0x18, 0x0a, 0x07, 0x6e, 0x65, 0x74, 0x6d, 0x61, 0x73, 0x6b, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x6e, 0x65, 0x74, 0x6d, 0x61, 0x73, 0x6b, 0x12, 0x1d, 0x0a, 0x0a, 0x64,
	0x6e, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x18, 0x07, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x09, 0x64, 0x6e, 0x73, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x12, 0x1c, 0x0a, 0x09, 0x62, 0x72,
	0x6f, 0x61, 0x64, 0x63, 0x61, 0x73, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x62,
	0x72, 0x6f, 0x61, 0x64, 0x63, 0x61, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x70, 0x65, 0x65,
	0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x73, 0x70, 0x65, 0x65, 0x64, 0x12, 0x16,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x5a, 0x0a, 0x1c, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x52, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x45, 0x74, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3a, 0x0a, 0x08, 0x65, 0x74, 0x68, 0x5f, 0x6c, 0x69,
	0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65,
	0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x45, 0x74,
	0x68, 0x49, 0x6e, 0x66, 0x6f, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x07, 0x65, 0x74, 0x68, 0x4c, 0x69,
	0x73, 0x74, 0x22, 0xf9, 0x02, 0x0a, 0x22, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x72, 0x61,
	0x63, 0x65, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x42, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x22, 0x0a, 0x0d, 0x61, 0x6c, 0x6c,
	0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x03, 0x28, 0x04,
	0x52, 0x0b, 0x61, 0x6c, 0x6c, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x12, 0x1b, 0x0a,
	0x09, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x03, 0x28, 0x04,
	0x52, 0x08, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x0a, 0x61, 0x6c,
	0x6c, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0d,
	0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x09, 0x61,
	0x6c, 0x6c, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x25, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65,
	0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12,
	0x28, 0x0a, 0x10, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x66, 0x69, 0x72, 0x73, 0x74,
	0x4d, 0x65, 0x72, 0x67, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x6c, 0x61, 0x73,
	0x74, 0x5f, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x6c, 0x61, 0x73, 0x74, 0x4d, 0x65, 0x72, 0x67, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x07,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x4b, 0x65, 0x79, 0x12, 0x0e,
	0x0a, 0x02, 0x73, 0x6e, 0x18, 0x08, 0x20, 0x03, 0x28, 0x09, 0x52, 0x02, 0x73, 0x6e, 0x12, 0x1a,
	0x0a, 0x08, 0x68, 0x6f, 0x73, 0x74, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x08, 0x68, 0x6f, 0x73, 0x74, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x61,
	0x63, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x61, 0x63, 0x12, 0x10, 0x0a, 0x03,
	0x66, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x66, 0x69, 0x64, 0x32, 0xe5,
	0x10, 0x0a, 0x05, 0x4d, 0x65, 0x72, 0x67, 0x65, 0x12, 0x43, 0x0a, 0x0a, 0x54, 0x65, 0x73, 0x74,
	0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x18, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x54,
	0x65, 0x73, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x19, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x4d, 0x65, 0x74,
	0x68, 0x6f, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x64, 0x0a,
	0x15, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x65, 0x72, 0x67, 0x65, 0x44, 0x61, 0x74, 0x61,
	0x54, 0x6f, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x23, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x65, 0x72, 0x67, 0x65, 0x44, 0x61, 0x74, 0x61, 0x54, 0x6f,
	0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x6d, 0x65,
	0x72, 0x67, 0x65, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x65, 0x72, 0x67, 0x65, 0x44,
	0x61, 0x74, 0x61, 0x54, 0x6f, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0x46, 0x0a, 0x0b, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x4d, 0x65, 0x72,
	0x67, 0x65, 0x12, 0x19, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x4d, 0x61, 0x6e, 0x75, 0x61,
	0x6c, 0x4d, 0x65, 0x72, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1a, 0x2e,
	0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x4d, 0x65, 0x72, 0x67,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x58, 0x0a, 0x11, 0x4d,
	0x61, 0x6e, 0x75, 0x61, 0x6c, 0x43, 0x61, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x1f, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x43,
	0x61, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x20, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c,
	0x43, 0x61, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x3f, 0x0a, 0x0e, 0x52, 0x65, 0x63, 0x61, 0x6c, 0x52, 0x69,
	0x73, 0x6b, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x0c, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x1d, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x52, 0x65,
	0x63, 0x61, 0x6c, 0x52, 0x69, 0x73, 0x6b, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x5b, 0x0a, 0x12, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65,
	0x72, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x54, 0x61, 0x67, 0x67, 0x65, 0x72, 0x12, 0x20, 0x2e, 0x6d,
	0x65, 0x72, 0x67, 0x65, 0x2e, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x46, 0x69, 0x65, 0x6c,
	0x64, 0x54, 0x61, 0x67, 0x67, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x21,
	0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x46, 0x69,
	0x65, 0x6c, 0x64, 0x54, 0x61, 0x67, 0x67, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0x59, 0x0a, 0x14, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x4d, 0x65,
	0x72, 0x67, 0x65, 0x46, 0x6f, 0x72, 0x41, 0x73, 0x73, 0x65, 0x74, 0x12, 0x22, 0x2e, 0x6d, 0x65,
	0x72, 0x67, 0x65, 0x2e, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x4d, 0x65, 0x72, 0x67, 0x65,
	0x46, 0x6f, 0x72, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x1b, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x4d,
	0x65, 0x72, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x57,
	0x0a, 0x13, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x4d, 0x65, 0x72, 0x67, 0x65, 0x46, 0x6f,
	0x72, 0x56, 0x75, 0x6c, 0x6e, 0x12, 0x21, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x54, 0x72,
	0x69, 0x67, 0x67, 0x65, 0x72, 0x4d, 0x65, 0x72, 0x67, 0x65, 0x46, 0x6f, 0x72, 0x56, 0x75, 0x6c,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65,
	0x2e, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x4d, 0x65, 0x72, 0x67, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x59, 0x0a, 0x14, 0x54, 0x72, 0x69, 0x67, 0x67,
	0x65, 0x72, 0x4d, 0x65, 0x72, 0x67, 0x65, 0x46, 0x6f, 0x72, 0x53, 0x74, 0x61, 0x66, 0x66, 0x12,
	0x22, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x4d,
	0x65, 0x72, 0x67, 0x65, 0x46, 0x6f, 0x72, 0x53, 0x74, 0x61, 0x66, 0x66, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x54, 0x72, 0x69, 0x67,
	0x67, 0x65, 0x72, 0x4d, 0x65, 0x72, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x00, 0x12, 0x5b, 0x0a, 0x15, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x4d, 0x65, 0x72,
	0x67, 0x65, 0x46, 0x6f, 0x72, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x23, 0x2e, 0x6d, 0x65,
	0x72, 0x67, 0x65, 0x2e, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x4d, 0x65, 0x72, 0x67, 0x65,
	0x46, 0x6f, 0x72, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x1b, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72,
	0x4d, 0x65, 0x72, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12,
	0x41, 0x0a, 0x0b, 0x49, 0x70, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x14,
	0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x49, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x1a, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x49, 0x70, 0x41,
	0x64, 0x6d, 0x69, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x00, 0x12, 0x45, 0x0a, 0x0d, 0x49, 0x70, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x14, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x49, 0x70, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x6d, 0x65, 0x72, 0x67,
	0x65, 0x2e, 0x49, 0x70, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x3f, 0x0a, 0x0a, 0x49, 0x70, 0x50,
	0x6f, 0x72, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x14, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e,
	0x49, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x19, 0x2e,
	0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x49, 0x70, 0x50, 0x6f, 0x72, 0x74, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x35, 0x0a, 0x0a, 0x49, 0x70,
	0x48, 0x6f, 0x73, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x14, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65,
	0x2e, 0x49, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x0f,
	0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x48, 0x6f, 0x73, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x22,
	0x00, 0x12, 0x47, 0x0a, 0x0e, 0x49, 0x70, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x14, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x49, 0x70, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1d, 0x2e, 0x6d, 0x65, 0x72, 0x67,
	0x65, 0x2e, 0x49, 0x70, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x57, 0x0a, 0x16, 0x49, 0x70,
	0x54, 0x72, 0x61, 0x63, 0x65, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x42, 0x61, 0x73, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x14, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x49, 0x70, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x25, 0x2e, 0x6d, 0x65, 0x72,
	0x67, 0x65, 0x2e, 0x49, 0x70, 0x54, 0x72, 0x61, 0x63, 0x65, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74,
	0x79, 0x42, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0x6c, 0x0a, 0x19, 0x49, 0x70, 0x54, 0x72, 0x61, 0x63, 0x65, 0x61, 0x62,
	0x69, 0x6c, 0x69, 0x74, 0x79, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x25, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x54, 0x72, 0x61, 0x63, 0x65, 0x61, 0x62,
	0x69, 0x6c, 0x69, 0x74, 0x79, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e,
	0x54, 0x72, 0x61, 0x63, 0x65, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x50, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x12, 0x6b, 0x0a, 0x18, 0x49, 0x70, 0x54, 0x72, 0x61, 0x63, 0x65, 0x61, 0x62, 0x69, 0x6c,
	0x69, 0x74, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x24, 0x2e,
	0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x54, 0x72, 0x61, 0x63, 0x65, 0x61, 0x62, 0x69, 0x6c, 0x69,
	0x74, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x49, 0x70, 0x54, 0x72,
	0x61, 0x63, 0x65, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x4b,
	0x0a, 0x0e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x42, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x18, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1d, 0x2e, 0x6d, 0x65, 0x72,
	0x67, 0x65, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x42, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x55, 0x0a, 0x13, 0x44,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x49, 0x70, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x18, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x6d,
	0x65, 0x72, 0x67, 0x65, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74,
	0x65, 0x64, 0x49, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x00, 0x12, 0x3d, 0x0a, 0x0e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x48, 0x6f, 0x73, 0x74,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x18, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x44, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x0f,
	0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x48, 0x6f, 0x73, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x22,
	0x00, 0x12, 0x57, 0x0a, 0x14, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74,
	0x65, 0x64, 0x45, 0x74, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x18, 0x2e, 0x6d, 0x65, 0x72, 0x67,
	0x65, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x44, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x45, 0x74, 0x68, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x63, 0x0a, 0x1a, 0x44, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x54, 0x72, 0x61, 0x63, 0x65, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79,
	0x42, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x18, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65,
	0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x29, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x54, 0x72, 0x61, 0x63, 0x65, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x42, 0x61, 0x73,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12,
	0x70, 0x0a, 0x1d, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x72, 0x61, 0x63, 0x65, 0x61, 0x62,
	0x69, 0x6c, 0x69, 0x74, 0x79, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x25, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x54, 0x72, 0x61, 0x63, 0x65, 0x61, 0x62,
	0x69, 0x6c, 0x69, 0x74, 0x79, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e,
	0x54, 0x72, 0x61, 0x63, 0x65, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x50, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x12, 0x73, 0x0a, 0x1c, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x72, 0x61, 0x63, 0x65,
	0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x24, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e, 0x54, 0x72, 0x61, 0x63, 0x65, 0x61,
	0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x2e,
	0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x72, 0x61, 0x63, 0x65, 0x61, 0x62, 0x69, 0x6c, 0x69,
	0x74, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x42, 0x0f, 0x5a, 0x0d, 0x2e, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x3b, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_merge_proto_rawDescOnce sync.Once
	file_merge_proto_rawDescData = file_merge_proto_rawDesc
)

func file_merge_proto_rawDescGZIP() []byte {
	file_merge_proto_rawDescOnce.Do(func() {
		file_merge_proto_rawDescData = protoimpl.X.CompressGZIP(file_merge_proto_rawDescData)
	})
	return file_merge_proto_rawDescData
}

var file_merge_proto_msgTypes = make([]protoimpl.MessageInfo, 107)
var file_merge_proto_goTypes = []interface{}{
	(*Empty)(nil),                                          // 0: merge.Empty
	(*Source)(nil),                                         // 1: merge.Source
	(*RuleInfo)(nil),                                       // 2: merge.RuleInfo
	(*Area)(nil),                                           // 3: merge.Area
	(*TestMethodRequest)(nil),                              // 4: merge.TestMethodRequest
	(*TestMethodResponse)(nil),                             // 5: merge.TestMethodResponse
	(*TriggerFieldTaggerRequest)(nil),                      // 6: merge.TriggerFieldTaggerRequest
	(*TriggerFieldTaggerResponse)(nil),                     // 7: merge.TriggerFieldTaggerResponse
	(*TriggerMergeBaseRequest)(nil),                        // 8: merge.TriggerMergeBaseRequest
	(*IpInfo)(nil),                                         // 9: merge.IpInfo
	(*DataRangeByTask)(nil),                                // 10: merge.DataRangeByTask
	(*TriggerMergeForAssetRequest)(nil),                    // 11: merge.TriggerMergeForAssetRequest
	(*TriggerMergeForDeviceRequest)(nil),                   // 12: merge.TriggerMergeForDeviceRequest
	(*TriggerMergeForVulnRequest)(nil),                     // 13: merge.TriggerMergeForVulnRequest
	(*TriggerMergeForStaffRequest)(nil),                    // 14: merge.TriggerMergeForStaffRequest
	(*TriggerMergeResponse)(nil),                           // 15: merge.TriggerMergeResponse
	(*UpdateMergeDataToTaskRequest)(nil),                   // 16: merge.UpdateMergeDataToTaskRequest
	(*RecalRiskLevelResponse)(nil),                         // 17: merge.RecalRiskLevelResponse
	(*UpdateMergeDataToTaskResponse)(nil),                  // 18: merge.UpdateMergeDataToTaskResponse
	(*ManualMergeRequest)(nil),                             // 19: merge.ManualMergeRequest
	(*ManualMergeResult)(nil),                              // 20: merge.ManualMergeResult
	(*ManualMergeResponse)(nil),                            // 21: merge.ManualMergeResponse
	(*ReMergeResponse)(nil),                                // 22: merge.ReMergeResponse
	(*ManualCalibrationRequest)(nil),                       // 23: merge.ManualCalibrationRequest
	(*ManualCalibrationResponse)(nil),                      // 24: merge.ManualCalibrationResponse
	(*IPInfo)(nil),                                         // 25: merge.IPInfo
	(*BusinessInfo)(nil),                                   // 26: merge.BusinessInfo
	(*Department)(nil),                                     // 27: merge.Department
	(*OperInfo)(nil),                                       // 28: merge.OperInfo
	(*DeviceBusiness)(nil),                                 // 29: merge.DeviceBusiness
	(*DeviceOpers)(nil),                                    // 30: merge.DeviceOpers
	(*Device)(nil),                                         // 31: merge.Device
	(*IpInfoRequest)(nil),                                  // 32: merge.IpInfoRequest
	(*DeviceInfoRequest)(nil),                              // 33: merge.DeviceInfoRequest
	(*DepartmentBase)(nil),                                 // 34: merge.DepartmentBase
	(*PersonFindInfo)(nil),                                 // 35: merge.PersonFindInfo
	(*PersonBase)(nil),                                     // 36: merge.PersonBase
	(*PersonWithMapping)(nil),                              // 37: merge.PersonWithMapping
	(*IpAdminInfoResponseItem)(nil),                        // 38: merge.IpAdminInfoResponseItem
	(*IpAdminInfoResponseList)(nil),                        // 39: merge.IpAdminInfoResponseList
	(*PortMappingList)(nil),                                // 40: merge.PortMappingList
	(*PortMapping)(nil),                                    // 41: merge.PortMapping
	(*PersonInfo)(nil),                                     // 42: merge.PersonInfo
	(*IpAdminInfoResponse)(nil),                            // 43: merge.IpAdminInfoResponse
	(*OperDepartment)(nil),                                 // 44: merge.OperDepartment
	(*IpAccountInfoResponse)(nil),                          // 45: merge.IpAccountInfoResponse
	(*IpPortInfoResponseItem)(nil),                         // 46: merge.IpPortInfoResponseItem
	(*IpPortInfoResponse)(nil),                             // 47: merge.IpPortInfoResponse
	(*HostInfo)(nil),                                       // 48: merge.HostInfo
	(*IpSecurityInfoResponse)(nil),                         // 49: merge.IpSecurityInfoResponse
	(*IpTraceabilityBaseInfoResponse)(nil),                 // 50: merge.IpTraceabilityBaseInfoResponse
	(*TraceabilityProcessInfoResponseItem)(nil),            // 51: merge.TraceabilityProcessInfoResponseItem
	(*TraceabilityProcessInfoRequest)(nil),                 // 52: merge.TraceabilityProcessInfoRequest
	(*TraceabilityProcessInfoResponse)(nil),                // 53: merge.TraceabilityProcessInfoResponse
	(*TraceabilityDetailInfoRequest)(nil),                  // 54: merge.TraceabilityDetailInfoRequest
	(*DeviceTraceabilityDetailInfoResponseItem)(nil),       // 55: merge.DeviceTraceabilityDetailInfoResponseItem
	(*DeviceTraceabilityDetailInfoResponse)(nil),           // 56: merge.DeviceTraceabilityDetailInfoResponse
	(*MapStringString)(nil),                                // 57: merge.MapStringString
	(*TraceabilityDetailInfoResponseOriginalDataItem)(nil), // 58: merge.TraceabilityDetailInfoResponseOriginalDataItem
	(*Business)(nil),                                       // 59: merge.Business
	(*PortInfo)(nil),                                       // 60: merge.PortInfo
	(*ListPortInfo)(nil),                                   // 61: merge.ListPortInfo
	(*Asset)(nil),                                          // 62: merge.Asset
	(*ListAsset)(nil),                                      // 63: merge.ListAsset
	(*TraceabilityDetailInfoResponseStrategyItem)(nil),     // 64: merge.TraceabilityDetailInfoResponseStrategyItem
	(*TraceabilityDetailInfoResponseFieldValInfo)(nil),     // 65: merge.TraceabilityDetailInfoResponseFieldValInfo
	(*IpTraceabilityDetailInfoResponseItem)(nil),           // 66: merge.IpTraceabilityDetailInfoResponseItem
	(*IpTraceabilityDetailInfoResponse)(nil),               // 67: merge.IpTraceabilityDetailInfoResponse
	(*DeviceBaseInfoResponse)(nil),                         // 68: merge.DeviceBaseInfoResponse
	(*DeviceRelatedIpInfoResponse)(nil),                    // 69: merge.DeviceRelatedIpInfoResponse
	(*DeviceRelatedEthInfoItem)(nil),                       // 70: merge.DeviceRelatedEthInfoItem
	(*DeviceRelatedEthInfoResponse)(nil),                   // 71: merge.DeviceRelatedEthInfoResponse
	(*DeviceTraceabilityBaseInfoResponse)(nil),             // 72: merge.DeviceTraceabilityBaseInfoResponse
	nil, // 73: merge.ManualCalibrationRequest.ValuesEntry
	nil, // 74: merge.DeviceTraceabilityDetailInfoResponseItem.AllSourceForMergeDataEntry
	nil, // 75: merge.DeviceTraceabilityDetailInfoResponseItem.AllSourceForStrategyEntry
	nil, // 76: merge.MapStringString.FieldsEntry
	nil, // 77: merge.Asset.IpSegmentSourceEntry
	nil, // 78: merge.Asset.HostNameSourceEntry
	nil, // 79: merge.Asset.EthNameSourceEntry
	nil, // 80: merge.Asset.OsSourceEntry
	nil, // 81: merge.Asset.KernelSourceEntry
	nil, // 82: merge.Asset.ModelSourceEntry
	nil, // 83: merge.Asset.MakerSourceEntry
	nil, // 84: merge.Asset.SnSourceEntry
	nil, // 85: merge.Asset.MacSourceEntry
	nil, // 86: merge.Asset.ProductSourceEntry
	nil, // 87: merge.Asset.BusinessSystemSourceEntry
	nil, // 88: merge.Asset.BusinessOwnerSourceEntry
	nil, // 89: merge.Asset.OperSourceEntry
	nil, // 90: merge.Asset.MachineRoomSourceEntry
	nil, // 91: merge.Asset.StatusSourceEntry
	nil, // 92: merge.Asset.PortsSourceEntry
	nil, // 93: merge.Asset.MemorySizeSourceEntry
	nil, // 94: merge.Asset.MemoryUsageRateSourceEntry
	nil, // 95: merge.Asset.CpuMakerSourceEntry
	nil, // 96: merge.Asset.CpuBrandSourceEntry
	nil, // 97: merge.Asset.CpuCountSourceEntry
	nil, // 98: merge.Asset.DiskCountSourceEntry
	nil, // 99: merge.Asset.DiskSizeSourceEntry
	nil, // 100: merge.Asset.DiskUsageRateSourceEntry
	nil, // 101: merge.Asset.LoadAverageSourceEntry
	nil, // 102: merge.Asset.BusinessSourceEntry
	nil, // 103: merge.TraceabilityDetailInfoResponseStrategyItem.SourcePriorityEntry
	nil, // 104: merge.IpTraceabilityDetailInfoResponseItem.AllSourceForMergeDataEntry
	nil, // 105: merge.IpTraceabilityDetailInfoResponseItem.AllSourceForStrategyEntry
	nil, // 106: merge.DeviceRelatedIpInfoResponse.IpListEntry
}
var file_merge_proto_depIdxs = []int32{
	8,   // 0: merge.TriggerMergeForAssetRequest.trigger_merge_base_request:type_name -> merge.TriggerMergeBaseRequest
	9,   // 1: merge.TriggerMergeForAssetRequest.ip_infos:type_name -> merge.IpInfo
	10,  // 2: merge.TriggerMergeForAssetRequest.data_range_by_task:type_name -> merge.DataRangeByTask
	8,   // 3: merge.TriggerMergeForDeviceRequest.trigger_merge_base_request:type_name -> merge.TriggerMergeBaseRequest
	10,  // 4: merge.TriggerMergeForDeviceRequest.data_range_by_task:type_name -> merge.DataRangeByTask
	8,   // 5: merge.TriggerMergeForVulnRequest.trigger_merge_base_request:type_name -> merge.TriggerMergeBaseRequest
	10,  // 6: merge.TriggerMergeForVulnRequest.data_range_by_task:type_name -> merge.DataRangeByTask
	8,   // 7: merge.TriggerMergeForStaffRequest.trigger_merge_base_request:type_name -> merge.TriggerMergeBaseRequest
	10,  // 8: merge.TriggerMergeForStaffRequest.data_range_by_task:type_name -> merge.DataRangeByTask
	20,  // 9: merge.ManualMergeResponse.results:type_name -> merge.ManualMergeResult
	73,  // 10: merge.ManualCalibrationRequest.values:type_name -> merge.ManualCalibrationRequest.ValuesEntry
	27,  // 11: merge.Department.parents:type_name -> merge.Department
	26,  // 12: merge.DeviceBusiness.business:type_name -> merge.BusinessInfo
	27,  // 13: merge.DeviceBusiness.business_department:type_name -> merge.Department
	25,  // 14: merge.DeviceBusiness.ip:type_name -> merge.IPInfo
	28,  // 15: merge.DeviceOpers.oper_info:type_name -> merge.OperInfo
	27,  // 16: merge.DeviceOpers.oper_department:type_name -> merge.Department
	25,  // 17: merge.DeviceOpers.ip:type_name -> merge.IPInfo
	1,   // 18: merge.Device.source:type_name -> merge.Source
	3,   // 19: merge.Device.area:type_name -> merge.Area
	29,  // 20: merge.Device.business:type_name -> merge.DeviceBusiness
	30,  // 21: merge.Device.opers:type_name -> merge.DeviceOpers
	34,  // 22: merge.DepartmentBase.parents:type_name -> merge.DepartmentBase
	35,  // 23: merge.PersonBase.find_info:type_name -> merge.PersonFindInfo
	34,  // 24: merge.PersonBase.department:type_name -> merge.DepartmentBase
	36,  // 25: merge.IpAdminInfoResponseItem.person_base:type_name -> merge.PersonBase
	38,  // 26: merge.IpAdminInfoResponseList.admin:type_name -> merge.IpAdminInfoResponseItem
	41,  // 27: merge.PortMappingList.mappings:type_name -> merge.PortMapping
	38,  // 28: merge.IpAdminInfoResponse.admin:type_name -> merge.IpAdminInfoResponseItem
	36,  // 29: merge.IpAdminInfoResponse.oper:type_name -> merge.PersonBase
	44,  // 30: merge.IpAdminInfoResponse.oper_department:type_name -> merge.OperDepartment
	46,  // 31: merge.IpPortInfoResponse.port:type_name -> merge.IpPortInfoResponseItem
	1,   // 32: merge.IpTraceabilityBaseInfoResponse.all_source:type_name -> merge.Source
	1,   // 33: merge.IpTraceabilityBaseInfoResponse.source:type_name -> merge.Source
	1,   // 34: merge.TraceabilityProcessInfoResponseItem.source:type_name -> merge.Source
	1,   // 35: merge.TraceabilityProcessInfoResponseItem.tigger_source:type_name -> merge.Source
	51,  // 36: merge.TraceabilityProcessInfoResponse.process:type_name -> merge.TraceabilityProcessInfoResponseItem
	51,  // 37: merge.TraceabilityDetailInfoRequest.process_info:type_name -> merge.TraceabilityProcessInfoResponseItem
	58,  // 38: merge.DeviceTraceabilityDetailInfoResponseItem.original_data:type_name -> merge.TraceabilityDetailInfoResponseOriginalDataItem
	31,  // 39: merge.DeviceTraceabilityDetailInfoResponseItem.merged_data:type_name -> merge.Device
	64,  // 40: merge.DeviceTraceabilityDetailInfoResponseItem.strategies:type_name -> merge.TraceabilityDetailInfoResponseStrategyItem
	74,  // 41: merge.DeviceTraceabilityDetailInfoResponseItem.all_source_for_merge_data:type_name -> merge.DeviceTraceabilityDetailInfoResponseItem.AllSourceForMergeDataEntry
	75,  // 42: merge.DeviceTraceabilityDetailInfoResponseItem.all_source_for_strategy:type_name -> merge.DeviceTraceabilityDetailInfoResponseItem.AllSourceForStrategyEntry
	57,  // 43: merge.DeviceTraceabilityDetailInfoResponseItem.list_fields:type_name -> merge.MapStringString
	55,  // 44: merge.DeviceTraceabilityDetailInfoResponse.items:type_name -> merge.DeviceTraceabilityDetailInfoResponseItem
	76,  // 45: merge.MapStringString.fields:type_name -> merge.MapStringString.FieldsEntry
	1,   // 46: merge.TraceabilityDetailInfoResponseOriginalDataItem.source:type_name -> merge.Source
	57,  // 47: merge.TraceabilityDetailInfoResponseOriginalDataItem.list_fields:type_name -> merge.MapStringString
	57,  // 48: merge.TraceabilityDetailInfoResponseOriginalDataItem.list_detail_fields:type_name -> merge.MapStringString
	60,  // 49: merge.ListPortInfo.ports:type_name -> merge.PortInfo
	77,  // 50: merge.Asset.ip_segment_source:type_name -> merge.Asset.IpSegmentSourceEntry
	78,  // 51: merge.Asset.host_name_source:type_name -> merge.Asset.HostNameSourceEntry
	79,  // 52: merge.Asset.eth_name_source:type_name -> merge.Asset.EthNameSourceEntry
	80,  // 53: merge.Asset.os_source:type_name -> merge.Asset.OsSourceEntry
	81,  // 54: merge.Asset.kernel_source:type_name -> merge.Asset.KernelSourceEntry
	82,  // 55: merge.Asset.model_source:type_name -> merge.Asset.ModelSourceEntry
	83,  // 56: merge.Asset.maker_source:type_name -> merge.Asset.MakerSourceEntry
	84,  // 57: merge.Asset.sn_source:type_name -> merge.Asset.SnSourceEntry
	85,  // 58: merge.Asset.mac_source:type_name -> merge.Asset.MacSourceEntry
	86,  // 59: merge.Asset.product_source:type_name -> merge.Asset.ProductSourceEntry
	38,  // 60: merge.Asset.business:type_name -> merge.IpAdminInfoResponseItem
	87,  // 61: merge.Asset.business_system_source:type_name -> merge.Asset.BusinessSystemSourceEntry
	88,  // 62: merge.Asset.business_owner_source:type_name -> merge.Asset.BusinessOwnerSourceEntry
	36,  // 63: merge.Asset.oper:type_name -> merge.PersonBase
	89,  // 64: merge.Asset.oper_source:type_name -> merge.Asset.OperSourceEntry
	90,  // 65: merge.Asset.machine_room_source:type_name -> merge.Asset.MachineRoomSourceEntry
	91,  // 66: merge.Asset.status_source:type_name -> merge.Asset.StatusSourceEntry
	60,  // 67: merge.Asset.ports:type_name -> merge.PortInfo
	92,  // 68: merge.Asset.ports_source:type_name -> merge.Asset.PortsSourceEntry
	93,  // 69: merge.Asset.memory_size_source:type_name -> merge.Asset.MemorySizeSourceEntry
	94,  // 70: merge.Asset.memory_usage_rate_source:type_name -> merge.Asset.MemoryUsageRateSourceEntry
	95,  // 71: merge.Asset.cpu_maker_source:type_name -> merge.Asset.CpuMakerSourceEntry
	96,  // 72: merge.Asset.cpu_brand_source:type_name -> merge.Asset.CpuBrandSourceEntry
	97,  // 73: merge.Asset.cpu_count_source:type_name -> merge.Asset.CpuCountSourceEntry
	98,  // 74: merge.Asset.disk_count_source:type_name -> merge.Asset.DiskCountSourceEntry
	99,  // 75: merge.Asset.disk_size_source:type_name -> merge.Asset.DiskSizeSourceEntry
	100, // 76: merge.Asset.disk_usage_rate_source:type_name -> merge.Asset.DiskUsageRateSourceEntry
	101, // 77: merge.Asset.load_average_source:type_name -> merge.Asset.LoadAverageSourceEntry
	3,   // 78: merge.Asset.area:type_name -> merge.Area
	1,   // 79: merge.Asset.source:type_name -> merge.Source
	1,   // 80: merge.Asset.all_source:type_name -> merge.Source
	2,   // 81: merge.Asset.rule_infos:type_name -> merge.RuleInfo
	102, // 82: merge.Asset.business_source:type_name -> merge.Asset.BusinessSourceEntry
	62,  // 83: merge.ListAsset.assets:type_name -> merge.Asset
	103, // 84: merge.TraceabilityDetailInfoResponseStrategyItem.source_priority:type_name -> merge.TraceabilityDetailInfoResponseStrategyItem.SourcePriorityEntry
	58,  // 85: merge.IpTraceabilityDetailInfoResponseItem.original_data:type_name -> merge.TraceabilityDetailInfoResponseOriginalDataItem
	62,  // 86: merge.IpTraceabilityDetailInfoResponseItem.merged_data:type_name -> merge.Asset
	64,  // 87: merge.IpTraceabilityDetailInfoResponseItem.strategies:type_name -> merge.TraceabilityDetailInfoResponseStrategyItem
	104, // 88: merge.IpTraceabilityDetailInfoResponseItem.all_source_for_merge_data:type_name -> merge.IpTraceabilityDetailInfoResponseItem.AllSourceForMergeDataEntry
	105, // 89: merge.IpTraceabilityDetailInfoResponseItem.all_source_for_strategy:type_name -> merge.IpTraceabilityDetailInfoResponseItem.AllSourceForStrategyEntry
	57,  // 90: merge.IpTraceabilityDetailInfoResponseItem.list_fields:type_name -> merge.MapStringString
	65,  // 91: merge.IpTraceabilityDetailInfoResponseItem.field_val_info:type_name -> merge.TraceabilityDetailInfoResponseFieldValInfo
	66,  // 92: merge.IpTraceabilityDetailInfoResponse.items:type_name -> merge.IpTraceabilityDetailInfoResponseItem
	1,   // 93: merge.DeviceBaseInfoResponse.source:type_name -> merge.Source
	3,   // 94: merge.DeviceBaseInfoResponse.area:type_name -> merge.Area
	106, // 95: merge.DeviceRelatedIpInfoResponse.ip_list:type_name -> merge.DeviceRelatedIpInfoResponse.IpListEntry
	70,  // 96: merge.DeviceRelatedEthInfoResponse.eth_list:type_name -> merge.DeviceRelatedEthInfoItem
	1,   // 97: merge.DeviceTraceabilityBaseInfoResponse.all_source:type_name -> merge.Source
	1,   // 98: merge.DeviceTraceabilityBaseInfoResponse.source:type_name -> merge.Source
	1,   // 99: merge.DeviceTraceabilityDetailInfoResponseItem.AllSourceForMergeDataEntry.value:type_name -> merge.Source
	1,   // 100: merge.DeviceTraceabilityDetailInfoResponseItem.AllSourceForStrategyEntry.value:type_name -> merge.Source
	61,  // 101: merge.Asset.PortsSourceEntry.value:type_name -> merge.ListPortInfo
	39,  // 102: merge.Asset.BusinessSourceEntry.value:type_name -> merge.IpAdminInfoResponseList
	1,   // 103: merge.IpTraceabilityDetailInfoResponseItem.AllSourceForMergeDataEntry.value:type_name -> merge.Source
	1,   // 104: merge.IpTraceabilityDetailInfoResponseItem.AllSourceForStrategyEntry.value:type_name -> merge.Source
	63,  // 105: merge.DeviceRelatedIpInfoResponse.IpListEntry.value:type_name -> merge.ListAsset
	4,   // 106: merge.Merge.TestMethod:input_type -> merge.TestMethodRequest
	16,  // 107: merge.Merge.UpdateMergeDataToTask:input_type -> merge.UpdateMergeDataToTaskRequest
	19,  // 108: merge.Merge.ManualMerge:input_type -> merge.ManualMergeRequest
	23,  // 109: merge.Merge.ManualCalibration:input_type -> merge.ManualCalibrationRequest
	0,   // 110: merge.Merge.RecalRiskLevel:input_type -> merge.Empty
	6,   // 111: merge.Merge.TriggerFieldTagger:input_type -> merge.TriggerFieldTaggerRequest
	11,  // 112: merge.Merge.TriggerMergeForAsset:input_type -> merge.TriggerMergeForAssetRequest
	13,  // 113: merge.Merge.TriggerMergeForVuln:input_type -> merge.TriggerMergeForVulnRequest
	14,  // 114: merge.Merge.TriggerMergeForStaff:input_type -> merge.TriggerMergeForStaffRequest
	12,  // 115: merge.Merge.TriggerMergeForDevice:input_type -> merge.TriggerMergeForDeviceRequest
	32,  // 116: merge.Merge.IpAdminInfo:input_type -> merge.IpInfoRequest
	32,  // 117: merge.Merge.IpAccountInfo:input_type -> merge.IpInfoRequest
	32,  // 118: merge.Merge.IpPortInfo:input_type -> merge.IpInfoRequest
	32,  // 119: merge.Merge.IpHostInfo:input_type -> merge.IpInfoRequest
	32,  // 120: merge.Merge.IpSecurityInfo:input_type -> merge.IpInfoRequest
	32,  // 121: merge.Merge.IpTraceabilityBaseInfo:input_type -> merge.IpInfoRequest
	52,  // 122: merge.Merge.IpTraceabilityProcessInfo:input_type -> merge.TraceabilityProcessInfoRequest
	54,  // 123: merge.Merge.IpTraceabilityDetailInfo:input_type -> merge.TraceabilityDetailInfoRequest
	33,  // 124: merge.Merge.DeviceBaseInfo:input_type -> merge.DeviceInfoRequest
	33,  // 125: merge.Merge.DeviceRelatedIpInfo:input_type -> merge.DeviceInfoRequest
	33,  // 126: merge.Merge.DeviceHostInfo:input_type -> merge.DeviceInfoRequest
	33,  // 127: merge.Merge.DeviceRelatedEthInfo:input_type -> merge.DeviceInfoRequest
	33,  // 128: merge.Merge.DeviceTraceabilityBaseInfo:input_type -> merge.DeviceInfoRequest
	52,  // 129: merge.Merge.DeviceTraceabilityProcessInfo:input_type -> merge.TraceabilityProcessInfoRequest
	54,  // 130: merge.Merge.DeviceTraceabilityDetailInfo:input_type -> merge.TraceabilityDetailInfoRequest
	5,   // 131: merge.Merge.TestMethod:output_type -> merge.TestMethodResponse
	18,  // 132: merge.Merge.UpdateMergeDataToTask:output_type -> merge.UpdateMergeDataToTaskResponse
	21,  // 133: merge.Merge.ManualMerge:output_type -> merge.ManualMergeResponse
	24,  // 134: merge.Merge.ManualCalibration:output_type -> merge.ManualCalibrationResponse
	17,  // 135: merge.Merge.RecalRiskLevel:output_type -> merge.RecalRiskLevelResponse
	7,   // 136: merge.Merge.TriggerFieldTagger:output_type -> merge.TriggerFieldTaggerResponse
	15,  // 137: merge.Merge.TriggerMergeForAsset:output_type -> merge.TriggerMergeResponse
	15,  // 138: merge.Merge.TriggerMergeForVuln:output_type -> merge.TriggerMergeResponse
	15,  // 139: merge.Merge.TriggerMergeForStaff:output_type -> merge.TriggerMergeResponse
	15,  // 140: merge.Merge.TriggerMergeForDevice:output_type -> merge.TriggerMergeResponse
	43,  // 141: merge.Merge.IpAdminInfo:output_type -> merge.IpAdminInfoResponse
	45,  // 142: merge.Merge.IpAccountInfo:output_type -> merge.IpAccountInfoResponse
	47,  // 143: merge.Merge.IpPortInfo:output_type -> merge.IpPortInfoResponse
	48,  // 144: merge.Merge.IpHostInfo:output_type -> merge.HostInfo
	49,  // 145: merge.Merge.IpSecurityInfo:output_type -> merge.IpSecurityInfoResponse
	50,  // 146: merge.Merge.IpTraceabilityBaseInfo:output_type -> merge.IpTraceabilityBaseInfoResponse
	53,  // 147: merge.Merge.IpTraceabilityProcessInfo:output_type -> merge.TraceabilityProcessInfoResponse
	67,  // 148: merge.Merge.IpTraceabilityDetailInfo:output_type -> merge.IpTraceabilityDetailInfoResponse
	68,  // 149: merge.Merge.DeviceBaseInfo:output_type -> merge.DeviceBaseInfoResponse
	69,  // 150: merge.Merge.DeviceRelatedIpInfo:output_type -> merge.DeviceRelatedIpInfoResponse
	48,  // 151: merge.Merge.DeviceHostInfo:output_type -> merge.HostInfo
	71,  // 152: merge.Merge.DeviceRelatedEthInfo:output_type -> merge.DeviceRelatedEthInfoResponse
	72,  // 153: merge.Merge.DeviceTraceabilityBaseInfo:output_type -> merge.DeviceTraceabilityBaseInfoResponse
	53,  // 154: merge.Merge.DeviceTraceabilityProcessInfo:output_type -> merge.TraceabilityProcessInfoResponse
	56,  // 155: merge.Merge.DeviceTraceabilityDetailInfo:output_type -> merge.DeviceTraceabilityDetailInfoResponse
	131, // [131:156] is the sub-list for method output_type
	106, // [106:131] is the sub-list for method input_type
	106, // [106:106] is the sub-list for extension type_name
	106, // [106:106] is the sub-list for extension extendee
	0,   // [0:106] is the sub-list for field type_name
}

func init() { file_merge_proto_init() }
func file_merge_proto_init() {
	if File_merge_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_merge_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Empty); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_merge_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Source); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_merge_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RuleInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_merge_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Area); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_merge_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TestMethodRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_merge_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TestMethodResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_merge_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TriggerFieldTaggerRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_merge_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TriggerFieldTaggerResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_merge_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TriggerMergeBaseRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_merge_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IpInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_merge_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DataRangeByTask); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_merge_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TriggerMergeForAssetRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_merge_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TriggerMergeForDeviceRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_merge_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TriggerMergeForVulnRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_merge_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TriggerMergeForStaffRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_merge_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TriggerMergeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_merge_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateMergeDataToTaskRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_merge_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecalRiskLevelResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_merge_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateMergeDataToTaskResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_merge_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ManualMergeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_merge_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ManualMergeResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_merge_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ManualMergeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_merge_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReMergeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_merge_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ManualCalibrationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_merge_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ManualCalibrationResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_merge_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IPInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_merge_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BusinessInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_merge_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Department); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_merge_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OperInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_merge_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeviceBusiness); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_merge_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeviceOpers); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_merge_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Device); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_merge_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IpInfoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_merge_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeviceInfoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_merge_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DepartmentBase); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_merge_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PersonFindInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_merge_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PersonBase); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_merge_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PersonWithMapping); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_merge_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IpAdminInfoResponseItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_merge_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IpAdminInfoResponseList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_merge_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PortMappingList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_merge_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PortMapping); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_merge_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PersonInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_merge_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IpAdminInfoResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_merge_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OperDepartment); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_merge_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IpAccountInfoResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_merge_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IpPortInfoResponseItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_merge_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IpPortInfoResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_merge_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HostInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_merge_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IpSecurityInfoResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_merge_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IpTraceabilityBaseInfoResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_merge_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TraceabilityProcessInfoResponseItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_merge_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TraceabilityProcessInfoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_merge_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TraceabilityProcessInfoResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_merge_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TraceabilityDetailInfoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_merge_proto_msgTypes[55].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeviceTraceabilityDetailInfoResponseItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_merge_proto_msgTypes[56].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeviceTraceabilityDetailInfoResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_merge_proto_msgTypes[57].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MapStringString); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_merge_proto_msgTypes[58].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TraceabilityDetailInfoResponseOriginalDataItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_merge_proto_msgTypes[59].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Business); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_merge_proto_msgTypes[60].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PortInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_merge_proto_msgTypes[61].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPortInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_merge_proto_msgTypes[62].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Asset); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_merge_proto_msgTypes[63].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAsset); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_merge_proto_msgTypes[64].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TraceabilityDetailInfoResponseStrategyItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_merge_proto_msgTypes[65].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TraceabilityDetailInfoResponseFieldValInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_merge_proto_msgTypes[66].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IpTraceabilityDetailInfoResponseItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_merge_proto_msgTypes[67].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IpTraceabilityDetailInfoResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_merge_proto_msgTypes[68].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeviceBaseInfoResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_merge_proto_msgTypes[69].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeviceRelatedIpInfoResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_merge_proto_msgTypes[70].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeviceRelatedEthInfoItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_merge_proto_msgTypes[71].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeviceRelatedEthInfoResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_merge_proto_msgTypes[72].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeviceTraceabilityBaseInfoResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_merge_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   107,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_merge_proto_goTypes,
		DependencyIndexes: file_merge_proto_depIdxs,
		MessageInfos:      file_merge_proto_msgTypes,
	}.Build()
	File_merge_proto = out.File
	file_merge_proto_rawDesc = nil
	file_merge_proto_goTypes = nil
	file_merge_proto_depIdxs = nil
}
