// Code generated by protoc-gen-micro. DO NOT EDIT.
// source: merge.proto

package merge

import (
	fmt "fmt"
	proto "google.golang.org/protobuf/proto"
	math "math"
)

import (
	context "context"
	api "go-micro.dev/v4/api"
	client "go-micro.dev/v4/client"
	server "go-micro.dev/v4/server"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// Reference imports to suppress errors if they are not otherwise used.
var _ api.Endpoint
var _ context.Context
var _ client.Option
var _ server.Option

// Api Endpoints for Merge service

func NewMergeEndpoints() []*api.Endpoint {
	return []*api.Endpoint{}
}

// Client API for Merge service

type MergeService interface {
	TestMethod(ctx context.Context, in *TestMethodRequest, opts ...client.CallOption) (*TestMethodResponse, error)
	UpdateMergeDataToTask(ctx context.Context, in *UpdateMergeDataToTaskRequest, opts ...client.CallOption) (*UpdateMergeDataToTaskResponse, error)
	ManualMerge(ctx context.Context, in *ManualMergeRequest, opts ...client.CallOption) (*ManualMergeResponse, error)
	ManualCalibration(ctx context.Context, in *ManualCalibrationRequest, opts ...client.CallOption) (*ManualCalibrationResponse, error)
	RecalRiskLevel(ctx context.Context, in *Empty, opts ...client.CallOption) (*RecalRiskLevelResponse, error)
	TriggerFieldTagger(ctx context.Context, in *TriggerFieldTaggerRequest, opts ...client.CallOption) (*TriggerFieldTaggerResponse, error)
	TriggerMergeForAsset(ctx context.Context, in *TriggerMergeForAssetRequest, opts ...client.CallOption) (*TriggerMergeResponse, error)
	TriggerMergeForVuln(ctx context.Context, in *TriggerMergeForVulnRequest, opts ...client.CallOption) (*TriggerMergeResponse, error)
	TriggerMergeForStaff(ctx context.Context, in *TriggerMergeForStaffRequest, opts ...client.CallOption) (*TriggerMergeResponse, error)
	TriggerMergeForDevice(ctx context.Context, in *TriggerMergeForDeviceRequest, opts ...client.CallOption) (*TriggerMergeResponse, error)
	IpAdminInfo(ctx context.Context, in *IpInfoRequest, opts ...client.CallOption) (*IpAdminInfoResponse, error)
	IpAccountInfo(ctx context.Context, in *IpInfoRequest, opts ...client.CallOption) (*IpAccountInfoResponse, error)
	IpPortInfo(ctx context.Context, in *IpInfoRequest, opts ...client.CallOption) (*IpPortInfoResponse, error)
	IpHostInfo(ctx context.Context, in *IpInfoRequest, opts ...client.CallOption) (*HostInfo, error)
	IpSecurityInfo(ctx context.Context, in *IpInfoRequest, opts ...client.CallOption) (*IpSecurityInfoResponse, error)
	IpTraceabilityBaseInfo(ctx context.Context, in *IpInfoRequest, opts ...client.CallOption) (*IpTraceabilityBaseInfoResponse, error)
	IpTraceabilityProcessInfo(ctx context.Context, in *TraceabilityProcessInfoRequest, opts ...client.CallOption) (*TraceabilityProcessInfoResponse, error)
	IpTraceabilityDetailInfo(ctx context.Context, in *TraceabilityDetailInfoRequest, opts ...client.CallOption) (*IpTraceabilityDetailInfoResponse, error)
	DeviceBaseInfo(ctx context.Context, in *DeviceInfoRequest, opts ...client.CallOption) (*DeviceBaseInfoResponse, error)
	DeviceRelatedIpInfo(ctx context.Context, in *DeviceInfoRequest, opts ...client.CallOption) (*DeviceRelatedIpInfoResponse, error)
	DeviceHostInfo(ctx context.Context, in *DeviceInfoRequest, opts ...client.CallOption) (*HostInfo, error)
	DeviceRelatedEthInfo(ctx context.Context, in *DeviceInfoRequest, opts ...client.CallOption) (*DeviceRelatedEthInfoResponse, error)
	DeviceTraceabilityBaseInfo(ctx context.Context, in *DeviceInfoRequest, opts ...client.CallOption) (*DeviceTraceabilityBaseInfoResponse, error)
	DeviceTraceabilityProcessInfo(ctx context.Context, in *TraceabilityProcessInfoRequest, opts ...client.CallOption) (*TraceabilityProcessInfoResponse, error)
	DeviceTraceabilityDetailInfo(ctx context.Context, in *TraceabilityDetailInfoRequest, opts ...client.CallOption) (*DeviceTraceabilityDetailInfoResponse, error)
}

type mergeService struct {
	c    client.Client
	name string
}

func NewMergeService(name string, c client.Client) MergeService {
	return &mergeService{
		c:    c,
		name: name,
	}
}

func (c *mergeService) TestMethod(ctx context.Context, in *TestMethodRequest, opts ...client.CallOption) (*TestMethodResponse, error) {
	req := c.c.NewRequest(c.name, "Merge.TestMethod", in)
	out := new(TestMethodResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mergeService) UpdateMergeDataToTask(ctx context.Context, in *UpdateMergeDataToTaskRequest, opts ...client.CallOption) (*UpdateMergeDataToTaskResponse, error) {
	req := c.c.NewRequest(c.name, "Merge.UpdateMergeDataToTask", in)
	out := new(UpdateMergeDataToTaskResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mergeService) ManualMerge(ctx context.Context, in *ManualMergeRequest, opts ...client.CallOption) (*ManualMergeResponse, error) {
	req := c.c.NewRequest(c.name, "Merge.ManualMerge", in)
	out := new(ManualMergeResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mergeService) ManualCalibration(ctx context.Context, in *ManualCalibrationRequest, opts ...client.CallOption) (*ManualCalibrationResponse, error) {
	req := c.c.NewRequest(c.name, "Merge.ManualCalibration", in)
	out := new(ManualCalibrationResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mergeService) RecalRiskLevel(ctx context.Context, in *Empty, opts ...client.CallOption) (*RecalRiskLevelResponse, error) {
	req := c.c.NewRequest(c.name, "Merge.RecalRiskLevel", in)
	out := new(RecalRiskLevelResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mergeService) TriggerFieldTagger(ctx context.Context, in *TriggerFieldTaggerRequest, opts ...client.CallOption) (*TriggerFieldTaggerResponse, error) {
	req := c.c.NewRequest(c.name, "Merge.TriggerFieldTagger", in)
	out := new(TriggerFieldTaggerResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mergeService) TriggerMergeForAsset(ctx context.Context, in *TriggerMergeForAssetRequest, opts ...client.CallOption) (*TriggerMergeResponse, error) {
	req := c.c.NewRequest(c.name, "Merge.TriggerMergeForAsset", in)
	out := new(TriggerMergeResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mergeService) TriggerMergeForVuln(ctx context.Context, in *TriggerMergeForVulnRequest, opts ...client.CallOption) (*TriggerMergeResponse, error) {
	req := c.c.NewRequest(c.name, "Merge.TriggerMergeForVuln", in)
	out := new(TriggerMergeResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mergeService) TriggerMergeForStaff(ctx context.Context, in *TriggerMergeForStaffRequest, opts ...client.CallOption) (*TriggerMergeResponse, error) {
	req := c.c.NewRequest(c.name, "Merge.TriggerMergeForStaff", in)
	out := new(TriggerMergeResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mergeService) TriggerMergeForDevice(ctx context.Context, in *TriggerMergeForDeviceRequest, opts ...client.CallOption) (*TriggerMergeResponse, error) {
	req := c.c.NewRequest(c.name, "Merge.TriggerMergeForDevice", in)
	out := new(TriggerMergeResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mergeService) IpAdminInfo(ctx context.Context, in *IpInfoRequest, opts ...client.CallOption) (*IpAdminInfoResponse, error) {
	req := c.c.NewRequest(c.name, "Merge.IpAdminInfo", in)
	out := new(IpAdminInfoResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mergeService) IpAccountInfo(ctx context.Context, in *IpInfoRequest, opts ...client.CallOption) (*IpAccountInfoResponse, error) {
	req := c.c.NewRequest(c.name, "Merge.IpAccountInfo", in)
	out := new(IpAccountInfoResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mergeService) IpPortInfo(ctx context.Context, in *IpInfoRequest, opts ...client.CallOption) (*IpPortInfoResponse, error) {
	req := c.c.NewRequest(c.name, "Merge.IpPortInfo", in)
	out := new(IpPortInfoResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mergeService) IpHostInfo(ctx context.Context, in *IpInfoRequest, opts ...client.CallOption) (*HostInfo, error) {
	req := c.c.NewRequest(c.name, "Merge.IpHostInfo", in)
	out := new(HostInfo)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mergeService) IpSecurityInfo(ctx context.Context, in *IpInfoRequest, opts ...client.CallOption) (*IpSecurityInfoResponse, error) {
	req := c.c.NewRequest(c.name, "Merge.IpSecurityInfo", in)
	out := new(IpSecurityInfoResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mergeService) IpTraceabilityBaseInfo(ctx context.Context, in *IpInfoRequest, opts ...client.CallOption) (*IpTraceabilityBaseInfoResponse, error) {
	req := c.c.NewRequest(c.name, "Merge.IpTraceabilityBaseInfo", in)
	out := new(IpTraceabilityBaseInfoResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mergeService) IpTraceabilityProcessInfo(ctx context.Context, in *TraceabilityProcessInfoRequest, opts ...client.CallOption) (*TraceabilityProcessInfoResponse, error) {
	req := c.c.NewRequest(c.name, "Merge.IpTraceabilityProcessInfo", in)
	out := new(TraceabilityProcessInfoResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mergeService) IpTraceabilityDetailInfo(ctx context.Context, in *TraceabilityDetailInfoRequest, opts ...client.CallOption) (*IpTraceabilityDetailInfoResponse, error) {
	req := c.c.NewRequest(c.name, "Merge.IpTraceabilityDetailInfo", in)
	out := new(IpTraceabilityDetailInfoResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mergeService) DeviceBaseInfo(ctx context.Context, in *DeviceInfoRequest, opts ...client.CallOption) (*DeviceBaseInfoResponse, error) {
	req := c.c.NewRequest(c.name, "Merge.DeviceBaseInfo", in)
	out := new(DeviceBaseInfoResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mergeService) DeviceRelatedIpInfo(ctx context.Context, in *DeviceInfoRequest, opts ...client.CallOption) (*DeviceRelatedIpInfoResponse, error) {
	req := c.c.NewRequest(c.name, "Merge.DeviceRelatedIpInfo", in)
	out := new(DeviceRelatedIpInfoResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mergeService) DeviceHostInfo(ctx context.Context, in *DeviceInfoRequest, opts ...client.CallOption) (*HostInfo, error) {
	req := c.c.NewRequest(c.name, "Merge.DeviceHostInfo", in)
	out := new(HostInfo)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mergeService) DeviceRelatedEthInfo(ctx context.Context, in *DeviceInfoRequest, opts ...client.CallOption) (*DeviceRelatedEthInfoResponse, error) {
	req := c.c.NewRequest(c.name, "Merge.DeviceRelatedEthInfo", in)
	out := new(DeviceRelatedEthInfoResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mergeService) DeviceTraceabilityBaseInfo(ctx context.Context, in *DeviceInfoRequest, opts ...client.CallOption) (*DeviceTraceabilityBaseInfoResponse, error) {
	req := c.c.NewRequest(c.name, "Merge.DeviceTraceabilityBaseInfo", in)
	out := new(DeviceTraceabilityBaseInfoResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mergeService) DeviceTraceabilityProcessInfo(ctx context.Context, in *TraceabilityProcessInfoRequest, opts ...client.CallOption) (*TraceabilityProcessInfoResponse, error) {
	req := c.c.NewRequest(c.name, "Merge.DeviceTraceabilityProcessInfo", in)
	out := new(TraceabilityProcessInfoResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mergeService) DeviceTraceabilityDetailInfo(ctx context.Context, in *TraceabilityDetailInfoRequest, opts ...client.CallOption) (*DeviceTraceabilityDetailInfoResponse, error) {
	req := c.c.NewRequest(c.name, "Merge.DeviceTraceabilityDetailInfo", in)
	out := new(DeviceTraceabilityDetailInfoResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for Merge service

type MergeHandler interface {
	TestMethod(context.Context, *TestMethodRequest, *TestMethodResponse) error
	UpdateMergeDataToTask(context.Context, *UpdateMergeDataToTaskRequest, *UpdateMergeDataToTaskResponse) error
	ManualMerge(context.Context, *ManualMergeRequest, *ManualMergeResponse) error
	ManualCalibration(context.Context, *ManualCalibrationRequest, *ManualCalibrationResponse) error
	RecalRiskLevel(context.Context, *Empty, *RecalRiskLevelResponse) error
	TriggerFieldTagger(context.Context, *TriggerFieldTaggerRequest, *TriggerFieldTaggerResponse) error
	TriggerMergeForAsset(context.Context, *TriggerMergeForAssetRequest, *TriggerMergeResponse) error
	TriggerMergeForVuln(context.Context, *TriggerMergeForVulnRequest, *TriggerMergeResponse) error
	TriggerMergeForStaff(context.Context, *TriggerMergeForStaffRequest, *TriggerMergeResponse) error
	TriggerMergeForDevice(context.Context, *TriggerMergeForDeviceRequest, *TriggerMergeResponse) error
	IpAdminInfo(context.Context, *IpInfoRequest, *IpAdminInfoResponse) error
	IpAccountInfo(context.Context, *IpInfoRequest, *IpAccountInfoResponse) error
	IpPortInfo(context.Context, *IpInfoRequest, *IpPortInfoResponse) error
	IpHostInfo(context.Context, *IpInfoRequest, *HostInfo) error
	IpSecurityInfo(context.Context, *IpInfoRequest, *IpSecurityInfoResponse) error
	IpTraceabilityBaseInfo(context.Context, *IpInfoRequest, *IpTraceabilityBaseInfoResponse) error
	IpTraceabilityProcessInfo(context.Context, *TraceabilityProcessInfoRequest, *TraceabilityProcessInfoResponse) error
	IpTraceabilityDetailInfo(context.Context, *TraceabilityDetailInfoRequest, *IpTraceabilityDetailInfoResponse) error
	DeviceBaseInfo(context.Context, *DeviceInfoRequest, *DeviceBaseInfoResponse) error
	DeviceRelatedIpInfo(context.Context, *DeviceInfoRequest, *DeviceRelatedIpInfoResponse) error
	DeviceHostInfo(context.Context, *DeviceInfoRequest, *HostInfo) error
	DeviceRelatedEthInfo(context.Context, *DeviceInfoRequest, *DeviceRelatedEthInfoResponse) error
	DeviceTraceabilityBaseInfo(context.Context, *DeviceInfoRequest, *DeviceTraceabilityBaseInfoResponse) error
	DeviceTraceabilityProcessInfo(context.Context, *TraceabilityProcessInfoRequest, *TraceabilityProcessInfoResponse) error
	DeviceTraceabilityDetailInfo(context.Context, *TraceabilityDetailInfoRequest, *DeviceTraceabilityDetailInfoResponse) error
}

func RegisterMergeHandler(s server.Server, hdlr MergeHandler, opts ...server.HandlerOption) error {
	type merge interface {
		TestMethod(ctx context.Context, in *TestMethodRequest, out *TestMethodResponse) error
		UpdateMergeDataToTask(ctx context.Context, in *UpdateMergeDataToTaskRequest, out *UpdateMergeDataToTaskResponse) error
		ManualMerge(ctx context.Context, in *ManualMergeRequest, out *ManualMergeResponse) error
		ManualCalibration(ctx context.Context, in *ManualCalibrationRequest, out *ManualCalibrationResponse) error
		RecalRiskLevel(ctx context.Context, in *Empty, out *RecalRiskLevelResponse) error
		TriggerFieldTagger(ctx context.Context, in *TriggerFieldTaggerRequest, out *TriggerFieldTaggerResponse) error
		TriggerMergeForAsset(ctx context.Context, in *TriggerMergeForAssetRequest, out *TriggerMergeResponse) error
		TriggerMergeForVuln(ctx context.Context, in *TriggerMergeForVulnRequest, out *TriggerMergeResponse) error
		TriggerMergeForStaff(ctx context.Context, in *TriggerMergeForStaffRequest, out *TriggerMergeResponse) error
		TriggerMergeForDevice(ctx context.Context, in *TriggerMergeForDeviceRequest, out *TriggerMergeResponse) error
		IpAdminInfo(ctx context.Context, in *IpInfoRequest, out *IpAdminInfoResponse) error
		IpAccountInfo(ctx context.Context, in *IpInfoRequest, out *IpAccountInfoResponse) error
		IpPortInfo(ctx context.Context, in *IpInfoRequest, out *IpPortInfoResponse) error
		IpHostInfo(ctx context.Context, in *IpInfoRequest, out *HostInfo) error
		IpSecurityInfo(ctx context.Context, in *IpInfoRequest, out *IpSecurityInfoResponse) error
		IpTraceabilityBaseInfo(ctx context.Context, in *IpInfoRequest, out *IpTraceabilityBaseInfoResponse) error
		IpTraceabilityProcessInfo(ctx context.Context, in *TraceabilityProcessInfoRequest, out *TraceabilityProcessInfoResponse) error
		IpTraceabilityDetailInfo(ctx context.Context, in *TraceabilityDetailInfoRequest, out *IpTraceabilityDetailInfoResponse) error
		DeviceBaseInfo(ctx context.Context, in *DeviceInfoRequest, out *DeviceBaseInfoResponse) error
		DeviceRelatedIpInfo(ctx context.Context, in *DeviceInfoRequest, out *DeviceRelatedIpInfoResponse) error
		DeviceHostInfo(ctx context.Context, in *DeviceInfoRequest, out *HostInfo) error
		DeviceRelatedEthInfo(ctx context.Context, in *DeviceInfoRequest, out *DeviceRelatedEthInfoResponse) error
		DeviceTraceabilityBaseInfo(ctx context.Context, in *DeviceInfoRequest, out *DeviceTraceabilityBaseInfoResponse) error
		DeviceTraceabilityProcessInfo(ctx context.Context, in *TraceabilityProcessInfoRequest, out *TraceabilityProcessInfoResponse) error
		DeviceTraceabilityDetailInfo(ctx context.Context, in *TraceabilityDetailInfoRequest, out *DeviceTraceabilityDetailInfoResponse) error
	}
	type Merge struct {
		merge
	}
	h := &mergeHandler{hdlr}
	return s.Handle(s.NewHandler(&Merge{h}, opts...))
}

type mergeHandler struct {
	MergeHandler
}

func (h *mergeHandler) TestMethod(ctx context.Context, in *TestMethodRequest, out *TestMethodResponse) error {
	return h.MergeHandler.TestMethod(ctx, in, out)
}

func (h *mergeHandler) UpdateMergeDataToTask(ctx context.Context, in *UpdateMergeDataToTaskRequest, out *UpdateMergeDataToTaskResponse) error {
	return h.MergeHandler.UpdateMergeDataToTask(ctx, in, out)
}

func (h *mergeHandler) ManualMerge(ctx context.Context, in *ManualMergeRequest, out *ManualMergeResponse) error {
	return h.MergeHandler.ManualMerge(ctx, in, out)
}

func (h *mergeHandler) ManualCalibration(ctx context.Context, in *ManualCalibrationRequest, out *ManualCalibrationResponse) error {
	return h.MergeHandler.ManualCalibration(ctx, in, out)
}

func (h *mergeHandler) RecalRiskLevel(ctx context.Context, in *Empty, out *RecalRiskLevelResponse) error {
	return h.MergeHandler.RecalRiskLevel(ctx, in, out)
}

func (h *mergeHandler) TriggerFieldTagger(ctx context.Context, in *TriggerFieldTaggerRequest, out *TriggerFieldTaggerResponse) error {
	return h.MergeHandler.TriggerFieldTagger(ctx, in, out)
}

func (h *mergeHandler) TriggerMergeForAsset(ctx context.Context, in *TriggerMergeForAssetRequest, out *TriggerMergeResponse) error {
	return h.MergeHandler.TriggerMergeForAsset(ctx, in, out)
}

func (h *mergeHandler) TriggerMergeForVuln(ctx context.Context, in *TriggerMergeForVulnRequest, out *TriggerMergeResponse) error {
	return h.MergeHandler.TriggerMergeForVuln(ctx, in, out)
}

func (h *mergeHandler) TriggerMergeForStaff(ctx context.Context, in *TriggerMergeForStaffRequest, out *TriggerMergeResponse) error {
	return h.MergeHandler.TriggerMergeForStaff(ctx, in, out)
}

func (h *mergeHandler) TriggerMergeForDevice(ctx context.Context, in *TriggerMergeForDeviceRequest, out *TriggerMergeResponse) error {
	return h.MergeHandler.TriggerMergeForDevice(ctx, in, out)
}

func (h *mergeHandler) IpAdminInfo(ctx context.Context, in *IpInfoRequest, out *IpAdminInfoResponse) error {
	return h.MergeHandler.IpAdminInfo(ctx, in, out)
}

func (h *mergeHandler) IpAccountInfo(ctx context.Context, in *IpInfoRequest, out *IpAccountInfoResponse) error {
	return h.MergeHandler.IpAccountInfo(ctx, in, out)
}

func (h *mergeHandler) IpPortInfo(ctx context.Context, in *IpInfoRequest, out *IpPortInfoResponse) error {
	return h.MergeHandler.IpPortInfo(ctx, in, out)
}

func (h *mergeHandler) IpHostInfo(ctx context.Context, in *IpInfoRequest, out *HostInfo) error {
	return h.MergeHandler.IpHostInfo(ctx, in, out)
}

func (h *mergeHandler) IpSecurityInfo(ctx context.Context, in *IpInfoRequest, out *IpSecurityInfoResponse) error {
	return h.MergeHandler.IpSecurityInfo(ctx, in, out)
}

func (h *mergeHandler) IpTraceabilityBaseInfo(ctx context.Context, in *IpInfoRequest, out *IpTraceabilityBaseInfoResponse) error {
	return h.MergeHandler.IpTraceabilityBaseInfo(ctx, in, out)
}

func (h *mergeHandler) IpTraceabilityProcessInfo(ctx context.Context, in *TraceabilityProcessInfoRequest, out *TraceabilityProcessInfoResponse) error {
	return h.MergeHandler.IpTraceabilityProcessInfo(ctx, in, out)
}

func (h *mergeHandler) IpTraceabilityDetailInfo(ctx context.Context, in *TraceabilityDetailInfoRequest, out *IpTraceabilityDetailInfoResponse) error {
	return h.MergeHandler.IpTraceabilityDetailInfo(ctx, in, out)
}

func (h *mergeHandler) DeviceBaseInfo(ctx context.Context, in *DeviceInfoRequest, out *DeviceBaseInfoResponse) error {
	return h.MergeHandler.DeviceBaseInfo(ctx, in, out)
}

func (h *mergeHandler) DeviceRelatedIpInfo(ctx context.Context, in *DeviceInfoRequest, out *DeviceRelatedIpInfoResponse) error {
	return h.MergeHandler.DeviceRelatedIpInfo(ctx, in, out)
}

func (h *mergeHandler) DeviceHostInfo(ctx context.Context, in *DeviceInfoRequest, out *HostInfo) error {
	return h.MergeHandler.DeviceHostInfo(ctx, in, out)
}

func (h *mergeHandler) DeviceRelatedEthInfo(ctx context.Context, in *DeviceInfoRequest, out *DeviceRelatedEthInfoResponse) error {
	return h.MergeHandler.DeviceRelatedEthInfo(ctx, in, out)
}

func (h *mergeHandler) DeviceTraceabilityBaseInfo(ctx context.Context, in *DeviceInfoRequest, out *DeviceTraceabilityBaseInfoResponse) error {
	return h.MergeHandler.DeviceTraceabilityBaseInfo(ctx, in, out)
}

func (h *mergeHandler) DeviceTraceabilityProcessInfo(ctx context.Context, in *TraceabilityProcessInfoRequest, out *TraceabilityProcessInfoResponse) error {
	return h.MergeHandler.DeviceTraceabilityProcessInfo(ctx, in, out)
}

func (h *mergeHandler) DeviceTraceabilityDetailInfo(ctx context.Context, in *TraceabilityDetailInfoRequest, out *DeviceTraceabilityDetailInfoResponse) error {
	return h.MergeHandler.DeviceTraceabilityDetailInfo(ctx, in, out)
}
