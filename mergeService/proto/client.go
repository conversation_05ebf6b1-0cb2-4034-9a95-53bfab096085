package merge

import (
	"fmt"
	"fobrain/pkg/cfg"
	"sync"
	"time"

	"go-micro.dev/v4"
	"go-micro.dev/v4/logger"

	"go-micro.dev/v4/client"
)

var once sync.Once

var singleInstance client.Client

var ClientWithAddress = client.WithAddress(fmt.Sprintf("127.0.0.1:%s", cfg.LoadCommon().MergeListen))

// SetRpcTimeoutOpt rpc超时设置（秒）
func SetRpcTimeoutOpt(timeout int) client.CallOption {
	return func(o *client.CallOptions) {
		o.Retries = 0
		o.ConnectionTimeout = time.Second * time.Duration(timeout)
		o.RequestTimeout = time.Second * time.Duration(timeout)
	}
}

func getInstance() client.Client {
	if singleInstance == nil {
		once.Do(func() {
			srv := micro.NewService(
				micro.RegisterTTL(5*time.Second),      // TTL指定从上一次心跳间隔起，超过这个时间服务会被服务发现移除
				micro.RegisterInterval(4*time.Second), // 让服务在指定时间内重新注册，保持TTL获取的注册时间有效
				micro.Logger(logger.DefaultLogger),
			)
			singleInstance = srv.Client()
			singleInstance.Init(
				client.Retries(4),
				client.PoolSize(20),
				client.DialTimeout(15*time.Second),
				client.RequestTimeout(30*time.Second),
			)
		})
	}
	return singleInstance
}

func GetProtoClient() MergeService {
	return NewMergeService(
		"fobrain.api.merge",
		getInstance(),
	)
}
