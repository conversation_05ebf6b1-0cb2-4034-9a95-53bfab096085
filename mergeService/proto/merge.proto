syntax = "proto3";

package merge;

option go_package = "./proto;merge";

service Merge {
  rpc TestMethod (TestMethodRequest) returns (TestMethodResponse) {}
  rpc UpdateMergeDataToTask (UpdateMergeDataToTaskRequest) returns (UpdateMergeDataToTaskResponse) {}
  rpc ManualMerge (ManualMergeRequest) returns (ManualMergeResponse) {}
  rpc ManualCalibration (ManualCalibrationRequest) returns (ManualCalibrationResponse) {}
  rpc RecalRiskLevel (Empty) returns (RecalRiskLevelResponse) {}
  rpc TriggerFieldTagger (TriggerFieldTaggerRequest) returns (TriggerFieldTaggerResponse) {}
  rpc TriggerMergeForAsset (TriggerMergeForAssetRequest) returns (TriggerMergeResponse) {}
  rpc TriggerMergeForVuln (TriggerMergeForVulnRequest) returns (TriggerMergeResponse) {}
  rpc TriggerMergeForStaff (TriggerMergeForStaffRequest) returns (TriggerMergeResponse) {}
  rpc TriggerMergeForDevice (TriggerMergeForDeviceRequest) returns (TriggerMergeResponse) {}

  rpc IpAdminInfo(IpInfoRequest) returns (IpAdminInfoResponse) {} // IP管理信息
  rpc IpAccountInfo(IpInfoRequest) returns (IpAccountInfoResponse) {} // IP账户信息
  rpc IpPortInfo(IpInfoRequest) returns (IpPortInfoResponse) {} // IP端口信息
  rpc IpHostInfo(IpInfoRequest) returns (HostInfo) {} // IP主机信息
  rpc IpSecurityInfo(IpInfoRequest) returns (IpSecurityInfoResponse) {} // IP安全信息

  rpc IpTraceabilityBaseInfo(IpInfoRequest) returns (IpTraceabilityBaseInfoResponse) {} // IP溯源基本信息
  rpc IpTraceabilityProcessInfo(TraceabilityProcessInfoRequest) returns (TraceabilityProcessInfoResponse) {} // IP溯源流程信息
  rpc IpTraceabilityDetailInfo(TraceabilityDetailInfoRequest) returns (IpTraceabilityDetailInfoResponse) {} // IP溯源详细信息

  rpc DeviceBaseInfo(DeviceInfoRequest) returns (DeviceBaseInfoResponse) {} // 设备基本信息
  rpc DeviceRelatedIpInfo(DeviceInfoRequest) returns (DeviceRelatedIpInfoResponse) {} // 设备关联ip信息
  rpc DeviceHostInfo(DeviceInfoRequest) returns (HostInfo) {} // 设备主机信息
  rpc DeviceRelatedEthInfo(DeviceInfoRequest) returns (DeviceRelatedEthInfoResponse) {} // 设备关联网卡信息

  rpc DeviceTraceabilityBaseInfo(DeviceInfoRequest) returns (DeviceTraceabilityBaseInfoResponse){} // 设备溯源基本信息
  rpc DeviceTraceabilityProcessInfo(TraceabilityProcessInfoRequest) returns (TraceabilityProcessInfoResponse) {} // IP溯源流程信息
  rpc DeviceTraceabilityDetailInfo(TraceabilityDetailInfoRequest) returns (DeviceTraceabilityDetailInfoResponse) {} // 设备溯源详细信息
}

message Empty {}

message Source {
  uint64 id = 1;
  string name = 2;
  string icon = 3;
}

//message RuleInfos {
//  repeated RuleInfo rule_info = 1;
//}

message RuleInfo {
  string product = 1;
  string first_tag = 2;
  string second_tag = 3;
  string level = 4;
}

message Area{
  uint64 id = 1;
  string name = 2;
}

message TestMethodRequest {
  string name = 1;
}

message TestMethodResponse {
  string welcome = 1;
}


message TriggerFieldTaggerRequest {
  string field = 1;
  string condition = 2;
  string value = 3;
  string set_field = 4;
  string set_value = 5;
}

message TriggerFieldTaggerResponse {
  bool success = 1;
  string message = 2;
}

message TriggerMergeBaseRequest {
  uint64 source_id = 1;
  uint64 node_id = 2;
  string task_id = 3;
  string child_task_id = 4;
  string trigger_event = 5;
}

message IpInfo {
  string ip = 1;
  uint64 area = 2;
}

message DataRangeByTask {
  string task_id = 1;
  string child_task_id = 2;
  uint64 node_id = 3;
}

message TriggerMergeForAssetRequest {
  TriggerMergeBaseRequest trigger_merge_base_request = 1;
  repeated string asset_ids = 2;
  repeated IpInfo ip_infos = 3;
  DataRangeByTask data_range_by_task = 4;
  repeated string fields = 5;
  int32 sub_trigger = 6;
}

message TriggerMergeForDeviceRequest {
  TriggerMergeBaseRequest trigger_merge_base_request = 1;
  repeated string device_ids = 2;
  repeated string unique_keys = 3;
  DataRangeByTask data_range_by_task = 4;
  repeated string fields = 5;
  int32 sub_trigger = 6;
}

message TriggerMergeForVulnRequest {
  TriggerMergeBaseRequest trigger_merge_base_request = 1;
  repeated string vul_ids = 2;
  DataRangeByTask data_range_by_task = 3;
  repeated string fields = 4;
  int32 sub_trigger = 5;
}

message TriggerMergeForStaffRequest {
  TriggerMergeBaseRequest trigger_merge_base_request = 1;
  repeated string staff_ids = 2;
  DataRangeByTask data_range_by_task = 3;
  repeated string fields = 4;
  int32 sub_trigger = 5;
}

message TriggerMergeResponse {
  bool success = 1;
  string message = 2;
}

message UpdateMergeDataToTaskRequest {
  string task_type = 1;
  int64 task_id = 2;
  string business_type = 3;
  uint64 record_id = 4;
}

message RecalRiskLevelResponse {
  bool success = 1;
  string message = 2;
}

message UpdateMergeDataToTaskResponse {
  int32 count = 1;
  int32 success_count = 2;
  int32 failed_count = 3;
  int32 discarded_count = 4;
  string start_time = 5;
  string end_time = 6;
  int64 work_hours = 7;
  int64 duration = 8;
}

message ManualMergeRequest {
  string business_type = 1;
  repeated string fields = 2;
  string batch_no = 3;
}

message ManualMergeResult{
  string field = 1;
  bool success = 2;
  string message = 3;
  int64 updated = 4;
}
message ManualMergeResponse {
  repeated ManualMergeResult results = 1;
  string batch_no = 2;
  string message = 3;
}

message ReMergeResponse {
  string task_id = 1;
  bool success = 2;
  string message = 3;
}

message ManualCalibrationRequest {
  string business_type = 1;
  repeated string ids = 2;
  map<string, string> values = 3; // values的值是json字符串,为了免去proto中使用any类型
  string batch_no = 4;
}

message ManualCalibrationResponse {
  bool success = 1;
  string message = 2;
}

message IPInfo {
  string ip = 1;
  uint64 area = 2;
}

message BusinessInfo{
  string system = 1;
  string system_id = 2;
  string owner = 3;
  string owner_id = 4;
  repeated string department = 5;
}

message Department {
  string business_system_id = 1;
  string business_system_name = 2;
  string user_id = 3;
  string user_name = 4;
  string name = 5;
  uint64 id = 6;
  repeated Department parents = 7;
}

message OperInfo {
  string id = 1;
  string fid = 2;
  string name = 3;
}

message DeviceBusiness {
  BusinessInfo business = 1;
  Department business_department = 2;
  repeated IPInfo ip = 3;
}

message DeviceOpers {
  OperInfo oper_info = 1;
  Department oper_department = 2;
  repeated IPInfo ip = 3;
}

message Device {
  string id = 1;
  repeated Source source = 2;
  repeated string hostname = 3;
  repeated string sn = 4;
  repeated string mac = 5;
  repeated string ip = 6;
  repeated string private_ip = 7;
  repeated string public_ip = 8;
  repeated string machine_room = 9;
  repeated Area area = 10;
  repeated string os = 11;
  string created_at = 12;
  string updated_at = 13;
  string deleted_at = 14;
  repeated string oper = 15; //运维人员
  repeated uint64 source_id = 16; //数据源ID
  repeated uint64 area_id = 17; //区域ID
  repeated string tags = 18; //标签
  string fid = 19; //融合key
  int64 poc_num = 20; // 设备对应的漏洞数量
  repeated DeviceBusiness business = 21; // 业务系统
  repeated DeviceOpers opers = 22; // 运维人员
}

message IpInfoRequest {
  string id = 1; //@gotags: form:"id" validate:"required"
}

message DeviceInfoRequest{
  string id = 1; //@gotags: form:"id" validate:"required"
}

// DepartmentBase 部门信息
message DepartmentBase {
  string business_system_id = 1;   // 业务系统
  string business_system_name = 2; // 业务系统名称
  string user_id = 3;              // 用户id
  string user_name = 4;            // 用户名称
  string name = 5;                 // 部门名称,完整的部门名称，以/分隔
  uint64 id = 6;                   // 部门id
  repeated DepartmentBase parents = 7; // 部门父级
}

// PersonFindInfo 人员查找信息
message PersonFindInfo {
  uint64 source_id = 1;     // 来源id
  uint64 node_id = 2;       // 节点id
  string source_value = 3;  // 来源字段值
  string mapping_field = 4; // 映射字段，name,english_name,email,phone,work_number
  int32 find_count = 5;     // 找到的人数
}

// PersonBase 人员基本信息
message PersonBase {
  string id = 1;
  string fid = 2;
  string name = 3;
  string pgid = 4;
  repeated PersonFindInfo find_info = 5;
  repeated DepartmentBase department = 6;
}

// PersonWithMapping 人员映射信息
// PersonWithMapping 人员映射信息
message PersonWithMapping {
  uint64 source_id = 1;     // 来源id
  uint64 node_id = 2;       // 节点id
  string source_value = 3;  // 来源字段值
  string mapping_field = 4; // 映射字段，name,english_name,email,phone,work_number
}

message IpAdminInfoResponseItem{
  string system = 1; // 业务系统
  string system_id = 2; // 业务系统id
  string owner = 3; // 负责人
  string owner_id = 4; // 负责人id
  repeated PersonBase person_base = 5; // 负责人信息
  int64 reliability = 6; // 可信度（1 - 可信 2 - 待确认 3 - 黑名单）
}

message IpAdminInfoResponseList{
  repeated IpAdminInfoResponseItem admin = 1; //业务系统信息
}

// 定义 IP 对应的端口映射列表的消息类型
message PortMappingList {
  repeated PortMapping mappings = 1;  // 端口映射的列表
}

// 定义端口映射的消息类型
message PortMapping {
  string public_ip = 1;   // 公网 IP
  uint64 public_port = 2; // 公网端口
  string private_ip = 3;     // 内网 IP
  uint64 private_port = 4;   // 内网端口
}

message PersonInfo{
  string id = 1;
  string fid = 2;
  string name = 3;
  string find_by = 4; // 查找方式
  string source_value = 5; // 来源值
}

message IpAdminInfoResponse{
  repeated IpAdminInfoResponseItem admin = 1; //业务系统信息
  repeated PersonBase oper = 2; //运维人员
  repeated string machine_room = 3; //机房信息
  repeated OperDepartment oper_department = 4; //机房信息
  repeated string tags = 5; // 标签信息
}

message OperDepartment{
  string oper = 1; // 运维人员
  repeated string department = 2; // 部门
}

message IpAccountInfoResponse{
}

message IpPortInfoResponseItem{
  int32 port = 1; // 端口
  string protocol = 2; // 协议
  int32 status = 3; // 状态
  string url = 4; // URL
  string domain = 5; // Domain
  string title = 6; // 网站标题
}

message IpPortInfoResponse{
  repeated IpPortInfoResponseItem port = 1; // 端口信息
}

message HostInfo{
  repeated string maker = 1; // 厂商
  repeated string model = 2; // 型号
  repeated string sn = 3; // 序列号

  repeated string os = 4; // 操作系统
  repeated string os_kernel = 5; // 操作系统内核

  repeated string memory_size = 6; // 内存大小
  repeated string memory_usage_rate = 7; // 内存使用率

  repeated string cpu_maker = 8; // CPU厂商
  repeated string cpu_brand = 9; // CPU品牌
  repeated int32 cpu_count = 10; // CPU核数
  repeated string load_average = 11; // 负载均衡

  repeated int32 disk_count = 12; // 硬盘数量
  repeated int32 disk_size = 13; // 硬盘大小
  repeated string disk_usage_rate = 14; // 硬盘使用率
}

message IpSecurityInfoResponse{
  int32 poc_count = 1; // 安全漏洞数量
  int32 is_cmdb = 2; // 是否加入cmdb
  int32 is_hids = 3; // 是否加入hids
  int32 is_jumpserver = 4; // 是否加入jumpserver
  bool has_admin_for_business = 5; // 是否有业务管理员
  bool has_admin_for_operation = 6; // 是否有运维管理员
  bool has_deparment = 7; // 是否有归属部门
  int32 has_waf = 8; // 是否有waf
}

message IpTraceabilityBaseInfoResponse{
  repeated uint64 all_source_id = 1; // 所有数据源
  repeated uint64 source_id = 2; // 最后一次融合生效的数据源
  repeated Source all_source = 3; // 所有数据源信息
  repeated Source source = 4; // 最后一次融合生效的数据源信息
  int32 is_extrat_device = 5; // 是否提取了设备
  int32 merge_count = 6; // 合并次数
  string first_merge_time = 7; // 第一次合并时间
  string last_merge_time = 8; // 最后一次合并时间
}

message TraceabilityProcessInfoResponseItem{
  string id = 1; // 过程ID
  int32 type = 2; // 过程类型
  string name = 3; // 过程名称
  string time = 4; // 时间
  repeated Source source = 5; // 数据源信息
  repeated uint64 source_id = 6; // 数据源ID
  uint64 tigger_source_id = 7; // 触发源ID
  Source tigger_source = 8; // 触发源信息
}

message TraceabilityProcessInfoRequest{
  string id = 1; //@gotags: form:"id" validate:"required" zh:"数据ID"
  int32 page = 2; //@gotags: form:"page" validate:"required,gt=0" zh:"页数"
  int32 per_page = 3; //@gotags: form:"per_page" validate:"required,gt=0" zh:"条数"
}

message TraceabilityProcessInfoResponse{
  int64 total = 1;
  int32 current_page = 2;
  int32 page = 3;
  repeated TraceabilityProcessInfoResponseItem process = 4; // 过程信息
}

message TraceabilityDetailInfoRequest{
  string id = 1; //@gotags: form:"id" validate:"required" zh:"数据ID"
  repeated TraceabilityProcessInfoResponseItem process_info = 2; //@gotags: form:"process_info" validate:"required,min=1" zh:"过程ID"
}

message DeviceTraceabilityDetailInfoResponseItem{
  repeated TraceabilityDetailInfoResponseOriginalDataItem original_data = 1; // 数据信息
  Device merged_data = 2; // 融合数据
  repeated TraceabilityDetailInfoResponseStrategyItem strategies = 3; // 策略信息
  map<string, Source> all_source_for_merge_data = 4; // 所有数据源信息，用于融合
  map<string, Source> all_source_for_strategy = 5; // 所有数据源信息，用于策略
  int32 type = 6; // 节点类型，原样返回参数中的值
  string id = 7; // 数据ID, 原样返回参数中的值
  repeated MapStringString list_fields = 8; // 列表字段

}

message DeviceTraceabilityDetailInfoResponse{
  repeated DeviceTraceabilityDetailInfoResponseItem items = 1; // 数据信息
}

message MapStringString{
  map<string, string> fields = 1;
}

message TraceabilityDetailInfoResponseOriginalDataItem{
  string source_id = 1; // 数据源 ID
  Source source = 2; // 数据源信息
  string data = 3; // 数据
  repeated MapStringString list_fields = 4; // 列表字段
  repeated MapStringString list_detail_fields = 5; // 列表详细字段
}

message Business{
  string system = 1; // 业务系统名称
  string owner = 2; // 业务系统负责人
}

message PortInfo{
  int32 port = 1; // 端口
  string protocol = 2; // 协议
  int32 status = 3; // 状态
  string url = 4; // URL
  string domain = 5; // Domain
  string title = 6; // 网站标题
}

message ListPortInfo{
  repeated PortInfo ports = 1; // 端口信息
}

message Asset{
  string id = 1; // ID
  int32 area_id = 2; // 区域ID
  repeated string process_ids = 3; // 过程表ids
  repeated uint64 source_ids = 4; // 数据源ids
  repeated uint64 node_ids = 5; // 节点ids
  repeated string task_data_ids = 6; // 资产任务ids
  repeated uint64 all_source_ids = 7; // 所有数据源ids
  repeated uint64 all_node_ids = 8; // 所有节点ids
  repeated string all_task_data_ids = 9; // 所有任务ids
  repeated string all_process_ids = 10; // 所有过程ids
  string ip = 11; // IP
  int32 ip_type = 12; // IP类型
  repeated string ip_segment = 13; // IP段
  map<string, string> ip_segment_source = 14; // IP段来源
  repeated string host_name = 15; // 主机名
  map<string, string> host_name_source = 16; // 主机名来源
  repeated string eth_name = 17; // 网卡名
  map<string, string> eth_name_source = 18; // 网卡名来源
  repeated string os = 19; // 操作系统
  map<string, string> os_source = 20; // 操作系统来源
  repeated string kernel = 21; // 内核
  map<string, string> kernel_source = 22; // 内核来源
  repeated string model = 25; // 型号
  map<string, string> model_source = 26; // 型号来源
  repeated string maker = 27; // 制造商
  map<string, string> maker_source = 28; // 制造商来源
  repeated string sn = 29; // 序列号
  map<string, string> sn_source = 30; // 序列号来源
  repeated string mac = 31; // MAC地址
  map<string, string> mac_source = 32; // MAC地址来源
  repeated string product = 33; // 组件
  map<string, string> product_source = 34; // 组件来源
  repeated IpAdminInfoResponseItem business = 35; // 业务系统
  map<string, string> business_system_source = 36; // 业务系统来源
  map<string, string> business_owner_source = 37; // 业务系统负责人来源
  repeated PersonBase oper = 38; // 运维人员
  map<string, string> oper_source = 39; // 运维人员来源
  repeated string machine_room = 40; // 机房
  map<string, string> machine_room_source = 41; // 机房来源
  int32 status = 42; // 状态
  map<string, int32> status_source = 43; // 状态来源
  repeated PortInfo ports = 44; // 端口
  map<string, ListPortInfo> ports_source = 45; // 端口来源
  repeated string memory_size = 46; // 内存大小
  map<string, string> memory_size_source = 47; // 内存大小来源
  repeated string memory_usage_rate = 48; // 内存使用率
  map<string, string> memory_usage_rate_source = 49; // 内存使用率来源
  repeated string cpu_maker = 50; // CPU厂商
  map<string, string> cpu_maker_source = 51; // CPU厂商来源
  repeated string cpu_brand = 52; // CPU品牌
  map<string, string> cpu_brand_source = 53; // CPU品牌来源
  repeated int32 cpu_count = 54; // CPU数量
  map<string, int32> cpu_count_source = 55; // CPU数量来源
  repeated int32 disk_count = 56; // 磁盘数量
  map<string, int32> disk_count_source = 57; // 磁盘数量来源
  repeated int32 disk_size = 58; // 磁盘大小
  map<string, int32> disk_size_source = 59; // 磁盘大小来源
  repeated string disk_usage_rate = 60; // 磁盘使用率
  map<string, string> disk_usage_rate_source = 61; // 磁盘使用率来源
  repeated string load_average = 62; // 负载
  map<string, string> load_average_source = 63; // 负载来源
  int32 network_type = 66; // 网络类型
  string deleted_at = 68; // 删除时间
  string purged_at = 69; // 彻底删除时间
  string created_at = 70; // 创建时间
  string updated_at = 71; // 更新时间
  int32 is_device_extracted = 72; // 是否参与了实体提取
  int32 merge_count = 73; // 融合次数
  string ip_type_text = 74; // IP类型文本
  string network_type_text = 75; // 网络类型文本
  Area area = 76; // 区域
  repeated Source source = 77; // 数据源
  repeated Source all_source = 78; // 所有数据源
  repeated RuleInfo rule_infos = 79; // 组件
  repeated string tags = 80; // 标签信息
  map<string, IpAdminInfoResponseList> business_source = 81; // 业务系统来源
}

message ListAsset{
  repeated Asset assets = 1; // 资产列表
}

message TraceabilityDetailInfoResponseStrategyItem{
  string business_type = 1; // 业务类型
  string field_name = 2; // 字段名称
  string display_name = 3; // 字段显示名称
  map<string, int32> source_priority = 4; // 源优先级
  repeated string untrusted_source = 5; // 不可信源
  int64 version = 6; // 版本
}

message TraceabilityDetailInfoResponseFieldValInfo{
  string field_name = 1; // 字段名称
  repeated int64 source_ids = 2; // 字段采信数据源ID
  repeated int64 node_ids = 3; // 字段采信节点ID
  repeated string task_data_ids = 4; // 字段采信任务ID
  repeated string process_ids = 5; // 字段采信过程ID
}

message IpTraceabilityDetailInfoResponseItem{
  repeated TraceabilityDetailInfoResponseOriginalDataItem original_data = 1; // 数据信息
  Asset merged_data = 2; // 融合数据
  repeated TraceabilityDetailInfoResponseStrategyItem strategies = 3; // 策略信息
  map<string, Source> all_source_for_merge_data = 4; // 所有数据源信息，用于融合
  map<string, Source> all_source_for_strategy = 5; // 所有数据源信息，用于策略
  int32 type = 6; // 节点类型，原样返回参数中的值
  repeated MapStringString list_fields = 7; // 列表字段
  string id = 8; // 数据ID, 原样返回参数中的值
  repeated TraceabilityDetailInfoResponseFieldValInfo field_val_info = 9; // 字段采信信息
}

message IpTraceabilityDetailInfoResponse{
  repeated IpTraceabilityDetailInfoResponseItem items = 1; // 详细信息
}

message DeviceBaseInfoResponse{
  repeated string hostname = 1;
  repeated string sn = 2;
  repeated string mac = 3;
  repeated uint64 source_id = 4;
  repeated Source source = 5;
  repeated uint64 area_id = 6;
  repeated Area area = 7;
  repeated string os = 8;
  repeated string ip = 10; // 映射 IP，目前为空
  repeated string machine_room = 11;
  repeated string oper = 12; // 运维人员，目前为空
  repeated string tags = 13; // 标签，目前为空
}

message DeviceRelatedIpInfoResponse{
  map<string, ListAsset> ip_list = 1; // IP列表,key为内外网标识,value为IP列表
}

message DeviceRelatedEthInfoItem{
  string name = 1; // 网卡名
  string mac = 2; // MAC地址
  string ipv4 = 3; // IP地址
  string ipv6 = 4; // IPV6地址
  string gateway = 5; // 网关
  string netmask = 6; // 子网掩码
  repeated string dns_server = 7; // DNS
  string broadcast = 8; // 广播
  int64 speed = 9; // 速度
  string status = 10; // 状态
}

message DeviceRelatedEthInfoResponse{
  repeated DeviceRelatedEthInfoItem eth_list = 1; // 网卡列表
}

message DeviceTraceabilityBaseInfoResponse{
  repeated uint64 all_source_id = 1; // 所有数据源
  repeated uint64 source_id = 2; // 最后一次融合生效的数据源
  repeated Source all_source = 3; // 所有数据源信息
  repeated Source source = 4; // 最后一次融合生效的数据源信息
  string first_merge_time = 5; // 第一次合并时间
  string last_merge_time = 6; // 最后一次合并时间
  repeated string merge_key = 7; // 合并键
  repeated string sn = 8; // 序列号
  repeated string hostname = 9; // 主机名
  repeated string mac = 10; // MAC地址
  string fid = 11; // 融合key
}