package utils

func SlicesConvertInt32(arr []int) []int32 {
	arr32 := make([]int32, 0)
	for _, v := range arr {
		arr32 = append(arr32, int32(v))
	}
	return arr32
}

func SlicesConvertUint64(arr []int) []uint64 {
	arr32 := make([]uint64, 0)
	for _, v := range arr {
		arr32 = append(arr32, uint64(v))
	}
	return arr32
}

func SlicesConvertMapInt32(arr map[string]int) map[string]int32 {
	arr32 := make(map[string]int32)
	for k, v := range arr {
		arr32[k] = int32(v)
	}
	return arr32
}

func SlicesConvert[T, R any](arr []T, f func(T) R) []R {
	arrR := make([]R, 0)
	for _, v := range arr {
		arrR = append(arrR, f(v))
	}
	return arrR
}
