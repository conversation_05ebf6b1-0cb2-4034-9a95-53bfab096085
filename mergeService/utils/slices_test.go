package utils

import (
	"reflect"
	"strconv"
	"testing"
)

func TestSlicesConvertInt32(t *testing.T) {
	tests := []struct {
		name  string
		input []int
		want  []int32
	}{
		{"空切片", []int{}, []int32{}},
		{"正数", []int{1, 2, 3}, []int32{1, 2, 3}},
		{"负数", []int{-1, -2, -3}, []int32{-1, -2, -3}},
		{"混合", []int{-1, 0, 1}, []int32{-1, 0, 1}},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := SlicesConvertInt32(tt.input); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("SlicesConvertInt32() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestSlicesConvertUint64(t *testing.T) {
	tests := []struct {
		name  string
		input []int
		want  []uint64
	}{
		{"空切片", []int{}, []uint64{}},
		{"正数", []int{1, 2, 3}, []uint64{1, 2, 3}},
		{"零", []int{0, 0, 0}, []uint64{0, 0, 0}},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := SlicesConvertUint64(tt.input); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("SlicesConvertUint64() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestSlicesConvertMapInt32(t *testing.T) {
	tests := []struct {
		name  string
		input map[string]int
		want  map[string]int32
	}{
		{"空映射", map[string]int{}, map[string]int32{}},
		{"正数", map[string]int{"a": 1, "b": 2}, map[string]int32{"a": 1, "b": 2}},
		{"负数", map[string]int{"x": -1, "y": -2}, map[string]int32{"x": -1, "y": -2}},
		{"混合", map[string]int{"p": -1, "q": 0, "r": 1}, map[string]int32{"p": -1, "q": 0, "r": 1}},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := SlicesConvertMapInt32(tt.input); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("SlicesConvertMapInt32() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestSlicesConvert(t *testing.T) {
	tests := []struct {
		name  string
		input []int
		want  []string
	}{
		{"空切片", []int{}, []string{}},
		{"正数", []int{1, 2, 3}, []string{"1", "2", "3"}},
		{"负数", []int{-1, -2, -3}, []string{"-1", "-2", "-3"}},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := SlicesConvert(tt.input, func(i int) string {
				return strconv.Itoa(i)
			})
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("SlicesConvert() = %v, want %v", got, tt.want)
			}
		})
	}
}
