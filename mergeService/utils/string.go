package utils

import "unicode"

// CamelToSnake 将 CamelCase 转换为 snake_case
func CamelToSnake(s string) string {
	// 特殊处理,兼容老数据
	if s == "HostName" {
		return "hostname"
	}

	runes := []rune(s)
	length := len(runes)
	var snake []rune

	for i := 0; i < length; i++ {
		if i > 0 && unicode.IsUpper(runes[i]) &&
			((i+1 < length && unicode.IsLower(runes[i+1])) || unicode.IsLower(runes[i-1])) {
			snake = append(snake, '_')
		}
		snake = append(snake, unicode.ToLower(runes[i]))
	}

	return string(snake)
}
