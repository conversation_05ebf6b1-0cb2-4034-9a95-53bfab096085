package utils

import "testing"

func TestCamelToSnake(t *testing.T) {
	tests := []struct {
		name  string
		input string
		want  string
	}{
		{"空字符串", "", ""},
		{"单个小写字母", "a", "a"},
		{"单个大写字母", "A", "a"},
		{"简单驼峰命名", "camelCase", "camel_case"},
		{"首字母大写", "CamelCase", "camel_case"},
		{"多个连续大写字母", "APIResponse", "api_response"},
		{"混合大小写", "getHTTPResponseCode", "get_http_response_code"},
		{"已经是蛇形命名", "snake_case", "snake_case"},
		{"数字", "user123Name", "user123_name"},
		{"特殊字符", "special$Case", "special$_case"},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := CamelToSnake(tt.input); got != tt.want {
				t.<PERSON>("CamelToSnake() = %v, want %v", got, tt.want)
			}
		})
	}
}
