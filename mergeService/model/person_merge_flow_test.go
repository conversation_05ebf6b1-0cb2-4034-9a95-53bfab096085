package model

import (
	"context"
	"fmt"
	"testing"

	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/pkg/cfg"
	"fobrain/pkg/queue"

	"github.com/alicebob/miniredis/v2"
	redis2 "github.com/go-redis/redis/v8"
	"github.com/olivere/elastic/v7"
	"github.com/stretchr/testify/assert"
)

func TestPersonMerge(t *testing.T) {
	cfg.InitLoadCfg()
	s := miniredis.RunT(t)
	defer s.Close()

	client := redis2.NewClient(&redis2.Options{Addr: s.Addr()})
	rq := &queue.RedisQueue{Client: client}

	qcfg := cfg.LoadQueue()
	for i := 0; i < 10; i++ {
		rq.Push(qcfg.PersonMergeQueue, []map[string]interface{}{{fmt.Sprintf("testKey-%d", i): "testValue"}})
	}
}

func TestGetPersonRelationData_HappyPath(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	mockServer.Register("process_staff/_search", []*elastic.SearchHit{
		{
			Id:     "1",
			Source: []byte(`{"id": "1", "name": "test", "mobile": "12345678901", "unique_key": "1"}`),
		},
	})

	amf := &PersonMergeFlow{}
	data, total, err := amf.GetRelationData(context.Background(), "1")

	assert.NoError(t, err)
	assert.Equal(t, int64(1), total)
	assert.Len(t, data, 1)
	assert.Equal(t, "1", data[0].Id)
	assert.Equal(t, "test", data[0].Name)
	assert.Equal(t, "12345678901", data[0].Mobile)
}

func TestGetPersonRelationData_Error(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	mockServer.Register("process_staff/_search", []*elastic.SearchHit{
		{
			Id:     "1",
			Source: []byte(`error json`),
		},
	})

	amf := &PersonMergeFlow{}
	data, total, err := amf.GetRelationData(context.Background(), "1")

	assert.Error(t, err)
	assert.Equal(t, int64(0), total)
	assert.Nil(t, data)
}

func TestGetPersonRelationData_EmptyResult(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	mockServer.Register("process_staff/_search", []*elastic.SearchHit{
		{
			Id:     "",
			Source: []byte(``),
		},
	})

	amf := &PersonMergeFlow{}
	data, total, err := amf.GetRelationData(context.Background(), "1")

	assert.NoError(t, err)
	assert.Equal(t, int64(0), total)
	assert.Len(t, data, 0)
}

//func TestGetPersonMergeRules_HappyPath(t *testing.T) {
//	// Setup mock redis
//	s, err := miniredis.Run()
//	if err != nil {
//		t.Fatal(err)
//	}
//	defer s.Close()
//
//	client := redis2.NewClient(&redis2.Options{Addr: s.Addr()})
//	testcommon.SetRedisClient(client)
//
//	mockDb := testcommon.GetMysqlMock()
//	defer mockDb.Close()
//	mockDb.ExpectQuery(`
//	SELECT s.*
//		FROM strategy s
//		JOIN (
//			SELECT field_name, MAX(version) AS max_version
//			FROM strategy
//			GROUP BY field_name
//		) sub
//		ON s.field_name = sub.field_name AND s.version = sub.max_version
//		WHERE s.business_type = ?
//		ORDER BY s.field_name ASC
//	`).WithArgs("person_merge").WillReturnRows(sqlmock.NewRows([]string{"id", "field_name"}).
//		AddRow(1, "Rule1").AddRow(2, "Rule2"))
//
//	// Setup mock strategies
//	mockStrategies := []*strategy.Strategy{
//		{FieldName: "Rule1"},
//		{FieldName: "Rule2"},
//	}
//	mockStrategiesJSON, _ := json.Marshal(mockStrategies)
//	redis.GetRedisClient().Set(context.Background(), "merge:rule:person_merge_rules", string(mockStrategiesJSON), time.Duration(30*time.Second))
//
//	// Create PersonMergeFlow instance with mock logger
//	amf := &PersonMergeFlow{
//		mlog: logs.GetLogger("test"),
//	}
//
//	strategies, err := amf.GetMergeRules()
//	assert.NoError(t, err)
//	assert.Equal(t, len(mockStrategies), len(strategies))
//	assert.Equal(t, mockStrategies[0].FieldName, strategies[0].FieldName)
//	assert.Equal(t, mockStrategies[1].FieldName, strategies[1].FieldName)
//}

//func TestPersonWriteResult_HappyPath(t *testing.T) {
//	mockServer := testcommon.NewMockServer()
//	defer mockServer.Close()
//
//	now := localtime.NewLocalTime(time.Now())
//
//	item := make(map[string]*elastic.BulkResponseItem)
//	item["index"] = &elastic.BulkResponseItem{
//		Index:   "staff",
//		Ids:      "test-id1",
//		Status:  201,
//		Version: 1,
//	}
//	mockServer.Register("_bulk", elastic.BulkResponse{
//		Took:   1,
//		Errors: false,
//		Items:  []map[string]*elastic.BulkResponseItem{item},
//	})
//	mockServer.Register("/staff_record/_doc/", []*elastic.SearchHit{
//		{
//			Ids:     "1",
//			Source: []byte(`{"id": "test-id2", "created_at": "` + time.Now().Format("2006-01-02T15:04:05Z07:00") + `"}`),
//		},
//	})
//	amf := &PersonMergeFlow{
//		mlog: logs.GetLogger("test"),
//	}
//
//	resultCh := make(chan *esmodel.Staff, 1)
//	var wg sync.WaitGroup
//	wg.Add(1)
//
//	Staff := &esmodel.Staff{
//		Ids:        "test-id2",
//		CreatedAt: now,
//	}
//	resultCh <- Staff
//	close(resultCh)
//
//	amf.WriteResult()
//
//	wg.Wait()
//
//	// assert.Equal(t, 1, amf.Record.Status)
//}

//func TestPersonWriteResult_WriteError_Staff(t *testing.T) {
//	mockServer := testcommon.NewMockServer()
//	defer mockServer.Close()
//
//	now := localtime.NewLocalTime(time.Now())
//
//	item := make(map[string]*elastic.BulkResponseItem)
//	item["index"] = &elastic.BulkResponseItem{
//		Index:   "Staffs",
//		Ids:      "test-id1",
//		Status:  201,
//		Version: 1,
//	}
//	mockServer.Register("_bulk", elastic.BulkResponse{
//		Took:   1,
//		Errors: true,
//		Items:  []map[string]*elastic.BulkResponseItem{},
//	})
//
//	amf := &PersonMergeFlow{
//		mlog: logs.GetLogger("test"),
//	}
//
//	resultCh := make(chan *esmodel.Staff, 1)
//	var wg sync.WaitGroup
//	wg.Add(1)
//
//	Staff := &esmodel.Staff{
//		Ids:        "test-id2",
//		CreatedAt: now,
//	}
//	resultCh <- Staff
//	close(resultCh)
//
//	amf.WriteResult()
//
//	wg.Wait()
//
//	// assert.Equal(t, 2, amf.Record.Status)
//}
//
//func TestPersonWriteResult_WriteError_StaffRecord(t *testing.T) {
//	mockServer := testcommon.NewMockServer()
//	defer mockServer.Close()
//
//	now := localtime.NewLocalTime(time.Now())
//
//	item := make(map[string]*elastic.BulkResponseItem)
//	item["index"] = &elastic.BulkResponseItem{
//		Index:   "Staffs",
//		Ids:      "test-id1",
//		Status:  201,
//		Version: 1,
//	}
//	mockServer.Register("_bulk", elastic.BulkResponse{
//		Took:   1,
//		Errors: false,
//		Items:  []map[string]*elastic.BulkResponseItem{item},
//	})
//	mockServer.Register("/Staff_record/_doc/", []*elastic.Error{
//		{
//			Status: 400,
//			Details: &elastic.ErrorDetails{
//				Type:   "mapper_parsing_exception",
//				Reason: "failed to parse [created_at]",
//			},
//		},
//	})
//	amf := &PersonMergeFlow{
//		mlog: logs.GetLogger("test"),
//	}
//
//	resultCh := make(chan *esmodel.Staff, 1)
//	var wg sync.WaitGroup
//	wg.Add(1)
//
//	Staff := &esmodel.Staff{
//		Ids:        "test-id2",
//		CreatedAt: now,
//	}
//	resultCh <- Staff
//	close(resultCh)
//
//	amf.WriteResult()
//
//	wg.Wait()
//
//	// assert.Equal(t, 2, amf.Record.Status)
//}
//
//func TestPersonWriteRecord_HappyPath(t *testing.T) {
//	mockServer := testcommon.NewMockServer()
//	defer mockServer.Close()
//
//	amf := &PersonMergeFlow{
//		mlog: logs.GetLogger("test"),
//	}
//
//	// ctx := context.WithValue(context.Background(), "msg", queue.QueueMsg{ID: "msg123"})
//
//	mockServer.Register("/asset_merge_record/_doc/", []*elastic.SearchHit{
//		{
//			Ids:     "1",
//			Source: []byte(`{"id": "test-id2", "created_at": "` + time.Now().Format("2006-01-02T15:04:05Z07:00") + `"}`),
//		},
//	})
//
//	s := miniredis.RunT(t)
//	defer s.Close()
//
//	client := redis2.NewClient(&redis2.Options{Addr: s.Addr()})
//	testcommon.SetRedisClient(client)
//
//	amf.WriteRecord()
//}
