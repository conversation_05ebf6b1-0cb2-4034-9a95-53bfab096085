package model

import (
	"context"
	"fmt"
	"fobrain/fobrain/common/localtime"
	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/initialize/mysql"
	"fobrain/mergeService/model/trigger_merge"
	logs "fobrain/mergeService/utils/log"
	esmodel_asset "fobrain/models/elastic/assets"
	esmodel_device "fobrain/models/elastic/device"
	esmodel_vuln "fobrain/models/elastic/poc"
	esmodel_person "fobrain/models/elastic/staff"
	"fobrain/models/mysql/merge"
	redis_helper "fobrain/models/redis"
	"fobrain/pkg/queue"
	"fobrain/pkg/tracker"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/agiledragon/gomonkey/v2"
	. "github.com/smartystreets/goconvey/convey"

	"github.com/alicebob/miniredis/v2"
	goRedis "github.com/go-redis/redis/v8"
	"github.com/olivere/elastic/v7"
	"github.com/stretchr/testify/assert"
)

func TestBuildCondition(t *testing.T) {
	tests := []struct {
		name         string
		msg          queue.QueueMsg
		mustOrShould string
		expected     []*elastic.BoolQuery
	}{
		{
			name: "Happy Path - Must",
			msg: queue.QueueMsg{
				Values: map[string]interface{}{
					"key1": "value1",
					"key2": "value2",
				},
			},
			mustOrShould: "must",
			expected: []*elastic.BoolQuery{elastic.NewBoolQuery().Must(elastic.NewTermQuery("key1", "value1"), elastic.NewTermQuery("key2", "value2")),
				elastic.NewBoolQuery().Must(elastic.NewTermQuery("key2", "value2"), elastic.NewTermQuery("key1", "value1"))},
		},
		{
			name: "Happy Path - Should",
			msg: queue.QueueMsg{
				Values: map[string]interface{}{
					"key1": "value1",
					"key2": "value2",
				},
			},
			mustOrShould: "should",
			expected: []*elastic.BoolQuery{elastic.NewBoolQuery().Should(elastic.NewTermQuery("key1", "value1"), elastic.NewTermQuery("key2", "value2")),
				elastic.NewBoolQuery().Should(elastic.NewTermQuery("key2", "value2"), elastic.NewTermQuery("key1", "value1"))},
		},
		{
			name: "Edge Case - Empty Msg",
			msg: queue.QueueMsg{
				Values: map[string]interface{}{},
			},
			mustOrShould: "must",
			expected:     []*elastic.BoolQuery{elastic.NewBoolQuery()},
		},
		{
			name: "Edge Case - Invalid mustOrShould",
			msg: queue.QueueMsg{
				Values: map[string]interface{}{
					"key1": "value1",
					"key2": "value2",
				},
			},
			mustOrShould: "invalid",
			expected: []*elastic.BoolQuery{elastic.NewBoolQuery().Should(elastic.NewTermQuery("key1", "value1"), elastic.NewTermQuery("key2", "value2")),
				elastic.NewBoolQuery().Should(elastic.NewTermQuery("key2", "value2"), elastic.NewTermQuery("key1", "value1"))},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := buildCondition(tt.msg.Values, tt.mustOrShould)
			assert.Contains(t, tt.expected, result)
		})
	}
}

func TestNewPersonMergeFlow(t *testing.T) {
	// Happy path test case
	t.Run("HappyPath", func(t *testing.T) {
		flow := NewPersonMergeFlow()
		assert.NotNil(t, flow, "Expected non-nil MergeFlow instance for person merge flow")
	})

	// Edge case: Invalid MergeFlowType
	t.Run("InvalidMergeFlowType", func(t *testing.T) {
		invalidFlow := NewMergeFlow[esmodel_person.ProcessStaff, esmodel_person.Staff, MergePerson]("invalidType")
		assert.Nil(t, invalidFlow, "Expected nil MergeFlow instance for invalid merge flow type")
	})
}

func TestNewDeviceMergeFlow(t *testing.T) {
	// Happy path test case
	t.Run("HappyPath", func(t *testing.T) {
		flow := NewDeviceMergeFlow()
		assert.NotNil(t, flow, "Expected non-nil MergeFlow instance for device merge flow")
	})

	// Edge case: Invalid MergeFlowType
	t.Run("InvalidMergeFlowType", func(t *testing.T) {
		invalidFlow := NewMergeFlow[esmodel_asset.ProcessAssets, esmodel_device.Device, MergeDevice]("invalidType")
		assert.Nil(t, invalidFlow, "Expected nil MergeFlow instance for invalid merge flow type")
	})
}

func TestNewAssetMergeFlow(t *testing.T) {
	// Happy path test case
	t.Run("HappyPath", func(t *testing.T) {
		flow := NewAssetMergeFlow()
		assert.NotNil(t, flow, "Expected non-nil MergeFlow instance for asset merge flow")
	})

	// Edge case: Invalid MergeFlowType
	t.Run("InvalidMergeFlowType", func(t *testing.T) {
		invalidFlow := NewMergeFlow[esmodel_asset.ProcessAssets, esmodel_asset.Assets, MergeAsset]("invalidType")
		assert.Nil(t, invalidFlow, "Expected nil MergeFlow instance for invalid merge flow type")
	})
}

func TestNewVulnMergeFlow(t *testing.T) {
	// Happy path test case
	t.Run("HappyPath", func(t *testing.T) {
		flow := NewVulnMergeFlow()
		assert.NotNil(t, flow, "Expected non-nil MergeFlow instance for vulnerability merge flow")
	})

	// Edge case: Invalid MergeFlowType
	t.Run("InvalidMergeFlowType", func(t *testing.T) {
		invalidFlow := NewMergeFlow[esmodel_vuln.ProcessPoc, esmodel_vuln.Poc, MergeVuln]("invalidType")
		assert.Nil(t, invalidFlow, "Expected nil MergeFlow instance for invalid merge flow type")
	})
}

func TestGetTaskId(t *testing.T) {
	tests := []struct {
		name    string
		msg     queue.QueueMsg
		want    string
		wantErr bool
	}{
		{
			name: "Happy Path - int64",
			msg: queue.QueueMsg{
				Values: map[string]interface{}{
					"task_id": int64(123),
				},
			},
			want:    "123",
			wantErr: false,
		},
		{
			name: "Happy Path - int",
			msg: queue.QueueMsg{
				Values: map[string]interface{}{
					"task_id": int(123),
				},
			},
			want:    "123",
			wantErr: false,
		},
		{
			name: "Happy Path - float64",
			msg: queue.QueueMsg{
				Values: map[string]interface{}{
					"task_id": float64(123),
				},
			},
			want:    "123",
			wantErr: false,
		},
		{
			name: "Happy Path - string",
			msg: queue.QueueMsg{
				Values: map[string]interface{}{
					"task_id": "123",
				},
			},
			want:    "123",
			wantErr: false,
		},
		{
			name: "Error - Missing task_id",
			msg: queue.QueueMsg{
				Values: map[string]interface{}{},
			},
			want:    "",
			wantErr: true,
		},
		{
			name: "Happy Path - task_id 0",
			msg: queue.QueueMsg{
				Values: map[string]interface{}{
					"task_id": int64(0),
				},
			},
			want:    "0",
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := getTaskId(tt.msg)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.want, got)
			}
		})
	}
}

func TestStartHandle(t *testing.T) {
	// Setup
	s, err := miniredis.Run()
	if err != nil {
		t.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
	}
	defer s.Close()

	testcommon.SetRedisClient(goRedis.NewClient(&goRedis.Options{
		Addr: s.Addr(),
	}))

	// Mock data_sync_child_task.NewDataSyncChildTaskModel().UpdateMergeData
	mockDb := testcommon.GetMysqlMock()
	// defer mockDb.Close()
	mockDb.ExpectQuery(`
	SELECT * FROM data_sync_child_tasks WHERE id = ? ORDER BY data_sync_child_tasks.id LIMIT 1
	`).WithArgs(123).WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(123))
	mockDb.ExpectExec(`
	update data_sync_child_task set merge_start = ? where id = ?
	`).WithArgs("1").WillReturnResult(sqlmock.NewResult(1, 1))

	mlog := logs.GetLogger("test")

	// Test cases
	tests := []struct {
		name     string
		msg      queue.QueueMsg
		expected bool
		wantErr  bool
	}{
		{
			name: "is_start is string '1'",
			msg: queue.QueueMsg{
				Values: map[string]interface{}{
					"is_start":  "1",
					"mr_id":     "123",
					"task_type": "sync",
					"task_id":   "123",
				},
			},
			expected: true,
			wantErr:  false,
		},
		{
			name: "is_start is bool true",
			msg: queue.QueueMsg{
				Values: map[string]interface{}{
					"task_type": "sync",
					"mr_id":     "123",
					"is_start":  true,
					"task_id":   "123",
				},
			},
			expected: true,
			wantErr:  false,
		},
		{
			name: "is_start is int 1",
			msg: queue.QueueMsg{
				Values: map[string]interface{}{
					"task_type": "sync",
					"mr_id":     "123",
					"is_start":  int(1),
					"task_id":   "123",
				},
			},
			expected: true,
			wantErr:  false,
		},
		{
			name: "is_start is string '0'",
			msg: queue.QueueMsg{
				Values: map[string]interface{}{
					"task_type": "sync",
					"mr_id":     "123",
					"is_start":  "0",
					"task_id":   "123",
				},
			},
			expected: false,
			wantErr:  false,
		},
		{
			name: "is_start is bool false",
			msg: queue.QueueMsg{
				Values: map[string]interface{}{
					"task_type": "sync",
					"mr_id":     "123",
					"is_start":  false,
					"task_id":   "123",
				},
			},
			expected: false,
			wantErr:  false,
		},
		{
			name: "is_start is int 0",
			msg: queue.QueueMsg{
				Values: map[string]interface{}{
					"task_type": "sync",
					"mr_id":     "123",
					"is_start":  int(0),
					"task_id":   "123",
				},
			},
			expected: false,
			wantErr:  false,
		},
		{
			name: "is_start is missing",
			msg: queue.QueueMsg{
				Values: map[string]interface{}{},
			},
			expected: false,
			wantErr:  false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := startHandle(tt.msg, "123", "asset", mlog)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expected, result)
			}
		})
	}
}

func TestUpdateMergeInfoToTask_HappyPath(t *testing.T) {
	task := merge.NewMergeRecordsModel()
	// Mock data_sync_child_task.NewDataSyncChildTaskModel().UpdateMergeData
	mock := gomonkey.ApplyMethodReturn(task, "UpdateByTaskIdAndMergeType", nil)
	defer mock.Reset()

	// Setup mock redis
	s, err := miniredis.Run()
	if err != nil {
		t.Fatal(err)
	}
	defer s.Close()

	client := goRedis.NewClient(&goRedis.Options{Addr: s.Addr()})
	testcommon.SetRedisClient(client)

	ctx := context.Background()
	taskId := "123"
	now := time.Now()
	businessType := "device_merge"
	totaltimeKey := redis_helper.GenerateMergeTaskKey(fmt.Sprintf("%v", taskId), businessType, "totaltime")
	startKey := redis_helper.GenerateMergeTaskKey(fmt.Sprintf("%v", taskId), businessType, "start")
	countKey := redis_helper.GenerateMergeTaskKey(fmt.Sprintf("%v", taskId), businessType, "count")
	failedCountKey := redis_helper.GenerateMergeTaskKey(fmt.Sprintf("%v", taskId), businessType, "failed_count")

	client.Set(ctx, totaltimeKey, 10, 0)
	client.Set(ctx, startKey, now.Format("2006-01-02 15:04:05"), 0)
	client.Set(ctx, countKey, 100, 0)
	client.Set(ctx, failedCountKey, 5, 0)

	count, successCount, failedCount, discardedCount, start, end, workHours, duration, err := UpdateMergeInfoToTask(uint64(123), merge.TaskTypeSync, taskId, businessType, true)
	assert.NoError(t, err)
	assert.Equal(t, 105, count)
	assert.Equal(t, 100, successCount)
	assert.Equal(t, 5, failedCount)
	assert.Equal(t, 0, discardedCount)
	assert.Equal(t, 10*time.Millisecond, workHours)
	assert.Greater(t, end, now)
	assert.Greater(t, duration, time.Duration.Microseconds(1))
	assert.Equal(t, now.Format("2006-01-02 15:04:05"), start.Format("2006-01-02 15:04:05"))
}

func TestUpdateMergeInfoToTask_NoRedisData(t *testing.T) {
	// Setup mock redis
	s, err := miniredis.Run()
	if err != nil {
		t.Fatal(err)
	}
	defer s.Close()

	client := goRedis.NewClient(&goRedis.Options{Addr: s.Addr()})
	testcommon.SetRedisClient(client)

	taskId := "123"
	businessType := "device_merge"

	count, _, _, _, _, _, workHours, duration, err := UpdateMergeInfoToTask(uint64(123), merge.TaskTypeSync, taskId, businessType, true)
	assert.Error(t, err)
	assert.Equal(t, 0, count)
	assert.Equal(t, 0*time.Millisecond, workHours)
	assert.Equal(t, duration, 0*time.Millisecond)
}

func TestBatchEndHandle_HappyPath(t *testing.T) {
	now := time.Now()
	mock := gomonkey.ApplyFuncReturn(UpdateMergeInfoToTask, 100, 100, 0, 0, now, now, 10*time.Millisecond, 10*time.Millisecond, nil)
	defer mock.Reset()

	// Setup mock redis
	s, err := miniredis.Run()
	if err != nil {
		t.Fatal(err)
	}
	defer s.Close()

	client := goRedis.NewClient(&goRedis.Options{Addr: s.Addr()})
	testcommon.SetRedisClient(client)

	taskId := "123"
	businessType := "device_merge"
	count, start, end, workHours, duration, err := updateMergeTaskAndDelRedis(uint64(123), merge.TaskTypeSync, taskId, businessType)

	assert.NoError(t, err)
	assert.Equal(t, 100, count)
	assert.Equal(t, 10*time.Millisecond, workHours)
	assert.Equal(t, 10*time.Millisecond, duration)
	assert.Equal(t, start, now)
	assert.Equal(t, end, now)
}

func TestBatchEndHandle_RedisError(t *testing.T) {
	now := time.Now()
	mock := gomonkey.ApplyFuncReturn(UpdateMergeInfoToTask, 100, 100, 0, 0, now, now, 10*time.Millisecond, 10*time.Millisecond, nil)

	defer mock.Reset()

	// Setup mock redis
	s, err := miniredis.Run()
	if err != nil {
		t.Fatal(err)
	}
	defer s.Close()

	client := goRedis.NewClient(&goRedis.Options{Addr: s.Addr()})
	testcommon.SetRedisClient(client)

	// mock redis error
	s.SetError("ERR")

	taskId := "123"
	businessType := "device_merge"
	_, _, _, _, _, err = updateMergeTaskAndDelRedis(uint64(123), merge.TaskTypeSync, taskId, businessType)

	assert.Error(t, err)
}

func TestPrintDutation_HappyPath(t *testing.T) {
	// Setup mock redis
	s, err := miniredis.Run()
	if err != nil {
		t.Fatal(err)
	}
	defer s.Close()

	client := goRedis.NewClient(&goRedis.Options{Addr: s.Addr()})
	testcommon.SetRedisClient(client)

	tracker := tracker.NewProcessingTimeTracker(1*time.Minute, "test")
	signalChan := make(chan struct{}, 1)
	mlog := logs.GetLogger("test")
	enablePrintDuration := printDutation(tracker, signalChan, "test", mlog)
	assert.Equal(t, true, enablePrintDuration)
}

func TestPrintDutation_Error(t *testing.T) {
	// Setup mock redis
	s, err := miniredis.Run()
	if err != nil {
		t.Fatal(err)
	}
	defer s.Close()

	client := goRedis.NewClient(&goRedis.Options{Addr: s.Addr()})
	testcommon.SetRedisClient(client)

	tracker := tracker.NewProcessingTimeTracker(1*time.Minute, "test")
	signalChan := make(chan struct{}, 1)
	mlog := logs.GetLogger("test")
	printDutation(tracker, signalChan, "test", mlog)
	// Wait for 1 second to ensure the printDutation function is blocked
	time.Sleep(time.Second)
	enablePrintDuration := printDutation(tracker, signalChan, "test", mlog)
	assert.Equal(t, false, enablePrintDuration)
}

func TestEndHandle(t *testing.T) {
	Convey("Test EndHandle", t, func() {
		// Setup mock redis
		s, err := miniredis.Run()
		if err != nil {
			t.Fatal(err)
		}
		defer s.Close()

		client := goRedis.NewClient(&goRedis.Options{Addr: s.Addr()})
		testcommon.SetRedisClient(client)

		Convey("Test EndHandle happy path", func() {
			mock := gomonkey.ApplyFuncReturn(updateMergeTaskAndDelRedis, 100, time.Now(), time.Now(), 10*time.Millisecond, 10*time.Millisecond, nil)
			defer mock.Reset()

			taskId := "123"
			businesstype := "device_merge"
			msg := queue.QueueMsg{
				Values: map[string]interface{}{
					"mr_id":   "123",
					"task_id": 123,
					"is_end":  1,
					"total":   10,
				},
			}
			isEnd := endHandle(msg, taskId, businesstype, logs.GetLogger("test"))
			So(isEnd, ShouldEqual, true)
		})
	})
}
func TestTriggerPocMergeByAsset(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()
	mockServer.Register("asset/_search", []*elastic.SearchHit{
		{
			Id:     "1",
			Source: []byte(`{"ip":"***********","area":1}`),
		},
	})
	mockServer.Register("/_search/scroll", []*elastic.SearchHit{})
	mockServer.Register("poc/_search", []*elastic.SearchHit{
		{
			Id:     "1",
			Source: []byte(`{"id":"***********"}`),
		},
	})
	// 设置 miniredis
	s, err := miniredis.Run()
	if err != nil {
		t.Fatalf("无法启动 miniredis: %v", err)
	}
	defer s.Close()

	// 创建 Redis 客户端
	cli := goRedis.NewClient(&goRedis.Options{
		Addr: s.Addr(),
	})
	testcommon.SetRedisClient(cli)

	// 使用 gomonkey 模拟函数
	patches := gomonkey.NewPatches()
	defer patches.Reset()

	// 模拟 DeleteKeysByPrefixWithScan 函数
	patches.ApplyFunc(trigger_merge.TriggerMergeForVuln, func(taskParams *trigger_merge.TriggerParamsForVuln) error {
		return nil
	})

	triggerPocMergeByAsset(&merge.MergeRecords{
		BaseModel: mysql.BaseModel{
			Id: 1,
		},
		StartTime: localtime.Now(),
	}, "asset", "123", logs.GetLogger("asset"), map[string]struct{}{"10.10.10.10_1": {}})
}
func TestTriggerDeviceMergeByAsset(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()
	mockServer.Register("asset/_search", []*elastic.SearchHit{
		{
			Id:     "1",
			Source: []byte(`{"ip":"***********","area":1}`),
		},
	})
	mockServer.Register("/_search/scroll", []*elastic.SearchHit{})
	mockServer.Register("device/_search", []*elastic.SearchHit{
		{
			Id:     "1",
			Source: []byte(`{"id":"***********"}`),
		},
	})
	// 设置 miniredis
	s, err := miniredis.Run()
	if err != nil {
		t.Fatalf("无法启动 miniredis: %v", err)
	}
	defer s.Close()

	// 创建 Redis 客户端
	cli := goRedis.NewClient(&goRedis.Options{
		Addr: s.Addr(),
	})
	testcommon.SetRedisClient(cli)

	// 使用 gomonkey 模拟函数
	patches := gomonkey.NewPatches()
	defer patches.Reset()

	// 模拟 DeleteKeysByPrefixWithScan 函数
	patches.ApplyFunc(trigger_merge.TriggerMergeForDevice, func(taskParams *trigger_merge.TriggerParamsForDevice) error {
		return nil
	})

	triggerDeviceMergeByAsset(&merge.MergeRecords{
		BaseModel: mysql.BaseModel{
			Id: 1,
		},
		StartTime: localtime.Now(),
	}, "asset", "123", logs.GetLogger("asset"), map[string]struct{}{"10.10.10.10_1": {}})
}

func TestFetchDeviceIds(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	mockServer.Register("device/_search", []*elastic.SearchHit{
		{
			Id: "device1",
			Source: []byte(`{
				"id": "device1",
				"public_ip": ["***********"],
				"private_ip": ["********"],
				"area": [1]
			}`),
		},
		{
			Id: "device2",
			Source: []byte(`{
				"id": "device2",
				"public_ip": ["***********"],
				"private_ip": ["********"],
				"area": [2]
			}`),
		},
	})
	mockServer.Register("/_search/scroll", []*elastic.SearchHit{})

	changedSet := map[string]struct{}{
		"***********_1": {},
		"********_1":    {},
		"***********_2": {},
	}

	ids, err := FetchDeviceIds(context.Background(), changedSet)
	assert.NoError(t, err)
	assert.ElementsMatch(t, []string{"device1", "device2"}, ids)
}

func TestFetchDeviceIds_NoMatch(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	mockServer.Register("device/_search", []*elastic.SearchHit{})

	changedSet := map[string]struct{}{"192.168.1.3_3": {}}

	ids, err := FetchDeviceIds(context.Background(), changedSet)
	assert.NoError(t, err)
	assert.Empty(t, ids)
}

func TestFetchDeviceIds_Error(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	mockServer.Register("device/_search", &elastic.Error{
		Status: 500,
	})

	changedSet := map[string]struct{}{"***********_1": {}}

	ids, err := FetchDeviceIds(context.Background(), changedSet)
	assert.Error(t, err)
	assert.Empty(t, ids)
}

func TestFetchPocIds(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	mockServer.Register("poc/_search", []*elastic.SearchHit{
		{
			Id: "poc1",
			Source: []byte(`{
"id":"poc1",
				"ip": "***********",
				"area": 1
			}`),
		},
		{
			Id: "poc2",
			Source: []byte(`{
"id":"poc2",
				"ip": "***********",
				"area": 2
			}`),
		},
	})
	mockServer.Register("/_search/scroll", []*elastic.SearchHit{})

	changedSet := map[string]struct{}{
		"***********_1": {},
		"***********_2": {},
	}

	ids, err := FetchPocIds(context.Background(), changedSet)
	assert.NoError(t, err)
	assert.ElementsMatch(t, []string{"poc1", "poc2"}, ids)
}

func TestFetchPocIds_NoMatch(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	mockServer.Register("poc/_search", []*elastic.SearchHit{})

	changedSet := map[string]struct{}{"192.168.1.3_3": {}}

	ids, err := FetchPocIds(context.Background(), changedSet)
	assert.NoError(t, err)
	assert.Empty(t, ids)
}

func TestFetchPocIds_Error(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	mockServer.Register("poc/_search", &elastic.Error{Status: 500})

	changedSet := map[string]struct{}{"***********_1": {}}

	ids, err := FetchPocIds(context.Background(), changedSet)
	assert.Error(t, err)
	assert.Empty(t, ids)
}

func TestForceRefreshIndex(t *testing.T) {
	// 设置日志
	logPatch := gomonkey.ApplyFuncReturn(logs.GetLogger, logs.GetLogger("test"))
	defer logPatch.Reset()

	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	t.Run("测试资产索引刷新", func(t *testing.T) {
		// 模拟ES返回成功响应
		mockServer.Register("_refresh", map[string]interface{}{
			"_shards": map[string]interface{}{
				"total":      1,
				"successful": 1,
				"failed":     0,
			},
		})

		ForceRefreshIndex("asset")
	})

	t.Run("测试设备索引刷新", func(t *testing.T) {
		// 模拟ES返回成功响应
		mockServer.Register("_refresh", map[string]interface{}{
			"_shards": map[string]interface{}{
				"total":      1,
				"successful": 1,
				"failed":     0,
			},
		})

		ForceRefreshIndex("device")
	})

	t.Run("测试漏洞索引刷新", func(t *testing.T) {
		// 模拟ES返回成功响应
		mockServer.Register("_refresh", map[string]interface{}{
			"_shards": map[string]interface{}{
				"total":      1,
				"successful": 1,
				"failed":     0,
			},
		})

		ForceRefreshIndex("vuln")
	})

	t.Run("测试无效数据类型", func(t *testing.T) {
		// 模拟ES返回成功响应
		mockServer.Register("_refresh", map[string]interface{}{
			"_shards": map[string]interface{}{
				"total":      1,
				"successful": 1,
				"failed":     0,
			},
		})

		ForceRefreshIndex("invalid_type")
	})
}
