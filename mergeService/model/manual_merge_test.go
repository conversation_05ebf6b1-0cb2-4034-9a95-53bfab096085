package model

// func Test_ExecuteMergeWithScript_Normally(t *testing.T) {
// 	business_type := MergeFlowType_AssetMerge
// 	field := "business"
// 	priorityMap := map[uint64][]uint64{1: {2, 3}}
// 	untrustedSources := strategy.UntrustedSource{4}
// 	count, err := ExecuteMergeWithScript(business_type, field, priorityMap, untrustedSources)
// 	assert.Empty(t, err)
// 	assert.GreaterOrEqual(t, count, int64(1))
// }

// func Test_ExecuteReindexWithScript_Normally(t *testing.T) {
// 	business_type := MergeFlowType_AssetMerge
// 	fields := []string{"hostname", "eth_name"}
// 	batchNo := UUIDStr()
// 	count, err := ExecuteReindexWithScript(business_type, batchNo, fields)
// 	assert.Empty(t, err)
// 	assert.GreaterOrEqual(t, count, int64(1))
// }
