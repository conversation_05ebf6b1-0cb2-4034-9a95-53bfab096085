// 融合策略修改后，手动合并，立即融合

package model

import (
	"context"
	"fmt"
	"fobrain/fobrain/common/localtime"
	"fobrain/initialize/es"
	"fobrain/mergeService/utils"
	logs "fobrain/mergeService/utils/log"
	esmodel_asset "fobrain/models/elastic/assets"
	esmodel_device "fobrain/models/elastic/device"
	esmodel_vuln "fobrain/models/elastic/poc"
	esmodel_person "fobrain/models/elastic/staff"
	"fobrain/models/mysql/strategy"
	"slices"
	"strings"
	"time"

	"github.com/olivere/elastic/v7"
)

const (
	// 合并脚本模板,用于合并数组类型的字段
	mergeScriptTemplateForSlice = `
		def sourceData = new HashMap(ctx._source.{{{source_field}}});
		def sourcePriority = params.SourceData;

		def resultSet = new HashSet();  // Use a Set to ensure uniqueness
		boolean found = false;
		// Create a pattern for splitting strings by comma
		def pattern = /,/;

		// Check each priority level
		for (priority in sourcePriority.keySet().stream().sorted().collect(Collectors.toList())) {
			if (found) break;
			def sources = sourcePriority[priority];
			for (s in sources) {
				for (entry in sourceData.entrySet()) {
					if (entry.getKey().startsWith(s + "-") && entry.getValue() != "") {
						def value = entry.getValue();
						if ("{{{target_field}}}" == "product" && value instanceof String) {
							// Split the string by comma and add each element to the set
							for (v in pattern.split(value)) {
								resultSet.add(v.trim());  // Trim whitespace and add to the set
							}
						} else if (value instanceof List) {
							// If the value is a list, add each element to the set
							for (v in value) {
								resultSet.add(v);
							}
						} else {
							// Otherwise, add the single value to the set
							resultSet.add(value);
						}
						found = true;
					} else if (entry.getKey()=="0"){
					 	// 0 表示是人工校准数据
						resultSet.add(entry.getValue());
					}
				}
			}
		}

		// Convert the Set to a List
		def result = new ArrayList(resultSet);

		// If no valid sourceData found, set result to empty
		if (result.isEmpty() && !found) {
			result = [""];
		}

		ctx._source.{{{target_field}}} = result;
		// 合并次数+1
		ctx._source.merge_count = ctx._source.merge_count + 1;
`
	// 合并脚本模板,用于合并单个值的字段
	mergeScriptTemplateForSingle = `
		def sourceData = new HashMap(ctx._source.{{{source_field}}});
		def sourcePriority = params.SourceData;

		def result = 1;
		boolean found = false;

		// Check each priority level
		for (priority in sourcePriority.keySet().stream().sorted().collect(Collectors.toList())) {
			if (found) break;
			def sources = sourcePriority[priority];
			for (s in sources) {
				for (entry in sourceData.entrySet()) {
					if (entry.getKey().startsWith(s + "-") && entry.getValue() != "") {
						result = entry.getValue();
						found = true;
					}
				}
			}
		}

		ctx._source.{{{target_field}}} = result;
		// 合并次数+1
		ctx._source.merge_count = ctx._source.merge_count + 1;
`

	// 合并脚本模板,用于合并复合值的字段-业务字段
	mergeScriptTemplateForBusiness = `
		def system_source = new HashMap(ctx._source.business_system_source);
		def owner_source = ctx._source.business_owner_source;
		def sourceData = params.SourceData;
		def business = [];

		// Temporary map to hold valid business elements
		def temp_business = new HashSet();
		def found = false;  // 添加一个标志来指示是否找到了值

		// 检查每个优先级
		for (priority in sourceData.keySet().stream().sorted().collect(Collectors.toList())) {
			// 获取当前优先级的所有源
			def sources = sourceData[priority];
			for (source in sources) {
				// 构造系统和所有者的键前缀
				def key_prefix = source + "-";
				
				// 遍历系统源中的每个条目
				for (entry in system_source.entrySet()) {
					if (entry.getKey().startsWith(key_prefix)) {
						def system_value = entry.getValue();
						def owner_value = owner_source.get(entry.getKey());

						// 如果系统源值或所有者源值任意一个不为空，则构造一个新的 business 元素
						if (system_value != "" || owner_value != "") {
							def new_business = [
								"owner": owner_value,
								"system": system_value
							];
							// 将新的 business 元素添加到临时集合中
							temp_business.add(new_business);
							found = true;  // 设置标志为true，表示已找到值
						}
					}
				}
			}
			if (found) break;  // 如果在当前优先级的源中找到值，跳出源循环
		}

		// 确保唯一性
		def existing_business_set = new HashSet(business);
		for (item in temp_business) {
			if (!existing_business_set.contains(item)) {
				business.add(item);
			}
		}

		ctx._source.business = business;
		// 合并次数+1
		ctx._source.merge_count = ctx._source.merge_count + 1;
`

	// 合并脚本模板,用于合并nested复杂对象字段
	mergeScriptTemplateForNested = `
		// 安全检查字段是否存在并初始化
		def sourceData = new HashMap();
		if (ctx._source.containsKey("{{{source_field}}}") && ctx._source.{{{source_field}}} != null) {
			try {
				sourceData = new HashMap(ctx._source.{{{source_field}}});
			} catch (Exception e) {
				sourceData = new HashMap();
			}
		}
		
		def sourcePriority = params.SourceData;

		def resultList = new ArrayList();  // Final result list
		def resultStrings = new HashSet();  // Use strings for deduplication
		boolean found = false;

		// Check each priority level
		for (priority in sourcePriority.keySet().stream().sorted().collect(Collectors.toList())) {
			if (found) break;
			def sources = sourcePriority[priority];
			for (s in sources) {
				for (entry in sourceData.entrySet()) {
					if (entry.getKey().startsWith(s + "-") && entry.getValue() != null && entry.getValue() != "") {
						def value = entry.getValue();
						if (value instanceof List) {
							// Handle array of nested objects
							for (item in value) {
								if (item != null) {
									def itemStr = item.toString();
									if (!resultStrings.contains(itemStr)) {
										resultStrings.add(itemStr);
										resultList.add(item);
									}
								}
							}
						} else if (value instanceof Map) {
							// Handle single nested object
							def valueStr = value.toString();
							if (!resultStrings.contains(valueStr)) {
								resultStrings.add(valueStr);
								resultList.add(value);
							}
						}
						found = true;
					} else if (entry.getKey() == "0") {
						// 0 表示是人工校准数据
						def value = entry.getValue();
						if (value instanceof List) {
							for (item in value) {
								if (item != null) {
									def itemStr = item.toString();
									if (!resultStrings.contains(itemStr)) {
										resultStrings.add(itemStr);
										resultList.add(item);
									}
								}
							}
						} else if (value instanceof Map) {
							def valueStr = value.toString();
							if (!resultStrings.contains(valueStr)) {
								resultStrings.add(valueStr);
								resultList.add(value);
							}
						}
						found = true;
					}
				}
			}
		}

		// If no valid sourceData found, set result to empty
		if (resultList.isEmpty() && !found) {
			resultList = [];
		}

		ctx._source.{{{target_field}}} = resultList;
		// 合并次数+1
		ctx._source.merge_count = ctx._source.merge_count + 1;
	`

	// reindex 备份数据脚本模板
	reindexScriptTemplate = `
		def fields = params.Fields;
		def newSource = new HashMap();

		def Asset=new HashMap();
		for (field in fields) {
			Asset[field] = ctx._source[field];
		}
		newSource[params.FieldType] = Asset;

		newSource["id"]= UUID.randomUUID().toString().replace('-', '');
		newSource["batch_no"] = params.BatchNo;
		newSource["asset_id"]= ctx._source.id;
		newSource["ip"]= ctx._source.ip;
		newSource["area"]= ctx._source.area;
		newSource["backup_mode"]="diff";
		newSource["backup_time"]= params.Now;
		ctx._source = newSource;
		ctx._id = newSource["id"];
	`
)

var (
	// 允许合并的字段
	allowMergeField = []string{"ipsegment", "hostname", "ethname", "os", "kernel", "model", "maker", "sn", "mac", "product", "oper", "business", "machineroom", "memorysize", "memoryusagerate", "cpumaker", "cpubrand", "cpucount", "diskcount", "disksize", "diskusagerate", "loadaverage", "ports", "status", "jarpackageinfo"}
)

// generateMergeScript 生成合并脚本
func generateMergeScript(sourceField, targetField string) string {
	template := mergeScriptTemplateForSlice
	if sourceField == "status" {
		template = mergeScriptTemplateForSingle
	} else if sourceField == "business" {
		template = mergeScriptTemplateForBusiness
	} else if isNestedField(sourceField) {
		template = mergeScriptTemplateForNested
	}
	s := strings.Replace(template, "{{{source_field}}}", targetField, -1)
	s = strings.Replace(s, "{{{target_field}}}", sourceField, -1)
	// 打印合并脚本
	// fmt.Println(s)
	return s
}

// isNestedField 判断字段是否为nested复杂对象字段
func isNestedField(field string) bool {
	// 定义需要特殊处理的nested字段
	nestedFields := []string{"jar_package_info"}

	// 检查明确的nested字段
	for _, nestedField := range nestedFields {
		if field == nestedField {
			return true
		}
	}

	return false
}

// ExecuteMergeWithScript 执行合并脚本，根据优先级合并数据
// field: 要合并的字段名
// sourceData: 优先级映射，key为优先级，value为要合并的SourceId列表
func ExecuteMergeWithScript(business_type, orgiField, batchNo string, sourceData map[uint64][]string) (int64, error) {
	logs := logs.GetLogger("service")
	field := strings.ToLower(orgiField)
	if field == "" || !slices.Contains(allowMergeField, field) {
		return 0, fmt.Errorf("field not allowed")
	}
	now := time.Now()
	snakeField := utils.CamelToSnake(orgiField)
	targetField := snakeField + "_source"
	s := generateMergeScript(snakeField, targetField)
	script := elastic.NewScriptInline(s).Lang("painless").Params(map[string]interface{}{
		"SourceData": sourceData,
	})
	indexName := ""
	switch business_type {
	case MergeFlowType_Device:
		indexName = esmodel_device.NewDeviceModel().IndexName()
	case MergeFlowType_Asset:
		indexName = esmodel_asset.NewAssets().IndexName()
	case MergeFlowType_Person:
		indexName = esmodel_person.NewStaff().IndexName()
	case MergeFlowType_Vulnerability:
		indexName = esmodel_vuln.NewPoc().IndexName()
	}
	ctx, cancel := context.WithTimeout(context.Background(), 55*time.Second)
	defer func() {
		logs.Warnf("ExecuteMergeWithScript end, businessType: %s, batchNo: %s,start: %s, end: %s", business_type, batchNo, now.Format("2006-01-02 15:04:05"), time.Now().Format("2006-01-02 15:04:05"))
		cancel()
	}()
	updated := int64(0)
	var err error
	// 创建一个done通道，用于通知主线程任务完成
	done := make(chan struct{})
	go func() {
		defer close(done)
		updateService := es.GetEsClient().UpdateByQuery(indexName).Query(elastic.NewMatchAllQuery()).Script(script).Refresh("true")
		resp, updateErr := updateService.Do(ctx)
		err = updateErr
		if updateErr == nil {
			updated = resp.Updated
			logs.Infof("ExecuteMergeWithScript done, businessType: %s, batchNo: %s, total: %d, updated: %d, took: %dms", business_type, batchNo, resp.Total, resp.Updated, resp.Took)
		}
	}()
FOR:
	for {
		select {
		case <-ctx.Done():
			if ctx.Err() == context.DeadlineExceeded {
				logs.Warnf("ExecuteMergeWithScript timeout exceeded, businessType: %s, batchNo: %s,start: %s, end: %s", business_type, batchNo, now.Format("2006-01-02 15:04:05"), time.Now().Format("2006-01-02 15:04:05"))
				return 0, err
			}
			break FOR
		case <-done:
			break FOR
		}
	}
	return updated, err
}

// ExecuteReindexWithScript 执行reindex脚本，将原始数据拷贝到备份索引中
func ExecuteReindexWithScript(business_type, batchNo string, fields []string, ids []string) (int64, error) {
	logs := logs.GetLogger("service")
	now := time.Now()
	fieldType := "Asset"
	if business_type == MergeFlowType_Device {
		fieldType = "Device"
	}
	script := elastic.NewScriptInline(reindexScriptTemplate).Lang("painless").Params(map[string]interface{}{
		"Fields":    fields,
		"BatchNo":   batchNo,
		"FieldType": fieldType,
		"Now":       now.Format("2006-01-02 15:04:05"),
	})
	sourceIndexName := ""
	destIndexName := ""
	switch business_type {
	case MergeFlowType_Device:
		sourceIndexName = esmodel_device.NewDeviceModel().IndexName()
		destIndexName = esmodel_device.NewDeviceRecordModel().IndexName()
	case MergeFlowType_Asset:
		sourceIndexName = esmodel_asset.NewAssets().IndexName()
		destIndexName = esmodel_asset.NewAssetRecord().IndexName()
	case MergeFlowType_Person:
		sourceIndexName = esmodel_person.NewStaff().IndexName()
		destIndexName = esmodel_person.NewStaffRecord().IndexName()
	case MergeFlowType_Vulnerability:
		sourceIndexName = esmodel_vuln.NewPoc().IndexName()
		destIndexName = esmodel_vuln.NewPocRecord().IndexName()
	case "vuln":
		sourceIndexName = esmodel_vuln.NewPoc().IndexName()
		destIndexName = esmodel_vuln.NewPocRecord().IndexName()
	}
	source := elastic.NewReindexSource().Index(sourceIndexName)
	if len(ids) > 0 {
		source.Query(elastic.NewIdsQuery().Ids(ids...))
	} else {
		source.Query(elastic.NewMatchAllQuery())
	}
	reindexService := es.GetEsClient().Reindex().Source(source).DestinationIndex(destIndexName).Script(script).Refresh("true")
	resp, err := reindexService.Do(context.Background())
	if err != nil {
		logs.Errorf("ExecuteReindexWithScript error, batchNo: %s, start: %s, end: %s, error: %s", batchNo, now.Format("2006-01-02 15:04:05"), time.Now().Format("2006-01-02 15:04:05"), err.Error())
		return 0, err
	}
	logs.Infof("ExecuteReindexWithScript done, batchNo: %s, total: %d, updated: %d, took: %dms", batchNo, resp.Total, resp.Updated, resp.Took)
	return resp.Total, nil
}

// CreateMergeRecord 创建合并记录
// businessType: 业务类型
// batchNo: 批次号
// mergeMode: 融合模式
// assetIds: 受影响的资产ID
// s: 融合策略
// mergeStatus: 融合状态
func CreateMergeRecord(businessType, batchNo, mergeMode string, assetIds []string, s []*strategy.Strategy, mergeStatus bool) error {
	err := *new(error)
	// 创建合并记录，错误则跳过
	switch businessType {
	case MergeFlowType_Device:
		mergeRecord := esmodel_device.NewMergeRecordsModel()
		mergeRecord.Id = UUIDStr()
		mergeRecord.MergeMode = mergeMode
		mergeRecord.BatchNo = batchNo
		mergeRecord.DeviceIds = assetIds
		mergeRecord.Strategies = s
		mergeRecord.CreatedAt = localtime.NewLocalTime(time.Now())
		mergeRecord.Status = func() int {
			if mergeStatus {
				return 1
			} else {
				return 2
			}
		}()
		_, err = mergeRecord.Insert(context.Background(), mergeRecord)
	case MergeFlowType_Asset:
		mergeRecord := esmodel_asset.NewMergeRecordsModel()
		mergeRecord.Id = UUIDStr()
		mergeRecord.MergeMode = mergeMode
		mergeRecord.BatchNo = batchNo
		mergeRecord.Ip = "0.0.0.0"
		mergeRecord.AssetIds = assetIds
		mergeRecord.Strategies = s
		mergeRecord.CreatedAt = localtime.NewLocalTime(time.Now())
		mergeRecord.Status = func() int {
			if mergeStatus {
				return 1
			} else {
				return 2
			}
		}()
		_, err = mergeRecord.Insert(context.Background(), mergeRecord)
	case MergeFlowType_Person:
		mergeRecord := esmodel_person.NewMergeRecordsModel()
		mergeRecord.Id = UUIDStr()
		mergeRecord.MergeMode = mergeMode
		mergeRecord.BatchNo = batchNo
		mergeRecord.StaffIds = assetIds
		mergeRecord.Strategies = s
		mergeRecord.CreatedAt = localtime.NewLocalTime(time.Now())
		mergeRecord.Status = func() int {
			if mergeStatus {
				return 1
			} else {
				return 2
			}
		}()
		_, err = mergeRecord.Insert(context.Background(), mergeRecord)
	case MergeFlowType_Vulnerability:
		mergeRecord := esmodel_vuln.NewMergeRecordsModel()
		mergeRecord.Id = UUIDStr()
		mergeRecord.MergeMode = mergeMode
		mergeRecord.BatchNo = batchNo
		mergeRecord.PocIds = assetIds
		mergeRecord.Strategies = s
		mergeRecord.CreatedAt = localtime.NewLocalTime(time.Now())
		mergeRecord.Status = func() int {
			if mergeStatus {
				return 1
			} else {
				return 2
			}
		}()
		_, err = mergeRecord.Insert(context.Background(), mergeRecord)
	}
	return err
}
