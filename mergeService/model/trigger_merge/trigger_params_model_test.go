package trigger_merge

import (
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestTriggerParamsForAsset_Interface(t *testing.T) {
	// 创建测试数据
	params := &TriggerParamsForAsset{
		TriggerParams: TriggerParams{
			TriggerSourceId: 1,
			TriggerNodeId:   2,
			TriggerEvent:    "test_event",
			TaskId:          "task_1",
			ChildTaskId:     "child_1",
			SubTrigger:      1,
		},
		AssetIds: []string{"asset1", "asset2"},
		IpInfos: []*IpInfo{
			{Ip: "***********", Area: 1},
			{Ip: "***********", Area: 2},
		},
		DataRangeByTask: &DataRangeByTask{
			TaskId:      "task_2",
			ChildTaskId: "child_2",
			NodeId:      3,
		},
		Fields: []string{"field1", "field2"},
	}

	// 测试接口方法实现
	t.Run("GetTriggerSourceId", func(t *testing.T) {
		assert.Equal(t, uint64(1), params.GetTriggerSourceId())
	})

	t.Run("GetTriggerNodeId", func(t *testing.T) {
		assert.Equal(t, uint64(2), params.GetTriggerNodeId())
	})

	t.Run("GetTriggerEvent", func(t *testing.T) {
		assert.Equal(t, "test_event", params.GetTriggerEvent())
	})

	t.Run("GetTaskId", func(t *testing.T) {
		assert.Equal(t, "task_1", params.GetTaskId())
	})

	t.Run("GetChildTaskId", func(t *testing.T) {
		assert.Equal(t, "child_1", params.GetChildTaskId())
	})
	t.Run("GetSubTrigger", func(t *testing.T) {
		assert.Equal(t, true, params.GetSubTrigger())
	})

	t.Run("IsPartialMerge", func(t *testing.T) {
		assert.True(t, params.IsPartialMerge())

		// 测试空参数时的返回值
		emptyParams := &TriggerParamsForAsset{
			TriggerParams: TriggerParams{
				TriggerSourceId: 1,
				TriggerNodeId:   2,
				TriggerEvent:    "test_event",
				TaskId:          "task_1",
				ChildTaskId:     "child_1",
			},
		}
		assert.False(t, emptyParams.IsPartialMerge())
	})

	t.Run("GetParamsJson", func(t *testing.T) {
		jsonStr := params.GetParamsJson()
		var result map[string]interface{}
		err := json.Unmarshal([]byte(jsonStr), &result)
		assert.NoError(t, err)

		// 验证JSON中包含正确的字段
		assert.Contains(t, result, "asset_ids")
		assert.Contains(t, result, "ip_infos")
		assert.Contains(t, result, "data_range_by_task")
		assert.Contains(t, result, "fields")

		// 验证字段值正确
		assetIds, ok := result["asset_ids"].([]interface{})
		assert.True(t, ok)
		assert.Equal(t, 2, len(assetIds))
		assert.Equal(t, "asset1", assetIds[0])
		assert.Equal(t, "asset2", assetIds[1])

		fields, ok := result["fields"].([]interface{})
		assert.True(t, ok)
		assert.Equal(t, 2, len(fields))
		assert.Equal(t, "field1", fields[0])
		assert.Equal(t, "field2", fields[1])
	})
}

func TestTriggerParamsForDevice_Interface(t *testing.T) {
	// 创建测试数据
	params := &TriggerParamsForDevice{
		TriggerParams: TriggerParams{
			TriggerSourceId: 1,
			TriggerNodeId:   2,
			TriggerEvent:    "test_event",
			TaskId:          "task_1",
			ChildTaskId:     "child_1",
			SubTrigger:      1,
		},
		DeviceIds:  []string{"device1", "device2"},
		UniqueKeys: []string{"key1", "key2"},
		DataRangeByTask: &DataRangeByTask{
			TaskId:      "task_2",
			ChildTaskId: "child_2",
			NodeId:      3,
		},
		Fields: []string{"field1", "field2"},
	}

	// 测试接口方法实现
	t.Run("GetTriggerSourceId", func(t *testing.T) {
		assert.Equal(t, uint64(1), params.GetTriggerSourceId())
	})

	t.Run("GetTriggerNodeId", func(t *testing.T) {
		assert.Equal(t, uint64(2), params.GetTriggerNodeId())
	})

	t.Run("GetTriggerEvent", func(t *testing.T) {
		assert.Equal(t, "test_event", params.GetTriggerEvent())
	})

	t.Run("GetTaskId", func(t *testing.T) {
		assert.Equal(t, "task_1", params.GetTaskId())
	})

	t.Run("GetChildTaskId", func(t *testing.T) {
		assert.Equal(t, "child_1", params.GetChildTaskId())
	})
	t.Run("GetSubTrigger", func(t *testing.T) {
		assert.Equal(t, true, params.GetSubTrigger())
	})

	t.Run("IsPartialMerge", func(t *testing.T) {
		assert.True(t, params.IsPartialMerge())

		// 测试空参数时的返回值
		emptyParams := &TriggerParamsForDevice{
			TriggerParams: TriggerParams{
				TriggerSourceId: 1,
				TriggerNodeId:   2,
				TriggerEvent:    "test_event",
				TaskId:          "task_1",
				ChildTaskId:     "child_1",
			},
		}
		assert.False(t, emptyParams.IsPartialMerge())
	})

	t.Run("GetParamsJson", func(t *testing.T) {
		jsonStr := params.GetParamsJson()
		var result map[string]interface{}
		err := json.Unmarshal([]byte(jsonStr), &result)
		assert.NoError(t, err)

		// 验证JSON中包含正确的字段
		assert.Contains(t, result, "device_ids")
		assert.Contains(t, result, "unique_keys")
		assert.Contains(t, result, "data_range_by_task")
		assert.Contains(t, result, "fields")

		// 验证字段值正确
		deviceIds, ok := result["device_ids"].([]interface{})
		assert.True(t, ok)
		assert.Equal(t, 2, len(deviceIds))
		assert.Equal(t, "device1", deviceIds[0])
		assert.Equal(t, "device2", deviceIds[1])

		uniqueKeys, ok := result["unique_keys"].([]interface{})
		assert.True(t, ok)
		assert.Equal(t, 2, len(uniqueKeys))
		assert.Equal(t, "key1", uniqueKeys[0])
		assert.Equal(t, "key2", uniqueKeys[1])
	})
}

func TestTriggerParamsForStaff_Interface(t *testing.T) {
	// 创建测试数据
	params := &TriggerParamsForStaff{
		TriggerParams: TriggerParams{
			TriggerSourceId: 1,
			TriggerNodeId:   2,
			TriggerEvent:    "test_event",
			TaskId:          "task_1",
			ChildTaskId:     "child_1",
			SubTrigger:      1,
		},
		StaffIds: []string{"staff1", "staff2"},
		DataRangeByTask: &DataRangeByTask{
			TaskId:      "task_2",
			ChildTaskId: "child_2",
			NodeId:      3,
		},
		Fields: []string{"field1", "field2"},
	}

	// 测试接口方法实现
	t.Run("GetTriggerSourceId", func(t *testing.T) {
		assert.Equal(t, uint64(1), params.GetTriggerSourceId())
	})

	t.Run("GetTriggerNodeId", func(t *testing.T) {
		assert.Equal(t, uint64(2), params.GetTriggerNodeId())
	})

	t.Run("GetTriggerEvent", func(t *testing.T) {
		assert.Equal(t, "test_event", params.GetTriggerEvent())
	})

	t.Run("GetTaskId", func(t *testing.T) {
		assert.Equal(t, "task_1", params.GetTaskId())
	})

	t.Run("GetChildTaskId", func(t *testing.T) {
		assert.Equal(t, "child_1", params.GetChildTaskId())
	})
	t.Run("GetSubTrigger", func(t *testing.T) {
		assert.Equal(t, true, params.GetSubTrigger())
	})

	t.Run("IsPartialMerge", func(t *testing.T) {
		assert.True(t, params.IsPartialMerge())

		// 测试空参数时的返回值
		emptyParams := &TriggerParamsForStaff{
			TriggerParams: TriggerParams{
				TriggerSourceId: 1,
				TriggerNodeId:   2,
				TriggerEvent:    "test_event",
				TaskId:          "task_1",
				ChildTaskId:     "child_1",
			},
		}
		assert.False(t, emptyParams.IsPartialMerge())
	})

	t.Run("GetParamsJson", func(t *testing.T) {
		jsonStr := params.GetParamsJson()
		var result map[string]interface{}
		err := json.Unmarshal([]byte(jsonStr), &result)
		assert.NoError(t, err)

		// 验证JSON中包含正确的字段
		assert.Contains(t, result, "staff_ids")
		assert.Contains(t, result, "data_range_by_task")
		assert.Contains(t, result, "fields")

		// 验证字段值正确
		staffIds, ok := result["staff_ids"].([]interface{})
		assert.True(t, ok)
		assert.Equal(t, 2, len(staffIds))
		assert.Equal(t, "staff1", staffIds[0])
		assert.Equal(t, "staff2", staffIds[1])
	})
}

func TestTriggerParamsForVuln_Interface(t *testing.T) {
	// 创建测试数据
	params := &TriggerParamsForVuln{
		TriggerParams: TriggerParams{
			TriggerSourceId: 1,
			TriggerNodeId:   2,
			TriggerEvent:    "test_event",
			TaskId:          "task_1",
			ChildTaskId:     "child_1",
			SubTrigger:      1,
		},
		VulnIds: []string{"vuln1", "vuln2"},
		DataRangeByTask: &DataRangeByTask{
			TaskId:      "task_2",
			ChildTaskId: "child_2",
			NodeId:      3,
		},
		Fields: []string{"field1", "field2"},
	}

	// 测试接口方法实现
	t.Run("GetTriggerSourceId", func(t *testing.T) {
		assert.Equal(t, uint64(1), params.GetTriggerSourceId())
	})

	t.Run("GetTriggerNodeId", func(t *testing.T) {
		assert.Equal(t, uint64(2), params.GetTriggerNodeId())
	})

	t.Run("GetTriggerEvent", func(t *testing.T) {
		assert.Equal(t, "test_event", params.GetTriggerEvent())
	})

	t.Run("GetTaskId", func(t *testing.T) {
		assert.Equal(t, "task_1", params.GetTaskId())
	})

	t.Run("GetChildTaskId", func(t *testing.T) {
		assert.Equal(t, "child_1", params.GetChildTaskId())
	})
	t.Run("GetSubTrigger", func(t *testing.T) {
		assert.Equal(t, true, params.GetSubTrigger())
	})

	t.Run("IsPartialMerge", func(t *testing.T) {
		assert.True(t, params.IsPartialMerge())

		// 测试空参数时的返回值
		emptyParams := &TriggerParamsForVuln{
			TriggerParams: TriggerParams{
				TriggerSourceId: 1,
				TriggerNodeId:   2,
				TriggerEvent:    "test_event",
				TaskId:          "task_1",
				ChildTaskId:     "child_1",
			},
		}
		assert.False(t, emptyParams.IsPartialMerge())
	})

	t.Run("GetParamsJson", func(t *testing.T) {
		jsonStr := params.GetParamsJson()
		var result map[string]interface{}
		err := json.Unmarshal([]byte(jsonStr), &result)
		assert.NoError(t, err)

		// 验证JSON中包含正确的字段
		assert.Contains(t, result, "vuln_ids")
		assert.Contains(t, result, "data_range_by_task")
		assert.Contains(t, result, "fields")

		// 验证字段值正确
		vulnIds, ok := result["vuln_ids"].([]interface{})
		assert.True(t, ok)
		assert.Equal(t, 2, len(vulnIds))
		assert.Equal(t, "vuln1", vulnIds[0])
		assert.Equal(t, "vuln2", vulnIds[1])
	})
}

func TestMergeTypeConstants(t *testing.T) {
	// 测试常量值是否正确
	assert.Equal(t, "asset", MergeTypeAsset)
	assert.Equal(t, "device", MergeTypeDevice)
	assert.Equal(t, "staff", MergeTypeStaff)
	assert.Equal(t, "vuln", MergeTypeVuln)
}

func TestITriggerParamsImplementation(t *testing.T) {
	// 测试各个结构体是否实现了ITriggerParams接口
	var _ ITriggerParams = &TriggerParamsForAsset{}
	var _ ITriggerParams = &TriggerParamsForDevice{}
	var _ ITriggerParams = &TriggerParamsForStaff{}
	var _ ITriggerParams = &TriggerParamsForVuln{}
}
