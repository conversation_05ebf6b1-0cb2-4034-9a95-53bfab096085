package trigger_merge

import (
	"context"
	"errors"
	"reflect"
	"testing"
	"time"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/alicebob/miniredis/v2"
	redis2 "github.com/go-redis/redis/v8"
	"github.com/olivere/elastic/v7"
	"github.com/stretchr/testify/assert"

	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/initialize/es"
	"fobrain/initialize/mysql"
	merge_helper "fobrain/mergeService/model/helper"
	logs "fobrain/mergeService/utils/log"

	"fobrain/models/mysql/merge"
	"fobrain/models/mysql/strategy"
)

func TestGetScriptForRefreshAsset(t *testing.T) {
	tests := []struct {
		name    string
		field   string
		want    *elastic.Script
		wantErr bool
	}{
		{
			name:    "空字段测试",
			field:   "",
			want:    nil,
			wantErr: true,
		},
		{
			name:    "不支持的字段测试",
			field:   "unsupported",
			want:    nil,
			wantErr: true,
		},
		{
			name:    "business字段测试",
			field:   "business",
			want:    elastic.NewScriptInline(Script_Refresh_Asset_Business).Lang("painless").Params(map[string]interface{}{"SourceData": map[uint64][]string{1: {"source1"}}}),
			wantErr: false,
		},
	}

	// Mock strategy.NewStrategyModel()
	getLatestByFieldMock := gomonkey.ApplyMethodReturn(strategy.NewStrategyModel(), "GetLatestByField", &strategy.Strategy{
		SourcePriority: map[string]uint64{"source1": 1},
	}, nil)
	defer getLatestByFieldMock.Reset()

	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyFuncReturn(merge_helper.GetMergeRules, []*strategy.Strategy{
		{
			FieldName:      "business",
			SourcePriority: map[string]uint64{"source1": 1},
		},
	}, nil).Reset()

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := GetScriptForRefreshAsset(tt.field)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetScriptForRefreshAsset() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !tt.wantErr && !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetScriptForRefreshAsset() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestFindLastMergeRecordAndTrigger(t *testing.T) {
	// Setup mock redis
	s, err := miniredis.Run()
	if err != nil {
		t.Fatal(err)
	}
	defer s.Close()

	client := redis2.NewClient(&redis2.Options{Addr: s.Addr()})
	testcommon.SetRedisClient(client)

	// 测试获取最后一个等待状态的融合任务成功
	mergeRecord := &merge.MergeRecords{
		BaseModel: mysql.BaseModel{
			Id: 1,
		},
		MergeType:        merge.MergeRecordsTypeAsset,
		TriggerSourceId:  100,
		TriggerNodeId:    200,
		TriggerEvent:     "event",
		EventTaskId:      "task-123",
		EventChildTaskId: "child-task-123",
	}

	patch := gomonkey.ApplyMethodReturn(merge.NewMergeRecordsModel(), "GetLastMergeRecord", mergeRecord, nil)
	defer patch.Reset()

	patch = gomonkey.ApplyMethodReturn(merge.NewMergeRecordsModel(), "RunAndClearOther", nil)
	defer patch.Reset()

	patch = gomonkey.ApplyFuncReturn(executeMergeTask, nil)
	defer patch.Reset()

	err = findLastMergeRecordAndTrigger("asset")
	assert.NoError(t, err)
	patch.Reset()
}

func TestCleanUnNormalEndTask(t *testing.T) {
	// Setup mock redis
	s, err := miniredis.Run()
	if err != nil {
		t.Fatal(err)
	}
	defer s.Close()

	client := redis2.NewClient(&redis2.Options{Addr: s.Addr()})
	testcommon.SetRedisClient(client)

	// 测试强制取消执行中的任务成功
	patch := gomonkey.ApplyMethodReturn(merge.NewMergeRecordsModel(), "UpdateByQuery", nil)
	defer patch.Reset()
	err = CleanUnNormalEndTask()
	assert.NoError(t, err)
	patch.Reset()

	// 测试强制取消执行中的任务失败
	patch = gomonkey.ApplyMethodReturn(merge.NewMergeRecordsModel(), "UpdateByQuery", errors.New("update error"))
	defer patch.Reset()
	err = CleanUnNormalEndTask()
	assert.Error(t, err)
	assert.Equal(t, "强制取消执行中的任务失败,err: update error", err.Error())
	patch.Reset()
}

func TestGetScriptForAppendData(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	mockServer.Register("/_scripts/script_append_data_business_id", &elastic.PutScriptResponse{
		AcknowledgedResponse: elastic.AcknowledgedResponse{
			Acknowledged:       true,
			ShardsAcknowledged: true,
		},
	})
	//testcommon.SetTestEnv(false)

	_, err := getScriptAppendDataBusinessId()
	//res, err := es.GetEsClient().GetScript().Id(ScriptAppendDataBusinessId).Do(context.Background())
	//if nil != err {
	//	fmt.Println(err)
	//} else {
	//	b, _ := res.Script.MarshalJSON()
	//	fmt.Println(string(b))
	//}
	//fmt.Println(*res, err)
	assert.NoError(t, err)
}

func TestUpdateRefreshInterval_Normal(t *testing.T) {
	// 设置日志
	logPatch := gomonkey.ApplyFuncReturn(logs.GetLogger, logs.GetLogger("test"))
	defer logPatch.Reset()

	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	// 模拟ES返回成功响应
	mockServer.Register("assets/_settings", &elastic.IndicesPutSettingsResponse{
		Acknowledged: true,
	})

	err := es.UpdateRefreshInterval(context.Background(), "assets", "3600s")
	assert.Nil(t, err)
}

func TestUpdateRefreshInterval_NotAcknowledged(t *testing.T) {
	// 设置日志
	logPatch := gomonkey.ApplyFuncReturn(logs.GetLogger, logs.GetLogger("test"))
	defer logPatch.Reset()

	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	// 模拟ES返回未确认响应
	mockServer.Register("assets/_settings", &elastic.IndicesPutSettingsResponse{
		Acknowledged: false,
	})

	err := es.UpdateRefreshInterval(context.Background(), "assets", "3600s")
	assert.NotNil(t, err)
	assert.Contains(t, err.Error(), "put settings ack failed")
}

func TestUpdateRefreshInterval_Error(t *testing.T) {
	// 设置日志
	logPatch := gomonkey.ApplyFuncReturn(logs.GetLogger, logs.GetLogger("test"))
	defer logPatch.Reset()

	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	// 模拟ES返回错误
	mockServer.Register("assets/_settings", &elastic.Error{
		Status: 500,
		Details: &elastic.ErrorDetails{
			Reason: "internal server error",
		},
	})

	err := es.UpdateRefreshInterval(context.Background(), "assets", "3600s")
	assert.NotNil(t, err)
	assert.Contains(t, err.Error(), "UpdateRefreshInterval")
}

func TestUpdateRefreshInterval_NilResponse(t *testing.T) {
	// 设置日志
	logPatch := gomonkey.ApplyFuncReturn(logs.GetLogger, logs.GetLogger("test"))
	defer logPatch.Reset()

	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	// 模拟ES返回nil响应
	mockServer.Register("assets/_settings", nil)

	err := es.UpdateRefreshInterval(context.Background(), "assets", "3600s")
	assert.NotNil(t, err)
	assert.Contains(t, err.Error(), "UpdateRefreshInterval: put settings ack failed")
}

func TestUpdateRefreshInterval_Asset(t *testing.T) {
	// 设置日志
	logPatch := gomonkey.ApplyFuncReturn(logs.GetLogger, logs.GetLogger("test"))
	defer logPatch.Reset()

	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	// 模拟ES返回成功响应
	mockServer.Register("assets/_settings", &elastic.IndicesPutSettingsResponse{
		Acknowledged: true,
	})
	mockServer.Register("asset_record/_settings", &elastic.IndicesPutSettingsResponse{
		Acknowledged: true,
	})
	mockServer.Register("asset_merge_record/_settings", &elastic.IndicesPutSettingsResponse{
		Acknowledged: true,
	})

	// 调用测试方法
	UpdateRefreshInterval("asset", "3600s")

	// 验证日志输出
	// 由于函数没有返回值，我们只能通过日志来验证
	// 如果ES调用失败，会有警告日志
}

func TestUpdateRefreshInterval_Device(t *testing.T) {
	// 设置日志
	logPatch := gomonkey.ApplyFuncReturn(logs.GetLogger, logs.GetLogger("test"))
	defer logPatch.Reset()

	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	// 模拟ES返回成功响应
	mockServer.Register("device/_settings", &elastic.IndicesPutSettingsResponse{
		Acknowledged: true,
	})
	mockServer.Register("device_record/_settings", &elastic.IndicesPutSettingsResponse{
		Acknowledged: true,
	})
	mockServer.Register("device_merge_record/_settings", &elastic.IndicesPutSettingsResponse{
		Acknowledged: true,
	})

	// 调用测试方法
	UpdateRefreshInterval("device", "3600s")

	// 验证日志输出
	// 由于函数没有返回值，我们只能通过日志来验证
	// 如果ES调用失败，会有警告日志
}

func TestUpdateRefreshInterval_InvalidType(t *testing.T) {
	// 设置日志
	logPatch := gomonkey.ApplyFuncReturn(logs.GetLogger, logs.GetLogger("test"))
	defer logPatch.Reset()

	// 调用测试方法
	UpdateRefreshInterval("invalid_type", "3600s")

	// 验证日志输出
	// 由于函数没有返回值，我们只能通过日志来验证
	// 对于无效类型，函数应该不会调用ES
}
