package trigger_merge

import (
	"context"
	"errors"
	"fmt"
	"testing"
	"time"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/alicebob/miniredis/v2"
	"github.com/stretchr/testify/assert"

	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/models/mysql/data_sync_child_task"
	"fobrain/models/mysql/merge"
	"fobrain/models/mysql/proactive_task_node_relations"
	distributedlock "fobrain/pkg/distributedLock"

	redis2 "github.com/go-redis/redis/v8"
)

func TestGetMergeRecordTypeByTaskType(t *testing.T) {
	tests := []struct {
		taskType string
		expected string
	}{
		{"asset", merge.MergeRecordsTypeAsset},
		{"person", merge.MergeRecordsTypeStaff},
		{"staff", merge.MergeRecordsTypeStaff},
		{"people", merge.MergeRecordsTypeStaff},
		{"vuln", merge.MergeRecordsTypeVuln},
		{"threat", merge.MergeRecordsTypeVuln},
		{"poc", merge.MergeRecordsTypeVuln},
		{"device", merge.MergeRecordsTypeDevice},
		{"unknown", ""},
	}

	for _, test := range tests {
		result := getMergeRecordTypeByTaskType(test.taskType)
		assert.Equal(t, test.expected, result, "getMergeRecordTypeByTaskType(%q) = %v; want %v", test.taskType, result, test.expected)
	}
}

func TestGetTaskTypeByMergeRecordType(t *testing.T) {
	tests := []struct {
		mergeRecordType string
		expected        string
	}{
		{merge.MergeRecordsTypeAsset, "asset"},
		{merge.MergeRecordsTypeStaff, "person"},
		{merge.MergeRecordsTypeVuln, "vuln"},
		{merge.MergeRecordsTypeDevice, "device"},
		{"unknown", ""},
	}

	for _, test := range tests {
		result := getTaskTypeByMergeRecordType(test.mergeRecordType)
		assert.Equal(t, test.expected, result, "getTaskTypeByMergeRecordType(%q) = %v; want %v", test.mergeRecordType, result, test.expected)
	}
}

func TestUnlockAndTrigger(t *testing.T) {
	// Setup mock redis
	s, err := miniredis.Run()
	if err != nil {
		t.Fatal(err)
	}
	defer s.Close()

	client := redis2.NewClient(&redis2.Options{Addr: s.Addr()})
	testcommon.SetRedisClient(client)

	dataType := "asset"
	statusKey := fmt.Sprintf("merge_status:%s", dataType)
	// 把锁写入redis
	client.Set(context.Background(), statusKey, "1", 0)

	patch := gomonkey.ApplyMethodReturn(merge.NewMergeRecordsModel(), "UpdateByMap", nil)
	defer patch.Reset()

	patch = gomonkey.ApplyMethodReturn(merge.NewMergeRecordsModel(), "GetLastMergeRecord", nil, nil)
	defer patch.Reset()

	// 解锁
	ok := distributedlock.Unlock(statusKey, "1")
	assert.True(t, ok)

	// 检查redis中的锁是否被删除
	_, err = client.Get(context.Background(), statusKey).Result()
	assert.Equal(t, redis2.Nil, err)
}

func TestCheckParamsForAsset(t *testing.T) {
	testData := []struct {
		name       string
		taskParams *TriggerParamsForAsset
		expected   error
	}{
		{"全量融合参数，正常情况", &TriggerParamsForAsset{TriggerParams: TriggerParams{TaskId: "1", TriggerEvent: "sync"}, AssetIds: []string{}, IpInfos: []*IpInfo{}, DataRangeByTask: nil}, nil},
		{"触发事件为空", &TriggerParamsForAsset{TriggerParams: TriggerParams{TaskId: "1", TriggerEvent: ""}}, fmt.Errorf("触发事件不能为空")},
		{"任务ID为空", &TriggerParamsForAsset{TriggerParams: TriggerParams{TaskId: "", TriggerEvent: "sync"}}, fmt.Errorf("任务ID不能为空")},
		{"任务ID和触发事件都为空", &TriggerParamsForAsset{TriggerParams: TriggerParams{TaskId: "", TriggerEvent: ""}}, fmt.Errorf("任务ID不能为空")},
		{"任务ID和触发事件不为空，其他参数忽略", &TriggerParamsForAsset{TriggerParams: TriggerParams{TaskId: "1", TriggerEvent: "sync"}}, nil},
	}

	for _, test := range testData {
		t.Run(test.name, func(t *testing.T) {
			err := checkParamsForAsset(test.taskParams)
			assert.Equal(t, test.expected, err)
		})
	}
}

func TestCheckParamsForDevice(t *testing.T) {
	testData := []struct {
		name       string
		taskParams *TriggerParamsForDevice
		expected   error
	}{
		{"全量融合参数，正常情况", &TriggerParamsForDevice{TriggerParams: TriggerParams{TaskId: "1", TriggerEvent: "sync"}, DeviceIds: []string{}}, nil},
		{"触发事件为空", &TriggerParamsForDevice{TriggerParams: TriggerParams{TaskId: "1", TriggerEvent: ""}}, fmt.Errorf("触发事件不能为空")},
		{"任务ID为空", &TriggerParamsForDevice{TriggerParams: TriggerParams{TaskId: "", TriggerEvent: "sync"}}, fmt.Errorf("任务ID不能为空")},
		{"任务ID和触发事件都为空", &TriggerParamsForDevice{TriggerParams: TriggerParams{TaskId: "", TriggerEvent: ""}}, fmt.Errorf("任务ID不能为空")},
		{"任务ID和触发事件不为空，其他参数忽略", &TriggerParamsForDevice{TriggerParams: TriggerParams{TaskId: "1", TriggerEvent: "sync"}}, nil},
	}

	for _, test := range testData {
		t.Run(test.name, func(t *testing.T) {
			err := checkParamsForDevice(test.taskParams)
			assert.Equal(t, test.expected, err)
		})
	}
}

func TestCheckParamsForStaff(t *testing.T) {
	testData := []struct {
		name       string
		taskParams *TriggerParamsForStaff
		expected   error
	}{
		{"全量融合参数，正常情况", &TriggerParamsForStaff{TriggerParams: TriggerParams{TaskId: "1", TriggerEvent: "sync"}, StaffIds: []string{}}, nil},
		{"触发事件为空", &TriggerParamsForStaff{TriggerParams: TriggerParams{TaskId: "1", TriggerEvent: ""}}, fmt.Errorf("触发事件不能为空")},
		{"任务ID为空", &TriggerParamsForStaff{TriggerParams: TriggerParams{TaskId: "", TriggerEvent: "sync"}}, fmt.Errorf("任务ID不能为空")},
		{"任务ID和触发事件都为空", &TriggerParamsForStaff{TriggerParams: TriggerParams{TaskId: "", TriggerEvent: ""}}, fmt.Errorf("任务ID不能为空")},
		{"任务ID和触发事件不为空，其他参数忽略", &TriggerParamsForStaff{TriggerParams: TriggerParams{TaskId: "1", TriggerEvent: "sync"}}, nil},
	}

	for _, test := range testData {
		t.Run(test.name, func(t *testing.T) {
			err := checkParamsForStaff(test.taskParams)
			assert.Equal(t, test.expected, err)
		})
	}
}

func TestCheckParamsForVuln(t *testing.T) {
	testData := []struct {
		name       string
		taskParams *TriggerParamsForVuln
		expected   error
	}{
		{"全量融合参数，正常情况", &TriggerParamsForVuln{TriggerParams: TriggerParams{TaskId: "1", TriggerEvent: "sync"}, VulnIds: []string{}}, nil},
		{"触发事件为空", &TriggerParamsForVuln{TriggerParams: TriggerParams{TaskId: "1", TriggerEvent: ""}}, fmt.Errorf("触发事件不能为空")},
		{"任务ID为空", &TriggerParamsForVuln{TriggerParams: TriggerParams{TaskId: "", TriggerEvent: "sync"}}, fmt.Errorf("任务ID不能为空")},
		{"任务ID和触发事件都为空", &TriggerParamsForVuln{TriggerParams: TriggerParams{TaskId: "", TriggerEvent: ""}}, fmt.Errorf("任务ID不能为空")},
		{"任务ID和触发事件不为空，其他参数忽略", &TriggerParamsForVuln{TriggerParams: TriggerParams{TaskId: "1", TriggerEvent: "sync"}}, nil},
	}

	for _, test := range testData {
		t.Run(test.name, func(t *testing.T) {
			err := checkParamsForVuln(test.taskParams)
			assert.Equal(t, test.expected, err)
		})
	}
}

func TestGetNodeIdAndTaskId(t *testing.T) {
	patch := gomonkey.ApplyMethodReturn(data_sync_child_task.NewDataSyncChildTaskModel(), "GetAllNodeLastSuccessTaskIDs", []data_sync_child_task.NodeLastSuccessTaskIDs{{NodeId: uint64(1), TaskId: uint64(1), TaskAssetId: uint64(1), TaskStaffId: uint64(1)}}, nil)
	defer patch.Reset()

	// 测试资产数据
	allNodeAndTaskId1, err := getNodeIdAndSyncTaskId("asset")
	assert.NoError(t, err)
	assert.Equal(t, map[uint64]uint64{1: 1}, allNodeAndTaskId1)
	patch.Reset()

	// 测试人员数据
	patch = gomonkey.ApplyMethodReturn(data_sync_child_task.NewDataSyncChildTaskModel(), "GetAllNodeLastSuccessTaskIDs", []data_sync_child_task.NodeLastSuccessTaskIDs{{NodeId: uint64(1), TaskId: uint64(1), TaskAssetId: uint64(0), TaskStaffId: uint64(1)}}, nil)
	defer patch.Reset()
	allNodeAndTaskId2, err := getNodeIdAndSyncTaskId("person")
	assert.NoError(t, err)
	assert.Equal(t, map[uint64]uint64{1: 1}, allNodeAndTaskId2)
	patch.Reset()

	// 测试错误dataType
	patch = gomonkey.ApplyMethodReturn(data_sync_child_task.NewDataSyncChildTaskModel(), "GetAllNodeLastSuccessTaskIDs", []data_sync_child_task.NodeLastSuccessTaskIDs{{NodeId: uint64(1), TaskId: uint64(1), TaskAssetId: uint64(1), TaskStaffId: uint64(1)}}, nil)
	defer patch.Reset()
	allNodeAndTaskId4, err := getNodeIdAndSyncTaskId("unsupported")
	assert.NoError(t, err)
	assert.Equal(t, 0, len(allNodeAndTaskId4))
	patch.Reset()

	// 休眠1秒，确保之前的mock失效
	time.Sleep(1 * time.Second)
	// 测试获取节点最新任务ID失败
	patch = gomonkey.ApplyMethodReturn(data_sync_child_task.NewDataSyncChildTaskModel(), "GetAllNodeLastSuccessTaskIDs", nil, errors.New("test error"))
	defer patch.Reset()
	allNodeAndTaskId3, err := getNodeIdAndSyncTaskId("asset")
	assert.Error(t, err)
	assert.Nil(t, allNodeAndTaskId3)
	patch.Reset()

}

func TestGetNodeIdAndProactiveTaskId(t *testing.T) {
	patch := gomonkey.ApplyMethodReturn(proactive_task_node_relations.NewProactiveTaskNodeRelations(), "GetProactiveTaskIds", []*proactive_task_node_relations.NodeLastProactiveSuccessTaskIds{{NodeId: 1, TaskId: 1, AssetChildTaskId: uint64(1)}}, nil)
	defer patch.Reset()

	// 测试资产数据
	allNodeAndTaskId1, err := getNodeIdAndProactiveTaskId("asset")
	assert.NoError(t, err)
	assert.Equal(t, map[uint64]uint64{1: 1}, allNodeAndTaskId1)
	patch.Reset()

	// 测试漏洞数据
	patch = gomonkey.ApplyMethodReturn(proactive_task_node_relations.NewProactiveTaskNodeRelations(), "GetProactiveTaskIds", []*proactive_task_node_relations.NodeLastProactiveSuccessTaskIds{{NodeId: uint64(1), TaskId: uint64(1), AssetChildTaskId: uint64(0), ThreatChildTaskId: uint64(1)}}, nil)
	defer patch.Reset()
	allNodeAndTaskId2, err := getNodeIdAndProactiveTaskId("vuln")
	assert.NoError(t, err)
	assert.Equal(t, map[uint64]uint64{1: 1}, allNodeAndTaskId2)
	patch.Reset()

	// 测试错误dataType
	patch = gomonkey.ApplyMethodReturn(proactive_task_node_relations.NewProactiveTaskNodeRelations(), "GetProactiveTaskIds", []*proactive_task_node_relations.NodeLastProactiveSuccessTaskIds{{NodeId: uint64(1), TaskId: uint64(1), AssetChildTaskId: uint64(1), ThreatChildTaskId: uint64(1)}}, nil)
	defer patch.Reset()
	allNodeAndTaskId4, err := getNodeIdAndProactiveTaskId("unsupported")
	assert.NoError(t, err)
	assert.Equal(t, 0, len(allNodeAndTaskId4))
	patch.Reset()

	// 休眠1秒，确保之前的mock失效
	time.Sleep(1 * time.Second)
	// 测试获取节点最新任务ID失败
	patch = gomonkey.ApplyMethodReturn(proactive_task_node_relations.NewProactiveTaskNodeRelations(), "GetProactiveTaskIds", nil, errors.New("test error"))
	defer patch.Reset()
	allNodeAndTaskId3, err := getNodeIdAndProactiveTaskId("asset")
	assert.Error(t, err)
	assert.Nil(t, allNodeAndTaskId3)
	patch.Reset()
}

// func TestGetAllNodeLastSuccessTaskIDs(t *testing.T) {
// 	testcommon.SetTestEnv(false)
// 	// 全量融合  这部分需要根据局部融合做优化
// 	allNodeAndSyncTaskId, err := getNodeIdAndSyncTaskId("asset")
// 	assert.NoError(t, err)
// 	// 获取所有数据源的最新扫描任务id
// 	allNodeAndProactiveTaskId, err := getNodeIdAndProactiveTaskId("asset")
// 	assert.NoError(t, err)

// 	// 合并所有数据源的最新任务id,包括扫描任务和同步任务
// 	allNodeAndTaskId := make(map[uint64][]uint64)
// 	for nodeId, taskId := range allNodeAndProactiveTaskId {
// 		allNodeAndTaskId[nodeId] = append(allNodeAndTaskId[nodeId], taskId)
// 	}
// 	for nodeId, taskId := range allNodeAndSyncTaskId {
// 		allNodeAndTaskId[nodeId] = append(allNodeAndTaskId[nodeId], taskId)
// 	}
// 	for nodeId, tidList := range allNodeAndTaskId {
// 		page := 1
// 		perPage := 500
// 		// 生成query
// 		query := elastic.NewBoolQuery()
// 		query.Must(elastic.NewTermQuery("node", nodeId))
// 		// 根据child_task_id过滤
// 		tids := make([]string, 0)
// 		for _, tid := range tidList {
// 			tids = append(tids, fmt.Sprintf("%d", tid))
// 		}
// 		query.Must(elastic.NewTermsQueryFromStrings("child_task_id", tids...))
// 		for {
// 			count, paList, err := es.List[assets.ProcessAssets](page, perPage, query, []elastic.Sorter{elastic.NewFieldSort("created_at").Desc()}, "id", "ip", "area")
// 			if err != nil {
// 				logger.Errorf("下发融合任务时,获取资产数据失败: %v", err)
// 				return
// 			}
// 			if count == 0 || len(paList) == 0 {
// 				break
// 			}

// 			if len(paList) < perPage {
// 				break
// 			}
// 			page++
// 		}
// 	}
// }
