package trigger_merge

import (
	"context"
	"errors"
	"fmt"
	"fobrain/fobrain/common/localtime"
	"fobrain/initialize/mysql"
	"fobrain/initialize/redis"
	"fobrain/models/mysql/data_sync_child_task"
	"fobrain/models/mysql/merge"
	"fobrain/models/mysql/proactive_task_node_relations"
	"fobrain/pkg/cfg"
	distributedlock "fobrain/pkg/distributedLock"
	"strings"

	"gorm.io/gorm"
)

// 获取锁并触发队列
// @return ok 是否获取到锁
func lock(dataType string, taskId string) bool {
	statusKey := fmt.Sprintf("merge_status:%s", dataType)
	statusValue := taskId
	// 24小时过期，避免锁死
	return distributedlock.Lock(statusKey, statusValue, 86400)
}

// 解锁并触发队列中最后一个任务
// @param mergeRecordId 融合任务ID
// @param dataType 数据类型
// @param taskId 任务ID
// @param childTaskId 子任务ID
// @param errorMsg 错误信息,如果为空，则表示任务完成
func UnlockAndTrigger(mergeRecordId uint64, dataType string, taskId string, childTaskId string, errorMsg string, fail bool) error {
	return unlockAndTrigger(mergeRecordId, dataType, taskId, childTaskId, "", errorMsg, fail)
}

func getMergeRecordTypeByTaskType(taskType string) string {
	if taskType == "asset" {
		return merge.MergeRecordsTypeAsset
	}
	if taskType == "device" {
		return merge.MergeRecordsTypeDevice
	}
	if taskType == "person" || taskType == "staff" || taskType == "people" {
		return merge.MergeRecordsTypeStaff
	}
	if taskType == "vuln" || taskType == "threat" || taskType == "poc" {
		return merge.MergeRecordsTypeVuln
	}
	return ""
}

func getTaskTypeByMergeRecordType(mergeRecordType string) string {
	if mergeRecordType == merge.MergeRecordsTypeAsset {
		return "asset"
	} else if mergeRecordType == merge.MergeRecordsTypeDevice {
		return "device"
	} else if mergeRecordType == merge.MergeRecordsTypeStaff {
		return "person"
	} else if mergeRecordType == merge.MergeRecordsTypeVuln {
		return "vuln"
	}
	return ""
}

func checkParamsForAsset(taskParams *TriggerParamsForAsset) error {
	if taskParams == nil {
		return fmt.Errorf("任务参数不能为空")
	}
	if taskParams.TaskId == "" {
		return fmt.Errorf("任务ID不能为空")
	}
	if strings.Contains(taskParams.TaskId, "-") {
		return fmt.Errorf("任务ID格式错误,任务ID不能包含'-'")
	}

	if taskParams.TriggerEvent == "" {
		return fmt.Errorf("触发事件不能为空")
	}
	return nil
}

func checkParamsForDevice(taskParams *TriggerParamsForDevice) error {
	if taskParams == nil {
		return fmt.Errorf("任务参数不能为空")
	}
	if taskParams.TaskId == "" {
		return fmt.Errorf("任务ID不能为空")
	}
	if strings.Contains(taskParams.TaskId, "-") {
		return fmt.Errorf("任务ID格式错误,任务ID不能包含'-'")
	}

	if taskParams.TriggerEvent == "" {
		return fmt.Errorf("触发事件不能为空")
	}
	return nil
}

func checkParamsForStaff(taskParams *TriggerParamsForStaff) error {
	if taskParams == nil {
		return fmt.Errorf("任务参数不能为空")
	}
	if taskParams.TaskId == "" {
		return fmt.Errorf("任务ID不能为空")
	}
	if strings.Contains(taskParams.TaskId, "-") {
		return fmt.Errorf("任务ID格式错误,任务ID不能包含'-'")
	}

	if taskParams.TriggerEvent == "" {
		return fmt.Errorf("触发事件不能为空")
	}
	return nil
}

func checkParamsForVuln(taskParams *TriggerParamsForVuln) error {
	if taskParams == nil {
		return fmt.Errorf("任务参数不能为空")
	}
	if taskParams.TaskId == "" {
		return fmt.Errorf("任务ID不能为空")
	}
	if strings.Contains(taskParams.TaskId, "-") {
		return fmt.Errorf("任务ID格式错误,任务ID不能包含'-'")
	}

	if taskParams.TriggerEvent == "" {
		return fmt.Errorf("触发事件不能为空")
	}
	return nil
}

// 加入队列
// @return mergeRecordId 融合任务ID
// @return err 错误
func enqueue(mergeRecord *merge.MergeRecords) (uint64, error) {
	mergeRecord.Priority = 1
	mergeRecord.TriggerTime = localtime.Now()
	if mergeRecord.TaskDesc == "" {
		mergeRecord.TaskDesc = fmt.Sprintf("因为 %s 触发 %s 数据融合", mergeRecord.TriggerEvent, mergeRecord.MergeType)
	}
	createErr := merge.NewMergeRecordsModel().Create(mergeRecord)
	if createErr != nil {
		return 0, fmt.Errorf("加入队列失败: %v", createErr)
	}
	return mergeRecord.Id, nil
}

func unlockAndTrigger(mergeRecordId uint64, dataType string, taskId string, childTaskId string, triggerParams string, errorMsg string, fail bool) error {
	logger.Warnf("解锁并触发队列中最后一个任务,融合任务ID：%d, 任务ID：%v, 任务类型:%s, 参数:%+v, errorMsg:%s", mergeRecordId, taskId, dataType, triggerParams, errorMsg)
	if dataType == "asset" || dataType == "device" || dataType == "person" || dataType == "staff" || dataType == "vuln" || dataType == "vulnerability" {
		if dataType == "vulnerability" {
			dataType = "vuln"
		}
		if dataType == "person" {
			dataType = "staff"
		}
		logger.Infof("解锁并触发队列中最后一个任务,数据类型：%v", dataType)

		// 任务完成后删除状态
		statusKey := fmt.Sprintf("merge_status:%s", dataType)
		statusValue := fmt.Sprintf("%s-%s", taskId, childTaskId)
		if !distributedlock.Unlock(statusKey, statusValue) {
			return fmt.Errorf("解锁失败,任务ID：%v", taskId)
		}
	}

	// 更新当前任务状态
	if fail {
		updateMap := map[string]interface{}{
			"task_status": merge.MergeRecordsStatusFailed,
			"error_msg":   errorMsg,
		}
		if triggerParams != "" {
			updateMap["params"] = triggerParams
		}
		if err := merge.NewMergeRecordsModel().UpdateByMap(mergeRecordId, updateMap); err != nil {
			return fmt.Errorf("更新任务状态失败,任务ID：%v,err: %v", mergeRecordId, err)
		}
	} else {
		updateMap := map[string]interface{}{
			"task_status": merge.MergeRecordsStatusComplete,
			"error_msg":   errorMsg,
			"end_time":    localtime.Now(),
		}
		if triggerParams != "" {
			updateMap["params"] = triggerParams
		}
		if err := merge.NewMergeRecordsModel().UpdateByMap(mergeRecordId, updateMap); err != nil {
			return fmt.Errorf("更新任务状态失败,任务ID：%v,err: %v", mergeRecordId, err)
		}
	}
	go func() {
		// 触发后续任务
		err := findLastMergeRecordAndTrigger(dataType)
		if err != nil {
			logger.Errorf("触发后续任务失败,err: %v", err)
		}
	}()

	return nil
}

func findLastMergeRecordAndTrigger(dataType string) error {
	mergeRecordType := getMergeRecordTypeByTaskType(dataType)
	// 优先获取最后一个等待中的且全量的任务，注意这里需要获取id最大的，因为要取消所有小于id的任务
	mergeRecord, err := merge.NewMergeRecordsModel().GetLastMergeRecord(mergeRecordType, merge.MergeRecordsStatusWaiting, merge.MergeRecordsDataRangeAll, "id desc")
	if err != nil && err != gorm.ErrRecordNotFound {
		return fmt.Errorf("获取最后一个等待状态的融合任务(全量)失败,err: %v", err)
	}
	if mergeRecord == nil || mergeRecord.Id == 0 {
		logger.Infof("没有找到未触发的融合任务(全量),尝试获取指定范围的任务")
		// 获取最后一个等待状态的部分融合任务,注意这里需要获取id最小的,因为要按顺序依次触发
		mergeRecord, err = merge.NewMergeRecordsModel().GetLastMergeRecord(mergeRecordType, merge.MergeRecordsStatusWaiting, merge.MergeRecordsDataRangeSpec, "id asc")
		if err != nil && err != gorm.ErrRecordNotFound {
			return fmt.Errorf("获取最后一个等待状态的融合任务(指定)失败,err: %v", err)
		}
	}
	if mergeRecord == nil || mergeRecord.Id == 0 {
		logger.Warnf("没有找到未触发的融合任务: %s", dataType)
		return nil
	}

	// 如果存在未触发的全量融合任务,则清空队列
	if mergeRecord.DataRange == merge.MergeRecordsDataRangeAll {
		err := merge.NewMergeRecordsModel().RunAndClearOther(mergeRecord.Id, mergeRecord.MergeType, fmt.Sprintf("后续任务触发,任务ID：%v", mergeRecord.Id))
		if err != nil {
			return fmt.Errorf("清空队列失败,err: %v", err)
		}
	}

	// 获取锁
	lockVal := fmt.Sprintf("%v-%v", mergeRecord.EventTaskId, mergeRecord.EventChildTaskId)
	ok := lock(dataType, lockVal)
	if !ok {
		msg := fmt.Sprintf("触发%v融合任务失败,存在正在进行的融合任务,任务ID：%v,子任务ID：%v", dataType, mergeRecord.EventTaskId, mergeRecord.EventChildTaskId)
		logger.Warnf(msg)
		return errors.New(msg)
	}

	err = executeMergeTask(
		dataType,
		mergeRecord.Id,
		mergeRecord.TriggerSourceId,
		mergeRecord.TriggerNodeId,
		mergeRecord.TriggerEvent,
		mergeRecord.EventTaskId,
		mergeRecord.EventChildTaskId,
	)
	if err != nil {
		return fmt.Errorf("触发数据融合任务失败,err: %v", err)
	}

	return nil
}

// 清理未正常结束的任务
func CleanUnNormalEndTask() error {
	// 强制删除融合锁
	redisClient := redis.GetRedisClient()
	redisClient.Del(context.Background(), "merge_status:asset")
	redisClient.Del(context.Background(), "merge_status:person")
	redisClient.Del(context.Background(), "merge_status:vuln")
	redisClient.Del(context.Background(), "merge_status:device")
	redisClient.Del(context.Background(), cfg.LoadQueue().AssetMergeQueue)
	redisClient.Del(context.Background(), cfg.LoadQueue().VulnMergeQueue)
	redisClient.Del(context.Background(), cfg.LoadQueue().PersonMergeQueue)
	redisClient.Del(context.Background(), cfg.LoadQueue().DeviceMergeQueue)

	// 所有执行中的任务强制取消
	err := merge.NewMergeRecordsModel().UpdateByQuery(
		[]mysql.HandleFunc{
			mysql.WithValuesIn[string]("task_status", []string{merge.MergeRecordsStatusRunning, merge.MergeRecordsStatusPrepare}),
		},
		map[string]interface{}{
			"task_status":   merge.MergeRecordsStatusCancel,
			"cancel_reason": "强制取消",
		},
	)
	if err != nil {
		return fmt.Errorf("强制取消执行中的任务失败,err: %v", err)
	}
	return nil
}

func triggerFailHandler(mergeRecordId uint64, dataType string, taskId string, childTaskId string, triggerParams string, errMsg string, fail bool) error {
	// 删除并发锁
	err := unlockAndTrigger(mergeRecordId, dataType, taskId, childTaskId, triggerParams, errMsg, fail)
	if err != nil {
		msg := fmt.Sprintf("触发数据融合任务失败,任务ID：%v, err: %v", mergeRecordId, errMsg)
		if err.Error() != "" {
			msg = fmt.Sprintf("%v(%v)", msg, err.Error())
		}
		return errors.New(msg)
	}
	UpdateRefreshInterval(dataType, "1s")

	// 记录错误日志
	logger.Errorf("触发数据融合任务失败,任务ID：%v, err: %v", mergeRecordId, errMsg)
	// 返回错误
	return fmt.Errorf("触发数据融合任务失败,任务ID：%v, err: %v", mergeRecordId, errMsg)
}

// 获取所有数据源的最新扫描任务ID
func getNodeIdAndProactiveTaskId(dataType string) (map[uint64]uint64, error) {
	// 获取所有数据源的最新taskid
	nodeTaskInfo, err := proactive_task_node_relations.NewProactiveTaskNodeRelations().GetProactiveTaskIds()
	if err != nil {
		return nil, fmt.Errorf("获取节点最新扫描任务ID失败,err: %v", err)
	}
	// key:nodeId,value:taskId
	allNodeAndTaskId := make(map[uint64]uint64)
	// 解析所有数据源的最新taskid
	for _, item := range nodeTaskInfo {
		if (dataType == "asset" || dataType == "device") && item.AssetChildTaskId > 0 {
			allNodeAndTaskId[item.NodeId] = item.AssetChildTaskId
		} else if dataType == "vuln" && item.ThreatChildTaskId > 0 {
			allNodeAndTaskId[item.NodeId] = item.ThreatChildTaskId
		}
	}
	return allNodeAndTaskId, nil
}

// 获取所有数据源的最新同步任务ID
func getNodeIdAndSyncTaskId(dataType string) (map[uint64]uint64, error) {
	// 获取所有数据源的最新taskid
	nodeTaskInfo, err := data_sync_child_task.NewDataSyncChildTaskModel().GetAllNodeLastSuccessTaskIDs()
	if err != nil {
		return nil, fmt.Errorf("获取节点最新同步任务ID失败,err: %v", err)
	}

	// key:nodeId,value:taskId
	allNodeAndTaskId := make(map[uint64]uint64)
	// 解析所有数据源的最新taskid
	for _, item := range nodeTaskInfo {
		if (dataType == "asset" || dataType == "device") && item.TaskAssetId > 0 {
			allNodeAndTaskId[item.NodeId] = item.TaskAssetId
		} else if dataType == "person" && item.TaskStaffId > 0 {
			allNodeAndTaskId[item.NodeId] = item.TaskStaffId
		} else if dataType == "vuln" && item.TaskVulId > 0 {
			allNodeAndTaskId[item.NodeId] = item.TaskVulId
		}
	}
	return allNodeAndTaskId, nil
}
