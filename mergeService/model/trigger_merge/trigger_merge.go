package trigger_merge

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"fobrain/fobrain/common/localtime"
	"fobrain/initialize/redis"
	"fobrain/models/elastic/business_system"
	"fobrain/models/mysql/field_tag_rules"
	"fobrain/pkg/utils"
	"math/rand"
	"slices"
	"time"

	"fobrain/initialize/es"
	"fobrain/mergeService/event"
	logs "fobrain/mergeService/utils/log"
	"fobrain/models/elastic/assets"
	"fobrain/models/elastic/device"
	"fobrain/models/elastic/poc"
	"fobrain/models/elastic/staff"
	"fobrain/models/mysql/merge"
	"fobrain/pkg/cfg"
	distributedlock "fobrain/pkg/distributedLock"
	"fobrain/pkg/queue"
	"fobrain/pkg/utils/common_logs"

	merge_helper "fobrain/mergeService/model/helper"

	"github.com/olivere/elastic/v7"
)

var logger *common_logs.Logger

func init() {
	logger = logs.GetLogger("service")
}

// triggerMergeCommon 通用触发融合任务的内部方法,参数校验成功后触发融合任务,任务成功创建即成功,触发执行是异步的
// @param dataType 数据类型
// @param taskParams 触发参数
// @param validateErr 参数校验错误
func triggerMergeCommon(dataType string, taskParams ITriggerParams, validateErr error) error {
	logger.Infof("开始触发%s融合任务,任务参数：%+v", dataType, taskParams)
	if validateErr != nil {
		logger.Errorf("参数校验失败,err: %v", validateErr)
		return validateErr
	}

	// 构造融合任务
	mergeRecord := &merge.MergeRecords{
		TriggerSourceId:  taskParams.GetTriggerSourceId(),
		TriggerNodeId:    taskParams.GetTriggerNodeId(),
		TriggerEvent:     taskParams.GetTriggerEvent(),
		EventTaskId:      taskParams.GetTaskId(),
		EventChildTaskId: taskParams.GetChildTaskId(),
		MergeType:        getMergeRecordTypeByTaskType(dataType),
		SubTrigger:       0,
	}
	if taskParams.GetSubTrigger() {
		mergeRecord.SubTrigger = 1
	}
	// 补充数据范围和参数
	if taskParams.IsPartialMerge() {
		mergeRecord.DataRange = merge.MergeRecordsDataRangeSpec
		mergeRecord.Params = taskParams.GetParamsJson()
	} else {
		mergeRecord.DataRange = merge.MergeRecordsDataRangeAll
		// 参数后续触发执行时补充
	}

	// 获取锁
	lockVal := fmt.Sprintf("%v-%v", taskParams.GetTaskId(), taskParams.GetChildTaskId())
	ok := lock(dataType, lockVal)
	if !ok {
		logger.Warnf("融合任务触发失败,存在正在进行的%v融合任务,任务ID：%v,子任务ID：%v", dataType, taskParams.GetTaskId(), taskParams.GetChildTaskId())
		mergeRecord.TaskStatus = merge.MergeRecordsStatusWaiting
	} else {
		mergeRecord.TaskStatus = merge.MergeRecordsStatusPrepare
	}

	// 加入队列
	mergeRecordId, err := enqueue(mergeRecord)
	if err != nil {
		logger.Errorf("加入队列失败,err: %v", err)
		if ok {
			// 解锁
			statusKey := fmt.Sprintf("merge_status:%s", dataType)
			distributedlock.Unlock(statusKey, lockVal)
		}
		return err
	}

	go func(locked bool) {
		if locked {
			// 触发融合任务
			err := executeMergeTask(dataType, mergeRecordId, taskParams.GetTriggerSourceId(), taskParams.GetTriggerNodeId(),
				taskParams.GetTriggerEvent(), taskParams.GetTaskId(), taskParams.GetChildTaskId())
			if err != nil {
				// 解锁
				statusKey := fmt.Sprintf("merge_status:%s", dataType)
				distributedlock.Unlock(statusKey, lockVal)
			}
		}
	}(ok)

	return nil
}

// TriggerMergeForAsset 触发融合任务
// 触发资产融合任务
// @param taskParams 资产融合任务参数
func TriggerMergeForAsset(taskParams *TriggerParamsForAsset) error {
	return triggerMergeCommon(MergeTypeAsset, taskParams, checkParamsForAsset(taskParams))
}

// TriggerMergeForDevice 触发设备融合任务
// @param taskParams 设备融合任务参数
func TriggerMergeForDevice(taskParams *TriggerParamsForDevice) error {
	return triggerMergeCommon(MergeTypeDevice, taskParams, checkParamsForDevice(taskParams))
}

// TriggerMergeForStaff 触发人员融合任务
// @param taskParams 人员融合任务参数
func TriggerMergeForStaff(taskParams *TriggerParamsForStaff) error {
	return triggerMergeCommon(MergeTypeStaff, taskParams, checkParamsForStaff(taskParams))
}

// TriggerMergeForVuln 触发漏洞融合任务
// @param taskParams 漏洞融合任务参数
func TriggerMergeForVuln(taskParams *TriggerParamsForVuln) error {
	return triggerMergeCommon(MergeTypeVuln, taskParams, checkParamsForVuln(taskParams))
}
func UpdateRefreshInterval(dataType string, time string) {
	switch dataType {
	case "asset":
		// 暂时调长index的刷新时间、避免频繁的刷新索引
		err1 := es.UpdateRefreshInterval(context.Background(), assets.NewAssets().IndexName(), time)
		if err1 != nil {
			logger.Warnf("更新索引刷新时间失败,err: %v", err1)
		}
		err2 := es.UpdateRefreshInterval(context.Background(), assets.NewAssetRecord().IndexName(), time)
		if err2 != nil {
			logger.Warnf("更新索引刷新时间失败,err: %v", err2)
		}
		err3 := es.UpdateRefreshInterval(context.Background(), assets.NewMergeRecordsModel().IndexName(), time)
		if err3 != nil {
			logger.Warnf("更新索引刷新时间失败,err: %v", err3)
		}
	case "device":
		// 暂时调长index的刷新时间、避免频繁的刷新索引
		err1 := es.UpdateRefreshInterval(context.Background(), device.NewDeviceModel().IndexName(), time)
		if err1 != nil {
			logger.Warnf("更新索引刷新时间失败,err: %v", err1)
		}
		err2 := es.UpdateRefreshInterval(context.Background(), device.NewDeviceRecordModel().IndexName(), time)
		if err2 != nil {
			logger.Warnf("更新索引刷新时间失败,err: %v", err2)
		}
		err3 := es.UpdateRefreshInterval(context.Background(), device.NewMergeRecordsModel().IndexName(), time)
		if err3 != nil {
			logger.Warnf("更新索引刷新时间失败,err: %v", err3)
		}
	case "vuln", "vulnerability":
		err1 := es.UpdateRefreshInterval(context.Background(), poc.NewPoc().IndexName(), time)
		if err1 != nil {
			logger.Warnf("更新索引刷新时间失败,err: %v", err1)
		}
		err2 := es.UpdateRefreshInterval(context.Background(), poc.NewPocRecord().IndexName(), time)
		if err2 != nil {
			logger.Warnf("更新索引刷新时间失败,err: %v", err2)
		}
		err3 := es.UpdateRefreshInterval(context.Background(), poc.NewMergeRecordsModel().IndexName(), time)
		if err3 != nil {
			logger.Warnf("更新索引刷新时间失败,err: %v", err3)
		}
	}
}

// executeMergeTask 执行融合任务
// @param dataType 数据类型
// @param mergeRecordId 融合任务ID
// @param triggerSourceId 数据源ID
// @param triggerNodeId 节点ID
// @param taskType 任务类型
// @param taskId 任务ID
// @param childTaskId 子任务ID
func executeMergeTask(dataType string, mergeRecordId uint64, triggerSourceId uint64, triggerNodeId uint64, taskType, taskId, childTaskId string) error {
	logger.Warnf("触发%v数据融合任务,融合任务ID：%v,数据源ID：%v,节点ID：%v,任务类型：%v,taskId:%v,childTaskId:%v", dataType, mergeRecordId, triggerSourceId, triggerNodeId, taskType, taskId, childTaskId)

	// 根据mergeRecordId获取融合任务
	mergeRecord, err := merge.NewMergeRecordsModel().GetById(mergeRecordId)
	if err != nil || mergeRecord == nil {
		return fmt.Errorf("获取融合任务失败,err: %v", err)
	}

	// 更新融合记录的内容
	updateMap := map[string]interface{}{}
	// 实际下发数据量
	var count int
	// 全量融合  这部分需要根据局部融合做优化
	allNodeAndSyncTaskId, err := getNodeIdAndSyncTaskId(dataType)
	if err != nil {
		return triggerFailHandler(mergeRecordId, dataType, taskId, childTaskId, "", fmt.Sprintf("获取节点最新任务ID失败,err: %+v", err), true)
	}
	// 获取所有数据源的最新扫描任务id
	allNodeAndProactiveTaskId, err := getNodeIdAndProactiveTaskId(dataType)
	if err != nil {
		return triggerFailHandler(mergeRecordId, dataType, taskId, childTaskId, "", fmt.Sprintf("获取节点最新任务ID失败,err: %+v", err), true)
	}
	// 合并所有数据源的最新任务id,包括扫描任务和同步任务
	allNodeAndTaskId := make(map[uint64][]uint64)
	for nodeId, taskId := range allNodeAndProactiveTaskId {
		allNodeAndTaskId[nodeId] = append(allNodeAndTaskId[nodeId], taskId)
	}
	for nodeId, taskId := range allNodeAndSyncTaskId {
		allNodeAndTaskId[nodeId] = append(allNodeAndTaskId[nodeId], taskId)
	}
	params := ""
	paramsBytes, err := json.Marshal(map[string]interface{}{
		"allNodeAndSyncTaskId":      allNodeAndSyncTaskId,
		"allNodeAndProactiveTaskId": allNodeAndProactiveTaskId,
	})
	if err != nil {
		logger.Warnf("序列化任务参数失败,err: %v", err)
	} else {
		params = string(paramsBytes)
	}
	// 触发数据融合任务开始事件
	logger.Infof("触发融合任务开始事件开始: %+v %s %s", allNodeAndProactiveTaskId, taskId, time.Now().Format("2006-01-02 15:04:05"))
	UpdateRefreshInterval(dataType, "3600s")
	if dataType == "asset" {
		// 因为event bus设计是map，触发时没有顺序，业务系统提取也需要用到人员和部门信息，所以不能走事件，直接提出来先触发缓存了
		merge_helper.AssetMergeTaskStartPrepareCache(allNodeAndTaskId, triggerSourceId, triggerNodeId, taskType, taskId, childTaskId)

		// 缓存业务系统，业务系统提取需要和旧的业务系统数据做合并，所以需要缓存
		_ = business_system.NewBusinessSystems().CacheAllBusinessSystems(context.Background())
		err = event.NewEventBus().Emit(event.Event_Asset_MergeTask_Start, allNodeAndTaskId, triggerSourceId, triggerNodeId, taskType, taskId, childTaskId)
		// 最后一批业务系统数据可能未落盘，强制等待5秒
		time.Sleep(5 * time.Second)
		// 缓存业务系统
		_ = business_system.NewBusinessSystems().CacheAllBusinessSystems(context.Background())
		// 缓存标签数据
		_ = field_tag_rules.NewFieldTagRuleConfig().CacheRuleData(redis.GetRedisClient())
	} else if dataType == "device" {
		err = event.NewEventBus().Emit(event.Event_Device_MergeTask_Start, allNodeAndTaskId, triggerSourceId, triggerNodeId, taskType, taskId, childTaskId)
	} else if dataType == "person" || dataType == "staff" {
		err = event.NewEventBus().Emit(event.Event_Person_MergeTask_Start, allNodeAndTaskId, triggerSourceId, triggerNodeId, taskType, taskId, childTaskId)
	} else if dataType == "vuln" {
		err = event.NewEventBus().Emit(event.Event_Vuln_MergeTask_Start, allNodeAndTaskId, triggerSourceId, triggerNodeId, taskType, taskId, childTaskId)
	}
	if err != nil {
		return triggerFailHandler(mergeRecordId, dataType, taskId, childTaskId, params, fmt.Sprintf("触发数据融合任务开始事件(%s)失败,任务ID：%v, err: %+v", event.Event_Asset_MergeTask_Start, mergeRecordId, err), true)
	}
	logger.Infof("触发融合任务开始事件结束: %+v %s %s", allNodeAndProactiveTaskId, taskId, time.Now().Format("2006-01-02 15:04:05"))

	if mergeRecord.DataRange == merge.MergeRecordsDataRangeAll {
		// 执行任务
		// 1:获取所有数据源的最新同步任务的taskid
		// 2:获取所有数据源的最新扫描任务的taskid
		// 3:根据scope获取所有数据源的最新数据
		// 4:根据下发数据融合任务

		// 根据下发数据融合任务
		if dataType == "asset" {
			// 将过程表的数据根据 ip+area 作为key，id作为value，保存到redis, 用于查询融合关联数据
			merge_helper.CacheAssetsProcessData([]string{})
			count, err = getAllProcessAssetAndSendMergeMsg(mergeRecordId, allNodeAndTaskId, taskType, taskId, childTaskId)
		} else if dataType == "device" {
			count, err = getAllProcessDeviceAndSendMergeMsg(mergeRecordId, allNodeAndTaskId, taskType, taskId, childTaskId)
		} else if dataType == "person" || dataType == "staff" {
			count, err = getAllProcessStaffAndSendMergeMsg(mergeRecordId, allNodeAndTaskId, taskType, taskId, childTaskId)
		} else if dataType == "vuln" {
			count, err = getAllProcessVulAndSendMergeMsg(mergeRecordId, allNodeAndTaskId, taskType, taskId, childTaskId)
		}
		if nil != err {
			logger.Warnf("触发数据融合任务失败,err: %+v", err)
			if count == 0 {
				return triggerFailHandler(mergeRecordId, dataType, taskId, childTaskId, params, fmt.Sprintf("下发数据融合任务失败,err: %+v", err), true)
			}
		}
		// 更新触发参数
		updateMap["params"] = params
	} else if mergeRecord.DataRange == merge.MergeRecordsDataRangeSpec {
		// 指定资产融合
		// 根据下发数据融合任务
		if dataType == "asset" {
			triggerParams := TriggerParamsForAsset{}
			err = json.Unmarshal([]byte(mergeRecord.Params), &triggerParams)
			if err != nil {
				return triggerFailHandler(mergeRecordId, dataType, taskId, childTaskId, mergeRecord.Params, fmt.Sprintf("触发数据融合任务失败,err: %+v", err), true)
			}
			count, err = getSpecAssetAndSendMergeMsg(mergeRecordId, triggerParams.AssetIds, triggerParams.IpInfos, triggerParams.DataRangeByTask, triggerParams.Fields, taskType, taskId, childTaskId)
		} else if dataType == "device" {
			triggerParams := TriggerParamsForDevice{}
			err = json.Unmarshal([]byte(mergeRecord.Params), &triggerParams)
			if err != nil {
				return triggerFailHandler(mergeRecordId, dataType, taskId, childTaskId, mergeRecord.Params, fmt.Sprintf("触发数据融合任务失败,err: %+v", err), true)
			}
			count, err = getSpecDeviceAndSendMergeMsg(mergeRecordId, triggerParams.DeviceIds, triggerParams.UniqueKeys, triggerParams.DataRangeByTask, triggerParams.Fields, taskType, taskId, childTaskId)
		} else if dataType == "person" || dataType == "staff" {
			triggerParams := TriggerParamsForStaff{}
			err = json.Unmarshal([]byte(mergeRecord.Params), &triggerParams)
			if err != nil {
				return triggerFailHandler(mergeRecordId, dataType, taskId, childTaskId, mergeRecord.Params, fmt.Sprintf("触发数据融合任务失败,err: %+v", err), true)
			}
			count, err = getSpecStaffAndSendMergeMsg(mergeRecordId, triggerParams.StaffIds, triggerParams.DataRangeByTask, triggerParams.Fields, taskType, taskId, childTaskId)
		} else if dataType == "vuln" {
			triggerParams := TriggerParamsForVuln{}
			err = json.Unmarshal([]byte(mergeRecord.Params), &triggerParams)
			if err != nil {
				return triggerFailHandler(mergeRecordId, dataType, taskId, childTaskId, mergeRecord.Params, fmt.Sprintf("触发数据融合任务失败,err: %+v", err), true)
			}
			count, err = getSpecVulAndSendMergeMsg(mergeRecordId, triggerParams.VulnIds, triggerParams.DataRangeByTask, triggerParams.Fields, taskType, taskId, childTaskId)
		}
		if nil != err {
			logger.Warnf("触发数据融合任务失败,err: %+v", err)
			if count == 0 {
				return triggerFailHandler(mergeRecordId, dataType, taskId, childTaskId, mergeRecord.Params, fmt.Sprintf("下发数据融合任务失败,err: %+v", err), true)
			}
		}
	}
	// 更新实际下发数据量
	updateMap["actual_data_num"] = count
	updateMap["start_time"] = localtime.Now()
	updateMap["task_status"] = merge.MergeRecordsStatusRunning

	// 更新融合记录
	err = merge.NewMergeRecordsModel().UpdateByMapIn([]uint64{mergeRecordId}, updateMap)
	if err != nil {
		logger.Warnf("更新融合记录失败,融合任务ID：%v,数据源ID：%v,节点ID：%v,任务类型：%v,任务ID：%v,err: %v", mergeRecordId, triggerSourceId, triggerNodeId, taskType, taskId, err)
	}
	if err != nil {
		return triggerFailHandler(mergeRecordId, dataType, taskId, childTaskId, mergeRecord.Params, fmt.Sprintf("触发数据融合任务失败,err: %+v", err), true)
	}
	if count == 0 {
		_ = triggerFailHandler(mergeRecordId, dataType, taskId, childTaskId, mergeRecord.Params, "触发数据量为空", false)
		return nil
	}
	logger.Warnf("触发%v数据融合任务成功,融合任务ID：%v,数据源ID：%v,节点ID：%v,任务类型：%v,任务ID：%v,共计下发%d条融合任务", dataType, mergeRecordId, triggerSourceId, triggerNodeId, taskType, taskId, count)
	return nil
}

// getSpecAssetAndSendMergeMsg 获取指定的资产数据并下发数据融合任务
func getSpecAssetAndSendMergeMsg(mergeRecordId uint64, assetIds []string, ipInfos []*IpInfo, dataRangeByTask *DataRangeByTask, fields []string, taskType, taskId, childTaskId string) (int, error) {
	dataList := make([]map[string]interface{}, 0)
	var err error
	if len(assetIds) > 0 {
		dataList, err = getSpecAssetByAssetIds(assetIds, taskId, childTaskId)
	} else if len(ipInfos) > 0 {
		dataList, err = getSpecAssetByIpInfos(ipInfos, taskId, childTaskId)
	} else if dataRangeByTask != nil {
		dataList, err = getSpecAssetByDataRangeByTask(dataRangeByTask, fields, taskId, childTaskId)
	}
	if err != nil {
		return 0, fmt.Errorf("获取指定的资产数据失败,taskType:%v,taskId:%v,err:%v", taskType, taskId, err)
	}
	ipList := make([]string, 0)
	for _, data := range dataList {
		ipList = append(ipList, fmt.Sprintf("%v_%v", data["ip"], data["area"]))
	}
	merge_helper.CacheAssetsProcessData(ipList)
	msgTaskId := fmt.Sprintf("%v-%v", taskId, childTaskId)
	msgList := make([]map[string]interface{}, 0)
	msgList = append(msgList, map[string]interface{}{
		"mr_id":     mergeRecordId,
		"task_id":   msgTaskId,
		"task_type": taskType,
		"is_start":  true,
	})
	// 用于去重,key为ip+area
	msgMap := make(map[string]struct{})
	for _, data := range dataList {
		msg := fmt.Sprintf("%v%v", data["ip"], data["area"])
		if _, ok := msgMap[msg]; ok {
			continue
		}
		msgMap[msg] = struct{}{}
		// 添加融合记录ID
		data["mr_id"] = mergeRecordId
		msgList = append(msgList, data)
	}
	// 如果数据量大于1，插入消息队列
	if len(msgList) > 1 {
		logger.Infof("准备插入消息队列,共计%d条数据,任务ID:%v", len(msgList)-1, taskId)
		msgList = append(msgList, map[string]interface{}{
			"mr_id":     mergeRecordId,
			"task_id":   msgTaskId,
			"task_type": taskType,
			"total":     len(msgList) - 1,
			"is_end":    true,
		})
		count := queue.NewQueue(queue.QueueType_Redis).Push(cfg.LoadQueue().AssetMergeQueue, msgList)
		logger.Infof("插入消息队列成功,共计%d条(含2条标识数据)数据,任务ID:%v", count, taskId)
		return count - 2, nil
	}
	return 0, nil
}

// getSpecAssetByAssetIds 根据资产ID获取资产数据
func getSpecAssetByAssetIds(assetIds []string, taskId, childTaskId string) ([]map[string]interface{}, error) {
	logger.Debugf("根据资产ID获取资产数据,assetIds:%v,taskId:%v", assetIds, taskId)
	var assetList []*assets.Assets
	chunks := slices.Chunk(assetIds, 1000)
	for chunk := range chunks {
		query := elastic.NewTermsQueryFromStrings("id", chunk...)
		rows, err := es.All[assets.Assets](1000, query, nil, "id", "all_process_ids", "ip", "area")
		if err != nil {
			logger.Warnf("根据资产ID获取资产数据失败,assetIds:%v,taskId:%v,err:%v", assetIds, taskId, err)
			return nil, err
		}
		assetList = append(assetList, rows...)
	}
	logger.Debugf("根据资产ID获取资产数据成功,taskId:%v,共%d条数据", taskId, len(assetList))
	msgTaskId := fmt.Sprintf("%v-%v", taskId, childTaskId)
	msgList := make([]map[string]interface{}, 0, len(assetList))
	for _, asset := range assetList {
		for _, processId := range asset.AllProcessIds {
			msgList = append(msgList, map[string]interface{}{
				"id":      processId,
				"ip":      asset.Ip,
				"area":    asset.Area,
				"task_id": msgTaskId,
			})
		}
	}
	logger.Debugf("根据资产ID获取任务数据成功,taskId:%v,共%d条数据", taskId, len(msgList))
	return msgList, nil
}

// getSpecAssetByIpInfos 根据IP信息获取资产数据
func getSpecAssetByIpInfos(ipInfos []*IpInfo, taskId, childTaskId string) ([]map[string]interface{}, error) {
	logger.Debugf("根据IP信息获取资产数据,ipInfos:%v,taskId:%v", ipInfos, taskId)
	// 预处理
	ipInfoMap := make(map[string]bool)
	ipList := make([]string, 0)
	areaList := make([]string, 0)
	for _, ipInfo := range ipInfos {
		ipInfoMap[fmt.Sprintf("%s_%d", ipInfo.Ip, ipInfo.Area)] = true
		ipList = append(ipList, ipInfo.Ip)
		areaList = append(areaList, fmt.Sprintf("%d", ipInfo.Area))
	}
	// 构造查询，这种查询可能会返回不符合条件的数据，需要根据ipInfoMap过滤
	query := elastic.NewBoolQuery().Must(elastic.NewTermsQueryFromStrings("ip", ipList...), elastic.NewTermsQueryFromStrings("area", areaList...))
	assetList, err := es.All[assets.Assets](1000, query, nil, "id", "all_process_ids", "ip", "area")
	if err != nil {
		logger.Warnf("根据IP信息获取资产数据失败,ipInfos:%v,taskId:%v,err:%v", ipInfos, taskId, err)
		return nil, err
	}
	logger.Debugf("根据IP信息获取资产数据成功,taskId:%v,共%d条数据", taskId, len(assetList))
	msgTaskId := fmt.Sprintf("%v-%v", taskId, childTaskId)
	msgList := make([]map[string]interface{}, 0, len(assetList))
	for _, asset := range assetList {
		// 过滤掉不符合条件的资产
		if _, ok := ipInfoMap[fmt.Sprintf("%s_%d", asset.Ip, asset.Area)]; !ok {
			continue
		}
		for _, processId := range asset.AllProcessIds {
			msgList = append(msgList, map[string]interface{}{
				"id":      processId,
				"ip":      asset.Ip,
				"area":    asset.Area,
				"task_id": msgTaskId,
			})
		}
	}
	logger.Debugf("根据IP信息获取任务数据成功,taskId:%v,共%d条数据", taskId, len(msgList))
	return msgList, nil
}

// getSpecAssetByDataRangeByTask 根据数据范围获取资产数据
func getSpecAssetByDataRangeByTask(dataRangeByTask *DataRangeByTask, fields []string, taskId, childTaskId string) ([]map[string]interface{}, error) {
	if dataRangeByTask == nil {
		return nil, errors.New("dataRangeByTask is nil")
	}
	logger.Debugf("根据数据范围获取资产数据,dataRangeByTask:%v,taskId:%v", dataRangeByTask, taskId)
	// 构造查询
	query := elastic.NewBoolQuery()
	query.Must(elastic.NewTermQuery("node", dataRangeByTask.NodeId))
	query.Must(elastic.NewTermsQuery("child_task_id", dataRangeByTask.ChildTaskId))
	assetList, err := es.All[assets.ProcessAssets](1000, query, nil, "id", "ip", "area")
	if err != nil {
		logger.Warnf("根据数据范围获取资产数据失败,dataRangeByTask:%v,taskId:%v,err:%v", dataRangeByTask, taskId, err)
		return nil, err
	}
	logger.Debugf("根据数据范围获取资产数据成功,taskId:%v,共%d条数据", taskId, len(assetList))
	msgTaskId := fmt.Sprintf("%v-%v", taskId, childTaskId)
	msgList := make([]map[string]interface{}, 0, len(assetList))
	for _, asset := range assetList {
		msgList = append(msgList, map[string]interface{}{
			"id":      asset.Id,
			"ip":      asset.Ip,
			"area":    asset.Area,
			"task_id": msgTaskId,
		})
	}
	logger.Debugf("根据数据范围获取任务数据成功,taskId:%v,共%d条数据", taskId, len(msgList))
	return msgList, nil
}

// getSpecDeviceAndSendMergeMsg 获取指定的设备数据并下发数据融合任务
func getSpecDeviceAndSendMergeMsg(mergeRecordId uint64, deviceIds []string, uniqueKeys []string, dataRangeByTask *DataRangeByTask, fields []string, taskType, taskId, childTaskId string) (int, error) {
	msgTaskId := fmt.Sprintf("%v-%v", taskId, childTaskId)
	msgList := make([]map[string]interface{}, 0)
	msgList = append(msgList, map[string]interface{}{
		"mr_id":     mergeRecordId,
		"task_id":   msgTaskId,
		"task_type": taskType,
		"is_start":  true,
	})
	var dataList []map[string]interface{}
	var err error
	if len(deviceIds) > 0 {
		dataList, err = getSpecDeviceByDeviceIds(deviceIds, taskId, childTaskId)
	} else if len(uniqueKeys) > 0 {
		dataList, err = getSpecDeviceByUniqueKeys(uniqueKeys, taskId, childTaskId)
	} else if dataRangeByTask != nil {
		dataList, err = getSpecDeviceByDataRangeByTask(dataRangeByTask, fields, taskId, childTaskId)
	}
	if err != nil {
		return 0, fmt.Errorf("获取指定的设备数据失败,taskType:%v,taskId:%v,err:%v", taskType, taskId, err)
	}
	// 如果数据量大于1，插入消息队列
	if len(dataList) >= 1 {
		logger.Infof("准备插入消息队列,共计%d条数据,任务ID:%v", len(dataList), taskId)
		for _, data := range dataList {
			data["mr_id"] = mergeRecordId
		}
		msgList = append(msgList, dataList...)
		msgList = append(msgList, map[string]interface{}{
			"mr_id":     mergeRecordId,
			"task_id":   msgTaskId,
			"task_type": taskType,
			"total":     len(dataList),
			"is_end":    true,
		})
		count := queue.NewQueue(queue.QueueType_Redis).Push(cfg.LoadQueue().DeviceMergeQueue, msgList)
		logger.Infof("插入消息队列成功,共计%d条(含2条标识数据)数据,任务ID:%v", count, taskId)
		return count - 2, nil
	}
	return 0, nil
}

// getSpecDeviceByDeviceIds 根据设备ID获取指定的设备数据
func getSpecDeviceByDeviceIds(deviceIds []string, taskId, childTaskId string) ([]map[string]interface{}, error) {
	logger.Debugf("根据设备ID获取设备数据,deviceIds:%v,taskId:%v", deviceIds, taskId)
	var deviceList = make([]*device.Device, 0)
	var processDevices = make([]*device.ProcessDevice, 0)
	chunks := slices.Chunk(deviceIds, 1000)
	for chunk := range chunks {
		query := elastic.NewTermsQueryFromStrings("id", chunk...)
		rows, err := es.All[device.Device](1000, query, nil, "id", "all_process_ids")
		if err != nil {
			logger.Warnf("根据设备ID获取设备数据失败,deviceIds:%v,taskId:%v,err:%v", chunk, taskId, err)
			return nil, err
		}
		var allProcessIds []string
		for _, d := range rows {
			allProcessIds = append(allProcessIds, d.AllProcessIds...)
			deviceList = append(deviceList, d)
		}
		if len(allProcessIds) == 0 {
			continue
		}
		processDevicesRows, err := es.All[device.ProcessDevice](1000, elastic.NewTermsQueryFromStrings("id", allProcessIds...), nil, "id", "unique_key")
		if err != nil {
			logger.Warnf("根据设备ID获取设备数据失败,deviceIds:%v,taskId:%v,err:%v", chunk, taskId, err)
			return nil, err
		}

		processDevices = append(processDevices, processDevicesRows...)
	}

	if len(processDevices) == 0 {
		return make([]map[string]interface{}, 0), nil
	}

	// 如果数据量大于1，插入消息队列
	msgList := make([]map[string]interface{}, 0)
	// 去重集合
	var set = make(map[string]struct{})
	msgTaskId := fmt.Sprintf("%v-%v", taskId, childTaskId)
	for _, d := range processDevices {
		if _, ok := set[d.UniqueKey]; ok {
			continue
		}
		set[d.UniqueKey] = struct{}{}
		msgList = append(msgList, map[string]interface{}{
			"id":      d.Id,
			"task_id": msgTaskId,
		})
	}

	logger.Debugf("根据设备ID获取任务数据成功,taskId:%v,共%d条数据", taskId, len(msgList))
	return msgList, nil
}

// getSpecDeviceByUniqueKeys 根据唯一键获取指定的设备数据
func getSpecDeviceByUniqueKeys(uniqueKeys []string, taskId, childTaskId string) ([]map[string]interface{}, error) {
	//去重
	uniqueKeys = utils.RemoveStringDuplicates(uniqueKeys)

	logger.Debugf("根据唯一键获取设备数据,uniqueKeys:%v,taskId:%v", uniqueKeys, taskId)

	var deviceList = make([]*device.Device, 0)
	var processDevices = make([]*device.ProcessDevice, 0)
	chunks := slices.Chunk(uniqueKeys, 1000)
	for chunk := range chunks {
		query := elastic.NewTermsQueryFromStrings("fid", chunk...)
		rows, err := es.All[device.Device](1000, query, nil, "id", "all_process_ids")
		if err != nil {
			logger.Warnf("根据设备ID获取设备数据失败,uniqueKeys:%v,taskId:%v,err:%v", chunk, taskId, err)
			return nil, err
		}
		var allProcessIds []string
		for _, d := range rows {
			allProcessIds = append(allProcessIds, d.AllProcessIds...)
			deviceList = append(deviceList, d)
		}
		if len(allProcessIds) == 0 {
			continue
		}
		processDevicesRows, err := es.All[device.ProcessDevice](1000, elastic.NewTermsQueryFromStrings("id", allProcessIds...), nil, "id", "unique_key")
		if err != nil {
			logger.Warnf("根据设备ID获取设备数据失败,uniqueKeys:%v,taskId:%v,err:%v", chunk, taskId, err)
			return nil, err
		}
		processDevices = append(processDevices, processDevicesRows...)
	}

	if len(processDevices) == 0 {
		return make([]map[string]interface{}, 0), nil
	}
	msgTaskId := fmt.Sprintf("%v-%v", taskId, childTaskId)
	msgList := make([]map[string]interface{}, 0, len(deviceList))
	// set集合去重 key为unique_key
	set := make(map[string]struct{})
	for _, d := range processDevices {
		if _, ok := set[d.UniqueKey]; ok {
			continue
		}
		set[d.UniqueKey] = struct{}{}
		msgList = append(msgList, map[string]interface{}{
			"id":      d.Id,
			"task_id": msgTaskId,
		})
	}
	logger.Debugf("根据唯一键获取任务数据成功,taskId:%v,共%d条数据", taskId, len(msgList))
	return msgList, nil
}

// getSpecDeviceByDataRangeByTask 根据数据范围获取指定的设备数据
func getSpecDeviceByDataRangeByTask(dataRangeByTask *DataRangeByTask, fields []string, taskId, childTaskId string) ([]map[string]interface{}, error) {
	if dataRangeByTask == nil {
		return nil, errors.New("dataRangeByTask is nil")
	}
	logger.Debugf("根据数据范围获取设备数据,dataRangeByTask:%v,taskId:%v", dataRangeByTask, taskId)
	// 构造查询
	query := elastic.NewBoolQuery()
	query.Must(elastic.NewTermQuery("node", dataRangeByTask.NodeId))
	query.Must(elastic.NewTermQuery("task_id", dataRangeByTask.TaskId))
	query.Must(elastic.NewTermsQuery("child_task_id", dataRangeByTask.ChildTaskId))
	deviceList, err := es.All[device.ProcessDevice](1000, query, nil, "id", "unique_key")
	if err != nil {
		logger.Warnf("根据数据范围获取设备数据失败,dataRangeByTask:%v,taskId:%v,err:%v", dataRangeByTask, taskId, err)
		return nil, err
	}
	logger.Debugf("根据数据范围获取设备数据成功,taskId:%v,共%d条数据", taskId, len(deviceList))
	msgTaskId := fmt.Sprintf("%v-%v", taskId, childTaskId)
	msgList := make([]map[string]interface{}, 0, len(deviceList))
	//去重 key 为 unique_key
	set := make(map[string]struct{})
	for _, d := range deviceList {
		if _, ok := set[d.UniqueKey]; ok {
			continue
		}
		set[d.UniqueKey] = struct{}{}
		msgList = append(msgList, map[string]interface{}{
			"id":      d.Id,
			"task_id": msgTaskId,
		})
	}
	logger.Debugf("根据数据范围获取任务数据成功,taskId:%v,共%d条数据", taskId, len(msgList))
	return msgList, nil
}

// getSpecStaffAndSendMergeMsg 获取指定的人员数据并下发数据融合任务
func getSpecStaffAndSendMergeMsg(mergeRecordId uint64, staffIds []string, dataRangeByTask *DataRangeByTask, fields []string, taskType string, taskId, childTaskId string) (int, error) {
	dataList := make([]map[string]interface{}, 0)
	var err error
	if len(staffIds) > 0 {
		dataList, err = getSpecStaffByStaffIds(staffIds, taskId, childTaskId)
	} else if dataRangeByTask != nil {
		dataList, err = getSpecStaffByDataRangeByTask(dataRangeByTask, fields, taskId, childTaskId)
	}
	if err != nil {
		return 0, fmt.Errorf("获取指定的人员数据失败,taskId:%v,err:%v", taskId, err)
	}
	msgTaskId := fmt.Sprintf("%v-%v", taskId, childTaskId)
	msgList := make([]map[string]interface{}, 0)
	msgList = append(msgList, map[string]interface{}{
		"mr_id":     mergeRecordId,
		"task_id":   msgTaskId,
		"task_type": taskType,
		"is_start":  true,
	})
	// 用于去重,key为unique_key
	msgMap := make(map[string]struct{})
	for _, data := range dataList {
		// 忽略没有unique_key的数据
		if _, ok := data["unique_key"]; !ok {
			continue
		}
		msg := fmt.Sprintf("%v", data["unique_key"])
		if _, ok := msgMap[msg]; ok {
			continue
		}
		msgMap[msg] = struct{}{}
		data["mr_id"] = mergeRecordId
		msgList = append(msgList, data)
	}
	// 如果数据量大于1，插入消息队列
	if len(msgList) > 1 {
		logger.Infof("准备插入消息队列,共计%d条数据,任务ID:%v", len(msgList)-1, taskId)
		msgList = append(msgList, map[string]interface{}{
			"mr_id":     mergeRecordId,
			"task_id":   msgTaskId,
			"task_type": taskType,
			"total":     len(msgList) - 1,
			"is_end":    true,
		})
		count := queue.NewQueue(queue.QueueType_Redis).Push(cfg.LoadQueue().PersonMergeQueue, msgList)
		logger.Infof("插入消息队列成功,共计%d条(含2条标识数据)数据,任务ID:%v", count, taskId)
		return count - 2, nil
	}
	rand.Int()
	return 0, nil
}

// getSpecStaffByStaffIds 根据人员ID获取人员数据
func getSpecStaffByStaffIds(staffIds []string, taskId, childTaskId string) ([]map[string]interface{}, error) {
	logger.Debugf("根据人员ID获取人员数据,staffIds:%v,taskId:%v", staffIds, taskId)
	var staffList = make([]*staff.Staff, 0)
	chunks := slices.Chunk(staffIds, 1000)
	for chunk := range chunks {
		// 构造查询
		query := elastic.NewBoolQuery()
		query.Must(elastic.NewTermsQueryFromStrings("id", chunk...))
		rows, err := es.All[staff.Staff](1000, query, nil, "id", "all_process_ids", "fid")
		if err != nil {
			logger.Warnf("根据人员ID获取人员数据失败,staffIds:%v,taskId:%v,err:%v", chunks, taskId, err)
			return nil, err
		}
		staffList = append(staffList, rows...)
	}

	logger.Debugf("根据人员ID获取人员数据成功,taskId:%v,共%d条数据", taskId, len(staffList))
	msgTaskId := fmt.Sprintf("%v-%v", taskId, childTaskId)
	msgList := make([]map[string]interface{}, 0, len(staffList))
	for _, s := range staffList {
		for _, processId := range s.AllProcessIds {
			msgList = append(msgList, map[string]interface{}{
				"id":         processId,
				"unique_key": s.Fid,
				"task_id":    msgTaskId,
			})
		}
	}
	logger.Debugf("根据人员ID获取任务数据成功,taskId:%v,共%d条数据", taskId, len(msgList))
	return msgList, nil
}

// getSpecStaffByDataRangeByTask 根据数据范围获取人员数据
func getSpecStaffByDataRangeByTask(dataRangeByTask *DataRangeByTask, fields []string, taskId, childTaskId string) ([]map[string]interface{}, error) {
	if dataRangeByTask == nil {
		return nil, errors.New("dataRangeByTask is nil")
	}
	logger.Debugf("根据数据范围获取人员数据,dataRangeByTask:%v,taskId:%v", dataRangeByTask, taskId)
	// 构造查询
	query := elastic.NewBoolQuery()
	query.Must(elastic.NewTermQuery("node", dataRangeByTask.NodeId))
	query.Must(elastic.NewTermQuery("task_id", dataRangeByTask.TaskId))
	query.Must(elastic.NewTermsQueryFromStrings("child_task_id", dataRangeByTask.ChildTaskId))
	staffList, err := es.All[staff.ProcessStaff](1000, query, nil, "id", "unique_key")
	if err != nil {
		logger.Warnf("根据数据范围获取人员数据失败,dataRangeByTask:%v,taskId:%v,err:%v", dataRangeByTask, taskId, err)
		return nil, err
	}
	logger.Debugf("根据数据范围获取人员数据成功,taskId:%v,共%d条数据", taskId, len(staffList))
	msgTaskId := fmt.Sprintf("%v-%v", taskId, childTaskId)
	msgList := make([]map[string]interface{}, 0, len(staffList))
	for _, s := range staffList {
		// 忽略没有unique_key的数据
		if s.UniqueKey == "" {
			continue
		}
		msgList = append(msgList, map[string]interface{}{
			"id":         s.Id,
			"unique_key": s.UniqueKey,
			"task_id":    msgTaskId,
		})
	}
	logger.Debugf("根据数据范围获取任务数据成功,taskId:%v,共%d条数据", taskId, len(msgList))
	return msgList, nil
}

// getSpecVulAndSendMergeMsg 获取指定的漏洞数据并下发数据融合任务
func getSpecVulAndSendMergeMsg(mergeRecordId uint64, vulnIds []string, dataRangeByTask *DataRangeByTask, fields []string, taskType string, taskId, childTaskId string) (int, error) {
	logger.Debugf("获取指定的漏洞数据并下发数据融合任务,mergeRecordId:%v,vulnIds:%v,dataRangeByTask:%v,taskType:%v,taskId:%v,childTaskId:%v", mergeRecordId, vulnIds, dataRangeByTask, taskType, taskId, childTaskId)
	msgTaskId := fmt.Sprintf("%v-%v", taskId, childTaskId)
	msgList := make([]map[string]interface{}, 0)
	msgList = append(msgList, map[string]interface{}{
		"mr_id":     mergeRecordId,
		"task_id":   msgTaskId,
		"task_type": taskType,
		"is_start":  true,
	})
	var dataList []map[string]interface{}
	var err error
	if len(vulnIds) > 0 {
		dataList, err = getSpecVulByVulnIds(vulnIds, taskId, childTaskId)
	} else if dataRangeByTask != nil {
		dataList, err = getSpecVulByDataRangeByTask(dataRangeByTask, fields, taskId, childTaskId)
	}
	if err != nil {
		return 0, fmt.Errorf("获取指定的漏洞数据失败,taskId:%v,err:%v", taskId, err)
	}

	// 如果数据量大于1，插入消息队列
	if len(dataList) >= 1 {
		for _, data := range dataList {
			data["mr_id"] = mergeRecordId
		}
		msgList = append(msgList, dataList...)
		logger.Infof("准备插入消息队列,共计%d条数据,任务ID:%v", len(msgList)-1, taskId)
		msgList = append(msgList, map[string]interface{}{
			"mr_id":     mergeRecordId,
			"task_id":   msgTaskId,
			"task_type": taskType,
			"total":     len(msgList) - 1,
			"is_end":    true,
		})
		count := queue.NewQueue(queue.QueueType_Redis).Push(cfg.LoadQueue().VulnMergeQueue, msgList)
		logger.Infof("插入消息队列成功,共计%d条(含2条标识数据)数据,任务ID:%v", count, taskId)
		return count - 2, nil
	}
	return 0, nil
}

// getSpecVulByVulnIds 根据漏洞ID获取指定的漏洞数据
func getSpecVulByVulnIds(vulnIds []string, taskId, childTaskId string) ([]map[string]interface{}, error) {
	logger.Debugf("根据漏洞ID获取漏洞数据,vulnIds:%v,taskId:%v", vulnIds, taskId)
	chunks := slices.Chunk(vulnIds, 1000)
	var vulnList = make([]*poc.Poc, 0)
	var mapProcessPocs = make(map[string]*poc.ProcessPoc)
	for chunk := range chunks {
		query := elastic.NewTermsQueryFromStrings("id", chunk...)
		rows, err := es.All[poc.Poc](1000, query, nil, "id", "all_process_ids")
		if err != nil {
			logger.Warnf("根据漏洞ID获取漏洞数据失败,vulnIds:%v,taskId:%v,err:%v", chunk, taskId, err)
			return nil, err
		}
		var allProcessIds []string
		for _, v := range rows {
			allProcessIds = append(allProcessIds, v.AllProcessIds...)
			vulnList = append(vulnList, v)
		}
		rows = nil
		if len(allProcessIds) == 0 {
			return make([]map[string]interface{}, 0), nil
		}
		processPocs, err := es.All[poc.ProcessPoc](1000, elastic.NewTermsQueryFromStrings("id", allProcessIds...), nil, "id", "original_id", "area", "ip", "source", "is_poc", "url", "cve", "cnvd", "cnnvd")
		if err != nil {
			logger.Warnf("根据漏洞ID获取漏洞数据失败,vulnIds:%v,taskId:%v,err:%v", chunk, taskId, err)
			continue
		}

		for _, v := range processPocs {
			mapProcessPocs[v.Id] = v
		}
		processPocs = nil
	}

	var mapKey = make(map[string]struct{})
	logger.Debugf("根据漏洞ID获取漏洞数据成功,taskId:%v,共%d条数据", taskId, len(vulnList))
	msgTaskId := fmt.Sprintf("%v-%v", taskId, childTaskId)
	msgList := make([]map[string]interface{}, 0, len(vulnList))
	// processId去重下发
	processIdSet := make(map[string]struct{})
	for _, vuln := range vulnList {
		for _, processId := range vuln.AllProcessIds {
			if _, ok := processIdSet[processId]; !ok {
				if p, po := mapProcessPocs[processId]; po {
					urlHash := utils.Get16MD5Encode(p.Url)
					k := ""
					if p.IsPoc == 1 || (p.Cve == "" && p.Cnvd == "" && p.Cnnvd == "") {
						k = fmt.Sprintf("%s-%s", p.OriginalId, urlHash)
					} else {
						k = fmt.Sprintf("%s-%s-%s-%s", p.Cve, p.Cnvd, p.Cnnvd, urlHash)
					}
					if _, e := mapKey[k]; !e {
						mapKey[k] = struct{}{}
						processIdSet[processId] = struct{}{}
						msgList = append(msgList, map[string]interface{}{
							"id":      processId,
							"task_id": msgTaskId,
						})
					}
				}

			}
		}
	}
	logger.Debugf("根据漏洞ID获取任务数据成功,taskId:%v,共%d条数据", taskId, len(msgList))
	return msgList, nil
}

// getSpecVulByDataRangeByTask 根据数据范围获取指定的任务数据
func getSpecVulByDataRangeByTask(dataRangeByTask *DataRangeByTask, fields []string, taskId, childTaskId string) ([]map[string]interface{}, error) {
	if dataRangeByTask == nil {
		return nil, errors.New("dataRangeByTask is nil")
	}
	logger.Debugf("根据数据范围获取漏洞数据,dataRangeByTask:%v,taskId:%v,childTaskId:%v", dataRangeByTask, taskId, childTaskId)
	query := elastic.NewBoolQuery()
	query.Must(elastic.NewTermQuery("node", dataRangeByTask.NodeId))
	query.Must(elastic.NewTermQuery("task_id", dataRangeByTask.TaskId))
	query.Must(elastic.NewTermQuery("child_task_id", dataRangeByTask.ChildTaskId))
	vulnList, err := es.All[poc.ProcessPoc](1000, query, nil, "id")
	if err != nil {
		logger.Warnf("根据数据范围获取漏洞数据失败,dataRangeByTask:%v,taskId:%v,err:%v", dataRangeByTask, taskId, err)
		return nil, err
	}
	logger.Debugf("根据数据范围获取漏洞数据成功,taskId:%v,共%d条数据", taskId, len(vulnList))
	msgTaskId := fmt.Sprintf("%v-%v", taskId, childTaskId)
	msgList := make([]map[string]interface{}, 0, len(vulnList))
	for _, vuln := range vulnList {
		msgList = append(msgList, map[string]interface{}{
			"id":      vuln.Id,
			"task_id": msgTaskId,
		})
	}
	logger.Debugf("根据数据范围获取任务数据成功,taskId:%v,共%d条数据", taskId, len(msgList))
	return msgList, nil
}

// getAllProcessAssetAndSendMergeMsg 获取所有资产数据并下发数据融合任务
// nodeTaskInfo 节点ID和任务ID的映射
// taskType 任务类型,sync\自定义
// taskId 资产任务ID
func getAllProcessAssetAndSendMergeMsg(mergeRecordId uint64, nodeTaskInfo map[uint64][]uint64, taskType, taskId, childTaskId string) (int, error) {
	msgTaskId := fmt.Sprintf("%v-%v", taskId, childTaskId)
	msgList := make([]map[string]interface{}, 0)
	msgList = append(msgList, map[string]interface{}{
		"mr_id":     mergeRecordId,
		"task_id":   msgTaskId,
		"task_type": taskType,
		"is_start":  true,
	})
	// 用于去重,key为ip+area
	msgMap := make(map[string]struct{})
	// 遍历nodeTaskInfo
	for nodeId, tidList := range nodeTaskInfo {
		perPage := 500
		currentCount := 0
		// 生成query
		query := elastic.NewBoolQuery()
		query.Must(elastic.NewTermQuery("node", nodeId))
		// 根据child_task_id过滤
		tids := make([]string, 0)
		for _, tid := range tidList {
			tids = append(tids, fmt.Sprintf("%d", tid))
		}
		query.Must(elastic.NewTermsQueryFromStrings("child_task_id", tids...))
		paList, err := es.All[assets.ProcessAssets](perPage, query, []elastic.Sorter{elastic.NewFieldSort("created_at").Desc(), elastic.NewFieldSort("id").Desc()}, "id", "ip", "area")
		if err != nil {
			logger.Errorf("下发融合任务时,获取资产数据失败: %v", err)
			return 0, fmt.Errorf("获取资产数据失败: %v", err)
		}
		if len(paList) == 0 {
			continue
		}
		currentCount += len(paList)
		for _, pa := range paList {
			key := fmt.Sprintf("%s-%d", pa.Ip, pa.Area)
			if _, ok := msgMap[key]; ok {
				continue
			}
			msgMap[key] = struct{}{}
			msgList = append(msgList, map[string]interface{}{
				"id":      pa.Id,
				"mr_id":   mergeRecordId,
				"ip":      pa.Ip,
				"area":    pa.Area,
				"task_id": msgTaskId,
			})
		}
		logger.Infof("获取 process_asset 数据,融合任务ID:%v,节点:%d,子任务IDs:%v,获取总量:%d", mergeRecordId, nodeId, tidList, currentCount)
	}
	logger.Infof("读取 process_asset 索引中的数据成功,共计%d条数据,任务ID：%v", len(msgList)-1, taskId)
	if len(msgList) > 1 {
		logger.Infof("准备插入消息队列,共计%d条数据,任务ID:%v", len(msgList)-1, taskId)
		msgList = append(msgList, map[string]interface{}{
			"mr_id":     mergeRecordId,
			"task_id":   msgTaskId,
			"task_type": taskType,
			"total":     len(msgList) - 1,
			"is_end":    true,
		})
		count := queue.NewQueue(queue.QueueType_Redis).Push(cfg.LoadQueue().AssetMergeQueue, msgList)
		logger.Infof("插入消息队列成功,共计%d条(含2条标识数据)数据,任务ID:%v", count, taskId)
		return count - 2, nil
	}
	return 0, nil
}

// getAllProcessDeviceAndSendMergeMsg 获取所有设备数据并下发数据融合任务
// nodeTaskInfo 节点ID和任务ID的映射
// taskType 任务类型,sync\自定义
// taskId 设备任务ID
func getAllProcessDeviceAndSendMergeMsg(mergeRecordId uint64, nodeTaskInfo map[uint64][]uint64, taskType, taskId, childTaskId string) (int, error) {
	msgTaskId := fmt.Sprintf("%v-%v", taskId, childTaskId)
	msgList := make([]map[string]interface{}, 0)
	msgList = append(msgList, map[string]interface{}{
		"mr_id":     mergeRecordId,
		"task_id":   msgTaskId,
		"task_type": taskType,
		"is_start":  true,
	})
	// set集合去重,key为unique_key
	set := make(map[string]struct{})
	// 遍历nodeTaskInfo
	for nodeId, tidList := range nodeTaskInfo {
		perPage := 500
		currentCount := 0
		// 生成query
		query := elastic.NewBoolQuery()
		query.Must(elastic.NewTermQuery("node", nodeId))
		// 根据child_task_id过滤
		tids := make([]string, 0)
		for _, tid := range tidList {
			tids = append(tids, fmt.Sprintf("%d", tid))
		}
		query.Must(elastic.NewTermsQueryFromStrings("child_task_id", tids...))
		paList, err := es.All[device.ProcessDevice](perPage, query, []elastic.Sorter{elastic.NewFieldSort("created_at").Desc(), elastic.NewFieldSort("id").Desc()}, "id", "unique_key")
		if err != nil {
			logger.Errorf("下发融合任务时,获取设备数据失败: %v", err)
			return 0, fmt.Errorf("获取设备数据失败: %v", err)
		}
		if len(paList) == 0 {
			continue
		}
		currentCount += len(paList)
		for _, pa := range paList {
			if _, ok := set[pa.UniqueKey]; ok {
				continue
			}
			set[pa.UniqueKey] = struct{}{}
			msgList = append(msgList, map[string]interface{}{
				"id":      pa.Id,
				"mr_id":   mergeRecordId,
				"task_id": msgTaskId,
			})
		}

		logger.Infof("获取 process_device 数据,融合任务ID:%v,节点:%d,子任务ID:%v,获取总量:%d", mergeRecordId, nodeId, tidList, currentCount)
	}
	logger.Infof("读取 process_device 索引中的数据成功,共计%d条数据,任务ID:%v", len(msgList)-1, taskId)
	if len(msgList) > 1 {
		logger.Infof("准备插入消息队列,共计%d条数据,任务ID:%v", len(msgList)-1, taskId)
		msgList = append(msgList, map[string]interface{}{
			"mr_id":     mergeRecordId,
			"task_id":   msgTaskId,
			"task_type": taskType,
			"total":     len(msgList) - 1,
			"is_end":    true,
		})
		count := queue.NewQueue(queue.QueueType_Redis).Push(cfg.LoadQueue().DeviceMergeQueue, msgList)
		logger.Infof("插入消息队列成功,共计%d条(含2条标识数据)数据,任务ID:%v", count, taskId)
		return count - 2, nil
	}
	return 0, nil
}

// getAllProcessStaffAndSendMergeMsg 获取所有人员数据并下发数据融合任务
// nodeTaskInfo 节点ID和任务ID的映射
// taskType 任务类型,sync\自定义
// taskId 人员任务ID
// childTaskId 子任务ID
func getAllProcessStaffAndSendMergeMsg(mergeRecordId uint64, nodeTaskInfo map[uint64][]uint64, taskType, taskId, childTaskId string) (int, error) {
	msgTaskId := fmt.Sprintf("%v-%v", taskId, childTaskId)
	msgList := make([]map[string]interface{}, 0)
	msgList = append(msgList, map[string]interface{}{
		"mr_id":     mergeRecordId,
		"task_id":   msgTaskId,
		"task_type": taskType,
		"is_start":  true,
	})
	// 用于去重,key为unique_key
	msgMap := make(map[string]struct{})
	// 遍历nodeTaskInfo
	for nodeId, tidList := range nodeTaskInfo {
		perPage := 500
		currentCount := 0
		// 生成query
		query := elastic.NewBoolQuery()
		query.Must(elastic.NewTermQuery("node", nodeId))
		// 根据child_task_id过滤
		tids := make([]string, 0)
		for _, tid := range tidList {
			tids = append(tids, fmt.Sprintf("%d", tid))
		}
		query.Must(elastic.NewTermsQueryFromStrings("child_task_id", tids...))
		paList, err := es.All[staff.ProcessStaff](perPage, query, []elastic.Sorter{elastic.NewFieldSort("created_at").Desc(), elastic.NewFieldSort("id").Desc()}, "id", "unique_key")
		if err != nil {
			logger.Errorf("下发融合任务时,获取人员数据失败: %v", err)
			return 0, fmt.Errorf("获取人员数据失败: %v", err)
		}
		if len(paList) == 0 {
			continue
		}
		currentCount += len(paList)
		for _, pa := range paList {
			if _, ok := msgMap[pa.UniqueKey]; ok {
				continue
			}
			msgMap[pa.UniqueKey] = struct{}{}
			msgList = append(msgList, map[string]interface{}{
				"id":         pa.Id,
				"unique_key": pa.UniqueKey,
				"mr_id":      mergeRecordId,
				"task_id":    msgTaskId,
			})
		}

		logger.Infof("获取 process_staff 数据,融合任务ID:%v,节点:%d,子任务ID:%v,获取总量:%d", mergeRecordId, nodeId, tidList, currentCount)
	}
	logger.Infof("读取 process_staff 索引中的数据成功,共计%d条数据,任务ID:%v", len(msgList)-1, taskId)
	if len(msgList) > 1 {
		logger.Infof("准备插入消息队列,共计%d条数据,任务ID:%v", len(msgList)-1, taskId)
		msgList = append(msgList, map[string]interface{}{
			"mr_id":     mergeRecordId,
			"task_id":   msgTaskId,
			"task_type": taskType,
			"total":     len(msgList) - 1,
			"is_end":    true,
		})
		count := queue.NewQueue(queue.QueueType_Redis).Push(cfg.LoadQueue().PersonMergeQueue, msgList)
		logger.Infof("插入消息队列成功,共计%d条(含2条标识数据)数据,任务ID:%v", count, taskId)
		return count - 2, nil
	}
	return 0, nil
}

// getAllProcessVulAndSendMergeMsg 获取所有漏洞数据并下发数据融合任务
// nodeTaskInfo 节点ID和任务ID的映射
// taskType 任务类型,sync\自定义
// taskId 漏洞任务ID
// childTaskId 子任务ID
func getAllProcessVulAndSendMergeMsg(mergeRecordId uint64, nodeTaskInfo map[uint64][]uint64, taskType, taskId, childTaskId string) (int, error) {
	msgTaskId := fmt.Sprintf("%v-%v", taskId, childTaskId)
	msgList := make([]map[string]interface{}, 0)
	msgList = append(msgList, map[string]interface{}{
		"mr_id":     mergeRecordId,
		"task_id":   msgTaskId,
		"task_type": taskType,
		"is_start":  true,
	})
	// 遍历nodeTaskInfo
	for nodeId, tidList := range nodeTaskInfo {
		perPage := 500
		currentCount := 0
		// 生成query
		query := elastic.NewBoolQuery()
		query.Must(elastic.NewTermQuery("node", nodeId))
		// 根据child_task_id过滤
		tids := make([]string, 0)
		for _, tid := range tidList {
			tids = append(tids, fmt.Sprintf("%d", tid))
		}
		query.Must(elastic.NewTermsQueryFromStrings("child_task_id", tids...))
		paList, err := es.All[poc.ProcessPoc](perPage, query, []elastic.Sorter{elastic.NewFieldSort("created_at").Desc(), elastic.NewFieldSort("id").Desc()}, "id")
		if err != nil {
			logger.Errorf("下发融合任务时,获取漏洞数据失败: %v", err)
			return 0, fmt.Errorf("获取漏洞数据失败: %v", err)
		}
		if len(paList) == 0 {
			continue
		}
		currentCount += len(paList)
		for _, pa := range paList {
			msgList = append(msgList, map[string]interface{}{
				"id":      pa.Id,
				"mr_id":   mergeRecordId,
				"task_id": msgTaskId,
			})
		}
		logger.Infof("获取 process_poc 数据,融合任务ID:%v,节点:%d,子任务ID:%v,获取总量:%d", mergeRecordId, nodeId, tidList, currentCount)
	}
	logger.Infof("读取 process_poc 索引中的数据成功,共计%d条数据,任务ID:%v", len(msgList)-1, taskId)
	if len(msgList) > 1 {
		logger.Infof("准备插入消息队列,共计%d条数据,任务ID:%v", len(msgList)-1, taskId)
		msgList = append(msgList, map[string]interface{}{
			"mr_id":     mergeRecordId,
			"task_id":   msgTaskId,
			"task_type": taskType,
			"total":     len(msgList) - 1,
			"is_end":    true,
		})
		count := queue.NewQueue(queue.QueueType_Redis).Push(cfg.LoadQueue().VulnMergeQueue, msgList)
		logger.Infof("插入消息队列成功,共计%d条(含2条标识数据)数据,任务ID:%v", count, taskId)
		return count - 2, nil
	}
	return 0, nil
}
