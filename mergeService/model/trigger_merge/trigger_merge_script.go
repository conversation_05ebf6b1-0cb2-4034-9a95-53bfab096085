package trigger_merge

import (
	"context"
	"encoding/json"
	"fmt"
	"fobrain/initialize/es"
	"fobrain/models/elastic/assets"
	"fobrain/models/mysql/strategy"
	"fobrain/pkg/utils"
	"strings"
	"sync"

	merge_helper "fobrain/mergeService/model/helper"

	"github.com/olivere/elastic/v7"
)

var ScriptAppendDataBusinessId = "script_append_data_business_id"
var lockGetScriptAppendDataBusinessId sync.RWMutex
var initScriptAppendDataBusinessId = false

func getScriptAppendDataBusinessId() (string, error) {
	lockGetScriptAppendDataBusinessId.Lock()
	defer lockGetScriptAppendDataBusinessId.Unlock()
	if initScriptAppendDataBusinessId {
		return ScriptAppendDataBusinessId, nil
	}

	script := map[string]interface{}{
		"script": map[string]string{
			"lang":   "painless",
			"source": Script_AppendData_Business,
		},
	}
	utils.AnyToStr(script)
	_, err := es.GetEsClient().PutScript().Id(ScriptAppendDataBusinessId).BodyString(utils.AnyToStr(script)).Do(context.Background())
	if err != nil {
		return "", err
	}
	initScriptAppendDataBusinessId = true
	return ScriptAppendDataBusinessId, nil
}

// GetScriptForAppendData 获取追加数据的脚本
func GetScriptForAppendData(field string, value string) (*elastic.Script, error) {
	if field == "" || value == "" {
		return nil, fmt.Errorf("字段或值不能为空")
	}
	if field == "business" {
		// 业务字段,尝试反序列化value
		var val assets.Business
		err := json.Unmarshal([]byte(value), &val)
		if err != nil {
			return nil, fmt.Errorf("要追加的值反序列化失败: %v", err)
		}
		// 来源为rule
		val.Source = "rule"
		businessDepartmentIds := make([]uint64, 0)
		for _, db := range val.DepartmentBase {
			businessDepartmentIds = append(businessDepartmentIds, db.Id)
		}
		id, err := getScriptAppendDataBusinessId()
		if err != nil {
			return nil, fmt.Errorf("获取追加数据业务脚本ID失败: %v", err)
		}
		script := elastic.NewScriptStored(id).Params(map[string]interface{}{
			"business_staff_ids":      []string{val.OwnerId},
			"business_department_ids": businessDepartmentIds,
			"new_entry":               val,
			"business_department":     val.DepartmentBase,
			"business_key":            "7-0",
		})
		return script, nil
	} else if field == "oper" {
		script := elastic.NewScriptInline(Script_AppendData_Asset_Oper).Lang("painless").Params(map[string]interface{}{
			"new_entry": value,
			"oper_key":  "7-0",
		})
		return script, nil
	} else {
		return nil, fmt.Errorf("不支持的字段: %s", field)
	}
}

func GetScriptForRefreshAsset(field string) (*elastic.Script, error) {
	if field == "" {
		return nil, fmt.Errorf("字段不能为空")
	}
	strategies, err := merge_helper.GetMergeRules(strategy.BusinessType_AssetMerge, logger)
	if err != nil {
		return nil, fmt.Errorf("获取最新融合策略失败: %v", err)
	}

	var s *strategy.Strategy
	for _, item := range strategies {
		if strings.EqualFold(item.FieldName, field) {
			s = item
			break
		}
	}
	if s == nil {
		return nil, fmt.Errorf("未找到字段策略: %s", field)
	}
	sourcePriority := make(map[uint64][]string)
	for _, s := range strategies {
		for sid, pri := range s.SourcePriority {
			// 添加到可信源列表中
			sourcePriority[pri] = append(sourcePriority[pri], sid)
		}
	}
	if field == "business" {
		script := elastic.NewScriptInline(Script_Refresh_Asset_Business).Lang("painless").Params(map[string]interface{}{
			"SourceData": sourcePriority,
		})
		return script, nil
	} else if field == "oper" {
		script := elastic.NewScriptInline(Script_Refresh_Asset_Oper).Lang("painless").Params(map[string]interface{}{
			"SourceData": sourcePriority,
		})
		return script, nil
	}
	return nil, fmt.Errorf("不支持的字段: %s", field)
}

const (
	Script_AppendData_Business = `
	// 初始化所有可能为 null 的字段
if (ctx._source.business == null) {
    ctx._source.business = [];
}
if (ctx._source.business_department == null) {
    ctx._source.business_department = [];
}
if (ctx._source.business_staff_ids == null) {
    ctx._source.business_staff_ids = [];
}
if (ctx._source.business_department_ids == null) {
    ctx._source.business_department_ids = [];
}
if (ctx._source.business_source == null) {
    ctx._source.business_source = [:];
}

// 参数校验
if (params.new_entry == null || params.new_entry.system_id == null) {
    throw new IllegalArgumentException("Missing required param: new_entry.system_id");
}
if (params.business_key == null) {
    params.business_key = "default_key";
}

// 更新 business_department（按 id 替换或添加）
if (params.business_department != null) {
    for (item in params.business_department) {
        boolean exists = false;
        for (int i = 0; i < ctx._source.business_department.size(); i++) {
            def entry = ctx._source.business_department[i];
            if (entry.id == item.id) {
                ctx._source.business_department.set(i, item);
                exists = true;
                break;
            }
        }
        if (!exists) {
            ctx._source.business_department.add(item);
        }
    }
}

// 合并 staff/department IDs（去重追加）
if (params.business_staff_ids != null) {
    ctx._source.business_staff_ids.addAll(params.business_staff_ids);
    ctx._source.business_staff_ids = ctx._source.business_staff_ids.stream().distinct().collect(Collectors.toList());
}
if (params.business_department_ids != null) {
    ctx._source.business_department_ids.addAll(params.business_department_ids);
    ctx._source.business_department_ids = ctx._source.business_department_ids.stream().distinct().collect(Collectors.toList());
}

// 更新 business 列表（按 system_id 替换）
ctx._source.business.removeIf(entry -> entry.system_id == params.new_entry.system_id);
ctx._source.business.add(params.new_entry);

// 更新 business_source 映射表
def targetList = ctx._source.business_source.getOrDefault(params.business_key, []);
targetList.removeIf(entry -> entry.system_id == params.new_entry.system_id);
targetList.add(params.new_entry);
ctx._source.business_source.put(params.business_key, targetList);
	`

	Script_Refresh_Asset_Business = `
		def sourceData = params.SourceData;
		def business = [];

		// Temporary map to hold valid business elements
		def temp_business = new HashSet();
		def found = false;  // 添加一个标志来指示是否找到了值

		// 检查每个优先级
		for (priority in sourceData.keySet().stream().sorted().collect(Collectors.toList())) {
			// 获取当前优先级的所有源
			def sources = sourceData[priority];
			for (source in sources) {
				// 构造系统和所有者的键前缀
				def key_prefix = source + "-";
				// 遍历系统源中的每个条目
				for (entry in ctx._source.business_source.entrySet()) {
					if (entry.getKey().startsWith(key_prefix)) {
						// 将新的 business 元素添加到临时集合中
						temp_business.addAll(entry.getValue());
						found = true;  // 设置标志为true,表示已找到值
					}
				}
			}
			if (found) break;  // 如果在当前优先级的源中找到值,跳出源循环
		}

		// 通过system_id确保唯一性
		def existing_business = new HashSet();
		for (item in temp_business) {
			if (!existing_business.contains(item.system_id) && (item.system != "" || item.owner != "")) {
				business.add(item);
                existing_business.add(item.system_id);
			}
		}

		ctx._source.business = business;
		// 合并次数+1
		ctx._source.merge_count = ctx._source.merge_count + 1;
`

	// "oper_source": {
	// 	"0": "王源",
	// 	"3-8": ""
	// }
	// "oper": [
	// 	"王源"
	// ]
	Script_AppendData_Asset_Oper = `
		if (ctx._source.oper == null) {
			ctx._source.oper = [];
		}

		if (ctx._source.oper_source == null) {
			ctx._source.oper_source = [:]; // Initialize as a map if null
		}

		ctx._source.oper_source.put(params.oper_key, params.new_entry);
		// 追加到oper字段
		if (!ctx._source.oper.contains(params.new_entry)) {
			ctx._source.oper.add(params.new_entry);
		}
	`

	Script_Refresh_Asset_Oper = `
		def sourceData = params.SourceData;
		def oper = [];

		// 临时字符串数组变量保存oper
		def temp_oper = [];
		def found = false;  // 添加一个标志来指示是否找到了值

		// 检查每个优先级
		for (priority in sourceData.keySet().stream().sorted().collect(Collectors.toList())) {
			// 获取当前优先级的所有源
			def sources = sourceData[priority];
			for (source in sources) {
				// 构造系统和所有者的键前缀
				def key_prefix = source + "-";
				// 遍历系统源中的每个条目
				for (entry in ctx._source.oper_source.entrySet()) {
					if (entry.getKey().startsWith(key_prefix)) {
						// 将新的 oper 元素添加到临时集合中
						if (!temp_oper.contains(entry.getValue()) && entry.getValue() != "") {
							temp_oper.add(entry.getValue());
						}
						found = true;  // 设置标志为true,表示已找到值
					}
				}
			}
			if (found) break;  // 如果在当前优先级的源中找到值,跳出源循环
		}

		ctx._source.oper = temp_oper;
		// 合并次数+1
		ctx._source.merge_count = ctx._source.merge_count + 1;
	`
)
