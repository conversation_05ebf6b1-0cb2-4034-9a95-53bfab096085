package trigger_merge

import "encoding/json"

const (
	MergeTypeAsset  = "asset"
	MergeTypeDevice = "device"
	MergeTypeStaff  = "staff"
	MergeTypeVuln   = "vuln"
)

// ITriggerParams 接口定义，用于统一处理不同类型的触发参数
type ITriggerParams interface {
	GetTriggerSourceId() uint64
	GetTriggerNodeId() uint64
	GetTriggerEvent() string
	GetTaskId() string
	GetChildTaskId() string
	IsPartialMerge() bool  // 是否部分融合
	GetParamsJson() string // 获取触发参数的json字符串
	GetSubTrigger() bool   // 是否需要继续触发关联数据的融合
}

// 实现ITriggerParams接口的方法
func (t *TriggerParamsForAsset) GetTriggerSourceId() uint64 { return t.TriggerParams.TriggerSourceId }
func (t *TriggerParamsForAsset) GetTriggerNodeId() uint64   { return t.TriggerParams.TriggerNodeId }
func (t *TriggerParamsForAsset) GetTriggerEvent() string    { return t.TriggerParams.TriggerEvent }
func (t *TriggerParamsForAsset) GetTaskId() string          { return t.TriggerParams.TaskId }
func (t *TriggerParamsForAsset) GetChildTaskId() string     { return t.TriggerParams.ChildTaskId }
func (t *TriggerParamsForAsset) GetSubTrigger() bool {
	return t.SubTrigger == 1
}
func (t *TriggerParamsForAsset) IsPartialMerge() bool {
	return len(t.AssetIds) > 0 || len(t.IpInfos) > 0 || t.DataRangeByTask != nil || len(t.Fields) > 0
}
func (t *TriggerParamsForAsset) GetParamsJson() string {
	p := map[string]interface{}{
		"asset_ids":          t.AssetIds,
		"ip_infos":           t.IpInfos,
		"data_range_by_task": t.DataRangeByTask,
		"fields":             t.Fields,
	}
	b, _ := json.Marshal(p)
	return string(b)
}

func (t *TriggerParamsForDevice) GetTriggerSourceId() uint64 { return t.TriggerParams.TriggerSourceId }
func (t *TriggerParamsForDevice) GetTriggerNodeId() uint64   { return t.TriggerParams.TriggerNodeId }
func (t *TriggerParamsForDevice) GetTriggerEvent() string    { return t.TriggerParams.TriggerEvent }
func (t *TriggerParamsForDevice) GetTaskId() string          { return t.TriggerParams.TaskId }
func (t *TriggerParamsForDevice) GetChildTaskId() string     { return t.TriggerParams.ChildTaskId }
func (t *TriggerParamsForDevice) GetSubTrigger() bool {
	return t.SubTrigger == 1
}
func (t *TriggerParamsForDevice) IsPartialMerge() bool {
	return len(t.DeviceIds) > 0 || len(t.UniqueKeys) > 0 || t.DataRangeByTask != nil || len(t.Fields) > 0
}
func (t *TriggerParamsForDevice) GetParamsJson() string {
	p := map[string]interface{}{
		"device_ids":         t.DeviceIds,
		"unique_keys":        t.UniqueKeys,
		"data_range_by_task": t.DataRangeByTask,
		"fields":             t.Fields,
	}
	b, _ := json.Marshal(p)
	return string(b)
}

func (t *TriggerParamsForStaff) GetTriggerSourceId() uint64 { return t.TriggerParams.TriggerSourceId }
func (t *TriggerParamsForStaff) GetTriggerNodeId() uint64   { return t.TriggerParams.TriggerNodeId }
func (t *TriggerParamsForStaff) GetTriggerEvent() string    { return t.TriggerParams.TriggerEvent }
func (t *TriggerParamsForStaff) GetTaskId() string          { return t.TriggerParams.TaskId }
func (t *TriggerParamsForStaff) GetChildTaskId() string     { return t.TriggerParams.ChildTaskId }
func (t *TriggerParamsForStaff) GetSubTrigger() bool {
	return t.SubTrigger == 1
}
func (t *TriggerParamsForStaff) IsPartialMerge() bool {
	return len(t.StaffIds) > 0 || t.DataRangeByTask != nil || len(t.Fields) > 0
}
func (t *TriggerParamsForStaff) GetParamsJson() string {
	p := map[string]interface{}{
		"staff_ids":          t.StaffIds,
		"data_range_by_task": t.DataRangeByTask,
		"fields":             t.Fields,
	}
	b, _ := json.Marshal(p)
	return string(b)
}

func (t *TriggerParamsForVuln) GetTriggerSourceId() uint64 { return t.TriggerParams.TriggerSourceId }
func (t *TriggerParamsForVuln) GetTriggerNodeId() uint64   { return t.TriggerParams.TriggerNodeId }
func (t *TriggerParamsForVuln) GetTriggerEvent() string    { return t.TriggerParams.TriggerEvent }
func (t *TriggerParamsForVuln) GetTaskId() string          { return t.TriggerParams.TaskId }
func (t *TriggerParamsForVuln) GetChildTaskId() string     { return t.TriggerParams.ChildTaskId }
func (t *TriggerParamsForVuln) GetSubTrigger() bool {
	return t.SubTrigger == 1
}
func (t *TriggerParamsForVuln) IsPartialMerge() bool {
	return len(t.VulnIds) > 0 || t.DataRangeByTask != nil || len(t.Fields) > 0
}
func (t *TriggerParamsForVuln) GetParamsJson() string {
	p := map[string]interface{}{
		"vuln_ids":           t.VulnIds,
		"data_range_by_task": t.DataRangeByTask,
		"fields":             t.Fields,
	}
	b, _ := json.Marshal(p)
	return string(b)
}

type TriggerParams struct {
	TriggerSourceId uint64 `json:"trigger_source_id" zh:"触发数据源ID"`
	TriggerNodeId   uint64 `json:"trigger_node_id" zh:"触发节点ID"`
	TriggerEvent    string `json:"trigger_event" zh:"触发事件" validate:"required"`
	TaskId          string `json:"task_id" zh:"任务ID,必填,用来溯源触发事件,触发者自行设置" validate:"required"`
	ChildTaskId     string `json:"child_task_id" zh:"子任务ID,辅助溯源,触发者自行设置"`
	SubTrigger      int8   `json:"sub_trigger" zh:"是否为子任务触发"`
}

type IpInfo struct {
	Ip   string `json:"ip" zh:"IP"`
	Area uint64 `json:"area" zh:"区域"`
}

type DataRangeByTask struct {
	TaskId      string `json:"task_id" zh:"任务ID"`
	ChildTaskId string `json:"child_task_id" zh:"子任务ID"`
	NodeId      uint64 `json:"node_id" zh:"节点ID"`
}

// TriggerParamsForAsset 资产融合触发参数
// AssetIds 资产ID列表,限制融合范围，优先级高于IpInfos和DataRangeByTask
// IpInfos IP信息列表,限制融合范围，优先级高于DataRangeByTask
// DataRangeByTask 融合范围,限制融合范围
// Fields 融合字段列表，限制融合范围,指定后只有指定字段会被融合
type TriggerParamsForAsset struct {
	TriggerParams
	AssetIds        []string         `json:"asset_ids" zh:"资产ID列表,来自asset.id"`
	IpInfos         []*IpInfo        `json:"ip_infos" zh:"IP信息列表"`
	DataRangeByTask *DataRangeByTask `json:"data_range_by_task" zh:"数据范围,限制融合范围"`
	Fields          []string         `json:"fields" zh:"字段列表"`
}

// TriggerParamsForDevice 设备融合触发参数
// DeviceIds 设备ID列表,限制融合范围，优先级高于UniqueKeys和DataRangeByTask
// UniqueKeys 设备唯一值列表,限制融合范围，优先级高于DataRangeByTask
// DataRangeByTask 融合范围,限制融合范围
// Fields 融合字段列表，限制融合范围,指定后只有指定字段会被融合
type TriggerParamsForDevice struct {
	TriggerParams
	DeviceIds       []string         `json:"device_ids" zh:"设备ID列表,来自device.id"`
	UniqueKeys      []string         `json:"unique_keys" zh:"设备唯一值列表"`
	DataRangeByTask *DataRangeByTask `json:"data_range_by_task" zh:"数据范围,限制融合范围"`
	Fields          []string         `json:"fields" zh:"字段列表"`
}

type StaffInfo struct {
	Name   string `json:"name" zh:"名称"`
	Mobile string `json:"mobile" zh:"手机号"`
}

// TriggerParamsForStaff 人员融合触发参数
// StaffIds 人员ID列表,限制融合范围，优先级高于DataRangeByTask
// DataRangeByTask 融合范围,限制融合范围
// Fields 融合字段列表，限制融合范围,指定后只有指定字段会被融合
type TriggerParamsForStaff struct {
	TriggerParams
	StaffIds        []string         `json:"staff_ids" zh:"人员ID列表,来自staff.id"`
	DataRangeByTask *DataRangeByTask `json:"data_range_by_task" zh:"数据范围,限制融合范围"`
	Fields          []string         `json:"fields" zh:"字段列表"`
}

// TriggerParamsForVuln 漏洞融合触发参数
// VulIds 漏洞ID列表,限制融合范围，优先级高于DataRangeByTask
// DataRangeByTask 融合范围,限制融合范围
// Fields 融合字段列表，限制融合范围,指定后只有指定字段会被融合
type TriggerParamsForVuln struct {
	TriggerParams
	VulnIds         []string         `json:"vuln_ids" zh:"漏洞ID列表,来自poc.id"`
	DataRangeByTask *DataRangeByTask `json:"data_range_by_task" zh:"数据范围,限制融合范围"`
	Fields          []string         `json:"fields" zh:"字段列表"`
}
