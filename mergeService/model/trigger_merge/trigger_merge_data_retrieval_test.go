package trigger_merge

import (
	"context"
	"testing"
	"time"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/alicebob/miniredis/v2"
	"github.com/go-redis/redis/v8"
	"github.com/olivere/elastic/v7"
	"github.com/stretchr/testify/assert"

	testcommon "fobrain/fobrain/tests/common_test"
	logs "fobrain/mergeService/utils/log"
	"fobrain/pkg/cfg"
)

func TestGetSpecAssetByAssetIds_Normal(t *testing.T) {
	// 设置日志
	logPatch := gomonkey.ApplyFuncReturn(logs.GetLogger, logs.GetLogger("test"))
	defer logPatch.Reset()

	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	// 模拟ES.All返回资产数据
	mockServer.Register("asset/_search", []*elastic.SearchHit{
		{
			Id:     "asset1",
			Source: []byte(`{"id":"asset1","ip":"***********","area":1,"all_process_ids":["process1","process2"]}`),
		},
		{
			Id:     "asset2",
			Source: []byte(`{"id":"asset2","ip":"***********","area":2,"all_process_ids":["process3"]}`),
		},
	})
	mockServer.RegisterEmptyScrollHandler()

	// 调用测试方法
	result, err := getSpecAssetByAssetIds([]string{"asset1", "asset2"}, "task1", "child1")

	// 验证结果
	assert.NoError(t, err)
	assert.Equal(t, 3, len(result)) // 两个资产共三个process_id

	// 验证返回的数据格式
	assert.Equal(t, "process1", result[0]["id"])
	assert.Equal(t, "***********", result[0]["ip"])
	assert.Equal(t, 1, result[0]["area"])
	assert.Equal(t, "task1-child1", result[0]["task_id"])
}

func TestGetSpecAssetByAssetIds_EmptyList(t *testing.T) {
	// 设置日志
	logPatch := gomonkey.ApplyFuncReturn(logs.GetLogger, logs.GetLogger("test"))
	defer logPatch.Reset()

	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	// 模拟ES返回空数据
	mockServer.Register("asset/_search", []*elastic.SearchHit{})
	mockServer.RegisterEmptyScrollHandler()

	// 调用测试方法
	result, err := getSpecAssetByAssetIds([]string{}, "task1", "child1")

	// 验证结果
	assert.NoError(t, err)
	assert.Equal(t, 0, len(result))
}

func TestGetSpecAssetByIpInfos_Normal(t *testing.T) {
	// 设置日志
	logPatch := gomonkey.ApplyFuncReturn(logs.GetLogger, logs.GetLogger("test"))
	defer logPatch.Reset()

	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	// 模拟ES返回资产数据
	mockServer.Register("asset/_search", []*elastic.SearchHit{
		{
			Id:     "asset1",
			Source: []byte(`{"id":"asset1","ip":"***********","area":1,"all_process_ids":["process1","process2"]}`),
		},
		{
			Id:     "asset2",
			Source: []byte(`{"id":"asset2","ip":"***********","area":2,"all_process_ids":["process3"]}`),
		},
		{
			Id:     "asset3",
			Source: []byte(`{"id":"asset3","ip":"***********","area":3,"all_process_ids":["process4"]}`),
		},
	})
	mockServer.RegisterEmptyScrollHandler()

	// 调用测试方法
	ipInfos := []*IpInfo{
		{Ip: "***********", Area: 1},
		{Ip: "***********", Area: 2},
	}
	result, err := getSpecAssetByIpInfos(ipInfos, "task1", "child1")

	// 验证结果
	assert.NoError(t, err)
	assert.Equal(t, 3, len(result)) // 两个资产共三个process_id

	// 验证返回的数据格式
	assert.Equal(t, "process1", result[0]["id"])
	assert.Equal(t, "***********", result[0]["ip"])
	assert.Equal(t, 1, result[0]["area"])
	assert.Equal(t, "task1-child1", result[0]["task_id"])

	// 验证过滤逻辑
	for _, item := range result {
		assert.NotEqual(t, "***********", item["ip"])
	}
}

func TestGetSpecAssetByIpInfos_EmptyList(t *testing.T) {
	// 设置日志
	logPatch := gomonkey.ApplyFuncReturn(logs.GetLogger, logs.GetLogger("test"))
	defer logPatch.Reset()

	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	// 模拟ES返回空数据
	mockServer.Register("asset/_search", []*elastic.SearchHit{})
	mockServer.RegisterEmptyScrollHandler()

	// 调用测试方法
	result, err := getSpecAssetByIpInfos([]*IpInfo{}, "task1", "child1")

	// 验证结果
	assert.NoError(t, err)
	assert.Equal(t, 0, len(result))
}

func TestGetSpecAssetByDataRangeByTask_Normal(t *testing.T) {
	// 设置日志
	logPatch := gomonkey.ApplyFuncReturn(logs.GetLogger, logs.GetLogger("test"))
	defer logPatch.Reset()

	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	// 模拟ES返回资产数据
	mockServer.Register("process_asset/_search", []*elastic.SearchHit{
		{
			Id:     "process1",
			Source: []byte(`{"id":"process1","ip":"***********","area":1}`),
		},
		{
			Id:     "process2",
			Source: []byte(`{"id":"process2","ip":"***********","area":2}`),
		},
	})
	mockServer.RegisterEmptyScrollHandler()

	// 调用测试方法
	dataRange := &DataRangeByTask{
		TaskId:      "task2",
		ChildTaskId: "child2",
		NodeId:      1,
	}
	result, err := getSpecAssetByDataRangeByTask(dataRange, []string{"field1"}, "task1", "child1")

	// 验证结果
	assert.NoError(t, err)
	assert.Equal(t, 2, len(result))

	// 验证返回的数据格式
	assert.Equal(t, "process1", result[0]["id"])
	assert.Equal(t, "***********", result[0]["ip"])
	assert.Equal(t, 1, result[0]["area"])
	assert.Equal(t, "task1-child1", result[0]["task_id"])
}

func TestGetSpecAssetByDataRangeByTask_EmptyRange(t *testing.T) {
	// 设置日志
	logPatch := gomonkey.ApplyFuncReturn(logs.GetLogger, logs.GetLogger("test"))
	defer logPatch.Reset()

	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	// 模拟ES返回空数据
	mockServer.Register("process_asset/_search", []*elastic.SearchHit{})
	mockServer.RegisterEmptyScrollHandler()

	// 调用测试方法
	result, err := getSpecAssetByDataRangeByTask(nil, []string{}, "task1", "child1")

	// 验证结果
	assert.Error(t, err) // 应该报错，因为dataRangeByTask为nil会导致空指针
	assert.Nil(t, result)
}

func TestGetSpecDeviceByDeviceIds_Normal(t *testing.T) {
	// 设置日志
	logPatch := gomonkey.ApplyFuncReturn(logs.GetLogger, logs.GetLogger("test"))
	defer logPatch.Reset()

	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()
	// mock es有BUG，正则匹配
	mockServer.Register("/process_device/_search", []*elastic.SearchHit{
		{
			Id:     "process1",
			Source: []byte(`{"id":"process1","unique_key":"k1"}`),
		},
		{
			Id:     "process2",
			Source: []byte(`{"id":"process2","unique_key":"k2"}`),
		},
		{
			Id:     "process3",
			Source: []byte(`{"id":"process3","unique_key":"k3"}`),
		},
	})
	// 模拟ES返回设备数据
	mockServer.Register("/device/_search", []*elastic.SearchHit{
		{
			Id:     "device1",
			Source: []byte(`{"id":"device1","all_process_ids":["process1","process2"]}`),
		},
		{
			Id:     "device2",
			Source: []byte(`{"id":"device2","all_process_ids":["process3"]}`),
		},
	})
	mockServer.RegisterEmptyScrollHandler()

	// 调用测试方法
	result, err := getSpecDeviceByDeviceIds([]string{"device1", "device2"}, "task1", "child1")

	// 验证结果
	assert.NoError(t, err)
	assert.Equal(t, 3, len(result)) // 两个设备共三个process_id

	// 验证返回的数据格式
	assert.Equal(t, "process1", result[0]["id"])
	assert.Equal(t, "task1-child1", result[0]["task_id"])
}

func TestGetSpecDeviceByDeviceIds_EmptyList(t *testing.T) {
	// 设置日志
	logPatch := gomonkey.ApplyFuncReturn(logs.GetLogger, logs.GetLogger("test"))
	defer logPatch.Reset()

	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()
	// 模拟ES返回空数据
	mockServer.Register("/device/_search", []*elastic.SearchHit{})
	mockServer.Register("/process_device/_search", []*elastic.SearchHit{})
	mockServer.RegisterEmptyScrollHandler()

	// 调用测试方法
	result, err := getSpecDeviceByDeviceIds([]string{}, "task1", "child1")

	// 验证结果
	assert.NoError(t, err)
	assert.Equal(t, 0, len(result))
}

func TestGetSpecDeviceByDeviceIds_Deduplicate(t *testing.T) {
	// 设置日志
	logPatch := gomonkey.ApplyFuncReturn(logs.GetLogger, logs.GetLogger("test"))
	defer logPatch.Reset()

	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()
	// mock es有BUG，正则匹配
	mockServer.Register("/process_device/_search", []*elastic.SearchHit{
		{
			Id:     "process1",
			Source: []byte(`{"id":"process1","unique_key":"k1"}`),
		},
		{
			Id:     "process2",
			Source: []byte(`{"id":"process2","unique_key":"k1"}`),
		},
		{
			Id:     "process3",
			Source: []byte(`{"id":"process3","unique_key":"k3"}`),
		},
	})
	// 模拟ES返回设备数据
	mockServer.Register("/device/_search", []*elastic.SearchHit{
		{
			Id:     "device1",
			Source: []byte(`{"id":"device1","all_process_ids":["process1","process2"]}`),
		},
		{
			Id:     "device2",
			Source: []byte(`{"id":"device2","all_process_ids":["process3"]}`),
		},
	})
	mockServer.RegisterEmptyScrollHandler()

	// 调用测试方法
	result, err := getSpecDeviceByDeviceIds([]string{"device1", "device2"}, "task1", "child1")

	// 验证结果
	assert.NoError(t, err)
	assert.Equal(t, 2, len(result)) // 去重后剩余两个

	// 验证返回的数据格式
	assert.Equal(t, "process1", result[0]["id"])
	assert.Equal(t, "task1-child1", result[0]["task_id"])
}

func TestGetSpecDeviceByUniqueKeys_Normal(t *testing.T) {
	// 设置日志
	logPatch := gomonkey.ApplyFuncReturn(logs.GetLogger, logs.GetLogger("test"))
	defer logPatch.Reset()

	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	// 模拟ES返回设备数据
	mockServer.Register("/device/_search", []*elastic.SearchHit{
		{
			Id:     "device1",
			Source: []byte(`{"id":"device1","all_process_ids":["process1","process2"]}`),
		},
		{
			Id:     "device2",
			Source: []byte(`{"id":"device2","all_process_ids":["process3"]}`),
		},
	})

	// 模拟ES返回process_device数据
	mockServer.Register("/process_device/_search", []*elastic.SearchHit{
		{
			Id:     "process1",
			Source: []byte(`{"id":"process1","unique_key":"device1"}`),
		},
		{
			Id:     "process2",
			Source: []byte(`{"id":"process2","unique_key":"device1"}`),
		},
		{
			Id:     "process3",
			Source: []byte(`{"id":"process3","unique_key":"device2"}`),
		},
	})

	// 调用测试方法
	result, err := getSpecDeviceByUniqueKeys([]string{"key1", "key2"}, "task1", "child1")

	// 验证结果
	assert.NoError(t, err)
	assert.Equal(t, 2, len(result), "应该返回2个process_device（去重后）")

	// 验证返回的数据格式
	// 确保返回的是process_device而不是device
	assert.Equal(t, "process1", result[0]["id"])
	assert.Equal(t, "task1-child1", result[0]["task_id"])
}

func TestGetSpecDeviceByUniqueKeys_EmptyList(t *testing.T) {
	// 设置日志
	logPatch := gomonkey.ApplyFuncReturn(logs.GetLogger, logs.GetLogger("test"))
	defer logPatch.Reset()

	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()
	// 模拟ES返回空数据
	mockServer.Register("/device/_search", []*elastic.SearchHit{})
	mockServer.RegisterEmptyScrollHandler()

	// 调用测试方法
	result, err := getSpecDeviceByUniqueKeys([]string{}, "task1", "child1")

	// 验证结果
	assert.NoError(t, err)
	assert.Equal(t, 0, len(result))
}

func TestGetSpecDeviceByUniqueKeys_DuplicateKeys(t *testing.T) {
	// 设置日志
	logPatch := gomonkey.ApplyFuncReturn(logs.GetLogger, logs.GetLogger("test"))
	defer logPatch.Reset()

	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()
	//重复unique_key
	mockServer.Register("/process_device/_search", []*elastic.SearchHit{
		{
			Id:     "process1",
			Source: []byte(`{"id":"process1","unique_key":"k1"}`),
		},
		{
			Id:     "process2",
			Source: []byte(`{"id":"process2","unique_key":"k1"}`),
		},
		{
			Id:     "process3",
			Source: []byte(`{"id":"process3","unique_key":"k3"}`),
		},
	})
	// 模拟ES返回设备数据
	mockServer.Register("/device/_search", []*elastic.SearchHit{
		{
			Id:     "device1",
			Source: []byte(`{"id":"device1","all_process_ids":["process1","process2"]}`),
		},
		{
			Id:     "device2",
			Source: []byte(`{"id":"device2","all_process_ids":["process3"]}`),
		},
	})
	mockServer.RegisterEmptyScrollHandler()

	// 调用测试方法
	result, err := getSpecDeviceByUniqueKeys([]string{"key1", "key2"}, "task1", "child1")

	// 验证结果
	assert.NoError(t, err)
	assert.Equal(t, 2, len(result)) // 去重后只剩两个

	// 验证返回的数据格式
	assert.Equal(t, "process1", result[0]["id"])
	assert.Equal(t, "task1-child1", result[0]["task_id"])
}

func TestGetSpecDeviceByDataRangeByTask_Normal(t *testing.T) {
	// 设置日志
	logPatch := gomonkey.ApplyFuncReturn(logs.GetLogger, logs.GetLogger("test"))
	defer logPatch.Reset()

	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	// 模拟ES返回设备数据
	mockServer.Register("process_device/_search", []*elastic.SearchHit{
		{
			Id:     "process1",
			Source: []byte(`{"id":"process1","ip":"***********","area":1,"unique_key":"111"}`),
		},
		{
			Id:     "process2",
			Source: []byte(`{"id":"process2","ip":"***********","area":2,"unique_key":"123"}`),
		},
	})
	mockServer.RegisterEmptyScrollHandler()

	// 调用测试方法
	dataRange := &DataRangeByTask{
		TaskId:      "task2",
		ChildTaskId: "child2",
		NodeId:      1,
	}
	result, err := getSpecDeviceByDataRangeByTask(dataRange, []string{"field1"}, "task1", "child1")

	// 验证结果
	assert.NoError(t, err)
	assert.Equal(t, 2, len(result))

	// 验证返回的数据格式
	assert.Equal(t, "process1", result[0]["id"])
	assert.Equal(t, "task1-child1", result[0]["task_id"])
}

func TestGetSpecDeviceByDataRangeByTask_EmptyRange(t *testing.T) {
	// 设置日志
	logPatch := gomonkey.ApplyFuncReturn(logs.GetLogger, logs.GetLogger("test"))
	defer logPatch.Reset()

	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	// 模拟ES返回空数据
	mockServer.Register("process_device/_search", []*elastic.SearchHit{})
	mockServer.RegisterEmptyScrollHandler()

	// 调用测试方法
	result, err := getSpecDeviceByDataRangeByTask(nil, []string{}, "task1", "child1")

	// 验证结果
	assert.Error(t, err) // 应该报错，因为dataRangeByTask为nil会导致空指针
	assert.Nil(t, result)
}

func TestGetSpecDeviceByDataRangeByTask_Deduplicate(t *testing.T) {
	// 设置日志
	logPatch := gomonkey.ApplyFuncReturn(logs.GetLogger, logs.GetLogger("test"))
	defer logPatch.Reset()

	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	// 模拟ES返回设备数据
	mockServer.Register("process_device/_search", []*elastic.SearchHit{
		{
			Id:     "process1",
			Source: []byte(`{"id":"process1","ip":"***********","area":1,"unique_key":"key1"}`),
		},
		{
			Id:     "process2",
			Source: []byte(`{"id":"process2","ip":"***********","area":1,"unique_key":"key1"}`),
		},
	})
	mockServer.RegisterEmptyScrollHandler()

	// 调用测试方法
	dataRange := &DataRangeByTask{
		TaskId:      "task2",
		ChildTaskId: "child2",
		NodeId:      1,
	}
	result, err := getSpecDeviceByDataRangeByTask(dataRange, []string{"field1"}, "task1", "child1")

	// 验证结果
	assert.NoError(t, err)
	assert.Equal(t, 1, len(result)) //去重后数据剩余一个

	// 验证返回的数据格式
	assert.Equal(t, "process1", result[0]["id"])
	assert.Equal(t, "task1-child1", result[0]["task_id"])
}

func TestGetSpecStaffByStaffIds(t *testing.T) {
	// 设置日志
	defer gomonkey.ApplyFuncReturn(logs.GetLogger, logs.GetLogger("test")).Reset()

	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	t.Run("正常获取人员数据", func(t *testing.T) {
		// 模拟ES返回人员数据
		mockServer.Register("staff/_search", []*elastic.SearchHit{
			{
				Id:     "staff1",
				Source: []byte(`{"id":"staff1","name":"张三","mobile":"13800138000","all_process_ids":["process1","process2"]}`),
			},
			{
				Id:     "staff2",
				Source: []byte(`{"id":"staff2","name":"李四","mobile":"13800138001","all_process_ids":["process3"]}`),
			},
		})
		mockServer.RegisterEmptyScrollHandler()
		// 调用测试方法
		result, err := getSpecStaffByStaffIds([]string{"staff1", "staff2"}, "task1", "child1")

		// 验证结果
		assert.NoError(t, err)
		assert.Equal(t, 3, len(result)) // 两个人员共三个process_id

		// 验证返回的数据格式
		assert.Equal(t, "process1", result[0]["id"])
		assert.Equal(t, "task1-child1", result[0]["task_id"])
	})
	time.Sleep(1 * time.Second)

	t.Run("空人员列表", func(t *testing.T) {
		// 模拟ES返回空数据
		mockServer.Register("staff/_search", []*elastic.SearchHit{})
		mockServer.RegisterEmptyScrollHandler()

		// 调用测试方法
		result, err := getSpecStaffByStaffIds([]string{}, "task1", "child1")

		// 验证结果
		assert.NoError(t, err)
		assert.Equal(t, 0, len(result))
	})
}

func TestGetSpecStaffByDataRangeByTask_Normal(t *testing.T) {
	// 设置日志
	defer gomonkey.ApplyFuncReturn(logs.GetLogger, logs.GetLogger("test")).Reset()

	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	// 模拟ES返回人员数据
	mockServer.Register("process_staff/_search", []*elastic.SearchHit{
		{
			Id:     "process1",
			Source: []byte(`{"id":"process1","unique_key":"111","name":"张三","mobile":"13800138000"}`),
		},
		{
			Id:     "process2",
			Source: []byte(`{"id":"process2","unique_key":"123","name":"李四","mobile":"13800138001"}`),
		},
	})
	mockServer.RegisterEmptyScrollHandler()

	// 调用测试方法
	dataRange := &DataRangeByTask{
		TaskId:      "task2",
		ChildTaskId: "child2",
		NodeId:      1,
	}
	result, err := getSpecStaffByDataRangeByTask(dataRange, []string{}, "task1", "child1")

	// 验证结果
	assert.NoError(t, err)
	assert.Equal(t, 2, len(result))

	// 验证返回的数据格式
	assert.Equal(t, "process1", result[0]["id"])
	assert.Equal(t, "task1-child1", result[0]["task_id"])
}

func TestGetSpecStaffByDataRangeByTask_EmptyRange(t *testing.T) {
	// 设置日志
	defer gomonkey.ApplyFuncReturn(logs.GetLogger, logs.GetLogger("test")).Reset()

	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	// 模拟ES返回空数据
	mockServer.Register("process_staff/_search", []*elastic.SearchHit{})
	mockServer.RegisterEmptyScrollHandler()

	// 调用测试方法
	result, err := getSpecStaffByDataRangeByTask(nil, []string{}, "task1", "child1")

	// 验证结果
	assert.Error(t, err) // 应该报错，因为dataRangeByTask为nil会导致空指针
	assert.Nil(t, result)
}

func TestGetSpecVulByVulnIds_Normal(t *testing.T) {
	// 设置日志
	defer gomonkey.ApplyFuncReturn(logs.GetLogger, logs.GetLogger("test")).Reset()

	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()
	// 模拟ES返回漏洞数据
	mockServer.Register("/process_poc/_search", []*elastic.SearchHit{
		{
			Id:     "process1",
			Source: []byte(`{"id":"process1","original_id":"process1","url":"127.0.0.1:22"}`),
		},
		{
			Id:     "process2",
			Source: []byte(`{"id":"process2","original_id":"process2","cve":"cve1","cnvd":"cnvd1","cnnvd":"cnnvd1","url":"127.0.0.1:23"}`),
		},
		{
			Id:     "process3",
			Source: []byte(`{"id":"process3","original_id":"process3","url":"127.0.0.1:24"}`),
		},
	})
	mockServer.Register("/poc/_search", []*elastic.SearchHit{
		{
			Id:     "vuln1",
			Source: []byte(`{"id":"vuln1","all_process_ids":["process1","process2"]}`),
		},
		{
			Id:     "vuln2",
			Source: []byte(`{"id":"vuln2","all_process_ids":["process3"]}`),
		},
	})
	mockServer.RegisterEmptyScrollHandler()

	// 调用测试方法
	result, err := getSpecVulByVulnIds([]string{"vuln1", "vuln2"}, "task1", "child1")

	// 验证结果
	assert.NoError(t, err)
	assert.Equal(t, 3, len(result)) // 两个漏洞共三个process_id

	// 验证返回的数据格式
	assert.Equal(t, "process1", result[0]["id"])
	assert.Equal(t, "task1-child1", result[0]["task_id"])
}

func TestGetSpecVulByVulnIds_EmptyList(t *testing.T) {
	// 设置日志
	defer gomonkey.ApplyFuncReturn(logs.GetLogger, logs.GetLogger("test")).Reset()

	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()
	// 模拟ES返回空数据
	mockServer.Register("/process_poc/_search", []*elastic.SearchHit{})
	mockServer.Register("/poc/_search", []*elastic.SearchHit{})
	mockServer.RegisterEmptyScrollHandler()

	// 调用测试方法
	result, err := getSpecVulByVulnIds([]string{}, "task1", "child1")

	// 验证结果
	assert.NoError(t, err)
	assert.Equal(t, 0, len(result))
}

func TestGetSpecVulByDataRangeByTask_Normal(t *testing.T) {
	// 设置日志
	defer gomonkey.ApplyFuncReturn(logs.GetLogger, logs.GetLogger("test")).Reset()

	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	// 模拟ES返回漏洞数据
	mockServer.Register("process_poc/_search", []*elastic.SearchHit{
		{
			Id:     "process1",
			Source: []byte(`{"id":"process1"}`),
		},
		{
			Id:     "process2",
			Source: []byte(`{"id":"process2"}`),
		},
	})
	mockServer.RegisterEmptyScrollHandler()

	// 调用测试方法
	dataRange := &DataRangeByTask{
		TaskId:      "task2",
		ChildTaskId: "child2",
		NodeId:      1,
	}
	result, err := getSpecVulByDataRangeByTask(dataRange, []string{}, "task1", "child1")

	// 验证结果
	assert.NoError(t, err)
	assert.Equal(t, 2, len(result))

	// 验证返回的数据格式
	assert.Equal(t, "process1", result[0]["id"])
	assert.Equal(t, "task1-child1", result[0]["task_id"])
}

func TestGetSpecVulByDataRangeByTask_EmptyRange(t *testing.T) {
	// 设置日志
	defer gomonkey.ApplyFuncReturn(logs.GetLogger, logs.GetLogger("test")).Reset()

	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	// 模拟ES返回空数据
	mockServer.Register("process_poc/_search", []*elastic.SearchHit{})
	mockServer.RegisterEmptyScrollHandler()

	// 调用测试方法
	result, err := getSpecVulByDataRangeByTask(nil, []string{}, "task1", "child1")

	// 验证结果
	assert.Error(t, err) // 应该报错，因为dataRangeByTask为nil会导致空指针
	assert.Nil(t, result)
}

func TestGetAllProcessAssetAndSendMergeMsg_Normal(t *testing.T) {
	defer gomonkey.ApplyFuncReturn(logs.GetLogger, logs.GetLogger("test")).Reset()

	// Setup mock redis
	s, err := miniredis.Run()
	if err != nil {
		t.Fatal(err)
	}
	defer s.Close()
	client := redis.NewClient(&redis.Options{Addr: s.Addr()})
	testcommon.SetRedisClient(client)

	// Setup mock elasticsearch server
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()
	mockServer.Register("process_asset/_search", []*elastic.SearchHit{
		{
			Id:     "1",
			Source: []byte(`{"id": "1", "ip": "127.0.0.1", "area": 1}`),
		},
	})

	count, err := getAllProcessAssetAndSendMergeMsg(uint64(1), map[uint64][]uint64{1: {1}}, "sync", "1", "c1")
	assert.Nil(t, err)
	assert.Equal(t, 1, count)
}

func TestGetAllProcessAssetAndSendMergeMsg_EmptyData(t *testing.T) {
	defer gomonkey.ApplyFuncReturn(logs.GetLogger, logs.GetLogger("test")).Reset()

	// Setup mock redis
	s, err := miniredis.Run()
	if err != nil {
		t.Fatal(err)
	}
	defer s.Close()
	client := redis.NewClient(&redis.Options{Addr: s.Addr()})
	testcommon.SetRedisClient(client)

	// Setup mock elasticsearch server
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()
	mockServer.Register("process_asset/_search", []*elastic.SearchHit{})

	count, err := getAllProcessAssetAndSendMergeMsg(uint64(1), map[uint64][]uint64{1: {1}}, "sync", "1", "c1")
	assert.Nil(t, err)
	assert.Equal(t, 0, count)
}

func TestGetAllProcessAssetAndSendMergeMsg_Error(t *testing.T) {
	defer gomonkey.ApplyFuncReturn(logs.GetLogger, logs.GetLogger("test")).Reset()

	// Setup mock redis
	s, err := miniredis.Run()
	if err != nil {
		t.Fatal(err)
	}
	defer s.Close()
	client := redis.NewClient(&redis.Options{Addr: s.Addr()})
	testcommon.SetRedisClient(client)

	// Setup mock elasticsearch server
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()
	mockServer.Register("process_asset/_search", []*elastic.SearchResult{
		{
			Error: &elastic.ErrorDetails{
				Reason: "error json",
			},
		},
	})

	count, err := getAllProcessAssetAndSendMergeMsg(uint64(1), map[uint64][]uint64{1: {1}}, "sync", "1", "c1")
	assert.NotNil(t, err)
	assert.Equal(t, 0, count)
}

func TestGetAllProcessAssetAndSendMergeMsg_RedisData(t *testing.T) {
	defer gomonkey.ApplyFuncReturn(logs.GetLogger, logs.GetLogger("test")).Reset()

	// Setup mock redis
	s, err := miniredis.Run()
	if err != nil {
		t.Fatal(err)
	}
	defer s.Close()
	client := redis.NewClient(&redis.Options{Addr: s.Addr()})
	testcommon.SetRedisClient(client)

	// Setup mock elasticsearch server
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()
	mockServer.Register("process_asset/_search", []*elastic.SearchHit{
		{
			Id:     "1",
			Source: []byte(`{"id": "1", "ip": "127.0.0.1", "area": 1}`),
		},
	})

	count, err := getAllProcessAssetAndSendMergeMsg(uint64(1), map[uint64][]uint64{1: {1}}, "sync", "1", "c1")
	assert.Nil(t, err)
	assert.Equal(t, 1, count)

	key := cfg.LoadQueue().AssetMergeQueue
	// 注册消费者组
	err = client.XGroupCreate(context.Background(), key, "test-group", "0").Err()
	assert.Nil(t, err)
	// 获取redis中的数据
	res, err := client.XReadGroup(context.Background(), &redis.XReadGroupArgs{
		Streams: []string{key, ">"},
		Count:   10,
		Block:   0,
		Group:   "test-group",
	}).Result()
	assert.Nil(t, err)
	assert.NotEmpty(t, res)
	for _, msg := range res[0].Messages {
		if msg.Values["is_end"] == true {
			assert.Equal(t, "1-c1", msg.Values["task_id"])
			assert.Equal(t, "sync", msg.Values["task_type"])
			assert.Equal(t, true, msg.Values["is_end"])
			assert.Equal(t, 1, msg.Values["total"])
		}
	}
}

func TestGetAllProcessDeviceAndSendMergeMsg_Normal(t *testing.T) {
	defer gomonkey.ApplyFuncReturn(logs.GetLogger, logs.GetLogger("test")).Reset()

	// Setup mock redis
	s, err := miniredis.Run()
	if err != nil {
		t.Fatal(err)
	}
	defer s.Close()
	client := redis.NewClient(&redis.Options{Addr: s.Addr()})
	testcommon.SetRedisClient(client)

	// Setup mock elasticsearch server
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()
	mockServer.Register("process_device/_search", []*elastic.SearchHit{
		{
			Id:     "device1",
			Source: []byte(`{"id": "device1"}`),
		},
	})

	count, err := getAllProcessDeviceAndSendMergeMsg(uint64(1), map[uint64][]uint64{1: {1}}, "sync", "1", "c1")
	assert.Nil(t, err)
	assert.Equal(t, 1, count)
}

func TestGetAllProcessDeviceAndSendMergeMsg_EmptyData(t *testing.T) {
	defer gomonkey.ApplyFuncReturn(logs.GetLogger, logs.GetLogger("test")).Reset()

	// Setup mock redis
	s, err := miniredis.Run()
	if err != nil {
		t.Fatal(err)
	}
	defer s.Close()
	client := redis.NewClient(&redis.Options{Addr: s.Addr()})
	testcommon.SetRedisClient(client)

	// Setup mock elasticsearch server
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()
	mockServer.Register("process_device/_search", []*elastic.SearchHit{})

	count, err := getAllProcessDeviceAndSendMergeMsg(uint64(1), map[uint64][]uint64{1: {1}}, "sync", "1", "c1")
	assert.Nil(t, err)
	assert.Equal(t, 0, count)
}

func TestGetAllProcessDeviceAndSendMergeMsg_Error(t *testing.T) {
	defer gomonkey.ApplyFuncReturn(logs.GetLogger, logs.GetLogger("test")).Reset()

	// Setup mock redis
	s, err := miniredis.Run()
	if err != nil {
		t.Fatal(err)
	}
	defer s.Close()
	client := redis.NewClient(&redis.Options{Addr: s.Addr()})
	testcommon.SetRedisClient(client)

	// Setup mock elasticsearch server
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()
	mockServer.Register("process_device/_search", []*elastic.SearchResult{
		{
			Error: &elastic.ErrorDetails{
				Reason: "error json",
			},
		},
	})

	count, err := getAllProcessDeviceAndSendMergeMsg(uint64(1), map[uint64][]uint64{1: {1}}, "sync", "1", "c1")
	assert.NotNil(t, err)
	assert.Equal(t, 0, count)
}

func TestGetAllProcessDeviceAndSendMergeMsg_RedisData(t *testing.T) {
	defer gomonkey.ApplyFuncReturn(logs.GetLogger, logs.GetLogger("test")).Reset()

	// Setup mock redis
	s, err := miniredis.Run()
	if err != nil {
		t.Fatal(err)
	}
	defer s.Close()
	client := redis.NewClient(&redis.Options{Addr: s.Addr()})
	testcommon.SetRedisClient(client)

	// Setup mock elasticsearch server
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()
	mockServer.Register("process_device/_search", []*elastic.SearchHit{
		{
			Id:     "device1",
			Source: []byte(`{"id": "device1"}`),
		},
	})

	count, err := getAllProcessDeviceAndSendMergeMsg(uint64(1), map[uint64][]uint64{1: {1}}, "sync", "1", "c1")
	assert.Nil(t, err)
	assert.Equal(t, 1, count)

	key := cfg.LoadQueue().DeviceMergeQueue
	// 注册消费者组
	err = client.XGroupCreate(context.Background(), key, "test-group", "0").Err()
	assert.Nil(t, err)
	// 获取redis中的数据
	res, err := client.XReadGroup(context.Background(), &redis.XReadGroupArgs{
		Streams: []string{key, ">"},
		Count:   10,
		Block:   0,
		Group:   "test-group",
	}).Result()
	assert.Nil(t, err)
	assert.NotEmpty(t, res)
	for _, msg := range res[0].Messages {
		if msg.Values["is_end"] == true {
			assert.Equal(t, "1-c1", msg.Values["task_id"])
			assert.Equal(t, "sync", msg.Values["task_type"])
			assert.Equal(t, true, msg.Values["is_end"])
			assert.Equal(t, 1, msg.Values["total"])
		}
	}
}

func TestGetAllProcessDeviceAndSendMergeMsg_Deduplicate(t *testing.T) {
	defer gomonkey.ApplyFuncReturn(logs.GetLogger, logs.GetLogger("test")).Reset()

	// Setup mock redis
	s, err := miniredis.Run()
	if err != nil {
		t.Fatal(err)
	}
	defer s.Close()
	client := redis.NewClient(&redis.Options{Addr: s.Addr()})
	testcommon.SetRedisClient(client)

	// Setup mock elasticsearch server
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()
	mockServer.Register("process_device/_search", []*elastic.SearchHit{
		{
			Id:     "device1",
			Source: []byte(`{"id": "device1","unique_key":"test_key1"}`),
		},
		{
			Id:     "device1", // 相同ID，应该被去重
			Source: []byte(`{"id": "device1","unique_key":"test_key1"}`),
		},
		{
			Id:     "device2",
			Source: []byte(`{"id": "device2","unique_key":"test_key2"}`),
		},
	})

	count, err := getAllProcessDeviceAndSendMergeMsg(uint64(1), map[uint64][]uint64{1: {1}}, "sync", "1", "c1")
	assert.Nil(t, err)
	assert.Equal(t, 2, count) // 去重后应该只有2个设备
}

func TestGetAllProcessStaffAndSendMergeMsg_Normal(t *testing.T) {
	defer gomonkey.ApplyFuncReturn(logs.GetLogger, logs.GetLogger("test")).Reset()

	// Setup mock redis
	s, err := miniredis.Run()
	if err != nil {
		t.Fatal(err)
	}
	defer s.Close()
	client := redis.NewClient(&redis.Options{Addr: s.Addr()})
	testcommon.SetRedisClient(client)

	// Setup mock elasticsearch server
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()
	mockServer.Register("process_staff/_search", []*elastic.SearchHit{
		{
			Id:     "staff1",
			Source: []byte(`{"id": "staff1","name":"staff1","mobile":"1234567890"}`),
		},
	})

	count, err := getAllProcessStaffAndSendMergeMsg(uint64(1), map[uint64][]uint64{1: {1}}, "sync", "1", "c1")
	assert.Nil(t, err)
	assert.Equal(t, 1, count)
}

func TestGetAllProcessStaffAndSendMergeMsg_EmptyData(t *testing.T) {
	defer gomonkey.ApplyFuncReturn(logs.GetLogger, logs.GetLogger("test")).Reset()

	// Setup mock redis
	s, err := miniredis.Run()
	if err != nil {
		t.Fatal(err)
	}
	defer s.Close()
	client := redis.NewClient(&redis.Options{Addr: s.Addr()})
	testcommon.SetRedisClient(client)

	// Setup mock elasticsearch server
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()
	mockServer.Register("process_staff/_search", []*elastic.SearchHit{})

	count, err := getAllProcessStaffAndSendMergeMsg(uint64(1), map[uint64][]uint64{1: {1}}, "sync", "1", "c1")
	assert.Nil(t, err)
	assert.Equal(t, 0, count)
}

func TestGetAllProcessStaffAndSendMergeMsg_Error(t *testing.T) {
	defer gomonkey.ApplyFuncReturn(logs.GetLogger, logs.GetLogger("test")).Reset()

	// Setup mock redis
	s, err := miniredis.Run()
	if err != nil {
		t.Fatal(err)
	}
	defer s.Close()
	client := redis.NewClient(&redis.Options{Addr: s.Addr()})
	testcommon.SetRedisClient(client)

	// Setup mock elasticsearch server
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()
	mockServer.Register("process_staff/_search", []*elastic.SearchResult{
		{
			Error: &elastic.ErrorDetails{
				Reason: "error json",
			},
		},
	})

	count, err := getAllProcessStaffAndSendMergeMsg(uint64(1), map[uint64][]uint64{1: {1}}, "sync", "1", "c1")
	assert.NotNil(t, err)
	assert.Equal(t, 0, count)
}

func TestGetAllProcessStaffAndSendMergeMsg_RedisData(t *testing.T) {
	defer gomonkey.ApplyFuncReturn(logs.GetLogger, logs.GetLogger("test")).Reset()

	// Setup mock redis
	s, err := miniredis.Run()
	if err != nil {
		t.Fatal(err)
	}
	defer s.Close()
	client := redis.NewClient(&redis.Options{Addr: s.Addr()})
	testcommon.SetRedisClient(client)

	// Setup mock elasticsearch server
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()
	mockServer.Register("process_staff/_search", []*elastic.SearchHit{
		{
			Id:     "staff1",
			Source: []byte(`{"id": "staff1","name":"staff1","mobile":"1234567890"}`),
		},
	})

	count, err := getAllProcessStaffAndSendMergeMsg(uint64(1), map[uint64][]uint64{1: {1}}, "sync", "1", "c1")
	assert.Nil(t, err)
	assert.Equal(t, 1, count)

	key := cfg.LoadQueue().PersonMergeQueue
	// 注册消费者组
	err = client.XGroupCreate(context.Background(), key, "test-group", "0").Err()
	assert.Nil(t, err)
	// 获取redis中的数据
	res, err := client.XReadGroup(context.Background(), &redis.XReadGroupArgs{
		Streams: []string{key, ">"},
		Count:   10,
		Block:   0,
		Group:   "test-group",
	}).Result()
	assert.Nil(t, err)
	assert.NotEmpty(t, res)
	for _, msg := range res[0].Messages {
		if msg.Values["is_end"] == true {
			assert.Equal(t, "1-c1", msg.Values["task_id"])
			assert.Equal(t, "sync", msg.Values["task_type"])
			assert.Equal(t, true, msg.Values["is_end"])
			assert.Equal(t, 1, msg.Values["total"])
		}
	}
}

func TestGetAllProcessVulnAndSendMergeMsg_Normal(t *testing.T) {
	defer gomonkey.ApplyFuncReturn(logs.GetLogger, logs.GetLogger("test")).Reset()

	// Setup mock redis
	s, err := miniredis.Run()
	if err != nil {
		t.Fatal(err)
	}
	defer s.Close()
	client := redis.NewClient(&redis.Options{Addr: s.Addr()})
	testcommon.SetRedisClient(client)

	// Setup mock elasticsearch server
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()
	mockServer.Register("process_poc/_search", []*elastic.SearchHit{
		{
			Id:     "poc1",
			Source: []byte(`{"id": "poc1"}`),
		},
	})

	count, err := getAllProcessVulAndSendMergeMsg(uint64(1), map[uint64][]uint64{1: {1}}, "sync", "1", "c1")
	assert.Nil(t, err)
	assert.Equal(t, 1, count)
}

func TestGetAllProcessVulnAndSendMergeMsg_EmptyData(t *testing.T) {
	defer gomonkey.ApplyFuncReturn(logs.GetLogger, logs.GetLogger("test")).Reset()

	// Setup mock redis
	s, err := miniredis.Run()
	if err != nil {
		t.Fatal(err)
	}
	defer s.Close()
	client := redis.NewClient(&redis.Options{Addr: s.Addr()})
	testcommon.SetRedisClient(client)

	// Setup mock elasticsearch server
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()
	mockServer.Register("process_poc/_search", []*elastic.SearchHit{})

	count, err := getAllProcessVulAndSendMergeMsg(uint64(1), map[uint64][]uint64{1: {1}}, "sync", "1", "c1")
	assert.Nil(t, err)
	assert.Equal(t, 0, count)
}

func TestGetAllProcessVulnAndSendMergeMsg_Error(t *testing.T) {
	defer gomonkey.ApplyFuncReturn(logs.GetLogger, logs.GetLogger("test")).Reset()

	// Setup mock redis
	s, err := miniredis.Run()
	if err != nil {
		t.Fatal(err)
	}
	defer s.Close()
	client := redis.NewClient(&redis.Options{Addr: s.Addr()})
	testcommon.SetRedisClient(client)

	// Setup mock elasticsearch server
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()
	mockServer.Register("process_poc/_search", []*elastic.SearchResult{
		{
			Error: &elastic.ErrorDetails{
				Reason: "error json",
			},
		},
	})

	count, err := getAllProcessVulAndSendMergeMsg(uint64(1), map[uint64][]uint64{1: {1}}, "sync", "1", "c1")
	assert.NotNil(t, err)
	assert.Equal(t, 0, count)
}

func TestGetAllProcessVulnAndSendMergeMsg_RedisData(t *testing.T) {
	defer gomonkey.ApplyFuncReturn(logs.GetLogger, logs.GetLogger("test")).Reset()

	// Setup mock redis
	s, err := miniredis.Run()
	if err != nil {
		t.Fatal(err)
	}
	defer s.Close()
	client := redis.NewClient(&redis.Options{Addr: s.Addr()})
	testcommon.SetRedisClient(client)

	// Setup mock elasticsearch server
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()
	mockServer.Register("process_poc/_search", []*elastic.SearchHit{
		{
			Id:     "poc1",
			Source: []byte(`{"id": "poc1"}`),
		},
	})

	count, err := getAllProcessVulAndSendMergeMsg(uint64(1), map[uint64][]uint64{1: {1}}, "sync", "1", "c1")
	assert.Nil(t, err)
	assert.Equal(t, 1, count)

	key := cfg.LoadQueue().VulnMergeQueue
	// 注册消费者组
	err = client.XGroupCreate(context.Background(), key, "test-group", "0").Err()
	assert.Nil(t, err)
	// 获取redis中的数据
	res, err := client.XReadGroup(context.Background(), &redis.XReadGroupArgs{
		Streams: []string{key, ">"},
		Count:   10,
		Block:   0,
		Group:   "test-group",
	}).Result()
	assert.Nil(t, err)
	assert.NotEmpty(t, res)
	for _, msg := range res[0].Messages {
		if msg.Values["is_end"] == true {
			assert.Equal(t, "1-c1", msg.Values["task_id"])
			assert.Equal(t, "sync", msg.Values["task_type"])
			assert.Equal(t, true, msg.Values["is_end"])
			assert.Equal(t, 1, msg.Values["total"])
		}
	}
}
