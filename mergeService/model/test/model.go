package test

import "fobrain/models/elastic/assets"

type OriginalData struct {
	ProcessAssets []*assets.ProcessAssets `json:"process_asset"`
	ExpectData    *ExpectData             `json:"expect_data"`
}

type ExpectData struct {
	AssetData        []*assets.Assets       `json:"asset"`
	AssetRecord      []*assets.AssetRecord  `json:"asset_record"`
	AssetMergeRecord []*assets.MergeRecords `json:"asset_merge_record"`
}
