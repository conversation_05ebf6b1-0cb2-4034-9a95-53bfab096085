//go:build ignore

package test

import (
	"context"
	"fobrain/initialize/es"
	"fobrain/mergeService/model"
	"fobrain/models/elastic/assets"
	"fobrain/models/mysql/strategy"
	"fobrain/pkg/cfg"
	"fobrain/pkg/queue"
	"sort"
	"testing"
	"time"

	"github.com/likexian/gokit/assert"
	"github.com/olivere/elastic/v7"

	testcommon "fobrain/fobrain/tests/common_test"
)

// 策略不存在数据源
// 预期：融合结果未采信任何数据源
func TestIpMerge_9(t *testing.T) {
	// 关闭测试模式
	testcommon.SetTestEnv(false)
	// 初始化配置
	cfg.InitLoadCfg()

	// 清除es数据
	err := clearDataFromEs("process_asset", "asset", "asset_record", "asset_merge_record")
	if err != nil {
		t.Errorf("清除es数据失败. err: %v", err)
		return
	}
	// 初始化设备融合key到redis，保证数据不会触发设备提取
	err = initDeviceMergeKeyToRedis()
	if err != nil {
		t.Errorf("初始化设备融合key到redis失败. err: %v", err)
		return
	}
	// 初始化资产融合规则到redis
	mergeRules := make([]*strategy.Strategy, 0)
	mergeRules = append(mergeRules, &strategy.Strategy{
		BusinessType:    "asset_merge",
		FieldName:       "HostName",
		SourcePriority:  map[string]uint64{},
		UntrustedSource: []string{},
	})
	mergeRules = append(mergeRules, &strategy.Strategy{
		BusinessType:    "asset_merge",
		FieldName:       "Business",
		SourcePriority:  map[string]uint64{},
		UntrustedSource: []string{},
	})
	err = initMergeRulesToRedis(mergeRules)
	if err != nil {
		t.Errorf("初始化设备融合规则到redis失败. err: %v", err)
		return
	}

	// 初始化资产融合流
	amt := model.NewAssetMergeFlow()
	// 启动资产融合流
	amt.Merge()

	originalData, err := initDataToEsFromFile("./data/9-策略不存在数据源.json", true)
	if err != nil {
		t.Errorf("初始化测试数据失败. err: %v", err)
	}
	if len(originalData.ProcessAssets) == 0 {
		t.Error("process_asset数据为空")
	} else {
		t.Log("process_asset数据初始化成功")
	}
	msgList, taskId, err := generateQueueData(originalData.ProcessAssets)
	if err != nil {
		t.Errorf("生成消息队列数据失败. err: %v", err)
	}
	count := queue.NewQueue(queue.QueueType_Redis).Push(cfg.LoadQueue().AssetMergeQueue, msgList)
	t.Logf("插入消息队列成功，共计%d条(含2条标识数据)数据,任务ID：%v", count, taskId)

	// 等待30s
	t.Log("等待30s")
	time.Sleep(30 * time.Second)

	t.Log("开始验证asset数据")
	// 获取es client
	elasticClient := es.GetEsClient()
	// 读取es数据
	expectAsset := originalData.ExpectData.AssetData[0]
	query := elastic.NewBoolQuery().Must(elastic.NewTermQuery("ip", expectAsset.Ip), elastic.NewTermQuery("area", expectAsset.Area))
	assetResult, err := elasticClient.Search().Index("asset").Query(query).Do(context.Background())
	if err != nil {
		t.Errorf("读取es数据失败. err: %v", err)
	}
	if assetResult.Hits.TotalHits.Value == 0 {
		t.Errorf("读取es数据失败. err: %v", err)
	}
	assetData, err := assets.ConvertToAssetsModel(assetResult.Hits.Hits[0])
	if err != nil {
		t.Errorf("解析es数据失败. err: %v", err)
	}
	sort.Strings(assetData.AllProcessIds)
	sort.Strings(expectAsset.AllProcessIds)
	assert.Equal(t, assetData.AllProcessIds, expectAsset.AllProcessIds, "AllProcessIds不一致")
	assert.Equal(t, 0, len(assetData.ProcessIds), "ProcessIds错误")
	sort.Slice(assetData.AllNodeIds, func(i, j int) bool { return assetData.AllNodeIds[i] < assetData.AllNodeIds[j] })
	sort.Slice(expectAsset.AllNodeIds, func(i, j int) bool { return expectAsset.AllNodeIds[i] < expectAsset.AllNodeIds[j] })
	assert.Equal(t, assetData.AllNodeIds, expectAsset.AllNodeIds, "AllNodeIds不一致")
	assert.Equal(t, 0, len(assetData.NodeIds), "NodeIds错误")
	sort.Slice(assetData.AllTaskDataIds, func(i, j int) bool { return assetData.AllTaskDataIds[i] < assetData.AllTaskDataIds[j] })
	sort.Slice(expectAsset.AllTaskDataIds, func(i, j int) bool { return expectAsset.AllTaskDataIds[i] < expectAsset.AllTaskDataIds[j] })
	assert.Equal(t, assetData.AllTaskDataIds, expectAsset.AllTaskDataIds, "AllTaskDataIds不一致")
	assert.Equal(t, 0, len(assetData.TaskDataIds), "TaskDataIds错误")
	t.Log("验证asset数据成功")

	t.Log("开始验证asset_record数据")
	// 获取asset_record数据
	// 获取asset_record数据
	query = elastic.NewBoolQuery().Must(elastic.NewTermQuery("asset_id", assetData.Id))
	assetRecordResult, err := elasticClient.Search().Index("asset_record").Query(query).Do(context.Background())
	if err != nil {
		t.Errorf("读取es数据(asset_record)失败. err: %v", err)
	}
	assetRecordData, err := assets.ConvertToAssetRecordModel(assetRecordResult.Hits.Hits[0])
	if err != nil {
		t.Errorf("解析es数据(asset_record)失败. err: %v", err)
	}
	expectAssetRecord := originalData.ExpectData.AssetRecord[0]
	assert.Equal(t, assetRecordData.Ip, expectAssetRecord.Ip, "Ip不一致")
	assert.Equal(t, assetRecordData.Area, expectAssetRecord.Area, "Area不一致")
	sort.Strings(assetRecordData.Asset.AllProcessIds)
	sort.Strings(expectAssetRecord.Asset.AllProcessIds)
	assert.Equal(t, assetRecordData.Asset.AllProcessIds, expectAssetRecord.Asset.AllProcessIds, "AllProcessIds不一致")
	assert.Equal(t, 0, len(assetRecordData.Asset.ProcessIds), "ProcessIds错误")
	t.Log("验证asset_record数据成功")

	t.Log("开始验证asset_merge_record数据")

	// 获取asset_merge_record数据
	t.Log("开始验证asset_merge_record数据")
	if len(originalData.ExpectData.AssetMergeRecord) == 0 {
		t.Log("预期数据的asset_merge_record数据为空,跳过验证asset_merge_record数据")
	} else {
		// 获取asset_merge_record数据
		query := elastic.NewBoolQuery().Must(elastic.NewTermQuery("asset_id", assetData.Id))
		assetMergeRecordResult, err := elasticClient.Search().Index("asset_merge_record").Query(query).Do(context.Background())
		if err != nil {
			t.Errorf("读取es数据(asset_merge_record)失败. err: %v", err)
		}
		assetMergeRecordData, err := assets.ConvertToAssetMergeRecordModel(assetMergeRecordResult.Hits.Hits[0])
		if err != nil {
			t.Errorf("解析es数据(asset_merge_record)失败. err: %v", err)
		}
		expectAssetMergeRecord := originalData.ExpectData.AssetMergeRecord[0]
		assert.Equal(t, assetMergeRecordData.Area, expectAssetMergeRecord.Area, "Area不一致")
		assert.Equal(t, assetMergeRecordData.Ip, expectAssetMergeRecord.Ip, "Ip不一致")
		assert.Equal(t, assetMergeRecordData.MergeMode, expectAssetMergeRecord.MergeMode, "MergeMode不一致")
		assert.Equal(t, assetMergeRecordData.AssetTaskIds, expectAssetMergeRecord.AssetTaskIds, "AssetTaskIds不一致")
		assert.Equal(t, assetMergeRecordData.NodeIds, expectAssetMergeRecord.NodeIds, "NodeIds不一致")
		t.Log("验证asset_merge_record数据成功")
	}
}
