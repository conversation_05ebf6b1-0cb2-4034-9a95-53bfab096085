package test

import (
	"context"
	"encoding/json"
	"fmt"
	"fobrain/initialize/es"
	"fobrain/initialize/redis"
	"fobrain/models/elastic/assets"
	"fobrain/models/mysql/strategy"
	redis_helper "fobrain/models/redis"
	"io"
	"os"
	"time"

	"github.com/olivere/elastic/v7"
)

// 清除es数据
func clearDataFromEs(index ...string) error {
	fmt.Println("开始清除es数据, 删除索引包括", index)
	// 获取es client
	elasticClient := es.GetEsClient()
	// 删除数据
	_, err := elasticClient.DeleteByQuery(index...).Query(elastic.NewMatchAllQuery()).Refresh("true").Do(context.Background())
	if err != nil {
		return fmt.Errorf("删除数据失败. err: %v", err)
	}
	fmt.Println("清除es数据成功")
	return nil
}

// 初始化设备融合key到redis
func initDeviceMergeKeyToRedis() error {
	client := redis.GetRedisClient()
	mergeKey := &strategy.Strategy{
		BusinessType:    "device_merge_key",
		FieldName:       "DeviceMergeKey",
		SourcePriority:  map[string]uint64{},
		UntrustedSource: []string{"Mac", "Sn", "HostName"},
	}
	// 序列化
	mergeKeyByte, err := json.Marshal(mergeKey)
	if err != nil {
		return fmt.Errorf("序列化设备融合key失败. err: %v", err)
	}
	_, err = client.Set(context.Background(), redis_helper.DeviceMergeKey(), mergeKeyByte, time.Minute*5).Result()
	if err != nil {
		return fmt.Errorf("设置redis数据失败. err: %v", err)
	}
	return nil
}

// 初始化设备融合规则到redis
func initMergeRulesToRedis(mergeRules []*strategy.Strategy) error {
	client := redis.GetRedisClient()
	// 序列化
	mergeRulesByte, err := json.Marshal(mergeRules)
	if err != nil {
		return fmt.Errorf("序列化设备融合规则失败. err: %v", err)
	}
	_, err = client.Set(context.Background(), redis_helper.AssetMergeRuleKey(), mergeRulesByte, time.Minute*5).Result()
	if err != nil {
		return fmt.Errorf("设置redis数据失败. err: %v", err)
	}
	return nil
}

// 初始化测试数据
func initDataToEsFromFile(fileName string, checkAsset bool) (*OriginalData, error) {
	fmt.Println("开始初始化测试数据")
	// 判断文件是否存在
	if _, err := os.Stat(fileName); os.IsNotExist(err) {
		return nil, fmt.Errorf("文件不存在. fileName: %v", fileName)
	}
	// 读取json文件
	jsonFile, err := os.Open(fileName)
	if err != nil {
		return nil, fmt.Errorf("读取json文件失败. err: %v", err)
	}
	defer jsonFile.Close()
	// 读取文件内容
	jsonData, err := io.ReadAll(jsonFile)
	if err != nil {
		return nil, fmt.Errorf("读取json文件内容失败. err: %v", err)
	}
	// 解析json数据
	data := &OriginalData{}
	err = json.Unmarshal(jsonData, &data)
	if err != nil {
		return nil, fmt.Errorf("解析json数据失败. err: %v", err)
	}
	if len(data.ProcessAssets) == 0 {
		return nil, fmt.Errorf("process_asset数据为空")
	}
	if checkAsset && len(data.ExpectData.AssetData) == 0 {
		return nil, fmt.Errorf("预期数据的asset数据为空")
	}
	// 写入es
	bulkService := es.GetEsClient().Bulk().Refresh("true")
	for _, pa := range data.ProcessAssets {
		bulkService.Add(elastic.NewBulkIndexRequest().Index("process_asset").Id(pa.Id).Doc(pa))
	}
	resp, err := bulkService.Do(context.Background())
	if err != nil {
		return nil, fmt.Errorf("写入es失败. err: %v", err)
	}
	fmt.Println("写入es成功. resp: ", resp)
	fmt.Println("初始化测试数据成功")
	return data, nil
}

// 生成消息队列数据
func generateQueueData(processAsset []*assets.ProcessAssets) ([]map[string]interface{}, string, error) {
	fmt.Println("开始生成消息队列数据")
	taskId := "real_test"
	taskIdMap := make(map[uint64]uint64)
	for _, pa := range processAsset {
		if pa.ChildTaskId > taskIdMap[pa.Node] {
			taskIdMap[pa.Node] = pa.ChildTaskId
		}
	}
	// 写入redis队列
	msgList := make([]map[string]interface{}, 0)
	msgList = append(msgList, map[string]interface{}{
		"task_id":   taskId,
		"task_type": "sync",
		"is_start":  true,
	})
	for _, pa := range processAsset {
		if pa.ChildTaskId == taskIdMap[pa.Node] {
			msgList = append(msgList, map[string]interface{}{
				"id":      pa.Id,
				"ip":      pa.Ip,
				"area":    pa.Area,
				"task_id": taskId,
			})
		}
	}
	msgList = append(msgList, map[string]interface{}{
		"task_id":   taskId,
		"task_type": "sync",
		"is_end":    true,
		"total":     len(processAsset),
	})
	fmt.Println("生成消息队列数据成功")
	return msgList, taskId, nil
}
