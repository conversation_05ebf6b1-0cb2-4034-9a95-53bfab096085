{"process_asset": [{"id_comment": "fmt.Sprintf(\"%d_%d_%d_%s\", st.Task.Id, st.Node.Id, st.Node.AreaId, id)", "id": "3_1_1_*******", "area": 1, "source": 5, "node": 1, "task_id": 3, "child_task_id": 4, "ip": "*******", "network_type": 1, "hostname": "hostname:taskid3+node1+area1+*******", "sn": "7795DB018A81413B887AA323557601D3", "mac": "00:00:00:00:00:00", "asset_task_id": "78_1_1_10.10.10.145", "created_at": "2024-11-27 22:31:48", "updated_at": "2024-11-27 22:31:48"}, {"id_comment": "fmt.Sprintf(\"%d_%d_%d_%s\", st.Task.Id, st.Node.Id, st.Node.AreaId, id)", "id": "31_2_1_*******", "area": 1, "source": 6, "node": 2, "task_id": 31, "child_task_id": 4, "ip": "*******", "network_type": 1, "hostname": "hostname:taskid31+node2+area1+*******", "sn": "7795DB018A81413B887AA323557601D3", "mac": "00:00:00:00:00:00", "asset_task_id": "78_2_1_10.10.10.145", "created_at": "2024-12-04 17:16:08", "updated_at": "2024-12-04 17:16:08"}], "expect_data": {"asset": [{"fid": "*******:1", "fid_hash": "6c06aec179a41116cee87f2977e40f1c", "area": 1, "process_ids": ["31_2_1_*******"], "source_ids": [6], "node_ids": [2], "asset_task_ids": ["78_2_1_10.10.10.145"], "all_source_ids": [5, 6], "all_node_ids": [1, 2], "all_asset_task_ids": ["78_1_1_10.10.10.145", "78_2_1_10.10.10.145"], "all_process_ids": ["3_1_1_*******", "31_2_1_*******"], "ip": "*******", "ip_type": 1, "hostname": ["hostname:taskid31+node2+area1+*******"], "hostname_source": {"5-1": "hostname:taskid3+node1+area1+*******", "6-2": "hostname:taskid31+node2+area1+*******"}, "network_type": 1, "fusion_rules": null, "deleted_at": null, "purged_at": null, "created_at": "2024-11-28 17:16:08", "updated_at": "2024-11-28 17:16:08", "is_device_extracted": 2, "merge_count": 0, "person_limit": null, "person_limit_hash": null, "tag": null}], "asset_record": [{"Asset": {"id": "120a3612d86d40368f1b875a13071185", "fid": "*******:1", "fid_hash": "6c06aec179a41116cee87f2977e40f1c", "area": 1, "process_ids": ["3_1_1_*******", "31_2_1_*******"], "source_ids": [5, 6], "node_ids": [1, 2], "asset_task_ids": ["78_1_1_10.10.10.145", "78_2_1_10.10.10.145"], "all_source_ids": [5, 6], "all_node_ids": [1, 2], "all_asset_task_ids": ["78_1_1_10.10.10.145", "78_2_1_10.10.10.145"], "all_process_ids": ["3_1_1_*******", "31_2_1_*******"], "ip": "*******", "ip_type": 1, "ip_segment": null, "ip_segment_source": {"5-1": "", "6-2": ""}, "hostname": ["hostname:taskid3+node1+area1+*******", "hostname:taskid31+node2+area1+*******"], "hostname_source": {"5-1": "hostname:taskid3+node1+area1+*******", "6-2": "hostname:taskid31+node2+area1+*******"}, "network_type": 1, "fusion_rules": null, "deleted_at": null, "purged_at": null, "created_at": "2024-11-29 11:05:13", "updated_at": "2024-11-29 11:05:13", "is_device_extracted": 2, "merge_count": 0, "person_limit": null, "person_limit_hash": null, "tag": null}, "id": "26c145049a834dc8bc183cf3ddb90b69", "batch_no": "", "backup_mode": "full", "backup_time": "2024-11-29 11:05:13", "asset_id": "120a3612d86d40368f1b875a13071185", "ip": "*******", "area": 1}], "asset_merge_record": [{"id": "e5096ab36dd74746a502008ff0f850e9", "asset_id": "ad696162425b4210b6f775d7ea11f988", "asset_record_id": "a35c83b9fcd94bd1b80c75feb64be9a2", "area": 1, "ip": "*******", "status": 1, "message": "", "source_ids": [6], "node_ids": [2], "asset_task_ids": ["78_2_1_10.10.10.145"], "strategies": [{"id": 0, "created_at": "", "updated_at": "", "business_type": "asset_merge", "field_name": "HostName", "display_name": "", "source_priority": {"5": 2, "6": 1}, "untrusted_source": [], "version": 0}, {"id": 0, "created_at": "", "updated_at": "", "business_type": "asset_merge", "field_name": "Business", "display_name": "", "source_priority": {"5": 2, "6": 1}, "untrusted_source": null, "version": 0}], "created_at": "2024-12-12 17:32:06", "updated_at": null, "merge_mode": "auto", "batch_no": "", "asset_ids": null, "is_device_extracted": 1, "tigger_source_id": 5}]}}