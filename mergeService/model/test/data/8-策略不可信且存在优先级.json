{"process_asset": [{"id_comment": "fmt.Sprintf(\"%d_%d_%d_%s\", st.Task.Id, st.Node.Id, st.Node.AreaId, id)", "id": "2_1_1_*******", "area": 1, "source": 4, "node": 1, "task_id": 2, "child_task_id": 4, "ip": "*******", "network_type": 1, "hostname": "hostname:taskid2+node1+area1+*******", "sn": "CA4ADACC15A24EA9ABB8573C50617AAC", "asset_task_id": "78_2_4_*******", "created_at": "2024-11-27 22:31:48", "updated_at": "2024-11-27 22:31:48"}, {"id_comment": "fmt.Sprintf(\"%d_%d_%d_%s\", st.Task.Id, st.Node.Id, st.Node.AreaId, id)", "id": "3_3_1_*******", "area": 1, "source": 5, "node": 3, "task_id": 3, "child_task_id": 4, "ip": "*******", "network_type": 1, "hostname": "hostname:taskid3+node1+area1+*******", "sn": "7795DB018A81413B887AA323557601D3", "asset_task_id": "78_3_4_*******", "created_at": "2024-11-27 22:31:48", "updated_at": "2024-11-27 22:31:48"}, {"id_comment": "fmt.Sprintf(\"%d_%d_%d_%s\", st.Task.Id, st.Node.Id, st.Node.AreaId, id)", "id": "31_2_1_*******", "area": 1, "source": 6, "node": 2, "task_id": 31, "child_task_id": 4, "ip": "*******", "network_type": 1, "hostname": "hostname:taskid31+node2+area1+*******", "sn": "7795DB018A81413B887AA323557601D3", "asset_task_id": "78_31_4_*******", "created_at": "2024-12-04 17:16:08", "updated_at": "2024-12-04 17:16:08"}], "expect_data": {"asset": [{"fid": "*******:1", "fid_hash": "6c06aec179a41116cee87f2977e40f1c", "area": 1, "process_ids": ["31_2_1_*******"], "source_ids": [6], "node_ids": [2], "asset_task_ids": ["78_31_4_*******"], "all_source_ids": [4, 5, 6], "all_node_ids": [1, 2, 3], "all_asset_task_ids": ["78_2_4_*******", "78_3_4_*******", "78_31_4_*******"], "all_process_ids": ["2_1_1_*******", "3_3_1_*******", "31_2_1_*******"], "ip": "*******", "ip_type": 1, "hostname": ["hostname:taskid31+node2+area1+*******"], "hostname_source": {"6-2": "hostname:taskid31+node2+area1+*******"}, "network_type": 1, "deleted_at": null, "purged_at": null, "created_at": "2024-11-28 17:16:08", "updated_at": "2024-11-28 17:16:08", "is_device_extracted": 2, "merge_count": 0}], "asset_record": [{"Asset": {"id": "120a3612d86d40368f1b875a13071185", "fid": "*******:1", "fid_hash": "6c06aec179a41116cee87f2977e40f1c", "area": 1, "process_ids": ["31_2_1_*******"], "source_ids": [6], "node_ids": [2], "asset_task_ids": ["78_31_4_*******"], "all_source_ids": [4, 5, 6], "all_node_ids": [1, 2, 3], "all_asset_task_ids": ["78_2_4_*******", "78_3_4_*******", "78_31_4_*******"], "all_process_ids": ["2_1_1_*******", "3_3_1_*******", "31_2_1_*******"], "ip": "*******", "hostname": ["hostname:taskid31+node2+area1+*******"], "hostname_source": {"5-1": "hostname:taskid3+node1+area1+*******", "6-2": "hostname:taskid31+node2+area1+*******"}, "network_type": 1, "deleted_at": null, "purged_at": null, "created_at": "2024-11-29 11:05:13", "updated_at": "2024-11-29 11:05:13", "is_device_extracted": 2, "merge_count": 0, "tag": null}, "id": "26c145049a834dc8bc183cf3ddb90b69", "batch_no": "", "backup_mode": "full", "backup_time": "2024-11-29 11:05:13", "asset_id": "120a3612d86d40368f1b875a13071185", "ip": "*******", "area": 1}], "asset_merge_record": [{"asset_id": "120a3612d86d40368f1b875a13071185", "asset_record_id": "26c145049a834dc8bc183cf3ddb90b69", "area": 1, "ip": "*******", "status": 1, "message": "", "source_ids": [6], "node_ids": [2], "asset_task_ids": ["78_31_4_*******"], "strategies": [{"id": 11, "created_at": "2024-11-21 11:13:23", "updated_at": "2024-11-21 11:13:23", "business_type": "asset_merge", "field_name": "Business", "display_name": "业务系统", "source_priority": {"1": 1, "2": 1, "3": 1, "5": 1, "6": 1, "7": 1, "8": 1, "9": 1, "10": 1, "11": 1, "13": 1}, "untrusted_source": [], "version": 1732158803}, {"id": 24, "created_at": "2024-11-21 11:13:23", "updated_at": "2024-11-21 11:13:23", "business_type": "asset_merge", "field_name": "Status", "display_name": "资产状态", "source_priority": {"1": 2, "2": 3, "3": 6, "5": 7, "6": 5, "7": 1, "8": 9, "9": 11, "10": 4, "11": 8, "13": 10}, "untrusted_source": [], "version": 1732158803}], "created_at": "2024-11-29 11:05:13", "updated_at": null, "merge_mode": "auto", "batch_no": "", "asset_ids": null, "is_device_extracted": 2, "tigger_source_id": 6}]}}