//go:build ignore

package test

import (
	"context"
	"testing"
	"time"

	"github.com/likexian/gokit/assert"
	"github.com/olivere/elastic/v7"

	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/initialize/es"
	"fobrain/mergeService/model"
	"fobrain/models/elastic/assets"
	"fobrain/pkg/cfg"
	"fobrain/pkg/queue"
)

func TestIpMerge_1(t *testing.T) {
	// 关闭测试模式
	testcommon.SetTestEnv(false)
	// 初始化配置
	cfg.InitLoadCfg()

	// 清除es数据
	err := clearDataFromEs("process_asset", "asset", "asset_record", "asset_merge_record")
	if err != nil {
		t.<PERSON>rrorf("清除es数据失败. err: %v", err)
		return
	}

	// 初始化资产融合流
	amt := model.NewAssetMergeFlow()
	// 启动资产融合流
	amt.Merge()

	originalData, err := initDataToEsFromFile("./data/1-单数据源单节点单条数据.json", true)
	if err != nil {
		t.<PERSON>rrorf("初始化测试数据失败. err: %v", err)
	}
	if len(originalData.ProcessAssets) == 0 {
		t.Error("process_asset数据为空")
	} else {
		t.Log("process_asset数据初始化成功")
	}
	msgList, taskId, err := generateQueueData(originalData.ProcessAssets)
	if err != nil {
		t.Errorf("生成消息队列数据失败. err: %v", err)
	}
	count := queue.NewQueue(queue.QueueType_Redis).Push(cfg.LoadQueue().AssetMergeQueue, msgList)
	t.Logf("插入消息队列成功，共计%d条(含2条标识数据)数据,任务ID：%v", count, taskId)

	// 等待30s
	t.Log("等待30s")
	time.Sleep(30 * time.Second)

	t.Log("开始验证asset数据")
	// 获取es client
	elasticClient := es.GetEsClient()
	// 读取es数据
	expectAsset := originalData.ExpectData.AssetData[0]
	query := elastic.NewBoolQuery().Must(elastic.NewTermQuery("ip", expectAsset.Ip), elastic.NewTermQuery("area", expectAsset.Area))
	assetResult, err := elasticClient.Search().Index("asset").Query(query).Do(context.Background())
	if err != nil {
		t.Errorf("读取es数据失败. err: %v", err)
	}
	if assetResult.Hits.TotalHits.Value == 0 {
		//t.Errorf("读取es数据失败. err: %v", err)
	}
	assetData, err := assets.ConvertToAssetsModel(assetResult.Hits.Hits[0])
	if err != nil {
		t.Errorf("读取es数据失败. err: %v", err)
	}
	assert.Equal(t, expectAsset.ProcessIds, assetData.ProcessIds)
	assert.Equal(t, expectAsset.HostName, assetData.HostName)
	assert.Equal(t, expectAsset.Sn, assetData.Sn)
	assert.Equal(t, expectAsset.Mac, assetData.Mac)
	t.Log("验证asset数据成功")

	t.Log("开始验证asset_record数据")
	if len(originalData.ExpectData.AssetRecord) == 0 {
		t.Log("预期数据的asset_record数据为空,跳过验证asset_record数据")
	} else {
		// 获取asset_record数据
		query := elastic.NewBoolQuery().Must(elastic.NewTermQuery("asset_id", assetData.Id))
		assetRecordResult, err := elasticClient.Search().Index("asset_record").Query(query).Do(context.Background())
		if err != nil {
			t.Errorf("读取es数据(asset_record)失败. err: %v", err)
		}
		assetRecordData, err := assets.ConvertToAssetRecordModel(assetRecordResult.Hits.Hits[0])
		if err != nil {
			t.Errorf("解析es数据(asset_record)失败. err: %v", err)
		}
		expectAssetRecord := originalData.ExpectData.AssetRecord[0]
		assert.Equal(t, expectAssetRecord.Ip, assetRecordData.Ip)
		assert.Equal(t, expectAssetRecord.Area, assetRecordData.Area)
		assert.Equal(t, expectAssetRecord.BackupMode, assetRecordData.BackupMode)
		assert.NotNil(t, assetRecordData.BackupTime)
		t.Log("验证asset_record数据成功")
	}

	t.Log("开始验证asset_merge_record数据")
	if len(originalData.ExpectData.AssetMergeRecord) == 0 {
		t.Log("预期数据的asset_merge_record数据为空,跳过验证asset_merge_record数据")
	} else {
		// 获取asset_merge_record数据
		query := elastic.NewBoolQuery().Must(elastic.NewTermQuery("asset_id", assetData.Id))
		assetMergeRecordResult, err := elasticClient.Search().Index("asset_merge_record").Query(query).Do(context.Background())
		if err != nil {
			t.Errorf("读取es数据(asset_merge_record)失败. err: %v", err)
		}
		assetMergeRecordData, err := assets.ConvertToAssetMergeRecordModel(assetMergeRecordResult.Hits.Hits[0])
		if err != nil {
			t.Errorf("解析es数据(asset_merge_record)失败. err: %v", err)
		}
		expectAssetMergeRecord := originalData.ExpectData.AssetMergeRecord[0]
		assert.Equal(t, expectAssetMergeRecord.Area, assetMergeRecordData.Area)
		assert.Equal(t, expectAssetMergeRecord.Ip, assetMergeRecordData.Ip)
		assert.Equal(t, expectAssetMergeRecord.TiggerSourceId, assetMergeRecordData.TiggerSourceId)
		assert.Equal(t, expectAssetMergeRecord.MergeMode, assetMergeRecordData.MergeMode)
		t.Log("验证asset_merge_record数据成功")
	}
}
