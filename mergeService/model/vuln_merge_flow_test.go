package model

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"testing"
	"time"

	"github.com/agiledragon/gomonkey/v2"

	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/models/mysql/poc_settings"
	"fobrain/pkg/cfg"
	"fobrain/pkg/queue"

	"github.com/alicebob/miniredis/v2"
	redis2 "github.com/go-redis/redis/v8"
	"github.com/olivere/elastic/v7"
	"github.com/stretchr/testify/assert"

	"fobrain/fobrain/common/localtime"
	"fobrain/initialize/mysql"
	esmodel "fobrain/models/elastic/poc"
	"fobrain/models/mysql/threat_histories"
)

func TestVulnMerge(t *testing.T) {
	cfg.InitLoadCfg()
	s := miniredis.RunT(t)
	defer s.Close()

	client := redis2.NewClient(&redis2.Options{Addr: s.Addr()})
	rq := &queue.RedisQueue{Client: client}

	qcfg := cfg.LoadQueue()
	for i := 0; i < 10; i++ {
		rq.Push(qcfg.VulnMergeQueue, []map[string]interface{}{{fmt.Sprintf("testKey-%d", i): "testValue"}})
	}
}

func TestGetVulnRelationData_HappyPath(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	mockServer.Register("process_poc/_search", []*elastic.SearchHit{
		{
			Id:     "1",
			Source: []byte(`{"id": "1","is_poc":1, "area": 1, "url": "http://test.com/12345678901","cve": "CVE-2021-12345","cnvd": "CNVD-2021-12345","cnnvd": "CNNVD-2021-12345"}`),
		},
	})

	amf := &VulnMergeFlow{}
	condition := elastic.NewBoolQuery()
	data, total, err := amf.GetRelationData(context.Background(), condition)

	assert.NoError(t, err)
	assert.Equal(t, int64(1), total)
	assert.Len(t, data, 1)
	assert.Equal(t, "1", data[0].Id)
	assert.Equal(t, uint64(1), data[0].Area)
	assert.Equal(t, "http://test.com/12345678901", data[0].Url)
}

func TestGetVulnRelationData_Error(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	mockServer.Register("process_poc/_search", []*elastic.SearchHit{
		{
			Id:     "1",
			Source: []byte(`error json`),
		},
	})

	amf := &VulnMergeFlow{}
	condition := elastic.NewBoolQuery()
	data, total, err := amf.GetRelationData(context.Background(), condition)

	assert.Error(t, err)
	assert.Equal(t, int64(0), total)
	assert.Nil(t, data)
}

func TestGetVulnRelationData_EmptyResult(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	mockServer.Register("process_poc/_search", []*elastic.SearchHit{
		{
			Id:     "",
			Source: []byte(``),
		},
	})

	amf := &VulnMergeFlow{}
	condition := elastic.NewBoolQuery()
	data, total, err := amf.GetRelationData(context.Background(), condition)

	assert.NoError(t, err)
	assert.Equal(t, int64(0), total)
	assert.Len(t, data, 0)
}

// 添加一个辅助函数来计算 sync.Map 的大小
func mapLen(m *sync.Map) int {
	count := 0
	m.Range(func(_, _ interface{}) bool {
		count++
		return true
	})
	return count
}

func TestBatchCalculateRiskLevel_Normal(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	// 设置 Redis mock
	s, _ := miniredis.Run()
	defer s.Close()
	client := redis2.NewClient(&redis2.Options{Addr: s.Addr()})
	testcommon.SetRedisClient(client)

	// 模拟 PocSettingObj
	settings := &poc_settings.PocSettingObj{
		PocLevelVeryHigh:         2,
		PocLevelHigh:             2,
		PocLevelMedium:           1,
		PocLevelLow:              1,
		PocLevelEnable:           1,
		AssetSameBusinessEnable:  1,
		AssetSameBusinessThreeGe: 3,
		AssetSameBusinessTwo:     2,
		AssetSameBusinessOne:     1,
		VulRepairPriorityP0:      "901,-1",
		VulRepairPriorityP1:      "601,900",
		VulRepairPriorityP2:      "301,600",
		VulRepairPriorityP3:      "1,300",
	}
	settingsJSON, _ := json.Marshal(settings)

	// 模拟 Redis 响应
	s.Set("cache:vuln:poc_setting_json", string(settingsJSON))

	// 模拟 ES 搜索响应
	mockServer.Register("poc/_search", &elastic.SearchResult{
		Hits: &elastic.SearchHits{
			Hits: []*elastic.SearchHit{
				{
					Id: "1",
					Source: []byte(`{
						"id": "1",
						"level": 4,
						"has_poc": 1,
						"has_exp": 1,
						"network_type": 2,
						"business_name_tmp": ["business1"],
						"business": [{"importance": 3}]
					}`),
				},
			},
		},
		Aggregations: elastic.Aggregations{
			"business_count": json.RawMessage(`{
				"buckets": [
					{
						"key": "business1",
						"doc_count": 3
					}
				]
			}`),
		},
	})

	// 模拟批量更新响应
	mockServer.Register("_bulk", &elastic.BulkResponse{
		Items: []map[string]*elastic.BulkResponseItem{
			{
				"update": {
					Status: 200,
				},
			},
		},
	})

	// 准备测试数据
	pocIds.Store("1", "1")
	// 使用 gomonkey 模拟 time.Sleep
	patches := gomonkey.ApplyFunc(time.Sleep, func(d time.Duration) {})
	defer patches.Reset()
	// 执行测试
	BatchCalculateRiskLevel(false)

	// 验证
	assert.Equal(t, 0, mapLen(&pocIds), "pocIds 应该被清空")
}

func TestBatchCalculateRiskLevel_RedisError(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	// 设置 Redis mock
	s := miniredis.RunT(t)
	defer s.Close()
	client := redis2.NewClient(&redis2.Options{Addr: s.Addr()})
	testcommon.SetRedisClient(client)

	// 模拟 Redis 错误
	s.Del("cache:vuln:poc_setting_json")

	// 准备测试数据
	pocIds.Store("1", "1")
	// 使用 gomonkey 模拟 time.Sleep
	patches := gomonkey.ApplyFunc(time.Sleep, func(d time.Duration) {})
	defer patches.Reset()
	// 执行测试
	BatchCalculateRiskLevel(false)

	// 验证
	assert.Equal(t, 0, mapLen(&pocIds), "pocIds 应该被清空")
}

func TestBatchCalculateRiskLevel_ESSearchError(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	// 设置 Redis mock
	s := miniredis.RunT(t)
	defer s.Close()
	client := redis2.NewClient(&redis2.Options{Addr: s.Addr()})
	testcommon.SetRedisClient(client)

	// 模拟 PocSettingObj
	settings := &poc_settings.PocSettingObj{
		VulRepairPriorityP0: "901,-1",
		VulRepairPriorityP1: "601,900",
		VulRepairPriorityP2: "301,600",
		VulRepairPriorityP3: "1,300",
	}
	settingsJSON, _ := json.Marshal(settings)
	s.Set("cache:vuln:poc_setting_json", string(settingsJSON))

	// 模拟 ES 搜索错误
	mockServer.Register("poc/_search", &elastic.Error{
		Status: 500,
		Details: &elastic.ErrorDetails{
			Type:   "search_error",
			Reason: "search error",
		},
	})

	// 使用 gomonkey 模拟 time.Sleep
	patches := gomonkey.ApplyFunc(time.Sleep, func(d time.Duration) {})
	defer patches.Reset()

	// 准备测试数据
	pocIds.Store("1", "1")

	// 执行测试
	BatchCalculateRiskLevel(false)

	// 验证
	assert.Equal(t, 0, mapLen(&pocIds), "pocIds 应该被清空")
}

func TestBatchCalculateRiskLevel_ESAggregationError(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	// 设置 Redis mock
	s := miniredis.RunT(t)
	defer s.Close()
	client := redis2.NewClient(&redis2.Options{Addr: s.Addr()})
	testcommon.SetRedisClient(client)

	// 模拟 PocSettingObj
	settings := &poc_settings.PocSettingObj{
		VulRepairPriorityP0: "901,-1",
		VulRepairPriorityP1: "601,900",
		VulRepairPriorityP2: "301,600",
		VulRepairPriorityP3: "1,300",
	}
	settingsJSON, _ := json.Marshal(settings)

	// 模拟 Redis 响应
	s.Set("cache:vuln:poc_setting_json", string(settingsJSON))

	// 模拟 ES 搜索错误响应
	mockServer.Register("poc/_search", &elastic.Error{
		Status: 500,
		Details: &elastic.ErrorDetails{
			Type:   "aggregation_error",
			Reason: "aggregation error",
		},
	})

	// 准备测试数据
	pocIds.Store("1", "1")
	// 使用 gomonkey 模拟 time.Sleep
	patches := gomonkey.ApplyFunc(time.Sleep, func(d time.Duration) {})
	defer patches.Reset()
	// 执行测试
	BatchCalculateRiskLevel(false)

	// 验证
	assert.Equal(t, 0, mapLen(&pocIds), "pocIds 应该被清空")
}

func TestBatchCalculateRiskLevel_ESBulkError(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	// 设置 Redis mock
	s := miniredis.RunT(t)
	defer s.Close()
	client := redis2.NewClient(&redis2.Options{Addr: s.Addr()})
	testcommon.SetRedisClient(client)

	// 模拟 PocSettingObj
	settings := &poc_settings.PocSettingObj{
		VulRepairPriorityP0: "901,-1",
		VulRepairPriorityP1: "601,900",
		VulRepairPriorityP2: "301,600",
		VulRepairPriorityP3: "1,300",
	}
	settingsJSON, _ := json.Marshal(settings)

	// 模拟 Redis 响应
	s.Set("cache:vuln:poc_setting_json", string(settingsJSON))

	// 模拟 ES 搜索响应
	mockServer.Register("poc/_search", &elastic.SearchResult{
		Hits: &elastic.SearchHits{
			Hits: []*elastic.SearchHit{
				{
					Id: "1",
					Source: []byte(`{
						"id": "1",
						"level": 4,
						"has_poc": 1,
						"has_exp": 1,
						"network_type": 2,
						"business_name_tmp": ["business1"],
						"business": [{"importance": 3}]
					}`),
				},
			},
		},
		Aggregations: elastic.Aggregations{
			"business_count": json.RawMessage(`{
				"buckets": [
					{
						"key": "business1",
						"doc_count": 3
					}
				]
			}`),
		},
	})

	// 模拟批量更新错误
	mockServer.Register("poc/_bulk", &elastic.BulkResponse{
		Items: []map[string]*elastic.BulkResponseItem{
			{
				"update": {
					Error: &elastic.ErrorDetails{
						Type:   "bulk_error",
						Reason: "bulk error",
					},
				},
			},
		},
	})

	// 准备测试数据
	pocIds.Store("1", "1")
	// 使用 gomonkey 模拟 time.Sleep
	patches := gomonkey.ApplyFunc(time.Sleep, func(d time.Duration) {})
	defer patches.Reset()
	// 执行测试
	BatchCalculateRiskLevel(false)

	// 验证
	assert.Equal(t, 0, mapLen(&pocIds), "pocIds 应该被清空")
}

func TestMergeVuln_GetOriginalLatestCreatedAt(t *testing.T) {
	tests := []struct {
		name         string
		originalData []*esmodel.ProcessPoc
		want         *localtime.Time
	}{
		{
			name:         "空数据返回默认时间",
			originalData: []*esmodel.ProcessPoc{},
			want:         localtime.NewLocalTime(time.Date(2025, 1, 1, 0, 0, 0, 0, time.Local)),
		},
		{
			name: "单条数据返回该数据时间",
			originalData: []*esmodel.ProcessPoc{
				{
					CreatedAt: localtime.NewLocalTime(time.Date(2024, 3, 15, 10, 30, 0, 0, time.Local)),
				},
			},
			want: localtime.NewLocalTime(time.Date(2024, 3, 15, 10, 30, 0, 0, time.Local)),
		},
		{
			name: "多条数据返回最新时间",
			originalData: []*esmodel.ProcessPoc{
				{
					CreatedAt: localtime.NewLocalTime(time.Date(2024, 3, 15, 10, 30, 0, 0, time.Local)),
				},
				{
					CreatedAt: localtime.NewLocalTime(time.Date(2024, 3, 16, 10, 30, 0, 0, time.Local)),
				},
				{
					CreatedAt: localtime.NewLocalTime(time.Date(2024, 3, 14, 10, 30, 0, 0, time.Local)),
				},
			},
			want: localtime.NewLocalTime(time.Date(2024, 3, 16, 10, 30, 0, 0, time.Local)),
		},
		{
			name: "所有时间都早于默认时间",
			originalData: []*esmodel.ProcessPoc{
				{
					CreatedAt: localtime.NewLocalTime(time.Date(2023, 3, 15, 10, 30, 0, 0, time.Local)),
				},
				{
					CreatedAt: localtime.NewLocalTime(time.Date(2023, 3, 16, 10, 30, 0, 0, time.Local)),
				},
			},
			want: localtime.NewLocalTime(time.Date(2023, 3, 16, 10, 30, 0, 0, time.Local)),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mv := &MergeVuln{
				OriginalData: tt.originalData,
			}
			got := mv.GetOriginalLatestCreatedAt()
			assert.Equal(t, tt.want, got)
		})
	}
}

func TestMergeVuln_GetStatusChangeTime(t *testing.T) {
	historyTime := localtime.NewLocalTime(time.Date(2024, 3, 15, 10, 30, 0, 0, time.Local))

	tests := []struct {
		name     string
		msgData  *esmodel.Poc
		mockList []threat_histories.ThreatHistory
		mockErr  error
		want     *localtime.Time
	}{
		{
			name: "状态变更时间为空时返回历史记录时间",
			msgData: &esmodel.Poc{
				Id:               "test1",
				StatusChangeTime: nil,
			},
			mockList: []threat_histories.ThreatHistory{
				{
					BaseModel: mysql.BaseModel{
						CreatedAt: *historyTime,
					},
				},
			},
			mockErr: nil,
			want:    historyTime,
		},
		{
			name: "状态变更时间早于2025年时返回历史记录时间",
			msgData: &esmodel.Poc{
				Id:               "test2",
				StatusChangeTime: localtime.NewLocalTime(time.Date(2024, 1, 1, 0, 0, 0, 0, time.Local)),
			},
			mockList: []threat_histories.ThreatHistory{
				{
					BaseModel: mysql.BaseModel{
						CreatedAt: *historyTime,
					},
				},
			},
			mockErr: nil,
			want:    historyTime,
		},
		{
			name: "获取历史记录失败时返回nil",
			msgData: &esmodel.Poc{
				Id:               "test3",
				StatusChangeTime: nil,
			},
			mockList: nil,
			mockErr:  assert.AnError,
			want:     nil,
		},
		{
			name: "没有历史记录时返回nil",
			msgData: &esmodel.Poc{
				Id:               "test4",
				StatusChangeTime: nil,
			},
			mockList: []threat_histories.ThreatHistory{},
			mockErr:  nil,
			want:     nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mv := &MergeVuln{
				MsgData: tt.msgData,
			}

			// Mock ThreatHistoryModel.List
			patches := gomonkey.ApplyMethod(threat_histories.NewThreatHistoryModel(), "List",
				func(_ *threat_histories.ThreatHistory, _, _ int, _ ...mysql.HandleFunc) ([]threat_histories.ThreatHistory, int64, error) {
					return tt.mockList, int64(len(tt.mockList)), tt.mockErr
				})
			defer patches.Reset()

			got := mv.GetStatusChangeTime()
			if tt.want == nil {
				assert.Nil(t, got)
			} else {
				assert.Equal(t, tt.want, got)
			}
		})
	}
}

func TestUpdateVulnStatus(t *testing.T) {
	tests := []struct {
		name                    string
		msgData                 *esmodel.Poc
		pocData                 *esmodel.Poc // 添加当前Poc状态
		originalLatestCreatedAt *localtime.Time
		statusChangeTime        *localtime.Time
		wantStatus              int
	}{
		{
			name: "复测通过状态再次上报变为复现",
			msgData: &esmodel.Poc{
				Status: esmodel.PocStatusOfRepaired,
			},
			pocData: &esmodel.Poc{
				Status: esmodel.PocStatusOfNew, // 当前状态不是复测通过
			},
			originalLatestCreatedAt: localtime.NewLocalTime(time.Date(2024, 3, 16, 10, 30, 0, 0, time.Local)),
			statusChangeTime:        localtime.NewLocalTime(time.Date(2024, 3, 15, 10, 30, 0, 0, time.Local)),
			wantStatus:              esmodel.PocStatusOfStillExist,
		},
		{
			name: "复测通过的数据重复上报状态不变",
			msgData: &esmodel.Poc{
				Status: esmodel.PocStatusOfRepaired, // 新数据状态是复测通过
			},
			pocData: &esmodel.Poc{
				Status: esmodel.PocStatusOfRepaired, // 当前状态也是复测通过
			},
			originalLatestCreatedAt: localtime.NewLocalTime(time.Date(2024, 3, 16, 10, 30, 0, 0, time.Local)),
			statusChangeTime:        localtime.NewLocalTime(time.Date(2024, 3, 15, 10, 30, 0, 0, time.Local)),
			wantStatus:              esmodel.PocStatusOfRepaired, // 状态保持不变
		},
		{
			name: "其他状态更新为最新状态",
			msgData: &esmodel.Poc{
				Status: esmodel.PocStatusOfReRepairing,
			},
			pocData: &esmodel.Poc{
				Status: esmodel.PocStatusOfNew,
			},
			originalLatestCreatedAt: localtime.NewLocalTime(time.Date(2024, 3, 16, 10, 30, 0, 0, time.Local)),
			statusChangeTime:        localtime.NewLocalTime(time.Date(2024, 3, 15, 10, 30, 0, 0, time.Local)),
			wantStatus:              esmodel.PocStatusOfReRepairing,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mv := &MergeVuln{
				MsgData: tt.msgData,
				Poc:     tt.pocData, // 使用测试提供的Poc状态
			}

			// Mock GetStatusChangeTime
			patches := gomonkey.ApplyMethod(mv, "GetStatusChangeTime",
				func(_ *MergeVuln) *localtime.Time {
					return tt.statusChangeTime
				})
			defer patches.Reset()

			// Mock GetOriginalLatestCreatedAt
			patches.ApplyMethod(mv, "GetOriginalLatestCreatedAt",
				func(_ *MergeVuln) *localtime.Time {
					return tt.originalLatestCreatedAt
				})

			// Mock CreateThreatHistory
			patches.ApplyMethod(threat_histories.NewThreatHistoryModel(), "CreateThreatHistory",
				func(_ *threat_histories.ThreatHistory, _, _ string, _, _ int, _ []uint64) error {
					return nil
				})

			updateVulnStatus(mv)

			if tt.statusChangeTime.After(*tt.originalLatestCreatedAt) {
				assert.Equal(t, tt.msgData.Status, mv.Poc.Status, "当状态变更时间更晚时，应该使用新数据状态")
			} else {
				assert.Equal(t, tt.wantStatus, mv.Poc.Status, "状态应该被更新为期望值")
				assert.NotNil(t, mv.Poc.StatusChangeTime, "状态变更时间应该被设置")
			}
		})
	}
}
