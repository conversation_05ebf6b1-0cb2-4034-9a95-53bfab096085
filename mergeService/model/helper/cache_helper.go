package merge_helper

import (
	"context"
	"encoding/json"
	"fmt"
	"slices"
	"strconv"
	"strings"
	"sync"
	"time"

	"fobrain/initialize/es"
	"fobrain/initialize/redis"
	logs "fobrain/mergeService/utils/log"
	esmodel "fobrain/models/elastic/assets"
	esmodel_device "fobrain/models/elastic/device"
	"fobrain/models/elastic/staff"
	"fobrain/models/mysql/personnel_departments"
	redis_helper "fobrain/models/redis"
	"fobrain/pkg/utils"

	"github.com/olivere/elastic/v7"
	"go-micro.dev/v4/logger"

	goRedis "github.com/go-redis/redis/v8"
	"github.com/pkg/errors"
)

// AssetMergeTaskStartPrepareCache 资产数据融合任务开始事件-准备工作
// 执行时机：资产数据融合任务开始
func AssetMergeTaskStartPrepareCache(nodeTaskInfo map[uint64][]uint64, sourceId, nodeId uint64, taskType, taskId, assetTaskId string) {
	defer func() {
		logger := logs.GetLogger("asset")
		if err := recover(); err != nil {
			logger.Errorf("资产数据融合任务开始事件-准备工作发生错误. err: %v", err)
		}
	}()
	logger := logs.GetLogger("asset")
	redisClient := redis.GetRedisClient()
	var wg sync.WaitGroup

	// 缓存部门数据
	logger.Infof("缓存部门数据")
	wg.Add(1)
	go func() {
		err := CacheDepartmentData(redisClient)
		if err != nil {
			logger.Errorf("缓存部门数据失败. err: %v", err)
		}
		wg.Done()
	}()

	// 缓存黑名单数据
	logger.Infof("缓存黑名单数据")
	wg.Add(1)
	go func() {
		err := CacheBlackSource(redisClient)
		if err != nil {
			logger.Errorf("缓存黑名单数据失败. err: %v", err)
			redisClient.Set(context.Background(), redis_helper.BlackSourceCacheStateKey(), "0", 24*time.Hour)
		} else {
			redisClient.Set(context.Background(), redis_helper.BlackSourceCacheStateKey(), "1", 24*time.Hour)
		}
		wg.Done()
	}()
	// 缓存人员数据
	logger.Infof("缓存人员数据")
	wg.Add(1)
	go func() {
		err := staff.NewStaff().CacheAllStaff(true)
		if err != nil {
			logger.Errorf("缓存人员数据失败. err: %v", err)
		} else {
		}
		wg.Done()
	}()

	// 等待所有缓存任务完成
	wg.Wait()
}

// CacheDepartmentData 缓存部门数据
func CacheDepartmentData(redisClient *goRedis.Client) error {
	logger := logs.GetLogger("asset")
	// 清除redis中现有的部门数据
	err := redis_helper.DeleteKeysByPrefixWithScan(context.Background(), redisClient, redis_helper.DepartmentKey("*"))
	if err != nil {
		logger.Errorf("删除部门数据失败. err: %v", err)
		return err
	}
	allDepartments, _, err := personnel_departments.NewPersonnelDepartmentsModel().List(0, 0)
	if err != nil {
		logger.Warnf("获取所有部门信息失败. err: %v", err)
		return nil
	}
	ctx := context.Background()
	pipe := redisClient.Pipeline()
	// 将部门信息转换为map, key为部门id
	allDepartmentsMap := make(map[uint64]*personnel_departments.PersonnelDepartments)
	for _, department := range allDepartments {
		allDepartmentsMap[department.Id] = department
		// 缓存单个部门数据
		key := redis_helper.DepartmentKey(strconv.FormatUint(department.Id, 10))
		data, err := json.Marshal(department)
		if err != nil {
			logger.Errorf("序列化部门数据失败. key: %s, err: %v", key, err)
			continue
		}
		err = pipe.Set(ctx, key, data, 24*time.Hour).Err()
		if err != nil {
			logger.Errorf("缓存部门数据到redis失败. key: %s, err: %v", key, err)
			continue
		}
	}
	_, err = pipe.Exec(ctx)
	if err != nil {
		logger.Errorf("缓存单个部门数据到redis失败. err: %v", err)
	}
	// 将所有部门信息保存到redis
	key := redis_helper.DepartmentKey("all")
	data, err := json.Marshal(allDepartmentsMap)
	if err != nil {
		logger.Errorf("序列化部门数据失败. key: %s, err: %v", key, err)
		return err
	}
	err = redisClient.Set(ctx, key, data, 24*time.Hour).Err()
	if err != nil {
		logger.Errorf("缓存部门数据到redis失败. key: %s, err: %v", key, err)
		return err
	}
	logger.Debugf("缓存部门数据完成, 共 %d 条部门数据", len(allDepartments))
	return nil
}
func CacheBlackSource(redisClient *goRedis.Client) error {
	// 清除redis中现有的部门数据
	err := redis_helper.DeleteKeysByPrefixWithScan(context.Background(), redisClient, redis_helper.BlackSourceKey("*"))
	if err != nil {
		return errors.Wrap(err, "清除redis中现有的黑名单数据失败")
	}
	// 根据 ip+area 查询黑名单
	query := elastic.NewBoolQuery()
	query.Must(elastic.NewExistsQuery("deleted_at"))
	dataList, err := es.All[esmodel.Assets](1000, query, nil, "ip", "area", "source_ids")
	if err != nil {
		return err
	}
	blackSource := make(map[string][]uint64)
	for _, item := range dataList {
		key := fmt.Sprintf("%s_%d", item.Ip, item.Area)
		if _, ok := blackSource[key]; !ok {
			blackSource[key] = make([]uint64, 0)
		}
		blackSource[key] = append(blackSource[key], item.SourceIds...)
	}
	ctx := context.Background()
	for key, sourceIds := range blackSource {
		redisKey := redis_helper.BlackSourceKey(key)
		data, err := json.Marshal(utils.ListDistinctNonZero(sourceIds))
		if err != nil {
			logs.GetLogger("asset").Errorf("序列化黑名单数据失败. key: %s, err: %v", key, err)
			return err
		}
		err = redisClient.Set(ctx, redisKey, data, 24*time.Hour).Err()
		if err != nil {
			logs.GetLogger("asset").Errorf("保存黑名单数据到redis失败. key: %s, err: %v", key, err)
			return err
		}
	}
	return nil
}

// CacheAssetsProcessData 缓存资产数据 将过程表的数据根据 ip+area 作为key，id作为value，保存到redis, 用于查询融合关联数据
func CacheAssetsProcessData(ipList []string) error {
	redisClient := redis.GetRedisClient()
	l := logs.GetLogger("asset")
	// 清除redis中现有的资产数据
	err := redisClient.Del(context.Background(), redis_helper.AssetsProcessKey("ip_area_map_ids")).Err()
	if err != nil {
		l.Errorf("删除资产数据失败. err: %v", err)
		return err
	}
	var dataList []*esmodel.ProcessAssets
	// 调用es 查询 process_assets 数据，按created_at, id desc排序，遍历数据，按ip area source 去重,即同数据源，相同的Ip+area，只有最新上报的数据参与融合，重复的跳过，存储[ip+area]=> id list
	var query elastic.Query
	if len(ipList) > 0 {
		// ipList可能太多，会超出es的限制，所以需要分页查询， ipList trunk 分组 一次1万
		ipChunk := slices.Chunk(ipList, 10000)
		for chunk := range ipChunk {
			var ips []string
			var areas []interface{}
			for _, ip := range chunk {
				arr := strings.Split(ip, "_")
				ips = append(ips, arr[0])
				areas = append(areas, arr[1])
			}
			query = elastic.NewBoolQuery().Must(elastic.NewTermsQueryFromStrings("ip", ips...)).Must(elastic.NewTermsQuery("area", areas...))
			rows, err := es.All[esmodel.ProcessAssets](1000, query, []elastic.Sorter{elastic.NewFieldSort("child_task_id").Desc(), elastic.NewFieldSort("id").Desc()}, "ip", "area", "source", "id")
			if err != nil {
				return err
			}
			dataList = append(dataList, rows...)
		}
	} else {
		query = elastic.NewMatchAllQuery()
		dataList, err = es.All[esmodel.ProcessAssets](1000, query, []elastic.Sorter{elastic.NewFieldSort("child_task_id").Desc(), elastic.NewFieldSort("id").Desc()}, "ip", "area", "source", "id")
		if err != nil {
			return err
		}
	}

	processAssetsMap := make(map[string][]string)
	distinctMap := make(map[string]bool)
	for _, item := range dataList {
		keyDistinc := fmt.Sprintf("%s_%d_%d", item.Ip, item.Area, item.Source)
		if _, ok := distinctMap[keyDistinc]; !ok {
			distinctMap[keyDistinc] = true
			key := fmt.Sprintf("%s_%d", item.Ip, item.Area)
			if _, ok := processAssetsMap[key]; !ok {
				processAssetsMap[key] = make([]string, 0)
			}
			processAssetsMap[key] = append(processAssetsMap[key], item.Id)
		}
	}
	ctx := context.Background()
	var mapSet = make(map[string]string)
	for key, ids := range processAssetsMap {
		data, err := json.Marshal(ids)
		if err != nil {
			l.Errorf("序列化资产数据失败. key: %s, err: %v", key, err)
			return err
		}
		mapSet[key] = string(data)
		if len(mapSet) > 5000 {
			err = redisClient.HSet(ctx, redis_helper.AssetsProcessKey("ip_area_map_ids"), mapSet).Err()
			if err != nil {
				l.Errorf("保存资产数据到redis失败.  err: %v", err)
				return err
			}
			mapSet = make(map[string]string)
		}
	}
	if len(mapSet) > 0 {
		err = redisClient.HSet(ctx, redis_helper.AssetsProcessKey("ip_area_map_ids"), mapSet).Err()
		if err != nil {
			l.Errorf("保存资产数据到redis失败.  err: %v", err)
			return err
		}
	}
	return nil
}

// CacheDeviceUniqueKeyToID 查询device索引，获取fid=>id的映射关系
func CacheDeviceUniqueKeyToID(redisClient *goRedis.Client) error {
	devices, err := es.All[esmodel_device.Device](1000, elastic.NewMatchAllQuery(), nil, "id", "fid")
	if err != nil {
		return err
	}
	uniqueKeyToIDMap := make(map[string]string)
	for _, device := range devices {
		uniqueKeyToIDMap[device.Fid] = device.Id
	}
	ctx := context.Background()
	key := redis_helper.DeviceUniqueKeyToIDKey()
	err = redisClient.Del(ctx, key).Err()
	if err != nil {
		return err
	}
	err = redisClient.HSet(ctx, key, uniqueKeyToIDMap).Err()
	if err != nil {
		return err
	}
	return nil
}

// CacheProcessDeviceUniqueKeyToID 查询process_device索引，获取unique_key=>id的映射关系
func CacheProcessDeviceUniqueKeyToID(redisClient *goRedis.Client) error {
	processDevices, err := es.All[esmodel_device.ProcessDevice](1000, elastic.NewMatchAllQuery(), nil, "id", "unique_key")
	if err != nil {
		return err
	}
	uniqueKeyToIDMap := make(map[string][]string)
	for _, device := range processDevices {
		if _, ok := uniqueKeyToIDMap[device.UniqueKey]; !ok {
			uniqueKeyToIDMap[device.UniqueKey] = make([]string, 0)
		}
		uniqueKeyToIDMap[device.UniqueKey] = append(uniqueKeyToIDMap[device.UniqueKey], device.Id)
	}
	ctx := context.Background()
	cacheKey := redis_helper.ProcessDeviceUniqueKeyToIDKey()
	err = redisClient.Del(ctx, cacheKey).Err()
	if err != nil {
		return err
	}
	var mapSet = make(map[string]string)
	for key, ids := range uniqueKeyToIDMap {
		data, err := json.Marshal(ids)
		if err != nil {
			logger.Errorf("序列化资产数据失败. key: %s, err: %v", key, err)
			return err
		}
		mapSet[key] = string(data)
		if len(mapSet) > 5000 {
			err = redisClient.HSet(ctx, cacheKey, mapSet).Err()
			if err != nil {
				return err
			}
			mapSet = make(map[string]string)
		}
	}
	if len(mapSet) > 0 {
		err = redisClient.HSet(ctx, cacheKey, mapSet).Err()
		if err != nil {
			return err
		}
	}
	return nil
}
