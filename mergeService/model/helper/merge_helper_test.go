package merge_helper

import (
	"context"
	"encoding/json"
	"errors"
	"fobrain/fobrain/common/localtime"
	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/initialize/mysql"
	"fobrain/initialize/redis"
	logs "fobrain/mergeService/utils/log"
	"fobrain/models/mysql/strategy"
	redis_helper "fobrain/models/redis"
	"net/http"
	"testing"
	"time"

	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/data_sync_child_task"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/alicebob/miniredis/v2"
	redis2 "github.com/go-redis/redis/v8"
	"github.com/stretchr/testify/assert"
)

func TestGetMergeRules(t *testing.T) {
	// Setup mock redis
	s, err := miniredis.Run()
	if err != nil {
		t.Fatal(err)
	}
	defer s.<PERSON>()

	client := redis2.NewClient(&redis2.Options{Addr: s.Addr()})
	testcommon.SetRedisClient(client)

	assetMock := gomonkey.ApplyMethodReturn(strategy.NewStrategyModel(), "ListAllDistinct", []*strategy.Strategy{{FieldName: "Rule1"}, {FieldName: "Rule2"}}, nil)
	defer assetMock.Reset()

	// Setup mock strategies
	mockAssetStrategies := []*strategy.Strategy{
		{FieldName: "Rule1"},
		{FieldName: "Rule2"},
	}
	mockDeviceStrategies := []*strategy.Strategy{
		{FieldName: "Rule3"},
		{FieldName: "Rule4"},
	}
	mockStrategiesJSON, _ := json.Marshal(mockAssetStrategies)
	key := redis_helper.AssetMergeRuleKey()
	redis.GetRedisClient().Set(context.Background(), key, string(mockStrategiesJSON), time.Duration(30*time.Second))

	strategies, err := GetMergeRules(strategy.BusinessType_AssetMerge, logs.GetLogger("test"))
	assert.NoError(t, err)
	assert.Equal(t, len(mockAssetStrategies), len(strategies))
	assert.Equal(t, mockAssetStrategies[0].FieldName, strategies[0].FieldName)
	assert.Equal(t, mockAssetStrategies[1].FieldName, strategies[1].FieldName)

	// 测试 device_merge
	{
		assetMock.Reset()
		deviceMock := gomonkey.ApplyMethodReturn(strategy.NewStrategyModel(), "ListAllDistinct", []*strategy.Strategy{{FieldName: "Rule3"}, {FieldName: "Rule4"}}, nil)
		defer deviceMock.Reset()
		strategies, err = GetMergeRules(strategy.BusinessType_DeviceMerge, logs.GetLogger("test"))
		assert.NoError(t, err)
		assert.Equal(t, len(mockDeviceStrategies), len(strategies))
		assert.Equal(t, mockDeviceStrategies[0].FieldName, strategies[0].FieldName)
		assert.Equal(t, mockDeviceStrategies[1].FieldName, strategies[1].FieldName)
		// 从redis获取设备融合规则
		key = redis_helper.DeviceMergeRuleKey()
		redisValue, err := client.Get(context.Background(), key).Result()
		assert.NoError(t, err)
		err = json.Unmarshal([]byte(redisValue), &strategies)
		assert.NoError(t, err)
		assert.Equal(t, len(mockDeviceStrategies), len(strategies))
		assert.Equal(t, mockDeviceStrategies[0].FieldName, strategies[0].FieldName)
		assert.Equal(t, mockDeviceStrategies[1].FieldName, strategies[1].FieldName)
	}
}

func TestAnyToString(t *testing.T) {
	tests := []struct {
		name     string
		input    any
		expected string
	}{
		{
			name:     "nil值",
			input:    nil,
			expected: "",
		},
		{
			name:     "字符串值",
			input:    "测试字符串",
			expected: "测试字符串",
		},
		{
			name:     "整数值",
			input:    123,
			expected: "123",
		},
		{
			name:     "浮点数值",
			input:    123.456,
			expected: "123.456",
		},
		{
			name:     "布尔值true",
			input:    true,
			expected: "true",
		},
		{
			name:     "布尔值false",
			input:    false,
			expected: "false",
		},
		{
			name:     "切片值",
			input:    []string{"a", "b", "c"},
			expected: "[a b c]",
		},
		{
			name:     "结构体值",
			input:    struct{ Name string }{"测试"},
			expected: "{测试}",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := AnyToString(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestAnyToTime(t *testing.T) {
	// 创建一个localtime.Time实例用于测试
	now := time.Now()
	localTimeNow := localtime.Time(now)

	tests := []struct {
		name     string
		input    any
		expected *localtime.Time
	}{
		{
			name:     "nil值",
			input:    nil,
			expected: nil,
		},
		{
			name:     "非localtime.Time类型",
			input:    "2023-01-01",
			expected: nil,
		},
		{
			name:     "非localtime.Time类型的时间",
			input:    now,
			expected: nil,
		},
		{
			name:     "localtime.Time指针类型",
			input:    &localTimeNow,
			expected: &localTimeNow,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := AnyToTime(tt.input)
			if tt.expected == nil {
				assert.Nil(t, result)
			} else {
				assert.Equal(t, tt.expected, result)
			}
		})
	}
}

func TestAnyToStringSlice(t *testing.T) {
	tests := []struct {
		name     string
		input    any
		expected []string
	}{
		{
			name:     "nil值",
			input:    nil,
			expected: nil,
		},
		{
			name:     "字符串切片",
			input:    []string{"a", "b", "c"},
			expected: []string{"a", "b", "c"},
		},
		{
			name:     "逗号分隔的字符串",
			input:    "a,b,c",
			expected: []string{"a", "b", "c"},
		},
		{
			name:     "空字符串",
			input:    "",
			expected: []string{""},
		},
		{
			name:     "any切片",
			input:    []any{1, "b", 3.14},
			expected: []string{"1", "b", "3.14"},
		},
		{
			name:     "整数",
			input:    123,
			expected: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := AnyToStringSlice(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestAnyToInt(t *testing.T) {
	tests := []struct {
		name     string
		input    any
		expected int
	}{
		{
			name:     "nil值",
			input:    nil,
			expected: 0,
		},
		{
			name:     "整数",
			input:    123,
			expected: 123,
		},
		{
			name:     "浮点数",
			input:    123.456,
			expected: 123,
		},
		{
			name:     "有效数字字符串",
			input:    "123",
			expected: 123,
		},
		{
			name:     "无效数字字符串",
			input:    "abc",
			expected: 0,
		},
		{
			name:     "空字符串",
			input:    "",
			expected: 0,
		},
		{
			name:     "布尔值",
			input:    true,
			expected: 0,
		},
		{
			name:     "切片",
			input:    []int{1, 2, 3},
			expected: 0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := AnyToInt(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestClearProcessData(t *testing.T) {
	// 创建模拟的 Elasticsearch 客户端
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	// 测试正常情况
	t.Run("正常情况", func(t *testing.T) {
		// 模拟 List 方法返回有效的任务列表
		patches := gomonkey.ApplyMethodReturn(data_sync_child_task.NewDataSyncChildTaskModel(), "List", []*data_sync_child_task.DataSyncChildTask{
			{
				BaseModel: mysql.BaseModel{Id: 1},
				Type:      1,
			},
			{
				BaseModel: mysql.BaseModel{Id: 2},
				Type:      1,
			},
			{
				BaseModel: mysql.BaseModel{Id: 3},
				Type:      1,
			},
			{
				BaseModel: mysql.BaseModel{Id: 4},
				Type:      1,
			},
		}, int64(4), nil)
		defer patches.Reset()

		// 模拟 Elasticsearch 删除操作
		mockServer.RegisterHandler("/_delete_by_query", func(w http.ResponseWriter, r *http.Request) {
			w.WriteHeader(http.StatusOK)
			w.Write([]byte(`{"deleted": 1}`))
		})

		ClearProcessData(1, 1)
	})

	// 测试文件导入源ID的情况
	t.Run("文件导入源ID", func(t *testing.T) {
		ClearProcessData(1, data_source.FileImportSourceId)
	})

	// 测试数据库查询错误的情况
	t.Run("数据库查询错误", func(t *testing.T) {
		patches := gomonkey.ApplyMethodReturn(data_sync_child_task.NewDataSyncChildTaskModel(), "List", nil, int64(0), errors.New("数据库查询错误"))
		defer patches.Reset()

		ClearProcessData(1, 1)
	})
}
