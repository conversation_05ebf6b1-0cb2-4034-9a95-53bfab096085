package merge_helper

import (
	"context"
	"encoding/json"
	"fmt"
	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/initialize/mysql"
	"fobrain/models/mysql/personnel_departments"
	redis_helper "fobrain/models/redis"
	"testing"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/alicebob/miniredis/v2"
	goRedis "github.com/go-redis/redis/v8"
	"github.com/olivere/elastic/v7"
	"github.com/stretchr/testify/assert"
)

// 测试 cacheDepartmentData 函数
func TestCacheDepartmentData(t *testing.T) {
	// 设置 miniredis
	s, err := miniredis.Run()
	if err != nil {
		t.Fatalf("无法启动 miniredis: %v", err)
	}
	defer s.Close()

	// 创建 Redis 客户端
	cli := goRedis.NewClient(&goRedis.Options{
		Addr: s.Addr(),
	})

	// 使用 gomonkey 模拟函数
	patches := gomonkey.NewPatches()
	defer patches.Reset()

	// 模拟 DeleteKeysByPrefixWithScan 函数
	patches.ApplyFunc(redis_helper.DeleteKeysByPrefixWithScan, func(ctx context.Context, client *goRedis.Client, prefix string) error {
		// 验证传入的前缀是否正确
		assert.Equal(t, redis_helper.DepartmentKey("*"), prefix)
		return nil
	})

	// 模拟 personnel_departments.NewPersonnelDepartmentsModel().List 函数
	mockDepartments := []*personnel_departments.PersonnelDepartments{
		{
			Name:     "部门1",
			FullName: "公司/部门1",
			ParentId: 0,
		},
		{
			Name:     "部门2",
			FullName: "公司/部门1/部门2",
			ParentId: 1,
		},
	}

	// 设置 ID
	mockDepartments[0].Id = 1
	mockDepartments[1].Id = 2

	patches.ApplyFunc(personnel_departments.NewPersonnelDepartmentsModel, func() *personnel_departments.PersonnelDepartments {
		mockModel := &personnel_departments.PersonnelDepartments{}
		patches.ApplyMethod(mockModel, "List", func(_ *personnel_departments.PersonnelDepartments, page, size int, opts ...mysql.HandleFunc) ([]*personnel_departments.PersonnelDepartments, int64, error) {
			// 验证分页参数
			assert.Equal(t, 0, page)
			assert.Equal(t, 0, size)
			return mockDepartments, int64(len(mockDepartments)), nil
		})
		return mockModel
	})

	// 执行测试函数
	err = CacheDepartmentData(cli)
	assert.NoError(t, err)

	// 验证 Redis 中是否设置了正确的键值对
	expectedKeys := []string{
		redis_helper.DepartmentKey("1"),
		redis_helper.DepartmentKey("2"),
		redis_helper.DepartmentKey("all"),
	}

	// 验证 Redis 中的数据
	for _, key := range expectedKeys {
		val, err := s.Get(key)
		assert.NoError(t, err)
		assert.NotEmpty(t, val)

		// 验证 all 键中包含所有部门的映射
		if key == redis_helper.DepartmentKey("all") {
			var allDepartmentsMap map[uint64]*personnel_departments.PersonnelDepartments
			err = json.Unmarshal([]byte(val), &allDepartmentsMap)
			assert.NoError(t, err)
			assert.Equal(t, 2, len(allDepartmentsMap))
			assert.NotNil(t, allDepartmentsMap[1])
			assert.NotNil(t, allDepartmentsMap[2])
		}
	}
}

func TestCacheBlackSources(t *testing.T) {
	// 设置 miniredis
	s, err := miniredis.Run()
	if err != nil {
		t.Fatalf("无法启动 miniredis: %v", err)
	}
	defer s.Close()

	// 创建 Redis 客户端
	cli := goRedis.NewClient(&goRedis.Options{
		Addr: s.Addr(),
	})

	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	mockServer.Register("/asset/_search", []*elastic.SearchHit{
		{
			Id: "1",
			Source: []byte(`{
				  "ip": "************",
				  "area": 1,
				  "source_ids": [1,2]
			}`),
		},
		{
			Id: "2",
			Source: []byte(`{
				  "ip": "************",
				  "area": 1,
				  "source_ids": [1,2]
			}`),
		},
	})
	mockServer.Register("/_search/scroll", []*elastic.SearchHit{})
	err = CacheBlackSource(cli)
	assert.NoError(t, err)
	key := fmt.Sprintf("%s_%d", "************", 1)
	redisKey := redis_helper.BlackSourceKey(key)
	bt, err := cli.Get(context.Background(), redisKey).Bytes()
	assert.NoError(t, err)
	var sourceIds []uint64
	err = json.Unmarshal(bt, &sourceIds)
	assert.NoError(t, err)
	assert.Equal(t, []uint64{1, 2}, sourceIds)
}
func TestAssetMergeTaskStartPreparationHandler(t *testing.T) {
	s, err := miniredis.Run()
	if err != nil {
		t.Fatalf("无法启动 miniredis: %v", err)
	}
	defer s.Close()

	// 创建 Redis 客户端
	cli := goRedis.NewClient(&goRedis.Options{
		Addr: s.Addr(),
	})
	testcommon.SetRedisClient(cli)

	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	mockServer.Register("/staff/_search", []*elastic.SearchHit{
		{
			Id: "1",
			Source: []byte(`{
				  "fid": "1"
			}`),
		},
		{
			Id: "2",
			Source: []byte(`{
				  "fid": "2"
			}`),
		},
	})
	mockServer.Register("/asset/_search", []*elastic.SearchHit{
		{
			Id: "1",
			Source: []byte(`{
				  "ip": "************",
				  "area": 1,
				  "source_ids": [1,2]
			}`),
		},
		{
			Id: "2",
			Source: []byte(`{
				  "ip": "************",
				  "area": 1,
				  "source_ids": [1,2]
			}`),
		},
	})
	mockServer.Register("/_search/scroll", []*elastic.SearchHit{})

	// 使用 gomonkey 模拟函数
	patches := gomonkey.NewPatches()
	defer patches.Reset()

	// 模拟 DeleteKeysByPrefixWithScan 函数
	patches.ApplyFunc(redis_helper.DeleteKeysByPrefixWithScan, func(ctx context.Context, client *goRedis.Client, prefix string) error {
		// 验证传入的前缀是否正确
		//assert.Equal(t, redis_helper.AssetTagRulesKey("*"), prefix)
		return nil
	})

	AssetMergeTaskStartPrepareCache(nil, 0, 0, "", "", "")
}

func TestCacheAssetsProcessData(t *testing.T) {
	s, err := miniredis.Run()
	if err != nil {
		t.Fatalf("无法启动 miniredis: %v", err)
	}
	defer s.Close()

	cli := goRedis.NewClient(&goRedis.Options{
		Addr: s.Addr(),
	})
	testcommon.SetRedisClient(cli)

	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	mockServer.Register("/process_asset/_search", []*elastic.SearchHit{
		{
			Id: "1",
			Source: []byte(`{
				  "ip": "************",
				  "area": 1,
				  "source": 1,
				  "id": 1
			}`),
		},
		{
			Id: "2",
			Source: []byte(`{
				  "ip": "************",
				  "area": 1,
				  "source": 1,
				  "id": 2
			}`),
		},
	})
	mockServer.Register("/_search/scroll", []*elastic.SearchHit{})

	ipList := []string{"************_1", "************_1"}
	err = CacheAssetsProcessData(ipList)
	assert.NoError(t, err)

	// all
	err = CacheAssetsProcessData([]string{})
	assert.NoError(t, err)

}
func TestCacheProcessDeviceUniqueKeyToID(t *testing.T) {
	s, err := miniredis.Run()
	if err != nil {
		t.Fatalf("无法启动 miniredis: %v", err)
	}
	defer s.Close()

	cli := goRedis.NewClient(&goRedis.Options{
		Addr: s.Addr(),
	})
	testcommon.SetRedisClient(cli)

	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	mockServer.Register("/process_device/_search", []*elastic.SearchHit{
		{
			Id: "1",
			Source: []byte(`{
				  "unique_key": "************_1",
				  "id": "1"
			}`),
		},
		{
			Id: "2",
			Source: []byte(`{
				  "unique_key": "************_1",
				  "id": "2"
			}`),
		},
	})
	mockServer.Register("/_search/scroll", []*elastic.SearchHit{})

	err = CacheProcessDeviceUniqueKeyToID(cli)
	assert.NoError(t, err)

}
func TestCahceDeviceUniqueKeyToID(t *testing.T) {
	s, err := miniredis.Run()
	if err != nil {
		t.Fatalf("无法启动 miniredis: %v", err)
	}
	defer s.Close()

	cli := goRedis.NewClient(&goRedis.Options{
		Addr: s.Addr(),
	})
	testcommon.SetRedisClient(cli)

	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	mockServer.Register("/device/_search", []*elastic.SearchHit{
		{
			Id: "1",
			Source: []byte(`{
				  "fid": "************_1",
				  "id": "1"
			}`),
		},
		{
			Id: "2",
			Source: []byte(`{
				  "fid": "************_1",
				  "id": "2"
			}`),
		},
	})
	mockServer.Register("/_search/scroll", []*elastic.SearchHit{})

	err = CacheDeviceUniqueKeyToID(cli)
	assert.NoError(t, err)
}
