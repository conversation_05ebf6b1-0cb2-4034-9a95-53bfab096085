package merge_helper

import (
	"context"
	"encoding/json"
	"fmt"
	"fobrain/fobrain/common/localtime"
	"fobrain/initialize/es"
	"fobrain/initialize/mysql"
	"fobrain/initialize/redis"
	logs "fobrain/mergeService/utils/log"
	"fobrain/models/elastic/assets"
	"fobrain/models/elastic/device"
	"fobrain/models/elastic/poc"
	"fobrain/models/elastic/staff"
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/data_sync_child_task"
	"fobrain/models/mysql/strategy"
	redis_helper "fobrain/models/redis"

	"fobrain/pkg/cfg"
	"fobrain/pkg/utils/common_logs"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/olivere/elastic/v7"
)

var lock = sync.RWMutex{}

// GetMergeRules 获取融合规则
func GetMergeRules(businessType string, mlog *common_logs.Logger) ([]*strategy.Strategy, error) {
	key := ""
	switch businessType {
	case strategy.BusinessType_AssetMerge:
		key = redis_helper.AssetMergeRuleKey()
	case strategy.BusinessType_VulnMerge:
		key = redis_helper.VulnMergeRuleKey()
	case strategy.BusinessType_PersonMerge:
		key = redis_helper.PersonMergeRuleKey()
	case strategy.BusinessType_DeviceMerge:
		key = redis_helper.DeviceMergeRuleKey()
	}
	if key == "" {
		return nil, fmt.Errorf("未找到对应的 redis key")
	}
	redisClient := redis.GetRedisClient()
	// 从redis缓存获取规则
	ruleByte, err := redisClient.Get(context.Background(), key).Bytes()
	if err != nil {
		mlog.Info("从redis缓存获取资产融合规则失败. err: ", err)
	}
	strategies := make([]*strategy.Strategy, 0)
	if len(ruleByte) > 0 {
		err = json.Unmarshal(ruleByte, &strategies)
		if err != nil {
			mlog.Warn("解析redis缓存中的资产融合规则失败. err: ", err)
		}
	}
	if len(strategies) == 0 {
		lock.Lock()
		defer lock.Unlock()
		sModel := strategy.NewStrategyModel()
		res, err := sModel.ListAllDistinct(businessType)
		if err != nil {
			mlog.Warn("获取资产融合规则失败. err: ", err)
			return nil, err
		} else {
			strategies = res
			// 写入缓存
			ruleByte, _ := json.Marshal(strategies)
			mergeRuleCacheTime := cfg.LoadRedis().MergeRuleCacheTime
			err = redisClient.Set(context.Background(), key, ruleByte, time.Minute*time.Duration(mergeRuleCacheTime)).Err()
			if err != nil {
				mlog.Warn("写入资产融合规则缓存失败. err: ", err)
				return strategies, nil
			}
		}
	}

	return strategies, nil
}

// any类型转换为string类型
func AnyToString(val any) string {
	if val == nil {
		return ""
	}
	return fmt.Sprintf("%v", val)
}

// any类型转换为localtime.Time类型
func AnyToTime(val any) *localtime.Time {
	time, ok := val.(*localtime.Time)
	if ok {
		return time
	}
	return nil
}

// any类型转换为[]string类型
func AnyToStringSlice(val any) []string {
	switch v := val.(type) {
	case []string:
		return v
	case string:
		return strings.Split(v, ",")
	case []any:
		result := make([]string, 0)
		for _, v := range v {
			result = append(result, AnyToString(v))
		}
		return result
	}
	return nil
}

// any类型转换为int类型
func AnyToInt(val any) int {
	switch v := val.(type) {
	case int:
		return v
	case float64:
		return int(v)
	case string:
		i, err := strconv.Atoi(v)
		if err != nil {
			return 0
		}
		return i
	}
	return 0
}

// any类型转换为uint64类型
func AnyToUint64(val any) uint64 {
	switch v := val.(type) {
	case uint64:
		return v
	case int64:
		return uint64(v)
	case int:
		return uint64(v)
	case float64:
		return uint64(v)
	case string:
		i, err := strconv.ParseUint(v, 10, 64)
		if err != nil {
			return 0
		}
		return i
	}
	return 0
}
func ClearProcessData(nodeId uint64, sourceId uint64) {
	if sourceId == data_source.FileImportSourceId {
		return
	}
	rows, _, err := data_sync_child_task.NewDataSyncChildTaskModel().List(0, 0, mysql.WithWhere("node_id = ? and source_id = ?", nodeId, sourceId), mysql.WithOrder("id desc"))
	if err != nil {
		logs.GetLogger("service").Errorf("ClearProcessData error:%v, nodeId: %d, sourceId: %d", err, nodeId, sourceId)
		return
	}
	// 遍历每个type 只保留最新的三次，超过的，保存一个切片中，接下来调用es删除对应的process索引中的数据， type:1为资产和设备，2为漏洞，3为人员， 过程索引有个child_task_id字段关联
	// 按type分组,保持id倒序
	typeGroups := make(map[int][]*data_sync_child_task.DataSyncChildTask)
	for _, row := range rows {
		typeGroups[row.Type] = append(typeGroups[row.Type], row)
	}

	// 对每种type,只保留最新的3条记录
	for typ, tasks := range typeGroups {
		if len(tasks) <= 3 {
			continue
		}

		// 按ID降序排序
		sort.Slice(tasks, func(i, j int) bool {
			return tasks[i].Id > tasks[j].Id
		})

		// 获取需要删除的任务ID(跳过最新的3条)
		var taskIds []interface{}
		for i := 3; i < len(tasks); i++ {
			// 72小时内的数据不删除，数据删除后无法查问题，所以需要保留一定时间
			if tasks[i].CreatedAt.Time().Add(72 * time.Hour).After(time.Now()) {
				continue
			}
			taskIds = append(taskIds, tasks[i].Id)
		}

		ctx := context.Background()
		switch typ {
		case 1: // 资产和设备
			_, err = es.GetEsClient().DeleteByQuery().
				Index(assets.NewProcessAssetsModel().IndexName(), device.NewProcessDeviceModel().IndexName()).
				Query(elastic.NewTermsQuery("child_task_id", taskIds...)).
				Do(ctx)
			if err != nil {
				logs.GetLogger("service").Errorf("删除资产/设备process索引数据失败, taskIds: %v, err: %v", taskIds, err)
			}
		case 2: // 漏洞
			_, err = es.GetEsClient().DeleteByQuery().
				Index(poc.NewProcessPocModel().IndexName()).
				Query(elastic.NewTermsQuery("child_task_id", taskIds...)).
				Do(ctx)
			if err != nil {
				logs.GetLogger("service").Errorf("删除漏洞process索引数据失败, taskIds: %v, err: %v", taskIds, err)
			}
		case 3: // 人员
			_, err = es.GetEsClient().DeleteByQuery().
				Index(staff.NewProcessStaffModel().IndexName()).
				Query(elastic.NewTermsQuery("child_task_id", taskIds...)).
				Do(ctx)
			if err != nil {
				logs.GetLogger("service").Errorf("删除人员process索引数据失败, taskIds: %v, err: %v", taskIds, err)
			}
		}
	}
	//todo 主动扫描也要删除，但因当前主动扫描是proactive_tasks表本身数据的自复制，没有存父子关系，导致无法获取每个数据源，每个节点，最3次的同步
}
