package model

import (
	"testing"
	"time"

	"fobrain/fobrain/common/localtime"
	frame "fobrain/mergeFrame"

	esmodel_asset "fobrain/models/elastic/assets"
	esmodel_device "fobrain/models/elastic/device"
	esmodel_vuln "fobrain/models/elastic/poc"
	dbmodel "fobrain/models/mysql/strategy"

	"github.com/stretchr/testify/assert"
)

func TestDeviceFieldHandler(t *testing.T) {
	// Mock data
	now := time.Now()
	sourceData := []*esmodel_device.ProcessDevice{
		{
			Source:          1,
			Node:            1,
			UniqueKey:       "123456789",
			PrivateIp:       []string{"***********"},
			PublicIp:        []string{"*******"},
			HostName:        "host1",
			EthName:         "eth0",
			Os:              "Linux",
			Kernel:          "5.4.0",
			Model:           "Model1",
			Maker:           "Maker1",
			Sn:              "SN1",
			Mac:             "00:1A:2B:3C:4D:5E",
			MachineRoom:     "Room1",
			MemorySize:      "16GB",
			MemoryUsageRate: "50%",
			CpuMaker:        "Intel",
			CpuBrand:        "Core i7",
			CpuCount:        4,
			DiskCount:       2,
			DiskSize:        1024,
			DiskUsageRate:   "75%",
			LoadAverage:     "0.5",
			CreatedAt:       localtime.NewLocalTime(now),
			UpdatedAt:       localtime.NewLocalTime(now),
		},
		{
			Source:          2,
			Node:            2,
			UniqueKey:       "123456789",
			PrivateIp:       []string{"***********"},
			PublicIp:        []string{"*******"},
			HostName:        "host2",
			EthName:         "eth1",
			Os:              "Windows",
			Kernel:          "10.0",
			Model:           "Model2",
			Maker:           "Maker2",
			Sn:              "SN2",
			Mac:             "00:1A:2B:3C:4D:5F",
			MachineRoom:     "Room2",
			MemorySize:      "32GB",
			MemoryUsageRate: "60%",
			CpuMaker:        "AMD",
			CpuBrand:        "Ryzen 7",
			CpuCount:        8,
			DiskCount:       4,
			DiskSize:        2048,
			DiskUsageRate:   "80%",
			LoadAverage:     "0.6",
			CreatedAt:       localtime.NewLocalTime(now),
			UpdatedAt:       localtime.NewLocalTime(now),
		},
	}

	strategy := []*dbmodel.Strategy{
		{
			FieldName:       "ip",
			SourcePriority:  map[string]uint64{"1": 1, "2": 2},
			UntrustedSource: []string{},
		},
		{
			FieldName:       "hostname",
			SourcePriority:  map[string]uint64{"1": 1, "2": 2},
			UntrustedSource: []string{},
		},
		{
			FieldName:       "os",
			SourcePriority:  map[string]uint64{"1": 1, "2": 1},
			UntrustedSource: []string{},
		},
		{
			FieldName:       "kernel",
			SourcePriority:  map[string]uint64{"1": 1, "2": 1},
			UntrustedSource: []string{},
		},
		{
			FieldName:       "model",
			SourcePriority:  map[string]uint64{"1": 1, "2": 1},
			UntrustedSource: []string{},
		},
		{
			FieldName:       "maker",
			SourcePriority:  map[string]uint64{"1": 1, "2": 1},
			UntrustedSource: []string{},
		},
		{
			FieldName:       "sn",
			SourcePriority:  map[string]uint64{"1": 1, "2": 1},
			UntrustedSource: []string{},
		},
		{
			FieldName:       "mac",
			SourcePriority:  map[string]uint64{"1": 1, "2": 1},
			UntrustedSource: []string{},
		},
		{
			FieldName:       "machineroom",
			SourcePriority:  map[string]uint64{"1": 1, "2": 1},
			UntrustedSource: []string{},
		},
		{
			FieldName:       "memorysize",
			SourcePriority:  map[string]uint64{"1": 1, "2": 1},
			UntrustedSource: []string{},
		},
		{
			FieldName:       "memoryusagerate",
			SourcePriority:  map[string]uint64{"1": 1, "2": 1},
			UntrustedSource: []string{},
		},
		{
			FieldName:       "cpumaker",
			SourcePriority:  map[string]uint64{"1": 1, "2": 1},
			UntrustedSource: []string{},
		},
		{
			FieldName:       "cpubrand",
			SourcePriority:  map[string]uint64{"1": 1, "2": 1},
			UntrustedSource: []string{},
		},
		{
			FieldName:       "cpucount",
			SourcePriority:  map[string]uint64{"1": 1, "2": 1},
			UntrustedSource: []string{},
		},
		{
			FieldName:       "diskcount",
			SourcePriority:  map[string]uint64{"1": 1, "2": 1},
			UntrustedSource: []string{},
		},
		{
			FieldName:       "disksize",
			SourcePriority:  map[string]uint64{"1": 1, "2": 1},
			UntrustedSource: []string{},
		},
		{
			FieldName:       "diskusagerate",
			SourcePriority:  map[string]uint64{"1": 1, "2": 1},
			UntrustedSource: []string{},
		},
		{
			FieldName:       "loadaverage",
			SourcePriority:  map[string]uint64{"1": 1, "2": 1},
			UntrustedSource: []string{"1"},
		},
	}

	device := &esmodel_device.Device{
		IpSource:              make(map[string]string),
		HostNameSource:        make(map[string]string),
		OsSource:              make(map[string]string),
		KernelSource:          make(map[string]string),
		ModelSource:           make(map[string]string),
		MakerSource:           make(map[string]string),
		SnSource:              make(map[string]string),
		MacSource:             make(map[string]string),
		MachineRoomSource:     make(map[string]string),
		MemorySizeSource:      make(map[string]string),
		MemoryUsageRateSource: make(map[string]string),
		CpuMakerSource:        make(map[string]string),
		CpuBrandSource:        make(map[string]string),
		CpuCountSource:        make(map[string]int),
		DiskCountSource:       make(map[string]int),
		DiskSizeSource:        make(map[string]int),
		DiskUsageRateSource:   make(map[string]string),
		LoadAverageSource:     make(map[string]string),
	}

	latestData := map[string]*esmodel_device.ProcessDevice{
		"1": sourceData[0],
		"2": sourceData[1],
	}

	// Test cases
	tests := []struct {
		name     string
		expected *esmodel_device.Device
	}{
		{
			name: "Happy Path",
			expected: &esmodel_device.Device{
				PrivateIp:       []string{"***********"},
				PublicIp:        []string{"*******"},
				HostName:        []string{"host1"},
				Os:              []string{"Linux", "Windows"},
				Kernel:          []string{"5.4.0", "10.0"},
				Model:           []string{"Model1", "Model2"},
				Maker:           []string{"Maker1", "Maker2"},
				Sn:              []string{"SN1", "SN2"},
				Mac:             []string{"00:1A:2B:3C:4D:5E", "00:1A:2B:3C:4D:5F"},
				MachineRoom:     []string{"Room1", "Room2"},
				MemorySize:      []string{"16GB", "32GB"},
				MemoryUsageRate: []string{"50%", "60%"},
				CpuMaker:        []string{"Intel", "AMD"},
				CpuBrand:        []string{"Core i7", "Ryzen 7"},
				CpuCount:        []int{4, 8},
				DiskCount:       []int{2, 4},
				DiskSize:        []int{1024, 2048},
				DiskUsageRate:   []string{"75%", "80%"},
				LoadAverage:     []string{"0.6"},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			frame.DeviceFieldProcess(device, strategy, latestData)
			assert.ElementsMatch(t, tt.expected.HostName, device.HostName)
			assert.ElementsMatch(t, tt.expected.Os, device.Os)
			assert.ElementsMatch(t, tt.expected.Kernel, device.Kernel)
			assert.ElementsMatch(t, tt.expected.Model, device.Model)
			assert.ElementsMatch(t, tt.expected.Maker, device.Maker)
			assert.ElementsMatch(t, tt.expected.Sn, device.Sn)
			assert.ElementsMatch(t, tt.expected.Mac, device.Mac)
			assert.ElementsMatch(t, tt.expected.MachineRoom, device.MachineRoom)
			assert.ElementsMatch(t, tt.expected.MemorySize, device.MemorySize)
			assert.ElementsMatch(t, tt.expected.MemoryUsageRate, device.MemoryUsageRate)
			assert.ElementsMatch(t, tt.expected.CpuMaker, device.CpuMaker)
			assert.ElementsMatch(t, tt.expected.CpuBrand, device.CpuBrand)
			assert.ElementsMatch(t, tt.expected.CpuCount, device.CpuCount)
			assert.ElementsMatch(t, tt.expected.DiskCount, device.DiskCount)
			assert.ElementsMatch(t, tt.expected.DiskSize, device.DiskSize)
			assert.ElementsMatch(t, tt.expected.DiskUsageRate, device.DiskUsageRate)
			assert.ElementsMatch(t, tt.expected.LoadAverage, device.LoadAverage)
		})
	}
}

func TestDeviceStrategyExecutor_Execute(t *testing.T) {
	// Mock data
	now := time.Now()
	sourceData := []*esmodel_device.ProcessDevice{
		{
			Source:          1,
			Node:            1,
			TaskDataId:      "task1",
			Id:              "proc1",
			Area:            1,
			PrivateIp:       []string{"***********"},
			PublicIp:        []string{"*******"},
			HostName:        "host1",
			EthName:         "eth0",
			Os:              "Linux",
			Kernel:          "4.15",
			Model:           "Model1",
			Maker:           "Maker1",
			Sn:              "SN1",
			Mac:             "00:1A:2B:3C:4D:5E",
			MachineRoom:     "Room1",
			MemorySize:      "16GB",
			MemoryUsageRate: "50%",
			CpuMaker:        "Intel",
			CpuBrand:        "Core i7",
			CpuCount:        4,
			DiskCount:       2,
			DiskSize:        1024,
			DiskUsageRate:   "75%",
			LoadAverage:     "0.5",
			CreatedAt:       localtime.NewLocalTime(now),
			UpdatedAt:       localtime.NewLocalTime(now),
		},
		{
			Source:          2,
			Node:            2,
			TaskDataId:      "task2",
			Id:              "proc2",
			Area:            2,
			PrivateIp:       []string{"***********"},
			PublicIp:        []string{"*******"},
			HostName:        "host2",
			EthName:         "eth1",
			Os:              "Windows",
			Kernel:          "10",
			Model:           "Model2",
			Maker:           "Maker2",
			Sn:              "SN2",
			Mac:             "00:1A:2B:3C:4D:5F",
			MachineRoom:     "Room2",
			MemorySize:      "8GB",
			MemoryUsageRate: "30%",
			CpuMaker:        "AMD",
			CpuBrand:        "Ryzen",
			CpuCount:        8,
			DiskCount:       1,
			DiskSize:        512,
			DiskUsageRate:   "50%",
			LoadAverage:     "0.3",
			CreatedAt:       localtime.NewLocalTime(now),
			UpdatedAt:       localtime.NewLocalTime(now),
		},
	}

	strategy := []*dbmodel.Strategy{
		{
			FieldName:       "ip",
			SourcePriority:  map[string]uint64{"1": 1, "2": 2},
			UntrustedSource: []string{},
		},
	}

	// Execute function
	executor := &DeviceStrategyExecutor{
		SourceData: sourceData,
		Strategy:   strategy,
	}
	result, _ := executor.Execute()

	// Assertions
	assert.NotNil(t, result)
	assert.Equal(t, []uint64{1, 2}, result.AllSourceIds)
	assert.Equal(t, []uint64{1, 2}, result.AllNodeIds)
	assert.Equal(t, []string{"task1", "task2"}, result.AllTaskDataIds)
	assert.Equal(t, []string{"proc1", "proc2"}, result.AllProcessIds)
	assert.Equal(t, map[string]int{"1-1": 1, "2-2": 2}, result.AreaSource)
	assert.Equal(t, map[string]string{"1-1": "host1", "2-2": "host2"}, result.HostNameSource)
	assert.Equal(t, map[string]string{"1-1": "Linux", "2-2": "Windows"}, result.OsSource)
	assert.Equal(t, map[string]string{"1-1": "4.15", "2-2": "10"}, result.KernelSource)
	assert.Equal(t, map[string]string{"1-1": "Model1", "2-2": "Model2"}, result.ModelSource)
	assert.Equal(t, map[string]string{"1-1": "Maker1", "2-2": "Maker2"}, result.MakerSource)
	assert.Equal(t, map[string]string{"1-1": "SN1", "2-2": "SN2"}, result.SnSource)
	assert.Equal(t, map[string]string{"1-1": "00:1A:2B:3C:4D:5E", "2-2": "00:1A:2B:3C:4D:5F"}, result.MacSource)
	assert.Equal(t, map[string]string{"1-1": "Room1", "2-2": "Room2"}, result.MachineRoomSource)
	assert.Equal(t, map[string]string{"1-1": "16GB", "2-2": "8GB"}, result.MemorySizeSource)
	assert.Equal(t, map[string]string{"1-1": "50%", "2-2": "30%"}, result.MemoryUsageRateSource)
	assert.Equal(t, map[string]string{"1-1": "Intel", "2-2": "AMD"}, result.CpuMakerSource)
	assert.Equal(t, map[string]string{"1-1": "Core i7", "2-2": "Ryzen"}, result.CpuBrandSource)
	assert.Equal(t, map[string]int{"1-1": 4, "2-2": 8}, result.CpuCountSource)
	assert.Equal(t, map[string]int{"1-1": 2, "2-2": 1}, result.DiskCountSource)
	assert.Equal(t, map[string]int{"1-1": 1024, "2-2": 512}, result.DiskSizeSource)
	assert.Equal(t, map[string]string{"1-1": "75%", "2-2": "50%"}, result.DiskUsageRateSource)
	assert.Equal(t, map[string]string{"1-1": "0.5", "2-2": "0.3"}, result.LoadAverageSource)
}

func TestAssetFieldHandler_HappyPath(t *testing.T) {
	// Mock data
	now := time.Now()
	sourceData := []*esmodel_asset.ProcessAssets{
		{
			Source:          1,
			Node:            1,
			Ip:              "***********",
			Area:            1,
			HostName:        "host1",
			EthName:         "eth0",
			Os:              "Linux",
			Kernel:          "5.4.0",
			Model:           "Model1",
			Maker:           "Maker1",
			Sn:              "SN1",
			Mac:             "00:1A:2B:3C:4D:5E",
			MachineRoom:     "Room1",
			MemorySize:      "16GB",
			MemoryUsageRate: "50%",
			CpuMaker:        "Intel",
			CpuBrand:        "Core i7",
			CpuCount:        4,
			DiskCount:       2,
			DiskSize:        1024,
			DiskUsageRate:   "75%",
			LoadAverage:     "0.5",
			CreatedAt:       localtime.NewLocalTime(now),
			UpdatedAt:       localtime.NewLocalTime(now),
		},
		{
			Source:          2,
			Node:            2,
			Ip:              "***********",
			Area:            1,
			HostName:        "host2",
			EthName:         "eth1",
			Os:              "Windows",
			Kernel:          "10.0",
			Model:           "Model2",
			Maker:           "Maker2",
			Sn:              "SN2",
			Mac:             "00:1A:2B:3C:4D:5F",
			MachineRoom:     "Room2",
			MemorySize:      "32GB",
			MemoryUsageRate: "60%",
			CpuMaker:        "AMD",
			CpuBrand:        "Ryzen 7",
			CpuCount:        8,
			DiskCount:       4,
			DiskSize:        2048,
			DiskUsageRate:   "80%",
			LoadAverage:     "0.6",
			CreatedAt:       localtime.NewLocalTime(now),
			UpdatedAt:       localtime.NewLocalTime(now),
		},
	}

	strategy := []*dbmodel.Strategy{
		{
			FieldName:       "hostname",
			SourcePriority:  map[string]uint64{"1": 1, "2": 2},
			UntrustedSource: []string{},
		},
		{
			FieldName:       "os",
			SourcePriority:  map[string]uint64{"1": 1, "2": 1},
			UntrustedSource: []string{},
		},
		{
			FieldName:       "kernel",
			SourcePriority:  map[string]uint64{"1": 1, "2": 1},
			UntrustedSource: []string{},
		},
		{
			FieldName:       "model",
			SourcePriority:  map[string]uint64{"1": 1, "2": 1},
			UntrustedSource: []string{},
		},
		{
			FieldName:       "maker",
			SourcePriority:  map[string]uint64{"1": 1, "2": 1},
			UntrustedSource: []string{},
		},
		{
			FieldName:       "sn",
			SourcePriority:  map[string]uint64{"1": 1, "2": 1},
			UntrustedSource: []string{},
		},
		{
			FieldName:       "mac",
			SourcePriority:  map[string]uint64{"1": 1, "2": 1},
			UntrustedSource: []string{},
		},
		{
			FieldName:       "machineroom",
			SourcePriority:  map[string]uint64{"1": 1, "2": 1},
			UntrustedSource: []string{},
		},
		{
			FieldName:       "memorysize",
			SourcePriority:  map[string]uint64{"1": 1, "2": 1},
			UntrustedSource: []string{},
		},
		{
			FieldName:       "memoryusagerate",
			SourcePriority:  map[string]uint64{"1": 1, "2": 1},
			UntrustedSource: []string{},
		},
		{
			FieldName:       "cpumaker",
			SourcePriority:  map[string]uint64{"1": 1, "2": 1},
			UntrustedSource: []string{},
		},
		{
			FieldName:       "cpubrand",
			SourcePriority:  map[string]uint64{"1": 1, "2": 1},
			UntrustedSource: []string{},
		},
		{
			FieldName:       "cpucount",
			SourcePriority:  map[string]uint64{"1": 1, "2": 1},
			UntrustedSource: []string{},
		},
		{
			FieldName:       "diskcount",
			SourcePriority:  map[string]uint64{"1": 1, "2": 1},
			UntrustedSource: []string{},
		},
		{
			FieldName:       "disksize",
			SourcePriority:  map[string]uint64{"1": 1, "2": 1},
			UntrustedSource: []string{},
		},
		{
			FieldName:       "diskusagerate",
			SourcePriority:  map[string]uint64{"1": 1, "2": 1},
			UntrustedSource: []string{},
		},
		{
			FieldName:       "loadaverage",
			SourcePriority:  map[string]uint64{"1": 1, "2": 1},
			UntrustedSource: []string{"1"},
		},
	}

	asset := &esmodel_asset.Assets{
		HostNameSource:        make(map[string]string),
		OsSource:              make(map[string]string),
		KernelSource:          make(map[string]string),
		ModelSource:           make(map[string]string),
		MakerSource:           make(map[string]string),
		SnSource:              make(map[string]string),
		MacSource:             make(map[string]string),
		MachineRoomSource:     make(map[string]string),
		MemorySizeSource:      make(map[string]string),
		MemoryUsageRateSource: make(map[string]string),
		CpuMakerSource:        make(map[string]string),
		CpuBrandSource:        make(map[string]string),
		CpuCountSource:        make(map[string]int),
		DiskCountSource:       make(map[string]int),
		DiskSizeSource:        make(map[string]int),
		DiskUsageRateSource:   make(map[string]string),
		LoadAverageSource:     make(map[string]string),
		BusinessSource:        make(map[string][]*esmodel_asset.Business),
	}

	latestData := map[string]*esmodel_asset.ProcessAssets{
		"1": sourceData[0],
		"2": sourceData[1],
	}

	// Test cases
	tests := []struct {
		name     string
		expected *esmodel_asset.Assets
	}{
		{
			name: "Happy Path",
			expected: &esmodel_asset.Assets{
				Ip:              "***********",
				HostName:        []string{"host1"},
				Os:              []string{"Linux", "Windows"},
				Kernel:          []string{"5.4.0", "10.0"},
				Model:           []string{"Model1", "Model2"},
				Maker:           []string{"Maker1", "Maker2"},
				Sn:              []string{"SN1", "SN2"},
				Mac:             []string{"00:1A:2B:3C:4D:5E", "00:1A:2B:3C:4D:5F"},
				MachineRoom:     []string{"Room1", "Room2"},
				MemorySize:      []string{"16GB", "32GB"},
				MemoryUsageRate: []string{"50%", "60%"},
				CpuMaker:        []string{"Intel", "AMD"},
				CpuBrand:        []string{"Core i7", "Ryzen 7"},
				CpuCount:        []int{4, 8},
				DiskCount:       []int{2, 4},
				DiskSize:        []int{1024, 2048},
				DiskUsageRate:   []string{"75%", "80%"},
				LoadAverage:     []string{"0.6"},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			frame.AssetFieldProcess(asset, strategy, latestData)
			assert.ElementsMatch(t, tt.expected.HostName, asset.HostName)
			assert.ElementsMatch(t, tt.expected.Os, asset.Os)
			assert.ElementsMatch(t, tt.expected.Kernel, asset.Kernel)
			assert.ElementsMatch(t, tt.expected.Model, asset.Model)
			assert.ElementsMatch(t, tt.expected.Maker, asset.Maker)
			assert.ElementsMatch(t, tt.expected.Sn, asset.Sn)
			assert.ElementsMatch(t, tt.expected.Mac, asset.Mac)
			assert.ElementsMatch(t, tt.expected.MachineRoom, asset.MachineRoom)
			assert.ElementsMatch(t, tt.expected.MemorySize, asset.MemorySize)
			assert.ElementsMatch(t, tt.expected.MemoryUsageRate, asset.MemoryUsageRate)
			assert.ElementsMatch(t, tt.expected.CpuMaker, asset.CpuMaker)
			assert.ElementsMatch(t, tt.expected.CpuBrand, asset.CpuBrand)
			assert.ElementsMatch(t, tt.expected.CpuCount, asset.CpuCount)
			assert.ElementsMatch(t, tt.expected.DiskCount, asset.DiskCount)
			assert.ElementsMatch(t, tt.expected.DiskSize, asset.DiskSize)
			assert.ElementsMatch(t, tt.expected.DiskUsageRate, asset.DiskUsageRate)
			assert.ElementsMatch(t, tt.expected.LoadAverage, asset.LoadAverage)
		})
	}
}

func TestAssetStrategyExecutor_HappyPath(t *testing.T) {
	// Mock data
	now := time.Now()
	sourceData := []*esmodel_asset.ProcessAssets{
		{
			Source:          1,
			Node:            1,
			TaskDataId:      "task1",
			Id:              "proc1",
			Area:            1,
			Ip:              "***********",
			HostName:        "host1",
			EthName:         "eth0",
			Os:              "Linux",
			Kernel:          "4.15",
			Model:           "Model1",
			Maker:           "Maker1",
			Sn:              "SN1",
			Mac:             "00:1A:2B:3C:4D:5E",
			MachineRoom:     "Room1",
			MemorySize:      "16GB",
			MemoryUsageRate: "50%",
			CpuMaker:        "Intel",
			CpuBrand:        "Core i7",
			CpuCount:        4,
			DiskCount:       2,
			DiskSize:        1024,
			DiskUsageRate:   "75%",
			LoadAverage:     "0.5",
			CreatedAt:       localtime.NewLocalTime(now),
			UpdatedAt:       localtime.NewLocalTime(now),
		},
		{
			Source:          2,
			Node:            2,
			TaskDataId:      "task2",
			Id:              "proc2",
			Area:            1,
			Ip:              "***********",
			HostName:        "host2",
			EthName:         "eth1",
			Os:              "Windows",
			Kernel:          "10",
			Model:           "Model2",
			Maker:           "Maker2",
			Sn:              "SN2",
			Mac:             "00:1A:2B:3C:4D:5F",
			MachineRoom:     "Room2",
			MemorySize:      "8GB",
			MemoryUsageRate: "30%",
			CpuMaker:        "AMD",
			CpuBrand:        "Ryzen",
			CpuCount:        8,
			DiskCount:       1,
			DiskSize:        512,
			DiskUsageRate:   "50%",
			LoadAverage:     "0.3",
			CreatedAt:       localtime.NewLocalTime(now),
			UpdatedAt:       localtime.NewLocalTime(now),
		},
	}

	strategy := []*dbmodel.Strategy{
		{
			FieldName:       "Os",
			SourcePriority:  map[string]uint64{"1": 1, "2": 2},
			UntrustedSource: []string{},
		},
	}

	// Execute function
	executor := &AssetStrategyExecutor{
		SourceData: sourceData,
		Strategy:   strategy,
	}
	result, _ := executor.Execute()

	// Assertions
	assert.NotNil(t, result)
	assert.Equal(t, []uint64{1, 2}, result.AllSourceIds)
	assert.Equal(t, []uint64{1, 2}, result.AllNodeIds)
	assert.Equal(t, []string{"task1", "task2"}, result.AllTaskDataIds)
	assert.Equal(t, []string{"proc1", "proc2"}, result.AllProcessIds)
	assert.Equal(t, map[string]string{"1-1": "host1", "2-2": "host2"}, result.HostNameSource)
	assert.Equal(t, map[string]string{"1-1": "eth0", "2-2": "eth1"}, result.EthNameSource)
	assert.Equal(t, map[string]string{"1-1": "Linux", "2-2": "Windows"}, result.OsSource)
	assert.Equal(t, map[string]string{"1-1": "4.15", "2-2": "10"}, result.KernelSource)
	assert.Equal(t, map[string]string{"1-1": "Model1", "2-2": "Model2"}, result.ModelSource)
	assert.Equal(t, map[string]string{"1-1": "Maker1", "2-2": "Maker2"}, result.MakerSource)
	assert.Equal(t, map[string]string{"1-1": "SN1", "2-2": "SN2"}, result.SnSource)
	assert.Equal(t, map[string]string{"1-1": "00:1A:2B:3C:4D:5E", "2-2": "00:1A:2B:3C:4D:5F"}, result.MacSource)
	assert.Equal(t, map[string]string{"1-1": "Room1", "2-2": "Room2"}, result.MachineRoomSource)
	assert.Equal(t, map[string]string{"1-1": "16GB", "2-2": "8GB"}, result.MemorySizeSource)
	assert.Equal(t, map[string]string{"1-1": "50%", "2-2": "30%"}, result.MemoryUsageRateSource)
	assert.Equal(t, map[string]string{"1-1": "Intel", "2-2": "AMD"}, result.CpuMakerSource)
	assert.Equal(t, map[string]string{"1-1": "Core i7", "2-2": "Ryzen"}, result.CpuBrandSource)
	assert.Equal(t, map[string]int{"1-1": 4, "2-2": 8}, result.CpuCountSource)
	assert.Equal(t, map[string]int{"1-1": 2, "2-2": 1}, result.DiskCountSource)
	assert.Equal(t, map[string]int{"1-1": 1024, "2-2": 512}, result.DiskSizeSource)
	assert.Equal(t, map[string]string{"1-1": "75%", "2-2": "50%"}, result.DiskUsageRateSource)
	assert.Equal(t, map[string]string{"1-1": "0.5", "2-2": "0.3"}, result.LoadAverageSource)
}

func TestVulnStrategyExecutor_Execute(t *testing.T) {
	// Mock data
	now := time.Now()
	sourceData := []*esmodel_vuln.ProcessPoc{
		{
			Source:     1,
			Node:       1,
			TaskDataId: "task1",
			Id:         "id1",
			IsPoc:      1,
			Ip:         "***********",
			Port:       8080,
			Level:      2,
			Name:       "Test Vuln",
			Cve:        "CVE-2021-1234",
			Cnvd:       "CNVD-2021-1234",
			Cnnvd:      "CNNVD-2021-1234",
			HasExp:     1,
			HasPoc:     1,
			Status:     1,
			CreatedAt:  localtime.NewLocalTime(now),
			UpdatedAt:  localtime.NewLocalTime(now),
		},
	}
	strategy := []*dbmodel.Strategy{
		{
			FieldName:       "ip",
			SourcePriority:  map[string]uint64{"1": 1},
			UntrustedSource: []string{},
		},
		{
			FieldName:       "port",
			SourcePriority:  map[string]uint64{"1": 1},
			UntrustedSource: []string{},
		},
		{
			FieldName:       "ispoc",
			SourcePriority:  map[string]uint64{"1": 1},
			UntrustedSource: []string{},
		},
		{
			FieldName:       "level",
			SourcePriority:  map[string]uint64{"1": 1},
			UntrustedSource: []string{},
		},
		{
			FieldName:       "name",
			SourcePriority:  map[string]uint64{"1": 1},
			UntrustedSource: []string{},
		},
		{
			FieldName:       "hasexp",
			SourcePriority:  map[string]uint64{"1": 1},
			UntrustedSource: []string{},
		},
		{
			FieldName:       "haspoc",
			SourcePriority:  map[string]uint64{"1": 1},
			UntrustedSource: []string{},
		},
		{
			FieldName:       "status",
			SourcePriority:  map[string]uint64{"1": 1},
			UntrustedSource: []string{},
		},
	}

	// Create executor
	executor := &VulnStrategyExecutor{
		SourceData: sourceData,
		Strategy:   strategy,
	}

	// Execute
	result, _ := executor.Execute()

	// Assertions
	assert.NotNil(t, result)
	assert.Equal(t, []uint64{1}, result.AllSourceIds)
	assert.Equal(t, []uint64{1}, result.AllNodeIds)
	assert.Equal(t, []string{"task1"}, result.AllTaskDataIds)
	assert.Equal(t, []string{"id1"}, result.AllProcessIds)
	assert.Equal(t, 1, result.IsPoc)
	assert.Equal(t, "***********", result.Ip)
	assert.Equal(t, 8080, result.Port)
	assert.Equal(t, 2, result.Level)
	assert.Equal(t, "Test Vuln", result.Name)
	assert.Equal(t, 1, result.HasExp)
	assert.Equal(t, 1, result.HasPoc)
	assert.Equal(t, 0, result.Status)
}

func TestVulnStrategyExecutor_Execute_EmptySourceData(t *testing.T) {
	// Create executor with empty source data
	executor := &VulnStrategyExecutor{
		SourceData: []*esmodel_vuln.ProcessPoc{},
		Strategy:   []*dbmodel.Strategy{},
	}

	// Execute
	result, _ := executor.Execute()

	// Assertions
	assert.NotNil(t, result)
	assert.Empty(t, result.AllSourceIds)
	assert.Empty(t, result.AllNodeIds)
	assert.Empty(t, result.AllTaskDataIds)
	assert.Empty(t, result.AllProcessIds)
	assert.Zero(t, result.IsPoc)
	assert.Empty(t, result.Ip)
	assert.Zero(t, result.Port)
	assert.Zero(t, result.Level)
	assert.Empty(t, result.Name)
	assert.Empty(t, result.Cve)
	assert.Empty(t, result.Cnvd)
	assert.Empty(t, result.Cnnvd)
	assert.Zero(t, result.HasExp)
	assert.Zero(t, result.HasPoc)
	assert.Zero(t, result.Status)
}

func TestVulnStrategyExecutor_Execute_MultipleSources(t *testing.T) {
	// Mock data with multiple sources
	now := time.Now()
	sourceData := []*esmodel_vuln.ProcessPoc{
		{
			Source:     1,
			Node:       1,
			TaskDataId: "task1",
			Id:         "id1",
			IsPoc:      2,
			Ip:         "***********",
			Port:       8080,
			Level:      2,
			Name:       "Test Vuln",
			Cve:        "CVE-2021-1234",
			Cnvd:       "CNVD-2021-1234",
			Cnnvd:      "CNNVD-2021-1234",
			HasExp:     1,
			HasPoc:     1,
			Status:     1,
			CreatedAt:  localtime.NewLocalTime(now),
			UpdatedAt:  localtime.NewLocalTime(now),
		},
		{
			Source:     2,
			Node:       1,
			TaskDataId: "task2",
			Id:         "id2",
			IsPoc:      2,
			Ip:         "***********",
			Port:       8081,
			Level:      1,
			Name:       "Test Vuln 2",
			Cve:        "CVE-2021-1235",
			Cnvd:       "CNVD-2021-1235",
			Cnnvd:      "CNNVD-2021-1235",
			HasExp:     0,
			HasPoc:     0,
			Status:     0,
			CreatedAt:  localtime.NewLocalTime(now),
			UpdatedAt:  localtime.NewLocalTime(now),
		},
	}
	strategy := []*dbmodel.Strategy{
		{
			FieldName:       "ip",
			SourcePriority:  map[string]uint64{"1": 2, "2": 1},
			UntrustedSource: []string{},
		},
		{
			FieldName:       "port",
			SourcePriority:  map[string]uint64{"1": 2, "2": 1},
			UntrustedSource: []string{},
		},
		{
			FieldName:       "ispoc",
			SourcePriority:  map[string]uint64{"1": 1, "2": 1},
			UntrustedSource: []string{},
		},
		{
			FieldName:       "level",
			SourcePriority:  map[string]uint64{"1": 2, "2": 1},
			UntrustedSource: []string{},
		},
		{
			FieldName:       "name",
			SourcePriority:  map[string]uint64{"1": 2, "2": 1},
			UntrustedSource: []string{},
		},
		{
			FieldName:       "hasexp",
			SourcePriority:  map[string]uint64{"1": 2, "2": 1},
			UntrustedSource: []string{},
		},
		{
			FieldName:       "haspoc",
			SourcePriority:  map[string]uint64{"1": 2, "2": 1},
			UntrustedSource: []string{},
		},
		{
			FieldName:       "status",
			SourcePriority:  map[string]uint64{"1": 2, "2": 1},
			UntrustedSource: []string{},
		},
	}

	// Create executor
	executor := &VulnStrategyExecutor{
		SourceData: sourceData,
		Strategy:   strategy,
	}

	// Execute
	result, _ := executor.Execute()

	// Assertions
	assert.NotNil(t, result)
	assert.Equal(t, []uint64{1, 2}, result.AllSourceIds)
	assert.Equal(t, []uint64{1}, result.AllNodeIds)
	assert.Equal(t, []string{"task1", "task2"}, result.AllTaskDataIds)
	assert.Equal(t, []string{"id1", "id2"}, result.AllProcessIds)
	assert.Equal(t, 2, result.IsPoc)
	assert.Equal(t, "***********", result.Ip)
	assert.Equal(t, 8081, result.Port)
	assert.Equal(t, 1, result.Level)
	assert.Equal(t, "Test Vuln 2", result.Name)
	assert.Equal(t, 0, result.HasExp)
	assert.Equal(t, 0, result.HasPoc)
	assert.Equal(t, 0, result.Status)
}
