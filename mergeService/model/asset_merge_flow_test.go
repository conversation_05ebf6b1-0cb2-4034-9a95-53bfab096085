package model

import (
	"context"
	"fmt"
	"testing"

	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/pkg/cfg"
	"fobrain/pkg/queue"

	redis_helper "fobrain/models/redis"

	"github.com/alicebob/miniredis/v2"
	redis2 "github.com/go-redis/redis/v8"
	"github.com/olivere/elastic/v7"
	"github.com/stretchr/testify/assert"
)

func TestAssetMerge(t *testing.T) {
	cfg.InitLoadCfg()
	s := miniredis.RunT(t)
	defer s.Close()

	client := redis2.NewClient(&redis2.Options{Addr: s.Addr()})
	rq := &queue.RedisQueue{Client: client}

	qcfg := cfg.LoadQueue()
	for i := 0; i < 10; i++ {
		rq.Push(qcfg.AssetMergeQueue, []map[string]interface{}{{fmt.Sprintf("testKey-%d", i): "testValue"}})
	}
}

func TestGetAssetRelationData_Error(t *testing.T) {
	s, err := miniredis.Run()
	if err != nil {
		t.Fatalf("无法启动 miniredis: %v", err)
	}
	defer s.Close()

	cli := redis2.NewClient(&redis2.Options{
		Addr: s.Addr(),
	})
	testcommon.SetRedisClient(cli)

	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()
	cacheKey := redis_helper.AssetsProcessKey("ip_area_map_ids")
	var mapSet = make(map[string]string)
	mapSet["127.0.0.1_1"] = `["1","2","3"]`
	cli.HSet(context.Background(), cacheKey, mapSet)

	mockServer.Register("process_asset/_search", []*elastic.SearchHit{
		{
			Id:     "1",
			Source: []byte(`error json`),
		},
	})

	amf := &AssetMergeFlow{}
	data, total, err := amf.GetProcessDataList(context.Background(), "127.0.0.1", 1, []uint64{})

	assert.Error(t, err)
	assert.Equal(t, int64(0), total)
	assert.Nil(t, data)
}

func TestGetAssetRelationData_EmptyResult(t *testing.T) {
	s, err := miniredis.Run()
	if err != nil {
		t.Fatalf("无法启动 miniredis: %v", err)
	}
	defer s.Close()

	cli := redis2.NewClient(&redis2.Options{
		Addr: s.Addr(),
	})
	testcommon.SetRedisClient(cli)

	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()
	cacheKey := redis_helper.AssetsProcessKey("ip_area_map_ids")
	var mapSet = make(map[string]string)
	mapSet["127.0.0.1_1"] = `["1","2","3"]`
	cli.HSet(context.Background(), cacheKey, mapSet)

	mockServer.Register("process_asset/_search", []*elastic.SearchHit{
		{
			Id:     "",
			Source: []byte(``),
		},
	})

	amf := &AssetMergeFlow{}
	data, total, err := amf.GetProcessDataList(context.Background(), "127.0.0.1", 1, []uint64{})

	t.Log(err)
	//assert.NoError(t, err)
	assert.Equal(t, int64(0), total)
	assert.Len(t, data, 0)
}
