package model

import (
	"context"
	"testing"

	testcommon "fobrain/fobrain/tests/common_test"
	redis_helper "fobrain/models/redis"
	"fobrain/pkg/queue"

	"github.com/alicebob/miniredis/v2"
	redis2 "github.com/go-redis/redis/v8"
	"github.com/olivere/elastic/v7"
	"github.com/stretchr/testify/assert"
)

func TestGetMsgData_HappyPath(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	mockServer.Register("process_device/_doc/1", &elastic.GetResult{
		Id:     "1",
		Source: []byte(`{"id": "1", "ip": "127.0.0.1", "area": 1}`),
		Found:  true,
	})

	msg := queue.QueueMsg{
		Values: map[string]interface{}{
			"id": "1",
		},
		ID: "202408230927123-1",
	}

	data, err := GetProcessDataByMsg(msg)
	assert.NoError(t, err)
	assert.NotNil(t, data)
}

func TestGetMsgData_MissingID(t *testing.T) {

	s, err := miniredis.Run()
	if err != nil {
		t.Fatalf("无法启动 miniredis: %v", err)
	}
	defer s.Close()

	cli := redis2.NewClient(&redis2.Options{
		Addr: s.Addr(),
	})
	testcommon.SetRedisClient(cli)

	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	mockServer.Register("process_device/_doc/1", &elastic.GetResult{
		Id:     "1",
		Source: []byte(`{"id": "1", "ip": "127.0.0.1", "area": 1}`),
		Found:  true,
	})

	msg := queue.QueueMsg{
		Values: map[string]interface{}{
			"no_id": "1",
		},
		ID: "202408230927123-1",
	}

	data, err := GetProcessDataByMsg(msg)
	assert.Error(t, err)
	assert.Nil(t, data)
	assert.Equal(t, "消息中未包含id字段", err.Error())
}

func TestGetMsgData_ESGetByIdError(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	mockServer.Register("process_device/_doc/2", &elastic.GetResult{
		Id:     "2",
		Source: []byte(`{"id": "2", "ip": "127.0.0.1", "area": 1}`),
		Found:  false,
	})

	msg := queue.QueueMsg{
		Values: map[string]interface{}{
			"id": "2",
		},
		ID: "202408230927123-1",
	}

	data, err := GetProcessDataByMsg(msg)
	assert.Error(t, err)
	assert.Nil(t, data)
	assert.Equal(t, "record not found", err.Error())
}

func TestGetMsgData_IdTypeError(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	mockServer.Register("process_device/_search", []*elastic.SearchHit{
		{
			Id:     "1",
			Source: []byte(`{"id": "1", "ip": "127.0.0.1", "area": 1}`),
		},
	})

	msg := queue.QueueMsg{
		Values: map[string]interface{}{
			"id": false,
		},
		ID: "202408230927123-1",
	}

	data, err := GetProcessDataByMsg(msg)
	assert.Error(t, err)
	assert.Nil(t, data)
	assert.Equal(t, "id字段类型错误", err.Error())
}

func TestGetRelationData_HappyPath(t *testing.T) {
	s, err := miniredis.Run()
	if err != nil {
		t.Fatalf("无法启动 miniredis: %v", err)
	}
	defer s.Close()

	cli := redis2.NewClient(&redis2.Options{
		Addr: s.Addr(),
	})
	testcommon.SetRedisClient(cli)
	key := redis_helper.ProcessDeviceUniqueKeyToIDKey()
	cli.HSet(context.Background(), key, "123456789", `["1"]`)
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	mockServer.Register("/_mget", &elastic.MgetResponse{
		Docs: []*elastic.GetResult{
			{
				Id:     "1",
				Source: []byte(`{"id": "1", "unique_key": "123456789", "area": 1}`),
			},
		},
	})

	dmf := &DeviceMergeFlow{}
	data, count, err := dmf.GetRelationData(context.Background(), "123456789", 1)
	assert.NoError(t, err)
	assert.Equal(t, int64(1), count)
	assert.Equal(t, 1, len(data))
}

func TestGetRelationData_ESGetByIdError(t *testing.T) {
	s, err := miniredis.Run()
	if err != nil {
		t.Fatalf("无法启动 miniredis: %v", err)
	}
	defer s.Close()

	cli := redis2.NewClient(&redis2.Options{
		Addr: s.Addr(),
	})
	testcommon.SetRedisClient(cli)
	key := redis_helper.ProcessDeviceUniqueKeyToIDKey()
	cli.HSet(context.Background(), key, "123456789", `["1"]`)
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	mockServer.Register("process_device/_search", &elastic.Error{
		Status: 404,
		Details: &elastic.ErrorDetails{
			Type: "document_missing_exception",
		},
	})

	dmf := &DeviceMergeFlow{}
	data, count, err := dmf.GetRelationData(context.Background(), "123456789", 1)
	assert.Error(t, err)
	assert.Equal(t, int64(0), count)
	assert.Equal(t, 0, len(data))
}

func TestGetRelationData_ESSearchError(t *testing.T) {
	s, err := miniredis.Run()
	if err != nil {
		t.Fatalf("无法启动 miniredis: %v", err)
	}
	defer s.Close()

	cli := redis2.NewClient(&redis2.Options{
		Addr: s.Addr(),
	})
	testcommon.SetRedisClient(cli)
	key := redis_helper.ProcessDeviceUniqueKeyToIDKey()
	cli.HSet(context.Background(), key, "123456789", `["1"]`)

	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	mockServer.Register("/_mget", &elastic.Error{
		Status: 500,
		Details: &elastic.ErrorDetails{
			Type: "internal_server_error",
		},
	})

	dmf := &DeviceMergeFlow{}
	data, count, err := dmf.GetRelationData(context.Background(), "123456789", 1)
	assert.Error(t, err)
	assert.Equal(t, int64(0), count)
	assert.Equal(t, 0, len(data))
}

func TestGetRelationData_NoData(t *testing.T) {
	s, err := miniredis.Run()
	if err != nil {
		t.Fatalf("无法启动 miniredis: %v", err)
	}
	defer s.Close()

	cli := redis2.NewClient(&redis2.Options{
		Addr: s.Addr(),
	})
	testcommon.SetRedisClient(cli)
	key := redis_helper.ProcessDeviceUniqueKeyToIDKey()
	cli.HSet(context.Background(), key, "123456789", `["1"]`)
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	mockServer.Register("/_mget", &elastic.MgetResponse{
		Docs: []*elastic.GetResult{},
	})

	dmf := &DeviceMergeFlow{}
	data, count, err := dmf.GetRelationData(context.Background(), "123456789", 1)
	assert.NoError(t, err)
	assert.Equal(t, int64(0), count)
	assert.Equal(t, 0, len(data))
}
