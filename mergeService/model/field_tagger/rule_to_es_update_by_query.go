package field_tagger

import (
	"context"
	"fmt"
	"fobrain/mergeService/model/trigger_merge"
	"fobrain/models/elastic/assets"
	"fobrain/models/mysql/field_tag_rules"
	"log"

	logs "fobrain/mergeService/utils/log"

	"github.com/olivere/elastic/v7"
	"github.com/pkg/errors"
)

const (
	BusinessPath = "business.system"
	TitlePath    = "ports.title"
)

// buildUpdateScript 根据规则生成es脚本: elasticsearch update by query
// 返回值：脚本、是否需要刷新、错误
func (ft *FieldTagger) buildUpdateScript(r *field_tag_rules.SetFieldType) (*elastic.Script, bool, error) {
	// 构建更新脚本
	var script *elastic.Script
	// field := ft.extendFieldEsPath(r.SetField)
	field := r.SetField
	needRefresh := false
	// todo: 补充完整业务需要更新的字段
	switch field {
	// "product" 存在策略，可以写入对应的source字段，
	// "tag" 不存在策略，直接写入tag字段
	case "product", "tag":
		// 对于数组字段，追加并去重
		scriptContent := fmt.Sprintf(`
            if (ctx._source.%s == null) {
                ctx._source.%s = [params.setValue]
            } else if (!ctx._source.%s.contains(params.setValue)) {
                ctx._source.%s.add(params.setValue)
            }
        `, field, field, field, r.SetField)
		script = elastic.NewScriptInline(scriptContent).Lang("painless").Param("setValue", r.SetValue)
	case "business", "oper":
		script, err := trigger_merge.GetScriptForAppendData(r.SetField, r.SetValue)
		if err != nil {
			return nil, false, err
		}
		needRefresh = true
		return script, needRefresh, nil
	case TitlePath:
		return nil, false, fmt.Errorf("not impl：%s", r.SetField)
	default:
		return nil, false, fmt.Errorf("unknown field：%s", r.SetField)
	}

	return script, needRefresh, nil
}

func (ft *FieldTagger) esUpdateByScript(ctx context.Context, updateScript *elastic.Script) (v int64, err error) {
	defer func() {
		if e := recover(); e != nil {
			err = errors.New(fmt.Sprintf("panic: %v", e))
		}
	}()
	fmt.Printf("=============%+v", updateScript)
	resp, err := ft.getElasticClient().UpdateByQuery(assets.NewAssets().IndexName()).
		Script(updateScript).Refresh("true").Do(ctx)
	if err != nil {
		return 0, err
	}
	log.Println("esUpdateByQuery updated:", resp.Updated)
	return resp.Updated, nil
}

// extendFieldEsPath 根据查询字段扩展为完整的es path
func (ft *FieldTagger) extendFieldEsPath(field string) string {
	// todo: 根据字段名称扩展为完整的es field path
	switch field {
	case "business": // 业务系统
		return BusinessPath
	case "title": // 标题
		return TitlePath
	}
	return field
}

func (ft *FieldTagger) esUpdateByQuery(ctx context.Context, rule *field_tag_rules.SetFieldType, updateScript *elastic.Script) (v int64, err error) {
	logs := logs.GetLogger("service")
	defer func() {
		if e := recover(); e != nil {
			err = errors.New(fmt.Sprintf("panic: %v", e))
		}
	}()
	//s, _ := updateScript.Source()
	//logs.Infof("esUpdateByQuery update script: %s", utils.AnyToStr(s))
	resp, err := ft.getElasticClient().UpdateByQuery(assets.NewAssets().IndexName()).
		Query(elastic.NewBoolQuery().Must(
			elastic.NewTermQuery(
				// ft.extendFieldEsPath(rule.Field),
				rule.Field,
				rule.Value),
		)).
		Script(updateScript).Refresh("true").Do(ctx)
	if err != nil {
		logs.Errorf("esUpdateByQuery updated:", err)
		return 0, err
	}
	logs.Debugf("esUpdateByQuery updated:", resp.Updated)
	return resp.Updated, nil
}
