package field_tagger

import (
	"context"
	"log"
	"testing"

	"fobrain/models/mysql/field_tag_rules"
)

func TestNew(t *testing.T) {
	// mockServer := testcommon.NewMockServer()
	// defer mockServer.Close()
	// mockDb := testcommon.InitSqlMock()
	// defer mockDb.Close()

	// // Setup mock redis
	// s, err := miniredis.Run()
	// if err != nil {
	// 	t.Fatal(err)
	// }
	// defer s.Close()
	// client := redis2.NewClient(&redis2.Options{Addr: s.Addr()})
	// testcommon.SetRedisClient(client)

	// mockDb.ExpectQuery("SELECT * FROM `strategy` WHERE business_type =? and field_name =? ORDER BY version DESC,`strategy`.`id` LIMIT 1").
	// 	WithArgs("asset_merge", "business").
	// 	WillReturnRows(
	// 		mockDb.NewRows([]string{"id", "business_type", "field_name", "version"}).AddRow(1, "business", "asset_merge", 1),
	// 	)

	// mockServer.Register("/asset/_update_by_query", &elastic.BulkIndexByScrollResponse{
	// 	Updated: 1,
	// })

	// // 没有设置 WithGetFiledTagRules
	// assert.Panics(t, func() {
	// 	ft := New()
	// 	ft.Run(context.Background(), func(offset, allRules int, rule *field_tag_rules.SetFieldType, updated int64) {
	// 		log.Println(offset, allRules, rule, updated)
	// 	})
	// })

	// // GetFiledTagRules返回错误
	// {
	// 	ft := New(WithGetFiledTagRules(func() ([]*field_tag_rules.SetFieldType, error) {
	// 		return nil, errors.New("not impl")
	// 	}))
	// 	assert.Error(t, ft.Run(context.Background(), func(offset, allRules int, rule *field_tag_rules.SetFieldType, updated int64) {
	// 		log.Println(offset, allRules, rule, updated)
	// 	}))
	// }

	// // GetFiledTagRules 不支持的字段
	// {
	// 	ft := New(WithGetFiledTagRules(func() ([]*field_tag_rules.SetFieldType, error) {
	// 		return []*field_tag_rules.SetFieldType{
	// 			{
	// 				Field:    "ip",
	// 				Value:    "**********/24",
	// 				SetField: "aaa",
	// 			},
	// 		}, nil
	// 	}))
	// 	assert.NoError(t, ft.Run(context.Background(), func(offset, allRules int, rule *field_tag_rules.SetFieldType, updated int64) {
	// 		log.Println(offset, allRules, rule, updated)
	// 	}))
	// }

	// // GetFiledTagRules 语法错误
	// {
	// 	ft := New(WithGetFiledTagRules(func() ([]*field_tag_rules.SetFieldType, error) {
	// 		return []*field_tag_rules.SetFieldType{
	// 			{
	// 				Field:    "ip",
	// 				Value:    "**********/24",
	// 				SetField: "product",
	// 				SetValue: `aaa`,
	// 			},
	// 		}, nil
	// 	}),
	// 		WithGetElasticClient(nil), // 构造es错误
	// 	)
	// 	assert.NoError(t, ft.Run(context.Background(), func(offset, allRules int, rule *field_tag_rules.SetFieldType, updated int64) {
	// 		log.Println(offset, allRules, rule, updated)
	// 	}))
	// }

	// // 成功
	// ft := New(WithGetFiledTagRules(func() ([]*field_tag_rules.SetFieldType, error) {
	// 	return []*field_tag_rules.SetFieldType{
	// 		{
	// 			Field:    "ip",
	// 			Value:    "**********/24",
	// 			SetField: "business",
	// 			SetValue: `{
	//                             "owner": "彭新怡",
	//                             "system": "ERP系统",
	//                             "business_info": {
	//                                 "fid": "3a44047b26e3402eb7b7da3365ac1da1",
	//                                 "business_name": "ERP系统",
	//                                 "address": "***********",
	//                                 "business": {
	//                                     "business_department_name": [
	//                                         "北京华顺信安科技有限公司/人力资源部/人力行政中心"
	//                                     ],
	//                                     "business_oper": "ef18dcb507a1396b8489d433b54d09f2",
	//                                     "business_department": null,
	//                                     "business_oper_name": "彭新怡",
	//                                     "business_oper_not_found": ""
	//                                 },
	//                                 "person_limit": [
	//                                     "彭新怡:***********"
	//                                 ],
	//                                 "created_at": "2024-11-18 19:07:13",
	//                                 "assets_attribute": {
	//                                     "purchase_type": 1,
	//                                     "important_types": 1,
	//                                     "is_gj": 1,
	//                                     "operating_env": 1,
	//                                     "running_state": 1,
	//                                     "insurance_level": 1,
	//                                     "is_xc": 1
	//                                 },
	//                                 "deleted_at": null,
	//                                 "ips": [
	//                                     "***********1"
	//                                 ],
	//                                 "person_limit_hash": [
	//                                     "ef18dcb507a1396b8489d433b54d09f2"
	//                                 ],
	//                                 "updated_at": "2024-11-18 19:07:13",
	//                                 "system_version": "",
	//                                 "tag_id": 0,
	//                                 "id": "3a44047b26e3402eb7b7da3365ac1da1"
	//                             },
	//                             "system_id": "3a44047b26e3402eb7b7da3365ac1da1",
	//                             "owner_id": "ef18dcb507a1396b8489d433b54d09f2",
	//                             "source": "rule",
	//                             "addition": ""
	//                         }`,
	// 		},
	// 	}, nil
	// }))
	// assert.NoError(t, ft.Run(context.Background(), func(offset, allRules int, rule *field_tag_rules.SetFieldType, updated int64) {
	// 	assert.True(t, updated > 0)
	// 	log.Println(offset, allRules, rule, updated)
	// }))
	// // 未实现
	// ft = New(WithGetFiledTagRules(func() ([]*field_tag_rules.SetFieldType, error) {
	// 	return []*field_tag_rules.SetFieldType{
	// 		{
	// 			Field:    "ip",
	// 			Value:    "**********/24",
	// 			SetField: "business",
	// 			SetValue: "fofa",
	// 		},
	// 	}, nil
	// }))
	// assert.NoError(t, ft.Run(context.Background(), func(offset, allRules int, rule *field_tag_rules.SetFieldType, updated int64) {
	// 	log.Println(offset, allRules, rule, updated)
	// }))

	// // 未实现
	// ft = New(WithGetFiledTagRules(func() ([]*field_tag_rules.SetFieldType, error) {
	// 	return []*field_tag_rules.SetFieldType{
	// 		{
	// 			Field:    "ip",
	// 			Value:    "**********/24",
	// 			SetField: "title",
	// 			SetValue: "fofa",
	// 		},
	// 	}, nil
	// }))
	// assert.NoError(t, ft.Run(context.Background(), func(offset, allRules int, rule *field_tag_rules.SetFieldType, updated int64) {
	// 	log.Println(offset, allRules, rule, updated)
	// }))

	// // GetFiledTagRules 语法错误
	// {
	// 	mockServer1 := testcommon.NewMockServer()
	// 	defer mockServer1.Close()

	// 	mockServer1.RegisterHandler("/asset/_update_by_query", func(writer http.ResponseWriter, request *http.Request) {
	// 		writer.WriteHeader(404)
	// 	})
	// 	ft := New(WithGetFiledTagRules(func() ([]*field_tag_rules.SetFieldType, error) {
	// 		return []*field_tag_rules.SetFieldType{
	// 			{
	// 				Field:    "ip",
	// 				Value:    "**********/24",
	// 				SetField: "product",
	// 				SetValue: `aaa`,
	// 			},
	// 		}, nil
	// 	}))
	// 	assert.NoError(t, ft.Run(context.Background(), func(offset, allRules int, rule *field_tag_rules.SetFieldType, updated int64) {
	// 		log.Println(offset, allRules, rule, updated)
	// 	}))
	// }
}

func TestNew_Real(t *testing.T) {
	// 执行真实的测试
	//ft := New(WithGetFiledTagRules(func() ([]*field_tag_rules.SetFieldType, error) {
	//	return []*field_tag_rules.SetFieldType{
	//		{
	//			Field:    "ip",
	//			Value:    "**********/24",
	//			SetField: "product",
	//			SetValue: "foeye",
	//		},
	//	}, nil
	//}), WithGetElasticClient(func() *elastic.Client {
	//	c, err := elastic.NewClient(elastic.SetURL("http://************:9200"))
	//	assert.NoError(t, err)
	//	return c
	//}))
	//assert.NoError(t, ft.Run(context.Background(), func(offset, allRules int, rule *field_tag_rules.SetFieldType, updated int64) {
	//	assert.True(t, updated > 0)
	//	log.Println(offset, allRules, rule, updated)
	//}))
}

func Example() {
	// 创建一个FieldTagger
	ft := New(WithGetFiledTagRules(func() ([]*field_tag_rules.SetFieldType, error) {
		return []*field_tag_rules.SetFieldType{
			{
				Field:    "ip",
				Value:    "**********/24",
				SetField: "product",
				SetValue: "fofa",
			},
		}, nil
	}))

	// 运行
	err := ft.Run(context.Background(), func(offset, allRules int, rule *field_tag_rules.SetFieldType, updated int64) {
		log.Println(offset, allRules, rule, updated)
	})
	if err != nil {
		log.Println(err)
	}
}
