package field_tagger

// func TestExecute_Tag(t *testing.T) {
// testcommon.SetTestEnv(false)
// cfg.InitLoadCfg()

// // 删除资产表数据
// // 往资产表插入一条数据
// asset := assets.NewAssets()
// _, err := asset.Insert(context.Background(), &assets.Assets{
// 	Ids:   "1",
// 	Ip:   "***********",
// 	Area: 1,
// 	Tags: []string{},
// })
// assert.Nil(t, err)

// updatedResult := "init"
// ft := field_tagger.New()
// ft.Execute(context.Background(), []*field_tag_rules.SetFieldType{
// 	{
// 		Field:    "ip",
// 		Value:    "***********",
// 		SetField: "tag",
// 		SetValue: "标签-1",
// 	},
// }, func(runOffset int, allRules int, rule *field_tag_rules.SetFieldType, updated int64) {
// 	updatedResult = fmt.Sprintf("%v", updated)
// })
// // 验证回调是否被调用
// assert.Equal(t, "1", updatedResult)

// // 查询资产表，验证tag字段是否被更新
// asset, err = asset.First(context.Background(), "***********", "1")
// assert.Nil(t, err)
// assert.Equal(t, []string{"标签-1"}, asset.Tags)
// }
