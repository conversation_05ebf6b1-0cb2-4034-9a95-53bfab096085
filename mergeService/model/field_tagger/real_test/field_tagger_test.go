package field_tagger

// import (
// 	"context"
// 	"fmt"
// 	"fobrain/models/mysql/field_tag_rules"
// 	"testing"

// 	"github.com/stretchr/testify/assert"
// )

// func TestExecute_Real(t *testing.T) {
// 	var updatedResult string
// 	ft := New()
// 	ft.Execute(context.Background(), []*field_tag_rules.SetFieldType{
// 		{
// 			Field:    "ip",
// 			Value:    "**********/24",
// 			SetField: "tag",
// 			SetValue: "标签-1",
// 		},
// 	}, func(runOffset int, allRules int, rule *field_tag_rules.SetFieldType, updated int64) {
// 		updatedResult = fmt.Sprintf("%v", updated)
// 	})
// 	assert.Equal(t, updatedResult, "1")
// }
