// Package field_tagger 属性打标签模块
//
// 根据用户自定义的打标签规则实现属性字段的入库，如果field是唯一就替换，否则就是数组那么就要追加，然后去重
//
// 参考：[属性打标签特性]
//
// [属性打标签特性]: https://git.gobies.org/caasm/fobrain/-/issues/1329
package field_tagger

import (
	"context"
	"fmt"
	"fobrain/mergeService/model/trigger_merge"
	logs "fobrain/mergeService/utils/log"
	"fobrain/models/elastic/assets"
	"fobrain/models/mysql/field_tag_rules"
	"fobrain/pkg/utils/common_logs"
	"time"

	"github.com/olivere/elastic/v7"
)

var logger *common_logs.Logger

func init() {
	logger = logs.GetLogger("service")
}

// FieldTaggerRunOption 执行过程中的通知回调
//
// 参数：
//   - runOffset 执行规则的偏移
//   - allRules 执行规则的总数
//   - rule 当前执行的规则
//   - updated 当前执行的规则更新的资产数量
type FieldTaggerRunOption func(runOffset int, allRules int, rule *field_tag_rules.SetFieldType, updated int64)

// Run 执行打标签
// 可选设置通知回调，比如想要获取执行的进度，可以如下方式调用：
//
//	ft := New()
//	ft.Run(context.Background(), func(runOffset, allRules int, rule *FieldTagRule, updated int64) {
//	  log.Println(offset, allRules, rule, updated)
//	})
func (ft *FieldTagger) Run(ctx context.Context, opts ...FieldTaggerRunOption) error {
	logger.Infof("开始执行打标签")
	// 获取规则列表
	rules, err := ft.getFiledTagRules()
	if err != nil {
		return err
	}
	logger.Infof("开始执行打标签，获取完规则")
	return ft.Execute(ctx, rules, opts...)
}

// TriggerOnce 执行一次打标签
func (ft *FieldTagger) TriggerOnce(ctx context.Context, rule *field_tag_rules.SetFieldType) error {
	return ft.Execute(ctx, []*field_tag_rules.SetFieldType{rule})
}

// Execute 执行打标签
//
// 参数：
//   - ctx 上下文
//   - rules 规则列表
//   - opts 执行过程中的通知回调
func (ft *FieldTagger) Execute(ctx context.Context, rules []*field_tag_rules.SetFieldType, opts ...FieldTaggerRunOption) error {
	// 生成和执行es的批量更新脚本
	allRules := len(rules)
	logger.Infof("开始执行打标签，规则总数：%d", allRules)
	if nil == rules || allRules == 0 {
		return nil
	}
	var ips []*trigger_merge.IpInfo
	for _, rule := range rules {
		query := elastic.NewBoolQuery().Must(
			elastic.NewTermQuery(
				rule.Field,
				rule.Value,
			),
		)
		allByQuery, _ := assets.NewAssets().FindAllByQuery(ctx, query, nil)

		for _, asset := range allByQuery {
			ips = append(ips, &trigger_merge.IpInfo{
				Ip:   asset.Ip,
				Area: uint64(asset.Area),
			})
		}
	}
	err := trigger_merge.TriggerMergeForAsset(&trigger_merge.TriggerParamsForAsset{
		IpInfos: ips,
		TriggerParams: trigger_merge.TriggerParams{
			TriggerSourceId: 0,
			TriggerNodeId:   0,
			TriggerEvent:    "资产融合",
			TaskId:          fmt.Sprintf("field_tagger_merge_by_asset_%s", fmt.Sprintf("%d", time.Now().Unix())),
			ChildTaskId:     fmt.Sprintf("%d", time.Now().Unix()),
		},
	})
	if err != nil {
		logger.Warnf("打标签触发融合任务失败 err : %v", err)
	}
	return nil
}
