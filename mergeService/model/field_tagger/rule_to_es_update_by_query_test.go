package field_tagger

import (
	"encoding/json"
	testcommon "fobrain/fobrain/tests/common_test"
	"github.com/olivere/elastic/v7"
	"testing"

	"github.com/stretchr/testify/assert"

	"fobrain/initialize/es"
	"fobrain/models/elastic/assets"
	"fobrain/models/mysql/field_tag_rules"
)

func Test_buildUpdateScript(t *testing.T) {
	t.Run("tag", func(t *testing.T) {
		ft := New(WithGetElasticClient(es.GetEsClient), WithGetFiledTagRules(field_tag_rules.NewFieldTagRuleConfig().GetFiledTagRules))
		rule := &field_tag_rules.SetFieldType{
			SetField: "tag",
			SetValue: "标签-1",
		}
		script, needRefresh, err := ft.buildUpdateScript(rule)
		assert.NoError(t, err)
		assert.False(t, needRefresh)
		assert.NotNil(t, script)
	})

	t.Run("oper", func(t *testing.T) {
		ft := New(WithGetElasticClient(es.GetEsClient), WithGetFiledTagRules(field_tag_rules.NewFieldTagRuleConfig().GetFiledTagRules))
		rule := &field_tag_rules.SetFieldType{
			SetField: "oper",
			SetValue: "彭新怡",
		}
		script, needRefresh, err := ft.buildUpdateScript(rule)
		assert.NoError(t, err)
		assert.True(t, needRefresh)
		assert.NotNil(t, script)
	})

	t.Run("business", func(t *testing.T) {
		ft := New(WithGetElasticClient(es.GetEsClient), WithGetFiledTagRules(field_tag_rules.NewFieldTagRuleConfig().GetFiledTagRules))
		assetBusiness := &assets.Business{
			Source: "rule",
			System: "test",
			Owner:  "test",
		}
		json, err := json.Marshal(assetBusiness)
		assert.NoError(t, err)
		rule := &field_tag_rules.SetFieldType{
			SetField: "business",
			SetValue: string(json),
		}
		mockServer := testcommon.NewMockServer()
		mockServer.Register("_scripts/script_append_data_business_id", &elastic.PutScriptResponse{
			AcknowledgedResponse: elastic.AcknowledgedResponse{
				Acknowledged: true,
			},
		})

		script, needRefresh, err := ft.buildUpdateScript(rule)
		assert.NoError(t, err)
		assert.True(t, needRefresh)
		assert.NotNil(t, script)
	})
}
