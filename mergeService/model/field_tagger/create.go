package field_tagger

import (
	"fobrain/initialize/es"
	"fobrain/models/mysql/field_tag_rules"
)

// FieldTagger 属性打标签管理器
type FieldTagger struct {
	getFiledTagRules func() ([]*field_tag_rules.SetFieldType, error)
	getElasticClient func() *es.SafeClient
}

type FieldTaggerOption func(*FieldTagger)

// New 创建一个属性打标签管理器
func New(opts ...FieldTaggerOption) *FieldTagger {
	ft := &FieldTagger{
		getElasticClient: es.GetEsClient,
	}
	for _, opt := range opts {
		opt(ft)
	}
	return ft
}

// WithGetFiledTagRules 设置获取打标签规则的方法，测试用
func WithGetFiledTagRules(getFiledTagRules func() ([]*field_tag_rules.SetFieldType, error)) FieldTaggerOption {
	return func(ft *FieldTagger) {
		ft.getFiledTagRules = getFiledTagRules
	}
}

// WithGetElasticClient 设置elastic连接客户端
func WithGetElasticClient(getElasticClient func() *es.SafeClient) FieldTaggerOption {
	return func(ft *FieldTagger) {
		ft.getElasticClient = getElasticClient
	}
}
