// 配合漏洞闭环，更新漏洞信息
package model

import (
	"context"
	"fmt"
	"fobrain/fobrain/common/localtime"
	"fobrain/initialize/es"
	"fobrain/mergeService/event"
	logs "fobrain/mergeService/utils/log"
	models "fobrain/models/elastic"
	"fobrain/models/elastic/assets"
	"fobrain/models/elastic/helper"
	esmodel_poc "fobrain/models/elastic/poc"
	esmodel_staff "fobrain/models/elastic/staff"
	"fobrain/models/mysql/strategy"
	"fobrain/pkg/cfg"
	"fobrain/pkg/queue"
	"fobrain/pkg/utils"
	"fobrain/pkg/utils/common_logs"
	"strconv"
	"strings"
	"time"

	"github.com/olivere/elastic/v7"
)

type VulnUpdateFlow struct {
	mlog      *common_logs.Logger
	groupName string
	qName     string
}

func NewVulnUpdateFlow() *VulnUpdateFlow {
	return &VulnUpdateFlow{
		mlog:      logs.GetLogger("vuln_update"),
		qName:     cfg.LoadQueue().VulnUpdateQueue,
		groupName: "vuln_update_flow",
	}
}

type UpdateVuln struct {
	Msg    queue.QueueMsg
	Record *esmodel_poc.MergeRecords
	Poc    *esmodel_poc.Poc
}

var (
	// 漏洞更新记录写入通道
	vulnUpdateRecordChan = make(chan *UpdateVuln)
	// 漏洞更新结果写入通道
	vulnUpdateResultChan = make(chan *UpdateVuln)
)

// Update 更新漏洞信息
func (vuf *VulnUpdateFlow) Update() {
	mlog := vuf.mlog
	// 最大并发数量
	var maxConcurrency = cfg.LoadQueue().VulnUpdateConcurrent

	msgChan, consumerName, err := queue.NewQueue(queue.QueueType_Redis).Subscribe(vuf.qName, vuf.groupName, maxConcurrency)
	mlog.Info("订阅漏洞更新消息. qName: ", vuf.qName, " consumerName: ", consumerName)
	if err != nil {
		mlog.Error("订阅漏洞更新消息失败. qName: ", vuf.qName, " err: ", err)
		return
	}
	// defer queue.NewQueue(queue.QueueType_Redis).Unsubscribe(qName, consumerName)
	eb := event.NewEventBus()

	// 开启融合结果写入协程
	go vuf.WriteResult()
	// 开启融合记录写入协程
	go vuf.WriteRecord()
	sem := make(chan struct{}, maxConcurrency)
	go func(sem chan struct{}) {
		for msg := range msgChan {
			sem <- struct{}{}
			go func(m queue.QueueMsg) {
				defer func() {
					if r := recover(); r != nil {
						mlog.Errorf("漏洞更新消息处理失败. msg: %v, err: %v", m, r)
					}
					// 释放一个处理能力
					mlog.Debug("释放一个处理能力")
					<-sem
				}()
				now := time.Now()
				mlog.Infof("收到漏洞更新消息. msg: %v", m)
				// 构建更新记录-消息
				ma := &UpdateVuln{
					Msg: m,
					Record: &esmodel_poc.MergeRecords{
						Id:        UUIDStr(),
						CreatedAt: localtime.NewLocalTime(now),
						MergeMode: models.MergeMode_Update,
					},
				}
				idStr, personFid, ccPersonFid, statusInt, limitDateStr, err := vuf.getInfoFromMsg(m)
				if err != nil {
					vuf.failedHandle(fmt.Sprintf("从消息中获取漏洞id、人员信息、状态信息失败. msg: %v, err: %v", m, err), m)
					return
				}

				// 根据id查询漏洞
				data, err := es.GetById[esmodel_poc.Poc](idStr)
				if err != nil {
					vuf.failedHandle(fmt.Sprintf("根据id查询漏洞失败. msg: %v, id: %s, err: %v", m, idStr, err), m)
					return
				}
				if data == nil {
					vuf.failedHandle(fmt.Sprintf("根据id查询漏洞为空. msg: %v, id: %v", m, idStr), m)
					return
				}
				if personFid != "" {
					// 获取人员信息
					person, err := es.First[esmodel_staff.Staff](elastic.NewBoolQuery().Must(elastic.NewTermQuery("fid", personFid)), nil)
					if err != nil {
						vuf.failedHandle(fmt.Sprintf("根据fid查询人员失败. msg: %v, fid: %v, err: %v", m, personFid, err), m)
						return
					}
					if person == nil {
						vuf.failedHandle(fmt.Sprintf("根据fid查询人员为空. msg: %v, fid: %v", m, personFid), m)
						return
					}

					data.RepairDepartmentIds = make([]uint64, 0)

					// 获取人员部门
					var departments []*assets.DepartmentBase
					for _, departmentId := range person.DepartmentsIds {
						departmentIdInt, err := strconv.ParseUint(departmentId, 10, 64)
						if err != nil {
							vuf.failedHandle(fmt.Sprintf("获取人员部门失败. msg: %v, fid: %v, err: %v", m, personFid, err), m)
							return
						}
						department := helper.GetDepartment(departmentIdInt, person.Name, person.Id, "", "")
						if department == nil {
							vuf.failedHandle(fmt.Sprintf("获取人员部门失败. msg: %v, fid: %v", m, personFid), m)
							return
						}
						//人员部门id
						data.RepairDepartmentIds = append(data.RepairDepartmentIds, department.Id)
						//人员部门父级id
						for _, d := range department.Parents {
							data.RepairDepartmentIds = append(data.RepairDepartmentIds, d.Id)
						}
						departments = append(departments, department)
					}
					data.RepairDepartmentIds = utils.ListDistinctNonZero(data.RepairDepartmentIds)
					data.PersonInfo = make([]*assets.PersonBase, 0)
					data.PersonDepartment = make([]*assets.DepartmentBase, 0)
					pb := assets.NewPersonBase()
					pb.Id = person.Id
					pb.Name = person.Name
					pb.Fid = personFid
					pb.Department = departments
					data.PersonInfo = append(data.PersonInfo, pb)
					data.PersonDepartment = append(data.PersonDepartment, departments...)
					// 人员信息更新，当前人员
					data.Person = map[string]string{
						"fid":    personFid,
						"name":   person.Name,
						"mobile": person.Mobile,
					}
					// 更新历史人员
					data.PersonLimit = append(data.PersonLimit, personFid)
					data.PersonLimit = utils.ListDistinctNonZero(data.PersonLimit)
					data.PersonLimitHash = append(data.PersonLimitHash, utils.Md5Hash(personFid))
					data.PersonLimitHash = utils.ListDistinctNonZero(data.PersonLimitHash)
				}
				if ccPersonFid != "" {
					// 更新抄送人员
					name := ""
					mobile := ""
					personInfo := strings.Split(ccPersonFid, ":")
					if len(personInfo) == 2 {
						name = personInfo[0]
						mobile = personInfo[1]
					}
					// 人员信息更新，当前人员
					data.CcPerson = map[string]string{
						"fid":    ccPersonFid,
						"name":   name,
						"mobile": mobile,
					}
					// 更新历史人员
					data.PersonLimit = append(data.PersonLimit, ccPersonFid)
					data.PersonLimit = utils.ListDistinctNonZero(data.PersonLimit)
					data.PersonLimitHash = append(data.PersonLimitHash, utils.Md5Hash(ccPersonFid))
					data.PersonLimitHash = utils.ListDistinctNonZero(data.PersonLimitHash)
				}
				if statusInt != -1 {
					// 更新漏洞状态
					data.Status = statusInt
					data.StatusChangeTime = localtime.NewLocalTime(time.Now())
					// 通过"漏洞更新队列"更新的漏洞，不记录历史
				}
				if limitDateStr != "" {
					// 尝试把修复超期时间转换为时间，支持多种格式
					var limitDate time.Time
					var err error
					var parseSuccess bool

					// 先尝试只包含日期的格式
					limitDate, err = time.Parse("2006-01-02", limitDateStr)
					if err != nil {
						// 如果失败，尝试包含时间的格式
						limitDate, err = time.Parse("2006-01-02 15:04:05", limitDateStr)
						if err != nil {
							mlog.Warnf("修复超期时间转换失败. limitDateStr: %v, err: %v", limitDateStr, err)
							parseSuccess = false
						} else {
							parseSuccess = true
						}
					} else {
						parseSuccess = true
					}

					// 只有解析成功时才设置LimitDate
					if parseSuccess {
						data.LimitDate = localtime.NewLocalTime(limitDate)
					}
				}
				if data.Status == esmodel_poc.PocStatusOfRepaired || data.Status == esmodel_poc.PocStatusOfErrorReport || data.Status == esmodel_poc.PocStatusOfCantRepaired {
					// 漏洞状态为待重测，更新重测时间
					data.ProcessedTime = localtime.NewLocalTime(now)
				}
				data.UpdatedAt = localtime.NewLocalTime(now)
				ma.Poc = data

				// 构建融合记录-关联数据
				ma.Record.PocId = data.Id
				ma.Record.Cve = data.Cve
				ma.Record.Cnvd = data.Cnvd
				ma.Record.Cnnvd = data.Cnnvd
				ma.Record.OriginalIds = data.OriginalIds
				// 构建融合记录-策略
				s := strategy.NewStrategyModel()
				s.BusinessType = "vuln"
				s.FieldName = "status"
				s.DisplayName = "漏洞状态"
				s.SourcePriority = strategy.SourcePriority{
					"漏洞闭环": 1,
				}
				s.UntrustedSource = strategy.UntrustedSource{}
				ma.Record.Strategies = []*strategy.Strategy{
					s,
				}
				// 发送漏洞更新事件
				err = eb.Emit(event.Event_Vuln_UpdateData, ma.Poc)
				if err != nil {
					mlog.Warnf("发送漏洞更新事件失败. err: %v", err)
				}
				// 写入更新结果
				vulnUpdateResultChan <- ma

				duration := time.Now().UnixMilli() - now.UnixMilli()
				mlog.Infof("漏洞更新消息处理完成. msg: %v,耗时: %vms", m, duration)
			}(msg)
		}
	}(sem)
}

func (vmf *VulnUpdateFlow) failedHandle(errMsg string, m queue.QueueMsg) {
	vmf.mlog.Warnf(errMsg)
	// 确认消息已处理
	queue.NewQueue(queue.QueueType_Redis).Ack(vmf.qName, vmf.groupName, m.ID)
}

// getInfoFromMsg 从消息中获取漏洞id、人员信息、状态信息
func (vuf *VulnUpdateFlow) getInfoFromMsg(m queue.QueueMsg) (idStr string, personStr string, ccPersonStr string, statusInt int, limitDateStr string, err error) {
	// 根据消息组装获取关联数据的条件
	if len(m.Values) < 1 {
		return "", "", "", -1, "", fmt.Errorf("漏洞融合消息中未包含搜索条件. msg: %v", m)
	}
	// 获取人员信息
	person, ok := m.Values["person"]
	if ok {
		personStr, ok = person.(string)
		if !ok {
			return "", "", "", -1, "", fmt.Errorf("漏洞更新消息中人员信息无法解析. msg: %v", m)
		}
	}
	// 获取抄送人员信息
	ccPerson, ok := m.Values["cc_person"]
	if ok {
		ccPersonStr, ok = ccPerson.(string)
		if !ok {
			return "", "", "", -1, "", fmt.Errorf("漏洞更新消息中抄送人员信息无法解析. msg: %v", m)
		}
	}
	// 获取状态信息
	status, ok := m.Values["status"]
	if ok {
		switch v := status.(type) {
		case string:
			statusInt, err = strconv.Atoi(v)
			if err != nil {
				return "", "", "", -1, "", fmt.Errorf("漏洞更新消息中状态信息无法解析. msg: %v", m)
			}
		case int:
			statusInt = v
		default:
			return "", "", "", -1, "", fmt.Errorf("漏洞更新消息中状态信息无法解析. msg: %v", m)
		}
	}
	// 如果人员信息和状态信息都为空，则不进行更新
	if person == nil && status == nil {
		return "", "", "", -1, "", fmt.Errorf("漏洞更新消息中未包含人员信息或状态信息. msg: %v", m)
	}
	// 获取漏洞id
	id, ok := m.Values["id"]
	if !ok {
		return "", "", "", -1, "", fmt.Errorf("漏洞更新消息中未包含id. msg: %v", m)
	}
	idStr, ok = id.(string)
	if !ok {
		return "", "", "", -1, "", fmt.Errorf("漏洞更新消息中id信息无法解析. msg: %v", m)
	}
	// 验证漏洞ID是否为空或无效
	if strings.TrimSpace(idStr) == "" {
		return "", "", "", -1, "", fmt.Errorf("漏洞更新消息中id为空. msg: %v", m)
	}
	// 获取修复超期时间
	limitDate := ""
	limitDateVal, ok := m.Values["limit_date"]
	if ok {
		limitDate = fmt.Sprintf("%v", limitDateVal)
	}
	return idStr, personStr, ccPersonStr, statusInt, limitDate, nil
}

// WriteResult 写入更新结果
func (vuf *VulnUpdateFlow) WriteResult() {
	mlog := vuf.mlog
	for v := range vulnUpdateResultChan {
		// 写入结果
		mlog.Debugf("开始写入漏洞更新结果. result: %v", *v)
		err := es.CreateOrUpdate[esmodel_poc.Poc](*v.Poc)
		if err != nil {
			errMsg := fmt.Sprintf("写入漏洞更新结果失败.msg: %v, err: %v", v.Msg, err)
			mlog.Warn(errMsg)
			v.Record.Status = 2
			v.Record.Message = errMsg
			vulnUpdateRecordChan <- v
			continue
		}
		pocRecordId := UUIDStr()
		pocRecord := &esmodel_poc.PocRecord{Poc: v.Poc}
		pocRecord.Poc.Id = pocRecordId
		_, err = esmodel_poc.NewPocRecord().Insert(context.Background(), pocRecord)
		if err != nil {
			errMsg := fmt.Sprintf("写入漏洞版本失败.msg: %v, err: %v", v.Msg, err)
			mlog.Warn(errMsg)
			v.Record.Status = 2
			v.Record.Message = errMsg
			vulnUpdateRecordChan <- v
			continue
		}
		v.Record.Status = 1
		v.Record.PocRecordId = pocRecordId
		vulnUpdateRecordChan <- v
	}
}

// WriteRecord 写入更新记录
func (vuf *VulnUpdateFlow) WriteRecord() {
	mlog := vuf.mlog
	for v := range vulnUpdateRecordChan {
		mlog.Debugf("开始写入漏洞更新记录. result: %v", *v.Record)
		rsp, err := esmodel_poc.NewMergeRecordsModel().Insert(context.Background(), v.Record)
		if err != nil {
			mlog.Warnf("写入漏洞更新记录失败. err: %v", err)
		}
		mlog.Debugf("写入漏洞更新记录成功. rsp: %v", rsp)
		// ack
		queue.NewQueue(queue.QueueType_Redis).Ack(cfg.LoadQueue().VulnUpdateQueue, vuf.groupName, v.Msg.ID)
	}
}
