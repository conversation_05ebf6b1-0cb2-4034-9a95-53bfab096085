package business_extract

import (
	"encoding/json"
	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/models/elastic/assets"
	poc2 "fobrain/models/elastic/poc"
	"reflect"
	"testing"

	"github.com/olivere/elastic/v7"
	"github.com/stretchr/testify/assert"
)

//
//func TestAssetMergeTaskEndHandler(t *testing.T) {
//	// 初始化SQL模拟
//	mockDb := testcommon.InitSqlMock()
//	defer mockDb.Close()
//
//	// 模拟查询返回1条记录
//	mockDb.ExpectQuery("SELECT count(*) FROM `field_tag_rules` WHERE `set_field` IN (?)").
//		WithArgs("business").
//		WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(1))
//
//	// 模拟查询返回一条规则记录
//	mockDb.ExpectQuery("SELECT * FROM `field_tag_rules` WHERE `set_field` IN (?) ORDER BY created_at ASC").
//		WithArgs("business").
//		WillReturnRows(mockDb.NewRows([]string{"id", "source_id", "set_field", "field", "value"}).
//			AddRow(1, "source_id", "business", "field_name", "field_value"))
//
//	// 模拟ES客户端
//	mockServer := testcommon.NewMockServer()
//	defer mockServer.Close()
//
//	// 模拟ES查询结果
//	mockServer.Register("*", []*elastic.SearchHit{})
//
//	// 使用gomonkey模拟trigger_merge.TriggerMergeForAsset函数
//	patches := gomonkey.ApplyFunc(trigger_merge.TriggerMergeForAsset,
//		func(params *trigger_merge.TriggerParamsForAsset) error {
//			return nil
//		})
//	defer patches.Reset()
//
//	err := AssetMergeTaskEndHandler("", "")
//	assert.NoError(t, err)
//}

func TestVulnMergeBusinessHandler(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	mockServer.Register("asset/_search", []*elastic.SearchHit{
		{
			Id:     "1_************",
			Source: []byte(`{"ip":"************", "area":1, "business":[], "BusinessDepartment":[]}`),
		},
	})

	poc := &poc2.Poc{
		Id:      "1_************",
		Fid:     "1_************",
		FidHash: "1_************",
		Area:    0,
		Ip:      "************",
	}
	sourceData := []*poc2.ProcessPoc{
		{
			Id:   "1_************",
			Area: 1,
			Ip:   "************",
		},
	}
	err := VulnMergeBusinessHandler(poc, nil, sourceData, nil)
	if err != nil {
		t.Error(err)
	}
	assert.Equal(t, "************", poc.Ip)
	assert.Equal(t, 1, int(poc.Area))
}

func TestBatchUpdateBusiness(t *testing.T) {
	t.Run("update business ips sucess", func(t *testing.T) {
		mockServer := testcommon.NewMockServer()
		defer mockServer.Close()

		mockServer.Register("/asset/_search", elastic.SearchResult{
			ScrollId: "scroll-id-2",
			Hits: &elastic.SearchHits{
				TotalHits: &elastic.TotalHits{
					Value:    2,
					Relation: "eq",
				},
				Hits: []*elastic.SearchHit{
					{
						Id:     "1",
						Source: json.RawMessage(`{"business": [{"system": "业务系统", "system_id": "1", "owner": "张三", "owner_id": "1", "department": ["部门负责人"], "source": "rule"}]}`),
					},
					{
						Id:     "2",
						Source: json.RawMessage(`{"business": [{"system": "业务系统", "system_id": "1", "owner": "张三", "owner_id": "1", "department": ["部门负责人"], "source": "rule"}], "ip_type": 1}`),
					},
				},
			},
		})
		mockServer.Register("/business_systems/_update_by_query", &elastic.BulkIndexByScrollResponse{
			Updated: 1,
		})
		mockServer.RegisterEmptyScrollHandler()
		ids := []string{"1", "2", "3"}
		values := map[string]interface{}{"business": []*assets.Business{{SystemId: "0263354408ee408587ef4a9b3a3b9a58", Owner: "小左", OwnerId: "", System: ""}}}
		err := Business_AssetManualCalibrationHandler_UpdateBusiness("asset", ids, values, nil, nil)
		assert.Nil(t, err)
	})
}

func TestGenerateUpdateScript(t *testing.T) {
	// 测试数据
	addIp := map[string]map[string][]string{
		"biz1": {
			"internal": {"***********", "***********"},
			"external": {"*******"},
		},
		"biz2": {
			"internal": {"********"},
			"external": {"*******", "*******"},
		},
	}

	removeIp := map[string]map[string][]string{
		"biz1": {
			"internal": {"***********"},
			"external": {"*******"},
		},
		"biz2": {
			"internal": {"********"},
			"external": {"*******"},
		},
	}
	expectedParams := map[string]interface{}{
		"bizId_biz1":          "biz1",
		"addIntranet_biz1":    []string{"***********", "***********"},
		"addInternet_biz1":    []string{"*******"},
		"bizId_biz2":          "biz2",
		"addIntranet_biz2":    []string{"********"},
		"addInternet_biz2":    []string{"*******", "*******"},
		"removeIntranet_biz1": []string{"***********"},
		"removeInternet_biz1": []string{"*******"},
		"removeIntranet_biz2": []string{"********"},
		"removeInternet_biz2": []string{"*******"},
	}

	// 生成脚本
	_, params := generateUpdateScript(addIp, removeIp)

	// 比较参数
	if !reflect.DeepEqual(params, expectedParams) {
		t.Errorf("Expected params:\n%v\nbut got:\n%v", expectedParams, params)
	}
}
