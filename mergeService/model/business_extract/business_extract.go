package business_extract

import (
	"context"
	"errors"
	"fmt"
	"fobrain/fobrain/app/services/business_strategy"
	"fobrain/initialize/es"
	"fobrain/initialize/redis"
	logs "fobrain/mergeService/utils/log"
	"fobrain/models/elastic/assets"
	"fobrain/models/elastic/business_system"
	"fobrain/models/elastic/poc"
	dbmodel "fobrain/models/mysql/strategy"
	distributedlock "fobrain/pkg/distributedLock"
	"fobrain/pkg/utils"
	strategy "fobrain/services/strategy_business"
	"slices"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/olivere/elastic/v7"
)

func AssetMergeDataEndHandler(data *assets.Assets, existData *assets.Assets) error {
	if data == nil {
		return errors.New("资产数据为空")
	}
	// 更新 business 处理校准数据
	if existData != nil && len(existData.BusinessSource) > 0 {
		businesses, ok := existData.BusinessSource["0"]
		if ok {
			data.Business = []*assets.Business{}
			for _, business := range businesses {
				if business.System != "" {
					newBusiness, err := business_system.NewBusinessSystems().GetBusinessByName(context.Background(), business.System)
					if err != nil {
						return err
					}
					data.Business = append(data.Business, newBusiness)
				}
			}
		}
	}
	data.BusinessDepartment = make([]*assets.DepartmentBase, 0)
	data.BusinessDepartmentIds = make([]uint64, 0)
	data.BusinessStaffIds = make([]string, 0)
	departmentIds := make([]uint64, 0)

	for _, business := range data.Business {
		for _, dep := range business.DepartmentBase {
			if !slices.Contains(departmentIds, dep.Id) && !isExistBusinessDepartment(data.BusinessDepartment, dep.BusinessSystemId, dep.Id) {
				data.BusinessDepartment = append(data.BusinessDepartment, dep)
				departmentIds = append(departmentIds, dep.Id)
			}
		}
		for _, person := range business.PersonBase {
			if person.Id != "" {
				data.BusinessStaffIds = append(data.BusinessStaffIds, person.Id)
			}
		}
	}
	// 获取新的业务系统ID
	businessIds := make([]string, 0)
	for _, business := range data.Business {
		businessIds = append(businessIds, business.SystemId)
	}

	if existData != nil {
		// 对比业务系统是否变更
		for _, business := range existData.Business {
			businessIds = append(businessIds, business.SystemId)
		}
	}
	businessIds = utils.ListDistinct(businessIds)
	values := make([]interface{}, len(businessIds))
	for i, v := range businessIds {
		values[i] = v
	}
	// 如果有需要更新的业务系统ID，写入redis
	if len(businessIds) > 0 {
		redis.GetRedisClient().RPush(context.Background(), business_strategy.RedisKeyBusinessRefresh, values...)
	}
	// 更新 business_department
	data.BusinessDepartmentIds = utils.ListDistinctNonZero(departmentIds)
	return nil
}

func isExistBusinessDepartment(businessDepartment []*assets.DepartmentBase, businessSystemId string, departmentId uint64) bool {
	for _, dep := range businessDepartment {
		if dep.BusinessSystemId == businessSystemId && dep.Id == departmentId {
			return true
		}
	}
	return false
}

// VulnMergeBusinessHandler 漏洞融合处理
func VulnMergeBusinessHandler(data *poc.Poc, strategies []*dbmodel.Strategy, sourceData []*poc.ProcessPoc, latestData map[string]*poc.ProcessPoc) error {
	//查询资产IP业务系统信息
	if len(sourceData) == 0 {
		return nil
	}
	err := VulnConvertBusinessDataHandler(data, sourceData[0])
	if err != nil {
		return err
	}
	return nil
}

func VulnConvertBusinessDataHandler(data *poc.Poc, sourceData *poc.ProcessPoc) error {
	//查询资产IP业务系统信息
	asset, err := assets.NewAssets().First(context.Background(), data.Ip, strconv.FormatUint(sourceData.Area, 10))
	if err != nil {
		return err
	}
	if asset == nil {
		return nil
	}
	data.Area = sourceData.Area
	data.Business = asset.Business
	data.BusinessDepartment = asset.BusinessDepartment
	var names = make([]string, 0, len(data.Business))
	for _, business := range data.Business {
		names = append(names, business.System)
	}
	// 不要去掉，漏洞风险等级计算依赖这个字段
	data.BusinessNamesTmp = names
	data.NetworkType = asset.NetworkType
	data.HostName = asset.HostName
	data.BusinessStaffIds = asset.BusinessStaffIds
	data.BusinessDepartmentIds = asset.BusinessDepartmentIds
	data.OperStaffIds = asset.OperStaffIds
	data.OperDepartmentIds = asset.OperDepartmentIds
	data.OperInfo = asset.OperInfo
	data.OperDepartment = asset.OperDepartment
	return nil
}

// AssetMergeTaskStartBusinessHandler 事件处理函数
func AssetMergeTaskStartBusinessHandler(nodeTaskInfo map[uint64][]uint64, sourceId, nodeId uint64, taskType, taskId, assetTaskId string) error {
	logger := logs.GetLogger("[business_extract]asset-business-extract")
	if taskType != "sync" && taskType != "同步数据" && taskType != "数据同步" {
		return nil
	}
	lockTimes := 0
	lockKey := fmt.Sprintf("lock:business:merge")
	for {
		if !distributedlock.Lock(lockKey, fmt.Sprintf("%d-%d", sourceId, nodeId), 7200) {
			lockTimes++
			// 15分钟都没取到锁，则放弃
			if lockTimes > 15 {
				return fmt.Errorf("[business_extract]AssetMergeTaskStartBusinessHandler lock failed")
			}
			time.Sleep(1 * time.Minute)
			continue
		}
		break
	}
	defer distributedlock.Unlock(lockKey, fmt.Sprintf("%d-%d", sourceId, nodeId))

	var childTaskIds []uint64
	for _, cid := range nodeTaskInfo {
		childTaskIds = append(childTaskIds, cid...)
	}
	// 获取资产业务系统数据
	logger.Infof("[business_extract] start")
	query := buildQuery(childTaskIds)

	businessInfo := make(map[string]map[string]string)

	result, _, err := assets.NewProcessAssetsModel().GetDuplicateCombinations([]string{"business_system", "business_owner", "person_field.keyword"}, query)
	if err != nil {
		logger.Errorf("[business_extract]error executing query: %v", err)
		return fmt.Errorf("error executing query: %v", err)
	}

	// 处理当前页的数据
	for _, item := range result {
		item_key, _ := item["key"].(string)
		businessInfos := strings.Split(item_key, "|")
		business := businessInfos[0]
		owner := businessInfos[1]
		perField := businessInfos[2]
		if perField == "" {
			perField = "name"
		}
		if business != "" {
			businessInfo[business] = map[string]string{
				"owner":    owner,
				"perField": perField,
			}
		}
	}

	if len(businessInfo) == 0 {
		logger.Warn("[business_extract]No valid business information found.")
		return nil
	}
	logger.Infof("[business_extract]businessInfo total:%d", len(businessInfo))

	// 数据入库并发执行
	return processBusinessInfoConcurrently(businessInfo, sourceId, nodeId, query)
}

// 构建查询
func buildQuery(childIds []uint64) *elastic.BoolQuery {
	terms := make([]interface{}, len(childIds))
	for i, id := range childIds {
		terms[i] = id
	}

	return elastic.NewBoolQuery().
		Must(elastic.NewTermsQuery("child_task_id", terms...)).
		Must(elastic.NewExistsQuery("business_system"))
}

// 处理业务信息并发执行
func processBusinessInfoConcurrently(businessInfo map[string]map[string]string, sourceId, nodeId uint64, query *elastic.BoolQuery) error {
	logger := logs.GetLogger("asset-business-extract")
	sem := make(chan struct{}, 20) // 限制并发数为 20
	var wg sync.WaitGroup
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	for businessName, info := range businessInfo {
		wg.Add(1)
		go func(businessName string, info map[string]string, sourceId, nodeId uint64) {
			defer wg.Done()
			// 控制并发
			sem <- struct{}{}
			defer func() { <-sem }()
			if err := processBusiness(ctx, businessName, info["owner"], info["perField"], sourceId, nodeId); err != nil {
				logger.Errorf("[business_extract] error processing business %s: %v", businessName, err)
			}
			logger.Infof("[business_extract] success business %s owner %s", businessName, info["owner"])
		}(businessName, info, sourceId, nodeId)
	}

	wg.Wait() // 等待所有任务完成
	strategy.NewStrategy().FlushResult()
	logger.Infof("[business_extract] all tasks completed successfully")
	return nil
}

// 处理每个业务信息
func processBusiness(ctx context.Context, businessName, owner, perField string, sourceId, nodeId uint64) error {
	dataSource := &strategy.SourceBusiness{
		Source:      sourceId,
		Node:        nodeId,
		PersonNames: strings.Split(owner, ","),
		StaffField:  perField,
	}
	_, err := strategy.NewStrategy().UpSet(businessName, 3, dataSource)
	return err
}

// 资产人工校准事件, 更新业务系统数据
func Business_AssetManualCalibrationHandler_UpdateBusiness(businessType string, ids []string, values map[string]interface{}, eventData map[string]interface{}, eventSource map[string]interface{}) error {
	if businessType != "asset" {
		return nil
	}
	return batchUpdateBusiness(ids, values)
}

func batchUpdateBusiness(assetIds []string, input map[string]interface{}) error {
	mlog := logs.GetLogger("asset")
	mlog.Debug("手动校准业务系统更新业务系统关联IP")
	// 获取传入的input中的 key
	nBusinessIds := make([]string, 0)
	for key, value := range input {
		if key == "business" {
			for _, business := range value.([]*assets.Business) {
				if business.SystemId != "" {
					nBusinessIds = append(nBusinessIds, business.SystemId)
				}
			}
			break
		}
	}
	if len(nBusinessIds) == 0 {
		return nil
	}
	// 获取资产业务系统
	query := elastic.NewBoolQuery().Must(elastic.NewTermsQueryFromStrings("_id", assetIds...))
	all, err := es.All[assets.Assets](1000, query, nil, "id", "business", "ip")
	if err != nil {
		return err
	}
	if all == nil {
		return nil
	}
	allBusinessIds := nBusinessIds
	// 使用 map 提高查找效率
	nBusinessIdSet := make(map[string]struct{}, len(nBusinessIds))
	for _, id := range nBusinessIds {
		nBusinessIdSet[id] = struct{}{}
	}

	addIp := make(map[string]map[string][]string)
	removeIp := make(map[string]map[string][]string)

	for _, basset := range all {
		// 提取资产已有的业务系统 ID
		assetBusinessIdSet := make(map[string]struct{})
		for _, business := range basset.Business {
			if business.SystemId != "" {
				assetBusinessIdSet[business.SystemId] = struct{}{}
				allBusinessIds = append(allBusinessIds, business.SystemId)
			}
		}

		// 计算新增和删除的业务系统 ID
		add := utils.Difference(nBusinessIdSet, assetBusinessIdSet)
		remove := utils.Difference(assetBusinessIdSet, nBusinessIdSet)

		// 按 IP 类型（内网/外网）存储
		for bId := range add {
			storeIPByType(addIp, bId, basset.Ip)
		}
		for bId := range remove {
			storeIPByType(removeIp, bId, basset.Ip)
		}
	}

	// 输出日志，方便调试
	mlog.Debug(fmt.Sprintf("Add IPs: %v", addIp))
	mlog.Debug(fmt.Sprintf("Remove IPs: %v", removeIp))
	if len(addIp) == 0 && len(removeIp) == 0 {
		return nil
	}
	// 生成脚本和参数
	script, params := generateUpdateScript(addIp, removeIp)
	_, err = es.GetEsClient().
		UpdateByQuery(business_system.NewBusinessSystems().IndexName()).   // 索引名称
		Query(elastic.NewTermsQueryFromStrings("fid", allBusinessIds...)). // 需要更新的业务系统 ID
		Script(elastic.NewScript(script).Params(params)).
		Do(context.Background())

	if err != nil {
		mlog.Fatalf("UpdateByQuery failed: %v", err)
		return err
	}
	return nil
}

// storeIPByType 根据 IP 类型（内网/外网）存储 IP
func storeIPByType(ipMap map[string]map[string][]string, businessId string, ip string) {
	ipType := "external" // 默认是外网 IP
	iType := utils.CheckPrivateIp(ip)
	if iType {
		ipType = "internal"
	}

	if _, exists := ipMap[businessId]; !exists {
		ipMap[businessId] = map[string][]string{
			"internal": {},
			"external": {},
		}
	}
	ipMap[businessId][ipType] = append(ipMap[businessId][ipType], ip)
}

// generateUpdateScript 生成 Elasticsearch 更新业务系统的 Painless 脚本和参数
func generateUpdateScript(addIp, removeIp map[string]map[string][]string) (string, map[string]interface{}) {
	var scriptBuilder strings.Builder
	params := make(map[string]interface{})

	// 处理新增 IP 的脚本
	for bizID, ipTypes := range addIp {
		// 内网 IP
		if len(ipTypes["internal"]) > 0 {
			scriptBuilder.WriteString(fmt.Sprintf(`
			if (ctx._source.fid == params.bizId_%s) {
				if (ctx._source.intranet_ips == null) { ctx._source.intranet_ips = []; }
				for (ip in params.addIntranet_%s) {
					if (!ctx._source.intranet_ips.contains(ip)) { ctx._source.intranet_ips.add(ip); }
				}
			}
			`, bizID, bizID))
			params[fmt.Sprintf("bizId_%s", bizID)] = bizID
			params[fmt.Sprintf("addIntranet_%s", bizID)] = ipTypes["internal"]
		}

		// 外网 IP
		if len(ipTypes["external"]) > 0 {
			scriptBuilder.WriteString(fmt.Sprintf(`
			if (ctx._source.fid == params.bizId_%s) {
				if (ctx._source.internet_ips == null) { ctx._source.internet_ips = []; }
				for (ip in params.addInternet_%s) {
					if (!ctx._source.internet_ips.contains(ip)) { ctx._source.internet_ips.add(ip); }
				}
			}
			`, bizID, bizID))
			params[fmt.Sprintf("bizId_%s", bizID)] = bizID
			params[fmt.Sprintf("addInternet_%s", bizID)] = ipTypes["external"]
		}
	}

	// 处理删除 IP 的脚本
	for bizID, ipTypes := range removeIp {
		// 内网 IP
		if len(ipTypes["internal"]) > 0 {
			scriptBuilder.WriteString(fmt.Sprintf(`
			if (ctx._source.fid == params.bizId_%s && ctx._source.intranet_ips != null) {
				ctx._source.intranet_ips.removeAll(params.removeIntranet_%s);
			}
			`, bizID, bizID))
			params[fmt.Sprintf("bizId_%s", bizID)] = bizID
			params[fmt.Sprintf("removeIntranet_%s", bizID)] = ipTypes["internal"]
		}

		// 外网 IP
		if len(ipTypes["external"]) > 0 {
			scriptBuilder.WriteString(fmt.Sprintf(`
			if (ctx._source.fid == params.bizId_%s && ctx._source.internet_ips != null) {
				ctx._source.internet_ips.removeAll(params.removeInternet_%s);
			}
			`, bizID, bizID))
			params[fmt.Sprintf("removeInternet_%s", bizID)] = ipTypes["external"]
		}
	}

	return scriptBuilder.String(), params
}

// 资产人工校准事件, 更新漏洞关联的业务系统
func Business_AssetManualCalibrationHandler_UpdatePoc(businessType string, ids []string, values map[string]interface{}, eventData map[string]interface{}, eventSource map[string]interface{}) error {
	if businessType != "asset" {
		return nil
	}
	// 获取资产业务系统
	query := elastic.NewBoolQuery().Must(elastic.NewTermsQueryFromStrings("_id", ids...))
	all, err := es.All[assets.Assets](1000, query, nil, "id", "ip")
	if err != nil {
		return err
	}
	if all == nil {
		return nil
	}
	ipList := make([]string, 0)
	for _, asset := range all {
		ipList = append(ipList, asset.Ip)
	}

	var scriptParts []string
	params := make(map[string]interface{})

	// 检查并添加业务相关字段
	if business, ok := values["business"]; ok {
		scriptParts = append(scriptParts, "ctx._source.business = params.business;")
		params["business"] = business
	}
	if businessDepartment, ok := values["business_department"]; ok {
		scriptParts = append(scriptParts, "ctx._source.business_department = params.business_department;")
		params["business_department"] = businessDepartment
	}
	if businessStaffIds, ok := values["business_staff_ids"]; ok {
		scriptParts = append(scriptParts, "ctx._source.business_staff_ids = params.business_staff_ids;")
		params["business_staff_ids"] = businessStaffIds
	}
	if businessDepartmentIds, ok := values["business_department_ids"]; ok {
		scriptParts = append(scriptParts, "ctx._source.business_department_ids = params.business_department_ids;")
		params["business_department_ids"] = businessDepartmentIds
	}

	// 检查并添加运维相关字段
	if operStaffIds, ok := values["oper_staff_ids"]; ok {
		scriptParts = append(scriptParts, "ctx._source.oper_staff_ids = params.oper_staff_ids;")
		params["oper_staff_ids"] = operStaffIds
	}
	if operDepartmentIds, ok := values["oper_department_ids"]; ok {
		scriptParts = append(scriptParts, "ctx._source.oper_department_ids = params.oper_department_ids;")
		params["oper_department_ids"] = operDepartmentIds
	}

	// 如果没有需要更新的字段，直接返回
	if len(scriptParts) == 0 {
		return nil
	}

	// 拼接脚本
	script := elastic.NewScriptInline(strings.Join(scriptParts, "")).Params(params)

	// 根据ip更新漏洞关联的业务系统
	pocQuery := elastic.NewBoolQuery().Must(elastic.NewTermsQueryFromStrings("ip", ipList...))
	_, err = es.GetEsClient().UpdateByQuery(poc.NewPoc().IndexName()).Query(pocQuery).Script(script).Do(context.Background())
	return err
}

// 更新业务系统关联的资产
// 资产单条融合结束任务中，会记录变更业务系统的ID到redis
// 任务结束时，会读取redis中的业务系统ID，刷新业务系统
func UpdateBusinessRelationAssets(taskType, taskId string) error {
	// 一次性读取所有数据
	allBusinessIds, err := redis.GetRedisClient().LRange(context.Background(), business_strategy.RedisKeyBusinessRefresh, 0, -1).Result()
	if err != nil {
		return err
	}

	// 如果没有需要处理的业务系统ID，直接返回
	if len(allBusinessIds) == 0 {
		return nil
	}

	// 刷新业务系统
	err = business_system.NewBusinessSystems().RefreshBusiness(context.Background(), allBusinessIds, []string{"ip"})
	if err != nil {
		return err
	}

	// 只清理已经处理过的数据，而不是清空整个列表
	// 这样可以避免在处理过程中新写入的数据被清空
	redisClient := redis.GetRedisClient()
	pipe := redisClient.Pipeline()

	// 对每个已处理的业务系统ID执行LREM操作，从列表中移除
	for _, businessId := range allBusinessIds {
		pipe.LRem(context.Background(), business_strategy.RedisKeyBusinessRefresh, 0, businessId)
	}

	// 执行管道命令
	_, err = pipe.Exec(context.Background())
	if err != nil {
		return err
	}

	return nil
}

// 更新业务系统关联的人员
// 人员信息变更，需要更新业务系统存储的人员信息，目前无法确定范围，进行全量更新
func UpdateBusinessStaff(taskType, taskId string) error {
	// 刷新业务系统
	err := business_system.NewBusinessSystems().RefreshBusiness(context.Background(), []string{}, []string{"staff"})
	if err != nil {
		return err
	}
	return nil
}
