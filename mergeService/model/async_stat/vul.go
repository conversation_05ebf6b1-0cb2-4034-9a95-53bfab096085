package async_stat

import (
	"context"
	"encoding/json"
	"fmt"
	"fobrain/fobrain/common/localtime"
	es2 "fobrain/initialize/es"
	"fobrain/initialize/redis"
	logs "fobrain/mergeService/utils/log"
	"fobrain/models/elastic/assets"
	"fobrain/models/elastic/device"
	"io"
	"slices"
	"strconv"
	"strings"
	"time"

	"github.com/olivere/elastic/v7"
)

func init() {
	logger = logs.GetLogger("service")
	client = es2.GetEsClient()
}

// StartMonitorVul 启动监控协程，持续监听 poc 索引更新，触发 asset 与 device 索引更新
func StartMonitorVul() {
	// 持续运行的监控协程
	go func() {
		var pendingMax int64 = 0
		var delayCount = 0
		// debug
		//redis.GetRedisClient().Del(context.Background(), lastUpdatedAtKey)
		for {
			// 每分钟检测一次
			newMax, err := getMaxUpdatedAt()
			if err != nil {
				logger.Infof("[async_stat] 获取 maxUpdatedAt 失败: %v", err)
				time.Sleep(time.Duration(delaySeconds) * time.Second)
				continue
			}
			// 从redis获取 lastMaxUpdatedAt
			last, err := getLastMaxUpdatedAt()
			if err != nil {
				logger.Infof("[async_stat] 获取 lastMaxUpdatedAt 失败: %v", err)
				last = 0
			}

			// 如果没有新数据，则等待下一次检测
			if last > 0 && newMax <= last {
				logger.Debugf("[async_stat] 无新数据, newMax: %d, last: %d", newMax, last)
				// 重置 pending
				pendingMax = 0
				delayCount = 0
				time.Sleep(time.Duration(delaySeconds) * time.Second)
				continue
			}

			// 如果是首次检测到新数据，则记录 pendingMax，并延迟计数设为1
			if pendingMax == 0 {
				pendingMax = newMax
				delayCount = 1
				logger.Infof("[async_stat] 检测到更新, pendingMax 设置为: %d", pendingMax)
			} else {
				if newMax == pendingMax {
					// 数据稳定
					logger.Infof("[async_stat] 数据稳定, pendingMax=%d, delayCount=%d", pendingMax, delayCount)
					// 稳定阈值设为 1 分钟，满足则触发更新
					if delayCount >= 1 {
						logger.Infof("[async_stat] 数据稳定达到阈值，触发更新")
						DoUpdatePocNumWithMax(pendingMax, last)
						pendingMax = 0
						delayCount = 0
					}
				} else {
					// 数据持续变化：更新 pendingMax 并增加延迟计数
					pendingMax = newMax
					delayCount++
					logger.Infof("[async_stat] 数据变化, pendingMax 更新为 %d, delayCount=%d", pendingMax, delayCount)
					// 如果持续变化达到 5 分钟，强制触发更新
					if delayCount >= 5 {
						logger.Infof("[async_stat] 持续变化超过 5 分钟，强制更新")
						DoUpdatePocNumWithMax(pendingMax, last)
						pendingMax = 0
						delayCount = 0
					}
				}
			}
			time.Sleep(time.Duration(delaySeconds) * time.Second)
		}
	}()
}

// DoUpdatePocNumWithMax 监听 poc 索引更新，触发 asset 与 device 索引 poc_num 更新
func DoUpdatePocNumWithMax(maxUpdatedAt, lastMaxUpdatedAt int64) {
	ctx := context.Background()
	logger.Infof("[async_stat] 检测到 poc 数据有更新, lastMaxUpdatedAt: %d, maxUpdatedAt: %d", lastMaxUpdatedAt, maxUpdatedAt)

	// 3. 获取受影响的 (ip, area) 组合（仅根据 poc updated_at > lastMaxUpdatedAt 获取触发范围）
	changedSet, err := fetchAffectedIPs(ctx, lastMaxUpdatedAt)
	if err != nil {
		logger.Errorf("[async_stat] 获取受影响 IP 失败: %v", err)
		return
	}
	if len(changedSet) == 0 {
		logger.Infof("[async_stat] 无受影响的 IP，跳过更新")
		err = updateLastMaxUpdatedAt(maxUpdatedAt)
		if err != nil {
			logger.Errorf("[async_stat] 更新 lastMaxUpdatedAt 失败: %v", err)
		}
		return
	}
	logger.Infof("[async_stat] 受影响 (ip, area) 数量: %d", len(changedSet))

	// 4. 查询 device 索引，收集所有相关设备的 (ip, area) 组合
	deviceIPAreaSet, err := FetchDeviceIPAreaSet(ctx, changedSet)
	if err != nil {
		logger.Errorf("[async_stat] 获取设备相关 (ip, area) 失败: %v", err)
		return
	}
	var ipSet = deviceIPAreaSet
	if len(deviceIPAreaSet) == 0 {
		ipSet = deviceIPAreaSet
		logger.Infof("[async_stat] 无相关设备，跳过更新")
	}
	logger.Infof("[async_stat] 设备相关 (ip, area) 数量: %d", len(deviceIPAreaSet))

	// 5. 对 poc 索引进行聚合，仅针对设备相关的 (ip, area) 组合
	aggResults, err := FetchPocAggResultsForIPAreaSet(ctx, ipSet)
	if err != nil {
		logger.Errorf("[async_stat] 聚合 poc 数据失败: %v", err)
		return
	}
	logger.Infof("[async_stat] 聚合得到 %d 个 (ip, area) 的漏洞统计数据", len(aggResults))

	// 6. 更新 asset 索引：根据聚合结果，查找 asset 索引中匹配 (ip, area) 的文档并更新 poc_num 字段
	if err := updateAssetIndex(ctx, aggResults); err != nil {
		logger.Errorf("[async_stat] 更新 asset 索引失败: %v", err)
	}
	if len(deviceIPAreaSet) > 0 {
		// 7. 更新 device 索引：查找 device 索引中 ip 数组包含受影响 IP 且 device.area 与聚合结果中 area 相等的设备
		if err := updateDeviceIndex(ctx, aggResults); err != nil {
			logger.Errorf("[async_stat] 更新 device 索引失败: %v", err)
		}
	}

	// 8. 更新 Redis 中记录的 lastMaxUpdatedAt
	if err := updateLastMaxUpdatedAt(maxUpdatedAt); err != nil {
		logger.Errorf("[async_stat] 更新 lastMaxUpdatedAt 失败: %v", err)
	}
}

// updateAssetIndex 更新 asset 索引中匹配 ip 与 area 的文档，更新 poc_num 字段。
// 注意：asset 索引中存储独立的 ip 和 area 字段，_id 为 uuid，不是 ip+area 组合。
func updateAssetIndex(ctx context.Context, aggResults []AggResult) error {
	if len(aggResults) == 0 {
		return nil
	}
	chunks := slices.Chunk(aggResults, 5000)
	for chunk := range chunks {
		var affectedKeys []interface{}
		// 构造聚合结果 map: key = "ip_area" -> vulnCount
		aggMap := make(map[string]int64)
		for _, agg := range chunk {
			key := fmt.Sprintf("%s_%d", agg.IP, agg.Area)
			aggMap[key] = agg.VulnCount
			affectedKeys = append(affectedKeys, key)
		}

		// 使用脚本查询过滤 asset 索引中受影响的文档
		scriptQuery := elastic.NewScriptQuery(
			elastic.NewScriptInline("params.affected.contains(doc['ip'].value + '_' + doc['area'].value)").
				Lang("painless").
				Param("affected", affectedKeys),
		)
		// 指定返回字段：仅包含 "ip" 和 "area"
		fetchSource := elastic.NewFetchSourceContext(true).
			Include("ip", "area", "poc_num", "id") // 包含字段
		// 使用 Scroll API 查询所有匹配的 asset 文档
		scrollService := client.Scroll().
			Index(assets.NewAssets().IndexName()).
			Query(scriptQuery).
			Size(1000).
			FetchSourceContext(fetchSource)

		bulkRequest := client.Bulk().Index(assets.NewAssets().IndexName())
		for {
			searchResult, err := scrollService.Do(ctx)
			if err == io.EOF {
				break
			}
			if err != nil {
				err := scrollService.Clear(ctx)
				if err != nil {
					logger.Errorf("ClearScroll 错误: %v", err)
				}
				return fmt.Errorf("updateAssetIndex scroll error: %v", err)
			}
			if len(searchResult.Hits.Hits) == 0 {
				break
			}
			for _, hit := range searchResult.Hits.Hits {
				// 反序列化 asset 文档，获取 ip 和 area 字段
				var asset struct {
					IP     string `json:"ip"`
					Area   int    `json:"area"`
					PocNum int64  `json:"poc_num"`
				}
				if err := json.Unmarshal(hit.Source, &asset); err != nil {
					logger.Errorf("反序列化 asset 失败: %v", err)
					continue
				}
				// 构造 key
				key := fmt.Sprintf("%s_%d", asset.IP, asset.Area)
				newCount, exists := aggMap[key]
				if !exists {
					// 如果受影响集合中没有该 key，则不更新
					continue
				}
				// 仅当新统计值与当前值不同才更新
				if newCount != asset.PocNum {
					script := elastic.NewScriptInline("ctx._source.poc_num = params.count;ctx._source.poc_num_updated_at = params.updatedAt").
						Lang("painless").
						Param("count", newCount).
						Param("updatedAt", localtime.Now())
					req := elastic.NewBulkUpdateRequest().Id(hit.Id).Doc(script)
					bulkRequest = bulkRequest.Add(req)
					if bulkRequest.NumberOfActions() >= 100 {
						_, err := bulkRequest.Do(ctx)
						if err != nil {
							logger.Errorf("Bulk 更新 asset 失败: %v", err)
						} else {
							bulkRequest = client.Bulk().Index(assets.NewAssets().IndexName())
						}
					}
				}
			}
		}
		// 清除 scroll 游标
		err := scrollService.Clear(ctx)
		if err != nil {
			logger.Errorf("ClearScroll 错误: %v", err)
		}
		// 执行 Bulk 更新
		if bulkRequest.NumberOfActions() > 0 {
			bulkResp, err := bulkRequest.Do(ctx)
			if err != nil {
				return fmt.Errorf("bulk update asset error: %v", err)
			}
			logger.Infof("更新 asset 文档数量: %d", len(bulkResp.Items))
		} else {
			logger.Infof("asset 索引无更新数据")
		}
	}

	return nil
}

// updateDeviceIndex 更新 device 索引中受影响设备的 poc_num 字段。
// 设备文档中字段 ip 为 []string，且有 area 字段；对每个设备，遍历其所有 IP（和设备 area 组合），累计漏洞数量，再更新设备 poc_num 字段
// updateDeviceIndex 更新 device 索引中受影响设备的 poc_num 字段。
// 对于每个设备文档（设备的 ip 字段为 []string，且有 area 字段），
// 查询时使用 Scroll API 获取所有包含受影响 IP 的文档，然后针对每个设备，
// 遍历其 ip 数组（与设备 area 组合），累计漏洞数量（来自 aggMap），再通过 Bulk 更新。
func updateDeviceIndex(ctx context.Context, aggResults []AggResult) error {
	// 构造聚合结果 map：key 格式 "ip_area" -> vulnCount
	aggMap := make(map[string]int64)
	// 构造受影响 IP 集合（只取 ip 部分）
	affectedIPSet := make(map[string]struct{})
	areasMap := make(map[int]struct{})
	for _, agg := range aggResults {
		key := fmt.Sprintf("%s_%d", agg.IP, agg.Area)
		aggMap[key] = agg.VulnCount
		affectedIPSet[agg.IP] = struct{}{}
		areasMap[agg.Area] = struct{}{}
	}
	// 转换 affectedIPSet 为 []interface{}
	var affectedIPs []interface{}
	for ip := range affectedIPSet {
		affectedIPs = append(affectedIPs, ip)
	}
	chunks := slices.Chunk(affectedIPs, 200)
	for chunk := range chunks {
		sq := &elastic.BoolQuery{}
		sq.Should((&elastic.BoolQuery{}).Must(elastic.NewTermsQuery("ip", chunk...)), (&elastic.BoolQuery{}).Must(elastic.NewTermsQuery("private_ip", chunk...)), (&elastic.BoolQuery{}).Must(elastic.NewTermsQuery("public_ip", chunk...)))
		sq.MinimumNumberShouldMatch(1)
		// 如果有从回收站还原时，此处需要考虑有字段，但是没有值的情况，但是目前用的es客户端有bug，range用的from to方式已经不被支持
		q := (&elastic.BoolQuery{}).Must(sq).MustNot(elastic.NewExistsQuery("deleted_at"))

		rows, err := es2.All[device.Device](1000, q, nil, "id", "public_ip", "private_ip", "area", "poc_num")
		if err != nil {
			logger.Errorf("updateDeviceIndex query device: %v", err)
			continue
		}
		bulkReq := client.Bulk().Index(device.NewDeviceModel().IndexName())
		for _, d := range rows {
			// 设备文档可能包含多个 IP，此处统计所有与设备 area 组合的漏洞数量
			var total int64 = 0
			var hasArea = false
			for _, ip := range d.PublicIp {
				for _, area := range d.Area {
					// 组合 key 为 "ip_area"
					key := fmt.Sprintf("%s_%d", ip, area)
					if cnt, ok := aggMap[key]; ok {
						total += cnt
						hasArea = true
					}
				}
			}
			for _, ip := range d.PrivateIp {
				for _, area := range d.Area {
					// 组合 key 为 "ip_area"
					key := fmt.Sprintf("%s_%d", ip, area)
					if cnt, ok := aggMap[key]; ok {
						total += cnt
						hasArea = true
					}
				}
			}
			// 如果累计漏洞数量与当前设备 poc_num 不一致，则更新该设备
			if total != d.PocNum && hasArea {
				script := elastic.NewScriptInline("ctx._source.poc_num = params.count;ctx._source.poc_num_updated_at = params.updatedAt").
					Lang("painless").
					Param("count", total).
					Param("updatedAt", localtime.Now())
				req := elastic.NewBulkUpdateRequest().Id(d.Id).Doc(script)
				bulkReq = bulkReq.Add(req)
				if bulkReq.NumberOfActions() >= 100 {
					_, err := bulkReq.Do(ctx)
					if err != nil {
						logger.Errorf("updateDeviceIndex bulk update error: %v", err)
					} else {
						bulkReq = client.Bulk().Index(device.NewDeviceModel().IndexName())
					}
				}
			}
		}
		// 执行 Bulk 更新
		if bulkReq.NumberOfActions() > 0 {
			bulkResp, err := bulkReq.Do(ctx)
			if err != nil {
				logger.Errorf("updateDeviceIndex bulk update error: %v", err)
				continue
			}
			logger.Infof("updateDeviceIndex: updated %d device documents", len(bulkResp.Items))
		} else {
			logger.Infof("updateDeviceIndex: no device documents updated")
		}
	}

	return nil
}

// FetchDeviceIPAreaSet 查询 device 索引，收集所有相关设备的 (ip, area) 组合，使用 scroll 查询确保获取所有数据
func FetchDeviceIPAreaSet(ctx context.Context, changedSet map[string]struct{}) (map[string]struct{}, error) {
	deviceIPAreaSet := make(map[string]struct{})

	// 构建 ip -> areas 的映射，从 changedSet 中提取变更的 (ip, area) 组合
	ipAreaMap := make(map[string]map[int]struct{})
	for key := range changedSet {
		parts := strings.Split(key, "_")
		if len(parts) != 2 {
			continue
		}
		ip := parts[0]
		areaStr := parts[1]
		area, err := strconv.Atoi(areaStr)
		if err != nil {
			continue
		}
		if _, ok := ipAreaMap[ip]; !ok {
			ipAreaMap[ip] = make(map[int]struct{})
		}
		ipAreaMap[ip][area] = struct{}{}
	}

	// 收集所有受影响的 IP（不考虑 area），构造 Terms 查询条件
	var changedIPs []interface{}
	for ip := range ipAreaMap {
		changedIPs = append(changedIPs, ip)
	}
	if len(changedIPs) == 0 {
		return deviceIPAreaSet, nil
	}
	chunks := slices.Chunk(changedIPs, 200)
	for chunk := range chunks {

		sq := &elastic.BoolQuery{}
		sq.Should((&elastic.BoolQuery{}).Must(elastic.NewTermsQuery("ip", chunk...)), (&elastic.BoolQuery{}).Must(elastic.NewTermsQuery("private_ip", chunk...)), (&elastic.BoolQuery{}).Must(elastic.NewTermsQuery("public_ip", chunk...)))
		sq.MinimumNumberShouldMatch(1)
		// 如果有从回收站还原时，此处需要考虑有字段，但是没有值的情况，但是目前用的es客户端有bug，range用的from to方式已经不被支持
		q := (&elastic.BoolQuery{}).Must(sq).MustNot(elastic.NewExistsQuery("deleted_at"))

		rows, err := es2.All[device.Device](1000, q, nil, "id", "public_ip", "private_ip", "area")

		if err != nil {
			return nil, fmt.Errorf("FetchDeviceIPAreaSet 查询失败: %v", err)
		}
		if len(rows) == 0 {
			continue
		}
		for _, d := range rows {
			for _, area := range d.Area {
				// 检查每个 d 的 IP 数组是否有与 changedSet 中匹配的 (ip, area)
				for _, ip := range d.PublicIp {
					if areas, exists := ipAreaMap[ip]; exists {
						if _, areaExists := areas[area]; areaExists {
							// 如果匹配，记录该设备的所有 IP 与设备 area 的组合到结果集合
							for _, devIP := range d.PublicIp {
								key := fmt.Sprintf("%s_%d", devIP, area)
								deviceIPAreaSet[key] = struct{}{}
							}
							break // 一个设备处理完毕，不必继续检查其余 IP
						}
					}
				}
				// 检查每个 d 的 IP 数组是否有与 changedSet 中匹配的 (ip, area)
				for _, ip := range d.PrivateIp {
					if areas, exists := ipAreaMap[ip]; exists {
						if _, areaExists := areas[area]; areaExists {
							// 如果匹配，记录该设备的所有 IP 与设备 area 的组合到结果集合
							for _, devIP := range d.PrivateIp {
								key := fmt.Sprintf("%s_%d", devIP, area)
								deviceIPAreaSet[key] = struct{}{}
							}
							break // 一个设备处理完毕，不必继续检查其余 IP
						}
					}
				}
			}

		}
	}

	return deviceIPAreaSet, nil
}

// FetchPocAggResultsForIPAreaSet 对 poc 索引聚合，仅处理 deviceIPAreaSet 中的 (ip, area)
func FetchPocAggResultsForIPAreaSet(ctx context.Context, deviceIPAreaSet map[string]struct{}) ([]AggResult, error) {
	var results []AggResult

	// 构建查询条件：匹配 deviceIPAreaSet 中的任意 (ip, area)
	// map 转成数组
	var changedIPs []string
	for key := range deviceIPAreaSet {
		changedIPs = append(changedIPs, key)
	}
	chunks := slices.Chunk(changedIPs, 1000)
	for chunk := range chunks {
		query := elastic.NewBoolQuery()
		for _, key := range chunk {
			parts := strings.Split(key, "_")
			if len(parts) != 2 {
				continue
			}
			ip := parts[0]
			area, err := strconv.Atoi(parts[1])
			if err != nil {
				continue
			}
			query.Should(
				elastic.NewBoolQuery().
					Must(elastic.NewTermQuery("ip", ip)).
					Must(elastic.NewTermQuery("area", area)),
			)
		}
		query.MinimumNumberShouldMatch(1)

		compositeAgg := elastic.NewCompositeAggregation().
			Size(1000).
			Sources(
				elastic.NewCompositeAggregationTermsValuesSource("ip").Field("ip"),
				elastic.NewCompositeAggregationTermsValuesSource("area").Field("area"),
			).SubAggregation("vuln_count", elastic.NewValueCountAggregation().Field("id"))

		var afterKey map[string]interface{}
		for {
			searchResult, err := client.Search().
				Index("poc").
				Size(0).
				Query(query).
				Aggregation("comp", compositeAgg.AggregateAfter(afterKey)).
				Do(ctx)
			if err != nil {
				return nil, fmt.Errorf("FetchPocAggResultsForIPAreaSet 错误: %v", err)
			}
			compAggRes, found := searchResult.Aggregations.Composite("comp")
			if !found {
				break
			}
			for _, bucket := range compAggRes.Buckets {
				ip, ok1 := bucket.Key["ip"].(string)
				// 将 area 转换为 int，无论 ES 返回 float64 还是 int
				var area int
				var ok2 = true
				switch v := bucket.Key["area"].(type) {
				case float64:
					area = int(v)
				case int:
					area = v
				default:
					ok2 = false
					continue
				}
				if !ok1 || !ok2 {
					continue
				}
				results = append(results, AggResult{
					IP:        ip,
					Area:      area,
					VulnCount: bucket.DocCount,
				})
			}
			if compAggRes.AfterKey == nil {
				break
			}
			afterKey = compAggRes.AfterKey
		}
	}

	return results, nil
}

// fetchAffectedIPs 根据 poc 索引中 updated_at > lastMaxUpdatedAt 获取受影响的 (ip, area) 组合，key 格式："ip_area"
func fetchAffectedIPs(ctx context.Context, lastMax int64) (map[string]struct{}, error) {
	affected := make(map[string]struct{})

	var query elastic.Query
	if lastMax > 0 {
		t := time.Unix(0, lastMax*int64(time.Millisecond))
		dateStr := t.Format("2006-01-02 15:04:05")
		query = elastic.NewRawStringQuery(fmt.Sprintf(`{"bool":{"must":[{"range":{"updated_at":{"gt":"%s"}}}]}}`, dateStr))
	} else {
		query = elastic.NewMatchAllQuery()
	}

	// Composite Aggregation：按 ip 和 area 分组，获取 key（不统计数量）
	compositeAgg := elastic.NewCompositeAggregation().
		Size(1000).
		Sources(
			elastic.NewCompositeAggregationTermsValuesSource("ip").Field("ip"),
			elastic.NewCompositeAggregationTermsValuesSource("area").Field("area"),
		)

	var afterKey map[string]interface{}
	for {
		searchResult, err := client.Search().
			Index("poc").
			Size(0).
			Query(query).
			Aggregation("comp", compositeAgg.AggregateAfter(afterKey)).
			Do(ctx)
		if err != nil {
			return nil, fmt.Errorf("fetchAffectedIPs error: %v", err)
		}
		compAggRes, found := searchResult.Aggregations.Composite("comp")
		if !found {
			break
		}
		for _, bucket := range compAggRes.Buckets {
			ip, ok1 := bucket.Key["ip"].(string)
			var area int
			var ok2 = true
			switch v := bucket.Key["area"].(type) {
			case float64:
				area = int(v)
			case int:
				area = v
			default:
				ok2 = false
				continue
			}
			if !ok1 || !ok2 {
				continue
			}
			key := fmt.Sprintf("%s_%d", ip, area)
			affected[key] = struct{}{}
		}
		if compAggRes.AfterKey == nil {
			break
		}
		afterKey = compAggRes.AfterKey
	}
	return affected, nil
}

// getLastMaxUpdatedAt 从 Redis 获取上一次记录的最大 updated_at 值
func getLastMaxUpdatedAt() (int64, error) {
	return redis.GetRedisClient().Get(context.Background(), lastUpdatedAtKey).Int64()
}

// updateLastMaxUpdatedAt 将最新的 maxUpdatedAt 值写入 Redis
func updateLastMaxUpdatedAt(maxUpdatedAt int64) error {
	return redis.GetRedisClient().Set(context.Background(), lastUpdatedAtKey, maxUpdatedAt, 0).Err()
}

// getMaxUpdatedAt 从 poc 索引获取最大 updated_at 值（以 epoch 毫秒数返回）
func getMaxUpdatedAt() (int64, error) {
	ctx := context.Background()

	maxAgg := elastic.NewMaxAggregation().Field("updated_at")
	searchResult, err := client.Search().
		Index("poc").
		Size(0).
		Aggregation("max_updated_at", maxAgg).
		Do(ctx)
	if err != nil {
		logger.Errorf("Search failed: %v", err)
		return 0, err
	}
	aggRes, found := searchResult.Aggregations.Max("max_updated_at")
	if !found || aggRes.Value == nil {
		logger.Info("Aggregation 'max_updated_at' not found or nil")
		return 0, fmt.Errorf("aggregation 'max_updated_at' not found or nil")
	}
	// 返回 epoch 毫秒数
	return int64(*aggRes.Value), nil
}
