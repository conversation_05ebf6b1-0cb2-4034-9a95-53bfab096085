package async_stat

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	testcommon "fobrain/fobrain/tests/common_test"
	es2 "fobrain/initialize/es"
	"fobrain/initialize/redis"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/alicebob/miniredis/v2"
	redis2 "github.com/go-redis/redis/v8"
	"github.com/olivere/elastic/v7"
	. "github.com/smartystreets/goconvey/convey"
	"github.com/stretchr/testify/assert"
	"testing"
	"time"
)

func TestDoUpdatePocNumWithMax(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	client = es2.GetEsClient()
	defer mockServer.Close()
	Convey("DoUpdatePocNumWithMax 单元测试", t, func() {
		// 初始化 Mock 环境
		ctx := context.Background()
		maxUpdatedAt := time.Now().UnixNano() / int64(time.Millisecond)
		lastMaxUpdatedAt := maxUpdatedAt - 1000

		// 创建 gomonkey patches
		patches := gomonkey.NewPatches()
		defer patches.Reset()
		var logOutput string
		patches.ApplyMethodFunc(logger, "Errorf", func(format string, args ...interface{}) {
			logOutput += fmt.Sprintf(format, args...)
		})

		// Setup mock redis
		s, err := miniredis.Run()
		if err != nil {
			t.Fatal(err)
		}
		defer s.Close()
		c := redis2.NewClient(&redis2.Options{Addr: s.Addr()})
		testcommon.SetRedisClient(c)

		Convey("场景1: 正常流程", func() {
			// Mock Redis 操作
			patches.ApplyMethodFunc(redis.GetRedisClient(), "Set",
				func(ctx context.Context, key string, value interface{}, expiration time.Duration) error {
					return nil
				})

			// Mock fetchAffectedIPs
			patches.ApplyFunc(fetchAffectedIPs, func(_ context.Context, _ int64) (map[string]struct{}, error) {
				return map[string]struct{}{
					"***********_1": {},
					"********_2":    {},
				}, nil
			})

			// Mock FetchDeviceIPAreaSet
			patches.ApplyFunc(FetchDeviceIPAreaSet, func(_ context.Context, _ map[string]struct{}) (map[string]struct{}, error) {
				return map[string]struct{}{
					"***********_1": {},
					"********_2":    {},
				}, nil
			})

			// Mock FetchPocAggResultsForIPAreaSet
			patches.ApplyFunc(FetchPocAggResultsForIPAreaSet, func(_ context.Context, _ map[string]struct{}) ([]AggResult, error) {
				return []AggResult{
					{IP: "***********", Area: 1, VulnCount: 5},
					{IP: "********", Area: 2, VulnCount: 3},
				}, nil
			})

			// Mock updateAssetIndex
			patches.ApplyFunc(updateAssetIndex, func(_ context.Context, _ []AggResult) error {
				return nil
			})

			// Mock updateDeviceIndex
			patches.ApplyFunc(updateDeviceIndex, func(_ context.Context, _ []AggResult) error {
				return nil
			})

			// 执行测试
			DoUpdatePocNumWithMax(maxUpdatedAt, lastMaxUpdatedAt)

			// 验证 Redis 更新
			val, _ := redis.GetRedisClient().Get(ctx, lastUpdatedAtKey).Int64()
			So(val, ShouldEqual, maxUpdatedAt)
		})

		Convey("场景2: 获取受影响IP失败", func() {
			// Mock fetchAffectedIPs 返回错误
			patches.ApplyFunc(fetchAffectedIPs, func(_ context.Context, _ int64) (map[string]struct{}, error) {
				return nil, errors.New("mock fetch error")
			})

			DoUpdatePocNumWithMax(maxUpdatedAt, lastMaxUpdatedAt)

			So(logOutput, ShouldContainSubstring, "获取受影响 IP 失败")
		})

		Convey("场景3: 无受影响IP", func() {
			// Mock fetchAffectedIPs 返回空
			patches.ApplyFunc(fetchAffectedIPs, func(_ context.Context, _ int64) (map[string]struct{}, error) {
				return map[string]struct{}{}, nil
			})

			// Mock Redis Set
			setCalled := false

			patches.ApplyFunc(updateLastMaxUpdatedAt, func(_ int64) error {
				setCalled = true
				return nil
			})

			DoUpdatePocNumWithMax(maxUpdatedAt, lastMaxUpdatedAt)

			So(setCalled, ShouldBeTrue)
		})
	})
}

func TestFetchPocAggResultsForIPAreaSet(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	client = es2.GetEsClient()
	defer mockServer.Close()
	mockServer.Register("/poc/_search", elastic.SearchResult{
		TookInMillis: 10,
		TimedOut:     false,
		Shards: &elastic.ShardsInfo{
			Total:      5,
			Successful: 5,
			Skipped:    0,
			Failed:     0,
		},
		Aggregations: elastic.Aggregations{
			"comp": json.RawMessage(`
				{
					"buckets": [
						{"ip": "127.0.0.1", "area":"1", "doc_count": 100},
						{"ip": "*********", "area":"1","doc_count": 200}
					]
				}
			`),
		},
	})
	_, err := FetchPocAggResultsForIPAreaSet(context.Background(), map[string]struct{}{"127.0.0.1_1": {}, "*********_1": {}})
	assert.NoError(t, err)
}
func TestFetchAffectedIPs(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	client = es2.GetEsClient()
	defer mockServer.Close()
	mockServer.Register("/poc/_search", elastic.SearchResult{
		TookInMillis: 10,
		TimedOut:     false,
		Shards: &elastic.ShardsInfo{
			Total:      5,
			Successful: 5,
			Skipped:    0,
			Failed:     0,
		},
		Aggregations: elastic.Aggregations{
			"comp": json.RawMessage(`
				{
					"buckets": [
						{"ip": "127.0.0.1", "area":"1", "doc_count": 100},
						{"ip": "*********", "area":"1","doc_count": 200}
					]
				}
			`),
		},
	})
	_, err := fetchAffectedIPs(context.Background(), time.Now().AddDate(0, 0, -1).Unix())
	assert.NoError(t, err)
}
func TestGetMaxUpdatedAt(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	client = es2.GetEsClient()
	defer mockServer.Close()
	mockServer.Register("/poc/_search", elastic.SearchResult{
		TookInMillis: 10,
		TimedOut:     false,
		Shards: &elastic.ShardsInfo{
			Total:      5,
			Successful: 5,
			Skipped:    0,
			Failed:     0,
		},
		Aggregations: elastic.Aggregations{
			"max_updated_at": json.RawMessage(`
				{
					"value": 1
				}
			`),
		},
	})
	l, err := getMaxUpdatedAt()
	fmt.Println(l)
	assert.NoError(t, err)
}
func TestFetchDeviceIPAreaSet(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	client = es2.GetEsClient()
	defer mockServer.Close()
	mockServer.Register("/device/_search", []*elastic.SearchHit{
		{
			Id:     "1",
			Source: []byte(`{"public_ip":["***********"], "private_ip":["***********"], "area":[1]}`),
		},
	})
	mockServer.Register("/_search/scroll", []*elastic.SearchHit{})
	l, err := FetchDeviceIPAreaSet(context.Background(), map[string]struct{}{"***********_1": {}, "***********_1": {}})
	fmt.Println(l)
	assert.NoError(t, err)
}
func TestUpdateDeviceIndex(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	client = es2.GetEsClient()
	defer mockServer.Close()
	mockServer.Register("/device/_search", []*elastic.SearchHit{
		{
			Id:     "1",
			Source: []byte(`{"public_ip":["***********"], "private_ip":["***********"], "area":[1]}`),
		},
	})
	mockServer.Register("/_search/scroll", []*elastic.SearchHit{})
	mockServer.Register("/device/_bulk", &elastic.BulkIndexByScrollResponse{
		Updated: 1,
	})
	err := updateDeviceIndex(context.Background(), []AggResult{{IP: "***********", Area: 1, VulnCount: 5}})
	assert.NoError(t, err)
}

func TestUpdateAssetIndex(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	client = es2.GetEsClient()
	defer mockServer.Close()
	mockServer.Register("/asset/_search", []*elastic.SearchHit{
		{
			Id:     "1",
			Source: []byte(`{"ip":"***********", "area":1}`),
		},
	})
	mockServer.Register("/_search/scroll", []*elastic.SearchHit{})
	mockServer.Register("/asset/_bulk", &elastic.BulkIndexByScrollResponse{
		Updated: 1,
	})
	err := updateAssetIndex(context.Background(), []AggResult{{IP: "***********", Area: 1, VulnCount: 5}})
	assert.NoError(t, err)
}
func TestStartMonitorVul(t *testing.T) {
	mockServer := testcommon.NewMockServer()
	client = es2.GetEsClient()
	defer mockServer.Close()
	mockServer.Register("/poc/_search", elastic.SearchResult{
		TookInMillis: 10,
		TimedOut:     false,
		Shards: &elastic.ShardsInfo{
			Total:      5,
			Successful: 5,
			Skipped:    0,
			Failed:     0,
		},
		Aggregations: elastic.Aggregations{
			"max_updated_at": json.RawMessage(`
				{
					"value": 1
				}
			`),
		},
	})
	// Setup mock redis
	s, err := miniredis.Run()
	if err != nil {
		t.Fatal(err)
	}
	defer s.Close()
	c := redis2.NewClient(&redis2.Options{Addr: s.Addr()})
	testcommon.SetRedisClient(c)
	delaySeconds = 1
	StartMonitorVul()
	select {
	case <-time.After(5 * time.Second):

	}
}
