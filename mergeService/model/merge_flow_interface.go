// 融合流程接口，存在资产、漏洞、人员、设备四种类型实现，通过工厂模式创建对应实例

package model

import (
	"context"
	"errors"
	"fmt"
	"fobrain/fobrain/common/localtime"
	"fobrain/initialize/es"
	"fobrain/initialize/redis"
	"fobrain/mergeService/event"
	merge_helper "fobrain/mergeService/model/helper"
	"fobrain/mergeService/model/trigger_merge"
	logs "fobrain/mergeService/utils/log"
	esmodel_asset "fobrain/models/elastic/assets"
	esmodel_device "fobrain/models/elastic/device"
	esmodel_vuln "fobrain/models/elastic/poc"
	esmodel_person "fobrain/models/elastic/staff"
	"fobrain/models/mysql/data_sync_child_task"
	"fobrain/models/mysql/merge"
	"fobrain/models/mysql/workbench"
	redis_helper "fobrain/models/redis"
	"fobrain/pkg/cfg"
	distributedlock "fobrain/pkg/distributedLock"
	"fobrain/pkg/queue"
	"fobrain/pkg/scheduler"
	"fobrain/pkg/tracker"
	"fobrain/pkg/utils"
	"fobrain/pkg/utils/common_logs"
	"math/rand"
	"slices"
	"strconv"
	"strings"
	"time"

	"gorm.io/gorm"

	goRedis "github.com/go-redis/redis/v8"

	"github.com/google/uuid"
	"github.com/olivere/elastic/v7"
)

// MergeFlow is the interface that wraps the basic MergeFlow method.
// 对于调用方来说，只需要关心 Merge 方法，不需要关心具体的实现。
// 但是为了预留扩展，接口中开放了关键节点的方法。
type MergeFlow[I any, R any, M any] interface {
	// Merge 合并主方法
	Merge()
	// 执行合并
	ExecuteMerge(ctx context.Context, mergeData *M)
	// 写入结果
	WriteResult()
	// 写入融合记录
	WriteRecord()
}

var (
	// 人员融合规则缓存配置
	mergeRuleCacheTime int
	// 许可证资产限制检查结果标识
	LicenseLimitCheckResultAsset = false
)

const (
	MergeFlowType_Asset         = "asset"
	MergeFlowType_Vulnerability = "vulnerability"
	MergeFlowType_Person        = "person"
	MergeFlowType_Device        = "device"
)

// 使用工厂模式，返回 MergeFlow 的实例
func NewMergeFlow[I any, R any, M any](mfType string) MergeFlow[I, R, M] {
	mergeRuleCacheTime = cfg.LoadRedis().MergeRuleCacheTime
	switch mfType {
	case MergeFlowType_Asset:
		return any(&AssetMergeFlow{
			mlog: logs.GetLogger("asset"),
		}).(MergeFlow[I, R, M])
	case MergeFlowType_Vulnerability:
		return any(&VulnMergeFlow{
			mlog: logs.GetLogger("vuln"),
		}).(MergeFlow[I, R, M])
	case MergeFlowType_Person:
		return any(&PersonMergeFlow{
			mlog: logs.GetLogger("person"),
		}).(MergeFlow[I, R, M])
	case MergeFlowType_Device:
		return any(&DeviceMergeFlow{
			mlog: logs.GetLogger("device"),
		}).(MergeFlow[I, R, M])
	}
	return nil
}

// NewAssetMergeFlow 返回资产融合流
// 调用方也可以直接调用 NewMergeFlow 获取 MergeFlow 实例，但是这样需要调用方了解具体数据类型
// 保留 NewMergeFlow 方法，是为了给调用方提供更底层的方法
func NewAssetMergeFlow() MergeFlow[esmodel_asset.ProcessAssets, esmodel_asset.Assets, MergeAsset] {
	return NewMergeFlow[esmodel_asset.ProcessAssets, esmodel_asset.Assets, MergeAsset](MergeFlowType_Asset)
}

// NewVulnMergeFlow 返回漏洞融合流
func NewVulnMergeFlow() MergeFlow[esmodel_vuln.ProcessPoc, esmodel_vuln.Poc, MergeVuln] {
	return NewMergeFlow[esmodel_vuln.ProcessPoc, esmodel_vuln.Poc, MergeVuln](MergeFlowType_Vulnerability)
}

// NewPersonMergeFlow 返回人员融合流
func NewPersonMergeFlow() MergeFlow[esmodel_person.ProcessStaff, esmodel_person.Staff, MergePerson] {
	return NewMergeFlow[esmodel_person.ProcessStaff, esmodel_person.Staff, MergePerson](MergeFlowType_Person)
}

// NewDeviceMergeFlow 返回设备融合流
func NewDeviceMergeFlow() MergeFlow[esmodel_asset.ProcessAssets, esmodel_device.Device, MergeDevice] {
	return NewMergeFlow[esmodel_asset.ProcessAssets, esmodel_device.Device, MergeDevice](MergeFlowType_Device)
}

// 生成不带连字符的 UUID
func UUIDStr() string {
	u := uuid.New()
	return strings.ReplaceAll(u.String(), "-", "")
}

// buildCondition 构建关联数据的查询条件
func buildCondition(query map[string]interface{}, mustOrShould string) *elastic.BoolQuery {
	// mlog.Debug("根据融合消息开始创建查询条件. msg: ", msg)
	condition := make([]elastic.Query, 0)
	for k, v := range query {
		// id 字段不参与查询条件
		if k == "task_type" || k == "task_id" || k == "is_start" || k == "is_end" || k == "id" || k == "total" {
			continue
		}
		condition = append(condition, elastic.NewTermQuery(k, v))
	}
	if mustOrShould == "must" {
		q := elastic.NewBoolQuery().Must(condition...)
		// 打印es查询语句
		// es.PrintEsQuery(q)
		return q
	} else {
		q := elastic.NewBoolQuery().Should(condition...)
		// 打印es查询语句
		// es.PrintEsQuery(q)
		return q
	}
}

// 更新任务表中的融合信息，并返回任务的融合信息
func UpdateMergeInfoToTask(mergeRecordId uint64, taskType string, taskId string, businessType string, sureEnd bool) (totalCount, successCount, failedCount, discardedCount int, start time.Time, end time.Time, workHours time.Duration, duration time.Duration, err error) {
	now := time.Now()
	end = now
	redisClient := redis.GetRedisClient()
	pipe := redisClient.Pipeline()
	workHoursCmd := pipe.Get(context.Background(), redis_helper.GenerateMergeTaskKey(taskId, businessType, "totaltime"))
	startCmd := pipe.Get(context.Background(), redis_helper.GenerateMergeTaskKey(taskId, businessType, "start"))
	countCmd := pipe.Get(context.Background(), redis_helper.GenerateMergeTaskKey(taskId, businessType, "count"))
	failedCountCmd := pipe.Get(context.Background(), redis_helper.GenerateMergeTaskKey(taskId, businessType, "failed_count"))
	discardedCountCmd := pipe.Get(context.Background(), redis_helper.GenerateMergeTaskKey(taskId, businessType, "discarded_count"))
	pipe.Exec(context.Background())
	// if err != nil {
	// 	return 0, start, end, 0, duration, err
	// }

	// 开始时间
	startStr, err := startCmd.Result()
	if err != nil && !errors.Is(err, goRedis.Nil) {
		return 0, 0, 0, 0, start, end, 0, duration, err
	}
	start, err = time.ParseInLocation("2006-01-02 15:04:05", startStr, time.Local)
	if err != nil && !errors.Is(err, goRedis.Nil) {
		return 0, 0, 0, 0, start, end, 0, duration, err
	}
	endStr := now.Format("2006-01-02 15:04:05")
	// 总工时
	workHoursVal, err := workHoursCmd.Int64()
	if err != nil && !errors.Is(err, goRedis.Nil) {
		return 0, 0, 0, 0, start, end, 0, duration, err
	}
	workHours, err = time.ParseDuration(fmt.Sprintf("%dms", workHoursVal))
	if err != nil && !errors.Is(err, goRedis.Nil) {
		return 0, 0, 0, 0, start, end, workHours, duration, err
	}
	// 总历时
	totalDuration := now.UnixMilli() - start.UnixMilli()
	duration, err = time.ParseDuration(fmt.Sprintf("%dms", totalDuration))
	if err != nil && !errors.Is(err, goRedis.Nil) {
		return 0, 0, 0, 0, start, end, workHours, duration, err
	}

	// 成功条数
	successCount, _ = countCmd.Int()
	// 失败条数
	failedCount, _ = failedCountCmd.Int()
	// 丢弃条数
	discardedCount, _ = discardedCountCmd.Int()
	// 总条数 = 成功条数 + 失败条数 + 丢弃条数
	totalCount = successCount + failedCount + discardedCount

	data := map[string]interface{}{
		"merge_count":           totalCount,
		"merge_success_count":   successCount,
		"merge_failed_count":    failedCount,
		"merge_discarded_count": discardedCount,
		"merge_start":           startStr,
		"merge_duration":        totalDuration,
		"merge_work_hours":      workHoursVal,
	}
	if sureEnd {
		data["merge_end"] = endStr
	}
	logs.GetLogger("service").Infof("更新任务表中的融合信息, mergeRecordId: %d, taskType: %s, taskId: %s, businessType: %s, sureEnd: %v, data: %v", mergeRecordId, taskType, taskId, businessType, sureEnd, data)

	endTime := localtime.NewLocalTime(end)
	// 更新融合进度
	err = merge.NewMergeRecordsModel().UpdateByTaskIdAndMergeType(mergeRecordId, totalCount, successCount, failedCount, discardedCount, endTime, workHoursVal, totalDuration)
	if err != nil {
		return totalCount, successCount, failedCount, discardedCount, start, end, workHours, duration, err
	}
	return totalCount, successCount, failedCount, discardedCount, start, end, workHours, duration, nil
}

// 批次结束处理，更新任务表中的融合信息，并删除 redis 中该任务的相关数据
func updateMergeTaskAndDelRedis(mergeRecordId uint64, taskType string, taskId string, businessType string) (totalCount int, start time.Time, end time.Time, workHours time.Duration, duration time.Duration, err error) {
	// 更新任务表中的融合信息
	totalCount, _, _, _, start, end, workHours, duration, err = UpdateMergeInfoToTask(mergeRecordId, taskType, taskId, businessType, true)
	if err != nil {
		return 0, start, end, workHours, duration, err
	}
	// 删除 redis中该任务的相关数据
	pipe := redis.GetRedisClient().Pipeline()
	pipe.Del(context.Background(), redis_helper.GenerateMergeTaskKey(taskId, businessType, "start"))
	pipe.Del(context.Background(), redis_helper.GenerateMergeTaskKey(taskId, businessType, "count"))
	pipe.Del(context.Background(), redis_helper.GenerateMergeTaskKey(taskId, businessType, "failed_count"))
	pipe.Del(context.Background(), redis_helper.GenerateMergeTaskKey(taskId, businessType, "discarded_count"))
	pipe.Del(context.Background(), redis_helper.GenerateMergeTaskKey(taskId, businessType, "totaltime"))
	_, err = pipe.Exec(context.Background())
	return totalCount, start, end, workHours, duration, err
}

func startHandle(msg queue.QueueMsg, taskId string, businessType string, mlog *common_logs.Logger) (bool, error) {
	isStartVal, hasIsStart := msg.Values["is_start"]
	isStart := false
	if hasIsStart {
		switch v := isStartVal.(type) {
		case string:
			isStart = v == "1"
		case bool:
			isStart = v
		case int:
			isStart = v == 1
		}
		// 未指定是否开始，默认开始
		if isStart {
			mergeRecordId, err := getMergeRecordId(msg)
			if err != nil {
				mlog.Warnf("获取融合记录ID失败. msg: %v, err: %v", msg, err)
				return false, err
			}
			rc := redis.GetRedisClient()
			db := data_sync_child_task.NewDataSyncChildTaskModel()
			go func(reidsClient *goRedis.Client, db *data_sync_child_task.DataSyncChildTask) {
				// 记录批次任务开始时间
				mlog.Infof("批次开始. 融合任务ID: %v, taskId: %s, businessType: %s", mergeRecordId, taskId, businessType)
				ok, err := reidsClient.SetNX(context.Background(), redis_helper.GenerateMergeTaskKey(taskId, businessType, "start"), time.Now().Format("2006-01-02 15:04:05"), 0).Result()
				if err == nil && ok {
					// 更新到任务表中
					startTime := localtime.Now()

					// 写入融合进度
					err = merge.NewMergeRecordsModel().UpdateByMap(mergeRecordId, map[string]interface{}{
						"start_time": startTime,
					})
					if err != nil {
						mlog.Warnf("创建融合进度失败. businessType: %s, taskId: %d, err: %v", businessType, mergeRecordId, err)
					}
				}
			}(rc, db)
		}
	}
	return isStart, nil
}
func ForceRefreshIndex(businessType string) {
	switch businessType {
	case "asset":
		_, _ = es.GetEsClient().Refresh(esmodel_asset.NewAssets().IndexName()).Do(context.Background())
		_, _ = es.GetEsClient().Refresh(esmodel_asset.NewAssetRecord().IndexName()).Do(context.Background())
		_, _ = es.GetEsClient().Refresh(esmodel_asset.NewMergeRecordsModel().IndexName()).Do(context.Background())
	case "device":
		_, _ = es.GetEsClient().Refresh(esmodel_device.NewDeviceModel().IndexName()).Do(context.Background())
		_, _ = es.GetEsClient().Refresh(esmodel_device.NewDeviceRecordModel().IndexName()).Do(context.Background())
		_, _ = es.GetEsClient().Refresh(esmodel_device.NewMergeRecordsModel().IndexName()).Do(context.Background())
	case "vuln", "vulnerability":
		_, _ = es.GetEsClient().Refresh(esmodel_vuln.NewPoc().IndexName()).Do(context.Background())
		_, _ = es.GetEsClient().Refresh(esmodel_vuln.NewPocRecord().IndexName()).Do(context.Background())
		_, _ = es.GetEsClient().Refresh(esmodel_vuln.NewMergeRecordsModel().IndexName()).Do(context.Background())
	}
}

func endHandle(msg queue.QueueMsg, taskId string, businessType string, mlog *common_logs.Logger) bool {
	isEndVal, hasIsEnd := msg.Values["is_end"]
	isEnd := false
	if hasIsEnd {
		switch v := isEndVal.(type) {
		case string:
			isEnd = v == "1"
		case bool:
			isEnd = v
		case int:
			isEnd = v == 1
		}
		// 未指定是否结束，默认未结束
		if isEnd {
			// 获取 taskType
			taskType, err := getTaskType(msg)
			if err != nil {
				mlog.Warnf("获取任务类型失败. msg: %v, err: %v", msg, err)
				return false
			}
			// 获取预期总数量
			expectedTotalCount, err := getExpectedTotalCount(msg)
			if err != nil {
				mlog.Warnf("获取预期总数量失败. msg: %v, err: %v", msg, err)
			}
			// 获取融合任务ID
			mergeRecordId, err := getMergeRecordId(msg)
			if err != nil {
				mlog.Warnf("获取融合任务ID失败. msg: %v, err: %v", msg, err)
				return false
			}

			// 更新任务的融合信息
			updateStatus := func(mrId uint64, taskType string, tid string, schedulerTaskName string, businessType string) bool {
				defer func() {
					mlog.Infof("更新任务表中的融合信息defer, mergeRecordId: %d, taskType: %s, taskId: %s, businessType: %s, sureEnd: %v, data: %v", mrId, taskType, tid, businessType, true, map[string]interface{}{})
					if err := recover(); err != nil {
						mlog.Warnf("businessType: %s, 批次ID: %v, 检查是否结束失败. err: %v", businessType, tid, err)
						// 解锁
						t := strings.Split(taskId, "-")
						err = trigger_merge.UnlockAndTrigger(mrId, businessType, t[0], t[1], "", false)
						if err != nil {
							mlog.Warnf("businessType: %s, 批次ID: %v, 解锁融合任务失败. err: %v", businessType, tid, err)
						}
					}
				}()
				mlog.Infof("批次结束,开始更新任务的融合信息.businessType: %s, taskId: %s", businessType, tid)
				totalCount, start, end, workHours, duration, err := updateMergeTaskAndDelRedis(mergeRecordId, taskType, tid, businessType)
				if err != nil {
					mlog.Warnf("businessType: %s, 批次ID: %v,更新任务的融合信息失败. err: %v", businessType, tid, err)
				}
				startStr := start.Format("2006-01-02 15:04:05")
				endStr := end.Format("2006-01-02 15:04:05")
				mlog.Infof("businessType: %s, 批次ID: %v,处理数据量: %d,开始处理时间: %s,结束时间: %s,工时 %s,历时: %s", businessType, tid, totalCount, startStr, endStr, workHours.Abs().String(), duration.Abs().String())
				// 停止定时任务
				scheduler.Stop(schedulerTaskName)
				// 更新索引刷新时间 恢复成默认的1s
				trigger_merge.UpdateRefreshInterval(businessType, "1s")
				// 强制刷新索引, 落盘
				ForceRefreshIndex(businessType)
				// 触发事件-批次结束
				if businessType == "asset" {
					err = event.NewEventBus().Emit(event.Event_Asset_MergeTask_End, taskType, tid)
				} else if businessType == "device" {
					err = event.NewEventBus().Emit(event.Event_Device_MergeTask_End, taskType, tid)
				} else if businessType == "person" {
					err = event.NewEventBus().Emit(event.Event_Person_MergeTask_End, taskType, tid)
				} else if businessType == "vuln" || businessType == "vulnerability" {
					err = event.NewEventBus().Emit(event.Event_Vuln_MergeTask_End, taskType, tid)
					// 批量刷新漏洞风险等级
					go BatchCalculateRiskLevel(false)
				}
				if err != nil {
					mlog.Warnf("businessType: %s, 批次ID: %v, 触发事件-批次结束失败. err: %v", businessType, tid, err)
				}

				// 解锁
				t := strings.Split(taskId, "-")
				err = trigger_merge.UnlockAndTrigger(mrId, businessType, t[0], t[1], "", false)
				if err != nil {
					mlog.Warnf("businessType: %s, 批次ID: %v, 解锁融合任务失败. err: %v", businessType, tid, err)
				}

				// 二次触发关联融合, 避免死循环，只能由人员->资产->设备、漏洞方向，不可由设备、漏洞反向触发资产和人员的融合
				mergeRecord, err := merge.NewMergeRecordsModel().GetById(mergeRecordId)
				if err != nil {
					mlog.Warnf("businessType: %s, 批次ID: %v, 获取融合任务失败. err: %v", businessType, tid, err)
				} else if mergeRecord != nil {
					// 二级触发
					go subTrigger(businessType, mergeRecord, mlog, tid)
					// 清理历史数据(过程表只保留最近三次节点同步的数据)
					go merge_helper.ClearProcessData(mergeRecord.TriggerNodeId, mergeRecord.TriggerSourceId)
				}

				return true
			}
			go func(mrId uint64, taskType string, tid string) {
				// 设置定时任务，持续 3 分钟，每 10 秒执行一次，检查是否结束
				taskEnd := false
				schedulerTaskName := fmt.Sprintf("batch_end_handle_%s_%v", businessType, tid)
				// 9秒一次
				scheduler.Start(schedulerTaskName, 9*time.Second, false, func() {
					redisClient := redis.GetRedisClient()
					redisCount, err := redisClient.Get(context.Background(), redis_helper.GenerateMergeTaskKey(fmt.Sprintf("%v", tid), businessType, "count")).Int()
					if err != nil && !errors.Is(err, goRedis.Nil) {
						mlog.Warnf("businessType: %s, 批次ID: %v, 获取redis中任务的条数失败. err: %v", businessType, tid, err)
						return
					}
					redisFailedCount, err := redisClient.Get(context.Background(), redis_helper.GenerateMergeTaskKey(fmt.Sprintf("%v", tid), businessType, "failed_count")).Int()
					if err != nil && !errors.Is(err, goRedis.Nil) {
						mlog.Warnf("businessType: %s, 批次ID: %v,获取redis中任务的失败条数失败. err: %v", businessType, tid, err)
						return
					}
					redisDiscardedCount, err := redisClient.Get(context.Background(), redis_helper.GenerateMergeTaskKey(fmt.Sprintf("%v", tid), businessType, "discarded_count")).Int()
					if err != nil && !errors.Is(err, goRedis.Nil) {
						mlog.Warnf("businessType: %s, 批次ID: %v,获取redis中任务的丢弃条数失败. err: %v", businessType, tid, err)
						return
					}

					if expectedTotalCount > 0 {
						// 如果任务表中总条数 = 成功条数 + 失败条数 + 丢弃条数，表示任务已结束
						if expectedTotalCount <= redisCount+redisFailedCount+redisDiscardedCount {
							if redisFailedCount > 0 || redisDiscardedCount > 0 {
								if businessType == "asset" {
									// 如果失败条数或丢弃条数大于 0，则发送告警信息
									workbench.NewNotifyAlarmCenter().Create(&workbench.NotifyAlarmCenter{
										MsgType:         workbench.MsgTypeAbnormal,
										MsgContent:      fmt.Sprintf("数据融合批次ID: %v, 失败条数: %d, 丢弃条数: %d", tid, redisFailedCount, redisDiscardedCount),
										MsgSource:       workbench.MsgSoucDataFusionFail,
										RelationType:    "",
										RelationContent: "",
										Remark:          "",
									})
								} else if businessType == "device" {
									// 如果失败条数或丢弃条数大于 0，则发送告警信息
									workbench.NewNotifyAlarmCenter().Create(&workbench.NotifyAlarmCenter{
										MsgType:         workbench.MsgTypeAbnormal,
										MsgContent:      fmt.Sprintf("设备提取批次ID: %v, 失败条数: %d, 丢弃条数: %d", tid, redisFailedCount, redisDiscardedCount),
										MsgSource:       workbench.MsgSouceDataExtractFail,
										RelationType:    "",
										RelationContent: "",
										Remark:          "",
									})
								}
							}
							// 更新任务的融合信息
							taskEnd = updateStatus(mrId, taskType, tid, schedulerTaskName, businessType)
							mlog.Infof("redis中任务的条数: %d, redis中失败条数: %d, redis中丢弃条数: %d, 任务预期总条数: %d. businessType: %s, 批次ID: %v, 任务已结束", redisCount, redisFailedCount, redisDiscardedCount, expectedTotalCount, businessType, tid)
						}
					} else {
						mlog.Infof("任务预期总条数为 0, 延迟结束任务. businessType: %s, 批次ID: %v", businessType, tid)
						// 停止每 10 秒执行一次的定时任务，只等待 3 分钟后更新一次
						scheduler.Stop(schedulerTaskName)
					}
				})
				// 延迟 3 分钟后，结束任务
				delay := time.Duration(cfg.LoadQueue().EndDelay) * time.Second
				mlog.Infof("延迟开始任务. businessType: %s, 批次ID: %v, 延迟时间: %s", businessType, taskId, delay.String())
				time.AfterFunc(delay, func() {
					mlog.Infof("延迟结束任务. businessType: %s, 批次ID: %v", businessType, taskId)
					// 停止定时任务
					scheduler.Stop(schedulerTaskName)
					if !taskEnd {
						// 更新任务的融合信息
						updateStatus(mrId, taskType, tid, schedulerTaskName, businessType)
					}
				})
			}(mergeRecordId, taskType, taskId)
		}
	}
	return isEnd
}
func subTrigger(businessType string, mergeRecord *merge.MergeRecords, mlog *common_logs.Logger, tid string) {
	// 因融合是协程并发的，收到end消息时，可能最后一批数据还没写完，此处加强制延时下
	time.Sleep(10 * time.Second)
	switch businessType {
	case "asset":
		// 局部融合或者明确指定了要二次触发的才触发
		if mergeRecord.DataRange == merge.MergeRecordsDataRangeSpec || mergeRecord.DataRange == merge.MergeRecordsDataRangeInc || mergeRecord.SubTrigger == 1 {
			affected := make(map[string]struct{})
			assetMergeIpList.Range(func(key, value any) bool {
				ip, _ := key.(string)
				area, _ := value.(int)
				ipArea := fmt.Sprintf("%s_%d", ip, area)
				affected[ipArea] = struct{}{}
				return true
			})
			assetMergeIpList.Clear()
			if len(affected) > 0 {
				triggerDeviceMergeByAsset(mergeRecord, businessType, tid, mlog, affected)
				triggerPocMergeByAsset(mergeRecord, businessType, tid, mlog, affected)
			}
		}
	case "person":
		// 人员变了，需要重新触发全量融合，因为无法确定数据范围，人可能增加了、删除了、变更部门了，原数据比如资产可能存在之前没匹配上人的、已经匹配上的，这些都要重新匹配
		// 所以无法确定数据范围，只能全量融合，所以这里触发全量融合
		// 根据scheduler的逻辑，存在极端情况stop的时候，当前正在执行中，因他是for select 事件，所以会执行完才能stop, 这个时候已经拿到的taskEnd值还是之前的上下文的，所以这需要判断下是不是已经触发过了，避免重复
		var exist = &merge.MergeRecords{}
		err := merge.NewMergeRecordsModel().Where("event_task_id=?", fmt.Sprintf("asset_merge_by_person_%d", mergeRecord.Id)).First(&exist).Error
		if errors.Is(err, gorm.ErrRecordNotFound) {
			err = trigger_merge.TriggerMergeForAsset(&trigger_merge.TriggerParamsForAsset{
				TriggerParams: trigger_merge.TriggerParams{
					TriggerSourceId: mergeRecord.TriggerSourceId,
					TriggerNodeId:   mergeRecord.TriggerNodeId,
					TriggerEvent:    "人员融合",
					TaskId:          fmt.Sprintf("asset_merge_by_person_%d", mergeRecord.Id),
					ChildTaskId:     fmt.Sprintf("%d", time.Now().Unix()),
					SubTrigger:      0, // 串行触发漏洞融合
				},
			})
			if err != nil {
				mlog.Warnf("businessType: %s, 批次ID: %v, 二次触发关联融合失败. err: %v", businessType, tid, err)
			}
		}
	}
}

func triggerPocMergeByAsset(m *merge.MergeRecords, businessType, tid string, mlog *common_logs.Logger, affected map[string]struct{}) {
	vulIds, err := FetchPocIds(context.Background(), affected)
	if err != nil {
		mlog.Warnf("businessType: %s, 批次ID: %v, 二次触发关联漏洞融合失败. err: %v", businessType, tid, err)
		return
	}
	if len(vulIds) == 0 {
		return
	}
	// 根据scheduler的逻辑，存在极端情况stop的时候，当前正在执行中，因他是for select 事件，所以会执行完才能stop, 这个时候已经拿到的taskEnd值还是之前的上下文的，所以这需要判断下是不是已经触发过了，避免重复
	var exist = &merge.MergeRecords{}
	_ = merge.NewMergeRecordsModel().Where("event_task_id=?", fmt.Sprintf("poc_merge_by_asset_%d", m.Id)).First(&exist).Error
	if exist.Id > 0 {
		return
	}

	err = trigger_merge.TriggerMergeForVuln(&trigger_merge.TriggerParamsForVuln{
		TriggerParams: trigger_merge.TriggerParams{
			TriggerSourceId: m.TriggerSourceId,
			TriggerNodeId:   m.TriggerNodeId,
			TriggerEvent:    "资产融合",
			TaskId:          fmt.Sprintf("poc_merge_by_asset_%d", m.Id),
			ChildTaskId:     fmt.Sprintf("%d", time.Now().Unix()),
		},
		VulnIds: vulIds,
	})
	if err != nil {
		mlog.Warnf("businessType: %s, 批次ID: %v, 二次触发关联融合失败. err: %v", businessType, tid, err)
	}
}

func triggerDeviceMergeByAsset(m *merge.MergeRecords, businessType, tid string, mlog *common_logs.Logger, affected map[string]struct{}) {
	ids, err := FetchDeviceIds(context.Background(), affected)
	if err != nil {
		mlog.Warnf("businessType: %s, 批次ID: %v, 二次触发关联漏洞融合失败. err: %v", businessType, tid, err)
		return
	}
	if len(ids) == 0 {
		return
	}
	// 根据scheduler的逻辑，存在极端情况stop的时候，当前正在执行中，因他是for select 事件，所以会执行完才能stop, 这个时候已经拿到的taskEnd值还是之前的上下文的，所以这需要判断下是不是已经触发过了，避免重复
	var exist = &merge.MergeRecords{}
	_ = merge.NewMergeRecordsModel().Where("event_task_id=?", fmt.Sprintf("device_merge_by_asset_%d", m.Id)).First(&exist).Error
	if exist.Id > 0 {
		return
	}
	err = trigger_merge.TriggerMergeForDevice(&trigger_merge.TriggerParamsForDevice{
		TriggerParams: trigger_merge.TriggerParams{
			TriggerSourceId: m.TriggerSourceId,
			TriggerNodeId:   m.TriggerNodeId,
			TriggerEvent:    "资产融合",
			TaskId:          fmt.Sprintf("device_merge_by_asset_%d", m.Id),
			ChildTaskId:     fmt.Sprintf("%d", time.Now().Unix()),
		},
		DeviceIds: ids,
	})
	if err != nil {
		mlog.Warnf("businessType: %s, 批次ID: %v, 二次触发关联融合失败. err: %v", businessType, tid, err)
	}
}

// FetchDeviceIds 查询 device 索引，收集所有相关设备的 (ip, area) 组合，使用 scroll 查询确保获取所有数据
func FetchDeviceIds(ctx context.Context, changedSet map[string]struct{}) ([]string, error) {
	// 构建 ip -> areas 的映射，从 changedSet 中提取变更的 (ip, area) 组合
	ipAreaMap := make(map[string]map[int]struct{})
	for key := range changedSet {
		parts := strings.Split(key, "_")
		if len(parts) != 2 {
			continue
		}
		ip := parts[0]
		areaStr := parts[1]
		area, err := strconv.Atoi(areaStr)
		if err != nil {
			continue
		}
		if _, ok := ipAreaMap[ip]; !ok {
			ipAreaMap[ip] = make(map[int]struct{})
		}
		ipAreaMap[ip][area] = struct{}{}
	}

	// 收集所有受影响的 IP（不考虑 area），构造 Terms 查询条件
	var changedIPs []interface{}
	for ip := range ipAreaMap {
		changedIPs = append(changedIPs, ip)
	}
	if len(changedIPs) == 0 {
		return []string{}, nil
	}
	chunks := slices.Chunk(changedIPs, 500)
	var ids []string
	//var log = logs.GetLogger("asset")
	for chunk := range chunks {
		sq := &elastic.BoolQuery{}
		sq.Should((&elastic.BoolQuery{}).Must(elastic.NewTermsQuery("ip", chunk...)), (&elastic.BoolQuery{}).Must(elastic.NewTermsQuery("private_ip", chunk...)), (&elastic.BoolQuery{}).Must(elastic.NewTermsQuery("public_ip", chunk...)))
		sq.MinimumNumberShouldMatch(1)
		// 如果有从回收站还原时，此处需要考虑有字段，但是没有值的情况，但是目前用的es客户端有bug，range用的from to方式已经不被支持
		q := (&elastic.BoolQuery{}).Must(sq).MustNot(elastic.NewExistsQuery("deleted_at"))
		// 指定返回字段：仅包含 "ip" 和 "area"
		//fetchSource := elastic.NewFetchSourceContext(true).
		//	Include("public_ip", "private_ip", "area", "id") // 包含字段
		//// 使用 Scroll API 查询 device 索引，确保获取所有匹配数据
		//scrollService := es.GetEsClient().Scroll().
		//	Index(esmodel_device.NewDeviceModel().IndexName()).
		//	Query(q).
		//	Size(1000).
		//	FetchSourceContext(fetchSource)
		rows, err := es.All[esmodel_device.Device](1000, q, nil, "public_ip", "private_ip", "area", "id")
		if err != nil {
			return nil, fmt.Errorf("FetchDeviceIds 查询失败: %v", err)
		}
		//for {
		//	searchResult, err := scrollService.Do(ctx)
		//	if err == io.EOF {
		//		break
		//	}
		//	if err != nil {
		//		err := scrollService.Clear(ctx)
		//		if err != nil {
		//			log.Errorf("ClearScroll 错误: %v", err)
		//		}
		//		return nil, fmt.Errorf("FetchDeviceIds 查询失败: %v", err)
		//	}
		//	if len(searchResult.Hits.Hits) == 0 {
		//		break
		//	}
		for _, d := range rows {
			//var d struct {
			//	Id        string   `json:"id"`
			//	PublicIP  []string `json:"public_ip"`
			//	PrivateIP []string `json:"private_ip"`
			//	Area      []int    `json:"area"`
			//}
			//if err := json.Unmarshal(hit.Source, &d); err != nil {
			//	log.Errorf("反序列化 device 失败: %v", err)
			//	continue
			//}
			for _, area := range d.Area {
				// 检查每个 d 的 IP 数组是否有与 changedSet 中匹配的 (ip, area)
				for _, ip := range d.PublicIp {
					if areas, exists := ipAreaMap[ip]; exists {
						if _, areaExists := areas[area]; areaExists {
							ids = append(ids, d.Id)
							break // 一个设备处理完毕，不必继续检查其余 IP
						}
					}
				}
				// 检查每个 d 的 IP 数组是否有与 changedSet 中匹配的 (ip, area)
				for _, ip := range d.PrivateIp {
					if areas, exists := ipAreaMap[ip]; exists {
						if _, areaExists := areas[area]; areaExists {
							// 如果匹配，记录该设备的所有 IP 与设备 area 的组合到结果集合
							ids = append(ids, d.Id)
							break // 一个设备处理完毕，不必继续检查其余 IP
						}
					}
				}
			}

		}
		//}
		//// 清除 scroll
		//err := scrollService.Clear(ctx)
		//if err != nil {
		//	log.Errorf("ClearScroll 错误: %v", err)
		//}
	}
	return utils.ListDistinct[string](ids), nil
}

// FetchPocIds 查询 poc 索引，收集所有相关设备的 (ip, area) 组合，使用 scroll 查询确保获取所有数据
func FetchPocIds(ctx context.Context, changedSet map[string]struct{}) ([]string, error) {
	// 构建 ip -> areas 的映射，从 changedSet 中提取变更的 (ip, area) 组合
	ipAreaMap := make(map[string]map[int]struct{})
	for key := range changedSet {
		parts := strings.Split(key, "_")
		if len(parts) != 2 {
			continue
		}
		ip := parts[0]
		areaStr := parts[1]
		area, err := strconv.Atoi(areaStr)
		if err != nil {
			continue
		}
		if _, ok := ipAreaMap[ip]; !ok {
			ipAreaMap[ip] = make(map[int]struct{})
		}
		ipAreaMap[ip][area] = struct{}{}
	}

	// 收集所有受影响的 IP（不考虑 area），构造 Terms 查询条件
	var changedIPs []interface{}
	for ip := range ipAreaMap {
		changedIPs = append(changedIPs, ip)
	}
	if len(changedIPs) == 0 {
		return []string{}, nil
	}
	chunks := slices.Chunk(changedIPs, 1000)
	var ids []string
	//var log = logs.GetLogger("asset")
	for chunk := range chunks {
		sq := &elastic.BoolQuery{}
		sq.Should(elastic.NewTermsQuery("ip", chunk...))
		sq.MinimumNumberShouldMatch(1)
		// 如果有从回收站还原时，此处需要考虑有字段，但是没有值的情况，但是目前用的es客户端有bug，range用的from to方式已经不被支持
		q := (&elastic.BoolQuery{}).Must(sq).MustNot(elastic.NewExistsQuery("deleted_at"))
		// 指定返回字段：仅包含 "ip" 和 "area"
		//fetchSource := elastic.NewFetchSourceContext(true).
		//	Include("ip", "area", "id") // 包含字段
		//// 使用 Scroll API 查询 device 索引，确保获取所有匹配数据
		//scrollService := es.GetEsClient().Scroll().
		//	Index(esmodel_vuln.NewPoc().IndexName()).
		//	Query(q).
		//	Size(1000).
		//	FetchSourceContext(fetchSource)
		rows, err := es.All[esmodel_vuln.Poc](1000, q, nil, "ip", "area", "id")
		if err != nil {
			return nil, fmt.Errorf("FetchPocIPAreaSet 查询失败: %v", err)
		}
		//for {
		//	searchResult, err := scrollService.Do(ctx)
		//	if err == io.EOF {
		//		break
		//	}
		//	if err != nil {
		//		err := scrollService.Clear(ctx)
		//		if err != nil {
		//			log.Errorf("ClearScroll 错误: %v", err)
		//		}
		//		return nil, fmt.Errorf("FetchPocIPAreaSet 查询失败: %v", err)
		//	}
		//	if len(searchResult.Hits.Hits) == 0 {
		//		break
		//	}
		for _, d := range rows {
			//var d struct {
			//	Id   string `json:"id"`
			//	Ip   string `json:"ip"`
			//	Area int    `json:"area"`
			//}
			//if err := json.Unmarshal(hit.Source, &d); err != nil {
			//	log.Errorf("反序列化 poc 失败: %v", err)
			//	continue
			//}
			// 检查每个 d 的 IP 数组是否有与 changedSet 中匹配的 (ip, area)
			if areas, exists := ipAreaMap[d.Ip]; exists {
				if _, areaExists := areas[int(d.Area)]; areaExists {
					ids = append(ids, d.Id)
					continue // 不必继续检查其余 IP
				}
			}
		}
		//}
		//// 清除 scroll
		//err := scrollService.Clear(ctx)
		//if err != nil {
		//	log.Errorf("ClearScroll 错误: %v", err)
		//}
	}

	return utils.ListDistinct[string](ids), nil
}

func getTaskId(msg queue.QueueMsg) (string, error) {
	taskIdVal, ok := msg.Values["task_id"]
	if !ok {
		return "", fmt.Errorf("融合消息中未包含任务ID")
	}
	taskId := ""
	if taskIdVal == "" {
		return "", fmt.Errorf("融合消息中未包含任务ID")
	} else {
		switch v := taskIdVal.(type) {
		case int64:
			taskId = fmt.Sprintf("%v", v)
		case int:
			taskId = fmt.Sprintf("%v", v)
		case float64:
			taskId = fmt.Sprintf("%v", v)
		case string:
			taskId = v
		}
	}
	return taskId, nil
}

func getTaskType(msg queue.QueueMsg) (string, error) {
	taskTypeVal := msg.Values["task_type"]
	if taskTypeVal == "" {
		return "", fmt.Errorf("融合消息中未包含任务类型")
	}
	return fmt.Sprintf("%v", taskTypeVal), nil
}

func getExpectedTotalCount(msg queue.QueueMsg) (int, error) {
	expectedTotalCountVal := msg.Values["total"]
	if expectedTotalCountVal == "" {
		return 0, fmt.Errorf("融合消息中未包含预期总数量")
	}
	switch v := expectedTotalCountVal.(type) {
	case int64:
		return int(v), nil
	case int:
		return v, nil
	case float64:
		return int(v), nil
	case string:
		return strconv.Atoi(v)
	}
	return 0, fmt.Errorf("预期总数量格式错误")
}

func getMergeRecordId(msg queue.QueueMsg) (uint64, error) {
	mergeRecordIdVal, ok := msg.Values["mr_id"]
	if !ok {
		return 0, fmt.Errorf("融合消息中未包含任务ID")
	}
	switch v := mergeRecordIdVal.(type) {
	case uint64:
		return v, nil
	case int64:
		return uint64(v), nil
	case int:
		return uint64(v), nil
	case string:
		return strconv.ParseUint(v, 10, 64)
	}
	return 0, fmt.Errorf("任务ID格式错误")
}

// printDutation, 打印融合耗时信息，每 10 秒打印一次，超过 1 分钟没有信号则停止打印
func printDutation(tracker *tracker.ProcessingTimeTracker, signalChan <-chan struct{}, businessType string, mlog *common_logs.Logger) bool {
	// 获取分布式锁，防止多个实例同时打印
	lockKey := fmt.Sprintf("lock:merge_flow_duration_print:%s", businessType)
	ok := distributedlock.Lock(lockKey, time.Now().Format("20060102150405"), 1800)
	if !ok {
		return false
	}

	go func() {
		minDuration := 0.0
		maxDuration := 10000.0
		shortInterval := 10 * time.Second
		longInterval := 1 * time.Minute
		// 定时打印耗时，每 10 秒打印一次

		// 此处的打印增加 5 秒随机值，避免多个实例同时打印时，导致打印并发
		rand := rand.New(rand.NewSource(time.Now().UnixMicro())).Intn(5000)
		shortInterval = time.Duration(rand)*time.Millisecond + shortInterval

		taskName := fmt.Sprintf("merge_flow_duration_print_%s", businessType)
		ticker := time.NewTicker(longInterval)
		for {
			select {
			case <-signalChan:
				ticker.Reset(longInterval)
				scheduler.Start(taskName, shortInterval, true, func() {
					averageDuration, count, err := tracker.GetFilteredAverage(minDuration, maxDuration)
					if err != nil {
						mlog.Warnf("BusinessType: %s, Error getting filtered average: %v", businessType, err)
						return
					}
					if averageDuration == 0 {
						mlog.Infof("BusinessType: %s, 无信号或耗时为 0", businessType)
					} else {
						mlog.Infof("BusinessType: %s, 每%s处理数据量: %d, 耗时在 %.2f 到 %.2f 毫秒之间的抽样数据平均耗时: %f", businessType, tracker.GetWindowSize().String(), count, minDuration, maxDuration, averageDuration)
					}
				})
			case <-ticker.C:
				scheduler.Stop(taskName)
			}
		}
	}()
	return true
}

// concurrentConflictLock 并发冲突解决方法
// 使用 redis 的 zset 实现并发冲突解决方法
// businessType: 业务类型
// uniqueKey: 唯一键
// 返回值：是否成功获取锁(true 表示成功获取锁，false 表示获取锁失败)，错误信息
// func concurrentConflictLock(businessType string, uniqueKey string) (bool, error) {
// 	redisClient := redis.GetRedisClient()
// 	redisKey := ""
// 	switch businessType {
// 	case "asset":
// 		redisKey = redis_helper.GetAssetMergeResultKey()
// 	case "vulnerability":
// 		redisKey = redis_helper.GetVulnMergeResultKey()
// 	case "vuln":
// 		redisKey = redis_helper.GetVulnMergeResultKey()
// 	case "person":
// 		redisKey = redis_helper.GetPersonMergeResultKey()
// 	case "device":
// 		redisKey = redis_helper.GetDeviceMergeResultKey()
// 	}
// 	if redisKey == "" {
// 		return false, fmt.Errorf("未找到对应的 redis key")
// 	}
// 	luaScript := `
// 		local key = KEYS[1]
// 		local value = ARGV[1]
// 		local time=redis.call('TIME')
// 		local currentTime = tonumber(time[1])

// 		-- 检查指定的 member 是否存在
// 		local s = redis.call('ZSCORE', key, value)

// 		if s then
// 			-- member 存在，解析 score，判断是否超过 5 秒
// 			-- score 是毫秒级时间戳
// 			local timestamp = tonumber(s)
// 			if timestamp + 5000 > currentTime then
// 				return 0
// 			else
// 				return 1
// 			end
// 		end

// 		-- 不存在冲突,更新 member 的 score 为当前时间戳
// 		redis.call('ZADD', key, currentTime, value)

// 		-- 返回 1 表示不冲突
// 		return 1
// 	`
// 	// 获取当前时间
// 	now := time.Now()
// 	// 转换为 Unix 毫秒时间戳
// 	millis := now.UnixNano() / int64(time.Millisecond)
// 	result, err := redisClient.Eval(context.Background(), luaScript, []string{redisKey}, uniqueKey, millis).Result()
// 	if err != nil {
// 		return false, err
// 	}
// 	return fmt.Sprintf("%v", result) == "1", nil
// }
