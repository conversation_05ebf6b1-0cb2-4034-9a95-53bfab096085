// 资产融合流程

package model

import (
	"context"
	"encoding/json"
	"fmt"
	"fobrain/fobrain/common/localtime"
	"fobrain/fobrain/logs"
	"fobrain/initialize/es"
	"fobrain/initialize/redis"
	"fobrain/mergeService/event"
	merge_helper "fobrain/mergeService/model/helper"
	model "fobrain/mergeService/model/manual_calibration"
	es_model "fobrain/models/elastic"
	"fobrain/models/elastic/assets"
	merge_models "fobrain/models/mysql/merge"
	"fobrain/models/mysql/strategy"
	redis_helper "fobrain/models/redis"
	"fobrain/pkg/cfg"
	distributedlock "fobrain/pkg/distributedLock"
	"fobrain/pkg/queue"
	"fobrain/pkg/tracker"
	"fobrain/pkg/utils"
	"fobrain/pkg/utils/common_logs"
	"math/rand"
	"runtime/debug"
	"strings"
	"sync"
	"time"

	"github.com/go-errors/errors"

	redis2 "github.com/go-redis/redis/v8"

	"github.com/olivere/elastic/v7"
)

var (
	// 资产融合记录写入通道
	assetMergeRecordChan = make(chan *assets.MergeRecords, 400)
	// 资产融合结果写入通道
	assetMergeResultChan = make(chan *MergeAsset, 400)
	// 资产融合记录列表，为批次写入结果使用
	assetMergeRecordList = &sync.Map{}
	// 资产融合记录 id 和 消息 id
	assetMergeRecordAndMsgId = &sync.Map{}
	// 融合涉及的IP区域列表，用于二次触发
	assetMergeIpList = &sync.Map{}
)

type AssetMergeFlow struct {
	mlog      *common_logs.Logger
	groupName string
	qName     string
}

type MergeAsset struct {
	Record           *assets.MergeRecords    // 融合记录
	Asset            *assets.Assets          // 最终融合结果
	MsgData          *assets.Assets          // 根据消息获取的资产数据，可能为空
	OriginalData     []*assets.ProcessAssets // 根据消息获取的过程数据，可能为空
	Rule             []*strategy.Strategy
	FieldValInfoList []*es_model.FieldValInfo // 所有字段的采信信息
	Msg              queue.QueueMsg
	TaskId           string
	StartTime        time.Time
	AssetRecoreId    string
}

// Merge 融合主流程
func (amf *AssetMergeFlow) Merge() {
	mlog := amf.mlog
	redisClient := redis.GetRedisClient()
	qName := cfg.LoadQueue().AssetMergeQueue
	amf.qName = qName
	amf.groupName = "asset_merge_flow"
	// 最大并发数量
	var maxConcurrency = cfg.LoadQueue().AssetMergeConcurrent
	msgChan, consumerName, err := queue.NewQueue(queue.QueueType_Redis).Subscribe(qName, amf.groupName, maxConcurrency)
	mlog.Info("订阅资产融合消息. qName: ", qName, " consumerName: ", consumerName)
	if err != nil {
		mlog.Error("订阅资产融合消息失败. qName: ", qName, " err: ", err)
		return
	}
	// defer queue.NewQueue(queue.QueueType_Redis).Unsubscribe(qName, consumerName)

	// 开启处理时间跟踪器
	timeTracker := tracker.NewProcessingTimeTracker(1*time.Minute, MergeFlowType_Asset)
	trackerSignalChan := make(chan struct{})
	enableTracker := printDutation(timeTracker, trackerSignalChan, MergeFlowType_Asset, mlog)

	// 开启融合结果写入协程
	go amf.WriteResult()
	// 开启融合记录写入协程
	go amf.WriteRecord()
	sem := make(chan struct{}, maxConcurrency)
	go func(sem chan struct{}) {
		for msg := range msgChan {
			sem <- struct{}{}
			go func(m queue.QueueMsg) {
				defer func() {
					if r := recover(); r != nil {
						wrapErr := errors.Wrap(r, 3)
						mlog.Errorf("资产融合消息处理失败. msg: %v, err: %v, stack: %+v", m, wrapErr.Error(), wrapErr.Stack())
						debug.PrintStack()

						// 记录异常
						mergeRecordId, _ := getMergeRecordId(m)
						merge_models.NewMergeExceptionsModel().Create(&merge_models.MergeExceptions{
							MergeRecordId:  mergeRecordId,
							BatchId:        "",
							ExceptionCode:  merge_models.ExceptionCodeProcessFailed,
							BusinessModule: merge_models.BusinessModuleAssetMerge,
							Status:         merge_models.StatusFailed,
							ErrorMessage:   fmt.Sprintf("资产融合消息处理失败. msg: %v, err: %v, stack: %+v", m, wrapErr.Error(), wrapErr.Stack()),
							Payload:        fmt.Sprintf("%+v", m),
						})
					}
					// 释放一个处理能力
					mlog.Debug("释放一个处理能力")
					<-sem
				}()
				now := time.Now()
				mlog.Info("收到资产融合消息. msg: ", m)
				// 构建融合记录-消息
				ma := &MergeAsset{
					Msg:           m,
					StartTime:     now,
					AssetRecoreId: UUIDStr(),
					Record: &assets.MergeRecords{
						Id: UUIDStr(),
						// 设置融合记录模式
						MergeMode: es_model.MergeMode_Auto,
						CreatedAt: localtime.NewLocalTime(now),
					},
				}
				assetMergeRecordAndMsgId.Store(ma.Record.Id, m.ID)

				// 根据消息组装获取关联数据的条件
				if len(m.Values) < 1 {
					amf.failedHandle("", merge_models.ExceptionCodeInvalidMsg, fmt.Sprintf("资产融合消息中未包含搜索条件. msg: %v", m), "", "", m, now, timeTracker, trackerSignalChan, enableTracker, false, false)
					return
				}

				taskId, err := getTaskId(m)
				if err != nil {
					amf.failedHandle(taskId, merge_models.ExceptionCodeInvalidMsg, fmt.Sprintf("获取任务ID失败. msg: %v, err: %v", m, err), "", "", m, now, timeTracker, trackerSignalChan, enableTracker, false, false)
					return
				}
				ma.TaskId = taskId
				isStart, err := startHandle(m, taskId, MergeFlowType_Asset, mlog)
				if err != nil {
					amf.failedHandle(taskId, merge_models.ExceptionCodeInvalidMsg, fmt.Sprintf("获取任务开始状态失败. msg: %v, err: %v", m, err), "", "", m, now, timeTracker, trackerSignalChan, enableTracker, false, false)
					return
				}
				if isStart {
					queue.NewQueue(queue.QueueType_Redis).Ack(qName, amf.groupName, m.ID)
					return
				}
				isEnd := endHandle(m, taskId, MergeFlowType_Asset, mlog)
				if isEnd {
					queue.NewQueue(queue.QueueType_Redis).Ack(qName, amf.groupName, m.ID)
					return
				}

				ctx := context.Background()
				// 查询 redis 中是否存在已处理未入库的数据
				retryCount := 0
				uniqueKey := fmt.Sprintf("%s-%v", m.Values["ip"], m.Values["area"])
			retry:
				lockKey := redis_helper.GetAssetMergeLockKey(uniqueKey)
				ok := distributedlock.Lock(lockKey, taskId, 120)
				if !ok {
					// 尝试获取锁内容，判断如果是同一个taskId，则不跳过处理
					lockContent, err := redisClient.Get(ctx, lockKey).Result()
					if err != nil {
						amf.failedHandle(taskId, merge_models.ExceptionCodeLockAcquireFailed, fmt.Sprintf("获取锁内容失败. taskId: %s, Msg: %v", taskId, m), "lockkey", lockKey, m, now, timeTracker, trackerSignalChan, enableTracker, true, false)
						return
					} else {
						if lockContent == taskId {
							// 如果是同一个taskId，则丢弃这条数据
							amf.failedHandle(taskId, merge_models.ExceptionCodeLockAcquireFailed, fmt.Sprintf("同一批数据已存在相同IP, 丢弃本数据. taskId: %s, Msg: %v", taskId, m), "lockkey", lockKey, m, now, timeTracker, trackerSignalChan, enableTracker, true, false)
							return
						}
					}
					// 生成 3000 到 5000 之间的随机毫秒数
					randomMilliseconds := rand.Intn(2001) + 3000
					amf.mlog.Infof("获取资产关联数据(未入库)冲突, 等待%d毫秒后继续处理. taskId: %s, Msg: %v", randomMilliseconds, taskId, m)
					// 将毫秒转换为 Duration
					sleepDuration := time.Duration(randomMilliseconds) * time.Millisecond
					// 休眠随机时间
					time.Sleep(sleepDuration)
					retryCount++
					if retryCount > 3 {
						amf.failedHandle(taskId, merge_models.ExceptionCodeLockAcquireFailed, fmt.Sprintf("获取资产关联数据(未入库)冲突, 超过最大重试次数. Msg: %v", m), "lockkey", lockKey, m, now, timeTracker, trackerSignalChan, enableTracker, false, false)
						return
					} else {
						goto retry
					}
				}

				// 获取消息对应的过程表唯一数据
				processAsset, err := amf.GetProcessAssetByMsg(ctx, m)
				if err != nil {
					amf.failedHandle(taskId, merge_models.ExceptionCodeDataFetchFailed, fmt.Sprintf("获取消息对应的过程表数据失败. Msg: %v, err: %v", m, err), "msgid", fmt.Sprintf("id:%v", m.Values["id"]), m, now, timeTracker, trackerSignalChan, enableTracker, false, true)
					return
				}
				// 设置触发源ID
				ma.Record.TiggerSourceId = processAsset.Source
				// 获取黑名单 删除的IP 对应源 都进黑名单？
				blacklistSource, err := getAssetSourceBlackList(processAsset.Ip, processAsset.Area)
				if err != nil {
					amf.failedHandle(taskId, merge_models.ExceptionCodeDataFetchFailed, fmt.Sprintf("获取资产源黑名单失败. msg: %v, err: %v", m, err), "ip-area", fmt.Sprintf("%s-%d", processAsset.Ip, processAsset.Area), m, now, timeTracker, trackerSignalChan, enableTracker, false, true)
					return
				}
				// 如果资产源在黑名单中，则丢弃数据
				if utils.ListContains(blacklistSource, processAsset.Source) {
					amf.failedHandle(taskId, merge_models.ExceptionCodeSourceInBlacklist, fmt.Sprintf("资产源在黑名单中. source: %d, msg: %v", processAsset.Source, m), "source-ip-area", fmt.Sprintf("%d-%s-%d", processAsset.Source, processAsset.Ip, processAsset.Area), m, now, timeTracker, trackerSignalChan, enableTracker, true, true)
					return
				}

				// 获取消息对应的唯一数据
				ma.MsgData, err = amf.GetExistMergeAssetByMsg(ctx, m)
				if err != nil {
					amf.failedHandle(taskId, merge_models.ExceptionCodeDataFetchFailed, fmt.Sprintf("获取消息对应的数据失败. Msg: %v, err: %v", m, err), "ip-area", fmt.Sprintf("%s-%d", processAsset.Ip, processAsset.Area), m, now, timeTracker, trackerSignalChan, enableTracker, false, true)
					return
				}
				// 只有产生新的融合资产时，才进行许可证资产限制检查
				// 如果消息数据为空，说明是新数据，则进行许可证资产限制检查
				if ma.MsgData == nil {
					if LicenseLimitCheckResultAsset {
						// 许可证资产限制检查结果为true，则不再融合新数据
						amf.mlog.Debugf("许可证资产限制检查不通过，不再融合新数据. msg: %v", ma.Msg)
						amf.failedHandle(taskId, merge_models.ExceptionCodeLicenseLimitReached, fmt.Sprintf("许可证资产限制检查不通过，不再融合新数据. msg: %v", m), "", "", m, now, timeTracker, trackerSignalChan, enableTracker, true, true)
						return
					}
				}

				// 获取关联数据
				originalData, total, err := amf.GetProcessDataList(ctx, processAsset.Ip, processAsset.Area, blacklistSource)
				if err != nil {
					amf.failedHandle(taskId, merge_models.ExceptionCodeDataFetchFailed, fmt.Sprintf("获取资产关联数据失败. Msg: %v, err: %v", m, err), "ip-area", fmt.Sprintf("%s-%d", processAsset.Ip, processAsset.Area), m, now, timeTracker, trackerSignalChan, enableTracker, false, true)
					return
				}
				if len(originalData) < 1 {
					amf.failedHandle(taskId, merge_models.ExceptionCodeDataFetchFailed, fmt.Sprintf("获取资产关联数据为空. Msg:%v", m), "ip-area", fmt.Sprintf("%s-%d", processAsset.Ip, processAsset.Area), m, now, timeTracker, trackerSignalChan, enableTracker, false, true)
					return
				}
				originalInfo := utils.ListColumn(originalData, func(item *assets.ProcessAssets) string {
					return fmt.Sprintf("id:%s,source:%v,node:%v", item.Id, item.Source, item.Node)
				})
				mlog.Debugf("获取资产关联数据成功. Msg id: %s, total: %d, originalInfo: %s", m.ID, total, originalInfo)
				// 构建融合记录-关联数据
				ma.OriginalData = originalData

				// 获取融合规则
				rules, err := merge_helper.GetMergeRules(strategy.BusinessType_AssetMerge, mlog)
				if err != nil {
					amf.failedHandle(taskId, merge_models.ExceptionCodeMergeRuleFetchFailed, fmt.Sprintf("获取资产融合规则失败. err: %v", err), "", "", m, now, timeTracker, trackerSignalChan, enableTracker, false, true)
					return
				}
				// 构建融合记录-规则
				ma.Rule = rules
				ma.Record.Strategies = rules

				// 开启执行融合逻辑
				amf.ExecuteMerge(ctx, ma)

				assetMergeIpList.Store(processAsset.Ip, processAsset.Area)
				// 计算任务处理耗时
				duration := time.Now().UnixMilli() - now.UnixMilli()
				mlog.Infof("资产融合消息处理完成. msg: %v,耗时: %vms", m, duration)
				// 记录任务处理量
				tracker.IncrementCount(redis_helper.AssetMergeTaskKey(fmt.Sprintf("%v", taskId), "count"), 1)
				//if cfg.LoadQueue().LogMergeCost {
				// 记录耗时统计
				timeTracker.AddRecord(fmt.Sprintf("%d:%s", duration, m.ID))
				// 统计任务总耗时
				tracker.IncrementCount(redis_helper.AssetMergeTotalTimeKey(fmt.Sprintf("%v", taskId)), int(duration))
				//}
				if enableTracker {
					// 给追踪器发信号，打印耗时信息
					trackerSignalChan <- struct{}{}
				}
			}(msg)
		}
	}(sem)
}

// failedHandle 处理失败逻辑
// taskId 任务ID
// errType 错误类型
// errMsg 错误信息
// m 消息体
// start 开始时间
// timeTracker 时间追踪器
// trackerSignalChan 时间追踪器信号通道
// enableTracker 是否启用时间追踪器
// isDiscard 数据是否丢弃，相对于失败，丢弃的数据不记录失败次数
func (amf *AssetMergeFlow) failedHandle(taskId string, errType, errMsg string, identifier, identifierValue string, m queue.QueueMsg, start time.Time, timeTracker *tracker.ProcessingTimeTracker, trackerSignalChan chan struct{}, enableTracker bool, isDiscard bool, deleteLock bool) {
	amf.mlog.Warnf(errMsg)

	// 记录任务处理量
	if !isDiscard {
		amf.mlog.Errorf("资产融合失败. taskId: %s, m: %+v, errMsg: %s", taskId, m, errMsg)
		tracker.IncrementCount(redis_helper.AssetMergeTaskKey(taskId, "failed_count"), 1)
	} else {
		amf.mlog.Warnf("资产融合丢弃. taskId: %s, m: %+v, errMsg: %s", taskId, m, errMsg)
		tracker.IncrementCount(redis_helper.AssetMergeTaskKey(taskId, "discarded_count"), 1)
	}

	// 计算任务处理耗时
	duration := time.Now().UnixMilli() - start.UnixMilli()
	// 记录耗时统计
	timeTracker.AddRecord(fmt.Sprintf("%d:%s", duration, m.ID))
	// 统计任务总耗时
	tracker.IncrementCount(redis_helper.AssetMergeTotalTimeKey(taskId), int(duration))

	if enableTracker {
		// 给追踪器发信号，打印耗时信息
		trackerSignalChan <- struct{}{}
	}
	// 确认消息已处理
	queue.NewQueue(queue.QueueType_Redis).Ack(amf.qName, amf.groupName, m.ID)

	if deleteLock {
		// 删除入库数据的锁
		redisKey := redis_helper.GetAssetMergeLockKey(fmt.Sprintf("%s-%v", m.Values["ip"], m.Values["area"]))
		distributedlock.Unlock(redisKey, taskId)
		amf.mlog.Infof("处理失败，删除入库数据的锁. redisKey: %s, msgId: %s", redisKey, taskId)
	}

	mergeRecordId, _ := getMergeRecordId(m)
	exception := &merge_models.MergeExceptions{
		MergeRecordId:  mergeRecordId,
		BatchId:        taskId,
		ExceptionCode:  errType,
		BusinessModule: merge_models.BusinessModuleAssetMerge,
		Status: func() string {
			if isDiscard {
				return merge_models.StatusDiscarded
			}
			return merge_models.StatusFailed
		}(),
		ErrorMessage:    errMsg,
		Payload:         fmt.Sprintf("%+v", m),
		Identifier:      identifier,
		IdentifierValue: identifierValue,
	}
	merge_models.NewMergeExceptionsModel().Create(exception)
}

// getAssetSourceBlackList 获取资产的源黑名单
func getAssetSourceBlackList(ip string, area int) ([]uint64, error) {
	key := redis_helper.BlackSourceKey(fmt.Sprintf("%s_%d", ip, area))
	val, err := redis.GetRedisClient().Get(context.Background(), key).Bytes()
	for {
		if err != nil {
			if errors.Is(err, redis2.Nil) {
				return make([]uint64, 0), nil
			}
			logs.GetLogger().Errorf("获取redis资产源黑名单失败. key: %s, err: %v", key, err)
			break
		}
		if len(val) == 0 {
			break
		}

		var blackSource []uint64
		if err = json.Unmarshal(val, &blackSource); err != nil {
			logs.GetLogger().Errorf("反序列化资产源黑名单失败. key: %s, err: %v", key, err)
			break
		}
		return blackSource, nil
	}
	state := redis.GetRedisClient().Get(context.Background(), redis_helper.BlackSourceCacheStateKey()).Val()
	if state == "1" {
		return []uint64{}, fmt.Errorf("从redis获取黑名单失败")
	}
	// 根据 ip+area 查询黑名单
	query := elastic.NewBoolQuery()
	query = query.Must(elastic.NewTermQuery("ip", ip), elastic.NewTermQuery("area", area))
	query.Must(elastic.NewExistsQuery("deleted_at"))
	dataList, err := es.All[assets.Assets](100, query, nil, "source_ids")
	if err != nil {
		return nil, err
	}
	blackSource := make([]uint64, 0)
	for _, item := range dataList {
		blackSource = append(blackSource, item.SourceIds...)
	}
	blackSource = utils.ListDistinctNonZero(blackSource)
	return blackSource, nil
}

// GetProcessDataList 获取关联数据，已排除已提取设备的数据
func (amf *AssetMergeFlow) GetProcessDataList(ctx context.Context, ip string, area int, sourceBlackList []uint64) ([]*assets.ProcessAssets, int64, error) {
	cacheKey := redis_helper.AssetsProcessKey("ip_area_map_ids")
	redisClient := redis.GetRedisClient()
	idsJson, err := redisClient.HGet(context.Background(), cacheKey, fmt.Sprintf("%s_%d", ip, area)).Result()
	if err != nil {
		if errors.Is(err, redis2.Nil) {
			return nil, 0, nil
		}
		return nil, 0, err
	}
	if len(idsJson) == 0 {
		return nil, 0, nil
	}
	ids := make([]string, 0)
	if err = json.Unmarshal([]byte(idsJson), &ids); err != nil {
		return nil, 0, err
	}
	esClient := es.GetEsClient()
	mget := esClient.Mget()
	for _, id := range ids {
		mget.Add(elastic.NewMultiGetItem().Id(id).Index(assets.NewProcessAssetsModel().IndexName()))
	}
	mgetResult, err := mget.Do(context.Background())
	if err != nil {
		return nil, 0, err
	}
	dataList := make([]*assets.ProcessAssets, 0)
	for _, item := range mgetResult.Docs {
		if item.Source != nil {
			var processAssets assets.ProcessAssets
			err = json.Unmarshal(item.Source, &processAssets)
			if err != nil {
				return nil, 0, err
			}
			if utils.ListContains(sourceBlackList, processAssets.Source) {
				continue
			}
			dataList = append(dataList, &processAssets)
		}
	}
	return dataList, int64(len(dataList)), nil
}

// GetProcessAssetByMsg 获取消息对应的过程表唯一数据
func (amf *AssetMergeFlow) GetProcessAssetByMsg(ctx context.Context, msg queue.QueueMsg) (*assets.ProcessAssets, error) {
	var idStr string
	id, exist := msg.Values["id"]
	if !exist {
		return nil, fmt.Errorf("消息中未包含id字段")
	}
	switch v := id.(type) {
	case string:
		idStr = v
	case float64:
		idStr = fmt.Sprintf("%d", int(v))
	case int:
		idStr = fmt.Sprintf("%d", v)
	default:
		return nil, fmt.Errorf("id字段类型错误")
	}
	data, err := es.GetById[assets.ProcessAssets](idStr)
	if err != nil {
		return nil, err
	}
	return data, nil
}

// GetExistMergeAssetByMsg 获取消息对应的已融合数据
func (amf *AssetMergeFlow) GetExistMergeAssetByMsg(ctx context.Context, msg queue.QueueMsg) (*assets.Assets, error) {
	if len(msg.Values) < 1 {
		return nil, errors.New("消息中未包含搜索条件")
	}
	ip, ok := msg.Values["ip"]
	if !ok {
		return nil, errors.New("消息中未包含ip")
	}
	area, ok := msg.Values["area"]
	if !ok {
		return nil, errors.New("消息中未包含area")
	}
	q := elastic.NewBoolQuery()
	q = q.Must(elastic.NewTermQuery("area", area), elastic.NewTermQuery("ip", ip))
	q.MustNot(elastic.NewExistsQuery("deleted_at"))
	data, err := es.First[assets.Assets](q, nil)
	if err != nil {
		amf.mlog.Warnf("获取已融合资产数据失败. msg: %v, err: %v", msg, err)
		return nil, err
	}
	return data, nil
}

// ExecuteMerge 执行融合过程
func (amf *AssetMergeFlow) ExecuteMerge(ctx context.Context, ma *MergeAsset) {
	ma.Record.Ip = ma.OriginalData[0].Ip
	ma.Record.Area = ma.OriginalData[0].Area
	ma.Record.Strategies = ma.Rule
	executor := &AssetStrategyExecutor{
		SourceData: ma.OriginalData,
		Strategy:   ma.Rule,
	}
	strategyContext := &StrategyExecutorContext[*assets.Assets]{
		Executor: executor,
	}
	// 根据融合策略，执行字段融合
	ma.Asset, ma.FieldValInfoList = strategyContext.Executor.Execute()
	assetId := UUIDStr()
	ma.Asset.Id = assetId
	ma.Asset.Ip = ma.OriginalData[0].Ip
	ma.Asset.IpType = utils.GetIpType(ma.Asset.Ip)
	// issue:1593 https://git.gobies.org/caasm/fobrain/-/issues/1593#note_248455
	// 如果消息数据为空，则根据原始数据设置网络类型
	if ma.MsgData == nil {
		// 获取所有节点的网络类型设置
		networkTypeList := utils.ListDistinctNonZero(utils.ListColumn(ma.OriginalData, func(item *assets.ProcessAssets) int {
			return item.NetworkType
		}))
		// 如果网络类型不一致，则设置为内网
		// 2024-11-07 飞哥、秀坤讨论确定
		if len(networkTypeList) == 0 {
			ma.Asset.NetworkType = utils.GetNetworkType(ma.Asset.Ip)
		} else if len(networkTypeList) > 1 {
			ma.Asset.NetworkType = 1
		} else {
			// 如果网络类型为3，则设置为智能判断
			if networkTypeList[0] == 3 {
				ma.Asset.NetworkType = utils.GetNetworkType(ma.Asset.Ip)
			} else {
				// 否则设置为节点设置的网络类型
				ma.Asset.NetworkType = networkTypeList[0]
				// 如果网络类型不是1、2、3，则智能判断
				if networkTypeList[0] < 1 || networkTypeList[0] > 3 {
					ma.Asset.NetworkType = utils.GetNetworkType(ma.Asset.Ip)
				}
			}
		}
	} else {
		// 如果消息数据不为空，则根据消息数据设置网络类型
		ma.Asset.NetworkType = ma.MsgData.NetworkType
	}
	ma.Asset.Area = ma.OriginalData[0].Area
	ma.Asset.Fid = fmt.Sprintf("%s:%d", ma.Asset.Ip, ma.Asset.Area)
	ma.Asset.FidHash = utils.Md5Hash(ma.Asset.Fid)

	// 存在融合记录
	if ma.MsgData != nil {
		// 保存历史用过的所有process_id,防止删除时遗漏
		ma.Asset.AllProcessIds = append(ma.Asset.AllProcessIds, ma.MsgData.AllProcessIds...)
		ma.Asset.AllProcessIds = utils.ListDistinctNonZero(ma.Asset.AllProcessIds)
		ma.Asset.MergeCount = ma.MsgData.MergeCount + 1
		ma.Asset.Id = ma.MsgData.Id
		ma.Asset.CreatedAt = ma.MsgData.CreatedAt

		// 更新人工校准数据
		assetCalibration := &model.AssetManualCalibration{}
		err := assetCalibration.UpdateMergeResult(ma.Asset, ma.MsgData)
		if err != nil {
			amf.mlog.Warnf("更新人工校准数据失败. err: %v", err)
		}
	} else {
		ma.Asset.MergeCount = 1
		ma.Asset.CreatedAt = localtime.NewLocalTime(time.Now())
	}

	// 触发资产数据融合单条数据结束事件
	err := event.NewEventBus().Emit(event.Event_Asset_MergeData_End, ma.Asset, ma.MsgData)
	if err != nil {
		amf.mlog.Warnf("触发资产数据融合单条数据结束事件失败. err: %v", err)
	}
	ma.Asset.UpdatedAt = localtime.NewLocalTime(time.Now())
	ma.Record.AssetId = ma.Asset.Id
	ma.Record.SourceIds = ma.Asset.SourceIds
	ma.Record.AllSourceIds = ma.Asset.SourceIds
	ma.Record.NodeIds = ma.Asset.NodeIds
	ma.Record.AllNodeIds = ma.Asset.NodeIds
	ma.Record.AssetTaskIds = ma.Asset.TaskDataIds
	ma.Record.FieldValInfoList = ma.FieldValInfoList
	// 记录融合记录信息
	assetMergeRecordList.Store(ma.Asset.Id, ma.Record)
	// 因为结果表和结果记录表id不同，所以需要保存两份
	assetMergeRecordList.Store(ma.AssetRecoreId, ma.Record)
	assetMergeResultChan <- ma
}
func (amf *AssetMergeFlow) FlushRecords(records []assets.AssetRecord, currentBatchIpList []string, ipAreaList map[string]struct{}) error {
	mlog := amf.mlog
	assetIndexName := assets.NewAssets().IndexName()
	assetRecordIndexName := assets.NewAssetRecord().IndexName()
	err := event.NewEventBus().Emit(event.EvtAssetMergeDataBeforeWrite, records, ipAreaList)
	if err != nil {
		mlog.Warnf("资产批次写入前事件执行失败. err: %v", err)
	}
	bulkService := es.GetEsClient().Bulk()
	for _, record := range records {
		// 融合结果，资产索引
		bulkService.Add(elastic.NewBulkUpdateRequest().UseEasyJSON(true).DocAsUpsert(true).Index(assetIndexName).Id(record.Asset.Id).Doc(record.Asset))
		// 融合记录索引
		bulkService.Add(elastic.NewBulkCreateRequest().Index(assetRecordIndexName).Id(record.Id).Doc(record))
	}
	start := time.Now()
	ipList := strings.Join(currentBatchIpList, ",")
	// 里面有bulkService reset，所以外边不需要了
	err = assetResultBulkServiceDo(bulkService, ipList, mlog)
	bulkService = nil
	if err != nil {
		mlog.Warnf("资产融合结果或记录写入失败. err: %v, %+v", err, utils.AnyToStr(records))
		return err
	} else {
		end := time.Now()
		mlog.Infof("资产融合结果或记录写入完成. current batch count: %d, current time: %s, cost: %dms", len(records), localtime.NewLocalTime(end).Format("2006-01-02 15:04:05:0000"), end.Sub(start).Milliseconds())
		return nil
	}
}

// WriteResult 执行结果写入逻辑，包括写入融合结果和融合记录
func (amf *AssetMergeFlow) WriteResult() {
	currentBatchIpList := make([]string, 0)
	var records []assets.AssetRecord
	var ipAreaList = make(map[string]struct{})
	ticker := time.NewTicker(4 * time.Second)
	defer ticker.Stop()
	for {
		select {
		case <-ticker.C:
			if len(records) > 0 {
				amf.mlog.Infof("4秒未收到融合结果消息，开始写入剩余数据. count: %d", len(records))
				if err := amf.FlushRecords(records, currentBatchIpList, ipAreaList); err == nil {
					// 上层已经记录过日志，不在重复打印
					records = make([]assets.AssetRecord, 0)
					currentBatchIpList = make([]string, 0)
					ipAreaList = make(map[string]struct{})
				}
			}
		case v := <-assetMergeResultChan:
			assetRecord := assets.AssetRecord{Asset: v.Asset}
			assetRecord.Id = v.AssetRecoreId
			assetRecord.AssetId = v.Asset.Id
			assetRecord.Ip = v.Asset.Ip
			assetRecord.Area = v.Asset.Area
			assetRecord.BackupMode = "full"
			assetRecord.BackupTime = localtime.NewLocalTime(v.StartTime)

			currentBatchIpList = append(currentBatchIpList, fmt.Sprintf("%s-%d:%s", v.Asset.Ip, v.Asset.Area, v.TaskId))
			records = append(records, assetRecord)
			if _, ok := ipAreaList[v.Asset.Ip]; !ok {
				ipAreaList[fmt.Sprintf("%s_%d", v.Asset.Ip, v.Asset.Area)] = struct{}{}
			}
			count := len(records)
			if count >= 100 {
				if err := amf.FlushRecords(records, currentBatchIpList, ipAreaList); err == nil {
					// 上层已经记录过日志，不在重复打印
					records = make([]assets.AssetRecord, 0)
					currentBatchIpList = make([]string, 0)
					ipAreaList = make(map[string]struct{})
				}
			}
		}
	}
}

// assetResultBulkServiceDo 执行bulk写入
func assetResultBulkServiceDo(bulkService *es.SafeBulkService, currentBatchIpList string, mlog *common_logs.Logger) (err error) {
	defer func() {
		if r := recover(); r != nil {
			mlog.Warnf("资产融合结果或记录写入失败. err: %v", r)
			err = fmt.Errorf("panic: %v", r)
			return
		}
	}()
	mlog.Infof("开始写入资产融合结果. current batch count: %d", bulkService.NumberOfActions())
	resp, e := bulkService.Do(context.Background())
	if e != nil {
		mlog.Warnf("资产融合结果或记录写入失败. err: %v", e)
		err = e
		return
	} else {
		// 复制一份，防止数据被覆盖
		items := resp.Items
		go assetResultWriteHandler(items, mlog)
		mlog.Debug("资产融合结果写入成功.")
		bulkService.Reset()

		// 删除入库数据的锁
		list := strings.Split(currentBatchIpList, ",")
		for _, item := range list {
			key, taskId := strings.Split(item, ":")[0], strings.Split(item, ":")[1]
			redisKey := redis_helper.GetAssetMergeLockKey(key)
			ok := distributedlock.Unlock(redisKey, taskId)
			mlog.Debugf("删除入库数据的锁. redisKey: %s, taskId: %s, ok: %v", redisKey, taskId, ok)
		}
	}
	return nil
}

// assetResultWriteHandler 处理bulk写入结果
func assetResultWriteHandler(items []map[string]*elastic.BulkResponseItem, mlog *common_logs.Logger) {
	assetIndexName := assets.NewAssets().IndexName()
	assetRecordIndexName := assets.NewAssetRecord().IndexName()
	for _, item := range items {
		for op, detail := range item {
			if op == "update" || op == "create" {
				// 结果表
				if detail.Index == assetIndexName {
					if detail.Error != nil {
						mlog.Warnf("资产融合结果写入失败. op: %s, detail: %v", op, detail)
						record, ok := assetMergeRecordList.Load(detail.Id)
						if ok {
							r := record.(*assets.MergeRecords)
							r.Status = 2
							r.Message = fmt.Sprintf("资产融合结果写入失败. op: %s, detail: %v", op, detail)
							assetMergeRecordChan <- r
							assetMergeRecordList.Delete(detail.Id)
						}
					} else {
						mlog.Debug(fmt.Sprintf("资产融合结果写入成功. op: %s, detail: %v", op, detail))
						record, ok := assetMergeRecordList.Load(detail.Id)
						if ok {
							r := record.(*assets.MergeRecords)
							r.Status = 1
							r.Message = fmt.Sprintf("资产融合结果写入成功. op: %s, detail: %v", op, detail)
							assetMergeRecordChan <- r
							assetMergeRecordList.Delete(detail.Id)
						}
					}
				} else if detail.Index == assetRecordIndexName {
					if detail.Error != nil {
						mlog.Warnf("资产记录写入失败. op: %s, detail: %v", op, detail)
						record, ok := assetMergeRecordList.Load(detail.Id)
						if ok {
							r := record.(*assets.MergeRecords)
							r.Status = 2
							r.Message = fmt.Sprintf("资产记录写入失败. op: %s, detail: %v", op, detail)
							assetMergeRecordChan <- r
							assetMergeRecordList.Delete(detail.Id)
						}
					} else {
						mlog.Debug(fmt.Sprintf("资产记录写入成功. op: %s, detail: %v", op, detail))
						record, ok := assetMergeRecordList.Load(detail.Id)
						if ok {
							r := record.(*assets.MergeRecords)
							r.Status = 1
							r.AssetRecordId = detail.Id
							assetMergeRecordChan <- r
							assetMergeRecordList.Delete(detail.Id)
						}
					}
				}
			}
		}
	}
}
func (amf *AssetMergeFlow) FlushMergeRecord(bulkService *es.SafeBulkService) error {
	amf.mlog.Infof("开始写入资产融合记录. count: %d", bulkService.NumberOfActions())
	_, err := bulkService.Do(context.Background())
	if err != nil {
		amf.mlog.Warnf("资产融合记录写入失败. err: %v", err)
	} else {
		amf.mlog.Infof("资产融合记录写入成功.")
		bulkService = bulkService.Reset()
	}
	return nil
}
func (amf *AssetMergeFlow) WriteRecord() {
	q := queue.NewQueue(queue.QueueType_Redis)
	mlog := amf.mlog
	bulkService := es.GetEsClient().Bulk()
	indexName := assets.NewMergeRecordsModel().IndexName()
	ticker := time.NewTicker(4 * time.Second)
	defer ticker.Stop()
	for {
		select {
		case <-ticker.C:
			if bulkService.NumberOfActions() > 0 {
				mlog.Infof("4秒未收到资产融合记录消息，开始写入剩余数据. count: %d", bulkService.NumberOfActions())
				_ = amf.FlushMergeRecord(bulkService)
			}
		case r := <-assetMergeRecordChan:
			// ack
			msgId, ok := assetMergeRecordAndMsgId.Load(r.Id)
			mlog.Debugf("Ack-asset 开始确认消息. msgId: %s", msgId)
			if ok {
				q.Ack(cfg.LoadQueue().AssetMergeQueue, amf.groupName, msgId.(string))
				assetMergeRecordAndMsgId.Delete(r.Id)
			}
			bulkService.Add(elastic.NewBulkUpdateRequest().DocAsUpsert(true).Index(indexName).Id(r.Id).Doc(r))
			if bulkService.NumberOfActions() >= 100 {
				_ = amf.FlushMergeRecord(bulkService)
			}
		}
	}
}
