// 设备融合流程

package model

import (
	"context"
	"encoding/json"
	"fmt"
	"fobrain/fobrain/common/localtime"
	"fobrain/initialize/es"
	"fobrain/initialize/redis"
	"fobrain/mergeService/event"
	merge_helper "fobrain/mergeService/model/helper"
	model "fobrain/mergeService/model/manual_calibration"
	es_model "fobrain/models/elastic"
	esmodel_device "fobrain/models/elastic/device"
	esmodel_person "fobrain/models/elastic/staff"
	merge_models "fobrain/models/mysql/merge"
	"fobrain/models/mysql/strategy"
	redis_helper "fobrain/models/redis"
	"fobrain/pkg/cfg"
	distributedlock "fobrain/pkg/distributedLock"
	"fobrain/pkg/queue"
	"fobrain/pkg/tracker"
	"fobrain/pkg/utils"
	"fobrain/pkg/utils/common_logs"
	"math/rand"
	"strings"
	"sync"
	"time"

	"github.com/go-errors/errors"

	models "fobrain/models/elastic"

	redisV8 "github.com/go-redis/redis/v8"
	"github.com/olivere/elastic/v7"
)

var (
	// 设备融合记录写入通道
	deviceMergeRecordChan = make(chan *esmodel_device.MergeRecords, 100)
	// 设备融合结果写入通道
	deviceMergeResultChan = make(chan *MergeDevice, 100)
	// 设备融合记录列表，为批次写入结果使用
	deviceMergeRecordList = &sync.Map{}
	// 设备融合记录 id 和 消息 id
	deviceMergeRecordAndMsgId = &sync.Map{}
	// 设备融合处理时间统计
	deviceTimeTracker = &tracker.ProcessingTimeTracker{}
)

type DeviceMergeFlow struct {
	mlog      *common_logs.Logger
	groupName string // 组名称
	qName     string // 融合消息队列名称
}

type MergeDevice struct {
	Record         *esmodel_device.MergeRecords    // 融合记录
	Device         *esmodel_device.Device          // 融合结果
	UniqueKey      string                          // 唯一值
	MsgData        *esmodel_device.Device          // 人工校准数据
	OriginalData   []*esmodel_device.ProcessDevice // 所有关联数据
	Rule           []*strategy.Strategy            // 融合规则
	FieldValInfo   []*es_model.FieldValInfo        // 字段采信信息
	Msg            queue.QueueMsg                  // 原始消息
	TaskId         string                          // 任务ID
	StartTime      time.Time                       // 开始时间
	DeviceRecoreId string                          // 设备融合记录 id
}

// Merge 融合主流程
func (dmf *DeviceMergeFlow) Merge() {
	mlog := dmf.mlog
	redisClient := redis.GetRedisClient()
	// 设备融合消息来源与设备融合消息来源是一个
	qName := cfg.LoadQueue().DeviceMergeQueue
	dmf.qName = qName
	dmf.groupName = "device_merge_flow"
	// 最大并发数量
	var maxConcurrency = cfg.LoadQueue().DeviceMergeConcurrent
	msgChan, consumerName, err := queue.NewQueue(queue.QueueType_Redis).Subscribe(qName, dmf.groupName, maxConcurrency)
	mlog.Info("订阅设备融合消息. qName: ", qName, " consumerName: ", consumerName)
	if err != nil {
		mlog.Error("订阅设备融合消息失败. qName: ", qName, " err: ", err)
		return
	}
	// defer queue.NewQueue(queue.QueueType_Redis).Unsubscribe(qName, consumerName)

	// 开启处理时间跟踪器
	deviceTimeTracker = tracker.NewProcessingTimeTracker(1*time.Minute, MergeFlowType_Device)
	trackerSignalChan := make(chan struct{})
	enableTracker := printDutation(deviceTimeTracker, trackerSignalChan, MergeFlowType_Device, mlog)

	// 开启融合结果写入协程
	go dmf.WriteResult()
	// 开启融合记录写入协程
	go dmf.WriteRecord()
	sem := make(chan struct{}, maxConcurrency)
	go func(sem chan struct{}) {
		for msg := range msgChan {
			sem <- struct{}{}
			go func(m queue.QueueMsg) {
				defer func() {
					if r := recover(); r != nil {
						wrapError := errors.Wrap(r, 3)
						mlog.Errorf("设备融合消息处理失败. msg: %v, err: %v, stack: %+v", m, wrapError.Error(), wrapError.Stack())

						mergeRecordId, _ := getMergeRecordId(m)
						merge_models.NewMergeExceptionsModel().Create(&merge_models.MergeExceptions{
							MergeRecordId:  mergeRecordId,
							BatchId:        "",
							ExceptionCode:  merge_models.ExceptionCodeProcessFailed,
							BusinessModule: merge_models.BusinessModuleDeviceMerge,
							Status:         merge_models.StatusFailed,
							ErrorMessage:   fmt.Sprintf("设备融合消息处理失败. msg: %v, err: %v, stack: %+v", m, wrapError.Error(), wrapError.Stack()),
							Payload:        fmt.Sprintf("%+v", m),
						})
					}
					// 释放一个处理能力
					mlog.Debug("释放一个处理能力")
					<-sem
				}()
				now := time.Now()
				mlog.Info("收到设备融合消息. msg: ", m)
				// 构建融合记录-消息
				md := &MergeDevice{
					Msg:       m,
					StartTime: now,
					Record: &esmodel_device.MergeRecords{
						Id:        UUIDStr(),
						CreatedAt: localtime.NewLocalTime(now),
						MergeMode: models.MergeMode_Auto,
					},
					DeviceRecoreId: UUIDStr(),
				}
				deviceMergeRecordAndMsgId.Store(md.Record.Id, m.ID)

				// 根据消息组装获取关联数据的条件
				if len(m.Values) < 1 {
					dmf.failedHandle("", merge_models.ExceptionCodeInvalidMsg, fmt.Sprintf("设备融合消息中未包含搜索条件, Msg: %v", m), "", "", m, now, trackerSignalChan, enableTracker, "", false)
					return
				}

				taskId, err := getTaskId(m)
				if err != nil {
					dmf.failedHandle("", merge_models.ExceptionCodeInvalidMsg, fmt.Sprintf("获取任务ID失败. err: %v", err), "", "", m, now, trackerSignalChan, enableTracker, "", false)
					return
				}
				md.TaskId = taskId
				isStart, err := startHandle(m, taskId, MergeFlowType_Device, mlog)
				if err != nil {
					dmf.failedHandle(taskId, merge_models.ExceptionCodeInvalidMsg, fmt.Sprintf("获取任务开始状态失败. msg: %v, err: %v", m, err), "", "", m, now, trackerSignalChan, enableTracker, "", false)
					return
				}
				if isStart {
					queue.NewQueue(queue.QueueType_Redis).Ack(qName, dmf.groupName, m.ID)
					return
				}
				isEnd := endHandle(m, taskId, MergeFlowType_Device, mlog)
				if isEnd {
					queue.NewQueue(queue.QueueType_Redis).Ack(qName, dmf.groupName, m.ID)
					return
				}

				ctx := context.Background()

				// 获取消息对应的唯一数据
				msgData, err := GetProcessDataByMsg(m)
				if err != nil {
					dmf.failedHandle(taskId, merge_models.ExceptionCodeDataFetchFailed, fmt.Sprintf("获取消息对应的数据失败.Msg: %v, err: %v", m, err), "id", fmt.Sprintf("%v", m.Values["id"]), m, now, trackerSignalChan, enableTracker, "", false)
					return
				}
				md.UniqueKey = msgData.UniqueKey
				md.Record.UniqueKey = msgData.UniqueKey
				// 设置触发源ID
				md.Record.TiggerSourceId = msgData.Source

				// 唯一标识
				retryCount := 0
				msgData.UniqueKey = strings.TrimSpace(msgData.UniqueKey)
				lockKey := redis_helper.GetDeviceMergeLockKey(msgData.UniqueKey)
			retry:
				ok := distributedlock.Lock(lockKey, taskId, 120)
				if !ok {
					// 尝试获取锁内容，判断如果是同一个taskId，则不跳过处理
					lockContent, err := redisClient.Get(context.Background(), lockKey).Result()
					if err != nil {
						dmf.failedHandle(taskId, merge_models.ExceptionCodeLockAcquireFailed, fmt.Sprintf("获取锁内容失败. taskId: %s, Msg: %v", taskId, m), "lockKey", lockKey, m, now, trackerSignalChan, enableTracker, "", false)
						return
					} else {
						if lockContent == taskId {
							// 如果是同一个taskId，则丢弃这条数据
							dmf.failedHandle(taskId, merge_models.ExceptionCodeDuplicateKeyInBatch, fmt.Sprintf("同一批数据已存在相同数据, 丢弃本数据. taskId: %s, Msg: %v", taskId, m), "lockKey", lockKey, m, now, trackerSignalChan, enableTracker, "", true)
							return
						}
					}
					// 生成 3000 到 5000 之间的随机毫秒数
					randomMilliseconds := rand.Intn(2001) + 3000
					dmf.mlog.Infof("获取设备关联数据(未入库)冲突, 等待%d毫秒后继续处理. taskId: %s, Msg: %v", randomMilliseconds, taskId, m)
					// 将毫秒转换为 Duration
					sleepDuration := time.Duration(randomMilliseconds) * time.Millisecond
					// 休眠随机时间
					time.Sleep(sleepDuration)
					retryCount++
					if retryCount > 3 {
						dmf.failedHandle(taskId, merge_models.ExceptionCodeLockAcquireFailed, fmt.Sprintf("获取设备关联数据(未入库)冲突, 超过最大重试次数. Msg: %v", m), "lockKey", lockKey, m, now, trackerSignalChan, enableTracker, "", false)
						return
					} else {
						goto retry
					}
				}

				// 获取已提取的设备唯一数据
				md.MsgData, err = GetExistMergeDeviceByQuery(md.UniqueKey)
				if err != nil {
					dmf.failedHandle(taskId, merge_models.ExceptionCodeDataFetchFailed, fmt.Sprintf("获取已提取的设备唯一数据失败.Msg: %v, err: %v", m, err), "uniqueKey", md.UniqueKey, m, now, trackerSignalChan, enableTracker, lockKey, false)
					return
				}
				// 被删除的设备不在融合
				if md.MsgData != nil && md.MsgData.DeletedAt != nil {
					dmf.failedHandle(taskId, merge_models.ExceptionCodeDataDeleted, fmt.Sprintf("已提取的设备唯一数据已删除.Msg: %v", m), "uniqueKey", md.UniqueKey, m, now, trackerSignalChan, enableTracker, lockKey, true)
					return
				}
				// 获取关联数据
				originalData, total, err := dmf.GetRelationData(ctx, msgData.UniqueKey, msgData.Node)
				if err != nil {
					dmf.failedHandle(taskId, merge_models.ExceptionCodeDataFetchFailed, fmt.Sprintf("获取设备关联数据失败.Msg: %v, err: %v", m, err), "uniqueKey", md.UniqueKey, m, now, trackerSignalChan, enableTracker, lockKey, false)
					return
				}
				// 当前数据添加到关联数据中
				originalData = append(originalData, msgData)
				originalInfo := utils.ListColumn(originalData, func(item *esmodel_device.ProcessDevice) string {
					return fmt.Sprintf("id:%s,source:%v,node:%v", item.Id, item.Source, item.Node)
				})
				mlog.Debugf("获取设备关联数据成功. Msg id: %s, total: %d, originalInfo: %s", m.ID, total, originalInfo)
				// 构建融合记录-关联数据
				md.OriginalData = originalData
				// 获取融合规则
				rules, err := merge_helper.GetMergeRules(strategy.BusinessType_DeviceMerge, mlog)
				if err != nil {
					dmf.failedHandle(taskId, merge_models.ExceptionCodeDataFetchFailed, fmt.Sprintf("获取设备融合规则失败. err: %v", err), "", "", m, now, trackerSignalChan, enableTracker, lockKey, false)
					return
				}
				mlog.Debug("获取设备融合规则成功. rules count: ", len(rules))
				// 构建融合记录-规则
				md.Rule = rules
				md.Record.Strategies = rules

				// 开启执行融合逻辑
				dmf.ExecuteMerge(ctx, md)

				// 记录任务处理量
				tracker.IncrementCount(redis_helper.DeviceMergeTaskKey(fmt.Sprintf("%v", taskId), "count"), 1)
				//if cfg.LoadQueue().LogMergeCost {
				// 计算任务处理耗时
				duration := time.Now().UnixMilli() - now.UnixMilli()
				mlog.Infof("设备融合消息处理完成. msg: %v,耗时: %vms", m, duration)
				// 记录耗时统计
				deviceTimeTracker.AddRecord(fmt.Sprintf("%d:%s", duration, m.ID))
				// 统计任务总耗时
				tracker.IncrementCount(redis_helper.DeviceMergeTotalTimeKey(fmt.Sprintf("%v", taskId)), int(duration))
				//}
				if enableTracker {
					// 给追踪器发信号，打印耗时信息
					trackerSignalChan <- struct{}{}
				}
			}(msg)
		}
	}(sem)
}

// failedHandle 处理失败的消息
func (dmf *DeviceMergeFlow) failedHandle(taskId string, errType string, errMsg string, identifier string, identifierValue string, m queue.QueueMsg, start time.Time, trackerSignalChan chan struct{}, enableTracker bool, dataUniqueKey string, isDiscard bool) {
	dmf.mlog.Warnf(errMsg)

	// 记录任务处理量
	if !isDiscard {
		tracker.IncrementCount(redis_helper.DeviceMergeTaskKey(taskId, "failed_count"), 1)
	} else {
		tracker.IncrementCount(redis_helper.DeviceMergeTaskKey(taskId, "discarded_count"), 1)
	}
	// 计算任务处理耗时
	duration := time.Now().UnixMilli() - start.UnixMilli()
	// 记录耗时统计
	deviceTimeTracker.AddRecord(fmt.Sprintf("%d:%s", duration, m.ID))
	// 统计任务总耗时
	tracker.IncrementCount(redis_helper.DeviceMergeTotalTimeKey(taskId), int(duration))
	if enableTracker {
		// 给追踪器发信号，打印耗时信息
		trackerSignalChan <- struct{}{}
	}
	// 确认消息已处理
	queue.NewQueue(queue.QueueType_Redis).Ack(dmf.qName, dmf.groupName, m.ID)

	// 删除当前写入数据的锁
	if dataUniqueKey != "" {
		redisKey := redis_helper.GetDeviceMergeLockKey(dataUniqueKey)
		distributedlock.Unlock(redisKey, taskId)
		dmf.mlog.Infof("处理失败，删除入库数据的锁. redisKey: %s, msgId: %s", redisKey, m.ID)
	}

	mergeRecordId, _ := getMergeRecordId(m)
	exception := &merge_models.MergeExceptions{
		MergeRecordId:  mergeRecordId,
		BatchId:        taskId,
		ExceptionCode:  errType,
		BusinessModule: merge_models.BusinessModuleDeviceMerge,
		Status: func() string {
			if isDiscard {
				return merge_models.StatusDiscarded
			}
			return merge_models.StatusFailed
		}(),
		ErrorMessage:    errMsg,
		Payload:         fmt.Sprintf("%+v", m),
		Identifier:      identifier,
		IdentifierValue: identifierValue,
	}
	merge_models.NewMergeExceptionsModel().Create(exception)
}

func GetProcessDataByMsg(msg queue.QueueMsg) (*esmodel_device.ProcessDevice, error) {
	var idStr string
	id, exist := msg.Values["id"]
	if !exist {
		return nil, fmt.Errorf("消息中未包含id字段")
	}
	switch v := id.(type) {
	case string:
		idStr = v
	case float64:
		idStr = fmt.Sprintf("%d", int(v))
	case int:
		idStr = fmt.Sprintf("%d", v)
	default:
		return nil, fmt.Errorf("id字段类型错误")
	}
	data, err := es.GetById[esmodel_device.ProcessDevice](idStr)
	if err != nil {
		return nil, err
	}
	return data, nil
}

func GetExistMergeDeviceByQuery(uniqueKey string) (*esmodel_device.Device, error) {
	redisClient := redis.GetRedisClient()
	ctx := context.Background()
	key := redis_helper.DeviceUniqueKeyToIDKey()
	id, err := redisClient.HGet(ctx, key, uniqueKey).Result()
	if err != nil && !errors.Is(err, redisV8.Nil) {
		return nil, err
	}
	if id != "" {
		d, err := es.GetById[esmodel_device.Device](id)
		if err != nil {
			return nil, err
		}
		return d, nil
	}
	return nil, nil
}

// GetRelationData 获取关联数据
func (dmf *DeviceMergeFlow) GetRelationData(ctx context.Context, uniqueKey string, node uint64) ([]*esmodel_device.ProcessDevice, int64, error) {
	redisClient := redis.GetRedisClient()
	key := redis_helper.ProcessDeviceUniqueKeyToIDKey()
	ids, err := redisClient.HGet(ctx, key, uniqueKey).Result()
	if err != nil {
		return nil, 0, err
	}
	if ids != "" {
		var idsList []string
		err = json.Unmarshal([]byte(ids), &idsList)
		if err != nil {
			return nil, 0, err
		}
		esClient := es.GetEsClient()
		mget := esClient.Mget()
		for _, id := range idsList {
			mget.Add(elastic.NewMultiGetItem().Id(id).Index(esmodel_device.NewProcessDeviceModel().IndexName()))
		}
		result, err := mget.Do(context.Background())
		if err != nil {
			return nil, 0, err
		}
		data := make([]*esmodel_device.ProcessDevice, 0)
		for _, item := range result.Docs {
			if item.Source != nil {
				var processDevice esmodel_device.ProcessDevice
				err = json.Unmarshal(item.Source, &processDevice)
				if err != nil {
					return nil, 0, err
				}
				if processDevice.Node != node {
					data = append(data, &processDevice)
				}
			}
		}
		return data, int64(len(data)), nil
	}
	return nil, 0, nil
}

// ExecuteMerge 执行融合过程
func (dmf *DeviceMergeFlow) ExecuteMerge(ctx context.Context, md *MergeDevice) {
	md.Record.Strategies = md.Rule

	if md.MsgData != nil {
		if md.MsgData.DeletedAt != nil {
			// 已有数据已被删除，不能再融合
			dmf.mlog.Warnf("已有设备数据已被删除. msg: %v", md.Msg)
			md.Record.Status = 2
			md.Record.Message = "已有设备数据已被删除"
			deviceMergeRecordChan <- md.Record
			return
		}
	}

	executor := &DeviceStrategyExecutor{
		SourceData: md.OriginalData,
		Strategy:   md.Rule,
	}
	strategyContext := &StrategyExecutorContext[*esmodel_device.Device]{
		Executor: executor,
	}
	// 根据融合策略，执行字段融合
	md.Device, md.FieldValInfo = strategyContext.Executor.Execute()
	deviceId := UUIDStr()
	md.Device.Id = deviceId
	md.Device.Fid = md.UniqueKey
	md.Device.FidHash = utils.Md5Hash(md.Device.Fid)
	// 补充权限信息
	// 1-获取运维人员
	personList := make([]string, 0)
	personList = append(personList, md.Device.Oper...)
	personList = utils.ListDistinctNonZero(personList)
	// 2-获取对应人员台账信息
	personData, err := esmodel_person.NewStaff().GetByNames(ctx, personList)
	if err != nil {
		dmf.mlog.Warnf("获取人员台账信息失败. err: %v", err)
	} else {
		for _, person := range personData {
			md.Device.PersonLimit = append(md.Device.PersonLimit, person.Fid)
			md.Device.PersonLimitHash = append(md.Device.PersonLimitHash, person.FidHash)
		}
	}

	if md.MsgData != nil {
		md.Device.Id = md.MsgData.Id
		// 保存历史用过的所有process_id,防止删除时遗漏
		md.Device.AllProcessIds = append(md.Device.AllProcessIds, md.MsgData.AllProcessIds...)
		md.Device.AllProcessIds = utils.ListDistinctNonZero(md.Device.AllProcessIds)
		md.Device.MergeCount = md.MsgData.MergeCount + 1
		md.Device.CreatedAt = md.MsgData.CreatedAt
		// 更新人工校准数据
		deviceCalibration := &model.DeviceCalibrationModel{}
		err := deviceCalibration.UpdateMergeResult(md.Device, md.MsgData)
		if err != nil {
			dmf.mlog.Warnf("更新人工校准数据失败. err: %v", err)
		}
	} else {
		md.Device.MergeCount = 1
		md.Device.CreatedAt = localtime.NewLocalTime(time.Now())
	}
	// 触发设备数据融合单条数据结束事件
	err = event.NewEventBus().Emit(event.Event_Device_MergeData_End, md.Device, md.MsgData)
	if err != nil {
		dmf.mlog.Warnf("触发设备数据融合单条数据结束事件失败. err: %v", err)
	}
	md.Device.UpdatedAt = localtime.NewLocalTime(time.Now())
	md.Record.DeviceId = md.Device.Id
	md.Record.SourceIds = md.Device.SourceIds
	md.Record.NodeIds = md.Device.NodeIds
	md.Record.TaskDataIds = md.Device.TaskDataIds
	md.Record.FieldValInfoList = md.FieldValInfo

	// 记录融合记录信息
	deviceMergeRecordList.Store(md.Device.Id, md.Record)
	// 因为结果表和结果记录表id不同，所以需要保存两份
	deviceMergeRecordList.Store(md.DeviceRecoreId, md.Record)

	deviceMergeResultChan <- md
}
func (dmf *DeviceMergeFlow) flushDeviceRecordBulk(records []*esmodel_device.DeviceRecord, currentBatchList []string) error {
	mlog := dmf.mlog
	deviceIndexName := esmodel_device.NewDeviceModel().IndexName()
	deviceRecordIndexName := esmodel_device.NewDeviceRecordModel().IndexName()

	err := event.NewEventBus().Emit(event.EvtDeviceMergeDataBeforeWrite, records)
	if err != nil {
		mlog.Warnf("设备批次写入前事件执行失败. err: %v", err)
	}
	bulkService := es.GetEsClient().Bulk()
	for _, record := range records {
		// 融合结果，设备索引
		bulkService.Add(elastic.NewBulkUpdateRequest().DocAsUpsert(true).Index(deviceIndexName).Id(record.Device.Id).Doc(record.Device))
		// 融合记录索引
		bulkService.Add(elastic.NewBulkCreateRequest().Index(deviceRecordIndexName).Id(record.Id).Doc(record))
	}
	err = deviceResultBulkServiceDo(bulkService, currentBatchList, mlog)
	if err != nil {
		mlog.Warnf("设备融合结果或记录写入失败. err: %v", err)
		return err
	} else {
		return nil
	}
}

// WriteResult 执行结果写入逻辑，包括写入融合结果和融合记录
func (dmf *DeviceMergeFlow) WriteResult() {
	mlog := dmf.mlog

	currentBatchList := make([]string, 0)
	var records []*esmodel_device.DeviceRecord
	ticker := time.NewTicker(3 * time.Second)
	defer ticker.Stop()
	for {
		select {
		case <-ticker.C:
			if len(records) > 0 {
				mlog.Infof("3秒未收到设备融合记录消息，开始写入剩余数据. count: %d", len(records))
				err := dmf.flushDeviceRecordBulk(records, currentBatchList)
				if err == nil {
					records = make([]*esmodel_device.DeviceRecord, 0)
					currentBatchList = make([]string, 0)
				}
			}
		case v := <-deviceMergeResultChan:
			// 写入设备融合结果记录
			deviceRecord := &esmodel_device.DeviceRecord{Device: v.Device, DeviceId: v.Device.Id}
			deviceRecord.Id = v.DeviceRecoreId

			// 唯一标识
			uniqueKey := fmt.Sprintf("%s:%s", v.Device.Fid, v.TaskId)
			records = append(records, deviceRecord)
			count := len(records)
			currentBatchList = append(currentBatchList, uniqueKey)

			if count >= 100 {
				err := dmf.flushDeviceRecordBulk(records, currentBatchList)
				if err == nil {
					records = make([]*esmodel_device.DeviceRecord, 0)
					currentBatchList = make([]string, 0)
				}
			}
		}
	}
}

// deviceResultBulkServiceDo 执行bulk写入
func deviceResultBulkServiceDo(bulkService *es.SafeBulkService, currentBatchList []string, mlog *common_logs.Logger) error {
	mlog.Debugf("开始写入设备融合结果. current batch count: %d", bulkService.NumberOfActions())
	resp, err := bulkService.Do(context.Background())
	if err != nil {
		mlog.Warnf("设备融合结果或记录写入失败. err: %v", err)
		return err
	} else {
		deviceResultWriteHandler(resp.Items, mlog)
		mlog.Debug("设备融合记录写入成功.")
		bulkService.Reset()

		for _, item := range currentBatchList {
			key, taskId := strings.Split(item, ":")[0], strings.Split(item, ":")[1]
			redisKey := redis_helper.GetDeviceMergeLockKey(key)
			distributedlock.Unlock(redisKey, taskId)
		}
	}
	return nil
}

// deviceResultWriteHandler 处理bulk写入结果
func deviceResultWriteHandler(items []map[string]*elastic.BulkResponseItem, mlog *common_logs.Logger) {
	deviceIndexName := esmodel_device.NewDeviceModel().IndexName()
	deviceRecordIndexName := esmodel_device.NewDeviceRecordModel().IndexName()
	for _, item := range items {
		for op, detail := range item {
			if op == "update" || op == "create" {
				// 结果表
				if detail.Index == deviceIndexName {
					if detail.Error != nil {
						mlog.Warnf("设备融合结果写入失败. op: %s, detail: %v", op, detail)
						record, ok := deviceMergeRecordList.Load(detail.Id)
						if ok {
							r := record.(*esmodel_device.MergeRecords)
							r.Status = 2
							r.Message = fmt.Sprintf("设备融合结果写入失败. op: %s, detail: %v", op, detail)
							deviceMergeRecordChan <- r
							deviceMergeRecordList.Delete(detail.Id)
						}
					} else {
						mlog.Debug(fmt.Sprintf("设备融合结果写入成功. op: %s, detail: %v", op, detail))
						record, ok := deviceMergeRecordList.Load(detail.Id)
						if ok {
							r := record.(*esmodel_device.MergeRecords)
							r.Status = 1
							r.Message = fmt.Sprintf("设备融合结果写入成功. op: %s, detail: %v", op, detail)
							deviceMergeRecordChan <- r
							deviceMergeRecordList.Delete(detail.Id)
						}
					}
				} else if detail.Index == deviceRecordIndexName {
					if detail.Error != nil {
						mlog.Warnf("设备融合记录写入失败. op: %s, detail: %v", op, detail)
						record, ok := deviceMergeRecordList.Load(detail.Id)
						if ok {
							r := record.(*esmodel_device.MergeRecords)
							r.Status = 2
							r.Message = fmt.Sprintf("设备融合记录写入失败. op: %s, detail: %v", op, detail)
							deviceMergeRecordChan <- r
							deviceMergeRecordList.Delete(detail.Id)
						}
					} else {
						mlog.Debug(fmt.Sprintf("设备融合记录写入成功. op: %s, detail: %v", op, detail))
						record, ok := deviceMergeRecordList.Load(detail.Id)
						if ok {
							r := record.(*esmodel_device.MergeRecords)
							r.Status = 1
							r.DeviceRecordId = detail.Id
							deviceMergeRecordChan <- r
							deviceMergeRecordList.Delete(detail.Id)
						}
					}
				}
			}
		}
	}
}
func (dmf *DeviceMergeFlow) flushDeviceMergeRecord(records []*esmodel_device.MergeRecords) error {
	mlog := dmf.mlog
	indexName := esmodel_device.NewMergeRecordsModel().IndexName()
	bulk := es.GetEsClient().Bulk()
	for _, record := range records {
		bulk.Add(elastic.NewBulkCreateRequest().Index(indexName).Id(record.Id).Doc(record))
	}
	_, err := bulk.Do(context.Background())
	if err != nil {
		mlog.Warnf("设备融合记录写入失败. err: %v", err)
		return err
	}
	return nil
}
func (dmf *DeviceMergeFlow) WriteRecord() {
	q := queue.NewQueue(queue.QueueType_Redis)
	mlog := dmf.mlog
	var listRecords []*esmodel_device.MergeRecords
	ticker := time.NewTicker(3 * time.Second)
	defer ticker.Stop()
	for {
		select {
		case <-ticker.C:
			if len(listRecords) > 0 {
				mlog.Infof("3秒未收到设备融合记录消息，开始写入剩余数据. count: %d", len(listRecords))
				err := dmf.flushDeviceMergeRecord(listRecords)
				if err == nil {
					listRecords = make([]*esmodel_device.MergeRecords, 0)
				}
			}
		case r := <-deviceMergeRecordChan:
			msgId, ok := deviceMergeRecordAndMsgId.Load(r.Id)
			if ok {
				q.Ack(cfg.LoadQueue().AssetMergeQueue, dmf.groupName, msgId.(string))
				deviceMergeRecordAndMsgId.Delete(r.Id)
			}
			listRecords = append(listRecords, r)
			count := len(listRecords)
			if count >= 200 {
				mlog.Infof("开始写入设备融合记录. count: %d", count)
				err := dmf.flushDeviceMergeRecord(listRecords)
				if err == nil {
					listRecords = make([]*esmodel_device.MergeRecords, 0)
				}
			}
		}
	}
}
