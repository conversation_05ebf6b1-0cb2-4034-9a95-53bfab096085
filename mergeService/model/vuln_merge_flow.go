// 漏洞融合流程

package model

import (
	"context"
	"encoding/json"
	"fmt"
	"fobrain/initialize/mysql"
	"fobrain/pkg/utils/common_logs"
	"io"
	"math/rand"
	"slices"
	"strings"
	"sync"
	"time"

	"github.com/go-errors/errors"

	"fobrain/fobrain/common/localtime"
	"fobrain/initialize/es"
	"fobrain/initialize/redis"
	"fobrain/mergeService/event"
	model "fobrain/mergeService/model/manual_calibration"
	logs "fobrain/mergeService/utils/log"
	es_model "fobrain/models/elastic"
	esmodel "fobrain/models/elastic/poc"
	merge_models "fobrain/models/mysql/merge"
	"fobrain/models/mysql/poc_settings"
	"fobrain/models/mysql/strategy"
	"fobrain/models/mysql/threat_histories"
	"fobrain/models/mysql/vulnerability"
	redis_helper "fobrain/models/redis"
	"fobrain/pkg/cfg"
	distributedlock "fobrain/pkg/distributedLock"
	"fobrain/pkg/queue"
	"fobrain/pkg/tracker"
	"fobrain/pkg/utils"

	"github.com/olivere/elastic/v7"
	"gorm.io/gorm"
)

var (
	vuln_lock = sync.RWMutex{}
	// 漏洞融合记录写入通道
	vulnMergeRecordChan = make(chan *esmodel.MergeRecords, 100)
	// 漏洞融合结果写入通道
	vulnMergeResultChan = make(chan *MergeVuln, 100)
	// 漏洞融合记录列表，为批次写入结果使用
	vulnMergeRecordList = &sync.Map{}
	// 漏洞融合记录 id 和 消息 id
	vulnMergeRecordAndMsgId = &sync.Map{}
	// 漏洞融合处理时间统计
	vulnTimeTracker = &tracker.ProcessingTimeTracker{}
)

type VulnMergeFlow struct {
	mlog      *common_logs.Logger
	groupName string
	qName     string
}

type MergeVuln struct {
	Record                     *esmodel.MergeRecords
	Poc                        *esmodel.Poc // 最终融合结果
	MsgData                    *esmodel.Poc // 根据消息获取的唯一数据
	OriginalData               []*esmodel.ProcessPoc
	Rule                       []*strategy.Strategy
	FieldValInfoList           []*es_model.FieldValInfo // 字段采信信息
	Msg                        queue.QueueMsg
	TaskId                     string
	StartTime                  time.Time
	IsPoc                      bool               // 是否是PoC漏洞,来自查询到的原始数据
	CVE, CNVD, CNNVD, URL, OID string             // 融合条件
	Condition                  *elastic.BoolQuery // 查询条件，查询过程表和结果表的条件一样
	PocRecordId                string             // 漏洞融合记录id
	LockKey                    string
}

// Merge 融合流程主方法
func (vmf *VulnMergeFlow) Merge() {
	mlog := vmf.mlog
	redisClient := redis.GetRedisClient()
	qName := cfg.LoadQueue().VulnMergeQueue
	vmf.qName = qName
	vmf.groupName = "vuln_merge_flow"
	// 最大并发数量
	var maxConcurrency = cfg.LoadQueue().VulnMergeConcurrent
	msgChan, consumerName, err := queue.NewQueue(queue.QueueType_Redis).Subscribe(qName, vmf.groupName, maxConcurrency)
	mlog.Info("订阅漏洞融合消息. qName: ", qName, " consumerName: ", consumerName)
	if err != nil {
		mlog.Error("订阅漏洞融合消息失败. qName: ", qName, " err: ", err)
		return
	}
	// defer queue.NewQueue(queue.QueueType_Redis).Unsubscribe(qName, consumerName)

	// 开启处理时间跟踪器
	vulnTimeTracker = tracker.NewProcessingTimeTracker(1*time.Minute, MergeFlowType_Vulnerability)
	trackerSignalChan := make(chan struct{})
	enableTracker := printDutation(vulnTimeTracker, trackerSignalChan, MergeFlowType_Vulnerability, mlog)

	// 开启融合结果写入协程
	go vmf.WriteResult()
	// 开启融合记录写入协程
	go vmf.WriteRecord()
	sem := make(chan struct{}, maxConcurrency)
	go func(sem chan struct{}) {
		for msg := range msgChan {
			sem <- struct{}{}
			go func(m queue.QueueMsg) {
				defer func() {
					// if r := recover(); r != nil {
					// 	wrapErr := errors.Wrap(r, 3)
					// 	mlog.Errorf("漏洞融合消息处理失败. msg: %v, err: %v, stack: %+v", m, wrapErr.Error(), wrapErr.Stack())

					// 	// 记录异常
					// 	mergeRecordId, _ := getMergeRecordId(m)
					// 	merge_models.NewMergeExceptionsModel().Create(&merge_models.MergeExceptions{
					// 		MergeRecordId:  mergeRecordId,
					// 		BatchId:        "",
					// 		ExceptionCode:  merge_models.ExceptionCodeProcessFailed,
					// 		BusinessModule: merge_models.BusinessModuleAssetMerge,
					// 		Status:         merge_models.StatusFailed,
					// 		ErrorMessage:   fmt.Sprintf("漏洞融合消息处理失败. msg: %v, err: %v, stack: %+v", m, wrapErr.Error(), wrapErr.Stack()),
					// 		Payload:        fmt.Sprintf("%+v", m),
					// 	})
					// }
					// 释放一个处理能力
					mlog.Debug("释放一个处理能力")
					<-sem
				}()
				now := time.Now()
				mlog.Infof("收到漏洞融合消息. msg: %v", m)
				// 构建融合记录-消息
				ma := &MergeVuln{
					Msg:         m,
					StartTime:   now,
					PocRecordId: UUIDStr(),
					Record: &esmodel.MergeRecords{
						Id:        UUIDStr(),
						CreatedAt: localtime.NewLocalTime(now),
						MergeMode: es_model.MergeMode_Auto,
					},
				}
				vulnMergeRecordAndMsgId.Store(ma.Record.Id, m.ID)

				// 根据消息组装获取关联数据的条件
				if len(m.Values) < 1 {
					vmf.failedHandle("", merge_models.ExceptionCodeInvalidMsg, fmt.Sprintf("漏洞融合消息中未包含搜索条件. msg: %v", m), "", "", m, now, trackerSignalChan, enableTracker, false, "")
					return
				}
				taskId, err := getTaskId(m)
				if err != nil {
					vmf.failedHandle(taskId, merge_models.ExceptionCodeInvalidMsg, fmt.Sprintf("获取任务ID失败. msg: %v, err: %v", m, err), "", "", m, now, trackerSignalChan, enableTracker, false, "")
					return
				}
				ma.TaskId = taskId
				isStart, err := startHandle(m, taskId, MergeFlowType_Vulnerability, mlog)
				if err != nil {
					vmf.failedHandle(taskId, merge_models.ExceptionCodeInvalidMsg, fmt.Sprintf("获取任务开始状态失败. msg: %v, err: %v", m, err), "", "", m, now, trackerSignalChan, enableTracker, false, "")
					return
				}
				if isStart {
					queue.NewQueue(queue.QueueType_Redis).Ack(qName, vmf.groupName, m.ID)
					return
				}
				isEnd := endHandle(m, taskId, MergeFlowType_Vulnerability, mlog)
				if isEnd {
					queue.NewQueue(queue.QueueType_Redis).Ack(qName, vmf.groupName, m.ID)
					return
				}

				ctx := context.Background()

				originalData := make([]*esmodel.ProcessPoc, 0)

				// 获取PoC消息数据
				processPoc, err := getProcessPocByMsg(m)
				if err != nil {
					vmf.failedHandle(taskId, merge_models.ExceptionCodeInvalidMsg, fmt.Sprintf("获取PoC消息数据失败. msg: %v, err: %v", m, err), "id", fmt.Sprintf("%v", m.Values["id"]), m, now, trackerSignalChan, enableTracker, false, "")
					return
				}

				// 设置触发源ID
				ma.Record.TiggerSourceId = processPoc.Source
				ma.CVE = processPoc.Cve
				ma.CNVD = processPoc.Cnvd
				ma.CNNVD = processPoc.Cnnvd
				ma.OID = processPoc.OriginalId
				ma.IsPoc = func() bool {
					// 1 表示是PoC漏洞, 否则不是PoC漏洞
					return processPoc.IsPoc == 1
				}()

				// 漏洞的 URL 为必须的信息
				if processPoc.Url == "" {
					vmf.failedHandle(taskId, merge_models.ExceptionCodeInvalidMsg, fmt.Sprintf("原始数据中URL为空. msg: %v", m), "processid", processPoc.Id, m, now, trackerSignalChan, enableTracker, false, "")
					return
				}
				ma.URL = processPoc.Url
				originalData = append(originalData, processPoc)

				retryCount := 0
				lockKey := ""
				// 计算url的hash值
				urlHash := utils.Get16MD5Encode(ma.URL)
				// 如果CVE、CNVD、CNNVD为空，则尝试根据原始数据信息获取漏洞数据
				if ma.IsPoc || (ma.CVE == "" && ma.CNVD == "" && ma.CNNVD == "") {
					poc, err := vmf.getPocByOriginalInfo(processPoc.OriginalId, processPoc.Source, processPoc.Url)
					if err != nil {
						vmf.failedHandle(taskId, merge_models.ExceptionCodeDataFetchFailed, fmt.Sprintf("获取PoC消息数据失败. msg: %v, err: %v", m, err), "originalid-source-url", fmt.Sprintf("%s-%d-%s", processPoc.OriginalId, processPoc.Source, processPoc.Url), m, now, trackerSignalChan, enableTracker, false, "")
						return
					}
					ma.MsgData = poc
					lockKey = fmt.Sprintf("%s-%s", ma.OID, urlHash)
				} else {
					// 查询 redis 中是否存在已处理未入库的数据
					lockKey = fmt.Sprintf("%s-%s-%s-%s", ma.CVE, ma.CNVD, ma.CNNVD, urlHash)
				}
				ma.LockKey = lockKey
			retry:
				ok := distributedlock.Lock(redis_helper.GetVulnMergeLockKey(lockKey), taskId, 120)
				if !ok {
					// 尝试获取锁内容，判断如果是同一个taskId，则不跳过处理
					lockContent, err := redisClient.Get(context.Background(), lockKey).Result()
					if err == nil && lockContent == taskId {
						// 如果是同一个taskId，则丢弃这条数据
						vmf.failedHandle(taskId, merge_models.ExceptionCodeLockAcquireFailed, fmt.Sprintf("同一批数据已存在相同漏洞, 丢弃本数据. taskId: %s, Msg: %v", taskId, m), "lockKey", lockKey, m, now, trackerSignalChan, enableTracker, true, "")
						return
					}

					// 生成 3000 到 5000 之间的随机毫秒数
					randomMilliseconds := rand.Intn(2001) + 3000
					vmf.mlog.Infof("获取漏洞关联数据(未入库)冲突, 等待%d毫秒后继续处理.lockKey: %s, taskId: %s, Msg: %v", randomMilliseconds, lockKey, taskId, m)
					// 将毫秒转换为 Duration
					sleepDuration := time.Duration(randomMilliseconds) * time.Millisecond
					// 休眠随机时间
					time.Sleep(sleepDuration)
					retryCount++
					if retryCount > 5 {
						vmf.failedHandle(taskId, merge_models.ExceptionCodeLockAcquireFailed, fmt.Sprintf("获取漏洞关联数据(未入库)冲突, 超过最大重试次数. Msg: %v", m), "lockKey", lockKey, m, now, trackerSignalChan, enableTracker, false, "")
						return
					} else {
						goto retry
					}
				}
				// 构建查询条件
				processPocQuery, pocQuery, err := getCondition(ma)
				if err != nil {
					vmf.failedHandle(taskId, merge_models.ExceptionCodeDataFetchFailed, fmt.Sprintf("获取漏洞关联数据失败. msg: %v, err: %v", m, err), "originalid-cve-cnvd-cnnvd-url", fmt.Sprintf("%s-%s-%s-%s-%s", ma.OID, ma.CVE, ma.CNVD, ma.CNNVD, ma.URL), m, now, trackerSignalChan, enableTracker, false, lockKey)
					return
				}

				// 尝试获取融合结果，如果获取成功，则使用人工校准数据
				poc, resultCount, err := vmf.getPocByMsg(pocQuery)
				if err != nil {
					vmf.failedHandle(taskId, merge_models.ExceptionCodeDataFetchFailed, fmt.Sprintf("获取融合结果失败. msg: %v, err: %v", m, err), "originalid-cve-cnvd-cnnvd-url", fmt.Sprintf("%s-%s-%s-%s-%s", ma.OID, ma.CVE, ma.CNVD, ma.CNNVD, ma.URL), m, now, trackerSignalChan, enableTracker, false, lockKey)
					return
				}
				if resultCount > 1 {
					vmf.failedHandle(taskId, merge_models.ExceptionCodeDataFetchFailed, fmt.Sprintf("获取漏洞融合结果不唯一. msg: %v, err: %v", m, err), "originalid-cve-cnvd-cnnvd-url", fmt.Sprintf("%s-%s-%s-%s-%s", ma.OID, ma.CVE, ma.CNVD, ma.CNNVD, ma.URL), m, now, trackerSignalChan, enableTracker, false, lockKey)
					return
				} else {
					ma.MsgData = poc
				}
				// 非PoC漏洞，获取关联数据
				if !ma.IsPoc {
					// 获取关联数据
					relatedData, total, err := vmf.GetRelationData(ctx, processPocQuery)
					if err != nil {
						vmf.failedHandle(taskId, merge_models.ExceptionCodeDataFetchFailed, fmt.Sprintf("获取漏洞关联数据失败. msg: %v, err: %v", m, err), "originalid-cve-cnvd-cnnvd-url", fmt.Sprintf("%s-%s-%s-%s-%s", ma.OID, ma.CVE, ma.CNVD, ma.CNNVD, ma.URL), m, now, trackerSignalChan, enableTracker, false, lockKey)
						return
					}
					if len(originalData) < 1 {
						vmf.failedHandle(taskId, merge_models.ExceptionCodeDataFetchFailed, fmt.Sprintf("获取漏洞关联数据为空. msg: %v", m), "originalid-cve-cnvd-cnnvd-url", fmt.Sprintf("%s-%s-%s-%s-%s", ma.OID, ma.CVE, ma.CNVD, ma.CNNVD, ma.URL), m, now, trackerSignalChan, enableTracker, false, lockKey)
						return
					}
					originalData = append(originalData, relatedData...)
					originalInfo := utils.ListColumn(originalData, func(item *esmodel.ProcessPoc) string {
						return fmt.Sprintf("id:%s,source:%v,node:%v", item.Id, item.Source, item.Node)
					})
					mlog.Debugf("获取漏洞关联数据成功. Msg id: %s, total: %d, originaleInfo: %s", m.ID, total, originalInfo)
				}
				// 构建融合记录-关联数据
				ma.OriginalData = originalData
				ma.Record.Cve = ma.CVE
				ma.Record.Cnvd = ma.CNVD
				ma.Record.Cnnvd = ma.CNNVD
				if ma.IsPoc && len(originalData) != 1 {
					vmf.failedHandle(taskId, merge_models.ExceptionCodeDataFetchFailed, fmt.Sprintf("PoC漏洞获取关联数据不唯一.Msg: %v,", m), "originalid-cve-cnvd-cnnvd-url", fmt.Sprintf("%s-%s-%s-%s-%s", ma.OID, ma.CVE, ma.CNVD, ma.CNNVD, ma.URL), m, now, trackerSignalChan, enableTracker, false, lockKey)
					return
				}

				// 获取融合规则
				rules, err := vmf.GetMergeRules()
				if err != nil {
					vmf.failedHandle(taskId, merge_models.ExceptionCodeDataFetchFailed, fmt.Sprintf("获取漏洞融合规则失败. msg: %v, err: %v", m, err), "", "", m, now, trackerSignalChan, enableTracker, false, lockKey)
					return
				}
				mlog.Debug("获取漏洞融合规则成功. rules count: ", len(rules))
				// 构建融合记录-规则
				ma.Rule = rules
				ma.Record.Strategies = rules

				// 开启执行融合逻辑
				vmf.ExecuteMerge(ctx, ma)

				// 记录任务处理量
				tracker.IncrementCount(redis_helper.VulnMergeTaskKey(fmt.Sprintf("%v", taskId), "count"), 1)
				duration := time.Now().UnixMilli() - now.UnixMilli()
				mlog.Infof("漏洞融合消息处理完成. msg: %v,耗时: %vms", m, duration)
				// 记录耗时统计
				vulnTimeTracker.AddRecord(fmt.Sprintf("%d:%s", duration, m.ID))
				// 统计任务总耗时
				tracker.IncrementCount(redis_helper.VulnMergeTotalTimeKey(fmt.Sprintf("%v", taskId)), int(duration))
				if enableTracker {
					// 给追踪器发信号，打印耗时信息
					trackerSignalChan <- struct{}{}
				}
			}(msg)
		}
	}(sem)
}

func (vmf *VulnMergeFlow) failedHandle(taskId string, errType, errMsg string, identifier string, identifierValue string, m queue.QueueMsg, start time.Time, trackerSignalChan chan struct{}, enableTracker bool, isDistinct bool, lockKey string) {
	vmf.mlog.Warnf(errMsg)

	if isDistinct {
		// 记录任务处理量
		tracker.IncrementCount(redis_helper.VulnMergeTaskKey(taskId, "distinct_count"), 1)
	} else {
		// 记录任务处理量
		tracker.IncrementCount(redis_helper.VulnMergeTaskKey(taskId, "failed_count"), 1)
	}

	// 计算任务处理耗时
	duration := time.Now().UnixMilli() - start.UnixMilli()
	// 记录耗时统计
	vulnTimeTracker.AddRecord(fmt.Sprintf("%d:%s", duration, m.ID))
	// 统计任务总耗时
	tracker.IncrementCount(redis_helper.VulnMergeTotalTimeKey(taskId), int(duration))
	if enableTracker {
		// 给追踪器发信号，打印耗时信息
		trackerSignalChan <- struct{}{}
	}
	// 确认消息已处理
	queue.NewQueue(queue.QueueType_Redis).Ack(vmf.qName, vmf.groupName, m.ID)

	if lockKey != "" {
		// 删除入库数据的锁
		distributedlock.Unlock(redis_helper.GetVulnMergeLockKey(lockKey), taskId)
		vmf.mlog.Infof("处理失败，删除入库数据的锁. redisKey: %s, msgId: %s", lockKey, taskId)
	}

	mergeRecordId, _ := getMergeRecordId(m)
	exception := &merge_models.MergeExceptions{
		MergeRecordId:  mergeRecordId,
		BatchId:        taskId,
		ExceptionCode:  errType,
		BusinessModule: merge_models.BusinessModuleVulnMerge,
		Status: func() string {
			if isDistinct {
				return merge_models.StatusDiscarded
			}
			return merge_models.StatusFailed
		}(),
		ErrorMessage:    errMsg,
		Payload:         fmt.Sprintf("%+v", m),
		Identifier:      identifier,
		IdentifierValue: identifierValue,
	}
	// 写入异常
	merge_models.NewMergeExceptionsModel().Create(exception)
}

// getCondition 从漏洞知识库补充漏洞id信息，然后生成查询条件
func getCondition(ma *MergeVuln) (processPocQuery, pocQuery *elastic.BoolQuery, err error) {
	// 如果 CVE、CNVD、CNNVD 为空，则根据source、original_id、url字段查询
	if ma.CVE == "" && ma.CNVD == "" && ma.CNNVD == "" {
		processPocQuery = elastic.NewBoolQuery().Must(elastic.NewTermQuery("source", ma.Record.TiggerSourceId), elastic.NewTermQuery("original_id", ma.OID), elastic.NewTermQuery("url", ma.URL))
		pocQuery = elastic.NewBoolQuery().Must(elastic.NewTermsQuery("source_ids", ma.Record.TiggerSourceId), elastic.NewTermsQueryFromStrings("original_ids.keyword", ma.OID), elastic.NewTermQuery("url", ma.URL))
		return processPocQuery, pocQuery, nil
	} else {
		// 从漏洞知识库获取对应数据
		dbData, err := vulnerability.NewVulnerabilityIntegration().GetByAnyId(ma.CVE, ma.CNVD, ma.CNNVD)
		if err != nil && err != gorm.ErrRecordNotFound {
			return nil, nil, err
		}
		// 补充三个 ID
		if dbData != nil {
			if ma.CVE == "" {
				ma.CVE = dbData.CVEID
			}
			if ma.CNVD == "" {
				ma.CNVD = dbData.CNVDID
			}
			if ma.CNNVD == "" {
				ma.CNNVD = dbData.CNNVDID
			}
		}

		fidList := make([]string, 0)
		urlHash := utils.Get16MD5Encode(ma.URL)
		hasId := false
		if ma.CVE != "" {
			hasId = true
			if ma.CNVD != "" {
				if ma.CNNVD != "" {
					fid := fmt.Sprintf("%s:%s:%s:%s", ma.CVE, ma.CNVD, ma.CNNVD, urlHash)
					fidList = append(fidList, fid)
				} else {
					fid := fmt.Sprintf("%s:%s:%s:%s", ma.CVE, ma.CNVD, "0", urlHash)
					fidList = append(fidList, fid)
				}
			} else {
				if ma.CNNVD != "" {
					fid := fmt.Sprintf("%s:%s:%s:%s", ma.CVE, "0", ma.CNNVD, urlHash)
					fidList = append(fidList, fid)
				} else {
					fid := fmt.Sprintf("%s:%s:%s:%s", ma.CVE, "0", "0", urlHash)
					fidList = append(fidList, fid)
				}
			}
		}
		if ma.CNVD != "" {
			hasId = true
			if ma.CNNVD != "" {
				fid := fmt.Sprintf("%s:%s:%s:%s", "0", ma.CNVD, ma.CNNVD, urlHash)
				fidList = append(fidList, fid)
			} else {
				fid := fmt.Sprintf("%s:%s:%s:%s", "0", ma.CNVD, "0", urlHash)
				fidList = append(fidList, fid)
			}
		}
		if ma.CNNVD != "" {
			hasId = true
			fid := fmt.Sprintf("%s:%s:%s:%s", "0", "0", ma.CNNVD, urlHash)
			fidList = append(fidList, fid)
		}
		if !hasId {
			fid := fmt.Sprintf("%s:%s", ma.OID, urlHash)
			fidList = append(fidList, fid)
		}
		query := elastic.NewBoolQuery()
		query.Should(elastic.NewTermsQueryFromStrings("fid", fidList...)).MinimumNumberShouldMatch(1)
		query.Must(elastic.NewTermQuery("url", ma.URL))
		processPocQuery = query
		pocQuery = query
		return processPocQuery, pocQuery, nil
	}
}

// GetMergeRules 获取融合规则
func (vmf *VulnMergeFlow) GetMergeRules() ([]*strategy.Strategy, error) {
	mlog := vmf.mlog
	key := redis_helper.VulnMergeRuleKey()
	redisClient := redis.GetRedisClient()
	// 从redis缓存获取规则
	ruleByte, err := redisClient.Get(context.Background(), key).Bytes()
	if err != nil {
		mlog.Warn("从redis缓存获取漏洞融合规则失败. err: ", err)
	}
	strategies := make([]*strategy.Strategy, 0)
	if len(ruleByte) > 0 {
		err = json.Unmarshal(ruleByte, &strategies)
		if err != nil {
			mlog.Warn("解析redis缓存中的漏洞融合规则失败. err: ", err)
		}
	}
	if len(strategies) == 0 {
		vuln_lock.Lock()
		defer vuln_lock.Unlock()
		sModel := strategy.NewStrategyModel()
		res, err := sModel.ListAllDistinct(strategy.BusinessType_VulnMerge)
		if err != nil {
			mlog.Error("获取漏洞融合规则失败. err: ", err)
		} else {
			strategies = res
			// 写入缓存
			ruleByte, _ := json.Marshal(strategies)
			err = redisClient.Set(context.Background(), key, ruleByte, time.Minute*time.Duration(mergeRuleCacheTime)).Err()
			if err != nil {
				mlog.Warn("写入漏洞融合规则缓存失败. err: ", err)
			}
		}
	}

	return strategies, nil
}

// getProcessPocByMsg 获取消息对应的唯一数据
func getProcessPocByMsg(msg queue.QueueMsg) (*esmodel.ProcessPoc, error) {
	var idStr string
	id, exist := msg.Values["id"]
	if !exist {
		return nil, fmt.Errorf("消息中未包含id字段")
	}
	switch v := id.(type) {
	case string:
		idStr = v
	case float64:
		idStr = fmt.Sprintf("%d", int(v))
	case int:
		idStr = fmt.Sprintf("%d", v)
	default:
		return nil, fmt.Errorf("id字段类型错误")
	}
	data, err := es.GetById[esmodel.ProcessPoc](idStr)
	if err != nil {
		return nil, err
	}
	return data, nil
}

// getPocByMsg 获取消息对应的唯一数据
// 如果存在错误，则其他返回值无意义，以错误为准
// 如果count>1，则返回count，poc无意义
func (vmf *VulnMergeFlow) getPocByMsg(condition *elastic.BoolQuery) (*esmodel.Poc, int64, error) {
	count, err := es.GetCount(esmodel.NewPoc().IndexName(), condition)
	if err != nil {
		return nil, 0, err
	}
	if count > 1 {
		return nil, count, nil
	}
	if count == 0 {
		return nil, 0, nil
	}
	data, err := es.First[esmodel.Poc](condition, nil)
	if err != nil {
		return nil, count, err
	}
	return data, count, nil
}

// getPocByOriginalInfo 根据原始数据信息获取漏洞数据
func (vmf *VulnMergeFlow) getPocByOriginalInfo(originalId string, sourceId uint64, url string) (*esmodel.Poc, error) {
	query := elastic.NewBoolQuery().Must(elastic.NewTermQuery("original_ids.keyword", originalId), elastic.NewTermQuery("source_ids", sourceId), elastic.NewTermQuery("url", url))
	data, err := es.First[esmodel.Poc](query, nil)
	if err != nil {
		return nil, err
	}
	return data, nil
}

// GetMsgData 获取消息对应的唯一数据
func (vmf *VulnMergeFlow) GetMsgData(ctx context.Context, msg queue.QueueMsg) (*esmodel.Poc, error) {
	return nil, errors.New("未实现,请使用 getPocByMsg")
}

// GetRelationData 获取相关的原始记录
func (vmf *VulnMergeFlow) GetRelationData(ctx context.Context, condition *elastic.BoolQuery) ([]*esmodel.ProcessPoc, int64, error) {
	_, data, err := es.List[esmodel.ProcessPoc](1, 1000, condition, nil)
	// 如果查询出现异常，则终止所有查询
	if err != nil {
		return nil, 0, err
	}

	return data, int64(len(data)), nil
}

// ExecuteMerge 执行融合逻辑
func (vmf *VulnMergeFlow) ExecuteMerge(ctx context.Context, mv *MergeVuln) {
	if mv.IsPoc || (mv.CVE == "" && mv.CNVD == "" && mv.CNNVD == "") {
		mv.Poc = mv.OriginalData[0].ConvertToPoc()
		// 触发漏洞单条数据转换
		event.NewEventBus().Emit(event.Event_Vuln_ConvertData, mv.Poc, mv.OriginalData[0])
		mv.Poc.Fid = generateFid(mv)
		mv.Poc.FidHash = utils.Md5Hash(mv.Poc.Fid)
		// 如果存在消息数据，则更新漏洞数据
		if mv.MsgData != nil {
			// 如果消息数据被删除，则不更新漏洞数据
			if mv.MsgData.DeletedAt != nil && slices.Contains(mv.MsgData.SourceIds, mv.OriginalData[0].Source) {
				vmf.failedHandle(mv.TaskId, merge_models.ExceptionCodeDataDeleted, fmt.Sprintf("漏洞数据被删除. msg: %v", mv.Msg), "pocid", mv.MsgData.Id, mv.Msg, mv.StartTime, nil, false, true, mv.LockKey)
				return
			}

			// 更新人工校准数据
			vulnCalibration := &model.VulnCalibrationModel{}
			vulnCalibration.UpdateMergeResult(mv.Poc, mv.MsgData)
			// 保留漏洞超期时间设置，该数据来自闭环流转，融合不修改该数据
			mv.Poc.LimitDate = mv.MsgData.LimitDate

			mv.Poc.Id = mv.MsgData.Id
			// 保存历史用过的所有process_id,防止删除时遗漏
			mv.Poc.AllProcessIds = append(mv.Poc.AllProcessIds, mv.MsgData.AllProcessIds...)
			mv.Poc.AllProcessIds = utils.ListDistinctNonZero(mv.Poc.AllProcessIds)
			mv.Poc.CreatedAt = mv.MsgData.CreatedAt

			// 更新历史上报的原信息
			mv.Poc.AllSourceIds = append(mv.Poc.AllSourceIds, mv.MsgData.AllSourceIds...)
			mv.Poc.AllNodeIds = append(mv.Poc.AllNodeIds, mv.MsgData.AllNodeIds...)
			mv.Poc.AllTaskDataIds = append(mv.Poc.AllTaskDataIds, mv.MsgData.AllTaskDataIds...)
			mv.Poc.AllProcessIds = append(mv.Poc.AllProcessIds, mv.MsgData.AllProcessIds...)
			mv.Poc.AllSourceIds = utils.ListDistinctNonZero(mv.Poc.AllSourceIds)
			mv.Poc.AllNodeIds = utils.ListDistinctNonZero(mv.Poc.AllNodeIds)
			mv.Poc.AllTaskDataIds = utils.ListDistinctNonZero(mv.Poc.AllTaskDataIds)
			mv.Poc.AllProcessIds = utils.ListDistinctNonZero(mv.Poc.AllProcessIds)

			// 补充当前处理人员信息
			mv.Poc.Person = mv.MsgData.Person
			mv.Poc.PersonInfo = mv.MsgData.PersonInfo
			mv.Poc.PersonDepartment = mv.MsgData.PersonDepartment
			mv.Poc.PersonLimit = mv.MsgData.PersonLimit
			mv.Poc.PersonLimitHash = mv.MsgData.PersonLimitHash

			// status字段特殊处理
			updateVulnStatus(mv)
		} else {
			mv.Poc.Id = UUIDStr()
			mv.Poc.CreatedAt = localtime.NewLocalTime(time.Now())
		}
		mv.Poc.IsPoc = func() int {
			if mv.IsPoc {
				return 1
			}
			return 2
		}()
		// poc 不融合，所以merge_count为1
		mv.Poc.MergeCount = 1
		mv.Poc.UpdatedAt = localtime.NewLocalTime(time.Now())
		mv.Record.SourceIds = mv.Poc.SourceIds
		mv.Record.NodeIds = mv.Poc.NodeIds
		mv.Record.PocTaskIds = mv.Poc.TaskDataIds
		mv.Record.ProcessIds = mv.Poc.ProcessIds
		mv.Record.PocId = mv.Poc.Id
		// 记录融合记录信息
		vulnMergeRecordList.Store(mv.Poc.Id, mv.Record)
		// 因为结果表和结果记录表id不同，所以需要保存两份
		vulnMergeRecordList.Store(mv.PocRecordId, mv.Record)
		vulnMergeResultChan <- mv
	} else {
		executor := &VulnStrategyExecutor{
			SourceData: mv.OriginalData,
			Strategy:   mv.Rule,
		}
		strategyContext := &StrategyExecutorContext[*esmodel.Poc]{
			Executor: executor,
		}
		// 根据融合策略，执行字段融合
		mv.Poc, mv.FieldValInfoList = strategyContext.Executor.Execute()
		pocId := UUIDStr()
		mv.Poc.Id = pocId
		mv.Poc.IsPoc = func() int {
			if mv.IsPoc {
				return 1
			}
			return 2
		}()
		// 漏洞不融合历史记录，所以merge_count为1
		mv.Poc.MergeCount = 1
		mv.Poc.Area = mv.OriginalData[0].Area
		mv.Poc.Url = mv.URL
		mv.Poc.Cve = mv.CVE
		mv.Poc.Cnvd = mv.CNVD
		mv.Poc.Cnnvd = mv.CNNVD
		mv.Poc.Fid = generateFid(mv)
		mv.Poc.FidHash = utils.Md5Hash(mv.Poc.Fid)
		mv.Poc.CreatedAt = localtime.NewLocalTime(time.Now())
		mv.Poc.UpdatedAt = localtime.NewLocalTime(time.Now())
		if mv.MsgData != nil {
			// 如果消息数据被删除，则不更新漏洞数据
			if mv.MsgData.DeletedAt != nil && slices.Contains(mv.MsgData.SourceIds, mv.OriginalData[0].Source) {
				vmf.failedHandle(mv.TaskId, merge_models.ExceptionCodeDataDeleted, fmt.Sprintf("漏洞数据被删除. msg: %v", mv.Msg), "pocid", mv.MsgData.Id, mv.Msg, mv.StartTime, nil, false, true, mv.LockKey)
				return
			}
			// 更新人工校准数据
			vulnCalibration := &model.VulnCalibrationModel{}
			vulnCalibration.UpdateMergeResult(mv.Poc, mv.MsgData)
			// 保留漏洞超期时间设置，该数据来自闭环流转，融合不修改该数据
			mv.Poc.LimitDate = mv.MsgData.LimitDate

			// 使用旧数据的id，实现更新
			mv.Poc.Id = mv.MsgData.Id
			// 保存历史用过的所有process_id,防止删除时遗漏
			mv.Poc.AllProcessIds = append(mv.Poc.AllProcessIds, mv.MsgData.AllProcessIds...)
			mv.Poc.AllProcessIds = utils.ListDistinctNonZero(mv.Poc.AllProcessIds)
			mv.Poc.CreatedAt = mv.MsgData.CreatedAt

			// status字段特殊处理
			updateVulnStatus(mv)

			// 补充当前处理人员信息
			mv.Poc.Person = mv.MsgData.Person
			mv.Poc.PersonInfo = mv.MsgData.PersonInfo
			mv.Poc.PersonDepartment = mv.MsgData.PersonDepartment
			mv.Poc.PersonLimit = mv.MsgData.PersonLimit
			mv.Poc.PersonLimitHash = mv.MsgData.PersonLimitHash
		}

		mv.Record.SourceIds = mv.Poc.SourceIds
		mv.Record.NodeIds = mv.Poc.NodeIds
		mv.Record.PocTaskIds = mv.Poc.TaskDataIds
		mv.Record.ProcessIds = mv.Poc.ProcessIds
		mv.Record.PocId = mv.Poc.Id
		mv.Record.FieldValInfoList = mv.FieldValInfoList
		// 记录融合记录信息
		vulnMergeRecordList.Store(mv.Poc.Id, mv.Record)
		// 因为结果表和结果记录表id不同，所以需要保存两份
		vulnMergeRecordList.Store(mv.PocRecordId, mv.Record)
		vulnMergeResultChan <- mv
	}
}

// 获取原始数据中创建时间最新的数据
func (mv *MergeVuln) GetOriginalLatestCreatedAt() *localtime.Time {
	defaultTime := localtime.NewLocalTime(time.Date(2025, 1, 1, 0, 0, 0, 0, time.Local))
	if len(mv.OriginalData) == 0 {
		return defaultTime
	}

	createdAt := mv.OriginalData[0].CreatedAt
	for _, poc := range mv.OriginalData[1:] {
		if poc.CreatedAt.After(*createdAt) {
			createdAt = poc.CreatedAt
		}
	}

	// 如果最新时间晚于默认时间，返回默认时间
	if createdAt.After(*defaultTime) {
		return defaultTime
	}
	return createdAt
}

func (mv *MergeVuln) GetStatusChangeTime() *localtime.Time {
	// 如果StatusChangeTime为空或者早于2025年，就去NewThreatHistoryModel取最新的状态变更时间
	twit := localtime.NewLocalTime(time.Date(2025, 1, 1, 0, 0, 0, 0, time.Local))
	if mv.MsgData.StatusChangeTime == nil || twit.After(*mv.MsgData.StatusChangeTime) {
		opts := []mysql.HandleFunc{
			mysql.WithWhere("poc_id", mv.MsgData.Id),
			mysql.WithWhere("category", "operation"),
			mysql.WithOrder("id DESC"),
		}
		th := threat_histories.NewThreatHistoryModel()
		list, _, err := th.List(0, 1, opts...)
		if err != nil {
			return nil
		}
		// 闭环变化
		t := th.ShowFields(list, map[string]any{})
		if len(t) > 0 {
			ct := t[0].CreatedAt
			return &ct
		}
	}
	return mv.MsgData.StatusChangeTime
}

// updateVulnStatus 更新漏洞状态
func updateVulnStatus(ma *MergeVuln) {
	// 判断一下状态变更记录的时间和原始数据最新的时间，如果状态变更记录的时间更晚，则不更新漏洞状态
	statusChangeTime := ma.GetStatusChangeTime()
	originalLatestCreatedAt := ma.GetOriginalLatestCreatedAt()
	if statusChangeTime != nil {
		ma.Poc.StatusChangeTime = statusChangeTime
	}
	if statusChangeTime != nil && statusChangeTime.After(*originalLatestCreatedAt) {
		ma.Poc.Status = ma.MsgData.Status
		return
	}

	// 其中复测通过的状态再次上报数据变为复现
	if ma.MsgData.Status == esmodel.PocStatusOfRepaired && ma.Poc.Status != esmodel.PocStatusOfRepaired {
		ma.Poc.Status = esmodel.PocStatusOfStillExist
		// 更新漏洞状态历史
		th := threat_histories.NewThreatHistoryModel()
		th.CreateThreatHistory(ma.Poc.Id, fmt.Sprintf("复测通过(%v)的漏洞数据再次上报，状态变更为复现(%v)", ma.MsgData.Status, ma.Poc.Status), ma.MsgData.Status, ma.Poc.Status, ma.Poc.SourceIds)
	} else {
		ma.Poc.Status = ma.MsgData.Status
	}
	// // 复测中+非PoC的漏洞可以按照最新的状态变更
	// if ma.MsgData.IsPoc == 1 && ma.MsgData.Status == esmodel.PocStatusOfReRepairing {
	// 	// 更新漏洞状态历史
	// 	th := threat_histories.NewThreatHistoryModel()
	// 	th.CreateThreatHistory(ma.Poc.Id, fmt.Sprintf("非PoC漏洞且状态为复测中(%v)，因为融合，状态更新为%v", ma.MsgData.Status, ma.Poc.Status), ma.MsgData.Status, ma.Poc.Status, ma.Poc.SourceIds)
	// } else {
	// 	// 其他状态下，全部以闭环处理的状态优先级最高
	// 	ma.Poc.Status = ma.MsgData.Status
	// 	// 状态不变，不更新漏洞状态历史
	// }
}

// generateFid 生成fid
func generateFid(ma *MergeVuln) string {
	hasId := false
	cve := "0"
	cnvd := "0"
	cnnvd := "0"
	urlHash := utils.Get16MD5Encode(ma.URL)
	if ma.CVE != "" {
		hasId = true
		cve = ma.CVE
	}
	if ma.CNVD != "" {
		hasId = true
		cnvd = ma.CNVD
	}
	if ma.CNNVD != "" {
		hasId = true
		cnnvd = ma.CNNVD
	}
	if hasId {
		return fmt.Sprintf("%s:%s:%s:%s", cve, cnvd, cnnvd, urlHash)
	}
	return fmt.Sprintf("%s:%s", ma.OriginalData[0].OriginalId, urlHash)
}

// 记录融合的漏洞id列表，融合完成后，进行批量风险等级计算
var pocIds = sync.Map{}

func (vmf *VulnMergeFlow) FlushMergeResult(records []*esmodel.PocRecord, currentBatchPocList []string) error {
	pocIndexName := esmodel.NewPoc().IndexName()
	pocRecordIndexName := esmodel.NewPocRecord().IndexName()
	bulkService := es.GetEsClient().Bulk()
	err := event.NewEventBus().Emit(event.EvtVulnMergeDataBeforeWrite, records)
	if err != nil {
		vmf.mlog.Warnf("漏洞批次写入前事件执行失败. err: %v", err)
	}

	for _, record := range records {
		bulkService.Add(elastic.NewBulkCreateRequest().Index(pocRecordIndexName).Id(record.Id).Doc(record))
		bulkService.Add(elastic.NewBulkUpdateRequest().DocAsUpsert(true).Index(pocIndexName).Id(record.Poc.Id).Doc(record.Poc))
		pocIds.Store(record.Poc.Id, record.Poc.Id)
	}
	err = vulnResultBulkServiceDo(bulkService, currentBatchPocList, vmf.mlog)
	if err != nil {
		vmf.mlog.Warnf("漏洞融合结果或记录写入失败. err: %v", err)
		return err
	}
	return nil
}

// WriteResult 写入融合结果，包括融合结果记录
func (vmf *VulnMergeFlow) WriteResult() {
	mlog := vmf.mlog
	currentBatchPocList := make([]string, 0)

	var records = make([]*esmodel.PocRecord, 0)
	ticker := time.NewTicker(3 * time.Second)
	defer ticker.Stop()
	for {
		select {
		case <-ticker.C:
			if len(records) > 0 {
				mlog.Infof("3秒未收到漏洞融合结果消息，开始写入剩余数据. count: %d", len(records))
				err := vmf.FlushMergeResult(records, currentBatchPocList)
				if err == nil {
					records = make([]*esmodel.PocRecord, 0)
					currentBatchPocList = make([]string, 0)
				}
			}
		case v := <-vulnMergeResultChan:
			// 计算url的hash值
			urlHash := utils.Get16MD5Encode(v.Poc.Url)
			uniqueKey := ""
			if v.IsPoc || (v.Poc.Cve == "" && v.Poc.Cnvd == "" && v.Poc.Cnnvd == "") {
				uniqueKey = fmt.Sprintf("%s-%s:%s", v.Poc.OriginalIds[0], urlHash, v.TaskId)
			} else {
				uniqueKey = fmt.Sprintf("%s-%s-%s-%s:%s", v.Poc.Cve, v.Poc.Cnvd, v.Poc.Cnnvd, urlHash, v.TaskId)
			}

			// 写入版本记录
			pocRecord := &esmodel.PocRecord{Poc: v.Poc}
			pocRecord.Id = v.PocRecordId
			pocRecord.PocId = v.Poc.Id
			pocRecord.BackupMode = "full"
			pocRecord.BackupTime = localtime.NewLocalTime(time.Now())
			// 记录当前批次的数据信息, 用于删除锁
			currentBatchPocList = append(currentBatchPocList, uniqueKey)
			records = append(records, pocRecord)

			// 触发写入
			count := len(records)
			if count > 200 {
				mlog.Infof("开始写入漏洞融合结果或记录. count: %d", count)
				err := vmf.FlushMergeResult(records, currentBatchPocList)
				if err == nil {
					records = make([]*esmodel.PocRecord, 0)
					currentBatchPocList = make([]string, 0)
				}
			}
		}
	}
}

// vulnResultBulkServiceDo 写入漏洞融合结果或记录
func vulnResultBulkServiceDo(bulkSercice *es.SafeBulkService, currentBatchPocList []string, mlog *common_logs.Logger) error {
	resp, err := bulkSercice.Refresh("true").Do(context.Background())
	if err != nil {
		mlog.Warnf("漏洞融合结果或记录写入失败. err: %v", err)
		return err
	} else {
		vulnResultWriteHandler(resp.Items, mlog)
		mlog.Debug("漏洞融合记录写入成功.")
		bulkSercice.Reset()

		// 删除入库数据的锁
		for _, uniqueKey := range currentBatchPocList {
			key, taskId := strings.Split(uniqueKey, ":")[0], strings.Split(uniqueKey, ":")[1]
			redisKey := redis_helper.GetVulnMergeLockKey(key)
			distributedlock.Unlock(redisKey, taskId)
		}
	}
	return nil
}

// vulnResultWriteHandler 处理bulk写入结果
func vulnResultWriteHandler(items []map[string]*elastic.BulkResponseItem, mlog *common_logs.Logger) {
	pocIndexName := esmodel.NewPoc().IndexName()
	pocRecordIndexName := esmodel.NewPocRecord().IndexName()
	for _, item := range items {
		for op, detail := range item {
			if op == "update" || op == "create" {
				// 结果表
				if detail.Index == pocIndexName {
					if detail.Error != nil {
						mlog.Warnf("漏洞融合结果写入失败. op: %s, detail: %v", op, detail)
						record, ok := vulnMergeRecordList.Load(detail.Id)
						if ok {
							r := record.(*esmodel.MergeRecords)
							r.Status = 2
							r.Message = fmt.Sprintf("漏洞融合结果写入失败. op: %s, detail: %v", op, detail)
							vulnMergeRecordChan <- r
							vulnMergeRecordList.Delete(detail.Id)
						}
					} else {
						mlog.Debug(fmt.Sprintf("资产融合结果写入成功. op: %s, detail: %v", op, detail))
						record, ok := vulnMergeRecordList.Load(detail.Id)
						if ok {
							r := record.(*esmodel.MergeRecords)
							r.Status = 1
							r.Message = fmt.Sprintf("资产融合结果写入成功. op: %s, detail: %v", op, detail)
							vulnMergeRecordChan <- r
							vulnMergeRecordList.Delete(detail.Id)
						}
					}
				} else if detail.Index == pocRecordIndexName {
					if detail.Error != nil {
						mlog.Warnf("漏洞融合记录写入失败. op: %s, detail: %+v", op, *detail)
						record, ok := vulnMergeRecordList.Load(detail.Id)
						if ok {
							r := record.(*esmodel.MergeRecords)
							r.Status = 2
							r.Message = fmt.Sprintf("资产融合记录写入失败. op: %s, detail: %v", op, detail)
							vulnMergeRecordChan <- r
							vulnMergeRecordList.Delete(detail.Id)
						}
					} else {
						mlog.Debug(fmt.Sprintf("漏洞融合记录写入成功. op: %s, detail: %v", op, detail))
						record, ok := vulnMergeRecordList.Load(detail.Id)
						if ok {
							r := record.(*esmodel.MergeRecords)
							r.Status = 1
							r.PocRecordId = detail.Id
							vulnMergeRecordChan <- r
							vulnMergeRecordList.Delete(detail.Id)
						}
					}
				}
			}
		}
	}
}
func (vmf *VulnMergeFlow) FlushMergeRecord(bulkService *es.SafeBulkService) {
	mlog := vmf.mlog
	_, err := bulkService.Do(context.Background())
	if err != nil {
		mlog.Warnf("漏洞融合记录写入失败. err: %v", err)
	} else {
		mlog.Debug("漏洞融合记录写入成功.")
		bulkService.Reset()
	}
}

// WriteRecord 写入融合记录
func (vmf *VulnMergeFlow) WriteRecord() {
	mlog := vmf.mlog
	q := queue.NewQueue(queue.QueueType_Redis)
	bulkService := es.GetEsClient().Bulk()
	indexName := esmodel.NewMergeRecordsModel().IndexName()
	ticker := time.NewTicker(4 * time.Second)
	defer ticker.Stop()
	for {
		select {
		case <-ticker.C:
			if bulkService.NumberOfActions() > 0 {
				mlog.Infof("4秒未收到漏洞融合记录消息，开始写入剩余数据. count: %d", bulkService.NumberOfActions())
				vmf.FlushMergeRecord(bulkService)
			}
		case r := <-vulnMergeRecordChan:
			mlog.Debugf("开始写入资产融合记录. result: %v", r)
			bulkService.Add(elastic.NewBulkUpdateRequest().DocAsUpsert(true).Index(indexName).Id(r.Id).Doc(r))
			// ack
			msgId, ok := vulnMergeRecordAndMsgId.Load(r.Id)
			mlog.Debugf("Ack-vlun 开始确认消息. msgId: %s", msgId)
			if ok {
				q.Ack(cfg.LoadQueue().VulnMergeQueue, vmf.groupName, msgId.(string))
				vulnMergeRecordAndMsgId.Delete(r.Id)
			}

			if bulkService.NumberOfActions() >= 200 {
				mlog.Infof("开始写入漏洞融合记录. count: %d", bulkService.NumberOfActions())
				vmf.FlushMergeRecord(bulkService)
			}
		}
	}
}

// BatchCalculateRiskLevel 批量计算风险等级
func BatchCalculateRiskLevel(updateAll bool) (int64, error) {
	mlog := logs.GetLogger("vuln")
	// 收到is_end消息时，可能最后一批数据还未刷盘
	time.Sleep(10 * time.Second)
	listIds := make([]interface{}, 0)

	if updateAll {
		// 查询所有poc数据的ID
		mlog.Info("正在获取所有poc数据ID进行风险等级更新")

		// 使用scan API获取所有文档ID
		query := elastic.NewMatchAllQuery()
		scroll := es.GetEsClient().Scroll().
			Index(esmodel.NewPoc().IndexName()).
			Query(query).
			Size(5000).
			FetchSourceContext(elastic.NewFetchSourceContext(false))

		for {
			results, err := scroll.Do(context.Background())
			if err == io.EOF {
				break // 所有结果都获取完毕
			}
			if err != nil {
				mlog.Errorf("获取所有poc ID失败: %v", err)
				return 0, err
			}

			// 提取文档ID
			for _, hit := range results.Hits.Hits {
				listIds = append(listIds, hit.Id)
			}
		}
		// 清除scroll
		scroll.Clear(context.Background())

		mlog.Infof("获取到 %d 个poc数据ID用于更新风险等级", len(listIds))
	} else {
		// 从pocIds中获取需要更新的ID
		pocIds.Range(func(key, value interface{}) bool {
			listIds = append(listIds, key)
			return true
		})
		pocIds.Clear()

		if len(listIds) == 0 {
			mlog.Info("没有需要更新风险等级的poc数据")
			return 0, nil
		}

		mlog.Infof("从pocIds中获取到 %d 个需要更新风险等级的poc数据ID", len(listIds))
	}

	// 获取设置
	ps := poc_settings.NewPocSettingModel()
	settings, err := ps.GetPocSettingFromRedis()
	if err != nil {
		mlog.Warnf("BatchCalculateRiskLevel获取漏洞设置失败. err: %v", err)
		return 0, err
	}

	// 分批处理，每批5000条
	batchSize := 5000
	batchCount := 0

	// 手动分批处理，避免使用slices.Chunk的return迭代器
	for i := 0; i < len(listIds); i += batchSize {
		end := i + batchSize
		if end > len(listIds) {
			end = len(listIds)
		}
		chunk := listIds[i:end]

		batchCount++
		mlog.Infof("正在处理第 %d 批poc数据，本批数量: %d", batchCount, len(chunk))

		// 批量查询漏洞
		pocList, err := es.All[esmodel.Poc](1000, elastic.NewTermsQuery("id", chunk...), nil)
		if err != nil {
			mlog.Warnf("BatchCalculateRiskLevel批量查询漏洞失败. err: %v", err)
			continue
		}

		// 获取所有业务系统名称
		businessNames := make([]string, 0)
		for _, poc := range pocList {
			businessNames = append(businessNames, poc.BusinessNamesTmp...)
		}
		businessNames = utils.ListDistinctNonZero(businessNames)

		// 使用聚合查询一次性获取所有业务系统的漏洞数量
		businessPocCount := make(map[string]int64)
		if len(businessNames) > 0 {
			query := elastic.NewBoolQuery().
				Must(elastic.NewTermsQueryFromStrings("business_name_tmp", businessNames...))
			searchResult, err := es.GetEsClient().Search().
				Index(esmodel.NewPoc().IndexName()).
				Query(query).
				Aggregation("business_count", elastic.NewTermsAggregation().
					Field("business_name_tmp").
					Size(len(businessNames))).
				Size(0).
				Do(context.Background())
			if err != nil {
				mlog.Warnf("BatchCalculateRiskLevel聚合查询业务系统漏洞数量失败. err: %v", err)
			} else {
				if agg, found := searchResult.Aggregations.Terms("business_count"); found {
					for _, bucket := range agg.Buckets {
						businessPocCount[bucket.Key.(string)] = bucket.DocCount
					}
				}
			}
		}

		// 批量计算风险等级
		for _, poc := range pocList {
			// 计算漏洞等级
			levelNum := poc.GetPocLevelNum(settings)
			foundStatus := poc.GetPocFoundStatus(settings)
			havePoc := poc.GetHavePoc(settings)
			haveExp := poc.GetHaveExp(settings)
			internet := poc.GetInternet(settings)
			business := poc.GetBusiness(settings)

			// 计算同业务系统漏洞数量
			sameBusiness := 1 // 默认值为1
			if settings.AssetSameBusinessEnable != 0 {
				for _, businessName := range poc.BusinessNamesTmp {
					count := businessPocCount[businessName]
					if count >= 3 {
						sameBusiness = settings.AssetSameBusinessThreeGe
						break
					} else if count == 2 {
						sameBusiness = settings.AssetSameBusinessTwo
						break
					} else if count == 1 {
						sameBusiness = settings.AssetSameBusinessOne
						break
					}
				}
			}

			// 计算风险值
			poc.RiskNum = levelNum * foundStatus * havePoc * haveExp * internet * business * sameBusiness
			poc.RepairPriority = poc.GetRepairPriority(settings, poc.RiskNum)
		}

		// 分批更新风险等级，每批500条
		updateBatchSize := 500
		updateBatchCount := 0

		// 手动分批处理更新，避免使用slices.Chunk
		for i := 0; i < len(pocList); i += updateBatchSize {
			end := i + updateBatchSize
			if end > len(pocList) {
				end = len(pocList)
			}
			updateChunk := pocList[i:end]

			updateBatchCount++
			mlog.Infof("正在更新第 %d 批poc数据的风险等级，本批数量: %d", updateBatchCount, len(updateChunk))

			bulkService := es.GetEsClient().Bulk()
			for _, poc := range updateChunk {
				bulkService.Add(elastic.NewBulkUpdateRequest().
					Index(poc.IndexName()).
					Id(poc.Id).
					Doc(map[string]interface{}{
						"risk_num":        poc.RiskNum,
						"repair_priority": poc.RepairPriority,
					}))
			}

			if bulkService.NumberOfActions() > 0 {
				_, err = bulkService.Do(context.Background())
				if err != nil {
					mlog.Warnf("BatchCalculateRiskLevel批量更新风险等级失败. err: %v", err)
				}
			}
		}
	}

	mlog.Info("批量计算风险等级完成")
	return int64(len(listIds)), nil
}
