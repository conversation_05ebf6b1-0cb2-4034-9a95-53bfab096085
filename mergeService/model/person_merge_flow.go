// 人员融合流程

package model

import (
	"context"
	"encoding/json"
	"fmt"
	"math/rand"
	"sync"
	"time"

	"fobrain/fobrain/common/localtime"
	"fobrain/initialize/es"
	"fobrain/initialize/redis"
	"fobrain/mergeService/event"
	model "fobrain/mergeService/model/manual_calibration"
	es_model "fobrain/models/elastic"
	esmodel "fobrain/models/elastic/staff"
	merge_models "fobrain/models/mysql/merge"
	"fobrain/models/mysql/strategy"
	redis_helper "fobrain/models/redis"
	"fobrain/pkg/cfg"
	distributedlock "fobrain/pkg/distributedLock"
	"fobrain/pkg/queue"
	"fobrain/pkg/tracker"
	"fobrain/pkg/utils"
	"fobrain/pkg/utils/common_logs"

	"github.com/go-errors/errors"
	"github.com/olivere/elastic/v7"
)

var (
	personStrategyLock = sync.RWMutex{}
	// 人员融合记录写入通道
	personMergeRecordChan = make(chan *<PERSON><PERSON><PERSON><PERSON>, 100)
	// 人员融合结果写入通道
	personMergeResultChan = make(chan *<PERSON>rge<PERSON>, 100)

	personMergeRecordList = sync.Map{}
	personMergeMsgId      = sync.Map{}
)

type PersonMergeFlow struct {
	mlog      *common_logs.Logger
	groupName string
	qName     string
}

type MergePerson struct {
	Record       *esmodel.MergeRecords
	Staff        *esmodel.Staff // 最终融合结果
	MsgData      *esmodel.Staff // 根据消息获取的唯一数据
	OriginalData []*esmodel.ProcessStaff
	Rule         []*strategy.Strategy
	FieldValInfo []*es_model.FieldValInfo // 字段采信信息
	Msg          queue.QueueMsg
	TaskId       string
	StartTime    time.Time
}

// Merge 合并主方法
func (p *PersonMergeFlow) Merge() {
	mlog := p.mlog
	redisClient := redis.GetRedisClient()
	qName := cfg.LoadQueue().PersonMergeQueue
	p.qName = qName
	p.groupName = "person_merge_flow"
	// 最大并发数量
	var maxConcurrency = cfg.LoadQueue().PersonMergeConcurrent
	msgChan, consumerName, err := queue.NewQueue(queue.QueueType_Redis).Subscribe(qName, p.groupName, maxConcurrency)
	mlog.Info("订阅人员融合消息. qName: ", qName, " consumerName: ", consumerName)
	if err != nil {
		mlog.Error("订阅人员融合消息失败. qName: ", qName, " err: ", err)
		return
	}
	// defer queue.NewQueue(queue.QueueType_Redis).Unsubscribe(qName, consumerName)

	// 开启处理时间跟踪器
	timeTracker := tracker.NewProcessingTimeTracker(1*time.Minute, MergeFlowType_Person)
	trackerSignalChan := make(chan struct{})
	enableTracker := printDutation(timeTracker, trackerSignalChan, MergeFlowType_Person, mlog)

	// 开启融合结果写入协程
	go p.WriteResult()
	// 开启融合记录写入协程
	go p.WriteRecord()
	sem := make(chan struct{}, maxConcurrency)
	go func(sem chan struct{}) {
		for msg := range msgChan {
			sem <- struct{}{}
			go func(m queue.QueueMsg) {
				defer func() {
					if r := recover(); r != nil {
						wrapError := errors.Wrap(r, 3)
						mlog.Errorf("人员融合消息处理失败. msg: %v, err: %v, stack: %+v", m, wrapError.Error(), wrapError.Stack())

						// 记录异常
						mergeRecordId, _ := getMergeRecordId(m)
						merge_models.NewMergeExceptionsModel().Create(&merge_models.MergeExceptions{
							MergeRecordId:  mergeRecordId,
							BatchId:        "",
							ExceptionCode:  merge_models.ExceptionCodeProcessFailed,
							BusinessModule: merge_models.BusinessModulePersonMerge,
							Status:         merge_models.StatusFailed,
							ErrorMessage:   fmt.Sprintf("人员融合消息处理失败. msg: %v, err: %v, stack: %+v", m, wrapError.Error(), wrapError.Stack()),
							Payload:        fmt.Sprintf("%+v", m),
						})
					}
					// 释放一个处理能力
					mlog.Debug("释放一个处理能力")
					<-sem
				}()
				now := time.Now()
				mlog.Info("收到人员融合消息. msg: ", m)
				// 构建融合记录-消息
				ma := &MergePerson{
					Msg:       m,
					StartTime: now,
					Record: &esmodel.MergeRecords{
						Id:        UUIDStr(),
						CreatedAt: localtime.NewLocalTime(now),
						MergeMode: es_model.MergeMode_Auto,
					},
				}

				// 根据消息组装获取关联数据的条件
				if len(m.Values) < 1 {
					p.failedHandle("", merge_models.ExceptionCodeInvalidMsg, fmt.Sprintf("人员融合消息中未包含搜索条件, msg: %v", m), "", "", m, now, timeTracker, trackerSignalChan, enableTracker, false, false, "")
					return
				}
				taskId, err := getTaskId(m)
				if err != nil {
					mlog.Warn("获取任务ID失败. msg: ", m, " err: ", err)
					p.failedHandle(taskId, merge_models.ExceptionCodeInvalidMsg, fmt.Sprintf("获取任务ID失败. msg: %v, err: %v", m, err), "", "", m, now, timeTracker, trackerSignalChan, enableTracker, false, false, "")
					return
				}
				ma.TaskId = taskId
				isStart, err := startHandle(m, taskId, MergeFlowType_Person, mlog)
				if err != nil {
					p.failedHandle(taskId, merge_models.ExceptionCodeInvalidMsg, fmt.Sprintf("获取任务开始状态失败. msg: %v, err: %v", m, err), "", "", m, now, timeTracker, trackerSignalChan, enableTracker, false, false, "")
					return
				}
				if isStart {
					queue.NewQueue(queue.QueueType_Redis).Ack(qName, p.groupName, m.ID)
					return
				}
				isEnd := endHandle(m, taskId, MergeFlowType_Person, mlog)
				if isEnd {
					queue.NewQueue(queue.QueueType_Redis).Ack(qName, p.groupName, m.ID)
					return
				}

				ctx := context.Background()

				// 获取消息对应的唯一数据
				processData, err := p.GetProcessDataByMsg(ctx, m)
				if err != nil {
					p.failedHandle(taskId, merge_models.ExceptionCodeDataFetchFailed, fmt.Sprintf("获取消息数据失败. msg: %v, err: %v", m, err), "id", fmt.Sprintf("%v", m.Values["id"]), m, now, timeTracker, trackerSignalChan, enableTracker, false, false, "")
					return
				}

				// 查询 redis 中是否存在已处理未入库的数据
				retryCount := 0
				uniqueKey := processData.UniqueKey
			retry:
				lockKey := redis_helper.GetPersonMergeLockKey(uniqueKey)
				ok := distributedlock.Lock(lockKey, taskId, 120)
				if !ok {
					// 尝试获取锁内容，判断如果是同一个taskId，则不跳过处理
					lockContent, err := redisClient.Get(ctx, lockKey).Result()
					if err != nil {
						p.failedHandle(taskId, merge_models.ExceptionCodeLockAcquireFailed, fmt.Sprintf("获取锁内容失败. taskId: %s, Msg: %v", taskId, m), "lockKey", lockKey, m, now, timeTracker, trackerSignalChan, enableTracker, true, false, lockKey)
						return
					} else {
						if lockContent == taskId {
							// 如果是同一个taskId，则丢弃这条数据
							p.failedHandle(taskId, merge_models.ExceptionCodeDuplicateKeyInBatch, fmt.Sprintf("同一批数据已存在相同唯一值, 丢弃本数据. taskId: %s, Msg: %v", taskId, m), "lockKey", lockKey, m, now, timeTracker, trackerSignalChan, enableTracker, true, false, lockKey)
							return
						}
					}
					// 生成 3000 到 5000 之间的随机毫秒数
					randomMilliseconds := rand.Intn(2001) + 3000
					p.mlog.Infof("获取人员关联数据(未入库)冲突, 等待%d毫秒后继续处理. taskId: %s, Msg: %v", randomMilliseconds, taskId, m)
					// 将毫秒转换为 Duration
					sleepDuration := time.Duration(randomMilliseconds) * time.Millisecond
					// 休眠随机时间
					time.Sleep(sleepDuration)
					retryCount++
					if retryCount > 3 {
						p.failedHandle(taskId, merge_models.ExceptionCodeLockAcquireFailed, fmt.Sprintf("获取人员关联数据(未入库)冲突, 超过最大重试次数. Msg: %v", m), "lockKey", lockKey, m, now, timeTracker, trackerSignalChan, enableTracker, false, false, lockKey)
						return
					} else {
						goto retry
					}
				}

				// todo 设置上下文超时条件
				// 获取消息对应的数据
				msgData, err := p.GetMsgData(ctx, processData.UniqueKey)
				if err != nil {
					p.failedHandle(taskId, merge_models.ExceptionCodeDataFetchFailed, fmt.Sprintf("获取消息对应的数据失败. msg: %v, err: %v", m, err), "uniqueKey", processData.UniqueKey, m, now, timeTracker, trackerSignalChan, enableTracker, false, true, lockKey)
					return
				}
				ma.MsgData = msgData
				// 获取关联数据
				originaleData, total, err := p.GetRelationData(ctx, processData.UniqueKey)
				if err != nil {
					p.failedHandle(taskId, merge_models.ExceptionCodeDataFetchFailed, fmt.Sprintf("获取关联数据失败. msg: %v, err: %v", m, err), "uniqueKey", processData.UniqueKey, m, now, timeTracker, trackerSignalChan, enableTracker, false, true, lockKey)
					return
				}
				if len(originaleData) < 1 {
					p.failedHandle(taskId, merge_models.ExceptionCodeDataFetchFailed, fmt.Sprintf("获取关联数据为空. msg:%v", m), "uniqueKey", processData.UniqueKey, m, now, timeTracker, trackerSignalChan, enableTracker, false, true, lockKey)
					return
				}
				originaleInfo := utils.ListColumn(originaleData, func(item *esmodel.ProcessStaff) string {
					return fmt.Sprintf("id:%s,source:%v,node:%v", item.Id, item.Source, item.Node)
				})
				mlog.Debugf("获取关联数据成功. Msg id: %s, total: %d, originaleInfo: %s", m.ID, total, originaleInfo)
				ma.Record.Name = originaleData[0].Name
				ma.Record.Mobile = originaleData[0].Mobile
				ma.Record.Fid = originaleData[0].UniqueKey
				// 构建融合记录-关联数据
				ma.OriginalData = originaleData

				// 获取融合规则
				rules, err := p.GetMergeRules()
				if err != nil {
					p.failedHandle(taskId, merge_models.ExceptionCodeDataFetchFailed, fmt.Sprintf("获取人员融合规则失败. msg: %v, err: %v", m, err), "", "", m, now, timeTracker, trackerSignalChan, enableTracker, false, true, lockKey)
					return
				}
				mlog.Debug("获取人员融合规则成功. rules count: ", len(rules))
				// 构建融合记录-规则
				ma.Rule = rules
				ma.Record.Strategies = rules

				// 开启执行融合逻辑
				p.ExecuteMerge(ctx, ma)

				// 记录任务处理量
				tracker.IncrementCount(redis_helper.PersonMergeTaskKey(fmt.Sprintf("%v", taskId), "count"), 1)
				duration := time.Now().UnixMilli() - now.UnixMilli()
				mlog.Infof("人员融合消息处理完成. msg: %v,耗时: %vms", m, duration)
				// 记录耗时统计
				timeTracker.AddRecord(fmt.Sprintf("%d:%s", duration, m.ID))
				// 统计任务总耗时
				tracker.IncrementCount(redis_helper.PersonMergeTotalTimeKey(fmt.Sprintf("%v", taskId)), int(duration))
				if enableTracker {
					// 给追踪器发信号，打印耗时信息
					trackerSignalChan <- struct{}{}
				}
			}(msg)
		}
	}(sem)
}

func (p *PersonMergeFlow) failedHandle(taskId string, errType, errMsg string, identifier string, identifierValue string, m queue.QueueMsg, start time.Time, timeTracker *tracker.ProcessingTimeTracker, trackerSignalChan chan struct{}, enableTracker bool, isDiscard bool, deleteLock bool, lockKey string) {
	p.mlog.Warnf(errMsg)

	if !isDiscard {
		// 记录任务处理量
		_ = tracker.IncrementCount(redis_helper.PersonMergeTaskKey(taskId, "failed_count"), 1)
	} else {
		// 记录任务处理量
		_ = tracker.IncrementCount(redis_helper.PersonMergeTaskKey(taskId, "discard_count"), 1)
	}

	// 计算任务处理耗时
	duration := time.Now().UnixMilli() - start.UnixMilli()
	// 记录耗时统计
	_ = timeTracker.AddRecord(fmt.Sprintf("%d:%s", duration, m.ID))
	// 统计任务总耗时
	_ = tracker.IncrementCount(redis_helper.PersonMergeTotalTimeKey(taskId), int(duration))
	if enableTracker {
		// 给追踪器发信号，打印耗时信息
		trackerSignalChan <- struct{}{}
	}
	// 确认消息已处理
	queue.NewQueue(queue.QueueType_Redis).Ack(p.qName, p.groupName, m.ID)

	if deleteLock {
		// 删除入库数据的锁
		distributedlock.Unlock(lockKey, taskId)
		p.mlog.Infof("处理失败，删除入库数据的锁. redisKey: %s, msgId: %s", lockKey, taskId)
	}
	mergeRecordId, _ := getMergeRecordId(m)
	exception := &merge_models.MergeExceptions{
		MergeRecordId:  mergeRecordId,
		BatchId:        taskId,
		ExceptionCode:  errType,
		BusinessModule: merge_models.BusinessModulePersonMerge,
		Status: func() string {
			if isDiscard {
				return merge_models.StatusDiscarded
			}
			return merge_models.StatusFailed
		}(),
		ErrorMessage:    errMsg,
		Payload:         fmt.Sprintf("%+v", m),
		Identifier:      identifier,
		IdentifierValue: identifierValue,
	}
	// 记录异常
	merge_models.NewMergeExceptionsModel().Create(exception)
}

// 获取融合规则
func (p *PersonMergeFlow) GetMergeRules() ([]*strategy.Strategy, error) {
	key := redis_helper.PersonMergeRuleKey()
	redisClient := redis.GetRedisClient()
	// 从redis缓存获取规则
	ruleByte, err := redisClient.Get(context.Background(), key).Bytes()
	if err != nil {
		p.mlog.Warn("从redis缓存获取人员融合规则失败. err: ", err)
	}
	strategies := make([]*strategy.Strategy, 0)
	if len(ruleByte) > 0 {
		err = json.Unmarshal(ruleByte, &strategies)
		if err != nil {
			p.mlog.Warn("解析redis缓存中的人员融合规则失败. err: ", err)
		}
	}
	if len(strategies) == 0 {
		personStrategyLock.Lock()
		defer personStrategyLock.Unlock()
		sModel := strategy.NewStrategyModel()
		res, err := sModel.ListAllDistinct(strategy.BusinessType_PersonMerge)
		if err != nil {
			p.mlog.Warn("获取人员融合规则失败. err: ", err)
		} else {
			strategies = res
			// 写入缓存
			ruleByte, _ := json.Marshal(strategies)
			td := time.Duration(mergeRuleCacheTime) * time.Minute
			err = redisClient.Set(context.Background(), key, ruleByte, td).Err()
			if err != nil {
				p.mlog.Warn("写入人员融合规则缓存失败. err: ", err)
			}
		}
	}

	return strategies, nil
}

// GetMsgData 获取消息对应的数据
func (p *PersonMergeFlow) GetMsgData(ctx context.Context, uniqueKey string) (*esmodel.Staff, error) {
	if uniqueKey == "" {
		return nil, errors.New("uniqueKey不能为空")
	}
	data, err := esmodel.NewStaff().GetByFId(context.Background(), uniqueKey)
	if err != nil {
		return nil, err
	}
	return data, nil
}

// GetProcessDataByMsg 获取消息对应的唯一数据
func (p *PersonMergeFlow) GetProcessDataByMsg(ctx context.Context, msg queue.QueueMsg) (*esmodel.ProcessStaff, error) {
	var idStr string
	id, exist := msg.Values["id"]
	if !exist {
		return nil, fmt.Errorf("消息中未包含id字段")
	}
	switch v := id.(type) {
	case string:
		idStr = v
	case float64:
		idStr = fmt.Sprintf("%d", int(v))
	case int:
		idStr = fmt.Sprintf("%d", v)
	default:
		return nil, fmt.Errorf("id字段类型错误")
	}
	data, err := es.GetById[esmodel.ProcessStaff](idStr)
	if err != nil {
		return nil, err
	}
	return data, nil
}

// GetRelationData 获取关联数据
func (p *PersonMergeFlow) GetRelationData(ctx context.Context, uniqueKey string) ([]*esmodel.ProcessStaff, int64, error) {
	sortCon := []elastic.Sorter{elastic.NewFieldSort("created_at").Order(false)}
	_, data, err := es.List[esmodel.ProcessStaff](1, 100, elastic.NewBoolQuery().Must(elastic.NewTermQuery("unique_key", uniqueKey)), sortCon)
	if err != nil {
		return nil, 0, err
	}
	result := make([]*esmodel.ProcessStaff, 0, len(data))
	result = append(result, data...)

	return result, int64(len(data)), nil
}

// 执行合并
func (p *PersonMergeFlow) ExecuteMerge(ctx context.Context, ma *MergePerson) {
	executor := &PersonStrategyExecutor{
		SourceData: ma.OriginalData,
		Strategy:   ma.Rule,
	}
	strategyContext := &StrategyExecutorContext[*esmodel.Staff]{
		Executor: executor,
	}
	// 根据融合策略，执行字段融合
	ma.Staff, ma.FieldValInfo = strategyContext.Executor.Execute()
	staffId := UUIDStr()
	ma.Staff.Id = staffId
	ma.Staff.Name = ma.OriginalData[0].Name
	ma.Staff.Mobile = ma.OriginalData[0].Mobile
	ma.Staff.Fid = ma.OriginalData[0].UniqueKey
	ma.Staff.FidHash = utils.Md5Hash(ma.Staff.Fid)
	ma.Staff.CreatedAt = localtime.NewLocalTime(time.Now())

	if ma.MsgData != nil {
		ma.Staff.MergeCount = ma.MsgData.MergeCount + 1
		ma.Staff.Id = ma.MsgData.Id
		// 保存历史用过的所有process_id,防止删除时遗漏
		ma.Staff.AllProcessIds = append(ma.Staff.AllProcessIds, ma.MsgData.AllProcessIds...)
		ma.Staff.AllProcessIds = utils.ListDistinctNonZero(ma.Staff.AllProcessIds)
		ma.Staff.CreatedAt = ma.MsgData.CreatedAt
		// 更新人工校准数据
		personCalibration := &model.PersonManualCalibrationModel{}
		err := personCalibration.UpdateMergeResult(ma.Staff, ma.MsgData)
		if err != nil {
			p.mlog.Warnf("更新人工校准数据失败. err: %v", err)
		}
	} else {
		ma.Staff.MergeCount = 1
		ma.Staff.CreatedAt = localtime.NewLocalTime(time.Now())
	}

	// 触发人员数据融合单条数据结束事件
	err := event.NewEventBus().Emit(event.Event_Person_MergeData_End, ma.Staff, ma.MsgData)
	if err != nil {
		p.mlog.Warnf("触发人员数据融合单条数据结束事件失败. err: %v", err)
	}

	ma.Staff.UpdatedAt = localtime.NewLocalTime(time.Now())

	ma.Record.StaffId = ma.Staff.Id
	ma.Record.SourceIds = ma.Staff.SourceIds
	ma.Record.NodeIds = ma.Staff.NodeIds
	ma.Record.StaffTaskIds = ma.Staff.TaskDataIds

	// 记录融合记录信息
	personMergeRecordList.Store(ma.Staff.Id, ma.Record)
	// 因为结果表和结果记录表id不同，所以需要保存两份
	personMergeRecordList.Store(ma.Record.Id, ma.Record)
	personMergeMsgId.Store(ma.Record.Id, ma.Msg.ID)

	personMergeResultChan <- ma
}

// WriteResult 写入结果（批量写入实现）
func (p *PersonMergeFlow) WriteResult() {
	mlog := p.mlog
	var bulkService = es.GetEsClient().Bulk()
	var ticker = time.NewTicker(3 * time.Second)
	var indexName = esmodel.NewStaff().IndexName()
	var staffRecordIndexName = esmodel.NewStaffRecord().IndexName()
	var uniqueKeys = make(map[string]string)
	defer ticker.Stop()
	for {
		select {
		case <-ticker.C:
			if bulkService.NumberOfActions() > 0 {
				mlog.Infof("3秒未收到人员融合结果消息，开始写入剩余数据. count: %d", bulkService.NumberOfActions())
				err := p.flushPersonBulk(bulkService, uniqueKeys)
				if err == nil {
					uniqueKeys = make(map[string]string)
				}
			}
		case v := <-personMergeResultChan:
			// 写入结果
			mlog.Debugf("开始写入人员融合结果. result: %v", *v)
			if v.Staff.CreatedAt == nil || v.Staff.CreatedAt.Time().IsZero() {
				v.Staff.CreatedAt = localtime.NewLocalTime(time.Now())
			}
			if v.Staff.UpdatedAt == nil || v.Staff.UpdatedAt.Time().IsZero() {
				v.Staff.UpdatedAt = localtime.NewLocalTime(time.Now())
			}
			uniqueKeys[v.Staff.Fid] = v.TaskId
			bulkService.Add(elastic.NewBulkUpdateRequest().DocAsUpsert(true).Index(indexName).Id(v.Staff.Id).Doc(v.Staff))
			staffRecordId := UUIDStr()
			staffRecord := &esmodel.StaffRecord{Staff: v.Staff, StaffId: v.Staff.Id}
			staffRecord.StaffId = staffRecordId
			bulkService.Add(elastic.NewBulkCreateRequest().Index(staffRecordIndexName).Id(staffRecordId).Doc(staffRecord))
			if bulkService.NumberOfActions() >= 100 {
				err := p.flushPersonBulk(bulkService, uniqueKeys)
				if err == nil {
					uniqueKeys = make(map[string]string)
				}
			}
		}
	}
}

// flushPersonBulk 批量写入人员融合结果和记录
func (p *PersonMergeFlow) flushPersonBulk(bulkService *es.SafeBulkService, uniqueKeys map[string]string) error {
	mlog := p.mlog
	resp, err := bulkService.Refresh("true").Do(context.Background())
	if err != nil {
		mlog.Warnf("批量写入人员融合结果或记录失败. err: %v", err)
		return err
	}
	mlog.Infof("批量写入人员融合结果或记录成功. count: %d", bulkService.NumberOfActions())
	bulkService.Reset()
	var indexName = esmodel.NewStaff().IndexName()
	var staffRecordIndexName = esmodel.NewStaffRecord().IndexName()
	// 处理bulk返回结果
	for _, item := range resp.Items {
		for op, detail := range item {
			if op == "create" && detail.Index == indexName {
				if detail.Error != nil {
					mlog.Warnf("人员融合结果写入失败. op: %s, detail: %v", op, detail)
					record, ok := personMergeRecordList.Load(detail.Id)
					if ok {
						r := record.(*esmodel.MergeRecords)
						r.Status = 2
						r.Message = fmt.Sprintf("人员融合结果写入失败. op: %s, detail: %v", op, detail)
						personMergeRecordChan <- &MergePerson{Record: r}
						personMergeRecordList.Delete(detail.Id)
					}
				} else {
					mlog.Debugf("人员融合结果写入成功. op: %s, detail: %v", op, detail)
					record, ok := personMergeRecordList.Load(detail.Id)
					if ok {
						r := record.(*esmodel.MergeRecords)
						r.Status = 1
						r.Message = fmt.Sprintf("人员融合结果写入成功. op: %s, detail: %v", op, detail)
						personMergeRecordChan <- &MergePerson{Record: r}
						personMergeRecordList.Delete(detail.Id)
					}
				}
			} else if op == "create" && detail.Index == staffRecordIndexName {
				if detail.Error != nil {
					mlog.Warnf("人员记录写入失败. op: %s, detail: %v", op, detail)
					record, ok := personMergeRecordList.Load(detail.Id)
					if ok {
						r := record.(*esmodel.MergeRecords)
						r.Status = 2
						r.Message = fmt.Sprintf("人员记录写入失败. op: %s, detail: %v", op, detail)
						personMergeRecordChan <- &MergePerson{Record: r}
						personMergeRecordList.Delete(detail.Id)
					}
				} else {
					mlog.Debugf("人员记录写入成功. op: %s, detail: %v", op, detail)
					record, ok := personMergeRecordList.Load(detail.Id)
					if ok {
						r := record.(*esmodel.MergeRecords)
						r.Status = 1
						r.Message = fmt.Sprintf("人员记录写入成功. op: %s, detail: %v", op, detail)
						personMergeRecordChan <- &MergePerson{Record: r}
						personMergeRecordList.Delete(detail.Id)
					}
				}
			}
		}
	}
	for key, value := range uniqueKeys {
		redisKey := redis_helper.GetPersonMergeLockKey(key)
		distributedlock.Unlock(redisKey, value)
	}
	return nil
}

// WriteRecord 写入融合记录（批量写入实现）
func (p *PersonMergeFlow) WriteRecord() {
	mlog := p.mlog
	var bulkService = es.GetEsClient().Bulk()
	var ticker = time.NewTicker(3 * time.Second)
	var indexName = esmodel.NewMergeRecordsModel().IndexName()
	var q = queue.NewQueue(queue.QueueType_Redis)
	defer ticker.Stop()
	for {
		select {
		case <-ticker.C:
			if bulkService.NumberOfActions() > 0 {
				mlog.Infof("3秒未收到人员融合记录消息，开始写入剩余数据. count: %d", bulkService.NumberOfActions())
				p.flushPersonRecordBulk(bulkService)
			}
		case v := <-personMergeRecordChan:
			mlog.Debugf("开始写入人员融合记录. result: %v", *v)
			msgId, ok := personMergeMsgId.Load(v.Record.Id)
			if ok {
				q.Ack(cfg.LoadQueue().PersonMergeQueue, p.groupName, msgId.(string))
			}
			bulkService.Add(elastic.NewBulkUpdateRequest().Index(indexName).DocAsUpsert(true).Id(v.Record.Id).Doc(v))
			if bulkService.NumberOfActions() >= 100 {
				p.flushPersonRecordBulk(bulkService)
			}
		}
	}
}

// flushPersonRecordBulk 批量写入人员融合记录
func (p *PersonMergeFlow) flushPersonRecordBulk(bulkService *es.SafeBulkService) {
	mlog := p.mlog
	_, err := bulkService.Do(context.Background())
	if err != nil {
		mlog.Warnf("批量写入人员融合记录失败. err: %v", err)
		return
	}
	mlog.Infof("批量写入人员融合记录成功. count: %d", bulkService.NumberOfActions())
	bulkService.Reset()
}
