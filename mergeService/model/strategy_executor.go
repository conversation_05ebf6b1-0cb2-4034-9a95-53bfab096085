// 策略执行器，执行策略，返回融合结果

package model

import (
	"fmt"
	frame "fobrain/mergeFrame"
	"fobrain/mergeService/event"
	logs "fobrain/mergeService/utils/log"
	es_model "fobrain/models/elastic"
	esmodel_asset "fobrain/models/elastic/assets"
	esmodel_device "fobrain/models/elastic/device"
	esmodel_vuln "fobrain/models/elastic/poc"
	esmodel_person "fobrain/models/elastic/staff"
	dbmodel "fobrain/models/mysql/strategy"
	"fobrain/pkg/utils"

	_ "fobrain/mergeService/field_handler/asset_field_handler"
	_ "fobrain/mergeService/field_handler/device_field_handler"
	_ "fobrain/mergeService/field_handler/person_field_handler"
	_ "fobrain/mergeService/field_handler/vuln_field_handler"
)

type StrategyExecutor[T any] interface {
	Execute() (T, []*es_model.FieldValInfo)
}

// 执行器上下文
type StrategyExecutorContext[T any] struct {
	Executor StrategyExecutor[T]
}

// AssetStrategyExecutor 资产融合策略执行器
type AssetStrategyExecutor struct {
	// 源数据
	SourceData []*esmodel_asset.ProcessAssets
	// 策略参数
	Strategy []*dbmodel.Strategy
}

// VulnStrategyExecutor 漏洞融合策略执行器
type VulnStrategyExecutor struct {
	// 源数据
	SourceData []*esmodel_vuln.ProcessPoc
	// 策略参数
	Strategy []*dbmodel.Strategy
}

// PersonStrategyExecutor 人员融合策略执行器
type PersonStrategyExecutor struct {
	// 源数据
	SourceData []*esmodel_person.ProcessStaff
	// 策略参数
	Strategy []*dbmodel.Strategy
}

// DeviceStrategyExecutor 设备融合策略执行器
type DeviceStrategyExecutor struct {
	// 源数据
	SourceData []*esmodel_device.ProcessDevice
	// 策略参数
	Strategy []*dbmodel.Strategy
}

// Execute 资产策略执行
func (e *AssetStrategyExecutor) Execute() (*esmodel_asset.Assets, []*es_model.FieldValInfo) {
	logger := logs.GetLogger("asset")
	allSourceIds := make([]uint64, 0)
	allNodeIds := make([]uint64, 0)
	allTaskDataIds := make([]string, 0)
	allProcessIds := make([]string, 0)
	asset := &esmodel_asset.Assets{
		SourceIds:             make([]uint64, 0),
		NodeIds:               make([]uint64, 0),
		TaskDataIds:           make([]string, 0),
		ProcessIds:            make([]string, 0),
		IpSegmentSource:       make(map[string]string),
		HostNameSource:        make(map[string]string),
		EthNameSource:         make(map[string]string),
		OsSource:              make(map[string]string),
		KernelSource:          make(map[string]string),
		ModelSource:           make(map[string]string),
		MakerSource:           make(map[string]string),
		SnSource:              make(map[string]string),
		MacSource:             make(map[string]string),
		ProductSource:         make(map[string]string),
		OperSource:            make(map[string]string),
		BusinessSource:        make(map[string][]*esmodel_asset.Business),
		MachineRoomSource:     make(map[string]string),
		MemorySizeSource:      make(map[string]string),
		MemoryUsageRateSource: make(map[string]string),
		CpuMakerSource:        make(map[string]string),
		CpuBrandSource:        make(map[string]string),
		CpuCountSource:        make(map[string]int),
		DiskCountSource:       make(map[string]int),
		DiskSizeSource:        make(map[string]int),
		DiskUsageRateSource:   make(map[string]string),
		LoadAverageSource:     make(map[string]string),
		PortsSource:           make(map[string][]*esmodel_asset.PortInfo),
		StatusSource:          make(map[string]int),
		TagsSource:            make(map[string]string),
		JarPackageInfoSource:  make(map[string][]*esmodel_asset.JarPackageInfo),
	}
	// 同源不同节点数据，取最新的数据，其他数据抛弃
	// 存储每个源节点的最新数据
	latestData := make(map[string]*esmodel_asset.ProcessAssets)
	// 遍历源数据
	for _, data := range e.SourceData {
		allSourceIds = append(allSourceIds, data.Source)
		allNodeIds = append(allNodeIds, data.Node)
		allTaskDataIds = append(allTaskDataIds, data.TaskDataId)
		allProcessIds = append(allProcessIds, data.Id)

		sourceKey := fmt.Sprintf("%d-%d", data.Source, data.Node)
		asset.IpSegmentSource[sourceKey] = data.IpSegment
		asset.HostNameSource[sourceKey] = data.HostName
		asset.EthNameSource[sourceKey] = data.EthName
		asset.OsSource[sourceKey] = data.Os
		asset.KernelSource[sourceKey] = data.Kernel
		asset.ModelSource[sourceKey] = data.Model
		asset.MakerSource[sourceKey] = data.Maker
		asset.SnSource[sourceKey] = data.Sn
		asset.MacSource[sourceKey] = data.Mac
		asset.OperSource[sourceKey] = data.Oper
		asset.MachineRoomSource[sourceKey] = data.MachineRoom
		asset.MemorySizeSource[sourceKey] = data.MemorySize
		asset.MemoryUsageRateSource[sourceKey] = data.MemoryUsageRate
		asset.CpuMakerSource[sourceKey] = data.CpuMaker
		asset.CpuBrandSource[sourceKey] = data.CpuBrand
		asset.CpuCountSource[sourceKey] = data.CpuCount
		asset.DiskCountSource[sourceKey] = data.DiskCount
		asset.DiskSizeSource[sourceKey] = data.DiskSize
		asset.DiskUsageRateSource[sourceKey] = data.DiskUsageRate
		asset.LoadAverageSource[sourceKey] = data.LoadAverage
		asset.PortsSource[sourceKey] = data.Ports
		asset.StatusSource[sourceKey] = data.Status
		asset.JarPackageInfoSource[sourceKey] = data.JarPackageInfo

		key := fmt.Sprintf("%d", data.Source)
		if existing, found := latestData[key]; found {
			if data.CreatedAt.After(*existing.UpdatedAt) {
				latestData[key] = data
			}
		} else {
			latestData[key] = data
		}
	}
	allSourceIds = utils.ListDistinct(allSourceIds)
	allNodeIds = utils.ListDistinct(allNodeIds)
	allTaskDataIds = utils.ListDistinct(allTaskDataIds)
	allProcessIds = utils.ListDistinct(allProcessIds)
	asset.AllSourceIds = allSourceIds
	asset.AllNodeIds = allNodeIds
	asset.AllTaskDataIds = allTaskDataIds
	asset.AllProcessIds = allProcessIds

	// 基础字段赋值
	asset.Ip = e.SourceData[0].Ip
	asset.Area = e.SourceData[0].Area
	// 基础融合字段处理
	fieldValInfoList := frame.AssetFieldProcess(asset, e.Strategy, latestData)

	// 触发事件-单条资产融合
	err := event.NewEventBus().Emit(event.Event_Asset_MergeData, asset, e.Strategy, e.SourceData, latestData)
	if err != nil {
		logger.Errorf("触发事件-单条资产融合失败,err:%v", err)
	}

	return asset, fieldValInfoList
}

// Execute 漏洞策略执行
func (e *VulnStrategyExecutor) Execute() (*esmodel_vuln.Poc, []*es_model.FieldValInfo) {
	logger := logs.GetLogger("vuln")
	allSourceIds := make([]uint64, 0)
	allNodeIds := make([]uint64, 0)
	allTaskDataIds := make([]string, 0)
	allProcessIds := make([]string, 0)
	poc := &esmodel_vuln.Poc{
		SourceIds:            make([]uint64, 0),
		NodeIds:              make([]uint64, 0),
		TaskDataIds:          make([]string, 0),
		ProcessIds:           make([]string, 0),
		IsPocSource:          make(map[string]int),
		IpSource:             make(map[string]string),
		PortSource:           make(map[string]int),
		LevelSource:          make(map[string]int),
		NameSource:           make(map[string]string),
		CveSource:            make(map[string]string),
		CnvdSource:           make(map[string]string),
		CnnvdSource:          make(map[string]string),
		HasExpSource:         make(map[string]int),
		HasPocSource:         make(map[string]int),
		StatusSource:         make(map[string]interface{}),
		OriginalIdsSource:    make(map[string]string),
		LastResponseAtSource: make(map[string]string),
	}
	// 同源不同节点数据，取最新的数据，其他数据抛弃
	// 存储每个源节点的最新数据
	latestData := make(map[string]*esmodel_vuln.ProcessPoc)
	for _, data := range e.SourceData {
		allSourceIds = append(allSourceIds, data.Source)
		allNodeIds = append(allNodeIds, data.Node)
		allTaskDataIds = append(allTaskDataIds, data.TaskDataId)
		allProcessIds = append(allProcessIds, data.Id)

		sourceKey := fmt.Sprintf("%d-%d", data.Source, data.Node)
		poc.IsPocSource[sourceKey] = data.IsPoc
		poc.IpSource[sourceKey] = data.Ip
		poc.PortSource[sourceKey] = data.Port
		poc.LevelSource[sourceKey] = data.Level
		poc.NameSource[sourceKey] = data.Name
		poc.CveSource[sourceKey] = data.Cve
		poc.CnvdSource[sourceKey] = data.Cnvd
		poc.CnnvdSource[sourceKey] = data.Cnnvd
		poc.HasExpSource[sourceKey] = data.HasExp
		poc.HasPocSource[sourceKey] = data.HasPoc
		poc.StatusSource[sourceKey] = data.Status
		poc.OriginalIdsSource[sourceKey] = data.OriginalId
		poc.LastResponseAtSource[sourceKey] = data.LastResponseAt.String()
		// 原始任务id不需要策略，直接补充
		poc.OriginalIds = append(poc.OriginalIds, data.OriginalId)

		key := fmt.Sprintf("%d", data.Source)
		if existing, found := latestData[key]; found {
			if existing.UpdatedAt != nil {
				if data.CreatedAt.After(*existing.UpdatedAt) {
					latestData[key] = data
				}
			} else if existing.CreatedAt != nil {
				if data.CreatedAt.After(*existing.CreatedAt) {
					latestData[key] = data
				}
			}
		} else {
			latestData[key] = data
		}
	}
	allSourceIds = utils.ListDistinct(allSourceIds)
	allNodeIds = utils.ListDistinct(allNodeIds)
	allTaskDataIds = utils.ListDistinct(allTaskDataIds)
	allProcessIds = utils.ListDistinct(allProcessIds)

	poc.AllSourceIds = allSourceIds
	poc.AllNodeIds = allNodeIds
	poc.AllTaskDataIds = allTaskDataIds
	poc.AllProcessIds = allProcessIds

	// 漏洞融合字段处理
	fieldValInfoList := frame.VulnFieldProcess(poc, e.Strategy, latestData)

	// 触发事件-单条漏洞融合
	err := event.NewEventBus().Emit(event.Event_Vuln_MergeData, poc, e.Strategy, e.SourceData, latestData)
	if err != nil {
		logger.Errorf("触发事件-单条漏洞融合失败,err:%v", err)
	}

	return poc, fieldValInfoList
}

// Execute 人员策略执行
func (e *PersonStrategyExecutor) Execute() (*esmodel_person.Staff, []*es_model.FieldValInfo) {
	logger := logs.GetLogger("person")
	allSourceIds := make([]uint64, 0)
	allNodeIds := make([]uint64, 0)
	allTaskDataIds := make([]string, 0)
	allProcessIds := make([]string, 0)
	allOriginalIds := make([]string, 0)
	staff := &esmodel_person.Staff{
		SourceIds:         make([]uint64, 0),
		NodeIds:           make([]uint64, 0),
		TaskDataIds:       make([]string, 0),
		ProcessIds:        make([]string, 0),
		AreaSource:        make(map[string]int, 0),
		EnglishNameSource: make(map[string]string, 0),
		TitleSource:       make(map[string]string, 0),
		EmailSource:       make(map[string]string, 0),
		DepartmentSource:  make(map[string]string, 0),
		WorkNumberSource:  make(map[string]string, 0),
		StatusSource:      make(map[string]int, 0),
		OriginalIdsSource: make(map[string]string, 0),
	}
	// 同源不同节点数据，取最新的数据，其他数据抛弃
	// 存储每个源节点的最新数据
	latestData := make(map[string]*esmodel_person.ProcessStaff)
	for _, data := range e.SourceData {
		allSourceIds = append(allSourceIds, data.Source)
		allNodeIds = append(allNodeIds, data.Node)
		allTaskDataIds = append(allTaskDataIds, data.TaskDataId)
		allProcessIds = append(allProcessIds, data.Id)
		allOriginalIds = append(allOriginalIds, data.OriginalId)

		sourceKey := fmt.Sprintf("%d-%d", data.Source, data.Node)
		staff.AreaSource[sourceKey] = data.Area
		staff.EnglishNameSource[sourceKey] = data.EnglishName
		staff.TitleSource[sourceKey] = data.Title
		staff.EmailSource[sourceKey] = data.Email
		staff.DepartmentSource[sourceKey] = data.Department
		staff.StatusSource[sourceKey] = data.Status
		staff.WorkNumberSource[sourceKey] = data.WorkNumber
		staff.OriginalIdsSource[sourceKey] = data.OriginalId

		// todo 单点登录字段补充，只有隆基会上报这个字段
		staff.SsoId = func() string {
			if data.SsoId != "" {
				return data.SsoId
			}
			return ""
		}()
		staff.SsoName = func() string {
			if data.SsoName != "" {
				return data.SsoName
			}
			return ""
		}()

		key := fmt.Sprintf("%d", data.Source)
		if existing, found := latestData[key]; found {
			if existing.UpdatedAt != nil && data.CreatedAt.After(*existing.UpdatedAt) {
				latestData[key] = data
			}
		} else {
			latestData[key] = data
		}
	}
	allSourceIds = utils.ListDistinct(allSourceIds)
	allNodeIds = utils.ListDistinct(allNodeIds)
	allTaskDataIds = utils.ListDistinct(allTaskDataIds)
	allProcessIds = utils.ListDistinct(allProcessIds)
	allOriginalIds = utils.ListDistinct(allOriginalIds)

	staff.AllSourceIds = allSourceIds
	staff.AllNodeIds = allNodeIds
	staff.AllTaskDataIds = allTaskDataIds
	staff.AllProcessIds = allProcessIds
	staff.OriginalIds = allOriginalIds

	// 人员融合字段处理
	fieldValInfoList := frame.PersonFieldProcess(staff, e.Strategy, latestData)

	// 触发事件-单条人员融合
	err := event.NewEventBus().Emit(event.Event_Person_MergeData, staff, e.Strategy, e.SourceData, latestData)
	if err != nil {
		logger.Errorf("触发事件-单条人员融合失败,err:%v", err)
	}

	return staff, fieldValInfoList
}

// Execute 设备策略执行
func (e *DeviceStrategyExecutor) Execute() (*esmodel_device.Device, []*es_model.FieldValInfo) {
	logger := logs.GetLogger("device")
	allSourceIds := make([]uint64, 0)
	allNodeIds := make([]uint64, 0)
	allTaskDataIds := make([]string, 0)
	allProcessIds := make([]string, 0)
	device := &esmodel_device.Device{
		SourceIds:             make([]uint64, 0),
		NodeIds:               make([]uint64, 0),
		TaskDataIds:           make([]string, 0),
		ProcessIds:            make([]string, 0),
		AreaSource:            make(map[string]int),
		IpSource:              make(map[string]string),
		PrivateIpSource:       make(map[string][]string),
		PublicIpSource:        make(map[string][]string),
		OperSource:            make(map[string]string),
		HostNameSource:        make(map[string]string),
		NetworkCards:          make([]*esmodel_device.NetworkCardInfo, 0),
		OsSource:              make(map[string]string),
		KernelSource:          make(map[string]string),
		ModelSource:           make(map[string]string),
		MakerSource:           make(map[string]string),
		SnSource:              make(map[string]string),
		MacSource:             make(map[string]string),
		MachineRoomSource:     make(map[string]string),
		MemorySizeSource:      make(map[string]string),
		MemoryUsageRateSource: make(map[string]string),
		CpuMakerSource:        make(map[string]string),
		CpuBrandSource:        make(map[string]string),
		CpuCountSource:        make(map[string]int),
		DiskCountSource:       make(map[string]int),
		DiskSizeSource:        make(map[string]int),
		DiskUsageRateSource:   make(map[string]string),
		LoadAverageSource:     make(map[string]string),
	}
	// 同源不同节点数据，取最新的数据，其他数据抛弃
	// 存储每个源节点的最新数据
	latestData := make(map[string]*esmodel_device.ProcessDevice)
	// 遍历源数据
	for _, data := range e.SourceData {
		allSourceIds = append(allSourceIds, data.Source)
		allNodeIds = append(allNodeIds, data.Node)
		allTaskDataIds = append(allTaskDataIds, data.TaskDataId)
		allProcessIds = append(allProcessIds, data.Id)

		sourceKey := fmt.Sprintf("%d-%d", data.Source, data.Node)
		device.AreaSource[sourceKey] = data.Area
		device.PrivateIpSource[sourceKey] = data.PrivateIp
		device.PublicIpSource[sourceKey] = data.PublicIp
		device.OperSource[sourceKey] = data.Oper
		device.HostNameSource[sourceKey] = data.HostName
		device.OsSource[sourceKey] = data.Os
		device.KernelSource[sourceKey] = data.Kernel
		device.ModelSource[sourceKey] = data.Model
		device.MakerSource[sourceKey] = data.Maker
		device.SnSource[sourceKey] = data.Sn
		device.MacSource[sourceKey] = data.Mac
		device.MachineRoomSource[sourceKey] = data.MachineRoom
		device.MemorySizeSource[sourceKey] = data.MemorySize
		device.MemoryUsageRateSource[sourceKey] = data.MemoryUsageRate
		device.CpuMakerSource[sourceKey] = data.CpuMaker
		device.CpuBrandSource[sourceKey] = data.CpuBrand
		device.CpuCountSource[sourceKey] = data.CpuCount
		device.DiskCountSource[sourceKey] = data.DiskCount
		device.DiskSizeSource[sourceKey] = data.DiskSize
		device.DiskUsageRateSource[sourceKey] = data.DiskUsageRate
		device.LoadAverageSource[sourceKey] = data.LoadAverage

		key := fmt.Sprintf("%d", data.Source)
		if existing, found := latestData[key]; found {
			if data.CreatedAt != nil && existing.UpdatedAt != nil && data.CreatedAt.After(*existing.UpdatedAt) {
				latestData[key] = data
			}
		} else {
			latestData[key] = data
		}
	}
	allSourceIds = utils.ListDistinctNonZero(allSourceIds)
	allNodeIds = utils.ListDistinctNonZero(allNodeIds)
	allTaskDataIds = utils.ListDistinctNonZero(allTaskDataIds)
	allProcessIds = utils.ListDistinctNonZero(allProcessIds)

	// 设备融合字段处理
	fieldValInfoList := frame.DeviceFieldProcess(device, e.Strategy, latestData)

	device.AllSourceIds = allSourceIds
	device.AllNodeIds = allNodeIds
	device.AllTaskDataIds = allTaskDataIds
	device.AllProcessIds = allProcessIds
	device.SourceIds = utils.ListDistinctNonZero(device.SourceIds)
	device.NodeIds = utils.ListDistinctNonZero(device.NodeIds)
	device.TaskDataIds = utils.ListDistinctNonZero(device.TaskDataIds)
	device.ProcessIds = utils.ListDistinctNonZero(device.ProcessIds)

	// 触发事件-单条设备融合
	err := event.NewEventBus().Emit(event.Event_Device_MergeData, device, e.Strategy, e.SourceData, latestData)
	if err != nil {
		logger.Errorf("触发事件-单条设备融合失败,err:%v", err)
	}

	return device, fieldValInfoList
}
