package model

import (
	"testing"

	"github.com/agiledragon/gomonkey/v2"
)

func TestManualCalibrationModel_Check(t *testing.T) {
	tests := []struct {
		name       string
		model      ManualCalibrationModel
		values     map[string]string
		allowKeys  []string
		customKeys []string
		wantErr    bool
	}{
		{
			name: "有效输入",
			model: ManualCalibrationModel{
				Ids: []string{"1", "2", "3"},
			},
			values:     map[string]string{"key1": "value1", "key2": "value2", "custom_fields": `{"custom1":"值1","custom2":"值2"}`},
			allowKeys:  []string{"key1", "key2", "key3"},
			customKeys: []string{"custom1", "custom2"},
			wantErr:    false,
		},
		{
			name: "Ids为空",
			model: ManualCalibrationModel{
				Ids: []string{},
			},
			values:     map[string]string{"key1": "value1"},
			allowKeys:  []string{"key1"},
			customKeys: []string{"custom1", "custom2"},
			wantErr:    true,
		},
		{
			name: "Values为空",
			model: ManualCalibrationModel{
				Ids: []string{"1", "2", "3"},
			},
			values:     map[string]string{},
			allowKeys:  []string{"key1"},
			customKeys: []string{"custom1", "custom2"},
			wantErr:    true,
		},
		{
			name: "不合法的key",
			model: ManualCalibrationModel{
				Ids: []string{"1", "2", "3"},
			},
			values:     map[string]string{"key1": "value1", "invalidKey": "value2"},
			allowKeys:  []string{"key1", "key2"},
			customKeys: []string{"custom1", "custom2"},
			wantErr:    true,
		},
		{
			name: "存在多余的自定义字段",
			model: ManualCalibrationModel{
				Ids: []string{"1", "2", "3"},
			},
			values:     map[string]string{"custom_fields": `{"custom1":"值1","custom2":"值2"}`},
			allowKeys:  []string{"key1", "key2"},
			customKeys: []string{"custom1"},
			wantErr:    false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.model.Check(tt.allowKeys, tt.customKeys, tt.model.Ids, tt.values)
			if (err != nil) != tt.wantErr {
				t.Errorf("ManualCalibrationModel.Check() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestConvertValue(t *testing.T) {
	gomonkey.ApplyMethodReturn(&ManualCalibrationModel{}, "GetCustomKeys", []string{"custom1", "custom2"})

	m := &ManualCalibrationModel{BusinessType: "vuln"}
	input := map[string]string{
		"port":          "80",
		"level":         "2",
		"name":          "测试漏洞",
		"custom_fields": `{"custom1":"值1","custom2":"值2"}`,
	}
	values, sources, err := m.ConvertValue(input)
	if err != nil {
		t.Errorf("ConvertValue error: %v", err)
	}
	if values["port"] != 80 {
		t.Errorf("expected port=80, got %v", values["port"])
	}
	if sources["name_source"] != "测试漏洞" {
		t.Errorf("expected name_source=测试漏洞, got %v", sources["name_source"])
	}
}

func TestConvertCustomValues(t *testing.T) {
	customValue := `{"custom1":"值1","custom2":"值2"}`
	allCustomKeys := []string{"custom1", "custom2", "custom3"}
	result := ConvertCustomValues(customValue, allCustomKeys)
	if result["custom1"] != "值1" {
		t.Errorf("custom1 error")
	}
	if result["custom2"] != "值2" {
		t.Errorf("custom2 error")
	}
	if result["custom3"] != "" {
		t.Errorf("custom3 should be empty string")
	}
}
