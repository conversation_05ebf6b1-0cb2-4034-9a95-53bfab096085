package model

import (
	"reflect"
	"testing"
	"time"

	esmodel "fobrain/models/elastic/device"
	esmodel_person "fobrain/models/elastic/staff"

	"github.com/agiledragon/gomonkey/v2"
)

func TestDeviceCalibrationModel_GetDisplayName(t *testing.T) {
	m := &DeviceCalibrationModel{}
	tests := []struct {
		name string
		key  string
		want string
	}{
		{"机房", "machine_room", "机房"},
		{"运维人员", "oper", "运维人员"},
		{"未知键", "unknown", ""},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := m.GetDisplayName(tt.key); got != tt.want {
				t.<PERSON>("DeviceCalibrationModel.GetDisplayName() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestDeviceCalibrationModel_ConvertValue(t *testing.T) {
	m := &DeviceCalibrationModel{}
	tests := []struct {
		name        string
		input       map[string]string
		wantValues  map[string]interface{}
		wantSources map[string]interface{}
		wantErr     bool
	}{
		{
			name: "有效输入",
			input: map[string]string{
				"machine_room": `["room1", "room2"]`,
				"oper":         `["1"]`,
			},
			wantValues: map[string]interface{}{
				"machine_room": []string{"room1", "room2"},
				"oper":         []string{"oper1"},
			},
			wantSources: map[string]interface{}{
				"machine_room_source": "room1,room2",
				"oper_source":         "oper1",
			},
			wantErr: false,
		},
		{
			name: "无效JSON",
			input: map[string]string{
				"machine_room": `["room1", "room2"`,
			},
			wantValues:  nil,
			wantSources: nil,
			wantErr:     true,
		},
		{
			name:        "空输入",
			input:       map[string]string{},
			wantValues:  map[string]interface{}{},
			wantSources: map[string]interface{}{},
			wantErr:     false,
		},
	}

	// defer gomonkey.ApplyMethod(esmodel_person.NewStaff(), "GetById", func(ctx context.Context, id string) (*esmodel_person.Staff, error) {
	// 	if id == "1" {
	// 		return &esmodel_person.Staff{
	// 			Name: "oper1",
	// 		}, nil
	// 	}
	// 	if id == "2" {
	// 		return &esmodel_person.Staff{
	// 			Name: "oper2",
	// 		}, nil
	// 	}
	// 	return nil, nil
	// }).Reset()
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyMethodReturn(esmodel_person.NewStaff(), "GetById", &esmodel_person.Staff{Name: "oper1"}, nil).Reset()

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotValues, gotSources, err := m.ConvertValue(tt.input)
			if (err != nil) != tt.wantErr {
				t.Errorf("DeviceCalibrationModel.ConvertValue() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotValues, tt.wantValues) {
				t.Errorf("DeviceCalibrationModel.ConvertValue() gotValues = %v, want %v", gotValues, tt.wantValues)
			}
			if !reflect.DeepEqual(gotSources, tt.wantSources) {
				t.Errorf("DeviceCalibrationModel.ConvertValue() gotSources = %v, want %v", gotSources, tt.wantSources)
			}
		})
	}
}

func TestDeviceCalibrationModel_UpdateMergeResult(t *testing.T) {
	m := &DeviceCalibrationModel{}

	tests := []struct {
		name         string
		mergeResult  *esmodel.Device
		originalData *esmodel.Device
		wantErr      bool
		wantResult   *esmodel.Device
	}{
		{
			name:         "空输入",
			mergeResult:  nil,
			originalData: nil,
			wantErr:      true,
			wantResult:   nil,
		},
		{
			name: "更新机房和运维人员",
			mergeResult: &esmodel.Device{
				MachineRoom: []string{"oldRoom"},
				Oper:        []string{"oldOper"},
			},
			originalData: &esmodel.Device{
				MachineRoom:       []string{"newRoom"},
				MachineRoomSource: map[string]string{"0": "newRoomSource"},
				Oper:              []string{"newOper"},
				OperSource:        map[string]string{"0": "newOperSource"},
			},
			wantErr: false,
			wantResult: &esmodel.Device{
				MachineRoom:       []string{"newRoom"},
				MachineRoomSource: map[string]string{"0": "newRoomSource"},
				Oper:              []string{"newOper"},
				OperSource:        map[string]string{"0": "newOperSource"},
				AllSourceIds:      []uint64{0},
				SourceIds:         []uint64{0},
			},
		},
		{
			name: "只更新机房",
			mergeResult: &esmodel.Device{
				MachineRoom: []string{"oldRoom"},
				Oper:        []string{"oldOper"},
			},
			originalData: &esmodel.Device{
				MachineRoom:       []string{"newRoom"},
				MachineRoomSource: map[string]string{"0": "newRoomSource"},
			},
			wantErr: false,
			wantResult: &esmodel.Device{
				MachineRoom:       []string{"newRoom"},
				MachineRoomSource: map[string]string{"0": "newRoomSource"},
				Oper:              []string{"oldOper"},
				AllSourceIds:      []uint64{0},
				SourceIds:         []uint64{0},
			},
		},
		{
			name: "无更新",
			mergeResult: &esmodel.Device{
				MachineRoom: []string{"oldRoom"},
				Oper:        []string{"oldOper"},
			},
			originalData: &esmodel.Device{},
			wantErr:      false,
			wantResult: &esmodel.Device{
				MachineRoom: []string{"oldRoom"},
				Oper:        []string{"oldOper"},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := m.UpdateMergeResult(tt.mergeResult, tt.originalData)
			if (err != nil) != tt.wantErr {
				t.Errorf("DeviceCalibrationModel.UpdateMergeResult() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !tt.wantErr && !reflect.DeepEqual(tt.mergeResult, tt.wantResult) {
				t.Errorf("DeviceCalibrationModel.UpdateMergeResult() got = %v, want %v", tt.mergeResult, tt.wantResult)
			}
		})
	}
}

func TestGetDeviceAllowKeys(t *testing.T) {
	expectedKeys := []string{
		"machine_room",
		"oper",
		"tag",
		"tags",
	}

	keys := GetDeviceAllowKeys()
	if !reflect.DeepEqual(keys, expectedKeys) {
		t.Errorf("Expected keys %v, got %v", expectedKeys, keys)
	}
}
