package model

import (
	"context"
	"encoding/json"
	"errors"
	"strings"

	esmodel "fobrain/models/elastic/device"
	esmodel_person "fobrain/models/elastic/staff"
)

type DeviceCalibrationModel struct {
	ManualCalibrationModel
}

var (
	deviceAllowKeys = []string{
		"machine_room",
		"oper",
		"tag",
		"tags",
	}
)

func GetDeviceAllowKeys() []string {
	return deviceAllowKeys
}

func (m *DeviceCalibrationModel) GetDisplayName(key string) string {
	switch key {
	case "machine_room":
		return "机房"
	case "oper":
		return "运维人员"
	case "tag":
		return "标签"
	}
	return ""
}

func (m *DeviceCalibrationModel) ConvertValue(input map[string]string) (map[string]interface{}, map[string]interface{}, error) {
	values := make(map[string]interface{})
	valuesSource := make(map[string]interface{})
	// 把input的值反序列化为Device对象，用来检查传入的参数格式是否正确
	device := &esmodel.Device{}
	// 获取传入的input中的 key
	for key, value := range input {
		switch key {
		case "machine_room":
			err := json.Unmarshal([]byte(value), &device.MachineRoom)
			if err != nil {
				return nil, nil, err
			}
			values[key] = device.MachineRoom
			valuesSource[key+"_source"] = strings.Join(device.MachineRoom, ",")
		case "oper":
			err := json.Unmarshal([]byte(value), &device.Oper)
			if err != nil {
				return nil, nil, err
			}
			operNameList := make([]string, 0)
			for _, oper := range device.Oper {
				staff, err := esmodel_person.NewStaff().GetById(context.Background(), oper)
				if err != nil {
					continue
				}
				if staff != nil {
					operNameList = append(operNameList, staff.Name)
				}
			}
			values[key] = operNameList
			valuesSource[key+"_source"] = strings.Join(operNameList, ",")
		case "tag":
			err := json.Unmarshal([]byte(value), &device.Tags)
			if err != nil {
				return nil, nil, err
			}
			values["tag"] = device.Tags
			valuesSource["tag_source"] = strings.Join(device.Tags, ",")
		case "custom_fields":
			customValues := ConvertCustomValues(value, m.GetCustomKeys())
			if len(customValues) > 0 {
				values[key] = customValues
				valuesSource[key+"_source"] = customValues
			}
		}
	}
	return values, valuesSource, nil
}

// 使用人工校准数据更新融合结果
// mergeResult: 当前融合结果
// originalData: 原始数据，人工校准数据
func (m *DeviceCalibrationModel) UpdateMergeResult(mergeResult *esmodel.Device, originalData *esmodel.Device) error {
	if mergeResult == nil || originalData == nil {
		return errors.New("mergeResult或originalData为空")
	}

	// 是否存在被覆盖的数据
	exist := false

	// 更新machine_room
	if len(originalData.MachineRoomSource) > 0 {
		machineRoom, ok := originalData.MachineRoomSource["0"]
		if ok {
			exist = true
			mergeResult.MachineRoom = originalData.MachineRoom
			if len(mergeResult.MachineRoomSource) == 0 {
				mergeResult.MachineRoomSource = make(map[string]string)
			}
			mergeResult.MachineRoomSource["0"] = machineRoom
		}
	}

	// 更新oper
	if len(originalData.OperSource) > 0 {
		oper, ok := originalData.OperSource["0"]
		if ok {
			exist = true
			mergeResult.Oper = originalData.Oper
			if len(mergeResult.OperSource) == 0 {
				mergeResult.OperSource = make(map[string]string)
			}
			mergeResult.OperSource["0"] = oper
		}
	}

	// 更新tag
	if len(originalData.Tags) > 0 {
		tag, ok := originalData.TagsSource["0"]
		if ok {
			exist = true
			mergeResult.Tags = strings.Split(tag, ",")
			if len(mergeResult.TagsSource) == 0 {
				mergeResult.TagsSource = make(map[string]string)
			}
			mergeResult.TagsSource["0"] = tag
		}
	}

	// 如果存在被覆盖的数据，则更新 all_source_ids 和 source_ids
	if exist {
		if len(mergeResult.AllSourceIds) == 0 {
			mergeResult.AllSourceIds = make([]uint64, 0)
		}
		if len(mergeResult.SourceIds) == 0 {
			mergeResult.SourceIds = make([]uint64, 0)
		}
		mergeResult.AllSourceIds = append(mergeResult.AllSourceIds, 0)
		mergeResult.SourceIds = append(mergeResult.SourceIds, 0)
	}

	return nil
}
