package model

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	logs "fobrain/mergeService/utils/log"
	"fobrain/models/elastic/assets"
	business_system2 "fobrain/models/elastic/business_system"
	"fobrain/models/mysql/personnel_departments"
	"slices"
	"strconv"
	"strings"

	esmodel_person "fobrain/models/elastic/staff"
)

type AssetManualCalibration struct {
	ManualCalibrationModel
}

var (
	// 内网资产：业务系统、运维人员、业务系统负责人、机房、 型号、制造商
	// 外网资产：业务系统、运维人员、业务系统负责人、机房、 型号、制造商
	assetAllowKeys = []string{
		"business",
		"business_department",
		"oper",
		"oper_info",
		"oper_department",
		"oper_with_mapping",
		"machine_room",
		"model",
		"maker",
		"tag",
		"business_staff_ids",
		"business_department_ids",
		"oper_staff_ids",
		"oper_department_ids",
	}
)

func GetAssetAllowKeys() []string {
	return assetAllowKeys
}

func (m *AssetManualCalibration) GetDisplayName(key string) string {
	switch key {
	case "business":
		return "业务系统"
	case "business_department":
		return "业务系统部门"
	case "oper":
		return "运维人员"
	case "oper_department":
		return "运维部门"
	case "machine_room":
		return "机房"
	case "model":
		return "型号"
	case "maker":
		return "制造商"
	case "tag":
		return "标签"
	}
	return ""
}

func (m *AssetManualCalibration) ConvertValue(input map[string]string) (map[string]interface{}, map[string]interface{}, error) {
	values := make(map[string]interface{})
	valuesSource := make(map[string]interface{})
	// 把input的值反序列化为Asset对象，用来检查传入的参数格式是否正确
	asset := &assets.Assets{}

	// 获取部门信息
	allDepartments, _, err := personnel_departments.NewPersonnelDepartmentsModel().List(0, 0)
	if err != nil {
		return nil, nil, fmt.Errorf("获取部门信息失败. err: %v", err)
	}

	// 获取传入的input中的 key
	for key, value := range input {
		switch key {
		case "business":
			type businessStruct struct {
				Business   string   `json:"business_id"`
				Department []uint64 `json:"department"`
			}
			businessInput := make([]businessStruct, 0)
			err = json.Unmarshal([]byte(value), &businessInput)
			if err != nil {
				return nil, nil, err
			}

			businessDepartment := make([]*assets.DepartmentBase, 0)
			system := make([]string, 0)
			owner := make([]string, 0)
			businessOwnerIds := make([]string, 0)
			businessDepartmentIds := make([]uint64, 0)
			departmentIds := make([]uint64, 0)
			for _, value := range businessInput {
				currentDepartment := make([]*assets.DepartmentBase, 0)
				business := &assets.Business{
					PersonBase: make([]*assets.PersonBase, 0),
				}
				// 如果business.SystemId不为空，则获取业务系统信息
				if value.Business != "" {
					bs, err := business_system2.NewBusinessSystems().GetAssetBusinessStructById(context.Background(), value.Business)
					if err != nil {
						continue
					}
					asset.Business = append(asset.Business, bs)
				} else if len(value.Department) > 0 {
					for _, departmentId := range value.Department {
						dep := getDeparment(allDepartments, departmentId, "", "", "", "")
						if dep != nil {
							if !slices.Contains(departmentIds, dep.Id) {
								currentDepartment = append(currentDepartment, dep)
								departmentIds = append(departmentIds, dep.Id)
							}
						}
					}
					personBase := &assets.PersonBase{
						Name:       "",
						Id:         "",
						Fid:        "",
						FindInfo:   []*assets.PersonFindInfo{},
						Department: currentDepartment,
					}
					business.PersonBase = append(business.PersonBase, personBase)
					asset.Business = append(asset.Business, business)
				}
			}
			values[key] = asset.Business
			valuesSource[key+"_source"] = asset.Business
			for _, business := range asset.Business {
				for _, person := range business.PersonBase {
					businessOwnerIds = append(businessOwnerIds, person.Id)
					for _, department := range person.Department {
						businessDepartmentIds = append(businessDepartmentIds, department.Id)
					}
				}
				businessDepartment = append(businessDepartment, business.DepartmentBase...)
			}
			values["business_department"] = businessDepartment
			valuesSource["business_system_source"] = strings.Join(system, ",")
			valuesSource["business_owner_source"] = strings.Join(owner, ",")
			values["business_staff_ids"] = businessOwnerIds
			values["business_department_ids"] = businessDepartmentIds
		case "oper":
			type operStruct struct {
				Oper       string   `json:"oper_id"`
				Department []uint64 `json:"department"`
			}
			operInput := make([]operStruct, 0)
			err := json.Unmarshal([]byte(value), &operInput)
			if err != nil {
				return nil, nil, err
			}
			operNameList := make([]string, 0)
			operWithMappingList := make([]*assets.PersonWithMapping, 0)
			operInfoList := make([]*assets.PersonBase, 0)
			operDepartment := make([]*assets.DepartmentBase, 0)
			operIds := make([]string, 0)
			operDepartmentIds := make([]uint64, 0)
			for _, oper := range operInput {
				currentDepartment := make([]*assets.DepartmentBase, 0)
				if oper.Oper != "" {
					staff, err := esmodel_person.NewStaff().GetById(context.Background(), oper.Oper)
					if err != nil {
						continue
					}
					if staff != nil {
						personBase := &assets.PersonBase{
							Name: staff.Name,
							Id:   staff.Id,
							Fid:  staff.Fid,
							FindInfo: []*assets.PersonFindInfo{
								{
									SourceId:     0, // 0表示人工校准
									NodeId:       0, // 0表示人工校准
									SourceValue:  oper.Oper,
									MappingField: "id", // 人工校准一定是从台账选择，所以mapping_field是id
									FindCount:    1,
								},
							},
							Department: make([]*assets.DepartmentBase, 0),
						}
						operNameList = append(operNameList, staff.Name)
						operIds = append(operIds, staff.Id)
						for _, departmentId := range staff.DepartmentsIds {
							// string 类型的departmentId转uint64类型
							departmentId, err := strconv.ParseUint(departmentId, 10, 64)
							if err != nil {
								logs.GetLogger("asset").Warnf("将string类型的departmentId转uint64类型失败. err: %v", err)
								continue
							}
							operDepartmentIds = append(operDepartmentIds, departmentId)
							// 根据部门id获取部门name，包括父级部门的Id+Name
							depa := getDeparment(allDepartments, departmentId, staff.Name, staff.Id, "", "")
							if depa != nil {
								operDepartment = append(operDepartment, depa)
								currentDepartment = append(currentDepartment, depa)
							}
						}
						personBase.Department = currentDepartment
						operInfoList = append(operInfoList, personBase)
						operWithMappingList = append(operWithMappingList, &assets.PersonWithMapping{
							SourceId:     0, // 0表示人工校准
							NodeId:       0, // 0表示人工校准
							SourceValue:  oper.Oper,
							MappingField: "id", // 人工校准一定是从台账选择，所以mapping_field是id
						})
					}
				} else if len(oper.Department) > 0 {
					for _, departmentId := range oper.Department {
						dep := getDeparment(allDepartments, departmentId, "", "", "", "")
						if dep != nil {
							operDepartment = append(operDepartment, dep)
							currentDepartment = append(currentDepartment, dep)
						}
					}
					personBase := &assets.PersonBase{
						Name:       "",
						Id:         "",
						Fid:        "",
						FindInfo:   []*assets.PersonFindInfo{},
						Department: currentDepartment,
					}
					operInfoList = append(operInfoList, personBase)
				}
			}
			values["oper_info"] = operInfoList
			values["oper_department"] = operDepartment
			values["oper_with_mapping"] = operWithMappingList
			values[key] = operNameList
			valuesSource[key+"_source"] = strings.Join(operNameList, ",")
			values["oper_staff_ids"] = operIds
			values["oper_department_ids"] = operDepartmentIds
		case "machine_room":
			err := json.Unmarshal([]byte(value), &asset.MachineRoom)
			if err != nil {
				return nil, nil, err
			}
			values[key] = asset.MachineRoom
			valuesSource[key+"_source"] = strings.Join(asset.MachineRoom, ",")
		case "model":
			err := json.Unmarshal([]byte(value), &asset.Model)
			if err != nil {
				return nil, nil, err
			}
			values[key] = asset.Model
			valuesSource[key+"_source"] = strings.Join(asset.Model, ",")
		case "maker":
			err := json.Unmarshal([]byte(value), &asset.Maker)
			if err != nil {
				return nil, nil, err
			}
			values[key] = asset.Maker
			valuesSource[key+"_source"] = strings.Join(asset.Maker, ",")
		case "tag":
			err := json.Unmarshal([]byte(value), &asset.Tags)
			if err != nil {
				return nil, nil, err
			}
			values[key] = asset.Tags
			valuesSource[key+"_source"] = strings.Join(asset.Tags, ",")
		case "custom_fields":
			customValues := ConvertCustomValues(value, m.GetCustomKeys())
			if len(customValues) > 0 {
				values[key] = customValues
				valuesSource[key+"_source"] = customValues
			}
		}
	}

	return values, valuesSource, nil
}

// getDeparment 根据部门id获取部门信息
func getDeparment(allDepartments []*personnel_departments.PersonnelDepartments, departmentId uint64, personName string, personId string, businessName string, businessId string) *assets.DepartmentBase {
	if len(allDepartments) == 0 {
		return nil
	}
	// Map 存储所有部门信息，快速查找
	departmentMap := make(map[uint64]*personnel_departments.PersonnelDepartments)
	for _, department := range allDepartments {
		departmentMap[department.Id] = department
	}

	// 查找目标部门
	targetDepartment, exists := departmentMap[departmentId]
	if !exists {
		return nil
	}

	result := &assets.DepartmentBase{
		BusinessSystemId:   businessId,
		BusinessSystemName: businessName,
		UserId:             personId,
		UserName:           personName,
		Id:                 targetDepartment.Id,
		Name:               targetDepartment.FullName,
	}
	// 寻找父级部门
	currentParentId := targetDepartment.ParentId
	for currentParentId != 0 {
		parent, exists := departmentMap[currentParentId]
		if !exists {
			break // 父级部门不存在，退出循环
		}
		// 将父级部门添加到结果列表
		result.Parents = append(result.Parents, &assets.DepartmentBase{
			Id:   parent.Id,
			Name: parent.FullName,
		})
		currentParentId = parent.ParentId // Move to the next parent
	}
	return result
}

// 使用人工校准数据更新融合结果
// mergeResult: 当前融合结果
// originalData: 原始数据，人工校准数据
// 融合后处理，处理tag等
func (m *AssetManualCalibration) UpdateMergeResult(mergeResult *assets.Assets, originalData *assets.Assets) error {
	if mergeResult == nil || originalData == nil {
		return errors.New("mergeResult或originalData为空")
	}

	// 是否存在被覆盖的数据
	exist := false

	// 更新business
	if len(originalData.BusinessSource) > 0 {
		exist = updateUserTags(mergeResult, originalData)
	}

	// 更新oper
	if len(originalData.OperSource) > 0 {
		oper, ok := originalData.OperSource["0"]
		if ok {
			exist = true
			mergeResult.OperInfo = originalData.OperInfo
			mergeResult.OperDepartment = originalData.OperDepartment
			if len(mergeResult.OperSource) == 0 {
				mergeResult.OperSource = make(map[string]string)
			}
			mergeResult.OperSource["0"] = oper
		}
	}

	// 更新machine_room
	if len(originalData.MachineRoomSource) > 0 {
		machineRoom, ok := originalData.MachineRoomSource["0"]
		if ok {
			exist = true
			mergeResult.MachineRoom = originalData.MachineRoom
			if len(mergeResult.MachineRoomSource) == 0 {
				mergeResult.MachineRoomSource = make(map[string]string)
			}
			mergeResult.MachineRoomSource["0"] = machineRoom
		}
	}

	// 更新model
	if len(originalData.ModelSource) > 0 {
		model, ok := originalData.ModelSource["0"]
		if ok {
			exist = true
			mergeResult.Model = originalData.Model
			if len(mergeResult.ModelSource) == 0 {
				mergeResult.ModelSource = make(map[string]string)
			}
			mergeResult.ModelSource["0"] = model
		}
	}

	// 更新maker
	if len(originalData.MakerSource) > 0 {
		maker, ok := originalData.MakerSource["0"]
		if ok {
			exist = true
			mergeResult.Maker = originalData.Maker
			if len(mergeResult.MakerSource) == 0 {
				mergeResult.MakerSource = make(map[string]string)
			}
			mergeResult.MakerSource["0"] = maker
		}
	}

	// 更新tag
	if len(originalData.Tags) > 0 {
		tag, ok := originalData.TagsSource["0"]
		if ok {
			exist = true
			mergeResult.Tags = strings.Split(tag, ",")
			if len(mergeResult.TagsSource) == 0 {
				mergeResult.TagsSource = make(map[string]string)
			}
			mergeResult.TagsSource["0"] = tag
		}
	}

	// 如果存在被覆盖的数据，则更新 all_source_ids 和 source_ids
	if exist {
		if len(mergeResult.AllSourceIds) == 0 {
			mergeResult.AllSourceIds = make([]uint64, 0)
		}
		if len(mergeResult.SourceIds) == 0 {
			mergeResult.SourceIds = make([]uint64, 0)
		}
		mergeResult.AllSourceIds = append(mergeResult.AllSourceIds, 0)
		mergeResult.SourceIds = append(mergeResult.SourceIds, 0)
	}

	return nil
}

// 融合后处理，处理人工校准的业务系统字段
// 如果originalData.BusinessSource包含0，则使用originalData.Business覆盖mergeResult.Business
func updateUserTags(mergeResult *assets.Assets, originalData *assets.Assets) bool {
	exist := false
	system, ok := originalData.BusinessSource["0"]
	if ok {
		exist = true
		mergeResult.Business = originalData.Business
		if len(mergeResult.BusinessSource) == 0 {
			mergeResult.BusinessSource = make(map[string][]*assets.Business)
		}
		mergeResult.BusinessSource["0"] = system
	}
	return exist
}
