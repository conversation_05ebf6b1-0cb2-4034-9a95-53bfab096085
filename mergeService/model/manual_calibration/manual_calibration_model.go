package model

import (
	"context"
	"encoding/json"
	"errors"
	"fobrain/initialize/es"
	"fobrain/initialize/redis"
	logs "fobrain/mergeService/utils/log"
	"fobrain/models/mysql/custom_column"
	redis_helper "fobrain/models/redis"
	"fobrain/pkg/utils"
	"slices"
	"strings"
	"time"

	"github.com/olivere/elastic/v7"
)

var (
	manualCalibrationUpdateScript = `
		for (entry in params.Entries.entrySet()) {
			String fieldName = entry.getKey();
			def newValue = entry.getValue();
        
			// 更新主字段
			ctx._source[fieldName] = newValue;
		}

		for (entry in params.EntriesSource.entrySet()) {
			String fieldName = entry.getKey();
			def newValue = entry.getValue();
			
			// 检查源字段是否存在，如果不存在，则初始化为空map
			if (ctx._source[fieldName] == null) {
				ctx._source[fieldName] = [:];
			}
			
			// 将新值添加到源字段，键为'0'
			ctx._source[fieldName]['0'] = newValue;
		}
		
		// 更新updated_at
		ctx._source.updated_at = params.Now;

		// 检查和更新all_source_ids字段
		if (ctx._source.all_source_ids == null) {
			ctx._source.all_source_ids = [];
		}
		if (!ctx._source.all_source_ids.contains(0)) {
			ctx._source.all_source_ids.add(0);
		}
		
		// 检查和更新source_ids字段
		if (ctx._source.source_ids == null) {
			ctx._source.source_ids = [];
		}
		if (!ctx._source.source_ids.contains(0)) {
			ctx._source.source_ids.add(0);
		}
	`
)

// 手动校准基础模型
type ManualCalibrationModel struct {
	Ids          []string               `json:"ids"`
	Values       map[string]interface{} `json:"values"`
	ValuesSource map[string]interface{} `json:"values_source"`
	BusinessType string                 `json:"business_type"`
}

// 检查数据是否合法
func (m *ManualCalibrationModel) Check(allowKeys, customKeys []string, ids []string, values map[string]string) error {
	if len(ids) == 0 {
		return errors.New("ids不能为空")
	}
	if len(values) == 0 {
		return errors.New("values不能为空")
	}
	// 获取values中的key
	keys := make([]string, 0, len(values))
	for key := range values {
		if key == "custom_fields" {
			continue
		}
		keys = append(keys, key)
	}
	// 检查keys是否合法
	for _, key := range keys {
		if !slices.Contains(allowKeys, key) {
			return errors.New("key不合法")
		}
	}
	// 校验合格的自定义字段信息
	customField := make(map[string]string)
	// 检查customKeys是否合法
	if values["custom_fields"] != "" {
		// 反序列化
		var custom map[string]string
		err := json.Unmarshal([]byte(values["custom_fields"]), &custom)
		if err != nil {
			return errors.New("custom字段类型错误")
		}
		for key := range custom {
			if slices.Contains(customKeys, key) {
				customField[key] = custom[key]
			}
		}
		// 序列化
		customFieldValue, _ := json.Marshal(customField)
		values["custom_fields"] = string(customFieldValue)
	}
	return nil
}

func (m *ManualCalibrationModel) ConvertValue(input map[string]string) (values map[string]interface{}, valuesSource map[string]interface{}, err error) {
	switch m.BusinessType {
	case "asset":
		assetCalibration := &AssetManualCalibration{ManualCalibrationModel: *m}
		return assetCalibration.ConvertValue(input)
	case "device":
		deviceCalibration := &DeviceCalibrationModel{ManualCalibrationModel: *m}
		return deviceCalibration.ConvertValue(input)
	case "vuln":
		vulnCalibration := &VulnCalibrationModel{ManualCalibrationModel: *m}
		return vulnCalibration.ConvertValue(input)
	case "person":
		personCalibration := &PersonManualCalibrationModel{ManualCalibrationModel: *m}
		return personCalibration.ConvertValue(input)
	default:
		return nil, nil, errors.New("business_type不合法")
	}
}

func GetDisplayName(businessType string, key string) string {
	switch businessType {
	case "asset":
		assetCalibration := &AssetManualCalibration{}
		return assetCalibration.GetDisplayName(key)
	case "device":
		deviceCalibration := &DeviceCalibrationModel{}
		return deviceCalibration.GetDisplayName(key)
	case "vuln":
		vulnCalibration := &VulnCalibrationModel{}
		return vulnCalibration.GetDisplayName(key)
	case "person":
		personCalibration := &PersonManualCalibrationModel{}
		return personCalibration.GetDisplayName(key)
	default:
		return ""
	}
}

// ConvertCustomValues 转换custom字段
// @param customValue string，custom字段的值
// @return map[string]interface{}, custom字段转换后的值
func ConvertCustomValues(customValue string, allCustomKeys []string) map[string]interface{} {
	values := make(map[string]interface{})
	if customValue == "" {
		return values
	}
	// 反序列化
	var customMap map[string]string
	err := json.Unmarshal([]byte(customValue), &customMap)
	if err != nil {
		return values
	}
	for _, key := range allCustomKeys {
		if key == "" {
			continue
		}
		if _, ok := customMap[key]; ok {
			values[key] = customMap[key]
		} else {
			values[key] = ""
		}
	}
	return values
}

// GetCustomKeys 获取custom字段
// 优先从redis获取，如果不存在则从数据库获取，然后存入redis，过期时间为5分钟
func (m *ManualCalibrationModel) GetCustomKeys() []string {
	moduleType := ""
	redisKey := ""
	switch m.BusinessType {
	case "vuln":
		moduleType = custom_column.CustomFieldMetaModuleTypeVuln
		redisKey = redis_helper.VulnCustomFieldKey()
	case "person":
		moduleType = custom_column.CustomFieldMetaModuleTypeStaff
		redisKey = redis_helper.PersonCustomFieldKey()
	case "asset":
		moduleType = custom_column.CustomFieldMetaModuleTypeAsset
		redisKey = redis_helper.AssetCustomFieldKey()
	case "device":
		moduleType = custom_column.CustomFieldMetaModuleTypeDevice
		redisKey = redis_helper.DeviceCustomFieldKey()
	default:
		return nil
	}
	// 优先从redis获取
	customKeysStr, err := redis.GetRedisClient().Get(context.Background(), redisKey).Result()
	if err == nil {
		return strings.Split(customKeysStr, ",")
	}
	// 获取custom字段
	customColumnMeta, err := custom_column.NewCustomFieldMetaModel().GetByModuleType(moduleType, false)
	if err != nil {
		return nil
	}
	customKeys := make([]string, 0)
	for _, meta := range customColumnMeta {
		customKeys = append(customKeys, meta.FieldKey)
	}
	// 存入redis
	_ = redis.GetRedisClient().Set(context.Background(), redisKey, strings.Join(customKeys, ","), 1*time.Minute).Err()
	customKeys = utils.ListDistinctNonZero(customKeys)
	return customKeys
}

// BatchUpdate 批量更新数据
func (m *ManualCalibrationModel) BatchUpdate() error {
	mlog := logs.GetLogger(m.BusinessType)
	// 确定indexName
	indexName := ""
	switch m.BusinessType {
	case "asset":
		indexName = "asset"
	case "device":
		indexName = "device"
	case "vuln":
		indexName = "poc"
	case "person":
		indexName = "staff"
	default:
		return errors.New("business_type不合法")
	}
	// 生成更新脚本
	script := elastic.NewScriptInline(manualCalibrationUpdateScript).Lang("painless").Params(map[string]interface{}{
		"Entries":       m.Values,
		"EntriesSource": m.ValuesSource,
		"Now":           time.Now().Format("2006-01-02 15:04:05"),
	})
	mlog.Infof("更新数据, values: %+v, valuesSource: %+v", m.Values, m.ValuesSource)
	// 执行更新
	updateService := es.GetEsClient().UpdateByQuery(indexName).Query(elastic.NewTermsQueryFromStrings("_id", m.Ids...)).Script(script).Refresh("true")
	resp, err := updateService.Do(context.Background())
	if err != nil {
		mlog.Errorf("更新数据失败, err: %v", err)
		return err
	}
	mlog.Debug("更新数据成功, resp: %v", resp)
	return nil
}
