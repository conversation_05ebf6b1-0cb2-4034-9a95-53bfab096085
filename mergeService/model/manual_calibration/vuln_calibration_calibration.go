package model

import (
	"errors"

	"github.com/spf13/cast"

	"fobrain/fobrain/common/localtime"
	esmodel "fobrain/models/elastic/poc"
)

type VulnCalibrationModel struct {
	ManualCalibrationModel
}

var (
	vulnAllowKeys = []string{
		"port",
		"level",
		"name",
		"vulType",
		"has_exp",
		"has_poc",
		"describe",
		"details",
		"hazard",
		"suggestions",
		"last_response_at",
	}
)

func GetVulnAllowKeys() []string {
	return vulnAllowKeys
}

func (m *VulnCalibrationModel) GetDisplayName(key string) string {
	switch key {
	case "port":
		return "端口"
	case "level":
		return "漏洞等级"
	case "name":
		return "漏洞名称"
	case "vulType":
		return "漏洞类型"
	case "has_exp":
		return "是否存在exp"
	case "has_poc":
		return "是否存在poc"
	case "describe":
		return "描述"
	case "details":
		return "详情"
	case "hazard":
		return "危险性"
	case "suggestions":
		return "修复建议"
	case "last_response_at":
		return "最后响应"
	}
	return ""
}

func (m *VulnCalibrationModel) ConvertValue(input map[string]string) (map[string]interface{}, map[string]interface{}, error) {
	values := make(map[string]interface{})
	valuesSource := make(map[string]interface{})
	// 获取传入的input中的 key
	for key, value := range input {
		switch key {
		case "port":
			port, err := cast.ToIntE(value)
			if err != nil {
				return nil, nil, err
			}
			values[key] = port
			valuesSource[key+"_source"] = port
		case "level":
			level, err := cast.ToIntE(value)
			if err != nil {
				return nil, nil, err
			}
			if level < 1 || level > 5 {
				return nil, nil, errors.New("漏洞等级校准结果应为低危、中危、高危、严重")
			}
			values[key] = level
			valuesSource[key+"_source"] = level
		case "name":
			values[key] = value
			valuesSource[key+"_source"] = value
		case "vulType":
			values[key] = value
			valuesSource[key+"_source"] = value
		case "has_exp":
			hasExp, err := cast.ToIntE(value)
			if err != nil {
				return nil, nil, err
			}
			values[key] = hasExp
			valuesSource[key+"_source"] = hasExp
		case "has_poc":
			hasPoc, err := cast.ToIntE(value)
			if err != nil {
				return nil, nil, err
			}
			values[key] = hasPoc
			valuesSource["has_poc_source"] = hasPoc
		case "describe":
			values[key] = value
			valuesSource[key+"_source"] = value
		case "details":
			values[key] = value
			valuesSource[key+"_source"] = value
		case "hazard":
			values[key] = value
			valuesSource[key+"_source"] = value
		case "suggestions":
			values[key] = value
			valuesSource[key+"_source"] = value
		case "last_response_at":
			if value == "" {
				values[key] = nil
				valuesSource["last_response_at_source"] = nil
				break
			}
			lastResponseAt, err := cast.ToTimeE(value)
			if err != nil {
				return nil, nil, err
			}
			values[key] = localtime.NewLocalTime(lastResponseAt)
			valuesSource["last_response_at_source"] = value
		case "custom_fields":
			customValues := ConvertCustomValues(value, m.GetCustomKeys())
			if len(customValues) > 0 {
				values[key] = customValues
				valuesSource[key+"_source"] = customValues
			}
		}
	}
	return values, valuesSource, nil
}

// 使用人工校准数据更新融合结果
// mergeResult: 当前融合结果
// originalData: 原始数据，人工校准数据
func (m *VulnCalibrationModel) UpdateMergeResult(mergeResult *esmodel.Poc, originalData *esmodel.Poc) error {
	if mergeResult == nil || originalData == nil {
		return errors.New("mergeResult或originalData为空")
	}

	// 是否存在被覆盖的数据
	exist := false

	// 更新port
	if len(originalData.PortSource) > 0 {
		port, ok := originalData.PortSource["0"]
		if ok {
			exist = true
			mergeResult.Port = originalData.Port
			if len(mergeResult.PortSource) == 0 {
				mergeResult.PortSource = make(map[string]int)
			}
			mergeResult.PortSource["0"] = port
		}
	}

	// 更新level
	if len(originalData.LevelSource) > 0 {
		level, ok := originalData.LevelSource["0"]
		if ok {
			exist = true
			mergeResult.Level = originalData.Level
			if len(mergeResult.LevelSource) == 0 {
				mergeResult.LevelSource = make(map[string]int)
			}
			mergeResult.LevelSource["0"] = level
		}
	}

	// 更新name
	if len(originalData.NameSource) > 0 {
		name, ok := originalData.NameSource["0"]
		if ok {
			exist = true
			mergeResult.Name = originalData.Name
			if len(mergeResult.NameSource) == 0 {
				mergeResult.NameSource = make(map[string]string)
			}
			mergeResult.NameSource["0"] = name
		}
	}

	// 更新vulType
	if len(originalData.VulTypeSource) > 0 {
		vulType, ok := originalData.VulTypeSource["0"]
		if ok {
			exist = true
			mergeResult.VulType = originalData.VulType
			if len(mergeResult.VulTypeSource) == 0 {
				mergeResult.VulTypeSource = make(map[string]string)
			}
			mergeResult.VulTypeSource["0"] = vulType
		}
	}

	// 更新hasExp
	if len(originalData.HasExpSource) > 0 {
		hasExp, ok := originalData.HasExpSource["0"]
		if ok {
			exist = true
			mergeResult.HasExp = originalData.HasExp
			if len(mergeResult.HasExpSource) == 0 {
				mergeResult.HasExpSource = make(map[string]int)
			}
			mergeResult.HasExpSource["0"] = hasExp
		}
	}

	// 更新hasPoc
	if len(originalData.HasPocSource) > 0 {
		hasPoc, ok := originalData.HasPocSource["0"]
		if ok {
			exist = true
			mergeResult.HasPoc = originalData.HasPoc
			if len(mergeResult.HasPocSource) == 0 {
				mergeResult.HasPocSource = make(map[string]int)
			}
			mergeResult.HasPocSource["0"] = hasPoc
		}
	}

	// 更新describe
	if len(originalData.DescribeSource) > 0 {
		describe, ok := originalData.DescribeSource["0"]
		if ok {
			exist = true
			mergeResult.Describe = originalData.Describe
			if len(mergeResult.DescribeSource) == 0 {
				mergeResult.DescribeSource = make(map[string]string)
			}
			mergeResult.DescribeSource["0"] = describe
		}
	}

	// 更新details
	if len(originalData.DetailsSource) > 0 {
		details, ok := originalData.DetailsSource["0"]
		if ok {
			exist = true
			mergeResult.Details = originalData.Details
			if len(mergeResult.DetailsSource) == 0 {
				mergeResult.DetailsSource = make(map[string]string)
			}
			mergeResult.DetailsSource["0"] = details
		}
	}

	// 更新hazard
	if len(originalData.HazardSource) > 0 {
		hazard, ok := originalData.HazardSource["0"]
		if ok {
			exist = true
			mergeResult.Hazard = originalData.Hazard
			if len(mergeResult.HazardSource) == 0 {
				mergeResult.HazardSource = make(map[string]string)
			}
			mergeResult.HazardSource["0"] = hazard
		}
	}

	// 更新suggestions
	if len(originalData.SuggestionsSource) > 0 {
		suggestions, ok := originalData.SuggestionsSource["0"]
		if ok {
			exist = true
			mergeResult.Suggestions = originalData.Suggestions
			if len(mergeResult.SuggestionsSource) == 0 {
				mergeResult.SuggestionsSource = make(map[string]string)
			}
			mergeResult.SuggestionsSource["0"] = suggestions
		}
	}

	// 更新lastResponseAt
	if len(originalData.LastResponseAtSource) > 0 {
		lastResponseAt, ok := originalData.LastResponseAtSource["0"]
		if ok {
			exist = true
			mergeResult.LastResponseAt = originalData.LastResponseAt
			if len(mergeResult.LastResponseAtSource) == 0 {
				mergeResult.LastResponseAtSource = make(map[string]string)
			}
			mergeResult.LastResponseAtSource["0"] = lastResponseAt
		}
	}

	// 如果存在被覆盖的数据，则更新 all_source_ids 和 source_ids
	if exist {
		if len(mergeResult.AllSourceIds) == 0 {
			mergeResult.AllSourceIds = make([]uint64, 0)
		}
		if len(mergeResult.SourceIds) == 0 {
			mergeResult.SourceIds = make([]uint64, 0)
		}
		mergeResult.AllSourceIds = append(mergeResult.AllSourceIds, 0)
		mergeResult.SourceIds = append(mergeResult.SourceIds, 0)
	}

	return nil
}
