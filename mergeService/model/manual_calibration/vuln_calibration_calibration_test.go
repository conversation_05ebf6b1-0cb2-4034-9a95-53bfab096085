package model

import (
	"reflect"
	"testing"
	"time"

	"fobrain/fobrain/common/localtime"
	esmodel "fobrain/models/elastic/poc"
)

func TestVulnCalibrationModel_GetDisplayName(t *testing.T) {
	m := &VulnCalibrationModel{}
	tests := []struct {
		name string
		key  string
		want string
	}{
		{"端口", "port", "端口"},
		{"等级", "level", "漏洞等级"},
		{"名称", "name", "漏洞名称"},
		{"漏洞类型", "vulType", "漏洞类型"},
		{"是否存在exp", "has_exp", "是否存在exp"},
		{"是否存在poc", "has_poc", "是否存在poc"},
		{"描述", "describe", "描述"},
		{"详情", "details", "详情"},
		{"危害", "hazard", "危险性"},
		{"建议", "suggestions", "修复建议"},
		{"最后响应", "last_response_at", "最后响应"},
		{"未知键", "unknown", ""},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := m.GetDisplayName(tt.key); got != tt.want {
				t.Errorf("VulnCalibrationModel.GetDisplayName() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestVulnCalibrationModel_ConvertValue(t *testing.T) {
	lastResponseAt, _ := time.Parse("2006-01-02 15:04:05", "2024-01-01 00:00:00")
	m := &VulnCalibrationModel{}
	tests := []struct {
		name        string
		input       map[string]string
		wantValues  map[string]interface{}
		wantSources map[string]interface{}
		wantErr     bool
	}{
		{
			name: "有效输入",
			input: map[string]string{
				"port":             "80",
				"level":            "2",
				"name":             "测试漏洞",
				"vulType":          "XSS",
				"has_exp":          "1",
				"has_poc":          "2",
				"describe":         "这是一个测试描述",
				"details":          "详细信息",
				"hazard":           "中危",
				"suggestions":      "修复建议",
				"last_response_at": "2024-01-01 00:00:00",
			},
			wantValues: map[string]interface{}{
				"port":             80,
				"level":            2,
				"name":             "测试漏洞",
				"vulType":          "XSS",
				"has_exp":          1,
				"has_poc":          2,
				"describe":         "这是一个测试描述",
				"details":          "详细信息",
				"hazard":           "中危",
				"suggestions":      "修复建议",
				"last_response_at": localtime.NewLocalTime(lastResponseAt),
			},
			wantSources: map[string]interface{}{
				"port_source":             80,
				"level_source":            2,
				"name_source":             "测试漏洞",
				"vulType_source":          "XSS",
				"has_exp_source":          1,
				"has_poc_source":          2,
				"describe_source":         "这是一个测试描述",
				"details_source":          "详细信息",
				"hazard_source":           "中危",
				"suggestions_source":      "修复建议",
				"last_response_at_source": "2024-01-01 00:00:00",
			},
			wantErr: false,
		},
		{
			name: "无效等级",
			input: map[string]string{
				"level": "invalid",
			},
			wantValues:  nil,
			wantSources: nil,
			wantErr:     true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotValues, gotSources, err := m.ConvertValue(tt.input)
			if (err != nil) != tt.wantErr {
				t.Errorf("VulnCalibrationModel.ConvertValue() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotValues, tt.wantValues) {
				t.Errorf("VulnCalibrationModel.ConvertValue() gotValues = %v, want %v", gotValues, tt.wantValues)
			}
			if !reflect.DeepEqual(gotSources, tt.wantSources) {
				t.Errorf("VulnCalibrationModel.ConvertValue() gotSources = %v, want %v", gotSources, tt.wantSources)
			}
		})
	}
}

func TestVulnCalibrationModel_UpdateMergeResult(t *testing.T) {
	m := &VulnCalibrationModel{}
	lastResponseAt1, _ := time.Parse("2006-01-02 15:04:05", "2024-01-01 00:00:00")
	lastResponseAt2, _ := time.Parse("2006-01-02 15:04:05", "2024-01-02 00:00:00")

	tests := []struct {
		name         string
		mergeResult  *esmodel.Poc
		originalData *esmodel.Poc
		wantErr      bool
		wantResult   *esmodel.Poc
	}{
		{
			name:         "空输入",
			mergeResult:  nil,
			originalData: nil,
			wantErr:      true,
			wantResult:   nil,
		},
		{
			name: "更新所有字段",
			mergeResult: &esmodel.Poc{
				Port:           22,
				Level:          1,
				Name:           "旧名称",
				VulType:        "旧类型",
				HasExp:         2,
				HasPoc:         1,
				Describe:       "旧描述",
				Details:        "旧详情",
				Hazard:         "旧危害",
				Suggestions:    "旧建议",
				LastResponseAt: localtime.NewLocalTime(lastResponseAt1),
			},
			originalData: &esmodel.Poc{
				Port:                 80,
				PortSource:           map[string]int{"0": 80},
				Level:                2,
				LevelSource:          map[string]int{"0": 2},
				Name:                 "新名称",
				NameSource:           map[string]string{"0": "新名称"},
				VulType:              "新类型",
				VulTypeSource:        map[string]string{"0": "新类型"},
				HasExp:               1,
				HasExpSource:         map[string]int{"0": 1},
				HasPoc:               2,
				HasPocSource:         map[string]int{"0": 2},
				Describe:             "新描述",
				DescribeSource:       map[string]string{"0": "新描述"},
				Details:              "新详情",
				DetailsSource:        map[string]string{"0": "新详情"},
				Hazard:               "新危害",
				HazardSource:         map[string]string{"0": "新危害"},
				LastResponseAt:       localtime.NewLocalTime(lastResponseAt2),
				LastResponseAtSource: map[string]string{"0": "2024-01-02 00:00:00"},
				Suggestions:          "新建议",
				SuggestionsSource:    map[string]string{"0": "新建议"},
			},
			wantErr: false,
			wantResult: &esmodel.Poc{
				Port:                 80,
				PortSource:           map[string]int{"0": 80},
				Level:                2,
				LevelSource:          map[string]int{"0": 2},
				Name:                 "新名称",
				NameSource:           map[string]string{"0": "新名称"},
				VulType:              "新类型",
				VulTypeSource:        map[string]string{"0": "新类型"},
				HasExp:               1,
				HasExpSource:         map[string]int{"0": 1},
				HasPoc:               2,
				HasPocSource:         map[string]int{"0": 2},
				Describe:             "新描述",
				DescribeSource:       map[string]string{"0": "新描述"},
				Details:              "新详情",
				DetailsSource:        map[string]string{"0": "新详情"},
				Hazard:               "新危害",
				HazardSource:         map[string]string{"0": "新危害"},
				Suggestions:          "新建议",
				SuggestionsSource:    map[string]string{"0": "新建议"},
				LastResponseAt:       localtime.NewLocalTime(lastResponseAt2),
				LastResponseAtSource: map[string]string{"0": "2024-01-02 00:00:00"},
				AllSourceIds:         []uint64{0},
				SourceIds:            []uint64{0},
			},
		},
		{
			name: "部分更新",
			mergeResult: &esmodel.Poc{
				Level:       1,
				Name:        "旧名称",
				VulType:     "旧类型",
				Describe:    "旧描述",
				Details:     "旧详情",
				Hazard:      "旧危害",
				Suggestions: "旧建议",
			},
			originalData: &esmodel.Poc{
				Level:         2,
				LevelSource:   map[string]int{"0": 2},
				Name:          "新名称",
				NameSource:    map[string]string{"0": "新名称"},
				VulType:       "新类型",
				VulTypeSource: map[string]string{"0": "新类型"},
			},
			wantErr: false,
			wantResult: &esmodel.Poc{
				Level:         2,
				LevelSource:   map[string]int{"0": 2},
				Name:          "新名称",
				NameSource:    map[string]string{"0": "新名称"},
				VulType:       "新类型",
				VulTypeSource: map[string]string{"0": "新类型"},
				Describe:      "旧描述",
				Details:       "旧详情",
				Hazard:        "旧危害",
				Suggestions:   "旧建议",
				AllSourceIds:  []uint64{0},
				SourceIds:     []uint64{0},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := m.UpdateMergeResult(tt.mergeResult, tt.originalData)
			if (err != nil) != tt.wantErr {
				t.Errorf("VulnCalibrationModel.UpdateMergeResult() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !tt.wantErr && !reflect.DeepEqual(tt.mergeResult, tt.wantResult) {
				t.Errorf("VulnCalibrationModel.UpdateMergeResult() got = %v, want %v", tt.mergeResult, tt.wantResult)
			}
		})
	}
}
