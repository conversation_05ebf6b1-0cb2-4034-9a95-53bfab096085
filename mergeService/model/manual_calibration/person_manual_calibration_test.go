package model

import (
	"reflect"
	"testing"

	esmodel "fobrain/models/elastic/staff"
)

func TestPersonManualCalibrationModel_GetDisplayName(t *testing.T) {
	m := &PersonManualCalibrationModel{}
	tests := []struct {
		name string
		key  string

		want string
	}{
		{"英文名", "english_name", "英文名"},
		{"职称", "title", "职称"},
		{"邮箱", "email", "邮箱"},
		{"部门", "department", "部门"},
		{"全部门ids", "all_departments_ids", "全部门ids"},
		{"当前部门ids", "departments_ids", "当前部门ids"},
		{"状态", "status", "状态"},
		{"未知键", "unknown", ""},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := m.GetDisplayName(tt.key); got != tt.want {
				t.<PERSON>("PersonManualCalibrationModel.GetDisplayName() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPersonManualCalibrationModel_ConvertValue(t *testing.T) {
	m := &PersonManualCalibrationModel{}
	tests := []struct {
		name        string
		input       map[string]string
		wantValues  map[string]interface{}
		wantSources map[string]interface{}
		wantErr     bool
	}{
		{
			name: "有效输入",
			input: map[string]string{
				"english_name": `["John", "Doe"]`,
				"title":        `["Engineer"]`,
				"email":        `["<EMAIL>"]`,
				"department":   `[{"department": "北京华顺信安科技有限公司/项目部/销售中心","departments_ids": "1112","all_departments_ids": ["1105", "1106", "1112"]}]`,
				"status":       "1",
			},
			wantValues: map[string]interface{}{
				"english_name":        []string{"John", "Doe"},
				"title":               []string{"Engineer"},
				"email":               []string{"<EMAIL>"},
				"department":          []string{"北京华顺信安科技有限公司/项目部/销售中心"},
				"departments_ids":     []string{"1112"},
				"all_departments_ids": []string{"1105", "1106", "1112"},
				"status":              1,
			},
			wantSources: map[string]interface{}{
				"english_name_source": "John,Doe",
				"title_source":        "Engineer",
				"email_source":        "<EMAIL>",
				"department_source":   "北京华顺信安科技有限公司/项目部/销售中心",
				"status_source":       1,
			},
			wantErr: false,
		},
		{
			name: "无效JSON",
			input: map[string]string{
				"english_name": `["John", "Doe"`,
			},
			wantValues:  nil,
			wantSources: nil,
			wantErr:     true,
		},
		{
			name: "无效状态值",
			input: map[string]string{
				"status": "invalid",
			},
			wantValues:  nil,
			wantSources: nil,
			wantErr:     true,
		},
		{
			name: "department格式不正确",
			input: map[string]string{
				"department": `{"department": [],"departments_ids": [],"all_departments_ids": []}`,
			},
			wantValues:  nil,
			wantSources: nil,
			wantErr:     true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotValues, gotSources, err := m.ConvertValue(tt.input)
			if (err != nil) != tt.wantErr {
				t.Errorf("PersonManualCalibrationModel.ConvertValue() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotValues, tt.wantValues) {
				t.Errorf("PersonManualCalibrationModel.ConvertValue() gotValues = %v, want %v", gotValues, tt.wantValues)
			}
			if !reflect.DeepEqual(gotSources, tt.wantSources) {
				t.Errorf("PersonManualCalibrationModel.ConvertValue() gotSources = %v, want %v", gotSources, tt.wantSources)
			}
		})
	}
}

func TestPersonManualCalibrationModel_UpdateMergeResult(t *testing.T) {
	m := &PersonManualCalibrationModel{}

	tests := []struct {
		name         string
		mergeResult  *esmodel.Staff
		originalData *esmodel.Staff
		wantErr      bool
		wantResult   *esmodel.Staff
	}{
		{
			name:         "空输入",
			mergeResult:  nil,
			originalData: nil,
			wantErr:      true,
			wantResult:   nil,
		},
		{
			name: "更新所有字段",
			mergeResult: &esmodel.Staff{
				EnglishName: []string{"OldName"},
				Title:       []string{"OldTitle"},
				Email:       []string{"<EMAIL>"},
				Department:  []string{"OldDept"},
				Status:      0,
			},
			originalData: &esmodel.Staff{
				EnglishName:       []string{"NewName"},
				EnglishNameSource: map[string]string{"0": "NewNameSource"},
				Title:             []string{"NewTitle"},
				TitleSource:       map[string]string{"0": "NewTitleSource"},
				Email:             []string{"<EMAIL>"},
				EmailSource:       map[string]string{"0": "NewEmailSource"},
				Department:        []string{"NewDept"},
				DepartmentSource:  map[string]string{"0": "NewDeptSource"},
				Status:            1,
				StatusSource:      map[string]int{"0": 1},
			},
			wantErr: false,
			wantResult: &esmodel.Staff{
				EnglishName:       []string{"NewName"},
				EnglishNameSource: map[string]string{"0": "NewNameSource"},
				Title:             []string{"NewTitle"},
				TitleSource:       map[string]string{"0": "NewTitleSource"},
				Email:             []string{"<EMAIL>"},
				EmailSource:       map[string]string{"0": "NewEmailSource"},
				Department:        []string{"NewDept"},
				DepartmentSource:  map[string]string{"0": "NewDeptSource"},
				Status:            1,
				StatusSource:      map[string]int{"0": 1},
				AllSourceIds:      []uint64{0},
				SourceIds:         []uint64{0},
			},
		},
		{
			name: "部分更新",
			mergeResult: &esmodel.Staff{
				EnglishName: []string{"OldName"},
				Title:       []string{"OldTitle"},
				Email:       []string{"<EMAIL>"},
				Department:  []string{"OldDept"},
				Status:      0,
			},
			originalData: &esmodel.Staff{
				EnglishName:       []string{"NewName"},
				EnglishNameSource: map[string]string{"0": "NewNameSource"},
				Email:             []string{"<EMAIL>"},
				EmailSource:       map[string]string{"0": "NewEmailSource"},
			},
			wantErr: false,
			wantResult: &esmodel.Staff{
				EnglishName:       []string{"NewName"},
				EnglishNameSource: map[string]string{"0": "NewNameSource"},
				Title:             []string{"OldTitle"},
				Email:             []string{"<EMAIL>"},
				EmailSource:       map[string]string{"0": "NewEmailSource"},
				Department:        []string{"OldDept"},
				Status:            0,
				AllSourceIds:      []uint64{0},
				SourceIds:         []uint64{0},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := m.UpdateMergeResult(tt.mergeResult, tt.originalData)
			if (err != nil) != tt.wantErr {
				t.Errorf("PersonManualCalibrationModel.UpdateMergeResult() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !tt.wantErr && !reflect.DeepEqual(tt.mergeResult, tt.wantResult) {
				t.Errorf("PersonManualCalibrationModel.UpdateMergeResult() got = %v, want %v", tt.mergeResult, tt.wantResult)
			}
		})
	}
}
