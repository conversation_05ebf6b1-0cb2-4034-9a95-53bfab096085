package model

import (
	"encoding/json"
	"errors"
	"strings"

	esmodel "fobrain/models/elastic/staff"
	"fobrain/pkg/utils"

	"github.com/spf13/cast"
)

type PersonManualCalibrationModel struct {
	ManualCalibrationModel
}

var (
	personAllowKeys = []string{
		"english_name",
		"title",
		"email",
		"department",
		"all_departments_ids",
		"departments_ids",
		"status",
	}
)

func GetPersonAllowKeys() []string {
	return personAllowKeys
}

func (m *PersonManualCalibrationModel) GetDisplayName(key string) string {
	switch key {
	case "english_name":
		return "英文名"
	case "title":
		return "职称"
	case "email":
		return "邮箱"
	case "department":
		return "部门"
	case "all_departments_ids":
		return "全部门ids"
	case "departments_ids":
		return "当前部门ids"
	case "status":
		return "状态"
	}
	return ""
}

func (m *PersonManualCalibrationModel) ConvertValue(input map[string]string) (map[string]interface{}, map[string]interface{}, error) {
	// 把input的值反序列化为Person对象，用来检查传入的参数格式是否正确
	person := &esmodel.Staff{}
	values := make(map[string]interface{})
	valuesSource := make(map[string]interface{})
	// 获取传入的input中的 key
	for key, value := range input {
		switch key {
		case "english_name":
			err := json.Unmarshal([]byte(value), &person.EnglishName)
			if err != nil {
				return nil, nil, err
			}
			values[key] = person.EnglishName
			valuesSource[key+"_source"] = strings.Join(person.EnglishName, ",")
		case "title":
			err := json.Unmarshal([]byte(value), &person.Title)
			if err != nil {
				return nil, nil, err
			}
			values[key] = person.Title
			valuesSource[key+"_source"] = strings.Join(person.Title, ",")
		case "email":
			err := json.Unmarshal([]byte(value), &person.Email)
			if err != nil {
				return nil, nil, err
			}
			values[key] = person.Email
			valuesSource[key+"_source"] = strings.Join(person.Email, ",")
		case "department":
			// 定义结构体
			type Department struct {
				Department        string   `json:"department"`
				DepartmentIds     string   `json:"departments_ids"`
				AllDepartmentsIds []string `json:"all_departments_ids"`
			}
			departments := make([]Department, 0)
			err := json.Unmarshal([]byte(value), &departments)
			if err != nil {
				return nil, nil, err
			}
			if len(departments) == 0 {
				return nil, nil, errors.New("department格式不正确")
			}
			names := make([]string, 0)
			ids := make([]string, 0)
			allIds := make([]string, 0)
			for _, department := range departments {
				if len(department.Department) == 0 || len(department.DepartmentIds) == 0 || len(department.AllDepartmentsIds) == 0 {
					return nil, nil, errors.New("department格式不正确")
				}
				names = append(names, department.Department)
				ids = append(ids, department.DepartmentIds)
				allIds = append(allIds, department.AllDepartmentsIds...)
			}
			names = utils.ListDistinct(names)
			values["department"] = names
			values["departments_ids"] = utils.ListDistinct(ids)
			values["all_departments_ids"] = utils.ListDistinct(allIds)
			valuesSource[key+"_source"] = strings.Join(names, ",")
		case "status":
			status, err := cast.ToIntE(value)
			if err != nil {
				return nil, nil, err
			}
			values[key] = status
			valuesSource[key+"_source"] = status
		case "custom_fields":
			customValues := ConvertCustomValues(value, m.GetCustomKeys())
			if len(customValues) > 0 {
				values[key] = customValues
				valuesSource[key+"_source"] = customValues
			}
		}
	}
	return values, valuesSource, nil
}

// 使用人工校准数据更新融合结果
// mergeResult: 当前融合结果
// originalData: 原始数据，人工校准数据
func (m *PersonManualCalibrationModel) UpdateMergeResult(mergeResult *esmodel.Staff, originalData *esmodel.Staff) error {
	if mergeResult == nil || originalData == nil {
		return errors.New("mergeResult或originalData为空")
	}

	// 是否存在被覆盖的数据
	exist := false

	// 更新english_name
	if len(originalData.EnglishNameSource) > 0 {
		englishName, ok := originalData.EnglishNameSource["0"]
		if ok {
			exist = true
			mergeResult.EnglishName = originalData.EnglishName
			if len(mergeResult.EnglishNameSource) == 0 {
				mergeResult.EnglishNameSource = make(map[string]string)
			}
			mergeResult.EnglishNameSource["0"] = englishName
		}
	}

	// 更新title
	if len(originalData.TitleSource) > 0 {
		title, ok := originalData.TitleSource["0"]
		if ok {
			exist = true
			mergeResult.Title = originalData.Title
			if len(mergeResult.TitleSource) == 0 {
				mergeResult.TitleSource = make(map[string]string)
			}
			mergeResult.TitleSource["0"] = title
		}
	}

	// 更新email
	if len(originalData.EmailSource) > 0 {
		email, ok := originalData.EmailSource["0"]
		if ok {
			exist = true
			mergeResult.Email = originalData.Email
			if len(mergeResult.EmailSource) == 0 {
				mergeResult.EmailSource = make(map[string]string)
			}
			mergeResult.EmailSource["0"] = email
		}
	}

	// 更新department
	if len(originalData.DepartmentSource) > 0 {
		department, ok := originalData.DepartmentSource["0"]
		if ok {
			exist = true
			mergeResult.Department = originalData.Department
			if len(mergeResult.DepartmentSource) == 0 {
				mergeResult.DepartmentSource = make(map[string]string)
			}
			mergeResult.DepartmentSource["0"] = department
		}
	}

	// 更新status
	if len(originalData.StatusSource) > 0 {
		status, ok := originalData.StatusSource["0"]
		if ok {
			exist = true
			mergeResult.Status = originalData.Status
			if len(mergeResult.StatusSource) == 0 {
				mergeResult.StatusSource = make(map[string]int)
			}
			mergeResult.StatusSource["0"] = status
		}
	}

	// 如果存在被覆盖的数据，则更新 all_source_ids 和 source_ids
	if exist {
		if len(mergeResult.AllSourceIds) == 0 {
			mergeResult.AllSourceIds = make([]uint64, 0)
		}
		if len(mergeResult.SourceIds) == 0 {
			mergeResult.SourceIds = make([]uint64, 0)
		}
		mergeResult.AllSourceIds = append(mergeResult.AllSourceIds, 0)
		mergeResult.SourceIds = append(mergeResult.SourceIds, 0)
	}

	return nil
}
