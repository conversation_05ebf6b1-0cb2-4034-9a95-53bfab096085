package threat_center

import (
	"errors"
	pgidservice "fobrain/services/people_pgid"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"

	"fobrain/fobrain/app/repository/threat_history"
	"fobrain/models/elastic/poc"
	"fobrain/models/elastic/staff"
	"fobrain/models/mysql/user"
	"fobrain/pkg/utils"
)

func Distribute(user *user.User, staff *staff.Staff, pocObj map[string]interface{}, oneThreatHistory *threat_history.OneThreatHistory) error {
	var ctx *gin.Context
	pocObjStatusCode := cast.ToInt(pocObj["statusCode"])
	err := checkDistribute(oneThreatHistory.Status, pocObjStatusCode)
	if err != nil {
		return err
	}
	// 检查漏洞状态
	ok, msg := threat_history.CheckStatus(pocObjStatusCode, oneThreatHistory.Status)
	if !ok {
		return errors.New(msg)
	}
	// 创建操作记录
	pgid, _ := pgidservice.GetPgidById(staff.Id)
	content := threat_history.GetOperateContent(user.Account, oneThreatHistory.Status, "漏洞", pgid)
	err = threat_history.CreateHistory(ctx, user, oneThreatHistory, staff, pocObjStatusCode, content)
	if err != nil {
		return err
	}
	// 发送邮件通知
	someThreatHistory := &threat_history.SomeThreatHistory{
		PocIds:            []string{oneThreatHistory.PocId},
		Descrition:        oneThreatHistory.Descrition,
		ToCc:              oneThreatHistory.ToCc,
		Status:            oneThreatHistory.Status,
		SendNotice:        oneThreatHistory.SendNotice,
		ToStaffId:         oneThreatHistory.ToStaffId,
		ToStaffName:       oneThreatHistory.ToStaffName,
		LimitDate:         oneThreatHistory.LimitDate,
		TimeoutNotice:     oneThreatHistory.TimeoutNotice,
		ExecNow:           oneThreatHistory.ExecNow,
		OriginalId:        oneThreatHistory.OriginalId,
		OperationType:     oneThreatHistory.OperationType,
		TimeoutReceiverId: oneThreatHistory.TimeoutReceiverId,
		TimeoutFrequency:  oneThreatHistory.TimeoutFrequency,
	}
	err = threat_history.SendNotice(ctx, someThreatHistory, staff)
	if err != nil {
		return err
	}
	err = threat_history.SendWebhookMsg(oneThreatHistory, staff, user, []map[string]interface{}{pocObj})
	if err != nil {
		return err
	}
	// 修改状态
	err = threat_history.UpdateThreatStatus(oneThreatHistory, pocObj, staff, nil)
	if err != nil {
		return err
	}
	return nil
}

func checkDistribute(operationStatus int, pocStatus int) error {
	//检查漏洞是否符合操作类型
	if operationStatus != poc.PocStatusOfBeRepair && operationStatus != poc.PocStatusOfForward {
		return errors.New("不支持的操作类型")
	}

	//校验操作类型 派发仅支持：
	if operationStatus == poc.PocStatusOfBeRepair {
		//操作的是派发不在符合的状态范围内，返回失败
		if !utils.InArray(pocStatus, []int{poc.PocStatusOfNew, poc.PocStatusOfStillExist, poc.PocStatusOfErrorReport, poc.PocStatusOfCantRepaired}) {
			return errors.New("漏洞状态不符合操作类型")
		}
	}

	//校验操作类型 转交仅支持：
	if operationStatus == poc.PocStatusOfForward {
		//操作的是派发不在符合的状态范围内，返回失败
		if !utils.InArray(pocStatus, []int{poc.PocStatusOfBeRepair, poc.PocStatusOfForward, poc.PocStatusOfDelay, poc.PocStatusOfTimeout, poc.PocStatusOfNoRepair, poc.PocOperateOfUrge}) {
			return errors.New("漏洞状态不符合操作类型")
		}
	}
	return nil
}
