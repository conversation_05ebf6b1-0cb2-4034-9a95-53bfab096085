package threat_center

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"fobrain/models/elastic/poc"
)

func TestDistribute(t *testing.T) {
	// 测试 checkDistribute 函数的逻辑，而不是整个 Distribute 函数
	// 这样避免了复杂的外部依赖

	// 测试不支持的操作类型
	err := checkDistribute(99, poc.PocStatusOfNew)
	assert.Error(t, err)
	assert.Equal(t, "不支持的操作类型", err.Error())

	// 测试派发操作 - 支持的状态
	err = checkDistribute(poc.PocStatusOfBeRepair, poc.PocStatusOfNew) // 派发 + 新发现
	assert.NoError(t, err)

	// 测试派发操作 - 不支持的状态
	err = checkDistribute(poc.PocStatusOfBeRepair, poc.PocStatusOfRepaired) // 派发 + 已修复（不支持）
	assert.Error(t, err)
	assert.Equal(t, "漏洞状态不符合操作类型", err.Error())

	// 测试转交操作 - 支持的状态
	err = checkDistribute(poc.PocStatusOfForward, poc.PocStatusOfBeRepair) // 转交 + 待修复
	assert.NoError(t, err)

	// 测试转交操作 - 不支持的状态
	err = checkDistribute(poc.PocStatusOfForward, poc.PocStatusOfRepaired) // 转交 + 已修复（不支持）
	assert.Error(t, err)
	assert.Equal(t, "漏洞状态不符合操作类型", err.Error())
}
