package ipranges_service

import (
	"errors"
	"testing"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/olivere/elastic/v7"
	"github.com/stretchr/testify/assert"

	"fobrain/initialize/es"
	"fobrain/initialize/mysql"
	"fobrain/models/elastic/assets"
	ipRangesSql "fobrain/models/mysql/ip_ranges"
)

// TestRefreshIpUsageRate 测试 IP 使用率刷新功能
func TestRefreshIpUsageRate(t *testing.T) {
	// 定义测试用例
	testCases := []struct {
		name        string                          // 测试用例名称
		setupMocks  func(patches *gomonkey.Patches) // 设置 mock 函数
		expectedErr error                           // 期望的错误
	}{
		{
			name: "正常刷新 IP 使用率",
			setupMocks: func(patches *gomonkey.Patches) {
				// 模拟 IP 范围总数
				patches.ApplyMethod(
					(*ipRangesSql.IpRanges)(nil),
					"Count",
					func(_ *ipRangesSql.IpRanges, _ ...mysql.HandleFunc) (int64, error) {
						return 150, nil // 返回 150 条记录，会触发两批处理
					},
				)

				// 模拟第一批 IP 范围数据
				patches.ApplyMethod(
					(*ipRangesSql.IpRanges)(nil),
					"List",
					func(_ *ipRangesSql.IpRanges, page, pageSize int, _ ...mysql.HandleFunc) ([]*ipRangesSql.IpRanges, int64, error) {
						// 根据页码返回不同的数据
						if page == 0 {
							return []*ipRangesSql.IpRanges{
								{
									BaseModel:       mysql.BaseModel{Id: 1},
									NetworkAreasId:  1001, // 使用uint64类型
									IpRange:         "***********/24",
									IpRangeCapacity: 256,
								},
								{
									BaseModel:       mysql.BaseModel{Id: 2},
									NetworkAreasId:  1002, // 使用uint64类型
									IpRange:         "10.0.0.0/24",
									IpRangeCapacity: 256,
								},
							}, 150, nil
						} else {
							return []*ipRangesSql.IpRanges{
								{
									BaseModel:       mysql.BaseModel{Id: 3},
									NetworkAreasId:  1003, // 使用uint64类型
									IpRange:         "**********/24",
									IpRangeCapacity: 256,
								},
							}, 150, nil
						}
					},
				)

				// 模拟 ES 查询结果
				patches.ApplyFunc(
					es.GetCount,
					func(index string, query elastic.Query) (int64, error) {
						// 根据查询条件返回不同的计数
						_, ok := query.(*elastic.BoolQuery)
						if !ok {
							return 0, errors.New("invalid query type")
						}

						// 简化处理，根据不同的 IP 范围返回不同的计数
						if index == assets.NewAssets().IndexName() {
							// 这里可以根据实际查询条件进行更精细的匹配
							// 简化处理，返回固定值
							return 100, nil
						}
						return 0, errors.New("unexpected index")
					},
				)

				// 模拟更新操作
				patches.ApplyMethod(
					(*ipRangesSql.IpRanges)(nil),
					"Update",
					func(_ *ipRangesSql.IpRanges, ipRange *ipRangesSql.IpRanges) error {
						// 验证更新的数据
						assert.NotNil(t, ipRange)
						assert.NotZero(t, ipRange.Id)
						assert.Equal(t, uint64(100), ipRange.IpNumber)
						// 使用率计算: (100/256)*10000 = 3906
						assert.Equal(t, uint(3906), ipRange.IpUsageRate)
						return nil
					},
				)
			},
			expectedErr: nil,
		},
		{
			name: "IP 范围总数为 0",
			setupMocks: func(patches *gomonkey.Patches) {
				// 模拟 IP 范围总数为 0
				patches.ApplyMethod(
					(*ipRangesSql.IpRanges)(nil),
					"Count",
					func(_ *ipRangesSql.IpRanges, _ ...mysql.HandleFunc) (int64, error) {
						return 0, nil
					},
				)
			},
			expectedErr: nil,
		},
	}

	// 遍历测试用例
	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 创建 mock
			patches := gomonkey.NewPatches()
			defer patches.Reset()

			// 设置 mock
			tc.setupMocks(patches)

			// 执行被测试的函数
			err := RefreshIpUsageRate()

			// 验证结果
			if tc.expectedErr != nil {
				assert.Error(t, err)
				assert.Equal(t, tc.expectedErr.Error(), err.Error())
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestParseIpRange(t *testing.T) {
	tests := []struct {
		name            string
		ipRange         string
		expectedErr     bool
		expectedStartIp string
		expectedEndIp   string
	}{
		{
			name:            "正常情况",
			ipRange:         "***********-10",
			expectedErr:     false,
			expectedStartIp: "***********",
			expectedEndIp:   "***********0",
		},
		{
			name:        "IP段格式错误",
			ipRange:     "***********-10-20",
			expectedErr: true,
		},
		{
			name:        "CIDR格式",
			ipRange:     "***********/24",
			expectedErr: true,
		},
		{
			name:        "空值",
			ipRange:     "",
			expectedErr: true,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			startIp, endIp, err := parseIpRange(test.ipRange)
			if test.expectedErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, test.expectedStartIp, startIp)
				assert.Equal(t, test.expectedEndIp, endIp)
			}
		})
	}
}
