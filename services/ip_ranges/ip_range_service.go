package ipranges_service

import (
	"errors"
	"fobrain/fobrain/logs"
	"fobrain/initialize/es"
	"fobrain/initialize/mysql"
	"fobrain/models/elastic/assets"
	ipRangesSql "fobrain/models/mysql/ip_ranges"
	"math"
	"strings"
	"sync"

	"github.com/olivere/elastic/v7"
)

func RefreshIpUsageRate() error {
	logger := logs.GetLogger()
	handlers := make([]mysql.HandleFunc, 0)
	total, err := ipRangesSql.NewIpRangesModel().Count(handlers...)
	if err != nil {
		logger.Errorf("ipRangesSql.NewIpRangesModel().Count err:%v", err)
		return err
	}

	if total == 0 {
		return nil
	}
	batchSize := 100
	totalPages := int(math.Ceil(float64(total) / float64(batchSize)))

	var wg sync.WaitGroup
	for i := 0; i < totalPages; i++ {
		wg.Add(1)
		ipRanges, _, err := ipRangesSql.NewIpRangesModel().List(i, batchSize, handlers...)
		if err != nil {
			return err
		}
		go func(ipRanges []*ipRangesSql.IpRanges) {
			defer wg.Done()
			for _, ipRange := range ipRanges {
				if ipRange.IpRange == "" {
					continue
				}
				ipCount := int64(0)
				// ***********-10格式
				if strings.Contains(ipRange.IpRange, "-") {
					startIp, endIp, err := parseIpRange(ipRange.IpRange)
					if err != nil {
						logger.Errorf("解析ip段失败，ipRange:%s, err:%v", ipRange.IpRange, err)
						continue
					}
					count, err := es.GetCount(assets.NewAssets().IndexName(),
						elastic.NewBoolQuery().
							Must(elastic.NewTermQuery("area", ipRange.NetworkAreasId),
								elastic.NewRangeQuery("ip").From(startIp).To(endIp)))
					if err != nil {
						logger.Errorf("获取ip数量失败，ipRange:%s, err:%v", ipRange.IpRange, err)
						continue
					}
					ipCount = count
				} else {
					// CIDR格式
					count, err := es.GetCount(assets.NewAssets().IndexName(),
						elastic.NewBoolQuery().
							Must(elastic.NewTermQuery("area", ipRange.NetworkAreasId),
								elastic.NewTermQuery("ip", ipRange.IpRange)))
					if err != nil {
						logger.Errorf("获取ip数量失败，ipRange:%s, err:%v", ipRange.IpRange, err)
						continue
					}
					ipCount = count
				}

				ipUpdate := ipRangesSql.IpRanges{
					BaseModel: mysql.BaseModel{
						Id: ipRange.Id,
					},
					IpNumber: uint64(ipCount),
					IpUsageRate: func() uint {
						if ipCount == 0 {
							return 0
						}
						// 计算使用率，取小数点后2位，整数缩放10000
						t1 := float64(ipCount) / float64(ipRange.IpRangeCapacity)
						t1 = math.Round(t1 * 10000)
						return uint(t1)
					}(),
				}
				err = ipRangesSql.NewIpRangesModel().Update(&ipUpdate)
				if err != nil {
					logger.Errorf("ipRangesSql.NewIpRangesModel().Update err:%v", err)
					continue
				}
			}
		}(ipRanges)
	}
	wg.Wait()
	return nil
}

// parseIpRange 解析ip段，返回起始ip和结束ip
// 支持格式：***********-10
func parseIpRange(ipRange string) (string, string, error) {
	ipRange = strings.TrimSpace(ipRange)
	if ipRange == "" {
		return "", "", errors.New("ipRange is empty")
	}
	// 解析 ***********-10格式
	parts := strings.Split(ipRange, "-")
	if len(parts) != 2 {
		return "", "", errors.New("ipRange format is invalid")
	}
	startIp := parts[0]
	// 获取startIp的前32位
	startIpPrefix := strings.Split(startIp, ".")[0] + "." + strings.Split(startIp, ".")[1] + "." + strings.Split(startIp, ".")[2] + "."
	// 拼接startIpPrefix和parts[1]
	endIp := startIpPrefix + parts[1]
	return startIp, endIp, nil
}
