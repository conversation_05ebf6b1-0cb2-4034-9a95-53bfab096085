package pgidservice_test

import (
	"context"
	"fobrain/initialize/redis"
	"fobrain/models/elastic/staff"
	pgidservice "fobrain/services/people_pgid"
	"reflect"
	"testing"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/alicebob/miniredis/v2"
	redis2 "github.com/go-redis/redis/v8"
)

// Test GetPgidSetting function
func TestGetPgidSetting(t *testing.T) {
	// Setup mock redis client
	s := miniredis.RunT(t)
	defer s.Close()
	client := redis2.NewClient(&redis2.Options{Addr: s.Addr()})
	patches := gomonkey.NewPatches()
	defer patches.Reset()
	patches.ApplyFuncReturn(redis.GetRedisClient, client)
	patches.ApplyFuncReturn(pgidservice.GetPgidSettingFromDB, "value_from_db", nil)

	tests := []struct {
		name          string
		setUp         func()
		mockRedisData map[string]string
		mockRedisErr  error
		expected      string
	}{
		{
			name:          "Redis hit",
			setUp:         func() { client.Set(context.Background(), "cache:people:pgid", "value_from_redis", 0) },
			mockRedisData: map[string]string{"cache:people:pgid": "value_from_redis"},
			expected:      "value_from_redis",
		},
		{
			name:          "Redis miss, DB success",
			setUp:         func() { client.Del(context.Background(), "cache:people:pgid") },
			mockRedisData: map[string]string{},
			expected:      "value_from_db",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.setUp != nil {
				tt.setUp()
			}
			// Call the function
			result := pgidservice.GetPgidSetting()

			// Assert the result
			if result != tt.expected {
				t.Errorf("expected %v, got %v", tt.expected, result)
			}
		})
	}
}

// Test GetPeopleBaseInfo function
func TestGetPeopleBaseInfo(t *testing.T) {
	// Setup mock redis client
	s := miniredis.RunT(t)
	defer s.Close()
	client := redis2.NewClient(&redis2.Options{Addr: s.Addr()})
	patches := gomonkey.NewPatches()
	defer patches.Reset()
	patches.ApplyFuncReturn(redis.GetRedisClient, client)
	patches.ApplyMethodReturn(staff.NewStaff(), "GetById", &staff.Staff{
		Name:        "John Doe",
		Mobile:      "123456789",
		Email:       []string{"<EMAIL>"},
		WorkNumber:  "001",
		EnglishName: []string{"John"},
		Title:       []string{"Engineer"},
	}, nil)

	tests := []struct {
		name          string
		setUp         func()
		mockRedisData map[string]string
		mockRedisErr  error
		expected      map[string]string
	}{
		{
			name: "Redis hit",
			setUp: func() {
				client.Set(context.Background(), "cache:people:baseinfo:123", `{"name":"John Doe","mobile":"123456789"}`, 0)
			},
			mockRedisData: map[string]string{"cache:people:baseinfo:123": `{"name":"John Doe","mobile":"123456789"}`},
			expected:      map[string]string{"name": "John Doe", "mobile": "123456789"},
		},
		{
			name: "Redis miss, DB success",
			setUp: func() {
				client.Del(context.Background(), "cache:people:baseinfo:123")
			},
			mockRedisData: map[string]string{},
			expected:      map[string]string{"name": "John Doe", "mobile": "123456789"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.setUp != nil {
				tt.setUp()
			}
			// Call the function
			result, err := pgidservice.GetPeopleBaseInfo("123")

			// Assert the result
			if tt.mockRedisErr == nil && result["name"] != tt.expected["name"] {
				t.Errorf("expected %v, got %v", tt.expected, result)
			} else if tt.mockRedisErr != nil && err == nil {
				t.Errorf("expected error, got nil")
			}
		})
	}
}

// Test GetPeopleBaseInfoFromStaffModel function
func TestGetPeopleBaseInfoFromStaffModel(t *testing.T) {
	tests := []struct {
		name     string
		input    *staff.Staff
		expected map[string]string
	}{
		{
			name: "Valid staff model",
			input: &staff.Staff{
				Name:        "John Doe",
				Mobile:      "123456789",
				Email:       []string{"<EMAIL>"},
				WorkNumber:  "001",
				EnglishName: []string{"John"},
				Title:       []string{"Engineer"},
			},
			expected: map[string]string{
				"name":         "John Doe",
				"mobile":       "123456789",
				"email":        "<EMAIL>",
				"work_number":  "001",
				"english_name": "John",
				"title":        "Engineer",
			},
		},
		{
			name:     "Nil staff model",
			input:    nil,
			expected: map[string]string{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := pgidservice.GetPeopleBaseInfoFromStaffModel(tt.input)

			// Assert the result
			if !reflect.DeepEqual(result, tt.expected) {
				t.Errorf("expected %v, got %v", tt.expected, result)
			}
		})
	}
}
