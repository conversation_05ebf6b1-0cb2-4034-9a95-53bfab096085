package pgidservice

import (
	"context"
	"encoding/json"
	"fmt"
	"fobrain/fobrain/logs"
	"fobrain/initialize/redis"
	"fobrain/models/elastic/staff"
	"fobrain/models/mysql/system_configs"
	redis_helper "fobrain/models/redis"
	"strings"
	"time"
)

// GetPeoplePgid 获取PGID设置，优先从redis中获取
func GetPgidSetting() string {
	// 从redis中获取
	redisKey := redis_helper.GetPeoplePgidKey()
	redisClient := redis.GetRedisClient()
	value, err := redisClient.Get(context.Background(), redisKey).Result()
	if err != nil {
		// 从数据库中获取
		value, err = GetPgidSettingFromDB()
		if err != nil {
			return ""
		}
		// 缓存到redis
		err = redisClient.Set(context.Background(), redisKey, value, 0).Err()
		if err != nil {
			logs.GetLogger().Warnf("获取people_pgid失败", "error", err)
		}
	}
	return value
}

// GetPgidSettingFromDB 从数据库中获取PGID设置
func GetPgidSettingFromDB() (string, error) {
	key := "people_pgid"
	value, err := system_configs.NewSystemConfigs().GetConfig(key)
	if err != nil {
		logs.GetLogger().Warnf("Error while getting people_pgid", "error", err)
		return "", err
	}
	return value, nil
}

func ClearCache(id string) {
	// 删除缓存
	redisClient := redis.GetRedisClient()
	redisKey := redis_helper.GetPeopleBaseInfoKey(id)
	err := redisClient.Del(context.Background(), redisKey).Err()
	if err != nil {
		logs.GetLogger().Warnf("删除people_base_info缓存失败，error: %v", err)
	}
}

// CachePeopleBaseInfo 缓存人员基本信息
func CachePeopleBaseInfo(id string, peopleBaseInfo *staff.Staff) (map[string]string, error) {
	// 缓存到redis
	redisClient := redis.GetRedisClient()
	redisKey := redis_helper.GetPeopleBaseInfoKey(id)
	defer func() {
		if r := recover(); r != nil {
			// 如果缓存失败，删除缓存
			redisClient.Del(context.Background(), redisKey)
		}
	}()
	cacheData := GetPeopleBaseInfoFromStaffModel(peopleBaseInfo)
	// 序列化
	jsonCacheData, err := json.Marshal(cacheData)
	if err != nil {
		logs.GetLogger().Warnf("序列化people_base_info失败，error: %v", err)
		return cacheData, err
	}
	err = redisClient.Set(context.Background(), redisKey, jsonCacheData, 15*time.Minute).Err()
	if err != nil {
		logs.GetLogger().Warnf("设置people_base_info缓存失败，error: %v", err)
		return cacheData, err
	}
	return cacheData, nil
}

// GetPeopleBaseInfo 获取人员基本信息，优先从redis中获取
func GetPeopleBaseInfo(id string) (map[string]string, error) {
	redisClient := redis.GetRedisClient()
	redisKey := redis_helper.GetPeopleBaseInfoKey(id)
	// 从redis中获取
	value, err := redisClient.Get(context.Background(), redisKey).Result()
	if err != nil {
		// 从数据库中获取
		staffModel, err := staff.NewStaff().GetById(context.Background(), id)
		if err != nil {
			return nil, err
		}
		baseInfo, err := CachePeopleBaseInfo(id, staffModel)
		if err != nil {
			return nil, err
		}
		return baseInfo, nil
	}
	var peopleBaseInfo map[string]string
	err = json.Unmarshal([]byte(value), &peopleBaseInfo)
	if err != nil {
		logs.GetLogger().Warnf("解析people_base_info失败，error: %v", err)
		return nil, err
	}
	return peopleBaseInfo, nil
}

// GetPeopleBaseInfoByFid 获取人员基本信息，优先从redis中获取
func GetPeopleBaseInfoByFid(fid string) (map[string]string, error) {
	// 从数据库中获取
	staffModel, err := staff.NewStaff().GetByFId(context.Background(), fid)
	if err != nil {
		return nil, err
	}
	if staffModel == nil {
		return nil, fmt.Errorf("staff not found")
	}
	baseInfo, err := CachePeopleBaseInfo(staffModel.Id, staffModel)
	if err != nil {
		return nil, err
	}
	return baseInfo, nil
}

// GetPeopleBaseInfoFromStaffModel 获取人员基本信息
func GetPeopleBaseInfoFromStaffModel(staffModel *staff.Staff) map[string]string {
	cacheData := make(map[string]string)
	if staffModel == nil {
		return cacheData
	}
	cacheData["name"] = staffModel.Name
	cacheData["mobile"] = staffModel.Mobile
	cacheData["email"] = strings.Join(staffModel.Email, ",")
	cacheData["work_number"] = staffModel.WorkNumber
	cacheData["english_name"] = strings.Join(staffModel.EnglishName, ",")
	cacheData["title"] = strings.Join(staffModel.Title, ",")
	return cacheData
}

func getPgid(identifier string, isFid bool, format ...string) (string, error) {
	if identifier == "" {
		return "", nil
	}
	// 默认格式:%s-%s
	formatStr := "%s-%s"
	if len(format) > 0 {
		formatStr = format[0]
	}

	var baseInfo map[string]string
	var err error

	// 根据是否为Fid选择获取基本信息的方法
	if isFid {
		baseInfo, err = GetPeopleBaseInfoByFid(identifier)
	} else {
		baseInfo, err = GetPeopleBaseInfo(identifier)
	}
	if err != nil {
		return "", err
	}

	// 获取pgid设置
	pgidSetting := GetPgidSetting()
	if pgidSetting == "" || pgidSetting == "name" {
		return baseInfo["name"], nil
	}
	// 从基本信息中获取pgid部分并组成pgid字符串
	if info, ok := baseInfo[pgidSetting]; ok && info != "" {
		return fmt.Sprintf(formatStr, baseInfo["name"], info), nil
	}
	return baseInfo["name"], nil
}

// GetPgidById 获取PGID
// @peopleId 人员id
// @format 可选参数，PGID格式，接受两个字符串拼接，第一个字符串是人名，第二个字符串是动态值，可以为空，默认:%s-%s
func GetPgidById(peopleId string, format ...string) (string, error) {
	return getPgid(peopleId, false, format...)
}

// GetPgidByFid 获取PGID
func GetPgidByFid(peopleFid string, format ...string) (string, error) {
	return getPgid(peopleFid, true, format...)
}
