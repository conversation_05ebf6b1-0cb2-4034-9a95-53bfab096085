package strategy_business

import (
	"context"
	"encoding/json"
	"fobrain/pkg/utils"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/alicebob/miniredis/v2"
	"github.com/go-redis/redis/v8"

	testcommon "fobrain/fobrain/tests/common_test"
	pb "fobrain/mergeService/proto"
	"fobrain/models/elastic/assets"
	"fobrain/models/elastic/business_system"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/olivere/elastic/v7"
	"github.com/stretchr/testify/assert"
	"github.com/tidwall/sjson"
)

func TestUpSet(t *testing.T) {
	// Setup miniredis
	s, err := miniredis.Run()
	if err != nil {
		t.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
	}
	defer s.Close()

	cli := redis.NewClient(&redis.Options{
		Addr: s.Addr(),
	})
	testcommon.SetRedisClient(cli)

	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()
	srouceBusiness := &SourceBusiness{
		Address:        "https://127.0.0.1",
		SystemVersion:  "2.0",
		IsGj:           ptrInt(1), //是否关基设施：''-空/ 1-是/ 2-否
		IsXc:           ptrInt(1), //是否信创：''- 空/ 1-是/ 2- 否
		PurchaseType:   ptrInt(1), //采购类型：''-空/ 1-自研/ 2-外包/ 3-第三方采购
		ImportantTypes: ptrInt(1), //重要性 ''-空/ 1-非常重要/ 2- 重要/ 3- 一般
		InsuranceLevel: ptrInt(1), //等保级别: ''- 空/ 1-一级/2- 二级/3- 三级
		OperatingEnv:   nil,       //运行环境 1-生产环境/2-开发环境
		RunningState:   nil,       //运行状态 1-运行中/ 0-已下线
		//管理信息
		UserId: 0, //操作人
	}
	tests := []struct {
		businessName   string
		from           int
		sourceBusiness *SourceBusiness
		input          []*elastic.SearchHit
		res            any
	}{
		{
			businessName:   "test",
			from:           1,
			sourceBusiness: srouceBusiness,
			input:          []*elastic.SearchHit{},
			res: &assets.Business{
				System:   "test",
				SystemId: utils.Md5bHash([]byte("test"), false),
				Owner:    "",
				OwnerId:  "",
				Source:   "",
				BusinessInfo: &business_system.BusinessSystems{
					BaseModel:       nil,
					Id:              utils.Md5bHash([]byte("test"), false),
					TagId:           0,
					Status:          2,
					From:            1,
					Address:         "https://127.0.0.1",
					FromMark:        0,
					ContinuityLevel: 0,
					UseMark:         "",
					Fid:             utils.Md5bHash([]byte("test"), false),
					BusinessName:    "test",
					AssetsAttribute: business_system.AssetsAttribute{
						IsGj:           ptrInt(1), //是否关基设施：''-空/ 1-是/ 2-否
						IsXc:           ptrInt(1), //是否信创：''- 空/ 1-是/ 2- 否
						PurchaseType:   ptrInt(1), //采购类型：''-空/ 1-自研/ 2-外包/ 3-第三方采购
						ImportantTypes: ptrInt(1), //重要性 ''-空/ 1-非常重要/ 2- 重要/ 3- 一般
						InsuranceLevel: ptrInt(1), //等保级别: ''- 空/ 1-一级/2- 二级/3- 三级
						OperatingEnv:   nil,       //运行环境 1-生产环境/2-开发环境
						RunningState:   nil,       //运行状态 1-运行中/ 0-已下线
					},
					SystemVersion: "2.0",
					CreatedAt:     nil,
					DeletedAt:     nil,
					UpdatedAt:     nil,
				},
				Addition:             "",
				BusinessFrom:         1,
				BusinessTrustedState: 2,
			},
		},
	}
	client := pb.GetProtoClient() // 假设这是一个结构体实例
	patches := gomonkey.ApplyMethodReturn(client, "TriggerMergeForAsset", &pb.TriggerMergeResponse{
		Success: true,
	}, nil)
	defer patches.Reset()
	for _, tt := range tests {
		mockRows := sqlmock.NewRows([]string{"id", "data_source_name", "weight"}).
			AddRow(1, "data1", 10).
			AddRow(2, "data2", 20)
		mockServer.Register("/staff/_search", []*elastic.SearchHit{})
		// 模拟查询操作
		mockDb.ExpectQuery("SELECT * FROM `business_strategies` WHERE weight != ? ORDER BY weight ASC").
			WillReturnRows(mockRows)

		mockServer.Register("/business_systems/_search", tt.input)
		mockServer.Register("_bulk", &elastic.BulkIndexByScrollResponse{
			Updated: 1,
		})
		mockServer.Register("/asset/_search", []*elastic.SearchHit{})
		data, err := NewStrategy().UpSet(tt.businessName, tt.from, tt.sourceBusiness)
		dataJs, _ := json.Marshal(data)
		resJs, _ := json.Marshal(tt.res)
		dataS, _ := sjson.Set(string(dataJs), "business_info.created_at", nil)
		dataS, _ = sjson.Set(dataS, "business_info.updated_at", nil)
		assert.Nil(t, err)
		dataS, _ = sjson.Delete(dataS, "business_info.person_base")
		dataS, _ = sjson.Delete(dataS, "person_base")
		dataS, _ = sjson.Delete(dataS, "business_info.department_base")
		dataS, _ = sjson.Delete(dataS, "department_base")
		resS := string(resJs)
		resS, _ = sjson.Delete(resS, "business_info.person_base")
		resS, _ = sjson.Delete(resS, "person_base")
		resS, _ = sjson.Delete(resS, "business_info.department_base")
		resS, _ = sjson.Delete(resS, "department_base")
		assert.Equal(t, resS, dataS)
	}
}

func TestProcessByStrategy(t *testing.T) {
	// 设置 MySQL mock
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()
	mockRows := sqlmock.NewRows([]string{"id", "data_source_name", "weight"}).
		AddRow(1, "data1", 10).
		AddRow(2, "data2", 20)

	// 模拟查询业务策略
	mockDb.ExpectQuery("SELECT * FROM `business_strategies` WHERE weight != ? ORDER BY weight ASC").
		WillReturnRows(mockRows)

	// 设置 Redis mock
	s, err := miniredis.Run()
	if err != nil {
		t.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
	}
	defer s.Close()

	cli := redis.NewClient(&redis.Options{
		Addr: s.Addr(),
	})
	testcommon.SetRedisClient(cli)

	// 设置 ES mock
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	// 测试用例
	tests := []struct {
		name           string
		businessName   string
		from           int
		sourceBusiness *SourceBusiness
		res            *business_system.BusinessSystems
		mockSetup      func()
		expectUpdated  bool
	}{
		{
			name:         "新业务系统-手动添加",
			businessName: "test",
			from:         business_system.SourceStatusHandleAndImport,
			sourceBusiness: &SourceBusiness{
				Address:        "https://127.0.0.1",
				SystemVersion:  "2.0",
				IsGj:           ptrInt(1), // 是否关基设施：''-空/ 1-是/ 2-否
				IsXc:           ptrInt(1), // 是否信创：''- 空/ 1-是/ 2- 否
				PurchaseType:   ptrInt(1), // 采购类型：''-空/ 1-自研/ 2-外包/ 3-第三方采购
				ImportantTypes: ptrInt(1), // 重要性 ''-空/ 1-非常重要/ 2- 重要/ 3- 一般
				InsuranceLevel: ptrInt(1), // 等保级别: ''- 空/ 1-一级/2- 二级/3- 三级
				OperatingEnv:   nil,       // 运行环境 1-生产环境/2-开发环境
				RunningState:   nil,       // 运行状态 1-运行中/ 0-已下线
				UserId:         0,         // 操作人
			},
			res: &business_system.BusinessSystems{
				Id:              utils.Md5bHash([]byte("test"), false),
				TagId:           0,
				Status:          business_system.TrustStatusYesOrNo, // 新业务系统默认是可疑状态(2)
				From:            business_system.SourceStatusHandleAndImport,
				FromMark:        0,
				IntranetIps:     nil,
				InternetIps:     nil,
				ContinuityLevel: 0,
				Fid:             utils.Md5bHash([]byte("test"), false),
				BusinessName:    "test",
				Address:         "https://127.0.0.1", // 应该使用源业务的地址
				AssetsAttribute: business_system.AssetsAttribute{
					IsGj:           ptrInt(1),
					IsXc:           ptrInt(1),
					PurchaseType:   ptrInt(1),
					ImportantTypes: ptrInt(1),
					InsuranceLevel: ptrInt(1),
					OperatingEnv:   nil,
					RunningState:   nil,
				},
				SystemVersion: "2.0",
			},
			mockSetup: func() {
				// 模拟没有找到现有业务系统
				mockServer.Register("/business_systems/_search", []*elastic.SearchHit{})

				// 模拟业务策略缓存
				cacheData := []int{1, 2, 3}
				cacheDataJSON, _ := json.Marshal(cacheData)
				gomonkey.ApplyMethodFunc(testcommon.GetRedisClient(), "Get", func(ctx context.Context, key string) *redis.StringCmd {
					cmd := redis.NewStringCmd(ctx)
					cmd.SetVal(string(cacheDataJSON))
					return cmd
				})

				// 模拟ES创建成功
				mockServer.Register("_bulk", &elastic.BulkIndexByScrollResponse{
					Updated: 1,
				})
			},
			expectUpdated: true,
		},
		{
			name:         "资产属性提取-数据源来源",
			businessName: "test",
			from:         business_system.SourceStatusAsset,
			sourceBusiness: &SourceBusiness{
				Address:        "https://127.0.0.1",
				SystemVersion:  "2.0",
				IsGj:           ptrInt(1),
				IsXc:           ptrInt(1),
				PurchaseType:   ptrInt(1),
				ImportantTypes: ptrInt(1),
				InsuranceLevel: ptrInt(1),
				OperatingEnv:   nil,
				RunningState:   nil,
				UserId:         0,
				Source:         6,
			},
			res: &business_system.BusinessSystems{
				Id:              "test",
				TagId:           0,
				Status:          business_system.TrustStatusYes,
				From:            1,
				FromMark:        0,
				IntranetIps:     []string{"************"},
				InternetIps:     nil,
				ContinuityLevel: 0,
				Fid:             "test",
				BusinessName:    "test",
				AssetsAttribute: business_system.AssetsAttribute{
					IsGj:           ptrInt(1),
					IsXc:           ptrInt(1),
					PurchaseType:   ptrInt(1),
					ImportantTypes: ptrInt(1),
					InsuranceLevel: ptrInt(1),
					OperatingEnv:   nil, // 保留原来的值
					RunningState:   nil,
				},
				SystemVersion: "2.0",
			},
			mockSetup: func() {
				// 模拟已有业务系统
				mockServer.Register("/business_systems/_search", []*elastic.SearchHit{
					{
						Id: "test",
						Source: []byte(`{
					  "id": "test",
					  "fid": "test",
					  "business_name": "test",
					  "business_app_principal": "",
					  "intranet_ips": ["************"],
					  "assets_attribute": {
						"is_gj": 1,
						"is_xc": 1,
						"purchase_type": 1,
						"important_types": 1,
						"insurance_level": 1
					  },
					  "system_version": "2.0",
					  "operating_env": 1,
					  "from":1,
					  "status":1,
					  "source":0
				}`),
					},
				})

				// 模拟业务策略缓存
				cacheData := []int{1, 2, 3}
				cacheDataJSON, _ := json.Marshal(cacheData)
				gomonkey.ApplyMethodFunc(testcommon.GetRedisClient(), "Get", func(ctx context.Context, key string) *redis.StringCmd {
					cmd := redis.NewStringCmd(ctx)
					cmd.SetVal(string(cacheDataJSON))
					return cmd
				})

				// 模拟ES更新成功
				mockServer.Register("_bulk", &elastic.BulkIndexByScrollResponse{
					Updated: 1,
				})
			},
			expectUpdated: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 设置测试的 mock
			if tt.mockSetup != nil {
				// 应用测试用例特定的 mock 设置
				tt.mockSetup()
			}

			// 执行测试
			nBusiness, _, err := NewStrategy().processByStrategy(tt.businessName, tt.from, tt.sourceBusiness)

			// 验证结果
			assert.Nil(t, err, "处理业务系统时出错")
			assert.NotNil(t, nBusiness, "返回的业务系统不应为空")

			// 清除时间戳字段以便比较
			if nBusiness != nil {
				nBusiness.CreatedAt = nil
				nBusiness.UpdatedAt = nil
				nBusiness.DeletedAt = nil
			}

			// 转换为JSON进行比较
			dataJs, _ := json.Marshal(nBusiness)
			resJs, _ := json.Marshal(tt.res)

			// 使用JSON字符串比较，更容易查看差异
			assert.JSONEq(t, string(resJs), string(dataJs), "返回的业务系统与预期不符")
		})
	}
}

func TestAssemble(t *testing.T) {
	// Setup miniredis
	s, err := miniredis.Run()
	if err != nil {
		t.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
	}
	defer s.Close()

	cli := redis.NewClient(&redis.Options{
		Addr: s.Addr(),
	})
	testcommon.SetRedisClient(cli)

	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()
	tests := []struct {
		businessName   string
		from           int
		oldBusiness    *business_system.BusinessSystems
		sourceBusiness *SourceBusiness
		res            *business_system.BusinessSystems
	}{
		{
			businessName: "test",
			from:         1,
			oldBusiness: &business_system.BusinessSystems{
				IntranetIps: []string{"***********"},
				InternetIps: []string{"*******"},
				Id:          utils.Md5bHash([]byte("test"), false),
			},
			sourceBusiness: &SourceBusiness{
				Address:        "https://127.0.0.1",
				SystemVersion:  "v1.1",
				IsGj:           ptrInt(1), //是否关基设施：''-空/ 1-是/ 2-否
				IsXc:           ptrInt(1), //是否信创：''- 空/ 1-是/ 2- 否
				PurchaseType:   ptrInt(1), //采购类型：''-空/ 1-自研/ 2-外包/ 3-第三方采购
				ImportantTypes: ptrInt(1), //重要性 ''-空/ 1-非常重要/ 2- 重要/ 3- 一般
				InsuranceLevel: ptrInt(1), //等保级别: ''- 空/ 1-一级/2- 二级/3- 三级
				OperatingEnv:   nil,       //运行环境 1-生产环境/2-开发环境
				RunningState:   nil,       //运行状态 1-运行中/ 0-已下线
				//管理信息
				UserId: 0, //操作人
				Source: 6,
			},
			res: &business_system.BusinessSystems{
				Id:              utils.Md5bHash([]byte("test"), false),
				TagId:           0,
				Status:          business_system.TrustStatusYesOrNo, // 默认是可疑状态(2)
				From:            1,
				FromMark:        0,
				IntranetIps:     nil, // assemble 方法不会处理 IP
				InternetIps:     nil, // assemble 方法不会处理 IP
				ContinuityLevel: 0,
				Fid:             utils.Md5bHash([]byte("test"), false),
				BusinessName:    "test",
				Address:         "https://127.0.0.1", // 使用源业务的地址
				AssetsAttribute: business_system.AssetsAttribute{
					IsGj:           ptrInt(1), // 是否关基设施：''-空/ 1-是/ 2-否
					IsXc:           ptrInt(1), // 是否信创：''- 空/ 1-是/ 2- 否
					PurchaseType:   ptrInt(1), // 采购类型：''-空/ 1-自研/ 2-外包/ 3-第三方采购
					ImportantTypes: ptrInt(1), // 重要性 ''-空/ 1-非常重要/ 2- 重要/ 3- 一般
					InsuranceLevel: ptrInt(1), // 等保级别: ''- 空/ 1-一级/2- 二级/3- 三级
					OperatingEnv:   nil,       // 运行环境 1-生产环境/2-开发环境
					RunningState:   nil,       // 运行状态 1-运行中/ 0-已下线
				},
				SystemVersion: "v1.1", // 使用源业务的系统版本
			},
		},
		{
			businessName: "test-oldBusiness nil",
			from:         1,
			oldBusiness:  nil,
			sourceBusiness: &SourceBusiness{
				Address:        "https://127.0.0.1",
				SystemVersion:  "v1.1",
				IsGj:           ptrInt(1), //是否关基设施：''-空/ 1-是/ 2-否
				IsXc:           ptrInt(1), //是否信创：''- 空/ 1-是/ 2- 否
				PurchaseType:   ptrInt(1), //采购类型：''-空/ 1-自研/ 2-外包/ 3-第三方采购
				ImportantTypes: ptrInt(1), //重要性 ''-空/ 1-非常重要/ 2- 重要/ 3- 一般
				InsuranceLevel: ptrInt(1), //等保级别: ''- 空/ 1-一级/2- 二级/3- 三级
				OperatingEnv:   nil,       //运行环境 1-生产环境/2-开发环境
				RunningState:   nil,       //运行状态 1-运行中/ 0-已下线
				//管理信息
				UserId: 0, //操作人
				Source: 6,
			},
			res: &business_system.BusinessSystems{
				Id:              utils.Md5bHash([]byte("test-oldBusiness nil"), false),
				TagId:           0,
				Status:          business_system.TrustStatusYesOrNo, // 默认是可疑状态(2)
				From:            1,
				FromMark:        0,
				IntranetIps:     nil, // assemble 方法不会处理 IP
				InternetIps:     nil, // assemble 方法不会处理 IP
				ContinuityLevel: 0,
				Fid:             utils.Md5bHash([]byte("test-oldBusiness nil"), false),
				BusinessName:    "test-oldBusiness nil",
				Address:         "https://127.0.0.1", // 使用源业务的地址
				AssetsAttribute: business_system.AssetsAttribute{
					IsGj:           ptrInt(1), // 是否关基设施：''-空/ 1-是/ 2-否
					IsXc:           ptrInt(1), // 是否信创：''- 空/ 1-是/ 2- 否
					PurchaseType:   ptrInt(1), // 采购类型：''-空/ 1-自研/ 2-外包/ 3-第三方采购
					ImportantTypes: ptrInt(1), // 重要性 ''-空/ 1-非常重要/ 2- 重要/ 3- 一般
					InsuranceLevel: ptrInt(1), // 等保级别: ''- 空/ 1-一级/2- 二级/3- 三级
					OperatingEnv:   nil,       // 运行环境 1-生产环境/2-开发环境
					RunningState:   nil,       // 运行状态 1-运行中/ 0-已下线
				},
				SystemVersion: "v1.1", // 使用源业务的系统版本
			},
		},
	}
	for _, tt := range tests {
		// 模拟 MySQL
		mockDb := testcommon.GetMysqlMock()
		defer mockDb.Close()
		mockRows := sqlmock.NewRows([]string{"id", "data_source_name", "weight"}).
			AddRow(1, "data1", 10).
			AddRow(2, "data2", 20)

		// 模拟查询业务策略
		mockDb.ExpectQuery("SELECT * FROM `business_strategies` WHERE weight != ? ORDER BY weight ASC").
			WillReturnRows(mockRows)

		// 模拟 Redis
		cacheData := []int{1, 2, 3}
		cacheDataJSON, _ := json.Marshal(cacheData)
		// 使用 _ 忽略返回值，避免未使用变量的警告
		_ = gomonkey.ApplyMethodFunc(testcommon.GetRedisClient(), "Get", func(ctx context.Context, key string) *redis.StringCmd {
			cmd := redis.NewStringCmd(ctx)
			cmd.SetVal(string(cacheDataJSON)) // 模拟缓存数据
			return cmd
		})
		nBusiness := NewStrategy().assemble(tt.businessName, tt.from, tt.oldBusiness, tt.sourceBusiness)
		nBusiness.CreatedAt = nil
		nBusiness.UpdatedAt = nil
		dataJs, _ := json.Marshal(nBusiness)
		resJs, _ := json.Marshal(tt.res)
		dataS := string(dataJs)
		resS := string(resJs)
		dataS, _ = sjson.Delete(dataS, "person_base")
		dataS, _ = sjson.Delete(dataS, "department_base")
		resS, _ = sjson.Delete(resS, "person_base")
		resS, _ = sjson.Delete(resS, "department_base")
		assert.Equal(t, resS, dataS)
		// 注意：不要尝试重置已经不存在的mock，这会导致空指针异常
	}
}

// Helper function to create pointer to int
func ptrInt(i int) *int {
	return &i
}

// TestIsSliceEqual 测试isSliceEqual方法
func TestIsSliceEqual(t *testing.T) {
	tests := []struct {
		name     string
		a        interface{}
		b        interface{}
		expected bool
	}{
		{
			name:     "两个nil切片",
			a:        nil,
			b:        nil,
			expected: true,
		},
		{
			name:     "一个nil切片和一个空切片",
			a:        nil,
			b:        []string{},
			expected: false,
		},
		{
			name:     "两个空切片",
			a:        []string{},
			b:        []string{},
			expected: true,
		},
		{
			name:     "相同的字符串切片",
			a:        []string{"a", "b", "c"},
			b:        []string{"a", "b", "c"},
			expected: true,
		},
		{
			name:     "不同的字符串切片",
			a:        []string{"a", "b", "c"},
			b:        []string{"a", "b", "d"},
			expected: false,
		},
		{
			name:     "不同长度的字符串切片",
			a:        []string{"a", "b"},
			b:        []string{"a", "b", "c"},
			expected: false,
		},
		{
			name:     "相同的整数切片",
			a:        []int{1, 2, 3},
			b:        []int{1, 2, 3},
			expected: true,
		},
		{
			name:     "嵌套切片相同",
			a:        [][]string{{"", "a"}, {"b", "c"}},
			b:        [][]string{{"", "a"}, {"b", "c"}},
			expected: true,
		},
		{
			name:     "嵌套切片不同",
			a:        [][]string{{"", "a"}, {"b", "c"}},
			b:        [][]string{{"", "a"}, {"b", "d"}},
			expected: false,
		},
		{
			name:     "非切片类型",
			a:        "not a slice",
			b:        []string{"a"},
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := isSliceEqual(tt.a, tt.b)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestShouldUpdateBasedOnPriority(t *testing.T) {
	tests := []struct {
		oldFrom int
		newFrom int
		res     bool
	}{
		{1, 1, true},
		{1, 2, false},
		{2, 1, true},
		{1, 3, false},
	}
	for _, tt := range tests {
		assert.Equal(t, tt.res, shouldUpdateBasedOnPriority(tt.oldFrom, tt.newFrom, []int{1, 2, 3, 6}))
	}
}

func TestProcessByStrategyErr(t *testing.T) {
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()
	mockRows := sqlmock.NewRows([]string{"id", "data_source_name", "weight"}).
		AddRow(1, "data1", 10).
		AddRow(2, "data2", 20)

	// 模拟查询操作
	mockDb.ExpectQuery("SELECT * FROM `business_strategies` WHERE weight != ? ORDER BY weight ASC").
		WillReturnRows(mockRows)
	// Setup miniredis
	s, err := miniredis.Run()
	if err != nil {
		t.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
	}
	defer s.Close()

	cli := redis.NewClient(&redis.Options{
		Addr: s.Addr(),
	})
	testcommon.SetRedisClient(cli)

	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()
	tests := []struct {
		businessName   string
		from           int
		sourceBusiness *SourceBusiness
		res            *business_system.BusinessSystems
	}{
		{
			//from 1 status 1
			businessName: "testaaa",
			from:         1,
			sourceBusiness: &SourceBusiness{
				Address: "https://127.0.0.1",
				State:   1,
			},
			res: &business_system.BusinessSystems{
				Id:          "test",
				Fid:         "test",
				Status:      1,
				From:        1,
				IntranetIps: []string{},
				InternetIps: []string{},

				BusinessName: "testaaa",
				Address:      "https://127.0.0.1",
			},
		},
	}
	for _, tt := range tests {
		// 模拟 Redis
		cacheData := []int{1}
		cacheDataJSON, _ := json.Marshal(cacheData)
		mockRedisGet := gomonkey.ApplyMethodFunc(testcommon.GetRedisClient(), "Get", func(ctx context.Context, key string) *redis.StringCmd {
			cmd := redis.NewStringCmd(ctx)
			cmd.SetVal(string(cacheDataJSON)) // 模拟缓存数据
			return cmd
		})
		defer mockRedisGet.Reset()
		mockServer.Register("/business_systems/_search", []*elastic.SearchHit{
			{
				Id: "test",
				Source: []byte(`{
				  "id": "test",
				  "fid": "test",
				  "business_name": "testaaa",
				  "business_app_principal": "",
                  "intranet_ips": [],
                  "internet_ips": [],
				  "assets_attribute": {
					"is_gj": 1,
					"is_xc": 1,
					"purchase_type": 1,
					"important_types": 1,
					"insurance_level": 1
				  },
				  "system_version": "2.0",
				  "operating_env": 1,
                  "from":3,
                  "status":2,
                  "source":0
			}`),
			},
		})
		mockServer.Register("_bulk", &elastic.BulkIndexByScrollResponse{
			Updated: 1,
		})
		nBusiness, _, err := NewStrategy().processByStrategy(tt.businessName, tt.from, tt.sourceBusiness)
		assert.Nil(t, err)
		nBusiness.CreatedAt = nil
		nBusiness.UpdatedAt = nil
		dataJs, _ := json.Marshal(nBusiness)
		resJs, _ := json.Marshal(tt.res)
		dataS := string(dataJs)
		resS := string(resJs)
		dataS, _ = sjson.Delete(dataS, "person_base")
		dataS, _ = sjson.Delete(dataS, "department_base")
		resS, _ = sjson.Delete(resS, "person_base")
		resS, _ = sjson.Delete(resS, "department_base")
		dataS, _ = sjson.Delete(resS, "id")
		resS, _ = sjson.Delete(resS, "id")
		assert.Equal(t, resS, dataS)
	}
}

func TestIsSliceEqual1(t *testing.T) {
	tests := []struct {
		name     string
		a        interface{}
		b        interface{}
		expected bool
	}{
		{
			name:     "两个nil切片",
			a:        nil,
			b:        nil,
			expected: true,
		},
		{
			name:     "一个nil切片和一个空切片",
			a:        nil,
			b:        []int{},
			expected: false,
		},
		{
			name:     "两个空切片",
			a:        []int{},
			b:        []int{},
			expected: true,
		},
		{
			name:     "相同的整数切片",
			a:        []int{1, 2, 3},
			b:        []int{1, 2, 3},
			expected: true,
		},
		{
			name:     "不同的整数切片",
			a:        []int{1, 2, 3},
			b:        []int{1, 2, 4},
			expected: false,
		},
		{
			name:     "长度不同的整数切片",
			a:        []int{1, 2, 3},
			b:        []int{1, 2},
			expected: false,
		},
		{
			name:     "相同的字符串切片",
			a:        []string{"a", "b", "c"},
			b:        []string{"a", "b", "c"},
			expected: true,
		},
		{
			name:     "不同的字符串切片",
			a:        []string{"a", "b", "c"},
			b:        []string{"a", "b", "d"},
			expected: false,
		},
		{
			name:     "相同的嵌套整数切片",
			a:        []interface{}{[]int{1, 2}, []int{3, 4}},
			b:        []interface{}{[]int{1, 2}, []int{3, 4}},
			expected: true,
		},
		{
			name:     "不同的嵌套整数切片",
			a:        []interface{}{[]int{1, 2}, []int{3, 4}},
			b:        []interface{}{[]int{1, 2}, []int{3, 5}},
			expected: false,
		},
		{
			name:     "深度嵌套的切片",
			a:        []interface{}{[]interface{}{[]int{1, 2}, []int{3, 4}}, []int{5, 6}},
			b:        []interface{}{[]interface{}{[]int{1, 2}, []int{3, 4}}, []int{5, 6}},
			expected: true,
		},
		{
			name:     "不同类型的切片",
			a:        []int{1, 2, 3},
			b:        []string{"1", "2", "3"},
			expected: false,
		},
		{
			name:     "非切片类型",
			a:        123,
			b:        []int{1, 2, 3},
			expected: false,
		},
		{
			name:     "两个非切片类型",
			a:        123,
			b:        456,
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := isSliceEqual(tt.a, tt.b)
			assert.Equal(t, tt.expected, result, "测试用例 '%s' 失败: 期望 %v, 实际 %v", tt.name, tt.expected, result)
		})
	}
}
