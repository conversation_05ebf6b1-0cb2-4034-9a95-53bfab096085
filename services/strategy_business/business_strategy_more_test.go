package strategy_business

import (
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/alicebob/miniredis/v2"
	"github.com/go-redis/redis/v8"
	"github.com/olivere/elastic/v7"
	"github.com/stretchr/testify/assert"

	testcommon "fobrain/fobrain/tests/common_test"
	pd "fobrain/mergeService/proto"
	"fobrain/models/elastic/assets"
	"fobrain/models/elastic/business_system"

	"github.com/agiledragon/gomonkey/v2"
)

// TestReMerge 测试ReMerge方法
func TestReMerge(t *testing.T) {
	// 设置 ES mock
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	// 设置 Redis mock
	s, err := miniredis.Run()
	if err != nil {
		t.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
	}
	defer s.Close()

	cli := redis.NewClient(&redis.Options{
		Addr: s.Addr(),
	})
	testcommon.SetRedisClient(cli)

	// 测试用例
	tests := []struct {
		name         string
		businessName []string
		field        string
		assets       []*elastic.SearchHit
		expectError  bool
		mockSetup    func()
	}{
		{
			name:         "成功触发重新融合",
			businessName: []string{"test1", "test2"},
			field:        "system",
			assets: []*elastic.SearchHit{
				{
					Id:     "asset1",
					Source: []byte(`{"id":"asset1","ip":"***********"}`),
				},
				{
					Id:     "asset2",
					Source: []byte(`{"id":"asset2","ip":"***********"}`),
				},
			},
			expectError: false,
			mockSetup: func() {
				// 模拟ES查询结果
				mockServer.Register("/asset/_search", []*elastic.SearchHit{
					{
						Id:     "asset1",
						Source: []byte(`{"id":"asset1","ip":"***********"}`),
					},
					{
						Id:     "asset2",
						Source: []byte(`{"id":"asset2","ip":"***********"}`),
					},
				})

				// 模拟Proto客户端调用
				client := pd.GetProtoClient()
				patches := gomonkey.ApplyMethodReturn(client, "TriggerMergeForAsset", &pd.TriggerMergeResponse{
					Success: true,
				}, nil)
				defer patches.Reset()
			},
		},
		{
			name:         "没有找到资产",
			businessName: []string{"test3"},
			field:        "system",
			assets:       []*elastic.SearchHit{},
			expectError:  false,
			mockSetup: func() {
				// 模拟ES查询结果 - 空结果
				mockServer.Register("/asset/_search", []*elastic.SearchHit{})
			},
		},
		{
			name:         "触发融合失败",
			businessName: []string{"test4"},
			field:        "system",
			assets: []*elastic.SearchHit{
				{
					Id:     "asset3",
					Source: []byte(`{"id":"asset3","ip":"***********"}`),
				},
			},
			expectError: true,
			mockSetup: func() {
				// 模拟ES查询结果
				mockServer.Register("/asset/_search", []*elastic.SearchHit{
					{
						Id:     "asset3",
						Source: []byte(`{"id":"asset3","ip":"***********"}`),
					},
				})
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 设置测试的 mock
			if tt.mockSetup != nil {
				tt.mockSetup()
			}

			// 创建资产模型的猴子补丁
			assetModel := assets.NewAssets()
			// 将elastic.SearchHit转换为assets.Assets
			assetsModels := make([]*assets.Assets, 0, len(tt.assets))
			for _, hit := range tt.assets {
				asset := &assets.Assets{
					Id: hit.Id,
				}
				assetsModels = append(assetsModels, asset)
			}
			patches := gomonkey.ApplyMethodReturn(assetModel, "FindAllByQuery", assetsModels, int64(len(assetsModels)))
			defer patches.Reset()

			// 为"触发融合失败"测试用例单独设置Proto客户端的mock
			var protoPatches *gomonkey.Patches
			if tt.name == "触发融合失败" {
				client := pd.GetProtoClient()
				protoPatches = gomonkey.ApplyMethodReturn(client, "TriggerMergeForAsset", (*pd.TriggerMergeResponse)(nil), assert.AnError)
				defer protoPatches.Reset()
			}

			// 执行测试
			err := NewStrategy().ReMerge(tt.businessName, tt.field)

			// 验证结果
			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.Nil(t, err)
			}
		})
	}
}

// TestFlushResultAndWriteResult 测试FlushResult和WriteResult方法
func TestFlushResultAndWriteResult(t *testing.T) {
	// 设置 ES mock
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	// 设置 Redis mock
	s, err := miniredis.Run()
	if err != nil {
		t.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
	}
	defer s.Close()

	cli := redis.NewClient(&redis.Options{
		Addr: s.Addr(),
	})
	testcommon.SetRedisClient(cli)

	// 创建测试业务系统数据
	testBusiness := &business_system.BusinessSystems{
		Id:           "test_business_id",
		BusinessName: "test_business",
		Status:       business_system.TrustStatusYes,
		From:         business_system.SourceStatusHandleAndImport,
	}

	// 模拟ES批量操作成功
	mockServer.Register("_bulk", &elastic.BulkIndexByScrollResponse{
		Updated: 1,
	})

	// 启动WriteResult协程
	strategy := NewStrategy()
	strategy.WriteResult()

	// 等待协程启动
	time.Sleep(100 * time.Millisecond)

	// 发送测试数据到通道
	businessMergeResultChan <- testBusiness

	// 发送刷新信号
	strategy.FlushResult()

	// 等待处理完成
	time.Sleep(500 * time.Millisecond)

	// 由于WriteResult是异步的，我们无法直接验证结果
	// 这里主要测试方法不会panic，实际效果需要通过集成测试验证
	// 或者使用更复杂的mock和channel监控
}

// TestGetStrategy 测试getStrategy方法
func TestGetStrategy(t *testing.T) {
	// 设置 MySQL mock
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()
	// 设置 Redis mock
	s, err := miniredis.Run()
	if err != nil {
		t.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
	}
	defer s.Close()
	cli := redis.NewClient(&redis.Options{
		Addr: s.Addr(),
	})
	testcommon.SetRedisClient(cli)

	// 模拟数据库查询结果
	mockRows := sqlmock.NewRows([]string{"id", "data_source_name", "weight"}).
		AddRow(1, "data1", 10).
		AddRow(2, "data2", 20).
		AddRow(3, "data3", 30)

	// 模拟查询业务策略
	mockDb.ExpectQuery("SELECT * FROM `business_strategies` WHERE weight != ? ORDER BY weight ASC").
		WillReturnRows(mockRows)

	// 执行测试
	strategy := NewStrategy()
	result := strategy.getStrategy()

	// 验证结果
	expected := []int{1, 2, 3}
	assert.Equal(t, expected, result)

	// 验证所有期望的SQL都已执行
	if err := mockDb.ExpectationsWereMet(); err != nil {
		t.Errorf("存在未满足的数据库操作期望: %s", err)
	}
}

// TestIsStatusUpdatable 测试isStatusUpdatable方法
func TestIsStatusUpdatable(t *testing.T) {
	tests := []struct {
		name        string
		oldStatus   int
		newStatus   int
		expected    bool
		description string
	}{
		{
			name:        "旧状态为不可信",
			oldStatus:   business_system.TrustStatusNo,
			newStatus:   business_system.TrustStatusYes,
			expected:    false,
			description: "如果旧数据是不可信，不需要更新",
		},
		{
			name:        "旧状态为可信，新状态为可信",
			oldStatus:   business_system.TrustStatusYes,
			newStatus:   business_system.TrustStatusYes,
			expected:    true,
			description: "如果旧数据是可信，新数据也是可信，允许更新",
		},
		{
			name:        "旧状态为可疑，新状态为可信",
			oldStatus:   business_system.TrustStatusYesOrNo,
			newStatus:   business_system.TrustStatusYes,
			expected:    true,
			description: "如果旧数据是可疑，新数据是可信，允许更新",
		},
		{
			name:        "旧状态为可疑，新状态为不可信",
			oldStatus:   business_system.TrustStatusYesOrNo,
			newStatus:   business_system.TrustStatusNo,
			expected:    true,
			description: "如果旧数据是可疑，新数据是不可信，允许更新",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := isStatusUpdatable(tt.oldStatus, tt.newStatus)
			assert.Equal(t, tt.expected, result, tt.description)
		})
	}
}
