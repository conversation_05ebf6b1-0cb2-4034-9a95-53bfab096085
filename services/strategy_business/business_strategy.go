package strategy_business

import (
	"context"
	"encoding/json"
	"errors"
	"fobrain/fobrain/app/services/business_strategy"
	"fobrain/initialize/es"
	"fobrain/initialize/mysql"
	"fobrain/models/mysql/personnel_departments"
	"fobrain/pkg/scheduler"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/google/uuid"
	es7 "github.com/olivere/elastic/v7"

	"fobrain/fobrain/common/localtime"
	"fobrain/fobrain/logs"
	"fobrain/models/elastic/assets"
	businesssystem2 "fobrain/models/elastic/business_system"
	"fobrain/models/elastic/helper"
	"fobrain/pkg/utils"

	pd "fobrain/mergeService/proto"
)

type Strategy struct {
}

// SourceBusiness 源业务配置结构
// 主键信息
// BusinessName   string   `json:"business_name"` //业务系统名称
// From           int      `json:"from"`            //来源 1资产IP导入 2数据源 3业务信息配置人工添加 4业务信息配置人工导入
type SourceBusiness struct {
	//业务属性
	Source          uint64                 `json:"source"`          // 来源
	Node            uint64                 `json:"node"`            // 节点
	Address         string                 `json:"address"`         //业务系统地址
	DepartmentsIds  []string               `json:"departments_ids"` //业务系统部门名称id
	StaffField      string                 `json:"staff_field" zh:"负责人匹配的字段"`
	ContinuityLevel int                    `json:"continuity_level" zh:"连续性级别"` // 1一级 2二级 3三级 4四级 5五级
	PersonIds       []string               `json:"person_ids" zh:"业务系统负责人"`
	SystemVersion   string                 `json:"system_version"`  //系统版本
	IsGj            *int                   `json:"is_gj"`           //是否关基设施：''-空/ 1-是/ 2-否
	IsXc            *int                   `json:"is_xc"`           //是否信创：''- 空/ 1-是/ 2- 否
	PurchaseType    *int                   `json:"purchase_type"`   //采购类型：''-空/ 1-自研/ 2-外包/ 3-第三方采购
	ImportantTypes  *int                   `json:"important_types"` //重要性 ''-空/ 1-非常重要/ 2- 重要/ 3- 一般
	InsuranceLevel  *int                   `json:"insurance_level"` //等保级别: ''- 空/ 1-一级/2- 二级/3- 三级
	OperatingEnv    *int                   `json:"operating_env"`   //运行环境 1-生产环境/2-开发环境
	RunningState    *int                   `json:"running_state"`   //运行状态 1-运行中/ 0-已下线
	UseMark         string                 `json:"use_mark" form:"use_mark" validate:"omitempty" zh:"规划用途"`
	PersonNames     []string               `json:"person_names"`     //业务系统负责人名称--- 只支持导入数据 支持多个人，用英文逗号隔开
	DepartmentNames []string               `json:"department_names"` //业务系统部门名称--- 只支持导入数据 支持多个人，用英文逗号隔开
	CustomFields    map[string]interface{} `json:"custom_fields"`    //自定义字段
	//管理信息
	UserId uint64 `json:"user_id"` //操作人
	State  int    `json:"state"`
}

func NewStrategy() *Strategy {
	return &Strategy{}
}

var lockMap sync.Map

func getLock(key string) *sync.Mutex {
	val, _ := lockMap.LoadOrStore(key, &sync.Mutex{})
	return val.(*sync.Mutex)
}

// UpSet 更新或插入业务系统
// 根据策略处理业务系统信息
// 有关联IP 根据IP 更新资产标签或给资产打标签
// 触发重新融合
func (st *Strategy) UpSet(businessName string, from int, sourceBusiness *SourceBusiness) (*assets.Business, error) {
	//if from == businesssystem2.SourceStatusAsset {
	//	return nil, nil
	//}
	if sourceBusiness.StaffField == "" {
		sourceBusiness.StaffField = "name"
	}
	businessName = strings.TrimSpace(businessName)
	sourJs, _ := json.Marshal(sourceBusiness)
	logs.GetLogger().Debugf("【updateBusiness】开始更新业务系统 %s", string(sourJs))
	if businessName == "" {
		return nil, errors.New("business name is empty")
	}

	//根据策略处理业务系统信息
	nBusiness, ok, err := st.processByStrategy(businessName, from, sourceBusiness)

	if err != nil {
		return nil, err
	}
	if nBusiness == nil {
		return nil, nil
	}

	assetBusiness := &assets.Business{
		SystemId:             nBusiness.Id,
		System:               nBusiness.BusinessName,
		BusinessInfo:         nBusiness,
		BusinessFrom:         nBusiness.From,
		BusinessTrustedState: nBusiness.Status,
		PersonBase:           nBusiness.PersonBase,
		DepartmentBase:       nBusiness.DepartmentBase,
	}

	// 业务配置未更新
	if !ok {
		return assetBusiness, nil
	}
	logs.GetLogger().Debugf("【updateBusiness】业务系统后处理完成 %+v", assetBusiness)
	return assetBusiness, nil
}

// 使用反射来比较任意类型的切片
func isSliceEqual(a, b interface{}) bool {
	// 如果两者都是nil，则相等
	if a == nil && b == nil {
		return true
	}

	// 如果只有一个是nil，则不相等
	if a == nil || b == nil {
		return false
	}

	// 使用反射获取切片的值
	aVal := reflect.ValueOf(a)
	bVal := reflect.ValueOf(b)

	// 检查是否为切片类型
	if aVal.Kind() != reflect.Slice || bVal.Kind() != reflect.Slice {
		return false
	}

	// 长度不同，则不相等
	if aVal.Len() != bVal.Len() {
		return false
	}

	// 比较每个元素
	for i := 0; i < aVal.Len(); i++ {
		aElem := aVal.Index(i).Interface()
		bElem := bVal.Index(i).Interface()

		// 如果元素是切片，递归比较
		aElemVal := reflect.ValueOf(aElem)
		bElemVal := reflect.ValueOf(bElem)
		if aElemVal.Kind() == reflect.Slice && bElemVal.Kind() == reflect.Slice {
			if !isSliceEqual(aElem, bElem) {
				return false
			}
			continue
		}

		// 否则直接比较
		if !reflect.DeepEqual(aElem, bElem) {
			return false
		}
	}

	return true
}

func (st *Strategy) ReMerge(businessName []string, field string) error {
	logs.GetLogger().Infof("【updateBusiness】业务系统后开始触发重新融合")
	// 根据业务系统名称查询IP资产
	// 查询受影响的资产
	nestedQuery := es7.NewNestedQuery(
		"business", // 嵌套字段路径
		es7.NewTermsQueryFromStrings("business."+field, businessName...), // 查询条件
	)
	q := es7.NewBoolQuery().Must(nestedQuery)
	// 使用10000作为批量大小，避免超过Elasticsearch的最大结果窗口限制
	assetsAll, _ := assets.NewAssets().FindAllByQuery(context.Background(), q, nil, "id")
	if len(assetsAll) == 0 {
		return nil
	}
	assetIds := make([]string, 0, len(assetsAll))
	for _, asset := range assetsAll {
		assetIds = append(assetIds, asset.Id)
	}
	batchNo := strings.ReplaceAll(uuid.New().String(), "-", "")
	input := &pd.TriggerMergeForAssetRequest{
		TriggerMergeBaseRequest: &pd.TriggerMergeBaseRequest{
			SourceId:     0,
			NodeId:       0,
			TaskId:       batchNo,
			ChildTaskId:  batchNo,
			TriggerEvent: "业务系统更新",
		},
		AssetIds:   assetIds,
		SubTrigger: 1,
	}
	_, err := pd.GetProtoClient().TriggerMergeForAsset(context.Background(), input, pd.ClientWithAddress)
	if err != nil {
		logs.GetLogger().Errorf("【updateBusiness】业务系统更新触发重新融合，err:%+v", err)
		return err
	}
	return nil
}

var businessMergeResultChan = make(chan *businesssystem2.BusinessSystems, 1000)

func (st *Strategy) FlushResult() {
	businessMergeResultChan <- nil
}
func (st *Strategy) WriteResult() {
	go func() {
		var bulk = es.GetEsClient().Bulk().Refresh("true")
		var indexName = businesssystem2.NewBusinessSystems().IndexName()
		schedulerTaskKey := "business_merge_result_scheduler"
		for v := range businessMergeResultChan {
			scheduler.Stop(schedulerTaskKey)
			if v == nil {
				// 数据落盘事件
				if bulk.NumberOfActions() > 0 {
					_, err := bulk.Do(context.Background())
					if err != nil {
						logs.GetLogger().Errorf("【updateBusiness】业务系统更新失败，err:%+v", err)
						continue
					}
					bulk = es.GetEsClient().Bulk().Refresh("true")
				}
				continue
			}
			logs.GetLogger().Infof("【updateBusiness】业务系统后处理完成 %+v", v)
			bulk.Add(es7.NewBulkUpdateRequest().Index(indexName).Id(v.Id).Doc(v).DocAsUpsert(true))
			if bulk.NumberOfActions() >= 500 {
				_, err := bulk.Do(context.Background())
				if err != nil {
					logs.GetLogger().Errorf("【updateBusiness】业务系统更新失败，err:%+v", err)
					continue
				}
				bulk = es.GetEsClient().Bulk().Refresh("true")
			}
			scheduler.Start(schedulerTaskKey, 3*time.Second, false, func() {
				if bulk.NumberOfActions() >= 1 {
					_, err := bulk.Do(context.Background())
					if err != nil {
						logs.GetLogger().Errorf("【updateBusiness】业务系统更新失败，err:%+v", err)
						return
					}
					bulk = es.GetEsClient().Bulk().Refresh("true")
				}
			})
		}
	}()
}

// processByStrategy 根据优先级策略更新业务配置
// nBusiness 返回业务配置 更新后的业务配置
func (st *Strategy) processByStrategy(businessName string, from int, sourceBusiness *SourceBusiness) (nBusiness *businesssystem2.BusinessSystems, ok bool, err error) {
	lock := getLock(businessName)
	lock.Lock()
	defer lock.Unlock()
	// 查询旧业务系统
	old, err := businesssystem2.NewBusinessSystems().GetBusinessByName(context.Background(), businessName)
	if err != nil {
		return nil, false, err
	}
	var oldBusiness *businesssystem2.BusinessSystems
	if old != nil {
		oldBusiness = old.BusinessInfo.(*businesssystem2.BusinessSystems)
	}

	logs.GetLogger().Debug("【updateBusiness】开始组装业务系统信息数据")
	nBusiness = st.assemble(businessName, from, oldBusiness, sourceBusiness)
	if oldBusiness != nil {
		// 判断是否更新状态和优先级
		if from != businesssystem2.SourceStatusHandleAndImport {
			if !isStatusUpdatable(oldBusiness.Status, nBusiness.Status) ||
				!shouldUpdateBasedOnPriority(oldBusiness.From, nBusiness.From, st.getStrategy()) {
				logs.GetLogger().Infof("【updateBusiness】条件不满足，不更新业务系统")
				return oldBusiness, false, nil
			}
		}
		// 更新业务 ID 和状态
		nBusiness.Id = oldBusiness.Id
		nBusiness.Fid = oldBusiness.Fid
		nBusiness.CreatedAt = oldBusiness.CreatedAt
		nBusiness.InternetIps = oldBusiness.InternetIps
		nBusiness.IntranetIps = oldBusiness.IntranetIps
		nBusiness.CreatedAt = oldBusiness.CreatedAt
	}

	if from == businesssystem2.SourceStatusHandleAndImport && sourceBusiness.State != 0 {
		nBusiness.Status = sourceBusiness.State
	}
	nBusiness.ContinuityLevel = sourceBusiness.ContinuityLevel

	// //排序转换 json
	// nBusinessJs, _ := json.Marshal(nBusiness)
	// oBusinessJs, _ := json.Marshal(oldBusiness)

	// // 删除时间戳字段以进行比较
	// nBusinessJs, _ = sjson.DeleteBytes(nBusinessJs, "created_at")
	// nBusinessJs, _ = sjson.DeleteBytes(nBusinessJs, "updated_at")
	// oBusinessJs, _ = sjson.DeleteBytes(oBusinessJs, "created_at")
	// oBusinessJs, _ = sjson.DeleteBytes(oBusinessJs, "updated_at")

	// if string(nBusinessJs) == string(oBusinessJs) {
	// 	logs.GetLogger().Debug("【updateBusiness】数据相同，不更新业务系统")
	// 	return nBusiness, false, updateRelationAssets, nil
	// }
	// 插入或更新业务系统
	businessMergeResultChan <- nBusiness
	//err = businesssystem2.NewBusinessSystems().CreateOrUpdate(nBusiness)
	//if err != nil {
	//	logs.GetLogger().Errorf("【updateBusiness】更新业务系统失败，%+v", err)
	//	return nil, false, updateRelationAssets, err
	//}
	return nBusiness, true, nil
}

func (st *Strategy) getStrategy() []int {
	validStrategies, err := business_strategy.NewService().GetValid()
	if err != nil {
		return nil
	}
	return validStrategies
	//return []int{
	//	businesssystem2.SourceStatusHandleAndImport,
	//	businesssystem2.SourceStatusDataSource,
	//	businesssystem2.SourceStatusAsset,
	//}
}

// assemble 组装业务配置结构信息
// sourceBusiness 需要处理的源配置信息
// 如果来源是人工添加/导入 则修改为可信
// 如果有部门 且来源参与优先级，则可信
// 返回组装好的业务配置信息
func (st *Strategy) assemble(businessName string, from int, oldBusiness *businesssystem2.BusinessSystems, sourceBusiness *SourceBusiness) *businesssystem2.BusinessSystems {
	id := utils.Md5bHash([]byte(businessName), false)
	if oldBusiness != nil {
		id = oldBusiness.Id
	}
	insertBusiness := &businesssystem2.BusinessSystems{
		Id:              id,
		Fid:             id,
		BusinessName:    businessName,
		AssetsAttribute: businesssystem2.AssetsAttribute{},
		Status:          businesssystem2.TrustStatusYesOrNo, //默认可疑
		From:            from,
		CreatedAt:       localtime.NewLocalTime(time.Now()),
		UpdatedAt:       localtime.NewLocalTime(time.Now()),
	}

	if sourceBusiness == nil {
		return insertBusiness
	}
	// 组装原始信息
	mapOper := make(map[string][]*assets.PersonWithMapping)
	sourceBusiness.PersonIds = utils.CompactStrings(utils.Unique(sourceBusiness.PersonIds))
	sourceBusiness.PersonNames = utils.CompactStrings(utils.Unique(sourceBusiness.PersonNames))
	sourceBusiness.DepartmentsIds = utils.CompactStrings(utils.Unique(sourceBusiness.DepartmentsIds))
	sourceBusiness.DepartmentNames = utils.CompactStrings(utils.Unique(sourceBusiness.DepartmentNames))
	if len(sourceBusiness.PersonIds) > 0 || len(sourceBusiness.PersonNames) > 0 {
		if len(sourceBusiness.PersonIds) > 0 {
			for _, personId := range sourceBusiness.PersonIds {
				mapOper["id"] = append(mapOper["id"], &assets.PersonWithMapping{
					SourceId:     sourceBusiness.Source,
					NodeId:       sourceBusiness.Node,
					SourceValue:  personId,
					MappingField: "id",
				})
			}
		} else {
			for _, personName := range sourceBusiness.PersonNames {
				mapOper[sourceBusiness.StaffField] = append(mapOper[sourceBusiness.StaffField], &assets.PersonWithMapping{
					SourceId:     sourceBusiness.Source,
					NodeId:       sourceBusiness.Node,
					SourceValue:  personName,
					MappingField: sourceBusiness.StaffField,
				})
			}
		}
		if len(mapOper) > 0 {
			//根据人员ID查询人员信息
			personResult, departmentResult, err := helper.GetPersonInfoByList(mapOper)
			if err != nil {
				logs.GetLogger().Errorf("【updateBusiness】获取人员信息失败，%+v", err)
			}
			if personResult != nil {
				for _, person := range personResult {
					for _, d := range person.Department {
						d.BusinessSystemId = id
						d.BusinessSystemName = businessName
					}
				}
				insertBusiness.PersonBase = personResult
			}
			if departmentResult != nil {
				for _, d := range departmentResult {
					d.BusinessSystemId = id
					d.BusinessSystemName = businessName
				}
				insertBusiness.DepartmentBase = departmentResult
			}
		}
	}
	if len(insertBusiness.DepartmentBase) == 0 && (len(sourceBusiness.DepartmentsIds) > 0 || len(sourceBusiness.DepartmentNames) > 0) {
		if len(sourceBusiness.DepartmentNames) > 0 {
			//根据部门名称查询部门信息
			dts, _, err := personnel_departments.NewPersonnelDepartmentsModel().List(0, 0, mysql.WithWhere("name in (?)", sourceBusiness.DepartmentNames))
			if err != nil {
				logs.GetLogger().Errorf("【updateBusiness】获取部门信息失败，%+v", err)
			}
			if len(dts) > 0 {
				sourceBusiness.DepartmentsIds = make([]string, len(dts))
				for i, dt := range dts {
					sourceBusiness.DepartmentsIds[i] = strconv.FormatUint(dt.Id, 10)
				}
			} else {
				insertBusiness.DepartmentBase = make([]*assets.DepartmentBase, len(sourceBusiness.DepartmentNames))
				for i, dt := range sourceBusiness.DepartmentNames {
					insertBusiness.DepartmentBase[i] = &assets.DepartmentBase{
						Id:                 0,
						Name:               dt,
						BusinessSystemId:   id,
						BusinessSystemName: businessName,
					}
				}
			}
		}
		if len(sourceBusiness.DepartmentsIds) > 0 {
			//根据部门ID查询部门信息
			insertBusiness.DepartmentBase = make([]*assets.DepartmentBase, 0, len(sourceBusiness.DepartmentsIds))
			for _, dId := range sourceBusiness.DepartmentsIds {
				departmentId, _ := strconv.ParseUint(dId, 10, 64)
				department := helper.GetDepartment(departmentId, "", "", businessName, id)
				if department != nil {
					insertBusiness.DepartmentBase = append(insertBusiness.DepartmentBase, department)
				}
			}
		}
	}
	insertBusiness.Address = sourceBusiness.Address

	insertBusiness.SystemVersion = sourceBusiness.SystemVersion
	insertBusiness.UseMark = sourceBusiness.UseMark
	insertBusiness.CustomFields = sourceBusiness.CustomFields
	insertBusiness.AssetsAttribute = businesssystem2.AssetsAttribute{
		IsGj:           sourceBusiness.IsGj,
		IsXc:           sourceBusiness.IsXc,
		PurchaseType:   sourceBusiness.PurchaseType,
		ImportantTypes: sourceBusiness.ImportantTypes,
		InsuranceLevel: sourceBusiness.InsuranceLevel,
		OperatingEnv:   sourceBusiness.OperatingEnv,
		RunningState:   sourceBusiness.RunningState,
	}

	//如果有部门 且来源参与优先级，则可信
	if utils.ListContains(st.getStrategy(), from) && len(insertBusiness.DepartmentBase) > 0 && insertBusiness.DepartmentBase[0].Name != "" {
		insertBusiness.Status = businesssystem2.TrustStatusYes
	}
	return insertBusiness
}

// 判断业务状态是否需要更新
func isStatusUpdatable(oldStatus, newStatus int) bool {
	// TrustStatusNo = 不可信, TrustStatusYes = 可信
	if oldStatus == businesssystem2.TrustStatusNo {
		// 如果旧数据是不可信，不需要更新
		return false
	}
	// 如果新数据可信/可疑，允许更新
	//return newStatus == businesssystem2.TrustStatusYes
	return true
}

// 根据优先级判断是否更新
func shouldUpdateBasedOnPriority(oldFrom, newFrom int, priorityList []int) bool {
	// 从优先级列表中查找索引值
	oldPriority := utils.IndexOf(oldFrom, priorityList)
	newPriority := utils.IndexOf(newFrom, priorityList)
	if newPriority == -1 {
		newPriority = 99
	}
	if oldPriority == -1 {
		oldPriority = 99
	}
	return newPriority <= oldPriority
}
