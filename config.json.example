{"common": {"env": "vKm4/Px7108mNMtoFDXZUMXtNSW6BHh6XhA3IU74Yp5U6pT1X0mQtbM+6dfG6jGU71SuvlS97UhAW18HPejq6A==", "log_level": "debug", "listen": ":8090", "network": "en0", "root_storage": "/storage", "binlog_base_path": "/data/fobrain/storage/data/mysql/mysql-binlogs", "mysql_binlog_path": "", "mysql_dump_path": "", "mysql_bin_path": "", "mysql_admin_bin_path": "", "repo_base_path": "/usr/share/elasticsearch/data/snapshot"}, "elastic": {"address": "127.0.0.1", "port": 9200, "username": "", "password": "", "sniff": false}, "logger": {"output_console": true, "output_file": true, "file_name": "/storage/logs/logs.log", "file_name_fobrain": "/logs/fobrain-service.log", "file_name_scheduler": "/logs/scheduler-logs.log", "file_name_sync": "/logs/sync-logs.log", "file_name_cascade": "/logs/cascade-logs.log", "file_name_backup": "/logs/backup-logs.log", "level": "info", "max_size": 8, "max_age": 30, "local_time": true, "compress": true, "max_backups": 5, "audit_log_expired_days": 180, "audit_log_max_size_gb": 50}, "merge_logger": {"output_console": true, "output_file": true, "file_name_service": "/logs/merge_service.log", "file_name_device": "/logs/merge_device.log", "file_name_asset": "/logs/merge_asset.log", "file_name_vuln": "/logs/merge_vuln.log", "file_name_person": "/logs/merge_person.log", "level": "info", "max_size": 10, "max_age": 30, "local_time": true, "compress": false, "max_backups": 20}, "mysql": {"address": "127.0.0.1", "port": 3306, "username": "root", "password": "Fobrainpass", "database": "fobrain", "charset": "utf8mb4", "log-level": "debug", "slow-time": 15}, "queue": {"device_merge_queue": "device_merge_queue_dev", "device_merge_concurrent": 20, "asset_merge_queue": "asset_merge_queue_dev", "asset_merge_concurrent": 20, "vuln_merge_queue": "vuln_merge_queue_dev", "vuln_merge_concurrent": 20, "vuln_update_queue": "vuln_update_queue_dev", "vuln_update_concurrent": 5, "person_merge_queue": "person_merge_queue_dev", "person_merge_concurrent": 15, "del_msg_batch": 500, "end_delay": 180}, "redis": {"address": "127.0.0.1", "port": 6379, "password": "Fobrainpass", "database": 0}, "source_sync": {"aliyun_cloud_size": 50, "aliyun_cloud_time": 3, "changting_waf_size": 50, "changting_waf_time": 1, "mach_lake_time": 1, "qizhi_uaudithost_size": 50, "qizhi_uaudithost_time": 1, "weibu_size": 50, "weibu_time": 1}}